package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.CancellationSignal;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;
import o.ah.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\i.smali */
public final class i<P> extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int q;
    private static int r;
    private static int t;
    P h;
    final o.dw.d<P> i;
    private final boolean k;
    private final e.a l;
    CancellationSignal m;

    /* renamed from: o, reason: collision with root package name */
    private final String f105o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        t = 0;
        q = 1;
        y();
        TypedValue.complexToFloat(0);
        Process.myPid();
        ViewConfiguration.getMaximumDrawingCacheSize();
        int i = t + 35;
        q = i % 128;
        switch (i % 2 == 0) {
            case false:
                break;
            default:
                int i2 = 57 / 0;
                break;
        }
    }

    static void init$0() {
        $$d = new byte[]{13, -73, -57, -113};
        $$e = 202;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(int r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = o.v.i.$$d
            int r8 = r8 * 3
            int r8 = 4 - r8
            int r9 = r9 * 2
            int r9 = r9 + 107
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r9 = r9 + 1
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.i.v(int, int, short, java.lang.Object[]):void");
    }

    static void y() {
        r = 874635280;
    }

    static /* synthetic */ void a(i iVar) {
        int i = t + 1;
        q = i % 128;
        char c = i % 2 == 0 ? (char) 24 : 'S';
        iVar.n();
        switch (c) {
            case 24:
                int i2 = 8 / 0;
                break;
        }
        int i3 = t + 9;
        q = i3 % 128;
        int i4 = i3 % 2;
    }

    static /* synthetic */ o.p.g b(i iVar) {
        int i = q + 23;
        t = i % 128;
        switch (i % 2 == 0) {
            case false:
                iVar.l();
                throw null;
            default:
                o.p.g l = iVar.l();
                int i2 = t + 47;
                q = i2 % 128;
                int i3 = i2 % 2;
                return l;
        }
    }

    static /* synthetic */ o.p.g c(i iVar) {
        int i = t + 57;
        q = i % 128;
        boolean z = i % 2 == 0;
        o.p.g l = iVar.l();
        switch (z) {
            case false:
                break;
            default:
                int i2 = 9 / 0;
                break;
        }
        int i3 = q + 95;
        t = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ o.p.g d(i iVar) {
        int i = t + 25;
        q = i % 128;
        int i2 = i % 2;
        o.p.g l = iVar.l();
        int i3 = t + Opcodes.DREM;
        q = i3 % 128;
        switch (i3 % 2 == 0 ? '[' : '4') {
            case Opcodes.DUP_X2 /* 91 */:
                int i4 = 30 / 0;
                return l;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g e(i iVar) {
        int i = q + Opcodes.LNEG;
        t = i % 128;
        int i2 = i % 2;
        o.p.g l = iVar.l();
        int i3 = q + Opcodes.LSHR;
        t = i3 % 128;
        switch (i3 % 2 != 0 ? '#' : '%') {
            case '#':
                throw null;
            default:
                return l;
        }
    }

    public i(String str, o.eo.e eVar, boolean z, e.a aVar, o.dw.d<P> dVar, String str2) {
        super(str, eVar);
        this.k = z;
        this.l = aVar;
        this.i = dVar;
        this.f105o = str2;
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = t + Opcodes.DSUB;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        if (this.k) {
            int i4 = i2 + 1;
            t = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }
        WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
        String str = this.f105o;
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        u(((byte) KeyEvent.getModifierMetaStateMask()) + 17, "\u000fￊ￮\u0013\u0011\u0013\u001e\u000b\u0016ￊ￭\u000b\u001c\u000eￊ\n\ufffe\u0012", 17 - ImageFormat.getBitsPerPixel(0), 197 - Gravity.getAbsoluteGravity(0, 0), false, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(((d) this).n.e());
        Object[] objArr2 = new Object[1];
        u((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "ￃ\u0003ￃ\u0007\u0012\b\u0016ￃ\u0011\u0012\u0017ￃ\u0016\u0018\u0013\u0013\u0012\u0015\u0017", Color.red(0) + 19, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 204, false, objArr2);
        throw new WalletValidationException(walletValidationErrorCode, str, append.append(((String) objArr2[0]).intern()).append(this.l.name()).toString());
    }

    @Override // o.p.h
    public final String d() {
        String str;
        int i = t + 97;
        int i2 = i % 128;
        q = i2;
        switch (i % 2 != 0) {
            case true:
                str = this.f105o;
                break;
            default:
                str = this.f105o;
                int i3 = 68 / 0;
                break;
        }
        int i4 = i2 + 19;
        t = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    @Override // o.p.h
    public final void a(final Context context, o.ei.c cVar, o.h.d dVar) {
        new o.ah.e(context, new e.d() { // from class: o.v.i.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int b;
            private static long d;
            private static int e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                b = 0;
                e = 1;
                d = 2588015706194167816L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0037). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(short r7, byte r8, short r9, java.lang.Object[] r10) {
                /*
                    int r9 = r9 * 3
                    int r9 = r9 + 4
                    int r7 = r7 * 4
                    int r7 = r7 + 1
                    byte[] r0 = o.v.i.AnonymousClass2.$$a
                    int r8 = r8 * 2
                    int r8 = 114 - r8
                    byte[] r1 = new byte[r7]
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r8 = r7
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    goto L37
                L1a:
                    r3 = r2
                    r6 = r9
                    r9 = r8
                L1d:
                    r8 = r6
                    int r4 = r3 + 1
                    byte r5 = (byte) r9
                    r1[r3] = r5
                    if (r4 != r7) goto L2d
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2d:
                    r3 = r0[r8]
                    r6 = r8
                    r8 = r7
                    r7 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    r9 = r6
                L37:
                    int r7 = -r7
                    int r9 = r9 + 1
                    int r7 = r7 + r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r6 = r9
                    r9 = r7
                    r7 = r8
                    goto L1d
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.i.AnonymousClass2.g(short, byte, short, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{8, 72, -108, -33};
                $$b = 12;
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0068. Please report as an issue. */
            @Override // o.ah.e.d
            public final void e(String str) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f("໑觫Ì鮪ኞ굚\u2453뼰㘅컯䧟샣宭튆浧\ue45f缩\uf619軑\u09de肮ᮝ銢\u2d6aꑓ㼽똆件짍", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 34589, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("\u0ef7٭´ᜬⲗ◪㵈㋀䨉䎅壢偔榮愡皃迮蝍鲀鐋굺ꋧ멊뎹쬆", 2202 - ImageFormat.getBitsPerPixel(0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                if (i.c(i.this) != null) {
                    i.this.m = new CancellationSignal();
                    i.this.i.launch(context, i.this.h, str, i.this.m);
                    int i = b + 71;
                    e = i % 128;
                    switch (i % 2 == 0) {
                    }
                }
                int i2 = b + Opcodes.LMUL;
                e = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:13:0x008c. Please report as an issue. */
            @Override // o.ah.e.d
            public final void b(o.bb.d dVar2) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f("໑觫Ì鮪ኞ굚\u2453뼰㘅컯䧟샣宭튆浧\ue45f缩\uf619軑\u09de肮ᮝ銢\u2d6aꑓ㼽똆件짍", 34588 - TextUtils.indexOf((CharSequence) "", '0'), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("\u0ef7㆑瀅냈\uf367㏮犀딬\uf5e9㑩瓪랐\uf63e㛅祛맲\uf88d㬉篇메\ufaf8㶞簰벼", 16232 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                if (i.b(i.this) != null) {
                    int i = b + 109;
                    e = i % 128;
                    int i2 = i % 2;
                    switch (dVar2.d() == o.bb.a.aA ? (char) 22 : (char) 26) {
                        case 26:
                            i.d(i.this).onError(o.bv.c.c(dVar2));
                            int i3 = b + 13;
                            e = i3 % 128;
                            switch (i3 % 2 == 0 ? 'a' : (char) 21) {
                            }
                        default:
                            i.a(i.this);
                            i.e(i.this).onAuthenticationDeclined();
                            return;
                    }
                }
                int i4 = e + 19;
                b = i4 % 128;
                switch (i4 % 2 != 0 ? 'I' : Typography.quote) {
                    case 'I':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(java.lang.String r19, int r20, java.lang.Object[] r21) {
                /*
                    Method dump skipped, instructions count: 618
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.i.AnonymousClass2.f(java.lang.String, int, java.lang.Object[]):void");
            }
        }, j()).a(dVar, o(), ((d) this).n.e(), this.l);
        int i = q + 9;
        t = i % 128;
        int i2 = i % 2;
    }

    public final void e(Context context, o.p.g gVar, P p) throws WalletValidationException {
        int i = q + 23;
        t = i % 128;
        switch (i % 2 != 0) {
            case true:
                this.h = p;
                this.i.setProcessCallback(gVar);
                d(context, gVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.h = p;
                this.i.setProcessCallback(gVar);
                d(context, gVar);
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0017, code lost:
    
        if (((o.v.d) r4).n.q() != null) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final fr.antelop.sdk.card.ICardDisplay a() {
        /*
            r4 = this;
            int r0 = o.v.i.q
            int r0 = r0 + 125
            int r1 = r0 % 128
            o.v.i.t = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L1c
            o.eo.e r0 = r4.n
            o.er.j r0 = r0.q()
            r3 = 44
            int r3 = r3 / r2
            if (r0 == 0) goto L2a
            goto L2b
        L1a:
            r0 = move-exception
            throw r0
        L1c:
            o.eo.e r0 = r4.n
            o.er.j r0 = r0.q()
            if (r0 == 0) goto L26
            r0 = r2
            goto L27
        L26:
            r0 = r1
        L27:
            switch(r0) {
                case 0: goto L2b;
                default: goto L2a;
            }
        L2a:
            goto L7a
        L2b:
            o.eo.e r0 = r4.n
            o.er.j r0 = r0.q()
            boolean r0 = r0.d()
            if (r0 == 0) goto L2a
            o.eo.e r0 = r4.n
            java.lang.Class<o.er.b> r3 = o.er.b.class
            o.el.d r0 = r0.b(r3)
            o.er.b r0 = (o.er.b) r0
            if (r0 == 0) goto L45
            r1 = r2
            goto L46
        L45:
        L46:
            switch(r1) {
                case 1: goto L77;
                default: goto L49;
            }
        L49:
            int r1 = o.v.i.t
            int r1 = r1 + 113
            int r2 = r1 % 128
            o.v.i.q = r2
            int r1 = r1 % 2
            o.eo.a r1 = r0.C()
            if (r1 == 0) goto L5c
            r1 = 62
            goto L5e
        L5c:
            r1 = 9
        L5e:
            switch(r1) {
                case 9: goto L77;
                default: goto L61;
            }
        L61:
            o.eo.a r1 = r0.C()
            o.eo.b r1 = r1.f()
            o.eo.b r2 = o.eo.b.c
            if (r1 == r2) goto L77
            o.eo.a r0 = r0.C()
            fr.antelop.sdk.card.EcomStaticTokenDisplay r1 = new fr.antelop.sdk.card.EcomStaticTokenDisplay
            r1.<init>(r0)
            return r1
        L77:
            r0 = 0
            return r0
        L7a:
            fr.antelop.sdk.card.CardDisplay r0 = new fr.antelop.sdk.card.CardDisplay
            o.eo.e r1 = r4.n
            r0.<init>(r1)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.i.a():fr.antelop.sdk.card.ICardDisplay");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.du.c t() {
        /*
            r7 = this;
            o.eo.e r0 = r7.n
            o.er.j r0 = r0.q()
            r1 = 0
            if (r0 == 0) goto Lb7
            int r0 = o.v.i.q
            int r0 = r0 + 115
            int r2 = r0 % 128
            o.v.i.t = r2
            int r0 = r0 % 2
            o.eo.e r0 = r7.n
            o.er.j r0 = r0.q()
            boolean r0 = r0.d()
            if (r0 == 0) goto Lb7
            o.eo.e r0 = r7.n
            java.util.LinkedHashMap r0 = r0.A()
            if (r0 != 0) goto L2a
            goto Lb7
        L2a:
            o.eo.e r0 = r7.n
            java.util.LinkedHashMap r0 = r0.A()
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L38:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L41
            r2 = 45
            goto L43
        L41:
            r2 = 52
        L43:
            switch(r2) {
                case 52: goto L53;
                default: goto L46;
            }
        L46:
            int r2 = o.v.i.t
            int r2 = r2 + 31
            int r3 = r2 % 128
            o.v.i.q = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L54
            goto L54
        L53:
            return r1
        L54:
            java.lang.Object r2 = r0.next()
            o.eo.d r2 = (o.eo.d) r2
            java.util.List r2 = r2.b()
            java.util.Iterator r2 = r2.iterator()
        L62:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto Lb6
            int r3 = o.v.i.q
            int r3 = r3 + 17
            int r4 = r3 % 128
            o.v.i.t = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L8a
            java.lang.Object r3 = r2.next()
            o.el.d r3 = (o.el.d) r3
            boolean r4 = r3 instanceof o.er.b
            r5 = 45
            r6 = 0
            int r5 = r5 / r6
            if (r4 == 0) goto L83
            goto L84
        L83:
            r6 = 1
        L84:
            switch(r6) {
                case 1: goto Lb4;
                default: goto L87;
            }
        L87:
            goto L94
        L88:
            r0 = move-exception
            throw r0
        L8a:
            java.lang.Object r3 = r2.next()
            o.el.d r3 = (o.el.d) r3
            boolean r4 = r3 instanceof o.er.b
            if (r4 == 0) goto Lb4
        L94:
            o.er.b r3 = (o.er.b) r3
            o.eo.a r0 = r3.C()
            if (r0 != 0) goto L9f
            r2 = 28
            goto La1
        L9f:
            r2 = 24
        La1:
            switch(r2) {
                case 28: goto La9;
                default: goto La4;
            }
        La4:
            o.du.c r0 = r0.d()
            return r0
        La9:
            int r0 = o.v.i.q
            int r0 = r0 + 39
            int r2 = r0 % 128
            o.v.i.t = r2
            int r0 = r0 % 2
            return r1
        Lb4:
            goto L62
        Lb6:
            goto L38
        Lb7:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.i.t():o.du.c");
    }

    public final fr.antelop.sdk.CancellationSignal s() {
        CancellationSignal cancellationSignal = this.m;
        switch (cancellationSignal == null) {
            case true:
                return null;
            default:
                int i = t + 65;
                q = i % 128;
                int i2 = i % 2;
                switch (cancellationSignal.isCanceled() ? (char) 14 : 'T') {
                    case 14:
                        return null;
                    default:
                        fr.antelop.sdk.CancellationSignal cancellationSignal2 = new fr.antelop.sdk.CancellationSignal(this.m);
                        int i3 = t + 91;
                        q = i3 % 128;
                        int i4 = i3 % 2;
                        return cancellationSignal2;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 512
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.i.u(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
