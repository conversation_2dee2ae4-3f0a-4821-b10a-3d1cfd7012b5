package o.ev;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.eg.b;
import o.eg.d;
import o.ey.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ev\a.smali */
public final class a extends o.ey.a<o.fd.a, c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] c;
    private static long d;
    private static int f;
    private static int j;
    private final String a;
    private final String b;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        a();
        ViewConfiguration.getWindowTouchSlop();
        SystemClock.uptimeMillis();
        ExpandableListView.getPackedPositionType(0L);
        int i = f + 63;
        j = i % 128;
        switch (i % 2 != 0 ? '`' : '0') {
            case '0':
                return;
            default:
                throw null;
        }
    }

    static void a() {
        c = new char[]{56969, 52102, 62617, 57766, 35459, 46985, 41089, 19863, 30347, 25503, 3201, 14726, 8832, 53164, 63619, 58776, 36508, 48007, 42133, 20867, 14008, 9135, 7339, 2474, 25274, 24496, 18601, 42399, 40609, 35755, 58529, 53668, 51896, 10161, 4263, 3509, 26278, 21389, 19628, 47526, 37546, 36796, 63660, 54712, 52906, 15277, 5295, 387, 31400, 22451, 16555, 48560, 11428, 14761, 1715, 5001, 30911, 17845, 21183, 49082, 33966, 37287, 65201, 52131, 53416, 15747, 2722, 6056, 31932, 18858, 22202, 41902, 34996, 38323, 58033, 53149, 54462, 8613, 3773, 7078, 11434, 14781, 1721, 5048, 30888, 17826, 21179, 49029, 33956, 37305, 65154, 52150, 53414, 15767, 2739, 6055, 31915, 18860};
        d = 8525753664398965192L;
    }

    static void init$0() {
        $$d = new byte[]{38, -75, -91, -62};
        $$e = 30;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = 105 - r8
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.ev.a.$$d
            int r6 = r6 * 2
            int r6 = 3 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2e
        L17:
            r3 = r2
        L18:
            int r6 = r6 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
        L2e:
            int r4 = -r4
            int r7 = r7 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ev.a.m(int, short, short, java.lang.Object[]):void");
    }

    public a() {
        Object[] objArr = new Object[1];
        k((char) (61995 - TextUtils.getOffsetBefore("", 0)), TextUtils.lastIndexOf("", '0', 0, 0) + 1, 20 - TextUtils.indexOf("", "", 0, 0), objArr);
        this.e = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (6674 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 19 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), 32 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr2);
        this.a = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        k((char) ExpandableListView.getPackedPositionType(0L), 51 - MotionEvent.axisFromString(""), (KeyEvent.getMaxKeyCode() >> 16) + 28, objArr3);
        this.b = ((String) objArr3[0]).intern();
    }

    @Override // o.ey.a
    public final /* synthetic */ c b(b bVar) throws d {
        int i = f + 37;
        j = i % 128;
        int i2 = i % 2;
        c d2 = d(bVar);
        int i3 = j + 3;
        f = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    @Override // o.ey.a
    public final /* bridge */ /* synthetic */ o.fd.a b(o.fc.c cVar, short s) {
        int i = f + 95;
        j = i % 128;
        int i2 = i % 2;
        o.fd.a b = b(false, cVar, s);
        int i3 = j + 43;
        f = i3 % 128;
        int i4 = i3 % 2;
        return b;
    }

    @Override // o.ey.a
    public final /* bridge */ /* synthetic */ b e(c cVar) throws d {
        int i = j + 85;
        f = i % 128;
        boolean z = i % 2 == 0;
        b e2 = e2(cVar);
        switch (z) {
            case false:
                break;
            default:
                int i2 = 38 / 0;
                break;
        }
        int i3 = f + 25;
        j = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    @Override // o.ey.a
    public final /* synthetic */ b e(o.fd.a aVar) throws d {
        int i = f + 77;
        j = i % 128;
        int i2 = i % 2;
        b b = b(aVar);
        int i3 = j + 63;
        f = i3 % 128;
        int i4 = i3 % 2;
        return b;
    }

    @Override // o.ey.a
    public final /* synthetic */ c e(String str, String str2, boolean z) {
        int i = f + Opcodes.LNEG;
        j = i % 128;
        int i2 = i % 2;
        c a = a(str, str2, z);
        int i3 = f + 17;
        j = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.ey.a
    public final /* synthetic */ o.fd.a e(b bVar) throws d {
        int i = j + 5;
        f = i % 128;
        int i2 = i % 2;
        o.fd.a c2 = c(bVar);
        int i3 = j + 37;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 25 : (char) 4) {
            case 4:
                return c2;
            default:
                int i4 = 51 / 0;
                return c2;
        }
    }

    @Override // o.ey.a
    public final a.d d() {
        int i = f + 77;
        j = i % 128;
        switch (i % 2 != 0 ? 'a' : 'D') {
            case Opcodes.LADD /* 97 */:
                a.d dVar = a.d.d;
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return a.d.d;
        }
    }

    private static c a(String str, String str2, boolean z) {
        c cVar = new c(str, str2, z);
        int i = j + 75;
        f = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    /* renamed from: e, reason: avoid collision after fix types in other method */
    private b e2(c cVar) throws d {
        int i = j + 47;
        f = i % 128;
        int i2 = i % 2;
        b e = super.e((a) cVar);
        Object[] objArr = new Object[1];
        k((char) KeyEvent.keyCodeFromString(""), 79 - MotionEvent.axisFromString(""), 18 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
        e.d(((String) objArr[0]).intern(), cVar.r());
        int i3 = f + 23;
        j = i3 % 128;
        int i4 = i3 % 2;
        return e;
    }

    private c d(b bVar) throws d {
        int i = f + Opcodes.LNEG;
        j = i % 128;
        int i2 = i % 2;
        c cVar = (c) super.b(bVar);
        Object[] objArr = new Object[1];
        k((char) (Process.myTid() >> 22), 80 - View.resolveSize(0, 0), 19 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        cVar.b(bVar.i(((String) objArr[0]).intern()).intValue());
        int i3 = j + 97;
        f = i3 % 128;
        int i4 = i3 % 2;
        return cVar;
    }

    private static o.fd.a b(boolean z, o.fc.c cVar, short s) {
        o.fd.a aVar = new o.fd.a(false, cVar, s);
        int i = f + 39;
        j = i % 128;
        int i2 = i % 2;
        return aVar;
    }

    private o.fd.a c(b bVar) throws d {
        int i = j + 77;
        f = i % 128;
        int i2 = i % 2;
        o.fd.a aVar = (o.fd.a) super.e(bVar);
        Object[] objArr = new Object[1];
        k((char) (6674 - View.resolveSize(0, 0)), KeyEvent.normalizeMetaState(0) + 20, 33 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
        aVar.a(bVar.i(((String) objArr[0]).intern()).intValue());
        Object[] objArr2 = new Object[1];
        k((char) Color.green(0), (ViewConfiguration.getWindowTouchSlop() >> 8) + 52, View.combineMeasuredStates(0, 0) + 28, objArr2);
        aVar.e(bVar.i(((String) objArr2[0]).intern()).intValue());
        Object[] objArr3 = new Object[1];
        k((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 61994), ImageFormat.getBitsPerPixel(0) + 1, 21 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr3);
        aVar.b(bVar.i(((String) objArr3[0]).intern()).intValue());
        int i3 = f + 25;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return aVar;
        }
    }

    private b b(o.fd.a aVar) throws d {
        int i = j + 15;
        f = i % 128;
        int i2 = i % 2;
        b e = super.e((a) aVar);
        Object[] objArr = new Object[1];
        k((char) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 61995), ViewConfiguration.getFadingEdgeLength() >> 16, 20 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
        e.d(((String) objArr[0]).intern(), aVar.h());
        Object[] objArr2 = new Object[1];
        k((char) (6674 - TextUtils.getTrimmedLength("")), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 19, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 31, objArr2);
        e.d(((String) objArr2[0]).intern(), aVar.i());
        Object[] objArr3 = new Object[1];
        k((char) TextUtils.getCapsMode("", 0, 0), 52 - (ViewConfiguration.getJumpTapTimeout() >> 16), 28 - Color.argb(0, 0, 0, 0), objArr3);
        e.d(((String) objArr3[0]).intern(), aVar.j());
        int i3 = f + 3;
        j = i3 % 128;
        switch (i3 % 2 != 0 ? '+' : '2') {
            case '+':
                int i4 = 55 / 0;
                return e;
            default:
                return e;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 582
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ev.a.k(char, int, int, java.lang.Object[]):void");
    }
}
