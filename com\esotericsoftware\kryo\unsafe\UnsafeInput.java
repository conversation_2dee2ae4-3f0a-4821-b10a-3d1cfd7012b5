package com.esotericsoftware.kryo.unsafe;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Input;
import java.io.InputStream;
import sun.misc.Unsafe;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\unsafe\UnsafeInput.smali */
public class UnsafeInput extends Input {
    public UnsafeInput() {
    }

    public UnsafeInput(int bufferSize) {
        super(bufferSize);
    }

    public UnsafeInput(byte[] buffer) {
        super(buffer);
    }

    public UnsafeInput(byte[] buffer, int offset, int count) {
        super(buffer, offset, count);
    }

    public UnsafeInput(InputStream inputStream) {
        super(inputStream);
    }

    public UnsafeInput(InputStream inputStream, int bufferSize) {
        super(inputStream, bufferSize);
    }

    @Override // com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public int read() throws KryoException {
        if (optional(1) <= 0) {
            return -1;
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        return unsafe.getByte(bArr, j + i) & 255;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public byte readByte() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        return unsafe.getByte(bArr, j + i);
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readByteUnsigned() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        return unsafe.getByte(bArr, j + i) & 255;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int readInt() throws KryoException {
        require(4);
        int result = UnsafeUtil.unsafe.getInt(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 4;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public long readLong() throws KryoException {
        require(8);
        long result = UnsafeUtil.unsafe.getLong(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 8;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public float readFloat() throws KryoException {
        require(4);
        float result = UnsafeUtil.unsafe.getFloat(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 4;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public double readDouble() throws KryoException {
        require(8);
        double result = UnsafeUtil.unsafe.getDouble(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 8;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public short readShort() throws KryoException {
        require(2);
        short result = UnsafeUtil.unsafe.getShort(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 2;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public char readChar() throws KryoException {
        require(2);
        char result = UnsafeUtil.unsafe.getChar(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position);
        this.position += 2;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean readBoolean() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        byte[] bArr = this.buffer;
        long j = UnsafeUtil.byteArrayBaseOffset;
        int i = this.position;
        this.position = i + 1;
        boolean result = unsafe.getByte(bArr, j + ((long) i)) != 0;
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public int[] readInts(int length) throws KryoException {
        int[] array = new int[length];
        readBytes(array, UnsafeUtil.intArrayBaseOffset, length << 2);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public long[] readLongs(int length) throws KryoException {
        long[] array = new long[length];
        readBytes(array, UnsafeUtil.longArrayBaseOffset, length << 3);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public float[] readFloats(int length) throws KryoException {
        float[] array = new float[length];
        readBytes(array, UnsafeUtil.floatArrayBaseOffset, length << 2);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public double[] readDoubles(int length) throws KryoException {
        double[] array = new double[length];
        readBytes(array, UnsafeUtil.doubleArrayBaseOffset, length << 3);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public short[] readShorts(int length) throws KryoException {
        short[] array = new short[length];
        readBytes(array, UnsafeUtil.shortArrayBaseOffset, length << 1);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public char[] readChars(int length) throws KryoException {
        char[] array = new char[length];
        readBytes(array, UnsafeUtil.charArrayBaseOffset, length << 1);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public boolean[] readBooleans(int length) throws KryoException {
        boolean[] array = new boolean[length];
        readBytes(array, UnsafeUtil.booleanArrayBaseOffset, length);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.Input
    public void readBytes(byte[] bytes, int offset, int count) throws KryoException {
        readBytes(bytes, UnsafeUtil.byteArrayBaseOffset + offset, count);
    }

    public void readBytes(Object to, long offset, int count) throws KryoException {
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            UnsafeUtil.unsafe.copyMemory(this.buffer, UnsafeUtil.byteArrayBaseOffset + this.position, to, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(count, this.capacity);
                require(copyCount);
            } else {
                return;
            }
        }
    }
}
