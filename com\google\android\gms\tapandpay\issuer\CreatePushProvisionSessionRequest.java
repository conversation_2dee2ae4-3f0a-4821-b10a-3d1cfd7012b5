package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\CreatePushProvisionSessionRequest.smali */
public class CreatePushProvisionSessionRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<CreatePushProvisionSessionRequest> CREATOR = new zza();
    private final String zza;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\CreatePushProvisionSessionRequest$Builder.smali */
    public static class Builder {
        private String zza;

        public CreatePushProvisionSessionRequest build() {
            return new CreatePushProvisionSessionRequest(this.zza);
        }

        public Builder setIntegratorId(String str) {
            this.zza = str;
            return this;
        }
    }

    CreatePushProvisionSessionRequest(String str) {
        this.zza = str;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, this.zza, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
