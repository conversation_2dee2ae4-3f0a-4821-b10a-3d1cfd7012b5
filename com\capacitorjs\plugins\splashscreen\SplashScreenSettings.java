package com.capacitorjs.plugins.splashscreen;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\capacitorjs\plugins\splashscreen\SplashScreenSettings.smali */
public class SplashScreenSettings {
    private Integer showDuration = 3000;
    private Integer fadeInDuration = 200;
    private Integer fadeOutDuration = 200;
    private boolean autoHide = true;

    public Integer getShowDuration() {
        return this.showDuration;
    }

    public void setShowDuration(Integer showDuration) {
        this.showDuration = showDuration;
    }

    public Integer getFadeInDuration() {
        return this.fadeInDuration;
    }

    public void setFadeInDuration(Integer fadeInDuration) {
        this.fadeInDuration = fadeInDuration;
    }

    public Integer getFadeOutDuration() {
        return this.fadeOutDuration;
    }

    public void setFadeOutDuration(Integer fadeOutDuration) {
        this.fadeOutDuration = fadeOutDuration;
    }

    public boolean isAutoHide() {
        return this.autoHide;
    }

    public void setAutoHide(boolean autoHide) {
        this.autoHide = autoHide;
    }
}
