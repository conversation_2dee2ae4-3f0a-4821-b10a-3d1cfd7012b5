package o.an;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\a.smali */
public interface a<Request, Response> {
    Response a(o.eg.b bVar) throws o.eg.d;

    void b(o.eg.b bVar) throws o.eg.d, C0022a;

    /* renamed from: o.an.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\a$a.smali */
    public static final class C0022a extends Exception {
        private static int c = 0;
        private static int e = 1;
        private final o.bv.c b;

        public C0022a(o.bv.c cVar) {
            super(cVar.b());
            this.b = cVar;
        }

        public final o.bv.c c() {
            int i = e;
            int i2 = i + 51;
            c = i2 % 128;
            switch (i2 % 2 != 0 ? '1' : 'D') {
                case '1':
                    throw null;
                default:
                    o.bv.c cVar = this.b;
                    int i3 = i + 43;
                    c = i3 % 128;
                    int i4 = i3 % 2;
                    return cVar;
            }
        }
    }
}
