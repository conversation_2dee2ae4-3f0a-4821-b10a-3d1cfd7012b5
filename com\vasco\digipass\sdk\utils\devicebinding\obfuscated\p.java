package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import java.util.Locale;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0012\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\f\n\u0000\n\u0002\u0010\u0005\n\u0002\b\u0003\bÆ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\f\u0010\rJ\u000e\u0010\u0004\u001a\u00020\u0003*\u0004\u0018\u00010\u0002H\u0007J\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0002*\u0004\u0018\u00010\u0003H\u0007J \u0010\u0004\u001a\u00020\u00072\u0006\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u00022\u0006\u0010\b\u001a\u00020\u0007H\u0002J\u0010\u0010\u0004\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\tH\u0002¨\u0006\u000e"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/p;", "", "", "", "a", "str", "buffer", "", "offset", "", "c", "", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\p.smali */
public final class p {
    public static final p a = new p();
    private static final String b = p.class.getName();

    private p() {
    }

    @JvmStatic
    public static final String a(byte[] bArr) {
        if (bArr == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (byte b2 : bArr) {
            String hexString = Integer.toHexString(b2 & 255);
            Intrinsics.checkNotNullExpressionValue(hexString, "toHexString(value)");
            if (hexString.length() == 1) {
                sb.append('0');
            }
            sb.append(hexString);
        }
        String sb2 = sb.toString();
        Intrinsics.checkNotNullExpressionValue(sb2, "buffer.toString()");
        String upperCase = sb2.toUpperCase(Locale.ROOT);
        Intrinsics.checkNotNullExpressionValue(upperCase, "this as java.lang.String).toUpperCase(Locale.ROOT)");
        return upperCase;
    }

    @JvmStatic
    public static final byte[] a(String str) {
        if (str == null) {
            return null;
        }
        byte[] bArr = new byte[str.length() / 2];
        a.a(str, bArr, 0);
        return bArr;
    }

    private final int a(String str, byte[] buffer, int offset) {
        int length = str.length() / 2;
        for (int i = 0; i < length; i++) {
            int i2 = i * 2;
            buffer[i + offset] = (byte) ((a(str.charAt(i2)) << 4) + (a(str.charAt(i2 + 1)) & 255));
        }
        return length;
    }

    private final byte a(char c) {
        if (Intrinsics.compare((int) c, 97) >= 0) {
            return (byte) ((c - 'a') + 10);
        }
        return (byte) (Intrinsics.compare((int) c, 65) >= 0 ? (c - 'A') + 10 : c - '0');
    }
}
