package o.er;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\i.smali */
public final class i extends a {
    private final String a;
    private static int e = 0;
    private static int d = 1;

    public i(boolean z, String str) {
        super(z);
        this.a = str;
    }

    public final String d() {
        int i = d + Opcodes.DREM;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.a;
            default:
                throw null;
        }
    }
}
