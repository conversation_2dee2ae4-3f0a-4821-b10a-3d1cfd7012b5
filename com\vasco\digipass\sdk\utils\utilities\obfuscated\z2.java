package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\z2.smali */
public class z2 implements e {
    private final x3 b;
    private int x = 0;

    z2(x3 x3Var) {
        this.b = x3Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return d.b(this.b.c());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public InputStream b() throws IOException {
        return a(false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public int d() {
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0("IOException converting stream to byte array: " + e.getMessage(), e);
        }
    }

    private InputStream a(boolean z) throws IOException {
        int b = this.b.b();
        if (b < 1) {
            throw new IllegalStateException("content octets cannot be empty");
        }
        int read = this.b.read();
        this.x = read;
        if (read > 0) {
            if (b < 2) {
                throw new IllegalStateException("zero length data with non-zero pad bits");
            }
            if (read > 7) {
                throw new IllegalStateException("pad bits cannot be greater than 7 or less than 0");
            }
            if (z) {
                throw new IOException("expected octet-aligned bitstring, but found padBits: " + this.x);
            }
        }
        return this.b;
    }
}
