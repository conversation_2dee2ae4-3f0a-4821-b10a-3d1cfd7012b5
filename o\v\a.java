package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.card.CreateCardRequestBuilder;
import fr.antelop.sdk.card.CreateCardRequestPanSource;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Locale;
import kotlin.text.Typography;
import o.an.b;
import o.an.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\a.smali */
public final class a extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int m;

    /* renamed from: o, reason: collision with root package name */
    private static int[] f99o;
    boolean h;
    private final boolean i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        k = 1;
        a();
        KeyEvent.getDeadChar(0, 0);
        int i = m + Opcodes.DMUL;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void a() {
        f99o = new int[]{-853736374, -939094442, 413493481, -1016400601, 1460038974, 1833408819, -1664215837, 907251321, -1479273854, 1419503475, 872046057, -1462107424, -1812615703, 349049529, -266801438, -2102558079, 2142413234, 1030942158};
    }

    static void init$0() {
        $$d = new byte[]{122, -6, -127, 6};
        $$e = Opcodes.FNEG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(byte r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            byte[] r0 = o.v.a.$$d
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = 116 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L18:
            r3 = r2
        L19:
            int r9 = r9 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r9]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L35:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.a.v(byte, short, short, java.lang.Object[]):void");
    }

    static /* synthetic */ o.p.g a(a aVar) {
        int i = m + 11;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = aVar.l();
        int i3 = m + 55;
        k = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g b(a aVar) {
        int i = m + Opcodes.LREM;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                aVar.l();
                throw null;
            default:
                o.p.g l = aVar.l();
                int i2 = k + 71;
                m = i2 % 128;
                int i3 = i2 % 2;
                return l;
        }
    }

    static /* synthetic */ o.p.g c(a aVar) {
        int i = m + 25;
        k = i % 128;
        switch (i % 2 == 0 ? (char) 17 : (char) 27) {
            case 27:
                return aVar.l();
            default:
                aVar.l();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g d(a aVar) {
        int i = k + 15;
        m = i % 128;
        switch (i % 2 == 0) {
            case false:
                aVar.l();
                throw null;
            default:
                o.p.g l = aVar.l();
                int i2 = m + 33;
                k = i2 % 128;
                int i3 = i2 % 2;
                return l;
        }
    }

    static /* synthetic */ o.p.g e(a aVar) {
        int i = k + 25;
        m = i % 128;
        char c = i % 2 != 0 ? Typography.dollar : ']';
        o.p.g l = aVar.l();
        switch (c) {
            case '$':
                int i2 = 90 / 0;
                break;
        }
        int i3 = k + 3;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 43 / 0;
                return l;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g f(a aVar) {
        int i = k + 27;
        m = i % 128;
        int i2 = i % 2;
        o.p.g l = aVar.l();
        int i3 = k + 25;
        m = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g i(a aVar) {
        int i = k + 59;
        m = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case true:
                aVar.l();
                obj.hashCode();
                throw null;
            default:
                o.p.g l = aVar.l();
                int i2 = k + 17;
                m = i2 % 128;
                switch (i2 % 2 != 0 ? '!' : ')') {
                    case '!':
                        obj.hashCode();
                        throw null;
                    default:
                        return l;
                }
        }
    }

    static /* synthetic */ o.p.g j(a aVar) {
        int i = k + 65;
        m = i % 128;
        int i2 = i % 2;
        o.p.g l = aVar.l();
        int i3 = m + Opcodes.DREM;
        k = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return l;
            default:
                throw null;
        }
    }

    public a(String str, o.eo.e eVar, boolean z) {
        super(str, eVar);
        this.h = false;
        this.i = z;
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = m;
        int i2 = i + 53;
        k = i2 % 128;
        int i3 = i2 % 2;
        if (this.i) {
            int i4 = i + 23;
            k = i4 % 128;
            int i5 = i4 % 2;
            return;
        }
        WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
        Object[] objArr = new Object[1];
        u(new int[]{-529318533, 2011000807, 1680497180, -1893686528, 1222320272, 718381096, -1326411251, -40291569, -649875549, -469797699, 438144909, 225495326, 974585040, -1224637865, 1362367237, 23169784}, 31 - View.MeasureSpec.getMode(0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u(new int[]{-427149274, 2006491163, 1572373066, -728252142, 1403354512, 321486828, -2025820033, -1927790576, -1469917127, -838148174}, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 17, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(((d) this).n.e());
        Object[] objArr3 = new Object[1];
        u(new int[]{-1691343796, 525091370, -1178813856, -370485665, 65132028, -1419071055, -1495238909, 48695414, 1094127106, -1329433483, -527116683, -487764884, 1258584604, 2142134552, -868177325, 1211508453, -729871909, 1792876780, 1453711144, -1426422373, 667932761, 932740348}, KeyEvent.normalizeMetaState(0) + 44, objArr3);
        throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
    }

    @Override // o.p.h
    public final String d() {
        int i = k + Opcodes.LMUL;
        m = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        u(new int[]{-529318533, 2011000807, 1680497180, -1893686528, 1222320272, 718381096, -1326411251, -40291569, -649875549, -469797699, 438144909, 225495326, 974585040, -1224637865, 1362367237, 23169784}, 31 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = m + 69;
        k = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 4 : 'K') {
            case 4:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* renamed from: o.v.a$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\a$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int a;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            a = 0;
            e = 1;
            int[] iArr = new int[b.c.EnumC0024b.values().length];
            d = iArr;
            try {
                iArr[b.c.EnumC0024b.d.ordinal()] = 1;
                int i = a;
                int i2 = (i ^ 51) + ((i & 51) << 1);
                e = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[b.c.EnumC0024b.e.ordinal()] = 2;
                int i4 = e;
                int i5 = (i4 ^ 91) + ((i4 & 91) << 1);
                a = i5 % 128;
                switch (i5 % 2 != 0 ? ']' : Typography.greater) {
                    case '>':
                        return;
                    default:
                        throw null;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    @Override // o.p.h
    public final void a(final Context context, final o.ei.c cVar, o.h.d dVar) {
        int i = k + 97;
        int i2 = i % 128;
        m = i2;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                switch (context == null) {
                    case false:
                        new o.an.f(context, new f.b<b.c>() { // from class: o.v.a.3
                            public static final byte[] $$a = null;
                            public static final int $$b = 0;
                            private static int $10;
                            private static int $11;
                            private static int a;
                            private static int d;
                            private static byte[] f;
                            private static short[] g;
                            private static int h;
                            private static int i;
                            private static int j;

                            static {
                                init$0();
                                $10 = 0;
                                $11 = 1;
                                j = 0;
                                h = 1;
                                f = new byte[]{DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 14, 53, 32, 63, 9, 50, 45, 17, 34, 5, 55, 53, 95, -17, 80, 1, 10, 51, 90, 1, 7, 38, 83, 19, 8, 50, 39, 51, 39, 22, 34, 12, 53, 90, -95, -81, -93, -95, -113, -61, UtilitiesSDKConstants.SRP_LABEL_ENC, -77, -46, -33, -100, -74, -65, -58, -125, -96, -107, 108, -100, -61, 33, -102, -106, -61, 38, -98, 109, -70, 122, 103, -122, -77, 68, -124, -109, -36, 104, -126, 34, -107, -125, -105, -107, 99, -73, 100, 103, -122, -77, 112, 106, -109, -70, 119, -108, 74, 62, 74, -8, -49, -1, 38, -124, -3, -7, 38, -119, -31, -16, 29, -83, -2, -15, -2, -28, -8, 26, -91, -25, -10, 63, -53, -27, -123, -8, -26, -6, -8, -58, 26, -57, -54, -23, 22, -45, -51, -10, 29, -38, -9, -102, -124, UtilitiesSDKConstants.SRP_LABEL_ENC, -118, -113, -94, 105, -103, -72, -91, 98, -100, -123, -84, 105, -122, -112, -112, -112, -112, -112, -112};
                                a = 909053629;
                                i = -20080573;
                                d = 480500127;
                            }

                            static void init$0() {
                                $$a = new byte[]{48, 67, 97, 27};
                                $$b = 57;
                            }

                            /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
                            /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
                            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0038). Please report as a decompilation issue!!! */
                            /*
                                Code decompiled incorrectly, please refer to instructions dump.
                                To view partially-correct add '--show-bad-code' argument
                            */
                            private static void l(int r6, byte r7, short r8, java.lang.Object[] r9) {
                                /*
                                    int r7 = r7 * 4
                                    int r7 = 3 - r7
                                    int r8 = r8 * 2
                                    int r8 = 110 - r8
                                    byte[] r0 = o.v.a.AnonymousClass3.$$a
                                    int r6 = r6 * 3
                                    int r6 = r6 + 1
                                    byte[] r1 = new byte[r6]
                                    int r6 = r6 + (-1)
                                    r2 = 0
                                    if (r0 != 0) goto L1c
                                    r3 = r1
                                    r4 = r2
                                    r1 = r0
                                    r0 = r9
                                    r9 = r8
                                    r8 = r7
                                    goto L38
                                L1c:
                                    r3 = r2
                                L1d:
                                    int r7 = r7 + 1
                                    byte r4 = (byte) r8
                                    r1[r3] = r4
                                    if (r3 != r6) goto L2c
                                    java.lang.String r6 = new java.lang.String
                                    r6.<init>(r1, r2)
                                    r9[r2] = r6
                                    return
                                L2c:
                                    int r3 = r3 + 1
                                    r4 = r0[r7]
                                    r5 = r8
                                    r8 = r7
                                    r7 = r4
                                    r4 = r3
                                    r3 = r1
                                    r1 = r0
                                    r0 = r9
                                    r9 = r5
                                L38:
                                    int r7 = -r7
                                    int r7 = r7 + r9
                                    r9 = r0
                                    r0 = r1
                                    r1 = r3
                                    r3 = r4
                                    r5 = r8
                                    r8 = r7
                                    r7 = r5
                                    goto L1d
                                */
                                throw new UnsupportedOperationException("Method not decompiled: o.v.a.AnonymousClass3.l(int, byte, short, java.lang.Object[]):void");
                            }

                            @Override // o.an.f.b
                            public final /* synthetic */ void c(b.c cVar2) {
                                int i3 = j + 47;
                                h = i3 % 128;
                                boolean z = i3 % 2 != 0;
                                b(cVar2);
                                switch (z) {
                                    case false:
                                        throw null;
                                    default:
                                        int i4 = h + Opcodes.LSHL;
                                        j = i4 % 128;
                                        switch (i4 % 2 == 0) {
                                            case true:
                                                return;
                                            default:
                                                int i5 = 0 / 0;
                                                return;
                                        }
                                }
                            }

                            private void b(b.c cVar2) {
                                o.ee.g.c();
                                Object[] objArr = new Object[1];
                                k((byte) ((-1) - MotionEvent.axisFromString("")), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - *********, (short) (91 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), (Process.myTid() >> 22) - 9, (KeyEvent.getMaxKeyCode() >> 16) + *********, objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                k((byte) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (-713873132) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) ((-49) - Color.green(0)), (-29) - ((byte) KeyEvent.getModifierMetaStateMask()), ********* - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr2);
                                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                                CreateCardRequestBuilder panSource = new CreateCardRequestBuilder().setIssuerCardId(((d) a.this).n.n()).setPanSource(CreateCardRequestPanSource.IssuerPush);
                                switch (((d) a.this).n.s() != null ? '%' : 'C') {
                                    case 'C':
                                        break;
                                    default:
                                        int i3 = h + 95;
                                        j = i3 % 128;
                                        int i4 = i3 % 2;
                                        o.ee.g.c();
                                        Object[] objArr3 = new Object[1];
                                        k((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), (-713873168) + (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) (91 - View.resolveSize(0, 0)), (-9) - (ViewConfiguration.getWindowTouchSlop() >> 8), ********* - Color.alpha(0), objArr3);
                                        String intern2 = ((String) objArr3[0]).intern();
                                        Object[] objArr4 = new Object[1];
                                        k((byte) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.indexOf("", "") - 713873116, (short) (TextUtils.indexOf("", "") - 5), (-6) - MotionEvent.axisFromString(""), (ViewConfiguration.getEdgeSlop() >> 16) + *********, objArr4);
                                        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                                        panSource.setLastDigits(((d) a.this).n.s().a()).setBin(((d) a.this).n.s().c());
                                        if (((d) a.this).n.s().b() != null) {
                                            Object[] objArr5 = new Object[1];
                                            k((byte) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (-713873077) - Gravity.getAbsoluteGravity(0, 0), (short) (TextUtils.lastIndexOf("", '0', 0, 0) + 39), (-41) - View.MeasureSpec.getSize(0), (ViewConfiguration.getTouchSlop() >> 8) + 924674470, objArr5);
                                            panSource.setExpiryDate(new SimpleDateFormat(((String) objArr5[0]).intern(), Locale.ENGLISH).format(((d) a.this).n.s().b()));
                                            break;
                                        }
                                        break;
                                }
                                switch (AnonymousClass5.d[cVar2.b().ordinal()]) {
                                    case 1:
                                        panSource.setMdesFundingAccountInfo(cVar2.d());
                                        panSource.setMdesTav(cVar2.c());
                                        break;
                                    case 2:
                                        panSource.setVtsEncPaymentInstrument(cVar2.a());
                                        break;
                                }
                                if (((d) a.this).n.q() != null) {
                                    o.ee.g.c();
                                    Object[] objArr6 = new Object[1];
                                    k((byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), TextUtils.indexOf("", "", 0, 0) - *********, (short) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 92), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 8, ********* - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr6);
                                    String intern3 = ((String) objArr6[0]).intern();
                                    Object[] objArr7 = new Object[1];
                                    k((byte) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-*********) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (short) ((-104) - Color.green(0)), (-2) - View.resolveSize(0, 0), ********* - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr7);
                                    o.ee.g.d(intern3, ((String) objArr7[0]).intern());
                                    panSource.setFinancialAccountNumber(((d) a.this).n.q().b());
                                    if (((d) a.this).n.q().c() != null) {
                                        int i5 = j + 85;
                                        h = i5 % 128;
                                        switch (i5 % 2 == 0 ? 'a' : 'c') {
                                            case Opcodes.LADD /* 97 */:
                                                panSource.setFinancialAccountLabel(((d) a.this).n.q().c());
                                                Object obj2 = null;
                                                obj2.hashCode();
                                                throw null;
                                            default:
                                                panSource.setFinancialAccountLabel(((d) a.this).n.q().c());
                                                break;
                                        }
                                    }
                                }
                                panSource.requireTermsAndConditionsApproval(a.this.h);
                                try {
                                    cVar.b(context, panSource, new AntelopCallback() { // from class: o.v.a.3.2
                                        private static int c = 0;
                                        private static int d = 1;

                                        @Override // fr.antelop.sdk.AntelopCallback
                                        public final void onSuccess() {
                                            int i6 = (d + 10) - 1;
                                            c = i6 % 128;
                                            int i7 = i6 % 2;
                                            switch (a.b(a.this) != null) {
                                                case false:
                                                    break;
                                                default:
                                                    int i8 = d;
                                                    int i9 = (i8 ^ 15) + ((i8 & 15) << 1);
                                                    c = i9 % 128;
                                                    int i10 = i9 % 2;
                                                    a.a(a.this).onProcessSuccess();
                                                    int i11 = c + 21;
                                                    d = i11 % 128;
                                                    int i12 = i11 % 2;
                                                    break;
                                            }
                                            int i13 = c;
                                            int i14 = (i13 & 9) + (i13 | 9);
                                            d = i14 % 128;
                                            int i15 = i14 % 2;
                                        }

                                        @Override // fr.antelop.sdk.AntelopCallback
                                        public final void onError(AntelopError antelopError) {
                                            int i6 = (d + 52) - 1;
                                            c = i6 % 128;
                                            int i7 = i6 % 2;
                                            if (a.c(a.this) != null) {
                                                a.d(a.this).onError(new o.bv.c(antelopError.getCode(), antelopError.getMessage()));
                                                int i8 = d + 41;
                                                c = i8 % 128;
                                                int i9 = i8 % 2;
                                            }
                                        }
                                    });
                                } catch (WalletValidationException e) {
                                    if (a.e(a.this) != null) {
                                        a.j(a.this).onError(new o.bv.c(AntelopErrorCode.InternalError, e.getMessage()));
                                        int i6 = h + 3;
                                        j = i6 % 128;
                                        int i7 = i6 % 2;
                                    }
                                }
                            }

                            @Override // o.an.f.b
                            public final void c(o.bb.d dVar2) {
                                int i3 = h + 3;
                                j = i3 % 128;
                                int i4 = i3 % 2;
                                o.ee.g.c();
                                boolean z = false;
                                Object[] objArr = new Object[1];
                                k((byte) View.MeasureSpec.getSize(0), (-713873166) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (short) (View.getDefaultSize(0, 0) + 91), ImageFormat.getBitsPerPixel(0) - 8, TextUtils.getOffsetBefore("", 0) + *********, objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                k((byte) (ViewConfiguration.getKeyRepeatDelay() >> 16), Color.red(0) - 713873032, (short) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 24), (-28) - (ViewConfiguration.getScrollBarSize() >> 8), TextUtils.getOffsetBefore("", 0) + *********, objArr2);
                                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                                o.bv.c c = o.bv.c.c(dVar2);
                                if (a.f(a.this) != null) {
                                    z = true;
                                }
                                switch (z) {
                                    case false:
                                        break;
                                    default:
                                        a.i(a.this).onError(c);
                                        break;
                                }
                                int i5 = h + 73;
                                j = i5 % 128;
                                int i6 = i5 % 2;
                            }

                            private static void k(byte b, int i3, short s, int i4, int i5, Object[] objArr) {
                                int i6;
                                boolean z;
                                int i7;
                                int length;
                                byte[] bArr;
                                int i8;
                                o.a.f fVar = new o.a.f();
                                StringBuilder sb = new StringBuilder();
                                int i9 = 2;
                                try {
                                    Object[] objArr2 = {Integer.valueOf(i4), Integer.valueOf(a)};
                                    Object obj2 = o.e.a.s.get(-2120899312);
                                    long j2 = 0;
                                    if (obj2 == null) {
                                        Class cls = (Class) o.e.a.c(Drawable.resolveOpacity(0, 0) + 11, (char) (TextUtils.indexOf((CharSequence) "", '0') + 1), ExpandableListView.getPackedPositionType(0L) + 65);
                                        byte b2 = (byte) 0;
                                        byte b3 = b2;
                                        Object[] objArr3 = new Object[1];
                                        l(b2, b3, (byte) (b3 + 1), objArr3);
                                        obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj2);
                                    }
                                    int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                                    boolean z2 = intValue == -1;
                                    switch (z2 ? (char) 5 : 'A') {
                                        case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                            break;
                                        default:
                                            byte[] bArr2 = f;
                                            float f2 = 0.0f;
                                            if (bArr2 != null) {
                                                int i10 = $10 + 91;
                                                $11 = i10 % 128;
                                                if (i10 % 2 == 0) {
                                                    length = bArr2.length;
                                                    bArr = new byte[length];
                                                    i8 = 1;
                                                } else {
                                                    length = bArr2.length;
                                                    bArr = new byte[length];
                                                    i8 = 0;
                                                }
                                                while (i8 < length) {
                                                    int i11 = $10 + 69;
                                                    $11 = i11 % 128;
                                                    int i12 = i11 % i9;
                                                    try {
                                                        Object[] objArr4 = {Integer.valueOf(bArr2[i8])};
                                                        Object obj3 = o.e.a.s.get(494867332);
                                                        if (obj3 == null) {
                                                            Class cls2 = (Class) o.e.a.c((ViewConfiguration.getZoomControlsTimeout() > j2 ? 1 : (ViewConfiguration.getZoomControlsTimeout() == j2 ? 0 : -1)) + 18, (char) (16426 - (ViewConfiguration.getScrollFriction() > f2 ? 1 : (ViewConfiguration.getScrollFriction() == f2 ? 0 : -1))), ((byte) KeyEvent.getModifierMetaStateMask()) + 151);
                                                            byte b4 = (byte) 0;
                                                            byte b5 = b4;
                                                            Object[] objArr5 = new Object[1];
                                                            l(b4, b5, b5, objArr5);
                                                            obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                                            o.e.a.s.put(494867332, obj3);
                                                        }
                                                        bArr[i8] = ((Byte) ((Method) obj3).invoke(null, objArr4)).byteValue();
                                                        i8++;
                                                        i9 = 2;
                                                        j2 = 0;
                                                        f2 = 0.0f;
                                                    } catch (Throwable th) {
                                                        Throwable cause = th.getCause();
                                                        if (cause == null) {
                                                            throw th;
                                                        }
                                                        throw cause;
                                                    }
                                                }
                                                bArr2 = bArr;
                                            }
                                            switch (bArr2 != null ? '6' : 'L') {
                                                case Opcodes.ISTORE /* 54 */:
                                                    int i13 = $10 + 25;
                                                    $11 = i13 % 128;
                                                    if (i13 % 2 == 0) {
                                                        byte[] bArr3 = f;
                                                        try {
                                                            Object[] objArr6 = {Integer.valueOf(i3), Integer.valueOf(d)};
                                                            Object obj4 = o.e.a.s.get(-2120899312);
                                                            if (obj4 == null) {
                                                                Class cls3 = (Class) o.e.a.c(11 - Gravity.getAbsoluteGravity(0, 0), (char) (ViewConfiguration.getTouchSlop() >> 8), Color.green(0) + 65);
                                                                byte b6 = (byte) 0;
                                                                byte b7 = b6;
                                                                Object[] objArr7 = new Object[1];
                                                                l(b6, b7, (byte) (b7 + 1), objArr7);
                                                                obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                                                o.e.a.s.put(-2120899312, obj4);
                                                            }
                                                            i7 = ((byte) (bArr3[((Integer) ((Method) obj4).invoke(null, objArr6)).intValue()] % (-5810760824076169584L))) - ((int) (a & (-5810760824076169584L)));
                                                        } catch (Throwable th2) {
                                                            Throwable cause2 = th2.getCause();
                                                            if (cause2 == null) {
                                                                throw th2;
                                                            }
                                                            throw cause2;
                                                        }
                                                    } else {
                                                        byte[] bArr4 = f;
                                                        try {
                                                            Object[] objArr8 = {Integer.valueOf(i3), Integer.valueOf(d)};
                                                            Object obj5 = o.e.a.s.get(-2120899312);
                                                            if (obj5 == null) {
                                                                Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getFadingEdgeLength() >> 16), (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 64 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)));
                                                                byte b8 = (byte) 0;
                                                                byte b9 = b8;
                                                                Object[] objArr9 = new Object[1];
                                                                l(b8, b9, (byte) (b9 + 1), objArr9);
                                                                obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                                                o.e.a.s.put(-2120899312, obj5);
                                                            }
                                                            i7 = ((byte) (bArr4[((Integer) ((Method) obj5).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (a ^ (-5810760824076169584L)));
                                                        } catch (Throwable th3) {
                                                            Throwable cause3 = th3.getCause();
                                                            if (cause3 == null) {
                                                                throw th3;
                                                            }
                                                            throw cause3;
                                                        }
                                                    }
                                                    intValue = (byte) i7;
                                                    break;
                                                default:
                                                    intValue = (short) (((short) (g[i3 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (a ^ (-5810760824076169584L))));
                                                    break;
                                            }
                                    }
                                    if (intValue > 0) {
                                        int i14 = $11 + 15;
                                        $10 = i14 % 128;
                                        int i15 = i14 % 2;
                                        int i16 = ((i3 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                                        switch (z2) {
                                            case false:
                                                i6 = 0;
                                                break;
                                            default:
                                                i6 = 1;
                                                break;
                                        }
                                        fVar.d = i16 + i6;
                                        try {
                                            Object[] objArr10 = {fVar, Integer.valueOf(i5), Integer.valueOf(i), sb};
                                            Object obj6 = o.e.a.s.get(160906762);
                                            if (obj6 == null) {
                                                obj6 = ((Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 12, (char) ((Process.getThreadPriority(0) + 20) >> 6), TextUtils.lastIndexOf("", '0') + 604)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                                                o.e.a.s.put(160906762, obj6);
                                            }
                                            ((StringBuilder) ((Method) obj6).invoke(null, objArr10)).append(fVar.e);
                                            fVar.b = fVar.e;
                                            byte[] bArr5 = f;
                                            switch (bArr5 != null) {
                                                case true:
                                                    int length2 = bArr5.length;
                                                    byte[] bArr6 = new byte[length2];
                                                    int i17 = 0;
                                                    while (i17 < length2) {
                                                        bArr6[i17] = (byte) (bArr5[i17] ^ (-5810760824076169584L));
                                                        i17++;
                                                        int i18 = $10 + Opcodes.LMUL;
                                                        $11 = i18 % 128;
                                                        int i19 = i18 % 2;
                                                    }
                                                    bArr5 = bArr6;
                                                    break;
                                            }
                                            switch (bArr5 == null) {
                                                case false:
                                                    int i20 = $11 + 63;
                                                    $10 = i20 % 128;
                                                    if (i20 % 2 == 0) {
                                                        z = true;
                                                        break;
                                                    } else {
                                                        z = false;
                                                        break;
                                                    }
                                                default:
                                                    z = false;
                                                    break;
                                            }
                                            fVar.c = 1;
                                            while (true) {
                                                switch (fVar.c < intValue ? '^' : '1') {
                                                    case '1':
                                                        break;
                                                    default:
                                                        int i21 = $10 + Opcodes.LMUL;
                                                        $11 = i21 % 128;
                                                        int i22 = i21 % 2;
                                                        switch (z ? ' ' : ')') {
                                                            case ' ':
                                                                byte[] bArr7 = f;
                                                                fVar.d = fVar.d - 1;
                                                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                                                break;
                                                            default:
                                                                short[] sArr = g;
                                                                fVar.d = fVar.d - 1;
                                                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                                                break;
                                                        }
                                                        sb.append(fVar.e);
                                                        fVar.b = fVar.e;
                                                        fVar.c++;
                                                }
                                            }
                                        } catch (Throwable th4) {
                                            Throwable cause4 = th4.getCause();
                                            if (cause4 == null) {
                                                throw th4;
                                            }
                                            throw cause4;
                                        }
                                    }
                                    objArr[0] = sb.toString();
                                } catch (Throwable th5) {
                                    Throwable cause5 = th5.getCause();
                                    if (cause5 == null) {
                                        throw th5;
                                    }
                                    throw cause5;
                                }
                            }
                        }, cVar, new o.an.b(new b.C0023b())).a(dVar, o(), ((d) this).n.e(), null);
                        return;
                    default:
                        int i3 = i2 + 47;
                        k = i3 % 128;
                        int i4 = i3 % 2;
                        o.ee.g.c();
                        Object[] objArr = new Object[1];
                        u(new int[]{-1801870547, -880000020, -492735013, -1835202452, 803805673, 1927571145, 2073450999, -627406461, 1229651366, 1025131095, 2030764818, -882186077, 1601500463, -213722996, 194101779, 443832012, 667932761, 932740348}, 36 - View.getDefaultSize(0, 0), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        u(new int[]{1086333595, 775848480, -358956250, 1609507162, -1496647781, -776059211, 1555307142, 554384827, 502284062, 854873090, 241613761, -1864375800, 438834054, -742878715, 672872797, 549535176}, 32 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
                        o.ee.g.d(intern, ((String) objArr2[0]).intern());
                        if (l() != null) {
                            l().onError(new o.bv.c(AntelopErrorCode.InternalError));
                            return;
                        }
                        return;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void b(Context context, o.p.g gVar) throws WalletValidationException {
        int i = k + 25;
        m = i % 128;
        int i2 = i % 2;
        d(context, gVar);
        int i3 = m + 43;
        k = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void b(boolean z) {
        int i = k;
        int i2 = i + 3;
        m = i2 % 128;
        boolean z2 = i2 % 2 == 0;
        this.h = z;
        switch (z2) {
            case true:
                int i3 = i + 63;
                m = i3 % 128;
                int i4 = i3 % 2;
                return;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 872
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.a.u(int[], int, java.lang.Object[]):void");
    }
}
