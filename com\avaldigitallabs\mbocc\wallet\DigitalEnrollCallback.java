package com.avaldigitallabs.mbocc.wallet;

import android.content.Intent;
import com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener;
import fr.antelop.sdk.util.AndroidActivityResultCallback;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalEnrollCallback.smali */
public class DigitalEnrollCallback {
    private static DigitalEnrollCallback instance;
    private static DigitalWalletListener listener;
    private AndroidActivityResultCallback cardStatusCallback = null;

    private DigitalEnrollCallback() {
    }

    public void setWalletListener(DigitalWalletListener listener2) {
        listener = listener2;
    }

    public void emitEnrollStatus(String status) {
        DigitalWalletListener digitalWalletListener = listener;
        if (digitalWalletListener != null) {
            digitalWalletListener.onEnrollStatus(status);
        }
    }

    public void setAndroidActivityResultCallback(AndroidActivityResultCallback cardStatusCallback) {
        this.cardStatusCallback = cardStatusCallback;
    }

    public boolean runAndroidActivityResultCallback(int requestCode, int resultCode, Intent data) {
        AndroidActivityResultCallback androidActivityResultCallback = this.cardStatusCallback;
        if (androidActivityResultCallback == null) {
            return false;
        }
        boolean response = androidActivityResultCallback.onActivityResult(requestCode, resultCode, data);
        this.cardStatusCallback = null;
        return response;
    }

    private static DigitalEnrollCallback getInstance() {
        if (instance == null) {
            instance = new DigitalEnrollCallback();
        }
        return instance;
    }

    public static void setListener(DigitalWalletListener listener2) {
        getInstance().setWalletListener(listener2);
    }

    public static void emitStatus(String notificationStatus) {
        getInstance().emitEnrollStatus(notificationStatus);
    }

    public static void setPushCardCallback(AndroidActivityResultCallback cardStatusCallback) {
        getInstance().setAndroidActivityResultCallback(cardStatusCallback);
    }

    public static boolean runPushCardResult(int requestCode, int resultCode, Intent data) {
        return getInstance().runAndroidActivityResultCallback(requestCode, resultCode, data);
    }
}
