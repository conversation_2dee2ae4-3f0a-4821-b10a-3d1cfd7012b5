package kotlinx.coroutines.flow.internal;

import kotlin.Metadata;
import kotlinx.coroutines.internal.Symbol;

/* compiled from: NullSurrogate.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\"\u0016\u0010\u0000\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0002\u0010\u0003\"\u0016\u0010\u0004\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0005\u0010\u0003\"\u0016\u0010\u0006\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0007\u0010\u0003¨\u0006\b"}, d2 = {"DONE", "Lkotlinx/coroutines/internal/Symbol;", "getDONE$annotations", "()V", "NULL", "getNULL$annotations", "UNINITIALIZED", "getUNINITIALIZED$annotations", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\flow\internal\NullSurrogateKt.smali */
public final class NullSurrogateKt {
    public static final Symbol NULL = new Symbol("NULL");
    public static final Symbol UNINITIALIZED = new Symbol("UNINITIALIZED");
    public static final Symbol DONE = new Symbol("DONE");

    public static /* synthetic */ void getDONE$annotations() {
    }

    public static /* synthetic */ void getNULL$annotations() {
    }

    public static /* synthetic */ void getUNINITIALIZED$annotations() {
    }
}
