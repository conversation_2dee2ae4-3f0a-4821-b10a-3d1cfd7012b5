package com.vasco.digipass.sdk.utils.securestorage.biometrics;

import androidx.fragment.app.FragmentActivity;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0002\b\u0012\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u000b\u001a\u00020\u0002\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\b¢\u0006\u0004\b \u0010!J\u0010\u0010\u0003\u001a\u00020\u0002HÆ\u0003¢\u0006\u0004\b\u0003\u0010\u0004J\u0010\u0010\u0006\u001a\u00020\u0005HÆ\u0003¢\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\bHÆ\u0003¢\u0006\u0004\b\t\u0010\nJ.\u0010\u000e\u001a\u00020\u00002\b\b\u0002\u0010\u000b\u001a\u00020\u00022\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\bHÆ\u0001¢\u0006\u0004\b\u000e\u0010\u000fJ\u0010\u0010\u0011\u001a\u00020\u0010HÖ\u0001¢\u0006\u0004\b\u0011\u0010\u0012J\u0010\u0010\u0013\u001a\u00020\u0005HÖ\u0001¢\u0006\u0004\b\u0013\u0010\u0007J\u001a\u0010\u0015\u001a\u00020\b2\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0015\u0010\u0016R\u0017\u0010\u000b\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0019\u0010\u0004R\u0017\u0010\f\u001a\u00020\u00058\u0006¢\u0006\f\n\u0004\b\u001a\u0010\u001b\u001a\u0004\b\u001c\u0010\u0007R\u0017\u0010\r\u001a\u00020\b8\u0006¢\u0006\f\n\u0004\b\u001d\u0010\u001e\u001a\u0004\b\u001f\u0010\n¨\u0006\""}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/biometrics/BiometricWriteProtectionSettings;", "", "Landroidx/fragment/app/FragmentActivity;", "component1", "()Landroidx/fragment/app/FragmentActivity;", "", "component2", "()I", "", "component3", "()Z", "fragmentActivity", "timeout", "fallbackToDeviceCredential", "copy", "(Landroidx/fragment/app/FragmentActivity;IZ)Lcom/vasco/digipass/sdk/utils/securestorage/biometrics/BiometricWriteProtectionSettings;", "", "toString", "()Ljava/lang/String;", "hashCode", "other", "equals", "(Ljava/lang/Object;)Z", "a", "Landroidx/fragment/app/FragmentActivity;", "getFragmentActivity", "b", "I", "getTimeout", "c", "Z", "getFallbackToDeviceCredential", "<init>", "(Landroidx/fragment/app/FragmentActivity;IZ)V", "lib_release"}, k = 1, mv = {1, 8, 0})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\biometrics\BiometricWriteProtectionSettings.smali */
public final /* data */ class BiometricWriteProtectionSettings {

    /* renamed from: a, reason: from kotlin metadata */
    public final FragmentActivity fragmentActivity;

    /* renamed from: b, reason: from kotlin metadata */
    public final int timeout;

    /* renamed from: c, reason: from kotlin metadata */
    public final boolean fallbackToDeviceCredential;

    public BiometricWriteProtectionSettings(FragmentActivity fragmentActivity, int i, boolean z) {
        Intrinsics.checkNotNullParameter(fragmentActivity, "fragmentActivity");
        this.fragmentActivity = fragmentActivity;
        this.timeout = i;
        this.fallbackToDeviceCredential = z;
    }

    public static /* synthetic */ BiometricWriteProtectionSettings copy$default(BiometricWriteProtectionSettings biometricWriteProtectionSettings, FragmentActivity fragmentActivity, int i, boolean z, int i2, Object obj) {
        if ((i2 & 1) != 0) {
            fragmentActivity = biometricWriteProtectionSettings.fragmentActivity;
        }
        if ((i2 & 2) != 0) {
            i = biometricWriteProtectionSettings.timeout;
        }
        if ((i2 & 4) != 0) {
            z = biometricWriteProtectionSettings.fallbackToDeviceCredential;
        }
        return biometricWriteProtectionSettings.copy(fragmentActivity, i, z);
    }

    /* renamed from: component1, reason: from getter */
    public final FragmentActivity getFragmentActivity() {
        return this.fragmentActivity;
    }

    /* renamed from: component2, reason: from getter */
    public final int getTimeout() {
        return this.timeout;
    }

    /* renamed from: component3, reason: from getter */
    public final boolean getFallbackToDeviceCredential() {
        return this.fallbackToDeviceCredential;
    }

    public final BiometricWriteProtectionSettings copy(FragmentActivity fragmentActivity, int timeout, boolean fallbackToDeviceCredential) {
        Intrinsics.checkNotNullParameter(fragmentActivity, "fragmentActivity");
        return new BiometricWriteProtectionSettings(fragmentActivity, timeout, fallbackToDeviceCredential);
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof BiometricWriteProtectionSettings)) {
            return false;
        }
        BiometricWriteProtectionSettings biometricWriteProtectionSettings = (BiometricWriteProtectionSettings) other;
        return Intrinsics.areEqual(this.fragmentActivity, biometricWriteProtectionSettings.fragmentActivity) && this.timeout == biometricWriteProtectionSettings.timeout && this.fallbackToDeviceCredential == biometricWriteProtectionSettings.fallbackToDeviceCredential;
    }

    public final boolean getFallbackToDeviceCredential() {
        return this.fallbackToDeviceCredential;
    }

    public final FragmentActivity getFragmentActivity() {
        return this.fragmentActivity;
    }

    public final int getTimeout() {
        return this.timeout;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public int hashCode() {
        int hashCode = (Integer.hashCode(this.timeout) + (this.fragmentActivity.hashCode() * 31)) * 31;
        boolean z = this.fallbackToDeviceCredential;
        int i = z;
        if (z != 0) {
            i = 1;
        }
        return hashCode + i;
    }

    public String toString() {
        return "BiometricWriteProtectionSettings(fragmentActivity=" + this.fragmentActivity + ", timeout=" + this.timeout + ", fallbackToDeviceCredential=" + this.fallbackToDeviceCredential + ')';
    }
}
