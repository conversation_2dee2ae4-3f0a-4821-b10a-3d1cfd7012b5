package com.vasco.digipass.sdk.obfuscated;

import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.responses.DigipassResponse;
import com.vasco.digipass.sdk.responses.GenericResponse;
import com.vasco.digipass.sdk.responses.ValidationResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import kotlin.io.encoding.Base64;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\l.smali */
public final class l implements DigipassSDKConstants {
    private static final byte[] a = {-14, -29, -34, -49, 57, 68, 58, 87, 57, -6, 29, -34, Base64.padSymbol, 90, -27, -42};
    private static final byte[] b = {Tnaf.POW_2_WIDTH, -76, -122, 7, -123, 26, -121, 105, -113, 42, -54, 82, -49, -94, -79, 24};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\l$a.smali */
    public static class a extends DigipassResponse {
        final byte[] c;

        public a(int i, byte[] bArr) {
            super(i);
            this.c = bArr;
        }
    }

    protected static a a(byte[] bArr, int i, e eVar, String str) {
        int i2;
        byte[] bArr2;
        byte b2 = eVar.d.a;
        boolean z = b2 == 56 || b2 < 1;
        byte[] c = q.c(bArr);
        if (eVar.c.d || !q.f(bArr)) {
            int a2 = a(bArr, i, eVar);
            if (a2 != 0) {
                q.g(c);
                return new a(a2, null);
            }
            i2 = i;
            bArr2 = c;
        } else {
            String b3 = z ? b(eVar) : a(eVar);
            byte[] bytes = b3.getBytes();
            i2 = b3.length();
            bArr2 = bytes;
        }
        if (!q.f(bArr2) && eVar.c.g && q.d(bArr2)) {
            q.g(bArr2);
            return new a(DigipassSDKReturnCodes.PASSWORD_WEAK, null);
        }
        c(bArr2, eVar);
        byte[] bArr3 = new byte[16];
        if (!q.d(str) && eVar.c.a > 7) {
            eVar.d.n = true;
        }
        a(eVar, bArr, i, bArr2, i2, bArr3, str);
        q.g(bArr2);
        return new a(0, bArr3);
    }

    private static String b(e eVar) {
        byte[] bArr = eVar.c.c;
        return Integer.toHexString(bArr[0] & 255) + Integer.toHexString(bArr[2] & 255) + Integer.toHexString(bArr[4] & 255) + Integer.toHexString(bArr[6] & 255) + Integer.toHexString(bArr[8] & 255) + Integer.toHexString(bArr[10] & 255);
    }

    private static boolean c(byte[] bArr, int i) {
        if (!q.f(bArr) || i >= 0) {
            return !q.f(bArr) && i < 1;
        }
        return true;
    }

    private static void c(byte[] bArr, e eVar) {
        System.arraycopy(b(bArr, eVar), 0, eVar.d.d, 0, 4);
    }

    private static boolean b(byte[] bArr, int i) {
        return (q.f(bArr) && i > 0) || (!q.f(bArr) && i > bArr.length);
    }

    private static byte[] b(byte[] bArr, e eVar) {
        byte b2 = eVar.d.b;
        boolean z = b2 == 56;
        byte[] bArr2 = new byte[4];
        byte b3 = eVar.c.h;
        if (b3 == 1) {
            if (!z && b2 >= 1) {
                r2 = 0;
            }
            bArr2[0] = (byte) ((r2 != 0 ? b(bArr) : a(bArr)) % 10);
        } else if (b3 == 2) {
            System.arraycopy(a(bArr, z || b2 < 5 ? (byte) 1 : (byte) 3), 0, bArr2, 0, 4);
        }
        return bArr2;
    }

    private static int b(byte[] bArr) {
        return a(bArr, false);
    }

    private static byte[] b(byte[] bArr, int i, e eVar) {
        byte[] bArr2 = a;
        byte[] bArr3 = new byte[i + 10 + bArr2.length];
        byte[] c = q.c(bArr);
        System.arraycopy(c, 0, bArr3, 0, i);
        int i2 = i + 0;
        System.arraycopy(eVar.c.b.getBytes(), 0, bArr3, i2, 10);
        System.arraycopy(bArr2, 0, bArr3, i2 + 10, bArr2.length);
        for (int i3 = 0; i3 < eVar.c.l + 1; i3++) {
            bArr3 = UtilitiesSDK.hash((byte) 3, bArr3).getOutputData();
        }
        byte[] bArr4 = new byte[16];
        System.arraycopy(bArr3, 0, bArr4, 0, 16);
        q.g(bArr3);
        q.g(c);
        return bArr4;
    }

    public static String a(e eVar) {
        byte[] bArr = eVar.c.c;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byteArrayOutputStream.write(bArr[0]);
        byteArrayOutputStream.write(bArr[2]);
        byteArrayOutputStream.write(bArr[4]);
        byteArrayOutputStream.write(bArr[6]);
        byteArrayOutputStream.write(bArr[8]);
        byteArrayOutputStream.write(bArr[10]);
        byteArrayOutputStream.write(eVar.c.b.getBytes(), 0, 10);
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        byte[] outputData = UtilitiesSDK.hash((byte) 3, byteArray).getOutputData();
        String a2 = q.a(outputData);
        byteArrayOutputStream.reset();
        q.g(byteArray);
        q.g(outputData);
        return a2;
    }

    protected static ValidationResponse b(byte[] bArr, int i, e eVar, String str) {
        boolean z;
        String a2;
        int length;
        byte b2 = eVar.d.b;
        boolean z2 = false;
        boolean z3 = b2 == 56 || b2 < 1;
        byte[] c = q.c(bArr);
        i iVar = eVar.c;
        if (iVar.a <= 7) {
            z = iVar.d || !q.f(bArr);
        } else {
            z = eVar.d.k;
        }
        if (z) {
            int a3 = a(bArr, i, eVar);
            if (a3 != 0) {
                q.g(c);
                return a(a3, eVar);
            }
            length = i;
        } else {
            if (!q.f(bArr)) {
                q.g(c);
                return a(DigipassSDKReturnCodes.NOT_PASSWORD_PROTECTED, eVar);
            }
            if (z3) {
                a2 = b(eVar);
            } else {
                a2 = a(eVar);
            }
            c = a2.getBytes();
            length = a2.length();
        }
        byte[] b3 = b(c, eVar);
        byte[] bArr2 = eVar.d.d;
        int i2 = 0;
        while (true) {
            if (i2 >= b3.length) {
                z2 = true;
                break;
            }
            if (b3[i2] != bArr2[i2]) {
                break;
            }
            i2++;
        }
        if (z2) {
            g gVar = eVar.d;
            gVar.c = (byte) 1;
            gVar.e = eVar.c.i;
        } else {
            g gVar2 = eVar.d;
            byte b4 = gVar2.e;
            if (b4 == 0) {
                if (eVar.c.k == 0) {
                    eVar.f();
                    eVar.d.c = (byte) 2;
                    q.g(c);
                    return a(DigipassSDKReturnCodes.PASSWORD_LOCK, eVar);
                }
                gVar2.c = (byte) 3;
            } else {
                gVar2.e = (byte) (b4 - 1);
                q.g(c);
                return new ValidationResponse(DigipassSDKReturnCodes.PASSWORD_WRONG, eVar.d.c, p.a(eVar), eVar.d.e, null);
            }
        }
        byte[] bArr3 = new byte[16];
        a(eVar, bArr, i, c, length, bArr3, str);
        q.g(c);
        return new ValidationResponse(0, eVar.d.c, p.a(eVar), 0, bArr3);
    }

    private static int a(byte[] bArr, int i, e eVar) {
        if (bArr == null) {
            return DigipassSDKReturnCodes.PASSWORD_NULL;
        }
        i iVar = eVar.c;
        if (i < iVar.e) {
            return DigipassSDKReturnCodes.PASSWORD_LENGTH_TOO_SHORT;
        }
        if (i > iVar.f) {
            return DigipassSDKReturnCodes.PASSWORD_LENGTH_TOO_LONG;
        }
        return 0;
    }

    public static void a(byte[] bArr, int i) {
        if (!c(bArr, i)) {
            if (b(bArr, i)) {
                throw new h(DigipassSDKReturnCodes.PROVIDED_PASSWORD_AS_STRING_LENGTH_TOO_LONG);
            }
            return;
        }
        throw new h(DigipassSDKReturnCodes.PROVIDED_PASSWORD_AS_STRING_LENGTH_TOO_SHORT);
    }

    private static int a(byte[] bArr) {
        return a(bArr, true);
    }

    private static int a(byte[] bArr, boolean z) {
        boolean z2 = true;
        int i = 0;
        for (byte b2 : bArr) {
            int i2 = b2 - 48;
            if (z2 && (i2 = i2 * 2) > 9) {
                i2 -= 9;
            }
            i += i2;
            z2 = !z2;
        }
        return z ? i % 32 : i;
    }

    private static byte[] a(byte[] bArr, byte b2) {
        byte[] c = q.c(bArr);
        byte[] outputData = UtilitiesSDK.hash(b2, bArr).getOutputData();
        q.g(c);
        return outputData;
    }

    private static byte[] a(byte[] bArr, e eVar, String str) {
        return a(bArr, eVar, str, q.a(UtilitiesSDK.decryptAESCTRWithMasterKey(b, q.a("B3ED8FB496B1FF8567B26178583EB91F01D853E5D7B8B63F75C2C50ED6FC3E80300D986DD3A70A66C65A48F02127111A7B5B260A88E0911561B6FF873F90D5D8")).getOutputData()));
    }

    public static byte[] a(byte[] bArr, e eVar, String str, String str2) {
        if (!eVar.d.n || q.d(str)) {
            str = "";
        }
        byte[] bytes = str.getBytes();
        byte[] bytes2 = eVar.c.b.getBytes();
        byte[] bArr2 = eVar.c.c;
        byte[] a2 = q.a(str2);
        byte[] bArr3 = new byte[bytes.length + bytes2.length + bArr2.length + a2.length];
        System.arraycopy(bytes, 0, bArr3, 0, bytes.length);
        int length = bytes.length + 0;
        System.arraycopy(bytes2, 0, bArr3, length, bytes2.length);
        int length2 = length + bytes2.length;
        System.arraycopy(bArr2, 0, bArr3, length2, bArr2.length);
        System.arraycopy(a2, 0, bArr3, length2 + bArr2.length, a2.length);
        byte[] outputData = UtilitiesSDK.hash((byte) 3, bArr3).getOutputData();
        i iVar = eVar.c;
        int i = iVar.l;
        for (int i2 = 0; i2 < iVar.m; i2++) {
            i *= 10;
        }
        if (i == 0) {
            i = 1;
        }
        byte[] outputData2 = UtilitiesSDK.deriveKey((byte) 3, bArr, outputData, i, 16).getOutputData();
        q.g(a2);
        q.g(bArr3);
        q.g(outputData);
        return outputData2;
    }

    protected static void a(e eVar, byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, String str) {
        int i3;
        byte[] bArr4;
        int i4;
        byte b2 = eVar.d.b;
        boolean f = q.f(bArr3);
        boolean z = f && q.f(bArr);
        if (b2 == 56 && f) {
            byte[] a2 = a(bArr2, eVar);
            byte[] b3 = b(bArr2, i2, eVar);
            a(eVar, a2, b3);
            q.g(a2);
            q.g(b3);
        }
        if (b2 == 56 || b2 < 1) {
            byte[] c = q.c(bArr);
            if (z) {
                String a3 = a(eVar);
                byte[] bytes = a3.getBytes();
                int length = a3.length();
                c(bytes, eVar);
                i3 = length;
                c = bytes;
            } else {
                i3 = i;
            }
            if (f) {
                byte[] b4 = b(bArr2, i2, eVar);
                System.arraycopy(b4, 0, bArr3, 0, b4.length);
                q.g(b4);
            }
            byte[] a4 = q.a(bArr3, 0, 8);
            byte[] outputData = UtilitiesSDK.decrypt((byte) 1, (byte) 2, a4, null, eVar.d.h).getOutputData();
            q.g(a4);
            if (z) {
                byte[] b5 = b(c, i3, eVar);
                System.arraycopy(b5, 0, bArr3, 0, b5.length);
                q.g(b5);
            }
            byte[] outputData2 = UtilitiesSDK.encrypt((byte) 3, (byte) 2, bArr3, null, outputData).getOutputData();
            System.arraycopy(outputData, 0, eVar.d.g, 0, 16);
            System.arraycopy(outputData2, 0, eVar.d.h, 0, 16);
            q.g(outputData);
            q.g(c);
            if (f && eVar.c.h == 1) {
                byte[] bArr5 = new byte[4];
                bArr5[0] = (byte) (a(bArr) % 10);
                System.arraycopy(bArr5, 0, eVar.d.d, 0, 4);
            }
        }
        if ((b2 == 56 || b2 < 3) && f) {
            byte[] c2 = q.c(bArr);
            if (!z) {
                bArr4 = c2;
                i4 = i;
            } else {
                String a5 = a(eVar);
                bArr4 = a5.getBytes();
                i4 = a5.length();
            }
            byte[] b6 = b(bArr4, i4, eVar);
            byte[] a6 = a(bArr4, eVar, str);
            a(eVar, b6, a6, false);
            q.g(bArr4);
            q.g(b6);
            q.g(a6);
        }
        if ((b2 == 56 || b2 < 5) && f && eVar.c.h == 2) {
            System.arraycopy(a(bArr2, (byte) 3), 0, eVar.d.d, 0, 4);
        }
        if (f) {
            byte[] c3 = q.c(bArr);
            if (z) {
                c3 = a(eVar).getBytes();
            }
            byte[] a7 = a(c3, eVar, str);
            System.arraycopy(a7, 0, bArr3, 0, a7.length);
            q.g(c3);
            q.g(a7);
        }
        g gVar = eVar.d;
        gVar.b = (byte) 7;
        if (eVar.c.a <= 7) {
            gVar.a = (byte) 7;
        }
    }

    private static byte[] a(byte[] bArr, e eVar) {
        byte[] bArr2 = new byte[16];
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int i = 0;
        byteArrayOutputStream.write(bArr, 0, bArr.length);
        for (int i2 = 1; i2 < 8; i2++) {
            byteArrayOutputStream.write(byteArrayOutputStream.toByteArray(), byteArrayOutputStream.size(), byteArrayOutputStream.size() * 2);
            if (byteArrayOutputStream.size() > 10) {
                break;
            }
        }
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        System.arraycopy(byteArray, 0, bArr2, 0, 10);
        String substring = eVar.c.b.substring(3);
        System.arraycopy(substring.getBytes(), 1, bArr2, 10, substring.length() - 1);
        while (true) {
            i iVar = eVar.c;
            if (i <= iVar.l) {
                bArr2 = UtilitiesSDK.encrypt((byte) 2, (byte) 2, iVar.c, null, bArr2).getOutputData();
                i++;
            } else {
                byteArrayOutputStream.reset();
                q.g(byteArray);
                return bArr2;
            }
        }
    }

    private static void a(e eVar, byte[] bArr, byte[] bArr2) {
        byte[] outputData = UtilitiesSDK.decrypt((byte) 1, (byte) 2, bArr, null, eVar.d.h).getOutputData();
        System.arraycopy(outputData, 0, eVar.d.g, 0, 16);
        q.g(outputData);
        System.arraycopy(UtilitiesSDK.encrypt((byte) 1, (byte) 2, bArr2, null, outputData).getOutputData(), 0, eVar.d.h, 0, 16);
    }

    public static ValidationResponse a(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, String str) {
        try {
            a(bArr3, i);
            e b2 = p.b(bArr, bArr2);
            if (!b2.e()) {
                b2.f();
                return new ValidationResponse(DigipassSDKReturnCodes.STATUS_INVALID, b2.d.c, p.a(b2), 0, null);
            }
            ValidationResponse b3 = b(bArr3, i, b2, str);
            b2.f();
            return b3;
        } catch (h e) {
            return a(e.a());
        } catch (Exception e2) {
            return a(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    public static GenericResponse a(byte[] bArr, byte[] bArr2, byte[] bArr3, int i, byte[] bArr4, int i2, String str) {
        try {
            a(bArr3, i);
            a(bArr4, i2);
            e b2 = p.b(bArr, bArr2);
            if (!b2.e()) {
                b2.f();
                return new GenericResponse(DigipassSDKReturnCodes.STATUS_INVALID, b2.d.c, p.a(b2));
            }
            GenericResponse a2 = a(bArr3, i, bArr4, i2, b2, str);
            b2.f();
            return a2;
        } catch (h e) {
            return new GenericResponse(e.a());
        } catch (Exception e2) {
            return new GenericResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static GenericResponse a(byte[] bArr, int i, byte[] bArr2, int i2, e eVar, String str) {
        ValidationResponse b2 = b(bArr, i, eVar, str);
        if (b2.getReturnCode() != 0) {
            return new GenericResponse(b2.getReturnCode(), eVar.d.c, p.a(eVar), b2.getAttemptLeft());
        }
        int a2 = a(bArr2, i2, eVar);
        if (a2 != 0) {
            return new GenericResponse(a2, eVar.d.c, p.a(eVar));
        }
        if (eVar.c.g && a(bArr2, bArr)) {
            return new GenericResponse(DigipassSDKReturnCodes.PASSWORD_WEAK, eVar.d.c, p.a(eVar));
        }
        c(bArr2, eVar);
        byte[] a3 = a(bArr2, eVar, str);
        a(eVar, b2.getEncryptionKey(), a3, true);
        g gVar = eVar.d;
        gVar.k = true;
        GenericResponse genericResponse = new GenericResponse(0, gVar.c, p.a(eVar));
        q.g(a3);
        return genericResponse;
    }

    private static boolean a(byte[] bArr, byte[] bArr2) {
        return q.d(bArr) || Arrays.equals(bArr, bArr2);
    }

    public static GenericResponse a(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4) {
        try {
            int a2 = j.a(bArr3);
            if (a2 != 0) {
                return new GenericResponse(a2);
            }
            int a3 = j.a(bArr4);
            if (a3 != 0) {
                return new GenericResponse(a3);
            }
            e b2 = p.b(bArr, bArr2);
            if (!b2.e()) {
                b2.f();
                return new GenericResponse(DigipassSDKReturnCodes.STATUS_INVALID, b2.d.c, p.a(b2));
            }
            a(b2, bArr3, bArr4, true);
            GenericResponse genericResponse = new GenericResponse(0, b2.d.c, p.a(b2));
            b2.f();
            return genericResponse;
        } catch (h e) {
            return new GenericResponse(e.a());
        } catch (Exception e2) {
            return new GenericResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static void a(e eVar, byte[] bArr, byte[] bArr2, boolean z) {
        if (z) {
            a(eVar, (byte[]) null, 0, (byte[]) null, 0, bArr, (String) null);
        }
        byte[] outputData = UtilitiesSDK.decrypt((byte) 3, (byte) 2, bArr, null, eVar.d.h).getOutputData();
        System.arraycopy(outputData, 0, eVar.d.g, 0, 16);
        byte[] outputData2 = UtilitiesSDK.decrypt((byte) 3, (byte) 2, bArr, null, eVar.d.j).getOutputData();
        System.arraycopy(outputData2, 0, eVar.d.i, 0, 16);
        System.arraycopy(UtilitiesSDK.encrypt((byte) 3, (byte) 2, bArr2, null, outputData).getOutputData(), 0, eVar.d.h, 0, 16);
        System.arraycopy(UtilitiesSDK.encrypt((byte) 3, (byte) 2, bArr2, null, outputData2).getOutputData(), 0, eVar.d.j, 0, 16);
        q.g(outputData);
        q.g(outputData2);
    }

    private static ValidationResponse a(int i) {
        return new ValidationResponse(i);
    }

    private static ValidationResponse a(int i, Throwable th) {
        return new ValidationResponse(i, th);
    }

    private static ValidationResponse a(int i, e eVar) {
        return new ValidationResponse(i, eVar.d.c, p.a(eVar), 0, null);
    }
}
