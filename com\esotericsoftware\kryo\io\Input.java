package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.util.Pool;
import java.io.IOException;
import java.io.InputStream;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\Input.smali */
public class Input extends InputStream implements Pool.Poolable {
    protected byte[] buffer;
    protected int capacity;
    protected char[] chars;
    protected InputStream inputStream;
    protected int limit;
    protected int position;
    protected long total;
    protected boolean varEncoding;

    public Input() {
        this.chars = new char[32];
        this.varEncoding = true;
    }

    public Input(int bufferSize) {
        this.chars = new char[32];
        this.varEncoding = true;
        this.capacity = bufferSize;
        this.buffer = new byte[bufferSize];
    }

    public Input(byte[] buffer) {
        this.chars = new char[32];
        this.varEncoding = true;
        setBuffer(buffer, 0, buffer.length);
    }

    public Input(byte[] buffer, int offset, int count) {
        this.chars = new char[32];
        this.varEncoding = true;
        setBuffer(buffer, offset, count);
    }

    public Input(InputStream inputStream) {
        this(4096);
        if (inputStream == null) {
            throw new IllegalArgumentException("inputStream cannot be null.");
        }
        this.inputStream = inputStream;
    }

    public Input(InputStream inputStream, int bufferSize) {
        this(bufferSize);
        if (inputStream == null) {
            throw new IllegalArgumentException("inputStream cannot be null.");
        }
        this.inputStream = inputStream;
    }

    public void setBuffer(byte[] bytes) {
        setBuffer(bytes, 0, bytes.length);
    }

    public void setBuffer(byte[] bytes, int offset, int count) {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        this.buffer = bytes;
        this.position = offset;
        this.limit = offset + count;
        this.capacity = bytes.length;
        this.total = 0L;
        this.inputStream = null;
    }

    public byte[] getBuffer() {
        return this.buffer;
    }

    public InputStream getInputStream() {
        return this.inputStream;
    }

    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
        this.limit = 0;
        reset();
    }

    public boolean getVariableLengthEncoding() {
        return this.varEncoding;
    }

    public void setVariableLengthEncoding(boolean varEncoding) {
        this.varEncoding = varEncoding;
    }

    public long total() {
        return this.total + this.position;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public int position() {
        return this.position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int limit() {
        return this.limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    @Override // java.io.InputStream, com.esotericsoftware.kryo.util.Pool.Poolable
    public void reset() {
        this.position = 0;
        this.total = 0L;
    }

    public void skip(int count) throws KryoException {
        int skipCount = Math.min(this.limit - this.position, count);
        while (true) {
            this.position += skipCount;
            count -= skipCount;
            if (count != 0) {
                skipCount = Math.min(count, this.capacity);
                require(skipCount);
            } else {
                return;
            }
        }
    }

    protected int fill(byte[] buffer, int offset, int count) throws KryoException {
        InputStream inputStream = this.inputStream;
        if (inputStream == null) {
            return -1;
        }
        try {
            return inputStream.read(buffer, offset, count);
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }

    protected int require(int required) throws KryoException {
        int i = this.limit;
        int remaining = i - this.position;
        if (remaining >= required) {
            return remaining;
        }
        int i2 = this.capacity;
        if (required > i2) {
            throw new KryoException("Buffer too small: capacity: " + this.capacity + ", required: " + required);
        }
        if (remaining > 0) {
            int count = fill(this.buffer, i, i2 - i);
            if (count == -1) {
                throw new KryoBufferUnderflowException("Buffer underflow.");
            }
            remaining += count;
            if (remaining >= required) {
                this.limit += count;
                return remaining;
            }
        }
        byte[] bArr = this.buffer;
        System.arraycopy(bArr, this.position, bArr, 0, remaining);
        this.total += this.position;
        this.position = 0;
        while (true) {
            int count2 = fill(this.buffer, remaining, this.capacity - remaining);
            if (count2 == -1) {
                if (remaining < required) {
                    throw new KryoBufferUnderflowException("Buffer underflow.");
                }
            } else {
                remaining += count2;
                if (remaining >= required) {
                    break;
                }
            }
        }
        this.limit = remaining;
        return remaining;
    }

    protected int optional(int optional) throws KryoException {
        int remaining = this.limit - this.position;
        if (remaining >= optional) {
            return optional;
        }
        int optional2 = Math.min(optional, this.capacity);
        byte[] bArr = this.buffer;
        int i = this.limit;
        int count = fill(bArr, i, this.capacity - i);
        if (count == -1) {
            if (remaining == 0) {
                return -1;
            }
            return Math.min(remaining, optional2);
        }
        int remaining2 = remaining + count;
        if (remaining2 >= optional2) {
            this.limit += count;
            return optional2;
        }
        byte[] bArr2 = this.buffer;
        System.arraycopy(bArr2, this.position, bArr2, 0, remaining2);
        this.total += this.position;
        this.position = 0;
        do {
            int count2 = fill(this.buffer, remaining2, this.capacity - remaining2);
            if (count2 == -1) {
                break;
            }
            remaining2 += count2;
        } while (remaining2 < optional2);
        this.limit = remaining2;
        if (remaining2 == 0) {
            return -1;
        }
        return Math.min(remaining2, optional2);
    }

    public boolean end() {
        return optional(1) <= 0;
    }

    @Override // java.io.InputStream
    public int available() throws IOException {
        int i = this.limit - this.position;
        InputStream inputStream = this.inputStream;
        return i + (inputStream != null ? inputStream.available() : 0);
    }

    @Override // java.io.InputStream
    public int read() throws KryoException {
        if (optional(1) <= 0) {
            return -1;
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        return bArr[i] & 255;
    }

    @Override // java.io.InputStream
    public int read(byte[] bytes) throws KryoException {
        return read(bytes, 0, bytes.length);
    }

    @Override // java.io.InputStream
    public int read(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            System.arraycopy(this.buffer, this.position, bytes, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count == 0) {
                break;
            }
            offset += copyCount;
            copyCount = optional(count);
            if (copyCount == -1) {
                if (count == count) {
                    return -1;
                }
            } else if (this.position == this.limit) {
                break;
            }
        }
        return count - count;
    }

    @Override // java.io.InputStream
    public long skip(long count) throws KryoException {
        long remaining = count;
        while (remaining > 0) {
            int skip = (int) Math.min(2147483639L, remaining);
            skip(skip);
            remaining -= skip;
        }
        return count;
    }

    @Override // java.io.InputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws KryoException {
        InputStream inputStream = this.inputStream;
        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
            }
        }
    }

    public byte readByte() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        return bArr[i];
    }

    public int readByteUnsigned() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        return bArr[i] & 255;
    }

    public byte[] readBytes(int length) throws KryoException {
        byte[] bytes = new byte[length];
        readBytes(bytes, 0, length);
        return bytes;
    }

    public void readBytes(byte[] bytes) throws KryoException {
        readBytes(bytes, 0, bytes.length);
    }

    public void readBytes(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            System.arraycopy(this.buffer, this.position, bytes, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(count, this.capacity);
                require(copyCount);
            } else {
                return;
            }
        }
    }

    public int readInt() throws KryoException {
        require(4);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 4;
        return (buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24);
    }

    public int readInt(boolean optimizePositive) throws KryoException {
        return this.varEncoding ? readVarInt(optimizePositive) : readInt();
    }

    public boolean canReadInt() throws KryoException {
        return this.varEncoding ? canReadVarInt() : this.limit - this.position >= 4 || optional(4) == 4;
    }

    public int readVarInt(boolean optimizePositive) throws KryoException {
        if (require(1) < 5) {
            return readVarInt_slow(optimizePositive);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        int b = bArr[i];
        int result = b & 127;
        if ((b & 128) != 0) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int p2 = p + 1;
            int b2 = buffer[p];
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                int p3 = p2 + 1;
                int b3 = buffer[p2];
                result |= (b3 & 127) << 14;
                if ((b3 & 128) == 0) {
                    p2 = p3;
                } else {
                    p2 = p3 + 1;
                    int b4 = buffer[p3];
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        result |= (buffer[p2] & 127) << 28;
                        p2++;
                    }
                }
            }
            this.position = p2;
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    private int readVarInt_slow(boolean optimizePositive) {
        byte[] bArr = this.buffer;
        int i = this.position;
        int i2 = i + 1;
        this.position = i2;
        int b = bArr[i];
        int result = b & 127;
        if ((b & 128) != 0) {
            if (i2 == this.limit) {
                require(1);
            }
            byte[] buffer = this.buffer;
            int i3 = this.position;
            int i4 = i3 + 1;
            this.position = i4;
            int b2 = buffer[i3];
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                if (i4 == this.limit) {
                    require(1);
                }
                int i5 = this.position;
                int i6 = i5 + 1;
                this.position = i6;
                int b3 = buffer[i5];
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    if (i6 == this.limit) {
                        require(1);
                    }
                    int i7 = this.position;
                    int i8 = i7 + 1;
                    this.position = i8;
                    int b4 = buffer[i7];
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        if (i8 == this.limit) {
                            require(1);
                        }
                        int i9 = this.position;
                        this.position = i9 + 1;
                        result |= (buffer[i9] & 127) << 28;
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    public boolean canReadVarInt() throws KryoException {
        if (this.limit - this.position >= 5) {
            return true;
        }
        if (optional(5) <= 0) {
            return false;
        }
        int p = this.position;
        int limit = this.limit;
        byte[] buffer = this.buffer;
        int p2 = p + 1;
        if ((buffer[p] & 128) == 0) {
            return true;
        }
        if (p2 == limit) {
            return false;
        }
        int p3 = p2 + 1;
        if ((buffer[p2] & 128) == 0) {
            return true;
        }
        if (p3 == limit) {
            return false;
        }
        int p4 = p3 + 1;
        if ((buffer[p3] & 128) == 0) {
            return true;
        }
        if (p4 == limit) {
            return false;
        }
        return (buffer[p4] & 128) == 0 || p4 + 1 != limit;
    }

    public boolean readVarIntFlag() {
        if (this.position == this.limit) {
            require(1);
        }
        return (this.buffer[this.position] & ByteCompanionObject.MIN_VALUE) != 0;
    }

    public int readVarIntFlag(boolean optimizePositive) {
        if (require(1) < 5) {
            return readVarIntFlag_slow(optimizePositive);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        int b = bArr[i];
        int result = b & 63;
        if ((b & 64) != 0) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int p2 = p + 1;
            int b2 = buffer[p];
            result |= (b2 & 127) << 6;
            if ((b2 & 128) != 0) {
                int p3 = p2 + 1;
                int b3 = buffer[p2];
                result |= (b3 & 127) << 13;
                if ((b3 & 128) == 0) {
                    p2 = p3;
                } else {
                    p2 = p3 + 1;
                    int b4 = buffer[p3];
                    result |= (b4 & 127) << 20;
                    if ((b4 & 128) != 0) {
                        result |= (buffer[p2] & 127) << 27;
                        p2++;
                    }
                }
            }
            this.position = p2;
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    private int readVarIntFlag_slow(boolean optimizePositive) {
        byte[] bArr = this.buffer;
        int i = this.position;
        int i2 = i + 1;
        this.position = i2;
        int b = bArr[i];
        int result = b & 63;
        if ((b & 64) != 0) {
            if (i2 == this.limit) {
                require(1);
            }
            byte[] buffer = this.buffer;
            int i3 = this.position;
            int i4 = i3 + 1;
            this.position = i4;
            int b2 = buffer[i3];
            result |= (b2 & 127) << 6;
            if ((b2 & 128) != 0) {
                if (i4 == this.limit) {
                    require(1);
                }
                int i5 = this.position;
                int i6 = i5 + 1;
                this.position = i6;
                int b3 = buffer[i5];
                result |= (b3 & 127) << 13;
                if ((b3 & 128) != 0) {
                    if (i6 == this.limit) {
                        require(1);
                    }
                    int i7 = this.position;
                    int i8 = i7 + 1;
                    this.position = i8;
                    int b4 = buffer[i7];
                    result |= (b4 & 127) << 20;
                    if ((b4 & 128) != 0) {
                        if (i8 == this.limit) {
                            require(1);
                        }
                        int i9 = this.position;
                        this.position = i9 + 1;
                        result |= (buffer[i9] & 127) << 27;
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(result & 1));
    }

    public long readLong() throws KryoException {
        require(8);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 8;
        return ((buffer[p + 1] & 255) << 8) | (buffer[p] & 255) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24) | ((buffer[p + 4] & 255) << 32) | ((buffer[p + 5] & 255) << 40) | ((buffer[p + 6] & 255) << 48) | (buffer[p + 7] << 56);
    }

    public long readLong(boolean optimizePositive) throws KryoException {
        return this.varEncoding ? readVarLong(optimizePositive) : readLong();
    }

    public long readVarLong(boolean optimizePositive) throws KryoException {
        if (require(1) < 9) {
            return readVarLong_slow(optimizePositive);
        }
        int p = this.position;
        int p2 = p + 1;
        int b = this.buffer[p];
        long result = b & 127;
        if ((b & 128) != 0) {
            byte[] buffer = this.buffer;
            int p3 = p2 + 1;
            int b2 = buffer[p2];
            result |= (b2 & 127) << 7;
            if ((b2 & 128) == 0) {
                p2 = p3;
            } else {
                p2 = p3 + 1;
                int b3 = buffer[p3];
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    int p4 = p2 + 1;
                    int b4 = buffer[p2];
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) == 0) {
                        p2 = p4;
                    } else {
                        p2 = p4 + 1;
                        int b5 = buffer[p4];
                        result |= (b5 & 127) << 28;
                        if ((b5 & 128) != 0) {
                            int p5 = p2 + 1;
                            int b6 = buffer[p2];
                            result |= (b6 & 127) << 35;
                            if ((b6 & 128) == 0) {
                                p2 = p5;
                            } else {
                                p2 = p5 + 1;
                                int b7 = buffer[p5];
                                result |= (b7 & 127) << 42;
                                if ((b7 & 128) != 0) {
                                    int p6 = p2 + 1;
                                    int b8 = buffer[p2];
                                    result |= (b8 & 127) << 49;
                                    if ((b8 & 128) == 0) {
                                        p2 = p6;
                                    } else {
                                        p2 = p6 + 1;
                                        result |= buffer[p6] << 56;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        this.position = p2;
        return optimizePositive ? result : (result >>> 1) ^ (-(1 & result));
    }

    private long readVarLong_slow(boolean optimizePositive) {
        byte[] bArr = this.buffer;
        int i = this.position;
        int i2 = i + 1;
        this.position = i2;
        int b = bArr[i];
        long result = b & 127;
        if ((b & 128) != 0) {
            if (i2 == this.limit) {
                require(1);
            }
            byte[] buffer = this.buffer;
            int i3 = this.position;
            int i4 = i3 + 1;
            this.position = i4;
            int b2 = buffer[i3];
            result |= (b2 & 127) << 7;
            if ((b2 & 128) != 0) {
                if (i4 == this.limit) {
                    require(1);
                }
                int i5 = this.position;
                int i6 = i5 + 1;
                this.position = i6;
                int b3 = buffer[i5];
                result |= (b3 & 127) << 14;
                if ((b3 & 128) != 0) {
                    if (i6 == this.limit) {
                        require(1);
                    }
                    int i7 = this.position;
                    int i8 = i7 + 1;
                    this.position = i8;
                    int b4 = buffer[i7];
                    result |= (b4 & 127) << 21;
                    if ((b4 & 128) != 0) {
                        if (i8 == this.limit) {
                            require(1);
                        }
                        int i9 = this.position;
                        int i10 = i9 + 1;
                        this.position = i10;
                        int b5 = buffer[i9];
                        result |= (b5 & 127) << 28;
                        if ((b5 & 128) != 0) {
                            if (i10 == this.limit) {
                                require(1);
                            }
                            int i11 = this.position;
                            int i12 = i11 + 1;
                            this.position = i12;
                            int b6 = buffer[i11];
                            result |= (b6 & 127) << 35;
                            if ((b6 & 128) != 0) {
                                if (i12 == this.limit) {
                                    require(1);
                                }
                                int i13 = this.position;
                                int i14 = i13 + 1;
                                this.position = i14;
                                int b7 = buffer[i13];
                                result |= (b7 & 127) << 42;
                                if ((b7 & 128) != 0) {
                                    if (i14 == this.limit) {
                                        require(1);
                                    }
                                    int i15 = this.position;
                                    int i16 = i15 + 1;
                                    this.position = i16;
                                    int b8 = buffer[i15];
                                    result |= (b8 & 127) << 49;
                                    if ((b8 & 128) != 0) {
                                        if (i16 == this.limit) {
                                            require(1);
                                        }
                                        this.position = this.position + 1;
                                        result |= buffer[r2] << 56;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return optimizePositive ? result : (result >>> 1) ^ (-(1 & result));
    }

    public boolean canReadLong() throws KryoException {
        return this.varEncoding ? canReadVarLong() : this.limit - this.position >= 8 || optional(8) == 8;
    }

    public boolean canReadVarLong() throws KryoException {
        if (this.limit - this.position >= 9) {
            return true;
        }
        if (optional(5) <= 0) {
            return false;
        }
        int p = this.position;
        int limit = this.limit;
        byte[] buffer = this.buffer;
        int p2 = p + 1;
        if ((buffer[p] & 128) == 0) {
            return true;
        }
        if (p2 == limit) {
            return false;
        }
        int p3 = p2 + 1;
        if ((buffer[p2] & 128) == 0) {
            return true;
        }
        if (p3 == limit) {
            return false;
        }
        int p4 = p3 + 1;
        if ((buffer[p3] & 128) == 0) {
            return true;
        }
        if (p4 == limit) {
            return false;
        }
        int p5 = p4 + 1;
        if ((buffer[p4] & 128) == 0) {
            return true;
        }
        if (p5 == limit) {
            return false;
        }
        int p6 = p5 + 1;
        if ((buffer[p5] & 128) == 0) {
            return true;
        }
        if (p6 == limit) {
            return false;
        }
        int p7 = p6 + 1;
        if ((buffer[p6] & 128) == 0) {
            return true;
        }
        if (p7 == limit) {
            return false;
        }
        int p8 = p7 + 1;
        if ((buffer[p7] & 128) == 0) {
            return true;
        }
        if (p8 == limit) {
            return false;
        }
        return (buffer[p8] & 128) == 0 || p8 + 1 != limit;
    }

    public float readFloat() throws KryoException {
        require(4);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 4;
        return Float.intBitsToFloat((buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24));
    }

    public float readVarFloat(float precision, boolean optimizePositive) throws KryoException {
        return readVarInt(optimizePositive) / precision;
    }

    public double readDouble() throws KryoException {
        require(8);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 8;
        return Double.longBitsToDouble(((buffer[p + 1] & 255) << 8) | (buffer[p] & 255) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24) | ((buffer[p + 4] & 255) << 32) | ((buffer[p + 5] & 255) << 40) | ((buffer[p + 6] & 255) << 48) | (buffer[p + 7] << 56));
    }

    public double readVarDouble(double precision, boolean optimizePositive) throws KryoException {
        return readVarLong(optimizePositive) / precision;
    }

    public short readShort() throws KryoException {
        require(2);
        int p = this.position;
        this.position = p + 2;
        byte[] bArr = this.buffer;
        return (short) (((bArr[p + 1] & 255) << 8) | (bArr[p] & 255));
    }

    public int readShortUnsigned() throws KryoException {
        require(2);
        int p = this.position;
        this.position = p + 2;
        byte[] bArr = this.buffer;
        return ((bArr[p + 1] & 255) << 8) | (bArr[p] & 255);
    }

    public char readChar() throws KryoException {
        require(2);
        int p = this.position;
        this.position = p + 2;
        byte[] bArr = this.buffer;
        return (char) (((bArr[p + 1] & 255) << 8) | (bArr[p] & 255));
    }

    public boolean readBoolean() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        return bArr[i] == 1;
    }

    public String readString() {
        if (!readVarIntFlag()) {
            return readAsciiString();
        }
        int charCount = readVarIntFlag(true);
        switch (charCount) {
            case 0:
                return null;
            case 1:
                return "";
            default:
                int charCount2 = charCount - 1;
                readUtf8Chars(charCount2);
                return new String(this.chars, 0, charCount2);
        }
    }

    public StringBuilder readStringBuilder() {
        if (!readVarIntFlag()) {
            return new StringBuilder(readAsciiString());
        }
        int charCount = readVarIntFlag(true);
        switch (charCount) {
            case 0:
                return null;
            case 1:
                return new StringBuilder(0);
            default:
                int charCount2 = charCount - 1;
                readUtf8Chars(charCount2);
                StringBuilder builder = new StringBuilder(charCount2);
                builder.append(this.chars, 0, charCount2);
                return builder;
        }
    }

    private void readUtf8Chars(int charCount) {
        if (this.chars.length < charCount) {
            this.chars = new char[charCount];
        }
        byte[] buffer = this.buffer;
        char[] chars = this.chars;
        int charIndex = 0;
        int count = Math.min(require(1), charCount);
        int b = this.position;
        while (true) {
            if (charIndex >= count) {
                break;
            }
            int p = b + 1;
            int b2 = buffer[b];
            if (b2 < 0) {
                b = p - 1;
                break;
            } else {
                chars[charIndex] = (char) b2;
                b = p;
                charIndex++;
            }
        }
        this.position = b;
        if (charIndex < charCount) {
            readUtf8Chars_slow(charCount, charIndex);
        }
    }

    private void readUtf8Chars_slow(int charCount, int charIndex) {
        char[] chars = this.chars;
        byte[] buffer = this.buffer;
        while (charIndex < charCount) {
            if (this.position == this.limit) {
                require(1);
            }
            int i = this.position;
            int i2 = i + 1;
            this.position = i2;
            int b = buffer[i] & 255;
            switch (b >> 4) {
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                    chars[charIndex] = (char) b;
                    break;
                case 12:
                case 13:
                    if (i2 == this.limit) {
                        require(1);
                    }
                    int i3 = this.position;
                    this.position = i3 + 1;
                    chars[charIndex] = (char) (((b & 31) << 6) | (buffer[i3] & 63));
                    break;
                case 14:
                    require(2);
                    int p = this.position;
                    this.position = p + 2;
                    chars[charIndex] = (char) (((b & 15) << 12) | ((buffer[p] & 63) << 6) | (buffer[p + 1] & 63));
                    break;
            }
            charIndex++;
        }
    }

    private String readAsciiString() {
        char[] chars = this.chars;
        byte[] buffer = this.buffer;
        int p = this.position;
        int charCount = 0;
        int n = Math.min(chars.length, this.limit - this.position);
        while (charCount < n) {
            int b = buffer[p];
            if ((b & 128) == 128) {
                this.position = p + 1;
                chars[charCount] = (char) (b & 127);
                return new String(chars, 0, charCount + 1);
            }
            chars[charCount] = (char) b;
            charCount++;
            p++;
        }
        this.position = p;
        return readAscii_slow(charCount);
    }

    private String readAscii_slow(int charCount) {
        char[] chars = this.chars;
        byte[] buffer = this.buffer;
        while (true) {
            if (this.position == this.limit) {
                require(1);
            }
            int i = this.position;
            this.position = i + 1;
            int b = buffer[i];
            if (charCount == chars.length) {
                char[] newChars = new char[charCount * 2];
                System.arraycopy(chars, 0, newChars, 0, charCount);
                chars = newChars;
                this.chars = newChars;
            }
            if ((b & 128) == 128) {
                chars[charCount] = (char) (b & 127);
                return new String(chars, 0, charCount + 1);
            }
            chars[charCount] = (char) b;
            charCount++;
        }
    }

    public int[] readInts(int length) throws KryoException {
        int[] array = new int[length];
        if (optional(length << 2) == (length << 2)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = (buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24);
                i++;
                p += 4;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readInt();
            }
        }
        return array;
    }

    public int[] readInts(int length, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            int[] array = new int[length];
            for (int i = 0; i < length; i++) {
                array[i] = readVarInt(optimizePositive);
            }
            return array;
        }
        return readInts(length);
    }

    public long[] readLongs(int length) throws KryoException {
        long[] array = new long[length];
        if (optional(length << 3) == (length << 3)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = (buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24) | ((buffer[p + 4] & 255) << 32) | ((buffer[p + 5] & 255) << 40) | ((buffer[p + 6] & 255) << 48) | (buffer[p + 7] << 56);
                i++;
                p += 8;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readLong();
            }
        }
        return array;
    }

    public long[] readLongs(int length, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            long[] array = new long[length];
            for (int i = 0; i < length; i++) {
                array[i] = readVarLong(optimizePositive);
            }
            return array;
        }
        return readLongs(length);
    }

    public float[] readFloats(int length) throws KryoException {
        float[] array = new float[length];
        if (optional(length << 2) == (length << 2)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = Float.intBitsToFloat((buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24));
                i++;
                p += 4;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readFloat();
            }
        }
        return array;
    }

    public double[] readDoubles(int length) throws KryoException {
        double[] array = new double[length];
        if (optional(length << 3) == (length << 3)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = Double.longBitsToDouble((buffer[p] & 255) | ((buffer[p + 1] & 255) << 8) | ((buffer[p + 2] & 255) << 16) | ((buffer[p + 3] & 255) << 24) | ((buffer[p + 4] & 255) << 32) | ((buffer[p + 5] & 255) << 40) | ((buffer[p + 6] & 255) << 48) | (buffer[p + 7] << 56));
                i++;
                p += 8;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readDouble();
            }
        }
        return array;
    }

    public short[] readShorts(int length) throws KryoException {
        short[] array = new short[length];
        if (optional(length << 1) == (length << 1)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = (short) ((buffer[p] & 255) | ((buffer[p + 1] & 255) << 8));
                i++;
                p += 2;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readShort();
            }
        }
        return array;
    }

    public char[] readChars(int length) throws KryoException {
        char[] array = new char[length];
        if (optional(length << 1) == (length << 1)) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = (char) ((buffer[p] & 255) | ((buffer[p + 1] & 255) << 8));
                i++;
                p += 2;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readChar();
            }
        }
        return array;
    }

    public boolean[] readBooleans(int length) throws KryoException {
        boolean[] array = new boolean[length];
        if (optional(length) == length) {
            byte[] buffer = this.buffer;
            int p = this.position;
            int i = 0;
            while (i < length) {
                array[i] = buffer[p] != 0;
                i++;
                p++;
            }
            this.position = p;
        } else {
            for (int i2 = 0; i2 < length; i2++) {
                array[i2] = readBoolean();
            }
        }
        return array;
    }
}
