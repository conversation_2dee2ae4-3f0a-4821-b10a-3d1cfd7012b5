package com.google.android.gms.common.api.internal;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.internal.IAccountAccessor;
import java.util.Set;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\common\api\internal\zacs.smali */
public interface zacs {
    void zae(ConnectionResult connectionResult);

    void zaf(IAccountAccessor iAccountAccessor, Set set);

    void zag(int i);
}
