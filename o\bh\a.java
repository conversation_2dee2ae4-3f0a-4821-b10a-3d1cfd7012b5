package o.bh;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.firebase.FirebaseError;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.g;
import o.a.o;
import o.cf.i;
import o.cf.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\a.smali */
public final class a extends o.y.b<c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static int b;
    private static int d;
    private static long e;
    private static int j;
    String c;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\a$c.smali */
    public interface c {
        void d();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        j = 1;
        l();
        ExpandableListView.getPackedPositionGroup(0L);
        Drawable.resolveOpacity(0, 0);
        ViewConfiguration.getTapTimeout();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        int i = j + 7;
        d = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{71, -50, -52, -118};
        $$e = Opcodes.INVOKESPECIAL;
    }

    static void l() {
        a = (char) 17957;
        b = 161105445;
        e = -1116120767795307313L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(int r5, short r6, byte r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 4
            int r5 = r5 + 4
            int r6 = r6 + 99
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.bh.a.$$d
            byte[] r1 = new byte[r7]
            r2 = -1
            int r7 = r7 + r2
            if (r0 != 0) goto L19
            r6 = r5
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L35
        L19:
            r4 = r6
            r6 = r5
            r5 = r4
        L1c:
            int r2 = r2 + 1
            byte r3 = (byte) r5
            r1[r2] = r3
            if (r2 != r7) goto L2c
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2c:
            r3 = r0[r6]
            r4 = r8
            r8 = r7
            r7 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r4
        L35:
            int r7 = -r7
            int r5 = r5 + r7
            int r6 = r6 + 1
            r7 = r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bh.a.n(int, short, byte, java.lang.Object[]):void");
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = j + 17;
        d = i % 128;
        switch (i % 2 != 0 ? '\f' : (char) 28) {
            case 28:
                return m();
            default:
                int i2 = 84 / 0;
                return m();
        }
    }

    public a(Context context, c cVar, o.ei.c cVar2) {
        super(context, cVar, cVar2, o.bb.e.i);
    }

    public final void e(d dVar) {
        int i = j + 39;
        d = i % 128;
        int i2 = i % 2;
        this.c = dVar.a();
        c();
        int i3 = d + 85;
        j = i3 % 128;
        int i4 = i3 % 2;
    }

    private AsyncTaskC0031a m() {
        AsyncTaskC0031a asyncTaskC0031a = new AsyncTaskC0031a(this);
        int i = d + 27;
        j = i % 128;
        switch (i % 2 == 0 ? 'B' : 'V') {
            case Opcodes.SASTORE /* 86 */:
                return asyncTaskC0031a;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = j + 9;
        d = i % 128;
        switch (i % 2 != 0 ? '\b' : (char) 26) {
            case '\b':
                Object[] objArr = new Object[1];
                k(Color.blue(1) * (-1713225695), "\u2fe1\ued9c螂碅ኝⒷᳪ\udb46㉦卼뽈ľ衵\u1cc8ퟏ\uf7c9伸赫뫬", (char) (FirebaseError.ERROR_REQUIRES_RECENT_LOGIN >> (ViewConfiguration.getScrollBarFadeDuration() >>> Opcodes.DDIV)), "ⅇ\ue240Კ浨", "髪㒅\u2455ꮜ", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k(Color.blue(0) - 1713225695, "\u2fe1\ued9c螂碅ኝⒷᳪ\udb46㉦卼뽈ľ衵\u1cc8ퟏ\uf7c9伸赫뫬", (char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 26652), "ⅇ\ue240Კ浨", "髪㒅\u2455ꮜ", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* renamed from: o.bh.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\a$a.smali */
    static final class AsyncTaskC0031a extends o.y.c<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int b;
        private static int c;
        private static int[] d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            c = 1;
            d = new int[]{-1157211904, 8469630, -1658281686, -1634840347, 933564216, 143125208, -112463584, -1487758833, -1090445714, 1953163787, -1831582605, -1351717594, 337131841, -730104635, 728229465, 2011546811, -224579426, 609970384};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0031). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r7, int r8, byte r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.bh.a.AsyncTaskC0031a.$$d
                int r7 = r7 * 3
                int r7 = 1 - r7
                int r9 = 116 - r9
                int r8 = r8 + 4
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L16
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r8
                goto L31
            L16:
                r3 = r2
            L17:
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                if (r4 != r7) goto L26
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L26:
                int r8 = r8 + 1
                r3 = r0[r8]
                r6 = r9
                r9 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r6
            L31:
                int r8 = -r8
                int r8 = r8 + r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r6 = r9
                r9 = r8
                r8 = r6
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bh.a.AsyncTaskC0031a.B(byte, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{108, 119, -51, 110};
            $$e = Opcodes.DRETURN;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = b + 87;
            c = i % 128;
            switch (i % 2 == 0 ? '+' : 'O') {
                case Opcodes.IASTORE /* 79 */:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = b + Opcodes.LREM;
            c = i % 128;
            int i2 = i % 2;
        }

        AsyncTaskC0031a(a aVar) {
            super(aVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = b + 67;
            c = i % 128;
            switch (i % 2 == 0 ? 'X' : (char) 22) {
                case Opcodes.POP2 /* 88 */:
                    Object[] objArr = new Object[1];
                    w(new int[]{888804305, 603443348, 473440241, 1715664749, 1608871853, -1720566373}, 17 << View.MeasureSpec.makeMeasureSpec(1, 1), objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{888804305, 603443348, 473440241, 1715664749, 1608871853, -1720566373}, View.MeasureSpec.makeMeasureSpec(0, 0) + 12, objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            e eVar = new e(context);
            int i = c + 17;
            b = i % 128;
            switch (i % 2 != 0 ? Typography.amp : (char) 31) {
                case '&':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return eVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(new int[]{521290115, -1206833872, 799368830, 1556780603}, 6 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
            bVar.d(((String) objArr[0]).intern(), ((a) e()).c);
            int i = b + 83;
            c = i % 128;
            switch (i % 2 != 0 ? 'P' : 'S') {
                case 'P':
                    return bVar;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = b + 11;
            int i2 = i % 128;
            c = i2;
            int i3 = i % 2;
            int i4 = i2 + 87;
            b = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b + Opcodes.DREM;
            c = i % 128;
            switch (i % 2 == 0 ? 'K' : (char) 22) {
                case 22:
                    return null;
                default:
                    int i2 = 76 / 0;
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = b + 31;
            c = i % 128;
            switch (i % 2 == 0) {
                case false:
                    o.b.c.j(((a) e()).e());
                    i().e();
                    return;
                default:
                    o.b.c.j(((a) e()).e());
                    i().e();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = b + 49;
            c = i % 128;
            switch (i % 2 == 0) {
                case true:
                    o.b.c.j(((a) e()).e());
                    i().e();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    o.b.c.j(((a) e()).e());
                    i().e();
                    int i2 = b + 41;
                    c = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = b + 71;
            c = i % 128;
            int i2 = i % 2;
            ((a) e()).j().d();
            int i3 = b + 91;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = b + 71;
            c = i % 128;
            int i2 = i % 2;
            ((a) e()).j().d();
            int i3 = b + 45;
            c = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    throw null;
                default:
                    return;
            }
        }

        @Override // o.y.c
        public final boolean o() {
            int i = b;
            int i2 = i + 81;
            c = i2 % 128;
            boolean z = false;
            switch (i2 % 2 == 0) {
                case false:
                    break;
                default:
                    z = true;
                    break;
            }
            int i3 = i + 1;
            c = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 7 : '\'') {
                case 7:
                    throw null;
                default:
                    return z;
            }
        }

        @Override // o.y.c
        public final boolean u() {
            int i = b;
            int i2 = i + Opcodes.LREM;
            c = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 17;
            c = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return true;
            }
        }

        private static void w(int[] iArr, int i, Object[] objArr) {
            Object method;
            int[] iArr2;
            int i2;
            int length;
            int[] iArr3;
            int i3;
            g gVar = new g();
            char[] cArr = new char[4];
            char[] cArr2 = new char[iArr.length * 2];
            int[] iArr4 = d;
            long j = 0;
            int i4 = -1667374059;
            int i5 = 1;
            int i6 = 0;
            switch (iArr4 != null ? 'B' : '\f') {
                case '\f':
                    break;
                default:
                    int i7 = $11 + 23;
                    $10 = i7 % 128;
                    switch (i7 % 2 == 0) {
                        case false:
                            length = iArr4.length;
                            iArr3 = new int[length];
                            i3 = 1;
                            break;
                        default:
                            length = iArr4.length;
                            iArr3 = new int[length];
                            i3 = 0;
                            break;
                    }
                    while (i3 < length) {
                        try {
                            Object[] objArr2 = new Object[i5];
                            objArr2[i6] = Integer.valueOf(iArr4[i3]);
                            Object obj = o.e.a.s.get(Integer.valueOf(i4));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((Process.myPid() >> 22) + 10, (char) (ExpandableListView.getPackedPositionType(j) + 8856), 324 - Color.argb(i6, i6, i6, i6));
                                byte b2 = (byte) i6;
                                byte b3 = (byte) (b2 - 1);
                                Object[] objArr3 = new Object[1];
                                B(b2, b3, (byte) (b3 + 1), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj);
                            }
                            iArr3[i3] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                            i3++;
                            j = 0;
                            i4 = -1667374059;
                            i5 = 1;
                            i6 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    iArr4 = iArr3;
                    break;
            }
            int length2 = iArr4.length;
            int[] iArr5 = new int[length2];
            int[] iArr6 = d;
            if (iArr6 != null) {
                int i8 = $10 + 47;
                int i9 = i8 % 128;
                $11 = i9;
                int i10 = i8 % 2;
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i11 = i9 + 21;
                $10 = i11 % 128;
                int i12 = i11 % 2;
                int i13 = 0;
                while (true) {
                    switch (i13 >= length3) {
                        case false:
                            try {
                                Object[] objArr4 = {Integer.valueOf(iArr6[i13])};
                                Object obj2 = o.e.a.s.get(-1667374059);
                                if (obj2 != null) {
                                    iArr2 = iArr6;
                                    i2 = length3;
                                } else {
                                    Class cls2 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 10, (char) (8856 - View.MeasureSpec.makeMeasureSpec(0, 0)), 325 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)));
                                    byte b4 = (byte) 0;
                                    byte b5 = (byte) (b4 - 1);
                                    iArr2 = iArr6;
                                    i2 = length3;
                                    Object[] objArr5 = new Object[1];
                                    B(b4, b5, (byte) (b5 + 1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj2);
                                }
                                iArr7[i13] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i13++;
                                int i14 = $11 + 1;
                                $10 = i14 % 128;
                                int i15 = i14 % 2;
                                iArr6 = iArr2;
                                length3 = i2;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        default:
                            iArr6 = iArr7;
                            break;
                    }
                }
            }
            System.arraycopy(iArr6, 0, iArr5, 0, length2);
            gVar.a = 0;
            while (gVar.a < iArr.length) {
                cArr[0] = (char) (iArr[gVar.a] >> 16);
                cArr[1] = (char) iArr[gVar.a];
                cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
                cArr[3] = (char) iArr[gVar.a + 1];
                gVar.e = (cArr[0] << 16) + cArr[1];
                gVar.c = (cArr[2] << 16) + cArr[3];
                g.d(iArr5);
                for (int i16 = 0; i16 < 16; i16++) {
                    gVar.e ^= iArr5[i16];
                    try {
                        Object[] objArr6 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                        Object obj3 = o.e.a.s.get(-2036901605);
                        if (obj3 == null) {
                            obj3 = ((Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0', 0), (char) TextUtils.indexOf("", "", 0), Gravity.getAbsoluteGravity(0, 0) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                            o.e.a.s.put(-2036901605, obj3);
                        }
                        int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                        gVar.e = gVar.c;
                        gVar.c = intValue;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                int i17 = gVar.e;
                gVar.e = gVar.c;
                gVar.c = i17;
                gVar.c ^= iArr5[16];
                gVar.e ^= iArr5[17];
                int i18 = gVar.e;
                int i19 = gVar.c;
                cArr[0] = (char) (gVar.e >>> 16);
                cArr[1] = (char) gVar.e;
                cArr[2] = (char) (gVar.c >>> 16);
                cArr[3] = (char) gVar.c;
                g.d(iArr5);
                cArr2[gVar.a * 2] = cArr[0];
                cArr2[(gVar.a * 2) + 1] = cArr[1];
                cArr2[(gVar.a * 2) + 2] = cArr[2];
                cArr2[(gVar.a * 2) + 3] = cArr[3];
                try {
                    Object[] objArr7 = {gVar, gVar};
                    Object obj4 = o.e.a.s.get(-331007466);
                    if (obj4 != null) {
                        method = obj4;
                    } else {
                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 12, (char) (55184 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 516 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                        byte b6 = (byte) 0;
                        byte b7 = (byte) (b6 - 1);
                        Object[] objArr8 = new Object[1];
                        B(b6, b7, (byte) (-b7), objArr8);
                        method = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                        o.e.a.s.put(-331007466, method);
                    }
                    ((Method) method).invoke(null, objArr7);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = new String(cArr2, 0, i);
        }
    }

    private static void k(int i, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] charArray;
        char[] charArray2;
        int i2;
        int i3 = $10;
        int i4 = i3 + 21;
        $11 = i4 % 128;
        int i5 = 2;
        int i6 = i4 % 2;
        if (str3 != null) {
            int i7 = i3 + 91;
            $11 = i7 % 128;
            switch (i7 % 2 == 0 ? (char) 30 : Typography.dollar) {
                case '$':
                    cArr = str3.toCharArray();
                    break;
                default:
                    str3.toCharArray();
                    throw null;
            }
        } else {
            cArr = str3;
        }
        char[] cArr2 = cArr;
        switch (str2 != null ? 'Q' : 'c') {
            case Opcodes.FASTORE /* 81 */:
                charArray = str2.toCharArray();
                break;
            default:
                charArray = str2;
                break;
        }
        char[] cArr3 = charArray;
        switch (str == null) {
            case false:
                int i8 = $11 + 61;
                $10 = i8 % 128;
                if (i8 % 2 == 0) {
                    charArray2 = str.toCharArray();
                    break;
                } else {
                    charArray2 = str.toCharArray();
                    int i9 = 3 / 0;
                    break;
                }
            default:
                charArray2 = str;
                break;
        }
        o oVar = new o();
        int length = cArr3.length;
        char[] cArr4 = new char[length];
        int length2 = cArr2.length;
        char[] cArr5 = new char[length2];
        System.arraycopy(cArr3, 0, cArr4, 0, length);
        System.arraycopy(cArr2, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c2);
        cArr5[2] = (char) (cArr5[2] + ((char) i));
        int length3 = charArray2.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i10 = $11 + 51;
            $10 = i10 % 128;
            int i11 = i10 % i5;
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0', 0, 0), (char) (Color.rgb(0, 0, 0) + 16798170), 344 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                    byte b2 = (byte) 0;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    n(b2, b3, b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - TextUtils.indexOf("", "", 0), (char) (TextUtils.lastIndexOf("", '0', 0) + 1), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 206);
                        byte b4 = (byte) 0;
                        byte b5 = (byte) (b4 + 2);
                        Object[] objArr5 = new Object[1];
                        n(b4, b5, (byte) (b5 - 2), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr4[oVar.e % 4] * 32718), Integer.valueOf(cArr5[intValue])};
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(11 - TextUtils.getOffsetAfter("", 0), (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 281 - (ViewConfiguration.getPressedStateDuration() >> 16));
                            byte length4 = (byte) $$d.length;
                            Object[] objArr7 = new Object[1];
                            n((byte) 0, length4, (byte) (length4 - 4), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 != null) {
                                i2 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(19 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) (ExpandableListView.getPackedPositionChild(0L) + 14688), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + Opcodes.LREM);
                                byte b6 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                n(b6, (byte) (b6 | 7), b6, objArr9);
                                i2 = 2;
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((int) (b ^ 6565854932352255525L)) ^ ((cArr4[intValue2] ^ r6[oVar.e]) ^ (e ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                            oVar.e++;
                            cArr4 = cArr4;
                            i5 = i2;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }
}
