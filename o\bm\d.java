package o.bm;

import o.eg.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bm\d.smali */
public abstract class d {
    private static int b = 0;
    private static int a = 1;

    public abstract o.bb.d a(o.bb.d dVar);

    public abstract b a() throws o.eg.d;

    public final int hashCode() {
        int i = b;
        int i2 = (i ^ 23) + ((i & 23) << 1);
        a = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = a;
        int i5 = (i4 & 73) + (i4 | 73);
        b = i5 % 128;
        int i6 = i5 % 2;
        return hashCode;
    }

    public final boolean equals(Object obj) {
        int i = b;
        int i2 = (i ^ 17) + ((i & 17) << 1);
        a = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = a;
        int i5 = (i4 & 99) + (i4 | 99);
        b = i5 % 128;
        int i6 = i5 % 2;
        return equals;
    }

    public final String toString() {
        int i = (a + 96) - 1;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                super.toString();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return super.toString();
        }
    }

    protected final void finalize() throws Throwable {
        int i = b + 11;
        a = i % 128;
        int i2 = i % 2;
        super.finalize();
        int i3 = a + 1;
        b = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
