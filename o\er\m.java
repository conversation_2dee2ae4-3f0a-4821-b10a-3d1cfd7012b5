package o.er;

import android.content.Context;
import android.media.AudioTrack;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import fr.antelop.sdk.digitalcard.SecureCardPushToIssuerNfcWallet;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\m.smali */
public final class m extends h {
    private static char a;
    private static char e;
    private static char g;
    private static char h;
    private static int j;
    private final o.ei.c b;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int i = 0;

    static {
        j = 1;
        c();
        ExpandableListView.getPackedPositionGroup(0L);
        int i2 = i + 5;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        e = (char) 49972;
        g = (char) 53042;
        h = (char) 10851;
        a = (char) 56197;
    }

    public m(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
        this.b = o.ei.c.c();
    }

    @Override // o.er.h
    public final boolean b() {
        int i2 = j + 73;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? 'Q' : Typography.amp) {
            case Opcodes.FASTORE /* 81 */:
                super.b();
                throw null;
            default:
                switch (!super.b()) {
                    case false:
                        return this.b.e().e().b(o.ei.a.d);
                    default:
                        int i3 = i + 37;
                        j = i3 % 128;
                        int i4 = i3 % 2;
                        return false;
                }
        }
    }

    @Override // o.er.h
    public final a[] i() {
        int i2 = j + Opcodes.DMUL;
        i = i2 % 128;
        int i3 = i2 % 2;
        a[] aVarArr = {this.d.e()};
        int i4 = j + 59;
        i = i4 % 128;
        int i5 = i4 % 2;
        return aVarArr;
    }

    /* JADX WARN: Code restructure failed: missing block: B:55:0x00cf, code lost:
    
        continue;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final fr.antelop.sdk.card.Card a() {
        /*
            r7 = this;
            o.ei.c r0 = r7.b
            o.en.d r0 = r0.a()
            r1 = 1
            java.util.LinkedHashMap r0 = r0.c(r1)
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L14:
            boolean r2 = r0.hasNext()
            r3 = 0
            if (r2 == 0) goto Le5
            int r2 = o.er.m.j
            int r2 = r2 + 61
            int r4 = r2 % 128
            o.er.m.i = r4
            int r2 = r2 % 2
            java.lang.Object r2 = r0.next()
            o.eo.e r2 = (o.eo.e) r2
            o.ei.a r4 = r2.h()
            o.ei.a r5 = o.ei.a.d
            r6 = 0
            if (r4 != r5) goto L37
            r4 = r1
            goto L38
        L37:
            r4 = r6
        L38:
            switch(r4) {
                case 0: goto L63;
                default: goto L3b;
            }
        L3b:
            int r4 = o.er.m.i
            int r4 = r4 + 83
            int r5 = r4 % 128
            o.er.m.j = r5
            int r4 = r4 % 2
            if (r4 == 0) goto Ldc
            java.lang.String r3 = r2.n()
            if (r3 == 0) goto L63
            java.lang.String r3 = r2.n()
            o.eo.e r4 = r7.c
            java.lang.String r4 = r4.n()
            boolean r3 = r3.equals(r4)
            if (r3 == 0) goto L63
            fr.antelop.sdk.card.Card r0 = new fr.antelop.sdk.card.Card
            r0.<init>(r2)
            return r0
        L63:
            o.ei.a r3 = r2.h()
            o.ei.a r4 = o.ei.a.d
            if (r3 != r4) goto L6d
            r3 = r6
            goto L6e
        L6d:
            r3 = r1
        L6e:
            switch(r3) {
                case 0: goto L72;
                default: goto L71;
            }
        L71:
            goto Lcf
        L72:
            o.er.j r3 = r2.q()
            if (r3 == 0) goto L7a
            r3 = r1
            goto L7b
        L7a:
            r3 = r6
        L7b:
            switch(r3) {
                case 1: goto L7f;
                default: goto L7e;
            }
        L7e:
            goto L71
        L7f:
            o.eo.e r3 = r7.c
            o.er.j r3 = r3.q()
            if (r3 == 0) goto Lcf
            int r3 = o.er.m.j
            int r3 = r3 + 3
            int r4 = r3 % 128
            o.er.m.i = r4
            int r3 = r3 % 2
            if (r3 == 0) goto Lb1
            o.er.j r3 = r2.q()
            java.lang.String r3 = r3.b()
            o.eo.e r4 = r7.c
            o.er.j r4 = r4.q()
            java.lang.String r4 = r4.b()
            boolean r3 = r3.equals(r4)
            r4 = 62
            int r4 = r4 / r6
            if (r3 == 0) goto L71
            goto Lc9
        Laf:
            r0 = move-exception
            throw r0
        Lb1:
            o.er.j r3 = r2.q()
            java.lang.String r3 = r3.b()
            o.eo.e r4 = r7.c
            o.er.j r4 = r4.q()
            java.lang.String r4 = r4.b()
            boolean r3 = r3.equals(r4)
            if (r3 == 0) goto L71
        Lc9:
            fr.antelop.sdk.card.Card r0 = new fr.antelop.sdk.card.Card
            r0.<init>(r2)
            return r0
        Lcf:
            int r2 = o.er.m.i
            int r2 = r2 + 115
            int r3 = r2 % 128
            o.er.m.j = r3
            int r2 = r2 % 2
            goto L14
        Ldc:
            r2.n()
            r3.hashCode()     // Catch: java.lang.Throwable -> Le3
            throw r3     // Catch: java.lang.Throwable -> Le3
        Le3:
            r0 = move-exception
            throw r0
        Le5:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.m.a():fr.antelop.sdk.card.Card");
    }

    public final boolean e() {
        switch (a() != null) {
            case true:
                int i2 = i + 61;
                j = i2 % 128;
                int i3 = i2 % 2;
                return true;
            default:
                int i4 = i + Opcodes.LREM;
                j = i4 % 128;
                switch (i4 % 2 == 0 ? '\'' : 'H') {
                    case 'H':
                        return false;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0078. Please report as an issue. */
    private String j() {
        int i2 = j + 81;
        i = i2 % 128;
        int i3 = i2 % 2;
        String d = this.d.e().d();
        switch (d == null ? 'N' : 'c') {
            case Opcodes.DADD /* 99 */:
                return d;
            default:
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f("逫䤱̝倆벎ﺳ揁鿚趂蟗त\uea06㋒⊖몀㵦࠰䍦᠘ᾫ纳ꦤꗻ臀ड傁㎕䀵", ((byte) KeyEvent.getModifierMetaStateMask()) + 28, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("᱓\uf23aꗮ逝墀獖幩붒᜴錶ड傁蕉킞䩴汧\udb23骟蕉킞雝\ueef2ꎃၜೂؗ᱅㋳氥ⅼ욈ᒦ䰵⳯\uaa5a\uedda䜘빵᠘ᾫࡋ뙴揁鿚趂蟗त\uea06㋒⊖몀㵦࠰䍦᠘ᾫ\ue2cc긮蕉킞雝\ueef2ꎃၜ譑␀鲂\ue6e9䰵⳯\ue350뷇㩷勮鉇唎蕉킞\uf42f\uf271ೂؗ᱅㋳\ud9cf\uaaf8맔➦幩붒驂辙炏༠ត笛샒鵀譑␀㩉鞨ត笛弶ꁜ\ue2cc긮蕉킞雝\ueef2ꎃၜ", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 114, objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\ue5e2稺郹返墀獖幩붒᜴錶ड傁蕉킞䩴汧ڃꥇ", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 17, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                int i4 = j + Opcodes.DDIV;
                i = i4 % 128;
                switch (i4 % 2 == 0) {
                }
                return intern2;
        }
    }

    public final SecureCardPushToIssuerNfcWallet d() {
        SecureCardPushToIssuerNfcWallet secureCardPushToIssuerNfcWallet = new SecureCardPushToIssuerNfcWallet(new o.v.a(j(), this.c, b()));
        int i2 = j + 23;
        i = i2 % 128;
        int i3 = i2 % 2;
        return secureCardPushToIssuerNfcWallet;
    }

    public final void c(Context context, boolean z, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        SecureCardPushToIssuerNfcWallet d = d();
        d.requireTermsAndConditionsApproval(z);
        d.launch(context, new CustomCustomerAuthenticatedProcessCallback() { // from class: o.er.m.3
            private static int c = 0;
            private static int a = 1;

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = (c + 16) - 1;
                a = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsRequired(List<CustomerAuthenticationMethod> list, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i2 = (c + 54) - 1;
                a = i2 % 128;
                switch (i2 % 2 == 0 ? Typography.greater : (char) 1) {
                    case 1:
                        return;
                    default:
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsInvalid(LocalAuthenticationErrorReason localAuthenticationErrorReason, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerCredentialsInvalid).d());
                int i2 = (c + 90) - 1;
                a = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = c + 93;
                a = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onSuccess(null);
                int i4 = (c + 108) - 1;
                a = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        int i5 = 74 / 0;
                        return;
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = a;
                int i3 = (i2 ^ Opcodes.LMUL) + ((i2 & Opcodes.LMUL) << 1);
                c = i3 % 128;
                int i4 = i3 % 2;
                operationCallback.onError(antelopError);
                int i5 = a;
                int i6 = (i5 ^ 71) + ((i5 & 71) << 1);
                c = i6 % 128;
                switch (i6 % 2 == 0) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i2 = (c + Opcodes.FMUL) - 1;
                a = i2 % 128;
                int i3 = i2 % 2;
            }
        });
        int i2 = i + 23;
        j = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 4 : 'T') {
            case 4:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 580
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.m.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
