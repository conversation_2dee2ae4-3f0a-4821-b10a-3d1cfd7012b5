package com.google.android.gms.tapandpay.issuer;

import android.os.IBinder;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.concurrent.Executor;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\PushTokenizeRequest.smali */
public class PushTokenizeRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<PushTokenizeRequest> CREATOR = new zzi();
    final int zza;
    final int zzb;
    final byte[] zzc;
    final String zzd;
    final String zze;
    final UserAddress zzf;
    final boolean zzg;
    final int[] zzh;
    final IBinder zzi;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\PushTokenizeRequest$Builder.smali */
    public static class Builder {
        private int zza;
        private int zzb;
        private byte[] zzc;
        private String zzd;
        private String zze;
        private UserAddress zzf;
        private boolean zzg;
        private Executor zzh;
        private WalletAvailabilityChecker zzi;
        private PaymentCredentialsGenerator zzj;

        public PushTokenizeRequest build() {
            PushTokenizeCallbacks tryCreate = PushTokenizeCallbacks.tryCreate(this.zzh, this.zzi, this.zzj);
            int i = this.zza;
            int i2 = this.zzb;
            byte[] bArr = this.zzc;
            String str = this.zzd;
            String str2 = this.zze;
            UserAddress userAddress = this.zzf;
            boolean z = this.zzg;
            int[] supportedCallbackRequestTypes = tryCreate == null ? new int[0] : tryCreate.getSupportedCallbackRequestTypes();
            if (tryCreate == null) {
                tryCreate = null;
            }
            return new PushTokenizeRequest(i, i2, bArr, str, str2, userAddress, z, supportedCallbackRequestTypes, tryCreate);
        }

        public Builder setCallbackRequestExecutor(Executor executor) {
            this.zzh = executor;
            return this;
        }

        public Builder setDisplayName(String str) {
            this.zze = str;
            return this;
        }

        public Builder setIsTransit(boolean z) {
            this.zzg = z;
            return this;
        }

        public Builder setLastDigits(String str) {
            this.zzd = str;
            return this;
        }

        public Builder setNetwork(int i) {
            this.zza = i;
            return this;
        }

        public Builder setOpaquePaymentCard(byte[] bArr) {
            this.zzc = bArr;
            return this;
        }

        public Builder setPaymentCredentialsGenerator(PaymentCredentialsGenerator paymentCredentialsGenerator) {
            this.zzj = paymentCredentialsGenerator;
            return this;
        }

        public Builder setTokenServiceProvider(int i) {
            this.zzb = i;
            return this;
        }

        public Builder setUserAddress(UserAddress userAddress) {
            this.zzf = userAddress;
            return this;
        }

        public Builder setWalletAvailabilityChecker(WalletAvailabilityChecker walletAvailabilityChecker) {
            this.zzi = walletAvailabilityChecker;
            return this;
        }
    }

    PushTokenizeRequest(int i, int i2, byte[] bArr, String str, String str2, UserAddress userAddress, boolean z, int[] iArr, IBinder iBinder) {
        this.zza = i;
        this.zzb = i2;
        this.zzc = bArr;
        this.zzd = str;
        this.zze = str2;
        this.zzf = userAddress;
        this.zzg = z;
        this.zzh = iArr;
        this.zzi = iBinder;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 2, this.zza);
        SafeParcelWriter.writeInt(dest, 3, this.zzb);
        SafeParcelWriter.writeByteArray(dest, 4, this.zzc, false);
        SafeParcelWriter.writeString(dest, 5, this.zzd, false);
        SafeParcelWriter.writeString(dest, 6, this.zze, false);
        SafeParcelWriter.writeParcelable(dest, 7, this.zzf, flags, false);
        SafeParcelWriter.writeBoolean(dest, 8, this.zzg);
        SafeParcelWriter.writeIntArray(dest, 9, this.zzh, false);
        SafeParcelWriter.writeIBinder(dest, 10, this.zzi, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
