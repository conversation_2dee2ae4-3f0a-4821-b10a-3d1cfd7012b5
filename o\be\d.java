package o.be;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.EligibilityDenialReason;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.i;
import o.bb.e;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\d.smali */
public final class d extends o.y.b<c> {
    private static char f;
    private static char g;
    private static char h;
    private static char j;
    private static int l;
    boolean a;
    boolean b;
    String c;
    List<o.ei.a> d;
    b e;
    boolean i;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int m = 1;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\d$c.smali */
    public interface c {
        void a(boolean z, List<o.ei.a> list);

        void d(o.bb.d dVar);

        void e(EligibilityDenialReason eligibilityDenialReason, String str);
    }

    static {
        l = 0;
        m();
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getScrollDefaultDelay();
        int i = m + 77;
        l = i % 128;
        switch (i % 2 != 0 ? 'W' : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void m() {
        g = (char) 15236;
        f = (char) 51867;
        h = (char) 17961;
        j = (char) 40982;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = m + 13;
        l = i % 128;
        switch (i % 2 != 0) {
            case false:
                return n();
            default:
                n();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public d(Context context, o.ei.c cVar, c cVar2) {
        super(context, cVar2, cVar, e.c);
        this.a = false;
        this.i = false;
    }

    public final void a(boolean z) {
        this.b = z;
        g.c();
        Object[] objArr = new Object[1];
        k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", 24 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("罄鸺졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 18, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.i = true;
        o.bn.e.b().a(e(), false, false);
        Object obj = null;
        if (o.bt.b.c(e()).isEmpty()) {
            switch (o.bk.c.b(e())) {
                case false:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", (ViewConfiguration.getPressedStateDuration() >> 16) + 23, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    k("㵂惉捽䛢\uf881荢⌬☆ᔎꟆ\udea3ꅠ붳淮姒ᯃ㹒쌭䢭ၫ扤牎숓솥視궉\uf157\u05fb箝聡ෲ\udece⺊◤ᇗ뵣", 36 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
                    g.d(intern2, ((String) objArr4[0]).intern());
                    j().e(EligibilityDenialReason.CpuArchitectureNotSupported, null);
                    int i = m + Opcodes.DREM;
                    l = i % 128;
                    int i2 = i % 2;
                    return;
                default:
                    c();
                    int i3 = m + 77;
                    l = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case true:
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }
        g.c();
        Object[] objArr5 = new Object[1];
        k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", Color.green(0) + 23, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        k("꣰䎗므뻌\uf6a4⹍巭ࠚ䔬ȡ\uea14볂\uecf7闲갎슌䒠\ue8e8栬\ue2d9\udc03戁圪鑾\uea14볂\uecf7闲醏귺⏎༓䨎ꄿ条指잗⫸谢㷦龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫", 49 - TextUtils.lastIndexOf("", '0'), objArr6);
        g.d(intern3, ((String) objArr6[0]).intern());
        o.bt.b.a();
        new o.dd.e(e()).a();
        o.bb.d d = d();
        o.bb.a aVar = o.bb.a.m;
        Object[] objArr7 = new Object[1];
        k("洞⹅髦쌣솦槔蟚缛\ueee9䭺权ꥃ⏎༓扤牎\uebc5㍹⌂银췉\udec2扤牎\uebc5㍹\ue1cd剬", 27 - MotionEvent.axisFromString(""), objArr7);
        d.c(aVar, ((String) objArr7[0]).intern());
        j().d(d());
        int i4 = l + Opcodes.DSUB;
        m = i4 % 128;
        if (i4 % 2 != 0) {
        } else {
            throw null;
        }
    }

    private a n() {
        a aVar = new a(this);
        int i = l + Opcodes.LMUL;
        m = i % 128;
        switch (i % 2 == 0 ? ':' : ')') {
            case Opcodes.ASTORE /* 58 */:
                throw null;
            default:
                return aVar;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i = l + Opcodes.LSUB;
        m = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", 23 - View.resolveSizeAndState(0, 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = m + 99;
        l = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    public final boolean l() {
        boolean z;
        int i = l + 61;
        int i2 = i % 128;
        m = i2;
        switch (i % 2 != 0) {
            case true:
                z = this.i;
                break;
            default:
                z = this.i;
                int i3 = 17 / 0;
                break;
        }
        int i4 = i2 + 41;
        l = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final void o() {
        if (i() != null) {
            int i = l + 45;
            m = i % 128;
            switch (i % 2 != 0) {
                case true:
                    g.c();
                    Object[] objArr = new Object[1];
                    k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", AndroidCharacter.getMirror('0') - 25, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    k("빐ꆭ樭\ue8c6졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫幍쒯", 22 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    this.i = false;
                    break;
                default:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k("졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫㢃鈰啼﮻↶齒ਮ㕅", 'Z' - AndroidCharacter.getMirror('x'), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    k("빐ꆭ樭\ue8c6졋뿬❽\u0005ബ윿龠ꠂ\uf371囜㮹먷龠ꠂ㲱↫幍쒯", Opcodes.LMUL << (TypedValue.complexToFraction(0, 1.0f, 2.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 1.0f, 2.0f) == 0.0f ? 0 : -1)), objArr4);
                    g.d(intern2, ((String) objArr4[0]).intern());
                    this.i = true;
                    break;
            }
        }
        int i2 = l + Opcodes.LNEG;
        m = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 25 : ']') {
            case 25:
                int i3 = 41 / 0;
                return;
            default:
                return;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\d$a.smali */
    static final class a extends o.y.a<d> {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static byte[] a;
        private static int b;
        private static long c;
        private static int d;
        private static int e;
        private static int f;
        private static short[] g;
        private static int i;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            f = 1;
            c = -177906079819085043L;
            a = new byte[]{-83, -26, -41, -24, -31, -22, -63, 25, -43, -83, -48, -29, 2, -39, -1, -43, -21, -110, -45, -23, 102, -126, 43, -121, -117, 119, -115, 96, -116, 109, -105, 119, -40, 121, -117, 117, -121, 34, ByteCompanionObject.MAX_VALUE, -119, -29, 7, -90, -17, 0, -22, -22, -24, 27, -18, 79, -18, 24, 18, 28, -89, -28, 30, -37, 6, -46, 12, -82, 0, -38, -84, 55, -44, 1, 4, -46, -38, -53, -52, 58, -57, 35, -12, 35, 32, -52, -58, -55, 54, -55, 45, 37, -38, -120, 118, -37, -47, -98, 99, 33, -46, 33, -38, 38, 37, -33, -99, 117, -46, -111, 99, -38, 34, 43, -55, -7, 109, -98, 120, 113, 112, 121, 15, -36, 115, 79, 62, -37, 122, 55, -87, 114, 106, 97, 95, 79, 52, -36, 65, 103, 100, 4, -89, 103, 115, 108, 104, 114, 112, 66, 15, 100, -82, 109, 108, 66, 108, 97, 2, -92, 121, 104, 105, 119, 79, 52, -43, 115, 121, 107, 113, 117, 103, 114, 110, 107, 117, 51, -94, 101, 120, 79, 113, 107, 71, 51, 74, 49, 69, 59, 105, 15, 66, 112, 58, 72, 68, 62, 71, 59, 58, 100, 31, 77, 59, 58, 98, -17, -126, -17, -102, -18, -21, -75, -57, -109, -105, 94, -10, UtilitiesSDKConstants.SRP_LABEL_MAC, 81, -25, -19, -118, -18, -5, -87, -57, -105, -108, -115, -6, -19, 93, -23, -101, 35, Tnaf.POW_2_WIDTH, -118, -101, -15, -109, -127, -21, -103, -107, -17, -102, -18, -21, -75, -14, ByteCompanionObject.MIN_VALUE, -18, -21, -77, -112, -112, -112, -112, -112, -112, -112, -112, -112};
            d = 909053639;
            b = 509676108;
            e = 2139036494;
        }

        static void init$0() {
            $$a = new byte[]{110, -66, 47, -83};
            $$b = Opcodes.NEW;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void n(short r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.be.d.a.$$a
                int r8 = r8 * 4
                int r8 = 4 - r8
                int r6 = 110 - r6
                int r7 = r7 * 3
                int r7 = 1 - r7
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L19:
                r3 = r2
                r5 = r8
                r8 = r6
                r6 = r5
            L1d:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r9
                r9 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L35:
                int r8 = r8 + r6
                int r6 = r9 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.n(short, byte, byte, java.lang.Object[]):void");
        }

        a(d dVar) {
            super(dVar, false);
        }

        @Override // o.y.a
        public final void a() {
            int i2 = f + 71;
            i = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    o.b.c.e(f(), g());
                    return;
                default:
                    o.b.c.e(f(), g());
                    throw null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:68:0x02ea, code lost:
        
            r1 = e();
            r6 = new java.lang.Object[1];
            l((byte) (91 - android.graphics.ImageFormat.getBitsPerPixel(0)), (android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1229985189, (short) (19 - android.view.View.resolveSizeAndState(0, 0, 0)), (android.view.ViewConfiguration.getScrollBarFadeDuration() >> 16) - 72, android.view.KeyEvent.getDeadChar(0, 0) - 676201592, r6);
            r1.c = r0.r(((java.lang.String) r6[0]).intern());
            e().e = r2;
         */
        @Override // o.y.a
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void j() {
            /*
                Method dump skipped, instructions count: 1328
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.j():void");
        }

        private b a(int i2) {
            int i3 = f;
            int i4 = i3 + 95;
            i = i4 % 128;
            int i5 = i4 % 2;
            switch (i2 != 0) {
                case true:
                    b c2 = b.c(i2);
                    switch (c2 == null) {
                        case false:
                            g.c();
                            String c3 = c();
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            k("ʄˀదꔧޑ壀답篟\u2daf㝙\ue3ebਗ屧晤퍽\udaa9輨閬>\ue9e2뿍쒳熊렴\uee80\uf479ꄋ坸ᥛ⯖陓枖䠎媃쟟㛛碧詀㓣알ꬬ", View.resolveSizeAndState(0, 0, 0), objArr);
                            g.d(c3, sb.append(((String) objArr[0]).intern()).append(c2).toString());
                            return c2;
                        default:
                            int i6 = f + 79;
                            i = i6 % 128;
                            if (i6 % 2 != 0) {
                            }
                            g.c();
                            String c4 = c();
                            Object[] objArr2 = new Object[1];
                            l((byte) (113 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), (-1229985135) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) (TextUtils.getCapsMode("", 0, 0) - 111), (ViewConfiguration.getFadingEdgeLength() >> 16) - 17, View.combineMeasuredStates(0, 0) - 676201607, objArr2);
                            g.e(c4, ((String) objArr2[0]).intern());
                            return b.j;
                    }
                default:
                    int i7 = i3 + Opcodes.LSHR;
                    i = i7 % 128;
                    if (i7 % 2 != 0) {
                    }
                    g.c();
                    String c5 = c();
                    Object[] objArr3 = new Object[1];
                    l((byte) ((Process.myPid() >> 22) - 56), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 1229985164, (short) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 129), (ViewConfiguration.getTapTimeout() >> 16) - 57, (-676201624) - Gravity.getAbsoluteGravity(0, 0), objArr3);
                    g.d(c5, ((String) objArr3[0]).intern());
                    return null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:11:0x005c, code lost:
        
            if (e().d.isEmpty() != false) goto L24;
         */
        @Override // o.y.a
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void a(o.bb.d r14) {
            /*
                Method dump skipped, instructions count: 386
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.a(o.bb.d):void");
        }

        /* JADX WARN: Code restructure failed: missing block: B:6:0x0021, code lost:
        
            if (r3.d() == o.bb.a.z) goto L16;
         */
        @Override // o.y.a
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void e(o.bb.d r3) {
            /*
                r2 = this;
                int r0 = o.be.d.a.i
                int r0 = r0 + 73
                int r1 = r0 % 128
                o.be.d.a.f = r1
                int r0 = r0 % 2
                r1 = 0
                if (r0 != 0) goto Lf
                r0 = r1
                goto L10
            Lf:
                r0 = 1
            L10:
                switch(r0) {
                    case 0: goto L24;
                    default: goto L13;
                }
            L13:
                o.y.b r0 = r2.e()
                o.be.d r0 = (o.be.d) r0
                r0.i = r1
                o.bb.a r0 = r3.d()
                o.bb.a r1 = o.bb.a.z
                if (r0 != r1) goto L5a
            L23:
                goto L3d
            L24:
                o.y.b r0 = r2.e()
                o.be.d r0 = (o.be.d) r0
                r0.i = r1
                o.bb.a r0 = r3.d()
                o.bb.a r1 = o.bb.a.z
                if (r0 != r1) goto L37
                r0 = 10
                goto L39
            L37:
                r0 = 79
            L39:
                switch(r0) {
                    case 10: goto L23;
                    default: goto L3c;
                }
            L3c:
                goto L5a
            L3d:
                int r3 = o.be.d.a.f
                int r3 = r3 + 53
                int r0 = r3 % 128
                o.be.d.a.i = r0
                int r3 = r3 % 2
                o.y.b r3 = r2.e()
                o.be.d r3 = (o.be.d) r3
                java.lang.Object r3 = r3.j()
                o.be.d$c r3 = (o.be.d.c) r3
                fr.antelop.sdk.EligibilityDenialReason r0 = fr.antelop.sdk.EligibilityDenialReason.OsNotSupported
                r1 = 0
                r3.e(r0, r1)
                return
            L5a:
                o.y.b r0 = r2.e()
                o.be.d r0 = (o.be.d) r0
                java.lang.Object r0 = r0.j()
                o.be.d$c r0 = (o.be.d.c) r0
                r0.d(r3)
                int r3 = o.be.d.a.i
                int r3 = r3 + 51
                int r0 = r3 % 128
                o.be.d.a.f = r0
                int r3 = r3 % 2
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.e(o.bb.d):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 368
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.k(java.lang.String, int, java.lang.Object[]):void");
        }

        /* JADX WARN: Removed duplicated region for block: B:129:0x02c3 A[PHI: r3
  0x02c3: PHI (r3v7 int) = (r3v6 int), (r3v37 int) binds: [B:134:0x02bd, B:91:0x02a3] A[DONT_GENERATE, DONT_INLINE]] */
        /* JADX WARN: Removed duplicated region for block: B:92:0x02c1 A[PHI: r3
  0x02c1: PHI (r3v34 int) = (r3v6 int), (r3v37 int) binds: [B:134:0x02bd, B:91:0x02a3] A[DONT_GENERATE, DONT_INLINE]] */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r16, int r17, short r18, int r19, int r20, java.lang.Object[] r21) {
            /*
                Method dump skipped, instructions count: 1050
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.be.d.a.l(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    private static void k(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2;
        int i3 = 2;
        int i4 = 0;
        switch (str != null ? (char) 6 : (char) 21) {
            case 21:
                cArr = str;
                break;
            default:
                int i5 = $11 + 71;
                $10 = i5 % 128;
                switch (i5 % 2 != 0) {
                    case false:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str.toCharArray();
                        int i6 = 88 / 0;
                        break;
                }
        }
        char[] cArr2 = cArr;
        i iVar = new i();
        char[] cArr3 = new char[cArr2.length];
        iVar.b = 0;
        char[] cArr4 = new char[2];
        while (iVar.b < cArr2.length) {
            cArr4[i4] = cArr2[iVar.b];
            cArr4[1] = cArr2[iVar.b + 1];
            int i7 = 58224;
            int i8 = i4;
            while (i8 < 16) {
                int i9 = $11 + 97;
                $10 = i9 % 128;
                int i10 = i9 % i3;
                char c2 = cArr4[1];
                char c3 = cArr4[i4];
                int i11 = (c3 + i7) ^ ((c3 << 4) + ((char) (f ^ 8439748517800462901L)));
                int i12 = c3 >>> 5;
                try {
                    Object[] objArr2 = new Object[4];
                    objArr2[3] = Integer.valueOf(h);
                    objArr2[i3] = Integer.valueOf(i12);
                    objArr2[1] = Integer.valueOf(i11);
                    objArr2[i4] = Integer.valueOf(c2);
                    Object obj = o.e.a.s.get(-1512468642);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(12 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), 603 - (ViewConfiguration.getTouchSlop() >> 8));
                        Class<?>[] clsArr = new Class[4];
                        clsArr[i4] = Integer.TYPE;
                        clsArr[1] = Integer.TYPE;
                        clsArr[i3] = Integer.TYPE;
                        clsArr[3] = Integer.TYPE;
                        obj = cls.getMethod("C", clsArr);
                        o.e.a.s.put(-1512468642, obj);
                    }
                    char charValue = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    cArr4[1] = charValue;
                    i iVar2 = iVar;
                    try {
                        Object[] objArr3 = {Integer.valueOf(cArr4[i4]), Integer.valueOf((charValue + i7) ^ ((charValue << 4) + ((char) (j ^ 8439748517800462901L)))), Integer.valueOf(charValue >>> 5), Integer.valueOf(g)};
                        Object obj2 = o.e.a.s.get(-1512468642);
                        if (obj2 == null) {
                            obj2 = ((Class) o.e.a.c(Color.alpha(0) + 11, (char) TextUtils.indexOf("", "", 0, 0), MotionEvent.axisFromString("") + 604)).getMethod("C", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1512468642, obj2);
                        }
                        cArr4[0] = ((Character) ((Method) obj2).invoke(null, objArr3)).charValue();
                        i7 -= 40503;
                        i8++;
                        iVar = iVar2;
                        i3 = 2;
                        i4 = 0;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            i iVar3 = iVar;
            cArr3[iVar3.b] = cArr4[0];
            cArr3[iVar3.b + 1] = cArr4[1];
            try {
                Object[] objArr4 = {iVar3, iVar3};
                Object obj3 = o.e.a.s.get(2062727845);
                if (obj3 != null) {
                    i2 = 2;
                } else {
                    i2 = 2;
                    obj3 = ((Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 9, (char) (30725 - (Process.myTid() >> 22)), Color.argb(0, 0, 0, 0) + 614)).getMethod("A", Object.class, Object.class);
                    o.e.a.s.put(2062727845, obj3);
                }
                ((Method) obj3).invoke(null, objArr4);
                iVar = iVar3;
                i3 = i2;
                i4 = 0;
            } catch (Throwable th3) {
                Throwable cause3 = th3.getCause();
                if (cause3 == null) {
                    throw th3;
                }
                throw cause3;
            }
        }
        objArr[0] = new String(cArr3, 0, i);
    }
}
