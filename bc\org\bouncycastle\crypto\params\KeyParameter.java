package bc.org.bouncycastle.crypto.params;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\params\KeyParameter.smali */
public class KeyParameter implements CipherParameters {
    private byte[] a;

    public KeyParameter(byte[] bArr) {
        this(bArr, 0, bArr.length);
    }

    public void copyTo(byte[] bArr, int i, int i2) {
        byte[] bArr2 = this.a;
        if (bArr2.length != i2) {
            throw new IllegalArgumentException("len");
        }
        System.arraycopy(bArr2, 0, bArr, i, i2);
    }

    public byte[] getKey() {
        return this.a;
    }

    public int getKeyLength() {
        return this.a.length;
    }

    public KeyParameter reverse() {
        KeyParameter keyParameter = new KeyParameter(this.a.length);
        Arrays.reverse(this.a, keyParameter.a);
        return keyParameter;
    }

    public KeyParameter(byte[] bArr, int i, int i2) {
        this(i2);
        System.arraycopy(bArr, i, this.a, 0, i2);
    }

    private KeyParameter(int i) {
        this.a = new byte[i];
    }
}
