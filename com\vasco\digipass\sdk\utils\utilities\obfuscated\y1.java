package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y1.smali */
public class y1 implements h, b5 {
    private g0 b;

    public y1(g0 g0Var) {
        this.b = g0Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return a(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0("unable to get DER object", e);
        } catch (IllegalArgumentException e2) {
            throw new a0("unable to get DER object", e2);
        }
    }

    static a3 a(g0 g0Var) throws IOException {
        try {
            return new a3(new d3(g0Var.b()));
        } catch (IllegalArgumentException e) {
            throw new k(e.getMessage(), e);
        }
    }
}
