package fr.antelop.sdk.digitalcard;

import android.app.Activity;
import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCardPushToSamsungPay.smali */
public final class SecureCardPushToSamsungPay implements CustomerAuthenticatedProcess {
    private final f innerSecureDigitalCardPushToSamsungPayProcess;

    public SecureCardPushToSamsungPay(f fVar) {
        this.innerSecureDigitalCardPushToSamsungPayProcess = fVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardPushToSamsungPayProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardPushToSamsungPayProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardPushToSamsungPayProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardPushToSamsungPayProcess.k();
    }

    public final void launch(Activity activity, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardPushToSamsungPayProcess.e(activity, new i(activity, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToSamsungPayProcess));
    }

    public final void launch(Activity activity, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardPushToSamsungPayProcess.e(activity, new e(activity, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToSamsungPayProcess));
    }

    public final String getMessage() {
        return null;
    }
}
