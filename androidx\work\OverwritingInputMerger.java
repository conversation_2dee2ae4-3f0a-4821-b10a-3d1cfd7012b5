package androidx.work;

import androidx.work.Data;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\OverwritingInputMerger.smali */
public final class OverwritingInputMerger extends InputMerger {
    @Override // androidx.work.InputMerger
    public Data merge(List<Data> inputs) {
        Data.Builder output = new Data.Builder();
        Map<String, Object> mergedValues = new HashMap<>();
        for (Data input : inputs) {
            mergedValues.putAll(input.getKeyValueMap());
        }
        output.putAll(mergedValues);
        return output.build();
    }
}
