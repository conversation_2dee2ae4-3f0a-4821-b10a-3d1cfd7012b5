package o.bn;

import android.content.Context;
import android.os.Process;
import android.os.SystemClock;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;
import o.bo.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\d.smali */
public abstract class d {
    private static char a;
    private static char b;
    private static char d;
    private static char e;
    private static int g;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int c = 0;

    static {
        g = 1;
        b();
        SystemClock.currentThreadTimeMillis();
        int i = c + Opcodes.LNEG;
        g = i % 128;
        switch (i % 2 == 0) {
            case false:
                break;
            default:
                int i2 = 27 / 0;
                break;
        }
    }

    static void b() {
        a = (char) 10682;
        d = (char) 43320;
        b = (char) 607;
        e = (char) 30607;
    }

    abstract int a();

    public abstract String b(Context context) throws c, g;

    public abstract String c(Context context, boolean z) throws c;

    public abstract String e(Context context) throws c;

    private static String c(Context context) {
        StringBuilder append = new StringBuilder().append(context.getPackageName());
        o.ee.e.a();
        String obj = append.append(o.ee.c.j(context)).toString();
        o.ee.g.c();
        Object[] objArr = new Object[1];
        h("튋\ued7bܼ⨪⣨ē뇱뗻ꅌ蜴蟕\u0cf6\uf23a\uf89b蝻ⷡ", ExpandableListView.getPackedPositionType(0L) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        h("⪤ᬁ性崔⌉깒ⷒ櫜ꅌ蜴쯾㹰\ue4aa\ue486▉㥘৾쪏䄄禘龜墘䃯창ꇓꍞ坜\u0cd2ʧ丹", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 29, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(obj).toString());
        String c2 = o.c(obj);
        int i = c + Opcodes.LSHL;
        g = i % 128;
        switch (i % 2 == 0 ? 'H' : '\n') {
            case 'H':
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return c2;
        }
    }

    protected static String a(Context context) throws g {
        new o.bo.d();
        String e2 = o.bo.d.e(context);
        o.ee.g.c();
        Object[] objArr = new Object[1];
        h("튋\ued7bܼ⨪⣨ē뇱뗻ꅌ蜴蟕\u0cf6\uf23a\uf89b蝻ⷡ", (Process.myPid() >> 22) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        h("쯾㹰鉂蒱彚鞝䔼㳳땒ଣ\ue7c9뗤쇱\uf058ᦽ\u1ae8ꂝ䳈㑢ᱯ䵎뵰⌅㎒ﺟ삘ᠾὣꅌ蜴쯾㹰\ue4aa\ue486▉㥘৾쪏䄄禘龜墘䃯창ꇓꍞ坜\u0cd2ʧ丹", View.combineMeasuredStates(0, 0) + 49, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(e2).toString());
        switch (e2 == null) {
            case false:
                return o.c(e2);
            default:
                int i = c;
                int i2 = i + 41;
                g = i2 % 128;
                Object obj = null;
                if (i2 % 2 == 0) {
                    obj.hashCode();
                    throw null;
                }
                int i3 = i + 49;
                g = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        throw null;
                    default:
                        return null;
                }
        }
    }

    public static String d(Context context) throws c {
        String intern;
        Object obj;
        int i = c + Opcodes.LSHR;
        g = i % 128;
        switch (i % 2 == 0 ? (char) 7 : '\t') {
            case 7:
                o.ee.g.c();
                Object[] objArr = new Object[1];
                h("튋\ued7bܼ⨪⣨ē뇱뗻ꅌ蜴蟕\u0cf6\uf23a\uf89b蝻ⷡ", 79 << (KeyEvent.getMaxKeyCode() << 20), objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                h("쯾㹰ජ\uf528㩻뷡倅馫넯蟣", (ViewConfiguration.getEdgeSlop() % 70) + 42, objArr2);
                obj = objArr2[0];
                break;
            default:
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                h("튋\ued7bܼ⨪⣨ē뇱뗻ꅌ蜴蟕\u0cf6\uf23a\uf89b蝻ⷡ", 16 - (KeyEvent.getMaxKeyCode() >> 16), objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                h("쯾㹰ජ\uf528㩻뷡倅馫넯蟣", 10 - (ViewConfiguration.getEdgeSlop() >> 16), objArr4);
                obj = objArr4[0];
                break;
        }
        o.ee.g.d(intern, ((String) obj).intern());
        return c(context);
    }

    public final boolean equals(Object obj) {
        switch (this != obj) {
            case true:
                switch (obj != null ? 'V' : '+') {
                    case Opcodes.SASTORE /* 86 */:
                        int i = g + 5;
                        c = i % 128;
                        int i2 = i % 2;
                        return getClass() == obj.getClass() && a() == ((a) obj).a();
                    default:
                        return false;
                }
            default:
                int i3 = c + 67;
                g = i3 % 128;
                int i4 = i3 % 2;
                return true;
        }
    }

    public final int hashCode() {
        int i = c + 109;
        g = i % 128;
        int i2 = i % 2;
        int hash = Objects.hash(Integer.valueOf(a()));
        int i3 = c + 53;
        g = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return hash;
            default:
                int i4 = 23 / 0;
                return hash;
        }
    }

    public final String toString() {
        int i = c + 51;
        g = i % 128;
        switch (i % 2 == 0 ? 'Z' : 'C') {
            case 'C':
                return super.toString();
            default:
                int i2 = 66 / 0;
                return super.toString();
        }
    }

    protected final void finalize() throws Throwable {
        int i = c + Opcodes.LUSHR;
        g = i % 128;
        int i2 = i % 2;
        super.finalize();
        int i3 = c + Opcodes.LNEG;
        g = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 560
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.d.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
