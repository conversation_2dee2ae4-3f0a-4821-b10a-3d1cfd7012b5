package com.vasco.digipass.sdk.responses;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\MultiDeviceLicenseActivationResponse.smali */
public class MultiDeviceLicenseActivationResponse extends ActivationResponse {
    private String g;

    public MultiDeviceLicenseActivationResponse(int i) {
        super(i);
    }

    public String getDeviceCode() {
        return this.g;
    }

    public MultiDeviceLicenseActivationResponse(int i, Throwable th) {
        super(i, th);
    }

    public MultiDeviceLicenseActivationResponse(int i, int i2, byte[] bArr, byte[] bArr2, String str) {
        super(i, i2, bArr2, 0);
        super.setStaticVector(bArr);
        this.g = str;
    }
}
