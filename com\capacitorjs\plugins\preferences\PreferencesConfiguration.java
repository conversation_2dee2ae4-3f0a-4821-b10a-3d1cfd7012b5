package com.capacitorjs.plugins.preferences;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes13\com\capacitorjs\plugins\preferences\PreferencesConfiguration.smali */
public class PreferencesConfiguration implements Cloneable {
    static final PreferencesConfiguration DEFAULTS;
    String group;

    static {
        PreferencesConfiguration preferencesConfiguration = new PreferencesConfiguration();
        DEFAULTS = preferencesConfiguration;
        preferencesConfiguration.group = "CapacitorStorage";
    }

    /* renamed from: clone, reason: merged with bridge method [inline-methods] */
    public PreferencesConfiguration m92clone() throws CloneNotSupportedException {
        return (PreferencesConfiguration) super.clone();
    }
}
