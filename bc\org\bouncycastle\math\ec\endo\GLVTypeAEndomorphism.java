package bc.org.bouncycastle.math.ec.endo;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPointMap;
import bc.org.bouncycastle.math.ec.ScaleYNegateXPointMap;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\GLVTypeAEndomorphism.smali */
public class GLVTypeAEndomorphism implements GLVEndomorphism {
    protected final GLVTypeAParameters a;
    protected final ECPointMap b;

    public GLVTypeAEndomorphism(ECCurve eCCurve, GLVTypeAParameters gLVTypeAParameters) {
        this.a = gLVTypeAParameters;
        this.b = new ScaleYNegateXPointMap(eCCurve.fromBigInteger(gLVTypeAParameters.getI()));
    }

    @Override // bc.org.bouncycastle.math.ec.endo.GLVEndomorphism
    public BigInteger[] decomposeScalar(BigInteger bigInteger) {
        return EndoUtil.decomposeScalar(this.a.getSplitParams(), bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.endo.ECEndomorphism
    public ECPointMap getPointMap() {
        return this.b;
    }

    @Override // bc.org.bouncycastle.math.ec.endo.ECEndomorphism
    public boolean hasEfficientPointMap() {
        return true;
    }
}
