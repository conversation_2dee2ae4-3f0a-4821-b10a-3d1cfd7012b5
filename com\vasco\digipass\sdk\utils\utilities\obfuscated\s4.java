package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s4.smali */
public abstract class s4 {
    static final r4 a = new o6(BigInteger.valueOf(2));
    static final r4 b = new o6(BigInteger.valueOf(3));

    public static n6 a(int[] iArr) {
        if (iArr[0] != 0) {
            throw new IllegalArgumentException("Irreducible polynomials in GF(2) must have constant term");
        }
        for (int i = 1; i < iArr.length; i++) {
            if (iArr[i] <= iArr[i - 1]) {
                throw new IllegalArgumentException("Polynomial exponents must be monotonically increasing");
            }
        }
        return new y4(a, new t4(iArr));
    }

    public static r4 a(BigInteger bigInteger) {
        int bitLength = bigInteger.bitLength();
        if (bigInteger.signum() > 0 && bitLength >= 2) {
            if (bitLength < 3) {
                int b2 = f1.b(bigInteger);
                if (b2 == 2) {
                    return a;
                }
                if (b2 == 3) {
                    return b;
                }
            }
            return new o6(bigInteger);
        }
        throw new IllegalArgumentException("'characteristic' must be >= 2");
    }
}
