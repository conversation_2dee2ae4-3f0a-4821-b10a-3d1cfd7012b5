package com.vasco.digipass.sdk.utils.utilities;

import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u7;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelGenerateResponse;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelParseResponse;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessage;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessageGenerator;
import com.vasco.digipass.sdk.utils.utilities.sc.UtilitiesSDKSecureChannelMessageParser;
import com.vasco.digipass.sdk.utils.utilities.wbc.UtilitiesSDKWhiteBoxCryptography;
import com.vasco.digipass.sdk.utils.utilities.wbc.WBCTable;
import java.nio.charset.StandardCharsets;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\UtilitiesSDK.smali */
public final class UtilitiesSDK {
    public static final String VERSION = "4.30.0";
    private static final byte[] a = {37, -107, 7, 103, -67, 2, 2, -126};
    private static final byte[] b = {29, -5, -57, 18, -122, -88, 19, -88};
    private static final byte[] c = {43, 44, -76, 4, -94, 125, 23, 28};
    private static final byte[] d = {17, 72, 123, 99, -127, 7, 100, 118};
    private static final byte[] e = {45, -111, 107, -120, -61, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -5, -36};
    private static final byte[] f = {1, -110, -76, -72, 85, 87, 53, -126};

    private UtilitiesSDK() {
    }

    private static int a(byte b2, byte b3, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        if (bArr == null) {
            return UtilitiesSDKReturnCodes.KEY_NULL;
        }
        if (bArr3 == null) {
            return UtilitiesSDKReturnCodes.INPUT_DATA_NULL;
        }
        if (b2 != 1 && b2 != 2 && b2 != 3) {
            return UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID;
        }
        if (b3 != 1 && b3 != 2 && b3 != 3 && b3 != 4) {
            return UtilitiesSDKReturnCodes.CRYPTO_MODE_INVALID;
        }
        if (b2 == 1) {
            Integer b4 = b(b3, bArr, bArr2, bArr3);
            if (b4 != null) {
                return b4.intValue();
            }
        } else if (b2 != 2) {
            Integer a2 = a(b3, bArr, bArr2, bArr3);
            if (a2 != null) {
                return a2.intValue();
            }
        } else {
            Integer c2 = c(b3, bArr, bArr2, bArr3);
            if (c2 != null) {
                return c2.intValue();
            }
        }
        if (b3 == 4 && bArr2 == null) {
            return UtilitiesSDKReturnCodes.INITIAL_VECTOR_NULL;
        }
        return 0;
    }

    private static Integer b(byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        if (bArr.length != 8) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH);
        }
        if (b2 != 3 && b2 != 4 && bArr3.length % 8 != 0) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH);
        }
        if ((b2 != 2 && b2 != 4 && b2 != 3) || bArr2 == null || bArr2.length == 8) {
            return null;
        }
        return Integer.valueOf(UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH);
    }

    private static Integer c(byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        if (bArr.length != 16 && bArr.length != 24) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH);
        }
        if (b2 != 3 && b2 != 4 && bArr3.length % 8 != 0) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH);
        }
        if ((b2 != 2 && b2 != 4 && b2 != 3) || bArr2 == null || bArr2.length == 8) {
            return null;
        }
        return Integer.valueOf(UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH);
    }

    public static UtilitiesSDKCryptoResponse decrypt(byte b2, byte b3, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int a2 = a(b2, b3, bArr, bArr2, bArr3);
        if (a2 != 0) {
            return new UtilitiesSDKCryptoResponse(a2);
        }
        try {
            return new UtilitiesSDKCryptoResponse(a2, a(false, b2, b3, bArr, bArr2, bArr3));
        } catch (UtilitiesSDKException e2) {
            return new UtilitiesSDKCryptoResponse(e2.getReturnErrorCode());
        }
    }

    public static UtilitiesSDKCryptoResponse decryptAESCTR(WBCTable wBCTable, byte[] bArr, byte[] bArr2) {
        return UtilitiesSDKWhiteBoxCryptography.decryptAESCTR(wBCTable, bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse decryptAESCTRWithMasterKey(byte[] bArr, byte[] bArr2) {
        return UtilitiesSDKWhiteBoxCryptography.decryptAESCTRWithMasterKey(bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse deriveKey(byte b2, byte[] bArr, byte[] bArr2, int i, int i2) {
        return b2 != 3 ? new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID) : bArr == null ? new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.INPUT_KEY_NULL) : bArr2 == null ? new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.SALT_NULL) : i <= 0 ? new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.ITERATION_COUNT_INCORRECT) : i2 == 0 ? new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.OUTPUT_DATA_INCORRECT_LENGTH) : new UtilitiesSDKCryptoResponse(0, new PBKDF2_HMAC_SHA256().generateDerivedKey(bArr, bArr2, i, i2));
    }

    public static UtilitiesSDKCryptoResponse encrypt(byte b2, byte b3, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int a2 = a(b2, b3, bArr, bArr2, bArr3);
        if (a2 != 0) {
            return new UtilitiesSDKCryptoResponse(a2);
        }
        try {
            return new UtilitiesSDKCryptoResponse(a2, a(true, b2, b3, bArr, bArr2, bArr3));
        } catch (UtilitiesSDKException e2) {
            return new UtilitiesSDKCryptoResponse(e2.getReturnErrorCode());
        }
    }

    public static UtilitiesSDKCryptoResponse encryptAESBlock(WBCTable wBCTable, byte[] bArr) {
        return UtilitiesSDKWhiteBoxCryptography.encryptAESBlock(wBCTable, bArr);
    }

    public static UtilitiesSDKCryptoResponse encryptAESCTR(WBCTable wBCTable, byte[] bArr, byte[] bArr2) {
        return UtilitiesSDKWhiteBoxCryptography.encryptAESCTR(wBCTable, bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse encryptAESCTRWithMasterKey(byte[] bArr, byte[] bArr2) {
        return UtilitiesSDKWhiteBoxCryptography.encryptAESCTRWithMasterKey(bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse generateRandomByteArray(int i) {
        return UtilitiesSDKCrypto.b(i);
    }

    public static UtilitiesSDKSecureChannelGenerateResponse generateSecureChannelMessage(UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) {
        return UtilitiesSDKSecureChannelMessageGenerator.generateSecureChannelMessage(utilitiesSDKSecureChannelMessage);
    }

    public static UtilitiesSDKCryptoResponse hash(byte b2, byte[] bArr) {
        return UtilitiesSDKCrypto.hash(b2, bArr);
    }

    public static UtilitiesSDKCryptoResponse hmac(byte b2, byte[] bArr, byte[] bArr2) {
        return UtilitiesSDKCrypto.hmac(b2, bArr, bArr2);
    }

    public static boolean isPasswordWeak(CharSequence charSequence) {
        if (charSequence != null && charSequence.length() >= 4) {
            for (int i = 0; i < charSequence.length(); i++) {
                if (!Character.isLetterOrDigit(charSequence.charAt(i))) {
                    return true;
                }
            }
            if (((charSequence.charAt(0) == '0') ^ (charSequence.charAt(charSequence.length() - 1) == '0')) && a(charSequence) == charSequence.length() - 1) {
                return true;
            }
            int length = charSequence.length() - 1;
            int[] iArr = new int[length];
            int i2 = 0;
            while (i2 < length) {
                int i3 = i2 + 1;
                iArr[i2] = charSequence.charAt(i2) - charSequence.charAt(i3);
                i2 = i3;
            }
            int i4 = 0;
            while (i4 < length - 1) {
                int i5 = iArr[i4];
                i4++;
                if (i5 != iArr[i4]) {
                    return false;
                }
            }
        }
        return true;
    }

    public static UtilitiesSDKSecureChannelParseResponse parseSecureChannelMessage(String str) {
        return UtilitiesSDKSecureChannelMessageParser.parseSecureChannelMessage(str);
    }

    @Deprecated
    public static String xsVUhL4q6C(String str) {
        byte[] bArr = new byte[32];
        byte[] bArr2 = a;
        System.arraycopy(bArr2, 0, bArr, 0, bArr2.length);
        byte[] bArr3 = b;
        System.arraycopy(bArr3, 0, bArr, 8, bArr3.length);
        byte[] bArr4 = c;
        System.arraycopy(bArr4, 0, bArr, 16, bArr4.length);
        byte[] bArr5 = d;
        System.arraycopy(bArr5, 0, bArr, 24, bArr5.length);
        byte[] bArr6 = new byte[16];
        byte[] bArr7 = e;
        System.arraycopy(bArr7, 0, bArr6, 0, bArr7.length);
        byte[] bArr8 = f;
        System.arraycopy(bArr8, 0, bArr6, 8, bArr8.length);
        UtilitiesSDKCryptoResponse decrypt = decrypt((byte) 3, (byte) 4, bArr, bArr6, u7.a(str));
        if (decrypt.getReturnCode() != 0) {
            return null;
        }
        return new String(decrypt.getOutputData(), StandardCharsets.UTF_8);
    }

    private static Integer a(byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        if (bArr.length != 16 && bArr.length != 24 && bArr.length != 32) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH);
        }
        if (b2 != 3 && b2 != 4 && bArr3.length % 16 != 0) {
            return Integer.valueOf(UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH);
        }
        if ((b2 != 2 && b2 != 4 && b2 != 3) || bArr2 == null || bArr2.length == 16) {
            return null;
        }
        return Integer.valueOf(UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH);
    }

    private static byte[] a(boolean z, byte b2, byte b3, byte[] bArr, byte[] bArr2, byte[] bArr3) throws UtilitiesSDKException {
        return UtilitiesSDKCrypto.a(z, b2, b3, bArr, bArr2, bArr3);
    }

    private static int a(CharSequence charSequence) {
        int i = 0;
        for (int i2 = 0; i2 < charSequence.length(); i2++) {
            if (charSequence.charAt(i2) == '0') {
                i++;
            }
        }
        return i;
    }
}
