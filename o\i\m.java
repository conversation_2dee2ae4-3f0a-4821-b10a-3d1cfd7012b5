package o.i;

import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.ForeignCurrencySupport;
import org.bouncycastle.math.ec.Tnaf;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\m.smali */
public final class m {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static m a;
    private static m b;
    private static final /* synthetic */ m[] c;
    private static m e;
    private static int f;
    private static boolean g;
    private static boolean h;
    private static char[] i;
    private static int j;
    private static int m;
    private final String d;

    static void c() {
        i = new char[]{61940, 61727, 61715, 61710, 61721, 61899, 61928, 61726, 61717, 61704, 61730, 61720, 61723, 61716, 61712, 61724, 61921, 61933, 61705, 61714, 61711, 61941, 61943, 61725, 61719, 61949, 61947, 61930};
        g = true;
        h = true;
        f = 782102955;
    }

    static void init$0() {
        $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
        $$b = 79;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            int r7 = 121 - r7
            byte[] r0 = o.i.m.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r7 = r9
            r3 = r1
            r4 = r2
            r9 = r8
            r1 = r0
            r0 = r10
            r10 = r7
            goto L35
        L17:
            r3 = r2
            r6 = r9
            r9 = r7
            r7 = r6
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r8 = -r8
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.m.l(short, int, short, java.lang.Object[]):void");
    }

    private static /* synthetic */ m[] b() {
        m[] mVarArr;
        int i2 = m;
        int i3 = i2 + 69;
        j = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                mVarArr = new m[]{a, e, b};
                break;
            default:
                mVarArr = new m[4];
                mVarArr[1] = a;
                mVarArr[1] = e;
                mVarArr[2] = b;
                break;
        }
        int i4 = i2 + 59;
        j = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return mVarArr;
            default:
                throw null;
        }
    }

    public static m valueOf(String str) {
        int i2 = m + 23;
        j = i2 % 128;
        boolean z = i2 % 2 != 0;
        m mVar = (m) Enum.valueOf(m.class, str);
        switch (z) {
            default:
                int i3 = 83 / 0;
            case false:
                return mVar;
        }
    }

    public static m[] values() {
        int i2 = j + 61;
        m = i2 % 128;
        int i3 = i2 % 2;
        m[] mVarArr = (m[]) c.clone();
        int i4 = m + 9;
        j = i4 % 128;
        int i5 = i4 % 2;
        return mVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        m = 1;
        c();
        Object[] objArr = new Object[1];
        k(null, (ViewConfiguration.getTapTimeout() >> 16) + 127, null, "\u0089\u0084\u0095\u0095\u0094\u0093\u0085\u008e\u0092", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, 126 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), null, "\u0081\u0096", objArr2);
        a = new m(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k(null, TextUtils.getOffsetAfter("", 0) + 127, null, "\u008b\u0099\u0089\u0081\u0082\u0098\u0097", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        k(null, 128 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), null, "\u009b\u009a\u0097", objArr4);
        e = new m(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        k(null, 127 - (ViewConfiguration.getWindowTouchSlop() >> 8), null, "\u0095\u0084\u0090\u008e\u0099\u0099\u009c", objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        k(null, (Process.myTid() >> 22) + 127, null, "\u0097\u0097\u009c", objArr6);
        b = new m(intern3, 2, ((String) objArr6[0]).intern());
        c = b();
        int i2 = j + 19;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    private m(String str, int i2, String str2) {
        this.d = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = j + 37;
        int i3 = i2 % 128;
        m = i3;
        int i4 = i2 % 2;
        String str = this.d;
        int i5 = i3 + 31;
        j = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.i.m d(java.lang.String r7) {
        /*
            int r0 = o.i.m.m
            int r0 = r0 + 35
            int r1 = r0 % 128
            o.i.m.j = r1
            int r0 = r0 % 2
            o.i.m[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L11:
            r4 = 1
            if (r3 >= r1) goto L16
            r5 = r4
            goto L17
        L16:
            r5 = r2
        L17:
            switch(r5) {
                case 1: goto L4b;
                default: goto L1a;
            }
        L1a:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r3 = ""
            r5 = 48
            int r3 = android.text.TextUtils.lastIndexOf(r3, r5)
            int r3 = r3 + 128
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r5 = 0
            java.lang.String r6 = "\u0086\u0091\u0086\u0089\u0090\u008e\u0089\u008f\u0089\u0088\u0086\u0084\u008d\u008b\u0082\u0086\u0082\u0085\u008e\u008d\u008d\u0088\u008c\u0086\u008b\u008a\u0089\u0084\u0085\u0085\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            k(r5, r3, r5, r6, r4)
            r2 = r4[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r7 = r1.append(r7)
            java.lang.String r7 = r7.toString()
            r0.<init>(r7)
            throw r0
        L4b:
            r5 = r0[r3]
            java.lang.String r6 = r5.d
            boolean r6 = r6.equals(r7)
            if (r6 == 0) goto L57
            r4 = r2
            goto L58
        L57:
        L58:
            switch(r4) {
                case 0: goto L5e;
                default: goto L5b;
            }
        L5b:
            int r3 = r3 + 1
            goto L69
        L5e:
            int r7 = o.i.m.m
            int r7 = r7 + 11
            int r0 = r7 % 128
            o.i.m.j = r0
            int r7 = r7 % 2
            return r5
        L69:
            goto L11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.m.d(java.lang.String):o.i.m");
    }

    public final ForeignCurrencySupport d() {
        if (equals(a)) {
            ForeignCurrencySupport foreignCurrencySupport = ForeignCurrencySupport.No;
            int i2 = m + Opcodes.LMUL;
            j = i2 % 128;
            switch (i2 % 2 != 0 ? 'O' : 'Q') {
                case Opcodes.FASTORE /* 81 */:
                    return foreignCurrencySupport;
                default:
                    throw null;
            }
        }
        switch (equals(e) ? '%' : (char) 30) {
            case '%':
                int i3 = j + 17;
                m = i3 % 128;
                if (i3 % 2 != 0) {
                    return ForeignCurrencySupport.LowValueTransactions;
                }
                ForeignCurrencySupport foreignCurrencySupport2 = ForeignCurrencySupport.LowValueTransactions;
                throw null;
            default:
                if (!equals(b)) {
                    return null;
                }
                int i4 = j + Opcodes.DREM;
                m = i4 % 128;
                if (i4 % 2 != 0) {
                    return ForeignCurrencySupport.All;
                }
                ForeignCurrencySupport foreignCurrencySupport3 = ForeignCurrencySupport.All;
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 928
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.m.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
