package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\XORBoxState.smali */
public class XORBoxState implements Serializable {
    public static final int BOXES = 32;
    public static final int WIDTH = 16;
    private static final long serialVersionUID = 1274697272673072694L;
    protected byte[][] xor = null;

    public XORBoxState() {
        init();
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.deepEquals(this.xor, ((XORBoxState) obj).xor);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.deepHashCode(this.xor) + 485;
    }

    public final void init() {
        this.xor = (byte[][]) Array.newInstance((Class<?>) Byte.TYPE, 32, 256);
    }

    public State xorA(State state, State state2) {
        return xorA(this.xor, state, state2);
    }

    public static State xorA(byte[][] bArr, State state, State state2) {
        byte[] state3 = state.getState();
        byte[] state4 = state2.getState();
        for (int i = 0; i < 16; i++) {
            int i2 = i * 2;
            byte[] bArr2 = bArr[i2];
            byte b = state3[i];
            byte b2 = state4[i];
            state3[i] = (byte) ((bArr[i2 + 1][(((b >>> 4) & 15) << 4) | ((b2 >>> 4) & 15)] << 4) | bArr2[((b & 15) << 4) | (b2 & 15)]);
        }
        state.setState(state3, false);
        return state;
    }
}
