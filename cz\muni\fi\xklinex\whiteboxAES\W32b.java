package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\W32b.smali */
public class W32b implements Serializable {
    public static final int WIDTH = 4;
    private static final long serialVersionUID = 5382577044317817112L;
    protected byte[] b = null;

    public W32b() {
        init();
    }

    public static byte[] initNew() {
        return new byte[4];
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.equals(this.b, ((W32b) obj).b);
        }
        return false;
    }

    public byte[] get() {
        return this.b;
    }

    public long getLong() {
        return Utils.byte2long(get());
    }

    public int hashCode() {
        return Arrays.hashCode(this.b) + 205;
    }

    public final void init() {
        this.b = initNew();
    }

    public void set(long j) {
        Utils.long2byte(this.b, j);
    }

    public String toString() {
        if (this.b == null) {
            return "W32b{b=null}";
        }
        StringBuilder sb = new StringBuilder();
        int length = this.b.length;
        int i = 0;
        while (i < length) {
            sb.append(String.format("0x%02X", Integer.valueOf(this.b[i] & 255)));
            i++;
            if (i != length) {
                sb.append(", ");
            }
        }
        return "W32b{b=" + ((Object) sb) + ";mem=" + Arrays.toString(this.b) + "}";
    }
}
