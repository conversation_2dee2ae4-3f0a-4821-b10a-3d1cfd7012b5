package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP384R1FieldElement.smali */
public class SecP384R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF"));
    protected int[] a;

    public SecP384R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP384R1FieldElement");
        }
        this.a = SecP384R1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = c6.a(12);
        SecP384R1Field.add(this.a, ((SecP384R1FieldElement) eCFieldElement).a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = c6.a(12);
        SecP384R1Field.addOne(this.a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = c6.a(12);
        SecP384R1Field.inv(((SecP384R1FieldElement) eCFieldElement).a, a);
        SecP384R1Field.multiply(a, this.a, a);
        return new SecP384R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP384R1FieldElement) {
            return c6.c(12, this.a, ((SecP384R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP384R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 12);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = c6.a(12);
        SecP384R1Field.inv(this.a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return c6.d(12, this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return c6.e(12, this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = c6.a(12);
        SecP384R1Field.multiply(this.a, ((SecP384R1FieldElement) eCFieldElement).a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = c6.a(12);
        SecP384R1Field.negate(this.a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (c6.e(12, iArr) || c6.d(12, iArr)) {
            return this;
        }
        int[] a = c6.a(24);
        int[] a2 = c6.a(12);
        int[] a3 = c6.a(12);
        int[] a4 = c6.a(12);
        int[] a5 = c6.a(12);
        SecP384R1Field.square(iArr, a2, a);
        SecP384R1Field.multiply(a2, iArr, a2, a);
        SecP384R1Field.squareN(a2, 2, a3, a);
        SecP384R1Field.multiply(a3, a2, a3, a);
        SecP384R1Field.square(a3, a3, a);
        SecP384R1Field.multiply(a3, iArr, a3, a);
        SecP384R1Field.squareN(a3, 5, a4, a);
        SecP384R1Field.multiply(a4, a3, a4, a);
        SecP384R1Field.squareN(a4, 5, a5, a);
        SecP384R1Field.multiply(a5, a3, a5, a);
        SecP384R1Field.squareN(a5, 15, a3, a);
        SecP384R1Field.multiply(a3, a5, a3, a);
        SecP384R1Field.squareN(a3, 2, a4, a);
        SecP384R1Field.multiply(a2, a4, a2, a);
        SecP384R1Field.squareN(a4, 28, a4, a);
        SecP384R1Field.multiply(a3, a4, a3, a);
        SecP384R1Field.squareN(a3, 60, a4, a);
        SecP384R1Field.multiply(a4, a3, a4, a);
        SecP384R1Field.squareN(a4, Opcodes.ISHL, a3, a);
        SecP384R1Field.multiply(a3, a4, a3, a);
        SecP384R1Field.squareN(a3, 15, a3, a);
        SecP384R1Field.multiply(a3, a5, a3, a);
        SecP384R1Field.squareN(a3, 33, a3, a);
        SecP384R1Field.multiply(a3, a2, a3, a);
        SecP384R1Field.squareN(a3, 64, a3, a);
        SecP384R1Field.multiply(a3, iArr, a3, a);
        SecP384R1Field.squareN(a3, 30, a2, a);
        SecP384R1Field.square(a2, a3, a);
        if (c6.c(12, iArr, a3)) {
            return new SecP384R1FieldElement(a2);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = c6.a(12);
        SecP384R1Field.square(this.a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = c6.a(12);
        SecP384R1Field.subtract(this.a, ((SecP384R1FieldElement) eCFieldElement).a, a);
        return new SecP384R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return c6.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return c6.f(12, this.a);
    }

    public SecP384R1FieldElement() {
        this.a = c6.a(12);
    }

    protected SecP384R1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
