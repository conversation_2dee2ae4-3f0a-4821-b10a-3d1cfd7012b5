package o.dg;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.work.WorkRequest;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.sca.CancellationReason;
import fr.antelop.sdk.sca.PushAuthenticationRequestListener;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.f;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\b.smali */
public final class b extends c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static final List<String> c;
    private static final Map<String, o.w.d> d;
    private static char[] e;
    private static byte[] f;
    private static int g;
    private static short[] h;
    private static int i;
    private static int j;

    static void a() {
        e = new char[]{50916, 50838, 50863, 50842, 50835, 50876, 50851, 50858, 50858, 50859, 50863, 50855, 50851, 50849, 50851, 50836, 50843, 50851, 50876, 50853, 50855, 50854, 50856, 50859, 50854, 50872, 50721, 50745, 50746, 50723, 50749, 50748, 50726, 50695, 50785, 50785, 50688, 50745, 50688, 50688, 50745, 50746, 50745, 50720, 50720, 50721, 50725, 50749, 50745, 50751, 50745, 50688, 50695, 50721, 50691, 50785, 50699, 50730, 50723, 50747, 50740, 50745, 50748, 50749, 51179, 51180, 51181, 50719, 51174, 51179, 51183, 51154, 51162, 50719, 50706, 50719, 51159, 51160, 51179, 51166, 51183, 51176, 51158, 51163, 51156, 51181, 51158, 51179, 51177, 51180, 51161, 51166, 50719, 50707, 51181, 51180, 51158, 51179, 51166, 51160, 51158, 51157, 51158, 50930, 50870, 50867, 50760, 50865, 50875, 50869, 50941, 50854, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50834, 50837, 50855, 50873, 50849, 50854, 50877, 50943, 50855, 50873, 50859, 50857, 50876, 50852, 50849, 50876, 50851, 50836, 50841, 50855, 50850, 50877, 50876, 50844, 50786, 50786, 50697, 50723, 50721, 50729, 50697, 50691, 50746, 50722, 50723, 50747, 50749, 50697, 50789, 50789, 50692, 50749, 50723, 50749, 50721, 50729, 50725, 50724, 50724, 50749, 50750, 50749, 50692, 50697, 50731, 50720, 50722, 50723, 50747, 50749, 50697, 50789, 50789, 50699, 50730, 50720, 50721, 50727, 50750, 50749, 50725, 50932, 50854, 50852, 50852, 50849, 50878, 50851, 50839, 50861, 50878, 50849, 50848, 50878, 50854, 50838, 50861, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50841, 50848, 50873, 50879, 50818, 50818, 50850, 50848, 50853, 50849, 50876, 50858, 50826, 50845, 50849, 50822, 50804, 50710, 50706, 50706, 50710, 50706, 50707, 50708, 50805, 50803, 50728, 50734, 50728, 50732, 50708, 50704, 50707, 50707, 50728, 50725, 50712, 50787, 50768, 50768, 50806, 50705, 50735, 50732, 50706, 50725, 50728, 50704, 50708, 50781, 51181, 51177, 51173, 51177, 51181, 51177, 51182, 51139, 51162, 51170, 51173, 51172, 51170, 51178, 51162, 51153, 51175, 51150, 51144, 51178, 51183, 51182, 51177, 51172, 51170, 51147, 50735, 50735, 51149, 51180, 51178, 51179, 51177, 51168, 51175, 51183, 50941, 50829, 50831, 50848, 50848, 50848, 50878, 50821, 50826, 50854, 50825, 50830, 50855, 50854, 50859, 50857, 50863, 50829, 50923, 50845, 50878, 50849, 50848, 50878, 50854, 50854, 50820, 50826, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50854, 50829, 50831, 50857, 50848, 50850, 50858, 50857, 50857, 50854, 50820, 50817, 50878, 50858, 50829, 50826, 50876, 50823, 50831, 50852, 50876, 50879, 50853, 50828, 50823, 50876, 50851, 50826, 50823, 50878, 50849, 50848, 50878, 50854, 50854, 50820, 50827, 50853, 50857, 50853, 50858, 50847, 50940, 50923, 50923, 50825, 50856, 50854, 50855, 50853, 50876, 50851, 50859, 50935, 50855, 50863, 50863};
        f = new byte[]{4, -111, 123, -121, 101, 105, -98, -111, 5, 60, -14, 38, 41, -41, 35, 43, -44, 2, -54, 53, 39, -39, 53, 6, -55, 43, 37, -13, 63, 49, -63, -64, 43, 73, -33, 37, 37, 96, -118, 39, 40, -39, -37, -35, 43, -46, 107, -108, 35, 39, -46, 45, -44, 113, -120, 43, -47, 47, -41, 39, 44, 59, -2, 39, 40, -42, 34, 42, 53, -62, -39, 32, -45, 53, -40, -36, -45, 32, 47, -37, -46, -39, 18, -1, -45, -40, 35, 100, -104, 39, 37, 33, -41, 32, 43, 21, -102, 42, 96, -102, -48, 35, 37, 96, -118, 35, 39, 38, 43, 56, 63, -122, 107, 56, -57, -43, 43, -57, -107, 125, 52, -57, -41, 55, -106, 119, Base64.padSymbol, -110, 125, 63, -52, -57, 53, -33, -13, 53, -53, 126, -61, 41, -43, 55, 59, -52, -61, 29, 48, ByteCompanionObject.MIN_VALUE, 108, -63, -50, 48, -60, -52, -45, -14, 51, -51, 120, -59, 47, -45, 49, Base64.padSymbol, -54, -59, 14, -20, 14, -16, 28, 78, -92, -28, 24, 24, -21, 20, -24, 77, -81, 24, 84, -35, 38, 80, -84, -17, -23, 23, 18, -27, 29, -18, 83, -73, 5, 19, -20, -29, 22, 27, 81, -94, -17, 22, -27, 3, -18, -22, 19, -19, -27, 21, 49, 62, -29, 29, -88, 21, -1, 3, -31, -19, 26, 21, 4, 99, -112, -106, 113, -117, -106, 71, 79, 108, -112, 51, 115, -113, 124, -118, -124, -120, 126, 119, -120, -38, 51, 115, -113, ByteCompanionObject.MAX_VALUE, -120, -119, -121, -117, -53, 126, 77, 116, -116, 100, -118, -124, 114, -57, 56, 117, -116, ByteCompanionObject.MAX_VALUE, -103, 116, 112, ByteCompanionObject.MAX_VALUE, -116, -125, 119, 126, 117, -66, -85, 50, ByteCompanionObject.MAX_VALUE, 116, -81, -70, 54, 113, 119, -127, 97, -126, -113, -61, 121, -121, 56, 117, -116, ByteCompanionObject.MAX_VALUE, -103, 116, 112, -119, 119, ByteCompanionObject.MAX_VALUE, -113, -85, 103, 116, -102, 100, 115, -126, 124, -121, 115};
        b = 909053668;
        j = 113010655;
        a = 2129603518;
    }

    static void init$0() {
        $$a = new byte[]{31, 57, -118, -60};
        $$b = 40;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = 3 - r6
            int r7 = r7 + 66
            byte[] r0 = o.dg.b.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r6
            goto L39
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1e:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L39:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dg.b.m(int, short, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        g = 1;
        a();
        d = new HashMap();
        c = new ArrayList();
        int i2 = g + 109;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return;
            default:
                int i3 = 64 / 0;
                return;
        }
    }

    @Override // o.dg.c
    public final a e(Context context, o.eg.b bVar, Long l) {
        String str;
        String intern;
        String intern2;
        o.eg.b bVar2 = bVar;
        Object[] objArr = new Object[1];
        k("\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{0, 25, 0, 0}, false, objArr);
        String intern3 = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        l((byte) (5 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1220551982, (short) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-118) - TextUtils.indexOf((CharSequence) "", '0'), AndroidCharacter.getMirror('0') - 26907, objArr2);
        g.d(intern3, ((String) objArr2[0]).intern());
        if (l == null) {
            g.c();
            Object[] objArr3 = new Object[1];
            k("\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{25, 39, Opcodes.I2F, 0}, false, objArr3);
            g.d(intern3, ((String) objArr3[0]).intern());
            a aVar = new a(false);
            int i2 = i + 5;
            g = i2 % 128;
            int i3 = i2 % 2;
            return aVar;
        }
        Object obj = null;
        try {
            if (bVar2 == null) {
                g.c();
                Object[] objArr4 = new Object[1];
                k(null, new int[]{64, 39, Opcodes.GETSTATIC, 20}, true, objArr4);
                g.d(intern3, ((String) objArr4[0]).intern());
                a aVar2 = new a(false);
                int i4 = i + 47;
                g = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return aVar2;
                    default:
                        obj.hashCode();
                        throw null;
                }
            }
            Object[] objArr5 = new Object[1];
            k("\u0001\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{Opcodes.DSUB, 7, 18, 0}, false, objArr5);
            switch (bVar2.b(((String) objArr5[0]).intern())) {
                case false:
                    break;
                default:
                    int i5 = i + Opcodes.LSHR;
                    g = i5 % 128;
                    if (i5 % 2 == 0) {
                        Object[] objArr6 = new Object[1];
                        k("\u0001\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{Opcodes.DSUB, 7, 18, 0}, false, objArr6);
                        intern2 = ((String) objArr6[0]).intern();
                    } else {
                        Object[] objArr7 = new Object[1];
                        k("\u0001\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{Opcodes.DSUB, 7, 18, 0}, false, objArr7);
                        intern2 = ((String) objArr7[0]).intern();
                    }
                    bVar2 = bVar2.v(intern2);
                    break;
            }
            Object[] objArr8 = new Object[1];
            l((byte) ((-73) - View.MeasureSpec.makeMeasureSpec(0, 0)), KeyEvent.getDeadChar(0, 0) - 1220551974, (short) (ViewConfiguration.getTouchSlop() >> 8), Drawable.resolveOpacity(0, 0) - 117, Gravity.getAbsoluteGravity(0, 0) - 814967005, objArr8);
            String r = bVar2.r(((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            k("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{Opcodes.FDIV, 21, 0, 0}, false, objArr9);
            String r2 = bVar2.r(((String) objArr9[0]).intern());
            Object[] objArr10 = new Object[1];
            k("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{Opcodes.LXOR, 16, 0, 0}, true, objArr10);
            if (bVar2.b(((String) objArr10[0]).intern())) {
                int i6 = i + 31;
                g = i6 % 128;
                if (i6 % 2 == 0) {
                    Object[] objArr11 = new Object[1];
                    k("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{Opcodes.LXOR, 16, 0, 0}, false, objArr11);
                    intern = ((String) objArr11[0]).intern();
                } else {
                    Object[] objArr12 = new Object[1];
                    k("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{Opcodes.LXOR, 16, 0, 0}, true, objArr12);
                    intern = ((String) objArr12[0]).intern();
                }
                str = bVar2.r(intern);
            } else {
                str = null;
            }
            Object[] objArr13 = new Object[1];
            l((byte) ((-92) - TextUtils.getCapsMode("", 0, 0)), (-1220551965) - View.combineMeasuredStates(0, 0), (short) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), (-117) - View.resolveSizeAndState(0, 0, 0), (-814967004) - ((Process.getThreadPriority(0) + 20) >> 6), objArr13);
            String q = bVar2.q(((String) objArr13[0]).intern());
            Object[] objArr14 = new Object[1];
            l((byte) (TextUtils.indexOf("", "") - 88), (-1220551959) - (ViewConfiguration.getPressedStateDuration() >> 16), (short) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (-117) - TextUtils.indexOf("", "", 0), (ViewConfiguration.getEdgeSlop() >> 16) - 814967018, objArr14);
            Date e2 = bVar2.e(((String) objArr14[0]).intern(), false);
            if (e2.before(new Date(new Date().getTime() + WorkRequest.MIN_BACKOFF_MILLIS))) {
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr15 = new Object[1];
                k("\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{Opcodes.I2S, 48, Opcodes.IXOR, 0}, true, objArr15);
                g.e(intern3, sb.append(((String) objArr15[0]).intern()).append(e2).toString());
                return new a(false, false);
            }
            try {
                Object[] objArr16 = new Object[1];
                k("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{Opcodes.MONITOREXIT, 44, 0, 0}, true, objArr16);
                String a2 = o.a(context, ((String) objArr16[0]).intern());
                int i7 = i + 27;
                g = i7 % 128;
                int i8 = i7 % 2;
                Object[] objArr17 = new Object[1];
                k("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000", new int[]{Opcodes.MONITOREXIT, 44, 0, 0}, true, objArr17);
                PushAuthenticationRequestListener pushAuthenticationRequestListener = (PushAuthenticationRequestListener) o.e(PushAuthenticationRequestListener.class, a2, ((String) objArr17[0]).intern());
                if (q == null) {
                    List<String> list = c;
                    if (!list.contains(r)) {
                        Object[] objArr18 = new Object[1];
                        l((byte) ((-6) - TextUtils.indexOf("", "", 0)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1220551757, (short) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (-117) - Color.argb(0, 0, 0, 0), (-814967045) - Drawable.resolveOpacity(0, 0), objArr18);
                        o.w.d dVar = new o.w.d(r2, new byte[0], r, e2, l, ((String) objArr18[0]).intern().equals(str));
                        d.put(r, dVar);
                        Object[] objArr19 = new Object[1];
                        k("\u0000\u0001\u0001\u0001", new int[]{403, 4, 0, 2}, false, objArr19);
                        dVar.e(android.util.Base64.decode(bVar2.r(((String) objArr19[0]).intern()), 2));
                        pushAuthenticationRequestListener.onRequestReceived(context, dVar.y());
                        return new a(true, true);
                    }
                    g.c();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr20 = new Object[1];
                    l((byte) ((-128) - ((Process.getThreadPriority(0) + 20) >> 6)), Color.alpha(0) - 1220551798, (short) (ViewConfiguration.getDoubleTapTimeout() >> 16), (-117) - View.MeasureSpec.getMode(0), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 814967020, objArr20);
                    StringBuilder append = sb2.append(((String) objArr20[0]).intern()).append(r);
                    Object[] objArr21 = new Object[1];
                    l((byte) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 2), (-1220551837) - View.MeasureSpec.getMode(0), (short) TextUtils.getOffsetAfter("", 0), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) - 117, (-814967023) - TextUtils.getTrimmedLength(""), objArr21);
                    g.d(intern3, append.append(((String) objArr21[0]).intern()).toString());
                    list.remove(r);
                    return new a(false, false);
                }
                int i9 = g + 57;
                i = i9 % 128;
                int i10 = i9 % 2;
                e eVar = (e) o.c(e.class, q);
                if (eVar == null) {
                    g.c();
                    StringBuilder sb3 = new StringBuilder();
                    Object[] objArr22 = new Object[1];
                    l((byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 85), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 1220551872, (short) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), MotionEvent.axisFromString("") - 116, MotionEvent.axisFromString("") - 814967018, objArr22);
                    StringBuilder append2 = sb3.append(((String) objArr22[0]).intern()).append(q);
                    Object[] objArr23 = new Object[1];
                    l((byte) ((ViewConfiguration.getTouchSlop() >> 8) + 2), (-1220551837) - KeyEvent.normalizeMetaState(0), (short) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (-118) - TextUtils.indexOf((CharSequence) "", '0'), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 814967023, objArr23);
                    g.e(intern3, append2.append(((String) objArr23[0]).intern()).toString());
                    return new a(false, false);
                }
                g.c();
                StringBuilder sb4 = new StringBuilder();
                Object[] objArr24 = new Object[1];
                k("\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000", new int[]{239, 34, Opcodes.DNEG, 33}, true, objArr24);
                StringBuilder append3 = sb4.append(((String) objArr24[0]).intern()).append(eVar.name());
                Object[] objArr25 = new Object[1];
                l((byte) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 2), ExpandableListView.getPackedPositionGroup(0L) - 1220551837, (short) (ViewConfiguration.getTapTimeout() >> 16), (-117) - (ViewConfiguration.getScrollDefaultDelay() >> 16), (-814967023) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr25);
                g.d(intern3, append3.append(((String) objArr25[0]).intern()).toString());
                Map<String, o.w.d> map = d;
                if (!map.containsKey(r)) {
                    g.c();
                    StringBuilder sb5 = new StringBuilder();
                    Object[] objArr26 = new Object[1];
                    k("\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{310, 93, 0, 0}, true, objArr26);
                    StringBuilder append4 = sb5.append(((String) objArr26[0]).intern()).append(r);
                    Object[] objArr27 = new Object[1];
                    l((byte) (KeyEvent.getDeadChar(0, 0) + 2), (-1220551837) - ExpandableListView.getPackedPositionGroup(0L), (short) TextUtils.getCapsMode("", 0, 0), (-117) - (ViewConfiguration.getScrollBarFadeDuration() >> 16), Color.alpha(0) - 814967023, objArr27);
                    g.d(intern3, append4.append(((String) objArr27[0]).intern()).toString());
                    c.add(r);
                    return new a(true, false);
                }
                g.c();
                StringBuilder sb6 = new StringBuilder();
                Object[] objArr28 = new Object[1];
                l((byte) (80 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (Process.myTid() >> 22) - 1220551836, (short) ExpandableListView.getPackedPositionGroup(0L), Color.red(0) - 117, Color.red(0) - 814967019, objArr28);
                StringBuilder append5 = sb6.append(((String) objArr28[0]).intern()).append(r);
                Object[] objArr29 = new Object[1];
                l((byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 116), (-1220551816) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) (ExpandableListView.getPackedPositionChild(0L) + 1), (-117) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), View.getDefaultSize(0, 0) - 814967023, objArr29);
                g.d(intern3, append5.append(((String) objArr29[0]).intern()).toString());
                o.w.d dVar2 = map.get(r);
                dVar2.a(new o.bv.c(AntelopErrorCode.ScaCancelledFromBackend));
                map.remove(r);
                g.c();
                Object[] objArr30 = new Object[1];
                k("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{273, 37, 188, 0}, true, objArr30);
                g.d(intern3, ((String) objArr30[0]).intern());
                pushAuthenticationRequestListener.onRequestCancelled(context, r, eVar.d());
                return new a(true, false, dVar2.x(), null);
            } catch (Exception e3) {
                Object[] objArr31 = new Object[1];
                l((byte) (Process.getGidForName("") - 73), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1220551950, (short) ((-1) - MotionEvent.axisFromString("")), (-117) - (KeyEvent.getMaxKeyCode() >> 16), (KeyEvent.getMaxKeyCode() >> 16) - 814967052, objArr31);
                throw new RuntimeException(((String) objArr31[0]).intern());
            }
        } catch (o.eg.d e4) {
            g.c();
            StringBuilder sb7 = new StringBuilder();
            Object[] objArr32 = new Object[1];
            l((byte) (26 - View.combineMeasuredStates(0, 0)), (-1220551748) - View.resolveSizeAndState(0, 0, 0), (short) TextUtils.getOffsetBefore("", 0), (-117) - TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) - 814967015, objArr32);
            g.d(intern3, sb7.append(((String) objArr32[0]).intern()).append(e4.getMessage()).toString());
            return new a(false);
        }
    }

    public static void b(String str) {
        int i2 = i + 41;
        g = i2 % 128;
        int i3 = i2 % 2;
        d.remove(str);
        int i4 = i + Opcodes.DSUB;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\b$e.smali */
    public static final class e implements o.ee.a<CancellationReason>, o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final e a;
        public static final e b;
        private static int c;
        private static final /* synthetic */ e[] d;
        private static int f;
        private static short[] g;
        private static int h;
        private static byte[] i;
        private static int j;
        private static int m;
        private final String e;

        static void b() {
            i = new byte[]{114, -51, -2, -37, 34, 32, 32, 29, -105, -44, -38, 58, -43, -34, -45, 56, -36, 50, 113, -20, -32, 23, -24, -13, 59, -42, 63, 30, 24, -31, -26, -29, 20, -20, -1, 73, -87, -81, 86, 81, 84, -93, 91, -88, 78, 114, -116, 123, 114, 114, 112, -125, -98, 109, ByteCompanionObject.MIN_VALUE, 112, 121, -88, 79, -38, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -45, -38, -38, -40, 43, -42, -42, 40, 40, -40, -47, 32};
            f = 909053600;
            h = -46702338;
            c = 791804734;
        }

        static void init$0() {
            $$a = new byte[]{15, -30, 44, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
            $$b = 248;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r6, short r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 + 4
                int r8 = r8 * 2
                int r8 = 110 - r8
                byte[] r0 = o.dg.b.e.$$a
                int r6 = r6 * 3
                int r6 = r6 + 1
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r7
                goto L37
            L19:
                r3 = r2
                r5 = r8
                r8 = r7
                r7 = r5
            L1d:
                int r8 = r8 + 1
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L2c
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2c:
                int r3 = r3 + 1
                r4 = r0[r8]
                r5 = r9
                r9 = r8
                r8 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L37:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.dg.b.e.l(short, short, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ e[] c() {
            int i2 = m + 5;
            j = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    e[] eVarArr = new e[4];
                    eVarArr[1] = b;
                    eVarArr[1] = a;
                    return eVarArr;
                default:
                    return new e[]{b, a};
            }
        }

        public static e valueOf(String str) {
            int i2 = m + 61;
            j = i2 % 128;
            int i3 = i2 % 2;
            e eVar = (e) Enum.valueOf(e.class, str);
            int i4 = m + 33;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return eVar;
            }
        }

        public static e[] values() {
            int i2 = m + 67;
            j = i2 % 128;
            switch (i2 % 2 != 0 ? 'c' : 'O') {
                case Opcodes.IASTORE /* 79 */:
                    return (e[]) d.clone();
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.ee.d
        public final /* synthetic */ Object a() {
            int i2 = j + 33;
            m = i2 % 128;
            int i3 = i2 % 2;
            CancellationReason d2 = d();
            int i4 = m + 9;
            j = i4 % 128;
            int i5 = i4 % 2;
            return d2;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            m = 1;
            b();
            Object[] objArr = new Object[1];
            k((byte) (113 - View.resolveSize(0, 0)), (-421459355) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), View.getDefaultSize(0, 0) - 49, 887591381 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((byte) ((-58) - Color.alpha(0)), (-421459339) - Color.blue(0), (short) TextUtils.getOffsetAfter("", 0), View.MeasureSpec.getSize(0) - 49, (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 887591380, objArr2);
            b = new e(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k((byte) ((ViewConfiguration.getEdgeSlop() >> 16) + 29), Process.getGidForName("") - 421459329, (short) KeyEvent.keyCodeFromString(""), (-50) - TextUtils.indexOf((CharSequence) "", '0', 0), View.MeasureSpec.getMode(0) + 887591393, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((byte) ((-75) - (ViewConfiguration.getScrollDefaultDelay() >> 16)), (-421459317) - ImageFormat.getBitsPerPixel(0), (short) View.getDefaultSize(0, 0), (-49) - (ViewConfiguration.getWindowTouchSlop() >> 8), (KeyEvent.getMaxKeyCode() >> 16) + 887591393, objArr4);
            a = new e(intern2, 1, ((String) objArr4[0]).intern());
            d = c();
            int i2 = j + Opcodes.LUSHR;
            m = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return;
                default:
                    int i3 = 68 / 0;
                    return;
            }
        }

        private e(String str, int i2, String str2) {
            this.e = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = j;
            int i3 = i2 + 19;
            m = i3 % 128;
            int i4 = i3 % 2;
            String str = this.e;
            int i5 = i2 + 27;
            m = i5 % 128;
            switch (i5 % 2 == 0 ? 'I' : Typography.quote) {
                case '\"':
                    return str;
                default:
                    int i6 = 94 / 0;
                    return str;
            }
        }

        public final CancellationReason d() {
            int i2 = j + 69;
            m = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0) {
                case true:
                    switch (AnonymousClass4.e[ordinal()]) {
                        case 1:
                            CancellationReason cancellationReason = CancellationReason.CancelledBySender;
                            int i3 = j + 51;
                            m = i3 % 128;
                            switch (i3 % 2 == 0) {
                                case false:
                                    return cancellationReason;
                                default:
                                    obj.hashCode();
                                    throw null;
                            }
                        case 2:
                            return CancellationReason.OtherResponded;
                        default:
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            k((byte) ((-69) - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), (-421459374) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), (-50) - ExpandableListView.getPackedPositionChild(0L), 887591399 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
                            throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                    }
                default:
                    int i4 = AnonymousClass4.e[ordinal()];
                    obj.hashCode();
                    throw null;
            }
        }

        private static void k(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
            boolean z;
            int i5;
            boolean z2;
            f fVar = new f();
            StringBuilder sb = new StringBuilder();
            try {
                Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(f)};
                Object obj = o.e.a.s.get(-2120899312);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(11 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) View.getDefaultSize(0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 65);
                    byte b3 = (byte) 0;
                    byte b4 = (byte) (b3 - 1);
                    Object[] objArr3 = new Object[1];
                    l(b3, b4, (byte) (-b4), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(-2120899312, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                switch (intValue == -1) {
                    case false:
                        z = false;
                        break;
                    default:
                        int i6 = $10 + 89;
                        $11 = i6 % 128;
                        if (i6 % 2 == 0) {
                        }
                        z = true;
                        break;
                }
                switch (z ? '=' : Typography.dollar) {
                    case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                        byte[] bArr = i;
                        char c2 = '0';
                        if (bArr != null) {
                            int length = bArr.length;
                            byte[] bArr2 = new byte[length];
                            int i7 = 0;
                            while (i7 < length) {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr[i7])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(19 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) (TextUtils.indexOf("", c2, 0) + 16426), 150 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                                        byte b5 = (byte) 0;
                                        byte b6 = (byte) (b5 - 1);
                                        Object[] objArr5 = new Object[1];
                                        l(b5, b6, (byte) (b6 + 1), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr2[i7] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i7++;
                                    c2 = '0';
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            }
                            bArr = bArr2;
                        }
                        if (bArr == null) {
                            intValue = (short) (((short) (g[i2 + ((int) (c ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (f ^ (-5810760824076169584L))));
                            break;
                        } else {
                            int i8 = $11 + Opcodes.LSHL;
                            $10 = i8 % 128;
                            int i9 = i8 % 2;
                            byte[] bArr3 = i;
                            try {
                                Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(c)};
                                Object obj3 = o.e.a.s.get(-2120899312);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) (TextUtils.lastIndexOf("", '0') + 1), TextUtils.lastIndexOf("", '0', 0, 0) + 66);
                                    byte b7 = (byte) 0;
                                    byte b8 = (byte) (b7 - 1);
                                    Object[] objArr7 = new Object[1];
                                    l(b7, b8, (byte) (-b8), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-2120899312, obj3);
                                }
                                intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (f ^ (-5810760824076169584L))));
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
                if (intValue > 0) {
                    int i10 = ((i2 + intValue) - 2) + ((int) (c ^ (-5810760824076169584L)));
                    switch (!z) {
                        case false:
                            i5 = 1;
                            break;
                        default:
                            i5 = 0;
                            break;
                    }
                    fVar.d = i10 + i5;
                    try {
                        Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(h), sb};
                        Object obj4 = o.e.a.s.get(160906762);
                        if (obj4 == null) {
                            obj4 = ((Class) o.e.a.c(11 - View.resolveSize(0, 0), (char) (ExpandableListView.getPackedPositionChild(0L) + 1), TextUtils.getCapsMode("", 0, 0) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj4);
                        }
                        ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr4 = i;
                        switch (bArr4 == null) {
                            case true:
                                break;
                            default:
                                int length2 = bArr4.length;
                                byte[] bArr5 = new byte[length2];
                                for (int i11 = 0; i11 < length2; i11++) {
                                    bArr5[i11] = (byte) (bArr4[i11] ^ (-5810760824076169584L));
                                }
                                int i12 = $10 + 77;
                                $11 = i12 % 128;
                                int i13 = i12 % 2;
                                bArr4 = bArr5;
                                break;
                        }
                        switch (bArr4 == null) {
                            case false:
                                int i14 = $11 + 59;
                                $10 = i14 % 128;
                                int i15 = i14 % 2;
                                z2 = true;
                                break;
                            default:
                                z2 = false;
                                break;
                        }
                        fVar.c = 1;
                        while (fVar.c < intValue) {
                            int i16 = $11 + 13;
                            $10 = i16 % 128;
                            int i17 = i16 % 2;
                            if (z2) {
                                byte[] bArr6 = i;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                            } else {
                                short[] sArr = g;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                            }
                            sb.append(fVar.e);
                            fVar.b = fVar.e;
                            fVar.c++;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr[0] = sb.toString();
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /* renamed from: o.dg.b$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\b$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int c = 0;
        static final /* synthetic */ int[] e;

        static {
            a = 1;
            int[] iArr = new int[e.values().length];
            e = iArr;
            try {
                iArr[e.b.ordinal()] = 1;
                int i = (c + 34) - 1;
                a = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[e.a.ordinal()] = 2;
                int i3 = c + 61;
                a = i3 % 128;
                switch (i3 % 2 == 0 ? 'C' : '0') {
                    case 'C':
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:38:0x00e5, code lost:
    
        if (r0[r1.d] == 1) goto L46;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 798
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dg.b.k(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    private static void l(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        int i5;
        boolean z2;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(b)};
            Object obj = o.e.a.s.get(-2120899312);
            char c2 = '0';
            if (obj == null) {
                Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) (TextUtils.lastIndexOf("", '0') + 1), 66 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)));
                byte b3 = (byte) 0;
                Object[] objArr3 = new Object[1];
                m(b3, (byte) (b3 | 42), b3, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            switch (intValue == -1 ? 'J' : (char) 0) {
                case 'J':
                    z = true;
                    break;
                default:
                    z = false;
                    break;
            }
            if (z) {
                byte[] bArr = f;
                if (bArr != null) {
                    int length = bArr.length;
                    byte[] bArr2 = new byte[length];
                    int i6 = 0;
                    while (true) {
                        switch (i6 < length ? 'G' : 'U') {
                            case Opcodes.CASTORE /* 85 */:
                                bArr = bArr2;
                                break;
                            default:
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr[i6])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(18 - TextUtils.lastIndexOf("", c2, 0), (char) (16425 - TextUtils.indexOf("", "", 0, 0)), (Process.myTid() >> 22) + Opcodes.FCMPG);
                                        byte b4 = (byte) 0;
                                        Object[] objArr5 = new Object[1];
                                        m(b4, (byte) (b4 | 44), b4, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr2[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i6++;
                                    c2 = '0';
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                        }
                    }
                }
                if (bArr != null) {
                    byte[] bArr3 = f;
                    try {
                        Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(a)};
                        Object obj3 = o.e.a.s.get(-2120899312);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(TextUtils.getOffsetBefore("", 0) + 11, (char) TextUtils.getTrimmedLength(""), 64 - ExpandableListView.getPackedPositionChild(0L));
                            byte b5 = (byte) 0;
                            Object[] objArr7 = new Object[1];
                            m(b5, (byte) (b5 | 42), b5, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-2120899312, obj3);
                        }
                        intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } else {
                    intValue = (short) (((short) (h[i2 + ((int) (a ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                }
            }
            if (intValue > 0) {
                int i7 = ((i2 + intValue) - 2) + ((int) (a ^ (-5810760824076169584L)));
                if (z) {
                    i5 = 1;
                } else {
                    int i8 = $10 + 13;
                    $11 = i8 % 128;
                    int i9 = i8 % 2;
                    i5 = 0;
                }
                fVar.d = i7 + i5;
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c(11 - View.getDefaultSize(0, 0), (char) (ViewConfiguration.getJumpTapTimeout() >> 16), 603 - TextUtils.indexOf("", "", 0, 0))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = f;
                    if (bArr4 != null) {
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        int i10 = 0;
                        while (i10 < length2) {
                            int i11 = $10 + Opcodes.LMUL;
                            $11 = i11 % 128;
                            switch (i11 % 2 == 0 ? 'X' : (char) 25) {
                                case Opcodes.POP2 /* 88 */:
                                    bArr5[i10] = (byte) (bArr4[i10] - 5810760824076169584L);
                                    i10 /= 0;
                                    break;
                                default:
                                    bArr5[i10] = (byte) (bArr4[i10] ^ (-5810760824076169584L));
                                    i10++;
                                    break;
                            }
                        }
                        bArr4 = bArr5;
                    }
                    if (bArr4 != null) {
                        int i12 = $11 + Opcodes.DMUL;
                        $10 = i12 % 128;
                        int i13 = i12 % 2;
                        z2 = true;
                    } else {
                        z2 = false;
                    }
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        switch (!z2) {
                            case false:
                                byte[] bArr6 = f;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                break;
                            default:
                                short[] sArr = h;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                break;
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                        int i14 = $10 + 91;
                        $11 = i14 % 128;
                        int i15 = i14 % 2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
