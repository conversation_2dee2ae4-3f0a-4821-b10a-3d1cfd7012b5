package o.fi;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.sql.Timestamp;
import kotlin.text.Typography;
import o.fc.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fi\a.smali */
public final class a extends o.fg.a {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static char[] b;
    private static int i;
    private static int j;
    private Timestamp d;
    private Timestamp e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        l();
        Process.getGidForName("");
        TextUtils.lastIndexOf("", '0');
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getTouchSlop();
        Process.getElapsedCpuTime();
        ViewConfiguration.getScrollFriction();
        ViewConfiguration.getKeyRepeatTimeout();
        TextUtils.lastIndexOf("", '0');
        View.resolveSizeAndState(0, 0, 0);
        Color.blue(0);
        TextUtils.getOffsetAfter("", 0);
        View.MeasureSpec.makeMeasureSpec(0, 0);
        SystemClock.elapsedRealtimeNanos();
        ViewConfiguration.getGlobalActionKeyTimeout();
        TextUtils.getCapsMode("", 0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        TextUtils.lastIndexOf("", '0', 0);
        TextUtils.getOffsetAfter("", 0);
        Color.alpha(0);
        ViewConfiguration.getDoubleTapTimeout();
        int i2 = i + 69;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{108, 119, -51, 110};
        $$e = Opcodes.INVOKEVIRTUAL;
    }

    static void l() {
        b = new char[]{11420, 2105, 26080, 17056, 48756, 39739, 61666, 11706, 2426, 26163, 17388, 47285, 37988, 11420, 2105, 26080, 17056, 48756, 39739, 61666, 11711, 2416, 26164, 17382, 47266, 38003, 61739, 12008, 2991, 11420, 2084, 26096, 17079, 48750, 39737, 61682, 11689, 2406, 26148, 17386, 47277, 38005, 61742, 12014, 3007, 26469, 23570, 47558, 38528, 11420, 2083, 26102, 38342, 45432, 56506, 64508, 1898, 8772, 18822, 38048, 45099, 57205, 64244, 449, 11632, 18513, 38803, 45735, 56882, 58700, 217, 12246, 19220, 38414, 48561, 55434, 23887, 31212, 5162, 13156, 53168, 60153, 33070, 23652, 30907, 11450, 2051, 26068, 17031, 48708, 39692, 11420, 2084, 26096, 17079, 48750, 39737, 61682, 11689, 2406, 26147, 17398, 47281, 38001};
        a = 3494148112662530167L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 102
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r0 = o.fi.a.$$d
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fi.a.p(int, short, byte, java.lang.Object[]):void");
    }

    public a(boolean z, c cVar, short s) {
        super(z, cVar, s);
    }

    public final void b(Timestamp timestamp) {
        int i2 = i + Opcodes.DREM;
        int i3 = i2 % 128;
        j = i3;
        char c = i2 % 2 != 0 ? Typography.dollar : '4';
        this.d = timestamp;
        switch (c) {
            case '4':
                break;
            default:
                int i4 = 34 / 0;
                break;
        }
        int i5 = i3 + 29;
        i = i5 % 128;
        switch (i5 % 2 == 0 ? ';' : 'H') {
            case ';':
                int i6 = 84 / 0;
                return;
            default:
                return;
        }
    }

    public final void e(Timestamp timestamp) {
        int i2 = j + 17;
        i = i2 % 128;
        boolean z = i2 % 2 == 0;
        this.e = timestamp;
        switch (z) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final Timestamp i() {
        int i2 = j + 27;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        Timestamp timestamp = this.d;
        int i5 = i3 + 67;
        j = i5 % 128;
        int i6 = i5 % 2;
        return timestamp;
    }

    public final Timestamp n() {
        int i2 = i + 93;
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        Timestamp timestamp = this.e;
        int i5 = i3 + 91;
        i = i5 % 128;
        int i6 = i5 % 2;
        return timestamp;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0038, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x001f, code lost:
    
        if (r5 != false) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0019, code lost:
    
        if (r5 != false) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0039, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0022, code lost:
    
        r4.d = new java.sql.Timestamp(java.lang.System.currentTimeMillis());
        r5 = o.fi.a.j + 75;
        o.fi.a.i = r5 % 128;
        r5 = r5 % 2;
     */
    @Override // o.fc.e, o.fc.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean e(java.lang.String r5, o.dd.e r6) {
        /*
            r4 = this;
            int r0 = o.fi.a.j
            int r0 = r0 + 21
            int r1 = r0 % 128
            o.fi.a.i = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            boolean r5 = super.e(r5, r6)
            switch(r0) {
                case 0: goto L19;
                default: goto L18;
            }
        L18:
            goto L1c
        L19:
            if (r5 == 0) goto L39
        L1b:
            goto L22
        L1c:
            r6 = 74
            int r6 = r6 / r2
            if (r5 == 0) goto L39
            goto L1b
        L22:
            java.sql.Timestamp r5 = new java.sql.Timestamp
            long r2 = java.lang.System.currentTimeMillis()
            r5.<init>(r2)
            r4.d = r5
            int r5 = o.fi.a.j
            int r5 = r5 + 75
            int r6 = r5 % 128
            o.fi.a.i = r6
            int r5 = r5 % 2
            return r1
        L39:
            return r2
        L3a:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fi.a.e(java.lang.String, o.dd.e):boolean");
    }

    /* renamed from: o.fi.a$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fi\a$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int a;
        private static int b = 0;
        static final /* synthetic */ int[] d;

        static {
            a = 1;
            int[] iArr = new int[c.values().length];
            d = iArr;
            try {
                iArr[c.b.ordinal()] = 1;
                int i = b;
                int i2 = ((i | Opcodes.LUSHR) << 1) - (i ^ Opcodes.LUSHR);
                a = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                d[c.a.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 13) + ((i3 & 13) << 1);
                a = i4 % 128;
                if (i4 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[c.e.ordinal()] = 3;
                int i5 = b;
                int i6 = ((i5 | 57) << 1) - (i5 ^ 57);
                a = i6 % 128;
                if (i6 % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[c.c.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[c.d.ordinal()] = 5;
                int i7 = (b + 32) - 1;
                a = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:19:0x0081, code lost:
    
        r14 = r1;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.eg.b d(java.util.Date r14) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 398
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fi.a.d(java.util.Date):o.eg.b");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void o(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 608
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fi.a.o(char, int, int, java.lang.Object[]):void");
    }
}
