package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p7.smali */
public class p7 extends u {
    private s0 b;
    private d x;

    public p7(e0 e0Var) {
        if (e0Var.size() != 2) {
            throw new IllegalArgumentException("Bad sequence size: " + e0Var.size());
        }
        Enumeration j = e0Var.j();
        this.b = s0.a(j.nextElement());
        this.x = d.a(j.nextElement());
    }

    public static p7 a(Object obj) {
        if (obj instanceof p7) {
            return (p7) obj;
        }
        if (obj != null) {
            return new p7(e0.a(obj));
        }
        return null;
    }

    public s0 e() {
        return this.b;
    }

    public d f() {
        return this.x;
    }

    public b0 g() throws IOException {
        return b0.a(this.x.i());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(this.b);
        iVar.a(this.x);
        return new j2(iVar);
    }
}
