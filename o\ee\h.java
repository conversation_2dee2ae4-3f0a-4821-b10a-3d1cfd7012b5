package o.ee;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.util.Address;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\h.smali */
public final class h {
    private static int g = 0;
    private static int i = 1;
    private final String a;
    private final String b;
    private final String c;
    private final String d;
    private final String e;
    private final String j;

    public h(Address address) {
        this.c = address.getLine1();
        this.d = address.getLine2();
        this.b = address.getAdministrativeArea();
        this.a = address.getCountryCode();
        this.e = address.getLocality();
        this.j = address.getPostalCode();
    }

    public final String d() {
        int i2 = i;
        int i3 = ((i2 | 61) << 1) - (i2 ^ 61);
        int i4 = i3 % 128;
        g = i4;
        int i5 = i3 % 2;
        String str = this.c;
        int i6 = (i4 & 79) + (i4 | 79);
        i = i6 % 128;
        int i7 = i6 % 2;
        return str;
    }

    public final String a() {
        int i2 = i;
        int i3 = (i2 & 25) + (i2 | 25);
        int i4 = i3 % 128;
        g = i4;
        int i5 = i3 % 2;
        String str = this.d;
        int i6 = (i4 ^ Opcodes.DMUL) + ((i4 & Opcodes.DMUL) << 1);
        i = i6 % 128;
        int i7 = i6 % 2;
        return str;
    }

    public final String b() {
        int i2 = g;
        int i3 = (i2 ^ Opcodes.DSUB) + ((i2 & Opcodes.DSUB) << 1);
        i = i3 % 128;
        switch (i3 % 2 == 0 ? '3' : ')') {
            case ')':
                String str = this.b;
                int i4 = (i2 ^ 35) + ((i2 & 35) << 1);
                i = i4 % 128;
                int i5 = i4 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String e() {
        int i2 = i;
        int i3 = (i2 ^ 79) + ((i2 & 79) << 1);
        g = i3 % 128;
        switch (i3 % 2 != 0 ? 'H' : '?') {
            case 'H':
                throw null;
            default:
                return this.a;
        }
    }

    public final String c() {
        int i2 = g + 3;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        String str = this.e;
        int i5 = (i3 & 59) + (i3 | 59);
        g = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String h() {
        int i2 = i;
        int i3 = (i2 & 89) + (i2 | 89);
        g = i3 % 128;
        int i4 = i3 % 2;
        String str = this.j;
        int i5 = i2 + 11;
        g = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }
}
