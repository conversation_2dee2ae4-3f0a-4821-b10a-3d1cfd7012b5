package o.cf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\c.smali */
public final class c extends Exception {
    private final a c;
    private static int e = 0;
    private static int b = 1;

    public c(a aVar, String str) {
        super(str);
        this.c = aVar;
    }

    public c(a aVar) {
        this(aVar, aVar.name());
    }

    public final a c() {
        int i = e;
        int i2 = (i + 28) - 1;
        b = i2 % 128;
        int i3 = i2 % 2;
        a aVar = this.c;
        int i4 = (i & 75) + (i | 75);
        b = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 24 / 0;
                return aVar;
            default:
                return aVar;
        }
    }
}
