package o.dy;

import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dy\d.smali */
public final class d implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final d a;
    private static char b;
    private static int c;
    private static long d;
    private static int e;
    private static int f;

    static void d() {
        b = (char) 49814;
        c = 161105445;
        d = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 3
            int r7 = 4 - r7
            byte[] r0 = o.dy.d.$$a
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r9 = 106 - r9
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r9 = r8
            r3 = r1
            r4 = r2
            r8 = r7
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L19:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1d:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L36:
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.d.h(int, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{78, -3, -72, 11};
        $$b = 240;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        f = 1;
        d();
        a = new d();
        int i = f + 109;
        e = i % 128;
        switch (i % 2 != 0 ? '@' : '3') {
            case '@':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private d() {
    }

    @Override // o.dy.e
    public final String e() {
        Object obj;
        int i = e + 59;
        f = i % 128;
        switch (i % 2 == 0 ? 'W' : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                Object[] objArr = new Object[1];
                g(ExpandableListView.getPackedPositionType(0L), "\ue772\ue3d6뜘\ue857\ue960ꤑ㋆\ue110堣З\uda4bꔕ", (char) (KeyEvent.getMaxKeyCode() >> 16), "谵戶땠쥮", "\u0000\u0000\u0000\u0000", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                g(ExpandableListView.getPackedPositionType(0L), "\ue772\ue3d6뜘\ue857\ue960ꤑ㋆\ue110堣З\uda4bꔕ", (char) (KeyEvent.getMaxKeyCode() / Opcodes.ISHL), "谵戶땠쥮", "\u0000\u0000\u0000\u0000", objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = f + 83;
        e = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        g(((byte) KeyEvent.getModifierMetaStateMask()) + 948614952, "遪宭藉䰺퀭\ud7fc᳆ᇎ쨆얰줺褅\ua7c0ᄠ\uf5a3\ud82b莼▇헾죡臨쩃鈷爙\uf361䯵欶洿\uf647匪⣩䩦븂\uee20\uf629烤뾣\ue43f˫뻸벳ⴢ榵\udc0b쑻㆝➗\uf2fc῾\u0fe1л헲\udded娋隋﮳ᅮ鯉짗᰷밡\ue63d笤\ua7cd⠁", (char) (4117 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), "✕誷ᔸ搐", "\u0000\u0000\u0000\u0000", objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(e()).append('\'').append('}').toString();
        int i = e + 17;
        f = i % 128;
        switch (i % 2 == 0 ? (char) 11 : '9') {
            case 11:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0020. Please report as an issue. */
    private static void g(int r18, java.lang.String r19, char r20, java.lang.String r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 686
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.d.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
