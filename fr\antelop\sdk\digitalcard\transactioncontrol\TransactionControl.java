package fr.antelop.sdk.digitalcard.transactioncontrol;

import o.es.a;
import o.es.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControl.smali */
public final class TransactionControl<T> {
    private final a<T> innerTransactionControl;

    public TransactionControl(a<T> aVar) {
        this.innerTransactionControl = aVar;
    }

    public final Boolean isSupported() {
        a<T> aVar = this.innerTransactionControl;
        if (aVar == null) {
            return Boolean.FALSE;
        }
        return aVar.b();
    }

    public final T getValue() {
        if (!isSupported().booleanValue()) {
            return null;
        }
        return this.innerTransactionControl.d();
    }

    public final TransactionControlUpdate<T> setValue(T t) {
        if (!isSupported().booleanValue()) {
            return null;
        }
        return new TransactionControlUpdate<>(new d(t, this.innerTransactionControl));
    }
}
