package o.ay;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import o.a.o;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ay\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final Object a;
    private static List<b> b;
    private static int c;
    private static int[] d;
    private static long e;
    private static int g;
    private static char i;
    private static int j;

    static void a() {
        d = new int[]{96745671, 1476666769, -1660717858, -1275734041, -751109165, 539246873, 842359144, -1817444463, -1256493711, -2009672240, -850790469, -858518545, 1791755652, 1669341111, -1484331375, 1385925910, 1450098997, 1403912947};
        i = (char) 17957;
        c = 161105445;
        e = 5923036836091299459L;
    }

    static void init$0() {
        $$a = new byte[]{3, 65, Tnaf.POW_2_WIDTH, 100};
        $$b = 88;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 4
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r9 = r9 + 99
            byte[] r0 = o.ay.a.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1d:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L34:
            int r9 = -r9
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ay.a.k(byte, int, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        g = 1;
        a();
        ExpandableListView.getPackedPositionGroup(0L);
        TextUtils.indexOf("", "");
        AudioTrack.getMaxVolume();
        a = new Object();
        int i2 = j + 29;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? '8' : '\n') {
            case '\n':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public a(Context context) {
        synchronized (a) {
            if (b == null) {
                b(context);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ay\a$b.smali */
    static final class b {
        int d;
        final String e;

        b(String str, int i) {
            this.e = str;
            this.d = i;
        }
    }

    public static void b(Context context, String str) {
        synchronized (a) {
            Iterator<b> it = b.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                if (it.next().e.equals(str)) {
                    it.remove();
                    d(context);
                    break;
                }
            }
        }
    }

    public static void c(Context context) {
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, 25 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(new int[]{-1952574053, -1728600241, -1465863179, 1244524630, -1823068276, 352987706}, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 10, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            b.clear();
            d(context);
        }
    }

    public static void a(Context context, String str) {
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, ExpandableListView.getPackedPositionType(0L) + 25, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            h(1070575015 - TextUtils.lastIndexOf("", '0', 0), "ꃌ났墯ሸ㚫癄ⷶ托\u1075ebⰻ琓濆ﭕ\ud8bb屰蠹櫛ӯ닾ꩂ\uf58f\u0098蕾Ꭓ䎱拶鷼\uf60f锠\ueefc禍勽\ude68ᝡ\u10c8⭻쥢ዥᡸ젋Ꮅ\ude22", (char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 10600), "ꣽ쾭栿ㄩ", "삦\ue75c䃘ब", objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
            b.add(0, new b(str, 0));
            d(context);
        }
    }

    public static String a(Context context) {
        String str;
        synchronized (a) {
            Iterator<b> it = b.iterator();
            boolean z = false;
            while (true) {
                if (!it.hasNext()) {
                    str = null;
                    break;
                }
                b next = it.next();
                if (next.d >= 5) {
                    g.c();
                    Object[] objArr = new Object[1];
                    f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, 24 - ImageFormat.getBitsPerPixel(0), objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    h(ViewConfiguration.getTapTimeout() >> 16, "\u09c5ꃠ\uf24d\uec44ꠍ\uda5e䨆គ눒ΐ경층蜀ࠔ츑ᑱ\uf5af茯蚔痗覩뽈剎㸪擞\u0cd2ㆰ칈찌\udf66튳䀕꘧㍓\ue517愧\ue7aa꽔\ue924덳煫㙐ឍ箱첶\uf829", (char) (ViewConfiguration.getFadingEdgeLength() >> 16), "㷥ᙏﾙ愂", "삦\ue75c䃘ब", objArr2);
                    StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(next.e);
                    Object[] objArr3 = new Object[1];
                    f(new int[]{161022440, -2095702930, 1504853200, -236495212, -1122300529, 1799215944, 1204832289, -440199757, -172095122, 942628343, 302265039, -579372325, 204123377, 543387034, -242546458, -1543028847, -713171913, -1541110697, 1417585747, -1704918849}, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 39, objArr3);
                    g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
                    it.remove();
                    z = true;
                } else {
                    g.c();
                    Object[] objArr4 = new Object[1];
                    f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, MotionEvent.axisFromString("") + 26, objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr5 = new Object[1];
                    h(TextUtils.indexOf((CharSequence) "", '0', 0) + 1, "Ἒ캿䴼ꯖ鞏㻬轳ꘉ儘ȳ\ue08c〹뼆쇫讚\u1942箑\uee18괈⩏ᾥ\ue450\uf186쿳锂╧\ue062솏惷᤹羴啅Ჳ෪셲，", (char) TextUtils.getCapsMode("", 0, 0), "㌗桺䢣蒂", "삦\ue75c䃘ब", objArr5);
                    g.d(intern2, sb2.append(((String) objArr5[0]).intern()).append(next.e).toString());
                    str = next.e;
                    break;
                }
            }
            if (z) {
                d(context);
            }
            if (str == null) {
                g.c();
                Object[] objArr6 = new Object[1];
                f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 25, objArr6);
                String intern3 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                h(Color.green(0), "볱簧禠\uf45fᖆꊜ渌嵭坽\udd9f\uf579䧕扄납퀖\uec8f쐤俧摿呱꘨桷㥓㥫\udf23꽆쾜Ợ⦮堑Ǵꐦꀐ찊ऊ\ue649", (char) ((Process.getThreadPriority(0) + 20) >> 6), "寤灆띜\ue560", "삦\ue75c䃘ब", objArr7);
                g.d(intern3, ((String) objArr7[0]).intern());
            }
        }
        return str;
    }

    public static void c(Context context, String str) {
        synchronized (a) {
            for (b bVar : b) {
                if (bVar.e.equals(str)) {
                    g.c();
                    Object[] objArr = new Object[1];
                    f(new int[]{-2037788210, -114175370, -1150743803, 420015294, -862197271, -81764926, -528174132, 1028043175, -1752379364, 715798937, -250874756, 476477916, 1566968229, 695496626}, TextUtils.getOffsetAfter("", 0) + 25, objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    h(1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "ꔽ챣纥ᤠ眸ṡꊇಪ騬ሻ①\udd19\uddb1떗暜纇鷔▛꽤鋚씸\udd05呯툍ꔿf㠯퇘螔任쑏瓵䤭蓷껵ᣛ", (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), "꼖⇓嗖⸌", "삦\ue75c䃘ब", objArr2);
                    g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
                    bVar.d++;
                    d(context);
                    return;
                }
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x0189, code lost:
    
        r3 = '0';
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void b(android.content.Context r21) {
        /*
            Method dump skipped, instructions count: 654
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ay.a.b(android.content.Context):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x00a9, code lost:
    
        r4 = r4 + 11;
        o.ay.a.g = r4 % 128;
        r4 = r4 % 2;
        r10.b(r12);
        r4 = 0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void d(android.content.Context r18) {
        /*
            Method dump skipped, instructions count: 444
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ay.a.d(android.content.Context):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 1016
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ay.a.f(int[], int, java.lang.Object[]):void");
    }

    private static void h(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] charArray;
        int i3 = $11 + 77;
        int i4 = i3 % 128;
        $10 = i4;
        int i5 = 2;
        Object obj = null;
        if (i3 % 2 != 0) {
            Object obj2 = null;
            obj2.hashCode();
            throw null;
        }
        if (str3 != null) {
            int i6 = i4 + 99;
            $11 = i6 % 128;
            if (i6 % 2 == 0) {
                cArr = str3.toCharArray();
                int i7 = 29 / 0;
            } else {
                cArr = str3.toCharArray();
            }
        } else {
            cArr = str3;
        }
        char[] cArr3 = cArr;
        if (str2 != null) {
            int i8 = $11 + 57;
            $10 = i8 % 128;
            switch (i8 % 2 != 0 ? 'E' : (char) 18) {
                case 'E':
                    str2.toCharArray();
                    throw null;
                default:
                    cArr2 = str2.toCharArray();
                    break;
            }
        } else {
            cArr2 = str2;
        }
        char[] cArr4 = cArr2;
        switch (str == null ? (char) 7 : '9') {
            case '9':
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i2));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i9 = $10 + 85;
            $11 = i9 % 128;
            int i10 = i9 % i5;
            try {
                Object[] objArr2 = {oVar};
                Object obj3 = o.e.a.s.get(-429442487);
                if (obj3 == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 10, (char) (KeyEvent.keyCodeFromString("") + 20954), 344 - KeyEvent.getDeadChar(0, 0));
                    byte b2 = (byte) 0;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    k(b2, b3, b3, objArr3);
                    obj3 = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj3);
                }
                int intValue = ((Integer) ((Method) obj3).invoke(obj, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj4 = o.e.a.s.get(-515165572);
                    if (obj4 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 9, (char) Gravity.getAbsoluteGravity(0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 207);
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        k(b4, b5, (byte) (b5 + 2), objArr5);
                        obj4 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj4);
                    }
                    int intValue2 = ((Integer) ((Method) obj4).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr5[oVar.e % 4] * 32718), Integer.valueOf(cArr6[intValue])};
                        Object obj5 = o.e.a.s.get(-1614232674);
                        if (obj5 == null) {
                            Class cls3 = (Class) o.e.a.c(11 - View.resolveSizeAndState(0, 0, 0), (char) Color.argb(0, 0, 0, 0), 281 - Color.red(0));
                            byte b6 = (byte) 0;
                            Object[] objArr7 = new Object[1];
                            k(b6, b6, (byte) $$a.length, objArr7);
                            obj5 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj6 = o.e.a.s.get(406147795);
                            if (obj6 == null) {
                                Class cls4 = (Class) o.e.a.c((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 18, (char) (((Process.getThreadPriority(0) + 20) >> 6) + 14687), 112 - ExpandableListView.getPackedPositionType(0L));
                                byte b7 = (byte) 0;
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                k(b7, b8, (byte) (b8 | 7), objArr9);
                                obj6 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj6);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj6).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r5[oVar.e]) ^ (e ^ 6565854932352255525L)) ^ ((int) (c ^ 6565854932352255525L))) ^ ((char) (i ^ 6565854932352255525L)));
                            oVar.e++;
                            int i11 = $10 + Opcodes.DMUL;
                            $11 = i11 % 128;
                            switch (i11 % 2 != 0) {
                                case false:
                                default:
                                    i5 = 2;
                                    obj = null;
                            }
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
