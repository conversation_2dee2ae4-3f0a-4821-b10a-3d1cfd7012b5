package com.esotericsoftware.kryo.util;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\IdentityMap.smali */
public class IdentityMap<K, V> extends ObjectMap<K, V> {
    public IdentityMap() {
    }

    public IdentityMap(int initialCapacity) {
        super(initialCapacity);
    }

    public IdentityMap(int initialCapacity, float loadFactor) {
        super(initialCapacity, loadFactor);
    }

    public IdentityMap(IdentityMap<K, V> map) {
        super(map);
    }

    @Override // com.esotericsoftware.kryo.util.ObjectMap
    protected int place(K item) {
        return System.identityHashCode(item) & this.mask;
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // com.esotericsoftware.kryo.util.ObjectMap
    public <T extends K> V get(T t) {
        int i = place(t);
        while (true) {
            K other = this.keyTable[i];
            if (other == null) {
                return null;
            }
            if (other == t) {
                return this.valueTable[i];
            }
            i = (i + 1) & this.mask;
        }
    }

    @Override // com.esotericsoftware.kryo.util.ObjectMap
    public V get(K key, V defaultValue) {
        int i = place(key);
        while (true) {
            K other = this.keyTable[i];
            if (other == null) {
                return defaultValue;
            }
            if (other == key) {
                return this.valueTable[i];
            }
            i = (i + 1) & this.mask;
        }
    }

    @Override // com.esotericsoftware.kryo.util.ObjectMap
    int locateKey(K key) {
        if (key == null) {
            throw new IllegalArgumentException("key cannot be null.");
        }
        K[] keyTable = this.keyTable;
        int i = place(key);
        while (true) {
            K other = keyTable[i];
            if (other == null) {
                return -(i + 1);
            }
            if (other == key) {
                return i;
            }
            i = (i + 1) & this.mask;
        }
    }

    @Override // com.esotericsoftware.kryo.util.ObjectMap
    public int hashCode() {
        int h = this.size;
        K[] keyTable = this.keyTable;
        V[] valueTable = this.valueTable;
        int n = keyTable.length;
        for (int i = 0; i < n; i++) {
            K key = keyTable[i];
            if (key != null) {
                h += System.identityHashCode(key);
                V value = valueTable[i];
                if (value != null) {
                    h += value.hashCode();
                }
            }
        }
        return h;
    }
}
