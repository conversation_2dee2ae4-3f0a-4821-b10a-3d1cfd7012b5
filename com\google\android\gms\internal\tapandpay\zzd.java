package com.google.android.gms.internal.tapandpay;

import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import com.google.android.gms.tapandpay.issuer.CreatePushProvisionSessionRequest;
import com.google.android.gms.tapandpay.issuer.IsTokenizedRequest;
import com.google.android.gms.tapandpay.issuer.PushTokenizeRequest;
import com.google.android.gms.tapandpay.issuer.ServerPushProvisionRequest;
import com.google.android.gms.tapandpay.issuer.ViewTokenRequest;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\tapandpay\zzd.smali */
public final class zzd extends zza implements IInterface {
    zzd(IBinder iBinder) {
        super(iBinder, "com.google.android.gms.tapandpay.internal.ITapAndPayService");
    }

    public final void zzd(CreatePushProvisionSessionRequest createPushProvisionSessionRequest, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzc(zza, createPushProvisionSessionRequest);
        zzc.zzd(zza, zzfVar);
        zzb(67, zza);
    }

    public final void zze(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(29, zza);
    }

    public final void zzf(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(21, zza);
    }

    public final void zzg(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(31, zza);
    }

    public final void zzh(String str, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zza.writeString(str);
        zzc.zzd(zza, zzfVar);
        zzb(61, zza);
    }

    public final void zzi(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(30, zza);
    }

    public final void zzj(int i, String str, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zza.writeInt(i);
        zza.writeString(str);
        zzc.zzd(zza, zzfVar);
        zzb(22, zza);
    }

    public final void zzk(IsTokenizedRequest isTokenizedRequest, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzc(zza, isTokenizedRequest);
        zzc.zzd(zza, zzfVar);
        zzb(75, zza);
    }

    public final void zzl(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(74, zza);
    }

    public final void zzm(PushTokenizeRequest pushTokenizeRequest, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzc(zza, pushTokenizeRequest);
        zzc.zzd(zza, zzfVar);
        zzb(28, zza);
    }

    public final void zzn(zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzd(zza, zzfVar);
        zzb(10, zza);
    }

    public final void zzo(int i, String str, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zza.writeInt(i);
        zza.writeString(str);
        zzc.zzd(zza, zzfVar);
        zzb(25, zza);
    }

    public final void zzp(int i, String str, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zza.writeInt(i);
        zza.writeString(str);
        zzc.zzd(zza, zzfVar);
        zzb(24, zza);
    }

    public final void zzq(ServerPushProvisionRequest serverPushProvisionRequest, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzc(zza, serverPushProvisionRequest);
        zzc.zzd(zza, zzfVar);
        zzb(68, zza);
    }

    public final void zzr(int i, String str, String str2, int i2, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zza.writeInt(i);
        zza.writeString(str);
        zza.writeString(str2);
        zza.writeInt(i2);
        zzc.zzd(zza, zzfVar);
        zzb(23, zza);
    }

    public final void zzs(ViewTokenRequest viewTokenRequest, zzf zzfVar) throws RemoteException {
        Parcel zza = zza();
        zzc.zzc(zza, viewTokenRequest);
        zzc.zzd(zza, zzfVar);
        zzb(79, zza);
    }
}
