package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.AbstractECLookupTable;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECLookupTable;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecT163R2Curve.smali */
public class SecT163R2Curve extends ECCurve.AbstractF2m {
    private static final ECFieldElement[] k = {new SecT163FieldElement(ECConstants.ONE)};
    protected SecT163R2Point j;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecT163R2Curve$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ long[] b;

        a(int i, long[] jArr) {
            this.a = i;
            this.b = jArr;
        }

        private ECPoint a(long[] jArr, long[] jArr2) {
            return SecT163R2Curve.this.a(new SecT163FieldElement(jArr), new SecT163FieldElement(jArr2), SecT163R2Curve.k);
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            long[] b = u5.b();
            long[] b2 = u5.b();
            int i2 = 0;
            for (int i3 = 0; i3 < this.a; i3++) {
                long j = ((i3 ^ i) - 1) >> 31;
                for (int i4 = 0; i4 < 3; i4++) {
                    long j2 = b[i4];
                    long[] jArr = this.b;
                    b[i4] = j2 ^ (jArr[i2 + i4] & j);
                    b2[i4] = b2[i4] ^ (jArr[(i2 + 3) + i4] & j);
                }
                i2 += 6;
            }
            return a(b, b2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            long[] b = u5.b();
            long[] b2 = u5.b();
            int i2 = i * 3 * 2;
            for (int i3 = 0; i3 < 3; i3++) {
                long[] jArr = this.b;
                b[i3] = jArr[i2 + i3];
                b2[i3] = jArr[i2 + 3 + i3];
            }
            return a(b, b2);
        }
    }

    public SecT163R2Curve() {
        super(Opcodes.IF_ICMPGT, 3, 6, 7);
        this.j = new SecT163R2Point(this, null, null);
        this.b = fromBigInteger(BigInteger.valueOf(1L));
        this.c = fromBigInteger(new BigInteger(1, z4.a("020A601907B8C953CA1481EB10512F78744A3205FD")));
        this.d = new BigInteger(1, z4.a("040000000000000000000292FE77E70C12A4234C33"));
        this.e = BigInteger.valueOf(2L);
        this.f = 6;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECCurve a() {
        return new SecT163R2Curve();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        long[] jArr = new long[i2 * 3 * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            u5.a(((SecT163FieldElement) eCPoint.getRawXCoord()).a, 0, jArr, i3);
            int i5 = i3 + 3;
            u5.a(((SecT163FieldElement) eCPoint.getRawYCoord()).a, 0, jArr, i5);
            i3 = i5 + 3;
        }
        return new a(i2, jArr);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement fromBigInteger(BigInteger bigInteger) {
        return new SecT163FieldElement(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public int getFieldSize() {
        return Opcodes.IF_ICMPGT;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECPoint getInfinity() {
        return this.j;
    }

    public int getK1() {
        return 3;
    }

    public int getK2() {
        return 6;
    }

    public int getK3() {
        return 7;
    }

    public int getM() {
        return Opcodes.IF_ICMPGT;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractF2m
    public boolean isKoblitz() {
        return false;
    }

    public boolean isTrinomial() {
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public boolean supportsCoordinateSystem(int i) {
        return i == 6;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return new SecT163R2Point(this, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        return new SecT163R2Point(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
