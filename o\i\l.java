package o.i;

import android.content.Context;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\l.smali */
public final class l extends g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static byte[] a;
    private static int b;
    private static int c;
    private static int d;
    private static short[] e;
    private static int f;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        d();
        ViewConfiguration.getScrollBarFadeDuration();
        ViewConfiguration.getMaximumFlingVelocity();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewConfiguration.getScrollBarFadeDuration();
        TextUtils.indexOf((CharSequence) "", '0');
        int i2 = i + Opcodes.DMUL;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void d() {
        a = new byte[]{ByteCompanionObject.MAX_VALUE, 88, -22, 89, -30, -11, 50, 82, -21, 88, -2, 83, 87, 88, -21, -28, 80, 89, 82, 25, 34, -32, 85, 83, 86, -20, 83, 31, 51, 90, 82, 12, 118, -11, -3, -4, 103, -29, 108, -1, -11, 34, 111, -7, 88, -32, -1, 106, 105, -34, -104, 100, -1, -112, -49, 123, 116, 77, -86, -85, 110, -82, -87, 114, -93, 93, -86, -90, -93, 114, 119, -85, -92, -87, 100, -71, 123, -88, -86, -91, 79, -86, 126};
        b = 909053601;
        d = -638985820;
        c = 1747675297;
    }

    static void init$0() {
        $$a = new byte[]{43, -103, 93, -106};
        $$b = 76;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 4
            int r8 = r8 * 2
            int r8 = 110 - r8
            byte[] r0 = o.i.l.$$a
            int r9 = r9 * 3
            int r9 = 1 - r9
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L1a
            r8 = r7
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L34:
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.l.n(byte, byte, int, java.lang.Object[]):void");
    }

    public l() {
        super(f.f);
    }

    @Override // o.i.g
    protected final o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        l((byte) ((Process.myPid() >> 22) + 93), View.resolveSize(0, 0) - 1577346609, (short) (TextUtils.getOffsetBefore("", 0) - 32), TextUtils.getTrimmedLength("") - 50, 272181530 + TextUtils.getOffsetBefore("", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) ((ViewConfiguration.getKeyRepeatDelay() >> 16) - 72), (ViewConfiguration.getPressedStateDuration() >> 16) - 1577346577, (short) (75 - TextUtils.lastIndexOf("", '0', 0)), (-50) - (ViewConfiguration.getEdgeSlop() >> 16), 272181550 - TextUtils.indexOf("", ""), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
        Object[] objArr3 = new Object[1];
        l((byte) ((-45) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), TextUtils.indexOf("", "") - 1577346554, (short) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 13), (ViewConfiguration.getScrollBarFadeDuration() >> 16) - 50, 272181518 - TextUtils.indexOf((CharSequence) "", '0'), objArr3);
        throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
    }

    /* JADX WARN: Code restructure failed: missing block: B:112:0x0356, code lost:
    
        r3 = r7;
     */
    /* JADX WARN: Code restructure failed: missing block: B:126:0x039b, code lost:
    
        if (r3 != false) goto L123;
     */
    /* JADX WARN: Code restructure failed: missing block: B:127:0x03c7, code lost:
    
        r4 = o.i.l.e;
        r0.d = r0.d - 1;
        r0.e = (char) (r0.b + (((short) (((short) (r4[r8] ^ (-5810760824076169584L))) + r21)) ^ r19));
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x03a3, code lost:
    
        r4 = o.i.l.a;
        r0.d = r0.d - 1;
        r0.e = (char) (r0.b + (((byte) (((byte) (r4[r8] ^ (-5810760824076169584L))) + r21)) ^ r19));
     */
    /* JADX WARN: Code restructure failed: missing block: B:136:0x03a1, code lost:
    
        if (r3 != false) goto L123;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1078
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.l.l(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
