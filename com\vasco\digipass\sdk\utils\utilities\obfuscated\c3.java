package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c3.smali */
class c3 extends z {
    c3(OutputStream outputStream) {
        super(outputStream);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.z
    void a(h[] hVarArr) throws IOException {
        for (h hVar : hVarArr) {
            hVar.toASN1Primitive().g().a(this, true);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.z
    c3 c() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.z
    void a(b0 b0Var, boolean z) throws IOException {
        b0Var.g().a(this, z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.z
    void a(b0[] b0VarArr) throws IOException {
        for (b0 b0Var : b0VarArr) {
            b0Var.g().a(this, true);
        }
    }
}
