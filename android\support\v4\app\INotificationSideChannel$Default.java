package android.support.v4.app;

import android.app.Notification;
import android.os.IBinder;
import android.os.RemoteException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\android\support\v4\app\INotificationSideChannel$Default.smali */
public class INotificationSideChannel$Default implements INotificationSideChannel {
    public void notify(String packageName, int id, String tag, Notification notification) throws RemoteException {
    }

    public void cancel(String packageName, int id, String tag) throws RemoteException {
    }

    public void cancelAll(String packageName) throws RemoteException {
    }

    public IBinder asBinder() {
        return null;
    }
}
