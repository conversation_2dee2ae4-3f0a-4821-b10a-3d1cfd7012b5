package o.bv;

import android.os.Process;
import android.util.TypedValue;
import android.view.View;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\c.smali */
public final class c implements o.ee.d<AntelopError> {
    private final int a;
    private final AntelopErrorCode b;
    private final String e;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int g = 0;
    private static int i = 1;
    private static char d = 10597;
    private static char h = 15319;
    private static char j = 11377;
    private static char c = 62440;

    @Override // o.ee.d
    public final /* synthetic */ AntelopError a() {
        int i2 = i + 41;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                AntelopError i3 = i();
                int i4 = g + Opcodes.DDIV;
                i = i4 % 128;
                int i5 = i4 % 2;
                return i3;
            default:
                i();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public c(AntelopErrorCode antelopErrorCode, int i2, String str) {
        this.b = antelopErrorCode;
        this.a = i2;
        this.e = str;
    }

    public c(AntelopErrorCode antelopErrorCode, String str) {
        this(antelopErrorCode, -1, str);
    }

    public c(AntelopErrorCode antelopErrorCode) {
        this(antelopErrorCode, -1, "");
    }

    public static c c(o.bb.d dVar) {
        int i2 = g + 73;
        i = i2 % 128;
        int i3 = i2 % 2;
        String e = dVar.e();
        switch (dVar.d() != o.bb.a.E) {
            case false:
                int i4 = g + 73;
                i = i4 % 128;
                int i5 = i4 % 2;
                Object[] objArr = new Object[1];
                f("Ơ츳歖䔳薲㡪හ줕Ơ츳\u0a7c䬐䗄\u2e60颌㘳\u0a7c䬐ﱦㄕ歖䔳ﰜ㊭죺来씲닅柛\ue937࿎מ\ueeac岖륿裋ꇆ\ue283冏뷓ꘘ犱뒿倇絿ᣖੜ\ud8a0ẶῙ\ued65긓ꏔ쎩ﱦㄕ䗄\u2e60\udd17숏ၨ䀃\u0a7c䬐ﱦㄕ", ExpandableListView.getPackedPositionChild(0L) + 67, objArr);
                e = ((String) objArr[0]).intern();
                break;
        }
        return new c(dVar.d().d(), dVar.d().b(), e);
    }

    public final AntelopErrorCode c() {
        int i2 = i + 39;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? '_' : 'N') {
            case 'N':
                return this.b;
            default:
                int i3 = 55 / 0;
                return this.b;
        }
    }

    public final int e() {
        int i2;
        int i3 = i + 7;
        int i4 = i3 % 128;
        g = i4;
        switch (i3 % 2 != 0 ? 'b' : ']') {
            case Opcodes.DUP2_X1 /* 93 */:
                i2 = this.a;
                break;
            default:
                i2 = this.a;
                int i5 = 94 / 0;
                break;
        }
        int i6 = i4 + 55;
        i = i6 % 128;
        int i7 = i6 % 2;
        return i2;
    }

    public final String b() {
        int i2 = g + 109;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? '`' : (char) 1) {
            case 1:
                return this.e;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        f("ꮃ႞簜ዺ顙ྔ", 6 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.b);
        Object[] objArr2 = new Object[1];
        f("\u0ecfᅅ祲㹃䆕\uf64cﱦㄕ燿༃", View.resolveSizeAndState(0, 0, 0) + 9, objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(this.a);
        Object[] objArr3 = new Object[1];
        f("\u0ecfᅅմට\uef79ྋߏ廂顙ྔ녖署", 10 - Process.getGidForName(""), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(this.e).append('\'').append('}').toString();
        int i2 = i + 55;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int i3 = 15 / 0;
                return obj;
            default:
                return obj;
        }
    }

    public final AntelopError d() {
        AntelopError antelopError = new AntelopError(this);
        int i2 = i + 67;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 4 : (char) 22) {
            case 4:
                throw null;
            default:
                return antelopError;
        }
    }

    private AntelopError i() {
        int i2 = i + 63;
        g = i2 % 128;
        int i3 = i2 % 2;
        AntelopError d2 = d();
        int i4 = i + 69;
        g = i4 % 128;
        int i5 = i4 % 2;
        return d2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 578
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.c.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
