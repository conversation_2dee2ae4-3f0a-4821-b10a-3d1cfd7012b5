package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import o.bv.c;
import o.er.o;
import o.g.b;
import o.p.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\PinUpdateService.smali */
public final class PinUpdateService {
    private final o innerPinUpdateService;

    public PinUpdateService(o oVar) {
        this.innerPinUpdateService = oVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerPinUpdateService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final SecurePinUpdate getSecurePinUpdate() throws WalletValidationException {
        return new SecurePinUpdate(this.innerPinUpdateService.e());
    }

    public final int getRemainingAttemptNumber() throws WalletValidationException {
        return this.innerPinUpdateService.e().a();
    }

    public final void updatePin(Context context, SecurePinInput securePinInput, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerPinUpdateService.e().d(context, securePinInput, new g() { // from class: fr.antelop.sdk.digitalcard.PinUpdateService.1
            @Override // o.p.g
            public void onProcessSuccess() {
                operationCallback.onSuccess(null);
            }

            @Override // o.p.g
            public void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
            }

            @Override // o.p.g
            public void onError(c cVar) {
                operationCallback.onError(cVar.d());
            }

            @Override // o.p.g
            public void onCustomerCredentialsInvalid(b bVar) {
            }

            @Override // o.p.g
            public void onProcessStart() {
            }

            @Override // o.p.g
            public void onAuthenticationDeclined() {
            }

            @Override // o.p.g
            public void abortPrompt() {
            }
        });
    }
}
