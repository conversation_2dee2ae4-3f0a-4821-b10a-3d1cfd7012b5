package o.v;

import android.content.Context;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.al.c;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\g.smali */
public final class g extends b {
    public static final byte[] $$j = null;
    public static final int $$k = 0;
    private static int $10;
    private static int $11;
    private static int h;
    private static char[] i;
    private static int k;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        k = 1;
        i = new char[]{50818, 50771, 50771, 50771, 50769, 50771, 50775, 50783, 50779, 50771, 50796, 50772, 50779, 50771, 50793, 50774, 50781, 50765, 50746, 50737, 51141, 51150, 51145, 50745, 50722, 51150, 51150, 51146, 51146, 50746, 50746, 50738, 51146, 51139};
    }

    static void init$0() {
        $$j = new byte[]{20, -38, -101, -62};
        $$k = Opcodes.INVOKESPECIAL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void y(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r8 = 122 - r8
            int r6 = r6 * 4
            int r6 = r6 + 1
            byte[] r0 = o.v.g.$$j
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            goto L34
        L17:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1b:
            byte r4 = (byte) r7
            int r8 = r8 + 1
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.g.y(int, int, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public g(boolean r6, o.eo.e r7, o.eo.f r8) {
        /*
            r5 = this;
            r0 = 48
            r1 = 2
            r2 = 0
            r3 = 17
            int[] r0 = new int[]{r2, r3, r0, r1}
            r1 = 1
            java.lang.Object[] r3 = new java.lang.Object[r1]
            java.lang.String r4 = "\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000"
            w(r4, r0, r1, r3)
            r0 = r3[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r5.<init>(r0, r7, r6, r8)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.g.<init>(boolean, o.eo.e, o.eo.f):void");
    }

    @Override // o.v.b
    public final c.e a() {
        int i2 = k + 55;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.dollar : ',') {
            case ',':
                return c.e.c;
            default:
                c.e eVar = c.e.c;
                throw null;
        }
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i2 = k + 77;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'S' : 'Y') {
            case Opcodes.DUP /* 89 */:
                Object[] objArr = new Object[1];
                w("\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001", new int[]{17, 17, Opcodes.IFLT, 12}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                w("\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001", new int[]{17, 17, Opcodes.IFLT, 12}, true, objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    public final void b(Context context, o.p.g gVar) throws WalletValidationException {
        int i2 = k + Opcodes.LSHL;
        h = i2 % 128;
        int i3 = i2 % 2;
        d(context, gVar);
        int i4 = h + 87;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? '*' : 'B') {
            case 'B':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static void w(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i2;
        int i3;
        int i4;
        char[] cArr2;
        int i5;
        String str2 = str;
        int i6 = $11 + Opcodes.DSUB;
        $10 = i6 % 128;
        int i7 = i6 % 2;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        o.a.l lVar = new o.a.l();
        int i8 = 0;
        int i9 = iArr[0];
        int i10 = 1;
        int i11 = iArr[1];
        int i12 = iArr[2];
        int i13 = iArr[3];
        char[] cArr3 = i;
        char c = '0';
        switch (cArr3 != null) {
            case true:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i14 = 0;
                while (i14 < length) {
                    try {
                        Object[] objArr2 = new Object[i10];
                        objArr2[i8] = Integer.valueOf(cArr3[i14]);
                        Object obj = o.e.a.s.get(1951085128);
                        if (obj != null) {
                            cArr = cArr3;
                            i2 = length;
                        } else {
                            Class cls = (Class) o.e.a.c(TextUtils.indexOf("", c, i8, i8) + 12, (char) (ViewConfiguration.getJumpTapTimeout() >> 16), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 42);
                            byte b = (byte) i8;
                            byte b2 = (byte) (b - 1);
                            cArr = cArr3;
                            i2 = length;
                            Object[] objArr3 = new Object[1];
                            y(b, b2, (byte) (b2 + 3), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1951085128, obj);
                        }
                        cArr4[i14] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i14++;
                        cArr3 = cArr;
                        length = i2;
                        i8 = 0;
                        i10 = 1;
                        c = '0';
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        char[] cArr5 = new char[i11];
        System.arraycopy(cArr3, i9, cArr5, 0, i11);
        if (bArr2 != null) {
            char[] cArr6 = new char[i11];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i11 ? 'Z' : Typography.dollar) {
                    case '$':
                        cArr5 = cArr6;
                        break;
                    default:
                        switch (bArr2[lVar.d] == 1) {
                            case true:
                                int i15 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                    Object obj2 = o.e.a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(View.combineMeasuredStates(0, 0) + 11, (char) (Process.myPid() >> 22), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 448);
                                        byte b3 = (byte) 0;
                                        byte b4 = (byte) (b3 - 1);
                                        Object[] objArr5 = new Object[1];
                                        y(b3, b4, (byte) (b4 + 4), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj2);
                                    }
                                    cArr6[i15] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                int i16 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                    Object obj3 = o.e.a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0) + 11, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), 206 - MotionEvent.axisFromString(""));
                                        byte b5 = (byte) 0;
                                        byte b6 = (byte) (b5 - 1);
                                        Object[] objArr7 = new Object[1];
                                        y(b5, b6, (byte) (b6 + 1), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj3);
                                    }
                                    cArr6[i16] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                        c2 = cArr6[lVar.d];
                        try {
                            Object[] objArr8 = {lVar, lVar};
                            Object obj4 = o.e.a.s.get(-2112603350);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 11, (char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), 259 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                                byte b7 = (byte) 0;
                                byte b8 = (byte) (b7 - 1);
                                Object[] objArr9 = new Object[1];
                                y(b7, b8, (byte) (b8 & 56), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
        }
        switch (i13 > 0 ? '+' : (char) 16) {
            case 16:
                break;
            default:
                char[] cArr7 = new char[i11];
                System.arraycopy(cArr5, 0, cArr7, 0, i11);
                int i17 = i11 - i13;
                System.arraycopy(cArr7, 0, cArr5, i17, i13);
                System.arraycopy(cArr7, i13, cArr5, 0, i17);
                break;
        }
        if (z) {
            int i18 = $10 + 109;
            $11 = i18 % 128;
            switch (i18 % 2 == 0 ? '1' : ':') {
                case Opcodes.ASTORE /* 58 */:
                    cArr2 = new char[i11];
                    lVar.d = 0;
                    i5 = 1;
                    break;
                default:
                    cArr2 = new char[i11];
                    i5 = 1;
                    lVar.d = 1;
                    break;
            }
            while (lVar.d < i11) {
                cArr2[lVar.d] = cArr5[(i11 - lVar.d) - i5];
                lVar.d += i5;
                i5 = 1;
            }
            cArr5 = cArr2;
        }
        if (i12 > 0) {
            int i19 = 0;
            while (true) {
                lVar.d = i19;
                if (lVar.d < i11) {
                    int i20 = $10 + 19;
                    $11 = i20 % 128;
                    if (i20 % 2 == 0) {
                        cArr5[lVar.d] = (char) (cArr5[lVar.d] / iArr[2]);
                        i3 = lVar.d;
                        i4 = 0;
                    } else {
                        cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                        i3 = lVar.d;
                        i4 = 1;
                    }
                    i19 = i3 + i4;
                }
            }
        }
        objArr[0] = new String(cArr5);
    }
}
