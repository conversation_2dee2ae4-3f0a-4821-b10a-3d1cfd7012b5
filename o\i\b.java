package o.i;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int c;
    private static int e;
    private final f a;
    private final d d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        d();
        int i = c + 51;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        b = new char[]{50932, 50854, 50849, 50851, 50848, 50857, 50834, 50853, 50858, 50855, 50851, 50836, 50846, 50852, 50854, 50851, 50849, 50836, 50832, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50836, 50854, 50852, 50851, 50876, 50878, 50873, 50833, 51148, 51148, 51150, 50746, 51151, 51150, 50736, 51141, 51145, 50740, 50746, 51137, 50708, 51151, 51150, 51146, 50707, 50809, 50700, 50809, 50740, 51141, 50744, 50741, 51145, 50724, 51141, 50736};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 66
            byte[] r0 = o.i.b.$$a
            int r6 = r6 * 4
            int r6 = r6 + 4
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r6 = r6 + 1
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.b.g(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{117, 56, 99, 31};
        $$b = Opcodes.ARETURN;
    }

    public b(d dVar, f fVar) {
        this.d = dVar;
        this.a = fVar;
    }

    public final void a(Context context, o.eg.b bVar) {
        int i = e + 43;
        c = i % 128;
        switch (i % 2 == 0 ? '@' : 'F') {
            case '@':
                int i2 = 48 / 0;
                if (bVar == null) {
                    return;
                }
                break;
            default:
                if (bVar == null) {
                    return;
                }
                break;
        }
        o.eg.b e2 = this.d.e(context);
        try {
            e2.d(this.a.toString(), bVar);
            int i3 = c + 25;
            e = i3 % 128;
            int i4 = i3 % 2;
            this.d.b(context, e2);
            int i5 = e + 71;
            c = i5 % 128;
            switch (i5 % 2 == 0) {
                case false:
                    return;
                default:
                    throw null;
            }
        } catch (o.eg.d e3) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000", new int[]{0, 40, 0, 0}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(null, new int[]{40, 28, Opcodes.LCMP, 4}, true, objArr2);
            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e3);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:33:0x00d4  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 748
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.b.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
