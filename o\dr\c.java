package o.dr;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.transaction.hce.HceTransactionStatus;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\c.smali */
public final class c implements o.ee.a<HceTransactionStatus> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c b;
    public static final c c;
    public static final c d;
    public static final c e;
    private static int f;
    public static final c g;
    private static int h;
    private static final /* synthetic */ c[] i;
    private static int k;
    private static byte[] l;
    private static int m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static short[] f58o;
    private final int j;

    static void e() {
        l = new byte[]{123, 94, 13, 40, 97, 99, 99, 110, 20, 87, 41, 73, 86, 45, 80, 75, 47, 113, 112, -115, -63, -116, -2, 109, -89, 112, -23, 68, 91, 93, -17, 115, 113, 122, 114, 100, 124, 104, 123, ByteCompanionObject.MIN_VALUE, 112, 118, 72, 116, 118, 40, -108, 112, 91, 95, 33, 92, 89, 9, 113, -13, -56, 117, 97, -61, 101, 103};
        h = 909053623;
        n = 1335936710;
        f = -163547988;
    }

    static void init$0() {
        $$a = new byte[]{74, -5, -75, 11};
        $$b = 209;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(byte r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 110 - r6
            int r7 = r7 + 4
            byte[] r0 = o.dr.c.$$a
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.c.q(byte, int, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ c[] c() {
        c[] cVarArr;
        int i2 = m;
        int i3 = i2 + 35;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                cVarArr = new c[]{b, d, c, a, e, g};
                break;
            default:
                cVarArr = new c[114];
                cVarArr[1] = b;
                cVarArr[0] = d;
                cVarArr[3] = c;
                cVarArr[2] = a;
                cVarArr[5] = e;
                cVarArr[3] = g;
                break;
        }
        int i4 = i2 + 1;
        k = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return cVarArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static c valueOf(String str) {
        int i2 = m + 85;
        k = i2 % 128;
        int i3 = i2 % 2;
        c cVar = (c) Enum.valueOf(c.class, str);
        int i4 = m + Opcodes.DSUB;
        k = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public static c[] values() {
        int i2 = k + 41;
        m = i2 % 128;
        int i3 = i2 % 2;
        c[] cVarArr = (c[]) i.clone();
        int i4 = k + 29;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return cVarArr;
            default:
                int i5 = 72 / 0;
                return cVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = m + 79;
        k = i2 % 128;
        int i3 = i2 % 2;
        HceTransactionStatus d2 = d();
        int i4 = m + 83;
        k = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 31 : (char) 30) {
            case 30:
                return d2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        m = 1;
        e();
        Object[] objArr = new Object[1];
        p((byte) (View.combineMeasuredStates(0, 0) - 97), 1066436054 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) (73 - KeyEvent.getDeadChar(0, 0)), (-40) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 2039465991, objArr);
        b = new c(((String) objArr[0]).intern(), 0, 1);
        Object[] objArr2 = new Object[1];
        p((byte) (Color.alpha(0) + 37), Color.argb(0, 0, 0, 0) + 1066436061, (short) (KeyEvent.getDeadChar(0, 0) + 89), Color.blue(0) - 40, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 2039465986, objArr2);
        d = new c(((String) objArr2[0]).intern(), 1, 2);
        Object[] objArr3 = new Object[1];
        p((byte) (3 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), 1066436068 - View.MeasureSpec.getMode(0), (short) (18 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), (-40) - KeyEvent.keyCodeFromString(""), TextUtils.indexOf("", "") - 2039466002, objArr3);
        c = new c(((String) objArr3[0]).intern(), 2, 3);
        Object[] objArr4 = new Object[1];
        p((byte) ((-113) - Color.blue(0)), 1066436077 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) (TextUtils.indexOf("", "", 0, 0) - 87), (-39) - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (-2039465988) - TextUtils.lastIndexOf("", '0', 0, 0), objArr4);
        a = new c(((String) objArr4[0]).intern(), 3, 4);
        Object[] objArr5 = new Object[1];
        p((byte) (122 - (ViewConfiguration.getFadingEdgeLength() >> 16)), 1066436083 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (short) (TextUtils.indexOf("", "", 0, 0) - 70), (-40) - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 2039466003, objArr5);
        e = new c(((String) objArr5[0]).intern(), 4, 5);
        Object[] objArr6 = new Object[1];
        p((byte) (65 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 1066436090 + (ViewConfiguration.getTapTimeout() >> 16), (short) (TextUtils.indexOf("", "", 0) + 91), TextUtils.indexOf((CharSequence) "", '0', 0) - 39, (-2039465988) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr6);
        g = new c(((String) objArr6[0]).intern(), 5, 6);
        i = c();
        int i2 = m + 1;
        k = i2 % 128;
        int i3 = i2 % 2;
    }

    private c(String str, int i2, int i3) {
        this.j = i3;
    }

    public final int b() {
        int i2 = m;
        int i3 = i2 + 5;
        k = i3 % 128;
        Object obj = null;
        switch (i3 % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                int i4 = this.j;
                int i5 = i2 + 59;
                k = i5 % 128;
                switch (i5 % 2 != 0 ? Typography.quote : (char) 20) {
                    case 20:
                        return i4;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.dr.c e(int r7) {
        /*
            int r0 = o.dr.c.m
            int r0 = r0 + 117
            int r1 = r0 % 128
            o.dr.c.k = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L15
            o.dr.c[] r0 = values()
            int r3 = r0.length
            r4 = r1
            goto L1b
        L15:
            o.dr.c[] r0 = values()
            int r3 = r0.length
            r4 = r2
        L1b:
            if (r4 >= r3) goto L20
            r5 = 67
            goto L22
        L20:
            r5 = 10
        L22:
            switch(r5) {
                case 10: goto L2c;
                default: goto L25;
            }
        L25:
            r5 = r0[r4]
            int r6 = r5.j
            if (r6 != r7) goto L42
            goto L40
        L2c:
            int r7 = o.dr.c.k
            int r7 = r7 + 53
            int r0 = r7 % 128
            o.dr.c.m = r0
            int r7 = r7 % 2
            r0 = 0
            if (r7 != 0) goto L3f
            r7 = 82
            int r7 = r7 / r2
            return r0
        L3d:
            r7 = move-exception
            throw r7
        L3f:
            return r0
        L40:
            r6 = r2
            goto L43
        L42:
            r6 = r1
        L43:
            switch(r6) {
                case 1: goto L47;
                default: goto L46;
            }
        L46:
            return r5
        L47:
            int r4 = r4 + 1
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.c.e(int):o.dr.c");
    }

    /* renamed from: o.dr.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int c;

        static {
            b = 0;
            c = 1;
            int[] iArr = new int[c.values().length];
            a = iArr;
            try {
                iArr[c.b.ordinal()] = 1;
                int i = c + 9;
                b = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                a[c.d.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[c.c.ordinal()] = 3;
                int i3 = c;
                int i4 = (i3 & 65) + (i3 | 65);
                b = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[c.a.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[c.e.ordinal()] = 5;
                int i6 = (b + 22) - 1;
                c = i6 % 128;
                if (i6 % 2 != 0) {
                }
            } catch (NoSuchFieldError e5) {
            }
            try {
                a[c.g.ordinal()] = 6;
                int i7 = c + 61;
                b = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    public final HceTransactionStatus d() {
        int i2 = m + 67;
        k = i2 % 128;
        int i3 = i2 % 2;
        switch (AnonymousClass4.a[ordinal()]) {
            case 1:
                return HceTransactionStatus.OnGoing;
            case 2:
                return HceTransactionStatus.Unknown;
            case 3:
                return HceTransactionStatus.Declined;
            case 4:
                return HceTransactionStatus.Success;
            case 5:
                HceTransactionStatus hceTransactionStatus = HceTransactionStatus.Cleared;
                int i4 = m + 59;
                k = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        return hceTransactionStatus;
                    default:
                        throw null;
                }
            case 6:
                return HceTransactionStatus.Reversed;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                p((byte) (16 - (ViewConfiguration.getScrollBarSize() >> 8)), View.MeasureSpec.getSize(0) + 1066436036, (short) (40 - View.MeasureSpec.getMode(0)), (-40) - View.getDefaultSize(0, 0), (-2039465986) - Process.getGidForName(""), objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:75:0x02a2, code lost:
    
        if (r4 != false) goto L78;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:73:0x0290. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r20, int r21, short r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1028
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dr.c.p(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
