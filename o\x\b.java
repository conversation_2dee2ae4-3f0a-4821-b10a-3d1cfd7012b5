package o.x;

import com.esotericsoftware.asm.Opcodes;
import java.util.List;
import o.i.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\x\b.smali */
public abstract class b {
    private static int a = 0;
    private static int c = 1;
    private List<g> b;
    private final boolean d;

    public b(boolean z, List<g> list) {
        this.d = z;
        this.b = list;
    }

    public final boolean c() {
        boolean z;
        int i = c;
        int i2 = (i & 15) + (i | 15);
        int i3 = i2 % 128;
        a = i3;
        switch (i2 % 2 != 0 ? (char) 29 : (char) 28) {
            case 28:
                z = this.d;
                break;
            default:
                z = this.d;
                int i4 = 53 / 0;
                break;
        }
        int i5 = ((i3 | 3) << 1) - (i3 ^ 3);
        c = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    public final int hashCode() {
        int i = c;
        int i2 = ((i | Opcodes.DDIV) << 1) - (i ^ Opcodes.DDIV);
        a = i2 % 128;
        switch (i2 % 2 != 0 ? 'b' : '%') {
            case '%':
                return super.hashCode();
            default:
                super.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = a + 99;
        c = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = c;
        int i4 = (i3 & 45) + (i3 | 45);
        a = i4 % 128;
        int i5 = i4 % 2;
        return equals;
    }

    public final String toString() {
        int i = a;
        int i2 = (i ^ 15) + ((i & 15) << 1);
        c = i2 % 128;
        int i3 = i2 % 2;
        String obj = super.toString();
        int i4 = a + 31;
        c = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 40 / 0;
                return obj;
            default:
                return obj;
        }
    }

    protected final void finalize() throws Throwable {
        int i = (a + 22) - 1;
        c = i % 128;
        int i2 = i % 2;
        super.finalize();
        int i3 = c;
        int i4 = ((i3 | Opcodes.LNEG) << 1) - (i3 ^ Opcodes.LNEG);
        a = i4 % 128;
        int i5 = i4 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
