package o.et;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\i.smali */
public final class i extends h {
    private static int a = 0;
    private static int b;
    private static long c;
    private static char d;
    private static final byte[] e;
    private static int j;

    static void b() {
        d = (char) 55999;
        b = 161105445;
        c = 6565854932352255525L;
    }

    @Override // o.et.h, o.el.d
    public final /* synthetic */ o.ey.e a(String str) {
        int i = j + 25;
        a = i % 128;
        int i2 = i % 2;
        o.ew.b h = h(str);
        int i3 = a + 43;
        j = i3 % 128;
        int i4 = i3 % 2;
        return h;
    }

    @Override // o.et.h
    public final /* synthetic */ o.ey.b c(String str) {
        int i = j + 71;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                h(str);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                o.ew.b h = h(str);
                int i2 = j + 95;
                a = i2 % 128;
                int i3 = i2 % 2;
                return h;
        }
    }

    static {
        j = 1;
        b();
        TextUtils.indexOf("", "");
        ViewConfiguration.getPressedStateDuration();
        e = new byte[]{-1, -1, -1, -1, -1, -1};
        int i = a + Opcodes.DSUB;
        j = i % 128;
        int i2 = i % 2;
    }

    public i(String str, String str2, int i, String str3) {
        super(str, str2, i, str3);
        b(o.dp.b.e);
        h((byte[]) e.clone());
    }

    @Override // o.et.h, o.et.c
    protected final o.et.c c(String str, String str2, int i, String str3) {
        i iVar = new i(str, str2, i, str3);
        int i2 = a + 61;
        j = i2 % 128;
        int i3 = i2 % 2;
        return iVar;
    }

    private o.ew.b h(String str) {
        o.ew.b bVar = new o.ew.b(n(), str, false);
        bVar.d(f());
        int i = j + Opcodes.LSUB;
        a = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    @Override // o.et.h, o.et.c
    public final EmvApplicationType e() {
        int i = a + 77;
        j = i % 128;
        int i2 = i % 2;
        EmvApplicationType emvApplicationType = EmvApplicationType.HceMdes;
        int i3 = j + 27;
        a = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return emvApplicationType;
            default:
                int i4 = 23 / 0;
                return emvApplicationType;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\i$c.smali */
    public static final class c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static c a;
        private static c b;
        private static c c;
        private static c d;
        private static c e;
        private static final /* synthetic */ c[] f;
        private static int g;
        private static int h;
        private static int i;
        private static byte[] j;
        private static short[] l;
        private static int m;

        /* renamed from: o, reason: collision with root package name */
        private static int f81o;

        static void c() {
            j = new byte[]{32, 58, 52, 8, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 56, 52, 10, 14, -63, -82, -36, -89, -61, -82, -42, -44, -62, -74, -93, -116, -90, -114, -67, -79, -94, -69, -67, -73, -77, -112, -112, -112, -112, -112};
            i = 909053690;
            g = -674730592;
            h = 1528848607;
        }

        static void init$0() {
            $$a = new byte[]{43, 59, -40, 18};
            $$b = 65;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void n(int r6, short r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 4
                int r7 = r7 + 4
                byte[] r0 = o.et.i.c.$$a
                int r8 = r8 * 2
                int r8 = r8 + 1
                int r6 = r6 * 2
                int r6 = 110 - r6
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L1b
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L1b:
                r3 = r2
            L1c:
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r8) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r8
                r8 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r6 = r6 + r8
                int r7 = r7 + 1
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1c
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.c.n(int, short, short, java.lang.Object[]):void");
        }

        private c(String str, int i2) {
        }

        private static /* synthetic */ c[] b() {
            int i2 = f81o + 59;
            int i3 = i2 % 128;
            m = i3;
            int i4 = i2 % 2;
            c[] cVarArr = {e, d, a, b, c};
            int i5 = i3 + 65;
            f81o = i5 % 128;
            int i6 = i5 % 2;
            return cVarArr;
        }

        public static c valueOf(String str) {
            int i2 = m + Opcodes.LSHL;
            f81o = i2 % 128;
            int i3 = i2 % 2;
            c cVar = (c) Enum.valueOf(c.class, str);
            int i4 = m + 13;
            f81o = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    return cVar;
                default:
                    int i5 = 7 / 0;
                    return cVar;
            }
        }

        public static c[] values() {
            int i2 = f81o + 63;
            m = i2 % 128;
            switch (i2 % 2 != 0 ? '/' : 'A') {
                case '/':
                    throw null;
                default:
                    c[] cVarArr = (c[]) f.clone();
                    int i3 = f81o + 71;
                    m = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            int i4 = 34 / 0;
                            return cVarArr;
                        default:
                            return cVarArr;
                    }
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            m = 0;
            f81o = 1;
            c();
            Object[] objArr = new Object[1];
            k((byte) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (-1829727823) - Color.red(0), (short) (90 - Process.getGidForName("")), (-99) - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 504929556 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
            e = new c(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            k((byte) (TextUtils.lastIndexOf("", '0') + 1), (-1829727818) - View.getDefaultSize(0, 0), (short) (99 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), (-102) - MotionEvent.axisFromString(""), View.combineMeasuredStates(0, 0) + 504929556, objArr2);
            d = new c(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            k((byte) View.MeasureSpec.getMode(0), (-1829727814) - Color.argb(0, 0, 0, 0), (short) (View.resolveSizeAndState(0, 0, 0) - 70), (-96) - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 504929555 - TextUtils.getCapsMode("", 0, 0), objArr3);
            a = new c(((String) objArr3[0]).intern(), 2);
            Object[] objArr4 = new Object[1];
            k((byte) (ViewConfiguration.getScrollBarSize() >> 8), (-1829727805) + (ViewConfiguration.getTouchSlop() >> 8), (short) (Color.red(0) - 43), Color.red(0) - 99, 504929568 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
            b = new c(((String) objArr4[0]).intern(), 3);
            Object[] objArr5 = new Object[1];
            k((byte) ((-1) - TextUtils.lastIndexOf("", '0')), Gravity.getAbsoluteGravity(0, 0) - 1829727799, (short) (TextUtils.lastIndexOf("", '0', 0) - 41), (-99) - (ViewConfiguration.getFadingEdgeLength() >> 16), 504929572 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr5);
            c = new c(((String) objArr5[0]).intern(), 4);
            f = b();
            int i2 = m + 61;
            f81o = i2 % 128;
            int i3 = i2 % 2;
        }

        /* JADX WARN: Code restructure failed: missing block: B:101:0x0373, code lost:
        
            r8 = 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:102:0x0374, code lost:
        
            if (r8 >= r4) goto L143;
         */
        /* JADX WARN: Code restructure failed: missing block: B:103:0x0376, code lost:
        
            r7[r8] = (byte) (r3[r8] ^ (-5810760824076169584L));
            r8 = r8 + 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:105:0x0386, code lost:
        
            r3 = o.et.i.c.$11 + com.esotericsoftware.asm.Opcodes.DSUB;
            o.et.i.c.$10 = r3 % 128;
            r3 = r3 % 2;
            r3 = r7;
         */
        /* JADX WARN: Code restructure failed: missing block: B:127:0x02d6, code lost:
        
            r7 = 1;
         */
        /* JADX WARN: Code restructure failed: missing block: B:129:0x02d4, code lost:
        
            if (r10 != false) goto L89;
         */
        /* JADX WARN: Code restructure failed: missing block: B:87:0x02c3, code lost:
        
            if (r10 != false) goto L89;
         */
        /* JADX WARN: Code restructure failed: missing block: B:88:0x02d8, code lost:
        
            r7 = 0;
         */
        /* JADX WARN: Failed to find 'out' block for switch in B:85:0x02b1. Please report as an issue. */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 1080
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.c.k(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\i$d.smali */
    public static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static d b;
        private static final /* synthetic */ d[] c;
        private static d d;
        private static d e;
        private static int f;
        private static byte[] g;
        private static int h;
        private static int i;
        private static short[] j;
        private static int m;

        static void c() {
            g = new byte[]{59, -86, -84, -74, -94, -90, 58, 27, 31, 9, 5, 60, 42, -103, ByteCompanionObject.MIN_VALUE, -98, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 40};
            h = 909053643;
            i = 2142691853;
            a = 937740300;
        }

        static void init$0() {
            $$a = new byte[]{79, 74, -126, -127};
            $$b = 45;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r6, int r7, short r8, java.lang.Object[] r9) {
            /*
                int r8 = r8 * 2
                int r8 = 110 - r8
                byte[] r0 = o.et.i.d.$$a
                int r7 = r7 * 2
                int r7 = r7 + 4
                int r6 = r6 * 2
                int r6 = 1 - r6
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r8
                r4 = r2
                r8 = r7
                goto L2e
            L19:
                r3 = r2
            L1a:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                r3 = r0[r7]
                r5 = r8
                r8 = r7
                r7 = r5
            L2e:
                int r7 = r7 + r3
                int r8 = r8 + 1
                r3 = r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.d.l(short, int, short, java.lang.Object[]):void");
        }

        private d(String str, int i2) {
        }

        private static /* synthetic */ d[] d() {
            int i2 = m + 23;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            d[] dVarArr = {b, d, e};
            int i5 = i3 + 19;
            m = i5 % 128;
            int i6 = i5 % 2;
            return dVarArr;
        }

        public static d valueOf(String str) {
            int i2 = m + Opcodes.DMUL;
            f = i2 % 128;
            int i3 = i2 % 2;
            d dVar = (d) Enum.valueOf(d.class, str);
            int i4 = m + Opcodes.DREM;
            f = i4 % 128;
            int i5 = i4 % 2;
            return dVar;
        }

        public static d[] values() {
            int i2 = m + 7;
            f = i2 % 128;
            int i3 = i2 % 2;
            d[] dVarArr = (d[]) c.clone();
            int i4 = f + 35;
            m = i4 % 128;
            int i5 = i4 % 2;
            return dVarArr;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            m = 1;
            c();
            Object[] objArr = new Object[1];
            k((byte) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) - 116), (-30131868) - (ViewConfiguration.getKeyRepeatDelay() >> 16), (short) (View.resolveSizeAndState(0, 0, 0) + 77), (-92) - View.combineMeasuredStates(0, 0), (-1234824282) + (Process.myTid() >> 22), objArr);
            b = new d(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            k((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.IAND), TextUtils.indexOf("", "") - 30131862, (short) (TextUtils.getTrimmedLength("") - 23), Gravity.getAbsoluteGravity(0, 0) - 92, Color.blue(0) - 1234824281, objArr2);
            d = new d(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            k((byte) ((-81) - View.MeasureSpec.getMode(0)), (-30131857) + (ViewConfiguration.getJumpTapTimeout() >> 16), (short) (((Process.getThreadPriority(0) + 20) >> 6) - 98), ((byte) KeyEvent.getModifierMetaStateMask()) - 91, (-1234824265) - TextUtils.lastIndexOf("", '0', 0, 0), objArr3);
            e = new d(((String) objArr3[0]).intern(), 2);
            c = d();
            int i2 = m + 55;
            f = i2 % 128;
            int i3 = i2 % 2;
        }

        /* JADX WARN: Code restructure failed: missing block: B:105:0x0237, code lost:
        
            r3 = 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:109:0x0219, code lost:
        
            if (r4 != false) goto L62;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 900
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.d.k(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\i$e.smali */
    public static final class e {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        private static e b;
        private static e c;
        private static final /* synthetic */ e[] d;
        private static e e;
        private static char f;
        private static int i;
        private static int j;

        static void b() {
            a = new char[]{17047, 30542, 30532, 17040, 30552, 30539, 30550, 30544, 30556, 17046, 17053, 30553, 30538, 17043, 30537, 30531, 30557, 17044, 30551, 17042, 30530, 30541, 30534, 17041, 30540};
            f = (char) 17040;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(byte r7, int r8, int r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.et.i.e.$$a
                int r9 = r9 + 69
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r7 = r7 * 3
                int r7 = 4 - r7
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L18
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r8
                goto L33
            L18:
                r3 = r2
            L19:
                int r7 = r7 + 1
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                if (r4 != r8) goto L2a
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2a:
                r3 = r0[r7]
                r6 = r9
                r9 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r6
            L33:
                int r8 = -r8
                int r8 = r8 + r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r6 = r9
                r9 = r8
                r8 = r6
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.e.h(byte, int, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
            $$b = Opcodes.LSUB;
        }

        private e(String str, int i2) {
        }

        private static /* synthetic */ e[] e() {
            e[] eVarArr;
            int i2 = i + 51;
            int i3 = i2 % 128;
            j = i3;
            switch (i2 % 2 != 0 ? (char) 29 : '0') {
                case 29:
                    eVarArr = new e[5];
                    eVarArr[0] = e;
                    eVarArr[1] = b;
                    eVarArr[4] = c;
                    break;
                default:
                    eVarArr = new e[]{e, b, c};
                    break;
            }
            int i4 = i3 + 47;
            i = i4 % 128;
            int i5 = i4 % 2;
            return eVarArr;
        }

        public static e valueOf(String str) {
            int i2 = i + 27;
            j = i2 % 128;
            int i3 = i2 % 2;
            e eVar = (e) Enum.valueOf(e.class, str);
            int i4 = i + 39;
            j = i4 % 128;
            int i5 = i4 % 2;
            return eVar;
        }

        public static e[] values() {
            int i2 = j + Opcodes.DNEG;
            i = i2 % 128;
            int i3 = i2 % 2;
            e[] eVarArr = (e[]) d.clone();
            int i4 = j + 43;
            i = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return eVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            i = 1;
            b();
            Object[] objArr = new Object[1];
            g((ViewConfiguration.getKeyRepeatDelay() >> 16) + 12, "\u0014\t\u0015\u000e\u0016\u0005\u0000\u0010\u0000\u0002\u0007\t", (byte) (Color.blue(0) + 26), objArr);
            e = new e(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            g(14 - Color.red(0), "\n\u0013\r\u0011\u0017\u0016\u0011\n\t\u0016\t\u0014\n\u0015", (byte) (TextUtils.indexOf("", "", 0, 0) + 86), objArr2);
            b = new e(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            g((ViewConfiguration.getScrollBarSize() >> 8) + 9, "\u0015\u0004\u000f\u0006\u0005\u0011\u0002\u0007㘛", (byte) ((ViewConfiguration.getTapTimeout() >> 16) + 60), objArr3);
            c = new e(((String) objArr3[0]).intern(), 2);
            d = e();
            int i2 = j + 11;
            i = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Failed to find 'out' block for switch in B:50:0x015e. Please report as an issue. */
        private static void g(int r24, java.lang.String r25, byte r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 1080
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.e.g(int, java.lang.String, byte, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\i$a.smali */
    public static final class a {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static a a;
        private static a b;
        private static a c;
        private static final /* synthetic */ a[] d;
        private static int[] e;
        private static int g;
        private static int i;

        static void d() {
            e = new int[]{-1723754046, -1598403172, -1219397356, 569696476, 2120702515, -1730981691, 1300220330, -490180550, -480839301, 95003559, -50430056, -2037877705, 947237288, -428717143, -1561220450, 860615977, 802912472, 2017681812};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(int r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.et.i.a.$$a
                int r6 = 116 - r6
                int r7 = r7 * 4
                int r7 = 1 - r7
                int r8 = r8 * 3
                int r8 = 4 - r8
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L34
            L1a:
                r3 = r2
            L1b:
                byte r4 = (byte) r6
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r7) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                r3 = r0[r8]
                r5 = r7
                r7 = r6
                r6 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r5
            L34:
                int r6 = r6 + r7
                int r7 = r9 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.a.h(int, byte, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{21, -38, 51, PSSSigner.TRAILER_IMPLICIT};
            $$b = 1;
        }

        private a(String str, int i2) {
        }

        private static /* synthetic */ a[] a() {
            a[] aVarArr;
            int i2 = i;
            int i3 = i2 + 3;
            g = i3 % 128;
            switch (i3 % 2 != 0 ? '-' : (char) 22) {
                case '-':
                    aVarArr = new a[4];
                    aVarArr[1] = b;
                    aVarArr[0] = c;
                    aVarArr[5] = a;
                    break;
                default:
                    aVarArr = new a[]{b, c, a};
                    break;
            }
            int i4 = i2 + Opcodes.LUSHR;
            g = i4 % 128;
            int i5 = i4 % 2;
            return aVarArr;
        }

        public static a valueOf(String str) {
            int i2 = g + 99;
            i = i2 % 128;
            boolean z = i2 % 2 != 0;
            a aVar = (a) Enum.valueOf(a.class, str);
            switch (z) {
                case true:
                    return aVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public static a[] values() {
            a[] aVarArr;
            int i2 = i + Opcodes.LSHR;
            g = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    aVarArr = (a[]) d.clone();
                    break;
                default:
                    aVarArr = (a[]) d.clone();
                    int i3 = 86 / 0;
                    break;
            }
            int i4 = g + 23;
            i = i4 % 128;
            int i5 = i4 % 2;
            return aVarArr;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            g = 0;
            i = 1;
            d();
            Object[] objArr = new Object[1];
            f(new int[]{-878935822, -2003568988, -1228522746, 1479568955, -2072071638, 1473325286, 925982270, -1499383027, 811013691, 193455263, -1231884091, -128111337, -780630071, 1661274242}, AndroidCharacter.getMirror('0') - 21, objArr);
            b = new a(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            f(new int[]{154937772, 994884391, 977665283, 943225764, 2073177514, -991253191, 778347628, -1996660422, -385812948, -2142451418, 692022940, 433644366, 1422352665, 1480471771}, 25 - TextUtils.getOffsetBefore("", 0), objArr2);
            c = new a(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            f(new int[]{154937772, 994884391, 977665283, 943225764, 2073177514, -991253191, 778347628, -1996660422, -1437393546, -2030922196, -809725934, -1667332533, 1355438555, -505415285}, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 26, objArr3);
            a = new a(((String) objArr3[0]).intern(), 2);
            d = a();
            int i2 = i + 79;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(int[] r24, int r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 998
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.et.i.a.f(int[], int, java.lang.Object[]):void");
        }
    }
}
