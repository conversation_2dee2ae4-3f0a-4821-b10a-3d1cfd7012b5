package com.vasco.digipass.sdk.utils.securestorage.model;

import com.google.firebase.messaging.Constants;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u0012\n\u0002\b\u000b\u0018\u00002\u00020\u0001B\u0019\b\u0000\u0012\u0006\u0010\u0007\u001a\u00020\u0002\u0012\u0006\u0010\n\u001a\u00020\u0002¢\u0006\u0004\b\u000b\u0010\fR\u0017\u0010\u0007\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0003\u0010\u0004\u001a\u0004\b\u0005\u0010\u0006R\u0017\u0010\n\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\b\u0010\u0004\u001a\u0004\b\t\u0010\u0006¨\u0006\r"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/model/IVEncryptedData;", "", "", "a", "[B", "getIv", "()[B", "iv", "b", "getData", Constants.ScionAnalytics.MessageType.DATA_MESSAGE, "<init>", "([B[B)V", "lib_release"}, k = 1, mv = {1, 8, 0})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\model\IVEncryptedData.smali */
public final class IVEncryptedData {

    /* renamed from: a, reason: from kotlin metadata */
    public final byte[] iv;

    /* renamed from: b, reason: from kotlin metadata */
    public final byte[] data;

    public IVEncryptedData(byte[] iv, byte[] data) {
        Intrinsics.checkNotNullParameter(iv, "iv");
        Intrinsics.checkNotNullParameter(data, "data");
        this.iv = iv;
        this.data = data;
    }

    public final byte[] getData() {
        return this.data;
    }

    public final byte[] getIv() {
        return this.iv;
    }
}
