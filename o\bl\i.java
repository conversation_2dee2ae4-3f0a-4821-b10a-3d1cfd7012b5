package o.bl;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.j;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\i.smali */
public final class i implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static int c;
    private static boolean d;
    private static boolean e;
    private static int f;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        f = 1;
        g();
        PointF.length(0.0f, 0.0f);
        int i = a + 71;
        f = i % 128;
        int i2 = i % 2;
    }

    static void g() {
        b = new char[]{61584, 61585, 61596, 61591, 61603, 61593, 61590, 61589, 61606, 61575, 61599, 61808, 61605, 61598, 61815, 61588, 61595, 61600, 61570, 61604};
        d = true;
        e = true;
        c = 782102834;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = r6 + 117
            byte[] r0 = o.bl.i.$$a
            int r7 = r7 * 3
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = -r7
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.i.i(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{126, -85, 96, -58};
        $$b = Opcodes.TABLESWITCH;
    }

    @Override // o.bl.a
    public final void c(Context context) {
        int i = a + 1;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 5 / 0;
                break;
        }
    }

    @Override // o.bl.a
    public final String e() {
        int i = f + 3;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = i2 + 47;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return null;
            default:
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0054, code lost:
    
        if (((java.lang.String) r0[0]).intern().isEmpty() != false) goto L19;
     */
    @Override // o.bl.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String b() {
        /*
            r10 = this;
            int r0 = o.bl.i.f
            int r0 = r0 + 87
            int r1 = r0 % 128
            o.bl.i.a = r1
            int r0 = r0 % 2
            r1 = 82
            if (r0 == 0) goto L10
            r0 = r1
            goto L12
        L10:
            r0 = 56
        L12:
            r2 = 1
            java.lang.String r3 = "\u0088\u0089\u0083\u0088\u0087\u0086\u0084\u0084\u0085\u0084\u0083\u0082\u0081"
            r4 = 0
            r5 = 0
            switch(r0) {
                case 82: goto L38;
                default: goto L1a;
            }
        L1a:
            long r6 = android.view.ViewConfiguration.getZoomControlsTimeout()
            r8 = 0
            int r0 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            int r0 = r0 + 126
            java.lang.Object[] r6 = new java.lang.Object[r2]
            h(r5, r0, r5, r3, r6)
            r0 = r6[r4]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r0.isEmpty()
            if (r0 == 0) goto L58
            goto L57
        L38:
            long r0 = android.view.ViewConfiguration.getZoomControlsTimeout()
            r6 = 1
            int r0 = (r0 > r6 ? 1 : (r0 == r6 ? 0 : -1))
            r1 = 67
            int r1 = r1 % r0
            java.lang.Object[] r0 = new java.lang.Object[r2]
            h(r5, r1, r5, r3, r0)
            r0 = r0[r4]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r0.isEmpty()
            if (r0 == 0) goto L5c
        L56:
            goto L7a
        L57:
            r1 = 6
        L58:
            switch(r1) {
                case 82: goto L5c;
                default: goto L5b;
            }
        L5b:
            goto L56
        L5c:
            int r0 = android.graphics.Color.green(r4)
            int r0 = r0 + 127
            java.lang.Object[] r1 = new java.lang.Object[r2]
            h(r5, r0, r5, r3, r1)
            r0 = r1[r4]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r1 = o.bl.i.a
            int r1 = r1 + 35
            int r2 = r1 % 128
            o.bl.i.f = r2
            int r1 = r1 % 2
            return r0
        L7a:
            int r0 = o.bl.i.f
            int r0 = r0 + 19
            int r1 = r0 % 128
            o.bl.i.a = r1
            int r0 = r0 % 2
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.i.b():java.lang.String");
    }

    @Override // o.bl.a
    public final o.bi.a a() {
        int i = f + 71;
        int i2 = i % 128;
        a = i2;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                int i3 = i2 + 99;
                f = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.bl.a
    public final o.bi.d c() {
        int i = f + 89;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 0 : 'N') {
            case 'N':
                return null;
            default:
                throw null;
        }
    }

    @Override // o.bl.a
    public final String d() {
        Object obj;
        int i = f + 29;
        a = i % 128;
        switch (i % 2 != 0 ? 64 : 2) {
            case true:
                Object[] objArr = new Object[1];
                h(null, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 127, null, "\u0092\u0088\u0087\u0086\u0094\u0085\u0092\u0093\u0083\u0085\u0086\u0089\u0082\u0092\u008d\u0091\u0086\u0090\u0083\u0085\u008f\u0087\u008e\u0086\u008d\u008c\u008b\u0087\u008a", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                h(null, (ViewConfiguration.getMaximumFlingVelocity() >> 50) * 86, null, "\u0092\u0088\u0087\u0086\u0094\u0085\u0092\u0093\u0083\u0085\u0086\u0089\u0082\u0092\u008d\u0091\u0086\u0090\u0083\u0085\u008f\u0087\u008e\u0086\u008d\u008c\u008b\u0087\u008a", objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = f + Opcodes.LSHR;
        a = i2 % 128;
        switch (i2 % 2 != 0 ? '-' : '@') {
            case '@':
                return intern;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x0026. Please report as an issue. */
    private static void h(String str, int i, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        String str3 = str2;
        byte[] bArr = str3;
        if (str3 != null) {
            bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        if (str != null) {
            cArr = str.toCharArray();
            int i2 = $11 + 69;
            $10 = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 17 : (char) 28) {
            }
        } else {
            cArr = str;
        }
        char[] cArr2 = cArr;
        j jVar = new j();
        char[] cArr3 = b;
        int i3 = 0;
        switch (cArr3 != null ? (char) 3 : 'B') {
            case 3:
                int i4 = $11 + 51;
                $10 = i4 % 128;
                int i5 = i4 % 2;
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i6 = 0;
                while (true) {
                    switch (i6 < length ? '=' : 'U') {
                        case Opcodes.CASTORE /* 85 */:
                            cArr3 = cArr4;
                            break;
                        default:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i3] = Integer.valueOf(cArr3[i6]);
                                Object obj = o.e.a.s.get(1085633688);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c((ViewConfiguration.getTapTimeout() >> 16) + 11, (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), TextUtils.indexOf("", "", i3, i3) + 493);
                                    byte length2 = (byte) $$a.length;
                                    byte b2 = (byte) (length2 - 4);
                                    Object[] objArr3 = new Object[1];
                                    i(length2, b2, b2, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1085633688, obj);
                                }
                                cArr4[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i6++;
                                i3 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                    }
                }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(c)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(10 - ((Process.getThreadPriority(0) + 20) >> 6), (char) (KeyEvent.getDeadChar(0, 0) + 8856), KeyEvent.normalizeMetaState(0) + 324);
                byte b3 = (byte) 1;
                byte b4 = (byte) (b3 - 1);
                Object[] objArr5 = new Object[1];
                i(b3, b4, b4, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            if (e) {
                int i7 = $11 + 77;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                jVar.e = bArr2.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    int i9 = $10 + 69;
                    $11 = i9 % 128;
                    if (i9 % 2 == 0) {
                        cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e * 1) >> jVar.c] >> i] % intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj3 = o.e.a.s.get(745816316);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - (ViewConfiguration.getFadingEdgeLength() >> 16), (char) Gravity.getAbsoluteGravity(0, 0), Process.getGidForName("") + 208);
                                byte b5 = (byte) 0;
                                byte b6 = b5;
                                Object[] objArr7 = new Object[1];
                                i(b5, b6, b6, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } else {
                        cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0', 0), (char) ((-1) - MotionEvent.axisFromString("")), 207 - TextUtils.getCapsMode("", 0, 0));
                                byte b7 = (byte) 0;
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                i(b7, b8, b8, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                }
                objArr[0] = new String(cArr5);
                return;
            }
            if (d) {
                jVar.e = cArr2.length;
                char[] cArr6 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i] - intValue);
                    try {
                        Object[] objArr10 = {jVar, jVar};
                        Object obj5 = o.e.a.s.get(745816316);
                        if (obj5 == null) {
                            Class cls5 = (Class) o.e.a.c(11 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), Color.alpha(0) + 207);
                            byte b9 = (byte) 0;
                            byte b10 = b9;
                            Object[] objArr11 = new Object[1];
                            i(b9, b10, b10, objArr11);
                            obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr10);
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                String str4 = new String(cArr6);
                int i10 = $10 + 3;
                $11 = i10 % 128;
                int i11 = i10 % 2;
                objArr[0] = str4;
                return;
            }
            jVar.e = iArr.length;
            char[] cArr7 = new char[jVar.e];
            jVar.c = 0;
            while (true) {
                switch (jVar.c < jVar.e ? 'Y' : '+') {
                    case '+':
                        objArr[0] = new String(cArr7);
                        return;
                    default:
                        cArr7[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                        jVar.c++;
                }
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
