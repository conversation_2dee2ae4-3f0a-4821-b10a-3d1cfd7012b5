package o.fc;

import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fc\d.smali */
public abstract class d {
    private static int c;
    private static int e;
    private static byte[] f;
    private static int h;
    private static int i;
    private static int j = 0;
    private final short a;
    private final boolean b;
    private c d;

    static {
        h = 1;
        g();
        TextUtils.indexOf("", "", 0);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ViewConfiguration.getMaximumDrawingCacheSize();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        SystemClock.elapsedRealtime();
        ViewConfiguration.getGlobalActionKeyTimeout();
        KeyEvent.normalizeMetaState(0);
        ViewConfiguration.getMaximumFlingVelocity();
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getDoubleTapTimeout();
        int i2 = j + 109;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                int i3 = 27 / 0;
                break;
        }
    }

    static void g() {
        f = new byte[]{-112, -109, -123, ByteCompanionObject.MAX_VALUE, -109, 68, 104, -112, -112};
        c = 909053574;
        i = -517560082;
        e = -1312978910;
    }

    public abstract short e();

    public abstract boolean e(String str, o.dd.e eVar);

    public d(boolean z, c cVar, short s) {
        this.b = z;
        this.d = cVar;
        this.a = s;
    }

    public final short c() {
        int i2 = j + Opcodes.LUSHR;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        short s = this.a;
        int i5 = i3 + 25;
        j = i5 % 128;
        int i6 = i5 % 2;
        return s;
    }

    public final c b() {
        int i2 = j + 1;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        c cVar = this.d;
        int i5 = i3 + 81;
        j = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 20 : 'B') {
            case 'B':
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void c(c cVar) {
        int i2 = h + 37;
        j = i2 % 128;
        char c2 = i2 % 2 != 0 ? 'B' : '%';
        this.d = cVar;
        switch (c2) {
            case 'B':
                int i3 = 47 / 0;
                return;
            default:
                return;
        }
    }

    public final boolean a() {
        switch (this.b) {
            case true:
                int i2 = j + Opcodes.LREM;
                h = i2 % 128;
                Object obj = null;
                switch (i2 % 2 == 0 ? '6' : (char) 11) {
                    case 11:
                        switch (this.d == c.b ? '#' : (char) 4) {
                            case '#':
                                int i3 = j + 31;
                                h = i3 % 128;
                                if (i3 % 2 != 0) {
                                    return true;
                                }
                                obj.hashCode();
                                throw null;
                        }
                    default:
                        c cVar = c.b;
                        throw null;
                }
            default:
                return false;
        }
    }

    public final boolean d() {
        int i2 = h + 61;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return this.b;
            default:
                int i3 = 9 / 0;
                return this.b;
        }
    }

    public final int hashCode() {
        int i2 = j + 55;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                super.hashCode();
                throw null;
            default:
                return super.hashCode();
        }
    }

    public final boolean equals(Object obj) {
        int i2 = h + 27;
        j = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = j + 91;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i2 = h + 27;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '/' : (char) 31) {
            case '/':
                super.toString();
                throw null;
            default:
                String obj = super.toString();
                int i3 = j + 95;
                h = i3 % 128;
                switch (i3 % 2 == 0 ? '\b' : ']') {
                    case Opcodes.DUP2_X1 /* 93 */:
                        return obj;
                    default:
                        throw null;
                }
        }
    }

    protected void finalize() throws Throwable {
        int i2 = h + 57;
        j = i2 % 128;
        boolean z = i2 % 2 != 0;
        super.finalize();
        switch (z) {
            case true:
                int i3 = 69 / 0;
                break;
        }
        int i4 = j + 87;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
