package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecT193FieldElement.smali */
public class SecT193FieldElement extends ECFieldElement.AbstractF2m {
    protected long[] a;

    public SecT193FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.bitLength() > 193) {
            throw new IllegalArgumentException("x value invalid for SecT193FieldElement");
        }
        this.a = SecT193Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        long[] b = w5.b();
        SecT193Field.add(this.a, ((SecT193FieldElement) eCFieldElement).a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        long[] b = w5.b();
        SecT193Field.addOne(this.a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        return multiply(eCFieldElement.invert());
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecT193FieldElement) {
            return w5.b(this.a, ((SecT193FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecT193Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Opcodes.INSTANCEOF;
    }

    public int getK1() {
        return 15;
    }

    public int getK2() {
        return 0;
    }

    public int getK3() {
        return 0;
    }

    public int getM() {
        return Opcodes.INSTANCEOF;
    }

    public int getRepresentation() {
        return 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public ECFieldElement halfTrace() {
        long[] b = w5.b();
        SecT193Field.halfTrace(this.a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public boolean hasFastTrace() {
        return true;
    }

    public int hashCode() {
        return Arrays.hashCode(this.a, 0, 4) ^ 1930015;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        long[] b = w5.b();
        SecT193Field.invert(this.a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return w5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return w5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        long[] b = w5.b();
        SecT193Field.multiply(this.a, ((SecT193FieldElement) eCFieldElement).a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        return multiplyPlusProduct(eCFieldElement, eCFieldElement2, eCFieldElement3);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT193FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT193FieldElement) eCFieldElement2).a;
        long[] jArr4 = ((SecT193FieldElement) eCFieldElement3).a;
        long[] d = w5.d();
        SecT193Field.multiplyAddToExt(jArr, jArr2, d);
        SecT193Field.multiplyAddToExt(jArr3, jArr4, d);
        long[] b = w5.b();
        SecT193Field.reduce(d, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        return this;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        long[] b = w5.b();
        SecT193Field.sqrt(this.a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        long[] b = w5.b();
        SecT193Field.square(this.a, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return squarePlusProduct(eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT193FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT193FieldElement) eCFieldElement2).a;
        long[] d = w5.d();
        SecT193Field.squareAddToExt(jArr, d);
        SecT193Field.multiplyAddToExt(jArr2, jArr3, d);
        long[] b = w5.b();
        SecT193Field.reduce(d, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePow(int i) {
        if (i < 1) {
            return this;
        }
        long[] b = w5.b();
        SecT193Field.squareN(this.a, i, b);
        return new SecT193FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        return add(eCFieldElement);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return (this.a[0] & 1) != 0;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return w5.c(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public int trace() {
        return SecT193Field.trace(this.a);
    }

    public SecT193FieldElement() {
        this.a = w5.b();
    }

    protected SecT193FieldElement(long[] jArr) {
        this.a = jArr;
    }
}
