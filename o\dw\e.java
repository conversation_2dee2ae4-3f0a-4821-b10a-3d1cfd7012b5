package o.dw;

import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dw\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static byte[] a;
    private static int b;
    private static short[] c;
    private static int d;
    private static int e;
    private static int f;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        a = new byte[]{-14, -8, 27, -27, -27, Tnaf.POW_2_WIDTH, -13, -11, 1, -42, 34, -26, 8, -25, 41, -20, 1, 8, 9, -2, -47, 43, 7, 9, -26, 10, -26, 27, 6, -27, -22, -5, -3, 14, 5, -22, 14, 33, 32, -65, 6, 6, -1, -16, 9, -11, 2, 33, -32, -9, -7, 22, -6, 22, 55, -65, -9, -7, 5, 23, -8, 27, 34, -30, 30, -72, -5, -11, 6, 7, -5, 4, -1, 6, 87, -88, 1, 37, 42, -9, 9, -88, 21, -6, 23, -33, 22, -8, 23, -10, -10, 21, 26, -43, 7, 3, 1, -15, 41, -55, 11, -6, -112, -112};
        d = 909053617;
        b = 1336192760;
        e = 72497253;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.dw.e.$$a
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = r7 + 108
            int r6 = r6 * 3
            int r6 = 3 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r6 = r6 + 1
            int r4 = r3 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = -r7
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.e.h(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{106, 35, -45, 57};
        $$b = 90;
    }

    public static Rect a() {
        int i = j + 17;
        f = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        g((byte) ((Process.myPid() >> 22) + 100), (-847066869) - (ViewConfiguration.getFadingEdgeLength() >> 16), (short) Drawable.resolveOpacity(0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) - 1, (-2039193637) + TextUtils.indexOf("", "", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((byte) ((-108) - ((Process.getThreadPriority(0) + 20) >> 6)), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 847066839, (short) (TextUtils.lastIndexOf("", '0') + 1), 39 - (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 2039193602, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = f + 93;
        j = i3 % 128;
        int i4 = i3 % 2;
        return null;
    }

    /* JADX WARN: Code restructure failed: missing block: B:53:0x01f5, code lost:
    
        if (r10 != false) goto L57;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x02ac, code lost:
    
        r3 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 846
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.e.g(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
