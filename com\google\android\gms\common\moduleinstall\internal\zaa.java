package com.google.android.gms.common.moduleinstall.internal;

import com.google.android.gms.common.api.Status;
import com.google.android.gms.common.moduleinstall.ModuleAvailabilityResponse;
import com.google.android.gms.common.moduleinstall.ModuleInstallIntentResponse;
import com.google.android.gms.common.moduleinstall.ModuleInstallResponse;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\moduleinstall\internal\zaa.smali */
public class zaa extends zad {
    @Override // com.google.android.gms.common.moduleinstall.internal.zae
    public void zab(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.common.moduleinstall.internal.zae
    public void zac(Status status, ModuleInstallIntentResponse moduleInstallIntentResponse) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.common.moduleinstall.internal.zae
    public void zad(Status status, ModuleInstallResponse moduleInstallResponse) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.common.moduleinstall.internal.zae
    public void zae(Status status, ModuleAvailabilityResponse moduleAvailabilityResponse) {
        throw new UnsupportedOperationException();
    }
}
