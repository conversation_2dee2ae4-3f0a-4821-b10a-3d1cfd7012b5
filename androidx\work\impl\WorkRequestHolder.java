package androidx.work.impl;

import androidx.work.WorkRequest;
import androidx.work.impl.model.WorkSpec;
import java.util.Set;
import java.util.UUID;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\WorkRequestHolder.smali */
public class WorkRequestHolder extends WorkRequest {
    public WorkRequestHolder(UUID id, WorkSpec workSpec, Set<String> tags) {
        super(id, workSpec, tags);
    }
}
