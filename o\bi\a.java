package o.bi;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bi\a.smali */
public final class a {
    private static int e = 0;
    private static int g = 1;
    private final String a;
    private final String b;
    private final String c;
    private final String d;

    public a(String str, String str2, String str3, String str4) {
        this.a = str;
        this.c = str2;
        this.b = str3;
        this.d = str4;
    }

    public final String e() {
        int i = (e + 10) - 1;
        g = i % 128;
        switch (i % 2 == 0 ? '(' : (char) 3) {
            case '(':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.a;
        }
    }

    public final String b() {
        String str;
        int i = e;
        int i2 = i + 9;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                str = this.c;
                int i3 = 69 / 0;
                break;
            default:
                str = this.c;
                break;
        }
        int i4 = (i ^ 69) + ((i & 69) << 1);
        g = i4 % 128;
        switch (i4 % 2 == 0 ? 'Z' : '7') {
            case '7':
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String d() {
        int i = g;
        int i2 = (i & 47) + (i | 47);
        e = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? '[' : 'Z') {
            case Opcodes.DUP_X2 /* 91 */:
                obj.hashCode();
                throw null;
            default:
                String str = this.b;
                int i3 = ((i | Opcodes.LREM) << 1) - (i ^ Opcodes.LREM);
                e = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return str;
                    default:
                        throw null;
                }
        }
    }

    public final String a() {
        int i = g;
        int i2 = (i + 58) - 1;
        e = i2 % 128;
        int i3 = i2 % 2;
        String str = this.d;
        int i4 = (i & 5) + (i | 5);
        e = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    /* JADX WARN: Code restructure failed: missing block: B:84:0x007d, code lost:
    
        if (getClass() != r9.getClass()) goto L100;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:55:0x0102. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:20:0x004d  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x002a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean equals(java.lang.Object r9) {
        /*
            Method dump skipped, instructions count: 376
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bi.a.equals(java.lang.Object):boolean");
    }
}
