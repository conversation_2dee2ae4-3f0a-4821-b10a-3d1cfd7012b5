package com.capacitorjs.plugins.device;

import android.os.Build;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import java.util.Locale;

@CapacitorPlugin(name = "Device")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\capacitorjs\plugins\device\DevicePlugin.smali */
public class DevicePlugin extends Plugin {
    private Device implementation;

    @Override // com.getcapacitor.Plugin
    public void load() {
        this.implementation = new Device(getContext());
    }

    @PluginMethod
    public void getId(PluginCall call) {
        JSObject r = new JSObject();
        r.put("identifier", this.implementation.getUuid());
        call.resolve(r);
    }

    @PluginMethod
    public void getInfo(PluginCall call) {
        JSObject r = new JSObject();
        r.put("memUsed", this.implementation.getMemUsed());
        r.put("diskFree", this.implementation.getDiskFree());
        r.put("diskTotal", this.implementation.getDiskTotal());
        r.put("realDiskFree", this.implementation.getRealDiskFree());
        r.put("realDiskTotal", this.implementation.getRealDiskTotal());
        r.put("model", Build.MODEL);
        r.put("operatingSystem", "android");
        r.put("osVersion", Build.VERSION.RELEASE);
        r.put("androidSDKVersion", Build.VERSION.SDK_INT);
        r.put("platform", this.implementation.getPlatform());
        r.put("manufacturer", Build.MANUFACTURER);
        r.put("isVirtual", this.implementation.isVirtual());
        r.put("name", this.implementation.getName());
        r.put("webViewVersion", this.implementation.getWebViewVersion());
        call.resolve(r);
    }

    @PluginMethod
    public void getBatteryInfo(PluginCall call) {
        JSObject r = new JSObject();
        r.put("batteryLevel", this.implementation.getBatteryLevel());
        r.put("isCharging", this.implementation.isCharging());
        call.resolve(r);
    }

    @PluginMethod
    public void getLanguageCode(PluginCall call) {
        JSObject ret = new JSObject();
        ret.put("value", Locale.getDefault().getLanguage());
        call.resolve(ret);
    }

    @PluginMethod
    public void getLanguageTag(PluginCall call) {
        JSObject ret = new JSObject();
        ret.put("value", Locale.getDefault().toLanguageTag());
        call.resolve(ret);
    }
}
