package com.vasco.digipass.sdk.utils.utilities.sc;

import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u7;
import com.vasco.digipass.sdk.utils.utilities.responses.UtilitiesSDKSecureChannelParseResponse;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\sc\UtilitiesSDKSecureChannelMessageParser.smali */
public class UtilitiesSDKSecureChannelMessageParser {
    private UtilitiesSDKSecureChannelMessageParser() {
    }

    private static void a(byte[] bArr, UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) throws UtilitiesSDKException {
        byte b = utilitiesSDKSecureChannelMessage.messageType;
        int i = b == 0 ? 54 : b == 1 ? 37 : b == 2 ? 17 : 15;
        byte b2 = utilitiesSDKSecureChannelMessage.protectionType;
        if (b2 == 1 || b2 == 17) {
            i += 8;
        }
        if (bArr.length < i) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH);
        }
        int length = (bArr.length - 15) - (b2 == 0 ? 0 : 8);
        byte[] bArr2 = new byte[length];
        System.arraycopy(bArr, 15, bArr2, 0, length);
        utilitiesSDKSecureChannelMessage.body = u7.a(bArr2);
    }

    private static void b(byte[] bArr, UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) {
        if (utilitiesSDKSecureChannelMessage.protectionType != 0) {
            byte[] bArr2 = new byte[8];
            System.arraycopy(bArr, bArr.length - 8, bArr2, 0, 8);
            utilitiesSDKSecureChannelMessage.authenticationTag = u7.a(bArr2);
        }
    }

    private static void c(byte[] bArr, UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage) throws UtilitiesSDKException {
        if (bArr.length < 15) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH);
        }
        int a = a(bArr, 0, 2);
        utilitiesSDKSecureChannelMessage.protocolVersion = (byte) (15 & (a >> 12));
        byte b = (byte) ((a >> 6) & 63);
        utilitiesSDKSecureChannelMessage.messageType = b;
        byte b2 = (byte) (a & 63);
        utilitiesSDKSecureChannelMessage.protectionType = b2;
        utilitiesSDKSecureChannelMessage.encrypted = b2 == 1;
        switch (b) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
                String a2 = a(a(bArr, 2, 2));
                if (!a2.matches("[0-9A-Z]+") || a2.length() != 3) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
                }
                int a3 = a(bArr, 4, 3);
                if (a3 < 0 || a3 > 9999999) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
                }
                String valueOf = String.valueOf(a3);
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < 7 - valueOf.length(); i++) {
                    sb.append("0");
                }
                if (!valueOf.matches("[0-9]+") || valueOf.length() + sb.length() != 7) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
                }
                utilitiesSDKSecureChannelMessage.serialNumber = a2 + ((Object) sb) + valueOf;
                byte[] bArr2 = new byte[8];
                System.arraycopy(bArr, 7, bArr2, 0, 8);
                utilitiesSDKSecureChannelMessage.nonce = u7.a(bArr2);
                return;
            default:
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
        }
    }

    public static UtilitiesSDKSecureChannelParseResponse parseSecureChannelMessage(String str) {
        try {
            if (str == null) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL);
            }
            if (!u7.c(str)) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT);
            }
            UtilitiesSDKSecureChannelMessage utilitiesSDKSecureChannelMessage = new UtilitiesSDKSecureChannelMessage();
            utilitiesSDKSecureChannelMessage.rawData = str;
            byte[] a = u7.a(str);
            c(a, utilitiesSDKSecureChannelMessage);
            a(a, utilitiesSDKSecureChannelMessage);
            b(a, utilitiesSDKSecureChannelMessage);
            return new UtilitiesSDKSecureChannelParseResponse(0, utilitiesSDKSecureChannelMessage);
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKSecureChannelParseResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKSecureChannelParseResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static int a(byte[] bArr, int i, int i2) {
        int i3 = 0;
        for (int i4 = i; i4 < i + i2; i4++) {
            i3 += ((bArr[i4] + 256) % 256) << ((((i2 - i4) + i) - 1) * 8);
        }
        return i3;
    }

    private static String a(int i) {
        int i2 = i % 40;
        int i3 = (i - i2) / 40;
        int i4 = i3 % 40;
        return String.valueOf(new char[]{"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ &=%".charAt((i3 - i4) / 40), "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ &=%".charAt(i4), "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ &=%".charAt(i2)});
    }
}
