package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.p;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecurePinUpdate.smali */
public final class SecurePinUpdate implements CustomerAuthenticatedProcess {
    private final p innerSecurePinUpdate;

    public SecurePinUpdate(p pVar) {
        this.innerSecurePinUpdate = pVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecurePinUpdate.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecurePinUpdate.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecurePinUpdate.o();
    }

    public final boolean isOnline() {
        return !this.innerSecurePinUpdate.k();
    }

    public final String getMessage() {
        return null;
    }

    public final int getRemainingAttemptNumber() {
        return this.innerSecurePinUpdate.a();
    }

    public final void launch(Context context, SecurePinInput securePinInput, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecurePinUpdate.d(context, securePinInput, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecurePinUpdate));
    }

    public final void launch(Context context, SecurePinInput securePinInput, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecurePinUpdate.e(context, securePinInput, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecurePinUpdate));
    }

    public final void setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.innerSecurePinUpdate.b(customerAuthenticatedProcessActivityCallback);
    }
}
