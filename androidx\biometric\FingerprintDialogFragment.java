package androidx.biometric;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.AnimatedVectorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import org.bouncycastle.i18n.MessageBundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\FingerprintDialogFragment.smali */
public class FingerprintDialogFragment extends DialogFragment {
    static final int DISPLAYED_FOR_500_MS = 6;
    private static final String KEY_DIALOG_BUNDLE = "SavedBundle";
    private static final int MESSAGE_DISPLAY_TIME_MS = 2000;
    static final int MSG_DISMISS_DIALOG_AUTHENTICATED = 5;
    static final int MSG_DISMISS_DIALOG_ERROR = 3;
    static final int MSG_RESET_MESSAGE = 4;
    static final int MSG_SHOW_ERROR = 2;
    static final int MSG_SHOW_HELP = 1;
    private static final int STATE_FINGERPRINT = 1;
    private static final int STATE_FINGERPRINT_AUTHENTICATED = 3;
    private static final int STATE_FINGERPRINT_ERROR = 2;
    private static final int STATE_NONE = 0;
    private static final String TAG = "FingerprintDialogFrag";
    private Bundle mBundle;
    private Context mContext;
    private int mErrorColor;
    private TextView mErrorText;
    private ImageView mFingerprintIcon;
    private int mLastState;
    DialogInterface.OnClickListener mNegativeButtonListener;
    private int mTextColor;
    private H mHandler = new H();
    private boolean mDismissInstantly = true;
    private final DialogInterface.OnClickListener mDeviceCredentialButtonListener = new DialogInterface.OnClickListener() { // from class: androidx.biometric.FingerprintDialogFragment.1
        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(final DialogInterface dialog, int which) {
            if (which == -2) {
                Utils.launchDeviceCredentialConfirmation(FingerprintDialogFragment.TAG, FingerprintDialogFragment.this.getActivity(), FingerprintDialogFragment.this.mBundle, new Runnable() { // from class: androidx.biometric.FingerprintDialogFragment.1.1
                    @Override // java.lang.Runnable
                    public void run() {
                        FingerprintDialogFragment.this.onCancel(dialog);
                    }
                });
            }
        }
    };

    static FingerprintDialogFragment newInstance() {
        FingerprintDialogFragment fragment = new FingerprintDialogFragment();
        return fragment;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\FingerprintDialogFragment$H.smali */
    final class H extends Handler {
        H() {
        }

        @Override // android.os.Handler
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 1:
                    FingerprintDialogFragment.this.handleShowHelp((CharSequence) msg.obj);
                    break;
                case 2:
                    FingerprintDialogFragment.this.handleShowError((CharSequence) msg.obj);
                    break;
                case 3:
                    FingerprintDialogFragment.this.handleDismissDialogError((CharSequence) msg.obj);
                    break;
                case 4:
                    FingerprintDialogFragment.this.handleResetMessage();
                    break;
                case 5:
                    FingerprintDialogFragment.this.dismissSafely();
                    break;
                case 6:
                    Context context = FingerprintDialogFragment.this.getContext();
                    FingerprintDialogFragment.this.mDismissInstantly = context != null && Utils.shouldHideFingerprintDialog(context, Build.MODEL);
                    break;
            }
        }
    }

    @Override // androidx.fragment.app.DialogFragment
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        CharSequence negativeButtonText;
        if (savedInstanceState != null && this.mBundle == null) {
            this.mBundle = savedInstanceState.getBundle(KEY_DIALOG_BUNDLE);
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle(this.mBundle.getCharSequence(MessageBundle.TITLE_ENTRY));
        View layout = LayoutInflater.from(builder.getContext()).inflate(R.layout.fingerprint_dialog_layout, (ViewGroup) null);
        TextView subtitleView = (TextView) layout.findViewById(R.id.fingerprint_subtitle);
        TextView descriptionView = (TextView) layout.findViewById(R.id.fingerprint_description);
        CharSequence subtitle = this.mBundle.getCharSequence("subtitle");
        if (TextUtils.isEmpty(subtitle)) {
            subtitleView.setVisibility(8);
        } else {
            subtitleView.setVisibility(0);
            subtitleView.setText(subtitle);
        }
        CharSequence description = this.mBundle.getCharSequence("description");
        if (TextUtils.isEmpty(description)) {
            descriptionView.setVisibility(8);
        } else {
            descriptionView.setVisibility(0);
            descriptionView.setText(description);
        }
        this.mFingerprintIcon = (ImageView) layout.findViewById(R.id.fingerprint_icon);
        this.mErrorText = (TextView) layout.findViewById(R.id.fingerprint_error);
        if (isDeviceCredentialAllowed()) {
            negativeButtonText = getString(R.string.confirm_device_credential_password);
        } else {
            negativeButtonText = this.mBundle.getCharSequence("negative_text");
        }
        builder.setNegativeButton(negativeButtonText, new DialogInterface.OnClickListener() { // from class: androidx.biometric.FingerprintDialogFragment.2
            @Override // android.content.DialogInterface.OnClickListener
            public void onClick(DialogInterface dialog, int which) {
                if (FingerprintDialogFragment.this.isDeviceCredentialAllowed()) {
                    FingerprintDialogFragment.this.mDeviceCredentialButtonListener.onClick(dialog, which);
                } else if (FingerprintDialogFragment.this.mNegativeButtonListener != null) {
                    FingerprintDialogFragment.this.mNegativeButtonListener.onClick(dialog, which);
                } else {
                    Log.w(FingerprintDialogFragment.TAG, "No suitable negative button listener.");
                }
            }
        });
        builder.setView(layout);
        Dialog dialog = builder.create();
        dialog.setCanceledOnTouchOutside(false);
        return dialog;
    }

    @Override // androidx.fragment.app.DialogFragment, androidx.fragment.app.Fragment
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBundle(KEY_DIALOG_BUNDLE, this.mBundle);
    }

    @Override // androidx.fragment.app.DialogFragment, androidx.fragment.app.Fragment
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.mContext = getContext();
        this.mErrorColor = getThemedColorFor(android.R.attr.colorError);
        this.mTextColor = getThemedColorFor(android.R.attr.textColorSecondary);
    }

    @Override // androidx.fragment.app.Fragment
    public void onResume() {
        super.onResume();
        this.mLastState = 0;
        updateFingerprintIcon(1);
    }

    @Override // androidx.fragment.app.Fragment
    public void onPause() {
        super.onPause();
        this.mHandler.removeCallbacksAndMessages(null);
    }

    @Override // androidx.fragment.app.DialogFragment, android.content.DialogInterface.OnCancelListener
    public void onCancel(DialogInterface dialog) {
        super.onCancel(dialog);
        FingerprintHelperFragment fingerprintHelperFragment = (FingerprintHelperFragment) getFragmentManager().findFragmentByTag("FingerprintHelperFragment");
        if (fingerprintHelperFragment != null) {
            fingerprintHelperFragment.cancel(1);
        }
    }

    public void setBundle(Bundle bundle) {
        this.mBundle = bundle;
    }

    private int getThemedColorFor(int attr) {
        TypedValue tv = new TypedValue();
        Resources.Theme theme = this.mContext.getTheme();
        theme.resolveAttribute(attr, tv, true);
        TypedArray arr = getActivity().obtainStyledAttributes(tv.data, new int[]{attr});
        int color = arr.getColor(0, 0);
        arr.recycle();
        return color;
    }

    protected CharSequence getNegativeButtonText() {
        return this.mBundle.getCharSequence("negative_text");
    }

    void setNegativeButtonListener(DialogInterface.OnClickListener listener) {
        this.mNegativeButtonListener = listener;
    }

    Handler getHandler() {
        return this.mHandler;
    }

    void dismissSafely() {
        if (getFragmentManager() == null) {
            Log.e(TAG, "Failed to dismiss fingerprint dialog fragment. Fragment manager was null.");
        } else {
            dismissAllowingStateLoss();
        }
    }

    static int getHideDialogDelay(Context context) {
        return (context == null || !Utils.shouldHideFingerprintDialog(context, Build.MODEL)) ? 2000 : 0;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean isDeviceCredentialAllowed() {
        return this.mBundle.getBoolean("allow_device_credential");
    }

    private boolean shouldAnimateForTransition(int oldState, int newState) {
        if (oldState == 0 && newState == 1) {
            return false;
        }
        if (oldState == 1 && newState == 2) {
            return true;
        }
        return oldState == 2 && newState == 1;
    }

    private Drawable getAnimationForTransition(int oldState, int newState) {
        int iconRes;
        if (oldState != 0 || newState != 1) {
            if (oldState == 1 && newState == 2) {
                iconRes = R.drawable.fingerprint_dialog_fp_to_error;
            } else if (oldState == 2 && newState == 1) {
                iconRes = R.drawable.fingerprint_dialog_error_to_fp;
            } else if (oldState == 1 && newState == 3) {
                iconRes = R.drawable.fingerprint_dialog_error_to_fp;
            } else {
                return null;
            }
        } else {
            iconRes = R.drawable.fingerprint_dialog_fp_to_error;
        }
        return this.mContext.getDrawable(iconRes);
    }

    private void updateFingerprintIcon(int newState) {
        Drawable icon;
        if (this.mFingerprintIcon == null || (icon = getAnimationForTransition(this.mLastState, newState)) == null) {
            return;
        }
        AnimatedVectorDrawable animation = icon instanceof AnimatedVectorDrawable ? (AnimatedVectorDrawable) icon : null;
        this.mFingerprintIcon.setImageDrawable(icon);
        if (animation != null && shouldAnimateForTransition(this.mLastState, newState)) {
            animation.start();
        }
        this.mLastState = newState;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleShowHelp(CharSequence msg) {
        updateFingerprintIcon(2);
        this.mHandler.removeMessages(4);
        TextView textView = this.mErrorText;
        if (textView != null) {
            textView.setTextColor(this.mErrorColor);
            this.mErrorText.setText(msg);
        }
        H h = this.mHandler;
        h.sendMessageDelayed(h.obtainMessage(4), 2000L);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleShowError(CharSequence msg) {
        updateFingerprintIcon(2);
        this.mHandler.removeMessages(4);
        TextView textView = this.mErrorText;
        if (textView != null) {
            textView.setTextColor(this.mErrorColor);
            this.mErrorText.setText(msg);
        }
        H h = this.mHandler;
        h.sendMessageDelayed(h.obtainMessage(3), getHideDialogDelay(this.mContext));
    }

    private void dismissAfterDelay(CharSequence msg) {
        TextView textView = this.mErrorText;
        if (textView != null) {
            textView.setTextColor(this.mErrorColor);
            if (msg != null) {
                this.mErrorText.setText(msg);
            } else {
                this.mErrorText.setText(R.string.fingerprint_error_lockout);
            }
        }
        this.mHandler.postDelayed(new Runnable() { // from class: androidx.biometric.FingerprintDialogFragment.3
            @Override // java.lang.Runnable
            public void run() {
                FingerprintDialogFragment.this.dismissSafely();
            }
        }, getHideDialogDelay(this.mContext));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleDismissDialogError(CharSequence msg) {
        if (this.mDismissInstantly) {
            dismissSafely();
        } else {
            dismissAfterDelay(msg);
        }
        this.mDismissInstantly = true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void handleResetMessage() {
        updateFingerprintIcon(1);
        TextView textView = this.mErrorText;
        if (textView != null) {
            textView.setTextColor(this.mTextColor);
            this.mErrorText.setText(this.mContext.getString(R.string.fingerprint_dialog_touch_sensor));
        }
    }
}
