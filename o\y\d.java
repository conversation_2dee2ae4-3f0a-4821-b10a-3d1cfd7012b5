package o.y;

import android.content.Context;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\d.smali */
public abstract class d<CB> extends b<CB> {
    private static int c = 0;
    private static int b = 1;

    public d(Context context, CB cb, o.ei.c cVar, o.bb.e eVar) {
        super(context, cb, cVar, eVar);
    }

    public final void c(o.el.d dVar) {
        int i = b;
        int i2 = (i & 15) + (i | 15);
        c = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                g().a().i().d(dVar);
                g().d(e());
                int i3 = 59 / 0;
                break;
            default:
                g().a().i().d(dVar);
                g().d(e());
                break;
        }
        int i4 = b + 29;
        c = i4 % 128;
        int i5 = i4 % 2;
    }
}
