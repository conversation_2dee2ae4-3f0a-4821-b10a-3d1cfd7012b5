package com.rolster.capacitor.otp;

import android.content.BroadcastReceiver;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import androidx.activity.result.ActivityResult;
import androidx.core.app.NotificationCompat;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.android.gms.auth.api.phone.SmsRetriever;
import java.text.MessageFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@CapacitorPlugin(name = "OtpManager")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\rolster\capacitor\otp\OtpManagerPlugin.smali */
public class OtpManagerPlugin extends Plugin implements OtpReceiveListener {
    private BroadcastReceiver broadcastReceiver;
    private OtpManagerResolver otpManagerResolver;
    private PluginCall pluginCall;

    @Override // com.getcapacitor.Plugin
    public void load() {
        try {
            this.otpManagerResolver = (OtpManagerResolver) Class.forName("com.rolster.capacitor.otp.google.GoogleOtpManagerResolver").getConstructor(new Class[0]).newInstance(new Object[0]);
        } catch (Exception e) {
            throw new RuntimeException("Error inicializando StoreVerifyServices", e);
        }
    }

    @PluginMethod
    public void activate(PluginCall call) {
        resetBroadcastReceiver();
        this.otpManagerResolver.execute(this, getActivity(), call);
        this.pluginCall = call;
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void onSmsReceivedSuccess(String sms) {
        int otpSize = this.pluginCall.getInt("otpSize").intValue();
        resolveOtpFromSMS(sms, otpSize);
        resetBroadcastReceiver();
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void onSmsReceivedSuccess(Intent intent, String activityCallback) {
        startActivityForResult(this.pluginCall, intent, activityCallback);
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void onSmsReceivedCancel() {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerCanceled");
        result.put("message", "User does not accept read permission");
        notifyListeners("otpManagerEvent", result);
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void onSmsReceivedTimeOut() {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerTimeout");
        result.put("message", "Sorry, the waiting time of 5 minutes for the arrival of the SMS has expired");
        notifyListeners("otpManagerEvent", result);
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void onSmsReceivedError(String msgError) {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerError");
        result.put("message", msgError);
        notifyListeners("otpManagerEvent", result);
    }

    @Override // com.rolster.capacitor.otp.OtpReceiveListener
    public void registerReceiverSms(BroadcastReceiver receiver, IntentFilter intent) {
        this.broadcastReceiver = receiver;
        if (Build.VERSION.SDK_INT >= 33) {
            getActivity().registerReceiver(receiver, intent, 2);
        } else {
            getActivity().registerReceiver(receiver, intent);
        }
    }

    @ActivityCallback
    private void handlerGoogleSMS(PluginCall call, ActivityResult activityResult) {
        if (activityResult.getResultCode() == -1) {
            String sms = activityResult.getData().getStringExtra(SmsRetriever.EXTRA_SMS_MESSAGE);
            int otpSize = call.getInt("otpSize").intValue();
            resolveOtpFromSMS(sms, otpSize);
            resetBroadcastReceiver();
            return;
        }
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerCanceled");
        result.put("message", "User does not accept read permission");
        notifyListeners("otpManagerEvent", result);
    }

    private String requestOtpFromSMS(String sms, int otpSize) {
        String pattern = MessageFormat.format("(\\d'{'{0}'}')", Integer.valueOf(otpSize));
        Matcher matcher = Pattern.compile(pattern).matcher(sms);
        return matcher.find() ? matcher.group() : "";
    }

    private void resolveOtpFromSMS(String sms, int otpSize) {
        String otp = requestOtpFromSMS(sms, otpSize);
        JSObject result = new JSObject();
        result.put("sms", sms);
        if (!otp.isEmpty()) {
            result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerSuccess");
            result.put("otp", otp);
        } else {
            result.put(NotificationCompat.CATEGORY_STATUS, "otpManagerEmpty");
        }
        notifyListeners("otpManagerEvent", result);
    }

    private void resetBroadcastReceiver() {
        if (this.broadcastReceiver != null) {
            getActivity().unregisterReceiver(this.broadcastReceiver);
            this.broadcastReceiver = null;
        }
    }
}
