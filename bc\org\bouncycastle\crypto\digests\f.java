package bc.org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\f.smali */
public class f extends a {
    private static final int[] i = new int[64];
    private int[] e;
    private int[] f;
    private int g;
    private int[] h;

    static {
        int i2;
        int i3 = 0;
        while (true) {
            if (i3 >= 16) {
                break;
            }
            i[i3] = (2043430169 >>> (32 - i3)) | (2043430169 << i3);
            i3++;
        }
        for (i2 = 16; i2 < 64; i2++) {
            int i4 = i2 % 32;
            i[i2] = (2055708042 >>> (32 - i4)) | (2055708042 << i4);
        }
    }

    public f() {
        this(q1.ANY);
    }

    private int a(int i2) {
        return (i2 ^ ((i2 << 9) | (i2 >>> 23))) ^ ((i2 << 17) | (i2 >>> 15));
    }

    private int a(int i2, int i3, int i4) {
        return (i2 ^ i3) ^ i4;
    }

    private void a(f fVar) {
        int[] iArr = fVar.e;
        int[] iArr2 = this.e;
        System.arraycopy(iArr, 0, iArr2, 0, iArr2.length);
        int[] iArr3 = fVar.f;
        int[] iArr4 = this.f;
        System.arraycopy(iArr3, 0, iArr4, 0, iArr4.length);
        this.g = fVar.g;
    }

    private int b(int i2) {
        return (i2 ^ ((i2 << 15) | (i2 >>> 17))) ^ ((i2 << 23) | (i2 >>> 9));
    }

    private int b(int i2, int i3, int i4) {
        return (i2 & i4) | (i2 & i3) | (i3 & i4);
    }

    private int c(int i2, int i3, int i4) {
        return (i2 ^ i3) ^ i4;
    }

    private int d(int i2, int i3, int i4) {
        return ((~i2) & i4) | (i3 & i2);
    }

    protected p1 b() {
        return g.a(this, 256, this.a);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public m5 copy() {
        return new f(this);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i2) {
        finish();
        j6.a(this.e, bArr, i2);
        reset();
        return 32;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public String getAlgorithmName() {
        return "SM3";
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int getDigestSize() {
        return 32;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public void reset(m5 m5Var) {
        f fVar = (f) m5Var;
        super.a((a) fVar);
        a(fVar);
    }

    public f(q1 q1Var) {
        super(q1Var);
        this.e = new int[8];
        this.f = new int[16];
        this.h = new int[68];
        t1.a(b());
        reset();
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(byte[] bArr, int i2) {
        int[] iArr = this.f;
        int i3 = this.g;
        this.g = i3 + 1;
        iArr[i3] = j6.a(bArr, i2);
        if (this.g >= 16) {
            a();
        }
    }

    @Override // bc.org.bouncycastle.crypto.digests.a, bc.org.bouncycastle.crypto.Digest
    public void reset() {
        super.reset();
        int[] iArr = this.e;
        iArr[0] = 1937774191;
        iArr[1] = 1226093241;
        iArr[2] = 388252375;
        iArr[3] = -628488704;
        iArr[4] = -1452330820;
        iArr[5] = 372324522;
        iArr[6] = -477237683;
        iArr[7] = -1325724082;
        this.g = 0;
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(long j) {
        int i2 = this.g;
        if (i2 > 14) {
            this.f[i2] = 0;
            this.g = i2 + 1;
            a();
        }
        while (true) {
            int i3 = this.g;
            if (i3 < 14) {
                this.f[i3] = 0;
                this.g = i3 + 1;
            } else {
                int[] iArr = this.f;
                int i4 = i3 + 1;
                iArr[i3] = (int) (j >>> 32);
                this.g = i4 + 1;
                iArr[i4] = (int) j;
                return;
            }
        }
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a() {
        int i2;
        int i3 = 0;
        while (true) {
            if (i3 >= 16) {
                break;
            }
            this.h[i3] = this.f[i3];
            i3++;
        }
        for (int i4 = 16; i4 < 68; i4++) {
            int[] iArr = this.h;
            int i5 = iArr[i4 - 3];
            int i6 = iArr[i4 - 13];
            iArr[i4] = (b(((i5 >>> 17) | (i5 << 15)) ^ (iArr[i4 - 16] ^ iArr[i4 - 9])) ^ ((i6 >>> 25) | (i6 << 7))) ^ this.h[i4 - 6];
        }
        int[] iArr2 = this.e;
        int i7 = iArr2[0];
        int i8 = iArr2[1];
        int i9 = iArr2[2];
        int i10 = iArr2[3];
        int i11 = iArr2[4];
        int i12 = iArr2[5];
        int i13 = iArr2[6];
        int i14 = iArr2[7];
        int i15 = 0;
        int i16 = i13;
        for (i2 = 16; i15 < i2; i2 = 16) {
            int i17 = (i7 << 12) | (i7 >>> 20);
            int i18 = i17 + i11 + i[i15];
            int i19 = (i18 << 7) | (i18 >>> 25);
            int[] iArr3 = this.h;
            int i20 = iArr3[i15];
            int i21 = i20 ^ iArr3[i15 + 4];
            int a = a(i7, i8, i9) + i10;
            int c = c(i11, i12, i16) + i14 + i19 + i20;
            int i22 = (i8 << 9) | (i8 >>> 23);
            int i23 = (i12 << 19) | (i12 >>> 13);
            i15++;
            i12 = i11;
            i11 = a(c);
            i10 = i9;
            i9 = i22;
            i14 = i16;
            i16 = i23;
            i8 = i7;
            i7 = a + (i19 ^ i17) + i21;
        }
        int i24 = i14;
        int i25 = i11;
        int i26 = i16;
        int i27 = i10;
        int i28 = i9;
        int i29 = i8;
        int i30 = i7;
        int i31 = 16;
        while (i31 < 64) {
            int i32 = (i30 << 12) | (i30 >>> 20);
            int i33 = i32 + i25 + i[i31];
            int i34 = (i33 << 7) | (i33 >>> 25);
            int[] iArr4 = this.h;
            int i35 = iArr4[i31];
            int i36 = i35 ^ iArr4[i31 + 4];
            int b = b(i30, i29, i28) + i27;
            int d = d(i25, i12, i26) + i24 + i34 + i35;
            int i37 = (i12 << 19) | (i12 >>> 13);
            i31++;
            i12 = i25;
            i25 = a(d);
            i27 = i28;
            i28 = (i29 >>> 23) | (i29 << 9);
            i29 = i30;
            i30 = b + (i34 ^ i32) + i36;
            i24 = i26;
            i26 = i37;
        }
        int[] iArr5 = this.e;
        iArr5[0] = i30 ^ iArr5[0];
        iArr5[1] = iArr5[1] ^ i29;
        iArr5[2] = iArr5[2] ^ i28;
        iArr5[3] = iArr5[3] ^ i27;
        iArr5[4] = iArr5[4] ^ i25;
        iArr5[5] = iArr5[5] ^ i12;
        iArr5[6] = i26 ^ iArr5[6];
        iArr5[7] = iArr5[7] ^ i24;
        this.g = 0;
    }

    public f(f fVar) {
        super(fVar);
        this.e = new int[8];
        this.f = new int[16];
        this.h = new int[68];
        t1.a(b());
        a(fVar);
    }
}
