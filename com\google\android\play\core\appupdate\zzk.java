package com.google.android.play.core.appupdate;

import android.content.Context;
import com.google.android.play.core.appupdate.internal.zzae;
import com.google.android.play.core.appupdate.internal.zzaf;

/* compiled from: com.google.android.play:app-update@@2.1.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\appupdate\zzk.smali */
public final class zzk implements zzaf {
    private final zzi zza;

    public zzk(zzi zziVar) {
        this.zza = zziVar;
    }

    @Override // com.google.android.play.core.appupdate.internal.zzaf
    public final /* synthetic */ Object zza() {
        Context zza = this.zza.zza();
        zzae.zza(zza);
        return zza;
    }

    public final Context zzb() {
        Context zza = this.zza.zza();
        zzae.zza(zza);
        return zza;
    }
}
