package o.db;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\db\c.smali */
public final class c extends Exception {
    private static int a = 0;
    private static int e = 1;
    private final EnumC0035c c;

    c(EnumC0035c enumC0035c, String str) {
        super(str);
        this.c = enumC0035c;
    }

    public final EnumC0035c b() {
        int i = a;
        int i2 = (i ^ Opcodes.LSHL) + ((i & Opcodes.LSHL) << 1);
        e = i2 % 128;
        int i3 = i2 % 2;
        EnumC0035c enumC0035c = this.c;
        int i4 = ((i | 109) << 1) - (i ^ 109);
        e = i4 % 128;
        int i5 = i4 % 2;
        return enumC0035c;
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* renamed from: o.db.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\db\c$c.smali */
    public static final class EnumC0035c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int a;
        public static final EnumC0035c b;
        public static final EnumC0035c c;
        private static final /* synthetic */ EnumC0035c[] d;
        private static int e;
        private static short[] f;
        private static int g;
        private static int h;
        private static int i;
        private static byte[] j;

        static void b() {
            j = new byte[]{104, -111, 98, -122, -107, 121, -110, -102, 120, -119, 76, -96, 67, -98, -106, -105, 108, -112, 103, -108, -66, 69, -106, -103, 103, -109, -101, -124, 92, 83, -94, 82, -92, 92, -87, 87, 78, UtilitiesSDKConstants.SRP_LABEL_MAC, -85, 95, 88, 82, 73, -125, 109, -114, 83, 91, 90, -95, 93, -86, 89, 115, -120, 91, 84, -86, 94, 86, 73, -112, -112};
            e = 909053686;
            h = -713873230;
            a = 1642969225;
        }

        static void init$0() {
            $$a = new byte[]{21, -84, -91, -118};
            $$b = 38;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x003a). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 2
                int r7 = r7 + 1
                int r8 = r8 * 2
                int r8 = 4 - r8
                byte[] r0 = o.db.c.EnumC0035c.$$a
                int r6 = r6 * 2
                int r6 = 110 - r6
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1c
                r6 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L3a
            L1c:
                r3 = r2
                r5 = r8
                r8 = r6
                r6 = r5
            L20:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L2d
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2d:
                r4 = r0[r6]
                int r3 = r3 + 1
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L3a:
                int r8 = r8 + 1
                int r7 = r7 + r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r8
                r8 = r5
                goto L20
            */
            throw new UnsupportedOperationException("Method not decompiled: o.db.c.EnumC0035c.l(short, byte, byte, java.lang.Object[]):void");
        }

        private EnumC0035c(String str, int i2) {
        }

        private static /* synthetic */ EnumC0035c[] a() {
            int i2 = i + Opcodes.LREM;
            int i3 = i2 % 128;
            g = i3;
            int i4 = i2 % 2;
            EnumC0035c[] enumC0035cArr = {b, c};
            int i5 = i3 + 69;
            i = i5 % 128;
            int i6 = i5 % 2;
            return enumC0035cArr;
        }

        public static EnumC0035c valueOf(String str) {
            int i2 = g + 65;
            i = i2 % 128;
            int i3 = i2 % 2;
            EnumC0035c enumC0035c = (EnumC0035c) Enum.valueOf(EnumC0035c.class, str);
            int i4 = i + 27;
            g = i4 % 128;
            switch (i4 % 2 == 0 ? ':' : 'Q') {
                case Opcodes.FASTORE /* 81 */:
                    return enumC0035c;
                default:
                    throw null;
            }
        }

        public static EnumC0035c[] values() {
            int i2 = g + 75;
            i = i2 % 128;
            int i3 = i2 % 2;
            EnumC0035c[] enumC0035cArr = (EnumC0035c[]) d.clone();
            int i4 = i + 39;
            g = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return enumC0035cArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            g = 1;
            b();
            Object[] objArr = new Object[1];
            k((byte) (7 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), (-1472378393) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) Color.blue(0), (-73) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 480500272, objArr);
            b = new EnumC0035c(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            k((byte) ((-54) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), Drawable.resolveOpacity(0, 0) - 1472378365, (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') - 67, View.resolveSize(0, 0) + 480500272, objArr2);
            c = new EnumC0035c(((String) objArr2[0]).intern(), 1);
            d = a();
            int i2 = i + Opcodes.DNEG;
            g = i2 % 128;
            switch (i2 % 2 == 0 ? (char) 4 : 'H') {
                case 4:
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:70:0x0281, code lost:
        
            r3 = r7;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 854
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.db.c.EnumC0035c.k(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }
}
