package fr.antelop.sdk.authentication;

import o.f.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationPasscode.smali */
public final class CustomerAuthenticationPasscode extends CustomerAuthenticationCredentials {
    private static final int MAX_PASSCODE_LENGTH = 8;
    private static final int MIN_PASSCODE_LENGTH = 4;
    private final d value;

    public CustomerAuthenticationPasscode(byte[] bArr) {
        this.value = new d(bArr);
    }

    public final d getValue() {
        return this.value;
    }

    public final boolean isValid() {
        int d = this.value.d();
        return this.value.c() && d >= 4 && d <= 8;
    }
}
