package com.capacitorjs.plugins.localnotifications;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\capacitorjs\plugins\localnotifications\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\capacitorjs\plugins\localnotifications\R$drawable.smali */
    public static final class drawable {
        public static int ic_transparent = 0x7f08014f;

        private drawable() {
        }
    }

    private R() {
    }
}
