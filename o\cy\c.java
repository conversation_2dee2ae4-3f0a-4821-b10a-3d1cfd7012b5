package o.cy;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cy\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static char c;
    private static int d;
    private static int[] e;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        h = 1;
        c();
        ViewConfiguration.getDoubleTapTimeout();
        ViewConfiguration.getTapTimeout();
        int i = d + Opcodes.DDIV;
        h = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = new int[]{1776497936, 1242619129, -1966732270, -1606875728, 1333039371, 1942716262, -1539172094, 1670369444, 2015034195, 934220197, 1558616497, 1105447980, -1563439162, 998793973, 177144607, 233398947, 641851048, 628858422};
        c = (char) 35878;
        a = 161105445;
        b = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = 4 - r7
            int r8 = r8 + 99
            byte[] r0 = o.cy.c.$$a
            int r9 = r9 * 3
            int r9 = r9 + 1
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L32
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L32:
            int r7 = -r7
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.c.i(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{30, 126, 60, -105};
        $$b = 245;
    }

    /* JADX WARN: Code restructure failed: missing block: B:46:0x005b, code lost:
    
        if (r27.b(((java.lang.String) r8[0]).intern()) == false) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x003a, code lost:
    
        if (r27.b(((java.lang.String) r8[0]).intern()) == false) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0062, code lost:
    
        return java.util.Collections.emptyMap();
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.util.Map<java.lang.String, java.util.List<o.dr.a>> e(o.eg.b r27) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1058
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.c.e(o.eg.b):java.util.Map");
    }

    private static void f(int[] iArr, int i, Object[] objArr) {
        int[] iArr2;
        g gVar = new g();
        char[] cArr = new char[4];
        int i2 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = e;
        int i3 = -1667374059;
        int i4 = 16;
        int i5 = 1;
        int i6 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i7 = 0;
            while (i7 < length) {
                int i8 = $11 + 37;
                $10 = i8 % 128;
                int i9 = i8 % i2;
                try {
                    Object[] objArr2 = {Integer.valueOf(iArr3[i7])};
                    Object obj = o.e.a.s.get(-1667374059);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((ViewConfiguration.getTapTimeout() >> i4) + 10, (char) (8857 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 325 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                        byte b2 = (byte) 0;
                        Object[] objArr3 = new Object[1];
                        i(b2, (byte) (b2 | 17), b2, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1667374059, obj);
                    }
                    iArr4[i7] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                    i7++;
                    i2 = 2;
                    i4 = 16;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            iArr3 = iArr4;
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = e;
        if (iArr6 != null) {
            int length3 = iArr6.length;
            int[] iArr7 = new int[length3];
            int i10 = 0;
            while (i10 < length3) {
                try {
                    Object[] objArr4 = new Object[i5];
                    objArr4[i6] = Integer.valueOf(iArr6[i10]);
                    Object obj2 = o.e.a.s.get(Integer.valueOf(i3));
                    if (obj2 != null) {
                        iArr2 = iArr6;
                    } else {
                        Class cls2 = (Class) o.e.a.c(10 - Color.green(i6), (char) (TextUtils.lastIndexOf("", '0', i6) + 8857), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 323);
                        byte b3 = (byte) i6;
                        iArr2 = iArr6;
                        Object[] objArr5 = new Object[1];
                        i(b3, (byte) (b3 | 17), b3, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                        i3 = -1667374059;
                        o.e.a.s.put(-1667374059, obj2);
                    }
                    iArr7[i10] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    i10++;
                    iArr6 = iArr2;
                    i5 = 1;
                    i6 = 0;
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            iArr6 = iArr7;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        int i11 = $10 + 55;
        $11 = i11 % 128;
        switch (i11 % 2 == 0 ? (char) 27 : '6') {
        }
        while (gVar.a < iArr.length) {
            int i12 = $10 + 3;
            $11 = i12 % 128;
            int i13 = i12 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr5);
            int i14 = 0;
            for (int i15 = 16; i14 < i15; i15 = 16) {
                gVar.e ^= iArr5[i14];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                    i14++;
                    int i16 = $11 + Opcodes.LSHL;
                    $10 = i16 % 128;
                    switch (i16 % 2 != 0 ? '(' : 'a') {
                        case Opcodes.LADD /* 97 */:
                        default:
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i17 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i17;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i18 = gVar.e;
            int i19 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) o.e.a.c(Color.blue(0) + 12, (char) (KeyEvent.keyCodeFromString("") + 55183), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 515);
                    byte b4 = (byte) 0;
                    Object[] objArr8 = new Object[1];
                    i(b4, (byte) (b4 | Tnaf.POW_2_WIDTH), b4, objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int r18, java.lang.String r19, char r20, java.lang.String r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 672
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cy.c.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
