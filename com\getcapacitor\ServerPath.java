package com.getcapacitor;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\ServerPath.smali */
public class ServerPath {
    private final String path;
    private final PathType type;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\ServerPath$PathType.smali */
    public enum PathType {
        BASE_PATH,
        ASSET_PATH
    }

    public ServerPath(PathType type, String path) {
        this.type = type;
        this.path = path;
    }

    public PathType getType() {
        return this.type;
    }

    public String getPath() {
        return this.path;
    }
}
