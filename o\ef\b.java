package o.ef;

import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ef\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    private static final /* synthetic */ b[] d;
    public static final b e;
    private static int f;
    private static int g;
    private static long h;
    private static char i;
    private static int j;

    static void a() {
        i = (char) 17957;
        f = 161105445;
        h = 3051225478195646414L;
    }

    static void init$0() {
        $$a = new byte[]{17, -116, 103, 33};
        $$b = 93;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ef.b.$$a
            int r8 = r8 * 3
            int r8 = 3 - r8
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r7 = 106 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L37
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r8 = r8 + 1
            r3 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ef.b.l(int, int, int, java.lang.Object[]):void");
    }

    private b(String str, int i2) {
    }

    private static /* synthetic */ b[] b() {
        int i2 = j + 95;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        b[] bVarArr = {c, e, a, b};
        int i5 = i3 + 75;
        j = i5 % 128;
        int i6 = i5 % 2;
        return bVarArr;
    }

    public static b valueOf(String str) {
        int i2 = j + 23;
        g = i2 % 128;
        boolean z = i2 % 2 != 0;
        b bVar = (b) Enum.valueOf(b.class, str);
        switch (z) {
            case true:
                return bVar;
            default:
                throw null;
        }
    }

    public static b[] values() {
        int i2 = j + 91;
        g = i2 % 128;
        int i3 = i2 % 2;
        b[] bVarArr = (b[]) d.clone();
        int i4 = j + 7;
        g = i4 % 128;
        int i5 = i4 % 2;
        return bVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        g = 1;
        a();
        Object[] objArr = new Object[1];
        k((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "\uef3e⽷ᶥ唋㭵잊", (char) (53171 - (ViewConfiguration.getPressedStateDuration() >> 16)), "\ue145ꔺ돈돏", "䧫ೣ묮煆", objArr);
        c = new b(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        k(View.MeasureSpec.getMode(0) - 402542632, "힎왠팚", (char) (TextUtils.getTrimmedLength("") + 60834), "\ud8e7Ưꋨ웭", "䧫ೣ묮煆", objArr2);
        e = new b(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        k((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1, "돠豢ᄇ㫎훌\u070e", (char) (63666 - View.getDefaultSize(0, 0)), "濻숚눧鯸", "䧫ೣ묮煆", objArr3);
        a = new b(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        k(1456329336 - Drawable.resolveOpacity(0, 0), "获ᩋ쎏", (char) (52864 - (ViewConfiguration.getScrollBarSize() >> 8)), "磟췒聖㿎", "䧫ೣ묮煆", objArr4);
        b = new b(((String) objArr4[0]).intern(), 3);
        d = b();
        int i2 = j + 27;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 29 : Typography.greater) {
            case 29:
                int i3 = 71 / 0;
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r17, java.lang.String r18, char r19, java.lang.String r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 678
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ef.b.k(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
