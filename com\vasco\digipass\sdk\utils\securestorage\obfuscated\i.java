package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.content.Context;
import android.util.Log;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import java.io.File;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\i.smali */
public abstract class i {
    public static final void a(String fileName, Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(fileName, "fileName");
        String[] fileList = context.fileList();
        Intrinsics.checkNotNullExpressionValue(fileList, "context.fileList()");
        for (String str : fileList) {
            String a = g.a(fileName);
            if (Intrinsics.areEqual(str, a)) {
                File file = new File(context.getFilesDir().getAbsolutePath() + '/' + a);
                if (!file.exists()) {
                    throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_STORAGE, null, 2, null);
                }
                try {
                    if (file.renameTo(new File(context.getNoBackupFilesDir().getAbsolutePath() + '/' + a))) {
                        return;
                    } else {
                        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNKNOWN_STORAGE, null, 2, null);
                    }
                } catch (SecureStorageSDKException e) {
                    String message = e.getMessage();
                    Intrinsics.checkNotNull(message);
                    Log.e("SecureStorageSDK", message);
                    return;
                }
            }
        }
    }
}
