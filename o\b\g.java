package o.b;

import android.content.Context;
import android.os.Handler;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import kotlin.text.Typography;
import o.az.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\g.smali */
final class g {
    private static int f = 0;
    private static int i = 1;
    private final d.c a;
    private final a b;
    private final b c;
    private final Context d;
    private final o.h.d e;
    private final o.c.a g;
    private final Handler j;

    g(Context context, a aVar, d.c cVar, b bVar, o.h.d dVar, o.c.a aVar2, Handler handler) {
        this.d = context;
        this.b = aVar;
        this.a = cVar;
        this.c = bVar;
        this.e = dVar;
        this.g = aVar2;
        this.j = handler;
    }

    final boolean a() {
        int i2 = i;
        int i3 = (i2 ^ Opcodes.LSUB) + ((i2 & Opcodes.LSUB) << 1);
        int i4 = i3 % 128;
        f = i4;
        int i5 = i3 % 2;
        switch (this.e == null ? Typography.less : 'Z') {
            case '<':
                int i6 = i4 + 83;
                int i7 = i6 % 128;
                i = i7;
                int i8 = i6 % 2;
                switch (this.g == null) {
                    case false:
                        break;
                    default:
                        int i9 = (i7 ^ 45) + ((i7 & 45) << 1);
                        f = i9 % 128;
                        switch (i9 % 2 == 0) {
                        }
                }
                return false;
            default:
                return false;
        }
    }

    final Context b() {
        int i2 = f;
        int i3 = (i2 ^ 45) + ((i2 & 45) << 1);
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        Context context = this.d;
        int i6 = (i4 + 2) - 1;
        f = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                int i7 = 75 / 0;
                return context;
            default:
                return context;
        }
    }

    final b e() {
        int i2 = f;
        int i3 = (i2 ^ 5) + ((i2 & 5) << 1);
        i = i3 % 128;
        int i4 = i3 % 2;
        b bVar = this.c;
        int i5 = ((i2 | 5) << 1) - (i2 ^ 5);
        i = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return bVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    final o.h.d c() {
        int i2 = f;
        int i3 = (i2 ^ 91) + ((i2 & 91) << 1);
        i = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 4 : 'a') {
            case Opcodes.LADD /* 97 */:
                return this.e;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    final o.c.a d() {
        int i2 = f + 7;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        o.c.a aVar = this.g;
        int i5 = ((i3 | 67) << 1) - (i3 ^ 67);
        f = i5 % 128;
        int i6 = i5 % 2;
        return aVar;
    }

    final d.c i() {
        int i2 = f;
        int i3 = (i2 & 51) + (i2 | 51);
        int i4 = i3 % 128;
        i = i4;
        switch (i3 % 2 == 0 ? 'V' : '%') {
            case '%':
                d.c cVar = this.a;
                int i5 = i4 + 37;
                f = i5 % 128;
                int i6 = i5 % 2;
                return cVar;
            default:
                throw null;
        }
    }

    public final a h() {
        int i2 = f;
        int i3 = i2 + Opcodes.DDIV;
        i = i3 % 128;
        int i4 = i3 % 2;
        a aVar = this.b;
        int i5 = (i2 + 4) - 1;
        i = i5 % 128;
        switch (i5 % 2 == 0 ? ' ' : '_') {
            case Opcodes.SWAP /* 95 */:
                return aVar;
            default:
                int i6 = 26 / 0;
                return aVar;
        }
    }

    public final Handler f() {
        int i2 = f;
        int i3 = (i2 ^ Opcodes.DREM) + ((i2 & Opcodes.DREM) << 1);
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        Handler handler = this.j;
        int i6 = i4 + 23;
        f = i6 % 128;
        switch (i6 % 2 != 0 ? '`' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return handler;
            default:
                int i7 = 23 / 0;
                return handler;
        }
    }
}
