package o.cd;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.DefaultWalletEventListener;
import fr.antelop.sdk.WalletEventListener;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.WalletNotificationServiceCallback;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.cd.e;
import o.ee.b;
import o.ee.g;
import o.ee.o;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cd\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    static c d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        b();
        Color.red(0);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ViewConfiguration.getLongPressTimeout();
        SystemClock.elapsedRealtimeNanos();
        ViewConfiguration.getDoubleTapTimeout();
        TextUtils.lastIndexOf("", '0', 0, 0);
        ViewConfiguration.getTouchSlop();
        Color.alpha(0);
        View.MeasureSpec.getSize(0);
        int i = e + 17;
        b = i % 128;
        switch (i % 2 == 0 ? 'N' : 'G') {
            case 'N':
                throw null;
            default:
                return;
        }
    }

    static void b() {
        a = 874635476;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(byte r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 107
            int r6 = r6 * 2
            int r6 = r6 + 1
            byte[] r0 = o.cd.e.$$a
            int r8 = r8 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            int r8 = r8 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cd.e.f(byte, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{77, 4, -88, Tnaf.POW_2_WIDTH};
        $$b = 21;
    }

    private static void b(Context context) {
        WalletNotificationServiceCallback d2 = d(context);
        WalletEventListener e2 = e(context);
        Object[] objArr = new Object[1];
        c(TextUtils.indexOf((CharSequence) "", '0', 0) + 15, "\r\ufffe\u0005\u0005\ufffa\ufff0ￇ\t\b\u0005\ufffe\r\u0007\ufffa\u0012\u000b\b\r￼\ufffa\uffdf\u000b\ufffe\t\t\ufffa\u000b\ufff0\u000b\ufffe\u0007\ufffe\r\f\u0002￥\r\u0007\ufffe\u000f\uffde", View.MeasureSpec.makeMeasureSpec(0, 0) + 41, 274 - TextUtils.getOffsetBefore("", 0), true, objArr);
        HandlerThread handlerThread = new HandlerThread(((String) objArr[0]).intern());
        handlerThread.start();
        d = new c(new b(handlerThread.getLooper()), d2, e2);
        int i = b + 47;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public static c a(Context context) {
        int i = b;
        int i2 = i + 91;
        e = i2 % 128;
        int i3 = i2 % 2;
        switch (d == null ? (char) 21 : (char) 19) {
            case 19:
                break;
            default:
                int i4 = i + Opcodes.LMUL;
                e = i4 % 128;
                boolean z = i4 % 2 == 0;
                b(context);
                switch (z) {
                    case true:
                        break;
                    default:
                        throw null;
                }
        }
        return d;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x0049. Please report as an issue. */
    private static WalletNotificationServiceCallback d(Context context) {
        int i = e + 37;
        b = i % 128;
        int i2 = i % 2;
        try {
            Object[] objArr = new Object[1];
            c(15 - TextUtils.indexOf((CharSequence) "", '0', 0), "\u0000\u0003\u000e\t\b\uffc8\n\t\u0006\uffff\u000e\b\ufffb\uffc8\f\u0000\uffff�\u0003\u0010\f\uffff￭\b\t\u0003\u000e\ufffb�\u0003", (-16777186) - Color.rgb(0, 0, 0), ((byte) KeyEvent.getModifierMetaStateMask()) + 274, true, objArr);
            String a2 = o.a(context, ((String) objArr[0]).intern());
            int i3 = e + 81;
            int i4 = i3 % 128;
            b = i4;
            switch (i3 % 2 != 0) {
            }
            int i5 = i4 + 93;
            e = i5 % 128;
            int i6 = i5 % 2;
            Object[] objArr2 = new Object[1];
            c(View.MeasureSpec.getMode(0) + 16, "\u0000\u0003\u000e\t\b\uffc8\n\t\u0006\uffff\u000e\b\ufffb\uffc8\f\u0000\uffff�\u0003\u0010\f\uffff￭\b\t\u0003\u000e\ufffb�\u0003", ExpandableListView.getPackedPositionChild(0L) + 31, TextUtils.getOffsetAfter("", 0) + 273, true, objArr2);
            return (WalletNotificationServiceCallback) o.e(WalletNotificationServiceCallback.class, a2, ((String) objArr2[0]).intern());
        } catch (Exception e2) {
            g.c();
            Object[] objArr3 = new Object[1];
            c(15 - TextUtils.indexOf("", ""), "\n\uffef\n\ufff9\b\b�\n\uffde\ufff9\ufffb\f\u0007\n\u0011\uffef\ufff9\u0004\u0004�\f\uffdd\u000e�\u0006\f￤\u0001\u000b\f�\u0006�", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 33, 276 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), false, objArr3);
            String intern = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            c(22 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), "\u0006\uffc1\u0007\u0010\u0016\u000f\u0005ￍ\uffc1\u0015\u0002\f\u0006\uffc1\u0005\u0006\u0007\u0002\u0016\r\u0015\uffef\u0010\uffc1￢\u000f\u0015\u0006\r\u0010\u0011\uffc1\u000f\u0010\u0015\n\u0007\n\u0004\u0002\u0015\n\u0010\u000f\uffc1\u0014\u0006\u0013\u0017\n\u0004", 51 - (ViewConfiguration.getScrollDefaultDelay() >> 16), 266 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), false, objArr4);
            g.d(intern, ((String) objArr4[0]).intern());
            return new o.cd.c();
        }
    }

    private static WalletEventListener e(Context context) {
        String intern;
        Object e2;
        int i = b + Opcodes.DNEG;
        e = i % 128;
        try {
            switch (i % 2 != 0 ? (char) 18 : '*') {
                case '*':
                    Object[] objArr = new Object[1];
                    c((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 5, "\b\ufffb\uffc8\f\u0000\f\uffff\b\uffff\u000e\r\u0003￦\u000e\b\uffff\u0010\uffdf\u000e\uffff\u0006\u0006\ufffb\u0011\uffc8\n\t\u0006\uffff\u000e", (-16777186) - Color.rgb(0, 0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 273, true, objArr);
                    intern = ((String) objArr[0]).intern();
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    c((PointF.length(1.0f, 0.0f) > 2.0f ? 1 : (PointF.length(1.0f, 0.0f) == 2.0f ? 0 : -1)) + 5, "\b\ufffb\uffc8\f\u0000\f\uffff\b\uffff\u000e\r\u0003￦\u000e\b\uffff\u0010\uffdf\u000e\uffff\u0006\u0006\ufffb\u0011\uffc8\n\t\u0006\uffff\u000e", Color.rgb(0, 0, 1) * (-16777186), 19035 >>> (ViewConfiguration.getFadingEdgeLength() << 94), false, objArr2);
                    intern = ((String) objArr2[0]).intern();
                    break;
            }
            String a2 = o.a(context, intern);
            int i2 = e + Opcodes.LSHL;
            b = i2 % 128;
            try {
                switch (i2 % 2 != 0) {
                    case false:
                        Object[] objArr3 = new Object[1];
                        c(71 / (ViewConfiguration.getZoomControlsTimeout() > 1L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 1L ? 0 : -1)), "\b\ufffb\uffc8\f\u0000\f\uffff\b\uffff\u000e\r\u0003￦\u000e\b\uffff\u0010\uffdf\u000e\uffff\u0006\u0006\ufffb\u0011\uffc8\n\t\u0006\uffff\u000e", 6 / View.MeasureSpec.makeMeasureSpec(0, 0), 17834 >> (ViewConfiguration.getScrollFriction() > 1.0f ? 1 : (ViewConfiguration.getScrollFriction() == 1.0f ? 0 : -1)), true, objArr3);
                        e2 = o.e(WalletEventListener.class, a2, ((String) objArr3[0]).intern());
                        break;
                    default:
                        Object[] objArr4 = new Object[1];
                        c(6 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "\b\ufffb\uffc8\f\u0000\f\uffff\b\uffff\u000e\r\u0003￦\u000e\b\uffff\u0010\uffdf\u000e\uffff\u0006\u0006\ufffb\u0011\uffc8\n\t\u0006\uffff\u000e", 30 - View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 272, true, objArr4);
                        e2 = o.e(WalletEventListener.class, a2, ((String) objArr4[0]).intern());
                        break;
                }
                return (WalletEventListener) e2;
            } catch (Exception e3) {
                Object[] objArr5 = new Object[1];
                c(Gravity.getAbsoluteGravity(0, 0) + 5, "\b\ufffb\uffc8\f\u0000\f\uffff\b\uffff\u000e\r\u0003￦\u000e\b\uffff\u0010\uffdf\u000e\uffff\u0006\u0006\ufffb\u0011\uffc8\n\t\u0006\uffff\u000e", ((Process.getThreadPriority(0) + 20) >> 6) + 30, (-16776943) - Color.rgb(0, 0, 0), true, objArr5);
                return (WalletEventListener) o.e(DefaultWalletEventListener.class, a2, ((String) objArr5[0]).intern());
            }
        } catch (Exception e4) {
            g.c();
            Object[] objArr6 = new Object[1];
            c(16 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "\n\uffef\n\ufff9\b\b�\n\uffde\ufff9\ufffb\f\u0007\n\u0011\uffef\ufff9\u0004\u0004�\f\uffdd\u000e�\u0006\f￤\u0001\u000b\f�\u0006�", (Process.myTid() >> 22) + 33, 275 - View.MeasureSpec.getSize(0), false, objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            c(20 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\u0006\uffc1\u0007\u0010\u0016\u000f\u0005ￍ\uffc1\u0015\u0002\f\u0006\uffc1\u0005\u0006\u0007\u0002\u0016\r\u0015\uffef\u0010\uffc1￢\u000f\u0015\u0006\r\u0010\u0011\uffc1\u000f\u0010\u0015\n\u0007\n\u0004\u0002\u0015\n\u0010\u000f\uffc1\u0014\u0006\u0013\u0017\n\u0004", 51 - TextUtils.getCapsMode("", 0, 0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 265, false, objArr7);
            g.d(intern2, ((String) objArr7[0]).intern());
            return new DefaultWalletEventListener();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cd\e$c.smali */
    public static final class c {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static boolean c;
        private static final List<Runnable> e;
        private static char[] f;
        private static boolean g;
        private static int h;
        private static boolean i;
        private static int j;
        private static int m;
        private final Handler a;
        private final WalletEventListener b;
        private final WalletNotificationServiceCallback d;

        static void b() {
            f = new char[]{61600, 61655, 61632, 61647, 61649, 61601, 61636, 61650, 61645, 61628, 61634, 61637, 61651, 61646, 61622, 61641, 61618, 61821, 61576, 61644, 61648, 61654, 61635, 61638, 61633};
            g = true;
            i = true;
            j = 782102877;
        }

        static void init$0() {
            $$a = new byte[]{85, 91, 121, -13};
            $$b = 64;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void v(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                int r8 = r8 * 2
                int r8 = r8 + 1
                int r7 = 121 - r7
                byte[] r0 = o.cd.e.c.$$a
                int r6 = r6 * 4
                int r6 = r6 + 4
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r4 = r6
                r7 = r8
                r3 = r2
                goto L2c
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r8) goto L25
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L25:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r5
            L2c:
                int r6 = r6 + 1
                int r8 = r8 + r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cd.e.c.v(byte, byte, byte, java.lang.Object[]):void");
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            m = 1;
            b();
            ViewConfiguration.getEdgeSlop();
            c = false;
            e = new ArrayList();
            int i2 = h + 73;
            m = i2 % 128;
            int i3 = i2 % 2;
        }

        c(Handler handler, WalletNotificationServiceCallback walletNotificationServiceCallback, WalletEventListener walletEventListener) {
            this.a = handler;
            this.d = walletNotificationServiceCallback;
            this.b = walletEventListener;
        }

        public final synchronized void d() {
            switch (!c) {
                default:
                    int i2 = m + Opcodes.LSUB;
                    h = i2 % 128;
                    int i3 = i2 % 2;
                    g.c();
                    Object[] objArr = new Object[1];
                    u(null, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 127, null, "\u008d\u0083\u008c\u008b\u0085\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    u(null, KeyEvent.keyCodeFromString("") + 127, null, "\u0084\u008e\u0087\u0088\u0088\u0083\u0091\u0085\u0083\u0090\u0090\u008a\u008f\u0084\u0083\u0089\u008e", objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    c = true;
                    Iterator<Runnable> it = e.iterator();
                    while (true) {
                        switch (it.hasNext() ? 'C' : '[') {
                            case 'C':
                                int i4 = h + 99;
                                m = i4 % 128;
                                if (i4 % 2 == 0) {
                                    e.d.a.post(it.next());
                                    throw null;
                                }
                                e.d.a.post(it.next());
                            default:
                                e.clear();
                        }
                    }
                case false:
                    break;
            }
        }

        public final synchronized void c() {
            int i2 = m;
            int i3 = i2 + 49;
            h = i3 % 128;
            int i4 = i3 % 2;
            switch (c ? '\n' : 'S') {
                case '\n':
                    int i5 = i2 + 51;
                    h = i5 % 128;
                    int i6 = i5 % 2;
                    g.c();
                    Object[] objArr = new Object[1];
                    u(null, 127 - (ViewConfiguration.getTouchSlop() >> 8), null, "\u008d\u0083\u008c\u008b\u0085\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    u(null, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0084\u008e\u0087\u0088\u0088\u0083\u0091\u0085\u0083\u0090\u0090\u008a\u008f\u0083\u0088\u008e\u0090\u008b", objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    c = false;
                    int i7 = m + 67;
                    h = i7 % 128;
                    int i8 = i7 % 2;
            }
        }

        private synchronized void c(Runnable runnable, boolean z) {
            switch (z ? '\b' : (char) 19) {
                default:
                    int i2 = m + 13;
                    int i3 = i2 % 128;
                    h = i3;
                    int i4 = i2 % 2;
                    switch (c) {
                        case true:
                            break;
                        default:
                            int i5 = i3 + 89;
                            m = i5 % 128;
                            int i6 = i5 % 2;
                            g.c();
                            Object[] objArr = new Object[1];
                            u(null, 127 - View.MeasureSpec.getSize(0), null, "\u008d\u0083\u008c\u008b\u0085\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            u(null, 127 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), null, "\u0085\u0084\u0083\u0082\u0083\u0092\u0098\u0084\u0087\u008d\u0083\u0085\u0088\u0087\u0098\u0083\u008d\u0092\u0093\u0092\u0084\u0083\u0089\u008e\u0092\u0084\u008e\u0087\u0088\u0088\u0083\u0088\u0092\u0085\u0083\u0090\u0090\u008a\u0096\u0092\u008e\u0084\u0092\u0085\u0095\u0097\u0092\u0084\u008e\u0087\u0088\u0088\u0083\u0088\u0092\u0085\u0083\u0090\u0090\u008a\u0096\u0092\u0088\u0083\u008d\u0087\u0095\u0094\u0083\u008d\u0092\u0085\u0084\u0083\u0082\u0083\u0092\u0093\u0092\u0085\u0084\u0083\u0082\u0081\u0085\u008d\u008e\u0089\u0083\u008d", objArr2);
                            g.d(intern, ((String) objArr2[0]).intern());
                            e.add(runnable);
                            int i7 = m + 1;
                            h = i7 % 128;
                            int i8 = i7 % 2;
                            break;
                    }
                    break;
                case 19:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    u(null, TextUtils.indexOf("", "", 0) + 127, null, "\u008d\u0083\u008c\u008b\u0085\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    u(null, 127 - (ViewConfiguration.getFadingEdgeLength() >> 16), null, "\u0085\u0084\u0083\u0082\u0083\u0092\u0098\u0084\u0087\u008c\u008b\u0085\u008a\u0089\u0088\u0087\u0099\u0092\u0093\u0092\u0085\u0084\u0083\u0082\u0081\u0085\u008d\u008e\u0089\u0083\u008d", objArr4);
                    g.d(intern2, ((String) objArr4[0]).intern());
                    this.a.post(runnable);
                    break;
            }
        }

        public final void a(final Context context) {
            int i2 = h + 11;
            m = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda8
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.y(context);
                }
            }, true);
            int i4 = h + 23;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void y(Context context) {
            int i2 = m + 75;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? 'c' : '@') {
                case '@':
                    this.d.onWalletLoaded(context);
                    this.b.onWalletLoaded(context);
                    return;
                default:
                    this.d.onWalletLoaded(context);
                    this.b.onWalletLoaded(context);
                    throw null;
            }
        }

        public final void d(final Context context) {
            int i2 = m + 79;
            h = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda10
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.x(context);
                }
            }, true);
            int i4 = m + 53;
            h = i4 % 128;
            int i5 = i4 % 2;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void x(Context context) {
            int i2 = m + Opcodes.DDIV;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    this.d.onSettingsUpdated(context);
                    this.b.onWalletSettingUpdated(context);
                    int i3 = m + 109;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                default:
                    this.d.onSettingsUpdated(context);
                    this.b.onWalletSettingUpdated(context);
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void t(Context context) {
            int i2 = h + 31;
            m = i2 % 128;
            int i3 = i2 % 2;
            this.b.onWalletProductsUpdated(context);
            int i4 = h + 73;
            m = i4 % 128;
            switch (i4 % 2 != 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public final void c(final Context context) {
            Runnable runnable;
            int i2 = m + 81;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? ' ' : '0') {
                case ' ':
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda7
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.t(context);
                        }
                    };
                    break;
                default:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda7
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.t(context);
                        }
                    };
                    break;
            }
            c(runnable, true);
            int i3 = h + 83;
            m = i3 % 128;
            switch (i3 % 2 == 0 ? 'B' : '\\') {
                case Opcodes.DUP2 /* 92 */:
                    return;
                default:
                    int i4 = 79 / 0;
                    return;
            }
        }

        public final void e(final Context context) {
            int i2 = m + 47;
            h = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda1
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.p(context);
                }
            }, false);
            int i4 = h + 97;
            m = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void p(Context context) {
            int i2 = m + 73;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? '7' : '(') {
                case '7':
                    this.d.onCountersUpdated(context);
                    this.b.onWalletCountersUpdated(context);
                    throw null;
                default:
                    this.d.onCountersUpdated(context);
                    this.b.onWalletCountersUpdated(context);
                    int i3 = h + 49;
                    m = i3 % 128;
                    int i4 = i3 % 2;
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void q(Context context) {
            int i2 = m + 51;
            h = i2 % 128;
            int i3 = i2 % 2;
            this.d.onCardsUpdated(context);
            int i4 = h + 27;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        public final void b(final Context context) {
            Runnable runnable;
            int i2 = m + Opcodes.DNEG;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 5 : (char) 7) {
                case 5:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda0
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.q(context);
                        }
                    };
                    break;
                default:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda0
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.q(context);
                        }
                    };
                    break;
            }
            c(runnable, true);
            int i3 = m + 37;
            h = i3 % 128;
            switch (i3 % 2 != 0 ? 'W' : (char) 24) {
                case 24:
                    return;
                default:
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void b(Context context, String str, List list) {
            int i2 = m + 7;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    this.d.onEmvApplicationActivationRequired(context, str, list);
                    int i3 = h + 85;
                    m = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case false:
                            int i4 = 21 / 0;
                            return;
                        default:
                            return;
                    }
                default:
                    this.d.onEmvApplicationActivationRequired(context, str, list);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public final void e(final Context context, final String str, final List<EmvApplicationActivationMethod> list) {
            int i2 = m + 67;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? '`' : 'D') {
                case Opcodes.IADD /* 96 */:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda3
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.b(context, str, list);
                        }
                    }, false);
                    break;
                default:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda3
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.b(context, str, list);
                        }
                    }, true);
                    break;
            }
            int i3 = h + 93;
            m = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    int i4 = 57 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void r(Context context) {
            int i2 = m + Opcodes.DDIV;
            h = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    this.d.onEmvApplicationCredentialsUpdated(context);
                    return;
                default:
                    this.d.onEmvApplicationCredentialsUpdated(context);
                    int i3 = 82 / 0;
                    return;
            }
        }

        public final void g(final Context context) {
            int i2 = m + Opcodes.LSHL;
            h = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda2
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.r(context);
                }
            }, true);
            int i4 = h + 5;
            m = i4 % 128;
            int i5 = i4 % 2;
        }

        public final void a(final Context context, final WalletLockReason walletLockReason) {
            int i2 = m + 37;
            h = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda12
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.c(context, walletLockReason);
                }
            }, false);
            int i4 = m + 27;
            h = i4 % 128;
            switch (i4 % 2 != 0 ? '^' : '\'') {
                case '\'':
                    return;
                default:
                    int i5 = 59 / 0;
                    return;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void c(Context context, WalletLockReason walletLockReason) {
            int i2 = h + 69;
            m = i2 % 128;
            switch (i2 % 2 == 0 ? '!' : '?') {
                case '!':
                    this.d.onWalletLocked(context, walletLockReason);
                    this.b.onWalletLocked(context, walletLockReason);
                    int i3 = 94 / 0;
                    return;
                default:
                    this.d.onWalletLocked(context, walletLockReason);
                    this.b.onWalletLocked(context, walletLockReason);
                    return;
            }
        }

        public final void f(final Context context) {
            int i2 = h + 89;
            m = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda6
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.s(context);
                        }
                    }, true);
                    break;
                default:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda6
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.s(context);
                        }
                    }, false);
                    break;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void s(Context context) {
            int i2 = h + 75;
            m = i2 % 128;
            switch (i2 % 2 == 0 ? '6' : Typography.greater) {
                case '>':
                    this.d.onWalletUnlocked(context);
                    this.b.onWalletUnlocked(context);
                    return;
                default:
                    this.d.onWalletUnlocked(context);
                    this.b.onWalletUnlocked(context);
                    int i3 = 4 / 0;
                    return;
            }
        }

        public final void j(final Context context) {
            Runnable runnable;
            int i2 = m + 51;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? 'A' : 'Z') {
                case 'Z':
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda11
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.k(context);
                        }
                    };
                    break;
                default:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda11
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.k(context);
                        }
                    };
                    break;
            }
            c(runnable, false);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void k(Context context) {
            int i2 = m + 23;
            h = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    this.d.onLogout(context);
                    this.b.onWalletLogout(context);
                    break;
                default:
                    this.d.onLogout(context);
                    this.b.onWalletLogout(context);
                    int i3 = 7 / 0;
                    break;
            }
            int i4 = m + 39;
            h = i4 % 128;
            switch (i4 % 2 != 0 ? (char) 18 : 'P') {
                case 18:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        public final void h(final Context context) {
            int i2 = m + Opcodes.DREM;
            h = i2 % 128;
            int i3 = i2 % 2;
            c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda13
                @Override // java.lang.Runnable
                public final void run() {
                    e.c.this.n(context);
                }
            }, false);
            int i4 = m + 45;
            h = i4 % 128;
            switch (i4 % 2 != 0 ? (char) 21 : (char) 30) {
                case 30:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void n(Context context) {
            int i2 = h + 49;
            m = i2 % 128;
            int i3 = i2 % 2;
            this.d.onWalletDeleted(context);
            this.b.onWalletDeleted(context);
            int i4 = m + 9;
            h = i4 % 128;
            int i5 = i4 % 2;
        }

        public final void i(final Context context) {
            Runnable runnable;
            int i2 = m + 33;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda4
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.l(context);
                        }
                    };
                    break;
                default:
                    runnable = new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda4
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.l(context);
                        }
                    };
                    break;
            }
            c(runnable, false);
            int i3 = h + 19;
            m = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return;
                default:
                    throw null;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void l(Context context) {
            int i2 = m + 9;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? '@' : ',') {
                case '@':
                    this.d.onCustomerCredentialsReset(context);
                    this.b.onCustomerCredentialsReset(context);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    this.d.onCustomerCredentialsReset(context);
                    this.b.onCustomerCredentialsReset(context);
                    int i3 = h + 87;
                    m = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            return;
                        default:
                            int i4 = 31 / 0;
                            return;
                    }
            }
        }

        public final void d(final Context context, final Date date) {
            int i2 = h + Opcodes.LUSHR;
            m = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda5
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.b(context, date);
                        }
                    }, false);
                    break;
                default:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda5
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.b(context, date);
                        }
                    }, true);
                    break;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void b(Context context, Date date) {
            int i2 = h + 7;
            m = i2 % 128;
            int i3 = i2 % 2;
            this.d.onSunsetScheduled(context, date);
            this.b.onAppSunsetScheduled(context, date);
            int i4 = m + Opcodes.LNEG;
            h = i4 % 128;
            switch (i4 % 2 != 0 ? '(' : Typography.greater) {
                case '>':
                    return;
                default:
                    int i5 = 82 / 0;
                    return;
            }
        }

        public final void m(final Context context) {
            int i2 = m + 59;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda9
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.o(context);
                        }
                    }, false);
                    break;
                default:
                    c(new Runnable() { // from class: o.cd.e$c$$ExternalSyntheticLambda9
                        @Override // java.lang.Runnable
                        public final void run() {
                            e.c.this.o(context);
                        }
                    }, true);
                    break;
            }
        }

        /* JADX INFO: Access modifiers changed from: private */
        public /* synthetic */ void o(Context context) {
            int i2 = h + 59;
            m = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    this.d.onLostEligibility(context);
                    this.b.onDeviceEligibilityLost(context);
                    return;
                default:
                    this.d.onLostEligibility(context);
                    this.b.onDeviceEligibilityLost(context);
                    int i3 = 62 / 0;
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void u(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 856
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.cd.e.c.u(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void c(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 526
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cd.e.c(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
