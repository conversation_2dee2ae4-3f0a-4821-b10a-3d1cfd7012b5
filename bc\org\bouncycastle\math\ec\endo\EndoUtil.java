package bc.org.bouncycastle.math.ec.endo;

import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.math.ec.PreCompCallback;
import bc.org.bouncycastle.math.ec.PreCompInfo;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\EndoUtil.smali */
public abstract class EndoUtil {
    public static final String PRECOMP_NAME = "bc_endo";

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\EndoUtil$a.smali */
    class a implements PreCompCallback {
        final /* synthetic */ ECEndomorphism a;
        final /* synthetic */ ECPoint b;

        a(ECEndomorphism eCEndomorphism, ECPoint eCPoint) {
            this.a = eCEndomorphism;
            this.b = eCPoint;
        }

        private boolean a(EndoPreCompInfo endoPreCompInfo, ECEndomorphism eCEndomorphism) {
            return (endoPreCompInfo == null || endoPreCompInfo.getEndomorphism() != eCEndomorphism || endoPreCompInfo.getMappedPoint() == null) ? false : true;
        }

        public PreCompInfo precompute(PreCompInfo preCompInfo) {
            EndoPreCompInfo endoPreCompInfo = preCompInfo instanceof EndoPreCompInfo ? (EndoPreCompInfo) preCompInfo : null;
            if (a(endoPreCompInfo, this.a)) {
                return endoPreCompInfo;
            }
            ECPoint map = this.a.getPointMap().map(this.b);
            EndoPreCompInfo endoPreCompInfo2 = new EndoPreCompInfo();
            endoPreCompInfo2.setEndomorphism(this.a);
            endoPreCompInfo2.setMappedPoint(map);
            return endoPreCompInfo2;
        }
    }

    private static BigInteger a(BigInteger bigInteger, BigInteger bigInteger2, int i) {
        boolean z = bigInteger2.signum() < 0;
        BigInteger multiply = bigInteger.multiply(bigInteger2.abs());
        boolean testBit = multiply.testBit(i - 1);
        BigInteger shiftRight = multiply.shiftRight(i);
        if (testBit) {
            shiftRight = shiftRight.add(ECConstants.ONE);
        }
        return z ? shiftRight.negate() : shiftRight;
    }

    public static BigInteger[] decomposeScalar(ScalarSplitParameters scalarSplitParameters, BigInteger bigInteger) {
        int bits = scalarSplitParameters.getBits();
        BigInteger a2 = a(bigInteger, scalarSplitParameters.getG1(), bits);
        BigInteger a3 = a(bigInteger, scalarSplitParameters.getG2(), bits);
        return new BigInteger[]{bigInteger.subtract(a2.multiply(scalarSplitParameters.getV1A()).add(a3.multiply(scalarSplitParameters.getV2A()))), a2.multiply(scalarSplitParameters.getV1B()).add(a3.multiply(scalarSplitParameters.getV2B())).negate()};
    }

    public static ECPoint mapPoint(ECEndomorphism eCEndomorphism, ECPoint eCPoint) {
        return ((EndoPreCompInfo) eCPoint.getCurve().precompute(eCPoint, "bc_endo", new a(eCEndomorphism, eCPoint))).getMappedPoint();
    }
}
