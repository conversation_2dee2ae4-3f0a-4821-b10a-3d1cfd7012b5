package com.esotericsoftware.kryo.serializers;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport0.smali */
public final /* synthetic */ class ImmutableCollectionsSerializers$JdkImmutableMapSerializer$$ExternalSyntheticBackport0 {
    public static /* synthetic */ Map m(Map map) {
        HashMap hashMap = new HashMap(map.size());
        for (Map.Entry entry : map.entrySet()) {
            hashMap.put(Objects.requireNonNull(entry.getKey()), Objects.requireNonNull(entry.getValue()));
        }
        return Collections.unmodifiableMap(hashMap);
    }
}
