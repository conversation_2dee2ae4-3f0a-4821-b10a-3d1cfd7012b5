package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity;
import java.util.Date;
import java.util.HashSet;
import kotlin.text.Typography;
import o.an.h;
import o.ee.g;
import o.ee.i;
import o.ee.o;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\b.smali */
public final class b implements o.ep.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static b a;
    private static char[] d;
    private static char f;
    private static char g;
    private static char h;
    private static long i;
    private static char j;
    private static int l;
    private static int m;
    private final boolean b;
    private final SharedPreferences c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        m = 1;
        d();
        ExpandableListView.getPackedPositionGroup(0L);
        Process.myTid();
        TextUtils.indexOf("", "", 0, 0);
        SystemClock.currentThreadTimeMillis();
        View.combineMeasuredStates(0, 0);
        SystemClock.uptimeMillis();
        AudioTrack.getMinVolume();
        ExpandableListView.getPackedPositionGroup(0L);
        int i2 = m + 5;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    static void d() {
        d = new char[]{29020, 4852, 46700, 23151, 65444, 33762, 10039, 52082, 27829, 61654, 37964, 14415, 56708, 25026, 1303, 43346, 19093, 60982, 29290, 6061, 48111, 24378, 58235, 33980, 10472, 52231, 20560, 62871, 39413, 15621, 49498, 25247, 1736, 43619, 20390, 54225, 30522, 7012, 48311, 16632, 58431, 34900, 11655, 45504, 21769, 63827, 39553, 59248, 33990, 8219, 52305, 27022, 11439, 20231, 60319, 1948, 41559, 56849, 31428, 38529, 12614, 44325, 51647, 26032, 32886, 15398, 22762, 62610, 6010, 46036, 12188, 19022, 58892, 715, 48774, 55666, 29977, 37364, 3496, 11438, 20240, 60357, 1966, 41560, 56840, 31442, 38552, 12615, 44338, 51649, 26044, 32864, 15378, 22752, 62625, 5989, 46032, 12165, 19060, 58909, 11414, 20266, 60412, 1970, 41594, 56878, 31486, 38590, 12665, 44308, 51656, 25986, 32861, 15360, 22743, 62596, 5962, 46064, 12206, 19060, 58941, 762, 48830, 55666, 11438, 20240, 60357, 1976, 41559, 56855, 31438, 38529, 12613, 44336, 51701, 25993, 32886, 15406, 22756, 62627, 6010, 11449, 20224, 60354, 1941, 41594, 56836, 31443, 38537, 41722, 49501, 26013, 35295, 11280, 20587, 62594, 6351, 48896, 9062, 18367, 60412, 3639, 45671, 54964, 11405, 20276, 60385, 1969, 41588, 56870, 31377, 38621, 12569, 44389, 51619, 26092, 32809, 15475, 22711, 62719, 5949, 46033, 12229, 18949, 58959, 657, 48775, 55578, 30045, 37285, 3509, 43128, 50285, 24752, 40176, 16232, 23409, 63425, 4690, 36426, 10889, 18131, 57669, 7433, 47567, 54758, 28706, 60516, 2298, 42150, 50999, 25460, 40941, 14855, 2096, 27549, 53078, 9016, 34513, 64159, 24156, 45602, 5583, 35241, 60778, 57461, 33746, 10001, 52037, 28341, 4811, 46620, 23110};
        i = -640612549665403019L;
        j = (char) 56497;
        f = (char) 1478;
        h = (char) 26543;
        g = (char) 33867;
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = 109;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.$$a
            int r8 = r8 + 102
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r8
            r4 = r2
            r8 = r7
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r3 = -r3
            int r6 = r6 + r3
            int r7 = r7 + 1
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.o(int, short, short, java.lang.Object[]):void");
    }

    @Override // o.ep.a
    public final /* synthetic */ void e(Activity activity, a.c cVar, i iVar, o.eo.e eVar, o.ep.e eVar2, h.d dVar, o.ee.h hVar) {
        int i2 = m + 75;
        l = i2 % 128;
        int i3 = i2 % 2;
        a(cVar, eVar);
        int i4 = l + 79;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                int i5 = 55 / 0;
                return;
        }
    }

    public static b b(Context context) {
        if (a == null) {
            a = new b(context);
            int i2 = m + Opcodes.DREM;
            l = i2 % 128;
            int i3 = i2 % 2;
        }
        b bVar = a;
        switch (bVar.b ? (char) 20 : '6') {
            case 20:
                return bVar;
            default:
                int i4 = l + Opcodes.LUSHR;
                m = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    private b(Context context) {
        boolean d2 = d(context);
        this.b = d2;
        Object[] objArr = new Object[1];
        k((char) ((Process.myPid() >> 22) + 24051), View.resolveSize(0, 0), 47 - ExpandableListView.getPackedPositionGroup(0L), objArr);
        this.c = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        if (!d2) {
            c(context);
        }
    }

    private static void c(Context context) {
        int i2 = l + 99;
        m = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 26 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (52191 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), TextUtils.getOffsetAfter("", 0) + 47, TextUtils.getCapsMode("", 0, 0) + 5, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k((char) (24051 - Color.red(0)), View.resolveSizeAndState(0, 0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 47, objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        n("\uf89d䒮夰ꦃ", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 2, objArr4);
        edit.remove(((String) objArr4[0]).intern()).apply();
        int i4 = l + Opcodes.LUSHR;
        m = i4 % 128;
        int i5 = i4 % 2;
    }

    private static boolean d(Context context) {
        String a2;
        int i2 = m + 23;
        l = i2 % 128;
        try {
            switch (i2 % 2 == 0) {
                case true:
                    Object[] objArr = new Object[1];
                    k((char) Color.green(0), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 51, (ViewConfiguration.getTouchSlop() >> 8) + 27, objArr);
                    a2 = o.a(context, ((String) objArr[0]).intern());
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    k((char) Color.green(0), Opcodes.LNEG << (ViewConfiguration.getGlobalActionKeyTimeout() > 1L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 1L ? 0 : -1)), 42 - (ViewConfiguration.getTouchSlop() - 26), objArr2);
                    a2 = o.a(context, ((String) objArr2[0]).intern());
                    break;
            }
            boolean parseBoolean = Boolean.parseBoolean(a2);
            int i3 = m + 71;
            l = i3 % 128;
            switch (i3 % 2 != 0 ? '\t' : 'A') {
                case '\t':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return parseBoolean;
            }
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    @Override // o.ep.a
    public final void b(a.InterfaceC0042a<String> interfaceC0042a) {
        int i2 = m + Opcodes.DDIV;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", Process.getGidForName("") + 27, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) ((Process.getThreadPriority(0) + 20) >> 6), 79 - KeyEvent.getDeadChar(0, 0), 21 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        n("㼰耯뼧豜ᘆ澓\uf267ⱼ煣၅捈翾⋌辤䷚ᎏꓰ给鴤ﾇ﹃؏㼰耯", View.MeasureSpec.getSize(0) + 24, objArr3);
        interfaceC0042a.e((a.InterfaceC0042a<String>) ((String) objArr3[0]).intern());
        int i4 = m + 11;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.ep.a
    public final void a(a.InterfaceC0042a<String> interfaceC0042a) {
        int i2 = l + 91;
        m = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", View.getDefaultSize(0, 0) + 26, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("㵰\ude36\ueadeꭋ䭁簆ᇌ\uf54a멫犻ᴴỚ㬂꺃\udc9d⬄\ua8c6䋭➇ꬩ\ue4a8\udfaf", 20 - ImageFormat.getBitsPerPixel(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 100 - ExpandableListView.getPackedPositionGroup(0L), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 23, objArr3);
        interfaceC0042a.e((a.InterfaceC0042a<String>) ((String) objArr3[0]).intern());
        int i4 = l + 37;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 88 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.ep.a
    public final void e(a.InterfaceC0042a<a.b> interfaceC0042a) {
        int i2 = m + Opcodes.LSUB;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 26 - (ViewConfiguration.getTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("㵰\ude36\ueadeꭋ䭁簆ᇌ\uf54a멫犻ᴴỚЩ杮갟䎧\ude1e\uf334\ueadeꭋ⛔આᐦ욌\u1af1뻘", (ViewConfiguration.getJumpTapTimeout() >> 16) + 25, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        interfaceC0042a.e((a.InterfaceC0042a<a.b>) a.b.c);
        int i4 = m + Opcodes.LNEG;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.ep.a
    public final void d(o.ep.a.InterfaceC0042a<java.util.List<o.ep.e>> r14) {
        /*
            Method dump skipped, instructions count: 258
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.d(o.ep.a$a):void");
    }

    private void a(a.c cVar, o.eo.e eVar) {
        int i2 = m + 75;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 26 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) ExpandableListView.getPackedPositionGroup(0L), 141 - KeyEvent.normalizeMetaState(0), View.MeasureSpec.getSize(0) + 8, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        cVar.a(e(eVar.s().a()));
        int i4 = l + 47;
        m = i4 % 128;
        int i5 = i4 % 2;
    }

    private String e(String str) {
        try {
            Long valueOf = Long.valueOf(new Date().getTime());
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            k((char) (36423 - View.MeasureSpec.getMode(0)), TextUtils.indexOf("", "", 0, 0) + Opcodes.FCMPL, Process.getGidForName("") + 16, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) TextUtils.getTrimmedLength(""), (ViewConfiguration.getTouchSlop() >> 8) + Opcodes.IF_ICMPLE, 51 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr2);
            bVar.d(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            n("帺怑\ufaf3\uf17a︆礄⅗\ud889⛔આ\uecd9\udbb6", 12 - TextUtils.indexOf("", "", 0), objArr3);
            bVar.d(((String) objArr3[0]).intern(), valueOf);
            Object[] objArr4 = new Object[1];
            k((char) (TextUtils.getOffsetBefore("", 0) + 9353), 214 - Color.alpha(0), TextUtils.getCapsMode("", 0, 0) + 11, objArr4);
            bVar.d(((String) objArr4[0]).intern(), str);
            SharedPreferences sharedPreferences = this.c;
            Object[] objArr5 = new Object[1];
            n("\uf89d䒮夰ꦃ", 3 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr5);
            HashSet hashSet = new HashSet(sharedPreferences.getStringSet(((String) objArr5[0]).intern(), new HashSet()));
            hashSet.add(bVar.b());
            SharedPreferences.Editor edit = this.c.edit();
            Object[] objArr6 = new Object[1];
            n("\uf89d䒮夰ꦃ", KeyEvent.normalizeMetaState(0) + 3, objArr6);
            edit.putStringSet(((String) objArr6[0]).intern(), hashSet).apply();
        } catch (o.eg.d e) {
            g.c();
            Object[] objArr7 = new Object[1];
            n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", KeyEvent.keyCodeFromString("") + 26, objArr7);
            String intern2 = ((String) objArr7[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr8 = new Object[1];
            n("帺怑\ufaf3\uf17a艴䴥☡鏆⛤\ud904螬쉥Ґⷲ\ue901ሄ糤ࠑ䧏\ude6a⧣쓅⅗\ud889\ue804\udd6b꼻㕜Ր앎ﲢ澽⏶箏艴䴥☡鏆\ue701ﹱ", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 39, objArr8);
            g.d(intern2, sb.append(((String) objArr8[0]).intern()).append(e.getMessage()).toString());
        }
        Object[] objArr9 = new Object[1];
        k((char) (Process.myPid() >> 22), 164 - TextUtils.indexOf("", "", 0, 0), 50 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr9);
        String intern3 = ((String) objArr9[0]).intern();
        int i2 = m + 49;
        l = i2 % 128;
        int i3 = i2 % 2;
        return intern3;
    }

    @Override // o.ep.a
    public final void a(Activity activity, a.InterfaceC0042a<Object> interfaceC0042a, i iVar) {
        int i2 = m + 75;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", ExpandableListView.getPackedPositionType(0L) + 26, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n("璡⠜탷鑂荱戀\uf300킕溇揬갟䎧\ude1e\uf334䷩溯", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 15, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = m + Opcodes.LSHL;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.ep.a
    public final void c(Activity activity) {
        String intern;
        Object obj;
        int i2 = m + 57;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 0 : '\\') {
            case 0:
                g.c();
                Object[] objArr = new Object[1];
                n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 51 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 10), objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                n("璡⠜탷鑂荱戀\uf300킕溇揬갟䎧\ude1e\uf334䷩溯", Opcodes.INEG >>> (ViewConfiguration.getLongPressTimeout() << 13), objArr2);
                obj = objArr2[0];
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 26 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                n("璡⠜탷鑂荱戀\uf300킕溇揬갟䎧\ude1e\uf334䷩溯", 15 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr4);
                obj = objArr4[0];
                break;
        }
        g.d(intern, ((String) obj).intern());
        int i3 = m + 57;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? '*' : Typography.greater) {
            case '*':
                throw null;
            default:
                return;
        }
    }

    @Override // o.ep.b
    public final void c(Activity activity, String str) {
        g.c();
        Object[] objArr = new Object[1];
        n("쎡邷ﱺ\ueac2⧍걹㨻弊\uf89d䒮ᚪ䢐啘枕ᬻ\uedba䩰픲昝ﴒ✪⯺硃⸃螺䩒", 26 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (52432 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), Color.red(0) + 225, '8' - AndroidCharacter.getMirror('0'), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Intent intent = new Intent(activity, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr3 = new Object[1];
        n("癜俤\ufffa殝\uf538⇱鄯밳媃簾ﳚᒜቜ섓\ua87d샜壆쑠ﳚᒜ", KeyEvent.normalizeMetaState(0) + 20, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        n("桟畬刪줅네⥖⺈驋煣၅\uf3f2⊜", ImageFormat.getBitsPerPixel(0) + 12, objArr4);
        activity.startActivity(intent.putExtra(intern2, ((String) objArr4[0]).intern()));
        int i2 = l + 69;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 578
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.k(char, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void n(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.n(java.lang.String, int, java.lang.Object[]):void");
    }
}
