package androidx.core.content;

import android.content.ClipData;
import androidx.core.util.Consumer;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\content\IntentSanitizer$Api16Impl$Api31Impl.smali */
public class IntentSanitizer$Api16Impl$Api31Impl {
    private IntentSanitizer$Api16Impl$Api31Impl() {
    }

    static void checkOtherMembers(int i, ClipData.Item item, Consumer<String> penalty) {
        if (item.getHtmlText() != null || item.getIntent() != null || item.getTextLinks() != null) {
            penalty.accept("ClipData item at position " + i + " contains htmlText, textLinks or intent: " + item);
        }
    }
}
