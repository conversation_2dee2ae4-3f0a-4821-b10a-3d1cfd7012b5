package o.es;

import fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControlUpdate;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import o.er.p;
import o.v.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\es\e.smali */
public final class e {
    private static int a = 0;
    private static int e = 1;
    private final o.eo.e c;
    private final List<d<?>> d = new ArrayList();

    public e(o.eo.e eVar) {
        this.c = eVar;
    }

    public final n a() throws WalletValidationException {
        o.eo.e eVar = this.c;
        p pVar = new p(eVar, (o.er.e) eVar.H());
        n nVar = new n(pVar.c(), this.c, pVar.b(), pVar, this.d);
        int i = (a + 46) - 1;
        e = i % 128;
        int i2 = i % 2;
        return nVar;
    }

    public final void b(d<?> dVar) {
        int i = e + 93;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                this.d.add(dVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.d.add(dVar);
                return;
        }
    }

    public final List<TransactionControlUpdate<?>> e() {
        ArrayList arrayList = new ArrayList();
        Iterator<d<?>> it = this.d.iterator();
        int i = (e + 12) - 1;
        a = i % 128;
        int i2 = i % 2;
        while (it.hasNext()) {
            arrayList.add(new TransactionControlUpdate(it.next()));
            int i3 = (a + 68) - 1;
            e = i3 % 128;
            if (i3 % 2 == 0) {
            }
        }
        int i4 = a;
        int i5 = (i4 ^ 67) + ((i4 & 67) << 1);
        e = i5 % 128;
        int i6 = i5 % 2;
        return arrayList;
    }
}
