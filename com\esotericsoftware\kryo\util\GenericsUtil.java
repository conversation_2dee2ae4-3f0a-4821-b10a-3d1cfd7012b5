package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.KryoException;
import java.lang.reflect.Array;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.WildcardType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\GenericsUtil.smali */
public class GenericsUtil {
    public static Type resolveType(Class fromClass, Class toClass, Type type) {
        if (type instanceof Class) {
            return type;
        }
        if (type instanceof TypeVariable) {
            return resolveTypeVariable(fromClass, toClass, type, true);
        }
        if (type instanceof ParameterizedType) {
            return ((ParameterizedType) type).getRawType();
        }
        if (type instanceof GenericArrayType) {
            int dimensions = 1;
            while (true) {
                type = ((GenericArrayType) type).getGenericComponentType();
                if (!(type instanceof GenericArrayType)) {
                    break;
                }
                dimensions++;
            }
            Type componentType = resolveType(fromClass, toClass, type);
            return !(componentType instanceof Class) ? type : dimensions == 1 ? Array.newInstance((Class<?>) componentType, 0).getClass() : Array.newInstance((Class<?>) componentType, new int[dimensions]).getClass();
        }
        if (type instanceof WildcardType) {
            Type upperBound = ((WildcardType) type).getUpperBounds()[0];
            if (upperBound != Object.class) {
                return resolveType(fromClass, toClass, upperBound);
            }
            Type[] lowerBounds = ((WildcardType) type).getLowerBounds();
            return lowerBounds.length != 0 ? resolveType(fromClass, toClass, lowerBounds[0]) : Object.class;
        }
        throw new KryoException("Unable to resolve type: " + type);
    }

    private static Type resolveTypeVariable(Class fromClass, Class current, Type type, boolean first) {
        Type genericSuper = current.getGenericSuperclass();
        if (!(genericSuper instanceof ParameterizedType)) {
            return type;
        }
        Class superClass = current.getSuperclass();
        if (superClass != fromClass) {
            Type resolved = resolveTypeVariable(fromClass, superClass, type, false);
            if (resolved instanceof Class) {
                return resolved;
            }
            type = resolved;
        }
        String name = type.toString();
        TypeVariable[] params = superClass.getTypeParameters();
        int n = params.length;
        for (int i = 0; i < n; i++) {
            TypeVariable param = params[i];
            if (param.getName().equals(name)) {
                Type arg = ((ParameterizedType) genericSuper).getActualTypeArguments()[i];
                if (arg instanceof Class) {
                    return arg;
                }
                if (!(arg instanceof ParameterizedType) && !(arg instanceof GenericArrayType)) {
                    if (arg instanceof TypeVariable) {
                        return first ? type : arg;
                    }
                }
                return resolveType(fromClass, current, arg);
            }
        }
        return type;
    }

    public static Type[] resolveTypeParameters(Class fromClass, Class toClass, Type type) {
        if (type instanceof ParameterizedType) {
            Type[] actualArgs = ((ParameterizedType) type).getActualTypeArguments();
            int n = actualArgs.length;
            Type[] generics = new Type[n];
            for (int i = 0; i < n; i++) {
                generics[i] = resolveType(fromClass, toClass, actualArgs[i]);
            }
            return generics;
        }
        if (type instanceof GenericArrayType) {
            do {
                type = ((GenericArrayType) type).getGenericComponentType();
            } while (type instanceof GenericArrayType);
            return resolveTypeParameters(fromClass, toClass, type);
        }
        return null;
    }
}
