package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.params.ParametersWithIV;
import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j1.smali */
public class j1 extends k7 {
    private byte[] b;
    private byte[] c;
    private byte[] d;
    private byte[] e;
    private int f;
    private BlockCipher g;
    private boolean h;
    private int i;

    public j1(BlockCipher blockCipher, int i) {
        super(blockCipher);
        this.g = null;
        if (i > blockCipher.getBlockSize() * 8 || i < 8 || i % 8 != 0) {
            throw new IllegalArgumentException("CFB" + i + " not supported");
        }
        this.g = blockCipher;
        this.f = i / 8;
        this.b = new byte[blockCipher.getBlockSize()];
        this.c = new byte[blockCipher.getBlockSize()];
        this.d = new byte[blockCipher.getBlockSize()];
        this.e = new byte[this.f];
    }

    private byte b(byte b) {
        if (this.i == 0) {
            this.g.processBlock(this.c, 0, this.d, 0);
        }
        byte[] bArr = this.e;
        int i = this.i;
        bArr[i] = b;
        byte[] bArr2 = this.d;
        int i2 = i + 1;
        this.i = i2;
        byte b2 = (byte) (b ^ bArr2[i]);
        int i3 = this.f;
        if (i2 == i3) {
            this.i = 0;
            byte[] bArr3 = this.c;
            System.arraycopy(bArr3, i3, bArr3, 0, bArr3.length - i3);
            byte[] bArr4 = this.e;
            byte[] bArr5 = this.c;
            int length = bArr5.length;
            int i4 = this.f;
            System.arraycopy(bArr4, 0, bArr5, length - i4, i4);
        }
        return b2;
    }

    private byte c(byte b) {
        if (this.i == 0) {
            this.g.processBlock(this.c, 0, this.d, 0);
        }
        byte[] bArr = this.d;
        int i = this.i;
        byte b2 = (byte) (b ^ bArr[i]);
        byte[] bArr2 = this.e;
        int i2 = i + 1;
        this.i = i2;
        bArr2[i] = b2;
        int i3 = this.f;
        if (i2 == i3) {
            this.i = 0;
            byte[] bArr3 = this.c;
            System.arraycopy(bArr3, i3, bArr3, 0, bArr3.length - i3);
            byte[] bArr4 = this.e;
            byte[] bArr5 = this.c;
            int length = bArr5.length;
            int i4 = this.f;
            System.arraycopy(bArr4, 0, bArr5, length - i4, i4);
        }
        return b2;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k7
    protected byte a(byte b) throws DataLengthException, IllegalStateException {
        return this.h ? c(b) : b(b);
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return this.g.getAlgorithmName() + "/CFB" + (this.f * 8);
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return this.f;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        this.h = z;
        if (!(cipherParameters instanceof ParametersWithIV)) {
            reset();
            if (cipherParameters != null) {
                this.g.init(true, cipherParameters);
                return;
            }
            return;
        }
        ParametersWithIV parametersWithIV = (ParametersWithIV) cipherParameters;
        byte[] iv = parametersWithIV.getIV();
        int length = iv.length;
        byte[] bArr = this.b;
        if (length < bArr.length) {
            System.arraycopy(iv, 0, bArr, bArr.length - iv.length, iv.length);
            int i = 0;
            while (true) {
                byte[] bArr2 = this.b;
                if (i >= bArr2.length - iv.length) {
                    break;
                }
                bArr2[i] = 0;
                i++;
            }
        } else {
            System.arraycopy(iv, 0, bArr, 0, bArr.length);
        }
        reset();
        if (parametersWithIV.getParameters() != null) {
            this.g.init(true, parametersWithIV.getParameters());
        }
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i, byte[] bArr2, int i2) throws DataLengthException, IllegalStateException {
        a(bArr, i, this.f, bArr2, i2);
        return this.f;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void reset() {
        byte[] bArr = this.b;
        System.arraycopy(bArr, 0, this.c, 0, bArr.length);
        Arrays.fill(this.e, (byte) 0);
        this.i = 0;
        this.g.reset();
    }
}
