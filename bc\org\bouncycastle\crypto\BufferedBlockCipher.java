package bc.org.bouncycastle.crypto;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.g6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\BufferedBlockCipher.smali */
public class BufferedBlockCipher {
    protected byte[] a;
    protected int b;
    protected boolean c;
    protected BlockCipher d;
    protected o5 e;
    protected boolean f;
    protected boolean g;

    BufferedBlockCipher() {
    }

    public int doFinal(byte[] bArr, int i) throws DataLengthException, IllegalStateException, InvalidCipherTextException {
        try {
            int i2 = this.b;
            if (i + i2 > bArr.length) {
                throw new g6("output buffer too short for doFinal()");
            }
            int i3 = 0;
            if (i2 != 0) {
                if (!this.f) {
                    throw new DataLengthException("data not block size aligned");
                }
                BlockCipher blockCipher = this.d;
                byte[] bArr2 = this.a;
                blockCipher.processBlock(bArr2, 0, bArr2, 0);
                int i4 = this.b;
                this.b = 0;
                System.arraycopy(this.a, 0, bArr, i, i4);
                i3 = i4;
            }
            return i3;
        } finally {
            reset();
        }
    }

    public int getBlockSize() {
        return this.d.getBlockSize();
    }

    public int getOutputSize(int i) {
        return (this.g && this.c) ? i + this.b + this.d.getBlockSize() + 2 : i + this.b;
    }

    public BlockCipher getUnderlyingCipher() {
        return this.d;
    }

    public int getUpdateOutputSize(int i) {
        int i2 = i + this.b;
        return i2 - (this.g ? this.c ? (i2 % this.a.length) - (this.d.getBlockSize() + 2) : i2 % this.a.length : i2 % this.a.length);
    }

    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        this.c = z;
        reset();
        this.d.init(z, cipherParameters);
    }

    public int processByte(byte b, byte[] bArr, int i) throws DataLengthException, IllegalStateException {
        byte[] bArr2 = this.a;
        int i2 = this.b;
        int i3 = i2 + 1;
        this.b = i3;
        bArr2[i2] = b;
        if (i3 != bArr2.length) {
            return 0;
        }
        int processBlock = this.d.processBlock(bArr2, 0, bArr, i);
        this.b = 0;
        return processBlock;
    }

    public int processBytes(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException, IllegalStateException {
        int i4;
        int i5;
        int i6;
        if (i2 < 0) {
            throw new IllegalArgumentException("Can't have a negative input length!");
        }
        int blockSize = getBlockSize();
        int updateOutputSize = getUpdateOutputSize(i2);
        if (updateOutputSize > 0 && updateOutputSize + i3 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        byte[] bArr3 = this.a;
        int length = bArr3.length;
        int i7 = this.b;
        int i8 = length - i7;
        if (i2 > i8) {
            System.arraycopy(bArr, i, bArr3, i7, i8);
            i6 = this.d.processBlock(this.a, 0, bArr2, i3) + 0;
            this.b = 0;
            i5 = i2 - i8;
            i4 = i + i8;
            o5 o5Var = this.e;
            if (o5Var != null) {
                int multiBlockSize = i5 / o5Var.getMultiBlockSize();
                if (multiBlockSize > 0) {
                    i6 += this.e.processBlocks(bArr, i4, multiBlockSize, bArr2, i3 + i6);
                    int multiBlockSize2 = multiBlockSize * this.e.getMultiBlockSize();
                    i5 -= multiBlockSize2;
                    i4 += multiBlockSize2;
                }
            } else {
                while (i5 > this.a.length) {
                    i6 += this.d.processBlock(bArr, i4, bArr2, i3 + i6);
                    i5 -= blockSize;
                    i4 += blockSize;
                }
            }
        } else {
            i4 = i;
            i5 = i2;
            i6 = 0;
        }
        System.arraycopy(bArr, i4, this.a, this.b, i5);
        int i9 = this.b + i5;
        this.b = i9;
        byte[] bArr4 = this.a;
        if (i9 != bArr4.length) {
            return i6;
        }
        int processBlock = i6 + this.d.processBlock(bArr4, 0, bArr2, i3 + i6);
        this.b = 0;
        return processBlock;
    }

    public void reset() {
        int i = 0;
        while (true) {
            byte[] bArr = this.a;
            if (i >= bArr.length) {
                this.b = 0;
                this.d.reset();
                return;
            } else {
                bArr[i] = 0;
                i++;
            }
        }
    }

    public BufferedBlockCipher(BlockCipher blockCipher) {
        this.d = blockCipher;
        if (blockCipher instanceof o5) {
            o5 o5Var = (o5) blockCipher;
            this.e = o5Var;
            this.a = new byte[o5Var.getMultiBlockSize()];
        } else {
            this.e = null;
            this.a = new byte[blockCipher.getBlockSize()];
        }
        boolean z = false;
        this.b = 0;
        String algorithmName = blockCipher.getAlgorithmName();
        int indexOf = algorithmName.indexOf(47) + 1;
        boolean z2 = indexOf > 0 && algorithmName.startsWith("PGP", indexOf);
        this.g = z2;
        if (z2 || (blockCipher instanceof l7)) {
            this.f = true;
            return;
        }
        if (indexOf > 0 && algorithmName.startsWith("OpenPGP", indexOf)) {
            z = true;
        }
        this.f = z;
    }
}
