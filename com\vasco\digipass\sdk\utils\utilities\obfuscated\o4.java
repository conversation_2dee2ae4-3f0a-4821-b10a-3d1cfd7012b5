package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\o4.smali */
public class o4 extends k4 {
    private BigInteger c;

    public o4(BigInteger bigInteger, m4 m4Var) {
        super(false, m4Var);
        this.c = bigInteger;
    }

    public BigInteger b() {
        return this.c;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k4
    public boolean equals(Object obj) {
        return (obj instanceof o4) && ((o4) obj).b().equals(this.c) && super.equals(obj);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k4
    public int hashCode() {
        return this.c.hashCode() ^ super.hashCode();
    }
}
