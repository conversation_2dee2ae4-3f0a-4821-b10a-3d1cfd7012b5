package o.bc;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.l;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.y.a;
import o.y.b;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bc\e.smali */
public final class e extends b<InterfaceC0030e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int d;
    private static int j;
    public o.ba.e a;
    String c;
    boolean e;

    /* renamed from: o.bc.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bc\e$e.smali */
    public interface InterfaceC0030e {
        void b();

        void c(o.bb.d dVar);

        void d();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        j = 1;
        m();
        int i = j + 13;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                break;
            default:
                int i2 = 3 / 0;
                break;
        }
    }

    static void init$0() {
        $$d = new byte[]{62, -87, 120, -83};
        $$e = 212;
    }

    static void m() {
        b = new char[]{50938, 50836, 50840, 50855, 50854, 50854, 50851, 50849, 50851, 50851, 50850, 50879, 50877, 50860, 50839, 50852, 50858, 50858, 50848, 50937, 50851, 50851, 50849, 50851, 50854, 50854, 50855, 50826, 50917, 50827, 50852, 50834, 50860, 50877, 50879, 50941, 50854, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50849, 50836, 50835, 50853, 50856, 50855, 50853, 50935, 50849, 50853, 50849, 50859, 50833, 50835, 50850, 50876, 50855, 50859, 50856, 50851, 50851, 50854, 50854};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r8 = 122 - r8
            int r7 = r7 * 4
            int r7 = 4 - r7
            byte[] r0 = o.bc.e.$$d
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = -r6
            int r8 = r8 + r6
            int r6 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bc.e.n(int, short, int, java.lang.Object[]):void");
    }

    static /* synthetic */ void b(e eVar) {
        int i = d + 57;
        j = i % 128;
        int i2 = i % 2;
        eVar.c();
        int i3 = j + 89;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 45 / 0;
                return;
            default:
                return;
        }
    }

    public e(Context context, InterfaceC0030e interfaceC0030e, o.ei.c cVar) {
        super(context, interfaceC0030e, cVar, o.bb.e.b);
        this.e = false;
    }

    public final void c(o.ba.e eVar) {
        int i = j + 19;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        l("\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000", new int[]{0, 19, 0, 14}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001", new int[]{19, 16, 0, 10}, false, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.a = eVar;
        c();
        int i3 = j + 3;
        d = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.y.b
    public final a<?> b() {
        int i = j + 87;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                if (this.e) {
                    return new c(this);
                }
                d dVar = new d(this);
                int i2 = d + 21;
                j = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 15 : 'W') {
                    case Opcodes.POP /* 87 */:
                        int i3 = 70 / 0;
                        return dVar;
                    default:
                        return dVar;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i = d + 41;
        j = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        l("\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000", new int[]{0, 19, 0, 14}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = d + 93;
        j = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bc\e$d.smali */
    public static final class d extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static char a;
        private static char[] b;
        private static long c;
        private static int d;
        private static char[] e;
        private static int f;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            f = 1;
            e = new char[]{17044, 17043, 30521, 30523, 17053, 30562, 30520, 30586, 30557, 30511, 30587, 30572, 30526, 30528, 17051, 30560, 30542, 30564, 30541, 30571, 17052, 30525, 17040, 30574, 30538, 30563, 30570, 30519, 17054, 17049, 30552, 30529, 30518, 30539, 17046, 30508, 17041, 30573, 17048, 30585, 30568, 30522, 30498, 30561, 30591, 30556, 30588, 30555, 17055, 17042, 17047, 30566, 30581, 30569, 30582, 30534, 30589, 30533, 30524, 30530, 30567, 30565, 30540, 30527};
            a = (char) 17053;
            b = new char[]{11432, 63138, 39085, 41656, 17567, 28288, 12429, 55960, 64742, 34543, 43226, 29438, 5325, 16068, 31876, 42646, 51342, 62080, 5288, 16055, 24734, 35513, 44228, 54976, 63699, 8897, 17604, 28385, 63898, 9092, 19853, 30597, 37280, 48047, 58814, 4002, 10745, 21459, 32214, 42967, 49632, 60397, 5628, 8301, 64100, 37987, 30155, 11449, 63136, 39083, 41634, 17548, 28331, 12426, 55966, 64743, 34510, 43259, 29435, 5324, 16066, 49357, 60131, 35884, 22066, 30761, 574, 9223, 52754, 36892, 47697, 23588, 26145, 2138, 53886, 62535, 40519, 41040, 19011, 28068, 14240, 55725, 58296, 34182, 44943, 29114, 7070, 15853, 51172, 59833, 46079, 21958, 32709, 476, 11153, 52527, 38702, 47404, 17215, 25869, 11399, 63150, 39161, 41618, 17542, 28303, 12447, 55960, 64763, 34540, 43256, 29413, 5312, 16078, 49367, 60049, 35850, 22062, 30781, 564, 9289, 52787, 36892, 47634, 23660, 26216, 2159, 53876, 62541, 11454, 63136, 39093, 41661, 17548, 28309, 12464, 55957, 11425, 63138, 39100, 41620, 17541, 28296, 12446, 55960, 64747, 34536, 43253, 29432, 5341, 16088, 53002, 5394, 31496, 16676, 42805, 36152, 54062, 14632, 8027, 25944, 19269, 37192, 63341, 56680, 29876, 44717, 49318, 64175, 7297, 13990, 26759, 33427, 42218, 57027, 61686, 10998, 19649, 26319, 39104, 45806, 54305, 3647, 8228, 23091, 31754, 38431, 51217, 57948, 1065, 15916, 20570, 35443, 44036, 50780, 63558, 4691, 13728, 28601, 33207, 48040, 56772, 63373, 10648, 17296, 26091, 40955, 45553, 60408};
            c = 3508515568194483905L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(int r7, byte r8, int r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 * 2
                int r7 = r7 + 4
                int r8 = r8 * 2
                int r8 = 1 - r8
                int r9 = r9 + 69
                byte[] r0 = o.bc.e.d.$$d
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L16
                r9 = r8
                r3 = r9
                r4 = r2
                r8 = r7
                goto L2c
            L16:
                r3 = r2
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L1b:
                int r4 = r3 + 1
                byte r5 = (byte) r7
                r1[r3] = r5
                if (r4 != r9) goto L2a
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2a:
                r3 = r0[r8]
            L2c:
                int r7 = r7 + r3
                int r8 = r8 + 1
                r3 = r4
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.d.C(int, byte, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{12, -43, 42, 57};
            $$e = 50;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = f + 99;
            d = i % 128;
            switch (i % 2 != 0 ? 'V' : (char) 6) {
                case 6:
                    break;
                default:
                    int i2 = 78 / 0;
                    break;
            }
        }

        d(e eVar) {
            super(eVar, false);
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
        @Override // o.y.a
        public final void a() {
            int i = f + 1;
            d = i % 128;
            switch (i % 2 != 0) {
            }
            o.bn.e.b().a(g(), false, true);
            super.a();
            int i2 = f + 33;
            d = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c, o.y.a
        public final String c() {
            StringBuilder append = new StringBuilder().append(((e) e()).a());
            Object[] objArr = new Object[1];
            w((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 21, "'33;\u000b2\u0011\u001f45\u001b\u001f\u0011\u001f\u001a\u001b\u000f*\u0016/㘍", (byte) (28 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), objArr);
            String obj = append.append(((String) objArr[0]).intern()).toString();
            int i = f + Opcodes.LNEG;
            d = i % 128;
            switch (i % 2 != 0 ? Typography.less : '\n') {
                case '<':
                    throw null;
                default:
                    return obj;
            }
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = d + 67;
            f = i % 128;
            boolean z = i % 2 == 0;
            long packedPositionForChild = ExpandableListView.getPackedPositionForChild(0, 0);
            switch (z) {
                case false:
                    Object[] objArr = new Object[1];
                    w(15 - (packedPositionForChild > 0L ? 1 : (packedPositionForChild == 0L ? 0 : -1)), ";32\u000b7\u0013\u001b12\u001c\u001f\u0016㘡㘡\"\u0012", (byte) (43 - (ViewConfiguration.getPressedStateDuration() >> 16)), objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(Opcodes.IUSHR % (packedPositionForChild > 0L ? 1 : (packedPositionForChild == 0L ? 0 : -1)), ";32\u000b7\u0013\u001b12\u001c\u001f\u0016㘡㘡\"\u0012", (byte) (ViewConfiguration.getPressedStateDuration() * 38 * 80), objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(View.resolveSize(0, 0) + 19, "\u0007;\t,\u0004\n;\u001f\u0012=;\u001f\u0010%\u0004\u000e\u0005\u0016㗸", (byte) (80 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), objArr);
            o.cf.d dVar = new o.cf.d(context, 0, ((String) objArr[0]).intern());
            int i = f + 17;
            d = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Code restructure failed: missing block: B:23:0x005c, code lost:
        
            if (r1.a() != false) goto L22;
         */
        /* JADX WARN: Code restructure failed: missing block: B:24:0x00c3, code lost:
        
            r12 = new java.lang.Object[1];
            B((char) (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16), android.view.KeyEvent.normalizeMetaState(0), 15 - (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)), r12);
            r0.d(((java.lang.String) r12[0]).intern(), r4);
         */
        /* JADX WARN: Code restructure failed: missing block: B:25:0x0078, code lost:
        
            r1 = o.bc.e.d.d + com.esotericsoftware.asm.Opcodes.DREM;
            o.bc.e.d.f = r1 % 128;
         */
        /* JADX WARN: Code restructure failed: missing block: B:26:0x0083, code lost:
        
            if ((r1 % 2) != 0) goto L25;
         */
        /* JADX WARN: Code restructure failed: missing block: B:27:0x0085, code lost:
        
            r13 = new java.lang.Object[1];
            w(android.view.View.resolveSize(0, 1) * 7, "\u0013\u000f\u000b2/\u001f\u000b2\u000b/?\u000e\u0012\u001b\u001a\u0002㗯", (byte) (com.esotericsoftware.asm.Opcodes.ISHL % android.text.TextUtils.getTrimmedLength("")), r13);
            r1 = r13[0];
         */
        /* JADX WARN: Code restructure failed: missing block: B:28:0x00af, code lost:
        
            r0.d(((java.lang.String) r1).intern(), r4);
            r1 = o.bc.e.d.f + com.esotericsoftware.asm.Opcodes.LSUB;
            o.bc.e.d.d = r1 % 128;
            r1 = r1 % 2;
         */
        /* JADX WARN: Code restructure failed: missing block: B:29:0x009b, code lost:
        
            r13 = new java.lang.Object[1];
            w(android.view.View.resolveSize(0, 0) + 17, "\u0013\u000f\u000b2/\u001f\u000b2\u000b/?\u000e\u0012\u001b\u001a\u0002㗯", (byte) (android.text.TextUtils.getTrimmedLength("") + 71), r13);
            r1 = r13[0];
         */
        /* JADX WARN: Code restructure failed: missing block: B:31:0x0075, code lost:
        
            if (r1.a() != false) goto L22;
         */
        /* JADX WARN: Failed to find 'out' block for switch in B:21:0x0043. Please report as an issue. */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.eg.b m() throws o.eg.d {
            /*
                Method dump skipped, instructions count: 760
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.d.m():o.eg.b");
        }

        @Override // o.y.c
        public final j n() {
            int i = d + Opcodes.LUSHR;
            int i2 = i % 128;
            f = i2;
            int i3 = i % 2;
            int i4 = i2 + 91;
            d = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = d;
            int i2 = i + 33;
            f = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 73;
            f = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:37:0x00cf  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x006d  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void c(o.eg.b r14) throws o.eg.d {
            /*
                Method dump skipped, instructions count: 624
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.d.c(o.eg.b):void");
        }

        @Override // o.y.c
        public final o.bb.a d(o.cf.g gVar) {
            switch (gVar.b().e() == o.cf.a.v ? ')' : '5') {
                case ')':
                    int i = d + 5;
                    f = i % 128;
                    int i2 = i % 2;
                    o.bb.a aVar = o.bb.a.aa;
                    int i3 = f + 55;
                    d = i3 % 128;
                    int i4 = i3 % 2;
                    return aVar;
                default:
                    o.bb.a d2 = super.d(gVar);
                    int i5 = f + Opcodes.LNEG;
                    d = i5 % 128;
                    switch (i5 % 2 != 0) {
                        case true:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return d2;
                    }
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0034. Please report as an issue. */
        private static boolean e(o.eg.b bVar, String str) throws o.eg.d {
            switch (bVar.b(str)) {
                case true:
                    int i = f + Opcodes.LSHL;
                    d = i % 128;
                    int i2 = i % 2;
                    if (bVar.i(str).intValue() == 0) {
                        int i3 = d + 35;
                        f = i3 % 128;
                        switch (i3 % 2 == 0 ? 'Q' : 'O') {
                        }
                        return true;
                    }
                    break;
            }
            int i4 = f + Opcodes.DNEG;
            d = i4 % 128;
            int i5 = i4 % 2;
            return false;
        }

        @Override // o.y.c
        public final boolean o() {
            int i = d + 91;
            f = i % 128;
            switch (i % 2 == 0 ? 'a' : 'P') {
                case 'P':
                    return false;
                default:
                    return true;
            }
        }

        @Override // o.y.c
        public final boolean r() {
            int i = f;
            int i2 = i + 53;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 41;
            d = i4 % 128;
            switch (i4 % 2 != 0 ? ']' : (char) 19) {
                case Opcodes.DUP2_X1 /* 93 */:
                    throw null;
                default:
                    return false;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + Opcodes.LMUL;
            f = i % 128;
            int i2 = i % 2;
            ((e) e()).j().b();
            ((e) e()).e = true;
            e.b((e) e());
            int i3 = d + 17;
            f = i3 % 128;
            switch (i3 % 2 == 0 ? '0' : '\n') {
                case '0':
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = f + 51;
            d = i % 128;
            int i2 = i % 2;
            ((e) e()).j().c(dVar);
            int i3 = f + 79;
            d = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:111:0x0028, code lost:
        
            if (r25 != null) goto L16;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void w(int r24, java.lang.String r25, byte r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 1046
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.d.w(int, java.lang.String, byte, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void B(char r21, int r22, int r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 574
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.d.B(char, int, int, java.lang.Object[]):void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bc\e$c.smali */
    public static final class c extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static boolean b;
        private static boolean c;
        private static int d;
        private static char[] e;
        private static int j;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            j = 1;
            e = new char[]{61939, 61709, 61761, 61762, 61750, 61745, 61752, 61749, 61747, 61741, 61721, 61759, 61740, 61754, 61730, 61763, 61755, 61949, 61697, 61701, 61699, 61948, 61950, 61698, 61696, 61702, 61744, 61756, 61753, 61715, 61746};
            b = true;
            c = true;
            d = 782102990;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, short r7, short r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.bc.e.c.$$d
                int r7 = r7 + 4
                int r6 = r6 * 4
                int r6 = r6 + 1
                int r8 = 121 - r8
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                r7 = r6
                goto L35
            L19:
                r3 = r2
            L1a:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r4 = r3 + 1
                int r7 = r7 + 1
                if (r3 != r6) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                r3 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r5
            L35:
                int r6 = r6 + r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r5
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.c.B(byte, short, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{45, -21, -97, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
            $$e = 92;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = j + Opcodes.DSUB;
            a = i % 128;
            switch (i % 2 != 0 ? 'N' : (char) 3) {
                case 3:
                    return;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = a + 59;
            j = i % 128;
            switch (i % 2 == 0 ? '\t' : '\b') {
                case '\t':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        c(e eVar) {
            super(eVar, false);
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c, o.y.a
        public final String c() {
            StringBuilder append = new StringBuilder().append(((e) e()).a());
            Object[] objArr = new Object[1];
            w(null, 127 - (ViewConfiguration.getFadingEdgeLength() >> 16), null, "\u0091\u0090\u008a\u008f\u0086\u008e\u0088\u008d\u008c\u008b\u0086\u0084\u008a\u0089\u0088\u0084\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
            String obj = append.append(((String) objArr[0]).intern()).toString();
            int i = a + Opcodes.DREM;
            j = i % 128;
            int i2 = i % 2;
            return obj;
        }

        @Override // o.y.c
        public final String l() {
            int i = a + 17;
            j = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(null, 127 - TextUtils.indexOf("", "", 0, 0), null, "\u0086\u008e\u0088\u008d\u008c\u008b\u0086\u0084\u008a\u0089\u0088\u0084\u0087\u0086\u0085\u0084\u0083\u008a", objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = j + 67;
            a = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(null, 126 - ImageFormat.getBitsPerPixel(0), null, "\u0098\u0097\u0092\u0098\u0097\u0094\u009a\u0096\u0099\u0094\u0092\u0098\u0095\u0097\u0096\u0095\u0094\u0093\u0092", objArr);
            o.cf.d dVar = new o.cf.d(context, 1, ((String) objArr[0]).intern());
            int i = j + 51;
            a = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(null, 127 - (ViewConfiguration.getLongPressTimeout() >> 16), null, "\u0086\u009f\u008c\u009e\u0087\u008c\u0088\u0084\u008a\u009d\u009c\u0088\u009b\u0087\u008c\u0089", objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).c);
            int i = a + 63;
            j = i % 128;
            switch (i % 2 == 0) {
                case true:
                    throw null;
                default:
                    return bVar;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = j;
            int i2 = i + Opcodes.DNEG;
            a = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 47;
            a = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a + 93;
            j = i % 128;
            Object obj = null;
            switch (i % 2 == 0 ? (char) 31 : '\r') {
                case 31:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.y.c
        public final boolean o() {
            int i = j;
            int i2 = i + 65;
            a = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 71;
            a = i4 % 128;
            int i5 = i4 % 2;
            return false;
        }

        @Override // o.y.c
        public final boolean r() {
            int i = j;
            int i2 = i + 27;
            a = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 23;
            a = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    return false;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final void q() {
            int i = j + Opcodes.LSHL;
            a = i % 128;
            switch (i % 2 != 0) {
                case true:
                    o.b.c.i(g());
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    o.b.c.i(g());
                    int i2 = a + 83;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = j + 43;
            a = i % 128;
            int i2 = i % 2;
            ((e) e()).j().d();
            int i3 = j + 43;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = a + 13;
            j = i % 128;
            int i2 = i % 2;
            ((e) e()).j().c(dVar);
            int i3 = a + 27;
            j = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
        /* JADX WARN: Type inference failed for: r1v1 */
        /* JADX WARN: Type inference failed for: r1v27, types: [byte[]] */
        private static void w(java.lang.String r20, int r21, int[] r22, java.lang.String r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 984
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bc.e.c.w(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
        }
    }

    public final boolean k() {
        int i = d + 29;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        boolean z = this.e;
        int i4 = i2 + 33;
        d = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v36, types: [byte[]] */
    private static void l(String str, int[] iArr, boolean z, Object[] objArr) {
        int length;
        char[] cArr;
        int i;
        int i2;
        int i3;
        int i4;
        char[] cArr2;
        int i5;
        int i6;
        ?? r0 = str;
        int i7 = $11 + Opcodes.DNEG;
        $10 = i7 % 128;
        int i8 = 2;
        if (i7 % 2 != 0) {
            throw null;
        }
        switch (r0 != 0 ? '!' : '0') {
            case '!':
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                int i9 = $10 + 19;
                $11 = i9 % 128;
                int i10 = i9 % 2;
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i11 = 0;
        int i12 = iArr[0];
        int i13 = 1;
        int i14 = iArr[1];
        int i15 = iArr[2];
        int i16 = iArr[3];
        char[] cArr3 = b;
        switch (cArr3 == null) {
            case false:
                int i17 = $10 + 93;
                $11 = i17 % 128;
                if (i17 % 2 == 0) {
                    length = cArr3.length;
                    cArr = new char[length];
                    i = 0;
                } else {
                    length = cArr3.length;
                    cArr = new char[length];
                    i = 0;
                }
                while (i < length) {
                    int i18 = $10 + 5;
                    $11 = i18 % 128;
                    switch (i18 % i8 == 0 ? i13 : i11) {
                        case 0:
                            try {
                                Object[] objArr2 = new Object[i13];
                                objArr2[i11] = Integer.valueOf(cArr3[i]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    i3 = i15;
                                    i4 = length;
                                } else {
                                    Class cls = (Class) o.e.a.c(10 - Process.getGidForName(""), (char) Color.blue(i11), 42 - TextUtils.lastIndexOf("", '0', i11, i11));
                                    byte b2 = (byte) i11;
                                    byte b3 = b2;
                                    i3 = i15;
                                    i4 = length;
                                    Object[] objArr3 = new Object[1];
                                    n(b2, b3, (byte) (b3 + 2), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr[i] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i++;
                                length = i4;
                                i15 = i3;
                                i8 = 2;
                                i11 = 0;
                                i13 = 1;
                                break;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            int i19 = i15;
                            int i20 = length;
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr3[i])};
                                Object obj2 = o.e.a.s.get(1951085128);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, (char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 43);
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    n(b4, b5, (byte) (b5 + 2), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj2);
                                }
                                cArr[i] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                i <<= 0;
                                length = i20;
                                i15 = i19;
                                i8 = 2;
                                i11 = 0;
                                i13 = 1;
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                    }
                }
                i2 = i15;
                cArr3 = cArr;
                break;
            default:
                i2 = i15;
                break;
        }
        char[] cArr4 = new char[i14];
        System.arraycopy(cArr3, i12, cArr4, 0, i14);
        if (bArr != null) {
            char[] cArr5 = new char[i14];
            lVar.d = 0;
            char c2 = 0;
            while (lVar.d < i14) {
                if (bArr[lVar.d] == 1) {
                    int i21 = $10 + 25;
                    $11 = i21 % 128;
                    int i22 = i21 % 2;
                    int i23 = lVar.d;
                    try {
                        Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                        Object obj3 = o.e.a.s.get(2016040108);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 11, (char) (AndroidCharacter.getMirror('0') - '0'), KeyEvent.getDeadChar(0, 0) + 448);
                            byte b6 = (byte) 0;
                            byte b7 = b6;
                            Object[] objArr7 = new Object[1];
                            n(b6, b7, (byte) (b7 + 3), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(2016040108, obj3);
                        }
                        cArr5[i23] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } else {
                    int i24 = lVar.d;
                    try {
                        Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                        Object obj4 = o.e.a.s.get(804049217);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 9, (char) View.resolveSizeAndState(0, 0, 0), 206 - TextUtils.indexOf((CharSequence) "", '0', 0, 0));
                            byte b8 = (byte) 0;
                            byte b9 = b8;
                            Object[] objArr9 = new Object[1];
                            n(b8, b9, b9, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(804049217, obj4);
                        }
                        cArr5[i24] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                c2 = cArr5[lVar.d];
                try {
                    Object[] objArr10 = {lVar, lVar};
                    Object obj5 = o.e.a.s.get(-2112603350);
                    if (obj5 == null) {
                        Class cls5 = (Class) o.e.a.c((Process.myTid() >> 22) + 11, (char) (Process.myTid() >> 22), View.resolveSizeAndState(0, 0, 0) + 259);
                        byte b10 = (byte) 0;
                        byte b11 = b10;
                        Object[] objArr11 = new Object[1];
                        n(b10, b11, (byte) (b11 | 56), objArr11);
                        obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                        o.e.a.s.put(-2112603350, obj5);
                    }
                    ((Method) obj5).invoke(null, objArr10);
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
            }
            cArr4 = cArr5;
        }
        switch (i16 > 0) {
            case false:
                break;
            default:
                char[] cArr6 = new char[i14];
                System.arraycopy(cArr4, 0, cArr6, 0, i14);
                int i25 = i14 - i16;
                System.arraycopy(cArr6, 0, cArr4, i25, i16);
                System.arraycopy(cArr6, i16, cArr4, 0, i25);
                break;
        }
        switch (z) {
            case false:
                break;
            default:
                int i26 = $11 + Opcodes.DMUL;
                $10 = i26 % 128;
                if (i26 % 2 != 0) {
                    cArr2 = new char[i14];
                    i5 = 1;
                } else {
                    cArr2 = new char[i14];
                    i5 = 0;
                }
                lVar.d = i5;
                while (lVar.d < i14) {
                    int i27 = $10 + 63;
                    $11 = i27 % 128;
                    if (i27 % 2 == 0) {
                        cArr2[lVar.d] = cArr4[(lVar.d * i14) << 0];
                        i6 = lVar.d - 1;
                    } else {
                        cArr2[lVar.d] = cArr4[(i14 - lVar.d) - 1];
                        i6 = lVar.d + 1;
                    }
                    lVar.d = i6;
                }
                cArr4 = cArr2;
                break;
        }
        switch (i2 <= 0) {
            case true:
                break;
            default:
                int i28 = $11 + 83;
                $10 = i28 % 128;
                int i29 = i28 % 2;
                int i30 = 0;
                while (true) {
                    lVar.d = i30;
                    if (lVar.d >= i14) {
                        break;
                    } else {
                        cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                        i30 = lVar.d + 1;
                    }
                }
        }
        objArr[0] = new String(cArr4);
    }
}
