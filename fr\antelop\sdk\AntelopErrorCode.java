package fr.antelop.sdk;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\AntelopErrorCode.smali */
public enum AntelopErrorCode {
    InternalError,
    NoProductAllowed,
    AndroidPermissionNotGranted,
    InvalidConfiguration,
    IssuerIdNotDefined,
    OperationOnGoing,
    WalletLocked,
    WalletDeleted,
    WalletNotActivated,
    ActivationRequired,
    CustomerCredentialsInvalid,
    NetworkNotAvailable,
    ProductNotSupportedBySdk,
    DeviceArchitectureNotSupported,
    GooglePlayServiceError,
    GooglePlayServicesMissing,
    GooglePlayServicesInvalid,
    GooglePlayServicesDisabled,
    GooglePlayServicesUpdating,
    GooglePlayServicesUpdateRequired,
    HmsMissing,
    HmsInvalid,
    HmsDisabled,
    HmsUpdateRequired,
    HmsNotSupported,
    InvalidCustomerCredentialsFormat,
    InvalidActivationCode,
    ExpiredActivationCode,
    OperationRefused,
    ScaTimeout,
    ScaDuplicated,
    ScaCancelledFromBackend,
    ScaInvalidPromptOverride,
    TransactionOnGoing,
    TransactionDeclined,
    TransactionDeclinedByPos,
    TransactionDeclinedByApplication,
    TransactionAborted,
    TransactionTimeout,
    TransactionConfigurationInvalid,
    NoFreshKeys,
    NoCard,
    ExpiredCard,
    MaximumAmountExceeded,
    CustomerAuthenticationImpossible,
    TransactionConditionNotVerified,
    TransactionContextChanged,
    NoCardSelected,
    OtherError,
    NfcDisconnection,
    LockedActivationCode,
    AlreadyUsedActivationCode,
    GooglePayWalletNotAvailable,
    SamsungPayWalletNotAvailable,
    UserCancelled,
    CardLocked,
    CardDeleted,
    AppIntegrityCheckFailed
}
