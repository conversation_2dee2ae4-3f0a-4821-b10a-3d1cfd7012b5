package o.r;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.security.keystore.KeyGenParameterSpec;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import java.security.PrivateKey;
import java.security.Provider;
import java.security.ProviderException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\r\a.smali */
public final class a implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final String b;
    private static char[] c;
    private static final String d;
    private static final String e;
    private static boolean f;
    private static char[] g;
    private static boolean h;
    private static int i;
    private static long j;
    private static int l;
    private static int m;
    private byte[] a;

    static void b() {
        c = new char[]{61801, 61576, 61599, 61580, 61578, 61803, 61590, 61584, 61593, 61595, 61810, 61596, 61594, 61813, 61577, 61818, 61592, 61589, 61765, 61776, 61581, 61572, 61591, 61583, 61582, 61598, 61795, 61807, 61796, 61809, 61800, 61586, 61802, 61585, 61819, 61587, 61597, 61783, 61782, 61780};
        f = true;
        h = true;
        i = 782102821;
        char[] cArr = new char[517];
        ByteBuffer.wrap("¨ñ\u001aäÌÕ¾À`ÔÒ£\u0084\u00adv\u00838\u0094êz\\j\u000e\u001dð\u0001¢{\u0014.Æ\u001c\u0088\u001ez\u000e-õ\u009fàA\u00943Ëå³W²\u0019ÐË\u0081½qo-Ñ^\u0083Bu5'$é\u001d[\u0003\r\u0014ðü¢ç\u0014\u0093ÆÊ\u0088°z²,\u009b\u009e\u0099@|2~ä^V\u0010\u0018yÊ\u000e¼\u0006n:Ð6\u0083Ñ,¨\u009e¨H³:\u0096ä\u009aVë\u0000÷ò\u008e¼Án+Ø-\u008a\u0001t\u0014&y\u0090hBA\fVþX©é\u001b\u0081Å¶··aÜÓÖ\u009dèOð9\bë\u0019U?\u0007*ñY£Qmgßrå5W \u0081\u0011ó\u0004-\u0010\u009fgÉi;GuP§¾\u0011®CÙ½Åï¿Yè\u008bÔÅÂ7Ä`7Ò3\f\u0000~\u0015¨\u007f\u001akT@\u0086\u000bðª\"¬\u009c\u008aÎ\u00828÷jþ¤Ï\u0016Ú@Í½?ï`Y\u0019\u008b\tÅa7$a\\ÓX\r¸\u007f¦©\u008b\u001b\u008bUù\u0087¬ñ¹#\u0092\u009dçÎ\u00118\u000bj%¤ , \u009eµH\u0084:\u0091ä\u0085Vò\u0000üòÒ¼Ån+Ø;\u008aLtP&*\u0090\u007fBM\fOþ_©¤\u001b±ÅÅ·\u0093aóÓù\u009d\u0081OÚ9 ë9U\u001e\u0007Zñe£wm]ß\u0006\u0089Dt±&¥\u0090\u0092B\u009c\fòþå¨\u008e\u001aÙÄ%¶3`\rÒ\u001e\u009czNi8dêNTZ\u0007±ñò£ÙmÐßÇ\u0089ß{ã%ï\u0097\b, \u009eµH\u0094:\u008bä\u009bVä\u0000úòÇ¼Än<Ø:\u008a\bt]&'\u0090;BF\fVþ\u0016©¡\u001b½Å\u008b·\u0095aæÓâ\u009dÑOÌ9&ë2U\u0019\u0007Zñy£}mNßO\u0089Dt°&°\u0090\u0090B\u0096\fäþ±¨\u0094\u001a\u009fÄ\n¶\u001c`&Ò(\u009cM·A\u0005aÓz¡_\u007fSÍ\"\u009b>i\"'\u001dõþCÅ\u0011ÑïÛ½±\u000b·q\u009cÃ\u0099\u0015\u0091g¤¹®\u000b\u0091]\u008d¯³áç3\u0005\u0085\u000f×:)>{MÍA\u001ftQdöfDk\u0092màC>M\u008c-Ú)(\u000b, \u009eµH\u0096:\u0087ä\u0081Vë\u0000åòÁ¼Ån+Ø;\u008aLtP&*\u0090~BP\fZþS©·\u001b Å\u008c·\u009daíxºÊ·\u001c·n¹°\u008f\u0002åTó¦ÕèË:9\u008c., \u009eµH\u0096:\u0087ä\u0081Vë\u0000åòÁ¼Ån+Ø;\u008aLtP&*\u0090oBZ\fLþS,¨\u009e¨H£:\u0081ä\u0099Ví\u0000ãòÆ¼Ön>Ø7\u008a\rt\u000e&bq\nÃ\u0017\u0015Ag\u0018¹a\u000bO]S¯5áN3\u009d\u0085\u0082×¿)µ{ÇÍí\u001f×QÚ£Òô8F3\u0098-ê;<U\u008eBÀS\u0012ud»¶\u008f\b\u008eZ\u009d¬åþ\u008e0Ý\u0082ÏÔÖ)X{\u001bÍ\"\u001f\u000fQd£[õ~GY\u0099\u008eë\u0092=\u008b\u008f\u0089ÁÂ\u0013ËeÖ·Ú\tÅZ\u0006¬\u0000þ\b0b\u0082IÔ\t&mxOÊ°\u001c\u009cn\u009b ¾\bòºölõ\u001e\u0091ÀÉr¹$¶Ö\u0080\u0098\u0090J;ü0®\u0019Æïtë¢ÓÐÏ\u000eÙ¼¡ê½O§ý©+¾Y\u009c\u0087\u00865øcí\u0091îßÚ\r0»4éF\u0017ZE ó\u007f!Mo\u0013\u009dXÊ¬xª¦\u008eÔØ\u0002ý°õþ\u008b,ÖZ \u0088v6\u0003d\u0015\u0092bÀ`\u000eZ¼\\êI\u0017«E»,§\u009e©H÷:\u0080ä\u0094Vö\u0000òò\u0080¼Ån!Ø\u007f\u008a\u000et\u0018&*\u0090\u007fBM\fZþD©¾\u001b¤Å\u0091·\u0097açÜÕnÁ¸ØÊû\u0014\u0088¦\u009eð¾\u0002\u0088L\u0099\u009ek(}zG,\u009b\u009e\u0095H\u0096:Ë".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 517);
        g = cArr;
        j = -7707794099313991994L;
    }

    static void init$0() {
        $$a = new byte[]{3, 85, -79, -50};
        $$b = 16;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r8 = r8 + 102
            byte[] r0 = o.r.a.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.o(int, short, short, java.lang.Object[]):void");
    }

    static {
        String intern;
        String intern2;
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        m = 1;
        b();
        ViewConfiguration.getScrollDefaultDelay();
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getLongPressTimeout();
        TextUtils.getOffsetBefore("", 0);
        Color.red(0);
        Color.rgb(0, 0, 0);
        TextUtils.indexOf((CharSequence) "", '0');
        TextUtils.indexOf("", "", 0, 0);
        ViewConfiguration.getScrollDefaultDelay();
        int i2 = m + Opcodes.LUSHR;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? 'W' : (char) 26) {
            case Opcodes.POP /* 87 */:
                Object[] objArr = new Object[1];
                k(null, 4373 >> Process.getGidForName(""), null, "\u0086¡\u009f", objArr);
                intern = ((String) objArr[0]).intern();
                break;
            default:
                Object[] objArr2 = new Object[1];
                k(null, Process.getGidForName("") + 128, null, "\u0086¡\u009f", objArr2);
                intern = ((String) objArr2[0]).intern();
                break;
        }
        int i3 = m + 73;
        l = i3 % 128;
        int i4 = i3 % 2;
        b = intern;
        switch (61) {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                Object[] objArr3 = new Object[1];
                n((char) (61517 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 501 - Color.alpha(0), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 12, objArr3);
                intern2 = ((String) objArr3[0]).intern();
                break;
            default:
                Object[] objArr4 = new Object[1];
                n((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 61515), Color.red(0) + 501, 12 - Color.argb(0, 0, 0, 0), objArr4);
                intern2 = ((String) objArr4[0]).intern();
                int i5 = m + 53;
                l = i5 % 128;
                int i6 = i5 % 2;
                break;
        }
        e = intern2;
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        n((char) Color.alpha(0), 513 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 3 - MotionEvent.axisFromString(""), objArr5);
        StringBuilder append = sb.append(((String) objArr5[0]).intern()).append(intern);
        Object[] objArr6 = new Object[1];
        k(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "§", objArr6);
        d = append.append(((String) objArr6[0]).intern()).append(intern2).toString();
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0018, code lost:
    
        if (android.os.Build.VERSION.SDK_INT >= 30) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static int c(android.hardware.biometrics.BiometricManager r2) {
        /*
            int r0 = o.r.a.l
            int r0 = r0 + 71
            int r1 = r0 % 128
            o.r.a.m = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 77
            goto L11
        Lf:
            r0 = 74
        L11:
            switch(r0) {
                case 77: goto L1b;
                default: goto L14;
            }
        L14:
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 30
            if (r0 < r1) goto L3b
        L1a:
            goto L2a
        L1b:
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 99
            if (r0 < r1) goto L24
            r0 = 91
            goto L26
        L24:
            r0 = 76
        L26:
            switch(r0) {
                case 76: goto L3b;
                default: goto L29;
            }
        L29:
            goto L1a
        L2a:
            r0 = 15
            int r2 = r2.canAuthenticate(r0)
            int r0 = o.r.a.m
            int r0 = r0 + 83
            int r1 = r0 % 128
            o.r.a.l = r1
            int r0 = r0 % 2
            goto L3f
        L3b:
            int r2 = r2.canAuthenticate()
        L3f:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.c(android.hardware.biometrics.BiometricManager):int");
    }

    /* JADX WARN: Code restructure failed: missing block: B:29:0x010a, code lost:
    
        if (androidx.core.content.ContextCompat.checkSelfPermission(r11, ((java.lang.String) r6[0]).intern()) == 0) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0135, code lost:
    
        o.ee.g.c();
        r2 = new java.lang.Object[1];
        n((char) (51604 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0')), 87 - android.graphics.Color.argb(0, 0, 0, 0), 55 - android.text.TextUtils.lastIndexOf("", '0', 0), r2);
        o.ee.g.d(r3, ((java.lang.String) r2[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0131, code lost:
    
        if (androidx.core.content.ContextCompat.checkSelfPermission(r11, ((java.lang.String) r6[0]).intern()) == 0) goto L35;
     */
    @Override // o.r.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(android.content.Context r11) {
        /*
            Method dump skipped, instructions count: 402
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.a(android.content.Context):boolean");
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x0022, code lost:
    
        if (android.os.Build.VERSION.SDK_INT >= 62) goto L17;
     */
    @Override // o.r.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean e(android.content.Context r11) {
        /*
            Method dump skipped, instructions count: 332
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.e(android.content.Context):boolean");
    }

    @Override // o.r.e
    public final void e() throws b {
        int i2 = l + Opcodes.DSUB;
        m = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(null, 127 - Color.argb(0, 0, 0, 0), null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, View.MeasureSpec.getMode(0) + 127, null, "\u0095\u008d\u0091¢\u0098", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            Object[] objArr3 = new Object[1];
            n((char) (39882 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (ViewConfiguration.getLongPressTimeout() >> 16) + 252, 15 - Gravity.getAbsoluteGravity(0, 0), objArr3);
            KeyStore keyStore = KeyStore.getInstance(((String) objArr3[0]).intern());
            keyStore.load(null);
            Object[] objArr4 = new Object[1];
            k(null, AndroidCharacter.getMirror('0') + 'O', null, "\u0089\u0097\u0084\u008a\u0092\u008a\u0082\u0099\u0097\u0084\u0098\u0094\u0092 ", objArr4);
            keyStore.deleteEntry(((String) objArr4[0]).intern());
            int i4 = l + 81;
            m = i4 % 128;
            int i5 = i4 % 2;
        } catch (IOException | KeyStoreException | NoSuchAlgorithmException | CertificateException e2) {
            g.c();
            Object[] objArr5 = new Object[1];
            k(null, View.MeasureSpec.getMode(0) + 127, null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            n((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 23859), 267 - TextUtils.indexOf("", ""), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 17, objArr6);
            g.a(intern2, ((String) objArr6[0]).intern(), e2);
            throw new b(e2.getMessage());
        }
    }

    @Override // o.r.e
    public final byte[] c(Context context) throws b {
        int i2 = m + 47;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(null, View.MeasureSpec.makeMeasureSpec(0, 0) + 127, null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n((char) (56013 - ImageFormat.getBitsPerPixel(0)), 284 - TextUtils.getCapsMode("", 0, 0), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 7, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            Object[] objArr3 = new Object[1];
            k(null, 127 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), null, "\u009d\u0090£", objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            n((char) (39881 - (ViewConfiguration.getJumpTapTimeout() >> 16)), 252 - (Process.myTid() >> 22), 15 - TextUtils.getOffsetBefore("", 0), objArr4);
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(intern2, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k(null, 127 - TextUtils.indexOf("", "", 0), null, "\u0089\u0097\u0084\u008a\u0092\u008a\u0082\u0099\u0097\u0084\u0098\u0094\u0092 ", objArr5);
            keyPairGenerator.initialize(new KeyGenParameterSpec.Builder(((String) objArr5[0]).intern(), 3).setUserAuthenticationRequired(true).setBlockModes(b).setEncryptionPaddings(e).setKeySize(2048).setUserAuthenticationValidityDurationSeconds(-1).build());
            KeyPair generateKeyPair = keyPairGenerator.generateKeyPair();
            g.c();
            Object[] objArr6 = new Object[1];
            k(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr6);
            String intern3 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            k(null, 126 - ExpandableListView.getPackedPositionChild(0L), null, "\u008d\u008d\u0082\u0085\u0085\u0091\u008d\u0093\u0094\u0093\u0082\u0089\u0096\u0083\u0084\u0089\u0085\u0096", objArr7);
            g.d(intern3, ((String) objArr7[0]).intern());
            byte[] encoded = generateKeyPair.getPublic().getEncoded();
            int i4 = l + 67;
            m = i4 % 128;
            int i5 = i4 % 2;
            return encoded;
        } catch (InvalidAlgorithmParameterException | NoSuchAlgorithmException | NoSuchProviderException | ProviderException e2) {
            throw new b(e2.getMessage(), e2);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x0088, code lost:
    
        if (r13 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x008a, code lost:
    
        r14 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x008d, code lost:
    
        switch(r14) {
            case 1: goto L21;
            default: goto L18;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0090, code lost:
    
        r0 = r0 + 5;
        o.r.a.l = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0097, code lost:
    
        if ((r0 % 2) == 0) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x014e, code lost:
    
        r0 = 'Z';
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0152, code lost:
    
        switch(r0) {
            case 90: goto L53;
            default: goto L52;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0155, code lost:
    
        o.ee.g.c();
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0175, code lost:
    
        r0 = new java.lang.Object[1];
        k(null, 43 / (android.view.ViewConfiguration.getEdgeSlop() * 51), null, "¢¢\u0091\u0097\u0093\u008d\u0084\u0093\u008c\u0082 \u0093\u0094\u0093\u008f\u0082\u0089\u0096\u0083\u0084\u0089\u0085\u009d\u008d\u0084", r0);
        r0 = ((java.lang.String) r0[0]).intern();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0171, code lost:
    
        o.ee.g.d(r6, r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x018a, code lost:
    
        r0 = o.r.a.l + 67;
        o.r.a.m = r0 % 128;
        r0 = r0 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0193, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0159, code lost:
    
        o.ee.g.c();
        r3 = new java.lang.Object[1];
        k(null, 127 - (android.view.ViewConfiguration.getEdgeSlop() >> 16), null, "¢¢\u0091\u0097\u0093\u008d\u0084\u0093\u008c\u0082 \u0093\u0094\u0093\u008f\u0082\u0089\u0096\u0083\u0084\u0089\u0085\u009d\u008d\u0084", r3);
        r0 = ((java.lang.String) r3[0]).intern();
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x014b, code lost:
    
        r0 = '2';
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x009d, code lost:
    
        if ((r13 instanceof java.security.PrivateKey) != false) goto L24;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x009f, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x00a0, code lost:
    
        r3 = (java.security.PrivateKey) r13;
        r14 = o.r.a.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x00a5, code lost:
    
        r0 = r0 + 33;
        o.r.a.l = r0 % 128;
        r0 = r0 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00ac, code lost:
    
        r0 = new java.lang.Object[]{r14};
        r14 = new java.lang.Object[1];
        k(null, (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)) + com.esotericsoftware.asm.Opcodes.IAND, null, "\u008a\u0082\u0095\u0092\u0084¡¦\u0087\u0089\u0092\u008c\u008a\u0085¦¥\u0096\u0083\u0096¤", r14);
        r11 = java.lang.Class.forName(((java.lang.String) r14[0]).intern());
        r10 = new java.lang.Object[1];
        n((char) (android.text.TextUtils.indexOf("", "") + 21524), 315 - android.view.KeyEvent.normalizeMetaState(0), 11 - (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), r10);
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x00ff, code lost:
    
        ((javax.crypto.Cipher) r11.getMethod(((java.lang.String) r10[0]).intern(), java.lang.String.class).invoke(null, r0)).init(2, r3);
        o.ee.g.c();
        r11 = new java.lang.Object[1];
        n((char) ((-1) - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0)), android.view.View.MeasureSpec.getMode(0) + 326, (android.view.ViewConfiguration.getScrollBarFadeDuration() >> 16) + 18, r11);
        o.ee.g.d(r6, ((java.lang.String) r11[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x012c, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x012d, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x012e, code lost:
    
        r3 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x0132, code lost:
    
        if (r3 != null) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x0134, code lost:
    
        throw r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0135, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0145, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0136, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0148, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x0142, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x013c, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x013f, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0139, code lost:
    
        r0 = e;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x008c, code lost:
    
        r14 = true;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:55:0x022b. Please report as an issue. */
    @Override // o.r.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean c() {
        /*
            Method dump skipped, instructions count: 614
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.c():boolean");
    }

    public static Cipher d() throws b {
        int i2 = l + 21;
        m = i2 % 128;
        int i3 = i2 % 2;
        try {
            Object[] objArr = new Object[1];
            n((char) (39880 - ExpandableListView.getPackedPositionChild(0L)), 252 - ExpandableListView.getPackedPositionGroup(0L), KeyEvent.getDeadChar(0, 0) + 15, objArr);
            KeyStore keyStore = KeyStore.getInstance(((String) objArr[0]).intern());
            keyStore.load(null);
            try {
                Object[] objArr2 = new Object[1];
                k(null, AndroidCharacter.getMirror('0') + 'O', null, "\u0089\u0097\u0084\u008a\u0092\u008a\u0082\u0099\u0097\u0084\u0098\u0094\u0092 ", objArr2);
                Key key = keyStore.getKey(((String) objArr2[0]).intern(), null);
                int i4 = l + 9;
                m = i4 % 128;
                int i5 = i4 % 2;
                PrivateKey privateKey = (PrivateKey) key;
                Object[] objArr3 = new Object[1];
                k(null, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0099\u0097\u0084\u008f\u008f\u0096\u008e¨\u0090¡\u008b\u008e§\u0086¡\u009f§\u009d\u0090£", objArr3);
                try {
                    Object[] objArr4 = {((String) objArr3[0]).intern()};
                    Object[] objArr5 = new Object[1];
                    k(null, 127 - Color.alpha(0), null, "\u008a\u0082\u0095\u0092\u0084¡¦\u0087\u0089\u0092\u008c\u008a\u0085¦¥\u0096\u0083\u0096¤", objArr5);
                    Class<?> cls = Class.forName(((String) objArr5[0]).intern());
                    Object[] objArr6 = new Object[1];
                    n((char) (21524 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), (ViewConfiguration.getEdgeSlop() >> 16) + 315, ExpandableListView.getPackedPositionChild(0L) + 12, objArr6);
                    Cipher cipher = (Cipher) cls.getMethod(((String) objArr6[0]).intern(), String.class).invoke(null, objArr4);
                    cipher.init(2, privateKey);
                    Provider provider = cipher.getProvider();
                    Object[] objArr7 = new Object[1];
                    n((char) View.MeasureSpec.getMode(0), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 344, 14 - (ViewConfiguration.getTapTimeout() >> 16), objArr7);
                    String intern = ((String) objArr7[0]).intern();
                    Object[] objArr8 = new Object[1];
                    n((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 23973), (ViewConfiguration.getWindowTouchSlop() >> 8) + 358, MotionEvent.axisFromString("") + 65, objArr8);
                    provider.setProperty(intern, ((String) objArr8[0]).intern());
                    int i6 = l + 51;
                    m = i6 % 128;
                    switch (i6 % 2 == 0 ? 'H' : '\\') {
                        case 'H':
                            int i7 = 46 / 0;
                            return cipher;
                        default:
                            return cipher;
                    }
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
            } catch (RuntimeException e2) {
                throw new b(e2.getMessage());
            }
        } catch (IOException | InvalidKeyException | KeyStoreException | NoSuchAlgorithmException | UnrecoverableKeyException | CertificateException | NoSuchPaddingException e3) {
            g.c();
            Object[] objArr9 = new Object[1];
            k(null, 126 - ImageFormat.getBitsPerPixel(0), null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr9);
            String intern2 = ((String) objArr9[0]).intern();
            Object[] objArr10 = new Object[1];
            k(null, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0093\u009b\u0093\u0097\u0087\u0084\u0089\u0092\u0082\u0085¥\u0082\u0093\u0094\u0093\u008a\u0082\u0095\u0092\u0084¡\u0089\u0082\u0099", objArr10);
            g.a(intern2, ((String) objArr10[0]).intern(), e3);
            throw new b(e3.getMessage());
        }
    }

    @Override // o.r.e
    public final void c(byte[] bArr) {
        g.c();
        Object[] objArr = new Object[1];
        k(null, 127 - (KeyEvent.getMaxKeyCode() >> 16), null, "\u008a\u0082\u008f\u0084\u0083\u0087\u008a\u008e\u0082\u008a\u0087\u0089\u008d\u008c\u0082\u008b\u0085\u0084\u008a\u0089\u0082\u0088\u0087\u0084\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        n((char) (9301 - Gravity.getAbsoluteGravity(0, 0)), (ViewConfiguration.getJumpTapTimeout() >> 16) + 422, 12 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(o.dk.b.e(bArr)).toString());
        this.a = bArr;
        int i2 = m + 23;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0024, code lost:
    
        if (r14.getBlockSize() != 0) goto L16;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0012. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final byte[] d(javax.crypto.Cipher r14) throws javax.crypto.BadPaddingException, javax.crypto.IllegalBlockSizeException, o.r.b, java.lang.IllegalArgumentException {
        /*
            Method dump skipped, instructions count: 528
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.d(javax.crypto.Cipher):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v29, types: [byte[]] */
    private static void k(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 1034
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void n(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.r.a.n(char, int, int, java.lang.Object[]):void");
    }
}
