package com.avaldigitallabs.mbocc.wallet;

import com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener;
import com.avaldigitallabs.mbocc.wallet.types.ProvisionStatus;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.EligibilityDenialReason;
import fr.antelop.sdk.Product;
import fr.antelop.sdk.WalletProvisioningCallback;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalProvisionCallback.smali */
public class DigitalProvisionCallback implements WalletProvisioningCallback {
    private DigitalWalletListener listener;

    DigitalProvisionCallback(DigitalWalletListener listener) {
        this.listener = listener;
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onInitializationSuccess(Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.INITIALIZATION_SUCCESS);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onInitializationError(AntelopError error, Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.INITIALIZATION_ERROR);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onPermissionNotGranted(String[] permissions, Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.PROCESS_NOT_GRANTED);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onDeviceEligible(boolean fingerprintAllowed, List<Product> eligibleProducts, Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.PROCESS_GRANTED);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onDeviceNotEligible(EligibilityDenialReason reason, Object data, String denialReference) {
        this.listener.onProvisionStatus(ProvisionStatus.DEVICE_NOT_ELIGIBLE);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onCheckEligibilityError(AntelopError error, Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.DEVICE_ERROR);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onProvisioningPending(Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.PROCESS_PENDING);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onProvisioningSuccess(Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.PROCESS_SUCCESS);
    }

    @Override // fr.antelop.sdk.WalletProvisioningCallback
    public void onProvisioningError(AntelopError error, Object data) {
        this.listener.onProvisionStatus(ProvisionStatus.PROCESS_ERROR);
    }
}
