package androidx.vectordrawable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$attr.smali */
    public static final class attr {
        public static int alpha = 0x7f04002b;
        public static int font = 0x7f0401b0;
        public static int fontProviderAuthority = 0x7f0401b2;
        public static int fontProviderCerts = 0x7f0401b3;
        public static int fontProviderFetchStrategy = 0x7f0401b4;
        public static int fontProviderFetchTimeout = 0x7f0401b5;
        public static int fontProviderPackage = 0x7f0401b6;
        public static int fontProviderQuery = 0x7f0401b7;
        public static int fontStyle = 0x7f0401b9;
        public static int fontVariationSettings = 0x7f0401ba;
        public static int fontWeight = 0x7f0401bb;
        public static int ttcIndex = 0x7f040331;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$color.smali */
    public static final class color {
        public static int notification_action_color_filter = 0x7f0600dc;
        public static int notification_icon_bg_color = 0x7f0600dd;
        public static int ripple_material_light = 0x7f0600e8;
        public static int secondary_text_default_material_light = 0x7f0600ea;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$dimen.smali */
    public static final class dimen {
        public static int compat_button_inset_horizontal_material = 0x7f07008e;
        public static int compat_button_inset_vertical_material = 0x7f07008f;
        public static int compat_button_padding_horizontal_material = 0x7f070090;
        public static int compat_button_padding_vertical_material = 0x7f070091;
        public static int compat_control_corner_material = 0x7f070092;
        public static int compat_notification_large_icon_max_height = 0x7f070093;
        public static int compat_notification_large_icon_max_width = 0x7f070094;
        public static int notification_action_icon_size = 0x7f07016b;
        public static int notification_action_text_size = 0x7f07016c;
        public static int notification_big_circle_margin = 0x7f07016d;
        public static int notification_content_margin_start = 0x7f07016e;
        public static int notification_large_icon_height = 0x7f07016f;
        public static int notification_large_icon_width = 0x7f070170;
        public static int notification_main_column_padding_top = 0x7f070171;
        public static int notification_media_narrow_margin = 0x7f070172;
        public static int notification_right_icon_size = 0x7f070173;
        public static int notification_right_side_padding_top = 0x7f070174;
        public static int notification_small_icon_background_padding = 0x7f070175;
        public static int notification_small_icon_size_as_large = 0x7f070176;
        public static int notification_subtext_size = 0x7f070177;
        public static int notification_top_pad = 0x7f070178;
        public static int notification_top_pad_large_text = 0x7f070179;

        private dimen() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$drawable.smali */
    public static final class drawable {
        public static int notification_action_background = 0x7f08015d;
        public static int notification_bg = 0x7f08015e;
        public static int notification_bg_low = 0x7f08015f;
        public static int notification_bg_low_normal = 0x7f080160;
        public static int notification_bg_low_pressed = 0x7f080161;
        public static int notification_bg_normal = 0x7f080162;
        public static int notification_bg_normal_pressed = 0x7f080163;
        public static int notification_icon_background = 0x7f080164;
        public static int notification_template_icon_bg = 0x7f080166;
        public static int notification_template_icon_low_bg = 0x7f080167;
        public static int notification_tile_bg = 0x7f080168;
        public static int notify_panel_notification_icon_bg = 0x7f080169;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$id.smali */
    public static final class id {
        public static int accessibility_action_clickable_span = 0x7f0a000d;
        public static int accessibility_custom_action_0 = 0x7f0a000e;
        public static int accessibility_custom_action_1 = 0x7f0a000f;
        public static int accessibility_custom_action_10 = 0x7f0a0010;
        public static int accessibility_custom_action_11 = 0x7f0a0011;
        public static int accessibility_custom_action_12 = 0x7f0a0012;
        public static int accessibility_custom_action_13 = 0x7f0a0013;
        public static int accessibility_custom_action_14 = 0x7f0a0014;
        public static int accessibility_custom_action_15 = 0x7f0a0015;
        public static int accessibility_custom_action_16 = 0x7f0a0016;
        public static int accessibility_custom_action_17 = 0x7f0a0017;
        public static int accessibility_custom_action_18 = 0x7f0a0018;
        public static int accessibility_custom_action_19 = 0x7f0a0019;
        public static int accessibility_custom_action_2 = 0x7f0a001a;
        public static int accessibility_custom_action_20 = 0x7f0a001b;
        public static int accessibility_custom_action_21 = 0x7f0a001c;
        public static int accessibility_custom_action_22 = 0x7f0a001d;
        public static int accessibility_custom_action_23 = 0x7f0a001e;
        public static int accessibility_custom_action_24 = 0x7f0a001f;
        public static int accessibility_custom_action_25 = 0x7f0a0020;
        public static int accessibility_custom_action_26 = 0x7f0a0021;
        public static int accessibility_custom_action_27 = 0x7f0a0022;
        public static int accessibility_custom_action_28 = 0x7f0a0023;
        public static int accessibility_custom_action_29 = 0x7f0a0024;
        public static int accessibility_custom_action_3 = 0x7f0a0025;
        public static int accessibility_custom_action_30 = 0x7f0a0026;
        public static int accessibility_custom_action_31 = 0x7f0a0027;
        public static int accessibility_custom_action_4 = 0x7f0a0028;
        public static int accessibility_custom_action_5 = 0x7f0a0029;
        public static int accessibility_custom_action_6 = 0x7f0a002a;
        public static int accessibility_custom_action_7 = 0x7f0a002b;
        public static int accessibility_custom_action_8 = 0x7f0a002c;
        public static int accessibility_custom_action_9 = 0x7f0a002d;
        public static int action_container = 0x7f0a0038;
        public static int action_divider = 0x7f0a003a;
        public static int action_image = 0x7f0a003b;
        public static int action_text = 0x7f0a0041;
        public static int actions = 0x7f0a0042;
        public static int async = 0x7f0a005b;
        public static int blocking = 0x7f0a005f;
        public static int chronometer = 0x7f0a006e;
        public static int dialog_button = 0x7f0a0087;
        public static int forever = 0x7f0a00a3;
        public static int icon = 0x7f0a00b1;
        public static int icon_group = 0x7f0a00b2;
        public static int info = 0x7f0a00b6;
        public static int italic = 0x7f0a00b8;
        public static int line1 = 0x7f0a00bf;
        public static int line3 = 0x7f0a00c0;
        public static int normal = 0x7f0a00ea;
        public static int notification_background = 0x7f0a00eb;
        public static int notification_main_column = 0x7f0a00ec;
        public static int notification_main_column_container = 0x7f0a00ed;
        public static int right_icon = 0x7f0a0100;
        public static int right_side = 0x7f0a0101;
        public static int tag_accessibility_actions = 0x7f0a0138;
        public static int tag_accessibility_clickable_spans = 0x7f0a0139;
        public static int tag_accessibility_heading = 0x7f0a013a;
        public static int tag_accessibility_pane_title = 0x7f0a013b;
        public static int tag_screen_reader_focusable = 0x7f0a013f;
        public static int tag_transition_group = 0x7f0a0141;
        public static int tag_unhandled_key_event_manager = 0x7f0a0142;
        public static int tag_unhandled_key_listeners = 0x7f0a0143;
        public static int text = 0x7f0a0147;
        public static int text2 = 0x7f0a0148;
        public static int time = 0x7f0a0153;
        public static int title = 0x7f0a0154;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$integer.smali */
    public static final class integer {
        public static int status_bar_notification_info_maxnum = 0x7f0b0019;

        private integer() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$layout.smali */
    public static final class layout {
        public static int custom_dialog = 0x7f0d0026;
        public static int notification_action = 0x7f0d005b;
        public static int notification_action_tombstone = 0x7f0d005c;
        public static int notification_template_custom_big = 0x7f0d0063;
        public static int notification_template_icon_group = 0x7f0d0064;
        public static int notification_template_part_chronometer = 0x7f0d0068;
        public static int notification_template_part_time = 0x7f0d0069;

        private layout() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$string.smali */
    public static final class string {
        public static int status_bar_notification_info_overflow = 0x7f120138;

        private string() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$style.smali */
    public static final class style {
        public static int TextAppearance_Compat_Notification = 0x7f13017d;
        public static int TextAppearance_Compat_Notification_Info = 0x7f13017e;
        public static int TextAppearance_Compat_Notification_Line2 = 0x7f130180;
        public static int TextAppearance_Compat_Notification_Time = 0x7f130183;
        public static int TextAppearance_Compat_Notification_Title = 0x7f130185;
        public static int Widget_Compat_NotificationActionContainer = 0x7f130272;
        public static int Widget_Compat_NotificationActionText = 0x7f130273;

        private style() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\vectordrawable\R$styleable.smali */
    public static final class styleable {
        public static int ColorStateListItem_alpha = 0x00000003;
        public static int ColorStateListItem_android_alpha = 0x00000001;
        public static int ColorStateListItem_android_color = 0x00000000;
        public static int ColorStateListItem_android_lStar = 0x00000002;
        public static int ColorStateListItem_lStar = 0x00000004;
        public static int FontFamilyFont_android_font = 0x00000000;
        public static int FontFamilyFont_android_fontStyle = 0x00000002;
        public static int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static int FontFamilyFont_android_fontWeight = 0x00000001;
        public static int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static int FontFamilyFont_font = 0x00000005;
        public static int FontFamilyFont_fontStyle = 0x00000006;
        public static int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static int FontFamilyFont_fontWeight = 0x00000008;
        public static int FontFamilyFont_ttcIndex = 0x00000009;
        public static int FontFamily_fontProviderAuthority = 0x00000000;
        public static int FontFamily_fontProviderCerts = 0x00000001;
        public static int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static int FontFamily_fontProviderPackage = 0x00000004;
        public static int FontFamily_fontProviderQuery = 0x00000005;
        public static int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static int GradientColorItem_android_color = 0x00000000;
        public static int GradientColorItem_android_offset = 0x00000001;
        public static int GradientColor_android_centerColor = 0x00000007;
        public static int GradientColor_android_centerX = 0x00000003;
        public static int GradientColor_android_centerY = 0x00000004;
        public static int GradientColor_android_endColor = 0x00000001;
        public static int GradientColor_android_endX = 0x0000000a;
        public static int GradientColor_android_endY = 0x0000000b;
        public static int GradientColor_android_gradientRadius = 0x00000005;
        public static int GradientColor_android_startColor = 0x00000000;
        public static int GradientColor_android_startX = 0x00000008;
        public static int GradientColor_android_startY = 0x00000009;
        public static int GradientColor_android_tileMode = 0x00000006;
        public static int GradientColor_android_type = 0x00000002;
        public static int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, 2130968619, 2130969082};
        public static int[] FontFamily = {2130969010, 2130969011, 2130969012, 2130969013, 2130969014, 2130969015, 2130969016};
        public static int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, 2130969008, 2130969017, 2130969018, 2130969019, 2130969393};
        public static int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};

        private styleable() {
        }
    }

    private R() {
    }
}
