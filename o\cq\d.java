package o.cq;

import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.l;
import o.e.a;
import o.ei.i;
import o.et.h;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cq\d.smali */
public abstract class d<T extends h> implements o.cc.e<T> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int[] b;
    private static int c;
    private static int d;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        c();
        int i = d + 49;
        c = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        e = new char[]{50852, 50691, 50697, 50803, 50697, 50696, 50770, 50794, 50691, 50793, 50768, 50702, 50688, 50692, 50693, 50688, 50697, 50698, 50692, 50770, 50768, 50696, 50688, 50694, 50698, 50702, 50802, 50698, 50694, 50692, 50694, 50793, 50799, 50694, 50691, 50694, 50864, 51150, 50735, 50735, 51140, 51144, 51149, 51148, 50706, 50733, 51149, 50738, 50706, 50710, 51145, 51141, 51147, 50707, 50732, 51150, 50737, 51148, 51141, 51150, 51150, 51137, 51140, 50738, 50740, 50738, 51149, 50711, 50735, 51140, 50706, 50711, 51147, 51139, 51142, 51148, 50848, 50714, 50688, 50698, 50688, 50691, 50789, 50813, 50714, 50784, 50789, 50713, 50705, 50708, 50690, 50693, 50716, 50813, 50786, 50716, 50695, 50690, 50715, 50716, 50716, 50786, 50790, 50713, 50714, 50713, 50835, 50806, 50703, 50697, 50790, 50791, 50702, 50807, 50807, 50692, 50702, 50701, 50702, 50700, 50809, 50809, 50801, 50806, 50800, 50697, 50805, 50813, 50702, 50689, 50696, 50697, 50872, 50819, 50857, 50879, 50850, 50862, 50856, 50879, 50879, 50856, 50863, 50848, 50857, 50703, 50800, 50942, 50839, 50860, 50873, 50868, 50859, 50846, 50909, 50912, 50841, 50851, 50849, 50851, 50855, 50863, 50859, 50855, 50851, 50877, 50853, 50829, 50925, 50921, 50917, 50823, 50848, 50852, 50839, 50857, 50877, 50842, 50840, 50852, 50842, 50846, 50849, 50879, 50852, 50853, 50938, 50852, 50855, 50821, 50925, 50925, 50925, 50912, 50818, 50857, 50851, 50878, 50848, 50849, 50843, 50846, 50857, 50859, 50848, 50879, 50854, 50848, 50863, 50832, 50926, 50931, 50822, 50772, 50773, 50781, 50873, 50856, 50839, 50851, 50776, 50773, 50876, 50855, 50903, 50934, 50897, 50936, 50929, 50930, 50909, 50912, 50829, 50931, 50843, 50851, 50852};
        b = new int[]{435638575, -1332464810, 950786939, 393546911, 418362447, -1750078254, 1303474750, 1475978566, 1065779839, 121880586, 398327736, -1860954107, -1159471363, 1908459432, -84312782, -596811190, -1034772466, -1318721403};
    }

    static void init$0() {
        $$d = new byte[]{108, 119, -51, 110};
        $$e = 238;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r8, int r9, int r10, java.lang.Object[] r11) {
        /*
            int r9 = r9 + 66
            int r10 = r10 * 3
            int r10 = 1 - r10
            byte[] r0 = o.cq.d.$$d
            int r8 = r8 + 4
            byte[] r1 = new byte[r10]
            r2 = 0
            if (r0 != 0) goto L16
            r9 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r11
            r11 = r10
            goto L33
        L16:
            r3 = r2
        L17:
            int r8 = r8 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r10) goto L28
            java.lang.String r8 = new java.lang.String
            r8.<init>(r1, r2)
            r11[r2] = r8
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r6
            r7 = r11
            r11 = r10
            r10 = r3
            r3 = r1
            r1 = r0
            r0 = r7
        L33:
            int r10 = -r10
            int r8 = r8 + r10
            r10 = r11
            r11 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.d.k(int, int, int, java.lang.Object[]):void");
    }

    public abstract boolean a();

    public abstract o.eg.e b(o.eg.b bVar) throws o.eg.d;

    public abstract T b(T t, o.eg.b bVar, int i, String str, o.eg.b bVar2) throws o.eg.d, i;

    public abstract boolean b(int i, o.eg.b bVar) throws o.eg.d;

    public abstract o.eg.b c(int i, o.eg.b bVar) throws o.eg.d;

    public abstract T c(String str, String str2, int i, String str3, o.eg.b bVar) throws o.eg.d, i;

    /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
    @Override // o.cc.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.List<T> a(java.lang.String r16, java.lang.String r17, int r18, java.lang.String r19, o.eg.b r20) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 402
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.d.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    protected java.util.List<o.cc.a> b(o.eg.e r17) throws o.ei.i, o.eg.d {
        /*
            Method dump skipped, instructions count: 606
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.d.b(o.eg.e):java.util.List");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void b(T r23, boolean r24) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1200
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.d.b(o.et.h, boolean):void");
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    private static void i(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        char c2 = 0;
        int i = iArr[0];
        int i2 = 1;
        int i3 = iArr[1];
        int i4 = iArr[2];
        int i5 = iArr[3];
        char[] cArr2 = e;
        long j = 0;
        int i6 = -1;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i7 = 0;
            while (i7 < length) {
                try {
                    Object[] objArr2 = new Object[i2];
                    objArr2[c2] = Integer.valueOf(cArr2[i7]);
                    Object obj = a.s.get(1951085128);
                    if (obj != null) {
                        cArr = cArr2;
                    } else {
                        Class cls = (Class) a.c((ViewConfiguration.getLongPressTimeout() >> 16) + 11, (char) ((Process.getElapsedCpuTime() > j ? 1 : (Process.getElapsedCpuTime() == j ? 0 : -1)) + i6), (ViewConfiguration.getJumpTapTimeout() >> 16) + 43);
                        byte b2 = (byte) i6;
                        cArr = cArr2;
                        Object[] objArr3 = new Object[1];
                        k(b2, (byte) (b2 & 54), (byte) 0, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        a.s.put(1951085128, obj);
                    }
                    cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i7++;
                    cArr2 = cArr;
                    c2 = 0;
                    i2 = 1;
                    j = 0;
                    i6 = -1;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        char[] cArr4 = new char[i3];
        System.arraycopy(cArr2, i, cArr4, 0, i3);
        switch (bArr2 != null) {
            case true:
                int i8 = $10 + 41;
                $11 = i8 % 128;
                int i9 = i8 % 2;
                char[] cArr5 = new char[i3];
                lVar.d = 0;
                char c3 = 0;
                while (true) {
                    switch (lVar.d >= i3) {
                        case false:
                            if (bArr2[lVar.d] == 1) {
                                int i10 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c3)};
                                    Object obj2 = a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) a.c(10 - TextUtils.lastIndexOf("", '0', 0, 0), (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), 449 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)));
                                        byte b3 = (byte) (-1);
                                        Object[] objArr5 = new Object[1];
                                        k(b3, (byte) (b3 & 53), (byte) 0, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(2016040108, obj2);
                                    }
                                    cArr5[i10] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c3)};
                                    Object obj3 = a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) a.c(View.resolveSize(0, 0) + 10, (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 207 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                                        byte b4 = (byte) (-1);
                                        Object[] objArr7 = new Object[1];
                                        k(b4, (byte) (b4 & 56), (byte) 0, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(804049217, obj3);
                                    }
                                    cArr5[i11] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c3 = cArr5[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj4 = a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) a.c(TextUtils.getCapsMode("", 0, 0) + 11, (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 259 - (ViewConfiguration.getTapTimeout() >> 16));
                                    byte b5 = (byte) (-1);
                                    byte b6 = (byte) (b5 + 1);
                                    Object[] objArr9 = new Object[1];
                                    k(b5, b6, b6, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        default:
                            cArr4 = cArr5;
                            break;
                    }
                }
        }
        if (i5 > 0) {
            int i12 = $11 + Opcodes.DMUL;
            $10 = i12 % 128;
            int i13 = i12 % 2;
            char[] cArr6 = new char[i3];
            System.arraycopy(cArr4, 0, cArr6, 0, i3);
            int i14 = i3 - i5;
            System.arraycopy(cArr6, 0, cArr4, i14, i5);
            System.arraycopy(cArr6, i5, cArr4, 0, i14);
        }
        switch (!z) {
            case false:
                char[] cArr7 = new char[i3];
                int i15 = 0;
                while (true) {
                    lVar.d = i15;
                    if (lVar.d >= i3) {
                        cArr4 = cArr7;
                        break;
                    } else {
                        cArr7[lVar.d] = cArr4[(i3 - lVar.d) - 1];
                        i15 = lVar.d + 1;
                    }
                }
        }
        switch (i4 > 0) {
            case true:
                lVar.d = 0;
                while (true) {
                    switch (lVar.d >= i3) {
                        case true:
                            break;
                        default:
                            cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                            lVar.d++;
                            int i16 = $10 + 61;
                            $11 = i16 % 128;
                            int i17 = i16 % 2;
                    }
                }
                break;
        }
        objArr[0] = new String(cArr4);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(int[] r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 994
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.d.j(int[], int, java.lang.Object[]):void");
    }
}
