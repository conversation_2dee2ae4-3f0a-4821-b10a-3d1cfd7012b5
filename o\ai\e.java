package o.ai;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.o;
import o.cf.i;
import o.cf.j;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ai\e.smali */
public final class e extends b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static int h;
    private static int j;
    int a;
    o.eo.e b;
    byte[] c;
    String d;
    byte[] e;
    o.h.d i;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ai\e$d.smali */
    public interface d {
        void a(o.bb.d dVar, int i);

        void c();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        n();
        int i = h + 63;
        j = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{108, 119, -51, 110};
        $$e = 205;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r8, int r9, int r10, java.lang.Object[] r11) {
        /*
            byte[] r0 = o.ai.e.$$d
            int r10 = r10 * 3
            int r10 = r10 + 1
            int r8 = r8 + 66
            int r9 = r9 + 4
            byte[] r1 = new byte[r10]
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r9
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r11
            r11 = r10
            goto L33
        L16:
            r3 = r2
        L17:
            int r9 = r9 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r10) goto L28
            java.lang.String r8 = new java.lang.String
            r8.<init>(r1, r2)
            r11[r2] = r8
            return
        L28:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r6
            r7 = r11
            r11 = r10
            r10 = r3
            r3 = r1
            r1 = r0
            r0 = r7
        L33:
            int r10 = -r10
            int r9 = r9 + r10
            r10 = r11
            r11 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ai.e.l(int, int, int, java.lang.Object[]):void");
    }

    static void n() {
        f = new char[]{50862, 50690, 50691, 50717, 50706, 50689, 50688, 50735, 50732, 50710, 50710, 50704, 50943, 50854, 50852, 50847, 50837, 50854, 50849, 50855, 50854};
    }

    public e(Context context, d dVar, o.ei.c cVar) {
        super(context, dVar, cVar, o.bb.e.C);
    }

    public final void c(byte[] bArr, byte[] bArr2, o.h.d dVar, String str, o.eo.e eVar) {
        int i = h + 97;
        j = i % 128;
        switch (i % 2 != 0 ? 'C' : (char) 25) {
            case 'C':
                this.c = bArr;
                this.e = bArr2;
                this.i = dVar;
                this.d = str;
                this.b = eVar;
                c();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.c = bArr;
                this.e = bArr2;
                this.i = dVar;
                this.d = str;
                this.b = eVar;
                c();
                return;
        }
    }

    @Override // o.y.b
    public final a<?> b() {
        c cVar = new c(this);
        int i = h + 11;
        j = i % 128;
        switch (i % 2 != 0 ? '=' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = h + 27;
        j = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                k("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000", new int[]{0, 12, Opcodes.INEG, 0}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000", new int[]{0, 12, Opcodes.INEG, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ai\e$c.smali */
    static final class c extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static char b;
        private static int c;
        private static long d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            c = 0;
            e = 1;
            b = (char) 17957;
            a = 161105445;
            d = -2002418545916110869L;
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r5v10 */
        /* JADX WARN: Type inference failed for: r5v11 */
        /* JADX WARN: Type inference failed for: r5v2 */
        /* JADX WARN: Type inference failed for: r5v3 */
        /* JADX WARN: Type inference failed for: r5v8, types: [int] */
        private static void B(byte b2, byte b3, byte b4, Object[] objArr) {
            int i = 3 - (b3 * 4);
            byte[] bArr = $$d;
            int i2 = 106 - b2;
            int i3 = 1 - (b4 * 2);
            byte[] bArr2 = new byte[i3];
            int i4 = -1;
            int i5 = i3 - 1;
            ?? r5 = i2;
            if (bArr == null) {
                i5 = i5;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = -1;
                r5 = i2 + i;
            }
            while (true) {
                int i6 = i;
                byte b5 = r5;
                int i7 = i4 + 1;
                int i8 = i6 + 1;
                bArr2[i7] = b5;
                if (i7 == i5) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                i = i8;
                r5 = bArr[i8] + b5;
                i5 = i5;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = i7;
            }
        }

        static void init$0() {
            $$d = new byte[]{20, -38, -101, -62};
            $$e = 251;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = c + 95;
            e = i % 128;
            switch (i % 2 == 0 ? 'U' : Typography.less) {
                case '<':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        c(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = c + 99;
            e = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w((ViewConfiguration.getKeyRepeatDelay() >> 16) - 152163125, "驟『ꞁ\udbfb\ue8d6᧡㹴쿇ᖉ", (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "쬅\uee2c㗶\udf00", "凎ẞ悷뼫", objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = c + 43;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return intern;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(View.combineMeasuredStates(0, 0) - 802856110, "䄍\ue2bf␏鷂勪毥誧\uf3de窈웪鉐훘쮾\uee60ﮊ\udc97是觱囐", (char) (16220 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), "勸╣寐\udc3f", "凎ẞ悷뼫", objArr);
            o.cf.d dVar = new o.cf.d(context, 43, ((String) objArr[0]).intern());
            int i = c + 67;
            e = i % 128;
            switch (i % 2 == 0 ? (char) 4 : '/') {
                case 4:
                    int i2 = 93 / 0;
                    return dVar;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            switch (((e) e()).b != null) {
                case false:
                    break;
                default:
                    int i = c + 37;
                    e = i % 128;
                    int i2 = i % 2;
                    Object[] objArr = new Object[1];
                    w(View.MeasureSpec.getSize(0), "鐕ㆯ捾鹫蝳쎃", (char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 34510), "늍ᘁ칞䊆", "凎ẞ悷뼫", objArr);
                    bVar.d(((String) objArr[0]).intern(), ((e) e()).b.e());
                    break;
            }
            Object[] objArr2 = new Object[1];
            w(ExpandableListView.getPackedPositionChild(0L) + 1, "ຸ\udc8b칾탅\u0ad2ᙽ鋊焻諠뫣", (char) View.MeasureSpec.getMode(0), "瑥징丛⠏", "凎ẞ悷뼫", objArr2);
            bVar.d(((String) objArr2[0]).intern(), 0);
            Object[] objArr3 = new Object[1];
            w(ExpandableListView.getPackedPositionType(0L), "䒾訴藴棇ᴲⳞ蝩䪾褁쥐", (char) View.combineMeasuredStates(0, 0), "呆鶒蠲Ꜳ", "凎ẞ悷뼫", objArr3);
            bVar.d(((String) objArr3[0]).intern(), 1);
            int i3 = e + 45;
            c = i3 % 128;
            int i4 = i3 % 2;
            return bVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            switch (((e) e()).d == null) {
                case true:
                    break;
                default:
                    int i = c + 5;
                    e = i % 128;
                    int i2 = i % 2;
                    if (((e) e()).i != null) {
                        return new j(((e) e()).d, false, ((e) e()).i);
                    }
                    break;
            }
            int i3 = c + Opcodes.DMUL;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 26 : (char) 2) {
                case 2:
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final byte[][] k() {
            byte[] bArr;
            byte[] bArr2;
            byte[][] bArr3 = new byte[2][];
            switch (((e) e()).c != null) {
                case true:
                    bArr = ((e) e()).c;
                    break;
                default:
                    int i = e + Opcodes.LNEG;
                    c = i % 128;
                    switch (i % 2 == 0) {
                        case false:
                            bArr = new byte[1];
                            break;
                        default:
                            bArr = new byte[0];
                            break;
                    }
            }
            bArr3[0] = bArr;
            switch (((e) e()).e == null ? (char) 26 : (char) 21) {
                case 26:
                    int i2 = e + Opcodes.DNEG;
                    c = i2 % 128;
                    int i3 = i2 % 2;
                    bArr2 = new byte[0];
                    break;
                default:
                    bArr2 = ((e) e()).e;
                    break;
            }
            bArr3[1] = bArr2;
            return bArr3;
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = e + 3;
            c = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i);
                    int i4 = c + 81;
                    e = i4 % 128;
                    switch (i4 % 2 == 0 ? (char) 27 : (char) 15) {
                        case 27:
                            throw null;
                        default:
                            return c2;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            Object[] objArr = new Object[1];
            w(TextUtils.indexOf("", "", 0, 0), "䕮\uefe9\uecba䊗ԩ⠒ﳜ垗퇣严䃞唼㥳ㄒ", (char) (39233 - Drawable.resolveOpacity(0, 0)), "䏿㊿䆞Კ", "凎ẞ悷뼫", objArr);
            boolean booleanValue = bVar.b(((String) objArr[0]).intern(), Boolean.FALSE).booleanValue();
            e eVar = (e) e();
            Object[] objArr2 = new Object[1];
            w((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 539036622, "貶唪㭧\ue01a\uef56飿琹頮뤛䢰㫖䖊卆６瓱畷遏", (char) (52796 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), "㌄\udef4㯟䇎", "凎ẞ悷뼫", objArr2);
            eVar.a = bVar.e(((String) objArr2[0]).intern(), (Integer) (-1)).intValue();
            switch (!booleanValue ? '4' : 'U') {
                case Opcodes.CASTORE /* 85 */:
                    break;
                default:
                    int i = c + 83;
                    e = i % 128;
                    int i2 = i % 2;
                    h().c(o.bb.a.aF);
                    break;
            }
            int i3 = c + Opcodes.DREM;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + Opcodes.LREM;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    switch (AnonymousClass2.b[h().d().ordinal()]) {
                        case 1:
                            f().c(g(), ((e) e()).b.e());
                            return;
                        case 2:
                            f().e(g(), ((e) e()).b.e());
                            int i2 = c + 45;
                            e = i2 % 128;
                            int i3 = i2 % 2;
                            return;
                        default:
                            super.t();
                            int i4 = e + 51;
                            c = i4 % 128;
                            int i5 = i4 % 2;
                            return;
                    }
                default:
                    int i6 = AnonymousClass2.b[h().d().ordinal()];
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = e + 53;
            c = i % 128;
            switch (i % 2 != 0 ? (char) 17 : 'H') {
                case 'H':
                    ((e) e()).j().c();
                    int i2 = e + 25;
                    c = i2 % 128;
                    int i3 = i2 % 2;
                    return;
                default:
                    ((e) e()).j().c();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = e + 23;
            c = i % 128;
            int i2 = i % 2;
            ((e) e()).j().a(dVar, ((e) e()).a);
            int i3 = c + Opcodes.DNEG;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        private static void w(int i, String str, char c2, String str2, String str3, Object[] objArr) {
            char[] charArray;
            char[] cArr;
            char c3;
            int i2 = $11 + 13;
            $10 = i2 % 128;
            if (i2 % 2 != 0) {
                Object obj = null;
                obj.hashCode();
                throw null;
            }
            char[] charArray2 = str3 != null ? str3.toCharArray() : str3;
            int i3 = 1;
            int i4 = 0;
            switch (str2 != null) {
                case true:
                    int i5 = $10 + 57;
                    $11 = i5 % 128;
                    int i6 = i5 % 2;
                    charArray = str2.toCharArray();
                    break;
                default:
                    charArray = str2;
                    break;
            }
            char[] cArr2 = charArray;
            if (str != null) {
                int i7 = $10 + 95;
                $11 = i7 % 128;
                int i8 = i7 % 2;
                cArr = str.toCharArray();
            } else {
                cArr = str;
            }
            o oVar = new o();
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int length2 = charArray2.length;
            char[] cArr4 = new char[length2];
            System.arraycopy(cArr2, 0, cArr3, 0, length);
            System.arraycopy(charArray2, 0, cArr4, 0, length2);
            cArr3[0] = (char) (cArr3[0] ^ c2);
            cArr4[2] = (char) (cArr4[2] + ((char) i));
            int length3 = cArr.length;
            char[] cArr5 = new char[length3];
            oVar.e = 0;
            while (oVar.e < length3) {
                try {
                    Object[] objArr2 = {oVar};
                    Object obj2 = o.e.a.s.get(-429442487);
                    if (obj2 == null) {
                        Class cls = (Class) o.e.a.c(10 - (ViewConfiguration.getPressedStateDuration() >> 16), (char) (20953 - TextUtils.indexOf((CharSequence) "", '0', i4, i4)), TextUtils.getCapsMode("", i4, i4) + 344);
                        byte b2 = (byte) i4;
                        Object[] objArr3 = new Object[i3];
                        B((byte) 7, b2, b2, objArr3);
                        String str4 = (String) objArr3[i4];
                        Class<?>[] clsArr = new Class[i3];
                        clsArr[i4] = Object.class;
                        obj2 = cls.getMethod(str4, clsArr);
                        o.e.a.s.put(-429442487, obj2);
                    }
                    int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                    try {
                        Object[] objArr4 = {oVar};
                        Object obj3 = o.e.a.s.get(-515165572);
                        if (obj3 == null) {
                            Class cls2 = (Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 10, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), 207 - View.MeasureSpec.getSize(i4));
                            byte b3 = (byte) 5;
                            byte b4 = (byte) (b3 - 5);
                            Object[] objArr5 = new Object[i3];
                            B(b3, b4, b4, objArr5);
                            String str5 = (String) objArr5[i4];
                            Class<?>[] clsArr2 = new Class[i3];
                            clsArr2[i4] = Object.class;
                            obj3 = cls2.getMethod(str5, clsArr2);
                            o.e.a.s.put(-515165572, obj3);
                        }
                        int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                        int i9 = cArr3[oVar.e % 4] * 32718;
                        try {
                            Object[] objArr6 = new Object[3];
                            objArr6[2] = Integer.valueOf(cArr4[intValue]);
                            objArr6[i3] = Integer.valueOf(i9);
                            objArr6[i4] = oVar;
                            Object obj4 = o.e.a.s.get(-1614232674);
                            if (obj4 == null) {
                                Class cls3 = (Class) o.e.a.c((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), TextUtils.indexOf("", "", i4, i4) + 281);
                                byte b5 = (byte) ($$e & 7);
                                byte b6 = (byte) (b5 - 3);
                                Object[] objArr7 = new Object[i3];
                                B(b5, b6, b6, objArr7);
                                String str6 = (String) objArr7[0];
                                Class<?>[] clsArr3 = new Class[3];
                                clsArr3[0] = Object.class;
                                clsArr3[i3] = Integer.TYPE;
                                clsArr3[2] = Integer.TYPE;
                                obj4 = cls3.getMethod(str6, clsArr3);
                                o.e.a.s.put(-1614232674, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr6);
                            int i10 = cArr3[intValue2] * 32718;
                            try {
                                Object[] objArr8 = new Object[2];
                                objArr8[i3] = Integer.valueOf(cArr4[intValue]);
                                objArr8[0] = Integer.valueOf(i10);
                                Object obj5 = o.e.a.s.get(406147795);
                                if (obj5 != null) {
                                    c3 = 2;
                                } else {
                                    Class cls4 = (Class) o.e.a.c(19 - TextUtils.getOffsetAfter("", 0), (char) (14688 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), (-16777104) - Color.rgb(0, 0, 0));
                                    byte b7 = (byte) 0;
                                    byte b8 = b7;
                                    Object[] objArr9 = new Object[i3];
                                    B(b7, b8, b8, objArr9);
                                    String str7 = (String) objArr9[0];
                                    c3 = 2;
                                    Class<?>[] clsArr4 = new Class[2];
                                    clsArr4[0] = Integer.TYPE;
                                    clsArr4[i3] = Integer.TYPE;
                                    obj5 = cls4.getMethod(str7, clsArr4);
                                    o.e.a.s.put(406147795, obj5);
                                }
                                cArr4[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                                cArr3[intValue2] = oVar.d;
                                cArr5[oVar.e] = (char) ((((int) (a ^ 6565854932352255525L)) ^ ((cArr3[intValue2] ^ r6[oVar.e]) ^ (d ^ 6565854932352255525L))) ^ ((char) (b ^ 6565854932352255525L)));
                                oVar.e++;
                                i3 = 1;
                                i4 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = new String(cArr5);
        }
    }

    /* renamed from: o.ai.e$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ai\e$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a = 0;
        static final /* synthetic */ int[] b;
        private static int c;

        static {
            c = 1;
            int[] iArr = new int[o.bb.a.values().length];
            b = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = a;
                int i2 = (i & 81) + (i | 81);
                c = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e) {
            }
            try {
                b[o.bb.a.az.ordinal()] = 2;
                int i4 = a;
                int i5 = ((i4 | 45) << 1) - (i4 ^ 45);
                c = i5 % 128;
                switch (i5 % 2 != 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:28:0x00cf, code lost:
    
        r9 = r15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 976
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ai.e.k(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
