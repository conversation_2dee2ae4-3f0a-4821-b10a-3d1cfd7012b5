package o.em;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import java.util.List;
import o.ei.i;
import o.em.a;
import o.em.d;
import o.er.t;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int f;
    private static int g;
    private static long h;
    private final a a = new a();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\e$c.smali */
    public interface c<T> {
        void a(List<T> list);

        void d(AntelopError antelopError);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        c();
        KeyEvent.keyCodeFromString("");
        KeyEvent.getDeadChar(0, 0);
        int i = g + 35;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void c() {
        e = (char) 39112;
        c = (char) 23498;
        b = (char) 22745;
        d = (char) 32932;
        h = 6651703920706776072L;
    }

    static void init$0() {
        $$a = new byte[]{40, 24, -45, -26};
        $$b = Opcodes.I2B;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            byte[] r0 = o.em.e.$$a
            int r7 = r7 * 2
            int r7 = r7 + 112
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L19:
            r3 = r2
        L1a:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.k(short, short, int, java.lang.Object[]):void");
    }

    public final void a(Context context) throws i {
        o.eg.b v;
        a aVar;
        int i = f + 13;
        g = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        i("\ufafe\uda55\ud977狯畧㇍烹爴鷸ꠃ\ud977狯畧㇍烹爴鷸ꠃ腝\uf4d1烹爴햊鞊쮧웆㵸ഹ⻖끍쨮茧於\u180e頲㱮褚붟❃\ue666\ue321淂廁蠺\udeaf료䬾殃", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 47, objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        j("⫳쎵\uf862", 59723 - (Process.myPid() >> 22), objArr2);
        String string = sharedPreferences.getString(((String) objArr2[0]).intern(), "");
        if (string.isEmpty()) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32917 - AndroidCharacter.getMirror('0'), objArr3);
            String intern = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            i("笼Ԏﭒ臂Ⱓ鱤禮▫\uf083\uda4e⌠䉦േ\ue414歾夰鲓\ueb3f饇橋丌Ὡ䒁\uecb5都홱\u0a12疋於\u180e媲\u10ce〧\uf278\udeaf료若ቅ뫭꼒䕺\uf53f\uea48珟㩁볖\uf3b1滆\uf25a瞬㸖䶐笼Ԏ谰௪褚붟顕\ud7fd䌎氠깷⨅흋嘎", 66 - KeyEvent.normalizeMetaState(0), objArr4);
            o.ee.g.d(intern, ((String) objArr4[0]).intern());
            return;
        }
        o.ee.g.c();
        Object[] objArr5 = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32869 - KeyEvent.getDeadChar(0, 0), objArr5);
        String intern2 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        i("笼Ԏﭒ臂Ⱓ鱤禮▫\uf083\uda4e⌠䉦䱞\uebfe䍑\u14980樸ﭒ臂鶝쥄ʬ腁\u139f\ud800渲\ua62f䌎氠蟯\ue6ee\uefba榹䌎氠鿎즧若ቅ뫭꼒䕺\uf53f\uea48珟똚咿ꠁ聗守\uea9d誗\u1680", KeyEvent.normalizeMetaState(0) + 54, objArr6);
        o.ee.g.d(intern2, ((String) objArr6[0]).intern());
        String d2 = new o.dd.e(context).d(string);
        o.ee.g.c();
        Object[] objArr7 = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 32869, objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr8 = new Object[1];
        i("笼Ԏﭒ臂Ⱓ鱤禮▫\uf083\uda4e⌠䉦э逩꜖竓㵸ഹۤ҈\uea48珟䍑\u14980樸ﭒ臂鶝쥄ʬ腁\u139f\ud800渲\ua62f䌎氠蟯\ue6ee\uefba榹䌎氠㩁볖誵믃", '_' - AndroidCharacter.getMirror('0'), objArr8);
        o.ee.g.d(intern3, sb.append(((String) objArr8[0]).intern()).append(d2).toString());
        switch (d2 != null ? (char) 19 : '!') {
            case 19:
                int i3 = f + 87;
                g = i3 % 128;
                int i4 = i3 % 2;
                switch (d2.isEmpty()) {
                    case false:
                        try {
                            o.eg.b bVar = new o.eg.b(d2);
                            Object[] objArr9 = new Object[1];
                            i("守\uea9d饇橋丌Ὡ䒁\uecb5都홱풻돴\u1fc5灜螺빦\ue321淂", 18 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr9);
                            switch (bVar.b(((String) objArr9[0]).intern()) ? 'F' : '^') {
                                case Opcodes.DUP2_X2 /* 94 */:
                                    return;
                                default:
                                    int i5 = g + 19;
                                    f = i5 % 128;
                                    switch (i5 % 2 != 0 ? 'T' : '\b') {
                                        case Opcodes.BASTORE /* 84 */:
                                            Object[] objArr10 = new Object[1];
                                            i("守\uea9d饇橋丌Ὡ䒁\uecb5都홱풻돴\u1fc5灜螺빦\ue321淂", 72 >> View.resolveSizeAndState(0, 0, 1), objArr10);
                                            v = bVar.v(((String) objArr10[0]).intern());
                                            aVar = this.a;
                                            break;
                                        default:
                                            Object[] objArr11 = new Object[1];
                                            i("守\uea9d饇橋丌Ὡ䒁\uecb5都홱풻돴\u1fc5灜螺빦\ue321淂", View.resolveSizeAndState(0, 0, 0) + 18, objArr11);
                                            v = bVar.v(((String) objArr11[0]).intern());
                                            aVar = this.a;
                                            break;
                                    }
                                    aVar.d(v);
                                    return;
                            }
                        } catch (o.eg.d e2) {
                            StringBuilder sb2 = new StringBuilder();
                            Object[] objArr12 = new Object[1];
                            i("笼Ԏ蓿\uf3b9禮▫\u0a12疋刞譊䮪⑰鿎즧䥤诧븲櫊鶝쥄钄ࣞ笼Ԏ谰௪쎠韚数ﲂ廁蠺祁ឧ於\u180e媲\u10ce〧\uf278\udeaf료若ቅ쾚坺", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 45, objArr12);
                            throw new i(sb2.append(((String) objArr12[0]).intern()).append(e2.getMessage()).toString());
                        }
                        StringBuilder sb22 = new StringBuilder();
                        Object[] objArr122 = new Object[1];
                        i("笼Ԏ蓿\uf3b9禮▫\u0a12疋刞譊䮪⑰鿎즧䥤诧븲櫊鶝쥄钄ࣞ笼Ԏ谰௪쎠韚数ﲂ廁蠺祁ឧ於\u180e媲\u10ce〧\uf278\udeaf료若ቅ쾚坺", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 45, objArr122);
                        throw new i(sb22.append(((String) objArr122[0]).intern()).append(e2.getMessage()).toString());
                }
        }
        o.ee.g.c();
        Object[] objArr13 = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32870 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr13);
        String intern4 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        j("⫱\ue59b됫䒫ᝅ⟘\uf67a脊冊怨ヺ쌚鎤ꉴ紃උ\udc3c\uecdc뼒俿ṽ⤐嶺蠶壎歉㮪쪕蔑喲搱㓞읊韶ꚇ焌Ǭ큣\ue096데䏤ኂⴊﶧ豍峟潩㾻캘餷ꦽ硌ࣙ\udb7a\uea15몚甮ֺ", 53101 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr14);
        o.ee.g.d(intern4, ((String) objArr14[0]).intern());
    }

    public final void e(Context context) {
        Object[] objArr = new Object[1];
        i("\ufafe\uda55\ud977狯畧㇍烹爴鷸ꠃ\ud977狯畧㇍烹爴鷸ꠃ腝\uf4d1烹爴햊鞊쮧웆㵸ഹ⻖끍쨮茧於\u180e頲㱮褚붟❃\ue666\ue321淂廁蠺\udeaf료䬾殃", 47 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        try {
            o.eg.b bVar = new o.eg.b();
            try {
                o.eg.b c2 = this.a.c();
                if (c2.d() != 0) {
                    o.ee.g.c();
                    Object[] objArr2 = new Object[1];
                    j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32870 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr2);
                    String intern = ((String) objArr2[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr3 = new Object[1];
                    j("⫻쌠拏靰趭믟准世摤ቾ\u088d⛎\udcac\uf55e\ue37a饲랍궭寯瀌游ђ㊘⢬월ｇ锭荔륮垧䶊篼မแ⑷튚죁\ue6f9齒땣ꌠ", (KeyEvent.getMaxKeyCode() >> 16) + 59863, objArr3);
                    o.ee.g.d(intern, sb.append(((String) objArr3[0]).intern()).append(c2).toString());
                    Object[] objArr4 = new Object[1];
                    i("守\uea9d饇橋丌Ὡ䒁\uecb5都홱풻돴\u1fc5灜螺빦\ue321淂", 17 - TextUtils.lastIndexOf("", '0'), objArr4);
                    bVar.d(((String) objArr4[0]).intern(), c2);
                    int i = g + 31;
                    f = i % 128;
                    int i2 = i % 2;
                } else {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32870 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr5);
                    String intern2 = ((String) objArr5[0]).intern();
                    Object[] objArr6 = new Object[1];
                    i("鶢똍⢬骕ﭒ臂晣젛鲓\ueb3f誗\u1680⌠䉦⛼및硒㠀ꡞ岶讦ҏ都홱\u0a12疋詵渃꿶榋躕\u0ee1퉣ͮ", View.MeasureSpec.getSize(0) + 33, objArr6);
                    o.ee.g.d(intern2, ((String) objArr6[0]).intern());
                }
                String a = new o.dd.e(context).a(bVar.b());
                o.ee.g.c();
                Object[] objArr7 = new Object[1];
                j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", Drawable.resolveOpacity(0, 0) + 32869, objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                i("鶢똍⢬骕ﭒ臂晣젛鲓\ueb3f誗\u1680⌠䉦\uf3b1滆\uf25a瞬䍻\uf7a7흋嘎讦ҏ饮켭㩵遂於\u180e\u0a12疋刞譊䮪⑰쟾㦀歾夰守\uea9d誗\u1680", (Process.myTid() >> 22) + 44, objArr8);
                o.ee.g.d(intern3, ((String) objArr8[0]).intern());
                SharedPreferences.Editor edit = sharedPreferences.edit();
                Object[] objArr9 = new Object[1];
                j("⫳쎵\uf862", 59723 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr9);
                edit.putString(((String) objArr9[0]).intern(), a).commit();
                int i3 = g + 17;
                f = i3 % 128;
                int i4 = i3 % 2;
            } catch (o.eg.d e2) {
                e = e2;
                o.ee.g.c();
                Object[] objArr10 = new Object[1];
                j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32870 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr10);
                String intern4 = ((String) objArr10[0]).intern();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr11 = new Object[1];
                j("⫻\ue8aa깏淢⎅\ue13dꓢ穼㠴ﾴ뵉猌㛤\uf40c议䦁༻싚聼䙟ֹ\udb41餅岦ቐ퇹鞃唸棚⸱\uec09ꎳ慑✉盛렏翿㶌\uf324뛚瑱\u0a11즳载䴍¯왉薫官ᤴ\udccb鉲候ិ핲\ueb43꺫汈⏳\ue1d7ꜹ竐㡰\ufe1a붿獠ㄐ\uf4ef訖䦩", 49756 - ExpandableListView.getPackedPositionChild(0L), objArr11);
                o.ee.g.d(intern4, sb2.append(((String) objArr11[0]).intern()).append(e.getMessage()).toString());
                SharedPreferences.Editor edit2 = sharedPreferences.edit();
                Object[] objArr12 = new Object[1];
                j("⫳쎵\uf862", 59723 - View.combineMeasuredStates(0, 0), objArr12);
                edit2.putString(((String) objArr12[0]).intern(), "").commit();
            }
        } catch (o.eg.d e3) {
            e = e3;
        }
    }

    public final void e() {
        int i = g + 93;
        f = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32869 - View.MeasureSpec.getSize(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j("⫾仍\ue29fـ먔\udfc1玧靰ହ곭샃撇顷㰜凴\uf5ab楻赹⚳", 25656 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.a.d();
        int i3 = f + Opcodes.LREM;
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final a b() {
        int i = g;
        int i2 = i + 69;
        f = i2 % 128;
        int i3 = i2 % 2;
        a aVar = this.a;
        int i4 = i + 75;
        f = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 7 : '?') {
            case '?':
                return aVar;
            default:
                int i5 = 21 / 0;
                return aVar;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0091, code lost:
    
        r8.a.k(r10).b().a(r9, r10, new o.em.e.AnonymousClass3(r8));
        r9 = o.em.e.g + 51;
        o.em.e.f = r9 % 128;
        r9 = r9 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x00ad, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x008f, code lost:
    
        if (o.ei.c.c().q() != false) goto L13;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0057, code lost:
    
        if (o.ei.c.c().q() != false) goto L13;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(final android.content.Context r9, java.lang.String r10, final o.em.e.c<o.eo.f> r11) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r8 = this;
            int r0 = o.em.e.f
            int r0 = r0 + 11
            int r1 = r0 % 128
            o.em.e.g = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 95
            goto L11
        Lf:
            r0 = 64
        L11:
            java.lang.String r1 = ""
            java.lang.String r2 = "於\u180e\ufafe\uda55䌎氠틬\ue630궫\ue81a廁蠺"
            r3 = 32869(0x8065, float:4.6059E-41)
            java.lang.String r4 = "⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔"
            r5 = 0
            r6 = 1
            r7 = 0
            switch(r0) {
                case 64: goto L5a;
                default: goto L20;
            }
        L20:
            o.ee.g.c()
            float r0 = android.media.AudioTrack.getMinVolume()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            int r3 = r3 - r0
            java.lang.Object[] r0 = new java.lang.Object[r6]
            j(r4, r3, r0)
            r0 = r0[r7]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r3 = 66
            int r1 = android.text.TextUtils.getOffsetAfter(r1, r6)
            int r1 = r3 << r1
            java.lang.Object[] r3 = new java.lang.Object[r6]
            i(r2, r1, r3)
            r1 = r3[r7]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            o.ei.c r0 = o.ei.c.c()
            boolean r0 = r0.q()
            if (r0 == 0) goto Lae
            goto L91
        L5a:
            o.ee.g.c()
            float r0 = android.media.AudioTrack.getMinVolume()
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            int r0 = r0 + r3
            java.lang.Object[] r3 = new java.lang.Object[r6]
            j(r4, r0, r3)
            r0 = r3[r7]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r1 = android.text.TextUtils.getOffsetAfter(r1, r7)
            int r1 = r1 + 12
            java.lang.Object[] r3 = new java.lang.Object[r6]
            i(r2, r1, r3)
            r1 = r3[r7]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            o.ei.c r0 = o.ei.c.c()
            boolean r0 = r0.q()
            if (r0 == 0) goto Lae
        L91:
            o.em.a r0 = r8.a
            o.em.a$c r0 = r0.k(r10)
            o.em.c r0 = r0.b()
            o.em.e$3 r1 = new o.em.e$3
            r1.<init>()
            r0.a(r9, r10, r1)
            int r9 = o.em.e.g
            int r9 = r9 + 51
            int r10 = r9 % 128
            o.em.e.f = r10
            int r9 = r9 % 2
            return
        Lae:
            fr.antelop.sdk.exception.WalletValidationException r9 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r10 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            int r11 = android.graphics.Color.argb(r7, r7, r7, r7)
            int r11 = r11 + 6
            java.lang.Object[] r0 = new java.lang.Object[r6]
            java.lang.String r1 = "䛗쨜参汥쀴ಂ"
            i(r1, r11, r0)
            r11 = r0[r7]
            java.lang.String r11 = (java.lang.String) r11
            java.lang.String r11 = r11.intern()
            float r0 = android.util.TypedValue.complexToFloat(r7)
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            int r0 = r0 + 42
            java.lang.Object[] r1 = new java.lang.Object[r6]
            java.lang.String r2 = "䛗쨜参汥쀴ಂ鿎즧若ቅ껖ᕼ祁ឧ⃦ﵡ\ud89c轉笼Ԏ埋㽆讦ҏ䮪⑰ꪠ븼ઉ켳Ꚍ\uf1caꠁ聗於\u180e\uf0ff딁ណቝ\uea48珟"
            i(r2, r0, r1)
            r0 = r1[r7]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r9.<init>(r10, r11, r0)
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.e(android.content.Context, java.lang.String, o.em.e$c):void");
    }

    public final void d(final Context context, String str, final c<t> cVar) throws WalletValidationException {
        int i = g + Opcodes.LUSHR;
        f = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", (ViewConfiguration.getPressedStateDuration() >> 16) + 32869, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j("⫿쳠\ue6d6颛늃呢乓怽ᨢ㷸ퟋ짒\ue3a1薒뽺兄䬺洆", 58909 - ExpandableListView.getPackedPositionGroup(0L), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!o.ei.c.c().q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            i("䛗쨜参汥쀴ಂ", 6 - TextUtils.indexOf("", ""), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            i("䛗쨜参汥쀴ಂ鿎즧若ቅ껖ᕼ祁ឧ⃦ﵡ\ud89c轉笼Ԏ埋㽆讦ҏ䮪⑰ꪠ븼ઉ켳Ꚍ\uf1caꠁ聗於\u180e\uf0ff딁ណቝ\uea48珟", 42 - KeyEvent.normalizeMetaState(0), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        this.a.k(str).c().d(context, str, new d.e<t>() { // from class: o.em.e.4
            private static int e = 0;
            private static int b = 1;

            @Override // o.em.d.e
            public final void e(List<t> list) {
                int i3 = b;
                int i4 = ((i3 | 27) << 1) - (i3 ^ 27);
                e = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        e.this.e(context);
                        cVar.a(list);
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        e.this.e(context);
                        cVar.a(list);
                        return;
                }
            }

            @Override // o.em.d.e
            public final void e(AntelopError antelopError) {
                int i3 = e;
                int i4 = ((i3 | 7) << 1) - (i3 ^ 7);
                b = i4 % 128;
                int i5 = i4 % 2;
                cVar.d(antelopError);
                int i6 = (b + 38) - 1;
                e = i6 % 128;
                int i7 = i6 % 2;
            }
        });
        int i3 = f + Opcodes.LUSHR;
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final void a(final Context context, String str, final c<o.eo.j> cVar) throws WalletValidationException {
        int i = f + 61;
        g = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32869 - Color.red(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j("⫪뛼ዼﻩ嫹⛮苶滉쫹団㋦黦竵웹ꋕ\u0ef6\ueafa盭틄뻾\u1ae1\ue6ef䋫\u2efd諳", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 39937, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!o.ei.c.c().q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            i("䛗쨜参汥쀴ಂ", 6 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            i("䛗쨜参汥쀴ಂ鿎즧若ቅ껖ᕼ祁ឧ⃦ﵡ\ud89c轉笼Ԏ埋㽆讦ҏ䮪⑰ꪠ븼ઉ켳Ꚍ\uf1caꠁ聗於\u180e\uf0ff딁ណቝ\uea48珟", 42 - View.MeasureSpec.getSize(0), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        this.a.k(str).e().e(context, str, new d.e<o.eo.j>() { // from class: o.em.e.5
            private static int a = 0;
            private static int c = 1;

            @Override // o.em.d.e
            public final void e(List<o.eo.j> list) {
                int i3 = c;
                int i4 = (i3 & 41) + (i3 | 41);
                a = i4 % 128;
                int i5 = i4 % 2;
                e.this.e(context);
                cVar.a(list);
                int i6 = c;
                int i7 = ((i6 | 83) << 1) - (i6 ^ 83);
                a = i7 % 128;
                switch (i7 % 2 != 0 ? 'I' : ':') {
                    case Opcodes.ASTORE /* 58 */:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.em.d.e
            public final void e(AntelopError antelopError) {
                int i3 = c;
                int i4 = ((i3 | 71) << 1) - (i3 ^ 71);
                a = i4 % 128;
                switch (i4 % 2 != 0 ? 'c' : 'S') {
                    case Opcodes.DADD /* 99 */:
                        cVar.d(antelopError);
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        cVar.d(antelopError);
                        return;
                }
            }
        });
        int i3 = f + 7;
        g = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void d(android.content.Context r6, java.lang.String r7, o.eo.f r8, o.eo.f.d r9) {
        /*
            r5 = this;
            o.em.a r0 = r5.b()
            o.em.a$c r7 = r0.f(r7)
            if (r7 != 0) goto Lc
            return
        Lc:
            o.em.c r7 = r7.b()
            java.util.List r7 = r7.d()
            java.util.Iterator r7 = r7.iterator()
        L19:
            boolean r0 = r7.hasNext()
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L23
            r0 = r1
            goto L24
        L23:
            r0 = r2
        L24:
            switch(r0) {
                case 1: goto L2b;
                default: goto L27;
            }
        L27:
            r5.e(r6)
            return
        L2b:
            int r0 = o.em.e.f
            int r0 = r0 + 83
            int r3 = r0 % 128
            o.em.e.g = r3
            int r0 = r0 % 2
            java.lang.Object r0 = r7.next()
            o.eo.f r0 = (o.eo.f) r0
            java.lang.String r3 = r0.b()
            java.lang.String r4 = r8.b()
            boolean r3 = r3.equals(r4)
            if (r3 == 0) goto L4c
            r3 = 41
            goto L4e
        L4c:
            r3 = 53
        L4e:
            switch(r3) {
                case 41: goto L52;
                default: goto L51;
            }
        L51:
            goto L19
        L52:
            int r6 = o.em.e.f
            int r6 = r6 + 25
            int r7 = r6 % 128
            o.em.e.g = r7
            int r6 = r6 % 2
            if (r6 != 0) goto L5f
            goto L60
        L5f:
            r1 = r2
        L60:
            r0.c(r9)
            switch(r1) {
                case 1: goto L67;
                default: goto L66;
            }
        L66:
            goto L6d
        L67:
            r6 = 44
            int r6 = r6 / r2
            goto L6d
        L6b:
            r6 = move-exception
            throw r6
        L6d:
            int r6 = o.em.e.g
            int r6 = r6 + 67
            int r7 = r6 % 128
            o.em.e.f = r7
            int r6 = r6 % 2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.d(android.content.Context, java.lang.String, o.eo.f, o.eo.f$d):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void e(android.content.Context r4, java.lang.String r5, o.eo.j r6, o.eo.j.b r7) {
        /*
            r3 = this;
            o.em.a r0 = r3.b()
            o.em.a$c r5 = r0.f(r5)
            if (r5 != 0) goto Lc
            return
        Lc:
            o.em.g r5 = r5.e()
            java.util.List r5 = r5.d()
            java.util.Iterator r5 = r5.iterator()
            int r0 = o.em.e.f
            int r0 = r0 + 117
            int r1 = r0 % 128
            o.em.e.g = r1
            int r0 = r0 % 2
        L22:
            boolean r0 = r5.hasNext()
            if (r0 == 0) goto L2b
            r0 = 80
            goto L2d
        L2b:
            r0 = 12
        L2d:
            switch(r0) {
                case 80: goto L34;
                default: goto L30;
            }
        L30:
            r3.e(r4)
            return
        L34:
            int r0 = o.em.e.f
            int r0 = r0 + 67
            int r1 = r0 % 128
            o.em.e.g = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L42
            r0 = 0
            goto L43
        L42:
            r0 = 1
        L43:
            switch(r0) {
                case 0: goto L5b;
                default: goto L46;
            }
        L46:
            java.lang.Object r0 = r5.next()
            o.eo.j r0 = (o.eo.j) r0
            java.lang.String r1 = r0.e()
            java.lang.String r2 = r6.e()
            boolean r1 = r1.equals(r2)
            if (r1 == 0) goto L7e
            goto L70
        L5b:
            java.lang.Object r4 = r5.next()
            o.eo.j r4 = (o.eo.j) r4
            java.lang.String r4 = r4.e()
            java.lang.String r5 = r6.e()
            r4.equals(r5)
            r4 = 0
            throw r4     // Catch: java.lang.Throwable -> L6e
        L6e:
            r4 = move-exception
            throw r4
        L70:
            r0.b(r7)
            int r4 = o.em.e.g
            int r4 = r4 + 107
            int r5 = r4 % 128
            o.em.e.f = r5
            int r4 = r4 % 2
            return
        L7e:
            goto L22
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.e(android.content.Context, java.lang.String, o.eo.j, o.eo.j$b):void");
    }

    public final void b(final Context context, String str, final c<o.es.a<?>> cVar) throws WalletValidationException {
        int i = f + 59;
        g = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j("⫝̸ꪔ⨵ꯞ⭸\uab00⢪ꠘ⧑ꥧ⤎꺝⹁꿊⽱꼆Ⲻ걎ⷧ궔", 32870 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j("⫪\uf5f4铬럱囙燆\u10c6㏳튢ﶨ鲬뾈底禎ᢒ㭶\uda67\ue56f葹Ꝝ䙂慑,⌸숬\ued0a", 57097 - (KeyEvent.getMaxKeyCode() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!o.ei.c.c().q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            i("䛗쨜参汥쀴ಂ", 6 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            i("䛗쨜参汥쀴ಂ鿎즧若ቅ껖ᕼ祁ឧ⃦ﵡ\ud89c轉笼Ԏ埋㽆讦ҏ䮪⑰ꪠ븼ઉ켳Ꚍ\uf1caꠁ聗於\u180e\uf0ff딁ណቝ\uea48珟", 41 - TextUtils.lastIndexOf("", '0'), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        this.a.k(str).a().b(context, str, new d.e<o.es.a<?>>() { // from class: o.em.e.1
            private static int e = 0;
            private static int c = 1;

            @Override // o.em.d.e
            public final void e(List<o.es.a<?>> list) {
                int i3 = (c + 78) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
                e.this.e(context);
                cVar.a(list);
                int i5 = c;
                int i6 = ((i5 | 25) << 1) - (i5 ^ 25);
                e = i6 % 128;
                int i7 = i6 % 2;
            }

            @Override // o.em.d.e
            public final void e(AntelopError antelopError) {
                int i3 = (e + 22) - 1;
                c = i3 % 128;
                int i4 = i3 % 2;
                cVar.d(antelopError);
                int i5 = c + 41;
                e = i5 % 128;
                int i6 = i5 % 2;
            }
        });
        int i3 = f + Opcodes.LSHL;
        g = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 29 : 'Z') {
            case 'Z':
                return;
            default:
                int i4 = 54 / 0;
                return;
        }
    }

    public final void a(Context context, String str, List<o.es.a<?>> list, Date date) {
        a.c k = this.a.k(str);
        k.a().a(list, new Date().getTime());
        k.a().a(date);
        e(context);
        int i = f + 25;
        g = i % 128;
        int i2 = i % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.i(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:67:0x0033. Please report as an issue. */
    private static void j(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 508
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.e.j(java.lang.String, int, java.lang.Object[]):void");
    }
}
