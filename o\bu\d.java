package o.bu;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.location.Location;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.app.ActivityCompat;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.j;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bu\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static char[] c;
    private static boolean d;
    private static boolean e;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        j = 1;
        a();
        AudioTrack.getMaxVolume();
        int i = j + 25;
        a = i % 128;
        switch (i % 2 != 0 ? 'b' : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                break;
            default:
                int i2 = 55 / 0;
                break;
        }
    }

    static void a() {
        c = new char[]{61803, 61576, 61820, 61822, 61587, 61574, 61577, 61802, 61568, 61570, 61581, 61759, 61770, 61579, 61584, 61571, 61821, 61590, 61580, 61781, 61586, 61769, 61583, 61578, 61790, 61788, 61794, 61804, 61816, 61793, 61798, 61801, 61800, 61811, 61805, 61585};
        d = true;
        e = true;
        b = 782102815;
    }

    private static void g(int i, short s, byte b2, Object[] objArr) {
        int i2 = 1 - (i * 2);
        byte[] bArr = $$a;
        int i3 = 121 - b2;
        int i4 = 3 - (s * 2);
        byte[] bArr2 = new byte[i2];
        int i5 = -1;
        int i6 = i2 - 1;
        if (bArr == null) {
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i3 = (-i6) + i3;
            i6 = i6;
            i4 = i4;
        }
        while (true) {
            int i7 = i5 + 1;
            int i8 = i4 + 1;
            bArr2[i7] = (byte) i3;
            if (i7 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i8];
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = i7;
            i3 = (-b3) + i3;
            i6 = i6;
            i4 = i8;
        }
    }

    static void init$0() {
        $$a = new byte[]{71, 41, -111, Base64.padSymbol};
        $$b = Opcodes.INVOKEINTERFACE;
    }

    public static Location b(Context context, o.ei.c cVar) {
        int i = a + 9;
        j = i % 128;
        int i2 = i % 2;
        Location c2 = c(context, cVar.e().c().b(context));
        int i3 = a + 65;
        j = i3 % 128;
        int i4 = i3 % 2;
        return c2;
    }

    private static Location c(Context context, boolean z) {
        int i = j;
        int i2 = i + 87;
        a = i2 % 128;
        int i3 = i2 % 2;
        if (!z) {
            int i4 = i + Opcodes.LSHL;
            a = i4 % 128;
            int i5 = i4 % 2;
            g.c();
            Object[] objArr = new Object[1];
            f(null, 127 - (ViewConfiguration.getTapTimeout() >> 16), null, "\u008b\u008a\u0089\u0084\u0087\u0084\u0088\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u008e\u008e\u0095\u0087\u008c\u0089\u0087\u0086\u0087\u008b\u0095\u0085\u008a\u008b\u008c\u0094\u008c\u0093\u0089\u0087\u0086\u0085\u0085\u008a\u0093\u008c\u0085\u008a\u008e\u008e\u0084\u008f\u008c\u0092\u0091\u008c\u0090\u008a\u008f\u0082\u008e\u008e\u0084\u008c\u0085\u0082\u0087\u008c\u008d\u008c\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081\u0085\u008a\u0089", objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            return null;
        }
        Object[] objArr3 = new Object[1];
        f(null, ((byte) KeyEvent.getModifierMetaStateMask()) + ByteCompanionObject.MIN_VALUE, null, " ¡\u009f¢\u0099\u009a¡\u0081\u009d\u009b \u009f\u009e\u009d\u009c\u009c\u009b\u009a\u009a\u0099\u0096\u0087\u0082\u0086\u0093\u0093\u0086\u0098\u008b\u008a\u0097\u0096\u0090\u0086\u0082\u008b\u0090\u0087\u0084", objArr3);
        switch (ActivityCompat.checkSelfPermission(context, ((String) objArr3[0]).intern()) != 0 ? 'K' : 'H') {
            case 'K':
                Object[] objArr4 = new Object[1];
                f(null, 126 - Process.getGidForName(""), null, " ¡\u009f¢\u0099\u009a¡\u0081\u009d\u009b\u009c£\u0099¡\u009a\u009d\u009c\u009c\u009b\u009a\u009a\u0099\u0096\u0087\u0082\u0086\u0093\u0093\u0086\u0098\u008b\u008a\u0097\u0096\u0090\u0086\u0082\u008b\u0090\u0087\u0084", objArr4);
                switch (ActivityCompat.checkSelfPermission(context, ((String) objArr4[0]).intern()) != 0 ? '7' : '\b') {
                    case '\b':
                        break;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f(null, 127 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), null, "\u008b\u008a\u0089\u0084\u0087\u0084\u0088\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081", objArr5);
                        String intern2 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        f(null, 127 - (ViewConfiguration.getKeyRepeatDelay() >> 16), null, "\u008e\u008e\u0095\u0087\u008c\u0089\u0087\u0086\u0087\u008b\u0095\u0085\u008a\u008b\u008c\u0094\u008c\u0090\u008a\u0085\u0087\u0084\u008b\u0089\u008c\u0087\u0082\u0086\u0093\u0093\u0086\u0098\u008b\u008a\u0097\u008c\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u008e\u008c\u0082\u0087\u008c\u008d\u008c\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081\u0085\u008a\u0089", objArr6);
                        g.d(intern2, ((String) objArr6[0]).intern());
                        int i6 = a + 85;
                        j = i6 % 128;
                        if (i6 % 2 != 0) {
                            return null;
                        }
                        int i7 = 15 / 0;
                        return null;
                }
        }
        new e();
        c c2 = e.c(context);
        switch (c2 == null ? 'M' : 'K') {
            case 'K':
                return c2.d(context);
            default:
                int i8 = j + 99;
                a = i8 % 128;
                if (i8 % 2 != 0) {
                }
                g.c();
                Object[] objArr7 = new Object[1];
                f(null, MotionEvent.axisFromString("") + 128, null, "\u008b\u008a\u0089\u0084\u0087\u0084\u0088\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081", objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                f(null, 127 - (ViewConfiguration.getTapTimeout() >> 16), null, "\u008e\u008e\u0095\u0087\u008c\u0089\u0087\u0086\u0087\u008b\u0095\u0085\u008a\u008b\u008c\u0094\u008c\u008a\u008e\u0091\u0084\u008e\u0086\u0084¤\u0084\u008c\u008b\u008a\u0090\u0086¤\u0082\u008b\u0097\u008c\u0082\u0087\u008c\u008d\u008c\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081\u0085\u008a\u0089", objArr8);
                g.d(intern3, ((String) objArr8[0]).intern());
                int i9 = j + 31;
                a = i9 % 128;
                int i10 = i9 % 2;
                return null;
        }
    }

    private static void f(String str, int i, int[] iArr, String str2, Object[] objArr) {
        String str3 = str2;
        int i2 = $11 + 91;
        $10 = i2 % 128;
        byte[] bArr = str3;
        if (i2 % 2 != 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        if (str3 != null) {
            bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        char[] charArray = str != null ? str.toCharArray() : str;
        j jVar = new j();
        char[] cArr = c;
        int i3 = 0;
        switch (cArr != null ? (char) 26 : 'G') {
            case 26:
                int length = cArr.length;
                char[] cArr2 = new char[length];
                int i4 = 0;
                while (i4 < length) {
                    try {
                        Object[] objArr2 = new Object[1];
                        objArr2[i3] = Integer.valueOf(cArr[i4]);
                        Object obj2 = o.e.a.s.get(1085633688);
                        if (obj2 == null) {
                            Class cls = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0', i3, i3), (char) (Process.getGidForName("") + 1), 493 - (TypedValue.complexToFloat(i3) > 0.0f ? 1 : (TypedValue.complexToFloat(i3) == 0.0f ? 0 : -1)));
                            byte b2 = (byte) i3;
                            byte b3 = b2;
                            Object[] objArr3 = new Object[1];
                            g(b2, b3, b3, objArr3);
                            obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1085633688, obj2);
                        }
                        cArr2[i4] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                        i4++;
                        i3 = 0;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr = cArr2;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(b)};
            Object obj3 = o.e.a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls2 = (Class) o.e.a.c(10 - ((Process.getThreadPriority(0) + 20) >> 6), (char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 8856), TextUtils.indexOf("", "") + 324);
                byte b4 = (byte) 0;
                byte b5 = b4;
                Object[] objArr5 = new Object[1];
                g(b4, b5, (byte) (b5 + 3), objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
            switch (e) {
                case true:
                    jVar.e = bArr2.length;
                    char[] cArr3 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr3[jVar.c] = (char) (cArr[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - (ViewConfiguration.getLongPressTimeout() >> 16), (char) View.MeasureSpec.getSize(0), 207 - TextUtils.getOffsetAfter("", 0));
                                byte b6 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                g(b6, b6, (byte) $$a.length, objArr7);
                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    objArr[0] = new String(cArr3);
                    return;
                default:
                    if (!d) {
                        jVar.e = iArr.length;
                        char[] cArr4 = new char[jVar.e];
                        jVar.c = 0;
                        while (jVar.c < jVar.e) {
                            int i5 = $11 + 99;
                            $10 = i5 % 128;
                            int i6 = i5 % 2;
                            cArr4[jVar.c] = (char) (cArr[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                            jVar.c++;
                        }
                        objArr[0] = new String(cArr4);
                        return;
                    }
                    int i7 = $10 + Opcodes.DDIV;
                    $11 = i7 % 128;
                    int i8 = i7 % 2;
                    jVar.e = charArray.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    int i9 = $11 + 63;
                    $10 = i9 % 128;
                    int i10 = i9 % 2;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr[charArray[(jVar.e - 1) - jVar.c] - i] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj5 = o.e.a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls4 = (Class) o.e.a.c(Color.rgb(0, 0, 0) + 16777226, (char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 207 - Color.green(0));
                                byte b7 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                g(b7, b7, (byte) $$a.length, objArr9);
                                obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
