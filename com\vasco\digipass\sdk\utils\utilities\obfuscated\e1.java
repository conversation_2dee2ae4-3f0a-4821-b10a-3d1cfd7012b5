package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e1.smali */
class e1 implements h, b5 {
    final g0 C;
    final int b;
    final int x;

    e1(int i, int i2, g0 g0Var) {
        this.b = i;
        this.x = i2;
        this.C = g0Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return this.C.a(this.b, this.x);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0(e.getMessage());
        }
    }
}
