package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\t7.smali */
public class t7 {
    private static final short[] a = new short[128];
    private static final byte[] b;

    static {
        byte[] bArr = new byte[Opcodes.IREM];
        b = bArr;
        byte[] bArr2 = new byte[128];
        a(bArr2, 0, 15, (byte) 1);
        a(bArr2, 16, 31, (byte) 2);
        a(bArr2, 32, 63, (byte) 3);
        a(bArr2, 64, 65, (byte) 0);
        a(bArr2, 66, 95, (byte) 4);
        a(bArr2, 96, 96, (byte) 5);
        a(bArr2, 97, 108, (byte) 6);
        a(bArr2, 109, 109, (byte) 7);
        a(bArr2, Opcodes.FDIV, Opcodes.DDIV, (byte) 6);
        a(bArr2, Opcodes.IREM, Opcodes.IREM, (byte) 8);
        a(bArr2, Opcodes.LREM, Opcodes.DREM, (byte) 9);
        a(bArr2, Opcodes.INEG, Opcodes.INEG, (byte) 10);
        a(bArr2, Opcodes.LNEG, 127, (byte) 0);
        a(bArr, 0, Opcodes.DDIV, (byte) -2);
        a(bArr, 8, 11, (byte) -1);
        a(bArr, 24, 27, (byte) 0);
        a(bArr, 40, 43, Tnaf.POW_2_WIDTH);
        a(bArr, 58, 59, (byte) 0);
        a(bArr, 72, 73, (byte) 0);
        a(bArr, 89, 91, Tnaf.POW_2_WIDTH);
        a(bArr, 104, 104, Tnaf.POW_2_WIDTH);
        byte[] bArr3 = {0, 0, 0, 0, 31, 15, 15, 15, 7, 7, 7};
        byte[] bArr4 = {-2, -2, -2, -2, 0, 48, Tnaf.POW_2_WIDTH, 64, 80, 32, 96};
        for (int i = 0; i < 128; i++) {
            byte b2 = bArr2[i];
            a[i] = (short) (bArr4[b2] | ((bArr3[b2] & i) << 8));
        }
    }

    private static void a(byte[] bArr, int i, int i2, byte b2) {
        while (i <= i2) {
            bArr[i] = b2;
            i++;
        }
    }

    public static int a(byte[] bArr, char[] cArr) {
        return a(bArr, 0, bArr.length, cArr);
    }

    public static int a(byte[] bArr, int i, int i2, char[] cArr) {
        int i3 = i2 + i;
        int i4 = 0;
        while (i < i3) {
            int i5 = i + 1;
            byte b2 = bArr[i];
            if (b2 >= 0) {
                if (i4 >= cArr.length) {
                    return -1;
                }
                cArr[i4] = (char) b2;
                i = i5;
                i4++;
            } else {
                short s = a[b2 & ByteCompanionObject.MAX_VALUE];
                int i6 = s >>> 8;
                byte b3 = (byte) s;
                while (b3 >= 0) {
                    if (i5 >= i3) {
                        return -1;
                    }
                    int i7 = i5 + 1;
                    byte b4 = bArr[i5];
                    i6 = (i6 << 6) | (b4 & 63);
                    b3 = b[b3 + ((b4 & 255) >>> 4)];
                    i5 = i7;
                }
                if (b3 == -2) {
                    return -1;
                }
                if (i6 <= 65535) {
                    if (i4 >= cArr.length) {
                        return -1;
                    }
                    cArr[i4] = (char) i6;
                    i4++;
                    i = i5;
                } else {
                    if (i4 >= cArr.length - 1) {
                        return -1;
                    }
                    int i8 = i4 + 1;
                    cArr[i4] = (char) ((i6 >>> 10) + 55232);
                    i4 = i8 + 1;
                    cArr[i8] = (char) ((i6 & 1023) | 56320);
                    i = i5;
                }
            }
        }
        return i4;
    }
}
