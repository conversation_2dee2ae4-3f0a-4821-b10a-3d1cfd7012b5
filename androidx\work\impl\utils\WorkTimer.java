package androidx.work.impl.utils;

import androidx.work.Logger;
import androidx.work.RunnableScheduler;
import androidx.work.impl.model.WorkGenerationalId;
import java.util.HashMap;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\WorkTimer.smali */
public class WorkTimer {
    private static final String TAG = Logger.tagWithPrefix("WorkTimer");
    final RunnableScheduler mRunnableScheduler;
    final Map<WorkGenerationalId, WorkTimerRunnable> mTimerMap = new HashMap();
    final Map<WorkGenerationalId, TimeLimitExceededListener> mListeners = new HashMap();
    final Object mLock = new Object();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\WorkTimer$TimeLimitExceededListener.smali */
    public interface TimeLimitExceededListener {
        void onTimeLimitExceeded(WorkGenerationalId id);
    }

    public WorkTimer(RunnableScheduler scheduler) {
        this.mRunnableScheduler = scheduler;
    }

    public void startTimer(final WorkGenerationalId id, long processingTimeMillis, TimeLimitExceededListener listener) {
        synchronized (this.mLock) {
            Logger.get().debug(TAG, "Starting timer for " + id);
            stopTimer(id);
            WorkTimerRunnable runnable = new WorkTimerRunnable(this, id);
            this.mTimerMap.put(id, runnable);
            this.mListeners.put(id, listener);
            this.mRunnableScheduler.scheduleWithDelay(processingTimeMillis, runnable);
        }
    }

    public void stopTimer(final WorkGenerationalId id) {
        synchronized (this.mLock) {
            WorkTimerRunnable removed = this.mTimerMap.remove(id);
            if (removed != null) {
                Logger.get().debug(TAG, "Stopping timer for " + id);
                this.mListeners.remove(id);
            }
        }
    }

    public Map<WorkGenerationalId, WorkTimerRunnable> getTimerMap() {
        Map<WorkGenerationalId, WorkTimerRunnable> map;
        synchronized (this.mLock) {
            map = this.mTimerMap;
        }
        return map;
    }

    public Map<WorkGenerationalId, TimeLimitExceededListener> getListeners() {
        Map<WorkGenerationalId, TimeLimitExceededListener> map;
        synchronized (this.mLock) {
            map = this.mListeners;
        }
        return map;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\WorkTimer$WorkTimerRunnable.smali */
    public static class WorkTimerRunnable implements Runnable {
        static final String TAG = "WrkTimerRunnable";
        private final WorkGenerationalId mWorkGenerationalId;
        private final WorkTimer mWorkTimer;

        WorkTimerRunnable(WorkTimer workTimer, WorkGenerationalId id) {
            this.mWorkTimer = workTimer;
            this.mWorkGenerationalId = id;
        }

        @Override // java.lang.Runnable
        public void run() {
            synchronized (this.mWorkTimer.mLock) {
                WorkTimerRunnable removed = this.mWorkTimer.mTimerMap.remove(this.mWorkGenerationalId);
                if (removed != null) {
                    TimeLimitExceededListener listener = this.mWorkTimer.mListeners.remove(this.mWorkGenerationalId);
                    if (listener != null) {
                        listener.onTimeLimitExceeded(this.mWorkGenerationalId);
                    }
                } else {
                    Logger.get().debug(TAG, String.format("Timer with %s is already marked as complete.", this.mWorkGenerationalId));
                }
            }
        }
    }
}
