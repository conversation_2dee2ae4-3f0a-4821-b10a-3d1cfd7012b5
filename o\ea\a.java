package o.ea;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.bv.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ea\a.smali */
public final class a extends d {
    private static int b;
    private static int c;
    private static int d = 1;

    static {
        b = 0;
        b();
        ViewConfiguration.getTapTimeout();
        SystemClock.currentThreadTimeMillis();
        TextUtils.getCapsMode("", 0, 0);
        int i = d + Opcodes.LSHR;
        b = i % 128;
        switch (i % 2 != 0 ? '-' : ';') {
            case ';':
                return;
            default:
                throw null;
        }
    }

    static void b() {
        c = 874635322;
    }
}
