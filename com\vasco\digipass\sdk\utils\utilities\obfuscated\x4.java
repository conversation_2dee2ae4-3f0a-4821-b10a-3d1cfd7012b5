package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x4.smali */
public class x4 extends u {
    private w C;
    private w b;
    private w x;

    private x4(e0 e0Var) {
        this.b = (w) e0Var.a(0);
        this.x = (w) e0Var.a(1);
        if (e0Var.size() > 2) {
            this.C = (w) e0Var.a(2);
        }
    }

    public static x4 a(Object obj) {
        if (obj instanceof x4) {
            return (x4) obj;
        }
        if (obj != null) {
            return new x4(e0.a(obj));
        }
        return null;
    }

    public w e() {
        return this.x;
    }

    public w f() {
        return this.C;
    }

    public w g() {
        return this.b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(3);
        iVar.a(this.b);
        iVar.a(this.x);
        w wVar = this.C;
        if (wVar != null) {
            iVar.a(wVar);
        }
        return new j2(iVar);
    }
}
