package o.bg;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.WalletLockReason;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.m;
import o.bb.e;
import o.cf.i;
import o.cf.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\b.smali */
public final class b extends o.y.b<c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int c;
    private static int d;
    int a;
    WalletLockReason e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\b$c.smali */
    public interface c {
        void b();

        void b(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        d = 1;
        n();
        int i = d + 33;
        c = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{57, -45, 96, 7};
        $$e = 0;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.bg.b.$$d
            int r8 = 122 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L19:
            r3 = r2
        L1a:
            int r6 = r6 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bg.b.m(byte, short, int, java.lang.Object[]):void");
    }

    static void n() {
        b = new char[]{50943, 50852, 50858, 50858, 50848, 50851, 50836, 50838, 50849, 50853, 50849, 50859, 50833, 50860, 50858, 50852, 50832, 50819, 50772, 50774, 50770};
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i = d + 73;
        c = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? 'Z' : 'B') {
            case 'Z':
                k();
                obj.hashCode();
                throw null;
            default:
                a k = k();
                int i2 = d + Opcodes.DSUB;
                c = i2 % 128;
                switch (i2 % 2 != 0 ? 'V' : (char) 0) {
                    case 0:
                        return k;
                    default:
                        throw null;
                }
        }
    }

    public b(Context context, c cVar, o.ei.c cVar2) {
        super(context, cVar, cVar2, e.f);
    }

    /* renamed from: o.bg.b$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\b$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int c;
        private static int d = 1;
        static final /* synthetic */ int[] e;

        static {
            c = 0;
            int[] iArr = new int[WalletLockReason.values().length];
            e = iArr;
            try {
                iArr[WalletLockReason.FraudulentUseSuspected.ordinal()] = 1;
                int i = d;
                int i2 = (i ^ 73) + ((i & 73) << 1);
                c = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[WalletLockReason.StopService.ordinal()] = 2;
                int i3 = d;
                int i4 = (i3 & 85) + (i3 | 85);
                c = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[WalletLockReason.StrongestCvmAttemptCountExceeded.ordinal()] = 3;
                int i5 = d;
                int i6 = (i5 & 89) + (i5 | 89);
                c = i6 % 128;
                if (i6 % 2 != 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[WalletLockReason.OtherReason.ordinal()] = 4;
                int i7 = d;
                int i8 = (i7 ^ Opcodes.LMUL) + ((i7 & Opcodes.LMUL) << 1);
                c = i8 % 128;
                int i9 = i8 % 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public final void d(WalletLockReason walletLockReason) {
        int i = d + 11;
        c = i % 128;
        int i2 = i % 2;
        this.e = walletLockReason;
        switch (AnonymousClass1.e[walletLockReason.ordinal()]) {
            case 1:
                this.a = 2;
                break;
            case 2:
                this.a = 3;
                int i3 = d + 3;
                c = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
        c();
    }

    private a k() {
        a aVar = new a(this);
        int i = d + 49;
        c = i % 128;
        int i2 = i % 2;
        return aVar;
    }

    @Override // o.y.b
    public final String a() {
        int i = d + 1;
        c = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        l("\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001", new int[]{0, 17, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = c + 95;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return intern;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bg\b$a.smali */
    static final class a extends o.y.c<b> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static char b;
        private static char[] c;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            e = 1;
            c = new char[]{30525, 30520, 30527, 30496, 30500, 30560, 30518, 30524, 30572, 30503, 30589, 30570, 30574, 30563, 30521, 30522, 30564, 30523, 30588, 30561, 30502, 30519, 30497, 30526, 30501};
            b = (char) 17040;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, byte r7, int r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.bg.b.a.$$d
                int r6 = r6 * 4
                int r6 = r6 + 1
                int r8 = 73 - r8
                int r7 = r7 * 4
                int r7 = 4 - r7
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L1b
                r8 = r7
                r3 = r1
                r4 = r2
                r7 = r6
                r1 = r0
                r0 = r9
                r9 = r8
                goto L34
            L1b:
                r3 = r2
            L1c:
                byte r4 = (byte) r8
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                r3 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L34:
                int r8 = r8 + r6
                int r6 = r9 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L1c
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bg.b.a.B(byte, byte, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{78, -3, -72, 11};
            $$e = 87;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = e + 47;
            d = i % 128;
            switch (i % 2 != 0 ? (char) 15 : 'Z') {
                case 15:
                    throw null;
                default:
                    return;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = d + 51;
            e = i % 128;
            switch (i % 2 == 0 ? 'b' : (char) 16) {
                case Opcodes.FADD /* 98 */:
                    throw null;
                default:
                    return;
            }
        }

        a(b bVar) {
            super(bVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = e + Opcodes.DDIV;
            d = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(TextUtils.indexOf("", "", 0) + 4, "\n\b\u0006\u0012", (byte) (122 - (ViewConfiguration.getLongPressTimeout() >> 16)), objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = d + Opcodes.LSHL;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return intern;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 19, "\u0016\u0006\u0016\b\u0012\u0014\u0000\u0011㗙㗙\u000b\t\u0010\u0007\u0014\u0001\u0002\u0010㗙", (byte) (42 - View.resolveSizeAndState(0, 0, 0)), objArr);
            o.cf.d dVar = new o.cf.d(context, 8, ((String) objArr[0]).intern());
            int i = d + 29;
            e = i % 128;
            switch (i % 2 != 0) {
                case false:
                    throw null;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(6 - Color.blue(0), "\u000b\f\r\u0011\t\u000f", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 70), objArr);
            bVar.d(((String) objArr[0]).intern(), ((b) e()).a);
            int i = d + Opcodes.LSHL;
            e = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        @Override // o.y.c
        public final j n() {
            int i = d + 79;
            e = i % 128;
            switch (i % 2 == 0 ? '\n' : 'P') {
                case 'P':
                    return null;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = d + 75;
            e = i % 128;
            Object obj = null;
            switch (i % 2 == 0 ? '?' : Typography.dollar) {
                case '$':
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = d + 31;
            e = i % 128;
            switch (i % 2 == 0 ? '0' : 'X') {
                case '0':
                    o.b.c.c(g());
                    i().b(((b) e()).e);
                    throw null;
                default:
                    o.b.c.c(g());
                    i().b(((b) e()).e);
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 81;
            e = i % 128;
            int i2 = i % 2;
            ((b) e()).j().b();
            int i3 = e + 45;
            d = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 30 : 'Q') {
                case 30:
                    return;
                default:
                    int i4 = 13 / 0;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = e + 19;
            d = i % 128;
            int i2 = i % 2;
            ((b) e()).j().b(dVar);
            int i3 = e + 19;
            d = i3 % 128;
            switch (i3 % 2 != 0 ? '-' : 'G') {
                case 'G':
                    return;
                default:
                    int i4 = 78 / 0;
                    return;
            }
        }

        private static void w(int i, String str, byte b2, Object[] objArr) {
            int i2;
            int i3;
            int i4 = $11 + 45;
            $10 = i4 % 128;
            int i5 = 2;
            int i6 = i4 % 2;
            char[] charArray = str != null ? str.toCharArray() : str;
            m mVar = new m();
            char[] cArr = c;
            Object obj = null;
            switch (cArr != null ? 'Y' : (char) 16) {
                case 16:
                    break;
                default:
                    int length = cArr.length;
                    char[] cArr2 = new char[length];
                    int i7 = 0;
                    while (true) {
                        switch (i7 >= length) {
                            case true:
                                cArr = cArr2;
                                break;
                            default:
                                try {
                                    Object[] objArr2 = {Integer.valueOf(cArr[i7])};
                                    Object obj2 = o.e.a.s.get(-1401577988);
                                    if (obj2 == null) {
                                        Class cls = (Class) o.e.a.c(17 - Color.green(0), (char) KeyEvent.getDeadChar(0, 0), ExpandableListView.getPackedPositionType(0L) + 76);
                                        byte b3 = (byte) 0;
                                        byte b4 = b3;
                                        Object[] objArr3 = new Object[1];
                                        B(b3, b4, b4, objArr3);
                                        obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                        o.e.a.s.put(-1401577988, obj2);
                                    }
                                    cArr2[i7] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                                    i7++;
                                    obj = null;
                                    i5 = 2;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                        }
                    }
            }
            try {
                Object[] objArr4 = {Integer.valueOf(b)};
                Object obj3 = o.e.a.s.get(-1401577988);
                if (obj3 == null) {
                    Class cls2 = (Class) o.e.a.c(17 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (char) View.getDefaultSize(0, 0), 76 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                    byte b5 = (byte) 0;
                    byte b6 = b5;
                    Object[] objArr5 = new Object[1];
                    B(b5, b6, b6, objArr5);
                    obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj3);
                }
                char charValue = ((Character) ((Method) obj3).invoke(obj, objArr4)).charValue();
                char[] cArr3 = new char[i];
                int i8 = 9;
                switch (i % 2 == 0) {
                    case true:
                        i2 = i;
                        break;
                    default:
                        int i9 = $11 + 9;
                        $10 = i9 % 128;
                        if (i9 % i5 != 0) {
                        }
                        i2 = i - 1;
                        cArr3[i2] = (char) (charArray[i2] - b2);
                        break;
                }
                switch (i2 > 1) {
                    case false:
                        break;
                    default:
                        mVar.b = 0;
                        while (mVar.b < i2) {
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            switch (mVar.e != mVar.a) {
                                case true:
                                    try {
                                        Object[] objArr6 = new Object[13];
                                        objArr6[12] = mVar;
                                        objArr6[11] = Integer.valueOf(charValue);
                                        objArr6[10] = mVar;
                                        objArr6[i8] = mVar;
                                        objArr6[8] = Integer.valueOf(charValue);
                                        objArr6[7] = mVar;
                                        objArr6[6] = mVar;
                                        objArr6[5] = Integer.valueOf(charValue);
                                        objArr6[4] = mVar;
                                        objArr6[3] = mVar;
                                        objArr6[i5] = Integer.valueOf(charValue);
                                        objArr6[1] = mVar;
                                        objArr6[0] = mVar;
                                        Object obj4 = o.e.a.s.get(696901393);
                                        if (obj4 == null) {
                                            Class cls3 = (Class) o.e.a.c((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + i8, (char) (TextUtils.getTrimmedLength("") + 8856), 323 - TextUtils.indexOf((CharSequence) "", '0', 0, 0));
                                            byte b7 = (byte) 0;
                                            Object[] objArr7 = new Object[1];
                                            B(b7, b7, (byte) $$d.length, objArr7);
                                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(696901393, obj4);
                                        }
                                        if (((Integer) ((Method) obj4).invoke(null, objArr6)).intValue() != mVar.h) {
                                            i3 = 9;
                                            switch (mVar.c == mVar.d) {
                                                case false:
                                                    int i10 = (mVar.c * charValue) + mVar.h;
                                                    int i11 = (mVar.d * charValue) + mVar.i;
                                                    cArr3[mVar.b] = cArr[i10];
                                                    cArr3[mVar.b + 1] = cArr[i11];
                                                    break;
                                                default:
                                                    int i12 = $10 + Opcodes.LNEG;
                                                    $11 = i12 % 128;
                                                    if (i12 % 2 == 0) {
                                                    }
                                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                    int i13 = (mVar.c * charValue) + mVar.i;
                                                    int i14 = (mVar.d * charValue) + mVar.h;
                                                    cArr3[mVar.b] = cArr[i13];
                                                    cArr3[mVar.b + 1] = cArr[i14];
                                                    break;
                                            }
                                        } else {
                                            try {
                                                Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                Object obj5 = o.e.a.s.get(1075449051);
                                                if (obj5 != null) {
                                                    i3 = 9;
                                                } else {
                                                    Class cls4 = (Class) o.e.a.c(Color.red(0) + 11, (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 64);
                                                    byte b8 = (byte) 0;
                                                    Object[] objArr9 = new Object[1];
                                                    B(b8, b8, (byte) (-$$d[1]), objArr9);
                                                    String str2 = (String) objArr9[0];
                                                    i3 = 9;
                                                    obj5 = cls4.getMethod(str2, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(1075449051, obj5);
                                                }
                                                int intValue = ((Integer) ((Method) obj5).invoke(null, objArr8)).intValue();
                                                int i15 = (mVar.d * charValue) + mVar.h;
                                                cArr3[mVar.b] = cArr[intValue];
                                                cArr3[mVar.b + 1] = cArr[i15];
                                                break;
                                            } catch (Throwable th2) {
                                                Throwable cause2 = th2.getCause();
                                                if (cause2 == null) {
                                                    throw th2;
                                                }
                                                throw cause2;
                                            }
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                default:
                                    i3 = i8;
                                    cArr3[mVar.b] = (char) (mVar.e - b2);
                                    cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                                    break;
                            }
                            mVar.b += 2;
                            int i16 = $10 + 93;
                            $11 = i16 % 128;
                            int i17 = i16 % 2;
                            i8 = i3;
                            i5 = 2;
                        }
                        break;
                }
                int i18 = 0;
                while (i18 < i) {
                    int i19 = $11 + 61;
                    $10 = i19 % 128;
                    if (i19 % 2 != 0) {
                        cArr3[i18] = (char) (cArr3[i18] & 7450);
                        i18 += 83;
                    } else {
                        cArr3[i18] = (char) (cArr3[i18] ^ 13722);
                        i18++;
                    }
                }
                objArr[0] = new String(cArr3);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:88:0x02f1, code lost:
    
        r2 = r0;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:76:0x02c3 A[LOOP_START] */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v32, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 850
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bg.b.l(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
