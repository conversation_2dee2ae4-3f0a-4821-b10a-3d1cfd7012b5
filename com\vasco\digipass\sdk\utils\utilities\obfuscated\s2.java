package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s2.smali */
public class s2 extends u {
    r C;
    r b;
    r x;

    private s2(e0 e0Var) {
        Enumeration j = e0Var.j();
        this.b = r.a(j.nextElement());
        this.x = r.a(j.nextElement());
        if (j.hasMoreElements()) {
            this.C = (r) j.nextElement();
        } else {
            this.C = null;
        }
    }

    public static s2 a(Object obj) {
        if (obj instanceof s2) {
            return (s2) obj;
        }
        if (obj != null) {
            return new s2(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.x.h();
    }

    public BigInteger f() {
        r rVar = this.C;
        if (rVar == null) {
            return null;
        }
        return rVar.h();
    }

    public BigInteger g() {
        return this.b.h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(3);
        iVar.a(this.b);
        iVar.a(this.x);
        if (f() != null) {
            iVar.a(this.C);
        }
        return new j2(iVar);
    }
}
