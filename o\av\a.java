package o.av;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.g;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\av\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final /* synthetic */ a[] b;
    public static final a c;
    public static final a d;
    private static int[] e;
    private static int f;
    private static int g;
    private final String a;

    static void d() {
        e = new int[]{1131900965, -245379918, -1084857311, -1867593848, -721095237, 2008159899, -1047669002, -1535765815, -460462702, 1959624360, 596088217, -683284945, 30484212, 203909706, -1716783111, -312068874, -1261522315, 620679636};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = 3 - r8
            int r9 = r9 + 115
            byte[] r0 = o.av.a.$$a
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r9 = r8
            r3 = r1
            r5 = r2
            r8 = r7
            r1 = r0
            r0 = r10
            r10 = r9
            goto L38
        L19:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1d:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            int r9 = r9 + 1
            if (r5 != r7) goto L2e
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2e:
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L38:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.av.a.i(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{33, 17, -65, 85};
        $$b = 64;
    }

    private static /* synthetic */ a[] a() {
        a[] aVarArr;
        int i = f;
        int i2 = i + Opcodes.DDIV;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? '2' : 'T') {
            case '2':
                aVarArr = new a[2];
                aVarArr[1] = d;
                aVarArr[1] = c;
                break;
            default:
                aVarArr = new a[]{d, c};
                break;
        }
        int i3 = i + 27;
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return aVarArr;
            default:
                throw null;
        }
    }

    public static a valueOf(String str) {
        int i = g + 35;
        f = i % 128;
        int i2 = i % 2;
        a aVar = (a) Enum.valueOf(a.class, str);
        int i3 = f + 109;
        g = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 14 : '\'') {
            case '\'':
                return aVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static a[] values() {
        int i = f + 37;
        g = i % 128;
        switch (i % 2 != 0 ? 'C' : Typography.amp) {
            case 'C':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return (a[]) b.clone();
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        f = 1;
        d();
        Object[] objArr = new Object[1];
        h(new int[]{-1470105394, -57505016}, 4 - View.resolveSize(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h(new int[]{1217105668, 646305625}, (KeyEvent.getMaxKeyCode() >> 16) + 4, objArr2);
        d = new a(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        h(new int[]{1946289261, -237737244, -1288444421, -1898469368, -1880945920, -1713918260, 297791804, -964154516}, (ViewConfiguration.getPressedStateDuration() >> 16) + 15, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        h(new int[]{-1468600711, -838076657, -1481402040, 1374709545, 864508920, -1818844221, -1352802396, -664282116}, 14 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr4);
        c = new a(intern2, 1, ((String) objArr4[0]).intern());
        b = a();
        int i = g + 81;
        f = i % 128;
        int i2 = i % 2;
    }

    private a(String str, int i, String str2) {
        this.a = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i = g + 67;
        f = i % 128;
        switch (i % 2 == 0 ? 'A' : (char) 7) {
            case 7:
                return this.a;
            default:
                int i2 = 53 / 0;
                return this.a;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.av.a b(java.lang.String r7) {
        /*
            int r0 = o.av.a.f
            int r0 = r0 + 81
            int r1 = r0 % 128
            o.av.a.g = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 61
            goto L11
        Lf:
            r0 = 15
        L11:
            r1 = 0
            switch(r0) {
                case 15: goto L1b;
                default: goto L15;
            }
        L15:
            o.av.a[] r0 = values()
            int r2 = r0.length
            goto L20
        L1b:
            o.av.a[] r0 = values()
            int r2 = r0.length
        L20:
            r3 = r1
        L21:
            if (r3 >= r2) goto L25
            r4 = 1
            goto L26
        L25:
            r4 = r1
        L26:
            r5 = 0
            switch(r4) {
                case 0: goto L35;
                default: goto L2a;
            }
        L2a:
            r4 = r0[r3]
            java.lang.String r6 = r4.a
            boolean r6 = r7.equals(r6)
            if (r6 == 0) goto L52
            goto L36
        L35:
            return r5
        L36:
            int r7 = o.av.a.g
            int r7 = r7 + 79
            int r0 = r7 % 128
            o.av.a.f = r0
            int r7 = r7 % 2
            if (r7 != 0) goto L46
            r7 = 16
            goto L48
        L46:
            r7 = 26
        L48:
            switch(r7) {
                case 16: goto L4c;
                default: goto L4b;
            }
        L4b:
            return r4
        L4c:
            r5.hashCode()     // Catch: java.lang.Throwable -> L50
            throw r5     // Catch: java.lang.Throwable -> L50
        L50:
            r7 = move-exception
            throw r7
        L52:
            int r3 = r3 + 1
            goto L21
        */
        throw new UnsupportedOperationException("Method not decompiled: o.av.a.b(java.lang.String):o.av.a");
    }

    private static void h(int[] iArr, int i, Object[] objArr) {
        int length;
        int[] iArr2;
        int i2;
        g gVar;
        int i3;
        int i4;
        int length2;
        int[] iArr3;
        int i5;
        int[] iArr4 = iArr;
        g gVar2 = new g();
        char[] cArr = new char[4];
        int i6 = 2;
        char[] cArr2 = new char[iArr4.length * 2];
        int[] iArr5 = e;
        long j = 0;
        int i7 = -1667374059;
        int i8 = 1;
        int i9 = 0;
        if (iArr5 != null) {
            int i10 = $11 + 31;
            $10 = i10 % 128;
            if (i10 % 2 != 0) {
                length2 = iArr5.length;
                iArr3 = new int[length2];
                i5 = 0;
            } else {
                length2 = iArr5.length;
                iArr3 = new int[length2];
                i5 = 0;
            }
            while (i5 < length2) {
                int i11 = $11 + 53;
                $10 = i11 % 128;
                int i12 = i11 % i6;
                try {
                    Object[] objArr2 = {Integer.valueOf(iArr5[i5])};
                    Object obj = o.e.a.s.get(Integer.valueOf(i7));
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((SystemClock.elapsedRealtime() > j ? 1 : (SystemClock.elapsedRealtime() == j ? 0 : -1)) + 9, (char) ((ViewConfiguration.getTapTimeout() >> 16) + 8856), 324 - (ViewConfiguration.getTapTimeout() >> 16));
                        byte b2 = (byte) 0;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        i(b2, b3, (byte) (b3 + 1), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1667374059, obj);
                    }
                    iArr3[i5] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                    i5++;
                    int i13 = $11 + Opcodes.LSHR;
                    $10 = i13 % 128;
                    switch (i13 % 2 != 0 ? Typography.quote : '6') {
                        case Opcodes.ISTORE /* 54 */:
                        default:
                            i6 = 2;
                            j = 0;
                            i7 = -1667374059;
                    }
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            iArr5 = iArr3;
        }
        int length3 = iArr5.length;
        int[] iArr6 = new int[length3];
        int[] iArr7 = e;
        switch (iArr7 != null ? '\r' : (char) 27) {
            case 27:
                break;
            default:
                int i14 = $11 + 95;
                $10 = i14 % 128;
                if (i14 % 2 != 0) {
                    length = iArr7.length;
                    iArr2 = new int[length];
                    i2 = 1;
                } else {
                    length = iArr7.length;
                    iArr2 = new int[length];
                    i2 = 0;
                }
                while (true) {
                    switch (i2 < length ? ',' : '4') {
                        case '4':
                            iArr7 = iArr2;
                            break;
                        default:
                            try {
                                Object[] objArr4 = new Object[i8];
                                objArr4[i9] = Integer.valueOf(iArr7[i2]);
                                Object obj2 = o.e.a.s.get(-1667374059);
                                if (obj2 != null) {
                                    gVar = gVar2;
                                    i3 = i8;
                                    i4 = i9;
                                } else {
                                    Class cls2 = (Class) o.e.a.c(10 - (Process.myPid() >> 22), (char) (8855 - ExpandableListView.getPackedPositionChild(0L)), (TypedValue.complexToFloat(i9) > 0.0f ? 1 : (TypedValue.complexToFloat(i9) == 0.0f ? 0 : -1)) + 324);
                                    byte b4 = (byte) i9;
                                    byte b5 = b4;
                                    gVar = gVar2;
                                    Object[] objArr5 = new Object[1];
                                    i(b4, b5, (byte) (b5 + 1), objArr5);
                                    i3 = 1;
                                    i4 = 0;
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj2);
                                }
                                iArr2[i2] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i2++;
                                i8 = i3;
                                i9 = i4;
                                gVar2 = gVar;
                                iArr4 = iArr;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                    }
                }
        }
        System.arraycopy(iArr7, i9, iArr6, i9, length3);
        gVar2.a = i9;
        while (gVar2.a < iArr4.length) {
            int i15 = $10 + 31;
            $11 = i15 % 128;
            int i16 = i15 % 2;
            cArr[i9] = (char) (iArr4[gVar2.a] >> 16);
            cArr[i8] = (char) iArr4[gVar2.a];
            cArr[2] = (char) (iArr4[gVar2.a + i8] >> 16);
            cArr[3] = (char) iArr4[gVar2.a + i8];
            gVar2.e = (cArr[i9] << 16) + cArr[i8];
            gVar2.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr6);
            int i17 = i9;
            for (int i18 = 16; i17 < i18; i18 = 16) {
                int i19 = $11 + 35;
                $10 = i19 % 128;
                switch (i19 % 2 == 0 ? Typography.amp : '#') {
                    case '&':
                        gVar2.e ^= iArr6[i17];
                        int b6 = g.b(gVar2.e);
                        try {
                            Object[] objArr6 = new Object[4];
                            objArr6[3] = gVar2;
                            objArr6[2] = gVar2;
                            objArr6[i8] = Integer.valueOf(b6);
                            objArr6[i9] = gVar2;
                            Object obj3 = o.e.a.s.get(-2036901605);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(11 - Color.alpha(i9), (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), 572 - TextUtils.indexOf("", "", i9));
                                Class<?>[] clsArr = new Class[4];
                                clsArr[i9] = Object.class;
                                clsArr[i8] = Integer.TYPE;
                                clsArr[2] = Object.class;
                                clsArr[3] = Object.class;
                                obj3 = cls3.getMethod("q", clsArr);
                                o.e.a.s.put(-2036901605, obj3);
                            }
                            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                            gVar2.e = gVar2.c;
                            gVar2.c = intValue;
                            i17++;
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    default:
                        gVar2.e |= iArr6[i17];
                        int b7 = g.b(gVar2.e);
                        try {
                            Object[] objArr7 = new Object[4];
                            objArr7[3] = gVar2;
                            objArr7[2] = gVar2;
                            objArr7[i8] = Integer.valueOf(b7);
                            objArr7[i9] = gVar2;
                            Object obj4 = o.e.a.s.get(-2036901605);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((Process.myTid() >> 22) + 11, (char) TextUtils.getTrimmedLength(""), 572 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                                Class<?>[] clsArr2 = new Class[4];
                                clsArr2[i9] = Object.class;
                                clsArr2[i8] = Integer.TYPE;
                                clsArr2[2] = Object.class;
                                clsArr2[3] = Object.class;
                                obj4 = cls4.getMethod("q", clsArr2);
                                o.e.a.s.put(-2036901605, obj4);
                            }
                            int intValue2 = ((Integer) ((Method) obj4).invoke(null, objArr7)).intValue();
                            gVar2.e = gVar2.c;
                            gVar2.c = intValue2;
                            i17 += Opcodes.DSUB;
                            break;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
            int i20 = gVar2.e;
            gVar2.e = gVar2.c;
            gVar2.c = i20;
            gVar2.c ^= iArr6[16];
            gVar2.e ^= iArr6[17];
            int i21 = gVar2.e;
            int i22 = gVar2.c;
            cArr[i9] = (char) (gVar2.e >>> 16);
            cArr[i8] = (char) gVar2.e;
            cArr[2] = (char) (gVar2.c >>> 16);
            cArr[3] = (char) gVar2.c;
            g.d(iArr6);
            cArr2[gVar2.a * 2] = cArr[i9];
            cArr2[(gVar2.a * 2) + i8] = cArr[i8];
            cArr2[(gVar2.a * 2) + 2] = cArr[2];
            cArr2[(gVar2.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr8 = {gVar2, gVar2};
                Object obj5 = o.e.a.s.get(-331007466);
                if (obj5 == null) {
                    Class cls5 = (Class) o.e.a.c((Process.myTid() >> 22) + 12, (char) (55183 - KeyEvent.normalizeMetaState(i9)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 515);
                    byte b8 = (byte) i9;
                    byte b9 = b8;
                    Object[] objArr9 = new Object[i8];
                    i(b8, b9, b9, objArr9);
                    String str = (String) objArr9[i9];
                    Class<?>[] clsArr3 = new Class[2];
                    clsArr3[i9] = Object.class;
                    clsArr3[i8] = Object.class;
                    obj5 = cls5.getMethod(str, clsArr3);
                    o.e.a.s.put(-331007466, obj5);
                }
                ((Method) obj5).invoke(null, objArr8);
            } catch (Throwable th5) {
                Throwable cause5 = th5.getCause();
                if (cause5 == null) {
                    throw th5;
                }
                throw cause5;
            }
        }
        objArr[i9] = new String(cArr2, i9, i);
    }
}
