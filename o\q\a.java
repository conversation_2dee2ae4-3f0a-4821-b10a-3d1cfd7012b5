package o.q;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\q\a.smali */
public final class a implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static char c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        c();
        SystemClock.currentThreadTimeMillis();
        ViewConfiguration.getZoomControlsTimeout();
        int i = e + 21;
        a = i % 128;
        switch (i % 2 != 0 ? 'Q' : '5') {
            case Opcodes.FASTORE /* 81 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void c() {
        b = new char[]{30587, 30554, 30570, 30572, 30568, 30574, 30539, 30586, 30588};
        c = (char) 17046;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            int r6 = r6 * 2
            int r6 = 1 - r6
            byte[] r0 = o.q.a.$$a
            int r7 = 73 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.a.g(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{15, -30, 44, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
        $$b = Opcodes.LNEG;
    }

    @Override // o.q.c
    public final void d(o.eg.b bVar) {
        int i = a + 39;
        e = i % 128;
        int i2 = i % 2;
    }

    @Override // o.q.c
    public final void d(c cVar) {
        int i = e + 31;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = a + 49;
        e = i % 128;
        boolean z = obj instanceof a;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return z;
        }
    }

    @Override // o.q.c
    public final o.eg.b e() {
        int i = e + 77;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = i2 + 69;
        e = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.q.c
    public final String b() {
        int i = e + 33;
        a = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(TextUtils.lastIndexOf("", '0', 0) + 13, "\u0006\u0005\u0004\u0002\u0002\b\u0005\u0001\b\u0003\u0002\u0003", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 8), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = e + 45;
        a = i3 % 128;
        switch (i3 % 2 != 0 ? '\n' : (char) 29) {
            case '\n':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:126:0x020e, code lost:
    
        if (r5.e == r5.a) goto L86;
     */
    /* JADX WARN: Code restructure failed: missing block: B:155:0x001f, code lost:
    
        if (r30 != null) goto L15;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:132:0x047c. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r29, java.lang.String r30, byte r31, java.lang.Object[] r32) {
        /*
            Method dump skipped, instructions count: 1224
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.a.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
