package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.obfuscated.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\ManageApplicationResponse.smali */
public class ManageApplicationResponse extends DigipassResponse {
    private byte[] c;

    public ManageApplicationResponse(int i) {
        super(i);
    }

    public byte[] getDynamicVector() {
        return q.c(this.c);
    }

    public ManageApplicationResponse(int i, Throwable th) {
        super(i, th);
    }

    public ManageApplicationResponse(int i, byte[] bArr) {
        super(i);
        this.c = q.c(bArr);
    }
}
