package o.f;

import android.util.Base64;
import com.esotericsoftware.asm.Opcodes;
import java.util.Arrays;
import java.util.Date;
import kotlin.text.Typography;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\g.smali */
public final class g extends i {
    private static int b = 0;
    private static int c = 1;
    private final d d;
    private final byte[] e;

    public g(e.d dVar, Date date, d dVar2, d dVar3, byte[] bArr) {
        super(dVar, date, dVar2);
        this.d = dVar3;
        this.e = Arrays.copyOf(bArr, bArr.length);
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = b;
        int i2 = (i & Opcodes.DSUB) + (i | Opcodes.DSUB);
        c = i2 % 128;
        int i3 = i2 % 2;
        o.i.f f = o.i.d.c().f();
        int i4 = b;
        int i5 = (i4 & Opcodes.LNEG) + (i4 | Opcodes.LNEG);
        c = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return f;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final String c() {
        int i = (b + 36) - 1;
        c = i % 128;
        int i2 = i % 2;
        String encodeToString = Base64.encodeToString(this.d.b().a(), 10);
        int i3 = (b + 26) - 1;
        c = i3 % 128;
        int i4 = i3 % 2;
        return encodeToString;
    }

    @Override // o.f.e
    public final String j() {
        int i = b;
        int i2 = (i & 29) + (i | 29);
        c = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? 'I' : 'E') {
            case 'I':
                obj.hashCode();
                throw null;
            default:
                byte[] bArr = this.e;
                switch (bArr != null) {
                    case true:
                        String encodeToString = Base64.encodeToString(bArr, 10);
                        int i3 = c + 57;
                        b = i3 % 128;
                        int i4 = i3 % 2;
                        return encodeToString;
                    default:
                        int i5 = ((i | 41) << 1) - (i ^ 41);
                        c = i5 % 128;
                        switch (i5 % 2 == 0 ? 'S' : 'a') {
                            case Opcodes.LADD /* 97 */:
                                return null;
                            default:
                                int i6 = 47 / 0;
                                return null;
                        }
                }
        }
    }

    @Override // o.f.e
    public final byte[] d() {
        int i = c + 17;
        b = i % 128;
        switch (i % 2 != 0 ? 'c' : 'E') {
            case Opcodes.DADD /* 99 */:
                int i2 = 63 / 0;
                return l().b().e();
            default:
                return l().b().e();
        }
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = c + Opcodes.LSHR;
        b = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? Typography.greater : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                byte[] e = l().b().e();
                int i2 = c;
                int i3 = (i2 ^ 55) + ((i2 & 55) << 1);
                b = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return e;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                l().b().e();
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = c + 35;
        b = i % 128;
        int i2 = i % 2;
        byte[] a = l().b().a();
        int i3 = (c + 78) - 1;
        b = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }
}
