package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r7.smali */
public interface r7 {
    public static final w A;
    public static final w B;
    public static final w a;
    public static final w b;
    public static final w c;
    public static final w d;
    public static final w e;
    public static final w f;
    public static final w g;
    public static final w h;
    public static final w i;
    public static final w j;
    public static final w k;
    public static final w l;
    public static final w m;
    public static final w n;

    /* renamed from: o, reason: collision with root package name */
    public static final w f28o;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.3.36.3");
        a = wVar;
        b = wVar.a("2.1");
        c = wVar.a("2.2");
        d = wVar.a("2.3");
        w a2 = wVar.a("3.1");
        e = a2;
        f = a2.a("2");
        g = a2.a("3");
        h = a2.a("4");
        w a3 = wVar.a("3.2");
        i = a3;
        j = a3.a("1");
        k = a3.a("2");
        w a4 = wVar.a("3.2.8");
        l = a4;
        w a5 = a4.a("1");
        m = a5;
        w a6 = a5.a("1");
        n = a6;
        f28o = a6.a("1");
        p = a6.a("2");
        q = a6.a("3");
        r = a6.a("4");
        s = a6.a("5");
        t = a6.a("6");
        u = a6.a("7");
        v = a6.a("8");
        w = a6.a("9");
        x = a6.a("10");
        y = a6.a("11");
        z = a6.a("12");
        A = a6.a("13");
        B = a6.a("14");
    }
}
