package o.cf;

import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\h.smali */
public final class h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final h a;
    private static int b;
    private static int[] c;
    private static final /* synthetic */ h[] e;
    private static int i;
    private final String d;

    static void e() {
        c = new int[]{-1302215047, 751198192, 1353217633, 1368130851, 723217196, 1211398051, 458539724, 2102251182, 385757823, -535784132, 786279686, -92381932, 1210237030, 2084342268, 1545840967, 1673984748, 590428296, 755371564};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = 116 - r6
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.cf.h.$$a
            int r8 = r8 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L17:
            r3 = r2
        L18:
            int r8 = r8 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.h.g(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{48, 67, 97, 27};
        $$b = 65;
    }

    private static /* synthetic */ h[] a() {
        int i2 = b + 3;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                h[] hVarArr = new h[0];
                hVarArr[1] = a;
                return hVarArr;
            default:
                return new h[]{a};
        }
    }

    public static h valueOf(String str) {
        int i2 = b + 83;
        i = i2 % 128;
        int i3 = i2 % 2;
        h hVar = (h) Enum.valueOf(h.class, str);
        int i4 = i + 83;
        b = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 90 / 0;
                return hVar;
            default:
                return hVar;
        }
    }

    public static h[] values() {
        int i2 = i + 61;
        b = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return (h[]) e.clone();
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        i = 1;
        e();
        Object[] objArr = new Object[1];
        f(new int[]{-1414478810, 1173011905, -1732117212, 1896880910, 1549566836, 1641654119, -600594431, 112850384}, (ViewConfiguration.getJumpTapTimeout() >> 16) + 15, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(new int[]{-1466757203, 1885327038, 1750872476, 650794464, 586582000, -80134964, 1669276746, -1976328864, -146692334, 412191994}, 17 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
        a = new h(intern, ((String) objArr2[0]).intern());
        e = a();
        int i2 = i + 63;
        b = i2 % 128;
        int i3 = i2 % 2;
    }

    private h(String str, String str2) {
        this.d = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        String str;
        int i2 = i + 93;
        int i3 = i2 % 128;
        b = i3;
        switch (i2 % 2 != 0 ? '?' : 'U') {
            case '?':
                str = this.d;
                int i4 = 24 / 0;
                break;
            default:
                str = this.d;
                break;
        }
        int i5 = i3 + 109;
        i = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public static h c(String str) {
        switch (str == null ? '@' : '%') {
            case '@':
                return null;
            default:
                h[] values = values();
                int length = values.length;
                int i2 = 0;
                while (i2 < length) {
                    h hVar = values[i2];
                    switch (hVar.d.equals(str) ? '\r' : 'X') {
                        case Opcodes.POP2 /* 88 */:
                            i2++;
                            int i3 = b + 75;
                            i = i3 % 128;
                            int i4 = i3 % 2;
                        default:
                            int i5 = b + 53;
                            i = i5 % 128;
                            int i6 = i5 % 2;
                            return hVar;
                    }
                }
                return null;
        }
    }

    private static void f(int[] iArr, int i2, Object[] objArr) {
        int length;
        int[] iArr2;
        int[] iArr3;
        int i3;
        int length2;
        int[] iArr4;
        int i4;
        o.a.g gVar = new o.a.g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr5 = c;
        float f = 0.0f;
        int i5 = -1667374059;
        int i6 = 16;
        int i7 = 1;
        int i8 = 0;
        if (iArr5 != null) {
            int i9 = $11 + 7;
            $10 = i9 % 128;
            switch (i9 % 2 != 0 ? (char) 3 : (char) 4) {
                case 3:
                    length2 = iArr5.length;
                    iArr4 = new int[length2];
                    i4 = 0;
                    break;
                default:
                    length2 = iArr5.length;
                    iArr4 = new int[length2];
                    i4 = 0;
                    break;
            }
            while (true) {
                switch (i4 < length2 ? '!' : '\'') {
                    case '!':
                        try {
                            Object[] objArr2 = new Object[i7];
                            objArr2[i8] = Integer.valueOf(iArr5[i4]);
                            Object obj = o.e.a.s.get(Integer.valueOf(i5));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(10 - (AudioTrack.getMinVolume() > f ? 1 : (AudioTrack.getMinVolume() == f ? 0 : -1)), (char) ((ViewConfiguration.getJumpTapTimeout() >> i6) + 8856), 324 - TextUtils.getOffsetAfter("", i8));
                                byte b2 = (byte) i8;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                g(b2, b3, (byte) (b3 - 1), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj);
                            }
                            iArr4[i4] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                            i4++;
                            f = 0.0f;
                            i5 = -1667374059;
                            i6 = 16;
                            i7 = 1;
                            i8 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        iArr5 = iArr4;
                        break;
                }
            }
        }
        int length3 = iArr5.length;
        int[] iArr6 = new int[length3];
        int[] iArr7 = c;
        if (iArr7 != null) {
            int i10 = $10 + 87;
            int i11 = i10 % 128;
            $11 = i11;
            if (i10 % 2 == 0) {
                length = iArr7.length;
                iArr2 = new int[length];
            } else {
                length = iArr7.length;
                iArr2 = new int[length];
            }
            int i12 = i11 + Opcodes.DDIV;
            $10 = i12 % 128;
            int i13 = i12 % 2;
            int i14 = 0;
            while (i14 < length) {
                try {
                    Object[] objArr4 = {Integer.valueOf(iArr7[i14])};
                    Object obj2 = o.e.a.s.get(-1667374059);
                    if (obj2 != null) {
                        iArr3 = iArr7;
                        i3 = length;
                    } else {
                        Class cls2 = (Class) o.e.a.c((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 10, (char) (8856 - (ViewConfiguration.getJumpTapTimeout() >> 16)), KeyEvent.keyCodeFromString("") + 324);
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        iArr3 = iArr7;
                        i3 = length;
                        Object[] objArr5 = new Object[1];
                        g(b4, b5, (byte) (b5 - 1), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                        o.e.a.s.put(-1667374059, obj2);
                    }
                    iArr2[i14] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    i14++;
                    iArr7 = iArr3;
                    length = i3;
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            iArr7 = iArr2;
        }
        System.arraycopy(iArr7, 0, iArr6, 0, length3);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr6);
            int i15 = 0;
            for (int i16 = 16; i15 < i16; i16 = 16) {
                int i17 = $11 + 67;
                $10 = i17 % 128;
                int i18 = i17 % 2;
                gVar.e ^= iArr6[i15];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c(10 - Process.getGidForName(""), (char) (ViewConfiguration.getPressedStateDuration() >> 16), (KeyEvent.getMaxKeyCode() >> 16) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                    i15++;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i19 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i19;
            gVar.c ^= iArr6[16];
            gVar.e ^= iArr6[17];
            int i20 = gVar.e;
            int i21 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            o.a.g.d(iArr6);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) o.e.a.c(View.resolveSizeAndState(0, 0, 0) + 12, (char) (55183 - ExpandableListView.getPackedPositionGroup(0L)), (ViewConfiguration.getTapTimeout() >> 16) + 515);
                    byte b6 = (byte) ($$b & 7);
                    byte b7 = (byte) (b6 - 1);
                    Object[] objArr8 = new Object[1];
                    g(b6, b7, (byte) (b7 - 1), objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i2);
    }
}
