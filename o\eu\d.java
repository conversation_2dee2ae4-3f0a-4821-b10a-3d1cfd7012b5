package o.eu;

import o.cr.c;
import o.ey.b;
import o.fg.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eu\d.smali */
public final class d extends b<a> {
    private static int d = 0;
    private static int b = 1;

    @Override // o.ey.e
    public final /* synthetic */ o.ct.b c() {
        int i = d;
        int i2 = (i & 69) + (i | 69);
        b = i2 % 128;
        int i3 = i2 % 2;
        c t = t();
        int i4 = (d + 56) - 1;
        b = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                int i5 = 15 / 0;
                return t;
            default:
                return t;
        }
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ey.a e() {
        int i = b;
        int i2 = ((i | 43) << 1) - (i ^ 43);
        d = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 23 : '0') {
            case 23:
                r();
                throw null;
            default:
                e r = r();
                int i3 = b;
                int i4 = (i3 & 25) + (i3 | 25);
                d = i4 % 128;
                switch (i4 % 2 != 0 ? '9' : '3') {
                    case '9':
                        throw null;
                    default:
                        return r;
                }
        }
    }

    public d(String str, String str2, boolean z) {
        super(str, str2, z);
    }

    private static e r() {
        e eVar = new e();
        int i = d;
        int i2 = ((i | 59) << 1) - (i ^ 59);
        b = i2 % 128;
        int i3 = i2 % 2;
        return eVar;
    }

    private static c t() {
        c cVar = new c();
        int i = d;
        int i2 = ((i | 15) << 1) - (i ^ 15);
        b = i2 % 128;
        int i3 = i2 % 2;
        return cVar;
    }
}
