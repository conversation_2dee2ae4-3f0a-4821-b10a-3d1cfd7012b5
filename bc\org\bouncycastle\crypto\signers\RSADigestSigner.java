package bc.org.bouncycastle.crypto.signers;

import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.CryptoException;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.Digest;
import bc.org.bouncycastle.crypto.Signer;
import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.b8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d2;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.r7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t0;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.y3;
import java.io.IOException;
import java.util.Hashtable;
import org.bouncycastle.asn1.ASN1Encoding;
import org.bouncycastle.pqc.crypto.sphincs.SPHINCSKeyParameters;
import org.bouncycastle.pqc.jcajce.spec.McElieceCCA2KeyGenParameterSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\signers\RSADigestSigner.smali */
public class RSADigestSigner implements Signer {
    private static final Hashtable e;
    private final t0 a;
    private final s0 b;
    private final Digest c;
    private boolean d;

    static {
        Hashtable hashtable = new Hashtable();
        e = hashtable;
        hashtable.put("RIPEMD128", r7.c);
        hashtable.put("RIPEMD160", r7.b);
        hashtable.put("RIPEMD256", r7.d);
        hashtable.put(McElieceCCA2KeyGenParameterSpec.SHA1, b8.j);
        hashtable.put(McElieceCCA2KeyGenParameterSpec.SHA224, q5.f);
        hashtable.put("SHA-256", q5.c);
        hashtable.put(McElieceCCA2KeyGenParameterSpec.SHA384, q5.d);
        hashtable.put("SHA-512", q5.e);
        hashtable.put("SHA-512/224", q5.g);
        hashtable.put(SPHINCSKeyParameters.SHA512_256, q5.h);
        hashtable.put("SHA3-224", q5.i);
        hashtable.put("SHA3-256", q5.j);
        hashtable.put("SHA3-384", q5.k);
        hashtable.put("SHA3-512", q5.l);
        hashtable.put("MD2", i6.I);
        hashtable.put("MD4", i6.J);
        hashtable.put("MD5", i6.K);
    }

    public RSADigestSigner(Digest digest) {
        this(digest, (w) e.get(digest.getAlgorithmName()));
    }

    private byte[] a(byte[] bArr) throws IOException {
        s0 s0Var = this.b;
        if (s0Var != null) {
            return new y3(s0Var, bArr).getEncoded(ASN1Encoding.DER);
        }
        try {
            y3.a(bArr);
            return bArr;
        } catch (IllegalArgumentException e2) {
            throw new IOException("malformed DigestInfo for NONEwithRSA hash: " + e2.getMessage());
        }
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public byte[] generateSignature() throws CryptoException, DataLengthException {
        if (!this.d) {
            throw new IllegalStateException("RSADigestSigner not initialised for signature generation.");
        }
        byte[] bArr = new byte[this.c.getDigestSize()];
        this.c.doFinal(bArr, 0);
        try {
            byte[] a = a(bArr);
            return this.a.a(a, 0, a.length);
        } catch (IOException e2) {
            throw new CryptoException("unable to encode signature: " + e2.getMessage(), e2);
        }
    }

    public String getAlgorithmName() {
        return this.c.getAlgorithmName() + "withRSA";
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public void init(boolean z, CipherParameters cipherParameters) {
        this.d = z;
        AsymmetricKeyParameter asymmetricKeyParameter = cipherParameters instanceof k6 ? (AsymmetricKeyParameter) ((k6) cipherParameters).a() : (AsymmetricKeyParameter) cipherParameters;
        if (z && !asymmetricKeyParameter.isPrivate()) {
            throw new IllegalArgumentException("signing requires private key");
        }
        if (!z && asymmetricKeyParameter.isPrivate()) {
            throw new IllegalArgumentException("verification requires public key");
        }
        reset();
        this.a.init(z, cipherParameters);
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public void reset() {
        this.c.reset();
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public void update(byte b) {
        this.c.update(b);
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public boolean verifySignature(byte[] bArr) {
        if (this.d) {
            throw new IllegalStateException("RSADigestSigner not initialised for verification");
        }
        int digestSize = this.c.getDigestSize();
        byte[] bArr2 = new byte[digestSize];
        this.c.doFinal(bArr2, 0);
        try {
            byte[] a = this.a.a(bArr, 0, bArr.length);
            byte[] a2 = a(bArr2);
            if (a.length == a2.length) {
                return Arrays.constantTimeAreEqual(a, a2);
            }
            if (a.length != a2.length - 2) {
                Arrays.constantTimeAreEqual(a2, a2);
                return false;
            }
            int length = (a.length - digestSize) - 2;
            int length2 = (a2.length - digestSize) - 2;
            a2[1] = (byte) (a2[1] - 2);
            a2[3] = (byte) (a2[3] - 2);
            int i = 0;
            for (int i2 = 0; i2 < digestSize; i2++) {
                i |= a[length + i2] ^ a2[length2 + i2];
            }
            for (int i3 = 0; i3 < length; i3++) {
                i |= a[i3] ^ a2[i3];
            }
            return i == 0;
        } catch (Exception e2) {
            return false;
        }
    }

    public RSADigestSigner(Digest digest, w wVar) {
        this.a = new h6(new s6());
        this.c = digest;
        if (wVar != null) {
            this.b = new s0(wVar, d2.x);
        } else {
            this.b = null;
        }
    }

    @Override // bc.org.bouncycastle.crypto.Signer
    public void update(byte[] bArr, int i, int i2) {
        this.c.update(bArr, i, i2);
    }
}
