package com.google.firebase.encoders.json;

import android.util.Base64;
import android.util.JsonWriter;
import com.google.firebase.encoders.EncodingException;
import com.google.firebase.encoders.FieldDescriptor;
import com.google.firebase.encoders.ObjectEncoder;
import com.google.firebase.encoders.ObjectEncoderContext;
import com.google.firebase.encoders.ValueEncoder;
import com.google.firebase.encoders.ValueEncoderContext;
import java.io.IOException;
import java.io.Writer;
import java.util.Collection;
import java.util.Date;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\encoders\json\JsonValueObjectEncoderContext.smali */
final class JsonValueObjectEncoderContext implements ObjectEncoderContext, ValueEncoderContext {
    private final ObjectEncoder<Object> fallbackEncoder;
    private final boolean ignoreNullValues;
    private final JsonWriter jsonWriter;
    private final Map<Class<?>, ObjectEncoder<?>> objectEncoders;
    private final Map<Class<?>, ValueEncoder<?>> valueEncoders;
    private JsonValueObjectEncoderContext childContext = null;
    private boolean active = true;

    JsonValueObjectEncoderContext(Writer writer, Map<Class<?>, ObjectEncoder<?>> objectEncoders, Map<Class<?>, ValueEncoder<?>> valueEncoders, ObjectEncoder<Object> fallbackEncoder, boolean ignoreNullValues) {
        this.jsonWriter = new JsonWriter(writer);
        this.objectEncoders = objectEncoders;
        this.valueEncoders = valueEncoders;
        this.fallbackEncoder = fallbackEncoder;
        this.ignoreNullValues = ignoreNullValues;
    }

    private JsonValueObjectEncoderContext(JsonValueObjectEncoderContext anotherContext) {
        this.jsonWriter = anotherContext.jsonWriter;
        this.objectEncoders = anotherContext.objectEncoders;
        this.valueEncoders = anotherContext.valueEncoders;
        this.fallbackEncoder = anotherContext.fallbackEncoder;
        this.ignoreNullValues = anotherContext.ignoreNullValues;
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public JsonValueObjectEncoderContext add(String name, Object o2) throws IOException {
        if (this.ignoreNullValues) {
            return internalAddIgnoreNullValues(name, o2);
        }
        return internalAdd(name, o2);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public JsonValueObjectEncoderContext add(String name, double value) throws IOException {
        maybeUnNest();
        this.jsonWriter.name(name);
        return add(value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public JsonValueObjectEncoderContext add(String name, int value) throws IOException {
        maybeUnNest();
        this.jsonWriter.name(name);
        return add(value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public JsonValueObjectEncoderContext add(String name, long value) throws IOException {
        maybeUnNest();
        this.jsonWriter.name(name);
        return add(value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public JsonValueObjectEncoderContext add(String name, boolean value) throws IOException {
        maybeUnNest();
        this.jsonWriter.name(name);
        return add(value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, Object obj) throws IOException {
        return add(field.getName(), obj);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, float value) throws IOException {
        return add(field.getName(), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, double value) throws IOException {
        return add(field.getName(), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, int value) throws IOException {
        return add(field.getName(), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, long value) throws IOException {
        return add(field.getName(), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext add(FieldDescriptor field, boolean value) throws IOException {
        return add(field.getName(), value);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext inline(Object value) throws IOException {
        return add(value, true);
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext nested(String name) throws IOException {
        maybeUnNest();
        this.childContext = new JsonValueObjectEncoderContext(this);
        this.jsonWriter.name(name);
        this.jsonWriter.beginObject();
        return this.childContext;
    }

    @Override // com.google.firebase.encoders.ObjectEncoderContext
    public ObjectEncoderContext nested(FieldDescriptor field) throws IOException {
        return nested(field.getName());
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(String value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(float value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(double value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(int value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(long value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(boolean value) throws IOException {
        maybeUnNest();
        this.jsonWriter.value(value);
        return this;
    }

    @Override // com.google.firebase.encoders.ValueEncoderContext
    public JsonValueObjectEncoderContext add(byte[] bytes) throws IOException {
        maybeUnNest();
        if (bytes == null) {
            this.jsonWriter.nullValue();
        } else {
            this.jsonWriter.value(Base64.encodeToString(bytes, 2));
        }
        return this;
    }

    JsonValueObjectEncoderContext add(Object o2, boolean inline) throws IOException {
        int i = 0;
        if (inline && cannotBeInline(o2)) {
            Object[] objArr = new Object[1];
            objArr[0] = o2 == null ? null : o2.getClass();
            throw new EncodingException(String.format("%s cannot be encoded inline", objArr));
        }
        if (o2 == null) {
            this.jsonWriter.nullValue();
            return this;
        }
        if (o2 instanceof Number) {
            this.jsonWriter.value((Number) o2);
            return this;
        }
        if (o2.getClass().isArray()) {
            if (o2 instanceof byte[]) {
                return add((byte[]) o2);
            }
            this.jsonWriter.beginArray();
            if (o2 instanceof int[]) {
                int[] iArr = (int[]) o2;
                int length = iArr.length;
                while (i < length) {
                    int item = iArr[i];
                    this.jsonWriter.value(item);
                    i++;
                }
            } else if (o2 instanceof long[]) {
                long[] jArr = (long[]) o2;
                int length2 = jArr.length;
                while (i < length2) {
                    long item2 = jArr[i];
                    add(item2);
                    i++;
                }
            } else if (o2 instanceof double[]) {
                double[] dArr = (double[]) o2;
                int length3 = dArr.length;
                while (i < length3) {
                    double item3 = dArr[i];
                    this.jsonWriter.value(item3);
                    i++;
                }
            } else if (o2 instanceof boolean[]) {
                boolean[] zArr = (boolean[]) o2;
                int length4 = zArr.length;
                while (i < length4) {
                    boolean item4 = zArr[i];
                    this.jsonWriter.value(item4);
                    i++;
                }
            } else if (o2 instanceof Number[]) {
                for (Number item5 : (Number[]) o2) {
                    add((Object) item5, false);
                }
            } else {
                for (Object item6 : (Object[]) o2) {
                    add(item6, false);
                }
            }
            this.jsonWriter.endArray();
            return this;
        }
        if (o2 instanceof Collection) {
            Collection collection = (Collection) o2;
            this.jsonWriter.beginArray();
            for (Object elem : collection) {
                add(elem, false);
            }
            this.jsonWriter.endArray();
            return this;
        }
        if (o2 instanceof Map) {
            Map<Object, Object> map = (Map) o2;
            this.jsonWriter.beginObject();
            for (Map.Entry<Object, Object> entry : map.entrySet()) {
                Object key = entry.getKey();
                try {
                    add((String) key, entry.getValue());
                } catch (ClassCastException ex) {
                    throw new EncodingException(String.format("Only String keys are currently supported in maps, got %s of type %s instead.", key, key.getClass()), ex);
                }
            }
            this.jsonWriter.endObject();
            return this;
        }
        Map<Object, Object> map2 = this.objectEncoders;
        ObjectEncoder<Object> objectEncoder = (ObjectEncoder) map2.get(o2.getClass());
        if (objectEncoder != null) {
            return doEncode(objectEncoder, o2, inline);
        }
        ValueEncoder<Object> valueEncoder = this.valueEncoders.get(o2.getClass());
        if (valueEncoder != null) {
            valueEncoder.encode(o2, this);
            return this;
        }
        if (o2 instanceof Enum) {
            add(((Enum) o2).name());
            return this;
        }
        return doEncode(this.fallbackEncoder, o2, inline);
    }

    JsonValueObjectEncoderContext doEncode(ObjectEncoder<Object> encoder, Object o2, boolean inline) throws IOException {
        if (!inline) {
            this.jsonWriter.beginObject();
        }
        encoder.encode(o2, this);
        if (!inline) {
            this.jsonWriter.endObject();
        }
        return this;
    }

    private boolean cannotBeInline(Object value) {
        return value == null || value.getClass().isArray() || (value instanceof Collection) || (value instanceof Date) || (value instanceof Enum) || (value instanceof Number);
    }

    void close() throws IOException {
        maybeUnNest();
        this.jsonWriter.flush();
    }

    private void maybeUnNest() throws IOException {
        if (!this.active) {
            throw new IllegalStateException("Parent context used since this context was created. Cannot use this context anymore.");
        }
        JsonValueObjectEncoderContext jsonValueObjectEncoderContext = this.childContext;
        if (jsonValueObjectEncoderContext != null) {
            jsonValueObjectEncoderContext.maybeUnNest();
            this.childContext.active = false;
            this.childContext = null;
            this.jsonWriter.endObject();
        }
    }

    private JsonValueObjectEncoderContext internalAdd(String name, Object o2) throws IOException, EncodingException {
        maybeUnNest();
        this.jsonWriter.name(name);
        if (o2 == null) {
            this.jsonWriter.nullValue();
            return this;
        }
        return add(o2, false);
    }

    private JsonValueObjectEncoderContext internalAddIgnoreNullValues(String name, Object o2) throws IOException, EncodingException {
        if (o2 == null) {
            return this;
        }
        maybeUnNest();
        this.jsonWriter.name(name);
        return add(o2, false);
    }
}
