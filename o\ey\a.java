package o.ey;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import java.util.ArrayList;
import kotlin.text.Typography;
import o.a.f;
import o.a.l;
import o.ey.e;
import o.fc.c;
import o.fc.d;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\a.smali */
public abstract class a<T extends o.fc.d, U extends e<T>> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static int d;
    private static int f;
    private static int g;
    private static short[] h;
    private static byte[] i;
    private static char[] j;
    private final String b;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        e();
        TextUtils.getOffsetBefore("", 0);
        SystemClock.currentThreadTimeMillis();
        Color.red(0);
        KeyEvent.getMaxKeyCode();
        View.MeasureSpec.getSize(0);
        View.MeasureSpec.getSize(0);
        KeyEvent.normalizeMetaState(0);
        Color.blue(0);
        TextUtils.lastIndexOf("", '0', 0, 0);
        PointF.length(0.0f, 0.0f);
        int i2 = f + 81;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        i = new byte[]{-92, -89, -39, -77, -89, -13, -63, -42, -59, -46, -65, PSSSigner.TRAILER_IMPLICIT, -87, -76, Tnaf.POW_2_WIDTH, 115, -96, -67, -86, -21, 79, -82, -72, -90, -87, -114, -89, -96, -83, -28, 72, -43, -69, -20, 77, -89, -86, -71, -75, -39, UtilitiesSDKConstants.SRP_LABEL_MAC, 17, 117, -95, -72, -88, -10, -8, -26, 85, -113, -18, -8, -26, -23, -50, -25, -32, -19, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -120, 21, -5, 44, -115, -25, -22, -7, -11, 25, -14, 81, -67, -4, -2, -20, -52, -23, -26, 74, 76, 122, -87, 3, 98, 76, 122, 125, 66, 123, 116, 97, -72, 28, 105, 79, -96, 1, 123, 126, 77, 73, 109, 70, -91, 7, 76, 125, 118, 120, 114, 78, 102, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 75, -102, 54, 116, 115, 67, 13, 58, 89, 38, -112, -112, -112, -112, -112, -112, -112};
        c = 909053639;
        a = 806697882;
        d = 359167619;
        j = new char[]{50844, 50794, 50812, 50798, 50797, 50789, 50795, 50789, 50799, 50794, 50787, 50790, 50795, 50943, 50857, 50853, 50858, 50860, 50855, 50851, 50843, 50838, 50851, 50860, 50802, 50699, 50688, 50935, 50854, 50857, 50858, 50859, 50853, 50826, 50826, 50851, 50876, 50851, 50824, 50817, 50850, 50853, 50824, 50823, 50876, 50852, 50852, 50878, 50848, 50853, 50821, 50827, 50849, 50877, 50876, 50943, 50855, 50873, 50875, 50823, 50817, 50850, 50853, 50824, 50823, 50876, 50852, 50852, 50878, 50848, 50853, 50821, 50831, 50859, 50855, 50859, 50854, 50879, 50854, 50917, 50837, 50848, 50878, 50852, 50852, 50876, 50834, 50837, 50850, 50835, 50836, 50851, 50876, 50855, 50856, 50854, 50852, 50854, 50863, 50833, 50854, 50848, 50856, 50859, 50855, 50876, 50850, 50854};
    }

    static void init$0() {
        $$a = new byte[]{20, -38, -101, -62};
        $$b = Opcodes.DNEG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(byte r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.ey.a.$$a
            int r8 = r8 + 66
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L15:
            r3 = r2
        L16:
            r6 = r9
            r9 = r8
            r8 = r6
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r8 = -r8
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.a.q(byte, short, int, java.lang.Object[]):void");
    }

    protected abstract T b(c cVar, short s);

    protected abstract d d();

    protected abstract U e(String str, String str2, boolean z);

    public a() {
        Object[] objArr = new Object[1];
        l((byte) (ViewConfiguration.getJumpTapTimeout() >> 16), (-591886355) + (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) (65530 - AndroidCharacter.getMirror('0')), ExpandableListView.getPackedPositionChild(0L) - 80, View.MeasureSpec.getSize(0) - 104477847, objArr);
        this.b = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) TextUtils.indexOf("", "", 0, 0), (-591886350) - (ViewConfiguration.getTouchSlop() >> 8), (short) (TextUtils.lastIndexOf("", '0') - 79), (-82) - View.MeasureSpec.getSize(0), TextUtils.lastIndexOf("", '0', 0, 0) - 104477856, objArr2);
        this.e = ((String) objArr2[0]).intern();
    }

    /* renamed from: o.ey.a$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\a$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        static final /* synthetic */ int[] a;
        private static int c;
        private static int e;

        static {
            c = 0;
            e = 1;
            int[] iArr = new int[d.values().length];
            a = iArr;
            try {
                iArr[d.a.ordinal()] = 1;
                int i = c;
                int i2 = ((i | 5) << 1) - (i ^ 5);
                e = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[d.c.ordinal()] = 2;
                int i3 = e + 89;
                c = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[d.b.ordinal()] = 3;
                int i5 = (e + 96) - 1;
                c = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[d.e.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                a[d.d.ordinal()] = 5;
                int i7 = (e + 34) - 1;
                c = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e6) {
            }
            try {
                a[d.i.ordinal()] = 6;
                int i9 = e + 85;
                c = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e7) {
            }
        }
    }

    public static e a(o.eg.b bVar) throws o.eg.d {
        a eVar;
        if (bVar == null) {
            Object[] objArr = new Object[1];
            l((byte) Color.argb(0, 0, 0, 0), View.resolveSizeAndState(0, 0, 0) - 591886346, (short) ((-49) - View.combineMeasuredStates(0, 0)), (-49) - (KeyEvent.getMaxKeyCode() >> 16), (-104477852) + TextUtils.getOffsetBefore("", 0), objArr);
            throw new o.eg.d(((String) objArr[0]).intern());
        }
        try {
            Object[] objArr2 = new Object[1];
            p("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{0, 13, 61, 0}, true, objArr2);
            d a2 = d.a(bVar.r(((String) objArr2[0]).intern()));
            int i2 = f + 89;
            g = i2 % 128;
            int i3 = i2 % 2;
            switch (AnonymousClass2.a[a2.ordinal()]) {
                case 1:
                    eVar = new o.eu.e();
                    break;
                case 2:
                    eVar = new o.ew.a();
                    break;
                case 3:
                    eVar = new o.ez.a();
                    break;
                case 4:
                    eVar = new o.fa.c();
                    break;
                case 5:
                    eVar = new o.ev.a();
                    int i4 = g + 13;
                    f = i4 % 128;
                    int i5 = i4 % 2;
                    break;
                case 6:
                    eVar = new o.fb.b();
                    break;
                default:
                    Object[] objArr3 = new Object[1];
                    l((byte) (ViewConfiguration.getEdgeSlop() >> 16), (-591886275) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 26), (Process.myPid() >> 22) - 53, (ViewConfiguration.getScrollBarSize() >> 8) - 104477845, objArr3);
                    throw new o.eg.d(((String) objArr3[0]).intern());
            }
            e b = eVar.b(bVar);
            new o.fj.d();
            b.b(o.fj.d.b(bVar));
            return b;
        } catch (IllegalArgumentException e) {
            Object[] objArr4 = new Object[1];
            l((byte) (ViewConfiguration.getWindowTouchSlop() >> 8), (-591886309) - (ViewConfiguration.getTapTimeout() >> 16), (short) ((-114) - ExpandableListView.getPackedPositionChild(0L)), (-53) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), View.MeasureSpec.makeMeasureSpec(0, 0) - 104477857, objArr4);
            throw new o.eg.d(((String) objArr4[0]).intern());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0104, code lost:
    
        r4 = new java.lang.Object[1];
        p("\u0000\u0000\u0000\u0000", new int[]{23, 4, 87, 0}, false, r4);
        r0.d(((java.lang.String) r4[0]).intern(), r3);
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public o.eg.b e(U r18) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 332
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ey.a.e(o.ey.e):o.eg.b");
    }

    public U b(o.eg.b bVar) throws o.eg.d {
        int i2 = g + 65;
        f = i2 % 128;
        int i3 = i2 % 2;
        int i4 = 0;
        Object[] objArr = new Object[1];
        l((byte) (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.resolveSize(0, 0) - 591886243, (short) (TextUtils.getOffsetAfter("", 0) + 37), (-78) - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 104477862, objArr);
        String r = bVar.r(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        l((byte) Color.red(0), (-591886237) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (short) (72 - (ViewConfiguration.getTapTimeout() >> 16)), (-81) - TextUtils.getOffsetBefore("", 0), View.resolveSize(0, 0) - 104477863, objArr2);
        String r2 = bVar.r(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        p("\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000", new int[]{13, 10, 0, 0}, true, objArr3);
        U e = e(r, r2, bVar.g(((String) objArr3[0]).intern()).booleanValue());
        Object[] objArr4 = new Object[1];
        p("\u0000\u0000\u0000\u0000", new int[]{23, 4, 87, 0}, false, objArr4);
        if (bVar.b(((String) objArr4[0]).intern())) {
            Object[] objArr5 = new Object[1];
            p("\u0000\u0000\u0000\u0000", new int[]{23, 4, 87, 0}, false, objArr5);
            o.eg.e s = bVar.s(((String) objArr5[0]).intern());
            ArrayList arrayList = new ArrayList();
            while (true) {
                switch (i4 < s.d() ? 'Q' : (char) 24) {
                    case 24:
                        e.c(arrayList);
                        break;
                    default:
                        int i5 = g + 75;
                        f = i5 % 128;
                        if (i5 % 2 != 0) {
                        }
                        arrayList.add(e(s.b(i4)));
                        i4++;
                        int i6 = f + 29;
                        g = i6 % 128;
                        int i7 = i6 % 2;
                }
            }
        }
        return e;
    }

    public o.eg.b e(T t) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        l((byte) (ViewConfiguration.getScrollBarFadeDuration() >> 16), Color.alpha(0) - 591886355, (short) (AndroidCharacter.getMirror('0') - 'f'), (ViewConfiguration.getEdgeSlop() >> 16) - 81, (-104477847) + (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
        bVar.d(((String) objArr[0]).intern(), t.b().c());
        Object[] objArr2 = new Object[1];
        l((byte) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getTapTimeout() >> 16) - 591886350, (short) (((Process.getThreadPriority(0) + 20) >> 6) - 80), (-82) - Gravity.getAbsoluteGravity(0, 0), (-104477857) - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        bVar.d(((String) objArr2[0]).intern(), (int) t.c());
        int i2 = f + Opcodes.DNEG;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? '\\' : Typography.amp) {
            case Opcodes.DUP2 /* 92 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bVar;
        }
    }

    public T e(o.eg.b bVar) throws o.eg.d {
        int i2 = f + Opcodes.DDIV;
        g = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case false:
                if (bVar == null) {
                    Object[] objArr = new Object[1];
                    p("\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001", new int[]{27, 28, 0, 0}, true, objArr);
                    throw new o.eg.d(((String) objArr[0]).intern());
                }
                Object[] objArr2 = new Object[1];
                l((byte) (TextUtils.lastIndexOf("", '0') + 1), Drawable.resolveOpacity(0, 0) - 591886350, (short) (Color.rgb(0, 0, 0) + 16777136), (-82) - (ViewConfiguration.getKeyRepeatTimeout() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) - 104477857, objArr2);
                short shortValue = bVar.k(((String) objArr2[0]).intern()).shortValue();
                try {
                    Object[] objArr3 = new Object[1];
                    l((byte) ((-1) - TextUtils.lastIndexOf("", '0')), (Process.myPid() >> 22) - 591886355, (short) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) - 54), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 81, View.combineMeasuredStates(0, 0) - 104477847, objArr3);
                    c a2 = c.a(bVar.r(((String) objArr3[0]).intern()));
                    int i3 = g + Opcodes.LSUB;
                    f = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case true:
                            return b(a2, shortValue);
                        default:
                            b(a2, shortValue);
                            throw null;
                    }
                } catch (IllegalArgumentException e) {
                    Object[] objArr4 = new Object[1];
                    p("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{55, 24, 0, 0}, true, objArr4);
                    throw new o.eg.d(((String) objArr4[0]).intern());
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ey\a$d.smali */
    public static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final d a;
        public static final d b;
        public static final d c;
        public static final d d;
        public static final d e;
        private static char f;
        private static final /* synthetic */ d[] g;
        public static final d i;
        private static int[] j;
        private static char k;
        private static int l;
        private static char m;
        private static char n;

        /* renamed from: o, reason: collision with root package name */
        private static int f82o;
        private final String h;

        static void c() {
            j = new int[]{-1279584891, 1437474523, 769052050, -1947942606, -1049777484, -305060325, 339104123, -1824429764, -1890720536, 871285702, 2114380748, 921060641, -825372570, -1721301805, -1788813199, -839182343, 1768668568, -632703174};
            n = (char) 6124;
            m = (char) 29203;
            k = (char) 24193;
            f = (char) 2393;
        }

        static void init$0() {
            $$a = new byte[]{122, -6, -127, 6};
            $$b = 223;
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
        /* JADX WARN: Type inference failed for: r7v2, types: [int] */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void r(short r6, short r7, int r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 3
                int r7 = 4 - r7
                int r6 = r6 + 115
                int r8 = r8 * 3
                int r8 = r8 + 1
                byte[] r0 = o.ey.a.d.$$a
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L14
                r3 = r7
                r5 = r2
                goto L26
            L14:
                r3 = r2
            L15:
                byte r4 = (byte) r6
                int r5 = r3 + 1
                r1[r3] = r4
                if (r5 != r8) goto L24
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L24:
                r3 = r0[r7]
            L26:
                int r7 = r7 + 1
                int r3 = -r3
                int r6 = r6 + r3
                r3 = r5
                goto L15
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ey.a.d.r(short, short, int, java.lang.Object[]):void");
        }

        private static /* synthetic */ d[] b() {
            d[] dVarArr;
            int i2 = l + Opcodes.DREM;
            int i3 = i2 % 128;
            f82o = i3;
            switch (i2 % 2 == 0) {
                case true:
                    dVarArr = new d[]{a, c, b, e, d, i};
                    break;
                default:
                    dVarArr = new d[Opcodes.LMUL];
                    dVarArr[0] = a;
                    dVarArr[0] = c;
                    dVarArr[3] = b;
                    dVarArr[5] = e;
                    dVarArr[2] = d;
                    dVarArr[5] = i;
                    break;
            }
            int i4 = i3 + 83;
            l = i4 % 128;
            switch (i4 % 2 == 0 ? '=' : '\t') {
                case '\t':
                    return dVarArr;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public static d valueOf(String str) {
            int i2 = f82o + 81;
            l = i2 % 128;
            boolean z = i2 % 2 != 0;
            d dVar = (d) Enum.valueOf(d.class, str);
            switch (z) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    int i3 = l + 59;
                    f82o = i3 % 128;
                    int i4 = i3 % 2;
                    return dVar;
            }
        }

        public static d[] values() {
            int i2 = l + 61;
            f82o = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 11 : 'J') {
                case 11:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return (d[]) g.clone();
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f82o = 0;
            l = 1;
            c();
            Object[] objArr = new Object[1];
            p(new int[]{1849993348, 735906227, -827211416, 2038307001, 1021833339, -1975467072}, 10 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            q("燎홈舳拓馓먐⇰\ue759풱\ue13d", (ViewConfiguration.getScrollBarSize() >> 8) + 10, objArr2);
            a = new d(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            p(new int[]{-1238892593, -1009378490}, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 5, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            q("嶜䣙悔痑", 4 - KeyEvent.keyCodeFromString(""), objArr4);
            c = new d(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            p(new int[]{1751207164, -1791234561}, TextUtils.indexOf("", "", 0) + 4, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            p(new int[]{-1304406838, 54640094}, TextUtils.getTrimmedLength("") + 4, objArr6);
            b = new d(intern3, 2, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            q("켭娜훆㒌", 4 - (KeyEvent.getMaxKeyCode() >> 16), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            p(new int[]{1022558700, 244859482}, Color.rgb(0, 0, 0) + 16777220, objArr8);
            e = new d(intern4, 3, ((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            q("䖍\uef5a弪魒", 3 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr9);
            String intern5 = ((String) objArr9[0]).intern();
            Object[] objArr10 = new Object[1];
            q("㸈䮭弪魒", TextUtils.indexOf("", "", 0) + 3, objArr10);
            d = new d(intern5, 4, ((String) objArr10[0]).intern());
            Object[] objArr11 = new Object[1];
            p(new int[]{-1155158388, 1780686631}, 4 - Gravity.getAbsoluteGravity(0, 0), objArr11);
            String intern6 = ((String) objArr11[0]).intern();
            Object[] objArr12 = new Object[1];
            p(new int[]{2079919469, 1160281108}, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 4, objArr12);
            i = new d(intern6, 5, ((String) objArr12[0]).intern());
            g = b();
            int i2 = f82o + 75;
            l = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    return;
                default:
                    int i3 = 91 / 0;
                    return;
            }
        }

        private d(String str, int i2, String str2) {
            this.h = str2;
        }

        public final String a() {
            int i2 = l;
            int i3 = i2 + Opcodes.LSHR;
            f82o = i3 % 128;
            int i4 = i3 % 2;
            String str = this.h;
            int i5 = i2 + Opcodes.LSHR;
            f82o = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        public static o.ey.a.d a(java.lang.String r9) {
            /*
                int r0 = o.ey.a.d.l
                r1 = 13
                int r0 = r0 + r1
                int r2 = r0 % 128
                o.ey.a.d.f82o = r2
                int r0 = r0 % 2
                if (r0 == 0) goto L11
                r0 = 89
                goto L13
            L11:
                r0 = 29
            L13:
                r2 = 1
                r3 = 0
                switch(r0) {
                    case 89: goto L1f;
                    default: goto L18;
                }
            L18:
                o.ey.a$d[] r0 = values()
                int r4 = r0.length
                r5 = r3
                goto L25
            L1f:
                o.ey.a$d[] r0 = values()
                int r4 = r0.length
                r5 = r2
            L25:
                if (r5 >= r4) goto L29
                r6 = r1
                goto L2b
            L29:
                r6 = 95
            L2b:
                switch(r6) {
                    case 95: goto L3b;
                    default: goto L2e;
                }
            L2e:
                int r6 = o.ey.a.d.f82o
                int r6 = r6 + 73
                int r7 = r6 % 128
                o.ey.a.d.l = r7
                int r6 = r6 % 2
                if (r6 != 0) goto L73
                goto L70
            L3b:
                java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
                java.lang.StringBuilder r1 = new java.lang.StringBuilder
                r1.<init>()
                r4 = 22
                int[] r4 = new int[r4]
                r4 = {x00b4: FILL_ARRAY_DATA , data: [1103130872, 698493102, -145822374, -452231147, 1703367686, -627324704, 1431435587, -672151580, 938878774, 926883363, -465671643, 3568002, 789110877, 747187325, -1875265161, -1915811508, 1433773770, 715945768, -1017751233, 802379275, 1543736676, -517910349} // fill-array
                long r5 = android.widget.ExpandableListView.getPackedPositionForChild(r3, r3)
                r7 = 0
                int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
                int r5 = r5 + 44
                java.lang.Object[] r2 = new java.lang.Object[r2]
                p(r4, r5, r2)
                r2 = r2[r3]
                java.lang.String r2 = (java.lang.String) r2
                java.lang.String r2 = r2.intern()
                java.lang.StringBuilder r1 = r1.append(r2)
                java.lang.StringBuilder r9 = r1.append(r9)
                java.lang.String r9 = r9.toString()
                r0.<init>(r9)
                throw r0
            L70:
                r6 = 71
                goto L75
            L73:
                r6 = 72
            L75:
                switch(r6) {
                    case 71: goto L83;
                    default: goto L78;
                }
            L78:
                r6 = r0[r5]
                java.lang.String r7 = r6.h
                boolean r7 = r9.equals(r7)
                if (r7 == 0) goto L94
            L82:
                goto L93
            L83:
                r6 = r0[r5]
                java.lang.String r7 = r6.h
                boolean r7 = r9.equals(r7)
                r8 = 27
                int r8 = r8 / r3
                if (r7 == 0) goto L94
                goto L82
            L91:
                r9 = move-exception
                throw r9
            L93:
                return r6
            L94:
                int r5 = r5 + 1
                int r6 = o.ey.a.d.f82o
                int r6 = r6 + 3
                int r7 = r6 % 128
                o.ey.a.d.l = r7
                int r6 = r6 % 2
                goto L25
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ey.a.d.a(java.lang.String):o.ey.a$d");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void p(int[] r23, int r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 982
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ey.a.d.p(int[], int, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void q(java.lang.String r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 608
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ey.a.d.q(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    private static void l(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        int i5;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(c)};
            Object obj = o.e.a.s.get(-2120899312);
            int i6 = -1;
            if (obj == null) {
                Class cls = (Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 10, (char) TextUtils.indexOf("", ""), 'q' - AndroidCharacter.getMirror('0'));
                byte b2 = (byte) 0;
                Object[] objArr3 = new Object[1];
                q(b2, (byte) (b2 | 42), (byte) (-1), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            switch (intValue == -1) {
                case true:
                    z = true;
                    break;
                default:
                    z = false;
                    break;
            }
            switch (z ? 'S' : Typography.quote) {
                case Opcodes.AASTORE /* 83 */:
                    int i7 = $11 + 25;
                    $10 = i7 % 128;
                    if (i7 % 2 != 0) {
                        throw null;
                    }
                    byte[] bArr = i;
                    if (bArr != null) {
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i8 = 0;
                        while (true) {
                            switch (i8 < length) {
                                case false:
                                    bArr = bArr2;
                                    break;
                                default:
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(bArr[i8])};
                                        Object obj2 = o.e.a.s.get(494867332);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(Color.rgb(0, 0, 0) + 16777235, (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 16424), 150 - (ViewConfiguration.getMaximumFlingVelocity() >> 16));
                                            byte b3 = (byte) 0;
                                            byte b4 = (byte) i6;
                                            Object[] objArr5 = new Object[1];
                                            q(b3, (byte) (b3 | 44), b4, objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                            o.e.a.s.put(494867332, obj2);
                                        }
                                        bArr2[i8] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                        i8++;
                                        i6 = -1;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                            }
                        }
                    }
                    if (bArr == null) {
                        intValue = (short) (((short) (h[i2 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                        break;
                    } else {
                        byte[] bArr3 = i;
                        try {
                            Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(d)};
                            Object obj3 = o.e.a.s.get(-2120899312);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(TextUtils.indexOf("", "", 0) + 11, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 65);
                                byte b5 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                q(b5, (byte) (b5 | 42), (byte) (-1), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-2120899312, obj3);
                            }
                            intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
            }
            if (intValue > 0) {
                int i9 = $11 + Opcodes.DNEG;
                int i10 = i9 % 128;
                $10 = i10;
                int i11 = i9 % 2;
                int i12 = ((i2 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                switch (z ? '*' : (char) 29) {
                    case '*':
                        int i13 = i10 + 109;
                        $11 = i13 % 128;
                        switch (i13 % 2 == 0) {
                            case false:
                                i5 = 1;
                                break;
                            default:
                                i5 = 1;
                                break;
                        }
                    default:
                        i5 = 0;
                        break;
                }
                fVar.d = i12 + i5;
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(a), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c(11 - (Process.myPid() >> 22), (char) (Process.myTid() >> 22), 602 - ExpandableListView.getPackedPositionChild(0L))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = i;
                    if (bArr4 != null) {
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        for (int i14 = 0; i14 < length2; i14++) {
                            bArr5[i14] = (byte) (bArr4[i14] ^ (-5810760824076169584L));
                        }
                        bArr4 = bArr5;
                    }
                    boolean z2 = bArr4 != null;
                    fVar.c = 1;
                    while (true) {
                        switch (fVar.c < intValue) {
                            case false:
                                break;
                            default:
                                if (z2) {
                                    byte[] bArr6 = i;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                                } else {
                                    short[] sArr = h;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                        }
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    private static void p(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        String str2 = str;
        int i2 = $10 + 57;
        $11 = i2 % 128;
        int i3 = i2 % 2;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i4 = 0;
        int i5 = iArr[0];
        int i6 = 1;
        int i7 = iArr[1];
        int i8 = iArr[2];
        int i9 = iArr[3];
        char[] cArr2 = j;
        int i10 = -1;
        int i11 = 16;
        if (cArr2 != null) {
            int i12 = $10 + 5;
            int i13 = i12 % 128;
            $11 = i13;
            int i14 = i12 % 2;
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i15 = i13 + 83;
            $10 = i15 % 128;
            int i16 = i15 % 2;
            int i17 = 0;
            while (i17 < length) {
                try {
                    Object[] objArr2 = new Object[i6];
                    objArr2[i4] = Integer.valueOf(cArr2[i17]);
                    Object obj = o.e.a.s.get(1951085128);
                    if (obj != null) {
                        cArr = cArr2;
                    } else {
                        Class cls = (Class) o.e.a.c(12 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (char) (ViewConfiguration.getLongPressTimeout() >> i11), 44 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)));
                        byte b = (byte) i4;
                        cArr = cArr2;
                        Object[] objArr3 = new Object[1];
                        q(b, (byte) (b | 54), (byte) i10, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(1951085128, obj);
                    }
                    cArr3[i17] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i17++;
                    cArr2 = cArr;
                    i4 = 0;
                    i6 = 1;
                    i10 = -1;
                    i11 = 16;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        char[] cArr4 = new char[i7];
        System.arraycopy(cArr2, i5, cArr4, 0, i7);
        switch (bArr2 != null ? (char) 5 : (char) 14) {
            case 5:
                char[] cArr5 = new char[i7];
                lVar.d = 0;
                char c2 = 0;
                while (lVar.d < i7) {
                    switch (bArr2[lVar.d] != 1) {
                        case true:
                            int i18 = lVar.d;
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj2 = o.e.a.s.get(804049217);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) View.MeasureSpec.getSize(0), 206 - TextUtils.indexOf((CharSequence) "", '0'));
                                    byte b2 = (byte) 0;
                                    Object[] objArr5 = new Object[1];
                                    q(b2, (byte) (b2 | 56), (byte) (-1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj2);
                                }
                                cArr5[i18] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        default:
                            int i19 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj3 = o.e.a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(Drawable.resolveOpacity(0, 0) + 11, (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 449);
                                    byte b3 = (byte) 0;
                                    Object[] objArr7 = new Object[1];
                                    q(b3, (byte) (b3 | 53), (byte) (-1), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj3);
                                }
                                cArr5[i19] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                break;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                    }
                    c2 = cArr5[lVar.d];
                    try {
                        Object[] objArr8 = {lVar, lVar};
                        Object obj4 = o.e.a.s.get(-2112603350);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c((ViewConfiguration.getPressedStateDuration() >> 16) + 11, (char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), 259 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                            byte b4 = (byte) 0;
                            byte b5 = b4;
                            Object[] objArr9 = new Object[1];
                            q(b4, b5, (byte) (b5 - 1), objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(-2112603350, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                cArr4 = cArr5;
                break;
        }
        if (i9 > 0) {
            int i20 = $11 + 79;
            $10 = i20 % 128;
            switch (i20 % 2 != 0 ? (char) 3 : (char) 16) {
                case 16:
                    char[] cArr6 = new char[i7];
                    System.arraycopy(cArr4, 0, cArr6, 0, i7);
                    int i21 = i7 - i9;
                    System.arraycopy(cArr6, 0, cArr4, i21, i9);
                    System.arraycopy(cArr6, i9, cArr4, 0, i21);
                    break;
                default:
                    char[] cArr7 = new char[i7];
                    System.arraycopy(cArr4, 1, cArr7, 1, i7);
                    System.arraycopy(cArr7, 1, cArr4, i7 >>> i9, i9);
                    System.arraycopy(cArr7, i9, cArr4, 0, i7 - i9);
                    break;
            }
        }
        switch (z ? ':' : 'Z') {
            case 'Z':
                break;
            default:
                char[] cArr8 = new char[i7];
                int i22 = 0;
                while (true) {
                    lVar.d = i22;
                    if (lVar.d >= i7) {
                        cArr4 = cArr8;
                        break;
                    } else {
                        cArr8[lVar.d] = cArr4[(i7 - lVar.d) - 1];
                        i22 = lVar.d + 1;
                    }
                }
        }
        if (i8 > 0) {
            int i23 = $11 + 71;
            $10 = i23 % 128;
            int i24 = i23 % 2 != 0 ? 0 : 0;
            while (true) {
                lVar.d = i24;
                if (lVar.d < i7) {
                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                    i24 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr4);
    }
}
