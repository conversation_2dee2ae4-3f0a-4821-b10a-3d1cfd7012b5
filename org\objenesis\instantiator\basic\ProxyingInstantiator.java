package org.objenesis.instantiator.basic;

import org.objenesis.instantiator.annotations.Instantiator;
import org.objenesis.instantiator.annotations.Typology;

@Instantiator(Typology.STANDARD)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\objenesis\instantiator\basic\ProxyingInstantiator.smali */
public class ProxyingInstantiator<T> extends DelegatingToExoticInstantiator<T> {
    public ProxyingInstantiator(Class<T> type) {
        super("org.objenesis.instantiator.exotic.ProxyingInstantiator", type);
    }
}
