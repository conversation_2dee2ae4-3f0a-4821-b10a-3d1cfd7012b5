package o.ek;

import android.graphics.Color;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.eo.h;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\a.smali */
public final class a {
    private static int a = 0;
    private static int b = 1;
    private d c;
    private final String d;
    private h e;

    a(String str, h hVar, d dVar) {
        this.d = str;
        this.e = hVar;
        this.c = dVar;
    }

    public final String d() {
        int i = b;
        int i2 = (i ^ 67) + ((i & 67) << 1);
        int i3 = i2 % 128;
        a = i3;
        int i4 = i2 % 2;
        String str = this.d;
        int i5 = i3 + 31;
        b = i5 % 128;
        switch (i5 % 2 == 0 ? 'M' : ')') {
            case 'M':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final h e() {
        int i = a;
        int i2 = (i & 39) + (i | 39);
        b = i2 % 128;
        int i3 = i2 % 2;
        h hVar = this.e;
        int i4 = i + 69;
        b = i4 % 128;
        int i5 = i4 % 2;
        return hVar;
    }

    public final void d(h hVar) {
        int i = b;
        int i2 = (i ^ Opcodes.LSHL) + ((i & Opcodes.LSHL) << 1);
        int i3 = i2 % 128;
        a = i3;
        char c = i2 % 2 != 0 ? '!' : 'J';
        this.e = hVar;
        switch (c) {
            case '!':
                throw null;
            default:
                int i4 = (i3 + 2) - 1;
                b = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return;
                    default:
                        int i5 = 6 / 0;
                        return;
                }
        }
    }

    public final d a() {
        int i = a;
        int i2 = (i ^ 109) + ((i & 109) << 1);
        b = i2 % 128;
        int i3 = i2 % 2;
        d dVar = this.c;
        int i4 = ((i | 65) << 1) - (i ^ 65);
        b = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    public final void c(d dVar) {
        int i = b;
        int i2 = i + 9;
        a = i2 % 128;
        int i3 = i2 % 2;
        this.c = dVar;
        int i4 = i + 47;
        a = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* renamed from: o.ek.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\a$a.smali */
    static final class C0041a {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int b;
        private static int c;
        private static long e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            c = 1;
            c();
            TypedValue.complexToFloat(0);
            AudioTrack.getMinVolume();
            ViewConfiguration.getTouchSlop();
            Color.green(0);
            int i = b + 67;
            c = i % 128;
            switch (i % 2 == 0 ? (char) 26 : '1') {
                case '1':
                    break;
                default:
                    int i2 = 41 / 0;
                    break;
            }
        }

        static void c() {
            e = 1715143796040637088L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void g(int r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 4
                int r7 = r7 + 1
                int r6 = r6 * 3
                int r6 = 71 - r6
                int r8 = r8 * 4
                int r8 = 3 - r8
                byte[] r0 = o.ek.a.C0041a.$$a
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L34
            L19:
                r3 = r2
            L1a:
                int r8 = r8 + 1
                byte r4 = (byte) r6
                r1[r3] = r4
                int r3 = r3 + 1
                if (r3 != r7) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                r4 = r0[r8]
                r5 = r9
                r9 = r8
                r8 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L34:
                int r6 = r6 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.a.C0041a.g(int, byte, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{52, -61, 40, Tnaf.POW_2_WIDTH};
            $$b = 14;
        }

        C0041a() {
        }

        static o.eg.b e(a aVar) throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            f("ᢎ稐謆䑚ᣭꀎ㾊쭃焻㸏", TextUtils.indexOf("", "", 0) + 1, objArr);
            bVar.d(((String) objArr[0]).intern(), aVar.d());
            Object[] objArr2 = new Object[1];
            f("槸\uebfa﮽鳚榌㇠伱Ꮚw꿀\ue529旇멃䗢猥ￋ呥\uf3fdद凄칦槦꜀ꯃ硵ߥ㴢㷀", -((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
            String intern = ((String) objArr2[0]).intern();
            String str = null;
            switch (aVar.e() == null) {
                case true:
                    break;
                default:
                    int i = b + 73;
                    c = i % 128;
                    switch (i % 2 == 0 ? '#' : 'O') {
                        case Opcodes.IASTORE /* 79 */:
                            str = aVar.e().name();
                            break;
                        default:
                            aVar.e().name();
                            throw null;
                    }
            }
            bVar.d(intern, str);
            if (aVar.a() != null) {
                int i2 = b + 1;
                c = i2 % 128;
                int i3 = i2 % 2;
                Object[] objArr3 = new Object[1];
                f("聍嗘籬\uec14耨迄죽挄\ue9e2ᇗ拷ᔙ叜\ufbcc\uf4ce輎뷒䷎軰ℱ⟎\ud7c7⃖\udb16释맖뫨䴴﯁", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 1, objArr3);
                bVar.d(((String) objArr3[0]).intern(), aVar.a().b());
                Object[] objArr4 = new Object[1];
                f("翅糮▋䞭羠꛲鄚좽ᙪ㣡㬐뺠걔틺괩Ⓑ䉚擸휗誏\ud841ﻠ礑炵湚", 1 - View.MeasureSpec.getSize(0), objArr4);
                bVar.d(((String) objArr4[0]).intern(), aVar.a().c().e());
            }
            return bVar;
        }

        /* JADX WARN: Removed duplicated region for block: B:11:0x00a0  */
        /* JADX WARN: Removed duplicated region for block: B:13:0x00a6  */
        /* JADX WARN: Removed duplicated region for block: B:19:0x00a7  */
        /* JADX WARN: Removed duplicated region for block: B:21:0x00a2  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        static o.ek.a a(o.eg.b r11) throws o.eg.d {
            /*
                Method dump skipped, instructions count: 294
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.a.C0041a.a(o.eg.b):o.ek.a");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
        /* JADX WARN: Type inference failed for: r13v1 */
        /* JADX WARN: Type inference failed for: r13v9, types: [char[]] */
        private static void f(java.lang.String r13, int r14, java.lang.Object[] r15) {
            /*
                Method dump skipped, instructions count: 366
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.a.C0041a.f(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\a$d.smali */
    public static final class d {
        private final String b;
        private final o.eo.b e;
        private static int c = 0;
        private static int a = 1;

        public d(String str, o.eo.b bVar) {
            this.b = str;
            this.e = bVar;
        }

        public final String b() {
            int i = (c + 76) - 1;
            a = i % 128;
            switch (i % 2 == 0 ? '/' : (char) 16) {
                case 16:
                    return this.b;
                default:
                    throw null;
            }
        }

        public final o.eo.b c() {
            int i = c;
            int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
            a = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return this.e;
                default:
                    throw null;
            }
        }
    }
}
