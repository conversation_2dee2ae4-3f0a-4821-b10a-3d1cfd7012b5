package com.vasco.digipass.sdk.obfuscated;

import androidx.recyclerview.widget.ItemTouchHelper;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.responses.DigipassPropertiesResponse;
import com.vasco.digipass.sdk.responses.ManageApplicationResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import org.bouncycastle.asn1.BERTags;
import org.bouncycastle.math.Primes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\j.smali */
public final class j implements DigipassSDKConstants {
    static String A() {
        byte[] bArr = new byte[50];
        int[] iArr = {Opcodes.RET, 188, Opcodes.INVOKESTATIC, 114, Opcodes.INVOKEVIRTUAL, Opcodes.IFNULL, Opcodes.GETFIELD, 188, Opcodes.INSTANCEOF, 108, Opcodes.ANEWARRAY, Opcodes.DRETURN, Opcodes.TABLESWITCH, Opcodes.LOOKUPSWITCH, Opcodes.NEW, Opcodes.DRETURN, Opcodes.NEW, Opcodes.IF_ACMPEQ, Opcodes.INVOKESPECIAL, Opcodes.LOOKUPSWITCH, Opcodes.ARETURN, Opcodes.FRETURN, 95, Opcodes.IF_ICMPLT, Opcodes.IRETURN, Opcodes.RETURN, Opcodes.RET, Opcodes.FRETURN, Opcodes.IFLE, Opcodes.TABLESWITCH, 87, Opcodes.IFGE, Opcodes.IF_ICMPLE, Opcodes.IF_ACMPNE, Opcodes.IF_ICMPNE, Opcodes.I2S, Opcodes.IF_ACMPEQ, 80, Opcodes.DCMPG, Opcodes.IF_ICMPLT, 77, Opcodes.FCMPL, Opcodes.IFEQ, Opcodes.F2D, Opcodes.DCMPG, Opcodes.IFNE, Opcodes.IFEQ, Opcodes.F2I, Opcodes.L2I, Opcodes.DCMPG};
        for (int i = 0; i < 50; i++) {
            bArr[i] = (byte) ((iArr[i] - 85) + i);
        }
        return new String(bArr);
    }

    static String A0() {
        byte[] bArr = new byte[28];
        int[] iArr = {84, Opcodes.DSUB, 99, 29, 84, 65, 59, 61, 24, 99, 91, 99, 91, Opcodes.DSUB, 90, 17, 89, 98, 14, 86, 90, 78, 89, 91, 90, 76, 73, 89};
        for (int i = 0; i < 28; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String B() {
        byte[] bArr = new byte[50];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.LSUB, Opcodes.FNEG, Opcodes.LSUB, Opcodes.FDIV, Opcodes.INEG, 32, 114, Opcodes.LSUB, 97, 99, Opcodes.INEG, Opcodes.LMUL, Opcodes.FNEG, 97, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 32, 99, Opcodes.DDIV, Opcodes.LNEG, Opcodes.FDIV, Opcodes.INEG, Opcodes.LSUB, 114, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 50; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String B0() {
        byte[] bArr = new byte[16];
        int[] iArr = {16, 35, 39, 89, 16, 5, 127, Opcodes.LSHL, 92, 36, 45, 81, 38, 44, 26, 25};
        for (int i = 0; i < 16; i++) {
            bArr[i] = (byte) ((iArr[i] ^ 68) + i);
        }
        return new String(bArr);
    }

    static String C() {
        byte[] bArr = new byte[41];
        int[] iArr = {21, 1, 4, 217, 4, 51, 4, 11, 53, 217, 55, 4, 24, 6, 53, 0, 51, 24, 53, 0, 10, 11, 217, 6, 10, 52, 11, 53, 4, 55, 217, 0, 54, 217, 0, 11, 51, 24, 13, 0, 5};
        for (int i = 0; i < 41; i++) {
            bArr[i] = (byte) ((iArr[i] ^ 23) + 82);
        }
        return new String(bArr);
    }

    static String D() {
        byte[] bArr = new byte[38];
        int[] iArr = {25, Opcodes.IFGT, 57, Opcodes.D2F, Opcodes.INVOKESTATIC, 89, Opcodes.INVOKEDYNAMIC, 26, Opcodes.GETFIELD, Opcodes.GETSTATIC, 22, Opcodes.DCMPG, 81, 54, 245, 18, 108, 204, Opcodes.LOR, 15, 47, 73, 109, 10, Opcodes.IINC, Opcodes.L2D, Opcodes.IF_ACMPNE, 230, Opcodes.D2L, Opcodes.IF_ICMPLT, 66, 228, 92, 252, 255, 24, 223, 56};
        for (int i = 0; i < 38; i++) {
            int i2 = (iArr[i] ^ i) + Opcodes.LREM;
            bArr[i] = (byte) ((((i2 & 255) >> 5) | (i2 << 3)) & 255);
        }
        return new String(bArr);
    }

    static String E() {
        byte[] bArr = new byte[34];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.FDIV, Opcodes.IREM, Opcodes.LNEG, Opcodes.INEG, 32, 100, 97, Opcodes.INEG, 97, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 34; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) (((i2 & 255) | (i2 << 8)) & 255);
        }
        return new String(bArr);
    }

    static String F() {
        byte[] bArr = new byte[22];
        int[] iArr = {241, 5, 2, Opcodes.ANEWARRAY, 6, 11, 13, 18, 17, Opcodes.ANEWARRAY, 1, 254, 17, 254, Opcodes.ANEWARRAY, 6, 16, Opcodes.ANEWARRAY, 11, 18, 9, 9};
        for (int i = 0; i < 22; i++) {
            bArr[i] = (byte) (iArr[i] - 157);
        }
        return new String(bArr);
    }

    static String G() {
        byte[] bArr = new byte[31];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.FMUL, 97, Opcodes.LMUL, 108, 98, 114, Opcodes.LSUB, 97, Opcodes.DMUL, 32, Opcodes.DREM, Opcodes.INEG, 97, Opcodes.INEG, Opcodes.LNEG, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, Opcodes.FNEG, 97, 108, Opcodes.LMUL, 100};
        for (int i = 0; i < 31; i++) {
            bArr[i] = (byte) ((iArr[i] ^ i) ^ i);
        }
        return new String(bArr);
    }

    static String H() {
        byte[] bArr = new byte[27];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DMUL, Opcodes.LSUB, Opcodes.LSHL, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 27; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String I() {
        byte[] bArr = new byte[15];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, Opcodes.DDIV, Opcodes.FMUL, 127, 39, Opcodes.LREM, Opcodes.IUSHR, 42, Opcodes.LSHL, Opcodes.LOR, Opcodes.LSHL, Opcodes.ISHR};
        for (int i = 0; i < 15; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String J() {
        byte[] bArr = new byte[33];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 68, 73, 71, 73, 80, 65, 83, 83, 32, 108, Opcodes.LMUL, 99, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DREM, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 33; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String K() {
        byte[] bArr = new byte[64];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 109, Opcodes.LNEG, 108, Opcodes.INEG, Opcodes.LMUL, 45, 100, Opcodes.LSUB, Opcodes.FNEG, Opcodes.LMUL, 99, Opcodes.LSUB, 32, 97, 99, Opcodes.INEG, Opcodes.LMUL, Opcodes.FNEG, 97, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 32, Opcodes.LMUL, Opcodes.DREM, 32, 100, Opcodes.LMUL, Opcodes.DREM, 97, 98, 108, Opcodes.LSUB, 100, 32, Opcodes.LMUL, Opcodes.FDIV, 32, Opcodes.INEG, 104, Opcodes.LSUB, 32, 68, 73, 71, 73, 80, 65, 83, 83, 32, Opcodes.DREM, Opcodes.LSUB, Opcodes.INEG, Opcodes.INEG, Opcodes.LMUL, Opcodes.FDIV, Opcodes.DSUB, Opcodes.DREM};
        for (int i = 0; i < 64; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String L() {
        byte[] bArr = new byte[63];
        int[] iArr = {81, Opcodes.IF_ACMPEQ, Opcodes.IFGT, Opcodes.F2L, Opcodes.MULTIANEWARRAY, 233, 201, 237, Opcodes.MULTIANEWARRAY, 216, Opcodes.INVOKEINTERFACE, Opcodes.INSTANCEOF, 10, 217, Opcodes.MULTIANEWARRAY, 209, 192, 201, 213, 30, 245, 46, 221, 46, 6, 34, 34, 236, 22, 66, 248, 18, 58, 10, 18, 62, 38, 38, 25, 66, 90, 37, Opcodes.ISHR, 78, 70, 53, 201, 225, 221, 233, 10, 209, 30, 34, 89, Opcodes.TABLESWITCH, Opcodes.FNEG, Opcodes.INVOKEVIRTUAL, Opcodes.INVOKEDYNAMIC, Opcodes.I2C, Opcodes.TABLESWITCH, Opcodes.I2C, Opcodes.IFNULL};
        for (int i = 0; i < 63; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) (((((i2 & 255) >> 2) | (i2 << 6)) & 255) - i);
        }
        return new String(bArr);
    }

    static String M() {
        byte[] bArr = new byte[38];
        int[] iArr = {84, Opcodes.DSUB, 99, 29, 64, 68, 65, 66, 72, 56, 73, 72, 20, 92, Opcodes.LSUB, 17, 94, 94, 98, 13, 92, 76, 93, 92, 95, 86, 88, 73, 4, 83, 84, 80, 84, 68, 65, 81, 65, 63};
        for (int i = 0; i < 38; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String N() {
        byte[] bArr = new byte[24];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.IREM, 97, Opcodes.DREM, Opcodes.DREM, Opcodes.DNEG, Opcodes.DDIV, 114, 100, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.INEG, Opcodes.DDIV, Opcodes.DDIV, 32, 108, Opcodes.DDIV, Opcodes.FDIV, Opcodes.DSUB};
        for (int i = 0; i < 24; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String O() {
        byte[] bArr = new byte[25];
        int[] iArr = {202, 28, 251, Opcodes.INVOKESPECIAL, 204, 203, 220, 204, Opcodes.IUSHR, 236, 12, Opcodes.LSHR, 71, 204, 92, Opcodes.DNEG, 205, 109, 93, Opcodes.INVOKEVIRTUAL, 253, 93, 29, 221, 77};
        for (int i = 0; i < 25; i++) {
            int i2 = iArr[i];
            int i3 = (((((i2 & 255) >> 4) | (i2 << 4)) & 255) ^ i) + Opcodes.JSR;
            bArr[i] = (byte) (((i3 & 255) | (i3 << 8)) & 255);
        }
        return new String(bArr);
    }

    static String P() {
        byte[] bArr = new byte[68];
        int[] iArr = {9, 29, 26, 213, 22, 37, 37, 33, 30, 24, 22, 41, 30, 36, 35, 213, 30, 40, 213, 33, 36, 24, 32, 26, 25, 213, 22, 27, 41, 26, 39, 213, 41, 36, 36, 213, 34, 22, 35, 46, 213, 41, 39, 30, 26, 40, 213, 44, 30, 41, 29, 213, 22, 213, 44, 39, 36, 35, 28, 213, 37, 22, 40, 40, 44, 36, 39, 25};
        for (int i = 0; i < 68; i++) {
            bArr[i] = (byte) ((iArr[i] - 165) - 16);
        }
        return new String(bArr);
    }

    static String Q() {
        byte[] bArr = new byte[20];
        int[] iArr = {Opcodes.INVOKESTATIC, 204, 201, Opcodes.IINC, 212, Opcodes.MULTIANEWARRAY, 215, 215, 219, Primes.SMALL_FACTOR_LIMIT, 214, 200, Opcodes.IINC, 205, 215, Opcodes.IINC, 210, 217, 208, 208};
        for (int i = 0; i < 20; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) (((((i2 & 255) | (i2 << 8)) & 255) - 84) - 16);
        }
        return new String(bArr);
    }

    static String R() {
        byte[] bArr = new byte[20];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.IREM, 97, Opcodes.DREM, Opcodes.DREM, Opcodes.DNEG, Opcodes.DDIV, 114, 100, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.DNEG, Opcodes.LSUB, 97, Opcodes.DMUL};
        for (int i = 0; i < 20; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String S() {
        byte[] bArr = new byte[32];
        int[] iArr = {Opcodes.INSTANCEOF, 213, 210, Opcodes.F2D, 221, 206, BERTags.FLAGS, BERTags.FLAGS, 228, 220, 223, 209, Opcodes.F2D, 208, 206, 219, 219, 220, 225, Opcodes.F2D, 207, 210, Opcodes.F2D, 227, 206, 217, 214, 209, 206, 225, 210, 209};
        for (int i = 0; i < 32; i++) {
            bArr[i] = (byte) (iArr[i] + Opcodes.I2S);
        }
        return new String(bArr);
    }

    static String T() {
        byte[] bArr = new byte[90];
        int[] iArr = {BERTags.FLAGS, 221, Primes.SMALL_FACTOR_LIMIT, Opcodes.DCMPL, 192, 221, Primes.SMALL_FACTOR_LIMIT, Opcodes.IFNONNULL, 218, 210, 204, 210, Opcodes.DCMPG, 216, 217, 207, 205, Primes.SMALL_FACTOR_LIMIT, Opcodes.IFNONNULL, Primes.SMALL_FACTOR_LIMIT, 201, 206, 204, Opcodes.LXOR, Opcodes.IFNONNULL, 200, 215, Opcodes.D2L, Opcodes.INSTANCEOF, 218, Opcodes.L2D, Opcodes.MONITORENTER, ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION, 227, 247, 251, 249, 245, Opcodes.GETSTATIC, 241, 249, 254, 255, 234, 235, 252, Opcodes.INVOKEDYNAMIC, 242, 240, Opcodes.IF_ACMPEQ, 229, 230, 238, 239, 237, 247, Opcodes.IRETURN, 239, 235, Opcodes.DRETURN, 254, 232, 230, 226, Opcodes.D2F, Opcodes.LCMP, Opcodes.IXOR, Opcodes.I2C, Opcodes.LCMP, 209, Opcodes.I2S, Opcodes.LCMP, Opcodes.IFGT, Opcodes.LCMP, Opcodes.D2F, Opcodes.F2L, Opcodes.F2L, 217, Opcodes.I2S, Opcodes.D2L, Opcodes.DCMPL, Opcodes.MULTIANEWARRAY, Opcodes.I2L, Opcodes.D2L, Opcodes.I2L, Opcodes.IXOR, Opcodes.L2F, Opcodes.D2F, Opcodes.IFEQ, 128};
        for (int i = 0; i < 90; i++) {
            bArr[i] = (byte) ((iArr[i] ^ i) ^ Opcodes.GETFIELD);
        }
        return new String(bArr);
    }

    static String U() {
        byte[] bArr = new byte[39];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.IREM, 108, 97, Opcodes.INEG, 102, Opcodes.DDIV, 114, 109, 32, 102, Opcodes.LMUL, Opcodes.FDIV, Opcodes.DSUB, Opcodes.LSUB, 114, Opcodes.IREM, 114, Opcodes.LMUL, Opcodes.FDIV, Opcodes.INEG, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.FDIV, Opcodes.DDIV, Opcodes.INEG, 32, 100, Opcodes.LSUB, 102, Opcodes.LMUL, Opcodes.FDIV, Opcodes.LSUB, 100};
        for (int i = 0; i < 39; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String V() {
        byte[] bArr = new byte[36];
        int[] iArr = {16, 36, 33, 220, 44, 46, 43, 48, 33, 31, 48, 37, 43, 42, 220, 48, 53, 44, 33, 220, 37, 47, 220, 42, 43, 48, 220, 47, 49, 44, 44, 43, 46, 48, 33, 32};
        for (int i = 0; i < 36; i++) {
            bArr[i] = (byte) (iArr[i] + 68);
        }
        return new String(bArr);
    }

    static String W() {
        byte[] bArr = new byte[76];
        int[] iArr = {30, 35, 45, Opcodes.LMUL, 62, 61, 35, 59, 43, 39, 37, 37, 102, 43, 33, 43, 61, 47, 48, Opcodes.LSHL, 49, 57, Opcodes.IUSHR, 41, 58, 54, Opcodes.IREM, 33, 55, 36, 39, 34, 5, 25, 12, 73, 25, 7, 9, 3, 66, 17, 5, 17, 20, 2, 23, 0, 20, 15, 29, 29, 94, 30, 15, 93, 19, 83, 3, 5, 4, 30, 26, 18, 42, 98, Opcodes.LSHR, 41, Opcodes.ISHR, 96, 99, 45, Opcodes.FDIV, 108, Opcodes.FDIV, 102};
        for (int i = 0; i < 76; i++) {
            bArr[i] = (byte) ((iArr[i] ^ 74) ^ i);
        }
        return new String(bArr);
    }

    static String X() {
        byte[] bArr = new byte[77];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, Opcodes.INEG, Opcodes.DNEG, Opcodes.LMUL, Opcodes.LREM, 97, 109, Opcodes.DDIV, Opcodes.DDIV, 44, 97, Opcodes.DMUL, 97, Opcodes.DNEG, Opcodes.LSUB, Opcodes.ISHR, 51, Opcodes.LSHR, Opcodes.DREM, 54, 99, Opcodes.IREM, Opcodes.IUSHR, 58, Opcodes.DMUL, Opcodes.LUSHR, Opcodes.FDIV, 109, 104, 79, 83, 70, 3, 83, 77, 67, 73, 8, 91, 79, 91, 94, 72, 93, 74, 94, 69, 87, 87, 20, 84, 69, 23, 89, 25, 73, 79, 78, 84, 80, 88, 96, 40, 49, 99, 48, 42, 41, Opcodes.DSUB, 59, 33, 37, 57, 56};
        for (int i = 0; i < 77; i++) {
            bArr[i] = (byte) (iArr[i] ^ i);
        }
        return new String(bArr);
    }

    static String Y() {
        byte[] bArr = new byte[54];
        int[] iArr = {Opcodes.IRETURN, Opcodes.D2F, Opcodes.IFGT, 216, Opcodes.IFEQ, Opcodes.L2I, Opcodes.L2I, Opcodes.LCMP, Opcodes.I2B, Opcodes.IFLT, Opcodes.IFEQ, Opcodes.F2L, Opcodes.I2B, Opcodes.DCMPL, Opcodes.FCMPG, 216, Opcodes.D2F, Opcodes.IFEQ, Opcodes.F2I, 216, Opcodes.IFNE, Opcodes.IFGT, Opcodes.IFGT, Opcodes.FCMPG, 216, Opcodes.LCMP, Opcodes.DCMPL, Opcodes.IFLT, Opcodes.I2S, Opcodes.IFGT, Opcodes.IFGE, 216, Opcodes.I2B, Opcodes.FCMPG, 216, Opcodes.IFEQ, 216, Opcodes.L2D, Opcodes.IFGT, Opcodes.IFEQ, Opcodes.IFLT, Opcodes.F2L, Opcodes.I2B, Opcodes.D2I, Opcodes.IFEQ, Opcodes.F2L, Opcodes.I2B, Opcodes.DCMPL, Opcodes.FCMPG, 216, Opcodes.IFLT, Opcodes.IFEQ, Opcodes.F2I, Opcodes.IFGT};
        for (int i = 0; i < 54; i++) {
            bArr[i] = (byte) (iArr[i] ^ 248);
        }
        return new String(bArr);
    }

    static String Z() {
        byte[] bArr = new byte[39];
        int[] iArr = {Opcodes.IF_ICMPGE, 67, 43, 1, Opcodes.IFLT, 27, Opcodes.LSHR, Opcodes.I2S, 43, 1, Opcodes.PUTSTATIC, 11, 99, Opcodes.LOOKUPSWITCH, 43, 1, Opcodes.DMUL, Opcodes.LOOKUPSWITCH, Opcodes.IFLT, Opcodes.IF_ICMPGT, 1, 19, 43, 1, 19, 43, Opcodes.IF_ICMPGT, Opcodes.NEW, 43, 43, Opcodes.DREM, 1, Opcodes.LOR, 1, 11, Opcodes.DREM, 35, 1, Opcodes.INVOKEINTERFACE};
        for (int i = 0; i < 39; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) ((((i2 & 255) >> 3) | (i2 << 5)) & 255);
        }
        return new String(bArr);
    }

    public static ManageApplicationResponse a(byte[] bArr, byte[] bArr2, int i, boolean z) {
        try {
            e b = p.b(bArr, bArr2);
            if (!b.e()) {
                return new ManageApplicationResponse(DigipassSDKReturnCodes.STATUS_INVALID, p.a(b));
            }
            if (i >= 1 && i <= b.a) {
                b.a(i).a = z;
                return new ManageApplicationResponse(0, p.a(b));
            }
            return new ManageApplicationResponse(DigipassSDKReturnCodes.CRYPTO_APPLICATION_INDEX_INVALID, p.a(b));
        } catch (h e) {
            return new ManageApplicationResponse(e.a());
        } catch (Exception e2) {
            return new ManageApplicationResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    static String a0() {
        byte[] bArr = new byte[55];
        int[] iArr = {108, 128, Opcodes.LUSHR, 56, Opcodes.F2I, Opcodes.LUSHR, Opcodes.LSHR, Opcodes.F2D, Opcodes.L2D, Opcodes.LUSHR, 56, Opcodes.LSHR, 128, Opcodes.LSHL, Opcodes.I2F, Opcodes.I2F, Opcodes.LUSHR, Opcodes.IINC, 56, Opcodes.LOR, Opcodes.F2I, 56, Opcodes.IUSHR, Opcodes.LOR, Opcodes.F2I, Opcodes.LSHL, Opcodes.ISHR, Opcodes.IINC, Opcodes.LUSHR, Opcodes.IUSHR, 56, Opcodes.LOR, Opcodes.I2F, 56, Opcodes.F2L, 128, Opcodes.LUSHR, 56, 92, 97, 95, 97, 104, 89, Opcodes.DMUL, Opcodes.DMUL, 56, Opcodes.F2I, Opcodes.LUSHR, Opcodes.F2L, Opcodes.F2L, Opcodes.LOR, Opcodes.I2F, 127, Opcodes.F2I};
        for (int i = 0; i < 55; i++) {
            bArr[i] = (byte) (iArr[i] + 232);
        }
        return new String(bArr);
    }

    public static byte[] b(String str) {
        int i;
        if (((byte) (str.charAt(3) - '0')) >= 8) {
            byte[] a = q.a(str.substring(0, 8));
            i = ((a[2] & 255) << 8) + (a[3] & 255) + 4;
        } else {
            i = 56;
        }
        int i2 = i * 2;
        if (str.length() >= i2) {
            return q.a(str.substring(0, i2));
        }
        throw new h(DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH);
    }

    static String b0() {
        byte[] bArr = new byte[51];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.LSUB, 99, Opcodes.LNEG, 114, Opcodes.LSUB, 32, 99, 104, 97, Opcodes.FDIV, Opcodes.FDIV, Opcodes.LSUB, 108, 32, 109, Opcodes.LSUB, Opcodes.DREM, Opcodes.DREM, 97, Opcodes.DSUB, Opcodes.LSUB, 32, 98, Opcodes.DDIV, 100, Opcodes.LSHL, 32, 102, Opcodes.DDIV, 114, 109, 97, Opcodes.INEG, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 51; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    public static byte c(d dVar) {
        byte b = dVar.g;
        if (b == 0) {
            return (byte) 1;
        }
        if (b == 1 && dVar.h[0] == dVar.i[0]) {
            return (byte) 2;
        }
        b bVar = dVar.e;
        if (bVar.B) {
            return (byte) 2;
        }
        return (b > 0 && dVar.h[0] == 0 && bVar.c == 0) ? (byte) 4 : (byte) 3;
    }

    static String c0() {
        byte[] bArr = new byte[51];
        int[] iArr = {89, Opcodes.LSUB, 104, 45, Opcodes.IAND, 104, Opcodes.FDIV, Opcodes.ISHL, 127, 104, 45, Opcodes.FDIV, Opcodes.LSUB, 108, 99, 99, 104, 97, 45, 96, 104, Opcodes.IAND, Opcodes.IAND, 108, Opcodes.FMUL, 104, 45, Opcodes.DDIV, 98, Opcodes.LMUL, Opcodes.INEG, 45, 97, 104, 99, Opcodes.FMUL, Opcodes.LSHL, Opcodes.LSUB, 45, 100, Opcodes.IAND, 45, 100, 99, Opcodes.FDIV, 98, 127, 127, 104, Opcodes.FDIV, Opcodes.LSHL};
        for (int i = 0; i < 51; i++) {
            bArr[i] = (byte) (iArr[i] ^ 13);
        }
        return new String(bArr);
    }

    static String d() {
        byte[] bArr = new byte[2];
        int[] iArr = {83, 71};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String d0() {
        byte[] bArr = new byte[48];
        int[] iArr = {84, Opcodes.DSUB, 99, 29, Opcodes.DDIV, 96, 93, Opcodes.FDIV, Opcodes.FMUL, 92, 22, 88, 92, 84, 96, 95, 85, 91, 14, 90, 81, 94, 93, 74, 79, 76, 6, 71, 83, 71, 91, 1, 73, 82, 254, 75, 81, 71, 70, 249, 71, 73, 246, 58, 65, 67, 70, 74};
        for (int i = 0; i < 48; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String e() {
        byte[] bArr = new byte[39];
        int[] iArr = {Opcodes.IUSHR, 97, Opcodes.DDIV, 43, 109, 108, 98, 104, 98, 114, 102, 108, Opcodes.DDIV, Opcodes.DDIV, 54, Opcodes.FNEG, Opcodes.DREM, Opcodes.LUSHR, 127, 59, Opcodes.IAND, Opcodes.ISHL, 108, Opcodes.IUSHR, Opcodes.LOR, Opcodes.LNEG, 66, Opcodes.IUSHR, Opcodes.DREM, 69, 127, Opcodes.LOR, Opcodes.I2D, Opcodes.IINC, Opcodes.ISHL, Opcodes.LSHL, Opcodes.I2B, Opcodes.F2L, Opcodes.IXOR};
        for (int i = 0; i < 39; i++) {
            bArr[i] = (byte) ((((iArr[i] + 0) - 142) - i) ^ Opcodes.INVOKEDYNAMIC);
        }
        return new String(bArr);
    }

    static String e0() {
        byte[] bArr = new byte[46];
        int[] iArr = {213, 233, 228, Opcodes.IF_ICMPLT, 242, 228, 226, 244, 243, 228, Opcodes.IF_ICMPLT, 226, 233, BERTags.FLAGS, 239, 239, 228, 237, Opcodes.IF_ICMPLT, 236, 228, 242, 242, BERTags.FLAGS, 230, 228, Opcodes.IF_ICMPLT, 231, 238, 243, 236, BERTags.FLAGS, 245, Opcodes.IF_ICMPLT, 232, 242, Opcodes.IF_ICMPLT, 232, 239, 226, 238, 243, 243, 228, 226, 245};
        for (int i = 0; i < 46; i++) {
            bArr[i] = (byte) (iArr[i] ^ Opcodes.LOR);
        }
        return new String(bArr);
    }

    static String f() {
        byte[] bArr = new byte[39];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 97, 99, Opcodes.INEG, Opcodes.LMUL, Opcodes.FNEG, 97, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 32, 99, Opcodes.DDIV, 100, Opcodes.LSUB, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 39; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String f0() {
        byte[] bArr = new byte[46];
        int[] iArr = {218, 27, 235, Opcodes.DCMPL, 204, 235, 203, 236, 188, 235, Opcodes.DCMPL, 203, 27, Opcodes.LOOKUPSWITCH, Opcodes.LSHR, Opcodes.LSHR, 235, 91, Opcodes.DCMPL, Opcodes.DMUL, 235, 204, 204, Opcodes.LOOKUPSWITCH, 11, 235, Opcodes.DCMPL, 91, 235, Opcodes.LSHR, 11, 220, 27, Opcodes.DCMPL, 43, 204, Opcodes.DCMPL, 43, Opcodes.LSHR, 203, Opcodes.F2I, 188, 188, 235, 203, 220};
        for (int i = 0; i < 46; i++) {
            int i2 = iArr[i] + Opcodes.DMUL;
            bArr[i] = (byte) ((((i2 & 255) >> 4) | (i2 << 4)) & 255);
        }
        return new String(bArr);
    }

    static String g() {
        byte[] bArr = new byte[30];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 97, 99, Opcodes.INEG, Opcodes.LMUL, Opcodes.FNEG, 97, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 32, 99, Opcodes.DDIV, 100, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, Opcodes.FNEG, 97, 108, Opcodes.LMUL, 100};
        for (int i = 0; i < 30; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String g0() {
        byte[] bArr = new byte[67];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.LSUB, 99, Opcodes.LNEG, 114, Opcodes.LSUB, 32, 99, 104, 97, Opcodes.FDIV, Opcodes.FDIV, Opcodes.LSUB, 108, 32, 109, Opcodes.LSUB, Opcodes.DREM, Opcodes.DREM, 97, Opcodes.DSUB, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.FDIV, Opcodes.DDIV, Opcodes.INEG, 32, 100, Opcodes.LSUB, 100, Opcodes.LMUL, 99, 97, Opcodes.INEG, Opcodes.LSUB, 100, 32, Opcodes.INEG, Opcodes.DDIV, 32, Opcodes.INEG, 104, Opcodes.LSUB, 32, 99, Opcodes.LNEG, 114, 114, Opcodes.LSUB, Opcodes.FDIV, Opcodes.INEG, 32, 68, 73, 71, 73, 80, 65, 83, 83};
        for (int i = 0; i < 67; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String h() {
        byte[] bArr = new byte[27];
        int[] iArr = {236, 216, 223, Opcodes.IF_ICMPNE, 227, 221, 204, 219, 206, 227, 204, 219, 209, 214, Opcodes.IF_ICMPNE, 221, 209, 220, 223, Opcodes.IF_ICMPNE, 219, 205, Opcodes.IF_ICMPNE, 214, 207, 212, 212};
        for (int i = 0; i < 27; i++) {
            bArr[i] = (byte) ((iArr[i] ^ 61) - 125);
        }
        return new String(bArr);
    }

    static String h0() {
        byte[] bArr = new byte[44];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.LSUB, 99, Opcodes.LNEG, 114, Opcodes.LSUB, 32, 99, 104, 97, Opcodes.FDIV, Opcodes.FDIV, Opcodes.LSUB, 108, 32, 109, Opcodes.LSUB, Opcodes.DREM, Opcodes.DREM, 97, Opcodes.DSUB, Opcodes.LSUB, 32, Opcodes.INEG, Opcodes.LSHL, Opcodes.IREM, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 44; i++) {
            bArr[i] = (byte) ((iArr[i] + i) - i);
        }
        return new String(bArr);
    }

    static String i() {
        byte[] bArr = new byte[34];
        int[] iArr = {84, Opcodes.DSUB, 99, 29, 95, 109, Opcodes.DREM, Opcodes.LMUL, 108, 102, 22, 86, 100, 99, 94, 90, 83, 80, 98, 86, 91, 89, 10, 82, 91, 7, 74, 78, 87, 68, 68, 77, 69, 67};
        for (int i = 0; i < 34; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String i0() {
        byte[] bArr = new byte[34];
        int[] iArr = {Opcodes.IXOR, Opcodes.FCMPG, Opcodes.I2S, 78, Opcodes.IF_ICMPLT, Opcodes.I2S, Opcodes.I2B, Opcodes.IF_ICMPGT, Opcodes.IF_ICMPNE, Opcodes.I2S, 78, Opcodes.I2B, Opcodes.FCMPG, Opcodes.D2L, Opcodes.IFGE, Opcodes.IFGE, Opcodes.I2S, Opcodes.IFNE, 78, Opcodes.IFLT, Opcodes.I2S, Opcodes.IF_ICMPLT, Opcodes.IF_ICMPLT, Opcodes.D2L, Opcodes.FCMPL, Opcodes.I2S, 78, Opcodes.DCMPL, Opcodes.IF_ICMPLT, 78, Opcodes.IFGE, Opcodes.IF_ICMPGT, Opcodes.IFNE, Opcodes.IFNE};
        for (int i = 0; i < 34; i++) {
            bArr[i] = (byte) (iArr[i] + 210);
        }
        return new String(bArr);
    }

    static String j() {
        byte[] bArr = new byte[58];
        int[] iArr = {Opcodes.IFNULL, 229, 220, Opcodes.DCMPL, 230, 221, Opcodes.DCMPL, 235, 223, 220, Opcodes.DCMPL, 219, 216, 235, 216, Opcodes.DCMPL, 221, BERTags.FLAGS, 220, 227, 219, 234, Opcodes.DCMPL, 235, 230, Opcodes.DCMPL, 234, BERTags.FLAGS, 222, 229, Opcodes.DCMPL, 218, 230, 229, 235, 216, BERTags.FLAGS, 229, 234, Opcodes.DCMPL, BERTags.FLAGS, 229, 237, 216, 227, BERTags.FLAGS, 219, Opcodes.DCMPL, 218, 223, 216, 233, 216, 218, 235, 220, 233, 234};
        for (int i = 0; i < 58; i++) {
            bArr[i] = (byte) (iArr[i] + Opcodes.L2F);
        }
        return new String(bArr);
    }

    static String j0() {
        byte[] bArr = new byte[54];
        int[] iArr = {94, 114, Opcodes.DDIV, 42, Opcodes.LUSHR, Opcodes.DREM, Opcodes.LREM, Opcodes.ISHL, Opcodes.DMUL, Opcodes.IAND, 127, Opcodes.IUSHR, Opcodes.DDIV, 42, Opcodes.LSHL, Opcodes.IREM, 42, Opcodes.IAND, 114, Opcodes.DDIV, 42, Opcodes.LUSHR, Opcodes.DDIV, 109, 127, Opcodes.IUSHR, Opcodes.DDIV, 42, 109, 114, Opcodes.DMUL, Opcodes.ISHL, Opcodes.ISHL, Opcodes.DDIV, Opcodes.FNEG, 42, Opcodes.DNEG, Opcodes.DDIV, Opcodes.LUSHR, Opcodes.LUSHR, Opcodes.DMUL, Opcodes.LREM, Opcodes.DDIV, 42, Opcodes.DREM, Opcodes.LUSHR, 42, Opcodes.DREM, Opcodes.ISHL, 128, Opcodes.DMUL, Opcodes.FNEG, Opcodes.DREM, Opcodes.FDIV};
        for (int i = 0; i < 54; i++) {
            bArr[i] = (byte) (iArr[i] + 246);
        }
        return new String(bArr);
    }

    static String k() {
        byte[] bArr = new byte[33];
        int[] iArr = {188, 208, 205, Opcodes.L2I, 203, 208, 201, 212, 212, 205, 214, 207, 205, Opcodes.L2I, 212, 205, 214, 207, 220, 208, Opcodes.L2I, 209, 219, Opcodes.L2I, 209, 214, 203, 215, 218, 218, 205, 203, 220};
        for (int i = 0; i < 33; i++) {
            bArr[i] = (byte) (iArr[i] - 104);
        }
        return new String(bArr);
    }

    static String k0() {
        byte[] bArr = new byte[Opcodes.DSUB];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.INEG, 97, Opcodes.INEG, Opcodes.LMUL, 99, 32, Opcodes.FNEG, Opcodes.LSUB, 99, Opcodes.INEG, Opcodes.DDIV, 114, 32, 99, Opcodes.DDIV, Opcodes.FDIV, Opcodes.INEG, 97, Opcodes.LMUL, Opcodes.FDIV, Opcodes.LSUB, 100, 32, Opcodes.LMUL, Opcodes.FDIV, 32, Opcodes.INEG, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.LSUB, 99, Opcodes.LNEG, 114, Opcodes.LSUB, 32, 99, 104, 97, Opcodes.FDIV, Opcodes.FDIV, Opcodes.LSUB, 108, 32, 109, Opcodes.LSUB, Opcodes.DREM, Opcodes.DREM, 97, Opcodes.DSUB, Opcodes.LSUB, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.FDIV, Opcodes.DDIV, Opcodes.INEG, 32, Opcodes.LSUB, Opcodes.LREM, Opcodes.LNEG, 97, 108, 32, Opcodes.INEG, Opcodes.DDIV, 32, Opcodes.INEG, 104, Opcodes.LSUB, 32, Opcodes.DDIV, Opcodes.FDIV, Opcodes.LSUB, 32, Opcodes.IREM, 114, Opcodes.DDIV, Opcodes.FNEG, Opcodes.LMUL, 100, Opcodes.LSUB, 100, 32, 97, Opcodes.DREM, 32, Opcodes.IREM, 97, 114, 97, 109, Opcodes.LSUB, Opcodes.INEG, Opcodes.LSUB, 114};
        for (int i = 0; i < 103; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String l() {
        byte[] bArr = new byte[21];
        int[] iArr = {84, Opcodes.DSUB, 99, 29, 95, 99, 91, Opcodes.LSUB, 100, 92, 100, 92, 89, 19, 91, 100, 16, 93, 99, 89, 88};
        for (int i = 0; i < 21; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String l0() {
        byte[] bArr = new byte[37];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.DREM, Opcodes.LSUB, 114, Opcodes.LMUL, 97, 108, 32, Opcodes.FDIV, Opcodes.LNEG, 109, 98, Opcodes.LSUB, 114, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 37; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String m() {
        byte[] bArr = new byte[36];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, 71, Opcodes.LREM, Opcodes.DDIV, 108, Opcodes.FNEG, Opcodes.LUSHR, 42, 94, Opcodes.DDIV, Opcodes.IUSHR, 128, Opcodes.INEG, 48, Opcodes.DNEG, Opcodes.DNEG, Opcodes.INEG, Opcodes.L2I, Opcodes.L2D, Opcodes.L2I, Opcodes.IUSHR, 56, Opcodes.IXOR, Opcodes.F2D, 59, 128, Opcodes.I2F, Opcodes.I2B, 128, Opcodes.IXOR, Opcodes.F2D, Opcodes.I2D, Opcodes.I2D};
        for (int i = 0; i < 36; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String m0() {
        byte[] bArr = new byte[25];
        int[] iArr = {0, 243, Opcodes.IFNULL, 210, 128, 203, Opcodes.IFLE, 253, 220, 235, 219, Opcodes.FCMPL, Opcodes.ARETURN, Opcodes.I2S, Opcodes.IFNULL, 245, Opcodes.L2I, 192, 230, Opcodes.RETURN, Opcodes.MULTIANEWARRAY, Opcodes.IF_ICMPEQ, Opcodes.INVOKEDYNAMIC, Opcodes.IFEQ, Opcodes.DCMPG};
        for (int i = 0; i < 25; i++) {
            int i2 = (iArr[i] ^ 81) - i;
            bArr[i] = (byte) ((((i2 & 255) >> 2) | (i2 << 6)) & 255);
        }
        return new String(bArr);
    }

    static String n() {
        byte[] bArr = new byte[39];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, Opcodes.DSUB, Opcodes.DNEG, 127, Opcodes.DNEG, Opcodes.IUSHR, Opcodes.ISHL, 42, 108, Opcodes.IUSHR, Opcodes.LUSHR, Opcodes.ISHR, Opcodes.ISHL, Opcodes.DREM, 114, Opcodes.I2F, Opcodes.IUSHR, Opcodes.LXOR, Opcodes.LXOR, 54, 128, Opcodes.I2F, Opcodes.LUSHR, 127, Opcodes.I2S, 60, Opcodes.I2F, Opcodes.I2B, 63, Opcodes.L2F, Opcodes.D2L, Opcodes.DCMPG, Opcodes.IINC, Opcodes.D2F, Opcodes.D2I, Opcodes.L2D};
        for (int i = 0; i < 39; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String n0() {
        byte[] bArr = new byte[41];
        int[] iArr = {240, 204, Opcodes.INSTANCEOF, Opcodes.IINC, 215, Opcodes.INSTANCEOF, 214, 210, Opcodes.INSTANCEOF, 214, Opcodes.IINC, 212, 209, Opcodes.IFNULL, 200, 205, Opcodes.IFNONNULL, Opcodes.IINC, 207, Opcodes.INSTANCEOF, 221, Opcodes.IINC, 200, Opcodes.INSTANCEOF, 202, Opcodes.MONITOREXIT, 208, 204, Opcodes.IINC, 205, 215, Opcodes.IINC, 205, 202, Opcodes.IFNONNULL, 203, 214, 214, Opcodes.INSTANCEOF, Opcodes.IFNONNULL, 208};
        for (int i = 0; i < 41; i++) {
            bArr[i] = (byte) (iArr[i] ^ Opcodes.IF_ICMPLE);
        }
        return new String(bArr);
    }

    static String o() {
        byte[] bArr = new byte[31];
        int[] iArr = {229, 216, 214, Opcodes.I2C, 214, Opcodes.IFNULL, 206, Opcodes.IFNULL, 205, 201, Opcodes.IFLT, 201, 192, Opcodes.INSTANCEOF, Opcodes.IFNONNULL, Opcodes.INSTANCEOF, 207, 203, 52, 49, Opcodes.I2L, 207, 56, Opcodes.I2F, 48, 54, 33, 205, 57, 55, 51};
        for (int i = 0; i < 31; i++) {
            bArr[i] = (byte) ((iArr[i] ^ Opcodes.RETURN) - i);
        }
        return new String(bArr);
    }

    static String o0() {
        byte[] bArr = new byte[91];
        int[] iArr = {34, 30, 19, 86, 21, 3, 27, 3, 26, 23, 2, 31, 0, 19, 86, 5, 31, 12, 19, 86, 25, 16, 86, 2, 30, 19, 86, 23, 21, 2, 31, 0, 23, 2, 31, 25, 24, 86, 6, 23, 5, 5, 1, 25, 4, 18, 86, 23, 24, 18, 86, 2, 30, 19, 86, 24, 25, 24, 21, 19, 86, 31, 5, 86, 2, 25, 25, 86, 30, 31, 17, 30, 86, 94, 26, 31, 27, 31, 2, 76, 86, 67, 71, 68, 86, 20, 15, 2, 19, 5, 95};
        for (int i = 0; i < 91; i++) {
            bArr[i] = (byte) (iArr[i] ^ Opcodes.FNEG);
        }
        return new String(bArr);
    }

    static String p() {
        byte[] bArr = new byte[26];
        int[] iArr = {Opcodes.DSUB, 91, 86, 19, 80, 65, 74, 67, 71, 92, 19, 94, 92, 87, 86, 19, 90, 64, 19, 90, 93, 69, 82, 95, 90, 87};
        for (int i = 0; i < 26; i++) {
            bArr[i] = (byte) (iArr[i] ^ 51);
        }
        return new String(bArr);
    }

    static String p0() {
        byte[] bArr = new byte[37];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, Opcodes.DNEG, Opcodes.LSHL, Opcodes.DSUB, Opcodes.LSHR, Opcodes.LREM, 108, 42, Opcodes.LOR, Opcodes.LREM, Opcodes.IREM, Opcodes.IXOR, Opcodes.IAND, Opcodes.IXOR, 49, Opcodes.ISHL, Opcodes.IXOR, Opcodes.I2F, Opcodes.IXOR, Opcodes.DNEG, Opcodes.F2I, 56, Opcodes.IXOR, Opcodes.F2D, 59, Opcodes.I2L, Opcodes.F2I, Opcodes.LOR, Opcodes.D2I, Opcodes.I2C, Opcodes.I2S, Opcodes.I2D, Opcodes.I2F, Opcodes.DCMPG};
        for (int i = 0; i < 37; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String q() {
        byte[] bArr = new byte[50];
        int[] iArr = {79, 109, 99, 29, Opcodes.DMUL, 97, 26, 109, 96, 92, 22, 89, 85, Opcodes.DSUB, 83, 17, 86, 88, 83, 89, 80, 94, 10, 93, 87, 7, 89, 78, 75, 81, 2, 77, 69, 77, 69, 81, 68, 251, 67, 76, 248, 64, 68, 56, 67, 69, 68, 54, 51, 67};
        for (int i = 0; i < 50; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String q0() {
        byte[] bArr = new byte[37];
        int[] iArr = {225, 221, 208, Opcodes.FCMPL, Opcodes.IFNULL, Opcodes.INSTANCEOF, 212, Opcodes.INSTANCEOF, 220, 214, Opcodes.FCMPL, Opcodes.MONITOREXIT, 208, 214, Opcodes.INSTANCEOF, 218, Opcodes.IFNONNULL, Opcodes.FCMPL, 217, 208, 219, 210, Opcodes.INSTANCEOF, 221, Opcodes.FCMPL, 220, Opcodes.IFNULL, Opcodes.FCMPL, 220, 219, 214, 218, Opcodes.IFNONNULL, Opcodes.IFNONNULL, 208, 214, Opcodes.INSTANCEOF};
        for (int i = 0; i < 37; i++) {
            bArr[i] = (byte) (((iArr[i] ^ Opcodes.PUTFIELD) ^ i) ^ i);
        }
        return new String(bArr);
    }

    static String r() {
        byte[] bArr = new byte[38];
        int[] iArr = {79, Opcodes.DDIV, Opcodes.DSUB, 35, Opcodes.DREM, Opcodes.DMUL, 38, Opcodes.LSHR, Opcodes.IREM, Opcodes.FDIV, 42, Opcodes.DDIV, 109, Opcodes.LOR, Opcodes.DDIV, 47, Opcodes.FNEG, Opcodes.ISHR, Opcodes.DNEG, 127, Opcodes.ISHL, Opcodes.L2I, 54, Opcodes.F2I, Opcodes.I2D, 57, Opcodes.F2D, Opcodes.IINC, Opcodes.LXOR, Opcodes.F2I, 62, Opcodes.L2I, Opcodes.I2S, 65, Opcodes.D2F, Opcodes.DCMPG, Opcodes.D2F, Opcodes.I2B};
        for (int i = 0; i < 38; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String r0() {
        byte[] bArr = new byte[25];
        int[] iArr = {Opcodes.IF_ICMPLT, Opcodes.PUTSTATIC, Opcodes.FRETURN, Opcodes.DSUB, Opcodes.INVOKESTATIC, Opcodes.INVOKESPECIAL, Opcodes.IF_ICMPGE, Opcodes.PUTSTATIC, Opcodes.IF_ACMPNE, Opcodes.IFLE, 89, Opcodes.LRETURN, Opcodes.IFNE, Opcodes.FCMPG, Opcodes.IF_ACMPEQ, Opcodes.IFLE, Opcodes.IF_ICMPEQ, 75, Opcodes.I2C, Opcodes.IFNE, 69, Opcodes.I2B, Opcodes.FCMPG, Opcodes.F2I, Opcodes.L2F};
        for (int i = 0; i < 25; i++) {
            bArr[i] = (byte) (((iArr[i] + i) - 77) + i);
        }
        return new String(bArr);
    }

    static String s() {
        byte[] bArr = new byte[29];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 100, 97, Opcodes.INEG, 97, 32, 102, Opcodes.LMUL, Opcodes.LSUB, 108, 100, Opcodes.DREM, 32, 97, 114, 114, 97, Opcodes.LSHL, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.FDIV, Opcodes.LNEG, 108, 108};
        for (int i = 0; i < 29; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String s0() {
        byte[] bArr = new byte[56];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, 72, 78, 77, 80, 88, 74, 93, 94, 44, 128, Opcodes.IXOR, Opcodes.IREM, Opcodes.IINC, Opcodes.I2F, Opcodes.I2L, 51, Opcodes.LUSHR, Opcodes.L2I, 54, 128, Opcodes.I2F, Opcodes.D2L, Opcodes.LSHR, Opcodes.I2D, Opcodes.I2L, Opcodes.LOR, 62, 71, Opcodes.D2I, Opcodes.D2F, Opcodes.FCMPG, 67, Opcodes.I2L, Opcodes.L2I, Opcodes.IFNE, Opcodes.D2F, Opcodes.IFLE, Opcodes.L2D, Opcodes.IFLE, Opcodes.D2F, Opcodes.D2F, 77, Opcodes.IFGT, Opcodes.IF_ICMPLT, 80, Opcodes.IFGT, Opcodes.IF_ICMPLT, Opcodes.FCMPG, Opcodes.IF_ICMPEQ, Opcodes.IFNE, Opcodes.IFNE, 96};
        for (int i = 0; i < 56; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String t() {
        byte[] bArr = new byte[34];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 100, 97, Opcodes.INEG, 97, 32, 102, Opcodes.LMUL, Opcodes.LSUB, 108, 100, Opcodes.DREM, 32, 97, 114, Opcodes.LSUB, 32, Opcodes.FDIV, Opcodes.DDIV, Opcodes.INEG, 32, 99, Opcodes.DDIV, Opcodes.FDIV, Opcodes.INEG, Opcodes.LMUL, Opcodes.DSUB, Opcodes.LNEG, Opcodes.DDIV, Opcodes.LNEG, Opcodes.DREM};
        for (int i = 0; i < 34; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String t0() {
        byte[] bArr = new byte[20];
        int[] iArr = {79, Opcodes.LREM, Opcodes.DSUB, Opcodes.LREM, Opcodes.LSUB, Opcodes.LREM, Opcodes.DDIV, 104, 102, 41, Opcodes.LSHL, Opcodes.IAND, Opcodes.DDIV, Opcodes.FDIV, Opcodes.DMUL, Opcodes.IUSHR, 99, Opcodes.DNEG, Opcodes.DSUB, 127};
        for (int i = 0; i < 20; i++) {
            bArr[i] = (byte) (iArr[i] ^ i);
        }
        return new String(bArr);
    }

    static String u() {
        byte[] bArr = new byte[44];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, Opcodes.FDIV, Opcodes.LNEG, 109, 98, Opcodes.LSUB, 114, 32, Opcodes.DDIV, 102, 32, 100, 97, Opcodes.INEG, 97, 32, 102, Opcodes.LMUL, Opcodes.LSUB, 108, 100, Opcodes.DREM, 32, Opcodes.INEG, Opcodes.DDIV, 32, Opcodes.DREM, Opcodes.LMUL, Opcodes.DSUB, Opcodes.FDIV, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, Opcodes.FNEG, 97, 108, Opcodes.LMUL, 100};
        for (int i = 0; i < 44; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String u0() {
        byte[] bArr = new byte[37];
        int[] iArr = {84, Opcodes.LMUL, Opcodes.DSUB, 35, Opcodes.ISHL, Opcodes.INEG, Opcodes.LREM, 108, Opcodes.FNEG, 41, Opcodes.FDIV, Opcodes.IREM, Opcodes.IAND, Opcodes.FNEG, Opcodes.IINC, Opcodes.IREM, Opcodes.IINC, Opcodes.ISHR, Opcodes.LOR, Opcodes.LOR, 52, Opcodes.IAND, Opcodes.L2F, 55, Opcodes.I2F, Opcodes.L2I, Opcodes.D2I, 59, Opcodes.D2L, Opcodes.I2C, Opcodes.D2I, Opcodes.D2L, Opcodes.D2L, Opcodes.I2S, Opcodes.FCMPG, Opcodes.L2I, Opcodes.L2I};
        for (int i = 0; i < 37; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    static String v() {
        byte[] bArr = new byte[38];
        int[] iArr = {Opcodes.INVOKESPECIAL, 48, Opcodes.I2B, 57, Opcodes.RETURN, 18, 240, 17, Opcodes.D2F, 16, 81, 57, 243, Opcodes.I2B, 81, Opcodes.PUTSTATIC, 208, Opcodes.DREM, 57, 241, 208, Opcodes.DREM, Opcodes.D2F, 17, Opcodes.PUTSTATIC, 57, 16, 83, 57, 16, 240, 81, 208, Opcodes.DREM, Opcodes.DREM, Opcodes.I2B, 81, Opcodes.PUTSTATIC};
        for (int i = 0; i < 38; i++) {
            int i2 = iArr[i];
            int i3 = (((i2 & 255) >> 1) | (i2 << 7)) & 255;
            bArr[i] = (byte) (((((i3 & 255) >> 4) | (i3 << 4)) & 255) ^ 233);
        }
        return new String(bArr);
    }

    static String v0() {
        byte[] bArr = new byte[18];
        int[] iArr = {85, 109, Opcodes.LMUL, Opcodes.DMUL, Opcodes.DMUL, 114, 104, 25, 93, Opcodes.LMUL, 104, 100, 102, 19, 85, 96, 84, 84};
        for (int i = 0; i < 18; i++) {
            bArr[i] = (byte) (iArr[i] + i);
        }
        return new String(bArr);
    }

    static String w() {
        byte[] bArr = new byte[38];
        int[] iArr = {15, 62, 66, 88, 67, 47, 75, 25, 64, 67, 28, 80, 73, 27, 16, 64, 22, 68, Opcodes.LSUB, 28, 20, 28, 20, 24, 19, 94, 38, 27, 95, 39, 19, 31, 38, 20, 19, 33, Opcodes.FNEG, 38};
        for (int i = 0; i < 38; i++) {
            bArr[i] = (byte) (((iArr[i] - 141) ^ 214) + i);
        }
        return new String(bArr);
    }

    static String w0() {
        byte[] bArr = new byte[59];
        int[] iArr = {85, Opcodes.FDIV, Opcodes.DMUL, Opcodes.FDIV, Opcodes.DDIV, Opcodes.DNEG, Opcodes.FDIV, 32, Opcodes.LSUB, 114, 114, Opcodes.DDIV, 114, 58, 32, Opcodes.DDIV, 99, 99, Opcodes.LNEG, 114, Opcodes.DREM, 32, Opcodes.DNEG, 104, Opcodes.LSUB, Opcodes.FDIV, 32, 97, Opcodes.FDIV, 32, Opcodes.LNEG, Opcodes.FDIV, 109, 97, Opcodes.FDIV, 97, Opcodes.DSUB, Opcodes.LSUB, 100, 32, Opcodes.LSUB, Opcodes.ISHL, 99, Opcodes.LSUB, Opcodes.IREM, Opcodes.INEG, Opcodes.LMUL, Opcodes.DDIV, Opcodes.FDIV, 32, Opcodes.LMUL, Opcodes.DREM, 32, 114, 97, Opcodes.LMUL, Opcodes.DREM, Opcodes.LSUB, 100};
        for (int i = 0; i < 59; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String x() {
        byte[] bArr = new byte[26];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 100, Opcodes.LSHL, Opcodes.FDIV, 97, 109, Opcodes.LMUL, 99, 32, Opcodes.FNEG, Opcodes.LSUB, 99, Opcodes.INEG, Opcodes.DDIV, 114, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.FDIV, Opcodes.LNEG, 108, 108};
        for (int i = 0; i < 26; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String x0() {
        byte[] bArr = new byte[28];
        int[] iArr = {90, Opcodes.IINC, 128, 248, Opcodes.FMUL, 70, 98, 70, 2, Opcodes.D2F, Opcodes.IF_ICMPLE, Opcodes.IRETURN, Opcodes.IF_ICMPLE, Opcodes.D2I, Opcodes.INVOKESPECIAL, 16, Opcodes.IF_ICMPLE, Opcodes.NEW, 22, Opcodes.TABLESWITCH, Opcodes.INVOKESPECIAL, Opcodes.IF_ICMPGE, Opcodes.ANEWARRAY, Opcodes.MULTIANEWARRAY, Opcodes.IFNONNULL, Opcodes.FRETURN, Opcodes.IRETURN, 209};
        for (int i = 0; i < 28; i++) {
            int i2 = iArr[i] - 178;
            bArr[i] = (byte) (((((i2 & 255) >> 1) | (i2 << 7)) & 255) - i);
        }
        return new String(bArr);
    }

    static String y() {
        byte[] bArr = new byte[38];
        int[] iArr = {40, 60, 57, 244, 57, 66, 55, 70, 77, 68, 72, 61, 67, 66, 244, 63, 57, 77, 244, 64, 57, 66, 59, 72, 60, 244, 61, 71, 244, 61, 66, 55, 67, 70, 70, 57, 55, 72};
        for (int i = 0; i < 38; i++) {
            bArr[i] = (byte) ((iArr[i] - 208) + 108 + Opcodes.D2F);
        }
        return new String(bArr);
    }

    static String y0() {
        byte[] bArr = new byte[28];
        int[] iArr = {84, 104, Opcodes.LSUB, 32, 88, 69, 82, 67, 32, 108, Opcodes.LSUB, Opcodes.FDIV, Opcodes.DSUB, Opcodes.INEG, 104, 32, Opcodes.LMUL, Opcodes.DREM, 32, Opcodes.LMUL, Opcodes.FDIV, 99, Opcodes.DDIV, 114, 114, Opcodes.LSUB, 99, Opcodes.INEG};
        for (int i = 0; i < 28; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    static String z() {
        byte[] bArr = new byte[26];
        int[] iArr = {17, 60, 62, Opcodes.IXOR, 68, 58, 64, 48, 52, 44, 39, 67, 66, 66, Opcodes.DNEG, 65, 48, 61, Opcodes.DREM, 43, 62, Opcodes.IREM, 45, 67, 49, 48};
        for (int i = 0; i < 26; i++) {
            bArr[i] = (byte) (((iArr[i] - 134) ^ 223) ^ i);
        }
        return new String(bArr);
    }

    static String z0() {
        byte[] bArr = new byte[28];
        int[] iArr = {91, 254, Opcodes.ANEWARRAY, 53, 92, 57, Opcodes.INVOKEINTERFACE, 57, 214, Opcodes.ARRAYLENGTH, Opcodes.LUSHR, 0, 253, 94, 32, Opcodes.INVOKEVIRTUAL, 0, 29, 23, 32, 32, Opcodes.IF_ICMPEQ, 0, Opcodes.LUSHR, 30, 96, 0, Opcodes.ARRAYLENGTH};
        for (int i = 0; i < 28; i++) {
            int i2 = iArr[i] - 209;
            bArr[i] = (byte) (((((i2 & 255) >> 5) | (i2 << 3)) & 255) ^ i);
        }
        return new String(bArr);
    }

    static String c() {
        byte[] bArr = new byte[2];
        int[] iArr = {82, 78};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) (iArr[i] ^ i);
        }
        return new String(bArr);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    public static int b(byte[] bArr) {
        byte b;
        byte b2;
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        byte b3;
        byte b4;
        byte b5;
        int i6;
        int i7;
        byte b6;
        int i8;
        int i9;
        byte b7;
        int i10;
        int i11;
        byte b8;
        if (q.f(bArr)) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_NULL;
        }
        byte b9 = bArr[0];
        if (bArr.length != 56 || b9 != 56) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH;
        }
        int i12 = bArr[21] & 255;
        if (i12 != 27 && i12 != 47 && i12 != 139 && i12 != 159 && i12 != 34 && i12 != 54 && i12 != 146 && i12 != 166 && i12 != 30 && i12 != 46 && i12 != 142 && i12 != 158) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
        }
        byte b10 = bArr[23];
        if (b10 != 0 && b10 != 1 && b10 != 2 && b10 != 15) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
        }
        byte b11 = bArr[24];
        if (b10 != 15 && (b11 < 4 || b11 > 15)) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
        }
        byte b12 = bArr[25];
        if ((b12 == 0 || b12 == 1 || b12 == 2) && (b = bArr[26]) >= 0 && b <= 9 && (((b2 = bArr[27]) == 15 || (b2 >= 0 && b2 <= 9)) && (i = bArr[28] & 255) >= 10 && i <= 200 && (i2 = (bArr[29] & 240) >> 4) >= 0 && i2 <= 255)) {
            byte b13 = bArr[30];
            if (i2 != 0 && b13 != 1) {
                return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
            }
            byte b14 = bArr[36];
            int i13 = b14 & 15;
            byte b15 = bArr[39];
            int i14 = 16;
            if (i13 == 0) {
                i13 = 16;
            }
            int i15 = 19;
            if (b15 == 2) {
                switch (i13) {
                    case 1:
                        i13 = 17;
                        i3 = 20;
                        break;
                    case 2:
                        i13 = 18;
                        i3 = 20;
                        break;
                    case 3:
                        i13 = 19;
                        i3 = 20;
                        break;
                    case 4:
                        i13 = 20;
                        i3 = 20;
                        break;
                    default:
                        i3 = 20;
                        break;
                }
            } else {
                i3 = 16;
            }
            if (i13 < 4 || i13 > i3 || (i4 = (b14 & 240) >> 4) < 0 || i4 > 10 || i13 + i4 > i3) {
                return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
            }
            if (bArr[37] == 1 && i4 > 0) {
                return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
            }
            byte b16 = bArr[45];
            int i16 = b16 & 15;
            byte b17 = bArr[49];
            if (b17 >= 0 && b17 <= 2) {
                if (i16 == 0) {
                    i16 = 16;
                }
                if (b17 == 2) {
                    switch (i16) {
                        case 1:
                            i15 = 17;
                            i14 = 20;
                            break;
                        case 2:
                            i15 = 18;
                            i14 = 20;
                            break;
                        case 3:
                            i14 = 20;
                            break;
                        case 4:
                            i14 = 20;
                            i15 = 20;
                            break;
                        default:
                            i15 = i16;
                            i14 = 20;
                            break;
                    }
                } else {
                    i15 = i16;
                }
                if (i15 < 4 || i15 > i14 || (i5 = (b16 & 240) >> 4) < 0 || i5 > 10 || i15 + i5 > i14) {
                    return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
                }
                if ((bArr[46] != 1 || i5 <= 0) && (b3 = bArr[47]) >= 0 && b3 <= 3 && (b4 = bArr[48]) >= 0 && b4 <= 8 && (((i6 = (b5 = bArr[50]) & 15) == 15 || (i6 >= 0 && i6 <= 9)) && (i7 = (b5 & 240) >> 4) >= 0 && i7 <= 1 && (i8 = (b6 = bArr[51]) & 15) >= 0 && i8 <= 7 && (i9 = (b6 & 240) >> 4) >= 0 && i9 <= 3 && (i10 = (b7 = bArr[52]) & 15) >= 0 && i10 <= 7 && (i11 = (b7 & 240) >> 4) >= 0 && i11 <= 3 && (b8 = bArr[53]) >= 0 && b8 <= 1)) {
                    return 0;
                }
            }
        }
        return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
    }

    public static DigipassPropertiesResponse a(byte[] bArr, byte[] bArr2, String str) {
        e b;
        try {
            if (bArr == null && bArr2 == null) {
                b = p.a(str);
            } else if (bArr2 == null) {
                b = p.b(bArr);
            } else {
                b = p.b(bArr, bArr2);
            }
            DigipassPropertiesResponse digipassPropertiesResponse = new DigipassPropertiesResponse(0);
            digipassPropertiesResponse.setVersion(b.c.a);
            digipassPropertiesResponse.setStatus(b.d.c);
            digipassPropertiesResponse.setSerialNumber(b.c.b);
            digipassPropertiesResponse.setPasswordMandatory(b.c.d);
            digipassPropertiesResponse.setPasswordProtected(b.d.k);
            digipassPropertiesResponse.setPasswordMinLength(b.c.e);
            digipassPropertiesResponse.setPasswordMaxLength(b.c.f);
            digipassPropertiesResponse.setWeakPasswordControl(b.c.g);
            digipassPropertiesResponse.setPasswordCheckLevel(b.c.h);
            digipassPropertiesResponse.setPenaltyResetAction(b.c.k == 0);
            digipassPropertiesResponse.setPasswordFatal(b.c.i);
            digipassPropertiesResponse.setPasswordFatalCounter(b.d.e);
            digipassPropertiesResponse.setReactivationFatal(b.c.j);
            digipassPropertiesResponse.setReactivationFatalCounter(b.d.f);
            digipassPropertiesResponse.setTokenDerivationSupported(a(b, false));
            digipassPropertiesResponse.setHighSecurity(b.c.f18o);
            digipassPropertiesResponse.setDpPlusHighSecurity(b.c.p);
            digipassPropertiesResponse.setActivationCodeFormatHexa(b.c.q);
            digipassPropertiesResponse.setUseChecksumForActivationCode(b.c.s);
            digipassPropertiesResponse.setUtcTime(System.currentTimeMillis() / 1000);
            digipassPropertiesResponse.setMasterKey(b.c.c);
            digipassPropertiesResponse.setIterationNumber(b.c.l);
            digipassPropertiesResponse.setIterationPower(b.c.m);
            digipassPropertiesResponse.setUseSecretDerivation(b.c.r);
            digipassPropertiesResponse.setStorageVersion(b.d.a);
            digipassPropertiesResponse.setPinVersion(b.d.b);
            digipassPropertiesResponse.setCreationVersion(b.d.l);
            digipassPropertiesResponse.setTokenDerivationActivated(b.d.m);
            digipassPropertiesResponse.setPasswordDerivationActivated(b.d.n);
            digipassPropertiesResponse.setMultiDeviceActivationEnabled(b.c.t);
            digipassPropertiesResponse.setActivationCryptoApplication(a(b.e));
            digipassPropertiesResponse.setDeviceIdBitsNumber(b.c.u);
            digipassPropertiesResponse.setDeviceType(b.d.f17o);
            digipassPropertiesResponse.setSequenceNumber(b.d.r);
            digipassPropertiesResponse.setPayloadKeyType(b.d.u);
            digipassPropertiesResponse.setSecureChannelEnabled(b.d.v);
            DigipassPropertiesResponse.Application[] applicationArr = new DigipassPropertiesResponse.Application[b.a];
            d[] a = b.a();
            for (int i = 0; i < b.a; i++) {
                applicationArr[i] = a(a[i]);
            }
            digipassPropertiesResponse.setApplications(applicationArr);
            return digipassPropertiesResponse;
        } catch (h e) {
            return new DigipassPropertiesResponse(e.a());
        } catch (Exception e2) {
            return new DigipassPropertiesResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static DigipassPropertiesResponse.Application a(d dVar) {
        DigipassPropertiesResponse.Application application = new DigipassPropertiesResponse.Application();
        if (dVar != null) {
            application.setIndex(dVar.c);
            application.setName(dVar.d);
            application.setEnabled(dVar.a);
            application.setDpPlus(dVar.b);
            application.setResponseLength(dVar.j);
            application.setHostCodeLength(dVar.k);
            application.setCheckDigit(dVar.l);
            application.setDataFieldNumber(dVar.g);
            application.setDataFieldsMinLength(dVar.h);
            application.setDataFieldsMaxLength(dVar.i);
            application.setEventCounter(dVar.n);
            application.setLastTimeUsed(dVar.f16o);
            application.setTimeBased(dVar.e.l);
            application.setEventBased(dVar.e.n);
            application.setOutputType(dVar.m);
            application.setCodeword(dVar.e.a);
            b bVar = dVar.e;
            long j = bVar.e;
            int i = 8 << ((int) j);
            if (bVar.q && j > 7) {
                i = 15 << ((int) (j % 8));
            }
            application.setTimeStep(i);
            switch (dVar.p) {
                case 1:
                    application.setAuthenticationMode(c());
                    break;
                case 2:
                    application.setAuthenticationMode(a());
                    break;
                case 3:
                    application.setAuthenticationMode(d());
                    break;
                case 4:
                    application.setAuthenticationMode(b());
                    break;
            }
            application.setScoreCapable(dVar.e.D);
        }
        return application;
    }

    public static String a(int i) {
        if (i == -4999) {
            return w0();
        }
        if (i == -4059) {
            return D();
        }
        if (i == 0) {
            return t0();
        }
        if (i == -4045) {
            return n0();
        }
        if (i != -4044) {
            switch (i) {
                case DigipassSDKReturnCodes.PROVIDED_PASSWORD_AS_STRING_LENGTH_TOO_LONG /* -4087 */:
                    return W();
                case DigipassSDKReturnCodes.PROVIDED_PASSWORD_AS_STRING_LENGTH_TOO_SHORT /* -4086 */:
                    return X();
                case DigipassSDKReturnCodes.PROTECTION_TYPE_NOT_SUPPORTED /* -4085 */:
                    return V();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_FORMAT /* -4084 */:
                    return b0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_LENGTH /* -4083 */:
                    return c0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_BODY_NULL_OR_EMPTY /* -4082 */:
                    return d0();
                case DigipassSDKReturnCodes.SCORE_INVALID /* -4081 */:
                    return Z();
                case DigipassSDKReturnCodes.CLIENT_SCORE_DISABLED /* -4080 */:
                    return m();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_DISABLED /* -4079 */:
                    return a0();
                case DigipassSDKReturnCodes.MULTI_DEVICE_ACTIVATION_ENABLED /* -4078 */:
                    return L();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_TARGET /* -4077 */:
                    return g0();
                case DigipassSDKReturnCodes.LICENSE_INCORRECT /* -4076 */:
                    return J();
                case DigipassSDKReturnCodes.MULTI_DEVICE_ACTIVATION_DISABLED /* -4075 */:
                    return K();
                case DigipassSDKReturnCodes.PLATFORM_ACTIVATION_KEY_INVALID /* -4074 */:
                    return T();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_STATIC_VECTOR_INCONSISTENT /* -4073 */:
                    return k0();
                case DigipassSDKReturnCodes.JAILBREAK_STATUS_INVALID /* -4072 */:
                    return G();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_SIGNATURE_INVALID /* -4071 */:
                    return j0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_TYPE /* -4070 */:
                    return h0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH /* -4069 */:
                    return f0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT /* -4068 */:
                    return e0();
                case DigipassSDKReturnCodes.SECURE_CHANNEL_MESSAGE_NULL /* -4067 */:
                    return i0();
                case DigipassSDKReturnCodes.NOT_PASSWORD_PROTECTED /* -4066 */:
                    return M();
                case DigipassSDKReturnCodes.DATA_FIELDS_NOT_CONTIGUOUS /* -4065 */:
                    return t();
                case DigipassSDKReturnCodes.PLATFORM_FINGERPRINT_NOT_DEFINED /* -4064 */:
                    return U();
                case DigipassSDKReturnCodes.TOKEN_DERIVATION_NOT_SUPPORTED /* -4063 */:
                    return u0();
                default:
                    switch (i) {
                        case DigipassSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH /* -4056 */:
                            return E();
                        case DigipassSDKReturnCodes.INPUT_DATA_NULL /* -4055 */:
                            return F();
                        case DigipassSDKReturnCodes.KEY_INCORRECT_LENGTH /* -4054 */:
                            return H();
                        case DigipassSDKReturnCodes.KEY_NULL /* -4053 */:
                            return I();
                        case DigipassSDKReturnCodes.CRYPTO_MODE_INVALID /* -4052 */:
                            return p();
                        case DigipassSDKReturnCodes.CRYPTO_MECANISM_INVALID /* -4051 */:
                            return o();
                        default:
                            switch (i) {
                                case DigipassSDKReturnCodes.DATA_FIELD_INCORRECT_LENGTH /* -4039 */:
                                    return q();
                                case DigipassSDKReturnCodes.DATA_FIELD_NULL /* -4038 */:
                                    return r();
                                case DigipassSDKReturnCodes.DATA_FIELDS_NUMBER_INVALID /* -4037 */:
                                    return u();
                                case DigipassSDKReturnCodes.DATA_FIELDS_ARRAY_NULL /* -4036 */:
                                    return s();
                                case DigipassSDKReturnCodes.CHALLENGE_INCORRECT_LENGTH /* -4035 */:
                                    return k();
                                case DigipassSDKReturnCodes.CHALLENGE_NULL /* -4034 */:
                                    return l();
                                case DigipassSDKReturnCodes.APPLICATION_DISABLED /* -4033 */:
                                    return i();
                                case DigipassSDKReturnCodes.CRYPTO_APPLICATION_INDEX_INVALID /* -4032 */:
                                    return n();
                                case DigipassSDKReturnCodes.STATUS_INVALID /* -4031 */:
                                    return s0();
                                case DigipassSDKReturnCodes.PASSWORD_LOCK /* -4030 */:
                                    return P();
                                case DigipassSDKReturnCodes.PASSWORD_WRONG /* -4029 */:
                                    return S();
                                case DigipassSDKReturnCodes.PASSWORD_WEAK /* -4028 */:
                                    return R();
                                case DigipassSDKReturnCodes.PASSWORD_LENGTH_TOO_LONG /* -4027 */:
                                    return N();
                                case DigipassSDKReturnCodes.PASSWORD_LENGTH_TOO_SHORT /* -4026 */:
                                    return O();
                                case DigipassSDKReturnCodes.PASSWORD_NULL /* -4025 */:
                                    return Q();
                                case DigipassSDKReturnCodes.REACTIVATION_LOCK /* -4024 */:
                                    return Y();
                                case DigipassSDKReturnCodes.ACTIVATION_CODE_INVALID /* -4023 */:
                                    return g();
                                case DigipassSDKReturnCodes.SHARED_SECRET_TOO_LONG /* -4022 */:
                                    return o0();
                                case DigipassSDKReturnCodes.XERC_INCORRECT_FORMAT /* -4021 */:
                                    return x0();
                                case DigipassSDKReturnCodes.XERC_INCORRECT_LENGTH /* -4020 */:
                                    return y0();
                                case DigipassSDKReturnCodes.XFAD_INCORRECT_FORMAT /* -4019 */:
                                    return z0();
                                case DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH /* -4018 */:
                                    return A0();
                                case DigipassSDKReturnCodes.XFAD_NULL /* -4017 */:
                                    return B0();
                                case DigipassSDKReturnCodes.ERC_INVALID /* -4016 */:
                                    return C();
                                case DigipassSDKReturnCodes.ERC_INCORRECT_FORMAT /* -4015 */:
                                    return A();
                                case DigipassSDKReturnCodes.ERC_INCORRECT_LENGTH /* -4014 */:
                                    return B();
                                case DigipassSDKReturnCodes.ACTIVATION_CODE_INCORRECT_FORMAT /* -4013 */:
                                    return e();
                                case DigipassSDKReturnCodes.ACTIVATION_CODE_INCORRECT_LENGTH /* -4012 */:
                                    return f();
                                case DigipassSDKReturnCodes.ACTIVATION_CODE_NULL /* -4011 */:
                                    return h();
                                case -4010:
                                    return l0();
                                case -4009:
                                    return m0();
                                case DigipassSDKReturnCodes.ENCRYPTION_KEY_INCORRECT_LENGTH /* -4008 */:
                                    return y();
                                case DigipassSDKReturnCodes.ENCRYPTION_KEY_NULL /* -4007 */:
                                    return z();
                                case DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT /* -4006 */:
                                    return v();
                                case DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH /* -4005 */:
                                    return w();
                                case DigipassSDKReturnCodes.DYNAMIC_VECTOR_NULL /* -4004 */:
                                    return x();
                                case DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT /* -4003 */:
                                    return p0();
                                case DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH /* -4002 */:
                                    return q0();
                                case DigipassSDKReturnCodes.STATIC_VECTOR_NULL /* -4001 */:
                                    return r0();
                                default:
                                    return v0();
                            }
                    }
            }
        }
        return j();
    }

    public static byte b(d dVar) {
        b bVar = dVar.e;
        if (bVar.i) {
            return (byte) 5;
        }
        long b = c.b(bVar.a) & (-33554433);
        if (b == 20992 || b == 59393) {
            return (byte) 1;
        }
        if (b == 255 || b == 721104) {
            return (byte) 2;
        }
        if (b == 1037056 || b == 458752 || b == 479232 || b == 196608 || b == 217088 || b == 5439488 || b == 6311936) {
            return (byte) 3;
        }
        return c(dVar);
    }

    static String b() {
        byte[] bArr = new byte[2];
        int[] iArr = {223, 223};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) (iArr[i] - 146);
        }
        return new String(bArr);
    }

    public static long a(long j) {
        return j - (System.currentTimeMillis() / 1000);
    }

    public static int a(byte[] bArr) {
        if (bArr == null) {
            return DigipassSDKReturnCodes.ENCRYPTION_KEY_NULL;
        }
        if (bArr.length != 16) {
            return DigipassSDKReturnCodes.ENCRYPTION_KEY_INCORRECT_LENGTH;
        }
        return 0;
    }

    public static int a(String str) {
        if (str == null) {
            return DigipassSDKReturnCodes.XFAD_NULL;
        }
        if (!q.c(str)) {
            return DigipassSDKReturnCodes.XFAD_INCORRECT_FORMAT;
        }
        if (str.length() <= 4) {
            return DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH;
        }
        if (((byte) (str.charAt(3) - '0')) <= 7) {
            if (str.length() == 146 || str.length() == 142 || str.length() == 166 || str.length() == 158) {
                return 0;
            }
            return DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH;
        }
        if (str.length() < 205 || str.length() > 1964) {
            return DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH;
        }
        return 0;
    }

    public static byte a(byte[] bArr, int i, boolean z) {
        if (bArr == null || bArr.length < i) {
            return (byte) -1;
        }
        byte b = (byte) (z ? 10 : 16);
        byte b2 = (byte) (b + 1);
        byte b3 = b;
        for (int i2 = 0; i2 < i; i2++) {
            byte a = c.a(bArr[i2]);
            if (a < 0) {
                return (byte) -1;
            }
            byte b4 = (byte) (b3 + a);
            if (b4 > b) {
                b4 = (byte) (b4 - b);
            }
            b3 = (byte) (b4 << 1);
            if (b3 > b2) {
                b3 = (byte) (b3 - b2);
            }
        }
        byte b5 = (byte) (b2 - b3);
        byte b6 = b5 != b ? b5 : (byte) 0;
        return (byte) (b6 < 10 ? b6 + 48 : (b6 + 65) - 10);
    }

    public static boolean a(e eVar, boolean z) {
        if (!eVar.c.n) {
            return false;
        }
        d[] a = eVar.a();
        boolean z2 = false;
        for (int i = 0; i < eVar.a; i++) {
            d dVar = a[i];
            if ((z && dVar.b) || (!z && !dVar.b)) {
                byte b = b(dVar);
                boolean z3 = (b == 1) | (b == 2) | (b == 4);
                boolean z4 = b == 3;
                b bVar = dVar.e;
                z2 = (z4 | z3) & (!bVar.A) & (true ^ bVar.q) & bVar.j;
                if (!z2) {
                    break;
                }
            }
        }
        return z2;
    }

    public static byte[] a(e eVar, d dVar, String str, boolean z) {
        boolean z2 = z || (eVar.d.m && a(eVar, dVar.b));
        if (q.d(str) || !(eVar.c.t || z2)) {
            return null;
        }
        long b = c.b(q.a(UtilitiesSDK.hash((byte) 3, str.getBytes()).getOutputData(), 0, 4));
        i iVar = eVar.c;
        byte b2 = iVar.v;
        if (b2 == 0) {
            b >>= (32 - iVar.u) + (iVar.t ? 5 : 0);
        }
        if (iVar.t) {
            b = (b << 5) + (eVar.d.f17o & 255);
        }
        if (b2 != 0) {
            long j = 1;
            for (int i = 0; i < eVar.c.v; i++) {
                j *= 10;
            }
            b %= j;
        }
        return c.a(b);
    }

    static String a() {
        byte[] bArr = new byte[2];
        int[] iArr = {208, Opcodes.FCMPL};
        for (int i = 0; i < 2; i++) {
            int i2 = iArr[i] ^ i;
            int i3 = (((i2 & 255) >> 3) | (i2 << 5)) & 255;
            bArr[i] = (byte) ((((i3 & 255) >> 3) | (i3 << 5)) & 255);
        }
        return new String(bArr);
    }
}
