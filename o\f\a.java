package o.f;

import android.graphics.Color;
import android.os.SystemClock;
import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import javax.crypto.Cipher;
import o.f.e;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\a.smali */
public final class a extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean b;
    private static int c;
    private static char[] d;
    private static boolean e;
    private static int h;
    private static int j;
    private final Cipher a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        f();
        SystemClock.elapsedRealtimeNanos();
        int i = h + 51;
        j = i % 128;
        int i2 = i % 2;
    }

    static void f() {
        d = new char[]{61741, 61735, 61734};
        e = true;
        b = true;
        c = 782103031;
    }

    static void init$0() {
        $$a = new byte[]{125, -17, -70, 109};
        $$b = 208;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r6 = r6 + 117
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.f.a.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = -r7
            int r6 = r6 + r7
            int r7 = r8 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.a.m(short, int, int, java.lang.Object[]):void");
    }

    public a(e.d dVar, Date date, d dVar2, Cipher cipher) {
        super(dVar, date, dVar2);
        this.a = cipher;
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = j + 15;
        h = i % 128;
        int i2 = i % 2;
        o.i.f fVar = o.i.f.a;
        int i3 = h + 75;
        j = i3 % 128;
        int i4 = i3 % 2;
        return fVar;
    }

    @Override // o.f.e
    public final String j() {
        int i = h;
        int i2 = i + 73;
        j = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 109;
        j = i4 % 128;
        switch (i4 % 2 == 0 ? 'U' : '+') {
            case '+':
                return null;
            default:
                int i5 = 54 / 0;
                return null;
        }
    }

    @Override // o.f.e
    public final String c() {
        StringBuilder append = new StringBuilder().append(b().toString());
        Object[] objArr = new Object[1];
        k(null, TextUtils.indexOf("", "") + 127, null, "\u0081", objArr);
        StringBuilder append2 = append.append(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        k(null, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.IAND, null, "\u0083\u0082", objArr2);
        StringBuilder append3 = append2.append(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k(null, Color.green(0) + 127, null, "\u0081", objArr3);
        String obj = append3.append(((String) objArr3[0]).intern()).toString();
        int i = h + Opcodes.LNEG;
        j = i % 128;
        int i2 = i % 2;
        return obj;
    }

    @Override // o.f.e
    public final byte[] d() {
        byte[] a;
        int i = j + Opcodes.DREM;
        h = i % 128;
        switch (i % 2 != 0 ? ',' : (char) 0) {
            case 0:
                a = l().b().a();
                break;
            default:
                a = l().b().a();
                int i2 = 17 / 0;
                break;
        }
        int i3 = j + 43;
        h = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = j + Opcodes.LMUL;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                return l().b().a();
            default:
                l().b().a();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = h + Opcodes.DDIV;
        j = i % 128;
        int i2 = i % 2;
        byte[] a = l().b().a();
        int i3 = h + Opcodes.DSUB;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                int i4 = 84 / 0;
                return a;
            default:
                return a;
        }
    }

    public final Cipher h() {
        int i = h + 25;
        j = i % 128;
        switch (i % 2 == 0 ? 'F' : (char) 3) {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.a;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 736
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.a.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
