package bc.org.bouncycastle.math.ec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\WNafPreCompInfo.smali */
public class WNafPreCompInfo implements PreCompInfo {
    volatile int a = 4;
    protected int b = -1;
    protected ECPoint[] c = null;
    protected ECPoint[] d = null;
    protected ECPoint e = null;
    protected int f = -1;

    int a() {
        int i = this.a;
        if (i <= 0) {
            return i;
        }
        int i2 = i - 1;
        this.a = i2;
        return i2;
    }

    int b() {
        return this.a;
    }

    public int getConfWidth() {
        return this.b;
    }

    public ECPoint[] getPreComp() {
        return this.c;
    }

    public ECPoint[] getPreCompNeg() {
        return this.d;
    }

    public ECPoint getTwice() {
        return this.e;
    }

    public int getWidth() {
        return this.f;
    }

    public boolean isPromoted() {
        return this.a <= 0;
    }

    public void setConfWidth(int i) {
        this.b = i;
    }

    public void setPreComp(ECPoint[] eCPointArr) {
        this.c = eCPointArr;
    }

    public void setPreCompNeg(ECPoint[] eCPointArr) {
        this.d = eCPointArr;
    }

    public void setTwice(ECPoint eCPoint) {
        this.e = eCPoint;
    }

    public void setWidth(int i) {
        this.f = i;
    }

    void a(int i) {
        this.a = i;
    }
}
