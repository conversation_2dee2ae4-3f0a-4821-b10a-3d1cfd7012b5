package o.at;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.NotImplementedError;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\at\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static char[] b;
    private static int e;
    private static int g;
    private static boolean i;
    private static int j;
    private String c;
    private d d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        g = 1;
        b = new char[]{61936, 61943, 61930, 61941, 61926, 61947, 61934, 61937, 61915, 61950, 61940, 61924, 61946, 61935, 61913, 61879, 61878, 61932, 61863, 61931, 61939, 61929, 61938, 61904, 61885, 61902, 61899};
        a = true;
        i = true;
        e = 782102919;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.at.b.$$a
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r7 = r7 * 2
            int r7 = 4 - r7
            int r8 = r8 + 117
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = -r7
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.at.b.h(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{105, 1, -115, -23};
        $$b = Opcodes.FNEG;
    }

    final b c(d dVar) {
        int i2 = g + 95;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 4 : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                this.d = dVar;
                return this;
            default:
                this.d = dVar;
                int i3 = 0 / 0;
                return this;
        }
    }

    final b b(String str) {
        int i2 = j + 23;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        this.c = str;
        int i5 = i3 + 51;
        j = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                throw null;
            default:
                return this;
        }
    }

    final o.eg.b a() throws o.eg.d, NotImplementedError {
        o.eg.b bVar = new o.eg.b();
        o.eg.b bVar2 = new o.eg.b();
        switch (!d.e.equals(this.d)) {
            case true:
                int i2 = j + 19;
                g = i2 % 128;
                int i3 = i2 % 2;
                switch (d.a.equals(this.d)) {
                    case true:
                        break;
                    default:
                        if (!d.b.equals(this.d)) {
                            if (d.d.equals(this.d)) {
                                Object[] objArr = new Object[1];
                                f(null, 127 - KeyEvent.getDeadChar(0, 0), null, "\u0094\u0083\u0086\u0088\u0083\u0097\u0083\u0095\u0082\u0097\u0087\u0093\u0086\u0081\u0088\u0093\u008b\u0087\u0093\u008b\u0088\u0081\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0093\u0084\u0083\u008e\u0086\u0081\u0093\u0084\u0081\u0096\u0093\u008b\u0095\u0085\u0087\u0086\u0088\u0083\u0094\u0083\u0084\u008c\u0093\u0092\u008c\u0083\u008e\u008c", objArr);
                                throw new NotImplementedError(((String) objArr[0]).intern());
                            }
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr2 = new Object[1];
                            f(null, 126 - Process.getGidForName(""), null, "\u0093\u0099\u0093\u0083\u0082\u008a\u0089\u0088\u0081\u0087\u0086\u0085\u0084\u0083\u0082\u0098", objArr2);
                            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(this.d);
                            Object[] objArr3 = new Object[1];
                            f(null, View.resolveSize(0, 0) + 127, null, "\u0094\u0083\u0086\u0084\u0081\u0082\u0082\u008d\u008b\u0093\u0086\u0083\u008a\u0093\u0086\u0081\u0088\u0093\u008b\u0087\u0093", objArr3);
                            String obj = append.append(((String) objArr3[0]).intern()).toString();
                            g.c();
                            g.d(obj);
                            throw new NotImplementedError(obj);
                        }
                        break;
                }
        }
        Object[] objArr4 = new Object[1];
        f(null, ImageFormat.getBitsPerPixel(0) + 128, null, "\u0083\u0082\u008a\u0089\u0088\u0081\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr4);
        bVar2.d(((String) objArr4[0]).intern(), this.d.e());
        Object[] objArr5 = new Object[1];
        f(null, (Process.myPid() >> 22) + 127, null, "\u0083\u0082\u0081\u008c\u008b", objArr5);
        bVar.d(((String) objArr5[0]).intern(), (Object) null);
        Object[] objArr6 = new Object[1];
        f(null, TextUtils.getTrimmedLength("") + 127, null, "\u0088\u0081\u0087\u008b\u0084\u0083\u008f\u0088\u0081\u0087\u0086\u0085\u008c\u0087\u0086\u0088\u0083\u008e\u0086\u008d\u0085", objArr6);
        String intern = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        f(null, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 128, null, "\u0091\u0090\u008f", objArr7);
        bVar.d(intern, ((String) objArr7[0]).intern());
        int i4 = j + 43;
        int i5 = i4 % 128;
        g = i5;
        int i6 = i4 % 2;
        int i7 = i5 + 89;
        j = i7 % 128;
        int i8 = i7 % 2;
        Object[] objArr8 = new Object[1];
        f(null, Color.rgb(0, 0, 0) + 16777343, null, "\u0086\u008d\u0082\u0088\u009a\u0088\u0081\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr8);
        bVar2.d(((String) objArr8[0]).intern(), this.c);
        Object[] objArr9 = new Object[1];
        f(null, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 127, null, "\u0085\u0086\u0085\u009b\u0094\u0083\u0086\u0085\u008c\u0087\u0086\u0088\u0083\u008e\u0086\u008d\u0085", objArr9);
        bVar.d(((String) objArr9[0]).intern(), bVar2.b());
        return bVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v20, types: [byte[]] */
    private static void f(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 712
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.at.b.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
