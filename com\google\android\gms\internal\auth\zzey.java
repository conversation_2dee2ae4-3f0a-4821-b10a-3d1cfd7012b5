package com.google.android.gms.internal.auth;

import java.util.List;
import java.util.RandomAccess;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzey.smali */
public interface zzey extends List, RandomAccess {
    void zzb();

    boolean zzc();

    zzey zzd(int i);
}
