package o.av;

import android.content.Context;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.text.Typography;
import o.bb.d;
import o.bb.e;
import o.bv.g;
import o.cf.j;
import o.de.f;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\av\b.smali */
public abstract class b extends o.y.b<InterfaceC0029b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int c;
    private static char[] d;
    private static int e;
    protected final f b;

    /* renamed from: o.av.b$b, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\av\b$b.smali */
    public interface InterfaceC0029b {
        void b(d dVar, g gVar);

        void c(d dVar, g gVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        o();
        ViewConfiguration.getScrollBarSize();
        ViewConfiguration.getScrollBarFadeDuration();
        ViewConfiguration.getGlobalActionKeyTimeout();
        int i = e + Opcodes.DREM;
        c = i % 128;
        switch (i % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$d = new byte[]{18, UtilitiesSDKConstants.SRP_LABEL_MAC, -55, -33};
        $$e = 245;
    }

    static void o() {
        d = new char[]{34788, 39221, 47670, 56106, 64548, 7440, 15883, 24341, 28694, 37145, 45574, 54106, 62576, 5496, 13926, 22368, 26729, 35161, 11437, 12892, 4478, 28751, 22340, 46664, 38270, 62538, 56169, 14951, 6508, 30723, 24340, 48723, 40268, 64591, 49988, 8819, 297, 24629, 18216, 42540, 34258, 58586, 52171, 10905, 2445};
        a = -8974001815681420749L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(int r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 102
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r0 = o.av.b.$$d
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r6 = r6 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.av.b.q(int, byte, byte, java.lang.Object[]):void");
    }

    protected abstract boolean k();

    public b(Context context, InterfaceC0029b interfaceC0029b, c cVar, f fVar) {
        super(context, interfaceC0029b, cVar, e.m);
        this.b = fVar;
    }

    public final void m() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 43886), TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getScrollBarSize() >> 8) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        p((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), TextUtils.getCapsMode("", 0, 0) + 18, TextUtils.lastIndexOf("", '0', 0) + 28, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.b).toString());
        c();
        int i = e + 77;
        c = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void l() {
        int i = e + 53;
        c = i % 128;
        boolean z = i % 2 != 0;
        f();
        switch (z) {
            case true:
                throw null;
            default:
                int i2 = e + 23;
                c = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\av\b$a.smali */
    public static abstract class a<Command extends b> extends o.y.c<Command> {
        private static int c = 0;
        private static int b = 1;

        public a(Command command) {
            super(command, false);
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a() {
            int i = b + 41;
            c = i % 128;
            int i2 = i % 2;
            switch (((b) e()).k() ? '=' : '6') {
                case Opcodes.ISTORE /* 54 */:
                    break;
                default:
                    int i3 = b;
                    int i4 = (i3 ^ 21) + ((i3 & 21) << 1);
                    c = i4 % 128;
                    int i5 = i4 % 2;
                    o.b.c.e(((b) e()).g(), ((b) e()).e());
                    int i6 = (c + 96) - 1;
                    b = i6 % 128;
                    int i7 = i6 % 2;
                    break;
            }
            ((b) e()).d().a().e(((b) e()).b);
            int i8 = c;
            int i9 = (i8 ^ 63) + ((i8 & 63) << 1);
            b = i9 % 128;
            switch (i9 % 2 == 0 ? '\r' : (char) 20) {
                case 20:
                    return;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = b;
            int i2 = (i & 33) + (i | 33);
            c = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 22 : '/') {
                case '/':
                    return null;
                default:
                    int i3 = 79 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b;
            int i2 = ((i | 45) << 1) - (i ^ 45);
            c = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 29 : (char) 27) {
                case 27:
                    return null;
                default:
                    int i3 = 3 / 0;
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(d dVar) {
            int i = c + Opcodes.LUSHR;
            b = i % 128;
            int i2 = i % 2;
            ((b) e()).j().b(dVar, i());
            int i3 = c;
            int i4 = ((i3 | 65) << 1) - (i3 ^ 65);
            b = i4 % 128;
            switch (i4 % 2 == 0 ? (char) 20 : Typography.less) {
                case '<':
                    return;
                default:
                    int i5 = 5 / 0;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(d dVar) {
            int i = b;
            int i2 = (i & 23) + (i | 23);
            c = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    ((b) e()).j().c(dVar, i());
                    throw null;
                default:
                    ((b) e()).j().c(dVar, i());
                    return;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 998
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.av.b.p(char, int, int, java.lang.Object[]):void");
    }
}
