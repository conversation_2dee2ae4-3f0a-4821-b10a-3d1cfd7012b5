package o.db;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.k;
import o.b.d;
import o.de.f;
import o.e.a;
import o.ee.g;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\db\e.smali */
public final class e implements o.b.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        a = 1;
        a();
        Color.green(0);
        int i = e + 35;
        a = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void a() {
        c = -71705892134164723L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 4
            int r9 = 3 - r9
            int r8 = r8 * 2
            int r8 = 114 - r8
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r0 = o.db.e.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r8
            r5 = r2
            r8 = r7
            goto L2e
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            int r9 = r9 + 1
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r6
        L2e:
            int r3 = -r3
            int r7 = r7 + r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.e.g(short, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{21, -38, 51, PSSSigner.TRAILER_IMPLICIT};
        $$b = 31;
    }

    @Override // o.b.b
    public final d c(Context context, o.b.e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        f("ꇑ竗ួゑ쵒\ue64b茬射磠ᗵ⺦쭮\ue44f脳娔盵Ꮾⲡ즅\ue26a뽄堡瓫ᇑ⪺잘\ue052뵙嘽猂\u0fe8⣒얔鹪뭂", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 56099, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("ꇴ䥋炖᠑ͽ⫍툵ﵿ\ue4c1谹란廤䙠熐\u18f7Y⮺팍謹\ue58e赙둧忇䜯溑᧵Ĭ⣚펫\ufb0c", 59557 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar).toString());
        switch (eVar != o.b.e.c) {
            case false:
                int i = a + 79;
                e = i % 128;
                int i2 = i % 2;
                g.c();
                Object[] objArr3 = new Object[1];
                f("ꇑ竗ួゑ쵒\ue64b茬射磠ᗵ⺦쭮\ue44f脳娔盵Ꮾⲡ즅\ue26a뽄堡瓫ᇑ⪺잘\ue052뵙嘽猂\u0fe8⣒얔鹪뭂", 56099 - Drawable.resolveOpacity(0, 0), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("ꇴ〷艮ᑵ\ue68d磉쫽崓⼡腕ᎈ\ue5a0矰짴堯⩅뱺ຑ\ue0bb犦아坰⥌뮃ඩ鿀燲쀊刁②뚀ࣺ髜洇［兟⍯떥ދ馿桕悔䱤\ude9a낸ˎ锂朾籠䭻\udd91꾆Ǩ逜户\uf46d䚒\ud8a9\uaad3", (ViewConfiguration.getScrollBarSize() >> 8) + 37337, objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                return d.d;
            default:
                g.c();
                Object[] objArr5 = new Object[1];
                f("ꇑ竗ួゑ쵒\ue64b茬射磠ᗵ⺦쭮\ue44f脳娔盵Ꮾⲡ즅\ue26a뽄堡瓫ᇑ⪺잘\ue052뵙嘽猂\u0fe8⣒얔鹪뭂", 56099 - TextUtils.getOffsetAfter("", 0), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f("ꇴἡ\udc42鶓嫕᯿\ud931陕垑ᒳ헤錖偨ᅲ캣迓䴚ੇ쭷裠䦜݆쐠蕥䊙φ샾븼缙㲇ﶬ몬砓㥝\uf697런瓠㈕\uf353끽熳⻟\uec4b굵権⮟\ue8ccꧨ朤⑴\ue592ꋔ揸℩鹙忄Ჳ\udde5鬉塊ᥰ횪韛唉ሳ", 48847 - TextUtils.indexOf("", "", 0), objArr6);
                g.d(intern3, ((String) objArr6[0]).intern());
                d dVar = d.b;
                int i3 = a + 19;
                e = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return dVar;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    @Override // o.b.b
    public final f b(Context context) {
        int i = e + 7;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = i2 + 25;
        e = i4 % 128;
        boolean z = i4 % 2 == 0;
        Object obj = null;
        switch (z) {
            case true:
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    private static void f(String str, int i, Object[] objArr) {
        char[] charArray;
        int i2 = $10 + Opcodes.LSUB;
        $11 = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? 'V' : '?') {
            case Opcodes.SASTORE /* 86 */:
                obj.hashCode();
                throw null;
            default:
                switch (str == null) {
                    case true:
                        charArray = str;
                        break;
                    default:
                        charArray = str.toCharArray();
                        break;
                }
                char[] cArr = charArray;
                k kVar = new k();
                kVar.a = i;
                int length = cArr.length;
                long[] jArr = new long[length];
                kVar.b = 0;
                while (kVar.b < cArr.length) {
                    int i3 = kVar.b;
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr[kVar.b]), kVar, kVar};
                        Object obj2 = a.s.get(806930129);
                        if (obj2 == null) {
                            Class cls = (Class) a.c(AndroidCharacter.getMirror('0') - '&', (char) (60577 - (ViewConfiguration.getWindowTouchSlop() >> 8)), 354 - (ViewConfiguration.getScrollBarFadeDuration() >> 16));
                            byte b = (byte) 0;
                            byte b2 = b;
                            Object[] objArr3 = new Object[1];
                            g(b, b2, b2, objArr3);
                            obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE, Object.class, Object.class);
                            a.s.put(806930129, obj2);
                        }
                        jArr[i3] = ((Long) ((Method) obj2).invoke(null, objArr2)).longValue() ^ (c ^ (-5249873463433509232L));
                        try {
                            Object[] objArr4 = {kVar, kVar};
                            Object obj3 = a.s.get(-10300751);
                            if (obj3 == null) {
                                Class cls2 = (Class) a.c(13 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) (55185 - View.combineMeasuredStates(0, 0)), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 537);
                                byte b3 = (byte) 0;
                                byte b4 = (byte) (b3 + 1);
                                Object[] objArr5 = new Object[1];
                                g(b3, b4, (byte) (b4 - 1), objArr5);
                                obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                a.s.put(-10300751, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr4);
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                char[] cArr2 = new char[length];
                kVar.b = 0;
                while (kVar.b < cArr.length) {
                    cArr2[kVar.b] = (char) jArr[kVar.b];
                    try {
                        Object[] objArr6 = {kVar, kVar};
                        Object obj4 = a.s.get(-10300751);
                        if (obj4 == null) {
                            Class cls3 = (Class) a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 12, (char) (55185 - TextUtils.getCapsMode("", 0, 0)), TextUtils.getOffsetBefore("", 0) + 538);
                            byte b5 = (byte) 0;
                            byte b6 = (byte) (b5 + 1);
                            Object[] objArr7 = new Object[1];
                            g(b5, b6, (byte) (b6 - 1), objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            a.s.put(-10300751, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr6);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                String str2 = new String(cArr2);
                int i4 = $10 + 91;
                $11 = i4 % 128;
                int i5 = i4 % 2;
                objArr[0] = str2;
                return;
        }
    }
}
