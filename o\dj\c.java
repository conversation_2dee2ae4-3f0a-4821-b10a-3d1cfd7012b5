package o.dj;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\c.smali */
public final class c extends d {
    private static c b;
    private static int a = 0;
    private static int e = 1;

    @Override // o.dj.d
    public final /* bridge */ /* synthetic */ void e(Context context, List list) {
        int i = (e + 36) - 1;
        a = i % 128;
        int i2 = i % 2;
        super.e(context, list);
        int i3 = (a + 94) - 1;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    private c(Context context) {
        super(context);
    }

    public static synchronized c b(Context context) {
        c cVar;
        synchronized (c.class) {
            int i = a;
            int i2 = (i + 22) - 1;
            e = i2 % 128;
            int i3 = i2 % 2;
            switch (b == null ? '.' : '/') {
                case '/':
                    break;
                default:
                    int i4 = (i + Opcodes.IUSHR) - 1;
                    e = i4 % 128;
                    int i5 = i4 % 2;
                    c(context);
                    int i6 = a + 69;
                    e = i6 % 128;
                    int i7 = i6 % 2;
                    break;
            }
            cVar = b;
        }
        return cVar;
    }

    private static synchronized void c(Context context) {
        synchronized (c.class) {
            b = new c(context);
            int i = a + 25;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    break;
                default:
                    throw null;
            }
        }
    }
}
