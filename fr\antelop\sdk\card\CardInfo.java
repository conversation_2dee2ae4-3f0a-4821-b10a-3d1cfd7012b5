package fr.antelop.sdk.card;

import java.util.Date;
import o.eo.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\CardInfo.smali */
public final class CardInfo {
    private final c innerCardInfo;

    public CardInfo(c cVar) {
        this.innerCardInfo = cVar;
    }

    public final String getBin() {
        return this.innerCardInfo.c();
    }

    public final Date getExpiryDate() {
        return this.innerCardInfo.b();
    }

    public final String getLastDigits() {
        return this.innerCardInfo.a();
    }

    public final String getIssuerCardId() {
        return this.innerCardInfo.e();
    }

    public final CardDisplay getDisplay() {
        return this.innerCardInfo.d();
    }

    public final String toString() {
        return new StringBuilder("CardInfo{bin='").append(this.innerCardInfo.c()).append('\'').append(", expiryDate='").append(this.innerCardInfo.b()).append('\'').append(", lastDigits=").append(this.innerCardInfo.a()).append('\'').append(", display=").append(this.innerCardInfo.d()).append('\'').append('}').toString();
    }
}
