package bc.org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q4;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\a.smali */
public abstract class a implements q4, m5 {
    protected final q1 a;
    private final byte[] b;
    private int c;
    private long d;

    protected a(q1 q1Var) {
        this.b = new byte[4];
        this.a = q1Var;
        this.c = 0;
    }

    protected abstract void a();

    protected abstract void a(long j);

    protected void a(a aVar) {
        byte[] bArr = aVar.b;
        System.arraycopy(bArr, 0, this.b, 0, bArr.length);
        this.c = aVar.c;
        this.d = aVar.d;
    }

    protected abstract void a(byte[] bArr, int i);

    public void finish() {
        long j = this.d << 3;
        update(ByteCompanionObject.MIN_VALUE);
        while (this.c != 0) {
            update((byte) 0);
        }
        a(j);
        a();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.q4
    public int getByteLength() {
        return 64;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void reset() {
        this.d = 0L;
        this.c = 0;
        int i = 0;
        while (true) {
            byte[] bArr = this.b;
            if (i >= bArr.length) {
                return;
            }
            bArr[i] = 0;
            i++;
        }
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void update(byte b) {
        byte[] bArr = this.b;
        int i = this.c;
        int i2 = i + 1;
        this.c = i2;
        bArr[i] = b;
        if (i2 == bArr.length) {
            a(bArr, 0);
            this.c = 0;
        }
        this.d++;
    }

    protected void a(byte[] bArr) {
        System.arraycopy(this.b, 0, bArr, 0, this.c);
        j6.a(this.c, bArr, 4);
        j6.a(this.d, bArr, 8);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void update(byte[] bArr, int i, int i2) {
        int i3 = 0;
        int max = Math.max(0, i2);
        if (this.c != 0) {
            int i4 = 0;
            while (true) {
                if (i4 >= max) {
                    i3 = i4;
                    break;
                }
                byte[] bArr2 = this.b;
                int i5 = this.c;
                int i6 = i5 + 1;
                this.c = i6;
                int i7 = i4 + 1;
                bArr2[i5] = bArr[i4 + i];
                if (i6 != 4) {
                    i4 = i7;
                } else {
                    a(bArr2, 0);
                    this.c = 0;
                    i3 = i7;
                    break;
                }
            }
        }
        int i8 = max - 3;
        while (i3 < i8) {
            a(bArr, i + i3);
            i3 += 4;
        }
        while (i3 < max) {
            byte[] bArr3 = this.b;
            int i9 = this.c;
            this.c = i9 + 1;
            bArr3[i9] = bArr[i3 + i];
            i3++;
        }
        this.d += max;
    }

    protected a(a aVar) {
        this.b = new byte[4];
        this.a = aVar.a;
        a(aVar);
    }

    protected a(byte[] bArr) {
        byte[] bArr2 = new byte[4];
        this.b = bArr2;
        this.a = q1.values()[bArr[bArr.length - 1]];
        System.arraycopy(bArr, 0, bArr2, 0, 4);
        this.c = j6.a(bArr, 4);
        this.d = j6.b(bArr, 8);
    }
}
