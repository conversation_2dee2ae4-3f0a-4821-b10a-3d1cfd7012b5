package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\GetPaymentCredentialsResponse.smali */
public class GetPaymentCredentialsResponse extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GetPaymentCredentialsResponse> CREATOR = new zzd();
    final byte[] zza;
    final byte[] zzb;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\GetPaymentCredentialsResponse$Builder.smali */
    public static class Builder {
        byte[] zza;
        byte[] zzb;

        public GetPaymentCredentialsResponse build() {
            return new GetPaymentCredentialsResponse(this.zza, this.zzb);
        }

        public Builder setGoogleOpaquePaymentCard(byte[] bArr) {
            this.zzb = bArr;
            return this;
        }

        public Builder setOpaquePaymentCard(byte[] bArr) {
            this.zza = bArr;
            return this;
        }
    }

    GetPaymentCredentialsResponse(byte[] bArr, byte[] bArr2) {
        this.zza = bArr;
        this.zzb = bArr2;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeByteArray(dest, 1, this.zza, false);
        SafeParcelWriter.writeByteArray(dest, 2, this.zzb, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
