package fr.antelop.sdk.authentication;

import fr.antelop.sdk.AntelopError;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomCustomerAuthenticatedProcessCallback.smali */
public interface CustomCustomerAuthenticatedProcessCallback {
    void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onCustomerCredentialsInvalid(LocalAuthenticationErrorReason localAuthenticationErrorReason, CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onCustomerCredentialsRequired(List<CustomerAuthenticationMethod> list, CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess);

    void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess);
}
