package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\w.smali */
public abstract class w {
    public static String a() {
        byte[] bArr = new byte[15];
        int[] iArr = {227, 204, Opcodes.IFNULL, 208, 205, 203, Opcodes.IFNULL, 233, Opcodes.IFNONNULL, 219, 241, 214, 205, 208, Opcodes.IFNONNULL};
        for (int i = 0; i < 15; i++) {
            bArr[i] = (byte) (((iArr[i] ^ Opcodes.IF_ICMPGE) - i) + i);
        }
        return new String(bArr);
    }

    public static String b() {
        byte[] bArr = new byte[2];
        int[] iArr = {2, 231};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) (iArr[i] - 64);
        }
        return new String(bArr);
    }

    public static String c() {
        byte[] bArr = new byte[2];
        int[] iArr = {Opcodes.IAND, 100};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) (iArr[i] - 188);
        }
        return new String(bArr);
    }
}
