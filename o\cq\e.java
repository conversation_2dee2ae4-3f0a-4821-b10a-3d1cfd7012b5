package o.cq;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.e.a;
import o.ei.i;
import o.et.h;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cq\e.smali */
public abstract class e<T extends h> extends d<T> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static char[] b;
    private static int c;
    private static char d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        e();
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getMaximumFlingVelocity();
        int i = e + Opcodes.DREM;
        c = i % 128;
        switch (i % 2 == 0 ? (char) 0 : '=') {
            case 0:
                int i2 = 87 / 0;
                break;
        }
    }

    static void e() {
        b = new char[]{50943, 50849, 50851, 50839, 50835, 50877, 50851, 50852, 50853, 50877, 50849, 50852, 50835, 50827, 50783, 50778, 50775, 50782, 50758, 50777, 50772, 50780, 50776, 50769, 50759, 50764, 50779, 50769, 50783, 50783, 50775, 50760, 50870, 50782, 50782, 50785, 50773, 50800, 50700, 50787, 50786, 50804, 50753, 50811, 50803, 50943, 50843, 50843, 50854, 50852, 50847, 50846, 50857, 50850, 50876, 50851, 50851, 50853, 50853, 50859, 50935, 50855, 50858, 50850, 50877, 50855, 50830, 50831, 50859, 50855, 50859, 50854, 50879, 50854, 50825, 50826, 50858, 50829, 50820, 50855, 50857, 50825, 50825, 50851, 50848, 50855, 50852, 50853, 50827, 50829, 50855, 50855, 50863, 50831, 50831, 50857, 50849, 50855, 50859, 50853, 50854, 50820, 50827, 50848, 50852, 50862, 50828, 50831, 50859, 50855, 50859, 50854, 50879, 50854, 50879, 50732, 50708, 50709, 50710, 50714, 50709, 50705, 50814, 50815, 50708, 50714, 50708, 50712, 50688, 50716, 50712, 50708, 50710, 50718, 50790, 50784, 50716, 50712, 50716, 50715, 50704, 50715, 50933, 50853, 50852, 50879, 50849, 50846, 50843, 50877, 50849, 50854, 50877, 50858, 50855, 50849, 50831, 50923, 50923, 50826, 50851, 50876, 50823, 50829, 50859, 50849, 50827, 50823, 50851, 50859, 50831, 50830, 50858, 50858, 50853, 50853, 50854, 50820, 50826, 50855, 50830, 50823, 50851, 50859, 50831, 50829, 50859, 50877, 50849, 50854, 50877, 50858, 50855, 50849, 50831, 50829, 50853, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50829, 50852, 50854, 50831, 50820, 50849, 50849, 50845, 50923, 50829, 50859, 50877, 50849, 50854, 50877, 50858, 50855, 50849, 50831, 50829, 50853, 50877, 50851, 50855, 50859, 50863, 50855, 50851, 50849, 50851, 50826, 50828, 50863, 50858, 50851, 50851, 50876, 50823, 50828, 50862, 50831, 50828, 50854, 50850, 50855, 50877, 50823, 50782, 50759, 50752, 50754, 50874, 50865, 50780, 50777, 50752, 50760, 50755, 50782, 50758, 50754, 50779, 50761, 50870, 50781, 50779, 50753, 50753, 50777, 50866, 50872, 50752, 50752, 50761, 50756, 50782, 50754};
        a = new char[]{30537, 30566, 30567, 30574, 30563, 30585, 30536, 30559, 30587, 30588, 30538, 30511, 30542, 30526, 30581, 30570, 30540, 30589, 30530, 30568, 30498, 30586, 30569, 30560, 30572, 30562, 30557, 30539, 30591, 30531, 30561, 30499, 30571, 30582, 30564, 30573};
        d = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = 3 - r8
            byte[] r0 = o.cq.e.$$a
            int r9 = r9 * 3
            int r9 = 1 - r9
            int r7 = r7 + 66
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L31
        L17:
            r3 = r2
        L18:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            int r8 = r8 + 1
            if (r4 != r9) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L31:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.e.h(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -112, 34, 95};
        $$b = 252;
    }

    public abstract T a(String str, String str2, int i, String str3);

    @Override // o.cc.e
    public final /* synthetic */ o.el.d d(String str, String str2, int i, String str3) {
        int i2 = c + Opcodes.DDIV;
        e = i2 % 128;
        char c2 = i2 % 2 != 0 ? 'C' : '=';
        T e2 = e(str, str2, i, str3);
        switch (c2) {
            default:
                int i3 = 35 / 0;
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return e2;
        }
    }

    @Override // o.cq.d
    public final T c(String str, String str2, int i, String str3, o.eg.b bVar) throws o.eg.d, i {
        int intValue;
        int i2 = e + 83;
        c = i2 % 128;
        int i3 = i2 % 2;
        T a2 = a(str, str2, i, str3);
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{0, 13, 0, 7}, true, objArr);
        o.eg.b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        f("\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{13, 22, 41, 0}, false, objArr2);
        o.eg.b v2 = v.v(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(22 - Color.red(0), "\u001b\u0000\u000e#\u0019\u0002\n!\u0015\u0000!\u0000\u0015\r\u001b\r \u0006!\t\t\u0002", (byte) (19 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), objArr3);
        o.eg.b v3 = v.v(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        g(3 - TextUtils.indexOf("", "", 0), "\u0004\u0002㘹", (byte) (59 - (Process.myTid() >> 22)), objArr4);
        a2.a(v2.B(((String) objArr4[0]).intern()));
        Object[] objArr5 = new Object[1];
        g(10 - (ViewConfiguration.getTouchSlop() >> 8), "\u001b\u0004\u001f\u001b\f!\u0006\u0002\u0019\u0000", (byte) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + Opcodes.FMUL), objArr5);
        a2.e(v2.B(((String) objArr5[0]).intern()));
        Object[] objArr6 = new Object[1];
        g(TextUtils.getOffsetAfter("", 0) + 11, "\u0016\u0019\u0014\u001d\u0015\u000f\u001d\u0016!\u0006㙈", (byte) (74 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), objArr6);
        a2.c(v2.B(((String) objArr6[0]).intern()));
        Object[] objArr7 = new Object[1];
        f(null, new int[]{35, 10, 75, 5}, true, objArr7);
        a2.h(v2.z(((String) objArr7[0]).intern()));
        Object[] objArr8 = new Object[1];
        f("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001", new int[]{45, 15, 0, 0}, true, objArr8);
        a2.i(bVar.r(((String) objArr8[0]).intern()));
        Object[] objArr9 = new Object[1];
        g(14 - ((Process.getThreadPriority(0) + 20) >> 6), "\u001d\f\u001c\r\u0012\u0016 \u0006\u000f#\u0011\u0016!\u000e", (byte) ((KeyEvent.getMaxKeyCode() >> 16) + 13), objArr9);
        a2.i(v3.B(((String) objArr9[0]).intern()));
        byte[] j = a2.j();
        Object[] objArr10 = new Object[1];
        g(8 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "㘃㘃\u000f\u0015\u0006\u001e㘌", (byte) (25 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), objArr10);
        a2.b(o.dk.e.a(j, v2.B(((String) objArr10[0]).intern())));
        Object[] objArr11 = new Object[1];
        g(23 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "\u001a\u001e\u0016\u0005\u000e\u0019\u0010\u0003\u0002\t\u000e!!\t\t\u0002\u001b\u0011\u001f\u0012\u000e\b", (byte) (View.combineMeasuredStates(0, 0) + 54), objArr11);
        Object e2 = v2.e(((String) objArr11[0]).intern());
        switch (e2 instanceof String) {
            case false:
                switch (e2 instanceof Integer ? '3' : '9') {
                    case '9':
                        Object[] objArr12 = new Object[1];
                        f("\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{60, 54, 0, 0}, true, objArr12);
                        throw new i(((String) objArr12[0]).intern());
                    default:
                        int i4 = e + 95;
                        c = i4 % 128;
                        boolean z = i4 % 2 != 0;
                        intValue = ((Integer) e2).intValue();
                        switch (z) {
                            case true:
                                break;
                            default:
                                int i5 = 60 / 0;
                                break;
                        }
                }
            default:
                try {
                    intValue = Integer.valueOf((String) e2, 16).intValue();
                    break;
                } catch (NumberFormatException e3) {
                    Object[] objArr13 = new Object[1];
                    g(View.combineMeasuredStates(0, 0) + 50, "\u0000\u001f\u0000\u0004\u0005\u0002#\b\u001a\u001e\u0016\u0005\u0011\u0017\u0010\u0003\u0002\t\u000e!\b#\u0002\t\u0005\t\u0003\u0010\u001f\u0012\u000e\b\u0011\u000b\u0004\u0005\u001b\u0015\t\u0005\n\u0006\u0005\t\n\t\r\u0005\u001f\u0012", (byte) (84 - ImageFormat.getBitsPerPixel(0)), objArr13);
                    throw new i(((String) objArr13[0]).intern());
                }
        }
        Object[] objArr14 = new Object[1];
        g(21 - View.resolveSize(0, 0), "\u0003\u0007\u000f\u001b\u0010\f\u0010\u0018\"\n\u0000\u0019\u0002\t\u0005\u0013!\u0018\u0002\t㙐", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 85), objArr14);
        byte[] B = v2.B(((String) objArr14[0]).intern());
        a2.d(intValue);
        a2.j(B);
        short d2 = o.dk.e.d(a2.j(), a2.B());
        if (d2 != 0) {
            a2.e(d2);
            return a2;
        }
        Object[] objArr15 = new Object[1];
        f("\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{114, 28, Opcodes.DMUL, 0}, true, objArr15);
        throw new i(((String) objArr15[0]).intern());
    }

    /* JADX WARN: Removed duplicated region for block: B:14:0x00d3  */
    /* JADX WARN: Removed duplicated region for block: B:24:0x0108 A[ADDED_TO_REGION] */
    /* JADX WARN: Removed duplicated region for block: B:31:0x0202 A[ADDED_TO_REGION] */
    @Override // o.cq.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final T b(o.et.h r14, o.eg.b r15, int r16, java.lang.String r17, o.eg.b r18) throws o.eg.d, o.ei.i {
        /*
            Method dump skipped, instructions count: 598
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.e.b(o.et.h, o.eg.b, int, java.lang.String, o.eg.b):o.et.h");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0028. Please report as an issue. */
    @Override // o.cq.d
    public final boolean b(int i, o.eg.b bVar) throws o.eg.d {
        int i2 = c;
        int i3 = i2 + 51;
        e = i3 % 128;
        if (i3 % 2 != 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (i != 0 ? 'A' : '`') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                int i4 = i2 + Opcodes.LSUB;
                e = i4 % 128;
                switch (i4 % 2 == 0 ? (char) 15 : (char) 22) {
                }
                return false;
            default:
                Object[] objArr = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{0, 13, 0, 7}, true, objArr);
                o.eg.b v = bVar.v(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                f("\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{13, 22, 41, 0}, false, objArr2);
                o.eg.b v2 = v.v(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{253, 31, 35, 27}, false, objArr3);
                switch (v2.u(((String) objArr3[0]).intern()) != null) {
                    case false:
                        return false;
                    default:
                        int i5 = e + 27;
                        c = i5 % 128;
                        int i6 = i5 % 2;
                        return true;
                }
        }
    }

    @Override // o.cq.d
    public final o.eg.b c(int i, o.eg.b bVar) throws o.eg.d {
        switch (i != 0) {
            case true:
                int i2 = e + 39;
                c = i2 % 128;
                switch (i2 % 2 == 0 ? '-' : '5') {
                    case Opcodes.SALOAD /* 53 */:
                        return null;
                    default:
                        int i3 = 84 / 0;
                        return null;
                }
            default:
                Object[] objArr = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{0, 13, 0, 7}, true, objArr);
                o.eg.b v = bVar.v(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                f("\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{13, 22, 41, 0}, false, objArr2);
                o.eg.b v2 = v.v(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{253, 31, 35, 27}, false, objArr3);
                o.eg.b v3 = v2.v(((String) objArr3[0]).intern());
                int i4 = e + 29;
                c = i4 % 128;
                int i5 = i4 % 2;
                return v3;
        }
    }

    @Override // o.cq.d
    public final o.eg.e b(o.eg.b bVar) throws o.eg.d {
        int i = e + 3;
        c = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{0, 13, 0, 7}, true, objArr);
        o.eg.b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        f("\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{13, 22, 41, 0}, false, objArr2);
        o.eg.b v2 = v.v(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(KeyEvent.keyCodeFromString("") + 7, "\f\u0010\u001d\u0012\u000e#㘼", (byte) (View.getDefaultSize(0, 0) + 83), objArr3);
        o.eg.e s = v2.s(((String) objArr3[0]).intern());
        int i3 = c + 15;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 52 / 0;
                return s;
            default:
                return s;
        }
    }

    private T e(String str, String str2, int i, String str3) {
        int i2 = c + 29;
        e = i2 % 128;
        boolean z = i2 % 2 == 0;
        T a2 = a(str, str2, i, str3);
        switch (z) {
            case false:
                int i3 = 11 / 0;
                break;
        }
        int i4 = e + 11;
        c = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 25 : (char) 24) {
            case 24:
                return a2;
            default:
                int i5 = 33 / 0;
                return a2;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    @Override // o.cq.d
    public final boolean a() {
        int i = c;
        int i2 = i + 109;
        e = i2 % 128;
        switch (i2 % 2 != 0) {
        }
        int i3 = i + Opcodes.LNEG;
        e = i3 % 128;
        int i4 = i3 % 2;
        return false;
    }

    private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
        int i;
        char[] cArr;
        String str2 = str;
        int i2 = $10 + 73;
        int i3 = i2 % 128;
        $11 = i3;
        Object obj = null;
        if (i2 % 2 == 0) {
            Object obj2 = null;
            obj2.hashCode();
            throw null;
        }
        int i4 = 0;
        byte[] bArr = str2;
        if (str2 != null) {
            int i5 = i3 + 79;
            $10 = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    obj.hashCode();
                    throw null;
                default:
                    bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
            }
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i6 = iArr[0];
        int i7 = iArr[1];
        int i8 = iArr[2];
        int i9 = iArr[3];
        char[] cArr2 = b;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i10 = 0;
            while (true) {
                switch (i10 < length ? 1 : i4) {
                    case 0:
                        cArr2 = cArr3;
                        break;
                    default:
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i4] = Integer.valueOf(cArr2[i10]);
                            Object obj3 = a.s.get(1951085128);
                            if (obj3 != null) {
                                cArr = cArr2;
                            } else {
                                Class cls = (Class) a.c(11 - View.MeasureSpec.getSize(i4), (char) (ViewConfiguration.getJumpTapTimeout() >> 16), 43 - (ViewConfiguration.getTouchSlop() >> 8));
                                byte b2 = (byte) i4;
                                cArr = cArr2;
                                Object[] objArr3 = new Object[1];
                                h((byte) 54, b2, b2, objArr3);
                                obj3 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                a.s.put(1951085128, obj3);
                            }
                            cArr3[i10] = ((Character) ((Method) obj3).invoke(null, objArr2)).charValue();
                            i10++;
                            cArr2 = cArr;
                            i4 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        char[] cArr4 = new char[i7];
        System.arraycopy(cArr2, i6, cArr4, 0, i7);
        switch (bArr2 != null ? 'b' : 'N') {
            case 'N':
                break;
            default:
                char[] cArr5 = new char[i7];
                lVar.d = 0;
                char c2 = 0;
                while (true) {
                    switch (lVar.d >= i7) {
                        case false:
                            if (bArr2[lVar.d] == 1) {
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                    Object obj4 = a.s.get(2016040108);
                                    if (obj4 == null) {
                                        Class cls2 = (Class) a.c((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 10, (char) (Process.myPid() >> 22), Gravity.getAbsoluteGravity(0, 0) + 448);
                                        byte b3 = (byte) 0;
                                        Object[] objArr5 = new Object[1];
                                        h((byte) 53, b3, b3, objArr5);
                                        obj4 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(2016040108, obj4);
                                    }
                                    cArr5[i11] = ((Character) ((Method) obj4).invoke(null, objArr4)).charValue();
                                    int i12 = $10 + 13;
                                    $11 = i12 % 128;
                                    int i13 = i12 % 2;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            } else {
                                int i14 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                    Object obj5 = a.s.get(804049217);
                                    if (obj5 == null) {
                                        Class cls3 = (Class) a.c(10 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 208);
                                        byte b4 = (byte) 0;
                                        Object[] objArr7 = new Object[1];
                                        h((byte) ($$b & 58), b4, b4, objArr7);
                                        obj5 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(804049217, obj5);
                                    }
                                    cArr5[i14] = ((Character) ((Method) obj5).invoke(null, objArr6)).charValue();
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            c2 = cArr5[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj6 = a.s.get(-2112603350);
                                if (obj6 == null) {
                                    Class cls4 = (Class) a.c(11 - ExpandableListView.getPackedPositionType(0L), (char) (Process.myPid() >> 22), ExpandableListView.getPackedPositionChild(0L) + 260);
                                    byte b5 = (byte) 0;
                                    byte b6 = b5;
                                    Object[] objArr9 = new Object[1];
                                    h(b5, b6, b6, objArr9);
                                    obj6 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    a.s.put(-2112603350, obj6);
                                }
                                ((Method) obj6).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        default:
                            cArr4 = cArr5;
                            break;
                    }
                }
        }
        if (i9 > 0) {
            int i15 = $10 + 41;
            $11 = i15 % 128;
            int i16 = i15 % 2;
            char[] cArr6 = new char[i7];
            i = 0;
            System.arraycopy(cArr4, 0, cArr6, 0, i7);
            int i17 = i7 - i9;
            System.arraycopy(cArr6, 0, cArr4, i17, i9);
            System.arraycopy(cArr6, i9, cArr4, 0, i17);
        } else {
            i = 0;
        }
        if (z) {
            char[] cArr7 = new char[i7];
            while (true) {
                lVar.d = i;
                switch (lVar.d < i7 ? '#' : (char) 21) {
                    case 21:
                        cArr4 = cArr7;
                        break;
                    default:
                        int i18 = $10 + 93;
                        $11 = i18 % 128;
                        if (i18 % 2 == 0) {
                        }
                        cArr7[lVar.d] = cArr4[(i7 - lVar.d) - 1];
                        i = lVar.d + 1;
                }
            }
        }
        if (i8 > 0) {
            lVar.d = 0;
            while (lVar.d < i7) {
                cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                lVar.d++;
            }
        }
        objArr[0] = new String(cArr4);
    }

    /* JADX WARN: Code restructure failed: missing block: B:111:0x001a, code lost:
    
        r1 = r24.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x0018, code lost:
    
        if (r24 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0012, code lost:
    
        if (r24 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001f, code lost:
    
        r1 = r24;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r23, java.lang.String r24, byte r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 1008
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cq.e.g(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
