package o.ek;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import kotlin.text.Typography;
import o.ee.g;
import o.eg.d;
import o.ek.a;
import o.ek.e;
import o.eo.h;
import o.et.i;
import o.fk.c;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int g;
    private static long h;
    private static int i;
    private final HashMap<String, e> c = new HashMap<>();
    private final HashMap<String, o.ek.a> e = new HashMap<>();
    private final LinkedHashMap<String, o.eo.e> d = new LinkedHashMap<>();
    private final LinkedHashMap<String, o.eo.e> b = new LinkedHashMap<>();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        j();
        ViewConfiguration.getFadingEdgeLength();
        KeyEvent.keyCodeFromString("");
        ViewConfiguration.getMinimumFlingVelocity();
        KeyEvent.getMaxKeyCode();
        Color.red(0);
        Color.red(0);
        ViewConfiguration.getScrollFriction();
        ViewConfiguration.getMaximumDrawingCacheSize();
        TextUtils.indexOf("", "", 0, 0);
        Color.green(0);
        KeyEvent.getDeadChar(0, 0);
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getZoomControlsTimeout();
        ViewConfiguration.getLongPressTimeout();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        View.resolveSize(0, 0);
        Drawable.resolveOpacity(0, 0);
        ViewConfiguration.getZoomControlsTimeout();
        Color.blue(0);
        View.MeasureSpec.getMode(0);
        TextUtils.indexOf((CharSequence) "", '0');
        int i2 = i + 9;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void init$0() {
        $$a = new byte[]{31, 68, -4, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
        $$b = Opcodes.IF_ICMPGE;
    }

    static void j() {
        char[] cArr = new char[1979];
        ByteBuffer.wrap("\ts»?m¦\u001e'À©u.'\u0095è\f\u009a\u0095O\u000eñÄ¢{Tõ\u0019dËÍ|v.ðÓ\u001a\u0085Ý6@øÎ\u00adM_|\u0001·²\"d¸)%Û\u0087\u008cX>\u009bã\u001f\u0095ÆF\u000e\bï½xoâ\u0010$Â¢w_9×ê^\u009cÄAKó*¥øV;\u0018¢Í/\u007f´ \u0013Ò\u0095\u0087\u0002I\u0081ú\u0018¬ùQ6\u0003ó´kfö+ZÝ\u0098\u008e_0ÍåH\u0097ÁY½\n1¼¼Í\u008e\u007fü©i,ª\u009eÒHO;Ã<n\u008e\u0005X\u0093+\tõ\u009b,\u008a\u009eÒHO;ÃålPÊ\u0002{Íþ¿~jæÔ\u007f\u0017\t¥tsý\u0000zÞákc9Ðö_\u0084ÊQOïá¼3J®\u0007\u0013Õ¼b60³ÍZ\u009bÙ(Næ\u008e³\u0010As\u001fû¬0zà7wÅÑ\u0092V \u0092ý\u0016\u008b\u0086,\u008d\u009eÆHM;ËåHPÈ\u0002tÍë¿|jçÔ-\u0087\u0092q\u001c<\u008dîEY\u008e\u000b\u0019ö£ 1\u0013®Ý\"\u0088ªzÁ$V\u0097ÖAM\f\u008dþ~©õ\u001b;Æà°acê-\u001c\u0098\u0088J\t5\u0095ç\u000eR§\u001c:Ï½Ë\u009byæ¯oÜè\u0002s·ñåB*ÍXX\u008dÝ3u`\u00ad\u00968Û¤\t-¾ôì\u007f\u0011ÈG\u0000ô\u008e:\u0015o\u009d\u009d®Ãnpñ¦wëø\u0019\fN\u0090ü\u0000<-\u008efXí+kõè@h\u0012ÔÝK¯ÜzGÄ\u008d\u00974a°,)þ¡Io\u001b æ\u0017°Ý\u0003\u0002Í\u008f\u0098\bjz4ê\u0087wQ÷\u001chîÅ¹T\u000bß,ª\u009eÒHO;ÃåhPÏ, \u009eÀHN;ÒåDPÙ\u0002VÍþ¿kjçÔD\u0087\u0093,¹\u009eÁHR;ÃåTPÈ\u0002a,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔ~\u009c1.NøÔ\u008bKUØàC²Û}s\u000fæÚjdó7@ÁÜ\u0002¹°Æf\\\u0015ÃËP~Ë,Sãû\u0091nDâú{©Ò_Y\u0012ÞÀ#w\u008b%\u001eØ²\u008ex,é\u009eÛH\\;Ôå\u0001PÉ\u0002pÍú¿wj£Ôx\u0087\u0087q\u0015<\u009aî\u0011Y\u008a\u000b\r,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔ~\u0087×q\\<Ûî&Y\u008e\u000b\u001bö· }\u0013\u008eÝ/\u0088\u00adzÚ$\u001f,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔ~\u0087×q\\<Ûî&Y\u008e\u000b\u001bö· }\u0013\u0086Ý\"\u0088¨zÚ$J\u0097×AW\f\u008d,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔ~\u0087×q\\<Ûî\u0006Y\u009a\u000b\u001bö¡ 8\u0013©Ý5\u0088\u0088zÔ$M\u0097ÝA\r\fÊþr©å\u001bOÆà°}cä-\u0000\u0098¼J\t5\u0085ç(Rº\u001c1Ï½¹*d¹ÖÞ\u0080^sÕ=Vè\u008fZ\u0013\u0005³\u008fë=\u008aë\u001d\u0098\u0087,é\u009e\u009cH\u001d;Äå@PÙ\u0002qÍ±¿~jæÔy\u0087£q\u0014<\u0089î\bY\u009c\u000b(ö½ 9\u0013\u0084Ý.\u0088¥zÑ$V\u0097ÍAJ\fÂþy©â\u001b;Æ¿°/,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔ~\u0087×q\\<Ûî\u0011Y\u008a\u000b\u001bö¾ .\u0013\u0086Ý/\u0088¯zö$P\u0097×AG\fÄþc©ø\u001btÆë°|cÏ-\u001f\u0098\u009cJ\u00005ÁçQRõ\u001c\u001cÏ±¹\"d£ÖÐ\u0080Tsß=qèÀZh\u0005ã÷m¢õlnßý\u0089\u0014t\u0093&+\u0091\u0086C\u001c\u000e¢ø8«©\u0015 À«²i>\u0080\u008cÿZe)ú÷iBò\u0010jßÂ\u00adWxÛÆB\u0095ëc`.çü-K¶\u0019'ä\u0082²\u0012\u0001ºÏ\u0013\u009a\u0093hÊ6l\u0085ëS{\u001eøì_»Ä\tHÔ×¢@qó?#\u008a X<'ýõm@É\u000e Ý\u008d«\u001ev\u009fÄì\u0092haã/MúüHC\u0017ÎåM°Ò~YÍÖ\u009b=f¦4!1Ù\u0083¦U<&£ø0M«\u001f3Ð\u009b¢\u000ew\u0082É\u001b\u009a²l9!¾ótDï\u0016~ëÛ½K\u000eãÀJ\u0095Êg\u009395\u008a²\\\"\u0011¡ã\u0006´\u009d\u0006\u0011Û\u008e\u00ad\u0019~ª0z\u0085ùWe(¤ú4O\u0090\u0001yÒÔ¤GyÆËµ\u009d1nº \u0014õ¥G\r\u0018\u0086ê\b¿\u0090q\u000bÂ\u0098\u0094qiö;N\u008cã^y\u0013Çå]¶Ì\bEÝÎ,»\u009eÖH\\;ÃåbPÊ\u0002gÍû¿jj£Ô \u0087×q$<\u0095î\u0004Y\u008d\u000b\u0005ö¶ }\u0013³Ý.\u0088ëzÖ$^\u0097ÚAK\fÈþ7©ò\u001bzÆ÷°kc©-\u001f\u0098\u009cJ\u001e5\u008eç\u001eR¡,»\u009eÖH\\;ÃåbPÊ\u0002gÍû¿jj£Ô \u0087×q$<\u0095î\u0004Y\u008d\u000b\u0005ö¶ }\u0013³Ý.\u0088ëzÖ$^\u0097ÚAK\fÈþ7©Å\u001b=ÆÆ,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔZ\u0087\u009eq\u0005<\u0093î&Y\u008e\u000b\u001bö· \u0010\u0013¦Ý/\u0088ªzÒ$Z\u0097ÔAF\fÃþc©Ø\u001buÆã°`c©-^\u0098ÝJ.5\u008fç\u0018R°\u001c-Ï\u00ad¹cd®ÖÂ\u0080CsÉ=@èÁZ]\u0005³÷n¢ól`ßÿ\u0089\u0010\u0081[3$å¾\u0096!H²ý)¯±`\u0019\u0012\u008cÇ\u0000y½*yÜâ\u0091tCÁôi¦ü[P\r÷¾ApÈ%M×5\u0089½:3ì¡¡$S\u0084\u0004?¶\u0092k\u0004\u001d\u0087ÎN\u0080¹5:çÍ\u0098gJâÿS±ßb[\u0014ÉÉO{>-¢Þ|#5\u0091JGÐ4OêÜ_G\rßÂw°âenÛÓ\u0088\u0017~\u008c3\u001aá¯V\u0007\u0004\u0092ù>¯\u0099\u001c/Ò¦\u0087#u[+Ó\u0098]NÏ\u0003Jñê¦Q\u0014üÉj¿él \"×\u0097TE¡:\u001eè\u0087].\u0013¤À9¶®k!Ù\u001e\u008fì|W2ÞçKUÓ\n[øú\u00adjcËÐm\u0086\u0092{\u0012)\u0099\u009e\u001eL\u008d\u00011÷¶¤!\u001a\u009fÏ2½¡sÎ A\u0096ÝK\b9\u008fî<ö)DV\u0092ÌáS?À\u008a[ØÃ\u0017keþ°r\u000eÏ]\u000b«\u0090æ\u00064³\u0083\u001bÑ\u008e,\"z\u0085É3\u0007ºR? GþÏMA\u009bÓÖV$ösMÁà\u001cvjõ¹<÷ËBH\u0090½ï\u0002=\u009b\u00882Æ¸\u0015%c²¾=\f\u0002Zá©Mçß2W\u0080ïßr-éxf¶ý\u0005}S´®\u0005ü\u0087K\u0013\u0099\u0096Ôb\"·q+Ï¤\u001a\th¨¦Çõ\\CÇ\u009eGì\u009e;-\u0089ª,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔZ\u0087\u009eq\u0005<\u0093î&Y\u008e\u000b\u001bö· \u0010\u0013¦Ý/\u0088ªzÒ$Z\u0097ÔAF\fÃþc©Ø\u001buÆã°`c©-^\u0098ÝJ(5\u0097ç\u000eR§\u001c-Ï°¹'d¨Ö\u0097\u0080psØ=FèÀZ\\\u0005ý÷i¢§lhßå\u0089\u0013t\u0090&Y\u0091\u0090C\b\u000e£ø\u0019«º\u00156À\u008a²*|\\/Ð\u0099tDÕ6JááSv\u001eúÈW»âe\u001cÐ\u0094\u0082\u0015MÅ?BêÉT5\u0007¼ñ+¼²n.,¼\u009eÃHY;ÆåUPÎ\u0002VÍþ¿kjçÔZ\u0087\u009eq\u0005<\u0093î&Y\u008e\u000b\u001bö· \u0010\u0013¦Ý/\u0088ªzÒ$Z\u0097ÔAF\fÃþc©Ø\u001buÆã°`c©-^\u0098ÝJ\"5\u0082ç\u0004R¸\u001c\fÏ\u00ad¹\"d¹ÖÞ\u0080Rsï=JèÄZL\u0005ý÷=¢ãldßÿ\u0089\u0010t\u009c&\r\u0091\u0086C\t\u000eûøq«®\u00155À«²(|G/ØûAI>\u009f¤ì;2¨\u00873Õ«\u001a\u0003h\u0096½\u001a\u0003§Pc¦øën9Û\u008esÜæ!JwíÄ[\nÒ_W\u00ad/ó§@)\u0096»Û>)\u009e~%Ì\u0088\u0011\u001eg\u009d´Tú£O \u009dÔâs0¶\u0085mËÁ\u0018KnÓ³c\u0001>W\u00ad¤2ê±?1\u008d\u0080Ò\u0001 \u008bu\u001f»\u0092,»\u009eÖH\\;ÃåbPÊ\u0002gÍû¿jj£Ô \u0087×q\u0014<\u0096î\u0015Y\u009b\u000b\u0010öó >\u0013¦Ý3\u0088¯z\u0095$\u0005\u0097\u0099AP\fÆþ~©á\u001bkÆì°acîWrå\u001f3\u0095@\n\u009e«+\u0003y®¶2Ä£\u0011j¯éü\u001e\nûGS\u0095Þ\"Bp\u0080\u008d{Ûðhj¦íóf\u0001\\_\u0082ì\u001f:Êw\u0010\u0085¶Ò=`ò½/Ë§\u00182VÞã\u00141ÃNI\u009cÒc\u007fÑ'\u0007ºt6ª§\u001f~M\u0097\u0082\u0003ð\u0098%\u001e\u009bØÈv>ìsk¡°\u0016iDý¹KïÍ\\\u0012\u0092ýÇz5`k¯Ø\"\u000eµC7±\u0097æ\nT\u009a\u0089\u0015ÿ\u0088,\u0019bâ×(\u0005ûzz¨¾\u001djSù\u0080cöø\u008dA?\u0002é\u0090\u009a\u000bDÃñ\f£¯l>\u001e¾Ë1u»&\\ÐÜ\u009dWO\u0087øHªÅWr\u0001ð²p|í)}Û\u0012\u0085\u008f6\u001eà\u0085\u00adO_¢\b;º°g+\u0011¨Âk\u008cÃ9ZëÄ\u0094GFÀóy½ún;\u0018âÅnw\u0007!\u0097Ò\n\u009cÇIWûË,¤\u009eÖHO;ÀåDPî\u0002xÍé¿XjóÔ}\u0087\u009bq\u0018<\u0098î\u0004Y\u009b\u000b\u0000ö¼ 3\u0013\u008eÝ/\u0088\u00adzÚ$\u001f\u0097\u0094A\u0003\fÃþr©æ\u001b;Æà°bcÿ-S\u0098\u009cJ\u00175\u0091çKR±\u001c:Ï\u00ad¹&d®ÖÃ\u0080Tsß=\u0005è\u0095Z\t\u0005¶÷n¢§l,ß«\u0089\u0006t\u008b&\u0018\u0091\u0097C\u0018\u000e¤øq«á\u0015eÀê²:ÁÝs¯¥6Ö¹\b=½\u0097ï\u0001 \u0090R!\u0087\u008a9\u0004jâ\u009caÑá\u0003}´âæy\u001bÅMJþ÷0VeÔ\u0097£Éfzí¬zá±\u0013\u0003D\u009eöB+\u009d]\u0006\u008e\u0080À*uö§{Øì\n`¿ÅñC\"ÖT_\u0089Ð;îmr\u009eâÁ\u009bsé¥pÖÿ\b{½ÑïG ÖRg\u0087Ì9Bj¤\u009c'Ñ§\u0003;´¤æ?\u001b\u0083M\fþ±0\u0010e\u0092\u0097åÉ z«¬<áÿ\u0013MDÜöC+ß]T\u008e\u0096À?u¶§9Øª\n!¿\u0099ñ@\"ÜT\\,¤\u009eÖHO;ÀåDPî\u0002xÍé¿XjóÔ}\u0087\u009bq\u0018<\u0098î\u0004Y\u009b\u000b\u0000ö¼ 3\u0013\u008eÝ/\u0088\u00adzÚ$\u001f\u0097\u0094A\u0003\fÈþz©ç\u001b;Æä°\u007fcù-S\u0098\u008eJ\u00135\u0080ç\u001fR \u001c,Ïù¹ d¥ÖÖ\u0080_sÜ=@èËZ\t\u0005õ÷o¢èllß«,é\u009eÇHR;\u0087,¤\u009eÖHO;ÀåDPî\u0002xÍé¿XjóÔ}\u0087\u009bq\u0018<\u0098î\u0004Y\u009b\u000b\u0000ö¼ 3\u0013\u008eÝ/\u0088\u00adzÚ$\u001f\u0097\u0094A\u0003\fÈþz©ç\u001b;Æä°\u007fcù-S\u0098\u008eJ\u00135\u0080ç\u001fR \u001c,Ïù¹0d¹ÖÖ\u0080HsÈ=\u0005è\u0095Z\t,ª\u009eÛHX;ÄåJPû\u0002gÍð¿\u007fjêÔa\u0087\u0092q8<\u009fî0Y\u009f\u000b\rö² )\u0013¢Ýa\u0088æz\u0095$z\u0097ÔAU\f\u008dþV©á\u001bkÆé°fcê-\u0012\u0098\u0089J\u000e5\u008eç\u0005Rõ\u001ceÏùukÇX\u0011Ìb\u0005¼Í\tF[ã\u0094=æú3o\u008d¯Þ=(\u0090e\u001c·¢\u0000\u0000R\u009d¯\u0010ù¯J5\u0084¯Ñ #T}ÜÎO\u0018ÈU@§ûð?B¹\u009fiéâ:+t\u0081Á\r\u0013\u008al\u0005¾\u0080\u000b;E¸\u0096{à¨=+\u008f\u0015ÙÇ*Vd\u0087±N\u0003Ã\\t®üûn,é\u009eÚHN;\u0087åSPÎ\u0002vÍú¿pjõÔh\u0087\u0093qQ<\u009dî\nY\u009d\u000bIö§ 5\u0013¢Ýa\u0088úzÆ$K\u0097\u0099AW\fÄþz©ô\u001b7Æ¥°acæ-S\u0098\u008dJ\u00155\u008eç\rR¼\u001c3Ï¼¹cd¤ÖÓ\u0080\u0011sÏ=Jè\u008fZJ\u0005û÷x¢älj5û\u0087\u008aQ\t\"\u0095ü\u001bIª\u001b6Ô¡¦.s»Í0\u009eÃhi%Î÷a@Î\u0012\\ïã¹x\nóÄ0\u0091·cÄ=>\u008e\u009aX\u001d\u0015\u009aç/°¬\u0002/ßô©1z¾4\u0002\u0081éS[,Æþ\u001aKÅ\u0005~Öø ~}õÏ\u0085\u0099\u0001j\u009e$\u001dñ\u0091C\u0016\u001câîv»ö,é\u009eÛH\\;Ôå\u0001PÉ\u0002pÍú¿wj£Ôx\u0087\u0087q\u0015<\u009aî\u0011Y\u008a\u000b\röó ;\u0013µÝ.\u0088¦z\u0095$\u0005\u001d\u001c¯2y§\nrÔîa~,é\u009eÛH\\;Ôå\u0001PÅ\u0002zÍë¿9jáÔh\u0087\u0092q\u001f<Ûî\u0010Y\u009f\u000b\rö² )\u0013¢Ý%ýSO?\u0099¦ê'4©\u0081.Ó\u0095\u001c\fn\u0095»J\u0005ÉV> òía?ã\u0088hÚ 'UqÁÂZ\fØYW«(õöFj\u0090ê,º\u009eÖHO;Îå@PÇ\u0002|Íå¿|jÇÔx\u0087\u009aq\u001c<\u0092î\u0000Y\u009c\u000b*ö² /\u0013£Ý2\u0088ëz\u0098$\u001f\u0097ðAg\f\u008dþ-©±\u0084Ì6 à9\u0093¸M6ø±ª\ne\u0093\u0017\nÂ±|\u000e/ìÙj\u0094äFvñê£\\^Ä\bY»ÕuD \u009dÒî\u008ci?\u0086é\u0006¤\u0088V4\u0001¢³?nÓ\u0018:Ë¾\u0085W0Ïâ1\u009dÞOYú\u0083´\u0013g\u008f,§\u009eÜHI;ÎåGPÒ\u0002PÍò¿ojÂÔ}\u0087\u0087q\u001d<\u0092î\u0006Y\u008e\u000b\u001döº 2\u0013©Ý\u000e\u0088¿zÅ$~\u0097ÚAW\fÄþa©ð\u001boÆì°`cç-!\u0098\u0098J\u00015\u0094ç\u0018R°\u001c;Ïù¹ndíÖö\u0080RsÏ=LèÙZH\u0005ç÷t¢èloßÙ\u0089\u0010t\u0099&\f\u0091\u0090C\b\u000e³".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1979);
        a = cArr;
        h = 8686024387730906803L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = r7 + 102
            byte[] r0 = o.ek.b.$$a
            int r6 = r6 * 3
            int r6 = 4 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L32:
            int r7 = -r7
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.l(short, byte, byte, java.lang.Object[]):void");
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\b$a.smali */
    static final class a {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final a a;
        public static final a b;
        public static final a c;
        private static char[] d;
        private static final /* synthetic */ a[] e;
        private static long g;
        private static int h;
        private static int i;

        static void c() {
            d = new char[]{11402, 12469, 5248, 30875, 23806, 41160, 34005, 59409, 52230, 53308, 13425, 6245, 31819, 16802, 42407, 35204, 60901, 61903, 54724, 14788, 7468, 24836, 17667, 43360, 36173, 1252, 6363, 15598, 20725, 29840, 34982, 44219, 49279, 58472, 63557, 7182, 12311, 21566, 27079, 36318, 41471, 50562, 55703, 11399, 12466, 5263, 30864};
            g = -6982610949218488099L;
        }

        static void init$0() {
            $$a = new byte[]{40, 24, -45, -26};
            $$b = 214;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void j(int r6, short r7, int r8, java.lang.Object[] r9) {
            /*
                int r6 = 105 - r6
                int r7 = r7 * 4
                int r7 = r7 + 4
                byte[] r0 = o.ek.b.a.$$a
                int r8 = r8 * 4
                int r8 = 1 - r8
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r6 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L1a:
                r3 = r2
            L1b:
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r8) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                r4 = r0[r7]
                int r3 = r3 + 1
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r7 = -r7
                int r7 = r7 + r8
                int r6 = r6 + 1
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.b.a.j(int, short, int, java.lang.Object[]):void");
        }

        private a(String str, int i2) {
        }

        private static /* synthetic */ a[] d() {
            int i2 = i;
            int i3 = i2 + 27;
            h = i3 % 128;
            int i4 = i3 % 2;
            a[] aVarArr = {a, b, c};
            int i5 = i2 + 17;
            h = i5 % 128;
            switch (i5 % 2 == 0 ? 'V' : (char) 11) {
                case Opcodes.SASTORE /* 86 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return aVarArr;
            }
        }

        public static a valueOf(String str) {
            int i2 = i + Opcodes.LSUB;
            h = i2 % 128;
            int i3 = i2 % 2;
            a aVar = (a) Enum.valueOf(a.class, str);
            int i4 = h + Opcodes.LMUL;
            i = i4 % 128;
            switch (i4 % 2 != 0 ? (char) 4 : 'F') {
                case 4:
                    int i5 = 52 / 0;
                    return aVar;
                default:
                    return aVar;
            }
        }

        public static a[] values() {
            int i2 = h + 43;
            i = i2 % 128;
            int i3 = i2 % 2;
            a[] aVarArr = (a[]) e.clone();
            int i4 = h + 79;
            i = i4 % 128;
            int i5 = i4 % 2;
            return aVarArr;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            h = 1;
            c();
            Object[] objArr = new Object[1];
            f((char) Color.green(0), View.MeasureSpec.makeMeasureSpec(0, 0), TextUtils.indexOf("", "", 0) + 25, objArr);
            a = new a(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            f((char) (10350 - ((Process.getThreadPriority(0) + 20) >> 6)), 25 - (ViewConfiguration.getPressedStateDuration() >> 16), 18 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
            b = new a(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            f((char) TextUtils.getCapsMode("", 0, 0), 43 - View.MeasureSpec.makeMeasureSpec(0, 0), 4 - (ViewConfiguration.getEdgeSlop() >> 16), objArr3);
            c = new a(((String) objArr3[0]).intern(), 2);
            e = d();
            int i2 = h + 79;
            i = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 7 : 'F') {
                case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                    return;
                default:
                    int i3 = 36 / 0;
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(char r20, int r21, int r22, java.lang.Object[] r23) {
            /*
                Method dump skipped, instructions count: 730
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.b.a.f(char, int, int, java.lang.Object[]):void");
        }
    }

    public final LinkedHashMap<String, o.eo.e> c() {
        int i2 = g + 17;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        LinkedHashMap<String, o.eo.e> linkedHashMap = this.d;
        int i5 = i3 + 25;
        g = i5 % 128;
        int i6 = i5 % 2;
        return linkedHashMap;
    }

    public final LinkedHashMap<String, o.eo.e> b() {
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>(this.d);
        linkedHashMap.putAll(this.b);
        int i2 = i + 45;
        g = i2 % 128;
        int i3 = i2 % 2;
        return linkedHashMap;
    }

    public final void c(o.eg.b bVar) throws d {
        if (bVar == null) {
            Object[] objArr = new Object[1];
            k((char) (View.resolveSize(0, 0) + 9705), View.MeasureSpec.getMode(0), 68 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
            throw new d(((String) objArr[0]).intern());
        }
        Object[] objArr2 = new Object[1];
        k((char) (Color.argb(0, 0, 0, 0) + 57634), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 68, (ViewConfiguration.getEdgeSlop() >> 16) + 3, objArr2);
        c(bVar.s(((String) objArr2[0]).intern()));
        Object[] objArr3 = new Object[1];
        k((char) KeyEvent.getDeadChar(0, 0), 71 - Color.argb(0, 0, 0, 0), 3 - TextUtils.indexOf((CharSequence) "", '0'), objArr3);
        switch (bVar.C(((String) objArr3[0]).intern()) ? 'B' : (char) 22) {
            case 'B':
                int i2 = g + Opcodes.LMUL;
                i = i2 % 128;
                int i3 = i2 % 2;
                Object[] objArr4 = new Object[1];
                k((char) View.MeasureSpec.makeMeasureSpec(0, 0), View.resolveSize(0, 0) + 71, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 5, objArr4);
                a(bVar.s(((String) objArr4[0]).intern()));
                int i4 = g + 87;
                i = i4 % 128;
                int i5 = i4 % 2;
                break;
            default:
                a(new o.eg.e());
                break;
        }
        Object[] objArr5 = new Object[1];
        k((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 4291), 75 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), ((Process.getThreadPriority(0) + 20) >> 6) + 5, objArr5);
        e(bVar.s(((String) objArr5[0]).intern()));
        int i6 = g + Opcodes.LMUL;
        i = i6 % 128;
        int i7 = i6 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void c(o.eg.e r12) throws o.eg.d {
        /*
            r11 = this;
            o.ee.g.c()
            r0 = 0
            int r1 = android.graphics.Color.red(r0)
            char r1 = (char) r1
            int r2 = android.view.ViewConfiguration.getWindowTouchSlop()
            int r2 = r2 >> 8
            int r2 = 80 - r2
            int r3 = android.os.Process.getThreadPriority(r0)
            int r3 = r3 + 20
            int r3 = r3 >> 6
            r4 = 11
            int r3 = r3 + r4
            r5 = 1
            java.lang.Object[] r6 = new java.lang.Object[r5]
            k(r1, r2, r3, r6)
            r1 = r6[r0]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            java.lang.String r3 = ""
            int r6 = android.view.MotionEvent.axisFromString(r3)
            int r6 = 15272 - r6
            char r6 = (char) r6
            long r7 = android.os.SystemClock.currentThreadTimeMillis()
            r9 = -1
            int r7 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            int r7 = r7 + 90
            int r3 = android.view.KeyEvent.keyCodeFromString(r3)
            int r3 = r3 + 32
            java.lang.Object[] r8 = new java.lang.Object[r5]
            k(r6, r7, r3, r8)
            r3 = r8[r0]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.StringBuilder r2 = r2.append(r12)
            java.lang.String r2 = r2.toString()
            o.ee.g.d(r1, r2)
            o.ek.e$d r1 = new o.ek.e$d
            r1.<init>()
            r1 = r0
        L6b:
            int r2 = r12.d()
            if (r1 >= r2) goto L74
            r2 = 26
            goto L75
        L74:
            r2 = r4
        L75:
            switch(r2) {
                case 11: goto L85;
                default: goto L78;
            }
        L78:
            int r2 = o.ek.b.g
            int r2 = r2 + 85
            int r3 = r2 % 128
            o.ek.b.i = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L9e
            goto L9e
        L85:
            int r12 = o.ek.b.i
            int r12 = r12 + 95
            int r1 = r12 % 128
            o.ek.b.g = r1
            int r12 = r12 % 2
            if (r12 == 0) goto L92
            r0 = r5
        L92:
            switch(r0) {
                case 0: goto L96;
                default: goto L95;
            }
        L95:
            goto L97
        L96:
            return
        L97:
            r12 = 0
            r12.hashCode()     // Catch: java.lang.Throwable -> L9c
            throw r12     // Catch: java.lang.Throwable -> L9c
        L9c:
            r12 = move-exception
            throw r12
        L9e:
            o.eg.b r2 = r12.b(r1)
            o.ek.e r2 = o.ek.e.d.c(r2)
            java.util.HashMap<java.lang.String, o.ek.e> r3 = r11.c
            java.lang.String r6 = r2.e()
            java.lang.Object r2 = r3.put(r6, r2)
            if (r2 != 0) goto Lb5
            int r1 = r1 + 1
            goto L6b
        Lb5:
            o.eg.d r12 = new o.eg.d
            int r1 = android.view.View.combineMeasuredStates(r0, r0)
            char r1 = (char) r1
            int r2 = android.view.View.resolveSizeAndState(r0, r0, r0)
            int r2 = 123 - r2
            int r3 = android.view.ViewConfiguration.getKeyRepeatDelay()
            int r3 = r3 >> 16
            int r3 = 41 - r3
            java.lang.Object[] r4 = new java.lang.Object[r5]
            k(r1, r2, r3, r4)
            r0 = r4[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r12.<init>(r0)
            throw r12
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.c(o.eg.e):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0076. Please report as an issue. */
    private void a(o.eg.e r11) throws o.eg.d {
        /*
            r10 = this;
            o.ee.g.c()
            r0 = 0
            int r2 = android.widget.ExpandableListView.getPackedPositionType(r0)
            char r2 = (char) r2
            int r3 = android.widget.ExpandableListView.getPackedPositionChild(r0)
            int r3 = r3 + 81
            int r4 = android.view.ViewConfiguration.getLongPressTimeout()
            int r4 = r4 >> 16
            int r4 = r4 + 11
            r5 = 1
            java.lang.Object[] r6 = new java.lang.Object[r5]
            k(r2, r3, r4, r6)
            r2 = 0
            r3 = r6[r2]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            int r6 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r6 = r6 >> 16
            r7 = 59195(0xe73b, float:8.295E-41)
            int r7 = r7 - r6
            char r6 = (char) r7
            int r7 = android.graphics.ImageFormat.getBitsPerPixel(r2)
            int r7 = r7 + 165
            int r8 = android.view.ViewConfiguration.getJumpTapTimeout()
            int r8 = r8 >> 16
            int r8 = r8 + 30
            java.lang.Object[] r9 = new java.lang.Object[r5]
            k(r6, r7, r8, r9)
            r6 = r9[r2]
            java.lang.String r6 = (java.lang.String) r6
            java.lang.String r6 = r6.intern()
            java.lang.StringBuilder r4 = r4.append(r6)
            java.lang.StringBuilder r4 = r4.append(r11)
            java.lang.String r4 = r4.toString()
            o.ee.g.d(r3, r4)
            o.ek.a$a r3 = new o.ek.a$a
            r3.<init>()
            int r3 = o.ek.b.g
            int r3 = r3 + 13
            int r4 = r3 % 128
            o.ek.b.i = r4
            int r3 = r3 % 2
            if (r3 != 0) goto L75
            r3 = r2
            goto L76
        L75:
            r3 = r5
        L76:
            switch(r3) {
                case 0: goto L79;
                default: goto L79;
            }
        L79:
            r3 = r2
        L7a:
            int r4 = r11.d()
            if (r3 >= r4) goto L83
            r4 = 50
            goto L85
        L83:
            r4 = 55
        L85:
            switch(r4) {
                case 55: goto L95;
                default: goto L88;
            }
        L88:
            int r4 = o.ek.b.i
            int r4 = r4 + 89
            int r6 = r4 % 128
            o.ek.b.g = r6
            int r4 = r4 % 2
            if (r4 == 0) goto L96
            goto L96
        L95:
            return
        L96:
            o.eg.b r4 = r11.b(r3)
            o.ek.a r4 = o.ek.a.C0041a.a(r4)
            java.util.HashMap<java.lang.String, o.ek.a> r6 = r10.e
            java.lang.String r7 = r4.d()
            java.lang.Object r4 = r6.put(r7, r4)
            if (r4 != 0) goto Lad
            int r3 = r3 + 1
            goto L7a
        Lad:
            o.eg.d r11 = new o.eg.d
            java.lang.String r3 = ""
            int r4 = android.text.TextUtils.indexOf(r3, r3)
            int r4 = 4256 - r4
            char r4 = (char) r4
            r6 = 48
            int r3 = android.text.TextUtils.indexOf(r3, r6, r2)
            int r3 = 193 - r3
            long r6 = android.widget.ExpandableListView.getPackedPositionForChild(r2, r2)
            int r0 = (r6 > r0 ? 1 : (r6 == r0 ? 0 : -1))
            int r0 = r0 + 31
            java.lang.Object[] r1 = new java.lang.Object[r5]
            k(r4, r3, r0, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r11.<init>(r0)
            throw r11
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.a(o.eg.e):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private void e(o.eg.e r19) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 332
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.e(o.eg.e):void");
    }

    public final o.fk.b a(Context context, LinkedHashMap<String, o.eo.e> linkedHashMap, o.ex.d dVar) {
        String str;
        Iterator<Map.Entry<String, o.eo.e>> it;
        Object intern;
        a aVar;
        String str2;
        o.er.b bVar;
        Iterator<Map.Entry<String, o.eo.e>> it2;
        long j = 0;
        int i2 = 1;
        Object[] objArr = new Object[1];
        k((char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 80, 11 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr);
        int i3 = 0;
        String intern2 = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        k((char) TextUtils.getTrimmedLength(""), 249 - (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getFadingEdgeLength() >> 16) + 11, objArr2);
        g.d(intern2, ((String) objArr2[0]).intern());
        o.fk.b bVar2 = new o.fk.b();
        Iterator<Map.Entry<String, o.eo.e>> it3 = linkedHashMap.entrySet().iterator();
        while (it3.hasNext()) {
            o.eo.e value = it3.next().getValue();
            g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr3 = new Object[i2];
            k((char) (45196 - ((byte) KeyEvent.getModifierMetaStateMask())), 259 - (ExpandableListView.getPackedPositionForChild(i3, i3) > j ? 1 : (ExpandableListView.getPackedPositionForChild(i3, i3) == j ? 0 : -1)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 13, objArr3);
            g.d(intern2, sb.append(((String) objArr3[i3]).intern()).append(value.e()).toString());
            this.b.remove(value.e());
            o.eo.e eVar = this.d.get(value.e());
            if (eVar != null) {
                int i4 = i + Opcodes.LREM;
                g = i4 % 128;
                if (i4 % 2 != 0) {
                    eVar.a(value);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                if (eVar.a(value)) {
                    it = it3;
                } else {
                    g.c();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr4 = new Object[i2];
                    k((char) (View.combineMeasuredStates(i3, i3) + 11781), (ViewConfiguration.getWindowTouchSlop() >> 8) + 273, 19 - TextUtils.indexOf("", ""), objArr4);
                    StringBuilder append = sb2.append(((String) objArr4[i3]).intern()).append(value.e());
                    Object[] objArr5 = new Object[i2];
                    k((char) (AndroidCharacter.getMirror('0') - '0'), 292 - KeyEvent.normalizeMetaState(i3), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 16, objArr5);
                    g.d(intern2, append.append(((String) objArr5[i3]).intern()).toString());
                    bVar2.e();
                    if (eVar.A() != null) {
                        int i5 = i + Opcodes.LMUL;
                        g = i5 % 128;
                        int i6 = i5 % 2;
                        Iterator<o.eo.d> it4 = eVar.A().values().iterator();
                        int i7 = i3;
                        while (it4.hasNext()) {
                            Iterator<o.el.d> it5 = it4.next().iterator();
                            while (it5.hasNext()) {
                                switch (it5.next().z() == o.ei.a.d ? (char) 18 : (char) 19) {
                                    case 18:
                                        if (Objects.equals(eVar.s(), value.s())) {
                                            it2 = it3;
                                        } else {
                                            g.c();
                                            StringBuilder sb3 = new StringBuilder();
                                            it2 = it3;
                                            Object[] objArr6 = new Object[i2];
                                            k((char) Gravity.getAbsoluteGravity(i3, i3), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 308, Color.red(i3) + 24, objArr6);
                                            StringBuilder append2 = sb3.append(((String) objArr6[i3]).intern()).append(value.e());
                                            Object[] objArr7 = new Object[i2];
                                            k((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 292 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 17 - TextUtils.indexOf("", "", i3), objArr7);
                                            g.d(intern2, append2.append(((String) objArr7[i3]).intern()).toString());
                                            i7 = i2;
                                        }
                                        if (Objects.equals(eVar.q(), value.q())) {
                                            break;
                                        } else {
                                            g.c();
                                            StringBuilder sb4 = new StringBuilder();
                                            Object[] objArr8 = new Object[i2];
                                            k((char) (ViewConfiguration.getEdgeSlop() >> 16), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 332, (ViewConfiguration.getFadingEdgeLength() >> 16) + 27, objArr8);
                                            StringBuilder append3 = sb4.append(((String) objArr8[i3]).intern()).append(value.e());
                                            Object[] objArr9 = new Object[i2];
                                            k((char) Gravity.getAbsoluteGravity(i3, i3), (ExpandableListView.getPackedPositionForGroup(i3) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(i3) == 0L ? 0 : -1)) + 292, 16 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr9);
                                            g.d(intern2, append3.append(((String) objArr9[i3]).intern()).toString());
                                            i7 = i2;
                                            break;
                                        }
                                    default:
                                        it2 = it3;
                                        break;
                                }
                                it3 = it2;
                            }
                            it3 = it3;
                        }
                        it = it3;
                        if (i7 != 0) {
                            bVar2.b(new o.fk.a(value.e(), value.n(), c.h));
                        }
                        if (value.q() != null && eVar.q() != null) {
                            int i8 = g + 85;
                            i = i8 % 128;
                            int i9 = i8 % 2;
                            if (value.q().d() != eVar.q().d()) {
                                bVar2.e();
                            }
                        }
                        Iterator<o.eo.d> it6 = eVar.A().values().iterator();
                        while (true) {
                            switch (it6.hasNext() ? '3' : (char) 18) {
                                case 18:
                                    break;
                                default:
                                    o.eo.d next = it6.next();
                                    Iterator<o.el.d> it7 = next.iterator();
                                    while (it7.hasNext()) {
                                        o.el.d next2 = it7.next();
                                        if (next2 instanceof o.er.b) {
                                            int i10 = g + 109;
                                            i = i10 % 128;
                                            int i11 = i10 % 2;
                                            o.er.b bVar3 = (o.er.b) next2;
                                            if (value.A() != null) {
                                                switch (value.A().get(next.h()) != null ? i2 : i3) {
                                                    case 0:
                                                        break;
                                                    default:
                                                        if ((value.A().get(next.h()).e(value.e()) instanceof o.er.b) && (bVar = (o.er.b) value.A().get(next.h()).e(value.e())) != null && bVar3.C() != bVar.C()) {
                                                            bVar2.e();
                                                            break;
                                                        }
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                    break;
                            }
                        }
                    } else {
                        it = it3;
                    }
                }
            } else {
                it = it3;
            }
            c(value);
            a aVar2 = a.c;
            g.c();
            StringBuilder sb5 = new StringBuilder();
            Object[] objArr10 = new Object[i2];
            k((char) View.MeasureSpec.getSize(i3), Color.green(i3) + 360, 50 - (ExpandableListView.getPackedPositionForGroup(i3) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(i3) == 0L ? 0 : -1)), objArr10);
            StringBuilder append4 = sb5.append(((String) objArr10[i3]).intern());
            if (eVar != null) {
                intern = eVar.t();
            } else {
                Object[] objArr11 = new Object[i2];
                k((char) (Color.red(i3) + 41804), 410 - (TypedValue.complexToFloat(i3) > 0.0f ? 1 : (TypedValue.complexToFloat(i3) == 0.0f ? 0 : -1)), 4 - Drawable.resolveOpacity(i3, i3), objArr11);
                intern = ((String) objArr11[i3]).intern();
            }
            StringBuilder append5 = append4.append(intern);
            Object[] objArr12 = new Object[i2];
            k((char) Color.blue(i3), 414 - TextUtils.getOffsetAfter("", i3), 31 - TextUtils.indexOf((CharSequence) "", '0'), objArr12);
            g.d(intern2, append5.append(((String) objArr12[i3]).intern()).append(value.t()).toString());
            if (eVar == null) {
                if (value.t() != null) {
                    switch (value.t() == h.c ? '(' : ']') {
                        case '(':
                            g.c();
                            Object[] objArr13 = new Object[i2];
                            k((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 7524), 568 - TextUtils.getOffsetAfter("", i3), (CdmaCellLocation.convertQuartSecToDecDegrees(i3) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i3) == 0.0d ? 0 : -1)) + 64, objArr13);
                            g.d(intern2, ((String) objArr13[i3]).intern());
                            aVar2 = a.a;
                            break;
                    }
                }
            } else if (value.t() != eVar.t() && value.t() == h.c) {
                int i12 = g + i2;
                i = i12 % 128;
                int i13 = i12 % 2;
                g.c();
                Object[] objArr14 = new Object[i2];
                k((char) (CdmaCellLocation.convertQuartSecToDecDegrees(i3) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i3) == 0.0d ? 0 : -1)), ImageFormat.getBitsPerPixel(i3) + 447, 65 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr14);
                g.d(intern2, ((String) objArr14[i3]).intern());
                aVar2 = a.a;
            } else if (eVar.t() == h.c) {
                int i14 = i + 81;
                g = i14 % 128;
                int i15 = i14 % 2;
                if (value.t() == h.a) {
                    g.c();
                    Object[] objArr15 = new Object[i2];
                    k((char) (Gravity.getAbsoluteGravity(i3, i3) + 4668), 511 - (ViewConfiguration.getScrollDefaultDelay() >> 16), 57 - (KeyEvent.getMaxKeyCode() >> 16), objArr15);
                    g.d(intern2, ((String) objArr15[i3]).intern());
                    aVar2 = a.b;
                }
            }
            Iterator<o.eo.d> w = value.w();
            int i16 = i2;
            while (w.hasNext()) {
                Iterator<o.el.d> it8 = w.next().iterator();
                int i17 = i2;
                while (it8.hasNext()) {
                    o.el.d next3 = it8.next();
                    e a2 = a(bVar2, next3, value, aVar2);
                    boolean e = e(next3);
                    if (e) {
                        bVar2.b();
                        aVar = aVar2;
                        str2 = intern2;
                        bVar2.b(new o.fk.a(value.e(), value.n(), c.j));
                    } else {
                        aVar = aVar2;
                        str2 = intern2;
                    }
                    if (next3.s() == o.el.b.b && (next3 instanceof i)) {
                        new o.ay.a(context);
                        o.ay.a.b(context, ((i) next3).f());
                    }
                    dVar.e(context, next3, a2.a(), e);
                    if (next3.s() == o.el.b.b) {
                        int i18 = g + Opcodes.LSUB;
                        i = i18 % 128;
                        if (i18 % 2 == 0) {
                            it8.remove();
                            int i19 = 21 / 0;
                        } else {
                            it8.remove();
                        }
                        aVar2 = aVar;
                        intern2 = str2;
                    } else {
                        aVar2 = aVar;
                        intern2 = str2;
                        i17 = 0;
                    }
                }
                a aVar3 = aVar2;
                String str3 = intern2;
                if (i17 != 0) {
                    w.remove();
                    aVar2 = aVar3;
                    intern2 = str3;
                    i2 = 1;
                } else {
                    aVar2 = aVar3;
                    intern2 = str3;
                    i2 = 1;
                    i16 = 0;
                }
            }
            String str4 = intern2;
            switch (i16 != 0) {
                case false:
                    this.d.put(value.e(), value);
                    it3 = it;
                    intern2 = str4;
                    i2 = 1;
                    i3 = 0;
                    j = 0;
                    break;
                default:
                    this.e.remove(value.e());
                    this.d.remove(value.e());
                    it3 = it;
                    intern2 = str4;
                    i2 = 1;
                    i3 = 0;
                    j = 0;
                    break;
            }
        }
        String str5 = intern2;
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        for (o.eo.e eVar2 : this.d.values()) {
            int i20 = g + Opcodes.LNEG;
            i = i20 % 128;
            int i21 = i20 % 2;
            switch (eVar2.l() != null) {
                case true:
                    arrayList.add(eVar2.l());
                    try {
                        if (o.du.a.a().e(context, eVar2.l())) {
                            bVar2.b(new o.fk.a(eVar2.e(), eVar2.n(), c.g));
                        }
                        str = str5;
                        break;
                    } catch (o.du.e e2) {
                        g.c();
                        Object[] objArr16 = new Object[1];
                        k((char) ((-1) - Process.getGidForName("")), KeyEvent.normalizeMetaState(0) + 632, 39 - Color.argb(0, 0, 0, 0), objArr16);
                        str = str5;
                        g.a(str, ((String) objArr16[0]).intern(), e2);
                        break;
                    }
                default:
                    str = str5;
                    break;
            }
            o.er.b bVar4 = (o.er.b) eVar2.b(o.er.b.class);
            if (bVar4 != null && bVar4.C() != null) {
                switch (bVar4.C().d() != null ? 'b' : 'V') {
                    case Opcodes.SASTORE /* 86 */:
                        break;
                    default:
                        arrayList.add(bVar4.C().d());
                        try {
                            if (o.du.a.a().e(context, bVar4.C().d())) {
                                bVar2.b(new o.fk.a(eVar2.e(), eVar2.n(), c.g));
                            }
                            break;
                        } catch (o.du.e e3) {
                            g.c();
                            Object[] objArr17 = new Object[1];
                            k((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), (KeyEvent.getMaxKeyCode() >> 16) + 632, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 38, objArr17);
                            g.a(str, ((String) objArr17[0]).intern(), e3);
                            break;
                        }
                }
            }
            if (eVar2.o() != null) {
                int i22 = g + 69;
                i = i22 % 128;
                int i23 = i22 % 2;
                arrayList2.add(eVar2.o());
                try {
                    o.du.a.a().e(context, eVar2.o());
                    str5 = str;
                } catch (o.du.e e4) {
                    g.c();
                    Object[] objArr18 = new Object[1];
                    k((char) (Color.rgb(0, 0, 0) + 16777216), 672 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 32, objArr18);
                    g.a(str, ((String) objArr18[0]).intern(), e4);
                }
            }
            str5 = str;
        }
        o.du.a.a().c(context, (Context) o.dy.b.d, (Collection<o.du.d<Context, ?, ?, ?>>) arrayList);
        o.du.a.a().c(context, (Context) o.dy.a.a, (Collection<o.du.d<Context, ?, ?, ?>>) arrayList2);
        int i24 = g + 47;
        i = i24 % 128;
        if (i24 % 2 != 0) {
            return bVar2;
        }
        Object obj2 = null;
        obj2.hashCode();
        throw null;
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0094  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x00d5  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void c(o.eo.e r14) {
        /*
            Method dump skipped, instructions count: 670
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.c(o.eo.e):void");
    }

    public static LinkedHashMap<String, o.eo.e> d(o.eg.e eVar) throws o.ei.i {
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>();
        int i2 = g + 91;
        i = i2 % 128;
        int i3 = i2 % 2;
        int i4 = 0;
        while (i4 < eVar.d()) {
            try {
                o.eg.b b = eVar.b(i4);
                new o.cg.e();
                o.eo.e e = o.cg.e.e(b);
                switch (e == null) {
                    case true:
                        int i5 = g + 99;
                        i = i5 % 128;
                        int i6 = i5 % 2;
                        g.c();
                        Object[] objArr = new Object[1];
                        k((char) (Process.myTid() >> 22), TextUtils.indexOf("", "") + 80, 10 - MotionEvent.axisFromString(""), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        k((char) ((Process.getThreadPriority(0) + 20) >> 6), (-16776064) - Color.rgb(0, 0, 0), 33 - View.MeasureSpec.getMode(0), objArr2);
                        g.d(intern, ((String) objArr2[0]).intern());
                        continue;
                        i4++;
                        int i7 = g + Opcodes.LREM;
                        i = i7 % 128;
                        int i8 = i7 % 2;
                    default:
                        switch (linkedHashMap.put(e.e(), e) == null ? '8' : 'I') {
                            case 'I':
                                Object[] objArr3 = new Object[1];
                                k((char) (20470 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 1223 - TextUtils.getOffsetBefore("", 0), View.resolveSize(0, 0) + 42, objArr3);
                                throw new o.ei.i(((String) objArr3[0]).intern());
                            default:
                                g.c();
                                Object[] objArr4 = new Object[1];
                                k((char) KeyEvent.keyCodeFromString(""), 80 - (ViewConfiguration.getEdgeSlop() >> 16), Color.rgb(0, 0, 0) + 16777227, objArr4);
                                String intern2 = ((String) objArr4[0]).intern();
                                Object[] objArr5 = new Object[1];
                                k((char) (31689 - View.resolveSize(0, 0)), 1185 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 37 - ImageFormat.getBitsPerPixel(0), objArr5);
                                g.d(intern2, ((String) objArr5[0]).intern());
                                continue;
                                continue;
                                i4++;
                                int i72 = g + Opcodes.LREM;
                                i = i72 % 128;
                                int i82 = i72 % 2;
                        }
                }
            } catch (d e2) {
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                k((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 41410), 1264 - TextUtils.lastIndexOf("", '0', 0), 49 - TextUtils.indexOf("", "", 0, 0), objArr6);
                throw new o.ei.i(sb.append(((String) objArr6[0]).intern()).append(e2.getMessage()).toString());
            }
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr62 = new Object[1];
            k((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 41410), 1264 - TextUtils.lastIndexOf("", '0', 0), 49 - TextUtils.indexOf("", "", 0, 0), objArr62);
            throw new o.ei.i(sb2.append(((String) objArr62[0]).intern()).append(e2.getMessage()).toString());
        }
        int i9 = i + Opcodes.DREM;
        g = i9 % 128;
        int i10 = i9 % 2;
        return linkedHashMap;
    }

    private e a(o.fk.b bVar, o.el.d dVar, o.eo.e eVar, a aVar) {
        Object[] objArr = new Object[1];
        k((char) TextUtils.indexOf("", "", 0), TextUtils.getCapsMode("", 0, 0) + 80, TextUtils.getOffsetBefore("", 0) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        e eVar2 = this.c.get(dVar.n());
        if (eVar2 != null) {
            g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) (60793 - KeyEvent.normalizeMetaState(0)), View.resolveSizeAndState(0, 0, 0) + 1379, 47 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(dVar.n()).toString());
            o.el.b b = b(eVar2.c(), dVar.s());
            g.c();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr3 = new Object[1];
            k((char) (60736 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 1425 - View.MeasureSpec.getSize(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 42, objArr3);
            g.d(intern, sb2.append(((String) objArr3[0]).intern()).append(b).toString());
            if (b != eVar2.c()) {
                g.c();
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr4 = new Object[1];
                k((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 1467 - Color.red(0), Color.rgb(0, 0, 0) + 16777270, objArr4);
                StringBuilder append = sb3.append(((String) objArr4[0]).intern()).append(eVar2.c());
                Object[] objArr5 = new Object[1];
                k((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 1521 - Color.argb(0, 0, 0, 0), View.combineMeasuredStates(0, 0) + 4, objArr5);
                g.d(intern, append.append(((String) objArr5[0]).intern()).append(b).toString());
                bVar.d(dVar.s(), dVar);
                e(bVar, dVar, eVar, b, aVar);
            } else {
                g.c();
                StringBuilder sb4 = new StringBuilder();
                Object[] objArr6 = new Object[1];
                k((char) View.resolveSize(0, 0), 1525 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 49 - TextUtils.indexOf("", ""), objArr6);
                g.d(intern, sb4.append(((String) objArr6[0]).intern()).append(eVar2.c()).toString());
                if (aVar != a.c) {
                    e(bVar, dVar, eVar, eVar2.c(), aVar);
                }
            }
            eVar2.d(b);
            if (dVar.s() != o.el.b.d && (dVar instanceof o.et.c)) {
                this.c.remove(o.et.c.d(dVar.n()));
            }
            if (b == o.el.b.e) {
                switch (!eVar2.h() ? (char) 28 : 'V') {
                    case Opcodes.SASTORE /* 86 */:
                        break;
                    default:
                        dVar.x();
                        break;
                }
                String d = eVar2.d();
                Map<String, o.co.a> p = dVar.p();
                switch (d != null ? '4' : (char) 0) {
                    case '4':
                        if (p != null) {
                            int i2 = g + 5;
                            i = i2 % 128;
                            int i3 = i2 % 2;
                            switch (p.isEmpty() ? false : true) {
                                case false:
                                    break;
                                default:
                                    int i4 = g + 21;
                                    i = i4 % 128;
                                    int i5 = i4 % 2;
                                    dVar.b(p.get(d));
                                    break;
                            }
                        }
                        break;
                }
            } else {
                dVar.e((Map<String, o.co.a>) null);
            }
            if (b == o.el.b.b) {
                this.c.remove(dVar.n());
            }
            dVar.b(b);
            return eVar2;
        }
        g.c();
        Object[] objArr7 = new Object[1];
        k((char) Color.argb(0, 0, 0, 0), (Process.myPid() >> 22) + 1314, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 64, objArr7);
        g.d(intern, String.format(((String) objArr7[0]).intern(), dVar.n(), dVar.s()));
        e eVar3 = new e(dVar.n(), eVar.e(), dVar.s(), dVar.o(), null, true);
        switch (dVar.s() != o.el.b.b ? '0' : (char) 19) {
            default:
                int i6 = i + 37;
                g = i6 % 128;
                if (i6 % 2 != 0) {
                }
                this.c.put(eVar3.e(), eVar3);
                bVar.d(dVar.s(), dVar);
                e(bVar, dVar, eVar, dVar.s(), aVar);
            case 19:
                return eVar3;
        }
    }

    /* renamed from: o.ek.b$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\b$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int c;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            d = 0;
            c = 1;
            int[] iArr = new int[o.el.b.values().length];
            e = iArr;
            try {
                iArr[o.el.b.d.ordinal()] = 1;
                int i = d;
                int i2 = (i & 35) + (i | 35);
                c = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.el.b.e.ordinal()] = 2;
                int i4 = c;
                int i5 = (i4 ^ 47) + ((i4 & 47) << 1);
                d = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[o.el.b.c.ordinal()] = 3;
                int i6 = d + 33;
                c = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[o.el.b.a.ordinal()] = 4;
                int i8 = c;
                int i9 = ((i8 | 47) << 1) - (i8 ^ 47);
                d = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[o.el.b.b.ordinal()] = 5;
                int i11 = c;
                int i12 = ((i11 | 7) << 1) - (7 ^ i11);
                d = i12 % 128;
                int i13 = i12 % 2;
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:17:0x0051. Please report as an issue. */
    private static void e(o.fk.b bVar, o.el.d dVar, o.eo.e eVar, o.el.b bVar2, a aVar) {
        int i2 = i + 89;
        g = i2 % 128;
        c cVar = null;
        if (i2 % 2 != 0) {
            boolean z = dVar instanceof o.et.c;
            throw null;
        }
        if (dVar instanceof o.et.c) {
            switch (AnonymousClass2.e[bVar2.ordinal()]) {
                case 1:
                    cVar = c.b;
                    break;
                case 2:
                    cVar = c.c;
                    break;
                case 3:
                    cVar = c.a;
                    break;
                case 4:
                    cVar = c.d;
                    break;
                case 5:
                    cVar = c.e;
                    break;
            }
            c c = c(cVar, aVar);
            if (c != null) {
                bVar.b(new o.fk.a(eVar.e(), eVar.n(), c));
                int i3 = g + 33;
                i = i3 % 128;
                switch (i3 % 2 != 0) {
                }
            }
        }
    }

    private static c c(c cVar, a aVar) {
        int i2 = g + 23;
        i = i2 % 128;
        int i3 = i2 % 2;
        switch (cVar == c.b) {
            case false:
                switch (cVar != c.a) {
                    case true:
                        int i4 = g + 39;
                        i = i4 % 128;
                        switch (i4 % 2 == 0 ? (char) 22 : Typography.greater) {
                            case '>':
                                switch (cVar != c.c ? '1' : 'a') {
                                    case '1':
                                        if (cVar != null) {
                                            int i5 = g + 1;
                                            i = i5 % 128;
                                            int i6 = i5 % 2;
                                            return cVar;
                                        }
                                        break;
                                }
                            default:
                                c cVar2 = c.c;
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                        }
                }
        }
        return aVar == a.a ? c.i : cVar;
    }

    private boolean e(o.el.d dVar) {
        int i2 = g + 53;
        i = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? ';' : '\t') {
            case ';':
                boolean z = dVar instanceof o.et.c;
                obj.hashCode();
                throw null;
            default:
                if (!(dVar instanceof o.et.c)) {
                    g.c();
                    Object[] objArr = new Object[1];
                    k((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), (-16777136) - Color.rgb(0, 0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 11, objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    k((char) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1574, 41 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr2);
                    StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(dVar.n());
                    Object[] objArr3 = new Object[1];
                    k((char) (22914 - View.MeasureSpec.getMode(0)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1615, TextUtils.lastIndexOf("", '0', 0) + 53, objArr3);
                    g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
                    return false;
                }
                o.et.c e = e(dVar.n());
                if (e == null) {
                    g.c();
                    Object[] objArr4 = new Object[1];
                    k((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 1), 80 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 11 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr5 = new Object[1];
                    k((char) View.MeasureSpec.getSize(0), 1573 - ImageFormat.getBitsPerPixel(0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 41, objArr5);
                    StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(dVar.n());
                    Object[] objArr6 = new Object[1];
                    k((char) View.getDefaultSize(0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1666, 53 - TextUtils.getOffsetBefore("", 0), objArr6);
                    g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
                    int i3 = i + 95;
                    g = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            throw null;
                        default:
                            return false;
                    }
                }
                o.et.c cVar = (o.et.c) dVar;
                if (e.l().equals(cVar.l())) {
                    g.c();
                    Object[] objArr7 = new Object[1];
                    k((char) TextUtils.indexOf("", ""), 80 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), View.MeasureSpec.getMode(0) + 11, objArr7);
                    String intern3 = ((String) objArr7[0]).intern();
                    StringBuilder sb3 = new StringBuilder();
                    Object[] objArr8 = new Object[1];
                    k((char) (MotionEvent.axisFromString("") + 6482), 1720 - ((Process.getThreadPriority(0) + 20) >> 6), 52 - (Process.myTid() >> 22), objArr8);
                    StringBuilder append3 = sb3.append(((String) objArr8[0]).intern()).append(dVar.n());
                    Object[] objArr9 = new Object[1];
                    k((char) (ViewConfiguration.getTapTimeout() >> 16), 1802 - (ViewConfiguration.getKeyRepeatDelay() >> 16), TextUtils.indexOf("", "", 0, 0) + 21, objArr9);
                    g.d(intern3, append3.append(((String) objArr9[0]).intern()).toString());
                    return false;
                }
                g.c();
                Object[] objArr10 = new Object[1];
                k((char) ((Process.getThreadPriority(0) + 20) >> 6), 80 - ((Process.getThreadPriority(0) + 20) >> 6), 11 - TextUtils.getCapsMode("", 0, 0), objArr10);
                String intern4 = ((String) objArr10[0]).intern();
                StringBuilder sb4 = new StringBuilder();
                Object[] objArr11 = new Object[1];
                k((char) (6482 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 1720 - View.resolveSize(0, 0), 53 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr11);
                StringBuilder append4 = sb4.append(((String) objArr11[0]).intern()).append(dVar.n());
                Object[] objArr12 = new Object[1];
                k((char) ((Process.getThreadPriority(0) + 20) >> 6), 1771 - TextUtils.indexOf((CharSequence) "", '0'), View.resolveSizeAndState(0, 0, 0) + 24, objArr12);
                StringBuilder append5 = append4.append(((String) objArr12[0]).intern()).append(e.l());
                Object[] objArr13 = new Object[1];
                k((char) (12789 - Color.argb(0, 0, 0, 0)), 1797 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 6 - Color.green(0), objArr13);
                g.d(intern4, append5.append(((String) objArr13[0]).intern()).append(cVar.l()).toString());
                return true;
        }
    }

    public final void e(String str, String str2, o.ei.a aVar) {
        int i2 = g + 79;
        i = i2 % 128;
        int i3 = i2 % 2;
        this.b.put(str, o.eo.e.c(str, aVar, str2));
        int i4 = g + 27;
        i = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    public final o.eg.b d() throws d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        k((char) (TextUtils.indexOf("", "") + 57634), 68 - TextUtils.getTrimmedLength(""), (ViewConfiguration.getLongPressTimeout() >> 16) + 3, objArr);
        bVar.d(((String) objArr[0]).intern(), f());
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getWindowTouchSlop() >> 8), (-16777145) - Color.rgb(0, 0, 0), (ViewConfiguration.getEdgeSlop() >> 16) + 4, objArr2);
        bVar.d(((String) objArr2[0]).intern(), g());
        Object[] objArr3 = new Object[1];
        k((char) (Gravity.getAbsoluteGravity(0, 0) + 4291), ExpandableListView.getPackedPositionType(0L) + 75, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 4, objArr3);
        bVar.d(((String) objArr3[0]).intern(), i());
        g.c();
        Object[] objArr4 = new Object[1];
        k((char) Gravity.getAbsoluteGravity(0, 0), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 80, ImageFormat.getBitsPerPixel(0) + 12, objArr4);
        String intern = ((String) objArr4[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        k((char) (53737 - (ViewConfiguration.getTapTimeout() >> 16)), 1822 - ExpandableListView.getPackedPositionChild(0L), KeyEvent.getDeadChar(0, 0) + 26, objArr5);
        g.d(intern, sb.append(((String) objArr5[0]).intern()).append(bVar).toString());
        int i2 = g + Opcodes.LSUB;
        i = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    private o.eg.e f() throws d {
        new e.d();
        o.eg.e eVar = new o.eg.e();
        Iterator<e> it = this.c.values().iterator();
        while (true) {
            switch (it.hasNext() ? '\b' : 'M') {
                case '\b':
                    int i2 = i + 83;
                    g = i2 % 128;
                    int i3 = i2 % 2;
                    eVar.b(e.d.a(it.next()));
                default:
                    int i4 = i + 49;
                    g = i4 % 128;
                    int i5 = i4 % 2;
                    return eVar;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private o.eg.e g() throws o.eg.d {
        /*
            r6 = this;
            o.ek.a$a r0 = new o.ek.a$a
            r0.<init>()
            o.eg.e r0 = new o.eg.e
            r0.<init>()
            java.util.HashMap<java.lang.String, o.ek.a> r1 = r6.e
            java.util.Collection r1 = r1.values()
            java.util.Iterator r1 = r1.iterator()
        L15:
            boolean r2 = r1.hasNext()
            r3 = 1
            r4 = 0
            if (r2 == 0) goto L1f
            r2 = r3
            goto L20
        L1f:
            r2 = r4
        L20:
            switch(r2) {
                case 0: goto L30;
                default: goto L23;
            }
        L23:
            int r2 = o.ek.b.g
            int r2 = r2 + 53
            int r5 = r2 % 128
            o.ek.b.i = r5
            int r2 = r2 % 2
            if (r2 != 0) goto L3e
            goto L3c
        L30:
            int r1 = o.ek.b.i
            int r1 = r1 + 11
            int r2 = r1 % 128
            o.ek.b.g = r2
            int r1 = r1 % 2
            return r0
        L3c:
            r3 = r4
            goto L3f
        L3e:
        L3f:
            switch(r3) {
                case 1: goto L50;
                default: goto L42;
            }
        L42:
            java.lang.Object r2 = r1.next()
            o.ek.a r2 = (o.ek.a) r2
            o.eg.b r2 = o.ek.a.C0041a.e(r2)
            r0.b(r2)
            goto L5e
        L50:
            java.lang.Object r2 = r1.next()
            o.ek.a r2 = (o.ek.a) r2
            o.eg.b r2 = o.ek.a.C0041a.e(r2)
            r0.b(r2)
            goto L61
        L5e:
            r2 = 97
            int r2 = r2 / r4
        L61:
            goto L15
        L62:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.g():o.eg.e");
    }

    private o.eg.e i() throws d {
        Object obj;
        o.eg.e eVar = new o.eg.e();
        int i2 = g + 63;
        i = i2 % 128;
        int i3 = i2 % 2;
        for (o.eo.e eVar2 : this.b.values()) {
            g.c();
            Object[] objArr = new Object[1];
            k((char) TextUtils.getTrimmedLength(""), 80 - TextUtils.indexOf("", ""), ExpandableListView.getPackedPositionChild(0L) + 12, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) KeyEvent.keyCodeFromString(""), (-16775367) - Color.rgb(0, 0, 0), 28 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar2.e()).toString());
            Object[] objArr3 = new Object[1];
            k((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 80, TextUtils.lastIndexOf("", '0', 0) + 12, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr4 = new Object[1];
            k((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 43126), 1878 - Color.red(0), 41 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr4);
            g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(eVar2.n()).toString());
            o.eg.b bVar = new o.eg.b();
            Object[] objArr5 = new Object[1];
            k((char) (ViewConfiguration.getJumpTapTimeout() >> 16), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 223, (ViewConfiguration.getEdgeSlop() >> 16) + 6, objArr5);
            bVar.d(((String) objArr5[0]).intern(), eVar2.e());
            switch (eVar2.h() != null ? (char) 18 : 'S') {
                case 18:
                    int i4 = i + Opcodes.LNEG;
                    g = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case false:
                            Object[] objArr6 = new Object[1];
                            k((char) View.resolveSizeAndState(0, 0, 0), KeyEvent.getDeadChar(0, 0) + 242, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 7, objArr6);
                            obj = objArr6[0];
                            break;
                        default:
                            Object[] objArr7 = new Object[1];
                            k((char) View.resolveSizeAndState(0, 1, 0), 7112 >> KeyEvent.getDeadChar(0, 0), 82 << (TypedValue.complexToFraction(1, 0.0f, 1.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(1, 0.0f, 1.0f) == 0.0f ? 0 : -1)), objArr7);
                            obj = objArr7[0];
                            break;
                    }
                    bVar.d(((String) obj).intern(), eVar2.h().toString());
                    break;
            }
            Object[] objArr8 = new Object[1];
            k((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 230 - Drawable.resolveOpacity(0, 0), 13 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr8);
            bVar.d(((String) objArr8[0]).intern(), eVar2.n());
            eVar.b(bVar);
        }
        return eVar;
    }

    public final boolean d(String str) {
        int i2 = g + 81;
        i = i2 % 128;
        int i3 = i2 % 2;
        boolean containsKey = this.b.containsKey(str);
        int i4 = i + Opcodes.LSUB;
        g = i4 % 128;
        int i5 = i4 % 2;
        return containsKey;
    }

    public final void a() {
        int i2 = i + 45;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? 'I' : 'Y') {
            case 'I':
                this.c.clear();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.c.clear();
                return;
        }
    }

    public final void e() {
        int i2 = g + 51;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                this.e.clear();
                this.d.clear();
                throw null;
            default:
                this.e.clear();
                this.d.clear();
                return;
        }
    }

    public final void h() {
        int i2 = g + Opcodes.LREM;
        i = i2 % 128;
        int i3 = i2 % 2;
        this.b.clear();
        int i4 = i + 63;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.el.d b(java.lang.String r8) {
        /*
            r7 = this;
            int r0 = o.ek.b.g
            int r0 = r0 + 25
            int r1 = r0 % 128
            o.ek.b.i = r1
            int r0 = r0 % 2
            r0 = 0
            r2 = 0
            if (r8 != 0) goto L20
        Lf:
            int r1 = r1 + 37
            int r8 = r1 % 128
            o.ek.b.g = r8
            int r1 = r1 % 2
            if (r1 == 0) goto L1f
            r8 = 63
            int r8 = r8 / r0
            return r2
        L1d:
            r8 = move-exception
            throw r8
        L1f:
            return r2
        L20:
            java.util.LinkedHashMap<java.lang.String, o.eo.e> r1 = r7.d
            java.util.Collection r1 = r1.values()
            java.util.Iterator r1 = r1.iterator()
        L2a:
            boolean r3 = r1.hasNext()
            if (r3 == 0) goto L33
            r3 = 36
            goto L35
        L33:
            r3 = 34
        L35:
            switch(r3) {
                case 34: goto L8c;
                default: goto L38;
            }
        L38:
            int r3 = o.ek.b.i
            int r3 = r3 + 113
            int r4 = r3 % 128
            o.ek.b.g = r4
            int r3 = r3 % 2
            java.lang.Object r3 = r1.next()
            o.eo.e r3 = (o.eo.e) r3
            java.util.LinkedHashMap r4 = r3.A()
            if (r4 == 0) goto L8c
            java.util.LinkedHashMap r3 = r3.A()
            java.util.Collection r3 = r3.values()
            java.util.Iterator r3 = r3.iterator()
        L5b:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L63
            r4 = 1
            goto L64
        L63:
            r4 = r0
        L64:
            switch(r4) {
                case 1: goto L68;
                default: goto L67;
            }
        L67:
            goto L2a
        L68:
            java.lang.Object r4 = r3.next()
            o.eo.d r4 = (o.eo.d) r4
            java.util.Iterator r4 = r4.iterator()
        L72:
            boolean r5 = r4.hasNext()
            if (r5 == 0) goto L8b
            java.lang.Object r5 = r4.next()
            o.el.d r5 = (o.el.d) r5
            java.lang.String r6 = r5.n()
            boolean r6 = r6.equals(r8)
            if (r6 == 0) goto L8a
            return r5
        L8a:
            goto L72
        L8b:
            goto L5b
        L8c:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.b(java.lang.String):o.el.d");
    }

    /* JADX WARN: Removed duplicated region for block: B:35:0x008b A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:36:0x008c A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.et.c e(java.lang.String r9) {
        /*
            r8 = this;
            if (r9 != 0) goto L6
            r0 = 89
            goto L8
        L6:
            r0 = 20
        L8:
            r1 = 0
            switch(r0) {
                case 20: goto Le;
                default: goto Lc;
            }
        Lc:
            goto L92
        Le:
            java.util.LinkedHashMap<java.lang.String, o.eo.e> r0 = r8.d
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L19:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L7f
            java.lang.Object r2 = r0.next()
            o.eo.e r2 = (o.eo.e) r2
            java.util.LinkedHashMap r3 = r2.A()
            r4 = 0
            r5 = 1
            if (r3 == 0) goto L2f
            r3 = r4
            goto L30
        L2f:
            r3 = r5
        L30:
            switch(r3) {
                case 0: goto L34;
                default: goto L33;
            }
        L33:
            goto L7f
        L34:
            java.util.LinkedHashMap r2 = r2.A()
            java.util.Collection r2 = r2.values()
            java.util.Iterator r2 = r2.iterator()
        L40:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L7e
            java.lang.Object r3 = r2.next()
            o.eo.d r3 = (o.eo.d) r3
            java.util.List r3 = r3.d()
            java.util.Iterator r3 = r3.iterator()
        L54:
            boolean r6 = r3.hasNext()
            if (r6 == 0) goto L7d
            java.lang.Object r6 = r3.next()
            o.et.c r6 = (o.et.c) r6
            java.lang.String r7 = r6.n()
            boolean r7 = r7.equals(r9)
            if (r7 == 0) goto L6c
            r7 = r4
            goto L6d
        L6c:
            r7 = r5
        L6d:
            switch(r7) {
                case 0: goto L71;
                default: goto L70;
            }
        L70:
            goto L54
        L71:
            int r9 = o.ek.b.i
            int r9 = r9 + 121
            int r0 = r9 % 128
            o.ek.b.g = r0
            int r9 = r9 % 2
            return r6
        L7d:
            goto L40
        L7e:
            goto L19
        L7f:
            int r9 = o.ek.b.g
            int r9 = r9 + 39
            int r0 = r9 % 128
            o.ek.b.i = r0
            int r9 = r9 % 2
            if (r9 == 0) goto L8c
            return r1
        L8c:
            r1.hashCode()     // Catch: java.lang.Throwable -> L90
            throw r1     // Catch: java.lang.Throwable -> L90
        L90:
            r9 = move-exception
            throw r9
        L92:
            int r9 = o.ek.b.g
            int r9 = r9 + 37
            int r0 = r9 % 128
            o.ek.b.i = r0
            int r9 = r9 % 2
            if (r9 != 0) goto La0
            r9 = 6
            goto La2
        La0:
            r9 = 78
        La2:
            switch(r9) {
                case 6: goto La6;
                default: goto La5;
            }
        La5:
            return r1
        La6:
            throw r1     // Catch: java.lang.Throwable -> La7
        La7:
            r9 = move-exception
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.e(java.lang.String):o.et.c");
    }

    private static o.el.b b(o.el.b bVar, o.el.b bVar2) {
        switch (bVar != o.el.b.b) {
            case true:
                Object obj = null;
                if (bVar2 == o.el.b.e) {
                    int i2 = i + 35;
                    g = i2 % 128;
                    int i3 = i2 % 2;
                    if (bVar == o.el.b.c) {
                        int i4 = g + Opcodes.DDIV;
                        i = i4 % 128;
                        boolean z = i4 % 2 != 0;
                        o.el.b bVar3 = o.el.b.c;
                        switch (z) {
                            case false:
                                obj.hashCode();
                                throw null;
                            default:
                                return bVar3;
                        }
                    }
                }
                switch (bVar == o.el.b.g ? 'G' : 'L') {
                    case 'G':
                        switch (bVar2 == o.el.b.b) {
                            case false:
                                o.el.b bVar4 = o.el.b.g;
                                int i5 = i + 7;
                                g = i5 % 128;
                                if (i5 % 2 == 0) {
                                    return bVar4;
                                }
                                throw null;
                        }
                    default:
                        return bVar2;
                }
            default:
                int i6 = g + 37;
                i = i6 % 128;
                if (i6 % 2 == 0) {
                }
                o.el.b bVar5 = o.el.b.b;
                int i7 = g + 99;
                i = i7 % 128;
                switch (i7 % 2 != 0) {
                    case false:
                        int i8 = 55 / 0;
                        return bVar5;
                    default:
                        return bVar5;
                }
        }
    }

    public final void c(o.el.d dVar, o.co.a aVar) {
        int i2 = i + 27;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? 'P' : (char) 27) {
            case 27:
                dVar.b(aVar);
                this.c.get(dVar.n());
                throw null;
            default:
                dVar.b(aVar);
                e eVar = this.c.get(dVar.n());
                switch (eVar != null ? (char) 5 : ',') {
                    case ',':
                        return;
                    default:
                        int i3 = g + 71;
                        i = i3 % 128;
                        int i4 = i3 % 2;
                        eVar.b(aVar.d());
                        return;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0051, code lost:
    
        if (r4 != null) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(o.el.d r4) {
        /*
            r3 = this;
            int r0 = o.ek.b.i
            int r0 = r0 + 7
            int r1 = r0 % 128
            o.ek.b.g = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 == 0) goto Lf
            r0 = r1
            goto L10
        Lf:
            r0 = 1
        L10:
            r2 = 0
            switch(r0) {
                case 1: goto L2c;
                default: goto L14;
            }
        L14:
            r4.b(r2)
            r4.e(r2)
            o.el.b r0 = o.el.b.c
            r4.b(r0)
            java.util.HashMap<java.lang.String, o.ek.e> r0 = r3.c
            java.lang.String r4 = r4.n()
            java.lang.Object r4 = r0.get(r4)
            o.ek.e r4 = (o.ek.e) r4
            goto L4e
        L2c:
            r4.b(r2)
            r4.e(r2)
            o.el.b r0 = o.el.b.c
            r4.b(r0)
            java.util.HashMap<java.lang.String, o.ek.e> r0 = r3.c
            java.lang.String r4 = r4.n()
            java.lang.Object r4 = r0.get(r4)
            o.ek.e r4 = (o.ek.e) r4
            if (r4 == 0) goto L48
            r0 = 18
            goto L4a
        L48:
            r0 = 27
        L4a:
            switch(r0) {
                case 18: goto L53;
                default: goto L4d;
            }
        L4d:
            goto L5b
        L4e:
            r0 = 34
            int r0 = r0 / r1
            if (r4 == 0) goto L5b
        L53:
            r4.b(r2)
            o.el.b r0 = o.el.b.c
            r4.d(r0)
        L5b:
            int r4 = o.ek.b.g
            int r4 = r4 + 93
            int r0 = r4 % 128
            o.ek.b.i = r0
            int r4 = r4 % 2
            if (r4 == 0) goto L69
            return
        L69:
            throw r2     // Catch: java.lang.Throwable -> L6a
        L6a:
            r4 = move-exception
            throw r4
        L6c:
            r4 = move-exception
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.a(o.el.d):void");
    }

    public final void d(o.el.d dVar) {
        e eVar = this.c.get(dVar.n());
        if (dVar.y()) {
            switch (eVar != null) {
                case true:
                    int i2 = i + 99;
                    g = i2 % 128;
                    int i3 = i2 % 2;
                    eVar.b(null);
                    break;
            }
        }
        dVar.x();
        switch (eVar != null ? (char) 11 : 'a') {
            case Opcodes.LADD /* 97 */:
                break;
            default:
                int i4 = g + Opcodes.DSUB;
                i = i4 % 128;
                char c = i4 % 2 == 0 ? 'J' : 'D';
                eVar.g();
                switch (c) {
                    case 'J':
                        throw null;
                }
        }
        if (dVar.p() != null) {
            switch (dVar.p().size() == 0 ? (char) 23 : 'G') {
                case 'G':
                    return;
                default:
                    dVar.e((Map<String, o.co.a>) null);
                    g.c();
                    Object[] objArr = new Object[1];
                    k((char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 80 - (Process.myTid() >> 22), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 10, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    k((char) (ImageFormat.getBitsPerPixel(0) + 1), 1919 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 60 - Gravity.getAbsoluteGravity(0, 0), objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    dVar.b(o.el.b.g);
                    if (eVar != null) {
                        eVar.d(o.el.b.g);
                        return;
                    }
                    return;
            }
        }
    }

    public final void c(o.eo.e eVar, h hVar) {
        int i2 = g + Opcodes.LMUL;
        i = i2 % 128;
        int i3 = i2 % 2;
        o.ek.a aVar = this.e.get(eVar.e());
        switch (aVar != null ? (char) 11 : (char) 23) {
            case 11:
                int i4 = i + 19;
                g = i4 % 128;
                boolean z = i4 % 2 == 0;
                aVar.d(hVar);
                switch (z) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            default:
                return;
        }
    }

    public final void a(String str, String str2, o.eo.b bVar) {
        o.ek.a aVar = this.e.get(str);
        if (aVar != null) {
            aVar.c(new a.d(str2, bVar));
            int i2 = g + Opcodes.LMUL;
            i = i2 % 128;
            int i3 = i2 % 2;
        }
        int i4 = g + 55;
        i = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 998
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ek.b.k(char, int, int, java.lang.Object[]):void");
    }
}
