package o.i;

import android.graphics.ImageFormat;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodUsage;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\i.smali */
public final class i implements o.ee.a<CustomerAuthenticationMethodUsage>, o.ei.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final i a;
    public static final i b;
    public static final i d;
    public static final i e;
    private static long f;
    private static int g;
    private static int h;
    private static char i;
    private static final /* synthetic */ i[] j;
    private static int m;
    private final String c;

    static void d() {
        i = (char) 29754;
        g = 161105445;
        f = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{106, 33, -117, 89};
        $$b = Opcodes.IAND;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = 106 - r8
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r0 = o.i.i.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r8
            r4 = r2
            r8 = r7
            r7 = r6
            goto L30
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r6 = r6 + r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.i.l(byte, int, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ i[] b() {
        i[] iVarArr;
        int i2 = m + 39;
        int i3 = i2 % 128;
        h = i3;
        switch (i2 % 2 != 0) {
            case true:
                iVarArr = new i[4];
                iVarArr[0] = e;
                iVarArr[1] = a;
                iVarArr[4] = d;
                iVarArr[4] = b;
                break;
            default:
                iVarArr = new i[]{e, a, d, b};
                break;
        }
        int i4 = i3 + 89;
        m = i4 % 128;
        int i5 = i4 % 2;
        return iVarArr;
    }

    public static i valueOf(String str) {
        int i2 = m + 71;
        h = i2 % 128;
        int i3 = i2 % 2;
        i iVar = (i) Enum.valueOf(i.class, str);
        int i4 = m + Opcodes.LUSHR;
        h = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    public static i[] values() {
        int i2 = m + 11;
        h = i2 % 128;
        int i3 = i2 % 2;
        i[] iVarArr = (i[]) j.clone();
        int i4 = h + 69;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return iVarArr;
            default:
                throw null;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = h + 63;
        m = i2 % 128;
        int i3 = i2 % 2;
        CustomerAuthenticationMethodUsage c = c();
        int i4 = m + 73;
        h = i4 % 128;
        int i5 = i4 % 2;
        return c;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        m = 1;
        d();
        Object[] objArr = new Object[1];
        k(KeyEvent.normalizeMetaState(0) + 210506319, "茼⸗㼹䑽㯊烇衚", (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), "侈谒ဌ搦", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((-1128946355) - (ViewConfiguration.getScrollBarSize() >> 8), "‐끠²缟⧱Ქﳙ", (char) (ViewConfiguration.getTouchSlop() >> 8), "䷵떥閼䎫", "\u0000\u0000\u0000\u0000", objArr2);
        e = new i(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k(ImageFormat.getBitsPerPixel(0) + 1, "\u1718䈩\uf604⫿Ӏ꜓䪬ｭᜁ董ᄳ쵍鷑跓돦ꤓ", (char) (Gravity.getAbsoluteGravity(0, 0) + 39647), "鬊齔\udf9a纚", "\u0000\u0000\u0000\u0000", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        k(997589969 - ExpandableListView.getPackedPositionType(0L), "匥헗軘혐\ue632\u09d5\ue236禎\ud882⥃䃴찂\udb84笙\uecb1㋵\ue0a6", (char) (38963 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), "턪瘃㌻犘", "\u0000\u0000\u0000\u0000", objArr4);
        a = new i(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        k((-92228536) - ExpandableListView.getPackedPositionChild(0L), "嬳䢴쥪", (char) (42632 - ((Process.getThreadPriority(0) + 20) >> 6)), "䧠肴裺뮦", "\u0000\u0000\u0000\u0000", objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        k((ViewConfiguration.getDoubleTapTimeout() >> 16) - 1813136242, "簂㱓\ue922", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 12995), "躬\uedbc쒓ల", "\u0000\u0000\u0000\u0000", objArr6);
        d = new i(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        k(1411347149 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\ue670챙뀧뭏\ue25f蘊䐵첃涏묿ꎼ", (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), "쳦ὲ\ued54░", "\u0000\u0000\u0000\u0000", objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        k((-1) - ((byte) KeyEvent.getModifierMetaStateMask()), "\u1c8c墛肗ꔅ༑㙧䃴丒죙䄞쩪税", (char) KeyEvent.getDeadChar(0, 0), "\uea5d䯼졲㡂", "\u0000\u0000\u0000\u0000", objArr8);
        b = new i(intern4, 3, ((String) objArr8[0]).intern());
        j = b();
        int i2 = h + 99;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    private i(String str, int i2, String str2) {
        this.c = str2;
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = h;
        int i3 = i2 + 97;
        m = i3 % 128;
        int i4 = i3 % 2;
        String str = this.c;
        int i5 = i2 + Opcodes.LREM;
        m = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    /* renamed from: o.i.i$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\i$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            a = 1;
            int[] iArr = new int[i.values().length];
            e = iArr;
            try {
                iArr[i.e.ordinal()] = 1;
                int i = b + Opcodes.DMUL;
                a = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[i.a.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 19) + ((i3 & 19) << 1);
                a = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[i.d.ordinal()] = 3;
                int i5 = b;
                int i6 = (i5 ^ 21) + ((i5 & 21) << 1);
                a = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[i.b.ordinal()] = 4;
                int i8 = a;
                int i9 = ((i8 | 55) << 1) - (i8 ^ 55);
                b = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    private CustomerAuthenticationMethodUsage c() {
        int i2 = h + 25;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                switch (AnonymousClass4.e[ordinal()]) {
                    case 1:
                        return CustomerAuthenticationMethodUsage.Payment;
                    case 2:
                        return CustomerAuthenticationMethodUsage.WalletManagement;
                    case 3:
                        return CustomerAuthenticationMethodUsage.Sca;
                    case 4:
                        CustomerAuthenticationMethodUsage customerAuthenticationMethodUsage = CustomerAuthenticationMethodUsage.DigitalCard;
                        int i3 = h + Opcodes.DMUL;
                        m = i3 % 128;
                        int i4 = i3 % 2;
                        return customerAuthenticationMethodUsage;
                    default:
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr = new Object[1];
                        k(View.resolveSize(0, 0), "ᔗ䫭胻筣퍢㷩⾞暄鏾魙⥀\ue18c䑍䷽폅肼趎▾", (char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 47462), "騎쭝曅隹", "\u0000\u0000\u0000\u0000", objArr);
                        throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                }
            default:
                int i5 = AnonymousClass4.e[ordinal()];
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static java.util.Set<fr.antelop.sdk.authentication.CustomerAuthenticationMethodUsage> d(java.util.Set<o.i.i> r5) {
        /*
            java.util.HashSet r0 = new java.util.HashSet
            int r1 = r5.size()
            r0.<init>(r1)
            java.util.Iterator r5 = r5.iterator()
        Le:
            boolean r1 = r5.hasNext()
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L18
            r1 = r2
            goto L19
        L18:
            r1 = r3
        L19:
            switch(r1) {
                case 1: goto L1d;
                default: goto L1c;
            }
        L1c:
            return r0
        L1d:
            int r1 = o.i.i.m
            int r1 = r1 + 57
            int r4 = r1 % 128
            o.i.i.h = r4
            int r1 = r1 % 2
            java.lang.Object r1 = r5.next()
            o.i.i r1 = (o.i.i) r1
            fr.antelop.sdk.authentication.CustomerAuthenticationMethodUsage r1 = r1.c()
            r0.add(r1)
            int r1 = o.i.i.m
            int r1 = r1 + 125
            int r4 = r1 % 128
            o.i.i.h = r4
            int r1 = r1 % 2
            if (r1 == 0) goto L41
            goto L42
        L41:
            r2 = r3
        L42:
            switch(r2) {
                case 1: goto L45;
                default: goto L45;
            }
        L45:
            goto Le
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.i.d(java.util.Set):java.util.Set");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 694
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.i.k(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
