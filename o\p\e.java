package o.p;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import java.util.List;
import kotlin.text.Typography;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\e.smali */
public final class e implements g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static int g;
    private static int h;
    private static long i;
    private final h<?> a;
    private final String b;
    private final Context c;
    private final CustomCustomerAuthenticatedProcessCallback d;
    private final CustomerAuthenticatedProcess e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        f = new char[]{12539, 8999, 5930, 2854, 32556, 21270, 18213, 47900, 44830, 33550, 63249, 60216, 57091, 13182, 10108, 7016, 3951, 25468, 22374, 19282, 48987, 37713, 34632, 64346, 61268, 49989, 14245, 11176, 8103, 29609, 26557, 23475, 20354, 41883, 38795, 35712, 65435, 54150, 51075, 15308, 12267, 1005, 30704, 27641, 24567, 46030, 42947, 8478, 13028, 1732, 6901, 28398, 17122, 22268, 43713, 48844, 37584, 59132, 64202, 52944, 8874, 13998, 2730, 7861, 29363, 18102, 23228, 44702, 33460, 38534, 60045, 65164, 53915, 9853, 14957, 3681, 25142, 30258, 11430, 16220, 2940, 5965, 25430, 20314, 23364, 42873, 45940, 40808, 60228, 63346, 50024, 12050, 15126, 1810, 4877, 32523, 19214, 22276, 41766, 36631, 39733, 59186, 62240, 57126, 11230, 14292, 917, 28559, 31619, 18305, 21385, 49120, 35834, 38905, 58358, 53217, 56293, 10196, 13276, 8154, 11430, 16220, 2927, 5962, 25418, 20301, 23374, 42855, 45922, 40777, 60275, 63329, 50047, 12034, 15195, 1877, 11430, 16220, 2927, 5962, 25418, 20301, 23374, 42855, 45922, 40777, 60274, 63331, 50030, 12051, 15104, 1807, 4945, 32587, 14591, 11013, 7971, 787, 30478, 23320, 20224, 45925, 42849, 35683, 65395, 58233, 55089, 15197, 12120, 4938, 1874, 27393, 24342, 12868, 8638, 5532, 2479, 32179, 20900, 17836, 47512, 44423, 33169, 62854, 59779, 56731, 12797, 9726, 6640, 3551, 25061, 21998, 18918, 48606, 37330, 34268, 63938, 60811, 49537};
        i = 8688560519970111282L;
    }

    static void init$0() {
        $$a = new byte[]{31, 2, -38, 71};
        $$b = 85;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r6 = r6 + 4
            byte[] r0 = o.p.e.$$a
            int r7 = r7 + 102
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L32:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.e.k(int, short, short, java.lang.Object[]):void");
    }

    @Override // o.p.g
    public final void abortPrompt() {
        int i2 = g + 45;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    public e(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback, CustomerAuthenticatedProcess customerAuthenticatedProcess, h<?> hVar) {
        Object[] objArr = new Object[1];
        j((char) (Color.alpha(0) + 7291), ViewConfiguration.getWindowTouchSlop() >> 8, ((byte) KeyEvent.getModifierMetaStateMask()) + 48, objArr);
        this.b = ((String) objArr[0]).intern();
        this.c = context;
        this.d = customCustomerAuthenticatedProcessCallback;
        this.e = customerAuthenticatedProcess;
        this.a = hVar;
    }

    @Override // o.p.g
    public final void onCustomerCredentialsRequired(List<o.i.g> list) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) (((Process.getThreadPriority(0) + 20) >> 6) + 7291), Process.myPid() >> 22, Color.argb(0, 0, 0, 0) + 47, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 3512), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 46, 31 - View.combineMeasuredStates(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        switch (customCustomerAuthenticatedProcessCallback == null) {
            case false:
                int i2 = h + 109;
                g = i2 % 128;
                int i3 = i2 % 2;
                customCustomerAuthenticatedProcessCallback.onCustomerCredentialsRequired(o.d(list), this.e);
                break;
        }
        int i4 = g + 21;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return;
            default:
                int i5 = 19 / 0;
                return;
        }
    }

    @Override // o.p.g
    public final void onCustomerCredentialsInvalid(o.g.b bVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) (7291 - KeyEvent.getDeadChar(0, 0)), TextUtils.indexOf("", "", 0, 0), 47 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        j((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 78 - (ViewConfiguration.getLongPressTimeout() >> 16), Color.red(0) + 42, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        switch (customCustomerAuthenticatedProcessCallback != null ? (char) 27 : (char) 4) {
            case 4:
                return;
            default:
                int i2 = g + 49;
                h = i2 % 128;
                char c = i2 % 2 == 0 ? Typography.dollar : '`';
                LocalAuthenticationErrorReason b = bVar.b();
                switch (c) {
                    case '$':
                        customCustomerAuthenticatedProcessCallback.onCustomerCredentialsInvalid(b, this.e);
                        int i3 = 85 / 0;
                        break;
                    default:
                        customCustomerAuthenticatedProcessCallback.onCustomerCredentialsInvalid(b, this.e);
                        break;
                }
                int i4 = g + 79;
                h = i4 % 128;
                if (i4 % 2 == 0) {
                    return;
                } else {
                    return;
                }
        }
    }

    @Override // o.p.g
    public final void onProcessStart() {
        boolean z;
        int i2 = g + 83;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) (7290 - ImageFormat.getBitsPerPixel(0)), TextUtils.indexOf((CharSequence) "", '0', 0) + 1, 47 - TextUtils.getTrimmedLength(""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j((char) ((Process.getThreadPriority(0) + 20) >> 6), Process.getGidForName("") + Opcodes.LSHL, 17 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        if (customCustomerAuthenticatedProcessCallback == null) {
            z = false;
        } else {
            z = true;
        }
        switch (z) {
            case true:
                int i4 = g + 95;
                h = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        customCustomerAuthenticatedProcessCallback.onProcessStart(this.e);
                        return;
                    default:
                        customCustomerAuthenticatedProcessCallback.onProcessStart(this.e);
                        throw null;
                }
            default:
                return;
        }
    }

    @Override // o.p.g
    public final void onProcessSuccess() {
        int i2 = g + Opcodes.LUSHR;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 7290), 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 47 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j((char) TextUtils.getCapsMode("", 0, 0), 136 - (ViewConfiguration.getTapTimeout() >> 16), 18 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        switch (customCustomerAuthenticatedProcessCallback != null ? '^' : 'U') {
            case Opcodes.DUP2_X2 /* 94 */:
                int i4 = g + 25;
                h = i4 % 128;
                int i5 = i4 % 2;
                customCustomerAuthenticatedProcessCallback.onProcessSuccess(this.e);
                break;
        }
    }

    @Override // o.p.g
    public final void onError(o.bv.c cVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) (7291 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), Process.getGidForName("") + 1, 47 - (Process.myPid() >> 22), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        j((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 5208), (Process.myTid() >> 22) + Opcodes.IFNE, 19 - KeyEvent.getDeadChar(0, 0), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(cVar).toString());
        this.a.a(this.c, cVar);
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        switch (customCustomerAuthenticatedProcessCallback != null) {
            case true:
                int i2 = g + 87;
                h = i2 % 128;
                int i3 = i2 % 2;
                customCustomerAuthenticatedProcessCallback.onError(cVar.d(), this.e);
                int i4 = g + Opcodes.LNEG;
                h = i4 % 128;
                if (i4 % 2 != 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    @Override // o.p.g
    public final void onAuthenticationDeclined() {
        int i2 = g + 77;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        j((char) (7291 - (ViewConfiguration.getFadingEdgeLength() >> 16)), ViewConfiguration.getKeyRepeatTimeout() >> 16, Color.alpha(0) + 47, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j((char) ((Process.myTid() >> 22) + 7906), 173 - View.combineMeasuredStates(0, 0), View.getDefaultSize(0, 0) + 26, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback = this.d;
        switch (customCustomerAuthenticatedProcessCallback == null) {
            case true:
                break;
            default:
                int i4 = g + 1;
                h = i4 % 128;
                int i5 = i4 % 2;
                customCustomerAuthenticatedProcessCallback.onAuthenticationDeclined(this.e);
                break;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 734
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.e.j(char, int, int, java.lang.Object[]):void");
    }
}
