package o.df;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\df\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static long b;
    private static int e;
    private static int f;
    private static int g;
    private e c;
    private int d = -1;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        a = (char) 5796;
        e = 161105445;
        b = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r0 = o.df.a.$$a
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r8 = r8 + 99
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2e
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r7]
        L2e:
            int r6 = r6 + r4
            int r7 = r7 + 1
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.a.i(int, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{52, 109, 93, 87};
        $$b = 207;
    }

    public final e d(Context context) {
        int i = f + 45;
        g = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                e eVar = this.c;
                switch (eVar != null ? (char) 25 : '\'') {
                    case '\'':
                        Object[] objArr = new Object[1];
                        h(487807718 - View.resolveSize(0, 0), "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5", (char) View.combineMeasuredStates(0, 0), "\ue60cፚ찝⏎", "\u0000\u0000\u0000\u0000", objArr);
                        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
                        Object[] objArr2 = new Object[1];
                        h((-1263768405) - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), "\ue1a3１\uf886嶱䇒ᖌኺ⊟䲯", (char) TextUtils.getTrimmedLength(""), "ꯝ걬➴ෑ", "\u0000\u0000\u0000\u0000", objArr2);
                        String string = sharedPreferences.getString(((String) objArr2[0]).intern(), "");
                        d dVar = new d();
                        b bVar = new b();
                        c cVar = new c();
                        switch (string.equals(bVar.a()) ? 'b' : (char) 25) {
                            case Opcodes.FADD /* 98 */:
                                int i2 = f + 63;
                                g = i2 % 128;
                                if (i2 % 2 == 0) {
                                    this.c = bVar;
                                    throw null;
                                }
                                this.c = bVar;
                                break;
                            default:
                                if (!string.equals(cVar.a())) {
                                    this.c = dVar;
                                    break;
                                } else {
                                    this.c = cVar;
                                    int i3 = f + Opcodes.DMUL;
                                    g = i3 % 128;
                                    int i4 = i3 % 2;
                                    break;
                                }
                        }
                        return this.c;
                    default:
                        return eVar;
                }
        }
    }

    public final void e(Context context, o.bb.d dVar) {
        d dVar2 = new d();
        b bVar = new b();
        c cVar = new c();
        if (!b.d(dVar)) {
            switch (c.d(dVar) ? '`' : 'B') {
                case Opcodes.IADD /* 96 */:
                    this.c = cVar;
                    break;
                default:
                    this.c = dVar2;
                    break;
            }
        } else {
            int i = g + 55;
            f = i % 128;
            char c = i % 2 != 0 ? '\n' : '2';
            this.c = bVar;
            switch (c) {
                case '2':
                    break;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }
        Object[] objArr = new Object[1];
        h(487807718 - TextUtils.getOffsetAfter("", 0), "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5", (char) (ImageFormat.getBitsPerPixel(0) + 1), "\ue60cፚ찝⏎", "\u0000\u0000\u0000\u0000", objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        h((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1263768406, "\ue1a3１\uf886嶱䇒ᖌኺ⊟䲯", (char) ((-1) - Process.getGidForName("")), "ꯝ걬➴ෑ", "\u0000\u0000\u0000\u0000", objArr2);
        edit.putString(((String) objArr2[0]).intern(), this.c.a()).commit();
        int i2 = f + 31;
        g = i2 % 128;
        if (i2 % 2 == 0) {
            int i3 = 20 / 0;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0026, code lost:
    
        if (r12.d != (-1)) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final int c(android.content.Context r13) {
        /*
            r12 = this;
            int r0 = o.df.a.f
            int r1 = r0 + 33
            int r2 = r1 % 128
            o.df.a.g = r2
            int r1 = r1 % 2
            r2 = 1
            r3 = 0
            if (r1 != 0) goto L10
            r1 = r3
            goto L11
        L10:
            r1 = r2
        L11:
            r4 = -1
            switch(r1) {
                case 1: goto L18;
                default: goto L15;
            }
        L15:
            int r1 = r12.d
            goto L23
        L18:
            int r1 = r12.d
            if (r1 == r4) goto L1e
            r1 = r3
            goto L1f
        L1e:
            r1 = r2
        L1f:
            switch(r1) {
                case 0: goto L28;
                default: goto L22;
            }
        L22:
            goto L33
        L23:
            r5 = 56
            int r5 = r5 / r3
            if (r1 == r4) goto L33
        L28:
            int r0 = r0 + 5
            int r13 = r0 % 128
            o.df.a.g = r13
            int r0 = r0 % 2
            int r13 = r12.d
            return r13
        L33:
            r0 = 487807718(0x1d135ae6, float:1.9502281E-21)
            r4 = 0
            int r1 = android.widget.ExpandableListView.getPackedPositionGroup(r4)
            int r6 = r0 - r1
            java.lang.String r7 = "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5"
            int r0 = android.os.Process.myTid()
            int r0 = r0 >> 22
            char r8 = (char) r0
            java.lang.String r9 = "\ue60cፚ찝⏎"
            java.lang.String r10 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r0 = new java.lang.Object[r2]
            r11 = r0
            h(r6, r7, r8, r9, r10, r11)
            r0 = r0[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            android.content.SharedPreferences r13 = r13.getSharedPreferences(r0, r3)
            java.lang.String r0 = ""
            r1 = 48
            int r0 = android.text.TextUtils.indexOf(r0, r1, r3)
            r1 = 2072699116(0x7b8ae0ec, float:1.4421978E36)
            int r6 = r0 + r1
            java.lang.String r7 = "㬐\udc24㉠댸ᴣ\uf621桍执懎"
            int r0 = android.widget.ExpandableListView.getPackedPositionType(r4)
            char r8 = (char) r0
            java.lang.String r9 = "\ueb16諠ቻ컓"
            java.lang.String r10 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r0 = new java.lang.Object[r2]
            r11 = r0
            h(r6, r7, r8, r9, r10, r11)
            r0 = r0[r3]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r13 = r13.getInt(r0, r3)
            r12.d = r13
            return r13
        L8a:
            r13 = move-exception
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.a.c(android.content.Context):int");
    }

    public final void e(Context context) {
        int i = f + 41;
        g = i % 128;
        int i2 = i % 2;
        this.d = c(context) + 1;
        Object[] objArr = new Object[1];
        h(487807718 - View.resolveSizeAndState(0, 0, 0), "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5", (char) ((Process.getThreadPriority(0) + 20) >> 6), "\ue60cፚ찝⏎", "\u0000\u0000\u0000\u0000", objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        h(View.MeasureSpec.getSize(0) + 2072699115, "㬐\udc24㉠댸ᴣ\uf621桍执懎", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), "\ueb16諠ቻ컓", "\u0000\u0000\u0000\u0000", objArr2);
        edit.putInt(((String) objArr2[0]).intern(), this.d).commit();
        int i3 = g + 1;
        f = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x008a. Please report as an issue. */
    public final void a(Context context) {
        int i = f + 31;
        g = i % 128;
        if (i % 2 == 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (this.d != 0 ? '\t' : '+') {
            case '\t':
                this.d = 0;
                Object[] objArr = new Object[1];
                h(487807717 - TextUtils.lastIndexOf("", '0'), "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5", (char) TextUtils.getTrimmedLength(""), "\ue60cፚ찝⏎", "\u0000\u0000\u0000\u0000", objArr);
                SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
                Object[] objArr2 = new Object[1];
                h((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 2072699115, "㬐\udc24㉠댸ᴣ\uf621桍执懎", (char) Color.green(0), "\ueb16諠ቻ컓", "\u0000\u0000\u0000\u0000", objArr2);
                edit.putInt(((String) objArr2[0]).intern(), this.d).commit();
                int i2 = g + Opcodes.DMUL;
                f = i2 % 128;
                switch (i2 % 2 != 0 ? '.' : '*') {
                }
                return;
            default:
                return;
        }
    }

    public final void b(Context context) {
        this.c = new d();
        Object[] objArr = new Object[1];
        h(((Process.getThreadPriority(0) + 20) >> 6) + 487807718, "辀̆⮹߳落\uf25a荈냢晬㘞⺉辟塽\ue631ⳍ왐䡥튰\ue57c꿴쭲㼣뻼ᗫ뀜ᑁ蹄⢂ඖ轌虽㕒ꡎ\ue02e镑芺卥ᅉ鸞\ue172\ue361纱᪺予㘾ꢀ\ue6d5", (char) (Color.rgb(0, 0, 0) + 16777216), "\ue60cፚ찝⏎", "\u0000\u0000\u0000\u0000", objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        h((ViewConfiguration.getJumpTapTimeout() >> 16) - 1263768405, "\ue1a3１\uf886嶱䇒ᖌኺ⊟䲯", (char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), "ꯝ걬➴ෑ", "\u0000\u0000\u0000\u0000", objArr2);
        edit.putString(((String) objArr2[0]).intern(), this.c.a()).commit();
        a(context);
        int i = g + Opcodes.LSUB;
        f = i % 128;
        switch (i % 2 != 0 ? '^' : 'I') {
            case Opcodes.DUP2_X2 /* 94 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0018. Please report as an issue. */
    private static void h(int i, String str, char c, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char c2;
        int i2 = 0;
        if (str3 != null) {
            cArr = str3.toCharArray();
            int i3 = $10 + 7;
            $11 = i3 % 128;
            switch (i3 % 2 != 0) {
            }
        } else {
            cArr = str3;
        }
        char[] cArr3 = cArr;
        char[] charArray = str2 != null ? str2.toCharArray() : str2;
        switch (str != null ? '_' : 'Y') {
            case Opcodes.DUP /* 89 */:
                cArr2 = str;
                break;
            default:
                cArr2 = str.toCharArray();
                int i4 = $11 + 41;
                $10 = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
        o oVar = new o();
        int length = charArray.length;
        char[] cArr4 = new char[length];
        int length2 = cArr3.length;
        char[] cArr5 = new char[length2];
        System.arraycopy(charArray, 0, cArr4, 0, length);
        System.arraycopy(cArr3, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c);
        cArr5[2] = (char) (cArr5[2] + ((char) i));
        int length3 = cArr2.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(10 - KeyEvent.getDeadChar(i2, i2), (char) (20954 - TextUtils.indexOf("", "", i2)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 344);
                    byte b2 = (byte) i2;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    i(b2, b3, b3, objArr3);
                    String str4 = (String) objArr3[i2];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i2] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 9, (char) ((-1) - TextUtils.lastIndexOf("", '0', i2, i2)), 207 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                        byte b4 = (byte) i2;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        i(b4, b5, (byte) (b5 + 2), objArr5);
                        String str5 = (String) objArr5[i2];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i2] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i6 = cArr4[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr5[intValue]);
                        objArr6[1] = Integer.valueOf(i6);
                        objArr6[i2] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(i2) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i2) == 0.0d ? 0 : -1)) + 11, (char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), 281 - (ViewConfiguration.getTapTimeout() >> 16));
                            byte b6 = (byte) i2;
                            Object[] objArr7 = new Object[1];
                            i(b6, b6, (byte) $$a.length, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 != null) {
                                c2 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 20, (char) (14687 - TextUtils.getOffsetBefore("", 0)), 111 - ExpandableListView.getPackedPositionChild(0L));
                                byte b7 = (byte) 0;
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                i(b7, b8, (byte) (b8 | 7), objArr9);
                                c2 = 2;
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((cArr4[intValue2] ^ r5[oVar.e]) ^ (b ^ 6565854932352255525L)) ^ ((int) (e ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                            oVar.e++;
                            i2 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }
}
