package o.bl;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.m;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\b.smali */
public final class b implements o.bl.a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] d;
    private static int f;
    private static int g;
    private static char j;
    private a b;
    private o.bi.a c;
    private o.bi.d e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        f = 1;
        h();
        ViewConfiguration.getMaximumFlingVelocity();
        ImageFormat.getBitsPerPixel(0);
        ImageFormat.getBitsPerPixel(0);
        ExpandableListView.getPackedPositionForGroup(0);
        TextUtils.indexOf("", "", 0);
        Color.rgb(0, 0, 0);
        ViewConfiguration.getEdgeSlop();
        AudioTrack.getMaxVolume();
        TextUtils.indexOf("", "", 0);
        PointF.length(0.0f, 0.0f);
        View.getDefaultSize(0, 0);
        SystemClock.uptimeMillis();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        Color.green(0);
        ExpandableListView.getPackedPositionType(0L);
        ViewConfiguration.getJumpTapTimeout();
        ViewConfiguration.getZoomControlsTimeout();
        ViewConfiguration.getGlobalActionKeyTimeout();
        ViewConfiguration.getScrollDefaultDelay();
        ViewConfiguration.getPressedStateDuration();
        ViewConfiguration.getFadingEdgeLength();
        ViewConfiguration.getFadingEdgeLength();
        ViewConfiguration.getLongPressTimeout();
        TextUtils.getOffsetAfter("", 0);
        View.MeasureSpec.getSize(0);
        MotionEvent.axisFromString("");
        int i = f + 35;
        g = i % 128;
        switch (i % 2 != 0 ? '5' : (char) 30) {
            case Opcodes.SALOAD /* 53 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void h() {
        a = 874635451;
        d = new char[]{30582, 30554, 30503, 30530, 30535, 30532, 30577, 30572, 30498, 30511, 30561, 30564, 30585, 30578, 30568, 30517, 30575, 30569, 30529, 30540, 30591, 29844, 30570, 30502, 30560, 30576, 30584, 30557, 29845, 30562, 30563, 30542, 30534, 30571, 30567, 30559, 30565, 30587, 30537, 30581, 30574, 30590, 30573, 30579, 30589, 30566, 30556, 30588, 30586};
        j = (char) 17042;
    }

    static void init$0() {
        $$a = new byte[]{38, 115, -18, 59};
        $$b = Opcodes.INEG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r5, int r6, int r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 2
            int r5 = r5 + 4
            byte[] r0 = o.bl.b.$$a
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r7 = r7 + 69
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L14
            r4 = r5
            r3 = r2
            goto L26
        L14:
            r3 = r2
        L15:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r6) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            r4 = r0[r5]
        L26:
            int r5 = r5 + 1
            int r7 = r7 + r4
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.b.l(int, int, int, java.lang.Object[]):void");
    }

    public b(Context context) {
        a(context);
    }

    private void a(Context context) {
        g.c();
        Object[] objArr = new Object[1];
        i(10 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", 28 - Color.alpha(0), (Process.myTid() >> 22) + 299, false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(View.combineMeasuredStates(0, 0) + 10, "\u0003\u0011,&/&\u001f,$\u0019", (byte) (97 - (ViewConfiguration.getLongPressTimeout() >> 16)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.c = null;
        this.e = null;
        Object[] objArr3 = new Object[1];
        i(ExpandableListView.getPackedPositionGroup(0L) + 26, "\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe", KeyEvent.normalizeMetaState(0) + 47, TextUtils.lastIndexOf("", '0') + 300, false, objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        Object[] objArr4 = new Object[1];
        k(3 - Drawable.resolveOpacity(0, 0), "\u0004\u0007㘖", (byte) (TextUtils.lastIndexOf("", '0', 0, 0) + 31), objArr4);
        String d2 = new o.dd.e(context).d(sharedPreferences.getString(((String) objArr4[0]).intern(), ""));
        switch (d2 != null ? (char) 0 : '2') {
            case '2':
                break;
            default:
                int i = g + 31;
                f = i % 128;
                int i2 = i % 2;
                switch (d2.isEmpty() ? 'S' : ')') {
                    case Opcodes.AASTORE /* 83 */:
                        break;
                    default:
                        try {
                            e(new o.eg.b(d2));
                            break;
                        } catch (o.eg.d e) {
                            g.c();
                            Object[] objArr5 = new Object[1];
                            i(View.MeasureSpec.getMode(0) + 9, "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", 28 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 298, false, objArr5);
                            String intern2 = ((String) objArr5[0]).intern();
                            Object[] objArr6 = new Object[1];
                            k(10 - Color.red(0), "\u0003\u0011,&/&\u001f,$\u0019", (byte) (Process.getGidForName("") + 98), objArr6);
                            g.a(intern2, ((String) objArr6[0]).intern(), e);
                            return;
                        }
                }
        }
        g.c();
        Object[] objArr7 = new Object[1];
        i((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 9, "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", 27 - ImageFormat.getBitsPerPixel(0), 299 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), false, objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        k(ImageFormat.getBitsPerPixel(0) + 24, "\u0003\u0011,&/&\u001f,$\u0019\n\t\n\u000b,\"%\u0010\u0013/\",㘷", (byte) (56 - KeyEvent.normalizeMetaState(0)), objArr8);
        g.d(intern3, ((String) objArr8[0]).intern());
        int i3 = g + 83;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:28:0x0497, code lost:
    
        r8 = null;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:39:0x04b6. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean e(o.eg.b r30) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 1288
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.b.e(o.eg.b):boolean");
    }

    public final boolean e(Context context, o.eg.b bVar) throws o.eg.d {
        g.c();
        Object[] objArr = new Object[1];
        i((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 9, "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", View.MeasureSpec.getMode(0) + 28, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 299, false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(12 - View.resolveSize(0, 0), "\u0006\u001b(/$\u0017\u0011\u001a\u0011\u0018*\u0011", (byte) ((ViewConfiguration.getTapTimeout() >> 16) + 8), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        boolean e = e(bVar);
        Object[] objArr3 = new Object[1];
        i((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 26, "\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe", 46 - MotionEvent.axisFromString(""), TextUtils.indexOf((CharSequence) "", '0', 0) + 300, false, objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        String a2 = new o.dd.e(context).a(bVar.b());
        g.c();
        Object[] objArr4 = new Object[1];
        i(9 - (Process.myPid() >> 22), "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", (ViewConfiguration.getEdgeSlop() >> 16) + 28, 299 - View.getDefaultSize(0, 0), false, objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        k((ViewConfiguration.getKeyRepeatDelay() >> 16) + 44, "\u0006\u001b(/$\u0017\u0011\u001a\u0011\u0018*\u0011\n\t\f,&\u0017-.\u0007\u0011\n\b0\u0011\u001d\u001b+\u0017\u001e\f(+\u001f\u0011\u0010,\u0017\n\u001f/.\f", (byte) (121 - TextUtils.indexOf("", "", 0, 0)), objArr5);
        g.d(intern2, ((String) objArr5[0]).intern());
        SharedPreferences.Editor edit = sharedPreferences.edit();
        Object[] objArr6 = new Object[1];
        k(View.getDefaultSize(0, 0) + 3, "\u0004\u0007㘖", (byte) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 29), objArr6);
        edit.putString(((String) objArr6[0]).intern(), a2).commit();
        int i = g + 77;
        f = i % 128;
        switch (i % 2 != 0 ? '?' : '0') {
            case '?':
                return e;
            default:
                throw null;
        }
    }

    @Override // o.bl.a
    public final String e() {
        int i = g;
        int i2 = i + Opcodes.LUSHR;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = i + 29;
                f = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.bl.a
    public final String b() {
        int i = g;
        int i2 = i + 109;
        f = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 55;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return null;
        }
    }

    @Override // o.bl.a
    public final o.bi.a a() {
        int i = g;
        int i2 = i + 89;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                o.bi.a aVar = this.c;
                int i3 = i + 53;
                f = i3 % 128;
                switch (i3 % 2 == 0 ? '^' : '#') {
                    case '#':
                        return aVar;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.bl.a
    public final o.bi.d c() {
        o.bi.d dVar;
        int i = f + 93;
        int i2 = i % 128;
        g = i2;
        switch (i % 2 != 0 ? '\'' : ',') {
            case '\'':
                dVar = this.e;
                int i3 = 5 / 0;
                break;
            default:
                dVar = this.e;
                break;
        }
        int i4 = i2 + 55;
        f = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    @Override // o.bl.a
    public final String d() {
        Object obj;
        int i = g + Opcodes.LUSHR;
        f = i % 128;
        switch (i % 2 == 0 ? '(' : 'A') {
            case '(':
                Object[] objArr = new Object[1];
                i((ViewConfiguration.getPressedStateDuration() >>> 24) + Opcodes.FMUL, "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", 103 - View.MeasureSpec.makeMeasureSpec(0, 1), 27210 - Color.alpha(1), false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                i(9 - (ViewConfiguration.getPressedStateDuration() >> 16), "\u0007￩\u000b\b\u000f\u0002�\ufffe\u000bￛ\ufffa￼\u0004\ufffe\u0007�ￜ\b\u0007\uffff\u0002\u0000\u000e\u000b\ufffa\r\u0002\b", 28 - View.MeasureSpec.makeMeasureSpec(0, 0), Color.alpha(0) + 299, false, objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    public final a j() {
        int i = g + Opcodes.DDIV;
        f = i % 128;
        switch (i % 2 == 0 ? '2' : 'X') {
            case '2':
                throw null;
            default:
                return this.b;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\b$a.smali */
    public static final class a implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final a a;
        private static a b;
        public static final a d;
        private static final /* synthetic */ a[] e;
        private static int f;
        private static long g;
        private static char h;
        private static int i;
        private static int j;
        private final String c;

        static void a() {
            h = (char) 39600;
            f = 161105445;
            g = 6565854932352255525L;
        }

        static void init$0() {
            $$a = new byte[]{77, -122, 105, 67};
            $$b = 108;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0039). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(int r7, short r8, byte r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 * 4
                int r7 = 4 - r7
                byte[] r0 = o.bl.b.a.$$a
                int r9 = r9 * 2
                int r9 = 1 - r9
                int r8 = 106 - r8
                byte[] r1 = new byte[r9]
                int r9 = r9 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r8 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                goto L39
            L1a:
                r3 = r2
            L1b:
                r5 = r8
                r8 = r7
                r7 = r5
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r9) goto L2b
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2b:
                int r3 = r3 + 1
                r4 = r0[r8]
                r5 = r8
                r8 = r7
                r7 = r5
                r6 = r10
                r10 = r9
                r9 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r6
            L39:
                int r9 = -r9
                int r7 = r7 + 1
                int r8 = r8 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1b
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bl.b.a.l(int, short, byte, java.lang.Object[]):void");
        }

        private static /* synthetic */ a[] d() {
            int i2 = j + Opcodes.DDIV;
            int i3 = i2 % 128;
            i = i3;
            int i4 = i2 % 2;
            a[] aVarArr = {a, b, d};
            int i5 = i3 + Opcodes.DNEG;
            j = i5 % 128;
            int i6 = i5 % 2;
            return aVarArr;
        }

        public static a valueOf(String str) {
            int i2 = i + 83;
            j = i2 % 128;
            int i3 = i2 % 2;
            a aVar = (a) Enum.valueOf(a.class, str);
            int i4 = j + 79;
            i = i4 % 128;
            int i5 = i4 % 2;
            return aVar;
        }

        public static a[] values() {
            int i2 = j + 89;
            i = i2 % 128;
            int i3 = i2 % 2;
            a[] aVarArr = (a[]) e.clone();
            int i4 = j + 77;
            i = i4 % 128;
            int i5 = i4 % 2;
            return aVarArr;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            j = 1;
            a();
            Object[] objArr = new Object[1];
            k(ViewConfiguration.getScrollBarFadeDuration() >> 16, "쵸衊㏛鑅", (char) (54719 - View.getDefaultSize(0, 0)), "㠵捡뾁៕", "\u0000\u0000\u0000\u0000", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k(Process.myTid() >> 22, "쵸衊㏛鑅", (char) (54719 - ((Process.getThreadPriority(0) + 20) >> 6)), "㠵捡뾁៕", "\u0000\u0000\u0000\u0000", objArr2);
            a = new a(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k(ViewConfiguration.getScrollDefaultDelay() >> 16, "퓢ㅻ䓜퉲伹䘷쏞狷丗锁", (char) ((-1) - Process.getGidForName("")), "\uf57d䏒ᆁᖲ", "\u0000\u0000\u0000\u0000", objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k(KeyEvent.getMaxKeyCode() >> 16, "퓢ㅻ䓜퉲伹䘷쏞狷丗锁", (char) TextUtils.indexOf("", "", 0), "\uf57d䏒ᆁᖲ", "\u0000\u0000\u0000\u0000", objArr4);
            b = new a(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k((-1) - TextUtils.indexOf((CharSequence) "", '0'), "ɀ燐⭲ӭ", (char) (1969 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), "駘쏥놚ᰇ", "\u0000\u0000\u0000\u0000", objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            k((-1) - TextUtils.lastIndexOf("", '0', 0, 0), "ɀ燐⭲ӭ", (char) (1968 - TextUtils.lastIndexOf("", '0')), "駘쏥놚ᰇ", "\u0000\u0000\u0000\u0000", objArr6);
            d = new a(intern3, 2, ((String) objArr6[0]).intern());
            e = d();
            int i2 = i + 13;
            j = i2 % 128;
            switch (i2 % 2 == 0 ? 'b' : 'B') {
                case Opcodes.FADD /* 98 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        private a(String str, int i2, String str2) {
            this.c = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = i + 67;
            j = i2 % 128;
            switch (i2 % 2 == 0 ? '/' : Typography.amp) {
                case '&':
                    return this.c;
                default:
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 702
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bl.b.a.k(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
        }
    }

    @Override // o.bl.a
    public final void c(Context context) {
        int i = f + 65;
        g = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        i((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 26, "\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe", 47 - (ViewConfiguration.getScrollBarSize() >> 8), 298 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), false, objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        k((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 2, "\u0004\u0007㘖", (byte) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 29), objArr2);
        edit.putString(((String) objArr2[0]).intern(), "").commit();
        int i3 = g + 75;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(int r18, java.lang.String r19, int r20, int r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 538
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.b.i(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }

    private static void k(int i, String str, byte b, Object[] objArr) {
        char[] cArr;
        int i2;
        int length;
        char[] cArr2;
        if (str != null) {
            int i3 = $11 + 63;
            $10 = i3 % 128;
            if (i3 % 2 != 0) {
                str.toCharArray();
                throw null;
            }
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr3 = cArr;
        m mVar = new m();
        char[] cArr4 = d;
        char c = '0';
        int i4 = -1401577988;
        if (cArr4 != null) {
            int i5 = $11 + 53;
            $10 = i5 % 128;
            if (i5 % 2 != 0) {
                length = cArr4.length;
                cArr2 = new char[length];
            } else {
                length = cArr4.length;
                cArr2 = new char[length];
            }
            int i6 = 0;
            while (true) {
                switch (i6 < length ? '.' : (char) 4) {
                    case 4:
                        cArr4 = cArr2;
                        break;
                    default:
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr4[i6])};
                            Object obj = o.e.a.s.get(Integer.valueOf(i4));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(17 - View.getDefaultSize(0, 0), (char) ((-1) - TextUtils.indexOf("", c, 0)), (Process.myTid() >> 22) + 76);
                                byte b2 = (byte) 0;
                                Object[] objArr3 = new Object[1];
                                l(b2, b2, (byte) $$a.length, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr2[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i6++;
                            c = '0';
                            i4 = -1401577988;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(j)};
            Object obj2 = o.e.a.s.get(-1401577988);
            float f2 = 0.0f;
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 76 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)));
                byte b3 = (byte) 0;
                Object[] objArr5 = new Object[1];
                l(b3, b3, (byte) $$a.length, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i];
            if (i % 2 != 0) {
                i2 = i - 1;
                cArr5[i2] = (char) (cArr3[i2] - b);
            } else {
                i2 = i;
            }
            switch (i2 > 1) {
                case true:
                    mVar.b = 0;
                    while (true) {
                        switch (mVar.b < i2 ? ';' : 'A') {
                            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                break;
                            default:
                                int i7 = $11 + 61;
                                $10 = i7 % 128;
                                int i8 = i7 % 2;
                                mVar.e = cArr3[mVar.b];
                                mVar.a = cArr3[mVar.b + 1];
                                switch (mVar.e == mVar.a ? '`' : '#') {
                                    case '#':
                                        try {
                                            Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                            Object obj3 = o.e.a.s.get(696901393);
                                            if (obj3 == null) {
                                                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10, (char) ((TypedValue.complexToFloat(0) > f2 ? 1 : (TypedValue.complexToFloat(0) == f2 ? 0 : -1)) + 8856), 324 - Color.red(0));
                                                byte b4 = (byte) 0;
                                                byte b5 = b4;
                                                Object[] objArr7 = new Object[1];
                                                l(b4, b5, b5, objArr7);
                                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(696901393, obj3);
                                            }
                                            switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() != mVar.h) {
                                                case true:
                                                    if (mVar.c != mVar.d) {
                                                        int i9 = (mVar.c * charValue) + mVar.h;
                                                        int i10 = (mVar.d * charValue) + mVar.i;
                                                        cArr5[mVar.b] = cArr4[i9];
                                                        cArr5[mVar.b + 1] = cArr4[i10];
                                                        break;
                                                    } else {
                                                        mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                        mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                        int i11 = (mVar.c * charValue) + mVar.i;
                                                        int i12 = (mVar.d * charValue) + mVar.h;
                                                        cArr5[mVar.b] = cArr4[i11];
                                                        cArr5[mVar.b + 1] = cArr4[i12];
                                                        break;
                                                    }
                                                default:
                                                    try {
                                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                        Object obj4 = o.e.a.s.get(1075449051);
                                                        if (obj4 == null) {
                                                            Class cls4 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0', 0) + 12, (char) TextUtils.getTrimmedLength(""), 66 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                                                            byte b6 = (byte) 0;
                                                            byte b7 = b6;
                                                            Object[] objArr9 = new Object[1];
                                                            l(b6, b7, (byte) (b7 + 1), objArr9);
                                                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                            o.e.a.s.put(1075449051, obj4);
                                                        }
                                                        int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                        int i13 = (mVar.d * charValue) + mVar.h;
                                                        cArr5[mVar.b] = cArr4[intValue];
                                                        cArr5[mVar.b + 1] = cArr4[i13];
                                                        break;
                                                    } catch (Throwable th2) {
                                                        Throwable cause2 = th2.getCause();
                                                        if (cause2 == null) {
                                                            throw th2;
                                                        }
                                                        throw cause2;
                                                    }
                                            }
                                        } catch (Throwable th3) {
                                            Throwable cause3 = th3.getCause();
                                            if (cause3 == null) {
                                                throw th3;
                                            }
                                            throw cause3;
                                        }
                                    default:
                                        cArr5[mVar.b] = (char) (mVar.e - b);
                                        cArr5[mVar.b + 1] = (char) (mVar.a - b);
                                        break;
                                }
                                mVar.b += 2;
                                f2 = 0.0f;
                        }
                    }
            }
            int i14 = 0;
            while (i14 < i) {
                cArr5[i14] = (char) (cArr5[i14] ^ 13722);
                i14++;
                int i15 = $10 + 61;
                $11 = i15 % 128;
                int i16 = i15 % 2;
            }
            objArr[0] = new String(cArr5);
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
