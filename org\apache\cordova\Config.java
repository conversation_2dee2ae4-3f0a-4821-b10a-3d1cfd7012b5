package org.apache.cordova;

import android.app.Activity;
import java.util.List;

@Deprecated
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\Config.smali */
public class Config {
    private static final String TAG = "Config";
    static ConfigXmlParser parser;

    private Config() {
    }

    public static void init(Activity action) {
        ConfigXmlParser configXmlParser = new ConfigXmlParser();
        parser = configXmlParser;
        configXmlParser.parse(action);
        parser.getPreferences().setPreferencesBundle(action.getIntent().getExtras());
    }

    public static void init() {
        if (parser == null) {
            parser = new ConfigXmlParser();
        }
    }

    public static String getStartUrl() {
        ConfigXmlParser configXmlParser = parser;
        if (configXmlParser == null) {
            return "file:///android_asset/www/index.html";
        }
        return configXmlParser.getLaunchUrl();
    }

    public static String getErrorUrl() {
        return parser.getPreferences().getString("errorurl", null);
    }

    public static List<PluginEntry> getPluginEntries() {
        return parser.getPluginEntries();
    }

    public static CordovaPreferences getPreferences() {
        return parser.getPreferences();
    }

    public static boolean isInitialized() {
        return parser != null;
    }
}
