package com.google.android.gms.tapandpay.issuer;

import android.content.Intent;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.ReflectedParcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\UserAddress.smali */
public final class UserAddress extends AbstractSafeParcelable implements ReflectedParcelable {
    public static final Parcelable.Creator<UserAddress> CREATOR = new zzm();
    String zza;
    String zzb;
    String zzc;
    String zzd;
    String zze;
    String zzf;
    String zzg;
    String zzh;
    String zzi;
    String zzj;
    String zzk;
    String zzl;
    boolean zzm;
    String zzn;
    String zzo;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\UserAddress$Builder.smali */
    public static class Builder {
        private String zza;
        private String zzb;
        private String zzc;
        private String zzd;
        private String zze;
        private String zzf;
        private String zzg;
        private String zzh;

        public UserAddress build() {
            return new UserAddress(this.zza, this.zzb, this.zzc, null, null, null, this.zzd, this.zze, this.zzf, this.zzg, null, this.zzh, false, null, null);
        }

        public Builder setAddress1(String str) {
            this.zzb = str;
            return this;
        }

        public Builder setAddress2(String str) {
            this.zzc = str;
            return this;
        }

        public Builder setAdministrativeArea(String str) {
            this.zzd = str;
            return this;
        }

        public Builder setCountryCode(String str) {
            this.zzf = str;
            return this;
        }

        public Builder setLocality(String str) {
            this.zze = str;
            return this;
        }

        public Builder setName(String str) {
            this.zza = str;
            return this;
        }

        public Builder setPhoneNumber(String str) {
            this.zzh = str;
            return this;
        }

        public Builder setPostalCode(String str) {
            this.zzg = str;
            return this;
        }
    }

    UserAddress() {
    }

    public static UserAddress fromIntent(Intent data) {
        if (data == null || !data.hasExtra("com.google.android.gms.identity.intents.EXTRA_ADDRESS")) {
            return null;
        }
        return (UserAddress) data.getParcelableExtra("com.google.android.gms.identity.intents.EXTRA_ADDRESS");
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public String getAddress1() {
        return this.zzb;
    }

    public String getAddress2() {
        return this.zzc;
    }

    public String getAddress3() {
        return this.zzd;
    }

    public String getAddress4() {
        return this.zze;
    }

    public String getAddress5() {
        return this.zzf;
    }

    public String getAdministrativeArea() {
        return this.zzg;
    }

    public String getCompanyName() {
        return this.zzn;
    }

    public String getCountryCode() {
        return this.zzi;
    }

    public String getEmailAddress() {
        return this.zzo;
    }

    public String getLocality() {
        return this.zzh;
    }

    public String getName() {
        return this.zza;
    }

    public String getPhoneNumber() {
        return this.zzl;
    }

    public String getPostalCode() {
        return this.zzj;
    }

    public String getSortingCode() {
        return this.zzk;
    }

    public boolean isPostBox() {
        return this.zzm;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel out, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(out);
        SafeParcelWriter.writeString(out, 2, this.zza, false);
        SafeParcelWriter.writeString(out, 3, this.zzb, false);
        SafeParcelWriter.writeString(out, 4, this.zzc, false);
        SafeParcelWriter.writeString(out, 5, this.zzd, false);
        SafeParcelWriter.writeString(out, 6, this.zze, false);
        SafeParcelWriter.writeString(out, 7, this.zzf, false);
        SafeParcelWriter.writeString(out, 8, this.zzg, false);
        SafeParcelWriter.writeString(out, 9, this.zzh, false);
        SafeParcelWriter.writeString(out, 10, this.zzi, false);
        SafeParcelWriter.writeString(out, 11, this.zzj, false);
        SafeParcelWriter.writeString(out, 12, this.zzk, false);
        SafeParcelWriter.writeString(out, 13, this.zzl, false);
        SafeParcelWriter.writeBoolean(out, 14, this.zzm);
        SafeParcelWriter.writeString(out, 15, this.zzn, false);
        SafeParcelWriter.writeString(out, 16, this.zzo, false);
        SafeParcelWriter.finishObjectHeader(out, beginObjectHeader);
    }

    UserAddress(String str, String str2, String str3, String str4, String str5, String str6, String str7, String str8, String str9, String str10, String str11, String str12, boolean z, String str13, String str14) {
        this.zza = str;
        this.zzb = str2;
        this.zzc = str3;
        this.zzd = str4;
        this.zze = str5;
        this.zzf = str6;
        this.zzg = str7;
        this.zzh = str8;
        this.zzi = str9;
        this.zzj = str10;
        this.zzk = str11;
        this.zzl = str12;
        this.zzm = z;
        this.zzn = str13;
        this.zzo = str14;
    }
}
