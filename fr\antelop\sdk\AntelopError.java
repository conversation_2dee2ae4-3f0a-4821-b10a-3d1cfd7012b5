package fr.antelop.sdk;

import o.bv.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\AntelopError.smali */
public final class AntelopError {
    private final c innerError;

    public AntelopError(c cVar) {
        this.innerError = cVar;
    }

    public final AntelopErrorCode getCode() {
        return this.innerError.c();
    }

    public final int getReason() {
        return this.innerError.e();
    }

    public final String getMessage() {
        return this.innerError.b();
    }

    public final String toString() {
        return new StringBuilder("AntelopError").append(this.innerError.toString()).toString();
    }
}
