package o.es;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.ArrayList;
import o.er.p;
import o.v.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\es\d.smali */
public final class d<T> {
    private static int b = 0;
    private static int c = 1;
    private final a<T> e;

    public d(T t, a<T> aVar) {
        this.e = new a<>(t, aVar.e(), aVar.b(), aVar.c());
    }

    public final b d() {
        int i = c;
        int i2 = ((i | Opcodes.DSUB) << 1) - (i ^ Opcodes.DSUB);
        b = i2 % 128;
        int i3 = i2 % 2;
        b e = this.e.e();
        int i4 = c;
        int i5 = (i4 ^ 41) + ((i4 & 41) << 1);
        b = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 15 : ')') {
            case ')':
                throw null;
            default:
                return e;
        }
    }

    public final T c() {
        T d;
        int i = b + 69;
        c = i % 128;
        switch (i % 2 == 0 ? 'Z' : (char) 21) {
            case 21:
                d = this.e.d();
                break;
            default:
                d = this.e.d();
                int i2 = 14 / 0;
                break;
        }
        int i3 = b + 41;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final n a() throws WalletValidationException {
        o.eo.e c2 = this.e.c();
        p pVar = new p(c2, (o.er.e) c2.H());
        ArrayList arrayList = new ArrayList();
        arrayList.add(this);
        n nVar = new n(pVar.c(), c2, pVar.b(), pVar, arrayList);
        int i = c + 79;
        b = i % 128;
        switch (i % 2 != 0 ? '+' : 'F') {
            case '+':
                throw null;
            default:
                return nVar;
        }
    }
}
