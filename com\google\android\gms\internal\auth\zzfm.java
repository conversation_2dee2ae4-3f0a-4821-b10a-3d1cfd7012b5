package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfm.smali */
final class zzfm implements zzfu {
    zzfm() {
    }

    public final zzft zzb(Class cls) {
        throw new IllegalStateException("This should never be called.");
    }

    public final boolean zzc(Class cls) {
        return false;
    }
}
