package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u0.smali */
public class u0 extends d {
    private final int L;
    private final d[] R;

    public u0(byte[] bArr, int i) {
        this(bArr, i, 1000);
    }

    static byte[] a(d[] dVarArr) {
        int length = dVarArr.length;
        if (length == 0) {
            return new byte[]{0};
        }
        if (length == 1) {
            return dVarArr[0].b;
        }
        int i = length - 1;
        int i2 = 0;
        for (int i3 = 0; i3 < i; i3++) {
            byte[] bArr = dVarArr[i3].b;
            if (bArr[0] != 0) {
                throw new IllegalArgumentException("only the last nested bitstring can have padding");
            }
            i2 += bArr.length - 1;
        }
        byte[] bArr2 = dVarArr[i].b;
        byte b = bArr2[0];
        byte[] bArr3 = new byte[i2 + bArr2.length];
        bArr3[0] = b;
        int i4 = 1;
        for (d dVar : dVarArr) {
            byte[] bArr4 = dVar.b;
            int length2 = bArr4.length - 1;
            System.arraycopy(bArr4, 1, bArr3, i4, length2);
            i4 += length2;
        }
        return bArr3;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return this.R != null || this.b.length > this.L;
    }

    public u0(byte[] bArr, int i, int i2) {
        super(bArr, i);
        this.R = null;
        this.L = i2;
    }

    public u0(d[] dVarArr) {
        this(dVarArr, 1000);
    }

    public u0(d[] dVarArr, int i) {
        super(a(dVarArr), false);
        this.R = dVarArr;
        this.L = i;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        if (!e()) {
            return y2.a(z, this.b.length);
        }
        int i = z ? 4 : 3;
        if (this.R == null) {
            byte[] bArr = this.b;
            if (bArr.length < 2) {
                return i;
            }
            int length = bArr.length - 2;
            int i2 = this.L;
            int i3 = length / (i2 - 1);
            return i + (y2.a(true, i2) * i3) + y2.a(true, this.b.length - (i3 * (this.L - 1)));
        }
        int i4 = 0;
        while (true) {
            d[] dVarArr = this.R;
            if (i4 >= dVarArr.length) {
                return i;
            }
            i += dVarArr[i4].a(true);
            i4++;
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        if (!e()) {
            byte[] bArr = this.b;
            y2.a(zVar, z, bArr, 0, bArr.length);
            return;
        }
        zVar.b(z, 35);
        zVar.c(128);
        d[] dVarArr = this.R;
        if (dVarArr != null) {
            zVar.a((b0[]) dVarArr);
        } else {
            byte[] bArr2 = this.b;
            if (bArr2.length >= 2) {
                byte b = bArr2[0];
                int length = bArr2.length;
                int i = length - 1;
                int i2 = this.L - 1;
                while (i > i2) {
                    y2.a(zVar, true, (byte) 0, this.b, length - i, i2);
                    i -= i2;
                }
                y2.a(zVar, true, b, this.b, length - i, i);
            }
        }
        zVar.c(0);
        zVar.c(0);
    }
}
