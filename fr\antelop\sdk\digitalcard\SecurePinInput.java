package fr.antelop.sdk.digitalcard;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecurePinInput.smali */
public final class SecurePinInput {
    private final CurrentPinInputProperties currentPinInputProperties;
    private final NewPinInputProperties newPinInputProperties;

    public SecurePinInput(CurrentPinInputProperties currentPinInputProperties, NewPinInputProperties newPinInputProperties) {
        this.currentPinInputProperties = currentPinInputProperties;
        this.newPinInputProperties = newPinInputProperties;
    }

    public final CurrentPinInputProperties getCurrentPinInputProperties() {
        return this.currentPinInputProperties;
    }

    public final NewPinInputProperties getNewPinInputProperties() {
        return this.newPinInputProperties;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecurePinInput$CurrentPinInputProperties.smali */
    public static class CurrentPinInputProperties {
        private final String subtitle;
        private final String title;

        public CurrentPinInputProperties(String str, String str2) {
            this.title = str;
            this.subtitle = str2;
        }

        public String getTitle() {
            return this.title;
        }

        public String getSubtitle() {
            return this.subtitle;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecurePinInput$NewPinInputProperties.smali */
    public static class NewPinInputProperties {
        private final String confirmationSubtitle;
        private final String confirmationTitle;
        private final boolean requestConfirmation;
        private final String subtitle;
        private final String title;

        public NewPinInputProperties(String str, String str2) {
            this.title = str;
            this.subtitle = str2;
            this.confirmationTitle = null;
            this.confirmationSubtitle = null;
            this.requestConfirmation = false;
        }

        public NewPinInputProperties(String str, String str2, String str3, String str4) {
            this.title = str;
            this.subtitle = str2;
            this.confirmationTitle = str3;
            this.confirmationSubtitle = str4;
            this.requestConfirmation = true;
        }

        public String getTitle() {
            return this.title;
        }

        public String getSubtitle() {
            return this.subtitle;
        }

        public String getConfirmationTitle() {
            return this.confirmationTitle;
        }

        public String getConfirmationSubtitle() {
            return this.confirmationSubtitle;
        }

        public boolean getRequestConfirmation() {
            return this.requestConfirmation;
        }
    }
}
