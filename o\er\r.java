package o.er;

import android.content.Context;
import androidx.work.WorkRequest;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.VirtualCardNumber;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import o.em.e;
import o.eo.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\r.smali */
public final class r extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        a();
        int i = e + Opcodes.DNEG;
        b = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 49 / 0;
                break;
        }
    }

    static void a() {
        a = new char[]{50839, 50814, 50808, 50803, 50702, 50807, 50790, 50812, 50807, 50815, 50811, 50701, 50813, 50789, 50807, 50805, 50796, 50795, 50808, 50807, 50702, 50703, 50801, 50787, 50814, 50807, 50805, 50800, 50791, 50942, 50859, 50849, 50856, 50834, 50848, 50878, 50873, 50854, 50859, 50842, 50847, 50852, 50854, 50836, 50860, 50876, 50858, 50862, 50854, 50879, 50851, 50721, 51150, 51144, 50943, 50857, 50859, 50850, 50873, 50854, 50833, 50863, 50854, 50862, 50858, 50876, 50860, 50836, 50854, 50852, 50847, 50842, 50859, 50854, 50873, 50878, 50848, 50834, 50909, 50820, 50879, 50854, 50862, 50858, 50876, 50860, 50836, 50854, 50852, 50847, 50842, 50859, 50854, 50873, 50878, 50848, 50834, 50856, 50849, 50859, 50858, 50848, 50849, 50854, 50849, 50856, 50854, 50820, 50826, 50836, 50943, 50914, 50936, 50923, 50829, 50855, 50855, 50863, 50831, 50826, 50835, 50938, 50923, 50816, 50775, 50797, 50772, 50782, 50796, 50794, 50789, 50770, 50775, 50758, 50763, 50768, 50770, 50752, 50776, 50792, 50774, 50778, 50770, 50795, 50864, 50839, 50839, 50859, 50763, 50771, 50797, 50875, 50869, 50799, 50864, 50870, 50792, 50793, 50797, 50871, 50839, 50852, 50862, 50859, 50752, 50870, 50864, 50770, 50772, 50797, 50770, 50797, 50796, 50942, 50859, 50849, 50856, 50834, 50848, 50878, 50873, 50854, 50859, 50842, 50847, 50852, 50854, 50836, 50860, 50876, 50858, 50862, 50854, 50879, 50820, 50923, 50923, 50939, 50843, 50855, 50878, 50823, 50820, 50854, 50849, 50878, 50848, 50858, 50848, 50848, 50831, 50821, 50853, 50855, 50878, 50823, 50917, 50941, 50941, 50941, 50941, 50819, 50877, 50938, 50853, 50849, 50822, 50823, 50876, 50852, 50854, 50879, 50878, 50849, 50828, 50826, 50877, 50878, 50873, 50849, 50854, 50820, 50923, 50816, 50852, 50831, 50826, 50876, 50823, 50825, 50848, 50849, 50854, 50849, 50856, 50854, 50820, 50826, 50835, 50938, 50923, 50923, 50820, 50879, 50854, 50862, 50858, 50876, 50860, 50836, 50854, 50852, 50847, 50842, 50859, 50854, 50873, 50878, 50848, 50834, 50856, 50849, 50859, 50873, 50720, 50721, 50726, 50721, 50728, 50726, 50692, 50698, 50708, 50815, 50795, 50795, 50692, 50751, 50726, 50734, 50730, 50748, 50732, 50708, 50726, 50724, 50719, 50714, 50731, 50726, 50745, 50750, 50720, 50706, 50728, 50721, 50731};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r6 = r6 + 66
            int r8 = r8 * 2
            int r8 = r8 + 4
            byte[] r0 = o.er.r.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = r8 + 1
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.r.g(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{38, -75, -91, -62};
        $$b = 50;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = e + Opcodes.LSUB;
        b = i % 128;
        switch (i % 2 == 0 ? '_' : 'N') {
            case Opcodes.SWAP /* 95 */:
                super.b();
                throw null;
            default:
                boolean b2 = super.b();
                int i2 = b + 25;
                e = i2 % 128;
                switch (i2 % 2 != 0 ? '@' : (char) 1) {
                    case '@':
                        throw null;
                    default:
                        return b2;
                }
        }
    }

    public r(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        int i = e + 89;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                return new a[]{this.d.g(), this.d.f()};
            default:
                a[] aVarArr = new a[3];
                aVarArr[1] = this.d.g();
                aVarArr[0] = this.d.f();
                return aVarArr;
        }
    }

    public final void c(Context context, final OperationCallback<List<VirtualCardNumber>> operationCallback) throws WalletValidationException {
        boolean z;
        Object obj;
        Object obj2;
        int i = e + 47;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{0, 29, 79, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001", new int[]{29, 21, 0, 0}, false, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (this.c.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            f("\u0001\u0000\u0001\u0000", new int[]{50, 4, Opcodes.IFNE, 0}, false, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (!b()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{54, 24, 0, 0}, true, objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        o.em.e j = o.ei.c.c().j();
        List<o.eo.j> a2 = j.b().a(this.c.e());
        if (!a2.isEmpty()) {
            Long g = j.b().g(this.c.e());
            switch (g != null) {
                case false:
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001", new int[]{Opcodes.LSHR, 50, 52, 0}, false, objArr5);
                    o.ee.g.d(intern, ((String) objArr5[0]).intern());
                    z = true;
                    break;
                default:
                    if (g.longValue() >= System.currentTimeMillis() - WorkRequest.MIN_BACKOFF_MILLIS) {
                        z = false;
                        break;
                    } else {
                        int i3 = e + 33;
                        b = i3 % 128;
                        if (i3 % 2 != 0) {
                            o.ee.g.c();
                            Object[] objArr6 = new Object[1];
                            f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000", new int[]{Opcodes.LRETURN, 50, 0, 0}, false, objArr6);
                            o.ee.g.d(intern, ((String) objArr6[0]).intern());
                            z = true;
                            break;
                        } else {
                            o.ee.g.c();
                            Object[] objArr7 = new Object[1];
                            f("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000", new int[]{Opcodes.LRETURN, 50, 0, 0}, false, objArr7);
                            o.ee.g.d(intern, ((String) objArr7[0]).intern());
                            z = false;
                            break;
                        }
                    }
            }
        } else {
            int i4 = e + Opcodes.DMUL;
            b = i4 % 128;
            if (i4 % 2 == 0) {
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{78, 45, 0, 22}, true, objArr8);
                obj2 = objArr8[0];
            } else {
                o.ee.g.c();
                Object[] objArr9 = new Object[1];
                f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{78, 45, 0, 22}, true, objArr9);
                obj2 = objArr9[0];
            }
            o.ee.g.d(intern, ((String) obj2).intern());
            int i5 = e + 109;
            b = i5 % 128;
            int i6 = i5 % 2;
            z = true;
        }
        if (z) {
            o.ee.g.c();
            Object[] objArr10 = new Object[1];
            f("\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{283, 34, 128, 0}, true, objArr10);
            o.ee.g.d(intern, ((String) objArr10[0]).intern());
            j.a(context, this.c.e(), new e.c<o.eo.j>() { // from class: o.er.r.1
                private static int d = 0;
                private static int e = 1;

                /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                    jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                    	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                    	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                    	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                    	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                    	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                    */
                @Override // o.em.e.c
                public final void a(java.util.List<o.eo.j> r7) {
                    /*
                        Method dump skipped, instructions count: 248
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.er.r.AnonymousClass1.a(java.util.List):void");
                }

                @Override // o.em.e.c
                public final void d(AntelopError antelopError) {
                    int i7 = d;
                    int i8 = ((i7 | 95) << 1) - (i7 ^ 95);
                    e = i8 % 128;
                    switch (i8 % 2 != 0) {
                        case true:
                            operationCallback.onError(antelopError);
                            return;
                        default:
                            operationCallback.onError(antelopError);
                            int i9 = 44 / 0;
                            return;
                    }
                }
            });
            return;
        }
        int i7 = e + 73;
        b = i7 % 128;
        if (i7 % 2 == 0) {
            o.ee.g.c();
            Object[] objArr11 = new Object[1];
            f("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{223, 60, 0, 0}, true, objArr11);
            obj = objArr11[0];
        } else {
            o.ee.g.c();
            Object[] objArr12 = new Object[1];
            f("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{223, 60, 0, 0}, true, objArr12);
            obj = objArr12[0];
        }
        o.ee.g.d(intern, ((String) obj).intern());
        Iterator<o.eo.j> it = a2.iterator();
        while (it.hasNext()) {
            if (it.next().a() == j.b.d) {
                int i8 = e + 63;
                b = i8 % 128;
                switch (i8 % 2 == 0 ? (char) 11 : 'S') {
                    case 11:
                        it.remove();
                        throw null;
                    default:
                        it.remove();
                        break;
                }
            }
        }
        ArrayList arrayList = new ArrayList(a2.size());
        Iterator<o.eo.j> it2 = a2.iterator();
        while (it2.hasNext()) {
            arrayList.add(new VirtualCardNumber(this.c, (e) this.c.H(), it2.next()));
        }
        operationCallback.onSuccess(arrayList);
    }

    public final o.v.l e() {
        o.v.l lVar = new o.v.l(this.d.f().d(), this.c, this.d.f().b());
        int i = e + 69;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                return lVar;
            default:
                int i2 = 53 / 0;
                return lVar;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x00f7, code lost:
    
        if (r0[r1.d] == 0) goto L58;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 832
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.r.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
