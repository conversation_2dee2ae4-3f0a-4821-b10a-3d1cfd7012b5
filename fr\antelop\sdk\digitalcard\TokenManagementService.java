package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import o.er.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\TokenManagementService.smali */
public final class TokenManagementService {
    private final q innerTokenManagementService;

    public TokenManagementService(q qVar) {
        this.innerTokenManagementService = qVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerTokenManagementService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final void getTokens(Context context, OperationCallback<List<Token>> operationCallback) throws WalletValidationException {
        this.innerTokenManagementService.a(context, operationCallback);
    }
}
