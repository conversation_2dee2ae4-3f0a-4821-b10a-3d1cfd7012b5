package androidx.webkit;

import android.webkit.CookieManager;
import androidx.webkit.internal.ApiFeature;
import androidx.webkit.internal.CookieManagerAdapter;
import androidx.webkit.internal.WebViewFeatureInternal;
import androidx.webkit.internal.WebViewGlueCommunicator;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\webkit\CookieManagerCompat.smali */
public class CookieManagerCompat {
    private CookieManagerCompat() {
    }

    public static List<String> getCookieInfo(CookieManager cookieManager, String url) {
        ApiFeature.NoFramework feature = WebViewFeatureInternal.GET_COOKIE_INFO;
        if (feature.isSupportedByWebView()) {
            return getAdapter(cookieManager).getCookieInfo(url);
        }
        throw WebViewFeatureInternal.getUnsupportedOperationException();
    }

    private static CookieManagerAdapter getAdapter(CookieManager cookieManager) {
        return WebViewGlueCommunicator.getCompatConverter().convertCookieManager(cookieManager);
    }
}
