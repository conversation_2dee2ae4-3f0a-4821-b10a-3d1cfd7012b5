package o.de;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.work.Constraints;
import androidx.work.Data;
import androidx.work.ExistingWorkPolicy;
import androidx.work.ListenableWorker;
import androidx.work.NetworkType;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;
import androidx.work.Worker;
import androidx.work.WorkerParameters;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.exposed.AntelopLifecycleJobService;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import o.a.l;
import o.de.a;
import o.de.c;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\c.smali */
public final class c {
    private static int a;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 1;

    static {
        a = 0;
        d();
        Drawable.resolveOpacity(0, 0);
        Process.myTid();
        ViewConfiguration.getGlobalActionKeyTimeout();
        ViewConfiguration.getScrollDefaultDelay();
        View.MeasureSpec.getMode(0);
        int i = h + 83;
        a = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        e = (char) 54532;
        b = (char) 44998;
        d = (char) 28082;
        c = (char) 18757;
    }

    public static boolean c(Context context, b bVar, String str) {
        Data.Builder builder = new Data.Builder();
        Object[] objArr = new Object[1];
        f("튖햫罌\uf650\uddb0ऴ\ue32a둨㢐䰾뽵῾熿묢\uf11d溅\uf1f5柏磩\udfd8\ud818ⴂ", 23 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        Data.Builder putString = builder.putString(((String) objArr[0]).intern(), bVar.b().toString());
        Object[] objArr2 = new Object[1];
        f("튖햫罌\uf650\uddb0ऴ\ue32a둨㢐䰾뽵῾熿묢\uf11d溅\uf1f5柏㷓뫞簂щ\uf1f5柏⒖㐇", 26 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr2);
        Data.Builder putString2 = putString.putString(((String) objArr2[0]).intern(), bVar.c().toString());
        Object[] objArr3 = new Object[1];
        f("튖햫罌\uf650\uddb0ऴ\ue32a둨㢐䰾뽵῾熿묢\uf11d溅\uf1f5柏ꪟ卪", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 20, objArr3);
        OneTimeWorkRequest.Builder backoffCriteria = new OneTimeWorkRequest.Builder(AntelopLifecycleJobService.class).setConstraints(new Constraints.Builder().setRequiredNetworkType(NetworkType.CONNECTED).build()).setInputData(putString2.putString(((String) objArr3[0]).intern(), str).build()).setBackoffCriteria(bVar.d().b(), bVar.d().d(), TimeUnit.SECONDS);
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(13, 5);
        Date time = calendar.getTime();
        Date a2 = bVar.a();
        switch (a2.before(time)) {
            case false:
                int time2 = (int) (a2.getTime() - date.getTime());
                backoffCriteria.setInitialDelay(time2, TimeUnit.MILLISECONDS);
                g.c();
                Object[] objArr4 = new Object[1];
                f("࠸\ue4ccᬻ体݆Ｍ\ue2f2兜㖚Ｗ罌\uf650낤잩빛\ue168圴\u0bba鉻媬铷ꝿ퉋阠⒖㐇", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 24, objArr4);
                String intern = ((String) objArr4[0]).intern();
                Locale a3 = o.ee.j.a();
                Object[] objArr5 = new Object[1];
                f("\uec69ỡ㌂\ud875솕架䝮∏\ue21a턇衄浚䝮∏絷剘蘳\udf7d䱜\uf24c尪ᕍ觹ꩻ빛\ue168穃韂⍈렕\ud94aꙐ譶\uef67씎젛絷剘젶탧胭뭁\ud94aꙐ罌\uf650\ue97b攙ସ宻", 50 - ((Process.getThreadPriority(0) + 20) >> 6), objArr5);
                g.d(intern, String.format(a3, ((String) objArr5[0]).intern(), Integer.valueOf(time2 / 1000)));
                break;
            default:
                int i = h + 89;
                a = i % 128;
                if (i % 2 != 0) {
                }
                backoffCriteria.setInitialDelay(0L, TimeUnit.SECONDS);
                g.c();
                Object[] objArr6 = new Object[1];
                f("࠸\ue4ccᬻ体݆Ｍ\ue2f2兜㖚Ｗ罌\uf650낤잩빛\ue168圴\u0bba鉻媬铷ꝿ퉋阠⒖㐇", 24 - TextUtils.lastIndexOf("", '0'), objArr6);
                String intern2 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                f("\uec69ỡ㌂\ud875솕架䝮∏\ue21a턇\ud94aꙐ譶\uef67씎젛圴\u0bba۰표儹㤈\ud94aꙐ튖햫˦Ѡ釴繉˦Ѡ\uf1a9Š圉椑\uec69ỡ㌂\ud875艠쬰\uddd2烑妮\uec91", 46 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr7);
                g.d(intern2, ((String) objArr7[0]).intern());
                break;
        }
        OneTimeWorkRequest build = backoffCriteria.build();
        g.c();
        Object[] objArr8 = new Object[1];
        f("࠸\ue4ccᬻ体݆Ｍ\ue2f2兜㖚Ｗ罌\uf650낤잩빛\ue168圴\u0bba鉻媬铷ꝿ퉋阠⒖㐇", (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 24, objArr8);
        String intern3 = ((String) objArr8[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr9 = new Object[1];
        f("\uec69ỡ㌂\ud875솕架䝮∏\ue21a턇衄浚䝮∏眲쓼瞣䤘穃韂儹㤈Ꮵ귐澬䝷", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 25, objArr9);
        g.d(intern3, sb.append(((String) objArr9[0]).intern()).append(build.getId()).toString());
        WorkManager workManager = WorkManager.getInstance(context);
        Object[] objArr10 = new Object[1];
        f("큠낀ᝍꖛ\uf818冤栍\udc31⟁찡\uda34六㖚Ｗ罌\uf650낤잩빛\ue168圴\u0bba㷈ࢶ", 23 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr10);
        workManager.enqueueUniqueWork(((String) objArr10[0]).intern(), ExistingWorkPolicy.REPLACE, build);
        int i2 = h + 69;
        a = i2 % 128;
        int i3 = i2 % 2;
        return true;
    }

    public static void d(Context context) {
        int i = h + 91;
        a = i % 128;
        int i2 = i % 2;
        WorkManager workManager = WorkManager.getInstance(context);
        Object[] objArr = new Object[1];
        f("큠낀ᝍꖛ\uf818冤栍\udc31⟁찡\uda34六㖚Ｗ罌\uf650낤잩빛\ue168圴\u0bba㷈ࢶ", 23 - KeyEvent.keyCodeFromString(""), objArr);
        workManager.cancelUniqueWork(((String) objArr[0]).intern());
        int i3 = a + Opcodes.DNEG;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\c$e.smali */
    public static class e extends Worker {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static final String TAG;
        private static char[] b;
        private static int c;
        private static int d;
        private a lifecycleJob;
        private ListenableWorker.Result lockResult;
        final Object syncObject;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            c = 1;
            a();
            Object[] objArr = new Object[1];
            f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr);
            TAG = ((String) objArr[0]).intern();
            int i = d + 63;
            c = i % 128;
            switch (i % 2 != 0) {
                case true:
                    break;
                default:
                    int i2 = 45 / 0;
                    break;
            }
        }

        static void a() {
            b = new char[]{50940, 50856, 50859, 50857, 50858, 50861, 50863, 50855, 50863, 50855, 50849, 50836, 50836, 50849, 50878, 50855, 50857, 50859, 50833, 50859, 50873, 50855, 50943, 50857, 50859, 50850, 50873, 50854, 50833, 50839, 50853, 50833, 50842, 50853, 50858, 50851, 50851, 50857, 50856, 50858, 50839, 50929, 50818, 50854, 50852, 50851, 50876, 50862, 50839, 50853, 50833, 50842, 50853, 50858, 50851, 50851, 50857, 50856, 50858, 50839, 50837, 50857, 50850, 50848, 50859, 50853, 50841, 50932, 50878, 50855, 50852, 50878, 50856, 50862, 50877, 50851, 50824, 50923, 50923, 50940, 50840, 50859, 50857, 50858, 50824, 50823, 50879, 50855, 50863, 50855, 50849, 50831, 50827, 50852, 50849, 50878, 50855, 50857, 50859, 50831, 50823, 50875, 50873, 50855, 50831, 50826, 50851, 50876, 50823, 50830, 50855, 50879, 50876, 50852, 50940, 50856, 50859, 50857, 50858, 50861, 50863, 50855, 50863, 50855, 50849, 50836, 50836, 50849, 50878, 50855, 50857, 50859, 50833, 50862, 50848, 50853, 50858, 50859, 50854, 50851, 50735, 50727, 50726, 50734, 50801, 50702, 50727, 50730, 50701, 50703, 50729, 50706, 50733, 50732, 50731, 50721, 50702, 50807, 50732, 50728, 50803, 50806, 50732, 50733, 50730, 50730, 50704, 50707, 50733, 50718, 50786, 50770, 50770, 50803, 50730, 50724, 50705, 50707, 50721, 50735, 50734, 50721, 51199, 51154, 51183, 51171, 51166, 51172, 51180, 51182, 51179, 51180, 51183, 51146, 51182, 51176, 51154, 51196, 51196, 51182, 51142, 51182, 50876, 50725, 50749, 50748, 50724, 50703, 50692, 50749, 50720, 50699, 50812, 50696, 50814, 50701, 50730, 50710, 50809, 50792, 50792, 50697, 50720, 50722, 50735, 50729, 50751, 50725, 50724, 50751, 50940, 50853, 50849, 50824, 50822, 50851, 50852, 50826, 50830, 50855, 50854, 50851, 50878, 50852, 50855, 50878, 50820, 50923, 50923, 50824, 50851, 50877, 50862, 50856, 50878, 50852, 50855, 50878, 50938, 50854, 50825, 50923, 50923, 50831, 50857, 50855, 50877, 50850, 50876, 50862, 50861, 50851, 50853, 50853, 50849, 50824, 50830, 50855, 50854, 50851, 50879, 50879, 50878, 50879, 50854, 50849};
        }

        private static void g(byte b2, int i, short s, Object[] objArr) {
            byte[] bArr = $$a;
            int i2 = s + 66;
            int i3 = 4 - (i * 3);
            int i4 = (b2 * 2) + 1;
            byte[] bArr2 = new byte[i4];
            int i5 = -1;
            int i6 = i4 - 1;
            if (bArr == null) {
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = -1;
                i2 = i6 + i2;
                i6 = i6;
                i3++;
            }
            while (true) {
                int i7 = i5 + 1;
                bArr2[i7] = (byte) i2;
                if (i7 == i6) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b3 = bArr[i3];
                int i8 = i3;
                int i9 = i6;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = i7;
                i2 = b3 + i2;
                i6 = i9;
                i3 = i8 + 1;
            }
        }

        static void init$0() {
            $$a = new byte[]{106, 58, 15, 91};
            $$b = Opcodes.MONITORENTER;
        }

        public e(Context context, WorkerParameters workerParameters) {
            super(context, workerParameters);
            this.syncObject = new Object();
        }

        @Override // androidx.work.Worker
        public ListenableWorker.Result doWork() {
            ListenableWorker.Result result;
            Data inputData = getInputData();
            Object[] objArr = new Object[1];
            f("\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001", new int[]{0, 22, 0, 0}, false, objArr);
            final o.av.a b2 = o.av.a.b(inputData.getString(((String) objArr[0]).intern()));
            if (b2 == null) {
                g.c();
                Object[] objArr2 = new Object[1];
                f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr2);
                String intern = ((String) objArr2[0]).intern();
                Object[] objArr3 = new Object[1];
                f("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000", new int[]{67, 47, 0, 0}, false, objArr3);
                g.e(intern, ((String) objArr3[0]).intern());
                return ListenableWorker.Result.failure();
            }
            Data inputData2 = getInputData();
            Object[] objArr4 = new Object[1];
            f("\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{114, 25, 0, 0}, false, objArr4);
            f a = f.a(inputData2.getString(((String) objArr4[0]).intern()));
            if (a == null) {
                g.c();
                Object[] objArr5 = new Object[1];
                f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr5);
                String intern2 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f("\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{Opcodes.F2I, 43, Opcodes.LSHL, 0}, true, objArr6);
                g.e(intern2, ((String) objArr6[0]).intern());
                return ListenableWorker.Result.failure();
            }
            Data inputData3 = getInputData();
            Object[] objArr7 = new Object[1];
            f(null, new int[]{Opcodes.INVOKEVIRTUAL, 20, Opcodes.ARRAYLENGTH, 10}, true, objArr7);
            String string = inputData3.getString(((String) objArr7[0]).intern());
            if (string == null) {
                g.c();
                Object[] objArr8 = new Object[1];
                f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr8);
                String intern3 = ((String) objArr8[0]).intern();
                Object[] objArr9 = new Object[1];
                f("\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{202, 28, 127, 0}, true, objArr9);
                g.e(intern3, ((String) objArr9[0]).intern());
                return ListenableWorker.Result.failure();
            }
            o.db.b.a().d(getApplicationContext(), b2, a);
            g.c();
            Object[] objArr10 = new Object[1];
            f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr10);
            String intern4 = ((String) objArr10[0]).intern();
            Object[] objArr11 = new Object[1];
            f("\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{230, 28, 0, 0}, true, objArr11);
            g.d(intern4, ((String) objArr11[0]).intern());
            new a(getApplicationContext(), new a.e() { // from class: o.de.c$e$$ExternalSyntheticLambda0
                @Override // o.de.a.e
                public final void onJobEnd(o.bb.d dVar, a aVar, o.bv.g gVar) {
                    c.e.this.m1815lambda$doWork$0$odec$e(b2, dVar, aVar, gVar);
                }
            }, b2, string, a).c();
            synchronized (this.syncObject) {
                try {
                    try {
                        this.syncObject.wait();
                        result = this.lockResult;
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        return ListenableWorker.Result.failure();
                    }
                } catch (Throwable th) {
                    throw th;
                }
            }
            return result;
        }

        /* renamed from: lambda$doWork$0$o-de-c$e, reason: not valid java name */
        /* synthetic */ void m1815lambda$doWork$0$odec$e(o.av.a aVar, o.bb.d dVar, a aVar2, o.bv.g gVar) {
            synchronized (this.syncObject) {
                if (o.db.b.a().b(getApplicationContext(), dVar, aVar, true, gVar)) {
                    this.lockResult = ListenableWorker.Result.retry();
                } else {
                    this.lockResult = ListenableWorker.Result.success();
                }
                this.syncObject.notifyAll();
            }
        }

        @Override // androidx.work.ListenableWorker
        public synchronized void onStopped() {
            switch (this.lifecycleJob != null ? 'N' : 'I') {
                case 'N':
                    int i = c + 57;
                    d = i % 128;
                    int i2 = i % 2;
                    g.c();
                    Object[] objArr = new Object[1];
                    f("\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001", new int[]{22, 45, 0, 0}, true, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    f("\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{258, 28, 0, 14}, true, objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    this.lifecycleJob.d();
                    break;
            }
            int i3 = d + 35;
            c = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    break;
                default:
                    throw null;
            }
        }

        public int hashCode() {
            int i = c + 59;
            d = i % 128;
            int i2 = i % 2;
            int hashCode = super.hashCode();
            int i3 = c + 29;
            d = i3 % 128;
            int i4 = i3 % 2;
            return hashCode;
        }

        public final boolean equals(Object obj) {
            int i = c + 55;
            d = i % 128;
            int i2 = i % 2;
            boolean equals = super.equals(obj);
            int i3 = d + 7;
            c = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return equals;
            }
        }

        protected final Object clone() throws CloneNotSupportedException {
            throw new CloneNotSupportedException();
        }

        public final String toString() {
            int i = c + 43;
            d = i % 128;
            int i2 = i % 2;
            String obj = super.toString();
            int i3 = d + 87;
            c = i3 % 128;
            switch (i3 % 2 == 0 ? '\r' : '(') {
                case '(':
                    return obj;
                default:
                    int i4 = 75 / 0;
                    return obj;
            }
        }

        protected final void finalize() throws Throwable {
            int i = c + 43;
            d = i % 128;
            int i2 = i % 2;
            super.finalize();
            int i3 = c + 1;
            d = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
            int i;
            char[] cArr;
            char[] cArr2;
            int i2;
            String str2 = str;
            byte[] bArr = str2;
            if (str2 != null) {
                bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            }
            byte[] bArr2 = bArr;
            l lVar = new l();
            int i3 = 0;
            int i4 = iArr[0];
            int i5 = 1;
            int i6 = iArr[1];
            int i7 = iArr[2];
            int i8 = iArr[3];
            char[] cArr3 = b;
            float f = 0.0f;
            switch (cArr3 != null ? (char) 15 : '`') {
                case Opcodes.IADD /* 96 */:
                    break;
                default:
                    int length = cArr3.length;
                    char[] cArr4 = new char[length];
                    int i9 = 0;
                    while (i9 < length) {
                        try {
                            Object[] objArr2 = new Object[i5];
                            objArr2[i3] = Integer.valueOf(cArr3[i9]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr2 = cArr3;
                                i2 = length;
                            } else {
                                Class cls = (Class) o.e.a.c(ImageFormat.getBitsPerPixel(i3) + 12, (char) TextUtils.indexOf("", "", i3, i3), 43 - (PointF.length(f, f) > f ? 1 : (PointF.length(f, f) == f ? 0 : -1)));
                                byte b2 = (byte) i3;
                                byte b3 = b2;
                                cArr2 = cArr3;
                                i2 = length;
                                Object[] objArr3 = new Object[1];
                                g(b2, b3, (byte) (b3 | 54), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i9++;
                            cArr3 = cArr2;
                            length = i2;
                            i3 = 0;
                            i5 = 1;
                            f = 0.0f;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr3 = cArr4;
                    break;
            }
            char[] cArr5 = new char[i6];
            System.arraycopy(cArr3, i4, cArr5, 0, i6);
            switch (bArr2 == null) {
                case false:
                    int i10 = $11 + 9;
                    $10 = i10 % 128;
                    int i11 = i10 % 2;
                    char[] cArr6 = new char[i6];
                    lVar.d = 0;
                    char c2 = 0;
                    while (true) {
                        switch (lVar.d < i6 ? (char) 0 : 'S') {
                            case 0:
                                if (bArr2[lVar.d] == 1) {
                                    int i12 = $10 + 9;
                                    $11 = i12 % 128;
                                    int i13 = i12 % 2;
                                    int i14 = lVar.d;
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                        Object obj2 = o.e.a.s.get(2016040108);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 12, (char) Drawable.resolveOpacity(0, 0), 447 - MotionEvent.axisFromString(""));
                                            byte b4 = (byte) 0;
                                            byte b5 = b4;
                                            Object[] objArr5 = new Object[1];
                                            g(b4, b5, (byte) (b5 | 53), objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(2016040108, obj2);
                                        }
                                        cArr6[i14] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                } else {
                                    int i15 = lVar.d;
                                    try {
                                        Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c2)};
                                        Object obj3 = o.e.a.s.get(804049217);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c(10 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 207 - TextUtils.getCapsMode("", 0, 0));
                                            byte b6 = (byte) 0;
                                            byte b7 = b6;
                                            Object[] objArr7 = new Object[1];
                                            g(b6, b7, (byte) (b7 | 56), objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(804049217, obj3);
                                        }
                                        cArr6[i15] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                                c2 = cArr6[lVar.d];
                                try {
                                    Object[] objArr8 = {lVar, lVar};
                                    Object obj4 = o.e.a.s.get(-2112603350);
                                    if (obj4 == null) {
                                        Class cls4 = (Class) o.e.a.c(12 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), 259 - TextUtils.indexOf("", ""));
                                        byte b8 = (byte) 0;
                                        byte b9 = b8;
                                        Object[] objArr9 = new Object[1];
                                        g(b8, b9, b9, objArr9);
                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                        o.e.a.s.put(-2112603350, obj4);
                                    }
                                    ((Method) obj4).invoke(null, objArr8);
                                } catch (Throwable th4) {
                                    Throwable cause4 = th4.getCause();
                                    if (cause4 == null) {
                                        throw th4;
                                    }
                                    throw cause4;
                                }
                            default:
                                cArr5 = cArr6;
                                break;
                        }
                    }
            }
            if (i8 > 0) {
                char[] cArr7 = new char[i6];
                System.arraycopy(cArr5, 0, cArr7, 0, i6);
                int i16 = i6 - i8;
                System.arraycopy(cArr7, 0, cArr5, i16, i8);
                System.arraycopy(cArr7, i8, cArr5, 0, i16);
            }
            if (z) {
                int i17 = $10 + 73;
                $11 = i17 % 128;
                if (i17 % 2 == 0) {
                    cArr = new char[i6];
                    i = 0;
                } else {
                    i = 0;
                    cArr = new char[i6];
                }
                while (true) {
                    lVar.d = i;
                    if (lVar.d < i6) {
                        cArr[lVar.d] = cArr5[(i6 - lVar.d) - 1];
                        i = lVar.d + 1;
                    } else {
                        cArr5 = cArr;
                    }
                }
            }
            if (i7 > 0) {
                int i18 = 0;
                while (true) {
                    lVar.d = i18;
                    switch (lVar.d < i6) {
                        case false:
                            break;
                        default:
                            cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                            i18 = lVar.d + 1;
                    }
                }
            }
            objArr[0] = new String(cArr5);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.c.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
