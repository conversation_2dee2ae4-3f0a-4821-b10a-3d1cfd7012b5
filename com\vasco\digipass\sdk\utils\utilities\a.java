package com.vasco.digipass.sdk.utils.utilities;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.BufferedBlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.Digest;
import bc.org.bouncycastle.crypto.digests.MD5Digest;
import bc.org.bouncycastle.crypto.digests.SHA1Digest;
import bc.org.bouncycastle.crypto.digests.SHA256Digest;
import bc.org.bouncycastle.crypto.digests.f;
import bc.org.bouncycastle.crypto.engines.AESEngine;
import bc.org.bouncycastle.crypto.engines.DESEngine;
import bc.org.bouncycastle.crypto.engines.DESedeEngine;
import bc.org.bouncycastle.crypto.modes.CBCBlockCipher;
import bc.org.bouncycastle.crypto.params.KeyParameter;
import bc.org.bouncycastle.crypto.params.ParametersWithIV;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u7;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\a.smali */
final class a {
    static UtilitiesSDKCryptoResponse a(byte b, byte[] bArr) {
        if (bArr == null) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.INPUT_DATA_NULL);
        }
        if (b != 1 && b != 2 && b != 3 && b != 4) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID);
        }
        Digest mD5Digest = b != 2 ? b != 3 ? b != 4 ? new MD5Digest() : new f() : new SHA256Digest() : new SHA1Digest();
        byte[] bArr2 = new byte[mD5Digest.getDigestSize()];
        mD5Digest.update(bArr, 0, bArr.length);
        mD5Digest.doFinal(bArr2, 0);
        return new UtilitiesSDKCryptoResponse(0, bArr2);
    }

    static byte[] a(boolean z, byte b, byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) throws UtilitiesSDKException {
        BlockCipher dESEngine;
        CipherParameters cipherParameters;
        if (b == 1) {
            dESEngine = new DESEngine();
        } else if (b != 2) {
            dESEngine = new AESEngine();
        } else {
            dESEngine = new DESedeEngine();
        }
        if (b2 == 2) {
            dESEngine = new CBCBlockCipher(dESEngine);
        } else if (b2 == 3) {
            dESEngine = new j1(dESEngine, 8);
        } else if (b2 == 4) {
            dESEngine = new c7(dESEngine);
        }
        KeyParameter keyParameter = null;
        try {
            KeyParameter keyParameter2 = new KeyParameter(bArr);
            if (bArr2 == null || b2 == 1) {
                cipherParameters = keyParameter2;
            } else {
                try {
                    cipherParameters = new ParametersWithIV(keyParameter2, bArr2);
                } catch (Throwable th) {
                    th = th;
                    keyParameter = keyParameter2;
                    u7.d(keyParameter.getKey());
                    throw th;
                }
            }
            byte[] bArr4 = new byte[bArr3.length];
            BufferedBlockCipher bufferedBlockCipher = new BufferedBlockCipher(dESEngine);
            bufferedBlockCipher.init(z, cipherParameters);
            try {
                bufferedBlockCipher.doFinal(bArr4, bufferedBlockCipher.processBytes(bArr3, 0, bArr3.length, bArr4, 0));
                u7.d(keyParameter2.getKey());
                return bArr4;
            } catch (Exception e) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR, e);
            }
        } catch (Throwable th2) {
            th = th2;
        }
    }
}
