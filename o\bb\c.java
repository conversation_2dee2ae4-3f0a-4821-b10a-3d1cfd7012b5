package o.bb;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.a.o;
import o.de.f;
import o.de.j;
import o.ee.g;
import o.ei.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bb\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static long g;
    private static char j;
    private static int k;
    private static int n;
    private f a;
    private f b;
    private boolean c;
    private Long d;
    private List<o.i.f> e;
    private boolean h;
    private boolean i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        n = 0;
        k = 1;
        m();
        ImageFormat.getBitsPerPixel(0);
        ExpandableListView.getPackedPositionGroup(0L);
        int i = k + 29;
        n = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
        $$b = 43;
    }

    static void m() {
        j = (char) 38043;
        f = 161105445;
        g = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(short r5, int r6, int r7, java.lang.Object[] r8) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 5
            int r5 = r5 * 3
            int r5 = 1 - r5
            byte[] r0 = o.bb.c.$$a
            int r7 = r7 + 99
            byte[] r1 = new byte[r5]
            int r5 = r5 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r4 = r6
            r3 = r2
            goto L28
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r5) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            r4 = r0[r6]
            int r3 = r3 + 1
        L28:
            int r6 = r6 + 1
            int r7 = r7 + r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.c.o(short, int, int, java.lang.Object[]):void");
    }

    public final boolean c() {
        int i = n + 91;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        boolean z = this.c;
        int i4 = i2 + 11;
        n = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final void b(boolean z) {
        int i = n;
        int i2 = i + 95;
        k = i2 % 128;
        int i3 = i2 % 2;
        this.c = z;
        int i4 = i + 1;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? 'J' : (char) 1) {
            case 'J':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final f a() {
        int i = n + 91;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        f fVar = this.b;
        int i4 = i2 + 53;
        n = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                throw null;
            default:
                return fVar;
        }
    }

    public final void d() {
        int i = k + 93;
        n = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        l(View.getDefaultSize(0, 0) + 1332820136, "䗵返耉ퟯ랸镰拢\udf0a\uf6db狆✵ച姌톍\udbeb䣿ᦹ储掿\ue3f0䂥䝠鈂휵\ufadc", (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "ꣂ焸챏茻", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(TextUtils.indexOf((CharSequence) "", '0', 0) + 2071453807, "튵\uf0b2붬皨㨨␊ᘁﵶ䘙뜝ቖ즂넚\udc84믮䟃ᮌྕ\ue15dᘋ펷", (char) View.resolveSizeAndState(0, 0, 0), "溋矠⁻ꃞ", "\u0000\u0000\u0000\u0000", objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        this.b = null;
        int i3 = k + 37;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void e(Context context, o.ei.c cVar, f fVar) {
        new j();
        switch (this.b != null ? Typography.amp : Typography.greater) {
            case '&':
                int i = n + Opcodes.DSUB;
                k = i % 128;
                int i2 = i % 2;
                try {
                    if (j.a(context, cVar, fVar) < j.a(context, cVar, this.b)) {
                        int i3 = k + 7;
                        n = i3 % 128;
                        if (i3 % 2 == 0) {
                            break;
                        } else {
                            break;
                        }
                    } else {
                        return;
                    }
                } catch (i e) {
                    g.c();
                    Object[] objArr = new Object[1];
                    l(TextUtils.getOffsetAfter("", 0) + 1332820136, "䗵返耉ퟯ랸镰拢\udf0a\uf6db狆✵ച姌톍\udbeb䣿ᦹ储掿\ue3f0䂥䝠鈂휵\ufadc", (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "ꣂ焸챏茻", "\u0000\u0000\u0000\u0000", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    l(View.MeasureSpec.getSize(0), "\ue297垐樗♅桅எꆁ驲╁䃔㜶ฏ듹咡䨋ﴉ벸瘐偾⨙滢☽ᄨ菥粏⣞ដ\uf89f\u1c39缂ᾅ㿙ꭺ掄귱贄ﺇ\uf1fb鰑\uea7d럻薩쾯䪸\ue415톢\ue092ꕡ獕뼩蠧ठ", (char) (18944 - ExpandableListView.getPackedPositionChild(0L)), "돘鱵ş豊", "\u0000\u0000\u0000\u0000", objArr2);
                    g.a(intern, ((String) objArr2[0]).intern(), e);
                    break;
                }
        }
        this.b = fVar;
        g.c();
        Object[] objArr3 = new Object[1];
        l(View.combineMeasuredStates(0, 0) + 1332820136, "䗵返耉ퟯ랸镰拢\udf0a\uf6db狆✵ച姌톍\udbeb䣿ᦹ储掿\ue3f0䂥䝠鈂휵\ufadc", (char) View.getDefaultSize(0, 0), "ꣂ焸챏茻", "\u0000\u0000\u0000\u0000", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        l(View.MeasureSpec.getSize(0) - 56879697, "캵롢映Ṝ㩏쀙牾赣ჩ돮徲鄵ㄬ唷\ud80f\ue7a7\u0ef3蔉뾕⭹쳏Ẹ譐蕤톊ቈ㲥\ud9ebり딈\ue612\ue4fa❅销\ueebc\uf3f3擕鼒敔뗒呰豅\ue90d陜룏抝⊳\uddfc庂標롓ޠḓ縪잟", (char) (Color.red(0) + 57878), "꽦鰕\u16fc㇢", "\u0000\u0000\u0000\u0000", objArr4);
        g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(this.b).toString());
    }

    public final f e() {
        int i = k;
        int i2 = i + 109;
        n = i2 % 128;
        int i3 = i2 % 2;
        f fVar = this.a;
        int i4 = i + 51;
        n = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public final void e(f fVar) {
        int i = k + 11;
        int i2 = i % 128;
        n = i2;
        boolean z = i % 2 != 0;
        this.a = fVar;
        switch (z) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i3 = i2 + 65;
                k = i3 % 128;
                switch (i3 % 2 != 0 ? (char) 21 : 'A') {
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        int i4 = 77 / 0;
                        return;
                    default:
                        return;
                }
        }
    }

    public final boolean b() {
        boolean z;
        int i = k;
        int i2 = i + 87;
        n = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                z = this.h;
                int i3 = 74 / 0;
                break;
            default:
                z = this.h;
                break;
        }
        int i4 = i + 91;
        n = i4 % 128;
        switch (i4 % 2 != 0 ? '\r' : (char) 31) {
            case '\r':
                int i5 = 48 / 0;
                return z;
            default:
                return z;
        }
    }

    public final void i() {
        int i = n + 41;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        this.h = true;
        int i4 = i2 + 43;
        n = i4 % 128;
        int i5 = i4 % 2;
    }

    public final List<o.i.f> f() {
        int i = k;
        int i2 = i + 57;
        n = i2 % 128;
        int i3 = i2 % 2;
        List<o.i.f> list = this.e;
        int i4 = i + 83;
        n = i4 % 128;
        switch (i4 % 2 != 0 ? Typography.less : ':') {
            case '<':
                throw null;
            default:
                return list;
        }
    }

    public final void d(List<o.i.f> list) {
        int i = n;
        int i2 = i + 47;
        k = i2 % 128;
        int i3 = i2 % 2;
        this.e = list;
        int i4 = i + 61;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    public final Long g() {
        int i = k;
        int i2 = i + 53;
        n = i2 % 128;
        int i3 = i2 % 2;
        Long l = this.d;
        int i4 = i + Opcodes.LSHR;
        n = i4 % 128;
        int i5 = i4 % 2;
        return l;
    }

    public final void d(Long l) {
        int i = k + 7;
        int i2 = i % 128;
        n = i2;
        boolean z = i % 2 != 0;
        this.d = l;
        switch (z) {
            case false:
                int i3 = i2 + 99;
                k = i3 % 128;
                switch (i3 % 2 == 0 ? '%' : 'T') {
                    case '%':
                        int i4 = 78 / 0;
                        return;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    public final boolean j() {
        int i = k + 9;
        int i2 = i % 128;
        n = i2;
        int i3 = i % 2;
        boolean z = this.i;
        int i4 = i2 + Opcodes.LSHL;
        k = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final void c(boolean z) {
        int i = k + 85;
        int i2 = i % 128;
        n = i2;
        int i3 = i % 2;
        this.i = z;
        int i4 = i2 + 57;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void h() {
        int i = k + 109;
        n = i % 128;
        switch (i % 2 != 0 ? '^' : (char) 21) {
            case 21:
                this.c = false;
                this.b = null;
                this.a = null;
                this.e = null;
                this.d = null;
                this.h = false;
                this.i = false;
                break;
            default:
                this.c = false;
                this.b = null;
                this.a = null;
                this.e = null;
                this.d = null;
                this.h = false;
                this.i = true;
                break;
        }
    }

    private static void l(int i, String str, char c, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] charArray;
        switch (str3 != null ? '8' : 'G') {
            case 'G':
                cArr = str3;
                break;
            default:
                cArr = str3.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        int i2 = 2;
        if (str2 != null) {
            int i3 = $10 + 15;
            $11 = i3 % 128;
            if (i3 % 2 == 0) {
                cArr2 = str2.toCharArray();
                int i4 = 76 / 0;
            } else {
                cArr2 = str2.toCharArray();
            }
        } else {
            cArr2 = str2;
        }
        char[] cArr4 = cArr2;
        switch (str != null ? (char) 29 : (char) 21) {
            case 29:
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i5 = $10 + Opcodes.LREM;
            $11 = i5 % 128;
            int i6 = i5 % i2;
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0') + 11, (char) (20953 - Process.getGidForName("")), 344 - Color.red(0));
                    byte b = $$a[0];
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    o(b, b2, b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), Color.red(0) + 207);
                        byte b3 = $$a[0];
                        byte b4 = b3;
                        Object[] objArr5 = new Object[1];
                        o(b3, b4, (byte) (b4 + 2), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr5[oVar.e % 4] * 32718), Integer.valueOf(cArr6[intValue])};
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0'), (char) TextUtils.indexOf("", "", 0, 0), (KeyEvent.getMaxKeyCode() >> 16) + 281);
                            byte b5 = $$a[0];
                            byte b6 = b5;
                            Object[] objArr7 = new Object[1];
                            o(b5, b6, (byte) (b6 + 4), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(19 - KeyEvent.normalizeMetaState(0), (char) (14687 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), 112 - View.resolveSize(0, 0));
                                byte b7 = $$a[0];
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                o(b7, b8, (byte) (b8 | 7), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r4[oVar.e]) ^ (g ^ 6565854932352255525L)) ^ ((int) (f ^ 6565854932352255525L))) ^ ((char) (j ^ 6565854932352255525L)));
                            oVar.e++;
                            i2 = 2;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str4 = new String(cArr7);
        int i7 = $10 + 19;
        $11 = i7 % 128;
        int i8 = i7 % 2;
        objArr[0] = str4;
    }
}
