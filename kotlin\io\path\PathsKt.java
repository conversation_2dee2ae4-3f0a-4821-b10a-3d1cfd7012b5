package kotlin.io.path;

import kotlin.Metadata;

@Metadata(d1 = {"kotlin/io/path/PathsKt__PathReadWriteKt", "kotlin/io/path/PathsKt__PathRecursiveFunctionsKt", "kotlin/io/path/PathsKt__PathUtilsKt"}, k = 4, mv = {1, 9, 0}, xi = 49)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\io\path\PathsKt.smali */
public final class PathsKt extends PathsKt__PathUtilsKt {
    private PathsKt() {
    }
}
