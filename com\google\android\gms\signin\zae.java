package com.google.android.gms.signin;

import com.google.android.gms.common.api.Api;
import com.google.android.gms.common.internal.IAccountAccessor;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\signin\zae.smali */
public interface zae extends Api.Client {
    void zaa();

    void zab();

    void zac(IAccountAccessor iAccountAccessor, boolean z);

    void zad(com.google.android.gms.signin.internal.zae zaeVar);
}
