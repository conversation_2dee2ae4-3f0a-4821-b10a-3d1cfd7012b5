package o.n;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.fragment.app.Fragment;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import kotlin.text.Typography;
import o.n.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\c.smali */
public final class c extends Fragment {
    private static int c = 0;
    private static int e = 1;
    private final d a;
    private final a.InterfaceC0045a b;
    private View d;

    public c(d dVar, a.InterfaceC0045a interfaceC0045a) {
        this.a = dVar;
        this.b = interfaceC0045a;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void e(View view) {
        int i = (e + 22) - 1;
        c = i % 128;
        boolean z = i % 2 != 0;
        this.a.l();
        switch (z) {
            case false:
                return;
            default:
                int i2 = 40 / 0;
                return;
        }
    }

    @Override // androidx.fragment.app.Fragment
    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        int i;
        ImageView imageView;
        View.OnClickListener onClickListener;
        int i2 = e;
        int i3 = (i2 & 9) + (i2 | 9);
        c = i3 % 128;
        int i4 = i3 % 2;
        View inflate = layoutInflater.inflate(R.layout.antelop_pin_prompt_fragment, viewGroup, false);
        this.d = inflate;
        inflate.findViewById(R.id.antelop_pin_prompt_root).setBackgroundResource(R.color.antelopPinPromptColorBackground);
        o.o.c a = this.a.a();
        a aVar = (a) this.d.findViewById(R.id.antelop_pin_prompt_keypad);
        switch (!requireContext().getResources().getBoolean(R.bool.antelopPinPromptEnableOverlayProtection)) {
            case true:
                break;
            default:
                int i5 = c;
                int i6 = (i5 ^ 37) + ((i5 & 37) << 1);
                e = i6 % 128;
                int i7 = i6 % 2;
                aVar.enableOverlayProtection(requireContext().getString(R.string.antelopPinPromptOverlayWarningMessage));
                break;
        }
        boolean z = requireContext().getResources().getBoolean(R.bool.antelopPinPromptShowAlpha);
        switch (a != null ? '2' : '\'') {
            case '\'':
                i = 0;
                break;
            default:
                int i8 = e;
                int i9 = (i8 ^ 67) + ((i8 & 67) << 1);
                c = i9 % 128;
                switch (i9 % 2 == 0 ? (char) 26 : 'C') {
                    case 'C':
                        a.d();
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        i = a.d();
                        break;
                }
        }
        aVar.initializeView(this.b, new a.d(this.a.m(), this.a.h(), requireContext().getResources().getColor(R.color.antelopPinPromptColorPrimary), requireContext().getResources().getColor(R.color.antelopPinPromptColorSecondary), R.style.antelopPinPromptKeyboardDigitStyle, R.style.antelopPinPromptKeyboardAlphaStyle, R.style.antelopPinPromptKeyboardBackgroundStyle, R.drawable.antelopPinPromptDeleteButtonIcon, i, z));
        switch (!this.a.f()) {
            case false:
                int i10 = c;
                int i11 = (i10 ^ 31) + ((i10 & 31) << 1);
                e = i11 % 128;
                switch (i11 % 2 == 0) {
                    case true:
                        imageView = (ImageView) this.d.findViewById(R.id.antelop_pin_prompt_cancel);
                        imageView.setVisibility(0);
                        onClickListener = new View.OnClickListener() { // from class: o.n.c$$ExternalSyntheticLambda0
                            @Override // android.view.View.OnClickListener
                            public final void onClick(View view) {
                                c.this.e(view);
                            }
                        };
                        break;
                    default:
                        imageView = (ImageView) this.d.findViewById(R.id.antelop_pin_prompt_cancel);
                        imageView.setVisibility(0);
                        onClickListener = new View.OnClickListener() { // from class: o.n.c$$ExternalSyntheticLambda0
                            @Override // android.view.View.OnClickListener
                            public final void onClick(View view) {
                                c.this.e(view);
                            }
                        };
                        break;
                }
                imageView.setOnClickListener(onClickListener);
                break;
        }
        ((TextView) this.d.findViewById(R.id.antelop_pin_prompt_title)).setText(this.a.j());
        ((TextView) this.d.findViewById(R.id.antelop_pin_prompt_subtitle)).setText(this.a.i());
        b();
        return this.d;
    }

    public final void b() {
        int i = e + 79;
        c = i % 128;
        int i2 = i % 2;
        a aVar = (a) this.d.findViewById(R.id.antelop_pin_prompt_keypad);
        Object obj = null;
        switch (this.a.g() ? Typography.quote : 'V') {
            case '\"':
                int i3 = (c + Opcodes.IAND) - 1;
                e = i3 % 128;
                char c2 = i3 % 2 == 0 ? '5' : 'M';
                aVar.scramble();
                switch (c2) {
                    case Opcodes.SALOAD /* 53 */:
                        obj.hashCode();
                        throw null;
                }
        }
        aVar.resetPasscode();
        int i4 = c + 41;
        e = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void a(String str) {
        int i = (c + 90) - 1;
        e = i % 128;
        int i2 = i % 2;
        TextView textView = (TextView) this.d.findViewById(R.id.antelop_pin_prompt_description);
        switch (str == null ? 'C' : 'W') {
            case 'C':
                int i3 = (c + 28) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
                textView.setVisibility(4);
                int i5 = c + 21;
                e = i5 % 128;
                int i6 = i5 % 2;
                break;
            default:
                textView.setVisibility(0);
                textView.setText(str);
                int i7 = e;
                int i8 = ((i7 | 47) << 1) - (i7 ^ 47);
                c = i8 % 128;
                int i9 = i8 % 2;
                break;
        }
    }
}
