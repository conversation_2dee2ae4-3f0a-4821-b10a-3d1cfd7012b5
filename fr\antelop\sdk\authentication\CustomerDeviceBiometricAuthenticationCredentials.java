package fr.antelop.sdk.authentication;

import javax.crypto.Cipher;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerDeviceBiometricAuthenticationCredentials.smali */
public final class CustomerDeviceBiometricAuthenticationCredentials extends CustomerAuthenticationCredentials {
    private final Cipher cipher;

    public CustomerDeviceBiometricAuthenticationCredentials(Cipher cipher) {
        this.cipher = cipher;
    }

    public final Cipher getCipher() {
        return this.cipher;
    }
}
