package o.ei;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.card.Card;
import fr.antelop.sdk.card.CreateCardRequestBuilder;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.settings.WalletSettings;
import fr.antelop.sdk.util.OperationCallback;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;
import kotlin.text.Typography;
import o.ax.d;
import o.az.e;
import o.cd.e;
import o.eo.h;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ei\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final Semaphore b;
    private static final c f;
    private static char[] l;
    private static long m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static int f61o;
    private o.en.d a;
    private boolean c;
    private o.fn.i d;
    private boolean e;
    private o.fm.c g;
    private o.em.e h;
    private o.ca.a i;
    private Context j;

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void A(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 4
            int r6 = r6 * 3
            int r6 = 1 - r6
            int r7 = r7 + 102
            byte[] r0 = o.ei.c.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L17:
            r3 = r2
        L18:
            r5 = r8
            r8 = r7
            r7 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            int r7 = r7 + 1
            int r4 = r3 + 1
            if (r3 != r6) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.A(byte, byte, byte, java.lang.Object[]):void");
    }

    static void D() {
        char[] cArr = new char[1158];
        ByteBuffer.wrap("Ë¡ç\u0095\u0093 O´{Ö\u0017àÃûÿ\u0001«\u001cG&s\"Öwúc\u008eQRSf;\n Þ\u0000âò¶üZÐ, \u0000´t\u0086¨\u0084\u009cìð÷$×\u0018%L+ \u0007\u0094WÈ5<\r\u0010[E»¹·í\u009cÁú5ëiÉ]Ú±(åKÙ\u0019\roaqUH\u008a½þ³Ò\u009a\u0006\u0096zö®Ì\u0082Þöo*'\u001e\rr\u001f¦w\u009aIÎ\u0011#«\u0017¹K\u0091¿ù\u0093÷ÇÂ;Øo0C\u0010·\u0016ënßr3\u0006`¼T½\u0088\u008dü\u009eÐâ\u0004Ü, \u0000´t\u0086¨\u0084\u009cìð÷$×\u0018%L+ \u0007\u0094WÈ5<\r\u0010XE¯¹¡í\u008aÁâ5öiÎ]Ò±få\u000eÙ\u0004\rhaaUS\u008a¡þ³Ò\u0089\u0006Ózç®Æ\u0082Ôö)*9\u001e\u0002r\u0003¦i\u009aMÎE#«\u0017¸K\u0096,¼\u0000ªt\u008b¨\u0091\u009cñðó,¼\u0000ªt\u008b¨\u0091\u009cñðó$\u009b\u0018aLq \u0011\u0094\u0002È{<N\u0010[E°¹§\rÝ!ËUê\u0089ð½\u0090Ñ\u0092\u0005ú9\u0000m\u0010\u0081eµwé\u0010\u001d 1*dÐ\u0098ÐÌ¸àÆ\u0014ÞH\u0096|µ\u0090KÄføx,\u0014@0t)«ÇßÚóæ'õ[\u0090\u008fº£º×Z\u000bX?kSy\u0087?»5ï3\u0002Æ6Æjí\u009e\u0085²\u0090æ¬\u001aõN]be\u0096}Ê\u000eþ\u0001\u0012)AÞuØ©òÝöñ\u0082,º\u0000¿t\u009b¨´\u009càðð$Ú\u00189L= \u0016\u00944Èy<_\u0010ZEã¹îíÙ,\u008a\u0000»t\u009d¨\u0094\bí$ÈPð\u008cï¸\u0093Ô\u0091×\u0007û\"\u008f\u001aS\u0005gy\u000b{ß\u0002ã¼·»[Ûo\u00803îÇÀë\u0087¾(B8\u0016\u000e:}Îo\u0092W¦KJó\u001eÒ\"\u0086ö÷\u009aå®Ðq4\u0005')\u0003ý\u0003\u0081rU^y\u0003\r¤Ñ¬å\u008d\u0089\u009a]ëaÇ5ÍØ?ØáôÎ\u0080ö\\¬h\u009e\u0004\u0083Ð±ìU¸CT>`h<\u0005È#ä&±\u009fMÁ\u0019ö5ÖÁ\u008d\u009d³©½E\u001a\u0011g-rù\u0012\u0095\u0018¡2~Ç\nÈ&ýòá\u008e\u009dZ±=\u009e\u0011¼e\u009e¹\u009e\u008dõáµ5Ù\t!]6±A\u0085\u0017Ùt-@\u0001YT©¨£ü\u0093Ðæ$òxÐL\u0096 3ô\tÈ\u0013\u001ckpuDE\u009b¿ï·Ã\u0082\u0017\u009ek§¿Ã\u0093Êçl;!\u000f\u0003c\u0004·m\u008bFß@2¤\u0006°,º\u0000¿t\u009b¨¾\u009càðî$Ï\u0018\u0018L# \u0003\u0094\u0019Èk<L\u0010]E·¹½í\u0096Áä5ÜiÁ]Ç±\"åKÙF\r!,®\u0000¿t\u009b¨¾\u009càðî$Ï\u0018\u0018L# \u0003\u0094\u0019Èk<L\u0010]E·¹½í\u0096Áä5ÜiÁ]Ç±\"å\"Ù\u0018cÍOÉ;êçãÓ\u0087¿®k¨WB\u0003Sï@Ûs\u0087\u000fs5_;\nÔöÁ¢û\u008e\u0095z\u0086&¸\u0012\u0080þQªo\u0096nvÿZî.ÈòÁÆ£ªã~ÃB9\u0016múYÎA\u0092!f\rJ\u000f\u001fóãÏ·Ã\u009b«o\u009a3\u0087\u0007\u008fëe¿W\u0083ZW=;(\u000f\u001cÐø¤ì\u0088ø\\Ç £ô\u0098Ø\u009c¬ p%Çìëò\u009fÉCØw°\u001b¶Ï\u0096óN§qKQ\u007fR#*×LûR®¢Rü\u0006Ö*¨Þ²\u0082\u0094¶\u0090Zb\u000ed2Ræ4\u008a\u0003¾\u0014aæ\u0015ê9ÆíÁ\u0091¬E\u0087i\u0095\u001dkÁuõg\u0099VM(q\t%\u0003È¹ü¶,®\u0000¿t\u009b¨³\u009cäðä$ß\u0018lL| B\u0094\u0014Èy<_\u0010ZE\u008a¹°íÃÁª,®\u0000¿t\u009b¨´\u009cìðñ$Ò\u00188L0 \u000e\u00944Èy<_\u0010ZEã¹ùíÙÁé5þiÒ]Ñ±\u000få\u000fÙF\r!,\u008c\u0000·t\u0099¨±\u009cõðæ$×\u0018%L2 \u0003\u0094\u0003Èq<B\u0010P,®\u0000¿t\u009b¨µ\u009cèðà$ú\u0018<L! \u000e\u0094\u001eÈ{<L\u0010JEª¹»í\u0097Áª5²i\u0080]Ð±+å\u001dÙ=\rqabUK\u008a¡þ¾Ò\u008f\u0006\u0087zí®Æ\u0082Ôö\u0006*4\u001e_rV,¡\u0000»t\u0081¨\u0094\u009céðó$÷\u0018#L2 \t$\u0007\b\u001d|' 2\u0094OøU,Q\u0010\u0085D\u0090¨«\u009c¤ÀÊ÷\u009dÛ\u0087¯½s¨GÕ+ÏÿÃÃ\u0015\u0097\u0001{;O?\u0013A+\u0094\u0007\u008es´¯¡\u009bÜ÷Æ#Ê\u001f\u001cK\b§2\u00936ÏH;8\u0017&BÖ¾\u0084ê´ÆÜ2ÏnåZô¶\u001aâ1Þ'\n\u0014fPRz\u008d\u0098ù\u0086Õû\u0001 }Ý©é\u0085üñ\u0012-\f\u0019>u$¡\u000e\u009dxÉg$\u0083\u0010\u008bL»¸Ù\u0094ßÀÿ<îh\u0002D\u0013°9ì^Øp4ag\u0091S\u009f\u008f½û£××\u0003ï,½\u0000¨t\u0086¨\u0097\u009câðó$É\u0018\u000fL0 \u0010\u0094\u0013È\\<H\u0010RE¦¹ í\u009cÁª5²i\u0080]Ö±'å\u0019Ù\u0018\rHavU\u0007\u008aòþý,½\u0000¨t\u0086¨\u0097\u009câðó$É\u0018\u000fL0 \u0010\u0094\u0013ÈM<]\u0010ZE¢¹ í\u009cÁª5²i\u0080]Ö±'å\u0019Ù\u0018\rHavU\u0007\u008aòþý,½\u0000¨t\u0086¨\u0097\u009câðó$É\u0018\u000fL0 \u0010\u0094\u0013ÈT<B\u0010]E¨¹ôíÔÁª5üiÁ]Ç±\"å\"Ù\u0018\r!a(U\u0007,¹\u0000¿t\u009d¨\u0083\u009cìðå$Ï\u0018%L? \u0005\u0094WÈm<]\u0010ZE¢¹ í\u009cÁî5¿i×]Ô±*å\u0007Ù\u0019\rua2UD\u008a§þ³Ò\u0088\u0006\u009azã®Ü\u0082Èö.*$\u001e\fr\u0019¦u, \u0000©t½¨\u0085\u009cëðø$Ò\u0018\"L6 J\u0094^È8<\u0000\u0010\u001eE\u008a¹ºí\u008dÁï5íiÒ]À±6å\u001fÙ\u0019\reaWU_\u008a«þ¸Ò\u009e\u0006\u0087zí®Æ\u0082Ôöo*'\u001e\rr\u001f¦w\u009aIÎ\u0011#µ\u0017¶K\u0091¿ù\u0093÷ÇÍ;ÓoyC\f·\u0010ërß53Q`ªT°\u0088\u008dü\u0097Ðó\u0004\u0088xÈ¬>\u00807ô\u0005(}\u001c\u007fp\u000f¤\\\u0099ªÍµ!\u0090M\u008fa\u008b\u0015ªÉ±ýÔ\u0091ÑEûy>-\nÁ$õ\u0014©M]uqf$\u0092Ø\u0094\u008c\u0098 ÎTÏ\bõ<õÐ\u0017\u0084\u007f¸el\u0015\u0000G4`ë\u0097\u009fÉ³¼g¨\u001bÂÏ½ãý\u0097\u001eK\t\u007f0\u00132ÇGûw¯wB\u0093q\u009a]\u009e)¿õ¤ÁÁ\u00adÄyîE+\u0011\u001fý1É\u0001\u0095Xa`Ms\u0018\u0087ä\u0081°\u008d\u009cÛhÚ4à\u0000àì\u0002¸j\u0084pP\u0000<@\bc×\u0084£\u009d\u008f¿[º'Êóúßþ«Nw\u0010C'/&ûOÇd\u0093b~\u0086J\u0092=\u008b\u0011\u008fe³¹¥\u008dÔáÕ5î\t=]\u0007±&\u0085\"ÙZ-J\u0001oT\u009f¨\u0088ü¬ÐÎ$úxàLá \u0017ô/È)\u001c\u0011p\u000fD7\u009b\u008aï\u0088Ã²\u0017¦kÕ¿ê\u0093ïç_;\u0013\u000f0c+·J\u008blßi2\u009d\u0006\u0095Z\u00ad£S\u008fqûS'H\u0013(\u007f*«!\u0097ôÃú/ß\u001büG¤³\u0085\u009f\u0092Ê\u007f6~bTN\u0011º3æ\u0010Ò\u0000>ûj×V×,ª\u0000¨t\u008a¨\u0091\u009cñðó$ø\u0018-L# \u0006,\u008d\u0000³t\u0088¨\u0099\u009cñð÷$×\u0018\u000fL0 \u0010\u0094\u0013È]<C\u0010LE¬¹¸í\u0095Áç5úiÎ]Á±\u0002å\nÙ\b\r`ßßóÇ\u0087î[ìo\u009a\u0003\u0089×\u008cëV¿ESxgp;\nÏ2ã\u000e¶ÑJÕ\u001eîý\u001fÑ\u000e¥*y\u0017M@!TõIÉ\u0091\u009d\u0089q¶E¨\u0019ÝíËÁî\u0094\u001eh\t<-\u0010Oäo¸r\u008cg`\u00984¯\b£ÜÄ°ê\u0084ò[Y/A\u0003\u007f×+«F\u007fkS~'\u009bû\u0093Ïô£\u008ewÎK½\u001fîò\u001cÆ\u0012\u009ainOBJ\u0016f\u0087x«|ßJ\u0003A7#[&\u008f\u0010³Ëçû\u000bÆ?Ýc¯\u0097\u008f»\u0091îC\u0012vFHj-\u009e/".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1158);
        l = cArr;
        m = 7911181011690193114L;
    }

    static void init$0() {
        $$a = new byte[]{118, -84, -110, 65};
        $$b = 95;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f61o = 0;
        n = 1;
        D();
        Color.rgb(0, 0, 0);
        Drawable.resolveOpacity(0, 0);
        AudioTrack.getMinVolume();
        View.combineMeasuredStates(0, 0);
        TextUtils.getOffsetBefore("", 0);
        ViewConfiguration.getScrollBarSize();
        b = new Semaphore(1);
        f = new c();
        int i = f61o + 87;
        n = i % 128;
        int i2 = i % 2;
    }

    public static c c() {
        int i = n + 15;
        int i2 = i % 128;
        f61o = i2;
        int i3 = i % 2;
        c cVar = f;
        int i4 = i2 + 15;
        n = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    private c() {
    }

    public final synchronized void b(Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (View.MeasureSpec.getSize(0) + 59169), Drawable.resolveOpacity(0, 0), KeyEvent.keyCodeFromString("") + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (64215 - Drawable.resolveOpacity(0, 0)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 11, Color.red(0) + 10, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.i = new o.ca.a(this);
        try {
            this.j = context.getApplicationContext();
            o.fn.i iVar = new o.fn.i();
            this.d = iVar;
            iVar.d(context);
            o.en.d dVar = new o.en.d();
            this.a = dVar;
            dVar.c(context);
            o.fm.c cVar = new o.fm.c();
            this.g = cVar;
            cVar.a(context);
            o.em.e eVar = new o.em.e();
            this.h = eVar;
            eVar.a(context);
        } catch (i e) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            z((char) ((ViewConfiguration.getTouchSlop() >> 8) + 59169), Drawable.resolveOpacity(0, 0), 11 - View.MeasureSpec.getSize(0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            z((char) View.resolveSize(0, 0), 21 - KeyEvent.keyCodeFromString(""), 61 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr4);
            o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e);
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            z((char) (59169 - View.combineMeasuredStates(0, 0)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, 11 - Drawable.resolveOpacity(0, 0), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            z((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.getTrimmedLength("") + 81, 44 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr6);
            o.ee.g.e(intern3, ((String) objArr6[0]).intern());
            this.d = new o.fn.i();
            this.a = new o.en.d();
            this.g = new o.fm.c();
            this.h = new o.em.e();
            this.d.a(context);
            this.a.a(context);
            this.g.b(context);
            this.h.e(context);
        }
        this.e = true;
        int i = f61o + 41;
        n = i % 128;
        switch (i % 2 != 0 ? '?' : (char) 2) {
            case '?':
                break;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final synchronized o.fk.d e(Context context, o.eg.b bVar) {
        o.fk.d dVar;
        boolean z;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((Process.myPid() >> 22) + 59169), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionGroup(0L) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) TextUtils.getTrimmedLength(""), 126 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 6, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        dVar = new o.fk.d();
        try {
            o.fk.e b2 = this.d.b(context, bVar, false);
            o.fk.b e = this.a.e(context, bVar);
            List<String> c = this.a.c(context, bVar);
            dVar.b(this.d.e().b());
            dVar.d(b2);
            dVar.d(e);
            Iterator<String> it = c.iterator();
            while (it.hasNext()) {
                dVar.i().b(new o.fk.a(it.next(), m(), o.fk.c.f));
            }
            if (c.isEmpty()) {
                z = false;
            } else {
                int i = n + Opcodes.LSUB;
                f61o = i % 128;
                int i2 = i % 2;
                z = true;
            }
            dVar.e(z);
            new o.dt.c();
            dVar.b(o.dt.c.b(context, this, bVar));
            switch (e.f() ? 'C' : (char) 17) {
                case 'C':
                    this.g.e().e(this.a.j());
                    break;
            }
            if (!this.a.j().isEmpty()) {
                int i3 = f61o + 95;
                n = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        dVar.c(this.a.a());
                        int i4 = 75 / 0;
                        break;
                    default:
                        dVar.c(this.a.a());
                        break;
                }
            }
            d(context);
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            z((char) (59169 - Drawable.resolveOpacity(0, 0)), ViewConfiguration.getPressedStateDuration() >> 16, 11 - View.resolveSize(0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            z((char) ((Process.getThreadPriority(0) + 20) >> 6), (KeyEvent.getMaxKeyCode() >> 16) + Opcodes.LXOR, 17 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
            dVar.a(true);
        } catch (i e2) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            z((char) ((ViewConfiguration.getTouchSlop() >> 8) + 59169), Process.myTid() >> 22, 12 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            z((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 8544), TextUtils.lastIndexOf("", '0', 0) + Opcodes.LCMP, 59 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr6);
            o.ee.g.a(intern3, ((String) objArr6[0]).intern(), e2);
            dVar.a(false);
        }
        return dVar;
    }

    public final synchronized boolean b() {
        boolean z;
        int i = f61o;
        int i2 = i + 75;
        n = i2 % 128;
        int i3 = i2 % 2;
        z = this.e;
        int i4 = i + 45;
        n = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 22 : Typography.dollar) {
            case 22:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
        return z;
    }

    public final o.fn.i e() {
        int i = n + Opcodes.DSUB;
        int i2 = i % 128;
        f61o = i2;
        switch (i % 2 == 0) {
            case true:
                o.fn.i iVar = this.d;
                int i3 = i2 + 37;
                n = i3 % 128;
                int i4 = i3 % 2;
                return iVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final o.en.d a() {
        int i = n + 65;
        int i2 = i % 128;
        f61o = i2;
        int i3 = i % 2;
        o.en.d dVar = this.a;
        int i4 = i2 + 1;
        n = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 65 / 0;
                return dVar;
            default:
                return dVar;
        }
    }

    public final o.fm.c d() {
        o.fm.c cVar;
        int i = n;
        int i2 = i + 1;
        f61o = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                cVar = this.g;
                int i3 = 19 / 0;
                break;
            default:
                cVar = this.g;
                break;
        }
        int i4 = i + 77;
        f61o = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public final o.em.e j() {
        int i = f61o;
        int i2 = i + 59;
        n = i2 % 128;
        switch (i2 % 2 == 0 ? '-' : (char) 17) {
            case '-':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                o.em.e eVar = this.h;
                int i3 = i + 43;
                n = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        return eVar;
                    default:
                        int i4 = 49 / 0;
                        return eVar;
                }
        }
    }

    public final void a(String str) throws WalletValidationException {
        Object[] objArr = new Object[1];
        z((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (ViewConfiguration.getTapTimeout() >> 16) + 223, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 4, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        z((char) (Color.blue(0) + 59169), ViewConfiguration.getEdgeSlop() >> 16, TextUtils.indexOf((CharSequence) "", '0') + 12, objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        z((char) (TextUtils.lastIndexOf("", '0', 0) + 1), 206 - ExpandableListView.getPackedPositionGroup(0L), TextUtils.getCapsMode("", 0, 0) + 17, objArr3);
        o.ee.g.d(intern2, sb.append(((String) objArr3[0]).intern()).append(str).toString());
        if (str == null) {
            throw new WalletValidationException(WalletValidationErrorCode.Mandatory, intern);
        }
        if (!q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            z((char) (TextUtils.indexOf((CharSequence) "", '0') + 9332), 227 - KeyEvent.getDeadChar(0, 0), TextUtils.getCapsMode("", 0, 0) + 6, objArr4);
            String intern3 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            z((char) (View.getDefaultSize(0, 0) + 64409), 234 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (ViewConfiguration.getTouchSlop() >> 8) + 42, objArr5);
            throw new WalletValidationException(walletValidationErrorCode, intern3, ((String) objArr5[0]).intern());
        }
        if (this.a.i().d(str)) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            z((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 62589), 276 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), 33 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr6);
            throw new WalletValidationException(walletValidationErrorCode2, intern, ((String) objArr6[0]).intern());
        }
        o.eo.e eVar = this.a.j().get(str);
        if (eVar == null) {
            throw new WalletValidationException(WalletValidationErrorCode.Unknown, intern);
        }
        if (eVar.h() != a.d) {
            throw new WalletValidationException(WalletValidationErrorCode.Unexpected, intern);
        }
        if (!eVar.y()) {
            throw new WalletValidationException(WalletValidationErrorCode.WrongState, intern);
        }
        if (eVar.t() != h.a) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr7 = new Object[1];
            z((char) (KeyEvent.keyCodeFromString("") + 4355), TextUtils.getOffsetAfter("", 0) + 308, 44 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr7);
            throw new WalletValidationException(walletValidationErrorCode3, intern, ((String) objArr7[0]).intern());
        }
        this.g.e().d(str);
        int i = n + Opcodes.LNEG;
        f61o = i % 128;
        switch (i % 2 != 0 ? '\t' : (char) 21) {
            case '\t':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final String h() {
        String d = this.g.e().d();
        LinkedHashMap<String, o.eo.e> j = this.a.j();
        if (d == null) {
            return null;
        }
        int i = f61o + 61;
        n = i % 128;
        switch (i % 2 == 0) {
            case false:
                switch (j.containsKey(d)) {
                    case false:
                        return null;
                }
            default:
                int i2 = 84 / 0;
                if (!j.containsKey(d)) {
                    return null;
                }
                break;
        }
        int i3 = n + Opcodes.DSUB;
        f61o = i3 % 128;
        int i4 = i3 % 2;
        return d;
    }

    public final void e(String str) throws WalletValidationException {
        Object[] objArr = new Object[1];
        z((char) (ViewConfiguration.getTouchSlop() >> 8), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 223, 4 - (Process.myTid() >> 22), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        z((char) (AndroidCharacter.getMirror('0') + 59121), (-1) - TextUtils.lastIndexOf("", '0', 0, 0), TextUtils.lastIndexOf("", '0') + 12, objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        z((char) TextUtils.indexOf("", "", 0, 0), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 350, 25 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr3);
        o.ee.g.d(intern2, sb.append(((String) objArr3[0]).intern()).append(str).toString());
        if (str == null) {
            throw new WalletValidationException(WalletValidationErrorCode.Mandatory, intern);
        }
        if (!q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr4 = new Object[1];
            z((char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 9332), 227 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), Gravity.getAbsoluteGravity(0, 0) + 6, objArr4);
            String intern3 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            z((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 64409), 233 - View.MeasureSpec.makeMeasureSpec(0, 0), View.resolveSize(0, 0) + 42, objArr5);
            throw new WalletValidationException(walletValidationErrorCode, intern3, ((String) objArr5[0]).intern());
        }
        if (this.a.i().d(str)) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            z((char) (62587 - MotionEvent.axisFromString("")), 274 - TextUtils.lastIndexOf("", '0', 0, 0), 32 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr6);
            throw new WalletValidationException(walletValidationErrorCode2, intern, ((String) objArr6[0]).intern());
        }
        o.eo.e eVar = this.a.j().get(str);
        if (eVar == null) {
            throw new WalletValidationException(WalletValidationErrorCode.Unknown, intern);
        }
        if (eVar.h() != a.d) {
            throw new WalletValidationException(WalletValidationErrorCode.Unexpected, intern);
        }
        if (!eVar.y()) {
            throw new WalletValidationException(WalletValidationErrorCode.WrongState, intern);
        }
        if (eVar.t() != h.a) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr7 = new Object[1];
            z((char) (4354 - TextUtils.indexOf((CharSequence) "", '0', 0)), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 309, 43 - View.MeasureSpec.getSize(0), objArr7);
            throw new WalletValidationException(walletValidationErrorCode3, intern, ((String) objArr7[0]).intern());
        }
        this.g.e().b(str);
        int i = f61o + Opcodes.LSUB;
        n = i % 128;
        int i2 = i % 2;
    }

    public final String g() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (59168 - TextUtils.indexOf((CharSequence) "", '0', 0)), Color.green(0), 11 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 377 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), Color.alpha(0) + 24, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        String h = this.g.e().h();
        switch (h == null) {
            case false:
                switch (!this.a.j().keySet().contains(h) ? 'E' : (char) 26) {
                    case 26:
                        int i = f61o + 57;
                        n = i % 128;
                        int i2 = i % 2;
                        return h;
                    default:
                        int i3 = f61o + 83;
                        n = i3 % 128;
                        if (i3 % 2 == 0) {
                        }
                        this.g.e().b(null);
                        return h();
                }
            default:
                int i4 = f61o + 53;
                n = i4 % 128;
                if (i4 % 2 != 0) {
                    return h();
                }
                h();
                throw null;
        }
    }

    public final void f() throws WalletValidationException {
        int i = f61o + 67;
        n = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 59168), ViewConfiguration.getPressedStateDuration() >> 16, 11 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (Color.red(0) + 20342), 400 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 24 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            z((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 9331), Color.blue(0) + 227, TextUtils.getOffsetAfter("", 0) + 6, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            z((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 64408), 233 - View.MeasureSpec.getSize(0), 42 - Color.blue(0), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        this.g.e().g();
        int i3 = f61o + 29;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.Map<java.lang.String, fr.antelop.sdk.card.Card> b(boolean r9) {
        /*
            r8 = this;
            o.ee.g.c()
            r0 = 0
            float r1 = android.graphics.PointF.length(r0, r0)
            int r1 = (r1 > r0 ? 1 : (r1 == r0 ? 0 : -1))
            r2 = 59169(0xe721, float:8.2913E-41)
            int r1 = r1 + r2
            char r1 = (char) r1
            r2 = 0
            float r3 = android.util.TypedValue.complexToFloat(r2)
            int r0 = (r3 > r0 ? 1 : (r3 == r0 ? 0 : -1))
            java.lang.String r3 = ""
            r4 = 48
            int r5 = android.text.TextUtils.lastIndexOf(r3, r4, r2)
            int r5 = 10 - r5
            r6 = 1
            java.lang.Object[] r7 = new java.lang.Object[r6]
            z(r1, r0, r5, r7)
            r0 = r7[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            int r5 = android.text.TextUtils.getTrimmedLength(r3)
            int r5 = r5 + 23125
            char r5 = (char) r5
            int r7 = android.graphics.Color.blue(r2)
            int r7 = r7 + 424
            int r3 = android.text.TextUtils.lastIndexOf(r3, r4, r2)
            int r3 = 35 - r3
            java.lang.Object[] r4 = new java.lang.Object[r6]
            z(r5, r7, r3, r4)
            r3 = r4[r2]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r1 = r1.append(r3)
            java.lang.StringBuilder r1 = r1.append(r9)
            java.lang.String r1 = r1.toString()
            o.ee.g.d(r0, r1)
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            o.en.d r1 = r8.a
            java.util.LinkedHashMap r9 = r1.c(r9)
            java.util.Set r9 = r9.entrySet()
            java.util.Iterator r9 = r9.iterator()
            int r1 = o.ei.c.f61o
            int r1 = r1 + 47
            int r3 = r1 % 128
            o.ei.c.n = r3
            int r1 = r1 % 2
        L80:
            boolean r1 = r9.hasNext()
            if (r1 == 0) goto L89
            r1 = 88
            goto L8b
        L89:
            r1 = 86
        L8b:
            switch(r1) {
                case 88: goto L8f;
                default: goto L8e;
            }
        L8e:
            return r0
        L8f:
            int r1 = o.ei.c.f61o
            int r1 = r1 + r6
            int r3 = r1 % 128
            o.ei.c.n = r3
            int r1 = r1 % 2
            if (r1 != 0) goto L9d
            r1 = 44
            goto L9f
        L9d:
            r1 = 57
        L9f:
            switch(r1) {
                case 44: goto Lb7;
                default: goto La2;
            }
        La2:
            java.lang.Object r1 = r9.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r3 = r1.getValue()
            o.eo.e r3 = (o.eo.e) r3
            o.ei.a r3 = r3.h()
            o.ei.a r4 = o.ei.a.d
            if (r3 != r4) goto Le5
        Lb6:
            goto Ld1
        Lb7:
            java.lang.Object r1 = r9.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r3 = r1.getValue()
            o.eo.e r3 = (o.eo.e) r3
            o.ei.a r3 = r3.h()
            o.ei.a r4 = o.ei.a.d
            r5 = 28
            int r5 = r5 / r2
            if (r3 != r4) goto Le5
            goto Lb6
        Lcf:
            r9 = move-exception
            throw r9
        Ld1:
            java.lang.Object r3 = r1.getKey()
            java.lang.String r3 = (java.lang.String) r3
            fr.antelop.sdk.card.Card r4 = new fr.antelop.sdk.card.Card
            java.lang.Object r1 = r1.getValue()
            o.eo.e r1 = (o.eo.e) r1
            r4.<init>(r1)
            r0.put(r3, r4)
        Le5:
            goto L80
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.b(boolean):java.util.Map");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.Map<java.lang.String, fr.antelop.sdk.digitalcard.DigitalCard> d(boolean r10) {
        /*
            r9 = this;
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getScrollBarSize()
            int r0 = r0 >> 8
            r1 = 59169(0xe721, float:8.2913E-41)
            int r1 = r1 - r0
            char r0 = (char) r1
            java.lang.String r1 = ""
            int r2 = android.os.Process.getGidForName(r1)
            r3 = 1
            int r2 = r2 + r3
            int r4 = android.view.ViewConfiguration.getEdgeSlop()
            int r4 = r4 >> 16
            int r4 = 11 - r4
            java.lang.Object[] r5 = new java.lang.Object[r3]
            z(r0, r2, r4, r5)
            r0 = 0
            r2 = r5[r0]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            r5 = 60225(0xeb41, float:8.4393E-41)
            int r1 = android.text.TextUtils.indexOf(r1, r1, r0, r0)
            int r5 = r5 - r1
            char r1 = (char) r5
            double r5 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r0)
            r7 = 0
            int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            int r5 = 460 - r5
            int r6 = android.view.KeyEvent.normalizeMetaState(r0)
            int r6 = r6 + 43
            java.lang.Object[] r3 = new java.lang.Object[r3]
            z(r1, r5, r6, r3)
            r0 = r3[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r0 = r4.append(r0)
            java.lang.StringBuilder r0 = r0.append(r10)
            java.lang.String r0 = r0.toString()
            o.ee.g.d(r2, r0)
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            o.en.d r1 = r9.a
            java.util.LinkedHashMap r10 = r1.c(r10)
            java.util.Set r10 = r10.entrySet()
            java.util.Iterator r10 = r10.iterator()
            int r1 = o.ei.c.n
            int r1 = r1 + 101
            int r2 = r1 % 128
            o.ei.c.f61o = r2
            int r1 = r1 % 2
        L84:
            boolean r1 = r10.hasNext()
            r2 = 45
            if (r1 == 0) goto L8e
            r1 = r2
            goto L90
        L8e:
            r1 = 64
        L90:
            switch(r1) {
                case 64: goto La0;
                default: goto L93;
            }
        L93:
            int r1 = o.ei.c.f61o
            int r1 = r1 + 101
            int r2 = r1 % 128
            o.ei.c.n = r2
            int r1 = r1 % 2
            if (r1 != 0) goto Laa
            goto Laa
        La0:
            int r10 = o.ei.c.n
            int r10 = r10 + r2
            int r1 = r10 % 128
            o.ei.c.f61o = r1
            int r10 = r10 % 2
            return r0
        Laa:
            java.lang.Object r1 = r10.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r2 = r1.getValue()
            o.eo.e r2 = (o.eo.e) r2
            o.ei.a r2 = r2.h()
            o.ei.a r3 = o.ei.a.b
            if (r2 != r3) goto Ld3
            java.lang.Object r2 = r1.getKey()
            java.lang.String r2 = (java.lang.String) r2
            fr.antelop.sdk.digitalcard.DigitalCard r3 = new fr.antelop.sdk.digitalcard.DigitalCard
            java.lang.Object r1 = r1.getValue()
            o.eo.e r1 = (o.eo.e) r1
            r3.<init>(r1)
            r0.put(r2, r3)
        Ld3:
            int r1 = o.ei.c.n
            int r1 = r1 + 71
            int r2 = r1 % 128
            o.ei.c.f61o = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L84
            goto L84
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.d(boolean):java.util.Map");
    }

    public final Map<String, o.eo.e> i() {
        HashMap hashMap = new HashMap();
        Iterator<Map.Entry<String, o.eo.e>> it = this.a.j().entrySet().iterator();
        while (true) {
            switch (!it.hasNext()) {
                case false:
                    int i = n + 67;
                    f61o = i % 128;
                    int i2 = i % 2;
                    Map.Entry<String, o.eo.e> next = it.next();
                    hashMap.put(next.getKey(), next.getValue());
                    int i3 = f61o + Opcodes.LREM;
                    n = i3 % 128;
                    int i4 = i3 % 2;
                default:
                    return hashMap;
            }
        }
    }

    public final WalletSettings n() {
        WalletSettings walletSettings = new WalletSettings(new WalletSettings.Limits(e().d()), new WalletSettings.Security(e().c(), e().b()), new WalletSettings.Locale(e().a()));
        int i = f61o + Opcodes.LNEG;
        n = i % 128;
        int i2 = i % 2;
        return walletSettings;
    }

    public final Card c(String str) throws WalletValidationException {
        int i = n + 21;
        f61o = i % 128;
        int i2 = i % 2;
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            z((char) View.resolveSizeAndState(0, 0, 0), 222 - TextUtils.lastIndexOf("", '0'), (KeyEvent.getMaxKeyCode() >> 16) + 4, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        z((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 59170), '0' - AndroidCharacter.getMirror('0'), (ViewConfiguration.getScrollBarSize() >> 8) + 11, objArr2);
        String intern = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        z((char) View.resolveSize(0, 0), ((Process.getThreadPriority(0) + 20) >> 6) + 503, 18 - ((Process.getThreadPriority(0) + 20) >> 6), objArr3);
        o.ee.g.d(intern, sb.append(((String) objArr3[0]).intern()).append(str).toString());
        o.eo.e eVar = this.a.j().get(str);
        switch (eVar != null) {
            default:
                int i3 = n + 11;
                f61o = i3 % 128;
                int i4 = i3 % 2;
                switch (eVar.h() == a.d) {
                    case true:
                        Card card = new Card(eVar);
                        int i5 = f61o + 87;
                        n = i5 % 128;
                        if (i5 % 2 != 0) {
                            return card;
                        }
                        throw null;
                }
            case false:
                return null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x001c, code lost:
    
        if (r10 != null) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
    
        if (r10 != null) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x00ad, code lost:
    
        r0 = fr.antelop.sdk.exception.WalletValidationErrorCode.Mandatory;
        r1 = new java.lang.Object[1];
        z((char) android.text.TextUtils.indexOf("", ""), android.view.View.MeasureSpec.getSize(0) + 223, 4 - android.view.View.MeasureSpec.makeMeasureSpec(0, 0), r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x00d4, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r0, ((java.lang.String) r1[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final fr.antelop.sdk.digitalcard.DigitalCard b(java.lang.String r10) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r9 = this;
            int r0 = o.ei.c.n
            int r0 = r0 + 101
            int r1 = r0 % 128
            o.ei.c.f61o = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Le
            r0 = 3
            goto L10
        Le:
            r0 = 73
        L10:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 73: goto L16;
                default: goto L15;
            }
        L15:
            goto L19
        L16:
            if (r10 == 0) goto Lad
        L18:
            goto L1f
        L19:
            r0 = 44
            int r0 = r0 / r2
            if (r10 == 0) goto Lad
            goto L18
        L1f:
            o.ee.g.c()
            long r3 = android.view.ViewConfiguration.getZoomControlsTimeout()
            r5 = 0
            int r0 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            r3 = 59168(0xe720, float:8.2912E-41)
            int r0 = r0 + r3
            char r0 = (char) r0
            int r3 = android.graphics.Color.green(r2)
            int r4 = android.graphics.Color.alpha(r2)
            int r4 = 11 - r4
            java.lang.Object[] r5 = new java.lang.Object[r1]
            z(r0, r3, r4, r5)
            r0 = r5[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            int r4 = android.graphics.ImageFormat.getBitsPerPixel(r2)
            int r4 = (-1) - r4
            char r4 = (char) r4
            double r5 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r2)
            r7 = 0
            int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            int r5 = r5 + 521
            int r6 = android.view.View.resolveSize(r2, r2)
            int r6 = 25 - r6
            java.lang.Object[] r7 = new java.lang.Object[r1]
            z(r4, r5, r6, r7)
            r4 = r7[r2]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r3 = r3.append(r4)
            java.lang.StringBuilder r3 = r3.append(r10)
            java.lang.String r3 = r3.toString()
            o.ee.g.d(r0, r3)
            o.en.d r0 = r9.a
            java.util.LinkedHashMap r0 = r0.j()
            java.lang.Object r10 = r0.get(r10)
            o.eo.e r10 = (o.eo.e) r10
            if (r10 == 0) goto Lab
            int r0 = o.ei.c.n
            int r0 = r0 + 55
            int r3 = r0 % 128
            o.ei.c.f61o = r3
            int r0 = r0 % 2
            o.ei.a r0 = r10.h()
            o.ei.a r3 = o.ei.a.b
            if (r0 == r3) goto La0
            r1 = r2
            goto La1
        La0:
        La1:
            switch(r1) {
                case 1: goto La5;
                default: goto La4;
            }
        La4:
            goto Lab
        La5:
            fr.antelop.sdk.digitalcard.DigitalCard r0 = new fr.antelop.sdk.digitalcard.DigitalCard
            r0.<init>(r10)
            return r0
        Lab:
            r10 = 0
            return r10
        Lad:
            fr.antelop.sdk.exception.WalletValidationException r10 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r0 = fr.antelop.sdk.exception.WalletValidationErrorCode.Mandatory
            java.lang.String r3 = ""
            int r3 = android.text.TextUtils.indexOf(r3, r3)
            char r3 = (char) r3
            int r4 = android.view.View.MeasureSpec.getSize(r2)
            int r4 = r4 + 223
            int r5 = android.view.View.MeasureSpec.makeMeasureSpec(r2, r2)
            int r5 = 4 - r5
            java.lang.Object[] r1 = new java.lang.Object[r1]
            z(r3, r4, r5, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r10.<init>(r0, r1)
            throw r10
        Ld5:
            r10 = move-exception
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.b(java.lang.String):fr.antelop.sdk.digitalcard.DigitalCard");
    }

    public final EmvApplication d(String str) throws WalletValidationException {
        int i = f61o + 73;
        n = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                if (str == null) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
                    Object[] objArr = new Object[1];
                    z((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), 546 - Gravity.getAbsoluteGravity(0, 0), 14 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                o.ee.g.c();
                Object[] objArr2 = new Object[1];
                z((char) (59169 - Color.green(0)), TextUtils.indexOf("", "", 0), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 11, objArr2);
                String intern = ((String) objArr2[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                z((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), View.getDefaultSize(0, 0) + 560, ExpandableListView.getPackedPositionType(0L) + 38, objArr3);
                o.ee.g.d(intern, sb.append(((String) objArr3[0]).intern()).append(str).toString());
                o.el.d d = this.a.d(str);
                switch (d != null) {
                    case false:
                        int i2 = f61o + Opcodes.LSHR;
                        n = i2 % 128;
                        int i3 = i2 % 2;
                        return null;
                    default:
                        return d.a();
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final String o() {
        int i = n + Opcodes.LUSHR;
        f61o = i % 128;
        switch (i % 2 != 0) {
            case false:
                return this.d.i();
            default:
                this.d.i();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String k() {
        int i = n + 81;
        f61o = i % 128;
        switch (i % 2 != 0 ? (char) 23 : '(') {
            case '(':
                String h = this.d.h();
                int i2 = f61o + 35;
                n = i2 % 128;
                switch (i2 % 2 == 0 ? 'R' : 'S') {
                    case Opcodes.AASTORE /* 83 */:
                        return h;
                    default:
                        throw null;
                }
            default:
                this.d.h();
                throw null;
        }
    }

    public final String l() {
        int i = n + 99;
        f61o = i % 128;
        int i2 = i % 2;
        String f2 = this.d.f();
        int i3 = n + 49;
        f61o = i3 % 128;
        int i4 = i3 % 2;
        return f2;
    }

    public final String m() {
        int i = n + Opcodes.LREM;
        f61o = i % 128;
        int i2 = i % 2;
        String j = this.d.j();
        int i3 = f61o + 59;
        n = i3 % 128;
        int i4 = i3 % 2;
        return j;
    }

    public final void c(Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (View.getDefaultSize(0, 0) + 59169), (-1) - ImageFormat.getBitsPerPixel(0), 11 - TextUtils.indexOf("", ""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (KeyEvent.getMaxKeyCode() >> 16), 598 - Gravity.getAbsoluteGravity(0, 0), 11 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.dl.c();
        o.dl.c.b().a();
        this.a.c();
        this.a.f().c();
        this.a.a(context);
        int i = n + Opcodes.LUSHR;
        f61o = i % 128;
        switch (i % 2 != 0 ? (char) 18 : (char) 21) {
            case 18:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void a(Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 59169), View.combineMeasuredStates(0, 0), View.MeasureSpec.getMode(0) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (2214 - (ViewConfiguration.getEdgeSlop() >> 16)), 608 - Color.blue(0), Color.argb(0, 0, 0, 0) + 12, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.dl.c();
        o.dl.c.b().a();
        this.a.c();
        this.a.f().c();
        this.a.a(context);
        int i = n + 7;
        f61o = i % 128;
        int i2 = i % 2;
    }

    public final void e(Context context) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 59168), AndroidCharacter.getMirror('0') - '0', 10 - MotionEvent.axisFromString(""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (56123 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 619, 12 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.dc.a.b(context);
        o.dc.c.e(context);
        new o.dl.c();
        o.dl.c.b().a();
        o.fn.i iVar = this.d;
        if (iVar != null) {
            iVar.c(context);
        }
        o.az.e eVar = new o.az.e();
        try {
            eVar.d(context);
            eVar.c(context);
        } catch (e.c e) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            z((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 59168), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1, TextUtils.indexOf((CharSequence) "", '0') + 12, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            z((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 1844), 632 - TextUtils.getCapsMode("", 0, 0), (-16777156) - Color.rgb(0, 0, 0), objArr4);
            o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e);
        }
        new o.ay.a(context);
        o.ay.a.c(context);
        o.en.d dVar = this.a;
        if (dVar != null) {
            int i = n + Opcodes.DMUL;
            f61o = i % 128;
            int i2 = i % 2;
            dVar.c();
            this.a.f().b();
            this.a.d();
            this.a.b();
        }
        o.fm.c cVar = this.g;
        if (cVar != null) {
            cVar.d();
        }
        o.em.e eVar2 = this.h;
        switch (eVar2 != null ? '!' : '5') {
            case Opcodes.SALOAD /* 53 */:
                break;
            default:
                eVar2.e();
                int i3 = f61o + 85;
                n = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
        o.fn.i iVar2 = this.d;
        if (iVar2 != null) {
            int i5 = f61o + Opcodes.LSHR;
            n = i5 % 128;
            int i6 = i5 % 2;
            iVar2.a(context);
        }
        o.en.d dVar2 = this.a;
        if (dVar2 != null) {
            dVar2.a(context);
        }
        o.fm.c cVar2 = this.g;
        if (cVar2 != null) {
            cVar2.b(context);
        }
        o.em.e eVar3 = this.h;
        Object obj = null;
        switch (eVar3 != null ? 'D' : 'F') {
            case 'D':
                int i7 = f61o + 29;
                n = i7 % 128;
                boolean z = i7 % 2 == 0;
                eVar3.e(context);
                switch (z) {
                    case false:
                        break;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
        o.bi.e.d(context).e(context);
        int i8 = n + 87;
        f61o = i8 % 128;
        switch (i8 % 2 != 0 ? 'T' : Typography.dollar) {
            case Opcodes.BASTORE /* 84 */:
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void e(Context context, String str) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (59169 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), ViewConfiguration.getLongPressTimeout() >> 16, 11 - TextUtils.indexOf("", "", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        z((char) KeyEvent.normalizeMetaState(0), TextUtils.lastIndexOf("", '0') + 693, (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 28, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        switch (!this.a.d(context, str).f()) {
            case true:
                break;
            default:
                int i = n + 59;
                f61o = i % 128;
                switch (i % 2 != 0) {
                    case true:
                        this.g.e().e(this.a.j());
                        throw null;
                    default:
                        this.g.e().e(this.a.j());
                        break;
                }
        }
        d(context);
        int i2 = f61o + Opcodes.LSHL;
        n = i2 % 128;
        int i3 = i2 % 2;
    }

    public final o.fk.b b(Context context, o.eo.e eVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 59170), ViewConfiguration.getFadingEdgeLength() >> 16, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        z((char) TextUtils.getOffsetAfter("", 0), 721 - ExpandableListView.getPackedPositionGroup(0L), AndroidCharacter.getMirror('0') - 19, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar.e()).toString());
        o.fk.b e = this.a.e(context, eVar);
        switch (e.f() ? '\r' : ':') {
            case '\r':
                int i = f61o + 35;
                n = i % 128;
                switch (i % 2 == 0 ? (char) 19 : (char) 26) {
                    case 19:
                        this.g.e().e(this.a.j());
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        this.g.e().e(this.a.j());
                        break;
                }
        }
        d(context);
        return e;
    }

    public final void c(Context context, String str) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (59168 - ImageFormat.getBitsPerPixel(0)), ImageFormat.getBitsPerPixel(0) + 1, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        z((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), 750 - TextUtils.indexOf("", "", 0, 0), 27 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        switch (this.a.b(context, str).f() ? false : true) {
            case false:
                int i = f61o + 57;
                n = i % 128;
                int i2 = i % 2;
                this.g.e().e(this.a.j());
                int i3 = f61o + 11;
                n = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
        d(context);
    }

    public final void d(Context context) {
        int i = f61o + 43;
        n = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (59170 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), ViewConfiguration.getKeyRepeatTimeout() >> 16, 10 - Process.getGidForName(""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) KeyEvent.normalizeMetaState(0), 776 - ExpandableListView.getPackedPositionChild(0L), 39 - ExpandableListView.getPackedPositionGroup(0L), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.d.a(context);
        this.a.a(context);
        switch (this.g.a() ? (char) 30 : '5') {
            case Opcodes.SALOAD /* 53 */:
                break;
            default:
                int i3 = n + 21;
                f61o = i3 % 128;
                if (i3 % 2 != 0) {
                }
                this.g.b(context);
                break;
        }
        this.h.e(context);
    }

    public final Context t() {
        int i = f61o + Opcodes.LUSHR;
        int i2 = i % 128;
        n = i2;
        int i3 = i % 2;
        Context context = this.j;
        int i4 = i2 + 19;
        f61o = i4 % 128;
        int i5 = i4 % 2;
        return context;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x003c, code lost:
    
        if (o.ee.c.e(t()) == false) goto L13;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.List<fr.antelop.sdk.Product> r() {
        /*
            r3 = this;
            int r0 = o.ei.c.f61o
            int r0 = r0 + 93
            int r1 = r0 % 128
            o.ei.c.n = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L52
            o.fn.i r0 = r3.d
            o.fn.j r0 = r0.e()
            java.util.Set r0 = r0.a()
            o.ei.a r1 = o.ei.a.d
            boolean r1 = r0.contains(r1)
            if (r1 == 0) goto L4d
            o.ee.e.a()
            android.content.Context r1 = r3.t()
            boolean r1 = o.ee.c.a(r1)
            if (r1 == 0) goto L2d
            r1 = 1
            goto L2e
        L2d:
            r1 = 0
        L2e:
            switch(r1) {
                case 0: goto L3e;
                default: goto L31;
            }
        L31:
            o.ee.e.a()
            android.content.Context r1 = r3.t()
            boolean r1 = o.ee.c.e(r1)
            if (r1 != 0) goto L4d
        L3e:
            o.ei.a r1 = o.ei.a.d
            r0.remove(r1)
            int r1 = o.ei.c.n
            int r1 = r1 + 109
            int r2 = r1 % 128
            o.ei.c.f61o = r2
            int r1 = r1 % 2
        L4d:
            java.util.List r0 = o.ee.o.d(r0)
            return r0
        L52:
            o.fn.i r0 = r3.d
            o.fn.j r0 = r0.e()
            java.util.Set r0 = r0.a()
            o.ei.a r1 = o.ei.a.d
            r0.contains(r1)
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L66
            throw r0     // Catch: java.lang.Throwable -> L66
        L66:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.r():java.util.List");
    }

    public final boolean q() {
        boolean z;
        int i = f61o + 99;
        n = i % 128;
        int i2 = i % 2;
        try {
            s();
            z = this.c;
            try {
                p();
            } catch (InterruptedException e) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                z((char) ((ViewConfiguration.getJumpTapTimeout() >> 16) + 59169), KeyEvent.getDeadChar(0, 0), 10 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                z((char) (ViewConfiguration.getPressedStateDuration() >> 16), 816 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 71 - Color.green(0), objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                int i3 = n + Opcodes.LUSHR;
                f61o = i3 % 128;
                int i4 = i3 % 2;
                return z;
            }
        } catch (InterruptedException e2) {
            z = false;
        }
        int i32 = n + Opcodes.LUSHR;
        f61o = i32 % 128;
        int i42 = i32 % 2;
        return z;
    }

    public static void s() throws InterruptedException {
        int i = n + 67;
        f61o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (59169 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), ViewConfiguration.getWindowTouchSlop() >> 8, TextUtils.getOffsetBefore("", 0) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) ((-16752332) - Color.rgb(0, 0, 0)), TextUtils.indexOf((CharSequence) "", '0') + 888, TextUtils.indexOf("", "") + 42, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        b.acquire();
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        z((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 59169), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 11, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        z((char) (23842 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 929 - (ViewConfiguration.getTouchSlop() >> 8), 43 - TextUtils.getOffsetAfter("", 0), objArr4);
        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
        int i3 = f61o + 91;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    public static void p() {
        int i = n + 99;
        f61o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) (TextUtils.getTrimmedLength("") + 59169), ExpandableListView.getPackedPositionType(0L), 12 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (KeyEvent.getDeadChar(0, 0) + 4400), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 973, (ViewConfiguration.getLongPressTimeout() >> 16) + 44, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        b.release();
        int i3 = n + 27;
        f61o = i3 % 128;
        switch (i3 % 2 != 0 ? ']' : (char) 3) {
            case Opcodes.DUP2_X1 /* 93 */:
                throw null;
            default:
                return;
        }
    }

    public final void c(boolean z) {
        int i = n + 43;
        f61o = i % 128;
        switch (i % 2 != 0 ? (char) 26 : '%') {
            case '%':
                this.c = z;
                o.b.c.b(z);
                return;
            default:
                this.c = z;
                o.b.c.b(z);
                int i2 = 15 / 0;
                return;
        }
    }

    public static void c(Context context, boolean z) {
        new o.cd.e();
        e.c a = o.cd.e.a(context);
        switch (z ? '!' : '`') {
            case '!':
                int i = n + Opcodes.LSHL;
                f61o = i % 128;
                int i2 = i % 2;
                a.d();
                int i3 = n + 17;
                f61o = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return;
                    default:
                        int i4 = 43 / 0;
                        return;
                }
            default:
                a.c();
                return;
        }
    }

    public final void b(Context context, CreateCardRequestBuilder createCardRequestBuilder, final AntelopCallback antelopCallback) throws WalletValidationException {
        int i = n + 43;
        f61o = i % 128;
        int i2 = i % 2;
        if (createCardRequestBuilder == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            z((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 36824), 1015 - ImageFormat.getBitsPerPixel(0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 24, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        z((char) (59170 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), View.MeasureSpec.getSize(0), 11 - Color.argb(0, 0, 0, 0), objArr2);
        String intern = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        z((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), 1040 - (ViewConfiguration.getWindowTouchSlop() >> 8), (-16777206) - Color.rgb(0, 0, 0), objArr3);
        o.ee.g.d(intern, ((String) objArr3[0]).intern());
        o.ac.b build = createCardRequestBuilder.build();
        build.x();
        this.i.b(context, build, new o.ca.b() { // from class: o.ei.c.4
            private static int b = 0;
            private static int c = 1;

            /* JADX WARN: Failed to find 'out' block for switch in B:12:0x0038. Please report as an issue. */
            @Override // o.ca.b
            public final void d() {
                int i3 = b + 9;
                c = i3 % 128;
                switch (i3 % 2 == 0 ? ' ' : (char) 3) {
                    case 3:
                        AntelopCallback antelopCallback2 = antelopCallback;
                        switch (antelopCallback2 != null ? (char) 0 : '\f') {
                            case '\f':
                                break;
                            default:
                                antelopCallback2.onSuccess();
                                int i4 = c;
                                int i5 = (i4 ^ Opcodes.LSHR) + ((i4 & Opcodes.LSHR) << 1);
                                b = i5 % 128;
                                switch (i5 % 2 != 0 ? '5' : 'S') {
                                }
                        }
                        int i6 = b;
                        int i7 = (i6 ^ 41) + ((i6 & 41) << 1);
                        c = i7 % 128;
                        int i8 = i7 % 2;
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:13:0x0052. Please report as an issue. */
            @Override // o.ca.b
            public final void d(o.bb.d dVar) {
                int i3 = c;
                int i4 = (i3 ^ 77) + ((i3 & 77) << 1);
                b = i4 % 128;
                switch (i4 % 2 != 0 ? 'c' : '*') {
                    case Opcodes.DADD /* 99 */:
                        int i5 = 47 / 0;
                        switch (antelopCallback != null) {
                            case true:
                                break;
                            default:
                                return;
                        }
                    default:
                        switch (antelopCallback != null ? Typography.less : 'E') {
                            case 'E':
                                return;
                        }
                }
                antelopCallback.onError(o.bv.c.c(dVar).d());
                int i6 = b + 27;
                c = i6 % 128;
                switch (i6 % 2 == 0) {
                }
            }
        }, a.d);
        int i3 = n + 71;
        f61o = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                int i4 = 36 / 0;
                return;
            default:
                return;
        }
    }

    public final void c(Context context, String str, final AntelopCallback antelopCallback) throws WalletValidationException {
        int i = n + 33;
        f61o = i % 128;
        int i2 = i % 2;
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            z((char) TextUtils.getCapsMode("", 0, 0), 1049 - ExpandableListView.getPackedPositionChild(0L), TextUtils.getCapsMode("", 0, 0) + 25, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        z((char) (59168 - ImageFormat.getBitsPerPixel(0)), (-1) - ((byte) KeyEvent.getModifierMetaStateMask()), Color.blue(0) + 11, objArr2);
        String intern = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        z((char) (62323 - TextUtils.getCapsMode("", 0, 0)), View.MeasureSpec.makeMeasureSpec(0, 0) + 1075, (ViewConfiguration.getEdgeSlop() >> 16) + 17, objArr3);
        o.ee.g.d(intern, ((String) objArr3[0]).intern());
        o.ac.c cVar = new o.ac.c(str);
        cVar.a();
        this.i.b(context, cVar, new o.ca.b() { // from class: o.ei.c.5
            private static int a = 0;
            private static int d = 1;

            /* JADX WARN: Failed to find 'out' block for switch in B:13:0x0039. Please report as an issue. */
            @Override // o.ca.b
            public final void d() {
                int i3 = d + 1;
                a = i3 % 128;
                switch (i3 % 2 != 0 ? '_' : ';') {
                    case Opcodes.SWAP /* 95 */:
                        throw null;
                    default:
                        AntelopCallback antelopCallback2 = antelopCallback;
                        switch (antelopCallback2 != null) {
                            case true:
                                antelopCallback2.onSuccess();
                                int i4 = a + 43;
                                d = i4 % 128;
                                switch (i4 % 2 == 0 ? (char) 5 : '@') {
                                }
                        }
                        int i5 = a;
                        int i6 = ((i5 | 5) << 1) - (i5 ^ 5);
                        d = i6 % 128;
                        switch (i6 % 2 == 0 ? '6' : 'U') {
                            case Opcodes.ISTORE /* 54 */:
                                int i7 = 57 / 0;
                                return;
                            default:
                                return;
                        }
                }
            }

            @Override // o.ca.b
            public final void d(o.bb.d dVar) {
                int i3 = a;
                int i4 = (i3 & 47) + (i3 | 47);
                d = i4 % 128;
                Object obj = null;
                switch (i4 % 2 == 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        AntelopCallback antelopCallback2 = antelopCallback;
                        switch (antelopCallback2 != null ? 'P' : '+') {
                            case '+':
                                break;
                            default:
                                antelopCallback2.onError(o.bv.c.c(dVar).d());
                                break;
                        }
                        int i5 = d;
                        int i6 = (i5 ^ 39) + ((i5 & 39) << 1);
                        a = i6 % 128;
                        switch (i6 % 2 != 0 ? 'V' : '=') {
                            case Opcodes.SASTORE /* 86 */:
                                obj.hashCode();
                                throw null;
                            default:
                                return;
                        }
                }
            }
        }, a.b);
        int i3 = f61o + 5;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    public final String u() {
        int i = n + Opcodes.LNEG;
        f61o = i % 128;
        switch (i % 2 == 0) {
            case false:
                o();
                throw null;
            default:
                return o();
        }
    }

    public final String w() {
        String encodeToString;
        int i;
        int i2 = f61o + 49;
        n = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                o.ee.e.a();
                encodeToString = Base64.encodeToString(o.ec.e.a(o.ee.c.j(this.j).getBytes(StandardCharsets.UTF_8)), 84);
                i = 3;
                break;
            default:
                o.ee.e.a();
                encodeToString = Base64.encodeToString(o.ec.e.a(o.ee.c.j(this.j).getBytes(StandardCharsets.UTF_8)), 10);
                i = 24;
                break;
        }
        return encodeToString.substring(0, i);
    }

    public final String y() {
        try {
            String substring = Base64.encodeToString(o.ec.e.a(new StringBuilder().append(w()).append(o.bi.e.d(this.j).e()).toString().getBytes(StandardCharsets.UTF_8)), 10).substring(0, 24);
            int i = f61o + 55;
            n = i % 128;
            int i2 = i % 2;
            return substring;
        } catch (o.bi.c e) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            z((char) (KeyEvent.getDeadChar(0, 0) + 59169), ViewConfiguration.getScrollBarFadeDuration() >> 16, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            z((char) (View.MeasureSpec.getMode(0) + 53681), 1092 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), 47 - View.MeasureSpec.getSize(0), objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
            return "";
        }
    }

    public final X509Certificate x() {
        int i = n + Opcodes.DREM;
        f61o = i % 128;
        int i2 = i % 2;
        X509Certificate m2 = e().m();
        int i3 = f61o + Opcodes.DDIV;
        n = i3 % 128;
        int i4 = i3 % 2;
        return m2;
    }

    public final X509Certificate v() {
        int i = n + 79;
        f61o = i % 128;
        switch (i % 2 == 0) {
            case false:
                e().l();
                throw null;
            default:
                return e().l();
        }
    }

    public final void e(Context context, final OperationCallback<Void> operationCallback) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        z((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 59169), TextUtils.indexOf((CharSequence) "", '0') + 1, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 11, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        z((char) (43971 - Color.argb(0, 0, 0, 0)), View.MeasureSpec.getSize(0) + 1139, 19 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.ax.d(context, new d.b() { // from class: o.ei.c.3
            private static int a = 0;
            private static int d = 1;

            /* JADX WARN: Failed to find 'out' block for switch in B:22:0x0059. Please report as an issue. */
            @Override // o.ax.d.b
            public final void e(o.bb.d dVar) {
                int i = a;
                int i2 = ((i | 49) << 1) - (i ^ 49);
                d = i2 % 128;
                char c = i2 % 2 == 0 ? (char) 29 : 'E';
                Object obj = null;
                o.bb.c a2 = dVar.a();
                switch (c) {
                    case 'E':
                        switch (a2.b() ? (char) 19 : '`') {
                            case 19:
                                int i3 = d;
                                int i4 = (i3 ^ 13) + ((i3 & 13) << 1);
                                a = i4 % 128;
                                int i5 = i4 % 2;
                                c.c();
                                c.p();
                                int i6 = d;
                                int i7 = (i6 & 89) + (i6 | 89);
                                a = i7 % 128;
                                switch (i7 % 2 != 0 ? (char) 7 : '-') {
                                }
                        }
                        operationCallback.onSuccess(null);
                        int i8 = d;
                        int i9 = (i8 & 7) + (i8 | 7);
                        a = i9 % 128;
                        switch (i9 % 2 != 0) {
                            case true:
                                throw null;
                            default:
                                return;
                        }
                    default:
                        a2.b();
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.ax.d.b
            public final void b(o.bb.d dVar) {
                int i = (a + 60) - 1;
                d = i % 128;
                switch (i % 2 == 0) {
                    case false:
                        switch (dVar.a().b() ? '2' : 'c') {
                        }
                        operationCallback.onError(o.bv.c.c(dVar).d());
                        int i2 = a + 49;
                        d = i2 % 128;
                        int i3 = i2 % 2;
                    default:
                        int i4 = 65 / 0;
                        switch (dVar.a().b()) {
                        }
                        operationCallback.onError(o.bv.c.c(dVar).d());
                        int i22 = a + 49;
                        d = i22 % 128;
                        int i32 = i22 % 2;
                }
                int i5 = (a + 58) - 1;
                d = i5 % 128;
                int i6 = i5 % 2;
                c.c();
                c.p();
                operationCallback.onError(o.bv.c.c(dVar).d());
                int i222 = a + 49;
                d = i222 % 128;
                int i322 = i222 % 2;
            }
        }, this).k();
        int i = f61o + 5;
        n = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0025, code lost:
    
        if (q() != false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0028, code lost:
    
        r0 = o.ei.c.f61o + com.esotericsoftware.asm.Opcodes.LSUB;
        o.ei.c.n = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0033, code lost:
    
        if ((r0 % 2) != 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0036, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0037, code lost:
    
        switch(r1) {
            case 0: goto L22;
            default: goto L23;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x003b, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x003e, code lost:
    
        r0 = 15 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x003f, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x001f, code lost:
    
        if (q() != false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void C() throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r10 = this;
            int r0 = o.ei.c.n
            int r0 = r0 + 13
            int r1 = r0 % 128
            o.ei.c.f61o = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 26
            goto L11
        Lf:
            r0 = 40
        L11:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 40: goto L1b;
                default: goto L16;
            }
        L16:
            boolean r0 = r10.q()
            goto L22
        L1b:
            boolean r0 = r10.q()
            if (r0 == 0) goto L42
        L21:
            goto L28
        L22:
            r3 = 92
            int r3 = r3 / r2
            if (r0 == 0) goto L42
            goto L21
        L28:
            int r0 = o.ei.c.f61o
            int r0 = r0 + 101
            int r3 = r0 % 128
            o.ei.c.n = r3
            int r0 = r0 % 2
            if (r0 != 0) goto L36
            goto L37
        L36:
            r1 = r2
        L37:
            switch(r1) {
                case 0: goto L3b;
                default: goto L3a;
            }
        L3a:
            goto L3c
        L3b:
            return
        L3c:
            r0 = 15
            int r0 = r0 / r2
            return
        L40:
            r0 = move-exception
            throw r0
        L42:
            fr.antelop.sdk.exception.WalletValidationException r0 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r3 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            int r4 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r4 = r4 >> 16
            int r4 = 9331 - r4
            char r4 = (char) r4
            int r5 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r5 = (byte) r5
            int r5 = r5 + 228
            int r6 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r6 = r6 >> 16
            int r6 = 6 - r6
            java.lang.Object[] r7 = new java.lang.Object[r1]
            z(r4, r5, r6, r7)
            r4 = r7[r2]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            double r5 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r2)
            r7 = 0
            int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            r6 = 64409(0xfb99, float:9.0256E-41)
            int r6 = r6 - r5
            char r5 = (char) r6
            long r6 = android.os.Process.getElapsedCpuTime()
            r8 = 0
            int r6 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            int r6 = 234 - r6
            int r7 = android.view.ViewConfiguration.getKeyRepeatTimeout()
            int r7 = r7 >> 16
            int r7 = r7 + 42
            java.lang.Object[] r1 = new java.lang.Object[r1]
            z(r5, r6, r7, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r0.<init>(r3, r4, r1)
            throw r0
        L9b:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.C():void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void z(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.c.z(char, int, int, java.lang.Object[]):void");
    }
}
