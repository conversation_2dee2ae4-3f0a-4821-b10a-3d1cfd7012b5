package com.vasco.digipass.sdk.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.StandardCharsets;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\c.smali */
public final class c {
    public static byte a(byte b) {
        int i;
        if (b < 48 || b > 57) {
            byte b2 = 97;
            if (b < 97 || b > 102) {
                b2 = 65;
                if (b < 65 || b > 70) {
                    return (byte) -1;
                }
            }
            i = (b - b2) + 10;
        } else {
            i = b - 48;
        }
        return (byte) i;
    }

    public static String a(byte[] bArr) {
        if (bArr.length % 4 != 0) {
            throw new IllegalArgumentException(a());
        }
        String a = q.a(bArr);
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < a.length()) {
            int i2 = i + 8;
            String l = Long.toString(Long.parseLong(a.substring(i, i2), 16));
            for (int i3 = 0; i3 < 10 - l.length(); i3++) {
                sb.append('0');
            }
            sb.append(l);
            i = i2;
        }
        return sb.toString();
    }

    public static long b(byte[] bArr) {
        if (bArr == null) {
            throw new IllegalArgumentException(b());
        }
        if (bArr.length == 4) {
            return ((bArr[0] & 255) << 24) + ((bArr[1] & 255) << 16) + ((bArr[2] & 255) << 8) + (bArr[3] & 255);
        }
        throw new IllegalArgumentException(c());
    }

    static String c() {
        byte[] bArr = new byte[26];
        int[] iArr = {94, Opcodes.LCMP, Opcodes.D2I, Opcodes.IAND, Opcodes.F2I, 55, Opcodes.DNEG, Opcodes.I2D, Opcodes.I2F, Opcodes.INEG, Opcodes.F2I, 49, Opcodes.LXOR, Opcodes.ISHL, Opcodes.L2I, 114, 44, Opcodes.ISHL, 127, Opcodes.IUSHR, Opcodes.IUSHR, 39, 104, Opcodes.FMUL, 36, 55};
        for (int i = 0; i < 26; i++) {
            bArr[i] = (byte) (((iArr[i] + i) + 78) - 106);
        }
        return new String(bArr);
    }

    public static int b(String str) {
        return Integer.parseInt(str, 16);
    }

    public static String b(long j) {
        char[] cArr = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
        int i = 64;
        char[] cArr2 = new char[64];
        long j2 = 15;
        do {
            i--;
            cArr2[i] = cArr[(int) (j & j2)];
            j >>>= 4;
        } while (j != 0);
        return new String(cArr2, i, 64 - i);
    }

    static String b() {
        byte[] bArr = new byte[26];
        int[] iArr = {207, 6, 1, 242, 0, Opcodes.LRETURN, 238, 255, 255, 238, 6, Opcodes.LRETURN, 240, 238, 251, 251, 252, 1, Opcodes.LRETURN, 239, 242, Opcodes.LRETURN, 251, 2, 249, 249};
        for (int i = 0; i < 26; i++) {
            bArr[i] = (byte) (iArr[i] + Opcodes.DREM);
        }
        return new String(bArr);
    }

    public static String a(String str) {
        StringBuilder sb = new StringBuilder();
        int i = 0;
        while (i < str.length() / 10) {
            i++;
            int length = str.length() - (10 * i);
            String b = b(Long.parseLong(str.substring(length, length + 10)));
            int i2 = 0;
            while (i2 < 8 - b.length()) {
                sb.insert(0, '0');
                i2++;
            }
            sb.insert(i2, b);
        }
        int length2 = str.length() % 10;
        if (length2 != 0) {
            sb.insert(0, b(Long.parseLong(str.substring(0, length2))));
        }
        return sb.toString();
    }

    public static byte[] a(long j) {
        return new byte[]{(byte) (((byte) (j >> 24)) & 255), (byte) (((byte) (j >> 16)) & 255), (byte) (((byte) (j >> 8)) & 255), (byte) (((byte) j) & 255)};
    }

    static String a() {
        byte[] bArr = new byte[36];
        int[] iArr = {66, Opcodes.LSHL, Opcodes.INEG, Opcodes.LSUB, Opcodes.DREM, 32, 97, 114, 114, 97, Opcodes.LSHL, 32, 109, Opcodes.LNEG, Opcodes.DREM, Opcodes.INEG, 32, 98, Opcodes.LSUB, 32, 52, 32, 98, Opcodes.LSHL, Opcodes.INEG, Opcodes.LSUB, Opcodes.DREM, 32, 109, Opcodes.LNEG, 108, Opcodes.INEG, Opcodes.LMUL, Opcodes.IREM, 108, Opcodes.LSUB};
        for (int i = 0; i < 36; i++) {
            bArr[i] = (byte) ((iArr[i] ^ i) ^ i);
        }
        return new String(bArr);
    }

    public static byte[] a(CharSequence charSequence) {
        ByteBuffer encode = StandardCharsets.UTF_8.encode(CharBuffer.wrap(charSequence));
        byte[] bArr = new byte[encode.remaining()];
        encode.get(bArr);
        return bArr;
    }
}
