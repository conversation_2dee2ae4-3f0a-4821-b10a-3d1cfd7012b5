package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.ReflectedParcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\ServerPushProvisionRequest.smali */
public final class ServerPushProvisionRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<ServerPushProvisionRequest> CREATOR = new zzj();
    private final PushProvisionSessionContext zza;
    private final String zzb;
    private final UserAddress zzc;
    private final ExtraOptions zzd;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\ServerPushProvisionRequest$Builder.smali */
    public static class Builder {
        private PushProvisionSessionContext zza;
        private String zzb;
        private UserAddress zzc;
        private ExtraOptions zzd;

        public ServerPushProvisionRequest build() {
            return new ServerPushProvisionRequest(this.zza, this.zzb, this.zzc, this.zzd);
        }

        public Builder setDisplayName(String str) {
            this.zzb = str;
            return this;
        }

        public Builder setExtraOptions(ExtraOptions extraOptions) {
            this.zzd = extraOptions;
            return this;
        }

        public Builder setSessionContext(PushProvisionSessionContext pushProvisionSessionContext) {
            this.zza = pushProvisionSessionContext;
            return this;
        }

        public Builder setUserAddress(UserAddress userAddress) {
            this.zzc = userAddress;
            return this;
        }
    }

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\ServerPushProvisionRequest$ExtraOptions.smali */
    public static class ExtraOptions extends AbstractSafeParcelable implements ReflectedParcelable {
        public static final Parcelable.Creator<ExtraOptions> CREATOR = new zzb();
        private boolean zza;
        private boolean zzb;

        public ExtraOptions(boolean serverSideAddressDeliveryEnabled, boolean virtualCardsSetting) {
            this.zza = serverSideAddressDeliveryEnabled;
            this.zzb = virtualCardsSetting;
        }

        public static ExtraOptions defaultOptions() {
            return new ExtraOptions(false, false);
        }

        public boolean getServerSideAddressDeliveryEnabled() {
            return this.zza;
        }

        public boolean getVirtualCardsSetting() {
            return this.zzb;
        }

        public ExtraOptions setServerSideAddressDeliveryEnabled(boolean z) {
            this.zza = z;
            return this;
        }

        public ExtraOptions setVirtualCardsSetting(boolean z) {
            this.zzb = z;
            return this;
        }

        @Override // android.os.Parcelable
        public void writeToParcel(Parcel dest, int i) {
            int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
            SafeParcelWriter.writeBoolean(dest, 1, getServerSideAddressDeliveryEnabled());
            SafeParcelWriter.writeBoolean(dest, 2, getVirtualCardsSetting());
            SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
        }
    }

    ServerPushProvisionRequest(PushProvisionSessionContext pushProvisionSessionContext, String str, UserAddress userAddress, ExtraOptions extraOptions) {
        this.zza = pushProvisionSessionContext;
        this.zzb = str;
        this.zzc = userAddress;
        this.zzd = extraOptions;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeParcelable(dest, 1, this.zza, flags, false);
        SafeParcelWriter.writeString(dest, 2, this.zzb, false);
        SafeParcelWriter.writeParcelable(dest, 3, this.zzc, flags, false);
        SafeParcelWriter.writeParcelable(dest, 4, this.zzd, flags, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
