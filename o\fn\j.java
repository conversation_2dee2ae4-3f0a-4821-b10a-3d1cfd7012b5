package o.fn;

import android.content.ComponentName;
import android.content.Context;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\j.smali */
public final class j {
    private static char c;
    private static char e;
    private static char f;
    private static int g;
    private static char i;
    private boolean d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int j = 0;
    private final Set<o.ei.a> b = new HashSet();
    private List<o.ei.e> a = new ArrayList();

    static {
        g = 1;
        c();
        AudioTrack.getMaxVolume();
        ViewConfiguration.getDoubleTapTimeout();
        SystemClock.elapsedRealtimeNanos();
        int i2 = j + 37;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        c = (char) 48742;
        i = (char) 31297;
        f = (char) 23019;
        e = (char) 29439;
    }

    j() {
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    final boolean d(android.content.Context r11, o.eg.b r12) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 312
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.j.d(android.content.Context, o.eg.b):boolean");
    }

    final boolean a(Context context, o.ei.e[] eVarArr) {
        int i2 = j + 81;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? Typography.quote : (char) 15) {
            case '\"':
                this.d = true;
                break;
            default:
                this.d = false;
                break;
        }
        this.a = Arrays.asList(eVarArr);
        boolean b = b(context);
        int i3 = g + 109;
        j = i3 % 128;
        switch (i3 % 2 != 0 ? '#' : (char) 31) {
            case 31:
                return b;
            default:
                int i4 = 74 / 0;
                return b;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x003f. Please report as an issue. */
    private boolean b(Context context) {
        HashSet hashSet = new HashSet(this.b);
        a(context, o.ei.a.d, this.a);
        a(context, o.ei.a.a, this.a);
        a(context, o.ei.a.b, this.a);
        if (hashSet.size() == 0 || hashSet.equals(this.b)) {
            int i2 = j + Opcodes.DSUB;
            g = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return false;
            }
        }
        int i3 = g + 67;
        int i4 = i3 % 128;
        j = i4;
        switch (i3 % 2 != 0 ? '1' : 'a') {
        }
        int i5 = i4 + 49;
        g = i5 % 128;
        if (i5 % 2 != 0) {
            return true;
        }
        int i6 = 81 / 0;
        return true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x00d4, code lost:
    
        switch(r1) {
            case 1: goto L24;
            default: goto L23;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x00d7, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x00d8, code lost:
    
        r8 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x00d9, code lost:
    
        r8.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00dc, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0027, code lost:
    
        o.ee.g.c();
        r0 = new java.lang.Object[1];
        h("硹たꢣ럊埮曝㝬忳耋ሠ\ue8f3ﲢⳛ볞䋐㞛\ue8f3ﲢ蛍끻", android.text.AndroidCharacter.getMirror('0') - 29, r0);
        r10 = ((java.lang.String) r0[0]).intern();
        r0 = new java.lang.StringBuilder().append(r9.name());
        r1 = new java.lang.Object[1];
        h("Ẋ嶰䋐㞛㲼璃ꣲ身", 9 - (android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)), r1);
        o.ee.g.d(r10, r0.append(((java.lang.String) r1[0]).intern()).toString());
        r7.b.add(r9);
        e(r8, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0076, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0022, code lost:
    
        if (r10 != false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x001c, code lost:
    
        if (r10 != false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0077, code lost:
    
        o.ee.g.c();
        r0 = new java.lang.Object[1];
        h("硹たꢣ럊埮曝㝬忳耋ሠ\ue8f3ﲢⳛ볞䋐㞛\ue8f3ﲢ蛍끻", (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16) + 19, r0);
        r10 = ((java.lang.String) r0[0]).intern();
        r0 = new java.lang.StringBuilder().append(r9.name());
        r4 = new java.lang.Object[1];
        h("븝퍞洄깑䏲ힶ솧娋쑪ᬅ", android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0') + 10, r4);
        o.ee.g.d(r10, r0.append(((java.lang.String) r4[0]).intern()).toString());
        r7.b.remove(r9);
        c(r8, r9);
        r8 = o.fn.j.j + com.esotericsoftware.asm.Opcodes.LSHR;
        o.fn.j.g = r8 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x00d0, code lost:
    
        if ((r8 % 2) != 0) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x00d3, code lost:
    
        r1 = false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void a(android.content.Context r8, o.ei.a r9, java.util.List<o.ei.e> r10) {
        /*
            r7 = this;
            int r0 = o.fn.j.j
            int r0 = r0 + 3
            int r1 = r0 % 128
            o.fn.j.g = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            r3 = 48
            java.lang.String r4 = "硹たꢣ럊埮曝㝬忳耋ሠ\ue8f3ﲢⳛ볞䋐㞛\ue8f3ﲢ蛍끻"
            boolean r10 = r9.c(r10)
            switch(r0) {
                case 1: goto L1f;
                default: goto L1c;
            }
        L1c:
            if (r10 == 0) goto L77
        L1e:
            goto L27
        L1f:
            r0 = 12
            int r0 = r0 / r2
            if (r10 == 0) goto L77
            goto L1e
        L25:
            r8 = move-exception
            throw r8
        L27:
            o.ee.g.c()
            char r10 = android.text.AndroidCharacter.getMirror(r3)
            int r10 = r10 + (-29)
            java.lang.Object[] r0 = new java.lang.Object[r1]
            h(r4, r10, r0)
            r10 = r0[r2]
            java.lang.String r10 = (java.lang.String) r10
            java.lang.String r10 = r10.intern()
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r3 = r9.name()
            java.lang.StringBuilder r0 = r0.append(r3)
            long r3 = android.view.ViewConfiguration.getGlobalActionKeyTimeout()
            r5 = 0
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            int r3 = 9 - r3
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r4 = "Ẋ嶰䋐㞛㲼璃ꣲ身"
            h(r4, r3, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r0 = r0.append(r1)
            java.lang.String r0 = r0.toString()
            o.ee.g.d(r10, r0)
            java.util.Set<o.ei.a> r10 = r7.b
            r10.add(r9)
            e(r8, r9)
            return
        L77:
            o.ee.g.c()
            int r10 = android.view.ViewConfiguration.getKeyRepeatDelay()
            int r10 = r10 >> 16
            int r10 = r10 + 19
            java.lang.Object[] r0 = new java.lang.Object[r1]
            h(r4, r10, r0)
            r10 = r0[r2]
            java.lang.String r10 = (java.lang.String) r10
            java.lang.String r10 = r10.intern()
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.lang.String r4 = r9.name()
            java.lang.StringBuilder r0 = r0.append(r4)
            java.lang.String r4 = ""
            int r3 = android.text.TextUtils.indexOf(r4, r3)
            int r3 = r3 + 10
            java.lang.Object[] r4 = new java.lang.Object[r1]
            java.lang.String r5 = "븝퍞洄깑䏲ힶ솧娋쑪ᬅ"
            h(r5, r3, r4)
            r3 = r4[r2]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r0 = r0.append(r3)
            java.lang.String r0 = r0.toString()
            o.ee.g.d(r10, r0)
            java.util.Set<o.ei.a> r10 = r7.b
            r10.remove(r9)
            c(r8, r9)
            int r8 = o.fn.j.j
            int r8 = r8 + 123
            int r9 = r8 % 128
            o.fn.j.g = r9
            int r8 = r8 % 2
            if (r8 != 0) goto Ld3
            goto Ld4
        Ld3:
            r1 = r2
        Ld4:
            switch(r1) {
                case 1: goto Ld8;
                default: goto Ld7;
            }
        Ld7:
            return
        Ld8:
            r8 = 0
            r8.hashCode()     // Catch: java.lang.Throwable -> Ldd
            throw r8     // Catch: java.lang.Throwable -> Ldd
        Ldd:
            r8 = move-exception
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.j.a(android.content.Context, o.ei.a, java.util.List):void");
    }

    private static void e(Context context, o.ei.a aVar) {
        int i2 = j + 25;
        g = i2 % 128;
        int i3 = i2 % 2;
        Class<?> c2 = aVar.c();
        switch (c2 == null ? 'L' : (char) 11) {
            case Base64.mimeLineLength /* 76 */:
                int i4 = g + 79;
                j = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Object[] objArr = new Object[1];
                h("硹たꢣ럊埮曝㝬忳耋ሠ\ue8f3ﲢⳛ볞䋐㞛\ue8f3ﲢ蛍끻", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 19, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                h("\ufaf6プ䏲ힶ솧娋曃嫴㓘宆\ufefe῾\uf253턩ﾛ᠈硹たꢣ럊埮曝숯暷˔餴ུ刓ꢣ럊埮曝숯暷῎厖嵹䛨薳\uf46e듏檋ﾙⵉ솦꣢㓘宆\ufefe῾덿眊̖ષẊ嶰䋐㞛㲼璃٥▧", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 62, objArr2);
                g.d(intern, String.format(((String) objArr2[0]).intern(), aVar));
                break;
            default:
                context.getPackageManager().setComponentEnabledSetting(new ComponentName(context, c2), 1, 1);
                break;
        }
    }

    private static void c(Context context, o.ei.a aVar) {
        int i2 = j + 51;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                aVar.c();
                throw null;
            default:
                Class<?> c2 = aVar.c();
                switch (c2 == null) {
                    case true:
                        g.c();
                        Object[] objArr = new Object[1];
                        h("硹たꢣ럊埮曝㝬忳耋ሠ\ue8f3ﲢⳛ볞䋐㞛\ue8f3ﲢ蛍끻", 19 - View.combineMeasuredStates(0, 0), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        h("ꀛ窊耋ሠ㲼璃賊뱇됋朥ㅀ⦍\ud8a9룧\ueb70ἀ嵱▿붐⟅⮎\ue304舼륉ᨮ苊쟽ﶄ붐⟅⮎\ue304舼륉佛ܡ㎟⮗澔㣣㎟⮗㞄瘈序攑됋朥ㅀ⦍\ud8a9룧␜葂ﾙⵉꀛ窊耋ሠ㲼璃٥▧", 63 - ExpandableListView.getPackedPositionType(0L), objArr2);
                        g.d(intern, String.format(((String) objArr2[0]).intern(), aVar));
                        int i3 = g + 89;
                        j = i3 % 128;
                        int i4 = i3 % 2;
                        return;
                    default:
                        context.getPackageManager().setComponentEnabledSetting(new ComponentName(context, c2), 2, 1);
                        return;
                }
        }
    }

    public final boolean b(o.ei.a aVar) {
        int i2 = g + 97;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 31 : (char) 7) {
            case 7:
                return this.b.contains(aVar);
            default:
                this.b.contains(aVar);
                throw null;
        }
    }

    public final Set<o.ei.a> a() {
        int i2 = g + Opcodes.LSHL;
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        Set<o.ei.a> set = this.b;
        int i5 = i3 + 71;
        g = i5 % 128;
        int i6 = i5 % 2;
        return set;
    }

    public final boolean b() {
        int i2 = g + 19;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 30 : 'U') {
            case 30:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.d;
        }
    }

    /* renamed from: o.fn.j$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\j$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            d = 0;
            a = 1;
            int[] iArr = new int[o.ei.a.values().length];
            e = iArr;
            try {
                iArr[o.ei.a.a.ordinal()] = 1;
                int i = a;
                int i2 = (i & 13) + (i | 13);
                d = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.ei.a.d.ordinal()] = 2;
                int i4 = d;
                int i5 = (i4 & 45) + (i4 | 45);
                a = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[o.ei.a.b.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void e(android.content.Context r4) {
        /*
            r3 = this;
            int r0 = o.fn.j.j
            int r0 = r0 + 97
            int r1 = r0 % 128
            o.fn.j.g = r1
            int r0 = r0 % 2
            java.util.Set<o.ei.a> r0 = r3.b
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.fn.j.j
            int r1 = r1 + 31
            int r2 = r1 % 128
            o.fn.j.g = r2
            int r1 = r1 % 2
        L1a:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L24
            r1 = 95
            goto L26
        L24:
            r1 = 48
        L26:
            switch(r1) {
                case 48: goto L3b;
                default: goto L29;
            }
        L29:
            java.lang.Object r1 = r0.next()
            o.ei.a r1 = (o.ei.a) r1
            int[] r2 = o.fn.j.AnonymousClass2.e
            int r1 = r1.ordinal()
            r1 = r2[r1]
            switch(r1) {
                case 1: goto L3c;
                default: goto L3a;
            }
        L3a:
            goto L1a
        L3b:
            return
        L3c:
            o.dc.a.d(r4)
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.j.e(android.content.Context):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.j.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
