package androidx.work.impl.utils;

import java.util.concurrent.Callable;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\IdGenerator$$ExternalSyntheticLambda1.smali */
public final /* synthetic */ class IdGenerator$$ExternalSyntheticLambda1 implements Callable {
    public final /* synthetic */ IdGenerator f$0;
    public final /* synthetic */ int f$1;
    public final /* synthetic */ int f$2;

    public /* synthetic */ IdGenerator$$ExternalSyntheticLambda1(IdGenerator idGenerator, int i, int i2) {
        this.f$0 = idGenerator;
        this.f$1 = i;
        this.f$2 = i2;
    }

    @Override // java.util.concurrent.Callable
    public final Object call() {
        return IdGenerator.$r8$lambda$LyUC9fmKDw6AhARQq6V7VCdkafU(this.f$0, this.f$1, this.f$2);
    }
}
