package com.esotericsoftware.kryo.io;

import com.esotericsoftware.asm.Opcodes;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.util.Util;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\ByteBufferOutput.smali */
public class ByteBufferOutput extends Output {
    private static final ByteOrder nativeOrder = ByteOrder.nativeOrder();
    protected ByteBuffer byteBuffer;

    public ByteBufferOutput() {
    }

    public ByteBufferOutput(int bufferSize) {
        this(bufferSize, bufferSize);
    }

    public ByteBufferOutput(int bufferSize, int maxBufferSize) {
        if (maxBufferSize < -1) {
            throw new IllegalArgumentException("maxBufferSize cannot be < -1: " + maxBufferSize);
        }
        this.capacity = bufferSize;
        this.maxCapacity = maxBufferSize == -1 ? Util.maxArraySize : maxBufferSize;
        this.byteBuffer = ByteBuffer.allocateDirect(bufferSize);
    }

    public ByteBufferOutput(ByteBuffer buffer) {
        setBuffer(buffer);
    }

    public ByteBufferOutput(ByteBuffer buffer, int maxBufferSize) {
        setBuffer(buffer, maxBufferSize);
    }

    public ByteBufferOutput(OutputStream outputStream) {
        this(4096, 4096);
        if (outputStream == null) {
            throw new IllegalArgumentException("outputStream cannot be null.");
        }
        this.outputStream = outputStream;
    }

    public ByteBufferOutput(OutputStream outputStream, int bufferSize) {
        this(bufferSize, bufferSize);
        if (outputStream == null) {
            throw new IllegalArgumentException("outputStream cannot be null.");
        }
        this.outputStream = outputStream;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public OutputStream getOutputStream() {
        return this.outputStream;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public byte[] getBuffer() {
        throw new UnsupportedOperationException("This buffer does not used a byte[], see #getByteBuffer().");
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void setBuffer(byte[] buffer) {
        throw new UnsupportedOperationException("This buffer does not used a byte[], see #setByteBuffer(ByteBuffer).");
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void setBuffer(byte[] buffer, int maxBufferSize) {
        throw new UnsupportedOperationException("This buffer does not used a byte[], see #setByteBuffer(ByteBuffer).");
    }

    public void setBuffer(byte[] bytes, int offset, int count) {
        ByteBuffer buffer = ByteBuffer.allocateDirect(bytes.length);
        buffer.put(bytes, offset, count);
        setBufferPosition(buffer, 0);
        setBufferLimit(buffer, bytes.length);
        setBuffer(buffer);
    }

    public void setBuffer(ByteBuffer buffer) {
        setBuffer(buffer, buffer.capacity());
    }

    public void setBuffer(ByteBuffer buffer, int maxBufferSize) {
        if (buffer == null) {
            throw new IllegalArgumentException("buffer cannot be null.");
        }
        if (maxBufferSize < -1) {
            throw new IllegalArgumentException("maxBufferSize cannot be < -1: " + maxBufferSize);
        }
        this.byteBuffer = buffer;
        this.maxCapacity = maxBufferSize == -1 ? Util.maxArraySize : maxBufferSize;
        this.capacity = buffer.capacity();
        this.position = buffer.position();
        this.total = 0L;
        this.outputStream = null;
    }

    public ByteBuffer getByteBuffer() {
        return this.byteBuffer;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public byte[] toBytes() {
        byte[] newBuffer = new byte[this.position];
        setBufferPosition(this.byteBuffer, 0);
        this.byteBuffer.get(newBuffer, 0, this.position);
        return newBuffer;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void setPosition(int position) {
        this.position = position;
        setBufferPosition(this.byteBuffer, position);
    }

    @Override // com.esotericsoftware.kryo.io.Output, com.esotericsoftware.kryo.util.Pool.Poolable
    public void reset() {
        super.reset();
        setBufferPosition(this.byteBuffer, 0);
    }

    private int getBufferPosition(Buffer buffer) {
        return buffer.position();
    }

    private void setBufferPosition(Buffer buffer, int newPosition) {
        buffer.position(newPosition);
    }

    private void setBufferLimit(Buffer buffer, int length) {
        buffer.limit(length);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    protected boolean require(int required) throws KryoException {
        if (this.capacity - this.position >= required) {
            return false;
        }
        flush();
        if (this.capacity - this.position >= required) {
            return true;
        }
        if (required > this.maxCapacity - this.position) {
            if (required > this.maxCapacity) {
                throw new KryoBufferOverflowException("Buffer overflow. Max capacity: " + this.maxCapacity + ", required: " + required);
            }
            throw new KryoBufferOverflowException("Buffer overflow. Available: " + (this.maxCapacity - this.position) + ", required: " + required);
        }
        if (this.capacity == 0) {
            this.capacity = 16;
        }
        do {
            this.capacity = Math.min(this.capacity * 2, this.maxCapacity);
        } while (this.capacity - this.position < required);
        ByteBuffer newBuffer = !this.byteBuffer.isDirect() ? ByteBuffer.allocate(this.capacity) : ByteBuffer.allocateDirect(this.capacity);
        setBufferPosition(this.byteBuffer, 0);
        setBufferLimit(this.byteBuffer, this.position);
        newBuffer.put(this.byteBuffer);
        newBuffer.order(this.byteBuffer.order());
        this.byteBuffer = newBuffer;
        return true;
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream, java.io.Flushable
    public void flush() throws KryoException {
        if (this.outputStream == null) {
            return;
        }
        try {
            byte[] tmp = new byte[this.position];
            setBufferPosition(this.byteBuffer, 0);
            this.byteBuffer.get(tmp);
            setBufferPosition(this.byteBuffer, 0);
            this.outputStream.write(tmp, 0, this.position);
            this.total += this.position;
            this.position = 0;
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws KryoException {
        flush();
        if (this.outputStream != null) {
            try {
                this.outputStream.close();
            } catch (IOException e) {
            }
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream
    public void write(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        this.byteBuffer.put((byte) value);
        this.position++;
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream
    public void write(byte[] bytes) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        writeBytes(bytes, 0, bytes.length);
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream
    public void write(byte[] bytes, int offset, int length) throws KryoException {
        writeBytes(bytes, offset, length);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeByte(byte value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        this.byteBuffer.put(value);
        this.position++;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeByte(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        this.byteBuffer.put((byte) value);
        this.position++;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBytes(byte[] bytes) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        writeBytes(bytes, 0, bytes.length);
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBytes(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.capacity - this.position, count);
        while (true) {
            this.byteBuffer.put(bytes, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count == 0) {
                return;
            }
            offset += copyCount;
            copyCount = Math.min(this.capacity, count);
            require(copyCount);
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeInt(int value) throws KryoException {
        require(4);
        this.position += 4;
        ByteBuffer byteBuffer = this.byteBuffer;
        byteBuffer.put((byte) value);
        byteBuffer.put((byte) (value >> 8));
        byteBuffer.put((byte) (value >> 16));
        byteBuffer.put((byte) (value >> 24));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public int writeVarInt(int value, boolean optimizePositive) throws KryoException {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 31);
        }
        if ((value >>> 7) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            this.position++;
            this.byteBuffer.put((byte) value);
            return 1;
        }
        if ((value >>> 14) == 0) {
            require(2);
            this.position += 2;
            this.byteBuffer.put((byte) ((value & 127) | 128));
            this.byteBuffer.put((byte) (value >>> 7));
            return 2;
        }
        if ((value >>> 21) == 0) {
            require(3);
            this.position += 3;
            ByteBuffer byteBuffer = this.byteBuffer;
            byteBuffer.put((byte) ((value & 127) | 128));
            byteBuffer.put((byte) ((value >>> 7) | 128));
            byteBuffer.put((byte) (value >>> 14));
            return 3;
        }
        if ((value >>> 28) == 0) {
            require(4);
            this.position += 4;
            ByteBuffer byteBuffer2 = this.byteBuffer;
            byteBuffer2.put((byte) ((value & 127) | 128));
            byteBuffer2.put((byte) ((value >>> 7) | 128));
            byteBuffer2.put((byte) ((value >>> 14) | 128));
            byteBuffer2.put((byte) (value >>> 21));
            return 4;
        }
        require(5);
        this.position += 5;
        ByteBuffer byteBuffer3 = this.byteBuffer;
        byteBuffer3.put((byte) ((value & 127) | 128));
        byteBuffer3.put((byte) ((value >>> 7) | 128));
        byteBuffer3.put((byte) ((value >>> 14) | 128));
        byteBuffer3.put((byte) ((value >>> 21) | 128));
        byteBuffer3.put((byte) (value >>> 28));
        return 5;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public int writeVarIntFlag(boolean flag, int value, boolean optimizePositive) throws KryoException {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 31);
        }
        int first = (value & 63) | (flag ? 128 : 0);
        if ((value >>> 6) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            this.byteBuffer.put((byte) first);
            this.position++;
            return 1;
        }
        if ((value >>> 13) == 0) {
            require(2);
            this.position += 2;
            this.byteBuffer.put((byte) (first | 64));
            this.byteBuffer.put((byte) (value >>> 6));
            return 2;
        }
        if ((value >>> 20) == 0) {
            require(3);
            this.position += 3;
            ByteBuffer byteBuffer = this.byteBuffer;
            byteBuffer.put((byte) (first | 64));
            byteBuffer.put((byte) (128 | (value >>> 6)));
            byteBuffer.put((byte) (value >>> 13));
            return 3;
        }
        if ((value >>> 27) == 0) {
            require(4);
            this.position += 4;
            ByteBuffer byteBuffer2 = this.byteBuffer;
            byteBuffer2.put((byte) (first | 64));
            byteBuffer2.put((byte) ((value >>> 6) | 128));
            byteBuffer2.put((byte) (128 | (value >>> 13)));
            byteBuffer2.put((byte) (value >>> 20));
            return 4;
        }
        require(5);
        this.position += 5;
        ByteBuffer byteBuffer3 = this.byteBuffer;
        byteBuffer3.put((byte) (first | 64));
        byteBuffer3.put((byte) ((value >>> 6) | 128));
        byteBuffer3.put((byte) ((value >>> 13) | 128));
        byteBuffer3.put((byte) (128 | (value >>> 20)));
        byteBuffer3.put((byte) (value >>> 27));
        return 5;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeLong(long value) throws KryoException {
        require(8);
        this.position += 8;
        ByteBuffer byteBuffer = this.byteBuffer;
        byteBuffer.put((byte) value);
        byteBuffer.put((byte) (value >>> 8));
        byteBuffer.put((byte) (value >>> 16));
        byteBuffer.put((byte) (value >>> 24));
        byteBuffer.put((byte) (value >>> 32));
        byteBuffer.put((byte) (value >>> 40));
        byteBuffer.put((byte) (value >>> 48));
        byteBuffer.put((byte) (value >>> 56));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public int writeVarLong(long value, boolean optimizePositive) throws KryoException {
        long value2 = !optimizePositive ? (value << 1) ^ (value >> 63) : value;
        if ((value2 >>> 7) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            this.position++;
            this.byteBuffer.put((byte) value2);
            return 1;
        }
        if ((value2 >>> 14) == 0) {
            require(2);
            this.position += 2;
            this.byteBuffer.put((byte) ((value2 & 127) | 128));
            this.byteBuffer.put((byte) (value2 >>> 7));
            return 2;
        }
        if ((value2 >>> 21) == 0) {
            require(3);
            this.position += 3;
            ByteBuffer byteBuffer = this.byteBuffer;
            byteBuffer.put((byte) ((value2 & 127) | 128));
            byteBuffer.put((byte) ((value2 >>> 7) | 128));
            byteBuffer.put((byte) (value2 >>> 14));
            return 3;
        }
        if ((value2 >>> 28) == 0) {
            require(4);
            this.position += 4;
            ByteBuffer byteBuffer2 = this.byteBuffer;
            byteBuffer2.put((byte) ((value2 & 127) | 128));
            byteBuffer2.put((byte) ((value2 >>> 7) | 128));
            byteBuffer2.put((byte) ((value2 >>> 14) | 128));
            byteBuffer2.put((byte) (value2 >>> 21));
            return 4;
        }
        if ((value2 >>> 35) == 0) {
            require(5);
            this.position += 5;
            ByteBuffer byteBuffer3 = this.byteBuffer;
            byteBuffer3.put((byte) ((127 & value2) | 128));
            byteBuffer3.put((byte) ((value2 >>> 7) | 128));
            byteBuffer3.put((byte) ((value2 >>> 14) | 128));
            byteBuffer3.put((byte) ((value2 >>> 21) | 128));
            byteBuffer3.put((byte) (value2 >>> 28));
            return 5;
        }
        if ((value2 >>> 42) == 0) {
            require(6);
            this.position += 6;
            ByteBuffer byteBuffer4 = this.byteBuffer;
            byteBuffer4.put((byte) ((127 & value2) | 128));
            byteBuffer4.put((byte) ((value2 >>> 7) | 128));
            byteBuffer4.put((byte) ((value2 >>> 14) | 128));
            byteBuffer4.put((byte) ((value2 >>> 21) | 128));
            byteBuffer4.put((byte) ((value2 >>> 28) | 128));
            byteBuffer4.put((byte) (value2 >>> 35));
            return 6;
        }
        if ((value2 >>> 49) == 0) {
            require(7);
            this.position += 7;
            ByteBuffer byteBuffer5 = this.byteBuffer;
            byteBuffer5.put((byte) ((value2 & 127) | 128));
            byteBuffer5.put((byte) ((value2 >>> 7) | 128));
            byteBuffer5.put((byte) ((value2 >>> 14) | 128));
            byteBuffer5.put((byte) ((value2 >>> 21) | 128));
            byteBuffer5.put((byte) ((value2 >>> 28) | 128));
            byteBuffer5.put((byte) ((value2 >>> 35) | 128));
            byteBuffer5.put((byte) (value2 >>> 42));
            return 7;
        }
        if ((value2 >>> 56) == 0) {
            require(8);
            this.position += 8;
            ByteBuffer byteBuffer6 = this.byteBuffer;
            byteBuffer6.put((byte) ((127 & value2) | 128));
            byteBuffer6.put((byte) ((value2 >>> 7) | 128));
            byteBuffer6.put((byte) ((value2 >>> 14) | 128));
            byteBuffer6.put((byte) ((value2 >>> 21) | 128));
            byteBuffer6.put((byte) ((value2 >>> 28) | 128));
            byteBuffer6.put((byte) ((value2 >>> 35) | 128));
            byteBuffer6.put((byte) ((value2 >>> 42) | 128));
            byteBuffer6.put((byte) (value2 >>> 49));
            return 8;
        }
        require(9);
        this.position += 9;
        ByteBuffer byteBuffer7 = this.byteBuffer;
        byteBuffer7.put((byte) ((127 & value2) | 128));
        byteBuffer7.put((byte) ((value2 >>> 7) | 128));
        byteBuffer7.put((byte) ((value2 >>> 14) | 128));
        byteBuffer7.put((byte) ((value2 >>> 21) | 128));
        byteBuffer7.put((byte) ((value2 >>> 28) | 128));
        byteBuffer7.put((byte) ((value2 >>> 35) | 128));
        byteBuffer7.put((byte) ((value2 >>> 42) | 128));
        byteBuffer7.put((byte) ((value2 >>> 49) | 128));
        byteBuffer7.put((byte) (value2 >>> 56));
        return 9;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeFloat(float value) throws KryoException {
        require(4);
        ByteBuffer byteBuffer = this.byteBuffer;
        this.position += 4;
        int intValue = Float.floatToIntBits(value);
        byteBuffer.put((byte) intValue);
        byteBuffer.put((byte) (intValue >> 8));
        byteBuffer.put((byte) (intValue >> 16));
        byteBuffer.put((byte) (intValue >> 24));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeDouble(double value) throws KryoException {
        require(8);
        this.position += 8;
        ByteBuffer byteBuffer = this.byteBuffer;
        long longValue = Double.doubleToLongBits(value);
        byteBuffer.put((byte) longValue);
        byteBuffer.put((byte) (longValue >>> 8));
        byteBuffer.put((byte) (longValue >>> 16));
        byteBuffer.put((byte) (longValue >>> 24));
        byteBuffer.put((byte) (longValue >>> 32));
        byteBuffer.put((byte) (longValue >>> 40));
        byteBuffer.put((byte) (longValue >>> 48));
        byteBuffer.put((byte) (longValue >>> 56));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeShort(int value) throws KryoException {
        require(2);
        this.position += 2;
        this.byteBuffer.put((byte) value);
        this.byteBuffer.put((byte) (value >>> 8));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeChar(char value) throws KryoException {
        require(2);
        this.position += 2;
        this.byteBuffer.put((byte) value);
        this.byteBuffer.put((byte) (value >>> '\b'));
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBoolean(boolean z) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        this.byteBuffer.put(z ? (byte) 1 : (byte) 0);
        this.position++;
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeString(String value) throws KryoException {
        if (value == null) {
            writeByte(128);
            return;
        }
        int charCount = value.length();
        if (charCount == 0) {
            writeByte(Opcodes.LOR);
            return;
        }
        if (charCount > 1 && charCount <= 32) {
            for (int i = 0; i < charCount; i++) {
                if (value.charAt(i) <= 127) {
                }
            }
            if (this.capacity - this.position < charCount) {
                writeAscii_slow(value, charCount);
            } else {
                int n = value.length();
                for (int i2 = 0; i2 < n; i2++) {
                    this.byteBuffer.put((byte) value.charAt(i2));
                }
                int i3 = this.position;
                this.position = i3 + charCount;
            }
            this.byteBuffer.put(this.position - 1, (byte) (128 | this.byteBuffer.get(this.position - 1)));
            return;
        }
        writeVarIntFlag(true, charCount + 1, true);
        int charIndex = 0;
        if (this.capacity - this.position >= charCount) {
            ByteBuffer byteBuffer = this.byteBuffer;
            do {
                int c = value.charAt(charIndex);
                if (c <= 127) {
                    byteBuffer.put((byte) c);
                    charIndex++;
                } else {
                    this.position = getBufferPosition(byteBuffer);
                }
            } while (charIndex != charCount);
            this.position = getBufferPosition(byteBuffer);
            return;
        }
        if (charIndex < charCount) {
            writeUtf8_slow(value, charCount, charIndex);
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeAscii(String value) throws KryoException {
        if (value == null) {
            writeByte(128);
            return;
        }
        int charCount = value.length();
        if (charCount == 0) {
            writeByte(Opcodes.LOR);
            return;
        }
        if (this.capacity - this.position < charCount) {
            writeAscii_slow(value, charCount);
        } else {
            ByteBuffer byteBuffer = this.byteBuffer;
            int n = value.length();
            for (int i = 0; i < n; i++) {
                byteBuffer.put((byte) value.charAt(i));
            }
            int i2 = this.position;
            this.position = i2 + charCount;
        }
        ByteBuffer byteBuffer2 = this.byteBuffer;
        byteBuffer2.put(this.position - 1, (byte) (128 | this.byteBuffer.get(this.position - 1)));
    }

    private void writeUtf8_slow(String value, int charCount, int charIndex) {
        while (charIndex < charCount) {
            if (this.position == this.capacity) {
                require(Math.min(this.capacity, charCount - charIndex));
            }
            this.position++;
            int c = value.charAt(charIndex);
            if (c <= 127) {
                this.byteBuffer.put((byte) c);
            } else if (c > 2047) {
                this.byteBuffer.put((byte) (((c >> 12) & 15) | BERTags.FLAGS));
                require(2);
                this.position += 2;
                this.byteBuffer.put((byte) (((c >> 6) & 63) | 128));
                this.byteBuffer.put((byte) ((c & 63) | 128));
            } else {
                this.byteBuffer.put((byte) (((c >> 6) & 31) | 192));
                if (this.position == this.capacity) {
                    require(1);
                }
                this.position++;
                this.byteBuffer.put((byte) ((c & 63) | 128));
            }
            charIndex++;
        }
    }

    private void writeAscii_slow(String value, int charCount) throws KryoException {
        ByteBuffer buffer = this.byteBuffer;
        int charIndex = 0;
        int charsToWrite = Math.min(charCount, this.capacity - this.position);
        while (charIndex < charCount) {
            byte[] tmp = new byte[charCount];
            value.getBytes(charIndex, charIndex + charsToWrite, tmp, 0);
            buffer.put(tmp, 0, charsToWrite);
            charIndex += charsToWrite;
            this.position += charsToWrite;
            charsToWrite = Math.min(charCount - charIndex, this.capacity);
            if (require(charsToWrite)) {
                buffer = this.byteBuffer;
            }
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeInts(int[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 2)) {
            require(count << 2);
            ByteBuffer byteBuffer = this.byteBuffer;
            int n = offset + count;
            while (offset < n) {
                int value = array[offset];
                byteBuffer.put((byte) value);
                byteBuffer.put((byte) (value >> 8));
                byteBuffer.put((byte) (value >> 16));
                byteBuffer.put((byte) (value >> 24));
                offset++;
            }
            int n2 = getBufferPosition(byteBuffer);
            this.position = n2;
            return;
        }
        int n3 = offset + count;
        while (offset < n3) {
            writeInt(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeLongs(long[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 3)) {
            require(count << 3);
            ByteBuffer byteBuffer = this.byteBuffer;
            int n = offset + count;
            while (offset < n) {
                long value = array[offset];
                byteBuffer.put((byte) value);
                byteBuffer.put((byte) (value >>> 8));
                byteBuffer.put((byte) (value >>> 16));
                byteBuffer.put((byte) (value >>> 24));
                byteBuffer.put((byte) (value >>> 32));
                byteBuffer.put((byte) (value >>> 40));
                byteBuffer.put((byte) (value >>> 48));
                byteBuffer.put((byte) (value >>> 56));
                offset++;
            }
            int n2 = getBufferPosition(byteBuffer);
            this.position = n2;
            return;
        }
        int n3 = offset + count;
        while (offset < n3) {
            writeLong(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeFloats(float[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 2)) {
            require(count << 2);
            ByteBuffer byteBuffer = this.byteBuffer;
            int n = offset + count;
            while (offset < n) {
                int value = Float.floatToIntBits(array[offset]);
                byteBuffer.put((byte) value);
                byteBuffer.put((byte) (value >> 8));
                byteBuffer.put((byte) (value >> 16));
                byteBuffer.put((byte) (value >> 24));
                offset++;
            }
            int n2 = getBufferPosition(byteBuffer);
            this.position = n2;
            return;
        }
        int n3 = offset + count;
        while (offset < n3) {
            writeFloat(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeDoubles(double[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 3)) {
            require(count << 3);
            ByteBuffer byteBuffer = this.byteBuffer;
            int n = offset + count;
            while (offset < n) {
                long value = Double.doubleToLongBits(array[offset]);
                byteBuffer.put((byte) value);
                byteBuffer.put((byte) (value >>> 8));
                byteBuffer.put((byte) (value >>> 16));
                byteBuffer.put((byte) (value >>> 24));
                byteBuffer.put((byte) (value >>> 32));
                byteBuffer.put((byte) (value >>> 40));
                byteBuffer.put((byte) (value >>> 48));
                byteBuffer.put((byte) (value >>> 56));
                offset++;
            }
            int n2 = getBufferPosition(byteBuffer);
            this.position = n2;
            return;
        }
        int n3 = offset + count;
        while (offset < n3) {
            writeDouble(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeShorts(short[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 1)) {
            require(count << 1);
            int n = offset + count;
            while (offset < n) {
                short s = array[offset];
                this.byteBuffer.put((byte) s);
                this.byteBuffer.put((byte) (s >>> 8));
                offset++;
            }
            this.position = getBufferPosition(this.byteBuffer);
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeShort(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeChars(char[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 1)) {
            require(count << 1);
            int n = offset + count;
            while (offset < n) {
                char c = array[offset];
                this.byteBuffer.put((byte) c);
                this.byteBuffer.put((byte) (c >>> '\b'));
                offset++;
            }
            this.position = getBufferPosition(this.byteBuffer);
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeChar(array[offset]);
            offset++;
        }
    }

    @Override // com.esotericsoftware.kryo.io.Output
    public void writeBooleans(boolean[] zArr, int i, int i2) throws KryoException {
        if (this.capacity >= i2) {
            require(i2);
            int i3 = i + i2;
            while (i < i3) {
                this.byteBuffer.put(zArr[i] ? (byte) 1 : (byte) 0);
                i++;
            }
            this.position = getBufferPosition(this.byteBuffer);
            return;
        }
        int i4 = i + i2;
        while (i < i4) {
            writeBoolean(zArr[i]);
            i++;
        }
    }
}
