package com.google.android.gms.tapandpay.globalactions;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GetGlobalActionCardsRequest.smali */
public final class GetGlobalActionCardsRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GetGlobalActionCardsRequest> CREATOR = new zzb();
    private int zza;
    private int zzb;
    private int zzc;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GetGlobalActionCardsRequest$Builder.smali */
    public static final class Builder {
        private final GetGlobalActionCardsRequest zza;

        public Builder() {
            this.zza = new GetGlobalActionCardsRequest(null);
        }

        public GetGlobalActionCardsRequest build() {
            return this.zza;
        }

        public Builder setCardHeightPx(int cardHeightPx) {
            this.zza.zzc = cardHeightPx;
            return this;
        }

        public Builder setCardWidthPx(int cardWidthPx) {
            this.zza.zzb = cardWidthPx;
            return this;
        }

        public Builder setMaxCards(int maxCards) {
            this.zza.zza = maxCards;
            return this;
        }

        public Builder(GetGlobalActionCardsRequest origin) {
            GetGlobalActionCardsRequest getGlobalActionCardsRequest = new GetGlobalActionCardsRequest(null);
            this.zza = getGlobalActionCardsRequest;
            getGlobalActionCardsRequest.zza = origin.zza;
            getGlobalActionCardsRequest.zzb = origin.zzb;
            getGlobalActionCardsRequest.zzc = origin.zzc;
        }
    }

    private GetGlobalActionCardsRequest() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof GetGlobalActionCardsRequest) {
            GetGlobalActionCardsRequest getGlobalActionCardsRequest = (GetGlobalActionCardsRequest) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(getGlobalActionCardsRequest.zza)) && Objects.equal(Integer.valueOf(this.zzb), Integer.valueOf(getGlobalActionCardsRequest.zzb)) && Objects.equal(Integer.valueOf(this.zzc), Integer.valueOf(getGlobalActionCardsRequest.zzc))) {
                return true;
            }
        }
        return false;
    }

    public int getCardHeightPx() {
        return this.zzc;
    }

    public int getCardWidthPx() {
        return this.zzb;
    }

    public int getMaxCards() {
        return this.zza;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), Integer.valueOf(this.zzb), Integer.valueOf(this.zzc));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 1, getMaxCards());
        SafeParcelWriter.writeInt(dest, 2, getCardWidthPx());
        SafeParcelWriter.writeInt(dest, 3, getCardHeightPx());
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    GetGlobalActionCardsRequest(int i, int i2, int i3) {
        this.zza = i;
        this.zzb = i2;
        this.zzc = i3;
    }

    /* synthetic */ GetGlobalActionCardsRequest(zza zzaVar) {
    }
}
