package o.eo;

import android.graphics.ImageFormat;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\b.smali */
public final class b implements o.ei.b {
    private static final /* synthetic */ b[] b;
    public static final b c;
    public static final b d;
    public static final b e;
    private static char f;
    private static char g;
    private static int h;
    private static char i;
    private static char j;
    private final String a;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int n = 1;

    static void a() {
        g = (char) 21979;
        i = (char) 50403;
        f = (char) 26774;
        j = (char) 57350;
    }

    private static /* synthetic */ b[] d() {
        int i2 = n;
        int i3 = i2 + 3;
        h = i3 % 128;
        int i4 = i3 % 2;
        b[] bVarArr = {d, e, c};
        int i5 = i2 + 37;
        h = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                int i6 = 62 / 0;
                return bVarArr;
            default:
                return bVarArr;
        }
    }

    public static b valueOf(String str) {
        int i2 = h + 5;
        n = i2 % 128;
        int i3 = i2 % 2;
        b bVar = (b) Enum.valueOf(b.class, str);
        int i4 = n + 61;
        h = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    public static b[] values() {
        int i2 = n + 81;
        h = i2 % 128;
        int i3 = i2 % 2;
        b[] bVarArr = (b[]) b.clone();
        int i4 = h + 41;
        n = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return bVarArr;
            default:
                throw null;
        }
    }

    static {
        h = 0;
        a();
        Object[] objArr = new Object[1];
        k("뵭곩ซ咟㌗縯", ImageFormat.getBitsPerPixel(0) + 7, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("뱞뇰短贫\udd4d⤦", (ViewConfiguration.getJumpTapTimeout() >> 16) + 6, objArr2);
        d = new b(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k("ϼ檷巬횼\udf66煂ܛΏ䧄桼", TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 10, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        k("꼫ک庥ㄓ產\ue0eb炤\uf037\ud856惐", KeyEvent.getDeadChar(0, 0) + 9, objArr4);
        e = new b(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        k("意⎔蜠\udee8\ua95c㴗䧄桼", 7 - TextUtils.getOffsetBefore("", 0), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        k("炤\uf037噔嗈칗紽\ud856惐", ExpandableListView.getPackedPositionGroup(0L) + 7, objArr6);
        c = new b(intern3, 2, ((String) objArr6[0]).intern());
        b = d();
        int i2 = n + 63;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    private b(String str, int i2, String str2) {
        this.a = str2;
    }

    public final String c() {
        int i2 = h + 47;
        n = i2 % 128;
        switch (i2 % 2 == 0 ? 'Z' : ':') {
            case 'Z':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.a;
        }
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = n;
        int i3 = i2 + 77;
        h = i3 % 128;
        int i4 = i3 % 2;
        String str = this.a;
        int i5 = i2 + 87;
        h = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r25, int r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 556
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.b.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
