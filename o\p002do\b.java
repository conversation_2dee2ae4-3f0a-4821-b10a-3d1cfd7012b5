package o.p002do;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\do\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static b a;
    private static int c;
    private static char d;
    private static long e;
    private static int f;
    private static int g;
    private d b;

    static void b() {
        d = (char) 18369;
        c = 161105445;
        e = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.p002do.b.$$a
            int r8 = r8 * 2
            int r8 = r8 + 4
            int r6 = 106 - r6
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L31
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L31:
            int r8 = -r8
            int r9 = r9 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p002do.b.i(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{30, -100, -127, 60};
        $$b = 215;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        b();
        View.resolveSize(0, 0);
        TextUtils.indexOf("", "", 0);
        a = new b();
        int i = g + 21;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    public static b e() {
        int i = f + 27;
        g = i % 128;
        int i2 = i % 2;
        if (a == null) {
            a = new b();
        }
        b bVar = a;
        int i3 = f + 45;
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return bVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private b() {
    }

    public final void d() {
        int i = f + 25;
        g = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        h(TextUtils.lastIndexOf("", '0') + 2119830571, "ၕ썪\uf53d㚣潆뚮㝧贾ㄛ魳☸럭亨櫉\u0b45䓂髞ย챍篡ꏱ舡\uda6a腊젳쥙囷爌\ueb72竀赛", (char) (58181 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), "⫑娌䕾壣", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h((-787551105) - Drawable.resolveOpacity(0, 0), "䷉鈐훳슄樅膑ᖙᾡ᭩한橾嚞栲귃፝좋\udbf3釖嶺풇\ufafc燇ᖁ\udf3d땉馷☰鞫舗", (char) (51176 - TextUtils.indexOf("", "", 0)), "翖\u0eec\ue8d1\uecc7", "\u0000\u0000\u0000\u0000", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.b = null;
        int i3 = f + 69;
        g = i3 % 128;
        int i4 = i3 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 710
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p002do.b.h(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
