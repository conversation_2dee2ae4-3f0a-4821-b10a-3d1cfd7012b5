package o.dk;

import android.graphics.Color;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import kotlin.text.Typography;
import o.a.l;
import o.e.a;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dk\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static char[] c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        d();
        int i = b + Opcodes.LSHL;
        e = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        c = new char[]{50764, 51165, 51173, 51147, 50750, 51152, 51168, 51178, 51176, 51177, 50736, 50739, 51173, 51150, 51148, 51173, 51174, 51145, 51151, 51174, 51175, 51172, 51177, 51176, 51172, 51169, 51147, 50736, 51177, 51175, 51172, 51180, 51177, 51171, 51179, 51155, 50737, 50730, 50730, 50941, 50857, 50859, 50853, 50849, 50855, 50825, 50820, 50877, 50854, 50856, 50855, 50876, 50851, 50852, 50828, 50830, 50855, 50854, 50848, 50878, 50862, 50932, 50817, 50851, 50843, 50937, 50820, 50854, 50849, 50854, 50863, 50852, 50852, 50857, 50856, 50828, 50827, 50901, 50940, 50824, 50931, 50943, 50857, 50851, 50868, 50862, 50816, 50836, 50790, 50787, 50813, 50784, 50757, 50756, 50813, 50789, 50791, 50791, 50793, 50792, 50795, 50792, 50764, 50766, 50784, 50784, 50786, 50759, 50757, 50808, 50785, 50789, 50812, 50789, 50866, 50764, 50790, 50808, 50769, 50877, 50853, 50853, 50767, 50788, 50791, 50769, 50754, 50783, 50935, 50851, 50855, 50850, 50833, 50836, 50876, 50856};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 66
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r0 = o.dk.b.$$a
            int r7 = r7 * 2
            int r7 = 4 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r9 = r8
            r3 = r9
            r4 = r2
            r8 = r7
            goto L2c
        L16:
            r3 = r2
        L17:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2c:
            int r3 = -r3
            int r7 = r7 + r3
            int r8 = r8 + 1
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.b.g(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = 32;
    }

    public static byte[] b(String str) {
        switch (str != null ? 'c' : (char) 18) {
            case Opcodes.DADD /* 99 */:
                int i = e + 33;
                b = i % 128;
                int i2 = i % 2;
                if (str.length() != 0) {
                    int i3 = b + 75;
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    if (str.length() % 2 == 0) {
                        int length = str.length();
                        byte[] bArr = new byte[length / 2];
                        int i5 = 0;
                        while (i5 < length) {
                            int a = a(str.charAt(i5));
                            int a2 = a(str.charAt(i5 + 1));
                            switch (a == -1) {
                                case false:
                                    int i6 = e;
                                    int i7 = i6 + 71;
                                    b = i7 % 128;
                                    int i8 = i7 % 2;
                                    if (a2 == -1) {
                                        break;
                                    } else {
                                        bArr[i5 / 2] = (byte) ((a << 4) + a2);
                                        i5 += 2;
                                        int i9 = i6 + 17;
                                        b = i9 % 128;
                                        int i10 = i9 % 2;
                                    }
                            }
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            f("\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000", new int[]{39, 38, 0, 26}, true, objArr);
                            throw new IllegalArgumentException(sb.append(((String) objArr[0]).intern()).append(str).toString());
                        }
                        return bArr;
                    }
                }
                break;
        }
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000", new int[]{0, 39, Opcodes.INVOKEDYNAMIC, 0}, false, objArr2);
        throw new IllegalArgumentException(sb2.append(((String) objArr2[0]).intern()).append(str).toString());
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0025, code lost:
    
        r0 = a(r9.charAt(0));
        r5 = a(r9.charAt(1));
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0036, code lost:
    
        if (r0 == (-1)) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0038, code lost:
    
        r7 = o.dk.b.b + 79;
        o.dk.b.e = r7 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0041, code lost:
    
        if ((r7 % 2) != 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0043, code lost:
    
        r1 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0046, code lost:
    
        switch(r1) {
            case 1: goto L23;
            default: goto L21;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0049, code lost:
    
        if (r5 == (-1)) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x0054, code lost:
    
        return (byte) ((r0 << 4) + r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x004d, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0045, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0055, code lost:
    
        r1 = new java.lang.StringBuilder();
        r5 = new java.lang.Object[1];
        f("\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000", new int[]{39, 38, 0, 26}, true, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0082, code lost:
    
        throw new java.lang.IllegalArgumentException(r1.append(((java.lang.String) r5[0]).intern()).append(r9).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0022, code lost:
    
        if (r9.length() == 2) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001b, code lost:
    
        if (r9.length() == 5) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0083, code lost:
    
        r1 = new java.lang.StringBuilder();
        r2 = new java.lang.Object[1];
        f("\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000", new int[]{0, 39, com.esotericsoftware.asm.Opcodes.INVOKEDYNAMIC, 0}, false, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x00ae, code lost:
    
        throw new java.lang.IllegalArgumentException(r1.append(((java.lang.String) r2[0]).intern()).append(r9).toString());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte c(java.lang.String r9) {
        /*
            int r0 = o.dk.b.e
            int r0 = r0 + 19
            int r1 = r0 % 128
            o.dk.b.b = r1
            r1 = 2
            int r0 = r0 % r1
            r2 = 1
            r3 = 0
            if (r0 == 0) goto L10
            r0 = r3
            goto L11
        L10:
            r0 = r2
        L11:
            r4 = 39
            switch(r0) {
                case 1: goto L1e;
                default: goto L16;
            }
        L16:
            int r0 = r9.length()
            r5 = 5
            if (r0 != r5) goto L83
        L1d:
            goto L25
        L1e:
            int r0 = r9.length()
            if (r0 != r1) goto L83
            goto L1d
        L25:
            char r0 = r9.charAt(r3)
            int r0 = a(r0)
            char r5 = r9.charAt(r2)
            int r5 = a(r5)
            r6 = -1
            if (r0 == r6) goto L55
            int r7 = o.dk.b.b
            int r7 = r7 + 79
            int r8 = r7 % 128
            o.dk.b.e = r8
            int r7 = r7 % r1
            if (r7 != 0) goto L45
            r1 = r2
            goto L46
        L45:
            r1 = r3
        L46:
            switch(r1) {
                case 1: goto L4c;
                default: goto L49;
            }
        L49:
            if (r5 == r6) goto L55
            goto L50
        L4c:
            r9 = 0
            throw r9     // Catch: java.lang.Throwable -> L4e
        L4e:
            r9 = move-exception
            throw r9
        L50:
            int r9 = r0 << 4
            int r9 = r9 + r5
            byte r9 = (byte) r9
            return r9
        L55:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            r5 = 38
            r6 = 26
            int[] r4 = new int[]{r4, r5, r3, r6}
            java.lang.Object[] r5 = new java.lang.Object[r2]
            java.lang.String r6 = "\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000"
            f(r6, r4, r2, r5)
            r2 = r5[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r9 = r1.append(r9)
            java.lang.String r9 = r9.toString()
            r0.<init>(r9)
            throw r0
        L83:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            r5 = 186(0xba, float:2.6E-43)
            int[] r4 = new int[]{r3, r4, r5, r3}
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r5 = "\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000"
            f(r5, r4, r3, r2)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r9 = r1.append(r9)
            java.lang.String r9 = r9.toString()
            r0.<init>(r9)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.b.c(java.lang.String):byte");
    }

    /* JADX WARN: Code restructure failed: missing block: B:19:0x003a, code lost:
    
        if (r5 <= 'F') goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x003f, code lost:
    
        r5 = (r5 - 'A') + 10;
        r4 = r4 + 11;
        o.dk.b.b = r4 % 128;
        r4 = r4 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x004a, code lost:
    
        return r5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x003d, code lost:
    
        if (r5 <= 0) goto L25;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static int a(char r5) {
        /*
            r0 = 48
            r1 = 0
            r2 = 1
            if (r0 > r5) goto L9
            r3 = r1
            goto La
        L9:
            r3 = r2
        La:
            switch(r3) {
                case 0: goto Le;
                default: goto Ld;
            }
        Ld:
            goto L1e
        Le:
            int r3 = o.dk.b.b
            int r3 = r3 + 21
            int r4 = r3 % 128
            o.dk.b.e = r4
            int r3 = r3 % 2
            r3 = 57
            if (r5 > r3) goto L1e
            int r5 = r5 - r0
            return r5
        L1e:
            r0 = 65
            if (r0 > r5) goto L24
            r3 = r1
            goto L25
        L24:
            r3 = r2
        L25:
            switch(r3) {
                case 1: goto L4b;
                default: goto L28;
            }
        L28:
            int r3 = o.dk.b.b
            int r3 = r3 + 31
            int r4 = r3 % 128
            o.dk.b.e = r4
            int r3 = r3 % 2
            if (r3 != 0) goto L35
            r1 = r2
        L35:
            switch(r1) {
                case 1: goto L3d;
                default: goto L38;
            }
        L38:
            r1 = 70
            if (r5 > r1) goto L4b
            goto L3f
        L3d:
            if (r5 > 0) goto L4b
        L3f:
            int r5 = r5 - r0
            int r5 = r5 + 10
            int r4 = r4 + 11
            int r0 = r4 % 128
            o.dk.b.b = r0
            int r4 = r4 % 2
            return r5
        L4b:
            r0 = 97
            if (r0 > r5) goto L57
            r1 = 102(0x66, float:1.43E-43)
            if (r5 > r1) goto L57
            int r5 = r5 - r0
            int r5 = r5 + 10
            return r5
        L57:
            int r5 = o.dk.b.e
            int r5 = r5 + 45
            int r0 = r5 % 128
            o.dk.b.b = r0
            int r5 = r5 % 2
            if (r5 == 0) goto L66
            r5 = 86
            goto L68
        L66:
            r5 = 40
        L68:
            switch(r5) {
                case 86: goto L6d;
                default: goto L6b;
            }
        L6b:
            r5 = -1
            return r5
        L6d:
            r5 = 0
            throw r5     // Catch: java.lang.Throwable -> L6f
        L6f:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.b.a(char):int");
    }

    public static String e(byte[] bArr, String str) {
        if (bArr == null) {
            int i = e + 79;
            b = i % 128;
            if (i % 2 == 0) {
                return "";
            }
            throw null;
        }
        switch (str == null ? (char) 0 : 'b') {
            case 0:
                str = "";
                break;
        }
        StringBuilder sb = new StringBuilder();
        int length = bArr.length;
        int i2 = 0;
        while (i2 < length) {
            Object[] objArr = new Object[1];
            f("\u0000\u0000\u0000\u0001", new int[]{77, 4, 0, 3}, false, objArr);
            sb.append(String.format(((String) objArr[0]).intern(), Byte.valueOf(bArr[i2])));
            i2++;
            switch (i2 < length ? 'R' : '%') {
                case Opcodes.DASTORE /* 82 */:
                    int i3 = b + 79;
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    sb.append(str);
                    break;
            }
        }
        return sb.toString();
    }

    public static String e(byte[] bArr) {
        int i = b + 51;
        e = i % 128;
        boolean z = i % 2 != 0;
        String e2 = e(bArr, "");
        switch (z) {
            case true:
                break;
            default:
                int i2 = 46 / 0;
                break;
        }
        int i3 = b + 37;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? 'I' : '.') {
            case '.':
                return e2;
            default:
                throw null;
        }
    }

    public static BigDecimal a(byte[] bArr) throws NumberFormatException {
        int i = b + 99;
        e = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? '\'' : Typography.dollar) {
            case '$':
                String e2 = e(bArr);
                if ("".equals(e2)) {
                    throw new NumberFormatException();
                }
                BigDecimal bigDecimal = new BigDecimal(e2);
                int i2 = e + 45;
                b = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 3 : (char) 5) {
                    case 5:
                        return bigDecimal;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                "".equals(e(bArr));
                throw null;
        }
    }

    public static String a(int i, int i2) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(i2, i - 1, 1);
        calendar.set(5, calendar.getActualMaximum(5));
        calendar.getTime();
        Object[] objArr = new Object[1];
        f("\u0000\u0000\u0001\u0000\u0000\u0000", new int[]{81, 6, 0, 2}, false, objArr);
        String format = new SimpleDateFormat(((String) objArr[0]).intern(), Locale.ENGLISH).format(calendar.getTime());
        int i3 = e + 13;
        b = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return format;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static int c(java.util.List<int[]> r3, int[] r4) {
        /*
            int r0 = o.dk.b.e
            int r1 = r0 + 75
            int r2 = r1 % 128
            o.dk.b.b = r2
            int r1 = r1 % 2
            int r0 = r0 + 117
            int r1 = r0 % 128
            o.dk.b.b = r1
            int r0 = r0 % 2
            r0 = 0
            r1 = r0
        L15:
            int r2 = r3.size()
            if (r1 >= r2) goto L1d
            r2 = r0
            goto L1f
        L1d:
            r2 = 89
        L1f:
            switch(r2) {
                case 89: goto L2f;
                default: goto L22;
            }
        L22:
            java.lang.Object r2 = r3.get(r1)
            int[] r2 = (int[]) r2
            boolean r2 = java.util.Arrays.equals(r2, r4)
            if (r2 == 0) goto L34
            goto L31
        L2f:
            r3 = -1
            return r3
        L31:
            r2 = 72
            goto L36
        L34:
            r2 = 41
        L36:
            switch(r2) {
                case 41: goto L46;
                default: goto L39;
            }
        L39:
            int r3 = o.dk.b.b
            int r3 = r3 + 47
            int r4 = r3 % 128
            o.dk.b.e = r4
            int r3 = r3 % 2
            if (r3 != 0) goto L4b
            goto L49
        L46:
            int r1 = r1 + 1
            goto L15
        L49:
            r3 = 3
            goto L4d
        L4b:
            r3 = 34
        L4d:
            switch(r3) {
                case 3: goto L51;
                default: goto L50;
            }
        L50:
            return r1
        L51:
            r3 = 0
            throw r3     // Catch: java.lang.Throwable -> L53
        L53:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.b.c(java.util.List, int[]):int");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0020. Please report as an issue. */
    public static byte[] e(byte[] bArr, byte[] bArr2) throws RuntimeException {
        int i = e + 39;
        int i2 = i % 128;
        b = i2;
        int i3 = i % 2;
        int length = bArr.length;
        if (bArr2.length != length) {
            Object[] objArr = new Object[1];
            f("\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{87, 41, 63, 0}, true, objArr);
            throw new RuntimeException(((String) objArr[0]).intern());
        }
        byte[] bArr3 = new byte[length];
        int i4 = i2 + 59;
        e = i4 % 128;
        switch (i4 % 2 == 0) {
        }
        int i5 = 0;
        while (true) {
            switch (i5 >= length) {
                case true:
                    int i6 = b + 53;
                    e = i6 % 128;
                    int i7 = i6 % 2;
                    return bArr3;
                default:
                    bArr3[i5] = (byte) (bArr[i5] ^ bArr2[i5]);
                    i5++;
            }
        }
    }

    public static String d(String str) {
        int i = e;
        int i2 = i + 35;
        b = i2 % 128;
        int i3 = i2 % 2;
        switch (str == null) {
            case true:
                int i4 = i + Opcodes.DREM;
                int i5 = i4 % 128;
                b = i5;
                int i6 = i4 % 2;
                int i7 = i5 + Opcodes.DMUL;
                e = i7 % 128;
                int i8 = i7 % 2;
                return null;
            default:
                return o.e(str);
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v28, types: [byte[]] */
    private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        ?? r0 = str;
        switch (r0 != 0 ? ']' : (char) 1) {
            case Opcodes.DUP2_X1 /* 93 */:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i = 0;
        int i2 = iArr[0];
        int i3 = iArr[1];
        int i4 = 2;
        int i5 = iArr[2];
        int i6 = iArr[3];
        char[] cArr2 = c;
        float f = 0.0f;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i7 = 0;
            while (i7 < length) {
                int i8 = $11 + Opcodes.LUSHR;
                $10 = i8 % 128;
                int i9 = i8 % i4;
                try {
                    Object[] objArr2 = new Object[1];
                    objArr2[i] = Integer.valueOf(cArr2[i7]);
                    Object obj = a.s.get(1951085128);
                    if (obj != null) {
                        cArr = cArr2;
                    } else {
                        Class cls = (Class) a.c(12 - (AudioTrack.getMaxVolume() > f ? 1 : (AudioTrack.getMaxVolume() == f ? 0 : -1)), (char) (AudioTrack.getMinVolume() > f ? 1 : (AudioTrack.getMinVolume() == f ? 0 : -1)), 43 - Color.red(i));
                        byte b2 = (byte) i;
                        byte b3 = b2;
                        cArr = cArr2;
                        Object[] objArr3 = new Object[1];
                        g(b2, b3, (byte) (b3 | 54), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        a.s.put(1951085128, obj);
                    }
                    cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i7++;
                    cArr2 = cArr;
                    i = 0;
                    i4 = 2;
                    f = 0.0f;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        char[] cArr4 = new char[i3];
        System.arraycopy(cArr2, i2, cArr4, 0, i3);
        if (bArr != null) {
            char[] cArr5 = new char[i3];
            lVar.d = 0;
            char c2 = 0;
            while (lVar.d < i3) {
                switch (bArr[lVar.d] != 1) {
                    case true:
                        int i10 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                            Object obj2 = a.s.get(804049217);
                            if (obj2 == null) {
                                Class cls2 = (Class) a.c(TextUtils.getTrimmedLength("") + 10, (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 207 - Color.alpha(0));
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                g(b4, b5, (byte) (b5 | 56), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                a.s.put(804049217, obj2);
                            }
                            cArr5[i10] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        int i11 = lVar.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                            Object obj3 = a.s.get(2016040108);
                            if (obj3 == null) {
                                Class cls3 = (Class) a.c(11 - (ViewConfiguration.getLongPressTimeout() >> 16), (char) ((-1) - MotionEvent.axisFromString("")), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 448);
                                byte b6 = (byte) 0;
                                byte b7 = b6;
                                Object[] objArr7 = new Object[1];
                                g(b6, b7, (byte) (b7 | 53), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                a.s.put(2016040108, obj3);
                            }
                            cArr5[i11] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                }
                c2 = cArr5[lVar.d];
                try {
                    Object[] objArr8 = {lVar, lVar};
                    Object obj4 = a.s.get(-2112603350);
                    if (obj4 == null) {
                        Class cls4 = (Class) a.c(TextUtils.indexOf((CharSequence) "", '0', 0) + 12, (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), MotionEvent.axisFromString("") + 260);
                        byte b8 = (byte) 0;
                        byte b9 = b8;
                        Object[] objArr9 = new Object[1];
                        g(b8, b9, b9, objArr9);
                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                        a.s.put(-2112603350, obj4);
                    }
                    ((Method) obj4).invoke(null, objArr8);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            int i12 = $10 + 59;
            $11 = i12 % 128;
            int i13 = i12 % 2;
            cArr4 = cArr5;
        }
        if (i6 > 0) {
            char[] cArr6 = new char[i3];
            System.arraycopy(cArr4, 0, cArr6, 0, i3);
            int i14 = i3 - i6;
            System.arraycopy(cArr6, 0, cArr4, i14, i6);
            System.arraycopy(cArr6, i6, cArr4, 0, i14);
        }
        switch (z) {
            case false:
                break;
            default:
                char[] cArr7 = new char[i3];
                lVar.d = 0;
                while (lVar.d < i3) {
                    cArr7[lVar.d] = cArr4[(i3 - lVar.d) - 1];
                    lVar.d++;
                }
                cArr4 = cArr7;
                break;
        }
        switch (i5 <= 0) {
            case false:
                int i15 = 0;
                while (true) {
                    lVar.d = i15;
                    switch (lVar.d >= i3) {
                        case true:
                            break;
                        default:
                            cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                            i15 = lVar.d + 1;
                    }
                }
                break;
        }
        objArr[0] = new String(cArr4);
    }
}
