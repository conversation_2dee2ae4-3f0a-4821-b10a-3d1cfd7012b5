package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.AbstractECLookupTable;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECLookupTable;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP384R1Curve.smali */
public class SecP384R1Curve extends ECCurve.AbstractFp {
    protected SecP384R1Point i;
    public static final BigInteger q = SecP384R1FieldElement.Q;
    private static final ECFieldElement[] j = {new SecP384R1FieldElement(ECConstants.ONE)};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP384R1Curve$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ int[] b;

        a(int i, int[] iArr) {
            this.a = i;
            this.b = iArr;
        }

        private ECPoint a(int[] iArr, int[] iArr2) {
            return SecP384R1Curve.this.a(new SecP384R1FieldElement(iArr), new SecP384R1FieldElement(iArr2), SecP384R1Curve.j);
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            int[] a = c6.a(12);
            int[] a2 = c6.a(12);
            int i2 = 0;
            for (int i3 = 0; i3 < this.a; i3++) {
                int i4 = ((i3 ^ i) - 1) >> 31;
                for (int i5 = 0; i5 < 12; i5++) {
                    int i6 = a[i5];
                    int[] iArr = this.b;
                    a[i5] = i6 ^ (iArr[i2 + i5] & i4);
                    a2[i5] = a2[i5] ^ (iArr[(i2 + 12) + i5] & i4);
                }
                i2 += 24;
            }
            return a(a, a2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            int[] a = c6.a(12);
            int[] a2 = c6.a(12);
            int i2 = i * 12 * 2;
            for (int i3 = 0; i3 < 12; i3++) {
                int[] iArr = this.b;
                a[i3] = iArr[i2 + i3];
                a2[i3] = iArr[i2 + 12 + i3];
            }
            return a(a, a2);
        }
    }

    public SecP384R1Curve() {
        super(q);
        this.i = new SecP384R1Point(this, null, null);
        this.b = fromBigInteger(new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC")));
        this.c = fromBigInteger(new BigInteger(1, z4.a("B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF")));
        this.d = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973"));
        this.e = BigInteger.valueOf(1L);
        this.f = 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECCurve a() {
        return new SecP384R1Curve();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        int[] iArr = new int[i2 * 12 * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            c6.b(12, ((SecP384R1FieldElement) eCPoint.getRawXCoord()).a, 0, iArr, i3);
            int i5 = i3 + 12;
            c6.b(12, ((SecP384R1FieldElement) eCPoint.getRawYCoord()).a, 0, iArr, i5);
            i3 = i5 + 12;
        }
        return new a(i2, iArr);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement fromBigInteger(BigInteger bigInteger) {
        return new SecP384R1FieldElement(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public int getFieldSize() {
        return q.bitLength();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECPoint getInfinity() {
        return this.i;
    }

    public BigInteger getQ() {
        return q;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
        int[] a2 = c6.a(12);
        SecP384R1Field.random(secureRandom, a2);
        return new SecP384R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
        int[] a2 = c6.a(12);
        SecP384R1Field.randomMult(secureRandom, a2);
        return new SecP384R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public boolean supportsCoordinateSystem(int i) {
        return i == 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return new SecP384R1Point(this, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        return new SecP384R1Point(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
