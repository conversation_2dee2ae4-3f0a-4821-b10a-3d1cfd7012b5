package fr.antelop.sdk.util;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\util\Address.smali */
public final class Address {
    private final String administrativeArea;
    private final String countryCode;
    private final String line1;
    private final String line2;
    private final String locality;
    private final String postalCode;

    public Address(String str, String str2, String str3, String str4, String str5, String str6) {
        this.line1 = str;
        this.line2 = str2;
        this.administrativeArea = str3;
        this.countryCode = str4;
        this.locality = str5;
        this.postalCode = str6;
    }

    public final String getLine1() {
        return this.line1;
    }

    public final String getLine2() {
        return this.line2;
    }

    public final String getAdministrativeArea() {
        return this.administrativeArea;
    }

    public final String getCountryCode() {
        return this.countryCode;
    }

    public final String getLocality() {
        return this.locality;
    }

    public final String getPostalCode() {
        return this.postalCode;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\util\Address$Builder.smali */
    public static class Builder {
        private String administrativeArea;
        private String countryCode;
        private String line1;
        private String line2;
        private String locality;
        private String postalCode;

        public Builder setLine1(String str) {
            this.line1 = str;
            return this;
        }

        public Builder setLine2(String str) {
            this.line2 = str;
            return this;
        }

        public Builder setAdministrativeArea(String str) {
            this.administrativeArea = str;
            return this;
        }

        public Builder setCountryCode(String str) {
            this.countryCode = str;
            return this;
        }

        public Builder setLocality(String str) {
            this.locality = str;
            return this;
        }

        public Builder setPostalCode(String str) {
            this.postalCode = str;
            return this;
        }

        public Address build() {
            return new Address(this.line1, this.line2, this.administrativeArea, this.countryCode, this.locality, this.postalCode);
        }
    }
}
