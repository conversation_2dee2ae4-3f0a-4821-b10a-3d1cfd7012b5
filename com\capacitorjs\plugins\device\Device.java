package com.capacitorjs.plugins.device;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;
import android.provider.Settings;
import android.webkit.WebView;
import androidx.core.app.NotificationCompat;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\capacitorjs\plugins\device\Device.smali */
public class Device {
    private Context context;

    Device(Context context) {
        this.context = context;
    }

    public long getMemUsed() {
        Runtime runtime = Runtime.getRuntime();
        long usedMem = runtime.totalMemory() - runtime.freeMemory();
        return usedMem;
    }

    public long getDiskFree() {
        StatFs statFs = new StatFs(Environment.getRootDirectory().getAbsolutePath());
        return statFs.getAvailableBlocksLong() * statFs.getBlockSizeLong();
    }

    public long getDiskTotal() {
        StatFs statFs = new StatFs(Environment.getRootDirectory().getAbsolutePath());
        return statFs.getBlockCountLong() * statFs.getBlockSizeLong();
    }

    public long getRealDiskFree() {
        StatFs statFs = new StatFs(Environment.getDataDirectory().getAbsolutePath());
        return statFs.getAvailableBlocksLong() * statFs.getBlockSizeLong();
    }

    public long getRealDiskTotal() {
        StatFs statFs = new StatFs(Environment.getDataDirectory().getAbsolutePath());
        return statFs.getBlockCountLong() * statFs.getBlockSizeLong();
    }

    public String getPlatform() {
        return "android";
    }

    public String getUuid() {
        return Settings.Secure.getString(this.context.getContentResolver(), "android_id");
    }

    public float getBatteryLevel() {
        IntentFilter ifilter = new IntentFilter("android.intent.action.BATTERY_CHANGED");
        Intent batteryStatus = this.context.registerReceiver(null, ifilter);
        int level = -1;
        int scale = -1;
        if (batteryStatus != null) {
            level = batteryStatus.getIntExtra("level", -1);
            scale = batteryStatus.getIntExtra("scale", -1);
        }
        return level / scale;
    }

    public boolean isCharging() {
        IntentFilter ifilter = new IntentFilter("android.intent.action.BATTERY_CHANGED");
        Intent batteryStatus = this.context.registerReceiver(null, ifilter);
        if (batteryStatus == null) {
            return false;
        }
        int status = batteryStatus.getIntExtra(NotificationCompat.CATEGORY_STATUS, -1);
        return status == 2 || status == 5;
    }

    public boolean isVirtual() {
        return Build.FINGERPRINT.contains("generic") || Build.PRODUCT.contains("sdk");
    }

    public String getName() {
        return Settings.Global.getString(this.context.getContentResolver(), "device_name");
    }

    public String getWebViewVersion() {
        PackageInfo info = WebView.getCurrentWebViewPackage();
        if (info != null) {
            return info.versionName;
        }
        return Build.VERSION.RELEASE;
    }

    private PackageInfo getWebViewVersionSubAndroid26() throws PackageManager.NameNotFoundException {
        PackageManager pm = this.context.getPackageManager();
        return pm.getPackageInfo("com.android.chrome", 0);
    }
}
