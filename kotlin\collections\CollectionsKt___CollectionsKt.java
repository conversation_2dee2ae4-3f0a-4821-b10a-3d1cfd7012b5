package kotlin.collections;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.RandomAccess;
import java.util.Set;
import kotlin.Deprecated;
import kotlin.DeprecatedSinceKotlin;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.ReplaceWith;
import kotlin.TuplesKt;
import kotlin.UInt;
import kotlin.ULong;
import kotlin.Unit;
import kotlin.comparisons.ComparisonsKt;
import kotlin.comparisons.ComparisonsKt__ComparisonsKt;
import kotlin.comparisons.ComparisonsKt__ComparisonsKt$compareByDescending$1;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;
import kotlin.jvm.internal.Intrinsics;
import kotlin.random.Random;
import kotlin.ranges.IntRange;
import kotlin.ranges.RangesKt;
import kotlin.sequences.Sequence;
import kotlin.sequences.SequencesKt;
import kotlin.text.StringsKt;

/* JADX INFO: Access modifiers changed from: package-private */
/* compiled from: _Collections.kt */
@Metadata(d1 = {"\u0000ê\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u001c\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010%\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\u0010\u0005\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\n\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u001e\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u001f\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\b\u001d\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\"\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\r\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000f\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u0011\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0018\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u0019\n\u0002\u0010\f\n\u0002\b\u0002\n\u0002\u0010\u0013\n\u0000\n\u0002\u0010\u0014\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0015\n\u0002\b\u0002\n\u0002\u0010\u0016\n\u0002\b\u0002\n\u0002\u0010#\n\u0002\b\u0002\n\u0002\u0010\u0017\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a0\u0010\u0000\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u0016\u0010\u0006\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a0\u0010\u0006\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u001f\u0010\u0007\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0087\b\u001a\u001c\u0010\b\u001a\b\u0012\u0004\u0012\u0002H\u00020\t\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aT\u0010\n\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001e\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u0002H\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000f0\u0005H\u0086\bø\u0001\u0000\u001aB\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\u00020\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000\u001a\\\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000\u001a]\u0010\u0013\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0018\b\u0002\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\u00020\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001aw\u0010\u0013\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u0018\b\u0003\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001ao\u0010\u0019\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u0018\b\u0003\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u001e\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u0002H\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001aB\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\f\"\u0004\b\u0001\u0010\r*\b\u0012\u0004\u0012\u0002H\f0\u00032\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u0005H\u0087\bø\u0001\u0000\u001a]\u0010\u001c\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\f\"\u0004\b\u0001\u0010\r\"\u0018\b\u0002\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\f0\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001f0\u0003H\u0007¢\u0006\u0002\b \u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0002\b!\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0002\b#\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020$0\u0003H\u0007¢\u0006\u0002\b%\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020&0\u0003H\u0007¢\u0006\u0002\b'\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020(0\u0003H\u0007¢\u0006\u0002\b)\u001a,\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$H\u0007\u001aF\u0010*\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\u0004\u0012\u0002H-0\u0005H\u0007\u001a\u001e\u0010.\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+H\u0087\n¢\u0006\u0002\u0010/\u001a\u001e\u00100\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+H\u0087\n¢\u0006\u0002\u0010/\u001a\u001e\u00101\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+H\u0087\n¢\u0006\u0002\u0010/\u001a\u001e\u00102\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+H\u0087\n¢\u0006\u0002\u0010/\u001a\u001e\u00103\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+H\u0087\n¢\u0006\u0002\u0010/\u001a+\u00104\u001a\u00020\u0001\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b5*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0002\u00107\u001a\u0019\u00108\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u000209H\u0087\b\u001a\u0016\u00108\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a0\u00108\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u001c\u0010:\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a<\u0010;\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000\u001a$\u0010=\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010>\u001a\u00020$\u001a$\u0010?\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010>\u001a\u00020$\u001a6\u0010@\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a6\u0010A\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a#\u0010B\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010C\u001a\u00020$¢\u0006\u0002\u0010D\u001a&\u0010B\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010C\u001a\u00020$H\u0087\b¢\u0006\u0002\u0010E\u001a7\u0010F\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010C\u001a\u00020$2\u0012\u0010G\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u0002H\u00020\u0005¢\u0006\u0002\u0010H\u001a=\u0010F\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010C\u001a\u00020$2\u0012\u0010G\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u0002H\u00020\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010I\u001a%\u0010J\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010C\u001a\u00020$¢\u0006\u0002\u0010D\u001a(\u0010J\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010C\u001a\u00020$H\u0087\b¢\u0006\u0002\u0010E\u001a6\u0010K\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001aK\u0010L\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010\u0004\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010MH\u0086\bø\u0001\u0000\u001ad\u0010P\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2'\u0010\u0004\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010MH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010S\u001a$\u0010T\u001a\r\u0012\t\u0012\u0007H-¢\u0006\u0002\bU0+\"\u0006\b\u0000\u0010-\u0018\u0001*\u0006\u0012\u0002\b\u00030\u0003H\u0086\b\u001a8\u0010V\u001a\u0002HQ\"\u0006\b\u0000\u0010-\u0018\u0001\"\u0010\b\u0001\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\u0006\u0012\u0002\b\u00030\u00032\u0006\u0010\u0016\u001a\u0002HQH\u0086\b¢\u0006\u0002\u0010W\u001a6\u0010X\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\"\u0010Y\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\b\b\u0000\u0010\u0002*\u00020Z*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u0003\u001a;\u0010[\u001a\u0002HQ\"\u0010\b\u0000\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020R\"\b\b\u0001\u0010\u0002*\u00020Z*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ¢\u0006\u0002\u0010W\u001aO\u0010\\\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010]\u001aO\u0010^\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010]\u001a7\u0010_\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a7\u0010a\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a7\u0010a\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010b\u001a\u001b\u0010c\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a5\u0010c\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001b\u0010c\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001aA\u0010e\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001aC\u0010f\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001d\u0010g\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a7\u0010g\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001d\u0010g\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001aB\u0010h\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030\u0005H\u0086\bø\u0001\u0000\u001aG\u0010h\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\t0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\bi\u001a\\\u0010j\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030MH\u0087\bø\u0001\u0000¢\u0006\u0002\bk\u001a\\\u0010j\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\t0MH\u0087\bø\u0001\u0000¢\u0006\u0002\bl\u001ar\u0010m\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030MH\u0087\bø\u0001\u0000¢\u0006\u0004\bn\u0010S\u001ar\u0010m\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\t0MH\u0087\bø\u0001\u0000¢\u0006\u0004\bo\u0010S\u001a[\u0010p\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010]\u001a]\u0010p\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\t0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0004\bq\u0010]\u001aX\u0010r\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2'\u0010t\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0MH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010v\u001am\u0010w\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2<\u0010t\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0xH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010y\u001aX\u0010z\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010s\u001a\u0002H-2'\u0010t\u001a#\u0012\u0004\u0012\u0002H\u0002\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H-0MH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010{\u001am\u0010|\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010s\u001a\u0002H-2<\u0010t\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H-0xH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010}\u001a1\u0010~\u001a\u00020\u007f\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0013\u0010\u0080\u0001\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u007f0\u0005H\u0087\bø\u0001\u0000\u001aG\u0010\u0081\u0001\u001a\u00020\u007f\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032(\u0010\u0080\u0001\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u007f0MH\u0086\bø\u0001\u0000\u001a>\u0010\u0082\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010C\u001a\u00020$2\u0012\u0010G\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u0002H\u00020\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010I\u001a&\u0010\u0083\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010C\u001a\u00020$¢\u0006\u0002\u0010E\u001aI\u0010\u0084\u0001\u001a\u0014\u0012\u0004\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000\u001ac\u0010\u0084\u0001\u001a\u0014\u0012\u0004\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\r0+0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000\u001ac\u0010\u0085\u0001\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u001d\b\u0002\u0010\u0014*\u0017\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u000b\u0012\t\u0012\u0004\u0012\u0002H\u00020\u0086\u00010\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001a}\u0010\u0085\u0001\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u001d\b\u0003\u0010\u0014*\u0017\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u000b\u0012\t\u0012\u0004\u0012\u0002H\r0\u0086\u00010\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001aF\u0010\u0087\u0001\u001a\u000f\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0088\u0001\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\b\u0004\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0087\bø\u0001\u0000\u001a*\u0010\u0089\u0001\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b5*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002¢\u0006\u0003\u0010\u008a\u0001\u001a*\u0010\u0089\u0001\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b5*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u00106\u001a\u0002H\u0002¢\u0006\u0003\u0010\u008b\u0001\u001a1\u0010\u008c\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a1\u0010\u008c\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a1\u0010\u008d\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a1\u0010\u008d\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a0\u0010\u008e\u0001\u001a\t\u0012\u0004\u0012\u0002H\u00020\u008f\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010\u0090\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0004\u001a\u008d\u0001\u0010\u0091\u0001\u001a\u0003H\u0092\u0001\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010\u0092\u0001*\b0\u0093\u0001j\u0003`\u0094\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\b\u0010\u0095\u0001\u001a\u0003H\u0092\u00012\n\b\u0002\u0010\u0096\u0001\u001a\u00030\u0097\u00012\n\b\u0002\u0010\u0098\u0001\u001a\u00030\u0097\u00012\n\b\u0002\u0010\u0099\u0001\u001a\u00030\u0097\u00012\t\b\u0002\u0010\u009a\u0001\u001a\u00020$2\n\b\u0002\u0010\u009b\u0001\u001a\u00030\u0097\u00012\u0017\b\u0002\u0010\u000e\u001a\u0011\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0097\u0001\u0018\u00010\u0005¢\u0006\u0003\u0010\u009c\u0001\u001al\u0010\u009d\u0001\u001a\u00030\u009e\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\n\b\u0002\u0010\u0096\u0001\u001a\u00030\u0097\u00012\n\b\u0002\u0010\u0098\u0001\u001a\u00030\u0097\u00012\n\b\u0002\u0010\u0099\u0001\u001a\u00030\u0097\u00012\t\b\u0002\u0010\u009a\u0001\u001a\u00020$2\n\b\u0002\u0010\u009b\u0001\u001a\u00030\u0097\u00012\u0017\b\u0002\u0010\u000e\u001a\u0011\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0097\u0001\u0018\u00010\u0005\u001a\u001c\u0010\u009f\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a6\u0010\u009f\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001c\u0010\u009f\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001a6\u0010\u009f\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010b\u001a*\u0010 \u0001\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b5*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002¢\u0006\u0003\u0010\u008a\u0001\u001a*\u0010 \u0001\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b5*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u00106\u001a\u0002H\u0002¢\u0006\u0003\u0010\u008b\u0001\u001a\u001e\u0010¡\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a8\u0010¡\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001e\u0010¡\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001a8\u0010¡\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010b\u001a=\u0010¢\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0086\bø\u0001\u0000\u001aR\u0010£\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010\u000e\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0MH\u0086\bø\u0001\u0000\u001aX\u0010¤\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010\u000e\u001a%\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0MH\u0086\bø\u0001\u0000\u001aq\u0010¥\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2)\u0010\u000e\u001a%\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0MH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010S\u001ak\u0010¦\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2'\u0010\u000e\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0MH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010S\u001aC\u0010§\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001a\\\u0010¨\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020Z\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010]\u001aV\u0010©\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H-0R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010]\u001a-\u0010ª\u0001\u001a\u0002H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0006\b¬\u0001\u0010\u00ad\u0001\u001a\u0019\u0010ª\u0001\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\b¬\u0001\u001a\u0019\u0010ª\u0001\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\b¬\u0001\u001aJ\u0010®\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0005\b¯\u0001\u0010`\u001aI\u0010°\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001aH\u0010±\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010²\u0001\u001a1\u0010±\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010±\u0001\u001a\u00020\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000\u001aJ\u0010³\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010²\u0001\u001a9\u0010³\u0001\u001a\u0004\u0018\u00010\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010´\u0001\u001a9\u0010³\u0001\u001a\u0004\u0018\u00010\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010µ\u0001\u001a\\\u0010¶\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`¹\u00012\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010º\u0001\u001a^\u0010»\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`¹\u00012\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010º\u0001\u001a,\u0010¼\u0001\u001a\u0004\u0018\u0001H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0003\u0010\u00ad\u0001\u001a\u001b\u0010¼\u0001\u001a\u0004\u0018\u00010\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\u0010½\u0001\u001a\u001b\u0010¼\u0001\u001a\u0004\u0018\u00010\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\u0010¾\u0001\u001aA\u0010¿\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`¹\u0001H\u0007¢\u0006\u0006\bÀ\u0001\u0010Á\u0001\u001a@\u0010Â\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`¹\u0001H\u0007¢\u0006\u0003\u0010Á\u0001\u001a-\u0010Ã\u0001\u001a\u0002H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0006\bÄ\u0001\u0010\u00ad\u0001\u001a\u0019\u0010Ã\u0001\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\bÄ\u0001\u001a\u0019\u0010Ã\u0001\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\bÄ\u0001\u001aJ\u0010Å\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0005\bÆ\u0001\u0010`\u001aI\u0010Ç\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010`\u001aH\u0010È\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010²\u0001\u001a1\u0010È\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010È\u0001\u001a\u00020\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000\u001aJ\u0010É\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010²\u0001\u001a9\u0010É\u0001\u001a\u0004\u0018\u00010\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010´\u0001\u001a9\u0010É\u0001\u001a\u0004\u0018\u00010\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010µ\u0001\u001a\\\u0010Ê\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`¹\u00012\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010º\u0001\u001a^\u0010Ë\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`¹\u00012\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010º\u0001\u001a,\u0010Ì\u0001\u001a\u0004\u0018\u0001H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0003\u0010\u00ad\u0001\u001a\u001b\u0010Ì\u0001\u001a\u0004\u0018\u00010\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\u0010½\u0001\u001a\u001b\u0010Ì\u0001\u001a\u0004\u0018\u00010\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\u0010¾\u0001\u001aA\u0010Í\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`¹\u0001H\u0007¢\u0006\u0006\bÎ\u0001\u0010Á\u0001\u001a@\u0010Ï\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`¹\u0001H\u0007¢\u0006\u0003\u0010Á\u0001\u001a.\u0010Ð\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0003\u0010Ñ\u0001\u001a8\u0010Ð\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010Ò\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H\u00020Ó\u0001H\u0086\u0002¢\u0006\u0003\u0010Ô\u0001\u001a/\u0010Ð\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0002\u001a/\u0010Ð\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\tH\u0086\u0002\u001a.\u0010Õ\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002H\u0087\b¢\u0006\u0003\u0010Ñ\u0001\u001a\u0017\u0010Ö\u0001\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a1\u0010Ö\u0001\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001aB\u0010×\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u000e\b\u0001\u0010Q*\b\u0012\u0004\u0012\u0002H\u00020\u0003*\u0002HQ2\u0013\u0010\u0080\u0001\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u007f0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010Ø\u0001\u001aW\u0010Ù\u0001\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u000e\b\u0001\u0010Q*\b\u0012\u0004\u0012\u0002H\u00020\u0003*\u0002HQ2(\u0010\u0080\u0001\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u007f0MH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010Ú\u0001\u001aI\u0010Û\u0001\u001a\u001a\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u000f\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a.\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\u0006\u00106\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0003\u0010Ý\u0001\u001a8\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\u0010\u0010Ò\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H\u00020Ó\u0001H\u0086\u0002¢\u0006\u0003\u0010Þ\u0001\u001a/\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0002\u001a/\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\tH\u0086\u0002\u001a.\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0003\u0010Ñ\u0001\u001a8\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010Ò\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H\u00020Ó\u0001H\u0086\u0002¢\u0006\u0003\u0010Ô\u0001\u001a/\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0002\u001a/\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\tH\u0086\u0002\u001a.\u0010ß\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\u0006\u00106\u001a\u0002H\u0002H\u0087\b¢\u0006\u0003\u0010Ý\u0001\u001a.\u0010ß\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00106\u001a\u0002H\u0002H\u0087\b¢\u0006\u0003\u0010Ñ\u0001\u001a \u0010à\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u000209H\u0087\b¢\u0006\u0003\u0010á\u0001\u001a)\u0010à\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\b\u0010à\u0001\u001a\u00030â\u0001H\u0007¢\u0006\u0003\u0010ã\u0001\u001a\"\u0010ä\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u000209H\u0087\b¢\u0006\u0003\u0010á\u0001\u001a+\u0010ä\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u0002092\b\u0010à\u0001\u001a\u00030â\u0001H\u0007¢\u0006\u0003\u0010ã\u0001\u001a[\u0010å\u0001\u001a\u0003Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010t\u001a%\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010MH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010ç\u0001\u001ap\u0010è\u0001\u001a\u0003Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010t\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010xH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010é\u0001\u001ar\u0010ê\u0001\u001a\u0005\u0018\u0001Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010t\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010xH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010é\u0001\u001a]\u0010ë\u0001\u001a\u0005\u0018\u0001Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010t\u001a%\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010MH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010ç\u0001\u001a[\u0010ì\u0001\u001a\u0003Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020+2)\u0010t\u001a%\u0012\u0004\u0012\u0002H\u0002\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0005\u0012\u0003Hæ\u00010MH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010í\u0001\u001ap\u0010î\u0001\u001a\u0003Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020+2>\u0010t\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0005\u0012\u0003Hæ\u00010xH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010ï\u0001\u001ar\u0010ð\u0001\u001a\u0005\u0018\u0001Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020+2>\u0010t\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0004\u0012\u0002H\u0002\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0005\u0012\u0003Hæ\u00010xH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010ï\u0001\u001a]\u0010ñ\u0001\u001a\u0005\u0018\u0001Hæ\u0001\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020+2)\u0010t\u001a%\u0012\u0004\u0012\u0002H\u0002\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0005\u0012\u0003Hæ\u00010MH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010í\u0001\u001a#\u0010ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\b\b\u0000\u0010\u0002*\u00020Z*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u0003\u001a#\u0010ò\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\b\b\u0000\u0010\u0002*\u00020Z*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020+\u001a\u001d\u0010ó\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a`\u0010ô\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2'\u0010t\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0MH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010õ\u0001\u001au\u0010ö\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2<\u0010t\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0xH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010÷\u0001\u001a[\u0010ø\u0001\u001a\t\u0012\u0005\u0012\u0003Hæ\u00010+\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010t\u001a%\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010MH\u0087\bø\u0001\u0000\u001ap\u0010ù\u0001\u001a\t\u0012\u0005\u0012\u0003Hæ\u00010+\"\u0005\b\u0000\u0010æ\u0001\"\t\b\u0001\u0010\u0002*\u0003Hæ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010t\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0014\u0012\u0012Hæ\u0001¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003Hæ\u00010xH\u0087\bø\u0001\u0000\u001a`\u0010ú\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2'\u0010t\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0MH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010õ\u0001\u001au\u0010û\u0001\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010s\u001a\u0002H-2<\u0010t\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(C\u0012\u0013\u0012\u0011H-¢\u0006\f\bN\u0012\b\bO\u0012\u0004\b\b(u\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0xH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010÷\u0001\u001a$\u0010ü\u0001\u001a\u00020\u007f\"\u0004\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0086\u00012\b\u0010à\u0001\u001a\u00030â\u0001H\u0007\u001a\u001c\u0010ý\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a6\u0010ý\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001c\u0010ý\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001a\u001e\u0010þ\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010d\u001a8\u0010þ\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010`\u001a\u001e\u0010þ\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+¢\u0006\u0002\u0010/\u001a,\u0010ÿ\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\r\u0010\u0080\u0002\u001a\b\u0012\u0004\u0012\u00020$0\u0003\u001a'\u0010ÿ\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\b\u0010\u0080\u0002\u001a\u00030\u0081\u0002\u001aG\u0010\u0082\u0002\u001a\u00020\u007f\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\t\u0012\u0004\u0012\u0002H\u00020\u0086\u00012\u0016\b\u0004\u0010<\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001aG\u0010\u0083\u0002\u001a\u00020\u007f\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\t\u0012\u0004\u0012\u0002H\u00020\u0086\u00012\u0016\b\u0004\u0010<\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001a#\u0010\u0084\u0002\u001a\u00020\u007f\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\t\u0012\u0004\u0012\u0002H\u00020\u0086\u0001\u001a(\u0010\u0085\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aL\u0010\u0086\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0016\b\u0004\u0010<\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001aL\u0010\u0087\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0016\b\u0004\u0010<\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001a(\u0010\u0088\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020«\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a<\u0010\u0089\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010·\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¸\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`¹\u0001\u001a0\u0010\u008a\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020\u008f\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010\u0090\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0004\u001a\u0019\u0010\u008b\u0002\u001a\u00020$*\b\u0012\u0004\u0012\u00020\u001f0\u0003H\u0007¢\u0006\u0003\b\u008c\u0002\u001a\u0019\u0010\u008b\u0002\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\b\u008d\u0002\u001a\u0019\u0010\u008b\u0002\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\b\u008e\u0002\u001a\u0019\u0010\u008b\u0002\u001a\u00020$*\b\u0012\u0004\u0012\u00020$0\u0003H\u0007¢\u0006\u0003\b\u008f\u0002\u001a\u0019\u0010\u008b\u0002\u001a\u00020&*\b\u0012\u0004\u0012\u00020&0\u0003H\u0007¢\u0006\u0003\b\u0090\u0002\u001a\u0019\u0010\u008b\u0002\u001a\u00020$*\b\u0012\u0004\u0012\u00020(0\u0003H\u0007¢\u0006\u0003\b\u0091\u0002\u001a1\u0010\u0092\u0002\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020$0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010\u0093\u0002\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a7\u0010\u0094\u0002\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\b\u008d\u0002\u001a7\u0010\u0094\u0002\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020$0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\b\u008f\u0002\u001a7\u0010\u0094\u0002\u001a\u00020&\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010<\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020&0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\b\u0090\u0002\u001a?\u0010\u0094\u0002\u001a\u00030\u0095\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0013\u0010<\u001a\u000f\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0095\u00020\u0005H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\b\u0096\u0002\u0010\u0097\u0002\u001a?\u0010\u0094\u0002\u001a\u00030\u0098\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0013\u0010<\u001a\u000f\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0098\u00020\u0005H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\b\u0099\u0002\u0010\u009a\u0002\u001a%\u0010\u009b\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010>\u001a\u00020$\u001a%\u0010\u009c\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0006\u0010>\u001a\u00020$\u001a7\u0010\u009d\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020+2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a7\u0010\u009e\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u0012\u0010\u009f\u0002\u001a\u00030 \u0002*\b\u0012\u0004\u0012\u00020\u000109\u001a\u0012\u0010¡\u0002\u001a\u00030¢\u0002*\b\u0012\u0004\u0012\u00020\u001f09\u001a\u0013\u0010£\u0002\u001a\u00030¤\u0002*\t\u0012\u0005\u0012\u00030¥\u000209\u001a6\u0010¦\u0002\u001a\u0002HQ\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010Q*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020R*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HQ¢\u0006\u0002\u0010W\u001a\u0012\u0010§\u0002\u001a\u00030¨\u0002*\b\u0012\u0004\u0012\u00020\u001e09\u001a\u0012\u0010©\u0002\u001a\u00030ª\u0002*\b\u0012\u0004\u0012\u00020\"09\u001a)\u0010«\u0002\u001a\u0014\u0012\u0004\u0012\u0002H\u00020¬\u0002j\t\u0012\u0004\u0012\u0002H\u0002`\u00ad\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u0012\u0010®\u0002\u001a\u00030¯\u0002*\b\u0012\u0004\u0012\u00020$09\u001a\u001d\u0010°\u0002\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u0012\u0010±\u0002\u001a\u00030²\u0002*\b\u0012\u0004\u0012\u00020&09\u001a\u001e\u0010³\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020\u0086\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u000209\u001a\u001e\u0010³\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020\u0086\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001e\u0010´\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020µ\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001e\u0010¶\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020\u008f\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u0012\u0010·\u0002\u001a\u00030¸\u0002*\b\u0012\u0004\u0012\u00020(09\u001a0\u0010¹\u0002\u001a\t\u0012\u0004\u0012\u0002H\u00020\u008f\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010\u0090\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0004\u001aC\u0010º\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\t\b\u0002\u0010»\u0002\u001a\u00020$2\t\b\u0002\u0010¼\u0002\u001a\u00020\u0001H\u0007\u001a]\u0010º\u0002\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\t\b\u0002\u0010»\u0002\u001a\u00020$2\t\b\u0002\u0010¼\u0002\u001a\u00020\u00012\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\u0004\u0012\u0002H-0\u0005H\u0007\u001a$\u0010½\u0002\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u0002H\u00020¾\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aJ\u0010¿\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u000f0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010\u0090\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H-0Ó\u0001H\u0086\u0004¢\u0006\u0003\u0010Ô\u0001\u001a\u0081\u0001\u0010¿\u0002\u001a\b\u0012\u0004\u0012\u0002H\r0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010\u0090\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H-0Ó\u000128\u0010\u000e\u001a4\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(À\u0002\u0012\u0014\u0012\u0012H-¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(Á\u0002\u0012\u0004\u0012\u0002H\r0MH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010Â\u0002\u001aA\u0010¿\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u000f0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010\u0090\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003H\u0086\u0004\u001ax\u0010¿\u0002\u001a\b\u0012\u0004\u0012\u0002H\r0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010\u0090\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u000328\u0010\u000e\u001a4\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(À\u0002\u0012\u0014\u0012\u0012H-¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(Á\u0002\u0012\u0004\u0012\u0002H\r0MH\u0086\bø\u0001\u0000\u001a+\u0010Ã\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\u00020\u000f0+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007\u001ac\u0010Ã\u0002\u001a\b\u0012\u0004\u0012\u0002H-0+\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u000328\u0010\u000e\u001a4\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(À\u0002\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bN\u0012\t\bO\u0012\u0005\b\b(Á\u0002\u0012\u0004\u0012\u0002H-0MH\u0087\bø\u0001\u0000\u0082\u0002\u000b\n\u0005\b\u009920\u0001\n\u0002\b\u0019¨\u0006Ä\u0002"}, d2 = {"all", "", "T", "", "predicate", "Lkotlin/Function1;", "any", "asIterable", "asSequence", "Lkotlin/sequences/Sequence;", "associate", "", "K", "V", "transform", "Lkotlin/Pair;", "associateBy", "keySelector", "valueTransform", "associateByTo", "M", "", "destination", "(Ljava/lang/Iterable;Ljava/util/Map;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "(Ljava/lang/Iterable;Ljava/util/Map;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "associateTo", "associateWith", "valueSelector", "associateWithTo", "average", "", "", "averageOfByte", "averageOfDouble", "", "averageOfFloat", "", "averageOfInt", "", "averageOfLong", "", "averageOfShort", "chunked", "", "size", "R", "component1", "(Ljava/util/List;)Ljava/lang/Object;", "component2", "component3", "component4", "component5", "contains", "Lkotlin/internal/OnlyInputTypes;", "element", "(Ljava/lang/Iterable;Ljava/lang/Object;)Z", "count", "", "distinct", "distinctBy", "selector", "drop", "n", "dropLast", "dropLastWhile", "dropWhile", "elementAt", "index", "(Ljava/lang/Iterable;I)Ljava/lang/Object;", "(Ljava/util/List;I)Ljava/lang/Object;", "elementAtOrElse", "defaultValue", "(Ljava/lang/Iterable;ILkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "(Ljava/util/List;ILkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "elementAtOrNull", "filter", "filterIndexed", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "filterIndexedTo", "C", "", "(Ljava/lang/Iterable;Ljava/util/Collection;Lkotlin/jvm/functions/Function2;)Ljava/util/Collection;", "filterIsInstance", "Lkotlin/internal/NoInfer;", "filterIsInstanceTo", "(Ljava/lang/Iterable;Ljava/util/Collection;)Ljava/util/Collection;", "filterNot", "filterNotNull", "", "filterNotNullTo", "filterNotTo", "(Ljava/lang/Iterable;Ljava/util/Collection;Lkotlin/jvm/functions/Function1;)Ljava/util/Collection;", "filterTo", "find", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "findLast", "(Ljava/util/List;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "first", "(Ljava/lang/Iterable;)Ljava/lang/Object;", "firstNotNullOf", "firstNotNullOfOrNull", "firstOrNull", "flatMap", "flatMapSequence", "flatMapIndexed", "flatMapIndexedIterable", "flatMapIndexedSequence", "flatMapIndexedTo", "flatMapIndexedIterableTo", "flatMapIndexedSequenceTo", "flatMapTo", "flatMapSequenceTo", "fold", "initial", "operation", "acc", "(Ljava/lang/Iterable;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "foldIndexed", "Lkotlin/Function3;", "(Ljava/lang/Iterable;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "foldRight", "(Ljava/util/List;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "foldRightIndexed", "(Ljava/util/List;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "forEach", "", "action", "forEachIndexed", "getOrElse", "getOrNull", "groupBy", "groupByTo", "", "groupingBy", "Lkotlin/collections/Grouping;", "indexOf", "(Ljava/lang/Iterable;Ljava/lang/Object;)I", "(Ljava/util/List;Ljava/lang/Object;)I", "indexOfFirst", "indexOfLast", "intersect", "", "other", "joinTo", "A", "Ljava/lang/Appendable;", "Lkotlin/text/Appendable;", "buffer", "separator", "", "prefix", "postfix", "limit", "truncated", "(Ljava/lang/Iterable;Ljava/lang/Appendable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Appendable;", "joinToString", "", "last", "lastIndexOf", "lastOrNull", "map", "mapIndexed", "mapIndexedNotNull", "mapIndexedNotNullTo", "mapIndexedTo", "mapNotNull", "mapNotNullTo", "mapTo", "max", "", "maxOrThrow", "(Ljava/lang/Iterable;)Ljava/lang/Comparable;", "maxBy", "maxByOrThrow", "maxByOrNull", "maxOf", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Comparable;", "maxOfOrNull", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Double;", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Float;", "maxOfWith", "comparator", "Ljava/util/Comparator;", "Lkotlin/Comparator;", "(Ljava/lang/Iterable;Ljava/util/Comparator;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "maxOfWithOrNull", "maxOrNull", "(Ljava/lang/Iterable;)Ljava/lang/Double;", "(Ljava/lang/Iterable;)Ljava/lang/Float;", "maxWith", "maxWithOrThrow", "(Ljava/lang/Iterable;Ljava/util/Comparator;)Ljava/lang/Object;", "maxWithOrNull", "min", "minOrThrow", "minBy", "minByOrThrow", "minByOrNull", "minOf", "minOfOrNull", "minOfWith", "minOfWithOrNull", "minOrNull", "minWith", "minWithOrThrow", "minWithOrNull", "minus", "(Ljava/lang/Iterable;Ljava/lang/Object;)Ljava/util/List;", "elements", "", "(Ljava/lang/Iterable;[Ljava/lang/Object;)Ljava/util/List;", "minusElement", "none", "onEach", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Iterable;", "onEachIndexed", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Ljava/lang/Iterable;", "partition", "plus", "(Ljava/util/Collection;Ljava/lang/Object;)Ljava/util/List;", "(Ljava/util/Collection;[Ljava/lang/Object;)Ljava/util/List;", "plusElement", "random", "(Ljava/util/Collection;)Ljava/lang/Object;", "Lkotlin/random/Random;", "(Ljava/util/Collection;Lkotlin/random/Random;)Ljava/lang/Object;", "randomOrNull", "reduce", "S", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "reduceIndexed", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "reduceIndexedOrNull", "reduceOrNull", "reduceRight", "(Ljava/util/List;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "reduceRightIndexed", "(Ljava/util/List;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "reduceRightIndexedOrNull", "reduceRightOrNull", "requireNoNulls", "reversed", "runningFold", "(Ljava/lang/Iterable;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/util/List;", "runningFoldIndexed", "(Ljava/lang/Iterable;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/util/List;", "runningReduce", "runningReduceIndexed", "scan", "scanIndexed", "shuffle", "single", "singleOrNull", "slice", "indices", "Lkotlin/ranges/IntRange;", "sortBy", "sortByDescending", "sortDescending", "sorted", "sortedBy", "sortedByDescending", "sortedDescending", "sortedWith", "subtract", "sum", "sumOfByte", "sumOfDouble", "sumOfFloat", "sumOfInt", "sumOfLong", "sumOfShort", "sumBy", "sumByDouble", "sumOf", "Lkotlin/UInt;", "sumOfUInt", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)I", "Lkotlin/ULong;", "sumOfULong", "(Ljava/lang/Iterable;Lkotlin/jvm/functions/Function1;)J", "take", "takeLast", "takeLastWhile", "takeWhile", "toBooleanArray", "", "toByteArray", "", "toCharArray", "", "", "toCollection", "toDoubleArray", "", "toFloatArray", "", "toHashSet", "Ljava/util/HashSet;", "Lkotlin/collections/HashSet;", "toIntArray", "", "toList", "toLongArray", "", "toMutableList", "toMutableSet", "", "toSet", "toShortArray", "", "union", "windowed", "step", "partialWindows", "withIndex", "Lkotlin/collections/IndexedValue;", "zip", "a", "b", "(Ljava/lang/Iterable;[Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/util/List;", "zipWithNext", "kotlin-stdlib"}, k = 5, mv = {1, 9, 0}, xi = 49, xs = "kotlin/collections/CollectionsKt")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\collections\CollectionsKt___CollectionsKt.smali */
public class CollectionsKt___CollectionsKt extends CollectionsKt___CollectionsJvmKt {
    private static final <T> T component1(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(0);
    }

    private static final <T> T component2(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(1);
    }

    private static final <T> T component3(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(2);
    }

    private static final <T> T component4(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(3);
    }

    private static final <T> T component5(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(4);
    }

    public static final <T> boolean contains(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            return ((Collection) iterable).contains(t);
        }
        return CollectionsKt.indexOf(iterable, t) >= 0;
    }

    public static final <T> T elementAt(Iterable<? extends T> iterable, final int i) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return (T) ((List) iterable).get(i);
        }
        return (T) CollectionsKt.elementAtOrElse(iterable, i, new Function1<Integer, T>() { // from class: kotlin.collections.CollectionsKt___CollectionsKt$elementAt$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(1);
            }

            public final T invoke(int it) {
                throw new IndexOutOfBoundsException("Collection doesn't contain element at index " + i + '.');
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Object invoke(Integer num) {
                return invoke(num.intValue());
            }
        });
    }

    private static final <T> T elementAt(List<? extends T> list, int index) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.get(index);
    }

    public static final <T> T elementAtOrElse(Iterable<? extends T> iterable, int i, Function1<? super Integer, ? extends T> defaultValue) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        if (iterable instanceof List) {
            List list = (List) iterable;
            return (i < 0 || i > CollectionsKt.getLastIndex(list)) ? defaultValue.invoke(Integer.valueOf(i)) : (T) list.get(i);
        }
        if (i < 0) {
            return defaultValue.invoke(Integer.valueOf(i));
        }
        int i2 = 0;
        for (T t : iterable) {
            int i3 = i2 + 1;
            if (i != i2) {
                i2 = i3;
            } else {
                return t;
            }
        }
        return defaultValue.invoke(Integer.valueOf(i));
    }

    private static final <T> T elementAtOrElse(List<? extends T> list, int index, Function1<? super Integer, ? extends T> defaultValue) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        return (index < 0 || index > CollectionsKt.getLastIndex(list)) ? defaultValue.invoke(Integer.valueOf(index)) : list.get(index);
    }

    public static final <T> T elementAtOrNull(Iterable<? extends T> iterable, int i) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return (T) CollectionsKt.getOrNull((List) iterable, i);
        }
        if (i < 0) {
            return null;
        }
        int i2 = 0;
        for (T t : iterable) {
            int i3 = i2 + 1;
            if (i != i2) {
                i2 = i3;
            } else {
                return t;
            }
        }
        return null;
    }

    private static final <T> T elementAtOrNull(List<? extends T> list, int i) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return (T) CollectionsKt.getOrNull(list, i);
    }

    /* JADX WARN: Type inference failed for: r3v2, types: [T, java.lang.Object] */
    private static final <T> T find(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : iterable) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v1, types: [java.lang.Object] */
    private static final <T> T findLast(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        for (T t2 : iterable) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
            }
        }
        return t;
    }

    /* JADX WARN: Type inference failed for: r3v2, types: [T, java.lang.Object] */
    private static final <T> T findLast(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        while (listIterator.hasPrevious()) {
            ?? r3 = (Object) listIterator.previous();
            if (predicate.invoke(r3).booleanValue()) {
                return r3;
            }
        }
        return null;
    }

    public static final <T> T first(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return (T) CollectionsKt.first((List) iterable);
        }
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException("Collection is empty.");
        }
        return it.next();
    }

    public static final <T> T first(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (list.isEmpty()) {
            throw new NoSuchElementException("List is empty.");
        }
        return list.get(0);
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    public static final <T> T first(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : iterable) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        throw new NoSuchElementException("Collection contains no element matching the predicate.");
    }

    private static final <T, R> R firstNotNullOf(Iterable<? extends T> iterable, Function1<? super T, ? extends R> transform) {
        R r;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Iterator<? extends T> it = iterable.iterator();
        while (true) {
            if (!it.hasNext()) {
                r = null;
                break;
            }
            r = transform.invoke(it.next());
            if (r != null) {
                break;
            }
        }
        if (r != null) {
            return r;
        }
        throw new NoSuchElementException("No element of the collection was transformed to a non-null value.");
    }

    private static final <T, R> R firstNotNullOfOrNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : iterable) {
            R invoke = transform.invoke(element);
            if (invoke != null) {
                return invoke;
            }
        }
        return null;
    }

    public static final <T> T firstOrNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            if (((List) iterable).isEmpty()) {
                return null;
            }
            return (T) ((List) iterable).get(0);
        }
        Iterator<? extends T> it = iterable.iterator();
        if (it.hasNext()) {
            return it.next();
        }
        return null;
    }

    public static final <T> T firstOrNull(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    public static final <T> T firstOrNull(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : iterable) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        return null;
    }

    private static final <T> T getOrElse(List<? extends T> list, int index, Function1<? super Integer, ? extends T> defaultValue) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        return (index < 0 || index > CollectionsKt.getLastIndex(list)) ? defaultValue.invoke(Integer.valueOf(index)) : list.get(index);
    }

    public static final <T> T getOrNull(List<? extends T> list, int index) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (index < 0 || index > CollectionsKt.getLastIndex(list)) {
            return null;
        }
        return list.get(index);
    }

    public static final <T> int indexOf(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return ((List) iterable).indexOf(t);
        }
        int index = 0;
        for (Object item : iterable) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (Intrinsics.areEqual(t, item)) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public static final <T> int indexOf(List<? extends T> list, T t) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.indexOf(t);
    }

    public static final <T> int indexOfFirst(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index = 0;
        for (Object item : iterable) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(item).booleanValue()) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public static final <T> int indexOfFirst(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index = 0;
        for (Object item : list) {
            if (predicate.invoke(item).booleanValue()) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public static final <T> int indexOfLast(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int lastIndex = -1;
        int index = 0;
        for (Object item : iterable) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(item).booleanValue()) {
                lastIndex = index;
            }
            index++;
        }
        return lastIndex;
    }

    public static final <T> int indexOfLast(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        while (listIterator.hasPrevious()) {
            if (predicate.invoke((Object) listIterator.previous()).booleanValue()) {
                return listIterator.nextIndex();
            }
        }
        return -1;
    }

    public static final <T> T last(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return (T) CollectionsKt.last((List) iterable);
        }
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException("Collection is empty.");
        }
        T next = it.next();
        while (it.hasNext()) {
            next = it.next();
        }
        return next;
    }

    public static final <T> T last(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (list.isEmpty()) {
            throw new NoSuchElementException("List is empty.");
        }
        return list.get(CollectionsKt.getLastIndex(list));
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v2, types: [java.lang.Object] */
    public static final <T> T last(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : iterable) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
                z = true;
            }
        }
        if (!z) {
            throw new NoSuchElementException("Collection contains no element matching the predicate.");
        }
        return t;
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    public static final <T> T last(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        while (listIterator.hasPrevious()) {
            ?? r2 = (Object) listIterator.previous();
            if (predicate.invoke(r2).booleanValue()) {
                return r2;
            }
        }
        throw new NoSuchElementException("List contains no element matching the predicate.");
    }

    public static final <T> int lastIndexOf(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return ((List) iterable).lastIndexOf(t);
        }
        int lastIndex = -1;
        int index = 0;
        for (Object item : iterable) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (Intrinsics.areEqual(t, item)) {
                lastIndex = index;
            }
            index++;
        }
        return lastIndex;
    }

    public static final <T> int lastIndexOf(List<? extends T> list, T t) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        return list.lastIndexOf(t);
    }

    public static final <T> T lastOrNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            if (((List) iterable).isEmpty()) {
                return null;
            }
            return (T) ((List) iterable).get(((List) iterable).size() - 1);
        }
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        while (it.hasNext()) {
            next = it.next();
        }
        return next;
    }

    public static final <T> T lastOrNull(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (list.isEmpty()) {
            return null;
        }
        return list.get(list.size() - 1);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.lang.Object] */
    public static final <T> T lastOrNull(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        for (T t2 : iterable) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
            }
        }
        return t;
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    public static final <T> T lastOrNull(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        while (listIterator.hasPrevious()) {
            ?? r2 = (Object) listIterator.previous();
            if (predicate.invoke(r2).booleanValue()) {
                return r2;
            }
        }
        return null;
    }

    private static final <T> T random(Collection<? extends T> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        return (T) CollectionsKt.random(collection, Random.INSTANCE);
    }

    public static final <T> T random(Collection<? extends T> collection, Random random) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        Intrinsics.checkNotNullParameter(random, "random");
        if (collection.isEmpty()) {
            throw new NoSuchElementException("Collection is empty.");
        }
        return (T) CollectionsKt.elementAt(collection, random.nextInt(collection.size()));
    }

    private static final <T> T randomOrNull(Collection<? extends T> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        return (T) CollectionsKt.randomOrNull(collection, Random.INSTANCE);
    }

    public static final <T> T randomOrNull(Collection<? extends T> collection, Random random) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        Intrinsics.checkNotNullParameter(random, "random");
        if (collection.isEmpty()) {
            return null;
        }
        return (T) CollectionsKt.elementAt(collection, random.nextInt(collection.size()));
    }

    public static final <T> T single(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            return (T) CollectionsKt.single((List) iterable);
        }
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException("Collection is empty.");
        }
        T next = it.next();
        if (it.hasNext()) {
            throw new IllegalArgumentException("Collection has more than one element.");
        }
        return next;
    }

    public static final <T> T single(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        switch (list.size()) {
            case 0:
                throw new NoSuchElementException("List is empty.");
            case 1:
                return list.get(0);
            default:
                throw new IllegalArgumentException("List has more than one element.");
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v2, types: [java.lang.Object] */
    public static final <T> T single(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : iterable) {
            if (predicate.invoke(t2).booleanValue()) {
                if (z) {
                    throw new IllegalArgumentException("Collection contains more than one matching element.");
                }
                t = t2;
                z = true;
            }
        }
        if (!z) {
            throw new NoSuchElementException("Collection contains no element matching the predicate.");
        }
        return t;
    }

    public static final <T> T singleOrNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof List) {
            if (((List) iterable).size() == 1) {
                return (T) ((List) iterable).get(0);
            }
            return null;
        }
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        if (it.hasNext()) {
            return null;
        }
        return next;
    }

    public static final <T> T singleOrNull(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (list.size() == 1) {
            return list.get(0);
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v1, types: [java.lang.Object] */
    public static final <T> T singleOrNull(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : iterable) {
            if (predicate.invoke(t2).booleanValue()) {
                if (z) {
                    return null;
                }
                t = t2;
                z = true;
            }
        }
        if (z) {
            return t;
        }
        return null;
    }

    public static final <T> List<T> drop(Iterable<? extends T> iterable, int n) {
        ArrayList list;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
        }
        if (n == 0) {
            return CollectionsKt.toList(iterable);
        }
        if (iterable instanceof Collection) {
            int resultSize = ((Collection) iterable).size() - n;
            if (resultSize <= 0) {
                return CollectionsKt.emptyList();
            }
            if (resultSize == 1) {
                return CollectionsKt.listOf(CollectionsKt.last(iterable));
            }
            list = new ArrayList(resultSize);
            if (iterable instanceof List) {
                if (iterable instanceof RandomAccess) {
                    int size = ((Collection) iterable).size();
                    for (int index = n; index < size; index++) {
                        list.add(((List) iterable).get(index));
                    }
                } else {
                    ListIterator listIterator = ((List) iterable).listIterator(n);
                    while (listIterator.hasNext()) {
                        Object item = listIterator.next();
                        list.add(item);
                    }
                }
                return list;
            }
        } else {
            list = new ArrayList();
        }
        int count = 0;
        for (T t : iterable) {
            if (count >= n) {
                list.add(t);
            } else {
                count++;
            }
        }
        return CollectionsKt.optimizeReadOnlyList(list);
    }

    public static final <T> List<T> dropLast(List<? extends T> list, int n) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (n >= 0) {
            return CollectionsKt.take(list, RangesKt.coerceAtLeast(list.size() - n, 0));
        }
        throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
    }

    public static final <T> List<T> dropLastWhile(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if (!list.isEmpty()) {
            ListIterator<? extends T> listIterator = list.listIterator(list.size());
            while (listIterator.hasPrevious()) {
                if (!predicate.invoke((Object) listIterator.previous()).booleanValue()) {
                    return CollectionsKt.take(list, listIterator.nextIndex() + 1);
                }
            }
        }
        return CollectionsKt.emptyList();
    }

    public static final <T> List<T> dropWhile(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        boolean yielding = false;
        ArrayList list = new ArrayList();
        for (Object item : iterable) {
            if (yielding) {
                list.add(item);
            } else if (!predicate.invoke(item).booleanValue()) {
                list.add(item);
                yielding = true;
            }
        }
        return list;
    }

    public static final <T> List<T> filter(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Collection destination$iv = new ArrayList();
        for (Object element$iv : iterable) {
            if (predicate.invoke(element$iv).booleanValue()) {
                destination$iv.add(element$iv);
            }
        }
        return (List) destination$iv;
    }

    public static final <T> List<T> filterIndexed(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Collection destination$iv = new ArrayList();
        int index$iv = 0;
        for (Object item$iv$iv : iterable) {
            int index$iv$iv = index$iv + 1;
            if (index$iv < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(Integer.valueOf(index$iv), item$iv$iv).booleanValue()) {
                destination$iv.add(item$iv$iv);
            }
            index$iv = index$iv$iv;
        }
        return (List) destination$iv;
    }

    public static final <T, C extends Collection<? super T>> C filterIndexedTo(Iterable<? extends T> iterable, C destination, Function2<? super Integer, ? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index = 0;
        for (Object item$iv : iterable) {
            int index$iv = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(Integer.valueOf(index), item$iv).booleanValue()) {
                destination.add(item$iv);
            }
            index = index$iv;
        }
        return destination;
    }

    public static final /* synthetic */ <R> List<R> filterIsInstance(Iterable<?> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Collection destination$iv = new ArrayList();
        for (Object element$iv : iterable) {
            Intrinsics.reifiedOperationMarker(3, "R");
            if (element$iv instanceof Object) {
                destination$iv.add(element$iv);
            }
        }
        return (List) destination$iv;
    }

    public static final /* synthetic */ <R, C extends Collection<? super R>> C filterIsInstanceTo(Iterable<?> iterable, C destination) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        for (Object element : iterable) {
            Intrinsics.reifiedOperationMarker(3, "R");
            if (element instanceof Object) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T> List<T> filterNot(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Collection destination$iv = new ArrayList();
        for (Object element$iv : iterable) {
            if (!predicate.invoke(element$iv).booleanValue()) {
                destination$iv.add(element$iv);
            }
        }
        return (List) destination$iv;
    }

    public static final <T> List<T> filterNotNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return (List) CollectionsKt.filterNotNullTo(iterable, new ArrayList());
    }

    public static final <C extends Collection<? super T>, T> C filterNotNullTo(Iterable<? extends T> iterable, C destination) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        for (T t : iterable) {
            if (t != null) {
                destination.add(t);
            }
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C filterNotTo(Iterable<? extends T> iterable, C destination, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : iterable) {
            if (!predicate.invoke(element).booleanValue()) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C filterTo(Iterable<? extends T> iterable, C destination, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : iterable) {
            if (predicate.invoke(element).booleanValue()) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T> List<T> slice(List<? extends T> list, IntRange indices) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        return indices.isEmpty() ? CollectionsKt.emptyList() : CollectionsKt.toList(list.subList(indices.getStart().intValue(), indices.getEndInclusive().intValue() + 1));
    }

    public static final <T> List<T> slice(List<? extends T> list, Iterable<Integer> indices) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        int size = CollectionsKt.collectionSizeOrDefault(indices, 10);
        if (size == 0) {
            return CollectionsKt.emptyList();
        }
        ArrayList list2 = new ArrayList(size);
        Iterator<Integer> it = indices.iterator();
        while (it.hasNext()) {
            int index = it.next().intValue();
            list2.add(list.get(index));
        }
        return list2;
    }

    public static final <T> List<T> take(Iterable<? extends T> iterable, int n) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
        }
        if (n == 0) {
            return CollectionsKt.emptyList();
        }
        if (iterable instanceof Collection) {
            if (n >= ((Collection) iterable).size()) {
                return CollectionsKt.toList(iterable);
            }
            if (n == 1) {
                return CollectionsKt.listOf(CollectionsKt.first(iterable));
            }
        }
        int count = 0;
        ArrayList list = new ArrayList(n);
        Iterator<? extends T> it = iterable.iterator();
        while (it.hasNext()) {
            list.add(it.next());
            count++;
            if (count == n) {
                break;
            }
        }
        return CollectionsKt.optimizeReadOnlyList(list);
    }

    public static final <T> List<T> takeLast(List<? extends T> list, int n) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
        }
        if (n == 0) {
            return CollectionsKt.emptyList();
        }
        int size = list.size();
        if (n >= size) {
            return CollectionsKt.toList(list);
        }
        if (n == 1) {
            return CollectionsKt.listOf(CollectionsKt.last((List) list));
        }
        ArrayList list2 = new ArrayList(n);
        if (list instanceof RandomAccess) {
            for (int index = size - n; index < size; index++) {
                list2.add(list.get(index));
            }
        } else {
            int index2 = size - n;
            ListIterator<? extends T> listIterator = list.listIterator(index2);
            while (listIterator.hasNext()) {
                list2.add(listIterator.next());
            }
        }
        return list2;
    }

    public static final <T> List<T> takeLastWhile(List<? extends T> list, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if (list.isEmpty()) {
            return CollectionsKt.emptyList();
        }
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        while (listIterator.hasPrevious()) {
            if (!predicate.invoke((Object) listIterator.previous()).booleanValue()) {
                listIterator.next();
                int size = list.size() - listIterator.nextIndex();
                if (size == 0) {
                    return CollectionsKt.emptyList();
                }
                ArrayList arrayList = new ArrayList(size);
                while (listIterator.hasNext()) {
                    arrayList.add(listIterator.next());
                }
                return arrayList;
            }
        }
        return CollectionsKt.toList(list);
    }

    public static final <T> List<T> takeWhile(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ArrayList list = new ArrayList();
        for (Object item : iterable) {
            if (!predicate.invoke(item).booleanValue()) {
                break;
            }
            list.add(item);
        }
        return list;
    }

    public static final <T> List<T> reversed(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if ((iterable instanceof Collection) && ((Collection) iterable).size() <= 1) {
            return CollectionsKt.toList(iterable);
        }
        List list = CollectionsKt.toMutableList(iterable);
        CollectionsKt.reverse(list);
        return list;
    }

    public static final <T> void shuffle(List<T> list, Random random) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(random, "random");
        for (int i = CollectionsKt.getLastIndex(list); i > 0; i--) {
            int j = random.nextInt(i + 1);
            list.set(j, list.set(i, list.get(j)));
        }
    }

    public static final <T, R extends Comparable<? super R>> void sortBy(List<T> list, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (list.size() > 1) {
            CollectionsKt.sortWith(list, new ComparisonsKt__ComparisonsKt.compareBy.2(selector));
        }
    }

    public static final <T, R extends Comparable<? super R>> void sortByDescending(List<T> list, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (list.size() > 1) {
            CollectionsKt.sortWith(list, new ComparisonsKt__ComparisonsKt$compareByDescending$1(selector));
        }
    }

    public static final <T extends Comparable<? super T>> void sortDescending(List<T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        CollectionsKt.sortWith(list, ComparisonsKt.reverseOrder());
    }

    public static final <T extends Comparable<? super T>> List<T> sorted(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            if (((Collection) iterable).size() <= 1) {
                return CollectionsKt.toList(iterable);
            }
            Collection $this$toTypedArray$iv = (Collection) iterable;
            Object[] array = $this$toTypedArray$iv.toArray(new Comparable[0]);
            Comparable[] $this$sorted_u24lambda_u246 = (Comparable[]) array;
            ArraysKt.sort((Object[]) $this$sorted_u24lambda_u246);
            return ArraysKt.asList(array);
        }
        List $this$sorted_u24lambda_u247 = CollectionsKt.toMutableList(iterable);
        CollectionsKt.sort($this$sorted_u24lambda_u247);
        return $this$sorted_u24lambda_u247;
    }

    public static final <T, R extends Comparable<? super R>> List<T> sortedBy(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        return CollectionsKt.sortedWith(iterable, new ComparisonsKt__ComparisonsKt.compareBy.2(selector));
    }

    public static final <T, R extends Comparable<? super R>> List<T> sortedByDescending(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        return CollectionsKt.sortedWith(iterable, new ComparisonsKt__ComparisonsKt$compareByDescending$1(selector));
    }

    public static final <T extends Comparable<? super T>> List<T> sortedDescending(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return CollectionsKt.sortedWith(iterable, ComparisonsKt.reverseOrder());
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <T> List<T> sortedWith(Iterable<? extends T> iterable, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        if (iterable instanceof Collection) {
            if (((Collection) iterable).size() <= 1) {
                return CollectionsKt.toList(iterable);
            }
            Object[] array = ((Collection) iterable).toArray(new Object[0]);
            ArraysKt.sortWith(array, comparator);
            return ArraysKt.asList(array);
        }
        List $this$sortedWith_u24lambda_u249 = CollectionsKt.toMutableList(iterable);
        CollectionsKt.sortWith($this$sortedWith_u24lambda_u249, comparator);
        return $this$sortedWith_u24lambda_u249;
    }

    public static final boolean[] toBooleanArray(Collection<Boolean> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        boolean[] result = new boolean[collection.size()];
        int index = 0;
        Iterator<Boolean> it = collection.iterator();
        while (it.hasNext()) {
            boolean element = it.next().booleanValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final byte[] toByteArray(Collection<Byte> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        byte[] result = new byte[collection.size()];
        int index = 0;
        Iterator<Byte> it = collection.iterator();
        while (it.hasNext()) {
            byte element = it.next().byteValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final char[] toCharArray(Collection<Character> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        char[] result = new char[collection.size()];
        int index = 0;
        Iterator<Character> it = collection.iterator();
        while (it.hasNext()) {
            char element = it.next().charValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final double[] toDoubleArray(Collection<Double> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        double[] result = new double[collection.size()];
        int index = 0;
        Iterator<Double> it = collection.iterator();
        while (it.hasNext()) {
            double element = it.next().doubleValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final float[] toFloatArray(Collection<Float> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        float[] result = new float[collection.size()];
        int index = 0;
        Iterator<Float> it = collection.iterator();
        while (it.hasNext()) {
            float element = it.next().floatValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final int[] toIntArray(Collection<Integer> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        int[] result = new int[collection.size()];
        int index = 0;
        Iterator<Integer> it = collection.iterator();
        while (it.hasNext()) {
            int element = it.next().intValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final long[] toLongArray(Collection<Long> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        long[] result = new long[collection.size()];
        int index = 0;
        Iterator<Long> it = collection.iterator();
        while (it.hasNext()) {
            long element = it.next().longValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final short[] toShortArray(Collection<Short> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        short[] result = new short[collection.size()];
        int index = 0;
        Iterator<Short> it = collection.iterator();
        while (it.hasNext()) {
            short element = it.next().shortValue();
            result[index] = element;
            index++;
        }
        return result;
    }

    public static final <T, K, V> Map<K, V> associate(Iterable<? extends T> iterable, Function1<? super T, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(iterable, 10)), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (Object element$iv : iterable) {
            Pair<? extends K, ? extends V> invoke = transform.invoke(element$iv);
            destination$iv.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination$iv;
    }

    public static final <T, K> Map<K, T> associateBy(Iterable<? extends T> iterable, Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(iterable, 10)), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (Object element$iv : iterable) {
            destination$iv.put(keySelector.invoke(element$iv), element$iv);
        }
        return destination$iv;
    }

    public static final <T, K, V> Map<K, V> associateBy(Iterable<? extends T> iterable, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(iterable, 10)), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (Object element$iv : iterable) {
            destination$iv.put(keySelector.invoke(element$iv), valueTransform.invoke(element$iv));
        }
        return destination$iv;
    }

    public static final <T, K, M extends Map<? super K, ? super T>> M associateByTo(Iterable<? extends T> iterable, M destination, Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (Object element : iterable) {
            destination.put(keySelector.invoke(element), element);
        }
        return destination;
    }

    public static final <T, K, V, M extends Map<? super K, ? super V>> M associateByTo(Iterable<? extends T> iterable, M destination, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (Object element : iterable) {
            destination.put(keySelector.invoke(element), valueTransform.invoke(element));
        }
        return destination;
    }

    public static final <T, K, V, M extends Map<? super K, ? super V>> M associateTo(Iterable<? extends T> iterable, M destination, Function1<? super T, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : iterable) {
            Pair<? extends K, ? extends V> invoke = transform.invoke(element);
            destination.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <K, V> Map<K, V> associateWith(Iterable<? extends K> iterable, Function1<? super K, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        LinkedHashMap result = new LinkedHashMap(RangesKt.coerceAtLeast(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(iterable, 10)), 16));
        for (Object element$iv : iterable) {
            result.put(element$iv, valueSelector.invoke(element$iv));
        }
        return result;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <K, V, M extends Map<? super K, ? super V>> M associateWithTo(Iterable<? extends K> iterable, M destination, Function1<? super K, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        for (Object element : iterable) {
            destination.put(element, valueSelector.invoke(element));
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C toCollection(Iterable<? extends T> iterable, C destination) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Iterator<? extends T> it = iterable.iterator();
        while (it.hasNext()) {
            destination.add(it.next());
        }
        return destination;
    }

    public static final <T> HashSet<T> toHashSet(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return (HashSet) CollectionsKt.toCollection(iterable, new HashSet(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(iterable, 12))));
    }

    public static final <T> List<T> toList(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            switch (((Collection) iterable).size()) {
                case 0:
                    return CollectionsKt.emptyList();
                case 1:
                    return CollectionsKt.listOf(iterable instanceof List ? ((List) iterable).get(0) : iterable.iterator().next());
                default:
                    return CollectionsKt.toMutableList((Collection) iterable);
            }
        }
        return CollectionsKt.optimizeReadOnlyList(CollectionsKt.toMutableList(iterable));
    }

    public static final <T> List<T> toMutableList(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            return CollectionsKt.toMutableList((Collection) iterable);
        }
        return (List) CollectionsKt.toCollection(iterable, new ArrayList());
    }

    public static final <T> List<T> toMutableList(Collection<? extends T> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        return new ArrayList(collection);
    }

    public static final <T> Set<T> toSet(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            switch (((Collection) iterable).size()) {
                case 0:
                    return SetsKt.emptySet();
                case 1:
                    return SetsKt.setOf(iterable instanceof List ? ((List) iterable).get(0) : iterable.iterator().next());
                default:
                    return (Set) CollectionsKt.toCollection(iterable, new LinkedHashSet(MapsKt.mapCapacity(((Collection) iterable).size())));
            }
        }
        return SetsKt.optimizeReadOnlySet((Set) CollectionsKt.toCollection(iterable, new LinkedHashSet()));
    }

    public static final <T, R> List<R> flatMap(Iterable<? extends T> iterable, Function1<? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        for (Object element$iv : iterable) {
            Iterable list$iv = transform.invoke(element$iv);
            CollectionsKt.addAll(destination$iv, list$iv);
        }
        return (List) destination$iv;
    }

    public static final <T, R> List<R> flatMapSequence(Iterable<? extends T> iterable, Function1<? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        for (Object element$iv : iterable) {
            Sequence list$iv = transform.invoke(element$iv);
            CollectionsKt.addAll(destination$iv, list$iv);
        }
        return (List) destination$iv;
    }

    private static final <T, R> List<R> flatMapIndexedIterable(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        ArrayList arrayList = new ArrayList();
        int i = 0;
        for (T t : iterable) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            CollectionsKt.addAll(arrayList, transform.invoke(Integer.valueOf(i), t));
            i = i2;
        }
        return arrayList;
    }

    private static final <T, R> List<R> flatMapIndexedSequence(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        ArrayList arrayList = new ArrayList();
        int i = 0;
        for (T t : iterable) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            CollectionsKt.addAll(arrayList, transform.invoke(Integer.valueOf(i), t));
            i = i2;
        }
        return arrayList;
    }

    private static final <T, R, C extends Collection<? super R>> C flatMapIndexedIterableTo(Iterable<? extends T> iterable, C destination, Function2<? super Integer, ? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object element : iterable) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            Iterable list = transform.invoke(Integer.valueOf(index), element);
            CollectionsKt.addAll(destination, list);
            index = index2;
        }
        return destination;
    }

    private static final <T, R, C extends Collection<? super R>> C flatMapIndexedSequenceTo(Iterable<? extends T> iterable, C destination, Function2<? super Integer, ? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object element : iterable) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            Sequence list = transform.invoke(Integer.valueOf(index), element);
            CollectionsKt.addAll(destination, list);
            index = index2;
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C flatMapTo(Iterable<? extends T> iterable, C destination, Function1<? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : iterable) {
            Iterable list = transform.invoke(element);
            CollectionsKt.addAll(destination, list);
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C flatMapSequenceTo(Iterable<? extends T> iterable, C destination, Function1<? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : iterable) {
            Sequence list = transform.invoke(element);
            CollectionsKt.addAll(destination, list);
        }
        return destination;
    }

    public static final <T, K> Map<K, List<T>> groupBy(Iterable<? extends T> iterable, Function1<? super T, ? extends K> keySelector) {
        Object answer$iv$iv;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : iterable) {
            K invoke = keySelector.invoke(element$iv);
            Object value$iv$iv = destination$iv.get(invoke);
            if (value$iv$iv == null) {
                answer$iv$iv = new ArrayList();
                destination$iv.put(invoke, answer$iv$iv);
            } else {
                answer$iv$iv = value$iv$iv;
            }
            List list$iv = (List) answer$iv$iv;
            list$iv.add(element$iv);
        }
        return destination$iv;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r11v1, types: [java.util.List] */
    /* JADX WARN: Type inference failed for: r9v0, types: [java.lang.Object] */
    public static final <T, K, V> Map<K, List<V>> groupBy(Iterable<? extends T> iterable, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : iterable) {
            K invoke = keySelector.invoke(element$iv);
            ?? r9 = destination$iv.get(invoke);
            if (r9 == 0) {
                v = new ArrayList();
                destination$iv.put(invoke, v);
            } else {
                v = r9;
            }
            List list$iv = (List) v;
            list$iv.add(valueTransform.invoke(element$iv));
        }
        return destination$iv;
    }

    public static final <T, K, M extends Map<? super K, List<T>>> M groupByTo(Iterable<? extends T> iterable, M destination, Function1<? super T, ? extends K> keySelector) {
        Object answer$iv;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (Object element : iterable) {
            K invoke = keySelector.invoke(element);
            Object value$iv = destination.get(invoke);
            if (value$iv == null) {
                answer$iv = new ArrayList();
                destination.put(invoke, answer$iv);
            } else {
                answer$iv = value$iv;
            }
            List list = (List) answer$iv;
            list.add(element);
        }
        return destination;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r6v0, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r8v1, types: [java.util.List] */
    public static final <T, K, V, M extends Map<? super K, List<V>>> M groupByTo(Iterable<? extends T> iterable, M destination, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (Object element : iterable) {
            K invoke = keySelector.invoke(element);
            ?? r6 = destination.get(invoke);
            if (r6 == 0) {
                v = new ArrayList();
                destination.put(invoke, v);
            } else {
                v = r6;
            }
            List list = (List) v;
            list.add(valueTransform.invoke(element));
        }
        return destination;
    }

    public static final <T, K> Grouping<T, K> groupingBy(final Iterable<? extends T> iterable, final Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        return new Grouping<T, K>() { // from class: kotlin.collections.CollectionsKt___CollectionsKt$groupingBy$1
            @Override // kotlin.collections.Grouping
            public Iterator<T> sourceIterator() {
                return iterable.iterator();
            }

            @Override // kotlin.collections.Grouping
            public K keyOf(T element) {
                return keySelector.invoke(element);
            }
        };
    }

    public static final <T, R> List<R> map(Iterable<? extends T> iterable, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList(CollectionsKt.collectionSizeOrDefault(iterable, 10));
        for (Object item$iv : iterable) {
            destination$iv.add(transform.invoke(item$iv));
        }
        return (List) destination$iv;
    }

    public static final <T, R> List<R> mapIndexed(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList(CollectionsKt.collectionSizeOrDefault(iterable, 10));
        int index$iv = 0;
        for (Object item$iv : iterable) {
            int index$iv2 = index$iv + 1;
            if (index$iv < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            destination$iv.add(transform.invoke(Integer.valueOf(index$iv), item$iv));
            index$iv = index$iv2;
        }
        return (List) destination$iv;
    }

    public static final <T, R> List<R> mapIndexedNotNull(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        int index$iv = 0;
        for (Object item$iv$iv : iterable) {
            int index$iv$iv = index$iv + 1;
            if (index$iv < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            R invoke = transform.invoke(Integer.valueOf(index$iv), item$iv$iv);
            if (invoke != null) {
                destination$iv.add(invoke);
            }
            index$iv = index$iv$iv;
        }
        return (List) destination$iv;
    }

    public static final <T, R, C extends Collection<? super R>> C mapIndexedNotNullTo(Iterable<? extends T> iterable, C destination, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object item$iv : iterable) {
            int index$iv = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            R invoke = transform.invoke(Integer.valueOf(index), item$iv);
            if (invoke != null) {
                destination.add(invoke);
            }
            index = index$iv;
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C mapIndexedTo(Iterable<? extends T> iterable, C destination, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object item : iterable) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            destination.add(transform.invoke(Integer.valueOf(index), item));
            index = index2;
        }
        return destination;
    }

    public static final <T, R> List<R> mapNotNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        for (Object element$iv$iv : iterable) {
            R invoke = transform.invoke(element$iv$iv);
            if (invoke != null) {
                destination$iv.add(invoke);
            }
        }
        return (List) destination$iv;
    }

    public static final <T, R, C extends Collection<? super R>> C mapNotNullTo(Iterable<? extends T> iterable, C destination, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element$iv : iterable) {
            R invoke = transform.invoke(element$iv);
            if (invoke != null) {
                destination.add(invoke);
            }
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C mapTo(Iterable<? extends T> iterable, C destination, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object item : iterable) {
            destination.add(transform.invoke(item));
        }
        return destination;
    }

    public static final <T> Iterable<IndexedValue<T>> withIndex(final Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return new IndexingIterable(new Function0<Iterator<? extends T>>() { // from class: kotlin.collections.CollectionsKt___CollectionsKt$withIndex$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(0);
            }

            @Override // kotlin.jvm.functions.Function0
            public final Iterator<T> invoke() {
                return iterable.iterator();
            }
        });
    }

    public static final <T> List<T> distinct(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return CollectionsKt.toList(CollectionsKt.toMutableSet(iterable));
    }

    public static final <T, K> List<T> distinctBy(Iterable<? extends T> iterable, Function1<? super T, ? extends K> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        HashSet set = new HashSet();
        ArrayList list = new ArrayList();
        for (Object e : iterable) {
            if (set.add(selector.invoke(e))) {
                list.add(e);
            }
        }
        return list;
    }

    public static final <T> Set<T> intersect(Iterable<? extends T> iterable, Iterable<? extends T> other) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Set set = CollectionsKt.toMutableSet(iterable);
        CollectionsKt.retainAll(set, other);
        return set;
    }

    public static final <T> Set<T> subtract(Iterable<? extends T> iterable, Iterable<? extends T> other) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Set set = CollectionsKt.toMutableSet(iterable);
        CollectionsKt.removeAll(set, other);
        return set;
    }

    public static final <T> Set<T> toMutableSet(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return iterable instanceof Collection ? new LinkedHashSet((Collection) iterable) : (Set) CollectionsKt.toCollection(iterable, new LinkedHashSet());
    }

    public static final <T> Set<T> union(Iterable<? extends T> iterable, Iterable<? extends T> other) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Set set = CollectionsKt.toMutableSet(iterable);
        CollectionsKt.addAll(set, other);
        return set;
    }

    public static final <T> boolean all(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if ((iterable instanceof Collection) && ((Collection) iterable).isEmpty()) {
            return true;
        }
        for (Object element : iterable) {
            if (!predicate.invoke(element).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final <T> boolean any(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return iterable instanceof Collection ? !((Collection) iterable).isEmpty() : iterable.iterator().hasNext();
    }

    public static final <T> boolean any(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if ((iterable instanceof Collection) && ((Collection) iterable).isEmpty()) {
            return false;
        }
        for (Object element : iterable) {
            if (predicate.invoke(element).booleanValue()) {
                return true;
            }
        }
        return false;
    }

    public static final <T> int count(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            return ((Collection) iterable).size();
        }
        int count = 0;
        Iterator<? extends T> it = iterable.iterator();
        while (it.hasNext()) {
            it.next();
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        return count;
    }

    private static final <T> int count(Collection<? extends T> collection) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        return collection.size();
    }

    public static final <T> int count(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if ((iterable instanceof Collection) && ((Collection) iterable).isEmpty()) {
            return 0;
        }
        int count = 0;
        for (Object element : iterable) {
            if (predicate.invoke(element).booleanValue() && (count = count + 1) < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        return count;
    }

    public static final <T, R> R fold(Iterable<? extends T> iterable, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        Iterator<? extends T> it = iterable.iterator();
        while (it.hasNext()) {
            r2 = operation.invoke(r2, it.next());
        }
        return r2;
    }

    public static final <T, R> R foldIndexed(Iterable<? extends T> iterable, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int i = 0;
        R r2 = r;
        for (Object obj : iterable) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            r2 = operation.invoke(Integer.valueOf(i), r2, obj);
            i = i2;
        }
        return r2;
    }

    public static final <T, R> R foldRight(List<? extends T> list, R r, Function2<? super T, ? super R, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        if (!list.isEmpty()) {
            ListIterator<? extends T> listIterator = list.listIterator(list.size());
            while (listIterator.hasPrevious()) {
                r2 = operation.invoke((Object) listIterator.previous(), r2);
            }
        }
        return r2;
    }

    public static final <T, R> R foldRightIndexed(List<? extends T> list, R r, Function3<? super Integer, ? super T, ? super R, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        if (!list.isEmpty()) {
            ListIterator<? extends T> listIterator = list.listIterator(list.size());
            while (listIterator.hasPrevious()) {
                r2 = operation.invoke(Integer.valueOf(listIterator.previousIndex()), (Object) listIterator.previous(), r2);
            }
        }
        return r2;
    }

    public static final <T> void forEach(Iterable<? extends T> iterable, Function1<? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        for (Object element : iterable) {
            action.invoke(element);
        }
    }

    public static final <T> void forEachIndexed(Iterable<? extends T> iterable, Function2<? super Integer, ? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        int index = 0;
        for (Object item : iterable) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            action.invoke(Integer.valueOf(index), item);
            index = index2;
        }
    }

    public static final double maxOrThrow(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        double max = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            max = Math.max(max, e);
        }
        return max;
    }

    /* renamed from: maxOrThrow, reason: collision with other method in class */
    public static final float m674maxOrThrow(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        float max = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            max = Math.max(max, e);
        }
        return max;
    }

    /* renamed from: maxOrThrow, reason: collision with other method in class */
    public static final <T extends Comparable<? super T>> T m675maxOrThrow(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) < 0) {
                next = next2;
            }
        }
        return next;
    }

    /* JADX WARN: Type inference failed for: r2v10 */
    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v3 */
    /* JADX WARN: Type inference failed for: r2v4, types: [T] */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T maxByOrThrow(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v2 */
    /* JADX WARN: Type inference failed for: r2v3, types: [T] */
    /* JADX WARN: Type inference failed for: r2v8 */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T maxByOrNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    private static final <T> double maxOf(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.max(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return doubleValue;
    }

    /* renamed from: maxOf, reason: collision with other method in class */
    private static final <T> float m668maxOf(Iterable<? extends T> iterable, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.max(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return floatValue;
    }

    /* renamed from: maxOf, reason: collision with other method in class */
    private static final <T, R extends Comparable<? super R>> R m669maxOf(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final <T> Double m670maxOfOrNull(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.max(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return Double.valueOf(doubleValue);
    }

    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final <T> Float m671maxOfOrNull(Iterable<? extends T> iterable, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.max(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return Float.valueOf(floatValue);
    }

    private static final <T, R extends Comparable<? super R>> R maxOfOrNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R maxOfWith(Iterable<? extends T> iterable, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R maxOfWithOrNull(Iterable<? extends T> iterable, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: maxOrNull, reason: collision with other method in class */
    public static final Double m672maxOrNull(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        double max = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            max = Math.max(max, e);
        }
        return Double.valueOf(max);
    }

    /* renamed from: maxOrNull, reason: collision with other method in class */
    public static final Float m673maxOrNull(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        float max = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            max = Math.max(max, e);
        }
        return Float.valueOf(max);
    }

    public static final <T extends Comparable<? super T>> T maxOrNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) < 0) {
                next = next2;
            }
        }
        return next;
    }

    public static final <T> T maxWithOrThrow(Iterable<? extends T> iterable, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) < 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> T maxWithOrNull(Iterable<? extends T> iterable, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) < 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final double minOrThrow(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        double min = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            min = Math.min(min, e);
        }
        return min;
    }

    /* renamed from: minOrThrow, reason: collision with other method in class */
    public static final float m682minOrThrow(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        float min = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            min = Math.min(min, e);
        }
        return min;
    }

    /* renamed from: minOrThrow, reason: collision with other method in class */
    public static final <T extends Comparable<? super T>> T m683minOrThrow(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) > 0) {
                next = next2;
            }
        }
        return next;
    }

    /* JADX WARN: Type inference failed for: r2v10 */
    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v3 */
    /* JADX WARN: Type inference failed for: r2v4, types: [T] */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T minByOrThrow(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v2 */
    /* JADX WARN: Type inference failed for: r2v3, types: [T] */
    /* JADX WARN: Type inference failed for: r2v8 */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T minByOrNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    private static final <T> double minOf(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.min(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return doubleValue;
    }

    /* renamed from: minOf, reason: collision with other method in class */
    private static final <T> float m676minOf(Iterable<? extends T> iterable, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.min(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return floatValue;
    }

    /* renamed from: minOf, reason: collision with other method in class */
    private static final <T, R extends Comparable<? super R>> R m677minOf(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final <T> Double m678minOfOrNull(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.min(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return Double.valueOf(doubleValue);
    }

    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final <T> Float m679minOfOrNull(Iterable<? extends T> iterable, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.min(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return Float.valueOf(floatValue);
    }

    private static final <T, R extends Comparable<? super R>> R minOfOrNull(Iterable<? extends T> iterable, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R minOfWith(Iterable<? extends T> iterable, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R minOfWithOrNull(Iterable<? extends T> iterable, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: minOrNull, reason: collision with other method in class */
    public static final Double m680minOrNull(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        double min = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            min = Math.min(min, e);
        }
        return Double.valueOf(min);
    }

    /* renamed from: minOrNull, reason: collision with other method in class */
    public static final Float m681minOrNull(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        float min = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            min = Math.min(min, e);
        }
        return Float.valueOf(min);
    }

    public static final <T extends Comparable<? super T>> T minOrNull(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) > 0) {
                next = next2;
            }
        }
        return next;
    }

    public static final <T> T minWithOrThrow(Iterable<? extends T> iterable, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) > 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> T minWithOrNull(Iterable<? extends T> iterable, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) > 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> boolean none(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return iterable instanceof Collection ? ((Collection) iterable).isEmpty() : !iterable.iterator().hasNext();
    }

    public static final <T> boolean none(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        if ((iterable instanceof Collection) && ((Collection) iterable).isEmpty()) {
            return true;
        }
        for (Object element : iterable) {
            if (predicate.invoke(element).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final <T, C extends Iterable<? extends T>> C onEach(C c, Function1<? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(c, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        for (Object element : c) {
            action.invoke(element);
        }
        return c;
    }

    public static final <T, C extends Iterable<? extends T>> C onEachIndexed(C c, Function2<? super Integer, ? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(c, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        int index$iv = 0;
        for (Object item$iv : c) {
            int index$iv2 = index$iv + 1;
            if (index$iv < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            action.invoke(Integer.valueOf(index$iv), item$iv);
            index$iv = index$iv2;
        }
        return c;
    }

    public static final <S, T extends S> S reduce(Iterable<? extends T> iterable, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new UnsupportedOperationException("Empty collection can't be reduced.");
        }
        S s = (S) it.next();
        while (it.hasNext()) {
            s = operation.invoke(s, (Object) it.next());
        }
        return s;
    }

    public static final <S, T extends S> S reduceIndexed(Iterable<? extends T> iterable, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            throw new UnsupportedOperationException("Empty collection can't be reduced.");
        }
        int i = 1;
        S s = (S) it.next();
        while (it.hasNext()) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            s = operation.invoke(Integer.valueOf(i), s, (Object) it.next());
            i = i2;
        }
        return s;
    }

    public static final <S, T extends S> S reduceIndexedOrNull(Iterable<? extends T> iterable, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        int i = 1;
        S s = (S) it.next();
        while (it.hasNext()) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            s = operation.invoke(Integer.valueOf(i), s, (Object) it.next());
            i = i2;
        }
        return s;
    }

    public static final <S, T extends S> S reduceOrNull(Iterable<? extends T> iterable, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return null;
        }
        S s = (S) it.next();
        while (it.hasNext()) {
            s = operation.invoke(s, (Object) it.next());
        }
        return s;
    }

    public static final <S, T extends S> S reduceRight(List<? extends T> list, Function2<? super T, ? super S, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        if (!listIterator.hasPrevious()) {
            throw new UnsupportedOperationException("Empty list can't be reduced.");
        }
        S s = (S) listIterator.previous();
        while (listIterator.hasPrevious()) {
            s = operation.invoke((Object) listIterator.previous(), s);
        }
        return s;
    }

    public static final <S, T extends S> S reduceRightIndexed(List<? extends T> list, Function3<? super Integer, ? super T, ? super S, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        if (!listIterator.hasPrevious()) {
            throw new UnsupportedOperationException("Empty list can't be reduced.");
        }
        S s = (S) listIterator.previous();
        while (listIterator.hasPrevious()) {
            s = operation.invoke(Integer.valueOf(listIterator.previousIndex()), (Object) listIterator.previous(), s);
        }
        return s;
    }

    public static final <S, T extends S> S reduceRightIndexedOrNull(List<? extends T> list, Function3<? super Integer, ? super T, ? super S, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        if (!listIterator.hasPrevious()) {
            return null;
        }
        S s = (S) listIterator.previous();
        while (listIterator.hasPrevious()) {
            s = operation.invoke(Integer.valueOf(listIterator.previousIndex()), (Object) listIterator.previous(), s);
        }
        return s;
    }

    public static final <S, T extends S> S reduceRightOrNull(List<? extends T> list, Function2<? super T, ? super S, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        ListIterator<? extends T> listIterator = list.listIterator(list.size());
        if (!listIterator.hasPrevious()) {
            return null;
        }
        S s = (S) listIterator.previous();
        while (listIterator.hasPrevious()) {
            s = operation.invoke((Object) listIterator.previous(), s);
        }
        return s;
    }

    public static final <T, R> List<R> runningFold(Iterable<? extends T> iterable, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int estimatedSize = CollectionsKt.collectionSizeOrDefault(iterable, 9);
        if (estimatedSize == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result = new ArrayList(estimatedSize + 1);
        result.add(r);
        Object accumulator = r;
        for (Object element : iterable) {
            accumulator = operation.invoke(accumulator, element);
            result.add(accumulator);
        }
        return result;
    }

    public static final <T, R> List<R> runningFoldIndexed(Iterable<? extends T> iterable, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int estimatedSize = CollectionsKt.collectionSizeOrDefault(iterable, 9);
        if (estimatedSize == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result = new ArrayList(estimatedSize + 1);
        result.add(r);
        int index = 0;
        Object accumulator = r;
        for (Object element : iterable) {
            accumulator = operation.invoke(Integer.valueOf(index), accumulator, element);
            result.add(accumulator);
            index++;
        }
        return result;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <S, T extends S> List<S> runningReduce(Iterable<? extends T> iterable, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return CollectionsKt.emptyList();
        }
        T next = it.next();
        ArrayList arrayList = new ArrayList(CollectionsKt.collectionSizeOrDefault(iterable, 10));
        arrayList.add(next);
        Object obj = next;
        while (it.hasNext()) {
            S invoke = operation.invoke(obj, (Object) it.next());
            arrayList.add(invoke);
            obj = invoke;
        }
        return arrayList;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <S, T extends S> List<S> runningReduceIndexed(Iterable<? extends T> iterable, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = iterable.iterator();
        if (!it.hasNext()) {
            return CollectionsKt.emptyList();
        }
        T next = it.next();
        ArrayList arrayList = new ArrayList(CollectionsKt.collectionSizeOrDefault(iterable, 10));
        arrayList.add(next);
        int i = 1;
        Object obj = next;
        while (it.hasNext()) {
            S invoke = operation.invoke(Integer.valueOf(i), obj, (Object) it.next());
            arrayList.add(invoke);
            i++;
            obj = invoke;
        }
        return arrayList;
    }

    public static final <T, R> List<R> scan(Iterable<? extends T> iterable, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int estimatedSize$iv = CollectionsKt.collectionSizeOrDefault(iterable, 9);
        if (estimatedSize$iv == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result$iv = new ArrayList(estimatedSize$iv + 1);
        result$iv.add(r);
        Object accumulator$iv = r;
        for (Object element$iv : iterable) {
            accumulator$iv = operation.invoke(accumulator$iv, element$iv);
            result$iv.add(accumulator$iv);
        }
        return result$iv;
    }

    public static final <T, R> List<R> scanIndexed(Iterable<? extends T> iterable, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int estimatedSize$iv = CollectionsKt.collectionSizeOrDefault(iterable, 9);
        if (estimatedSize$iv == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result$iv = new ArrayList(estimatedSize$iv + 1);
        result$iv.add(r);
        int index$iv = 0;
        Object accumulator$iv = r;
        for (Object element$iv : iterable) {
            accumulator$iv = operation.invoke(Integer.valueOf(index$iv), accumulator$iv, element$iv);
            result$iv.add(accumulator$iv);
            index$iv++;
        }
        return result$iv;
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final <T> int sumBy(Iterable<? extends T> iterable, Function1<? super T, Integer> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (Object element : iterable) {
            sum += selector.invoke(element).intValue();
        }
        return sum;
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final <T> double sumByDouble(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (Object element : iterable) {
            sum += selector.invoke(element).doubleValue();
        }
        return sum;
    }

    private static final <T> double sumOfDouble(Iterable<? extends T> iterable, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (Object element : iterable) {
            sum += selector.invoke(element).doubleValue();
        }
        return sum;
    }

    private static final <T> int sumOfInt(Iterable<? extends T> iterable, Function1<? super T, Integer> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (Object element : iterable) {
            sum += selector.invoke(element).intValue();
        }
        return sum;
    }

    private static final <T> long sumOfLong(Iterable<? extends T> iterable, Function1<? super T, Long> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = 0;
        for (Object element : iterable) {
            sum += selector.invoke(element).longValue();
        }
        return sum;
    }

    private static final <T> int sumOfUInt(Iterable<? extends T> iterable, Function1<? super T, UInt> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = UInt.m332constructorimpl(0);
        for (Object element : iterable) {
            sum = UInt.m332constructorimpl(selector.invoke(element).getData() + sum);
        }
        return sum;
    }

    private static final <T> long sumOfULong(Iterable<? extends T> iterable, Function1<? super T, ULong> selector) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = ULong.m411constructorimpl(0L);
        for (Object element : iterable) {
            sum = ULong.m411constructorimpl(selector.invoke(element).getData() + sum);
        }
        return sum;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <T> Iterable<T> requireNoNulls(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        for (Object element : iterable) {
            if (element == null) {
                throw new IllegalArgumentException("null element found in " + iterable + '.');
            }
        }
        return iterable;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <T> List<T> requireNoNulls(List<? extends T> list) {
        Intrinsics.checkNotNullParameter(list, "<this>");
        for (Object element : list) {
            if (element == null) {
                throw new IllegalArgumentException("null element found in " + list + '.');
            }
        }
        return list;
    }

    public static final <T> List<List<T>> chunked(Iterable<? extends T> iterable, int size) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return CollectionsKt.windowed(iterable, size, size, true);
    }

    public static final <T, R> List<R> chunked(Iterable<? extends T> iterable, int size, Function1<? super List<? extends T>, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return CollectionsKt.windowed(iterable, size, size, true, transform);
    }

    public static final <T> List<T> minus(Iterable<? extends T> iterable, T t) {
        boolean z;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterable result = new ArrayList(CollectionsKt.collectionSizeOrDefault(iterable, 10));
        boolean removed = false;
        for (T t2 : iterable) {
            if (removed || !Intrinsics.areEqual(t2, t)) {
                z = true;
            } else {
                removed = true;
                z = false;
            }
            if (z) {
                ((Collection) result).add(t2);
            }
        }
        Iterable $this$filterTo$iv = (Collection) result;
        return (List) $this$filterTo$iv;
    }

    public static final <T> List<T> minus(Iterable<? extends T> iterable, T[] elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        if (elements.length == 0) {
            return CollectionsKt.toList(iterable);
        }
        Collection destination$iv$iv = new ArrayList();
        for (T t : iterable) {
            if (!ArraysKt.contains(elements, t)) {
                destination$iv$iv.add(t);
            }
        }
        return (List) destination$iv$iv;
    }

    public static final <T> List<T> minus(Iterable<? extends T> iterable, Iterable<? extends T> elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        Collection other = CollectionsKt.convertToListIfNotCollection(elements);
        if (other.isEmpty()) {
            return CollectionsKt.toList(iterable);
        }
        Collection destination$iv$iv = new ArrayList();
        for (T t : iterable) {
            if (!other.contains(t)) {
                destination$iv$iv.add(t);
            }
        }
        return (List) destination$iv$iv;
    }

    public static final <T> List<T> minus(Iterable<? extends T> iterable, Sequence<? extends T> elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        List other = SequencesKt.toList(elements);
        if (other.isEmpty()) {
            return CollectionsKt.toList(iterable);
        }
        Collection destination$iv$iv = new ArrayList();
        for (T t : iterable) {
            if (!other.contains(t)) {
                destination$iv$iv.add(t);
            }
        }
        return (List) destination$iv$iv;
    }

    private static final <T> List<T> minusElement(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return CollectionsKt.minus(iterable, t);
    }

    public static final <T> Pair<List<T>, List<T>> partition(Iterable<? extends T> iterable, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ArrayList first = new ArrayList();
        ArrayList second = new ArrayList();
        for (Object element : iterable) {
            if (predicate.invoke(element).booleanValue()) {
                first.add(element);
            } else {
                second.add(element);
            }
        }
        return new Pair<>(first, second);
    }

    public static final <T> List<T> plus(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        if (iterable instanceof Collection) {
            return CollectionsKt.plus((Collection) iterable, (Object) t);
        }
        ArrayList result = new ArrayList();
        CollectionsKt.addAll(result, iterable);
        result.add(t);
        return result;
    }

    public static final <T> List<T> plus(Collection<? extends T> collection, T t) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        ArrayList result = new ArrayList(collection.size() + 1);
        result.addAll(collection);
        result.add(t);
        return result;
    }

    public static final <T> List<T> plus(Iterable<? extends T> iterable, T[] elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        if (iterable instanceof Collection) {
            return CollectionsKt.plus((Collection) iterable, (Object[]) elements);
        }
        ArrayList result = new ArrayList();
        CollectionsKt.addAll(result, iterable);
        CollectionsKt.addAll(result, elements);
        return result;
    }

    public static final <T> List<T> plus(Collection<? extends T> collection, T[] elements) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        ArrayList result = new ArrayList(collection.size() + elements.length);
        result.addAll(collection);
        CollectionsKt.addAll(result, elements);
        return result;
    }

    public static final <T> List<T> plus(Iterable<? extends T> iterable, Iterable<? extends T> elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        if (iterable instanceof Collection) {
            return CollectionsKt.plus((Collection) iterable, (Iterable) elements);
        }
        ArrayList result = new ArrayList();
        CollectionsKt.addAll(result, iterable);
        CollectionsKt.addAll(result, elements);
        return result;
    }

    public static final <T> List<T> plus(Collection<? extends T> collection, Iterable<? extends T> elements) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        if (elements instanceof Collection) {
            ArrayList result = new ArrayList(collection.size() + ((Collection) elements).size());
            result.addAll(collection);
            result.addAll((Collection) elements);
            return result;
        }
        ArrayList result2 = new ArrayList(collection);
        CollectionsKt.addAll(result2, elements);
        return result2;
    }

    public static final <T> List<T> plus(Iterable<? extends T> iterable, Sequence<? extends T> elements) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        ArrayList result = new ArrayList();
        CollectionsKt.addAll(result, iterable);
        CollectionsKt.addAll(result, elements);
        return result;
    }

    public static final <T> List<T> plus(Collection<? extends T> collection, Sequence<? extends T> elements) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        ArrayList result = new ArrayList(collection.size() + 10);
        result.addAll(collection);
        CollectionsKt.addAll(result, elements);
        return result;
    }

    private static final <T> List<T> plusElement(Iterable<? extends T> iterable, T t) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return CollectionsKt.plus(iterable, t);
    }

    private static final <T> List<T> plusElement(Collection<? extends T> collection, T t) {
        Intrinsics.checkNotNullParameter(collection, "<this>");
        return CollectionsKt.plus((Collection) collection, (Object) t);
    }

    public static /* synthetic */ List windowed$default(Iterable iterable, int i, int i2, boolean z, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return CollectionsKt.windowed(iterable, i, i2, z);
    }

    public static final <T> List<List<T>> windowed(Iterable<? extends T> iterable, int size, int step, boolean partialWindows) {
        int windowSize;
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        SlidingWindowKt.checkWindowSizeStep(size, step);
        if ((iterable instanceof RandomAccess) && (iterable instanceof List)) {
            int thisSize = ((List) iterable).size();
            int resultCapacity = (thisSize / step) + (thisSize % step == 0 ? 0 : 1);
            ArrayList result = new ArrayList(resultCapacity);
            int index = 0;
            while (true) {
                if (!(index >= 0 && index < thisSize) || ((windowSize = RangesKt.coerceAtMost(size, thisSize - index)) < size && !partialWindows)) {
                    break;
                }
                ArrayList arrayList = new ArrayList(windowSize);
                for (int i = 0; i < windowSize; i++) {
                    int it = i;
                    arrayList.add(((List) iterable).get(it + index));
                }
                result.add(arrayList);
                index += step;
            }
            return result;
        }
        ArrayList result2 = new ArrayList();
        Iterator $this$forEach$iv = SlidingWindowKt.windowedIterator(iterable.iterator(), size, step, partialWindows, false);
        while ($this$forEach$iv.hasNext()) {
            Object element$iv = $this$forEach$iv.next();
            List it2 = (List) element$iv;
            result2.add(it2);
        }
        return result2;
    }

    public static /* synthetic */ List windowed$default(Iterable iterable, int i, int i2, boolean z, Function1 function1, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return CollectionsKt.windowed(iterable, i, i2, z, function1);
    }

    public static final <T, R> List<R> windowed(Iterable<? extends T> iterable, int size, int step, boolean partialWindows, Function1<? super List<? extends T>, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        SlidingWindowKt.checkWindowSizeStep(size, step);
        if ((iterable instanceof RandomAccess) && (iterable instanceof List)) {
            int thisSize = ((List) iterable).size();
            int resultCapacity = (thisSize / step) + (thisSize % step == 0 ? 0 : 1);
            ArrayList result = new ArrayList(resultCapacity);
            MovingSubList window = new MovingSubList((List) iterable);
            int index = 0;
            while (true) {
                if (!(index >= 0 && index < thisSize)) {
                    break;
                }
                int windowSize = RangesKt.coerceAtMost(size, thisSize - index);
                if (!partialWindows && windowSize < size) {
                    break;
                }
                window.move(index, index + windowSize);
                result.add(transform.invoke(window));
                index += step;
            }
            return result;
        }
        ArrayList result2 = new ArrayList();
        Iterator $this$forEach$iv = SlidingWindowKt.windowedIterator(iterable.iterator(), size, step, partialWindows, true);
        while ($this$forEach$iv.hasNext()) {
            Object element$iv = $this$forEach$iv.next();
            List it = (List) element$iv;
            result2.add(transform.invoke(it));
        }
        return result2;
    }

    public static final <T, R> List<Pair<T, R>> zip(Iterable<? extends T> iterable, R[] other) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        int arraySize$iv = other.length;
        ArrayList list$iv = new ArrayList(Math.min(CollectionsKt.collectionSizeOrDefault(iterable, 10), arraySize$iv));
        int i$iv = 0;
        for (Object element$iv : iterable) {
            if (i$iv >= arraySize$iv) {
                break;
            }
            list$iv.add(TuplesKt.to(element$iv, other[i$iv]));
            i$iv++;
        }
        return list$iv;
    }

    public static final <T, R, V> List<V> zip(Iterable<? extends T> iterable, R[] other, Function2<? super T, ? super R, ? extends V> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int arraySize = other.length;
        ArrayList list = new ArrayList(Math.min(CollectionsKt.collectionSizeOrDefault(iterable, 10), arraySize));
        int i = 0;
        for (Object element : iterable) {
            if (i >= arraySize) {
                break;
            }
            list.add(transform.invoke(element, other[i]));
            i++;
        }
        return list;
    }

    public static final <T, R> List<Pair<T, R>> zip(Iterable<? extends T> iterable, Iterable<? extends R> other) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Iterator first$iv = iterable.iterator();
        Iterator second$iv = other.iterator();
        ArrayList list$iv = new ArrayList(Math.min(CollectionsKt.collectionSizeOrDefault(iterable, 10), CollectionsKt.collectionSizeOrDefault(other, 10)));
        while (first$iv.hasNext() && second$iv.hasNext()) {
            Object t1 = first$iv.next();
            Object t2 = second$iv.next();
            list$iv.add(TuplesKt.to(t1, t2));
        }
        return list$iv;
    }

    public static final <T, R, V> List<V> zip(Iterable<? extends T> iterable, Iterable<? extends R> other, Function2<? super T, ? super R, ? extends V> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Iterator<? extends T> it = iterable.iterator();
        Iterator<? extends R> it2 = other.iterator();
        ArrayList arrayList = new ArrayList(Math.min(CollectionsKt.collectionSizeOrDefault(iterable, 10), CollectionsKt.collectionSizeOrDefault(other, 10)));
        while (it.hasNext() && it2.hasNext()) {
            arrayList.add(transform.invoke((Object) it.next(), (Object) it2.next()));
        }
        return arrayList;
    }

    public static final <T> List<Pair<T, T>> zipWithNext(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Iterator iterator$iv = iterable.iterator();
        if (!iterator$iv.hasNext()) {
            return CollectionsKt.emptyList();
        }
        List result$iv = new ArrayList();
        Object current$iv = iterator$iv.next();
        while (iterator$iv.hasNext()) {
            Object next$iv = iterator$iv.next();
            Object a = current$iv;
            result$iv.add(TuplesKt.to(a, next$iv));
            current$iv = next$iv;
        }
        return result$iv;
    }

    public static final <T, R> List<R> zipWithNext(Iterable<? extends T> iterable, Function2<? super T, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Iterator iterator = iterable.iterator();
        if (!iterator.hasNext()) {
            return CollectionsKt.emptyList();
        }
        List result = new ArrayList();
        Object current = iterator.next();
        while (iterator.hasNext()) {
            Object next = iterator.next();
            result.add(transform.invoke(current, next));
            current = next;
        }
        return result;
    }

    public static final <T, A extends Appendable> A joinTo(Iterable<? extends T> iterable, A buffer, CharSequence separator, CharSequence prefix, CharSequence postfix, int limit, CharSequence truncated, Function1<? super T, ? extends CharSequence> function1) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(buffer, "buffer");
        Intrinsics.checkNotNullParameter(separator, "separator");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Intrinsics.checkNotNullParameter(postfix, "postfix");
        Intrinsics.checkNotNullParameter(truncated, "truncated");
        buffer.append(prefix);
        int count = 0;
        for (Object element : iterable) {
            count++;
            if (count > 1) {
                buffer.append(separator);
            }
            if (limit >= 0 && count > limit) {
                break;
            }
            StringsKt.appendElement(buffer, element, function1);
        }
        if (limit >= 0 && count > limit) {
            buffer.append(truncated);
        }
        buffer.append(postfix);
        return buffer;
    }

    public static /* synthetic */ String joinToString$default(Iterable iterable, CharSequence charSequence, CharSequence charSequence2, CharSequence charSequence3, int i, CharSequence charSequence4, Function1 function1, int i2, Object obj) {
        if ((i2 & 1) != 0) {
        }
        if ((i2 & 2) != 0) {
        }
        CharSequence charSequence5 = charSequence2;
        if ((i2 & 4) != 0) {
        }
        CharSequence charSequence6 = charSequence3;
        if ((i2 & 8) != 0) {
            i = -1;
        }
        int i3 = i;
        if ((i2 & 16) != 0) {
        }
        CharSequence charSequence7 = charSequence4;
        if ((i2 & 32) != 0) {
            function1 = null;
        }
        return CollectionsKt.joinToString(iterable, charSequence, charSequence5, charSequence6, i3, charSequence7, function1);
    }

    public static final <T> String joinToString(Iterable<? extends T> iterable, CharSequence separator, CharSequence prefix, CharSequence postfix, int limit, CharSequence truncated, Function1<? super T, ? extends CharSequence> function1) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        Intrinsics.checkNotNullParameter(separator, "separator");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Intrinsics.checkNotNullParameter(postfix, "postfix");
        Intrinsics.checkNotNullParameter(truncated, "truncated");
        String sb = ((StringBuilder) CollectionsKt.joinTo(iterable, new StringBuilder(), separator, prefix, postfix, limit, truncated, function1)).toString();
        Intrinsics.checkNotNullExpressionValue(sb, "joinTo(StringBuilder(), …ed, transform).toString()");
        return sb;
    }

    /* JADX WARN: Multi-variable type inference failed */
    private static final <T> Iterable<T> asIterable(Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return iterable;
    }

    public static final <T> Sequence<T> asSequence(final Iterable<? extends T> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        return new Sequence<T>() { // from class: kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                return iterable.iterator();
            }
        };
    }

    public static final double averageOfByte(Iterable<Byte> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Byte> it = iterable.iterator();
        while (it.hasNext()) {
            byte element = it.next().byteValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfShort(Iterable<Short> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Short> it = iterable.iterator();
        while (it.hasNext()) {
            short element = it.next().shortValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfInt(Iterable<Integer> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Integer> it = iterable.iterator();
        while (it.hasNext()) {
            int element = it.next().intValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfLong(Iterable<Long> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Long> it = iterable.iterator();
        while (it.hasNext()) {
            long element = it.next().longValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfFloat(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Float> it = iterable.iterator();
        while (it.hasNext()) {
            float element = it.next().floatValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfDouble(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Double> it = iterable.iterator();
        while (it.hasNext()) {
            double element = it.next().doubleValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final int sumOfByte(Iterable<Byte> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        int sum = 0;
        Iterator<Byte> it = iterable.iterator();
        while (it.hasNext()) {
            byte element = it.next().byteValue();
            sum += element;
        }
        return sum;
    }

    public static final int sumOfShort(Iterable<Short> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        int sum = 0;
        Iterator<Short> it = iterable.iterator();
        while (it.hasNext()) {
            short element = it.next().shortValue();
            sum += element;
        }
        return sum;
    }

    public static final int sumOfInt(Iterable<Integer> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        int sum = 0;
        Iterator<Integer> it = iterable.iterator();
        while (it.hasNext()) {
            int element = it.next().intValue();
            sum += element;
        }
        return sum;
    }

    public static final long sumOfLong(Iterable<Long> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        long sum = 0;
        Iterator<Long> it = iterable.iterator();
        while (it.hasNext()) {
            long element = it.next().longValue();
            sum += element;
        }
        return sum;
    }

    public static final float sumOfFloat(Iterable<Float> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        float sum = 0.0f;
        Iterator<Float> it = iterable.iterator();
        while (it.hasNext()) {
            float element = it.next().floatValue();
            sum += element;
        }
        return sum;
    }

    public static final double sumOfDouble(Iterable<Double> iterable) {
        Intrinsics.checkNotNullParameter(iterable, "<this>");
        double sum = 0.0d;
        Iterator<Double> it = iterable.iterator();
        while (it.hasNext()) {
            double element = it.next().doubleValue();
            sum += element;
        }
        return sum;
    }
}
