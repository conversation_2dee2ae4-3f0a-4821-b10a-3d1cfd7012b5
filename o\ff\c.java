package o.ff;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.ee.g;
import o.eg.b;
import o.eg.e;
import org.bouncycastle.math.Primes;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ff\c.smali */
public final class c extends o.fc.d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static int g;
    private static byte[] h;
    private static char[] i;
    private static int j;
    private static int l;
    private static short[] m;

    /* renamed from: o, reason: collision with root package name */
    private static int f85o;
    private List<d> a;
    private int b;
    private short c;
    private byte d;
    private String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        f85o = 1;
        l();
        ViewConfiguration.getMaximumDrawingCacheSize();
        ViewConfiguration.getKeyRepeatTimeout();
        Color.argb(0, 0, 0, 0);
        Gravity.getAbsoluteGravity(0, 0);
        Color.rgb(0, 0, 0);
        Color.blue(0);
        TextUtils.indexOf("", "", 0, 0);
        ViewConfiguration.getMinimumFlingVelocity();
        ExpandableListView.getPackedPositionForGroup(0);
        Process.myPid();
        TextUtils.indexOf((CharSequence) "", '0');
        PointF.length(0.0f, 0.0f);
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getMinimumFlingVelocity();
        TextUtils.lastIndexOf("", '0');
        int i2 = f85o + 9;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$a = new byte[]{43, -103, 93, -106};
        $$b = 4;
    }

    static void l() {
        i = new char[]{50825, 50765, 50874, 50935, 50872, 50876, 50935, 50849, 50854, 50820, 50912, 50912, 50927, 50817, 50855, 50854, 50850, 50855, 50853, 50836, 50836, 50851, 50875, 50862, 50835, 50854, 50855, 50852, 50853, 50832, 50857, 50849, 50837, 50837, 50850, 50834, 50839, 50877, 50860, 50837, 50848, 50878, 50852, 50852, 50876, 50823, 50923, 50923, 50824, 50853, 50850, 50817, 50935, 50857, 50855, 50857, 50859, 50831, 50937, 50837, 50851, 50878, 50876, 50877, 50872, 50817, 50939, 50837, 50852, 50855, 50848, 50851, 50825, 50912, 50912, 50927, 50817, 50820, 50923, 50923, 50827, 50858, 50849, 50817, 50933, 50837, 50848, 50878, 50852, 50852, 50876, 50823, 50912, 50912, 50927, 50817, 50820, 50923, 50923, 50820, 50849, 50854, 50878, 50848, 50852, 50853, 50857, 50831, 50828, 50852, 50879, 50876, 50909, 50827, 50876, 50876, 50854, 50877, 50849, 50858, 50827, 50820, 50878, 50851, 50825, 50831, 50857, 50859, 50856, 50863, 50862, 50854, 50820, 50820, 50855, 50857, 50825, 50817, 50850, 50853, 50824, 50923, 50923, 50823, 50876, 50852, 50852, 50878, 50848, 50837, 50860, 50877, 50839, 50834, 50850, 50837, 50837, 50849, 50857, 50832, 50853, 50852, 50855, 50854, 50835, 50862, 50875, 50851, 50836, 50836, 50853, 50855, 50850, 50854, 50855, 50854, 50862, 50858, 50876, 50876, 50826, 50823, 50876, 50852, 50852, 50878, 50848, 50853, 50943, 50848, 50877, 50859, 50854, 50822, 50827, 50876, 50876, 50854, 50877, 50849, 50858, 50827, 50831, 50859, 50851, 50823, 50831, 50857, 50859, 50856, 50863, 50862, 50854, 50820, 50820, 50855, 50857, 50825, 50940, 50822, 50823, 50941, 50923, 50923, 50823, 50876, 50852, 50852, 50878, 50848, 50837, 50860, 50877, 50839, 50834, 50850, 50837, 50837, 50849, 50857, 50832, 50853, 50852, 50855, 50854, 50835, 50862, 50875, 50851, 50836, 50836, 50853, 50855, 50850, 50854};
        h = new byte[]{-65, ByteCompanionObject.MIN_VALUE, -110, -84, ByteCompanionObject.MIN_VALUE, -120, 50, 19, 20, -18, 83, -30, -31, 48, 52, Tnaf.POW_2_WIDTH, 25, 27, 26, -2, 23, 101, 120, 125, -111, ByteCompanionObject.MAX_VALUE, 118, -105, 113, Base64.padSymbol, -38, 97, 125, 60, -39, 120, 53, -61, 114, 104, 55, 111, 121, -38, 100, 117, 102, -110, 70, 77, -116, ByteCompanionObject.MAX_VALUE, 85, -71, 114, 72, -124, -112, 76, 110, 101, 97, 101, 97, 116, -117, 106, 79, 115, -104, 102, ByteCompanionObject.MAX_VALUE, -110, 79, 4, -51, -53, -21, -66, 67, 28, -52, -16, 5, -1, 0, -119, 91, 25, 0, -96, 88, 23, 25, -15, -14, 24, 3, -94, 41, -30, 29, -116, 38, -55, 5, -50, 29, -50, -56, -77, 91, -14, 1, -51, 4, -16, -56, -30, -61, 3, -55, 40, -50, -51, 28, 0, -4, -27, 54, -13, -83, 9, -32, -6, -18, 2, -10, 4, 29, -15, 29, -15, -2, 55, 24, -29, -1, -22, 28, -13, 0, -29, -112, -112, -112, -112, -112, -112};
        f = 909053647;
        j = -203112264;
        g = -158354576;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r7 = r7 + 66
            byte[] r0 = o.ff.c.$$a
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ff.c.q(byte, int, short, java.lang.Object[]):void");
    }

    public c(boolean z, o.fc.c cVar, short s) {
        super(z, cVar, s);
    }

    public final int f() {
        int i2 = f85o;
        int i3 = i2 + 93;
        l = i3 % 128;
        int i4 = i3 % 2;
        int i5 = this.b;
        int i6 = i2 + 75;
        l = i6 % 128;
        switch (i6 % 2 != 0 ? 'b' : (char) 20) {
            case Opcodes.FADD /* 98 */:
                int i7 = 3 / 0;
                return i5;
            default:
                return i5;
        }
    }

    public final void a(int i2) {
        int i3 = f85o + 73;
        l = i3 % 128;
        char c = i3 % 2 != 0 ? (char) 30 : ';';
        this.b = i2;
        switch (c) {
            case ';':
                return;
            default:
                throw null;
        }
    }

    public final short j() {
        int i2 = l + 41;
        f85o = i2 % 128;
        switch (i2 % 2 != 0 ? '\\' : (char) 2) {
            case 2:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c;
        }
    }

    public final void c(short s) {
        int i2 = l;
        int i3 = i2 + 49;
        f85o = i3 % 128;
        boolean z = i3 % 2 != 0;
        this.c = s;
        switch (z) {
            case false:
                throw null;
            default:
                int i4 = i2 + 11;
                f85o = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
        }
    }

    public final List<d> h() {
        int i2 = l;
        int i3 = i2 + 75;
        f85o = i3 % 128;
        int i4 = i3 % 2;
        List<d> list = this.a;
        int i5 = i2 + 83;
        f85o = i5 % 128;
        int i6 = i5 % 2;
        return list;
    }

    public final void d(List<d> list) {
        int i2 = l;
        int i3 = i2 + 49;
        f85o = i3 % 128;
        int i4 = i3 % 2;
        this.a = list;
        int i5 = i2 + 55;
        f85o = i5 % 128;
        switch (i5 % 2 == 0 ? Typography.dollar : ',') {
            case ',':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final byte i() {
        int i2 = l + 33;
        int i3 = i2 % 128;
        f85o = i3;
        int i4 = i2 % 2;
        byte b = this.d;
        int i5 = i3 + Opcodes.LREM;
        l = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                return b;
            default:
                int i6 = 34 / 0;
                return b;
        }
    }

    public final void c(byte b) {
        int i2 = f85o;
        int i3 = i2 + 31;
        l = i3 % 128;
        boolean z = i3 % 2 != 0;
        Object obj = null;
        this.d = b;
        switch (z) {
            case false:
                int i4 = i2 + 79;
                l = i4 % 128;
                switch (i4 % 2 != 0 ? '!' : (char) 17) {
                    case '!':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    public final String o() {
        String str;
        int i2 = l;
        int i3 = i2 + 11;
        f85o = i3 % 128;
        switch (i3 % 2 == 0 ? ';' : '\b') {
            case ';':
                str = this.e;
                int i4 = 67 / 0;
                break;
            default:
                str = this.e;
                break;
        }
        int i5 = i2 + 79;
        f85o = i5 % 128;
        switch (i5 % 2 == 0 ? 'G' : '_') {
            case 'G':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final void a(String str) {
        int i2 = l + 29;
        int i3 = i2 % 128;
        f85o = i3;
        int i4 = i2 % 2;
        this.e = str;
        int i5 = i3 + 47;
        l = i5 % 128;
        switch (i5 % 2 != 0 ? '\b' : '0') {
            case '0':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final e b(int i2) throws o.eg.d {
        int i3 = f85o + 37;
        l = i3 % 128;
        int i4 = 2;
        Object obj = null;
        if (i3 % 2 != 0) {
            obj.hashCode();
            throw null;
        }
        switch (this.c != 0) {
            case true:
                break;
            default:
                if (this.a.size() == 0) {
                    int i5 = f85o;
                    int i6 = i5 + 31;
                    l = i6 % 128;
                    int i7 = i6 % 2;
                    int i8 = i5 + 37;
                    l = i8 % 128;
                    int i9 = i8 % 2;
                    return null;
                }
                break;
        }
        e eVar = new e();
        Iterator<d> it = this.a.iterator();
        int i10 = 0;
        while (true) {
            long j2 = 0;
            if (!it.hasNext()) {
                int i11 = 0;
                while (i11 < this.c - this.a.size()) {
                    b bVar = new b();
                    int[] iArr = {0, 3, 21, i4};
                    Object[] objArr = new Object[1];
                    k("\u0001\u0001\u0000", iArr, false, objArr);
                    bVar.d(((String) objArr[0]).intern(), i2 + i10 + i11);
                    Object[] objArr2 = new Object[1];
                    p((byte) (ExpandableListView.getPackedPositionType(j2) + Opcodes.LREM), 1063207456 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (short) (Color.rgb(0, 0, 0) + 16777312), (-89) - (KeyEvent.getMaxKeyCode() >> 16), 976499275 - TextUtils.indexOf("", ""), objArr2);
                    bVar.d(((String) objArr2[0]).intern(), b().c());
                    eVar.b(bVar);
                    i11++;
                    i4 = 2;
                    j2 = 0;
                }
                return eVar;
            }
            d next = it.next();
            b bVar2 = new b();
            Object[] objArr3 = new Object[1];
            k("\u0001\u0001\u0000", new int[]{0, 3, 21, 2}, false, objArr3);
            bVar2.d(((String) objArr3[0]).intern(), i2 + i10);
            Object[] objArr4 = new Object[1];
            p((byte) (113 - (ViewConfiguration.getPressedStateDuration() >> 16)), ExpandableListView.getPackedPositionType(0L) + 1063207456, (short) ((-16777120) - Color.rgb(0, 0, 0)), (-89) - Color.green(0), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 976499275, objArr4);
            bVar2.d(((String) objArr4[0]).intern(), next.d().c());
            i10++;
            eVar.b(bVar2);
        }
    }

    public final b n() throws o.eg.d {
        List<d> subList;
        b bVar = new b();
        Object[] objArr = new Object[1];
        p((byte) (77 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 1063207461 - (ViewConfiguration.getTapTimeout() >> 16), (short) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 96), (-92) - (ViewConfiguration.getEdgeSlop() >> 16), ((byte) KeyEvent.getModifierMetaStateMask()) + 976499258, objArr);
        bVar.d(((String) objArr[0]).intern(), this.e);
        Object[] objArr2 = new Object[1];
        p((byte) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 98), 1063207463 - Color.argb(0, 0, 0, 0), (short) (KeyEvent.keyCodeFromString("") - 20), Gravity.getAbsoluteGravity(0, 0) - 93, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 976499275, objArr2);
        bVar.d(((String) objArr2[0]).intern(), this.b);
        e eVar = new e();
        switch (this.a.size() > 100 ? '/' : '=') {
            case '/':
                int i2 = f85o + Opcodes.DMUL;
                l = i2 % 128;
                int i3 = i2 % 2;
                List<d> list = this.a;
                subList = list.subList(list.size() - 100, this.a.size());
                break;
            default:
                subList = this.a;
                break;
        }
        Iterator<d> it = subList.iterator();
        while (true) {
            switch (it.hasNext()) {
                case false:
                    Object[] objArr3 = new Object[1];
                    k("\u0000\u0000\u0000", new int[]{3, 3, 0, 0}, false, objArr3);
                    bVar.d(((String) objArr3[0]).intern(), eVar);
                    int i4 = f85o + 43;
                    l = i4 % 128;
                    int i5 = i4 % 2;
                    return bVar;
                default:
                    eVar.e(eVar.d(), it.next().c());
            }
        }
    }

    public final o.fc.e d(int i2) {
        int i3 = f85o + 43;
        l = i3 % 128;
        int i4 = i3 % 2;
        Object[] objArr = new Object[1];
        p((byte) (Process.getGidForName("") + Opcodes.DDIV), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1063207463, (short) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) - 10), TextUtils.lastIndexOf("", '0', 0) - 80, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 976499246, objArr);
        String intern = ((String) objArr[0]).intern();
        if (!a()) {
            int i5 = f85o + 17;
            l = i5 % 128;
            int i6 = i5 % 2;
            g.c();
            Object[] objArr2 = new Object[1];
            p((byte) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 7), (ViewConfiguration.getFadingEdgeLength() >> 16) + 1063207477, (short) (TextUtils.indexOf("", "", 0) + 11), (ViewConfiguration.getEdgeSlop() >> 16) - 41, 976499258 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr2);
            g.e(intern, ((String) objArr2[0]).intern());
            return null;
        }
        g.c();
        Object[] objArr3 = new Object[1];
        k("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001", new int[]{6, Opcodes.FMUL, 0, 8}, false, objArr3);
        g.d(intern, String.format(((String) objArr3[0]).intern(), Integer.valueOf(this.a.size()), Short.valueOf(this.c), Integer.valueOf(this.b)));
        switch (this.a.size() >= this.c ? 'G' : '@') {
            case 'G':
                int i7 = l + 87;
                f85o = i7 % 128;
                int i8 = i7 % 2;
                g.c();
                Object[] objArr4 = new Object[1];
                k("\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001", new int[]{Opcodes.IREM, 76, 0, 62}, true, objArr4);
                g.d(intern, ((String) objArr4[0]).intern());
                break;
        }
        if (this.b >= 65535) {
            g.c();
            Object[] objArr5 = new Object[1];
            p((byte) ((ViewConfiguration.getKeyRepeatDelay() >> 16) - 18), 1063207530 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (short) (KeyEvent.normalizeMetaState(0) - 118), View.combineMeasuredStates(0, 0) - 15, ImageFormat.getBitsPerPixel(0) + 976499259, objArr5);
            g.e(intern, ((String) objArr5[0]).intern());
            return null;
        }
        switch (this.a.size() + i2 >= 65535 ? ']' : '!') {
            case '!':
                break;
            default:
                g.c();
                Object[] objArr6 = new Object[1];
                k("\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001", new int[]{188, 67, 0, 0}, true, objArr6);
                g.e(intern, ((String) objArr6[0]).intern());
                break;
        }
        return null;
    }

    @Override // o.fc.d
    public final boolean e(String str, o.dd.e eVar) {
        int i2 = f85o + 73;
        l = i2 % 128;
        int i3 = i2 % 2;
        switch (b() == o.fc.c.b) {
            case false:
                int i4 = l + 33;
                f85o = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return false;
                    default:
                        int i5 = 64 / 0;
                        return false;
                }
            default:
                int i6 = l + Opcodes.DDIV;
                f85o = i6 % 128;
                if (i6 % 2 == 0) {
                }
                c(o.fc.c.a);
                return true;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x001e, code lost:
    
        if (r0 > 0) goto L15;
     */
    @Override // o.fc.d
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final short e() {
        /*
            r4 = this;
            int r0 = o.ff.c.l
            int r0 = r0 + 99
            int r1 = r0 % 128
            o.ff.c.f85o = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 0: goto L21;
                default: goto L14;
            }
        L14:
            short r0 = r4.c
            java.util.List<o.ff.c$d> r1 = r4.a
            int r1 = r1.size()
            int r0 = r0 - r1
            short r0 = (short) r0
            if (r0 <= 0) goto L33
        L20:
            goto L32
        L21:
            short r0 = r4.c
            java.util.List<o.ff.c$d> r3 = r4.a
            int r3 = r3.size()
            int r0 = r0 << r3
            short r0 = (short) r0
            if (r0 <= 0) goto L2e
            r1 = r2
        L2e:
            switch(r1) {
                case 1: goto L33;
                default: goto L31;
            }
        L31:
            goto L20
        L32:
            return r0
        L33:
            int r0 = o.ff.c.l
            int r0 = r0 + 67
            int r1 = r0 % 128
            o.ff.c.f85o = r1
            int r0 = r0 % 2
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ff.c.e():short");
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ff\c$d.smali */
    public static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int f;
        private static int h;
        private static char[] i;
        private static long j;
        private final String a;
        private o.fc.c b;
        private final byte[] c;
        private final long d;
        private int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            f = 1;
            j();
            ExpandableListView.getPackedPositionType(0L);
            Color.red(0);
            TextUtils.getCapsMode("", 0, 0);
            ViewConfiguration.getScrollFriction();
            TextUtils.lastIndexOf("", '0');
            ViewConfiguration.getEdgeSlop();
            int i2 = h + 65;
            f = i2 % 128;
            int i3 = i2 % 2;
        }

        static void init$0() {
            $$a = new byte[]{74, -5, -75, 11};
            $$b = Primes.SMALL_FACTOR_LIMIT;
        }

        static void j() {
            i = new char[]{11445, 44352};
            j = -211649048354096370L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r6, short r7, byte r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.ff.c.d.$$a
                int r7 = r7 * 4
                int r7 = 3 - r7
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r6 = r6 + 102
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L19:
                r3 = r2
            L1a:
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r8) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                int r7 = r7 + 1
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r8
                r8 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r6 = -r6
                int r6 = r6 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ff.c.d.l(short, short, byte, java.lang.Object[]):void");
        }

        public d(String str, long j2, byte[] bArr, int i2, o.fc.c cVar) {
            this.a = str;
            this.d = j2;
            this.c = bArr;
            this.e = i2;
            this.b = cVar;
        }

        public final String c() {
            StringBuilder append = new StringBuilder().append(String.valueOf(this.d));
            Object[] objArr = new Object[1];
            k((char) TextUtils.indexOf("", "", 0, 0), KeyEvent.normalizeMetaState(0), View.MeasureSpec.makeMeasureSpec(0, 0) + 1, objArr);
            StringBuilder append2 = append.append(((String) objArr[0]).intern()).append(o.dk.b.e(this.c));
            Object[] objArr2 = new Object[1];
            k((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), ((byte) KeyEvent.getModifierMetaStateMask()) + 1, 1 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
            StringBuilder append3 = append2.append(((String) objArr2[0]).intern()).append(this.e);
            Object[] objArr3 = new Object[1];
            k((char) (ViewConfiguration.getWindowTouchSlop() >> 8), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), Color.rgb(0, 0, 0) + 16777217, objArr3);
            StringBuilder append4 = append3.append(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            k((char) (33240 - TextUtils.indexOf("", "", 0, 0)), 1 - KeyEvent.normalizeMetaState(0), 1 - ExpandableListView.getPackedPositionGroup(0L), objArr4);
            String obj = append4.append(((String) objArr4[0]).intern()).toString();
            int i2 = f + Opcodes.DSUB;
            h = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    return obj;
                default:
                    throw null;
            }
        }

        public final String e() {
            int i2 = f;
            int i3 = i2 + 77;
            h = i3 % 128;
            int i4 = i3 % 2;
            String str = this.a;
            int i5 = i2 + 1;
            h = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        public final o.fc.c d() {
            int i2 = f + 35;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? ']' : '8') {
                case '8':
                    return this.b;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public final long b() {
            int i2 = f + 63;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return this.d;
            }
        }

        public final byte[] a() {
            int i2 = h + Opcodes.DSUB;
            f = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    int i3 = 31 / 0;
                    return this.c;
                default:
                    return this.c;
            }
        }

        public final int g() {
            int i2 = h;
            int i3 = i2 + 43;
            f = i3 % 128;
            switch (i3 % 2 == 0 ? 'L' : 'c') {
                case Base64.mimeLineLength /* 76 */:
                    throw null;
                default:
                    int i4 = this.e;
                    int i5 = i2 + 27;
                    f = i5 % 128;
                    int i6 = i5 % 2;
                    return i4;
            }
        }

        public final void b(int i2) {
            int i3 = f;
            int i4 = i3 + 25;
            h = i4 % 128;
            int i5 = i4 % 2;
            this.e = i2;
            int i6 = i3 + Opcodes.DREM;
            h = i6 % 128;
            int i7 = i6 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(char r24, int r25, int r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 994
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ff.c.d.k(char, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:87:0x027b, code lost:
    
        r2 = r3;
     */
    /* JADX WARN: Removed duplicated region for block: B:32:0x00c0  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 802
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ff.c.k(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:85:0x02c1, code lost:
    
        r3 = r7;
     */
    /* JADX WARN: Removed duplicated region for block: B:106:0x02ea  */
    /* JADX WARN: Removed duplicated region for block: B:94:0x02e7  */
    /* JADX WARN: Removed duplicated region for block: B:96:0x02f0  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 898
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ff.c.p(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
