package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.WNafUtil;
import bc.org.bouncycastle.math.ec.endo.GLVTypeBEndomorphism;
import bc.org.bouncycastle.math.ec.endo.GLVTypeBParameters;
import bc.org.bouncycastle.math.ec.endo.ScalarSplitParameters;
import com.esotericsoftware.asm.Opcodes;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7.smali */
public class a7 {
    static e8 a = new k();
    static e8 b = new v();
    static e8 c = new a0();
    static e8 d = new b0();
    static e8 e = new c0();
    static e8 f = new d0();
    static e8 g = new e0();
    static e8 h = new f0();
    static e8 i = new g0();
    static e8 j = new a();
    static e8 k = new b();
    static e8 l = new c();
    static e8 m = new d();
    static e8 n = new e();

    /* renamed from: o, reason: collision with root package name */
    static e8 f20o = new f();
    static e8 p = new g();
    static e8 q = new h();
    static e8 r = new i();
    static e8 s = new j();
    static e8 t = new l();
    static e8 u = new m();
    static e8 v = new n();
    static e8 w = new o();
    static e8 x = new p();
    static e8 y = new q();
    static e8 z = new r();
    static e8 A = new s();
    static e8 B = new t();
    static e8 C = new u();
    static e8 D = new w();
    static e8 E = new x();
    static e8 F = new y();
    static e8 G = new z();
    static final Hashtable H = new Hashtable();
    static final Hashtable I = new Hashtable();
    static final Hashtable J = new Hashtable();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$a.smali */
    class a extends e8 {
        a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFE56D"), ECConstants.ZERO, BigInteger.valueOf(5L), a7.b("010000000000000000000000000001DCE8D2EC6184CAF0A971769FB1F7"), BigInteger.valueOf(1L), true), new GLVTypeBParameters(new BigInteger("fe0e87005b4e83761908c5131d552a850b3f58b749c37cf5b84d6768", 16), new BigInteger("60dcd2104c4cbc0be6eeefc2bdd610739ec34e317f9b33046c9e4788", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("6b8cf07d4ca75c88957d9d670591", 16), new BigInteger("-b8adf1378a6eb73409fa6c9c637d", 16)}, new BigInteger[]{new BigInteger("1243ae1b4d71613bc9f780a03690e", 16), new BigInteger("6b8cf07d4ca75c88957d9d670591", 16)}, new BigInteger("6b8cf07d4ca75c88957d9d67059037a4", 16), new BigInteger("b8adf1378a6eb73409fa6c9c637ba7f5", 16), 240)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04A1455B334DF099DF30FC28A169A467E9E47075A90F7E650EB6B7A45C7E089FED7FBA344282CAFBD6F7E319F7C0B0BD59E2CA4BDB556D61A5"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$b.smali */
    class b extends e8 {
        b() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE"), a7.b("B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("BD71344799D5C7FCDC45B59FA3B9AB8F6A948BC5");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$c.smali */
    class c extends e8 {
        c() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F"), ECConstants.ZERO, BigInteger.valueOf(7L), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEBAAEDCE6AF48A03BBFD25E8CD0364141"), BigInteger.valueOf(1L), true), new GLVTypeBParameters(new BigInteger("7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee", 16), new BigInteger("5363ad4cc05c30e0a5261c028812645a122e22ea20816678df02967c1b23bd72", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("3086d221a7d46bcde86c90e49284eb15", 16), new BigInteger("-e4437ed6010e88286f547fa90abfe4c3", 16)}, new BigInteger[]{new BigInteger("114ca50f7a8e2f3f657c1108d9d44cfd8", 16), new BigInteger("3086d221a7d46bcde86c90e49284eb15", 16)}, new BigInteger("3086d221a7d46bcde86c90e49284eb153dab", 16), new BigInteger("e4437ed6010e88286f547fa90abfe4c42212", 16), 272)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0479BE667EF9DCBBAC55A06295CE870B07029BFCDB2DCE28D959F2815B16F81798483ADA7726A3C4655DA4FBFC0E1108A8FD17B448A68554199C47D08FFB10D4B8"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$c0.smali */
    class c0 extends e8 {
        c0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73"), ECConstants.ZERO, BigInteger.valueOf(7L), a7.b("0100000000000000000001B8FA16DFAB9ACA16B6B3"), BigInteger.valueOf(1L), true), new GLVTypeBParameters(new BigInteger("9ba48cba5ebcb9b6bd33b92830b2a2e0e192f10a", 16), new BigInteger("c39c6c3b3a36d7701b9c71a1f5804ae5d0003f4", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("9162fbe73984472a0a9e", 16), new BigInteger("-96341f1138933bc2f505", 16)}, new BigInteger[]{new BigInteger("127971af8721782ecffa3", 16), new BigInteger("9162fbe73984472a0a9e", 16)}, new BigInteger("9162fbe73984472a0a9d0590", 16), new BigInteger("96341f1138933bc2f503fd44", 16), Opcodes.ARETURN)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "043B4C382CE37AA192A4019E763036F4F5DD4D7EBB938CF935318FDCED6BC28286531733C3F03C4FEE"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$d.smali */
    class d extends e8 {
        d() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF"), a7.b("FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC"), a7.b("5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B"), a7.b("FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("C49D360886E704936A6678E1139D26B7819F7E90");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "046B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C2964FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$d0.smali */
    class d0 extends e8 {
        d0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC"), a7.b("1C97BEFC54BD7A8B65ACF89F81D4D4ADC565FA45"), a7.b("0100000000000000000001F4C8F927AED3CA752257"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("1053CDE42C14D696E67687561517533BF3F83345");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "044A96B5688EF573284664698968C38BB913CBFC8223A628553168947D59DCC912042351377AC5FB32"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$e.smali */
    class e extends e8 {
        e() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFF"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFF0000000000000000FFFFFFFC"), a7.b("B3312FA7E23EE7E4988E056BE3F82D19181D9C6EFE8141120314088F5013875AC656398D8A2ED19D2A85C8EDD3EC2AEF"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC7634D81F4372DDF581A0DB248B0A77AECEC196ACCC52973"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("A335926AA319A27A1D00896A6773A4827ACDAC73");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04AA87CA22BE8B05378EB1C71EF320AD746E1D3B628BA79B9859F741E082542A385502F25DBF55296C3A545E3872760AB73617DE4A96262C6F5D9E98BF9292DC29F8F41DBD289A147CE9DA3113B5F0B8C00A60B1CE1D7E819D7A431D7C90EA0E5F"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$e0.smali */
    class e0 extends e8 {
        e0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC70"), a7.b("B4E134D3FB59EB8BAB57274904664D5AF50388BA"), a7.b("0100000000000000000000351EE786A818F3A1A16B"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("B99B99B099B323E02709A4D696E6768756151751");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0452DCB034293A117E1F4FF11B30F7199D3144CE6DFEAFFEF2E331F296E071FA0DF9982CFEA7D43F2E"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$f.smali */
    class f extends e8 {
        f() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"), a7.b("01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC"), a7.b("0051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00"), a7.b("01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("D09E8800291CB85396CC6717393284AAA0DA64BA");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0400C6858E06B70404E9CD9E3ECB662395B4429C648139053FB521F828AF606B4D3DBAA14B5E77EFE75928FE1DC127A2FFA8DE3348B3C1856A429BF97E7E31C2E5BD66011839296A789A3BC0045C8A5FB42C7D1BD998F54449579B446817AFBD17273E662C97EE72995EF42640C550B9013FAD0761353C7086A272C24088BE94769FD16650"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$f0.smali */
    class f0 extends e8 {
        f0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37"), ECConstants.ZERO, BigInteger.valueOf(3L), a7.b("FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D"), BigInteger.valueOf(1L), true), new GLVTypeBParameters(new BigInteger("bb85691939b869c1d087f601554b96b80cb4f55b35f433c2", 16), new BigInteger("3d84f26c12238d7b4f3d516613c1759033b1a5800175d0b1", 16), new ScalarSplitParameters(new BigInteger[]{new BigInteger("71169be7330b3038edb025f1", 16), new BigInteger("-b3fb3400dec5c4adceb8655c", 16)}, new BigInteger[]{new BigInteger("12511cfe811d0f4e6bc688b4d", 16), new BigInteger("71169be7330b3038edb025f1", 16)}, new BigInteger("71169be7330b3038edb025f1d0f9", 16), new BigInteger("b3fb3400dec5c4adceb8655d4c94", 16), 208)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$g.smali */
    class g extends e8 {
        g() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.LREM, 9, a7.b("003088250CA6E7C7FE649CE85820F7"), a7.b("00E8BEE4D3E2260744188BE0E9C723"), a7.b("0100000000000000D9CCEC8A39E56F"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("10E723AB14D696E6768756151756FEBF8FCB49A9");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04009D73616F35F4AB1407D73562C10F00A52830277958EE84D1315ED31886"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$g0.smali */
    class g0 extends e8 {
        g0() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC"), a7.b("64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1"), a7.b("FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("3045AE6FC8422F64ED579528D38120EAE12196D5");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF101207192B95FFC8DA78631011ED6B24CDD573F977A11E794811"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$h.smali */
    class h extends e8 {
        h() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.LREM, 9, a7.b("00689918DBEC7E5A0DD6DFC0AA55C7"), a7.b("0095E9A9EC9B297BD4BF36E059184F"), a7.b("010000000000000108789B2496AF93"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("10C0FB15760860DEF1EEF4D696E676875615175D");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0401A57A6A7B26CA5EF52FCDB816479700B3ADC94ED1FE674C06E695BABA1D"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$i.smali */
    class i extends e8 {
        i() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.LXOR, 2, 3, 8, a7.b("07A11B09A76B562144418FF3FF8C2570B8"), a7.b("0217C05610884B63B9C6C7291678F9D341"), a7.b("0400000000000000023123953A9464B54D"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("4D696E676875615175985BD3ADBADA21B43A97E2");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "040081BAF91FDF9833C40F9C181343638399078C6E7EA38C001F73C8134B1B4EF9E150"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$j.smali */
    class j extends e8 {
        j() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.LXOR, 2, 3, 8, a7.b("03E5A88919D7CAFCBF415F07C2176573B2"), a7.b("04B8266A46C55657AC734CE38F018F2192"), a7.b("0400000000000000016954A233049BA98F"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("985BD3ADBAD4D696E676875615175A21B43A97E3");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "040356DCD8F2F95031AD652D23951BB366A80648F06D867940A5366D9E265DE9EB240F"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$k.smali */
    class k extends e8 {
        k() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("DB7C2ABF62E35E668076BEAD208B"), a7.b("DB7C2ABF62E35E668076BEAD2088"), a7.b("659EF8BA043916EEDE8911702B22"), a7.b("DB7C2ABF62E35E7628DFAC6561C5"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("00F50B028E4D696E676875615175290472783FB1");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0409487239995A5EE76B55F9C2F098A89CE5AF8724C0A23E0E0FF77500"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$m.smali */
    class m extends e8 {
        m() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.IF_ICMPGT, 3, 6, 7, a7.b("07B6882CAAEFA84F9554FF8428BD88E246D2782AE2"), a7.b("0713612DCDDCB40AAB946BDA29CA91F73AF958AFD9"), a7.b("03FFFFFFFFFFFFFFFFFFFF48AAB689C29CA710279B"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("24B7B137C8A14D696E6768756151756FD0DA2E5C");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "040369979697AB43897789566789567F787A7876A65400435EDB42EFAFB2989D51FEFCE3C80988F41FF883"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$n.smali */
    class n extends e8 {
        n() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.IF_ICMPGT, 3, 6, 7, BigInteger.valueOf(1L), a7.b("020A601907B8C953CA1481EB10512F78744A3205FD"), a7.b("040000000000000000000292FE77E70C12A4234C33"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("85E25BFE5C86226CDB12016F7553F9D0E693A268");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0403F0EBA16286A2D57EA0991168D4994637E8343E3600D51FBC6C71A0094FA2CDD545B11C5C0C797324F1"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$o.smali */
    class o extends e8 {
        o() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(Opcodes.INSTANCEOF, 15, a7.b("0017858FEB7A98975169E171F77B4087DE098AC8A911DF7B01"), a7.b("00FDFB49BFE6C3A89FACADAA7A1E5BBC7CC1C2E5D831478814"), a7.b("01000000000000000000000000C7F34A778F443ACC920EBA49"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("103FAEC74D696E676875615175777FC5B191EF30");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0401F481BC5F0FF84A74AD6CDF6FDEF4BF6179625372D8C0C5E10025E399F2903712CCF3EA9E3A1AD17FB0B3201B6AF7CE1B05"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$q.smali */
    class q extends e8 {
        q() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(233, 74, ECConstants.ZERO, BigInteger.valueOf(1L), a7.b("8000000000000000000000000000069D5BB915BCD46EFB1AD5F173ABDF"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04017232BA853A7E731AF129F22FF4149563A419C26BF50A4C9D6EEFAD612601DB537DECE819B7F70F555A67C427A8CD9BF18AEB9B56E0C11056FAE6A3"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$r.smali */
    class r extends e8 {
        r() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(233, 74, BigInteger.valueOf(1L), a7.b("0066647EDE6C332C7F8C0923BB58213B333B20E9CE4281FE115F7D8F90AD"), a7.b("01000000000000000000000000000013E974E72F8A6922031D2603CFE0D7"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("74D59FF07F6B413D0EA14B344B20A2DB049B50C3");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0400FAC9DFCBAC8313BB2139F1BB755FEF65BC391F8B36F8F8EB7371FD558B01006A08A41903350678E58528BEBF8A0BEFF867A7CA36716F7E01F81052"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$s.smali */
    class s extends e8 {
        s() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(239, Opcodes.IFLE, ECConstants.ZERO, BigInteger.valueOf(1L), a7.b("2000000000000000000000000000005A79FEC67CB6E91F1C1DA800E478A5"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0429A0B6A887A983E9730988A68727A8B2D126C44CC2CC7B2A6555193035DC76310804F12E549BDB011C103089E73510ACB275FC312A5DC6B76553F0CA"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$u.smali */
    class u extends e8 {
        u() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(283, 5, 7, 12, BigInteger.valueOf(1L), a7.b("027B680AC8B8596DA5A4AF8A19A0303FCA97FD7645309FA2A581485AF6263E313B79A2F5"), a7.b("03FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEF90399660FC938A90165B042A7CEFADB307"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("77E2B07370EB0F832A6DD5B62DFC88CD06BB84BE");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "0405F939258DB7DD90E1934F8C70B0DFEC2EED25B8557EAC9C80E2E198F8CDBECD86B1205303676854FE24141CB98FE6D4B20D02B4516FF702350EDDB0826779C813F0DF45BE8112F4"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$v.smali */
    class v extends e8 {
        v() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.Fp(a7.b("DB7C2ABF62E35E668076BEAD208B"), a7.b("6127C24C05F38A0AAAF65C0EF02C"), a7.b("51DEF1815DB5ED74FCC34C85D709"), a7.b("36DF0AAFD8B8D7597CA10520D04B"), BigInteger.valueOf(4L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("002757A1114D696E6768756151755316C05E0BD4");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "044BA30AB5E892B4E1649DD0928643ADCD46F5882E3747DEF36E956E97"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$w.smali */
    class w extends e8 {
        w() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(409, 87, ECConstants.ZERO, BigInteger.valueOf(1L), a7.b("7FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE5F83B2D4EA20400EC4557D5ED3E3E7CA5B4B5C83B8E01E5FCF"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "040060F05F658F49C1AD3AB1890F7184210EFD0987E307C84C27ACCFB8F9F67CC2C460189EB5AAAA62EE222EB1B35540CFE902374601E369050B7C4E42ACBA1DACBF04299C3460782F918EA427E6325165E9EA10E3DA5F6C42E9C55215AA9CA27A5863EC48D8E0286B"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$x.smali */
    class x extends e8 {
        x() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(409, 87, BigInteger.valueOf(1L), a7.b("0021A5C2C8EE9FEB5C4B9A753B7B476B7FD6422EF1F3DD674761FA99D6AC27C8A9A197B272822F6CD57A55AA4F50AE317B13545F"), a7.b("010000000000000000000000000000000000000000000000000001E2AAD6A612F33307BE5FA47C3C9E052F838164CD37D9A21173"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("4099B5A457F9D69F79213D094C4BCD4D4262210B");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04015D4860D088DDB3496B0C6064756260441CDE4AF1771D4DB01FFE5B34E59703DC255A868A1180515603AEAB60794E54BB7996A70061B1CFAB6BE5F32BBFA78324ED106A7636B9C5A7BD198D0158AA4F5488D08F38514F1FDF4B4F40D2181B3681C364BA0273C706"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$y.smali */
    class y extends e8 {
        y() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(571, 2, 5, 10, ECConstants.ZERO, BigInteger.valueOf(1L), a7.b("020000000000000000000000000000000000000000000000000000000000000000000000131850E1F19A63E4B391A8DB917F4138B630D84BE5D639381E91DEB45CFE778F637C1001"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "04026EB7A859923FBC82189631F8103FE4AC9CA2970012D5D46024804801841CA44370958493B205E647DA304DB4CEB08CBBD1BA39494776FB988B47174DCA88C7E2945283A01C89720349DC807F4FBF374F4AEADE3BCA95314DD58CEC9F307A54FFC61EFC006D8A2C9D4979C0AC44AEA74FBEBBB9F772AEDCB620B01A7BA7AF1B320430C8591984F601CD4C143EF1C7A3"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a7$z.smali */
    class z extends e8 {
        z() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a7.b(new ECCurve.F2m(571, 2, 5, 10, BigInteger.valueOf(1L), a7.b("02F40E7E2221F295DE297117B7F3D62F5C6A97FFCB8CEFF1CD6BA8CE4A9A18AD84FFABBD8EFA59332BE7AD6756A66E294AFD185A78FF12AA520E4DE739BACA0C7FFEFF7F2955727A"), a7.b("03FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFE661CE18FF55987308059B186823851EC7DD9CA1161DE93D5174D66E8382E9BB2FE84E47"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("2AA058F73A0E33AB486B0F610410C53A7F132310");
            ECCurve c = c();
            return new X9ECParameters(c, a7.b(c, "040303001D34B856296C16C0D40D3CD7750A93D1D2955FA80AA5F40FC8DB7B2ABDBDE53950F4C0D293CDD711A35B67FB1499AE60038614F1394ABFA3B4C850D927E1E7769C8EEC2D19037BF27342DA639B6DCCFFFEB73D69D78C6C27A6009CBBCA1980F8533921E8A684423E43BAB08A576291AF8F461BB2A8B3531D2F0485C19B16E2F1516E23DD3C1A4827AF1B8AC15B"), c.getOrder(), c.getCofactor(), a);
        }
    }

    static {
        a("secp112r1", b7.g, a);
        a("secp112r2", b7.h, b);
        a("secp128r1", b7.u, c);
        a("secp128r2", b7.v, d);
        a("secp160k1", b7.j, e);
        a("secp160r1", b7.i, f);
        a("secp160r2", b7.w, g);
        a("secp192k1", b7.x, h);
        a("secp192r1", b7.G, i);
        a("secp224k1", b7.y, j);
        a("secp224r1", b7.z, k);
        a("secp256k1", b7.k, l);
        a("secp256r1", b7.H, m);
        a("secp384r1", b7.A, n);
        a("secp521r1", b7.B, f20o);
        a("sect113r1", b7.e, p);
        a("sect113r2", b7.f, q);
        a("sect131r1", b7.f21o, r);
        a("sect131r2", b7.p, s);
        a("sect163k1", b7.b, t);
        a("sect163r1", b7.c, u);
        a("sect163r2", b7.l, v);
        a("sect193r1", b7.q, w);
        a("sect193r2", b7.r, x);
        a("sect233k1", b7.s, y);
        a("sect233r1", b7.t, z);
        a("sect239k1", b7.d, A);
        a("sect283k1", b7.m, B);
        a("sect283r1", b7.n, C);
        a("sect409k1", b7.C, D);
        a("sect409r1", b7.D, E);
        a("sect571k1", b7.E, F);
        a("sect571r1", b7.F, G);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, String str) {
        f8 f8Var = new f8(eCCurve, z4.a(str));
        WNafUtil.configureBasepoint(f8Var.e());
        return f8Var;
    }

    public static X9ECParameters c(String str) {
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return a(e2);
    }

    public static e8 d(String str) {
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return b(e2);
    }

    public static com.vasco.digipass.sdk.utils.utilities.obfuscated.w e(String str) {
        return (com.vasco.digipass.sdk.utils.utilities.obfuscated.w) H.get(o7.b(str));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve, GLVTypeBParameters gLVTypeBParameters) {
        return eCCurve.configure().setEndomorphism(new GLVTypeBEndomorphism(eCCurve, gLVTypeBParameters)).create();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BigInteger b(String str) {
        return new BigInteger(1, z4.a(str));
    }

    static void a(String str, com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar, e8 e8Var) {
        H.put(str, wVar);
        J.put(wVar, str);
        I.put(wVar, e8Var);
    }

    public static e8 b(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        return (e8) I.get(wVar);
    }

    public static X9ECParameters a(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        e8 b2 = b(wVar);
        if (b2 == null) {
            return null;
        }
        return b2.d();
    }

    public static Enumeration a() {
        return J.elements();
    }
}
