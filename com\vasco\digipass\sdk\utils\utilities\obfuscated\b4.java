package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.ECDomainParameters;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\b4.smali */
public class b4 extends d4 {
    private final w h;
    private final w i;
    private final w j;

    public b4(ECDomainParameters eCDomainParameters, w wVar, w wVar2, w wVar3) {
        super(wVar, eCDomainParameters);
        if ((eCDomainParameters instanceof d4) && !wVar.b(((d4) eCDomainParameters).a())) {
            throw new IllegalArgumentException("named parameters do not match publicKeyParamSet value");
        }
        this.h = wVar;
        this.i = wVar2;
        this.j = wVar3;
    }
}
