package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192K1Point.smali */
public class SecP192K1Point extends ECPoint.AbstractFp {
    SecP192K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP192K1FieldElement secP192K1FieldElement = (SecP192K1FieldElement) this.b;
        SecP192K1FieldElement secP192K1FieldElement2 = (SecP192K1FieldElement) this.c;
        SecP192K1FieldElement secP192K1FieldElement3 = (SecP192K1FieldElement) eCPoint.getXCoord();
        SecP192K1FieldElement secP192K1FieldElement4 = (SecP192K1FieldElement) eCPoint.getYCoord();
        SecP192K1FieldElement secP192K1FieldElement5 = (SecP192K1FieldElement) this.d[0];
        SecP192K1FieldElement secP192K1FieldElement6 = (SecP192K1FieldElement) eCPoint.getZCoord(0);
        int[] c = u5.c();
        int[] a = u5.a();
        int[] a2 = u5.a();
        int[] a3 = u5.a();
        boolean isOne = secP192K1FieldElement5.isOne();
        if (isOne) {
            iArr = secP192K1FieldElement3.a;
            iArr2 = secP192K1FieldElement4.a;
        } else {
            SecP192K1Field.square(secP192K1FieldElement5.a, a2);
            SecP192K1Field.multiply(a2, secP192K1FieldElement3.a, a);
            SecP192K1Field.multiply(a2, secP192K1FieldElement5.a, a2);
            SecP192K1Field.multiply(a2, secP192K1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP192K1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP192K1FieldElement.a;
            iArr4 = secP192K1FieldElement2.a;
        } else {
            SecP192K1Field.square(secP192K1FieldElement6.a, a3);
            SecP192K1Field.multiply(a3, secP192K1FieldElement.a, c);
            SecP192K1Field.multiply(a3, secP192K1FieldElement6.a, a3);
            SecP192K1Field.multiply(a3, secP192K1FieldElement2.a, a3);
            iArr3 = c;
            iArr4 = a3;
        }
        int[] a4 = u5.a();
        SecP192K1Field.subtract(iArr3, iArr, a4);
        SecP192K1Field.subtract(iArr4, iArr2, a);
        if (u5.b(a4)) {
            return u5.b(a) ? twice() : curve.getInfinity();
        }
        SecP192K1Field.square(a4, a2);
        int[] a5 = u5.a();
        SecP192K1Field.multiply(a2, a4, a5);
        SecP192K1Field.multiply(a2, iArr3, a2);
        SecP192K1Field.negate(a5, a5);
        u5.c(iArr4, a5, c);
        SecP192K1Field.reduce32(u5.b(a2, a2, a5), a5);
        SecP192K1FieldElement secP192K1FieldElement7 = new SecP192K1FieldElement(a3);
        SecP192K1Field.square(a, secP192K1FieldElement7.a);
        int[] iArr5 = secP192K1FieldElement7.a;
        SecP192K1Field.subtract(iArr5, a5, iArr5);
        SecP192K1FieldElement secP192K1FieldElement8 = new SecP192K1FieldElement(a5);
        SecP192K1Field.subtract(a2, secP192K1FieldElement7.a, secP192K1FieldElement8.a);
        SecP192K1Field.multiplyAddToExt(secP192K1FieldElement8.a, a, c);
        SecP192K1Field.reduce(c, secP192K1FieldElement8.a);
        SecP192K1FieldElement secP192K1FieldElement9 = new SecP192K1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP192K1FieldElement9.a;
            SecP192K1Field.multiply(iArr6, secP192K1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP192K1FieldElement9.a;
            SecP192K1Field.multiply(iArr7, secP192K1FieldElement6.a, iArr7);
        }
        return new SecP192K1Point(curve, secP192K1FieldElement7, secP192K1FieldElement8, new ECFieldElement[]{secP192K1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP192K1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP192K1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP192K1FieldElement secP192K1FieldElement = (SecP192K1FieldElement) this.c;
        if (secP192K1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP192K1FieldElement secP192K1FieldElement2 = (SecP192K1FieldElement) this.b;
        SecP192K1FieldElement secP192K1FieldElement3 = (SecP192K1FieldElement) this.d[0];
        int[] a = u5.a();
        SecP192K1Field.square(secP192K1FieldElement.a, a);
        int[] a2 = u5.a();
        SecP192K1Field.square(a, a2);
        int[] a3 = u5.a();
        SecP192K1Field.square(secP192K1FieldElement2.a, a3);
        SecP192K1Field.reduce32(u5.b(a3, a3, a3), a3);
        SecP192K1Field.multiply(a, secP192K1FieldElement2.a, a);
        SecP192K1Field.reduce32(c6.c(6, a, 2, 0), a);
        int[] a4 = u5.a();
        SecP192K1Field.reduce32(c6.a(6, a2, 3, 0, a4), a4);
        SecP192K1FieldElement secP192K1FieldElement4 = new SecP192K1FieldElement(a2);
        SecP192K1Field.square(a3, secP192K1FieldElement4.a);
        int[] iArr = secP192K1FieldElement4.a;
        SecP192K1Field.subtract(iArr, a, iArr);
        int[] iArr2 = secP192K1FieldElement4.a;
        SecP192K1Field.subtract(iArr2, a, iArr2);
        SecP192K1FieldElement secP192K1FieldElement5 = new SecP192K1FieldElement(a);
        SecP192K1Field.subtract(a, secP192K1FieldElement4.a, secP192K1FieldElement5.a);
        int[] iArr3 = secP192K1FieldElement5.a;
        SecP192K1Field.multiply(iArr3, a3, iArr3);
        int[] iArr4 = secP192K1FieldElement5.a;
        SecP192K1Field.subtract(iArr4, a4, iArr4);
        SecP192K1FieldElement secP192K1FieldElement6 = new SecP192K1FieldElement(a3);
        SecP192K1Field.twice(secP192K1FieldElement.a, secP192K1FieldElement6.a);
        if (!secP192K1FieldElement3.isOne()) {
            int[] iArr5 = secP192K1FieldElement6.a;
            SecP192K1Field.multiply(iArr5, secP192K1FieldElement3.a, iArr5);
        }
        return new SecP192K1Point(curve, secP192K1FieldElement4, secP192K1FieldElement5, new ECFieldElement[]{secP192K1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP192K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
