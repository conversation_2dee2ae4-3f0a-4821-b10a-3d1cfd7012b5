package o.ce;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.security.Principal;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.X509TrustManager;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ce\d.smali */
public final class d implements X509TrustManager {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static String b;
    private static long d;
    private static int f;
    private static int i;
    private final String a;
    private final String c;
    private final X509TrustManager e;

    static void c() {
        d = 4849293242945110302L;
    }

    private static void h(short s, int i2, int i3, Object[] objArr) {
        int i4 = (i3 * 2) + Opcodes.IREM;
        int i5 = (s * 4) + 1;
        byte[] bArr = $$a;
        int i6 = 3 - (i2 * 4);
        byte[] bArr2 = new byte[i5];
        int i7 = -1;
        int i8 = i5 - 1;
        if (bArr == null) {
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = -1;
            i4 = i6 + i4;
            i6++;
        }
        while (true) {
            int i9 = i7 + 1;
            bArr2[i9] = (byte) i4;
            if (i9 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b2 = bArr[i6];
            int i10 = i4;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = i9;
            i4 = b2 + i10;
            i6++;
        }
    }

    static void init$0() {
        $$a = new byte[]{48, -21, 49};
        $$b = 26;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        c();
        Object[] objArr = new Object[1];
        g("쟍䙀쒋䋋섍佄춸䯡쨣䡮횴哊팫冟\udfd5帜\udc5b媗", Color.argb(0, 0, 0, 0) + 33211, objArr);
        b = ((String) objArr[0]).intern();
        int i2 = i + 19;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    d(X509TrustManager x509TrustManager, String str, String str2) {
        this.e = x509TrustManager;
        this.c = str;
        this.a = str2;
    }

    @Override // javax.net.ssl.X509TrustManager
    public final void checkClientTrusted(X509Certificate[] x509CertificateArr, String str) throws CertificateException {
        int i2 = i + 73;
        f = i2 % 128;
        int i3 = i2 % 2;
        this.e.checkClientTrusted(x509CertificateArr, str);
        int i4 = f + 23;
        i = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // javax.net.ssl.X509TrustManager
    public final void checkServerTrusted(X509Certificate[] x509CertificateArr, String str) throws CertificateException {
        String d2;
        int i2 = i + 23;
        f = i2 % 128;
        try {
            if (i2 % 2 == 0) {
                this.e.checkServerTrusted(x509CertificateArr, str);
                throw null;
            }
            this.e.checkServerTrusted(x509CertificateArr, str);
            Principal subjectDN = x509CertificateArr[0].getSubjectDN();
            Object[] objArr = new Object[1];
            g("쟍女𥉉", TextUtils.getOffsetAfter("", 0) + 40627, objArr);
            if (!this.c.equals(d(((String) objArr[0]).intern(), subjectDN))) {
                g.c();
                String str2 = b;
                Object[] objArr2 = new Object[1];
                g("쟋팀\uee2a壟鑦꾅몹嗟悶簖᜵∽㵞졠\ue382ﺪ觝ꓼ뀌䬲昒煲ౣ➆㊫췊\ud8ee\uf45f輴驘땹䂖寎皬ǆ᳣⡂쌵\ude4e\ue96e蒅龡\uaad5䗋傪氚ܡቑ\u2d68㢘펪\uee97隣锔ꀞ묿噏慣粉ូ⋛㷭중\ue473Ｍ詂ꕥ낊䮭曏熼കᠷ㍘칵\ud92f\uf48b辻髁떣䄐尲睌ȷᶆ⢹쎩\ude8b\ue9f5蔄逭ꭚ", TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 5348, objArr2);
                g.e(str2, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                g("쟇邧楶숺髾玄챀ꕟ緵횞꼥߮킵ꥻɌ\udae9뎟\u0c54\ue515", 22344 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr3);
                throw new CertificateException(((String) objArr3[0]).intern());
            }
            int i3 = f + Opcodes.LMUL;
            i = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    Object[] objArr4 = new Object[1];
                    g("쟁撂", 41777 >>> TextUtils.getOffsetBefore("", 1), objArr4);
                    d2 = d(((String) objArr4[0]).intern(), subjectDN);
                    switch (this.a.isEmpty()) {
                        case true:
                            return;
                    }
                default:
                    Object[] objArr5 = new Object[1];
                    g("쟁撂", TextUtils.getOffsetBefore("", 0) + 41777, objArr5);
                    d2 = d(((String) objArr5[0]).intern(), subjectDN);
                    if (this.a.isEmpty()) {
                        return;
                    }
                    break;
            }
            if (this.a.equals(d2)) {
                return;
            }
            g.c();
            String str3 = b;
            Object[] objArr6 = new Object[1];
            g("쟋﹜뒒櫖№\ue751鶑叓\u0a56쁚蚝볉猎⥔\uef9aꗖ尝ቀ좔軆䕂筊㆖\uf7c0긇摇᪁탘霋䵙΅㧀\uf000똱沾⋲\ud93f齰嗴௶숸\uf87d뻸瓯⬨\ue168ꞯ巫ᐻ쩥胠䛰紧㍷\ue9b2꿢昴ᰩ튫裮伸չ㮥\uf1e9ꠧ溒ⓑ\udb07酗埕ෛ쐅祝나盖ⴒ\ue349馜忈ᘖ챑芏뢀缍㕃\ueb88ꇁ塇ṇ풛諍䅋݄㶂\uf3d8\uaa4f恚⚹\udcf5鍳䥡ྴ엹ﰲ", TextUtils.getOffsetAfter("", 0) + 14783, objArr6);
            g.e(str3, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            g("쟇\uee35鑒몐悶ᛎ㴔\ue37d襩뾁斻ை㈜\ud836蹒뒔媪Â㜛\udd2f茊ꦹ御׀Ⱃ", TextUtils.indexOf((CharSequence) "", '0', 0) + 10710, objArr7);
            throw new CertificateException(((String) objArr7[0]).intern());
        } catch (CertificateException e) {
            g.c();
            String str4 = b;
            Object[] objArr8 = new Object[1];
            g("쟭ᆥ歭䔤黩\ue892쉹ᰩ痠侰饢\uf33b쳘⚜灗䨗ꏛﶙ휘⅚窒咰긠蟧퇦⭦ԥ廭ꢮ艰\udc32㗺ྍ奌댜賂\ue6c2\u3040\u0a0e揞뷖靀\ue11e㪻ᑸ渼䟷醷\ueb7b씹", ExpandableListView.getPackedPositionChild(0L) + 54852, objArr8);
            g.a(str4, ((String) objArr8[0]).intern(), e);
            throw e;
        }
    }

    @Override // javax.net.ssl.X509TrustManager
    public final X509Certificate[] getAcceptedIssuers() {
        int i2 = i + 33;
        f = i2 % 128;
        int i3 = i2 % 2;
        X509Certificate[] acceptedIssuers = this.e.getAcceptedIssuers();
        int i4 = i + 93;
        f = i4 % 128;
        int i5 = i4 % 2;
        return acceptedIssuers;
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x0061 A[PHI: r0 r8
  0x0061: PHI (r0v4 int) = (r0v3 int), (r0v8 int) binds: [B:19:0x005e, B:6:0x0035] A[DONT_GENERATE, DONT_INLINE]
  0x0061: PHI (r8v2 java.lang.String) = (r8v1 java.lang.String), (r8v7 java.lang.String) binds: [B:19:0x005e, B:6:0x0035] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static java.lang.String d(java.lang.String r7, java.security.Principal r8) {
        /*
            int r0 = o.ce.d.f
            r1 = 1
            int r0 = r0 + r1
            int r2 = r0 % 128
            o.ce.d.i = r2
            int r0 = r0 % 2
            r2 = -1
            r3 = 16783685(0x1001945, float:2.3528017E-38)
            java.lang.String r4 = "잢"
            r5 = 0
            if (r0 == 0) goto L39
            java.lang.String r8 = r8.getName()
            int r0 = r8.indexOf(r7)
            int r6 = android.graphics.Color.rgb(r1, r5, r1)
            int r3 = r3 % r6
            java.lang.Object[] r6 = new java.lang.Object[r1]
            g(r4, r3, r6)
            r3 = r6[r5]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            int r3 = r8.indexOf(r3, r0)
            if (r3 != r2) goto L34
            goto L35
        L34:
            r1 = r5
        L35:
            switch(r1) {
                case 0: goto L66;
                default: goto L38;
            }
        L38:
            goto L61
        L39:
            java.lang.String r8 = r8.getName()
            int r0 = r8.indexOf(r7)
            int r6 = android.graphics.Color.rgb(r5, r5, r5)
            int r6 = r6 + r3
            java.lang.Object[] r1 = new java.lang.Object[r1]
            g(r4, r6, r1)
            r1 = r1[r5]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            int r3 = r8.indexOf(r1, r0)
            if (r3 != r2) goto L5c
            r1 = 60
            goto L5e
        L5c:
            r1 = 23
        L5e:
            switch(r1) {
                case 23: goto L66;
                default: goto L61;
            }
        L61:
            int r3 = r8.length()
        L66:
            if (r0 < 0) goto L83
            int r1 = r7.length()
            int r1 = r1 + r0
            if (r3 < r1) goto L83
            int r7 = r7.length()
            int r0 = r0 + r7
            java.lang.String r7 = r8.substring(r0, r3)
            int r8 = o.ce.d.f
            int r8 = r8 + 65
            int r0 = r8 % 128
            o.ce.d.i = r0
            int r8 = r8 % 2
            return r7
        L83:
            r7 = 0
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.d.d(java.lang.String, java.security.Principal):java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 494
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.d.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
