package o.al;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.eo.f;
import o.h.d;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\al\c.smali */
public final class c extends b<a> {
    private static char f;
    private static char g;
    private static char h;
    private static char i;

    /* renamed from: o, reason: collision with root package name */
    private static int f35o;
    f a;
    o.eo.e b;
    e c;
    d d;
    String e;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int j = 0;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\al\c$a.smali */
    public interface a {
        void a(o.bb.d dVar);

        void b();
    }

    static {
        f35o = 1;
        l();
        View.combineMeasuredStates(0, 0);
        AndroidCharacter.getMirror('0');
        int i2 = j + 67;
        f35o = i2 % 128;
        int i3 = i2 % 2;
    }

    static void l() {
        f = (char) 49428;
        i = (char) 3560;
        h = (char) 61288;
        g = (char) 63607;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i2 = f35o + 79;
        j = i2 % 128;
        int i3 = i2 % 2;
        AsyncTaskC0020c o2 = o();
        int i4 = j + 91;
        f35o = i4 % 128;
        int i5 = i4 % 2;
        return o2;
    }

    public c(Context context, a aVar, o.ei.c cVar) {
        super(context, aVar, cVar, o.bb.e.p);
    }

    public final void a(d dVar, String str, e eVar, o.eo.e eVar2, f fVar) {
        g.c();
        Object[] objArr = new Object[1];
        k("\udba0Ѡ髌拘伅\ue444น\udd11ꆵ剺ﰦꔄ\uf8e6좏\udfcb羊\ueb30ぉ", (ViewConfiguration.getPressedStateDuration() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("\uf06fⅪ\udba0Ѡ髌拘伅\ue444น\udd11ꆵ剺䘺鶩꾍鶛\udba0Ѡ髌拘伅\ue444㬾֩譝뼻\ue518뮂튟\uf358韫ᳲ\ue34c鎬驋\ue31b汖빐", TextUtils.lastIndexOf("", '0', 0) + 39, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar2.e()).toString());
        this.d = dVar;
        this.e = str;
        this.c = eVar;
        this.b = eVar2;
        this.a = fVar;
        c();
        int i2 = f35o + 5;
        j = i2 % 128;
        switch (i2 % 2 == 0 ? '%' : '0') {
            case '%':
                return;
            default:
                int i3 = 97 / 0;
                return;
        }
    }

    private AsyncTaskC0020c o() {
        AsyncTaskC0020c asyncTaskC0020c = new AsyncTaskC0020c(this);
        int i2 = j + 81;
        f35o = i2 % 128;
        int i3 = i2 % 2;
        return asyncTaskC0020c;
    }

    @Override // o.y.b
    public final String a() {
        int i2 = f35o + 79;
        j = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("\udba0Ѡ髌拘伅\ue444น\udd11ꆵ剺ﰦꔄ\uf8e6좏\udfcb羊\ueb30ぉ", 19 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = f35o + 95;
        j = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 5 : (char) 19) {
            case 19:
                return intern;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* renamed from: o.al.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\al\c$c.smali */
    static final class AsyncTaskC0020c extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int[] a;
        private static int c;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            c = 0;
            e = 1;
            a = new int[]{-1154693196, 1952639962, 1842120472, 2025528781, 1304435572, -1236837951, 1689668810, -974940787, 674711286, 425513610, -386894826, -874732979, -684825536, -1327586600, -774646605, -911921192, -844538948, -1778138978};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0038). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(int r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.al.c.AsyncTaskC0020c.$$d
                int r8 = r8 * 4
                int r8 = r8 + 1
                int r7 = r7 * 2
                int r7 = 3 - r7
                int r6 = r6 + 115
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L1a
                r6 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L38
            L1a:
                r3 = r2
                r5 = r7
                r7 = r6
                r6 = r5
            L1e:
                byte r4 = (byte) r7
                int r6 = r6 + 1
                r1[r3] = r4
                if (r3 != r8) goto L2d
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2d:
                r4 = r0[r6]
                int r3 = r3 + 1
                r5 = r9
                r9 = r8
                r8 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L38:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1e
            */
            throw new UnsupportedOperationException("Method not decompiled: o.al.c.AsyncTaskC0020c.B(int, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{52, 109, 93, 87};
            $$e = Opcodes.LOR;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = c + 79;
            e = i % 128;
            int i2 = i % 2;
        }

        AsyncTaskC0020c(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = c + Opcodes.LREM;
            e = i % 128;
            switch (i % 2 == 0 ? (char) 3 : 'N') {
                case 'N':
                    Object[] objArr = new Object[1];
                    w(new int[]{19206653, -1906548594, -1081435273, -733668954, -2005693557, 1264208794}, 11 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{19206653, -1906548594, -1081435273, -733668954, -2005693557, 1264208794}, (ViewConfiguration.getScrollBarSize() + 65) * 8, objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(new int[]{434102870, 614751467, -236963983, -232490331, 676898868, -2141768231, 133741039, 923715599, 336484421, -1593239212}, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 18, objArr);
            o.cf.d dVar = new o.cf.d(context, 34, ((String) objArr[0]).intern());
            int i = c + 23;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return dVar;
                default:
                    int i2 = 38 / 0;
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(new int[]{733544431, -1776340212, 1223567911, 2119456745}, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 6, objArr);
            bVar.d(((String) objArr[0]).intern(), ((c) e()).c.e());
            Object[] objArr2 = new Object[1];
            w(new int[]{1404386168, -87318519, -739244214, 1987087772}, 6 - Gravity.getAbsoluteGravity(0, 0), objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((c) e()).b.e());
            o.eg.b bVar2 = new o.eg.b();
            Object[] objArr3 = new Object[1];
            w(new int[]{-374825933, 222665380}, 3 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr3);
            bVar2.d(((String) objArr3[0]).intern(), ((c) e()).a.a().e());
            Object[] objArr4 = new Object[1];
            w(new int[]{-1714123692, 2031051905, -250632815, -957839627, -739244214, 1987087772}, 10 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr4);
            bVar2.d(((String) objArr4[0]).intern(), ((c) e()).a.b());
            Object[] objArr5 = new Object[1];
            w(new int[]{-1714123692, 2031051905, -250632815, -957839627, -955601520, -1832282161, -690589369, 1796919212, 1540541068, -1496959497}, Color.alpha(0) + 19, objArr5);
            bVar2.d(((String) objArr5[0]).intern(), ((c) e()).a.e());
            Object[] objArr6 = new Object[1];
            w(new int[]{1597822472, 1368599265, 1356473752, -686778115}, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 8, objArr6);
            bVar2.d(((String) objArr6[0]).intern(), ((c) e()).a.k());
            Object[] objArr7 = new Object[1];
            w(new int[]{1597822472, 1368599265, -1420345008, 1797038099}, 4 - TextUtils.lastIndexOf("", '0', 0, 0), objArr7);
            bVar.d(((String) objArr7[0]).intern(), bVar2);
            int i = e + 59;
            c = i % 128;
            switch (i % 2 != 0 ? (char) 5 : 'M') {
                case 'M':
                    return bVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            j jVar = new j(((c) e()).e, false, ((c) e()).d);
            int i = c + 25;
            e = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = c + 63;
            e = i % 128;
            Object obj = null;
            switch (i % 2 == 0 ? 'B' : 'T') {
                case 'B':
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:19:0x003a  */
        /* JADX WARN: Removed duplicated region for block: B:21:0x003d  */
        /* JADX WARN: Removed duplicated region for block: B:6:0x001f  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.bb.a c(int r3) {
            /*
                r2 = this;
                int r0 = o.al.c.AsyncTaskC0020c.c
                int r0 = r0 + 67
                int r1 = r0 % 128
                o.al.c.AsyncTaskC0020c.e = r1
                int r0 = r0 % 2
                r1 = 0
                if (r0 != 0) goto Lf
                r0 = 1
                goto L10
            Lf:
                r0 = r1
            L10:
                switch(r0) {
                    case 0: goto L14;
                    default: goto L13;
                }
            L13:
                goto L18
            L14:
                switch(r3) {
                    case 5001: goto L3a;
                    case 5002: goto L1f;
                    default: goto L17;
                }
            L17:
                goto L3d
            L18:
                r0 = 8
                int r0 = r0 / r1
                switch(r3) {
                    case 5001: goto L3a;
                    case 5002: goto L1f;
                    default: goto L1e;
                }
            L1e:
                goto L17
            L1f:
                o.bb.a r3 = o.bb.a.az
                int r0 = o.al.c.AsyncTaskC0020c.c
                int r0 = r0 + 93
                int r1 = r0 % 128
                o.al.c.AsyncTaskC0020c.e = r1
                int r0 = r0 % 2
                if (r0 != 0) goto L30
                r0 = 69
                goto L32
            L30:
                r0 = 63
            L32:
                switch(r0) {
                    case 69: goto L36;
                    default: goto L35;
                }
            L35:
                return r3
            L36:
                r3 = 0
                throw r3     // Catch: java.lang.Throwable -> L38
            L38:
                r3 = move-exception
                throw r3
            L3a:
                o.bb.a r3 = o.bb.a.ay
                return r3
            L3d:
                o.bb.a r3 = super.c(r3)
                return r3
            L42:
                r3 = move-exception
                throw r3
            */
            throw new UnsupportedOperationException("Method not decompiled: o.al.c.AsyncTaskC0020c.c(int):o.bb.a");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = e + 51;
            c = i % 128;
            int i2 = i % 2;
            f().j().d(g(), ((c) e()).b.e(), ((c) e()).a, ((c) e()).c.a());
            int i3 = e + 51;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + 49;
            e = i % 128;
            switch (i % 2 == 0) {
                case true:
                    int i2 = AnonymousClass4.a[h().d().ordinal()];
                    throw null;
                default:
                    switch (AnonymousClass4.a[h().d().ordinal()]) {
                        case 1:
                            f().c(g(), ((c) e()).b.e());
                            return;
                        case 2:
                            f().e(g(), ((c) e()).b.e());
                            int i3 = c + 77;
                            e = i3 % 128;
                            int i4 = i3 % 2;
                            return;
                        default:
                            super.t();
                            return;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = c + 39;
            e = i % 128;
            int i2 = i % 2;
            ((c) e()).j().b();
            int i3 = c + Opcodes.DMUL;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = c + 99;
            e = i % 128;
            int i2 = i % 2;
            ((c) e()).j().a(dVar);
            int i3 = e + 43;
            c = i3 % 128;
            switch (i3 % 2 != 0 ? '.' : '(') {
                case '(':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int[] r21, int r22, java.lang.Object[] r23) {
            /*
                Method dump skipped, instructions count: 1058
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.al.c.AsyncTaskC0020c.w(int[], int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\al\c$e.smali */
    public static final class e implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final e a;
        public static final e b;
        public static final e c;
        private static final /* synthetic */ e[] d;
        private static int f;
        private static int g;
        private static int h;
        private static char i;
        private static long j;
        private final String e;

        static void b() {
            i = (char) 48258;
            h = 161105445;
            j = 6565854932352255525L;
        }

        static void init$0() {
            $$a = new byte[]{30, 126, 60, -105};
            $$b = 94;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0032). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r7, byte r8, short r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.al.c.e.$$a
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r9 = 106 - r9
                int r7 = r7 + 4
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L15
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r8
                goto L32
            L15:
                r3 = r2
                r6 = r9
                r9 = r8
                r8 = r6
            L19:
                int r7 = r7 + 1
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                if (r4 != r9) goto L2a
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2a:
                r3 = r0[r7]
                r6 = r10
                r10 = r9
                r9 = r3
                r3 = r1
                r1 = r0
                r0 = r6
            L32:
                int r9 = -r9
                int r8 = r8 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.al.c.e.l(byte, byte, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ e[] c() {
            int i2 = f + 5;
            g = i2 % 128;
            switch (i2 % 2 == 0 ? (char) 0 : 'T') {
                case Opcodes.BASTORE /* 84 */:
                    return new e[]{b, a, c};
                default:
                    e[] eVarArr = new e[2];
                    eVarArr[1] = b;
                    eVarArr[1] = a;
                    eVarArr[5] = c;
                    return eVarArr;
            }
        }

        public static e valueOf(String str) {
            int i2 = f + 9;
            g = i2 % 128;
            int i3 = i2 % 2;
            e eVar = (e) Enum.valueOf(e.class, str);
            int i4 = f + 1;
            g = i4 % 128;
            int i5 = i4 % 2;
            return eVar;
        }

        public static e[] values() {
            int i2 = f + 49;
            g = i2 % 128;
            int i3 = i2 % 2;
            e[] eVarArr = (e[]) d.clone();
            int i4 = g + 23;
            f = i4 % 128;
            switch (i4 % 2 != 0 ? (char) 14 : '#') {
                case 14:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return eVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            g = 1;
            b();
            Object[] objArr = new Object[1];
            k(View.MeasureSpec.getMode(0), "⟴㽽俿쐅㯴ᙰ", (char) (13807 - (ViewConfiguration.getFadingEdgeLength() >> 16)), "쯱\uf606\uef63뀵", "\u0000\u0000\u0000\u0000", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k(ViewConfiguration.getKeyRepeatDelay() >> 16, "⟴㽽俿쐅㯴ᙰ", (char) (13807 - (ViewConfiguration.getLongPressTimeout() >> 16)), "쯱\uf606\uef63뀵", "\u0000\u0000\u0000\u0000", objArr2);
            b = new e(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k(1874929075 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "磮獸褹슺ⴠꙓ᭘", (char) Drawable.resolveOpacity(0, 0), "댤섥\ue06f쓺", "\u0000\u0000\u0000\u0000", objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 1874929074, "磮獸褹슺ⴠꙓ᭘", (char) View.combineMeasuredStates(0, 0), "댤섥\ue06f쓺", "\u0000\u0000\u0000\u0000", objArr4);
            a = new e(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "䕴䙱烳ᄗ\ue485儅", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 35113), "흎\ueff8⨟䚉", "\u0000\u0000\u0000\u0000", objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            k(KeyEvent.getDeadChar(0, 0), "䕴䙱烳ᄗ\ue485儅", (char) (35114 - Color.blue(0)), "흎\ueff8⨟䚉", "\u0000\u0000\u0000\u0000", objArr6);
            c = new e(intern3, 2, ((String) objArr6[0]).intern());
            d = c();
            int i2 = f + 69;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        private e(String str, int i2, String str2) {
            this.e = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = g + Opcodes.DNEG;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            String str = this.e;
            int i5 = i3 + 47;
            g = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        /* JADX WARN: Removed duplicated region for block: B:18:0x0047  */
        /* JADX WARN: Removed duplicated region for block: B:20:0x004a  */
        /* JADX WARN: Removed duplicated region for block: B:31:0x0064  */
        /* JADX WARN: Removed duplicated region for block: B:8:0x0032  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.eo.f.d a() {
            /*
                r11 = this;
                int r0 = o.al.c.e.f
                int r0 = r0 + 97
                int r1 = r0 % 128
                o.al.c.e.g = r1
                int r0 = r0 % 2
                r1 = 1
                r2 = 0
                if (r0 != 0) goto L10
                r0 = r1
                goto L11
            L10:
                r0 = r2
            L11:
                r3 = 0
                switch(r0) {
                    case 1: goto L21;
                    default: goto L15;
                }
            L15:
                int[] r0 = o.al.c.AnonymousClass4.c
                int r4 = r11.ordinal()
                r0 = r0[r4]
                switch(r0) {
                    case 1: goto L4a;
                    case 2: goto L47;
                    case 3: goto L32;
                    default: goto L20;
                }
            L20:
                goto L64
            L21:
                int[] r0 = o.al.c.AnonymousClass4.c
                int r4 = r11.ordinal()
                r0 = r0[r4]
                r4 = 55
                int r4 = r4 / r2
                switch(r0) {
                    case 1: goto L4a;
                    case 2: goto L47;
                    case 3: goto L32;
                    default: goto L2f;
                }
            L2f:
                goto L20
            L30:
                r0 = move-exception
                throw r0
            L32:
                o.eo.f$d r0 = o.eo.f.d.c
                int r1 = o.al.c.e.g
                int r1 = r1 + 119
                int r2 = r1 % 128
                o.al.c.e.f = r2
                int r1 = r1 % 2
                if (r1 != 0) goto L41
                return r0
            L41:
                r3.hashCode()     // Catch: java.lang.Throwable -> L45
                throw r3     // Catch: java.lang.Throwable -> L45
            L45:
                r0 = move-exception
                throw r0
            L47:
                o.eo.f$d r0 = o.eo.f.d.b
                return r0
            L4a:
                o.eo.f$d r0 = o.eo.f.d.d
                int r1 = o.al.c.e.f
                int r1 = r1 + 125
                int r2 = r1 % 128
                o.al.c.e.g = r2
                int r1 = r1 % 2
                if (r1 != 0) goto L5b
                r1 = 96
                goto L5d
            L5b:
                r1 = 64
            L5d:
                switch(r1) {
                    case 96: goto L61;
                    default: goto L60;
                }
            L60:
                return r0
            L61:
                throw r3     // Catch: java.lang.Throwable -> L62
            L62:
                r0 = move-exception
                throw r0
            L64:
                java.lang.UnsupportedOperationException r0 = new java.lang.UnsupportedOperationException
                java.lang.StringBuilder r3 = new java.lang.StringBuilder
                r3.<init>()
                int r4 = android.view.KeyEvent.getMaxKeyCode()
                int r5 = r4 >> 16
                java.lang.String r6 = "ᚓ숒ꉴ\ue47a⊓됃喰ꀪ긩萫\uea9d雦妰\uf790∁陛\uda46ḣ"
                float r4 = android.media.AudioTrack.getMinVolume()
                r7 = 0
                int r4 = (r4 > r7 ? 1 : (r4 == r7 ? 0 : -1))
                r7 = 42669(0xa6ad, float:5.9792E-41)
                int r7 = r7 - r4
                char r7 = (char) r7
                java.lang.String r8 = "睴ꝩ궓⾦"
                java.lang.String r9 = "\u0000\u0000\u0000\u0000"
                java.lang.Object[] r1 = new java.lang.Object[r1]
                r10 = r1
                k(r5, r6, r7, r8, r9, r10)
                r1 = r1[r2]
                java.lang.String r1 = (java.lang.String) r1
                java.lang.String r1 = r1.intern()
                java.lang.StringBuilder r1 = r3.append(r1)
                java.lang.String r2 = r11.name()
                java.lang.StringBuilder r1 = r1.append(r2)
                java.lang.String r1 = r1.toString()
                r0.<init>(r1)
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.al.c.e.a():o.eo.f$d");
        }

        private static void k(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
            char[] charArray;
            int i3 = $11 + 31;
            int i4 = i3 % 128;
            $10 = i4;
            int i5 = i3 % 2;
            int i6 = 0;
            Object obj = null;
            switch (str3 == null) {
                case false:
                    int i7 = i4 + 7;
                    $11 = i7 % 128;
                    switch (i7 % 2 == 0) {
                        case false:
                            charArray = str3.toCharArray();
                            break;
                        default:
                            str3.toCharArray();
                            obj.hashCode();
                            throw null;
                    }
                default:
                    charArray = str3;
                    break;
            }
            char[] cArr = charArray;
            char[] charArray2 = str2 != null ? str2.toCharArray() : str2;
            char[] charArray3 = str != null ? str.toCharArray() : str;
            o oVar = new o();
            int length = charArray2.length;
            char[] cArr2 = new char[length];
            int length2 = cArr.length;
            char[] cArr3 = new char[length2];
            System.arraycopy(charArray2, 0, cArr2, 0, length);
            System.arraycopy(cArr, 0, cArr3, 0, length2);
            cArr2[0] = (char) (cArr2[0] ^ c2);
            cArr3[2] = (char) (cArr3[2] + ((char) i2));
            int length3 = charArray3.length;
            char[] cArr4 = new char[length3];
            oVar.e = 0;
            while (oVar.e < length3) {
                try {
                    Object[] objArr2 = {oVar};
                    Object obj2 = o.e.a.s.get(-429442487);
                    if (obj2 == null) {
                        Class cls = (Class) o.e.a.c(Color.blue(i6) + 10, (char) (20955 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 344 - TextUtils.getOffsetBefore("", i6));
                        byte b2 = (byte) (-1);
                        byte b3 = (byte) (b2 + 1);
                        Object[] objArr3 = new Object[1];
                        l(b2, b3, (byte) (b3 | 7), objArr3);
                        String str4 = (String) objArr3[i6];
                        Class<?>[] clsArr = new Class[1];
                        clsArr[i6] = Object.class;
                        obj2 = cls.getMethod(str4, clsArr);
                        o.e.a.s.put(-429442487, obj2);
                    }
                    int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                    try {
                        Object[] objArr4 = {oVar};
                        Object obj3 = o.e.a.s.get(-515165572);
                        if (obj3 == null) {
                            Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getPressedStateDuration() >> 16), (char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), View.resolveSizeAndState(i6, i6, i6) + 207);
                            byte b4 = (byte) (-1);
                            byte b5 = (byte) (b4 + 1);
                            Object[] objArr5 = new Object[1];
                            l(b4, b5, (byte) (b5 + 5), objArr5);
                            String str5 = (String) objArr5[i6];
                            Class<?>[] clsArr2 = new Class[1];
                            clsArr2[i6] = Object.class;
                            obj3 = cls2.getMethod(str5, clsArr2);
                            o.e.a.s.put(-515165572, obj3);
                        }
                        int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                        int i8 = cArr2[oVar.e % 4] * 32718;
                        try {
                            Object[] objArr6 = new Object[3];
                            objArr6[2] = Integer.valueOf(cArr3[intValue]);
                            objArr6[1] = Integer.valueOf(i8);
                            objArr6[i6] = oVar;
                            Object obj4 = o.e.a.s.get(-1614232674);
                            if (obj4 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - (ExpandableListView.getPackedPositionForChild(i6, i6) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(i6, i6) == 0L ? 0 : -1)), (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), (TypedValue.complexToFloat(i6) > 0.0f ? 1 : (TypedValue.complexToFloat(i6) == 0.0f ? 0 : -1)) + 281);
                                byte b6 = (byte) (-1);
                                byte b7 = (byte) (b6 + 1);
                                Object[] objArr7 = new Object[1];
                                l(b6, b7, (byte) (b7 + 3), objArr7);
                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-1614232674, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr6);
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr2[intValue2] * 32718), Integer.valueOf(cArr3[intValue])};
                                Object obj5 = o.e.a.s.get(406147795);
                                if (obj5 == null) {
                                    Class cls4 = (Class) o.e.a.c(19 - Color.alpha(0), (char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 14686), TextUtils.lastIndexOf("", '0', 0, 0) + Opcodes.LREM);
                                    byte b8 = (byte) (-1);
                                    byte b9 = (byte) (b8 + 1);
                                    Object[] objArr9 = new Object[1];
                                    l(b8, b9, b9, objArr9);
                                    obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(406147795, obj5);
                                }
                                cArr3[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                                cArr2[intValue2] = oVar.d;
                                cArr4[oVar.e] = (char) ((((cArr2[intValue2] ^ charArray3[oVar.e]) ^ (j ^ 6565854932352255525L)) ^ ((int) (h ^ 6565854932352255525L))) ^ ((char) (i ^ 6565854932352255525L)));
                                oVar.e++;
                                int i9 = $11 + 77;
                                $10 = i9 % 128;
                                int i10 = i9 % 2;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = new String(cArr4);
        }
    }

    /* renamed from: o.al.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\al\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        static final /* synthetic */ int[] a;
        private static int b;
        static final /* synthetic */ int[] c;
        private static int d;

        /* JADX WARN: Failed to find 'out' block for switch in B:13:0x004f. Please report as an issue. */
        static {
            d = 0;
            b = 1;
            int[] iArr = new int[e.values().length];
            c = iArr;
            try {
                iArr[e.b.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                c[e.a.ordinal()] = 2;
                int i = d;
                int i2 = (i ^ 29) + ((i & 29) << 1);
                b = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[e.c.ordinal()] = 3;
                int i4 = b;
                int i5 = (i4 & 87) + (i4 | 87);
                d = i5 % 128;
                switch (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            int[] iArr2 = new int[o.bb.a.values().length];
            a = iArr2;
            try {
                iArr2[o.bb.a.ay.ordinal()] = 1;
                int i6 = b;
                int i7 = (i6 & 67) + (i6 | 67);
                d = i7 % 128;
                if (i7 % 2 == 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[o.bb.a.az.ordinal()] = 2;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:11:0x0035. Please report as an issue. */
    private static void k(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 620
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.al.c.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
