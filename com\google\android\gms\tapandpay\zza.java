package com.google.android.gms.tapandpay;

import com.google.android.gms.common.Feature;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\zza.smali */
public final class zza {
    public static final Feature zzA;
    public static final Feature zzB;
    public static final Feature zzC;
    public static final Feature[] zzD;
    public static final Feature zza;
    public static final Feature zzb;
    public static final Feature zzc;
    public static final Feature zzd;
    public static final Feature zze;
    public static final Feature zzf;
    public static final Feature zzg;
    public static final Feature zzh;
    public static final Feature zzi;
    public static final Feature zzj;
    public static final Feature zzk;
    public static final Feature zzl;
    public static final Feature zzm;
    public static final Feature zzn;
    public static final Feature zzo;
    public static final Feature zzp;
    public static final Feature zzq;
    public static final Feature zzr;
    public static final Feature zzs;
    public static final Feature zzt;
    public static final Feature zzu;
    public static final Feature zzv;
    public static final Feature zzw;
    public static final Feature zzx;
    public static final Feature zzy;
    public static final Feature zzz;

    static {
        Feature feature = new Feature("tapandpay", 1L);
        zza = feature;
        Feature feature2 = new Feature("tapandpay_account_linking", 1L);
        zzb = feature2;
        Feature feature3 = new Feature("tapandpay_block_payment_cards", 1L);
        zzc = feature3;
        Feature feature4 = new Feature("tapandpay_check_contactless_eligibility", 1L);
        zzd = feature4;
        Feature feature5 = new Feature("tapandpay_dismiss_quick_access_wallet", 1L);
        zze = feature5;
        Feature feature6 = new Feature("tapandpay_felica_tos", 1L);
        zzf = feature6;
        Feature feature7 = new Feature("tapandpay_get_all_cards_for_account", 1L);
        zzg = feature7;
        Feature feature8 = new Feature("tapandpay_get_contactless_setup_configuration", 1L);
        zzh = feature8;
        Feature feature9 = new Feature("tapandpay_get_last_attestation_result", 1L);
        zzi = feature9;
        Feature feature10 = new Feature("tapandpay_get_token_details", 1L);
        zzj = feature10;
        Feature feature11 = new Feature("tapandpay_global_actions", 1L);
        zzk = feature11;
        Feature feature12 = new Feature("tapandpay_issuer_api", 2L);
        zzl = feature12;
        Feature feature13 = new Feature("tapandpay_perform_tokenization_operation", 1L);
        zzm = feature13;
        Feature feature14 = new Feature("tapandpay_push_tokenize", 1L);
        zzn = feature14;
        Feature feature15 = new Feature("tapandpay_push_tokenize_session", 6L);
        zzo = feature15;
        Feature feature16 = new Feature("tapandpay_quick_access_wallet", 1L);
        zzp = feature16;
        Feature feature17 = new Feature("tapandpay_report_unlock", 1L);
        zzq = feature17;
        Feature feature18 = new Feature("tapandpay_secureelement", 1L);
        zzr = feature18;
        Feature feature19 = new Feature("tapandpay_show_wear_card_management_view", 1L);
        zzs = feature19;
        Feature feature20 = new Feature("tapandpay_send_wear_request_to_phone", 1L);
        zzt = feature20;
        Feature feature21 = new Feature("tapandpay_sync_device_info", 1L);
        zzu = feature21;
        Feature feature22 = new Feature("tapandpay_tokenize_account", 1L);
        zzv = feature22;
        Feature feature23 = new Feature("tapandpay_tokenize_cache", 1L);
        zzw = feature23;
        Feature feature24 = new Feature("tapandpay_tokenize_pan", 1L);
        zzx = feature24;
        Feature feature25 = new Feature("tapandpay_transmission_event", 1L);
        zzy = feature25;
        Feature feature26 = new Feature("tapandpay_token_listing", 3L);
        zzz = feature26;
        Feature feature27 = new Feature("tapandpay_wallet_feedback_psd", 1L);
        zzA = feature27;
        Feature feature28 = new Feature("tapandpay_wallet_set_tap_doodle_enabled", 1L);
        zzB = feature28;
        Feature feature29 = new Feature("tapandpay_wallet_ui_shown_status", 1L);
        zzC = feature29;
        zzD = new Feature[]{feature, feature2, feature3, feature4, feature5, feature6, feature7, feature8, feature9, feature10, feature11, feature12, feature13, feature14, feature15, feature16, feature17, feature18, feature19, feature20, feature21, feature22, feature23, feature24, feature25, feature26, feature27, feature28, feature29};
    }
}
