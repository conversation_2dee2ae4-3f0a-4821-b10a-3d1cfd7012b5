package com.esotericsoftware.kryo.io;

import com.esotericsoftware.asm.Opcodes;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.util.Pool;
import com.esotericsoftware.kryo.util.Util;
import java.io.IOException;
import java.io.OutputStream;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\Output.smali */
public class Output extends OutputStream implements AutoCloseable, Pool.Poolable {
    protected byte[] buffer;
    protected int capacity;
    protected int maxCapacity;
    protected OutputStream outputStream;
    protected int position;
    protected long total;
    protected boolean varEncoding;

    public Output() {
        this.varEncoding = true;
    }

    public Output(int bufferSize) {
        this(bufferSize, bufferSize);
    }

    public Output(int bufferSize, int maxBufferSize) {
        this.varEncoding = true;
        if (bufferSize > maxBufferSize && maxBufferSize != -1) {
            throw new IllegalArgumentException("bufferSize: " + bufferSize + " cannot be greater than maxBufferSize: " + maxBufferSize);
        }
        if (maxBufferSize < -1) {
            throw new IllegalArgumentException("maxBufferSize cannot be < -1: " + maxBufferSize);
        }
        this.capacity = bufferSize;
        this.maxCapacity = maxBufferSize == -1 ? Util.maxArraySize : maxBufferSize;
        this.buffer = new byte[bufferSize];
    }

    public Output(byte[] buffer) {
        this(buffer, buffer.length);
    }

    public Output(byte[] buffer, int maxBufferSize) {
        this.varEncoding = true;
        if (buffer == null) {
            throw new IllegalArgumentException("buffer cannot be null.");
        }
        setBuffer(buffer, maxBufferSize);
    }

    public Output(OutputStream outputStream) {
        this(4096, 4096);
        if (outputStream == null) {
            throw new IllegalArgumentException("outputStream cannot be null.");
        }
        this.outputStream = outputStream;
    }

    public Output(OutputStream outputStream, int bufferSize) {
        this(bufferSize, bufferSize);
        if (outputStream == null) {
            throw new IllegalArgumentException("outputStream cannot be null.");
        }
        this.outputStream = outputStream;
    }

    public OutputStream getOutputStream() {
        return this.outputStream;
    }

    public void setOutputStream(OutputStream outputStream) {
        this.outputStream = outputStream;
        reset();
    }

    public void setBuffer(byte[] buffer) {
        setBuffer(buffer, buffer.length);
    }

    public void setBuffer(byte[] buffer, int maxBufferSize) {
        if (buffer == null) {
            throw new IllegalArgumentException("buffer cannot be null.");
        }
        if (buffer.length > maxBufferSize && maxBufferSize != -1) {
            throw new IllegalArgumentException("buffer has length: " + buffer.length + " cannot be greater than maxBufferSize: " + maxBufferSize);
        }
        if (maxBufferSize < -1) {
            throw new IllegalArgumentException("maxBufferSize cannot be < -1: " + maxBufferSize);
        }
        this.buffer = buffer;
        this.maxCapacity = maxBufferSize == -1 ? Util.maxArraySize : maxBufferSize;
        this.capacity = buffer.length;
        this.position = 0;
        this.total = 0L;
        this.outputStream = null;
    }

    public byte[] getBuffer() {
        return this.buffer;
    }

    public byte[] toBytes() {
        int i = this.position;
        byte[] newBuffer = new byte[i];
        System.arraycopy(this.buffer, 0, newBuffer, 0, i);
        return newBuffer;
    }

    public boolean getVariableLengthEncoding() {
        return this.varEncoding;
    }

    public void setVariableLengthEncoding(boolean varEncoding) {
        this.varEncoding = varEncoding;
    }

    public int position() {
        return this.position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public long total() {
        return this.total + this.position;
    }

    public int getMaxCapacity() {
        return this.maxCapacity;
    }

    public void reset() {
        this.position = 0;
        this.total = 0L;
    }

    protected boolean require(int required) throws KryoException {
        int min;
        int i;
        if (this.capacity - this.position >= required) {
            return false;
        }
        flush();
        int i2 = this.capacity;
        int i3 = this.position;
        if (i2 - i3 >= required) {
            return true;
        }
        int i4 = this.maxCapacity;
        if (required > i4 - i3) {
            if (required > i4) {
                throw new KryoBufferOverflowException("Buffer overflow. Max capacity: " + this.maxCapacity + ", required: " + required);
            }
            throw new KryoBufferOverflowException("Buffer overflow. Available: " + (this.maxCapacity - this.position) + ", required: " + required);
        }
        if (i2 == 0) {
            this.capacity = 16;
        }
        do {
            min = Math.min(this.capacity * 2, this.maxCapacity);
            this.capacity = min;
            i = this.position;
        } while (min - i < required);
        byte[] newBuffer = new byte[min];
        System.arraycopy(this.buffer, 0, newBuffer, 0, i);
        this.buffer = newBuffer;
        return true;
    }

    @Override // java.io.OutputStream, java.io.Flushable
    public void flush() throws KryoException {
        OutputStream outputStream = this.outputStream;
        if (outputStream == null) {
            return;
        }
        try {
            outputStream.write(this.buffer, 0, this.position);
            this.outputStream.flush();
            this.total += this.position;
            this.position = 0;
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }

    @Override // java.io.OutputStream, java.io.Closeable, java.lang.AutoCloseable
    public void close() throws KryoException {
        flush();
        OutputStream outputStream = this.outputStream;
        if (outputStream != null) {
            try {
                outputStream.close();
            } catch (IOException e) {
            }
        }
    }

    @Override // java.io.OutputStream
    public void write(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        bArr[i] = (byte) value;
    }

    @Override // java.io.OutputStream
    public void write(byte[] bytes) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        writeBytes(bytes, 0, bytes.length);
    }

    @Override // java.io.OutputStream
    public void write(byte[] bytes, int offset, int length) throws KryoException {
        writeBytes(bytes, offset, length);
    }

    public void writeByte(byte value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        bArr[i] = value;
    }

    public void writeByte(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        bArr[i] = (byte) value;
    }

    public void writeBytes(byte[] bytes) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        writeBytes(bytes, 0, bytes.length);
    }

    public void writeBytes(byte[] bytes, int offset, int count) throws KryoException {
        if (bytes == null) {
            throw new IllegalArgumentException("bytes cannot be null.");
        }
        int copyCount = Math.min(this.capacity - this.position, count);
        while (true) {
            System.arraycopy(bytes, offset, this.buffer, this.position, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count == 0) {
                return;
            }
            offset += copyCount;
            copyCount = Math.min(Math.max(this.capacity, 1), count);
            require(copyCount);
        }
    }

    public void writeInt(int value) throws KryoException {
        require(4);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 4;
        buffer[p] = (byte) value;
        buffer[p + 1] = (byte) (value >> 8);
        buffer[p + 2] = (byte) (value >> 16);
        buffer[p + 3] = (byte) (value >> 24);
    }

    public int writeInt(int value, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            return writeVarInt(value, optimizePositive);
        }
        writeInt(value);
        return 4;
    }

    public int writeVarInt(int value, boolean optimizePositive) throws KryoException {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 31);
        }
        if ((value >>> 7) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            byte[] bArr = this.buffer;
            int i = this.position;
            this.position = i + 1;
            bArr[i] = (byte) value;
            return 1;
        }
        if ((value >>> 14) == 0) {
            require(2);
            int p = this.position;
            this.position = p + 2;
            byte[] bArr2 = this.buffer;
            bArr2[p] = (byte) ((value & 127) | 128);
            bArr2[p + 1] = (byte) (value >>> 7);
            return 2;
        }
        if ((value >>> 21) == 0) {
            require(3);
            int p2 = this.position;
            this.position = p2 + 3;
            byte[] buffer = this.buffer;
            buffer[p2] = (byte) ((value & 127) | 128);
            buffer[p2 + 1] = (byte) ((value >>> 7) | 128);
            buffer[p2 + 2] = (byte) (value >>> 14);
            return 3;
        }
        if ((value >>> 28) == 0) {
            require(4);
            int p3 = this.position;
            this.position = p3 + 4;
            byte[] buffer2 = this.buffer;
            buffer2[p3] = (byte) ((value & 127) | 128);
            buffer2[p3 + 1] = (byte) ((value >>> 7) | 128);
            buffer2[p3 + 2] = (byte) ((value >>> 14) | 128);
            buffer2[p3 + 3] = (byte) (value >>> 21);
            return 4;
        }
        require(5);
        int p4 = this.position;
        this.position = p4 + 5;
        byte[] buffer3 = this.buffer;
        buffer3[p4] = (byte) ((value & 127) | 128);
        buffer3[p4 + 1] = (byte) ((value >>> 7) | 128);
        buffer3[p4 + 2] = (byte) ((value >>> 14) | 128);
        buffer3[p4 + 3] = (byte) ((value >>> 21) | 128);
        buffer3[p4 + 4] = (byte) (value >>> 28);
        return 5;
    }

    public int writeVarIntFlag(boolean flag, int value, boolean optimizePositive) throws KryoException {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 31);
        }
        int first = (value & 63) | (flag ? 128 : 0);
        if ((value >>> 6) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            byte[] bArr = this.buffer;
            int i = this.position;
            this.position = i + 1;
            bArr[i] = (byte) first;
            return 1;
        }
        if ((value >>> 13) == 0) {
            require(2);
            int p = this.position;
            this.position = p + 2;
            byte[] bArr2 = this.buffer;
            bArr2[p] = (byte) (first | 64);
            bArr2[p + 1] = (byte) (value >>> 6);
            return 2;
        }
        if ((value >>> 20) == 0) {
            require(3);
            byte[] buffer = this.buffer;
            int p2 = this.position;
            this.position = p2 + 3;
            buffer[p2] = (byte) (first | 64);
            buffer[p2 + 1] = (byte) (128 | (value >>> 6));
            buffer[p2 + 2] = (byte) (value >>> 13);
            return 3;
        }
        if ((value >>> 27) == 0) {
            require(4);
            byte[] buffer2 = this.buffer;
            int p3 = this.position;
            this.position = p3 + 4;
            buffer2[p3] = (byte) (first | 64);
            buffer2[p3 + 1] = (byte) ((value >>> 6) | 128);
            buffer2[p3 + 2] = (byte) (128 | (value >>> 13));
            buffer2[p3 + 3] = (byte) (value >>> 20);
            return 4;
        }
        require(5);
        byte[] buffer3 = this.buffer;
        int p4 = this.position;
        this.position = p4 + 5;
        buffer3[p4] = (byte) (first | 64);
        buffer3[p4 + 1] = (byte) ((value >>> 6) | 128);
        buffer3[p4 + 2] = (byte) ((value >>> 13) | 128);
        buffer3[p4 + 3] = (byte) (128 | (value >>> 20));
        buffer3[p4 + 4] = (byte) (value >>> 27);
        return 5;
    }

    public int intLength(int value, boolean optimizePositive) {
        if (this.varEncoding) {
            return varIntLength(value, optimizePositive);
        }
        return 4;
    }

    public void writeLong(long value) throws KryoException {
        require(8);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 8;
        buffer[p] = (byte) value;
        buffer[p + 1] = (byte) (value >>> 8);
        buffer[p + 2] = (byte) (value >>> 16);
        buffer[p + 3] = (byte) (value >>> 24);
        buffer[p + 4] = (byte) (value >>> 32);
        buffer[p + 5] = (byte) (value >>> 40);
        buffer[p + 6] = (byte) (value >>> 48);
        buffer[p + 7] = (byte) (value >>> 56);
    }

    public int writeLong(long value, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            return writeVarLong(value, optimizePositive);
        }
        writeLong(value);
        return 8;
    }

    public int writeVarLong(long value, boolean optimizePositive) throws KryoException {
        long value2 = !optimizePositive ? (value << 1) ^ (value >> 63) : value;
        if ((value2 >>> 7) == 0) {
            if (this.position == this.capacity) {
                require(1);
            }
            byte[] bArr = this.buffer;
            int i = this.position;
            this.position = i + 1;
            bArr[i] = (byte) value2;
            return 1;
        }
        if ((value2 >>> 14) == 0) {
            require(2);
            int p = this.position;
            this.position = p + 2;
            byte[] bArr2 = this.buffer;
            bArr2[p] = (byte) ((value2 & 127) | 128);
            bArr2[p + 1] = (byte) (value2 >>> 7);
            return 2;
        }
        if ((value2 >>> 21) == 0) {
            require(3);
            int p2 = this.position;
            this.position = p2 + 3;
            byte[] buffer = this.buffer;
            buffer[p2] = (byte) ((value2 & 127) | 128);
            buffer[p2 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer[p2 + 2] = (byte) (value2 >>> 14);
            return 3;
        }
        if ((value2 >>> 28) == 0) {
            require(4);
            int p3 = this.position;
            this.position = p3 + 4;
            byte[] buffer2 = this.buffer;
            buffer2[p3] = (byte) ((127 & value2) | 128);
            buffer2[p3 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer2[p3 + 2] = (byte) ((value2 >>> 14) | 128);
            buffer2[p3 + 3] = (byte) (value2 >>> 21);
            return 4;
        }
        if ((value2 >>> 35) == 0) {
            require(5);
            int p4 = this.position;
            this.position = p4 + 5;
            byte[] buffer3 = this.buffer;
            buffer3[p4] = (byte) ((127 & value2) | 128);
            buffer3[p4 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer3[p4 + 2] = (byte) ((value2 >>> 14) | 128);
            buffer3[p4 + 3] = (byte) ((value2 >>> 21) | 128);
            buffer3[p4 + 4] = (byte) (value2 >>> 28);
            return 5;
        }
        if ((value2 >>> 42) == 0) {
            require(6);
            int p5 = this.position;
            this.position = p5 + 6;
            byte[] buffer4 = this.buffer;
            buffer4[p5] = (byte) ((127 & value2) | 128);
            buffer4[p5 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer4[p5 + 2] = (byte) ((value2 >>> 14) | 128);
            buffer4[p5 + 3] = (byte) ((value2 >>> 21) | 128);
            buffer4[p5 + 4] = (byte) ((value2 >>> 28) | 128);
            buffer4[p5 + 5] = (byte) (value2 >>> 35);
            return 6;
        }
        if ((value2 >>> 49) == 0) {
            require(7);
            int p6 = this.position;
            this.position = p6 + 7;
            byte[] buffer5 = this.buffer;
            buffer5[p6] = (byte) ((value2 & 127) | 128);
            buffer5[p6 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer5[p6 + 2] = (byte) ((value2 >>> 14) | 128);
            buffer5[p6 + 3] = (byte) ((value2 >>> 21) | 128);
            buffer5[p6 + 4] = (byte) ((value2 >>> 28) | 128);
            buffer5[p6 + 5] = (byte) ((value2 >>> 35) | 128);
            buffer5[p6 + 6] = (byte) (value2 >>> 42);
            return 7;
        }
        if ((value2 >>> 56) == 0) {
            require(8);
            int p7 = this.position;
            this.position = p7 + 8;
            byte[] buffer6 = this.buffer;
            buffer6[p7] = (byte) ((127 & value2) | 128);
            buffer6[p7 + 1] = (byte) ((value2 >>> 7) | 128);
            buffer6[p7 + 2] = (byte) ((value2 >>> 14) | 128);
            buffer6[p7 + 3] = (byte) ((value2 >>> 21) | 128);
            buffer6[p7 + 4] = (byte) ((value2 >>> 28) | 128);
            buffer6[p7 + 5] = (byte) ((value2 >>> 35) | 128);
            buffer6[p7 + 6] = (byte) ((value2 >>> 42) | 128);
            buffer6[p7 + 7] = (byte) (value2 >>> 49);
            return 8;
        }
        require(9);
        int p8 = this.position;
        this.position = p8 + 9;
        byte[] buffer7 = this.buffer;
        buffer7[p8] = (byte) ((127 & value2) | 128);
        buffer7[p8 + 1] = (byte) ((value2 >>> 7) | 128);
        buffer7[p8 + 2] = (byte) ((value2 >>> 14) | 128);
        buffer7[p8 + 3] = (byte) ((value2 >>> 21) | 128);
        buffer7[p8 + 4] = (byte) ((value2 >>> 28) | 128);
        buffer7[p8 + 5] = (byte) ((value2 >>> 35) | 128);
        buffer7[p8 + 6] = (byte) ((value2 >>> 42) | 128);
        buffer7[p8 + 7] = (byte) ((value2 >>> 49) | 128);
        buffer7[p8 + 8] = (byte) (value2 >>> 56);
        return 9;
    }

    public int longLength(int value, boolean optimizePositive) {
        if (this.varEncoding) {
            return varLongLength(value, optimizePositive);
        }
        return 8;
    }

    public void writeFloat(float value) throws KryoException {
        require(4);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 4;
        int intValue = Float.floatToIntBits(value);
        buffer[p] = (byte) intValue;
        buffer[p + 1] = (byte) (intValue >> 8);
        buffer[p + 2] = (byte) (intValue >> 16);
        buffer[p + 3] = (byte) (intValue >> 24);
    }

    public int writeVarFloat(float value, float precision, boolean optimizePositive) throws KryoException {
        return writeVarInt((int) (value * precision), optimizePositive);
    }

    public void writeDouble(double value) throws KryoException {
        require(8);
        byte[] buffer = this.buffer;
        int p = this.position;
        this.position = p + 8;
        long longValue = Double.doubleToLongBits(value);
        buffer[p] = (byte) longValue;
        buffer[p + 1] = (byte) (longValue >>> 8);
        buffer[p + 2] = (byte) (longValue >>> 16);
        buffer[p + 3] = (byte) (longValue >>> 24);
        buffer[p + 4] = (byte) (longValue >>> 32);
        buffer[p + 5] = (byte) (longValue >>> 40);
        buffer[p + 6] = (byte) (longValue >>> 48);
        buffer[p + 7] = (byte) (longValue >>> 56);
    }

    public int writeVarDouble(double value, double precision, boolean optimizePositive) throws KryoException {
        return writeVarLong((long) (value * precision), optimizePositive);
    }

    public void writeShort(int value) throws KryoException {
        require(2);
        int p = this.position;
        this.position = p + 2;
        byte[] bArr = this.buffer;
        bArr[p] = (byte) value;
        bArr[p + 1] = (byte) (value >>> 8);
    }

    public void writeChar(char value) throws KryoException {
        require(2);
        int p = this.position;
        this.position = p + 2;
        byte[] bArr = this.buffer;
        bArr[p] = (byte) value;
        bArr[p + 1] = (byte) (value >>> '\b');
    }

    public void writeBoolean(boolean z) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        byte[] bArr = this.buffer;
        int i = this.position;
        this.position = i + 1;
        bArr[i] = z ? (byte) 1 : (byte) 0;
    }

    public void writeString(String value) throws KryoException {
        if (value == null) {
            writeByte(128);
            return;
        }
        int charCount = value.length();
        if (charCount == 0) {
            writeByte(Opcodes.LOR);
            return;
        }
        if (charCount > 1 && charCount <= 32) {
            for (int i = 0; i < charCount; i++) {
                if (value.charAt(i) <= 127) {
                }
            }
            int i2 = this.capacity;
            int i3 = this.position;
            if (i2 - i3 < charCount) {
                writeAscii_slow(value, charCount);
            } else {
                value.getBytes(0, charCount, this.buffer, i3);
                this.position += charCount;
            }
            byte[] bArr = this.buffer;
            int i4 = this.position - 1;
            bArr[i4] = (byte) (128 | bArr[i4]);
            return;
        }
        writeVarIntFlag(true, charCount + 1, true);
        int charIndex = 0;
        if (this.capacity - this.position >= charCount) {
            byte[] buffer = this.buffer;
            int p = this.position;
            while (true) {
                int c = value.charAt(charIndex);
                if (c <= 127) {
                    int p2 = p + 1;
                    buffer[p] = (byte) c;
                    charIndex++;
                    if (charIndex == charCount) {
                        this.position = p2;
                        return;
                    }
                    p = p2;
                } else {
                    this.position = p;
                    break;
                }
            }
        }
        if (charIndex < charCount) {
            writeUtf8_slow(value, charCount, charIndex);
        }
    }

    public void writeAscii(String value) throws KryoException {
        if (value == null) {
            writeByte(128);
        }
        int charCount = value.length();
        switch (charCount) {
            case 0:
                writeByte(Opcodes.LOR);
                break;
            case 1:
                require(2);
                byte[] bArr = this.buffer;
                int i = this.position;
                int i2 = i + 1;
                this.position = i2;
                bArr[i] = -126;
                this.position = i2 + 1;
                bArr[i2] = (byte) value.charAt(0);
                break;
            default:
                int i3 = this.capacity;
                int i4 = this.position;
                if (i3 - i4 >= charCount) {
                    value.getBytes(0, charCount, this.buffer, i4);
                    this.position += charCount;
                } else {
                    writeAscii_slow(value, charCount);
                }
                byte[] bArr2 = this.buffer;
                int i5 = this.position - 1;
                bArr2[i5] = (byte) (128 | bArr2[i5]);
                break;
        }
    }

    private void writeUtf8_slow(String value, int charCount, int charIndex) {
        while (charIndex < charCount) {
            int i = this.position;
            int i2 = this.capacity;
            if (i == i2) {
                require(Math.min(i2, charCount - charIndex));
            }
            int c = value.charAt(charIndex);
            if (c <= 127) {
                byte[] bArr = this.buffer;
                int i3 = this.position;
                this.position = i3 + 1;
                bArr[i3] = (byte) c;
            } else if (c > 2047) {
                byte[] bArr2 = this.buffer;
                int i4 = this.position;
                this.position = i4 + 1;
                bArr2[i4] = (byte) (((c >> 12) & 15) | BERTags.FLAGS);
                require(2);
                byte[] bArr3 = this.buffer;
                int i5 = this.position;
                int i6 = i5 + 1;
                this.position = i6;
                bArr3[i5] = (byte) (((c >> 6) & 63) | 128);
                this.position = i6 + 1;
                bArr3[i6] = (byte) ((c & 63) | 128);
            } else {
                byte[] bArr4 = this.buffer;
                int i7 = this.position;
                int i8 = i7 + 1;
                this.position = i8;
                bArr4[i7] = (byte) (((c >> 6) & 31) | 192);
                if (i8 == this.capacity) {
                    require(1);
                }
                byte[] bArr5 = this.buffer;
                int i9 = this.position;
                this.position = i9 + 1;
                bArr5[i9] = (byte) ((c & 63) | 128);
            }
            charIndex++;
        }
    }

    private void writeAscii_slow(String value, int charCount) throws KryoException {
        if (charCount == 0) {
            return;
        }
        if (this.position == this.capacity) {
            require(1);
        }
        int charIndex = 0;
        byte[] buffer = this.buffer;
        int charsToWrite = Math.min(charCount, this.capacity - this.position);
        while (charIndex < charCount) {
            value.getBytes(charIndex, charIndex + charsToWrite, buffer, this.position);
            charIndex += charsToWrite;
            this.position += charsToWrite;
            charsToWrite = Math.min(charCount - charIndex, this.capacity);
            if (require(charsToWrite)) {
                buffer = this.buffer;
            }
        }
    }

    public void writeInts(int[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 2)) {
            require(count << 2);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                int value = array[offset];
                buffer[p] = (byte) value;
                buffer[p + 1] = (byte) (value >> 8);
                buffer[p + 2] = (byte) (value >> 16);
                buffer[p + 3] = (byte) (value >> 24);
                offset++;
                p += 4;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeInt(array[offset]);
            offset++;
        }
    }

    public void writeInts(int[] array, int offset, int count, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            int n = offset + count;
            while (offset < n) {
                writeVarInt(array[offset], optimizePositive);
                offset++;
            }
            return;
        }
        writeInts(array, offset, count);
    }

    public void writeLongs(long[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 3)) {
            require(count << 3);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                long value = array[offset];
                buffer[p] = (byte) value;
                buffer[p + 1] = (byte) (value >>> 8);
                buffer[p + 2] = (byte) (value >>> 16);
                buffer[p + 3] = (byte) (value >>> 24);
                buffer[p + 4] = (byte) (value >>> 32);
                buffer[p + 5] = (byte) (value >>> 40);
                buffer[p + 6] = (byte) (value >>> 48);
                buffer[p + 7] = (byte) (value >>> 56);
                offset++;
                p += 8;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeLong(array[offset]);
            offset++;
        }
    }

    public void writeLongs(long[] array, int offset, int count, boolean optimizePositive) throws KryoException {
        if (this.varEncoding) {
            int n = offset + count;
            while (offset < n) {
                writeVarLong(array[offset], optimizePositive);
                offset++;
            }
            return;
        }
        writeLongs(array, offset, count);
    }

    public void writeFloats(float[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 2)) {
            require(count << 2);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                int value = Float.floatToIntBits(array[offset]);
                buffer[p] = (byte) value;
                buffer[p + 1] = (byte) (value >> 8);
                buffer[p + 2] = (byte) (value >> 16);
                buffer[p + 3] = (byte) (value >> 24);
                offset++;
                p += 4;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeFloat(array[offset]);
            offset++;
        }
    }

    public void writeDoubles(double[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 3)) {
            require(count << 3);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                long value = Double.doubleToLongBits(array[offset]);
                buffer[p] = (byte) value;
                buffer[p + 1] = (byte) (value >>> 8);
                buffer[p + 2] = (byte) (value >>> 16);
                buffer[p + 3] = (byte) (value >>> 24);
                buffer[p + 4] = (byte) (value >>> 32);
                buffer[p + 5] = (byte) (value >>> 40);
                buffer[p + 6] = (byte) (value >>> 48);
                buffer[p + 7] = (byte) (value >>> 56);
                offset++;
                p += 8;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeDouble(array[offset]);
            offset++;
        }
    }

    public void writeShorts(short[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 1)) {
            require(count << 1);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                short s = array[offset];
                buffer[p] = (byte) s;
                buffer[p + 1] = (byte) (s >>> 8);
                offset++;
                p += 2;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeShort(array[offset]);
            offset++;
        }
    }

    public void writeChars(char[] array, int offset, int count) throws KryoException {
        if (this.capacity >= (count << 1)) {
            require(count << 1);
            byte[] buffer = this.buffer;
            int p = this.position;
            int n = offset + count;
            while (offset < n) {
                char c = array[offset];
                buffer[p] = (byte) c;
                buffer[p + 1] = (byte) (c >>> '\b');
                offset++;
                p += 2;
            }
            this.position = p;
            return;
        }
        int n2 = offset + count;
        while (offset < n2) {
            writeChar(array[offset]);
            offset++;
        }
    }

    public void writeBooleans(boolean[] zArr, int i, int i2) throws KryoException {
        if (this.capacity >= i2) {
            require(i2);
            byte[] bArr = this.buffer;
            int i3 = this.position;
            int i4 = i + i2;
            while (i < i4) {
                bArr[i3] = zArr[i] ? (byte) 1 : (byte) 0;
                i++;
                i3++;
            }
            this.position = i3;
            return;
        }
        int i5 = i + i2;
        while (i < i5) {
            writeBoolean(zArr[i]);
            i++;
        }
    }

    public static int varIntLength(int value, boolean optimizePositive) {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 31);
        }
        if ((value >>> 7) == 0) {
            return 1;
        }
        if ((value >>> 14) == 0) {
            return 2;
        }
        if ((value >>> 21) == 0) {
            return 3;
        }
        return (value >>> 28) == 0 ? 4 : 5;
    }

    public static int varLongLength(long value, boolean optimizePositive) {
        if (!optimizePositive) {
            value = (value << 1) ^ (value >> 63);
        }
        if ((value >>> 7) == 0) {
            return 1;
        }
        if ((value >>> 14) == 0) {
            return 2;
        }
        if ((value >>> 21) == 0) {
            return 3;
        }
        if ((value >>> 28) == 0) {
            return 4;
        }
        if ((value >>> 35) == 0) {
            return 5;
        }
        if ((value >>> 42) == 0) {
            return 6;
        }
        if ((value >>> 49) == 0) {
            return 7;
        }
        return (value >>> 56) == 0 ? 8 : 9;
    }
}
