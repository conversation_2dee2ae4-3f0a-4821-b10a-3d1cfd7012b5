package o.ac;

import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\c.smali */
public final class c implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static boolean b;
    private static int c;
    private static char[] d;
    private static int g;
    private static int h;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        d = new char[]{61660, 61671, 61678, 61682, 61677, 61672, 61679, 61680, 61632, 61667, 61596, 61687, 61681, 61665, 61675, 61666, 61685, 61619, 61664, 61620, 61627, 61618, 61668};
        a = true;
        b = true;
        c = 782102908;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.ac.c.$$a
            int r9 = r9 * 4
            int r9 = r9 + 1
            int r7 = 121 - r7
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            int r9 = r9 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r9) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            int r8 = r8 + 1
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r9 = -r9
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.c.i(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{26, 103, -21, 32};
        $$b = 244;
    }

    public c(String str) {
        this.e = str;
    }

    public final String c() {
        int i = g;
        int i2 = i + 25;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? 'F' : '\\') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                String str = this.e;
                int i3 = i + 79;
                h = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final void a() throws WalletValidationException {
        int i = h + Opcodes.LSUB;
        g = i % 128;
        int i2 = i % 2;
        String str = this.e;
        Object[] objArr = new Object[1];
        f(null, 126 - TextUtils.lastIndexOf("", '0', 0), null, "\u0084\u008a\u0097\u008e\u008b\u0096\u0095\u0092\u0094\u008b\u0093\u0083\u008a\u008b\u0092\u008b\u0083\u0082\u0082\u0091\u0088\u0082\u0090\u008b\u0083\u008f\u008a\u0088\u0083\u0085\u008e\u008b\u0088\u008d\u008c\u0087\u008b\u0081\u008a\u0088\u008a\u0089\u0088\u0083\u0082\u0087\u0086\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        c(str, 1, 8192, ((String) objArr[0]).intern());
        int i3 = h + 61;
        g = i3 % 128;
        switch (i3 % 2 != 0 ? '\r' : 'F') {
            case '\r':
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 880
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.c.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
