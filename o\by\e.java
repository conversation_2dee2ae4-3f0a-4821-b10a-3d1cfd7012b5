package o.by;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.bm.e;
import o.i.f;
import o.i.g;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\by\e.smali */
public final class e extends c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int b;
    private static int g;
    private final f c;
    List<g> d;
    final boolean e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        g = 1;
        c();
        ViewConfiguration.getMinimumFlingVelocity();
        Color.alpha(0);
        ViewConfiguration.getFadingEdgeLength();
        TextUtils.indexOf("", "", 0, 0);
        int i = b + 49;
        g = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        a = new int[]{655685324, -1026970885, 985296449, 1011115454, -1567165426, 21357172, -1419085121, 1797665819, 69352964, 1945193810, 104344720, 132645646, -1577066167, -708592847, -351775431, 1119993375, -456457937, 1920143479};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.by.e.$$a
            int r7 = r7 + 115
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r9 = r9 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            int r9 = r9 + 1
            r3 = r0[r9]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L35:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.by.e.h(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{79, Tnaf.POW_2_WIDTH, 60, 65};
        $$b = 232;
    }

    e(Context context, d dVar, o.ei.c cVar, boolean z, f fVar) {
        super(context, dVar, cVar);
        this.e = z;
        this.c = fVar;
    }

    @Override // o.by.c
    public final void e(o.h.d dVar) throws WalletValidationException {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f(new int[]{782460325, 698065371, 513546392, -179351272, 1631631083, 970911419, 1130540133, -1326838203, 211692293, -1497606493, -463406923, 1786340776}, 24 - ExpandableListView.getPackedPositionType(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(new int[]{-845480530, -378163857}, 3 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        g e = o.i.d.c().e(this.c);
        if (e == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unknown;
            Object[] objArr3 = new Object[1];
            f(new int[]{-941707611, 786789402, 1903714014, 1732199715, 1505799297, -683832369, -33637514, -518116804, -1076374219, -624726653, 69733241, -1603043113, 1858525857, 1981620303}, 28 - View.getDefaultSize(0, 0), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        switch (this.e) {
            case false:
                if (!e.j().equals(o.i.c.c)) {
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    f(new int[]{782460325, 698065371, 513546392, -179351272, 1631631083, 970911419, 1130540133, -1326838203, 211692293, -1497606493, -463406923, 1786340776}, 24 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    Object[] objArr5 = new Object[1];
                    f(new int[]{-2064236436, 354086102, 575490428, 1445550789, 1681703185, 71306723, 2277132, 705776084, -1747881438, 1433278504, -225038989, 864459089, -397541329, 1659528723, -1860629658, 1563025866, 392889794, -92417136, -1077876626, 394883589, -900881450, -1179327672, 2055849783, 1209256126, 1280892658, -1247436843, 582042356, 821516752, 1585257244, -449134797}, 58 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr5);
                    o.ee.g.d(intern2, String.format(((String) objArr5[0]).intern(), this.c.name()));
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                    Object[] objArr6 = new Object[1];
                    f(new int[]{-941707611, 786789402, 1903714014, 1732199715, 1505799297, -683832369, -33637514, -518116804, -1076374219, -624726653, 69733241, -1603043113, 1858525857, 1981620303}, 28 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr6);
                    String intern3 = ((String) objArr6[0]).intern();
                    Object[] objArr7 = new Object[1];
                    f(new int[]{403347211, 1269474557, 2014772083, 256798465, 1926938598, 717203386, -1729222611, 1829107998, -1613830132, 1573151427, 1497263509, 249935543, -519604508, 843406309, 280934049, -475822933, -630123600, -661630738, -1664432072, -370754261, 1858525857, 1981620303, 392889794, -92417136, -1077876626, 394883589, -900881450, -1179327672, 2055849783, 1209256126, 1280892658, -1247436843, 1441420239, -1211272489}, Color.red(0) + 67, objArr7);
                    throw new WalletValidationException(walletValidationErrorCode2, intern3, ((String) objArr7[0]).intern());
                }
                break;
            default:
                int i = g + 99;
                b = i % 128;
                if (i % 2 != 0) {
                }
                if (!e.j().equals(o.i.c.a)) {
                    o.ee.g.c();
                    Object[] objArr8 = new Object[1];
                    f(new int[]{782460325, 698065371, 513546392, -179351272, 1631631083, 970911419, 1130540133, -1326838203, 211692293, -1497606493, -463406923, 1786340776}, 23 - TextUtils.lastIndexOf("", '0', 0, 0), objArr8);
                    String intern4 = ((String) objArr8[0]).intern();
                    Object[] objArr9 = new Object[1];
                    f(new int[]{-2064236436, 354086102, 714425859, 1863924154, 1280892658, -1247436843, -241018103, -319444258, -606804803, 1838097173, -1085740011, 1582924166, -1644269753, 1105382764, -323574157, -1659424298, 246366028, -17380749, -2016306129, -1524661113, -371713994, 836684448, 1280892658, -1247436843, 582042356, 821516752, 1824029818, -1951959532, -1054084532, -2077573972, -1406685974, -906093354, 724880753, 2016891435, 1176891501, 1991073348}, 72 - Color.green(0), objArr9);
                    o.ee.g.d(intern4, String.format(((String) objArr9[0]).intern(), this.c.name()));
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
                    Object[] objArr10 = new Object[1];
                    f(new int[]{-941707611, 786789402, 1903714014, 1732199715, 1505799297, -683832369, -33637514, -518116804, -1076374219, -624726653, 69733241, -1603043113, 1858525857, 1981620303}, 28 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr10);
                    String intern5 = ((String) objArr10[0]).intern();
                    Object[] objArr11 = new Object[1];
                    f(new int[]{403347211, 1269474557, -988592500, -1309345371, 1417450568, 1993856572, 958006790, -1110410565, 883776959, -1919687294, -1498742849, -1144395902, -1090397013, -1854397263, 558993464, -661933191, 1903714014, 1732199715, 405655271, -1304490556, -623153670, -431788115, 1963141073, -1358002788, -710515451, -562966477, 1352334201, 505702357, 1128528011, -1593485693, -677563080, -1127671074, 529715853, -965772210, -1959316231, 2014455981, 220339643, 385452101, 1280892658, -1247436843, -463406923, 1786340776}, 84 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr11);
                    throw new WalletValidationException(walletValidationErrorCode3, intern5, ((String) objArr11[0]).intern());
                }
                break;
        }
        ArrayList arrayList = new ArrayList();
        this.d = arrayList;
        arrayList.add(e);
        o.bm.d dVar2 = new o.bm.d() { // from class: o.by.e.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int b;
            private static int d;
            private static byte[] e;
            private static int f;
            private static short[] h;
            private static int i;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                f = 0;
                i = 1;
                e = new byte[]{125, -124, 119, -111, 109, -113, 119, -93, 94, 124, -125, -111, 111, -93, 100, 117, -79, 92, 115, -111, ByteCompanionObject.MAX_VALUE, 118, -103, 87, 87, -92, -112, ByteCompanionObject.MAX_VALUE, -95, 80, -95, 81, -70, -77, 77, 77, 92, 94, -111, 66, 83, -79, 77, -81, 85, -85, 92, 76, -74, 68, -73, 76, 91, -100, PSSSigner.TRAILER_IMPLICIT, 69, -74, 80, -67, -71, -74, 69, 74, -66, -73, PSSSigner.TRAILER_IMPLICIT, 119, -97, -84, 78, -74, 82, 65, -112, -112, -112};
                b = 909053667;
                a = -1234043136;
                d = -689422349;
            }

            static void init$0() {
                $$a = new byte[]{106, 35, -45, 57};
                $$b = Opcodes.INEG;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void j(int r7, int r8, short r9, java.lang.Object[] r10) {
                /*
                    int r7 = r7 * 2
                    int r7 = 110 - r7
                    int r9 = r9 * 4
                    int r9 = 3 - r9
                    int r8 = r8 * 2
                    int r8 = 1 - r8
                    byte[] r0 = o.by.e.AnonymousClass3.$$a
                    byte[] r1 = new byte[r8]
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    r9 = r8
                    goto L35
                L1a:
                    r3 = r2
                L1b:
                    int r4 = r3 + 1
                    byte r5 = (byte) r7
                    r1[r3] = r5
                    if (r4 != r8) goto L2a
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2a:
                    int r9 = r9 + 1
                    r3 = r0[r9]
                    r6 = r9
                    r9 = r8
                    r8 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r10
                    r10 = r6
                L35:
                    int r7 = r7 + r8
                    r8 = r9
                    r9 = r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.by.e.AnonymousClass3.j(int, int, short, java.lang.Object[]):void");
            }

            @Override // o.bm.d
            public final o.eg.b a() throws o.eg.d {
                o.ee.g.c();
                Object[] objArr12 = new Object[1];
                g((byte) (18 - (ViewConfiguration.getTouchSlop() >> 8)), 523816606 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), (-91) - Color.blue(0), (ViewConfiguration.getEdgeSlop() >> 16) + 2141261509, objArr12);
                String intern6 = ((String) objArr12[0]).intern();
                Object[] objArr13 = new Object[1];
                g((byte) (51 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (ViewConfiguration.getWindowTouchSlop() >> 8) + 523816628, (short) Color.red(0), (-90) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 2141261528 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr13);
                o.ee.g.d(intern6, ((String) objArr13[0]).intern());
                o.i.d c = o.i.d.c();
                Context d2 = e.this.d();
                e.this.b();
                o.eg.e c2 = c.c(d2, e.this.e, e.this.d);
                o.eg.b bVar = new o.eg.b();
                Object[] objArr14 = new Object[1];
                g((byte) (TextUtils.lastIndexOf("", '0', 0) - 44), 523816651 - ExpandableListView.getPackedPositionChild(0L), (short) TextUtils.indexOf("", "", 0, 0), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) - 88, TextUtils.indexOf("", "", 0) + 2141261521, objArr14);
                bVar.d(((String) objArr14[0]).intern(), c2);
                int i2 = f + 67;
                i = i2 % 128;
                int i3 = i2 % 2;
                return bVar;
            }

            @Override // o.bm.d
            public final o.bb.d a(o.bb.d dVar3) {
                o.i.d.c();
                o.i.d.a(e.this.b(), e.this.e, e.this.d, dVar3.b());
                switch (e.this.b().d().a() ? Typography.less : '[') {
                    case '<':
                        int i2 = i + 63;
                        f = i2 % 128;
                        switch (i2 % 2 != 0 ? 'A' : 'M') {
                            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                e.this.b().d().b(e.this.d());
                                throw null;
                            default:
                                e.this.b().d().b(e.this.d());
                                break;
                        }
                }
                int i3 = i + 67;
                f = i3 % 128;
                int i4 = i3 % 2;
                return dVar3;
            }

            private static void g(byte b2, int i2, short s, int i3, int i4, Object[] objArr12) {
                int i5;
                int length;
                byte[] bArr;
                int i6;
                o.a.f fVar = new o.a.f();
                StringBuilder sb = new StringBuilder();
                int i7 = 2;
                try {
                    Object[] objArr13 = {Integer.valueOf(i3), Integer.valueOf(b)};
                    Object obj = o.e.a.s.get(-2120899312);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(11 - Color.argb(0, 0, 0, 0), (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 64);
                        byte b3 = (byte) 1;
                        byte b4 = (byte) (b3 - 1);
                        Object[] objArr14 = new Object[1];
                        j(b3, b4, b4, objArr14);
                        obj = cls.getMethod((String) objArr14[0], Integer.TYPE, Integer.TYPE);
                        o.e.a.s.put(-2120899312, obj);
                    }
                    int intValue = ((Integer) ((Method) obj).invoke(null, objArr13)).intValue();
                    boolean z = intValue == -1;
                    char c = '0';
                    if (z) {
                        byte[] bArr2 = e;
                        switch (bArr2 == null) {
                            case false:
                                int length2 = bArr2.length;
                                byte[] bArr3 = new byte[length2];
                                int i8 = 0;
                                while (true) {
                                    switch (i8 < length2 ? (char) 17 : ',') {
                                        case ',':
                                            bArr2 = bArr3;
                                        default:
                                            int i9 = $11 + Opcodes.DDIV;
                                            $10 = i9 % 128;
                                            if (i9 % i7 != 0) {
                                                try {
                                                    Object[] objArr15 = {Integer.valueOf(bArr2[i8])};
                                                    Object obj2 = o.e.a.s.get(494867332);
                                                    if (obj2 == null) {
                                                        Class cls2 = (Class) o.e.a.c(KeyEvent.keyCodeFromString("") + 19, (char) (16473 - AndroidCharacter.getMirror(c)), (ViewConfiguration.getFadingEdgeLength() >> 16) + Opcodes.FCMPG);
                                                        byte b5 = (byte) 0;
                                                        byte b6 = b5;
                                                        Object[] objArr16 = new Object[1];
                                                        j(b5, b6, b6, objArr16);
                                                        obj2 = cls2.getMethod((String) objArr16[0], Integer.TYPE);
                                                        o.e.a.s.put(494867332, obj2);
                                                    }
                                                    bArr3[i8] = ((Byte) ((Method) obj2).invoke(null, objArr15)).byteValue();
                                                    i8 <<= 0;
                                                    i7 = 2;
                                                    c = '0';
                                                } catch (Throwable th) {
                                                    Throwable cause = th.getCause();
                                                    if (cause == null) {
                                                        throw th;
                                                    }
                                                    throw cause;
                                                }
                                            } else {
                                                try {
                                                    Object[] objArr17 = {Integer.valueOf(bArr2[i8])};
                                                    Object obj3 = o.e.a.s.get(494867332);
                                                    if (obj3 == null) {
                                                        Class cls3 = (Class) o.e.a.c(KeyEvent.getDeadChar(0, 0) + 19, (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 16424), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + Opcodes.FCMPG);
                                                        byte b7 = (byte) 0;
                                                        byte b8 = b7;
                                                        Object[] objArr18 = new Object[1];
                                                        j(b7, b8, b8, objArr18);
                                                        obj3 = cls3.getMethod((String) objArr18[0], Integer.TYPE);
                                                        o.e.a.s.put(494867332, obj3);
                                                    }
                                                    bArr3[i8] = ((Byte) ((Method) obj3).invoke(null, objArr17)).byteValue();
                                                    i8++;
                                                    i7 = 2;
                                                    c = '0';
                                                } catch (Throwable th2) {
                                                    Throwable cause2 = th2.getCause();
                                                    if (cause2 == null) {
                                                        throw th2;
                                                    }
                                                    throw cause2;
                                                }
                                            }
                                    }
                                }
                            default:
                                if (bArr2 == null) {
                                    intValue = (short) (((short) (h[i2 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                                    break;
                                } else {
                                    byte[] bArr4 = e;
                                    try {
                                        Object[] objArr19 = {Integer.valueOf(i2), Integer.valueOf(d)};
                                        Object obj4 = o.e.a.s.get(-2120899312);
                                        if (obj4 == null) {
                                            Class cls4 = (Class) o.e.a.c(Color.alpha(0) + 11, (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), ExpandableListView.getPackedPositionChild(0L) + 66);
                                            byte b9 = (byte) 1;
                                            byte b10 = (byte) (b9 - 1);
                                            Object[] objArr20 = new Object[1];
                                            j(b9, b10, b10, objArr20);
                                            obj4 = cls4.getMethod((String) objArr20[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(-2120899312, obj4);
                                        }
                                        intValue = (byte) (((byte) (bArr4[((Integer) ((Method) obj4).invoke(null, objArr19)).intValue()] ^ (-5810760824076169584L))) + ((int) (b ^ (-5810760824076169584L))));
                                        break;
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                        }
                    }
                    switch (intValue > 0 ? 'Z' : (char) 15) {
                        case 15:
                            break;
                        default:
                            int i10 = ((i2 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                            switch (!z) {
                                case true:
                                    i5 = 0;
                                    break;
                                default:
                                    i5 = 1;
                                    break;
                            }
                            fVar.d = i10 + i5;
                            try {
                                Object[] objArr21 = {fVar, Integer.valueOf(i4), Integer.valueOf(a), sb};
                                Object obj5 = o.e.a.s.get(160906762);
                                if (obj5 == null) {
                                    obj5 = ((Class) o.e.a.c(';' - AndroidCharacter.getMirror('0'), (char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), TextUtils.getOffsetBefore("", 0) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                                    o.e.a.s.put(160906762, obj5);
                                }
                                ((StringBuilder) ((Method) obj5).invoke(null, objArr21)).append(fVar.e);
                                fVar.b = fVar.e;
                                byte[] bArr5 = e;
                                if (bArr5 != null) {
                                    int i11 = $11 + 25;
                                    $10 = i11 % 128;
                                    if (i11 % 2 != 0) {
                                        length = bArr5.length;
                                        bArr = new byte[length];
                                        i6 = 1;
                                    } else {
                                        length = bArr5.length;
                                        bArr = new byte[length];
                                        i6 = 0;
                                    }
                                    while (i6 < length) {
                                        int i12 = $10 + 99;
                                        $11 = i12 % 128;
                                        int i13 = i12 % 2;
                                        bArr[i6] = (byte) (bArr5[i6] ^ (-5810760824076169584L));
                                        i6++;
                                    }
                                    bArr5 = bArr;
                                }
                                boolean z2 = bArr5 != null;
                                fVar.c = 1;
                                while (true) {
                                    switch (fVar.c < intValue ? 'a' : 'T') {
                                        case Opcodes.LADD /* 97 */:
                                            if (z2) {
                                                byte[] bArr6 = e;
                                                fVar.d = fVar.d - 1;
                                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                            } else {
                                                short[] sArr = h;
                                                fVar.d = fVar.d - 1;
                                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                            }
                                            sb.append(fVar.e);
                                            fVar.b = fVar.e;
                                            fVar.c++;
                                    }
                                }
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                            break;
                    }
                    objArr12[0] = sb.toString();
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
            }
        };
        o.bm.e eVar = new o.bm.e(d(), new e.b() { // from class: o.by.e.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int c;
            private static char[] e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                a = 0;
                c = 1;
                e = new char[]{50938, 50851, 50849, 50851, 50855, 50852, 50854, 50855, 50834, 50860, 50873, 50873, 50855, 50855, 50862, 50861, 50876, 50833, 50841, 50849, 50855, 50863, 50855, 50863, 50932, 50878, 50849, 50857, 50862, 50849, 50857, 50832, 50853, 50858, 50855, 50851, 50836, 50841, 50849, 50855, 50863, 50855, 50863, 50860, 50851, 50921, 50834, 50873, 50877, 50848, 50851, 50833, 50836, 50854, 50877, 50875, 50868, 50876, 50877, 50873, 50858, 50853, 50877, 50853, 50877, 50879};
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0036). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r6, int r7, short r8, java.lang.Object[] r9) {
                /*
                    int r6 = r6 * 3
                    int r6 = r6 + 4
                    int r8 = 122 - r8
                    int r7 = r7 * 3
                    int r7 = 1 - r7
                    byte[] r0 = o.by.e.AnonymousClass2.$$a
                    byte[] r1 = new byte[r7]
                    int r7 = r7 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r7
                    r7 = r6
                    goto L36
                L1a:
                    r3 = r2
                    r5 = r7
                    r7 = r6
                    r6 = r8
                    r8 = r5
                L1f:
                    byte r4 = (byte) r6
                    r1[r3] = r4
                    int r4 = r3 + 1
                    if (r3 != r8) goto L2e
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2e:
                    r3 = r0[r7]
                    r5 = r9
                    r9 = r8
                    r8 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r5
                L36:
                    int r6 = r6 + r8
                    int r7 = r7 + 1
                    r8 = r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1f
                */
                throw new UnsupportedOperationException("Method not decompiled: o.by.e.AnonymousClass2.g(int, int, short, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{45, 88, 59, 34};
                $$b = Opcodes.DMUL;
            }

            @Override // o.bm.e.b
            public final void d() {
                String intern6;
                Object obj;
                int i2 = a + 83;
                c = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        o.ee.g.c();
                        Object[] objArr12 = new Object[1];
                        f("\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 24, 0, 0}, true, objArr12);
                        intern6 = ((String) objArr12[0]).intern();
                        Object[] objArr13 = new Object[1];
                        f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{24, 21, 0, 0}, true, objArr13);
                        obj = objArr13[0];
                        break;
                    default:
                        o.ee.g.c();
                        Object[] objArr14 = new Object[1];
                        f("\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 24, 0, 0}, true, objArr14);
                        intern6 = ((String) objArr14[0]).intern();
                        Object[] objArr15 = new Object[1];
                        f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{24, 21, 0, 0}, true, objArr15);
                        obj = objArr15[0];
                        break;
                }
                o.ee.g.d(intern6, ((String) obj).intern());
                e.this.e().c();
                int i3 = a + 89;
                c = i3 % 128;
                int i4 = i3 % 2;
            }

            @Override // o.bm.e.b
            public final void c(o.bb.d dVar3) {
                int i2 = c + 85;
                a = i2 % 128;
                int i3 = i2 % 2;
                o.ee.g.c();
                Object[] objArr12 = new Object[1];
                f("\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001", new int[]{0, 24, 0, 0}, true, objArr12);
                String intern6 = ((String) objArr12[0]).intern();
                Object[] objArr13 = new Object[1];
                f("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001", new int[]{45, 21, 6, 13}, false, objArr13);
                o.ee.g.d(intern6, ((String) objArr13[0]).intern());
                e.this.e().d(dVar3);
                int i4 = c + 83;
                a = i4 % 128;
                int i5 = i4 % 2;
            }

            /* JADX WARN: Code restructure failed: missing block: B:126:0x0352, code lost:
            
                r1 = r0;
             */
            /* JADX WARN: Multi-variable type inference failed */
            /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
            /* JADX WARN: Type inference failed for: r0v1 */
            /* JADX WARN: Type inference failed for: r0v27, types: [byte[]] */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
                /*
                    Method dump skipped, instructions count: 988
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.by.e.AnonymousClass2.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
            }
        }, b());
        o.ee.g.c();
        Object[] objArr12 = new Object[1];
        f(new int[]{782460325, 698065371, 513546392, -179351272, 1631631083, 970911419, 1130540133, -1326838203, 211692293, -1497606493, -463406923, 1786340776}, 24 - (ViewConfiguration.getTouchSlop() >> 8), objArr12);
        String intern6 = ((String) objArr12[0]).intern();
        Object[] objArr13 = new Object[1];
        f(new int[]{-2064236436, 354086102, -435403369, -1697141167, -144178036, 193079692, 829905038, -1385550849, 1375356405, -133264283, 2461220, 1787141099, -1406685974, -906093354, -1594596840, 500446927}, (ViewConfiguration.getEdgeSlop() >> 16) + 29, objArr13);
        o.ee.g.d(intern6, ((String) objArr13[0]).intern());
        eVar.d(dVar, null, dVar2);
        int i2 = b + 51;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 908
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.by.e.f(int[], int, java.lang.Object[]):void");
    }
}
