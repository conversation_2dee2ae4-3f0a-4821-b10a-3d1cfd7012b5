package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w4.smali */
public interface w4 {
    public static final w A;
    public static final w B;
    public static final w C;
    public static final w D;
    public static final w E;
    public static final w F;
    public static final w G;
    public static final w H;
    public static final w I;
    public static final w J;
    public static final w K;
    public static final w L;
    public static final w M;
    public static final w N;
    public static final w O;
    public static final w P;
    public static final w Q;
    public static final w R;
    public static final w S;
    public static final w T;
    public static final w U;
    public static final w V;
    public static final w W;
    public static final w X;
    public static final w Y;
    public static final w Z;
    public static final w a;
    public static final w a0;
    public static final w b;
    public static final w b0;
    public static final w c;
    public static final w c0;
    public static final w d;
    public static final w d0;
    public static final w e;
    public static final w e0;
    public static final w f;
    public static final w f0;
    public static final w g;
    public static final w g0;
    public static final w h;
    public static final w h0;
    public static final w i;
    public static final w i0;
    public static final w j;
    public static final w j0;
    public static final w k;
    public static final w k0;
    public static final w l;
    public static final w l0;
    public static final w m;
    public static final w m0;
    public static final w n;
    public static final w n0;

    /* renamed from: o, reason: collision with root package name */
    public static final w f31o;
    public static final w o0;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.2.156.10197.1");
        a = wVar;
        b = wVar.a("101.1");
        c = wVar.a("101.2");
        d = wVar.a("101.3");
        e = wVar.a("101.4");
        f = wVar.a("102.1");
        g = wVar.a("102.2");
        h = wVar.a("102.3");
        i = wVar.a("102.4");
        j = wVar.a("102.5");
        k = wVar.a("102.6");
        l = wVar.a("103.1");
        m = wVar.a("103.2");
        n = wVar.a("103.3");
        f31o = wVar.a("103.4");
        p = wVar.a("103.5");
        q = wVar.a("103.6");
        r = wVar.a("104.1");
        s = wVar.a("104.2");
        t = wVar.a("104.3");
        u = wVar.a("104.4");
        v = wVar.a("104.5");
        w = wVar.a("104.6");
        x = wVar.a("104.7");
        y = wVar.a("104.8");
        z = wVar.a("104.9");
        A = wVar.a("104.10");
        B = wVar.a("104.11");
        C = wVar.a("104.12");
        D = wVar.a("104.100");
        E = wVar.a("201");
        F = wVar.a("301");
        G = wVar.a("301.1");
        H = wVar.a("301.2");
        w a2 = wVar.a("301.3");
        I = a2;
        J = wVar.a("301.101");
        K = new w("1.2.156.11235.1.1.1");
        L = new w("1.2.156.11235.1.1.2.1");
        M = a2.a("1");
        N = a2.a("2");
        O = a2.a("2.1");
        P = a2.a("2.2");
        Q = a2.a("2.3");
        R = a2.a("2.4");
        S = a2.a("2.5");
        T = a2.a("2.6");
        U = a2.a("2.7");
        V = a2.a("2.8");
        W = a2.a("2.9");
        X = a2.a("2.10");
        Y = a2.a("2.11");
        Z = wVar.a("302");
        a0 = wVar.a("302.1");
        b0 = wVar.a("302.2");
        c0 = wVar.a("302.3");
        w a3 = wVar.a("401");
        d0 = a3;
        e0 = a3.a("2");
        f0 = wVar.a("501");
        g0 = wVar.a("502");
        h0 = wVar.a("503");
        i0 = wVar.a("504");
        j0 = wVar.a("505");
        k0 = wVar.a("506");
        l0 = wVar.a("507");
        m0 = wVar.a("520");
        n0 = wVar.a("521");
        o0 = wVar.a("522");
    }
}
