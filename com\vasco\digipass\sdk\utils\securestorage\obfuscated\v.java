package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\v.smali */
public final class v {
    public static void a(int i) {
        StringBuilder sb = new StringBuilder();
        byte[] bArr = new byte[19];
        int[] iArr = {68, Opcodes.FMUL, Opcodes.LREM, 97, Opcodes.LREM, 102, 98, 102, 35, 85, 73, 59, 39, 99, Opcodes.DNEG, Opcodes.ISHL, Opcodes.ISHR, 94, 35};
        for (int i2 = 0; i2 < 19; i2++) {
            bArr[i2] = (byte) ((iArr[i2] + 5) ^ i2);
        }
        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, new Throwable(sb.append(new String(bArr)).append(i).toString()));
    }
}
