package com.rolster.capacitor.biometric;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import androidx.biometric.BiometricPrompt;
import com.google.firebase.messaging.Constants;
import java.util.concurrent.Executor;
import org.bouncycastle.i18n.MessageBundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes3\com\rolster\capacitor\biometric\NativeBiometricActivity.smali */
public class NativeBiometricActivity extends AppCompatActivity {
    private int counter = 0;
    private Executor executor;
    private int maxAttempts;

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R$layout.activity_auth_acitivy);
        this.maxAttempts = getIntent().getIntExtra("maxAttempts", 1);
        if (Build.VERSION.SDK_INT >= 28) {
            this.executor = getMainExecutor();
        } else {
            this.executor = new Executor() { // from class: com.rolster.capacitor.biometric.NativeBiometricActivity.1
                @Override // java.util.concurrent.Executor
                public void execute(Runnable command) {
                    new Handler().post(command);
                }
            };
        }
        BiometricPrompt.PromptInfo.Builder builder = new BiometricPrompt.PromptInfo.Builder().setTitle(getIntent().hasExtra(MessageBundle.TITLE_ENTRY) ? getIntent().getStringExtra(MessageBundle.TITLE_ENTRY) : "Authenticate").setSubtitle(getIntent().hasExtra("subtitle") ? getIntent().getStringExtra("subtitle") : null).setDescription(getIntent().hasExtra("description") ? getIntent().getStringExtra("description") : null);
        boolean useFallback = getIntent().getBooleanExtra("useFallback", false);
        if (useFallback) {
            builder.setDeviceCredentialAllowed(true);
        } else {
            builder.setNegativeButtonText(getIntent().hasExtra("negativeButtonText") ? getIntent().getStringExtra("negativeButtonText") : "Cancel");
        }
        BiometricPrompt.PromptInfo promptInfo = builder.build();
        BiometricPrompt biometricPrompt = new BiometricPrompt(this, this.executor, new BiometricPrompt.AuthenticationCallback() { // from class: com.rolster.capacitor.biometric.NativeBiometricActivity.2
            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public void onAuthenticationSucceeded(BiometricPrompt.AuthenticationResult result) {
                super.onAuthenticationSucceeded(result);
                NativeBiometricActivity.this.finishActivity("success");
            }

            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public void onAuthenticationError(int errorCode, CharSequence errString) {
                super.onAuthenticationError(errorCode, errString);
                int pluginErrorCode = NativeBiometricActivity.convertToPluginErrorCode(errorCode);
                NativeBiometricActivity.this.finishActivity(Constants.IPC_BUNDLE_KEY_SEND_ERROR, Integer.valueOf(pluginErrorCode), errString.toString());
            }

            @Override // androidx.biometric.BiometricPrompt.AuthenticationCallback
            public void onAuthenticationFailed() {
                super.onAuthenticationFailed();
                NativeBiometricActivity.this.counter++;
                if (NativeBiometricActivity.this.counter == NativeBiometricActivity.this.maxAttempts) {
                    NativeBiometricActivity.this.finishActivity("failed", 10, "Authentication failed.");
                    NativeBiometricActivity.this.counter = 0;
                }
            }
        });
        biometricPrompt.authenticate(promptInfo);
    }

    void finishActivity(String result) {
        finishActivity(result, null, null);
    }

    void finishActivity(String result, Integer errorCode, String errorDetails) {
        Intent intent = new Intent();
        intent.putExtra("result", result);
        if (errorCode != null) {
            intent.putExtra("errorCode", String.valueOf(errorCode));
        }
        if (errorDetails != null) {
            intent.putExtra("errorDetails", errorDetails);
        }
        setResult(-1, intent);
        finish();
    }

    public static int convertToPluginErrorCode(int errorCode) {
        switch (errorCode) {
            case 1:
            case 12:
                return 1;
            case 2:
            case 4:
            case 6:
            case 8:
            default:
                return 0;
            case 3:
            case 5:
                return 15;
            case 7:
                return 4;
            case 9:
                return 2;
            case 10:
            case 13:
                return 16;
            case 11:
                return 3;
            case 14:
                return 14;
        }
    }
}
