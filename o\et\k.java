package o.et;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\k.smali */
public abstract class k extends c {
    private static int a = 0;
    private static int d = 1;

    public k(String str, o.dp.b bVar, String str2, int i, String str3) {
        super(str, bVar, str2, i, str3);
    }

    @Override // o.el.d
    public final o.ey.e<?> a(String str) {
        o.fb.c cVar = new o.fb.c(n(), str, false);
        int i = (d + 82) - 1;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 16 : (char) 20) {
            case 20:
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.et.c
    public EmvApplicationType e() {
        int i = d;
        int i2 = (i ^ 45) + ((i & 45) << 1);
        a = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case true:
                EmvApplicationType emvApplicationType = EmvApplicationType.HceIdemiaWise;
                int i3 = d;
                int i4 = ((i3 | 85) << 1) - (i3 ^ 85);
                a = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return emvApplicationType;
                }
            default:
                EmvApplicationType emvApplicationType2 = EmvApplicationType.HceIdemiaWise;
                throw null;
        }
    }

    @Override // o.et.c
    public final byte[] E() {
        int i;
        int i2 = d + 67;
        a = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 16 : '6') {
            case Opcodes.ISTORE /* 54 */:
                i = 0;
                break;
            default:
                i = 1;
                break;
        }
        return new byte[i];
    }
}
