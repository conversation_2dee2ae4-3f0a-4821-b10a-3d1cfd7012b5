package kotlin.jvm.functions;

import kotlin.Function;
import kotlin.Metadata;

/* compiled from: Functions.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\u0017\bf\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u0000*\u0006\b\u0001\u0010\u0002 \u0000*\u0006\b\u0002\u0010\u0003 \u0000*\u0006\b\u0003\u0010\u0004 \u0000*\u0006\b\u0004\u0010\u0005 \u0000*\u0006\b\u0005\u0010\u0006 \u0000*\u0006\b\u0006\u0010\u0007 \u0000*\u0006\b\u0007\u0010\b \u0000*\u0006\b\b\u0010\t \u0000*\u0006\b\t\u0010\n \u0000*\u0006\b\n\u0010\u000b \u0000*\u0006\b\u000b\u0010\f \u0000*\u0006\b\f\u0010\r \u0000*\u0006\b\r\u0010\u000e \u0000*\u0006\b\u000e\u0010\u000f \u0000*\u0006\b\u000f\u0010\u0010 \u0000*\u0006\b\u0010\u0010\u0011 \u0000*\u0006\b\u0011\u0010\u0012 \u0000*\u0006\b\u0012\u0010\u0013 \u0000*\u0006\b\u0013\u0010\u0014 \u0000*\u0006\b\u0014\u0010\u0015 \u00012\b\u0012\u0004\u0012\u0002H\u00150\u0016J®\u0001\u0010\u0017\u001a\u00028\u00142\u0006\u0010\u0018\u001a\u00028\u00002\u0006\u0010\u0019\u001a\u00028\u00012\u0006\u0010\u001a\u001a\u00028\u00022\u0006\u0010\u001b\u001a\u00028\u00032\u0006\u0010\u001c\u001a\u00028\u00042\u0006\u0010\u001d\u001a\u00028\u00052\u0006\u0010\u001e\u001a\u00028\u00062\u0006\u0010\u001f\u001a\u00028\u00072\u0006\u0010 \u001a\u00028\b2\u0006\u0010!\u001a\u00028\t2\u0006\u0010\"\u001a\u00028\n2\u0006\u0010#\u001a\u00028\u000b2\u0006\u0010$\u001a\u00028\f2\u0006\u0010%\u001a\u00028\r2\u0006\u0010&\u001a\u00028\u000e2\u0006\u0010'\u001a\u00028\u000f2\u0006\u0010(\u001a\u00028\u00102\u0006\u0010)\u001a\u00028\u00112\u0006\u0010*\u001a\u00028\u00122\u0006\u0010+\u001a\u00028\u0013H¦\u0002¢\u0006\u0002\u0010,¨\u0006-"}, d2 = {"Lkotlin/jvm/functions/Function20;", "P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "P9", "P10", "P11", "P12", "P13", "P14", "P15", "P16", "P17", "P18", "P19", "P20", "R", "Lkotlin/Function;", "invoke", "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "p9", "p10", "p11", "p12", "p13", "p14", "p15", "p16", "p17", "p18", "p19", "p20", "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\functions\Function20.smali */
public interface Function20<P1, P2, P3, P4, P5, P6, P7, P8, P9, P10, P11, P12, P13, P14, P15, P16, P17, P18, P19, P20, R> extends Function<R> {
    R invoke(P1 p1, P2 p2, P3 p3, P4 p4, P5 p5, P6 p6, P7 p7, P8 p8, P9 p9, P10 p10, P11 p11, P12 p12, P13 p13, P14 p14, P15 p15, P16 p16, P17 p17, P18 p18, P19 p19, P20 p20);
}
