package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.Kryo;
import java.io.IOException;
import java.io.ObjectOutput;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\KryoObjectOutput.smali */
public class KryoObjectOutput extends KryoDataOutput implements ObjectOutput {
    private final Kryo kryo;

    public KryoObjectOutput(Kryo kryo, Output output) {
        super(output);
        this.kryo = kryo;
    }

    @Override // java.io.ObjectOutput
    public void writeObject(Object object) throws IOException {
        this.kryo.writeClassAndObject(this.output, object);
    }

    @Override // java.io.ObjectOutput
    public void flush() throws IOException {
        this.output.flush();
    }

    @Override // com.esotericsoftware.kryo.io.KryoDataOutput, java.lang.AutoCloseable
    public void close() throws IOException {
        this.output.close();
    }
}
