package androidx.biometric;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import androidx.biometric.BiometricPrompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\DeviceCredentialHandlerActivity.smali */
public class DeviceCredentialHandlerActivity extends AppCompatActivity {
    static final String EXTRA_PROMPT_INFO_BUNDLE = "prompt_info_bundle";
    private static final String KEY_DID_CHANGE_CONFIGURATION = "did_change_configuration";
    private static final String TAG = "DeviceCredentialHandler";
    private boolean mDidChangeConfiguration;

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle savedInstanceState) {
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstance();
        if (bridge.getClientThemeResId() != 0) {
            setTheme(bridge.getClientThemeResId());
            getTheme().applyStyle(R.style.TransparentStyle, true);
        }
        super.onCreate(savedInstanceState);
        boolean z = savedInstanceState != null && savedInstanceState.getBoolean(KEY_DID_CHANGE_CONFIGURATION, false);
        this.mDidChangeConfiguration = z;
        if (!z) {
            bridge.stopIgnoringReset();
        } else {
            this.mDidChangeConfiguration = false;
        }
        setTitle((CharSequence) null);
        setContentView(R.layout.device_credential_handler_activity);
        if (bridge.getExecutor() == null || bridge.getAuthenticationCallback() == null) {
            Log.e(TAG, "onCreate: Executor and/or callback was null!");
            finish();
        } else {
            BiometricPrompt biometricPrompt = new BiometricPrompt(this, bridge.getExecutor(), bridge.getAuthenticationCallback());
            Bundle infoBundle = getIntent().getBundleExtra(EXTRA_PROMPT_INFO_BUNDLE);
            BiometricPrompt.PromptInfo info = new BiometricPrompt.PromptInfo(infoBundle);
            biometricPrompt.authenticate(info);
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull();
        if (isChangingConfigurations() && bridge != null) {
            bridge.ignoreNextReset();
            this.mDidChangeConfiguration = true;
        }
    }

    @Override // androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putBoolean(KEY_DID_CHANGE_CONFIGURATION, this.mDidChangeConfiguration);
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        handleDeviceCredentialResult(resultCode);
    }

    void handleDeviceCredentialResult(int resultCode) {
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull();
        if (bridge == null) {
            Log.e(TAG, "onActivityResult: Bridge or callback was null!");
        } else if (resultCode == -1) {
            bridge.setDeviceCredentialResult(1);
            bridge.setConfirmingDeviceCredential(false);
            bridge.startIgnoringReset();
        } else {
            bridge.setDeviceCredentialResult(2);
            bridge.setConfirmingDeviceCredential(false);
            bridge.startIgnoringReset();
        }
        finish();
    }
}
