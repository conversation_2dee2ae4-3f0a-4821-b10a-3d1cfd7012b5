package fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels;

import android.app.Application;
import android.content.SharedPreferences;
import android.graphics.PointF;
import android.view.Gravity;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel;
import java.util.Date;
import o.ee.g;
import o.eg.b;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\googlepay\viewmodels\ManageGooglePayMockViewModel.smali */
public final class ManageGooglePayMockViewModel extends ManageDeviceWalletViewModel {
    private static final String MOCK_ISSUER_TOKEN_ID = "DAPLMC0000210662d04df42dda1b41e3b341a9d98124f660";
    private static final String TAG;
    private static char a;
    private static char c;
    private static char d;
    private static char e;
    private static int i;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int b = 0;

    static void e() {
        a = (char) 54155;
        e = (char) 57399;
        c = (char) 11988;
        d = (char) 8342;
    }

    static {
        i = 1;
        e();
        TAG = ManageGooglePayMockViewModel.class.getSimpleName();
        int i2 = b + 13;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    public ManageGooglePayMockViewModel(Application application) {
        super(application);
    }

    @Override // fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel
    public final String getSharedPrefDeviceWalletKey() {
        int i2 = i + Opcodes.LMUL;
        b = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        h("쪜鿋ظ⁋", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 2, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = b + 57;
        i = i4 % 128;
        int i5 = i4 % 2;
        return intern;
    }

    @Override // fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel
    public final String getTag() {
        int i2 = b + 97;
        int i3 = i2 % 128;
        i = i3;
        int i4 = i2 % 2;
        String str = TAG;
        int i5 = i3 + 31;
        b = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String addToken(String str) {
        try {
            Long valueOf = Long.valueOf(new Date().getTime());
            b bVar = new b();
            Object[] objArr = new Object[1];
            h("ᵅ涉梭䜐⼕\ue33d\ue3d8뀖\uef2eꙓ\ue484ɵォ녽ಞ鬺", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 15, objArr);
            bVar.d(((String) objArr[0]).intern(), MOCK_ISSUER_TOKEN_ID);
            Object[] objArr2 = new Object[1];
            h("托ﾉ㤳⫮밲﹇\ue4e2錗ﴑ⽄蘛讬", (ViewConfiguration.getPressedStateDuration() >> 16) + 12, objArr2);
            bVar.d(((String) objArr2[0]).intern(), valueOf);
            Object[] objArr3 = new Object[1];
            h("풃䕐\ueebc曰ﹲ୶蹐\uea26պ븙ಞ鬺", 11 - Gravity.getAbsoluteGravity(0, 0), objArr3);
            bVar.d(((String) objArr3[0]).intern(), str);
            this.persistedTokensSet.add(bVar.b());
            SharedPreferences.Editor edit = this.sharedPreferences.edit();
            Object[] objArr4 = new Object[1];
            h("쪜鿋ظ⁋", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 3, objArr4);
            edit.putStringSet(((String) objArr4[0]).intern(), this.persistedTokensSet).apply();
        } catch (d e2) {
            g.c();
            g.d(TAG, new StringBuilder("addToken - Unable to deserialize token: ").append(e2.getMessage()).toString());
        }
        int i2 = b + 93;
        i = i2 % 128;
        int i3 = i2 % 2;
        return MOCK_ISSUER_TOKEN_ID;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 568
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels.ManageGooglePayMockViewModel.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
