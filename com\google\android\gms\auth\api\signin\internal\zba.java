package com.google.android.gms.auth.api.signin.internal;

import android.os.RemoteException;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.common.api.Status;

/* compiled from: com.google.android.gms:play-services-auth@@20.6.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\auth\api\signin\internal\zba.smali */
public class zba extends zbq {
    @Override // com.google.android.gms.auth.api.signin.internal.zbr
    public void zbb(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.auth.api.signin.internal.zbr
    public void zbc(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.auth.api.signin.internal.zbr
    public void zbd(GoogleSignInAccount googleSignInAccount, Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }
}
