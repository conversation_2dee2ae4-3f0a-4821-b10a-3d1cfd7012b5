package bc.org.bouncycastle.math.ec.custom.djb;

import bc.org.bouncycastle.math.ec.AbstractECLookupTable;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECLookupTable;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\djb\Curve25519.smali */
public class Curve25519 extends ECCurve.AbstractFp {
    private static final BigInteger j;
    private static final BigInteger k;
    private static final ECFieldElement[] l;
    public static final BigInteger q = Curve25519FieldElement.Q;
    protected Curve25519Point i;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\djb\Curve25519$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ int[] b;

        a(int i, int[] iArr) {
            this.a = i;
            this.b = iArr;
        }

        private ECPoint a(int[] iArr, int[] iArr2) {
            return Curve25519.this.a(new Curve25519FieldElement(iArr), new Curve25519FieldElement(iArr2), Curve25519.l);
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            int[] a = w5.a();
            int[] a2 = w5.a();
            int i2 = 0;
            for (int i3 = 0; i3 < this.a; i3++) {
                int i4 = ((i3 ^ i) - 1) >> 31;
                for (int i5 = 0; i5 < 8; i5++) {
                    int i6 = a[i5];
                    int[] iArr = this.b;
                    a[i5] = i6 ^ (iArr[i2 + i5] & i4);
                    a2[i5] = a2[i5] ^ (iArr[(i2 + 8) + i5] & i4);
                }
                i2 += 16;
            }
            return a(a, a2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            int[] a = w5.a();
            int[] a2 = w5.a();
            int i2 = i * 8 * 2;
            for (int i3 = 0; i3 < 8; i3++) {
                int[] iArr = this.b;
                a[i3] = iArr[i2 + i3];
                a2[i3] = iArr[i2 + 8 + i3];
            }
            return a(a, a2);
        }
    }

    static {
        BigInteger bigInteger = new BigInteger(1, z4.a("2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA984914A144"));
        j = bigInteger;
        k = new BigInteger(1, z4.a("7B425ED097B425ED097B425ED097B425ED097B425ED097B4260B5E9C7710C864"));
        l = new ECFieldElement[]{new Curve25519FieldElement(ECConstants.ONE), new Curve25519FieldElement(bigInteger)};
    }

    public Curve25519() {
        super(q);
        this.i = new Curve25519Point(this, null, null);
        this.b = fromBigInteger(j);
        this.c = fromBigInteger(k);
        this.d = new BigInteger(1, z4.a("1000000000000000000000000000000014DEF9DEA2F79CD65812631A5CF5D3ED"));
        this.e = BigInteger.valueOf(8L);
        this.f = 4;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECCurve a() {
        return new Curve25519();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        int[] iArr = new int[i2 * 8 * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            w5.b(((Curve25519FieldElement) eCPoint.getRawXCoord()).a, 0, iArr, i3);
            int i5 = i3 + 8;
            w5.b(((Curve25519FieldElement) eCPoint.getRawYCoord()).a, 0, iArr, i5);
            i3 = i5 + 8;
        }
        return new a(i2, iArr);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement fromBigInteger(BigInteger bigInteger) {
        return new Curve25519FieldElement(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public int getFieldSize() {
        return q.bitLength();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECPoint getInfinity() {
        return this.i;
    }

    public BigInteger getQ() {
        return q;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
        int[] a2 = w5.a();
        Curve25519Field.random(secureRandom, a2);
        return new Curve25519FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
        int[] a2 = w5.a();
        Curve25519Field.randomMult(secureRandom, a2);
        return new Curve25519FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public boolean supportsCoordinateSystem(int i) {
        return i == 4;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return new Curve25519Point(this, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        return new Curve25519Point(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
