package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.math.ec.ECFieldElement;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\g8.smali */
public class g8 extends u {
    private static i8 x = new i8();
    protected ECFieldElement b;

    public g8(ECFieldElement eCFieldElement) {
        this.b = eCFieldElement;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        return new f2(x.a(this.b.toBigInteger(), x.a(this.b)));
    }
}
