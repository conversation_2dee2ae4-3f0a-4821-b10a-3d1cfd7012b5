package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP224R1FieldElement.smali */
public class SecP224R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001"));
    protected int[] a;

    public SecP224R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP224R1FieldElement");
        }
        this.a = SecP224R1Field.fromBigInteger(bigInteger);
    }

    private static boolean a(int[] iArr) {
        int[] a = v5.a();
        int[] a2 = v5.a();
        v5.a(iArr, a);
        for (int i = 0; i < 7; i++) {
            v5.a(a, a2);
            SecP224R1Field.squareN(a, 1 << i, a);
            SecP224R1Field.multiply(a, a2, a);
        }
        SecP224R1Field.squareN(a, 95, a);
        return v5.a(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224R1Field.add(this.a, ((SecP224R1FieldElement) eCFieldElement).a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = v5.a();
        SecP224R1Field.addOne(this.a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224R1Field.inv(((SecP224R1FieldElement) eCFieldElement).a, a);
        SecP224R1Field.multiply(a, this.a, a);
        return new SecP224R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP224R1FieldElement) {
            return v5.b(this.a, ((SecP224R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP224R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 7);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = v5.a();
        SecP224R1Field.inv(this.a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return v5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return v5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224R1Field.multiply(this.a, ((SecP224R1FieldElement) eCFieldElement).a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = v5.a();
        SecP224R1Field.negate(this.a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (v5.b(iArr) || v5.a(iArr)) {
            return this;
        }
        int[] a = v5.a();
        SecP224R1Field.negate(iArr, a);
        int[] a2 = n5.a(SecP224R1Field.a);
        int[] a3 = v5.a();
        if (!a(iArr)) {
            return null;
        }
        while (!a(a, a2, a3)) {
            SecP224R1Field.addOne(a2, a2);
        }
        SecP224R1Field.square(a3, a2);
        if (v5.b(iArr, a2)) {
            return new SecP224R1FieldElement(a3);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = v5.a();
        SecP224R1Field.square(this.a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224R1Field.subtract(this.a, ((SecP224R1FieldElement) eCFieldElement).a, a);
        return new SecP224R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return v5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return v5.c(this.a);
    }

    public SecP224R1FieldElement() {
        this.a = v5.a();
    }

    protected SecP224R1FieldElement(int[] iArr) {
        this.a = iArr;
    }

    private static void a(int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4, int[] iArr5, int[] iArr6, int[] iArr7) {
        SecP224R1Field.multiply(iArr5, iArr3, iArr7);
        SecP224R1Field.multiply(iArr7, iArr, iArr7);
        SecP224R1Field.multiply(iArr4, iArr2, iArr6);
        SecP224R1Field.add(iArr6, iArr7, iArr6);
        SecP224R1Field.multiply(iArr4, iArr3, iArr7);
        v5.a(iArr6, iArr4);
        SecP224R1Field.multiply(iArr5, iArr2, iArr5);
        SecP224R1Field.add(iArr5, iArr7, iArr5);
        SecP224R1Field.square(iArr5, iArr6);
        SecP224R1Field.multiply(iArr6, iArr, iArr6);
    }

    private static void a(int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4, int[] iArr5) {
        v5.a(iArr, iArr4);
        int[] a = v5.a();
        int[] a2 = v5.a();
        for (int i = 0; i < 7; i++) {
            v5.a(iArr2, a);
            v5.a(iArr3, a2);
            int i2 = 1 << i;
            while (true) {
                i2--;
                if (i2 >= 0) {
                    a(iArr2, iArr3, iArr4, iArr5);
                }
            }
            a(iArr, a, a2, iArr2, iArr3, iArr4, iArr5);
        }
    }

    private static void a(int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4) {
        SecP224R1Field.multiply(iArr2, iArr, iArr2);
        SecP224R1Field.twice(iArr2, iArr2);
        SecP224R1Field.square(iArr, iArr4);
        SecP224R1Field.add(iArr3, iArr4, iArr);
        SecP224R1Field.multiply(iArr3, iArr4, iArr3);
        SecP224R1Field.reduce32(c6.c(7, iArr3, 2, 0), iArr3);
    }

    private static boolean a(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] a = v5.a();
        v5.a(iArr2, a);
        int[] a2 = v5.a();
        a2[0] = 1;
        int[] a3 = v5.a();
        a(iArr, a, a2, a3, iArr3);
        int[] a4 = v5.a();
        int[] a5 = v5.a();
        for (int i = 1; i < 96; i++) {
            v5.a(a, a4);
            v5.a(a2, a5);
            a(a, a2, a3, iArr3);
            if (v5.b(a)) {
                SecP224R1Field.inv(a5, iArr3);
                SecP224R1Field.multiply(iArr3, a4, iArr3);
                return true;
            }
        }
        return false;
    }
}
