package o.dh;

import com.esotericsoftware.asm.Opcodes;
import java.util.Arrays;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.dk.b;
import o.ej.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dh\c.smali */
public final class c {
    private final byte[] b;
    private byte[] c;
    private static int e = 0;
    private static int a = 1;

    public c(String str, byte[] bArr) {
        this.b = b.b(str);
        this.c = bArr;
    }

    public final byte[] c() {
        int i = (e + 46) - 1;
        a = i % 128;
        int i2 = i % 2;
        byte[] e2 = e();
        switch (e2 == null ? (char) 25 : (char) 24) {
            case 24:
                byte[] copyOf = Arrays.copyOf(e2, e2.length);
                int i3 = (e + Opcodes.FDIV) - 1;
                a = i3 % 128;
                int i4 = i3 % 2;
                return copyOf;
            default:
                int i5 = a + 65;
                e = i5 % 128;
                Object obj = null;
                switch (i5 % 2 != 0 ? '\t' : 'b') {
                    case Opcodes.FADD /* 98 */:
                        return null;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    private byte[] e() {
        int i = a;
        int i2 = ((i | 67) << 1) - (i ^ 67);
        e = i2 % 128;
        switch (i2 % 2 != 0 ? '@' : (char) 18) {
            case 18:
                return this.c;
            default:
                int i3 = 61 / 0;
                return this.c;
        }
    }

    private byte[] a() {
        int i = a;
        int i2 = (i + 60) - 1;
        e = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 18 : ',') {
            case ',':
                byte[] bArr = this.b;
                int i3 = (i & 109) + (i | 109);
                e = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return bArr;
                    default:
                        throw null;
                }
            default:
                throw null;
        }
    }

    public final byte[] b() {
        int i = (a + Opcodes.FNEG) - 1;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                return d.d(a(), c());
            default:
                return d.d(a(), c());
        }
    }

    public final int hashCode() {
        int i = a;
        int i2 = ((i | 3) << 1) - (i ^ 3);
        e = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = e + Opcodes.DNEG;
        a = i4 % 128;
        int i5 = i4 % 2;
        return hashCode;
    }

    public final boolean equals(Object obj) {
        int i = a;
        int i2 = (i & 65) + (i | 65);
        e = i2 % 128;
        char c = i2 % 2 != 0 ? '\r' : 'R';
        boolean equals = super.equals(obj);
        switch (c) {
            case '\r':
                int i3 = 82 / 0;
                break;
        }
        int i4 = a;
        int i5 = ((i4 | Opcodes.DNEG) << 1) - (i4 ^ Opcodes.DNEG);
        e = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i = e;
        int i2 = ((i | 87) << 1) - (i ^ 87);
        a = i2 % 128;
        switch (i2 % 2 == 0 ? '=' : 'G') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                super.toString();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return super.toString();
        }
    }

    protected final void finalize() throws Throwable {
        int i = a;
        int i2 = (i ^ 71) + ((i & 71) << 1);
        e = i2 % 128;
        char c = i2 % 2 != 0 ? 'b' : (char) 20;
        super.finalize();
        switch (c) {
            case Opcodes.FADD /* 98 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i3 = e;
                int i4 = (i3 ^ 1) + ((i3 & 1) << 1);
                a = i4 % 128;
                int i5 = i4 % 2;
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
