package o.am;

import android.content.Context;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\am\c.smali */
public final class c extends b<InterfaceC0021c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static int[] d;
    String a;
    o.aj.d e;

    /* renamed from: o.am.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\am\c$c.smali */
    public interface InterfaceC0021c {
        void a(o.bb.d dVar);

        void e(o.aj.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        c = 1;
        o();
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        TextUtils.getTrimmedLength("");
        int i = b + 99;
        c = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{8, 72, -108, -33};
        $$e = 104;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 115
            byte[] r0 = o.am.c.$$d
            int r8 = r8 * 4
            int r8 = r8 + 4
            int r9 = r9 * 2
            int r9 = 1 - r9
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L32
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L32:
            int r7 = r7 + r8
            int r8 = r9 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.am.c.l(byte, byte, byte, java.lang.Object[]):void");
    }

    static void o() {
        d = new int[]{903783388, -1327217918, -2146627825, -1432374035, -2078142057, -427026799, -675437129, 286866390, -318129057, 1702630105, -1593281427, -1314607867, 404648919, 2099406962, -399618745, -1005898965, 440424783, -703270721};
    }

    public c(Context context, InterfaceC0021c interfaceC0021c, o.ei.c cVar) {
        super(context, interfaceC0021c, cVar, e.A);
        this.e = null;
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = c + Opcodes.LNEG;
        b = i % 128;
        switch (i % 2 != 0 ? '^' : (char) 19) {
            case 19:
                Object[] objArr = new Object[1];
                k(new int[]{268605161, -443397852, -770085259, -863586934, 465597209, -420549353, 1814917109, -1034376644, -271644278, 32288833, 1440033304, -1210198545, -1044946683, -1739364025}, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 27, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k(new int[]{268605161, -443397852, -770085259, -863586934, 465597209, -420549353, 1814917109, -1034376644, -271644278, 32288833, 1440033304, -1210198545, -1044946683, -1739364025}, 43 % (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = c + 17;
        b = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    @Override // o.y.b
    public final a<?> b() {
        d dVar = new d(this);
        int i = c + 33;
        b = i % 128;
        switch (i % 2 != 0 ? (char) 0 : 'Z') {
            case 0:
                int i2 = 67 / 0;
                return dVar;
            default:
                return dVar;
        }
    }

    public final void b(String str) {
        int i = c + 95;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                this.a = str;
                c();
                int i2 = 66 / 0;
                break;
            default:
                this.a = str;
                c();
                break;
        }
        int i3 = c + 21;
        b = i3 % 128;
        switch (i3 % 2 != 0 ? '9' : 'a') {
            case Opcodes.LADD /* 97 */:
                return;
            default:
                throw null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\am\c$d.smali */
    static final class d extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int[] b;
        private static int c;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            c = 0;
            e = 1;
            b = new int[]{-555841801, -1768464814, 1788725238, 985708576, -981310258, 806143927, 855526753, 922124430, 837138014, -1033891658, 488980350, 1813781854, 1187527306, -1767173142, -1917964290, 896412914, -828769160, -654036549};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
            /*
                int r8 = r8 * 2
                int r8 = 3 - r8
                int r7 = r7 + 115
                byte[] r0 = o.am.c.d.$$d
                int r6 = r6 * 2
                int r6 = 1 - r6
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L33
            L19:
                r3 = r2
            L1a:
                int r8 = r8 + 1
                byte r4 = (byte) r7
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L2b
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2b:
                r3 = r0[r8]
                r5 = r9
                r9 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L33:
                int r8 = -r8
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.am.c.d.B(byte, byte, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{81, -74, 18, 60};
            $$e = 33;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = e + 17;
            c = i % 128;
            int i2 = i % 2;
        }

        d(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = e + 3;
            c = i % 128;
            switch (i % 2 != 0) {
                case true:
                    Object[] objArr = new Object[1];
                    w(new int[]{699781816, -1722972466, 605804408, 2144446703, 880789799, -2049099312, -1923398184, 943061286}, KeyEvent.keyCodeFromString("") + 89, objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{699781816, -1722972466, 605804408, 2144446703, 880789799, -2049099312, -1923398184, 943061286}, KeyEvent.keyCodeFromString("") + 14, objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(new int[]{281154026, 987105410, 581530145, -767858976, -1648538969, 1829455144, -1700042883, 929395479, 1929541077, -2122254442}, View.MeasureSpec.makeMeasureSpec(0, 0) + 19, objArr);
            o.cf.d dVar = new o.cf.d(context, 44, ((String) objArr[0]).intern());
            int i = c + 85;
            e = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(new int[]{516814935, 1116692993, 1160022581, -417687981}, 6 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
            bVar.d(((String) objArr[0]).intern(), ((c) e()).a);
            int i = c + Opcodes.DREM;
            e = i % 128;
            switch (i % 2 != 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return bVar;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = c + 53;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return null;
                default:
                    int i2 = 31 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = e + 87;
            int i2 = i % 128;
            c = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.LSUB;
            e = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    return null;
                default:
                    int i5 = 87 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = e + Opcodes.LREM;
            c = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i);
                    int i4 = e + Opcodes.LSUB;
                    c = i4 % 128;
                    switch (i4 % 2 != 0 ? (char) 20 : '[') {
                        case Opcodes.DUP_X2 /* 91 */:
                            return c2;
                        default:
                            int i5 = 54 / 0;
                            return c2;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            Object[] objArr = new Object[1];
            w(new int[]{-1320384537, 467324154}, (ViewConfiguration.getTapTimeout() >> 16) + 2, objArr);
            switch (bVar.q(((String) objArr[0]).intern()) != null) {
                case true:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{-1320384537, 467324154}, 2 - TextUtils.getCapsMode("", 0, 0), objArr2);
                    String r = bVar.r(((String) objArr2[0]).intern());
                    Object[] objArr3 = new Object[1];
                    w(new int[]{1647544720, -1437049108, -815399135, 1758512682, -1274652959, 462132608}, 12 - View.MeasureSpec.getMode(0), objArr3);
                    Date e2 = bVar.e(((String) objArr3[0]).intern(), false);
                    Object[] objArr4 = new Object[1];
                    w(new int[]{764117007, 1644357912, 2093868711, 1246373557}, 5 - KeyEvent.keyCodeFromString(""), objArr4);
                    o.eg.b u = bVar.u(((String) objArr4[0]).intern());
                    switch (u == null ? '(' : '1') {
                        case '1':
                            ((c) e()).e = new o.aj.d(r, e2, o.aj.a.b(u));
                            int i = c + Opcodes.DDIV;
                            e = i % 128;
                            if (i % 2 != 0) {
                                return;
                            }
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            int i2 = c + 53;
                            e = i2 % 128;
                            if (i2 % 2 == 0) {
                            }
                            g.c();
                            String c2 = c();
                            Object[] objArr5 = new Object[1];
                            w(new int[]{-822161504, 1126696122, -557958808, -707178967, 1801790094, 1648765426, 1495451948, -841049624, -1237957769, -1948426553, -997087077, -13625538, -1767622861, 1300625560, 1444065656, -551926694, 1615282611, 357573936, -1849273729, -1476688779}, 37 - TextUtils.lastIndexOf("", '0', 0, 0), objArr5);
                            g.e(c2, ((String) objArr5[0]).intern());
                            return;
                    }
                default:
                    int i3 = c + 17;
                    e = i3 % 128;
                    if (i3 % 2 == 0) {
                    }
                    g.c();
                    String c3 = c();
                    Object[] objArr6 = new Object[1];
                    w(new int[]{-822161504, 1126696122, -557958808, -707178967, 1801790094, 1648765426, 1495451948, -841049624, -1237957769, -1948426553, -997087077, -13625538, -152335340, -78987382, -1705279366, 559201099, -1211180893, -398563310}, KeyEvent.getDeadChar(0, 0) + 36, objArr6);
                    g.e(c3, ((String) objArr6[0]).intern());
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + 55;
            e = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass2.c[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((c) e()).a);
                    return;
                case 2:
                    f().e(g(), ((c) e()).a);
                    return;
                default:
                    super.t();
                    int i3 = c + Opcodes.LNEG;
                    e = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case false:
                            throw null;
                        default:
                            return;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = e + 27;
            c = i % 128;
            switch (i % 2 != 0 ? '*' : '8') {
                case '*':
                    ((c) e()).j().e(((c) e()).e);
                    int i2 = 26 / 0;
                    return;
                default:
                    ((c) e()).j().e(((c) e()).e);
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = e + 61;
            c = i % 128;
            switch (i % 2 != 0 ? '_' : (char) 4) {
                case Opcodes.SWAP /* 95 */:
                    ((c) e()).j().a(dVar);
                    int i2 = 26 / 0;
                    break;
                default:
                    ((c) e()).j().a(dVar);
                    break;
            }
            int i3 = e + Opcodes.LREM;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int[] r28, int r29, java.lang.Object[] r30) {
            /*
                Method dump skipped, instructions count: 1012
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.am.c.d.w(int[], int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.am.c$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\am\c$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] c;

        static {
            a = 0;
            b = 1;
            int[] iArr = new int[o.bb.a.values().length];
            c = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = a;
                int i2 = (i & 63) + (i | 63);
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                c[o.bb.a.az.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 109) + ((i3 & 109) << 1);
                a = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1054
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.am.c.k(int[], int, java.lang.Object[]):void");
    }
}
