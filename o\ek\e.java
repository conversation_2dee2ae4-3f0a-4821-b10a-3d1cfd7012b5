package o.ek;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\e.smali */
public final class e {
    private static int h = 0;
    private static int j = 1;
    private String a;
    private o.el.b b;
    private final String c;
    private final String d;
    private final boolean e;
    private boolean i;

    e(String str, String str2, o.el.b bVar, boolean z, String str3, boolean z2) {
        this.c = str;
        this.d = str2;
        this.e = z;
        this.b = bVar;
        this.a = str3;
        this.i = z2;
    }

    public final String e() {
        int i = j;
        int i2 = (i ^ 93) + ((i & 93) << 1);
        h = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.less : 'J') {
            case '<':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c;
        }
    }

    public final String a() {
        int i = j + 19;
        h = i % 128;
        switch (i % 2 != 0 ? (char) 31 : (char) 30) {
            case 30:
                return this.d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final boolean b() {
        int i = h + 33;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        boolean z = this.e;
        int i4 = ((i2 | 43) << 1) - (i2 ^ 43);
        h = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 20 : 'b') {
            case 20:
                int i5 = 3 / 0;
                return z;
            default:
                return z;
        }
    }

    public final o.el.b c() {
        int i = h;
        int i2 = (i ^ 97) + ((i & 97) << 1);
        j = i2 % 128;
        switch (i2 % 2 == 0 ? 'E' : ';') {
            case ';':
                return this.b;
            default:
                int i3 = 87 / 0;
                return this.b;
        }
    }

    public final void d(o.el.b bVar) {
        int i = (j + 4) - 1;
        h = i % 128;
        boolean z = i % 2 != 0;
        this.b = bVar;
        switch (z) {
            case true:
                int i2 = 82 / 0;
                return;
            default:
                return;
        }
    }

    public final String d() {
        int i = h;
        int i2 = (i ^ 15) + ((i & 15) << 1);
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                throw null;
            default:
                String str = this.a;
                int i3 = ((i | 77) << 1) - (i ^ 77);
                j = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final void b(String str) {
        int i = j;
        int i2 = ((i | 65) << 1) - (i ^ 65);
        h = i2 % 128;
        int i3 = i2 % 2;
        this.a = str;
        int i4 = (i + 58) - 1;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    public final boolean h() {
        int i = j;
        int i2 = i + 25;
        h = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.i;
        int i4 = i + 59;
        h = i4 % 128;
        switch (i4 % 2 != 0 ? 'W' : '_') {
            case Opcodes.POP /* 87 */:
                throw null;
            default:
                return z;
        }
    }

    public final void g() {
        int i = h;
        int i2 = (i ^ 57) + ((i & 57) << 1);
        j = i2 % 128;
        int i3 = i2 % 2;
        this.i = false;
        int i4 = (i & 59) + (i | 59);
        j = i4 % 128;
        int i5 = i4 % 2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ek\e$d.smali */
    static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        private static int b;
        private static long c;
        private static int d;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            b = 1;
            c();
            TextUtils.indexOf("", "");
            ViewConfiguration.getKeyRepeatDelay();
            ViewConfiguration.getPressedStateDuration();
            TextUtils.getCapsMode("", 0, 0);
            ViewConfiguration.getMaximumDrawingCacheSize();
            View.resolveSizeAndState(0, 0, 0);
            ViewConfiguration.getGlobalActionKeyTimeout();
            View.resolveSizeAndState(0, 0, 0);
            SystemClock.elapsedRealtime();
            AudioTrack.getMinVolume();
            ViewConfiguration.getTapTimeout();
            View.resolveSize(0, 0);
            PointF.length(0.0f, 0.0f);
            ViewConfiguration.getScrollDefaultDelay();
            ViewConfiguration.getDoubleTapTimeout();
            ViewConfiguration.getKeyRepeatTimeout();
            KeyEvent.getMaxKeyCode();
            Color.rgb(0, 0, 0);
            int i = b + Opcodes.LSUB;
            d = i % 128;
            int i2 = i % 2;
        }

        static void c() {
            a = new char[]{11436, 56393, 52581, 65103, 61197, 38936, 35086, 47830, 27907, 40428, 36040, 49091, 44701, 55717, 34480, 30282, 26473, 21616, 17695, 12817, 9001, 4329, 459, 65262, 61421, 56456, 52656, 47807, 43647, 39748, 34923, 31036, 30215, 26427, 21554, 17874, 13045, 9191, 4255, 405, 11450, 56400, 52594, 65146, 61192, 38939, 2718, 64101, 60251, 55375, 51514, 48696, 44806, 40178, 36292, 29403, 25563, 20651, 16775, 13957, 9839, 5991, 1106, 62782, 64010, 60191, 55325, 51700, 48860, 44994, 40092, 36268, 11432, 56392, 52607, 65121, 61194, 38925, 35107, 47846, 43982, 21705, 17912, 30339, 26522, 4276, 'O', 12665, 8813, 54033, 56339, 52491, 65085, 61429, 39154, 35318, 47769, 43907, 21695, 17477, 11424, 56394, 52581, 65135, 61201, 38913, 35107, 47762, 43986, 21736, 17898, 30354, 26528, 4275, 31, 12620, 8822, 54022, 56387, 52539, 65056, 61390, 39095, 35299, 47745, 43932, 21755, 17420, 29957, 45995, 17245, 21107, 24942};
            c = -6711462131351823324L;
        }

        private static void f(int i, int i2, int i3, Object[] objArr) {
            int i4 = i + 102;
            byte[] bArr = $$a;
            int i5 = 4 - (i3 * 3);
            int i6 = 1 - (i2 * 3);
            byte[] bArr2 = new byte[i6];
            int i7 = -1;
            int i8 = i6 - 1;
            if (bArr == null) {
                i5++;
                i4 += i5;
            }
            while (true) {
                i7++;
                bArr2[i7] = (byte) i4;
                if (i7 == i8) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                } else {
                    byte b2 = bArr[i5];
                    i5++;
                    i4 += b2;
                }
            }
        }

        static void init$0() {
            $$a = new byte[]{1, 28, -75, 69};
            $$b = 200;
        }

        d() {
        }

        static o.eg.b a(e eVar) throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            e((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, Color.green(0) + 8, objArr);
            bVar.d(((String) objArr[0]).intern(), eVar.e());
            Object[] objArr2 = new Object[1];
            e((char) (16808 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 8 - Color.argb(0, 0, 0, 0), 6 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr2);
            bVar.d(((String) objArr2[0]).intern(), eVar.a());
            Object[] objArr3 = new Object[1];
            e((char) (43532 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 14 - (KeyEvent.getMaxKeyCode() >> 16), TextUtils.indexOf("", "", 0, 0) + 26, objArr3);
            bVar.d(((String) objArr3[0]).intern(), eVar.b());
            Object[] objArr4 = new Object[1];
            e((char) TextUtils.getOffsetAfter("", 0), 40 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (Process.myPid() >> 22) + 6, objArr4);
            bVar.d(((String) objArr4[0]).intern(), eVar.c().e());
            Object[] objArr5 = new Object[1];
            e((char) (9764 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), TextUtils.lastIndexOf("", '0') + 47, 26 - Color.alpha(0), objArr5);
            bVar.d(((String) objArr5[0]).intern(), eVar.d());
            Object[] objArr6 = new Object[1];
            e((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), View.resolveSize(0, 0) + 72, 27 - TextUtils.lastIndexOf("", '0', 0), objArr6);
            bVar.d(((String) objArr6[0]).intern(), eVar.h());
            int i = d + 5;
            b = i % 128;
            switch (i % 2 == 0 ? '1' : Typography.less) {
                case '<':
                    return bVar;
                default:
                    int i2 = 49 / 0;
                    return bVar;
            }
        }

        static e c(o.eg.b bVar) throws o.eg.d {
            try {
                Object[] objArr = new Object[1];
                e((char) (Process.getGidForName("") + 1), TextUtils.lastIndexOf("", '0', 0, 0) + 1, (ViewConfiguration.getTouchSlop() >> 8) + 8, objArr);
                String r = bVar.r(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                e((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 16809), (Process.myPid() >> 22) + 8, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 5, objArr2);
                String r2 = bVar.r(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                e((char) (43531 - (ViewConfiguration.getPressedStateDuration() >> 16)), 15 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 26 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr3);
                boolean booleanValue = bVar.g(((String) objArr3[0]).intern()).booleanValue();
                Object[] objArr4 = new Object[1];
                e((char) (Process.myPid() >> 22), 40 - Drawable.resolveOpacity(0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 5, objArr4);
                o.el.b b2 = o.el.b.b(bVar.r(((String) objArr4[0]).intern()));
                Object[] objArr5 = new Object[1];
                e((char) (View.combineMeasuredStates(0, 0) + 9764), 46 - View.resolveSizeAndState(0, 0, 0), 26 - ((Process.getThreadPriority(0) + 20) >> 6), objArr5);
                String q = bVar.q(((String) objArr5[0]).intern());
                Object[] objArr6 = new Object[1];
                e((char) (ExpandableListView.getPackedPositionChild(0L) + 1), (KeyEvent.getMaxKeyCode() >> 16) + 72, (ViewConfiguration.getWindowTouchSlop() >> 8) + 28, objArr6);
                e eVar = new e(r, r2, b2, booleanValue, q, bVar.g(((String) objArr6[0]).intern()).booleanValue());
                int i = d + 73;
                b = i % 128;
                int i2 = i % 2;
                return eVar;
            } catch (IllegalArgumentException e) {
                StringBuilder sb = new StringBuilder();
                Object[] objArr7 = new Object[1];
                e((char) View.MeasureSpec.getSize(0), 100 - Color.red(0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 29, objArr7);
                StringBuilder append = sb.append(((String) objArr7[0]).intern());
                Object[] objArr8 = new Object[1];
                e((char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 39, View.MeasureSpec.getSize(0) + 6, objArr8);
                String intern = ((String) objArr8[0]).intern();
                Object[] objArr9 = new Object[1];
                e((char) (40716 - (KeyEvent.getMaxKeyCode() >> 16)), 129 - View.combineMeasuredStates(0, 0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 4, objArr9);
                throw new o.eg.d(append.append(bVar.c(intern, ((String) objArr9[0]).intern())).toString());
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void e(char r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 598
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ek.e.d.e(char, int, int, java.lang.Object[]):void");
        }
    }
}
