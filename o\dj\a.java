package o.dj;

import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static final Object c;
    private static int d;

    static void e() {
        a = new char[]{50852, 50692, 50717, 50715, 50713, 50704, 50714, 50690, 50709, 50710, 50708, 50710, 50695, 50697, 50713, 50713, 50716, 50718, 50709, 50695, 50703, 50783, 51177, 51172, 51175, 51179, 51175, 51156, 51155, 51192, 51193, 51168, 51174, 51198, 51179, 51176, 51174, 51172, 51199, 51169, 51177, 51168, 51197, 51171, 51197};
    }

    private static void g(short s, byte b2, byte b3, Object[] objArr) {
        byte[] bArr = $$a;
        int i = s + 66;
        int i2 = b2 + 4;
        int i3 = (b3 * 2) + 1;
        byte[] bArr2 = new byte[i3];
        int i4 = -1;
        int i5 = i3 - 1;
        if (bArr == null) {
            int i6 = i2 + i5;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
            i2 = i2;
            i = i6;
        }
        while (true) {
            int i7 = i4 + 1;
            bArr2[i7] = (byte) i;
            if (i7 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i8 = i2 + 1;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = i7;
            i2 = i8;
            i = bArr[i8] + i;
        }
    }

    static void init$0() {
        $$a = new byte[]{85, 91, 121, -13};
        $$b = 66;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        d = 1;
        e();
        c = new Object();
        int i = b + 31;
        d = i % 128;
        switch (i % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static void a() {
        synchronized (c) {
            g.c();
            Object[] objArr = new Object[1];
            f("\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001", new int[]{0, 21, 109, 19}, false, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001", new int[]{21, 24, Opcodes.MONITORENTER, 0}, false, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:51:0x0124, code lost:
    
        if (r0[r1.d] == 1) goto L58;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 918
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.a.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
