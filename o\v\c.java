package o.v;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.an.a;
import o.an.f;
import o.eo.f;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\c.smali */
public abstract class c<TRres, TRreq, TR extends o.an.a<TRreq, TRres>, T extends o.ep.a<TRres>> extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int m;

    /* renamed from: o, reason: collision with root package name */
    private static long f101o;
    protected final boolean h;
    o.ee.h i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        k = 1;
        f101o = 2963067030782406518L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void A(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 4
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.v.c.$$d
            int r6 = r6 * 3
            int r6 = r6 + 68
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r8 = r8 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r8]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.c.A(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{106, 33, -117, 89};
        $$e = 104;
    }

    abstract String a();

    abstract T b(Context context);

    abstract TR e(T t);

    abstract f.a p();

    abstract AntelopErrorCode s();

    abstract String t();

    abstract o.ee.i v();

    abstract Activity y();

    static /* synthetic */ o.p.g a(c cVar) {
        int i = m + 49;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = k + 75;
        m = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 14 : '+') {
            case '+':
                return l;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g b(c cVar) {
        int i = m + 59;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = k + 43;
        m = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ o.p.g c(c cVar) {
        int i = k + 83;
        m = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = k + 65;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return l;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g d(c cVar) {
        int i = m + Opcodes.LSHR;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = k + 81;
        m = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g e(c cVar) {
        int i = m + 93;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = m + 9;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 86 / 0;
                return l;
            default:
                return l;
        }
    }

    static /* synthetic */ o.p.g f(c cVar) {
        int i = k + 99;
        m = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = m + 47;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return l;
            default:
                throw null;
        }
    }

    static /* synthetic */ o.p.g g(c cVar) {
        int i = m + 93;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                cVar.l();
                throw null;
            default:
                o.p.g l = cVar.l();
                int i2 = m + 49;
                k = i2 % 128;
                int i3 = i2 % 2;
                return l;
        }
    }

    static /* synthetic */ o.p.g h(c cVar) {
        int i = k + 33;
        m = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = m + 53;
        k = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ void i(c cVar) {
        int i = k + 17;
        m = i % 128;
        boolean z = i % 2 != 0;
        cVar.n();
        switch (z) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static /* synthetic */ o.p.g j(c cVar) {
        int i = m + 83;
        k = i % 128;
        int i2 = i % 2;
        o.p.g l = cVar.l();
        int i3 = k + Opcodes.LSUB;
        m = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    public c(String str, o.eo.e eVar, boolean z) {
        super(str, eVar);
        this.i = null;
        this.h = z;
    }

    /* JADX WARN: Code restructure failed: missing block: B:9:0x0029, code lost:
    
        if (r3 == null) goto L17;
     */
    @Override // o.p.h
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(android.content.Context r3, final o.ei.c r4, o.h.d r5) {
        /*
            r2 = this;
            int r3 = o.v.c.k
            int r3 = r3 + 81
            int r5 = r3 % 128
            o.v.c.m = r5
            int r3 = r3 % 2
            r5 = 0
            r0 = 1
            if (r3 == 0) goto L10
            r3 = r5
            goto L11
        L10:
            r3 = r0
        L11:
            switch(r3) {
                case 1: goto L19;
                default: goto L14;
            }
        L14:
            android.app.Activity r3 = r2.y()
            goto L26
        L19:
            android.app.Activity r3 = r2.y()
            if (r3 != 0) goto L21
            r1 = r0
            goto L22
        L21:
            r1 = r5
        L22:
            switch(r1) {
                case 0: goto L62;
                default: goto L25;
            }
        L25:
            goto L2b
        L26:
            r1 = 83
            int r1 = r1 / r5
            if (r3 != 0) goto L62
        L2b:
            o.ee.g.c()
            java.lang.String r3 = r2.a()
            int r4 = android.os.Process.myPid()
            int r4 = r4 >> 22
            int r4 = 1 - r4
            java.lang.Object[] r0 = new java.lang.Object[r0]
            java.lang.String r1 = "묍뭿㶪鉶ྛ傧犵簞כﱔ䴲뇤옶뻴谝\uf070胁禂쯺㚍䇴㠹ब痡ɀ懲䡏둄첹떛螡\ufaed贐琱씆㤛乙"
            z(r1, r4, r0)
            r4 = r0[r5]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            o.ee.g.d(r3, r4)
            o.p.g r3 = r2.l()
            if (r3 == 0) goto L60
            o.p.g r3 = r2.l()
            o.bv.c r4 = new o.bv.c
            fr.antelop.sdk.AntelopErrorCode r5 = fr.antelop.sdk.AntelopErrorCode.InternalError
            r4.<init>(r5)
            r3.onError(r4)
        L60:
            return
        L62:
            o.ep.a r5 = r2.b(r3)
            o.v.c$4 r0 = new o.v.c$4
            r0.<init>()
            r5.e(r0)
            int r3 = o.v.c.m
            int r3 = r3 + 99
            int r4 = r3 % 128
            o.v.c.k = r4
            int r3 = r3 % 2
            return
        L79:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.c.a(android.content.Context, o.ei.c, o.h.d):void");
    }

    final void c(final Activity activity, final o.ei.c cVar) {
        final a.c cVar2 = new a.c() { // from class: o.v.c.1
            private static int b = 0;
            private static int c = 1;

            @Override // o.ep.a.c
            public final void a(String str) {
                int i = b;
                int i2 = (i & 77) + (i | 77);
                c = i2 % 128;
                int i3 = i2 % 2;
                cVar.j().b().b(((d) c.this).n.e(), str, c.this.p());
                o.p.g c2 = c.c(c.this);
                switch (c2 != null ? (char) 2 : '`') {
                    case Opcodes.IADD /* 96 */:
                        return;
                    default:
                        int i4 = c;
                        int i5 = (i4 & 9) + (i4 | 9);
                        b = i5 % 128;
                        char c3 = i5 % 2 != 0 ? Typography.greater : ')';
                        c2.onProcessSuccess();
                        switch (c3) {
                            case '>':
                                throw null;
                            default:
                                return;
                        }
                }
            }

            @Override // o.ep.a.c
            public final void d(o.bv.c cVar3) {
                int i = (b + 10) - 1;
                c = i % 128;
                int i2 = i % 2;
                o.p.g j = c.j(c.this);
                switch (j != null ? '\b' : 'V') {
                    case Opcodes.SASTORE /* 86 */:
                        break;
                    default:
                        int i3 = c;
                        int i4 = ((i3 | 79) << 1) - (i3 ^ 79);
                        b = i4 % 128;
                        int i5 = i4 % 2;
                        j.onError(cVar3);
                        int i6 = b + Opcodes.LSUB;
                        c = i6 % 128;
                        int i7 = i6 % 2;
                        break;
                }
                int i8 = b + Opcodes.DREM;
                c = i8 % 128;
                int i9 = i8 % 2;
            }
        };
        new o.an.f(activity, new f.b<TRres>() { // from class: o.v.c.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static long a;
            private static int e;
            private static int j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                e = 0;
                j = 1;
                a = -4633407067520230935L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0030). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r6, short r7, byte r8, java.lang.Object[] r9) {
                /*
                    int r7 = r7 * 4
                    int r7 = r7 + 1
                    int r8 = r8 * 3
                    int r8 = r8 + 68
                    int r6 = r6 * 2
                    int r6 = 3 - r6
                    byte[] r0 = o.v.c.AnonymousClass2.$$a
                    byte[] r1 = new byte[r7]
                    r2 = 0
                    if (r0 != 0) goto L18
                    r8 = r7
                    r4 = r8
                    r3 = r2
                    r7 = r6
                    goto L30
                L18:
                    r3 = r2
                L19:
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    int r6 = r6 + 1
                    int r3 = r3 + 1
                    if (r3 != r7) goto L2a
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2a:
                    r4 = r0[r6]
                    r5 = r7
                    r7 = r6
                    r6 = r8
                    r8 = r5
                L30:
                    int r6 = r6 + r4
                    r5 = r8
                    r8 = r6
                    r6 = r7
                    r7 = r5
                    goto L19
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.c.AnonymousClass2.g(int, short, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{33, 17, -65, 85};
                $$b = Opcodes.FMUL;
            }

            @Override // o.an.f.b
            public final void c(final TRres trres) {
                o.ee.g.c();
                String a2 = c.this.a();
                Object[] objArr = new Object[1];
                f("뜛띴\uf7f1꺩龭왟ⶑ춈펰\u10cf㪖붓给谫凎ᢵ駾⥺촄\uf7cc␟䉬栨勩佀ﾢ蝆측", (-1) - TextUtils.lastIndexOf("", '0', 0, 0), objArr);
                o.ee.g.d(a2, ((String) objArr[0]).intern());
                final o.ep.a b = c.this.b(activity);
                b.d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.v.c.2.5
                    private static int d = 0;
                    private static int b = 1;

                    @Override // o.ep.a.InterfaceC0042a
                    public final /* synthetic */ void e(List<o.ep.e> list) {
                        int i = d;
                        int i2 = (i ^ Opcodes.DNEG) + ((i & Opcodes.DNEG) << 1);
                        b = i2 % 128;
                        int i3 = i2 % 2;
                        a(list);
                        int i4 = (b + 42) - 1;
                        d = i4 % 128;
                        switch (i4 % 2 == 0) {
                            case false:
                                int i5 = 87 / 0;
                                return;
                            default:
                                return;
                        }
                    }

                    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                        */
                    private void a(java.util.List<o.ep.e> r13) {
                        /*
                            Method dump skipped, instructions count: 264
                            To view this dump add '--comments-level debug' option
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.v.c.AnonymousClass2.AnonymousClass5.a(java.util.List):void");
                    }

                    @Override // o.ep.a.InterfaceC0042a
                    public final void e(o.bv.c cVar3) {
                        int i = b + 11;
                        d = i % 128;
                        switch (i % 2 != 0 ? 'L' : 'I') {
                            case Base64.mimeLineLength /* 76 */:
                                cVar2.d(cVar3);
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                            default:
                                cVar2.d(cVar3);
                                return;
                        }
                    }
                });
                int i = j + 93;
                e = i % 128;
                int i2 = i % 2;
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:13:0x009b. Please report as an issue. */
            @Override // o.an.f.b
            public final void c(o.bb.d dVar) {
                o.ee.g.c();
                String a2 = c.this.a();
                Object[] objArr = new Object[1];
                f("ᥝᤲ瀴⥬銭㤩ₑ㋾緶霊㞖䋥킟௮峎\ue7c3㞸꺿쀄ࢺ詙얼攼궕\ue109硷詇ㅁ", TextUtils.getOffsetAfter("", 0), objArr);
                o.ee.g.d(a2, ((String) objArr[0]).intern());
                switch (c.f(c.this) == null) {
                    case false:
                        int i = j + 13;
                        e = i % 128;
                        int i2 = i % 2;
                        switch (dVar.d() != o.bb.a.aA) {
                            case false:
                                int i3 = j + 1;
                                e = i3 % 128;
                                switch (i3 % 2 != 0) {
                                    case false:
                                        c.i(c.this);
                                        c.g(c.this).onAuthenticationDeclined();
                                        return;
                                    default:
                                        c.i(c.this);
                                        c.g(c.this).onAuthenticationDeclined();
                                        int i4 = 12 / 0;
                                        return;
                                }
                            default:
                                c.h(c.this).onError(o.bv.c.c(dVar));
                                int i5 = j + Opcodes.DSUB;
                                e = i5 % 128;
                                switch (i5 % 2 != 0) {
                                }
                                return;
                        }
                    default:
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(java.lang.String r17, int r18, java.lang.Object[] r19) {
                /*
                    Method dump skipped, instructions count: 366
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.c.AnonymousClass2.f(java.lang.String, int, java.lang.Object[]):void");
            }
        }, cVar, e((c<TRres, TRreq, TR, T>) b(activity))).a(this.a, o(), ((d) this).n.e(), this.i);
        int i = k + 49;
        m = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x006d, code lost:
    
        if (r11.getLine2().length() <= 89) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x007b, code lost:
    
        r1 = fr.antelop.sdk.exception.WalletValidationErrorCode.InvalidFormat;
        r3 = new java.lang.Object[1];
        z("⃝₱袌❌剅൹筠痾鹋䤡უ렄巻\u0bda톗崙ᬑ첰阴㼹\uda21贚哵簒馌俣ᗟ붥坭¬\uda3d\uf30dᚗ셝颗リ햕菤她熖", 1 - (android.view.KeyEvent.getMaxKeyCode() >> 16), r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0098, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r1, r2, ((java.lang.String) r3[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0078, code lost:
    
        if (r11.getLine2().length() <= 64) goto L32;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void c(fr.antelop.sdk.util.Address r11) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 462
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.c.c(fr.antelop.sdk.util.Address):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void z(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 356
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.c.z(java.lang.String, int, java.lang.Object[]):void");
    }
}
