package fr.antelop.sdk.settings;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\settings\WalletSettingsValue.smali */
public final class WalletSettingsValue<T> {
    private final WalletSettingsRights rights;
    private final T value;

    public WalletSettingsValue(T t, WalletSettingsRights walletSettingsRights) {
        this.value = t;
        this.rights = walletSettingsRights;
    }

    public final T getValue() {
        return this.value;
    }

    public final WalletSettingsRights getRights() {
        return this.rights;
    }

    public final String toString() {
        return new StringBuilder("WalletSettingsValue{value=").append(this.value).append(", rights=").append(this.rights).append('}').toString();
    }
}
