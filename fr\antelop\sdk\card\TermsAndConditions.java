package fr.antelop.sdk.card;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.net.URI;
import java.util.Objects;
import kotlin.io.encoding.Base64;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.e.a;
import o.eo.e;
import o.eo.h;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\TermsAndConditions.smali */
public final class TermsAndConditions {
    private final e card;
    private final FileType fileType;
    private final String sourceId;
    private final URI uri;

    public TermsAndConditions(String str, URI uri, FileType fileType, e eVar) {
        this.sourceId = str;
        this.uri = uri;
        this.fileType = fileType;
        this.card = eVar;
    }

    public final String getSourceId() {
        return this.sourceId;
    }

    public final URI getUri() {
        return this.uri;
    }

    public final FileType getFileType() {
        return this.fileType;
    }

    public final boolean requireApproval() {
        return Objects.equals(this.card.t(), h.c);
    }

    public final void approve(Context context, AntelopCallback antelopCallback) throws WalletValidationException {
        this.card.a(context, true, antelopCallback);
    }

    public final void decline(Context context, AntelopCallback antelopCallback) throws WalletValidationException {
        this.card.a(context, false, antelopCallback);
    }

    public final String toString() {
        return new StringBuilder("TermsAndConditions{sourceId='").append(this.sourceId).append('\'').append(", uri=").append(this.uri).append(", sourceType=").append(this.fileType).append(", requireApproval=").append(requireApproval()).append('}').toString();
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\TermsAndConditions$FileType.smali */
    public static final class FileType {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static final /* synthetic */ FileType[] $VALUES;
        public static final FileType HTML;
        public static final FileType JPG;
        public static final FileType OTHER;
        public static final FileType PDF;
        public static final FileType PNG;
        public static final FileType TEXT;
        private static int a;
        private static int d;
        private static char[] e;

        static void a() {
            e = new char[]{50873, 50726, 50725, 50733, 50728};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0033). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void f(short r7, short r8, byte r9, java.lang.Object[] r10) {
            /*
                int r8 = r8 + 66
                int r9 = r9 * 2
                int r9 = 1 - r9
                byte[] r0 = fr.antelop.sdk.card.TermsAndConditions.FileType.$$a
                int r7 = r7 + 4
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L17
                r3 = r1
                r5 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r8
                r8 = r7
                goto L33
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r8
                int r5 = r3 + 1
                r1[r3] = r4
                if (r5 != r9) goto L27
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L27:
                int r7 = r7 + 1
                r3 = r0[r7]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r6
            L33:
                int r7 = r7 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r5
                r6 = r8
                r8 = r7
                r7 = r6
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.card.TermsAndConditions.FileType.f(short, short, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{60, -43, Tnaf.POW_2_WIDTH, 118};
            $$b = Opcodes.NEW;
        }

        private static /* synthetic */ FileType[] $values() {
            int i = d;
            int i2 = i + 9;
            a = i2 % 128;
            int i3 = i2 % 2;
            FileType[] fileTypeArr = {HTML, PDF, JPG, PNG, TEXT, OTHER};
            int i4 = i + 3;
            a = i4 % 128;
            int i5 = i4 % 2;
            return fileTypeArr;
        }

        private FileType(String str, int i) {
        }

        public static FileType valueOf(String str) {
            int i = a + 93;
            d = i % 128;
            boolean z = i % 2 != 0;
            Object obj = null;
            FileType fileType = (FileType) Enum.valueOf(FileType.class, str);
            switch (z) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    int i2 = a + Opcodes.DDIV;
                    d = i2 % 128;
                    switch (i2 % 2 != 0 ? '^' : 'M') {
                        case 'M':
                            return fileType;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }

        public static FileType[] values() {
            int i = a + 61;
            d = i % 128;
            switch (i % 2 != 0) {
                case false:
                    return (FileType[]) $VALUES.clone();
                default:
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            a = 1;
            a();
            HTML = new FileType("HTML", 0);
            PDF = new FileType("PDF", 1);
            JPG = new FileType("JPG", 2);
            PNG = new FileType("PNG", 3);
            TEXT = new FileType("TEXT", 4);
            Object[] objArr = new Object[1];
            c("\u0001\u0001\u0000\u0001\u0001", new int[]{0, 5, Opcodes.IFNE, 0}, false, objArr);
            OTHER = new FileType(((String) objArr[0]).intern(), 5);
            $VALUES = $values();
            int i = d + 79;
            a = i % 128;
            switch (i % 2 == 0 ? 'a' : 'L') {
                case Base64.mimeLineLength /* 76 */:
                    return;
                default:
                    throw null;
            }
        }

        public static FileType extractFromExtensionFile(String str) {
            int i = a;
            int i2 = i + Opcodes.DDIV;
            d = i2 % 128;
            int i3 = i2 % 2;
            Object obj = null;
            switch (str == null ? '`' : ',') {
                case Opcodes.IADD /* 96 */:
                    int i4 = i + Opcodes.LUSHR;
                    d = i4 % 128;
                    if (i4 % 2 == 0) {
                        return OTHER;
                    }
                    obj.hashCode();
                    throw null;
                default:
                    if (str.equalsIgnoreCase("html")) {
                        return HTML;
                    }
                    switch (!str.equalsIgnoreCase("pdf")) {
                        case false:
                            return PDF;
                        default:
                            switch (str.equalsIgnoreCase("jpg") ? (char) 22 : '*') {
                                case '*':
                                    if (str.equalsIgnoreCase("png")) {
                                        return PNG;
                                    }
                                    if (!str.equalsIgnoreCase("txt")) {
                                        return OTHER;
                                    }
                                    int i5 = d + 67;
                                    a = i5 % 128;
                                    if (i5 % 2 != 0) {
                                        return TEXT;
                                    }
                                    throw null;
                                default:
                                    int i6 = a + 33;
                                    d = i6 % 128;
                                    if (i6 % 2 == 0) {
                                        return JPG;
                                    }
                                    int i7 = 89 / 0;
                                    return JPG;
                            }
                    }
            }
        }

        private static void c(String str, int[] iArr, boolean z, Object[] objArr) {
            int i;
            int i2;
            int i3;
            String str2 = str;
            int i4 = $11;
            int i5 = i4 + 37;
            $10 = i5 % 128;
            int i6 = 2;
            int i7 = i5 % 2;
            byte[] bArr = str2;
            if (str2 != null) {
                int i8 = i4 + Opcodes.DMUL;
                $10 = i8 % 128;
                int i9 = i8 % 2;
                bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
            }
            byte[] bArr2 = bArr;
            l lVar = new l();
            int i10 = 0;
            int i11 = iArr[0];
            int i12 = 1;
            int i13 = iArr[1];
            int i14 = iArr[2];
            int i15 = iArr[3];
            char[] cArr = e;
            if (cArr != null) {
                int length = cArr.length;
                char[] cArr2 = new char[length];
                int i16 = 0;
                while (true) {
                    switch (i16 < length ? i12 : i10) {
                        case 1:
                            int i17 = $11 + 71;
                            $10 = i17 % 128;
                            switch (i17 % i6 != 0 ? ',' : '=') {
                                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                                    try {
                                        Object[] objArr2 = new Object[i12];
                                        objArr2[i10] = Integer.valueOf(cArr[i16]);
                                        Object obj = a.s.get(1951085128);
                                        if (obj != null) {
                                            i3 = i14;
                                            i2 = length;
                                        } else {
                                            Class cls = (Class) a.c(((Process.getThreadPriority(i10) + 20) >> 6) + 11, (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 43 - (ViewConfiguration.getTouchSlop() >> 8));
                                            byte b = (byte) (-1);
                                            i2 = length;
                                            i3 = i14;
                                            Object[] objArr3 = new Object[1];
                                            f(b, (byte) (b & 54), (byte) i10, objArr3);
                                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                            a.s.put(1951085128, obj);
                                        }
                                        cArr2[i16] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                        i16++;
                                        length = i2;
                                        i14 = i3;
                                        i10 = 0;
                                        i6 = 2;
                                        i12 = 1;
                                        break;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                                default:
                                    int i18 = i14;
                                    int i19 = length;
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(cArr[i16])};
                                        Object obj2 = a.s.get(1951085128);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) a.c(11 - TextUtils.getCapsMode("", 0, 0), (char) View.combineMeasuredStates(0, 0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 42);
                                            byte b2 = (byte) (-1);
                                            Object[] objArr5 = new Object[1];
                                            f(b2, (byte) (b2 & 54), (byte) 0, objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                            a.s.put(1951085128, obj2);
                                        }
                                        cArr2[i16] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                        i16 <<= 0;
                                        length = i19;
                                        i14 = i18;
                                        i10 = 0;
                                        i6 = 2;
                                        i12 = 1;
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                            }
                        default:
                            i = i14;
                            cArr = cArr2;
                            break;
                    }
                }
            } else {
                i = i14;
            }
            char[] cArr3 = new char[i13];
            System.arraycopy(cArr, i11, cArr3, 0, i13);
            if (bArr2 != null) {
                char[] cArr4 = new char[i13];
                lVar.d = 0;
                char c = 0;
                while (lVar.d < i13) {
                    switch (bArr2[lVar.d] == 1 ? (char) 17 : 'Z') {
                        case 17:
                            int i20 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c)};
                                Object obj3 = a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls3 = (Class) a.c(11 - TextUtils.getTrimmedLength(""), (char) (ImageFormat.getBitsPerPixel(0) + 1), (ViewConfiguration.getScrollBarSize() >> 8) + 448);
                                    byte b3 = (byte) (-1);
                                    Object[] objArr7 = new Object[1];
                                    f(b3, (byte) (b3 & 53), (byte) 0, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    a.s.put(2016040108, obj3);
                                }
                                cArr4[i20] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                break;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        default:
                            int i21 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c)};
                                Object obj4 = a.s.get(804049217);
                                if (obj4 == null) {
                                    Class cls4 = (Class) a.c(TextUtils.indexOf("", "", 0, 0) + 10, (char) TextUtils.getCapsMode("", 0, 0), TextUtils.indexOf((CharSequence) "", '0', 0) + 208);
                                    byte b4 = (byte) (-1);
                                    Object[] objArr9 = new Object[1];
                                    f(b4, (byte) (b4 & 56), (byte) 0, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    a.s.put(804049217, obj4);
                                }
                                cArr4[i21] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                break;
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                    }
                    c = cArr4[lVar.d];
                    try {
                        Object[] objArr10 = {lVar, lVar};
                        Object obj5 = a.s.get(-2112603350);
                        if (obj5 == null) {
                            Class cls5 = (Class) a.c((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 11, (char) Color.green(0), 259 - (KeyEvent.getMaxKeyCode() >> 16));
                            byte b5 = (byte) (-1);
                            byte b6 = (byte) (b5 + 1);
                            Object[] objArr11 = new Object[1];
                            f(b5, b6, b6, objArr11);
                            obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                            a.s.put(-2112603350, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr10);
                    } catch (Throwable th5) {
                        Throwable cause5 = th5.getCause();
                        if (cause5 == null) {
                            throw th5;
                        }
                        throw cause5;
                    }
                }
                int i22 = $11 + 7;
                $10 = i22 % 128;
                int i23 = i22 % 2;
                cArr3 = cArr4;
            }
            switch (i15 > 0 ? 'I' : 'X') {
                case 'I':
                    char[] cArr5 = new char[i13];
                    System.arraycopy(cArr3, 0, cArr5, 0, i13);
                    int i24 = i13 - i15;
                    System.arraycopy(cArr5, 0, cArr3, i24, i15);
                    System.arraycopy(cArr5, i15, cArr3, 0, i24);
                    break;
            }
            if (z) {
                int i25 = $11 + Opcodes.LSUB;
                $10 = i25 % 128;
                int i26 = i25 % 2;
                char[] cArr6 = new char[i13];
                int i27 = 0;
                while (true) {
                    lVar.d = i27;
                    switch (lVar.d < i13 ? '3' : ')') {
                        case ')':
                            cArr3 = cArr6;
                            break;
                        default:
                            int i28 = $11 + 19;
                            $10 = i28 % 128;
                            if (i28 % 2 != 0) {
                                cArr6[lVar.d] = cArr3[(i13 << lVar.d) * 1];
                                i27 = lVar.d << 0;
                            } else {
                                cArr6[lVar.d] = cArr3[(i13 - lVar.d) - 1];
                                i27 = lVar.d + 1;
                            }
                    }
                }
            }
            if (i > 0) {
                int i29 = 0;
                while (true) {
                    lVar.d = i29;
                    switch (lVar.d < i13) {
                        case false:
                            break;
                        default:
                            cArr3[lVar.d] = (char) (cArr3[lVar.d] - iArr[2]);
                            i29 = lVar.d + 1;
                    }
                }
            }
            objArr[0] = new String(cArr3);
        }
    }
}
