package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzhq.smali */
public final class zzhq extends zzes implements zzfx {
    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private zzhq() {
        /*
            r1 = this;
            com.google.android.gms.internal.auth.zzhr r0 = com.google.android.gms.internal.auth.zzhr.zzj()
            r1.<init>(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.google.android.gms.internal.auth.zzhq.<init>():void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    /* synthetic */ zzhq(com.google.android.gms.internal.auth.zzhp r1) {
        /*
            r0 = this;
            com.google.android.gms.internal.auth.zzhr r1 = com.google.android.gms.internal.auth.zzhr.zzj()
            r0.<init>(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: com.google.android.gms.internal.auth.zzhq.<init>(com.google.android.gms.internal.auth.zzhp):void");
    }
}
