package o.ap;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.credentials.CredentialsApi;
import fr.antelop.sdk.digitalcard.VirtualCardNumberOption;
import java.util.Date;
import java.util.GregorianCalendar;
import o.cf.i;
import o.ee.g;
import o.eo.j;
import o.h.d;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ap\e.smali */
public final class e extends b<c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long f;
    private static int i;
    private static int j;
    String a;
    String b;
    boolean c;
    VirtualCardNumberOption d;
    d e;
    j g;
    String h;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ap\e$c.smali */
    public interface c {
        void b(String str);

        void b(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        j = 1;
        l();
        ViewConfiguration.getWindowTouchSlop();
        ImageFormat.getBitsPerPixel(0);
        int i2 = j + 15;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                break;
            default:
                int i3 = 91 / 0;
                break;
        }
    }

    static void init$0() {
        $$d = new byte[]{58, -5, 118, 12};
        $$e = 128;
    }

    static void l() {
        f = -7035803304014968431L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 112
            byte[] r0 = o.ap.e.$$d
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = r7 * 3
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L37:
            int r7 = -r7
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ap.e.n(short, int, byte, java.lang.Object[]):void");
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        int i2 = i + 45;
        j = i2 % 128;
        int i3 = i2 % 2;
        AsyncTaskC0027e k = k();
        int i4 = j + 75;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return k;
            default:
                throw null;
        }
    }

    public e(Context context, c cVar, o.ei.c cVar2) {
        super(context, cVar, cVar2, o.bb.e.t);
    }

    public final void c(d dVar, String str, String str2, VirtualCardNumberOption virtualCardNumberOption, boolean z) {
        g.c();
        Object[] objArr = new Object[1];
        m("콂倠\uf1c2ኙ눹폻璥鐧㗷嚩\uf650\u17fd뢈\ud857秥骸", 40787 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m("콥腹卬┶\uf738䤓᯿\uedc5뿯熭쎉關昸㠊訠尪⸔\ue0e7닫ӑ훭ꢔ窘쭾鴉漄ⅷ", 19991 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(virtualCardNumberOption).toString());
        this.e = dVar;
        this.b = str;
        this.a = str2;
        this.d = virtualCardNumberOption;
        this.c = z;
        c();
        int i2 = j + 71;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    private AsyncTaskC0027e k() {
        AsyncTaskC0027e asyncTaskC0027e = new AsyncTaskC0027e(this);
        int i2 = j + 11;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return asyncTaskC0027e;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i2 = j + 11;
        i = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        m("콂倠\uf1c2ኙ눹폻璥鐧㗷嚩\uf650\u17fd뢈\ud857秥骸", Drawable.resolveOpacity(0, 0) + 40787, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = i + 79;
        j = i4 % 128;
        int i5 = i4 % 2;
        return intern;
    }

    /* renamed from: o.ap.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ap\e$e.smali */
    static final class AsyncTaskC0027e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static boolean a;
        private static int b;
        private static long c;
        private static boolean d;
        private static char[] e;
        private static int f;
        private static int i;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            i = 0;
            f = 1;
            e = new char[]{61691, 61444, 61689, 61685, 61450, 61672, 61440, 61636, 61647, 61642, 61637, 61640, 61646, 61638, 61645, 61690, 61661, 61441, 61454, 61664, 61449, 61684, 61671, 61688, 61670, 61453, 61451, 61693, 61446, 61658, 61448, 61665, 61442};
            d = true;
            a = true;
            b = 782102678;
            c = 1713507056296656956L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(int r7, int r8, short r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.ap.e.AsyncTaskC0027e.$$d
                int r8 = r8 * 3
                int r8 = 4 - r8
                int r9 = r9 * 4
                int r9 = r9 + 1
                int r7 = r7 + 112
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L18
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r8
                goto L35
            L18:
                r3 = r2
            L19:
                r6 = r8
                r8 = r7
                r7 = r6
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                if (r4 != r9) goto L2b
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2b:
                r3 = r0[r7]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r6
            L35:
                int r8 = r8 + 1
                int r7 = r7 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ap.e.AsyncTaskC0027e.C(int, int, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{94, -116, 51, -9};
            $$e = Opcodes.INVOKESPECIAL;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i2 = f + Opcodes.LREM;
            i = i2 % 128;
            int i3 = i2 % 2;
        }

        AsyncTaskC0027e(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i2 = i + Opcodes.DDIV;
            f = i2 % 128;
            int i3 = i2 % 2;
            Object[] objArr = new Object[1];
            Object obj = null;
            w(null, View.MeasureSpec.getSize(0) + 127, null, "\u0087\u0081\u0086\u0083\u0085\u0084\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            int i4 = i + 99;
            f = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return intern;
            }
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(null, TextUtils.getCapsMode("", 0, 0) + 127, null, "\u0088\u008a\u008f\u0088\u008f\u008d\u008c\u008e\u008d\u008a\u008c\u008c\u008c\u0089\u008b\u0089\u008a\u0089\u0088", objArr);
            o.cf.d dVar = new o.cf.d(context, 39, ((String) objArr[0]).intern());
            int i2 = f + Opcodes.LNEG;
            i = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    int i3 = 43 / 0;
                    return dVar;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            Object obj = null;
            w(null, Color.rgb(0, 0, 0) + 16777343, null, "\u0090\u0091\u0090\u0082\u0084\u0081", objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).a);
            Object[] objArr2 = new Object[1];
            w(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "\u0083\u0092\u0084\u0087", objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((e) e()).d.getName());
            Object[] objArr3 = new Object[1];
            B("웚䯺\udcae慠\uf214Ӗ覒ᩔ꽐〶䋸힐塌\ued0e翁胻", 36150 - ExpandableListView.getPackedPositionChild(0L), objArr3);
            bVar.d(((String) objArr3[0]).intern(), ((e) e()).d.getValidityDurationFormat());
            Object[] objArr4 = new Object[1];
            w(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "\u009b\u0085\u0087\u0083\u0092\u009a\u0084\u0099\u0098\u0097\u0082\u0083\u0096\u0092\u0095\u0094\u0093\u0084\u0092", objArr4);
            bVar.d(((String) objArr4[0]).intern(), ((e) e()).d.getMaxPaymentNumber());
            int i2 = i + 49;
            f = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return bVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.cf.j n() {
            o.cf.j jVar = new o.cf.j(((e) e()).b, false, ((e) e()).e);
            int i2 = i + 29;
            f = i2 % 128;
            switch (i2 % 2 == 0 ? '7' : (char) 20) {
                case 20:
                    return jVar;
                default:
                    int i3 = 8 / 0;
                    return jVar;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i2 = i + 89;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            int i5 = i3 + 73;
            i = i5 % 128;
            int i6 = i5 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i2) {
            int i3 = f + 61;
            i = i3 % 128;
            int i4 = i3 % 2;
            switch (i2) {
                case 5001:
                    return o.bb.a.ay;
                case 5002:
                    return o.bb.a.az;
                default:
                    o.bb.a c2 = super.c(i2);
                    int i5 = f + 81;
                    i = i5 % 128;
                    switch (i5 % 2 != 0 ? '2' : (char) 25) {
                        case '2':
                            int i6 = 30 / 0;
                            return c2;
                        default:
                            return c2;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            Integer num;
            String r;
            Date date;
            String str;
            Object[] objArr = new Object[1];
            B("웅ॕ", (ViewConfiguration.getTouchSlop() >> 8) + 53149, objArr);
            String r2 = bVar.r(((String) objArr[0]).intern());
            Object[] objArr2 = new Object[1];
            w(null, 127 - KeyEvent.normalizeMetaState(0), null, "\u009b\u0095\u0085\u0084\u0085\u009b", objArr2);
            j.b bVar2 = (j.b) bVar.d(j.b.class, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            B("웜䈩켭䠿픙帛\udb03搟\ue142橠\uf764灸ﵜ", AndroidCharacter.getMirror('0') + 33991, objArr3);
            String r3 = bVar.r(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            w(null, 127 - View.combineMeasuredStates(0, 0), null, "\u0087\u009c\u0096", objArr4);
            String r4 = bVar.r(((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            B("움퉼\uefbd\uf8cb鐬ꆰ뫭嘒捐糦", TextUtils.lastIndexOf("", '0') + 5298, objArr5);
            String r5 = bVar.r(((String) objArr5[0]).intern());
            Object[] objArr6 = new Object[1];
            w(null, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0083\u0085\u0084\u009e\u009a\u0082\u009c\u009d\u0093\u0083", objArr6);
            String r6 = bVar.r(((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            w(null, 126 - TextUtils.lastIndexOf("", '0', 0, 0), null, "\u0081\u0084 \u0087\u0081\u009f", objArr7);
            String r7 = bVar.r(((String) objArr7[0]).intern());
            Object[] objArr8 = new Object[1];
            w(null, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 127, null, "\u0083\u0092\u0084\u0087", objArr8);
            String r8 = bVar.r(((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            B("웏吕\ue35f纬跴ᬲ똁앏傰\uefee紶衰", TextUtils.indexOf((CharSequence) "", '0', 0) + 37580, objArr9);
            Date b2 = bVar.b(((String) objArr9[0]).intern(), false);
            Object[] objArr10 = new Object[1];
            B("웁\u0ef4嚦鹗\ue629⿈瞗뽆蜊쳙ᓥ岑ꑵ\uec24㗐綞䕎", 51258 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr10);
            Integer j = bVar.j(((String) objArr10[0]).intern());
            Object[] objArr11 = new Object[1];
            w(null, 128 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), null, "\u0083\u0085\u0084\u009e\u009a\u0085\u009c\u0090\u009c¡\u0084\u0086\u0093\u0084\u0092", objArr11);
            Date b3 = bVar.b(((String) objArr11[0]).intern(), false);
            switch (((e) e()).c ? (char) 2 : 'U') {
                case 2:
                    int i2 = i + 95;
                    f = i2 % 128;
                    switch (i2 % 2 == 0 ? 'U' : (char) 31) {
                        case Opcodes.CASTORE /* 85 */:
                            num = j;
                            Object[] objArr12 = new Object[1];
                            w(null, 84 >> (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), null, "\u0084\u0085\u0084\u009e\u009a\u0084¡\u009d\u009b\u009c\u0090", objArr12);
                            r = bVar.r(((String) objArr12[0]).intern());
                            date = b2;
                            break;
                        default:
                            num = j;
                            date = b2;
                            Object[] objArr13 = new Object[1];
                            w(null, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0084\u0085\u0084\u009e\u009a\u0084¡\u009d\u009b\u009c\u0090", objArr13);
                            r = bVar.r(((String) objArr13[0]).intern());
                            break;
                    }
                    int i3 = f + 95;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                    str = r;
                    break;
                default:
                    date = b2;
                    num = j;
                    str = null;
                    break;
            }
            GregorianCalendar gregorianCalendar = new GregorianCalendar();
            int parseInt = Integer.parseInt(r6.substring(0, 2));
            int parseInt2 = Integer.parseInt(r6.substring(2, 4));
            gregorianCalendar.set(1, parseInt + CredentialsApi.CREDENTIAL_PICKER_REQUEST_CODE);
            gregorianCalendar.set(2, parseInt2);
            gregorianCalendar.set(5, 0);
            gregorianCalendar.set(11, 0);
            gregorianCalendar.set(12, 0);
            gregorianCalendar.set(13, 0);
            gregorianCalendar.set(14, 0);
            gregorianCalendar.roll(11, false);
            gregorianCalendar.roll(12, false);
            gregorianCalendar.roll(13, false);
            gregorianCalendar.roll(14, false);
            Date time = gregorianCalendar.getTime();
            if (b3 != null && b3.getTime() < time.getTime()) {
                int i5 = f + Opcodes.DMUL;
                i = i5 % 128;
                int i6 = i5 % 2;
                time = b3;
            }
            ((e) e()).g = new j(r2, bVar2, r3, r4, r5, time, r7, r8, date, num);
            ((e) e()).h = str;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final boolean p() {
            int i2 = f + 49;
            i = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return ((e) e()).c;
                default:
                    boolean z = ((e) e()).c;
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            switch (AnonymousClass3.d[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((e) e()).a);
                    int i2 = f + 75;
                    i = i2 % 128;
                    switch (i2 % 2 == 0) {
                        case false:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return;
                    }
                case 2:
                    f().e(g(), ((e) e()).a);
                    return;
                default:
                    super.t();
                    int i3 = i + 85;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i2 = f + 89;
            i = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0 ? 'E' : ',') {
                case ',':
                    c j = ((e) e()).j();
                    j jVar = ((e) e()).g;
                    j.b(((e) e()).h);
                    int i3 = f + 65;
                    i = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            throw null;
                        default:
                            return;
                    }
                default:
                    c j2 = ((e) e()).j();
                    j jVar2 = ((e) e()).g;
                    j2.b(((e) e()).h);
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i2 = f + Opcodes.LSHL;
            i = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    ((e) e()).j().b(dVar);
                    return;
                default:
                    ((e) e()).j().b(dVar);
                    int i3 = 61 / 0;
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
        /* JADX WARN: Type inference failed for: r1v1 */
        /* JADX WARN: Type inference failed for: r1v19, types: [byte[]] */
        private static void w(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 906
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ap.e.AsyncTaskC0027e.w(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void B(java.lang.String r19, int r20, java.lang.Object[] r21) {
            /*
                Method dump skipped, instructions count: 762
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ap.e.AsyncTaskC0027e.B(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.ap.e$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ap\e$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int a;
        static final /* synthetic */ int[] d;
        private static int e = 1;

        static {
            a = 0;
            int[] iArr = new int[o.bb.a.values().length];
            d = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = (e + 104) - 1;
                a = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.bb.a.az.ordinal()] = 2;
                int i2 = e;
                int i3 = (i2 ^ Opcodes.DREM) + ((i2 & Opcodes.DREM) << 1);
                a = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 736
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ap.e.m(java.lang.String, int, java.lang.Object[]):void");
    }
}
