package o.er;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\p.smali */
public final class p extends h {
    private static char a;
    private static char b;
    private static char e;
    private static int f;
    private static char j;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int i = 0;

    static {
        f = 1;
        e();
        Process.getGidForName("");
        int i2 = i + 67;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void e() {
        a = (char) 38320;
        b = (char) 2265;
        j = (char) 23427;
        e = (char) 59387;
    }

    @Override // o.er.h
    public final /* synthetic */ boolean b() {
        int i2 = f + Opcodes.DDIV;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 18 : 'X') {
            case 18:
                super.b();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                boolean b2 = super.b();
                int i3 = f + 95;
                i = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return b2;
                    default:
                        int i4 = 20 / 0;
                        return b2;
                }
        }
    }

    public p(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        int i2 = i + 7;
        f = i2 % 128;
        int i3 = i2 % 2;
        a[] aVarArr = {this.d.m()};
        int i4 = f + 29;
        i = i4 % 128;
        switch (i4 % 2 != 0 ? '\t' : (char) 2) {
            case 2:
                return aVarArr;
            default:
                throw null;
        }
    }

    public final String c() throws WalletValidationException {
        int i2 = f + 71;
        i = i2 % 128;
        int i3 = i2 % 2;
        String e2 = this.d.m().e();
        int i4 = f + 93;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return e2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0160  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x01ce  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(android.content.Context r17, final fr.antelop.sdk.util.OperationCallback<fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControls> r18) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 582
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.p.a(android.content.Context, fr.antelop.sdk.util.OperationCallback):void");
    }

    /* renamed from: o.er.p$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\p$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            c = 0;
            a = 1;
            int[] iArr = new int[o.es.b.values().length];
            e = iArr;
            try {
                iArr[o.es.b.b.ordinal()] = 1;
                int i = a + 71;
                c = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.es.b.c.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[o.es.b.a.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[o.es.b.d.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[o.es.b.e.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[o.es.b.f.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[o.es.b.j.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
            try {
                e[o.es.b.g.ordinal()] = 8;
                int i3 = c;
                int i4 = ((i3 | 85) << 1) - (i3 ^ 85);
                a = i4 % 128;
                if (i4 % 2 == 0) {
                }
            } catch (NoSuchFieldError e9) {
            }
            try {
                e[o.es.b.i.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
            try {
                e[o.es.b.h.ordinal()] = 10;
            } catch (NoSuchFieldError e11) {
            }
            try {
                e[o.es.b.l.ordinal()] = 11;
            } catch (NoSuchFieldError e12) {
            }
            try {
                e[o.es.b.n.ordinal()] = 12;
                int i5 = a;
                int i6 = (i5 ^ Opcodes.DMUL) + ((i5 & Opcodes.DMUL) << 1);
                c = i6 % 128;
                if (i6 % 2 != 0) {
                }
            } catch (NoSuchFieldError e13) {
            }
            try {
                e[o.es.b.f78o.ordinal()] = 13;
            } catch (NoSuchFieldError e14) {
            }
            try {
                e[o.es.b.m.ordinal()] = 14;
                int i7 = c;
                int i8 = (i7 & 81) + (i7 | 81);
                a = i8 % 128;
                int i9 = i8 % 2;
            } catch (NoSuchFieldError e15) {
            }
            try {
                e[o.es.b.k.ordinal()] = 15;
            } catch (NoSuchFieldError e16) {
            }
            try {
                e[o.es.b.t.ordinal()] = 16;
            } catch (NoSuchFieldError e17) {
            }
            try {
                e[o.es.b.p.ordinal()] = 17;
            } catch (NoSuchFieldError e18) {
            }
            try {
                e[o.es.b.r.ordinal()] = 18;
            } catch (NoSuchFieldError e19) {
            }
            try {
                e[o.es.b.s.ordinal()] = 19;
            } catch (NoSuchFieldError e20) {
            }
            try {
                e[o.es.b.q.ordinal()] = 20;
            } catch (NoSuchFieldError e21) {
            }
            try {
                e[o.es.b.w.ordinal()] = 21;
            } catch (NoSuchFieldError e22) {
            }
            try {
                e[o.es.b.u.ordinal()] = 22;
            } catch (NoSuchFieldError e23) {
            }
            try {
                e[o.es.b.y.ordinal()] = 23;
                int i10 = a;
                int i11 = ((i10 | 65) << 1) - (i10 ^ 65);
                c = i11 % 128;
                int i12 = i11 % 2;
            } catch (NoSuchFieldError e24) {
            }
            try {
                e[o.es.b.x.ordinal()] = 24;
            } catch (NoSuchFieldError e25) {
            }
            try {
                e[o.es.b.v.ordinal()] = 25;
                int i13 = c;
                int i14 = ((i13 | Opcodes.LUSHR) << 1) - (i13 ^ Opcodes.LUSHR);
                a = i14 % 128;
                if (i14 % 2 == 0) {
                }
            } catch (NoSuchFieldError e26) {
            }
            try {
                e[o.es.b.A.ordinal()] = 26;
            } catch (NoSuchFieldError e27) {
            }
            try {
                e[o.es.b.C.ordinal()] = 27;
            } catch (NoSuchFieldError e28) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    final fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControls b(java.util.List<o.es.a<?>> r40, java.util.Date r41, o.eo.e r42) {
        /*
            Method dump skipped, instructions count: 874
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.p.b(java.util.List, java.util.Date, o.eo.e):fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControls");
    }

    private static void g(String str, int i2, Object[] objArr) {
        int i3;
        int i4 = $10 + 59;
        $11 = i4 % 128;
        int i5 = 1;
        int i6 = 0;
        switch (i4 % 2 != 0) {
            case true:
                char[] charArray = str != null ? str.toCharArray() : str;
                o.a.i iVar = new o.a.i();
                char[] cArr = new char[charArray.length];
                iVar.b = 0;
                char[] cArr2 = new char[2];
                int i7 = $11 + 25;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                while (iVar.b < charArray.length) {
                    int i9 = $10 + 59;
                    $11 = i9 % 128;
                    int i10 = i9 % 2;
                    cArr2[i6] = charArray[iVar.b];
                    cArr2[i5] = charArray[iVar.b + i5];
                    int i11 = $11 + Opcodes.DMUL;
                    $10 = i11 % 128;
                    int i12 = i11 % 2;
                    int i13 = 58224;
                    int i14 = i6;
                    while (i14 < 16) {
                        char c = cArr2[i5];
                        char c2 = cArr2[i6];
                        int i15 = (c2 + i13) ^ ((c2 << 4) + ((char) (b ^ 8439748517800462901L)));
                        int i16 = c2 >>> 5;
                        try {
                            Object[] objArr2 = new Object[4];
                            objArr2[3] = Integer.valueOf(j);
                            objArr2[2] = Integer.valueOf(i16);
                            objArr2[i5] = Integer.valueOf(i15);
                            objArr2[i6] = Integer.valueOf(c);
                            Object obj = o.e.a.s.get(-1512468642);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((TypedValue.complexToFloat(i6) > 0.0f ? 1 : (TypedValue.complexToFloat(i6) == 0.0f ? 0 : -1)) + 11, (char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 603 - (ViewConfiguration.getTouchSlop() >> 8));
                                Class<?>[] clsArr = new Class[4];
                                clsArr[i6] = Integer.TYPE;
                                clsArr[i5] = Integer.TYPE;
                                clsArr[2] = Integer.TYPE;
                                clsArr[3] = Integer.TYPE;
                                obj = cls.getMethod("C", clsArr);
                                o.e.a.s.put(-1512468642, obj);
                            }
                            char charValue = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            cArr2[i5] = charValue;
                            try {
                                Object[] objArr3 = {Integer.valueOf(cArr2[i6]), Integer.valueOf((charValue + i13) ^ ((charValue << 4) + ((char) (e ^ 8439748517800462901L)))), Integer.valueOf(charValue >>> 5), Integer.valueOf(a)};
                                Object obj2 = o.e.a.s.get(-1512468642);
                                if (obj2 == null) {
                                    obj2 = ((Class) o.e.a.c((-16777205) - Color.rgb(0, 0, 0), (char) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), ExpandableListView.getPackedPositionGroup(0L) + 603)).getMethod("C", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-1512468642, obj2);
                                }
                                cArr2[0] = ((Character) ((Method) obj2).invoke(null, objArr3)).charValue();
                                i13 -= 40503;
                                i14++;
                                int i17 = $10 + 21;
                                $11 = i17 % 128;
                                switch (i17 % 2 == 0) {
                                    case true:
                                    default:
                                        i5 = 1;
                                        i6 = 0;
                                }
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    cArr[iVar.b] = cArr2[0];
                    cArr[iVar.b + 1] = cArr2[1];
                    try {
                        Object[] objArr4 = {iVar, iVar};
                        Object obj3 = o.e.a.s.get(2062727845);
                        if (obj3 != null) {
                            i3 = 1;
                        } else {
                            i3 = 1;
                            obj3 = ((Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 11, (char) (30724 - ExpandableListView.getPackedPositionChild(0L)), (ViewConfiguration.getPressedStateDuration() >> 16) + 614)).getMethod("A", Object.class, Object.class);
                            o.e.a.s.put(2062727845, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr4);
                        i5 = i3;
                        i6 = 0;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr[0] = new String(cArr, 0, i2);
                return;
            default:
                Object obj4 = null;
                obj4.hashCode();
                throw null;
        }
    }
}
