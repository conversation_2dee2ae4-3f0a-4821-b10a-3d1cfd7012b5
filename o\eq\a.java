package o.eq;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.google.android.gms.tapandpay.TapAndPay;
import com.google.android.gms.tapandpay.TapAndPayClient;
import com.google.android.gms.tapandpay.TapAndPayStatusCodes;
import com.google.android.gms.tapandpay.issuer.TokenInfo;
import com.google.android.gms.tapandpay.issuer.ViewTokenRequest;
import com.google.android.gms.tasks.OnCanceledListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.SuccessContinuation;
import com.google.android.gms.tasks.Task;
import fr.antelop.sdk.AntelopErrorCode;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import o.an.c;
import o.ee.g;
import o.ee.h;
import o.ee.i;
import o.ep.a;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eq\a.smali */
public final class a implements o.ep.c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] g;
    private static final a i;
    private static int k;
    private static long l;

    /* renamed from: o, reason: collision with root package name */
    private static int f76o;
    private a.b d;
    private TapAndPayClient b = null;
    private String c = null;
    private String a = null;
    private final AtomicBoolean h = new AtomicBoolean(false);
    private final AtomicBoolean j = new AtomicBoolean(false);
    private final List<e> f = new ArrayList();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eq\a$e.smali */
    interface e {
        void onDone();
    }

    static void a() {
        char[] cArr = new char[1619];
        ByteBuffer.wrap(",\u008epP\u0095J:L_}üb\u0001]¦\u0012Ë\u0000h8\u008d4Ò7w-\u0094Ò9É^êãÄ\u0000ï¥é\u0086§ÚV?K\u0090Xõ>V)¬\u000fðñ\u0015ëºíßÜ|Ã\u0081Ó&¢K¹è·\r\u008bR\u008e÷\u008f\u0014}¹yÞlc{~-\"ÌÇÕhÏ\râ®âSóôÍ\u0099Æ,¹pJ\u0095V:C_Rüf\u0001\u007f¦\u0017ËQhF\u000b¶WE²Y\u001dLx]Ûi&p\u0081\u0018ì^OIªzõyPn³Ù\u001eÑyØÄÏ'æ\u0082ãíðH\u0087«Èö\u0083Q²¼²\u001fà{[ÆW!Z\u008cqïdJe\u0095rð\tS8¾\u0001\u0019-d=Ç.\"È\u008dõèÁKö\u0096øñì\\\u0099¿\u0091\u001a\u0087eÆÀ±#¸\u008e¡ê\u001e5Z\u0090Góm^c¹i\u0004\bg\u0011Â\n,\u009dpp\u0095n:n__üX\u0001]¦!Ë6h9\u008d\u001cÒ\u001fw\u0004\u0094å9â^îãè\u0000Ì¥ÑÊÎo£\u008c¤Ñ¬v\u0081\u009b\u009d,\u009dpp\u0095n:n__üX\u0001]¦!Ë6h9\u008d\u001cÒ\u001fw\u0004\u0094å9â^õãà\u0000Ì¥Ä,\u009dpp\u0095n:n__üX\u0001]¦!Ë6h9\u008d\u001cÒ\u001fw\u0004\u0094å9â^çãà\u0000Ì¥ÆÊÄo§\u008c¢Ñ¿V(\nåïô@ç%Õ\u0086Ö{ÚÜ³±¨\u0012¿÷Á¨¹\r\u0094îoC|$r\u0099'z\u000b1'mÔ\u0088È'ÝBÛáö\u001cø»\u0088Ö\u0089u\u0098\u0090±Ï j÷\u0089\u0000Ò¹\u008eJkVÄC¡E\u0002hÿfX\u00165\u0017\u0096\u0006s/,>\u0089ij\u009eÇ\u009d \u008e\u001d\u0089þü[ä4ù\u0091\u0095r©/\u0088\u0088§e®Æ ¢G\u001fPø\u0001U-6=ô~¨\u008aM\u0083â\u009b\u0087º$½Ù¼~Ó\u0013Ú°ÔUó\nä¯êL\u000eá\b\u0086\u0004;\tØ.}#\u0012<·DTA\t],\u008ap~\u0095w:o_NüI\u0001H¦'Ë.h \u008d\u0007Ò\u0010w\u001e\u0094á9ô^ðãè\u0004\tXý½ô\u0012ìwÍÔÊ)Ë\u008e¤ã\u00ad@£¥\u0084ú\u0093_\u009d¼p\u0011wvsËi(S\u008dPâMG ên¶\u009dS\u0081ü\u0094\u0099\u0092:¿Ç±`Á\rÀ®ÑKø\u0014é±¾RIÿJ\u0098Y%^Æ\u001cc3\f,©gJ^\u0017^°T]oþa\u009a¡'\u0080À\u009fm¥\u000e¤« tð\u0011Ä²Û_Ïøò\u0085Ä&õÃ\u000fl\u000b\t\u0016ª1,¹pJ\u0095V:C_Eüh\u0001f¦\u0016Ë\u0017h\u0006\u008d/Ò>wi\u0094\u009e9\u009d^\u008eã\u0089\u0000Ë¥äÊûo°\u008c\u0089Ñ\u0089v\u0083\u009b¸8¶\\\u001báW\u0006H«dÈimW²f×\u0014t\u0000\u0099\u0005>\"Cià\"\u0005ÝªêÏÚlö±øÖä{\u0084\u0098\u008e=ÃBÄçÿ\u0004\u008b©¤Í\u0011\u0012J·LÔgyz\u009eg#\u001c@\u0015å\u0006\nw¯)Ì,\u0011\"¶ÚÛËxâ\u009dÿ\"áGâäÓ\t\u0089®\u009aó¦\u0010³µáÙ^~S\u0083W FE?êb\u000fd¬\u001eñ\u0000\u0016\u0001»6Ø\t}.\u0082Ì,¹pJ\u0095V:C_Eüh\u0001f¦\u0016Ë\u0017h\u0006\u008d/Ò>wi\u0094\u009e9\u009d^\u008eã\u0089\u0000Ë¥äÊûo°\u008c\u0089Ñ\u0089v\u0083\u009b¸8¶\\\u001báW\u0006H«dÈimW²f×\u0014t\u0000\u0099\u0005>\"Cià\"\u0005ÝªêÏÚlö±øÖä{\u0084\u0098\u008e=ÃBÄçÿ\u0004\u0088©ªÍE\u0012D·EÔzyw\u009eh#U@\u000få\u000e\n<¯8Ì-\u0011\u0000¶ÑÛÃxÄ\u009d±\"îGãäÓ\t\u008c®\u0081ó¡\u0010´µªÙR~S\u0083J SEzêa\u000f+¬\u0002ñ\u0013\u0016\f»'Ø<ã\u009d¿nZrõg\u0090a3LÎBi2\u00043§\"B\u000b\u001d\u001a¸M[ºö¹\u0091ª,\u00adÏïjÀ\u0005ß \u0094C\u00ad\u001e\u00ad¹§T\u009c÷\u0092\u0093?.sÉld@\u0007M¢s}B\u00180»$V!ñ\u0006\u008cM/\u0006ÊùeÎ\u0000þ£Ò~Ü\u0019À´ Wªòç\u008dà(ÛËµf\u008e\u0002eÝBxg\u001bS¶mQJì(\u008fq*5Å\u0006`\n\u0003\u000fÞ9yô\u0014ê·êRÛíÊ\u0088Ó+²Æ¹Ë^\u0097\u00adr±Ý¤¸¢\u001b\u008fæ\u0081Añ,ð\u008fájÈ5Ù\u0090\u008esyÞz¹i\u0004nç,B\u0003-\u001c\u0088Wkn6n\u0091d|_ßQ»ü\u0006°á¯L\u0083/\u008e\u008a°U\u00810ó\u0093ç~âÙÅ¤\u008e\u0007Åâ:M\r(=\u008b\u0011V\u001f1\u0003\u009cc\u007fiÚ$¥#\u0000\u0018ãoNM*¢õ£P¢3\u009d\u009e\u0090y\u008fÄ²§è\u0002éíÛHß+ÊöçQ6<$\u009f#zVÅ\t \u0004\u00034î<I}\u0014\\÷JRC>¢\u0099³d¢Ç§¢\u009d\r\u0086èÎK¶\u0016óñþ\\Õ?Ê\u009aÍe~À|£%\u000e\u0011é\u0016´\b\u0017NòL]c8|\u009bwfNÁN\u00ad\u0084\b¿ë±¶Ü\u0011\u0088ü\u0089_û:ÿ\u0085ê`çÃÂ®Ç,ªpM\u0095@:J_eüb\u0001Z¦\u0012Ë\u0015h\u0003\u008d0Ò/wi\u0094\u009e,ªpM\u0095@:J_eüb\u0001Z¦\u0012Ë\u0015h\u0003\u008d0Ò/wi\u0094\u009e9\u009d^\u008eã\u0089\u0000þ¥æÊÿo\u0098\u008c\u0091Ñ\u0084v§\u009b 8\u009d\\PáH\u0006T«{Èim@²h×\u0013t\t\u0099\t>0C$à&\u0005\u0093ªÐÏÜlµ±éÖä{\u0086\u0098\u0088=\u008aB\u009bçº\u0004¡\u008b\u008d×m2f\u009d]øU[t¦_\u0001\"l/Ï-*\u000eu\u0018Ð53á\u009eøùðD¶§\u0081,\u00adpZ\u0095I:N_eüb\u0001N¦\u0012Ë\u000bh\u000b\u008d}Òr,ºpW\u0095J:\\_Rüf\u0001\u007f¦\u0017ËQhF,ºpW\u0095J:\\_Rüf\u0001\u007f¦\u0017ËQhF\u008duÒvwa\u0094á9Ô^ÆãÞ\u0000¿¥ÑÊäo\u009a\u008c\u0082Ñ\u0083vó\u009bô8ï\\váZ\u0006O«tÈxmo²e×\u001at\u0001ñA\u00ad¬H±ç§\u0082©!\u009dÜ\u0084{ì\u0016ªµ½P\u008e\u000f\u008dª\u009aI\u001aä/\u0083=>%ÝDx*\u0017\u001f²aQy\fx«\bF\u000få\u0014\u0081\u0088<¡Û³v\u0080\u0015\u0093°\u008ao\u0097,ºpW\u0095J:\\_Rüf\u0001\u007f¦\u0017ËQhF\u008duÒvwa\u0094á9Ô^ÆãÞ\u0000¿¥ÑÊäo\u009a\u008c\u0082Ñ\u0083vó\u009bô8ï\\fá^\u0006O«sÈ=mj²g×\u000bt\u0000\u0099\u0005>%Cgà(\u0005ÁªËÏÀlç\\\u0017\u0000äåøJí/ë\u008cÆqÈÖ¸»¹\u0018¨ý\u0081¢\u0090\u0007ÏäzIr.c\u0093dpTÕGº@\u001f;üi¡n\u0006]ë\u0013H\u0000,ï\u0091ôv¯Û\u0083¸\u0093\u001d\u0088ÂÔ,¬pG\u0095Q:Y_püX\u0001d¦\u0000Ë\nh\u001a\u008d0Ò)w\u001e\u0094Ã9Ò^ÈãÌ\u0000ñ¥ÚÊâo\u0095ô\n¨ùMåâð\u0087á$ÕÙÌ~¤\u0013ê°ñUÆ\n\u0087¯\u009cLEám\u0086d;sØZ}_\u0012L·;T\u0006\t;®\u0013C\u001fà\u0010\u0084ò9¨Þ¿s\u0084\u0010ûµÞjÛ\u000f®¬ºA½æÂ\u009b\u00808\u0091Ý rl\u0017y´RiK\u000eZ£d@'å#\u009a)?\u0019Ü\u0013q\n\u0015¢Êàoñ\fË¡ÏFÒûæ\u0098¡=¶ÒÄw\u0088\u0014\u0082É\u0095na\u00036 jEGúG\u009fK<,Ñ>,¹pJ\u0095V:C_Rüf\u0001\u007f¦\u0017ËYhB\u008duÒ4w/\u0094ö9Þ^×ãÀ\u0000é¥ìÊÿo\u0088\u008cµÑ\u0088v \u009b¬8£\\Aá\u001b\u0006\f«7Èiml²b×\u001at\u000b\u0099\">5C}àm,¹pJ\u0095V:C_Rüf\u0001\u007f¦\u0017ËYhB\u008duÒ4w/\u0094ö9Þ^×ãÀ\u0000é¥ìÊÿo\u0088\u008cµÑ\u0088v \u009b¬8£\\Aá\u001b\u0006\f«7ÈHmm²b×\u0011t\n\u0099\u001c>?Cgà(\u0005ÁªËÏÀlç±»Öó{\u0092\u0098\u0089=\u0096B\u009bç±\u0004 ©¯Í\u0011\u0012A·_Ô|yt\u009e/#\u0012@\u0014å\u000e\n0¯1Ì&\u0011i¶ÏÛÄxÒ,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w\u000e\u0094Ù9\u009d^ÅãÈ\u0000ö¥éÊþo\u0083\u008c\u0082Ñ×vó,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w\u000e\u0094Ù9\u009d^ÅãÈ\u0000ö¥éÊþo\u0083\u008c\u0082ÑÍvþ\u009bù8\u0081\\ZáO\u0006r«bÈmms²f×\rt\u0011\u0099\u000e>5C\u0005à4\u0005÷ªÜÏÙlü±øÖäC±\u001fPúIUS0~\u0093~noÉQ¤Z\u0007Eâr½q\u0018\u0004ûÓV\u00971Ï\u008cÂoüÊã¥ô\u0000\u0089ã\u0088¾Ç\u0019ôôóW\u008b3P\u008eEihÄr§y\u0002oÝj¸\u0012\u001b\u001aö\u0013Q>,),»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w-\u0094Þ9Î^×ãý\u0000ð¥îÊîo\u009f\u008c\u0094,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w\u0005\u0094Ø9Ó^Æã\u0089\u0000è¥ìÊÿo\u0099\u008cÇÑ¾v¦\u009bº8¬\\PáH\u0006R,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w&\u0094Ò9É^æãÇ\u0000é¥ìÊùo\u009e\u008c\u0089Ñ\u0080v¶\u009b·8»\\\u0015áT\u0006O«DÈhm`²j×\u001at\u0016\u0099\u0018>kCgã\u001f¿þZçõý\u0090Ð3ÐÎÁiÿ\u0004ô§ëBÜ\u001dß¸\u0082[vöm\u0091T,yÏZjC\u0005C 0C\u000b\u001e(¹\u0005T\u0019÷\u001c\u0093ð.íÉàdú\u0007Ý¢\u0087}\u0080\u0018û»¨V«ñÏ\u008cÃ¡Dý¥\u0018¼·¦Ò\u008bq\u008b\u008c\u009a+¤F¯å°\u0000\u0087_\u0084úÙ\u0019-´6Ó\u000fn\"\u008d\u0001(\u0018G\u0018âk\u0001P\\sû^\u0016BµGÑ«l¶\u008b»&¡E\u0086àÜ?ÛZ ùó\u0014ð³\u008eÎÌmÝ\u0088#'fB<á\u0005<\n[\u0019,ºpK\u0095D:I_}üb\u0001E¦\u0012Ë\u000bh\u000b\u008d\"Ò:w3\u0094Ò9ô^Çã\u0089\u0000ö¥áÊ«o\u0085\u008c\u0088Ñ\u0082vó\u009bµ8 \\[á\\,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w&\u0094Ò9É^âãÊ\u0000ë¥ìÊýo\u0094\u008c°Ñ\u008cv¿\u009bµ8ª\\Aár\u0006E«7È0m#²^×\u001et\t\u0099\u0007>4C3àm\u0005ÚªÝÏ\u0095lµ,»pZ\u0095C:Y_tüt\u0001e¦[ËPhO\u008dxÒ{w&\u0094Ò9É^âãÊ\u0000ë¥ìÊýo\u0094\u008c°Ñ\u008cv¿\u009bµ8ª\\Aár\u0006E«7È0m#²^×\u001et\t\u0099\u0007>4C3àm\u0005ÚªÝÏ\u008flá±ôÖî{×\u0098\u0091=\u008cB\u0087ç¸,\u009ep^\u0095I:G_tüs\u0001-¦\u001aË\u001dhO\u008d!Ò4w.\u0094\u00979Ñ^ÌãÇ\u0000øÉø\u0095;p0ß\u000fº\u001a\u0019\u0006ä8Cw.e\u008d*h\u001d7\u001e\u0092Kq¼Ü\u009c»§\u0006¸å\u009b@£/\u0086\u008aõiì4ï\u0093Ó~ØÝ\u0082¹y".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1619);
        g = cArr;
        l = -350761489023274945L;
    }

    static void init$0() {
        $$a = new byte[]{31, 57, -118, -60};
        $$b = Opcodes.IFNE;
    }

    private static void n(int i2, byte b, int i3, Object[] objArr) {
        byte[] bArr = $$a;
        int i4 = 1 - (i3 * 3);
        int i5 = i2 + 4;
        int i6 = 105 - b;
        byte[] bArr2 = new byte[i4];
        int i7 = -1;
        int i8 = i4 - 1;
        if (bArr == null) {
            i6 += i8;
            i8 = i8;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = -1;
        }
        while (true) {
            i5++;
            int i9 = i7 + 1;
            bArr2[i9] = (byte) i6;
            if (i9 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            Object[] objArr2 = objArr;
            int i10 = i8;
            byte[] bArr3 = bArr2;
            byte[] bArr4 = bArr;
            i6 += bArr[i5];
            i8 = i10;
            objArr = objArr2;
            bArr = bArr4;
            bArr2 = bArr3;
            i7 = i9;
        }
    }

    @Override // o.ep.a
    public final /* bridge */ /* synthetic */ void e(Activity activity, a.c cVar, i iVar, o.eo.e eVar, o.ep.e eVar2, c.e eVar3, h hVar) {
        int i2 = f76o + 85;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                e2(activity, cVar, iVar, eVar, eVar2, eVar3, hVar);
                throw null;
            default:
                e2(activity, cVar, iVar, eVar, eVar2, eVar3, hVar);
                int i3 = k + 85;
                f76o = i3 % 128;
                int i4 = i3 % 2;
                return;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f76o = 0;
        k = 1;
        a();
        ViewConfiguration.getDoubleTapTimeout();
        Process.getThreadPriority(0);
        View.MeasureSpec.getSize(0);
        TextUtils.getOffsetBefore("", 0);
        MotionEvent.axisFromString("");
        TextUtils.lastIndexOf("", '0', 0);
        ViewConfiguration.getLongPressTimeout();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        TextUtils.indexOf("", "");
        ViewConfiguration.getScrollBarSize();
        TextUtils.indexOf((CharSequence) "", '0');
        Color.green(0);
        View.MeasureSpec.makeMeasureSpec(0, 0);
        KeyEvent.keyCodeFromString("");
        SystemClock.elapsedRealtime();
        AudioTrack.getMaxVolume();
        SystemClock.elapsedRealtimeNanos();
        Process.getThreadPriority(0);
        ExpandableListView.getPackedPositionForChild(0, 0);
        Color.red(0);
        TextUtils.getTrimmedLength("");
        i = new a();
        int i2 = k + 5;
        f76o = i2 % 128;
        int i3 = i2 % 2;
    }

    private a() {
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x002c, code lost:
    
        if (o.eq.a.i.h.get() == false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0026, code lost:
    
        if (o.eq.a.i.h.get() == false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static o.eq.a b(android.content.Context r3) {
        /*
            int r0 = o.eq.a.f76o
            int r0 = r0 + 29
            int r1 = r0 % 128
            o.eq.a.k = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 77
            goto L11
        Lf:
            r0 = 42
        L11:
            r1 = 0
            switch(r0) {
                case 42: goto L1e;
                default: goto L15;
            }
        L15:
            o.eq.a r0 = o.eq.a.i
            java.util.concurrent.atomic.AtomicBoolean r0 = r0.h
            boolean r0 = r0.get()
            goto L29
        L1e:
            o.eq.a r0 = o.eq.a.i
            java.util.concurrent.atomic.AtomicBoolean r0 = r0.h
            boolean r0 = r0.get()
            if (r0 != 0) goto L42
        L28:
            goto L2f
        L29:
            r2 = 15
            int r2 = r2 / r1
            if (r0 != 0) goto L42
            goto L28
        L2f:
            o.eq.a r0 = o.eq.a.i
            java.util.concurrent.atomic.AtomicBoolean r2 = r0.j
            boolean r2 = r2.get()
            if (r2 != 0) goto L3a
            r1 = 1
        L3a:
            switch(r1) {
                case 1: goto L3e;
                default: goto L3d;
            }
        L3d:
            goto L42
        L3e:
            r0.c(r3)
        L42:
            o.eq.a r3 = o.eq.a.i
            int r0 = o.eq.a.k
            int r0 = r0 + 53
            int r1 = r0 % 128
            o.eq.a.f76o = r1
            int r0 = r0 % 2
            return r3
        L4f:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.b(android.content.Context):o.eq.a");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void a(Context context) {
        int i2 = k + 45;
        f76o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (-1) - TextUtils.lastIndexOf("", '0'), 19 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (58725 - (Process.myTid() >> 22)), 1593 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 26, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        d(context);
        int i4 = f76o + 9;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    private void c(final Context context) {
        int i2 = f76o + Opcodes.LSUB;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m((char) ((-1) - TextUtils.lastIndexOf("", '0')), Color.alpha(0), 19 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 43527), 19 - (KeyEvent.getMaxKeyCode() >> 16), TextUtils.indexOf("", "", 0) + 6, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.d = a.b.b;
        this.b = TapAndPay.getClient(context);
        Object[] objArr3 = new Object[1];
        m((char) (32896 - TextUtils.indexOf((CharSequence) "", '0')), 25 - View.MeasureSpec.getSize(0), TextUtils.getTrimmedLength("") + 17, objArr3);
        d(context, ((String) objArr3[0]).intern());
        this.b.registerDataChangedListener(new TapAndPay.DataChangedListener() { // from class: o.eq.a$$ExternalSyntheticLambda9
            @Override // com.google.android.gms.tapandpay.TapAndPay.DataChangedListener
            public final void onDataChanged() {
                a.this.a(context);
            }
        });
        d(context);
        int i4 = f76o + 89;
        k = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Task d(String str) throws Exception {
        g.c();
        Object[] objArr = new Object[1];
        m((char) Color.argb(0, 0, 0, 0), ViewConfiguration.getKeyRepeatDelay() >> 16, 18 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 1481, View.resolveSizeAndState(0, 0, 0) + 43, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        if (str.length() > 24) {
            g.c();
            Object[] objArr3 = new Object[1];
            m((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), View.resolveSizeAndState(0, 0, 0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 18, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            m((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), Color.green(0) + 1524, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 49, objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            m((char) TextUtils.indexOf("", ""), 1574 - (Process.myTid() >> 22), ((byte) KeyEvent.getModifierMetaStateMask()) + 19, objArr5);
            throw new RuntimeException(((String) objArr5[0]).intern());
        }
        this.c = str;
        this.d = a.b.c;
        Task<String> stableHardwareId = this.b.getStableHardwareId();
        int i2 = f76o + 63;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return stableHardwareId;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Task c(String str) throws Exception {
        g.c();
        Object[] objArr = new Object[1];
        m((char) Color.green(0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), AndroidCharacter.getMirror('0') - 29, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 53156), 1370 - TextUtils.getCapsMode("", 0, 0), (-16777178) - Color.rgb(0, 0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        if (str.length() > 24) {
            g.c();
            Object[] objArr3 = new Object[1];
            m((char) TextUtils.getOffsetBefore("", 0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 18 - Process.getGidForName(""), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            m((char) (TextUtils.getCapsMode("", 0, 0) + 36351), 1408 - (ViewConfiguration.getWindowTouchSlop() >> 8), 44 - ImageFormat.getBitsPerPixel(0), objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            m((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 1452, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 28, objArr5);
            throw new RuntimeException(((String) objArr5[0]).intern());
        }
        this.a = str;
        Task<String> environment = this.b.getEnvironment();
        int i2 = k + 93;
        f76o = i2 % 128;
        int i3 = i2 % 2;
        return environment;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ Task b(String str) throws Exception {
        g.c();
        Object[] objArr = new Object[1];
        m((char) TextUtils.indexOf("", "", 0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), Color.green(0) + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 1332, 38 - KeyEvent.getDeadChar(0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        Task<List<TokenInfo>> listTokens = this.b.listTokens();
        int i2 = f76o + 9;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return listTokens;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void d(android.content.Context r14, java.util.List r15) {
        /*
            Method dump skipped, instructions count: 524
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.d(android.content.Context, java.util.List):void");
    }

    private void d(final Context context) {
        int i2 = k + Opcodes.DDIV;
        f76o = i2 % 128;
        int i3 = i2 % 2;
        if (this.b == null) {
            return;
        }
        g.c();
        Object[] objArr = new Object[1];
        m((char) Gravity.getAbsoluteGravity(0, 0), ViewConfiguration.getJumpTapTimeout() >> 16, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (21142 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 42, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 10, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.j.set(true);
        this.b.getActiveWalletId().onSuccessTask(new SuccessContinuation() { // from class: o.eq.a$$ExternalSyntheticLambda15
            @Override // com.google.android.gms.tasks.SuccessContinuation
            public final Task then(Object obj) {
                Task d;
                d = a.this.d((String) obj);
                return d;
            }
        }).onSuccessTask(new SuccessContinuation() { // from class: o.eq.a$$ExternalSyntheticLambda1
            @Override // com.google.android.gms.tasks.SuccessContinuation
            public final Task then(Object obj) {
                Task c;
                c = a.this.c((String) obj);
                return c;
            }
        }).onSuccessTask(new SuccessContinuation() { // from class: o.eq.a$$ExternalSyntheticLambda2
            @Override // com.google.android.gms.tasks.SuccessContinuation
            public final Task then(Object obj) {
                Task b;
                b = a.this.b((String) obj);
                return b;
            }
        }).addOnSuccessListener(new OnSuccessListener() { // from class: o.eq.a$$ExternalSyntheticLambda3
            @Override // com.google.android.gms.tasks.OnSuccessListener
            public final void onSuccess(Object obj) {
                a.this.d(context, (List) obj);
            }
        }).addOnFailureListener(new OnFailureListener() { // from class: o.eq.a$$ExternalSyntheticLambda4
            @Override // com.google.android.gms.tasks.OnFailureListener
            public final void onFailure(Exception exc) {
                a.this.e(context, exc);
            }
        });
        int i4 = f76o + 31;
        k = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 22 : '\n') {
            case 22:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void e(Context context, Exception exc) {
        int i2 = f76o + 109;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m((char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), Gravity.getAbsoluteGravity(0, 0), 19 - View.resolveSizeAndState(0, 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1175, 24 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr2);
        g.a(intern, ((String) objArr2[0]).intern(), exc);
        switch (exc instanceof ApiException) {
            case false:
                int i4 = k + 25;
                f76o = i4 % 128;
                int i5 = i4 % 2;
                this.d = a.b.b;
                break;
            default:
                switch (((ApiException) exc).getStatusCode()) {
                    case TapAndPayStatusCodes.TAP_AND_PAY_ATTESTATION_ERROR /* 15005 */:
                    case TapAndPayStatusCodes.TAP_AND_PAY_UNAVAILABLE /* 15009 */:
                        g.c();
                        Object[] objArr3 = new Object[1];
                        m((char) View.resolveSize(0, 0), (-1) - TextUtils.lastIndexOf("", '0'), TextUtils.indexOf("", "", 0, 0) + 19, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        m((char) TextUtils.getTrimmedLength(""), 1199 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), Color.red(0) + 45, objArr4);
                        g.d(intern2, ((String) objArr4[0]).intern());
                        this.d = a.b.b;
                        break;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        m((char) (ViewConfiguration.getPressedStateDuration() >> 16), ViewConfiguration.getScrollDefaultDelay() >> 16, 20 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        m((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 28426), 1242 - ImageFormat.getBitsPerPixel(0), 39 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        this.d = a.b.a;
                        break;
                }
        }
        Object obj = null;
        this.c = null;
        this.a = null;
        List<o.ep.e> list = e;
        Object[] objArr7 = new Object[1];
        m((char) (32898 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 26 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 17 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr7);
        a(context, list, ((String) objArr7[0]).intern());
        this.h.set(true);
        this.j.set(false);
        Iterator<e> it = this.f.iterator();
        while (it.hasNext()) {
            it.next().onDone();
        }
        this.f.clear();
        int i6 = f76o + 77;
        k = i6 % 128;
        switch (i6 % 2 == 0 ? 'F' : 'G') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void g(a.InterfaceC0042a interfaceC0042a) {
        int i2 = k;
        int i3 = i2 + 33;
        f76o = i3 % 128;
        int i4 = i3 % 2;
        a.b bVar = this.d;
        switch (bVar != null ? 'S' : '^') {
            case Opcodes.AASTORE /* 83 */:
                int i5 = i2 + 109;
                f76o = i5 % 128;
                boolean z = i5 % 2 != 0;
                interfaceC0042a.e((a.InterfaceC0042a) bVar);
                switch (z) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            default:
                return;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x003e A[PHI: r0
  0x003e: PHI (r0v5 o.eq.a$e) = (r0v3 o.eq.a$e), (r0v7 o.eq.a$e) binds: [B:39:0x003a, B:8:0x0023] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // o.ep.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(final o.ep.a.InterfaceC0042a<o.ep.a.b> r5) {
        /*
            r4 = this;
            int r0 = o.eq.a.k
            int r0 = r0 + 113
            int r1 = r0 % 128
            o.eq.a.f76o = r1
            int r0 = r0 % 2
            r1 = 55
            r2 = 0
            if (r0 == 0) goto L29
            o.eq.a$$ExternalSyntheticLambda6 r0 = new o.eq.a$$ExternalSyntheticLambda6
            r0.<init>()
            java.util.concurrent.atomic.AtomicBoolean r5 = r4.h
            boolean r5 = r5.get()
            r3 = 24
            int r3 = r3 / r2
            if (r5 == 0) goto L21
            r5 = 7
            goto L23
        L21:
            r5 = 53
        L23:
            switch(r5) {
                case 53: goto L3d;
                default: goto L26;
            }
        L26:
            goto L3e
        L27:
            r5 = move-exception
            throw r5
        L29:
            o.eq.a$$ExternalSyntheticLambda6 r0 = new o.eq.a$$ExternalSyntheticLambda6
            r0.<init>()
            java.util.concurrent.atomic.AtomicBoolean r5 = r4.h
            boolean r5 = r5.get()
            if (r5 == 0) goto L38
            r5 = r1
            goto L3a
        L38:
            r5 = 79
        L3a:
            switch(r5) {
                case 55: goto L3e;
                default: goto L3d;
            }
        L3d:
            goto L72
        L3e:
            java.util.concurrent.atomic.AtomicBoolean r5 = r4.j
            boolean r5 = r5.get()
            if (r5 != 0) goto L48
            r2 = 1
            goto L49
        L48:
        L49:
            switch(r2) {
                case 1: goto L4d;
                default: goto L4c;
            }
        L4c:
            goto L3d
        L4d:
            int r5 = o.eq.a.f76o
            int r5 = r5 + 81
            int r2 = r5 % 128
            o.eq.a.k = r2
            int r5 = r5 % 2
            r2 = 0
            if (r5 == 0) goto L6c
            r0.onDone()
            int r5 = o.eq.a.f76o
            int r5 = r5 + r1
            int r0 = r5 % 128
            o.eq.a.k = r0
            int r5 = r5 % 2
            if (r5 == 0) goto L69
            return
        L69:
            throw r2     // Catch: java.lang.Throwable -> L6a
        L6a:
            r5 = move-exception
            throw r5
        L6c:
            r0.onDone()
            throw r2     // Catch: java.lang.Throwable -> L70
        L70:
            r5 = move-exception
            throw r5
        L72:
            java.util.List<o.eq.a$e> r5 = r4.f
            r5.add(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.e(o.ep.a$a):void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void i(a.InterfaceC0042a interfaceC0042a) {
        int i2 = f76o + Opcodes.LSUB;
        k = i2 % 128;
        if (i2 % 2 == 0) {
            a.b bVar = a.b.c;
            throw null;
        }
        switch (this.d == a.b.c ? '1' : 'K') {
            case '1':
                String str = this.c;
                switch (str != null ? (char) 29 : '3') {
                    case 29:
                        int i3 = k + 19;
                        f76o = i3 % 128;
                        int i4 = i3 % 2;
                        interfaceC0042a.e((a.InterfaceC0042a) str);
                        return;
                    default:
                        return;
                }
            default:
                interfaceC0042a.e(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
                return;
        }
    }

    @Override // o.ep.a
    public final void b(final a.InterfaceC0042a<String> interfaceC0042a) {
        int i2 = f76o + Opcodes.DSUB;
        k = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case false:
                new e() { // from class: o.eq.a$$ExternalSyntheticLambda0
                    @Override // o.eq.a.e
                    public final void onDone() {
                        a.this.i(interfaceC0042a);
                    }
                };
                this.h.get();
                obj.hashCode();
                throw null;
            default:
                e eVar = new e() { // from class: o.eq.a$$ExternalSyntheticLambda0
                    @Override // o.eq.a.e
                    public final void onDone() {
                        a.this.i(interfaceC0042a);
                    }
                };
                if (this.h.get()) {
                    switch (!this.j.get() ? '3' : '\t') {
                        case '\t':
                            break;
                        default:
                            int i3 = f76o + Opcodes.LMUL;
                            k = i3 % 128;
                            if (i3 % 2 == 0) {
                                eVar.onDone();
                                obj.hashCode();
                                throw null;
                            }
                            eVar.onDone();
                            int i4 = f76o + 77;
                            k = i4 % 128;
                            if (i4 % 2 != 0) {
                                return;
                            }
                            obj.hashCode();
                            throw null;
                    }
                }
                this.f.add(eVar);
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Failed to find 'out' block for switch in B:24:0x003e. Please report as an issue. */
    public /* synthetic */ void j(a.InterfaceC0042a interfaceC0042a) {
        int i2 = f76o + 83;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                if (this.d == a.b.c) {
                    String str = this.a;
                    if (str != null) {
                        interfaceC0042a.e((a.InterfaceC0042a) str);
                        return;
                    }
                } else {
                    interfaceC0042a.e(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
                    int i3 = f76o + 19;
                    k = i3 % 128;
                    switch (i3 % 2 == 0 ? '=' : 'N') {
                    }
                }
                int i4 = f76o + 47;
                k = i4 % 128;
                int i5 = i4 % 2;
                return;
            default:
                a.b bVar = a.b.c;
                throw null;
        }
    }

    @Override // o.ep.a
    public final void a(final a.InterfaceC0042a<String> interfaceC0042a) {
        e eVar = new e() { // from class: o.eq.a$$ExternalSyntheticLambda14
            @Override // o.eq.a.e
            public final void onDone() {
                a.this.j(interfaceC0042a);
            }
        };
        switch (!this.h.get()) {
            case false:
                int i2 = k + Opcodes.LSHL;
                f76o = i2 % 128;
                int i3 = i2 % 2;
                switch (this.j.get() ? false : true) {
                    case true:
                        int i4 = f76o + 71;
                        k = i4 % 128;
                        Object obj = null;
                        if (i4 % 2 == 0) {
                            eVar.onDone();
                            obj.hashCode();
                            throw null;
                        }
                        eVar.onDone();
                        int i5 = k + 1;
                        f76o = i5 % 128;
                        if (i5 % 2 == 0) {
                            return;
                        } else {
                            throw null;
                        }
                }
        }
        this.f.add(eVar);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void c(a.InterfaceC0042a interfaceC0042a) {
        int i2 = k + 55;
        f76o = i2 % 128;
        int i3 = i2 % 2;
        switch (this.d == a.b.c ? 'V' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                interfaceC0042a.e(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
                int i4 = f76o + Opcodes.DMUL;
                k = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        int i5 = 76 / 0;
                        return;
                    default:
                        return;
                }
            default:
                interfaceC0042a.e((a.InterfaceC0042a) e);
                return;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:9:0x003c A[PHI: r0
  0x003c: PHI (r0v5 o.eq.a$e) = (r0v3 o.eq.a$e), (r0v7 o.eq.a$e) binds: [B:31:0x0038, B:8:0x0022] A[DONT_GENERATE, DONT_INLINE]] */
    @Override // o.ep.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(final o.ep.a.InterfaceC0042a<java.util.List<o.ep.e>> r4) {
        /*
            r3 = this;
            int r0 = o.eq.a.k
            int r0 = r0 + 53
            int r1 = r0 % 128
            o.eq.a.f76o = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 == 0) goto L28
            o.eq.a$$ExternalSyntheticLambda5 r0 = new o.eq.a$$ExternalSyntheticLambda5
            r0.<init>()
            java.util.concurrent.atomic.AtomicBoolean r4 = r3.h
            boolean r4 = r4.get()
            r2 = 74
            int r2 = r2 / r1
            if (r4 == 0) goto L20
            r4 = 10
            goto L22
        L20:
            r4 = 36
        L22:
            switch(r4) {
                case 36: goto L3b;
                default: goto L25;
            }
        L25:
            goto L3c
        L26:
            r4 = move-exception
            throw r4
        L28:
            o.eq.a$$ExternalSyntheticLambda5 r0 = new o.eq.a$$ExternalSyntheticLambda5
            r0.<init>()
            java.util.concurrent.atomic.AtomicBoolean r4 = r3.h
            boolean r4 = r4.get()
            if (r4 == 0) goto L37
            r4 = r1
            goto L38
        L37:
            r4 = 1
        L38:
            switch(r4) {
                case 0: goto L3c;
                default: goto L3b;
            }
        L3b:
            goto L5a
        L3c:
            java.util.concurrent.atomic.AtomicBoolean r4 = r3.j
            boolean r4 = r4.get()
            if (r4 != 0) goto L3b
            r0.onDone()
            int r4 = o.eq.a.k
            int r4 = r4 + 73
            int r0 = r4 % 128
            o.eq.a.f76o = r0
            int r4 = r4 % 2
            if (r4 == 0) goto L59
            r4 = 42
            int r4 = r4 / r1
            return
        L57:
            r4 = move-exception
            throw r4
        L59:
            return
        L5a:
            java.util.List<o.eq.a$e> r4 = r3.f
            r4.add(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.d(o.ep.a$a):void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x013d, code lost:
    
        if (r21.hasExtra(((java.lang.String) r9[0]).intern()) == false) goto L48;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x016b, code lost:
    
        r8 = new java.lang.Object[1];
        m((char) ((android.view.KeyEvent.getMaxKeyCode() >> 16) + 32897), 25 - (android.os.Process.myTid() >> 22), 17 - (android.os.Process.myTid() >> 22), r8);
        r10 = ((java.lang.String) r8[0]).intern();
        r8 = new java.lang.Object[1];
        m((char) (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 973 - (android.view.ViewConfiguration.getScrollBarSize() >> 8), 21 - android.view.View.MeasureSpec.getSize(0), r8);
        r1 = a(r17, r10, r21.getStringExtra(((java.lang.String) r8[0]).intern()), r18.s().a(), java.lang.Integer.valueOf(r19), o.ep.d.c);
        o.ee.g.c();
        r8 = new java.lang.Object[1];
        m((char) android.view.View.resolveSizeAndState(0, 0, 0), android.view.View.MeasureSpec.getSize(0), (android.view.ViewConfiguration.getWindowTouchSlop() >> 8) + 19, r8);
        r2 = ((java.lang.String) r8[0]).intern();
        r4 = new java.lang.StringBuilder();
        r3 = new java.lang.Object[1];
        m((char) (android.text.AndroidCharacter.getMirror('0') - '0'), 1067 - android.text.TextUtils.getTrimmedLength(""), (android.media.AudioTrack.getMaxVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 38, r3);
        o.ee.g.d(r2, r4.append(((java.lang.String) r3[0]).intern()).append(r1.c()).toString());
        r16.a(r1.c());
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0235, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0167, code lost:
    
        if (r21.hasExtra(((java.lang.String) r9[0]).intern()) == false) goto L48;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public /* synthetic */ void c(o.ep.a.c r16, android.app.Activity r17, o.eo.e r18, int r19, int r20, android.content.Intent r21) {
        /*
            Method dump skipped, instructions count: 692
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.c(o.ep.a$c, android.app.Activity, o.eo.e, int, int, android.content.Intent):void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void d(Activity activity, o.eo.e eVar, o.ep.e eVar2, c.e eVar3, int i2, h hVar) {
        int i3 = f76o + 11;
        k = i3 % 128;
        switch (i3 % 2 == 0 ? 'a' : 'P') {
            case Opcodes.LADD /* 97 */:
                c(activity, eVar, eVar2, eVar3.e(), eVar3.c(), i2, hVar);
                throw null;
            default:
                c(activity, eVar, eVar2, eVar3.e(), eVar3.c(), i2, hVar);
                int i4 = f76o + 53;
                k = i4 % 128;
                int i5 = i4 % 2;
                return;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:51:0x012b  */
    /* renamed from: e, reason: avoid collision after fix types in other method */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void e2(final android.app.Activity r15, final o.ep.a.c r16, o.ee.i r17, final o.eo.e r18, final o.ep.e r19, final o.an.c.e r20, final o.ee.h r21) {
        /*
            Method dump skipped, instructions count: 592
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.e2(android.app.Activity, o.ep.a$c, o.ee.i, o.eo.e, o.ep.e, o.an.c$e, o.ee.h):void");
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:105:0x0143  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void c(android.app.Activity r18, o.eo.e r19, o.ep.e r20, byte[] r21, java.lang.String r22, int r23, o.ee.h r24) {
        /*
            Method dump skipped, instructions count: 982
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.c(android.app.Activity, o.eo.e, o.ep.e, byte[], java.lang.String, int, o.ee.h):void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void e(a.InterfaceC0042a interfaceC0042a, int i2, Intent intent) {
        int i3 = k + 47;
        f76o = i3 % 128;
        int i4 = i3 % 2;
        if (i2 == 0) {
            interfaceC0042a.e(new o.bv.c(AntelopErrorCode.UserCancelled));
            return;
        }
        if (i2 == -1) {
            interfaceC0042a.e((a.InterfaceC0042a) new Object());
            return;
        }
        interfaceC0042a.e(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
        int i5 = f76o + Opcodes.LNEG;
        k = i5 % 128;
        int i6 = i5 % 2;
    }

    @Override // o.ep.a
    public final void a(Activity activity, final a.InterfaceC0042a<Object> interfaceC0042a, i iVar) {
        int i2 = f76o + 33;
        int i3 = i2 % 128;
        k = i3;
        switch (i2 % 2 != 0) {
            case true:
                if (this.b == null) {
                    int i4 = i3 + 71;
                    f76o = i4 % 128;
                    switch (i4 % 2 != 0 ? '9' : '(') {
                        case '9':
                            throw null;
                        default:
                            return;
                    }
                }
                g.c();
                Object[] objArr = new Object[1];
                m((char) View.MeasureSpec.getMode(0), (-1) - Process.getGidForName(""), 19 - Color.argb(0, 0, 0, 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                m((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 724, 14 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                if (iVar != null) {
                    iVar.e(51, new i.a() { // from class: o.eq.a$$ExternalSyntheticLambda10
                        @Override // o.ee.i.a
                        public final void onActivityResult(int i5, Intent intent) {
                            a.e(a.InterfaceC0042a.this, i5, intent);
                        }
                    });
                    this.b.createWallet(activity, 51);
                    return;
                }
                int i5 = k + 85;
                f76o = i5 % 128;
                int i6 = i5 % 2;
                g.c();
                Object[] objArr3 = new Object[1];
                m((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), ViewConfiguration.getEdgeSlop() >> 16, View.resolveSizeAndState(0, 0, 0) + 19, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                m((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), ((Process.getThreadPriority(0) + 20) >> 6) + 738, 51 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
                g.e(intern2, ((String) objArr4[0]).intern());
                return;
            default:
                throw null;
        }
    }

    @Override // o.ep.a
    public final void c(Activity activity) {
        int i2 = k + 53;
        f76o = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                if (this.b == null) {
                    return;
                }
                g.c();
                Object[] objArr = new Object[1];
                m((char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), Color.blue(0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                m((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), TextUtils.lastIndexOf("", '0', 0, 0) + 725, 14 - View.resolveSizeAndState(0, 0, 0), objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.b.createWallet(activity, 51);
                int i3 = k + 27;
                f76o = i3 % 128;
                int i4 = i3 % 2;
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ep.c
    public final void a(Activity activity, o.ep.e eVar) {
        int i2 = f76o;
        int i3 = i2 + 43;
        k = i3 % 128;
        int i4 = i3 % 2;
        if (this.b == null) {
            int i5 = i2 + 69;
            k = i5 % 128;
            switch (i5 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }
        g.c();
        Object[] objArr = new Object[1];
        m((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), KeyEvent.getDeadChar(0, 0) + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (View.resolveSizeAndState(0, 0, 0) + 42807), 789 - TextUtils.indexOf("", ""), 17 - ExpandableListView.getPackedPositionChild(0L), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.b.requestSelectToken(activity, eVar.c(), eVar.b().intValue(), 53);
    }

    @Override // o.ep.c
    public final void d(Activity activity, o.ep.e eVar) {
        int i2 = f76o + 17;
        k = i2 % 128;
        int i3 = i2 % 2;
        if (this.b == null) {
            return;
        }
        g.c();
        Object[] objArr = new Object[1];
        m((char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), TextUtils.getOffsetBefore("", 0), 19 - Color.green(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 807, 13 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.b.requestDeleteToken(activity, eVar.c(), eVar.b().intValue(), 54);
        int i4 = f76o + Opcodes.LSHL;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? ';' : 'H') {
            case 'H':
                return;
            default:
                throw null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void a(PendingIntent pendingIntent) {
        int i2 = k + 61;
        f76o = i2 % 128;
        try {
            switch (i2 % 2 != 0 ? 'W' : (char) 23) {
                case Opcodes.POP /* 87 */:
                    pendingIntent.send();
                    int i3 = 47 / 0;
                    break;
                default:
                    pendingIntent.send();
                    break;
            }
        } catch (PendingIntent.CanceledException e2) {
            g.c();
            Object[] objArr = new Object[1];
            m((char) (AndroidCharacter.getMirror('0') - '0'), ViewConfiguration.getMinimumFlingVelocity() >> 16, 19 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            m((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 896 - TextUtils.lastIndexOf("", '0'), Color.argb(0, 0, 0, 0) + 43, objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e2);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void c(Exception exc) {
        int i2 = f76o + 89;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m((char) TextUtils.indexOf("", "", 0, 0), ViewConfiguration.getTapTimeout() >> 16, 19 - TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (Process.getGidForName("") + 56828), 864 - (ViewConfiguration.getFadingEdgeLength() >> 16), View.combineMeasuredStates(0, 0) + 33, objArr2);
        g.a(intern, ((String) objArr2[0]).intern(), exc);
        int i4 = f76o + 69;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.ep.c
    public final void c(Activity activity, o.ep.e eVar) {
        int i2 = k + 65;
        f76o = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                if (this.b == null) {
                    return;
                }
                g.c();
                Object[] objArr = new Object[1];
                m((char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), TextUtils.indexOf("", "", 0, 0), View.getDefaultSize(0, 0) + 19, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                m((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), 819 - (ViewConfiguration.getFadingEdgeLength() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                this.b.viewToken(new ViewTokenRequest.Builder().setIssuerTokenId(eVar.c()).setTokenServiceProvider(eVar.b().intValue()).build()).addOnSuccessListener(new OnSuccessListener() { // from class: o.eq.a$$ExternalSyntheticLambda11
                    @Override // com.google.android.gms.tasks.OnSuccessListener
                    public final void onSuccess(Object obj2) {
                        a.a((PendingIntent) obj2);
                    }
                }).addOnFailureListener(new OnFailureListener() { // from class: o.eq.a$$ExternalSyntheticLambda12
                    @Override // com.google.android.gms.tasks.OnFailureListener
                    public final void onFailure(Exception exc) {
                        a.c(exc);
                    }
                }).addOnCanceledListener(new OnCanceledListener() { // from class: o.eq.a$$ExternalSyntheticLambda13
                    @Override // com.google.android.gms.tasks.OnCanceledListener
                    public final void onCanceled() {
                        a.b();
                    }
                });
                int i3 = f76o + 95;
                k = i3 % 128;
                switch (i3 % 2 == 0 ? '8' : 'C') {
                    case '8':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void b() {
        int i2 = k + 63;
        f76o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m((char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), ViewConfiguration.getWindowTouchSlop() >> 8, (ViewConfiguration.getEdgeSlop() >> 16) + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getTapTimeout() >> 16), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 829, (ViewConfiguration.getLongPressTimeout() >> 16) + 35, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i4 = f76o + Opcodes.DREM;
        k = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.a.m(char, int, int, java.lang.Object[]):void");
    }
}
