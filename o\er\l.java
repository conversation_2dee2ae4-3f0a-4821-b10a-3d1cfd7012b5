package o.er;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\l.smali */
public final class l extends h {
    private static char a;
    private static char b;
    private static char e;
    private static int f;
    private static char g;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int i = 0;

    static {
        f = 1;
        d();
        TextUtils.lastIndexOf("", '0', 0);
        int i2 = i + 77;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void d() {
        e = (char) 63455;
        a = (char) 42419;
        g = (char) 20638;
        b = (char) 32998;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        boolean b2;
        int i2 = i + 35;
        f = i2 % 128;
        switch (i2 % 2 == 0 ? '\b' : 'c') {
            case '\b':
                b2 = super.b();
                int i3 = 2 / 0;
                break;
            default:
                b2 = super.b();
                break;
        }
        int i4 = f + 45;
        i = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                int i5 = 23 / 0;
                return b2;
            default:
                return b2;
        }
    }

    public l(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        a[] aVarArr;
        int i2 = f + 19;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.quote : (char) 17) {
            case 17:
                aVarArr = new a[]{this.d.h()};
                break;
            default:
                aVarArr = new a[]{this.d.h()};
                break;
        }
        int i3 = i + 77;
        f = i3 % 128;
        int i4 = i3 % 2;
        return aVarArr;
    }

    private String e() throws WalletValidationException {
        int i2 = f + Opcodes.DNEG;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? 'M' : '^') {
            case Opcodes.DUP2_X2 /* 94 */:
                String e2 = this.d.h().e();
                switch (e2 != null) {
                    case true:
                        return e2;
                    default:
                        int i3 = f + 25;
                        i = i3 % 128;
                        int i4 = i3 % 2;
                        o.ee.g.c();
                        Object[] objArr = new Object[1];
                        h("乎㚊䝩쀓㵀ꭒ鶩颟ぅṒ᩸神碃攣᩸神멺\ue254屠㵠豑擭⅏凈연슃", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 24, objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        h("\ue72b充崒ꛆ⩭鄌ꫂ鰐뺒딎⅏凈上페S\ue4af뼗鶍上페\uf6c5撗巺\ue43f\u0002\uead8ცຐ\udebe氬댘旙\udf25뵢猟㷞\ua7e9⁼ᆐ큉奂ўꭧ\ud9ef砙坘鶩颟⤤\uddda等셆䲻횔上페S\ue4af剘쟶栢튉䘽鵷㿘뢉剘쟶\udf25뵢ცຐ⤤\udddaﱇ扤ृﳶ穾\uec97\uf6c5撗\uf15d타Ἅ횄炽骦\ua7e9⁼ღ韎\ua7e9⁼◚쵪ᨥ훁ម\ue684㏹↡ᣡ䶳⤤\uddda⩭鄌쮂囐栢튉䘽鵷㿘뢉쩤减", View.combineMeasuredStates(0, 0) + Opcodes.LNEG, objArr2);
                        o.ee.g.e(intern, ((String) objArr2[0]).intern());
                        Object[] objArr3 = new Object[1];
                        h("ᣡ䶳鼄ၐ⩭鄌ꫂ鰐뺒딎⅏凈上페S\ue4af쩤减", 17 - View.resolveSizeAndState(0, 0, 0), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        int i5 = i + Opcodes.LSHL;
                        f = i5 % 128;
                        int i6 = i5 % 2;
                        return intern2;
                }
            default:
                this.d.h().e();
                throw null;
        }
    }

    public final o.v.e a() throws WalletValidationException {
        o.v.e eVar = new o.v.e(e(), this.c, b());
        int i2 = i + 21;
        f = i2 % 128;
        int i3 = i2 % 2;
        return eVar;
    }

    public final boolean c() {
        int i2 = i + Opcodes.LSHR;
        f = i2 % 128;
        switch (i2 % 2 == 0 ? 'R' : (char) 0) {
            case Opcodes.DASTORE /* 82 */:
                this.d.h().d();
                throw null;
            default:
                boolean d = this.d.h().d();
                int i3 = i + 23;
                f = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        return d;
                    default:
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.l.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
