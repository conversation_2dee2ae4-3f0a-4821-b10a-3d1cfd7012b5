package o.f;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\d.smali */
public final class d extends o.bv.d {
    private static int d = 0;
    private static int a = 1;

    public d(byte[] bArr) {
        super(bArr);
    }

    public final o.bv.a b() {
        int i = (d + 84) - 1;
        a = i % 128;
        int i2 = i % 2;
        o.bv.a a2 = super.a();
        int i3 = d;
        int i4 = (i3 ^ 47) + ((i3 & 47) << 1);
        a = i4 % 128;
        int i5 = i4 % 2;
        return a2;
    }

    public final boolean c() {
        int i = (a + Opcodes.IAND) - 1;
        d = i % 128;
        int i2 = i % 2;
        boolean z = this.e;
        int i3 = a;
        int i4 = (i3 & 83) + (i3 | 83);
        d = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 72 / 0;
                return z;
            default:
                return z;
        }
    }
}
