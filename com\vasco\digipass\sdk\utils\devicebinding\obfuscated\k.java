package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import android.security.keystore.KeyGenParameterSpec;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import java.security.InvalidAlgorithmParameterException;
import java.security.NoSuchAlgorithmException;
import java.security.NoSuchProviderException;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import kotlin.Metadata;
import kotlin.NoWhenBranchMatchedException;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\bÀ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u000b\u0010\fJ(\u0010\n\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u0006¨\u0006\r"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/k;", "", "", "keyAlias", "Landroid/content/Context;", "context", "", "invalidateOnNewCredentialEnrollment", "userBiometricAuthenticationRequired", "Ljavax/crypto/SecretKey;", "a", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\k.smali */
public final class k {
    public static final k a = new k();

    private k() {
    }

    public final SecretKey a(String keyAlias, Context context, boolean invalidateOnNewCredentialEnrollment, boolean userBiometricAuthenticationRequired) throws DeviceBindingSDKException {
        KeyGenParameterSpec.Builder a2;
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        Intrinsics.checkNotNullParameter(context, "context");
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("HmacSHA256", "AndroidKeyStore");
            if (userBiometricAuthenticationRequired) {
                a2 = new b(context, keyAlias, invalidateOnNewCredentialEnrollment).a();
            } else {
                if (userBiometricAuthenticationRequired) {
                    throw new NoWhenBranchMatchedException();
                }
                a2 = new l(context, keyAlias).a();
            }
            keyGenerator.init(a2.build());
            return keyGenerator.generateKey();
        } catch (InvalidAlgorithmParameterException e) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e);
        } catch (NoSuchAlgorithmException e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e2);
        } catch (NoSuchProviderException e3) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e3);
        }
    }
}
