package o.dt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dt\d.smali */
final class d {
    private static int a = 0;
    private static int e = 1;

    d() {
    }

    /* JADX WARN: Code restructure failed: missing block: B:100:0x01ad, code lost:
    
        if (java.util.Objects.equals(r0, r9.p()) == false) goto L114;
     */
    /* JADX WARN: Code restructure failed: missing block: B:101:0x01af, code lost:
    
        r9.a(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:106:0x01a7, code lost:
    
        if (java.util.Objects.equals(r0, r9.p()) == false) goto L114;
     */
    /* JADX WARN: Code restructure failed: missing block: B:221:0x0023, code lost:
    
        if (r0 != null) goto L12;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:198:0x031e. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:117:0x01ea  */
    /* JADX WARN: Removed duplicated region for block: B:120:0x01f2  */
    /* JADX WARN: Removed duplicated region for block: B:136:0x0232  */
    /* JADX WARN: Removed duplicated region for block: B:153:0x026c  */
    /* JADX WARN: Removed duplicated region for block: B:155:0x0273  */
    /* JADX WARN: Removed duplicated region for block: B:169:0x02b4  */
    /* JADX WARN: Removed duplicated region for block: B:171:0x02bb  */
    /* JADX WARN: Removed duplicated region for block: B:191:0x02f0  */
    /* JADX WARN: Removed duplicated region for block: B:200:0x02b6  */
    /* JADX WARN: Removed duplicated region for block: B:201:0x026e  */
    /* JADX WARN: Removed duplicated region for block: B:202:0x01ec  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static boolean d(o.dr.a r9, o.dr.a r10) {
        /*
            Method dump skipped, instructions count: 930
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dt.d.d(o.dr.a, o.dr.a):boolean");
    }
}
