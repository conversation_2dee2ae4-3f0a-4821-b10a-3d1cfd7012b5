package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a2.smali */
public class a2 extends n {
    public a2(byte[] bArr) {
        super(bArr);
    }

    private byte[] k() {
        byte[] bArr = this.b;
        if (bArr[bArr.length - 1] != 90) {
            return bArr;
        }
        if (!i()) {
            byte[] bArr2 = this.b;
            byte[] bArr3 = new byte[bArr2.length + 4];
            System.arraycopy(bArr2, 0, bArr3, 0, bArr2.length - 1);
            System.arraycopy(o7.a("0000Z"), 0, bArr3, this.b.length - 1, 5);
            return bArr3;
        }
        if (!j()) {
            byte[] bArr4 = this.b;
            byte[] bArr5 = new byte[bArr4.length + 2];
            System.arraycopy(bArr4, 0, bArr5, 0, bArr4.length - 1);
            System.arraycopy(o7.a("00Z"), 0, bArr5, this.b.length - 1, 3);
            return bArr5;
        }
        if (!h()) {
            return this.b;
        }
        int length = this.b.length - 2;
        while (length > 0 && this.b[length] == 48) {
            length--;
        }
        byte[] bArr6 = this.b;
        if (bArr6[length] == 46) {
            byte[] bArr7 = new byte[length + 1];
            System.arraycopy(bArr6, 0, bArr7, 0, length);
            bArr7[length] = 90;
            return bArr7;
        }
        byte[] bArr8 = new byte[length + 2];
        int i = length + 1;
        System.arraycopy(bArr6, 0, bArr8, 0, i);
        bArr8[i] = 90;
        return bArr8;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.n, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, k().length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.n, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.n, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 24, k());
    }
}
