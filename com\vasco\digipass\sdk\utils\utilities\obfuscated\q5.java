package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import androidx.room.RoomMasterTable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\q5.smali */
public interface q5 {
    public static final w A;
    public static final w B;
    public static final w C;
    public static final w D;
    public static final w E;
    public static final w F;
    public static final w G;
    public static final w H;
    public static final w I;
    public static final w J;
    public static final w K;
    public static final w L;
    public static final w M;
    public static final w N;
    public static final w O;
    public static final w P;
    public static final w Q;
    public static final w R;
    public static final w S;
    public static final w T;
    public static final w U;
    public static final w V;
    public static final w W;
    public static final w X;
    public static final w Y;
    public static final w Z;
    public static final w a;
    public static final w a0;
    public static final w b;
    public static final w b0;
    public static final w c;
    public static final w c0;
    public static final w d;
    public static final w d0;
    public static final w e;
    public static final w e0;
    public static final w f;
    public static final w f0;
    public static final w g;
    public static final w g0;
    public static final w h;
    public static final w h0;
    public static final w i;
    public static final w i0;
    public static final w j;
    public static final w j0;
    public static final w k;
    public static final w k0;
    public static final w l;
    public static final w l0;
    public static final w m;
    public static final w m0;
    public static final w n;
    public static final w n0;

    /* renamed from: o, reason: collision with root package name */
    public static final w f26o;
    public static final w o0;
    public static final w p;
    public static final w p0;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("2.16.840.1.101.3.4");
        a = wVar;
        w a2 = wVar.a("2");
        b = a2;
        c = a2.a("1");
        d = a2.a("2");
        e = a2.a("3");
        f = a2.a("4");
        g = a2.a("5");
        h = a2.a("6");
        i = a2.a("7");
        j = a2.a("8");
        k = a2.a("9");
        l = a2.a("10");
        m = a2.a("11");
        n = a2.a("12");
        f26o = a2.a("13");
        p = a2.a("14");
        q = a2.a("15");
        r = a2.a("16");
        s = a2.a("17");
        t = a2.a("18");
        u = a2.a("19");
        v = a2.a("20");
        w a3 = wVar.a("1");
        w = a3;
        x = a3.a("1");
        y = a3.a("2");
        z = a3.a("3");
        A = a3.a("4");
        B = a3.a("5");
        C = a3.a("6");
        D = a3.a("7");
        E = a3.a("8");
        F = a3.a("9");
        G = a3.a("21");
        H = a3.a("22");
        I = a3.a("23");
        J = a3.a("24");
        K = a3.a("25");
        L = a3.a("26");
        M = a3.a("27");
        N = a3.a("28");
        O = a3.a("29");
        P = a3.a("41");
        Q = a3.a(RoomMasterTable.DEFAULT_ID);
        R = a3.a("43");
        S = a3.a("44");
        T = a3.a("45");
        U = a3.a("46");
        V = a3.a("47");
        W = a3.a("48");
        X = a3.a("49");
        w a4 = wVar.a("3");
        Y = a4;
        Z = a4;
        a0 = a4.a("1");
        b0 = a4.a("2");
        c0 = a4.a("3");
        d0 = a4.a("4");
        e0 = a4.a("5");
        f0 = a4.a("6");
        g0 = a4.a("7");
        h0 = a4.a("8");
        i0 = a4.a("9");
        j0 = a4.a("10");
        k0 = a4.a("11");
        l0 = a4.a("12");
        m0 = a4.a("13");
        n0 = a4.a("14");
        o0 = a4.a("15");
        p0 = a4.a("16");
    }
}
