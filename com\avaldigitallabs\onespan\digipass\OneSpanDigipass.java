package com.avaldigitallabs.onespan.digipass;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.vasco.digipass.sdk.DigipassSDK;
import com.vasco.digipass.sdk.responses.ActivationResponse;
import com.vasco.digipass.sdk.responses.GenerationResponse;
import com.vasco.digipass.sdk.responses.MultiDeviceLicenseActivationResponse;
import com.vasco.digipass.sdk.responses.SecureChannelDecryptionResponse;
import com.vasco.digipass.sdk.responses.SecureChannelParseResponse;

@CapacitorPlugin(name = "OneSpanDigipass")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes10\com\avaldigitallabs\onespan\digipass\OneSpanDigipass.smali */
public class OneSpanDigipass extends Plugin {
    private static final long clientServerTimeShift = 0;

    @PluginMethod
    public void multiDeviceActivateLicense(PluginCall call) {
        String enrollmentKey = call.getString("enrollmentKey");
        String staticVector = call.getString("staticVector");
        String platformFingerprint = call.getString("fingerprint");
        SecureChannelParseResponse secureChannel = DigipassSDK.parseSecureChannelMessage(enrollmentKey);
        if (secureChannel.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(secureChannel.getReturnCode()));
            return;
        }
        MultiDeviceLicenseActivationResponse licenseActivation = DigipassSDK.multiDeviceActivateLicense(secureChannel.getMessage(), staticVector, platformFingerprint, (byte) 0, 0L);
        if (licenseActivation.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(licenseActivation.getReturnCode()));
            return;
        }
        JSObject result = new JSObject();
        result.put("deviceCode", licenseActivation.getDeviceCode());
        byte[] dynamicVectorResponse = licenseActivation.getDynamicVector();
        byte[] staticVectorResponse = licenseActivation.getStaticVector();
        result.put("dynamicVector", toHexString(dynamicVectorResponse));
        result.put("staticVector", toHexString(staticVectorResponse));
        call.resolve(result);
    }

    @PluginMethod
    public void multiDeviceActivateInstance(PluginCall call) {
        String enrollmentKey = call.getString("enrollmentKey");
        byte[] dynamicVector = toByteArray(call.getString("dynamicVector"));
        byte[] staticVector = toByteArray(call.getString("staticVector"));
        String platformFingerprint = call.getString("fingerprint");
        SecureChannelParseResponse secureChannel = DigipassSDK.parseSecureChannelMessage(enrollmentKey);
        if (secureChannel.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(secureChannel.getReturnCode()));
            return;
        }
        ActivationResponse instanceActivation = DigipassSDK.multiDeviceActivateInstance(staticVector, dynamicVector, secureChannel.getMessage(), null, platformFingerprint);
        if (instanceActivation.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(instanceActivation.getReturnCode()));
            return;
        }
        byte[] staticVector2 = instanceActivation.getStaticVector();
        GenerationResponse signatureResponse = DigipassSDK.generateSignatureFromSecureChannelMessage(staticVector2, instanceActivation.getDynamicVector(), secureChannel.getMessage(), null, 0L, 2, platformFingerprint);
        if (signatureResponse.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(signatureResponse.getReturnCode()));
            return;
        }
        byte[] dynamicVector2 = signatureResponse.getDynamicVector();
        JSObject result = new JSObject();
        result.put("signatureCode", signatureResponse.getResponse());
        result.put("dynamicVector", toHexString(dynamicVector2));
        result.put("staticVector", toHexString(staticVector2));
        call.resolve(result);
    }

    @PluginMethod
    public void decryptSecureChannelMessageBody(PluginCall call) {
        String secureChannelMessageRequest = call.getString("secureChannelMessageRequest");
        String platformFingerprint = call.getString("fingerprint");
        byte[] dynamicVector = toByteArray(call.getString("dynamicVector"));
        byte[] staticVector = toByteArray(call.getString("staticVector"));
        SecureChannelParseResponse secureChannel = DigipassSDK.parseSecureChannelMessage(secureChannelMessageRequest);
        if (secureChannel.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(secureChannel.getReturnCode()));
            return;
        }
        SecureChannelDecryptionResponse decryptionResponse = DigipassSDK.decryptSecureChannelMessageBody(staticVector, dynamicVector, secureChannel.getMessage(), platformFingerprint);
        if (decryptionResponse.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(decryptionResponse.getReturnCode()));
            return;
        }
        JSObject result = new JSObject();
        result.put("decryptedBody", decryptionResponse.getMessageBody());
        call.resolve(result);
    }

    @PluginMethod
    public void generateSignatureFromSecureChannelMessage(PluginCall call) {
        String secureChannelMessageRequest = call.getString("secureChannelMessageRequest");
        String platformFingerprint = call.getString("fingerprint");
        byte[] dynamicVector = toByteArray(call.getString("dynamicVector"));
        byte[] staticVector = toByteArray(call.getString("staticVector"));
        SecureChannelParseResponse channelResponse = DigipassSDK.parseSecureChannelMessage(secureChannelMessageRequest);
        if (channelResponse.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(channelResponse.getReturnCode()));
            return;
        }
        GenerationResponse signatureResponse = DigipassSDK.generateSignatureFromSecureChannelMessage(staticVector, dynamicVector, channelResponse.getMessage(), null, 0L, 2, platformFingerprint);
        if (signatureResponse.getReturnCode() != 0) {
            call.reject(DigipassSDK.getMessageForReturnCode(signatureResponse.getReturnCode()));
            return;
        }
        JSObject result = new JSObject();
        result.put("dynamicCode", signatureResponse.getResponse());
        call.resolve(result);
    }

    private static String toHexString(byte[] vector) {
        StringBuffer buffer = new StringBuffer();
        for (byte b : vector) {
            buffer.append(Integer.toHexString((b & 255) | 256).substring(1, 3));
        }
        return buffer.toString();
    }

    private static byte[] toByteArray(String hexString) {
        byte[] data = new byte[hexString.length() / 2];
        for (int i = 0; i < hexString.length(); i += 2) {
            data[i / 2] = Integer.decode("0x" + hexString.charAt(i) + hexString.charAt(i + 1)).byteValue();
        }
        return data;
    }
}
