package com.esotericsoftware.reflectasm;

import com.esotericsoftware.asm.ClassWriter;
import com.esotericsoftware.asm.Label;
import com.esotericsoftware.asm.MethodVisitor;
import com.esotericsoftware.asm.Opcodes;
import com.esotericsoftware.asm.Type;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\reflectasm\MethodAccess.smali */
public abstract class MethodAccess {
    private String[] methodNames;
    private Class[][] parameterTypes;
    private Class[] returnTypes;

    public abstract Object invoke(Object obj, int i, Object... objArr);

    public Object invoke(Object object, String methodName, Class[] paramTypes, Object... args) {
        return invoke(object, getIndex(methodName, paramTypes), args);
    }

    public Object invoke(Object object, String methodName, Object... args) {
        return invoke(object, getIndex(methodName, args == null ? 0 : args.length), args);
    }

    public int getIndex(String methodName) {
        int n = this.methodNames.length;
        for (int i = 0; i < n; i++) {
            if (this.methodNames[i].equals(methodName)) {
                return i;
            }
        }
        throw new IllegalArgumentException("Unable to find non-private method: " + methodName);
    }

    public int getIndex(String methodName, Class... paramTypes) {
        int n = this.methodNames.length;
        for (int i = 0; i < n; i++) {
            if (this.methodNames[i].equals(methodName) && Arrays.equals(paramTypes, this.parameterTypes[i])) {
                return i;
            }
        }
        throw new IllegalArgumentException("Unable to find non-private method: " + methodName + " " + Arrays.toString(paramTypes));
    }

    public int getIndex(String methodName, int paramsCount) {
        int n = this.methodNames.length;
        for (int i = 0; i < n; i++) {
            if (this.methodNames[i].equals(methodName) && this.parameterTypes[i].length == paramsCount) {
                return i;
            }
        }
        throw new IllegalArgumentException("Unable to find non-private method: " + methodName + " with " + paramsCount + " params.");
    }

    public String[] getMethodNames() {
        return this.methodNames;
    }

    public Class[][] getParameterTypes() {
        return this.parameterTypes;
    }

    public Class[] getReturnTypes() {
        return this.returnTypes;
    }

    /* JADX WARN: Unreachable blocks removed: 2, instructions: 6 */
    public static MethodAccess get(Class type) {
        Class accessClass;
        Class[][] parameterTypes;
        Class[] returnTypes;
        String accessClassName;
        String accessClassName2;
        Class[] paramTypes;
        boolean isInterface = type.isInterface();
        if (!isInterface && type.getSuperclass() == null && type != Object.class) {
            throw new IllegalArgumentException("The type must not be an interface, a primitive type, or void.");
        }
        ArrayList<Method> methods = new ArrayList<>();
        if (isInterface) {
            recursiveAddInterfaceMethodsToList(type, methods);
        } else {
            for (Class nextClass = type; nextClass != Object.class; nextClass = nextClass.getSuperclass()) {
                addDeclaredMethodsToList(nextClass, methods);
            }
        }
        int n = methods.size();
        String[] methodNames = new String[n];
        Class[][] parameterTypes2 = new Class[n][];
        Class[] returnTypes2 = new Class[n];
        for (int i = 0; i < n; i++) {
            Method method = methods.get(i);
            methodNames[i] = method.getName();
            parameterTypes2[i] = method.getParameterTypes();
            returnTypes2[i] = method.getReturnType();
        }
        String className = type.getName();
        String accessClassName3 = className + "MethodAccess";
        if (accessClassName3.startsWith("java.")) {
            accessClassName3 = "reflectasm." + accessClassName3;
        }
        String accessClassName4 = accessClassName3;
        AccessClassLoader loader = AccessClassLoader.get(type);
        synchronized (loader) {
            try {
                Class accessClass2 = loader.loadAccessClass(accessClassName4);
                if (accessClass2 == null) {
                    try {
                        String accessClassNameInternal = accessClassName4.replace('.', '/');
                        String classNameInternal = className.replace('.', '/');
                        ClassWriter cw = new ClassWriter(1);
                        cw.visit(Opcodes.V1_1, 33, accessClassNameInternal, null, "com/esotericsoftware/reflectasm/MethodAccess", null);
                        MethodVisitor mv = cw.visitMethod(1, "<init>", "()V", null, null);
                        mv.visitCode();
                        mv.visitVarInsn(25, 0);
                        mv.visitMethodInsn(Opcodes.INVOKESPECIAL, "com/esotericsoftware/reflectasm/MethodAccess", "<init>", "()V");
                        mv.visitInsn(Opcodes.RETURN);
                        mv.visitMaxs(0, 0);
                        mv.visitEnd();
                        MethodVisitor mv2 = cw.visitMethod(Opcodes.LOR, "invoke", "(Ljava/lang/Object;I[Ljava/lang/Object;)Ljava/lang/Object;", null, null);
                        mv2.visitCode();
                        if (methods.isEmpty()) {
                            parameterTypes = parameterTypes2;
                            returnTypes = returnTypes2;
                            accessClassName2 = accessClassName4;
                        } else {
                            try {
                                mv2.visitVarInsn(25, 1);
                                mv2.visitTypeInsn(192, classNameInternal);
                                mv2.visitVarInsn(58, 4);
                                mv2.visitVarInsn(21, 2);
                                Label[] labels = new Label[n];
                                for (int i2 = 0; i2 < n; i2++) {
                                    try {
                                        labels[i2] = new Label();
                                    } catch (Throwable th) {
                                        t = th;
                                        while (true) {
                                            try {
                                                throw t;
                                            } catch (Throwable th2) {
                                                t = th2;
                                            }
                                        }
                                    }
                                }
                                Label defaultLabel = new Label();
                                mv2.visitTableSwitchInsn(0, labels.length - 1, defaultLabel, labels);
                                StringBuilder buffer = new StringBuilder(128);
                                int i3 = 0;
                                while (i3 < n) {
                                    int n2 = n;
                                    try {
                                        mv2.visitLabel(labels[i3]);
                                        if (i3 == 0) {
                                            try {
                                                mv2.visitFrame(1, 1, new Object[]{classNameInternal}, 0, null);
                                            } catch (Throwable th3) {
                                                t = th3;
                                                while (true) {
                                                    throw t;
                                                }
                                            }
                                        } else {
                                            mv2.visitFrame(3, 0, null, 0, null);
                                        }
                                        String className2 = className;
                                        try {
                                            mv2.visitVarInsn(25, 4);
                                            buffer.setLength(0);
                                            buffer.append('(');
                                            Class[] paramTypes2 = parameterTypes2[i3];
                                            Class returnType = returnTypes2[i3];
                                            int paramIndex = 0;
                                            while (true) {
                                                Label[] labels2 = labels;
                                                if (paramIndex < paramTypes2.length) {
                                                    returnTypes = returnTypes2;
                                                    try {
                                                        mv2.visitVarInsn(25, 3);
                                                        mv2.visitIntInsn(16, paramIndex);
                                                        mv2.visitInsn(50);
                                                        Type paramType = Type.getType(paramTypes2[paramIndex]);
                                                        switch (paramType.getSort()) {
                                                            case 1:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Boolean");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Boolean", "booleanValue", "()Z");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 2:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Character");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Character", "charValue", "()C");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 3:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Byte");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Byte", "byteValue", "()B");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 4:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Short");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Short", "shortValue", "()S");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 5:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Integer");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Integer", "intValue", "()I");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 6:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Float");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Float", "floatValue", "()F");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 7:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                mv2.visitTypeInsn(192, "java/lang/Long");
                                                                mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Long", "longValue", "()J");
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 8:
                                                                paramTypes = paramTypes2;
                                                                mv2.visitTypeInsn(192, "java/lang/Double");
                                                                parameterTypes = parameterTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                try {
                                                                    mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/Double", "doubleValue", "()D");
                                                                    buffer.append(paramType.getDescriptor());
                                                                    paramIndex++;
                                                                    paramTypes2 = paramTypes;
                                                                    labels = labels2;
                                                                    returnTypes2 = returnTypes;
                                                                    parameterTypes2 = parameterTypes;
                                                                    accessClassName4 = accessClassName2;
                                                                } catch (Throwable th4) {
                                                                    t = th4;
                                                                    while (true) {
                                                                        throw t;
                                                                    }
                                                                }
                                                            case 9:
                                                                paramTypes = paramTypes2;
                                                                mv2.visitTypeInsn(192, paramType.getDescriptor());
                                                                parameterTypes = parameterTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                            case 10:
                                                                try {
                                                                    paramTypes = paramTypes2;
                                                                    mv2.visitTypeInsn(192, paramType.getInternalName());
                                                                    parameterTypes = parameterTypes2;
                                                                    accessClassName2 = accessClassName4;
                                                                    buffer.append(paramType.getDescriptor());
                                                                    paramIndex++;
                                                                    paramTypes2 = paramTypes;
                                                                    labels = labels2;
                                                                    returnTypes2 = returnTypes;
                                                                    parameterTypes2 = parameterTypes;
                                                                    accessClassName4 = accessClassName2;
                                                                } catch (Throwable th5) {
                                                                    t = th5;
                                                                    while (true) {
                                                                        throw t;
                                                                    }
                                                                }
                                                            default:
                                                                parameterTypes = parameterTypes2;
                                                                paramTypes = paramTypes2;
                                                                accessClassName2 = accessClassName4;
                                                                buffer.append(paramType.getDescriptor());
                                                                paramIndex++;
                                                                paramTypes2 = paramTypes;
                                                                labels = labels2;
                                                                returnTypes2 = returnTypes;
                                                                parameterTypes2 = parameterTypes;
                                                                accessClassName4 = accessClassName2;
                                                        }
                                                    } catch (Throwable th6) {
                                                        t = th6;
                                                    }
                                                } else {
                                                    Class[][] parameterTypes3 = parameterTypes2;
                                                    Class[] returnTypes3 = returnTypes2;
                                                    String accessClassName5 = accessClassName4;
                                                    buffer.append(')');
                                                    buffer.append(Type.getDescriptor(returnType));
                                                    int invoke = isInterface ? Opcodes.INVOKEINTERFACE : Modifier.isStatic(methods.get(i3).getModifiers()) ? Opcodes.INVOKESTATIC : Opcodes.INVOKEVIRTUAL;
                                                    mv2.visitMethodInsn(invoke, classNameInternal, methodNames[i3], buffer.toString());
                                                    switch (Type.getType(returnType).getSort()) {
                                                        case 0:
                                                            mv2.visitInsn(1);
                                                            break;
                                                        case 1:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Boolean", "valueOf", "(Z)Ljava/lang/Boolean;");
                                                            break;
                                                        case 2:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Character", "valueOf", "(C)Ljava/lang/Character;");
                                                            break;
                                                        case 3:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Byte", "valueOf", "(B)Ljava/lang/Byte;");
                                                            break;
                                                        case 4:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Short", "valueOf", "(S)Ljava/lang/Short;");
                                                            break;
                                                        case 5:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Integer", "valueOf", "(I)Ljava/lang/Integer;");
                                                            break;
                                                        case 6:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Float", "valueOf", "(F)Ljava/lang/Float;");
                                                            break;
                                                        case 7:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Long", "valueOf", "(J)Ljava/lang/Long;");
                                                            break;
                                                        case 8:
                                                            mv2.visitMethodInsn(Opcodes.INVOKESTATIC, "java/lang/Double", "valueOf", "(D)Ljava/lang/Double;");
                                                            break;
                                                    }
                                                    mv2.visitInsn(Opcodes.ARETURN);
                                                    i3++;
                                                    className = className2;
                                                    n = n2;
                                                    labels = labels2;
                                                    returnTypes2 = returnTypes3;
                                                    parameterTypes2 = parameterTypes3;
                                                    accessClassName4 = accessClassName5;
                                                }
                                            }
                                        } catch (Throwable th7) {
                                            t = th7;
                                        }
                                    } catch (Throwable th8) {
                                        t = th8;
                                    }
                                }
                                parameterTypes = parameterTypes2;
                                returnTypes = returnTypes2;
                                accessClassName2 = accessClassName4;
                                mv2.visitLabel(defaultLabel);
                                mv2.visitFrame(3, 0, null, 0, null);
                            } catch (Throwable th9) {
                                t = th9;
                            }
                        }
                        try {
                            mv2.visitTypeInsn(Opcodes.NEW, "java/lang/IllegalArgumentException");
                            mv2.visitInsn(89);
                            mv2.visitTypeInsn(Opcodes.NEW, "java/lang/StringBuilder");
                            mv2.visitInsn(89);
                            mv2.visitLdcInsn("Method not found: ");
                            mv2.visitMethodInsn(Opcodes.INVOKESPECIAL, "java/lang/StringBuilder", "<init>", "(Ljava/lang/String;)V");
                            mv2.visitVarInsn(21, 2);
                            mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/StringBuilder", "append", "(I)Ljava/lang/StringBuilder;");
                            mv2.visitMethodInsn(Opcodes.INVOKEVIRTUAL, "java/lang/StringBuilder", "toString", "()Ljava/lang/String;");
                            mv2.visitMethodInsn(Opcodes.INVOKESPECIAL, "java/lang/IllegalArgumentException", "<init>", "(Ljava/lang/String;)V");
                            mv2.visitInsn(Opcodes.ATHROW);
                            mv2.visitMaxs(0, 0);
                            mv2.visitEnd();
                            cw.visitEnd();
                            byte[] data = cw.toByteArray();
                            accessClassName = accessClassName2;
                            try {
                                accessClass = loader.defineAccessClass(accessClassName, data);
                            } catch (Throwable th10) {
                                t = th10;
                                while (true) {
                                    throw t;
                                }
                            }
                        } catch (Throwable th11) {
                            t = th11;
                        }
                    } catch (Throwable th12) {
                        t = th12;
                    }
                } else {
                    accessClass = accessClass2;
                    parameterTypes = parameterTypes2;
                    returnTypes = returnTypes2;
                    accessClassName = accessClassName4;
                }
                try {
                    try {
                        MethodAccess access = (MethodAccess) accessClass.newInstance();
                        access.methodNames = methodNames;
                        try {
                            access.parameterTypes = parameterTypes;
                            try {
                                access.returnTypes = returnTypes;
                                return access;
                            } catch (Throwable th13) {
                                t = th13;
                                throw new RuntimeException("Error constructing method access class: " + accessClassName, t);
                            }
                        } catch (Throwable th14) {
                            t = th14;
                        }
                    } catch (Throwable th15) {
                        t = th15;
                    }
                } catch (Throwable th16) {
                    t = th16;
                    while (true) {
                        throw t;
                    }
                }
            } catch (Throwable th17) {
                t = th17;
            }
        }
    }

    private static void addDeclaredMethodsToList(Class type, ArrayList<Method> methods) {
        Method[] declaredMethods = type.getDeclaredMethods();
        for (Method method : declaredMethods) {
            int modifiers = method.getModifiers();
            if (!Modifier.isPrivate(modifiers)) {
                methods.add(method);
            }
        }
    }

    private static void recursiveAddInterfaceMethodsToList(Class interfaceType, ArrayList<Method> methods) {
        addDeclaredMethodsToList(interfaceType, methods);
        for (Class nextInterface : interfaceType.getInterfaces()) {
            recursiveAddInterfaceMethodsToList(nextInterface, methods);
        }
    }
}
