package o.fc;

import android.text.TextUtils;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.eg.b;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fc\e.smali */
public class e extends d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static int b;
    private static boolean d;
    private static char[] e;
    private static int i;
    private static int j;
    public int c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        e = new char[]{61935, 61948, 61933, 61949, 61699};
        a = true;
        d = true;
        b = 782102920;
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 33;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r8 = r8 + 117
            byte[] r0 = o.fc.e.$$a
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fc.e.m(byte, short, int, java.lang.Object[]):void");
    }

    public e(boolean z, c cVar, short s) {
        super(z, cVar, s);
    }

    @Override // o.fc.d
    public boolean e(String str, o.dd.e eVar) {
        switch (b() == c.b ? '?' : '6') {
            case Opcodes.ISTORE /* 54 */:
                return false;
            default:
                int i2 = i + 109;
                j = i2 % 128;
                if (i2 % 2 != 0) {
                    c(c.a);
                    throw null;
                }
                c(c.a);
                if (eVar != null) {
                    int i3 = i + 13;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                    eVar.d(str, this.c);
                }
                int i5 = j + 33;
                i = i5 % 128;
                int i6 = i5 % 2;
                return true;
        }
    }

    @Override // o.fc.d
    public final short e() {
        int i2 = j + 7;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? 'F' : (char) 30) {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                return (short) 0;
            default:
                return (short) 1;
        }
    }

    public final int h() {
        int i2 = i + Opcodes.DDIV;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 59 / 0;
                return this.c;
            default:
                return this.c;
        }
    }

    public final void a(int i2) {
        int i3 = j + 41;
        int i4 = i3 % 128;
        i = i4;
        int i5 = i3 % 2;
        this.c = i2;
        int i6 = i4 + 41;
        j = i6 % 128;
        int i7 = i6 % 2;
    }

    public final b j() throws o.eg.d {
        b bVar = new b();
        Object[] objArr = new Object[1];
        k(null, 127 - TextUtils.getTrimmedLength(""), null, "\u0083\u0082\u0081", objArr);
        bVar.d(((String) objArr[0]).intern(), this.c);
        Object[] objArr2 = new Object[1];
        k(null, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 127, null, "\u0084\u0085\u0082\u0081\u0082\u0084", objArr2);
        bVar.d(((String) objArr2[0]).intern(), b().c());
        int i2 = i + Opcodes.LMUL;
        j = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v2 */
    /* JADX WARN: Type inference failed for: r1v25, types: [byte[]] */
    private static void k(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 926
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fc.e.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
