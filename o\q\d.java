package o.q;

import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.math.BigDecimal;
import kotlin.text.Typography;
import o.ee.g;
import o.i.m;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\q\d.smali */
public final class d implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final BigDecimal e;
    private static char f;
    private static long j;
    private static char k;
    private static char l;
    private static int m;
    private static char n;

    /* renamed from: o, reason: collision with root package name */
    private static int f98o;
    private m a;
    private short b;
    private BigDecimal c;
    private BigDecimal d;
    private Integer h;
    private BigDecimal i = e;
    private Integer g = 0;

    static void init$0() {
        $$a = new byte[]{1, 25, 123, 58};
        $$b = Opcodes.IF_ICMPLE;
    }

    static void l() {
        j = -7220687940732697573L;
        l = (char) 6595;
        n = (char) 42802;
        k = (char) 32045;
        f = (char) 4228;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r0 = o.q.d.$$a
            int r7 = r7 * 4
            int r7 = r7 + 4
            int r6 = r6 * 3
            int r6 = r6 + 68
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = -r8
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.d.r(short, int, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f98o = 0;
        m = 1;
        l();
        TextUtils.lastIndexOf("", '0', 0, 0);
        ViewConfiguration.getScrollBarSize();
        ImageFormat.getBitsPerPixel(0);
        AndroidCharacter.getMirror('0');
        e = BigDecimal.ZERO;
        int i = m + Opcodes.LMUL;
        f98o = i % 128;
        int i2 = i % 2;
    }

    public d(short s, BigDecimal bigDecimal, m mVar, BigDecimal bigDecimal2, Integer num) {
        this.b = s;
        this.c = bigDecimal;
        this.a = mVar;
        this.d = bigDecimal2;
        this.h = num;
    }

    @Override // o.q.c
    public final void d(c cVar) {
        if (cVar instanceof d) {
            g.c();
            Object[] objArr = new Object[1];
            p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", ExpandableListView.getPackedPositionGroup(0L), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            p("애삶씕\ud901さ쐂탭㹸현휇엚ᅦ\ue32e\uea34\uf6c0ўﰥﴰ鯘睝襉針貧", KeyEvent.getDeadChar(0, 0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            d dVar = (d) cVar;
            this.b = dVar.b;
            this.c = dVar.c;
            this.a = dVar.a;
            BigDecimal bigDecimal = dVar.d;
            this.d = bigDecimal;
            Integer num = dVar.h;
            this.h = num;
            switch (bigDecimal != null) {
                case true:
                    break;
                default:
                    this.i = e;
                    int i = f98o + 11;
                    m = i % 128;
                    int i2 = i % 2;
                    break;
            }
            switch (num != null) {
                case true:
                    break;
                default:
                    int i3 = m + 41;
                    f98o = i3 % 128;
                    int i4 = i3 % 2;
                    this.g = 0;
                    int i5 = f98o + 41;
                    m = i5 % 128;
                    int i6 = i5 % 2;
                    break;
            }
            int i7 = m + 77;
            f98o = i7 % 128;
            int i8 = i7 % 2;
        }
    }

    @Override // o.q.c
    public final String b() {
        int i = f98o + 63;
        m = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        p("쳣\udac8첓쾐歵\ude6d왡敔\udf96쵲퍼䩼\ueab0\uf04d\ue05f彼\uf597\ue75d赜ⱨ", TextUtils.indexOf((CharSequence) "", '0') + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = f98o + 65;
        m = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.q.c
    public final void d(o.eg.b bVar) {
        int i = f98o + 65;
        m = i % 128;
        int i2 = i % 2;
        if (bVar == null) {
            g.c();
            Object[] objArr = new Object[1];
            p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", ViewConfiguration.getMaximumFlingVelocity() >> 16, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            p("洧ڌ浕䚔떵ȭ佯뮍繘ᄪ婩钪䭨Ⰶ楚膰呰㬍ў\uf2a8ℓ埡ጳ\uef57㉗抵\u2e6c\ud847Ἠ熈㴟㕶\ue839賞젅♮\uf5d2鮺\ue4fdግ웞뚷\uf3e2\u0c49폷얍軎礪볾킋鷘樬覃", ViewConfiguration.getJumpTapTimeout() >> 16, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
        }
        Object[] objArr3 = new Object[1];
        p("誒먙諢㱠붥뺼㖑뎄駧궣₌鲺곇邐Ꮍ覥돃螙纱絛욷\ueb5c槅\ue746햷\ude63哌큺\uf89d쵈䟦㵽྇た", View.resolveSizeAndState(0, 0, 0), objArr3);
        BigDecimal o2 = bVar.o(((String) objArr3[0]).intern());
        switch (o2 == null) {
            case false:
                if (this.d != null) {
                    g.c();
                    Object[] objArr4 = new Object[1];
                    p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", ViewConfiguration.getFadingEdgeLength() >> 16, objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr5 = new Object[1];
                    p("敻ឦ攉苢椥ጇ謙朝瘄\u0000鸟䠺䌴㴬괬崠尬⨧쀨⸸⥏䛋흅㏇㨋玟\uea1aӋ\u177e惱量\ue9e6\ue079鷻\u0c74\ufafe\ufddb誒₋쾐캆ꞗ㞔킍\udba8퓢䪩ꖼ뒦솧妶뚸臏﹋泜驌銋\ueb53菗潖濮ᡬ雾瀩磨㕽ꗯ䕷吏∇렘噉ℜ弛켎㬑㉻䰴\ue22bథ༾礷\uf17aᅣ\u181b", (-1) - ((byte) KeyEvent.getModifierMetaStateMask()), objArr5);
                    g.d(intern2, sb.append(((String) objArr5[0]).intern()).append(o2.doubleValue()).toString());
                    this.i = o2;
                    int i3 = m + 67;
                    f98o = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                }
                break;
        }
        Object[] objArr6 = new Object[1];
        q("䫿흞鵋ლ\ua7e9ᰚ荬ↈᒮΎ\uf4e8㜚먼\u0e85㟣릻䓁\ue3fc䂣惰", 19 - MotionEvent.axisFromString(""), objArr6);
        Integer j2 = bVar.j(((String) objArr6[0]).intern());
        switch (j2 == null) {
            case false:
                if (this.h != null) {
                    g.c();
                    Object[] objArr7 = new Object[1];
                    p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", (-1) - ExpandableListView.getPackedPositionChild(0L), objArr7);
                    String intern3 = ((String) objArr7[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr8 = new Object[1];
                    p("㏥훲㎗癓꒫퉓羨ꪓₚ셔檮薴ᖪﱸ妝邮લ\ueb73㒙\ue3b6翑螟⏴﹉沕닋ẫ쥅䇠ꆥා⑨뛧岯\uf8c5㝰ꭅ䯆퐺Ȟ領曃쌥ᴃ贶ᖶ븕栲\ue238ä괎笥휅㼕顴埒쑛⨒睮ꋅ㤥\ud921扒뷳\u2e7d\uf466兝裶ʉ\ue343䲾鯇矏鸆", TextUtils.indexOf("", ""), objArr8);
                    g.d(intern3, sb2.append(((String) objArr8[0]).intern()).append(j2).toString());
                    this.g = j2;
                    break;
                }
                break;
        }
    }

    @Override // o.q.c
    public final o.eg.b e() {
        String intern;
        Integer num;
        String intern2;
        double doubleValue;
        try {
            o.eg.b bVar = new o.eg.b();
            char c = this.d != null ? (char) 14 : '\\';
            char c2 = Typography.less;
            switch (c) {
                case 14:
                    int i = f98o + 13;
                    m = i % 128;
                    if (i % 2 == 0) {
                        Object[] objArr = new Object[1];
                        p("誒먙諢㱠붥뺼㖑뎄駧궣₌鲺곇邐Ꮍ覥돃螙纱絛욷\ueb5c槅\ue746햷\ude63哌큺\uf89d쵈䟦㵽྇た", ViewConfiguration.getDoubleTapTimeout() >> 60, objArr);
                        intern2 = ((String) objArr[0]).intern();
                        doubleValue = this.i.doubleValue();
                    } else {
                        Object[] objArr2 = new Object[1];
                        p("誒먙諢㱠붥뺼㖑뎄駧궣₌鲺곇邐Ꮍ覥돃螙纱絛욷\ueb5c槅\ue746햷\ude63哌큺\uf89d쵈䟦㵽྇た", ViewConfiguration.getDoubleTapTimeout() >> 16, objArr2);
                        intern2 = ((String) objArr2[0]).intern();
                        doubleValue = this.i.doubleValue();
                    }
                    bVar.c(intern2, doubleValue);
                    break;
            }
            if (this.h != null) {
                int i2 = m + 19;
                f98o = i2 % 128;
                if (i2 % 2 == 0) {
                    c2 = 'M';
                }
                switch (c2) {
                    case 'M':
                        Object[] objArr3 = new Object[1];
                        q("䫿흞鵋ლ\ua7e9ᰚ荬ↈᒮΎ\uf4e8㜚먼\u0e85㟣릻䓁\ue3fc䂣惰", 19 - MotionEvent.axisFromString(""), objArr3);
                        intern = ((String) objArr3[0]).intern();
                        num = this.g;
                        break;
                    default:
                        Object[] objArr4 = new Object[1];
                        q("䫿흞鵋ლ\ua7e9ᰚ荬ↈᒮΎ\uf4e8㜚먼\u0e85㟣릻䓁\ue3fc䂣惰", 64 >> MotionEvent.axisFromString(""), objArr4);
                        intern = ((String) objArr4[0]).intern();
                        num = this.g;
                        break;
                }
                bVar.d(intern, num);
            }
            g.c();
            Object[] objArr5 = new Object[1];
            p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", (Process.getThreadPriority(0) + 20) >> 6, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr6 = new Object[1];
            q("쭋\uf20b᱁睾熗\ue785䨌\uee03쇣㐹칸겅ᤧꓻ愑氨䨌\uee03\uf4bd䴻꣢㯁雼膈紒ｎ\uf011巄ᡛ帜", 30 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr6);
            g.d(intern3, sb.append(((String) objArr6[0]).intern()).append(bVar).toString());
            return bVar;
        } catch (o.eg.d e2) {
            g.c();
            Object[] objArr7 = new Object[1];
            p("廔诎庄佉\ua7e6轫䚸꧇䶡鱴厥蛯碇ꅋ悆鏯枠뙛අ\ue0fb", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            q("쭋\uf20b᱁睾熗\ue785䨌\uee03쇣㐹칸겅ᤧꓻ愑氨䨌\uee03", 19 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr8);
            g.a(intern4, ((String) objArr8[0]).intern(), e2);
            return new o.eg.b();
        }
    }

    public final short a() {
        int i = m + 41;
        f98o = i % 128;
        switch (i % 2 != 0 ? '\n' : (char) 22) {
            case '\n':
                int i2 = 1 / 0;
                return this.b;
            default:
                return this.b;
        }
    }

    public final BigDecimal d() {
        int i = f98o;
        int i2 = i + 67;
        m = i2 % 128;
        int i3 = i2 % 2;
        BigDecimal bigDecimal = this.c;
        int i4 = i + 39;
        m = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bigDecimal;
        }
    }

    public final BigDecimal c() {
        int i = f98o + 63;
        int i2 = i % 128;
        m = i2;
        int i3 = i % 2;
        BigDecimal bigDecimal = this.d;
        int i4 = i2 + 63;
        f98o = i4 % 128;
        int i5 = i4 % 2;
        return bigDecimal;
    }

    public final Integer h() {
        int i = f98o + 51;
        int i2 = i % 128;
        m = i2;
        switch (i % 2 == 0) {
            case false:
                Integer num = this.h;
                int i3 = i2 + 31;
                f98o = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return num;
                }
            default:
                throw null;
        }
    }

    public final BigDecimal i() {
        int i = m + 83;
        int i2 = i % 128;
        f98o = i2;
        int i3 = i % 2;
        BigDecimal bigDecimal = this.i;
        int i4 = i2 + 83;
        m = i4 % 128;
        switch (i4 % 2 == 0 ? 'U' : 'S') {
            case Opcodes.CASTORE /* 85 */:
                int i5 = 97 / 0;
                return bigDecimal;
            default:
                return bigDecimal;
        }
    }

    public final Integer f() {
        int i = f98o + Opcodes.LMUL;
        m = i % 128;
        switch (i % 2 == 0 ? 'G' : Typography.quote) {
            case 'G':
                throw null;
            default:
                return this.g;
        }
    }

    public final BigDecimal j() {
        BigDecimal max;
        Integer num = this.h;
        if (num != null) {
            int i = m + 19;
            f98o = i % 128;
            int i2 = i % 2;
            switch (num.intValue() <= this.g.intValue() ? '\f' : (char) 16) {
                case '\f':
                    max = BigDecimal.ZERO;
                    int i3 = m + Opcodes.LSUB;
                    f98o = i3 % 128;
                    int i4 = i3 % 2;
                    break;
            }
            int i5 = m + 109;
            f98o = i5 % 128;
            int i6 = i5 % 2;
            return max;
        }
        BigDecimal bigDecimal = this.d;
        switch (bigDecimal == null) {
            case false:
                BigDecimal subtract = bigDecimal.subtract(this.i);
                BigDecimal bigDecimal2 = this.c;
                switch (bigDecimal2 == null ? '\t' : Typography.dollar) {
                    case '$':
                        max = subtract.min(bigDecimal2).max(BigDecimal.ZERO);
                        break;
                    default:
                        max = subtract.max(BigDecimal.ZERO);
                        break;
                }
            default:
                max = this.c;
                break;
        }
        int i52 = m + 109;
        f98o = i52 % 128;
        int i62 = i52 % 2;
        return max;
    }

    public final m g() {
        int i = m;
        int i2 = i + 47;
        f98o = i2 % 128;
        int i3 = i2 % 2;
        m mVar = this.a;
        int i4 = i + 83;
        f98o = i4 % 128;
        int i5 = i4 % 2;
        return mVar;
    }

    /* JADX WARN: Removed duplicated region for block: B:43:0x0091 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 1
            if (r4 != r5) goto L5
            return r0
        L5:
            r1 = 0
            if (r5 == 0) goto L92
            int r2 = o.q.d.f98o
            int r2 = r2 + 73
            int r3 = r2 % 128
            o.q.d.m = r3
            int r2 = r2 % 2
            java.lang.Class r2 = r4.getClass()
            java.lang.Class r3 = r5.getClass()
            if (r2 == r3) goto L1f
            r2 = 38
            goto L21
        L1f:
            r2 = 43
        L21:
            switch(r2) {
                case 43: goto L26;
                default: goto L24;
            }
        L24:
            goto L92
        L26:
            o.q.d r5 = (o.q.d) r5
            short r2 = r4.b
            short r3 = r5.b
            if (r2 != r3) goto L91
            java.math.BigDecimal r2 = r4.c
            java.math.BigDecimal r3 = r5.c
            boolean r2 = java.util.Objects.equals(r2, r3)
            if (r2 == 0) goto L3a
            r2 = r0
            goto L3b
        L3a:
            r2 = r1
        L3b:
            switch(r2) {
                case 0: goto L91;
                default: goto L3e;
            }
        L3e:
            java.math.BigDecimal r2 = r4.d
            java.math.BigDecimal r3 = r5.d
            boolean r2 = java.util.Objects.equals(r2, r3)
            if (r2 == 0) goto L4b
            r2 = 83
            goto L4d
        L4b:
            r2 = 76
        L4d:
            switch(r2) {
                case 76: goto L91;
                default: goto L50;
            }
        L50:
            int r2 = o.q.d.m
            int r2 = r2 + 13
            int r3 = r2 % 128
            o.q.d.f98o = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L86
            java.lang.Integer r2 = r4.h
            java.lang.Integer r3 = r5.h
            boolean r2 = java.util.Objects.equals(r2, r3)
            if (r2 == 0) goto L91
            o.i.m r2 = r4.a
            o.i.m r5 = r5.a
            if (r2 != r5) goto L6f
            r5 = 66
            goto L71
        L6f:
            r5 = 35
        L71:
            switch(r5) {
                case 35: goto L91;
                default: goto L74;
            }
        L74:
            int r5 = o.q.d.m
            int r5 = r5 + 101
            int r1 = r5 % 128
            o.q.d.f98o = r1
            int r5 = r5 % 2
            if (r5 == 0) goto L83
            r5 = 55
            goto L85
        L83:
            r5 = 84
        L85:
            return r0
        L86:
            java.lang.Integer r0 = r4.h
            java.lang.Integer r5 = r5.h
            java.util.Objects.equals(r0, r5)
            r5 = 0
            throw r5     // Catch: java.lang.Throwable -> L8f
        L8f:
            r5 = move-exception
            throw r5
        L91:
            return r1
        L92:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.d.equals(java.lang.Object):boolean");
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        p("䘦怘䙶䑔ㆺ撽䶥㾛啓瞢墸Ⴓ恵䪝殛ֳ罒嶍ژ皧ਝㅯᇨ歄ᤓѢⳫ屒㐮ᜁ", ViewConfiguration.getScrollBarFadeDuration() >> 16, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append((int) this.b);
        Object[] objArr2 = new Object[1];
        q("\udbe5悎뺶ᒘ练ӯ䐌ꄾᡞ摼䓁\ue3fcᙎ澮㟣릻䓁\ue3fc긓ʹ", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(this.c);
        Object[] objArr3 = new Object[1];
        p("\uf886\u1af5\uf8aa⺜拵ḑ❻泍\uebfeൄ㉶䏪\uded3っņ囼쇘❢汝◚뒳䮁笤㠖Ꞥ纕䘐༐誖涴啉", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(this.a).append('}').toString();
        int i = m + 71;
        f98o = i % 128;
        int i2 = i % 2;
        return obj;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 370
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.d.p(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 562
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.q.d.q(java.lang.String, int, java.lang.Object[]):void");
    }
}
