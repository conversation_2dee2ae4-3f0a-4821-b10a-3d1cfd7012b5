package o.z;

import com.esotericsoftware.asm.Opcodes;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c c;
    private static final /* synthetic */ c[] d;
    public static final c e;
    private static int h;
    private static int i;
    private static char[] j;
    private final String b;

    static void d() {
        j = new char[]{50825, 50782, 50770, 50799, 50798, 50793, 50799, 50781, 50772, 50787, 50769, 50775, 50795, 50796, 50799, 50934, 50876, 50848, 50877, 50876, 50879, 50877, 50868, 50870, 50874, 50865, 50765, 50765, 50869, 50873, 50850, 50877, 50704, 50706, 50701, 50706, 50733, 50711, 50728, 50719, 50713, 50711, 50714, 50711, 50728, 50733, 50802, 50734, 50715, 50730, 50734, 50715, 50697, 50715, 50706, 50711, 50922, 50819, 50817, 50819, 50823, 50831, 50827, 50826, 50826, 50819, 50844, 50819, 50843, 50837, 50822, 50816, 50841, 50822, 50817, 51139, 51165, 51163, 51166, 51163, 51180, 51153, 50742, 51154, 51167, 51182, 51154, 51167, 51149, 51167, 51158, 51163, 51156, 51158, 50737, 51181, 51177, 50746, 51158, 51153, 51163, 51180, 50761, 51139, 51166, 51167, 51137, 51137, 51139, 51163, 51154, 51165, 51142, 51160, 51167, 51162, 51153, 51163, 51160, 51161, 51138, 51162, 51155, 51158, 51156, 51138, 51162, 51155, 51164, 51165, 51156, 51167, 51165, 51152, 51158, 51166, 51163, 51166, 51137, 51137, 51142, 51146, 51138, 51166, 51164, 51166, 51158, 51156, 51160, 51159};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.z.c.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r8 = r8 + 66
            int r6 = r6 + 5
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r7
            r3 = r8
            r4 = r2
            r7 = r6
            goto L2e
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r6 = r6 + 1
            int r4 = r3 + 1
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r3 = -r3
            int r6 = r6 + r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.z.c.g(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
        $$b = 64;
    }

    private static /* synthetic */ c[] a() {
        int i2 = i + 3;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        c[] cVarArr = {c, a, e};
        int i5 = i3 + Opcodes.DSUB;
        i = i5 % 128;
        int i6 = i5 % 2;
        return cVarArr;
    }

    public static c valueOf(String str) {
        int i2 = i + 31;
        h = i2 % 128;
        int i3 = i2 % 2;
        c cVar = (c) Enum.valueOf(c.class, str);
        int i4 = h + Opcodes.DMUL;
        i = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 7 : 'J') {
            case 'J':
                return cVar;
            default:
                int i5 = 6 / 0;
                return cVar;
        }
    }

    public static c[] values() {
        int i2 = h + 63;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.greater : (char) 0) {
            case '>':
                int i3 = 6 / 0;
                return (c[]) d.clone();
            default:
                return (c[]) d.clone();
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        d();
        Object[] objArr = new Object[1];
        f("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001", new int[]{0, 15, 57, 0}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001", new int[]{15, 17, 39, 0}, false, objArr2);
        c = new c(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f(null, new int[]{32, 24, Opcodes.LREM, 3}, true, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        f("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000", new int[]{56, 19, 0, 0}, true, objArr4);
        a = new c(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        f(null, new int[]{75, 27, Opcodes.LRETURN, 20}, true, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        f("\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{102, 48, Opcodes.MULTIANEWARRAY, 0}, false, objArr6);
        e = new c(intern3, 2, ((String) objArr6[0]).intern());
        d = a();
        int i2 = i + 1;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    private c(String str, int i2, String str2) {
        this.b = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = i + 71;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return this.b;
        }
    }

    /* renamed from: o.z.c$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\z\c$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] a;
        private static int b = 0;
        private static int d;

        static {
            d = 1;
            int[] iArr = new int[c.values().length];
            a = iArr;
            try {
                iArr[c.c.ordinal()] = 1;
                int i = b;
                int i2 = (i ^ 83) + ((i & 83) << 1);
                d = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                a[c.a.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 45) + ((i3 & 45) << 1);
                d = i4 % 128;
                if (i4 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[c.e.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    public static boolean c(c cVar) {
        int i2 = i + Opcodes.DDIV;
        h = i2 % 128;
        int i3 = i2 % 2;
        switch (AnonymousClass5.a[cVar.ordinal()]) {
            case 1:
                int i4 = h + 7;
                i = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        return true;
                    default:
                        int i5 = 15 / 0;
                        return true;
                }
            default:
                return false;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:104:0x02e8, code lost:
    
        r1 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r20, int[] r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 830
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.z.c.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
