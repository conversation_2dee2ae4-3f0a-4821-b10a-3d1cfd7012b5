package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.an.e;
import o.an.f;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\j.smali */
public final class j extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static char[] m;
    private static int p;
    private static boolean q;
    private static boolean r;
    private static int s;
    e.d h;
    private final boolean i;
    private final String l;

    /* renamed from: o, reason: collision with root package name */
    private String f106o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        s = 0;
        p = 1;
        s();
        PointF.length(0.0f, 0.0f);
        int i = s + 39;
        p = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{114, -113, -41, 111};
        $$e = Opcodes.DDIV;
    }

    static void s() {
        m = new char[]{61676, 61442, 61692, 61458, 61453, 61660, 61694, 61443, 61679, 61452, 61447, 61683, 61448, 61444, 61449, 61677, 61454, 61459, 61631, 61667, 61446, 61440, 61451, 61695, 61455, 61682, 61450, 61693, 61456, 61647, 61646, 61645, 61655, 61670, 61642, 61463, 61643};
        q = true;
        r = true;
        k = 782102687;
    }

    private static void v(int i, short s2, byte b, Object[] objArr) {
        int i2 = s2 + Opcodes.LNEG;
        int i3 = 3 - (b * 2);
        byte[] bArr = $$d;
        int i4 = 1 - (i * 3);
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            int i7 = i6 + (-i3);
            i3 = i3;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i2 = i7;
            i6 = i6;
        }
        while (true) {
            int i8 = i5 + 1;
            bArr2[i8] = (byte) i2;
            int i9 = i3 + 1;
            if (i8 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i10 = i2;
            int i11 = i6;
            Object[] objArr2 = objArr;
            byte[] bArr3 = bArr2;
            byte[] bArr4 = bArr;
            int i12 = i10 + (-bArr[i9]);
            i3 = i9;
            objArr = objArr2;
            bArr = bArr4;
            bArr2 = bArr3;
            i5 = i8;
            i2 = i12;
            i6 = i11;
        }
    }

    static /* synthetic */ o.p.g a(j jVar) {
        int i = s + 21;
        p = i % 128;
        int i2 = i % 2;
        o.p.g l = jVar.l();
        int i3 = s + Opcodes.DDIV;
        p = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ o.p.g b(j jVar) {
        int i = p + 53;
        s = i % 128;
        int i2 = i % 2;
        o.p.g l = jVar.l();
        int i3 = p + 79;
        s = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ o.p.g c(j jVar) {
        int i = s + 91;
        p = i % 128;
        char c = i % 2 == 0 ? (char) 19 : (char) 20;
        o.p.g l = jVar.l();
        switch (c) {
            default:
                int i2 = 94 / 0;
            case 20:
                return l;
        }
    }

    static /* synthetic */ o.p.g d(j jVar) {
        int i = s + Opcodes.DNEG;
        p = i % 128;
        int i2 = i % 2;
        o.p.g l = jVar.l();
        int i3 = s + 29;
        p = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return l;
            default:
                int i4 = 21 / 0;
                return l;
        }
    }

    static /* synthetic */ void e(j jVar) {
        int i = p + 59;
        s = i % 128;
        int i2 = i % 2;
        jVar.n();
        int i3 = p + 95;
        s = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                int i4 = 86 / 0;
                return;
            default:
                return;
        }
    }

    static /* synthetic */ o.p.g f(j jVar) {
        int i = s + 59;
        p = i % 128;
        switch (i % 2 == 0) {
            case false:
                return jVar.l();
            default:
                jVar.l();
                throw null;
        }
    }

    public j(String str, o.eo.e eVar, boolean z, String str2) {
        super(str, eVar);
        this.i = z;
        this.l = str2;
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = p;
        int i2 = i + 3;
        s = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                if (this.i) {
                    int i3 = i + 17;
                    s = i3 % 128;
                    switch (i3 % 2 != 0 ? 'b' : '[') {
                        case Opcodes.FADD /* 98 */:
                            int i4 = 51 / 0;
                            return;
                        default:
                            return;
                    }
                }
                WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                Object[] objArr = new Object[1];
                u(null, 127 - TextUtils.getTrimmedLength(""), null, "\u0085\u008d\u0092\u008a\u0082\u0084\u0091\u0082\u0090\u008f\u0082\u008e\u008d\u008c\u008d\u008c\u008b\u008a\u0084\u0089\u0088\u0085\u0087\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                u(null, 127 - (ViewConfiguration.getPressedStateDuration() >> 16), null, "\u0098\u0093\u0088\u0085\u0087\u0086\u0097\u0087\u0092\u0095\u0096\u0095\u0094\u0093\u0082\u008b\u008c", objArr2);
                StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(((d) this).n.e());
                Object[] objArr3 = new Object[1];
                u(null, (-16777089) - Color.rgb(0, 0, 0), null, "\u0085\u008d\u0092\u008a\u0082\u0084\u0091\u0082\u0085\u0093\u008f\u0082\u008e\u008d\u0092\u0093\u008d\u0092\u0093\u008b\u008a\u0084\u0099\u0093\u0092\u0085\u008d\u0099\u0099\u0084\u008a\u0093\u0092\u008d\u008f\u0093\u008a\u0082\u008d\u0088\u0093\u0098", objArr3);
                throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
        }
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i = s + 97;
        p = i % 128;
        switch (i % 2 == 0 ? '#' : '\f') {
            case '\f':
                Object[] objArr = new Object[1];
                u(null, 175 - AndroidCharacter.getMirror('0'), null, "\u0085\u008d\u0092\u008a\u0082\u0084\u0091\u0082\u0090\u008f\u0082\u008e\u008d\u008c\u008d\u008c\u008b\u008a\u0084\u0089\u0088\u0085\u0087\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                u(null, AndroidCharacter.getMirror('2') + 28868, null, "\u0085\u008d\u0092\u008a\u0082\u0084\u0091\u0082\u0090\u008f\u0082\u008e\u008d\u008c\u008d\u008c\u008b\u008a\u0084\u0089\u0088\u0085\u0087\u0086\u0082\u0085\u0084\u0083\u0082\u0081", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* JADX WARN: Code restructure failed: missing block: B:8:0x0022, code lost:
    
        if (r8.length() <= 128) goto L11;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(java.lang.String r8) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r7 = this;
            if (r8 == 0) goto L6
            r0 = 56
            goto L8
        L6:
            r0 = 94
        L8:
            r1 = 0
            switch(r0) {
                case 94: goto L24;
                default: goto Lc;
            }
        Lc:
            int r0 = r8.length()
            if (r0 < 0) goto L42
            int r0 = o.v.j.p
            int r0 = r0 + 87
            int r2 = r0 % 128
            o.v.j.s = r2
            int r0 = r0 % 2
            int r0 = r8.length()
            r2 = 128(0x80, float:1.794E-43)
            if (r0 > r2) goto L42
        L24:
            r7.f106o = r8
            int r8 = o.v.j.p
            int r8 = r8 + 91
            int r0 = r8 % 128
            o.v.j.s = r0
            int r8 = r8 % 2
            if (r8 == 0) goto L35
            r8 = 13
            goto L37
        L35:
            r8 = 78
        L37:
            switch(r8) {
                case 78: goto L3b;
                default: goto L3a;
            }
        L3a:
            goto L3c
        L3b:
            return
        L3c:
            r8 = 77
            int r8 = r8 / r1
            return
        L40:
            r8 = move-exception
            throw r8
        L42:
            fr.antelop.sdk.exception.WalletValidationException r8 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r0 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            int r2 = android.view.View.resolveSizeAndState(r1, r1, r1)
            int r2 = r2 + 127
            r3 = 1
            java.lang.Object[] r4 = new java.lang.Object[r3]
            r5 = 0
            java.lang.String r6 = "\u0097\u0085\u009a\u008f\u0085\u0084\u0092\u0082\u0090"
            u(r5, r2, r5, r6, r4)
            r2 = r4[r1]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            int r4 = android.view.KeyEvent.getMaxKeyCode()
            int r4 = r4 >> 16
            int r4 = r4 + 127
            java.lang.Object[] r3 = new java.lang.Object[r3]
            java.lang.String r6 = "\u0096\u008f\u008d\u0097\u0093¡ \u009f\u0093\u0088\u008f\u0087\u0093\u009e\u0093\u008f\u0082\u0082\u009d\u0092\u0082\u009c\u0093\u0082\u009c\u0093\u0092\u008a\u0084\u009b\u0093\u0098\u0097\u0085\u009a\u008f\u0085\u0084\u0092\u0082\u0090\u0098"
            u(r5, r4, r5, r6, r3)
            r1 = r3[r1]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r8.<init>(r0, r2, r1)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.j.e(java.lang.String):void");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:13:0x0091. Please report as an issue. */
    @Override // o.p.h
    public final void a(Context context, o.ei.c cVar, o.h.d dVar) {
        switch (context == null ? 'U' : 'X') {
            case Opcodes.CASTORE /* 85 */:
                int i = p + 19;
                s = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                u(null, MotionEvent.axisFromString("") + 128, null, "\u008a\u008a\u0082\u0083\u008d\u0085\u0089\u0085\u008d\u0092\u008a\u0082\u0084\u0091\u0082\u0090\u008f\u0082\u008e\u008d\u008c\u008d\u008c\u008b\u008a\u0084\u0089\u0088\u0085\u0087\u0086\u0097\u0087\u0092\u0095\u0096\u0095\u0094\u0082\u0085\u0084\u0083\u0082\u0081\u0085\u0082\u008f\u008f¢", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                u(null, 127 - View.MeasureSpec.getSize(0), null, "\u0092\u0085\u008d\u009c\u0087\u0093¥\u0097\u0097\u0084\u008f\u0093\u0092¤\u0082\u0092\u008f\u008d\u0083\u0093£\u0093\u008a\u008a\u0082\u0083\u008d\u0085\u0089\u008f\u0084\u0085", objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                if (l() != null) {
                    l().onError(new o.bv.c(AntelopErrorCode.InternalError));
                    int i3 = s + 47;
                    p = i3 % 128;
                    switch (i3 % 2 == 0 ? (char) 23 : (char) 2) {
                    }
                }
                break;
            default:
                new o.an.f(context, new f.b<e.d>() { // from class: o.v.j.4
                    private static int $10 = 0;
                    private static int $11 = 1;
                    private static int g = 0;
                    private static int h = 1;
                    private static char b = 43312;
                    private static char c = 24709;
                    private static char e = 21581;
                    private static char d = 12514;

                    @Override // o.an.f.b
                    public final /* synthetic */ void c(e.d dVar2) {
                        int i4 = h + Opcodes.LSUB;
                        g = i4 % 128;
                        int i5 = i4 % 2;
                        e(dVar2);
                        int i6 = g + 85;
                        h = i6 % 128;
                        switch (i6 % 2 == 0 ? ',' : (char) 30) {
                            case 30:
                                return;
                            default:
                                int i7 = 5 / 0;
                                return;
                        }
                    }

                    private void e(e.d dVar2) {
                        int i4 = g + 5;
                        h = i4 % 128;
                        int i5 = i4 % 2;
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        f("ꤜ\ue2fb킵ꜟ좹\ueb15\ue971鼅糍唦秿띒羄鉍治두\uf68c\ue102쮗ᕓ麜ߖ憧\u0ee8ߍ鑕욑뽪욑뽪ヵ韈\uf205伤滋예\u0ee3ฦ⩊펖\u2d73㊻\udb83⬥椡\ue123遉착䲴삅", 49 - ((Process.getThreadPriority(0) + 20) >> 6), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f("\u0e3cՒ憧\u0ee8ߍ鑕쮗ᕓ麜ߖ叿ඕ御ࢳ遉착䲴삅", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 17, objArr4);
                        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                        j.this.h = dVar2;
                        switch (j.a(j.this) != null ? '7' : Typography.less) {
                            case '7':
                                int i6 = h + Opcodes.DSUB;
                                g = i6 % 128;
                                boolean z = i6 % 2 != 0;
                                j.b(j.this).onProcessSuccess();
                                switch (z) {
                                    case true:
                                        throw null;
                                    default:
                                        return;
                                }
                            default:
                                return;
                        }
                    }

                    @Override // o.an.f.b
                    public final void c(o.bb.d dVar2) {
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        f("ꤜ\ue2fb킵ꜟ좹\ueb15\ue971鼅糍唦秿띒羄鉍治두\uf68c\ue102쮗ᕓ麜ߖ憧\u0ee8ߍ鑕욑뽪욑뽪ヵ韈\uf205伤滋예\u0ee3ฦ⩊펖\u2d73㊻\udb83⬥椡\ue123遉착䲴삅", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 49, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f("\u0e3cՒ憧\u0ee8ߍ鑕쮗ᕓ麜ߖષ\uee56ቹ煮糍唦返㻮", 17 - Color.alpha(0), objArr4);
                        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                        o.bv.c c2 = o.bv.c.c(dVar2);
                        switch (j.d(j.this) == null ? '\n' : (char) 16) {
                            case '\n':
                                return;
                            default:
                                int i4 = h + 83;
                                g = i4 % 128;
                                if (i4 % 2 != 0) {
                                    dVar2.d();
                                    o.bb.a aVar = o.bb.a.aA;
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                                }
                                switch (dVar2.d() != o.bb.a.aA) {
                                    case true:
                                        j.f(j.this).onError(c2);
                                        return;
                                    default:
                                        int i5 = h + Opcodes.DDIV;
                                        g = i5 % 128;
                                        int i6 = i5 % 2;
                                        j.e(j.this);
                                        j.c(j.this).onAuthenticationDeclined();
                                        return;
                                }
                        }
                    }

                    /* JADX WARN: Code restructure failed: missing block: B:10:0x003f, code lost:
                    
                        if (r4.b >= r0.length) goto L20;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:11:0x0041, code lost:
                    
                        r7 = 'c';
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:12:0x0046, code lost:
                    
                        switch(r7) {
                            case 99: goto L24;
                            default: goto L86;
                        };
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:13:0x0054, code lost:
                    
                        r7 = o.v.j.AnonymousClass4.$11 + 17;
                        o.v.j.AnonymousClass4.$10 = r7 % 128;
                        r9 = 58224;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:14:0x0061, code lost:
                    
                        if ((r7 % r1) == 0) goto L27;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:15:0x0063, code lost:
                    
                        r6[r3] = r0[r4.b];
                        r6[1] = r0[r4.b / 1];
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:16:0x007e, code lost:
                    
                        r7 = r3;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:18:0x0082, code lost:
                    
                        if (r7 >= 16) goto L32;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:19:0x0084, code lost:
                    
                        r11 = 1;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:21:0x008c, code lost:
                    
                        switch(r11) {
                            case 0: goto L87;
                            default: goto L35;
                        };
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:22:0x008f, code lost:
                    
                        r11 = o.v.j.AnonymousClass4.$10 + 79;
                        o.v.j.AnonymousClass4.$11 = r11 % 128;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:23:0x0098, code lost:
                    
                        if ((r11 % r1) != 0) goto L50;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:24:0x0101, code lost:
                    
                        r11 = r6[1];
                        r15 = r6[r3];
                        r2 = (r15 + r9) ^ ((r15 << 4) + ((char) (o.v.j.AnonymousClass4.c ^ 8439748517800462901L)));
                        r12 = r15 >>> 5;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:26:0x011e, code lost:
                    
                        r3 = new java.lang.Object[4];
                        r3[r8] = java.lang.Integer.valueOf(o.v.j.AnonymousClass4.e);
                        r3[r1] = java.lang.Integer.valueOf(r12);
                        r3[1] = java.lang.Integer.valueOf(r2);
                        r3[0] = java.lang.Integer.valueOf(r11);
                        r2 = o.e.a.s.get(-1512468642);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:28:0x0148, code lost:
                    
                        if (r2 == null) goto L55;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:30:0x014c, code lost:
                    
                        r2 = (java.lang.Class) o.e.a.c(11 - android.view.KeyEvent.normalizeMetaState(0), (char) android.text.TextUtils.indexOf("", ""), 602 - (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) == 0 ? 0 : -1)));
                        r11 = new java.lang.Class[4];
                        r11[0] = java.lang.Integer.TYPE;
                        r11[1] = java.lang.Integer.TYPE;
                        r11[r1] = java.lang.Integer.TYPE;
                        r11[r8] = java.lang.Integer.TYPE;
                        r2 = r2.getMethod("C", r11);
                        o.e.a.s.put(-1512468642, r2);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:31:0x018d, code lost:
                    
                        r1 = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r3)).charValue();
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:32:0x019a, code lost:
                    
                        r6[1] = r1;
                        r21 = r9;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:34:0x01b2, code lost:
                    
                        r9 = new java.lang.Object[]{java.lang.Integer.valueOf(r6[0]), java.lang.Integer.valueOf((r1 + r9) ^ ((r1 << 4) + ((char) (o.v.j.AnonymousClass4.d ^ 8439748517800462901L)))), java.lang.Integer.valueOf(r1 >>> 5), java.lang.Integer.valueOf(o.v.j.AnonymousClass4.b)};
                        r1 = o.e.a.s.get(-1512468642);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:35:0x01dc, code lost:
                    
                        if (r1 == null) goto L62;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:36:0x01de, code lost:
                    
                        r8 = 2;
                        r11 = 3;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:38:0x022e, code lost:
                    
                        r6[0] = ((java.lang.Character) ((java.lang.reflect.Method) r1).invoke(null, r9)).charValue();
                        r9 = r21 - 40503;
                        r7 = r7 + 1;
                        r1 = r8;
                        r8 = r11;
                        r3 = 0;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:39:0x01e1, code lost:
                    
                        r8 = 2;
                        r11 = 3;
                        r1 = ((java.lang.Class) o.e.a.c(11 - android.view.View.MeasureSpec.getMode(0), (char) (android.text.TextUtils.lastIndexOf("", '0', 0) + 1), android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0) + 604)).getMethod("C", java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
                        o.e.a.s.put(-1512468642, r1);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:41:0x023d, code lost:
                    
                        r0 = move-exception;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:42:0x023e, code lost:
                    
                        r1 = r0.getCause();
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:43:0x0242, code lost:
                    
                        if (r1 != null) goto L68;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:44:0x0244, code lost:
                    
                        throw r1;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:46:0x0245, code lost:
                    
                        throw r0;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:48:0x0246, code lost:
                    
                        r0 = move-exception;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:49:0x0247, code lost:
                    
                        r1 = r0.getCause();
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:50:0x024b, code lost:
                    
                        if (r1 != null) goto L73;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:51:0x024d, code lost:
                    
                        throw r1;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:52:0x024e, code lost:
                    
                        throw r0;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:54:0x009b, code lost:
                    
                        r5[r4.b] = r6[r3];
                        r5[r4.b + 1] = r6[1];
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:56:0x00a8, code lost:
                    
                        r7 = new java.lang.Object[]{r4, r4};
                        r9 = o.e.a.s.get(2062727845);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:57:0x00b9, code lost:
                    
                        if (r9 == null) goto L42;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:59:0x00f1, code lost:
                    
                        ((java.lang.reflect.Method) r9).invoke(null, r7);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:60:0x00bc, code lost:
                    
                        r9 = (java.lang.Class) o.e.a.c(android.text.TextUtils.lastIndexOf("", '0') + 11, (char) (android.graphics.ImageFormat.getBitsPerPixel(r3) + 30726), 614 - (android.view.ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                        r14 = new java.lang.Class[r1];
                        r14[r3] = java.lang.Object.class;
                        r14[1] = java.lang.Object.class;
                        r9 = r9.getMethod("A", r14);
                        o.e.a.s.put(2062727845, r9);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:62:0x00f8, code lost:
                    
                        r0 = move-exception;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:63:0x00f9, code lost:
                    
                        r1 = r0.getCause();
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:64:0x00fd, code lost:
                    
                        if (r1 != null) goto L48;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:65:0x00ff, code lost:
                    
                        throw r1;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:66:0x0100, code lost:
                    
                        throw r0;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:67:0x0086, code lost:
                    
                        r11 = r3;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:68:0x0071, code lost:
                    
                        r6[r3] = r0[r4.b];
                        r6[1] = r0[r4.b + 1];
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
                    
                        if (r24 != null) goto L14;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:70:0x0049, code lost:
                    
                        r26[0] = new java.lang.String(r5, 0, r25);
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:71:0x0053, code lost:
                    
                        return;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:72:0x0044, code lost:
                    
                        r7 = 'E';
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:73:0x001e, code lost:
                    
                        r0 = r24.toCharArray();
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:77:0x001c, code lost:
                    
                        if (r24 != null) goto L14;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:7:0x0023, code lost:
                    
                        r0 = r24;
                     */
                    /* JADX WARN: Code restructure failed: missing block: B:8:0x0025, code lost:
                    
                        r0 = r0;
                        r4 = new o.a.i();
                        r5 = new char[r0.length];
                        r4.b = 0;
                        r6 = new char[2];
                        r8 = 3;
                        r7 = o.v.j.AnonymousClass4.$10 + 3;
                        o.v.j.AnonymousClass4.$11 = r7 % 128;
                        r7 = r7 % 2;
                     */
                    /*
                        Code decompiled incorrectly, please refer to instructions dump.
                        To view partially-correct add '--show-bad-code' argument
                    */
                    private static void f(java.lang.String r24, int r25, java.lang.Object[] r26) {
                        /*
                            Method dump skipped, instructions count: 612
                            To view this dump add '--comments-level debug' option
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.v.j.AnonymousClass4.f(java.lang.String, int, java.lang.Object[]):void");
                    }
                }, cVar, new o.an.e(new e.C0025e(this.l, this.f106o))).a(dVar, o(), ((d) this).n.e(), null);
                break;
        }
    }

    public final void e(Context context, o.p.g gVar) throws WalletValidationException {
        int i = s + 61;
        p = i % 128;
        int i2 = i % 2;
        d(context, gVar);
        int i3 = p + Opcodes.DMUL;
        s = i3 % 128;
        int i4 = i3 % 2;
    }

    public final e.d a() {
        int i = s;
        int i2 = i + 47;
        p = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                e.d dVar = this.h;
                int i3 = i + Opcodes.LMUL;
                p = i3 % 128;
                switch (i3 % 2 == 0 ? 'L' : Typography.dollar) {
                    case '$':
                        return dVar;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                throw null;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v18, types: [byte[]] */
    private static void u(String str, int i, int[] iArr, String str2, Object[] objArr) {
        ?? r1 = str2;
        switch (r1 != 0 ? 'C' : (char) 18) {
            case 18:
                break;
            default:
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        char[] charArray = str != null ? str.toCharArray() : str;
        o.a.j jVar = new o.a.j();
        char[] cArr = m;
        int i2 = 2;
        switch (cArr != null) {
            case false:
                break;
            default:
                int length = cArr.length;
                char[] cArr2 = new char[length];
                int i3 = 0;
                while (i3 < length) {
                    int i4 = $11 + 37;
                    $10 = i4 % 128;
                    int i5 = i4 % i2;
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr[i3])};
                        Object obj = o.e.a.s.get(1085633688);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c((Process.myPid() >> 22) + 11, (char) ((Process.getThreadPriority(0) + 20) >> 6), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 493);
                            byte length2 = (byte) $$d.length;
                            Object[] objArr3 = new Object[1];
                            v((byte) 0, length2, (byte) (length2 - 4), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1085633688, obj);
                        }
                        cArr2[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i3++;
                        i2 = 2;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr = cArr2;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(k)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(10 - ExpandableListView.getPackedPositionType(0L), (char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 8856), (KeyEvent.getMaxKeyCode() >> 16) + 324);
                byte b = (byte) 0;
                byte b2 = (byte) (b + 1);
                Object[] objArr5 = new Object[1];
                v(b, b2, (byte) (b2 - 1), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            switch (r ? '/' : 'T') {
                case '/':
                    jVar.e = bArr.length;
                    char[] cArr3 = new char[jVar.e];
                    jVar.c = 0;
                    int i6 = $10 + 77;
                    $11 = i6 % 128;
                    int i7 = i6 % 2;
                    while (jVar.c < jVar.e) {
                        int i8 = $10 + 53;
                        $11 = i8 % 128;
                        int i9 = i8 % 2;
                        cArr3[jVar.c] = (char) (cArr[bArr[(jVar.e - 1) - jVar.c] + i] - intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj3 = o.e.a.s.get(745816316);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 10, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 207 - TextUtils.getOffsetAfter("", 0));
                                byte b3 = (byte) 0;
                                byte b4 = b3;
                                Object[] objArr7 = new Object[1];
                                v(b3, b4, b4, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    objArr[0] = new String(cArr3);
                    return;
                default:
                    if (!q) {
                        jVar.e = iArr.length;
                        char[] cArr4 = new char[jVar.e];
                        jVar.c = 0;
                        while (jVar.c < jVar.e) {
                            cArr4[jVar.c] = (char) (cArr[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                            jVar.c++;
                        }
                        objArr[0] = new String(cArr4);
                        return;
                    }
                    int i10 = $10 + 33;
                    $11 = i10 % 128;
                    int i11 = i10 % 2;
                    jVar.e = charArray.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr[charArray[(jVar.e - 1) - jVar.c] - i] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(10 - (ViewConfiguration.getScrollBarSize() >> 8), (char) Color.alpha(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 207);
                                byte b5 = (byte) 0;
                                byte b6 = b5;
                                Object[] objArr9 = new Object[1];
                                v(b5, b6, b6, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
