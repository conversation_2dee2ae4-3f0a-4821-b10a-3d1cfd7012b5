package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.unsafe.UnsafeUtil;
import com.esotericsoftware.kryo.util.Generics;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField.smali */
class UnsafeField extends ReflectField {
    public UnsafeField(Field field, FieldSerializer serializer, Generics.GenericType genericType) {
        super(field, serializer, genericType);
        this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField
    public Object get(Object object) throws IllegalAccessException {
        return UnsafeUtil.unsafe.getObject(object, this.offset);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField
    public void set(Object object, Object value) throws IllegalAccessException {
        UnsafeUtil.unsafe.putObject(object, this.offset, value);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField, com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
    public void copy(Object original, Object copy) {
        try {
            UnsafeUtil.unsafe.putObject(copy, this.offset, this.fieldSerializer.kryo.copy(UnsafeUtil.unsafe.getObject(original, this.offset)));
        } catch (KryoException ex) {
            ex.addTrace(this + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex;
        } catch (Throwable t) {
            KryoException ex2 = new KryoException(t);
            ex2.addTrace(this + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$IntUnsafeField.smali */
    static final class IntUnsafeField extends FieldSerializer.CachedField {
        public IntUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            if (this.varEncoding) {
                output.writeVarInt(UnsafeUtil.unsafe.getInt(object, this.offset), false);
            } else {
                output.writeInt(UnsafeUtil.unsafe.getInt(object, this.offset));
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            if (this.varEncoding) {
                UnsafeUtil.unsafe.putInt(object, this.offset, input.readVarInt(false));
            } else {
                UnsafeUtil.unsafe.putInt(object, this.offset, input.readInt());
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putInt(copy, this.offset, UnsafeUtil.unsafe.getInt(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$ByteUnsafeField.smali */
    static final class ByteUnsafeField extends FieldSerializer.CachedField {
        public ByteUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeByte(UnsafeUtil.unsafe.getByte(object, this.offset));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            UnsafeUtil.unsafe.putByte(object, this.offset, input.readByte());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putByte(copy, this.offset, UnsafeUtil.unsafe.getByte(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$BooleanUnsafeField.smali */
    static final class BooleanUnsafeField extends FieldSerializer.CachedField {
        public BooleanUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeBoolean(UnsafeUtil.unsafe.getBoolean(object, this.offset));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            UnsafeUtil.unsafe.putBoolean(object, this.offset, input.readBoolean());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putBoolean(copy, this.offset, UnsafeUtil.unsafe.getBoolean(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$CharUnsafeField.smali */
    static final class CharUnsafeField extends FieldSerializer.CachedField {
        public CharUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeChar(UnsafeUtil.unsafe.getChar(object, this.offset));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            UnsafeUtil.unsafe.putChar(object, this.offset, input.readChar());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putChar(copy, this.offset, UnsafeUtil.unsafe.getChar(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$LongUnsafeField.smali */
    static final class LongUnsafeField extends FieldSerializer.CachedField {
        public LongUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            if (this.varEncoding) {
                output.writeVarLong(UnsafeUtil.unsafe.getLong(object, this.offset), false);
            } else {
                output.writeLong(UnsafeUtil.unsafe.getLong(object, this.offset));
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            if (this.varEncoding) {
                UnsafeUtil.unsafe.putLong(object, this.offset, input.readVarLong(false));
            } else {
                UnsafeUtil.unsafe.putLong(object, this.offset, input.readLong());
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putLong(copy, this.offset, UnsafeUtil.unsafe.getLong(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$DoubleUnsafeField.smali */
    static final class DoubleUnsafeField extends FieldSerializer.CachedField {
        public DoubleUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeDouble(UnsafeUtil.unsafe.getDouble(object, this.offset));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            UnsafeUtil.unsafe.putDouble(object, this.offset, input.readDouble());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putDouble(copy, this.offset, UnsafeUtil.unsafe.getDouble(original, this.offset));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\UnsafeField$StringUnsafeField.smali */
    static final class StringUnsafeField extends FieldSerializer.CachedField {
        public StringUnsafeField(Field field) {
            super(field);
            this.offset = UnsafeUtil.unsafe.objectFieldOffset(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeString((String) UnsafeUtil.unsafe.getObject(object, this.offset));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            UnsafeUtil.unsafe.putObject(object, this.offset, input.readString());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            UnsafeUtil.unsafe.putObject(copy, this.offset, UnsafeUtil.unsafe.getObject(original, this.offset));
        }
    }
}
