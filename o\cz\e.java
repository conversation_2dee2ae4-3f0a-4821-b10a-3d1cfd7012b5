package o.cz;

import o.i.f;
import o.i.g;
import o.i.h;
import o.i.l;
import o.i.n;
import o.i.o;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\e.smali */
final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        a = new char[]{50825, 50776, 50810, 50712, 50709, 50691, 50702, 50798, 50791, 50689, 50713, 50718, 50689, 50719, 50705, 50715, 50716, 50692};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void e(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.cz.e.$$a
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r7 = r7 + 66
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.e.e(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
        $$b = 206;
    }

    e() {
    }

    /* renamed from: o.cz.e$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\e$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            a = 0;
            c = 1;
            int[] iArr = new int[f.values().length];
            e = iArr;
            try {
                iArr[f.d.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[f.e.ordinal()] = 2;
                int i = c + 39;
                a = i % 128;
                if (i % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[f.a.ordinal()] = 3;
                int i2 = (c + 50) - 1;
                a = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[f.b.ordinal()] = 4;
                int i3 = a;
                int i4 = (i3 ^ 71) + ((i3 & 71) << 1);
                c = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[f.c.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[f.f.ordinal()] = 6;
                int i6 = a;
                int i7 = ((i6 | 3) << 1) - (3 ^ i6);
                c = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e7) {
            }
        }
    }

    static g b(f fVar) {
        int i = b + 89;
        d = i % 128;
        int i2 = i % 2;
        switch (AnonymousClass3.e[fVar.ordinal()]) {
            case 1:
                return new n(fVar);
            case 2:
                return new h();
            case 3:
                return new o.i.j();
            case 4:
                o oVar = new o();
                int i3 = d + 91;
                b = i3 % 128;
                int i4 = i3 % 2;
                return oVar;
            case 5:
                return new o.i.e();
            case 6:
                return new l();
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                c("\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{0, 18, 104, 0}, true, objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(fVar.name()).toString());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:108:0x02e1, code lost:
    
        r2 = r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x00e8, code lost:
    
        if (r0[r1.d] == 0) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0197, code lost:
    
        r5 = r1.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x019e, code lost:
    
        r12 = new java.lang.Object[]{java.lang.Integer.valueOf(r2[r1.d]), java.lang.Integer.valueOf(r3)};
        r3 = o.e.a.s.get(804049217);
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x01bb, code lost:
    
        if (r3 == null) goto L64;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0217, code lost:
    
        r4[r5] = ((java.lang.Character) ((java.lang.reflect.Method) r3).invoke(null, r12)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0219, code lost:
    
        r3 = r4[r1.d];
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x021d, code lost:
    
        r5 = new java.lang.Object[]{r1, r1};
        r9 = o.e.a.s.get(-2112603350);
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x022e, code lost:
    
        if (r9 == null) goto L71;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x027c, code lost:
    
        ((java.lang.reflect.Method) r9).invoke(null, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0231, code lost:
    
        r12 = (java.lang.Class) o.e.a.c(10 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0'), (char) (android.graphics.ImageFormat.getBitsPerPixel(0) + 1), 259 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16));
        r14 = (byte) 0;
        r15 = r14;
        r13 = new java.lang.Object[1];
        e(r14, r15, r15, r13);
        r9 = r12.getMethod((java.lang.String) r13[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-2112603350, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0284, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0285, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x0289, code lost:
    
        if (r1 != null) goto L77;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x028b, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x028c, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x01c0, code lost:
    
        r10 = (java.lang.Class) o.e.a.c(android.graphics.Color.red(0) + 10, (char) (1 - (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1))), android.view.View.resolveSizeAndState(0, 0, 0) + 207);
        r13 = (byte) 0;
        r9 = new java.lang.Object[1];
        e(r13, (byte) (r13 | 56), r13, r9);
        r3 = r10.getMethod((java.lang.String) r9[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(804049217, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x028d, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x028e, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0292, code lost:
    
        if (r1 != null) goto L82;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0294, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x0295, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x00f3, code lost:
    
        r5 = r1.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x00fa, code lost:
    
        r12 = new java.lang.Object[]{java.lang.Integer.valueOf(r2[r1.d]), java.lang.Integer.valueOf(r3)};
        r3 = o.e.a.s.get(2016040108);
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x0117, code lost:
    
        if (r3 == null) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x0172, code lost:
    
        r4[r5] = ((java.lang.Character) ((java.lang.reflect.Method) r3).invoke(null, r12)).charValue();
        r3 = o.cz.e.$10 + com.esotericsoftware.asm.Opcodes.LSHL;
        o.cz.e.$11 = r3 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x017e, code lost:
    
        if ((r3 % 2) != 0) goto L51;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x0180, code lost:
    
        r3 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0183, code lost:
    
        switch(r3) {
            case 0: goto L54;
            default: goto L53;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0182, code lost:
    
        r3 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x011a, code lost:
    
        r3 = (java.lang.Class) o.e.a.c(11 - (android.os.Process.myTid() >> 22), (char) (android.view.ViewConfiguration.getPressedStateDuration() >> 16), 448 - (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
        r13 = (byte) 0;
        r9 = new java.lang.Object[1];
        e(r13, (byte) (r13 | 53), r13, r9);
        r3 = r3.getMethod((java.lang.String) r9[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(2016040108, r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x018e, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x018f, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x0193, code lost:
    
        if (r1 != null) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x0195, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:85:0x0196, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x00f0, code lost:
    
        if (r0[r1.d] == 1) goto L42;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void c(java.lang.String r22, int[] r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 860
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.e.c(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
