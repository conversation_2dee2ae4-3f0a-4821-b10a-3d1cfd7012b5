package com.avaldigitallabs.onespan.binding;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;

@CapacitorPlugin(name = "OnespanBinding")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\avaldigitallabs\onespan\binding\OnespanBinding.smali */
public class OnespanBinding extends Plugin {
    @PluginMethod
    public void getFingerprint(PluginCall call) {
        String idType = call.getString("androidIdType");
        String salt = call.getString("salt");
        DeviceBinding.FingerprintType fingerprintType = getFingerprintType(idType);
        DeviceBinding deviceBinding = DeviceBinding.createDeviceBinding(getContext(), fingerprintType);
        JSObject result = new JSObject();
        try {
            result.put("fingerPrint", deviceBinding.fingerprint(salt));
            call.resolve(result);
        } catch (DeviceBindingSDKException ex) {
            switch (ex.getErrorCode()) {
                case DeviceBindingSDKErrorCodes.UNIQUE_DATA_UNDEFINED /* -4405 */:
                    call.reject("Unique data is undefined");
                    break;
                case DeviceBindingSDKErrorCodes.SALT_TOO_SHORT /* -4404 */:
                    call.reject("Salt is too short");
                    break;
                case DeviceBindingSDKErrorCodes.SALT_NULL /* -4403 */:
                    call.reject("Salt is null");
                    break;
                case DeviceBindingSDKErrorCodes.PERMISSION_DENIED /* -4402 */:
                    call.reject("Permission denied");
                    break;
                case DeviceBindingSDKErrorCodes.CONTEXT_NULL /* -4401 */:
                    call.reject("Provided context is null");
                    break;
                case DeviceBindingSDKErrorCodes.INTERNAL_ERROR /* -4400 */:
                    call.reject("Internal error");
                    break;
                default:
                    call.reject(ex.getMessage());
                    break;
            }
        }
    }

    @PluginMethod
    public void cleanFingerprint(PluginCall call) {
        call.resolve();
    }

    private DeviceBinding.FingerprintType getFingerprintType(String idType) {
        if (idType == null) {
            return DeviceBinding.FingerprintType.ANDROID_ID;
        }
        return idType == "0" ? DeviceBinding.FingerprintType.ANDROID_ID : DeviceBinding.FingerprintType.HARDWARE;
    }
}
