package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.os.Build;
import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\t.smali */
public final class t implements q {
    public final FragmentActivity a;
    public h b;

    public t(FragmentActivity fragmentActivity) {
        this.a = fragmentActivity;
    }

    public final void a(String cipheredData, byte[] storageEncryptionKey, byte[] dataEncryptionKey, boolean z) {
        Intrinsics.checkNotNullParameter(cipheredData, "cipheredData");
        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        h hVar = null;
        try {
            if (z) {
                h hVar2 = this.b;
                if (hVar2 == null) {
                    Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
                    hVar2 = null;
                }
                hVar2.a();
                return;
            }
            l a = f.a(cipheredData, storageEncryptionKey, dataEncryptionKey);
            h hVar3 = this.b;
            if (hVar3 == null) {
                Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
                hVar3 = null;
            }
            hVar3.a(a);
        } catch (SecureStorageSDKException e) {
            h hVar4 = this.b;
            if (hVar4 != null) {
                hVar = hVar4;
            } else {
                Intrinsics.throwUninitializedPropertyAccessException("initializationResultCallback");
            }
            hVar.onInitFailed(e);
        }
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.q
    public final void a(String cipheredData, byte[] dataEncryptionKey, String fileName, int i, byte[] storageEncryptionKey, boolean z, String str) {
        Intrinsics.checkNotNullParameter(cipheredData, "cipheredData");
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        Intrinsics.checkNotNullParameter(fileName, "fileName");
        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
        try {
            String substring = cipheredData.substring(4, 5);
            Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
            if (!Intrinsics.areEqual(substring, "0") && Build.VERSION.SDK_INT >= 30) {
                FragmentActivity fragmentActivity = this.a;
                if (fragmentActivity != null) {
                    c.a(fileName, fragmentActivity, Intrinsics.areEqual(substring, "2"), storageEncryptionKey, new s(this, cipheredData, dataEncryptionKey, z, storageEncryptionKey));
                    return;
                }
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INITIALIZATION_FRAGMENTACTIVITY_NULL, null, 2, null);
            }
            a(cipheredData, storageEncryptionKey, dataEncryptionKey, z);
        } catch (SecureStorageSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }
}
