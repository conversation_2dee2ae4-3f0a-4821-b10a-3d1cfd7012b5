package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import androidx.core.view.MotionEventCompat;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f5.smali */
public class f5 {
    public static long a(int i) {
        return (((r6 >>> 1) & 1431655765) << 32) | (1431655765 & g1.a(g1.a(g1.a(g1.a(i, MotionEventCompat.ACTION_POINTER_INDEX_MASK, 8), 15728880, 4), 202116108, 2), 572662306, 1));
    }

    public static int b(int i) {
        int i2 = i & 255;
        int i3 = (i2 | (i2 << 4)) & 3855;
        int i4 = (i3 | (i3 << 2)) & 13107;
        return (i4 | (i4 << 1)) & 21845;
    }

    public static int c(int i) {
        return g1.a(g1.a(g1.a(g1.a(i, 11141290, 7), 52428, 14), 15728880, 4), MotionEventCompat.ACTION_POINTER_INDEX_MASK, 8);
    }

    public static void a(long j, long[] jArr, int i) {
        long a = g1.a(g1.a(g1.a(g1.a(g1.a(j, 4294901760L, 16), 280375465148160L, 8), 67555025218437360L, 4), 868082074056920076L, 2), 2459565876494606882L, 1);
        jArr[i] = a & 6148914691236517205L;
        jArr[i + 1] = (a >>> 1) & 6148914691236517205L;
    }

    public static void a(long[] jArr, int i, int i2, long[] jArr2, int i3) {
        for (int i4 = 0; i4 < i2; i4++) {
            a(jArr[i + i4], jArr2, i3);
            i3 += 2;
        }
    }

    public static long a(long j) {
        return g1.a(g1.a(g1.a(g1.a(g1.a(j, 2459565876494606882L, 1), 868082074056920076L, 2), 67555025218437360L, 4), 280375465148160L, 8), 4294901760L, 16);
    }
}
