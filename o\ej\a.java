package o.ej;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ej\a.smali */
public final class a {
    private static int a = 0;
    private static int b = 1;

    /* JADX WARN: Failed to find 'out' block for switch in B:44:0x009e. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:24:0x0059  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0036  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.math.BigDecimal a(byte[] r6, o.ej.e r7) {
        /*
            int r0 = o.ej.a.a
            int r1 = r0 + 107
            int r2 = r1 % 128
            o.ej.a.b = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L10
            r1 = 65
            goto L12
        L10:
            r1 = 62
        L12:
            r2 = 11
            r3 = 0
            r4 = 0
            r5 = 1
            switch(r1) {
                case 65: goto L1d;
                default: goto L1a;
            }
        L1a:
            if (r7 != 0) goto L30
            goto L2d
        L1d:
            r1 = 15
            int r1 = r1 / r3
            if (r7 != 0) goto L25
            r1 = 18
            goto L27
        L25:
            r1 = 10
        L27:
            switch(r1) {
                case 10: goto L35;
                default: goto L2a;
            }
        L2a:
            goto L36
        L2b:
            r6 = move-exception
            throw r6
        L2d:
            r1 = 53
            goto L32
        L30:
            r1 = 22
        L32:
            switch(r1) {
                case 53: goto L36;
                default: goto L35;
            }
        L35:
            goto L59
        L36:
            r7 = r0 ^ 11
            r0 = r0 & r2
            int r0 = r0 << r5
            int r7 = r7 + r0
            int r0 = r7 % 128
            o.ej.a.b = r0
            int r7 = r7 % 2
            if (r7 != 0) goto L46
            r7 = 81
            goto L48
        L46:
            r7 = 25
        L48:
            switch(r7) {
                case 81: goto L50;
                default: goto L4b;
            }
        L4b:
            java.math.BigDecimal r6 = o.dk.b.a(r6)
            return r6
        L50:
            o.dk.b.a(r6)
            r4.hashCode()     // Catch: java.lang.Throwable -> L57
            throw r4     // Catch: java.lang.Throwable -> L57
        L57:
            r6 = move-exception
            throw r6
        L59:
            int r0 = r7.e()
            r1 = -1
            if (r0 == r1) goto L63
            r0 = 37
            goto L64
        L63:
            r0 = 6
        L64:
            switch(r0) {
                case 37: goto L74;
                default: goto L67;
            }
        L67:
            java.lang.String r7 = r7.b()
            java.util.Currency r7 = java.util.Currency.getInstance(r7)
            int r7 = r7.getDefaultFractionDigits()
            goto La5
        L74:
            int r0 = o.ej.a.b
            int r0 = r0 + 39
            int r1 = r0 % 128
            o.ej.a.a = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L82
            r3 = r5
            goto L83
        L82:
        L83:
            switch(r3) {
                case 0: goto L8a;
                default: goto L86;
            }
        L86:
            r7.e()
            goto La2
        L8a:
            int r7 = r7.e()
            int r0 = o.ej.a.b
            int r0 = r0 + 104
            int r0 = r0 - r5
            int r1 = r0 % 128
            o.ej.a.a = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L9c
            goto L9e
        L9c:
            r2 = 79
        L9e:
            switch(r2) {
                case 11: goto La1;
                default: goto La1;
            }
        La1:
            goto La5
        La2:
            throw r4     // Catch: java.lang.Throwable -> La3
        La3:
            r6 = move-exception
            throw r6
        La5:
            java.math.BigDecimal r6 = o.dk.b.a(r6)
            java.math.BigDecimal r6 = r6.movePointLeft(r7)
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.a.a(byte[], o.ej.e):java.math.BigDecimal");
    }
}
