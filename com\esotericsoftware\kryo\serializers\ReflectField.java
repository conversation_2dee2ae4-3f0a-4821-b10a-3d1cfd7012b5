package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.util.Generics;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField.smali */
class ReflectField extends FieldSerializer.CachedField {
    final FieldSerializer fieldSerializer;
    final Generics.GenericType genericType;

    ReflectField(Field field, FieldSerializer fieldSerializer, Generics.GenericType genericType) {
        super(field);
        this.fieldSerializer = fieldSerializer;
        this.genericType = genericType;
    }

    public Object get(Object object) throws IllegalAccessException {
        return this.field.get(object);
    }

    public void set(Object object, Object value) throws IllegalAccessException {
        this.field.set(object, value);
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
    public void write(Output output, Object object) {
        Kryo kryo = this.fieldSerializer.kryo;
        try {
            Object value = get(object);
            Serializer serializer = this.serializer;
            Class concreteType = resolveFieldClass();
            if (concreteType == null) {
                if (value == null) {
                    kryo.writeClass(output, null);
                    return;
                }
                Registration registration = kryo.writeClass(output, value.getClass());
                if (serializer == null) {
                    serializer = registration.getSerializer();
                }
                kryo.getGenerics().pushGenericType(this.genericType);
                kryo.writeObject(output, value, serializer);
            } else {
                if (serializer == null) {
                    serializer = kryo.getSerializer(concreteType);
                    if (this.valueClass != null && this.reuseSerializer) {
                        this.serializer = serializer;
                    }
                }
                kryo.getGenerics().pushGenericType(this.genericType);
                if (this.canBeNull) {
                    kryo.writeObjectOrNull(output, value, serializer);
                } else {
                    if (value == null) {
                        throw new KryoException("Field value cannot be null when canBeNull is false: " + this.name + " (" + object.getClass().getName() + ")");
                    }
                    kryo.writeObject(output, value, serializer);
                }
            }
            kryo.getGenerics().popGenericType();
        } catch (KryoException ex) {
            ex.addTrace(this.name + " (" + object.getClass().getName() + ")");
            throw ex;
        } catch (IllegalAccessException ex2) {
            throw new KryoException("Error accessing field: " + this.name + " (" + object.getClass().getName() + ")", ex2);
        } catch (StackOverflowError ex3) {
            throw new KryoException("A StackOverflow occurred. The most likely cause is that your data has a circular reference resulting in infinite recursion. Try enabling references with Kryo.setReferences(true). If your data structure is really more than " + kryo.getDepth() + " levels deep then try increasing your Java stack size.", ex3);
        } catch (Throwable t) {
            KryoException ex4 = new KryoException(t);
            ex4.addTrace(this.name + " (" + object.getClass().getName() + ")");
            throw ex4;
        }
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
    public void read(Input input, Object object) {
        Object value;
        Kryo kryo = this.fieldSerializer.kryo;
        try {
            Serializer serializer = this.serializer;
            Class concreteType = resolveFieldClass();
            if (concreteType == null) {
                Registration registration = kryo.readClass(input);
                if (registration == null) {
                    set(object, null);
                    return;
                }
                if (serializer == null) {
                    serializer = registration.getSerializer();
                }
                kryo.getGenerics().pushGenericType(this.genericType);
                value = kryo.readObject(input, registration.getType(), serializer);
            } else {
                if (serializer == null) {
                    serializer = kryo.getSerializer(concreteType);
                    if (this.valueClass != null && this.reuseSerializer) {
                        this.serializer = serializer;
                    }
                }
                kryo.getGenerics().pushGenericType(this.genericType);
                if (this.canBeNull) {
                    value = kryo.readObjectOrNull(input, concreteType, serializer);
                } else {
                    value = kryo.readObject(input, concreteType, serializer);
                }
            }
            kryo.getGenerics().popGenericType();
            set(object, value);
        } catch (KryoException ex) {
            ex.addTrace(this.name + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex;
        } catch (IllegalAccessException ex2) {
            throw new KryoException("Error accessing field: " + this.name + " (" + this.fieldSerializer.type.getName() + ")", ex2);
        } catch (Throwable t) {
            KryoException ex3 = new KryoException(t);
            ex3.addTrace(this.name + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex3;
        }
    }

    Class resolveFieldClass() {
        Class fieldClass;
        return (this.valueClass == null && (fieldClass = this.genericType.resolve(this.fieldSerializer.kryo.getGenerics())) != null && this.fieldSerializer.kryo.isFinal(fieldClass)) ? fieldClass : this.valueClass;
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
    public void copy(Object original, Object copy) {
        try {
            set(copy, this.fieldSerializer.kryo.copy(get(original)));
        } catch (KryoException ex) {
            ex.addTrace(this.name + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex;
        } catch (IllegalAccessException ex2) {
            throw new KryoException("Error accessing field: " + this.name + " (" + this.fieldSerializer.type.getName() + ")", ex2);
        } catch (Throwable t) {
            KryoException ex3 = new KryoException(t);
            ex3.addTrace(this.name + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex3;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$IntReflectField.smali */
    static final class IntReflectField extends FieldSerializer.CachedField {
        public IntReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                if (this.varEncoding) {
                    output.writeVarInt(this.field.getInt(object), false);
                } else {
                    output.writeInt(this.field.getInt(object));
                }
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (int)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                if (this.varEncoding) {
                    this.field.setInt(object, input.readVarInt(false));
                } else {
                    this.field.setInt(object, input.readInt());
                }
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (int)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setInt(copy, this.field.getInt(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (int)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$FloatReflectField.smali */
    static final class FloatReflectField extends FieldSerializer.CachedField {
        public FloatReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeFloat(this.field.getFloat(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (float)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setFloat(object, input.readFloat());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (float)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setFloat(copy, this.field.getFloat(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (float)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$ShortReflectField.smali */
    static final class ShortReflectField extends FieldSerializer.CachedField {
        public ShortReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeShort(this.field.getShort(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (short)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setShort(object, input.readShort());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (short)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setShort(copy, this.field.getShort(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (short)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$ByteReflectField.smali */
    static final class ByteReflectField extends FieldSerializer.CachedField {
        public ByteReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeByte(this.field.getByte(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (byte)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setByte(object, input.readByte());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (byte)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setByte(copy, this.field.getByte(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (byte)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$BooleanReflectField.smali */
    static final class BooleanReflectField extends FieldSerializer.CachedField {
        public BooleanReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeBoolean(this.field.getBoolean(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (boolean)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setBoolean(object, input.readBoolean());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (boolean)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setBoolean(copy, this.field.getBoolean(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (boolean)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$CharReflectField.smali */
    static final class CharReflectField extends FieldSerializer.CachedField {
        public CharReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeChar(this.field.getChar(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (char)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setChar(object, input.readChar());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (char)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setChar(copy, this.field.getChar(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (char)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$LongReflectField.smali */
    static final class LongReflectField extends FieldSerializer.CachedField {
        public LongReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                if (this.varEncoding) {
                    output.writeVarLong(this.field.getLong(object), false);
                } else {
                    output.writeLong(this.field.getLong(object));
                }
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (long)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                if (this.varEncoding) {
                    this.field.setLong(object, input.readVarLong(false));
                } else {
                    this.field.setLong(object, input.readLong());
                }
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (long)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setLong(copy, this.field.getLong(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (long)");
                throw ex;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ReflectField$DoubleReflectField.smali */
    static final class DoubleReflectField extends FieldSerializer.CachedField {
        public DoubleReflectField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            try {
                output.writeDouble(this.field.getDouble(object));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (double)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            try {
                this.field.setDouble(object, input.readDouble());
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (double)");
                throw ex;
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            try {
                this.field.setDouble(copy, this.field.getDouble(original));
            } catch (Throwable t) {
                KryoException ex = new KryoException(t);
                ex.addTrace(this.name + " (double)");
                throw ex;
            }
        }
    }
}
