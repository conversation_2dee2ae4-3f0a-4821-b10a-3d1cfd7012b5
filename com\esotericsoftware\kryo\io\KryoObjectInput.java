package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.Kryo;
import java.io.IOException;
import java.io.ObjectInput;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\KryoObjectInput.smali */
public class KryoObjectInput extends KryoDataInput implements ObjectInput {
    private final Kryo kryo;

    public KryoObjectInput(Kryo kryo, Input input) {
        super(input);
        this.kryo = kryo;
    }

    @Override // java.io.ObjectInput
    public Object readObject() throws ClassNotFoundException, IOException {
        return this.kryo.readClassAndObject(this.input);
    }

    @Override // java.io.ObjectInput
    public int read() throws IOException {
        return this.input.read();
    }

    @Override // java.io.ObjectInput
    public int read(byte[] b) throws IOException {
        return this.input.read(b);
    }

    @Override // java.io.ObjectInput
    public int read(byte[] b, int off, int len) throws IOException {
        return this.input.read(b, off, len);
    }

    @Override // java.io.ObjectInput
    public long skip(long n) throws IOException {
        return this.input.skip(n);
    }

    @Override // java.io.ObjectInput
    public int available() throws IOException {
        return 0;
    }

    @Override // com.esotericsoftware.kryo.io.KryoDataInput, java.lang.AutoCloseable
    public void close() throws IOException {
        this.input.close();
    }
}
