package kotlin.sequences;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import kotlin.Deprecated;
import kotlin.DeprecatedSinceKotlin;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.ReplaceWith;
import kotlin.TuplesKt;
import kotlin.UInt;
import kotlin.ULong;
import kotlin.Unit;
import kotlin.collections.ArraysKt;
import kotlin.collections.CollectionsKt;
import kotlin.collections.Grouping;
import kotlin.collections.IndexedValue;
import kotlin.collections.SetsKt;
import kotlin.collections.SlidingWindowKt;
import kotlin.comparisons.ComparisonsKt;
import kotlin.comparisons.ComparisonsKt__ComparisonsKt;
import kotlin.comparisons.ComparisonsKt__ComparisonsKt$compareByDescending$1;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.Ref;
import kotlin.text.StringsKt;

/* JADX INFO: Access modifiers changed from: package-private */
/* compiled from: _Sequences.kt */
@Metadata(d1 = {"\u0000\u0098\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u001c\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010%\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\u0010\u0005\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\n\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u001f\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\b\u001c\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\r\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0010\u000f\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u0011\n\u0002\b)\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010#\n\u0000\n\u0002\u0010\"\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a0\u0010\u0000\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u0016\u0010\u0006\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a0\u0010\u0006\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u001c\u0010\u0007\u001a\b\u0012\u0004\u0012\u0002H\u00020\b\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001f\u0010\t\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0087\b\u001aT\u0010\n\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001e\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u0002H\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000f0\u0005H\u0086\bø\u0001\u0000\u001aB\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\u00020\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000\u001a\\\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000\u001a]\u0010\u0013\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0018\b\u0002\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\u00020\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001aw\u0010\u0013\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u0018\b\u0003\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001ao\u0010\u0019\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u0018\b\u0003\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u001e\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u0002H\u0002\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001aB\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u000b\"\u0004\b\u0000\u0010\f\"\u0004\b\u0001\u0010\r*\b\u0012\u0004\u0012\u0002H\f0\u00032\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u0005H\u0087\bø\u0001\u0000\u001a]\u0010\u001c\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\f\"\u0004\b\u0001\u0010\r\"\u0018\b\u0002\u0010\u0014*\u0012\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\u0006\b\u0000\u0012\u0002H\r0\u0015*\b\u0012\u0004\u0012\u0002H\f0\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u0002H\f\u0012\u0004\u0012\u0002H\r0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001f0\u0003H\u0007¢\u0006\u0002\b \u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0002\b!\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0002\b#\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020$0\u0003H\u0007¢\u0006\u0002\b%\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020&0\u0003H\u0007¢\u0006\u0002\b'\u001a\u0017\u0010\u001d\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020(0\u0003H\u0007¢\u0006\u0002\b)\u001a,\u0010*\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$H\u0007\u001aF\u0010*\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\u0004\u0012\u0002H-0\u0005H\u0007\u001a+\u0010.\u001a\u00020\u0001\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b/*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0002\u00101\u001a\u0016\u00102\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a0\u00102\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u001c\u00103\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a6\u00104\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005\u001a$\u00106\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00107\u001a\u00020$\u001a0\u00108\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005\u001a#\u00109\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010:\u001a\u00020$¢\u0006\u0002\u0010;\u001a7\u0010<\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010:\u001a\u00020$2\u0012\u0010=\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u0002H\u00020\u0005¢\u0006\u0002\u0010>\u001a%\u0010?\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010:\u001a\u00020$¢\u0006\u0002\u0010;\u001a0\u0010@\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005\u001aE\u0010A\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010\u0004\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010B\u001ad\u0010E\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2'\u0010\u0004\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010BH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010H\u001a$\u0010I\u001a\r\u0012\t\u0012\u0007H-¢\u0006\u0002\bJ0\u0003\"\u0006\b\u0000\u0010-\u0018\u0001*\u0006\u0012\u0002\b\u00030\u0003H\u0086\b\u001a8\u0010K\u001a\u0002HF\"\u0006\b\u0000\u0010-\u0018\u0001\"\u0010\b\u0001\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\u0006\u0012\u0002\b\u00030\u00032\u0006\u0010\u0016\u001a\u0002HFH\u0086\b¢\u0006\u0002\u0010L\u001a0\u0010M\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005\u001a\"\u0010N\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\b\b\u0000\u0010\u0002*\u00020O*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u0003\u001a;\u0010P\u001a\u0002HF\"\u0010\b\u0000\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020G\"\b\b\u0001\u0010\u0002*\u00020O*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF¢\u0006\u0002\u0010L\u001aO\u0010Q\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010R\u001aO\u0010S\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010R\u001a7\u0010T\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a7\u0010V\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a\u001b\u0010W\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a5\u0010W\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aA\u0010Y\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aC\u0010Z\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a\u001d\u0010[\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a7\u0010[\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aC\u0010\\\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\b0\u0005H\u0007¢\u0006\u0002\b]\u001a<\u0010\\\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030\u0005\u001aX\u0010^\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\b0BH\u0007¢\u0006\u0002\b_\u001aX\u0010^\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030BH\u0007¢\u0006\u0002\b`\u001ar\u0010a\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\b0BH\u0087\bø\u0001\u0000¢\u0006\u0004\bb\u0010H\u001ar\u0010a\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2-\u0010\u000e\u001a)\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030BH\u0087\bø\u0001\u0000¢\u0006\u0004\bc\u0010H\u001a]\u0010d\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\b0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0004\be\u0010R\u001a[\u0010d\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0018\u0010\u000e\u001a\u0014\u0012\u0004\u0012\u0002H\u0002\u0012\n\u0012\b\u0012\u0004\u0012\u0002H-0\u00030\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010R\u001aX\u0010f\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2'\u0010h\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0BH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010j\u001am\u0010k\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2<\u0010h\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0lH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010m\u001a0\u0010n\u001a\u00020o\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010p\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020o0\u0005H\u0086\bø\u0001\u0000\u001aE\u0010q\u001a\u00020o\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010p\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020o0BH\u0086\bø\u0001\u0000\u001aH\u0010r\u001a\u0014\u0012\u0004\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000\u001ab\u0010r\u001a\u0014\u0012\u0004\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\r0+0\u000b\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000\u001aa\u0010s\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u001c\b\u0002\u0010\u0014*\u0016\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020t0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0017\u001a{\u0010s\u001a\u0002H\u0014\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f\"\u0004\b\u0002\u0010\r\"\u001c\b\u0003\u0010\u0014*\u0016\u0012\u0006\b\u0000\u0012\u0002H\f\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\r0t0\u0015*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002H\u00142\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\r0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001aD\u0010u\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0v\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010\f*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\b\u0004\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\f0\u0005H\u0087\bø\u0001\u0000\u001a(\u0010w\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b/*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002¢\u0006\u0002\u0010x\u001a0\u0010y\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a0\u0010z\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a\u0086\u0001\u0010{\u001a\u0002H|\"\u0004\b\u0000\u0010\u0002\"\f\b\u0001\u0010|*\u00060}j\u0002`~*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u007f\u001a\u0002H|2\n\b\u0002\u0010\u0080\u0001\u001a\u00030\u0081\u00012\n\b\u0002\u0010\u0082\u0001\u001a\u00030\u0081\u00012\n\b\u0002\u0010\u0083\u0001\u001a\u00030\u0081\u00012\t\b\u0002\u0010\u0084\u0001\u001a\u00020$2\n\b\u0002\u0010\u0085\u0001\u001a\u00030\u0081\u00012\u0017\b\u0002\u0010\u000e\u001a\u0011\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0081\u0001\u0018\u00010\u0005¢\u0006\u0003\u0010\u0086\u0001\u001al\u0010\u0087\u0001\u001a\u00030\u0088\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\n\b\u0002\u0010\u0080\u0001\u001a\u00030\u0081\u00012\n\b\u0002\u0010\u0082\u0001\u001a\u00030\u0081\u00012\n\b\u0002\u0010\u0083\u0001\u001a\u00030\u0081\u00012\t\b\u0002\u0010\u0084\u0001\u001a\u00020$2\n\b\u0002\u0010\u0085\u0001\u001a\u00030\u0081\u00012\u0017\b\u0002\u0010\u000e\u001a\u0011\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030\u0081\u0001\u0018\u00010\u0005\u001a\u001c\u0010\u0089\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a6\u0010\u0089\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a)\u0010\u008a\u0001\u001a\u00020$\"\t\b\u0000\u0010\u0002¢\u0006\u0002\b/*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002¢\u0006\u0002\u0010x\u001a\u001e\u0010\u008b\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a8\u0010\u008b\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a7\u0010\u008c\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005\u001aL\u0010\u008d\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010\u000e\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0B\u001aR\u0010\u008e\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010\u000e\u001a%\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0B\u001aq\u0010\u008f\u0001\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2)\u0010\u000e\u001a%\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0BH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010H\u001ak\u0010\u0090\u0001\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2'\u0010\u000e\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0BH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010H\u001a=\u0010\u0091\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005\u001a\\\u0010\u0092\u0001\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\b\b\u0001\u0010-*\u00020O\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0014\u0010\u000e\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010R\u001aV\u0010\u0093\u0001\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0010\b\u0002\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H-0G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF2\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010R\u001a-\u0010\u0094\u0001\u001a\u0002H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0006\b\u0096\u0001\u0010\u0097\u0001\u001a\u0019\u0010\u0094\u0001\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\b\u0096\u0001\u001a\u0019\u0010\u0094\u0001\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\b\u0096\u0001\u001aJ\u0010\u0098\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0005\b\u0099\u0001\u0010U\u001aI\u0010\u009a\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aH\u0010\u009b\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009c\u0001\u001a1\u0010\u009b\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010\u009b\u0001\u001a\u00020\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000\u001aJ\u0010\u009d\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009c\u0001\u001a9\u0010\u009d\u0001\u001a\u0004\u0018\u00010\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009e\u0001\u001a9\u0010\u009d\u0001\u001a\u0004\u0018\u00010\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009f\u0001\u001a\\\u0010 \u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`£\u00012\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¤\u0001\u001a^\u0010¥\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`£\u00012\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¤\u0001\u001a,\u0010¦\u0001\u001a\u0004\u0018\u0001H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0003\u0010\u0097\u0001\u001a\u001b\u0010¦\u0001\u001a\u0004\u0018\u00010\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\u0010§\u0001\u001a\u001b\u0010¦\u0001\u001a\u0004\u0018\u00010\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\u0010¨\u0001\u001aA\u0010©\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`£\u0001H\u0007¢\u0006\u0006\bª\u0001\u0010«\u0001\u001a@\u0010¬\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`£\u0001H\u0007¢\u0006\u0003\u0010«\u0001\u001a-\u0010\u00ad\u0001\u001a\u0002H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0006\b®\u0001\u0010\u0097\u0001\u001a\u0019\u0010\u00ad\u0001\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\b®\u0001\u001a\u0019\u0010\u00ad\u0001\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\b®\u0001\u001aJ\u0010¯\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0005\b°\u0001\u0010U\u001aI\u0010±\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aH\u0010²\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009c\u0001\u001a1\u0010²\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010²\u0001\u001a\u00020\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000\u001aJ\u0010³\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009c\u0001\u001a9\u0010³\u0001\u001a\u0004\u0018\u00010\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009e\u0001\u001a9\u0010³\u0001\u001a\u0004\u0018\u00010\"\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\"0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009f\u0001\u001a\\\u0010´\u0001\u001a\u0002H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`£\u00012\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¤\u0001\u001a^\u0010µ\u0001\u001a\u0004\u0018\u0001H-\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H-0¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H-`£\u00012\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¤\u0001\u001a,\u0010¶\u0001\u001a\u0004\u0018\u0001H\u0002\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007¢\u0006\u0003\u0010\u0097\u0001\u001a\u001b\u0010¶\u0001\u001a\u0004\u0018\u00010\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\u0010§\u0001\u001a\u001b\u0010¶\u0001\u001a\u0004\u0018\u00010\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\u0010¨\u0001\u001aA\u0010·\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`£\u0001H\u0007¢\u0006\u0006\b¸\u0001\u0010«\u0001\u001a@\u0010¹\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`£\u0001H\u0007¢\u0006\u0003\u0010«\u0001\u001a.\u0010º\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0003\u0010»\u0001\u001a8\u0010º\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010¼\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H\u00020½\u0001H\u0086\u0002¢\u0006\u0003\u0010¾\u0001\u001a/\u0010º\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010¼\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\bH\u0086\u0002\u001a/\u0010º\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010¼\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0002\u001a.\u0010¿\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002H\u0087\b¢\u0006\u0003\u0010»\u0001\u001a\u0017\u0010À\u0001\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a1\u0010À\u0001\u001a\u00020\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a3\u0010Á\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010p\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020o0\u0005H\u0007\u001aH\u0010Â\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032'\u0010p\u001a#\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020o0BH\u0007\u001aI\u0010Ã\u0001\u001a\u001a\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u000f\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000\u001a.\u0010Ä\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002H\u0086\u0002¢\u0006\u0003\u0010»\u0001\u001a8\u0010Ä\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0010\u0010¼\u0001\u001a\u000b\u0012\u0006\b\u0001\u0012\u0002H\u00020½\u0001H\u0086\u0002¢\u0006\u0003\u0010¾\u0001\u001a/\u0010Ä\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010¼\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\bH\u0086\u0002\u001a/\u0010Ä\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010¼\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0086\u0002\u001a.\u0010Å\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00100\u001a\u0002H\u0002H\u0087\b¢\u0006\u0003\u0010»\u0001\u001a[\u0010Æ\u0001\u001a\u0003HÇ\u0001\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010h\u001a%\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010BH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010È\u0001\u001ap\u0010É\u0001\u001a\u0003HÇ\u0001\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010h\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010lH\u0086\bø\u0001\u0000¢\u0006\u0003\u0010Ê\u0001\u001ar\u0010Ë\u0001\u001a\u0005\u0018\u0001HÇ\u0001\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010h\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010lH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010Ê\u0001\u001a]\u0010Ì\u0001\u001a\u0005\u0018\u0001HÇ\u0001\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010h\u001a%\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010BH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010È\u0001\u001a#\u0010Í\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\b\b\u0000\u0010\u0002*\u00020O*\n\u0012\u0006\u0012\u0004\u0018\u0001H\u00020\u0003\u001a\\\u0010Î\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2'\u0010h\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0BH\u0007¢\u0006\u0003\u0010Ï\u0001\u001aq\u0010Ð\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2<\u0010h\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0lH\u0007¢\u0006\u0003\u0010Ñ\u0001\u001aW\u0010Ò\u0001\u001a\t\u0012\u0005\u0012\u0003HÇ\u00010\u0003\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032)\u0010h\u001a%\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010BH\u0007\u001al\u0010Ó\u0001\u001a\t\u0012\u0005\u0012\u0003HÇ\u00010\u0003\"\u0005\b\u0000\u0010Ç\u0001\"\t\b\u0001\u0010\u0002*\u0003HÇ\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032>\u0010h\u001a:\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0014\u0012\u0012HÇ\u0001¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u0003HÇ\u00010lH\u0007\u001a\\\u0010Ô\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2'\u0010h\u001a#\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0BH\u0007¢\u0006\u0003\u0010Ï\u0001\u001aq\u0010Õ\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010g\u001a\u0002H-2<\u0010h\u001a8\u0012\u0013\u0012\u00110$¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(:\u0012\u0013\u0012\u0011H-¢\u0006\f\bC\u0012\b\bD\u0012\u0004\b\b(i\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0lH\u0007¢\u0006\u0003\u0010Ñ\u0001\u001a\u001c\u0010Ö\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a6\u0010Ö\u0001\u001a\u0002H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a\u001e\u0010×\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003¢\u0006\u0002\u0010X\u001a8\u0010×\u0001\u001a\u0004\u0018\u0001H\u0002\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001a(\u0010Ø\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aL\u0010Ù\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0016\b\u0004\u00105\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001aL\u0010Ú\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002\"\u000f\b\u0001\u0010-*\t\u0012\u0004\u0012\u0002H-0\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0016\b\u0004\u00105\u001a\u0010\u0012\u0004\u0012\u0002H\u0002\u0012\u0006\u0012\u0004\u0018\u0001H-0\u0005H\u0086\bø\u0001\u0000\u001a(\u0010Û\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u000f\b\u0000\u0010\u0002*\t\u0012\u0004\u0012\u0002H\u00020\u0095\u0001*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a<\u0010Ü\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u001d\u0010¡\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H\u00020¢\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H\u0002`£\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020$*\b\u0012\u0004\u0012\u00020\u001f0\u0003H\u0007¢\u0006\u0003\bÞ\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020\u001e*\b\u0012\u0004\u0012\u00020\u001e0\u0003H\u0007¢\u0006\u0003\bß\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020\"*\b\u0012\u0004\u0012\u00020\"0\u0003H\u0007¢\u0006\u0003\bà\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020$*\b\u0012\u0004\u0012\u00020$0\u0003H\u0007¢\u0006\u0003\bá\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020&*\b\u0012\u0004\u0012\u00020&0\u0003H\u0007¢\u0006\u0003\bâ\u0001\u001a\u0019\u0010Ý\u0001\u001a\u00020$*\b\u0012\u0004\u0012\u00020(0\u0003H\u0007¢\u0006\u0003\bã\u0001\u001a1\u0010ä\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020$0\u0005H\u0087\bø\u0001\u0000\u001a1\u0010å\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000\u001a7\u0010æ\u0001\u001a\u00020\u001e\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u001e0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\bß\u0001\u001a7\u0010æ\u0001\u001a\u00020$\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020$0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\bá\u0001\u001a7\u0010æ\u0001\u001a\u00020&\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u00105\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020&0\u0005H\u0087\bø\u0001\u0000¢\u0006\u0003\bâ\u0001\u001a?\u0010æ\u0001\u001a\u00030ç\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0013\u00105\u001a\u000f\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030ç\u00010\u0005H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\bè\u0001\u0010é\u0001\u001a?\u0010æ\u0001\u001a\u00030ê\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0013\u00105\u001a\u000f\u0012\u0004\u0012\u0002H\u0002\u0012\u0005\u0012\u00030ê\u00010\u0005H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\bë\u0001\u0010ì\u0001\u001a%\u0010í\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u00107\u001a\u00020$\u001a1\u0010î\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u00020\u00010\u0005\u001a6\u0010ï\u0001\u001a\u0002HF\"\u0004\b\u0000\u0010\u0002\"\u0010\b\u0001\u0010F*\n\u0012\u0006\b\u0000\u0012\u0002H\u00020G*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010\u0016\u001a\u0002HF¢\u0006\u0002\u0010L\u001a)\u0010ð\u0001\u001a\u0014\u0012\u0004\u0012\u0002H\u00020ñ\u0001j\t\u0012\u0004\u0012\u0002H\u0002`ò\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001d\u0010ó\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020+\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001d\u0010ô\u0001\u001a\b\u0012\u0004\u0012\u0002H\u00020t\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001e\u0010õ\u0001\u001a\t\u0012\u0004\u0012\u0002H\u00020ö\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001a\u001e\u0010÷\u0001\u001a\t\u0012\u0004\u0012\u0002H\u00020ø\u0001\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aC\u0010ù\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+0\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\t\b\u0002\u0010ú\u0001\u001a\u00020$2\t\b\u0002\u0010û\u0001\u001a\u00020\u0001H\u0007\u001a]\u0010ù\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\u0006\u0010,\u001a\u00020$2\t\b\u0002\u0010ú\u0001\u001a\u00020$2\t\b\u0002\u0010û\u0001\u001a\u00020\u00012\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u00020+\u0012\u0004\u0012\u0002H-0\u0005H\u0007\u001a$\u0010ü\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u0002H\u00020ý\u00010\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003\u001aA\u0010þ\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H-0\u000f0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010ÿ\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u0003H\u0086\u0004\u001ar\u0010þ\u0001\u001a\b\u0012\u0004\u0012\u0002H\r0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-\"\u0004\b\u0002\u0010\r*\b\u0012\u0004\u0012\u0002H\u00020\u00032\r\u0010ÿ\u0001\u001a\b\u0012\u0004\u0012\u0002H-0\u000328\u0010\u000e\u001a4\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bC\u0012\t\bD\u0012\u0005\b\b(\u0080\u0002\u0012\u0014\u0012\u0012H-¢\u0006\r\bC\u0012\t\bD\u0012\u0005\b\b(\u0081\u0002\u0012\u0004\u0012\u0002H\r0B\u001a+\u0010\u0082\u0002\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\u0002\u0012\u0004\u0012\u0002H\u00020\u000f0\u0003\"\u0004\b\u0000\u0010\u0002*\b\u0012\u0004\u0012\u0002H\u00020\u0003H\u0007\u001a_\u0010\u0082\u0002\u001a\b\u0012\u0004\u0012\u0002H-0\u0003\"\u0004\b\u0000\u0010\u0002\"\u0004\b\u0001\u0010-*\b\u0012\u0004\u0012\u0002H\u00020\u000328\u0010\u000e\u001a4\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bC\u0012\t\bD\u0012\u0005\b\b(\u0080\u0002\u0012\u0014\u0012\u0012H\u0002¢\u0006\r\bC\u0012\t\bD\u0012\u0005\b\b(\u0081\u0002\u0012\u0004\u0012\u0002H-0BH\u0007\u0082\u0002\u000b\n\u0005\b\u009920\u0001\n\u0002\b\u0019¨\u0006\u0083\u0002"}, d2 = {"all", "", "T", "Lkotlin/sequences/Sequence;", "predicate", "Lkotlin/Function1;", "any", "asIterable", "", "asSequence", "associate", "", "K", "V", "transform", "Lkotlin/Pair;", "associateBy", "keySelector", "valueTransform", "associateByTo", "M", "", "destination", "(Lkotlin/sequences/Sequence;Ljava/util/Map;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "(Lkotlin/sequences/Sequence;Ljava/util/Map;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "associateTo", "associateWith", "valueSelector", "associateWithTo", "average", "", "", "averageOfByte", "averageOfDouble", "", "averageOfFloat", "", "averageOfInt", "", "averageOfLong", "", "averageOfShort", "chunked", "", "size", "R", "contains", "Lkotlin/internal/OnlyInputTypes;", "element", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;)Z", "count", "distinct", "distinctBy", "selector", "drop", "n", "dropWhile", "elementAt", "index", "(Lkotlin/sequences/Sequence;I)Ljava/lang/Object;", "elementAtOrElse", "defaultValue", "(Lkotlin/sequences/Sequence;ILkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "elementAtOrNull", "filter", "filterIndexed", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "filterIndexedTo", "C", "", "(Lkotlin/sequences/Sequence;Ljava/util/Collection;Lkotlin/jvm/functions/Function2;)Ljava/util/Collection;", "filterIsInstance", "Lkotlin/internal/NoInfer;", "filterIsInstanceTo", "(Lkotlin/sequences/Sequence;Ljava/util/Collection;)Ljava/util/Collection;", "filterNot", "filterNotNull", "", "filterNotNullTo", "filterNotTo", "(Lkotlin/sequences/Sequence;Ljava/util/Collection;Lkotlin/jvm/functions/Function1;)Ljava/util/Collection;", "filterTo", "find", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "findLast", "first", "(Lkotlin/sequences/Sequence;)Ljava/lang/Object;", "firstNotNullOf", "firstNotNullOfOrNull", "firstOrNull", "flatMap", "flatMapIterable", "flatMapIndexed", "flatMapIndexedIterable", "flatMapIndexedSequence", "flatMapIndexedTo", "flatMapIndexedIterableTo", "flatMapIndexedSequenceTo", "flatMapTo", "flatMapIterableTo", "fold", "initial", "operation", "acc", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "foldIndexed", "Lkotlin/Function3;", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "forEach", "", "action", "forEachIndexed", "groupBy", "groupByTo", "", "groupingBy", "Lkotlin/collections/Grouping;", "indexOf", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;)I", "indexOfFirst", "indexOfLast", "joinTo", "A", "Ljava/lang/Appendable;", "Lkotlin/text/Appendable;", "buffer", "separator", "", "prefix", "postfix", "limit", "truncated", "(Lkotlin/sequences/Sequence;Ljava/lang/Appendable;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Appendable;", "joinToString", "", "last", "lastIndexOf", "lastOrNull", "map", "mapIndexed", "mapIndexedNotNull", "mapIndexedNotNullTo", "mapIndexedTo", "mapNotNull", "mapNotNullTo", "mapTo", "max", "", "maxOrThrow", "(Lkotlin/sequences/Sequence;)Ljava/lang/Comparable;", "maxBy", "maxByOrThrow", "maxByOrNull", "maxOf", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Comparable;", "maxOfOrNull", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Double;", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Float;", "maxOfWith", "comparator", "Ljava/util/Comparator;", "Lkotlin/Comparator;", "(Lkotlin/sequences/Sequence;Ljava/util/Comparator;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "maxOfWithOrNull", "maxOrNull", "(Lkotlin/sequences/Sequence;)Ljava/lang/Double;", "(Lkotlin/sequences/Sequence;)Ljava/lang/Float;", "maxWith", "maxWithOrThrow", "(Lkotlin/sequences/Sequence;Ljava/util/Comparator;)Ljava/lang/Object;", "maxWithOrNull", "min", "minOrThrow", "minBy", "minByOrThrow", "minByOrNull", "minOf", "minOfOrNull", "minOfWith", "minOfWithOrNull", "minOrNull", "minWith", "minWithOrThrow", "minWithOrNull", "minus", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;)Lkotlin/sequences/Sequence;", "elements", "", "(Lkotlin/sequences/Sequence;[Ljava/lang/Object;)Lkotlin/sequences/Sequence;", "minusElement", "none", "onEach", "onEachIndexed", "partition", "plus", "plusElement", "reduce", "S", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "reduceIndexed", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "reduceIndexedOrNull", "reduceOrNull", "requireNoNulls", "runningFold", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Lkotlin/sequences/Sequence;", "runningFoldIndexed", "(Lkotlin/sequences/Sequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Lkotlin/sequences/Sequence;", "runningReduce", "runningReduceIndexed", "scan", "scanIndexed", "single", "singleOrNull", "sorted", "sortedBy", "sortedByDescending", "sortedDescending", "sortedWith", "sum", "sumOfByte", "sumOfDouble", "sumOfFloat", "sumOfInt", "sumOfLong", "sumOfShort", "sumBy", "sumByDouble", "sumOf", "Lkotlin/UInt;", "sumOfUInt", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)I", "Lkotlin/ULong;", "sumOfULong", "(Lkotlin/sequences/Sequence;Lkotlin/jvm/functions/Function1;)J", "take", "takeWhile", "toCollection", "toHashSet", "Ljava/util/HashSet;", "Lkotlin/collections/HashSet;", "toList", "toMutableList", "toMutableSet", "", "toSet", "", "windowed", "step", "partialWindows", "withIndex", "Lkotlin/collections/IndexedValue;", "zip", "other", "a", "b", "zipWithNext", "kotlin-stdlib"}, k = 5, mv = {1, 9, 0}, xi = 49, xs = "kotlin/sequences/SequencesKt")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\sequences\SequencesKt___SequencesKt.smali */
public class SequencesKt___SequencesKt extends SequencesKt___SequencesJvmKt {
    public static final <T> boolean contains(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.indexOf(sequence, t) >= 0;
    }

    public static final <T> T elementAt(Sequence<? extends T> sequence, final int i) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return (T) SequencesKt.elementAtOrElse(sequence, i, new Function1<Integer, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$elementAt$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(1);
            }

            public final T invoke(int it) {
                throw new IndexOutOfBoundsException("Sequence doesn't contain element at index " + i + '.');
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Object invoke(Integer num) {
                return invoke(num.intValue());
            }
        });
    }

    public static final <T> T elementAtOrElse(Sequence<? extends T> sequence, int index, Function1<? super Integer, ? extends T> defaultValue) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        if (index < 0) {
            return defaultValue.invoke(Integer.valueOf(index));
        }
        int count = 0;
        for (T t : sequence) {
            int count2 = count + 1;
            if (index != count) {
                count = count2;
            } else {
                return t;
            }
        }
        Object element = Integer.valueOf(index);
        return defaultValue.invoke(element);
    }

    public static final <T> T elementAtOrNull(Sequence<? extends T> sequence, int index) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        if (index < 0) {
            return null;
        }
        int count = 0;
        for (T t : sequence) {
            int count2 = count + 1;
            if (index != count) {
                count = count2;
            } else {
                return t;
            }
        }
        return null;
    }

    /* JADX WARN: Type inference failed for: r3v2, types: [T, java.lang.Object] */
    private static final <T> T find(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : sequence) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v1, types: [java.lang.Object] */
    private static final <T> T findLast(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        for (T t2 : sequence) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
            }
        }
        return t;
    }

    public static final <T> T first(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException("Sequence is empty.");
        }
        return iterator.next();
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    public static final <T> T first(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : sequence) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        throw new NoSuchElementException("Sequence contains no element matching the predicate.");
    }

    private static final <T, R> R firstNotNullOf(Sequence<? extends T> sequence, Function1<? super T, ? extends R> transform) {
        R r;
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Iterator<? extends T> it = sequence.iterator();
        while (true) {
            if (!it.hasNext()) {
                r = null;
                break;
            }
            r = transform.invoke(it.next());
            if (r != null) {
                break;
            }
        }
        if (r != null) {
            return r;
        }
        throw new NoSuchElementException("No element of the sequence was transformed to a non-null value.");
    }

    private static final <T, R> R firstNotNullOfOrNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : sequence) {
            R invoke = transform.invoke(element);
            if (invoke != null) {
                return invoke;
            }
        }
        return null;
    }

    public static final <T> T firstOrNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        return iterator.next();
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    public static final <T> T firstOrNull(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (T t : sequence) {
            if (predicate.invoke(t).booleanValue()) {
                return t;
            }
        }
        return null;
    }

    public static final <T> int indexOf(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int index = 0;
        for (Object item : sequence) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (Intrinsics.areEqual(t, item)) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public static final <T> int indexOfFirst(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index = 0;
        for (Object item : sequence) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(item).booleanValue()) {
                return index;
            }
            index++;
        }
        return -1;
    }

    public static final <T> int indexOfLast(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int lastIndex = -1;
        int index = 0;
        for (Object item : sequence) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(item).booleanValue()) {
                lastIndex = index;
            }
            index++;
        }
        return lastIndex;
    }

    public static final <T> T last(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException("Sequence is empty.");
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            next = iterator.next();
        }
        return next;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v2, types: [java.lang.Object] */
    public static final <T> T last(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : sequence) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
                z = true;
            }
        }
        if (!z) {
            throw new NoSuchElementException("Sequence contains no element matching the predicate.");
        }
        return t;
    }

    public static final <T> int lastIndexOf(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int lastIndex = -1;
        int index = 0;
        for (Object item : sequence) {
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (Intrinsics.areEqual(t, item)) {
                lastIndex = index;
            }
            index++;
        }
        return lastIndex;
    }

    public static final <T> T lastOrNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            next = iterator.next();
        }
        return next;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v1, types: [java.lang.Object] */
    public static final <T> T lastOrNull(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        for (T t2 : sequence) {
            if (predicate.invoke(t2).booleanValue()) {
                t = t2;
            }
        }
        return t;
    }

    public static final <T> T single(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException("Sequence is empty.");
        }
        T next = iterator.next();
        if (iterator.hasNext()) {
            throw new IllegalArgumentException("Sequence has more than one element.");
        }
        return next;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v2, types: [java.lang.Object] */
    public static final <T> T single(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : sequence) {
            if (predicate.invoke(t2).booleanValue()) {
                if (z) {
                    throw new IllegalArgumentException("Sequence contains more than one matching element.");
                }
                t = t2;
                z = true;
            }
        }
        if (!z) {
            throw new NoSuchElementException("Sequence contains no element matching the predicate.");
        }
        return t;
    }

    public static final <T> T singleOrNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        if (iterator.hasNext()) {
            return null;
        }
        return next;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r4v1, types: [java.lang.Object] */
    public static final <T> T singleOrNull(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        T t = null;
        boolean z = false;
        for (T t2 : sequence) {
            if (predicate.invoke(t2).booleanValue()) {
                if (z) {
                    return null;
                }
                t = t2;
                z = true;
            }
        }
        if (z) {
            return t;
        }
        return null;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <T> Sequence<T> drop(Sequence<? extends T> sequence, int n) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        if (n >= 0) {
            return n == 0 ? sequence : sequence instanceof DropTakeSequence ? ((DropTakeSequence) sequence).drop(n) : new DropSequence(sequence, n);
        }
        throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
    }

    public static final <T> Sequence<T> dropWhile(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        return new DropWhileSequence(sequence, predicate);
    }

    public static final <T> Sequence<T> filter(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        return new FilteringSequence(sequence, true, predicate);
    }

    public static final <T> Sequence<T> filterIndexed(Sequence<? extends T> sequence, final Function2<? super Integer, ? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        return new TransformingSequence(new FilteringSequence(new IndexingSequence(sequence), true, new Function1<IndexedValue<? extends T>, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$filterIndexed$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public final Boolean invoke(IndexedValue<? extends T> it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return predicate.invoke(Integer.valueOf(it.getIndex()), it.getValue());
            }
        }), new Function1<IndexedValue<? extends T>, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$filterIndexed$2
            @Override // kotlin.jvm.functions.Function1
            public final T invoke(IndexedValue<? extends T> it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return it.getValue();
            }
        });
    }

    public static final <T, C extends Collection<? super T>> C filterIndexedTo(Sequence<? extends T> sequence, C destination, Function2<? super Integer, ? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index = 0;
        for (Object item$iv : sequence) {
            int index$iv = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            if (predicate.invoke(Integer.valueOf(index), item$iv).booleanValue()) {
                destination.add(item$iv);
            }
            index = index$iv;
        }
        return destination;
    }

    public static final /* synthetic */ <R> Sequence<R> filterIsInstance(Sequence<?> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.needClassReification();
        Sequence<R> filter = SequencesKt.filter(sequence, new Function1<Object, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$filterIsInstance$1
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function1
            public final Boolean invoke(Object it) {
                Intrinsics.reifiedOperationMarker(3, "R");
                return Boolean.valueOf(it instanceof Object);
            }
        });
        Intrinsics.checkNotNull(filter, "null cannot be cast to non-null type kotlin.sequences.Sequence<R of kotlin.sequences.SequencesKt___SequencesKt.filterIsInstance>");
        return filter;
    }

    public static final /* synthetic */ <R, C extends Collection<? super R>> C filterIsInstanceTo(Sequence<?> sequence, C destination) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        for (Object element : sequence) {
            Intrinsics.reifiedOperationMarker(3, "R");
            if (element instanceof Object) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T> Sequence<T> filterNot(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        return new FilteringSequence(sequence, false, predicate);
    }

    public static final <T> Sequence<T> filterNotNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Sequence<T> filterNot = SequencesKt.filterNot(sequence, new Function1<T, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$filterNotNull$1
            /* JADX WARN: Can't rename method to resolve collision */
            @Override // kotlin.jvm.functions.Function1
            public final Boolean invoke(T t) {
                return Boolean.valueOf(t == null);
            }

            /* JADX WARN: Multi-variable type inference failed */
            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Boolean invoke(Object obj) {
                return invoke((SequencesKt___SequencesKt$filterNotNull$1<T>) obj);
            }
        });
        Intrinsics.checkNotNull(filterNot, "null cannot be cast to non-null type kotlin.sequences.Sequence<T of kotlin.sequences.SequencesKt___SequencesKt.filterNotNull>");
        return filterNot;
    }

    public static final <C extends Collection<? super T>, T> C filterNotNullTo(Sequence<? extends T> sequence, C destination) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        for (T t : sequence) {
            if (t != null) {
                destination.add(t);
            }
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C filterNotTo(Sequence<? extends T> sequence, C destination, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : sequence) {
            if (!predicate.invoke(element).booleanValue()) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C filterTo(Sequence<? extends T> sequence, C destination, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : sequence) {
            if (predicate.invoke(element).booleanValue()) {
                destination.add(element);
            }
        }
        return destination;
    }

    public static final <T> Sequence<T> take(Sequence<? extends T> sequence, int n) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        if (n >= 0) {
            return n == 0 ? SequencesKt.emptySequence() : sequence instanceof DropTakeSequence ? ((DropTakeSequence) sequence).take(n) : new TakeSequence(sequence, n);
        }
        throw new IllegalArgumentException(("Requested element count " + n + " is less than zero.").toString());
    }

    public static final <T> Sequence<T> takeWhile(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        return new TakeWhileSequence(sequence, predicate);
    }

    public static final <T extends Comparable<? super T>> Sequence<T> sorted(final Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return (Sequence) new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$sorted$1
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                List sortedList = SequencesKt.toMutableList(sequence);
                CollectionsKt.sort(sortedList);
                return sortedList.iterator();
            }
        };
    }

    public static final <T, R extends Comparable<? super R>> Sequence<T> sortedBy(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        return SequencesKt.sortedWith(sequence, new ComparisonsKt__ComparisonsKt.compareBy.2(selector));
    }

    public static final <T, R extends Comparable<? super R>> Sequence<T> sortedByDescending(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        return SequencesKt.sortedWith(sequence, new ComparisonsKt__ComparisonsKt$compareByDescending$1(selector));
    }

    public static final <T extends Comparable<? super T>> Sequence<T> sortedDescending(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.sortedWith(sequence, ComparisonsKt.reverseOrder());
    }

    public static final <T> Sequence<T> sortedWith(final Sequence<? extends T> sequence, final Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        return new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$sortedWith$1
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                List sortedList = SequencesKt.toMutableList(sequence);
                CollectionsKt.sortWith(sortedList, comparator);
                return sortedList.iterator();
            }
        };
    }

    public static final <T, K, V> Map<K, V> associate(Sequence<? extends T> sequence, Function1<? super T, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : sequence) {
            Pair<? extends K, ? extends V> invoke = transform.invoke(element$iv);
            destination$iv.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination$iv;
    }

    public static final <T, K> Map<K, T> associateBy(Sequence<? extends T> sequence, Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : sequence) {
            destination$iv.put(keySelector.invoke(element$iv), element$iv);
        }
        return destination$iv;
    }

    public static final <T, K, V> Map<K, V> associateBy(Sequence<? extends T> sequence, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : sequence) {
            destination$iv.put(keySelector.invoke(element$iv), valueTransform.invoke(element$iv));
        }
        return destination$iv;
    }

    public static final <T, K, M extends Map<? super K, ? super T>> M associateByTo(Sequence<? extends T> sequence, M destination, Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (Object element : sequence) {
            destination.put(keySelector.invoke(element), element);
        }
        return destination;
    }

    public static final <T, K, V, M extends Map<? super K, ? super V>> M associateByTo(Sequence<? extends T> sequence, M destination, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (Object element : sequence) {
            destination.put(keySelector.invoke(element), valueTransform.invoke(element));
        }
        return destination;
    }

    public static final <T, K, V, M extends Map<? super K, ? super V>> M associateTo(Sequence<? extends T> sequence, M destination, Function1<? super T, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : sequence) {
            Pair<? extends K, ? extends V> invoke = transform.invoke(element);
            destination.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <K, V> Map<K, V> associateWith(Sequence<? extends K> sequence, Function1<? super K, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        LinkedHashMap result = new LinkedHashMap();
        for (Object element$iv : sequence) {
            result.put(element$iv, valueSelector.invoke(element$iv));
        }
        return result;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <K, V, M extends Map<? super K, ? super V>> M associateWithTo(Sequence<? extends K> sequence, M destination, Function1<? super K, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        for (Object element : sequence) {
            destination.put(element, valueSelector.invoke(element));
        }
        return destination;
    }

    public static final <T, C extends Collection<? super T>> C toCollection(Sequence<? extends T> sequence, C destination) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Iterator<? extends T> it = sequence.iterator();
        while (it.hasNext()) {
            destination.add(it.next());
        }
        return destination;
    }

    public static final <T> HashSet<T> toHashSet(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return (HashSet) SequencesKt.toCollection(sequence, new HashSet());
    }

    public static final <T> List<T> toList(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator it = sequence.iterator();
        if (!it.hasNext()) {
            return CollectionsKt.emptyList();
        }
        T next = it.next();
        if (!it.hasNext()) {
            return CollectionsKt.listOf(next);
        }
        ArrayList dst = new ArrayList();
        dst.add(next);
        while (it.hasNext()) {
            dst.add(it.next());
        }
        return dst;
    }

    public static final <T> List<T> toMutableList(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return (List) SequencesKt.toCollection(sequence, new ArrayList());
    }

    public static final <T> Set<T> toSet(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator it = sequence.iterator();
        if (!it.hasNext()) {
            return SetsKt.emptySet();
        }
        T next = it.next();
        if (!it.hasNext()) {
            return SetsKt.setOf(next);
        }
        LinkedHashSet dst = new LinkedHashSet();
        dst.add(next);
        while (it.hasNext()) {
            dst.add(it.next());
        }
        return dst;
    }

    public static final <T, R> Sequence<R> flatMapIterable(Sequence<? extends T> sequence, Function1<? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return new FlatteningSequence(sequence, transform, SequencesKt___SequencesKt$flatMap$1.INSTANCE);
    }

    public static final <T, R> Sequence<R> flatMap(Sequence<? extends T> sequence, Function1<? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return new FlatteningSequence(sequence, transform, SequencesKt___SequencesKt$flatMap$2.INSTANCE);
    }

    public static final <T, R> Sequence<R> flatMapIndexedIterable(Sequence<? extends T> sequence, Function2<? super Integer, ? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.flatMapIndexed(sequence, transform, SequencesKt___SequencesKt$flatMapIndexed$1.INSTANCE);
    }

    public static final <T, R> Sequence<R> flatMapIndexedSequence(Sequence<? extends T> sequence, Function2<? super Integer, ? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.flatMapIndexed(sequence, transform, SequencesKt___SequencesKt$flatMapIndexed$2.INSTANCE);
    }

    private static final <T, R, C extends Collection<? super R>> C flatMapIndexedIterableTo(Sequence<? extends T> sequence, C destination, Function2<? super Integer, ? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object element : sequence) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            Iterable list = transform.invoke(Integer.valueOf(index), element);
            CollectionsKt.addAll(destination, list);
            index = index2;
        }
        return destination;
    }

    private static final <T, R, C extends Collection<? super R>> C flatMapIndexedSequenceTo(Sequence<? extends T> sequence, C destination, Function2<? super Integer, ? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object element : sequence) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            Sequence list = transform.invoke(Integer.valueOf(index), element);
            CollectionsKt.addAll(destination, list);
            index = index2;
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C flatMapIterableTo(Sequence<? extends T> sequence, C destination, Function1<? super T, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : sequence) {
            Iterable list = transform.invoke(element);
            CollectionsKt.addAll(destination, list);
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C flatMapTo(Sequence<? extends T> sequence, C destination, Function1<? super T, ? extends Sequence<? extends R>> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element : sequence) {
            Sequence list = transform.invoke(element);
            CollectionsKt.addAll(destination, list);
        }
        return destination;
    }

    public static final <T, K> Map<K, List<T>> groupBy(Sequence<? extends T> sequence, Function1<? super T, ? extends K> keySelector) {
        Object answer$iv$iv;
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : sequence) {
            K invoke = keySelector.invoke(element$iv);
            Object value$iv$iv = destination$iv.get(invoke);
            if (value$iv$iv == null) {
                answer$iv$iv = new ArrayList();
                destination$iv.put(invoke, answer$iv$iv);
            } else {
                answer$iv$iv = value$iv$iv;
            }
            List list$iv = (List) answer$iv$iv;
            list$iv.add(element$iv);
        }
        return destination$iv;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r11v1, types: [java.util.List] */
    /* JADX WARN: Type inference failed for: r9v0, types: [java.lang.Object] */
    public static final <T, K, V> Map<K, List<V>> groupBy(Sequence<? extends T> sequence, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        Map destination$iv = new LinkedHashMap();
        for (Object element$iv : sequence) {
            K invoke = keySelector.invoke(element$iv);
            ?? r9 = destination$iv.get(invoke);
            if (r9 == 0) {
                v = new ArrayList();
                destination$iv.put(invoke, v);
            } else {
                v = r9;
            }
            List list$iv = (List) v;
            list$iv.add(valueTransform.invoke(element$iv));
        }
        return destination$iv;
    }

    public static final <T, K, M extends Map<? super K, List<T>>> M groupByTo(Sequence<? extends T> sequence, M destination, Function1<? super T, ? extends K> keySelector) {
        Object answer$iv;
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (Object element : sequence) {
            K invoke = keySelector.invoke(element);
            Object value$iv = destination.get(invoke);
            if (value$iv == null) {
                answer$iv = new ArrayList();
                destination.put(invoke, answer$iv);
            } else {
                answer$iv = value$iv;
            }
            List list = (List) answer$iv;
            list.add(element);
        }
        return destination;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r6v0, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r8v1, types: [java.util.List] */
    public static final <T, K, V, M extends Map<? super K, List<V>>> M groupByTo(Sequence<? extends T> sequence, M destination, Function1<? super T, ? extends K> keySelector, Function1<? super T, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (Object element : sequence) {
            K invoke = keySelector.invoke(element);
            ?? r6 = destination.get(invoke);
            if (r6 == 0) {
                v = new ArrayList();
                destination.put(invoke, v);
            } else {
                v = r6;
            }
            List list = (List) v;
            list.add(valueTransform.invoke(element));
        }
        return destination;
    }

    public static final <T, K> Grouping<T, K> groupingBy(final Sequence<? extends T> sequence, final Function1<? super T, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        return new Grouping<T, K>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$groupingBy$1
            @Override // kotlin.collections.Grouping
            public Iterator<T> sourceIterator() {
                return sequence.iterator();
            }

            @Override // kotlin.collections.Grouping
            public K keyOf(T element) {
                return keySelector.invoke(element);
            }
        };
    }

    public static final <T, R> Sequence<R> map(Sequence<? extends T> sequence, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return new TransformingSequence(sequence, transform);
    }

    public static final <T, R> Sequence<R> mapIndexed(Sequence<? extends T> sequence, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return new TransformingIndexedSequence(sequence, transform);
    }

    public static final <T, R> Sequence<R> mapIndexedNotNull(Sequence<? extends T> sequence, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.filterNotNull(new TransformingIndexedSequence(sequence, transform));
    }

    public static final <T, R, C extends Collection<? super R>> C mapIndexedNotNullTo(Sequence<? extends T> sequence, C destination, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object item$iv : sequence) {
            int index$iv = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            R invoke = transform.invoke(Integer.valueOf(index), item$iv);
            if (invoke != null) {
                destination.add(invoke);
            }
            index = index$iv;
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C mapIndexedTo(Sequence<? extends T> sequence, C destination, Function2<? super Integer, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        for (Object item : sequence) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            destination.add(transform.invoke(Integer.valueOf(index), item));
            index = index2;
        }
        return destination;
    }

    public static final <T, R> Sequence<R> mapNotNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.filterNotNull(new TransformingSequence(sequence, transform));
    }

    public static final <T, R, C extends Collection<? super R>> C mapNotNullTo(Sequence<? extends T> sequence, C destination, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object element$iv : sequence) {
            R invoke = transform.invoke(element$iv);
            if (invoke != null) {
                destination.add(invoke);
            }
        }
        return destination;
    }

    public static final <T, R, C extends Collection<? super R>> C mapTo(Sequence<? extends T> sequence, C destination, Function1<? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (Object item : sequence) {
            destination.add(transform.invoke(item));
        }
        return destination;
    }

    public static final <T> Sequence<IndexedValue<T>> withIndex(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return new IndexingSequence(sequence);
    }

    public static final <T> Sequence<T> distinct(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.distinctBy(sequence, new Function1<T, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$distinct$1
            @Override // kotlin.jvm.functions.Function1
            public final T invoke(T t) {
                return t;
            }
        });
    }

    public static final <T, K> Sequence<T> distinctBy(Sequence<? extends T> sequence, Function1<? super T, ? extends K> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        return new DistinctSequence(sequence, selector);
    }

    public static final <T> Set<T> toMutableSet(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        LinkedHashSet set = new LinkedHashSet();
        Iterator<? extends T> it = sequence.iterator();
        while (it.hasNext()) {
            set.add(it.next());
        }
        return set;
    }

    public static final <T> boolean all(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : sequence) {
            if (!predicate.invoke(element).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final <T> boolean any(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return sequence.iterator().hasNext();
    }

    public static final <T> boolean any(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : sequence) {
            if (predicate.invoke(element).booleanValue()) {
                return true;
            }
        }
        return false;
    }

    public static final <T> int count(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int count = 0;
        Iterator<? extends T> it = sequence.iterator();
        while (it.hasNext()) {
            it.next();
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        return count;
    }

    public static final <T> int count(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int count = 0;
        for (Object element : sequence) {
            if (predicate.invoke(element).booleanValue() && (count = count + 1) < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        return count;
    }

    public static final <T, R> R fold(Sequence<? extends T> sequence, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        Iterator<? extends T> it = sequence.iterator();
        while (it.hasNext()) {
            r2 = operation.invoke(r2, it.next());
        }
        return r2;
    }

    public static final <T, R> R foldIndexed(Sequence<? extends T> sequence, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int i = 0;
        R r2 = r;
        for (Object obj : sequence) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            r2 = operation.invoke(Integer.valueOf(i), r2, obj);
            i = i2;
        }
        return r2;
    }

    public static final <T> void forEach(Sequence<? extends T> sequence, Function1<? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        for (Object element : sequence) {
            action.invoke(element);
        }
    }

    public static final <T> void forEachIndexed(Sequence<? extends T> sequence, Function2<? super Integer, ? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        int index = 0;
        for (Object item : sequence) {
            int index2 = index + 1;
            if (index < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            action.invoke(Integer.valueOf(index), item);
            index = index2;
        }
    }

    public static final double maxOrThrow(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        double max = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            max = Math.max(max, e);
        }
        return max;
    }

    /* renamed from: maxOrThrow, reason: collision with other method in class */
    public static final float m1519maxOrThrow(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        float max = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            max = Math.max(max, e);
        }
        return max;
    }

    /* renamed from: maxOrThrow, reason: collision with other method in class */
    public static final <T extends Comparable<? super T>> T m1520maxOrThrow(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) < 0) {
                next = next2;
            }
        }
        return next;
    }

    /* JADX WARN: Type inference failed for: r2v10 */
    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v3 */
    /* JADX WARN: Type inference failed for: r2v4, types: [T] */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T maxByOrThrow(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v2 */
    /* JADX WARN: Type inference failed for: r2v3, types: [T] */
    /* JADX WARN: Type inference failed for: r2v8 */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T maxByOrNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    private static final <T> double maxOf(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.max(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return doubleValue;
    }

    /* renamed from: maxOf, reason: collision with other method in class */
    private static final <T> float m1513maxOf(Sequence<? extends T> sequence, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.max(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return floatValue;
    }

    /* renamed from: maxOf, reason: collision with other method in class */
    private static final <T, R extends Comparable<? super R>> R m1514maxOf(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final <T> Double m1515maxOfOrNull(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.max(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return Double.valueOf(doubleValue);
    }

    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final <T> Float m1516maxOfOrNull(Sequence<? extends T> sequence, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.max(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return Float.valueOf(floatValue);
    }

    private static final <T, R extends Comparable<? super R>> R maxOfOrNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R maxOfWith(Sequence<? extends T> sequence, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R maxOfWithOrNull(Sequence<? extends T> sequence, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: maxOrNull, reason: collision with other method in class */
    public static final Double m1517maxOrNull(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        double max = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            max = Math.max(max, e);
        }
        return Double.valueOf(max);
    }

    /* renamed from: maxOrNull, reason: collision with other method in class */
    public static final Float m1518maxOrNull(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        float max = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            max = Math.max(max, e);
        }
        return Float.valueOf(max);
    }

    public static final <T extends Comparable<? super T>> T maxOrNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) < 0) {
                next = next2;
            }
        }
        return next;
    }

    public static final <T> T maxWithOrThrow(Sequence<? extends T> sequence, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) < 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> T maxWithOrNull(Sequence<? extends T> sequence, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) < 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final double minOrThrow(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        double min = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            min = Math.min(min, e);
        }
        return min;
    }

    /* renamed from: minOrThrow, reason: collision with other method in class */
    public static final float m1527minOrThrow(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        float min = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            min = Math.min(min, e);
        }
        return min;
    }

    /* renamed from: minOrThrow, reason: collision with other method in class */
    public static final <T extends Comparable<? super T>> T m1528minOrThrow(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) > 0) {
                next = next2;
            }
        }
        return next;
    }

    /* JADX WARN: Type inference failed for: r2v10 */
    /* JADX WARN: Type inference failed for: r2v2, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v3 */
    /* JADX WARN: Type inference failed for: r2v4, types: [T] */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T minByOrThrow(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [T, java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v2 */
    /* JADX WARN: Type inference failed for: r2v3, types: [T] */
    /* JADX WARN: Type inference failed for: r2v8 */
    /* JADX WARN: Type inference failed for: r2v9 */
    public static final <T, R extends Comparable<? super R>> T minByOrNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        ?? r2 = (Object) it.next();
        if (!it.hasNext()) {
            return r2;
        }
        R invoke = selector.invoke(r2);
        do {
            Object next = it.next();
            R invoke2 = selector.invoke(next);
            r2 = r2;
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
                r2 = (T) next;
            }
        } while (it.hasNext());
        return (T) r2;
    }

    private static final <T> double minOf(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.min(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return doubleValue;
    }

    /* renamed from: minOf, reason: collision with other method in class */
    private static final <T> float m1521minOf(Sequence<? extends T> sequence, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.min(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return floatValue;
    }

    /* renamed from: minOf, reason: collision with other method in class */
    private static final <T, R extends Comparable<? super R>> R m1522minOf(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final <T> Double m1523minOfOrNull(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        double doubleValue = selector.invoke((Object) it.next()).doubleValue();
        while (it.hasNext()) {
            doubleValue = Math.min(doubleValue, selector.invoke((Object) it.next()).doubleValue());
        }
        return Double.valueOf(doubleValue);
    }

    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final <T> Float m1524minOfOrNull(Sequence<? extends T> sequence, Function1<? super T, Float> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        float floatValue = selector.invoke((Object) it.next()).floatValue();
        while (it.hasNext()) {
            floatValue = Math.min(floatValue, selector.invoke((Object) it.next()).floatValue());
        }
        return Float.valueOf(floatValue);
    }

    private static final <T, R extends Comparable<? super R>> R minOfOrNull(Sequence<? extends T> sequence, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            R invoke2 = selector.invoke((Object) it.next());
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R minOfWith(Sequence<? extends T> sequence, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    private static final <T, R> R minOfWithOrNull(Sequence<? extends T> sequence, Comparator<? super R> comparator, Function1<? super T, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        R invoke = selector.invoke((Object) it.next());
        while (it.hasNext()) {
            Object invoke2 = selector.invoke((Object) it.next());
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* renamed from: minOrNull, reason: collision with other method in class */
    public static final Double m1525minOrNull(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        double min = iterator.next().doubleValue();
        while (iterator.hasNext()) {
            double e = iterator.next().doubleValue();
            min = Math.min(min, e);
        }
        return Double.valueOf(min);
    }

    /* renamed from: minOrNull, reason: collision with other method in class */
    public static final Float m1526minOrNull(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        float min = iterator.next().floatValue();
        while (iterator.hasNext()) {
            float e = iterator.next().floatValue();
            min = Math.min(min, e);
        }
        return Float.valueOf(min);
    }

    public static final <T extends Comparable<? super T>> T minOrNull(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Iterator iterator = sequence.iterator();
        if (!iterator.hasNext()) {
            return null;
        }
        T next = iterator.next();
        while (iterator.hasNext()) {
            T next2 = iterator.next();
            if (next.compareTo(next2) > 0) {
                next = next2;
            }
        }
        return next;
    }

    public static final <T> T minWithOrThrow(Sequence<? extends T> sequence, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new NoSuchElementException();
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) > 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> T minWithOrNull(Sequence<? extends T> sequence, Comparator<? super T> comparator) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        T next = it.next();
        while (it.hasNext()) {
            Object next2 = it.next();
            if (comparator.compare(next, next2) > 0) {
                next = (T) next2;
            }
        }
        return next;
    }

    public static final <T> boolean none(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return !sequence.iterator().hasNext();
    }

    public static final <T> boolean none(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (Object element : sequence) {
            if (predicate.invoke(element).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final <T> Sequence<T> onEach(Sequence<? extends T> sequence, final Function1<? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        return SequencesKt.map(sequence, new Function1<T, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$onEach$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public final T invoke(T t) {
                action.invoke(t);
                return t;
            }
        });
    }

    public static final <T> Sequence<T> onEachIndexed(Sequence<? extends T> sequence, final Function2<? super Integer, ? super T, Unit> action) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        return SequencesKt.mapIndexed(sequence, new Function2<Integer, T, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$onEachIndexed$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(2);
            }

            /* JADX WARN: Multi-variable type inference failed */
            @Override // kotlin.jvm.functions.Function2
            public /* bridge */ /* synthetic */ Object invoke(Integer num, Object obj) {
                return invoke(num.intValue(), (int) obj);
            }

            public final T invoke(int index, T t) {
                action.invoke(Integer.valueOf(index), t);
                return t;
            }
        });
    }

    public static final <S, T extends S> S reduce(Sequence<? extends T> sequence, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new UnsupportedOperationException("Empty sequence can't be reduced.");
        }
        S s = (S) it.next();
        while (it.hasNext()) {
            s = operation.invoke(s, (Object) it.next());
        }
        return s;
    }

    public static final <S, T extends S> S reduceIndexed(Sequence<? extends T> sequence, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            throw new UnsupportedOperationException("Empty sequence can't be reduced.");
        }
        int i = 1;
        S s = (S) it.next();
        while (it.hasNext()) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            s = operation.invoke(Integer.valueOf(i), s, (Object) it.next());
            i = i2;
        }
        return s;
    }

    public static final <S, T extends S> S reduceIndexedOrNull(Sequence<? extends T> sequence, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        int i = 1;
        S s = (S) it.next();
        while (it.hasNext()) {
            int i2 = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            s = operation.invoke(Integer.valueOf(i), s, (Object) it.next());
            i = i2;
        }
        return s;
    }

    public static final <S, T extends S> S reduceOrNull(Sequence<? extends T> sequence, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        Iterator<? extends T> it = sequence.iterator();
        if (!it.hasNext()) {
            return null;
        }
        S s = (S) it.next();
        while (it.hasNext()) {
            s = operation.invoke(s, (Object) it.next());
        }
        return s;
    }

    public static final <T, R> Sequence<R> runningFold(Sequence<? extends T> sequence, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.sequence(new SequencesKt___SequencesKt$runningFold$1(r, sequence, operation, null));
    }

    public static final <T, R> Sequence<R> runningFoldIndexed(Sequence<? extends T> sequence, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.sequence(new SequencesKt___SequencesKt$runningFoldIndexed$1(r, sequence, operation, null));
    }

    public static final <S, T extends S> Sequence<S> runningReduce(Sequence<? extends T> sequence, Function2<? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.sequence(new SequencesKt___SequencesKt$runningReduce$1(sequence, operation, null));
    }

    public static final <S, T extends S> Sequence<S> runningReduceIndexed(Sequence<? extends T> sequence, Function3<? super Integer, ? super S, ? super T, ? extends S> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.sequence(new SequencesKt___SequencesKt$runningReduceIndexed$1(sequence, operation, null));
    }

    public static final <T, R> Sequence<R> scan(Sequence<? extends T> sequence, R r, Function2<? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.runningFold(sequence, r, operation);
    }

    public static final <T, R> Sequence<R> scanIndexed(Sequence<? extends T> sequence, R r, Function3<? super Integer, ? super R, ? super T, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        return SequencesKt.runningFoldIndexed(sequence, r, operation);
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final <T> int sumBy(Sequence<? extends T> sequence, Function1<? super T, Integer> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (Object element : sequence) {
            sum += selector.invoke(element).intValue();
        }
        return sum;
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final <T> double sumByDouble(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (Object element : sequence) {
            sum += selector.invoke(element).doubleValue();
        }
        return sum;
    }

    private static final <T> double sumOfDouble(Sequence<? extends T> sequence, Function1<? super T, Double> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (Object element : sequence) {
            sum += selector.invoke(element).doubleValue();
        }
        return sum;
    }

    private static final <T> int sumOfInt(Sequence<? extends T> sequence, Function1<? super T, Integer> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (Object element : sequence) {
            sum += selector.invoke(element).intValue();
        }
        return sum;
    }

    private static final <T> long sumOfLong(Sequence<? extends T> sequence, Function1<? super T, Long> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = 0;
        for (Object element : sequence) {
            sum += selector.invoke(element).longValue();
        }
        return sum;
    }

    private static final <T> int sumOfUInt(Sequence<? extends T> sequence, Function1<? super T, UInt> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = UInt.m332constructorimpl(0);
        for (Object element : sequence) {
            sum = UInt.m332constructorimpl(selector.invoke(element).getData() + sum);
        }
        return sum;
    }

    private static final <T> long sumOfULong(Sequence<? extends T> sequence, Function1<? super T, ULong> selector) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = ULong.m411constructorimpl(0L);
        for (Object element : sequence) {
            sum = ULong.m411constructorimpl(selector.invoke(element).getData() + sum);
        }
        return sum;
    }

    public static final <T> Sequence<T> requireNoNulls(final Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.map(sequence, new Function1<T, T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$requireNoNulls$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public final T invoke(T t) {
                if (t != null) {
                    return t;
                }
                throw new IllegalArgumentException("null element found in " + sequence + '.');
            }
        });
    }

    public static final <T> Sequence<List<T>> chunked(Sequence<? extends T> sequence, int size) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.windowed(sequence, size, size, true);
    }

    public static final <T, R> Sequence<R> chunked(Sequence<? extends T> sequence, int size, Function1<? super List<? extends T>, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.windowed(sequence, size, size, true, transform);
    }

    public static final <T> Sequence<T> minus(final Sequence<? extends T> sequence, final T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$1
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                final Ref.BooleanRef removed = new Ref.BooleanRef();
                Sequence<T> sequence2 = sequence;
                final T t2 = t;
                return SequencesKt.filter(sequence2, new Function1<T, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$1$iterator$1
                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    {
                        super(1);
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // kotlin.jvm.functions.Function1
                    public final Boolean invoke(T t3) {
                        boolean z = true;
                        if (!Ref.BooleanRef.this.element && Intrinsics.areEqual(t3, t2)) {
                            Ref.BooleanRef.this.element = true;
                            z = false;
                        }
                        return Boolean.valueOf(z);
                    }

                    /* JADX WARN: Multi-variable type inference failed */
                    @Override // kotlin.jvm.functions.Function1
                    public /* bridge */ /* synthetic */ Boolean invoke(Object obj) {
                        return invoke((SequencesKt___SequencesKt$minus$1$iterator$1<T>) obj);
                    }
                }).iterator();
            }
        };
    }

    /* JADX WARN: Multi-variable type inference failed */
    public static final <T> Sequence<T> minus(final Sequence<? extends T> sequence, final T[] elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return elements.length == 0 ? sequence : new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$2
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                Sequence<T> sequence2 = sequence;
                final T[] tArr = elements;
                return SequencesKt.filterNot(sequence2, new Function1<T, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$2$iterator$1
                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    {
                        super(1);
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // kotlin.jvm.functions.Function1
                    public final Boolean invoke(T t) {
                        return Boolean.valueOf(ArraysKt.contains(tArr, t));
                    }

                    /* JADX WARN: Multi-variable type inference failed */
                    @Override // kotlin.jvm.functions.Function1
                    public /* bridge */ /* synthetic */ Boolean invoke(Object obj) {
                        return invoke((SequencesKt___SequencesKt$minus$2$iterator$1<T>) obj);
                    }
                }).iterator();
            }
        };
    }

    public static final <T> Sequence<T> minus(final Sequence<? extends T> sequence, final Iterable<? extends T> elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$3
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                final Collection other = CollectionsKt.convertToListIfNotCollection(elements);
                if (other.isEmpty()) {
                    return sequence.iterator();
                }
                return SequencesKt.filterNot(sequence, new Function1<T, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$3$iterator$1
                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    /* JADX WARN: Multi-variable type inference failed */
                    {
                        super(1);
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // kotlin.jvm.functions.Function1
                    public final Boolean invoke(T t) {
                        return Boolean.valueOf(other.contains(t));
                    }

                    /* JADX WARN: Multi-variable type inference failed */
                    @Override // kotlin.jvm.functions.Function1
                    public /* bridge */ /* synthetic */ Boolean invoke(Object obj) {
                        return invoke((SequencesKt___SequencesKt$minus$3$iterator$1<T>) obj);
                    }
                }).iterator();
            }
        };
    }

    public static final <T> Sequence<T> minus(final Sequence<? extends T> sequence, final Sequence<? extends T> elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return new Sequence<T>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$4
            @Override // kotlin.sequences.Sequence
            public Iterator<T> iterator() {
                final List other = SequencesKt.toList(elements);
                if (other.isEmpty()) {
                    return sequence.iterator();
                }
                return SequencesKt.filterNot(sequence, new Function1<T, Boolean>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$minus$4$iterator$1
                    /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
                    /* JADX WARN: Multi-variable type inference failed */
                    {
                        super(1);
                    }

                    /* JADX WARN: Can't rename method to resolve collision */
                    @Override // kotlin.jvm.functions.Function1
                    public final Boolean invoke(T t) {
                        return Boolean.valueOf(other.contains(t));
                    }

                    /* JADX WARN: Multi-variable type inference failed */
                    @Override // kotlin.jvm.functions.Function1
                    public /* bridge */ /* synthetic */ Boolean invoke(Object obj) {
                        return invoke((SequencesKt___SequencesKt$minus$4$iterator$1<T>) obj);
                    }
                }).iterator();
            }
        };
    }

    private static final <T> Sequence<T> minusElement(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.minus(sequence, t);
    }

    public static final <T> Pair<List<T>, List<T>> partition(Sequence<? extends T> sequence, Function1<? super T, Boolean> predicate) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        ArrayList first = new ArrayList();
        ArrayList second = new ArrayList();
        for (Object element : sequence) {
            if (predicate.invoke(element).booleanValue()) {
                first.add(element);
            } else {
                second.add(element);
            }
        }
        return new Pair<>(first, second);
    }

    public static final <T> Sequence<T> plus(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.flatten(SequencesKt.sequenceOf(sequence, SequencesKt.sequenceOf(t)));
    }

    public static final <T> Sequence<T> plus(Sequence<? extends T> sequence, T[] elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return SequencesKt.plus((Sequence) sequence, (Iterable) ArraysKt.asList(elements));
    }

    public static final <T> Sequence<T> plus(Sequence<? extends T> sequence, Iterable<? extends T> elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return SequencesKt.flatten(SequencesKt.sequenceOf(sequence, CollectionsKt.asSequence(elements)));
    }

    public static final <T> Sequence<T> plus(Sequence<? extends T> sequence, Sequence<? extends T> elements) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(elements, "elements");
        return SequencesKt.flatten(SequencesKt.sequenceOf(sequence, elements));
    }

    private static final <T> Sequence<T> plusElement(Sequence<? extends T> sequence, T t) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.plus(sequence, t);
    }

    public static /* synthetic */ Sequence windowed$default(Sequence sequence, int i, int i2, boolean z, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return SequencesKt.windowed(sequence, i, i2, z);
    }

    public static final <T> Sequence<List<T>> windowed(Sequence<? extends T> sequence, int size, int step, boolean partialWindows) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SlidingWindowKt.windowedSequence(sequence, size, step, partialWindows, false);
    }

    public static /* synthetic */ Sequence windowed$default(Sequence sequence, int i, int i2, boolean z, Function1 function1, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return SequencesKt.windowed(sequence, i, i2, z, function1);
    }

    public static final <T, R> Sequence<R> windowed(Sequence<? extends T> sequence, int size, int step, boolean partialWindows, Function1<? super List<? extends T>, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.map(SlidingWindowKt.windowedSequence(sequence, size, step, partialWindows, true), transform);
    }

    public static final <T, R> Sequence<Pair<T, R>> zip(Sequence<? extends T> sequence, Sequence<? extends R> other) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        return new MergingSequence(sequence, other, new Function2<T, R, Pair<? extends T, ? extends R>>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$zip$1
            /* JADX WARN: Multi-variable type inference failed */
            @Override // kotlin.jvm.functions.Function2
            public /* bridge */ /* synthetic */ Object invoke(Object obj, Object obj2) {
                return invoke((SequencesKt___SequencesKt$zip$1<R, T>) obj, obj2);
            }

            @Override // kotlin.jvm.functions.Function2
            public final Pair<T, R> invoke(T t, R r) {
                return TuplesKt.to(t, r);
            }
        });
    }

    public static final <T, R, V> Sequence<V> zip(Sequence<? extends T> sequence, Sequence<? extends R> other, Function2<? super T, ? super R, ? extends V> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return new MergingSequence(sequence, other, transform);
    }

    public static final <T> Sequence<Pair<T, T>> zipWithNext(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return SequencesKt.zipWithNext(sequence, new Function2<T, T, Pair<? extends T, ? extends T>>() { // from class: kotlin.sequences.SequencesKt___SequencesKt$zipWithNext$1
            @Override // kotlin.jvm.functions.Function2
            public final Pair<T, T> invoke(T t, T t2) {
                return TuplesKt.to(t, t2);
            }
        });
    }

    public static final <T, R> Sequence<R> zipWithNext(Sequence<? extends T> sequence, Function2<? super T, ? super T, ? extends R> transform) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return SequencesKt.sequence(new SequencesKt___SequencesKt$zipWithNext$2(sequence, transform, null));
    }

    public static final <T, A extends Appendable> A joinTo(Sequence<? extends T> sequence, A buffer, CharSequence separator, CharSequence prefix, CharSequence postfix, int limit, CharSequence truncated, Function1<? super T, ? extends CharSequence> function1) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(buffer, "buffer");
        Intrinsics.checkNotNullParameter(separator, "separator");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Intrinsics.checkNotNullParameter(postfix, "postfix");
        Intrinsics.checkNotNullParameter(truncated, "truncated");
        buffer.append(prefix);
        int count = 0;
        for (Object element : sequence) {
            count++;
            if (count > 1) {
                buffer.append(separator);
            }
            if (limit >= 0 && count > limit) {
                break;
            }
            StringsKt.appendElement(buffer, element, function1);
        }
        if (limit >= 0 && count > limit) {
            buffer.append(truncated);
        }
        buffer.append(postfix);
        return buffer;
    }

    public static /* synthetic */ String joinToString$default(Sequence sequence, CharSequence charSequence, CharSequence charSequence2, CharSequence charSequence3, int i, CharSequence charSequence4, Function1 function1, int i2, Object obj) {
        if ((i2 & 1) != 0) {
        }
        if ((i2 & 2) != 0) {
        }
        CharSequence charSequence5 = charSequence2;
        if ((i2 & 4) != 0) {
        }
        CharSequence charSequence6 = charSequence3;
        if ((i2 & 8) != 0) {
            i = -1;
        }
        int i3 = i;
        if ((i2 & 16) != 0) {
        }
        CharSequence charSequence7 = charSequence4;
        if ((i2 & 32) != 0) {
            function1 = null;
        }
        return SequencesKt.joinToString(sequence, charSequence, charSequence5, charSequence6, i3, charSequence7, function1);
    }

    public static final <T> String joinToString(Sequence<? extends T> sequence, CharSequence separator, CharSequence prefix, CharSequence postfix, int limit, CharSequence truncated, Function1<? super T, ? extends CharSequence> function1) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        Intrinsics.checkNotNullParameter(separator, "separator");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Intrinsics.checkNotNullParameter(postfix, "postfix");
        Intrinsics.checkNotNullParameter(truncated, "truncated");
        String sb = ((StringBuilder) SequencesKt.joinTo(sequence, new StringBuilder(), separator, prefix, postfix, limit, truncated, function1)).toString();
        Intrinsics.checkNotNullExpressionValue(sb, "joinTo(StringBuilder(), …ed, transform).toString()");
        return sb;
    }

    public static final <T> Iterable<T> asIterable(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return new SequencesKt___SequencesKt$asIterable$$inlined$Iterable$1(sequence);
    }

    /* JADX WARN: Multi-variable type inference failed */
    private static final <T> Sequence<T> asSequence(Sequence<? extends T> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        return sequence;
    }

    public static final double averageOfByte(Sequence<Byte> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Byte> it = sequence.iterator();
        while (it.hasNext()) {
            byte element = it.next().byteValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfShort(Sequence<Short> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Short> it = sequence.iterator();
        while (it.hasNext()) {
            short element = it.next().shortValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfInt(Sequence<Integer> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Integer> it = sequence.iterator();
        while (it.hasNext()) {
            int element = it.next().intValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfLong(Sequence<Long> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Long> it = sequence.iterator();
        while (it.hasNext()) {
            long element = it.next().longValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfFloat(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Float> it = sequence.iterator();
        while (it.hasNext()) {
            float element = it.next().floatValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final double averageOfDouble(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        int count = 0;
        Iterator<Double> it = sequence.iterator();
        while (it.hasNext()) {
            double element = it.next().doubleValue();
            sum += element;
            count++;
            if (count < 0) {
                CollectionsKt.throwCountOverflow();
            }
        }
        if (count == 0) {
            return Double.NaN;
        }
        return sum / count;
    }

    public static final int sumOfByte(Sequence<Byte> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int sum = 0;
        Iterator<Byte> it = sequence.iterator();
        while (it.hasNext()) {
            byte element = it.next().byteValue();
            sum += element;
        }
        return sum;
    }

    public static final int sumOfShort(Sequence<Short> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int sum = 0;
        Iterator<Short> it = sequence.iterator();
        while (it.hasNext()) {
            short element = it.next().shortValue();
            sum += element;
        }
        return sum;
    }

    public static final int sumOfInt(Sequence<Integer> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        int sum = 0;
        Iterator<Integer> it = sequence.iterator();
        while (it.hasNext()) {
            int element = it.next().intValue();
            sum += element;
        }
        return sum;
    }

    public static final long sumOfLong(Sequence<Long> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        long sum = 0;
        Iterator<Long> it = sequence.iterator();
        while (it.hasNext()) {
            long element = it.next().longValue();
            sum += element;
        }
        return sum;
    }

    public static final float sumOfFloat(Sequence<Float> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        float sum = 0.0f;
        Iterator<Float> it = sequence.iterator();
        while (it.hasNext()) {
            float element = it.next().floatValue();
            sum += element;
        }
        return sum;
    }

    public static final double sumOfDouble(Sequence<Double> sequence) {
        Intrinsics.checkNotNullParameter(sequence, "<this>");
        double sum = 0.0d;
        Iterator<Double> it = sequence.iterator();
        while (it.hasNext()) {
            double element = it.next().doubleValue();
            sum += element;
        }
        return sum;
    }
}
