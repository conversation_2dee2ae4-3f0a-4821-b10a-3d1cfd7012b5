package o.n;

import android.content.Context;
import android.content.res.Resources;
import android.os.CancellationSignal;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import java.util.Date;
import o.ee.o;
import o.f.e;
import o.f.f;
import o.i.n;
import o.n.a;
import o.o.b;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\d.smali */
public final class d extends o.o.c {
    private static int y;
    final boolean b;
    final n d;
    c e;
    private final String k;
    private b n;

    /* renamed from: o, reason: collision with root package name */
    private FragmentManager f93o;
    private final boolean p;
    private final boolean q;
    private final String r;
    private final e s;
    private final int t;
    private final int v;
    private static int x = 0;
    private static final int a = R.string.antelopPinPromptName;
    private static final int c = R.string.antelopPinPromptDefaultTitle;
    private static final int g = R.string.antelopPinPromptDefaultSubtitle;
    private static final int j = R.string.antelopPinPromptDefaultPinErrorMessage;
    private static final int f = R.bool.antelopPinPromptShowCancelButton;
    private static final int h = R.drawable.antelopPinPromptIcon;
    private static final int i = R.bool.antelopPinPromptRandomizeKeyboard;
    private static final int l = R.drawable.antelopPinPromptBulletIcon;
    private static final int m = R.integer.antelopPinPromptPinSize;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\d$e.smali */
    public interface e {
        String getMessage(int i);
    }

    static {
        y = 1;
        int i2 = (x + 36) - 1;
        y = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 14 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.o.c
    public final boolean c() {
        int i2 = x;
        int i3 = ((i2 | 31) << 1) - (i2 ^ 31);
        y = i3 % 128;
        switch (i3 % 2 == 0 ? 'S' : (char) 21) {
            case Opcodes.AASTORE /* 83 */:
                return false;
            default:
                return true;
        }
    }

    private static String c(Context context) {
        int i2 = y;
        int i3 = ((i2 | Opcodes.LSHR) << 1) - (i2 ^ Opcodes.LSHR);
        x = i3 % 128;
        int i4 = i3 % 2;
        String string = context.getResources().getString(a);
        int i5 = x;
        int i6 = (i5 ^ 67) + ((i5 & 67) << 1);
        y = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                return string;
            default:
                int i7 = 23 / 0;
                return string;
        }
    }

    private static String b(Context context) {
        int i2 = y + 25;
        x = i2 % 128;
        int i3 = i2 % 2;
        String string = context.getResources().getString(c);
        int i4 = x;
        int i5 = ((i4 | 97) << 1) - (i4 ^ 97);
        y = i5 % 128;
        int i6 = i5 % 2;
        return string;
    }

    private static String a(Context context) {
        int i2 = y + 15;
        x = i2 % 128;
        boolean z = i2 % 2 == 0;
        Resources resources = context.getResources();
        switch (z) {
            case false:
                int i3 = 8 / 0;
                return resources.getString(g);
            default:
                return resources.getString(g);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ String d(String str, int i2) {
        int i3 = y + Opcodes.LNEG;
        x = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                objArr[1] = Integer.valueOf(i2);
                return String.format(str, objArr);
            default:
                return String.format(str, Integer.valueOf(i2));
        }
    }

    private static e d(Context context) {
        int i2 = y;
        int i3 = (i2 & 19) + (i2 | 19);
        x = i3 % 128;
        int i4 = i3 % 2;
        final String string = context.getResources().getString(j);
        e eVar = new e() { // from class: o.n.d$$ExternalSyntheticLambda0
            @Override // o.n.d.e
            public final String getMessage(int i5) {
                String d;
                d = d.d(string, i5);
                return d;
            }
        };
        int i5 = x;
        int i6 = (i5 & 57) + (i5 | 57);
        y = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                return eVar;
            default:
                throw null;
        }
    }

    private static boolean e(Context context) {
        int i2 = (y + Opcodes.ISHL) - 1;
        x = i2 % 128;
        int i3 = i2 % 2;
        boolean z = context.getResources().getBoolean(i);
        int i4 = y;
        int i5 = (i4 ^ 31) + ((i4 & 31) << 1);
        x = i5 % 128;
        switch (i5 % 2 != 0 ? ';' : '-') {
            case '-':
                return z;
            default:
                int i6 = 47 / 0;
                return z;
        }
    }

    private static boolean g(Context context) {
        int i2 = x;
        int i3 = (i2 & 41) + (i2 | 41);
        y = i3 % 128;
        int i4 = i3 % 2;
        boolean z = context.getResources().getBoolean(f);
        int i5 = (x + 40) - 1;
        y = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    private static int f(Context context) {
        int i2 = x;
        int i3 = (i2 ^ Opcodes.LNEG) + ((i2 & Opcodes.LNEG) << 1);
        y = i3 % 128;
        int i4 = i3 % 2;
        int integer = context.getResources().getInteger(m);
        int i5 = x;
        int i6 = (i5 & 85) + (i5 | 85);
        y = i6 % 128;
        switch (i6 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return integer;
        }
    }

    public d(Context context, String str, String str2, e eVar, n nVar, boolean z) {
        super(c(context), h);
        if (str == null) {
            this.k = o.e((CharSequence) b(context));
        } else {
            this.k = o.e((CharSequence) str);
        }
        if (str2 == null) {
            this.r = o.e((CharSequence) a(context));
        } else {
            this.r = o.e((CharSequence) str2);
        }
        if (eVar == null) {
            this.s = d(context);
        } else {
            this.s = eVar;
        }
        this.q = e(context);
        this.p = g(context);
        this.t = l;
        this.v = f(context);
        this.d = nVar;
        this.b = z;
    }

    public final String j() {
        int i2 = x;
        int i3 = ((i2 | 61) << 1) - (i2 ^ 61);
        int i4 = i3 % 128;
        y = i4;
        Object obj = null;
        switch (i3 % 2 == 0 ? 'T' : '*') {
            case Opcodes.BASTORE /* 84 */:
                obj.hashCode();
                throw null;
            default:
                String str = this.k;
                int i5 = ((i4 | 85) << 1) - (i4 ^ 85);
                x = i5 % 128;
                switch (i5 % 2 != 0 ? (char) 11 : 'V') {
                    case Opcodes.SASTORE /* 86 */:
                        return str;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final String i() {
        int i2 = x;
        int i3 = (i2 ^ 75) + ((i2 & 75) << 1);
        int i4 = i3 % 128;
        y = i4;
        int i5 = i3 % 2;
        String str = this.r;
        int i6 = (i4 ^ 65) + ((i4 & 65) << 1);
        x = i6 % 128;
        int i7 = i6 % 2;
        return str;
    }

    public final boolean g() {
        boolean z;
        int i2 = x;
        int i3 = (i2 & Opcodes.DSUB) + (i2 | Opcodes.DSUB);
        int i4 = i3 % 128;
        y = i4;
        switch (i3 % 2 == 0 ? '\'' : '.') {
            case '\'':
                z = this.q;
                int i5 = 4 / 0;
                break;
            default:
                z = this.q;
                break;
        }
        int i6 = i4 + 63;
        x = i6 % 128;
        int i7 = i6 % 2;
        return z;
    }

    public final boolean f() {
        int i2 = (x + 30) - 1;
        y = i2 % 128;
        switch (i2 % 2 == 0 ? 'J' : (char) 25) {
            case 'J':
                throw null;
            default:
                return this.p;
        }
    }

    public final int h() {
        int i2 = (y + Opcodes.IAND) - 1;
        x = i2 % 128;
        switch (i2 % 2 != 0 ? '\f' : '_') {
            case '\f':
                int i3 = 16 / 0;
                return this.t;
            default:
                return this.t;
        }
    }

    public final int m() {
        int i2 = y;
        int i3 = (i2 ^ Opcodes.LSHL) + ((i2 & Opcodes.LSHL) << 1);
        int i4 = i3 % 128;
        x = i4;
        int i5 = i3 % 2;
        int i6 = this.v;
        int i7 = ((i4 | 69) << 1) - (i4 ^ 69);
        y = i7 % 128;
        int i8 = i7 % 2;
        return i6;
    }

    @Override // o.o.c
    public final void d(final FragmentActivity fragmentActivity, int i2, CancellationSignal cancellationSignal, final b bVar) {
        this.e = new c(this, new a.InterfaceC0045a() { // from class: o.n.d.5
            private static int e = 0;
            private static int b = 1;

            /* renamed from: o.n.d$5$1, reason: invalid class name */
            /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\n\d$5$1.smali */
            final class AnonymousClass1 implements o.g.a {
                private static int b = 0;
                private static int e = 1;

                AnonymousClass1() {
                }

                @Override // o.g.a
                public final void d(o.f.e eVar) {
                    int i = b;
                    int i2 = ((i | 95) << 1) - (i ^ 95);
                    e = i2 % 128;
                    boolean z = i2 % 2 != 0;
                    bVar.b(eVar, d.this);
                    switch (z) {
                        case false:
                            throw null;
                        default:
                            return;
                    }
                }

                /* JADX WARN: Removed duplicated region for block: B:12:0x0033  */
                /* JADX WARN: Removed duplicated region for block: B:23:0x0074  */
                @Override // o.g.a
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public final void c(o.g.b r5) {
                    /*
                        r4 = this;
                        int r0 = o.n.d.AnonymousClass5.AnonymousClass1.e
                        r1 = r0 & 69
                        r0 = r0 | 69
                        int r1 = r1 + r0
                        int r0 = r1 % 128
                        o.n.d.AnonymousClass5.AnonymousClass1.b = r0
                        int r1 = r1 % 2
                        r0 = 0
                        r2 = 1
                        if (r1 == 0) goto L13
                        r1 = r0
                        goto L14
                    L13:
                        r1 = r2
                    L14:
                        switch(r1) {
                            case 1: goto L1a;
                            default: goto L17;
                        }
                    L17:
                        o.g.b r1 = o.g.b.e
                        goto L27
                    L1a:
                        o.g.b r1 = o.g.b.e
                        if (r5 != r1) goto L21
                        r5 = 52
                        goto L23
                    L21:
                        r5 = 58
                    L23:
                        switch(r5) {
                            case 52: goto L33;
                            default: goto L26;
                        }
                    L26:
                        goto L84
                    L27:
                        r3 = 69
                        int r3 = r3 / r0
                        if (r5 != r1) goto L2e
                        r5 = r2
                        goto L2f
                    L2e:
                        r5 = r0
                    L2f:
                        switch(r5) {
                            case 1: goto L33;
                            default: goto L32;
                        }
                    L32:
                        goto L26
                    L33:
                        int r5 = o.n.d.AnonymousClass5.AnonymousClass1.e
                        r1 = r5 | 63
                        int r1 = r1 << r2
                        r5 = r5 ^ 63
                        int r1 = r1 - r5
                        int r5 = r1 % 128
                        o.n.d.AnonymousClass5.AnonymousClass1.b = r5
                        int r1 = r1 % 2
                        if (r1 == 0) goto L45
                        r5 = r0
                        goto L46
                    L45:
                        r5 = r2
                    L46:
                        switch(r5) {
                            case 1: goto L54;
                            default: goto L49;
                        }
                    L49:
                        o.n.d$5 r5 = o.n.d.AnonymousClass5.this
                        o.n.d r5 = o.n.d.this
                        o.i.n r5 = r5.d
                        boolean r5 = r5.l()
                        goto L66
                    L54:
                        o.n.d$5 r5 = o.n.d.AnonymousClass5.this
                        o.n.d r5 = o.n.d.this
                        o.i.n r5 = r5.d
                        boolean r5 = r5.l()
                        if (r5 == 0) goto L61
                        goto L62
                    L61:
                        r0 = r2
                    L62:
                        switch(r0) {
                            case 1: goto L26;
                            default: goto L65;
                        }
                    L65:
                        goto L74
                    L66:
                        r1 = 42
                        int r1 = r1 / r0
                        if (r5 == 0) goto L6e
                        r5 = 71
                        goto L70
                    L6e:
                        r5 = 85
                    L70:
                        switch(r5) {
                            case 71: goto L65;
                            default: goto L73;
                        }
                    L73:
                        goto L26
                    L74:
                        o.n.d$5 r5 = o.n.d.AnonymousClass5.this
                        o.o.b r5 = r2
                        o.o.e r0 = o.o.e.c
                        o.n.d$5 r1 = o.n.d.AnonymousClass5.this
                        o.n.d r1 = o.n.d.this
                        r5.c(r0, r1)
                        return
                    L82:
                        r5 = move-exception
                        throw r5
                    L84:
                        o.n.d$5 r5 = o.n.d.AnonymousClass5.this
                        androidx.fragment.app.FragmentActivity r5 = r3
                        o.n.d$5$1$$ExternalSyntheticLambda0 r0 = new o.n.d$5$1$$ExternalSyntheticLambda0
                        r0.<init>()
                        r5.runOnUiThread(r0)
                        int r5 = o.n.d.AnonymousClass5.AnonymousClass1.e
                        r0 = r5 | 39
                        int r0 = r0 << r2
                        r5 = r5 ^ 39
                        int r0 = r0 - r5
                        int r5 = r0 % 128
                        o.n.d.AnonymousClass5.AnonymousClass1.b = r5
                        int r0 = r0 % 2
                        return
                    L9f:
                        r5 = move-exception
                        throw r5
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.n.d.AnonymousClass5.AnonymousClass1.c(o.g.b):void");
                }

                /* JADX INFO: Access modifiers changed from: private */
                public /* synthetic */ void b() {
                    int i = b;
                    int i2 = ((i | 69) << 1) - (i ^ 69);
                    e = i2 % 128;
                    int i3 = i2 % 2;
                    d.this.e.a(d.this.o());
                    d.this.e.b();
                    int i4 = (e + Opcodes.FNEG) - 1;
                    b = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case false:
                            return;
                        default:
                            throw null;
                    }
                }
            }

            @Override // o.n.a.InterfaceC0045a
            public final void onPasscodeEntryDone(byte[] bArr) {
                f fVar = new f(e.d.c, new Date(), new o.f.d(bArr));
                Object obj = null;
                switch (d.this.d.l() ? '*' : ' ') {
                    case ' ':
                        break;
                    default:
                        int i3 = b;
                        int i4 = (i3 & 89) + (i3 | 89);
                        e = i4 % 128;
                        switch (i4 % 2 != 0 ? ',' : 'O') {
                            case ',':
                                boolean z = d.this.b;
                                throw null;
                            default:
                                switch (d.this.b ? ',' : '=') {
                                    case ',':
                                        break;
                                    default:
                                        int i5 = b + 13;
                                        e = i5 % 128;
                                        boolean z2 = i5 % 2 != 0;
                                        bVar.c(o.o.e.c, d.this);
                                        switch (z2) {
                                            case true:
                                                throw null;
                                            default:
                                                return;
                                        }
                                }
                        }
                }
                if (d.this.b) {
                    bVar.b(new f(e.d.b, new Date(), new o.f.d(bArr)), d.this);
                    int i6 = e;
                    int i7 = ((i6 | 35) << 1) - (i6 ^ 35);
                    b = i7 % 128;
                    if (i7 % 2 != 0) {
                        return;
                    } else {
                        throw null;
                    }
                }
                d.this.d.d(fragmentActivity, fVar, new AnonymousClass1(), new o.i.b(o.i.d.c(), fVar.b()), d.this.b);
                int i8 = e;
                int i9 = (i8 ^ 95) + ((i8 & 95) << 1);
                b = i9 % 128;
                switch (i9 % 2 != 0) {
                    case true:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.n.a.InterfaceC0045a
            public final void onKeyPressed() {
                int i3 = b;
                int i4 = ((i3 | Opcodes.LREM) << 1) - (i3 ^ Opcodes.LREM);
                e = i4 % 128;
                int i5 = i4 % 2;
                d.this.e.a(null);
                int i6 = e;
                int i7 = (i6 & 65) + (i6 | 65);
                b = i7 % 128;
                int i8 = i7 % 2;
            }

            @Override // o.n.a.InterfaceC0045a
            public final void onExtraButtonPressed() {
                int i3 = e + Opcodes.DREM;
                b = i3 % 128;
                int i4 = i3 % 2;
                switch (d.this.a() == null) {
                    case true:
                        break;
                    default:
                        int i5 = e;
                        int i6 = (i5 ^ Opcodes.LUSHR) + ((i5 & Opcodes.LUSHR) << 1);
                        b = i6 % 128;
                        int i7 = i6 % 2;
                        bVar.e(d.this);
                        int i8 = b;
                        int i9 = (i8 & Opcodes.DREM) + (i8 | Opcodes.DREM);
                        e = i9 % 128;
                        if (i9 % 2 == 0) {
                            break;
                        } else {
                            break;
                        }
                }
            }
        });
        switch (this.d.l() ? '1' : 'b') {
            case Opcodes.FADD /* 98 */:
                FragmentManager supportFragmentManager = fragmentActivity.getSupportFragmentManager();
                this.f93o = supportFragmentManager;
                this.n = bVar;
                supportFragmentManager.beginTransaction().replace(i2, this.e).addToBackStack(null).commit();
                int i3 = (y + 36) - 1;
                x = i3 % 128;
                int i4 = i3 % 2;
                break;
            default:
                int i5 = x;
                int i6 = (i5 & 77) + (i5 | 77);
                y = i6 % 128;
                if (i6 % 2 == 0) {
                }
                bVar.c(o.o.e.c, this);
                break;
        }
    }

    final void l() {
        int i2 = y;
        int i3 = (i2 ^ 7) + ((i2 & 7) << 1);
        x = i3 % 128;
        switch (i3 % 2 != 0 ? '\t' : '3') {
            case '\t':
                this.f93o.beginTransaction().remove(this.e).commit();
                this.n.c(o.o.e.a, this);
                throw null;
            default:
                this.f93o.beginTransaction().remove(this.e).commit();
                this.n.c(o.o.e.a, this);
                return;
        }
    }

    final String o() {
        String message;
        int i2 = x;
        int i3 = (i2 ^ 31) + ((i2 & 31) << 1);
        y = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0 ? 'X' : 'b') {
            case Opcodes.FADD /* 98 */:
                e eVar = this.s;
                switch (eVar != null ? 'F' : (char) 4) {
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        int i4 = (i2 + 36) - 1;
                        y = i4 % 128;
                        switch (i4 % 2 != 0) {
                            case true:
                                short b = this.d.b();
                                int i5 = -this.d.e();
                                message = eVar.getMessage((b ^ i5) + ((b & i5) << 1));
                                break;
                            default:
                                message = eVar.getMessage(this.d.b() + this.d.e());
                                break;
                        }
                        return o.e((CharSequence) message);
                    default:
                        int i6 = (i2 & Opcodes.DMUL) + (i2 | Opcodes.DMUL);
                        y = i6 % 128;
                        switch (i6 % 2 == 0) {
                            case true:
                                obj.hashCode();
                                throw null;
                            default:
                                return null;
                        }
                }
            default:
                obj.hashCode();
                throw null;
        }
    }
}
