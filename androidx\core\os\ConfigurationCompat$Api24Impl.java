package androidx.core.os;

import android.content.res.Configuration;
import android.os.LocaleList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\os\ConfigurationCompat$Api24Impl.smali */
class ConfigurationCompat$Api24Impl {
    private ConfigurationCompat$Api24Impl() {
    }

    static LocaleList getLocales(Configuration configuration) {
        return configuration.getLocales();
    }

    static void setLocales(Configuration configuration, LocaleListCompat locales) {
        configuration.setLocales((LocaleList) locales.unwrap());
    }
}
