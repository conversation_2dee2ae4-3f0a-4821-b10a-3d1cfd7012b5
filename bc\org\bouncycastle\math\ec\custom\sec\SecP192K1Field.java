package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192K1Field.smali */
public class SecP192K1Field {
    static final int[] a = {-4553, -2, -1, -1, -1, -1};
    private static final int[] b = {20729809, 9106, 1, 0, 0, 0, -9106, -3, -1, -1, -1, -1};
    private static final int[] c = {-20729809, -9107, -2, -1, -1, -1, 9105, 2};

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.a(iArr, iArr2, iArr3) != 0 || (iArr3[5] == -1 && u5.b(iArr3, a))) {
            c6.a(6, 4553, iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.a(12, iArr, iArr2, iArr3) != 0 || (iArr3[11] == -1 && c6.d(12, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(12, iArr3, iArr4.length);
            }
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(6, iArr, iArr2) != 0 || (iArr2[5] == -1 && u5.b(iArr2, a))) {
            c6.a(6, 4553, iArr2);
        }
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = u5.a(bigInteger);
        if (a2[5] == -1) {
            int[] iArr = a;
            if (u5.b(a2, iArr)) {
                u5.d(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(6, iArr, 0, iArr2);
        } else {
            c6.d(6, iArr2, u5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 6; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] c2 = u5.c();
        u5.c(iArr, iArr2, c2);
        reduce(c2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.d(iArr, iArr2, iArr3) != 0 || (iArr3[11] == -1 && c6.d(12, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(12, iArr3, iArr4.length);
            }
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            u5.e(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            u5.e(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[24];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 6);
        } while (c6.f(6, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        if (u5.a(4553, u5.a(4553, iArr, 6, iArr, 0, iArr2, 0), iArr2, 0) != 0 || (iArr2[5] == -1 && u5.b(iArr2, a))) {
            c6.a(6, 4553, iArr2);
        }
    }

    public static void reduce32(int i, int[] iArr) {
        if ((i == 0 || u5.a(4553, i, iArr, 0) == 0) && !(iArr[5] == -1 && u5.b(iArr, a))) {
            return;
        }
        c6.a(6, 4553, iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] c2 = u5.c();
        u5.c(iArr, c2);
        reduce(c2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] c2 = u5.c();
        u5.c(iArr, c2);
        reduce(c2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            u5.c(iArr2, c2);
            reduce(c2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.e(iArr, iArr2, iArr3) != 0) {
            c6.c(6, 4553, iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(12, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            if (c6.g(iArr4.length, iArr4, iArr3) != 0) {
                c6.a(12, iArr3, iArr4.length);
            }
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(6, iArr, 0, iArr2) != 0 || (iArr2[5] == -1 && u5.b(iArr2, a))) {
            c6.a(6, 4553, iArr2);
        }
    }
}
