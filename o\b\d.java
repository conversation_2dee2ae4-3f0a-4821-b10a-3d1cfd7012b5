package o.b;

import android.graphics.ImageFormat;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final /* synthetic */ d[] a;
    public static final d b;
    private static long c;
    public static final d d;
    public static final d e;
    private static int g;
    private static char h;
    private static int i;
    private static int j;

    static void d() {
        h = (char) 17957;
        g = 161105445;
        c = -7231878931692208781L;
    }

    static void init$0() {
        $$a = new byte[]{118, -84, -110, 65};
        $$b = 15;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r6 = 106 - r6
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r0 = o.b.d.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L1a:
            r3 = r2
        L1b:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.d.k(byte, byte, byte, java.lang.Object[]):void");
    }

    private d(String str, int i2) {
    }

    private static /* synthetic */ d[] b() {
        d[] dVarArr;
        int i2 = j + 21;
        int i3 = i2 % 128;
        i = i3;
        switch (i2 % 2 != 0) {
            case true:
                dVarArr = new d[4];
                dVarArr[1] = d;
                dVarArr[1] = e;
                dVarArr[3] = b;
                break;
            default:
                dVarArr = new d[]{d, e, b};
                break;
        }
        int i4 = i3 + 97;
        j = i4 % 128;
        int i5 = i4 % 2;
        return dVarArr;
    }

    public static d valueOf(String str) {
        int i2 = j + 5;
        i = i2 % 128;
        boolean z = i2 % 2 == 0;
        d dVar = (d) Enum.valueOf(d.class, str);
        switch (z) {
            case true:
                int i3 = j + 109;
                i = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        int i4 = 40 / 0;
                        return dVar;
                    default:
                        return dVar;
                }
            default:
                throw null;
        }
    }

    public static d[] values() {
        int i2 = i + 53;
        j = i2 % 128;
        int i3 = i2 % 2;
        d[] dVarArr = (d[]) a.clone();
        int i4 = i + 59;
        j = i4 % 128;
        switch (i4 % 2 == 0 ? 'b' : (char) 5) {
            case 5:
                return dVarArr;
            default:
                int i5 = 78 / 0;
                return dVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        j = 1;
        d();
        Object[] objArr = new Object[1];
        f(View.combineMeasuredStates(0, 0) + 1179092181, "蒽壆䲏輅媆섪⌟", (char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), "픓䞄깆\uf07e", "ﭖ血긫삽", objArr);
        d = new d(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        f(View.resolveSizeAndState(0, 0, 0), "뿯盚\udfd1ඥ⡐藅汈哪ᕋ\ue47e䁱쬔베䐃밴\uf292姉鰔立\uda8b\uf4fe㲎噼輻迗ﱮ", (char) (37251 - ImageFormat.getBitsPerPixel(0)), "\u2072饸蓭疑", "ﭖ血긫삽", objArr2);
        e = new d(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        f(View.resolveSizeAndState(0, 0, 0) + 570388082, "ﶣ\u0ee9쯝䣓ፂ\uf01b쩙弩䩘", (char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), "爸ｮ\ue121돔", "ﭖ血긫삽", objArr3);
        b = new d(((String) objArr3[0]).intern(), 2);
        a = b();
        int i2 = j + 11;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r18, java.lang.String r19, char r20, java.lang.String r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 678
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.d.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
