package bc.org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\d.smali */
public class d extends c {
    public d() {
        this(q1.ANY);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public m5 copy() {
        return new d(this);
    }

    protected p1 d() {
        return g.a(this, 256, this.a);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i) {
        b();
        j6.a(this.f, bArr, i);
        j6.a(this.g, bArr, i + 8);
        j6.a(this.h, bArr, i + 16);
        j6.a(this.i, bArr, i + 24);
        j6.a(this.j, bArr, i + 32);
        j6.a(this.k, bArr, i + 40);
        j6.a(this.l, bArr, i + 48);
        j6.a(this.m, bArr, i + 56);
        reset();
        return 64;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public String getAlgorithmName() {
        return "SHA-512";
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int getDigestSize() {
        return 64;
    }

    @Override // bc.org.bouncycastle.crypto.digests.c, bc.org.bouncycastle.crypto.Digest
    public void reset() {
        super.reset();
        this.f = 7640891576956012808L;
        this.g = -4942790177534073029L;
        this.h = 4354685564936845355L;
        this.i = -6534734903238641935L;
        this.j = 5840696475078001361L;
        this.k = -7276294671716946913L;
        this.l = 2270897969802886507L;
        this.m = 6620516959819538809L;
    }

    public d(q1 q1Var) {
        super(q1Var);
        t1.a(d());
        reset();
    }

    public d(d dVar) {
        super(dVar);
        t1.a(d());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public void reset(m5 m5Var) {
        a((d) m5Var);
    }
}
