package o.bp;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.ee.c;
import o.ee.e;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bp\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int c;
    private static final b d;
    private static int e;
    private Long a = null;

    static void c() {
        b = new char[]{50854, 50712, 50716, 50694, 50787, 50809, 50690, 50717, 50713, 50717, 50718, 50709, 50718, 50716, 50718, 50814, 50795, 50804, 50812, 50810, 50810, 50785, 50813, 50786, 50790, 50813, 50811, 50811, 50815, 50813, 50811, 50802, 50689, 50691, 50844, 50797, 50775, 50769, 50797, 50797, 50775, 50776, 50772, 50775, 50779, 50796, 50796, 50774, 50798, 50781, 50768, 50800, 50806, 50800, 50703, 50800, 50807, 50803, 50807, 50804, 50771, 50773, 50808, 50806, 50802, 50807, 50805, 50811, 50754, 50865, 50865, 50783, 50804, 50807, 50800, 50703, 50800, 50807, 50776, 50779, 50800, 50806, 50800, 50703, 50800, 50807, 50803, 50807, 50788, 50932, 50854, 50855, 50851, 50853, 50843, 50838, 50855, 50855, 50838, 50842, 50857, 50859, 50850, 50848, 50841, 50870, 50722, 50714, 50714, 50726, 50750, 50720, 50704, 50732, 50723, 50721, 50722, 50747, 50720, 50727, 50748, 50694, 50788, 50788, 50696, 50724, 50727, 50724, 50718, 50813, 50697, 50721, 50748, 50748, 50744, 50922, 50856, 50874, 50868, 50867, 50871, 50873};
    }

    private static void g(short s, short s2, byte b2, Object[] objArr) {
        byte[] bArr = $$a;
        int i = 122 - s2;
        int i2 = b2 + 4;
        int i3 = 1 - (s * 4);
        byte[] bArr2 = new byte[i3];
        int i4 = -1;
        int i5 = i3 - 1;
        if (bArr == null) {
            int i6 = (-i5) + i2;
            i2 = i2;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
            i = i6;
            i5 = i5;
        }
        while (true) {
            int i7 = i4 + 1;
            bArr2[i7] = (byte) i;
            int i8 = i2 + 1;
            if (i7 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i8];
            i2 = i8;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = i7;
            i = (-b3) + i;
            i5 = i5;
        }
    }

    static void init$0() {
        $$a = new byte[]{0, -16, -96, 75};
        $$b = Opcodes.INVOKESPECIAL;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        c();
        d = new b();
        int i = c + 77;
        e = i % 128;
        switch (i % 2 == 0 ? Typography.greater : (char) 25) {
            case 25:
                return;
            default:
                throw null;
        }
    }

    public static b b() {
        int i = e + 35;
        int i2 = i % 128;
        c = i2;
        Object obj = null;
        switch (i % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                b bVar = d;
                int i3 = i2 + 61;
                e = i3 % 128;
                switch (i3 % 2 == 0 ? 'N' : 'a') {
                    case 'N':
                        obj.hashCode();
                        throw null;
                    default:
                        return bVar;
                }
        }
    }

    private b() {
    }

    public static String a() {
        int i = e + 23;
        c = i % 128;
        int i2 = i % 2;
        e.a();
        String e2 = c.e();
        int i3 = c + 99;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return e2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static String e() {
        int i = e + 9;
        c = i % 128;
        int i2 = i % 2;
        e.a();
        String c2 = c.c();
        int i3 = e + 3;
        c = i3 % 128;
        int i4 = i3 % 2;
        return c2;
    }

    public static boolean d(Context context) {
        int i = e + 55;
        c = i % 128;
        boolean z = i % 2 != 0;
        Object obj = null;
        e.a();
        switch (z) {
            case false:
                boolean a = c.a(context);
                int i2 = e + 19;
                c = i2 % 128;
                switch (i2 % 2 != 0 ? '5' : '2') {
                    case '2':
                        return a;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                c.a(context);
                throw null;
        }
    }

    public static boolean c(Context context) {
        int i = c + 79;
        e = i % 128;
        int i2 = i % 2;
        e.a();
        boolean e2 = c.e(context);
        int i3 = e + 75;
        c = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    public static boolean e(Context context) throws o.bt.d {
        switch (1) {
            case 1:
                int i = c + 13;
                e = i % 128;
                int i2 = i % 2;
                Object[] objArr = new Object[1];
                f("\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000", new int[]{0, 34, Opcodes.LSUB, 31}, false, objArr);
                switch (o.bt.b.e(context, ((String) objArr[0]).intern()) ? 'Z' : 'E') {
                    case 'Z':
                        int i3 = c + 77;
                        e = i3 % 128;
                        int i4 = i3 % 2;
                        e.a();
                        return c.i(context);
                    default:
                        Object[] objArr2 = new Object[1];
                        f("\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{34, 55, 79, 0}, true, objArr2);
                        throw new o.bt.d(((String) objArr2[0]).intern());
                }
            default:
                e.a();
                boolean i5 = c.i(context);
                int i6 = e + 85;
                c = i6 % 128;
                int i7 = i6 % 2;
                return i5;
        }
    }

    public final Long d() {
        int i = c + 33;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        Long l = this.a;
        int i4 = i2 + 59;
        c = i4 % 128;
        int i5 = i4 % 2;
        return l;
    }

    public final void c(Long l) {
        e.a();
        long longValue = l.longValue() - c.j();
        g.c();
        Object[] objArr = new Object[1];
        f("\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{89, 16, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0001", new int[]{Opcodes.LMUL, 30, Opcodes.LXOR, 25}, false, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(longValue).toString());
        switch (Math.abs(longValue) < 60) {
            case false:
                int i = e + 1;
                c = i % 128;
                int i2 = i % 2;
                this.a = Long.valueOf(longValue);
                return;
            default:
                Object obj = null;
                this.a = null;
                int i3 = c + Opcodes.DDIV;
                e = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 23 : (char) 11) {
                    case 11:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:111:0x001b, code lost:
    
        r0 = r0.getBytes(org.bouncycastle.i18n.LocalizedMessage.DEFAULT_ENCODING);
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x0019, code lost:
    
        if (r0 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0013, code lost:
    
        if (r0 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x02d8, code lost:
    
        r3 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 816
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bp.b.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
