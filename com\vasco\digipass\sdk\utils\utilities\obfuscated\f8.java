package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f8.smali */
public class f8 extends u {
    private ECPoint C;
    private final x b;
    private ECCurve x;

    public f8(ECPoint eCPoint, boolean z) {
        this.C = eCPoint.normalize();
        this.b = new f2(eCPoint.getEncoded(z));
    }

    public synchronized ECPoint e() {
        if (this.C == null) {
            this.C = this.x.decodePoint(this.b.h()).normalize();
        }
        return this.C;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        return this.b;
    }

    public f8(ECCurve eCCurve, byte[] bArr) {
        this.x = eCCurve;
        this.b = new f2(Arrays.clone(bArr));
    }

    public f8(ECCurve eCCurve, x xVar) {
        this(eCCurve, xVar.h());
    }
}
