package com.google.android.gms.fido.fido2.api.common;

/* compiled from: com.google.android.gms:play-services-fido@@20.0.1 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\fido\fido2\api\common\UserVerificationMethods.smali */
public final class UserVerificationMethods {
    public static final int USER_VERIFY_ALL = 1024;
    public static final int USER_VERIFY_EYEPRINT = 64;
    public static final int USER_VERIFY_FACEPRINT = 16;
    public static final int USER_VERIFY_FINGERPRINT = 2;
    public static final int USER_VERIFY_HANDPRINT = 256;
    public static final int USER_VERIFY_LOCATION = 32;
    public static final int USER_VERIFY_NONE = 512;
    public static final int USER_VERIFY_PASSCODE = 4;
    public static final int USER_VERIFY_PATTERN = 128;
    public static final int USER_VERIFY_PRESENCE = 1;
    public static final int USER_VERIFY_VOICEPRINT = 8;

    private UserVerificationMethods() {
    }
}
