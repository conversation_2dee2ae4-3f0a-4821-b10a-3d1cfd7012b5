package com.google.android.datatransport;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\Encoding.smali */
public final class Encoding {
    private final String name;

    public static Encoding of(String name) {
        return new Encoding(name);
    }

    public String getName() {
        return this.name;
    }

    private Encoding(String name) {
        if (name == null) {
            throw new NullPointerException("name is null");
        }
        this.name = name;
    }

    public boolean equals(Object o2) {
        if (this == o2) {
            return true;
        }
        if (o2 instanceof Encoding) {
            return this.name.equals(((Encoding) o2).name);
        }
        return false;
    }

    public int hashCode() {
        int h = 1000003 ^ this.name.hashCode();
        return h;
    }

    public String toString() {
        return "Encoding{name=\"" + this.name + "\"}";
    }
}
