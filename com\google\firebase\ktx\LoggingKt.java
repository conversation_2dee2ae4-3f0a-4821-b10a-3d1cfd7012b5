package com.google.firebase.ktx;

import kotlin.Metadata;

/* compiled from: Logging.kt */
@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0010\u000e\n\u0000\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0080T¢\u0006\u0002\n\u0000¨\u0006\u0002"}, d2 = {"LIBRARY_NAME", "", "com.google.firebase-firebase-common-ktx"}, k = 2, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\ktx\LoggingKt.smali */
public final class LoggingKt {
    public static final String LIBRARY_NAME = "fire-core-ktx";
}
