package o.ce;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ce\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int c;
    private static char d;
    private static char[] e;
    private static int f;
    private static int h;
    private static boolean i;
    private static boolean j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        b();
        ViewConfiguration.getJumpTapTimeout();
        KeyEvent.keyCodeFromString("");
        int i2 = f + 81;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 70 / 0;
                break;
        }
    }

    static void b() {
        d = (char) 36454;
        a = 161105445;
        b = 6565854932352255525L;
        e = new char[]{61772, 61778, 61767, 61768, 61773, 61764, 61715, 61760, 61779, 61781, 61777, 61714, 61787, 61712, 61782, 61748, 61734, 61766, 61739, 61783, 61736, 61771, 61770, 61697, 61708, 61751, 61741, 61727, 61769, 61774, 61728, 61753, 61716, 61713, 61720, 61735, 61742, 61750};
        i = true;
        j = true;
        c = 782103009;
    }

    static void init$0() {
        $$a = new byte[]{105, 1, -115, -23};
        $$b = 9;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0030  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0028  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0030 -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ce.e.$$a
            int r8 = r8 * 4
            int r8 = 3 - r8
            int r6 = r6 + 99
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r6 = r8
            r3 = r1
            r4 = r2
            r8 = r7
            r1 = r0
            r0 = r9
            r9 = r6
            goto L39
        L1b:
            r3 = r2
            r5 = r8
            r8 = r6
            r6 = r5
        L1f:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            int r6 = r6 + 1
            if (r3 != r7) goto L30
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L30:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L39:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.e.l(int, int, short, java.lang.Object[]):void");
    }

    public final HttpsURLConnection d(Context context) throws PackageManager.NameNotFoundException, c {
        HttpsURLConnection e2;
        try {
            switch (!d()) {
                case false:
                    int i2 = h + 91;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                    e2 = e();
                    int i4 = h + 45;
                    f = i4 % 128;
                    if (i4 % 2 == 0) {
                        break;
                    } else {
                        break;
                    }
                default:
                    e2 = b(context);
                    int i5 = h + 95;
                    f = i5 % 128;
                    int i6 = i5 % 2;
                    break;
            }
            Object[] objArr = new Object[1];
            g((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) - 2045517399, "蛨㡽㕊+\ue783\uef5aϚ?ݮ操⮏ᨄ㥅", (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 595), "꥟Ꮱ咆鄂", "\u0000\u0000\u0000\u0000", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1, "ꖝ䙔긜쿺Ἒ꾄ੜ嶁", (char) Drawable.resolveOpacity(0, 0), "\ue682笞읝먂", "\u0000\u0000\u0000\u0000", objArr2);
            e2.setRequestProperty(intern, ((String) objArr2[0]).intern());
            e2.setUseCaches(false);
            int i7 = h + 87;
            f = i7 % 128;
            int i8 = i7 % 2;
            return e2;
        } catch (IOException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException | CertificateException e3) {
            g.c();
            Object[] objArr3 = new Object[1];
            g((-1007178706) - (ViewConfiguration.getScrollDefaultDelay() >> 16), "॓\uef3f沟탎ᖕ䇽皺㺎⥭\ude8e\udca7职틬뿸䑊ᐆ㿪ꬵ愢锈貔躿\ue5e4쎦䌓", (char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), "⸞\uf7ac폃ᗼ", "\u0000\u0000\u0000\u0000", objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g(View.MeasureSpec.makeMeasureSpec(0, 0), "\uf78a奯岤ﾘඌ긼쪔奄ဩꠚ嬹\uf011硄븰鷫撅見訮၃넚妳ś嬨ಶྻ됇ᴸ㵎鳶\ue038迸쬲炜 㚣ꓡ\uf27d\uebbaꃁ燀᷸鱪噯꺊앬蝂ѐ\uf1cfჽ㑡벛䬃᧖ᖢ\udefd汸ꆐ㜆ﭫ句㌺芐\ua82e䍔ᒂꁘ", (char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 21441), "뒱ຮ셅큓", "\u0000\u0000\u0000\u0000", objArr4);
            g.a(intern2, ((String) objArr4[0]).intern(), e3);
            throw new c(e3.getMessage());
        }
    }

    public static HttpsURLConnection b(URL url) throws c {
        int i2 = f + Opcodes.DDIV;
        h = i2 % 128;
        int i3 = i2 % 2;
        try {
            HttpsURLConnection d2 = d(url, false);
            int i4 = h + Opcodes.LUSHR;
            f = i4 % 128;
            switch (i4 % 2 != 0 ? '=' : (char) 23) {
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    throw null;
                default:
                    return d2;
            }
        } catch (IOException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException | CertificateException e2) {
            g.c();
            Object[] objArr = new Object[1];
            g((-1007178706) - TextUtils.indexOf("", "", 0), "॓\uef3f沟탎ᖕ䇽皺㺎⥭\ude8e\udca7职틬뿸䑊ᐆ㿪ꬵ愢锈貔躿\ue5e4쎦䌓", (char) View.resolveSizeAndState(0, 0, 0), "⸞\uf7ac폃ᗼ", "\u0000\u0000\u0000\u0000", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g((-582948812) - Gravity.getAbsoluteGravity(0, 0), "ꔜ䣁羌䤩ౙ篇薗锛둦鞪鶹合稅\ue4ab늺羨\ue2c8泿퇒맔㎣\ud936Ӽ⇀≡\uab2f鳫\udf8dޜ壜\ue892䀈䊃䃸⁂︧ໆ᧱駈㞲䏡쁆㓁梅雃ꑡ맶\uec51⡙捔﹫鱩⍏┙쵿\ude8d葔\u2458케\uf59aᲣ\uf053䇿ꄺ\ufae8ٶƺ믥֗噉䲐햶屢瓁嶪\udc83崩ᦐ鲻⎆䦰⫓̭", (char) (((byte) KeyEvent.getModifierMetaStateMask()) + 51561), "㐧䃨棝ᯉ", "\u0000\u0000\u0000\u0000", objArr2);
            g.e(intern, String.format(((String) objArr2[0]).intern(), e2.getClass().getSimpleName(), e2.getMessage()));
            Object[] objArr3 = new Object[1];
            g((-1007178707) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "॓\uef3f沟탎ᖕ䇽皺㺎⥭\ude8e\udca7职틬뿸䑊ᐆ㿪ꬵ愢锈貔躿\ue5e4쎦䌓", (char) (AndroidCharacter.getMirror('0') - '0'), "⸞\uf7ac폃ᗼ", "\u0000\u0000\u0000\u0000", objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1854558135, "抱︖逴舻⁰荂/盉\ude7cଃｂ宫\ue8f7遚ἣ৹ﰕ㋲祗骷⢿腁䅄ꎛ뺿阛鶨逘똔禹\ue020蓌݁㷫佭컿킙\uf41d源㊷ꡑ髂\ue01c\ue2c1㯼Ꮃ鋒ꖖ\ud7c8ꥺ抜㛄ꮢ䟩걉\ude36竰躹븱梇꽙끡䟖琐씼∩ႏअ혠\ueb3f呀圍剴", (char) KeyEvent.normalizeMetaState(0), "롘詏뽮\ue95d", "\u0000\u0000\u0000\u0000", objArr4);
            g.a(intern2, ((String) objArr4[0]).intern(), e2);
            throw new c(e2.getMessage());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x0034, code lost:
    
        if (((java.lang.String) r5[0]).intern().isEmpty() == false) goto L17;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static boolean d() {
        /*
            int r0 = o.ce.e.h
            int r0 = r0 + 31
            int r1 = r0 % 128
            o.ce.e.f = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 22
            goto L11
        Lf:
            r0 = 37
        L11:
            java.lang.String r1 = "\u008a\u0086\u0089\u0087\u008b\u0082\u0085\u0086\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            r2 = 0
            r3 = 1
            r4 = 0
            switch(r0) {
                case 37: goto L37;
                default: goto L19;
            }
        L19:
            int r0 = android.view.KeyEvent.getMaxKeyCode()
            int r0 = r0 / 5
            r5 = 85
            int r0 = r5 << r0
            java.lang.Object[] r5 = new java.lang.Object[r3]
            k(r4, r0, r4, r1, r5)
            r0 = r5[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r0.isEmpty()
            if (r0 != 0) goto L76
        L36:
            goto L5b
        L37:
            int r0 = android.view.KeyEvent.getMaxKeyCode()
            int r0 = r0 >> 16
            int r0 = 127 - r0
            java.lang.Object[] r5 = new java.lang.Object[r3]
            k(r4, r0, r4, r1, r5)
            r0 = r5[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            boolean r0 = r0.isEmpty()
            if (r0 != 0) goto L55
            r0 = 52
            goto L57
        L55:
            r0 = 78
        L57:
            switch(r0) {
                case 52: goto L36;
                default: goto L5a;
            }
        L5a:
            goto L76
        L5b:
            int r0 = o.ce.e.f
            int r1 = r0 + 103
            int r2 = r1 % 128
            o.ce.e.h = r2
            int r1 = r1 % 2
            int r0 = r0 + 101
            int r1 = r0 % 128
            o.ce.e.h = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L70
            return r3
        L70:
            r4.hashCode()     // Catch: java.lang.Throwable -> L74
            throw r4     // Catch: java.lang.Throwable -> L74
        L74:
            r0 = move-exception
            throw r0
        L76:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.e.d():boolean");
    }

    private static HttpsURLConnection e() throws IOException, KeyStoreException, NoSuchAlgorithmException, CertificateException, KeyManagementException {
        Object[] objArr = new Object[1];
        g(ViewConfiguration.getKeyRepeatDelay() >> 16, "욇ⷼ煈膷\ue122慙\uf3c9ꏵ", (char) View.MeasureSpec.getSize(0), "\u0a0c㾬〒歞", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, Drawable.resolveOpacity(0, 0) + 127, null, "\u008a\u0086\u0089\u0087\u008b\u0082\u0085\u0086\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        k(null, 127 - Color.green(0), null, "\u0086\u0085\u0084\u0083\u0082\u0081\u008c\u008e\u008d\u008c\u0084\u008b\u0088\u008c\u008b\u0088\u0081\u008c", objArr3);
        String obj = new StringBuilder().append(intern).append(intern2).append(((String) objArr3[0]).intern()).toString();
        g.c();
        Object[] objArr4 = new Object[1];
        g((ViewConfiguration.getPressedStateDuration() >> 16) - 1007178706, "॓\uef3f沟탎ᖕ䇽皺㺎⥭\ude8e\udca7职틬뿸䑊ᐆ㿪ꬵ愢锈貔躿\ue5e4쎦䌓", (char) Color.argb(0, 0, 0, 0), "⸞\uf7ac폃ᗼ", "\u0000\u0000\u0000\u0000", objArr4);
        String intern3 = ((String) objArr4[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        k(null, TextUtils.lastIndexOf("", '0', 0) + 128, null, "\u0098\u009c\u0098\u009b\u009a\u0090\u0098\u0099\u0098\u0097\u0084\u0096\u0089\u0082\u0091\u0085\u0088\u0089\u0094\u0086\u008a\u0089\u0095\u0081\u0082\u0094\u0093\u0089\u0082\u0084\u008a\u0092\u0086\u0089\u0089\u0082\u0091\u008b\u0090\u008a\u0086\u008f", objArr5);
        g.d(intern3, sb.append(((String) objArr5[0]).intern()).append(obj).toString());
        HttpsURLConnection d2 = d(new URL(obj), true);
        int i2 = h + 75;
        f = i2 % 128;
        int i3 = i2 % 2;
        return d2;
    }

    private static HttpsURLConnection d(URL url, boolean z) throws IOException, KeyStoreException, NoSuchAlgorithmException, CertificateException, KeyManagementException {
        Object[] objArr = new Object[1];
        k(null, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 127, null, "\u0086\u008d\u0084\u0085\u009f\u008b\u0086\u0086\u009e\u0087\u008b\u008a\u008a\u009d", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((-549561429) - (ViewConfiguration.getFadingEdgeLength() >> 16), "\uec7b䦈韪\udedd鉫", (char) (43737 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), "ꯇ㹛\ud9df₪", "\u0000\u0000\u0000\u0000", objArr2);
        System.setProperty(intern, ((String) objArr2[0]).intern());
        HttpsURLConnection httpsURLConnection = (HttpsURLConnection) url.openConnection();
        switch (!z) {
            case true:
                break;
            default:
                String[] strArr = fr.antelop.sdk.b.d;
                TrustManager[] trustManagerArr = new TrustManager[strArr.length];
                for (int i2 = 0; i2 < strArr.length; i2++) {
                    Object[] objArr3 = new Object[1];
                    k(null, Color.argb(0, 0, 0, 0) + 127, null, "£¢¡\u0087 ", objArr3);
                    CertificateFactory certificateFactory = CertificateFactory.getInstance(((String) objArr3[0]).intern());
                    String str = strArr[i2];
                    Object[] objArr4 = new Object[1];
                    g(2016155571 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "恴", (char) (56765 - View.resolveSize(0, 0)), "덡Ⱇ뵸뇝", "\u0000\u0000\u0000\u0000", objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    Object[] objArr5 = new Object[1];
                    g(1569793801 - (Process.myPid() >> 22), "\uab6d", (char) (49019 - TextUtils.getOffsetBefore("", 0)), "च鄧筝\ue5bf", "\u0000\u0000\u0000\u0000", objArr5);
                    X509Certificate x509Certificate = (X509Certificate) certificateFactory.generateCertificate(new ByteArrayInputStream(str.replace(intern2, ((String) objArr5[0]).intern()).getBytes(StandardCharsets.UTF_8)));
                    KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
                    keyStore.load(null, null);
                    Object[] objArr6 = new Object[1];
                    k(null, ((Process.getThreadPriority(0) + 20) >> 6) + 127, null, "\u0088\u0092", objArr6);
                    keyStore.setCertificateEntry(((String) objArr6[0]).intern(), x509Certificate);
                    TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
                    trustManagerFactory.init(keyStore);
                    trustManagerArr[i2] = new d((X509TrustManager) trustManagerFactory.getTrustManagers()[0], fr.antelop.sdk.b.b[i2], fr.antelop.sdk.b.c[i2]);
                }
                httpsURLConnection.setSSLSocketFactory(new b(trustManagerArr));
                int i3 = h + 11;
                f = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
        Object[] objArr7 = new Object[1];
        g((-2045517399) - TextUtils.indexOf("", "", 0), "蛨㡽㕊+\ue783\uef5aϚ?ݮ操⮏ᨄ㥅", (char) (596 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), "꥟Ꮱ咆鄂", "\u0000\u0000\u0000\u0000", objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        g(ViewConfiguration.getLongPressTimeout() >> 16, "ꖝ䙔긜쿺Ἒ꾄ੜ嶁", (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "\ue682笞읝먂", "\u0000\u0000\u0000\u0000", objArr8);
        httpsURLConnection.setRequestProperty(intern3, ((String) objArr8[0]).intern());
        httpsURLConnection.setUseCaches(false);
        int i5 = f + 35;
        h = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return httpsURLConnection;
            default:
                throw null;
        }
    }

    private static HttpsURLConnection b(Context context) throws PackageManager.NameNotFoundException, IOException, KeyStoreException, NoSuchAlgorithmException, CertificateException, KeyManagementException {
        Object[] objArr = new Object[1];
        g(AndroidCharacter.getMirror('0') - '0', "욇ⷼ煈膷\ue122慙\uf3c9ꏵ", (char) Color.argb(0, 0, 0, 0), "\u0a0c㾬〒歞", "\u0000\u0000\u0000\u0000", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((-1726849909) - (ViewConfiguration.getScrollDefaultDelay() >> 16), "餦୰ɗᜧĽ오ৱ䰘ﺄꄚ㸧\u070f꒦돻榜䠆뿡믙뾨\uedc8ၑ힞ⷺ", (char) ((-1) - Process.getGidForName("")), "诞ቜᒙꁜ", "\u0000\u0000\u0000\u0000", objArr2);
        String a2 = o.a(context, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(Process.getGidForName("") - 1405516607, "\udce4餲୴元ᜀᣗ젴爨ℶꏃ➤╲뗏廳駤\ue847葋ⶫ亡", (char) ((-16744686) - Color.rgb(0, 0, 0)), "쀕㦄ኬ\ue07f", "\u0000\u0000\u0000\u0000", objArr3);
        String obj = new StringBuilder().append(intern).append(a2).append(o.a(context, ((String) objArr3[0]).intern())).toString();
        g.c();
        Object[] objArr4 = new Object[1];
        g(View.resolveSize(0, 0) - 1007178706, "॓\uef3f沟탎ᖕ䇽皺㺎⥭\ude8e\udca7职틬뿸䑊ᐆ㿪ꬵ愢锈貔躿\ue5e4쎦䌓", (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "⸞\uf7ac폃ᗼ", "\u0000\u0000\u0000\u0000", objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        g(TextUtils.getCapsMode("", 0, 0), "ᦡ\uf151誄\udfb2檷쫷䉙粋ㄤ썎\udb1f喆\uedb8讎쿷ෝ\uf2eb䘵⡶檾ꃯ琅\u181b\ue2df╁례\ud8c8띦鉊秷巬䖏Ᏼ绅봩Ƈ㵬衽饼ଥ➥\uf2a2", (char) (32894 - View.combineMeasuredStates(0, 0)), "\ue0e3㫃繕Ⲁ", "\u0000\u0000\u0000\u0000", objArr5);
        g.d(intern2, sb.append(((String) objArr5[0]).intern()).append(obj).toString());
        URL url = new URL(obj);
        Object[] objArr6 = new Object[1];
        k(null, 126 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), null, "\u0086\u008d\u0084\u0085\u009f\u008b\u0086\u0086\u009e\u0087\u008b\u008a\u008a\u009d", objArr6);
        String intern3 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        g((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 549561430, "\uec7b䦈韪\udedd鉫", (char) (43736 - TextUtils.indexOf((CharSequence) "", '0')), "ꯇ㹛\ud9df₪", "\u0000\u0000\u0000\u0000", objArr7);
        System.setProperty(intern3, ((String) objArr7[0]).intern());
        Object[] objArr8 = new Object[1];
        k(null, (ViewConfiguration.getLongPressTimeout() >> 16) + 127, null, "¦¥¤", objArr8);
        KeyStore keyStore = KeyStore.getInstance(((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        k(null, 127 - (ViewConfiguration.getScrollDefaultDelay() >> 16), null, "£¢¡ ", objArr9);
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(((String) objArr9[0]).intern());
        trustManagerFactory.init(keyStore);
        HttpsURLConnection httpsURLConnection = (HttpsURLConnection) url.openConnection();
        httpsURLConnection.setSSLSocketFactory(new b(trustManagerFactory.getTrustManagers()));
        Object[] objArr10 = new Object[1];
        g((ViewConfiguration.getPressedStateDuration() >> 16) - 2045517399, "蛨㡽㕊+\ue783\uef5aϚ?ݮ操⮏ᨄ㥅", (char) (595 - TextUtils.lastIndexOf("", '0', 0, 0)), "꥟Ꮱ咆鄂", "\u0000\u0000\u0000\u0000", objArr10);
        String intern4 = ((String) objArr10[0]).intern();
        Object[] objArr11 = new Object[1];
        g((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1, "ꖝ䙔긜쿺Ἒ꾄ੜ嶁", (char) View.MeasureSpec.makeMeasureSpec(0, 0), "\ue682笞읝먂", "\u0000\u0000\u0000\u0000", objArr11);
        httpsURLConnection.setRequestProperty(intern4, ((String) objArr11[0]).intern());
        httpsURLConnection.setUseCaches(false);
        int i2 = f + 13;
        h = i2 % 128;
        int i3 = i2 % 2;
        return httpsURLConnection;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int r22, java.lang.String r23, char r24, java.lang.String r25, java.lang.String r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 734
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.e.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 924
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.e.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
