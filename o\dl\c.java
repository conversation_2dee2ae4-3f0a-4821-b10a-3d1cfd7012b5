package o.dl;

import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.l;
import o.h.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dl\c.smali */
public final class c extends a {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int c;
    private static final o.h.d d;
    private static int e;

    static void a() {
        a = new char[]{50819, 50770, 50754, 50752, 50773, 50783, 50777, 50774, 50782, 50782, 50772, 50778, 50797, 50775, 50783, 50774, 50771, 50769, 50771, 50757, 50759, 50774, 50777, 50777, 50772, 50796, 50771, 50776};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dl.c.$$d
            int r8 = r8 * 2
            int r8 = r8 + 4
            int r9 = r9 * 2
            int r9 = 1 - r9
            int r7 = 122 - r7
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L34
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1c:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r7]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r8 = r8 + r7
            int r7 = r9 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.c.h(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{62, -87, 120, -83};
        $$e = Opcodes.ISHL;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        a();
        d = new o.h.d();
        int i = e + 83;
        c = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public c() {
        /*
            r4 = this;
            r0 = 48
            r1 = 8
            r2 = 0
            r3 = 28
            int[] r0 = new int[]{r2, r3, r0, r1}
            r1 = 1
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r3 = "\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000"
            g(r3, r0, r2, r1)
            r0 = r1[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            o.i.i r1 = o.i.i.e
            r4.<init>(r0, r1, r2)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.c.<init>():void");
    }

    public static o.h.d b() {
        int i = e;
        int i2 = i + Opcodes.DMUL;
        c = i2 % 128;
        int i3 = i2 % 2;
        o.h.d dVar = d;
        int i4 = i + 33;
        c = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i;
        String str2 = str;
        int i2 = $11 + 17;
        $10 = i2 % 128;
        byte[] bArr = str2;
        if (i2 % 2 != 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i3 = 0;
        int i4 = iArr[0];
        int i5 = iArr[1];
        int i6 = iArr[2];
        int i7 = iArr[3];
        char[] cArr2 = a;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i8 = 0;
            while (i8 < length) {
                try {
                    Object[] objArr2 = new Object[1];
                    objArr2[i3] = Integer.valueOf(cArr2[i8]);
                    Object obj2 = o.e.a.s.get(1951085128);
                    if (obj2 != null) {
                        cArr = cArr2;
                        i = length;
                    } else {
                        Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getLongPressTimeout() >> 16), (char) (TextUtils.lastIndexOf("", '0', i3) + 1), TextUtils.indexOf("", "", i3) + 43);
                        byte b = (byte) 2;
                        byte b2 = (byte) (b - 2);
                        cArr = cArr2;
                        i = length;
                        Object[] objArr3 = new Object[1];
                        h(b, b2, b2, objArr3);
                        obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(1951085128, obj2);
                    }
                    cArr3[i8] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                    i8++;
                    cArr2 = cArr;
                    length = i;
                    i3 = 0;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        char[] cArr4 = new char[i5];
        System.arraycopy(cArr2, i4, cArr4, 0, i5);
        if (bArr2 != null) {
            int i9 = $11 + Opcodes.LMUL;
            $10 = i9 % 128;
            int i10 = i9 % 2;
            char[] cArr5 = new char[i5];
            lVar.d = 0;
            char c2 = 0;
            while (true) {
                switch (lVar.d < i5 ? (char) 23 : 'K') {
                    case 'K':
                        cArr4 = cArr5;
                        break;
                    default:
                        switch (bArr2[lVar.d] == 1 ? 'b' : '?') {
                            case '?':
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                    Object obj3 = o.e.a.s.get(804049217);
                                    if (obj3 == null) {
                                        Class cls2 = (Class) o.e.a.c(KeyEvent.keyCodeFromString("") + 10, (char) (Process.myTid() >> 22), (-16777009) - Color.rgb(0, 0, 0));
                                        byte b3 = (byte) 0;
                                        byte b4 = b3;
                                        Object[] objArr5 = new Object[1];
                                        h(b3, b4, b4, objArr5);
                                        obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj3);
                                    }
                                    cArr5[i11] = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                int i12 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                    Object obj4 = o.e.a.s.get(2016040108);
                                    if (obj4 == null) {
                                        Class cls3 = (Class) o.e.a.c(11 - TextUtils.getCapsMode("", 0, 0), (char) ExpandableListView.getPackedPositionGroup(0L), 448 - (ViewConfiguration.getWindowTouchSlop() >> 8));
                                        byte b5 = (byte) 3;
                                        byte b6 = (byte) (b5 - 3);
                                        Object[] objArr7 = new Object[1];
                                        h(b5, b6, b6, objArr7);
                                        obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj4);
                                    }
                                    cArr5[i12] = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                        c2 = cArr5[lVar.d];
                        try {
                            Object[] objArr8 = {lVar, lVar};
                            Object obj5 = o.e.a.s.get(-2112603350);
                            if (obj5 == null) {
                                Class cls4 = (Class) o.e.a.c(11 - Color.green(0), (char) TextUtils.indexOf("", "", 0), TextUtils.getCapsMode("", 0, 0) + 259);
                                byte b7 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                h((byte) ($$e & Opcodes.ARRAYLENGTH), b7, b7, objArr9);
                                obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr8);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
        }
        if (i7 > 0) {
            char[] cArr6 = new char[i5];
            System.arraycopy(cArr4, 0, cArr6, 0, i5);
            int i13 = i5 - i7;
            System.arraycopy(cArr6, 0, cArr4, i13, i7);
            System.arraycopy(cArr6, i7, cArr4, 0, i13);
        }
        switch (z ? 'Z' : '5') {
            case Opcodes.SALOAD /* 53 */:
                break;
            default:
                char[] cArr7 = new char[i5];
                int i14 = 0;
                while (true) {
                    lVar.d = i14;
                    if (lVar.d >= i5) {
                        cArr4 = cArr7;
                        break;
                    } else {
                        cArr7[lVar.d] = cArr4[(i5 - lVar.d) - 1];
                        i14 = lVar.d + 1;
                    }
                }
        }
        if (i6 > 0) {
            int i15 = $10 + 15;
            $11 = i15 % 128;
            int i16 = i15 % 2;
            int i17 = 0;
            while (true) {
                lVar.d = i17;
                if (lVar.d < i5) {
                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                    i17 = lVar.d + 1;
                }
            }
        }
        String str3 = new String(cArr4);
        int i18 = $10 + 31;
        $11 = i18 % 128;
        int i19 = i18 % 2;
        objArr[0] = str3;
    }
}
