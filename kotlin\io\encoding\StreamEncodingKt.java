package kotlin.io.encoding;

import kotlin.Metadata;

/* compiled from: Base64IOStream.kt */
@Metadata(d1 = {"kotlin/io/encoding/StreamEncodingKt__Base64IOStreamKt"}, k = 4, mv = {1, 9, 0}, xi = 49)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\io\encoding\StreamEncodingKt.smali */
public final class StreamEncodingKt extends StreamEncodingKt__Base64IOStreamKt {
    private StreamEncodingKt() {
    }
}
