package o.ba;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ba\a.smali */
public final class a extends e {
    private byte[] b;
    private byte[] e;
    private static int c = 0;
    private static int a = 1;

    public final byte[] c() {
        int i = c;
        int i2 = (i & 63) + (i | 63);
        int i3 = i2 % 128;
        a = i3;
        int i4 = i2 % 2;
        byte[] bArr = this.e;
        int i5 = (i3 ^ 59) + ((i3 & 59) << 1);
        c = i5 % 128;
        int i6 = i5 % 2;
        return bArr;
    }

    public final void c(byte[] bArr) {
        int i = c;
        int i2 = (i & 11) + (i | 11);
        a = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.e = bArr;
        switch (z) {
            case false:
                throw null;
            default:
                return;
        }
    }

    public final byte[] e() {
        int i = a + 41;
        c = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final void b(byte[] bArr) {
        int i = c;
        int i2 = (i & Opcodes.LSUB) + (i | Opcodes.LSUB);
        int i3 = i2 % 128;
        a = i3;
        boolean z = i2 % 2 != 0;
        Object obj = null;
        this.b = bArr;
        switch (z) {
            case false:
                obj.hashCode();
                throw null;
            default:
                int i4 = (i3 & 63) + (i3 | 63);
                c = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }
}
