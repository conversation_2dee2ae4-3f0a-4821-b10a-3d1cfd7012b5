package o.bo;

import android.graphics.ImageFormat;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Map;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.n;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\i.smali */
public final class i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long d;
    private static int e;
    private final Object a;
    private final Map<String, String> c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        d = 4323934677015124256L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bo.i.$$a
            int r8 = r8 + 5
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r7 = r7 * 3
            int r7 = r7 + 68
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2e
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            int r8 = r8 + 1
            r1[r3] = r4
            if (r3 != r6) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r5
        L2e:
            int r6 = r6 + r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.i.g(short, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
        $$b = 245;
    }

    public i(Map<String, String> map, Object obj) {
        this.c = map;
        this.a = obj;
    }

    public final Object e() {
        int i = b;
        int i2 = i + Opcodes.DNEG;
        e = i2 % 128;
        int i3 = i2 % 2;
        Object obj = this.a;
        int i4 = i + 87;
        e = i4 % 128;
        int i5 = i4 % 2;
        return obj;
    }

    public final String a() {
        Map<String, String> map;
        Object obj;
        int i = b + Opcodes.DSUB;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                map = this.c;
                Object[] objArr = new Object[1];
                f("䗰殤겡ﴄ䖀\uf23a鼦ゕ≣樾㜿좸試Ȱ꼥悈牴먣윺\uf89a\uda6e", -ImageFormat.getBitsPerPixel(1), objArr);
                obj = objArr[0];
                break;
            default:
                map = this.c;
                Object[] objArr2 = new Object[1];
                f("䗰殤겡ﴄ䖀\uf23a鼦ゕ≣樾㜿좸試Ȱ꼥悈牴먣윺\uf89a\uda6e", -ImageFormat.getBitsPerPixel(0), objArr2);
                obj = objArr2[0];
                break;
        }
        String e2 = o.e((CharSequence) map.get(((String) obj).intern()));
        int i2 = b + 47;
        e = i2 % 128;
        switch (i2 % 2 != 0 ? 'R' : '.') {
            case '.':
                return e2;
            default:
                int i3 = 35 / 0;
                return e2;
        }
    }

    public final String d() {
        int i = e + Opcodes.DSUB;
        b = i % 128;
        int i2 = i % 2;
        Map<String, String> map = this.c;
        Object[] objArr = new Object[1];
        f("靸膴홷䗡霈ᠪ\ue5f0衰\uf0eb耮䷩", -TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
        String e2 = o.e((CharSequence) map.get(((String) objArr[0]).intern()));
        int i3 = e + 41;
        b = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    public final String c() {
        int i = b + Opcodes.LSHR;
        e = i % 128;
        int i2 = i % 2;
        Map<String, String> map = this.c;
        Object[] objArr = new Object[1];
        f("遆鄔杇ꮅ逵\u088e哊昋\uf7d3邀ﳓ鸵忚", TextUtils.indexOf("", "") + 1, objArr);
        String e2 = o.e((CharSequence) map.get(((String) objArr[0]).intern()));
        int i3 = b + 13;
        e = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return e2;
            default:
                int i4 = 32 / 0;
                return e2;
        }
    }

    private static void f(String str, int i, Object[] objArr) {
        char[] charArray;
        int i2 = $10 + Opcodes.DNEG;
        int i3 = i2 % 128;
        $11 = i3;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                switch (str != null ? '7' : (char) 6) {
                    case 6:
                        charArray = str;
                        break;
                    default:
                        int i4 = i3 + 99;
                        $10 = i4 % 128;
                        int i5 = i4 % 2;
                        charArray = str.toCharArray();
                        break;
                }
                n nVar = new n();
                char[] b2 = n.b(d ^ 8632603938177761503L, charArray, i);
                int i6 = 4;
                nVar.c = 4;
                while (nVar.c < b2.length) {
                    nVar.e = nVar.c - i6;
                    int i7 = nVar.c;
                    try {
                        Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % i6]), Long.valueOf(nVar.e), Long.valueOf(d)};
                        Object obj = o.e.a.s.get(-1945790373);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(11 - TextUtils.getOffsetBefore("", 0), (char) TextUtils.getTrimmedLength(""), 43 - Gravity.getAbsoluteGravity(0, 0));
                            byte b3 = $$a[0];
                            byte b4 = b3;
                            Object[] objArr3 = new Object[1];
                            g(b3, b4, (byte) (b4 - 1), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                            o.e.a.s.put(-1945790373, obj);
                        }
                        b2[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        try {
                            Object[] objArr4 = {nVar, nVar};
                            Object obj2 = o.e.a.s.get(-341518981);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(TextUtils.getTrimmedLength("") + 10, (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 249 - (ViewConfiguration.getKeyRepeatTimeout() >> 16));
                                byte b5 = $$a[0];
                                byte b6 = (byte) (b5 + 1);
                                Object[] objArr5 = new Object[1];
                                g(b5, b6, (byte) (-b6), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                o.e.a.s.put(-341518981, obj2);
                            }
                            ((Method) obj2).invoke(null, objArr4);
                            int i8 = $11 + 9;
                            $10 = i8 % 128;
                            int i9 = i8 % 2;
                            i6 = 4;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(b2, 4, b2.length - 4);
                return;
        }
    }
}
