package o.et;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;
import fr.antelop.sdk.card.emvapplication.HceEmvApplication;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.List;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\c.smali */
public abstract class c extends o.el.d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] k;
    private static char m;

    /* renamed from: o, reason: collision with root package name */
    private static int f79o;
    private static int q;
    private final int a;
    private final String b;
    private o.dp.b c;
    private final String d;
    private short e;
    private boolean f;
    private final HashMap<Integer, byte[]> g;
    private byte[] h;
    private byte[] i;
    private byte[] j;
    private String l;
    private byte[] n;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f79o = 0;
        q = 1;
        F();
        ViewConfiguration.getDoubleTapTimeout();
        ViewConfiguration.getFadingEdgeLength();
        Color.green(0);
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getMaximumDrawingCacheSize();
        TextUtils.lastIndexOf("", '0');
        int i = f79o + 35;
        q = i % 128;
        switch (i % 2 != 0) {
            case true:
                break;
            default:
                int i2 = 51 / 0;
                break;
        }
    }

    static void F() {
        k = new char[]{30498, 30544, 30587, 30586, 30585, 30572, 30511, 30566, 30542, 30529, 30531, 30563, 30508, 30570, 30591, 30510, 30506, 30574, 30588, 30507, 30562, 30589, 30568, 30560, 30555, 30557, 30573, 30504, 30571, 30509, 30540, 30538, 30583, 30517, 30505, 30561};
        m = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void T(byte r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r0 = o.et.c.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r9 = r9 + 69
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r9 = r9 + 1
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.et.c.T(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{12, 95, -121};
        $$b = 91;
    }

    public abstract byte[] E();

    protected abstract c c(String str, String str2, int i, String str3);

    public abstract EmvApplicationType e();

    @Override // o.ee.d
    public final /* synthetic */ EmvApplication a() {
        int i = q + 17;
        f79o = i % 128;
        int i2 = i % 2;
        EmvApplication c = c();
        int i3 = q + Opcodes.DMUL;
        f79o = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 1 / 0;
                return c;
            default:
                return c;
        }
    }

    public c(String str, o.dp.b bVar, String str2, int i, String str3) {
        super(str);
        this.g = new HashMap<>();
        this.c = bVar;
        this.b = str2;
        this.a = i;
        this.d = str3;
    }

    @Override // o.el.d
    public final o.el.d b(String str) {
        int i = f79o + 61;
        q = i % 128;
        int i2 = i % 2;
        c c = c(str, this.b, this.a, this.d);
        int i3 = q + 9;
        f79o = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    @Override // o.el.d
    public final o.ei.a z() {
        int i = q + 15;
        f79o = i % 128;
        int i2 = i % 2;
        o.ei.a aVar = o.ei.a.d;
        int i3 = q + 31;
        f79o = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return aVar;
            default:
                int i4 = 82 / 0;
                return aVar;
        }
    }

    @Override // o.el.d
    public final String r() {
        int i = q + 19;
        f79o = i % 128;
        switch (i % 2 == 0) {
            case false:
                b().e();
                throw null;
            default:
                return b().e();
        }
    }

    private o.dp.b b() {
        int i = f79o;
        int i2 = i + 21;
        q = i2 % 128;
        int i3 = i2 % 2;
        o.dp.b bVar = this.c;
        int i4 = i + 89;
        q = i4 % 128;
        switch (i4 % 2 == 0 ? ';' : 'Z') {
            case ';':
                int i5 = 2 / 0;
                return bVar;
            default:
                return bVar;
        }
    }

    protected final void b(o.dp.b bVar) {
        int i = f79o;
        int i2 = i + 61;
        q = i2 % 128;
        int i3 = i2 % 2;
        this.c = bVar;
        int i4 = i + Opcodes.LUSHR;
        q = i4 % 128;
        int i5 = i4 % 2;
    }

    public static String d(String str) {
        int i = q + 25;
        f79o = i % 128;
        int i2 = i % 2;
        String c = c(str, 1);
        int i3 = q + 5;
        f79o = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    public static String c(String str, int i) {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        R(11 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), "\t\u000b\u0019\u001e\u001b\u0007\u0006\u001a\u0001\u0007", (byte) (TextUtils.indexOf((CharSequence) "", '0') + 22), objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(str).toString();
        if (i >= 2) {
            return obj;
        }
        StringBuilder append = new StringBuilder().append(obj);
        Object[] objArr2 = new Object[1];
        R(1 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "㘁", (byte) (60 - TextUtils.indexOf("", "", 0)), objArr2);
        String obj2 = append.append(((String) objArr2[0]).intern()).append(i).toString();
        int i2 = q + 59;
        f79o = i2 % 128;
        int i3 = i2 % 2;
        return obj2;
    }

    public static String e(String str) {
        int i = f79o + 45;
        q = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        R(9 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\t\u000b\u0019\u001e\u001b\u0007\u0006\u001a\u0001\u0007", (byte) (21 - TextUtils.getOffsetAfter("", 0)), objArr);
        String replace = str.replace(((String) objArr[0]).intern(), "");
        Object[] objArr2 = new Object[1];
        R(1 - Color.green(0), "㘁", (byte) (TextUtils.indexOf("", "", 0) + 60), objArr2);
        String str2 = replace.split(((String) objArr2[0]).intern())[0];
        int i3 = f79o + 21;
        q = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return str2;
        }
    }

    public final String g() {
        int i = q + 85;
        f79o = i % 128;
        int i2 = i % 2;
        switch (k() ? (char) 22 : 'R') {
            case 22:
                int i3 = q + 95;
                f79o = i3 % 128;
                int i4 = i3 % 2;
                String n = n();
                Object[] objArr = new Object[1];
                R((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 10, "\t\u000b\u0019\u001e\u001b\u0007\u0006\u001a\u0001\u0007", (byte) (View.combineMeasuredStates(0, 0) + 21), objArr);
                String replace = n.replace(((String) objArr[0]).intern(), "");
                Object[] objArr2 = new Object[1];
                R((ViewConfiguration.getFadingEdgeLength() >> 16) + 1, "㘁", (byte) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 61), objArr2);
                return replace.split(((String) objArr2[0]).intern())[0];
            default:
                return n();
        }
    }

    private void e(int i, int i2, byte[] bArr) {
        int i3 = q + 87;
        f79o = i3 % 128;
        int i4 = i3 % 2;
        this.g.put(Integer.valueOf((i * 100) + i2), bArr);
        int i5 = f79o + 37;
        q = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final void c(o.cc.a aVar) {
        int i = f79o + 59;
        q = i % 128;
        switch (i % 2 != 0) {
            case true:
                e(aVar.c(), aVar.e(), aVar.d());
                return;
            default:
                e(aVar.c(), aVar.e(), aVar.d());
                throw null;
        }
    }

    public final void a(List<o.cc.a> list) {
        int i = f79o + 67;
        q = i % 128;
        switch (i % 2 == 0 ? 'I' : (char) 4) {
            case 'I':
                list.iterator();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i2 = f79o + 27;
                q = i2 % 128;
                /*  JADX ERROR: Method code generation error
                    java.lang.ClassCastException: class jadx.core.dex.nodes.InsnNode cannot be cast to class jadx.core.dex.instructions.SwitchInsn (jadx.core.dex.nodes.InsnNode and jadx.core.dex.instructions.SwitchInsn are in unnamed module of loader 'app')
                    	at jadx.core.codegen.RegionGen.makeSwitch(RegionGen.java:245)
                    	at jadx.core.dex.regions.SwitchRegion.generate(SwitchRegion.java:84)
                    	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
                    	at jadx.core.dex.regions.Region.generate(Region.java:35)
                    	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
                    	at jadx.core.codegen.RegionGen.makeRegionIndent(RegionGen.java:83)
                    	at jadx.core.codegen.RegionGen.makeSwitch(RegionGen.java:267)
                    	at jadx.core.dex.regions.SwitchRegion.generate(SwitchRegion.java:84)
                    	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
                    	at jadx.core.dex.regions.Region.generate(Region.java:35)
                    	at jadx.core.codegen.RegionGen.makeRegion(RegionGen.java:66)
                    	at jadx.core.codegen.MethodGen.addRegionInsns(MethodGen.java:297)
                    	at jadx.core.codegen.MethodGen.addInstructions(MethodGen.java:276)
                    	at jadx.core.codegen.ClassGen.addMethodCode(ClassGen.java:406)
                    	at jadx.core.codegen.ClassGen.addMethod(ClassGen.java:335)
                    	at jadx.core.codegen.ClassGen.lambda$addInnerClsAndMethods$3(ClassGen.java:301)
                    	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
                    	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
                    	at java.base/java.util.stream.SortedOps$RefSortingSink.end(SortedOps.java:395)
                    	at java.base/java.util.stream.Sink$ChainedReference.end(Sink.java:258)
                    */
                /*
                    this = this;
                    int r0 = o.et.c.f79o
                    int r0 = r0 + 67
                    int r1 = r0 % 128
                    o.et.c.q = r1
                    r1 = 2
                    int r0 = r0 % r1
                    if (r0 != 0) goto Lf
                    r0 = 73
                    goto L10
                Lf:
                    r0 = 4
                L10:
                    switch(r0) {
                        case 73: goto L18;
                        default: goto L13;
                    }
                L13:
                    java.util.Iterator r5 = r5.iterator()
                    goto L22
                L18:
                    r5.iterator()
                    r5 = 0
                    r5.hashCode()     // Catch: java.lang.Throwable -> L20
                    throw r5     // Catch: java.lang.Throwable -> L20
                L20:
                    r5 = move-exception
                    throw r5
                L22:
                    int r0 = o.et.c.f79o
                    int r0 = r0 + 27
                    int r2 = r0 % 128
                    o.et.c.q = r2
                    int r0 = r0 % r1
                    if (r0 != 0) goto L2f
                    r0 = r1
                    goto L31
                L2f:
                    r0 = 69
                L31:
                    switch(r0) {
                        case 2: goto L34;
                        default: goto L35;
                    }
                L34:
                L35:
                    boolean r0 = r5.hasNext()
                    if (r0 == 0) goto L51
                    java.lang.Object r0 = r5.next()
                    o.cc.a r0 = (o.cc.a) r0
                    int r2 = r0.c()
                    int r3 = r0.e()
                    byte[] r0 = r0.d()
                    r4.e(r2, r3, r0)
                    goto L34
                L51:
                    int r5 = o.et.c.f79o
                    int r5 = r5 + 15
                    int r0 = r5 % 128
                    o.et.c.q = r0
                    int r5 = r5 % r1
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.et.c.a(java.util.List):void");
            }

            public final String f() {
                int i = q + 53;
                int i2 = i % 128;
                f79o = i2;
                int i3 = i % 2;
                String str = this.b;
                int i4 = i2 + 31;
                q = i4 % 128;
                int i5 = i4 % 2;
                return str;
            }

            public final byte[] j() {
                int i = q + Opcodes.DSUB;
                int i2 = i % 128;
                f79o = i2;
                switch (i % 2 != 0 ? (char) 31 : 'I') {
                    case 31:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        byte[] bArr = this.i;
                        int i3 = i2 + Opcodes.LSHL;
                        q = i3 % 128;
                        int i4 = i3 % 2;
                        return bArr;
                }
            }

            public final String h() {
                int i = q + 31;
                f79o = i % 128;
                int i2 = i % 2;
                String e = o.dk.b.e(this.i);
                int i3 = f79o + 11;
                q = i3 % 128;
                switch (i3 % 2 == 0 ? 'C' : 'R') {
                    case Opcodes.DASTORE /* 82 */:
                        return e;
                    default:
                        throw null;
                }
            }

            public final void a(byte[] bArr) {
                int i = q;
                int i2 = i + Opcodes.LSHR;
                f79o = i2 % 128;
                int i3 = i2 % 2;
                this.i = bArr;
                int i4 = i + 17;
                f79o = i4 % 128;
                int i5 = i4 % 2;
            }

            public final int i() {
                int i = q;
                int i2 = i + 65;
                f79o = i2 % 128;
                int i3 = i2 % 2;
                int i4 = this.a;
                int i5 = i + Opcodes.LSHL;
                f79o = i5 % 128;
                int i6 = i5 % 2;
                return i4;
            }

            public final String l() {
                int i = f79o + 13;
                q = i % 128;
                switch (i % 2 == 0 ? 'I' : '(') {
                    case 'I':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return this.d;
                }
            }

            public final short m() {
                int i = f79o + 25;
                int i2 = i % 128;
                q = i2;
                switch (i % 2 == 0 ? Typography.less : ';') {
                    case '<':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        short s = this.e;
                        int i3 = i2 + Opcodes.DMUL;
                        f79o = i3 % 128;
                        int i4 = i3 % 2;
                        return s;
                }
            }

            public final void e(short s) {
                int i = f79o + Opcodes.LREM;
                int i2 = i % 128;
                q = i2;
                boolean z = i % 2 == 0;
                this.e = s;
                switch (z) {
                    case false:
                        int i3 = i2 + Opcodes.DNEG;
                        f79o = i3 % 128;
                        switch (i3 % 2 == 0) {
                            case true:
                                return;
                            default:
                                int i4 = 98 / 0;
                                return;
                        }
                    default:
                        throw null;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:18:0x001f, code lost:
            
                if (r5.f == false) goto L21;
             */
            @Override // o.el.d
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final boolean o() {
                /*
                    r5 = this;
                    int r0 = o.et.c.q
                    int r1 = r0 + 19
                    int r2 = r1 % 128
                    o.et.c.f79o = r2
                    int r1 = r1 % 2
                    if (r1 == 0) goto Lf
                    r1 = 13
                    goto L11
                Lf:
                    r1 = 70
                L11:
                    r2 = 1
                    r3 = 0
                    switch(r1) {
                        case 13: goto L1b;
                        default: goto L16;
                    }
                L16:
                    boolean r1 = r5.f
                    if (r1 != 0) goto L26
                    goto L24
                L1b:
                    boolean r1 = r5.f
                    r4 = 6
                    int r4 = r4 / r3
                    if (r1 != 0) goto L34
                L21:
                    goto L2b
                L22:
                    r0 = move-exception
                    throw r0
                L24:
                    r1 = r2
                    goto L27
                L26:
                    r1 = r3
                L27:
                    switch(r1) {
                        case 1: goto L21;
                        default: goto L2a;
                    }
                L2a:
                    goto L34
                L2b:
                    int r0 = r0 + 95
                    int r1 = r0 % 128
                    o.et.c.f79o = r1
                    int r0 = r0 % 2
                    return r2
                L34:
                    return r3
                */
                throw new UnsupportedOperationException("Method not decompiled: o.et.c.o():boolean");
            }

            public final boolean k() {
                int i = f79o + 65;
                q = i % 128;
                switch (i % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return this.f;
                }
            }

            public final void b(boolean z) {
                int i = f79o;
                int i2 = i + 11;
                q = i2 % 128;
                char c = i2 % 2 == 0 ? '5' : '3';
                this.f = z;
                switch (c) {
                    case '3':
                        int i3 = i + 109;
                        q = i3 % 128;
                        switch (i3 % 2 == 0 ? (char) 17 : '\f') {
                            case 17:
                                int i4 = 5 / 0;
                                return;
                            default:
                                return;
                        }
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            public final byte[] A() {
                int i = f79o + 3;
                q = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                R(14 - TextUtils.getOffsetAfter("", 0), " \u0013\u0002\n㘥㘥\u0006\b\u000b\u0017\u0001\b\u001d\u0005", (byte) (TextUtils.indexOf((CharSequence) "", '0') + 60), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                R((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 30, "\u0013\u0010\u0003\b\u000e\u001f\b\u000e\u0003\u0000\f\u0006\t\u0012\f\u0013\u001d\u0011\u0001\n\"\u0017\u000b\u001e\u000e\u001f\u0000\b\u000e\u0005㙗", (byte) (94 - (KeyEvent.getMaxKeyCode() >> 16)), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                o.fc.e b = o.ei.c.c().a().f().b(this);
                switch (b == null ? '@' : '9') {
                    case '@':
                        return null;
                    default:
                        ByteBuffer allocate = ByteBuffer.allocate(4);
                        allocate.putInt(b.h());
                        byte[] array = allocate.array();
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        R(14 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), " \u0013\u0002\n㘥㘥\u0006\b\u000b\u0017\u0001\b\u001d\u0005", (byte) (59 - ExpandableListView.getPackedPositionGroup(0L)), objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr4 = new Object[1];
                        R(25 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "\u0013\u0010\u0003\b\u000e\u001f\b\u000e\u0003\u0000\t\u001e\u000b\u001e\u000e\u001f\u0000\b\u000e\u0005\u0000\u000b\u001e\t", (byte) (107 - ExpandableListView.getPackedPositionChild(0L)), objArr4);
                        o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(o.dk.b.e(new byte[]{array[2], array[3]})).toString());
                        byte[] bArr = {array[2], array[3]};
                        int i3 = f79o + 47;
                        q = i3 % 128;
                        int i4 = i3 % 2;
                        return bArr;
                }
            }

            public final short D() {
                int i = q + 45;
                f79o = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                R(13 - TextUtils.indexOf((CharSequence) "", '0'), " \u0013\u0002\n㘥㘥\u0006\b\u000b\u0017\u0001\b\u001d\u0005", (byte) (59 - Gravity.getAbsoluteGravity(0, 0)), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                R(29 - Color.green(0), "\u0013\u0010\b\u000e\u0005\u0010\b\u0006\u000e\u001d\u0007\u0011!\u0012\u0010\u0019\u0011\u001f\u0001\b\u0017\u0011\u0015\u0006\u0002\u0015\u0019\u000e㙠", (byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.DNEG), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                short a = o.ei.c.c().a().f().a(this);
                int i3 = f79o + 23;
                q = i3 % 128;
                switch (i3 % 2 == 0 ? 'Y' : 'U') {
                    case Opcodes.DUP /* 89 */:
                        int i4 = 61 / 0;
                        return a;
                    default:
                        return a;
                }
            }

            public final byte[] B() {
                int i = f79o;
                int i2 = i + 31;
                q = i2 % 128;
                switch (i2 % 2 == 0 ? '8' : '*') {
                    case '*':
                        byte[] bArr = this.h;
                        int i3 = i + Opcodes.LUSHR;
                        q = i3 % 128;
                        int i4 = i3 % 2;
                        return bArr;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            public final void b(byte[] bArr) {
                int i = q + 5;
                int i2 = i % 128;
                f79o = i2;
                int i3 = i % 2;
                this.h = bArr;
                int i4 = i2 + 21;
                q = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            public final byte[] C() {
                int i = q + 35;
                f79o = i % 128;
                switch (i % 2 != 0) {
                    case false:
                        return this.j;
                    default:
                        int i2 = 30 / 0;
                        return this.j;
                }
            }

            public final void e(byte[] bArr) {
                int i = q;
                int i2 = i + Opcodes.DREM;
                f79o = i2 % 128;
                int i3 = i2 % 2;
                this.j = bArr;
                int i4 = i + 73;
                f79o = i4 % 128;
                int i5 = i4 % 2;
            }

            public final String I() {
                int i = f79o + 109;
                int i2 = i % 128;
                q = i2;
                int i3 = i % 2;
                String str = this.l;
                int i4 = i2 + 51;
                f79o = i4 % 128;
                int i5 = i4 % 2;
                return str;
            }

            public final void g(String str) {
                int i = q;
                int i2 = i + 57;
                f79o = i2 % 128;
                int i3 = i2 % 2;
                this.l = str;
                int i4 = i + 61;
                f79o = i4 % 128;
                switch (i4 % 2 != 0 ? 'c' : '?') {
                    case Opcodes.DADD /* 99 */:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            public final byte[] H() {
                int i = f79o;
                int i2 = i + 35;
                q = i2 % 128;
                int i3 = i2 % 2;
                byte[] bArr = this.n;
                int i4 = i + 19;
                q = i4 % 128;
                int i5 = i4 % 2;
                return bArr;
            }

            public final void d(byte[] bArr) {
                int i = f79o + 75;
                q = i % 128;
                boolean z = i % 2 != 0;
                this.n = bArr;
                switch (z) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            private EmvApplication c() {
                HceEmvApplication hceEmvApplication = new HceEmvApplication(this);
                int i = q + 25;
                f79o = i % 128;
                int i2 = i % 2;
                return hceEmvApplication;
            }

            /* JADX WARN: Code restructure failed: missing block: B:45:0x016a, code lost:
            
                if (r3.e == r3.a) goto L51;
             */
            /* JADX WARN: Code restructure failed: missing block: B:48:0x01bc, code lost:
            
                r11 = new java.lang.Object[]{r3, r3, java.lang.Integer.valueOf(r1), r3, r3, java.lang.Integer.valueOf(r1), r3, r3, java.lang.Integer.valueOf(r1), r3, r3, java.lang.Integer.valueOf(r1), r3};
                r10 = o.e.a.s.get(696901393);
             */
            /* JADX WARN: Code restructure failed: missing block: B:49:0x0208, code lost:
            
                if (r10 == null) goto L60;
             */
            /* JADX WARN: Code restructure failed: missing block: B:53:0x0292, code lost:
            
                if (((java.lang.Integer) ((java.lang.reflect.Method) r10).invoke(null, r11)).intValue() != r3.h) goto L65;
             */
            /* JADX WARN: Code restructure failed: missing block: B:54:0x0294, code lost:
            
                r7 = 15;
             */
            /* JADX WARN: Code restructure failed: missing block: B:55:0x0299, code lost:
            
                switch(r7) {
                    case 8: goto L68;
                    default: goto L72;
                };
             */
            /* JADX WARN: Code restructure failed: missing block: B:57:0x02a3, code lost:
            
                if (r3.c != r3.d) goto L71;
             */
            /* JADX WARN: Code restructure failed: missing block: B:58:0x02a5, code lost:
            
                r3.i = ((r3.i + r1) - 1) % r1;
                r3.h = ((r3.h + r1) - 1) % r1;
                r7 = (r3.c * r1) + r3.i;
                r10 = (r3.d * r1) + r3.h;
                r4[r3.b] = r5[r7];
                r4[r3.b + 1] = r5[r10];
             */
            /* JADX WARN: Code restructure failed: missing block: B:61:0x02cf, code lost:
            
                r7 = (r3.c * r1) + r3.h;
                r10 = (r3.d * r1) + r3.i;
                r4[r3.b] = r5[r7];
                r4[r3.b + 1] = r5[r10];
             */
            /* JADX WARN: Code restructure failed: missing block: B:65:0x02ed, code lost:
            
                r10 = new java.lang.Object[]{r3, r3, java.lang.Integer.valueOf(r1), java.lang.Integer.valueOf(r1), r3, r3, java.lang.Integer.valueOf(r1), java.lang.Integer.valueOf(r1), r3, java.lang.Integer.valueOf(r1), r3};
                r7 = o.e.a.s.get(1075449051);
             */
            /* JADX WARN: Code restructure failed: missing block: B:66:0x032c, code lost:
            
                if (r7 == null) goto L76;
             */
            /* JADX WARN: Code restructure failed: missing block: B:68:0x03a3, code lost:
            
                r7 = ((java.lang.Integer) ((java.lang.reflect.Method) r7).invoke(null, r10)).intValue();
             */
            /* JADX WARN: Code restructure failed: missing block: B:69:0x03b0, code lost:
            
                r10 = (r3.d * r1) + r3.h;
                r4[r3.b] = r5[r7];
                r4[r3.b + 1] = r5[r10];
             */
            /* JADX WARN: Code restructure failed: missing block: B:71:0x032f, code lost:
            
                r7 = (java.lang.Class) o.e.a.c(11 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16), (char) (android.view.ViewConfiguration.getLongPressTimeout() >> 16), (android.view.ViewConfiguration.getLongPressTimeout() >> 16) + 65);
                r11 = (byte) 0;
                r12 = r11;
                r14 = new java.lang.Object[1];
                T(r11, r12, (byte) (r12 + 1), r14);
                r7 = r7.getMethod((java.lang.String) r14[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
                o.e.a.s.put(1075449051, r7);
             */
            /* JADX WARN: Code restructure failed: missing block: B:73:0x03cb, code lost:
            
                r0 = move-exception;
             */
            /* JADX WARN: Code restructure failed: missing block: B:74:0x03cc, code lost:
            
                r1 = r0.getCause();
             */
            /* JADX WARN: Code restructure failed: missing block: B:75:0x03d0, code lost:
            
                if (r1 != null) goto L83;
             */
            /* JADX WARN: Code restructure failed: missing block: B:76:0x03d2, code lost:
            
                throw r1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:77:0x03d3, code lost:
            
                throw r0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:78:0x0297, code lost:
            
                r7 = '\b';
             */
            /* JADX WARN: Code restructure failed: missing block: B:79:0x020b, code lost:
            
                r10 = (java.lang.Class) o.e.a.c(android.view.KeyEvent.normalizeMetaState(0) + 10, (char) ((android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)) + 8855), 372 - android.text.AndroidCharacter.getMirror('0'));
                r12 = (byte) 0;
                r13 = r12;
                r15 = new java.lang.Object[1];
                T(r12, r13, r13, r15);
                r10 = r10.getMethod((java.lang.String) r15[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
                o.e.a.s.put(696901393, r10);
             */
            /* JADX WARN: Code restructure failed: missing block: B:81:0x03d4, code lost:
            
                r0 = move-exception;
             */
            /* JADX WARN: Code restructure failed: missing block: B:82:0x03d5, code lost:
            
                r1 = r0.getCause();
             */
            /* JADX WARN: Code restructure failed: missing block: B:83:0x03d9, code lost:
            
                if (r1 != null) goto L88;
             */
            /* JADX WARN: Code restructure failed: missing block: B:84:0x03db, code lost:
            
                throw r1;
             */
            /* JADX WARN: Code restructure failed: missing block: B:85:0x03dc, code lost:
            
                throw r0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:86:0x0180, code lost:
            
                r7 = o.et.c.$10 + 75;
                o.et.c.$11 = r7 % 128;
             */
            /* JADX WARN: Code restructure failed: missing block: B:87:0x018a, code lost:
            
                if ((r7 % 2) != 0) goto L54;
             */
            /* JADX WARN: Code restructure failed: missing block: B:88:0x018c, code lost:
            
                r4[r3.b] = (char) (r3.e >>> r30);
                r4[r3.b >>> 1] = (char) (r3.a >>> r30);
             */
            /* JADX WARN: Code restructure failed: missing block: B:91:0x01a0, code lost:
            
                r4[r3.b] = (char) (r3.e - r30);
                r4[r3.b + 1] = (char) (r3.a - r30);
             */
            /* JADX WARN: Code restructure failed: missing block: B:93:0x017e, code lost:
            
                if (r3.e == r3.a) goto L51;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void R(int r28, java.lang.String r29, byte r30, java.lang.Object[] r31) {
                /*
                    Method dump skipped, instructions count: 1038
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.et.c.R(int, java.lang.String, byte, java.lang.Object[]):void");
            }
        }
