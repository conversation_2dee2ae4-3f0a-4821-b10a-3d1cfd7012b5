package com.google.android.gms.internal.auth;

import java.io.IOException;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzgh.smali */
interface zzgh<T> {
    int zza(Object obj);

    Object zzd();

    void zze(Object obj);

    void zzf(Object obj, Object obj2);

    void zzg(Object obj, byte[] bArr, int i, int i2, zzds zzdsVar) throws IOException;

    boolean zzh(Object obj, Object obj2);

    boolean zzi(Object obj);
}
