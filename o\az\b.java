package o.az;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.PointerIconCompat;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.Collection;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.az.j;
import o.bl.b;
import o.de.f;
import o.i.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\b.smali */
public final class b extends o.y.b<c> implements o.az.c, j.c {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int p;
    private static char[] q;
    private static int r;
    private static long t;
    o.c.a a;
    o.h.d b;
    boolean c;
    boolean d;
    String e;
    String f;
    o.ad.c g;
    boolean h;
    o.eg.b i;
    final Object j;
    private Collection<g> k;
    private boolean l;
    private o.az.e m;
    private final f n;

    /* renamed from: o, reason: collision with root package name */
    private boolean f36o;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\b$c.smali */
    public interface c {
        void a();

        void b(o.bb.d dVar, o.bv.g gVar);

        void c();

        void c(o.bb.d dVar);

        void e(boolean z, o.j.b bVar, o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        p = 0;
        r = 1;
        k();
        ImageFormat.getBitsPerPixel(0);
        Color.red(0);
        TextUtils.lastIndexOf("", '0', 0);
        ExpandableListView.getPackedPositionForGroup(0);
        ViewConfiguration.getGlobalActionKeyTimeout();
        MotionEvent.axisFromString("");
        KeyEvent.getModifierMetaStateMask();
        ExpandableListView.getPackedPositionGroup(0L);
        Process.getThreadPriority(0);
        SystemClock.uptimeMillis();
        View.MeasureSpec.getSize(0);
        ViewConfiguration.getScrollFriction();
        TextUtils.lastIndexOf("", '0');
        TypedValue.complexToFloat(0);
        ViewConfiguration.getEdgeSlop();
        View.MeasureSpec.getSize(0);
        View.resolveSizeAndState(0, 0, 0);
        ViewConfiguration.getMinimumFlingVelocity();
        TextUtils.lastIndexOf("", '0', 0, 0);
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        TextUtils.getOffsetAfter("", 0);
        int i = p + 97;
        r = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{2, Base64.padSymbol, -41, 17};
        $$e = Opcodes.DREM;
    }

    static void k() {
        char[] cArr = new char[3043];
        ByteBuffer.wrap(",\u0088>3\t\u008f\u001bkfÛpUC+®\u008f¸n\u008bÆ\u0095pà5ó\u0088Ýa(ö:P\u0005=,«>%\t\u0092\u001bnfÉpuC,®\u009f¸o\u008bË\u0095gà;ó\u0096Ýg(·:\u0013\u0005y\u0010\u0081bhMæ_Tª2µ\u008e\u0087\u0002\u0092øüWÏ-ÚÊ$\u00147þ\u0001Hl<\u007f\u009dI\u0015Tÿ¦\u0002±\"\u009c¦î_ùâËNÖ&!¶3Z\u001e¨h\f{ÖF¼P\u0016£ò\u008d_\u0098Ûë³õ\u0003\u00135\u0001¡6\u0010$ùYZOÂ|©\u0091\u001b\u0087ú´Dª\u008bßïÌ]âõ\u0017l\u0005Ò:¨/\u000e]òr~`Ì\u0095³\u008a\u0019¸Î\u00adhÃÌð©å\u0017\u001b\u008c\b`>ÆSö@\u0010v\u008akl\u0099È\u008e¡£)Ñ\u0083Æ>ôÖé¢\u001ek\f\u0086!rWÚDJyfoÌ\u009c8²Ú§MÔ+Ê\u0093ÿeíç\u0002G7'ØGÊÓýbï\u008b\u0092(\u0084°·ÛZiL\u0088\u007f6aù\u0014\u009d\u0007/)\u0087Ü\u001fÎ»ñÁä~\u0096\u0088¹\u0016«°^\u008eAdsÿf\u000f\b»;ß.aÐëÃ\u001fõ¢\u0098Ê,º>8\t\u0094\u001bpfÙpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:q\u00057\u0010§bdMû_Sª#,¥>?\t\u0095\u001befìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Ø:P\u0005\u001e\u0010\u008fbbMü_Z\u0094Æ\u0086^±Õ£\rÞ¹È=ûY\u0016ï\u0000\u00023©-'XSKêe\u0002\u0090¥\u0082;½Z¨åÚ\u0002õ\u0084ç8\u0012@\r¯?;*ÑD=wMbé\u009cg\u008f\u0085¹7ÔZÇìñtìÛ\u001e!\tB$ÚVkA\u0083s/n\\\u0099\u0093\u008b ¦Å,¦>>\tµ\u001bmfÙp]C9®\u008f¸b\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005:\u0010\u0085bbMä_Xª µÏ\u0087[\u0092±üYÏ Ú\u009e$\u001c7ê\u0001Fl:\u007f\u0080I\u001fTõ¦\u0002±,\u009c¶î\u0010ùôËUÖ-!·3Z\u001eêh^{\u0097FºP\u0016£î\u008dN\u0098\u0092ëðõDÀüÒb=Þ\b¨\u001a\u0013eãw{BÛ,¦>>\tµ\u001bmfÙp]C9®\u008f¸b\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005:\u0010\u0085bbMä_Xª µÏ\u0087[\u0092±üKÏ7Ú\u008b$\u00077è\u0001Nl \u007f\u008eIPTú¦A±9\u009c½î\tùçËUÖ!!¼3\u0014,¦>>\tµ\u001bmfÙp]C9®\u008f¸b\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005:\u0010\u0085bbMä_Xª ,¹>1\t\u0082\u001bnfÂpUC;v¬d#S\u009eAg<Ò*M\u0019'ô¹âs$B6Ì\u0001{\u0013\u0087n x\u009cKÕ¦{°\u0081\u00837\u009d»èÇûeÕ\u008a \u00102\u0085\rÕ\u0018xj\u0097E\u001eW§¢Ù½&\u008f²\u009aXô¡ÇØÒf,ê?\u001c\t¡dÒwsA¹\\\u0002®¹¹Ë\u0094^æóñ\u001cÃ»Þ\u0081)^;ü\u0016\t`¶s0N\u0010Xä«I\u0085§\u00905ãPý\u00adÈ\u0016Ú\u008d57\u0000A\u0012ïm\u0011\u007f\u0090J,¥\u000e·ª\u0082@\u009c¿ï=úYÔç'x1Ö\f \u001fFiíDsV\u009d¡)¼\u0005\u008eß\u0099të\u0084Æ ÑT#Ú>`\b\u0084\u001b)u±ÊðØ~ïÉý5\u0080\u0092\u0096.¥gHÉ^3m\u0085s\t\u0006u\u0015×;8Î¢Ü7ãgöÊ\u0084%«¬¹\u0015LkS\u0094a\u0000tê\u001a\u0013)j<ÔÂXÑ®ç\u0013\u008a`\u0099Á¯\u000b²°@\u000bWyzì\bA\u001f®-\t03ÇíÕOøº\u008eW\u009d\u009c ÷¶ME«k\u0015~\u009b\rê\u0013F&ô4`ÛÊîÀüW\u0083¿\u0091:¤\u008eKòY@l·,¨>3\t\u008f\u001bkfÛpUC+®\u008f¸n\u008bÆ\u0095uà3ó\u0097Ý\u007f(ã:n\u00058\u0010\u0092b\u007fM²_\u0010ªdµ\u0081\u0087\u0019\u0092±üHÏ6Ú\u0099$\u001d7¼\u0001Jl+\u007f\u009aI\u0003Tú¦E±$\u009cºî\u0018ù¦ËQÖ:!¼3\f\u001eìhH{ÒF¬PY£ä\u008dN\u0098Ôë´õ\nÀêÒr\u008c\u008a\u009e\u0007©ð»FÆæÐeã\u001f\u000e¯\u0018K+ý58@\u0019S¯}[\u0088Ñ\u009a~¥3°«ÂBíÞÿv\n\u0004\u0015\u008b'02À\\to\u0010z®\u0084$\u0097Ð¡mÌ\u0005ß\u009bé=ôÛ\u0006i\u0011&<\u009eN\nYÖkwv\u0005\u0081»\u0093:¾ÓÈzÛóæ\u009cð5\u0003Ë-i8ÇK\u008aU.`ÜrZ\u009dð¨\u0098º4,¨>3\t\u008f\u001bkfÛpUC+®\u008f¸n\u008bÆ\u0095uà3ó\u0097Ý\u007f(ã:n\u00058\u0010\u0092b\u007fM²_\u0010ªdµ\u0089\u0087\u0019\u0092ãü[Ï&Ú¥$\u001b7ð\u0001Nl \u007f\u008cI1Tø¦V±$\u009c¢î\u001eùòËHÖ'!½3-\u001eíhI{ÙF\u0090P\u0016£Ð\u008d^\u0098Áëµõ)ÀêÒe=Â\b¹\u001a\u0004eãw{BÛ\u00ad\u0094¿\u000b\u008aû\u0094fçÒò¡Ü\b/¤9m\u0004É\u0017·a\u0001L\u0097^\u007f©×´\u0088\u00862\u0091\u0098ãpÎÎÙ®+66Ý\u0000i\u0013Î}XH8[\u009e¥f°Ù\u0082Aí|ø\u0091ÊoÕÅ'E2>\u001dÂoczûDKWf¢\u0087\u008cg\u009fæéTô!ÇÌÑZ<¾\u000eL\u00193d\u0082v\u001cAúS\u0004¾+\u0089³\u009b\u0017æùðVÃ&.¡8\\\u000bñ\u0015O`%s¥]\u001e,¨>3\t\u008f\u001bkfÛpUC+®\u008f¸n\u008bÆ\u0095uà3ó\u0097Ý\u007f(ã:n\u00058\u0010\u0092b\u007fM²_\u0010ªdµ\u0089\u0087\u0019\u0092ãü[Ï*Ú\u0084$\u00127¼\u0001Wl;\u007f\u009aI\u0018T»¦O±(\u009c§î\fùçËFÖ!!½3\u001d\u001e¥h\\{ÅF±P\u000f£é\u008dO\u0098×ë¯õDÀýÒs=Â\b·\u001a\u000feÿwaBÕ\u00ad¨¿\u0000yÝkk\\ÛN#3\u008b%F\u0016lû×í'Þ\u0093À\u0017µi¦Ã\u00887}ªo\u0002P+EÁ76\u0018à\n\u000eÿuàÉÒMÇµ©\u000b\u009ae\u008fÑqHb TU9s*Ý\u001c\u0002\u0001¨ó\u001cäsÉ¦»n¬\u0082\u009e>,¨>3\t\u008f\u001bkfÛpQC\u001e®\u0093¸u\u008bÀ\u0095Và4ó\u0091Ýe(ô:_\u0005-\u0010\u0089bdMü_pª!µ\u009b\u0087\u001e\u0092þü\\Ï0,«>%\t\u0092\u001bnfÉpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:l\u0005<\u0010\u0091b~M÷_Nª0µÏ\u0087[\u0092±ü[Ï1Ú\u0093$\u00057è\u0001Hln\u007f\u0099I\u0002Tô¦T±$\u009c°î\u001aùôË\u0001Ö!!½3\u0013\u001eñh\f{ÑF¿P\u0010£ì\u008d^\u0098Àë¸,«>%\t\u0092\u001bnfÉpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:l\u0005<\u0010\u0091b~M÷_Nª0µÏ\u0087[\u0092±üYÏ Ú\u009e$\u001c7ê\u0001Fl:\u007f\u0080I\u001fTõ¦a±?\u009c\u00adî\u000fùòËNÖ\u0018!¡3\u0015\u001eóhE{ÓF»P\u000b£ \u008dB\u0098Áëýõ\u0016ÀêÒw=Õ\b¡\u001aCeìwzBÎ\u00adç¿\u0002\u008aú\u0094{ç\u009bò¦Ü\b/\u00979m\u0004ß\u0017±a\u001cL\u009a^u©ËêûøuÏÂÝ> \u0099¶%\u0085lhÂ~8M\u008eS\u0002&~5Ü\u001b3î©ü<ÃlÖÁ¤.\u008b§\u0099\u001el`s\u009fA\u000bTá:\r\ta\u001cÈâJñ¾ÇWªi¹Ñ\u008fI\u0092§`\u0017w=Zà(J?µ\r\u0003\u0010açóõ^Ø¼®\u0012½\u0080\u0080®\u0096Le¾K\u0018^\u0090-ô3D\u0006«\u0014#û\u0085Î¨Ü\u0013£¶±6\u0084\u0087D@VÎays\u0085\u000e\"\u0018\u009e+×ÆyÐ\u0083ã5ý¹\u0088Å\u009bgµ\u0088@\u0012R\u0087m×xz\n\u0095%\u001c7¥ÂÛÝ$ï°úZ\u0094°§Ú²xLî_\u0003i£\u0004\u0085\u0017r!é<\u001fÎ¿ÙÏô[\u0086ñ\u0091\u001f£ê¾ÅIT[äv\u001d\u0000¯\u0013|.S8óË\u0002å¬ð,\u0083D\u009dê,«>%\t\u0092\u001bnfÉpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:l\u0005<\u0010\u0091b~M÷_Nª0µÏ\u0087[\u0092±ü_Ï&Ú\u0084$\u00107î\u0001Fl:\u007f\u0080I\u001eTü¦\u0002±#\u009c±î\bù¦ËMÖ;!¸3Z\u001eàhB{ÔF¬P\u0000£ð\u008d_\u0098Ûë³õ\u0003À¯Ò}=Ô\b¡\u001aCeúwtBÕ\u00adµ,«>%\t\u0092\u001bnfÉpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:l\u0005<\u0010\u0091b~M÷_Nª0µÏ\u0087[\u0092±üQÏ.Ú\u009a$\u001a7ï\u0001Tl'\u007f\u008bI\u001cTþ¦\u0002±9\u009c»î_ùáËDÖ&!¶3\b\u001eähX{ÒFþP\u0015£ó\u008d@\u0098\u0092ë¸õ\nÀìÒd=È\b¨\u001a\u0017eãw{BÛ\u00adç¿\u0005\u008aì\u0094iç\u009bò²Ü\f/\u009d9m,¬>>\t\u0098\u001bpfÔpDC+®\u0083¸e\u008bä\u0095@à1åT÷ÏÀsÒ\u0097¯'¹©\u008a×g\u007f,¤>?\t\u0099\u001bkfÁpQC\f®\u0092¸`\u008bÜ\u0095Fà),«>%\t\u0092\u001bnfÉpuC<®\u0092¸h\u008bÞ\u0095Rà.ó\u008cÝc(ù:l\u0005<\u0010\u0091b~M÷_Nª0µÏ\u0087[\u0092±ü]Ï;Ú\u0089$\u00107ì\u0001Sl'\u007f\u0086I\u001e,§>?\t\u008f\u001bkfËpMC\u001c®\u0090¸l\u008bý\u0095Cà>ó\u0084Ýx(ò:l\u0005<\u0010\u0093b~Mþ_I,§>?\t\u008f\u001bkfËpMC\u001c®\u0090¸l\u008bý\u0095Cà>ó\u0084Ýx(ò:l\u0005<\u0010\u0093b~Mþ_IªdµÂ\u0087V\u0092ÿüWÏ7Ú\u0083$\u00137å\u0001dl8\u007f\u0084I%Të¦F±,\u009c î\u001aùÔËDÖ;!¦3\u0016\u001eñÍ¢ß:è\u008aún\u0087Î\u0091H¢\u0019O\u0095YijøtF\u0001;\u0012\u0081<}É÷Ûiä9ñ\u0096\u0083{¬û¾LKaTÇfSsæ\u001dX. ;\u009dÅ\u0015ÖêàJ\u008d\u0006\u009e\u0089¨\u0001µöGHP,}¢\u000f5\u0018í*v7(À¥Ò\u000fÿï\u0089G\u009aÁ§¾±?BêlCyÚ\n¹\u0014\u000f!î,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092áüJÏ&Ú\u009c$\u001c7ó\u0001Rl=\u007fÉI\u0000Té¦M±.\u009c±î\fùõË\u0001Ö,!¼3\u001f\u001eöhB{\u0090FªPY£å\u008dE\u0098Öëýõ\u0014ÀýÒy=Á\b½\u001a\u0011eæwlB\u009c\u00adê¿N\u008aÏ\u0094\u007fçÉò¡Ü\b/Ô9p\u0004È\u0017\u00ada\u0001L\u009d^\u007f©\u0085´\u00ad\u00864\u0091\u008aãpÎÖÙª+&6\u0094\u0000k\u0013Á,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092áüJÏ&Ú\u009c$\u001c7ó\u0001Rl=\u007fÉI\u0000Té¦M±.\u009c±î\fùõË\u0001Ö-!½3\u001e\u001e¥h\\{ÅF±P\t£å\u008dY\u0098Þë¤õDÀ¢Ò6=ò\b·\u001a\reþw|BÒ\u00ad²¿\u000b,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092Ãü]Ï2Ú\u009f$\u00107ï\u0001Sln\u007f\u0099I\u0011Tâ¦N±\"\u009cµî\u001bù¦ËQÖ:!¶3\n\u001eäh^{ÖFªP\u0010£ï\u008dE\u0098\u0092ë»õ\u0005ÀæÒz=Ô\b¼,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092Ãü]Ï2Ú\u009f$\u00107ï\u0001Sln\u007f\u0080I\u0003T»¦L±(\u009c î\bùéËSÖ#!¿3\u001f\u001eöh_,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092ÜüWÏ Ú\u0081$\u001c7ò\u0001@ln\u007f\u008bI\u0011Tø¦I±(\u009cºî\u001bù¦ËSÖ-! 3\n\u001eêhB{ÄF»¶\u001f¤\u009f\u0093/ôJæÅÑeÃ\u0096¾\u001c¨§\u009bÛv\u007f`\u0087S9M·8Ã+z\u0005\u0092ð5â«ÝØÈeº\u009e\u0095\u0011\u0087¹r\u0094m2_¦J,$§\u0017Ð\u0002qüìï\u0002Ù°´\u009e§x\u0091ã\u008c\u001f~»iËDE6û!\u001f\u0013¾\u000eÖù\u0003ëúÆ\u0000°¯£/\u009e\u000e\u0088ç{\u001fU¯@+3K-ý\u0018\u001c\n\u0087å5ÐAÂü½\u0014,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092ôü@Ï Ú\u008f$\u00057è\u0001Nl!\u007f\u0087IPTì¦J±$\u009c¸î\u001aù¦ËBÖ'!½3\t\u001eñh^{ÂF½P\r£é\u008dE\u0098Õëýõ\tÀàÒu=Ú\bø\u001a\u0010eïwgBÊ\u00ad¢¿\u001c\u008a©\u0094bçÞò±Ü\u001d/\u009b9q\u0004Õ\u0017¤,º>5\t\u0095\u001bffìpWC+®\u008f¸w\u008bÉ\u0095Gà3ó\u008aÝb(Å:[\u0005(\u0010\u0095bnMá_IªdµÂ\u0087V\u0092áüMÏ0Ú\u0082$%7ý\u0001^l\"\u007f\u0086I\u0011Tÿ¦\u0002±$\u009cºî\u001cùêËTÖ,!¶3\u001e\u001e¥hE{ÙFþP\n£å\u008dY\u0098Äë¸õ\u0016À¯Òd=Ô\b«\u001a\u0013eåw{BÏ\u00ad¢¿B\u008a©\u0094yçÕò¶Ü\b/\u00869o\u0004Ô\u0017¤a\u001cL\u009a^t©Â´ì\u0086>\u0091\u008aã9ÎÁÙ¸+r6\u008d\u0000q\u0013Ü}^UèGgpÇb4\u001f¾\t\u0005:y×ÝÁ%ò\u009bì\u0015\u0099a\u008aØ¤0Q\u0097C\t|ziÇ\u001b<4³&\u001bÓ6Ì\u0090þ\u0004ëª\u0085\u0004¶g£Ù]KN§x\u0011\u0015<\u0006Ë0W-ºß\u0018ÈOåç\u0097T\u0080¸²\u001c¯{XåJ\bg¾\u0011\u0010\u0002\u0086?à)^Ú¶ô\u001cá\u0084\u0092¯\u008c_¹³«dD\u0091qïcB\u001c¨\u000e(;\u0080ÔæÆY,¹>\"\t\u0094\u001bafÈpGC,®¶¸t\u008bÛ\u0095[à\u001eó\u0084Ýx(ö:m\u0005<\u0010\u0093bxMû_Rª*µÏ\u0087[\u0092±ü\u0002Ïc\n\\\u0018Ç/q=\u0084@-V¢eÉ\u0088S\u009e\u0091\u00ad>³¾ÆûÕaû\u009d\u000e\u0013\u001c\u0088#Ù6vD\u009dk\u001ey·\u008cÏ\u0093*¡¾´TÚ¾éÉüa\u0002ä\u0011\u001c'¬JßY,oür\r\u0080ç\u0097ÆºDÈöß\u000f,¹>\"\t\u0094\u001bafÈpGC,®¶¸t\u008bÛ\u0095[à\u001eó\u0084Ýx(ö:m\u0005<\u0010\u0093bxMû_Rª*µÏ\u0087[\u0092±ü\\Ï&Ú\u0089$\u001c7ì\u0001Ol+\u007f\u009bI\u0015Tÿ¦\u0002±=\u009cµî\u0006ùêËNÖ)!·3Z\u001e¿h\f,\u009a>\u0013\t¸,¹>\"\t\u0094\u001bafÈpGC,®¶¸t\u008bÛ\u0095[à\u001eó\u0084Ýx(ö:m\u0005<\u0010\u0093bxMû_Rª*µÏ\u0087[\u0092±ü~Ï\u0000Ú§$U7ò\u0001Hl:\u007f\u0080I\u0016Tò¦A±,\u009c î\u0016ùéËOÖh!°3\u0015\u001eáhI{\u0097F·P\n£ \u008dE\u0098Ýë©õDÀüÒo=ß\b»\u001a\u000beøwzBÒ\u00ad®¿\u0014\u008aì\u0094tç\u009bòïÜM/\u00879t\u0004Ï\u0017±a\u0018L\u009a^t©Â´ì\u00866\u0091\u009dãmÎÉÙ½+36\u0089\u0000m\u0013À}X,¹>\"\t\u0094\u001bafÈpGC,®¶¸t\u008bÛ\u0095[à\u001eó\u0084Ýx(ö:m\u0005<\u0010\u0093bxMû_Rª*µÏ\u0087[\u0092±ürÏ\u0010Ú¥$;7Ù\u0001_l-\u007f\u008cI\u0000Tï¦K±\"\u009cº,¹>\"\t\u0094\u001bafÈpGC,®§¸b\u008bÜ\u0095Zà,ó\u0084Ýx(þ:Q\u00057\u0010²bnMá_Mª+µ\u0081\u0087\u0005\u0092ô,¹>\"\t\u0094\u001bafÈpGC,®§¸b\u008bÜ\u0095Zà,ó\u0084Ýx(þ:Q\u00057\u0010²bnMá_Mª+µ\u0081\u0087\u0005\u0092ôü\u0018ÏnÚÊ$'7ù\u0001Tl>\u007f\u0086I\u001eTè¦G±m\u009côîEù¦f\u008bt\u0010C¦QS,ú:u\t\u001eä\u0095òPÁîßhª\u001e¹¶\u0097JbÌpcO\u0005Z\u0080(\\\u0007Ó\u0015\u007fà\u0019ÿ³Í7ØÆ¶*\u0085\\\u0090øn\u0017}ÏKg&\u001d5¶\u0003'\u001eÝìuû\rÖ\u0095¤m³\u0094\u0081)\u009cZJ\u0002X\u0099o/}Ú\u0000s\u0016ü%\u0097È\u001cÞÙígóá\u0086\u0097\u0095?»ÃNE\\êc\u008cv\t\u0004Õ+Z9öÌ\u0090Ó:á¾ôO\u009a£©Õ¼qB\u009dQrgß\n¶\u0019\u0017/\u00982s,¾>1\t\u0097\u001bnfÈp@C\u0016®\u0082\u001cÝ\u000eF9ð+\u0005V¬@#sH\u009eÃ\u0088\u0006»¸¥>ÐHÃàí\u001c\u0018\u009a\n55S ÖR\n}\u0085o)\u009aO\u0085å·a¢\u0090Ì|ÿ\nê®\u0014X\u0007¶1\u0015\\kOÁy]d»\u0096f\u0081j¬âÞ^É¦û\u0000æb\u0011ã\u0003W. X\u0004K\u0080\u0090Â\u0082Yµï§\u001aÚ³Ì<ÿW\u0012Ü\u0004\u00197§)!\\WOÿa\u0003\u0094\u0085\u0086*¹L¬ÉÞ\u0015ñ\u009aã6\u0016P\tú;~.\u008f@cs\u0015f±\u0098O\u008b\u0084½(Ð\\Ãäõjè\u0094\u001a0\rY ÁR$E\u009bw;jZ\u009dÄ\u008fd¢\u009aÔwÇ®úÀìa\u001f\u009a1%$ºWÃI?|\u0080n\u0005\u0081¯´\u0083¦hÙ\u0083Ë\u0001þ±\u0011Õ\u0003q6\u0097(\u000f[àNÚ`d\u0093ê\u0085\u0000¸¸«ÔÝgðáâ\u0000\u0015²\bÄ:\f-ä_\u0010r¾e\u0090\u0097@\u008aè¼\t¯µÁ!ôCçç,Î>U\tã\u001b\u0016f¿p0C[®Ð¸\u0015\u008b«\u0095-à[óóÝ\u000f(\u0089:&\u0005@\u0010Åb\u0019M\u0096_:ª\\µö\u0087r\u0092\u0083üoÏ\u0019Ú½$C7¨\u0001\u0004lp\u007fÈIFT¸¦\u001c±u\u009cíîWù¡Ë\u0003Öl!ì3R\u001e¿h\u001e{\u0093FúPO£°\u008d\u0015\u0098\u008bëíõLÀ±Ò%=\u0099\bý\u001aQe¬w7B\u0082\u00adâ¿\\\u008aºi:{¡L\u0017^â#K5Ä\u0006¯ë$ýáÎ_ÐÙ¥¯¶\u0007\u0098ûm}\u007fÒ@´U1'í\bb\u001aÎï¨ð\u0002Â\u0086×w¹\u009b\u008aí\u009fIa·r|DÐ)¤:\u001c\f\u0092\u0011lãÈô¡Ù9«Ü¼c\u008eÃ\u0093¢d<v\u009c[b-\u008f>V\u00038\u0015\u0099æbÈÝÝB®;°Ç\u0085o\u0097çxWM?_\u0085 g2â\u0007Vè%ú\u0081ÏyÑ³¢Y·3\u0099\u008bjW|ùA]R2$\u008e\t\u0013\u001bíìCñ+,¹>\"\t\u0094\u001bafÈpGC,®§¸b\u008bÜ\u0095Zà,ó\u0084Ýx(þ:Q\u00057\u0010²bnMá_Mª+µ\u0081\u0087\u0005\u0092ôü\u0018ÏnÚÊ$\u00117ù\u0001Dl<\u007f\u0090I\u0000Tï¦G±)\u009côî\u001eùåËUÖ!!¥3\u001b\u001eñhE{ØF°PY£ò\u008dN\u0098Áë\u00adõ\u000bÀáÒe=Ô\bø\u001a\neùw5BÒ\u00ad¨¿\u001a\u008a©\u0094~çÎò®Ü\u0001/Ô9%\u0004\u0086\u0017´a\u0018L\u0097^{©Ñ´¥\u00869\u0091\u0099ã9Î×Ùª+>6\u0091\u0000a\u0013Û}\u0016H2[\u0097¥m°Ì\u0082\\í;ø\u0092Ê|ÕÈ'D22\u001d\u008doc,ª><\t\u0094\u001bwfÉpyC:®\u0095¸r\u008bÉ\u0095Tà3ó\u008bÝk(Ó:_\u0005-\u0010\u0081,¹>\"\t\u0094\u001bafÈpGC,®§¸b\u008bÜ\u0095Zà,ó\u0084Ýx(þ:Q\u00057\u0010²bnMá_Mª+µ\u0081\u0087\u0005\u0092ôü\u0018ÏnÚÊ$\u00167ð\u0001Hl;\u007f\u008dIPTö¦G±>\u009c§î\u001eùáËHÖ&!´3Z\u001eáhM{ÃF¿PY£õ\u008d[\u0098Öë¼õ\u0010ÀêÒ6=Õ\b½\u001a\u0017eïwvBÈ\u00ad¢¿\nê·ø,Ï\u009aÝo Æ¶I\u0085\"h©~lMÒST&\"5\u008a\u001bvîðü_Ã9Ö¼¤`\u008bï\u0099Cl%s\u008fA\u000bTú:\u0016\t`\u001cÄâ\u0011ñáÇFª.¹Ç\u008f\u001b\u0092í`Ow&Zª(\u0005?á\r@\u0010(çýõ\u001dØå®\u0002½Ú\u0080¼\u0096\u0018eûKA^ñ-¶3\u0019\u0006ò\u0014yûØÎ¿Ü\u0003£ã±_\u0084Ók½y\u0001L§\u0087¤\u0095?¢\u0089°|ÍÕÛZè1\u0005º\u0013\u007f Á>GK1X\u0099ve\u0083ã\u0091L®*»¯ÉsæüôP\u00016\u001e\u009c,\u00189éW\u0005dsq×\u008f\"\u009cÒªuÇ\u001dÔ±â\u0015ÿå\rZ\u001a 7½E\u000bRô`R".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 3043);
        q = cArr;
        t = -1063248605567435184L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(int r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = r9 + 1
            int r7 = r7 + 4
            byte[] r0 = o.az.b.$$d
            int r8 = 105 - r8
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L16:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1a:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L33:
            int r9 = -r9
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.b.w(int, byte, short, java.lang.Object[]):void");
    }

    static /* synthetic */ void c(b bVar) {
        int i = r + 53;
        p = i % 128;
        int i2 = i % 2;
        bVar.c();
        int i3 = r + Opcodes.LSUB;
        p = i3 % 128;
        switch (i3 % 2 != 0 ? '#' : 'R') {
            case '#':
                int i4 = 35 / 0;
                return;
            default:
                return;
        }
    }

    b(Context context, c cVar, o.ei.c cVar2, f fVar) {
        super(context, cVar, cVar2, o.bb.e.a);
        this.e = null;
        this.i = null;
        this.f = null;
        this.h = false;
        this.l = false;
        this.j = new Object();
        this.n = fVar;
    }

    @Override // o.y.b
    public final String a() {
        int i = p + 19;
        r = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        v((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), ViewConfiguration.getMaximumFlingVelocity() >> 16, 17 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = p + 83;
        r = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 15 : (char) 21) {
            case 15:
                throw null;
            default:
                return intern;
        }
    }

    final void c(Context context) {
        int i = p + 11;
        r = i % 128;
        boolean z = i % 2 == 0;
        a(context);
        switch (z) {
            case false:
                int i2 = p + Opcodes.DMUL;
                r = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            default:
                throw null;
        }
    }

    final void a(o.h.d dVar, o.c.a aVar, boolean z, boolean z2, boolean z3) {
        synchronized (this.j) {
            this.e = null;
            this.a = aVar;
            this.b = dVar;
            this.f36o = z;
            this.c = z2;
            this.d = z3;
            this.m = new o.az.e();
            c();
        }
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        switch (this.l ? 'U' : 'a') {
            case Opcodes.LADD /* 97 */:
                return new e(this, this.e);
            default:
                int i = r + 69;
                p = i % 128;
                if (i % 2 != 0) {
                }
                o.ee.g.c();
                Object[] objArr = new Object[1];
                v((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (-1) - TextUtils.indexOf((CharSequence) "", '0', 0), (ViewConfiguration.getTouchSlop() >> 8) + 17, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                v((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 17 - (ViewConfiguration.getWindowTouchSlop() >> 8), 54 - TextUtils.getTrimmedLength(""), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                int i2 = r + Opcodes.DDIV;
                p = i2 % 128;
                switch (i2 % 2 == 0 ? Typography.quote : (char) 2) {
                    case 2:
                        int i3 = 39 / 0;
                        return null;
                    default:
                        return null;
                }
        }
    }

    @Override // o.y.b
    public final void f() {
        synchronized (this.j) {
            if (!this.l && !this.h) {
                super.f();
                this.l = true;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                v((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), Drawable.resolveOpacity(0, 0), View.resolveSizeAndState(0, 0, 0) + 17, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                v((char) (62698 - KeyEvent.getDeadChar(0, 0)), 129 - Color.green(0), 32 - View.getDefaultSize(0, 0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                e eVar = (e) i();
                if (eVar != null) {
                    eVar.o();
                }
                return;
            }
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            v((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), 17 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            v((char) (View.resolveSize(0, 0) + 16280), TextUtils.lastIndexOf("", '0', 0) + 72, 58 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\b$e.smali */
    static class e extends o.y.a<b> {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        private static int f;
        private static int h;
        private static long j;
        private final String b;
        private boolean c;
        private boolean d;
        private boolean e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            f = 1;
            char[] cArr = new char[PointerIconCompat.TYPE_ALIAS];
            ByteBuffer.wrap("X1\u0092ÝÍ\u00988ss>®\u0017\u0098ÐÓ»\u000eoy ´?îÅÙµ\u0014kOUº\fôôãT)\u009dvÔ\u0083lÈ3\u0015\u0000#\u0082höµ3Ân\u000fCU\u0093bû¯#ôB\u0001XO¯\u0094î¡?î\u000f;GA£\u008eþÛÁàV-R{¥\u0080þÍ\u008e\u001a\u0014'mm±º\u0095ÇÆ\f]Yxgþ¬\u0085ùÜ\u0006 S6\u0099¨¦\u0098óÓ8>Eu\u0092PØ\u0098åÿ2h\u007f7\u0084\fÒ\u008e\u001fï$!q\u0010¾\u0016Ä\u0099\u0011é^(k\u0007°Fþ£\u000bàPÏ\u009d\u0007ªTðì=îJÉ\u0097\u001cÜc\u009eÌT\u0005\u000bLþôµ«h\u0098^\u000b\u0015\u007fÈº¿érÄ(\u0005\u001fbÒ¡\u0089\u0095|Â2~éqÜ \u0093\u009bFÔ<,óo¦X,»ær¹;L\u0083\u0007ÜÚïìo§\u000ezÔ\r\u0098À±\u009av\u00adA`Ò;ìÎµ\u0080H[\u0000nÐ!îô´\u008eAA\t\u0014k/êâ»´JO\u0007\u0002aÕíè\u0088¢Qul\b$Ãá\u0096\u0086¨UhÔ¢\u001dýT\bìC³\u009e\u0080¨\u0013ãq>¢Ið\u0084ÏÞ\u0012éz$¹\u007f\u0081\u008aÕÄ2\u001fa*µe\u0082°\u009eÊ2\u0005wPUk\u0083¦Ñð8\u000byFJ+5áü¾µK\r\u0000RÝaëæ \u008b}R\n\u0001Ç;\u009døª\u008cgE<fÉ1\u0087\u0087\\\u0088iN&yó7\u0089ÄF\u009d\u0013±(~å:³ÊH\u0089\u0005¦Ò~ï\r¥\u0095rõ\u000f¬Äj\u0091\u0018¯Ödó1¶ÎAHv\u0082¿Ýö(Nc\u0011¾\"\u0088£ÃÃ\u001e\u001ai^¤aþ°ÉË\u0004R_!ªuä\u0090?Ã\n\u000eE/\u0090hê\u008b%ßpèKt\u0086hÐ\u008d+Ïfù±7\u008cSÆ\u0082,»ær¹;L\u0083\u0007ÜÚïìI§\u000ezÚ\r\u009fÀ«\u009az\u00ad\u0002`Þ;áÎû\u0080O[\u0006nÜ!ïô¤\u008e]A\u0018\u0014k/öâ¹´\u0005O\u0012\u0002\"Õëè\u0084¢Muh\b3Ãü\u0096\u008c¨_c/6pÉ\u008b\u009c\u0098VUij<!÷Õ\u008a\u0096]£\u0017|Þß\u0014\u0016K_¾çõ¸(\u008b\u001e)U}\u0088²ÿð2Äh\u0004_v\u0092²É\u0087<Ørm©a\u009c°Ó\u0084\u0006Þ|.³wæKÝÝ\u0010ÁF$½dðU'\u0094\u001açP,\u0087\b,»ær¹;L\u0083\u0007ÜÚïìs§\u001ezÕ\r\u009bÀå\u009ar\u00ad\u0002`Ë;äÎ\u00ad\u0080H[\u0013nÜ!ìô¿\u008e\u000fA\u000f\u0014./êâ§´JO\u001d\u00022Õú,»ær¹;L\u0083\u0007ÜÚïìm§\u001ezË\r\u0084À°\u009az\u00ad\u000f`Ø;\u00adÎ¬\u0080@[\u0013nÝ!£ô¡\u008eZA\u000e\u0014#/¹â§´DO\n\u0002-Õðè\u008c¢_u)\b5Ãð\u0096\u0080¨Tcf6+ÉÎ\u009c\u009dV\u0017ic<!÷Î\u008a\u0092]í\u0017u*\u0006ýÓ°\u009cK¥\u001dxÐ\fëÜ¾ÿq°\u000bxÞ\u000b,»ær¹;L\u0083\u0007ÜÚïìm§\u001ezË\r\u0084À°\u009az\u00ad\u000f`Ø;\u00adÎ¬\u0080@[\u0013nÝ!£ô¡\u008eZA\u000e\u0014#/¹â§´DO\n\u0002-Õðè\u008c¢_u)\b*Ãú\u0096\u0080¨Zcj69É\u008b\u009c\u0096VEi%<!÷Ä\u008a\u009c]¨\u0017r*\u001fýÂ°\u0091Kã\u001dwÐ\u001dëÒ¾æqù\u000b\u007fÞ\u0011\u0091Ç¤ñ\u007fÿ1_Ä\u001e\u009f:R÷eº?Mò\u0002\u0085*,»ær¹;L\u0083\u0007ÜÚïìs§\u0004z\u0099\r\u0087À°\u009a`\u00ad\t`\u009f;ýÎº\u0080P[\u000bnÚ!âôµ\u008e\u000fA\u0014\u0014%/¹â¿´QO\u0007\u00021Õ¿è\u009f¢^uz\b7Ãú\u0096\u008d¨Bcj6}ÉÊ\u009c\u0097VSi%<=÷Î\u008aß]½\u0017n*\u001aýÏ°ÕK\u00ad\u001d~Ð\u001bëÔ¾íq°\u000btÞ\u0004\u0091Ç¤è\u007f°1CÄ[\u009f=Rèeõ?Fò\t\u0085?Xø\u0013\u0088%MøW³hF³\u0019\u0091ÓJæ\u007f¹(LÜ\u0007\u008eÙ[ìd§qzØ\r\u0094À¿\u009aq\u00adw`Ë;\u0086Î\u00ad\u0080s[MnË!üô´\u008e}AC\u0014Á/îâ¤´GO\u0016\u0002ÖÕá,»ær¹;L\u0083\u0007ÜÚïìn§\u001fzÖ\r\u0087Àµ\u009az\u00ad\u000f`Ø;\u00adÎº\u0080G[\u0003n\u0095!ôô°\u008eFA\t\u0014\"/÷â°´\u0005O\u0003\u00024Õìè\u0085¢\u001buy\b&Ãì\u0096\u008f¨^cn69É\u008b\u009c\u008dVXi%<1÷Ä\u008aß]¿\u0017~*\nýÂ°\u009cKµ\u001dtÐ\u000bë\u009d¾íq«\u000bxÞ\b\u0091\u0093¤ï\u007f°1YÄ\u0012\u009f/Rîe¶?Bò\u0005\u0085&Xò\u0013\u0085,»ær¹;L\u0083\u0007ÜÚïìm§\u0019zÖ\r\u0094À \u009a`\u00ad\u0012`Ö;ãÎ¼\u0080\t[\u0017nÀ!ðô¹\u008e\u000fA9\u0014*/íâ¶äÑ.\u0018qQ\u0084éÏ¶\u0012\u0085$\u0012os²¡Åò\bÝRYe|¨½ó\u0082\u0006ßHc\u0093}¦\u00adé\u0086<ØF \u0089dÜRç\u009a*Ó|(\u00879Ê[\u001d\u0080 ôj9½CÀi\u000b\u009e^ý`:Æ\u0010\fÙS\u0090¦(íw0D\u0006ÒM¥\u0090aç%*\u0000pÛG¢\u008afÑI$\u001ejë±¶\u0084{ËL\u001eZdô«£þ\u0093ÅZ\b\\^ê¥¹è\u009e?U\u0002fH½\u009f\u0082â»)_|!Bî\u0089\u0084Ü\u0090#ov ¼¼\u0083ÀÖ\u009d\u001d}`t·\u0016ýÅÀ±\u0017d%\u001bïÒ°\u009bE#\u000e|ÓOåÍ®¾sj\u0004?ÉE\u0093×¤ ik2LÇ[\u0089ùRµgz(Sý\u0014\u0087ýH±\u001d\u0092&\u0019ë\u0007½÷F¼\u000b\u0082ÜZá>«è|Ì\u0001\u0083\u0095\u009f_V\u0000\u001fõ§¾øcËUI\u001e=Ãò´°y\u0084#D\u00146Ùò\u0082Çw\u00989-â\"×ò\u0098ÓM\u009c7}ø8\u00ad\u001b\u0096Ô[\u009c\roöw»\u0017lÞQº\u001boÌB±\rzÂ/¢y5³üìµ\u0019\rRR\u008fa¹òò\u0086/CX\u0010\u0095=Ïüø\u009b5Xnl\u009b;Õ\u0087\u000e\u009b;^t~¡/ÛÎ\u0014\u009dA¶zr·yáÛ\u001a\u008fW \u0080r½\u0006÷Æ ô] \u0096uÃ\ný\u009f6çc²\u009cLÉ\u001b\u0003Ì<ùi¸ü\u00966_i\u0016\u009c®×ñ\nÂ<Qw%ªàÝ³\u0010\u009eJ_}8°ûëÏ\u001e\u0098P$\u008b8¾ýñÝ$\u008c^m\u0091>Ä\u0015ÿÑ2Údx\u009f,Ò\u0003\u0005Ñ8¥re¥WØ\u0003\u0013ÖF©x<³Qæ\u0005\u0019åL·\u0086\u007f¹[ì\r]t\u0097¸Èý=\u0016v[«r\u009dµÖÞ\u000b\n|E±9ë¸ÜÜ\u0011\nJ%¿nñ\u009b*Ü\u001fIP9\u0085bÿ\u00810\u0081eç^0\u0093xÅ\u0091>\u008fsó¤,\u0099EÓ\u008e\u0004³yò²*ç^Ù\u0099\u0012ºGî¸\u0019r\u0094¸[ç5\u0012ôY²\u0084\u0088²Jù*$ÿS\u0096\u009e\u0082ÄBó0>èeÌ\u0090\u009a,¦æi¹\u0007LÆ\u0007\u0080Úºìx§\u0018zÍ\r±À¤\u009az\u00ad\r".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, PointerIconCompat.TYPE_ALIAS);
            a = cArr;
            j = -4565206652553796089L;
        }

        static void init$0() {
            $$a = new byte[]{108, 119, -51, 110};
            $$b = 56;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r7, int r8, byte r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.az.b.e.$$a
                int r9 = 105 - r9
                int r8 = r8 * 2
                int r8 = 1 - r8
                int r7 = r7 * 3
                int r7 = 3 - r7
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r8
                r8 = r7
                goto L35
            L19:
                r3 = r2
            L1a:
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                int r7 = r7 + 1
                if (r4 != r8) goto L2b
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2b:
                r3 = r0[r7]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r6
            L35:
                int r7 = -r7
                int r7 = r7 + r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r6 = r9
                r9 = r7
                r7 = r8
                r8 = r6
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.az.b.e.l(byte, int, byte, java.lang.Object[]):void");
        }

        e(b bVar, String str) {
            super(bVar, false);
            this.c = false;
            this.d = false;
            this.e = false;
            this.b = str;
        }

        @Override // o.y.a
        public final void a() {
            int i = h + 87;
            f = i % 128;
            switch (i % 2 == 0 ? '?' : (char) 29) {
                case 29:
                    o.b.c.e(f(), g());
                    return;
                default:
                    o.b.c.e(f(), g());
                    throw null;
            }
        }

        @Override // o.y.a
        public final void j() {
            n();
            if (!this.c) {
                e().b(h().b());
                synchronized (e().j) {
                    if (!this.e) {
                        d();
                        this.d = false;
                    }
                }
                o.db.b.a().a(g(), false);
                if (o.de.a.e(g())) {
                    o.ee.g.c();
                    Object[] objArr = new Object[1];
                    k((char) (MotionEvent.axisFromString("") + 29882), View.MeasureSpec.getSize(0), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 16, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    k((char) (53231 - Drawable.resolveOpacity(0, 0)), 17 - View.getDefaultSize(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0) + 73, objArr2);
                    o.ee.g.d(intern, ((String) objArr2[0]).intern());
                    h().a().e(g(), f(), f.A);
                }
            }
        }

        private void n() {
            String str;
            boolean z;
            Object[] objArr = new Object[1];
            k((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 29880), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 17 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            if (this.b == null) {
                synchronized (e().j) {
                    if (this.e) {
                        o.ee.g.c();
                        Object[] objArr2 = new Object[1];
                        k((char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 45687), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 88, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 24, objArr2);
                        o.ee.g.d(intern, ((String) objArr2[0]).intern());
                        return;
                    }
                    if (!b()) {
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        k((char) KeyEvent.keyCodeFromString(""), 113 - (ViewConfiguration.getTouchSlop() >> 8), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 37, objArr3);
                        o.ee.g.e(intern, ((String) objArr3[0]).intern());
                        return;
                    }
                    this.d = true;
                    o.bn.e.b().a(g(), true, false);
                    o.ad.c c = new a(g(), e().a, e().d).c(e().b);
                    if (!c.c() || c.m()) {
                        o.ee.g.c();
                        String c2 = c();
                        Object[] objArr4 = new Object[1];
                        k((char) (View.resolveSizeAndState(0, 0, 0) + 17519), KeyEvent.normalizeMetaState(0) + Opcodes.FCMPG, 29 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr4);
                        o.ee.g.d(c2, ((String) objArr4[0]).intern());
                        h().c(o.bb.a.ae);
                        return;
                    }
                    if (c.h() || c.o()) {
                        o.ee.g.c();
                        String c3 = c();
                        Object[] objArr5 = new Object[1];
                        k((char) (1935 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + Opcodes.PUTSTATIC, KeyEvent.keyCodeFromString("") + 40, objArr5);
                        o.ee.g.d(c3, ((String) objArr5[0]).intern());
                        h().c(o.bb.a.aj);
                    }
                    e().g = c;
                    o.dc.a.e(e());
                    o.ee.g.c();
                    String c4 = c();
                    Object[] objArr6 = new Object[1];
                    k((char) (Color.argb(0, 0, 0, 0) + 25805), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 218, 32 - (Process.myTid() >> 22), objArr6);
                    o.ee.g.d(c4, ((String) objArr6[0]).intern());
                    e().l();
                    if (!h().b()) {
                        o.ee.g.c();
                        Object[] objArr7 = new Object[1];
                        k((char) TextUtils.getOffsetAfter("", 0), 252 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 49 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr7);
                        o.ee.g.e(intern, ((String) objArr7[0]).intern());
                        o.dc.a.a(e());
                        return;
                    }
                    o.ee.g.c();
                    Object[] objArr8 = new Object[1];
                    k((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 62052), 299 - View.MeasureSpec.getMode(0), 33 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr8);
                    o.ee.g.d(intern, ((String) objArr8[0]).intern());
                }
            }
            o.eg.b bVar = e().i;
            if (bVar == null) {
                o.ee.g.c();
                Object[] objArr9 = new Object[1];
                k((char) (ViewConfiguration.getFadingEdgeLength() >> 16), 332 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 30 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr9);
                o.ee.g.e(intern, ((String) objArr9[0]).intern());
                return;
            }
            synchronized (e().j) {
                if (this.e) {
                    o.ee.g.c();
                    Object[] objArr10 = new Object[1];
                    k((char) (45687 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), TextUtils.lastIndexOf("", '0') + 90, TextUtils.indexOf((CharSequence) "", '0') + 25, objArr10);
                    o.ee.g.d(intern, ((String) objArr10[0]).intern());
                    return;
                }
                if (this.b != null) {
                    o.ee.g.c();
                    Object[] objArr11 = new Object[1];
                    k((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getPressedStateDuration() >> 16) + 362, 59 - KeyEvent.getDeadChar(0, 0), objArr11);
                    o.ee.g.d(intern, ((String) objArr11[0]).intern());
                    str = this.b;
                    z = true;
                } else if (e().f != null) {
                    o.ee.g.c();
                    Object[] objArr12 = new Object[1];
                    k((char) TextUtils.indexOf("", ""), 421 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 70 - KeyEvent.getDeadChar(0, 0), objArr12);
                    o.ee.g.d(intern, ((String) objArr12[0]).intern());
                    o.dc.a.a(e());
                    str = e().f;
                    z = false;
                } else if (o.bi.e.d(g()).b().j() != b.a.d) {
                    o.ee.g.c();
                    Object[] objArr13 = new Object[1];
                    k((char) (ViewConfiguration.getTapTimeout() >> 16), 490 - ((byte) KeyEvent.getModifierMetaStateMask()), 107 - View.MeasureSpec.getMode(0), objArr13);
                    o.ee.g.d(intern, ((String) objArr13[0]).intern());
                    o.dc.a.a(e());
                    str = null;
                    z = false;
                } else {
                    o.ee.g.c();
                    Object[] objArr14 = new Object[1];
                    k((char) Color.blue(0), 597 - ImageFormat.getBitsPerPixel(0), Process.getGidForName("") + 73, objArr14);
                    o.ee.g.d(intern, ((String) objArr14[0]).intern());
                    this.c = true;
                    return;
                }
                if (str != null) {
                    o.ee.g.c();
                    Object[] objArr15 = new Object[1];
                    k((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 670 - (ViewConfiguration.getTapTimeout() >> 16), 26 - KeyEvent.getDeadChar(0, 0), objArr15);
                    o.ee.g.d(intern, ((String) objArr15[0]).intern());
                    boolean e = e().e(str);
                    if (!h().b()) {
                        o.ee.g.c();
                        String c5 = c();
                        Object[] objArr16 = new Object[1];
                        k((char) (51306 - Color.green(0)), 696 - (ViewConfiguration.getLongPressTimeout() >> 16), 37 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr16);
                        o.ee.g.d(c5, ((String) objArr16[0]).intern());
                        o.dc.a.a(e());
                        return;
                    }
                    if (e) {
                        if (z) {
                            o.ee.g.c();
                            String c6 = c();
                            Object[] objArr17 = new Object[1];
                            k((char) (60075 - View.combineMeasuredStates(0, 0)), 733 - TextUtils.getTrimmedLength(""), 50 - TextUtils.indexOf("", ""), objArr17);
                            o.ee.g.d(c6, ((String) objArr17[0]).intern());
                            this.c = true;
                            return;
                        }
                        h().c(o.bb.a.b);
                        o.dc.a.a(e());
                        return;
                    }
                    o.ee.g.c();
                    String c7 = c();
                    Object[] objArr18 = new Object[1];
                    k((char) (2463 - TextUtils.indexOf((CharSequence) "", '0', 0)), TextUtils.indexOf("", "") + 783, KeyEvent.keyCodeFromString("") + 34, objArr18);
                    o.ee.g.d(c7, ((String) objArr18[0]).intern());
                    o.dc.a.a(e());
                }
                o.ee.g.c();
                Object[] objArr19 = new Object[1];
                k((char) (47396 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), 817 - View.MeasureSpec.makeMeasureSpec(0, 0), TextUtils.getOffsetBefore("", 0) + 36, objArr19);
                o.ee.g.d(intern, ((String) objArr19[0]).intern());
                e().c(bVar);
                if (!h().b()) {
                    o.ee.g.c();
                    Object[] objArr20 = new Object[1];
                    k((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 21901), 852 - Process.getGidForName(""), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 44, objArr20);
                    o.ee.g.d(intern, ((String) objArr20[0]).intern());
                } else {
                    o.ee.g.c();
                    Object[] objArr21 = new Object[1];
                    k((char) (TextUtils.indexOf("", "", 0, 0) + 53293), 896 - TextUtils.indexOf((CharSequence) "", '0'), 43 - TextUtils.indexOf((CharSequence) "", '0'), objArr21);
                    o.ee.g.d(intern, ((String) objArr21[0]).intern());
                    if (e().c) {
                        d.d(g());
                    }
                }
                if (!h().b() && h().d() == o.bb.a.D) {
                    h().a().d();
                }
                if (h().b() || h().d() == o.bb.a.ag || h().d() == o.bb.a.ae) {
                    if (o.b.c.d(g(), false)) {
                        i().b();
                    }
                    if (h().d() == o.bb.a.ag || h().d() == o.bb.a.ae) {
                        o.b.e e2 = o.b.c.e(g());
                        if (o.b.c.f(g())) {
                            if (e2 == o.b.e.e) {
                                i().b();
                            } else {
                                i().d();
                            }
                        }
                    }
                }
            }
        }

        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            if (this.c) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((char) (29881 - Color.blue(0)), TextUtils.getOffsetAfter("", 0), 17 - Color.green(0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 29147), View.MeasureSpec.getMode(0) + 941, 40 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                return;
            }
            synchronized (e().j) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                k((char) (29882 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), View.MeasureSpec.getSize(0), View.combineMeasuredStates(0, 0) + 17, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                k((char) (24114 - Color.red(0)), 981 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 16 - ((Process.getThreadPriority(0) + 20) >> 6), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                if (e().b != null) {
                    e().b.a();
                }
                e().h = true;
                e().j().b(dVar, i());
            }
        }

        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            synchronized (e().j) {
                o.ee.g.c();
                Object[] objArr = new Object[1];
                k((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 29880), TextUtils.lastIndexOf("", '0', 0, 0) + 1, ImageFormat.getBitsPerPixel(0) + 18, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                k((char) TextUtils.indexOf("", "", 0, 0), 997 - TextUtils.indexOf("", ""), TextUtils.indexOf((CharSequence) "", '0') + 14, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                o.bb.a d = dVar.d();
                if (d == o.bb.a.D) {
                    h().f();
                    e().e = null;
                    e().f = null;
                    e().i = null;
                    b.c(e());
                } else {
                    if (d != o.bb.a.ag && d != o.bb.a.ae) {
                        if (d == o.bb.a.af) {
                            if (e().b != null) {
                                e().b.a();
                            }
                        } else {
                            e().h = true;
                            if (e().b != null) {
                                e().b.a();
                            }
                            e().j().c(dVar);
                        }
                    }
                    boolean z = d == o.bb.a.ag;
                    e().h = true;
                    if (e().b != null) {
                        e().b.a();
                    }
                    e().j().e(z, o.j.c.d(g()), dVar);
                }
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0025. Please report as an issue. */
        final void o() {
            this.e = true;
            h().c(o.bb.a.af);
            if (this.d) {
                d();
                int i = h + 51;
                f = i % 128;
                switch (i % 2 == 0 ? '\t' : (char) 31) {
                }
            }
            int i2 = h + 1;
            f = i2 % 128;
            switch (i2 % 2 == 0 ? '7' : 'K') {
                case '7':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(char r21, int r22, int r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 1096
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.az.b.e.k(char, int, int, java.lang.Object[]):void");
        }
    }

    @Override // o.az.j.c
    public final void m() {
        int i = p + 57;
        r = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        v((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), View.resolveSizeAndState(0, 0, 0), Color.alpha(0) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        v((char) (ViewConfiguration.getJumpTapTimeout() >> 16), 161 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 22 - View.MeasureSpec.getMode(0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        j().a();
        int i3 = r + 31;
        p = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.az.j.c
    public final void o() {
        int i = r + 75;
        p = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        v((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), TextUtils.getTrimmedLength("") + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        v((char) (ViewConfiguration.getEdgeSlop() >> 16), 182 - TextUtils.indexOf((CharSequence) "", '0'), 20 - TextUtils.lastIndexOf("", '0', 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        j().c();
        int i3 = p + 63;
        r = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.az.c
    public final void d(String str, String str2) {
        try {
            synchronized (this.j) {
                o.eg.b c2 = c(str, str2);
                o.ee.g.c();
                Object[] objArr = new Object[1];
                v((char) (TextUtils.lastIndexOf("", '0', 0) + 1), (-1) - ImageFormat.getBitsPerPixel(0), 17 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
                String intern = ((String) objArr[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                v((char) (View.resolveSizeAndState(0, 0, 0) + 47200), 205 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 44 - ImageFormat.getBitsPerPixel(0), objArr2);
                o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(c2).toString());
                if (!this.l && !this.h) {
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    v((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1, 17 - TextUtils.getCapsMode("", 0, 0), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    v((char) (AndroidCharacter.getMirror('0') - '0'), Color.green(0) + 311, 45 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr4);
                    o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                    this.e = c2.b();
                    c();
                    return;
                }
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                v((char) (Process.myTid() >> 22), 1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), ((Process.getThreadPriority(0) + 20) >> 6) + 17, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                v((char) TextUtils.getTrimmedLength(""), 249 - Color.argb(0, 0, 0, 0), 62 - View.MeasureSpec.getSize(0), objArr6);
                o.ee.g.d(intern3, ((String) objArr6[0]).intern());
            }
        } catch (o.eg.d e2) {
            o.ee.g.c();
            Object[] objArr7 = new Object[1];
            v((char) View.MeasureSpec.getSize(0), '0' - AndroidCharacter.getMirror('0'), 16 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            v((char) Color.argb(0, 0, 0, 0), TextUtils.lastIndexOf("", '0') + 356, 22 - Gravity.getAbsoluteGravity(0, 0), objArr8);
            o.ee.g.a(intern4, ((String) objArr8[0]).intern(), e2);
        }
    }

    private static o.eg.b c(String str, String str2) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        v((char) View.combineMeasuredStates(0, 0), 377 - View.resolveSizeAndState(0, 0, 0), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 7, objArr);
        bVar.d(((String) objArr[0]).intern(), str);
        Object[] objArr2 = new Object[1];
        v((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 23062), 383 - MotionEvent.axisFromString(""), TextUtils.lastIndexOf("", '0', 0, 0) + 10, objArr2);
        bVar.d(((String) objArr2[0]).intern(), str2);
        int i = p + Opcodes.LUSHR;
        r = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 55 / 0;
                return bVar;
            default:
                return bVar;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:20:0x017c A[Catch: d -> 0x0409, TryCatch #2 {d -> 0x0409, blocks: (B:18:0x0169, B:20:0x017c, B:22:0x01dd, B:23:0x01e8, B:25:0x01f0, B:27:0x0217, B:29:0x028b, B:31:0x02b6, B:32:0x0316, B:41:0x02c1, B:43:0x02f1, B:46:0x021f, B:48:0x024a, B:51:0x0255, B:56:0x03db, B:58:0x01db), top: B:17:0x0169, inners: #0, #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:25:0x01f0 A[Catch: d -> 0x0409, TRY_LEAVE, TryCatch #2 {d -> 0x0409, blocks: (B:18:0x0169, B:20:0x017c, B:22:0x01dd, B:23:0x01e8, B:25:0x01f0, B:27:0x0217, B:29:0x028b, B:31:0x02b6, B:32:0x0316, B:41:0x02c1, B:43:0x02f1, B:46:0x021f, B:48:0x024a, B:51:0x0255, B:56:0x03db, B:58:0x01db), top: B:17:0x0169, inners: #0, #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:29:0x028b A[Catch: d -> 0x0409, TRY_LEAVE, TryCatch #2 {d -> 0x0409, blocks: (B:18:0x0169, B:20:0x017c, B:22:0x01dd, B:23:0x01e8, B:25:0x01f0, B:27:0x0217, B:29:0x028b, B:31:0x02b6, B:32:0x0316, B:41:0x02c1, B:43:0x02f1, B:46:0x021f, B:48:0x024a, B:51:0x0255, B:56:0x03db, B:58:0x01db), top: B:17:0x0169, inners: #0, #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:35:0x03a9  */
    /* JADX WARN: Removed duplicated region for block: B:43:0x02f1 A[Catch: d -> 0x0409, TryCatch #2 {d -> 0x0409, blocks: (B:18:0x0169, B:20:0x017c, B:22:0x01dd, B:23:0x01e8, B:25:0x01f0, B:27:0x0217, B:29:0x028b, B:31:0x02b6, B:32:0x0316, B:41:0x02c1, B:43:0x02f1, B:46:0x021f, B:48:0x024a, B:51:0x0255, B:56:0x03db, B:58:0x01db), top: B:17:0x0169, inners: #0, #3, #4, #5 }] */
    /* JADX WARN: Removed duplicated region for block: B:58:0x01db A[Catch: d -> 0x0409, TRY_LEAVE, TryCatch #2 {d -> 0x0409, blocks: (B:18:0x0169, B:20:0x017c, B:22:0x01dd, B:23:0x01e8, B:25:0x01f0, B:27:0x0217, B:29:0x028b, B:31:0x02b6, B:32:0x0316, B:41:0x02c1, B:43:0x02f1, B:46:0x021f, B:48:0x024a, B:51:0x0255, B:56:0x03db, B:58:0x01db), top: B:17:0x0169, inners: #0, #3, #4, #5 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.az.j n() {
        /*
            Method dump skipped, instructions count: 1086
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.b.n():o.az.j");
    }

    final void b(boolean z) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        v((char) TextUtils.getTrimmedLength(""), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 17 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        v((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), 1370 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 20, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.i.d c2 = o.i.d.c();
        if (this.k != null) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            v((char) (ViewConfiguration.getJumpTapTimeout() >> 16), View.MeasureSpec.getMode(0), Process.getGidForName("") + 18, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            v((char) ExpandableListView.getPackedPositionGroup(0L), (ViewConfiguration.getTapTimeout() >> 16) + 1391, View.getDefaultSize(0, 0) + 45, objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
            o.i.d.a(g(), true, this.k, z);
            switch (g().d().a() ? '4' : 'A') {
                case '4':
                    int i = r + 25;
                    p = i % 128;
                    if (i % 2 != 0) {
                        g().d().b(e());
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    }
                    g().d().b(e());
                    int i2 = p + 71;
                    r = i2 % 128;
                    int i3 = i2 % 2;
                    break;
            }
        }
        o.ee.g.c();
        Object[] objArr5 = new Object[1];
        v((char) TextUtils.getOffsetAfter("", 0), (-1) - TextUtils.lastIndexOf("", '0'), 17 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        v((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 57606), (ViewConfiguration.getScrollBarSize() >> 8) + 1436, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 55, objArr6);
        o.ee.g.d(intern3, ((String) objArr6[0]).intern());
        switch (c2.b(e(), g(), d()) ? (char) 28 : '0') {
            case 28:
                h().c();
                return;
            default:
                return;
        }
    }

    /* renamed from: o.az.b$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\b$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        private static int a;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            a = 0;
            e = 1;
            int[] iArr = new int[o.cf.f.values().length];
            d = iArr;
            try {
                iArr[o.cf.f.e.ordinal()] = 1;
                int i = e + 35;
                a = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.cf.f.a.ordinal()] = 2;
                int i3 = a;
                int i4 = (i3 & 93) + (i3 | 93);
                e = i4 % 128;
                if (i4 % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[o.cf.f.d.ordinal()] = 3;
                int i5 = e;
                int i6 = (i5 & 9) + (i5 | 9);
                a = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        int i7 = 34 / 0;
                        return;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x02c0, code lost:
    
        if (o.ee.o.a.c(r0, r1) != false) goto L46;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x02ee, code lost:
    
        h().j();
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x02ec, code lost:
    
        if (o.ee.o.a.c(r2.b().e(), o.cf.a.p, o.cf.a.r) != false) goto L46;
     */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0309  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x0311  */
    /* JADX WARN: Removed duplicated region for block: B:54:0x0325  */
    /* JADX WARN: Removed duplicated region for block: B:79:0x030c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    final void l() {
        /*
            Method dump skipped, instructions count: 1116
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.b.l():void");
    }

    final boolean e(String str) {
        Object[] objArr = new Object[1];
        v((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), ViewConfiguration.getPressedStateDuration() >> 16, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        v((char) TextUtils.getTrimmedLength(""), 2082 - View.combineMeasuredStates(0, 0), 27 - Gravity.getAbsoluteGravity(0, 0), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        String b = new o.dd.e(e()).b(2, 4, str, null, null, null, null);
        switch (b != null ? (char) 20 : 'S') {
            case 20:
                switch (b.isEmpty() ? '\n' : 'E') {
                    case '\n':
                        break;
                    default:
                        o.ee.g.c();
                        StringBuilder sb2 = new StringBuilder();
                        Object[] objArr3 = new Object[1];
                        v((char) TextUtils.indexOf("", ""), 2148 - ((byte) KeyEvent.getModifierMetaStateMask()), 46 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr3);
                        o.ee.g.d(intern, sb2.append(((String) objArr3[0]).intern()).append(b).toString());
                        try {
                            o.eg.b bVar = new o.eg.b(b);
                            Object[] objArr4 = new Object[1];
                            v((char) View.MeasureSpec.getSize(0), 2195 - ExpandableListView.getPackedPositionType(0L), 3 - View.combineMeasuredStates(0, 0), objArr4);
                            if (bVar.b(((String) objArr4[0]).intern())) {
                                int i = r + 47;
                                p = i % 128;
                                int i2 = i % 2;
                                Object[] objArr5 = new Object[1];
                                v((char) (ViewConfiguration.getJumpTapTimeout() >> 16), 2195 - (Process.myTid() >> 22), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 3, objArr5);
                                if (bVar.i(((String) objArr5[0]).intern()).intValue() == -1) {
                                    o.ee.g.c();
                                    Object[] objArr6 = new Object[1];
                                    v((char) (ViewConfiguration.getTouchSlop() >> 8), 2198 - (ViewConfiguration.getScrollBarSize() >> 8), View.combineMeasuredStates(0, 0) + 88, objArr6);
                                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                                    return true;
                                }
                            }
                            Object[] objArr7 = new Object[1];
                            v((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 1304 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 12 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr7);
                            this.m.c(e(), bVar.r(((String) objArr7[0]).intern()));
                            int i3 = p + 33;
                            r = i3 % 128;
                            int i4 = i3 % 2;
                            return false;
                        } catch (o.eg.d e2) {
                            o.ee.g.c();
                            Object[] objArr8 = new Object[1];
                            v((char) (ViewConfiguration.getTapTimeout() >> 16), 2287 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), Color.blue(0) + 38, objArr8);
                            o.ee.g.a(intern, ((String) objArr8[0]).intern(), e2);
                            d().c(o.bb.a.b);
                            return false;
                        }
                }
        }
        o.ee.g.c();
        Object[] objArr9 = new Object[1];
        v((char) (9957 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 2109, ((Process.getThreadPriority(0) + 20) >> 6) + 40, objArr9);
        o.ee.g.e(intern, ((String) objArr9[0]).intern());
        d().c(o.bb.a.a);
        return false;
    }

    /* JADX WARN: Removed duplicated region for block: B:100:0x047d  */
    /* JADX WARN: Removed duplicated region for block: B:104:0x0525 A[RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:105:0x0526 A[Catch: d -> 0x05cb, TryCatch #1 {d -> 0x05cb, blocks: (B:56:0x023e, B:58:0x0246, B:60:0x02b3, B:61:0x02bc, B:65:0x02cb, B:66:0x02d3, B:68:0x02da, B:69:0x02dd, B:73:0x02e0, B:76:0x02ea, B:77:0x02ed, B:79:0x03d3, B:81:0x03dd, B:86:0x03f8, B:92:0x040a, B:94:0x041d, B:102:0x051b, B:105:0x0526, B:115:0x054f, B:117:0x040e, B:122:0x02ff, B:126:0x039e, B:127:0x0598, B:96:0x046b, B:99:0x047a, B:107:0x047f, B:109:0x04ab, B:111:0x04c1), top: B:55:0x023e, inners: #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:107:0x047f A[Catch: d -> 0x054e, TryCatch #2 {d -> 0x054e, blocks: (B:96:0x046b, B:99:0x047a, B:107:0x047f, B:109:0x04ab, B:111:0x04c1), top: B:95:0x046b, outer: #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:113:0x0478  */
    /* JADX WARN: Removed duplicated region for block: B:117:0x040e A[Catch: d -> 0x05cb, TryCatch #1 {d -> 0x05cb, blocks: (B:56:0x023e, B:58:0x0246, B:60:0x02b3, B:61:0x02bc, B:65:0x02cb, B:66:0x02d3, B:68:0x02da, B:69:0x02dd, B:73:0x02e0, B:76:0x02ea, B:77:0x02ed, B:79:0x03d3, B:81:0x03dd, B:86:0x03f8, B:92:0x040a, B:94:0x041d, B:102:0x051b, B:105:0x0526, B:115:0x054f, B:117:0x040e, B:122:0x02ff, B:126:0x039e, B:127:0x0598, B:96:0x046b, B:99:0x047a, B:107:0x047f, B:109:0x04ab, B:111:0x04c1), top: B:55:0x023e, inners: #2 }] */
    /* JADX WARN: Removed duplicated region for block: B:118:0x0409  */
    /* JADX WARN: Removed duplicated region for block: B:46:0x0190 A[FALL_THROUGH] */
    /* JADX WARN: Removed duplicated region for block: B:91:0x0407  */
    /* JADX WARN: Removed duplicated region for block: B:93:0x040d  */
    /* JADX WARN: Removed duplicated region for block: B:98:0x0475  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    final void c(o.eg.b r20) {
        /*
            Method dump skipped, instructions count: 1598
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.b.c(o.eg.b):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void v(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 596
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.b.v(char, int, int, java.lang.Object[]):void");
    }
}
