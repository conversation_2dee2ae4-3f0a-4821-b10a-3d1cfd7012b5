package bc.org.bouncycastle.math.ec.tools;

import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u1;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.TreeSet;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\tools\TraceOptimizer.smali */
public class TraceOptimizer {
    private static final BigInteger a = BigInteger.valueOf(1);
    private static final SecureRandom b = new SecureRandom();

    private static int a(ECFieldElement eCFieldElement) {
        int fieldSize = eCFieldElement.getFieldSize();
        int a2 = 31 - e5.a(fieldSize);
        ECFieldElement eCFieldElement2 = eCFieldElement;
        int i = 1;
        while (a2 > 0) {
            eCFieldElement2 = eCFieldElement2.squarePow(i).add(eCFieldElement2);
            a2--;
            i = fieldSize >>> a2;
            if ((i & 1) != 0) {
                eCFieldElement2 = eCFieldElement2.square().add(eCFieldElement);
            }
        }
        if (eCFieldElement2.isZero()) {
            return 0;
        }
        if (eCFieldElement2.isOne()) {
            return 1;
        }
        throw new IllegalStateException("Internal error in trace calculation");
    }

    public static void implPrintNonZeroTraceBits(ECCurve eCCurve) {
        int fieldSize = eCCurve.getFieldSize();
        ArrayList arrayList = new ArrayList();
        for (int i = 0; i < fieldSize; i++) {
            if ((i & 1) != 0 || i == 0) {
                if (a(eCCurve.fromBigInteger(a.shiftLeft(i))) != 0) {
                    arrayList.add(e5.c(i));
                    System.out.print(" " + i);
                }
            } else if (arrayList.contains(e5.c(i >>> 1))) {
                arrayList.add(e5.c(i));
                System.out.print(" " + i);
            }
        }
        System.out.println();
        for (int i2 = 0; i2 < 1000; i2++) {
            BigInteger bigInteger = new BigInteger(fieldSize, b);
            int a2 = a(eCCurve.fromBigInteger(bigInteger));
            int i3 = 0;
            for (int i4 = 0; i4 < arrayList.size(); i4++) {
                if (bigInteger.testBit(((Integer) arrayList.get(i4)).intValue())) {
                    i3 ^= 1;
                }
            }
            if (a2 != i3) {
                throw new IllegalStateException("Optimized-trace sanity check failed");
            }
        }
    }

    public static void main(String[] strArr) {
        TreeSet<String> treeSet = new TreeSet(a(c4.a()));
        treeSet.addAll(a(u1.a()));
        for (String str : treeSet) {
            e8 b2 = u1.b(str);
            if (b2 == null) {
                b2 = c4.b(str);
            }
            if (b2 != null) {
                ECCurve c = b2.c();
                if (ECAlgorithms.isF2mCurve(c)) {
                    System.out.print(str + ":");
                    implPrintNonZeroTraceBits(c);
                }
            }
        }
    }

    public static void printNonZeroTraceBits(ECCurve eCCurve) {
        if (!ECAlgorithms.isF2mCurve(eCCurve)) {
            throw new IllegalArgumentException("Trace only defined over characteristic-2 fields");
        }
        implPrintNonZeroTraceBits(eCCurve);
    }

    private static ArrayList a(Enumeration enumeration) {
        ArrayList arrayList = new ArrayList();
        while (enumeration.hasMoreElements()) {
            arrayList.add(enumeration.nextElement());
        }
        return arrayList;
    }
}
