package com.google.android.play.core.install.model;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/* compiled from: com.google.android.play:app-update@@2.1.0 */
@Retention(RetentionPolicy.CLASS)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\install\model\AppUpdateType.smali */
public @interface AppUpdateType {
    public static final int FLEXIBLE = 0;
    public static final int IMMEDIATE = 1;
}
