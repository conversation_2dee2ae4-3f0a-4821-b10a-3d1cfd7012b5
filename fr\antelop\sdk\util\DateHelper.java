package fr.antelop.sdk.util;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\util\DateHelper.smali */
public final class DateHelper {
    private static final String FULL_DATE_FORMAT = "d MMMM yyyy HH'h'mm";
    private static final String LASTWEEK_DATE_FORMAT = "EEEE HH'h'mm";
    private static final String TIME_FORMAT = "HH'h'mm";
    private static int $10 = 0;
    private static int $11 = 1;
    private static int c = 0;
    private static int h = 1;
    private static char d = 29657;
    private static char a = 50332;
    private static char b = 46877;
    private static char e = 11587;

    /* JADX WARN: Code restructure failed: missing block: B:36:0x0067, code lost:
    
        if (r0.get(1) == r7.get(1)) goto L12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.lang.String formatToYesterdayOrToday(java.util.Date r11, java.lang.String r12, java.lang.String r13) {
        /*
            Method dump skipped, instructions count: 342
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.util.DateHelper.formatToYesterdayOrToday(java.util.Date, java.lang.String, java.lang.String):java.lang.String");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.util.DateHelper.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
