package o.dc;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import o.ee.g;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final Object a;
    private static final Map<String, d> b;
    private static List<Long> c;
    private static long d;
    private static char[] e;
    private static int f;
    private static int h;

    static void a() {
        char[] cArr = new char[1281];
        ByteBuffer.wrap(",¯\u0002ßp/§\u0084\u00957ËI:ôh\u0019_\u0086\u008d=ã\u000fÒä\u0000\u0017w©¥Ô\u009byÊæ8\u001do©]Æ³|â\u0091Ð8\u0006WuÛ«l\u009a\u0093È<>fmîC\u0019²´à;ÖH\u0005å{:ª©\u0098ÏÎt=\u0093\u0013\fB¿°ÄækÕ\u009a\u000b8yB¨»\u009egÍ\u0082#5\u0011L@ÿ¶\u0014å²ÛÔ\t]xä®\u000e\u009d«óÊ!B\u0010\u009cF0µºëÞÙ`\b\u0082~<¬N,¯\u0002ßp/§\u0084\u00957ËI:ôh\u0019_\u0086\u008d=ã\u000fÒä\u0000\u0017w©¥Ô\u009byÊæ8\u001do©]Æ³|â\u0091Ð8\u0006WuÛ«l\u009a\u0093È<>fmîC\u0019²´à;ÖH\u0005å{:ª©\u0098ÏÎt=\u0093\u0013\fB¿°ÄækÕ\u009a\u000b8yB¨»\u009ezÍ\u0099#(\u0011F@ò¶\u0004å\u008eÛÛ\tFxù®\b\u009d£óÐ!~\u0010\u0090F!µ ëÂÙo\bº~0¬Y\u0083âÂ\u0087ìÆ\u009esI\u0097{\t%LÔû\u0086\u0002±\u0091c:\r\\<úî\u0013\u0099ªKÀue$ÚÖ\u0012\u0081±³Ú]`\f\u0086>=,¹\u0002Èps§\u0096\u00950ËN:åh;_\u0086\u008d9ãHÒã\u0000\u0010w¾¥Ð\u009baÊà8\u0002o¯]\u008d³0âÝÐ|\u0006\u0015uÇ«b\u009a\u0095È,>_môC\u0012²´à=ÖD\u0005î{\u000bª\u0094\u0098ØÎb=\u0086\u0013\bBª°Äæ?ÕÙ\u000e¢ ÓRh\u0085\u008d·+éU\u0018þJ }\u009d¯\"ÁSðø\"\u000bU¥\u0087Ë¹zèû\u001a\u0019M´\u007f\u0096\u0091+ÀÆòg$\u000eWç\u0089x¸\u009bê<\u001cNOãaJ\u0090ºÂ=ô\u0016'øY\u000b\u0088«ºÊìn\u001fÎ18`\u0085\u0092ÕÄp÷\u00ad)$[@\u008aë¼qï\u0082\u0001z3Xbð\u0094\tÇ§ù\u008e+|Zù\u008c\u000e¿·ÑÄ\u0003o2\u0089d/\u0097¦Éßûu*\u0090\\\u000f\u008eC¡ùÓ\u001d\u0002\u009341f_\u0099¤ËB~¹Pß\"bõ¤Ç/\u0099Fhï:\u0006\r\u00adß.±_\u0080ñR\u0005%³÷èÉm\u0098êj\u0013=°\u000fÛám°\u008b\u00822TK'ÑùtÈ\u0085\u009arl\u0003?ª\u0011\bà\u00ad²~\u0084IWâ)\u001bø\u00adÊÁ\u009c\u007foÂA\u0010\u0010µâÂ´{\u0087\u0088Y#+EúãÌj\u009f\u0093q9C\\\u0012®ä\u0018·£\u0089Å[W*éü\u0002Ï·¡ÜsoB\u0082,®\u0002Èpu§³\u00958ËQ:øh\u0011_º\u008d9ãHÒæ\u0000\u0012w¤¥ÿ\u009bzÊý8\u0004o§]Ì³zâ\u009cÐ%\u0006\\uÆ«c\u009a\u0092Èe>\u0014m½C\u0002²ºà$ÖH\u0005¡{\u0016ª\u00ad\u0098ÔÎr=\u009e\u0013\u0010Bí°ÏæjÕ\u008d\u000b4yW¨ü\u009ejÍ\u008c#5\u0011L@ö¶\u0013åñÛÔ\t[xè®A\u009d·óÜ!z\u0010\u0098F&µ½ëÈÙs\b\u0080~==\u0007\u0013laÉ¶)\u0084\u009aÚÕ+Dy¨N-\u009c\u0092òéÃL\u0011\u009af\u001f´h\u008aÑÛB)©~\u000fLi¢Àó9Á\u0093\u0017ödwº\u0088\u008be,ª\u0002Ápd§\u0084\u00957Ëx:éh\u0005_\u0080\u008d?ãDÒá\u00007w²¥Å\u009b|Êï8\u0004o¢]Ä³mâ\u0094Ð>\u0006[uÚ«%\u009aÈÈe>\u0014m½C2²¹à,ÖL\u0005ï{\fª·\u0098ÚÎ1,é\u0002ãpn§\u0091\u00950Ë[:øh\u0016_\u0088\u008d9ãHÒê\u0000\u0017w®,®\u0002Èpu§ \u0095!ËM:øh\u0007_\u008c\u008d)ãoÒê\u0000\rw´¥×\u009b|Êê8\foµ]Ì³vâ\u0093Ð\"\u0006\u001du\u0080«-\u009aÌÈe>VmïC\u0018²²à ÖC\u0005ò{#ª°\u0098ÑÎe=\u0090\u0013\u001bB÷°\u0081æ-J\u0096d®\u0016^\n\u0081(-\u0006Ktö£#\u0091¢ÏÎ>{l\u0084[\u000f\u0089ªçìÖi\u0004\u008es7¡T\u009fÿÎi<\u008fk6YO·õæ\u0010Ô¡\u0002\u009eq\u0003¯®\u009eOÌæ:ôiqG\u0086¶?ä¬ÒÇ\u0001a\u007f\u0087®.\u009cWÊý9\u0018\u0017Ê,é\u0002Äpr§Å,§\u0002Âpu§Å,¬\u0002Õpq§\u008c\u0095+ËX:õ,®\u0002Èpu§³\u00958ËQ:øh\u0011_§\u008d\"ãUÒì\u0000\u001fw´¥Ò\u009btÊý8\u0004o®]Ë³jâÕÐx\u0006\u0015u\u0084«-\u009a¯È*>MmôC\u0017²¼à*ÖL\u0005õ{\fª¶\u0098ÓÎ1,¿\u0002Ìpm§\u008c\u0095=ö\u008eØèªU}\u008bO\u0016\u0011iàØ²3\u0085 W\u000e9`\bÑÚ0\u00ad\u0092\u007fÿÜ=ò[\u0080æW8e¥;ÚÊk\u0098\u0080¯\u0013}½\u0013Ó\"bð\u0083\u0087!ULkõ:2È×\u009fr\u00ad\u001bCª\u0012> ·öÊ\u0085V[Ðj\u001d8¢ÎÃ\u009dh³\u008bB%\u0010»&Êõ{\u008b\u0099Z$hc>çÍ\u0015ã\u0089²?@U\u0016ó%Jû¯\u0089ÎXtnÿ=\u001fÓ¶áÏ°*F\u0087\u0015,+\u0006ùÙ\u0088\u007f^\u0091m>\u0003O,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009b5Ê¤8Mo´]Ë³râ\u0093Ð>\u0006BuÇ«-\u009a\u008fÈ*>MmôC\u0017²¼à*ÖL\u0005õ{\fª¶\u0098ÓÎ1=\u009c\u0013\rBí°\u009bæ%,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009b5Ê¤8Mo\u0091]Ä³kâ\u008eÐ8\u0006[uÎ«-\u009a\u0085È >ZmïC\b²¥à=ÖH\u0005å{!ª¸\u0098ÉÎp=Ï\u0013I,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009b5Ê¤8Mo\u0094]Ë³xâ\u009fÐ=\u0006Pu\u0089«y\u009a\u008eÈe>ImüC\u0003²¦à,Ö\r\u0005Ë{6ª¶\u0098ÓÎ^=\u0097\u0013\u0003B¨°ÂæqÕÙ\u000b;yC¨ú\u009edÍÍ#%\u0011@@ú¶\u000få¨ÛÅ\t]xè®\u0005\u009d\u0081óØ!i\u0010\u0090Foµé,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009b5Ê¤8Mo\u0097]Ä³uâ\u0088Ð4\u0006\u0015uÏ«b\u009a\u0093Èe>RmøC\b²ïài,é\u0002Äpr§Å\u00957ËR:åhU_\u0088\u008dmãRÒñ\u0000\u000bw´¥ß\u009br,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009bfÊ¡8D,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009bfÊ¡8Doá]\u0088³9â\u00adÐ$\u0006YuÅ«C\u009a\u008eÈ1>PmûC\u0018²¶à(ÖY\u0005è{\nª·\u0098ðÎt=\u0086\u0013\u001aB¬°Ææ`ÕÙ\u000b*y\u001e¨µ\u009e`Í\u0089#$\u0011K@í¶\u0014å·ÛÜ\tLxÿ®[\u009dåó\u009e,î\u0002\u008dp`§\u0089\u0095+ËX:ðh\u0011_\u0090\u008dmãHÒë\u0000Yw¾¥Ð\u009bvÊá8\b,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009bfÊ¡8Doá]\u0088³9â\u00adÐ0\u0006GuÚ«d\u009a\u008fÈ\">\u0019mùC\u0014²¶à;ÖT\u0005ñ{\u0011ª¼\u0098ÙÎU=\u0094\u0013\u001dB¬°\u009bæ%EËk\u00ad\u0019\u0010ÎÎüS¢,S\u009d\u0001v6åäK\u008a%»\u0094iu\u001e×Ìºò\u0003£ÄQ!\u0006\u00844íÚ\\\u008bÍ¹Zo1\u001c®Â\u0004óá¡\u0000W(\u0004\u0097*4ÛÀ\u0089M¿:l\u0097\u0012eÃ\u009cñ\u0092§'Tÿzb+çÙ¦\u008f\n¼ùb[\u0010 ÁÐ÷\n¤úJKx-)Üß|\u008cÑ²³`>\u0011\u0091ÇtôÔ\u009a¹H\u001cyÐ/QÜØ\u0082©°^a ,®\u0002Èpu§«\u00956ËI:øh\u0013_\u0080\u008d.ã@Òñ\u0000\u0010w²¥ß\u009bfÊ¡8Doá]\u0088³9â«Ð0\u0006YuÜ«h\u009aÁÈ#>VmïCQ²¾à,ÖT\u0005»{ENX`3\u0012\u0096Åv÷Å©ïX\"\nä=pïÑ\u0081¼°\u0000bç\u0015JÇ'ù\u0080¨\u001eZû\r\u0013?\u0019Ñ\u0084\u0080{²Êd¡\u00172É\u009cørªÃ\\¢\u000f\u0000!íÐTzÅT®&\u000bñëÃX\u009d\u0013l\u0092>v\tÈÛMµ:\u0084\u0083Vp!Ûó½Í\u001b\u009c\u0092nk9Á\u000b¤å\u0005,ª\u0002Ápd§\u0084\u0095+Ën:åh\u001c_\u008a\u008d&ãXÒË\u0000\u0016w©¥Ø\u009bsÊà8\u000eo ]Ñ³pâ\u0092Ð?\u0006FÜyò\u0010\u0080«½\u0095\u0093÷a«OÂD3jJ\u0018ýÏ\u0017ý¥£ÆA\u0081oô\u001dOÊ¯ø\u0006¦\u007fWÕ\u000502\u0086à\u0007\u008e~¿Ë,¬\u0002Õpq§\u008c\u0095+ËD,¤\u0002Èpr§\u0096\u00958ËZ:ô".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1281);
        e = cArr;
        d = 4232145448943551149L;
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 213;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = 105 - r7
            byte[] r0 = o.dc.c.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r6
            r7 = r8
            r3 = r2
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r6 = r6 + 1
            int r8 = r8 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.k(short, byte, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        a();
        SystemClock.elapsedRealtimeNanos();
        View.resolveSize(0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        SystemClock.currentThreadTimeMillis();
        ViewConfiguration.getScrollFriction();
        ViewConfiguration.getScrollBarFadeDuration();
        View.getDefaultSize(0, 0);
        TextUtils.indexOf((CharSequence) "", '0', 0);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ViewConfiguration.getScrollBarFadeDuration();
        View.resolveSizeAndState(0, 0, 0);
        TextUtils.indexOf("", "");
        a = new Object();
        b = new HashMap();
        int i = f + 71;
        h = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    private static SharedPreferences c(Context context) {
        int i = f + Opcodes.LSHR;
        h = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        j((char) TextUtils.getTrimmedLength(""), View.MeasureSpec.getSize(0), 71 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        int i3 = h + 81;
        f = i3 % 128;
        int i4 = i3 % 2;
        return sharedPreferences;
    }

    private static SharedPreferences i(Context context) {
        int i = f + 53;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                j((char) Drawable.resolveOpacity(0, 0), (ViewConfiguration.getPressedStateDuration() >> 16) + 70, 72 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
                return context.getSharedPreferences(((String) objArr[0]).intern(), 0);
            default:
                Object[] objArr2 = new Object[1];
                j((char) Drawable.resolveOpacity(1, 1), Opcodes.LREM >> (ViewConfiguration.getPressedStateDuration() % 63), 55 << (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr2);
                return context.getSharedPreferences(((String) objArr2[0]).intern(), 1);
        }
    }

    static boolean a(Context context, d dVar) {
        boolean commit;
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            j((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 60957), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + Opcodes.F2L, 23 - ((Process.getThreadPriority(0) + 20) >> 6), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            j((char) TextUtils.indexOf("", "", 0, 0), Color.red(0) + Opcodes.IF_ICMPLE, (ViewConfiguration.getTapTimeout() >> 16) + 45, objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(dVar).toString());
            try {
                commit = c(context).edit().putString(String.valueOf(dVar.d()), new o.dd.e(context).a(a(dVar).b())).commit();
                if (commit) {
                    b.put(String.valueOf(dVar.d()), dVar);
                }
            } catch (o.eg.d e2) {
                g.c();
                Object[] objArr3 = new Object[1];
                j((char) (60958 - Color.alpha(0)), 141 - TextUtils.indexOf("", ""), ImageFormat.getBitsPerPixel(0) + 24, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr4 = new Object[1];
                j((char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 8731), 208 - TextUtils.lastIndexOf("", '0', 0, 0), 77 - TextUtils.getCapsMode("", 0, 0), objArr4);
                g.a(intern2, sb2.append(((String) objArr4[0]).intern()).append(dVar.toString()).toString(), e2);
                return false;
            }
        }
        return commit;
    }

    static void c(Context context, Long l) {
        synchronized (a) {
            List<Long> g = g(context);
            g.add(l);
            e(context, g);
        }
    }

    public static void d(Context context, Long l) {
        synchronized (a) {
            List<Long> g = g(context);
            g.remove(l);
            e(context, g);
        }
    }

    static Set<d> a(Context context) {
        HashSet hashSet;
        synchronized (a) {
            hashSet = new HashSet();
            List<Long> g = g(context);
            if (g.isEmpty()) {
                g.c();
                Object[] objArr = new Object[1];
                j((char) (60959 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 140 - MotionEvent.axisFromString(""), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 22, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                j((char) (21015 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), Color.green(0) + 286, (ViewConfiguration.getPressedStateDuration() >> 16) + 63, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
            } else {
                g.c();
                Object[] objArr3 = new Object[1];
                j((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 60957), 141 - View.getDefaultSize(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0) + 24, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                j((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 349, TextUtils.lastIndexOf("", '0') + 70, objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                Iterator<Long> it = g.iterator();
                while (it.hasNext()) {
                    d b2 = b(new o.dd.e(context), c(context), String.valueOf(it.next()));
                    if (b2 != null) {
                        hashSet.add(b2);
                    }
                }
            }
        }
        return hashSet;
    }

    static boolean b(Context context, d dVar) {
        boolean contains;
        synchronized (a) {
            contains = c(context).contains(String.valueOf(dVar.d()));
        }
        return contains;
    }

    public static void b(Context context) {
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            j((char) (60958 - Drawable.resolveOpacity(0, 0)), 140 - ((byte) KeyEvent.getModifierMetaStateMask()), Gravity.getAbsoluteGravity(0, 0) + 23, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            j((char) (Color.blue(0) + 4525), Color.rgb(0, 0, 0) + 16777634, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 27, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            o.dd.e eVar = new o.dd.e(context);
            SharedPreferences c2 = c(context);
            Set<d> c3 = c(eVar, c2, h.values());
            g.c();
            Object[] objArr3 = new Object[1];
            j((char) (TextUtils.lastIndexOf("", '0', 0) + 60959), 141 - (ViewConfiguration.getJumpTapTimeout() >> 16), 23 - TextUtils.getCapsMode("", 0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr4 = new Object[1];
            j((char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), (ViewConfiguration.getLongPressTimeout() >> 16) + 445, Gravity.getAbsoluteGravity(0, 0) + 39, objArr4);
            StringBuilder append = sb.append(((String) objArr4[0]).intern()).append(c3.size());
            Object[] objArr5 = new Object[1];
            j((char) KeyEvent.keyCodeFromString(""), View.MeasureSpec.makeMeasureSpec(0, 0) + 484, 14 - TextUtils.indexOf("", ""), objArr5);
            g.d(intern2, append.append(((String) objArr5[0]).intern()).toString());
            a(context, c2, c3, true);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x00f9  */
    /* JADX WARN: Removed duplicated region for block: B:14:0x00ff  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x0181  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0187  */
    /* JADX WARN: Removed duplicated region for block: B:22:0x0189  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x0183  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x00fb  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static java.util.Set<o.dc.d> c(o.dd.e r16, android.content.SharedPreferences r17, o.dc.h... r18) {
        /*
            Method dump skipped, instructions count: 542
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.c(o.dd.e, android.content.SharedPreferences, o.dc.h[]):java.util.Set");
    }

    public static Set<d> b(Context context, h... hVarArr) {
        Set<d> b2;
        synchronized (a) {
            b2 = b(new o.dd.e(context), c(context), hVarArr);
        }
        return b2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.Set<o.dc.d> b(o.dd.e r12, android.content.SharedPreferences r13, o.dc.h... r14) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.b(o.dd.e, android.content.SharedPreferences, o.dc.h[]):java.util.Set");
    }

    private static d b(o.dd.e eVar, SharedPreferences sharedPreferences, String str) {
        char c2;
        d c3 = c(eVar, sharedPreferences, str);
        char c4 = 'a';
        if (c3 == null) {
            c2 = 'a';
        } else {
            c2 = 'Y';
        }
        switch (c2) {
            case Opcodes.DUP /* 89 */:
                int i = f + 73;
                h = i % 128;
                int i2 = i % 2;
                if (c3.b()) {
                    c4 = '\b';
                }
                switch (c4) {
                    case Opcodes.LADD /* 97 */:
                        int i3 = f + 75;
                        h = i3 % 128;
                        int i4 = i3 % 2;
                        return c3;
                    default:
                        return null;
                }
            default:
                return null;
        }
    }

    private static d c(o.dd.e eVar, SharedPreferences sharedPreferences, String str) {
        Object[] objArr = new Object[1];
        j((char) (Color.alpha(0) + 60958), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + Opcodes.F2D, Color.green(0) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        j((char) (TextUtils.indexOf("", "") + 55840), 646 - Color.argb(0, 0, 0, 0), 15 - TextUtils.indexOf("", "", 0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Map<String, d> map = b;
        d dVar = map.get(str);
        switch (dVar != null) {
            case false:
                Object obj = sharedPreferences.getAll().get(str);
                if (obj == null) {
                    g.c();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr3 = new Object[1];
                    j((char) (ViewConfiguration.getFadingEdgeLength() >> 16), 722 - View.resolveSize(0, 0), 44 - (KeyEvent.getMaxKeyCode() >> 16), objArr3);
                    g.e(intern, sb.append(((String) objArr3[0]).intern()).append(str).toString());
                    int i = h + 35;
                    f = i % 128;
                    int i2 = i % 2;
                    return null;
                }
                if (!(obj instanceof String)) {
                    g.c();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    j((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), 871 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0', 0) + 34, objArr4);
                    StringBuilder append = sb2.append(((String) objArr4[0]).intern()).append(str);
                    Object[] objArr5 = new Object[1];
                    j((char) (ViewConfiguration.getTouchSlop() >> 8), 904 - TextUtils.lastIndexOf("", '0', 0), 16 - ExpandableListView.getPackedPositionType(0L), objArr5);
                    g.e(intern, append.append(((String) objArr5[0]).intern()).toString());
                    return null;
                }
                String d2 = eVar.d((String) obj);
                try {
                    g.c();
                    StringBuilder sb3 = new StringBuilder();
                    Object[] objArr6 = new Object[1];
                    j((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 766, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 40, objArr6);
                    g.d(intern, sb3.append(((String) objArr6[0]).intern()).append(d2).toString());
                    d e2 = e(new o.eg.b(d2));
                    map.put(str, e2);
                    return e2;
                } catch (o.eg.d e3) {
                    g.c();
                    StringBuilder sb4 = new StringBuilder();
                    Object[] objArr7 = new Object[1];
                    j((char) (ViewConfiguration.getTapTimeout() >> 16), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 807, 64 - TextUtils.lastIndexOf("", '0', 0), objArr7);
                    g.a(intern, sb4.append(((String) objArr7[0]).intern()).append(d2).toString(), e3);
                    return null;
                }
            default:
                int i3 = h + 61;
                f = i3 % 128;
                if (i3 % 2 == 0) {
                }
                g.c();
                Object[] objArr8 = new Object[1];
                j((char) (61586 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), AndroidCharacter.getMirror('0') + 613, (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 60, objArr8);
                g.d(intern, ((String) objArr8[0]).intern());
                return dVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.Set<o.dc.d> d(o.dd.e r18, android.content.SharedPreferences r19) {
        /*
            Method dump skipped, instructions count: 546
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.d(o.dd.e, android.content.SharedPreferences):java.util.Set");
    }

    public static void e(Context context, Set<d> set) {
        g.c();
        Object[] objArr = new Object[1];
        j((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 60958), 141 - TextUtils.getCapsMode("", 0, 0), View.MeasureSpec.makeMeasureSpec(0, 0) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j((char) (25378 - AndroidCharacter.getMirror('0')), 1165 - ImageFormat.getBitsPerPixel(0), View.resolveSize(0, 0) + 32, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        synchronized (a) {
            a(context, c(context), set, false);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void a(android.content.Context r6, android.content.SharedPreferences r7, java.util.Set<o.dc.d> r8, boolean r9) {
        /*
            int r0 = o.dc.c.h
            int r0 = r0 + 57
            int r1 = r0 % 128
            o.dc.c.f = r1
            int r0 = r0 % 2
            android.content.SharedPreferences$Editor r7 = r7.edit()
            java.util.List r0 = g(r6)
            java.util.Iterator r8 = r8.iterator()
        L16:
            boolean r1 = r8.hasNext()
            r2 = 17
            if (r1 == 0) goto L20
            r1 = r2
            goto L22
        L20:
            r1 = 24
        L22:
            switch(r1) {
                case 24: goto L36;
                default: goto L25;
            }
        L25:
            java.lang.Object r1 = r8.next()
            o.dc.d r1 = (o.dc.d) r1
            long r3 = r1.d()
            java.lang.String r3 = java.lang.String.valueOf(r3)
            if (r9 != 0) goto L3f
            goto L3d
        L36:
            e(r6, r0)
            r7.commit()
            return
        L3d:
            r4 = 0
            goto L40
        L3f:
            r4 = 1
        L40:
            switch(r4) {
                case 1: goto L51;
                default: goto L43;
            }
        L43:
            long r4 = r1.d()
            java.lang.Long r4 = java.lang.Long.valueOf(r4)
            boolean r4 = r0.contains(r4)
            if (r4 != 0) goto L16
        L51:
            r7.remove(r3)
            java.util.Map<java.lang.String, o.dc.d> r4 = o.dc.c.b
            r4.remove(r3)
            long r3 = r1.d()
            java.lang.Long r1 = java.lang.Long.valueOf(r3)
            r0.remove(r1)
            int r1 = o.dc.c.h
            int r1 = r1 + r2
            int r2 = r1 % 128
            o.dc.c.f = r2
            int r1 = r1 % 2
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.a(android.content.Context, android.content.SharedPreferences, java.util.Set, boolean):void");
    }

    public static void e(Context context) {
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            j((char) ((KeyEvent.getMaxKeyCode() >> 16) + 60958), 141 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), TextUtils.indexOf("", "", 0, 0) + 23, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            j((char) (22127 - View.combineMeasuredStates(0, 0)), 1198 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 21 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            d(c(context));
        }
    }

    private static void d(SharedPreferences sharedPreferences) {
        int i = f + 45;
        h = i % 128;
        switch (i % 2 != 0) {
            case true:
                sharedPreferences.edit().clear().commit();
                b.clear();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                sharedPreferences.edit().clear().commit();
                b.clear();
                int i2 = h + Opcodes.LNEG;
                f = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    static void d(Context context) {
        synchronized (a) {
            g.c();
            Object[] objArr = new Object[1];
            j((char) (View.MeasureSpec.makeMeasureSpec(0, 0) + 60958), 140 - ImageFormat.getBitsPerPixel(0), (ViewConfiguration.getScrollBarSize() >> 8) + 23, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            j((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1219, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 24, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            List<Long> list = c;
            if (list != null) {
                list.clear();
            }
            SharedPreferences.Editor edit = i(context).edit();
            Object[] objArr3 = new Object[1];
            j((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 61657), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1243, 3 - View.resolveSize(0, 0), objArr3);
            edit.remove(((String) objArr3[0]).intern()).commit();
        }
    }

    private static List<Long> g(Context context) {
        synchronized (a) {
            if (c == null) {
                ArrayList arrayList = new ArrayList();
                try {
                    SharedPreferences i = i(context);
                    Object[] objArr = new Object[1];
                    j((char) (61657 - Color.alpha(0)), (ViewConfiguration.getScrollBarSize() >> 8) + 1243, Process.getGidForName("") + 4, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    j((char) ((ViewConfiguration.getTapTimeout() >> 16) + 37127), 1246 - (ViewConfiguration.getTapTimeout() >> 16), 2 - Drawable.resolveOpacity(0, 0), objArr2);
                    String string = i.getString(intern, ((String) objArr2[0]).intern());
                    if (!string.isEmpty()) {
                        o.eg.e eVar = new o.eg.e(string);
                        for (int i2 = 0; i2 < eVar.d(); i2++) {
                            arrayList.add(eVar.a(i2));
                        }
                    }
                    return arrayList;
                } catch (o.eg.d e2) {
                    SharedPreferences.Editor edit = i(context).edit();
                    Object[] objArr3 = new Object[1];
                    j((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 61657), 1242 - TextUtils.indexOf((CharSequence) "", '0', 0), 3 - ExpandableListView.getPackedPositionGroup(0L), objArr3);
                    edit.remove(((String) objArr3[0]).intern()).commit();
                    arrayList.clear();
                    c = arrayList;
                }
            }
            return c;
        }
    }

    private static void e(Context context, List<Long> list) {
        int i = f + Opcodes.DNEG;
        h = i % 128;
        int i2 = i % 2;
        c = list;
        o.eg.e e2 = o.e(list);
        SharedPreferences.Editor edit = i(context).edit();
        Object[] objArr = new Object[1];
        j((char) (61656 - ((byte) KeyEvent.getModifierMetaStateMask())), 1244 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 4 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
        edit.putString(((String) objArr[0]).intern(), e2.a()).commit();
        int i3 = f + 7;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    private static o.eg.b a(d dVar) throws o.eg.d {
        long j;
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        j((char) (19723 - (Process.myTid() >> 22)), ((Process.getThreadPriority(0) + 20) >> 6) + 1248, 2 - ((Process.getThreadPriority(0) + 20) >> 6), objArr);
        bVar.d(((String) objArr[0]).intern(), dVar.d());
        Object[] objArr2 = new Object[1];
        j((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 26772), 1250 - (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getLongPressTimeout() >> 16) + 6, objArr2);
        bVar.d(((String) objArr2[0]).intern(), dVar.a().ordinal());
        Object[] objArr3 = new Object[1];
        j((char) (ExpandableListView.getPackedPositionChild(0L) + 27948), 1256 - (Process.myPid() >> 22), 12 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
        bVar.d(((String) objArr3[0]).intern(), dVar.e().getTime());
        Object[] objArr4 = new Object[1];
        j((char) (ExpandableListView.getPackedPositionChild(0L) + 1), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1267, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 6, objArr4);
        String intern = ((String) objArr4[0]).intern();
        switch (dVar.c() != null ? '_' : 'Y') {
            case Opcodes.DUP /* 89 */:
                j = -1;
                break;
            default:
                int i = h + 45;
                f = i % 128;
                if (i % 2 == 0) {
                }
                j = dVar.c().getTime();
                break;
        }
        bVar.d(intern, j);
        Object[] objArr5 = new Object[1];
        j((char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), KeyEvent.getDeadChar(0, 0) + 1274, (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 7, objArr5);
        bVar.d(((String) objArr5[0]).intern(), dVar.g());
        int i2 = h + 7;
        f = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    private static d e(o.eg.b bVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        j((char) (TextUtils.getTrimmedLength("") + 19723), KeyEvent.normalizeMetaState(0) + 1248, 2 - (Process.myTid() >> 22), objArr);
        long longValue = bVar.m(((String) objArr[0]).intern()).longValue();
        h[] values = h.values();
        Object[] objArr2 = new Object[1];
        j((char) (View.resolveSize(0, 0) + 26773), 1250 - KeyEvent.normalizeMetaState(0), Drawable.resolveOpacity(0, 0) + 6, objArr2);
        h hVar = values[bVar.i(((String) objArr2[0]).intern()).intValue()];
        Object[] objArr3 = new Object[1];
        j((char) (27947 - ExpandableListView.getPackedPositionGroup(0L)), 1256 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (Process.myTid() >> 22) + 12, objArr3);
        Date date = (Date) Objects.requireNonNull(o.a(bVar.m(((String) objArr3[0]).intern()).longValue()));
        Object[] objArr4 = new Object[1];
        j((char) KeyEvent.getDeadChar(0, 0), 1268 - TextUtils.indexOf("", ""), 6 - (KeyEvent.getMaxKeyCode() >> 16), objArr4);
        Date a2 = o.a(bVar.m(((String) objArr4[0]).intern()).longValue());
        Object[] objArr5 = new Object[1];
        j((char) TextUtils.indexOf("", ""), 1274 - (Process.myTid() >> 22), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 6, objArr5);
        d dVar = new d(longValue, hVar, date, a2, bVar.u(((String) objArr5[0]).intern()));
        int i = h + 27;
        f = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.c.j(char, int, int, java.lang.Object[]):void");
    }
}
