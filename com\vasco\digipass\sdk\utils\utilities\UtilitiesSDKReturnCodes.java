package com.vasco.digipass.sdk.utils.utilities;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\UtilitiesSDKReturnCodes.smali */
public final class UtilitiesSDKReturnCodes {
    public static final int CLIENT_EPHEMERAL_KEY_INCORRECT_LENGTH = -4241;
    public static final int CLIENT_EPHEMERAL_KEY_INVALID = -4239;
    public static final int CLIENT_EPHEMERAL_KEY_NULL_OR_EMPTY = -4240;
    public static final int CLIENT_MESSAGE_INCORRECT_LENGTH = -4251;
    public static final int CLIENT_MESSAGE_NULL_OR_EMPTY = -4250;
    public static final int CLIENT_PRIVATE_KEY_INCORRECT_LENGTH = -4244;
    public static final int CLIENT_PRIVATE_KEY_INVALID = -4242;
    public static final int CLIENT_PRIVATE_KEY_NULL_OR_EMPTY = -4243;
    public static final int CRYPTO_MECANISM_INVALID = -4201;
    public static final int CRYPTO_MODE_INVALID = -4202;
    public static final int GENERATOR_NULL_OR_EMPTY = -4245;
    public static final int IDENTITY_INCORRECT_LENGTH = -4248;
    public static final int IDENTITY_NULL_OR_EMPTY = -4247;
    public static final int INITIAL_VECTOR_INCORRECT_LENGTH = -4207;
    public static final int INITIAL_VECTOR_NULL = -4232;
    public static final int INPUT_DATA_INCORRECT_LENGTH = -4206;
    public static final int INPUT_DATA_NULL = -4205;
    public static final int INPUT_KEY_NULL = -4210;
    public static final int ITERATION_COUNT_INCORRECT = -4212;
    public static final int KEY_INCORRECT_LENGTH = -4204;
    public static final int KEY_NULL = -4203;
    public static final int MODULO_NULL_OR_EMPTY = -4246;
    public static final int OUTPUT_DATA_INCORRECT_LENGTH = -4209;
    public static final int PASSWORD_INCORRECT_LENGTH = -4255;
    public static final int PASSWORD_NULL_OR_EMPTY = -4254;
    public static final int PASSWORD_VERIFIER_INCORRECT_LENGTH = -4257;
    public static final int PASSWORD_VERIFIER_NULL_OR_EMPTY = -4256;
    public static final int SALT_INCORRECT_LENGTH = -4249;
    public static final int SALT_NULL = -4211;
    public static final int SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_INCORRECT_FORMAT = -4230;
    public static final int SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_INCORRECT_LENGTH = -4229;
    public static final int SECURE_CHANNEL_MESSAGE_AUTHENTICATION_TAG_NULL = -4228;
    public static final int SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_FORMAT = -4227;
    public static final int SECURE_CHANNEL_MESSAGE_BODY_INCORRECT_LENGTH = -4226;
    public static final int SECURE_CHANNEL_MESSAGE_BODY_NULL = -4225;
    public static final int SECURE_CHANNEL_MESSAGE_INCORRECT_FORMAT = -4214;
    public static final int SECURE_CHANNEL_MESSAGE_INCORRECT_LENGTH = -4215;
    public static final int SECURE_CHANNEL_MESSAGE_INCORRECT_MESSAGE_TYPE = -4217;
    public static final int SECURE_CHANNEL_MESSAGE_INCORRECT_PROTECTION_TYPE = -4218;
    public static final int SECURE_CHANNEL_MESSAGE_INCORRECT_PROTOCOL_VERSION = -4216;
    public static final int SECURE_CHANNEL_MESSAGE_NONCE_INCORRECT_FORMAT = -4224;
    public static final int SECURE_CHANNEL_MESSAGE_NONCE_INCORRECT_LENGTH = -4223;
    public static final int SECURE_CHANNEL_MESSAGE_NONCE_NULL = -4222;
    public static final int SECURE_CHANNEL_MESSAGE_NULL = -4213;
    public static final int SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_INCORRECT_FORMAT = -4221;
    public static final int SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_INCORRECT_LENGTH = -4220;
    public static final int SECURE_CHANNEL_MESSAGE_SERIAL_NUMBER_NULL = -4219;
    public static final int SECURE_RANDOM_NULL_OR_EMPTY = -4258;
    public static final int SERVER_EPHEMERAL_KEY_INCORRECT_LENGTH = -4235;
    public static final int SERVER_EPHEMERAL_KEY_INVALID = -4233;
    public static final int SERVER_EPHEMERAL_KEY_NULL_OR_EMPTY = -4234;
    public static final int SERVER_MESSAGE_INCORRECT_LENGTH = -4253;
    public static final int SERVER_MESSAGE_NULL_OR_EMPTY = -4252;
    public static final int SERVER_PRIVATE_KEY_INCORRECT_LENGTH = -4238;
    public static final int SERVER_PRIVATE_KEY_INVALID = -4236;
    public static final int SERVER_PRIVATE_KEY_NULL_OR_EMPTY = -4237;
    public static final int SRP_CLIENT_NOT_INITIALIZED = -4259;
    public static final int SRP_SERVER_NOT_INITIALIZED = -4260;
    public static final int SUCCESS = 0;
    public static final int UNKNOWN_ERROR = -4299;
    public static final int WBC_INPUT_ARRAY_INVALID = -4231;
    int a = -4208;

    private UtilitiesSDKReturnCodes() {
    }
}
