package o.co;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.card.EmvApplicationActivationMethodType;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\co\e.smali */
public final class e implements o.ee.a<EmvApplicationActivationMethodType> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final e a;
    public static final e b;
    public static final e c;
    public static final e d;
    public static final e e;
    public static final e g;
    public static final e i;
    private static final /* synthetic */ e[] j;
    private static int k;
    private static byte[] l;
    private static int m;
    private static long n;

    /* renamed from: o, reason: collision with root package name */
    private static int f50o;
    private static int p;
    private static int q;
    private static short[] r;
    private final String f;
    private final boolean h;

    static void b() {
        n = -6260783292171259988L;
        l = new byte[]{121, -19, 76, 2, 0, -46, 7, 2, -16, 3, -8, -17, 40, 8, -15, 2, -28, 28, -6, 2, -26, -43, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 8, -15, 2, -28, 9, 13, 10, 11, -9, -40, 60, -2, -33, -46, 70, 12, -93, 90, -71, -14, 91, -11, -6, 24, -1, 8, -5, -76, 91, -14, -10, -9, -6, -23, 45, 57, -59, 62, Base64.padSymbol, -51, -33, 29, 55, -59, Base64.padSymbol, 62, -33, 35, 62, 34, 47, -114, 118, ByteCompanionObject.MAX_VALUE, -120, -127, -98, 109, -127, 116, 114, 119, -115, 114, -66, 80, -81, 87, 94, -87, -96, 95, 89, -96, -96, 85, 83, 86, -84, 83, -65, 35, -48, -33};
        m = 909053632;
        f50o = 914130404;
        k = 371109379;
    }

    static void init$0() {
        $$a = new byte[]{48, 67, 97, 27};
        $$b = Opcodes.INEG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(short r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 108
            int r9 = r9 * 4
            int r9 = 3 - r9
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r0 = o.co.e.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L35
        L1a:
            r3 = r2
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            int r9 = r9 + 1
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.e.u(short, short, byte, java.lang.Object[]):void");
    }

    private static /* synthetic */ e[] d() {
        int i2 = p + 81;
        int i3 = i2 % 128;
        q = i3;
        int i4 = i2 % 2;
        e[] eVarArr = {b, c, e, d, a, i, g};
        int i5 = i3 + 25;
        p = i5 % 128;
        int i6 = i5 % 2;
        return eVarArr;
    }

    public static e valueOf(String str) {
        int i2 = q + 25;
        p = i2 % 128;
        char c2 = i2 % 2 != 0 ? ',' : (char) 25;
        e eVar = (e) Enum.valueOf(e.class, str);
        switch (c2) {
            case 25:
                throw null;
            default:
                return eVar;
        }
    }

    public static e[] values() {
        int i2 = p + 27;
        q = i2 % 128;
        switch (i2 % 2 != 0 ? '#' : 'C') {
            case '#':
                throw null;
            default:
                return (e[]) j.clone();
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = p + 29;
        q = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case true:
                EmvApplicationActivationMethodType c2 = c();
                int i3 = q + 95;
                p = i3 % 128;
                switch (i3 % 2 == 0 ? 'F' : (char) 19) {
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return c2;
                }
            default:
                c();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        q = 0;
        p = 1;
        b();
        Object[] objArr = new Object[1];
        s("ꥹ\u19dc졇룲此", 45197 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        s("ꥹ堄䮗紪沤", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 61812, objArr2);
        b = new e(intern, 0, ((String) objArr2[0]).intern(), true);
        Object[] objArr3 = new Object[1];
        t((byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 81), (-540123226) - TextUtils.getOffsetAfter("", 0), (short) Gravity.getAbsoluteGravity(0, 0), TextUtils.lastIndexOf("", '0') - 80, (-5475109) - KeyEvent.keyCodeFromString(""), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        s("ꥳ炡\u1ad6␌츾\ue866뎑嶻查ę⭉\uf564鲖ꛌ", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 55764, objArr4);
        c = new e(intern2, 1, ((String) objArr4[0]).intern(), false);
        Object[] objArr5 = new Object[1];
        t((byte) ((Process.myPid() >> 22) - 88), KeyEvent.getDeadChar(0, 0) - 540123213, (short) (KeyEvent.getMaxKeyCode() >> 16), TextUtils.lastIndexOf("", '0', 0, 0) - 80, (-5475105) - View.resolveSizeAndState(0, 0, 0), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        s("ꥯ凚堹", 63659 - Color.argb(0, 0, 0, 0), objArr6);
        e = new e(intern3, 2, ((String) objArr6[0]).intern(), true);
        Object[] objArr7 = new Object[1];
        t((byte) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 28), (-540123210) - View.MeasureSpec.makeMeasureSpec(0, 0), (short) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), (-82) - TextUtils.lastIndexOf("", '0', 0, 0), (-5475122) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        t((byte) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 62), (-540123195) - TextUtils.getOffsetAfter("", 0), (short) TextUtils.indexOf("", "", 0, 0), TextUtils.lastIndexOf("", '0') - 80, TextUtils.getOffsetBefore("", 0) - 5475121, objArr8);
        d = new e(intern4, 3, ((String) objArr8[0]).intern(), false);
        Object[] objArr9 = new Object[1];
        s("\ua97dᖭ킎", ImageFormat.getBitsPerPixel(0) + 48354, objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        t((byte) (View.MeasureSpec.makeMeasureSpec(0, 0) + 64), (-540123178) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (short) (ViewConfiguration.getTapTimeout() >> 16), Drawable.resolveOpacity(0, 0) - 81, (-5475122) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr10);
        a = new e(intern5, 4, ((String) objArr10[0]).intern(), false);
        Object[] objArr11 = new Object[1];
        s("ꥳ穨༊퀽\ue5d7뛬宔沿ぷ앴阚묻", 54050 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        s("ꥳ쁤筲鉙േꐨ\udf3c瘣\ue10b᠊돿⫿䗬", 26893 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr12);
        i = new e(intern6, 5, ((String) objArr12[0]).intern(), true);
        Object[] objArr13 = new Object[1];
        s("ꥵ櫿⸤", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 50101, objArr13);
        String intern7 = ((String) objArr13[0]).intern();
        Object[] objArr14 = new Object[1];
        s("ꥵ\uec85⊰", ((byte) KeyEvent.getModifierMetaStateMask()) + 17904, objArr14);
        g = new e(intern7, 6, ((String) objArr14[0]).intern(), false);
        j = d();
        int i2 = q + 73;
        p = i2 % 128;
        int i3 = i2 % 2;
    }

    private e(String str, int i2, String str2, boolean z) {
        this.f = str2;
        this.h = z;
    }

    public final boolean e() {
        int i2 = q + 75;
        int i3 = i2 % 128;
        p = i3;
        int i4 = i2 % 2;
        boolean z = this.h;
        int i5 = i3 + 13;
        q = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.co.e b(java.lang.String r7) {
        /*
            int r0 = o.co.e.p
            int r0 = r0 + 115
            int r1 = r0 % 128
            o.co.e.q = r1
            int r0 = r0 % 2
            o.co.e[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L11:
            if (r3 >= r1) goto L16
            r4 = 54
            goto L18
        L16:
            r4 = 73
        L18:
            r5 = 1
            switch(r4) {
                case 73: goto L29;
                default: goto L1c;
            }
        L1c:
            int r4 = o.co.e.q
            int r4 = r4 + 49
            int r6 = r4 % 128
            o.co.e.p = r6
            int r4 = r4 % 2
            if (r4 != 0) goto L59
            goto L59
        L29:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            int r3 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r3 = r3 >> 16
            r4 = 65027(0xfe03, float:9.1122E-41)
            int r3 = r3 + r4
            java.lang.Object[] r4 = new java.lang.Object[r5]
            java.lang.String r5 = "ꥯ坋啟卅入彃嵣孌奐䝏䕍䍹䅌佢䵦䭴䤬睺畤獮煮罬紉笗祔杍敒"
            s(r5, r3, r4)
            r2 = r4[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r7 = r1.append(r7)
            java.lang.String r7 = r7.toString()
            r0.<init>(r7)
            throw r0
        L59:
            r4 = r0[r3]
            java.lang.String r6 = r4.f
            boolean r6 = r7.equals(r6)
            if (r6 == 0) goto L65
            r5 = r2
            goto L66
        L65:
        L66:
            switch(r5) {
                case 0: goto L6c;
                default: goto L69;
            }
        L69:
            int r3 = r3 + 1
            goto L11
        L6c:
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.e.b(java.lang.String):o.co.e");
    }

    /* renamed from: o.co.e$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\co\e$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            d = 1;
            int[] iArr = new int[e.values().length];
            e = iArr;
            try {
                iArr[e.e.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[e.b.ordinal()] = 2;
                int i = d;
                int i2 = (i ^ 5) + ((i & 5) << 1);
                b = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[e.a.ordinal()] = 3;
                int i4 = b;
                int i5 = ((i4 | 23) << 1) - (i4 ^ 23);
                d = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[e.i.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[e.g.ordinal()] = 5;
                int i7 = d;
                int i8 = ((i7 | 75) << 1) - (i7 ^ 75);
                b = i8 % 128;
                int i9 = i8 % 2;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[e.c.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[e.d.ordinal()] = 7;
                int i10 = b + 99;
                d = i10 % 128;
                int i11 = i10 % 2;
            } catch (NoSuchFieldError e8) {
            }
        }
    }

    public final EmvApplicationActivationMethodType c() {
        int i2 = p + 75;
        q = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                switch (AnonymousClass4.e[ordinal()]) {
                    case 1:
                        return EmvApplicationActivationMethodType.Sms;
                    case 2:
                        return EmvApplicationActivationMethodType.Email;
                    case 3:
                        return EmvApplicationActivationMethodType.App;
                    case 4:
                        return EmvApplicationActivationMethodType.OutboundCall;
                    case 5:
                        return EmvApplicationActivationMethodType.Ivr;
                    case 6:
                        return EmvApplicationActivationMethodType.OnlineBanking;
                    case 7:
                        EmvApplicationActivationMethodType emvApplicationActivationMethodType = EmvApplicationActivationMethodType.CustomerService;
                        int i3 = q + 41;
                        p = i3 % 128;
                        int i4 = i3 % 2;
                        return emvApplicationActivationMethodType;
                    default:
                        Object[] objArr = new Object[1];
                        t((byte) (103 - ExpandableListView.getPackedPositionType(0L)), Gravity.getAbsoluteGravity(0, 0) - *********, (short) (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.getOffsetAfter("", 0) - 81, (-5475121) - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
                        throw new IllegalArgumentException(String.format(((String) objArr[0]).intern(), name()));
                }
            default:
                int i5 = AnonymousClass4.e[ordinal()];
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void s(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 618
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.e.s(java.lang.String, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:78:0x02b1  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void t(byte r21, int r22, short r23, int r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 898
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.co.e.t(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
