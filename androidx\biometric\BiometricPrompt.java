package androidx.biometric;

import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import java.security.Signature;
import java.util.concurrent.Executor;
import javax.crypto.Cipher;
import javax.crypto.Mac;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt.smali */
public class BiometricPrompt implements BiometricConstants {
    static final String BIOMETRIC_FRAGMENT_TAG = "BiometricFragment";
    private static final boolean DEBUG = false;
    private static final boolean DEBUG_FORCE_FINGERPRINT = false;
    private static final int DELAY_MILLIS = 500;
    static final String DIALOG_FRAGMENT_TAG = "FingerprintDialogFragment";
    static final String FINGERPRINT_HELPER_FRAGMENT_TAG = "FingerprintHelperFragment";
    static final String KEY_ALLOW_DEVICE_CREDENTIAL = "allow_device_credential";
    static final String KEY_DESCRIPTION = "description";
    static final String KEY_HANDLING_DEVICE_CREDENTIAL_RESULT = "handling_device_credential_result";
    static final String KEY_NEGATIVE_TEXT = "negative_text";
    static final String KEY_REQUIRE_CONFIRMATION = "require_confirmation";
    static final String KEY_SUBTITLE = "subtitle";
    static final String KEY_TITLE = "title";
    private static final String TAG = "BiometricPromptCompat";
    private final AuthenticationCallback mAuthenticationCallback;
    private BiometricFragment mBiometricFragment;
    private final Executor mExecutor;
    private FingerprintDialogFragment mFingerprintDialogFragment;
    private FingerprintHelperFragment mFingerprintHelperFragment;
    private Fragment mFragment;
    private FragmentActivity mFragmentActivity;
    private boolean mIsHandlingDeviceCredential;
    private final LifecycleObserver mLifecycleObserver;
    private final DialogInterface.OnClickListener mNegativeButtonListener = new DialogInterface.OnClickListener() { // from class: androidx.biometric.BiometricPrompt.1
        @Override // android.content.DialogInterface.OnClickListener
        public void onClick(DialogInterface dialog, int which) {
            BiometricPrompt.this.mExecutor.execute(new Runnable() { // from class: androidx.biometric.BiometricPrompt.1.1
                @Override // java.lang.Runnable
                public void run() {
                    if (!BiometricPrompt.canUseBiometricFragment() || BiometricPrompt.this.mBiometricFragment == null) {
                        if (BiometricPrompt.this.mFingerprintDialogFragment != null && BiometricPrompt.this.mFingerprintHelperFragment != null) {
                            CharSequence errorText = BiometricPrompt.this.mFingerprintDialogFragment.getNegativeButtonText();
                            BiometricPrompt.this.mAuthenticationCallback.onAuthenticationError(13, errorText != null ? errorText : "");
                            BiometricPrompt.this.mFingerprintHelperFragment.cancel(2);
                            return;
                        }
                        Log.e(BiometricPrompt.TAG, "Negative button callback not run. Fragment was null.");
                        return;
                    }
                    CharSequence errorText2 = BiometricPrompt.this.mBiometricFragment.getNegativeButtonText();
                    BiometricPrompt.this.mAuthenticationCallback.onAuthenticationError(13, errorText2 != null ? errorText2 : "");
                    BiometricPrompt.this.mBiometricFragment.cleanup();
                }
            });
        }
    };
    private boolean mPausedOnce;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt$CryptoObject.smali */
    public static class CryptoObject {
        private final Cipher mCipher;
        private final Mac mMac;
        private final Signature mSignature;

        public CryptoObject(Signature signature) {
            this.mSignature = signature;
            this.mCipher = null;
            this.mMac = null;
        }

        public CryptoObject(Cipher cipher) {
            this.mCipher = cipher;
            this.mSignature = null;
            this.mMac = null;
        }

        public CryptoObject(Mac mac) {
            this.mMac = mac;
            this.mCipher = null;
            this.mSignature = null;
        }

        public Signature getSignature() {
            return this.mSignature;
        }

        public Cipher getCipher() {
            return this.mCipher;
        }

        public Mac getMac() {
            return this.mMac;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt$AuthenticationResult.smali */
    public static class AuthenticationResult {
        private final CryptoObject mCryptoObject;

        AuthenticationResult(CryptoObject crypto) {
            this.mCryptoObject = crypto;
        }

        public CryptoObject getCryptoObject() {
            return this.mCryptoObject;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt$AuthenticationCallback.smali */
    public static abstract class AuthenticationCallback {
        public void onAuthenticationError(int errorCode, CharSequence errString) {
        }

        public void onAuthenticationSucceeded(AuthenticationResult result) {
        }

        public void onAuthenticationFailed() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt$PromptInfo.smali */
    public static class PromptInfo {
        private Bundle mBundle;

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\BiometricPrompt$PromptInfo$Builder.smali */
        public static class Builder {
            private final Bundle mBundle = new Bundle();

            public Builder setTitle(CharSequence title) {
                this.mBundle.putCharSequence("title", title);
                return this;
            }

            public Builder setSubtitle(CharSequence subtitle) {
                this.mBundle.putCharSequence(BiometricPrompt.KEY_SUBTITLE, subtitle);
                return this;
            }

            public Builder setDescription(CharSequence description) {
                this.mBundle.putCharSequence(BiometricPrompt.KEY_DESCRIPTION, description);
                return this;
            }

            public Builder setNegativeButtonText(CharSequence text) {
                this.mBundle.putCharSequence(BiometricPrompt.KEY_NEGATIVE_TEXT, text);
                return this;
            }

            public Builder setConfirmationRequired(boolean requireConfirmation) {
                this.mBundle.putBoolean(BiometricPrompt.KEY_REQUIRE_CONFIRMATION, requireConfirmation);
                return this;
            }

            public Builder setDeviceCredentialAllowed(boolean enable) {
                this.mBundle.putBoolean(BiometricPrompt.KEY_ALLOW_DEVICE_CREDENTIAL, enable);
                return this;
            }

            Builder setHandlingDeviceCredentialResult(boolean isHandling) {
                this.mBundle.putBoolean(BiometricPrompt.KEY_HANDLING_DEVICE_CREDENTIAL_RESULT, isHandling);
                return this;
            }

            public PromptInfo build() {
                CharSequence title = this.mBundle.getCharSequence("title");
                CharSequence negative = this.mBundle.getCharSequence(BiometricPrompt.KEY_NEGATIVE_TEXT);
                boolean allowDeviceCredential = this.mBundle.getBoolean(BiometricPrompt.KEY_ALLOW_DEVICE_CREDENTIAL);
                boolean handlingDeviceCredentialResult = this.mBundle.getBoolean(BiometricPrompt.KEY_HANDLING_DEVICE_CREDENTIAL_RESULT);
                if (TextUtils.isEmpty(title)) {
                    throw new IllegalArgumentException("Title must be set and non-empty");
                }
                if (TextUtils.isEmpty(negative) && !allowDeviceCredential) {
                    throw new IllegalArgumentException("Negative text must be set and non-empty");
                }
                if (!TextUtils.isEmpty(negative) && allowDeviceCredential) {
                    throw new IllegalArgumentException("Can't have both negative button behavior and device credential enabled");
                }
                if (handlingDeviceCredentialResult && !allowDeviceCredential) {
                    throw new IllegalArgumentException("Can't be handling device credential result without device credential enabled");
                }
                return new PromptInfo(this.mBundle);
            }
        }

        PromptInfo(Bundle bundle) {
            this.mBundle = bundle;
        }

        Bundle getBundle() {
            return this.mBundle;
        }

        public CharSequence getTitle() {
            return this.mBundle.getCharSequence("title");
        }

        public CharSequence getSubtitle() {
            return this.mBundle.getCharSequence(BiometricPrompt.KEY_SUBTITLE);
        }

        public CharSequence getDescription() {
            return this.mBundle.getCharSequence(BiometricPrompt.KEY_DESCRIPTION);
        }

        public CharSequence getNegativeButtonText() {
            return this.mBundle.getCharSequence(BiometricPrompt.KEY_NEGATIVE_TEXT);
        }

        public boolean isConfirmationRequired() {
            return this.mBundle.getBoolean(BiometricPrompt.KEY_REQUIRE_CONFIRMATION);
        }

        public boolean isDeviceCredentialAllowed() {
            return this.mBundle.getBoolean(BiometricPrompt.KEY_ALLOW_DEVICE_CREDENTIAL);
        }

        boolean isHandlingDeviceCredentialResult() {
            return this.mBundle.getBoolean(BiometricPrompt.KEY_HANDLING_DEVICE_CREDENTIAL_RESULT);
        }
    }

    public BiometricPrompt(FragmentActivity fragmentActivity, Executor executor, AuthenticationCallback callback) {
        LifecycleObserver lifecycleObserver = new LifecycleObserver() { // from class: androidx.biometric.BiometricPrompt.2
            @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
            void onPause() {
                if (!BiometricPrompt.this.isChangingConfigurations()) {
                    if (!BiometricPrompt.canUseBiometricFragment() || BiometricPrompt.this.mBiometricFragment == null) {
                        if (BiometricPrompt.this.mFingerprintDialogFragment != null && BiometricPrompt.this.mFingerprintHelperFragment != null) {
                            BiometricPrompt.dismissFingerprintFragments(BiometricPrompt.this.mFingerprintDialogFragment, BiometricPrompt.this.mFingerprintHelperFragment);
                        }
                    } else if (BiometricPrompt.this.mBiometricFragment.isDeviceCredentialAllowed()) {
                        if (!BiometricPrompt.this.mPausedOnce) {
                            BiometricPrompt.this.mPausedOnce = true;
                        } else {
                            BiometricPrompt.this.mBiometricFragment.cancel();
                        }
                    } else {
                        BiometricPrompt.this.mBiometricFragment.cancel();
                    }
                    BiometricPrompt.this.maybeResetHandlerBridge();
                }
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
            void onResume() {
                BiometricPrompt.this.mBiometricFragment = BiometricPrompt.canUseBiometricFragment() ? (BiometricFragment) BiometricPrompt.this.getFragmentManager().findFragmentByTag(BiometricPrompt.BIOMETRIC_FRAGMENT_TAG) : null;
                if (BiometricPrompt.canUseBiometricFragment() && BiometricPrompt.this.mBiometricFragment != null) {
                    BiometricPrompt.this.mBiometricFragment.setCallbacks(BiometricPrompt.this.mExecutor, BiometricPrompt.this.mNegativeButtonListener, BiometricPrompt.this.mAuthenticationCallback);
                } else {
                    BiometricPrompt biometricPrompt = BiometricPrompt.this;
                    biometricPrompt.mFingerprintDialogFragment = (FingerprintDialogFragment) biometricPrompt.getFragmentManager().findFragmentByTag(BiometricPrompt.DIALOG_FRAGMENT_TAG);
                    BiometricPrompt biometricPrompt2 = BiometricPrompt.this;
                    biometricPrompt2.mFingerprintHelperFragment = (FingerprintHelperFragment) biometricPrompt2.getFragmentManager().findFragmentByTag(BiometricPrompt.FINGERPRINT_HELPER_FRAGMENT_TAG);
                    if (BiometricPrompt.this.mFingerprintDialogFragment != null) {
                        BiometricPrompt.this.mFingerprintDialogFragment.setNegativeButtonListener(BiometricPrompt.this.mNegativeButtonListener);
                    }
                    if (BiometricPrompt.this.mFingerprintHelperFragment != null) {
                        BiometricPrompt.this.mFingerprintHelperFragment.setCallback(BiometricPrompt.this.mExecutor, BiometricPrompt.this.mAuthenticationCallback);
                        if (BiometricPrompt.this.mFingerprintDialogFragment != null) {
                            BiometricPrompt.this.mFingerprintHelperFragment.setHandler(BiometricPrompt.this.mFingerprintDialogFragment.getHandler());
                        }
                    }
                }
                BiometricPrompt.this.maybeHandleDeviceCredentialResult();
                BiometricPrompt.this.maybeInitHandlerBridge(false);
            }
        };
        this.mLifecycleObserver = lifecycleObserver;
        if (fragmentActivity == null) {
            throw new IllegalArgumentException("FragmentActivity must not be null");
        }
        if (executor == null) {
            throw new IllegalArgumentException("Executor must not be null");
        }
        if (callback == null) {
            throw new IllegalArgumentException("AuthenticationCallback must not be null");
        }
        this.mFragmentActivity = fragmentActivity;
        this.mAuthenticationCallback = callback;
        this.mExecutor = executor;
        fragmentActivity.getLifecycle().addObserver(lifecycleObserver);
    }

    public BiometricPrompt(Fragment fragment, Executor executor, AuthenticationCallback callback) {
        LifecycleObserver lifecycleObserver = new LifecycleObserver() { // from class: androidx.biometric.BiometricPrompt.2
            @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
            void onPause() {
                if (!BiometricPrompt.this.isChangingConfigurations()) {
                    if (!BiometricPrompt.canUseBiometricFragment() || BiometricPrompt.this.mBiometricFragment == null) {
                        if (BiometricPrompt.this.mFingerprintDialogFragment != null && BiometricPrompt.this.mFingerprintHelperFragment != null) {
                            BiometricPrompt.dismissFingerprintFragments(BiometricPrompt.this.mFingerprintDialogFragment, BiometricPrompt.this.mFingerprintHelperFragment);
                        }
                    } else if (BiometricPrompt.this.mBiometricFragment.isDeviceCredentialAllowed()) {
                        if (!BiometricPrompt.this.mPausedOnce) {
                            BiometricPrompt.this.mPausedOnce = true;
                        } else {
                            BiometricPrompt.this.mBiometricFragment.cancel();
                        }
                    } else {
                        BiometricPrompt.this.mBiometricFragment.cancel();
                    }
                    BiometricPrompt.this.maybeResetHandlerBridge();
                }
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
            void onResume() {
                BiometricPrompt.this.mBiometricFragment = BiometricPrompt.canUseBiometricFragment() ? (BiometricFragment) BiometricPrompt.this.getFragmentManager().findFragmentByTag(BiometricPrompt.BIOMETRIC_FRAGMENT_TAG) : null;
                if (BiometricPrompt.canUseBiometricFragment() && BiometricPrompt.this.mBiometricFragment != null) {
                    BiometricPrompt.this.mBiometricFragment.setCallbacks(BiometricPrompt.this.mExecutor, BiometricPrompt.this.mNegativeButtonListener, BiometricPrompt.this.mAuthenticationCallback);
                } else {
                    BiometricPrompt biometricPrompt = BiometricPrompt.this;
                    biometricPrompt.mFingerprintDialogFragment = (FingerprintDialogFragment) biometricPrompt.getFragmentManager().findFragmentByTag(BiometricPrompt.DIALOG_FRAGMENT_TAG);
                    BiometricPrompt biometricPrompt2 = BiometricPrompt.this;
                    biometricPrompt2.mFingerprintHelperFragment = (FingerprintHelperFragment) biometricPrompt2.getFragmentManager().findFragmentByTag(BiometricPrompt.FINGERPRINT_HELPER_FRAGMENT_TAG);
                    if (BiometricPrompt.this.mFingerprintDialogFragment != null) {
                        BiometricPrompt.this.mFingerprintDialogFragment.setNegativeButtonListener(BiometricPrompt.this.mNegativeButtonListener);
                    }
                    if (BiometricPrompt.this.mFingerprintHelperFragment != null) {
                        BiometricPrompt.this.mFingerprintHelperFragment.setCallback(BiometricPrompt.this.mExecutor, BiometricPrompt.this.mAuthenticationCallback);
                        if (BiometricPrompt.this.mFingerprintDialogFragment != null) {
                            BiometricPrompt.this.mFingerprintHelperFragment.setHandler(BiometricPrompt.this.mFingerprintDialogFragment.getHandler());
                        }
                    }
                }
                BiometricPrompt.this.maybeHandleDeviceCredentialResult();
                BiometricPrompt.this.maybeInitHandlerBridge(false);
            }
        };
        this.mLifecycleObserver = lifecycleObserver;
        if (fragment == null) {
            throw new IllegalArgumentException("FragmentActivity must not be null");
        }
        if (executor == null) {
            throw new IllegalArgumentException("Executor must not be null");
        }
        if (callback == null) {
            throw new IllegalArgumentException("AuthenticationCallback must not be null");
        }
        this.mFragment = fragment;
        this.mAuthenticationCallback = callback;
        this.mExecutor = executor;
        fragment.getLifecycle().addObserver(lifecycleObserver);
    }

    public void authenticate(PromptInfo info, CryptoObject crypto) {
        if (info == null) {
            throw new IllegalArgumentException("PromptInfo can not be null");
        }
        if (crypto == null) {
            throw new IllegalArgumentException("CryptoObject can not be null");
        }
        if (info.getBundle().getBoolean(KEY_ALLOW_DEVICE_CREDENTIAL)) {
            throw new IllegalArgumentException("Device credential not supported with crypto");
        }
        authenticateInternal(info, crypto);
    }

    public void authenticate(PromptInfo info) {
        if (info == null) {
            throw new IllegalArgumentException("PromptInfo can not be null");
        }
        authenticateInternal(info, null);
    }

    private void authenticateInternal(PromptInfo info, CryptoObject crypto) {
        this.mIsHandlingDeviceCredential = info.isHandlingDeviceCredentialResult();
        FragmentActivity activity = getActivity();
        if (info.isDeviceCredentialAllowed() && Build.VERSION.SDK_INT <= 28) {
            if (!this.mIsHandlingDeviceCredential) {
                launchDeviceCredentialHandler(info);
                return;
            }
            if (activity == null) {
                Log.e(TAG, "Failed to authenticate with device credential. Activity was null.");
                return;
            }
            DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull();
            if (bridge == null) {
                Log.e(TAG, "Failed to authenticate with device credential. Bridge was null.");
                return;
            } else if (!bridge.isConfirmingDeviceCredential()) {
                BiometricManager biometricManager = BiometricManager.from(activity);
                if (biometricManager.canAuthenticate() != 0) {
                    Utils.launchDeviceCredentialConfirmation(TAG, activity, info.getBundle(), null);
                    return;
                }
            }
        }
        FragmentManager fragmentManager = getFragmentManager();
        if (fragmentManager.isStateSaved()) {
            Log.w(TAG, "Not launching prompt. authenticate() called after onSaveInstanceState()");
            return;
        }
        Bundle bundle = info.getBundle();
        boolean shouldForceFingerprint = false;
        this.mPausedOnce = false;
        if (activity != null && crypto != null && Utils.shouldUseFingerprintForCrypto(activity, Build.MANUFACTURER, Build.MODEL)) {
            shouldForceFingerprint = true;
        }
        if (!shouldForceFingerprint && canUseBiometricFragment()) {
            BiometricFragment biometricFragment = (BiometricFragment) fragmentManager.findFragmentByTag(BIOMETRIC_FRAGMENT_TAG);
            if (biometricFragment != null) {
                this.mBiometricFragment = biometricFragment;
            } else {
                this.mBiometricFragment = BiometricFragment.newInstance();
            }
            this.mBiometricFragment.setCallbacks(this.mExecutor, this.mNegativeButtonListener, this.mAuthenticationCallback);
            this.mBiometricFragment.setCryptoObject(crypto);
            this.mBiometricFragment.setBundle(bundle);
            if (biometricFragment == null) {
                fragmentManager.beginTransaction().add(this.mBiometricFragment, BIOMETRIC_FRAGMENT_TAG).commitAllowingStateLoss();
            } else if (this.mBiometricFragment.isDetached()) {
                fragmentManager.beginTransaction().attach(this.mBiometricFragment).commitAllowingStateLoss();
            }
        } else {
            FingerprintDialogFragment fingerprintDialogFragment = (FingerprintDialogFragment) fragmentManager.findFragmentByTag(DIALOG_FRAGMENT_TAG);
            if (fingerprintDialogFragment != null) {
                this.mFingerprintDialogFragment = fingerprintDialogFragment;
            } else {
                this.mFingerprintDialogFragment = FingerprintDialogFragment.newInstance();
            }
            this.mFingerprintDialogFragment.setNegativeButtonListener(this.mNegativeButtonListener);
            this.mFingerprintDialogFragment.setBundle(bundle);
            if (activity != null && !Utils.shouldHideFingerprintDialog(activity, Build.MODEL)) {
                if (fingerprintDialogFragment == null) {
                    this.mFingerprintDialogFragment.show(fragmentManager, DIALOG_FRAGMENT_TAG);
                } else if (this.mFingerprintDialogFragment.isDetached()) {
                    fragmentManager.beginTransaction().attach(this.mFingerprintDialogFragment).commitAllowingStateLoss();
                }
            }
            FingerprintHelperFragment fingerprintHelperFragment = (FingerprintHelperFragment) fragmentManager.findFragmentByTag(FINGERPRINT_HELPER_FRAGMENT_TAG);
            if (fingerprintHelperFragment != null) {
                this.mFingerprintHelperFragment = fingerprintHelperFragment;
            } else {
                this.mFingerprintHelperFragment = FingerprintHelperFragment.newInstance();
            }
            this.mFingerprintHelperFragment.setCallback(this.mExecutor, this.mAuthenticationCallback);
            Handler fingerprintDialogHandler = this.mFingerprintDialogFragment.getHandler();
            this.mFingerprintHelperFragment.setHandler(fingerprintDialogHandler);
            this.mFingerprintHelperFragment.setCryptoObject(crypto);
            fingerprintDialogHandler.sendMessageDelayed(fingerprintDialogHandler.obtainMessage(6), 500L);
            if (fingerprintHelperFragment == null) {
                fragmentManager.beginTransaction().add(this.mFingerprintHelperFragment, FINGERPRINT_HELPER_FRAGMENT_TAG).commitAllowingStateLoss();
            } else if (this.mFingerprintHelperFragment.isDetached()) {
                fragmentManager.beginTransaction().attach(this.mFingerprintHelperFragment).commitAllowingStateLoss();
            }
        }
        fragmentManager.executePendingTransactions();
    }

    public void cancelAuthentication() {
        DeviceCredentialHandlerBridge bridge;
        FingerprintDialogFragment fingerprintDialogFragment;
        BiometricFragment biometricFragment;
        DeviceCredentialHandlerBridge bridge2;
        if (canUseBiometricFragment() && (biometricFragment = this.mBiometricFragment) != null) {
            biometricFragment.cancel();
            if (!this.mIsHandlingDeviceCredential && (bridge2 = DeviceCredentialHandlerBridge.getInstanceIfNotNull()) != null && bridge2.getBiometricFragment() != null) {
                bridge2.getBiometricFragment().cancel();
                return;
            }
            return;
        }
        FingerprintHelperFragment fingerprintHelperFragment = this.mFingerprintHelperFragment;
        if (fingerprintHelperFragment != null && (fingerprintDialogFragment = this.mFingerprintDialogFragment) != null) {
            dismissFingerprintFragments(fingerprintDialogFragment, fingerprintHelperFragment);
        }
        if (!this.mIsHandlingDeviceCredential && (bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull()) != null && bridge.getFingerprintDialogFragment() != null && bridge.getFingerprintHelperFragment() != null) {
            dismissFingerprintFragments(bridge.getFingerprintDialogFragment(), bridge.getFingerprintHelperFragment());
        }
    }

    private void launchDeviceCredentialHandler(PromptInfo info) {
        FragmentActivity activity = getActivity();
        if (activity == null || activity.isFinishing()) {
            Log.w(TAG, "Failed to start handler activity. Parent activity was null or finishing.");
            return;
        }
        maybeInitHandlerBridge(true);
        Bundle infoBundle = info.getBundle();
        infoBundle.putBoolean(KEY_HANDLING_DEVICE_CREDENTIAL_RESULT, true);
        Intent intent = new Intent(activity, (Class<?>) DeviceCredentialHandlerActivity.class);
        intent.putExtra("prompt_info_bundle", infoBundle);
        activity.startActivity(intent);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void maybeInitHandlerBridge(boolean startIgnoringReset) {
        FingerprintHelperFragment fingerprintHelperFragment;
        BiometricFragment biometricFragment;
        if (Build.VERSION.SDK_INT >= 29) {
            return;
        }
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstance();
        if (this.mIsHandlingDeviceCredential) {
            if (canUseBiometricFragment() && (biometricFragment = this.mBiometricFragment) != null) {
                bridge.setBiometricFragment(biometricFragment);
            } else {
                FingerprintDialogFragment fingerprintDialogFragment = this.mFingerprintDialogFragment;
                if (fingerprintDialogFragment != null && (fingerprintHelperFragment = this.mFingerprintHelperFragment) != null) {
                    bridge.setFingerprintFragments(fingerprintDialogFragment, fingerprintHelperFragment);
                }
            }
        } else {
            FragmentActivity activity = getActivity();
            if (activity != null) {
                try {
                    bridge.setClientThemeResId(activity.getPackageManager().getActivityInfo(activity.getComponentName(), 0).getThemeResource());
                } catch (PackageManager.NameNotFoundException e) {
                    Log.e(TAG, "Failed to register client theme to bridge", e);
                }
            }
        }
        bridge.setCallbacks(this.mExecutor, this.mNegativeButtonListener, this.mAuthenticationCallback);
        if (startIgnoringReset) {
            bridge.startIgnoringReset();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void maybeHandleDeviceCredentialResult() {
        DeviceCredentialHandlerBridge bridge;
        if (!this.mIsHandlingDeviceCredential && (bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull()) != null) {
            switch (bridge.getDeviceCredentialResult()) {
                case 1:
                    this.mAuthenticationCallback.onAuthenticationSucceeded(new AuthenticationResult(null));
                    bridge.stopIgnoringReset();
                    bridge.reset();
                    break;
                case 2:
                    String errorMsg = getActivity() != null ? getActivity().getString(R.string.generic_error_user_canceled) : "";
                    this.mAuthenticationCallback.onAuthenticationError(10, errorMsg);
                    bridge.stopIgnoringReset();
                    bridge.reset();
                    break;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void maybeResetHandlerBridge() {
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull();
        if (bridge != null) {
            bridge.reset();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public boolean isChangingConfigurations() {
        return getActivity() != null && getActivity().isChangingConfigurations();
    }

    private FragmentActivity getActivity() {
        FragmentActivity fragmentActivity = this.mFragmentActivity;
        return fragmentActivity != null ? fragmentActivity : this.mFragment.getActivity();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public FragmentManager getFragmentManager() {
        FragmentActivity fragmentActivity = this.mFragmentActivity;
        return fragmentActivity != null ? fragmentActivity.getSupportFragmentManager() : this.mFragment.getChildFragmentManager();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static boolean canUseBiometricFragment() {
        return Build.VERSION.SDK_INT >= 28;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static void dismissFingerprintFragments(FingerprintDialogFragment fingerprintDialogFragment, FingerprintHelperFragment fingerprintHelperFragment) {
        fingerprintDialogFragment.dismissSafely();
        fingerprintHelperFragment.cancel(0);
    }
}
