package o.ee;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import kotlin.text.Typography;
import o.a.m;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\g.smali */
public final class g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static int b;
    private static long c;
    private static char[] d;
    private static char e;
    private static int f;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        b();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ViewConfiguration.getWindowTouchSlop();
        int i = f + Opcodes.LMUL;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 96 / 0;
                break;
        }
    }

    static void b() {
        d = new char[]{30498, 30568, 30574, 30535, 30505, 30588, 30559, 30507, 30526, 30523, 30509, 30591, 30508, 30573, 30570, 30521, 30575, 30572, 30517, 30537, 30497, 30579, 30503, 30550, 30522, 30511, 30582, 30542, 30564, 30586, 30540, 30524, 30545, 30510, 30587, 30556, 30585, 30525, 30561, 30562, 30569, 30549, 30590, 30539, 30534, 30504, 30547, 30546, 30527, 30571, 30589, 30506, 30502, 30563, 30548, 30551, 30557, 30544, 30552, 30532, 30501, 30538, 30566, 30560};
        a = (char) 17053;
        e = (char) 49276;
        b = 161105445;
        c = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{48, -21, 33, -7};
        $$b = Opcodes.IFNULL;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r5v7, types: [int] */
    /* JADX WARN: Type inference failed for: r5v9, types: [int] */
    /* JADX WARN: Type inference failed for: r6v1, types: [int] */
    private static void j(short s, short s2, short s3, Object[] objArr) {
        ?? r6 = 106 - s2;
        byte[] bArr = $$a;
        int i = (s3 * 4) + 1;
        int i2 = (s * 4) + 4;
        byte[] bArr2 = new byte[i];
        int i3 = -1;
        int i4 = i - 1;
        byte b2 = r6;
        if (bArr == null) {
            i4 = i4;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i3 = -1;
            b2 = i2 + r6;
            i2++;
        }
        while (true) {
            int i5 = i3 + 1;
            bArr2[i5] = b2;
            if (i5 == i4) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i2];
            byte b4 = b2;
            i4 = i4;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i3 = i5;
            b2 = b3 + b4;
            i2++;
        }
    }

    public static boolean c() {
        int i = f;
        int i2 = i + 109;
        h = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 91;
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return true;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static void d(String str, Object obj) {
        String obj2;
        Object obj3;
        Object[] objArr = new Object[1];
        g(7 - (ViewConfiguration.getTapTimeout() >> 16), "\u001e#&\n7=㘵", (byte) (Gravity.getAbsoluteGravity(0, 0) + 75), objArr);
        String intern = ((String) objArr[0]).intern();
        switch (obj == null ? '?' : 'I') {
            case 'I':
                obj2 = obj.toString();
                int i = h + Opcodes.DREM;
                f = i % 128;
                int i2 = i % 2;
                break;
            default:
                int i3 = h + Opcodes.DMUL;
                f = i3 % 128;
                char c2 = i3 % 2 != 0 ? '7' : '\n';
                long zoomControlsTimeout = ViewConfiguration.getZoomControlsTimeout();
                switch (c2) {
                    case '\n':
                        Object[] objArr2 = new Object[1];
                        i(1 - (zoomControlsTimeout > 0L ? 1 : (zoomControlsTimeout == 0L ? 0 : -1)), "䂶䅃輑뾽", (char) (38214 - TextUtils.indexOf((CharSequence) "", '0')), "崂㝖䞧\uf595", "\u0000\u0000\u0000\u0000", objArr2);
                        obj3 = objArr2[0];
                        break;
                    default:
                        Object[] objArr3 = new Object[1];
                        i(0 >>> (zoomControlsTimeout > 1L ? 1 : (zoomControlsTimeout == 1L ? 0 : -1)), "䂶䅃輑뾽", (char) (38214 - TextUtils.indexOf((CharSequence) "", 'G')), "崂㝖䞧\uf595", "\u0000\u0000\u0000\u0000", objArr3);
                        obj3 = objArr3[0];
                        break;
                }
                obj2 = ((String) obj3).intern();
                break;
        }
        Log.d(intern, c(str, obj2, 'D'));
        int i4 = h + 59;
        f = i4 % 128;
        if (i4 % 2 == 0) {
            return;
        }
        Object obj4 = null;
        obj4.hashCode();
        throw null;
    }

    public static void e(String str, String str2, Throwable th) {
        String intern;
        char c2;
        int i = h + Opcodes.DREM;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                g(89 >> (ViewConfiguration.getWindowTouchSlop() >> 64), "\u001e#&\n7=㘵", (byte) (75 - TextUtils.getCapsMode("", 0, 0)), objArr);
                intern = ((String) objArr[0]).intern();
                c2 = 21;
                break;
            default:
                Object[] objArr2 = new Object[1];
                g((ViewConfiguration.getWindowTouchSlop() >> 8) + 7, "\u001e#&\n7=㘵", (byte) (75 - TextUtils.getCapsMode("", 0, 0)), objArr2);
                intern = ((String) objArr2[0]).intern();
                c2 = 'D';
                break;
        }
        Log.i(intern, c(str, str2, c2), th);
    }

    public static void d(String str) {
        int i = f + 69;
        h = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((ViewConfiguration.getFadingEdgeLength() >> 16) + 7, "\u001e#&\n7=㘵", (byte) (TextUtils.indexOf("", "", 0, 0) + 75), objArr);
        Log.e(((String) objArr[0]).intern(), c(null, str, 'E'));
        int i3 = f + Opcodes.LMUL;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? '\n' : (char) 26) {
            case '\n':
                throw null;
            default:
                return;
        }
    }

    public static void e(String str, String str2) {
        int i = h + 93;
        f = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g(7 - ExpandableListView.getPackedPositionGroup(0L), "\u001e#&\n7=㘵", (byte) (74 - TextUtils.indexOf((CharSequence) "", '0')), objArr);
        Log.e(((String) objArr[0]).intern(), c(str, str2, 'E'));
        int i3 = h + 77;
        f = i3 % 128;
        int i4 = i3 % 2;
    }

    public static void a(String str, String str2, Throwable th) {
        int i = f + Opcodes.LSHR;
        h = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g(7 - ExpandableListView.getPackedPositionGroup(0L), "\u001e#&\n7=㘵", (byte) (75 - ExpandableListView.getPackedPositionGroup(0L)), objArr);
        Log.e(((String) objArr[0]).intern(), c(str, str2, 'E'), th);
        int i3 = f + 43;
        h = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    private static String c(String str, String str2, char c2) {
        int i = h + 67;
        f = i % 128;
        int i2 = i % 2;
        long currentTimeMillis = System.currentTimeMillis();
        long nanoTime = System.nanoTime();
        Locale a2 = j.a();
        Object[] objArr = new Object[1];
        g(23 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), "\u00135\r\u001d42\u001347\u0018247\u00185\u00111\u0013\u00135\u00035㙋", (byte) (98 - ((Process.getThreadPriority(0) + 20) >> 6)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[7];
        String str3 = "";
        Object[] objArr3 = new Object[1];
        g(6 - (ViewConfiguration.getFadingEdgeLength() >> 16), "\u0006\u001b;%3\u0003", (byte) (TextUtils.lastIndexOf("", '0', 0) + 61), objArr3);
        objArr2[0] = ((String) objArr3[0]).intern();
        objArr2[1] = Long.valueOf(currentTimeMillis / 1000);
        objArr2[2] = Long.valueOf(currentTimeMillis % 1000);
        objArr2[3] = Long.valueOf((nanoTime / 1000) % 1000);
        objArr2[4] = Character.valueOf(c2);
        Object obj = null;
        switch (str == null ? 'C' : '3') {
            case 'C':
                int i3 = h + 41;
                f = i3 % 128;
                switch (i3 % 2 != 0 ? (char) 6 : 'K') {
                    case 6:
                        throw null;
                }
            default:
                StringBuilder append = new StringBuilder().append(str);
                Object[] objArr4 = new Object[1];
                i((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 379033194, "ⱝ魶\ua4c9", (char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 38107), "歂鞖\udb16\ud994", "\u0000\u0000\u0000\u0000", objArr4);
                str3 = append.append(((String) objArr4[0]).intern()).toString();
                break;
        }
        objArr2[5] = str3;
        objArr2[6] = str2;
        String format = String.format(a2, intern, objArr2);
        int i4 = f + 35;
        h = i4 % 128;
        if (i4 % 2 != 0) {
            return format;
        }
        obj.hashCode();
        throw null;
    }

    public static String a(String str) {
        Object obj;
        int i = f + 83;
        int i2 = i % 128;
        h = i2;
        Object obj2 = null;
        switch (i % 2 == 0) {
            case true:
                obj2.hashCode();
                throw null;
            default:
                if (str == null) {
                    int i3 = i2 + 53;
                    f = i3 % 128;
                    switch (i3 % 2 == 0 ? Typography.quote : '\r') {
                        case '\"':
                            return null;
                        default:
                            throw null;
                    }
                }
                Object[] objArr = new Object[1];
                g(13 - TextUtils.indexOf("", "", 0, 0), "\t2=\u0006\r3\n\"*\u0003*\n㗃", (byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 10), objArr);
                if (!str.contains(((String) objArr[0]).intern())) {
                    return str;
                }
                TextUtils.indexOf((CharSequence) "", '0', 0, 0);
                SystemClock.elapsedRealtimeNanos();
                Object[] objArr2 = new Object[1];
                g((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 28, "\t2=\u0006\r3\n\"*\u0003*\n\u0012\u001a\u000e\u0012\u001e6*\u000e\u00165\"\b,7\u0004<㗻", (byte) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 68), objArr2);
                Matcher matcher = Pattern.compile(((String) objArr2[0]).intern(), 8).matcher(str);
                switch (matcher.find() ? (char) 20 : '\'') {
                    case 20:
                        int i4 = h + 51;
                        f = i4 % 128;
                        if (i4 % 2 != 0) {
                            Object[] objArr3 = new Object[1];
                            i((-1) / ImageFormat.getBitsPerPixel(1), "縉唶蓜⍦ꏠ⭣\uf532따䡞副\uf0ca兪\u0cf9潔䄆\uf56d啜됧둸订", (char) Color.alpha(0), "勓䱱\ue980㗽", "\u0000\u0000\u0000\u0000", objArr3);
                            obj = objArr3[0];
                        } else {
                            Object[] objArr4 = new Object[1];
                            i((-1) - ImageFormat.getBitsPerPixel(0), "縉唶蓜⍦ꏠ⭣\uf532따䡞副\uf0ca兪\u0cf9潔䄆\uf56d啜됧둸订", (char) Color.alpha(0), "勓䱱\ue980㗽", "\u0000\u0000\u0000\u0000", objArr4);
                            obj = objArr4[0];
                        }
                        return matcher.replaceFirst(((String) obj).intern());
                    default:
                        return str;
                }
        }
    }

    public static void e() {
        String str;
        String intern;
        int i = h + Opcodes.DDIV;
        f = i % 128;
        int i2 = i % 2;
        try {
            Object[] objArr = new Object[1];
            g(112 - TextUtils.lastIndexOf("", '0', 0), ".\u000b\u0015%\t!.$/87\"\n*?8\"\u0016㘇㘇㘇㘇!3\u001a\u0014\u0018\u00115\u0003\u001b1\u0006\u00015\u0003\u0011\u0018㘇㘇\u001a9\u000526\r!\u001a)4\u0011\u001a5\u0003㘇㘇㘇㘇\u001e#238?9!5\u0003\u001b1\u0001\u001d㘇㘇\u001b)\f&9\u0016\n\u0016\u001b1\u0001\u001d5\u0003\u0018\u00115\u0003\u0011\u0018\u0001\u0018\u00136\u00045㘇㘇㘇㘇\u0011\u0003\n\u0013\u0018\u00115\u0003㙇", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 77), objArr);
            String intern2 = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[11];
            Object[] objArr3 = new Object[1];
            g(Process.getGidForName("") + 21, "-\u001a\u000528.\u0005\b\u0006\"\u00179㙄㙄96\u0016.&\n", (byte) (75 - ExpandableListView.getPackedPositionGroup(0L)), objArr3);
            objArr2[0] = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            g(View.combineMeasuredStates(0, 0) + 5, "$\u0015\f\u0017㘟", (byte) (113 - Gravity.getAbsoluteGravity(0, 0)), objArr4);
            objArr2[1] = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            g(15 - TextUtils.lastIndexOf("", '0'), "8 \u00104\f\u0010㘟㘟\b\u0010\r \u00199\u000f\u001d", (byte) (118 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), objArr5);
            if (((String) objArr5[0]).intern().isEmpty()) {
                Object[] objArr6 = new Object[1];
                g(16 - (ViewConfiguration.getJumpTapTimeout() >> 16), "㘢㘢\u001044\u0010㘢㘢\b8㘢㘢㘢㘢㘢㘢", (byte) (120 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), objArr6);
                str = (String) objArr6[0];
            } else {
                Object[] objArr7 = new Object[1];
                g(16 - View.MeasureSpec.getMode(0), "8 \u00104\f\u0010㘟㘟\b\u0010\r \u00199\u000f\u001d", (byte) (116 - ExpandableListView.getPackedPositionChild(0L)), objArr7);
                str = (String) objArr7[0];
            }
            objArr2[2] = str.intern();
            switch (o.ei.c.c().b()) {
                case false:
                    Object[] objArr8 = new Object[1];
                    i((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "䂶䅃輑뾽", (char) (TextUtils.lastIndexOf("", '0') + 38216), "崂㝖䞧\uf595", "\u0000\u0000\u0000\u0000", objArr8);
                    intern = ((String) objArr8[0]).intern();
                    break;
                default:
                    int i3 = h + 13;
                    f = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            intern = o.ei.c.c().o();
                            int i4 = 32 / 0;
                            break;
                        default:
                            intern = o.ei.c.c().o();
                            break;
                    }
            }
            objArr2[3] = intern;
            e.a();
            objArr2[4] = c.c();
            objArr2[5] = Integer.valueOf(Build.VERSION.SDK_INT);
            objArr2[6] = Build.MANUFACTURER;
            e.a();
            objArr2[7] = c.h();
            objArr2[8] = Build.DEVICE;
            objArr2[9] = Build.BOOTLOADER;
            objArr2[10] = Build.FINGERPRINT;
            String format = String.format(intern2, objArr2);
            Object[] objArr9 = new Object[1];
            i(ViewConfiguration.getMinimumFlingVelocity() >> 16, "\ue702깔\ueab9냩뷕㉣", (char) (54293 - View.resolveSize(0, 0)), "鴒神ᗑࣔ", "\u0000\u0000\u0000\u0000", objArr9);
            d(((String) objArr9[0]).intern(), format);
        } catch (Exception e2) {
        }
    }

    private static void g(int i, String str, byte b2, Object[] objArr) {
        int i2;
        char c2;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr = d;
        char c3 = '0';
        char c4 = 2;
        switch (cArr != null) {
            case true:
                int length = cArr.length;
                char[] cArr2 = new char[length];
                int i3 = 0;
                while (i3 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr[i3])};
                        Object obj = o.e.a.s.get(-1401577988);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(17 - TextUtils.indexOf("", "", 0, 0), (char) Color.blue(0), TextUtils.indexOf("", c3, 0) + 77);
                            byte b3 = (byte) 0;
                            byte b4 = $$a[c4];
                            Object[] objArr3 = new Object[1];
                            j(b3, b4, b3, objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj);
                        }
                        cArr2[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i3++;
                        c3 = '0';
                        c4 = 2;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr = cArr2;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(a)};
            Object obj2 = o.e.a.s.get(-1401577988);
            char c5 = '\b';
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 17, (char) (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getTapTimeout() >> 16) + 76);
                byte b5 = (byte) 0;
                Object[] objArr5 = new Object[1];
                j(b5, $$a[2], b5, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i];
            if (i % 2 != 0) {
                i2 = i - 1;
                cArr3[i2] = (char) (charArray[i2] - b2);
            } else {
                i2 = i;
            }
            if (i2 > 1) {
                int i4 = $10 + 51;
                $11 = i4 % 128;
                int i5 = i4 % 2;
                mVar.b = 0;
                while (true) {
                    switch (mVar.b >= i2) {
                        case false:
                            int i6 = $10 + 93;
                            $11 = i6 % 128;
                            int i7 = i6 % 2;
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                int i8 = $10 + 49;
                                $11 = i8 % 128;
                                int i9 = i8 % 2;
                                cArr3[mVar.b] = (char) (mVar.e - b2);
                                cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                                c2 = c5;
                            } else {
                                try {
                                    Object[] objArr6 = new Object[13];
                                    objArr6[12] = mVar;
                                    objArr6[11] = Integer.valueOf(charValue);
                                    objArr6[10] = mVar;
                                    objArr6[9] = mVar;
                                    objArr6[c5] = Integer.valueOf(charValue);
                                    objArr6[7] = mVar;
                                    objArr6[6] = mVar;
                                    objArr6[5] = Integer.valueOf(charValue);
                                    objArr6[4] = mVar;
                                    objArr6[3] = mVar;
                                    objArr6[2] = Integer.valueOf(charValue);
                                    objArr6[1] = mVar;
                                    objArr6[0] = mVar;
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 10, (char) (8855 - Process.getGidForName("")), 323 - TextUtils.indexOf((CharSequence) "", '0', 0));
                                        byte b6 = (byte) 0;
                                        Object[] objArr7 = new Object[1];
                                        j(b6, (byte) (b6 | 37), b6, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 != null) {
                                                c2 = '\b';
                                            } else {
                                                Class cls4 = (Class) o.e.a.c(TextUtils.getOffsetAfter("", 0) + 11, (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), KeyEvent.keyCodeFromString("") + 65);
                                                byte b7 = (byte) 0;
                                                Object[] objArr9 = new Object[1];
                                                j(b7, (byte) (b7 | DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE), b7, objArr9);
                                                c2 = '\b';
                                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i10 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[intValue];
                                            cArr3[mVar.b + 1] = cArr[i10];
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else {
                                        c2 = '\b';
                                        if (mVar.c == mVar.d) {
                                            int i11 = $10 + Opcodes.DSUB;
                                            $11 = i11 % 128;
                                            int i12 = i11 % 2;
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i13 = (mVar.c * charValue) + mVar.i;
                                            int i14 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[i13];
                                            cArr3[mVar.b + 1] = cArr[i14];
                                        } else {
                                            int i15 = (mVar.c * charValue) + mVar.h;
                                            int i16 = (mVar.d * charValue) + mVar.i;
                                            cArr3[mVar.b] = cArr[i15];
                                            cArr3[mVar.b + 1] = cArr[i16];
                                            int i17 = $11 + 17;
                                            $10 = i17 % 128;
                                            switch (i17 % 2 != 0) {
                                            }
                                        }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                            c5 = c2;
                            break;
                    }
                }
            }
            int i18 = 0;
            while (true) {
                switch (i18 < i ? '@' : 'a') {
                    case '@':
                        cArr3[i18] = (char) (cArr3[i18] ^ 13722);
                        i18++;
                    default:
                        objArr[0] = new String(cArr3);
                        return;
                }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    private static void i(int i, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] charArray;
        char c3;
        int i2 = 0;
        Object obj = null;
        switch (str3 != null) {
            case false:
                cArr = str3;
                break;
            default:
                int i3 = $11 + Opcodes.DSUB;
                $10 = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        cArr = str3.toCharArray();
                        break;
                    default:
                        str3.toCharArray();
                        throw null;
                }
        }
        char[] cArr3 = cArr;
        if (str2 != null) {
            int i4 = $10 + Opcodes.LSHR;
            $11 = i4 % 128;
            if (i4 % 2 == 0) {
                cArr2 = str2.toCharArray();
                int i5 = 65 / 0;
            } else {
                cArr2 = str2.toCharArray();
            }
        } else {
            cArr2 = str2;
        }
        char[] cArr4 = cArr2;
        switch (str == null) {
            case false:
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        o.a.o oVar = new o.a.o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj2 = o.e.a.s.get(-429442487);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c(ExpandableListView.getPackedPositionGroup(0L) + 10, (char) (TextUtils.indexOf("", "", i2, i2) + 20954), 344 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                    byte b2 = (byte) i2;
                    Object[] objArr3 = new Object[1];
                    j(b2, (byte) (-$$a[3]), b2, objArr3);
                    String str4 = (String) objArr3[i2];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i2] = Object.class;
                    obj2 = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj2);
                }
                int intValue = ((Integer) ((Method) obj2).invoke(obj, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj3 = o.e.a.s.get(-515165572);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c(View.MeasureSpec.getMode(i2) + 10, (char) KeyEvent.keyCodeFromString(""), 208 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                        byte b3 = (byte) i2;
                        byte b4 = (byte) (b3 + 5);
                        Object[] objArr5 = new Object[1];
                        j(b3, b4, (byte) (b4 - 5), objArr5);
                        String str5 = (String) objArr5[i2];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i2] = Object.class;
                        obj3 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj3);
                    }
                    int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                    int i6 = cArr5[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr6[intValue]);
                        objArr6[1] = Integer.valueOf(i6);
                        objArr6[i2] = oVar;
                        Object obj4 = o.e.a.s.get(-1614232674);
                        if (obj4 == null) {
                            Class cls3 = (Class) o.e.a.c(10 - ((byte) KeyEvent.getModifierMetaStateMask()), (char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), ((byte) KeyEvent.getModifierMetaStateMask()) + 282);
                            byte b5 = (byte) i2;
                            byte b6 = (byte) (b5 + 3);
                            Object[] objArr7 = new Object[1];
                            j(b5, b6, (byte) (b6 - 3), objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj5 = o.e.a.s.get(406147795);
                            if (obj5 != null) {
                                c3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(19 - View.resolveSizeAndState(0, 0, 0), (char) (14686 - TextUtils.indexOf((CharSequence) "", '0')), 113 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)));
                                byte b7 = (byte) 0;
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                j(b7, b8, b8, objArr9);
                                c3 = 2;
                                obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj5);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r6[oVar.e]) ^ (c ^ 6565854932352255525L)) ^ ((int) (b ^ 6565854932352255525L))) ^ ((char) (e ^ 6565854932352255525L)));
                            oVar.e++;
                            i2 = 0;
                            obj = null;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
