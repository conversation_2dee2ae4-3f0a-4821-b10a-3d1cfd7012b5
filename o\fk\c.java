package o.fk;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c b;
    public static final c c;
    public static final c d;
    public static final c e;
    public static final c f;
    public static final c g;
    public static final c h;
    public static final c i;
    public static final c j;
    private static char[] k;
    private static int l;
    private static boolean m;
    private static final /* synthetic */ c[] n;

    /* renamed from: o, reason: collision with root package name */
    private static boolean f86o;
    private static long p;
    private static int s;
    private static int t;

    static void c() {
        k = new char[]{61766, 61792, 61815, 61797, 61765, 61796, 61805, 61813, 61760, 61798, 61800, 61819, 61810, 61811, 61783, 61808, 61812, 61777, 61816, 61804, 61774, 61814, 61803, 61801, 61809, 61780, 61781, 61768};
        f86o = true;
        m = true;
        l = 782102785;
        p = -3084515800840281949L;
    }

    static void init$0() {
        $$a = new byte[]{85, 91, 121, -13};
        $$b = 75;
    }

    private static void u(short s2, int i2, byte b2, Object[] objArr) {
        int i3 = s2 + Opcodes.IREM;
        byte[] bArr = $$a;
        int i4 = b2 + 4;
        int i5 = (i2 * 2) + 1;
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            i3 = (-i3) + i7;
            i7 = i7;
            i4 = i4;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
        }
        while (true) {
            int i8 = i6 + 1;
            int i9 = i4 + 1;
            bArr2[i8] = (byte) i3;
            if (i8 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i10 = i7;
            i3 = (-bArr[i9]) + i3;
            i7 = i10;
            i4 = i9;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i8;
        }
    }

    private c(String str, int i2) {
    }

    private static /* synthetic */ c[] d() {
        int i2 = t;
        int i3 = i2 + 47;
        s = i3 % 128;
        int i4 = i3 % 2;
        c[] cVarArr = {e, d, b, c, a, f, g, j, i, h};
        int i5 = i2 + 99;
        s = i5 % 128;
        int i6 = i5 % 2;
        return cVarArr;
    }

    public static c valueOf(String str) {
        int i2 = s + 85;
        t = i2 % 128;
        boolean z = i2 % 2 == 0;
        c cVar = (c) Enum.valueOf(c.class, str);
        switch (z) {
            default:
                int i3 = 81 / 0;
            case true:
                return cVar;
        }
    }

    public static c[] values() {
        int i2 = t + Opcodes.DNEG;
        s = i2 % 128;
        int i3 = i2 % 2;
        c[] cVarArr = (c[]) n.clone();
        int i4 = t + 85;
        s = i4 % 128;
        int i5 = i4 % 2;
        return cVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        t = 0;
        s = 1;
        c();
        Object[] objArr = new Object[1];
        q(null, MotionEvent.axisFromString("") + 128, null, "\u0084\u0086\u0088\u0086\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        e = new c(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        r("♰痓腃\udcd4桻蟙퍖滟멞짞", 21377 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr2);
        d = new c(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        q(null, KeyEvent.getDeadChar(0, 0) + 127, null, "\u0084\u0086\u0088\u0082\u008c\u008b\u0088\u008a\u0089\u0084\u0083\u0082\u0081", objArr3);
        b = new c(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        q(null, 127 - (ViewConfiguration.getTouchSlop() >> 8), null, "\u0084\u0086\u0083\u008b\u0091\u0090\u0086\u008f\u008e\u008d\u008b\u0088\u0082\u008c\u008b\u0088\u008a\u0089\u0084\u0083\u0082\u0081", objArr4);
        c = new c(((String) objArr4[0]).intern(), 3);
        Object[] objArr5 = new Object[1];
        r("♰೫猳꙼貖\uf3cd☑ൕ玍ꛓ赽\uf3a9⛱റ", Color.green(0) + 10937, objArr5);
        a = new c(((String) objArr5[0]).intern(), 4);
        Object[] objArr6 = new Object[1];
        q(null, KeyEvent.keyCodeFromString("") + 127, null, "\u0084\u0086\u0098\u0096\u0086\u0083\u0097\u0086\u008f\u0096\u0093\u0086\u0095\u0088\u008e\u0086\u0094\u0093\u0082\u0092\u0084\u0083\u0082\u0081", objArr6);
        f = new c(((String) objArr6[0]).intern(), 5);
        Object[] objArr7 = new Object[1];
        q(null, View.resolveSize(0, 0) + 127, null, "\u0086\u0088\u0082\u0084\u0099\u009a\u0093\u0082\u0087\u0099\u0096\u008b\u0085\u0084\u0083\u0082\u0081", objArr7);
        g = new c(((String) objArr7[0]).intern(), 6);
        Object[] objArr8 = new Object[1];
        r("♰\ued7b뀓䜬ૅ톛\ue4a1ꡅ缜ȫ짝鲙ꎥ睃㩩", 52009 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr8);
        j = new c(((String) objArr8[0]).intern(), 7);
        Object[] objArr9 = new Object[1];
        q(null, 126 - TextUtils.indexOf((CharSequence) "", '0', 0), null, "\u0084\u0086\u0083\u008b\u0091\u0090\u0086\u008f\u0087\u0082\u008c\u008d\u0083\u0099\u0099\u0089\u0096\u008e\u008d\u008b\u0088\u008b\u0084\u008e\u008d\u0081\u0084\u008e\u0089\u0096\u0094\u0083\u0086\u009b\u0084\u0083\u0082\u0081", objArr9);
        i = new c(((String) objArr9[0]).intern(), 8);
        Object[] objArr10 = new Object[1];
        q(null, 127 - Color.red(0), null, "\u0084\u0086\u0088\u0082\u0084\u0099\u009a\u008e\u008d\u008b\u0088\u0082\u0094\u0083\u008d\u0097\u008e\u009c\u0088\u008e\u0086\u0094\u0093\u0082\u0092\u0084\u0083\u0082\u0081", objArr10);
        h = new c(((String) objArr10[0]).intern(), 9);
        n = d();
        int i2 = s + 5;
        t = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:18:0x0079, code lost:
    
        r13 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x007a, code lost:
    
        if (r13 >= r11) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x007c, code lost:
    
        r14 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x007f, code lost:
    
        switch(r14) {
            case 0: goto L148;
            default: goto L46;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0082, code lost:
    
        r14 = o.fk.c.$11 + 109;
        o.fk.c.$10 = r14 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x008b, code lost:
    
        if ((r14 % r4) == 0) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0093, code lost:
    
        r15 = new java.lang.Object[1];
        r15[r6] = java.lang.Integer.valueOf(r8[r13]);
        r4 = o.e.a.s.get(1085633688);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00a8, code lost:
    
        if (r4 == null) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00f8, code lost:
    
        r12[r13] = ((java.lang.Character) ((java.lang.reflect.Method) r4).invoke(null, r15)).charValue();
        r13 = r13 + 1;
        r4 = 2;
        r6 = 0;
        r9 = '0';
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x00ab, code lost:
    
        r4 = (java.lang.Class) o.e.a.c(11 - android.view.View.MeasureSpec.getMode(r6), (char) (android.text.TextUtils.lastIndexOf("", r9, r6, r6) + 1), android.text.TextUtils.indexOf("", "", r6, r6) + 493);
        r14 = (byte) r6;
        r6 = new java.lang.Object[1];
        u((byte) (o.fk.c.$$b & 61), r14, (byte) (r14 - 1), r6);
        r4 = r4.getMethod((java.lang.String) r6[0], java.lang.Integer.TYPE);
        o.e.a.s.put(1085633688, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0103, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x0104, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0108, code lost:
    
        if (r1 != null) goto L60;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x010a, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x010b, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x008e, code lost:
    
        r8 = r12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x007e, code lost:
    
        r14 = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0016, code lost:
    
        if (r1 != 0) goto L16;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v2, types: [byte[]] */
    /* JADX WARN: Type inference failed for: r1v3 */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 948
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fk.c.q(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void r(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fk.c.r(java.lang.String, int, java.lang.Object[]):void");
    }
}
