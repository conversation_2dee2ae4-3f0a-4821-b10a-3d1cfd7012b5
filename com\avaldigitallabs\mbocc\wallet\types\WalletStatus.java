package com.avaldigitallabs.mbocc.wallet.types;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes7\com\avaldigitallabs\mbocc\wallet\types\WalletStatus.smali */
public class WalletStatus {
    public static final String ACTIVITY_IGNORE = "walletActivityIgnore";
    public static final String CONNECTION_ERROR = "walletConnectionError";
    public static final String CONNECTION_SUCCESS = "walletConnectionSuccess";
    public static final String CREDENTIALS_REQUIRED = "walletCredentialsRequired";
    public static final String DESTROY_SUCCESS = "walletDestroySuccess";
    public static final String ENROLLMENT_ERROR = "walletEnrollmentError";
    public static final String PROVISION_REQUIRED = "walletProvisionRequired";
    public static final String VALIDATION_ERROR = "walletValidationError";
}
