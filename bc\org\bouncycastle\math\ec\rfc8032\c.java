package bc.org.bouncycastle.math.ec.rfc8032;

import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z5;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\c.smali */
abstract class c {
    private static final int[] a = {-1420278541, 595116690, -1916432555, 560775794, -1361693040, -1001465015, 2093622249, -1, -1, -1, -1, -1, -1, LockFreeTaskQueueCore.MAX_CAPACITY_MASK};
    private static final int[] b = {463601321, -1045562440, 1239460018, -1189350089, -412821483, 1160071467, -1564970643, 1256291574, -1170454588, -240530412, 2118977290, -1845154869, -1618855054, -1019204973, 1437344377, -1849925303, 1189267370, 280387897, -680846520, -500732508, -1100672524, -1, -1, -1, -1, -1, -1, 268435455};

    static boolean a(byte[] bArr, int[] iArr) {
        if (bArr[56] != 0) {
            return false;
        }
        b(bArr, iArr);
        return !c6.d(14, iArr, a);
    }

    static void b(byte[] bArr, int[] iArr) {
        a.a(bArr, 0, iArr, 0, 14);
    }

    static byte[] b(byte[] bArr) {
        long c = a.c(bArr, 84) & 4294967295L;
        long c2 = a.c(bArr, 91) & 4294967295L;
        long c3 = a.c(bArr, 98) & 4294967295L;
        long c4 = a.c(bArr, Opcodes.LMUL) & 4294967295L;
        long a2 = a.a(bArr, Opcodes.IREM) & 4294967295L;
        long b2 = ((a.b(bArr, 109) << 4) & 4294967295L) + (c4 >>> 28);
        long j = c4 & 268435455;
        long b3 = ((a.b(bArr, 74) << 4) & 4294967295L) + (a2 * 227822194) + (b2 * 149865618);
        long c5 = (a.c(bArr, 77) & 4294967295L) + (a2 * 149865618) + (b2 * 550336261);
        long c6 = (a.c(bArr, 49) & 4294967295L) + (j * 43969588);
        long b4 = ((a.b(bArr, 53) << 4) & 4294967295L) + (b2 * 43969588) + (j * 30366549);
        long c7 = (a.c(bArr, 56) & 4294967295L) + (a2 * 43969588) + (b2 * 30366549) + (j * 163752818);
        long b5 = ((a.b(bArr, 60) << 4) & 4294967295L) + (a2 * 30366549) + (b2 * 163752818) + (j * 258169998);
        long c8 = (a.c(bArr, 63) & 4294967295L) + (a2 * 163752818) + (b2 * 258169998) + (j * 96434764);
        long b6 = ((a.b(bArr, 67) << 4) & 4294967295L) + (a2 * 258169998) + (b2 * 96434764) + (j * 227822194);
        long c9 = (a.c(bArr, 70) & 4294967295L) + (a2 * 96434764) + (b2 * 227822194) + (j * 149865618);
        long b7 = ((a.b(bArr, 102) << 4) & 4294967295L) + (c3 >>> 28);
        long j2 = c3 & 268435455;
        long b8 = ((a.b(bArr, 46) << 4) & 4294967295L) + (b7 * 43969588);
        long j3 = b6 + (b7 * 149865618);
        long j4 = c9 + (b7 * 550336261);
        long c10 = (a.c(bArr, 42) & 4294967295L) + (j2 * 43969588);
        long j5 = c6 + (b7 * 30366549) + (j2 * 163752818);
        long j6 = b4 + (b7 * 163752818) + (j2 * 258169998);
        long j7 = c7 + (b7 * 258169998) + (j2 * 96434764);
        long j8 = b5 + (b7 * 96434764) + (j2 * 227822194);
        long j9 = c8 + (b7 * 227822194) + (j2 * 149865618);
        long b9 = ((a.b(bArr, 95) << 4) & 4294967295L) + (c2 >>> 28);
        long j10 = c2 & 268435455;
        long b10 = ((a.b(bArr, 39) << 4) & 4294967295L) + (b9 * 43969588);
        long j11 = j9 + (b9 * 550336261);
        long c11 = (a.c(bArr, 35) & 4294967295L) + (j10 * 43969588);
        long j12 = c10 + (b9 * 30366549) + (j10 * 163752818);
        long j13 = b8 + (j2 * 30366549) + (b9 * 163752818) + (j10 * 258169998);
        long j14 = j5 + (b9 * 258169998) + (j10 * 96434764);
        long j15 = j6 + (b9 * 96434764) + (j10 * 227822194);
        long j16 = j7 + (b9 * 227822194) + (j10 * 149865618);
        long j17 = j8 + (b9 * 149865618) + (j10 * 550336261);
        long b11 = ((a.b(bArr, 88) << 4) & 4294967295L) + (c >>> 28);
        long j18 = b3 + (j * 550336261) + (j4 >>> 28);
        long j19 = c5 + (j18 >>> 28);
        long b12 = ((a.b(bArr, 81) << 4) & 4294967295L) + (a2 * 550336261) + (j19 >>> 28);
        long j20 = j19 & 268435455;
        long j21 = (c & 268435455) + (b12 >>> 28);
        long j22 = b12 & 268435455;
        long b13 = ((a.b(bArr, 25) << 4) & 4294967295L) + (j22 * 43969588);
        long c12 = (a.c(bArr, 28) & 4294967295L) + (j21 * 43969588) + (j22 * 30366549);
        long b14 = ((a.b(bArr, 32) << 4) & 4294967295L) + (b11 * 43969588) + (j21 * 30366549) + (j22 * 163752818);
        long j23 = c11 + (b11 * 30366549) + (j21 * 163752818) + (j22 * 258169998);
        long j24 = b10 + (j10 * 30366549) + (b11 * 163752818) + (j21 * 258169998) + (j22 * 96434764);
        long j25 = j12 + (b11 * 258169998) + (j21 * 96434764) + (j22 * 227822194);
        long j26 = j13 + (b11 * 96434764) + (j21 * 227822194) + (j22 * 149865618);
        long j27 = j14 + (b11 * 227822194) + (j21 * 149865618) + (j22 * 550336261);
        long c13 = (a.c(bArr, 21) & 4294967295L) + (j20 * 43969588);
        long j28 = j11 + (j17 >>> 28);
        long j29 = j3 + (j2 * 550336261) + (j28 >>> 28);
        long j30 = (j4 & 268435455) + (j29 >>> 28);
        long j31 = j29 & 268435455;
        long j32 = (j18 & 268435455) + (j30 >>> 28);
        long j33 = j30 & 268435455;
        long c14 = (a.c(bArr, 14) & 4294967295L) + (j33 * 43969588);
        long b15 = ((a.b(bArr, 18) << 4) & 4294967295L) + (j32 * 43969588) + (j33 * 30366549);
        long j34 = c13 + (j32 * 30366549) + (j33 * 163752818);
        long j35 = b13 + (j20 * 30366549) + (j32 * 163752818) + (j33 * 258169998);
        long j36 = c12 + (j20 * 163752818) + (j32 * 258169998) + (j33 * 96434764);
        long j37 = b14 + (j20 * 258169998) + (j32 * 96434764) + (j33 * 227822194);
        long j38 = j23 + (j20 * 96434764) + (j32 * 227822194) + (j33 * 149865618);
        long j39 = j24 + (j20 * 227822194) + (j32 * 149865618) + (j33 * 550336261);
        long j40 = j15 + (b11 * 149865618) + (j21 * 550336261) + (j27 >>> 28);
        long j41 = j16 + (b11 * 550336261) + (j40 >>> 28);
        long j42 = j40 & 268435455;
        long j43 = (j17 & 268435455) + (j41 >>> 28);
        long j44 = (j28 & 268435455) + (j43 >>> 28);
        long j45 = j43 & 268435455;
        long j46 = j36 + (j31 * 227822194) + (j44 * 149865618);
        long j47 = j37 + (j31 * 149865618) + (j44 * 550336261);
        long j48 = j42 & 67108863;
        long j49 = ((j41 & 268435455) * 4) + (j42 >>> 26) + 1;
        long c15 = (a.c(bArr, 0) & 4294967295L) + (78101261 * j49);
        long c16 = (a.c(bArr, 7) & 4294967295L) + (j44 * 43969588) + (30366549 * j45) + (175155932 * j49);
        long b16 = ((a.b(bArr, 11) << 4) & 4294967295L) + (j31 * 43969588) + (j44 * 30366549) + (163752818 * j45) + (64542499 * j49);
        long j50 = c14 + (j31 * 30366549) + (j44 * 163752818) + (258169998 * j45) + (158326419 * j49);
        long j51 = b15 + (j31 * 163752818) + (j44 * 258169998) + (96434764 * j45) + (191173276 * j49);
        long j52 = j34 + (j31 * 258169998) + (j44 * 96434764) + (227822194 * j45) + (104575268 * j49);
        long j53 = j35 + (j31 * 96434764) + (j44 * 227822194) + (149865618 * j45) + (j49 * 137584065);
        long b17 = ((a.b(bArr, 4) << 4) & 4294967295L) + (43969588 * j45) + (141809365 * j49) + (c15 >>> 28);
        long j54 = c16 + (b17 >>> 28);
        long j55 = b16 + (j54 >>> 28);
        long j56 = j50 + (j55 >>> 28);
        long j57 = j51 + (j56 >>> 28);
        long j58 = j56 & 268435455;
        long j59 = j52 + (j57 >>> 28);
        long j60 = j57 & 268435455;
        long j61 = j53 + (j59 >>> 28);
        long j62 = j46 + (j45 * 550336261) + (j61 >>> 28);
        long j63 = j47 + (j62 >>> 28);
        long j64 = j62 & 268435455;
        long j65 = j38 + (j31 * 550336261) + (j63 >>> 28);
        long j66 = j39 + (j65 >>> 28);
        long j67 = j25 + (j20 * 149865618) + (j32 * 550336261) + (j66 >>> 28);
        long j68 = j66 & 268435455;
        long j69 = j26 + (j20 * 550336261) + (j67 >>> 28);
        long j70 = (j27 & 268435455) + (j69 >>> 28);
        long j71 = j48 + (j70 >>> 28);
        long j72 = (j71 >>> 26) - 1;
        long j73 = (c15 & 268435455) - (j72 & 78101261);
        long j74 = ((b17 & 268435455) - (j72 & 141809365)) + (j73 >> 28);
        long j75 = ((j54 & 268435455) - (j72 & 175155932)) + (j74 >> 28);
        long j76 = ((j55 & 268435455) - (j72 & 64542499)) + (j75 >> 28);
        long j77 = j75 & 268435455;
        long j78 = (j58 - (j72 & 158326419)) + (j76 >> 28);
        long j79 = (j60 - (j72 & 191173276)) + (j78 >> 28);
        long j80 = ((j59 & 268435455) - (j72 & 104575268)) + (j79 >> 28);
        long j81 = ((j61 & 268435455) - (j72 & 137584065)) + (j80 >> 28);
        long j82 = j64 + (j81 >> 28);
        long j83 = (j63 & 268435455) + (j82 >> 28);
        long j84 = (j65 & 268435455) + (j83 >> 28);
        long j85 = j68 + (j84 >> 28);
        long j86 = (j67 & 268435455) + (j85 >> 28);
        long j87 = (j69 & 268435455) + (j86 >> 28);
        long j88 = (j70 & 268435455) + (j87 >> 28);
        byte[] bArr2 = new byte[57];
        a.a(((j74 & 268435455) << 28) | (j73 & 268435455), bArr2, 0);
        a.a(((j76 & 268435455) << 28) | j77, bArr2, 7);
        a.a((j78 & 268435455) | ((j79 & 268435455) << 28), bArr2, 14);
        a.a((j80 & 268435455) | ((j81 & 268435455) << 28), bArr2, 21);
        a.a((j82 & 268435455) | ((j83 & 268435455) << 28), bArr2, 28);
        a.a((j84 & 268435455) | ((j85 & 268435455) << 28), bArr2, 35);
        a.a((j86 & 268435455) | ((j87 & 268435455) << 28), bArr2, 42);
        a.a((((j71 & 67108863) + (j88 >> 28)) << 28) | (j88 & 268435455), bArr2, 49);
        return bArr2;
    }

    static void a(int i, byte[] bArr) {
        e.a(a, i, bArr);
    }

    static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] iArr4 = new int[22];
        c6.a(iArr2, 0, 8, iArr, 0, 14, iArr4, 0);
        if (iArr2[7] < 0) {
            c6.a(14, a, 0, iArr4, 8);
            c6.c(14, iArr, 0, iArr4, 8);
        }
        byte[] bArr = new byte[88];
        a.a(iArr4, 0, 22, bArr, 0);
        b(a(bArr), iArr3);
    }

    static byte[] a(byte[] bArr) {
        long b2 = (a.b(bArr, 60) << 4) & 4294967295L;
        long c = a.c(bArr, 70) & 4294967295L;
        long c2 = a.c(bArr, 84) & 4294967295L;
        long j = (c2 >>> 28) + 0;
        long b3 = ((a.b(bArr, 74) << 4) & 4294967295L) + (c >>> 28);
        long c3 = (a.c(bArr, 77) & 4294967295L) + (b3 >>> 28);
        long b4 = ((a.b(bArr, 81) << 4) & 4294967295L) + (c3 >>> 28);
        long j2 = c3 & 268435455;
        long j3 = (c2 & 268435455) + (b4 >>> 28);
        long j4 = b4 & 268435455;
        long c4 = (a.c(bArr, 28) & 4294967295L) + (j3 * 43969588);
        long b5 = ((a.b(bArr, 32) << 4) & 4294967295L) + (j * 43969588) + (j3 * 30366549);
        long c5 = (a.c(bArr, 35) & 4294967295L) + (j * 30366549) + (j3 * 163752818);
        long b6 = ((a.b(bArr, 39) << 4) & 4294967295L) + (j * 163752818) + (j3 * 258169998);
        long c6 = (a.c(bArr, 42) & 4294967295L) + (j * 258169998) + (j3 * 96434764);
        long b7 = ((a.b(bArr, 46) << 4) & 4294967295L) + (j * 96434764) + (j3 * 227822194);
        long c7 = (a.c(bArr, 49) & 4294967295L) + (j * 227822194) + (j3 * 149865618);
        long b8 = ((a.b(bArr, 53) << 4) & 4294967295L) + (j * 149865618) + (j3 * 550336261);
        long j5 = c7 + (j4 * 550336261);
        long c8 = (a.c(bArr, 21) & 4294967295L) + (j2 * 43969588);
        long c9 = (a.c(bArr, 63) & 4294967295L) + (b2 >>> 28);
        long j6 = b2 & 268435455;
        long b9 = ((a.b(bArr, 67) << 4) & 4294967295L) + (c9 >>> 28);
        long j7 = (c & 268435455) + (b9 >>> 28);
        long j8 = b9 & 268435455;
        long j9 = (b3 & 268435455) + (j7 >>> 28);
        long j10 = j7 & 268435455;
        long b10 = ((a.b(bArr, 18) << 4) & 4294967295L) + (j9 * 43969588);
        long j11 = b6 + (j4 * 96434764) + (j2 * 227822194) + (j9 * 149865618);
        long j12 = c6 + (j4 * 227822194) + (j2 * 149865618) + (j9 * 550336261);
        long c10 = (a.c(bArr, 14) & 4294967295L) + (j10 * 43969588);
        long j13 = c5 + (j4 * 258169998) + (j2 * 96434764) + (j9 * 227822194) + (j10 * 149865618);
        long b11 = ((a.b(bArr, 11) << 4) & 4294967295L) + (j8 * 43969588);
        long j14 = c8 + (j9 * 30366549) + (j10 * 163752818) + (j8 * 258169998);
        long b12 = ((a.b(bArr, 25) << 4) & 4294967295L) + (j4 * 43969588) + (j2 * 30366549) + (j9 * 163752818) + (j10 * 258169998) + (j8 * 96434764);
        long j15 = c4 + (j4 * 30366549) + (j2 * 163752818) + (j9 * 258169998) + (j10 * 96434764) + (j8 * 227822194);
        long j16 = b5 + (j4 * 163752818) + (j2 * 258169998) + (j9 * 96434764) + (j10 * 227822194) + (j8 * 149865618);
        long j17 = b8 + (j5 >>> 28);
        long c11 = (a.c(bArr, 56) & 4294967295L) + (j * 550336261) + (j17 >>> 28);
        long j18 = j17 & 268435455;
        long j19 = j6 + (c11 >>> 28);
        long j20 = (c9 & 268435455) + (j19 >>> 28);
        long j21 = j19 & 268435455;
        long c12 = (a.c(bArr, 7) & 4294967295L) + (j20 * 43969588) + (30366549 * j21);
        long j22 = ((c11 & 268435455) * 4) + (j18 >>> 26) + 1;
        long c13 = (a.c(bArr, 0) & 4294967295L) + (78101261 * j22);
        long j23 = c12 + (175155932 * j22);
        long j24 = b11 + (j20 * 30366549) + (163752818 * j21) + (64542499 * j22);
        long j25 = c10 + (j8 * 30366549) + (j20 * 163752818) + (258169998 * j21) + (158326419 * j22);
        long j26 = b10 + (j10 * 30366549) + (j8 * 163752818) + (j20 * 258169998) + (96434764 * j21) + (191173276 * j22);
        long j27 = j14 + (j20 * 96434764) + (227822194 * j21) + (104575268 * j22);
        long j28 = b12 + (j20 * 227822194) + (149865618 * j21) + (j22 * 137584065);
        long b13 = ((a.b(bArr, 4) << 4) & 4294967295L) + (43969588 * j21) + (141809365 * j22) + (c13 >>> 28);
        long j29 = j23 + (b13 >>> 28);
        long j30 = j24 + (j29 >>> 28);
        long j31 = j25 + (j30 >>> 28);
        long j32 = j30 & 268435455;
        long j33 = j26 + (j31 >>> 28);
        long j34 = j31 & 268435455;
        long j35 = j27 + (j33 >>> 28);
        long j36 = j33 & 268435455;
        long j37 = j28 + (j35 >>> 28);
        long j38 = j35 & 268435455;
        long j39 = j15 + (j20 * 149865618) + (j21 * 550336261) + (j37 >>> 28);
        long j40 = j16 + (j20 * 550336261) + (j39 >>> 28);
        long j41 = j39 & 268435455;
        long j42 = j13 + (j8 * 550336261) + (j40 >>> 28);
        long j43 = j11 + (j10 * 550336261) + (j42 >>> 28);
        long j44 = j12 + (j43 >>> 28);
        long j45 = b7 + (j4 * 149865618) + (j2 * 550336261) + (j44 >>> 28);
        long j46 = (j5 & 268435455) + (j45 >>> 28);
        long j47 = (j18 & 67108863) + (j46 >>> 28);
        long j48 = j46 & 268435455;
        long j49 = (j47 >>> 26) - 1;
        long j50 = (c13 & 268435455) - (j49 & 78101261);
        long j51 = ((b13 & 268435455) - (j49 & 141809365)) + (j50 >> 28);
        long j52 = ((j29 & 268435455) - (j49 & 175155932)) + (j51 >> 28);
        long j53 = (j32 - (j49 & 64542499)) + (j52 >> 28);
        long j54 = (j34 - (j49 & 158326419)) + (j53 >> 28);
        long j55 = (j36 - (j49 & 191173276)) + (j54 >> 28);
        long j56 = j54 & 268435455;
        long j57 = (j38 - (j49 & 104575268)) + (j55 >> 28);
        long j58 = j55 & 268435455;
        long j59 = ((j37 & 268435455) - (j49 & 137584065)) + (j57 >> 28);
        long j60 = j41 + (j59 >> 28);
        long j61 = (j40 & 268435455) + (j60 >> 28);
        long j62 = (j42 & 268435455) + (j61 >> 28);
        long j63 = (j43 & 268435455) + (j62 >> 28);
        long j64 = (j44 & 268435455) + (j63 >> 28);
        long j65 = (j45 & 268435455) + (j64 >> 28);
        long j66 = j48 + (j65 >> 28);
        byte[] bArr2 = new byte[57];
        a.a((j50 & 268435455) | ((j51 & 268435455) << 28), bArr2, 0);
        a.a((j52 & 268435455) | ((j53 & 268435455) << 28), bArr2, 7);
        a.a(j56 | (j58 << 28), bArr2, 14);
        a.a((j57 & 268435455) | ((j59 & 268435455) << 28), bArr2, 21);
        a.a((j60 & 268435455) | ((j61 & 268435455) << 28), bArr2, 28);
        a.a(((j63 & 268435455) << 28) | (j62 & 268435455), bArr2, 35);
        a.a((j64 & 268435455) | ((j65 & 268435455) << 28), bArr2, 42);
        a.a((j66 & 268435455) | (((j47 & 67108863) + (j66 >> 28)) << 28), bArr2, 49);
        return bArr2;
    }

    static void a(int i, int[] iArr, int[] iArr2) {
        iArr2[14] = (1 << (i - 448)) + c6.a(14, 1 & (~iArr[0]), iArr, a, iArr2);
        c6.d(15, iArr2, 0);
    }

    static void b(int[] iArr, int[] iArr2, int[] iArr3) {
        int i;
        int[] iArr4 = new int[28];
        System.arraycopy(b, 0, iArr4, 0, 28);
        int[] iArr5 = new int[28];
        z5.a(iArr, iArr5);
        iArr5[0] = iArr5[0] + 1;
        int[] iArr6 = new int[28];
        int[] iArr7 = a;
        z5.a(iArr7, iArr, iArr6);
        int[] iArr8 = new int[8];
        System.arraycopy(iArr7, 0, iArr8, 0, 8);
        int[] iArr9 = new int[8];
        System.arraycopy(iArr, 0, iArr9, 0, 8);
        int[] iArr10 = new int[8];
        iArr10[0] = 1;
        int i2 = 27;
        int[] iArr11 = iArr8;
        int[] iArr12 = iArr9;
        int b2 = d.b(27, iArr5);
        int[] iArr13 = iArr10;
        int[] iArr14 = new int[8];
        while (b2 > 447) {
            int a2 = d.a(i2, iArr6) - b2;
            int i3 = (~(a2 >> 31)) & a2;
            if (iArr6[i2] < 0) {
                d.a(i2, i3, iArr4, iArr5, iArr6);
                i = b2;
                d.a(7, i3, iArr11, iArr14, iArr12, iArr13);
            } else {
                i = b2;
                d.b(i2, i3, iArr4, iArr5, iArr6);
                d.b(7, i3, iArr11, iArr14, iArr12, iArr13);
            }
            if (!d.a(i2, iArr4, iArr5)) {
                b2 = i;
            } else {
                i2 = i >>> 5;
                b2 = d.b(i2, iArr4);
                int[] iArr15 = iArr13;
                iArr13 = iArr14;
                iArr14 = iArr15;
                int[] iArr16 = iArr5;
                iArr5 = iArr4;
                iArr4 = iArr16;
                int[] iArr17 = iArr11;
                iArr11 = iArr12;
                iArr12 = iArr17;
            }
        }
        System.arraycopy(iArr12, 0, iArr2, 0, 8);
        System.arraycopy(iArr13, 0, iArr3, 0, 8);
    }
}
