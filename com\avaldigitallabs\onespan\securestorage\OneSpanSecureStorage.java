package com.avaldigitallabs.onespan.securestorage;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.firebase.messaging.Constants;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDK;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import java.util.HashMap;
import java.util.Map;
import org.json.JSONException;

@CapacitorPlugin(name = "OneSpanSecureStorage")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\avaldigitallabs\onespan\securestorage\OneSpanSecureStorage.smali */
public class OneSpanSecureStorage extends Plugin {
    private Map<String, SecureStorageSDK> databases = new HashMap();
    private String fingerPrint;
    private Integer iterationNumber;
    private SecureStorageSDK secureStorage;
    private String storageName;

    @PluginMethod
    public void isProtectedBySecureHardware(PluginCall call) {
        if (this.secureStorage != null) {
            JSObject result = new JSObject();
            result.put("protected", this.secureStorage.isSecureHardwareProtected());
            call.resolve(result);
            return;
        }
        call.reject("Unable to access to secure storage variable, Storage could not be initialized");
    }

    @PluginMethod
    public void connect(PluginCall call) {
        this.iterationNumber = call.getInt("iterationNumber");
        this.storageName = call.getString("storageName");
        this.fingerPrint = call.getString("fingerPrint");
        try {
            SecureStorageSDK db = this.databases.get(this.storageName + this.fingerPrint);
            if (db != null) {
                this.secureStorage = db;
            } else {
                this.secureStorage = SecureStorageSDK.init(this.storageName, this.fingerPrint, this.iterationNumber.intValue(), getContext());
                this.databases.put(this.storageName + this.fingerPrint, this.secureStorage);
            }
            JSObject result = new JSObject();
            result.put("created", true);
            call.resolve(result);
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to initialize Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void getString(PluginCall call) {
        String key = call.getString("forKey");
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                String value = secureStorageSDK.getString(key);
                JSObject result = new JSObject();
                result.put("value", value);
                call.resolve(result);
            } else {
                call.reject("Unable to access to secure storage variable, Storage could not be initialized");
            }
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to get a string from Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void getAll(PluginCall call) {
        try {
            if (this.secureStorage != null) {
                JSObject result = new JSObject();
                JSObject data = new JSObject();
                Map<String, Object> keyValuePairs = this.secureStorage.getAll();
                for (Map.Entry<String, Object> entry : keyValuePairs.entrySet()) {
                    data.put(entry.getKey(), entry.getValue().toString());
                }
                result.accumulate(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, data);
                call.resolve(result);
                return;
            }
            call.reject("Unable to access to secure storage variable, Storage could not be initialized");
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to get all data from Secure Storage, " + getErrorMessage(e));
        } catch (JSONException e2) {
            call.reject("Error converting data to JSON");
        }
    }

    @PluginMethod
    public void putString(PluginCall call) {
        String value = call.getString("forValue");
        String key = call.getString("forKey");
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                secureStorageSDK.putString(key, value);
                JSObject result = new JSObject();
                result.put("put", true);
                call.resolve(result);
            } else {
                call.reject("Unable to access to secure storage variable, Storage could not be initialized");
            }
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to put a string to Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void remove(PluginCall call) {
        String key = call.getString("forKey");
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                secureStorageSDK.remove(key);
                JSObject result = new JSObject();
                result.put("remove", true);
                call.resolve(result);
            } else {
                call.reject("Unable to access to secure storage variable, Storage could not be initialized");
            }
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to remove data from Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void write(PluginCall call) {
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                secureStorageSDK.write(this.fingerPrint, this.iterationNumber.intValue(), getContext());
                JSObject result = new JSObject();
                result.put("write", true);
                call.resolve(result);
            } else {
                call.reject("Unable to access to secure storage variable, Storage could not be initialized");
            }
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to write data into Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void clear(PluginCall call) {
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                secureStorageSDK.clear();
                JSObject result = new JSObject();
                result.put("clear", true);
                call.resolve(result);
            } else {
                call.reject("Unable to access to secure storage variable, Storage could not be initialized");
            }
        } catch (SecureStorageSDKException e) {
            call.reject("Failed to clean Secure Storage, " + getErrorMessage(e));
        }
    }

    @PluginMethod
    public void contains(PluginCall call) {
        String key = call.getString("forKey");
        JSObject result = new JSObject();
        try {
            SecureStorageSDK secureStorageSDK = this.secureStorage;
            if (secureStorageSDK != null) {
                result.put("contains", secureStorageSDK.contains(key));
            } else {
                result.put("contains", false);
            }
        } catch (SecureStorageSDKException e) {
            result.put("contains", false);
        }
        call.resolve(result);
    }

    private String getErrorMessage(SecureStorageSDKException e) {
        switch (e.getErrorCode()) {
            case SecureStorageSDKErrorCodes.VALUE_INCORRECT_FORMAT /* -4314 */:
                return "Value contains invalid character";
            case -4313:
            default:
                return "Unknown error";
            case SecureStorageSDKErrorCodes.VALUE_NULL /* -4312 */:
                return "Value null";
            case SecureStorageSDKErrorCodes.UNKNOWN_KEY /* -4311 */:
                return "Storage does not contains requested key";
            case SecureStorageSDKErrorCodes.KEY_INCORRECT_FORMAT /* -4310 */:
                return "Key contains invalid character";
            case SecureStorageSDKErrorCodes.KEY_INCORRECT_LENGTH /* -4309 */:
                return "Key has incorrect length";
            case SecureStorageSDKErrorCodes.KEY_NULL /* -4308 */:
                return "Key null";
            case SecureStorageSDKErrorCodes.ITERATION_COUNT_INCORRECT /* -4307 */:
                return "Iteration count must be >0";
            case SecureStorageSDKErrorCodes.CONTEXT_NULL /* -4306 */:
                return "Android context null";
            case SecureStorageSDKErrorCodes.UNREADABLE_STORAGE /* -4305 */:
                return "Storage not readable";
            case SecureStorageSDKErrorCodes.UNKNOWN_STORAGE /* -4304 */:
                return "Storage does not exist";
            case SecureStorageSDKErrorCodes.STORAGE_NAME_INCORRECT_FORMAT /* -4303 */:
                return "Name of the storage contains invalid characters. Must be alphanumeric";
            case SecureStorageSDKErrorCodes.STORAGE_NAME_INCORRECT_LENGTH /* -4302 */:
                return "Name of the storage too long";
            case SecureStorageSDKErrorCodes.STORAGE_NAME_NULL /* -4301 */:
                return "Name of the storage null";
            case SecureStorageSDKErrorCodes.INTERNAL_ERROR /* -4300 */:
                return "Internal error: " + e.getMessage();
        }
    }
}
