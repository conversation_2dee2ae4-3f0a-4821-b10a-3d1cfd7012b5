package o.fa;

import com.esotericsoftware.asm.Opcodes;
import o.ey.b;
import o.fe.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fa\a.smali */
public final class a extends b<e> {
    private static int d = 0;
    private static int a = 1;

    @Override // o.ey.e
    public final /* synthetic */ o.ct.b c() {
        int i = d;
        int i2 = (i ^ 11) + ((i & 11) << 1);
        a = i2 % 128;
        int i3 = i2 % 2;
        o.cw.e r = r();
        int i4 = d;
        int i5 = ((i4 | 79) << 1) - (i4 ^ 79);
        a = i5 % 128;
        int i6 = i5 % 2;
        return r;
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ey.a e() {
        c t;
        int i = a;
        int i2 = ((i | 57) << 1) - (i ^ 57);
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                t = t();
                int i3 = 45 / 0;
                break;
            default:
                t = t();
                break;
        }
        int i4 = a;
        int i5 = (i4 ^ 75) + ((i4 & 75) << 1);
        d = i5 % 128;
        int i6 = i5 % 2;
        return t;
    }

    public a(String str, String str2, boolean z) {
        super(str, str2, z);
    }

    private static c t() {
        c cVar = new c();
        int i = d;
        int i2 = (i ^ 37) + ((i & 37) << 1);
        a = i2 % 128;
        switch (i2 % 2 == 0 ? 'V' : '!') {
            case Opcodes.SASTORE /* 86 */:
                throw null;
            default:
                return cVar;
        }
    }

    private static o.cw.e r() {
        o.cw.e eVar = new o.cw.e();
        int i = d;
        int i2 = ((i | 63) << 1) - (i ^ 63);
        a = i2 % 128;
        int i3 = i2 % 2;
        return eVar;
    }
}
