package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP224K1Point.smali */
public class SecP224K1Point extends ECPoint.AbstractFp {
    SecP224K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP224K1FieldElement secP224K1FieldElement = (SecP224K1FieldElement) this.b;
        SecP224K1FieldElement secP224K1FieldElement2 = (SecP224K1FieldElement) this.c;
        SecP224K1FieldElement secP224K1FieldElement3 = (SecP224K1FieldElement) eCPoint.getXCoord();
        SecP224K1FieldElement secP224K1FieldElement4 = (SecP224K1FieldElement) eCPoint.getYCoord();
        SecP224K1FieldElement secP224K1FieldElement5 = (SecP224K1FieldElement) this.d[0];
        SecP224K1FieldElement secP224K1FieldElement6 = (SecP224K1FieldElement) eCPoint.getZCoord(0);
        int[] b = v5.b();
        int[] a = v5.a();
        int[] a2 = v5.a();
        int[] a3 = v5.a();
        boolean isOne = secP224K1FieldElement5.isOne();
        if (isOne) {
            iArr = secP224K1FieldElement3.a;
            iArr2 = secP224K1FieldElement4.a;
        } else {
            SecP224K1Field.square(secP224K1FieldElement5.a, a2);
            SecP224K1Field.multiply(a2, secP224K1FieldElement3.a, a);
            SecP224K1Field.multiply(a2, secP224K1FieldElement5.a, a2);
            SecP224K1Field.multiply(a2, secP224K1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP224K1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP224K1FieldElement.a;
            iArr4 = secP224K1FieldElement2.a;
        } else {
            SecP224K1Field.square(secP224K1FieldElement6.a, a3);
            SecP224K1Field.multiply(a3, secP224K1FieldElement.a, b);
            SecP224K1Field.multiply(a3, secP224K1FieldElement6.a, a3);
            SecP224K1Field.multiply(a3, secP224K1FieldElement2.a, a3);
            iArr3 = b;
            iArr4 = a3;
        }
        int[] a4 = v5.a();
        SecP224K1Field.subtract(iArr3, iArr, a4);
        SecP224K1Field.subtract(iArr4, iArr2, a);
        if (v5.b(a4)) {
            return v5.b(a) ? twice() : curve.getInfinity();
        }
        SecP224K1Field.square(a4, a2);
        int[] a5 = v5.a();
        SecP224K1Field.multiply(a2, a4, a5);
        SecP224K1Field.multiply(a2, iArr3, a2);
        SecP224K1Field.negate(a5, a5);
        v5.c(iArr4, a5, b);
        SecP224K1Field.reduce32(v5.b(a2, a2, a5), a5);
        SecP224K1FieldElement secP224K1FieldElement7 = new SecP224K1FieldElement(a3);
        SecP224K1Field.square(a, secP224K1FieldElement7.a);
        int[] iArr5 = secP224K1FieldElement7.a;
        SecP224K1Field.subtract(iArr5, a5, iArr5);
        SecP224K1FieldElement secP224K1FieldElement8 = new SecP224K1FieldElement(a5);
        SecP224K1Field.subtract(a2, secP224K1FieldElement7.a, secP224K1FieldElement8.a);
        SecP224K1Field.multiplyAddToExt(secP224K1FieldElement8.a, a, b);
        SecP224K1Field.reduce(b, secP224K1FieldElement8.a);
        SecP224K1FieldElement secP224K1FieldElement9 = new SecP224K1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP224K1FieldElement9.a;
            SecP224K1Field.multiply(iArr6, secP224K1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP224K1FieldElement9.a;
            SecP224K1Field.multiply(iArr7, secP224K1FieldElement6.a, iArr7);
        }
        return new SecP224K1Point(curve, secP224K1FieldElement7, secP224K1FieldElement8, new ECFieldElement[]{secP224K1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP224K1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP224K1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP224K1FieldElement secP224K1FieldElement = (SecP224K1FieldElement) this.c;
        if (secP224K1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP224K1FieldElement secP224K1FieldElement2 = (SecP224K1FieldElement) this.b;
        SecP224K1FieldElement secP224K1FieldElement3 = (SecP224K1FieldElement) this.d[0];
        int[] a = v5.a();
        SecP224K1Field.square(secP224K1FieldElement.a, a);
        int[] a2 = v5.a();
        SecP224K1Field.square(a, a2);
        int[] a3 = v5.a();
        SecP224K1Field.square(secP224K1FieldElement2.a, a3);
        SecP224K1Field.reduce32(v5.b(a3, a3, a3), a3);
        SecP224K1Field.multiply(a, secP224K1FieldElement2.a, a);
        SecP224K1Field.reduce32(c6.c(7, a, 2, 0), a);
        int[] a4 = v5.a();
        SecP224K1Field.reduce32(c6.a(7, a2, 3, 0, a4), a4);
        SecP224K1FieldElement secP224K1FieldElement4 = new SecP224K1FieldElement(a2);
        SecP224K1Field.square(a3, secP224K1FieldElement4.a);
        int[] iArr = secP224K1FieldElement4.a;
        SecP224K1Field.subtract(iArr, a, iArr);
        int[] iArr2 = secP224K1FieldElement4.a;
        SecP224K1Field.subtract(iArr2, a, iArr2);
        SecP224K1FieldElement secP224K1FieldElement5 = new SecP224K1FieldElement(a);
        SecP224K1Field.subtract(a, secP224K1FieldElement4.a, secP224K1FieldElement5.a);
        int[] iArr3 = secP224K1FieldElement5.a;
        SecP224K1Field.multiply(iArr3, a3, iArr3);
        int[] iArr4 = secP224K1FieldElement5.a;
        SecP224K1Field.subtract(iArr4, a4, iArr4);
        SecP224K1FieldElement secP224K1FieldElement6 = new SecP224K1FieldElement(a3);
        SecP224K1Field.twice(secP224K1FieldElement.a, secP224K1FieldElement6.a);
        if (!secP224K1FieldElement3.isOne()) {
            int[] iArr5 = secP224K1FieldElement6.a;
            SecP224K1Field.multiply(iArr5, secP224K1FieldElement3.a, iArr5);
        }
        return new SecP224K1Point(curve, secP224K1FieldElement4, secP224K1FieldElement5, new ECFieldElement[]{secP224K1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP224K1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
