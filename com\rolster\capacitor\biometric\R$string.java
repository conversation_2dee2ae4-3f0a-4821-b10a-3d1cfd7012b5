package com.rolster.capacitor.biometric;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\rolster\capacitor\biometric\R$string.smali */
public final class R$string {
    public static int first_fragment_label = 2131886323;
    public static int hello_first_fragment = 2131886330;
    public static int hello_second_fragment = 2131886331;
    public static int my_string = 2131886377;
    public static int next = 2131886380;
    public static int previous = 2131886388;
    public static int second_fragment_label = 2131886391;
    public static int title_activity_auth_activity = 2131886393;

    private R$string() {
    }
}
