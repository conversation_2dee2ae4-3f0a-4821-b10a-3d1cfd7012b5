package o.ei;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.PointerIconCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.EligibilityDenialReason;
import fr.antelop.sdk.WalletProvisioningCallback;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.nio.ByteBuffer;
import java.util.List;
import kotlin.text.Typography;
import o.bc.e;
import o.be.d;
import o.ee.f;
import o.ee.l;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ei\d.smali */
public final class d implements e.InterfaceC0030e, d.c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] k;
    private static int l;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static long f62o;
    private final o.b.c a;
    private final c b;
    WalletProvisioningCallback c;
    private final Context d;
    private boolean e;
    private Handler f;
    private o.bc.e g;
    private HandlerThread h;
    private Handler i;
    private o.be.d j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        n = 1;
        a();
        ViewConfiguration.getMinimumFlingVelocity();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        ViewConfiguration.getJumpTapTimeout();
        ViewConfiguration.getPressedStateDuration();
        Color.alpha(0);
        AudioTrack.getMinVolume();
        ViewConfiguration.getKeyRepeatDelay();
        ViewConfiguration.getKeyRepeatDelay();
        SystemClock.uptimeMillis();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewConfiguration.getMinimumFlingVelocity();
        PointF.length(0.0f, 0.0f);
        int i = l + 27;
        n = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        char[] cArr = new char[1539];
        ByteBuffer.wrap("á\u00809\u0082Qñi=\u0081\u001eÙ]ð£,\u0080ô\u0089\u009cû¤&L\u0003\u0014x=¼ÅçíÕµ2]qec\u000e\u0093Öðþ;\u0086\u0012®Zw®\u001f\u009a'ÍÏ8\u0097a¿Z, ô\u0089\u009cü¤7L\u0018\u0014N=±ÅâíÃµ2Ìy\u0014X|0D÷¬Ìô\u0091Ý|%t\r!Uè½º\u0085\u0087îB6\u0019\u001eýfÆN\u0094\u0097sÿPÇ\"/òw±_\u009a S\u0088;Ð\u000f8Û\u0000¬iy±@\u0099\u001bó;+\u001eCa{±\u0093\u008bËûâ \u001as2Oj¯\u0082öºËÑ\u001c\tg!¨Y\u0093,ªô\u008f\u009cð¤ L\u001a\u0014j=±ÅâíÞµ>]geZ\u000e\u008dÖöþ9\u0086\u0002®\twê\u001fÕ'ÀÏ9\u0097j¿^@\u0080hð0ÙØ\u0002à3\u0089¤Q\u0093yÄ\u0001<)`ñE\u009a¼¢ïJØ\u0012\u001b:dÃëë\u0096³ù[ec\u0012\u000b\u0001,±ôâ\u009cÏ¤ILn\u0014[=\u008aÅåí&µ\u001c]Gf°\u000e\u008dÖÀþ7\u0086!®Rw\u008c\u001fõ'(Ï\u0000\u0097p¸±@Ýh\u008f0<ØiàV\u0089¥QñyÚ\u0001\u000f)x,\u009eô\u0086\u009cù¤/L\u0014\u0014[=\u008dÅùíÖµ!]le@\u000e\u0088Öðþ#\u0086\u0012®Gw ,\u009eô\u0086\u009cù¤/L\u0014\u0014[=\u008dÅùíÖµ!]le@\u000e\u0088Öðþ#\u0086\u0012®Gw \u001fÕ'ÊÏ\"\u0097/¿S@\u0084hí0\u0097Ø\fà}\u0089¨Q\u008byÄ\u0001:)eñN\u009a¯¢æJÕU÷\u008dÒå\u00adÝ}5Gm7Dì¼¿\u0094\u0083Ìc$:\u001c\u0007wÐ¯«\u0087dÿ_×T\u000e·f\u0088^¿¶`î Æ\u00059×\u0011 I\u0093¡\u0018\u0099'ðò(\u0082\u0000\u0080xtP;\u0088\u001dãúÛ»3\u009fkAC`º»\u0092\u0084Ê\u008b\"z\u001aAr\u000eUöª\\r\u007f\u001a\u0019\"ÔÊë\u0092¾»\u0004C_k`3ïÛ\u009fã¾\u0088qP\u0010xÕ\u0000ö(¹ñQ\u0099b¡zIë\u0011\u00999 Æwî@¶\u0003^óf\u008e\u000f]\u0087\u001a_\u00167s\u000f¸ç\u0095¿Ü\u0096;npFD\u001e«öÔÎÎ¥\u0017}h,\u0088ô\u0084\u009cá¤*L\u0007\u0014N=©ÅâíÖµ9]%eP\u000e\u008eÖûþ(\u0086[®Zw¯\u001f\u009a'ÖÏ=\u0097k¿\u001d@\u0089hü0\u0097Ø\u0011à{\u0089¤Qßy\u009c\u0001m)$ñE\u009a¬¢÷JÔ\u0012O:qÃ¤ë\u0097³ð[ec\u0012\u000bS,\u00adôì\u009cÂ¤ILu\u0014P=\u0093Åãí*µ\u000e]Nf·\u000e\u0083ÖÌþ=\u0086f®\u001fw\u0099\u001fó',ÏG\u0097t¸ @\u0085hÆ0+ØjàM\u0089¾QêyÝ\u0001A)|ò¢\u009a\u009f¢ÌJg\u0012\u001a:EÃñëî³\u009d[\u001ccx\u000b[,\u0089ôö\u009c5¤_LN\u0015©=ìÅÆí!µf]UfÏ\u000eòÖ%þY\u0086c¯\u00adw\u0096\u001f\u0081'=Ïl\u0097X¸\u0082@âhÛ0\u0007Ø1á¿\u0089\u0091QÊy-\u0001\u0011)Jò¡\u009aì\u0004±Ü¡´É\u008c\u0005d <t,\u0099ô\u008f\u009cú¤-L\u0014\u0014\u000f=³ÅþíÔµ5]`eA\u000eÁÖòþ8\u0086\b®]wç\u001f\u0086'×Ï0\u0097}¿I@Ëhî0ÞØ\u0011à{\u0089áQ\u009ey\u008d\u0001y)\"ñ\u0005\u009aõ¢âJß\u0012\u000b:=Ã¦ë\u008c³ä[1cS\u000bB,°ôã\u009cÏ¤\bLn\u0014[=ÃÅóí*µ\t]\\f¼\u000e\u0092ÖËþs\u00867®\u001fw\u008c\u001fõ'-ÏG\u0097$¸ö@ÑhË04ØlàP\u0089£Qöy\u009f\u0001A)vò£\u009aÛ¢ÏJ(\u0012\u0007:NÃ°ëû³\u009d[-cv\u000bE,\u0088ôò\u009c5¤_Lh\u0015õ=¸Å\u0091ía,¥ô\u0086\u009cà¤-L\u0012\u0014G=ýÅ¦í\u0099µ\u0016]peG\u000e\u008eÖòþ,\u0086\u000f®@w¤\u001fÕ'ôÏ0\u0097c¿Q@\u008ehí0\u0097Ø&àa\u0089¤Q\u009eyÙ\u00012)fñI\u009aõ¢ÎJÞ\u0012\u000b:xû\u000f#\u000eKys£\u009b\u009aÃÞê\u0011\u0012j\u0093÷Kö#\u0081\u001b[ób«&\u0082\u0080z\u009fR \n\nâ\u0015Ú;±ïi\u0096A\u00109e\u0011;ÈÔ ü\u0098¿pE(\u001c\u0000`ÿô×\u0081\u008f¾go_\u000b6ÙîìÆð¾\u0016\u0096TN;%Æ\u001d\u009aõì\u00ad!\u0085R|\u0096Tå\f\u0086äHÜf´=\u0093ÌK\u0085#«\u001bqó\b«!\u0082ýzÌRQ\nhâ7ÙÖ±ëi»AZ9\u0019\u00110Èã È¨0p(\u0018W \u0081Èº\u0090õ¹:AA,\u009eô\u0086\u009cù¤/L\u0014\u0014[=ýÅâíÝµw]heF\u000e\u0092Öëþm\u0086\u0018®Fw©\u001f\u0081'ÂÏ8\u0097a¿\u001d@\u0089hü0ÃØ\u0012àv\u0089¤Q\u0091y\u008d\u0001k))ñF\u009a»¢çJ\u0091\u0012\\:/Ãëë\u0098³û[5c\u001b\u000b@,±ôø\u009cÖ¤\fLu\u0014\\=\u0080Å±í,µ\u0015]Jf«\u000e\u0096ÖÆþ'\u0086d®Mw\u009e\u001fµíM5U]6eà\u008dÏÕ\u0096üm\u0004/,>tò\u009c½¤\u0082Ï_\u0017$?ÿGåo\u009a´¹l¡\u0004Â<\u0014Ô;\u008cb¥\u0099]Ûuº-\u0004ÅTý\u007f\u0096¤NÕf\u0002\u001e=6*ï\u008d\u0087²¿ W\u001f\u000fY'mØ¼ð\u009a¨÷@)x^\u0011\u0096É½áç\u0099\u0016±\nif\u0002\u0093:ÔÒå\u008a)¢[[\u0086sú+\u0084ÃFû1\u0093l´\u0098l\u008e\u0004«<xÔ\u0004\u008cw¥¬]Âu\u0004-?Åfþ\u008f\u0096¹Nãf\u0002\u001eK6\u007fïî\u0087Û¿\u0002W%\u000fD \u0081Ø±ðø¨\u001b@Zxi\u0011Ú,¥ô\u0086\u009cà¤-L\u0012\u0014G=ýÅ¦í\u0099µ\u001e]ve@\u000e\u0094Öúþ?\u0086[®Hw¤\u001f\u0081'ÊÏ'\u0097n¿I@\u0082hö0ÙØEàz\u0089¥Q\u009ayÃ\u0001/)`ñA\u009a¼¢æJÃ\u0012O:|Ã¥ë\u009d³·[&c\u001c\u000bE,º\u0083w[c3\u0011\u000bÁãã»ª\u0092kj\u001fB:\u001aÉò\u0084Ê¥¡by\u0001QÕ)â\u0001\u0097ØT\u0093KK_#-\u001býóß«\u0096\u00826z\u0001R\u0011\nèâ§Ú\u008e±Ki Aï9ß\u0011\u008cÈ, w\u0098\fpº(©\u0000\u0083ÿS×&\u008f\\gÍ_·6dî@Æ\u0007¾ù\u0096¬NÌ%|\u001d-õ\u000e\u00adÓ\u0085³|eT\\\f|ä¾Ü\u0098´\u008b\u0093zK\"#P\u001b\u0093óü«Ì\u0082\u001czzRæ\nÏâ\u0094Ùw±\u0012C\\\u009bHó:Ëê#È{\u0081R@ª4\u0082\u0011Úâ2¯\n\u008eaI¹*\u0091þéÉÁ¶\u0018tpMH\u001a,\u0080ô\u0094\u009cæ¤6L\u0014\u0014]=ýÅÊíÚµ#]leE\u000e\u0080Öëþ$\u0086\u0014®Gwç\u001f¶'ÌÏ5\u0097j¿\u001d@\u0086hì0ÄØ\u0011à3\u0089¢Q\u0090yÃ\u0001/)hñN\u009a»¢£JÓ\u0012\n:iÃ¼ë\u009c³ò[+cS\u000b\u0011,ÿôì\u009cÕ¤\rL'\u0014\u0004=ÓÅ£í{µ]]If \u000e\u0083ÖÀþ}/\\÷\u0002\u009f%§¸O\u0096\u0017×>$Æ=îVWÒ\u008fúç\u0093ßK7yo%FÀ¾´\u0096¤ÎW&\u001c\u001e5uý\u00ad\u0099\u0085MýzÕ/\fÆdýò\u001a*2B[z\u0083\u0092±Êíã\b\u001b|3lk\u009f\u0083Ô»ýÐ5\bQ \u0085X²pç©\u000eÁ5ù$\u0011ÛI\u0088aê\u009e>¶Qîf\u0006«>ÇW\u000f\u008f7§dß\u0095÷À/çDR|H\u0094wÌ½äÔ\u001d\u000f56mU\u0085\u0086½ôÕéò\u0016*\nB}zî\u0092ÎÊýã0\u001b\u00163\u0081k´\u0083å¸\nÐ9\bc \u0098XÏpâ©/ÁXùÎ\u0011\u00adIÓf\n\u009e7¶oî\u009f\u0006Þ>²WP\u008fC§vß©÷Ê,\u001eD5|`\u0094\u00876\u000eî\"\u0086M¾\u0082V¯\u000eë'\u0003ßS÷`¯\u0089GÜ\u007fã\u0014vÌDä\u009b\u009c¹´ðm\u0013\u0005*=qÕ\u0082\u008d\u0098¥ýZ4rG*lÂ·ú\u0084\u0093\u0017Khcm\u001b\u008d3Òëü\u0080\u0007¸@P&\b± ÙÙ\\ñ/©LA\u0080y¡\u0011÷6\fîC\u0086,¾®VÂ\u000eí'\"ßO÷\u008b¯£Gó|\u0000\u0014%ÌväÈ\u009c\u0096´ém8\u0005C=\u008cÕ¤\u008dË¢\u001aZ!,\u009eô\u0086\u009cù¤/L\u0014\u0014[\u0099¹A¡)Þ\u0011\bù3¡|\u0088ÚpÅXí\u0000PèCÐx»´cÝK\u000b38\u001bwÂÀª¢\u0092öz\u0019\"^\nsõ¿Ý×\u0085ÿm,UQ<\u0082,½ô\u0095\u009cü¤$L\u0016\u0014J=¯ÅÛíËµ8]seZ\u000e\u0092Ööþ\"\u0086\u0015®@w©\u001f\u0092'\u0083Ï|\u0097/¿|@\u0087hë0ÒØ\u0004àw\u0089¸QßyÄ\u00015))ñW\u009a§¢ìJÖ\u0012\u001d:xÃ¸ë\u008a³·[hcS\u000b`,½ôâ\u009cÉ¤\u001dÊ#\u0012\fz@B´ª\u009bòÜÛ1#}\u000bUS½»î\u0083ßè\n0}\u0018\u009b`\u009bHÞ\u00914ù\u0019ÁE)±qÚYÝ¦\u0000\u008exÖ[>\u008e\u0006ñL9\u0094\u0016üZÄ®,\u0081tÆ]+¥g\u008dOÕ§=ô\u0005Ån\u0010¶g\u009e\u0081æ\u0081ÎÄ\u0017.\u007f\u0003G_¯«÷ÓßÍ \u0019\bvPD¸\u009f\u0080øé;1\u0004\u0019qa¥Iú\u0091Ôú(Â}*Mr\u009b,¦ô\u0089\u009cÅ¤1L\u001e\u0014Y=´ÅøíÐµ8]keZ\u000e\u008fÖøþ\u001e\u0086\u001e®[w±\u001f\u009c'ÀÏ4\u0097I¿\\@\u0082hõ0ÂØ\u0017àv\u0089\u0082Q\u009eyÁ\u00017)kñF\u009a¶¢èJ\u0099\u0012F:=ÃæëÙ³ò[7c\u0001\u000bN,\u00adô°\u009cà\u007fM§o\u001b\u009c,¦ô\u0089\u009cÑ¤&L\u0007\u0014F=¾Åîíüµ;]leT\u000e\u0088Öýþ!\u0086\u001e®\u0005wç\u001f\u0093'ÄÏ!\u0097/¿\\@\u009ehí0ßØ\nàa\u0089¨Q\u0085yÈ\u0001?))ñ\u0007,¦ô\u0089\u009cÑ¤&L\u0007\u0014F=¾Åîí÷µ8]qev\u000e\u008dÖöþ*\u0086\u0012®Kw«\u001f\u0090'\u008fÏq\u0097}¿X@\u008ahê0ØØ\u000bà3\u0089ûQß,éôÇ\u009cµ¤cL\u0015\u0014J=³ÅâíØµ;]WeV\u000e\u0087Öúþ?\u0086\u001e®Gw¤\u001f\u0090'\u0099Ïq,¦ô\u0089\u009cÖ¤+L\u0014\u0014L=¶ÅÎíÕµ>]beZ\u000e\u0083Ööþ!\u0086\u0012®]w¾\u001f°'ÑÏ#\u0097`¿OÓ\u0090\u000b¹cÞ[\u001b³/ëlÂ\u0099:Æ\u0012³J\u001c¢\\\u009a`ñ¥)Ö\u0001\u000fy#Ql\u0088\u0083à°Øü0\bh\u0005@~¿¯\u0097ÚÏé'&\u001fXv\u0087®¼\u0086ýþ\u0010ÖW\u000ede\u0090]Ç".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1539);
        k = cArr;
        f62o = -8995465798114806553L;
    }

    static void init$0() {
        $$a = new byte[]{20, -126, 34, 119};
        $$b = Opcodes.IINC;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 102
            byte[] r0 = o.ei.d.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r6 = r6 * 4
            int r6 = 4 - r6
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2c
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2c:
            int r4 = -r4
            int r6 = r6 + r4
            int r7 = r7 + 1
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.d.p(int, short, int, java.lang.Object[]):void");
    }

    public d(Context context, WalletProvisioningCallback walletProvisioningCallback) throws WalletValidationException {
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            m((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 52490), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 7, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        this.d = context;
        this.e = false;
        this.c = walletProvisioningCallback;
        this.a = new o.b.c(context);
        c c = c.c();
        this.b = c;
        if (!c.b()) {
            c.b(context);
        }
    }

    public final void e() {
        o.fl.d.a(this.d);
        o.ee.g.e();
        l.d(this.d);
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) TextUtils.indexOf("", "", 0, 0), 7 - View.resolveSizeAndState(0, 0, 0), 22 - ImageFormat.getBitsPerPixel(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) View.MeasureSpec.getMode(0), 30 - (ViewConfiguration.getTouchSlop() >> 8), AndroidCharacter.getMirror('0') - '&', objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.fl.d.a(this.d);
        o.ee.g.e();
        l.d(this.d);
        this.i = new o.ee.b();
        Object[] objArr3 = new Object[1];
        m((char) (Color.alpha(0) + 57553), TextUtils.indexOf("", "", 0, 0) + 40, 31 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr3);
        HandlerThread handlerThread = new HandlerThread(((String) objArr3[0]).intern());
        this.h = handlerThread;
        handlerThread.start();
        o.ee.b bVar = new o.ee.b(this.h.getLooper());
        this.f = bVar;
        bVar.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda5
            @Override // java.lang.Runnable
            public final void run() {
                d.this.j();
            }
        });
        int i = n + 5;
        l = i % 128;
        switch (i % 2 != 0 ? 'B' : (char) 6) {
            case 'B':
                throw null;
            default:
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void j() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (ViewConfiguration.getEdgeSlop() >> 16), 7 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (65322 - Color.green(0)), 1503 - (ViewConfiguration.getFadingEdgeLength() >> 16), ((byte) KeyEvent.getModifierMetaStateMask()) + 37, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (o.bt.b.c(this.d).isEmpty()) {
            this.e = true;
            this.i.post(new Runnable() { // from class: o.ei.d.5
                public static final byte[] $$a = null;
                public static final int $$b = 0;
                private static int $10;
                private static int $11;
                private static long a;
                private static int c;
                private static int d;

                static {
                    init$0();
                    $10 = 0;
                    $11 = 1;
                    d = 0;
                    c = 1;
                    a = 4190931210952355139L;
                }

                /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
                /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
                /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                private static void f(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
                    /*
                        byte[] r0 = o.ei.d.AnonymousClass5.$$a
                        int r7 = r7 * 3
                        int r7 = r7 + 68
                        int r8 = r8 * 3
                        int r8 = 1 - r8
                        int r6 = r6 * 3
                        int r6 = r6 + 4
                        byte[] r1 = new byte[r8]
                        int r8 = r8 + (-1)
                        r2 = 0
                        if (r0 != 0) goto L1b
                        r3 = r1
                        r4 = r2
                        r1 = r0
                        r0 = r9
                        r9 = r8
                        goto L34
                    L1b:
                        r3 = r2
                    L1c:
                        byte r4 = (byte) r7
                        r1[r3] = r4
                        if (r3 != r8) goto L29
                        java.lang.String r6 = new java.lang.String
                        r6.<init>(r1, r2)
                        r9[r2] = r6
                        return
                    L29:
                        int r3 = r3 + 1
                        r4 = r0[r6]
                        r5 = r9
                        r9 = r8
                        r8 = r4
                        r4 = r3
                        r3 = r1
                        r1 = r0
                        r0 = r5
                    L34:
                        int r8 = -r8
                        int r6 = r6 + 1
                        int r7 = r7 + r8
                        r8 = r9
                        r9 = r0
                        r0 = r1
                        r1 = r3
                        r3 = r4
                        goto L1c
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.ei.d.AnonymousClass5.f(byte, byte, byte, java.lang.Object[]):void");
                }

                static void init$0() {
                    $$a = new byte[]{5, 125, -30, 121};
                    $$b = 36;
                }

                @Override // java.lang.Runnable
                public final void run() {
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    e("ზ剾႟뾌\ueed2㖄貈䐹ꛔ\uf625ꑍ\uf3a0籚ࡧ編륬㏴䋽쭺情쥥镋脓\u2e72肏\u2fdc庋", ViewConfiguration.getDoubleTapTimeout() >> 16, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    e("ᓹ\uecbeᒉŐ\uf36e⠹\udbc5፧ꋠ䣁맸ꓮ硷뚫恏\uee16㞉ﰻ훟㞈쵍⮋鲠礽蒠鄈䌰芵到\ud8edএ풑\ue99a٧\uf012Ṃꜜ䷑꛲", ExpandableListView.getPackedPositionGroup(0L), objArr4);
                    o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                    switch (d.this.c != null ? '/' : (char) 14) {
                        case 14:
                            break;
                        default:
                            int i = d + Opcodes.DDIV;
                            c = i % 128;
                            int i2 = i % 2;
                            d.this.c.onInitializationSuccess(null);
                            int i3 = c + 37;
                            d = i3 % 128;
                            int i4 = i3 % 2;
                            break;
                    }
                }

                /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                    jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                    	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                    	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                    	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                    	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                    	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                    	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                    */
                /* JADX WARN: Multi-variable type inference failed */
                /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
                /* JADX WARN: Type inference failed for: r13v1 */
                /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
                private static void e(java.lang.String r13, int r14, java.lang.Object[] r15) {
                    /*
                        Method dump skipped, instructions count: 364
                        To view this dump add '--comments-level debug' option
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.ei.d.AnonymousClass5.e(java.lang.String, int, java.lang.Object[]):void");
                }
            });
            this.h.quitSafely();
            int i = l + 91;
            n = i % 128;
            switch (i % 2 == 0 ? '?' : '\n') {
                case '?':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }
        this.i.post(new Runnable() { // from class: o.ei.d.1
            private static int c = 0;
            private static int b = 1;

            @Override // java.lang.Runnable
            public final void run() {
                int i2 = c;
                int i3 = (i2 & 57) + (i2 | 57);
                b = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        if (d.this.c != null) {
                            d.this.c.onProvisioningError(new AntelopError(new o.bv.c(AntelopErrorCode.AndroidPermissionNotGranted)), null);
                            int i4 = c;
                            int i5 = ((i4 | 47) << 1) - (i4 ^ 47);
                            b = i5 % 128;
                            int i6 = i5 % 2;
                        }
                        int i7 = (c + 6) - 1;
                        b = i7 % 128;
                        int i8 = i7 % 2;
                        return;
                    default:
                        WalletProvisioningCallback walletProvisioningCallback = d.this.c;
                        throw null;
                }
            }
        });
        this.h.quitSafely();
        int i2 = n + 61;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 20 : 'Q') {
            case 20:
                int i3 = 53 / 0;
                return;
            default:
                return;
        }
    }

    public final void c() {
        this.c = null;
        o.bc.e eVar = this.g;
        switch (eVar != null ? 'I' : (char) 11) {
            case 'I':
                eVar.f();
                break;
        }
        o.be.d dVar = this.j;
        switch (dVar == null) {
            case false:
                int i = l + 7;
                n = i % 128;
                int i2 = i % 2;
                dVar.o();
                break;
        }
        HandlerThread handlerThread = this.h;
        switch (handlerThread != null) {
            case true:
                handlerThread.quitSafely();
                int i3 = l + 83;
                n = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
    }

    public final void e(boolean z) throws WalletValidationException {
        boolean z2;
        int i = n + 17;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (-16777209) - Color.rgb(0, 0, 0), 23 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 57233), (ViewConfiguration.getTouchSlop() >> 8) + 71, 16 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!this.e) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            m((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), View.MeasureSpec.getSize(0) + 7, 23 - (Process.myTid() >> 22), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            m((char) (Color.rgb(0, 0, 0) + 16777216), 88 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 78, objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr5 = new Object[1];
            m((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), ImageFormat.getBitsPerPixel(0) + Opcodes.IF_ACMPNE, 17 - ExpandableListView.getPackedPositionChild(0L), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            m((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), TextUtils.lastIndexOf("", '0') + Opcodes.INVOKESTATIC, Color.green(0) + 37, objArr6);
            throw new WalletValidationException(walletValidationErrorCode, intern3, ((String) objArr6[0]).intern());
        }
        o.be.d dVar = this.j;
        if (dVar == null) {
            z2 = false;
        } else {
            z2 = true;
        }
        switch (z2) {
            case false:
                break;
            default:
                int i3 = l + Opcodes.LSUB;
                n = i3 % 128;
                int i4 = i3 % 2;
                if (dVar.l()) {
                    o.ee.g.c();
                    Object[] objArr7 = new Object[1];
                    m((char) ((-1) - ImageFormat.getBitsPerPixel(0)), 8 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), KeyEvent.normalizeMetaState(0) + 23, objArr7);
                    String intern4 = ((String) objArr7[0]).intern();
                    Object[] objArr8 = new Object[1];
                    m((char) (TextUtils.indexOf("", "") + 31069), 220 - Color.alpha(0), 46 - (KeyEvent.getMaxKeyCode() >> 16), objArr8);
                    o.ee.g.d(intern4, ((String) objArr8[0]).intern());
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unexpected;
                    Object[] objArr9 = new Object[1];
                    m((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), Color.alpha(0) + Opcodes.IF_ACMPEQ, 17 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr9);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr9[0]).intern());
                }
                break;
        }
        o.be.d dVar2 = new o.be.d(this.d, this.b, this);
        this.j = dVar2;
        dVar2.a(z);
        int i5 = n + 39;
        l = i5 % 128;
        switch (i5 % 2 != 0 ? 'W' : 'c') {
            case Opcodes.DADD /* 99 */:
                return;
            default:
                int i6 = 67 / 0;
                return;
        }
    }

    public final void e(byte[] bArr, String str) throws WalletValidationException {
        int i = n + 23;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), (ViewConfiguration.getJumpTapTimeout() >> 16) + 7, 23 - (KeyEvent.getMaxKeyCode() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 34554), 266 - KeyEvent.keyCodeFromString(""), 28 - TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (bArr == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            m((char) (43922 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), 296 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), View.getDefaultSize(0, 0) + 14, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (bArr.length != 16) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr4 = new Object[1];
            m((char) (43922 - Color.blue(0)), 296 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), Process.getGidForName("") + 15, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            m((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), Color.red(0) + 309, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.IUSHR, objArr5);
            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
        }
        switch (str != null ? '@' : '2') {
            case '2':
                break;
            default:
                int i3 = l + 37;
                n = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 21 : 'D') {
                    case 'D':
                        if (!f.c(str)) {
                            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.InvalidFormat;
                            Object[] objArr6 = new Object[1];
                            m((char) (10293 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), 434 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 6 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr6);
                            String intern3 = ((String) objArr6[0]).intern();
                            Object[] objArr7 = new Object[1];
                            m((char) View.MeasureSpec.getSize(0), ExpandableListView.getPackedPositionGroup(0L) + 440, TextUtils.indexOf((CharSequence) "", '0', 0) + 100, objArr7);
                            throw new WalletValidationException(walletValidationErrorCode3, intern3, ((String) objArr7[0]).intern());
                        }
                        break;
                    default:
                        f.c(str);
                        throw null;
                }
        }
        o.ba.c cVar = new o.ba.c();
        cVar.c(bArr);
        cVar.b(str);
        a(cVar);
        int i4 = l + 27;
        n = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void e(String str, String str2, String str3, String str4) throws WalletValidationException {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 6, 23 - View.combineMeasuredStates(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) TextUtils.indexOf("", "", 0, 0), View.resolveSizeAndState(0, 0, 0) + 539, ExpandableListView.getPackedPositionType(0L) + 39, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (str != null ? '@' : 'R') {
            case '@':
                int i = n + 3;
                l = i % 128;
                int i2 = i % 2;
                if (!b(str)) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr3 = new Object[1];
                    m((char) (Color.blue(0) + 55173), (Process.myTid() >> 22) + 578, Color.green(0) + 8, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    m((char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 49021), View.resolveSizeAndState(0, 0, 0) + 586, 65 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr4);
                    throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
                }
                break;
        }
        if (str2 != null) {
            int i3 = n + 55;
            l = i3 % 128;
            int i4 = i3 % 2;
            if (!b(str2)) {
                WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
                Object[] objArr5 = new Object[1];
                m((char) (33966 - (ViewConfiguration.getEdgeSlop() >> 16)), 651 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 7 - TextUtils.indexOf((CharSequence) "", '0'), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                m((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), 658 - ((Process.getThreadPriority(0) + 20) >> 6), (ViewConfiguration.getTouchSlop() >> 8) + 64, objArr6);
                throw new WalletValidationException(walletValidationErrorCode2, intern3, ((String) objArr6[0]).intern());
            }
        }
        if (str3 == null) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
            Object[] objArr7 = new Object[1];
            m((char) (49622 - ExpandableListView.getPackedPositionChild(0L)), 721 - TextUtils.lastIndexOf("", '0'), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 16, objArr7);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr7[0]).intern());
        }
        if (!b(str3)) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr8 = new Object[1];
            m((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 49623), 722 - (ViewConfiguration.getEdgeSlop() >> 16), Color.red(0) + 17, objArr8);
            String intern4 = ((String) objArr8[0]).intern();
            Object[] objArr9 = new Object[1];
            m((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 38947), ((Process.getThreadPriority(0) + 20) >> 6) + 739, 75 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr9);
            throw new WalletValidationException(walletValidationErrorCode4, intern4, ((String) objArr9[0]).intern());
        }
        switch (str4 != null ? Typography.less : 'D') {
            case '<':
                int i5 = n + 95;
                l = i5 % 128;
                int i6 = i5 % 2;
                if (!f.c(str4)) {
                    WalletValidationErrorCode walletValidationErrorCode5 = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr10 = new Object[1];
                    m((char) (Color.rgb(0, 0, 0) + 16787509), 435 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 6 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr10);
                    String intern5 = ((String) objArr10[0]).intern();
                    Object[] objArr11 = new Object[1];
                    m((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 439 - ImageFormat.getBitsPerPixel(0), 99 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr11);
                    throw new WalletValidationException(walletValidationErrorCode5, intern5, ((String) objArr11[0]).intern());
                }
                break;
        }
        o.ba.b bVar = new o.ba.b();
        bVar.a(str);
        bVar.c(str2);
        bVar.d(str3);
        bVar.b(str4);
        a(bVar);
    }

    public final void e(byte[] bArr, byte[] bArr2, String str) throws WalletValidationException {
        int i = l + 99;
        n = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 6 - ((byte) KeyEvent.getModifierMetaStateMask()), Color.rgb(0, 0, 0) + 16777239, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getPressedStateDuration() >> 16), Drawable.resolveOpacity(0, 0) + 813, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 46, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (bArr == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            m((char) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 45047), (ViewConfiguration.getJumpTapTimeout() >> 16) + 859, 18 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (bArr.length > 1024) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr4 = new Object[1];
            m((char) (Gravity.getAbsoluteGravity(0, 0) + 45047), TextUtils.indexOf("", "") + 859, (KeyEvent.getMaxKeyCode() >> 16) + 18, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            m((char) (49099 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 876 - TextUtils.indexOf((CharSequence) "", '0', 0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 57, objArr5);
            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
        }
        if (bArr2 == null) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
            Object[] objArr6 = new Object[1];
            m((char) (28635 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 935 - TextUtils.getCapsMode("", 0, 0), 19 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr6);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr6[0]).intern());
        }
        if (bArr2.length > 1024) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr7 = new Object[1];
            m((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 28636), ImageFormat.getBitsPerPixel(0) + 936, 20 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr7);
            String intern3 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            m((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 955, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 59, objArr8);
            throw new WalletValidationException(walletValidationErrorCode4, intern3, ((String) objArr8[0]).intern());
        }
        switch (str != null ? 'X' : '`') {
            case Opcodes.POP2 /* 88 */:
                int i3 = n + 81;
                l = i3 % 128;
                int i4 = i3 % 2;
                if (!f.c(str)) {
                    WalletValidationErrorCode walletValidationErrorCode5 = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr9 = new Object[1];
                    m((char) (Color.alpha(0) + 10293), 434 - (ViewConfiguration.getWindowTouchSlop() >> 8), View.combineMeasuredStates(0, 0) + 6, objArr9);
                    String intern4 = ((String) objArr9[0]).intern();
                    Object[] objArr10 = new Object[1];
                    m((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), KeyEvent.keyCodeFromString("") + 440, 99 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr10);
                    throw new WalletValidationException(walletValidationErrorCode5, intern4, ((String) objArr10[0]).intern());
                }
                break;
        }
        o.ba.a aVar = new o.ba.a();
        aVar.c(bArr);
        aVar.b(bArr2);
        aVar.b(str);
        a(aVar);
    }

    private static boolean b(String str) {
        int i = n + 9;
        l = i % 128;
        int i2 = i % 2;
        String e = o.e((CharSequence) str);
        Object[] objArr = new Object[1];
        m((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 970), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + PointerIconCompat.TYPE_VERTICAL_DOUBLE_ARROW, Gravity.getAbsoluteGravity(0, 0) + 9, objArr);
        boolean matches = e.matches(((String) objArr[0]).intern());
        int i3 = l + Opcodes.DDIV;
        n = i3 % 128;
        int i4 = i3 % 2;
        return matches;
    }

    private void a(o.ba.e eVar) throws WalletValidationException {
        int i = n + 47;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 6 - TextUtils.lastIndexOf("", '0'), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (31599 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), 1023 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 19, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!this.e) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            m((char) View.resolveSize(0, 0), 7 - ExpandableListView.getPackedPositionGroup(0L), Color.argb(0, 0, 0, 0) + 23, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            m((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 57000), 1043 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), TextUtils.indexOf("", "", 0, 0) + 82, objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr5 = new Object[1];
            m((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 165 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.lastIndexOf("", '0', 0, 0) + 19, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            m((char) (AndroidCharacter.getMirror('0') - '0'), 182 - TextUtils.indexOf((CharSequence) "", '0', 0), 36 - ExpandableListView.getPackedPositionChild(0L), objArr6);
            throw new WalletValidationException(walletValidationErrorCode, intern3, ((String) objArr6[0]).intern());
        }
        if (o.b.c.b(this.d)) {
            o.ee.g.c();
            Object[] objArr7 = new Object[1];
            m((char) Gravity.getAbsoluteGravity(0, 0), 7 - TextUtils.getOffsetAfter("", 0), View.resolveSize(0, 0) + 23, objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            m((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 6840), 1125 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 68 - MotionEvent.axisFromString(""), objArr8);
            o.ee.g.d(intern4, ((String) objArr8[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr9 = new Object[1];
            m((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 1195 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 6 - (Process.myTid() >> 22), objArr9);
            String intern5 = ((String) objArr9[0]).intern();
            Object[] objArr10 = new Object[1];
            m((char) (46375 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), KeyEvent.normalizeMetaState(0) + 1200, 'M' - AndroidCharacter.getMirror('0'), objArr10);
            throw new WalletValidationException(walletValidationErrorCode2, intern5, ((String) objArr10[0]).intern());
        }
        o.bc.e eVar2 = this.g;
        switch (eVar2 != null ? '8' : '@') {
            case '@':
                break;
            default:
                int i3 = l + 41;
                n = i3 % 128;
                switch (i3 % 2 == 0 ? 'C' : (char) 24) {
                    case 'C':
                        eVar2.k();
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        if (!eVar2.k()) {
                            o.ee.g.c();
                            Object[] objArr11 = new Object[1];
                            m((char) TextUtils.getTrimmedLength(""), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 7, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 22, objArr11);
                            String intern6 = ((String) objArr11[0]).intern();
                            Object[] objArr12 = new Object[1];
                            m((char) TextUtils.getOffsetAfter("", 0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 1229, ((byte) KeyEvent.getModifierMetaStateMask()) + 50, objArr12);
                            o.ee.g.d(intern6, ((String) objArr12[0]).intern());
                            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Unexpected;
                            Object[] objArr13 = new Object[1];
                            m((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 165 - Color.argb(0, 0, 0, 0), KeyEvent.keyCodeFromString("") + 18, objArr13);
                            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr13[0]).intern());
                        }
                        break;
                }
        }
        o.bc.e eVar3 = new o.bc.e(this.d, this, this.b);
        this.g = eVar3;
        eVar3.c(eVar);
        int i4 = l + 31;
        n = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void i() {
        int i = l + Opcodes.LMUL;
        n = i % 128;
        switch (i % 2 != 0) {
            case false:
                this.c.onProvisioningPending(null);
                throw null;
            default:
                this.c.onProvisioningPending(null);
                return;
        }
    }

    @Override // o.bc.e.InterfaceC0030e
    public final void b() {
        int i = n + 37;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (ViewConfiguration.getScrollBarSize() >> 8), 7 - View.getDefaultSize(0, 0), View.resolveSizeAndState(0, 0, 0) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (59013 - View.combineMeasuredStates(0, 0)), AndroidCharacter.getMirror('0') + 1230, View.resolveSize(0, 0) + 28, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (this.c != null ? 'O' : '1') {
            case Opcodes.IASTORE /* 79 */:
                Handler handler = this.i;
                switch (handler != null) {
                    case true:
                        int i3 = l + 67;
                        n = i3 % 128;
                        int i4 = i3 % 2;
                        handler.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda0
                            @Override // java.lang.Runnable
                            public final void run() {
                                d.this.i();
                            }
                        });
                        break;
                }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void f() {
        int i = n + 31;
        l = i % 128;
        switch (i % 2 != 0) {
            case true:
                this.c.onProvisioningSuccess(null);
                throw null;
            default:
                this.c.onProvisioningSuccess(null);
                return;
        }
    }

    @Override // o.bc.e.InterfaceC0030e
    public final void d() {
        int i = n + 77;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), Color.argb(0, 0, 0, 0) + 7, 23 - View.getDefaultSize(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (24735 - View.MeasureSpec.getMode(0)), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1305, 39 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.g = null;
        switch (this.c == null) {
            case true:
                break;
            default:
                int i3 = n + 109;
                l = i3 % 128;
                int i4 = i3 % 2;
                Handler handler = this.i;
                switch (handler == null ? 'B' : (char) 7) {
                    case 7:
                        handler.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda6
                            @Override // java.lang.Runnable
                            public final void run() {
                                d.this.f();
                            }
                        });
                        break;
                }
        }
        int i5 = n + 33;
        l = i5 % 128;
        int i6 = i5 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void a(o.bb.d dVar) {
        int i = n + 89;
        l = i % 128;
        switch (i % 2 != 0 ? (char) 14 : '\\') {
            case 14:
                this.c.onProvisioningError(o.bv.c.c(dVar).d(), null);
                throw null;
            default:
                this.c.onProvisioningError(o.bv.c.c(dVar).d(), null);
                return;
        }
    }

    @Override // o.bc.e.InterfaceC0030e
    public final void c(final o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (TextUtils.lastIndexOf("", '0', 0) + 1), 7 - (ViewConfiguration.getFadingEdgeLength() >> 16), TextUtils.lastIndexOf("", '0') + 24, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) TextUtils.getOffsetAfter("", 0), TextUtils.lastIndexOf("", '0', 0, 0) + 1345, (ViewConfiguration.getJumpTapTimeout() >> 16) + 48, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(dVar.d());
        Object[] objArr3 = new Object[1];
        m((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 21415), (ViewConfiguration.getPressedStateDuration() >> 16) + 1392, (ViewConfiguration.getFadingEdgeLength() >> 16) + 2, objArr3);
        StringBuilder append2 = append.append(((String) objArr3[0]).intern()).append(dVar.e());
        Object[] objArr4 = new Object[1];
        m((char) (14089 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (ViewConfiguration.getTapTimeout() >> 16) + 1394, 1 - Color.red(0), objArr4);
        o.ee.g.d(intern, append2.append(((String) objArr4[0]).intern()).toString());
        c.c().e(this.d);
        this.g = null;
        if (this.c != null) {
            int i = n + 33;
            int i2 = i % 128;
            l = i2;
            switch (i % 2 == 0) {
                case false:
                    int i3 = 90 / 0;
                    if (this.i == null) {
                        return;
                    }
                    break;
                default:
                    if (this.i == null) {
                        return;
                    }
                    break;
            }
            int i4 = i2 + 75;
            n = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    this.i.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda2
                        @Override // java.lang.Runnable
                        public final void run() {
                            d.this.a(dVar);
                        }
                    });
                    throw null;
                default:
                    this.i.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda2
                        @Override // java.lang.Runnable
                        public final void run() {
                            d.this.a(dVar);
                        }
                    });
                    return;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void c(boolean z, List list) {
        int i = n + 51;
        l = i % 128;
        int i2 = i % 2;
        this.c.onDeviceEligible(z, o.b(list), null);
        int i3 = n + 67;
        l = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                int i4 = 26 / 0;
                return;
        }
    }

    @Override // o.be.d.c
    public final void a(final boolean z, final List<a> list) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getJumpTapTimeout() >> 16) + 7, TextUtils.indexOf("", "") + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getFadingEdgeLength() >> 16), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1396, 34 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z).toString());
        switch (this.c != null) {
            case true:
                Handler handler = this.i;
                switch (handler != null ? ':' : 'S') {
                    case Opcodes.ASTORE /* 58 */:
                        handler.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda3
                            @Override // java.lang.Runnable
                            public final void run() {
                                d.this.c(z, list);
                            }
                        });
                        int i = n + 97;
                        l = i % 128;
                        if (i % 2 != 0) {
                            break;
                        }
                        break;
                }
        }
        int i2 = l + 35;
        n = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(EligibilityDenialReason eligibilityDenialReason, String str) {
        int i = n + 31;
        l = i % 128;
        switch (i % 2 != 0 ? 'B' : (char) 14) {
            case 14:
                this.c.onDeviceNotEligible(eligibilityDenialReason, null, str);
                return;
            default:
                this.c.onDeviceNotEligible(eligibilityDenialReason, null, str);
                int i2 = 95 / 0;
                return;
        }
    }

    @Override // o.be.d.c
    public final void e(final EligibilityDenialReason eligibilityDenialReason, final String str) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) (AndroidCharacter.getMirror('0') - '0'), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 7, 23 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 1429 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 30 - KeyEvent.getDeadChar(0, 0), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(eligibilityDenialReason);
        Object[] objArr3 = new Object[1];
        m((char) Color.alpha(0), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1459, 21 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr3);
        o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).append(str).toString());
        if (this.c != null) {
            int i = l + 27;
            n = i % 128;
            int i2 = i % 2;
            Handler handler = this.i;
            switch (handler == null) {
                case false:
                    handler.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda4
                        @Override // java.lang.Runnable
                        public final void run() {
                            d.this.b(eligibilityDenialReason, str);
                        }
                    });
                    int i3 = l + 45;
                    n = i3 % 128;
                    if (i3 % 2 == 0) {
                        break;
                    }
                    break;
            }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void e(o.bb.d dVar) {
        int i = l + 71;
        n = i % 128;
        int i2 = i % 2;
        this.c.onCheckEligibilityError(o.bv.c.c(dVar).d(), null);
        int i3 = l + 73;
        n = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.be.d.c
    public final void d(final o.bb.d dVar) {
        int i = l + 79;
        n = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        m((char) ((Process.getThreadPriority(0) + 20) >> 6), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 7, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m((char) (ViewConfiguration.getTapTimeout() >> 16), 1480 - View.combineMeasuredStates(0, 0), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 22, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (this.c != null ? '.' : '\b') {
            case '.':
                int i3 = n + 77;
                l = i3 % 128;
                if (i3 % 2 != 0) {
                    throw null;
                }
                Handler handler = this.i;
                switch (handler != null ? (char) 26 : '/') {
                    case '/':
                        break;
                    default:
                        handler.post(new Runnable() { // from class: o.ei.d$$ExternalSyntheticLambda1
                            @Override // java.lang.Runnable
                            public final void run() {
                                d.this.e(dVar);
                            }
                        });
                        break;
                }
        }
        int i4 = l + 59;
        n = i4 % 128;
        int i5 = i4 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 584
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ei.d.m(char, int, int, java.lang.Object[]):void");
    }
}
