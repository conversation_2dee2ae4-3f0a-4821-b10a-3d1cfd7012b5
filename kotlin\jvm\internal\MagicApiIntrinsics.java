package kotlin.jvm.internal;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\internal\MagicApiIntrinsics.smali */
public class MagicApiIntrinsics {
    public static void voidMagicApiCall(Object data) {
    }

    public static <T> T anyMagicApiCall(int id) {
        return null;
    }

    public static void voidMagicApiCall(int id) {
    }

    public static int intMagicApiCall(int id) {
        return 0;
    }

    public static <T> T anyMagicApiCall(Object data) {
        return null;
    }

    public static int intMagicApiCall(Object data) {
        return 0;
    }

    public static int intMagicApiCall(int id, long longData, Object anyData) {
        return 0;
    }

    public static int intMagicApiCall(int id, long longData1, long longData2, Object anyData) {
        return 0;
    }

    public static int intMagicApiCall(int id, Object anyData1, Object anyData2) {
        return 0;
    }

    public static int intMagicApiCall(int id, Object anyData1, Object anyData2, Object anyData3, Object anyData4) {
        return 0;
    }

    public static <T> T anyMagicApiCall(int id, long longData, Object anyData) {
        return null;
    }

    public static <T> T anyMagicApiCall(int id, long longData1, long longData2, Object anyData) {
        return null;
    }

    public static <T> T anyMagicApiCall(int id, Object anyData1, Object anyData2) {
        return null;
    }

    public static <T> T anyMagicApiCall(int id, Object anyData1, Object anyData2, Object anyData3, Object anyData4) {
        return null;
    }
}
