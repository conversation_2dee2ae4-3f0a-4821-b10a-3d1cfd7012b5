package o.v;

import o.al.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\o.smali */
public final class o extends b {
    public static final byte[] $$j = null;
    public static final int $$k = 0;
    private static int $10;
    private static int $11;
    private static char[] h;
    private static int i;

    /* renamed from: o, reason: collision with root package name */
    private static int f110o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f110o = 1;
        h = new char[]{50938, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50854, 50861, 50858, 50851, 50916, 50833, 50857, 50849, 50878, 50854, 50833, 50860, 50848, 50853, 50852, 50861, 50857, 50873, 50876, 50855, 50852, 50852, 50919, 50860, 50852, 50853, 50848};
    }

    static void init$0() {
        $$j = new byte[]{114, 12, -103, 122};
        $$k = 29;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void y(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r6 = r6 * 2
            int r6 = r6 + 4
            byte[] r0 = o.v.o.$$j
            int r7 = 122 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L31
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L31:
            int r6 = r6 + 1
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.o.y(int, int, byte, java.lang.Object[]):void");
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public o(boolean r6, o.eo.e r7, o.eo.f r8) {
        /*
            r5 = this;
            r0 = 17
            r1 = 0
            int[] r0 = new int[]{r1, r0, r1, r0}
            r2 = 1
            java.lang.Object[] r3 = new java.lang.Object[r2]
            java.lang.String r4 = "\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001"
            w(r4, r0, r2, r3)
            r0 = r3[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r5.<init>(r0, r7, r6, r8)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.o.<init>(boolean, o.eo.e, o.eo.f):void");
    }

    @Override // o.v.b
    public final c.e a() {
        int i2 = i + 67;
        f110o = i2 % 128;
        int i3 = i2 % 2;
        c.e eVar = c.e.a;
        int i4 = i + 49;
        f110o = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i2 = f110o + 33;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                w("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{17, 18, 0, 0}, true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                w("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{17, 18, 0, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = i + 31;
        f110o = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0031, code lost:
    
        if (t().n() == o.eo.f.d.d) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0034, code lost:
    
        d(r6, r7);
        r6 = o.v.o.i + 9;
        o.v.o.f110o = r6 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0041, code lost:
    
        if ((r6 % 2) != 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0044, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0045, code lost:
    
        switch(r1) {
            case 0: goto L22;
            default: goto L23;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0049, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x004b, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x002b, code lost:
    
        if (t().n() == o.eo.f.d.d) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(android.content.Context r6, o.p.g r7) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r5 = this;
            int r0 = o.v.o.f110o
            int r0 = r0 + 7
            int r1 = r0 % 128
            o.v.o.i = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 87
            goto L11
        Lf:
            r0 = 60
        L11:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 60: goto L21;
                default: goto L16;
            }
        L16:
            o.eo.f r0 = r5.t()
            o.eo.f$d r0 = r0.n()
            o.eo.f$d r3 = o.eo.f.d.d
            goto L2e
        L21:
            o.eo.f r0 = r5.t()
            o.eo.f$d r0 = r0.n()
            o.eo.f$d r3 = o.eo.f.d.d
            if (r0 != r3) goto L4e
        L2d:
            goto L34
        L2e:
            r4 = 29
            int r4 = r4 / r2
            if (r0 != r3) goto L4e
            goto L2d
        L34:
            r5.d(r6, r7)
            int r6 = o.v.o.i
            int r6 = r6 + 9
            int r7 = r6 % 128
            o.v.o.f110o = r7
            int r6 = r6 % 2
            if (r6 != 0) goto L44
            goto L45
        L44:
            r1 = r2
        L45:
            switch(r1) {
                case 0: goto L49;
                default: goto L48;
            }
        L48:
            goto L4a
        L49:
            return
        L4a:
            r6 = 0
            throw r6     // Catch: java.lang.Throwable -> L4c
        L4c:
            r6 = move-exception
            throw r6
        L4e:
            fr.antelop.sdk.exception.WalletValidationException r6 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r7 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            r0 = 35
            r3 = 5
            int[] r0 = new int[]{r0, r3, r2, r1}
            java.lang.Object[] r3 = new java.lang.Object[r1]
            java.lang.String r4 = "\u0000\u0000\u0001\u0000\u0000"
            w(r4, r0, r1, r3)
            r0 = r3[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r6.<init>(r7, r0)
            throw r6
        L6c:
            r6 = move-exception
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.o.e(android.content.Context, o.p.g):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:106:0x02ea, code lost:
    
        r1 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(java.lang.String r22, int[] r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 834
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.o.w(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
