package fr.antelop.exposed;

import android.content.pm.ApplicationInfo;
import android.content.pm.PackageItemInfo;
import android.content.res.AssetFileDescriptor;
import android.database.Cursor;
import android.database.MatrixCursor;
import android.graphics.Color;
import android.graphics.Point;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.CancellationSignal;
import android.os.ParcelFileDescriptor;
import android.os.Process;
import android.os.SystemClock;
import android.provider.DocumentsProvider;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.R;
import java.io.File;
import java.io.FileNotFoundException;
import java.lang.reflect.Method;
import o.a.h;
import o.e.a;
import o.ee.g;
import o.ee.o;
import o.fl.d;
import org.bouncycastle.i18n.ErrorBundle;
import org.bouncycastle.i18n.MessageBundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\exposed\AntelopLogProvider.smali */
public class AntelopLogProvider extends DocumentsProvider {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10 = 0;
    private static int $11 = 0;
    private static final String[] DEFAULT_DOCUMENT_PROJECTION;
    private static final String[] DEFAULT_ROOT_PROJECTION;
    private static final String LOG_MIME_TYPE = "text/plain";
    private static final String PROVIDER_AUTHORITY = "fr.antelop.sdk";
    private static final String ROOT_CHILD_MIME_TYPES = "text/*\n";
    private static final String ROOT_ID_PREFIX = "AntelopSDKLogProviderRootId";
    private static final String ROOT_SUMMARY = "Debug log files";
    private static final String ROOT_TITLE_SUFFIX = " - Antelop SDK logs";
    private static final String TAG = "AntelopLoggingDocumentProvider";
    private static int a;
    private static int c;
    private static int e;
    private String appName;
    private File baseLoggingDir;

    static void c() {
        c = 874635373;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            byte[] r0 = fr.antelop.exposed.AntelopLogProvider.$$a
            int r6 = r6 * 2
            int r6 = r6 + 107
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L38
        L1c:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L20:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2f
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2f:
            r3 = r0[r6]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L38:
            int r6 = -r6
            int r7 = r7 + r6
            int r6 = r8 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L20
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.exposed.AntelopLogProvider.f(short, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = Opcodes.IF_ICMPGE;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        c();
        DEFAULT_ROOT_PROJECTION = new String[]{"root_id", MessageBundle.TITLE_ENTRY, ErrorBundle.SUMMARY_ENTRY, "flags", "document_id", "mime_types", "available_bytes", "icon"};
        DEFAULT_DOCUMENT_PROJECTION = new String[]{"document_id", "_display_name", "mime_type", "last_modified", "flags", "_size"};
        int i = e + 19;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // android.content.ContentProvider
    public boolean onCreate() {
        String string;
        g.c();
        g.d(TAG, "onCreate");
        d.a(getContext());
        this.baseLoggingDir = new File(getContext().getFilesDir(), "logs");
        ApplicationInfo applicationInfo = getContext().getApplicationInfo();
        int i = ((PackageItemInfo) applicationInfo).labelRes;
        switch (i != 0) {
            case true:
                string = getContext().getString(i);
                int i2 = e + 77;
                a = i2 % 128;
                int i3 = i2 % 2;
                break;
            default:
                int i4 = a + 53;
                e = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        string = ((PackageItemInfo) applicationInfo).nonLocalizedLabel.toString();
                        break;
                    default:
                        ((PackageItemInfo) applicationInfo).nonLocalizedLabel.toString();
                        throw null;
                }
        }
        this.appName = string;
        return true;
    }

    @Override // android.provider.DocumentsProvider
    public Cursor queryRoots(String[] strArr) throws FileNotFoundException {
        String d;
        g.c();
        StringBuilder sb = new StringBuilder("queryRoots - projection: ");
        if (strArr == null) {
            d = "null";
        } else {
            Object[] objArr = new Object[1];
            d((Process.myTid() >> 22) + 1, "\u0000", 1 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 142 - Drawable.resolveOpacity(0, 0), false, objArr);
            d = o.d(((String) objArr[0]).intern(), (Object[]) strArr);
        }
        g.d(TAG, sb.append(d).toString());
        if (strArr == null) {
            strArr = DEFAULT_ROOT_PROJECTION;
        }
        MatrixCursor matrixCursor = new MatrixCursor(strArr);
        MatrixCursor.RowBuilder newRow = matrixCursor.newRow();
        newRow.add("root_id", new StringBuilder(ROOT_ID_PREFIX).append(this.appName).toString());
        newRow.add(MessageBundle.TITLE_ENTRY, new StringBuilder().append(this.appName).append(ROOT_TITLE_SUFFIX).toString());
        newRow.add(ErrorBundle.SUMMARY_ENTRY, ROOT_SUMMARY);
        newRow.add("flags", 0);
        newRow.add("document_id", getDocIdForFile(this.baseLoggingDir));
        newRow.add("mime_types", ROOT_CHILD_MIME_TYPES);
        newRow.add("available_bytes", Long.valueOf(this.baseLoggingDir.getFreeSpace()));
        newRow.add("icon", Integer.valueOf(R.drawable.ic_document_provider_logo));
        return matrixCursor;
    }

    @Override // android.provider.DocumentsProvider
    public Cursor queryDocument(String str, String[] strArr) throws FileNotFoundException {
        String d;
        g.c();
        StringBuilder append = new StringBuilder("queryDocument - documentId: ").append(str).append("  projection: ");
        if (strArr == null) {
            d = "null";
        } else {
            Object[] objArr = new Object[1];
            d((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "\u0000", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 1, TextUtils.indexOf((CharSequence) "", '0') + Opcodes.D2L, false, objArr);
            d = o.d(((String) objArr[0]).intern(), (Object[]) strArr);
        }
        g.d(TAG, append.append(d).toString());
        if (strArr == null) {
            strArr = DEFAULT_DOCUMENT_PROJECTION;
        }
        MatrixCursor matrixCursor = new MatrixCursor(strArr);
        if (!documentIsLogsFolder(str)) {
            g.c();
            g.d(TAG, new StringBuilder("queryDocument - documentId is not `").append(this.baseLoggingDir.getName()).append("` folder nothing to return").toString());
            insertFileInfo(matrixCursor, getFileForDocId(str));
            return matrixCursor;
        }
        MatrixCursor.RowBuilder newRow = matrixCursor.newRow();
        newRow.add("document_id", str);
        newRow.add("_display_name", this.baseLoggingDir.getName());
        newRow.add("_size", Long.valueOf(this.baseLoggingDir.length()));
        newRow.add("mime_type", "vnd.android.document/directory");
        newRow.add("last_modified", Long.valueOf(this.baseLoggingDir.lastModified()));
        newRow.add("flags", 0);
        return matrixCursor;
    }

    @Override // android.provider.DocumentsProvider
    public Cursor queryChildDocuments(String str, String[] strArr, String str2) throws FileNotFoundException {
        String d;
        g.c();
        StringBuilder append = new StringBuilder("queryChildDocuments - parentDocumentId: ").append(str).append("  projection: ");
        if (strArr == null) {
            d = "null";
        } else {
            Object[] objArr = new Object[1];
            d(1 - View.getDefaultSize(0, 0), "\u0000", 1 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + Opcodes.D2I, false, objArr);
            d = o.d(((String) objArr[0]).intern(), (Object[]) strArr);
        }
        g.d(TAG, append.append(d).append("  sortOrder: ").append(str2).toString());
        if (strArr == null) {
            strArr = DEFAULT_DOCUMENT_PROJECTION;
        }
        MatrixCursor matrixCursor = new MatrixCursor(strArr);
        if (!documentIsLogsFolder(str)) {
            g.c();
            g.d(TAG, new StringBuilder("queryChildDocuments - documentId is not `").append(this.baseLoggingDir.getName()).append("` folder nothing to return").toString());
            return matrixCursor;
        }
        File[] listFiles = this.baseLoggingDir.listFiles();
        if (listFiles == null) {
            return matrixCursor;
        }
        for (File file : listFiles) {
            insertFileInfo(matrixCursor, file);
        }
        return matrixCursor;
    }

    private void insertFileInfo(MatrixCursor matrixCursor, File file) {
        int i = e + 11;
        a = i % 128;
        int i2 = i % 2;
        MatrixCursor.RowBuilder newRow = matrixCursor.newRow();
        newRow.add("document_id", getDocIdForFile(file));
        newRow.add("_display_name", file.getName());
        newRow.add("_size", Long.valueOf(file.length()));
        newRow.add("mime_type", "text/plain");
        newRow.add("last_modified", Long.valueOf(file.lastModified()));
        newRow.add("flags", 1);
        newRow.add("flags", 4);
        int i3 = e + 39;
        a = i3 % 128;
        switch (i3 % 2 != 0 ? 'P' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // android.provider.DocumentsProvider
    public Cursor queryRecentDocuments(String str, String[] strArr) throws FileNotFoundException {
        String d;
        g.c();
        StringBuilder append = new StringBuilder("queryRecentDocuments - rootId: ").append(str).append("  projection: ");
        switch (strArr != null) {
            case true:
                Object[] objArr = new Object[1];
                d(1 - View.getDefaultSize(0, 0), "\u0000", 1 - TextUtils.getTrimmedLength(""), 142 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), false, objArr);
                d = o.d(((String) objArr[0]).intern(), (Object[]) strArr);
                break;
            default:
                int i = e + 79;
                a = i % 128;
                boolean z = i % 2 == 0;
                d = "null";
                switch (z) {
                    case false:
                        int i2 = 46 / 0;
                        break;
                }
        }
        g.d(TAG, append.append(d).toString());
        int i3 = e + 69;
        a = i3 % 128;
        if (i3 % 2 == 0) {
            return null;
        }
        int i4 = 65 / 0;
        return null;
    }

    @Override // android.provider.DocumentsProvider
    public ParcelFileDescriptor openDocument(String str, String str2, CancellationSignal cancellationSignal) throws FileNotFoundException {
        int i = a + Opcodes.DMUL;
        e = i % 128;
        switch (i % 2 == 0 ? (char) 17 : '0') {
            case '0':
                g.c();
                g.d(TAG, "openDocument - documentId: ".concat(String.valueOf(str)));
                return ParcelFileDescriptor.open(getFileForDocId(str), ParcelFileDescriptor.parseMode(str2));
            default:
                g.c();
                g.d(TAG, "openDocument - documentId: ".concat(String.valueOf(str)));
                ParcelFileDescriptor.open(getFileForDocId(str), ParcelFileDescriptor.parseMode(str2));
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // android.provider.DocumentsProvider
    public AssetFileDescriptor openDocumentThumbnail(String str, Point point, CancellationSignal cancellationSignal) throws FileNotFoundException {
        g.c();
        g.d(TAG, "openDocumentThumbnail - documentId: ".concat(String.valueOf(str)));
        AssetFileDescriptor assetFileDescriptor = new AssetFileDescriptor(ParcelFileDescriptor.open(getFileForDocId(str), 268435456), 0L, -1L);
        int i = a + 35;
        e = i % 128;
        int i2 = i % 2;
        return assetFileDescriptor;
    }

    @Override // android.provider.DocumentsProvider
    public String getDocumentType(String str) throws FileNotFoundException {
        int i = a + Opcodes.DSUB;
        e = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? 'H' : 'J') {
            case 'H':
                g.c();
                g.d(TAG, "getDocumentType - documentId: ".concat(String.valueOf(str)));
                documentIsLogsFolder(str);
                obj.hashCode();
                throw null;
            default:
                g.c();
                g.d(TAG, "getDocumentType - documentId: ".concat(String.valueOf(str)));
                if (!documentIsLogsFolder(str)) {
                    return "text/plain";
                }
                int i2 = a + 39;
                e = i2 % 128;
                switch (i2 % 2 == 0 ? ';' : 'W') {
                    case Opcodes.POP /* 87 */:
                        return "vnd.android.document/directory";
                    default:
                        throw null;
                }
        }
    }

    @Override // android.provider.DocumentsProvider
    public void deleteDocument(String str) throws FileNotFoundException {
        File fileForDocId;
        int i = e + 37;
        a = i % 128;
        switch (i % 2 != 0) {
            case true:
                fileForDocId = getFileForDocId(str);
                int i2 = 32 / 0;
                if (!fileForDocId.exists()) {
                    return;
                }
                break;
            default:
                fileForDocId = getFileForDocId(str);
                if (!fileForDocId.exists()) {
                    return;
                }
                break;
        }
        switch (fileForDocId.delete()) {
            case false:
                int i3 = e + Opcodes.DREM;
                a = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                g.e(TAG, "deleteDocument - Unable to delete: ".concat(String.valueOf(str)));
                return;
            default:
                return;
        }
    }

    private File getFileForDocId(String str) {
        File file = new File(this.baseLoggingDir, str.replace("root:", ""));
        int i = a + 43;
        e = i % 128;
        int i2 = i % 2;
        return file;
    }

    private String getDocIdForFile(File file) {
        int i = e + 5;
        a = i % 128;
        Object obj = null;
        if (i % 2 != 0) {
            file.equals(this.baseLoggingDir);
            obj.hashCode();
            throw null;
        }
        if (file.equals(this.baseLoggingDir)) {
            int i2 = e + 47;
            a = i2 % 128;
            if (i2 % 2 == 0) {
                return "root:";
            }
            obj.hashCode();
            throw null;
        }
        String obj2 = new StringBuilder("root:").append(file.getName()).toString();
        int i3 = a + 1;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 14 : 'O') {
            case Opcodes.IASTORE /* 79 */:
                return obj2;
            default:
                int i4 = 35 / 0;
                return obj2;
        }
    }

    private boolean documentIsLogsFolder(String str) {
        int i = e + 73;
        a = i % 128;
        int i2 = i % 2;
        boolean equals = str.equals("root:");
        int i3 = e + 31;
        a = i3 % 128;
        switch (i3 % 2 != 0 ? '\\' : '=') {
            case Opcodes.DUP2 /* 92 */:
                throw null;
            default:
                return equals;
        }
    }

    private static void d(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        int i4 = $10 + 45;
        int i5 = i4 % 128;
        $11 = i5;
        int i6 = i4 % 2;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                int i7 = i5 + 3;
                $10 = i7 % 128;
                if (i7 % 2 != 0) {
                }
                cArr = str.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        h hVar = new h();
        char[] cArr4 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            int i8 = $10 + 93;
            $11 = i8 % 128;
            int i9 = i8 % 2;
            hVar.b = cArr3[hVar.a];
            cArr4[hVar.a] = (char) (i3 + hVar.b);
            int i10 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr4[i10]), Integer.valueOf(c)};
                Object obj = a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) a.c(((byte) KeyEvent.getModifierMetaStateMask()) + 13, (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 459 - Color.argb(0, 0, 0, 0));
                    byte b = (byte) 0;
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    f(b, b2, b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    a.s.put(2038615114, obj);
                }
                cArr4[i10] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) a.c(11 - View.combineMeasuredStates(0, 0), (char) Drawable.resolveOpacity(0, 0), 313 - Drawable.resolveOpacity(0, 0));
                        byte b3 = (byte) 1;
                        byte b4 = (byte) (b3 - 1);
                        Object[] objArr5 = new Object[1];
                        f(b3, b4, b4, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        switch (i > 0 ? (char) 24 : 'Y') {
            case Opcodes.DUP /* 89 */:
                break;
            default:
                int i11 = $10 + 61;
                $11 = i11 % 128;
                int i12 = i11 % 2;
                hVar.c = i;
                char[] cArr5 = new char[i2];
                System.arraycopy(cArr4, 0, cArr5, 0, i2);
                System.arraycopy(cArr5, 0, cArr4, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr5, hVar.c, cArr4, 0, i2 - hVar.c);
                break;
        }
        switch (!z) {
            case true:
                break;
            default:
                int i13 = $10 + 69;
                $11 = i13 % 128;
                if (i13 % 2 == 0) {
                    cArr2 = new char[i2];
                    hVar.a = 1;
                } else {
                    cArr2 = new char[i2];
                    hVar.a = 0;
                }
                while (hVar.a < i2) {
                    int i14 = $11 + 95;
                    $10 = i14 % 128;
                    int i15 = i14 % 2;
                    cArr2[hVar.a] = cArr4[(i2 - hVar.a) - 1];
                    try {
                        Object[] objArr6 = {hVar, hVar};
                        Object obj3 = a.s.get(-1412673904);
                        if (obj3 == null) {
                            Class cls3 = (Class) a.c(11 - View.resolveSizeAndState(0, 0, 0), (char) View.MeasureSpec.getSize(0), 314 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                            byte b5 = (byte) 1;
                            byte b6 = (byte) (b5 - 1);
                            Object[] objArr7 = new Object[1];
                            f(b5, b6, b6, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            a.s.put(-1412673904, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        int i16 = $10 + 71;
                        $11 = i16 % 128;
                        int i17 = i16 % 2;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                cArr4 = cArr2;
                break;
        }
        objArr[0] = new String(cArr4);
    }
}
