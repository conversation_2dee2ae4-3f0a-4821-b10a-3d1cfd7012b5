package androidx.work.impl;

import androidx.work.impl.model.WorkSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\Scheduler.smali */
public interface Scheduler {
    public static final int MAX_GREEDY_SCHEDULER_LIMIT = 200;
    public static final int MAX_SCHEDULER_LIMIT = 50;

    void cancel(String workSpecId);

    boolean hasLimitedSchedulingSlots();

    void schedule(WorkSpec... workSpecs);
}
