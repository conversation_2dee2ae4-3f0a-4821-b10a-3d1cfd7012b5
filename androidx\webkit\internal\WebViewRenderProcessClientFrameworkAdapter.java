package androidx.webkit.internal;

import android.webkit.WebView;
import android.webkit.WebViewRenderProcess;
import android.webkit.WebViewRenderProcessClient;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\webkit\internal\WebViewRenderProcessClientFrameworkAdapter.smali */
public class WebViewRenderProcessClientFrameworkAdapter extends WebViewRenderProcessClient {
    private androidx.webkit.WebViewRenderProcessClient mWebViewRenderProcessClient;

    public WebViewRenderProcessClientFrameworkAdapter(androidx.webkit.WebViewRenderProcessClient client) {
        this.mWebViewRenderProcessClient = client;
    }

    @Override // android.webkit.WebViewRenderProcessClient
    public void onRenderProcessUnresponsive(WebView view, WebViewRenderProcess renderer) {
        this.mWebViewRenderProcessClient.onRenderProcessUnresponsive(view, WebViewRenderProcessImpl.forFrameworkObject(renderer));
    }

    @Override // android.webkit.WebViewRenderProcessClient
    public void onRenderProcessResponsive(WebView view, WebViewRenderProcess renderer) {
        this.mWebViewRenderProcessClient.onRenderProcessResponsive(view, WebViewRenderProcessImpl.forFrameworkObject(renderer));
    }

    public androidx.webkit.WebViewRenderProcessClient getFrameworkRenderProcessClient() {
        return this.mWebViewRenderProcessClient;
    }
}
