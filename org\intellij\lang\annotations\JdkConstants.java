package org.intellij.lang.annotations;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants.smali */
public class JdkConstants {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$AdjustableOrientation.smali */
    public @interface AdjustableOrientation {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$BoxLayoutAxis.smali */
    public @interface BoxLayoutAxis {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$CalendarMonth.smali */
    public @interface CalendarMonth {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$CursorType.smali */
    public @interface CursorType {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$FlowLayoutAlignment.smali */
    public @interface FlowLayoutAlignment {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$HorizontalAlignment.smali */
    public @interface HorizontalAlignment {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$InputEventMask.smali */
    public @interface InputEventMask {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$ListSelectionMode.smali */
    public @interface ListSelectionMode {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$PatternFlags.smali */
    public @interface PatternFlags {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$TabLayoutPolicy.smali */
    public @interface TabLayoutPolicy {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$TitledBorderJustification.smali */
    public @interface TitledBorderJustification {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$TitledBorderTitlePosition.smali */
    public @interface TitledBorderTitlePosition {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\intellij\lang\annotations\JdkConstants$TreeSelectionMode.smali */
    public @interface TreeSelectionMode {
    }
}
