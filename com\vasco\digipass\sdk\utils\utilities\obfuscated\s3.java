package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s3.smali */
public class s3 extends u {
    private static final byte[] L = {-87, -42, -21, 69, -15, 60, 112, -126, ByteCompanionObject.MIN_VALUE, -60, -106, 123, 35, 31, 94, -83, -10, 88, -21, -92, -64, 55, 41, 29, 56, -39, 107, -16, 37, -54, 78, 23, -8, -23, 114, 13, -58, 21, -76, 58, 40, -105, 95, 11, -63, -34, -93, 100, 56, -75, 100, -22, 44, 23, -97, -48, 18, 62, 109, -72, -6, -59, 121, 4};
    private byte[] C = L;
    private w b;
    private q3 x;

    public s3(w wVar) {
        this.b = wVar;
    }

    public static s3 a(Object obj) {
        if (obj instanceof s3) {
            return (s3) obj;
        }
        if (obj == null) {
            throw new IllegalArgumentException("object parse error");
        }
        e0 a = e0.a(obj);
        s3 s3Var = a.a(0) instanceof w ? new s3(w.a(a.a(0))) : new s3(q3.a(a.a(0)));
        if (a.size() == 2) {
            byte[] h = x.a(a.a(1)).h();
            s3Var.C = h;
            if (h.length != L.length) {
                throw new IllegalArgumentException("object parse error");
            }
        }
        return s3Var;
    }

    public q3 e() {
        return this.x;
    }

    public w f() {
        return this.b;
    }

    public boolean g() {
        return this.b != null;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        w wVar = this.b;
        if (wVar != null) {
            iVar.a(wVar);
        } else {
            iVar.a(this.x);
        }
        if (!Arrays.areEqual(this.C, L)) {
            iVar.a(new f2(this.C));
        }
        return new j2(iVar);
    }

    public s3(q3 q3Var) {
        this.x = q3Var;
    }
}
