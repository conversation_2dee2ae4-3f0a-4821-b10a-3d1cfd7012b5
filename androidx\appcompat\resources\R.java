package androidx.appcompat.resources;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\appcompat\resources\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\appcompat\resources\R$drawable.smali */
    public static final class drawable {
        public static int abc_vector_test = 0x7f0800c4;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\appcompat\resources\R$styleable.smali */
    public static final class styleable {
        public static int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static int StateListDrawableItem_android_drawable = 0x00000000;
        public static int StateListDrawable_android_constantSize = 0x00000003;
        public static int StateListDrawable_android_dither = 0x00000000;
        public static int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static int StateListDrawable_android_variablePadding = 0x00000002;
        public static int StateListDrawable_android_visible = 0x00000001;
        public static int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] StateListDrawableItem = {android.R.attr.drawable};

        private styleable() {
        }
    }

    private R() {
    }
}
