package o.v;

import android.content.Context;
import android.media.AudioTrack;
import android.os.CancellationSignal;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;
import o.as.c;
import o.eo.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\m.smali */
public final class m extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int[] k;
    private static int q;
    private static int r;
    o.dw.e h;
    final o.dw.b i;
    private final o.eo.j l;
    private final boolean m;

    /* renamed from: o, reason: collision with root package name */
    CancellationSignal f108o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        q = 0;
        r = 1;
        t();
        ViewConfiguration.getMinimumFlingVelocity();
        int i = q + 7;
        r = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{12, 95, -121};
        $$e = 97;
    }

    static void t() {
        k = new int[]{-946877434, -789380726, -647741920, 560671212, 1597389217, -377174538, 1944827309, 319169338, 1226335284, 76970534, -1588725338, 47952950, 685972093, 1209608705, 96836067, 1208269495, -999629625, 98372057};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x003a). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r7 = r7 * 4
            int r7 = r7 + 3
            byte[] r0 = o.v.m.$$d
            int r9 = r9 + 115
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            r8 = r7
            goto L3a
        L1a:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r9
            r9 = r5
        L1f:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L3a:
            int r7 = r7 + 1
            int r9 = -r9
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.m.v(int, short, byte, java.lang.Object[]):void");
    }

    static /* synthetic */ o.p.g a(m mVar) {
        int i = r + 61;
        q = i % 128;
        int i2 = i % 2;
        o.p.g l = mVar.l();
        int i3 = r + Opcodes.LREM;
        q = i3 % 128;
        int i4 = i3 % 2;
        return l;
    }

    static /* synthetic */ o.p.g b(m mVar) {
        int i = q + Opcodes.DDIV;
        r = i % 128;
        char c = i % 2 == 0 ? Typography.less : (char) 24;
        o.p.g l = mVar.l();
        switch (c) {
            default:
                int i2 = 79 / 0;
            case 24:
                return l;
        }
    }

    static /* synthetic */ o.p.g c(m mVar) {
        int i = q + 19;
        r = i % 128;
        int i2 = i % 2;
        o.p.g l = mVar.l();
        int i3 = r + 9;
        q = i3 % 128;
        switch (i3 % 2 != 0 ? 'Q' : (char) 18) {
            case Opcodes.FASTORE /* 81 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l;
        }
    }

    static /* synthetic */ void d(m mVar) {
        int i = q + 75;
        r = i % 128;
        int i2 = i % 2;
        mVar.n();
        int i3 = q + 67;
        r = i3 % 128;
        switch (i3 % 2 == 0 ? '/' : '.') {
            case '.':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ o.p.g e(m mVar) {
        int i = q + 87;
        r = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? (char) 4 : (char) 15) {
            case 15:
                o.p.g l = mVar.l();
                int i2 = r + 61;
                q = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 3 : (char) 0) {
                    case 3:
                        obj.hashCode();
                        throw null;
                    default:
                        return l;
                }
            default:
                mVar.l();
                obj.hashCode();
                throw null;
        }
    }

    public m(String str, o.eo.e eVar, boolean z, o.dw.b bVar, o.eo.j jVar) {
        super(str, eVar);
        this.m = z;
        this.i = bVar;
        this.l = jVar;
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i = q + 89;
        r = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                u(new int[]{652952188, -184984365, 1442378921, 1757451057, 249725928, 1007238791, -1149637499, -1979292655, -1554601581, -24964340}, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 18, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                u(new int[]{652952188, -184984365, 1442378921, 1757451057, 249725928, 1007238791, -1149637499, -1979292655, -1554601581, -24964340}, 82 / (ExpandableListView.getPackedPositionForChild(0, 1) > 1L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 1) == 1L ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.p.h
    public final void a(final Context context, o.ei.c cVar, o.h.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(new int[]{1758414535, -294072825, -1909161871, -179992186, -763588692, -1637131962, 517799344, 910423971, -838797315, 427007661, 702224132, 1263246689, -2063852893, 1214761901, -76653260, -1258878682, 1465759692, -963777592, 1425053298, -311228939}, 39 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(new int[]{96890487, 1232659706, -2025233542, 590940508, 2062610757, 156775856}, 9 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.as.c(context, new c.b() { // from class: o.v.m.3
            private static int $10 = 0;
            private static int $11 = 1;
            private static int j = 0;
            private static int f = 1;
            private static char d = 4486;
            private static char b = 64818;
            private static char i = 14930;
            private static char e = 64797;

            /* JADX WARN: Code restructure failed: missing block: B:11:0x0089, code lost:
            
                r7.a.f108o = new android.os.CancellationSignal();
                r7.a.i.launch2(r2, r7.a.h, r8, r7.a.f108o);
             */
            /* JADX WARN: Code restructure failed: missing block: B:13:0x0087, code lost:
            
                if (o.v.m.c(r7.a) != null) goto L13;
             */
            /* JADX WARN: Code restructure failed: missing block: B:7:0x0050, code lost:
            
                if (o.v.m.c(r7.a) != null) goto L13;
             */
            @Override // o.as.c.b
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void a(java.lang.String r8) {
                /*
                    r7 = this;
                    int r0 = o.v.m.AnonymousClass3.f
                    int r0 = r0 + 91
                    int r1 = r0 % 128
                    o.v.m.AnonymousClass3.j = r1
                    r1 = 2
                    int r0 = r0 % r1
                    if (r0 == 0) goto Lf
                    r0 = 67
                    goto L10
                Lf:
                    r0 = 5
                L10:
                    java.lang.String r2 = "\ude0a쏦뚌㾪⸭좂쌹䥎\u18fdﱲ쮪⫋ﳖ협\uea6d헰ﬠ笓ꑔ⹅"
                    java.lang.String r3 = ""
                    java.lang.String r4 = "쳔\uf379환僚憐\uf722쉗㶲㪊귫媓杌蔃髽೫臿ᄔ\ue933㈦杫ᷲ\ue0efۭ뤰棱趴銉蜑㧧㾖㨵歸⡞돉\ue03d䪆ﬠ笓ꑔ⹅"
                    r5 = 1
                    r6 = 0
                    switch(r0) {
                        case 5: goto L53;
                        default: goto L1b;
                    }
                L1b:
                    o.ee.g.c()
                    r0 = 76
                    int r0 = android.text.TextUtils.indexOf(r3, r0, r6, r6)
                    int r0 = r1 >> r0
                    java.lang.Object[] r3 = new java.lang.Object[r5]
                    g(r4, r0, r3)
                    r0 = r3[r6]
                    java.lang.String r0 = (java.lang.String) r0
                    java.lang.String r0 = r0.intern()
                    r3 = 112(0x70, float:1.57E-43)
                    int r4 = android.graphics.Color.blue(r5)
                    int r3 = r3 >> r4
                    java.lang.Object[] r4 = new java.lang.Object[r5]
                    g(r2, r3, r4)
                    r2 = r4[r6]
                    java.lang.String r2 = (java.lang.String) r2
                    java.lang.String r2 = r2.intern()
                    o.ee.g.d(r0, r2)
                    o.v.m r0 = o.v.m.this
                    o.p.g r0 = o.v.m.c(r0)
                    if (r0 == 0) goto La3
                    goto L89
                L53:
                    o.ee.g.c()
                    r0 = 48
                    int r0 = android.text.TextUtils.indexOf(r3, r0, r6, r6)
                    int r0 = r0 + 40
                    java.lang.Object[] r3 = new java.lang.Object[r5]
                    g(r4, r0, r3)
                    r0 = r3[r6]
                    java.lang.String r0 = (java.lang.String) r0
                    java.lang.String r0 = r0.intern()
                    int r3 = android.graphics.Color.blue(r6)
                    int r3 = 19 - r3
                    java.lang.Object[] r4 = new java.lang.Object[r5]
                    g(r2, r3, r4)
                    r2 = r4[r6]
                    java.lang.String r2 = (java.lang.String) r2
                    java.lang.String r2 = r2.intern()
                    o.ee.g.d(r0, r2)
                    o.v.m r0 = o.v.m.this
                    o.p.g r0 = o.v.m.c(r0)
                    if (r0 == 0) goto La3
                L89:
                    o.v.m r0 = o.v.m.this
                    android.os.CancellationSignal r2 = new android.os.CancellationSignal
                    r2.<init>()
                    r0.f108o = r2
                    o.v.m r0 = o.v.m.this
                    o.dw.b r0 = r0.i
                    android.content.Context r2 = r2
                    o.v.m r3 = o.v.m.this
                    o.dw.e r3 = r3.h
                    o.v.m r4 = o.v.m.this
                    android.os.CancellationSignal r4 = r4.f108o
                    r0.launch2(r2, r3, r8, r4)
                La3:
                    int r8 = o.v.m.AnonymousClass3.j
                    int r8 = r8 + 99
                    int r0 = r8 % 128
                    o.v.m.AnonymousClass3.f = r0
                    int r8 = r8 % r1
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.m.AnonymousClass3.a(java.lang.String):void");
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:15:0x0084. Please report as an issue. */
            @Override // o.as.c.b
            public final void d(o.bb.d dVar2) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                g("쳔\uf379환僚憐\uf722쉗㶲㪊귫媓杌蔃髽೫臿ᄔ\ue933㈦杫ᷲ\ue0efۭ뤰棱趴銉蜑㧧㾖㨵歸⡞돉\ue03d䪆ﬠ笓ꑔ⹅", 40 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                g("\ude0a쏦뚌㾪⸭좂쌹䥎\u18fdﱲ쮪⫋鍼ꂕⶲݩ㪊귫곤‼", 19 - (KeyEvent.getMaxKeyCode() >> 16), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                if (m.b(m.this) != null) {
                    int i2 = j + Opcodes.DNEG;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                    switch (dVar2.d() != o.bb.a.aA) {
                        case true:
                            m.a(m.this).onError(o.bv.c.c(dVar2));
                            int i4 = f + Opcodes.LSHL;
                            j = i4 % 128;
                            switch (i4 % 2 != 0 ? 'P' : Typography.less) {
                            }
                        default:
                            int i5 = j + 47;
                            f = i5 % 128;
                            if (i5 % 2 == 0) {
                            }
                            m.d(m.this);
                            m.e(m.this).onAuthenticationDeclined();
                            break;
                    }
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void g(java.lang.String r23, int r24, java.lang.Object[] r25) {
                /*
                    Method dump skipped, instructions count: 552
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.m.AnonymousClass3.g(java.lang.String, int, java.lang.Object[]):void");
            }
        }, cVar).d(dVar, o(), this.l, ((d) this).n.e());
        int i = q + 1;
        r = i % 128;
        switch (i % 2 == 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = r + 61;
        q = i % 128;
        switch (i % 2 == 0) {
            case true:
                if (!this.m) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    u(new int[]{652952188, -184984365, 1442378921, 1757451057, 249725928, 1007238791, -1149637499, -1979292655, 279286605, -1095063532, -586753888, 47428726}, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 24, objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                if (this.l.a() == j.b.b) {
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                    Object[] objArr2 = new Object[1];
                    u(new int[]{652952188, -184984365, 1442378921, 1757451057, 249725928, 1007238791, -1149637499, -1979292655, -1554601581, -24964340}, ((byte) KeyEvent.getModifierMetaStateMask()) + 18, objArr2);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
                }
                int i2 = r + 87;
                q = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        int i3 = 71 / 0;
                        return;
                    default:
                        return;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void a(Context context, o.p.g gVar, o.dw.e eVar) throws WalletValidationException {
        int i = q + Opcodes.LMUL;
        r = i % 128;
        switch (i % 2 == 0) {
            case false:
                this.h = eVar;
                this.i.setProcessCallback(gVar);
                d(context, gVar);
                int i2 = r + 31;
                q = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                this.h = eVar;
                this.i.setProcessCallback(gVar);
                d(context, gVar);
                throw null;
        }
    }

    public final fr.antelop.sdk.CancellationSignal a() {
        int i = r;
        int i2 = i + 39;
        q = i2 % 128;
        if (i2 % 2 != 0) {
            throw null;
        }
        CancellationSignal cancellationSignal = this.f108o;
        if (cancellationSignal != null) {
            int i3 = i + 79;
            q = i3 % 128;
            switch (i3 % 2 != 0 ? '+' : (char) 11) {
                case '+':
                    cancellationSignal.isCanceled();
                    throw null;
                default:
                    switch (cancellationSignal.isCanceled()) {
                        case false:
                            return new fr.antelop.sdk.CancellationSignal(this.f108o);
                    }
            }
        }
        return null;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 820
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.m.u(int[], int, java.lang.Object[]):void");
    }
}
