package o.cf;

import android.content.Context;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\d.smali */
public final class d extends i {
    private final String b;
    private static int e = 0;
    private static int d = 1;

    public d(Context context, int i, String str) {
        super(context, i);
        this.b = str;
    }

    @Override // o.cf.i
    public final void b() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        int i = d;
        int i2 = (i & 75) + (i | 75);
        e = i2 % 128;
        int i3 = i2 % 2;
        h();
        t();
        o();
        m();
        l();
        n();
        switch (!o.a.b(i(), 2, 4, 3, 9, 8)) {
            case true:
                return;
            default:
                int i4 = d;
                int i5 = (i4 & 71) + (i4 | 71);
                e = i5 % 128;
                boolean z = i5 % 2 == 0;
                k();
                switch (z) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    @Override // o.cf.i
    public final String c() {
        int i = d + 77;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        String str = this.b;
        int i4 = ((i2 | 65) << 1) - (i2 ^ 65);
        d = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }
}
