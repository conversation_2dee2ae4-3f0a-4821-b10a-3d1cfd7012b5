package fr.antelop.sdk.cardeventlistener;

import android.content.Context;
import android.util.Log;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\cardeventlistener\DefaultCardEventListener.smali */
public class DefaultCardEventListener implements CardEventListener {
    private static final String TAG = "DefaultCardEventListener";

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardDeleted(Context context, String str, String str2) {
        Log.d(TAG, "onCardDeleted : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardLocked(Context context, String str, String str2) {
        Log.d(TAG, "onCardLocked : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardActivated(Context context, String str, String str2) {
        Log.d(TAG, "onCardActivated : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardActivationRequired(Context context, String str, String str2) {
        Log.d(TAG, "onCardActivationRequired : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardActivating(Context context, String str, String str2) {
        Log.d(TAG, "onCardActivating : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardPaymentKeysRefreshed(Context context, String str, String str2) {
        Log.d(TAG, "onCardPaymentKeysRefreshed : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardDisplayUpdated(Context context, String str, String str2) {
        Log.d(TAG, "onCardDisplayUpdated : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardRedigitized(Context context, String str, String str2) {
        Log.d(TAG, "onCardRedigitized : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardTermsAndConditionsApprovalRequired(Context context, String str, String str2) {
        Log.d(TAG, "onCardTermsAndConditionsApprovalRequired : ".concat(String.valueOf(str)));
    }

    @Override // fr.antelop.sdk.cardeventlistener.CardEventListener
    public void onCardPaymentInformationUpdated(Context context, String str, String str2) {
        Log.d(TAG, "onCardPaymentInformationUpdated : ".concat(String.valueOf(str)));
    }
}
