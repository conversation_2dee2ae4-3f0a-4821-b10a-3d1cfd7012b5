package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x.smali */
public abstract class x extends b0 implements y {
    byte[] b;
    static final o0 x = new a(x.class, 4);
    static final byte[] C = new byte[0];

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(e0 e0Var) {
            return e0Var.m();
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return f2Var;
        }
    }

    public x(byte[] bArr) {
        if (bArr == null) {
            throw new NullPointerException("'string' cannot be null");
        }
        this.b = bArr;
    }

    public static x a(j0 j0Var, boolean z) {
        return (x) x.a(j0Var, z);
    }

    static x b(byte[] bArr) {
        return new f2(bArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.y
    public InputStream c() {
        return new ByteArrayInputStream(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return new f2(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new f2(this.b);
    }

    public byte[] h() {
        return this.b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return Arrays.hashCode(h());
    }

    public int i() {
        return h().length;
    }

    public String toString() {
        return "#" + o7.b(z4.a(this.b));
    }

    public static x a(Object obj) {
        if (obj == null || (obj instanceof x)) {
            return (x) obj;
        }
        if (obj instanceof h) {
            b0 aSN1Primitive = ((h) obj).toASN1Primitive();
            if (aSN1Primitive instanceof x) {
                return (x) aSN1Primitive;
            }
        } else if (obj instanceof byte[]) {
            try {
                return (x) x.a((byte[]) obj);
            } catch (IOException e) {
                throw new IllegalArgumentException("failed to construct OCTET STRING from byte[]: " + e.getMessage());
            }
        }
        throw new IllegalArgumentException("illegal object in getInstance: " + obj.getClass().getName());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (b0Var instanceof x) {
            return Arrays.areEqual(this.b, ((x) b0Var).b);
        }
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() {
        return toASN1Primitive();
    }
}
