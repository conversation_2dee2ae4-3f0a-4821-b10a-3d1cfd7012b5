package fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.activity.OnBackPressedCallback;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.material.button.MaterialButton;
import fr.antelop.sdk.R;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels.ManageGooglePayMockViewModel;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\googlepay\ui\AddGooglePayFragment.smali */
public final class AddGooglePayFragment extends Fragment {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10 = 0;
    private static int $11 = 0;
    private static final String TAP_AND_PAY_EXTRA_ISSUER_TOKEN_ID = "extra_issuer_token_id";
    private static int b;
    private static long d;
    private static int e;
    private MaterialButton addButton;
    private MaterialButton cancelButton;
    private ManageGooglePayMockViewModel manageGooglePayMockViewModel;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        d = -4759069625594737999L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void c(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r8 = r8 * 2
            int r8 = r8 + 112
            byte[] r0 = fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment.$$a
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            r7 = r6
            goto L39
        L1c:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L21:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L39:
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L21
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment.c(int, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -112, 34, 95};
        $$b = 5;
    }

    @Override // androidx.fragment.app.Fragment
    public final void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.manageGooglePayMockViewModel = (ManageGooglePayMockViewModel) new ViewModelProvider(requireActivity()).get(ManageGooglePayMockViewModel.class);
        int i = b + 83;
        e = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0012. Please report as an issue. */
    @Override // androidx.fragment.app.Fragment
    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        int i = b + Opcodes.DDIV;
        e = i % 128;
        switch (i % 2 != 0 ? 'W' : (char) 30) {
        }
        View inflate = layoutInflater.inflate(R.layout.add_google_pay_mock_fragment_layout, viewGroup, false);
        this.addButton = (MaterialButton) inflate.findViewById(R.id.add);
        this.cancelButton = (MaterialButton) inflate.findViewById(R.id.cancel);
        int i2 = b + Opcodes.DDIV;
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 23 / 0;
                return inflate;
            default:
                return inflate;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:12:0x0063. Please report as an issue. */
    /* renamed from: lambda$onViewCreated$0$fr-antelop-sdk-digitalcard-devicewallet-googlepay-ui-AddGooglePayFragment, reason: not valid java name */
    /* synthetic */ void m228xe45a1f61(String str, View view) {
        int i = e + 65;
        b = i % 128;
        int i2 = i % 2;
        if (str != null) {
            String addToken = this.manageGooglePayMockViewModel.addToken(str);
            Intent intent = new Intent();
            Object[] objArr = new Object[1];
            a("채慦阫쯮碼깅쌲烫ꖪ\udb63࠲뷦튊f딼\ueafbᾴ䵠\ue210៥䒩", TextUtils.lastIndexOf("", '0', 0) + 44352, objArr);
            requireActivity().setResult(-1, intent.putExtra(((String) objArr[0]).intern(), addToken));
            int i3 = e + 13;
            b = i3 % 128;
            if (i3 % 2 != 0) {
            }
        } else {
            requireActivity().setResult(0);
            int i4 = b + 25;
            e = i4 % 128;
            switch (i4 % 2 == 0) {
            }
        }
        requireActivity().finish();
    }

    /* renamed from: lambda$onViewCreated$1$fr-antelop-sdk-digitalcard-devicewallet-googlepay-ui-AddGooglePayFragment, reason: not valid java name */
    /* synthetic */ void m229x18084a22(View view) {
        FragmentActivity requireActivity;
        int i;
        int i2 = e + Opcodes.LUSHR;
        b = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 17 : '0') {
            case '0':
                requireActivity = requireActivity();
                i = 0;
                break;
            default:
                requireActivity = requireActivity();
                i = 1;
                break;
        }
        requireActivity.setResult(i);
        requireActivity().finish();
    }

    @Override // androidx.fragment.app.Fragment
    public final void onViewCreated(View view, Bundle bundle) {
        super.onViewCreated(view, bundle);
        final String stringExtra = requireActivity().getIntent().getStringExtra(DeviceWalletMockActivity.LAST_DIGITS_BUNDLE_KEY);
        this.addButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                AddGooglePayFragment.this.m228xe45a1f61(stringExtra, view2);
            }
        });
        this.cancelButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                AddGooglePayFragment.this.m229x18084a22(view2);
            }
        });
        requireActivity().getOnBackPressedDispatcher().addCallback(getViewLifecycleOwner(), new OnBackPressedCallback(true) { // from class: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment.1
            @Override // androidx.activity.OnBackPressedCallback
            public void handleOnBackPressed() {
                AddGooglePayFragment.this.requireActivity().setResult(0);
                AddGooglePayFragment.this.requireActivity().finish();
            }
        });
        int i = b + 79;
        e = i % 128;
        switch (i % 2 != 0 ? '5' : (char) 14) {
            case 14:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void a(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 488
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.googlepay.ui.AddGooglePayFragment.a(java.lang.String, int, java.lang.Object[]):void");
    }
}
