package fr.antelop.sdk.sca;

import android.content.Context;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import o.w.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\sca\PushAuthenticationRequestFetcher.smali */
public final class PushAuthenticationRequestFetcher {
    public final void fetchPendingRequests(Context context, AntelopCallback antelopCallback) throws WalletValidationException {
        d.a(context, antelopCallback);
    }
}
