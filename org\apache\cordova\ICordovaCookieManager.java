package org.apache.cordova;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\ICordovaCookieManager.smali */
public interface ICordovaCookieManager {
    void clearCookies();

    void flush();

    String getCookie(final String url);

    void setCookie(final String url, final String value);

    void setCookiesEnabled(boolean accept);
}
