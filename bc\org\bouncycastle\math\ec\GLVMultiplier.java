package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.math.ec.endo.EndoUtil;
import bc.org.bouncycastle.math.ec.endo.GLVEndomorphism;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\GLVMultiplier.smali */
public class GLVMultiplier extends AbstractECMultiplier {
    protected final ECCurve a;
    protected final GLVEndomorphism b;

    public GLVMultiplier(ECCurve eCCurve, GLVEndomorphism gLVEndomorphism) {
        if (eCCurve == null || eCCurve.getOrder() == null) {
            throw new IllegalArgumentException("Need curve with known group order");
        }
        this.a = eCCurve;
        this.b = gLVEndomorphism;
    }

    @Override // bc.org.bouncycastle.math.ec.AbstractECMultiplier
    protected ECPoint a(ECPoint eCPoint, BigInteger bigInteger) {
        if (!this.a.equals(eCPoint.getCurve())) {
            throw new IllegalStateException();
        }
        BigInteger[] decomposeScalar = this.b.decomposeScalar(bigInteger.mod(eCPoint.getCurve().getOrder()));
        BigInteger bigInteger2 = decomposeScalar[0];
        BigInteger bigInteger3 = decomposeScalar[1];
        return this.b.hasEfficientPointMap() ? ECAlgorithms.a(this.b, eCPoint, bigInteger2, bigInteger3) : ECAlgorithms.c(eCPoint, bigInteger2, EndoUtil.mapPoint(this.b, eCPoint), bigInteger3);
    }
}
