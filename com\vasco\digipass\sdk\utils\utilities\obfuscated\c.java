package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c.smali */
public abstract class c extends b0 {
    static final o0 x = new a(c.class, 30);
    final char[] b;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return c.b(f2Var.h());
        }
    }

    c(byte[] bArr) {
        if (bArr == null) {
            throw new NullPointerException("'string' cannot be null");
        }
        int length = bArr.length;
        if ((length & 1) != 0) {
            throw new IllegalArgumentException("malformed BMPString encoding encountered");
        }
        int i = length / 2;
        char[] cArr = new char[i];
        for (int i2 = 0; i2 != i; i2++) {
            int i3 = i2 * 2;
            cArr[i2] = (char) ((bArr[i3 + 1] & 255) | (bArr[i3] << 8));
        }
        this.b = cArr;
    }

    static c b(byte[] bArr) {
        return new v1(bArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean a(b0 b0Var) {
        if (b0Var instanceof c) {
            return Arrays.areEqual(this.b, ((c) b0Var).b);
        }
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean e() {
        return false;
    }

    public final String h() {
        return new String(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public final int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public String toString() {
        return h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final int a(boolean z) {
        return z.a(z, this.b.length * 2);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final void a(z zVar, boolean z) throws IOException {
        int length = this.b.length;
        zVar.b(z, 30);
        zVar.d(length * 2);
        byte[] bArr = new byte[8];
        int i = length & (-4);
        int i2 = 0;
        while (i2 < i) {
            char[] cArr = this.b;
            char c = cArr[i2];
            char c2 = cArr[i2 + 1];
            char c3 = cArr[i2 + 2];
            char c4 = cArr[i2 + 3];
            i2 += 4;
            bArr[0] = (byte) (c >> '\b');
            bArr[1] = (byte) c;
            bArr[2] = (byte) (c2 >> '\b');
            bArr[3] = (byte) c2;
            bArr[4] = (byte) (c3 >> '\b');
            bArr[5] = (byte) c3;
            bArr[6] = (byte) (c4 >> '\b');
            bArr[7] = (byte) c4;
            zVar.a(bArr, 0, 8);
        }
        if (i2 < length) {
            int i3 = 0;
            do {
                char c5 = this.b[i2];
                i2++;
                int i4 = i3 + 1;
                bArr[i3] = (byte) (c5 >> '\b');
                i3 = i4 + 1;
                bArr[i4] = (byte) c5;
            } while (i2 < length);
            zVar.a(bArr, 0, i3);
        }
    }

    c(char[] cArr) {
        if (cArr != null) {
            this.b = cArr;
            return;
        }
        throw new NullPointerException("'string' cannot be null");
    }

    static c a(char[] cArr) {
        return new v1(cArr);
    }
}
