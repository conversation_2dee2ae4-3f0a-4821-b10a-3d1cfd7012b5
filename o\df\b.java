package o.df;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.work.BackoffPolicy;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\df\b.smali */
public final class b implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static short[] b;
    private static byte[] c;
    private static int d;
    private static int e;
    private static int i;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        e();
        ViewConfiguration.getKeyRepeatTimeout();
        ViewConfiguration.getKeyRepeatDelay();
        ViewConfiguration.getLongPressTimeout();
        ExpandableListView.getPackedPositionForGroup(0);
        ViewConfiguration.getMinimumFlingVelocity();
        int i2 = j + 19;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void e() {
        c = new byte[]{47, 18, -30, 15, 17, 5, 54, -33, 90, -3, 54, -27, 17, 10, 11, -26, 49, -28, -1, 17, 87, -20, 26, 28, 55, -5, 11, -17, 54, 11, 56, -15};
        d = 909053681;
        e = -323233396;
        a = -588953586;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r6 = r6 + 4
            int r7 = r7 * 2
            int r7 = r7 + 108
            byte[] r0 = o.df.b.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2e
        L17:
            r3 = r2
        L18:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r5
        L2e:
            int r6 = r6 + r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.b.g(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{88, 54, 68, 27};
        $$b = 7;
    }

    public static boolean d(o.bb.d dVar) {
        if (dVar.b()) {
            int i2 = j + 19;
            i = i2 % 128;
            switch (i2 % 2 == 0 ? 'Z' : ' ') {
                case 'Z':
                    dVar.a().c();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    if (dVar.a().c() && dVar.a().a() != f.c) {
                        return true;
                    }
                    break;
            }
        }
        if (dVar.d() == o.bb.a.an) {
            return true;
        }
        int i3 = j + 19;
        i = i3 % 128;
        int i4 = i3 % 2;
        return false;
    }

    /* JADX WARN: Code restructure failed: missing block: B:8:0x0035, code lost:
    
        if (r5.b() != false) goto L22;
     */
    @Override // o.df.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(o.bb.d r5, o.de.b r6) {
        /*
            r4 = this;
            o.av.a r6 = r6.b()
            o.av.a r0 = o.av.a.d
            r1 = 1
            r2 = 0
            if (r6 != r0) goto Ld
            r6 = r2
            goto Le
        Ld:
            r6 = r1
        Le:
            switch(r6) {
                case 1: goto L1e;
                default: goto L11;
            }
        L11:
            int r6 = o.df.b.i
            int r6 = r6 + 19
            int r0 = r6 % 128
            o.df.b.j = r0
            r0 = 2
            int r6 = r6 % r0
            if (r6 == 0) goto L31
            goto L1f
        L1e:
            return r2
        L1f:
            boolean r6 = r5.b()
            r3 = 10
            int r3 = r3 / r2
            if (r6 == 0) goto L2a
            r6 = r1
            goto L2b
        L2a:
            r6 = r2
        L2b:
            switch(r6) {
                case 0: goto L53;
                default: goto L2e;
            }
        L2e:
            goto L37
        L2f:
            r5 = move-exception
            throw r5
        L31:
            boolean r6 = r5.b()
            if (r6 == 0) goto L53
        L37:
            int r6 = o.df.b.i
            int r6 = r6 + 83
            int r3 = r6 % 128
            o.df.b.j = r3
            int r6 = r6 % r0
            o.bb.c r5 = r5.a()
            boolean r5 = r5.c()
            if (r5 == 0) goto L4c
            r5 = r0
            goto L4e
        L4c:
            r5 = 76
        L4e:
            switch(r5) {
                case 2: goto L52;
                default: goto L51;
            }
        L51:
            goto L53
        L52:
            return r1
        L53:
            int r5 = o.df.b.j
            int r5 = r5 + 19
            int r6 = r5 % 128
            o.df.b.i = r6
            int r5 = r5 % r0
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.b.a(o.bb.d, o.de.b):boolean");
    }

    @Override // o.df.e
    public final BackoffPolicy b() {
        int i2 = j + 13;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 21 : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                BackoffPolicy backoffPolicy = BackoffPolicy.EXPONENTIAL;
                int i3 = i + 109;
                j = i3 % 128;
                int i4 = i3 % 2;
                return backoffPolicy;
            default:
                BackoffPolicy backoffPolicy2 = BackoffPolicy.EXPONENTIAL;
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.df.e
    public final int d() {
        int i2 = j + 55;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? '\t' : (char) 17) {
            case '\t':
                return 30;
            default:
                return Opcodes.ISHL;
        }
    }

    @Override // o.df.e
    public final int d(int i2) {
        int i3 = j + 97;
        i = i3 % 128;
        int i4 = i3 % 2;
        int pow = ((int) Math.pow(2.0d, i2 - 1)) * Opcodes.ISHL;
        int i5 = j + 9;
        i = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return pow;
            default:
                throw null;
        }
    }

    @Override // o.df.e
    public final String a() {
        int i2 = i + Opcodes.LMUL;
        j = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        f((byte) ((-28) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), View.resolveSizeAndState(0, 0, 0) + 355842402, (short) (117 - (Process.myPid() >> 22)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 99, Color.alpha(0) + 627779881, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = j + 59;
        i = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 27 : '7') {
            case '7':
                return intern;
            default:
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:31:0x0073  */
    @Override // o.df.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.de.f c(o.bb.d r4) {
        /*
            r3 = this;
            o.bb.e r0 = r4.c()
            o.bb.e r1 = o.bb.e.d
            if (r0 != r1) goto L1e
            int r4 = o.df.b.j
            int r4 = r4 + 9
            int r0 = r4 % 128
            o.df.b.i = r0
            int r4 = r4 % 2
            if (r4 == 0) goto L18
            o.de.f r4 = o.de.f.f54o
            return r4
        L18:
            o.de.f r4 = o.de.f.f54o
            r4 = 0
            throw r4     // Catch: java.lang.Throwable -> L1c
        L1c:
            r4 = move-exception
            throw r4
        L1e:
            o.bb.e r1 = o.bb.e.a
            r2 = 1
            if (r0 != r1) goto L2f
            int r4 = o.df.b.j
            int r4 = r4 + r2
            int r0 = r4 % 128
            o.df.b.i = r0
            int r4 = r4 % 2
            o.de.f r4 = o.de.f.p
            return r4
        L2f:
            o.bb.e r1 = o.bb.e.m
            if (r0 != r1) goto L78
            int r0 = o.df.b.i
            int r0 = r0 + 55
            int r1 = r0 % 128
            o.df.b.j = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 == 0) goto L41
            goto L42
        L41:
            r2 = r1
        L42:
            switch(r2) {
                case 0: goto L50;
                default: goto L45;
            }
        L45:
            o.bb.c r4 = r4.a()
            o.de.f r4 = r4.e()
            o.de.f r0 = o.de.f.c
            goto L65
        L50:
            o.bb.c r4 = r4.a()
            o.de.f r4 = r4.e()
            o.de.f r0 = o.de.f.c
            if (r4 != r0) goto L5f
            r4 = 77
            goto L61
        L5f:
            r4 = 53
        L61:
            switch(r4) {
                case 77: goto L73;
                default: goto L64;
            }
        L64:
            goto L78
        L65:
            r2 = 82
            int r2 = r2 / r1
            if (r4 != r0) goto L6d
            r4 = 51
            goto L6f
        L6d:
            r4 = 15
        L6f:
            switch(r4) {
                case 51: goto L73;
                default: goto L72;
            }
        L72:
            goto L64
        L73:
            o.de.f r4 = o.de.f.q
            return r4
        L76:
            r4 = move-exception
            throw r4
        L78:
            o.de.f r4 = o.de.f.n
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.b.c(o.bb.d):o.de.f");
    }

    /* JADX WARN: Removed duplicated region for block: B:42:0x006a A[FALL_THROUGH, RETURN] */
    @Override // o.df.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean b(o.de.f r6) {
        /*
            r5 = this;
            int r0 = o.df.b.j
            int r0 = r0 + 21
            int r1 = r0 % 128
            o.df.b.i = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 34
            goto L11
        Lf:
            r0 = 93
        L11:
            r1 = 0
            switch(r0) {
                case 34: goto L1c;
                default: goto L15;
            }
        L15:
            o.de.f r0 = o.de.f.f54o
            r2 = 1
            r3 = 0
            if (r6 == r0) goto L23
            goto L21
        L1c:
            o.de.f r6 = o.de.f.f54o
            throw r1     // Catch: java.lang.Throwable -> L1f
        L1f:
            r6 = move-exception
            throw r6
        L21:
            r0 = r3
            goto L24
        L23:
            r0 = r2
        L24:
            switch(r0) {
                case 1: goto L6a;
                default: goto L27;
            }
        L27:
            o.de.f r0 = o.de.f.p
            if (r6 == r0) goto L2e
            r0 = 37
            goto L30
        L2e:
            r0 = 39
        L30:
            switch(r0) {
                case 39: goto L6a;
                default: goto L33;
            }
        L33:
            o.de.f r0 = o.de.f.q
            if (r6 == r0) goto L6a
            int r0 = o.df.b.j
            int r0 = r0 + 111
            int r4 = r0 % 128
            o.df.b.i = r4
            int r0 = r0 % 2
            if (r0 == 0) goto L62
            o.de.f r0 = o.de.f.n
            if (r6 != r0) goto L4a
            r6 = 38
            goto L4b
        L4a:
            r6 = 7
        L4b:
            switch(r6) {
                case 7: goto L4f;
                default: goto L4e;
            }
        L4e:
            goto L6a
        L4f:
            int r6 = o.df.b.j
            int r6 = r6 + 79
            int r0 = r6 % 128
            o.df.b.i = r0
            int r6 = r6 % 2
            if (r6 != 0) goto L61
            r6 = 78
            int r6 = r6 / r3
            return r3
        L5f:
            r6 = move-exception
            throw r6
        L61:
            return r3
        L62:
            o.de.f r6 = o.de.f.n
            r1.hashCode()     // Catch: java.lang.Throwable -> L68
            throw r1     // Catch: java.lang.Throwable -> L68
        L68:
            r6 = move-exception
            throw r6
        L6a:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.b.b(o.de.f):boolean");
    }

    private static void f(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(d)};
            Object obj = o.e.a.s.get(-2120899312);
            int i5 = -1;
            if (obj == null) {
                Class cls = (Class) o.e.a.c(Color.alpha(0) + 11, (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 65);
                byte b3 = (byte) (-1);
                byte b4 = (byte) (b3 + 1);
                Object[] objArr3 = new Object[1];
                g(b3, b4, b4, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z2 = intValue == -1;
            switch (!z2) {
                case true:
                    break;
                default:
                    byte[] bArr = c;
                    long j2 = 0;
                    if (bArr != null) {
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i6 = 0;
                        while (true) {
                            switch (i6 >= length) {
                                case true:
                                    bArr = bArr2;
                                    break;
                                default:
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(bArr[i6])};
                                        Object obj2 = o.e.a.s.get(494867332);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(19 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (char) ((Process.getElapsedCpuTime() > j2 ? 1 : (Process.getElapsedCpuTime() == j2 ? 0 : -1)) + 16424), 150 - TextUtils.getOffsetAfter("", 0));
                                            byte b5 = (byte) i5;
                                            byte b6 = (byte) (-b5);
                                            Object[] objArr5 = new Object[1];
                                            g(b5, b6, (byte) (b6 - 1), objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                            o.e.a.s.put(494867332, obj2);
                                        }
                                        bArr2[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                        i6++;
                                        i5 = -1;
                                        j2 = 0;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                            }
                        }
                    }
                    if (bArr == null) {
                        intValue = (short) (((short) (b[i2 + ((int) (a ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (d ^ (-5810760824076169584L))));
                        break;
                    } else {
                        byte[] bArr3 = c;
                        try {
                            Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(a)};
                            Object obj3 = o.e.a.s.get(-2120899312);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 65 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                byte b7 = (byte) (-1);
                                byte b8 = (byte) (b7 + 1);
                                Object[] objArr7 = new Object[1];
                                g(b7, b8, b8, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-2120899312, obj3);
                            }
                            intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (d ^ (-5810760824076169584L))));
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
            }
            if (intValue > 0) {
                int i7 = $11 + Opcodes.DNEG;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                fVar.d = ((i2 + intValue) - 2) + ((int) (a ^ (-5810760824076169584L))) + (z2 ? 1 : 0);
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(e), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c(11 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 603 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = c;
                    if (bArr4 != null) {
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        int i9 = 0;
                        while (i9 < length2) {
                            int i10 = $10 + 55;
                            $11 = i10 % 128;
                            if (i10 % 2 == 0) {
                                bArr5[i9] = (byte) (bArr4[i9] - 5810760824076169584L);
                                i9 /= 0;
                            } else {
                                bArr5[i9] = (byte) (bArr4[i9] ^ (-5810760824076169584L));
                                i9++;
                            }
                        }
                        bArr4 = bArr5;
                    }
                    switch (bArr4 != null ? 'Z' : '\b') {
                        case 'Z':
                            z = true;
                            break;
                        default:
                            z = false;
                            break;
                    }
                    fVar.c = 1;
                    while (true) {
                        switch (fVar.c >= intValue) {
                            case true:
                                break;
                            default:
                                int i11 = $11;
                                int i12 = i11 + Opcodes.LSHR;
                                $10 = i12 % 128;
                                if (i12 % 2 != 0) {
                                    int i13 = 78 / 0;
                                    switch (z ? (char) 1 : '2') {
                                        case '2':
                                            break;
                                        default:
                                            int i14 = i11 + 97;
                                            $10 = i14 % 128;
                                            int i15 = i14 % 2;
                                            byte[] bArr6 = c;
                                            fVar.d = fVar.d - 1;
                                            fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                            continue;
                                    }
                                    short[] sArr = b;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                } else {
                                    if (!z) {
                                        short[] sArr2 = b;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((short) (((short) (sArr2[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                    }
                                    int i142 = i11 + 97;
                                    $10 = i142 % 128;
                                    int i152 = i142 % 2;
                                    byte[] bArr62 = c;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr62[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                    continue;
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                        }
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
