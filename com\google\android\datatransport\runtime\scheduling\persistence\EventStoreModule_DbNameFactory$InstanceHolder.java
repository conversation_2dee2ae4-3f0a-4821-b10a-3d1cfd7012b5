package com.google.android.datatransport.runtime.scheduling.persistence;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\scheduling\persistence\EventStoreModule_DbNameFactory$InstanceHolder.smali */
public final class EventStoreModule_DbNameFactory$InstanceHolder {
    private static final EventStoreModule_DbNameFactory INSTANCE = new EventStoreModule_DbNameFactory();

    private EventStoreModule_DbNameFactory$InstanceHolder() {
    }
}
