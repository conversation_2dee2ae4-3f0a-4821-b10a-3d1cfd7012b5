package o.ba;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ba\e.smali */
public abstract class e {
    private static int a = 0;
    private static int c = 1;
    private String e;

    public final String d() {
        int i = c;
        int i2 = (i ^ 29) + ((i & 29) << 1);
        a = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                String str = this.e;
                int i3 = i + 3;
                a = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final void b(String str) {
        int i = (c + 72) - 1;
        a = i % 128;
        boolean z = i % 2 == 0;
        this.e = str;
        switch (z) {
            case false:
                throw null;
            default:
                return;
        }
    }

    public final int hashCode() {
        int i = (c + 90) - 1;
        a = i % 128;
        int i2 = i % 2;
        int hashCode = super.hashCode();
        int i3 = c + 63;
        a = i3 % 128;
        int i4 = i3 % 2;
        return hashCode;
    }

    public final boolean equals(Object obj) {
        int i = c;
        int i2 = (i & 45) + (i | 45);
        a = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = c;
        int i5 = ((i4 | Opcodes.DREM) << 1) - (i4 ^ Opcodes.DREM);
        a = i5 % 128;
        switch (i5 % 2 != 0 ? '6' : (char) 16) {
            case 16:
                return equals;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    public final String toString() {
        int i = a;
        int i2 = ((i | 83) << 1) - (i ^ 83);
        c = i2 % 128;
        int i3 = i2 % 2;
        String obj = super.toString();
        int i4 = a + Opcodes.LSHR;
        c = i4 % 128;
        int i5 = i4 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i = a;
        int i2 = (i & 23) + (i | 23);
        c = i2 % 128;
        char c2 = i2 % 2 == 0 ? '9' : '0';
        super.finalize();
        switch (c2) {
            case '9':
                int i3 = 57 / 0;
                return;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
