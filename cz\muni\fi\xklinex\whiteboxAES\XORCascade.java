package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\XORCascade.smali */
public class XORCascade implements Serializable {
    public static final int BOXES = 3;
    public static final int C = 2;
    public static final int L = 0;
    public static final int R = 1;
    public static final int WIDTH = 4;
    private static final long serialVersionUID = -2288726654138138762L;
    protected final XORBox[] x = new XORBox[3];

    public XORCascade() {
        for (int i = 0; i < 3; i++) {
            this.x[i] = new XORBox();
        }
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.deepEquals(this.x, ((XORCascade) obj).x);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.deepHashCode(this.x) + 581;
    }

    public long xor(long j, long j2, long j3, long j4) {
        return xor(this.x, j, j2, j3, j4);
    }

    public static long xor(XORBox[] xORBoxArr, long j, long j2, long j3, long j4) {
        return xORBoxArr[2].xor(xORBoxArr[0].xor(j, j2), xORBoxArr[1].xor(j3, j4));
    }
}
