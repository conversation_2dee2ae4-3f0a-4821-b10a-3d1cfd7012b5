package fr.antelop.sdk.card;

import android.content.Context;
import android.graphics.drawable.Drawable;
import o.eo.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\EcomStaticTokenDisplay.smali */
public final class EcomStaticTokenDisplay implements ICardDisplay {
    private final a innerEcomStaticToken;

    public EcomStaticTokenDisplay(a aVar) {
        this.innerEcomStaticToken = aVar;
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getLabel() {
        return this.innerEcomStaticToken.b();
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getForegroundColor() {
        return null;
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final Drawable getGraphicResource(Context context) {
        if (this.innerEcomStaticToken.d() != null) {
            return this.innerEcomStaticToken.d().b(context);
        }
        return null;
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getLayoutDescription() {
        return null;
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getDescription() {
        return this.innerEcomStaticToken.b();
    }
}
