package com.google.android.gms.internal.tapandpay;

import android.os.Bundle;
import android.os.RemoteException;
import com.google.android.gms.common.api.Status;
import com.google.android.gms.tapandpay.firstparty.RetrieveInAppPaymentCredentialResponse;
import com.google.android.gms.tapandpay.globalactions.GetGlobalActionCardsResponse;
import com.google.android.gms.tapandpay.issuer.PushProvisionSessionContext;
import com.google.android.gms.tapandpay.issuer.TokenInfo;
import com.google.android.gms.tapandpay.issuer.TokenStatus;
import com.google.android.gms.tapandpay.quickaccesswallet.QuickAccessWalletConfig;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\tapandpay\zzah.smali */
public class zzah extends zze {
    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzA(Status status, com.google.android.gms.tapandpay.firstparty.zzz zzzVar) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzB(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzC(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzD(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzE(Status status, byte[] bArr) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public void zzF(Status status, PushProvisionSessionContext pushProvisionSessionContext) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzG(Status status, QuickAccessWalletConfig quickAccessWalletConfig) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzH(Status status, boolean z) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzI(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzJ(Status status, com.google.android.gms.tapandpay.firstparty.zzap zzapVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzK(Status status, com.google.android.gms.tapandpay.firstparty.zzab zzabVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzL(Status status, com.google.android.gms.tapandpay.firstparty.zzad zzadVar) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzM(Status status) {
        throw new UnsupportedOperationException();
    }

    public void zzN(Status status, String str) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzO(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzP(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzQ(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public void zzR(Status status, TokenStatus tokenStatus) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzS(Status status, boolean z) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public void zza() {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public void zzb(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzc(Status status, com.google.android.gms.tapandpay.firstparty.zzj zzjVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzd(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zze(Status status, com.google.android.gms.tapandpay.firstparty.zzl zzlVar) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzf(Status status, com.google.android.gms.tapandpay.firstparty.zzn zznVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public void zzg(Status status, String str) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzh(Status status, com.google.android.gms.tapandpay.firstparty.zzp zzpVar) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzi(Status status, String str) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzj(Status status, com.google.android.gms.tapandpay.firstparty.zzr zzrVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzk(Status status, com.google.android.gms.tapandpay.firstparty.zzg zzgVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzl(Status status, com.google.android.gms.tapandpay.firstparty.zzt zztVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzm(Status status, boolean z) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzn(Status status, boolean z) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzo(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    public void zzp(Status status, String str) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzq(Status status, com.google.android.gms.tapandpay.firstparty.zzv zzvVar) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzr(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzs(Status status, GetGlobalActionCardsResponse getGlobalActionCardsResponse) {
        throw new UnsupportedOperationException();
    }

    public void zzt(Status status, Bundle bundle) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzu(Status status, RetrieveInAppPaymentCredentialResponse retrieveInAppPaymentCredentialResponse) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzv(Status status) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    public void zzw(Status status, boolean z) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.tapandpay.zzf
    public final void zzx(Status status, com.google.android.gms.tapandpay.firstparty.zzx zzxVar) {
        throw new UnsupportedOperationException();
    }

    public void zzy(Status status, String str) throws RemoteException {
        throw new UnsupportedOperationException();
    }

    public void zzz(Status status, TokenInfo[] tokenInfoArr) {
        throw new UnsupportedOperationException();
    }
}
