package androidx.core.content;

import android.os.RemoteException;
import androidx.core.app.unusedapprestrictions.IUnusedAppRestrictionsBackportCallback;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\content\UnusedAppRestrictionsBackportCallback.smali */
public class UnusedAppRestrictionsBackportCallback {
    private IUnusedAppRestrictionsBackportCallback mCallback;

    public UnusedAppRestrictionsBackportCallback(IUnusedAppRestrictionsBackportCallback callback) {
        this.mCallback = callback;
    }

    public void onResult(boolean success, boolean enabled) throws RemoteException {
        this.mCallback.onIsPermissionRevocationEnabledForAppResult(success, enabled);
    }
}
