package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.params.ParametersWithIV;
import bc.org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c7.smali */
public class c7 extends k7 {
    private final BlockCipher b;
    private final int c;
    private byte[] d;
    private byte[] e;
    private byte[] f;
    private int g;

    public c7(BlockCipher blockCipher) {
        super(blockCipher);
        this.b = blockCipher;
        int blockSize = blockCipher.getBlockSize();
        this.c = blockSize;
        this.d = new byte[blockSize];
        this.e = new byte[blockSize];
        this.f = new byte[blockSize];
        this.g = 0;
    }

    private void b() {
        byte b;
        int length = this.e.length;
        do {
            length--;
            if (length < 0) {
                return;
            }
            byte[] bArr = this.e;
            b = (byte) (bArr[length] + 1);
            bArr[length] = b;
        } while (b == 0);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k7
    public int a(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException {
        byte b;
        if (i + i2 > bArr.length) {
            throw new DataLengthException("input buffer too small");
        }
        if (i3 + i2 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        for (int i4 = 0; i4 < i2; i4++) {
            int i5 = this.g;
            if (i5 == 0) {
                a();
                this.b.processBlock(this.e, 0, this.f, 0);
                byte b2 = bArr[i + i4];
                byte[] bArr3 = this.f;
                int i6 = this.g;
                this.g = i6 + 1;
                b = (byte) (b2 ^ bArr3[i6]);
            } else {
                byte b3 = bArr[i + i4];
                byte[] bArr4 = this.f;
                int i7 = i5 + 1;
                this.g = i7;
                b = (byte) (bArr4[i5] ^ b3);
                if (i7 == this.e.length) {
                    this.g = 0;
                    b();
                }
            }
            bArr2[i3 + i4] = b;
        }
        return i2;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return this.b.getAlgorithmName() + "/SIC";
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return this.b.getBlockSize();
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        if (!(cipherParameters instanceof ParametersWithIV)) {
            throw new IllegalArgumentException("CTR/SIC mode requires ParametersWithIV");
        }
        ParametersWithIV parametersWithIV = (ParametersWithIV) cipherParameters;
        byte[] clone = Arrays.clone(parametersWithIV.getIV());
        this.d = clone;
        int i = this.c;
        if (i < clone.length) {
            throw new IllegalArgumentException("CTR/SIC mode requires IV no greater than: " + this.c + " bytes.");
        }
        int i2 = i / 2;
        if (8 <= i2) {
            i2 = 8;
        }
        if (i - clone.length > i2) {
            throw new IllegalArgumentException("CTR/SIC mode requires IV of at least: " + (this.c - i2) + " bytes.");
        }
        if (parametersWithIV.getParameters() != null) {
            this.b.init(true, parametersWithIV.getParameters());
        }
        reset();
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i, byte[] bArr2, int i2) throws DataLengthException, IllegalStateException {
        if (this.g != 0) {
            a(bArr, i, this.c, bArr2, i2);
            return this.c;
        }
        int i3 = this.c;
        if (i + i3 > bArr.length) {
            throw new DataLengthException("input buffer too small");
        }
        if (i3 + i2 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        this.b.processBlock(this.e, 0, this.f, 0);
        for (int i4 = 0; i4 < this.c; i4++) {
            bArr2[i2 + i4] = (byte) (bArr[i + i4] ^ this.f[i4]);
        }
        b();
        return this.c;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void reset() {
        Arrays.fill(this.e, (byte) 0);
        byte[] bArr = this.d;
        System.arraycopy(bArr, 0, this.e, 0, bArr.length);
        this.b.reset();
        this.g = 0;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k7
    protected byte a(byte b) throws DataLengthException, IllegalStateException {
        int i = this.g;
        if (i == 0) {
            a();
            this.b.processBlock(this.e, 0, this.f, 0);
            byte[] bArr = this.f;
            int i2 = this.g;
            this.g = i2 + 1;
            return (byte) (b ^ bArr[i2]);
        }
        byte[] bArr2 = this.f;
        int i3 = i + 1;
        this.g = i3;
        byte b2 = (byte) (b ^ bArr2[i]);
        if (i3 == this.e.length) {
            this.g = 0;
            b();
        }
        return b2;
    }

    private void a() {
        byte[] bArr = this.d;
        if (bArr.length < this.c && this.e[bArr.length - 1] != bArr[bArr.length - 1]) {
            throw new IllegalStateException("Counter in CTR/SIC mode out of range.");
        }
    }
}
