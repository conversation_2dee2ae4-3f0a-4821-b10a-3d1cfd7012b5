package o.at;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.NotImplementedError;
import kotlin.text.Typography;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.y.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\at\c.smali */
public final class c extends o.y.b<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static int h;
    private static char i;
    private static int j;
    String a;
    String b;
    d c;
    String d;
    o.h.d e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\at\c$e.smali */
    public interface e {
        void b(String str);

        void e(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        n();
        ImageFormat.getBitsPerPixel(0);
        ViewConfiguration.getMinimumFlingVelocity();
        View.combineMeasuredStates(0, 0);
        ViewConfiguration.getPressedStateDuration();
        int i2 = h + 5;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{46, 74, -29, UtilitiesSDKConstants.SRP_LABEL_MAC};
        $$e = Opcodes.IF_ICMPNE;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r8 = 73 - r8
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.at.c.$$d
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r8 = r8 + r6
            int r6 = r7 + 1
            r7 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.at.c.l(byte, short, byte, java.lang.Object[]):void");
    }

    static void n() {
        f = new char[]{30560, 30563, 30570, 30572, 30566, 30567, 30589, 30587, 30574, 30573, 30564, 30561, 30571, 30562, 30540, 30588};
        i = (char) 17041;
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        int i2 = j + 85;
        h = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                b o2 = o();
                int i3 = j + 27;
                h = i3 % 128;
                switch (i3 % 2 == 0 ? 'T' : 'B') {
                    case Opcodes.BASTORE /* 84 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return o2;
                }
            default:
                o();
                throw null;
        }
    }

    public c(Context context, e eVar, o.ei.c cVar) {
        super(context, eVar, cVar, o.bb.e.u);
    }

    public final void c(o.h.d dVar, String str, d dVar2, String str2) {
        int i2 = j + Opcodes.DNEG;
        h = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(TextUtils.indexOf("", "", 0) + 23, "\r\u0006\u0003\u0000\u000e\u0002\n\u0006\u000e\u0000\u000f\u000b\b\f\u0003\r\f\u0002㘮㘮\t\b㘵", (byte) (55 - Color.alpha(0)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(View.combineMeasuredStates(0, 0) + 18, "\u0000\u0004\r\u0006\u0003\u0000\u000e\u0002\n\u0006\u000e\u0000\u000f\u000b\b\f\u0003\r", (byte) (96 - TextUtils.getOffsetAfter("", 0)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.e = dVar;
        this.d = str;
        this.c = dVar2;
        this.b = str2;
        c();
        int i4 = j + 99;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    private b o() {
        b bVar = new b(this);
        int i2 = h + 13;
        j = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    @Override // o.y.b
    public final String a() {
        int i2 = h + Opcodes.DMUL;
        j = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k(23 - (ViewConfiguration.getDoubleTapTimeout() >> 16), "\r\u0006\u0003\u0000\u000e\u0002\n\u0006\u000e\u0000\u000f\u000b\b\f\u0003\r\f\u0002㘮㘮\t\b㘵", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 56), objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = j + 109;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                int i5 = 65 / 0;
                return intern;
            default:
                return intern;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\at\c$b.smali */
    static final class b extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static boolean a;
        private static int b;
        private static boolean c;
        private static char[] d;
        private static int e;
        private static int j;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            j = 1;
            d = new char[]{61815, 61818, 61813, 61823, 61783, 61568, 61814, 61820, 61574, 61817, 61809, 61822, 61575, 61770, 61767, 61761, 61762, 61771, 61765, 61766, 61764, 61573, 61798, 61571};
            c = true;
            a = true;
            e = 782102802;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(short r6, short r7, short r8, java.lang.Object[] r9) {
            /*
                int r8 = r8 * 2
                int r8 = r8 + 1
                int r6 = r6 + 4
                int r7 = r7 + 117
                byte[] r0 = o.at.c.b.$$d
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                r7 = r6
                goto L36
            L19:
                r3 = r2
            L1a:
                int r6 = r6 + 1
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r8) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                r4 = r0[r6]
                int r3 = r3 + 1
                r5 = r7
                r7 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r5
            L36:
                int r6 = -r6
                int r6 = r6 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: o.at.c.b.B(short, short, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{71, -50, -52, -118};
            $$e = Opcodes.D2F;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = b + Opcodes.LSHR;
            j = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = b + 79;
            j = i % 128;
            char c2 = i % 2 == 0 ? 'V' : (char) 0;
            o.cf.d b2 = b(context);
            switch (c2) {
                case Opcodes.SASTORE /* 86 */:
                    int i2 = 45 / 0;
                    break;
            }
            int i3 = b + 33;
            j = i3 % 128;
            int i4 = i3 % 2;
            return b2;
        }

        b(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = j + 3;
            b = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(null, 127 - (ViewConfiguration.getWindowTouchSlop() >> 8), null, "\u008d\u008c\u008b\u008a\u0089\u0088\u0083\u0087\u0083\u0086\u0085\u0084\u0081\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = j + 57;
            b = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        private static o.cf.d b(Context context) {
            Object[] objArr = new Object[1];
            w(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u0091\u0093\u0093\u0095\u0094\u0090\u008f\u008e\u008f\u0092\u0090\u0093\u0092\u008e\u0091\u0090\u0090\u008f\u008e", objArr);
            o.cf.d dVar = new o.cf.d(context, 41, ((String) objArr[0]).intern());
            int i = j + 59;
            b = i % 128;
            switch (i % 2 != 0 ? '\n' : 'J') {
                case 'J':
                    return dVar;
                default:
                    int i2 = 53 / 0;
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d, NotImplementedError {
            o.eg.b a2 = new o.at.b().c(((c) e()).c).b(((c) e()).b).a();
            int i = j + 3;
            b = i % 128;
            int i2 = i % 2;
            return a2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            j jVar = new j(((c) e()).d, false, ((c) e()).e);
            int i = j + 89;
            b = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = b + 55;
            int i2 = i % 128;
            j = i2;
            switch (i % 2 == 0) {
                case true:
                    int i3 = 31 / 0;
                    break;
            }
            int i4 = i2 + 109;
            b = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    int i5 = 12 / 0;
                    return null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = j + 27;
            b = i % 128;
            int i2 = i % 2;
            c cVar = (c) e();
            Object[] objArr = new Object[1];
            w(null, 128 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), null, "\u0088\u0083\u0084\u0098\u0097\u0082\u0089\u0096\u008b", objArr);
            cVar.a = bVar.q(((String) objArr[0]).intern());
            int i3 = b + 39;
            j = i3 % 128;
            switch (i3 % 2 == 0 ? Typography.quote : '\n') {
                case '\"':
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = j + 75;
            b = i % 128;
            switch (i % 2 != 0 ? Typography.greater : 'C') {
                case '>':
                    ((c) e()).j().b(((c) e()).a);
                    int i2 = 17 / 0;
                    return;
                default:
                    ((c) e()).j().b(((c) e()).a);
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = b + 47;
            j = i % 128;
            switch (i % 2 != 0) {
                case true:
                    ((c) e()).j().e(dVar);
                    break;
                default:
                    ((c) e()).j().e(dVar);
                    int i2 = 33 / 0;
                    break;
            }
            int i3 = b + 91;
            j = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 778
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.at.c.b.w(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1108
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.at.c.k(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
