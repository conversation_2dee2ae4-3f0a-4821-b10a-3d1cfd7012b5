package o.dy;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dy\b.smali */
public final class b implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int c;
    public static final b d;
    private static char[] e;

    static void a() {
        e = new char[]{33458, 60270, 20767, 48943, 51738, 41958, 6551, 63399, 28019, 56170, 45312, 12068, 34013, 29426, 59559, 18102, 15426, 43632, '\b', 65065, 22490, 52610, 48022, 4428, 36724, 25885, 54062, 18638, 9949, 40078, 2725, 57430, 24170, 13336, 41528, 7119, 61897, 28597, 50496, 45925, 10504, 34615, 31888, 60076};
        b = 1422294501164008727L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = 105 - r8
            int r7 = r7 * 4
            int r7 = r7 + 5
            byte[] r0 = o.dy.b.$$a
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r8
            r4 = r2
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r6 = r6 + r3
            int r8 = r8 + 1
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.b.g(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, -60, 102, -85};
        $$b = 196;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        c = 1;
        a();
        d = new b();
        int i = a + 87;
        c = i % 128;
        int i2 = i % 2;
    }

    private b() {
    }

    @Override // o.dy.e
    public final String e() {
        int i = a + Opcodes.DNEG;
        c = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f((char) (TextUtils.indexOf("", "", 0) + 44568), ViewConfiguration.getTouchSlop() >> 8, 3 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = a + 41;
        c = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                int i4 = 68 / 0;
                return intern;
            default:
                return intern;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        f((char) (View.resolveSize(0, 0) + 59024), 4 - (ViewConfiguration.getScrollBarSize() >> 8), Color.alpha(0) + 40, objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(e()).append('\'').append('}').toString();
        int i = c + 25;
        a = i % 128;
        int i2 = i % 2;
        return obj;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 736
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.b.f(char, int, int, java.lang.Object[]):void");
    }
}
