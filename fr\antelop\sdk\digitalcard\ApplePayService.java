package fr.antelop.sdk.digitalcard;

import fr.antelop.sdk.util.OperationCallback;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\ApplePayService.smali */
public final class ApplePayService {
    ApplePayService() {
    }

    public final void getStatus(OperationCallback<DigitalCardServiceStatus> operationCallback) {
        operationCallback.onSuccess(DigitalCardServiceStatus.NotSupportedByDevice);
    }
}
