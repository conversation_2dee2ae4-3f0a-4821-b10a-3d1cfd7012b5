package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.math.ec.ECPoint;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\WTauNafPreCompInfo.smali */
public class WTauNafPreCompInfo implements PreCompInfo {
    protected ECPoint.AbstractF2m[] a = null;

    public ECPoint.AbstractF2m[] getPreComp() {
        return this.a;
    }

    public void setPreComp(ECPoint.AbstractF2m[] abstractF2mArr) {
        this.a = abstractF2mArr;
    }
}
