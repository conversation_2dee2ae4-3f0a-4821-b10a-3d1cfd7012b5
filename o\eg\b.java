package o.eg;

import android.graphics.Color;
import android.graphics.ColorSpace;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.h;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final Object b;
    private static long c;
    private static int d;
    private static final Double e;
    private static int f;
    private static int j;
    private final LinkedHashMap<String, Object> a;

    private static void L(byte b2, int i, byte b3, Object[] objArr) {
        int i2 = i + 4;
        int i3 = b3 + 68;
        byte[] bArr = $$a;
        int i4 = (b2 * 4) + 1;
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            i3 = i6 + i3;
            i6 = i6;
            i2 = i2;
        }
        while (true) {
            int i7 = i2 + 1;
            i5++;
            bArr2[i5] = (byte) i3;
            if (i5 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i3 += bArr[i7];
            i6 = i6;
            i2 = i7;
        }
    }

    static void i() {
        c = 1035056011617340118L;
        d = 874635317;
    }

    static void init$0() {
        $$a = new byte[]{71, 41, -111, Base64.padSymbol};
        $$b = Opcodes.INVOKEDYNAMIC;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        i();
        e = Double.valueOf(-0.0d);
        b = new Object() { // from class: o.eg.b.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int b;
            private static byte[] c;
            private static short[] d;
            private static int e;
            private static int h;
            private static int j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                j = 0;
                h = 1;
                c = new byte[]{45, -38, 42, -112};
                a = 909053665;
                b = 347297446;
                e = -24540413;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r7, int r8, byte r9, java.lang.Object[] r10) {
                /*
                    int r9 = r9 * 2
                    int r9 = r9 + 108
                    int r8 = r8 * 2
                    int r8 = 3 - r8
                    byte[] r0 = o.eg.b.AnonymousClass4.$$a
                    int r7 = r7 * 2
                    int r7 = r7 + 1
                    byte[] r1 = new byte[r7]
                    r2 = 0
                    if (r0 != 0) goto L18
                    r3 = r9
                    r4 = r2
                    r9 = r8
                    r8 = r7
                    goto L30
                L18:
                    r3 = r2
                L19:
                    int r4 = r3 + 1
                    byte r5 = (byte) r9
                    r1[r3] = r5
                    if (r4 != r7) goto L28
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L28:
                    int r8 = r8 + 1
                    r3 = r0[r8]
                    r6 = r8
                    r8 = r7
                    r7 = r9
                    r9 = r6
                L30:
                    int r7 = r7 + r3
                    r3 = r4
                    r6 = r9
                    r9 = r7
                    r7 = r8
                    r8 = r6
                    goto L19
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eg.b.AnonymousClass4.g(int, int, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{49, -72, 33, -42};
                $$b = 255;
            }

            public final boolean equals(Object obj) {
                int i = j;
                int i2 = i + Opcodes.DNEG;
                h = i2 % 128;
                int i3 = i2 % 2;
                switch (obj == this) {
                    case false:
                        int i4 = i + 47;
                        h = i4 % 128;
                        int i5 = i4 % 2;
                        switch (obj != null) {
                            case false:
                                break;
                            default:
                                int i6 = i + Opcodes.LNEG;
                                h = i6 % 128;
                                if (i6 % 2 != 0) {
                                    return false;
                                }
                                int i7 = 89 / 0;
                                return false;
                        }
                    default:
                        return true;
                }
            }

            public final int hashCode() {
                int i = h + 95;
                j = i % 128;
                int i2 = i % 2;
                int hashCode = Objects.hashCode(null);
                int i3 = j + 31;
                h = i3 % 128;
                int i4 = i3 % 2;
                return hashCode;
            }

            public final String toString() {
                int i = h + 15;
                j = i % 128;
                int i2 = i % 2;
                Object[] objArr = new Object[1];
                f((byte) (View.resolveSize(0, 0) - 67), 31389 - AndroidCharacter.getMirror('0'), (short) View.resolveSize(0, 0), (-109) - Color.blue(0), (-580671432) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
                String intern = ((String) objArr[0]).intern();
                int i3 = j + 33;
                h = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return intern;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:95:0x0343, code lost:
            
                r3 = r7;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
                /*
                    Method dump skipped, instructions count: 1028
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eg.b.AnonymousClass4.f(byte, int, short, int, int, java.lang.Object[]):void");
            }
        };
        int i = j + 53;
        f = i % 128;
        int i2 = i % 2;
    }

    public b() {
        this.a = new LinkedHashMap<>();
    }

    private b(Map<?, ?> map) {
        this();
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            String str = (String) entry.getKey();
            if (str == null) {
                Object[] objArr = new Object[1];
                J("䞽䟖ꆗ毻⤟\uebdb澤駫Ţ뵴㎱뗠ក燆奂", Drawable.resolveOpacity(0, 0) + 1, objArr);
                throw new NullPointerException(((String) objArr[0]).intern());
            }
            this.a.put(str, d(entry.getValue()));
        }
    }

    private b(j jVar) throws d {
        Object c2 = jVar.c();
        if (c2 instanceof b) {
            this.a = ((b) c2).a;
            return;
        }
        Object[] objArr = new Object[1];
        K(ExpandableListView.getPackedPositionGroup(0L) + 3, "\ufff4\ufff8\uffef\u0019\b\n\u000f\u0007\ufff4\ufff3", 10 - TextUtils.indexOf("", "", 0), TextUtils.getTrimmedLength("") + Opcodes.IF_ACMPEQ, true, objArr);
        throw a.a(c2, ((String) objArr[0]).intern());
    }

    public b(String str) throws d {
        this(new j(str));
    }

    public final int d() {
        int i = f + 31;
        j = i % 128;
        int i2 = i % 2;
        int size = this.a.size();
        int i3 = j + Opcodes.DNEG;
        f = i3 % 128;
        int i4 = i3 % 2;
        return size;
    }

    public final b d(String str, boolean z) throws d {
        int i = f + 19;
        j = i % 128;
        switch (i % 2 == 0) {
            case false:
                this.a.put(G(str), Boolean.valueOf(z));
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.a.put(G(str), Boolean.valueOf(z));
                int i2 = f + 87;
                j = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        int i3 = 83 / 0;
                        return this;
                    default:
                        return this;
                }
        }
    }

    public final b c(String str, double d2) throws d {
        int i = f + Opcodes.LMUL;
        j = i % 128;
        int i2 = i % 2;
        this.a.put(G(str), Double.valueOf(a.b(d2)));
        int i3 = j + 91;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? '^' : '[') {
            case Opcodes.DUP2_X2 /* 94 */:
                int i4 = 58 / 0;
                return this;
            default:
                return this;
        }
    }

    public final b d(String str, int i) throws d {
        int i2 = f + Opcodes.DREM;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 16 : (char) 0) {
            case 16:
                this.a.put(G(str), Integer.valueOf(i));
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.a.put(G(str), Integer.valueOf(i));
                int i3 = f + Opcodes.LSHR;
                j = i3 % 128;
                switch (i3 % 2 != 0 ? '=' : (char) 1) {
                    case 1:
                        return this;
                    default:
                        int i4 = 14 / 0;
                        return this;
                }
        }
    }

    public final b d(String str, long j2) throws d {
        int i = j + Opcodes.LMUL;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                this.a.put(G(str), Long.valueOf(j2));
                return this;
            default:
                this.a.put(G(str), Long.valueOf(j2));
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final b d(String str, Object obj) throws d {
        int i = f;
        int i2 = i + 43;
        j = i2 % 128;
        Object obj2 = null;
        if (i2 % 2 != 0) {
            obj2.hashCode();
            throw null;
        }
        switch (obj == null) {
            case true:
                int i3 = i + Opcodes.LNEG;
                j = i3 % 128;
                if (i3 % 2 == 0) {
                    this.a.remove(str);
                    return this;
                }
                this.a.remove(str);
                obj2.hashCode();
                throw null;
            default:
                if (obj instanceof Number) {
                    int i4 = i + 41;
                    j = i4 % 128;
                    if (i4 % 2 != 0) {
                        a.b(((Number) obj).doubleValue());
                        int i5 = 86 / 0;
                    } else {
                        a.b(((Number) obj).doubleValue());
                    }
                }
                this.a.put(G(str), obj);
                return this;
        }
    }

    private static String G(String str) throws d {
        int i = f + 77;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        if (str == null) {
            Object[] objArr = new Object[1];
            J("鳁鲏鯻冓칩࠻뒖ꎞ\ue64d娖퀏噅쳺䮶빬\uf844\ue4c8Ꮖ嘆肭㰿㬧滚ꢛ吙썂", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
            throw new d(((String) objArr[0]).intern());
        }
        int i4 = i2 + 15;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return str;
            default:
                int i5 = 64 / 0;
                return str;
        }
    }

    public final Object c(String str) {
        int i = j + 77;
        f = i % 128;
        int i2 = i % 2;
        Object remove = this.a.remove(str);
        int i3 = f + 69;
        j = i3 % 128;
        int i4 = i3 % 2;
        return remove;
    }

    /* JADX WARN: Code restructure failed: missing block: B:22:0x0028, code lost:
    
        if (r5 != null) goto L21;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(java.lang.String r5) {
        /*
            r4 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 89
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 74
            goto L11
        Lf:
            r0 = 90
        L11:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 74: goto L1f;
                default: goto L16;
            }
        L16:
            java.util.LinkedHashMap<java.lang.String, java.lang.Object> r0 = r4.a
            java.lang.Object r5 = r0.get(r5)
            if (r5 == 0) goto L30
            goto L2d
        L1f:
            java.util.LinkedHashMap<java.lang.String, java.lang.Object> r0 = r4.a
            java.lang.Object r5 = r0.get(r5)
            r0 = 11
            int r0 = r0 / r2
            if (r5 == 0) goto L4c
        L2a:
            goto L36
        L2b:
            r5 = move-exception
            throw r5
        L2d:
            r0 = 91
            goto L32
        L30:
            r0 = 59
        L32:
            switch(r0) {
                case 59: goto L4c;
                default: goto L35;
            }
        L35:
            goto L2a
        L36:
            int r0 = o.eg.b.j
            int r0 = r0 + 13
            int r3 = r0 % 128
            o.eg.b.f = r3
            int r0 = r0 % 2
            java.lang.Object r0 = o.eg.b.b
            if (r5 != r0) goto L46
            r5 = r1
            goto L47
        L46:
            r5 = r2
        L47:
            switch(r5) {
                case 0: goto L4b;
                default: goto L4a;
            }
        L4a:
            goto L4c
        L4b:
            return r2
        L4c:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.a(java.lang.String):boolean");
    }

    private static boolean c(Object obj) {
        int i = f + 81;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        switch (obj != null) {
            case true:
                switch (obj != b) {
                    case false:
                        break;
                    default:
                        int i4 = i2 + 83;
                        f = i4 % 128;
                        int i5 = i4 % 2;
                        return false;
                }
            default:
                return true;
        }
    }

    public final boolean b(String str) {
        int i = f + 99;
        j = i % 128;
        int i2 = i % 2;
        boolean containsKey = this.a.containsKey(str);
        int i3 = j + 89;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? 'G' : (char) 4) {
            case 4:
                return containsKey;
            default:
                throw null;
        }
    }

    public final Object e(String str) throws d {
        int i = j + 57;
        f = i % 128;
        switch (i % 2 == 0 ? '\b' : '*') {
            case '*':
                Object obj = this.a.get(str);
                if (obj != null) {
                    int i2 = f + 81;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    return obj;
                }
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                K(1 - (ViewConfiguration.getScrollDefaultDelay() >> 16), "\ufff6\uffc8\u001a\u0017\u000e\uffc8\r\u001d\u0014\t\u001e\uffc8\u0017", (Process.myPid() >> 22) + 13, TextUtils.indexOf((CharSequence) "", '0') + Opcodes.IF_ICMPGT, true, objArr);
                throw new d(sb.append(((String) objArr[0]).intern()).append(str).toString());
            default:
                this.a.get(str);
                throw null;
        }
    }

    public final Object d(String str) {
        int i = j + 97;
        f = i % 128;
        switch (i % 2 == 0 ? 'O' : '4') {
            case '4':
                return this.a.get(str);
            default:
                int i2 = 28 / 0;
                return this.a.get(str);
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x001c, code lost:
    
        if (r3 != null) goto L19;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.Boolean g(java.lang.String r8) throws o.eg.d {
        /*
            r7 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 7
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            switch(r0) {
                case 1: goto L1f;
                default: goto L14;
            }
        L14:
            java.lang.Object r0 = r7.e(r8)
            java.lang.Boolean r3 = o.eg.a.c(r0)
            if (r3 == 0) goto L4a
        L1e:
            goto L35
        L1f:
            java.lang.Object r0 = r7.e(r8)
            java.lang.Boolean r3 = o.eg.a.c(r0)
            r4 = 31
            int r4 = r4 / r2
            if (r3 != 0) goto L2e
            r4 = r2
            goto L2f
        L2e:
            r4 = r1
        L2f:
            switch(r4) {
                case 1: goto L1e;
                default: goto L32;
            }
        L32:
            goto L4a
        L33:
            r8 = move-exception
            throw r8
        L35:
            int r8 = o.eg.b.j
            int r8 = r8 + 29
            int r0 = r8 % 128
            o.eg.b.f = r0
            int r8 = r8 % 2
            if (r8 == 0) goto L43
            return r3
        L43:
            r8 = 0
            r8.hashCode()     // Catch: java.lang.Throwable -> L48
            throw r8     // Catch: java.lang.Throwable -> L48
        L48:
            r8 = move-exception
            throw r8
        L4a:
            double r3 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r2)
            r5 = 0
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            int r3 = 1 - r3
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r4 = "ଟ\u0b7d崂靤\udee7毢⍞攨\uf6c2䪚㖕"
            J(r4, r3, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.eg.d r8 = o.eg.a.b(r8, r0, r1)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.g(java.lang.String):java.lang.Boolean");
    }

    public final Boolean h(String str) {
        int i = j + 87;
        f = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                b(str, (Boolean) null);
                obj.hashCode();
                throw null;
            default:
                Boolean b2 = b(str, (Boolean) null);
                int i2 = j + 39;
                f = i2 % 128;
                switch (i2 % 2 == 0) {
                    case true:
                        int i3 = 49 / 0;
                        return b2;
                    default:
                        return b2;
                }
        }
    }

    public final Boolean b(String str, Boolean bool) {
        int i = j + 23;
        f = i % 128;
        Object obj = null;
        if (i % 2 != 0) {
            Boolean c2 = a.c(d(str));
            if (c2 != null) {
                return c2;
            }
            int i2 = j + 25;
            f = i2 % 128;
            switch (i2 % 2 == 0 ? ')' : (char) 27) {
                case ')':
                    throw null;
                default:
                    return bool;
            }
        }
        a.c(d(str));
        obj.hashCode();
        throw null;
    }

    public final Double f(String str) throws d {
        char c2;
        Object e2 = e(str);
        Double d2 = a.d(e2);
        if (d2 != null) {
            c2 = 0;
        } else {
            c2 = 17;
        }
        switch (c2) {
            case 17:
                int i = j + 79;
                f = i % 128;
                int i2 = i % 2;
                Object[] objArr = new Object[1];
                K(3 - ((byte) KeyEvent.getModifierMetaStateMask()), "\ufff9\f\u0006\ufffb￼\u0003", (ViewConfiguration.getTouchSlop() >> 8) + 6, 178 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), true, objArr);
                throw a.b(str, e2, ((String) objArr[0]).intern());
            default:
                int i3 = f + Opcodes.DREM;
                j = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return d2;
                }
        }
    }

    private Double E(String str) {
        int i = f + 93;
        j = i % 128;
        switch (i % 2 != 0 ? '2' : '1') {
            case '1':
                return c(str, (Double) null);
            default:
                c(str, (Double) null);
                throw null;
        }
    }

    public final Double c(String str, Double d2) {
        Double d3 = a.d(d(str));
        switch (d3 != null ? '`' : 'O') {
            case Opcodes.IADD /* 96 */:
                int i = j + 89;
                f = i % 128;
                switch (i % 2 == 0 ? ',' : 'Z') {
                    case ',':
                        throw null;
                    default:
                        return d3;
                }
            default:
                int i2 = j + 13;
                f = i2 % 128;
                int i3 = i2 % 2;
                return d2;
        }
    }

    public final Integer i(String str) throws d {
        boolean z;
        int i = j + Opcodes.LSHL;
        f = i % 128;
        int i2 = i % 2;
        Object e2 = e(str);
        Integer a = a.a(e2);
        if (a != null) {
            z = true;
        } else {
            z = false;
        }
        switch (z) {
            case false:
                int i3 = f + 73;
                j = i3 % 128;
                int i4 = i3 % 2;
                Object[] objArr = new Object[1];
                K((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\ufffb\u0006\u0000", 3 - TextUtils.getOffsetAfter("", 0), 184 - (ViewConfiguration.getEdgeSlop() >> 16), true, objArr);
                throw a.b(str, e2, ((String) objArr[0]).intern());
            default:
                return a;
        }
    }

    public final Integer j(String str) {
        int i = j + Opcodes.DSUB;
        f = i % 128;
        boolean z = i % 2 == 0;
        Integer e2 = e(str, (Integer) null);
        switch (z) {
            case true:
                int i2 = 37 / 0;
                break;
        }
        int i3 = j + 57;
        f = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:24:0x002b, code lost:
    
        if (r3 != null) goto L21;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.Integer e(java.lang.String r3, java.lang.Integer r4) {
        /*
            r2 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 19
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 45
            goto L11
        Lf:
            r0 = 63
        L11:
            r1 = 0
            switch(r0) {
                case 45: goto L20;
                default: goto L15;
            }
        L15:
            java.lang.Object r3 = r2.d(r3)
            java.lang.Integer r3 = o.eg.a.a(r3)
            if (r3 == 0) goto L30
            goto L31
        L20:
            java.lang.Object r3 = r2.d(r3)
            java.lang.Integer r3 = o.eg.a.a(r3)
            r0 = 94
            int r0 = r0 / r1
            if (r3 == 0) goto L35
        L2d:
            goto L36
        L2e:
            r3 = move-exception
            throw r3
        L30:
            r1 = 1
        L31:
            switch(r1) {
                case 1: goto L35;
                default: goto L34;
            }
        L34:
            goto L2d
        L35:
            return r4
        L36:
            int r4 = o.eg.b.f
            int r4 = r4 + 85
            int r0 = r4 % 128
            o.eg.b.j = r0
            int r4 = r4 % 2
            if (r4 != 0) goto L43
            return r3
        L43:
            r3 = 0
            throw r3     // Catch: java.lang.Throwable -> L45
        L45:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.e(java.lang.String, java.lang.Integer):java.lang.Integer");
    }

    public final Short k(String str) throws d {
        int i = j + 53;
        f = i % 128;
        switch (i % 2 == 0 ? '0' : (char) 20) {
            case 20:
                Object e2 = e(str);
                Short b2 = a.b(e2);
                if (b2 == null) {
                    Object[] objArr = new Object[1];
                    K(View.resolveSize(0, 0) + 3, "\uffff\ufff8\u0003\u0004\u0002", 5 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getDoubleTapTimeout() >> 16) + Opcodes.INVOKEDYNAMIC, true, objArr);
                    throw a.b(str, e2, ((String) objArr[0]).intern());
                }
                int i2 = f + Opcodes.DSUB;
                j = i2 % 128;
                int i3 = i2 % 2;
                return b2;
            default:
                a.b(e(str));
                throw null;
        }
    }

    public final Short d(String str, Short sh) {
        int i = f + 73;
        j = i % 128;
        int i2 = i % 2;
        Short b2 = a.b(d(str));
        Object obj = null;
        switch (b2 != null ? '6' : Typography.less) {
            case '<':
                int i3 = j + 49;
                f = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        return sh;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                int i4 = f + Opcodes.DSUB;
                j = i4 % 128;
                if (i4 % 2 == 0) {
                    return b2;
                }
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0033, code lost:
    
        if (r3 == null) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.Long m(java.lang.String r6) throws o.eg.d {
        /*
            r5 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 22
            goto L11
        Lf:
            r0 = 10
        L11:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 10: goto L1f;
                default: goto L16;
            }
        L16:
            java.lang.Object r0 = r5.e(r6)
            java.lang.Long r3 = o.eg.a.e(r0)
            goto L30
        L1f:
            java.lang.Object r0 = r5.e(r6)
            java.lang.Long r3 = o.eg.a.e(r0)
            if (r3 != 0) goto L2b
            r4 = r2
            goto L2c
        L2b:
            r4 = r1
        L2c:
            switch(r4) {
                case 0: goto L35;
                default: goto L2f;
            }
        L2f:
            goto L59
        L30:
            r4 = 48
            int r4 = r4 / r2
            if (r3 != 0) goto L2f
        L35:
            int r3 = o.eg.b.f
            int r3 = r3 + 19
            int r4 = r3 % 128
            o.eg.b.j = r4
            int r3 = r3 % 2
            int r3 = android.view.KeyEvent.getDeadChar(r2, r2)
            int r3 = 1 - r3
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r4 = "\ueeec\uee80맹玟ꮧ岂㿛˾"
            J(r4, r3, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.eg.d r6 = o.eg.a.b(r6, r0, r1)
            throw r6
        L59:
            return r3
        L5a:
            r6 = move-exception
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.m(java.lang.String):java.lang.Long");
    }

    public final Long n(String str) {
        int i = j + 47;
        f = i % 128;
        int i2 = i % 2;
        Long e2 = e(str, (Long) null);
        int i3 = f + 91;
        j = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    public final Long e(String str, Long l) {
        int i = j + 11;
        f = i % 128;
        int i2 = i % 2;
        Long e2 = a.e(d(str));
        switch (e2 != null ? '_' : (char) 25) {
            case 25:
                return l;
            default:
                int i3 = f + 5;
                j = i3 % 128;
                int i4 = i3 % 2;
                return e2;
        }
    }

    public final BigDecimal l(String str) throws d {
        int i = f + 21;
        j = i % 128;
        int i2 = i % 2;
        try {
            BigDecimal valueOf = BigDecimal.valueOf(f(str).doubleValue());
            int i3 = j + 1;
            f = i3 % 128;
            switch (i3 % 2 == 0 ? '?' : '6') {
                case Opcodes.ISTORE /* 54 */:
                    return valueOf;
                default:
                    int i4 = 68 / 0;
                    return valueOf;
            }
        } catch (NumberFormatException e2) {
            throw new d(e2.getMessage());
        }
    }

    public final BigDecimal o(String str) {
        int i = f + Opcodes.DNEG;
        j = i % 128;
        int i2 = i % 2;
        BigDecimal I = I(str);
        int i3 = j + 3;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? 'B' : 'X') {
            case Opcodes.POP2 /* 88 */:
                return I;
            default:
                int i4 = 97 / 0;
                return I;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x002c, code lost:
    
        if (r7 == null) goto L19;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private java.math.BigDecimal I(java.lang.String r7) {
        /*
            r6 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 9
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            r3 = 0
            switch(r0) {
                case 0: goto L1a;
                default: goto L15;
            }
        L15:
            java.lang.Double r7 = r6.E(r7)
            goto L29
        L1a:
            java.lang.Double r7 = r6.E(r7)
            if (r7 != 0) goto L23
            r0 = 56
            goto L25
        L23:
            r0 = 30
        L25:
            switch(r0) {
                case 30: goto L30;
                default: goto L28;
            }
        L28:
            goto L2f
        L29:
            r0 = 28
            int r0 = r0 / r2
            if (r7 != 0) goto L30
            goto L28
        L2f:
            return r3
        L30:
            double r4 = r7.doubleValue()     // Catch: java.lang.NumberFormatException -> L50
            java.math.BigDecimal r7 = java.math.BigDecimal.valueOf(r4)     // Catch: java.lang.NumberFormatException -> L50
            int r0 = o.eg.b.j
            int r0 = r0 + 85
            int r3 = r0 % 128
            o.eg.b.f = r3
            int r0 = r0 % 2
            if (r0 != 0) goto L45
            r1 = r2
        L45:
            switch(r1) {
                case 1: goto L49;
                default: goto L48;
            }
        L48:
            goto L4a
        L49:
            return r7
        L4a:
            r0 = 41
            int r0 = r0 / r2
            return r7
        L4e:
            r7 = move-exception
            throw r7
        L50:
            r7 = move-exception
            return r3
        L52:
            r7 = move-exception
            throw r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.I(java.lang.String):java.math.BigDecimal");
    }

    public final String r(String str) throws d {
        int i = f + 21;
        j = i % 128;
        int i2 = i % 2;
        Object e2 = e(str);
        switch (c(e2) ? 'O' : (char) 4) {
            case 4:
                String f2 = a.f(e2);
                if (f2 != null) {
                    int i3 = f + 89;
                    j = i3 % 128;
                    switch (i3 % 2 != 0 ? Typography.dollar : '`') {
                        case '$':
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            return f2;
                    }
                }
                int i4 = j + 43;
                f = i4 % 128;
                if (i4 % 2 == 0) {
                    Object[] objArr = new Object[1];
                    J("潪漹돵禈࿒㽁䜠诂鮲愳", 1 % Color.red(0), objArr);
                    throw a.b(str, e2, ((String) objArr[0]).intern());
                }
                Object[] objArr2 = new Object[1];
                J("潪漹돵禈࿒㽁䜠诂鮲愳", Color.red(0) + 1, objArr2);
                throw a.b(str, e2, ((String) objArr2[0]).intern());
            default:
                int i5 = f + Opcodes.LREM;
                j = i5 % 128;
                if (i5 % 2 != 0) {
                }
                Object[] objArr3 = new Object[1];
                J("潪漹돵禈࿒㽁䜠诂鮲愳", ((Process.getThreadPriority(0) + 20) >> 6) + 1, objArr3);
                throw a.b(str, e2, ((String) objArr3[0]).intern());
        }
    }

    public final String q(String str) {
        int i = f + 81;
        j = i % 128;
        int i2 = i % 2;
        String c2 = c(str, (String) null);
        int i3 = j + 49;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 25 : '%') {
            case '%':
                return c2;
            default:
                int i4 = 39 / 0;
                return c2;
        }
    }

    public final String c(String str, String str2) {
        int i = f + 71;
        j = i % 128;
        Object obj = null;
        if (i % 2 != 0) {
            c(d(str));
            throw null;
        }
        Object d2 = d(str);
        switch (!c(d2)) {
            case false:
                int i2 = j + 89;
                f = i2 % 128;
                if (i2 % 2 != 0) {
                    return str2;
                }
                obj.hashCode();
                throw null;
            default:
                String f2 = a.f(d2);
                switch (f2 != null ? 'K' : '\\') {
                    case 'K':
                        int i3 = j + 3;
                        f = i3 % 128;
                        int i4 = i3 % 2;
                        return f2;
                    default:
                        return str2;
                }
        }
    }

    public final e s(String str) throws d {
        int i = f + 85;
        j = i % 128;
        if (i % 2 == 0) {
            Object e2 = e(str);
            switch (e2 instanceof e ? (char) 23 : '0') {
                case 23:
                    e eVar = (e) e2;
                    int i2 = f + 55;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    return eVar;
                default:
                    Object[] objArr = new Object[1];
                    J("⮕⯟톫ᯱﮚ幍ϰ\ue9ae펃濇虆\u0018箤", Color.rgb(0, 0, 0) + 16777217, objArr);
                    throw a.b(str, e2, ((String) objArr[0]).intern());
            }
        }
        boolean z = e(str) instanceof e;
        throw null;
    }

    public final e p(String str) {
        int i = j + 93;
        f = i % 128;
        int i2 = i % 2;
        Object d2 = d(str);
        switch (d2 instanceof e) {
            case false:
                return null;
            default:
                int i3 = j + 39;
                f = i3 % 128;
                e eVar = (e) d2;
                switch (i3 % 2 == 0 ? 'V' : 'X') {
                    case Opcodes.POP2 /* 88 */:
                        return eVar;
                    default:
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.lang.String[] a(o.eg.e r6) throws o.eg.d {
        /*
            int r0 = o.eg.b.f
            int r0 = r0 + 43
            int r1 = r0 % 128
            o.eg.b.j = r1
            int r0 = r0 % 2
            int r0 = r6.d()
            java.lang.String[] r0 = new java.lang.String[r0]
            int r1 = o.eg.b.f
            int r1 = r1 + 69
            int r2 = r1 % 128
            o.eg.b.j = r2
            int r1 = r1 % 2
            r1 = 0
            r2 = r1
        L1d:
            int r3 = r6.d()
            r4 = 1
            if (r2 >= r3) goto L26
            r3 = r1
            goto L27
        L26:
            r3 = r4
        L27:
            switch(r3) {
                case 0: goto L2b;
                default: goto L2a;
            }
        L2a:
            goto L4f
        L2b:
            int r3 = o.eg.b.j
            int r3 = r3 + 41
            int r5 = r3 % 128
            o.eg.b.f = r5
            int r3 = r3 % 2
            if (r3 != 0) goto L39
            r4 = r1
            goto L3a
        L39:
        L3a:
            switch(r4) {
                case 1: goto L46;
                default: goto L3d;
            }
        L3d:
            java.lang.String r3 = r6.d(r2)
            r0[r2] = r3
            int r2 = r2 + 41
            goto L1d
        L46:
            java.lang.String r3 = r6.d(r2)
            r0[r2] = r3
            int r2 = r2 + 1
            goto L1d
        L4f:
            int r6 = o.eg.b.f
            int r6 = r6 + 55
            int r1 = r6 % 128
            o.eg.b.j = r1
            int r6 = r6 % 2
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.a(o.eg.e):java.lang.String[]");
    }

    public final String[] t(String str) throws d {
        Object e2 = e(str);
        switch (!(e2 instanceof e)) {
            case true:
                int i = f + 85;
                j = i % 128;
                if (i % 2 != 0) {
                    Object[] objArr = new Object[1];
                    J("⮕⯟톫ᯱﮚ幍ϰ\ue9ae펃濇虆\u0018箤", 1 - (ViewConfiguration.getKeyRepeatDelay() >> Opcodes.IUSHR), objArr);
                    throw a.b(str, e2, ((String) objArr[0]).intern());
                }
                Object[] objArr2 = new Object[1];
                J("⮕⯟톫ᯱﮚ幍ϰ\ue9ae펃濇虆\u0018箤", 1 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr2);
                throw a.b(str, e2, ((String) objArr2[0]).intern());
            default:
                String[] a = a((e) e2);
                int i2 = f + 45;
                j = i2 % 128;
                switch (i2 % 2 != 0 ? '\r' : 'U') {
                    case Opcodes.CASTORE /* 85 */:
                        return a;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final String[] w(String str) throws d {
        int i = f + Opcodes.LSHR;
        j = i % 128;
        int i2 = i % 2;
        String[] F = F(str);
        int i3 = f + 37;
        j = i3 % 128;
        int i4 = i3 % 2;
        return F;
    }

    private String[] F(String str) throws d {
        int i = j + Opcodes.DNEG;
        f = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? 'I' : (char) 3) {
            case 'I':
                c(d(str));
                throw null;
            default:
                Object d2 = d(str);
                if (c(d2)) {
                    return null;
                }
                if (d2 instanceof e) {
                    return a((e) d2);
                }
                int i2 = f + 71;
                j = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        obj.hashCode();
                        throw null;
                    default:
                        return null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.lang.Integer[] c(o.eg.e r6) throws o.eg.d {
        /*
            int r0 = o.eg.b.j
            int r0 = r0 + 25
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            if (r0 != 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 1: goto L1c;
                default: goto L14;
            }
        L14:
            int r0 = r6.d()
            java.lang.Integer[] r0 = new java.lang.Integer[r0]
            r3 = r1
            goto L23
        L1c:
            int r0 = r6.d()
            java.lang.Integer[] r0 = new java.lang.Integer[r0]
            r3 = r2
        L23:
            int r4 = r6.d()
            if (r3 >= r4) goto L2b
            r4 = r2
            goto L2c
        L2b:
            r4 = r1
        L2c:
            switch(r4) {
                case 0: goto L38;
                default: goto L2f;
            }
        L2f:
            java.lang.Integer r4 = r6.c(r3)
            r0[r3] = r4
            int r3 = r3 + 1
            goto L39
        L38:
            return r0
        L39:
            int r4 = o.eg.b.f
            int r4 = r4 + 87
            int r5 = r4 % 128
            o.eg.b.j = r5
            int r4 = r4 % 2
            goto L23
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.c(o.eg.e):java.lang.Integer[]");
    }

    public final Integer[] y(String str) throws d {
        int i = f + Opcodes.LMUL;
        j = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? 'E' : '%') {
            case 'E':
                boolean z = e(str) instanceof e;
                obj.hashCode();
                throw null;
            default:
                Object e2 = e(str);
                switch (!(e2 instanceof e) ? '#' : (char) 14) {
                    case 14:
                        Integer[] c2 = c((e) e2);
                        int i2 = f + 11;
                        j = i2 % 128;
                        switch (i2 % 2 == 0) {
                            case true:
                                return c2;
                            default:
                                throw null;
                        }
                    default:
                        Object[] objArr = new Object[1];
                        J("⮕⯟톫ᯱﮚ幍ϰ\ue9ae펃濇虆\u0018箤", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
                        throw a.b(str, e2, ((String) objArr[0]).intern());
                }
        }
    }

    public final Integer[] x(String str) throws d {
        int i = f + 67;
        j = i % 128;
        int i2 = i % 2;
        Integer[] H = H(str);
        int i3 = f + 67;
        j = i3 % 128;
        int i4 = i3 % 2;
        return H;
    }

    private Integer[] H(String str) throws d {
        Object d2 = d(str);
        switch (c(d2) ? '\n' : (char) 21) {
            case '\n':
                int i = f + 83;
                j = i % 128;
                switch (i % 2 != 0 ? '.' : Typography.quote) {
                    case '\"':
                        return null;
                    default:
                        throw null;
                }
            default:
                if (!(d2 instanceof e)) {
                    return null;
                }
                Integer[] c2 = c((e) d2);
                int i2 = j + 31;
                f = i2 % 128;
                int i3 = i2 % 2;
                return c2;
        }
    }

    public final b v(String str) throws d {
        int i = f + 25;
        j = i % 128;
        int i2 = i % 2;
        Object e2 = e(str);
        switch (e2 instanceof b) {
            case true:
                int i3 = j + 73;
                f = i3 % 128;
                int i4 = i3 % 2;
                return (b) e2;
            default:
                Object[] objArr = new Object[1];
                K(3 - TextUtils.getCapsMode("", 0, 0), "\ufff4\ufff8\uffef\u0019\b\n\u000f\u0007\ufff4\ufff3", Gravity.getAbsoluteGravity(0, 0) + 10, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + Opcodes.IF_ACMPEQ, true, objArr);
                throw a.b(str, e2, ((String) objArr[0]).intern());
        }
    }

    public final b u(String str) {
        int i = f + Opcodes.LMUL;
        j = i % 128;
        int i2 = i % 2;
        Object obj = null;
        b c2 = c(str, (b) null);
        int i3 = j + 41;
        f = i3 % 128;
        switch (i3 % 2 == 0 ? '[' : '\t') {
            case '\t':
                return c2;
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final b c(String str, b bVar) {
        int i = f + 41;
        j = i % 128;
        switch (i % 2 == 0) {
            case false:
                boolean z = d(str) instanceof b;
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                Object d2 = d(str);
                if (d2 instanceof b) {
                    int i2 = f + 97;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    return (b) d2;
                }
                int i4 = j + 85;
                f = i4 % 128;
                int i5 = i4 % 2;
                return bVar;
        }
    }

    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;)TE; */
    /* JADX WARN: Multi-variable type inference failed */
    public final Enum d(Class cls, String str) throws d {
        String r = r(str);
        Enum[] enumArr = (Enum[]) cls.getEnumConstants();
        int length = enumArr.length;
        int i = j + 9;
        f = i % 128;
        int i2 = i % 2;
        int i3 = 0;
        while (i3 < length) {
            int i4 = f + 77;
            j = i4 % 128;
            if (i4 % 2 != 0) {
                ((o.ei.b) enumArr[i3]).e().equals(r);
                Object obj = null;
                obj.hashCode();
                throw null;
            }
            ColorSpace.Named named = enumArr[i3];
            switch (((o.ei.b) named).e().equals(r) ? (char) 26 : '/') {
                case '/':
                    i3++;
                    int i5 = j + 19;
                    f = i5 % 128;
                    int i6 = i5 % 2;
                default:
                    return named;
            }
        }
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        K((ViewConfiguration.getDoubleTapTimeout() >> 16) + 5, "\u000f\f\u0007ￃ\u0003￬\u0011\u0019\u0004", TextUtils.indexOf("", "", 0) + 9, 168 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), false, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(str);
        Object[] objArr2 = new Object[1];
        J("攊敪ꈕ格眄塀䵂驤弳\ue360غ", -((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
        throw new d(append.append(((String) objArr2[0]).intern()).toString());
    }

    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;)TE; */
    public final Enum e(Class cls, String str) throws d {
        int i = j + Opcodes.DDIV;
        f = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? (char) 23 : 'b') {
            case Opcodes.FADD /* 98 */:
                Enum d2 = d(cls, str, null);
                int i2 = f + 5;
                j = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 21 : '\f') {
                    case '\f':
                        return d2;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                d(cls, str, null);
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;TE;)TE; */
    /* JADX WARN: Multi-variable type inference failed */
    public final Enum d(Class cls, String str, Enum r14) throws d {
        ColorSpace.Named named;
        int i = f + Opcodes.DDIV;
        j = i % 128;
        switch (i % 2 != 0 ? (char) 0 : (char) 31) {
            case 31:
                String c2 = c(str, (String) null);
                switch (c2 != null) {
                    case true:
                        Enum[] enumArr = (Enum[]) cls.getEnumConstants();
                        if (enumArr == 0) {
                            int i2 = j + 25;
                            f = i2 % 128;
                            int i3 = i2 % 2;
                            return r14;
                        }
                        int length = enumArr.length;
                        for (int i4 = 0; i4 < length; i4++) {
                            int i5 = f + 109;
                            j = i5 % 128;
                            if (i5 % 2 != 0) {
                                named = enumArr[i4];
                                int i6 = 21 / 0;
                                if (((o.ei.b) named).e().equals(c2)) {
                                    return named;
                                }
                            } else {
                                named = enumArr[i4];
                                switch (((o.ei.b) named).e().equals(c2) ? 'W' : '%') {
                                    case '%':
                                    default:
                                        return named;
                                }
                            }
                        }
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr = new Object[1];
                        K(ExpandableListView.getPackedPositionType(0L) + 5, "\u000f\f\u0007ￃ\u0003￬\u0011\u0019\u0004", TextUtils.indexOf("", "") + 9, 167 - (ViewConfiguration.getEdgeSlop() >> 16), false, objArr);
                        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(str);
                        Object[] objArr2 = new Object[1];
                        J("攊敪ꈕ格眄塀䵂驤弳\ue360غ", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
                        throw new d(append.append(((String) objArr2[0]).intern()).toString());
                    default:
                        return r14;
                }
            default:
                c(str, (String) null);
                throw null;
        }
    }

    public final Date e(String str, boolean z) throws d {
        return new Date(m(str).longValue() * (z ? 1 : 1000));
    }

    public final Date b(String str, boolean z) throws d {
        int i = j + Opcodes.LNEG;
        f = i % 128;
        int i2 = i % 2;
        Date a = a(str, z);
        int i3 = f + 13;
        j = i3 % 128;
        switch (i3 % 2 != 0 ? 'D' : (char) 16) {
            case 16:
                return a;
            default:
                int i4 = 4 / 0;
                return a;
        }
    }

    private Date a(String str, boolean z) throws d {
        Long n = n(str);
        if (c(n)) {
            return null;
        }
        return new Date(n.longValue() * (z ? 1 : 1000));
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x0022. Please report as an issue. */
    public final byte[] B(String str) throws d {
        int i = j + 35;
        f = i % 128;
        int i2 = i % 2;
        try {
            byte[] b2 = o.dk.b.b(r(str));
            int i3 = f + 87;
            j = i3 % 128;
            switch (i3 % 2 != 0) {
            }
            return b2;
        } catch (IllegalArgumentException e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            K((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 17, "\u000f\u000b\u0005\u0007\u0006\u0003\u001a\u0007\nￂ\u0006\u000b\u000e\u0003\u0018\u0010\u000bￂ\u0006\u000e\u0007\u000b\bￂ\u0014\u0011\bￂￂ\t\u0010\u000b\u0014\u0016\u0015ￂ\u000e\u0003", 37 - ImageFormat.getBitsPerPixel(0), Color.rgb(0, 0, 0) + 16777384, true, objArr);
            throw new d(sb.append(((String) objArr[0]).intern()).append(str).toString());
        }
    }

    public final byte[] z(String str) throws d {
        int i = j + Opcodes.DDIV;
        f = i % 128;
        boolean z = i % 2 != 0;
        byte[] M = M(str);
        switch (z) {
            case true:
                break;
            default:
                int i2 = 94 / 0;
                break;
        }
        int i3 = f + 99;
        j = i3 % 128;
        switch (i3 % 2 != 0 ? '*' : (char) 2) {
            case '*':
                throw null;
            default:
                return M;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0017, code lost:
    
        if (a(r13) != false) goto L36;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private byte[] M(java.lang.String r13) throws o.eg.d {
        /*
            r12 = this;
            int r0 = o.eg.b.j
            int r0 = r0 + 61
            int r1 = r0 % 128
            o.eg.b.f = r1
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            r3 = 0
            if (r0 != 0) goto L1c
            boolean r0 = r12.a(r13)
            r4 = 30
            int r4 = r4 / r3
            if (r0 == 0) goto L2a
            goto L28
        L1a:
            r13 = move-exception
            throw r13
        L1c:
            boolean r0 = r12.a(r13)
            if (r0 == 0) goto L24
            r0 = r2
            goto L25
        L24:
            r0 = r3
        L25:
            switch(r0) {
                case 0: goto L2a;
                default: goto L28;
            }
        L28:
            goto L9f
        L2a:
            java.lang.String r0 = r12.q(r13)
            if (r0 == 0) goto L32
            r4 = r2
            goto L33
        L32:
            r4 = r3
        L33:
            switch(r4) {
                case 1: goto L37;
                default: goto L36;
            }
        L36:
            goto L8e
        L37:
            int r4 = o.eg.b.f
            int r4 = r4 + 15
            int r5 = r4 % 128
            o.eg.b.j = r5
            int r4 = r4 % 2
            int r4 = r0.length()
            if (r4 != 0) goto L48
            goto L8e
        L48:
            byte[] r13 = o.dk.b.b(r0)     // Catch: java.lang.IllegalArgumentException -> L4d
            return r13
        L4d:
            r0 = move-exception
            o.eg.d r0 = new o.eg.d
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            java.lang.String r4 = ""
            int r5 = android.text.TextUtils.getTrimmedLength(r4)
            int r6 = r5 + 28
            java.lang.String r7 = "\u0007\uffc1\b\u000f\n\u0013\u0015\u0014\uffc1\r\u0002\u000e\n\u0004\u0006\u0005\u0002\u0019\u0006\t\uffc1\u0005\n\r\u0002\u0017\u000f\n\uffc1\u0005\r\u0006\n\u0007\uffc1\u0013\u0010"
            long r8 = android.os.SystemClock.uptimeMillis()
            r10 = 0
            int r5 = (r8 > r10 ? 1 : (r8 == r10 ? 0 : -1))
            int r8 = 38 - r5
            int r4 = android.os.Process.getGidForName(r4)
            int r9 = r4 + 170
            r10 = 1
            java.lang.Object[] r2 = new java.lang.Object[r2]
            r11 = r2
            K(r6, r7, r8, r9, r10, r11)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r13 = r1.append(r13)
            java.lang.String r13 = r13.toString()
            r0.<init>(r13)
            throw r0
        L8e:
            int r13 = o.eg.b.j
            int r13 = r13 + 87
            int r0 = r13 % 128
            o.eg.b.f = r0
            int r13 = r13 % 2
            if (r13 == 0) goto L9c
            return r1
        L9c:
            throw r1     // Catch: java.lang.Throwable -> L9d
        L9d:
            r13 = move-exception
            throw r13
        L9f:
            int r13 = o.eg.b.j
            int r13 = r13 + 61
            int r0 = r13 % 128
            o.eg.b.f = r0
            int r13 = r13 % 2
            if (r13 != 0) goto Lad
            r2 = r3
            goto Lae
        Lad:
        Lae:
            switch(r2) {
                case 0: goto Lb2;
                default: goto Lb1;
            }
        Lb1:
            return r1
        Lb2:
            r13 = 9
            int r13 = r13 / r3
            return r1
        Lb6:
            r13 = move-exception
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.M(java.lang.String):byte[]");
    }

    public final Byte D(String str) throws d {
        int i = j + Opcodes.DMUL;
        f = i % 128;
        int i2 = i % 2;
        try {
            Byte valueOf = Byte.valueOf(o.dk.b.c(r(str)));
            int i3 = j + 75;
            f = i3 % 128;
            int i4 = i3 % 2;
            return valueOf;
        } catch (IllegalArgumentException e2) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            K((ViewConfiguration.getKeyRepeatDelay() >> 16) + 28, "\u0007\uffc1\b\u000f\n\u0013\u0015\u0014\uffc1\r\u0002\u000e\n\u0004\u0006\u0005\u0002\u0019\u0006\t\uffc1\u0005\n\r\u0002\u0017\u000f\n\uffc1\u0005\r\u0006\n\u0007\uffc1\u0013\u0010", KeyEvent.getDeadChar(0, 0) + 37, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + Opcodes.RET, true, objArr);
            throw new d(sb.append(((String) objArr[0]).intern()).append(str).toString());
        }
    }

    public final Iterator<String> a() {
        int i = j + 77;
        f = i % 128;
        int i2 = i % 2;
        Iterator<String> it = this.a.keySet().iterator();
        int i3 = j + Opcodes.DDIV;
        f = i3 % 128;
        int i4 = i3 % 2;
        return it;
    }

    public final void c() {
        int i = j + Opcodes.LSUB;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                this.a.clear();
                throw null;
            default:
                this.a.clear();
                int i2 = f + 87;
                j = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    public static String b(Number number) throws d {
        if (number == null) {
            Object[] objArr = new Object[1];
            J("퍳팽衅䈹굌湲גּ끦蔥㤳뙙》荎堛\udd1d鹈ꭽ)㕭\ue6e2玌⣎\u0dbc컎᮲탨料", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
            throw new d(((String) objArr[0]).intern());
        }
        double doubleValue = number.doubleValue();
        a.b(doubleValue);
        switch (number.equals(e) ? (char) 2 : 'F') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                long longValue = number.longValue();
                switch (doubleValue == ((double) longValue)) {
                    case true:
                        String l = Long.toString(longValue);
                        int i = j + 71;
                        f = i % 128;
                        int i2 = i % 2;
                        return l;
                    default:
                        return number.toString();
                }
            default:
                Object[] objArr2 = new Object[1];
                J("鎿鎒蛗䳮㞴硍", 1 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr2);
                String intern = ((String) objArr2[0]).intern();
                int i3 = f + 57;
                j = i3 % 128;
                if (i3 % 2 == 0) {
                    return intern;
                }
                int i4 = 55 / 0;
                return intern;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:81:0x0123 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.lang.Object d(java.lang.Object r11) {
        /*
            Method dump skipped, instructions count: 342
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.d(java.lang.Object):java.lang.Object");
    }

    public final boolean C(String str) {
        int i = j + 71;
        f = i % 128;
        switch (i % 2 == 0 ? (char) 29 : '^') {
            case 29:
                b(str);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                switch (b(str) ? '*' : Typography.dollar) {
                    case '*':
                        if (!a(str)) {
                            int i2 = f + 99;
                            j = i2 % 128;
                            switch (i2 % 2 != 0) {
                                case true:
                                    return false;
                                default:
                                    return true;
                            }
                        }
                        break;
                }
                int i3 = j + 81;
                f = i3 % 128;
                int i4 = i3 % 2;
                return false;
        }
    }

    public final boolean a(String... strArr) {
        int i;
        int i2 = j + Opcodes.LSHR;
        f = i2 % 128;
        switch (i2 % 2 == 0 ? '\'' : '.') {
            case '.':
                int length = strArr.length;
                i = 0;
                break;
            default:
                int length2 = strArr.length;
                i = 1;
                break;
        }
        while (i < 4) {
            int i3 = f + Opcodes.LSHR;
            j = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 26 : (char) 14) {
                case 26:
                    int i4 = 30 / 0;
                    if (!C(strArr[i])) {
                        return false;
                    }
                    i++;
                default:
                    switch (!C(strArr[i])) {
                        case true:
                            return false;
                        default:
                            i++;
                    }
            }
        }
        return true;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;)[TE; */
    public final java.lang.Enum[] c(java.lang.Class r18, java.lang.String r19) throws o.eg.d {
        /*
            r17 = this;
            r0 = r18
            int r1 = o.eg.b.j
            int r1 = r1 + 73
            int r2 = r1 % 128
            o.eg.b.f = r2
            int r1 = r1 % 2
            r1 = r17
            r2 = r19
            o.eg.e r2 = r1.s(r2)
            int r3 = r2.d()
            java.lang.Object r4 = java.lang.reflect.Array.newInstance(r0, r3)
            java.lang.Enum[] r4 = (java.lang.Enum[]) r4
            r5 = 0
            r6 = r5
        L21:
            if (r6 >= r3) goto L26
            r7 = 37
            goto L28
        L26:
            r7 = 48
        L28:
            switch(r7) {
                case 37: goto L2c;
                default: goto L2b;
            }
        L2b:
            return r4
        L2c:
            int r7 = o.eg.b.f
            int r7 = r7 + 15
            int r8 = r7 % 128
            o.eg.b.j = r8
            int r7 = r7 % 2
            if (r7 == 0) goto L3b
            r7 = 56
            goto L3d
        L3b:
            r7 = 98
        L3d:
            switch(r7) {
                case 56: goto L4b;
                default: goto L40;
            }
        L40:
            java.lang.String r7 = r2.d(r6)
            java.lang.Enum r8 = o.ee.o.c(r0, r7)
            if (r8 == 0) goto L5e
            goto L59
        L4b:
            java.lang.String r2 = r2.d(r6)
            o.ee.o.c(r0, r2)
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L57
            throw r0     // Catch: java.lang.Throwable -> L57
        L57:
            r0 = move-exception
            throw r0
        L59:
            r4[r6] = r8
            int r6 = r6 + 1
            goto L21
        L5e:
            o.eg.d r2 = new o.eg.d
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            r3.<init>()
            int r4 = android.view.ViewConfiguration.getPressedStateDuration()
            int r4 = r4 >> 16
            r6 = 1
            int r4 = 1 - r4
            java.lang.Object[] r8 = new java.lang.Object[r6]
            java.lang.String r9 = "\ueef5\ueea0१쌀衆넬욽ㅈꀣᰵ椞\uef55뻒\ud971\uf81f䅗雫脆ီ㧲"
            J(r9, r4, r8)
            r4 = r8[r5]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r3 = r3.append(r4)
            java.lang.StringBuilder r3 = r3.append(r7)
            long r7 = android.os.SystemClock.elapsedRealtime()
            r9 = 0
            int r4 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            int r11 = r4 + 15
            java.lang.String r12 = "\u0018\uffefￊ\u0010\u0019ￊ\u001d\u000f\u001f\u0016\u000b ￊ\u001d\u000bￊￊ\u0017\u001f"
            java.lang.String r4 = ""
            int r4 = android.view.KeyEvent.keyCodeFromString(r4)
            int r13 = r4 + 19
            long r7 = android.os.SystemClock.elapsedRealtimeNanos()
            int r4 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            int r14 = 161 - r4
            r15 = 1
            java.lang.Object[] r4 = new java.lang.Object[r6]
            r16 = r4
            K(r11, r12, r13, r14, r15, r16)
            r4 = r4[r5]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r3 = r3.append(r4)
            java.lang.String r0 = r18.getName()
            java.lang.StringBuilder r0 = r3.append(r0)
            java.lang.String r0 = r0.toString()
            r2.<init>(r0)
            throw r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.c(java.lang.Class, java.lang.String):java.lang.Enum[]");
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0030, code lost:
    
        r12 = o.eg.b.j + 27;
        o.eg.b.f = r12 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x003a, code lost:
    
        if ((r12 % 2) != 0) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x003c, code lost:
    
        r12 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0041, code lost:
    
        switch(r12) {
            case 74: goto L23;
            default: goto L24;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0045, code lost:
    
        return r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0047, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x003f, code lost:
    
        r12 = 'J';
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x002b, code lost:
    
        if (r0 != null) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001d, code lost:
    
        if (r0 != null) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x004a, code lost:
    
        r2 = new java.lang.StringBuilder();
        r5 = new java.lang.Object[1];
        J("\ueef5\ueea0१쌀衆넬욽ㅈꀣᰵ椞\uef55뻒\ud971\uf81f䅗雫脆ီ㧲", (android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)), r5);
        r13 = r2.append(((java.lang.String) r5[0]).intern()).append(r13);
        r2 = new java.lang.Object[1];
        K(16 - (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "\u0018\uffefￊ\u0010\u0019ￊ\u001d\u000f\u001f\u0016\u000b ￊ\u001d\u000bￊￊ\u0017\u001f", 19 - (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), android.text.TextUtils.getTrimmedLength("") + com.esotericsoftware.asm.Opcodes.IF_ICMPNE, true, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x00af, code lost:
    
        throw new o.eg.d(r13.append(((java.lang.String) r2[0]).intern()).append(r12.getName()).toString());
     */
    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;)TE; */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.Enum b(java.lang.Class r12, java.lang.String r13) throws o.eg.d {
        /*
            r11 = this;
            int r0 = o.eg.b.f
            int r0 = r0 + 29
            int r1 = r0 % 128
            o.eg.b.j = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 15
            goto L11
        Lf:
            r0 = 97
        L11:
            r1 = 0
            switch(r0) {
                case 15: goto L20;
                default: goto L15;
            }
        L15:
            java.lang.String r13 = r11.r(r13)
            java.lang.Enum r0 = o.ee.o.c(r12, r13)
            if (r0 == 0) goto L4a
        L1f:
            goto L30
        L20:
            java.lang.String r13 = r11.r(r13)
            java.lang.Enum r0 = o.ee.o.c(r12, r13)
            r2 = 88
            int r2 = r2 / r1
            if (r0 == 0) goto L4a
            goto L1f
        L2e:
            r12 = move-exception
            throw r12
        L30:
            int r12 = o.eg.b.j
            int r12 = r12 + 27
            int r13 = r12 % 128
            o.eg.b.f = r13
            int r12 = r12 % 2
            if (r12 != 0) goto L3f
            r12 = 11
            goto L41
        L3f:
            r12 = 74
        L41:
            switch(r12) {
                case 74: goto L45;
                default: goto L44;
            }
        L44:
            goto L46
        L45:
            return r0
        L46:
            r12 = 0
            throw r12     // Catch: java.lang.Throwable -> L48
        L48:
            r12 = move-exception
            throw r12
        L4a:
            o.eg.d r0 = new o.eg.d
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>()
            long r3 = android.view.ViewConfiguration.getZoomControlsTimeout()
            r5 = 0
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            r4 = 1
            java.lang.Object[] r5 = new java.lang.Object[r4]
            java.lang.String r6 = "\ueef5\ueea0१쌀衆넬욽ㅈꀣᰵ椞\uef55뻒\ud971\uf81f䅗雫脆ီ㧲"
            J(r6, r3, r5)
            r3 = r5[r1]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.StringBuilder r2 = r2.append(r3)
            java.lang.StringBuilder r13 = r2.append(r13)
            double r2 = android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(r1)
            r5 = 0
            int r2 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            int r5 = 16 - r2
            java.lang.String r6 = "\u0018\uffefￊ\u0010\u0019ￊ\u001d\u000f\u001f\u0016\u000b ￊ\u001d\u000bￊￊ\u0017\u001f"
            int r2 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r2 = r2 >> 16
            int r7 = 19 - r2
            java.lang.String r2 = ""
            int r2 = android.text.TextUtils.getTrimmedLength(r2)
            int r8 = r2 + 160
            r9 = 1
            java.lang.Object[] r2 = new java.lang.Object[r4]
            r10 = r2
            K(r5, r6, r7, r8, r9, r10)
            r1 = r2[r1]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            java.lang.StringBuilder r13 = r13.append(r1)
            java.lang.String r12 = r12.getName()
            java.lang.StringBuilder r12 = r13.append(r12)
            java.lang.String r12 = r12.toString()
            r0.<init>(r12)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.b(java.lang.Class, java.lang.String):java.lang.Enum");
    }

    public final long A(String str) throws d {
        int i = f + 97;
        j = i % 128;
        int i2 = i % 2;
        long parseLong = Long.parseLong(r(str));
        int i3 = f + Opcodes.DSUB;
        j = i3 % 128;
        int i4 = i3 % 2;
        return parseLong;
    }

    final void a(c cVar) throws d {
        cVar.c();
        Iterator<Map.Entry<String, Object>> it = this.a.entrySet().iterator();
        while (true) {
            switch (!it.hasNext()) {
                case false:
                    int i = j + Opcodes.LSHL;
                    f = i % 128;
                    int i2 = i % 2;
                    Map.Entry<String, Object> next = it.next();
                    cVar.c(next.getKey()).c(next.getValue());
                    int i3 = f + 27;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                default:
                    cVar.e();
                    return;
            }
        }
    }

    public final String b() {
        try {
            c cVar = new c();
            a(cVar);
            String d2 = cVar.d();
            int i = j + 45;
            f = i % 128;
            int i2 = i % 2;
            return d2;
        } catch (d e2) {
            return "";
        }
    }

    public final String e() throws d {
        c cVar = new c(4);
        a(cVar);
        String d2 = cVar.d();
        int i = j + 63;
        f = i % 128;
        switch (i % 2 != 0 ? Typography.greater : (char) 4) {
            case 4:
                throw null;
            default:
                return d2;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void J(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.b.J(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void K(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] charArray;
        switch (str != null ? 'M' : '\t') {
            case 'M':
                int i4 = $11 + 25;
                $10 = i4 % 128;
                switch (i4 % 2 != 0 ? '[' : '9') {
                    case '9':
                        charArray = str.toCharArray();
                        break;
                    default:
                        str.toCharArray();
                        throw null;
                }
            default:
                charArray = str;
                break;
        }
        char[] cArr = charArray;
        h hVar = new h();
        char[] cArr2 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            int i5 = $11 + 87;
            $10 = i5 % 128;
            int i6 = i5 % 2;
            hVar.b = cArr[hVar.a];
            cArr2[hVar.a] = (char) (i3 + hVar.b);
            int i7 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr2[i7]), Integer.valueOf(d)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 12, (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), 459 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                    byte b2 = (byte) 0;
                    byte b3 = (byte) (b2 - 1);
                    Object[] objArr3 = new Object[1];
                    L(b2, b3, (byte) (b3 & 39), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr2[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(11 - Color.alpha(0), (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 313 - Color.blue(0));
                        byte b4 = (byte) 0;
                        Object[] objArr5 = new Object[1];
                        L(b4, (byte) (b4 - 1), $$a[1], objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            hVar.c = i;
            char[] cArr3 = new char[i2];
            System.arraycopy(cArr2, 0, cArr3, 0, i2);
            System.arraycopy(cArr3, 0, cArr2, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr3, hVar.c, cArr2, 0, i2 - hVar.c);
        }
        switch (z ? (char) 7 : '\t') {
            case '\t':
                break;
            default:
                char[] cArr4 = new char[i2];
                hVar.a = 0;
                while (hVar.a < i2) {
                    int i8 = $10 + 31;
                    $11 = i8 % 128;
                    int i9 = i8 % 2;
                    cArr4[hVar.a] = cArr2[(i2 - hVar.a) - 1];
                    try {
                        Object[] objArr6 = {hVar, hVar};
                        Object obj3 = o.e.a.s.get(-1412673904);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(KeyEvent.getDeadChar(0, 0) + 11, (char) View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getWindowTouchSlop() >> 8) + 313);
                            byte b5 = (byte) 0;
                            Object[] objArr7 = new Object[1];
                            L(b5, (byte) (b5 - 1), $$a[1], objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                int i10 = $10 + 3;
                $11 = i10 % 128;
                int i11 = i10 % 2;
                cArr2 = cArr4;
                break;
        }
        objArr[0] = new String(cArr2);
    }
}
