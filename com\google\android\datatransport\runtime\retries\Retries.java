package com.google.android.datatransport.runtime.retries;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\retries\Retries.smali */
public final class Retries {
    private Retries() {
    }

    public static <TInput, TResult, TException extends Throwable> TResult retry(int i, TInput tinput, Function<TInput, TResult, TException> function, RetryStrategy<TInput, TResult> retryStrategy) throws Throwable {
        TResult apply;
        if (i >= 1) {
            do {
                apply = function.apply(tinput);
                tinput = retryStrategy.shouldRetry(tinput, apply);
                if (tinput == null) {
                    break;
                }
                i--;
            } while (i >= 1);
            return apply;
        }
        return function.apply(tinput);
    }
}
