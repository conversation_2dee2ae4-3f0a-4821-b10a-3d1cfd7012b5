package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.content.Context;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.securestorage.model.IVEncryptedData;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.nio.charset.Charset;
import java.security.KeyStore;
import java.util.HashMap;
import kotlin.Pair;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;
import kotlin.text.StringsKt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\f.smali */
public abstract class f {
    public static l a(String cipheredDataWithVersion, byte[] storageEncryptionKey, byte[] dataEncryptionKey) {
        String a;
        UtilitiesSDKCryptoResponse a2;
        Intrinsics.checkNotNullParameter(cipheredDataWithVersion, "cipheredDataWithVersion");
        Intrinsics.checkNotNullParameter(storageEncryptionKey, "storageEncryptionKey");
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        String substring = cipheredDataWithVersion.substring(0, 4);
        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        if (Intrinsics.areEqual(substring, x.a) || Intrinsics.areEqual(substring, x.b)) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNSUPPORTED_VERSION, null, 2, null);
        }
        String substring2 = cipheredDataWithVersion.substring(0, cipheredDataWithVersion.length() - 64);
        Intrinsics.checkNotNullExpressionValue(substring2, "this as java.lang.String…ing(startIndex, endIndex)");
        byte[] dataToHashHex = y.a(substring2);
        if (Intrinsics.areEqual(substring, x.c) || Intrinsics.areEqual(substring, x.d)) {
            byte[] hmacKey = new byte[64];
            byte[] bArr = e.a;
            System.arraycopy(bArr, 0, hmacKey, 0, bArr.length);
            byte[] bArr2 = e.b;
            System.arraycopy(bArr2, 0, hmacKey, 16, bArr2.length);
            byte[] bArr3 = e.c;
            System.arraycopy(bArr3, 0, hmacKey, 32, bArr3.length);
            byte[] bArr4 = e.d;
            System.arraycopy(bArr4, 0, hmacKey, 48, bArr4.length);
            try {
                Intrinsics.checkNotNullParameter(hmacKey, "hmacKey");
                UtilitiesSDKCryptoResponse hmac = UtilitiesSDK.hmac((byte) 3, dataToHashHex, hmacKey);
                if (hmac.getReturnCode() != 0) {
                    v vVar = SecureStorageSDKException.Companion;
                    int returnCode = hmac.getReturnCode();
                    vVar.getClass();
                    v.a(returnCode);
                    throw null;
                }
                a = y.a(hmac.getOutputData());
            } finally {
                y.b(hmacKey);
            }
        } else {
            Intrinsics.checkNotNullExpressionValue(dataToHashHex, "dataToHashHex");
            a = e.a(dataToHashHex, storageEncryptionKey);
        }
        String substring3 = cipheredDataWithVersion.substring(cipheredDataWithVersion.length() - 64, cipheredDataWithVersion.length());
        Intrinsics.checkNotNullExpressionValue(substring3, "this as java.lang.String…ing(startIndex, endIndex)");
        if (!Intrinsics.areEqual(a, substring3)) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_CORRUPTED_HMAC_INCONSISTENT, null, 2, null);
        }
        if (Intrinsics.areEqual(substring, x.e)) {
            String substring4 = cipheredDataWithVersion.substring(5, 37);
            Intrinsics.checkNotNullExpressionValue(substring4, "this as java.lang.String…ing(startIndex, endIndex)");
            String substring5 = cipheredDataWithVersion.substring(37, cipheredDataWithVersion.length() - 64);
            Intrinsics.checkNotNullExpressionValue(substring5, "this as java.lang.String…ing(startIndex, endIndex)");
            a2 = UtilitiesSDK.decrypt((byte) 3, (byte) 3, storageEncryptionKey, y.a(substring4), y.a(substring5));
        } else {
            a2 = a(cipheredDataWithVersion, storageEncryptionKey);
        }
        if (a2.getReturnCode() == 0) {
            return a(a2.getOutputData(), dataEncryptionKey);
        }
        v vVar2 = SecureStorageSDKException.Companion;
        int returnCode2 = a2.getReturnCode();
        vVar2.getClass();
        v.a(returnCode2);
        throw null;
    }

    public static final IVEncryptedData b(byte[] data, byte[] key) {
        Intrinsics.checkNotNullParameter(data, "data");
        Intrinsics.checkNotNullParameter(key, "dataEncryptionKey");
        byte[] bArr = e.a;
        UtilitiesSDKCryptoResponse generateRandomByteArray = UtilitiesSDK.generateRandomByteArray(16);
        if (generateRandomByteArray.getReturnCode() != 0) {
            v vVar = SecureStorageSDKException.Companion;
            int returnCode = generateRandomByteArray.getReturnCode();
            vVar.getClass();
            v.a(returnCode);
            throw null;
        }
        byte[] vector = generateRandomByteArray.getOutputData();
        Intrinsics.checkNotNullExpressionValue(vector, "res.outputData");
        Intrinsics.checkNotNullParameter(key, "key");
        Intrinsics.checkNotNullParameter(data, "data");
        Intrinsics.checkNotNullParameter(vector, "vector");
        UtilitiesSDKCryptoResponse encrypt = UtilitiesSDK.encrypt((byte) 3, (byte) 4, key, vector, data);
        Intrinsics.checkNotNull(encrypt);
        if (encrypt.getReturnCode() == 0) {
            byte[] outputData = encrypt.getOutputData();
            Intrinsics.checkNotNullExpressionValue(outputData, "cipheredDataResponse.outputData");
            return new IVEncryptedData(vector, outputData);
        }
        v vVar2 = SecureStorageSDKException.Companion;
        int returnCode2 = encrypt.getReturnCode();
        vVar2.getClass();
        v.a(returnCode2);
        throw null;
    }

    public static UtilitiesSDKCryptoResponse a(String str, byte[] bArr) {
        String substring = str.substring(4, 36);
        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        String substring2 = str.substring(36, str.length() - 64);
        Intrinsics.checkNotNullExpressionValue(substring2, "this as java.lang.String…ing(startIndex, endIndex)");
        return UtilitiesSDK.decrypt((byte) 3, (byte) 3, bArr, y.a(substring), y.a(substring2));
    }

    public static final byte[] a(IVEncryptedData iVEncryptedData, byte[] dataEncryptionKey) {
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        String str = a.a;
        if (iVEncryptedData != null) {
            UtilitiesSDKCryptoResponse decrypt = UtilitiesSDK.decrypt((byte) 3, (byte) 4, dataEncryptionKey, iVEncryptedData.getIv(), iVEncryptedData.getCom.google.firebase.messaging.Constants.ScionAnalytics.MessageType.DATA_MESSAGE java.lang.String());
            if (decrypt.getReturnCode() == 0) {
                if (decrypt.getOutputData() == null) {
                    return new byte[0];
                }
                byte[] outputData = decrypt.getOutputData();
                Intrinsics.checkNotNullExpressionValue(outputData, "cipheredDataResponse.outputData");
                return outputData;
            }
            v vVar = SecureStorageSDKException.Companion;
            int returnCode = decrypt.getReturnCode();
            vVar.getClass();
            v.a(returnCode);
            throw null;
        }
        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.VALUE_NULL, null, 2, null);
    }

    public static l a(byte[] bArr, byte[] bArr2) {
        try {
            try {
                HashMap hashMap = new HashMap();
                HashMap hashMap2 = new HashMap();
                String hexData = y.a(bArr);
                Intrinsics.checkNotNullExpressionValue(hexData, "hexData");
                int i = 1;
                byte[] bytes = x.f.getBytes(Charsets.UTF_8);
                Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
                String a = y.a(bytes);
                Intrinsics.checkNotNullExpressionValue(a, "bytesToHexa(SEPARATOR_KE…ALUE_PAIRS.toByteArray())");
                String[] strArr = (String[]) StringsKt.split$default((CharSequence) hexData, new String[]{a}, false, 0, 6, (Object) null).toArray(new String[0]);
                if (strArr.length == 1 && strArr[0].length() == 0) {
                    return null;
                }
                int length = strArr.length;
                int i2 = 0;
                while (i2 < length) {
                    String str = strArr[i2];
                    String[] strArr2 = new String[i];
                    String str2 = x.g;
                    Charset charset = Charsets.UTF_8;
                    byte[] bytes2 = str2.getBytes(charset);
                    Intrinsics.checkNotNullExpressionValue(bytes2, "this as java.lang.String).getBytes(charset)");
                    String a2 = y.a(bytes2);
                    Intrinsics.checkNotNullExpressionValue(a2, "bytesToHexa(SEPARATOR_KEY_VALUE.toByteArray())");
                    strArr2[0] = a2;
                    String[] strArr3 = (String[]) StringsKt.split$default((CharSequence) str, strArr2, false, 0, 6, (Object) null).toArray(new String[0]);
                    if (strArr3.length == 3) {
                        byte[] key = y.a(strArr3[0]);
                        byte[] value = y.a(strArr3[i]);
                        byte[] a3 = y.a(strArr3[2]);
                        Intrinsics.checkNotNullExpressionValue(a3, "hexaToBytes(tmp[2])");
                        if (Intrinsics.areEqual("0", new String(a3, charset))) {
                            Intrinsics.checkNotNullExpressionValue(key, "key");
                            String str3 = new String(key, charset);
                            Intrinsics.checkNotNullExpressionValue(value, "value");
                            hashMap.put(str3, b(value, bArr2));
                        } else {
                            Intrinsics.checkNotNullExpressionValue(value, "value");
                            byte[] valueBytes = y.a(new String(value, charset));
                            Intrinsics.checkNotNullExpressionValue(key, "key");
                            String str4 = new String(key, charset);
                            Intrinsics.checkNotNullExpressionValue(valueBytes, "valueBytes");
                            hashMap2.put(str4, b(valueBytes, bArr2));
                        }
                        y.b(key);
                        y.b(value);
                        i2++;
                        i = 1;
                    } else {
                        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNREADABLE_STORAGE, null, 2, null);
                    }
                }
                return new l(hashMap, hashMap2);
            } catch (Exception e) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNREADABLE_STORAGE, null, 2, null);
            }
        } finally {
            y.b(bArr);
        }
    }

    public static final String a(String filename) {
        Intrinsics.checkNotNullParameter(filename, "filename");
        byte[] bytes = (filename + "329D2D6C").getBytes(Charsets.UTF_8);
        Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
        UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, bytes);
        Intrinsics.checkNotNullExpressionValue(hash, "hash(\n            Utilit…).toByteArray()\n        )");
        if (hash.getReturnCode() == 0) {
            String a = y.a(hash.getOutputData());
            Intrinsics.checkNotNullExpressionValue(a, "bytesToHexa(res.outputData)");
            return a;
        }
        v vVar = SecureStorageSDKException.Companion;
        int returnCode = hash.getReturnCode();
        vVar.getClass();
        v.a(returnCode);
        throw null;
    }

    public static final Pair a(String str, int i, String filename, Context context, byte[] dataEncryptionKey) {
        boolean z;
        String a;
        UtilitiesSDKCryptoResponse a2;
        l lVar;
        Intrinsics.checkNotNullParameter(filename, "filename");
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(dataEncryptionKey, "dataEncryptionKey");
        String c = g.c(filename, context);
        String substring = c.substring(0, 4);
        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
        if (!Intrinsics.areEqual(substring, x.a) && !Intrinsics.areEqual(substring, x.b)) {
            String substring2 = c.substring(0, c.length() - 64);
            Intrinsics.checkNotNullExpressionValue(substring2, "this as java.lang.String…ing(startIndex, endIndex)");
            byte[] dataToHashHex = y.a(substring2);
            String str2 = x.c;
            boolean areEqual = Intrinsics.areEqual(substring, str2);
            String newKeyName = a(filename);
            String packageName = context.getPackageName();
            Intrinsics.checkNotNullExpressionValue(packageName, "context.packageName");
            String str3 = j.a;
            Intrinsics.checkNotNullParameter(newKeyName, "newKeyName");
            Intrinsics.checkNotNullParameter(packageName, "packageName");
            Intrinsics.checkNotNullParameter(newKeyName, "newKeyName");
            Intrinsics.checkNotNullParameter(packageName, "packageName");
            if (areEqual) {
                newKeyName = packageName;
            }
            try {
                KeyStore keyStore = KeyStore.getInstance(j.a);
                keyStore.load(null);
                z = keyStore.containsAlias(newKeyName);
            } catch (Exception e) {
                z = false;
            }
            byte[] a3 = e.a();
            byte[] bArr = new byte[32];
            String a4 = a(filename);
            String packageName2 = context.getPackageName();
            Intrinsics.checkNotNullExpressionValue(packageName2, "context.packageName");
            boolean a5 = e.a(str, i, a3, z, bArr, a4, packageName2);
            if (!Intrinsics.areEqual(substring, str2) && !Intrinsics.areEqual(substring, x.d)) {
                Intrinsics.checkNotNullExpressionValue(dataToHashHex, "dataToHashHex");
                a = e.a(dataToHashHex, bArr);
            } else {
                byte[] hmacKey = new byte[64];
                byte[] bArr2 = e.a;
                System.arraycopy(bArr2, 0, hmacKey, 0, bArr2.length);
                byte[] bArr3 = e.b;
                System.arraycopy(bArr3, 0, hmacKey, 16, bArr3.length);
                byte[] bArr4 = e.c;
                System.arraycopy(bArr4, 0, hmacKey, 32, bArr4.length);
                byte[] bArr5 = e.d;
                System.arraycopy(bArr5, 0, hmacKey, 48, bArr5.length);
                try {
                    Intrinsics.checkNotNullParameter(hmacKey, "hmacKey");
                    UtilitiesSDKCryptoResponse hmac = UtilitiesSDK.hmac((byte) 3, dataToHashHex, hmacKey);
                    if (hmac.getReturnCode() == 0) {
                        a = y.a(hmac.getOutputData());
                    } else {
                        v vVar = SecureStorageSDKException.Companion;
                        int returnCode = hmac.getReturnCode();
                        vVar.getClass();
                        v.a(returnCode);
                        throw null;
                    }
                } finally {
                    y.b(hmacKey);
                }
            }
            y.b(a3);
            String substring3 = c.substring(c.length() - 64, c.length());
            Intrinsics.checkNotNullExpressionValue(substring3, "this as java.lang.String…ing(startIndex, endIndex)");
            if (Intrinsics.areEqual(a, substring3)) {
                if (Intrinsics.areEqual(substring, x.e)) {
                    String substring4 = c.substring(5, 37);
                    Intrinsics.checkNotNullExpressionValue(substring4, "this as java.lang.String…ing(startIndex, endIndex)");
                    String substring5 = c.substring(37, c.length() - 64);
                    Intrinsics.checkNotNullExpressionValue(substring5, "this as java.lang.String…ing(startIndex, endIndex)");
                    a2 = UtilitiesSDK.decrypt((byte) 3, (byte) 3, bArr, y.a(substring4), y.a(substring5));
                } else {
                    a2 = a(c, bArr);
                }
                if (a2.getReturnCode() == 0) {
                    try {
                        l a6 = a(a2.getOutputData(), dataEncryptionKey);
                        lVar = a6 != null ? a6 : null;
                    } catch (SecureStorageSDKException e2) {
                        if (e2.getErrorCode() == -4305 && Intrinsics.areEqual(substring, x.c)) {
                            byte[] a7 = e.a();
                            byte[] bArr6 = new byte[32];
                            String a8 = a(filename);
                            String packageName3 = context.getPackageName();
                            Intrinsics.checkNotNullExpressionValue(packageName3, "context.packageName");
                            a5 = e.a(str, i, a7, false, bArr6, a8, packageName3);
                            y.b(a7);
                            UtilitiesSDKCryptoResponse a9 = a(c, bArr6);
                            if (a9.getReturnCode() == 0) {
                                l a10 = a(a9.getOutputData(), dataEncryptionKey);
                                lVar = a10 != null ? a10 : null;
                            } else {
                                v vVar2 = SecureStorageSDKException.Companion;
                                int returnCode2 = a9.getReturnCode();
                                vVar2.getClass();
                                v.a(returnCode2);
                                throw null;
                            }
                        } else {
                            throw e2;
                        }
                    }
                    return new Pair(Boolean.valueOf(a5), lVar);
                }
                v vVar3 = SecureStorageSDKException.Companion;
                int returnCode3 = a2.getReturnCode();
                vVar3.getClass();
                v.a(returnCode3);
                throw null;
            }
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.STORAGE_CORRUPTED_HMAC_INCONSISTENT, null, 2, null);
        }
        throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNSUPPORTED_VERSION, null, 2, null);
    }
}
