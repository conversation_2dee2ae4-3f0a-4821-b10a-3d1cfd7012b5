package androidx.work.impl.utils;

import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

/* compiled from: NetworkApi21.kt */
@Metadata(d1 = {"\u0000*\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0018\u0010\u0000\u001a\u0004\u0018\u00010\u0001*\u00020\u00022\b\u0010\u0003\u001a\u0004\u0018\u00010\u0004H\u0001\u001a\u0014\u0010\u0005\u001a\u00020\u0006*\u00020\u00012\u0006\u0010\u0007\u001a\u00020\bH\u0001\u001a\u0014\u0010\t\u001a\u00020\n*\u00020\u00022\u0006\u0010\u000b\u001a\u00020\fH\u0001¨\u0006\r"}, d2 = {"getNetworkCapabilitiesCompat", "Landroid/net/NetworkCapabilities;", "Landroid/net/ConnectivityManager;", "network", "Landroid/net/Network;", "hasCapabilityCompat", "", "capability", "", "unregisterNetworkCallbackCompat", "", "networkCallback", "Landroid/net/ConnectivityManager$NetworkCallback;", "work-runtime_release"}, k = 2, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\utils\NetworkApi21.smali */
public final class NetworkApi21 {
    public static final void unregisterNetworkCallbackCompat(ConnectivityManager $this$unregisterNetworkCallbackCompat, ConnectivityManager.NetworkCallback networkCallback) {
        Intrinsics.checkNotNullParameter($this$unregisterNetworkCallbackCompat, "<this>");
        Intrinsics.checkNotNullParameter(networkCallback, "networkCallback");
        $this$unregisterNetworkCallbackCompat.unregisterNetworkCallback(networkCallback);
    }

    public static final NetworkCapabilities getNetworkCapabilitiesCompat(ConnectivityManager $this$getNetworkCapabilitiesCompat, Network network) {
        Intrinsics.checkNotNullParameter($this$getNetworkCapabilitiesCompat, "<this>");
        return $this$getNetworkCapabilitiesCompat.getNetworkCapabilities(network);
    }

    public static final boolean hasCapabilityCompat(NetworkCapabilities $this$hasCapabilityCompat, int capability) {
        Intrinsics.checkNotNullParameter($this$hasCapabilityCompat, "<this>");
        return $this$hasCapabilityCompat.hasCapability(capability);
    }
}
