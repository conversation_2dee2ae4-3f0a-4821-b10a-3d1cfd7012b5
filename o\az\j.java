package o.az;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;
import o.cf.i;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\j.smali */
final class j extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long d;
    private static int g;
    private static int h;
    private static char i;
    private static int j;
    private final boolean b;
    private final c e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\j$c.smali */
    interface c {
        void m();

        void o();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        j = 1;
        d();
        Color.alpha(0);
        AudioTrack.getMaxVolume();
        AudioTrack.getMaxVolume();
        SystemClock.elapsedRealtime();
        int i2 = g + 19;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                int i3 = 95 / 0;
                break;
        }
    }

    static void d() {
        i = (char) 17957;
        h = 161105445;
        d = 8256734792947567301L;
    }

    static void init$0() {
        $$a = new byte[]{27, 43, 25, -109};
        $$b = 109;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.az.j.$$a
            int r7 = r7 + 99
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r9 = r9 * 4
            int r9 = 4 - r9
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L32
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L32:
            int r7 = -r7
            int r10 = r10 + 1
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.j.x(int, short, int, java.lang.Object[]):void");
    }

    j(Context context, boolean z, c cVar) {
        super(context, 2);
        this.e = cVar;
        this.b = z;
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x002a, code lost:
    
        r0 = false;
     */
    @Override // o.cf.i
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void b() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        /*
            r2 = this;
            int r0 = o.az.j.g
            int r0 = r0 + 47
            int r1 = r0 % 128
            o.az.j.j = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L3e
            r2.t()
            boolean r0 = r2.b
            if (r0 != 0) goto L15
            r0 = 1
            goto L2b
        L15:
            int r0 = o.az.j.g
            int r0 = r0 + 87
            int r1 = r0 % 128
            o.az.j.j = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L24
            r0 = 79
            goto L26
        L24:
            r0 = 46
        L26:
            switch(r0) {
                case 46: goto L29;
                default: goto L2a;
            }
        L29:
        L2a:
            r0 = 0
        L2b:
            r2.c(r0)
            r2.o()
            r2.m()
            r2.l()
            r2.n()
            r2.k()
            return
        L3e:
            r2.t()
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L43
        L43:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.j.b():void");
    }

    @Override // o.cf.i
    public final boolean a() {
        switch (this.b ? '4' : 'W') {
            case Opcodes.POP /* 87 */:
                g.c();
                Object[] objArr = new Object[1];
                v(ViewConfiguration.getWindowTouchSlop() >> 8, "ꋡ锔\uf525㸋뾛䏄蓼鄺覐\uf5c8旹訊휣꽮륆뗻齦Ѝﭤ㍼몯魟ꆑ踻䤳\ueb83", (char) (View.MeasureSpec.makeMeasureSpec(0, 0) + 54923), "廞蠥诘姖", "쳠宥䫊⦋", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                v((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 250476920, "럭ᥝ⑬娓矎謁ب熝Ụ\uedf0䔑狀⠁芀\uf679\ud81a㖬䂱枇꜋껀ꭤ뚚麖퀱\udac0飰捡鬫ੲ峟暑ᅍ軮쾭꺴㎞芖浱握咐ﺄ\uf6ceᆯ\ue71f䪍ᵤ挅첩㍝\u1cc9깚Ⳑ\uf6beꈝ䨛뷸䲳\udaaf४땆⌆㒅", (char) TextUtils.getTrimmedLength(""), "硫\uedf9⬎뎦", "쳠宥䫊⦋", objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                v(ViewConfiguration.getMaximumDrawingCacheSize() >> 24, "ꋡ锔\uf525㸋뾛䏄蓼鄺覐\uf5c8旹訊휣꽮륆뗻齦Ѝﭤ㍼몯魟ꆑ踻䤳\ueb83", (char) (54923 - Color.green(0)), "廞蠥诘姖", "쳠宥䫊⦋", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                v(Color.alpha(0), "䤑淔콂䚂ﬂ䫚펓裈ײ䆼䉣傴ᨾຢ\ud941跎\u0cd2阮蹞ᑣጟ㿮\ueb68鬆需\uf417䛒⻅\uf7a9ꄳ⒔諍拑\uf0ed卥践뷸Ɪ㐼Ⲑꗬ汭\ua83d媊\u12d7皴Ǌ璫ୣ甍స\uf093뀇泄\uf064獣厎㣳\u0d49錑픂龃뾐㵪\uefd6胭ⴏꯧ", (char) (64213 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), "渟삯핶웺", "쳠宥䫊⦋", objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                j();
                break;
        }
        boolean a = super.a();
        if (o.bn.e.b().a() != null) {
            int i2 = j + 5;
            g = i2 % 128;
            if (i2 % 2 != 0) {
                o.bn.e.b().a().b(this.c);
                throw null;
            }
            o.bn.e.b().a().b(this.c);
        }
        switch (a) {
            case false:
                int i3 = g + Opcodes.LNEG;
                j = i3 % 128;
                int i4 = i3 % 2;
                return false;
            default:
                String f = f();
                Object[] objArr5 = new Object[1];
                v(1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\u09c5텞", (char) (58387 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), "바\uf2a8ᎀዤ", "쳠宥䫊⦋", objArr5);
                switch (f.equals(((String) objArr5[0]).intern()) ? '?' : '_') {
                    case Opcodes.SWAP /* 95 */:
                        g.c();
                        Object[] objArr6 = new Object[1];
                        v(Process.myTid() >> 22, "ꋡ锔\uf525㸋뾛䏄蓼鄺覐\uf5c8旹訊휣꽮륆뗻齦Ѝﭤ㍼몯魟ꆑ踻䤳\ueb83", (char) (54923 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), "廞蠥诘姖", "쳠宥䫊⦋", objArr6);
                        String intern3 = ((String) objArr6[0]).intern();
                        Object[] objArr7 = new Object[1];
                        v(View.resolveSize(0, 0), "侲끑\uf68e뽥ɘ욒ᣞ틬૩꿙湦샗霻괬鹶塀\uec79涬☫蔩\udf5d\ufde9\ude69\ued4d吮Ⱑ巹죃\uec5a笙큂㴍仹㋞훖\u1fdc肗攠澭铨啚\udadd", (char) (View.combineMeasuredStates(0, 0) + 18920), "Ⴕ꣤\ue874䩉", "쳠宥䫊⦋", objArr7);
                        g.d(intern3, ((String) objArr7[0]).intern());
                        this.e.o();
                        return true;
                    default:
                        g.c();
                        Object[] objArr8 = new Object[1];
                        v(ViewConfiguration.getPressedStateDuration() >> 16, "ꋡ锔\uf525㸋뾛䏄蓼鄺覐\uf5c8旹訊휣꽮륆뗻齦Ѝﭤ㍼몯魟ꆑ踻䤳\ueb83", (char) (54923 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), "廞蠥诘姖", "쳠宥䫊⦋", objArr8);
                        String intern4 = ((String) objArr8[0]).intern();
                        Object[] objArr9 = new Object[1];
                        v(Process.getGidForName("") - 1811786315, "甕赎䣿儚㛧赍\ue57cᩃ\udd78ጫ\udb20홄\ue47e㽚莱勷ꨁ詁ᣕᶵ汽擲拏螃ᣕ\ue4c6鶂诐锜\uf2d1葶體\uc5572뚠鿗童♳♺䠷癌\u200aõ龨켯鬓酼", (char) Drawable.resolveOpacity(0, 0), "뒀ɕ暔檟", "쳠宥䫊⦋", objArr9);
                        g.d(intern4, ((String) objArr9[0]).intern());
                        this.a = true;
                        this.e.m();
                        return false;
                }
        }
    }

    @Override // o.cf.i
    public final String c() {
        Object obj;
        int i2 = g + 53;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                v((Process.myTid() >>> Opcodes.ISHL) + 915897486, "␘る午蒴孅唑㊸쳓◥횰ٴꮘ⪺褪\ud87e驟懦㼥ꬩ", (char) (2864 % (SystemClock.elapsedRealtimeNanos() > 1L ? 1 : (SystemClock.elapsedRealtimeNanos() == 1L ? 0 : -1))), "蹛靼젶踢", "쳠宥䫊⦋", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                v((Process.myTid() >> 22) + 915897486, "␘る午蒴孅唑㊸쳓◥횰ٴꮘ⪺褪\ud87e驟懦㼥ꬩ", (char) (8905 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), "蹛靼젶踢", "쳠宥䫊⦋", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    private static void v(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        int i3;
        char[] charArray = str3 != null ? str3.toCharArray() : str3;
        Object obj = null;
        int i4 = 2;
        switch (str2 != null ? (char) 1 : ' ') {
            case ' ':
                cArr = str2;
                break;
            default:
                int i5 = $10 + 15;
                $11 = i5 % 128;
                if (i5 % 2 == 0) {
                    str2.toCharArray();
                    throw null;
                }
                cArr = str2.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        int i6 = 0;
        switch (str == null) {
            case true:
                cArr2 = str;
                break;
            default:
                int i7 = $10 + 27;
                $11 = i7 % 128;
                switch (i7 % 2 == 0 ? '9' : '^') {
                    case Opcodes.DUP2_X2 /* 94 */:
                        cArr2 = str.toCharArray();
                        break;
                    default:
                        str.toCharArray();
                        Object obj2 = null;
                        obj2.hashCode();
                        throw null;
                }
        }
        o oVar = new o();
        int length = cArr3.length;
        char[] cArr4 = new char[length];
        int length2 = charArray.length;
        char[] cArr5 = new char[length2];
        System.arraycopy(cArr3, 0, cArr4, 0, length);
        System.arraycopy(charArray, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c2);
        cArr5[2] = (char) (cArr5[2] + ((char) i2));
        int length3 = cArr2.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            int i8 = $10 + Opcodes.LREM;
            $11 = i8 % 128;
            int i9 = i8 % i4;
            try {
                Object[] objArr2 = {oVar};
                Object obj3 = o.e.a.s.get(-429442487);
                if (obj3 == null) {
                    Class cls = (Class) o.e.a.c(Color.green(i6) + 10, (char) (20954 - View.resolveSizeAndState(i6, i6, i6)), 344 - KeyEvent.getDeadChar(i6, i6));
                    byte b = (byte) i6;
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    x(b, b2, b2, objArr3);
                    String str4 = (String) objArr3[i6];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i6] = Object.class;
                    obj3 = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj3);
                }
                int intValue = ((Integer) ((Method) obj3).invoke(obj, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj4 = o.e.a.s.get(-515165572);
                    if (obj4 == null) {
                        Class cls2 = (Class) o.e.a.c(TextUtils.indexOf("", "", i6) + 10, (char) ((-1) - TextUtils.lastIndexOf("", '0', i6)), 208 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)));
                        byte b3 = (byte) 2;
                        byte b4 = (byte) (b3 - 2);
                        Object[] objArr5 = new Object[1];
                        x(b3, b4, b4, objArr5);
                        String str5 = (String) objArr5[i6];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i6] = Object.class;
                        obj4 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj4);
                    }
                    int intValue2 = ((Integer) ((Method) obj4).invoke(null, objArr4)).intValue();
                    int i10 = cArr4[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr5[intValue]);
                        objArr6[1] = Integer.valueOf(i10);
                        objArr6[i6] = oVar;
                        Object obj5 = o.e.a.s.get(-1614232674);
                        if (obj5 == null) {
                            Class cls3 = (Class) o.e.a.c(10 - (ExpandableListView.getPackedPositionForChild(i6, i6) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(i6, i6) == 0L ? 0 : -1)), (char) (ViewConfiguration.getTapTimeout() >> 16), 280 - ExpandableListView.getPackedPositionChild(0L));
                            byte length4 = (byte) $$a.length;
                            byte b5 = (byte) (length4 - 4);
                            Object[] objArr7 = new Object[1];
                            x(length4, b5, b5, objArr7);
                            obj5 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj6 = o.e.a.s.get(406147795);
                            if (obj6 != null) {
                                i3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(18 - TextUtils.lastIndexOf("", '0', 0), (char) (14687 - Color.alpha(0)), 112 - (ViewConfiguration.getWindowTouchSlop() >> 8));
                                byte b6 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                x((byte) 7, b6, b6, objArr9);
                                i3 = 2;
                                obj6 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj6);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj6).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((r6[oVar.e] ^ cArr4[intValue2]) ^ (d ^ 6565854932352255525L)) ^ ((int) (h ^ 6565854932352255525L))) ^ ((char) (i ^ 6565854932352255525L)));
                            oVar.e++;
                            i4 = i3;
                            obj = null;
                            i6 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }
}
