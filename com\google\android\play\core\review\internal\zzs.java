package com.google.android.play.core.review.internal;

import android.content.ComponentName;
import android.content.ServiceConnection;
import android.os.IBinder;

/* compiled from: com.google.android.play:review@@2.0.1 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\review\internal\zzs.smali */
final class zzs implements ServiceConnection {
    final /* synthetic */ zzt zza;

    /* synthetic */ zzs(zzt zztVar, zzr zzrVar) {
        this.zza = zztVar;
    }

    @Override // android.content.ServiceConnection
    public final void onServiceConnected(ComponentName componentName, IBinder iBinder) {
        zzi zziVar;
        zziVar = this.zza.zzc;
        zziVar.zzd("ServiceConnectionImpl.onServiceConnected(%s)", componentName);
        zzt zztVar = this.zza;
        zztVar.zzc().post(new zzp(this, iBinder));
    }

    @Override // android.content.ServiceConnection
    public final void onServiceDisconnected(ComponentName componentName) {
        zzi zziVar;
        zziVar = this.zza.zzc;
        zziVar.zzd("ServiceConnectionImpl.onServiceDisconnected(%s)", componentName);
        zzt zztVar = this.zza;
        zztVar.zzc().post(new zzq(this));
    }
}
