package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.math.ec.rfc8032.Ed448;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\i4.smali */
public final class i4 extends AsymmetricKeyParameter {
    private final Ed448.PublicPoint b;

    public i4(byte[] bArr) {
        this(a(bArr), 0);
    }

    private static Ed448.PublicPoint a(byte[] bArr, int i) {
        Ed448.PublicPoint validatePublicKeyPartialExport = Ed448.validatePublicKeyPartialExport(bArr, i);
        if (validatePublicKeyPartialExport != null) {
            return validatePublicKeyPartialExport;
        }
        throw new IllegalArgumentException("invalid public key");
    }

    public i4(byte[] bArr, int i) {
        super(false);
        this.b = a(bArr, i);
    }

    private static byte[] a(byte[] bArr) {
        if (bArr.length == 57) {
            return bArr;
        }
        throw new IllegalArgumentException("'buf' must have length 57");
    }
}
