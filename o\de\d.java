package o.de;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\d.smali */
public final class d {
    private static int b = 0;
    private static int e = 1;
    private f c;
    private o.av.a d;

    public d(o.av.a aVar, f fVar) {
        this.d = aVar;
        this.c = fVar;
    }

    public final o.av.a c() {
        int i = e + 5;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                int i2 = 73 / 0;
                return this.d;
            default:
                return this.d;
        }
    }

    public final f e() {
        int i = e;
        int i2 = (i + Opcodes.ISHL) - 1;
        b = i2 % 128;
        int i3 = i2 % 2;
        f fVar = this.c;
        int i4 = ((i | 51) << 1) - (i ^ 51);
        b = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return fVar;
            default:
                throw null;
        }
    }
}
