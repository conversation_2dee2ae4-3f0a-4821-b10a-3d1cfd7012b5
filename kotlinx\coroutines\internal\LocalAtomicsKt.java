package kotlinx.coroutines.internal;

import kotlin.Metadata;

/* compiled from: LocalAtomics.kt */
@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0018\u0002\n\u0000*\f\b\u0000\u0010\u0000\"\u00020\u00012\u00020\u0001¨\u0006\u0002"}, d2 = {"LocalAtomicInt", "Ljava/util/concurrent/atomic/AtomicInteger;", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\internal\LocalAtomicsKt.smali */
public final class LocalAtomicsKt {
    public static /* synthetic */ void LocalAtomicInt$annotations() {
    }
}
