package o.aj;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import java.util.Map;
import o.es.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aj\d.smali */
public final class d {
    private final Date a;
    private final Map<b, Object> b;
    private final String c;
    private static int e = 0;
    private static int d = 1;

    public d(String str, Date date, Map<b, Object> map) {
        this.c = str;
        this.a = date;
        this.b = map;
    }

    public final Date b() {
        int i = e;
        int i2 = (i ^ Opcodes.DDIV) + ((i & Opcodes.DDIV) << 1);
        d = i2 % 128;
        switch (i2 % 2 == 0 ? '[' : (char) 29) {
            case Opcodes.DUP_X2 /* 91 */:
                throw null;
            default:
                return this.a;
        }
    }

    public final Map<b, Object> a() {
        int i = e;
        int i2 = (i & 83) + (i | 83);
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return this.b;
            default:
                throw null;
        }
    }
}
