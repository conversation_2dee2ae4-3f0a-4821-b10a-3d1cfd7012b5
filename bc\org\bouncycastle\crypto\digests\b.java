package bc.org.bouncycastle.crypto.digests;

import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\b.smali */
public class b implements q4 {
    private static long[] h = {1, 32898, -9223372036854742902L, -9223372034707259392L, 32907, 2147483649L, -9223372034707259263L, -9223372036854743031L, 138, 136, 2147516425L, 2147483658L, 2147516555L, -9223372036854775669L, -9223372036854742903L, -9223372036854743037L, -9223372036854743038L, -9223372036854775680L, 32778, -9223372034707292150L, -9223372034707259263L, -9223372036854742912L, 2147483649L, -9223372034707259384L};
    protected final q1 a;
    protected long[] b = new long[25];
    protected byte[] c = new byte[192];
    protected int d;
    protected int e;
    protected int f;
    protected boolean g;

    public b(int i, q1 q1Var) {
        this.a = q1Var;
        a(i);
        t1.a(c());
    }

    private void a(int i) {
        switch (i) {
            case 128:
            case BERTags.FLAGS /* 224 */:
            case 256:
            case 288:
            case 384:
            case 512:
                b(1600 - (i << 1));
                return;
            default:
                throw new IllegalArgumentException("bitLength must be one of 128, 224, 256, 288, 384, or 512.");
        }
    }

    private void b(int i) {
        if (i <= 0 || i >= 1600 || i % 64 != 0) {
            throw new IllegalStateException("invalid rate value");
        }
        this.d = i;
        int i2 = 0;
        while (true) {
            long[] jArr = this.b;
            if (i2 >= jArr.length) {
                Arrays.fill(this.c, (byte) 0);
                this.e = 0;
                this.g = false;
                this.f = (1600 - i) / 2;
                return;
            }
            jArr[i2] = 0;
            i2++;
        }
    }

    private void d() {
        byte[] bArr = this.c;
        int i = this.e;
        int i2 = i >>> 3;
        bArr[i2] = (byte) (bArr[i2] | ((byte) (1 << (i & 7))));
        int i3 = i + 1;
        this.e = i3;
        if (i3 == this.d) {
            a(bArr, 0);
        } else {
            int i4 = i3 >>> 6;
            int i5 = i3 & 63;
            int i6 = 0;
            for (int i7 = 0; i7 < i4; i7++) {
                long[] jArr = this.b;
                jArr[i7] = jArr[i7] ^ j6.d(this.c, i6);
                i6 += 8;
            }
            if (i5 > 0) {
                long[] jArr2 = this.b;
                jArr2[i4] = (((1 << i5) - 1) & j6.d(this.c, i6)) ^ jArr2[i4];
            }
        }
        long[] jArr3 = this.b;
        int i8 = (this.d - 1) >>> 6;
        jArr3[i8] = jArr3[i8] ^ Long.MIN_VALUE;
        this.e = 0;
        this.g = true;
    }

    protected p1 c() {
        throw null;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.q4
    public int getByteLength() {
        return this.d / 8;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void reset() {
        a(this.f);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void update(byte b) {
        a(b);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public void update(byte[] bArr, int i, int i2) {
        b(bArr, i, i2);
    }

    protected void a(byte b) {
        int i = this.e;
        if (i % 8 == 0) {
            if (!this.g) {
                byte[] bArr = this.c;
                bArr[i >>> 3] = b;
                int i2 = i + 8;
                this.e = i2;
                if (i2 == this.d) {
                    a(bArr, 0);
                    this.e = 0;
                    return;
                }
                return;
            }
            throw new IllegalStateException("attempt to absorb while squeezing");
        }
        throw new IllegalStateException("attempt to absorb with odd length queue");
    }

    protected void b(byte[] bArr, int i, int i2) {
        int i3;
        int i4 = this.e;
        if (i4 % 8 == 0) {
            if (!this.g) {
                int i5 = i4 >>> 3;
                int i6 = this.d >>> 3;
                int i7 = i6 - i5;
                if (i2 < i7) {
                    System.arraycopy(bArr, i, this.c, i5, i2);
                    this.e += i2 << 3;
                    return;
                }
                if (i5 <= 0) {
                    i3 = 0;
                } else {
                    System.arraycopy(bArr, i, this.c, i5, i7);
                    i3 = i7 + 0;
                    a(this.c, 0);
                }
                while (true) {
                    int i8 = i2 - i3;
                    if (i8 >= i6) {
                        a(bArr, i + i3);
                        i3 += i6;
                    } else {
                        System.arraycopy(bArr, i + i3, this.c, 0, i8);
                        this.e = i8 << 3;
                        return;
                    }
                }
            } else {
                throw new IllegalStateException("attempt to absorb while squeezing");
            }
        } else {
            throw new IllegalStateException("attempt to absorb with odd length queue");
        }
    }

    protected void a(int i, int i2) {
        if (i2 >= 1 && i2 <= 7) {
            int i3 = this.e;
            if (i3 % 8 == 0) {
                if (!this.g) {
                    this.c[i3 >>> 3] = (byte) (i & ((1 << i2) - 1));
                    this.e = i3 + i2;
                    return;
                }
                throw new IllegalStateException("attempt to absorb while squeezing");
            }
            throw new IllegalStateException("attempt to absorb with odd length queue");
        }
        throw new IllegalArgumentException("'bits' must be in the range 1 to 7");
    }

    protected void a(byte[] bArr, int i, long j) {
        if (!this.g) {
            d();
        }
        long j2 = 0;
        if (j % 8 != 0) {
            throw new IllegalStateException("outputLength not a multiple of 8");
        }
        while (j2 < j) {
            if (this.e == 0) {
                a();
            }
            int min = (int) Math.min(this.e, j - j2);
            System.arraycopy(this.c, (this.d - this.e) / 8, bArr, ((int) (j2 / 8)) + i, min / 8);
            this.e -= min;
            j2 += min;
        }
    }

    private void b() {
        long[] jArr = this.b;
        int i = 0;
        long j = jArr[0];
        char c = 1;
        long j2 = jArr[1];
        long j3 = jArr[2];
        char c2 = 3;
        long j4 = jArr[3];
        long j5 = jArr[4];
        long j6 = jArr[5];
        long j7 = jArr[6];
        long j8 = jArr[7];
        long j9 = jArr[8];
        long j10 = jArr[9];
        long j11 = jArr[10];
        long j12 = jArr[11];
        long j13 = jArr[12];
        long j14 = jArr[13];
        long j15 = jArr[14];
        long j16 = jArr[15];
        long j17 = jArr[16];
        long j18 = jArr[17];
        long j19 = jArr[18];
        long j20 = jArr[19];
        long j21 = jArr[20];
        long j22 = jArr[21];
        long j23 = jArr[22];
        long j24 = jArr[23];
        int i2 = 24;
        long j25 = jArr[24];
        while (i < i2) {
            long j26 = (((j ^ j6) ^ j11) ^ j16) ^ j21;
            long j27 = (((j2 ^ j7) ^ j12) ^ j17) ^ j22;
            long j28 = (((j3 ^ j8) ^ j13) ^ j18) ^ j23;
            long j29 = (((j4 ^ j9) ^ j14) ^ j19) ^ j24;
            long j30 = (((j5 ^ j10) ^ j15) ^ j20) ^ j25;
            long j31 = ((j27 << c) | (j27 >>> (-1))) ^ j30;
            long j32 = ((j28 << c) | (j28 >>> (-1))) ^ j26;
            long j33 = ((j29 << c) | (j29 >>> (-1))) ^ j27;
            long j34 = ((j30 << c) | (j30 >>> (-1))) ^ j28;
            long j35 = ((j26 << c) | (j26 >>> (-1))) ^ j29;
            long j36 = j ^ j31;
            long j37 = j6 ^ j31;
            long j38 = j11 ^ j31;
            long j39 = j16 ^ j31;
            long j40 = j21 ^ j31;
            long j41 = j2 ^ j32;
            long j42 = j7 ^ j32;
            long j43 = j12 ^ j32;
            long j44 = j17 ^ j32;
            long j45 = j22 ^ j32;
            long j46 = j3 ^ j33;
            long j47 = j8 ^ j33;
            long j48 = j13 ^ j33;
            long j49 = j18 ^ j33;
            long j50 = j23 ^ j33;
            long j51 = j4 ^ j34;
            long j52 = j9 ^ j34;
            long j53 = j14 ^ j34;
            long j54 = j19 ^ j34;
            long j55 = j24 ^ j34;
            long j56 = j5 ^ j35;
            long j57 = j10 ^ j35;
            long j58 = j15 ^ j35;
            long j59 = j20 ^ j35;
            long j60 = j25 ^ j35;
            long j61 = (j41 << c) | (j41 >>> 63);
            long j62 = (j42 << 44) | (j42 >>> 20);
            long j63 = (j57 << 20) | (j57 >>> 44);
            long j64 = (j50 << 61) | (j50 >>> c2);
            long j65 = (j58 << 39) | (j58 >>> 25);
            long j66 = (j40 << 18) | (j40 >>> 46);
            long j67 = (j46 << 62) | (j46 >>> 2);
            long j68 = (j48 << 43) | (j48 >>> 21);
            long j69 = (j53 << 25) | (j53 >>> 39);
            long j70 = (j59 << 8) | (j59 >>> 56);
            long j71 = (j55 << 56) | (j55 >>> 8);
            long j72 = (j39 << 41) | (j39 >>> 23);
            long j73 = (j56 << 27) | (j56 >>> 37);
            long j74 = (j60 << 14) | (j60 >>> 50);
            long j75 = (j45 << 2) | (j45 >>> 62);
            long j76 = (j52 << 55) | (j52 >>> 9);
            long j77 = (j44 << 45) | (j44 >>> 19);
            long j78 = (j37 << 36) | (j37 >>> 28);
            long j79 = (j51 << 28) | (j51 >>> 36);
            long j80 = (j54 << 21) | (j54 >>> 43);
            long j81 = (j49 << 15) | (j49 >>> 49);
            long j82 = (j43 << 10) | (j43 >>> 54);
            long j83 = (j47 << 6) | (j47 >>> 58);
            long j84 = (j38 << 3) | (j38 >>> 61);
            long j85 = ((~j62) & j68) ^ j36;
            long j86 = ((~j68) & j80) ^ j62;
            j3 = j68 ^ ((~j80) & j74);
            j4 = j80 ^ ((~j74) & j36);
            long j87 = j74 ^ ((~j36) & j62);
            long j88 = j79 ^ ((~j63) & j84);
            long j89 = ((~j84) & j77) ^ j63;
            long j90 = ((~j77) & j64) ^ j84;
            long j91 = j77 ^ ((~j64) & j79);
            long j92 = ((~j79) & j63) ^ j64;
            j11 = j61 ^ ((~j83) & j69);
            long j93 = ((~j69) & j70) ^ j83;
            long j94 = ((~j70) & j66) ^ j69;
            long j95 = j70 ^ ((~j66) & j61);
            long j96 = ((~j61) & j83) ^ j66;
            long j97 = j73 ^ ((~j78) & j82);
            long j98 = ((~j82) & j81) ^ j78;
            long j99 = j82 ^ ((~j81) & j71);
            long j100 = ((~j71) & j73) ^ j81;
            long j101 = ((~j73) & j78) ^ j71;
            long j102 = j67 ^ ((~j76) & j65);
            long j103 = ((~j65) & j72) ^ j76;
            j21 = j102;
            long j104 = j65 ^ ((~j72) & j75);
            long j105 = ((~j75) & j67) ^ j72;
            long j106 = ((~j67) & j76) ^ j75;
            long j107 = j85 ^ h[i];
            i++;
            j7 = j89;
            j13 = j94;
            j12 = j93;
            j14 = j95;
            j22 = j103;
            c2 = 3;
            j24 = j105;
            j23 = j104;
            j10 = j92;
            jArr = jArr;
            j20 = j101;
            j15 = j96;
            j8 = j90;
            j9 = j91;
            j18 = j99;
            j16 = j97;
            j5 = j87;
            j6 = j88;
            i2 = 24;
            j19 = j100;
            j17 = j98;
            c = 1;
            j2 = j86;
            j25 = j106;
            j = j107;
        }
        long[] jArr2 = jArr;
        jArr2[0] = j;
        jArr2[1] = j2;
        jArr2[2] = j3;
        jArr2[3] = j4;
        jArr2[4] = j5;
        jArr2[5] = j6;
        jArr2[6] = j7;
        jArr2[7] = j8;
        jArr2[8] = j9;
        jArr2[9] = j10;
        jArr2[10] = j11;
        jArr2[11] = j12;
        jArr2[12] = j13;
        jArr2[13] = j14;
        jArr2[14] = j15;
        jArr2[15] = j16;
        jArr2[16] = j17;
        jArr2[17] = j18;
        jArr2[18] = j19;
        jArr2[19] = j20;
        jArr2[20] = j21;
        jArr2[21] = j22;
        jArr2[22] = j23;
        jArr2[23] = j24;
        jArr2[24] = j25;
    }

    private void a(byte[] bArr, int i) {
        int i2 = this.d >>> 6;
        for (int i3 = 0; i3 < i2; i3++) {
            long[] jArr = this.b;
            jArr[i3] = jArr[i3] ^ j6.d(bArr, i);
            i += 8;
        }
        b();
    }

    private void a() {
        b();
        j6.a(this.b, 0, this.d >>> 6, this.c, 0);
        this.e = this.d;
    }
}
