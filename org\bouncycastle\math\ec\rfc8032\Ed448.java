package org.bouncycastle.math.ec.rfc8032;

import com.esotericsoftware.asm.Opcodes;
import java.security.SecureRandom;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import org.bouncycastle.crypto.Xof;
import org.bouncycastle.crypto.digests.SHAKEDigest;
import org.bouncycastle.math.ec.rfc7748.X448;
import org.bouncycastle.math.ec.rfc7748.X448Field;
import org.bouncycastle.math.raw.Nat;
import org.bouncycastle.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\math\ec\rfc8032\Ed448.smali */
public abstract class Ed448 {
    private static final int COORD_INTS = 14;
    private static final int C_d = -39081;
    private static final int L4_0 = 43969588;
    private static final int L4_1 = 30366549;
    private static final int L4_2 = 163752818;
    private static final int L4_3 = 258169998;
    private static final int L4_4 = 96434764;
    private static final int L4_5 = 227822194;
    private static final int L4_6 = 149865618;
    private static final int L4_7 = 550336261;
    private static final int L_0 = 78101261;
    private static final int L_1 = 141809365;
    private static final int L_2 = 175155932;
    private static final int L_3 = 64542499;
    private static final int L_4 = 158326419;
    private static final int L_5 = 191173276;
    private static final int L_6 = 104575268;
    private static final int L_7 = 137584065;
    private static final long M26L = 67108863;
    private static final long M28L = 268435455;
    private static final long M32L = 4294967295L;
    private static final int POINT_BYTES = 57;
    private static final int PRECOMP_BLOCKS = 5;
    private static final int PRECOMP_MASK = 15;
    private static final int PRECOMP_POINTS = 16;
    private static final int PRECOMP_SPACING = 18;
    private static final int PRECOMP_TEETH = 5;
    public static final int PREHASH_SIZE = 64;
    public static final int PUBLIC_KEY_SIZE = 57;
    private static final int SCALAR_BYTES = 57;
    private static final int SCALAR_INTS = 14;
    public static final int SECRET_KEY_SIZE = 57;
    public static final int SIGNATURE_SIZE = 114;
    private static final int WNAF_WIDTH_BASE = 7;
    private static final byte[] DOM4_PREFIX = {83, 105, 103, 69, 100, 52, 52, 56};
    private static final int[] P = {-1, -1, -1, -1, -1, -1, -1, -2, -1, -1, -1, -1, -1, -1};
    private static final int[] L = {-1420278541, 595116690, -1916432555, 560775794, -1361693040, -1001465015, 2093622249, -1, -1, -1, -1, -1, -1, LockFreeTaskQueueCore.MAX_CAPACITY_MASK};
    private static final int[] B_x = {118276190, 40534716, 9670182, 135141552, 85017403, 259173222, 68333082, 171784774, 174973732, 15824510, 73756743, 57518561, 94773951, 248652241, 107736333, 82941708};
    private static final int[] B_y = {36764180, 8885695, 130592152, 20104429, 163904957, 30304195, 121295871, 5901357, 125344798, 171541512, 175338348, 209069246, 3626697, 38307682, 24032956, 110359655};
    private static final Object precompLock = new Object();
    private static PointExt[] precompBaseTable = null;
    private static int[] precompBase = null;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\math\ec\rfc8032\Ed448$Algorithm.smali */
    public static final class Algorithm {
        public static final int Ed448 = 0;
        public static final int Ed448ph = 1;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\math\ec\rfc8032\Ed448$F.smali */
    private static class F extends X448Field {
        private F() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\math\ec\rfc8032\Ed448$PointExt.smali */
    private static class PointExt {
        int[] x;
        int[] y;
        int[] z;

        private PointExt() {
            this.x = F.create();
            this.y = F.create();
            this.z = F.create();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\math\ec\rfc8032\Ed448$PointPrecomp.smali */
    private static class PointPrecomp {
        int[] x;
        int[] y;

        private PointPrecomp() {
            this.x = F.create();
            this.y = F.create();
        }
    }

    private static byte[] calculateS(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int[] iArr = new int[28];
        decodeScalar(bArr, 0, iArr);
        int[] iArr2 = new int[14];
        decodeScalar(bArr2, 0, iArr2);
        int[] iArr3 = new int[14];
        decodeScalar(bArr3, 0, iArr3);
        Nat.mulAddTo(14, iArr2, iArr3, iArr);
        byte[] bArr4 = new byte[114];
        for (int i = 0; i < 28; i++) {
            encode32(iArr[i], bArr4, i * 4);
        }
        return reduceScalar(bArr4);
    }

    private static boolean checkContextVar(byte[] bArr) {
        return bArr != null && bArr.length < 256;
    }

    private static int checkPoint(int[] iArr, int[] iArr2) {
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        F.sqr(iArr, create2);
        F.sqr(iArr2, create3);
        F.mul(create2, create3, create);
        F.add(create2, create3, create2);
        F.mul(create, 39081, create);
        F.subOne(create);
        F.add(create, create2, create);
        F.normalize(create);
        return F.isZero(create);
    }

    private static int checkPoint(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        int[] create4 = F.create();
        F.sqr(iArr, create2);
        F.sqr(iArr2, create3);
        F.sqr(iArr3, create4);
        F.mul(create2, create3, create);
        F.add(create2, create3, create2);
        F.mul(create2, create4, create2);
        F.sqr(create4, create4);
        F.mul(create, 39081, create);
        F.sub(create, create4, create);
        F.add(create, create2, create);
        F.normalize(create);
        return F.isZero(create);
    }

    private static boolean checkPointVar(byte[] bArr) {
        if ((bArr[56] & ByteCompanionObject.MAX_VALUE) != 0) {
            return false;
        }
        decode32(bArr, 0, new int[14], 0, 14);
        return !Nat.gte(14, r2, P);
    }

    private static boolean checkScalarVar(byte[] bArr, int[] iArr) {
        if (bArr[56] != 0) {
            return false;
        }
        decodeScalar(bArr, 0, iArr);
        return !Nat.gte(14, iArr, L);
    }

    private static byte[] copy(byte[] bArr, int i, int i2) {
        byte[] bArr2 = new byte[i2];
        System.arraycopy(bArr, i, bArr2, 0, i2);
        return bArr2;
    }

    public static Xof createPrehash() {
        return createXof();
    }

    private static Xof createXof() {
        return new SHAKEDigest(256);
    }

    private static int decode16(byte[] bArr, int i) {
        return ((bArr[i + 1] & 255) << 8) | (bArr[i] & 255);
    }

    private static int decode24(byte[] bArr, int i) {
        int i2 = bArr[i] & 255;
        int i3 = i + 1;
        return ((bArr[i3 + 1] & 255) << 16) | i2 | ((bArr[i3] & 255) << 8);
    }

    private static int decode32(byte[] bArr, int i) {
        int i2 = bArr[i] & 255;
        int i3 = i + 1;
        int i4 = i2 | ((bArr[i3] & 255) << 8);
        int i5 = i3 + 1;
        return (bArr[i5 + 1] << 24) | i4 | ((bArr[i5] & 255) << 16);
    }

    private static void decode32(byte[] bArr, int i, int[] iArr, int i2, int i3) {
        for (int i4 = 0; i4 < i3; i4++) {
            iArr[i2 + i4] = decode32(bArr, (i4 * 4) + i);
        }
    }

    private static boolean decodePointVar(byte[] bArr, int i, boolean z, PointExt pointExt) {
        byte[] copy = copy(bArr, i, 57);
        if (!checkPointVar(copy)) {
            return false;
        }
        byte b = copy[56];
        int i2 = (b & ByteCompanionObject.MIN_VALUE) >>> 7;
        copy[56] = (byte) (b & ByteCompanionObject.MAX_VALUE);
        F.decode(copy, 0, pointExt.y);
        int[] create = F.create();
        int[] create2 = F.create();
        F.sqr(pointExt.y, create);
        F.mul(create, 39081, create2);
        F.negate(create, create);
        F.addOne(create);
        F.addOne(create2);
        if (!F.sqrtRatioVar(create, create2, pointExt.x)) {
            return false;
        }
        F.normalize(pointExt.x);
        if (i2 == 1 && F.isZeroVar(pointExt.x)) {
            return false;
        }
        if (z ^ (i2 != (pointExt.x[0] & 1))) {
            F.negate(pointExt.x, pointExt.x);
        }
        pointExtendXY(pointExt);
        return true;
    }

    private static void decodeScalar(byte[] bArr, int i, int[] iArr) {
        decode32(bArr, i, iArr, 0, 14);
    }

    private static void dom4(Xof xof, byte b, byte[] bArr) {
        byte[] bArr2 = DOM4_PREFIX;
        int length = bArr2.length;
        int i = length + 2;
        int length2 = bArr.length + i;
        byte[] bArr3 = new byte[length2];
        System.arraycopy(bArr2, 0, bArr3, 0, length);
        bArr3[length] = b;
        bArr3[length + 1] = (byte) bArr.length;
        System.arraycopy(bArr, 0, bArr3, i, bArr.length);
        xof.update(bArr3, 0, length2);
    }

    private static void encode24(int i, byte[] bArr, int i2) {
        bArr[i2] = (byte) i;
        int i3 = i2 + 1;
        bArr[i3] = (byte) (i >>> 8);
        bArr[i3 + 1] = (byte) (i >>> 16);
    }

    private static void encode32(int i, byte[] bArr, int i2) {
        bArr[i2] = (byte) i;
        int i3 = i2 + 1;
        bArr[i3] = (byte) (i >>> 8);
        int i4 = i3 + 1;
        bArr[i4] = (byte) (i >>> 16);
        bArr[i4 + 1] = (byte) (i >>> 24);
    }

    private static void encode56(long j, byte[] bArr, int i) {
        encode32((int) j, bArr, i);
        encode24((int) (j >>> 32), bArr, i + 4);
    }

    private static int encodePoint(PointExt pointExt, byte[] bArr, int i) {
        int[] create = F.create();
        int[] create2 = F.create();
        F.inv(pointExt.z, create2);
        F.mul(pointExt.x, create2, create);
        F.mul(pointExt.y, create2, create2);
        F.normalize(create);
        F.normalize(create2);
        int checkPoint = checkPoint(create, create2);
        F.encode(create2, bArr, i);
        bArr[(i + 57) - 1] = (byte) ((create[0] & 1) << 7);
        return checkPoint;
    }

    public static void generatePrivateKey(SecureRandom secureRandom, byte[] bArr) {
        secureRandom.nextBytes(bArr);
    }

    public static void generatePublicKey(byte[] bArr, int i, byte[] bArr2, int i2) {
        Xof createXof = createXof();
        byte[] bArr3 = new byte[114];
        createXof.update(bArr, i, 57);
        createXof.doFinal(bArr3, 0, 114);
        byte[] bArr4 = new byte[57];
        pruneScalar(bArr3, 0, bArr4);
        scalarMultBaseEncoded(bArr4, bArr2, i2);
    }

    private static int getWindow4(int[] iArr, int i) {
        return (iArr[i >>> 3] >>> ((i & 7) << 2)) & 15;
    }

    private static byte[] getWnafVar(int[] iArr, int i) {
        int[] iArr2 = new int[28];
        int i2 = 0;
        int i3 = 14;
        int i4 = 28;
        int i5 = 0;
        while (true) {
            i3--;
            if (i3 < 0) {
                break;
            }
            int i6 = iArr[i3];
            int i7 = i4 - 1;
            iArr2[i7] = (i5 << 16) | (i6 >>> 16);
            i4 = i7 - 1;
            iArr2[i4] = i6;
            i5 = i6;
        }
        byte[] bArr = new byte[447];
        int i8 = 32 - i;
        int i9 = 0;
        int i10 = 0;
        while (i2 < 28) {
            int i11 = iArr2[i2];
            while (i9 < 16) {
                int i12 = i11 >>> i9;
                if ((i12 & 1) == i10) {
                    i9++;
                } else {
                    int i13 = (i12 | 1) << i8;
                    bArr[(i2 << 4) + i9] = (byte) (i13 >> i8);
                    i9 += i;
                    i10 = i13 >>> 31;
                }
            }
            i2++;
            i9 -= 16;
        }
        return bArr;
    }

    private static void implSign(Xof xof, byte[] bArr, byte[] bArr2, byte[] bArr3, int i, byte[] bArr4, byte b, byte[] bArr5, int i2, int i3, byte[] bArr6, int i4) {
        dom4(xof, b, bArr4);
        xof.update(bArr, 57, 57);
        xof.update(bArr5, i2, i3);
        xof.doFinal(bArr, 0, bArr.length);
        byte[] reduceScalar = reduceScalar(bArr);
        byte[] bArr7 = new byte[57];
        scalarMultBaseEncoded(reduceScalar, bArr7, 0);
        dom4(xof, b, bArr4);
        xof.update(bArr7, 0, 57);
        xof.update(bArr3, i, 57);
        xof.update(bArr5, i2, i3);
        xof.doFinal(bArr, 0, bArr.length);
        byte[] calculateS = calculateS(reduceScalar, reduceScalar(bArr), bArr2);
        System.arraycopy(bArr7, 0, bArr6, i4, 57);
        System.arraycopy(calculateS, 0, bArr6, i4 + 57, 57);
    }

    private static void implSign(byte[] bArr, int i, byte[] bArr2, byte b, byte[] bArr3, int i2, int i3, byte[] bArr4, int i4) {
        if (!checkContextVar(bArr2)) {
            throw new IllegalArgumentException("ctx");
        }
        Xof createXof = createXof();
        byte[] bArr5 = new byte[114];
        createXof.update(bArr, i, 57);
        createXof.doFinal(bArr5, 0, 114);
        byte[] bArr6 = new byte[57];
        pruneScalar(bArr5, 0, bArr6);
        byte[] bArr7 = new byte[57];
        scalarMultBaseEncoded(bArr6, bArr7, 0);
        implSign(createXof, bArr5, bArr6, bArr7, 0, bArr2, b, bArr3, i2, i3, bArr4, i4);
    }

    private static void implSign(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte b, byte[] bArr4, int i3, int i4, byte[] bArr5, int i5) {
        if (!checkContextVar(bArr3)) {
            throw new IllegalArgumentException("ctx");
        }
        Xof createXof = createXof();
        byte[] bArr6 = new byte[114];
        createXof.update(bArr, i, 57);
        createXof.doFinal(bArr6, 0, 114);
        byte[] bArr7 = new byte[57];
        pruneScalar(bArr6, 0, bArr7);
        implSign(createXof, bArr6, bArr7, bArr2, i2, bArr3, b, bArr4, i3, i4, bArr5, i5);
    }

    private static boolean implVerify(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte b, byte[] bArr4, int i3, int i4) {
        if (!checkContextVar(bArr3)) {
            throw new IllegalArgumentException("ctx");
        }
        byte[] copy = copy(bArr, i, 57);
        byte[] copy2 = copy(bArr, i + 57, 57);
        if (!checkPointVar(copy)) {
            return false;
        }
        int[] iArr = new int[14];
        if (!checkScalarVar(copy2, iArr)) {
            return false;
        }
        PointExt pointExt = new PointExt();
        if (!decodePointVar(bArr2, i2, true, pointExt)) {
            return false;
        }
        Xof createXof = createXof();
        byte[] bArr5 = new byte[114];
        dom4(createXof, b, bArr3);
        createXof.update(copy, 0, 57);
        createXof.update(bArr2, i2, 57);
        createXof.update(bArr4, i3, i4);
        createXof.doFinal(bArr5, 0, 114);
        int[] iArr2 = new int[14];
        decodeScalar(reduceScalar(bArr5), 0, iArr2);
        PointExt pointExt2 = new PointExt();
        scalarMultStrausVar(iArr, iArr2, pointExt, pointExt2);
        byte[] bArr6 = new byte[57];
        return encodePoint(pointExt2, bArr6, 0) != 0 && Arrays.areEqual(bArr6, copy);
    }

    private static boolean isNeutralElementVar(int[] iArr, int[] iArr2, int[] iArr3) {
        return F.isZeroVar(iArr) && F.areEqualVar(iArr2, iArr3);
    }

    private static void pointAdd(PointExt pointExt, PointExt pointExt2) {
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        int[] create4 = F.create();
        int[] create5 = F.create();
        int[] create6 = F.create();
        int[] create7 = F.create();
        int[] create8 = F.create();
        F.mul(pointExt.z, pointExt2.z, create);
        F.sqr(create, create2);
        F.mul(pointExt.x, pointExt2.x, create3);
        F.mul(pointExt.y, pointExt2.y, create4);
        F.mul(create3, create4, create5);
        F.mul(create5, 39081, create5);
        F.add(create2, create5, create6);
        F.sub(create2, create5, create7);
        F.add(pointExt.x, pointExt.y, create2);
        F.add(pointExt2.x, pointExt2.y, create5);
        F.mul(create2, create5, create8);
        F.add(create4, create3, create2);
        F.sub(create4, create3, create5);
        F.carry(create2);
        F.sub(create8, create2, create8);
        F.mul(create8, create, create8);
        F.mul(create5, create, create5);
        F.mul(create6, create8, pointExt2.x);
        F.mul(create5, create7, pointExt2.y);
        F.mul(create6, create7, pointExt2.z);
    }

    private static void pointAddPrecomp(PointPrecomp pointPrecomp, PointExt pointExt) {
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        int[] create4 = F.create();
        int[] create5 = F.create();
        int[] create6 = F.create();
        int[] create7 = F.create();
        F.sqr(pointExt.z, create);
        F.mul(pointPrecomp.x, pointExt.x, create2);
        F.mul(pointPrecomp.y, pointExt.y, create3);
        F.mul(create2, create3, create4);
        F.mul(create4, 39081, create4);
        F.add(create, create4, create5);
        F.sub(create, create4, create6);
        F.add(pointPrecomp.x, pointPrecomp.y, create);
        F.add(pointExt.x, pointExt.y, create4);
        F.mul(create, create4, create7);
        F.add(create3, create2, create);
        F.sub(create3, create2, create4);
        F.carry(create);
        F.sub(create7, create, create7);
        F.mul(create7, pointExt.z, create7);
        F.mul(create4, pointExt.z, create4);
        F.mul(create5, create7, pointExt.x);
        F.mul(create4, create6, pointExt.y);
        F.mul(create5, create6, pointExt.z);
    }

    private static void pointAddVar(boolean z, PointExt pointExt, PointExt pointExt2) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        int[] create4 = F.create();
        int[] create5 = F.create();
        int[] create6 = F.create();
        int[] create7 = F.create();
        int[] create8 = F.create();
        if (z) {
            F.sub(pointExt.y, pointExt.x, create8);
            iArr2 = create2;
            iArr = create5;
            iArr4 = create6;
            iArr3 = create7;
        } else {
            F.add(pointExt.y, pointExt.x, create8);
            iArr = create2;
            iArr2 = create5;
            iArr3 = create6;
            iArr4 = create7;
        }
        F.mul(pointExt.z, pointExt2.z, create);
        F.sqr(create, create2);
        F.mul(pointExt.x, pointExt2.x, create3);
        F.mul(pointExt.y, pointExt2.y, create4);
        F.mul(create3, create4, create5);
        F.mul(create5, 39081, create5);
        F.add(create2, create5, iArr3);
        F.sub(create2, create5, iArr4);
        F.add(pointExt2.x, pointExt2.y, create5);
        F.mul(create8, create5, create8);
        F.add(create4, create3, iArr);
        F.sub(create4, create3, iArr2);
        F.carry(iArr);
        F.sub(create8, create2, create8);
        F.mul(create8, create, create8);
        F.mul(create5, create, create5);
        F.mul(create6, create8, pointExt2.x);
        F.mul(create5, create7, pointExt2.y);
        F.mul(create6, create7, pointExt2.z);
    }

    private static PointExt pointCopy(PointExt pointExt) {
        PointExt pointExt2 = new PointExt();
        pointCopy(pointExt, pointExt2);
        return pointExt2;
    }

    private static void pointCopy(PointExt pointExt, PointExt pointExt2) {
        F.copy(pointExt.x, 0, pointExt2.x, 0);
        F.copy(pointExt.y, 0, pointExt2.y, 0);
        F.copy(pointExt.z, 0, pointExt2.z, 0);
    }

    private static void pointDouble(PointExt pointExt) {
        int[] create = F.create();
        int[] create2 = F.create();
        int[] create3 = F.create();
        int[] create4 = F.create();
        int[] create5 = F.create();
        int[] create6 = F.create();
        F.add(pointExt.x, pointExt.y, create);
        F.sqr(create, create);
        F.sqr(pointExt.x, create2);
        F.sqr(pointExt.y, create3);
        F.add(create2, create3, create4);
        F.carry(create4);
        F.sqr(pointExt.z, create5);
        F.add(create5, create5, create5);
        F.carry(create5);
        F.sub(create4, create5, create6);
        F.sub(create, create4, create);
        F.sub(create2, create3, create2);
        F.mul(create, create6, pointExt.x);
        F.mul(create4, create2, pointExt.y);
        F.mul(create4, create6, pointExt.z);
    }

    private static void pointExtendXY(PointExt pointExt) {
        F.one(pointExt.z);
    }

    private static void pointLookup(int i, int i2, PointPrecomp pointPrecomp) {
        int i3 = i * 16 * 2 * 16;
        for (int i4 = 0; i4 < 16; i4++) {
            int i5 = ((i4 ^ i2) - 1) >> 31;
            F.cmov(i5, precompBase, i3, pointPrecomp.x, 0);
            int i6 = i3 + 16;
            F.cmov(i5, precompBase, i6, pointPrecomp.y, 0);
            i3 = i6 + 16;
        }
    }

    private static void pointLookup(int[] iArr, int i, int[] iArr2, PointExt pointExt) {
        int window4 = getWindow4(iArr, i);
        int i2 = (window4 >>> 3) ^ 1;
        int i3 = (window4 ^ (-i2)) & 7;
        int i4 = 0;
        for (int i5 = 0; i5 < 8; i5++) {
            int i6 = ((i5 ^ i3) - 1) >> 31;
            F.cmov(i6, iArr2, i4, pointExt.x, 0);
            int i7 = i4 + 16;
            F.cmov(i6, iArr2, i7, pointExt.y, 0);
            int i8 = i7 + 16;
            F.cmov(i6, iArr2, i8, pointExt.z, 0);
            i4 = i8 + 16;
        }
        F.cnegate(i2, pointExt.x);
    }

    private static int[] pointPrecompute(PointExt pointExt, int i) {
        PointExt pointCopy = pointCopy(pointExt);
        PointExt pointCopy2 = pointCopy(pointCopy);
        pointDouble(pointCopy2);
        int[] createTable = F.createTable(i * 3);
        int i2 = 0;
        int i3 = 0;
        while (true) {
            F.copy(pointCopy.x, 0, createTable, i2);
            int i4 = i2 + 16;
            F.copy(pointCopy.y, 0, createTable, i4);
            int i5 = i4 + 16;
            F.copy(pointCopy.z, 0, createTable, i5);
            i2 = i5 + 16;
            i3++;
            if (i3 == i) {
                return createTable;
            }
            pointAdd(pointCopy2, pointCopy);
        }
    }

    private static PointExt[] pointPrecomputeVar(PointExt pointExt, int i) {
        PointExt pointCopy = pointCopy(pointExt);
        pointDouble(pointCopy);
        PointExt[] pointExtArr = new PointExt[i];
        pointExtArr[0] = pointCopy(pointExt);
        for (int i2 = 1; i2 < i; i2++) {
            PointExt pointCopy2 = pointCopy(pointExtArr[i2 - 1]);
            pointExtArr[i2] = pointCopy2;
            pointAddVar(false, pointCopy, pointCopy2);
        }
        return pointExtArr;
    }

    private static void pointSetNeutral(PointExt pointExt) {
        F.zero(pointExt.x);
        F.one(pointExt.y);
        F.one(pointExt.z);
    }

    public static void precompute() {
        synchronized (precompLock) {
            if (precompBase != null) {
                return;
            }
            PointExt pointExt = new PointExt();
            F.copy(B_x, 0, pointExt.x, 0);
            F.copy(B_y, 0, pointExt.y, 0);
            pointExtendXY(pointExt);
            precompBaseTable = pointPrecomputeVar(pointExt, 32);
            precompBase = F.createTable(Opcodes.IF_ICMPNE);
            int i = 0;
            for (int i2 = 0; i2 < 5; i2++) {
                PointExt[] pointExtArr = new PointExt[5];
                PointExt pointExt2 = new PointExt();
                pointSetNeutral(pointExt2);
                int i3 = 0;
                while (true) {
                    if (i3 >= 5) {
                        break;
                    }
                    pointAddVar(true, pointExt, pointExt2);
                    pointDouble(pointExt);
                    pointExtArr[i3] = pointCopy(pointExt);
                    if (i2 + i3 != 8) {
                        for (int i4 = 1; i4 < 18; i4++) {
                            pointDouble(pointExt);
                        }
                    }
                    i3++;
                }
                PointExt[] pointExtArr2 = new PointExt[16];
                pointExtArr2[0] = pointExt2;
                int i5 = 1;
                for (int i6 = 0; i6 < 4; i6++) {
                    int i7 = 1 << i6;
                    int i8 = 0;
                    while (i8 < i7) {
                        PointExt pointCopy = pointCopy(pointExtArr2[i5 - i7]);
                        pointExtArr2[i5] = pointCopy;
                        pointAddVar(false, pointExtArr[i6], pointCopy);
                        i8++;
                        i5++;
                    }
                }
                int[] createTable = F.createTable(16);
                int[] create = F.create();
                F.copy(pointExtArr2[0].z, 0, create, 0);
                F.copy(create, 0, createTable, 0);
                int i9 = 0;
                while (true) {
                    i9++;
                    if (i9 >= 16) {
                        break;
                    }
                    F.mul(create, pointExtArr2[i9].z, create);
                    F.copy(create, 0, createTable, i9 * 16);
                }
                F.invVar(create, create);
                int i10 = i9 - 1;
                int[] create2 = F.create();
                while (i10 > 0) {
                    int i11 = i10 - 1;
                    F.copy(createTable, i11 * 16, create2, 0);
                    F.mul(create2, create, create2);
                    F.copy(create2, 0, createTable, i10 * 16);
                    F.mul(create, pointExtArr2[i10].z, create);
                    i10 = i11;
                }
                F.copy(create, 0, createTable, 0);
                for (int i12 = 0; i12 < 16; i12++) {
                    PointExt pointExt3 = pointExtArr2[i12];
                    F.copy(createTable, i12 * 16, pointExt3.z, 0);
                    F.mul(pointExt3.x, pointExt3.z, pointExt3.x);
                    F.mul(pointExt3.y, pointExt3.z, pointExt3.y);
                    F.copy(pointExt3.x, 0, precompBase, i);
                    int i13 = i + 16;
                    F.copy(pointExt3.y, 0, precompBase, i13);
                    i = i13 + 16;
                }
            }
        }
    }

    private static void pruneScalar(byte[] bArr, int i, byte[] bArr2) {
        System.arraycopy(bArr, i, bArr2, 0, 56);
        bArr2[0] = (byte) (bArr2[0] & 252);
        bArr2[55] = (byte) (bArr2[55] | ByteCompanionObject.MIN_VALUE);
        bArr2[56] = 0;
    }

    private static byte[] reduceScalar(byte[] bArr) {
        long decode32 = decode32(bArr, 0) & M32L;
        long decode24 = (decode24(bArr, 4) << 4) & M32L;
        long decode322 = decode32(bArr, 7) & M32L;
        long decode242 = (decode24(bArr, 11) << 4) & M32L;
        long decode323 = decode32(bArr, 14) & M32L;
        long decode243 = (decode24(bArr, 18) << 4) & M32L;
        long decode324 = decode32(bArr, 21) & M32L;
        long decode244 = (decode24(bArr, 25) << 4) & M32L;
        long decode325 = decode32(bArr, 28) & M32L;
        long decode245 = (decode24(bArr, 32) << 4) & M32L;
        long decode326 = decode32(bArr, 35) & M32L;
        long decode246 = (decode24(bArr, 39) << 4) & M32L;
        long decode327 = decode32(bArr, 42) & M32L;
        long decode247 = (decode24(bArr, 46) << 4) & M32L;
        long decode328 = decode32(bArr, 49) & M32L;
        long decode248 = (decode24(bArr, 53) << 4) & M32L;
        long decode329 = decode32(bArr, 56) & M32L;
        long decode249 = (decode24(bArr, 60) << 4) & M32L;
        long decode3210 = decode32(bArr, 63) & M32L;
        long decode2410 = (decode24(bArr, 67) << 4) & M32L;
        long decode3211 = decode32(bArr, 70) & M32L;
        long decode2411 = (decode24(bArr, 74) << 4) & M32L;
        long decode3212 = decode32(bArr, 77) & M32L;
        long decode2412 = (decode24(bArr, 81) << 4) & M32L;
        long decode3213 = decode32(bArr, 84) & M32L;
        long decode2413 = (decode24(bArr, 88) << 4) & M32L;
        long decode3214 = decode32(bArr, 91) & M32L;
        long decode2414 = (decode24(bArr, 95) << 4) & M32L;
        long decode3215 = decode32(bArr, 98) & M32L;
        long decode2415 = (decode24(bArr, 102) << 4) & M32L;
        long decode3216 = decode32(bArr, Opcodes.LMUL) & M32L;
        long decode2416 = (decode24(bArr, 109) << 4) & M32L;
        long decode16 = decode16(bArr, Opcodes.IREM) & M32L;
        long j = decode2416 + (decode3216 >>> 28);
        long j2 = decode3216 & M28L;
        long j3 = decode2411 + (decode16 * 227822194) + (j * 149865618);
        long j4 = decode3212 + (decode16 * 149865618) + (j * 550336261);
        long j5 = decode328 + (j2 * 43969588);
        long j6 = decode248 + (j * 43969588) + (j2 * 30366549);
        long j7 = decode329 + (decode16 * 43969588) + (j * 30366549) + (j2 * 163752818);
        long j8 = decode249 + (decode16 * 30366549) + (j * 163752818) + (j2 * 258169998);
        long j9 = decode3210 + (decode16 * 163752818) + (j * 258169998) + (j2 * 96434764);
        long j10 = decode2410 + (decode16 * 258169998) + (j * 96434764) + (j2 * 227822194);
        long j11 = decode3211 + (decode16 * 96434764) + (j * 227822194) + (j2 * 149865618);
        long j12 = decode2415 + (decode3215 >>> 28);
        long j13 = decode3215 & M28L;
        long j14 = decode247 + (j12 * 43969588);
        long j15 = j10 + (j12 * 149865618);
        long j16 = j11 + (j12 * 550336261);
        long j17 = decode327 + (j13 * 43969588);
        long j18 = j5 + (j12 * 30366549) + (j13 * 163752818);
        long j19 = j6 + (j12 * 163752818) + (j13 * 258169998);
        long j20 = j7 + (j12 * 258169998) + (j13 * 96434764);
        long j21 = j8 + (j12 * 96434764) + (j13 * 227822194);
        long j22 = j9 + (j12 * 227822194) + (j13 * 149865618);
        long j23 = decode2414 + (decode3214 >>> 28);
        long j24 = decode3214 & M28L;
        long j25 = decode246 + (j23 * 43969588);
        long j26 = j22 + (j23 * 550336261);
        long j27 = decode326 + (j24 * 43969588);
        long j28 = j17 + (j23 * 30366549) + (j24 * 163752818);
        long j29 = j14 + (j13 * 30366549) + (j23 * 163752818) + (j24 * 258169998);
        long j30 = j18 + (j23 * 258169998) + (j24 * 96434764);
        long j31 = j19 + (j23 * 96434764) + (j24 * 227822194);
        long j32 = j20 + (j23 * 227822194) + (j24 * 149865618);
        long j33 = j21 + (j23 * 149865618) + (j24 * 550336261);
        long j34 = decode2413 + (decode3213 >>> 28);
        long j35 = decode3213 & M28L;
        long j36 = j3 + (j2 * 550336261) + (j16 >>> 28);
        long j37 = j16 & M28L;
        long j38 = j4 + (j36 >>> 28);
        long j39 = j36 & M28L;
        long j40 = decode2412 + (decode16 * 550336261) + (j38 >>> 28);
        long j41 = j38 & M28L;
        long j42 = j35 + (j40 >>> 28);
        long j43 = j40 & M28L;
        long j44 = decode244 + (j43 * 43969588);
        long j45 = decode325 + (j42 * 43969588) + (j43 * 30366549);
        long j46 = decode245 + (j34 * 43969588) + (j42 * 30366549) + (j43 * 163752818);
        long j47 = j27 + (j34 * 30366549) + (j42 * 163752818) + (j43 * 258169998);
        long j48 = j25 + (j24 * 30366549) + (j34 * 163752818) + (j42 * 258169998) + (j43 * 96434764);
        long j49 = j28 + (j34 * 258169998) + (j42 * 96434764) + (j43 * 227822194);
        long j50 = j29 + (j34 * 96434764) + (j42 * 227822194) + (j43 * 149865618);
        long j51 = j30 + (j34 * 227822194) + (j42 * 149865618) + (j43 * 550336261);
        long j52 = decode324 + (j41 * 43969588);
        long j53 = j26 + (j33 >>> 28);
        long j54 = j33 & M28L;
        long j55 = j15 + (j13 * 550336261) + (j53 >>> 28);
        long j56 = j53 & M28L;
        long j57 = j37 + (j55 >>> 28);
        long j58 = j55 & M28L;
        long j59 = j39 + (j57 >>> 28);
        long j60 = j57 & M28L;
        long j61 = decode323 + (j60 * 43969588);
        long j62 = decode243 + (j59 * 43969588) + (j60 * 30366549);
        long j63 = j52 + (j59 * 30366549) + (j60 * 163752818);
        long j64 = j44 + (j41 * 30366549) + (j59 * 163752818) + (j60 * 258169998);
        long j65 = j45 + (j41 * 163752818) + (j59 * 258169998) + (j60 * 96434764);
        long j66 = j46 + (j41 * 258169998) + (j59 * 96434764) + (j60 * 227822194);
        long j67 = j47 + (j41 * 96434764) + (j59 * 227822194) + (j60 * 149865618);
        long j68 = j48 + (j41 * 227822194) + (j59 * 149865618) + (j60 * 550336261);
        long j69 = decode242 + (j58 * 43969588);
        long j70 = j61 + (j58 * 30366549);
        long j71 = j62 + (j58 * 163752818);
        long j72 = j63 + (j58 * 258169998);
        long j73 = j64 + (j58 * 96434764);
        long j74 = j65 + (j58 * 227822194);
        long j75 = j66 + (j58 * 149865618);
        long j76 = j67 + (j58 * 550336261);
        long j77 = j31 + (j34 * 149865618) + (j42 * 550336261) + (j51 >>> 28);
        long j78 = j51 & M28L;
        long j79 = j32 + (j34 * 550336261) + (j77 >>> 28);
        long j80 = j77 & M28L;
        long j81 = j54 + (j79 >>> 28);
        long j82 = j79 & M28L;
        long j83 = j56 + (j81 >>> 28);
        long j84 = j81 & M28L;
        long j85 = j74 + (j83 * 149865618);
        long j86 = j75 + (j83 * 550336261);
        long j87 = j80 & M26L;
        long j88 = (j82 * 4) + (j80 >>> 26) + 1;
        long j89 = decode32 + (78101261 * j88);
        long j90 = decode322 + (j83 * 43969588) + (30366549 * j84) + (175155932 * j88);
        long j91 = j69 + (j83 * 30366549) + (163752818 * j84) + (64542499 * j88);
        long j92 = j70 + (j83 * 163752818) + (258169998 * j84) + (158326419 * j88);
        long j93 = j71 + (j83 * 258169998) + (96434764 * j84) + (191173276 * j88);
        long j94 = j72 + (j83 * 96434764) + (227822194 * j84) + (104575268 * j88);
        long j95 = j73 + (j83 * 227822194) + (149865618 * j84) + (j88 * 137584065);
        long j96 = decode24 + (43969588 * j84) + (141809365 * j88) + (j89 >>> 28);
        long j97 = j89 & M28L;
        long j98 = j90 + (j96 >>> 28);
        long j99 = j96 & M28L;
        long j100 = j91 + (j98 >>> 28);
        long j101 = j98 & M28L;
        long j102 = j92 + (j100 >>> 28);
        long j103 = j100 & M28L;
        long j104 = j93 + (j102 >>> 28);
        long j105 = j102 & M28L;
        long j106 = j94 + (j104 >>> 28);
        long j107 = j104 & M28L;
        long j108 = j95 + (j106 >>> 28);
        long j109 = j106 & M28L;
        long j110 = j85 + (j84 * 550336261) + (j108 >>> 28);
        long j111 = j108 & M28L;
        long j112 = j86 + (j110 >>> 28);
        long j113 = j110 & M28L;
        long j114 = j76 + (j112 >>> 28);
        long j115 = j112 & M28L;
        long j116 = j68 + (j114 >>> 28);
        long j117 = j114 & M28L;
        long j118 = j49 + (j41 * 149865618) + (j59 * 550336261) + (j116 >>> 28);
        long j119 = j116 & M28L;
        long j120 = j50 + (j41 * 550336261) + (j118 >>> 28);
        long j121 = j118 & M28L;
        long j122 = j78 + (j120 >>> 28);
        long j123 = j120 & M28L;
        long j124 = j87 + (j122 >>> 28);
        long j125 = j122 & M28L;
        long j126 = j124 & M26L;
        long j127 = (j124 >>> 26) - 1;
        long j128 = j97 - (j127 & 78101261);
        long j129 = (j99 - (j127 & 141809365)) + (j128 >> 28);
        long j130 = j128 & M28L;
        long j131 = (j101 - (j127 & 175155932)) + (j129 >> 28);
        long j132 = j129 & M28L;
        long j133 = (j103 - (j127 & 64542499)) + (j131 >> 28);
        long j134 = j131 & M28L;
        long j135 = (j105 - (j127 & 158326419)) + (j133 >> 28);
        long j136 = j133 & M28L;
        long j137 = (j107 - (j127 & 191173276)) + (j135 >> 28);
        long j138 = j135 & M28L;
        long j139 = (j109 - (j127 & 104575268)) + (j137 >> 28);
        long j140 = j137 & M28L;
        long j141 = (j111 - (j127 & 137584065)) + (j139 >> 28);
        long j142 = j139 & M28L;
        long j143 = j113 + (j141 >> 28);
        long j144 = j141 & M28L;
        long j145 = j115 + (j143 >> 28);
        long j146 = j143 & M28L;
        long j147 = j117 + (j145 >> 28);
        long j148 = j145 & M28L;
        long j149 = j119 + (j147 >> 28);
        long j150 = j147 & M28L;
        long j151 = j121 + (j149 >> 28);
        long j152 = j149 & M28L;
        long j153 = j123 + (j151 >> 28);
        long j154 = j151 & M28L;
        long j155 = j125 + (j153 >> 28);
        long j156 = j153 & M28L;
        long j157 = j126 + (j155 >> 28);
        long j158 = j155 & M28L;
        byte[] bArr2 = new byte[57];
        encode56((j132 << 28) | j130, bArr2, 0);
        encode56((j136 << 28) | j134, bArr2, 7);
        encode56(j138 | (j140 << 28), bArr2, 14);
        encode56(j142 | (j144 << 28), bArr2, 21);
        encode56(j146 | (j148 << 28), bArr2, 28);
        encode56(j150 | (j152 << 28), bArr2, 35);
        encode56(j154 | (j156 << 28), bArr2, 42);
        encode56((j157 << 28) | j158, bArr2, 49);
        return bArr2;
    }

    private static void scalarMult(byte[] bArr, PointExt pointExt, PointExt pointExt2) {
        int[] iArr = new int[14];
        decodeScalar(bArr, 0, iArr);
        Nat.shiftDownBits(14, iArr, 2, 0);
        Nat.cadd(14, (~iArr[0]) & 1, iArr, L, iArr);
        Nat.shiftDownBit(14, iArr, 1);
        int[] pointPrecompute = pointPrecompute(pointExt, 8);
        PointExt pointExt3 = new PointExt();
        pointLookup(iArr, Opcodes.DDIV, pointPrecompute, pointExt2);
        for (int i = Opcodes.FDIV; i >= 0; i--) {
            for (int i2 = 0; i2 < 4; i2++) {
                pointDouble(pointExt2);
            }
            pointLookup(iArr, i, pointPrecompute, pointExt3);
            pointAdd(pointExt3, pointExt2);
        }
        for (int i3 = 0; i3 < 2; i3++) {
            pointDouble(pointExt2);
        }
    }

    private static void scalarMultBase(byte[] bArr, PointExt pointExt) {
        precompute();
        int[] iArr = new int[15];
        decodeScalar(bArr, 0, iArr);
        iArr[14] = Nat.cadd(14, (~iArr[0]) & 1, iArr, L, iArr) + 4;
        Nat.shiftDownBit(15, iArr, 0);
        PointPrecomp pointPrecomp = new PointPrecomp();
        pointSetNeutral(pointExt);
        int i = 17;
        while (true) {
            int i2 = i;
            for (int i3 = 0; i3 < 5; i3++) {
                int i4 = 0;
                for (int i5 = 0; i5 < 5; i5++) {
                    i4 = (i4 & (~(1 << i5))) ^ ((iArr[i2 >>> 5] >>> (i2 & 31)) << i5);
                    i2 += 18;
                }
                int i6 = (i4 >>> 4) & 1;
                pointLookup(i3, ((-i6) ^ i4) & 15, pointPrecomp);
                F.cnegate(i6, pointPrecomp.x);
                pointAddPrecomp(pointPrecomp, pointExt);
            }
            i--;
            if (i < 0) {
                return;
            } else {
                pointDouble(pointExt);
            }
        }
    }

    private static void scalarMultBaseEncoded(byte[] bArr, byte[] bArr2, int i) {
        PointExt pointExt = new PointExt();
        scalarMultBase(bArr, pointExt);
        if (encodePoint(pointExt, bArr2, i) == 0) {
            throw new IllegalStateException();
        }
    }

    public static void scalarMultBaseXY(X448.Friend friend, byte[] bArr, int i, int[] iArr, int[] iArr2) {
        if (friend == null) {
            throw new NullPointerException("This method is only for use by X448");
        }
        byte[] bArr2 = new byte[57];
        pruneScalar(bArr, i, bArr2);
        PointExt pointExt = new PointExt();
        scalarMultBase(bArr2, pointExt);
        if (checkPoint(pointExt.x, pointExt.y, pointExt.z) == 0) {
            throw new IllegalStateException();
        }
        F.copy(pointExt.x, 0, iArr, 0);
        F.copy(pointExt.y, 0, iArr2, 0);
    }

    private static void scalarMultOrderVar(PointExt pointExt, PointExt pointExt2) {
        byte[] wnafVar = getWnafVar(L, 5);
        PointExt[] pointPrecomputeVar = pointPrecomputeVar(pointExt, 8);
        pointSetNeutral(pointExt2);
        int i = 446;
        while (true) {
            byte b = wnafVar[i];
            if (b != 0) {
                int i2 = b >> 31;
                pointAddVar(i2 != 0, pointPrecomputeVar[(b ^ i2) >>> 1], pointExt2);
            }
            i--;
            if (i < 0) {
                return;
            } else {
                pointDouble(pointExt2);
            }
        }
    }

    private static void scalarMultStrausVar(int[] iArr, int[] iArr2, PointExt pointExt, PointExt pointExt2) {
        precompute();
        byte[] wnafVar = getWnafVar(iArr, 7);
        byte[] wnafVar2 = getWnafVar(iArr2, 5);
        PointExt[] pointPrecomputeVar = pointPrecomputeVar(pointExt, 8);
        pointSetNeutral(pointExt2);
        int i = 446;
        while (true) {
            byte b = wnafVar[i];
            if (b != 0) {
                int i2 = b >> 31;
                pointAddVar(i2 != 0, precompBaseTable[(b ^ i2) >>> 1], pointExt2);
            }
            byte b2 = wnafVar2[i];
            if (b2 != 0) {
                int i3 = b2 >> 31;
                pointAddVar(i3 != 0, pointPrecomputeVar[(b2 ^ i3) >>> 1], pointExt2);
            }
            i--;
            if (i < 0) {
                return;
            } else {
                pointDouble(pointExt2);
            }
        }
    }

    public static void sign(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte[] bArr4, int i3, int i4, byte[] bArr5, int i5) {
        implSign(bArr, i, bArr2, i2, bArr3, (byte) 0, bArr4, i3, i4, bArr5, i5);
    }

    public static void sign(byte[] bArr, int i, byte[] bArr2, byte[] bArr3, int i2, int i3, byte[] bArr4, int i4) {
        implSign(bArr, i, bArr2, (byte) 0, bArr3, i2, i3, bArr4, i4);
    }

    public static void signPrehash(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, Xof xof, byte[] bArr4, int i3) {
        byte[] bArr5 = new byte[64];
        if (64 != xof.doFinal(bArr5, 0, 64)) {
            throw new IllegalArgumentException("ph");
        }
        implSign(bArr, i, bArr2, i2, bArr3, (byte) 1, bArr5, 0, 64, bArr4, i3);
    }

    public static void signPrehash(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte[] bArr4, int i3, byte[] bArr5, int i4) {
        implSign(bArr, i, bArr2, i2, bArr3, (byte) 1, bArr4, i3, 64, bArr5, i4);
    }

    public static void signPrehash(byte[] bArr, int i, byte[] bArr2, Xof xof, byte[] bArr3, int i2) {
        byte[] bArr4 = new byte[64];
        if (64 != xof.doFinal(bArr4, 0, 64)) {
            throw new IllegalArgumentException("ph");
        }
        implSign(bArr, i, bArr2, (byte) 1, bArr4, 0, 64, bArr3, i2);
    }

    public static void signPrehash(byte[] bArr, int i, byte[] bArr2, byte[] bArr3, int i2, byte[] bArr4, int i3) {
        implSign(bArr, i, bArr2, (byte) 1, bArr3, i2, 64, bArr4, i3);
    }

    public static boolean validatePublicKeyFull(byte[] bArr, int i) {
        PointExt pointExt = new PointExt();
        if (!decodePointVar(bArr, i, false, pointExt)) {
            return false;
        }
        F.normalize(pointExt.x);
        F.normalize(pointExt.y);
        F.normalize(pointExt.z);
        if (isNeutralElementVar(pointExt.x, pointExt.y, pointExt.z)) {
            return false;
        }
        PointExt pointExt2 = new PointExt();
        scalarMultOrderVar(pointExt, pointExt2);
        F.normalize(pointExt2.x);
        F.normalize(pointExt2.y);
        F.normalize(pointExt2.z);
        return isNeutralElementVar(pointExt2.x, pointExt2.y, pointExt2.z);
    }

    public static boolean validatePublicKeyPartial(byte[] bArr, int i) {
        return decodePointVar(bArr, i, false, new PointExt());
    }

    public static boolean verify(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte[] bArr4, int i3, int i4) {
        return implVerify(bArr, i, bArr2, i2, bArr3, (byte) 0, bArr4, i3, i4);
    }

    public static boolean verifyPrehash(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, Xof xof) {
        byte[] bArr4 = new byte[64];
        if (64 == xof.doFinal(bArr4, 0, 64)) {
            return implVerify(bArr, i, bArr2, i2, bArr3, (byte) 1, bArr4, 0, 64);
        }
        throw new IllegalArgumentException("ph");
    }

    public static boolean verifyPrehash(byte[] bArr, int i, byte[] bArr2, int i2, byte[] bArr3, byte[] bArr4, int i3) {
        return implVerify(bArr, i, bArr2, i2, bArr3, (byte) 1, bArr4, i3, 64);
    }
}
