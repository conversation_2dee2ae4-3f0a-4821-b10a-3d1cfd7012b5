package com.google.android.gms.tapandpay.issuer;

import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeResponseCallbacks.smali */
public interface IPushTokenizeResponseCallbacks extends IInterface {
    void onError(int i) throws RemoteException;

    void onPaymentCredentialsResponse(GetPaymentCredentialsResponse getPaymentCredentialsResponse) throws RemoteException;

    void onWalletAvailableResponse(boolean z) throws RemoteException;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeResponseCallbacks$Stub.smali */
    public static abstract class Stub extends com.google.android.gms.internal.tapandpay.zzb implements IPushTokenizeResponseCallbacks {

        /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\IPushTokenizeResponseCallbacks$Stub$Proxy.smali */
        public static class Proxy extends com.google.android.gms.internal.tapandpay.zza implements IPushTokenizeResponseCallbacks {
            Proxy(IBinder iBinder) {
                super(iBinder, "com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks");
            }

            @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks
            public void onError(int errorCode) throws RemoteException {
                Parcel zza = zza();
                zza.writeInt(errorCode);
                zzc(4, zza);
            }

            @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks
            public void onPaymentCredentialsResponse(GetPaymentCredentialsResponse response) throws RemoteException {
                Parcel zza = zza();
                com.google.android.gms.internal.tapandpay.zzc.zzc(zza, response);
                zzc(3, zza);
            }

            @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks
            public void onWalletAvailableResponse(boolean z) throws RemoteException {
                Parcel zza = zza();
                int i = com.google.android.gms.internal.tapandpay.zzc.zza;
                zza.writeInt(z ? 1 : 0);
                zzc(2, zza);
            }
        }

        public Stub() {
            super("com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks");
        }

        public static IPushTokenizeResponseCallbacks asInterface(IBinder obj) {
            if (obj == null) {
                return null;
            }
            IInterface queryLocalInterface = obj.queryLocalInterface("com.google.android.gms.tapandpay.issuer.IPushTokenizeResponseCallbacks");
            return queryLocalInterface instanceof IPushTokenizeResponseCallbacks ? (IPushTokenizeResponseCallbacks) queryLocalInterface : new Proxy(obj);
        }

        @Override // com.google.android.gms.internal.tapandpay.zzb
        protected boolean dispatchTransaction(int code, Parcel data, Parcel parcel, int i) throws RemoteException {
            switch (code) {
                case 2:
                    boolean zze = com.google.android.gms.internal.tapandpay.zzc.zze(data);
                    com.google.android.gms.internal.tapandpay.zzc.zzb(data);
                    onWalletAvailableResponse(zze);
                    return true;
                case 3:
                    GetPaymentCredentialsResponse getPaymentCredentialsResponse = (GetPaymentCredentialsResponse) com.google.android.gms.internal.tapandpay.zzc.zza(data, GetPaymentCredentialsResponse.CREATOR);
                    com.google.android.gms.internal.tapandpay.zzc.zzb(data);
                    onPaymentCredentialsResponse(getPaymentCredentialsResponse);
                    return true;
                case 4:
                    int readInt = data.readInt();
                    com.google.android.gms.internal.tapandpay.zzc.zzb(data);
                    onError(readInt);
                    return true;
                default:
                    return false;
            }
        }
    }
}
