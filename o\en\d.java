package o.en;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import kotlin.text.Typography;
import o.ee.g;
import o.eg.e;
import o.ei.i;
import o.ek.b;
import o.eo.h;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\en\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] c;
    private static long e;
    private static int f;
    private static int g;
    private boolean a;
    private final b d = new b();
    private final o.ex.d b = new o.ex.d();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        h();
        Gravity.getAbsoluteGravity(0, 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        TextUtils.getOffsetAfter("", 0);
        TextUtils.indexOf((CharSequence) "", '0');
        KeyEvent.keyCodeFromString("");
        TypedValue.complexToFloat(0);
        SystemClock.elapsedRealtimeNanos();
        TextUtils.indexOf("", "");
        TextUtils.lastIndexOf("", '0');
        ViewConfiguration.getScrollBarFadeDuration();
        ViewConfiguration.getTouchSlop();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        ViewConfiguration.getScrollDefaultDelay();
        ViewConfiguration.getFadingEdgeLength();
        PointF.length(0.0f, 0.0f);
        MotionEvent.axisFromString("");
        ViewConfiguration.getDoubleTapTimeout();
        AudioTrack.getMinVolume();
        int i = f + 35;
        g = i % 128;
        int i2 = i % 2;
    }

    static void h() {
        char[] cArr = new char[1447];
        ByteBuffer.wrap(",¯+x#a;á3«\nr\u0002>\u001að\u0012¾ibayyÉq\u0083HZ@\u0006XÈP\u0096¯J§\u0017¿Ó·\u0090\u008eZ\u0086b\u009e.\u0096óî£åuý!õÂÍ\u00adÄ{Ü5ÔÛ,\u008f+K#?;Õ3\u0094\n^\u0002\u001a\u001aÔ\u0012\u0080iRafy.qëI°[w\\¤ÚÔÝ&Õ{Í Åíü%ôbì\u0083äù\u009f,\u0097u\u008f\u0090\u0087Ò¾\u0000¶K®\u009ayï~+vin»fã_(WxOºGä<848,Ê$\u0082\u001d\u000f\u0015C\rË\u0005Æú\u0014òIê\u0092âßÛ\u0017Ó0Ë#Ã¼»è°;¨x §\u0098ã\u0091?\u0089~\u0081\u0095y\u0085~\u0013v[n\u0085fÛ_\u0011WWOÞG\u0087<X44,v$®\u001cü\u0015;\r\u007f\u0005»ý÷ò\u007fêjâ«ÚËÓ\u0000ËKÃ\u009e»Û°\u001e¨\\ \u0096, +d#&;ô3¬\ng\u00027\u001aõ\u0012«iwawy\u0085qÍHO@CXÔP\u0098¯C§\u0012¿Õ·\u009b\u008eB\u0086+\u009e>\u0096äî±åhý-õïÍ½ÄvÜ'Ô\u0089,\u0083+\\#@;Ö3\u0092\nT\u0002\u000e\u001aÔ\u0012\u0096i\u0017agy#q®I§@mX*Pñ, +d#&;ô3¬\ng\u00027\u001aõ\u0012«iwawy\u0085qÍHJ@\u0006XÇP\u008b¯C§\u000f¿Ä·\u0090\u008eR\u0086+\u009e<\u0096àî»åjý=õóÍªÄ3Ü&ÔÌ,\u0099+@#\u0015;×3\u0085\n^\u0002\u000f\u001a\u0091\u0012Èi\u0017WÓP\u0017XU@\u0087Hßq\u0014yDa\u0086iØ\u0012\u0004\u001a\u0004\u0002ö\n¾38;}#§+þÔ0Ü,Ä³Ìçõ<ý\u0015åZí\u009c\u0095Å\u009eT\u0086Y\u008e\u008b¶Þ¿\u000f§R¯¨WúP9X`@öH¯qhy|a¶iî\u00124\u001a\u000b\u0002W\n\u00932×;W#Z+\u009bÓÃÜ\u0000ÄCÌ\u0096ôëý6å|í¶,¹+k#6;í3 \nh\u0002/\u001a×\u0012´ika\u001ayÉq\u0083HO@\u0004XÁP\u008b,ª+k#=;ä3\u0095\nt\u00024\u001aú\u0012¸i~a2yîq\u0082H\\@\u000eXÅP\u008d¯l§\u001a¿Â·\u0086\u008e_\u0086d\u009e\"8\u001d?Ü, +d#&;ô3¬\ng\u00027\u001aõ\u0012«iwawy\u0085qÍH\\@\u0006XÐP\u008b¯S§\u001a¿Æ·\u0090\u008eR\u0086+\u009e/\u0096àî°åcýxõíÍ¬Ä|Ü2ÔÀ,\u0086+J#@;Ã3\u0089\nI\u0002\u0011\u001aÐ\u0012\u0086i\u0017a~y(qüI°@mX6Pô¨ÿ§y¿&·¶\u008f\u009e\u0086\\\u009eA\u0096Öî\u0088å\u0018ý\u0019õßÍ\u0087ÄQÜ)Ôg,¯$¶#`;43è\u000bµ\u0002~\u001a<\u0012·jÒa\r, +d#&;ô3¬\ng\u00027\u001aõ\u0012«iwawy\u0085qÍH\\@\u0006XÐP\u008b¯S§\u001a¿Æ·\u0090\u008eR\u0086+\u009e/\u0096àî°åcýxõíÍ¬Ä|Ü2ÔÀ,\u0086+J#@;Ã3\u0089\nI\u0002\u0011\u001aÐ\u0012\u0086i\u0017a~y(qüI°@mX6Pô¨ÿ§y¿&·¶\u008f\u0084\u0086N\u009e\u0012\u0096Íî\u008bå]ý\tõÛÍÓÄ\u0019Ü)Ô+,í$¯#w;23ò\u000b²\u0002v\u001ar\u0012þj\u0086aDy\u001aqÊI\u0085@UX\u0013PÅ¨\u0091§A¿\u001f·$\u008fâ\u0081e\u0086¡\u008eã\u00961\u009ei§¢¯ò·0¿nÄ¶ÌæÔ\u0004ÜGå\u0085í\u0086õ\u0004ýN\u0002\u008d\nÕ\u0012\u0007\u001a\u0010#É+î3ú;0ChH°PøX<`;iµqðy\u001e\u0081K\u0086Ê\u008eÕ\u0096\u0012\u009eL§\u0098¯Ð·\u0018¿RÄÒÌ«ÔçÜ9äkí õèý\u007f\u0005l\n°\u0012â\u001a \"G+\u00863Ê;GCKH\u008eP\u0098X\u0014`Ti\u0082q£yã\u0081/\u0089q\u008e¥, +d#&;ô3¬\ng\u00027\u001aõ\u0012«iwawy\u0085qÍH@@\fX\u0084P\u009a¯[§\r¿Ô·Õ\u008eF\u0086y\u009e#\u0096çî«åký=õ½Í¸Ä|Ü&ÔÄ,\u008b+[#@;Ó3\u0083\nI\u0002\u000f\u001aØ\u0012\u009diYa(y?qëI·@vX0Pÿ¨©§u¿1·¶\u008fÆ\u0086\f\u009e\u0000\u0096Àî\u0088åJý\tõ×Í\u009dÄSÜ)Ô#,á$©#q;/3ú\u000b°\u0002x\u001a(\u0012öj\u009caDy\u0001qÍTkS¯[íC?Kgr¬züb>j`\u0011¸\u0019è\u0001\n\tI0\u008b8\u0088 \n(@×\u0083ßÛÇ\tÏ\u001eöÇþàæéî%\u0096)\u009d¯\u0085ò\u008d$µq¼ø¤ï¬\u0010TNS\u0082[ÂC\u0002KHrÐzÑb\u0015jK\u0011\u0091\u0019¢\u0001ò\te1~8ª à(\"Ð}ß´ÇðÏ}÷Rþ\u0082æÞî\u001b\u0096E\u009d\u0096\u0085À\u008d\u0010µ\\,ª+k#=;ä3\u0088\ng\u00025\u001aý\u0012¶iwa%, +d#9;á3©\no\u0002?\u001a¼\u0012»iaa8yÆqÍHG@\rXÍP\u008d¯S§\u001e¿Ü·\u009c\u008eL\u0086b\u009e\"\u0096æîâåwý9õäÍ³ÄvÜ:ÔÝ,Ê+]#\u0005;Ö3\u0089\nN\u0002\u000e\u001aÒ\u0012\u0097iDa(ywq®,ª+k#=;ä3¶ Â§\u0004¯U·\u009f¿Ï\u0086\u001d\u008ef\u0096\u0083\u009eÝå\bíZõöý¾ÄpÌ~Ô»Üõ# +r3î;å\u0002'\n\u0011\u0012W\u001aßbÚi\u0016qSy\u008dAÄHMP\u0010X÷,\u0083+y# ;î3\u0080\n~\u00028\u001aù\u0012¡ifa>yÇq\u0083H\u000e@\u0014XÌP\u0090¯V§\u001a¿\u0090·\u0080\u008eF\u0086o\u009e-\u0096õî«åiý?õ½Í\u00adÄvÜ ÔÝ,\u0083+A#\u0007;Ö3Æ\n\u0016\u0002\\,»+o#\";ï3³\nc\u0002\u0018\u001aý\u0012£ivawy\u0085qÍHM@\u0002XÖP\u009d¯I§_¿Ù·\u0091\u008e\u0016\u00861\u009el,é+d# ;ô3å\n`\u00024\u001aé\u0012¿iváeæ«îãöoþ%ÇïÏ¶×~ß4¤ð¬¼´\u0004¼\u0011\u0085Ã\u008d\u0096\u0095E\u009d\u0010bØj\u0087r\u001cz\u0012CßKþS³,é+'#o;õ3µ\nb\u0002:\u001aè\u0012¸i|a0y\u0088q\u008eHO@\u0011XÀP\u008a,¼+z#+;á3±\nc\u0002\u0018\u001aý\u0012£ivawy\u0085qÍHM@\u0002XÖP\u009d¯I§_¿Ù·\u0091\u008e\u0016\u00861\u009el\u001cX\u001b\u0098\u0013Ñ\u000b\u0016\u0003{:\u009a2Ô*\u0005\"\fYÂQ\u008aI6Aqx¡púh*`$\u009f®\u0097æ\u008fm\u00872¾ë,¿+k##;é3¡\ng\u0002/\u001aù\u0012\u0092isa%yÌq¹HK@\u0011XÉP\u008a¯{§\u0011¿Ô·¶\u008eY\u0086e\u009e(\u0096èî¶åný7õóÍ\u00adÄ3ÜyÔ\u0089,\u0089+N#\u0012;Á3\u0095\n\u001b\u0002\u0015\u001aÕ\u0012Òi\ra(b»eim;uõ}±DyL>Tä\\¡'o/5\u0094º\u0093|\u009b-\u0083ç\u008b·²eº}¢·ª÷ÑwÙ#ÁËÉ\u008fðMø\u000bàÖè\u0096\u0017]\u001f\u0015\u0007Å\u000fÓ6^>b&..âVä]gE1Mîu¶|qdrl\u0095\u0094Ì\u009d¶\u009ay\u0092>\u008añ\u0082°»n³\u0013«ï£\u0089ØgÐ8ÈßÀÑù\u001fñ_éËá\u0080\u001eT\u0016\n\u000eÍ\u0006\u0085?C7m/5'ù_þTxL%Dó|¦u/m%eÔ\u009d\u0098\u009aR\u0092\u001b\u008aÜ\u0082\u0088»\u0007³Z«\u008d,ª+e#\";í3¬\nr\u0002\u000f\u001aó\u0012\u0095i{a$yÃqÍH\u0003@CXÁP\u0094¯J§\u000b¿É·Õ\u008eU\u0086j\u009e>\u0096åîâåjý9õóÍ¿ÄtÜ1ÔÛ¹\u0086¾I¶\u000e®Á¦\u0080\u009f^\u0097#\u008fß\u0087¹üWô\bìïäáÝ/ÕoÍûÅ°:d2:*ý\"µ\u001bs\u0013]\u000b\u0005\u0003É{Îp[h\u0015`ÈX\u009fQZI\u0016Añ¹æ¾h¶)®ð¦ê\u009fz\u00971\u008fó\u0087¿ü|ôAì\u0013ä\u0082ÜÕÕ\b¨K¯\u0084§Ã¿\f·M\u008e\u0093\u0086î\u009e\u0012\u0096tí\u009aåÅý\"õ,ÌâÄ¢Ü Ôu+«#ê;(34\n§\u0002\u008b\u001aÔ\u0012\rjFa\u0088yÍq\\IT@\u0097XÌPh¨f¯¯§ï¿%·`\u008e¿\u0086ï,ª+e#\";í3¬\nr\u0002\u000f\u001aó\u0012\u0095i{a$yÃqÍH\u0003@CXÁP\u0094¯J§\u000b¿É·Õ\u008eD\u0086n\u009e?\u0096îî·åuý;õøÍ\u00ad\u0081µ\u0086z\u008e=\u0096ò\u009e³§m¯\u0010·ì¿\u008aÄdÌ;ÔÜÜÒå\u001cí\\õÈý\u0083\u0002W\n\t\u0012Î\u001a\u0086#@+n36;úCýHrP4Xí`¯i,qqy\u0096,ª+e#\";í3¬\nr\u0002\u000f\u001aó\u0012\u0095i{a$yÃqÍH\u0003@CX×P\u008d¯U§\r¿Ù·\u009b\u008eQ\u0086+\u009e/\u0096èî²åoý=õïÍ»ÄwÜtÔÃ,\u0099+@#\u000e;\u00853\u0092\nT\u0002\\\u001aÕ\u0012\u009biDacjTm\u009beÜ}\u0013uRL\u008cDñ\\\rTk/\u0085'Ú?=73\u000eý\u0006½\u001e0\u0016té«áïùnñnÈ°À\u0096Ø×Ð\u000f¨H£\u0090»É³\r\u008b\u0000\u0082\u009a\u009aÂ\u0092>jxm´e¾}(u}L·Dë\\.T`/ '\u008c?Ú7\u001e\u000fZ\u0006Ú\u001e×\u0016\u0005îXá\u0083ùÎñ\u0006ÉaÀòØôÐ9¨`£æ»î³!\u008bc\u0082«\u009a\u0090\u0092Ñj\u0003b\u001eeÁ}\u0098,¨+x#*;Ë3 \n\u007f\u0002(\u001aÑ\u0012¸iaa$yÁq\u0083HI@CX\u0089PÙ¯N§\r¿Å·\u0090ÖnÑ¾ÙìÁ\rÉfð¹øîà\u0017è~\u0093§\u009bâ\u0083\u0007\u008bE²\u008fº\u0085¢Oª\u001fU\u009a]ØE\u001aM@t\u0095,¯+f#:;ó3\u00ad\n&\u00028\u001aý\u0012£iva$y\u0088q\u008bH\\@\fXÉPÙ¯W§\u001a¿Ý·\u009a\u008eD\u0086r".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1447);
        c = cArr;
        e = -5456595630265324790L;
    }

    static void init$0() {
        $$a = new byte[]{3, 85, -79, -50};
        $$b = 97;
    }

    private static void l(int i, int i2, byte b, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = 4 - (i * 2);
        int i4 = 105 - b;
        int i5 = (i2 * 3) + 1;
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            i4 = i7 + i3;
            i3++;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
        }
        while (true) {
            int i8 = i6 + 1;
            bArr2[i8] = (byte) i4;
            if (i8 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i4 += bArr[i3];
            i3++;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i8;
        }
    }

    public final void c(Context context) throws i {
        o.ex.d dVar;
        e s;
        int i = f + 51;
        g = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k((char) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 63053), Process.getGidForName("") + 50, (ViewConfiguration.getEdgeSlop() >> 16) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), TextUtils.indexOf((CharSequence) "", '0', 0) + 1, 47 - View.resolveSizeAndState(0, 0, 0), objArr2);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr2[0]).intern(), 0);
        Object[] objArr3 = new Object[1];
        k((char) (30685 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 48, 2 - TextUtils.getOffsetBefore("", 0), objArr3);
        String string = sharedPreferences.getString(((String) objArr3[0]).intern(), "");
        if (string.isEmpty()) {
            g.c();
            Object[] objArr4 = new Object[1];
            k((char) (21839 - Color.argb(0, 0, 0, 0)), 65 - (ViewConfiguration.getFadingEdgeLength() >> 16), (ViewConfiguration.getTapTimeout() >> 16) + 62, objArr4);
            g.d(intern, ((String) objArr4[0]).intern());
            return;
        }
        g.c();
        Object[] objArr5 = new Object[1];
        k((char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), 126 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 50, objArr5);
        g.d(intern, ((String) objArr5[0]).intern());
        String d = new o.dd.e(context).d(string);
        g.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr6 = new Object[1];
        k((char) View.resolveSizeAndState(0, 0, 0), Process.getGidForName("") + Opcodes.GETSTATIC, (-16777173) - Color.rgb(0, 0, 0), objArr6);
        g.d(intern, sb.append(((String) objArr6[0]).intern()).append(d).toString());
        if (d != null) {
            int i3 = g + 73;
            f = i3 % 128;
            int i4 = i3 % 2;
            if (!d.isEmpty()) {
                try {
                    o.eg.b bVar = new o.eg.b(d);
                    Object[] objArr7 = new Object[1];
                    k((char) View.MeasureSpec.getMode(0), TextUtils.indexOf("", "", 0, 0) + 278, TextUtils.lastIndexOf("", '0', 0, 0) + 18, objArr7);
                    if (bVar.b(((String) objArr7[0]).intern())) {
                        Object[] objArr8 = new Object[1];
                        k((char) Color.argb(0, 0, 0, 0), 296 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 24 - TextUtils.getCapsMode("", 0, 0), objArr8);
                        if (!bVar.b(((String) objArr8[0]).intern())) {
                            g.c();
                            Object[] objArr9 = new Object[1];
                            k((char) (AndroidCharacter.getMirror('0') - '0'), 555 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 79, objArr9);
                            g.e(intern, ((String) objArr9[0]).intern());
                            this.a = true;
                            Object[] objArr10 = new Object[1];
                            k((char) (ExpandableListView.getPackedPositionChild(0L) + 30924), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 633, 62 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr10);
                            throw new i(((String) objArr10[0]).intern());
                        }
                        Object[] objArr11 = new Object[1];
                        k((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 294 - Process.getGidForName(""), 23 - TextUtils.lastIndexOf("", '0'), objArr11);
                        String r = bVar.r(((String) objArr11[0]).intern());
                        Object[] objArr12 = new Object[1];
                        k((char) (5348 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), MotionEvent.axisFromString("") + 320, 2 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr12);
                        if (!r.equals(((String) objArr12[0]).intern())) {
                            g.c();
                            Object[] objArr13 = new Object[1];
                            k((char) Color.argb(0, 0, 0, 0), (ViewConfiguration.getWindowTouchSlop() >> 8) + 398, 88 - TextUtils.getTrimmedLength(""), objArr13);
                            g.e(intern, ((String) objArr13[0]).intern());
                            this.a = true;
                            Object[] objArr14 = new Object[1];
                            k((char) (44484 - TextUtils.lastIndexOf("", '0')), 486 - KeyEvent.getDeadChar(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 70, objArr14);
                            throw new i(((String) objArr14[0]).intern());
                        }
                        g.c();
                        StringBuilder sb2 = new StringBuilder();
                        Object[] objArr15 = new Object[1];
                        k((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), ImageFormat.getBitsPerPixel(0) + 322, TextUtils.getOffsetAfter("", 0) + 77, objArr15);
                        g.d(intern, sb2.append(((String) objArr15[0]).intern()).append(r).toString());
                        int i5 = g + Opcodes.LNEG;
                        f = i5 % 128;
                        switch (i5 % 2 == 0) {
                            case false:
                                dVar = this.b;
                                Object[] objArr16 = new Object[1];
                                k((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) * 0), 14 << TextUtils.indexOf((CharSequence) "", 'M', 0), 44 >>> Color.blue(1), objArr16);
                                s = bVar.s(((String) objArr16[0]).intern());
                                break;
                            default:
                                dVar = this.b;
                                Object[] objArr17 = new Object[1];
                                k((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 277 - TextUtils.indexOf((CharSequence) "", '0', 0), Color.blue(0) + 17, objArr17);
                                s = bVar.s(((String) objArr17[0]).intern());
                                break;
                        }
                        dVar.a(s);
                    }
                    Object[] objArr18 = new Object[1];
                    k((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), 697 - ExpandableListView.getPackedPositionGroup(0L), TextUtils.indexOf("", "", 0, 0) + 11, objArr18);
                    switch (bVar.b(((String) objArr18[0]).intern())) {
                        case true:
                            Object[] objArr19 = new Object[1];
                            k((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 696, View.MeasureSpec.getMode(0) + 11, objArr19);
                            Object e2 = bVar.e(((String) objArr19[0]).intern());
                            if (e2 instanceof o.eg.b) {
                                this.d.c((o.eg.b) e2);
                                return;
                            } else {
                                if (e2 instanceof e) {
                                    this.d.c((e) e2);
                                    return;
                                }
                                return;
                            }
                        default:
                            return;
                    }
                } catch (o.eg.d e3) {
                    StringBuilder sb3 = new StringBuilder();
                    Object[] objArr20 = new Object[1];
                    k((char) Color.alpha(0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 708, 46 - TextUtils.indexOf("", "", 0, 0), objArr20);
                    throw new i(sb3.append(((String) objArr20[0]).intern()).append(e3.getMessage()).toString());
                }
            }
        }
        g.c();
        Object[] objArr21 = new Object[1];
        k((char) (31603 - View.getDefaultSize(0, 0)), 220 - (ViewConfiguration.getEdgeSlop() >> 16), (KeyEvent.getMaxKeyCode() >> 16) + 58, objArr21);
        g.d(intern, ((String) objArr21[0]).intern());
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:16:0x00fc. Please report as an issue. */
    public final o.fk.b e(Context context, o.eg.b bVar) throws i {
        o.fk.b bVar2;
        int i = f + Opcodes.DREM;
        g = i % 128;
        int i2 = i % 2;
        try {
            Object[] objArr = new Object[1];
            k((char) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getScrollBarSize() >> 8) + 754, 5 - ExpandableListView.getPackedPositionGroup(0L), objArr);
            if (bVar.b(((String) objArr[0]).intern())) {
                Object[] objArr2 = new Object[1];
                k((char) Gravity.getAbsoluteGravity(0, 0), ImageFormat.getBitsPerPixel(0) + 755, '5' - AndroidCharacter.getMirror('0'), objArr2);
                e s = bVar.s(((String) objArr2[0]).intern());
                g.c();
                Object[] objArr3 = new Object[1];
                k((char) (63053 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), 48 - ImageFormat.getBitsPerPixel(0), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 16, objArr3);
                String intern = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                k((char) (35966 - KeyEvent.keyCodeFromString("")), 758 - TextUtils.indexOf((CharSequence) "", '0', 0), KeyEvent.getDeadChar(0, 0) + 33, objArr4);
                g.d(intern, sb.append(((String) objArr4[0]).intern()).append(s).toString());
                bVar2 = this.d.a(context, b.d(s), this.b);
            } else {
                bVar2 = new o.fk.b();
            }
            if (this.a) {
                int i3 = f + 39;
                int i4 = i3 % 128;
                g = i4;
                switch (i3 % 2 == 0 ? 'H' : '%') {
                    case '%':
                        this.a = false;
                        break;
                    default:
                        this.a = true;
                        break;
                }
                int i5 = i4 + 23;
                f = i5 % 128;
                switch (i5 % 2 != 0 ? 'F' : '`') {
                }
            }
            return bVar2;
        } catch (o.eg.d e2) {
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr5 = new Object[1];
            k((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), 792 - View.getDefaultSize(0, 0), 39 - ExpandableListView.getPackedPositionChild(0L), objArr5);
            throw new i(sb2.append(((String) objArr5[0]).intern()).append(e2.getMessage()).toString());
        }
    }

    public final o.fk.b d(Context context, String str) {
        o.fk.b bVar = new o.fk.b();
        o.eo.e eVar = this.d.c().get(str);
        if (eVar == null) {
            g.c();
            Object[] objArr = new Object[1];
            k((char) (Color.alpha(0) + 63053), AndroidCharacter.getMirror('0') + 1, 16 - TextUtils.indexOf("", "", 0, 0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) (ViewConfiguration.getTouchSlop() >> 8), 832 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), View.getDefaultSize(0, 0) + 24, objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            k((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 855, TextUtils.lastIndexOf("", '0', 0, 0) + 11, objArr3);
            g.e(intern, append.append(((String) objArr3[0]).intern()).toString());
            int i = f + 1;
            g = i % 128;
            int i2 = i % 2;
            return bVar;
        }
        g.c();
        Object[] objArr4 = new Object[1];
        k((char) (63052 - ImageFormat.getBitsPerPixel(0)), Drawable.resolveOpacity(0, 0) + 49, 16 - TextUtils.indexOf("", "", 0, 0), objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr5 = new Object[1];
        k((char) ExpandableListView.getPackedPositionGroup(0L), 832 - TextUtils.getOffsetAfter("", 0), View.combineMeasuredStates(0, 0) + 24, objArr5);
        StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(str);
        Object[] objArr6 = new Object[1];
        k((char) (52619 - ImageFormat.getBitsPerPixel(0)), TextUtils.lastIndexOf("", '0', 0) + 867, 24 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr6);
        g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
        this.b.e(new o.dd.e(context), str);
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>();
        o.eo.e b = eVar.b();
        linkedHashMap.put(b.e(), b);
        g.c();
        Object[] objArr7 = new Object[1];
        k((char) (63053 - Color.blue(0)), 'a' - AndroidCharacter.getMirror('0'), (ViewConfiguration.getTouchSlop() >> 8) + 16, objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr8 = new Object[1];
        k((char) KeyEvent.keyCodeFromString(""), 832 - (ViewConfiguration.getTouchSlop() >> 8), 23 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr8);
        StringBuilder append3 = sb3.append(((String) objArr8[0]).intern()).append(str);
        Object[] objArr9 = new Object[1];
        k((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (ViewConfiguration.getLongPressTimeout() >> 16) + 890, 18 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr9);
        g.d(intern3, append3.append(((String) objArr9[0]).intern()).toString());
        o.fk.b a = this.d.a(context, linkedHashMap, this.b);
        int i3 = g + Opcodes.DSUB;
        f = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    public final o.fk.b e(Context context, o.eo.e eVar) {
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>((Map<? extends String, ? extends o.eo.e>) Collections.singletonMap(eVar.e(), eVar));
        g.c();
        Object[] objArr = new Object[1];
        k((char) (63053 - (KeyEvent.getMaxKeyCode() >> 16)), ExpandableListView.getPackedPositionChild(0L) + 50, 16 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 907 - ExpandableListView.getPackedPositionGroup(0L), KeyEvent.getDeadChar(0, 0) + 24, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(eVar.e());
        Object[] objArr3 = new Object[1];
        k((char) (ViewConfiguration.getScrollBarSize() >> 8), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 890, TextUtils.lastIndexOf("", '0') + 18, objArr3);
        g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
        o.fk.b a = this.d.a(context, linkedHashMap, this.b);
        int i = g + Opcodes.LNEG;
        f = i % 128;
        int i2 = i % 2;
        return a;
    }

    public final o.fk.b b(Context context, String str) {
        o.fk.b bVar = new o.fk.b();
        o.eo.e eVar = this.d.c().get(str);
        if (eVar == null) {
            g.c();
            Object[] objArr = new Object[1];
            k((char) (View.getDefaultSize(0, 0) + 63053), 49 - TextUtils.indexOf("", "", 0, 0), (Process.myTid() >> 22) + 16, objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) (View.getDefaultSize(0, 0) + 12541), 930 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), TextUtils.lastIndexOf("", '0') + 23, objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            k((char) TextUtils.getOffsetAfter("", 0), 856 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 9 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
            g.e(intern, append.append(((String) objArr3[0]).intern()).toString());
            int i = f + Opcodes.DDIV;
            g = i % 128;
            int i2 = i % 2;
            return bVar;
        }
        g.c();
        Object[] objArr4 = new Object[1];
        k((char) (63052 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 50 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), 16 - KeyEvent.getDeadChar(0, 0), objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr5 = new Object[1];
        k((char) (12541 - Color.alpha(0)), KeyEvent.keyCodeFromString("") + 931, 22 - Drawable.resolveOpacity(0, 0), objArr5);
        StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(str);
        Object[] objArr6 = new Object[1];
        k((char) (TextUtils.lastIndexOf("", '0') + 52621), 866 - TextUtils.indexOf("", "", 0, 0), AndroidCharacter.getMirror('0') - 24, objArr6);
        g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
        this.b.e(new o.dd.e(context), str);
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>();
        o.eo.e c2 = eVar.c();
        linkedHashMap.put(c2.e(), c2);
        g.c();
        Object[] objArr7 = new Object[1];
        k((char) (63052 - ((byte) KeyEvent.getModifierMetaStateMask())), View.combineMeasuredStates(0, 0) + 49, View.MeasureSpec.getSize(0) + 16, objArr7);
        String intern3 = ((String) objArr7[0]).intern();
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr8 = new Object[1];
        k((char) (12540 - TextUtils.indexOf((CharSequence) "", '0')), 931 - (ViewConfiguration.getScrollDefaultDelay() >> 16), Gravity.getAbsoluteGravity(0, 0) + 22, objArr8);
        StringBuilder append3 = sb3.append(((String) objArr8[0]).intern()).append(str);
        Object[] objArr9 = new Object[1];
        k((char) View.MeasureSpec.makeMeasureSpec(0, 0), 891 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (Process.myPid() >> 22) + 17, objArr9);
        g.d(intern3, append3.append(((String) objArr9[0]).intern()).toString());
        o.fk.b a = this.d.a(context, linkedHashMap, this.b);
        int i3 = g + 71;
        f = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return a;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final o.fk.b a(Context context, String str) {
        o.fk.b bVar = new o.fk.b();
        o.eo.e eVar = this.d.c().get(str);
        if (eVar == null) {
            g.c();
            Object[] objArr = new Object[1];
            k((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 63053), ((Process.getThreadPriority(0) + 20) >> 6) + 49, 16 - Color.red(0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            k((char) Drawable.resolveOpacity(0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 953, ((byte) KeyEvent.getModifierMetaStateMask()) + 45, objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            k((char) (ViewConfiguration.getTouchSlop() >> 8), 856 - (ViewConfiguration.getDoubleTapTimeout() >> 16), View.getDefaultSize(0, 0) + 10, objArr3);
            g.e(intern, append.append(((String) objArr3[0]).intern()).toString());
            int i = g + 59;
            f = i % 128;
            int i2 = i % 2;
            return bVar;
        }
        g.c();
        Object[] objArr4 = new Object[1];
        k((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 63053), 48 - ImageFormat.getBitsPerPixel(0), 16 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr5 = new Object[1];
        k((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 953 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 43, objArr5);
        StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(str);
        Object[] objArr6 = new Object[1];
        k((char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), TextUtils.indexOf("", "", 0, 0) + 890, TextUtils.lastIndexOf("", '0', 0, 0) + 18, objArr6);
        g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
        LinkedHashMap<String, o.eo.e> linkedHashMap = new LinkedHashMap<>();
        o.eo.e d = eVar.d();
        linkedHashMap.put(d.e(), d);
        this.d.c(eVar, h.a);
        return this.d.a(context, linkedHashMap, this.b);
    }

    public final List<String> c(Context context, o.eg.b bVar) throws i {
        try {
            List<String> arrayList = new ArrayList<>();
            Object[] objArr = new Object[1];
            k((char) (19986 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 997 - Color.alpha(0), 12 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
            if (bVar.b(((String) objArr[0]).intern())) {
                Object[] objArr2 = new Object[1];
                k((char) (19985 - (ViewConfiguration.getLongPressTimeout() >> 16)), 997 - View.MeasureSpec.getSize(0), 11 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr2);
                e s = bVar.s(((String) objArr2[0]).intern());
                g.c();
                Object[] objArr3 = new Object[1];
                k((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 63053), 48 - TextUtils.lastIndexOf("", '0', 0, 0), 16 - Color.alpha(0), objArr3);
                String intern = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                k((char) (47110 - View.getDefaultSize(0, 0)), 1009 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), View.resolveSize(0, 0) + 34, objArr4);
                g.d(intern, sb.append(((String) objArr4[0]).intern()).append(s).toString());
                arrayList = this.b.c(context, s);
            }
            int i = f + 93;
            g = i % 128;
            switch (i % 2 == 0 ? 'Y' : 'L') {
                case Opcodes.DUP /* 89 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return arrayList;
            }
        } catch (o.eg.d e2) {
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr5 = new Object[1];
            k((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), TextUtils.lastIndexOf("", '0', 0, 0) + 793, 40 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr5);
            throw new i(sb2.append(((String) objArr5[0]).intern()).append(e2.getMessage()).toString());
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x00e2. Please report as an issue. */
    public final void a(Context context) {
        Object[] objArr = new Object[1];
        k((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 63053), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 49, 16 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (ViewConfiguration.getJumpTapTimeout() >> 16), 1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), Color.blue(0) + 47, objArr2);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr2[0]).intern(), 0);
        try {
            o.eg.b bVar = new o.eg.b();
            o.eg.b d = this.d.d();
            if (d.d() != 0) {
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                k((char) (45340 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), 1042 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 41 - Color.argb(0, 0, 0, 0), objArr3);
                g.d(intern, sb.append(((String) objArr3[0]).intern()).append(d.b()).toString());
                Object[] objArr4 = new Object[1];
                k((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 697 - TextUtils.indexOf("", "", 0, 0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 10, objArr4);
                bVar.d(((String) objArr4[0]).intern(), d);
                int i = g + 85;
                f = i % 128;
                switch (i % 2 != 0) {
                }
            } else {
                g.c();
                Object[] objArr5 = new Object[1];
                k((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 1082, (ViewConfiguration.getTapTimeout() >> 16) + 33, objArr5);
                g.d(intern, ((String) objArr5[0]).intern());
            }
            e a = this.b.a();
            if (a.d() != 0) {
                g.c();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr6 = new Object[1];
                k((char) (38189 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), (ViewConfiguration.getWindowTouchSlop() >> 8) + 1116, 49 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr6);
                g.d(intern, sb2.append(((String) objArr6[0]).intern()).append(a.a()).toString());
                Object[] objArr7 = new Object[1];
                k((char) (ViewConfiguration.getScrollBarSize() >> 8), ImageFormat.getBitsPerPixel(0) + 279, 18 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr7);
                bVar.d(((String) objArr7[0]).intern(), a);
            } else {
                g.c();
                Object[] objArr8 = new Object[1];
                k((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 34017), 1164 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (Process.myTid() >> 22) + 40, objArr8);
                g.d(intern, ((String) objArr8[0]).intern());
            }
            switch (bVar.d() == 0 ? 'S' : 'C') {
                case Opcodes.AASTORE /* 83 */:
                    int i2 = f + 5;
                    g = i2 % 128;
                    int i3 = i2 % 2;
                    g.c();
                    Object[] objArr9 = new Object[1];
                    k((char) TextUtils.getCapsMode("", 0, 0), 1204 - View.resolveSize(0, 0), TextUtils.lastIndexOf("", '0', 0) + 31, objArr9);
                    g.d(intern, ((String) objArr9[0]).intern());
                    SharedPreferences.Editor edit = sharedPreferences.edit();
                    Object[] objArr10 = new Object[1];
                    k((char) (30685 - TextUtils.indexOf("", "")), (KeyEvent.getMaxKeyCode() >> 16) + 47, MotionEvent.axisFromString("") + 3, objArr10);
                    edit.putString(((String) objArr10[0]).intern(), "").commit();
                    break;
                default:
                    Object[] objArr11 = new Object[1];
                    k((char) ((-1) - Process.getGidForName("")), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 295, 24 - (ViewConfiguration.getScrollBarSize() >> 8), objArr11);
                    String intern2 = ((String) objArr11[0]).intern();
                    Object[] objArr12 = new Object[1];
                    k((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 5347), 319 - KeyEvent.normalizeMetaState(0), (ViewConfiguration.getWindowTouchSlop() >> 8) + 2, objArr12);
                    bVar.d(intern2, ((String) objArr12[0]).intern());
                    g.c();
                    StringBuilder sb3 = new StringBuilder();
                    Object[] objArr13 = new Object[1];
                    k((char) (44319 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), 1234 - TextUtils.getOffsetBefore("", 0), View.resolveSize(0, 0) + 33, objArr13);
                    g.d(intern, sb3.append(((String) objArr13[0]).intern()).append(bVar.b()).toString());
                    String a2 = new o.dd.e(context).a(bVar.b());
                    g.c();
                    Object[] objArr14 = new Object[1];
                    k((char) View.MeasureSpec.getMode(0), Color.argb(0, 0, 0, 0) + 1267, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 43, objArr14);
                    g.d(intern, ((String) objArr14[0]).intern());
                    SharedPreferences.Editor edit2 = sharedPreferences.edit();
                    Object[] objArr15 = new Object[1];
                    k((char) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 30685), Color.alpha(0) + 47, 2 - Color.blue(0), objArr15);
                    edit2.putString(((String) objArr15[0]).intern(), a2).commit();
                    break;
            }
        } catch (o.eg.d e2) {
            g.c();
            StringBuilder sb4 = new StringBuilder();
            Object[] objArr16 = new Object[1];
            k((char) (TextUtils.getOffsetBefore("", 0) + 18174), 1312 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 71 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr16);
            g.d(intern, sb4.append(((String) objArr16[0]).intern()).append(e2.getMessage()).toString());
            SharedPreferences.Editor edit3 = sharedPreferences.edit();
            Object[] objArr17 = new Object[1];
            k((char) (30685 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), 47 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), 2 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr17);
            edit3.putString(((String) objArr17[0]).intern(), "").commit();
        }
    }

    public final o.el.d d(String str) {
        int i = f + 65;
        g = i % 128;
        int i2 = i % 2;
        o.el.d b = this.d.b(str);
        int i3 = f + 67;
        g = i3 % 128;
        int i4 = i3 % 2;
        return b;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean a() {
        /*
            Method dump skipped, instructions count: 426
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.en.d.a():boolean");
    }

    public final void c() {
        int i = f + 81;
        g = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((char) (63054 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 49 - KeyEvent.getDeadChar(0, 0), 16 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 1424 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 23 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.d.e();
        int i3 = g + 35;
        f = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                int i4 = 83 / 0;
                return;
        }
    }

    public final void d() {
        int i = f + 49;
        g = i % 128;
        int i2 = i % 2;
        this.d.a();
        int i3 = g + 61;
        f = i3 % 128;
        switch (i3 % 2 != 0 ? 'U' : (char) 26) {
            case 26:
                return;
            default:
                int i4 = 77 / 0;
                return;
        }
    }

    public final void b() {
        int i = f + Opcodes.DMUL;
        g = i % 128;
        switch (i % 2 == 0 ? 'Y' : Typography.greater) {
            case Opcodes.DUP /* 89 */:
                this.d.h();
                int i2 = 44 / 0;
                return;
            default:
                this.d.h();
                return;
        }
    }

    public final o.eg.b e() throws o.eg.d {
        int i = f + 41;
        g = i % 128;
        int i2 = i % 2;
        if (!this.a) {
            return this.b.a(this.d);
        }
        o.eg.b bVar = new o.eg.b();
        int i3 = g + 49;
        f = i3 % 128;
        int i4 = i3 % 2;
        return bVar;
    }

    public final LinkedHashMap<String, o.eo.e> j() {
        boolean z;
        int i = g + 97;
        f = i % 128;
        switch (i % 2 != 0 ? (char) 26 : (char) 28) {
            case 26:
                z = true;
                break;
            default:
                z = false;
                break;
        }
        return c(z);
    }

    public final LinkedHashMap<String, o.eo.e> c(boolean z) {
        int i = g;
        int i2 = i + Opcodes.DNEG;
        f = i2 % 128;
        int i3 = i2 % 2;
        switch (z ? (char) 22 : '?') {
            case 22:
                int i4 = i + 37;
                f = i4 % 128;
                switch (i4 % 2 == 0) {
                    case false:
                        this.d.b();
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        LinkedHashMap<String, o.eo.e> b = this.d.b();
                        int i5 = g + 7;
                        f = i5 % 128;
                        int i6 = i5 % 2;
                        return b;
                }
            default:
                return this.d.c();
        }
    }

    public final o.ex.d f() {
        int i = g;
        int i2 = i + 27;
        f = i2 % 128;
        int i3 = i2 % 2;
        o.ex.d dVar = this.b;
        int i4 = i + 43;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return dVar;
        }
    }

    public final b i() {
        int i = f + 59;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        b bVar = this.d;
        int i4 = i2 + 25;
        f = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 996
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.en.d.k(char, int, int, java.lang.Object[]):void");
    }
}
