package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v0.smali */
public class v0 implements e {
    private g0 b;
    private l1 x;

    v0(g0 g0Var) {
        this.b = g0Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return a(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public InputStream b() throws IOException {
        l1 l1Var = new l1(this.b, false);
        this.x = l1Var;
        return l1Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public int d() {
        return this.x.b();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0("IOException converting stream to byte array: " + e.getMessage(), e);
        }
    }

    static u0 a(g0 g0Var) throws IOException {
        l1 l1Var = new l1(g0Var, false);
        return new u0(n7.a(l1Var), l1Var.b());
    }
}
