package com.grupoavaloc1.bancamovil;

import android.content.Intent;
import android.os.Bundle;
import android.webkit.WebView;
import com.avaldigitallabs.mbocc.wallet.DigitalEnrollCallback;
import com.avaldigitallabs.mbocc.wallet.DigitalWalletPlugin;
import com.avaldigitallabs.onespan.binding.OnespanBinding;
import com.avaldigitallabs.onespan.digipass.OneSpanDigipass;
import com.avaldigitallabs.onespan.securestorage.OneSpanSecureStorage;
import com.getcapacitor.BridgeActivity;
import com.rolster.capacitor.biometric.NativeBiometricPlugin;
import com.rolster.capacitor.contacts.ContactsPlugin;
import com.rolster.capacitor.device.DeviceManagerPlugin;
import com.rolster.capacitor.otp.OtpManagerPlugin;
import com.rolster.capacitor.review.AppReviewPlugin;
import com.rolster.capacitor.scanner.BarcodeScannerPlugin;
import com.rolster.capacitor.update.UpdateManagerPlugin;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes16\com\grupoavaloc1\bancamovil\MainActivity.smali */
public class MainActivity extends BridgeActivity {
    @Override // com.getcapacitor.BridgeActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    public void onCreate(Bundle savedInstanceState) {
        registerPlugin(OnespanBinding.class);
        registerPlugin(OneSpanDigipass.class);
        registerPlugin(OneSpanSecureStorage.class);
        registerPlugin(AppReviewPlugin.class);
        registerPlugin(BarcodeScannerPlugin.class);
        registerPlugin(ContactsPlugin.class);
        registerPlugin(DeviceManagerPlugin.class);
        registerPlugin(NativeBiometricPlugin.class);
        registerPlugin(OtpManagerPlugin.class);
        registerPlugin(UpdateManagerPlugin.class);
        registerPlugin(DigitalWalletPlugin.class);
        super.onCreate(savedInstanceState);
    }

    @Override // com.getcapacitor.BridgeActivity, androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, android.app.Activity
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 50) {
            DigitalEnrollCallback.runPushCardResult(requestCode, resultCode, data);
        }
    }

    @Override // com.getcapacitor.BridgeActivity, androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    public void onDestroy() {
        WebView webView = getBridge().getWebView();
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }
}
