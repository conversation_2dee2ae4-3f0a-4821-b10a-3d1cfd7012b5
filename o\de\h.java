package o.de;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Date;
import kotlin.text.Typography;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\h.smali */
public final class h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long c;
    private static char d;
    private static int e;
    private static int h;
    private static int i;
    private b a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        a();
        View.resolveSizeAndState(0, 0, 0);
        ViewConfiguration.getDoubleTapTimeout();
        SystemClock.uptimeMillis();
        SystemClock.currentThreadTimeMillis();
        View.MeasureSpec.makeMeasureSpec(0, 0);
        ViewConfiguration.getKeyRepeatDelay();
        ViewConfiguration.getMaximumDrawingCacheSize();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        View.MeasureSpec.makeMeasureSpec(0, 0);
        TextUtils.indexOf((CharSequence) "", '0', 0);
        TextUtils.indexOf("", "", 0);
        TypedValue.complexToFloat(0);
        View.MeasureSpec.getMode(0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        TextUtils.getOffsetBefore("", 0);
        KeyEvent.normalizeMetaState(0);
        View.combineMeasuredStates(0, 0);
        int i2 = h + 97;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void a() {
        e = 874635307;
        d = (char) 17957;
        b = 1677116708;
        c = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{66, 0, -113, Tnaf.POW_2_WIDTH};
        $$b = 9;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r6 = r6 + 99
            byte[] r0 = o.de.h.$$a
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r6 = r6 + 1
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.h.j(int, short, byte, java.lang.Object[]):void");
    }

    public final void c(Context context, b bVar) {
        int i2 = i + 7;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                this.a = bVar;
                e(bVar);
                throw null;
            default:
                this.a = bVar;
                String e2 = e(bVar);
                if (e2 != null) {
                    g.c();
                    Object[] objArr = new Object[1];
                    f(TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 12, "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", 19 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + Opcodes.INVOKESPECIAL, false, objArr);
                    String intern = ((String) objArr[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr2 = new Object[1];
                    g(Gravity.getAbsoluteGravity(0, 0), "\ue143世믊Ἵⴿ㭢▘晓뒡蹞誡蓁\ue65b㬨༼良\ue14cຝ㦷伝䨣\u0cc5䩭翏ဪ菏\ueac6槊\udab4奭ꈢ嗻밪븕\uec6a킛ᛠ⯱懮仆豤汒躎\u0ba1᪵嗤\u07b2䧶䒊ꤥ늯ꔌ몾섾Ꞑ歅", (char) ((ViewConfiguration.getJumpTapTimeout() >> 16) + 64976), "\uf058㈌큱\uf5fd", "\u0000\u0000\u0000\u0000", objArr2);
                    g.d(intern, sb.append(((String) objArr2[0]).intern()).append(e2).toString());
                    Object[] objArr3 = new Object[1];
                    f(View.resolveSizeAndState(0, 0, 0) + 7, "\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 47, (-16777029) - Color.rgb(0, 0, 0), false, objArr3);
                    SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
                    Object[] objArr4 = new Object[1];
                    g(TextUtils.lastIndexOf("", '0', 0, 0) + 1, "扇⚗\ue519喂涎ꏛ팪繖", (char) (AndroidCharacter.getMirror('0') - '0'), "精센ິٻ", "\u0000\u0000\u0000\u0000", objArr4);
                    edit.putString(((String) objArr4[0]).intern(), e2).commit();
                    int i3 = i + 63;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                }
                return;
        }
    }

    public final b b(Context context) {
        int i2 = i + 87;
        h = i2 % 128;
        int i3 = i2 % 2;
        b bVar = this.a;
        switch (bVar != null ? 'S' : (char) 21) {
            case Opcodes.AASTORE /* 83 */:
                return bVar;
            default:
                Object[] objArr = new Object[1];
                f(6 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff", 46 - TextUtils.indexOf((CharSequence) "", '0', 0), TextUtils.getOffsetBefore("", 0) + Opcodes.NEW, false, objArr);
                SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
                Object[] objArr2 = new Object[1];
                g(TextUtils.getTrimmedLength(""), "扇⚗\ue519喂涎ꏛ팪繖", (char) (TextUtils.lastIndexOf("", '0') + 1), "精센ິٻ", "\u0000\u0000\u0000\u0000", objArr2);
                String string = sharedPreferences.getString(((String) objArr2[0]).intern(), "");
                g.c();
                Object[] objArr3 = new Object[1];
                f(TextUtils.getOffsetBefore("", 0) + 11, "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", 19 - View.resolveSizeAndState(0, 0, 0), KeyEvent.keyCodeFromString("") + Opcodes.INVOKESPECIAL, false, objArr3);
                String intern = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                g((-625099386) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "穎鍳\ue3b3눜┿䕡ﰍّ亞덅≒\uedd0燱맓겕\ue21d\ud8c1끳䠾\ude9b팃庄窅\ue73e偍况葅☪\uf3dfՖ\uf1ea롥\ude51ရ\uda8b꒑᠐쾥ጧ⦮㥐\ue453⌿잔\ud7ca\ud8b4\ue3cc焣\ue378漨홈ḙ䙳㫰賨̛挚探蟂ꄚ䥔", (char) (ViewConfiguration.getJumpTapTimeout() >> 16), "蕂붽藚胀", "\u0000\u0000\u0000\u0000", objArr4);
                g.d(intern, sb.append(((String) objArr4[0]).intern()).append(string).toString());
                if (string.isEmpty()) {
                    int i4 = h + Opcodes.DMUL;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                    return null;
                }
                b e2 = e(string);
                this.a = e2;
                int i6 = h + Opcodes.LREM;
                i = i6 % 128;
                switch (i6 % 2 != 0) {
                    case false:
                        return e2;
                    default:
                        int i7 = 84 / 0;
                        return e2;
                }
        }
    }

    public final void c(Context context) {
        int i2 = i + 31;
        h = i2 % 128;
        int i3 = i2 % 2;
        this.a = null;
        Object[] objArr = new Object[1];
        f(7 - Color.alpha(0), "\ufffe\u000b\ufffe\u0007￼\ufffe\f\uffff\u000bￇ\ufffa\u0007\r\ufffe\u0005\b\tￇ\ufffa\u0007\r\ufffe\u0005\b\t\u0001￼\ufffe\u0005\u0002\ufffb\u000b\ufffa\u000b\u0012\ufff8\f\u0001\ufffa\u000b\ufffe�\ufff8\t\u000b\ufffe\uffff", 47 - (ViewConfiguration.getPressedStateDuration() >> 16), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + Opcodes.INVOKEDYNAMIC, false, objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        g(TextUtils.indexOf("", "", 0, 0), "扇⚗\ue519喂涎ꏛ팪繖", (char) Color.red(0), "精센ິٻ", "\u0000\u0000\u0000\u0000", objArr2);
        edit.remove(((String) objArr2[0]).intern()).commit();
        int i4 = i + Opcodes.DREM;
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 81 / 0;
                return;
            default:
                return;
        }
    }

    private static String e(b bVar) {
        Object obj = null;
        try {
            o.eg.b bVar2 = new o.eg.b();
            Object[] objArr = new Object[1];
            f(12 - TextUtils.indexOf("", "", 0), "\u000e\uffff\ufffe￭\u000e\ufffb\f\u000e\uffde\ufffb\u000e\uffff\u0004\t￼\uffdf\u0012\n\uffff�", Process.getGidForName("") + 21, ((Process.getThreadPriority(0) + 20) >> 6) + Opcodes.INVOKEDYNAMIC, false, objArr);
            bVar2.d(((String) objArr[0]).intern(), bVar.a().getTime());
            Object[] objArr2 = new Object[1];
            f((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 2, "\uffff\u0002�\uffff\n\u0013￮\uffff\u0001\ufffb\r\r\uffff\uffe7\uffff\u000e\ufffb\ufffe\n\uffef\u0005�", 22 - View.MeasureSpec.getMode(0), 186 - Gravity.getAbsoluteGravity(0, 0), true, objArr2);
            bVar2.d(((String) objArr2[0]).intern(), bVar.b().toString());
            Object[] objArr3 = new Object[1];
            f((ViewConfiguration.getEdgeSlop() >> 16) + 13, "\f\ufffa\ufff8\u0007\ufff4\u0005\u0007￦\f\u0005\u0007\ufff8\u0005", 13 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 193 - View.resolveSize(0, 0), true, objArr3);
            bVar2.d(((String) objArr3[0]).intern(), bVar.d().a());
            Object[] objArr4 = new Object[1];
            f((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 6, "\u0006\ufff9\ufffb\ufffb�\u0006\b", 8 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), ImageFormat.getBitsPerPixel(0) + Opcodes.INSTANCEOF, true, objArr4);
            bVar2.d(((String) objArr4[0]).intern(), bVar.c().toString());
            Object[] objArr5 = new Object[1];
            g((-1721453490) - TextUtils.getTrimmedLength(""), "\ue0b3琐윣\ue03e\ueaed￮웴㥌黼䏐㽵\ueabd‾뜝檁䎱鉔锴춏", (char) (4744 - (ViewConfiguration.getTouchSlop() >> 8)), "亩撴袙\ue112", "\u0000\u0000\u0000\u0000", objArr5);
            bVar2.d(((String) objArr5[0]).intern(), bVar.e());
            String b2 = bVar2.b();
            int i2 = i + Opcodes.LUSHR;
            h = i2 % 128;
            switch (i2 % 2 == 0 ? Typography.greater : 'b') {
                case Opcodes.FADD /* 98 */:
                    return b2;
                default:
                    obj.hashCode();
                    throw null;
            }
        } catch (o.eg.d e2) {
            g.c();
            Object[] objArr6 = new Object[1];
            f((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 11, "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", 19 - Color.argb(0, 0, 0, 0), 182 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), false, objArr6);
            String intern = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            g((ViewConfiguration.getTapTimeout() >> 16) + 1564874023, "⏁ힱবጨ﬩畭욶d㖰㫙⍖䯜\udcdf\ue33c裷흖윸灲搓\ud9a2顀\ueed2旟㊽﨑獍垨\uf679枓ᇵ핼⁉择䔉㠸贊\uf056㪶阐", (char) (12480 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), "➵䘕뽝쐰", "\u0000\u0000\u0000\u0000", objArr7);
            g.a(intern, ((String) objArr7[0]).intern(), e2);
            return null;
        }
    }

    private static b e(String str) {
        try {
            o.eg.b bVar = new o.eg.b(str);
            Object[] objArr = new Object[1];
            f(11 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "\u000e\uffff\ufffe￭\u000e\ufffb\f\u000e\uffde\ufffb\u000e\uffff\u0004\t￼\uffdf\u0012\n\uffff�", 20 - TextUtils.getOffsetBefore("", 0), TextUtils.indexOf((CharSequence) "", '0') + Opcodes.NEW, false, objArr);
            Date date = new Date(bVar.m(((String) objArr[0]).intern()).longValue());
            Object[] objArr2 = new Object[1];
            f((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 3, "\uffff\u0002�\uffff\n\u0013￮\uffff\u0001\ufffb\r\r\uffff\uffe7\uffff\u000e\ufffb\ufffe\n\uffef\u0005�", TextUtils.getOffsetAfter("", 0) + 22, View.resolveSizeAndState(0, 0, 0) + Opcodes.INVOKEDYNAMIC, true, objArr2);
            o.av.a b2 = o.av.a.b(bVar.r(((String) objArr2[0]).intern()));
            if (b2 == null) {
                g.c();
                Object[] objArr3 = new Object[1];
                f((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 10, "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 19, (KeyEvent.getMaxKeyCode() >> 16) + Opcodes.INVOKESPECIAL, false, objArr3);
                String intern = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                f((ViewConfiguration.getScrollDefaultDelay() >> 16) + 18, "\u0013\n\u0012￭\u0006\u0013￮\t\u001e\r\u0010\u0005\r\u0016\t\u0017\t\bￄ\uffde\t\u0014\u001d\u0018ￄ\t\u0017\u0016\u0005\u0014ￄ\u0013\u0018ￄ\t\u0010\u0006\u0005\u0012\ufff9ￄ\uffd1ￄ", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 43, (ViewConfiguration.getFadingEdgeLength() >> 16) + Opcodes.ARETURN, true, objArr4);
                StringBuilder append = sb.append(((String) objArr4[0]).intern());
                Object[] objArr5 = new Object[1];
                f(View.combineMeasuredStates(0, 0) + 3, "\uffff\u0002�\uffff\n\u0013￮\uffff\u0001\ufffb\r\r\uffff\uffe7\uffff\u000e\ufffb\ufffe\n\uffef\u0005�", 22 - TextUtils.getOffsetAfter("", 0), 186 - TextUtils.indexOf("", "", 0, 0), true, objArr5);
                g.e(intern, append.append(bVar.r(((String) objArr5[0]).intern())).toString());
                return null;
            }
            Object[] objArr6 = new Object[1];
            f(View.MeasureSpec.makeMeasureSpec(0, 0) + 13, "\f\ufffa\ufff8\u0007\ufff4\u0005\u0007￦\f\u0005\u0007\ufff8\u0005", (Process.myTid() >> 22) + 13, View.resolveSizeAndState(0, 0, 0) + Opcodes.INSTANCEOF, true, objArr6);
            String r = bVar.r(((String) objArr6[0]).intern());
            o.df.e dVar = new o.df.d();
            o.df.e bVar2 = new o.df.b();
            o.df.e cVar = new o.df.c();
            switch (!r.equals(bVar2.a())) {
                case false:
                    int i2 = h;
                    int i3 = i2 + 5;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                    int i5 = i2 + 85;
                    i = i5 % 128;
                    int i6 = i5 % 2;
                    dVar = bVar2;
                    break;
                default:
                    switch (!r.equals(cVar.a())) {
                        case false:
                            dVar = cVar;
                            break;
                    }
            }
            Object[] objArr7 = new Object[1];
            f(7 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "\u0006\ufff9\ufffb\ufffb�\u0006\b", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 6, Color.red(0) + 192, true, objArr7);
            f a = f.a(bVar.r(((String) objArr7[0]).intern()));
            switch (a == null ? ',' : ']') {
                case ',':
                    int i7 = h + 99;
                    i = i7 % 128;
                    int i8 = i7 % 2;
                    g.c();
                    Object[] objArr8 = new Object[1];
                    f(11 - (ViewConfiguration.getTouchSlop() >> 8), "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", 19 - (ViewConfiguration.getScrollBarSize() >> 8), Color.red(0) + Opcodes.INVOKESPECIAL, false, objArr8);
                    String intern2 = ((String) objArr8[0]).intern();
                    Object[] objArr9 = new Object[1];
                    f(30 - View.MeasureSpec.getMode(0), "\u0005\f\u0003\u0019\u0003\u0005\u0006\t\f\uffc0ￍ\uffc0\u000f\u0006\u000e￩\u0002\u000f￪\u0005\u001a\t\f\u0001\t\u0012\u0005\u0013\u0005\u0004\f\f\u0015\u000e\uffc0\u0013\t\uffc0\u0012\u0005\u0007\u0007\t\u0012\ufff4\u0002\u000f￪", 48 - (ViewConfiguration.getTouchSlop() >> 8), 180 - View.MeasureSpec.getMode(0), true, objArr9);
                    g.e(intern2, ((String) objArr9[0]).intern());
                    int i9 = i + 93;
                    h = i9 % 128;
                    if (i9 % 2 != 0) {
                        return null;
                    }
                    throw null;
                default:
                    Object[] objArr10 = new Object[1];
                    g((-1721453490) - (ViewConfiguration.getJumpTapTimeout() >> 16), "\ue0b3琐윣\ue03e\ueaed￮웴㥌黼䏐㽵\ueabd‾뜝檁䎱鉔锴춏", (char) (4745 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), "亩撴袙\ue112", "\u0000\u0000\u0000\u0000", objArr10);
                    return new b(date, b2, dVar, a, bVar.g(((String) objArr10[0]).intern()).booleanValue());
            }
        } catch (o.eg.d e2) {
            g.c();
            Object[] objArr11 = new Object[1];
            f(11 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\u0001\uffe7\f\uffff￪\ufffe\u000b\ufffe\u0004\u0002\u000f\ufff0\u0000\u0005\u0002\u0001\u0012\t\u0002", View.getDefaultSize(0, 0) + 19, 183 - View.MeasureSpec.getMode(0), false, objArr11);
            String intern3 = ((String) objArr11[0]).intern();
            Object[] objArr12 = new Object[1];
            f(View.resolveSizeAndState(0, 0, 0) + 17, "\u0004\f\uffe7\u0000\r￨\u0003\u0018\u0007\n\uffff\u0007\u0010\u0003\u0011\u0003\u0002\f\r\u0007\u0012\u000e\u0003\u0001\u0016￣\f\r\u0011￨\u000e\r\n\u0003\u0012\f\uffdfﾾￋﾾ\r", View.getDefaultSize(0, 0) + 41, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.PUTFIELD, true, objArr12);
            g.a(intern3, ((String) objArr12[0]).intern(), e2);
            return null;
        }
    }

    private static void f(int i2, String str, int i3, int i4, boolean z, Object[] objArr) {
        char[] charArray;
        int i5 = $10 + 95;
        $11 = i5 % 128;
        int i6 = i5 % 2;
        switch (str == null) {
            case false:
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr = charArray;
        o.a.h hVar = new o.a.h();
        char[] cArr2 = new char[i3];
        hVar.a = 0;
        while (hVar.a < i3) {
            int i7 = $10 + Opcodes.DMUL;
            $11 = i7 % 128;
            int i8 = i7 % 2;
            hVar.b = cArr[hVar.a];
            cArr2[hVar.a] = (char) (i4 + hVar.b);
            int i9 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr2[i9]), Integer.valueOf(e)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 12, (char) TextUtils.getOffsetBefore("", 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 459);
                    byte b2 = (byte) ($$b - 1);
                    byte b3 = $$a[1];
                    Object[] objArr3 = new Object[1];
                    j(b2, (byte) (b3 - 1), b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr2[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (char) (ImageFormat.getBitsPerPixel(0) + 1), 313 - View.combineMeasuredStates(0, 0));
                        byte b4 = (byte) ($$b + 1);
                        byte b5 = $$a[1];
                        Object[] objArr5 = new Object[1];
                        j(b4, (byte) (b5 - 1), b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i2 > 0) {
            int i10 = $10 + 89;
            $11 = i10 % 128;
            int i11 = i10 % 2;
            hVar.c = i2;
            char[] cArr3 = new char[i3];
            System.arraycopy(cArr2, 0, cArr3, 0, i3);
            System.arraycopy(cArr3, 0, cArr2, i3 - hVar.c, hVar.c);
            System.arraycopy(cArr3, hVar.c, cArr2, 0, i3 - hVar.c);
        }
        switch (z) {
            case true:
                char[] cArr4 = new char[i3];
                hVar.a = 0;
                while (hVar.a < i3) {
                    cArr4[hVar.a] = cArr2[(i3 - hVar.a) - 1];
                    try {
                        Object[] objArr6 = {hVar, hVar};
                        Object obj3 = o.e.a.s.get(-1412673904);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (char) ((Process.getThreadPriority(0) + 20) >> 6), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 313);
                            byte b6 = (byte) ($$b + 1);
                            byte b7 = $$a[1];
                            Object[] objArr7 = new Object[1];
                            j(b6, (byte) (b7 - 1), b7, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                cArr2 = cArr4;
                break;
        }
        objArr[0] = new String(cArr2);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 748
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.h.g(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
