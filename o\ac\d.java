package o.ac;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.card.CreateCardRequestPanSource;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.k;
import o.a.l;
import o.cf.f;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d.smali */
public final class d extends o.y.b<b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static int g;
    private static long i;
    private static int j;
    o.ei.a a;
    String b;
    o.ac.a c;
    String d;
    C0016d e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d$b.smali */
    public interface b {
        void a();

        void b(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        g = 1;
        o();
        TextUtils.getOffsetAfter("", 0);
        int i2 = j + 47;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{14, -53, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -35};
        $$e = 70;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.ac.d.$$d
            int r9 = r9 * 2
            int r9 = 3 - r9
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r7 = r7 + 66
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L17:
            r3 = r2
        L18:
            r6 = r9
            r9 = r7
            r7 = r6
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r9
            r9 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.d.n(byte, byte, int, java.lang.Object[]):void");
    }

    static void o() {
        i = -9008054364948521915L;
        f = new char[]{50856, 50717, 50701, 50700, 50719, 50695, 50716, 50718, 50806, 50804, 50717, 50719, 50788, 50752, 50752, 50770, 50802, 50713, 50708, 50713, 50690, 50692, 50788, 50786, 50709, 50719, 50716, 50690, 50717, 50719, 50694, 50788, 50777, 50777, 50935, 50876, 50877, 50925, 50831, 50844, 50840, 50827, 50823, 50819, 50821, 50821, 50827, 50825, 50822, 50935, 50876, 50877, 50873, 50879, 50877, 50850, 50877, 50878, 50849, 50857, 50831, 50821, 50876, 50877, 50852, 50849, 50849, 50854, 50935, 50854, 50849, 50849, 50852, 50877, 50876, 50943, 50859, 50853, 50853, 50851, 50855, 50859, 50842, 50847, 50852, 50854, 50841, 50836, 50877, 50877, 50848, 50849, 50849, 50852, 50852, 50876, 50833, 50847, 50855, 50855, 50864, 50867, 50760, 50766, 50754, 50781, 50833, 50762, 50762, 50759, 50752, 50756, 50861, 50940, 50853, 50836, 50833, 50878, 50872, 50872, 50876, 50852, 50852, 50876, 50878, 50878, 50878, 50877, 50838, 50835, 50876, 50852, 50852, 50878, 50848, 50837, 50818, 50796, 50795, 50788, 50794, 50788, 50792, 50799, 50791, 50790, 50792, 50792, 50779, 50780, 50795, 50793, 50752, 50755, 50770, 50795, 50795, 50796, 50794, 50794, 50780, 50752, 50797, 50795, 50793, 50836, 50812, 50791, 50792, 50790, 50813, 50812, 50791, 50789, 50808, 50809, 50922, 50844, 50822, 50827, 50817, 50847, 50833, 50847, 50820, 50844, 50817, 50822, 50822, 50825, 50821, 50826, 50924, 50821, 50823, 50940, 50856, 50854, 50935, 50855, 50856, 50851, 50878, 50873, 50848, 50854, 50838, 50847, 50807, 50695, 50699, 50807, 50794, 50703, 50697, 50698, 50774, 50804, 50811, 50697, 50700, 50691, 50811, 50792, 50807, 50692, 50811, 50698, 50807, 50702, 50807, 50801, 50702, 50697, 50803, 50692, 50696, 50807, 50805, 50688, 50775, 50872, 50702, 50697, 50693, 50770, 50872, 50767, 50872, 50692, 50693, 50933, 50873, 50859, 50835, 50851, 50873, 50854, 50862, 50845, 50788, 50788, 50942, 50849, 50845, 50826, 50858, 50876, 50849, 50853, 50848, 50850, 50818, 50818, 50853, 50858, 50832, 50842, 50855, 50850, 50864, 50834, 50764, 50781, 50864, 50764, 50755, 50866, 50765, 50755};
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i2 = j + 55;
        g = i2 % 128;
        int i3 = i2 % 2;
        a m = m();
        int i4 = g + 27;
        j = i4 % 128;
        int i5 = i4 % 2;
        return m;
    }

    public d(Context context, b bVar, o.ei.c cVar) {
        super(context, bVar, cVar, o.bb.e.g);
    }

    public final void e(o.ac.a aVar, o.ei.a aVar2) throws o.y.e {
        this.a = aVar2;
        this.e = a(aVar);
        this.c = aVar;
        g.c();
        Object[] objArr = new Object[1];
        k("\uda96坴섖珍\uededᾯ衤㩱됿⛚储슫罜\ue90fᬾ闦ށ", 36306 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        l("\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000", new int[]{0, 34, Opcodes.DSUB, 0}, false, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.e.toString()).toString());
        c();
        int i2 = j + 63;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? 'W' : '?') {
            case Opcodes.POP /* 87 */:
                throw null;
            default:
                return;
        }
    }

    private a m() {
        a aVar = new a(this);
        int i2 = g + Opcodes.LNEG;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '2' : 'P') {
            case '2':
                throw null;
            default:
                return aVar;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i2 = g + 47;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? ',' : (char) 26) {
            case ',':
                Object[] objArr = new Object[1];
                k("\uda96坴섖珍\uededᾯ衤㩱됿⛚储슫罜\ue90fᬾ闦ށ", ViewConfiguration.getPressedStateDuration() * 97 * 36307, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("\uda96坴섖珍\uededᾯ衤㩱됿⛚储슫罜\ue90fᬾ闦ށ", (ViewConfiguration.getPressedStateDuration() >> 16) + 36307, objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d$a.smali */
    static final class a extends o.y.c<d> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int[] b;
        private static int c;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            c = 1;
            b = new int[]{468057297, 32343537, -2092099884, 67919437, 521298846, -229372579, 206459160, -264254539, -1906416559, 395888209, -2084751167, -1267357855, 1515445771, -2088315752, 2138379974, 370079351, -1409502834, -1422822009};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                int r8 = 116 - r8
                byte[] r0 = o.ac.d.a.$$d
                int r6 = r6 * 2
                int r6 = 4 - r6
                int r7 = r7 * 3
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1b
                r8 = r7
                r3 = r1
                r4 = r2
                r7 = r6
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L1b:
                r3 = r2
            L1c:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r7
                r7 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r8 = r8 + r6
                int r6 = r7 + 1
                r7 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1c
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ac.d.a.B(byte, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{1, 25, 123, 58};
            $$e = Opcodes.I2B;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = c + 65;
            e = i % 128;
            switch (i % 2 != 0 ? 'F' : '0') {
                case '0':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = e + 75;
            c = i % 128;
            int i2 = i % 2;
            o.cf.d a = a(context);
            int i3 = c + 47;
            e = i3 % 128;
            int i4 = i3 % 2;
            return a;
        }

        a(d dVar) {
            super(dVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = c + 21;
            e = i % 128;
            switch (i % 2 != 0 ? Typography.dollar : 'U') {
                case Opcodes.CASTORE /* 85 */:
                    Object[] objArr = new Object[1];
                    w(new int[]{-99668348, -693315083, 564365837, 1221405787, 1605501265, -109595232}, Process.getGidForName("") + 11, objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(new int[]{-99668348, -693315083, 564365837, 1221405787, 1605501265, -109595232}, 47 % Process.getGidForName(""), objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        private static o.cf.d a(Context context) {
            Object[] objArr = new Object[1];
            w(new int[]{-1331735960, 119963734, 1338213570, 1253443989, -1517221592, -735573542, -457982147, -938786833, -2009456313, 794224922}, 19 - (ViewConfiguration.getTapTimeout() >> 16), objArr);
            o.cf.d dVar = new o.cf.d(context, 21, ((String) objArr[0]).intern());
            int i = e + 75;
            c = i % 128;
            switch (i % 2 == 0 ? (char) 17 : '?') {
                case 17:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            int i = e + 33;
            c = i % 128;
            switch (i % 2 == 0 ? 'K' : (char) 3) {
                case 'K':
                    ((d) e()).e.e();
                    throw null;
                default:
                    return ((d) e()).e.e();
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = e + 63;
            int i2 = i % 128;
            c = i2;
            int i3 = i % 2;
            int i4 = i2 + 1;
            e = i4 % 128;
            boolean z = i4 % 2 == 0;
            Object obj = null;
            switch (z) {
                case false:
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final byte[][] k() {
            int i = e + 79;
            c = i % 128;
            switch (i % 2 == 0) {
                case true:
                    ((d) e()).e.a();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return ((d) e()).e.a();
            }
        }

        @Override // o.y.c
        public final o.bb.a d(o.cf.g gVar) {
            int i = e + 81;
            c = i % 128;
            if (i % 2 == 0) {
                gVar.b().e();
                o.cf.a aVar = o.cf.a.q;
                throw null;
            }
            switch (gVar.b().e() != o.cf.a.q) {
                case true:
                    break;
                default:
                    switch (gVar.j() != f.e) {
                        case false:
                            int i2 = c + 91;
                            e = i2 % 128;
                            if (i2 % 2 == 0) {
                                return o.bb.a.as;
                            }
                            o.bb.a aVar2 = o.bb.a.as;
                            throw null;
                    }
            }
            return super.d(gVar);
        }

        /* JADX WARN: Code restructure failed: missing block: B:23:0x001a, code lost:
        
            if (r4 == 5529) goto L17;
         */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.bb.a c(int r4) {
            /*
                r3 = this;
                int r0 = o.ac.d.a.c
                int r1 = r0 + 111
                int r2 = r1 % 128
                o.ac.d.a.e = r2
                r2 = 2
                int r1 = r1 % r2
                if (r1 == 0) goto Le
                r1 = 4
                goto L10
            Le:
                r1 = 20
            L10:
                switch(r1) {
                    case 4: goto L18;
                    default: goto L13;
                }
            L13:
                r1 = 2100(0x834, float:2.943E-42)
                if (r4 != r1) goto L20
                goto L1d
            L18:
                r1 = 5529(0x1599, float:7.748E-42)
                if (r4 != r1) goto L29
            L1c:
                goto L26
            L1d:
                r4 = 27
                goto L22
            L20:
                r4 = 90
            L22:
                switch(r4) {
                    case 27: goto L1c;
                    default: goto L25;
                }
            L25:
                goto L29
            L26:
                o.bb.a r4 = o.bb.a.at
                return r4
            L29:
                int r0 = r0 + 95
                int r4 = r0 % 128
                o.ac.d.a.e = r4
                int r0 = r0 % r2
                r4 = 0
                if (r0 == 0) goto L39
                int r2 = r2 / 0
                return r4
            L37:
                r4 = move-exception
                throw r4
            L39:
                return r4
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ac.d.a.c(int):o.bb.a");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = e + 39;
            c = i % 128;
            int i2 = i % 2;
            d dVar = (d) e();
            Object[] objArr = new Object[1];
            w(new int[]{-1411041031, -307710502, 2112408244, 1822156076}, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 5, objArr);
            dVar.b = bVar.r(((String) objArr[0]).intern());
            d dVar2 = (d) e();
            Object[] objArr2 = new Object[1];
            w(new int[]{-503075785, 1258825148, 2050349352, 315144210, -54602042, -1283228021}, (KeyEvent.getMaxKeyCode() >> 16) + 12, objArr2);
            dVar2.d = bVar.q(((String) objArr2[0]).intern());
            int i3 = c + 5;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            f().a().i().e(((d) e()).b, ((d) e()).d, ((d) e()).a);
            f().d(g());
            f().a().f().e(new o.dd.e(g()));
            if (((d) e()).d().b()) {
                int i = c + Opcodes.DDIV;
                e = i % 128;
                switch (i % 2 != 0 ? '\b' : '-') {
                    case '\b':
                        boolean z = ((d) e()).c instanceof c;
                        throw null;
                    default:
                        if (((d) e()).c instanceof c) {
                            i().e(new o.de.d(o.av.a.d, o.de.f.z));
                            break;
                        }
                        break;
                }
            }
            int i2 = c + 99;
            e = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return;
                default:
                    int i3 = 85 / 0;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = c + 61;
            e = i % 128;
            switch (i % 2 == 0) {
                case true:
                    ((d) e()).j().a();
                    return;
                default:
                    ((d) e()).j().a();
                    int i2 = 50 / 0;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = e + Opcodes.DSUB;
            c = i % 128;
            int i2 = i % 2;
            ((d) e()).j().b(dVar);
            int i3 = c + 67;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int[] r21, int r22, java.lang.Object[] r23) {
            /*
                Method dump skipped, instructions count: 862
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ac.d.a.w(int[], int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.ac.d$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a = 1;
        static final /* synthetic */ int[] c;
        static final /* synthetic */ int[] d;
        private static int e;

        /* JADX WARN: Failed to find 'out' block for switch in B:7:0x002c. Please report as an issue. */
        static {
            e = 0;
            int[] iArr = new int[CreateCardRequestPanSource.values().length];
            d = iArr;
            try {
                iArr[CreateCardRequestPanSource.IssuerPush.ordinal()] = 1;
                int i = a;
                int i2 = (i ^ 9) + ((i & 9) << 1);
                e = i2 % 128;
                switch (i2 % 2 != 0 ? 'B' : (char) 18) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[CreateCardRequestPanSource.ManualEntry.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[CreateCardRequestPanSource.NfcScan.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[CreateCardRequestPanSource.CameraScan.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            int[] iArr2 = new int[o.ei.a.values().length];
            c = iArr2;
            try {
                iArr2[o.ei.a.d.ordinal()] = 1;
                int i3 = (a + 8) - 1;
                e = i3 % 128;
                if (i3 % 2 == 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                c[o.ei.a.b.ordinal()] = 2;
            } catch (NoSuchFieldError e7) {
            }
            try {
                c[o.ei.a.a.ordinal()] = 3;
                int i4 = (a + 36) - 1;
                e = i4 % 128;
                switch (i4 % 2 != 0 ? '\b' : 'c') {
                    case '\b':
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e8) {
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:47:0x033a A[Catch: ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, TryCatch #5 {ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, blocks: (B:6:0x0019, B:7:0x0024, B:8:0x0027, B:9:0x0565, B:10:0x0580, B:12:0x002b, B:13:0x005a, B:18:0x0088, B:20:0x00a6, B:22:0x00c4, B:25:0x00d6, B:27:0x00dd, B:29:0x00e3, B:33:0x0110, B:36:0x02c3, B:39:0x02cc, B:40:0x02cf, B:42:0x02d9, B:44:0x02e9, B:45:0x0333, B:47:0x033a, B:48:0x037f, B:51:0x0388, B:54:0x03a8, B:56:0x03ac, B:58:0x03d3, B:59:0x03f5, B:60:0x0401, B:62:0x046f, B:64:0x0406, B:65:0x0421, B:66:0x0439, B:67:0x0454, B:68:0x038c, B:70:0x0354, B:76:0x036c, B:79:0x0379, B:84:0x0371, B:87:0x02fb, B:89:0x0301, B:92:0x0317, B:95:0x0327, B:98:0x0118, B:101:0x0121, B:103:0x0145, B:105:0x014b, B:106:0x016f, B:110:0x0184, B:111:0x01a2, B:113:0x01a8, B:114:0x01e8, B:117:0x01f8, B:118:0x021c, B:120:0x0222, B:121:0x0242, B:123:0x0248, B:125:0x026b, B:132:0x02a0, B:133:0x029c, B:135:0x0286, B:137:0x0125, B:140:0x00f3, B:142:0x00f9, B:144:0x0043), top: B:5:0x0019 }] */
    /* JADX WARN: Removed duplicated region for block: B:50:0x0385  */
    /* JADX WARN: Removed duplicated region for block: B:52:0x038b  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x03d3 A[Catch: ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, TryCatch #5 {ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, blocks: (B:6:0x0019, B:7:0x0024, B:8:0x0027, B:9:0x0565, B:10:0x0580, B:12:0x002b, B:13:0x005a, B:18:0x0088, B:20:0x00a6, B:22:0x00c4, B:25:0x00d6, B:27:0x00dd, B:29:0x00e3, B:33:0x0110, B:36:0x02c3, B:39:0x02cc, B:40:0x02cf, B:42:0x02d9, B:44:0x02e9, B:45:0x0333, B:47:0x033a, B:48:0x037f, B:51:0x0388, B:54:0x03a8, B:56:0x03ac, B:58:0x03d3, B:59:0x03f5, B:60:0x0401, B:62:0x046f, B:64:0x0406, B:65:0x0421, B:66:0x0439, B:67:0x0454, B:68:0x038c, B:70:0x0354, B:76:0x036c, B:79:0x0379, B:84:0x0371, B:87:0x02fb, B:89:0x0301, B:92:0x0317, B:95:0x0327, B:98:0x0118, B:101:0x0121, B:103:0x0145, B:105:0x014b, B:106:0x016f, B:110:0x0184, B:111:0x01a2, B:113:0x01a8, B:114:0x01e8, B:117:0x01f8, B:118:0x021c, B:120:0x0222, B:121:0x0242, B:123:0x0248, B:125:0x026b, B:132:0x02a0, B:133:0x029c, B:135:0x0286, B:137:0x0125, B:140:0x00f3, B:142:0x00f9, B:144:0x0043), top: B:5:0x0019 }] */
    /* JADX WARN: Removed duplicated region for block: B:68:0x038c A[Catch: ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, TryCatch #5 {ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, blocks: (B:6:0x0019, B:7:0x0024, B:8:0x0027, B:9:0x0565, B:10:0x0580, B:12:0x002b, B:13:0x005a, B:18:0x0088, B:20:0x00a6, B:22:0x00c4, B:25:0x00d6, B:27:0x00dd, B:29:0x00e3, B:33:0x0110, B:36:0x02c3, B:39:0x02cc, B:40:0x02cf, B:42:0x02d9, B:44:0x02e9, B:45:0x0333, B:47:0x033a, B:48:0x037f, B:51:0x0388, B:54:0x03a8, B:56:0x03ac, B:58:0x03d3, B:59:0x03f5, B:60:0x0401, B:62:0x046f, B:64:0x0406, B:65:0x0421, B:66:0x0439, B:67:0x0454, B:68:0x038c, B:70:0x0354, B:76:0x036c, B:79:0x0379, B:84:0x0371, B:87:0x02fb, B:89:0x0301, B:92:0x0317, B:95:0x0327, B:98:0x0118, B:101:0x0121, B:103:0x0145, B:105:0x014b, B:106:0x016f, B:110:0x0184, B:111:0x01a2, B:113:0x01a8, B:114:0x01e8, B:117:0x01f8, B:118:0x021c, B:120:0x0222, B:121:0x0242, B:123:0x0248, B:125:0x026b, B:132:0x02a0, B:133:0x029c, B:135:0x0286, B:137:0x0125, B:140:0x00f3, B:142:0x00f9, B:144:0x0043), top: B:5:0x0019 }] */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0387  */
    /* JADX WARN: Removed duplicated region for block: B:70:0x0354 A[Catch: ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, TRY_LEAVE, TryCatch #5 {ArrayIndexOutOfBoundsException -> 0x0581, d -> 0x0583, blocks: (B:6:0x0019, B:7:0x0024, B:8:0x0027, B:9:0x0565, B:10:0x0580, B:12:0x002b, B:13:0x005a, B:18:0x0088, B:20:0x00a6, B:22:0x00c4, B:25:0x00d6, B:27:0x00dd, B:29:0x00e3, B:33:0x0110, B:36:0x02c3, B:39:0x02cc, B:40:0x02cf, B:42:0x02d9, B:44:0x02e9, B:45:0x0333, B:47:0x033a, B:48:0x037f, B:51:0x0388, B:54:0x03a8, B:56:0x03ac, B:58:0x03d3, B:59:0x03f5, B:60:0x0401, B:62:0x046f, B:64:0x0406, B:65:0x0421, B:66:0x0439, B:67:0x0454, B:68:0x038c, B:70:0x0354, B:76:0x036c, B:79:0x0379, B:84:0x0371, B:87:0x02fb, B:89:0x0301, B:92:0x0317, B:95:0x0327, B:98:0x0118, B:101:0x0121, B:103:0x0145, B:105:0x014b, B:106:0x016f, B:110:0x0184, B:111:0x01a2, B:113:0x01a8, B:114:0x01e8, B:117:0x01f8, B:118:0x021c, B:120:0x0222, B:121:0x0242, B:123:0x0248, B:125:0x026b, B:132:0x02a0, B:133:0x029c, B:135:0x0286, B:137:0x0125, B:140:0x00f3, B:142:0x00f9, B:144:0x0043), top: B:5:0x0019 }] */
    /* JADX WARN: Type inference failed for: r13v0, types: [o.eg.b] */
    /* JADX WARN: Type inference failed for: r4v0, types: [o.ac.d$d] */
    /* JADX WARN: Type inference failed for: r6v111, types: [java.lang.Integer] */
    /* JADX WARN: Type inference failed for: r6v94 */
    /* JADX WARN: Type inference failed for: r6v95, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r6v96, types: [java.lang.Object] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.ac.d.C0016d a(o.ac.a r27) throws o.y.e {
        /*
            Method dump skipped, instructions count: 1616
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.d.a(o.ac.a):o.ac.d$d");
    }

    private static o.eg.b d(Object obj) throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        l("\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000", new int[]{251, 8, 0, 7}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("\u0001\u0000\u0000", new int[]{259, 3, 94, 0}, true, objArr2);
        bVar.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        k("\udab4薛擗윌", Gravity.getAbsoluteGravity(0, 0) + 24379, objArr3);
        bVar.d(((String) objArr3[0]).intern(), obj);
        int i2 = g + 63;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.dollar : (char) 19) {
            case 19:
                return bVar;
            default:
                int i3 = 49 / 0;
                return bVar;
        }
    }

    /* renamed from: o.ac.d$d, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d$d.smali */
    static final class C0016d {
        private o.eg.b a;
        private static int d = 0;
        private static int b = 1;
        private byte[][] e = new byte[10][];
        private int c = 0;

        C0016d() {
        }

        final int b(byte[] bArr) {
            int i = d;
            int i2 = (i ^ 29) + ((i & 29) << 1);
            b = i2 % 128;
            int i3 = i2 % 2;
            int i4 = this.c;
            if (i4 == 10) {
                throw new ArrayIndexOutOfBoundsException();
            }
            this.e[i4] = bArr;
            this.c = i4 + 1;
            int i5 = (i & 51) + (i | 51);
            b = i5 % 128;
            int i6 = i5 % 2;
            return i4;
        }

        final o.eg.b e() {
            int i = d;
            int i2 = (i ^ 21) + ((i & 21) << 1);
            b = i2 % 128;
            int i3 = i2 % 2;
            o.eg.b bVar = this.a;
            int i4 = (i + Opcodes.FDIV) - 1;
            b = i4 % 128;
            switch (i4 % 2 == 0 ? 'X' : (char) 29) {
                case 29:
                    return bVar;
                default:
                    throw null;
            }
        }

        public final void a(o.eg.b bVar) {
            int i = b + 33;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            this.a = bVar;
            int i4 = (i2 + 100) - 1;
            b = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        final byte[][] a() {
            int i = d;
            int i2 = (i ^ 23) + ((i & 23) << 1);
            b = i2 % 128;
            int i3 = i2 % 2;
            int i4 = this.c;
            byte[][] bArr = new byte[i4][];
            System.arraycopy(this.e, 0, bArr, 0, i4);
            int i5 = b;
            int i6 = (i5 ^ 93) + ((i5 & 93) << 1);
            d = i6 % 128;
            switch (i6 % 2 != 0 ? '\n' : 'B') {
                case 'B':
                    return bArr;
                default:
                    int i7 = 8 / 0;
                    return bArr;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        final void d() {
            /*
                r5 = this;
                int r0 = o.ac.d.C0016d.b
                int r0 = r0 + 49
                int r1 = r0 % 128
                o.ac.d.C0016d.d = r1
                int r0 = r0 % 2
                r0 = 0
                r1 = r0
            Lc:
                int r2 = r5.c
                if (r1 >= r2) goto L13
                r2 = 27
                goto L15
            L13:
                r2 = 82
            L15:
                r3 = 1
                switch(r2) {
                    case 27: goto L1a;
                    default: goto L19;
                }
            L19:
                goto L48
            L1a:
                int r2 = o.ac.d.C0016d.d
                r4 = r2 | 19
                int r4 = r4 << r3
                r2 = r2 ^ 19
                int r4 = r4 - r2
                int r2 = r4 % 128
                o.ac.d.C0016d.b = r2
                int r4 = r4 % 2
                if (r4 != 0) goto L2c
                r2 = r0
                goto L2d
            L2c:
                r2 = r3
            L2d:
                switch(r2) {
                    case 1: goto L3e;
                    default: goto L30;
                }
            L30:
                byte[][] r2 = r5.e
                r2 = r2[r1]
                java.util.Arrays.fill(r2, r0)
                r2 = r1 ^ 56
                r1 = r1 & 56
                int r1 = r1 << r3
                int r1 = r1 + r2
                goto Lc
            L3e:
                byte[][] r2 = r5.e
                r2 = r2[r1]
                java.util.Arrays.fill(r2, r0)
                int r1 = r1 + 1
                goto Lc
            L48:
                int r0 = o.ac.d.C0016d.b
                int r0 = r0 + 32
                int r0 = r0 - r3
                int r1 = r0 % 128
                o.ac.d.C0016d.d = r1
                int r0 = r0 % 2
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ac.d.C0016d.d():void");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\d$e.smali */
    public static final class e {
        e() {
        }
    }

    private static void k(String str, int i2, Object[] objArr) {
        char[] cArr;
        int i3 = $10 + 1;
        $11 = i3 % 128;
        Object obj = null;
        switch (i3 % 2 != 0) {
            case true:
                switch (str != null) {
                    case true:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str;
                        break;
                }
                char[] cArr2 = cArr;
                k kVar = new k();
                kVar.a = i2;
                int length = cArr2.length;
                long[] jArr = new long[length];
                kVar.b = 0;
                while (true) {
                    long j2 = 0;
                    if (kVar.b >= cArr2.length) {
                        char[] cArr3 = new char[length];
                        kVar.b = 0;
                        while (kVar.b < cArr2.length) {
                            int i4 = $11 + Opcodes.LSHL;
                            $10 = i4 % 128;
                            int i5 = i4 % 2;
                            cArr3[kVar.b] = (char) jArr[kVar.b];
                            try {
                                Object[] objArr2 = {kVar, kVar};
                                Object obj2 = o.e.a.s.get(-10300751);
                                if (obj2 == null) {
                                    Class cls = (Class) o.e.a.c(12 - ExpandableListView.getPackedPositionType(j2), (char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 55184), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 538);
                                    byte b2 = (byte) 0;
                                    Object[] objArr3 = new Object[1];
                                    n((byte) 46, b2, b2, objArr3);
                                    obj2 = cls.getMethod((String) objArr3[0], Object.class, Object.class);
                                    o.e.a.s.put(-10300751, obj2);
                                }
                                ((Method) obj2).invoke(null, objArr2);
                                j2 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        objArr[0] = new String(cArr3);
                        return;
                    }
                    int i6 = kVar.b;
                    try {
                        Object[] objArr4 = {Integer.valueOf(cArr2[kVar.b]), kVar, kVar};
                        Object obj3 = o.e.a.s.get(806930129);
                        if (obj3 == null) {
                            Class cls2 = (Class) o.e.a.c(10 - ExpandableListView.getPackedPositionType(0L), (char) (60578 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 354 - TextUtils.getOffsetAfter("", 0));
                            byte b3 = (byte) 0;
                            Object[] objArr5 = new Object[1];
                            n((byte) 48, b3, b3, objArr5);
                            obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Object.class, Object.class);
                            o.e.a.s.put(806930129, obj3);
                        }
                        jArr[i6] = ((Long) ((Method) obj3).invoke(null, objArr4)).longValue() ^ (i ^ (-5249873463433509232L));
                        try {
                            Object[] objArr6 = {kVar, kVar};
                            Object obj4 = o.e.a.s.get(-10300751);
                            if (obj4 == null) {
                                Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getPressedStateDuration() >> 16), (char) (Drawable.resolveOpacity(0, 0) + 55185), Color.red(0) + 538);
                                byte b4 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                n((byte) 46, b4, b4, objArr7);
                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-10300751, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    private static void l(String str, int[] iArr, boolean z, Object[] objArr) {
        int i2;
        int i3;
        char[] cArr;
        int i4;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i5 = 0;
        int i6 = iArr[0];
        int i7 = 1;
        int i8 = iArr[1];
        int i9 = 2;
        int i10 = iArr[2];
        int i11 = iArr[3];
        char[] cArr2 = f;
        if (cArr2 != null) {
            int i12 = $11 + 97;
            $10 = i12 % 128;
            int i13 = i12 % 2;
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i14 = 0;
            while (i14 < length) {
                int i15 = $10 + 35;
                $11 = i15 % 128;
                int i16 = i15 % i9;
                try {
                    Object[] objArr2 = new Object[i7];
                    objArr2[i5] = Integer.valueOf(cArr2[i14]);
                    Object obj = o.e.a.s.get(1951085128);
                    if (obj != null) {
                        cArr = cArr2;
                        i4 = length;
                    } else {
                        Class cls = (Class) o.e.a.c(((Process.getThreadPriority(i5) + 20) >> 6) + 11, (char) (ViewConfiguration.getEdgeSlop() >> 16), ((Process.getThreadPriority(i5) + 20) >> 6) + 43);
                        byte b2 = (byte) i5;
                        cArr = cArr2;
                        i4 = length;
                        Object[] objArr3 = new Object[1];
                        n((byte) 54, b2, b2, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(1951085128, obj);
                    }
                    cArr3[i14] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i14++;
                    cArr2 = cArr;
                    length = i4;
                    i5 = 0;
                    i7 = 1;
                    i9 = 2;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        char[] cArr4 = new char[i8];
        System.arraycopy(cArr2, i6, cArr4, 0, i8);
        switch (bArr2 != null ? (char) 11 : (char) 15) {
            case 15:
                break;
            default:
                char[] cArr5 = new char[i8];
                lVar.d = 0;
                char c = 0;
                while (true) {
                    switch (lVar.d < i8) {
                        case true:
                            switch (bArr2[lVar.d] == 1 ? ':' : (char) 19) {
                                case 19:
                                    int i17 = lVar.d;
                                    try {
                                        Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                        Object obj2 = o.e.a.s.get(804049217);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 11, (char) Color.blue(0), 206 - Process.getGidForName(""));
                                            byte b3 = (byte) 0;
                                            Object[] objArr5 = new Object[1];
                                            n((byte) 56, b3, b3, objArr5);
                                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(804049217, obj2);
                                        }
                                        cArr5[i17] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                        break;
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                default:
                                    int i18 = lVar.d;
                                    try {
                                        Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                        Object obj3 = o.e.a.s.get(2016040108);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 10, (char) (ViewConfiguration.getEdgeSlop() >> 16), 448 - Drawable.resolveOpacity(0, 0));
                                            byte b4 = (byte) 0;
                                            Object[] objArr7 = new Object[1];
                                            n((byte) (-$$d[1]), b4, b4, objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                            o.e.a.s.put(2016040108, obj3);
                                        }
                                        cArr5[i18] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                        break;
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                            }
                            c = cArr5[lVar.d];
                            try {
                                Object[] objArr8 = {lVar, lVar};
                                Object obj4 = o.e.a.s.get(-2112603350);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(12 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 260);
                                    byte b5 = (byte) 0;
                                    byte b6 = b5;
                                    Object[] objArr9 = new Object[1];
                                    n(b5, b6, b6, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(-2112603350, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        default:
                            cArr4 = cArr5;
                            break;
                    }
                }
        }
        if (i11 > 0) {
            char[] cArr6 = new char[i8];
            System.arraycopy(cArr4, 0, cArr6, 0, i8);
            int i19 = i8 - i11;
            System.arraycopy(cArr6, 0, cArr4, i19, i11);
            System.arraycopy(cArr6, i11, cArr4, 0, i19);
        }
        switch (z) {
            case true:
                char[] cArr7 = new char[i8];
                lVar.d = 0;
                while (lVar.d < i8) {
                    cArr7[lVar.d] = cArr4[(i8 - lVar.d) - 1];
                    lVar.d++;
                    int i20 = $11 + 85;
                    $10 = i20 % 128;
                    int i21 = i20 % 2;
                }
                int i22 = $11 + 53;
                $10 = i22 % 128;
                int i23 = i22 % 2;
                cArr4 = cArr7;
                break;
        }
        switch (i10 <= 0) {
            case true:
                break;
            default:
                int i24 = 0;
                while (true) {
                    lVar.d = i24;
                    if (lVar.d >= i8) {
                        break;
                    } else {
                        int i25 = $10 + 77;
                        $11 = i25 % 128;
                        switch (i25 % 2 == 0) {
                            case true:
                                cArr4[lVar.d] = (char) (cArr4[lVar.d] % iArr[5]);
                                i2 = lVar.d;
                                i3 = 0;
                                break;
                            default:
                                cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                                i2 = lVar.d;
                                i3 = 1;
                                break;
                        }
                        i24 = i2 + i3;
                    }
                }
        }
        objArr[0] = new String(cArr4);
    }
}
