package bc.org.bouncycastle.math.ec.custom.djb;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\djb\Curve25519Point.smali */
public class Curve25519Point extends ECPoint.AbstractFp {
    Curve25519Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    protected Curve25519FieldElement a(Curve25519FieldElement curve25519FieldElement, int[] iArr) {
        Curve25519FieldElement curve25519FieldElement2 = (Curve25519FieldElement) getCurve().getA();
        if (curve25519FieldElement.isOne()) {
            return curve25519FieldElement2;
        }
        Curve25519FieldElement curve25519FieldElement3 = new Curve25519FieldElement();
        if (iArr == null) {
            iArr = curve25519FieldElement3.a;
            Curve25519Field.square(curve25519FieldElement.a, iArr);
        }
        Curve25519Field.square(iArr, curve25519FieldElement3.a);
        int[] iArr2 = curve25519FieldElement3.a;
        Curve25519Field.multiply(iArr2, curve25519FieldElement2.a, iArr2);
        return curve25519FieldElement3;
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        Curve25519FieldElement curve25519FieldElement = (Curve25519FieldElement) this.b;
        Curve25519FieldElement curve25519FieldElement2 = (Curve25519FieldElement) this.c;
        Curve25519FieldElement curve25519FieldElement3 = (Curve25519FieldElement) this.d[0];
        Curve25519FieldElement curve25519FieldElement4 = (Curve25519FieldElement) eCPoint.getXCoord();
        Curve25519FieldElement curve25519FieldElement5 = (Curve25519FieldElement) eCPoint.getYCoord();
        Curve25519FieldElement curve25519FieldElement6 = (Curve25519FieldElement) eCPoint.getZCoord(0);
        int[] c = w5.c();
        int[] a = w5.a();
        int[] a2 = w5.a();
        int[] a3 = w5.a();
        boolean isOne = curve25519FieldElement3.isOne();
        if (isOne) {
            iArr = curve25519FieldElement4.a;
            iArr2 = curve25519FieldElement5.a;
        } else {
            Curve25519Field.square(curve25519FieldElement3.a, a2);
            Curve25519Field.multiply(a2, curve25519FieldElement4.a, a);
            Curve25519Field.multiply(a2, curve25519FieldElement3.a, a2);
            Curve25519Field.multiply(a2, curve25519FieldElement5.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = curve25519FieldElement6.isOne();
        if (isOne2) {
            iArr3 = curve25519FieldElement.a;
            iArr4 = curve25519FieldElement2.a;
        } else {
            Curve25519Field.square(curve25519FieldElement6.a, a3);
            Curve25519Field.multiply(a3, curve25519FieldElement.a, c);
            Curve25519Field.multiply(a3, curve25519FieldElement6.a, a3);
            Curve25519Field.multiply(a3, curve25519FieldElement2.a, a3);
            iArr3 = c;
            iArr4 = a3;
        }
        int[] a4 = w5.a();
        Curve25519Field.subtract(iArr3, iArr, a4);
        Curve25519Field.subtract(iArr4, iArr2, a);
        if (w5.b(a4)) {
            return w5.b(a) ? twice() : curve.getInfinity();
        }
        int[] a5 = w5.a();
        Curve25519Field.square(a4, a5);
        int[] a6 = w5.a();
        Curve25519Field.multiply(a5, a4, a6);
        Curve25519Field.multiply(a5, iArr3, a2);
        Curve25519Field.negate(a6, a6);
        w5.c(iArr4, a6, c);
        Curve25519Field.reduce27(w5.b(a2, a2, a6), a6);
        Curve25519FieldElement curve25519FieldElement7 = new Curve25519FieldElement(a3);
        Curve25519Field.square(a, curve25519FieldElement7.a);
        int[] iArr5 = curve25519FieldElement7.a;
        Curve25519Field.subtract(iArr5, a6, iArr5);
        Curve25519FieldElement curve25519FieldElement8 = new Curve25519FieldElement(a6);
        Curve25519Field.subtract(a2, curve25519FieldElement7.a, curve25519FieldElement8.a);
        Curve25519Field.multiplyAddToExt(curve25519FieldElement8.a, a, c);
        Curve25519Field.reduce(c, curve25519FieldElement8.a);
        Curve25519FieldElement curve25519FieldElement9 = new Curve25519FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = curve25519FieldElement9.a;
            Curve25519Field.multiply(iArr6, curve25519FieldElement3.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = curve25519FieldElement9.a;
            Curve25519Field.multiply(iArr7, curve25519FieldElement6.a, iArr7);
        }
        if (!isOne || !isOne2) {
            a5 = null;
        }
        return new Curve25519Point(curve, curve25519FieldElement7, curve25519FieldElement8, new ECFieldElement[]{curve25519FieldElement9, a(curve25519FieldElement9, a5)});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new Curve25519Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECFieldElement getZCoord(int i) {
        return i == 1 ? i() : super.getZCoord(i);
    }

    protected Curve25519FieldElement i() {
        ECFieldElement[] eCFieldElementArr = this.d;
        Curve25519FieldElement curve25519FieldElement = (Curve25519FieldElement) eCFieldElementArr[1];
        if (curve25519FieldElement != null) {
            return curve25519FieldElement;
        }
        Curve25519FieldElement a = a((Curve25519FieldElement) eCFieldElementArr[0], (int[]) null);
        eCFieldElementArr[1] = a;
        return a;
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new Curve25519Point(getCurve(), this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : a(false).add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        return this.c.isZero() ? getCurve().getInfinity() : a(true);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : a(false).add(eCPoint);
    }

    Curve25519Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }

    protected Curve25519Point a(boolean z) {
        Curve25519FieldElement curve25519FieldElement;
        Curve25519FieldElement curve25519FieldElement2 = (Curve25519FieldElement) this.b;
        Curve25519FieldElement curve25519FieldElement3 = (Curve25519FieldElement) this.c;
        Curve25519FieldElement curve25519FieldElement4 = (Curve25519FieldElement) this.d[0];
        Curve25519FieldElement i = i();
        int[] a = w5.a();
        Curve25519Field.square(curve25519FieldElement2.a, a);
        Curve25519Field.reduce27(w5.b(a, a, a) + w5.a(i.a, a), a);
        int[] a2 = w5.a();
        Curve25519Field.twice(curve25519FieldElement3.a, a2);
        int[] a3 = w5.a();
        Curve25519Field.multiply(a2, curve25519FieldElement3.a, a3);
        int[] a4 = w5.a();
        Curve25519Field.multiply(a3, curve25519FieldElement2.a, a4);
        Curve25519Field.twice(a4, a4);
        int[] a5 = w5.a();
        Curve25519Field.square(a3, a5);
        Curve25519Field.twice(a5, a5);
        Curve25519FieldElement curve25519FieldElement5 = new Curve25519FieldElement(a3);
        Curve25519Field.square(a, curve25519FieldElement5.a);
        int[] iArr = curve25519FieldElement5.a;
        Curve25519Field.subtract(iArr, a4, iArr);
        int[] iArr2 = curve25519FieldElement5.a;
        Curve25519Field.subtract(iArr2, a4, iArr2);
        Curve25519FieldElement curve25519FieldElement6 = new Curve25519FieldElement(a4);
        Curve25519Field.subtract(a4, curve25519FieldElement5.a, curve25519FieldElement6.a);
        int[] iArr3 = curve25519FieldElement6.a;
        Curve25519Field.multiply(iArr3, a, iArr3);
        int[] iArr4 = curve25519FieldElement6.a;
        Curve25519Field.subtract(iArr4, a5, iArr4);
        Curve25519FieldElement curve25519FieldElement7 = new Curve25519FieldElement(a2);
        if (!w5.a(curve25519FieldElement4.a)) {
            int[] iArr5 = curve25519FieldElement7.a;
            Curve25519Field.multiply(iArr5, curve25519FieldElement4.a, iArr5);
        }
        if (!z) {
            curve25519FieldElement = null;
        } else {
            curve25519FieldElement = new Curve25519FieldElement(a5);
            int[] iArr6 = curve25519FieldElement.a;
            Curve25519Field.multiply(iArr6, i.a, iArr6);
            int[] iArr7 = curve25519FieldElement.a;
            Curve25519Field.twice(iArr7, iArr7);
        }
        return new Curve25519Point(getCurve(), curve25519FieldElement5, curve25519FieldElement6, new ECFieldElement[]{curve25519FieldElement7, curve25519FieldElement});
    }
}
