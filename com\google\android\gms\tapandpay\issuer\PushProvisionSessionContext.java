package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\PushProvisionSessionContext.smali */
public final class PushProvisionSessionContext extends AbstractSafeParcelable {
    public static final Parcelable.Creator<PushProvisionSessionContext> CREATOR = new zzf();
    private final String zza;
    private final String zzb;
    private final String zzc;

    public PushProvisionSessionContext(String serverSessionId, String deviceId, String walletId) {
        this.zza = serverSessionId;
        this.zzb = deviceId;
        this.zzc = walletId;
    }

    public String getDeviceId() {
        return this.zzb;
    }

    public String getServerSessionId() {
        return this.zza;
    }

    public String getWalletId() {
        return this.zzc;
    }

    public String toString() {
        return String.format("PushProvisionSessionContext{serverSessionId=%s, deviceId=%s, walletId=%s}", this.zza, this.zzb, this.zzc);
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, getServerSessionId(), false);
        SafeParcelWriter.writeString(dest, 2, getDeviceId(), false);
        SafeParcelWriter.writeString(dest, 3, getWalletId(), false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
