package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Util;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.MonthDay;
import java.time.OffsetDateTime;
import java.time.OffsetTime;
import java.time.Period;
import java.time.Year;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers.smali */
public final class TimeSerializers {
    public static void addDefaultSerializers(Kryo kryo) {
        if (Util.isClassAvailable("java.time.Duration")) {
            kryo.addDefaultSerializer(Duration.class, DurationSerializer.class);
        }
        if (Util.isClassAvailable("java.time.Instant")) {
            kryo.addDefaultSerializer(Instant.class, InstantSerializer.class);
        }
        if (Util.isClassAvailable("java.time.LocalDate")) {
            kryo.addDefaultSerializer(LocalDate.class, LocalDateSerializer.class);
        }
        if (Util.isClassAvailable("java.time.LocalTime")) {
            kryo.addDefaultSerializer(LocalTime.class, LocalTimeSerializer.class);
        }
        if (Util.isClassAvailable("java.time.LocalDateTime")) {
            kryo.addDefaultSerializer(LocalDateTime.class, LocalDateTimeSerializer.class);
        }
        if (Util.isClassAvailable("java.time.ZoneOffset")) {
            kryo.addDefaultSerializer(ZoneOffset.class, ZoneOffsetSerializer.class);
        }
        if (Util.isClassAvailable("java.time.ZoneId")) {
            kryo.addDefaultSerializer(ZoneId.class, ZoneIdSerializer.class);
        }
        if (Util.isClassAvailable("java.time.OffsetTime")) {
            kryo.addDefaultSerializer(OffsetTime.class, OffsetTimeSerializer.class);
        }
        if (Util.isClassAvailable("java.time.OffsetDateTime")) {
            kryo.addDefaultSerializer(OffsetDateTime.class, OffsetDateTimeSerializer.class);
        }
        if (Util.isClassAvailable("java.time.ZonedDateTime")) {
            kryo.addDefaultSerializer(ZonedDateTime.class, ZonedDateTimeSerializer.class);
        }
        if (Util.isClassAvailable("java.time.Year")) {
            kryo.addDefaultSerializer(Year.class, YearSerializer.class);
        }
        if (Util.isClassAvailable("java.time.YearMonth")) {
            kryo.addDefaultSerializer(YearMonth.class, YearMonthSerializer.class);
        }
        if (Util.isClassAvailable("java.time.MonthDay")) {
            kryo.addDefaultSerializer(MonthDay.class, MonthDaySerializer.class);
        }
        if (Util.isClassAvailable("java.time.Period")) {
            kryo.addDefaultSerializer(Period.class, PeriodSerializer.class);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$DurationSerializer.smali */
    public static class DurationSerializer extends ImmutableSerializer<Duration> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, Duration duration) {
            out.writeLong(duration.getSeconds());
            out.writeInt(duration.getNano(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Duration read(Kryo kryo, Input in, Class type) {
            long seconds = in.readLong();
            int nanos = in.readInt(true);
            return Duration.ofSeconds(seconds, nanos);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$InstantSerializer.smali */
    public static class InstantSerializer extends ImmutableSerializer<Instant> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, Instant instant) {
            out.writeVarLong(instant.getEpochSecond(), true);
            out.writeInt(instant.getNano(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Instant read(Kryo kryo, Input in, Class type) {
            long seconds = in.readVarLong(true);
            int nanos = in.readInt(true);
            return Instant.ofEpochSecond(seconds, nanos);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$LocalDateSerializer.smali */
    public static class LocalDateSerializer extends ImmutableSerializer<LocalDate> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, LocalDate date) {
            write(out, date);
        }

        static void write(Output out, LocalDate date) {
            out.writeInt(date.getYear(), true);
            out.writeByte(date.getMonthValue());
            out.writeByte(date.getDayOfMonth());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public LocalDate read(Kryo kryo, Input in, Class type) {
            return read(in);
        }

        static LocalDate read(Input in) {
            int year = in.readInt(true);
            int month = in.readByte();
            int dayOfMonth = in.readByte();
            return LocalDate.of(year, month, dayOfMonth);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$LocalDateTimeSerializer.smali */
    public static class LocalDateTimeSerializer extends ImmutableSerializer<LocalDateTime> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, LocalDateTime dateTime) {
            LocalDateSerializer.write(out, dateTime.toLocalDate());
            LocalTimeSerializer.write(out, dateTime.toLocalTime());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public LocalDateTime read(Kryo kryo, Input in, Class type) {
            LocalDate date = LocalDateSerializer.read(in);
            LocalTime time = LocalTimeSerializer.read(in);
            return LocalDateTime.of(date, time);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$LocalTimeSerializer.smali */
    public static class LocalTimeSerializer extends ImmutableSerializer<LocalTime> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, LocalTime time) {
            write(out, time);
        }

        static void write(Output out, LocalTime time) {
            if (time.getNano() == 0) {
                if (time.getSecond() == 0) {
                    if (time.getMinute() == 0) {
                        out.writeByte(~time.getHour());
                        return;
                    } else {
                        out.writeByte(time.getHour());
                        out.writeByte(~time.getMinute());
                        return;
                    }
                }
                out.writeByte(time.getHour());
                out.writeByte(time.getMinute());
                out.writeByte(~time.getSecond());
                return;
            }
            out.writeByte(time.getHour());
            out.writeByte(time.getMinute());
            out.writeByte(time.getSecond());
            out.writeInt(time.getNano(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public LocalTime read(Kryo kryo, Input in, Class type) {
            return read(in);
        }

        static LocalTime read(Input in) {
            int hour = in.readByte();
            int minute = 0;
            int second = 0;
            int nano = 0;
            if (hour < 0) {
                hour = ~hour;
            } else {
                minute = in.readByte();
                if (minute < 0) {
                    minute = ~minute;
                } else {
                    second = in.readByte();
                    if (second < 0) {
                        second = ~second;
                    } else {
                        nano = in.readInt(true);
                    }
                }
            }
            return LocalTime.of(hour, minute, second, nano);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$ZoneOffsetSerializer.smali */
    public static class ZoneOffsetSerializer extends ImmutableSerializer<ZoneOffset> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, ZoneOffset obj) {
            write(out, obj);
        }

        static void write(Output out, ZoneOffset obj) {
            int offsetSecs = obj.getTotalSeconds();
            int offsetByte = offsetSecs % 900 == 0 ? offsetSecs / 900 : 127;
            out.writeByte(offsetByte);
            if (offsetByte == 127) {
                out.writeInt(offsetSecs);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public ZoneOffset read(Kryo kryo, Input in, Class type) {
            return read(in);
        }

        static ZoneOffset read(Input in) {
            int offsetByte = in.readByte();
            return ZoneOffset.ofTotalSeconds(offsetByte == 127 ? in.readInt() : offsetByte * 900);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$ZoneIdSerializer.smali */
    public static class ZoneIdSerializer extends ImmutableSerializer<ZoneId> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, ZoneId obj) {
            write(out, obj);
        }

        static void write(Output out, ZoneId obj) {
            out.writeString(obj.getId());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public ZoneId read(Kryo kryo, Input in, Class type) {
            return read(in);
        }

        static ZoneId read(Input in) {
            String id = in.readString();
            return ZoneId.of(id);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$OffsetTimeSerializer.smali */
    public static class OffsetTimeSerializer extends ImmutableSerializer<OffsetTime> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, OffsetTime obj) {
            LocalTimeSerializer.write(out, obj.toLocalTime());
            ZoneOffsetSerializer.write(out, obj.getOffset());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public OffsetTime read(Kryo kryo, Input in, Class type) {
            LocalTime time = LocalTimeSerializer.read(in);
            ZoneOffset offset = ZoneOffsetSerializer.read(in);
            return OffsetTime.of(time, offset);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$OffsetDateTimeSerializer.smali */
    public static class OffsetDateTimeSerializer extends ImmutableSerializer<OffsetDateTime> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, OffsetDateTime obj) {
            LocalDateSerializer.write(out, obj.toLocalDate());
            LocalTimeSerializer.write(out, obj.toLocalTime());
            ZoneOffsetSerializer.write(out, obj.getOffset());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public OffsetDateTime read(Kryo kryo, Input in, Class type) {
            LocalDate date = LocalDateSerializer.read(in);
            LocalTime time = LocalTimeSerializer.read(in);
            ZoneOffset offset = ZoneOffsetSerializer.read(in);
            return OffsetDateTime.of(date, time, offset);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$ZonedDateTimeSerializer.smali */
    public static class ZonedDateTimeSerializer extends ImmutableSerializer<ZonedDateTime> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, ZonedDateTime obj) {
            LocalDateSerializer.write(out, obj.toLocalDate());
            LocalTimeSerializer.write(out, obj.toLocalTime());
            ZoneIdSerializer.write(out, obj.getZone());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public ZonedDateTime read(Kryo kryo, Input in, Class type) {
            LocalDate date = LocalDateSerializer.read(in);
            LocalTime time = LocalTimeSerializer.read(in);
            ZoneId zone = ZoneIdSerializer.read(in);
            return ZonedDateTime.of(date, time, zone);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$YearSerializer.smali */
    public static class YearSerializer extends ImmutableSerializer<Year> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, Year obj) {
            out.writeVarInt(obj.getValue(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Year read(Kryo kryo, Input in, Class type) {
            return Year.of(in.readInt(true));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$YearMonthSerializer.smali */
    public static class YearMonthSerializer extends ImmutableSerializer<YearMonth> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, YearMonth obj) {
            out.writeVarInt(obj.getYear(), true);
            out.writeByte(obj.getMonthValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public YearMonth read(Kryo kryo, Input in, Class type) {
            int year = in.readInt(true);
            byte month = in.readByte();
            return YearMonth.of(year, month);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$MonthDaySerializer.smali */
    public static class MonthDaySerializer extends ImmutableSerializer<MonthDay> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, MonthDay obj) {
            out.writeByte(obj.getMonthValue());
            out.writeByte(obj.getDayOfMonth());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public MonthDay read(Kryo kryo, Input in, Class type) {
            byte month = in.readByte();
            byte day = in.readByte();
            return MonthDay.of(month, day);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TimeSerializers$PeriodSerializer.smali */
    public static class PeriodSerializer extends ImmutableSerializer<Period> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output out, Period obj) {
            out.writeVarInt(obj.getYears(), true);
            out.writeVarInt(obj.getMonths(), true);
            out.writeVarInt(obj.getDays(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Period read(Kryo kryo, Input in, Class type) {
            int years = in.readInt(true);
            int months = in.readInt(true);
            int days = in.readInt(true);
            return Period.of(years, months, days);
        }
    }
}
