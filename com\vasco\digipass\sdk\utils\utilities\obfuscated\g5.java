package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\g5.smali */
public class g5 {
    private SecureRandom a;
    private int b;

    public g5(SecureRandom secureRandom, int i) {
        this.a = t1.a(secureRandom);
        this.b = i;
    }

    public SecureRandom getRandom() {
        return this.a;
    }

    public int getStrength() {
        return this.b;
    }
}
