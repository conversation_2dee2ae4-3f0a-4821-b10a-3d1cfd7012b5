package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import java.lang.reflect.Array;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers.smali */
public class DefaultArraySerializers {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$ByteArraySerializer.smali */
    public static class ByteArraySerializer extends Serializer<byte[]> {
        public ByteArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, byte[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeBytes(object);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public byte[] read(Kryo kryo, Input input, Class<? extends byte[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readBytes(length - 1);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public byte[] copy(Kryo kryo, byte[] original) {
            byte[] copy = new byte[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$IntArraySerializer.smali */
    public static class IntArraySerializer extends Serializer<int[]> {
        public IntArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, int[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeInts(object, 0, object.length, false);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public int[] read(Kryo kryo, Input input, Class<? extends int[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readInts(length - 1, false);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public int[] copy(Kryo kryo, int[] original) {
            int[] copy = new int[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$FloatArraySerializer.smali */
    public static class FloatArraySerializer extends Serializer<float[]> {
        public FloatArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, float[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeFloats(object, 0, object.length);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public float[] read(Kryo kryo, Input input, Class<? extends float[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readFloats(length - 1);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public float[] copy(Kryo kryo, float[] original) {
            float[] copy = new float[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$LongArraySerializer.smali */
    public static class LongArraySerializer extends Serializer<long[]> {
        public LongArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, long[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeLongs(object, 0, object.length, false);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public long[] read(Kryo kryo, Input input, Class<? extends long[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readLongs(length - 1, false);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public long[] copy(Kryo kryo, long[] original) {
            long[] copy = new long[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$ShortArraySerializer.smali */
    public static class ShortArraySerializer extends Serializer<short[]> {
        public ShortArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, short[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeShorts(object, 0, object.length);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public short[] read(Kryo kryo, Input input, Class<? extends short[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readShorts(length - 1);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public short[] copy(Kryo kryo, short[] original) {
            short[] copy = new short[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$CharArraySerializer.smali */
    public static class CharArraySerializer extends Serializer<char[]> {
        public CharArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, char[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeChars(object, 0, object.length);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public char[] read(Kryo kryo, Input input, Class<? extends char[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readChars(length - 1);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public char[] copy(Kryo kryo, char[] original) {
            char[] copy = new char[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$DoubleArraySerializer.smali */
    public static class DoubleArraySerializer extends Serializer<double[]> {
        public DoubleArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, double[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else {
                output.writeVarInt(object.length + 1, true);
                output.writeDoubles(object, 0, object.length);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public double[] read(Kryo kryo, Input input, Class<? extends double[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            return input.readDoubles(length - 1);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public double[] copy(Kryo kryo, double[] original) {
            double[] copy = new double[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$BooleanArraySerializer.smali */
    public static class BooleanArraySerializer extends Serializer<boolean[]> {
        public BooleanArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, boolean[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
                return;
            }
            output.writeVarInt(object.length + 1, true);
            for (boolean z : object) {
                output.writeBoolean(z);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public boolean[] read(Kryo kryo, Input input, Class<? extends boolean[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            int length2 = length - 1;
            boolean[] array = new boolean[length2];
            for (int i = 0; i < length2; i++) {
                array[i] = input.readBoolean();
            }
            return array;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public boolean[] copy(Kryo kryo, boolean[] original) {
            boolean[] copy = new boolean[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$StringArraySerializer.smali */
    public static class StringArraySerializer extends Serializer<String[]> {
        public StringArraySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, String[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
                return;
            }
            output.writeVarInt(object.length + 1, true);
            if (kryo.getReferences() && kryo.getReferenceResolver().useReferences(String.class)) {
                Serializer serializer = kryo.getSerializer(String.class);
                for (String str : object) {
                    kryo.writeObjectOrNull(output, str, serializer);
                }
                return;
            }
            for (String str2 : object) {
                output.writeString(str2);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public String[] read(Kryo kryo, Input input, Class<? extends String[]> cls) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            int length2 = length - 1;
            String[] array = new String[length2];
            if (kryo.getReferences() && kryo.getReferenceResolver().useReferences(String.class)) {
                Serializer serializer = kryo.getSerializer(String.class);
                for (int i = 0; i < length2; i++) {
                    array[i] = (String) kryo.readObjectOrNull(input, String.class, serializer);
                }
            } else {
                for (int i2 = 0; i2 < length2; i2++) {
                    array[i2] = input.readString();
                }
            }
            return array;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public String[] copy(Kryo kryo, String[] original) {
            String[] copy = new String[original.length];
            System.arraycopy(original, 0, copy, 0, copy.length);
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultArraySerializers$ObjectArraySerializer.smali */
    public static class ObjectArraySerializer extends Serializer<Object[]> {
        private boolean elementsAreSameType;
        private boolean elementsCanBeNull = true;
        private final Class type;

        public ObjectArraySerializer(Kryo kryo, Class type) {
            setAcceptsNull(true);
            this.type = type;
            Class componentType = type.getComponentType();
            boolean isFinal = (componentType.getModifiers() & 16) != 0;
            if (isFinal) {
                setElementsAreSameType(true);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Object[] object) {
            if (object == null) {
                output.writeByte((byte) 0);
                return;
            }
            int n = object.length;
            output.writeVarInt(n + 1, true);
            Class elementClass = object.getClass().getComponentType();
            if (this.elementsAreSameType || kryo.isFinal(elementClass)) {
                Serializer elementSerializer = kryo.getSerializer(elementClass);
                if (this.elementsCanBeNull) {
                    for (Object obj : object) {
                        kryo.writeObjectOrNull(output, obj, elementSerializer);
                    }
                    return;
                }
                for (Object obj2 : object) {
                    kryo.writeObject(output, obj2, elementSerializer);
                }
                return;
            }
            for (Object obj3 : object) {
                kryo.writeClassAndObject(output, obj3);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Object[] read(Kryo kryo, Input input, Class<? extends Object[]> cls) {
            int n = input.readVarInt(true);
            if (n == 0) {
                return null;
            }
            int n2 = n - 1;
            Object[] object = (Object[]) Array.newInstance(cls.getComponentType(), n2);
            kryo.reference(object);
            Class elementClass = cls.getComponentType();
            if (this.elementsAreSameType || kryo.isFinal(elementClass)) {
                Serializer elementSerializer = kryo.getSerializer(elementClass);
                if (this.elementsCanBeNull) {
                    for (int i = 0; i < n2; i++) {
                        object[i] = kryo.readObjectOrNull(input, elementClass, elementSerializer);
                    }
                } else {
                    for (int i2 = 0; i2 < n2; i2++) {
                        object[i2] = kryo.readObject(input, elementClass, elementSerializer);
                    }
                }
            } else {
                for (int i3 = 0; i3 < n2; i3++) {
                    object[i3] = kryo.readClassAndObject(input);
                }
            }
            return object;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Object[] copy(Kryo kryo, Object[] original) {
            int n = original.length;
            Object[] copy = (Object[]) Array.newInstance(original.getClass().getComponentType(), n);
            kryo.reference(copy);
            for (int i = 0; i < n; i++) {
                copy[i] = kryo.copy(original[i]);
            }
            return copy;
        }

        public void setElementsCanBeNull(boolean elementsCanBeNull) {
            this.elementsCanBeNull = elementsCanBeNull;
        }

        public void setElementsAreSameType(boolean elementsAreSameType) {
            this.elementsAreSameType = elementsAreSameType;
        }
    }
}
