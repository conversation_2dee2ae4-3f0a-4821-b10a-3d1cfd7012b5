package com.capacitorjs.plugins.localnotifications;

import androidx.webkit.ProxyConfig;
import java.util.Calendar;
import java.util.Date;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\capacitorjs\plugins\localnotifications\DateMatch.smali */
public class DateMatch {
    private static final String separator = " ";
    private Integer day;
    private Integer hour;
    private Integer minute;
    private Integer month;
    private Integer second;
    private Integer unit = -1;
    private Integer weekday;
    private Integer year;

    public Integer getYear() {
        return this.year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return this.month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getDay() {
        return this.day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Integer getWeekday() {
        return this.weekday;
    }

    public void setWeekday(Integer weekday) {
        this.weekday = weekday;
    }

    public Integer getHour() {
        return this.hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }

    public Integer getMinute() {
        return this.minute;
    }

    public void setMinute(Integer minute) {
        this.minute = minute;
    }

    public Integer getSecond() {
        return this.second;
    }

    public void setSecond(Integer second) {
        this.second = second;
    }

    private Calendar buildCalendar(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(14, 0);
        return cal;
    }

    public long nextTrigger(Date date) {
        Calendar current = buildCalendar(date);
        Calendar next = buildNextTriggerTime(date);
        return postponeTriggerIfNeeded(current, next);
    }

    private long postponeTriggerIfNeeded(Calendar current, Calendar next) {
        if (next.getTimeInMillis() <= current.getTimeInMillis() && this.unit.intValue() != -1) {
            Integer incrementUnit = -1;
            if (this.unit.intValue() == 1 || this.unit.intValue() == 2) {
                incrementUnit = 1;
            } else if (this.unit.intValue() == 5) {
                incrementUnit = 2;
            } else if (this.unit.intValue() == 7) {
                incrementUnit = 4;
            } else if (this.unit.intValue() == 11) {
                incrementUnit = 5;
            } else if (this.unit.intValue() == 12) {
                incrementUnit = 11;
            } else if (this.unit.intValue() == 13) {
                incrementUnit = 12;
            }
            if (incrementUnit.intValue() != -1) {
                next.set(incrementUnit.intValue(), next.get(incrementUnit.intValue()) + 1);
            }
        }
        return next.getTimeInMillis();
    }

    private Calendar buildNextTriggerTime(Date date) {
        Calendar next = buildCalendar(date);
        Integer num = this.year;
        if (num != null) {
            next.set(1, num.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 1;
            }
        }
        Integer num2 = this.month;
        if (num2 != null) {
            next.set(2, num2.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 2;
            }
        }
        Integer num3 = this.day;
        if (num3 != null) {
            next.set(5, num3.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 5;
            }
        }
        Integer num4 = this.weekday;
        if (num4 != null) {
            next.set(7, num4.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 7;
            }
        }
        Integer num5 = this.hour;
        if (num5 != null) {
            next.set(11, num5.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 11;
            }
        }
        Integer num6 = this.minute;
        if (num6 != null) {
            next.set(12, num6.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 12;
            }
        }
        Integer num7 = this.second;
        if (num7 != null) {
            next.set(13, num7.intValue());
            if (this.unit.intValue() == -1) {
                this.unit = 13;
            }
        }
        return next;
    }

    public String toString() {
        return "DateMatch{year=" + this.year + ", month=" + this.month + ", day=" + this.day + ", weekday=" + this.weekday + ", hour=" + this.hour + ", minute=" + this.minute + ", second=" + this.second + '}';
    }

    public boolean equals(Object o2) {
        if (this == o2) {
            return true;
        }
        if (o2 == null || getClass() != o2.getClass()) {
            return false;
        }
        DateMatch dateMatch = (DateMatch) o2;
        Integer num = this.year;
        if (num == null ? dateMatch.year != null : !num.equals(dateMatch.year)) {
            return false;
        }
        Integer num2 = this.month;
        if (num2 == null ? dateMatch.month != null : !num2.equals(dateMatch.month)) {
            return false;
        }
        Integer num3 = this.day;
        if (num3 == null ? dateMatch.day != null : !num3.equals(dateMatch.day)) {
            return false;
        }
        Integer num4 = this.weekday;
        if (num4 == null ? dateMatch.weekday != null : !num4.equals(dateMatch.weekday)) {
            return false;
        }
        Integer num5 = this.hour;
        if (num5 == null ? dateMatch.hour != null : !num5.equals(dateMatch.hour)) {
            return false;
        }
        Integer num6 = this.minute;
        if (num6 == null ? dateMatch.minute != null : !num6.equals(dateMatch.minute)) {
            return false;
        }
        Integer num7 = this.second;
        if (num7 != null) {
            return num7.equals(dateMatch.second);
        }
        if (dateMatch.second == null) {
            return true;
        }
        return false;
    }

    public int hashCode() {
        Integer num = this.year;
        int result = num != null ? num.hashCode() : 0;
        int i = result * 31;
        Integer num2 = this.month;
        int result2 = i + (num2 != null ? num2.hashCode() : 0);
        int result3 = result2 * 31;
        Integer num3 = this.day;
        int result4 = (result3 + (num3 != null ? num3.hashCode() : 0)) * 31;
        Integer num4 = this.weekday;
        int result5 = (result4 + (num4 != null ? num4.hashCode() : 0)) * 31;
        Integer num5 = this.hour;
        int result6 = (result5 + (num5 != null ? num5.hashCode() : 0)) * 31;
        Integer num6 = this.minute;
        int result7 = result6 + (num6 != null ? num6.hashCode() : 0) + 31;
        Integer num7 = this.second;
        return result7 + (num7 != null ? num7.hashCode() : 0);
    }

    public String toMatchString() {
        String matchString = this.year + separator + this.month + separator + this.day + separator + this.weekday + separator + this.hour + separator + this.minute + separator + this.second + separator + this.unit;
        return matchString.replace("null", ProxyConfig.MATCH_ALL_SCHEMES);
    }

    public static DateMatch fromMatchString(String matchString) {
        DateMatch date = new DateMatch();
        String[] split = matchString.split(separator);
        if (split != null && split.length == 7) {
            date.setYear(getValueFromCronElement(split[0]));
            date.setMonth(getValueFromCronElement(split[1]));
            date.setDay(getValueFromCronElement(split[2]));
            date.setWeekday(getValueFromCronElement(split[3]));
            date.setHour(getValueFromCronElement(split[4]));
            date.setMinute(getValueFromCronElement(split[5]));
            date.setUnit(getValueFromCronElement(split[6]));
        }
        if (split != null && split.length == 8) {
            date.setYear(getValueFromCronElement(split[0]));
            date.setMonth(getValueFromCronElement(split[1]));
            date.setDay(getValueFromCronElement(split[2]));
            date.setWeekday(getValueFromCronElement(split[3]));
            date.setHour(getValueFromCronElement(split[4]));
            date.setMinute(getValueFromCronElement(split[5]));
            date.setSecond(getValueFromCronElement(split[6]));
            date.setUnit(getValueFromCronElement(split[7]));
        }
        return date;
    }

    public static Integer getValueFromCronElement(String token) {
        try {
            return Integer.valueOf(Integer.parseInt(token));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    public Integer getUnit() {
        return this.unit;
    }

    public void setUnit(Integer unit) {
        this.unit = unit;
    }
}
