package com.esotericsoftware.kryo.io;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import java.io.IOException;
import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\OutputChunked.smali */
public class OutputChunked extends Output {
    public OutputChunked() {
    }

    public OutputChunked(int bufferSize) {
        super(bufferSize);
    }

    public OutputChunked(OutputStream outputStream) {
        super(outputStream);
    }

    public OutputChunked(OutputStream outputStream, int bufferSize) {
        super(outputStream, bufferSize);
    }

    @Override // com.esotericsoftware.kryo.io.Output, java.io.OutputStream, java.io.Flushable
    public void flush() throws KryoException {
        if (position() > 0) {
            try {
                writeChunkSize();
                super.flush();
                return;
            } catch (IOException ex) {
                throw new KryoException(ex);
            }
        }
        super.flush();
    }

    private void writeChunkSize() throws IOException {
        int size = position();
        if (Log.TRACE) {
            Log.trace("kryo", "Write chunk: " + size + Util.pos(size));
        }
        OutputStream outputStream = getOutputStream();
        if ((size & (-128)) == 0) {
            outputStream.write(size);
            return;
        }
        outputStream.write((size & 127) | 128);
        int size2 = size >>> 7;
        if ((size2 & (-128)) == 0) {
            outputStream.write(size2);
            return;
        }
        outputStream.write((size2 & 127) | 128);
        int size3 = size2 >>> 7;
        if ((size3 & (-128)) == 0) {
            outputStream.write(size3);
            return;
        }
        outputStream.write((size3 & 127) | 128);
        int size4 = size3 >>> 7;
        if ((size4 & (-128)) == 0) {
            outputStream.write(size4);
        } else {
            outputStream.write((size4 & 127) | 128);
            outputStream.write(size4 >>> 7);
        }
    }

    public void endChunk() {
        flush();
        if (Log.TRACE) {
            Log.trace("kryo", "End chunk.");
        }
        try {
            getOutputStream().write(0);
        } catch (IOException ex) {
            throw new KryoException(ex);
        }
    }
}
