package bc.org.bouncycastle.crypto.modes;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.params.ParametersWithIV;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.i1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v3;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\modes\CBCBlockCipher.smali */
public class CBCBlockCipher extends v3 implements i1 {
    private byte[] a;
    private byte[] b;
    private byte[] c;
    private int d;
    private BlockCipher e;
    private boolean f;

    public CBCBlockCipher(BlockCipher blockCipher) {
        this.e = blockCipher;
        int blockSize = blockCipher.getBlockSize();
        this.d = blockSize;
        this.a = new byte[blockSize];
        this.b = new byte[blockSize];
        this.c = new byte[blockSize];
    }

    private int a(byte[] bArr, int i, byte[] bArr2, int i2) throws DataLengthException, IllegalStateException {
        int i3 = this.d;
        if (i + i3 > bArr.length) {
            throw new DataLengthException("input buffer too short");
        }
        System.arraycopy(bArr, i, this.c, 0, i3);
        int processBlock = this.e.processBlock(bArr, i, bArr2, i2);
        for (int i4 = 0; i4 < this.d; i4++) {
            int i5 = i2 + i4;
            bArr2[i5] = (byte) (bArr2[i5] ^ this.b[i4]);
        }
        byte[] bArr3 = this.b;
        this.b = this.c;
        this.c = bArr3;
        return processBlock;
    }

    private int b(byte[] bArr, int i, byte[] bArr2, int i2) throws DataLengthException, IllegalStateException {
        if (this.d + i > bArr.length) {
            throw new DataLengthException("input buffer too short");
        }
        for (int i3 = 0; i3 < this.d; i3++) {
            byte[] bArr3 = this.b;
            bArr3[i3] = (byte) (bArr3[i3] ^ bArr[i + i3]);
        }
        int processBlock = this.e.processBlock(this.b, 0, bArr2, i2);
        byte[] bArr4 = this.b;
        System.arraycopy(bArr2, i2, bArr4, 0, bArr4.length);
        return processBlock;
    }

    public static i1 newInstance(BlockCipher blockCipher) {
        return new CBCBlockCipher(blockCipher);
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return this.e.getAlgorithmName() + "/CBC";
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return this.e.getBlockSize();
    }

    public BlockCipher getUnderlyingCipher() {
        return this.e;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        boolean z2 = this.f;
        this.f = z;
        if (cipherParameters instanceof ParametersWithIV) {
            ParametersWithIV parametersWithIV = (ParametersWithIV) cipherParameters;
            byte[] iv = parametersWithIV.getIV();
            if (iv.length != this.d) {
                throw new IllegalArgumentException("initialisation vector must be the same length as block size");
            }
            System.arraycopy(iv, 0, this.a, 0, iv.length);
            cipherParameters = parametersWithIV.getParameters();
        } else {
            Arrays.fill(this.a, (byte) 0);
        }
        reset();
        if (cipherParameters != null) {
            this.e.init(z, cipherParameters);
        } else if (z2 != z) {
            throw new IllegalArgumentException("cannot change encrypting state without providing key.");
        }
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i, byte[] bArr2, int i2) throws DataLengthException, IllegalStateException {
        return this.f ? b(bArr, i, bArr2, i2) : a(bArr, i, bArr2, i2);
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void reset() {
        byte[] bArr = this.a;
        System.arraycopy(bArr, 0, this.b, 0, bArr.length);
        Arrays.fill(this.c, (byte) 0);
        this.e.reset();
    }
}
