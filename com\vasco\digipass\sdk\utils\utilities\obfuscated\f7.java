package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CryptoException;
import bc.org.bouncycastle.crypto.Digest;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f7.smali */
public class f7 {
    protected BigInteger a;
    protected BigInteger b;
    protected BigInteger c;
    protected SecureRandom d;
    protected Digest e;
    protected BigInteger f;
    protected BigInteger g;
    protected BigInteger h;
    protected BigInteger i;
    protected BigInteger j;
    protected BigInteger k;
    protected BigInteger l;
    protected BigInteger m;

    private BigInteger a() {
        return this.c.modPow(this.i, this.a).multiply(this.f).mod(this.a).modPow(this.g, this.a);
    }

    protected BigInteger b() {
        return g7.generatePrivateValue(this.e, this.a, this.b, this.d);
    }

    public BigInteger calculateSecret(BigInteger bigInteger) throws CryptoException {
        BigInteger validatePublicValue = g7.validatePublicValue(this.a, bigInteger);
        this.f = validatePublicValue;
        this.i = g7.calculateU(this.e, this.a, validatePublicValue, this.h);
        BigInteger a = a();
        this.j = a;
        return a;
    }

    public BigInteger calculateServerEvidenceMessage() throws CryptoException {
        BigInteger bigInteger;
        BigInteger bigInteger2;
        BigInteger bigInteger3 = this.f;
        if (bigInteger3 == null || (bigInteger = this.k) == null || (bigInteger2 = this.j) == null) {
            throw new CryptoException("Impossible to compute M2: some data are missing from the previous operations (A,M1,S)");
        }
        BigInteger calculateM2 = g7.calculateM2(this.e, this.a, bigInteger3, bigInteger, bigInteger2);
        this.l = calculateM2;
        return calculateM2;
    }

    public BigInteger calculateSessionKey() throws CryptoException {
        BigInteger bigInteger = this.j;
        if (bigInteger == null || this.k == null || this.l == null) {
            throw new CryptoException("Impossible to compute Key: some data are missing from the previous operations (S,M1,M2)");
        }
        BigInteger calculateKey = g7.calculateKey(this.e, this.a, bigInteger);
        this.m = calculateKey;
        return calculateKey;
    }

    public BigInteger generateServerCredentials() {
        BigInteger calculateK = g7.calculateK(this.e, this.a, this.b);
        this.g = b();
        BigInteger mod = calculateK.multiply(this.c).mod(this.a).add(this.b.modPow(this.g, this.a)).mod(this.a);
        this.h = mod;
        return mod;
    }

    public void init(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, Digest digest, SecureRandom secureRandom) {
        this.a = bigInteger;
        this.b = bigInteger2;
        this.c = bigInteger3;
        this.d = secureRandom;
        this.e = digest;
    }

    public boolean verifyClientEvidenceMessage(BigInteger bigInteger) throws CryptoException {
        BigInteger bigInteger2;
        BigInteger bigInteger3;
        BigInteger bigInteger4 = this.f;
        if (bigInteger4 == null || (bigInteger2 = this.h) == null || (bigInteger3 = this.j) == null) {
            throw new CryptoException("Impossible to compute and verify M1: some data are missing from the previous operations (A,B,S)");
        }
        if (!g7.calculateM1(this.e, this.a, bigInteger4, bigInteger2, bigInteger3).equals(bigInteger)) {
            return false;
        }
        this.k = bigInteger;
        return true;
    }

    public void init(e7 e7Var, BigInteger bigInteger, Digest digest, SecureRandom secureRandom) {
        throw null;
    }
}
