package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\n0.smali */
public abstract class n0 extends b0 {
    final byte[] b;
    static final o0 x = new a(n0.class, 28);
    private static final char[] C = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\n0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return n0.b(f2Var.h());
        }
    }

    n0(byte[] bArr, boolean z) {
        this.b = z ? Arrays.clone(bArr) : bArr;
    }

    static n0 b(byte[] bArr) {
        return new o2(bArr, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean e() {
        return false;
    }

    public final String h() {
        int length = this.b.length;
        StringBuffer stringBuffer = new StringBuffer(((z.a(length) + length) * 2) + 3);
        stringBuffer.append("#1C");
        b(stringBuffer, length);
        for (int i = 0; i < length; i++) {
            a(stringBuffer, this.b[i]);
        }
        return stringBuffer.toString();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public final int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public String toString() {
        return h();
    }

    private static void b(StringBuffer stringBuffer, int i) {
        if (i < 128) {
            a(stringBuffer, i);
            return;
        }
        byte[] bArr = new byte[5];
        int i2 = 5;
        do {
            i2--;
            bArr[i2] = (byte) i;
            i >>>= 8;
        } while (i != 0);
        int i3 = 5 - i2;
        int i4 = i2 - 1;
        bArr[i4] = (byte) (i3 | 128);
        while (true) {
            int i5 = i4 + 1;
            a(stringBuffer, bArr[i4]);
            if (i5 >= 5) {
                return;
            } else {
                i4 = i5;
            }
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 28, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean a(b0 b0Var) {
        if (b0Var instanceof n0) {
            return Arrays.areEqual(this.b, ((n0) b0Var).b);
        }
        return false;
    }

    private static void a(StringBuffer stringBuffer, int i) {
        char[] cArr = C;
        stringBuffer.append(cArr[(i >>> 4) & 15]);
        stringBuffer.append(cArr[i & 15]);
    }
}
