package androidx.webkit.internal;

import android.net.Uri;
import android.webkit.WebResourceRequest;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\webkit\internal\ApiHelperForLollipop.smali */
public class ApiHelperForLollipop {
    private ApiHelperForLollipop() {
    }

    public static boolean isForMainFrame(WebResourceRequest webResourceRequest) {
        return webResourceRequest.isForMainFrame();
    }

    public static Uri getUrl(WebResourceRequest webResourceRequest) {
        return webResourceRequest.getUrl();
    }
}
