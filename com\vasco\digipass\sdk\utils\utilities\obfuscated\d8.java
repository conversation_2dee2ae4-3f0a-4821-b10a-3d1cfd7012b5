package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.util.Arrays;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d8.smali */
public class d8 extends u implements j8 {
    private w C;
    private ECCurve b;
    private byte[] x;

    public d8(ECCurve eCCurve, byte[] bArr) {
        this.C = null;
        this.b = eCCurve;
        this.x = Arrays.clone(bArr);
        e();
    }

    private void e() {
        if (ECAlgorithms.isFpCurve(this.b)) {
            this.C = j8.d;
        } else {
            if (!ECAlgorithms.isF2mCurve(this.b)) {
                throw new IllegalArgumentException("This type of ECCurve is not implemented");
            }
            this.C = j8.e;
        }
    }

    public ECCurve getCurve() {
        return this.b;
    }

    public byte[] getSeed() {
        return Arrays.clone(this.x);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(3);
        if (this.C.b(j8.d)) {
            iVar.a(new g8(this.b.getA()).toASN1Primitive());
            iVar.a(new g8(this.b.getB()).toASN1Primitive());
        } else if (this.C.b(j8.e)) {
            iVar.a(new g8(this.b.getA()).toASN1Primitive());
            iVar.a(new g8(this.b.getB()).toASN1Primitive());
        }
        if (this.x != null) {
            iVar.a(new w1(this.x));
        }
        return new j2(iVar);
    }

    public d8(h8 h8Var, BigInteger bigInteger, BigInteger bigInteger2, e0 e0Var) {
        int k;
        int i;
        int i2;
        this.C = null;
        w e = h8Var.e();
        this.C = e;
        if (e.b(j8.d)) {
            this.b = new ECCurve.Fp(((r) h8Var.f()).i(), new BigInteger(1, x.a(e0Var.a(0)).h()), new BigInteger(1, x.a(e0Var.a(1)).h()), bigInteger, bigInteger2);
        } else if (this.C.b(j8.e)) {
            e0 a = e0.a((Object) h8Var.f());
            int k2 = ((r) a.a(0)).k();
            w wVar = (w) a.a(1);
            if (wVar.b(j8.g)) {
                i = r.a(a.a(2)).k();
                i2 = 0;
                k = 0;
            } else if (wVar.b(j8.h)) {
                e0 a2 = e0.a(a.a(2));
                int k3 = r.a(a2.a(0)).k();
                int k4 = r.a(a2.a(1)).k();
                k = r.a(a2.a(2)).k();
                i = k3;
                i2 = k4;
            } else {
                throw new IllegalArgumentException("This type of EC basis is not implemented");
            }
            this.b = new ECCurve.F2m(k2, i, i2, k, new BigInteger(1, x.a(e0Var.a(0)).h()), new BigInteger(1, x.a(e0Var.a(1)).h()), bigInteger, bigInteger2);
        } else {
            throw new IllegalArgumentException("This type of ECCurve is not implemented");
        }
        if (e0Var.size() == 3) {
            this.x = ((w1) e0Var.a(2)).h();
        }
    }
}
