package com.rolster.capacitor.otp;

import android.app.Activity;
import com.getcapacitor.PluginCall;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes5\com\rolster\capacitor\otp\OtpManagerResolver.smali */
public interface OtpManagerResolver {
    void execute(OtpReceiveListener otpReceiveListener, Activity activity, PluginCall pluginCall);
}
