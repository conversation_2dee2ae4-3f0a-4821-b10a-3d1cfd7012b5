package com.google.android.play.core.appupdate;

import android.content.Context;

/* compiled from: com.google.android.play:app-update@@2.1.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\appupdate\zzb.smali */
public final class zzb {
    private static zza zza;

    static synchronized zza zza(Context context) {
        zza zzaVar;
        synchronized (zzb.class) {
            if (zza == null) {
                zzab zzabVar = new zzab(null);
                zzabVar.zzb(new zzi(com.google.android.play.core.appupdate.internal.zzz.zza(context)));
                zza = zzabVar.zza();
            }
            zzaVar = zza;
        }
        return zzaVar;
    }
}
