package o.du;

import android.content.Context;
import o.dq.d;
import o.dx.a;
import o.dy.e;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\d.smali */
public interface d<Type extends o.dy.e, Source extends o.dx.a, Decoder extends o.dq.d<ObjRes>, ObjRes> {
    Decoder a();

    Type b();

    String c();

    Source d();

    default ObjRes b(Context context) {
        try {
            return (ObjRes) a.a().c(context, this);
        } catch (e e) {
            g.c();
            g.a("IRemoteResource", "getData - Unable to get data", e);
            return null;
        }
    }
}
