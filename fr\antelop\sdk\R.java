package fr.antelop.sdk;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$anim.smali */
    public static final class anim {
        public static int abc_fade_in = 0x7f010000;
        public static int abc_fade_out = 0x7f010001;
        public static int abc_grow_fade_in_from_bottom = 0x7f010002;
        public static int abc_popup_enter = 0x7f010003;
        public static int abc_popup_exit = 0x7f010004;
        public static int abc_shrink_fade_out_from_bottom = 0x7f010005;
        public static int abc_slide_in_bottom = 0x7f010006;
        public static int abc_slide_in_top = 0x7f010007;
        public static int abc_slide_out_bottom = 0x7f010008;
        public static int abc_slide_out_top = 0x7f010009;
        public static int abc_tooltip_enter = 0x7f01000a;
        public static int abc_tooltip_exit = 0x7f01000b;
        public static int btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c;
        public static int btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d;
        public static int btn_checkbox_to_checked_icon_null_animation = 0x7f01000e;
        public static int btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f;
        public static int btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010;
        public static int btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011;
        public static int btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012;
        public static int btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013;
        public static int btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014;
        public static int btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015;
        public static int btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016;
        public static int btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017;
        public static int design_bottom_sheet_slide_in = 0x7f010018;
        public static int design_bottom_sheet_slide_out = 0x7f010019;
        public static int design_snackbar_in = 0x7f01001a;
        public static int design_snackbar_out = 0x7f01001b;
        public static int fragment_fast_out_extra_slow_in = 0x7f01001c;
        public static int mtrl_bottom_sheet_slide_in = 0x7f01001d;
        public static int mtrl_bottom_sheet_slide_out = 0x7f01001e;
        public static int mtrl_card_lowers_interpolator = 0x7f01001f;

        private anim() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$animator.smali */
    public static final class animator {
        public static int design_appbar_state_list_animator = 0x7f020000;
        public static int design_fab_hide_motion_spec = 0x7f020001;
        public static int design_fab_show_motion_spec = 0x7f020002;
        public static int fragment_close_enter = 0x7f020003;
        public static int fragment_close_exit = 0x7f020004;
        public static int fragment_fade_enter = 0x7f020005;
        public static int fragment_fade_exit = 0x7f020006;
        public static int fragment_open_enter = 0x7f020007;
        public static int fragment_open_exit = 0x7f020008;
        public static int mtrl_btn_state_list_anim = 0x7f020009;
        public static int mtrl_btn_unelevated_state_list_anim = 0x7f02000a;
        public static int mtrl_card_state_list_anim = 0x7f02000b;
        public static int mtrl_chip_state_list_anim = 0x7f02000c;
        public static int mtrl_extended_fab_hide_motion_spec = 0x7f02000e;
        public static int mtrl_extended_fab_show_motion_spec = 0x7f02000f;
        public static int mtrl_extended_fab_state_list_animator = 0x7f020010;
        public static int mtrl_fab_hide_motion_spec = 0x7f020011;
        public static int mtrl_fab_show_motion_spec = 0x7f020012;
        public static int mtrl_fab_transformation_sheet_collapse_spec = 0x7f020013;
        public static int mtrl_fab_transformation_sheet_expand_spec = 0x7f020014;
        public static int tp_progress_animation = 0x7f020015;

        private animator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$array.smali */
    public static final class array {
        public static int crypto_fingerprint_fallback_prefixes = 0x7f030000;
        public static int crypto_fingerprint_fallback_vendors = 0x7f030001;
        public static int hide_fingerprint_instantly_prefixes = 0x7f030002;

        private array() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$attr.smali */
    public static final class attr {
        public static int actionBarDivider = 0x7f040001;
        public static int actionBarItemBackground = 0x7f040002;
        public static int actionBarPopupTheme = 0x7f040003;
        public static int actionBarSize = 0x7f040004;
        public static int actionBarSplitStyle = 0x7f040005;
        public static int actionBarStyle = 0x7f040006;
        public static int actionBarTabBarStyle = 0x7f040007;
        public static int actionBarTabStyle = 0x7f040008;
        public static int actionBarTabTextStyle = 0x7f040009;
        public static int actionBarTheme = 0x7f04000a;
        public static int actionBarWidgetTheme = 0x7f04000b;
        public static int actionButtonStyle = 0x7f04000c;
        public static int actionDropDownStyle = 0x7f04000d;
        public static int actionLayout = 0x7f04000e;
        public static int actionMenuTextAppearance = 0x7f04000f;
        public static int actionMenuTextColor = 0x7f040010;
        public static int actionModeBackground = 0x7f040011;
        public static int actionModeCloseButtonStyle = 0x7f040012;
        public static int actionModeCloseContentDescription = 0x7f040013;
        public static int actionModeCloseDrawable = 0x7f040014;
        public static int actionModeCopyDrawable = 0x7f040015;
        public static int actionModeCutDrawable = 0x7f040016;
        public static int actionModeFindDrawable = 0x7f040017;
        public static int actionModePasteDrawable = 0x7f040018;
        public static int actionModePopupWindowStyle = 0x7f040019;
        public static int actionModeSelectAllDrawable = 0x7f04001a;
        public static int actionModeShareDrawable = 0x7f04001b;
        public static int actionModeSplitBackground = 0x7f04001c;
        public static int actionModeStyle = 0x7f04001d;
        public static int actionModeTheme = 0x7f04001e;
        public static int actionModeWebSearchDrawable = 0x7f04001f;
        public static int actionOverflowButtonStyle = 0x7f040020;
        public static int actionOverflowMenuStyle = 0x7f040021;
        public static int actionProviderClass = 0x7f040022;
        public static int actionTextColorAlpha = 0x7f040023;
        public static int actionViewClass = 0x7f040024;
        public static int activityChooserViewStyle = 0x7f040025;
        public static int alertDialogButtonGroupStyle = 0x7f040026;
        public static int alertDialogCenterButtons = 0x7f040027;
        public static int alertDialogStyle = 0x7f040028;
        public static int alertDialogTheme = 0x7f040029;
        public static int allowStacking = 0x7f04002a;
        public static int alpha = 0x7f04002b;
        public static int alphabeticModifiers = 0x7f04002c;
        public static int animationMode = 0x7f04002d;
        public static int antelopCardDisplayTheme_activityBackground_style = 0x7f04002e;
        public static int antelopCardDisplayTheme_cardBackground_cornerRadius = 0x7f04002f;
        public static int antelopCardDisplayTheme_cardBackground_image = 0x7f040030;
        public static int antelopCardDisplayTheme_cardBackground_style = 0x7f040031;
        public static int antelopCardDisplayTheme_cardMargin = 0x7f040032;
        public static int antelopCardDisplayTheme_closeButton_description = 0x7f040033;
        public static int antelopCardDisplayTheme_closeButton_enabled = 0x7f040034;
        public static int antelopCardDisplayTheme_closeButton_icon = 0x7f040035;
        public static int antelopCardDisplayTheme_closeButton_style = 0x7f040036;
        public static int antelopCardDisplayTheme_copyPanButton_background_color = 0x7f040037;
        public static int antelopCardDisplayTheme_copyPanButton_confirmation_label = 0x7f040038;
        public static int antelopCardDisplayTheme_copyPanButton_description = 0x7f040039;
        public static int antelopCardDisplayTheme_copyPanButton_icon = 0x7f04003a;
        public static int antelopCardDisplayTheme_copyPanButton_icon_color = 0x7f04003b;
        public static int antelopCardDisplayTheme_copyPanButton_startPercent = 0x7f04003c;
        public static int antelopCardDisplayTheme_copyPanButton_style = 0x7f04003d;
        public static int antelopCardDisplayTheme_copyPanButton_topPercent = 0x7f04003e;
        public static int antelopCardDisplayTheme_cvx2TextView_style = 0x7f04003f;
        public static int antelopCardDisplayTheme_cvx2_endPercent = 0x7f040040;
        public static int antelopCardDisplayTheme_cvx2_startPercent = 0x7f040041;
        public static int antelopCardDisplayTheme_cvx2_topPercent = 0x7f040042;
        public static int antelopCardDisplayTheme_expiryDateTextView_style = 0x7f040043;
        public static int antelopCardDisplayTheme_expiryDate_endPercent = 0x7f040044;
        public static int antelopCardDisplayTheme_expiryDate_startPercent = 0x7f040045;
        public static int antelopCardDisplayTheme_expiryDate_topPercent = 0x7f040046;
        public static int antelopCardDisplayTheme_panTextView_style = 0x7f040047;
        public static int antelopCardDisplayTheme_pan_endPercent = 0x7f040048;
        public static int antelopCardDisplayTheme_pan_startPercent = 0x7f040049;
        public static int antelopCardDisplayTheme_pan_topPercent = 0x7f04004a;
        public static int antelopCardDisplayTheme_screenTitle = 0x7f04004b;
        public static int antelopCardPromptActivityTheme_cameraScanButton_color = 0x7f04004c;
        public static int antelopCardPromptActivityTheme_cameraScanButton_description = 0x7f04004d;
        public static int antelopCardPromptActivityTheme_cameraScanButton_src = 0x7f04004e;
        public static int antelopCardPromptActivityTheme_cameraScan_instructionText = 0x7f04004f;
        public static int antelopCardPromptActivityTheme_cardholderNameTextInputLayout_errorMessage = 0x7f040050;
        public static int antelopCardPromptActivityTheme_cardholderNameTextInputLayout_hint = 0x7f040051;
        public static int antelopCardPromptActivityTheme_cardholderNameTextInputLayout_style = 0x7f040052;
        public static int antelopCardPromptActivityTheme_cvx2DialogImage_src = 0x7f040053;
        public static int antelopCardPromptActivityTheme_cvx2Dialog_buttonText = 0x7f040054;
        public static int antelopCardPromptActivityTheme_cvx2Dialog_instructionText = 0x7f040055;
        public static int antelopCardPromptActivityTheme_cvx2TextInputLayout_description = 0x7f040056;
        public static int antelopCardPromptActivityTheme_cvx2TextInputLayout_errorMessage = 0x7f040057;
        public static int antelopCardPromptActivityTheme_cvx2TextInputLayout_hint = 0x7f040058;
        public static int antelopCardPromptActivityTheme_cvx2TextInputLayout_style = 0x7f040059;
        public static int antelopCardPromptActivityTheme_errorDialog_buttonText = 0x7f04005a;
        public static int antelopCardPromptActivityTheme_expiryDateDialogImage_src = 0x7f04005b;
        public static int antelopCardPromptActivityTheme_expiryDateDialog_buttonText = 0x7f04005c;
        public static int antelopCardPromptActivityTheme_expiryDateDialog_instructionText = 0x7f04005d;
        public static int antelopCardPromptActivityTheme_expiryDateTextInputLayout_description = 0x7f04005e;
        public static int antelopCardPromptActivityTheme_expiryDateTextInputLayout_errorMessage = 0x7f04005f;
        public static int antelopCardPromptActivityTheme_expiryDateTextInputLayout_hint = 0x7f040060;
        public static int antelopCardPromptActivityTheme_expiryDateTextInputLayout_style = 0x7f040061;
        public static int antelopCardPromptActivityTheme_genericCard_src = 0x7f040062;
        public static int antelopCardPromptActivityTheme_helpImage_color = 0x7f040063;
        public static int antelopCardPromptActivityTheme_helpImage_src = 0x7f040064;
        public static int antelopCardPromptActivityTheme_keypadViewCancelButton_src = 0x7f040065;
        public static int antelopCardPromptActivityTheme_keypadViewDigitBackground_style = 0x7f040066;
        public static int antelopCardPromptActivityTheme_keypadViewDigit_style = 0x7f040067;
        public static int antelopCardPromptActivityTheme_keypadViewLayout_style = 0x7f040068;
        public static int antelopCardPromptActivityTheme_keypadViewSubmitButton_src = 0x7f040069;
        public static int antelopCardPromptActivityTheme_mastercardCard_src = 0x7f04006a;
        public static int antelopCardPromptActivityTheme_nfcScanAnimation_errorColor = 0x7f04006b;
        public static int antelopCardPromptActivityTheme_nfcScanAnimation_errorSrc = 0x7f04006c;
        public static int antelopCardPromptActivityTheme_nfcScanAnimation_progressColor = 0x7f04006d;
        public static int antelopCardPromptActivityTheme_nfcScanAnimation_successColor = 0x7f04006e;
        public static int antelopCardPromptActivityTheme_nfcScanAnimation_successSrc = 0x7f04006f;
        public static int antelopCardPromptActivityTheme_nfcScanError_cardNotSupportedText = 0x7f040070;
        public static int antelopCardPromptActivityTheme_nfcScanError_otherText = 0x7f040071;
        public static int antelopCardPromptActivityTheme_nfcScanError_signalLostText = 0x7f040072;
        public static int antelopCardPromptActivityTheme_nfcScanImage_src = 0x7f040073;
        public static int antelopCardPromptActivityTheme_nfcScanImage_style = 0x7f040074;
        public static int antelopCardPromptActivityTheme_nfcScanTextView_style = 0x7f040075;
        public static int antelopCardPromptActivityTheme_nfcScanTextView_text = 0x7f040076;
        public static int antelopCardPromptActivityTheme_overlayProtection_enabled = 0x7f040077;
        public static int antelopCardPromptActivityTheme_overlayProtection_instructionText = 0x7f040078;
        public static int antelopCardPromptActivityTheme_panTextInputLayout_drawableStart = 0x7f040079;
        public static int antelopCardPromptActivityTheme_panTextInputLayout_errorMessage = 0x7f04007a;
        public static int antelopCardPromptActivityTheme_panTextInputLayout_hint = 0x7f04007b;
        public static int antelopCardPromptActivityTheme_panTextInputLayout_style = 0x7f04007c;
        public static int antelopCardPromptActivityTheme_submitButton_style = 0x7f04007d;
        public static int antelopCardPromptActivityTheme_submitButton_text = 0x7f04007e;
        public static int antelopCardPromptActivityTheme_title = 0x7f04007f;
        public static int antelopCardPromptActivityTheme_visaCard_src = 0x7f040080;
        public static int antelopCardPromptActivityTheme_waitingLayout_style = 0x7f040081;
        public static int antelopCardPromptActivityTheme_waitingProgressBar_style = 0x7f040082;
        public static int antelopCardPromptActivityTheme_waitingTextView_style = 0x7f040083;
        public static int antelopCardPromptActivityTheme_waitingTextView_text = 0x7f040084;
        public static int antelopKeypadViewBulletDrawable = 0x7f040085;
        public static int antelopKeypadViewColorPrimary = 0x7f040086;
        public static int antelopKeypadViewColorSecondary = 0x7f040087;
        public static int antelopKeypadViewDeleteDrawable = 0x7f040088;
        public static int antelopKeypadViewEnableOverlayProtection = 0x7f040089;
        public static int antelopKeypadViewExtraDrawable = 0x7f04008a;
        public static int antelopKeypadViewOverlayWarningMessage = 0x7f04008b;
        public static int antelopKeypadViewPinLength = 0x7f04008c;
        public static int antelopKeypadViewRandomizeKeyboard = 0x7f04008d;
        public static int antelopPinDisplayTheme_activityBackground_style = 0x7f04008e;
        public static int antelopPinDisplayTheme_cardBackground_cornerRadius = 0x7f04008f;
        public static int antelopPinDisplayTheme_cardBackground_image = 0x7f040090;
        public static int antelopPinDisplayTheme_cardBackground_style = 0x7f040091;
        public static int antelopPinDisplayTheme_cardMargin = 0x7f040092;
        public static int antelopPinDisplayTheme_closeButton_description = 0x7f040093;
        public static int antelopPinDisplayTheme_closeButton_enabled = 0x7f040094;
        public static int antelopPinDisplayTheme_closeButton_icon = 0x7f040095;
        public static int antelopPinDisplayTheme_closeButton_style = 0x7f040096;
        public static int antelopPinDisplayTheme_pinTextView_style = 0x7f040097;
        public static int antelopPinDisplayTheme_screenTitle = 0x7f040098;
        public static int antelopPinDisplayTheme_titleTextView_style = 0x7f040099;
        public static int antelopPinDisplayTheme_titleTextView_text = 0x7f04009a;
        public static int antelopSecurePinInputThemeInternal_alphaStyle = 0x7f04009b;
        public static int antelopSecurePinInputThemeInternal_backgroundStyle = 0x7f04009c;
        public static int antelopSecurePinInputThemeInternal_bulletIcon = 0x7f04009d;
        public static int antelopSecurePinInputThemeInternal_closeIcon = 0x7f04009e;
        public static int antelopSecurePinInputThemeInternal_colorPrimary = 0x7f04009f;
        public static int antelopSecurePinInputThemeInternal_colorSecondary = 0x7f0400a0;
        public static int antelopSecurePinInputThemeInternal_defaultSubTitle = 0x7f0400a1;
        public static int antelopSecurePinInputThemeInternal_defaultTitle = 0x7f0400a2;
        public static int antelopSecurePinInputThemeInternal_deleteIcon = 0x7f0400a3;
        public static int antelopSecurePinInputThemeInternal_digitStyle = 0x7f0400a4;
        public static int antelopSecurePinInputThemeInternal_displayAlpha = 0x7f0400a5;
        public static int antelopSecurePinInputThemeInternal_enableOverlayProtection = 0x7f0400a6;
        public static int antelopSecurePinInputThemeInternal_overlayWarningMessage = 0x7f0400a7;
        public static int antelopSecurePinInputThemeInternal_pinSize = 0x7f0400a8;
        public static int antelopSecurePinInputThemeInternal_pinsNotMatchingErrorDescription = 0x7f0400a9;
        public static int antelopSecurePinInputThemeInternal_randomizeKeyboard = 0x7f0400aa;
        public static int antelopSecurePinInputThemeInternal_showCloseButton = 0x7f0400ab;
        public static int appBarLayoutStyle = 0x7f0400ac;
        public static int arrowHeadLength = 0x7f0400ae;
        public static int arrowShaftLength = 0x7f0400af;
        public static int autoCompleteTextViewStyle = 0x7f0400b0;
        public static int autoSizeMaxTextSize = 0x7f0400b1;
        public static int autoSizeMinTextSize = 0x7f0400b2;
        public static int autoSizePresetSizes = 0x7f0400b3;
        public static int autoSizeStepGranularity = 0x7f0400b4;
        public static int autoSizeTextType = 0x7f0400b5;
        public static int background = 0x7f0400b6;
        public static int backgroundColor = 0x7f0400b7;
        public static int backgroundInsetBottom = 0x7f0400b8;
        public static int backgroundInsetEnd = 0x7f0400b9;
        public static int backgroundInsetStart = 0x7f0400ba;
        public static int backgroundInsetTop = 0x7f0400bb;
        public static int backgroundOverlayColorAlpha = 0x7f0400bc;
        public static int backgroundSplit = 0x7f0400bd;
        public static int backgroundStacked = 0x7f0400be;
        public static int backgroundTint = 0x7f0400bf;
        public static int backgroundTintMode = 0x7f0400c0;
        public static int badgeGravity = 0x7f0400c1;
        public static int badgeStyle = 0x7f0400c2;
        public static int badgeTextColor = 0x7f0400c3;
        public static int barLength = 0x7f0400c4;
        public static int barrierAllowsGoneWidgets = 0x7f0400c5;
        public static int barrierDirection = 0x7f0400c6;
        public static int behavior_autoHide = 0x7f0400c7;
        public static int behavior_autoShrink = 0x7f0400c8;
        public static int behavior_expandedOffset = 0x7f0400c9;
        public static int behavior_fitToContents = 0x7f0400ca;
        public static int behavior_halfExpandedRatio = 0x7f0400cb;
        public static int behavior_hideable = 0x7f0400cc;
        public static int behavior_overlapTop = 0x7f0400cd;
        public static int behavior_peekHeight = 0x7f0400ce;
        public static int behavior_saveFlags = 0x7f0400cf;
        public static int behavior_skipCollapsed = 0x7f0400d0;
        public static int borderWidth = 0x7f0400d1;
        public static int borderlessButtonStyle = 0x7f0400d2;
        public static int bottomAppBarStyle = 0x7f0400d3;
        public static int bottomNavigationStyle = 0x7f0400d4;
        public static int bottomSheetDialogTheme = 0x7f0400d5;
        public static int bottomSheetStyle = 0x7f0400d6;
        public static int boxBackgroundColor = 0x7f0400d7;
        public static int boxBackgroundMode = 0x7f0400d8;
        public static int boxCollapsedPaddingTop = 0x7f0400d9;
        public static int boxCornerRadiusBottomEnd = 0x7f0400da;
        public static int boxCornerRadiusBottomStart = 0x7f0400db;
        public static int boxCornerRadiusTopEnd = 0x7f0400dc;
        public static int boxCornerRadiusTopStart = 0x7f0400dd;
        public static int boxStrokeColor = 0x7f0400de;
        public static int boxStrokeWidth = 0x7f0400df;
        public static int boxStrokeWidthFocused = 0x7f0400e0;
        public static int buttonBarButtonStyle = 0x7f0400e1;
        public static int buttonBarNegativeButtonStyle = 0x7f0400e2;
        public static int buttonBarNeutralButtonStyle = 0x7f0400e3;
        public static int buttonBarPositiveButtonStyle = 0x7f0400e4;
        public static int buttonBarStyle = 0x7f0400e5;
        public static int buttonCompat = 0x7f0400e6;
        public static int buttonGravity = 0x7f0400e7;
        public static int buttonIconDimen = 0x7f0400e8;
        public static int buttonPanelSideLayout = 0x7f0400e9;
        public static int buttonSize = 0x7f0400ea;
        public static int buttonStyle = 0x7f0400eb;
        public static int buttonStyleSmall = 0x7f0400ec;
        public static int buttonTint = 0x7f0400ed;
        public static int buttonTintMode = 0x7f0400ee;
        public static int cardBackgroundColor = 0x7f0400ef;
        public static int cardCornerRadius = 0x7f0400f0;
        public static int cardElevation = 0x7f0400f1;
        public static int cardForegroundColor = 0x7f0400f2;
        public static int cardMaxElevation = 0x7f0400f3;
        public static int cardPreventCornerOverlap = 0x7f0400f4;
        public static int cardUseCompatPadding = 0x7f0400f5;
        public static int cardViewStyle = 0x7f0400f6;
        public static int chainUseRtl = 0x7f0400f7;
        public static int checkMarkCompat = 0x7f0400f8;
        public static int checkMarkTint = 0x7f0400f9;
        public static int checkMarkTintMode = 0x7f0400fa;
        public static int checkboxStyle = 0x7f0400fb;
        public static int checkedButton = 0x7f0400fc;
        public static int checkedChip = 0x7f0400fd;
        public static int checkedIcon = 0x7f0400fe;
        public static int checkedIconEnabled = 0x7f0400ff;
        public static int checkedIconTint = 0x7f040100;
        public static int checkedIconVisible = 0x7f040101;
        public static int checkedTextViewStyle = 0x7f040102;
        public static int chipBackgroundColor = 0x7f040103;
        public static int chipCornerRadius = 0x7f040104;
        public static int chipEndPadding = 0x7f040105;
        public static int chipGroupStyle = 0x7f040106;
        public static int chipIcon = 0x7f040107;
        public static int chipIconEnabled = 0x7f040108;
        public static int chipIconSize = 0x7f040109;
        public static int chipIconTint = 0x7f04010a;
        public static int chipIconVisible = 0x7f04010b;
        public static int chipMinHeight = 0x7f04010c;
        public static int chipMinTouchTargetSize = 0x7f04010d;
        public static int chipSpacing = 0x7f04010e;
        public static int chipSpacingHorizontal = 0x7f04010f;
        public static int chipSpacingVertical = 0x7f040110;
        public static int chipStandaloneStyle = 0x7f040111;
        public static int chipStartPadding = 0x7f040112;
        public static int chipStrokeColor = 0x7f040113;
        public static int chipStrokeWidth = 0x7f040114;
        public static int chipStyle = 0x7f040115;
        public static int chipSurfaceColor = 0x7f040116;
        public static int circleCrop = 0x7f040117;
        public static int closeIcon = 0x7f040118;
        public static int closeIconEnabled = 0x7f040119;
        public static int closeIconEndPadding = 0x7f04011a;
        public static int closeIconSize = 0x7f04011b;
        public static int closeIconStartPadding = 0x7f04011c;
        public static int closeIconTint = 0x7f04011d;
        public static int closeIconVisible = 0x7f04011e;
        public static int closeItemLayout = 0x7f04011f;
        public static int collapseContentDescription = 0x7f040120;
        public static int collapseIcon = 0x7f040121;
        public static int collapsedTitleGravity = 0x7f040122;
        public static int collapsedTitleTextAppearance = 0x7f040123;
        public static int color = 0x7f040124;
        public static int colorAccent = 0x7f040125;
        public static int colorBackgroundFloating = 0x7f040126;
        public static int colorButtonNormal = 0x7f040127;
        public static int colorControlActivated = 0x7f040128;
        public static int colorControlHighlight = 0x7f040129;
        public static int colorControlNormal = 0x7f04012a;
        public static int colorError = 0x7f04012b;
        public static int colorOnBackground = 0x7f04012c;
        public static int colorOnError = 0x7f04012d;
        public static int colorOnPrimary = 0x7f04012e;
        public static int colorOnPrimarySurface = 0x7f04012f;
        public static int colorOnSecondary = 0x7f040130;
        public static int colorOnSurface = 0x7f040131;
        public static int colorPrimary = 0x7f040132;
        public static int colorPrimaryDark = 0x7f040133;
        public static int colorPrimarySurface = 0x7f040134;
        public static int colorPrimaryVariant = 0x7f040135;
        public static int colorPrimaryVariantGoogle = 0x7f040136;
        public static int colorScheme = 0x7f040137;
        public static int colorSecondary = 0x7f040138;
        public static int colorSecondaryVariant = 0x7f040139;
        public static int colorSurface = 0x7f04013a;
        public static int colorSwitchThumbNormal = 0x7f04013b;
        public static int commitIcon = 0x7f04013c;
        public static int constraintSet = 0x7f04013d;
        public static int constraint_referenced_ids = 0x7f04013e;
        public static int content = 0x7f04013f;
        public static int contentDescription = 0x7f040140;
        public static int contentInsetEnd = 0x7f040141;
        public static int contentInsetEndWithActions = 0x7f040142;
        public static int contentInsetLeft = 0x7f040143;
        public static int contentInsetRight = 0x7f040144;
        public static int contentInsetStart = 0x7f040145;
        public static int contentInsetStartWithNavigation = 0x7f040146;
        public static int contentPadding = 0x7f040147;
        public static int contentPaddingBottom = 0x7f040148;
        public static int contentPaddingLeft = 0x7f040149;
        public static int contentPaddingRight = 0x7f04014a;
        public static int contentPaddingTop = 0x7f04014b;
        public static int contentScrim = 0x7f04014c;
        public static int controlBackground = 0x7f04014d;
        public static int coordinatorLayoutStyle = 0x7f04014e;
        public static int cornerFamily = 0x7f04014f;
        public static int cornerFamilyBottomLeft = 0x7f040150;
        public static int cornerFamilyBottomRight = 0x7f040151;
        public static int cornerFamilyTopLeft = 0x7f040152;
        public static int cornerFamilyTopRight = 0x7f040153;
        public static int cornerRadius = 0x7f040154;
        public static int cornerSize = 0x7f040155;
        public static int cornerSizeBottomLeft = 0x7f040156;
        public static int cornerSizeBottomRight = 0x7f040157;
        public static int cornerSizeTopLeft = 0x7f040158;
        public static int cornerSizeTopRight = 0x7f040159;
        public static int counterEnabled = 0x7f04015a;
        public static int counterMaxLength = 0x7f04015b;
        public static int counterOverflowTextAppearance = 0x7f04015c;
        public static int counterOverflowTextColor = 0x7f04015d;
        public static int counterTextAppearance = 0x7f04015e;
        public static int counterTextColor = 0x7f04015f;
        public static int customNavigationLayout = 0x7f040160;
        public static int dayInvalidStyle = 0x7f040163;
        public static int daySelectedStyle = 0x7f040164;
        public static int dayStyle = 0x7f040165;
        public static int dayTodayStyle = 0x7f040166;
        public static int defaultQueryHint = 0x7f040168;
        public static int dialogCornerRadius = 0x7f04016a;
        public static int dialogPreferredPadding = 0x7f04016b;
        public static int dialogTheme = 0x7f04016c;
        public static int displayOptions = 0x7f04016d;
        public static int divider = 0x7f04016e;
        public static int dividerHorizontal = 0x7f04016f;
        public static int dividerPadding = 0x7f040170;
        public static int dividerVertical = 0x7f040171;
        public static int drawableBottomCompat = 0x7f040172;
        public static int drawableEndCompat = 0x7f040173;
        public static int drawableLeftCompat = 0x7f040174;
        public static int drawableRightCompat = 0x7f040175;
        public static int drawableSize = 0x7f040176;
        public static int drawableStartCompat = 0x7f040177;
        public static int drawableTint = 0x7f040178;
        public static int drawableTintMode = 0x7f040179;
        public static int drawableTopCompat = 0x7f04017a;
        public static int drawerArrowStyle = 0x7f04017b;
        public static int dropDownListViewStyle = 0x7f04017c;
        public static int dropdownListPreferredItemHeight = 0x7f04017d;
        public static int editTextBackground = 0x7f04017e;
        public static int editTextColor = 0x7f04017f;
        public static int editTextStyle = 0x7f040180;
        public static int elevation = 0x7f040181;
        public static int elevationOverlayColor = 0x7f040182;
        public static int elevationOverlayEnabled = 0x7f040183;
        public static int emojiCompatEnabled = 0x7f040184;
        public static int endIconCheckable = 0x7f040186;
        public static int endIconContentDescription = 0x7f040187;
        public static int endIconDrawable = 0x7f040188;
        public static int endIconMode = 0x7f040189;
        public static int endIconTint = 0x7f04018a;
        public static int endIconTintMode = 0x7f04018b;
        public static int enforceMaterialTheme = 0x7f04018c;
        public static int enforceTextAppearance = 0x7f04018d;
        public static int ensureMinTouchTargetSize = 0x7f04018e;
        public static int errorEnabled = 0x7f040190;
        public static int errorIconDrawable = 0x7f040191;
        public static int errorIconTint = 0x7f040192;
        public static int errorIconTintMode = 0x7f040193;
        public static int errorTextAppearance = 0x7f040194;
        public static int errorTextColor = 0x7f040195;
        public static int expandActivityOverflowButtonDrawable = 0x7f040197;
        public static int expanded = 0x7f040198;
        public static int expandedTitleGravity = 0x7f040199;
        public static int expandedTitleMargin = 0x7f04019a;
        public static int expandedTitleMarginBottom = 0x7f04019b;
        public static int expandedTitleMarginEnd = 0x7f04019c;
        public static int expandedTitleMarginStart = 0x7f04019d;
        public static int expandedTitleMarginTop = 0x7f04019e;
        public static int expandedTitleTextAppearance = 0x7f04019f;
        public static int extendMotionSpec = 0x7f0401a0;
        public static int extendedFloatingActionButtonStyle = 0x7f0401a1;
        public static int fabAlignmentMode = 0x7f0401a2;
        public static int fabAnimationMode = 0x7f0401a3;
        public static int fabCradleMargin = 0x7f0401a4;
        public static int fabCradleRoundedCornerRadius = 0x7f0401a5;
        public static int fabCradleVerticalOffset = 0x7f0401a6;
        public static int fabCustomSize = 0x7f0401a7;
        public static int fabSize = 0x7f0401a8;
        public static int fastScrollEnabled = 0x7f0401a9;
        public static int fastScrollHorizontalThumbDrawable = 0x7f0401aa;
        public static int fastScrollHorizontalTrackDrawable = 0x7f0401ab;
        public static int fastScrollVerticalThumbDrawable = 0x7f0401ac;
        public static int fastScrollVerticalTrackDrawable = 0x7f0401ad;
        public static int firstBaselineToTopHeight = 0x7f0401ae;
        public static int floatingActionButtonStyle = 0x7f0401af;
        public static int font = 0x7f0401b0;
        public static int fontFamily = 0x7f0401b1;
        public static int fontProviderAuthority = 0x7f0401b2;
        public static int fontProviderCerts = 0x7f0401b3;
        public static int fontProviderFetchStrategy = 0x7f0401b4;
        public static int fontProviderFetchTimeout = 0x7f0401b5;
        public static int fontProviderPackage = 0x7f0401b6;
        public static int fontProviderQuery = 0x7f0401b7;
        public static int fontProviderSystemFontFamily = 0x7f0401b8;
        public static int fontStyle = 0x7f0401b9;
        public static int fontVariationSettings = 0x7f0401ba;
        public static int fontWeight = 0x7f0401bb;
        public static int foregroundInsidePadding = 0x7f0401bc;
        public static int gapBetweenBars = 0x7f0401bd;
        public static int goIcon = 0x7f0401be;
        public static int headerLayout = 0x7f0401c0;
        public static int height = 0x7f0401c1;
        public static int helperText = 0x7f0401c2;
        public static int helperTextEnabled = 0x7f0401c3;
        public static int helperTextTextAppearance = 0x7f0401c4;
        public static int helperTextTextColor = 0x7f0401c5;
        public static int hideMotionSpec = 0x7f0401c6;
        public static int hideOnContentScroll = 0x7f0401c7;
        public static int hideOnScroll = 0x7f0401c8;
        public static int hintAnimationEnabled = 0x7f0401c9;
        public static int hintEnabled = 0x7f0401ca;
        public static int hintTextAppearance = 0x7f0401cb;
        public static int hintTextColor = 0x7f0401cc;
        public static int homeAsUpIndicator = 0x7f0401cd;
        public static int homeLayout = 0x7f0401ce;
        public static int hoveredFocusedTranslationZ = 0x7f0401cf;
        public static int icon = 0x7f0401d0;
        public static int iconEndPadding = 0x7f0401d1;
        public static int iconGravity = 0x7f0401d2;
        public static int iconPadding = 0x7f0401d3;
        public static int iconSize = 0x7f0401d4;
        public static int iconStartPadding = 0x7f0401d5;
        public static int iconTint = 0x7f0401d6;
        public static int iconTintMode = 0x7f0401d7;
        public static int iconifiedByDefault = 0x7f0401d8;
        public static int imageAspectRatio = 0x7f0401d9;
        public static int imageAspectRatioAdjust = 0x7f0401da;
        public static int imageButtonStyle = 0x7f0401db;
        public static int indeterminateProgressStyle = 0x7f0401dc;
        public static int initialActivityCount = 0x7f0401dd;
        public static int insetForeground = 0x7f0401de;
        public static int isLightTheme = 0x7f0401df;
        public static int isMaterialTheme = 0x7f0401e0;
        public static int itemBackground = 0x7f0401e1;
        public static int itemFillColor = 0x7f0401e2;
        public static int itemHorizontalPadding = 0x7f0401e3;
        public static int itemHorizontalTranslationEnabled = 0x7f0401e4;
        public static int itemIconPadding = 0x7f0401e5;
        public static int itemIconSize = 0x7f0401e6;
        public static int itemIconTint = 0x7f0401e7;
        public static int itemMaxLines = 0x7f0401e8;
        public static int itemPadding = 0x7f0401e9;
        public static int itemRippleColor = 0x7f0401ea;
        public static int itemShapeAppearance = 0x7f0401eb;
        public static int itemShapeAppearanceOverlay = 0x7f0401ec;
        public static int itemShapeFillColor = 0x7f0401ed;
        public static int itemShapeInsetBottom = 0x7f0401ee;
        public static int itemShapeInsetEnd = 0x7f0401ef;
        public static int itemShapeInsetStart = 0x7f0401f0;
        public static int itemShapeInsetTop = 0x7f0401f1;
        public static int itemSpacing = 0x7f0401f2;
        public static int itemStrokeColor = 0x7f0401f3;
        public static int itemStrokeWidth = 0x7f0401f4;
        public static int itemTextAppearance = 0x7f0401f5;
        public static int itemTextAppearanceActive = 0x7f0401f6;
        public static int itemTextAppearanceInactive = 0x7f0401f7;
        public static int itemTextColor = 0x7f0401f8;
        public static int keylines = 0x7f0401f9;
        public static int lStar = 0x7f0401fa;
        public static int labelVisibilityMode = 0x7f0401fb;
        public static int lastBaselineToBottomHeight = 0x7f0401fc;
        public static int layout = 0x7f0401fe;
        public static int layoutManager = 0x7f0401ff;
        public static int layout_anchor = 0x7f040200;
        public static int layout_anchorGravity = 0x7f040201;
        public static int layout_behavior = 0x7f040202;
        public static int layout_collapseMode = 0x7f040203;
        public static int layout_collapseParallaxMultiplier = 0x7f040204;
        public static int layout_constrainedHeight = 0x7f040205;
        public static int layout_constrainedWidth = 0x7f040206;
        public static int layout_constraintBaseline_creator = 0x7f040207;
        public static int layout_constraintBaseline_toBaselineOf = 0x7f040208;
        public static int layout_constraintBottom_creator = 0x7f040209;
        public static int layout_constraintBottom_toBottomOf = 0x7f04020a;
        public static int layout_constraintBottom_toTopOf = 0x7f04020b;
        public static int layout_constraintCircle = 0x7f04020c;
        public static int layout_constraintCircleAngle = 0x7f04020d;
        public static int layout_constraintCircleRadius = 0x7f04020e;
        public static int layout_constraintDimensionRatio = 0x7f04020f;
        public static int layout_constraintEnd_toEndOf = 0x7f040210;
        public static int layout_constraintEnd_toStartOf = 0x7f040211;
        public static int layout_constraintGuide_begin = 0x7f040212;
        public static int layout_constraintGuide_end = 0x7f040213;
        public static int layout_constraintGuide_percent = 0x7f040214;
        public static int layout_constraintHeight_default = 0x7f040215;
        public static int layout_constraintHeight_max = 0x7f040216;
        public static int layout_constraintHeight_min = 0x7f040217;
        public static int layout_constraintHeight_percent = 0x7f040218;
        public static int layout_constraintHorizontal_bias = 0x7f040219;
        public static int layout_constraintHorizontal_chainStyle = 0x7f04021a;
        public static int layout_constraintHorizontal_weight = 0x7f04021b;
        public static int layout_constraintLeft_creator = 0x7f04021c;
        public static int layout_constraintLeft_toLeftOf = 0x7f04021d;
        public static int layout_constraintLeft_toRightOf = 0x7f04021e;
        public static int layout_constraintRight_creator = 0x7f04021f;
        public static int layout_constraintRight_toLeftOf = 0x7f040220;
        public static int layout_constraintRight_toRightOf = 0x7f040221;
        public static int layout_constraintStart_toEndOf = 0x7f040222;
        public static int layout_constraintStart_toStartOf = 0x7f040223;
        public static int layout_constraintTop_creator = 0x7f040224;
        public static int layout_constraintTop_toBottomOf = 0x7f040225;
        public static int layout_constraintTop_toTopOf = 0x7f040226;
        public static int layout_constraintVertical_bias = 0x7f040227;
        public static int layout_constraintVertical_chainStyle = 0x7f040228;
        public static int layout_constraintVertical_weight = 0x7f040229;
        public static int layout_constraintWidth_default = 0x7f04022a;
        public static int layout_constraintWidth_max = 0x7f04022b;
        public static int layout_constraintWidth_min = 0x7f04022c;
        public static int layout_constraintWidth_percent = 0x7f04022d;
        public static int layout_dodgeInsetEdges = 0x7f04022e;
        public static int layout_editor_absoluteX = 0x7f04022f;
        public static int layout_editor_absoluteY = 0x7f040230;
        public static int layout_goneMarginBottom = 0x7f040231;
        public static int layout_goneMarginEnd = 0x7f040232;
        public static int layout_goneMarginLeft = 0x7f040233;
        public static int layout_goneMarginRight = 0x7f040234;
        public static int layout_goneMarginStart = 0x7f040235;
        public static int layout_goneMarginTop = 0x7f040236;
        public static int layout_insetEdge = 0x7f040237;
        public static int layout_keyline = 0x7f040238;
        public static int layout_optimizationLevel = 0x7f040239;
        public static int layout_scrollFlags = 0x7f04023a;
        public static int layout_scrollInterpolator = 0x7f04023b;
        public static int liftOnScroll = 0x7f04023c;
        public static int liftOnScrollTargetViewId = 0x7f04023d;
        public static int lineHeight = 0x7f04023e;
        public static int lineSpacing = 0x7f04023f;
        public static int listChoiceBackgroundIndicator = 0x7f040240;
        public static int listChoiceIndicatorMultipleAnimated = 0x7f040241;
        public static int listChoiceIndicatorSingleAnimated = 0x7f040242;
        public static int listDividerAlertDialog = 0x7f040243;
        public static int listItemLayout = 0x7f040244;
        public static int listLayout = 0x7f040245;
        public static int listMenuViewStyle = 0x7f040246;
        public static int listPopupWindowStyle = 0x7f040247;
        public static int listPreferredItemHeight = 0x7f040248;
        public static int listPreferredItemHeightLarge = 0x7f040249;
        public static int listPreferredItemHeightSmall = 0x7f04024a;
        public static int listPreferredItemPaddingEnd = 0x7f04024b;
        public static int listPreferredItemPaddingLeft = 0x7f04024c;
        public static int listPreferredItemPaddingRight = 0x7f04024d;
        public static int listPreferredItemPaddingStart = 0x7f04024e;
        public static int logo = 0x7f04024f;
        public static int logoDescription = 0x7f040250;
        public static int materialAlertDialogBodyTextStyle = 0x7f040251;
        public static int materialAlertDialogTheme = 0x7f040252;
        public static int materialAlertDialogTitleIconStyle = 0x7f040253;
        public static int materialAlertDialogTitlePanelStyle = 0x7f040254;
        public static int materialAlertDialogTitleTextStyle = 0x7f040255;
        public static int materialButtonOutlinedStyle = 0x7f040256;
        public static int materialButtonStyle = 0x7f040257;
        public static int materialButtonToggleGroupStyle = 0x7f040258;
        public static int materialCalendarDay = 0x7f040259;
        public static int materialCalendarFullscreenTheme = 0x7f04025a;
        public static int materialCalendarHeaderConfirmButton = 0x7f04025b;
        public static int materialCalendarHeaderDivider = 0x7f04025c;
        public static int materialCalendarHeaderLayout = 0x7f04025d;
        public static int materialCalendarHeaderSelection = 0x7f04025e;
        public static int materialCalendarHeaderTitle = 0x7f04025f;
        public static int materialCalendarHeaderToggleButton = 0x7f040260;
        public static int materialCalendarStyle = 0x7f040261;
        public static int materialCalendarTheme = 0x7f040262;
        public static int materialCardViewStyle = 0x7f040263;
        public static int materialThemeOverlay = 0x7f040264;
        public static int maxActionInlineWidth = 0x7f040265;
        public static int maxButtonHeight = 0x7f040266;
        public static int maxCharacterCount = 0x7f040267;
        public static int maxImageSize = 0x7f040268;
        public static int measureWithLargestChild = 0x7f040269;
        public static int menu = 0x7f04026a;
        public static int minTouchTargetSize = 0x7f04026b;
        public static int multiChoiceItemLayout = 0x7f04026c;
        public static int navigationContentDescription = 0x7f04026e;
        public static int navigationIcon = 0x7f04026f;
        public static int navigationMode = 0x7f040270;
        public static int navigationViewStyle = 0x7f040271;
        public static int nestedScrollViewStyle = 0x7f040272;
        public static int number = 0x7f040274;
        public static int numericModifiers = 0x7f040275;
        public static int overlapAnchor = 0x7f040276;
        public static int paddingBottomNoButtons = 0x7f040277;
        public static int paddingEnd = 0x7f040278;
        public static int paddingStart = 0x7f040279;
        public static int paddingTopNoTitle = 0x7f04027a;
        public static int panelBackground = 0x7f04027b;
        public static int panelMenuListTheme = 0x7f04027c;
        public static int panelMenuListWidth = 0x7f04027d;
        public static int passwordToggleContentDescription = 0x7f04027e;
        public static int passwordToggleDrawable = 0x7f04027f;
        public static int passwordToggleEnabled = 0x7f040280;
        public static int passwordToggleTint = 0x7f040281;
        public static int passwordToggleTintMode = 0x7f040282;
        public static int popupMenuBackground = 0x7f040287;
        public static int popupMenuStyle = 0x7f040288;
        public static int popupTheme = 0x7f040289;
        public static int popupWindowStyle = 0x7f04028a;
        public static int preserveIconSpacing = 0x7f04028c;
        public static int pressedTranslationZ = 0x7f04028d;
        public static int progressBarPadding = 0x7f04028e;
        public static int progressBarStyle = 0x7f04028f;
        public static int queryBackground = 0x7f040290;
        public static int queryHint = 0x7f040291;
        public static int queryPatterns = 0x7f040292;
        public static int radioButtonStyle = 0x7f040293;
        public static int rangeFillColor = 0x7f040294;
        public static int ratingBarStyle = 0x7f040295;
        public static int ratingBarStyleIndicator = 0x7f040296;
        public static int ratingBarStyleSmall = 0x7f040297;
        public static int recyclerViewStyle = 0x7f040298;
        public static int reverseLayout = 0x7f040299;
        public static int rippleColor = 0x7f04029a;
        public static int scopeUris = 0x7f04029b;
        public static int scrimAnimationDuration = 0x7f04029c;
        public static int scrimBackground = 0x7f04029d;
        public static int scrimVisibleHeightTrigger = 0x7f04029e;
        public static int searchHintIcon = 0x7f04029f;
        public static int searchIcon = 0x7f0402a0;
        public static int searchViewStyle = 0x7f0402a1;
        public static int seekBarStyle = 0x7f0402a2;
        public static int selectableItemBackground = 0x7f0402a3;
        public static int selectableItemBackgroundBorderless = 0x7f0402a4;
        public static int shapeAppearance = 0x7f0402a5;
        public static int shapeAppearanceLargeComponent = 0x7f0402a6;
        public static int shapeAppearanceMediumComponent = 0x7f0402a7;
        public static int shapeAppearanceOverlay = 0x7f0402a8;
        public static int shapeAppearanceSmallComponent = 0x7f0402a9;
        public static int shortcutMatchRequired = 0x7f0402aa;
        public static int showAsAction = 0x7f0402ab;
        public static int showDividers = 0x7f0402ac;
        public static int showMotionSpec = 0x7f0402ad;
        public static int showText = 0x7f0402ae;
        public static int showTitle = 0x7f0402af;
        public static int shrinkMotionSpec = 0x7f0402b0;
        public static int singleChoiceItemLayout = 0x7f0402b1;
        public static int singleLine = 0x7f0402b2;
        public static int singleSelection = 0x7f0402b3;
        public static int snackbarButtonStyle = 0x7f0402b4;
        public static int snackbarStyle = 0x7f0402b5;
        public static int spanCount = 0x7f0402b6;
        public static int spinBars = 0x7f0402b7;
        public static int spinnerDropDownItemStyle = 0x7f0402b8;
        public static int spinnerStyle = 0x7f0402b9;
        public static int splitTrack = 0x7f0402bb;
        public static int srcCompat = 0x7f0402bc;
        public static int stackFromEnd = 0x7f0402bd;
        public static int startIconCheckable = 0x7f0402bf;
        public static int startIconContentDescription = 0x7f0402c0;
        public static int startIconDrawable = 0x7f0402c1;
        public static int startIconTint = 0x7f0402c2;
        public static int startIconTintMode = 0x7f0402c3;
        public static int state_above_anchor = 0x7f0402c5;
        public static int state_collapsed = 0x7f0402c6;
        public static int state_collapsible = 0x7f0402c7;
        public static int state_dragged = 0x7f0402c8;
        public static int state_liftable = 0x7f0402c9;
        public static int state_lifted = 0x7f0402ca;
        public static int statusBarBackground = 0x7f0402cb;
        public static int statusBarForeground = 0x7f0402cc;
        public static int statusBarScrim = 0x7f0402cd;
        public static int strokeColor = 0x7f0402ce;
        public static int strokeWidth = 0x7f0402cf;
        public static int subMenuArrow = 0x7f0402d0;
        public static int submitBackground = 0x7f0402d1;
        public static int subtitle = 0x7f0402d2;
        public static int subtitleTextAppearance = 0x7f0402d3;
        public static int subtitleTextColor = 0x7f0402d4;
        public static int subtitleTextStyle = 0x7f0402d5;
        public static int suggestionRowLayout = 0x7f0402d6;
        public static int switchMinWidth = 0x7f0402d7;
        public static int switchPadding = 0x7f0402d8;
        public static int switchStyle = 0x7f0402d9;
        public static int switchTextAppearance = 0x7f0402da;
        public static int tabBackground = 0x7f0402db;
        public static int tabContentStart = 0x7f0402dc;
        public static int tabGravity = 0x7f0402dd;
        public static int tabIconTint = 0x7f0402de;
        public static int tabIconTintMode = 0x7f0402df;
        public static int tabIndicator = 0x7f0402e0;
        public static int tabIndicatorAnimationDuration = 0x7f0402e1;
        public static int tabIndicatorColor = 0x7f0402e2;
        public static int tabIndicatorFullWidth = 0x7f0402e3;
        public static int tabIndicatorGravity = 0x7f0402e4;
        public static int tabIndicatorHeight = 0x7f0402e5;
        public static int tabInlineLabel = 0x7f0402e6;
        public static int tabMaxWidth = 0x7f0402e7;
        public static int tabMinWidth = 0x7f0402e8;
        public static int tabMode = 0x7f0402e9;
        public static int tabPadding = 0x7f0402ea;
        public static int tabPaddingBottom = 0x7f0402eb;
        public static int tabPaddingEnd = 0x7f0402ec;
        public static int tabPaddingStart = 0x7f0402ed;
        public static int tabPaddingTop = 0x7f0402ee;
        public static int tabRippleColor = 0x7f0402ef;
        public static int tabSelectedTextColor = 0x7f0402f0;
        public static int tabStyle = 0x7f0402f1;
        public static int tabTextAppearance = 0x7f0402f2;
        public static int tabTextColor = 0x7f0402f3;
        public static int tabUnboundedRipple = 0x7f0402f4;
        public static int textAllCaps = 0x7f0402f5;
        public static int textAppearanceBody1 = 0x7f0402f6;
        public static int textAppearanceBody2 = 0x7f0402f7;
        public static int textAppearanceButton = 0x7f0402f8;
        public static int textAppearanceCaption = 0x7f0402f9;
        public static int textAppearanceHeadline1 = 0x7f0402fa;
        public static int textAppearanceHeadline2 = 0x7f0402fb;
        public static int textAppearanceHeadline3 = 0x7f0402fc;
        public static int textAppearanceHeadline4 = 0x7f0402fd;
        public static int textAppearanceHeadline5 = 0x7f0402fe;
        public static int textAppearanceHeadline6 = 0x7f0402ff;
        public static int textAppearanceLargePopupMenu = 0x7f040300;
        public static int textAppearanceLineHeightEnabled = 0x7f040301;
        public static int textAppearanceListItem = 0x7f040302;
        public static int textAppearanceListItemSecondary = 0x7f040303;
        public static int textAppearanceListItemSmall = 0x7f040304;
        public static int textAppearanceOverline = 0x7f040305;
        public static int textAppearancePopupMenuHeader = 0x7f040306;
        public static int textAppearanceSearchResultSubtitle = 0x7f040307;
        public static int textAppearanceSearchResultTitle = 0x7f040308;
        public static int textAppearanceSmallPopupMenu = 0x7f040309;
        public static int textAppearanceSubtitle1 = 0x7f04030a;
        public static int textAppearanceSubtitle2 = 0x7f04030b;
        public static int textColorAlertDialogListItem = 0x7f04030c;
        public static int textColorSearchUrl = 0x7f04030d;
        public static int textEndPadding = 0x7f04030e;
        public static int textInputStyle = 0x7f04030f;
        public static int textLocale = 0x7f040310;
        public static int textStartPadding = 0x7f040311;
        public static int theme = 0x7f040312;
        public static int thickness = 0x7f040314;
        public static int thumbTextPadding = 0x7f040315;
        public static int thumbTint = 0x7f040316;
        public static int thumbTintMode = 0x7f040317;
        public static int tickMark = 0x7f040318;
        public static int tickMarkTint = 0x7f040319;
        public static int tickMarkTintMode = 0x7f04031a;
        public static int tint = 0x7f04031b;
        public static int tintMode = 0x7f04031c;
        public static int title = 0x7f04031d;
        public static int titleEnabled = 0x7f04031e;
        public static int titleMargin = 0x7f04031f;
        public static int titleMarginBottom = 0x7f040320;
        public static int titleMarginEnd = 0x7f040321;
        public static int titleMarginStart = 0x7f040322;
        public static int titleMarginTop = 0x7f040323;
        public static int titleMargins = 0x7f040324;
        public static int titleTextAppearance = 0x7f040325;
        public static int titleTextColor = 0x7f040326;
        public static int titleTextStyle = 0x7f040327;
        public static int toolbarId = 0x7f040328;
        public static int toolbarNavigationButtonStyle = 0x7f040329;
        public static int toolbarStyle = 0x7f04032a;
        public static int tooltipForegroundColor = 0x7f04032b;
        public static int tooltipFrameBackground = 0x7f04032c;
        public static int tooltipText = 0x7f04032d;
        public static int track = 0x7f04032e;
        public static int trackTint = 0x7f04032f;
        public static int trackTintMode = 0x7f040330;
        public static int ttcIndex = 0x7f040331;
        public static int useCompatPadding = 0x7f040333;
        public static int useMaterialThemeColors = 0x7f040334;
        public static int viewInflaterClass = 0x7f040335;
        public static int voiceIcon = 0x7f040336;
        public static int windowActionBar = 0x7f040337;
        public static int windowActionBarOverlay = 0x7f040338;
        public static int windowActionModeOverlay = 0x7f040339;
        public static int windowFixedHeightMajor = 0x7f04033a;
        public static int windowFixedHeightMinor = 0x7f04033b;
        public static int windowFixedWidthMajor = 0x7f04033c;
        public static int windowFixedWidthMinor = 0x7f04033d;
        public static int windowMinWidthMajor = 0x7f04033e;
        public static int windowMinWidthMinor = 0x7f04033f;
        public static int windowNoTitle = 0x7f040340;
        public static int yearSelectedStyle = 0x7f040345;
        public static int yearStyle = 0x7f040346;
        public static int yearTodayStyle = 0x7f040347;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$bool.smali */
    public static final class bool {
        public static int abc_action_bar_embed_tabs = 0x7f050000;
        public static int abc_config_actionMenuItemAllCaps = 0x7f050001;
        public static int antelopCardDisplay_displayCloseButton = 0x7f050002;
        public static int antelopCardPromptActivity_OverlayProtectionEnabled = 0x7f050003;
        public static int antelopConsentPromptEnableOverlayProtection = 0x7f050004;
        public static int antelopKeypadViewDefaultEnableOverlayProtection = 0x7f050005;
        public static int antelopKeypadViewDefaultRandomizeKeyboard = 0x7f050006;
        public static int antelopKeypadViewDefaultShowAlpha = 0x7f050007;
        public static int antelopPinDisplay_displayCloseButton = 0x7f050008;
        public static int antelopPinPromptEnableOverlayProtection = 0x7f050009;
        public static int antelopPinPromptRandomizeKeyboard = 0x7f05000a;
        public static int antelopPinPromptShowAlpha = 0x7f05000b;
        public static int antelopPinPromptShowCancelButton = 0x7f05000c;
        public static int antelopScreenUnlockPromptEnableOverlayProtection = 0x7f05000d;
        public static int antelopSecureCardDisplayCloseButton = 0x7f05000e;
        public static int antelopSecurePinDisplayCloseButton = 0x7f05000f;
        public static int antelopSecurePinInputDisplayAlpha = 0x7f050010;
        public static int antelopSecurePinInputEnableOverlayProtection = 0x7f050011;
        public static int antelopSecurePinInputRandomizeKeyboard = 0x7f050012;
        public static int antelopSecurePinInputShowCloseButton = 0x7f050013;
        public static int antelopSecureVirtualCardNumberDisplayCloseButton = 0x7f050014;
        public static int debitAsPositiveTransactionInDatabase = 0x7f050015;
        public static int enable_system_alarm_service_default = 0x7f050016;
        public static int enable_system_foreground_service_default = 0x7f050017;
        public static int enable_system_job_service_default = 0x7f050018;
        public static int mtrl_btn_textappearance_all_caps = 0x7f050019;
        public static int workmanager_test_configuration = 0x7f05001a;

        private bool() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$color.smali */
    public static final class color {
        public static int abc_background_cache_hint_selector_material_dark = 0x7f060000;
        public static int abc_background_cache_hint_selector_material_light = 0x7f060001;
        public static int abc_btn_colored_borderless_text_material = 0x7f060002;
        public static int abc_btn_colored_text_material = 0x7f060003;
        public static int abc_color_highlight_material = 0x7f060004;
        public static int abc_decor_view_status_guard = 0x7f060005;
        public static int abc_decor_view_status_guard_light = 0x7f060006;
        public static int abc_hint_foreground_material_dark = 0x7f060007;
        public static int abc_hint_foreground_material_light = 0x7f060008;
        public static int abc_primary_text_disable_only_material_dark = 0x7f060009;
        public static int abc_primary_text_disable_only_material_light = 0x7f06000a;
        public static int abc_primary_text_material_dark = 0x7f06000b;
        public static int abc_primary_text_material_light = 0x7f06000c;
        public static int abc_search_url_text = 0x7f06000d;
        public static int abc_search_url_text_normal = 0x7f06000e;
        public static int abc_search_url_text_pressed = 0x7f06000f;
        public static int abc_search_url_text_selected = 0x7f060010;
        public static int abc_secondary_text_material_dark = 0x7f060011;
        public static int abc_secondary_text_material_light = 0x7f060012;
        public static int abc_tint_btn_checkable = 0x7f060013;
        public static int abc_tint_default = 0x7f060014;
        public static int abc_tint_edittext = 0x7f060015;
        public static int abc_tint_seek_thumb = 0x7f060016;
        public static int abc_tint_spinner = 0x7f060017;
        public static int abc_tint_switch_track = 0x7f060018;
        public static int accent_material_dark = 0x7f060019;
        public static int accent_material_light = 0x7f06001a;
        public static int androidx_core_ripple_material_light = 0x7f06001b;
        public static int androidx_core_secondary_text_default_material_light = 0x7f06001c;
        public static int antelopCardDisplayActivityTransparentBackgroundColor = 0x7f06001d;
        public static int antelopCardDisplayCopyPanButtonBackgroundColor = 0x7f06001e;
        public static int antelopCardDisplayCopyPanButtonIconColor = 0x7f06001f;
        public static int antelopCardPromptActivityBackgroundLoadingScreenColor = 0x7f060020;
        public static int antelopCardPromptActivityIconColor = 0x7f060021;
        public static int antelopCardPromptActivityScanErrorColor = 0x7f060022;
        public static int antelopCardPromptActivityScanProgressColor = 0x7f060023;
        public static int antelopCardPromptActivityScanSuccessColor = 0x7f060024;
        public static int antelopCardPromptActivitySecuredKeyboardBackgroundColor = 0x7f060025;
        public static int antelopCardPromptActivitySecuredKeyboardPrimaryColor = 0x7f060026;
        public static int antelopCardPromptActivitySecuredKeyboardSecondaryColor = 0x7f060027;
        public static int antelopCardPromptActivityWaitingProgressBar = 0x7f060028;
        public static int antelopConsentPromptColorBackground = 0x7f060029;
        public static int antelopConsentPromptColorPrimary = 0x7f06002a;
        public static int antelopIconColor = 0x7f06002b;
        public static int antelopKeypadViewDefaultColorPrimary = 0x7f06002c;
        public static int antelopKeypadViewDefaultColorSecondary = 0x7f06002d;
        public static int antelopPinPromptColorBackground = 0x7f06002e;
        public static int antelopPinPromptColorPrimary = 0x7f06002f;
        public static int antelopPinPromptColorSecondary = 0x7f060030;
        public static int antelopPromptBackgroundColor = 0x7f060031;
        public static int antelopPromptPrimaryColor = 0x7f060032;
        public static int antelopPromptSecondaryColor = 0x7f060033;
        public static int antelopScreenUnlockPromptColorBackground = 0x7f060034;
        public static int antelopScreenUnlockPromptColorPrimary = 0x7f060035;
        public static int antelopSecureCardDisplayCopyPanButtonBackgroundColor = 0x7f060036;
        public static int antelopSecureCardDisplayCopyPanButtonIconColor = 0x7f060037;
        public static int antelopSecureDisplayBackgroundColor = 0x7f060038;
        public static int antelopSecureDisplayPrimaryColor = 0x7f060039;
        public static int antelopSecurePinInputColorBackground = 0x7f06003a;
        public static int antelopSecurePinInputColorPrimary = 0x7f06003b;
        public static int antelopSecurePinInputColorSecondary = 0x7f06003c;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonBackgroundColor = 0x7f06003d;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonIconColor = 0x7f06003e;
        public static int antelopTextOnSurfaceColor = 0x7f06003f;
        public static int background_floating_material_dark = 0x7f060040;
        public static int background_floating_material_light = 0x7f060041;
        public static int background_material_dark = 0x7f060042;
        public static int background_material_light = 0x7f060043;
        public static int biometric_error_color = 0x7f060044;
        public static int bright_foreground_disabled_material_dark = 0x7f060045;
        public static int bright_foreground_disabled_material_light = 0x7f060046;
        public static int bright_foreground_inverse_material_dark = 0x7f060047;
        public static int bright_foreground_inverse_material_light = 0x7f060048;
        public static int bright_foreground_material_dark = 0x7f060049;
        public static int bright_foreground_material_light = 0x7f06004a;
        public static int button_material_dark = 0x7f06004b;
        public static int button_material_light = 0x7f06004c;
        public static int cardview_dark_background = 0x7f06004f;
        public static int cardview_light_background = 0x7f060050;
        public static int cardview_shadow_end_color = 0x7f060051;
        public static int cardview_shadow_start_color = 0x7f060052;
        public static int common_google_signin_btn_text_dark = 0x7f060057;
        public static int common_google_signin_btn_text_dark_default = 0x7f060058;
        public static int common_google_signin_btn_text_dark_disabled = 0x7f060059;
        public static int common_google_signin_btn_text_dark_focused = 0x7f06005a;
        public static int common_google_signin_btn_text_dark_pressed = 0x7f06005b;
        public static int common_google_signin_btn_text_light = 0x7f06005c;
        public static int common_google_signin_btn_text_light_default = 0x7f06005d;
        public static int common_google_signin_btn_text_light_disabled = 0x7f06005e;
        public static int common_google_signin_btn_text_light_focused = 0x7f06005f;
        public static int common_google_signin_btn_text_light_pressed = 0x7f060060;
        public static int common_google_signin_btn_tint = 0x7f060061;
        public static int design_bottom_navigation_shadow_color = 0x7f060062;
        public static int design_box_stroke_color = 0x7f060063;
        public static int design_dark_default_color_background = 0x7f060064;
        public static int design_dark_default_color_error = 0x7f060065;
        public static int design_dark_default_color_on_background = 0x7f060066;
        public static int design_dark_default_color_on_error = 0x7f060067;
        public static int design_dark_default_color_on_primary = 0x7f060068;
        public static int design_dark_default_color_on_secondary = 0x7f060069;
        public static int design_dark_default_color_on_surface = 0x7f06006a;
        public static int design_dark_default_color_primary = 0x7f06006b;
        public static int design_dark_default_color_primary_dark = 0x7f06006c;
        public static int design_dark_default_color_primary_variant = 0x7f06006d;
        public static int design_dark_default_color_secondary = 0x7f06006e;
        public static int design_dark_default_color_secondary_variant = 0x7f06006f;
        public static int design_dark_default_color_surface = 0x7f060070;
        public static int design_default_color_background = 0x7f060071;
        public static int design_default_color_error = 0x7f060072;
        public static int design_default_color_on_background = 0x7f060073;
        public static int design_default_color_on_error = 0x7f060074;
        public static int design_default_color_on_primary = 0x7f060075;
        public static int design_default_color_on_secondary = 0x7f060076;
        public static int design_default_color_on_surface = 0x7f060077;
        public static int design_default_color_primary = 0x7f060078;
        public static int design_default_color_primary_dark = 0x7f060079;
        public static int design_default_color_primary_variant = 0x7f06007a;
        public static int design_default_color_secondary = 0x7f06007b;
        public static int design_default_color_secondary_variant = 0x7f06007c;
        public static int design_default_color_surface = 0x7f06007d;
        public static int design_error = 0x7f06007e;
        public static int design_fab_shadow_end_color = 0x7f06007f;
        public static int design_fab_shadow_mid_color = 0x7f060080;
        public static int design_fab_shadow_start_color = 0x7f060081;
        public static int design_fab_stroke_end_inner_color = 0x7f060082;
        public static int design_fab_stroke_end_outer_color = 0x7f060083;
        public static int design_fab_stroke_top_inner_color = 0x7f060084;
        public static int design_fab_stroke_top_outer_color = 0x7f060085;
        public static int design_icon_tint = 0x7f060086;
        public static int design_snackbar_background_color = 0x7f060087;
        public static int dim_foreground_disabled_material_dark = 0x7f060088;
        public static int dim_foreground_disabled_material_light = 0x7f060089;
        public static int dim_foreground_material_dark = 0x7f06008a;
        public static int dim_foreground_material_light = 0x7f06008b;
        public static int error_color_material_dark = 0x7f06008c;
        public static int error_color_material_light = 0x7f06008d;
        public static int foreground_material_dark = 0x7f06008e;
        public static int foreground_material_light = 0x7f06008f;
        public static int highlighted_text_material_dark = 0x7f060090;
        public static int highlighted_text_material_light = 0x7f060091;
        public static int material_blue_grey_800 = 0x7f060093;
        public static int material_blue_grey_900 = 0x7f060094;
        public static int material_blue_grey_950 = 0x7f060095;
        public static int material_deep_teal_200 = 0x7f060096;
        public static int material_deep_teal_500 = 0x7f060097;
        public static int material_grey_100 = 0x7f060098;
        public static int material_grey_300 = 0x7f060099;
        public static int material_grey_50 = 0x7f06009a;
        public static int material_grey_600 = 0x7f06009b;
        public static int material_grey_800 = 0x7f06009c;
        public static int material_grey_850 = 0x7f06009d;
        public static int material_grey_900 = 0x7f06009e;
        public static int material_on_background_disabled = 0x7f06009f;
        public static int material_on_background_emphasis_high_type = 0x7f0600a0;
        public static int material_on_background_emphasis_medium = 0x7f0600a1;
        public static int material_on_primary_disabled = 0x7f0600a2;
        public static int material_on_primary_emphasis_high_type = 0x7f0600a3;
        public static int material_on_primary_emphasis_medium = 0x7f0600a4;
        public static int material_on_surface_disabled = 0x7f0600a5;
        public static int material_on_surface_emphasis_high_type = 0x7f0600a6;
        public static int material_on_surface_emphasis_medium = 0x7f0600a7;
        public static int mtrl_btn_bg_color_selector = 0x7f0600ac;
        public static int mtrl_btn_ripple_color = 0x7f0600ad;
        public static int mtrl_btn_stroke_color_selector = 0x7f0600ae;
        public static int mtrl_btn_text_btn_bg_color_selector = 0x7f0600af;
        public static int mtrl_btn_text_btn_ripple_color = 0x7f0600b0;
        public static int mtrl_btn_text_color_disabled = 0x7f0600b1;
        public static int mtrl_btn_text_color_selector = 0x7f0600b2;
        public static int mtrl_btn_transparent_bg_color = 0x7f0600b3;
        public static int mtrl_calendar_item_stroke_color = 0x7f0600b4;
        public static int mtrl_calendar_selected_range = 0x7f0600b5;
        public static int mtrl_card_view_foreground = 0x7f0600b6;
        public static int mtrl_card_view_ripple = 0x7f0600b7;
        public static int mtrl_chip_background_color = 0x7f0600b8;
        public static int mtrl_chip_close_icon_tint = 0x7f0600b9;
        public static int mtrl_chip_surface_color = 0x7f0600bb;
        public static int mtrl_chip_text_color = 0x7f0600bc;
        public static int mtrl_choice_chip_background_color = 0x7f0600bd;
        public static int mtrl_choice_chip_ripple_color = 0x7f0600be;
        public static int mtrl_choice_chip_text_color = 0x7f0600bf;
        public static int mtrl_error = 0x7f0600c0;
        public static int mtrl_fab_ripple_color = 0x7f0600c4;
        public static int mtrl_filled_background_color = 0x7f0600c5;
        public static int mtrl_filled_icon_tint = 0x7f0600c6;
        public static int mtrl_filled_stroke_color = 0x7f0600c7;
        public static int mtrl_indicator_text_color = 0x7f0600c8;
        public static int mtrl_navigation_item_background_color = 0x7f0600c9;
        public static int mtrl_navigation_item_icon_tint = 0x7f0600ca;
        public static int mtrl_navigation_item_text_color = 0x7f0600cb;
        public static int mtrl_on_primary_text_btn_text_color_selector = 0x7f0600cc;
        public static int mtrl_outlined_icon_tint = 0x7f0600cd;
        public static int mtrl_outlined_stroke_color = 0x7f0600ce;
        public static int mtrl_popupmenu_overlay_color = 0x7f0600cf;
        public static int mtrl_scrim_color = 0x7f0600d0;
        public static int mtrl_tabs_colored_ripple_color = 0x7f0600d1;
        public static int mtrl_tabs_icon_color_selector = 0x7f0600d2;
        public static int mtrl_tabs_icon_color_selector_colored = 0x7f0600d3;
        public static int mtrl_tabs_legacy_text_color_selector = 0x7f0600d4;
        public static int mtrl_tabs_ripple_color = 0x7f0600d5;
        public static int mtrl_text_btn_text_color_selector = 0x7f0600d6;
        public static int mtrl_textinput_default_box_stroke_color = 0x7f0600d7;
        public static int mtrl_textinput_disabled_color = 0x7f0600d8;
        public static int mtrl_textinput_filled_box_default_background_color = 0x7f0600d9;
        public static int mtrl_textinput_focused_box_stroke_color = 0x7f0600da;
        public static int mtrl_textinput_hovered_box_stroke_color = 0x7f0600db;
        public static int notification_action_color_filter = 0x7f0600dc;
        public static int notification_icon_bg_color = 0x7f0600dd;
        public static int primary_dark_material_dark = 0x7f0600df;
        public static int primary_dark_material_light = 0x7f0600e0;
        public static int primary_material_dark = 0x7f0600e1;
        public static int primary_material_light = 0x7f0600e2;
        public static int primary_text_default_material_dark = 0x7f0600e3;
        public static int primary_text_default_material_light = 0x7f0600e4;
        public static int primary_text_disabled_material_dark = 0x7f0600e5;
        public static int primary_text_disabled_material_light = 0x7f0600e6;
        public static int ripple_material_dark = 0x7f0600e7;
        public static int ripple_material_light = 0x7f0600e8;
        public static int secondary_text_default_material_dark = 0x7f0600e9;
        public static int secondary_text_default_material_light = 0x7f0600ea;
        public static int secondary_text_disabled_material_dark = 0x7f0600eb;
        public static int secondary_text_disabled_material_light = 0x7f0600ec;
        public static int switch_thumb_disabled_material_dark = 0x7f0600ed;
        public static int switch_thumb_disabled_material_light = 0x7f0600ee;
        public static int switch_thumb_material_dark = 0x7f0600ef;
        public static int switch_thumb_material_light = 0x7f0600f0;
        public static int switch_thumb_normal_material_dark = 0x7f0600f1;
        public static int switch_thumb_normal_material_light = 0x7f0600f2;
        public static int tapandpay_sdk_default_color_background = 0x7f0600f3;
        public static int tapandpay_sdk_default_color_primary_variant = 0x7f0600f4;
        public static int tapandpay_sdk_default_color_secondary = 0x7f0600f5;
        public static int tooltip_background_dark = 0x7f0600f8;
        public static int tooltip_background_light = 0x7f0600f9;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$dimen.smali */
    public static final class dimen {
        public static int abc_action_bar_content_inset_material = 0x7f070000;
        public static int abc_action_bar_content_inset_with_nav = 0x7f070001;
        public static int abc_action_bar_default_height_material = 0x7f070002;
        public static int abc_action_bar_default_padding_end_material = 0x7f070003;
        public static int abc_action_bar_default_padding_start_material = 0x7f070004;
        public static int abc_action_bar_elevation_material = 0x7f070005;
        public static int abc_action_bar_icon_vertical_padding_material = 0x7f070006;
        public static int abc_action_bar_overflow_padding_end_material = 0x7f070007;
        public static int abc_action_bar_overflow_padding_start_material = 0x7f070008;
        public static int abc_action_bar_stacked_max_height = 0x7f070009;
        public static int abc_action_bar_stacked_tab_max_width = 0x7f07000a;
        public static int abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b;
        public static int abc_action_bar_subtitle_top_margin_material = 0x7f07000c;
        public static int abc_action_button_min_height_material = 0x7f07000d;
        public static int abc_action_button_min_width_material = 0x7f07000e;
        public static int abc_action_button_min_width_overflow_material = 0x7f07000f;
        public static int abc_alert_dialog_button_bar_height = 0x7f070010;
        public static int abc_alert_dialog_button_dimen = 0x7f070011;
        public static int abc_button_inset_horizontal_material = 0x7f070012;
        public static int abc_button_inset_vertical_material = 0x7f070013;
        public static int abc_button_padding_horizontal_material = 0x7f070014;
        public static int abc_button_padding_vertical_material = 0x7f070015;
        public static int abc_cascading_menus_min_smallest_width = 0x7f070016;
        public static int abc_config_prefDialogWidth = 0x7f070017;
        public static int abc_control_corner_material = 0x7f070018;
        public static int abc_control_inset_material = 0x7f070019;
        public static int abc_control_padding_material = 0x7f07001a;
        public static int abc_dialog_corner_radius_material = 0x7f07001b;
        public static int abc_dialog_fixed_height_major = 0x7f07001c;
        public static int abc_dialog_fixed_height_minor = 0x7f07001d;
        public static int abc_dialog_fixed_width_major = 0x7f07001e;
        public static int abc_dialog_fixed_width_minor = 0x7f07001f;
        public static int abc_dialog_list_padding_bottom_no_buttons = 0x7f070020;
        public static int abc_dialog_list_padding_top_no_title = 0x7f070021;
        public static int abc_dialog_min_width_major = 0x7f070022;
        public static int abc_dialog_min_width_minor = 0x7f070023;
        public static int abc_dialog_padding_material = 0x7f070024;
        public static int abc_dialog_padding_top_material = 0x7f070025;
        public static int abc_dialog_title_divider_material = 0x7f070026;
        public static int abc_disabled_alpha_material_dark = 0x7f070027;
        public static int abc_disabled_alpha_material_light = 0x7f070028;
        public static int abc_dropdownitem_icon_width = 0x7f070029;
        public static int abc_dropdownitem_text_padding_left = 0x7f07002a;
        public static int abc_dropdownitem_text_padding_right = 0x7f07002b;
        public static int abc_edit_text_inset_bottom_material = 0x7f07002c;
        public static int abc_edit_text_inset_horizontal_material = 0x7f07002d;
        public static int abc_edit_text_inset_top_material = 0x7f07002e;
        public static int abc_floating_window_z = 0x7f07002f;
        public static int abc_list_item_height_large_material = 0x7f070030;
        public static int abc_list_item_height_material = 0x7f070031;
        public static int abc_list_item_height_small_material = 0x7f070032;
        public static int abc_list_item_padding_horizontal_material = 0x7f070033;
        public static int abc_panel_menu_list_width = 0x7f070034;
        public static int abc_progress_bar_height_material = 0x7f070035;
        public static int abc_search_view_preferred_height = 0x7f070036;
        public static int abc_search_view_preferred_width = 0x7f070037;
        public static int abc_seekbar_track_background_height_material = 0x7f070038;
        public static int abc_seekbar_track_progress_height_material = 0x7f070039;
        public static int abc_select_dialog_padding_start_material = 0x7f07003a;
        public static int abc_star_big = 0x7f07003b;
        public static int abc_star_medium = 0x7f07003c;
        public static int abc_star_small = 0x7f07003d;
        public static int abc_switch_padding = 0x7f07003e;
        public static int abc_text_size_body_1_material = 0x7f07003f;
        public static int abc_text_size_body_2_material = 0x7f070040;
        public static int abc_text_size_button_material = 0x7f070041;
        public static int abc_text_size_caption_material = 0x7f070042;
        public static int abc_text_size_display_1_material = 0x7f070043;
        public static int abc_text_size_display_2_material = 0x7f070044;
        public static int abc_text_size_display_3_material = 0x7f070045;
        public static int abc_text_size_display_4_material = 0x7f070046;
        public static int abc_text_size_headline_material = 0x7f070047;
        public static int abc_text_size_large_material = 0x7f070048;
        public static int abc_text_size_medium_material = 0x7f070049;
        public static int abc_text_size_menu_header_material = 0x7f07004a;
        public static int abc_text_size_menu_material = 0x7f07004b;
        public static int abc_text_size_small_material = 0x7f07004c;
        public static int abc_text_size_subhead_material = 0x7f07004d;
        public static int abc_text_size_subtitle_material_toolbar = 0x7f07004e;
        public static int abc_text_size_title_material = 0x7f07004f;
        public static int abc_text_size_title_material_toolbar = 0x7f070050;
        public static int antelopBigpDimen = 0x7f070052;
        public static int antelopCardDisplayCardCornerRadius = 0x7f070053;
        public static int antelopCardDisplayCardMargin = 0x7f070054;
        public static int antelopConsentPromptLayoutGapBelowBottom = 0x7f070055;
        public static int antelopConsentPromptLayoutGapBelowTitle = 0x7f070056;
        public static int antelopConsentPromptLayoutImageViewPadding = 0x7f070057;
        public static int antelopConsentPromptLayoutImageViewSizeMedium = 0x7f070058;
        public static int antelopConsentPromptLayoutMargin = 0x7f070059;
        public static int antelopLargeDimen = 0x7f07005a;
        public static int antelopNormalDimen = 0x7f07005b;
        public static int antelopNormalpDimen = 0x7f07005c;
        public static int antelopPinDisplayCardCornerRadius = 0x7f07005d;
        public static int antelopPinDisplayCardMargin = 0x7f07005e;
        public static int antelopPinPromptLayoutImageViewPadding = 0x7f07005f;
        public static int antelopPinPromptLayoutImageViewSizeMedium = 0x7f070060;
        public static int antelopPinPromptLayoutMargin = 0x7f070061;
        public static int antelopPromptLayoutDescriptionMarginEnd = 0x7f070062;
        public static int antelopPromptLayoutDescriptionMarginStart = 0x7f070063;
        public static int antelopPromptLayoutGapBelowBottom = 0x7f070064;
        public static int antelopPromptLayoutGapBelowDescription = 0x7f070065;
        public static int antelopPromptLayoutGapBelowSubtitle = 0x7f070066;
        public static int antelopPromptLayoutGapBelowTitle = 0x7f070067;
        public static int antelopPromptLayoutImageViewPadding = 0x7f070068;
        public static int antelopPromptLayoutImageViewSizeBig = 0x7f070069;
        public static int antelopPromptLayoutImageViewSizeMedium = 0x7f07006a;
        public static int antelopPromptLayoutImageViewSizeSmall = 0x7f07006b;
        public static int antelopPromptLayoutMargin = 0x7f07006c;
        public static int antelopScreenUnlockPromptLayoutGapBelowBottom = 0x7f07006d;
        public static int antelopScreenUnlockPromptLayoutGapBelowTitle = 0x7f07006e;
        public static int antelopScreenUnlockPromptLayoutImageViewPadding = 0x7f07006f;
        public static int antelopScreenUnlockPromptLayoutImageViewSizeMedium = 0x7f070070;
        public static int antelopScreenUnlockPromptLayoutMargin = 0x7f070071;
        public static int antelopSecureCardDisplayCardCornerRadius = 0x7f070072;
        public static int antelopSecureCardDisplayCardMargin = 0x7f070073;
        public static int antelopSecureCardDisplayLargeSize = 0x7f070074;
        public static int antelopSecureCardDisplayMediumSize = 0x7f070075;
        public static int antelopSecurePinDisplayCardCornerRadius = 0x7f070076;
        public static int antelopSecurePinDisplayCardMargin = 0x7f070077;
        public static int antelopSecureVirtualCardNumberDisplayCardCornerRadius = 0x7f070078;
        public static int antelopSecureVirtualCardNumberDisplayCardMargin = 0x7f070079;
        public static int antelopSecureVirtualCardNumberDisplayLargeSize = 0x7f07007a;
        public static int antelopSecureVirtualCardNumberDisplayMediumSize = 0x7f07007b;
        public static int antelopSmallpDimen = 0x7f07007c;
        public static int antelopTextLargeSize = 0x7f07007d;
        public static int antelopTextMediumSize = 0x7f07007e;
        public static int antelopTextXLargeSize = 0x7f07007f;
        public static int antelopTextXXLargeSize = 0x7f070080;
        public static int antelopXbpDimen = 0x7f070081;
        public static int antelopXlDimen = 0x7f070082;
        public static int antelopXsDimen = 0x7f070083;
        public static int antelopXspDimen = 0x7f070084;
        public static int antelopXxbpDimen = 0x7f070085;
        public static int antelopXxlDimen = 0x7f070086;
        public static int antelopXxsDimen = 0x7f070087;
        public static int antelopXxspDimen = 0x7f070088;
        public static int antelopsSmallDimen = 0x7f070089;
        public static int appcompat_dialog_background_inset = 0x7f07008a;
        public static int cardview_compat_inset_shadow = 0x7f07008b;
        public static int cardview_default_elevation = 0x7f07008c;
        public static int cardview_default_radius = 0x7f07008d;
        public static int compat_button_inset_horizontal_material = 0x7f07008e;
        public static int compat_button_inset_vertical_material = 0x7f07008f;
        public static int compat_button_padding_horizontal_material = 0x7f070090;
        public static int compat_button_padding_vertical_material = 0x7f070091;
        public static int compat_control_corner_material = 0x7f070092;
        public static int compat_notification_large_icon_max_height = 0x7f070093;
        public static int compat_notification_large_icon_max_width = 0x7f070094;
        public static int design_appbar_elevation = 0x7f070096;
        public static int design_bottom_navigation_active_item_max_width = 0x7f070097;
        public static int design_bottom_navigation_active_item_min_width = 0x7f070098;
        public static int design_bottom_navigation_active_text_size = 0x7f070099;
        public static int design_bottom_navigation_elevation = 0x7f07009a;
        public static int design_bottom_navigation_height = 0x7f07009b;
        public static int design_bottom_navigation_icon_size = 0x7f07009c;
        public static int design_bottom_navigation_item_max_width = 0x7f07009d;
        public static int design_bottom_navigation_item_min_width = 0x7f07009e;
        public static int design_bottom_navigation_margin = 0x7f07009f;
        public static int design_bottom_navigation_shadow_height = 0x7f0700a0;
        public static int design_bottom_navigation_text_size = 0x7f0700a1;
        public static int design_bottom_sheet_elevation = 0x7f0700a2;
        public static int design_bottom_sheet_modal_elevation = 0x7f0700a3;
        public static int design_bottom_sheet_peek_height_min = 0x7f0700a4;
        public static int design_fab_border_width = 0x7f0700a5;
        public static int design_fab_elevation = 0x7f0700a6;
        public static int design_fab_image_size = 0x7f0700a7;
        public static int design_fab_size_mini = 0x7f0700a8;
        public static int design_fab_size_normal = 0x7f0700a9;
        public static int design_fab_translation_z_hovered_focused = 0x7f0700aa;
        public static int design_fab_translation_z_pressed = 0x7f0700ab;
        public static int design_navigation_elevation = 0x7f0700ac;
        public static int design_navigation_icon_padding = 0x7f0700ad;
        public static int design_navigation_icon_size = 0x7f0700ae;
        public static int design_navigation_item_horizontal_padding = 0x7f0700af;
        public static int design_navigation_item_icon_padding = 0x7f0700b0;
        public static int design_navigation_max_width = 0x7f0700b1;
        public static int design_navigation_padding_bottom = 0x7f0700b2;
        public static int design_navigation_separator_vertical_padding = 0x7f0700b3;
        public static int design_snackbar_action_inline_max_width = 0x7f0700b4;
        public static int design_snackbar_action_text_color_alpha = 0x7f0700b5;
        public static int design_snackbar_background_corner_radius = 0x7f0700b6;
        public static int design_snackbar_elevation = 0x7f0700b7;
        public static int design_snackbar_extra_spacing_horizontal = 0x7f0700b8;
        public static int design_snackbar_max_width = 0x7f0700b9;
        public static int design_snackbar_min_width = 0x7f0700ba;
        public static int design_snackbar_padding_horizontal = 0x7f0700bb;
        public static int design_snackbar_padding_vertical = 0x7f0700bc;
        public static int design_snackbar_padding_vertical_2lines = 0x7f0700bd;
        public static int design_snackbar_text_size = 0x7f0700be;
        public static int design_tab_max_width = 0x7f0700bf;
        public static int design_tab_scrollable_min_width = 0x7f0700c0;
        public static int design_tab_text_size = 0x7f0700c1;
        public static int design_tab_text_size_2line = 0x7f0700c2;
        public static int design_textinput_caption_translate_y = 0x7f0700c3;
        public static int disabled_alpha_material_dark = 0x7f0700c4;
        public static int disabled_alpha_material_light = 0x7f0700c5;
        public static int fastscroll_default_thickness = 0x7f0700c7;
        public static int fastscroll_margin = 0x7f0700c8;
        public static int fastscroll_minimum_range = 0x7f0700c9;
        public static int fingerprint_icon_size = 0x7f0700ca;
        public static int highlight_alpha_material_colored = 0x7f0700cb;
        public static int highlight_alpha_material_dark = 0x7f0700cc;
        public static int highlight_alpha_material_light = 0x7f0700cd;
        public static int hint_alpha_material_dark = 0x7f0700ce;
        public static int hint_alpha_material_light = 0x7f0700cf;
        public static int hint_pressed_alpha_material_dark = 0x7f0700d0;
        public static int hint_pressed_alpha_material_light = 0x7f0700d1;
        public static int item_touch_helper_max_drag_scroll_per_frame = 0x7f0700d2;
        public static int item_touch_helper_swipe_escape_max_velocity = 0x7f0700d3;
        public static int item_touch_helper_swipe_escape_velocity = 0x7f0700d4;
        public static int material_emphasis_disabled = 0x7f0700d5;
        public static int material_emphasis_high_type = 0x7f0700d6;
        public static int material_emphasis_medium = 0x7f0700d7;
        public static int mtrl_alert_dialog_background_inset_bottom = 0x7f0700da;
        public static int mtrl_alert_dialog_background_inset_end = 0x7f0700db;
        public static int mtrl_alert_dialog_background_inset_start = 0x7f0700dc;
        public static int mtrl_alert_dialog_background_inset_top = 0x7f0700dd;
        public static int mtrl_alert_dialog_picker_background_inset = 0x7f0700de;
        public static int mtrl_badge_horizontal_edge_offset = 0x7f0700df;
        public static int mtrl_badge_long_text_horizontal_padding = 0x7f0700e0;
        public static int mtrl_badge_text_horizontal_edge_offset = 0x7f0700e2;
        public static int mtrl_badge_text_size = 0x7f0700e3;
        public static int mtrl_bottomappbar_fabOffsetEndMode = 0x7f0700e5;
        public static int mtrl_bottomappbar_fab_bottom_margin = 0x7f0700e6;
        public static int mtrl_bottomappbar_fab_cradle_margin = 0x7f0700e7;
        public static int mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e8;
        public static int mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f0700e9;
        public static int mtrl_bottomappbar_height = 0x7f0700ea;
        public static int mtrl_btn_corner_radius = 0x7f0700eb;
        public static int mtrl_btn_dialog_btn_min_width = 0x7f0700ec;
        public static int mtrl_btn_disabled_elevation = 0x7f0700ed;
        public static int mtrl_btn_disabled_z = 0x7f0700ee;
        public static int mtrl_btn_elevation = 0x7f0700ef;
        public static int mtrl_btn_focused_z = 0x7f0700f0;
        public static int mtrl_btn_hovered_z = 0x7f0700f1;
        public static int mtrl_btn_icon_btn_padding_left = 0x7f0700f2;
        public static int mtrl_btn_icon_padding = 0x7f0700f3;
        public static int mtrl_btn_inset = 0x7f0700f4;
        public static int mtrl_btn_letter_spacing = 0x7f0700f5;
        public static int mtrl_btn_padding_bottom = 0x7f0700f6;
        public static int mtrl_btn_padding_left = 0x7f0700f7;
        public static int mtrl_btn_padding_right = 0x7f0700f8;
        public static int mtrl_btn_padding_top = 0x7f0700f9;
        public static int mtrl_btn_pressed_z = 0x7f0700fa;
        public static int mtrl_btn_stroke_size = 0x7f0700fb;
        public static int mtrl_btn_text_btn_icon_padding = 0x7f0700fc;
        public static int mtrl_btn_text_btn_padding_left = 0x7f0700fd;
        public static int mtrl_btn_text_btn_padding_right = 0x7f0700fe;
        public static int mtrl_btn_text_size = 0x7f0700ff;
        public static int mtrl_btn_z = 0x7f070100;
        public static int mtrl_calendar_action_height = 0x7f070101;
        public static int mtrl_calendar_action_padding = 0x7f070102;
        public static int mtrl_calendar_bottom_padding = 0x7f070103;
        public static int mtrl_calendar_content_padding = 0x7f070104;
        public static int mtrl_calendar_day_corner = 0x7f070105;
        public static int mtrl_calendar_day_height = 0x7f070106;
        public static int mtrl_calendar_day_horizontal_padding = 0x7f070107;
        public static int mtrl_calendar_day_today_stroke = 0x7f070108;
        public static int mtrl_calendar_day_vertical_padding = 0x7f070109;
        public static int mtrl_calendar_day_width = 0x7f07010a;
        public static int mtrl_calendar_days_of_week_height = 0x7f07010b;
        public static int mtrl_calendar_dialog_background_inset = 0x7f07010c;
        public static int mtrl_calendar_header_content_padding = 0x7f07010d;
        public static int mtrl_calendar_header_content_padding_fullscreen = 0x7f07010e;
        public static int mtrl_calendar_header_divider_thickness = 0x7f07010f;
        public static int mtrl_calendar_header_height = 0x7f070110;
        public static int mtrl_calendar_header_height_fullscreen = 0x7f070111;
        public static int mtrl_calendar_header_selection_line_height = 0x7f070112;
        public static int mtrl_calendar_header_text_padding = 0x7f070113;
        public static int mtrl_calendar_header_toggle_margin_bottom = 0x7f070114;
        public static int mtrl_calendar_header_toggle_margin_top = 0x7f070115;
        public static int mtrl_calendar_landscape_header_width = 0x7f070116;
        public static int mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f070117;
        public static int mtrl_calendar_month_horizontal_padding = 0x7f070118;
        public static int mtrl_calendar_month_vertical_padding = 0x7f070119;
        public static int mtrl_calendar_navigation_bottom_padding = 0x7f07011a;
        public static int mtrl_calendar_navigation_height = 0x7f07011b;
        public static int mtrl_calendar_navigation_top_padding = 0x7f07011c;
        public static int mtrl_calendar_pre_l_text_clip_padding = 0x7f07011d;
        public static int mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f07011e;
        public static int mtrl_calendar_selection_text_baseline_to_bottom = 0x7f07011f;
        public static int mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f070120;
        public static int mtrl_calendar_selection_text_baseline_to_top = 0x7f070121;
        public static int mtrl_calendar_text_input_padding_top = 0x7f070122;
        public static int mtrl_calendar_title_baseline_to_top = 0x7f070123;
        public static int mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f070124;
        public static int mtrl_calendar_year_corner = 0x7f070125;
        public static int mtrl_calendar_year_height = 0x7f070126;
        public static int mtrl_calendar_year_horizontal_padding = 0x7f070127;
        public static int mtrl_calendar_year_vertical_padding = 0x7f070128;
        public static int mtrl_calendar_year_width = 0x7f070129;
        public static int mtrl_card_checked_icon_margin = 0x7f07012a;
        public static int mtrl_card_checked_icon_size = 0x7f07012b;
        public static int mtrl_card_corner_radius = 0x7f07012c;
        public static int mtrl_card_dragged_z = 0x7f07012d;
        public static int mtrl_card_elevation = 0x7f07012e;
        public static int mtrl_card_spacing = 0x7f07012f;
        public static int mtrl_chip_pressed_translation_z = 0x7f070130;
        public static int mtrl_chip_text_size = 0x7f070131;
        public static int mtrl_exposed_dropdown_menu_popup_elevation = 0x7f070132;
        public static int mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f070133;
        public static int mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f070134;
        public static int mtrl_extended_fab_bottom_padding = 0x7f070135;
        public static int mtrl_extended_fab_disabled_elevation = 0x7f070137;
        public static int mtrl_extended_fab_disabled_translation_z = 0x7f070138;
        public static int mtrl_extended_fab_elevation = 0x7f070139;
        public static int mtrl_extended_fab_end_padding = 0x7f07013a;
        public static int mtrl_extended_fab_end_padding_icon = 0x7f07013b;
        public static int mtrl_extended_fab_icon_size = 0x7f07013c;
        public static int mtrl_extended_fab_icon_text_spacing = 0x7f07013d;
        public static int mtrl_extended_fab_min_height = 0x7f07013e;
        public static int mtrl_extended_fab_min_width = 0x7f07013f;
        public static int mtrl_extended_fab_start_padding = 0x7f070140;
        public static int mtrl_extended_fab_start_padding_icon = 0x7f070141;
        public static int mtrl_extended_fab_top_padding = 0x7f070142;
        public static int mtrl_extended_fab_translation_z_base = 0x7f070143;
        public static int mtrl_extended_fab_translation_z_hovered_focused = 0x7f070144;
        public static int mtrl_extended_fab_translation_z_pressed = 0x7f070145;
        public static int mtrl_fab_elevation = 0x7f070146;
        public static int mtrl_fab_min_touch_target = 0x7f070147;
        public static int mtrl_fab_translation_z_hovered_focused = 0x7f070148;
        public static int mtrl_fab_translation_z_pressed = 0x7f070149;
        public static int mtrl_high_ripple_default_alpha = 0x7f07014a;
        public static int mtrl_high_ripple_focused_alpha = 0x7f07014b;
        public static int mtrl_high_ripple_hovered_alpha = 0x7f07014c;
        public static int mtrl_high_ripple_pressed_alpha = 0x7f07014d;
        public static int mtrl_low_ripple_default_alpha = 0x7f07014f;
        public static int mtrl_low_ripple_focused_alpha = 0x7f070150;
        public static int mtrl_low_ripple_hovered_alpha = 0x7f070151;
        public static int mtrl_low_ripple_pressed_alpha = 0x7f070152;
        public static int mtrl_min_touch_target_size = 0x7f070153;
        public static int mtrl_navigation_elevation = 0x7f070154;
        public static int mtrl_navigation_item_horizontal_padding = 0x7f070155;
        public static int mtrl_navigation_item_icon_padding = 0x7f070156;
        public static int mtrl_navigation_item_icon_size = 0x7f070157;
        public static int mtrl_navigation_item_shape_horizontal_margin = 0x7f070158;
        public static int mtrl_navigation_item_shape_vertical_margin = 0x7f070159;
        public static int mtrl_shape_corner_size_large_component = 0x7f07015a;
        public static int mtrl_shape_corner_size_medium_component = 0x7f07015b;
        public static int mtrl_shape_corner_size_small_component = 0x7f07015c;
        public static int mtrl_snackbar_action_text_color_alpha = 0x7f07015d;
        public static int mtrl_snackbar_background_corner_radius = 0x7f07015e;
        public static int mtrl_snackbar_background_overlay_color_alpha = 0x7f07015f;
        public static int mtrl_snackbar_margin = 0x7f070160;
        public static int mtrl_switch_thumb_elevation = 0x7f070161;
        public static int mtrl_textinput_box_corner_radius_medium = 0x7f070162;
        public static int mtrl_textinput_box_corner_radius_small = 0x7f070163;
        public static int mtrl_textinput_box_label_cutout_padding = 0x7f070164;
        public static int mtrl_textinput_box_stroke_width_default = 0x7f070165;
        public static int mtrl_textinput_box_stroke_width_focused = 0x7f070166;
        public static int mtrl_textinput_end_icon_margin_start = 0x7f070167;
        public static int mtrl_textinput_outline_box_expanded_padding = 0x7f070168;
        public static int mtrl_textinput_start_icon_margin_end = 0x7f070169;
        public static int mtrl_toolbar_default_height = 0x7f07016a;
        public static int notification_action_icon_size = 0x7f07016b;
        public static int notification_action_text_size = 0x7f07016c;
        public static int notification_big_circle_margin = 0x7f07016d;
        public static int notification_content_margin_start = 0x7f07016e;
        public static int notification_large_icon_height = 0x7f07016f;
        public static int notification_large_icon_width = 0x7f070170;
        public static int notification_main_column_padding_top = 0x7f070171;
        public static int notification_media_narrow_margin = 0x7f070172;
        public static int notification_right_icon_size = 0x7f070173;
        public static int notification_right_side_padding_top = 0x7f070174;
        public static int notification_small_icon_background_padding = 0x7f070175;
        public static int notification_small_icon_size_as_large = 0x7f070176;
        public static int notification_subtext_size = 0x7f070177;
        public static int notification_top_pad = 0x7f070178;
        public static int notification_top_pad_large_text = 0x7f070179;
        public static int tooltip_corner_radius = 0x7f070186;
        public static int tooltip_horizontal_padding = 0x7f070187;
        public static int tooltip_margin = 0x7f070188;
        public static int tooltip_precise_anchor_extra_offset = 0x7f070189;
        public static int tooltip_precise_anchor_threshold = 0x7f07018a;
        public static int tooltip_vertical_padding = 0x7f07018b;
        public static int tooltip_y_offset_non_touch = 0x7f07018c;
        public static int tooltip_y_offset_touch = 0x7f07018d;

        private dimen() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$drawable.smali */
    public static final class drawable {
        public static int abc_ab_share_pack_mtrl_alpha = 0x7f080076;
        public static int abc_action_bar_item_background_material = 0x7f080077;
        public static int abc_btn_borderless_material = 0x7f080078;
        public static int abc_btn_check_material = 0x7f080079;
        public static int abc_btn_check_material_anim = 0x7f08007a;
        public static int abc_btn_check_to_on_mtrl_000 = 0x7f08007b;
        public static int abc_btn_check_to_on_mtrl_015 = 0x7f08007c;
        public static int abc_btn_colored_material = 0x7f08007d;
        public static int abc_btn_default_mtrl_shape = 0x7f08007e;
        public static int abc_btn_radio_material = 0x7f08007f;
        public static int abc_btn_radio_material_anim = 0x7f080080;
        public static int abc_btn_radio_to_on_mtrl_000 = 0x7f080081;
        public static int abc_btn_radio_to_on_mtrl_015 = 0x7f080082;
        public static int abc_btn_switch_to_on_mtrl_00001 = 0x7f080083;
        public static int abc_btn_switch_to_on_mtrl_00012 = 0x7f080084;
        public static int abc_cab_background_internal_bg = 0x7f080085;
        public static int abc_cab_background_top_material = 0x7f080086;
        public static int abc_cab_background_top_mtrl_alpha = 0x7f080087;
        public static int abc_control_background_material = 0x7f080088;
        public static int abc_dialog_material_background = 0x7f080089;
        public static int abc_edit_text_material = 0x7f08008a;
        public static int abc_ic_ab_back_material = 0x7f08008b;
        public static int abc_ic_arrow_drop_right_black_24dp = 0x7f08008c;
        public static int abc_ic_clear_material = 0x7f08008d;
        public static int abc_ic_commit_search_api_mtrl_alpha = 0x7f08008e;
        public static int abc_ic_go_search_api_material = 0x7f08008f;
        public static int abc_ic_menu_copy_mtrl_am_alpha = 0x7f080090;
        public static int abc_ic_menu_cut_mtrl_alpha = 0x7f080091;
        public static int abc_ic_menu_overflow_material = 0x7f080092;
        public static int abc_ic_menu_paste_mtrl_am_alpha = 0x7f080093;
        public static int abc_ic_menu_selectall_mtrl_alpha = 0x7f080094;
        public static int abc_ic_menu_share_mtrl_alpha = 0x7f080095;
        public static int abc_ic_search_api_material = 0x7f080096;
        public static int abc_ic_voice_search_api_material = 0x7f080097;
        public static int abc_item_background_holo_dark = 0x7f080098;
        public static int abc_item_background_holo_light = 0x7f080099;
        public static int abc_list_divider_material = 0x7f08009a;
        public static int abc_list_divider_mtrl_alpha = 0x7f08009b;
        public static int abc_list_focused_holo = 0x7f08009c;
        public static int abc_list_longpressed_holo = 0x7f08009d;
        public static int abc_list_pressed_holo_dark = 0x7f08009e;
        public static int abc_list_pressed_holo_light = 0x7f08009f;
        public static int abc_list_selector_background_transition_holo_dark = 0x7f0800a0;
        public static int abc_list_selector_background_transition_holo_light = 0x7f0800a1;
        public static int abc_list_selector_disabled_holo_dark = 0x7f0800a2;
        public static int abc_list_selector_disabled_holo_light = 0x7f0800a3;
        public static int abc_list_selector_holo_dark = 0x7f0800a4;
        public static int abc_list_selector_holo_light = 0x7f0800a5;
        public static int abc_menu_hardkey_panel_mtrl_mult = 0x7f0800a6;
        public static int abc_popup_background_mtrl_mult = 0x7f0800a7;
        public static int abc_ratingbar_indicator_material = 0x7f0800a8;
        public static int abc_ratingbar_material = 0x7f0800a9;
        public static int abc_ratingbar_small_material = 0x7f0800aa;
        public static int abc_scrubber_control_off_mtrl_alpha = 0x7f0800ab;
        public static int abc_scrubber_control_to_pressed_mtrl_000 = 0x7f0800ac;
        public static int abc_scrubber_control_to_pressed_mtrl_005 = 0x7f0800ad;
        public static int abc_scrubber_primary_mtrl_alpha = 0x7f0800ae;
        public static int abc_scrubber_track_mtrl_alpha = 0x7f0800af;
        public static int abc_seekbar_thumb_material = 0x7f0800b0;
        public static int abc_seekbar_tick_mark_material = 0x7f0800b1;
        public static int abc_seekbar_track_material = 0x7f0800b2;
        public static int abc_spinner_mtrl_am_alpha = 0x7f0800b3;
        public static int abc_spinner_textfield_background_material = 0x7f0800b4;
        public static int abc_star_black_48dp = 0x7f0800b5;
        public static int abc_star_half_black_48dp = 0x7f0800b6;
        public static int abc_switch_thumb_material = 0x7f0800b7;
        public static int abc_switch_track_mtrl_alpha = 0x7f0800b8;
        public static int abc_tab_indicator_material = 0x7f0800b9;
        public static int abc_tab_indicator_mtrl_alpha = 0x7f0800ba;
        public static int abc_text_cursor_material = 0x7f0800bb;
        public static int abc_text_select_handle_left_mtrl = 0x7f0800bc;
        public static int abc_text_select_handle_middle_mtrl = 0x7f0800bd;
        public static int abc_text_select_handle_right_mtrl = 0x7f0800be;
        public static int abc_textfield_activated_mtrl_alpha = 0x7f0800bf;
        public static int abc_textfield_default_mtrl_alpha = 0x7f0800c0;
        public static int abc_textfield_search_activated_mtrl_alpha = 0x7f0800c1;
        public static int abc_textfield_search_default_mtrl_alpha = 0x7f0800c2;
        public static int abc_textfield_search_material = 0x7f0800c3;
        public static int abc_vector_test = 0x7f0800c4;
        public static int antelopCardDisplayCloseButtonIcon = 0x7f0800c5;
        public static int antelopCardDisplayCopyPanButtonIcon = 0x7f0800c6;
        public static int antelopCardDisplayDefaultCardBackground = 0x7f0800c7;
        public static int antelopConsentPromptCancelButtonIcon = 0x7f0800c8;
        public static int antelopConsentPromptCancelButtonIconDefault = 0x7f0800c9;
        public static int antelopConsentPromptIcon = 0x7f0800ca;
        public static int antelopConsentPromptIconDefault = 0x7f0800cb;
        public static int antelopDeviceBiometricPromptIcon = 0x7f0800cc;
        public static int antelopDeviceBiometricPromptIconDefault = 0x7f0800cd;
        public static int antelopKeypadViewDefaultBulletIcon = 0x7f0800ce;
        public static int antelopKeypadViewDefaultBulletIconDefault = 0x7f0800cf;
        public static int antelopKeypadViewDefaultDeleteButtonIcon = 0x7f0800d0;
        public static int antelopKeypadViewDefaultDeleteButtonIconDefault = 0x7f0800d1;
        public static int antelopPinDisplayCloseButtonIcon = 0x7f0800d2;
        public static int antelopPinDisplayDefaultCardBackground = 0x7f0800d3;
        public static int antelopPinPromptBulletIcon = 0x7f0800d4;
        public static int antelopPinPromptBulletIconDefault = 0x7f0800d5;
        public static int antelopPinPromptCancelButtonIcon = 0x7f0800d6;
        public static int antelopPinPromptCancelButtonIconDefault = 0x7f0800d7;
        public static int antelopPinPromptDeleteButtonIcon = 0x7f0800d8;
        public static int antelopPinPromptDeleteButtonIconDefault = 0x7f0800d9;
        public static int antelopPinPromptIcon = 0x7f0800da;
        public static int antelopPinPromptIconDefault = 0x7f0800db;
        public static int antelopScreenUnlockPromptCancelButtonIcon = 0x7f0800dc;
        public static int antelopScreenUnlockPromptCancelButtonIconDefault = 0x7f0800dd;
        public static int antelopScreenUnlockPromptIcon = 0x7f0800de;
        public static int antelopScreenUnlockPromptIconDefault = 0x7f0800df;
        public static int antelopSecureCardDisplayCloseButtonIcon = 0x7f0800e0;
        public static int antelopSecureCardDisplayCloseButtonIconDefault = 0x7f0800e1;
        public static int antelopSecureCardDisplayCopyPanButtonIcon = 0x7f0800e2;
        public static int antelopSecureCardDisplayCopyPanButtonIconDefault = 0x7f0800e3;
        public static int antelopSecureCardDisplayDefaultCardBackground = 0x7f0800e4;
        public static int antelopSecureCardDisplayDefaultCardBackgroundDefault = 0x7f0800e5;
        public static int antelopSecurePinDisplayCloseButtonIcon = 0x7f0800e6;
        public static int antelopSecurePinDisplayCloseButtonIconDefault = 0x7f0800e7;
        public static int antelopSecurePinDisplayDefaultCardBackground = 0x7f0800e8;
        public static int antelopSecurePinDisplayDefaultCardBackgroundDefault = 0x7f0800e9;
        public static int antelopSecurePinInputBulletIcon = 0x7f0800ea;
        public static int antelopSecurePinInputBulletIconDefault = 0x7f0800eb;
        public static int antelopSecurePinInputCloseButtonIcon = 0x7f0800ec;
        public static int antelopSecurePinInputCloseButtonIconDefault = 0x7f0800ed;
        public static int antelopSecurePinInputDeleteButtonIcon = 0x7f0800ee;
        public static int antelopSecurePinInputDeleteButtonIconDefault = 0x7f0800ef;
        public static int antelopSecureVirtualCardNumberDisplayCloseButtonIcon = 0x7f0800f0;
        public static int antelopSecureVirtualCardNumberDisplayCloseButtonIconDefault = 0x7f0800f1;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonIcon = 0x7f0800f2;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonIconDefault = 0x7f0800f3;
        public static int antelopSecureVirtualCardNumberDisplayDefaultCardBackground = 0x7f0800f4;
        public static int antelopSecureVirtualCardNumberDisplayDefaultCardBackgroundDefault = 0x7f0800f5;
        public static int antelop_bullet_selected = 0x7f0800f6;
        public static int antelop_bullet_selector = 0x7f0800f7;
        public static int antelop_bullet_unselected = 0x7f0800f8;
        public static int antelop_card_scan_process_error = 0x7f0800f9;
        public static int antelop_card_scan_process_success = 0x7f0800fa;
        public static int antelop_credit_card = 0x7f0800fb;
        public static int antelop_google_pay_logo = 0x7f0800fc;
        public static int antelop_ic_backbutton = 0x7f0800fd;
        public static int antelop_ic_background_round = 0x7f0800fe;
        public static int antelop_ic_camera = 0x7f0800ff;
        public static int antelop_ic_card_default = 0x7f080100;
        public static int antelop_ic_close = 0x7f080101;
        public static int antelop_ic_contactless_card_symbol = 0x7f080102;
        public static int antelop_ic_copy = 0x7f080103;
        public static int antelop_ic_cvx2 = 0x7f080104;
        public static int antelop_ic_expiry_date = 0x7f080105;
        public static int antelop_ic_fingerprint_idle = 0x7f080106;
        public static int antelop_ic_generic_card = 0x7f080107;
        public static int antelop_ic_keypad_bullet = 0x7f080108;
        public static int antelop_ic_mastercard = 0x7f080109;
        public static int antelop_ic_pinprompt_keypad_icon = 0x7f08010a;
        public static int antelop_ic_question = 0x7f08010b;
        public static int antelop_ic_success = 0x7f08010c;
        public static int antelop_ic_visa = 0x7f08010d;
        public static int antelop_keypadview_keypadbutton_background = 0x7f08010e;
        public static int antelop_pinprompt_keypadbutton_background = 0x7f08010f;
        public static int antelop_samsung_pay_logo = 0x7f080110;
        public static int antelop_securepininput_keypadbutton_background = 0x7f080111;
        public static int avd_hide_password = 0x7f080112;
        public static int avd_show_password = 0x7f080113;
        public static int btn_checkbox_checked_mtrl = 0x7f080114;
        public static int btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080115;
        public static int btn_checkbox_unchecked_mtrl = 0x7f080116;
        public static int btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080117;
        public static int btn_radio_off_mtrl = 0x7f080118;
        public static int btn_radio_off_to_on_mtrl_animation = 0x7f080119;
        public static int btn_radio_on_mtrl = 0x7f08011a;
        public static int btn_radio_on_to_off_mtrl_animation = 0x7f08011b;
        public static int common_full_open_on_phone = 0x7f08011c;
        public static int common_google_signin_btn_icon_dark = 0x7f08011d;
        public static int common_google_signin_btn_icon_dark_focused = 0x7f08011e;
        public static int common_google_signin_btn_icon_dark_normal = 0x7f08011f;
        public static int common_google_signin_btn_icon_dark_normal_background = 0x7f080120;
        public static int common_google_signin_btn_icon_disabled = 0x7f080121;
        public static int common_google_signin_btn_icon_light = 0x7f080122;
        public static int common_google_signin_btn_icon_light_focused = 0x7f080123;
        public static int common_google_signin_btn_icon_light_normal = 0x7f080124;
        public static int common_google_signin_btn_icon_light_normal_background = 0x7f080125;
        public static int common_google_signin_btn_text_dark = 0x7f080126;
        public static int common_google_signin_btn_text_dark_focused = 0x7f080127;
        public static int common_google_signin_btn_text_dark_normal = 0x7f080128;
        public static int common_google_signin_btn_text_dark_normal_background = 0x7f080129;
        public static int common_google_signin_btn_text_disabled = 0x7f08012a;
        public static int common_google_signin_btn_text_light = 0x7f08012b;
        public static int common_google_signin_btn_text_light_focused = 0x7f08012c;
        public static int common_google_signin_btn_text_light_normal = 0x7f08012d;
        public static int common_google_signin_btn_text_light_normal_background = 0x7f08012e;
        public static int design_fab_background = 0x7f080132;
        public static int design_ic_visibility = 0x7f080133;
        public static int design_ic_visibility_off = 0x7f080134;
        public static int design_password_eye = 0x7f080135;
        public static int design_snackbar_background = 0x7f080136;
        public static int googleg_disabled_color_18 = 0x7f080139;
        public static int googleg_standard_color_18 = 0x7f08013a;
        public static int ic_document_provider_logo = 0x7f080143;
        public static int ic_mtrl_checked_circle = 0x7f08014b;
        public static int ic_mtrl_chip_checked_black = 0x7f08014c;
        public static int ic_mtrl_chip_checked_circle = 0x7f08014d;
        public static int ic_mtrl_chip_close_circle = 0x7f08014e;
        public static int mtrl_dialog_background = 0x7f080153;
        public static int mtrl_dropdown_arrow = 0x7f080154;
        public static int mtrl_ic_arrow_drop_down = 0x7f080155;
        public static int mtrl_ic_arrow_drop_up = 0x7f080156;
        public static int mtrl_ic_cancel = 0x7f080157;
        public static int mtrl_ic_error = 0x7f080158;
        public static int mtrl_popupmenu_background = 0x7f080159;
        public static int mtrl_tabs_default_indicator = 0x7f08015b;
        public static int navigation_empty_icon = 0x7f08015c;
        public static int notification_action_background = 0x7f08015d;
        public static int notification_bg = 0x7f08015e;
        public static int notification_bg_low = 0x7f08015f;
        public static int notification_bg_low_normal = 0x7f080160;
        public static int notification_bg_low_pressed = 0x7f080161;
        public static int notification_bg_normal = 0x7f080162;
        public static int notification_bg_normal_pressed = 0x7f080163;
        public static int notification_icon_background = 0x7f080164;
        public static int notification_template_icon_bg = 0x7f080166;
        public static int notification_template_icon_low_bg = 0x7f080167;
        public static int notification_tile_bg = 0x7f080168;
        public static int notify_panel_notification_icon_bg = 0x7f080169;
        public static int test_level_drawable = 0x7f08016c;
        public static int tooltip_frame_dark = 0x7f08016d;
        public static int tooltip_frame_light = 0x7f08016e;
        public static int tp_issuer_progress = 0x7f08016f;
        public static int tp_issuer_progress_animated = 0x7f080170;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$fraction.smali */
    public static final class fraction {
        public static int antelopCardDisplayCopyPanButtonStartPercent = 0x7f090000;
        public static int antelopCardDisplayCopyPanButtonTopPercent = 0x7f090001;
        public static int antelopCardDisplayCvx2EndPercent = 0x7f090002;
        public static int antelopCardDisplayCvx2StartPercent = 0x7f090003;
        public static int antelopCardDisplayCvx2TopPercent = 0x7f090004;
        public static int antelopCardDisplayExpiryDateEndPercent = 0x7f090005;
        public static int antelopCardDisplayExpiryDateStartPercent = 0x7f090006;
        public static int antelopCardDisplayExpiryDateTopPercent = 0x7f090007;
        public static int antelopCardDisplayPanEndPercent = 0x7f090008;
        public static int antelopCardDisplayPanStartPercent = 0x7f090009;
        public static int antelopCardDisplayPanTopPercent = 0x7f09000a;
        public static int antelopPinPromptLayoutDescriptionBottomPercent = 0x7f09000b;
        public static int antelopPinPromptLayoutSubtitleBottomPercent = 0x7f09000c;
        public static int antelopPinPromptLayoutTitleTopPercent = 0x7f09000d;
        public static int antelopSecureCardDisplayCopyPanButtonStartPercent = 0x7f09000e;
        public static int antelopSecureCardDisplayCopyPanButtonTopPercent = 0x7f09000f;
        public static int antelopSecureCardDisplayCvx2EndPercent = 0x7f090010;
        public static int antelopSecureCardDisplayCvx2StartPercent = 0x7f090011;
        public static int antelopSecureCardDisplayCvx2TopPercent = 0x7f090012;
        public static int antelopSecureCardDisplayExpiryDateEndPercent = 0x7f090013;
        public static int antelopSecureCardDisplayExpiryDateStartPercent = 0x7f090014;
        public static int antelopSecureCardDisplayExpiryDateTopPercent = 0x7f090015;
        public static int antelopSecureCardDisplayPanEndPercent = 0x7f090016;
        public static int antelopSecureCardDisplayPanStartPercent = 0x7f090017;
        public static int antelopSecureCardDisplayPanTopPercent = 0x7f090018;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonStartPercent = 0x7f090019;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonTopPercent = 0x7f09001a;
        public static int antelopSecureVirtualCardNumberDisplayCvx2EndPercent = 0x7f09001b;
        public static int antelopSecureVirtualCardNumberDisplayCvx2StartPercent = 0x7f09001c;
        public static int antelopSecureVirtualCardNumberDisplayCvx2TopPercent = 0x7f09001d;
        public static int antelopSecureVirtualCardNumberDisplayExpiryDateEndPercent = 0x7f09001e;
        public static int antelopSecureVirtualCardNumberDisplayExpiryDateStartPercent = 0x7f09001f;
        public static int antelopSecureVirtualCardNumberDisplayExpiryDateTopPercent = 0x7f090020;
        public static int antelopSecureVirtualCardNumberDisplayPanEndPercent = 0x7f090021;
        public static int antelopSecureVirtualCardNumberDisplayPanStartPercent = 0x7f090022;
        public static int antelopSecureVirtualCardNumberDisplayPanTopPercent = 0x7f090023;

        private fraction() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$id.smali */
    public static final class id {
        public static int BOTTOM_END = 0x7f0a0001;
        public static int BOTTOM_START = 0x7f0a0002;
        public static int RelativeLayout1 = 0x7f0a0007;
        public static int TOP_END = 0x7f0a000b;
        public static int TOP_START = 0x7f0a000c;
        public static int accessibility_action_clickable_span = 0x7f0a000d;
        public static int accessibility_custom_action_0 = 0x7f0a000e;
        public static int accessibility_custom_action_1 = 0x7f0a000f;
        public static int accessibility_custom_action_10 = 0x7f0a0010;
        public static int accessibility_custom_action_11 = 0x7f0a0011;
        public static int accessibility_custom_action_12 = 0x7f0a0012;
        public static int accessibility_custom_action_13 = 0x7f0a0013;
        public static int accessibility_custom_action_14 = 0x7f0a0014;
        public static int accessibility_custom_action_15 = 0x7f0a0015;
        public static int accessibility_custom_action_16 = 0x7f0a0016;
        public static int accessibility_custom_action_17 = 0x7f0a0017;
        public static int accessibility_custom_action_18 = 0x7f0a0018;
        public static int accessibility_custom_action_19 = 0x7f0a0019;
        public static int accessibility_custom_action_2 = 0x7f0a001a;
        public static int accessibility_custom_action_20 = 0x7f0a001b;
        public static int accessibility_custom_action_21 = 0x7f0a001c;
        public static int accessibility_custom_action_22 = 0x7f0a001d;
        public static int accessibility_custom_action_23 = 0x7f0a001e;
        public static int accessibility_custom_action_24 = 0x7f0a001f;
        public static int accessibility_custom_action_25 = 0x7f0a0020;
        public static int accessibility_custom_action_26 = 0x7f0a0021;
        public static int accessibility_custom_action_27 = 0x7f0a0022;
        public static int accessibility_custom_action_28 = 0x7f0a0023;
        public static int accessibility_custom_action_29 = 0x7f0a0024;
        public static int accessibility_custom_action_3 = 0x7f0a0025;
        public static int accessibility_custom_action_30 = 0x7f0a0026;
        public static int accessibility_custom_action_31 = 0x7f0a0027;
        public static int accessibility_custom_action_4 = 0x7f0a0028;
        public static int accessibility_custom_action_5 = 0x7f0a0029;
        public static int accessibility_custom_action_6 = 0x7f0a002a;
        public static int accessibility_custom_action_7 = 0x7f0a002b;
        public static int accessibility_custom_action_8 = 0x7f0a002c;
        public static int accessibility_custom_action_9 = 0x7f0a002d;
        public static int action_bar = 0x7f0a0031;
        public static int action_bar_activity_content = 0x7f0a0032;
        public static int action_bar_container = 0x7f0a0033;
        public static int action_bar_root = 0x7f0a0034;
        public static int action_bar_spinner = 0x7f0a0035;
        public static int action_bar_subtitle = 0x7f0a0036;
        public static int action_bar_title = 0x7f0a0037;
        public static int action_container = 0x7f0a0038;
        public static int action_context_bar = 0x7f0a0039;
        public static int action_divider = 0x7f0a003a;
        public static int action_image = 0x7f0a003b;
        public static int action_menu_divider = 0x7f0a003c;
        public static int action_menu_presenter = 0x7f0a003d;
        public static int action_mode_bar = 0x7f0a003e;
        public static int action_mode_bar_stub = 0x7f0a003f;
        public static int action_mode_close_button = 0x7f0a0040;
        public static int action_text = 0x7f0a0041;
        public static int actions = 0x7f0a0042;
        public static int activity_chooser_view_content = 0x7f0a0043;
        public static int add = 0x7f0a0044;
        public static int adjust_height = 0x7f0a0045;
        public static int adjust_width = 0x7f0a0046;
        public static int alertTitle = 0x7f0a0047;
        public static int antelop_consent_prompt_cancel = 0x7f0a004a;
        public static int antelop_consent_prompt_submit = 0x7f0a004b;
        public static int antelop_consent_prompt_subtitle = 0x7f0a004c;
        public static int antelop_consent_prompt_title = 0x7f0a004d;
        public static int antelop_pin_prompt_cancel = 0x7f0a004e;
        public static int antelop_pin_prompt_description = 0x7f0a004f;
        public static int antelop_pin_prompt_guide_description = 0x7f0a0050;
        public static int antelop_pin_prompt_guide_subtitle = 0x7f0a0051;
        public static int antelop_pin_prompt_guide_title = 0x7f0a0052;
        public static int antelop_pin_prompt_keypad = 0x7f0a0053;
        public static int antelop_pin_prompt_root = 0x7f0a0054;
        public static int antelop_pin_prompt_subtitle = 0x7f0a0055;
        public static int antelop_pin_prompt_title = 0x7f0a0056;
        public static int antelop_screenunlock_prompt_button_submit = 0x7f0a0057;
        public static int antelop_screenunlock_prompt_cancel = 0x7f0a0058;
        public static int antelop_screenunlock_prompt_subtitle = 0x7f0a0059;
        public static int antelop_screenunlock_prompt_title = 0x7f0a005a;
        public static int async = 0x7f0a005b;
        public static int auto = 0x7f0a005c;
        public static int blocking = 0x7f0a005f;
        public static int bottom = 0x7f0a0060;
        public static int buttonPanel = 0x7f0a0061;
        public static int cancel = 0x7f0a0062;
        public static int cancel_button = 0x7f0a0064;
        public static int center = 0x7f0a0065;
        public static int centerCrop = 0x7f0a0066;
        public static int checkbox = 0x7f0a006a;
        public static int checked = 0x7f0a006b;
        public static int chronometer = 0x7f0a006e;
        public static int clear_text = 0x7f0a006f;
        public static int confirm = 0x7f0a0073;
        public static int confirm_button = 0x7f0a0074;
        public static int container = 0x7f0a0075;
        public static int content = 0x7f0a0076;
        public static int contentPanel = 0x7f0a0077;
        public static int coordinator = 0x7f0a0078;
        public static int custom = 0x7f0a0079;
        public static int customPanel = 0x7f0a007a;
        public static int cut = 0x7f0a007b;
        public static int dark = 0x7f0a007c;
        public static int date_picker_actions = 0x7f0a007d;
        public static int decor_content_parent = 0x7f0a007e;
        public static int default_activity_button = 0x7f0a007f;
        public static int delete_button = 0x7f0a0080;
        public static int design_bottom_sheet = 0x7f0a0081;
        public static int design_menu_item_action_area = 0x7f0a0082;
        public static int design_menu_item_action_area_stub = 0x7f0a0083;
        public static int design_menu_item_text = 0x7f0a0084;
        public static int design_navigation_view = 0x7f0a0085;
        public static int device_wallet_logo = 0x7f0a0086;
        public static int dialog_button = 0x7f0a0087;
        public static int dropdown_menu = 0x7f0a008b;
        public static int edit_query = 0x7f0a008c;
        public static int end = 0x7f0a008e;
        public static int expand_activities_button = 0x7f0a0093;
        public static int expanded_menu = 0x7f0a0094;
        public static int fade = 0x7f0a0095;
        public static int fill = 0x7f0a0096;
        public static int filled = 0x7f0a0099;
        public static int fingerprint_description = 0x7f0a009b;
        public static int fingerprint_error = 0x7f0a009c;
        public static int fingerprint_icon = 0x7f0a009d;
        public static int fingerprint_subtitle = 0x7f0a009e;
        public static int fitCenter = 0x7f0a009f;
        public static int fitXY = 0x7f0a00a1;
        public static int fixed = 0x7f0a00a2;
        public static int forever = 0x7f0a00a3;
        public static int fragment_container = 0x7f0a00a4;
        public static int fragment_container_view_tag = 0x7f0a00a5;
        public static int ghost_view = 0x7f0a00a6;
        public static int ghost_view_holder = 0x7f0a00a7;
        public static int gone = 0x7f0a00a8;
        public static int group_divider = 0x7f0a00a9;
        public static int guideline_horizontal = 0x7f0a00ab;
        public static int home = 0x7f0a00ae;
        public static int horizontal_guideline = 0x7f0a00b0;
        public static int icon = 0x7f0a00b1;
        public static int icon_group = 0x7f0a00b2;
        public static int icon_only = 0x7f0a00b3;
        public static int image = 0x7f0a00b5;
        public static int info = 0x7f0a00b6;
        public static int invisible = 0x7f0a00b7;
        public static int italic = 0x7f0a00b8;
        public static int item_touch_helper_previous_elevation = 0x7f0a00b9;
        public static int labeled = 0x7f0a00ba;
        public static int lastdigits_textview = 0x7f0a00bc;
        public static int left = 0x7f0a00bd;
        public static int light = 0x7f0a00be;
        public static int line1 = 0x7f0a00bf;
        public static int line3 = 0x7f0a00c0;
        public static int listMode = 0x7f0a00c1;
        public static int list_item = 0x7f0a00c2;
        public static int masked = 0x7f0a00c3;
        public static int message = 0x7f0a00c5;
        public static int middle = 0x7f0a00c6;
        public static int mini = 0x7f0a00c7;
        public static int month_grid = 0x7f0a00c8;
        public static int month_navigation_bar = 0x7f0a00c9;
        public static int month_navigation_fragment_toggle = 0x7f0a00ca;
        public static int month_navigation_next = 0x7f0a00cb;
        public static int month_navigation_previous = 0x7f0a00cc;
        public static int month_title = 0x7f0a00cd;
        public static int mtrl_calendar_day_selector_frame = 0x7f0a00ce;
        public static int mtrl_calendar_days_of_week = 0x7f0a00cf;
        public static int mtrl_calendar_frame = 0x7f0a00d0;
        public static int mtrl_calendar_main_pane = 0x7f0a00d1;
        public static int mtrl_calendar_months = 0x7f0a00d2;
        public static int mtrl_calendar_selection_frame = 0x7f0a00d3;
        public static int mtrl_calendar_text_input_frame = 0x7f0a00d4;
        public static int mtrl_calendar_year_selector_frame = 0x7f0a00d5;
        public static int mtrl_card_checked_layer_id = 0x7f0a00d6;
        public static int mtrl_child_content_container = 0x7f0a00d7;
        public static int mtrl_internal_children_alpha_tag = 0x7f0a00d8;
        public static int mtrl_picker_fullscreen = 0x7f0a00d9;
        public static int mtrl_picker_header = 0x7f0a00da;
        public static int mtrl_picker_header_selection_text = 0x7f0a00db;
        public static int mtrl_picker_header_title_and_selection = 0x7f0a00dc;
        public static int mtrl_picker_header_toggle = 0x7f0a00dd;
        public static int mtrl_picker_text_input_date = 0x7f0a00de;
        public static int mtrl_picker_text_input_range_end = 0x7f0a00df;
        public static int mtrl_picker_text_input_range_start = 0x7f0a00e0;
        public static int mtrl_picker_title_text = 0x7f0a00e1;
        public static int multiply = 0x7f0a00e2;
        public static int navigation_header_container = 0x7f0a00e5;
        public static int no_card_message = 0x7f0a00e8;
        public static int none = 0x7f0a00e9;
        public static int normal = 0x7f0a00ea;
        public static int notification_background = 0x7f0a00eb;
        public static int notification_main_column = 0x7f0a00ec;
        public static int notification_main_column_container = 0x7f0a00ed;
        public static int off = 0x7f0a00ee;
        public static int on = 0x7f0a00ef;
        public static int outline = 0x7f0a00f0;
        public static int packed = 0x7f0a00f1;
        public static int pager = 0x7f0a00f2;
        public static int parallax = 0x7f0a00f3;
        public static int parent = 0x7f0a00f4;
        public static int parentPanel = 0x7f0a00f5;
        public static int parent_matrix = 0x7f0a00f6;
        public static int password_toggle = 0x7f0a00f7;
        public static int percent = 0x7f0a00f9;
        public static int pin = 0x7f0a00fa;
        public static int progress_circular = 0x7f0a00fb;
        public static int progress_horizontal = 0x7f0a00fc;
        public static int radio = 0x7f0a00fd;
        public static int report_drawn = 0x7f0a00fe;
        public static int right = 0x7f0a00ff;
        public static int right_icon = 0x7f0a0100;
        public static int right_side = 0x7f0a0101;
        public static int rounded = 0x7f0a0102;
        public static int save_non_transition_alpha = 0x7f0a0103;
        public static int save_overlay_view = 0x7f0a0104;
        public static int scale = 0x7f0a0105;
        public static int screen = 0x7f0a0106;
        public static int scrollIndicatorDown = 0x7f0a0108;
        public static int scrollIndicatorUp = 0x7f0a0109;
        public static int scrollView = 0x7f0a010a;
        public static int scrollable = 0x7f0a010b;
        public static int search_badge = 0x7f0a010c;
        public static int search_bar = 0x7f0a010d;
        public static int search_button = 0x7f0a010e;
        public static int search_close_btn = 0x7f0a010f;
        public static int search_edit_frame = 0x7f0a0110;
        public static int search_go_btn = 0x7f0a0111;
        public static int search_mag_icon = 0x7f0a0112;
        public static int search_plate = 0x7f0a0113;
        public static int search_src_text = 0x7f0a0114;
        public static int search_voice_btn = 0x7f0a0115;
        public static int select_dialog_listview = 0x7f0a0116;
        public static int selected = 0x7f0a0117;
        public static int set_as_default_cancel = 0x7f0a0118;
        public static int set_as_default_confirm = 0x7f0a0119;
        public static int set_as_default_logo = 0x7f0a011a;
        public static int set_as_default_title = 0x7f0a011b;
        public static int shortcut = 0x7f0a011c;
        public static int slide = 0x7f0a0121;
        public static int snackbar_action = 0x7f0a0123;
        public static int snackbar_text = 0x7f0a0124;
        public static int spacer = 0x7f0a0127;
        public static int special_effects_controller_view_tag = 0x7f0a0128;
        public static int split_action_bar = 0x7f0a012a;
        public static int spread = 0x7f0a012b;
        public static int spread_inside = 0x7f0a012c;
        public static int src_atop = 0x7f0a012d;
        public static int src_in = 0x7f0a012e;
        public static int src_over = 0x7f0a012f;
        public static int standard = 0x7f0a0130;
        public static int start = 0x7f0a0131;
        public static int stretch = 0x7f0a0133;
        public static int submenuarrow = 0x7f0a0134;
        public static int submit_area = 0x7f0a0135;
        public static int tabMode = 0x7f0a0136;
        public static int tab_layout = 0x7f0a0137;
        public static int tag_accessibility_actions = 0x7f0a0138;
        public static int tag_accessibility_clickable_spans = 0x7f0a0139;
        public static int tag_accessibility_heading = 0x7f0a013a;
        public static int tag_accessibility_pane_title = 0x7f0a013b;
        public static int tag_on_apply_window_listener = 0x7f0a013c;
        public static int tag_on_receive_content_listener = 0x7f0a013d;
        public static int tag_on_receive_content_mime_types = 0x7f0a013e;
        public static int tag_screen_reader_focusable = 0x7f0a013f;
        public static int tag_state_description = 0x7f0a0140;
        public static int tag_transition_group = 0x7f0a0141;
        public static int tag_unhandled_key_event_manager = 0x7f0a0142;
        public static int tag_unhandled_key_listeners = 0x7f0a0143;
        public static int tag_window_insets_animation_callback = 0x7f0a0144;
        public static int text = 0x7f0a0147;
        public static int text2 = 0x7f0a0148;
        public static int textSpacerNoButtons = 0x7f0a014a;
        public static int textSpacerNoTitle = 0x7f0a014b;
        public static int text_input_end_icon = 0x7f0a014e;
        public static int text_input_start_icon = 0x7f0a014f;
        public static int textinput_counter = 0x7f0a0150;
        public static int textinput_error = 0x7f0a0151;
        public static int textinput_helper_text = 0x7f0a0152;
        public static int time = 0x7f0a0153;
        public static int title = 0x7f0a0154;
        public static int titleDividerNoCustom = 0x7f0a0155;
        public static int title_template = 0x7f0a0156;
        public static int toolbar = 0x7f0a0157;
        public static int top = 0x7f0a0158;
        public static int topPanel = 0x7f0a0159;
        public static int touch_outside = 0x7f0a015a;
        public static int tp_progress = 0x7f0a015b;
        public static int tp_progress_container = 0x7f0a015c;
        public static int transition_current_scene = 0x7f0a015d;
        public static int transition_layout_save = 0x7f0a015e;
        public static int transition_position = 0x7f0a015f;
        public static int transition_scene_layoutid_cache = 0x7f0a0160;
        public static int transition_transform = 0x7f0a0161;
        public static int unchecked = 0x7f0a0162;
        public static int uniform = 0x7f0a0163;
        public static int unlabeled = 0x7f0a0164;
        public static int up = 0x7f0a0165;
        public static int vertical_guideline = 0x7f0a0167;
        public static int view_offset_helper = 0x7f0a0168;
        public static int view_tree_lifecycle_owner = 0x7f0a0169;
        public static int view_tree_on_back_pressed_dispatcher_owner = 0x7f0a016a;
        public static int view_tree_saved_state_registry_owner = 0x7f0a016b;
        public static int view_tree_view_model_store_owner = 0x7f0a016c;
        public static int visible = 0x7f0a016d;
        public static int visible_removing_fragment_view_tag = 0x7f0a016e;
        public static int wide = 0x7f0a0170;
        public static int wipe_data_button = 0x7f0a0171;
        public static int wrap = 0x7f0a0173;
        public static int wrap_content = 0x7f0a0174;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$integer.smali */
    public static final class integer {
        public static int abc_config_activityDefaultDur = 0x7f0b0000;
        public static int abc_config_activityShortDur = 0x7f0b0001;
        public static int antelopKeypadViewDefaultPinLength = 0x7f0b0002;
        public static int antelopPinPromptPinSize = 0x7f0b0003;
        public static int antelopSecurePinInputPinSize = 0x7f0b0004;
        public static int app_bar_elevation_anim_duration = 0x7f0b0005;
        public static int bottom_sheet_slide_duration = 0x7f0b0006;
        public static int cancel_button_image_alpha = 0x7f0b0007;
        public static int config_tooltipAnimTime = 0x7f0b0008;
        public static int design_snackbar_text_max_lines = 0x7f0b000a;
        public static int design_tab_indicator_anim_duration_ms = 0x7f0b000b;
        public static int google_play_services_version = 0x7f0b000c;
        public static int hide_password_duration = 0x7f0b000d;
        public static int mtrl_badge_max_character_count = 0x7f0b000e;
        public static int mtrl_btn_anim_delay_ms = 0x7f0b000f;
        public static int mtrl_btn_anim_duration_ms = 0x7f0b0010;
        public static int mtrl_calendar_header_orientation = 0x7f0b0011;
        public static int mtrl_calendar_selection_text_lines = 0x7f0b0012;
        public static int mtrl_calendar_year_selector_span = 0x7f0b0013;
        public static int mtrl_card_anim_delay_ms = 0x7f0b0014;
        public static int mtrl_card_anim_duration_ms = 0x7f0b0015;
        public static int mtrl_chip_anim_duration = 0x7f0b0016;
        public static int mtrl_tab_indicator_anim_duration_ms = 0x7f0b0017;
        public static int show_password_duration = 0x7f0b0018;
        public static int status_bar_notification_info_maxnum = 0x7f0b0019;

        private integer() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$interpolator.smali */
    public static final class interpolator {
        public static int btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000;
        public static int btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002;
        public static int btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003;
        public static int btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004;
        public static int btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005;
        public static int fast_out_slow_in = 0x7f0c0006;
        public static int mtrl_fast_out_linear_in = 0x7f0c0007;
        public static int mtrl_fast_out_slow_in = 0x7f0c0008;
        public static int mtrl_linear = 0x7f0c0009;
        public static int mtrl_linear_out_slow_in = 0x7f0c000a;

        private interpolator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$layout.smali */
    public static final class layout {
        public static int abc_action_bar_title_item = 0x7f0d0000;
        public static int abc_action_bar_up_container = 0x7f0d0001;
        public static int abc_action_menu_item_layout = 0x7f0d0002;
        public static int abc_action_menu_layout = 0x7f0d0003;
        public static int abc_action_mode_bar = 0x7f0d0004;
        public static int abc_action_mode_close_item_material = 0x7f0d0005;
        public static int abc_activity_chooser_view = 0x7f0d0006;
        public static int abc_activity_chooser_view_list_item = 0x7f0d0007;
        public static int abc_alert_dialog_button_bar_material = 0x7f0d0008;
        public static int abc_alert_dialog_material = 0x7f0d0009;
        public static int abc_alert_dialog_title_material = 0x7f0d000a;
        public static int abc_cascading_menu_item_layout = 0x7f0d000b;
        public static int abc_dialog_title_material = 0x7f0d000c;
        public static int abc_expanded_menu_layout = 0x7f0d000d;
        public static int abc_list_menu_item_checkbox = 0x7f0d000e;
        public static int abc_list_menu_item_icon = 0x7f0d000f;
        public static int abc_list_menu_item_layout = 0x7f0d0010;
        public static int abc_list_menu_item_radio = 0x7f0d0011;
        public static int abc_popup_menu_header_item_layout = 0x7f0d0012;
        public static int abc_popup_menu_item_layout = 0x7f0d0013;
        public static int abc_screen_content_include = 0x7f0d0014;
        public static int abc_screen_simple = 0x7f0d0015;
        public static int abc_screen_simple_overlay_action_mode = 0x7f0d0016;
        public static int abc_screen_toolbar = 0x7f0d0017;
        public static int abc_search_dropdown_item_icons_2line = 0x7f0d0018;
        public static int abc_search_view = 0x7f0d0019;
        public static int abc_select_dialog_material = 0x7f0d001a;
        public static int abc_tooltip = 0x7f0d001b;
        public static int add_google_pay_mock_fragment_layout = 0x7f0d001e;
        public static int antelop_activity_authentication_method_prompt = 0x7f0d001f;
        public static int antelop_consent_prompt_fragment = 0x7f0d0020;
        public static int antelop_keypadview_digitbutton = 0x7f0d0021;
        public static int antelop_keypadview_imagebutton = 0x7f0d0022;
        public static int antelop_pin_prompt_fragment = 0x7f0d0023;
        public static int antelop_screenunlock_prompt_fragment = 0x7f0d0024;
        public static int custom_dialog = 0x7f0d0026;
        public static int design_bottom_navigation_item = 0x7f0d0027;
        public static int design_bottom_sheet_dialog = 0x7f0d0028;
        public static int design_layout_snackbar = 0x7f0d0029;
        public static int design_layout_snackbar_include = 0x7f0d002a;
        public static int design_layout_tab_icon = 0x7f0d002b;
        public static int design_layout_tab_text = 0x7f0d002c;
        public static int design_menu_item_action_area = 0x7f0d002d;
        public static int design_navigation_item = 0x7f0d002e;
        public static int design_navigation_item_header = 0x7f0d002f;
        public static int design_navigation_item_separator = 0x7f0d0030;
        public static int design_navigation_item_subheader = 0x7f0d0031;
        public static int design_navigation_menu = 0x7f0d0032;
        public static int design_navigation_menu_item = 0x7f0d0033;
        public static int design_text_input_end_icon = 0x7f0d0034;
        public static int design_text_input_start_icon = 0x7f0d0035;
        public static int device_wallet_mock_activity_layout = 0x7f0d0037;
        public static int fingerprint_dialog_layout = 0x7f0d0038;
        public static int fragment_set_as_default = 0x7f0d003a;
        public static int manage_device_wallet_mock_fragment_layout = 0x7f0d003d;
        public static int mtrl_alert_dialog = 0x7f0d003e;
        public static int mtrl_alert_dialog_actions = 0x7f0d003f;
        public static int mtrl_alert_dialog_title = 0x7f0d0040;
        public static int mtrl_alert_select_dialog_item = 0x7f0d0041;
        public static int mtrl_alert_select_dialog_multichoice = 0x7f0d0042;
        public static int mtrl_alert_select_dialog_singlechoice = 0x7f0d0043;
        public static int mtrl_calendar_day = 0x7f0d0044;
        public static int mtrl_calendar_day_of_week = 0x7f0d0045;
        public static int mtrl_calendar_days_of_week = 0x7f0d0046;
        public static int mtrl_calendar_horizontal = 0x7f0d0047;
        public static int mtrl_calendar_month = 0x7f0d0048;
        public static int mtrl_calendar_month_labeled = 0x7f0d0049;
        public static int mtrl_calendar_month_navigation = 0x7f0d004a;
        public static int mtrl_calendar_months = 0x7f0d004b;
        public static int mtrl_calendar_vertical = 0x7f0d004c;
        public static int mtrl_calendar_year = 0x7f0d004d;
        public static int mtrl_layout_snackbar = 0x7f0d004e;
        public static int mtrl_layout_snackbar_include = 0x7f0d004f;
        public static int mtrl_picker_actions = 0x7f0d0050;
        public static int mtrl_picker_dialog = 0x7f0d0051;
        public static int mtrl_picker_fullscreen = 0x7f0d0052;
        public static int mtrl_picker_header_dialog = 0x7f0d0053;
        public static int mtrl_picker_header_fullscreen = 0x7f0d0054;
        public static int mtrl_picker_header_selection_text = 0x7f0d0055;
        public static int mtrl_picker_header_title_text = 0x7f0d0056;
        public static int mtrl_picker_header_toggle = 0x7f0d0057;
        public static int mtrl_picker_text_input_date = 0x7f0d0058;
        public static int mtrl_picker_text_input_date_range = 0x7f0d0059;
        public static int notification_action = 0x7f0d005b;
        public static int notification_action_tombstone = 0x7f0d005c;
        public static int notification_template_custom_big = 0x7f0d0063;
        public static int notification_template_icon_group = 0x7f0d0064;
        public static int notification_template_part_chronometer = 0x7f0d0068;
        public static int notification_template_part_time = 0x7f0d0069;
        public static int select_dialog_item_material = 0x7f0d006a;
        public static int select_dialog_multichoice_material = 0x7f0d006b;
        public static int select_dialog_singlechoice_material = 0x7f0d006c;
        public static int support_simple_spinner_dropdown_item = 0x7f0d006e;
        public static int token_mock_view_fragment = 0x7f0d007b;
        public static int tokenization_fragment = 0x7f0d007c;

        private layout() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$plurals.smali */
    public static final class plurals {
        public static int mtrl_badge_content_description = 0x7f100000;

        private plurals() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$raw.smali */
    public static final class raw {
        public static int firebase_common_keep = 0x7f110000;

        private raw() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$string.smali */
    public static final class string {
        public static int abc_action_bar_home_description = 0x7f120000;
        public static int abc_action_bar_up_description = 0x7f120001;
        public static int abc_action_menu_overflow_description = 0x7f120002;
        public static int abc_action_mode_done = 0x7f120003;
        public static int abc_activity_chooser_view_see_all = 0x7f120004;
        public static int abc_activitychooserview_choose_application = 0x7f120005;
        public static int abc_capital_off = 0x7f120006;
        public static int abc_capital_on = 0x7f120007;
        public static int abc_menu_alt_shortcut_label = 0x7f120008;
        public static int abc_menu_ctrl_shortcut_label = 0x7f120009;
        public static int abc_menu_delete_shortcut_label = 0x7f12000a;
        public static int abc_menu_enter_shortcut_label = 0x7f12000b;
        public static int abc_menu_function_shortcut_label = 0x7f12000c;
        public static int abc_menu_meta_shortcut_label = 0x7f12000d;
        public static int abc_menu_shift_shortcut_label = 0x7f12000e;
        public static int abc_menu_space_shortcut_label = 0x7f12000f;
        public static int abc_menu_sym_shortcut_label = 0x7f120010;
        public static int abc_prepend_shortcut_label = 0x7f120011;
        public static int abc_search_hint = 0x7f120012;
        public static int abc_searchview_description_clear = 0x7f120013;
        public static int abc_searchview_description_query = 0x7f120014;
        public static int abc_searchview_description_search = 0x7f120015;
        public static int abc_searchview_description_submit = 0x7f120016;
        public static int abc_searchview_description_voice = 0x7f120017;
        public static int abc_shareactionprovider_share_with = 0x7f120018;
        public static int abc_shareactionprovider_share_with_application = 0x7f120019;
        public static int abc_toolbar_collapse_description = 0x7f12001a;
        public static int androidx_startup = 0x7f12005e;
        public static int antelopAddCardToGooglePayTextTitle = 0x7f12005f;
        public static int antelopAddGooglePayTextButton = 0x7f120060;
        public static int antelopAlertDialogDismissTextButton = 0x7f120061;
        public static int antelopAlertDialogWipeDataNegativeButtonText = 0x7f120062;
        public static int antelopAlertDialogWipeDataPositiveButtonText = 0x7f120063;
        public static int antelopCancelGooglePayTextButton = 0x7f120064;
        public static int antelopCardDisplayCloseDescription = 0x7f120065;
        public static int antelopCardDisplayCopyPanButtonConfirmationLabel = 0x7f120066;
        public static int antelopCardDisplayCopyPanButtonDescription = 0x7f120067;
        public static int antelopCardDisplayScreenLabel = 0x7f120068;
        public static int antelopCardPromptActivity_ActivityLabel = 0x7f120069;
        public static int antelopCardPromptActivity_CameraScanDescription = 0x7f12006a;
        public static int antelopCardPromptActivity_CameraScanInstruction = 0x7f12006b;
        public static int antelopCardPromptActivity_CardholderNameErrorMessage = 0x7f12006c;
        public static int antelopCardPromptActivity_CardholderNameHint = 0x7f12006d;
        public static int antelopCardPromptActivity_ConfirmButtonText = 0x7f12006e;
        public static int antelopCardPromptActivity_Cvx2ErrorMessage = 0x7f12006f;
        public static int antelopCardPromptActivity_Cvx2Hint = 0x7f120070;
        public static int antelopCardPromptActivity_Cvx2InstructionsDescription = 0x7f120071;
        public static int antelopCardPromptActivity_Cvx2InstructionsTextButton = 0x7f120072;
        public static int antelopCardPromptActivity_Cvx2InstructionsTextMessage = 0x7f120073;
        public static int antelopCardPromptActivity_ErrorDialogButton = 0x7f120074;
        public static int antelopCardPromptActivity_ExpiryDateErrorMessage = 0x7f120075;
        public static int antelopCardPromptActivity_ExpiryDateHint = 0x7f120076;
        public static int antelopCardPromptActivity_ExpiryDateInstructionsDescription = 0x7f120077;
        public static int antelopCardPromptActivity_ExpiryDateInstructionsTextButton = 0x7f120078;
        public static int antelopCardPromptActivity_ExpiryDateInstructionsTextMessage = 0x7f120079;
        public static int antelopCardPromptActivity_NfcScanErrorCardNotSupported = 0x7f12007a;
        public static int antelopCardPromptActivity_NfcScanErrorCommunication = 0x7f12007b;
        public static int antelopCardPromptActivity_NfcScanErrorUnknown = 0x7f12007c;
        public static int antelopCardPromptActivity_NfcScanInstruction = 0x7f12007d;
        public static int antelopCardPromptActivity_OverlayProtectionMessage = 0x7f12007e;
        public static int antelopCardPromptActivity_PanErrorMessage = 0x7f12007f;
        public static int antelopCardPromptActivity_PanHint = 0x7f120080;
        public static int antelopCardPromptActivity_WaitingText = 0x7f120081;
        public static int antelopConfirmTextButton = 0x7f120082;
        public static int antelopConsentPromptCancelButtonLabel = 0x7f120083;
        public static int antelopConsentPromptDefaultSubtitle = 0x7f120084;
        public static int antelopConsentPromptDefaultTitle = 0x7f120085;
        public static int antelopConsentPromptName = 0x7f120086;
        public static int antelopConsentPromptOverlayWarningMessage = 0x7f120087;
        public static int antelopConsentPromptSubmitButtonLabel = 0x7f120088;
        public static int antelopDebugToastMessage = 0x7f120089;
        public static int antelopDefaultCardTextTitle = 0x7f12008a;
        public static int antelopDeleteCardTextMessage = 0x7f12008b;
        public static int antelopDeleteCardTextTitle = 0x7f12008c;
        public static int antelopDeviceBiometricPromptCancelButtonLabel = 0x7f12008d;
        public static int antelopDeviceBiometricPromptDefaultSubtitle = 0x7f12008e;
        public static int antelopDeviceBiometricPromptDefaultTitle = 0x7f12008f;
        public static int antelopDeviceBiometricPromptName = 0x7f120090;
        public static int antelopKeypadViewDefaultOverlayWarningMessage = 0x7f120091;
        public static int antelopLastDigitsCanvasText = 0x7f120092;
        public static int antelopManageDeviceWalletTextTitle = 0x7f120093;
        public static int antelopNoCardsTextMessage = 0x7f120094;
        public static int antelopPinDisplayCloseDescription = 0x7f120095;
        public static int antelopPinDisplayScreenLabel = 0x7f120096;
        public static int antelopPinDisplayTitleLabel = 0x7f120097;
        public static int antelopPinPromptCancelButtonDescription = 0x7f120098;
        public static int antelopPinPromptDefaultPinErrorMessage = 0x7f120099;
        public static int antelopPinPromptDefaultSubtitle = 0x7f12009a;
        public static int antelopPinPromptDefaultTitle = 0x7f12009b;
        public static int antelopPinPromptName = 0x7f12009c;
        public static int antelopPinPromptOverlayWarningMessage = 0x7f12009d;
        public static int antelopSamsungPayServiceId = 0x7f12009e;
        public static int antelopScreenUnlockPromptCancelButtonDescription = 0x7f12009f;
        public static int antelopScreenUnlockPromptDefaultSubtitle = 0x7f1200a0;
        public static int antelopScreenUnlockPromptDefaultTitle = 0x7f1200a1;
        public static int antelopScreenUnlockPromptName = 0x7f1200a2;
        public static int antelopScreenUnlockPromptOverlayWarningMessage = 0x7f1200a3;
        public static int antelopScreenUnlockPromptSubmitButtonLabel = 0x7f1200a4;
        public static int antelopSecureCardDisplayCloseDescription = 0x7f1200a5;
        public static int antelopSecureCardDisplayCopyPanButtonConfirmationLabel = 0x7f1200a6;
        public static int antelopSecureCardDisplayCopyPanButtonDescription = 0x7f1200a7;
        public static int antelopSecureCardDisplayScreenLabel = 0x7f1200a8;
        public static int antelopSecurePinDisplayCloseDescription = 0x7f1200a9;
        public static int antelopSecurePinDisplayScreenLabel = 0x7f1200aa;
        public static int antelopSecurePinDisplayTitleLabel = 0x7f1200ab;
        public static int antelopSecurePinInputDefaultSubtitle = 0x7f1200ac;
        public static int antelopSecurePinInputDefaultTitle = 0x7f1200ad;
        public static int antelopSecurePinInputOverlayWarningMessage = 0x7f1200ae;
        public static int antelopSecurePinInputPinsNotMatchingErrorDescription = 0x7f1200af;
        public static int antelopSecureVirtualCardNumberDisplayCloseDescription = 0x7f1200b0;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonConfirmationLabel = 0x7f1200b1;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButtonDescription = 0x7f1200b2;
        public static int antelopSecureVirtualCardNumberDisplayScreenLabel = 0x7f1200b3;
        public static int antelopTokenMockItemContentDescription = 0x7f1200b4;
        public static int antelopWipeGooglePayDataTextButton = 0x7f1200b5;
        public static int antelopWipeGooglePayDataTextDescription = 0x7f1200b6;
        public static int antelopWipeSamsungPayDataTextButton = 0x7f1200b7;
        public static int antelopWipeSamsungPayDataTextDescription = 0x7f1200b8;
        public static int app_name = 0x7f1200b9;
        public static int appbar_scrolling_view_behavior = 0x7f1200bb;
        public static int bottom_sheet_behavior = 0x7f1200c2;
        public static int character_counter_content_description = 0x7f1200ca;
        public static int character_counter_overflowed_content_description = 0x7f1200cb;
        public static int character_counter_pattern = 0x7f1200cc;
        public static int clear_text_end_icon_content_description = 0x7f1200ce;
        public static int common_google_play_services_enable_button = 0x7f1200cf;
        public static int common_google_play_services_enable_text = 0x7f1200d0;
        public static int common_google_play_services_enable_title = 0x7f1200d1;
        public static int common_google_play_services_install_button = 0x7f1200d2;
        public static int common_google_play_services_install_text = 0x7f1200d3;
        public static int common_google_play_services_install_title = 0x7f1200d4;
        public static int common_google_play_services_notification_channel_name = 0x7f1200d5;
        public static int common_google_play_services_notification_ticker = 0x7f1200d6;
        public static int common_google_play_services_unknown_issue = 0x7f1200d7;
        public static int common_google_play_services_unsupported_text = 0x7f1200d8;
        public static int common_google_play_services_update_button = 0x7f1200d9;
        public static int common_google_play_services_update_text = 0x7f1200da;
        public static int common_google_play_services_update_title = 0x7f1200db;
        public static int common_google_play_services_updating_text = 0x7f1200dc;
        public static int common_google_play_services_wear_update_text = 0x7f1200dd;
        public static int common_open_on_phone = 0x7f1200de;
        public static int common_signin_button_text = 0x7f1200df;
        public static int common_signin_button_text_long = 0x7f1200e0;
        public static int confirm_device_credential_password = 0x7f1200e1;
        public static int default_error_msg = 0x7f1200e3;
        public static int error_icon_content_description = 0x7f1200e6;
        public static int exposed_dropdown_menu_content_description = 0x7f1200e7;
        public static int fab_transformation_scrim_behavior = 0x7f1200e8;
        public static int fab_transformation_sheet_behavior = 0x7f1200e9;
        public static int fcm_fallback_notification_channel_label = 0x7f1200ea;
        public static int fingerprint_dialog_touch_sensor = 0x7f1200eb;
        public static int fingerprint_error_hw_not_available = 0x7f1200ec;
        public static int fingerprint_error_hw_not_present = 0x7f1200ed;
        public static int fingerprint_error_lockout = 0x7f1200ee;
        public static int fingerprint_error_no_fingerprints = 0x7f1200ef;
        public static int fingerprint_error_user_canceled = 0x7f1200f0;
        public static int fingerprint_not_recognized = 0x7f1200f1;
        public static int generic_error_user_canceled = 0x7f1200f5;
        public static int hide_bottom_view_on_scroll_behavior = 0x7f1200fc;
        public static int icon_content_description = 0x7f1200fd;
        public static int mtrl_badge_numberless_content_description = 0x7f120107;
        public static int mtrl_chip_close_icon_content_description = 0x7f120108;
        public static int mtrl_exceed_max_badge_number_suffix = 0x7f120109;
        public static int mtrl_picker_a11y_next_month = 0x7f12010a;
        public static int mtrl_picker_a11y_prev_month = 0x7f12010b;
        public static int mtrl_picker_announce_current_selection = 0x7f12010c;
        public static int mtrl_picker_cancel = 0x7f12010d;
        public static int mtrl_picker_confirm = 0x7f12010e;
        public static int mtrl_picker_date_header_selected = 0x7f12010f;
        public static int mtrl_picker_date_header_title = 0x7f120110;
        public static int mtrl_picker_date_header_unselected = 0x7f120111;
        public static int mtrl_picker_day_of_week_column_header = 0x7f120112;
        public static int mtrl_picker_invalid_format = 0x7f120113;
        public static int mtrl_picker_invalid_format_example = 0x7f120114;
        public static int mtrl_picker_invalid_format_use = 0x7f120115;
        public static int mtrl_picker_invalid_range = 0x7f120116;
        public static int mtrl_picker_navigate_to_year_description = 0x7f120117;
        public static int mtrl_picker_out_of_range = 0x7f120118;
        public static int mtrl_picker_range_header_only_end_selected = 0x7f120119;
        public static int mtrl_picker_range_header_only_start_selected = 0x7f12011a;
        public static int mtrl_picker_range_header_selected = 0x7f12011b;
        public static int mtrl_picker_range_header_title = 0x7f12011c;
        public static int mtrl_picker_range_header_unselected = 0x7f12011d;
        public static int mtrl_picker_save = 0x7f12011e;
        public static int mtrl_picker_text_input_date_hint = 0x7f12011f;
        public static int mtrl_picker_text_input_date_range_end_hint = 0x7f120120;
        public static int mtrl_picker_text_input_date_range_start_hint = 0x7f120121;
        public static int mtrl_picker_text_input_day_abbr = 0x7f120122;
        public static int mtrl_picker_text_input_month_abbr = 0x7f120123;
        public static int mtrl_picker_text_input_year_abbr = 0x7f120124;
        public static int mtrl_picker_toggle_to_calendar_input_mode = 0x7f120125;
        public static int mtrl_picker_toggle_to_day_selection = 0x7f120126;
        public static int mtrl_picker_toggle_to_text_input_mode = 0x7f120127;
        public static int mtrl_picker_toggle_to_year_selection = 0x7f120128;
        public static int password_toggle_content_description = 0x7f12012f;
        public static int path_password_eye = 0x7f120130;
        public static int path_password_eye_mask_strike_through = 0x7f120131;
        public static int path_password_eye_mask_visible = 0x7f120132;
        public static int path_password_strike_through = 0x7f120133;
        public static int search_menu_title = 0x7f120136;
        public static int status_bar_notification_info_overflow = 0x7f120138;
        public static int tp_loading_spinner_content_desc = 0x7f12013b;

        private string() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$style.smali */
    public static final class style {
        public static int AlertDialog_AppCompat = 0x7f130000;
        public static int AlertDialog_AppCompat_Light = 0x7f130001;
        public static int Animation_AppCompat_Dialog = 0x7f130002;
        public static int Animation_AppCompat_DropDownUp = 0x7f130003;
        public static int Animation_AppCompat_Tooltip = 0x7f130004;
        public static int Animation_Design_BottomSheetDialog = 0x7f130005;
        public static int Animation_MaterialComponents_BottomSheetDialog = 0x7f130006;
        public static int AntelopSdk = 0x7f130007;
        public static int AntelopSdk_Button = 0x7f130008;
        public static int AntelopSdk_Button_Prompt = 0x7f130009;
        public static int AntelopSdk_ImageView = 0x7f13000a;
        public static int AntelopSdk_ImageView_CardDisplay = 0x7f13000b;
        public static int AntelopSdk_ImageView_CardPromptActivity = 0x7f13000c;
        public static int AntelopSdk_ImageView_PinDisplay = 0x7f13000d;
        public static int AntelopSdk_ImageView_Prompt = 0x7f13000e;
        public static int AntelopSdk_Keyboard = 0x7f13000f;
        public static int AntelopSdk_Keyboard_Alpha = 0x7f130010;
        public static int AntelopSdk_Keyboard_Background = 0x7f130011;
        public static int AntelopSdk_Keyboard_Digit = 0x7f130012;
        public static int AntelopSdk_ProgressBar = 0x7f130013;
        public static int AntelopSdk_TextInputLayout = 0x7f130014;
        public static int AntelopSdk_TextInputLayout_CardPromptActivity = 0x7f130015;
        public static int AntelopSdk_TextView = 0x7f130016;
        public static int AntelopSdk_TextView_CardDisplay = 0x7f130017;
        public static int AntelopSdk_TextView_CardPromptActivity = 0x7f130018;
        public static int AntelopSdk_TextView_Description = 0x7f130019;
        public static int AntelopSdk_TextView_Description_Prompt = 0x7f13001a;
        public static int AntelopSdk_TextView_PinDisplay = 0x7f13001b;
        public static int AntelopSdk_TextView_Subtitle = 0x7f13001c;
        public static int AntelopSdk_TextView_Subtitle_Prompt = 0x7f13001d;
        public static int AntelopSdk_TextView_Title = 0x7f13001e;
        public static int AntelopSdk_TextView_Title_Prompt = 0x7f13001f;
        public static int Base_AlertDialog_AppCompat = 0x7f130026;
        public static int Base_AlertDialog_AppCompat_Light = 0x7f130027;
        public static int Base_Animation_AppCompat_Dialog = 0x7f130028;
        public static int Base_Animation_AppCompat_DropDownUp = 0x7f130029;
        public static int Base_Animation_AppCompat_Tooltip = 0x7f13002a;
        public static int Base_CardView = 0x7f13002b;
        public static int Base_DialogWindowTitleBackground_AppCompat = 0x7f13002d;
        public static int Base_DialogWindowTitle_AppCompat = 0x7f13002c;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f13002e;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f13002f;
        public static int Base_MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f130030;
        public static int Base_TextAppearance_AppCompat = 0x7f130031;
        public static int Base_TextAppearance_AppCompat_Body1 = 0x7f130032;
        public static int Base_TextAppearance_AppCompat_Body2 = 0x7f130033;
        public static int Base_TextAppearance_AppCompat_Button = 0x7f130034;
        public static int Base_TextAppearance_AppCompat_Caption = 0x7f130035;
        public static int Base_TextAppearance_AppCompat_Display1 = 0x7f130036;
        public static int Base_TextAppearance_AppCompat_Display2 = 0x7f130037;
        public static int Base_TextAppearance_AppCompat_Display3 = 0x7f130038;
        public static int Base_TextAppearance_AppCompat_Display4 = 0x7f130039;
        public static int Base_TextAppearance_AppCompat_Headline = 0x7f13003a;
        public static int Base_TextAppearance_AppCompat_Inverse = 0x7f13003b;
        public static int Base_TextAppearance_AppCompat_Large = 0x7f13003c;
        public static int Base_TextAppearance_AppCompat_Large_Inverse = 0x7f13003d;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f13003e;
        public static int Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f13003f;
        public static int Base_TextAppearance_AppCompat_Medium = 0x7f130040;
        public static int Base_TextAppearance_AppCompat_Medium_Inverse = 0x7f130041;
        public static int Base_TextAppearance_AppCompat_Menu = 0x7f130042;
        public static int Base_TextAppearance_AppCompat_SearchResult = 0x7f130043;
        public static int Base_TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f130044;
        public static int Base_TextAppearance_AppCompat_SearchResult_Title = 0x7f130045;
        public static int Base_TextAppearance_AppCompat_Small = 0x7f130046;
        public static int Base_TextAppearance_AppCompat_Small_Inverse = 0x7f130047;
        public static int Base_TextAppearance_AppCompat_Subhead = 0x7f130048;
        public static int Base_TextAppearance_AppCompat_Subhead_Inverse = 0x7f130049;
        public static int Base_TextAppearance_AppCompat_Title = 0x7f13004a;
        public static int Base_TextAppearance_AppCompat_Title_Inverse = 0x7f13004b;
        public static int Base_TextAppearance_AppCompat_Tooltip = 0x7f13004c;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f13004d;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f13004e;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f13004f;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f130050;
        public static int Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f130051;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f130052;
        public static int Base_TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f130053;
        public static int Base_TextAppearance_AppCompat_Widget_Button = 0x7f130054;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f130055;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Colored = 0x7f130056;
        public static int Base_TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f130057;
        public static int Base_TextAppearance_AppCompat_Widget_DropDownItem = 0x7f130058;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f130059;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f13005a;
        public static int Base_TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f13005b;
        public static int Base_TextAppearance_AppCompat_Widget_Switch = 0x7f13005c;
        public static int Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f13005d;
        public static int Base_TextAppearance_MaterialComponents_Badge = 0x7f13005e;
        public static int Base_TextAppearance_MaterialComponents_Button = 0x7f13005f;
        public static int Base_TextAppearance_MaterialComponents_Headline6 = 0x7f130060;
        public static int Base_TextAppearance_MaterialComponents_Subtitle2 = 0x7f130061;
        public static int Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f130062;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f130063;
        public static int Base_TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f130064;
        public static int Base_ThemeOverlay_AppCompat = 0x7f130089;
        public static int Base_ThemeOverlay_AppCompat_ActionBar = 0x7f13008a;
        public static int Base_ThemeOverlay_AppCompat_Dark = 0x7f13008b;
        public static int Base_ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f13008c;
        public static int Base_ThemeOverlay_AppCompat_Dialog = 0x7f13008d;
        public static int Base_ThemeOverlay_AppCompat_Dialog_Alert = 0x7f13008e;
        public static int Base_ThemeOverlay_AppCompat_Light = 0x7f13008f;
        public static int Base_ThemeOverlay_MaterialComponents_Dialog = 0x7f130090;
        public static int Base_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f130091;
        public static int Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f130092;
        public static int Base_Theme_AppCompat = 0x7f130065;
        public static int Base_Theme_AppCompat_CompactMenu = 0x7f130066;
        public static int Base_Theme_AppCompat_Dialog = 0x7f130067;
        public static int Base_Theme_AppCompat_DialogWhenLarge = 0x7f13006b;
        public static int Base_Theme_AppCompat_Dialog_Alert = 0x7f130068;
        public static int Base_Theme_AppCompat_Dialog_FixedSize = 0x7f130069;
        public static int Base_Theme_AppCompat_Dialog_MinWidth = 0x7f13006a;
        public static int Base_Theme_AppCompat_Light = 0x7f13006c;
        public static int Base_Theme_AppCompat_Light_DarkActionBar = 0x7f13006d;
        public static int Base_Theme_AppCompat_Light_Dialog = 0x7f13006e;
        public static int Base_Theme_AppCompat_Light_DialogWhenLarge = 0x7f130072;
        public static int Base_Theme_AppCompat_Light_Dialog_Alert = 0x7f13006f;
        public static int Base_Theme_AppCompat_Light_Dialog_FixedSize = 0x7f130070;
        public static int Base_Theme_AppCompat_Light_Dialog_MinWidth = 0x7f130071;
        public static int Base_Theme_MaterialComponents = 0x7f130073;
        public static int Base_Theme_MaterialComponents_Bridge = 0x7f130074;
        public static int Base_Theme_MaterialComponents_CompactMenu = 0x7f130075;
        public static int Base_Theme_MaterialComponents_Dialog = 0x7f130076;
        public static int Base_Theme_MaterialComponents_DialogWhenLarge = 0x7f13007b;
        public static int Base_Theme_MaterialComponents_Dialog_Alert = 0x7f130077;
        public static int Base_Theme_MaterialComponents_Dialog_Bridge = 0x7f130078;
        public static int Base_Theme_MaterialComponents_Dialog_FixedSize = 0x7f130079;
        public static int Base_Theme_MaterialComponents_Dialog_MinWidth = 0x7f13007a;
        public static int Base_Theme_MaterialComponents_Light = 0x7f13007c;
        public static int Base_Theme_MaterialComponents_Light_Bridge = 0x7f13007d;
        public static int Base_Theme_MaterialComponents_Light_DarkActionBar = 0x7f13007e;
        public static int Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f13007f;
        public static int Base_Theme_MaterialComponents_Light_Dialog = 0x7f130080;
        public static int Base_Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f130085;
        public static int Base_Theme_MaterialComponents_Light_Dialog_Alert = 0x7f130081;
        public static int Base_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f130082;
        public static int Base_Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f130083;
        public static int Base_Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f130084;
        public static int Base_V14_ThemeOverlay_MaterialComponents_Dialog = 0x7f13009c;
        public static int Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f13009d;
        public static int Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f13009e;
        public static int Base_V14_Theme_MaterialComponents = 0x7f130093;
        public static int Base_V14_Theme_MaterialComponents_Bridge = 0x7f130094;
        public static int Base_V14_Theme_MaterialComponents_Dialog = 0x7f130095;
        public static int Base_V14_Theme_MaterialComponents_Dialog_Bridge = 0x7f130096;
        public static int Base_V14_Theme_MaterialComponents_Light = 0x7f130097;
        public static int Base_V14_Theme_MaterialComponents_Light_Bridge = 0x7f130098;
        public static int Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f130099;
        public static int Base_V14_Theme_MaterialComponents_Light_Dialog = 0x7f13009a;
        public static int Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f13009b;
        public static int Base_V21_ThemeOverlay_AppCompat_Dialog = 0x7f1300a3;
        public static int Base_V21_Theme_AppCompat = 0x7f13009f;
        public static int Base_V21_Theme_AppCompat_Dialog = 0x7f1300a0;
        public static int Base_V21_Theme_AppCompat_Light = 0x7f1300a1;
        public static int Base_V21_Theme_AppCompat_Light_Dialog = 0x7f1300a2;
        public static int Base_V22_Theme_AppCompat = 0x7f1300a4;
        public static int Base_V22_Theme_AppCompat_Light = 0x7f1300a5;
        public static int Base_V23_Theme_AppCompat = 0x7f1300a6;
        public static int Base_V23_Theme_AppCompat_Light = 0x7f1300a7;
        public static int Base_V26_Theme_AppCompat = 0x7f1300a8;
        public static int Base_V26_Theme_AppCompat_Light = 0x7f1300a9;
        public static int Base_V26_Widget_AppCompat_Toolbar = 0x7f1300aa;
        public static int Base_V28_Theme_AppCompat = 0x7f1300ab;
        public static int Base_V28_Theme_AppCompat_Light = 0x7f1300ac;
        public static int Base_V7_ThemeOverlay_AppCompat_Dialog = 0x7f1300b1;
        public static int Base_V7_Theme_AppCompat = 0x7f1300ad;
        public static int Base_V7_Theme_AppCompat_Dialog = 0x7f1300ae;
        public static int Base_V7_Theme_AppCompat_Light = 0x7f1300af;
        public static int Base_V7_Theme_AppCompat_Light_Dialog = 0x7f1300b0;
        public static int Base_V7_Widget_AppCompat_AutoCompleteTextView = 0x7f1300b2;
        public static int Base_V7_Widget_AppCompat_EditText = 0x7f1300b3;
        public static int Base_V7_Widget_AppCompat_Toolbar = 0x7f1300b4;
        public static int Base_Widget_AppCompat_ActionBar = 0x7f1300b5;
        public static int Base_Widget_AppCompat_ActionBar_Solid = 0x7f1300b6;
        public static int Base_Widget_AppCompat_ActionBar_TabBar = 0x7f1300b7;
        public static int Base_Widget_AppCompat_ActionBar_TabText = 0x7f1300b8;
        public static int Base_Widget_AppCompat_ActionBar_TabView = 0x7f1300b9;
        public static int Base_Widget_AppCompat_ActionButton = 0x7f1300ba;
        public static int Base_Widget_AppCompat_ActionButton_CloseMode = 0x7f1300bb;
        public static int Base_Widget_AppCompat_ActionButton_Overflow = 0x7f1300bc;
        public static int Base_Widget_AppCompat_ActionMode = 0x7f1300bd;
        public static int Base_Widget_AppCompat_ActivityChooserView = 0x7f1300be;
        public static int Base_Widget_AppCompat_AutoCompleteTextView = 0x7f1300bf;
        public static int Base_Widget_AppCompat_Button = 0x7f1300c0;
        public static int Base_Widget_AppCompat_ButtonBar = 0x7f1300c6;
        public static int Base_Widget_AppCompat_ButtonBar_AlertDialog = 0x7f1300c7;
        public static int Base_Widget_AppCompat_Button_Borderless = 0x7f1300c1;
        public static int Base_Widget_AppCompat_Button_Borderless_Colored = 0x7f1300c2;
        public static int Base_Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f1300c3;
        public static int Base_Widget_AppCompat_Button_Colored = 0x7f1300c4;
        public static int Base_Widget_AppCompat_Button_Small = 0x7f1300c5;
        public static int Base_Widget_AppCompat_CompoundButton_CheckBox = 0x7f1300c8;
        public static int Base_Widget_AppCompat_CompoundButton_RadioButton = 0x7f1300c9;
        public static int Base_Widget_AppCompat_CompoundButton_Switch = 0x7f1300ca;
        public static int Base_Widget_AppCompat_DrawerArrowToggle = 0x7f1300cb;
        public static int Base_Widget_AppCompat_DrawerArrowToggle_Common = 0x7f1300cc;
        public static int Base_Widget_AppCompat_DropDownItem_Spinner = 0x7f1300cd;
        public static int Base_Widget_AppCompat_EditText = 0x7f1300ce;
        public static int Base_Widget_AppCompat_ImageButton = 0x7f1300cf;
        public static int Base_Widget_AppCompat_Light_ActionBar = 0x7f1300d0;
        public static int Base_Widget_AppCompat_Light_ActionBar_Solid = 0x7f1300d1;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabBar = 0x7f1300d2;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText = 0x7f1300d3;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f1300d4;
        public static int Base_Widget_AppCompat_Light_ActionBar_TabView = 0x7f1300d5;
        public static int Base_Widget_AppCompat_Light_PopupMenu = 0x7f1300d6;
        public static int Base_Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f1300d7;
        public static int Base_Widget_AppCompat_ListMenuView = 0x7f1300d8;
        public static int Base_Widget_AppCompat_ListPopupWindow = 0x7f1300d9;
        public static int Base_Widget_AppCompat_ListView = 0x7f1300da;
        public static int Base_Widget_AppCompat_ListView_DropDown = 0x7f1300db;
        public static int Base_Widget_AppCompat_ListView_Menu = 0x7f1300dc;
        public static int Base_Widget_AppCompat_PopupMenu = 0x7f1300dd;
        public static int Base_Widget_AppCompat_PopupMenu_Overflow = 0x7f1300de;
        public static int Base_Widget_AppCompat_PopupWindow = 0x7f1300df;
        public static int Base_Widget_AppCompat_ProgressBar = 0x7f1300e0;
        public static int Base_Widget_AppCompat_ProgressBar_Horizontal = 0x7f1300e1;
        public static int Base_Widget_AppCompat_RatingBar = 0x7f1300e2;
        public static int Base_Widget_AppCompat_RatingBar_Indicator = 0x7f1300e3;
        public static int Base_Widget_AppCompat_RatingBar_Small = 0x7f1300e4;
        public static int Base_Widget_AppCompat_SearchView = 0x7f1300e5;
        public static int Base_Widget_AppCompat_SearchView_ActionBar = 0x7f1300e6;
        public static int Base_Widget_AppCompat_SeekBar = 0x7f1300e7;
        public static int Base_Widget_AppCompat_SeekBar_Discrete = 0x7f1300e8;
        public static int Base_Widget_AppCompat_Spinner = 0x7f1300e9;
        public static int Base_Widget_AppCompat_Spinner_Underlined = 0x7f1300ea;
        public static int Base_Widget_AppCompat_TextView = 0x7f1300eb;
        public static int Base_Widget_AppCompat_TextView_SpinnerItem = 0x7f1300ec;
        public static int Base_Widget_AppCompat_Toolbar = 0x7f1300ed;
        public static int Base_Widget_AppCompat_Toolbar_Button_Navigation = 0x7f1300ee;
        public static int Base_Widget_Design_TabLayout = 0x7f1300ef;
        public static int Base_Widget_MaterialComponents_AutoCompleteTextView = 0x7f1300f0;
        public static int Base_Widget_MaterialComponents_CheckedTextView = 0x7f1300f1;
        public static int Base_Widget_MaterialComponents_Chip = 0x7f1300f2;
        public static int Base_Widget_MaterialComponents_PopupMenu = 0x7f1300f3;
        public static int Base_Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1300f4;
        public static int Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1300f5;
        public static int Base_Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1300f6;
        public static int Base_Widget_MaterialComponents_TextInputEditText = 0x7f1300f7;
        public static int Base_Widget_MaterialComponents_TextInputLayout = 0x7f1300f8;
        public static int Base_Widget_MaterialComponents_TextView = 0x7f1300f9;
        public static int CardView = 0x7f1300fe;
        public static int CardView_Dark = 0x7f1300ff;
        public static int CardView_Light = 0x7f130100;
        public static int MaterialAlertDialog_MaterialComponents = 0x7f130103;
        public static int MaterialAlertDialog_MaterialComponents_Body_Text = 0x7f130104;
        public static int MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar = 0x7f130105;
        public static int MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner = 0x7f130106;
        public static int MaterialAlertDialog_MaterialComponents_Title_Icon = 0x7f130107;
        public static int MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked = 0x7f130108;
        public static int MaterialAlertDialog_MaterialComponents_Title_Panel = 0x7f130109;
        public static int MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked = 0x7f13010a;
        public static int MaterialAlertDialog_MaterialComponents_Title_Text = 0x7f13010b;
        public static int MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked = 0x7f13010c;
        public static int Platform_AppCompat = 0x7f13010d;
        public static int Platform_AppCompat_Light = 0x7f13010e;
        public static int Platform_MaterialComponents = 0x7f13010f;
        public static int Platform_MaterialComponents_Dialog = 0x7f130110;
        public static int Platform_MaterialComponents_Light = 0x7f130111;
        public static int Platform_MaterialComponents_Light_Dialog = 0x7f130112;
        public static int Platform_ThemeOverlay_AppCompat = 0x7f130113;
        public static int Platform_ThemeOverlay_AppCompat_Dark = 0x7f130114;
        public static int Platform_ThemeOverlay_AppCompat_Light = 0x7f130115;
        public static int Platform_V21_AppCompat = 0x7f130116;
        public static int Platform_V21_AppCompat_Light = 0x7f130117;
        public static int Platform_V25_AppCompat = 0x7f130118;
        public static int Platform_V25_AppCompat_Light = 0x7f130119;
        public static int Platform_Widget_AppCompat_Spinner = 0x7f13011a;
        public static int RtlOverlay_DialogWindowTitle_AppCompat = 0x7f13011b;
        public static int RtlOverlay_Widget_AppCompat_ActionBar_TitleItem = 0x7f13011c;
        public static int RtlOverlay_Widget_AppCompat_DialogTitle_Icon = 0x7f13011d;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem = 0x7f13011e;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup = 0x7f13011f;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut = 0x7f130120;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow = 0x7f130121;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Text = 0x7f130122;
        public static int RtlOverlay_Widget_AppCompat_PopupMenuItem_Title = 0x7f130123;
        public static int RtlOverlay_Widget_AppCompat_SearchView_MagIcon = 0x7f130129;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown = 0x7f130124;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 = 0x7f130125;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 = 0x7f130126;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Query = 0x7f130127;
        public static int RtlOverlay_Widget_AppCompat_Search_DropDown_Text = 0x7f130128;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton = 0x7f13012a;
        public static int RtlUnderlay_Widget_AppCompat_ActionButton_Overflow = 0x7f13012b;
        public static int ShapeAppearanceOverlay_MaterialComponents_BottomSheet = 0x7f130136;
        public static int ShapeAppearanceOverlay_MaterialComponents_Chip = 0x7f130137;
        public static int ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton = 0x7f130138;
        public static int ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton = 0x7f130139;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day = 0x7f13013a;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen = 0x7f13013b;
        public static int ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year = 0x7f13013c;
        public static int ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox = 0x7f13013d;
        public static int ShapeAppearanceOverlay_MyApp_Button_Circle = 0x7f13013e;
        public static int ShapeAppearance_MaterialComponents = 0x7f13012c;
        public static int ShapeAppearance_MaterialComponents_LargeComponent = 0x7f13012d;
        public static int ShapeAppearance_MaterialComponents_MediumComponent = 0x7f13012e;
        public static int ShapeAppearance_MaterialComponents_SmallComponent = 0x7f13012f;
        public static int TapAndPayTheme = 0x7f130141;
        public static int TextAppearance_AppCompat = 0x7f13014d;
        public static int TextAppearance_AppCompat_Body1 = 0x7f13014e;
        public static int TextAppearance_AppCompat_Body2 = 0x7f13014f;
        public static int TextAppearance_AppCompat_Button = 0x7f130150;
        public static int TextAppearance_AppCompat_Caption = 0x7f130151;
        public static int TextAppearance_AppCompat_Display1 = 0x7f130152;
        public static int TextAppearance_AppCompat_Display2 = 0x7f130153;
        public static int TextAppearance_AppCompat_Display3 = 0x7f130154;
        public static int TextAppearance_AppCompat_Display4 = 0x7f130155;
        public static int TextAppearance_AppCompat_Headline = 0x7f130156;
        public static int TextAppearance_AppCompat_Inverse = 0x7f130157;
        public static int TextAppearance_AppCompat_Large = 0x7f130158;
        public static int TextAppearance_AppCompat_Large_Inverse = 0x7f130159;
        public static int TextAppearance_AppCompat_Light_SearchResult_Subtitle = 0x7f13015a;
        public static int TextAppearance_AppCompat_Light_SearchResult_Title = 0x7f13015b;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Large = 0x7f13015c;
        public static int TextAppearance_AppCompat_Light_Widget_PopupMenu_Small = 0x7f13015d;
        public static int TextAppearance_AppCompat_Medium = 0x7f13015e;
        public static int TextAppearance_AppCompat_Medium_Inverse = 0x7f13015f;
        public static int TextAppearance_AppCompat_Menu = 0x7f130160;
        public static int TextAppearance_AppCompat_SearchResult_Subtitle = 0x7f130161;
        public static int TextAppearance_AppCompat_SearchResult_Title = 0x7f130162;
        public static int TextAppearance_AppCompat_Small = 0x7f130163;
        public static int TextAppearance_AppCompat_Small_Inverse = 0x7f130164;
        public static int TextAppearance_AppCompat_Subhead = 0x7f130165;
        public static int TextAppearance_AppCompat_Subhead_Inverse = 0x7f130166;
        public static int TextAppearance_AppCompat_Title = 0x7f130167;
        public static int TextAppearance_AppCompat_Title_Inverse = 0x7f130168;
        public static int TextAppearance_AppCompat_Tooltip = 0x7f130169;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Menu = 0x7f13016a;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle = 0x7f13016b;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse = 0x7f13016c;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title = 0x7f13016d;
        public static int TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse = 0x7f13016e;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle = 0x7f13016f;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse = 0x7f130170;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title = 0x7f130171;
        public static int TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse = 0x7f130172;
        public static int TextAppearance_AppCompat_Widget_Button = 0x7f130173;
        public static int TextAppearance_AppCompat_Widget_Button_Borderless_Colored = 0x7f130174;
        public static int TextAppearance_AppCompat_Widget_Button_Colored = 0x7f130175;
        public static int TextAppearance_AppCompat_Widget_Button_Inverse = 0x7f130176;
        public static int TextAppearance_AppCompat_Widget_DropDownItem = 0x7f130177;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Header = 0x7f130178;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Large = 0x7f130179;
        public static int TextAppearance_AppCompat_Widget_PopupMenu_Small = 0x7f13017a;
        public static int TextAppearance_AppCompat_Widget_Switch = 0x7f13017b;
        public static int TextAppearance_AppCompat_Widget_TextView_SpinnerItem = 0x7f13017c;
        public static int TextAppearance_Compat_Notification = 0x7f13017d;
        public static int TextAppearance_Compat_Notification_Info = 0x7f13017e;
        public static int TextAppearance_Compat_Notification_Line2 = 0x7f130180;
        public static int TextAppearance_Compat_Notification_Time = 0x7f130183;
        public static int TextAppearance_Compat_Notification_Title = 0x7f130185;
        public static int TextAppearance_Design_CollapsingToolbar_Expanded = 0x7f130187;
        public static int TextAppearance_Design_Counter = 0x7f130188;
        public static int TextAppearance_Design_Counter_Overflow = 0x7f130189;
        public static int TextAppearance_Design_Error = 0x7f13018a;
        public static int TextAppearance_Design_HelperText = 0x7f13018b;
        public static int TextAppearance_Design_Hint = 0x7f13018c;
        public static int TextAppearance_Design_Snackbar_Message = 0x7f13018d;
        public static int TextAppearance_Design_Tab = 0x7f13018e;
        public static int TextAppearance_MaterialComponents_Badge = 0x7f13018f;
        public static int TextAppearance_MaterialComponents_Body1 = 0x7f130190;
        public static int TextAppearance_MaterialComponents_Body2 = 0x7f130191;
        public static int TextAppearance_MaterialComponents_Button = 0x7f130192;
        public static int TextAppearance_MaterialComponents_Caption = 0x7f130193;
        public static int TextAppearance_MaterialComponents_Chip = 0x7f130194;
        public static int TextAppearance_MaterialComponents_Headline1 = 0x7f130195;
        public static int TextAppearance_MaterialComponents_Headline2 = 0x7f130196;
        public static int TextAppearance_MaterialComponents_Headline3 = 0x7f130197;
        public static int TextAppearance_MaterialComponents_Headline4 = 0x7f130198;
        public static int TextAppearance_MaterialComponents_Headline5 = 0x7f130199;
        public static int TextAppearance_MaterialComponents_Headline6 = 0x7f13019a;
        public static int TextAppearance_MaterialComponents_Overline = 0x7f13019b;
        public static int TextAppearance_MaterialComponents_Subtitle1 = 0x7f13019c;
        public static int TextAppearance_MaterialComponents_Subtitle2 = 0x7f13019d;
        public static int TextAppearance_Widget_AppCompat_ExpandedMenu_Item = 0x7f13019e;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Subtitle = 0x7f13019f;
        public static int TextAppearance_Widget_AppCompat_Toolbar_Title = 0x7f1301a0;
        public static int ThemeOverlay_AppCompat = 0x7f1301fb;
        public static int ThemeOverlay_AppCompat_ActionBar = 0x7f1301fc;
        public static int ThemeOverlay_AppCompat_Dark = 0x7f1301fd;
        public static int ThemeOverlay_AppCompat_Dark_ActionBar = 0x7f1301fe;
        public static int ThemeOverlay_AppCompat_DayNight = 0x7f1301ff;
        public static int ThemeOverlay_AppCompat_DayNight_ActionBar = 0x7f130200;
        public static int ThemeOverlay_AppCompat_Dialog = 0x7f130201;
        public static int ThemeOverlay_AppCompat_Dialog_Alert = 0x7f130202;
        public static int ThemeOverlay_AppCompat_Light = 0x7f130203;
        public static int ThemeOverlay_Design_TextInputEditText = 0x7f130204;
        public static int ThemeOverlay_MaterialComponents = 0x7f130205;
        public static int ThemeOverlay_MaterialComponents_ActionBar = 0x7f130206;
        public static int ThemeOverlay_MaterialComponents_ActionBar_Primary = 0x7f130207;
        public static int ThemeOverlay_MaterialComponents_ActionBar_Surface = 0x7f130208;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView = 0x7f130209;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f13020a;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f13020b;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f13020c;
        public static int ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f13020d;
        public static int ThemeOverlay_MaterialComponents_BottomAppBar_Primary = 0x7f13020e;
        public static int ThemeOverlay_MaterialComponents_BottomAppBar_Surface = 0x7f13020f;
        public static int ThemeOverlay_MaterialComponents_BottomSheetDialog = 0x7f130210;
        public static int ThemeOverlay_MaterialComponents_Dark = 0x7f130211;
        public static int ThemeOverlay_MaterialComponents_Dark_ActionBar = 0x7f130212;
        public static int ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog = 0x7f130213;
        public static int ThemeOverlay_MaterialComponents_Dialog = 0x7f130214;
        public static int ThemeOverlay_MaterialComponents_Dialog_Alert = 0x7f130215;
        public static int ThemeOverlay_MaterialComponents_Light = 0x7f130216;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog = 0x7f130218;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered = 0x7f130219;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date = 0x7f13021a;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar = 0x7f13021b;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text = 0x7f13021c;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day = 0x7f13021d;
        public static int ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner = 0x7f13021e;
        public static int ThemeOverlay_MaterialComponents_MaterialCalendar = 0x7f13021f;
        public static int ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f130220;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText = 0x7f130221;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox = 0x7f130222;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f130223;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f130224;
        public static int ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f130225;
        public static int ThemeOverlay_MaterialComponents_Toolbar_Primary = 0x7f130226;
        public static int ThemeOverlay_MaterialComponents_Toolbar_Surface = 0x7f130227;
        public static int Theme_Antelop_Sdk = 0x7f1301a1;
        public static int Theme_Antelop_Sdk_Prompt = 0x7f1301a2;
        public static int Theme_Antelop_Sdk_SecureCardDisplay = 0x7f1301a3;
        public static int Theme_Antelop_Sdk_SecureCardDisplayTransparent = 0x7f1301a4;
        public static int Theme_Antelop_Sdk_SecurePinDisplayActivity = 0x7f1301a5;
        public static int Theme_Antelop_Sdk_SecurePinInput = 0x7f1301a6;
        public static int Theme_Antelop_Sdk_SecureVirtualCardNumberDisplay = 0x7f1301a7;
        public static int Theme_Antelop_Sdk_SecureVirtualCardNumberDisplayTransparent = 0x7f1301a8;
        public static int Theme_Antelop_Sdk_Transparent = 0x7f1301a9;
        public static int Theme_AppCompat = 0x7f1301aa;
        public static int Theme_AppCompat_CompactMenu = 0x7f1301ab;
        public static int Theme_AppCompat_DayNight = 0x7f1301ac;
        public static int Theme_AppCompat_DayNight_DarkActionBar = 0x7f1301ad;
        public static int Theme_AppCompat_DayNight_Dialog = 0x7f1301ae;
        public static int Theme_AppCompat_DayNight_DialogWhenLarge = 0x7f1301b1;
        public static int Theme_AppCompat_DayNight_Dialog_Alert = 0x7f1301af;
        public static int Theme_AppCompat_DayNight_Dialog_MinWidth = 0x7f1301b0;
        public static int Theme_AppCompat_DayNight_NoActionBar = 0x7f1301b2;
        public static int Theme_AppCompat_Dialog = 0x7f1301b3;
        public static int Theme_AppCompat_DialogWhenLarge = 0x7f1301b6;
        public static int Theme_AppCompat_Dialog_Alert = 0x7f1301b4;
        public static int Theme_AppCompat_Dialog_MinWidth = 0x7f1301b5;
        public static int Theme_AppCompat_Empty = 0x7f1301b7;
        public static int Theme_AppCompat_Light = 0x7f1301b8;
        public static int Theme_AppCompat_Light_DarkActionBar = 0x7f1301b9;
        public static int Theme_AppCompat_Light_Dialog = 0x7f1301ba;
        public static int Theme_AppCompat_Light_DialogWhenLarge = 0x7f1301bd;
        public static int Theme_AppCompat_Light_Dialog_Alert = 0x7f1301bb;
        public static int Theme_AppCompat_Light_Dialog_MinWidth = 0x7f1301bc;
        public static int Theme_AppCompat_Light_NoActionBar = 0x7f1301be;
        public static int Theme_AppCompat_NoActionBar = 0x7f1301bf;
        public static int Theme_Design = 0x7f1301c0;
        public static int Theme_Design_BottomSheetDialog = 0x7f1301c1;
        public static int Theme_Design_Light = 0x7f1301c2;
        public static int Theme_Design_Light_BottomSheetDialog = 0x7f1301c3;
        public static int Theme_Design_Light_NoActionBar = 0x7f1301c4;
        public static int Theme_Design_NoActionBar = 0x7f1301c5;
        public static int Theme_MaterialComponents = 0x7f1301c6;
        public static int Theme_MaterialComponents_BottomSheetDialog = 0x7f1301c7;
        public static int Theme_MaterialComponents_Bridge = 0x7f1301c8;
        public static int Theme_MaterialComponents_CompactMenu = 0x7f1301c9;
        public static int Theme_MaterialComponents_DayNight = 0x7f1301ca;
        public static int Theme_MaterialComponents_DayNight_BottomSheetDialog = 0x7f1301cb;
        public static int Theme_MaterialComponents_DayNight_Bridge = 0x7f1301cc;
        public static int Theme_MaterialComponents_DayNight_DarkActionBar = 0x7f1301cd;
        public static int Theme_MaterialComponents_DayNight_DarkActionBar_Bridge = 0x7f1301ce;
        public static int Theme_MaterialComponents_DayNight_Dialog = 0x7f1301cf;
        public static int Theme_MaterialComponents_DayNight_DialogWhenLarge = 0x7f1301d7;
        public static int Theme_MaterialComponents_DayNight_Dialog_Alert = 0x7f1301d0;
        public static int Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge = 0x7f1301d1;
        public static int Theme_MaterialComponents_DayNight_Dialog_Bridge = 0x7f1301d2;
        public static int Theme_MaterialComponents_DayNight_Dialog_FixedSize = 0x7f1301d3;
        public static int Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge = 0x7f1301d4;
        public static int Theme_MaterialComponents_DayNight_Dialog_MinWidth = 0x7f1301d5;
        public static int Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge = 0x7f1301d6;
        public static int Theme_MaterialComponents_DayNight_NoActionBar = 0x7f1301d8;
        public static int Theme_MaterialComponents_DayNight_NoActionBar_Bridge = 0x7f1301d9;
        public static int Theme_MaterialComponents_Dialog = 0x7f1301da;
        public static int Theme_MaterialComponents_DialogWhenLarge = 0x7f1301e2;
        public static int Theme_MaterialComponents_Dialog_Alert = 0x7f1301db;
        public static int Theme_MaterialComponents_Dialog_Alert_Bridge = 0x7f1301dc;
        public static int Theme_MaterialComponents_Dialog_Bridge = 0x7f1301dd;
        public static int Theme_MaterialComponents_Dialog_FixedSize = 0x7f1301de;
        public static int Theme_MaterialComponents_Dialog_FixedSize_Bridge = 0x7f1301df;
        public static int Theme_MaterialComponents_Dialog_MinWidth = 0x7f1301e0;
        public static int Theme_MaterialComponents_Dialog_MinWidth_Bridge = 0x7f1301e1;
        public static int Theme_MaterialComponents_Light = 0x7f1301e3;
        public static int Theme_MaterialComponents_Light_BottomSheetDialog = 0x7f1301e5;
        public static int Theme_MaterialComponents_Light_Bridge = 0x7f1301e6;
        public static int Theme_MaterialComponents_Light_DarkActionBar = 0x7f1301e7;
        public static int Theme_MaterialComponents_Light_DarkActionBar_Bridge = 0x7f1301e8;
        public static int Theme_MaterialComponents_Light_Dialog = 0x7f1301e9;
        public static int Theme_MaterialComponents_Light_DialogWhenLarge = 0x7f1301f1;
        public static int Theme_MaterialComponents_Light_Dialog_Alert = 0x7f1301ea;
        public static int Theme_MaterialComponents_Light_Dialog_Alert_Bridge = 0x7f1301eb;
        public static int Theme_MaterialComponents_Light_Dialog_Bridge = 0x7f1301ec;
        public static int Theme_MaterialComponents_Light_Dialog_FixedSize = 0x7f1301ed;
        public static int Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge = 0x7f1301ee;
        public static int Theme_MaterialComponents_Light_Dialog_MinWidth = 0x7f1301ef;
        public static int Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge = 0x7f1301f0;
        public static int Theme_MaterialComponents_Light_NoActionBar = 0x7f1301f3;
        public static int Theme_MaterialComponents_Light_NoActionBar_Bridge = 0x7f1301f4;
        public static int Theme_MaterialComponents_NoActionBar = 0x7f1301f5;
        public static int Theme_MaterialComponents_NoActionBar_Bridge = 0x7f1301f6;
        public static int Widget_AppCompat_ActionBar = 0x7f130229;
        public static int Widget_AppCompat_ActionBar_Solid = 0x7f13022a;
        public static int Widget_AppCompat_ActionBar_TabBar = 0x7f13022b;
        public static int Widget_AppCompat_ActionBar_TabText = 0x7f13022c;
        public static int Widget_AppCompat_ActionBar_TabView = 0x7f13022d;
        public static int Widget_AppCompat_ActionButton = 0x7f13022e;
        public static int Widget_AppCompat_ActionButton_CloseMode = 0x7f13022f;
        public static int Widget_AppCompat_ActionButton_Overflow = 0x7f130230;
        public static int Widget_AppCompat_ActionMode = 0x7f130231;
        public static int Widget_AppCompat_ActivityChooserView = 0x7f130232;
        public static int Widget_AppCompat_AutoCompleteTextView = 0x7f130233;
        public static int Widget_AppCompat_Button = 0x7f130234;
        public static int Widget_AppCompat_ButtonBar = 0x7f13023a;
        public static int Widget_AppCompat_ButtonBar_AlertDialog = 0x7f13023b;
        public static int Widget_AppCompat_Button_Borderless = 0x7f130235;
        public static int Widget_AppCompat_Button_Borderless_Colored = 0x7f130236;
        public static int Widget_AppCompat_Button_ButtonBar_AlertDialog = 0x7f130237;
        public static int Widget_AppCompat_Button_Colored = 0x7f130238;
        public static int Widget_AppCompat_Button_Small = 0x7f130239;
        public static int Widget_AppCompat_CompoundButton_CheckBox = 0x7f13023c;
        public static int Widget_AppCompat_CompoundButton_RadioButton = 0x7f13023d;
        public static int Widget_AppCompat_CompoundButton_Switch = 0x7f13023e;
        public static int Widget_AppCompat_DrawerArrowToggle = 0x7f13023f;
        public static int Widget_AppCompat_DropDownItem_Spinner = 0x7f130240;
        public static int Widget_AppCompat_EditText = 0x7f130241;
        public static int Widget_AppCompat_ImageButton = 0x7f130242;
        public static int Widget_AppCompat_Light_ActionBar = 0x7f130243;
        public static int Widget_AppCompat_Light_ActionBar_Solid = 0x7f130244;
        public static int Widget_AppCompat_Light_ActionBar_Solid_Inverse = 0x7f130245;
        public static int Widget_AppCompat_Light_ActionBar_TabBar = 0x7f130246;
        public static int Widget_AppCompat_Light_ActionBar_TabBar_Inverse = 0x7f130247;
        public static int Widget_AppCompat_Light_ActionBar_TabText = 0x7f130248;
        public static int Widget_AppCompat_Light_ActionBar_TabText_Inverse = 0x7f130249;
        public static int Widget_AppCompat_Light_ActionBar_TabView = 0x7f13024a;
        public static int Widget_AppCompat_Light_ActionBar_TabView_Inverse = 0x7f13024b;
        public static int Widget_AppCompat_Light_ActionButton = 0x7f13024c;
        public static int Widget_AppCompat_Light_ActionButton_CloseMode = 0x7f13024d;
        public static int Widget_AppCompat_Light_ActionButton_Overflow = 0x7f13024e;
        public static int Widget_AppCompat_Light_ActionMode_Inverse = 0x7f13024f;
        public static int Widget_AppCompat_Light_ActivityChooserView = 0x7f130250;
        public static int Widget_AppCompat_Light_AutoCompleteTextView = 0x7f130251;
        public static int Widget_AppCompat_Light_DropDownItem_Spinner = 0x7f130252;
        public static int Widget_AppCompat_Light_ListPopupWindow = 0x7f130253;
        public static int Widget_AppCompat_Light_ListView_DropDown = 0x7f130254;
        public static int Widget_AppCompat_Light_PopupMenu = 0x7f130255;
        public static int Widget_AppCompat_Light_PopupMenu_Overflow = 0x7f130256;
        public static int Widget_AppCompat_Light_SearchView = 0x7f130257;
        public static int Widget_AppCompat_Light_Spinner_DropDown_ActionBar = 0x7f130258;
        public static int Widget_AppCompat_ListMenuView = 0x7f130259;
        public static int Widget_AppCompat_ListPopupWindow = 0x7f13025a;
        public static int Widget_AppCompat_ListView = 0x7f13025b;
        public static int Widget_AppCompat_ListView_DropDown = 0x7f13025c;
        public static int Widget_AppCompat_ListView_Menu = 0x7f13025d;
        public static int Widget_AppCompat_PopupMenu = 0x7f13025e;
        public static int Widget_AppCompat_PopupMenu_Overflow = 0x7f13025f;
        public static int Widget_AppCompat_PopupWindow = 0x7f130260;
        public static int Widget_AppCompat_ProgressBar = 0x7f130261;
        public static int Widget_AppCompat_ProgressBar_Horizontal = 0x7f130262;
        public static int Widget_AppCompat_RatingBar = 0x7f130263;
        public static int Widget_AppCompat_RatingBar_Indicator = 0x7f130264;
        public static int Widget_AppCompat_RatingBar_Small = 0x7f130265;
        public static int Widget_AppCompat_SearchView = 0x7f130266;
        public static int Widget_AppCompat_SearchView_ActionBar = 0x7f130267;
        public static int Widget_AppCompat_SeekBar = 0x7f130268;
        public static int Widget_AppCompat_SeekBar_Discrete = 0x7f130269;
        public static int Widget_AppCompat_Spinner = 0x7f13026a;
        public static int Widget_AppCompat_Spinner_DropDown = 0x7f13026b;
        public static int Widget_AppCompat_Spinner_DropDown_ActionBar = 0x7f13026c;
        public static int Widget_AppCompat_Spinner_Underlined = 0x7f13026d;
        public static int Widget_AppCompat_TextView = 0x7f13026e;
        public static int Widget_AppCompat_TextView_SpinnerItem = 0x7f13026f;
        public static int Widget_AppCompat_Toolbar = 0x7f130270;
        public static int Widget_AppCompat_Toolbar_Button_Navigation = 0x7f130271;
        public static int Widget_Compat_NotificationActionContainer = 0x7f130272;
        public static int Widget_Compat_NotificationActionText = 0x7f130273;
        public static int Widget_Design_AppBarLayout = 0x7f130274;
        public static int Widget_Design_BottomNavigationView = 0x7f130275;
        public static int Widget_Design_BottomSheet_Modal = 0x7f130276;
        public static int Widget_Design_CollapsingToolbar = 0x7f130277;
        public static int Widget_Design_FloatingActionButton = 0x7f130278;
        public static int Widget_Design_NavigationView = 0x7f130279;
        public static int Widget_Design_ScrimInsetsFrameLayout = 0x7f13027a;
        public static int Widget_Design_Snackbar = 0x7f13027b;
        public static int Widget_Design_TabLayout = 0x7f13027c;
        public static int Widget_Design_TextInputLayout = 0x7f13027d;
        public static int Widget_MaterialComponents_ActionBar_Primary = 0x7f13027e;
        public static int Widget_MaterialComponents_ActionBar_PrimarySurface = 0x7f13027f;
        public static int Widget_MaterialComponents_ActionBar_Solid = 0x7f130280;
        public static int Widget_MaterialComponents_ActionBar_Surface = 0x7f130281;
        public static int Widget_MaterialComponents_AppBarLayout_Primary = 0x7f130282;
        public static int Widget_MaterialComponents_AppBarLayout_PrimarySurface = 0x7f130283;
        public static int Widget_MaterialComponents_AppBarLayout_Surface = 0x7f130284;
        public static int Widget_MaterialComponents_AutoCompleteTextView_FilledBox = 0x7f130285;
        public static int Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense = 0x7f130286;
        public static int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox = 0x7f130287;
        public static int Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense = 0x7f130288;
        public static int Widget_MaterialComponents_Badge = 0x7f130289;
        public static int Widget_MaterialComponents_BottomAppBar = 0x7f13028a;
        public static int Widget_MaterialComponents_BottomAppBar_Colored = 0x7f13028b;
        public static int Widget_MaterialComponents_BottomAppBar_PrimarySurface = 0x7f13028c;
        public static int Widget_MaterialComponents_BottomNavigationView = 0x7f13028d;
        public static int Widget_MaterialComponents_BottomNavigationView_Colored = 0x7f13028e;
        public static int Widget_MaterialComponents_BottomNavigationView_PrimarySurface = 0x7f13028f;
        public static int Widget_MaterialComponents_BottomSheet = 0x7f130290;
        public static int Widget_MaterialComponents_BottomSheet_Modal = 0x7f130291;
        public static int Widget_MaterialComponents_Button = 0x7f130292;
        public static int Widget_MaterialComponents_Button_Icon = 0x7f130293;
        public static int Widget_MaterialComponents_Button_OutlinedButton = 0x7f130294;
        public static int Widget_MaterialComponents_Button_OutlinedButton_Icon = 0x7f130295;
        public static int Widget_MaterialComponents_Button_TextButton = 0x7f130296;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog = 0x7f130297;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog_Flush = 0x7f130298;
        public static int Widget_MaterialComponents_Button_TextButton_Dialog_Icon = 0x7f130299;
        public static int Widget_MaterialComponents_Button_TextButton_Icon = 0x7f13029a;
        public static int Widget_MaterialComponents_Button_TextButton_Snackbar = 0x7f13029b;
        public static int Widget_MaterialComponents_Button_UnelevatedButton = 0x7f13029c;
        public static int Widget_MaterialComponents_Button_UnelevatedButton_Icon = 0x7f13029d;
        public static int Widget_MaterialComponents_CardView = 0x7f13029e;
        public static int Widget_MaterialComponents_CheckedTextView = 0x7f13029f;
        public static int Widget_MaterialComponents_ChipGroup = 0x7f1302a4;
        public static int Widget_MaterialComponents_Chip_Action = 0x7f1302a0;
        public static int Widget_MaterialComponents_Chip_Choice = 0x7f1302a1;
        public static int Widget_MaterialComponents_Chip_Entry = 0x7f1302a2;
        public static int Widget_MaterialComponents_Chip_Filter = 0x7f1302a3;
        public static int Widget_MaterialComponents_CompoundButton_CheckBox = 0x7f1302a5;
        public static int Widget_MaterialComponents_CompoundButton_RadioButton = 0x7f1302a6;
        public static int Widget_MaterialComponents_CompoundButton_Switch = 0x7f1302a7;
        public static int Widget_MaterialComponents_ExtendedFloatingActionButton = 0x7f1302a8;
        public static int Widget_MaterialComponents_ExtendedFloatingActionButton_Icon = 0x7f1302a9;
        public static int Widget_MaterialComponents_FloatingActionButton = 0x7f1302aa;
        public static int Widget_MaterialComponents_Light_ActionBar_Solid = 0x7f1302ab;
        public static int Widget_MaterialComponents_MaterialButtonToggleGroup = 0x7f1302ac;
        public static int Widget_MaterialComponents_MaterialCalendar = 0x7f1302ad;
        public static int Widget_MaterialComponents_MaterialCalendar_Day = 0x7f1302ae;
        public static int Widget_MaterialComponents_MaterialCalendar_DayTextView = 0x7f1302b2;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Invalid = 0x7f1302af;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Selected = 0x7f1302b0;
        public static int Widget_MaterialComponents_MaterialCalendar_Day_Today = 0x7f1302b1;
        public static int Widget_MaterialComponents_MaterialCalendar_Fullscreen = 0x7f1302b3;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton = 0x7f1302b4;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderDivider = 0x7f1302b5;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderLayout = 0x7f1302b6;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderSelection = 0x7f1302b7;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen = 0x7f1302b8;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderTitle = 0x7f1302b9;
        public static int Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton = 0x7f1302ba;
        public static int Widget_MaterialComponents_MaterialCalendar_Item = 0x7f1302bb;
        public static int Widget_MaterialComponents_MaterialCalendar_Year = 0x7f1302bc;
        public static int Widget_MaterialComponents_MaterialCalendar_Year_Selected = 0x7f1302bd;
        public static int Widget_MaterialComponents_MaterialCalendar_Year_Today = 0x7f1302be;
        public static int Widget_MaterialComponents_NavigationView = 0x7f1302bf;
        public static int Widget_MaterialComponents_PopupMenu = 0x7f1302c0;
        public static int Widget_MaterialComponents_PopupMenu_ContextMenu = 0x7f1302c1;
        public static int Widget_MaterialComponents_PopupMenu_ListPopupWindow = 0x7f1302c2;
        public static int Widget_MaterialComponents_PopupMenu_Overflow = 0x7f1302c3;
        public static int Widget_MaterialComponents_Snackbar = 0x7f1302c4;
        public static int Widget_MaterialComponents_Snackbar_FullWidth = 0x7f1302c5;
        public static int Widget_MaterialComponents_TabLayout = 0x7f1302c6;
        public static int Widget_MaterialComponents_TabLayout_Colored = 0x7f1302c7;
        public static int Widget_MaterialComponents_TabLayout_PrimarySurface = 0x7f1302c8;
        public static int Widget_MaterialComponents_TextInputEditText_FilledBox = 0x7f1302c9;
        public static int Widget_MaterialComponents_TextInputEditText_FilledBox_Dense = 0x7f1302ca;
        public static int Widget_MaterialComponents_TextInputEditText_OutlinedBox = 0x7f1302cb;
        public static int Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense = 0x7f1302cc;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox = 0x7f1302cd;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense = 0x7f1302ce;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu = 0x7f1302cf;
        public static int Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu = 0x7f1302d0;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox = 0x7f1302d1;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense = 0x7f1302d2;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu = 0x7f1302d3;
        public static int Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu = 0x7f1302d4;
        public static int Widget_MaterialComponents_TextView = 0x7f1302d5;
        public static int Widget_MaterialComponents_Toolbar = 0x7f1302d6;
        public static int Widget_MaterialComponents_Toolbar_Primary = 0x7f1302d7;
        public static int Widget_MaterialComponents_Toolbar_PrimarySurface = 0x7f1302d8;
        public static int Widget_MaterialComponents_Toolbar_Surface = 0x7f1302d9;
        public static int Widget_Support_CoordinatorLayout = 0x7f1302da;
        public static int antelopCardDisplayActivityBackgroundStyle = 0x7f1302db;
        public static int antelopCardDisplayActivityTransparentBackgroundStyle = 0x7f1302dc;
        public static int antelopCardDisplayCardBackground = 0x7f1302dd;
        public static int antelopCardDisplayCloseButton = 0x7f1302de;
        public static int antelopCardDisplayCopyPanButton = 0x7f1302df;
        public static int antelopCardDisplayCvx2TextView = 0x7f1302e0;
        public static int antelopCardDisplayExpiryDateTextView = 0x7f1302e1;
        public static int antelopCardDisplayPanTextView = 0x7f1302e2;
        public static int antelopCardDisplayTheme = 0x7f1302e3;
        public static int antelopCardDisplayTransparentTheme = 0x7f1302e4;
        public static int antelopCardPromptActivityCardholderNameTextInputLayout = 0x7f1302e5;
        public static int antelopCardPromptActivityCvx2TextInputLayout = 0x7f1302e6;
        public static int antelopCardPromptActivityExpiryDateTextInputLayout = 0x7f1302e7;
        public static int antelopCardPromptActivityKeyboardBackgroundStyle = 0x7f1302e8;
        public static int antelopCardPromptActivityKeyboardDigitStyle = 0x7f1302e9;
        public static int antelopCardPromptActivityKeyboardLayoutStyle = 0x7f1302ea;
        public static int antelopCardPromptActivityLoadingBackgroundStyle = 0x7f1302eb;
        public static int antelopCardPromptActivityLoadingProgressBar = 0x7f1302ec;
        public static int antelopCardPromptActivityLoadingTextView = 0x7f1302ed;
        public static int antelopCardPromptActivityNfcScanImageView = 0x7f1302ee;
        public static int antelopCardPromptActivityNfcScanTextView = 0x7f1302ef;
        public static int antelopCardPromptActivityPanImageButton = 0x7f1302f0;
        public static int antelopCardPromptActivityPanTextInputLayout = 0x7f1302f1;
        public static int antelopCardPromptActivitySubmitButton = 0x7f1302f2;
        public static int antelopCardPromptActivityTheme = 0x7f1302f3;
        public static int antelopConsentPromptCancelIcon = 0x7f1302f4;
        public static int antelopConsentPromptSubmitButton = 0x7f1302f5;
        public static int antelopConsentPromptSubtitle = 0x7f1302f6;
        public static int antelopConsentPromptTitle = 0x7f1302f7;
        public static int antelopDeviceWalletMockTheme = 0x7f1302f8;
        public static int antelopKeypadViewKeyboardAlphaStyle = 0x7f1302f9;
        public static int antelopKeypadViewKeyboardBackgroundStyle = 0x7f1302fa;
        public static int antelopKeypadViewKeyboardDigitStyle = 0x7f1302fb;
        public static int antelopPinDisplayCardBackground = 0x7f1302fc;
        public static int antelopPinDisplayCloseButtonStyle = 0x7f1302fd;
        public static int antelopPinDisplayPinTextViewLayout = 0x7f1302fe;
        public static int antelopPinDisplayTheme = 0x7f1302ff;
        public static int antelopPinDisplayTitleTextViewLayout = 0x7f130300;
        public static int antelopPinPromptCancelIcon = 0x7f130301;
        public static int antelopPinPromptDescription = 0x7f130302;
        public static int antelopPinPromptKeyboardAlphaStyle = 0x7f130303;
        public static int antelopPinPromptKeyboardBackgroundStyle = 0x7f130304;
        public static int antelopPinPromptKeyboardDigitStyle = 0x7f130305;
        public static int antelopPinPromptSubtitle = 0x7f130306;
        public static int antelopPinPromptTitle = 0x7f130307;
        public static int antelopScreenUnlockPromptCancelIcon = 0x7f130308;
        public static int antelopScreenUnlockPromptSubmitButton = 0x7f130309;
        public static int antelopScreenUnlockPromptSubtitle = 0x7f13030a;
        public static int antelopScreenUnlockPromptTitle = 0x7f13030b;
        public static int antelopSecureCardDisplayActivityBackgroundStyle = 0x7f13030c;
        public static int antelopSecureCardDisplayActivityTransparentBackgroundStyle = 0x7f13030d;
        public static int antelopSecureCardDisplayCardBackground = 0x7f13030e;
        public static int antelopSecureCardDisplayCloseButton = 0x7f13030f;
        public static int antelopSecureCardDisplayCopyPanButton = 0x7f130310;
        public static int antelopSecureCardDisplayCvx2TextView = 0x7f130311;
        public static int antelopSecureCardDisplayExpiryDateTextView = 0x7f130312;
        public static int antelopSecureCardDisplayPanTextView = 0x7f130313;
        public static int antelopSecureCardDisplayThemeInternal = 0x7f130314;
        public static int antelopSecureCardDisplayTransparentThemeInternal = 0x7f130315;
        public static int antelopSecurePinDisplayActivityBackgroundStyle = 0x7f130316;
        public static int antelopSecurePinDisplayCardBackground = 0x7f130317;
        public static int antelopSecurePinDisplayCloseButtonStyle = 0x7f130318;
        public static int antelopSecurePinDisplayPinTextViewLayout = 0x7f130319;
        public static int antelopSecurePinDisplayTheme = 0x7f13031a;
        public static int antelopSecurePinDisplayThemeInternal = 0x7f13031b;
        public static int antelopSecurePinDisplayTitleTextViewLayout = 0x7f13031c;
        public static int antelopSecurePinInputAlphaStyle = 0x7f13031d;
        public static int antelopSecurePinInputBackgroundStyle = 0x7f13031e;
        public static int antelopSecurePinInputDigitStyle = 0x7f13031f;
        public static int antelopSecurePinInputThemeInternal = 0x7f130320;
        public static int antelopSecureVirtualCardNumberDisplayActivityBackgroundStyle = 0x7f130321;
        public static int antelopSecureVirtualCardNumberDisplayActivityTransparentBackgroundStyle = 0x7f130322;
        public static int antelopSecureVirtualCardNumberDisplayCardBackground = 0x7f130323;
        public static int antelopSecureVirtualCardNumberDisplayCloseButton = 0x7f130324;
        public static int antelopSecureVirtualCardNumberDisplayCopyPanButton = 0x7f130325;
        public static int antelopSecureVirtualCardNumberDisplayCvx2TextView = 0x7f130326;
        public static int antelopSecureVirtualCardNumberDisplayExpiryDateTextView = 0x7f130327;
        public static int antelopSecureVirtualCardNumberDisplayPanTextView = 0x7f130328;
        public static int antelopSecureVirtualCardNumberDisplayThemeInternal = 0x7f130329;
        public static int antelopSecureVirtualCardNumberDisplayTransparentThemeInternal = 0x7f13032a;

        private style() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\fr\antelop\sdk\R$styleable.smali */
    public static final class styleable {
        public static int ActionBarLayout_android_layout_gravity = 0x00000000;
        public static int ActionBar_background = 0x00000000;
        public static int ActionBar_backgroundSplit = 0x00000001;
        public static int ActionBar_backgroundStacked = 0x00000002;
        public static int ActionBar_contentInsetEnd = 0x00000003;
        public static int ActionBar_contentInsetEndWithActions = 0x00000004;
        public static int ActionBar_contentInsetLeft = 0x00000005;
        public static int ActionBar_contentInsetRight = 0x00000006;
        public static int ActionBar_contentInsetStart = 0x00000007;
        public static int ActionBar_contentInsetStartWithNavigation = 0x00000008;
        public static int ActionBar_customNavigationLayout = 0x00000009;
        public static int ActionBar_displayOptions = 0x0000000a;
        public static int ActionBar_divider = 0x0000000b;
        public static int ActionBar_elevation = 0x0000000c;
        public static int ActionBar_height = 0x0000000d;
        public static int ActionBar_hideOnContentScroll = 0x0000000e;
        public static int ActionBar_homeAsUpIndicator = 0x0000000f;
        public static int ActionBar_homeLayout = 0x00000010;
        public static int ActionBar_icon = 0x00000011;
        public static int ActionBar_indeterminateProgressStyle = 0x00000012;
        public static int ActionBar_itemPadding = 0x00000013;
        public static int ActionBar_logo = 0x00000014;
        public static int ActionBar_navigationMode = 0x00000015;
        public static int ActionBar_popupTheme = 0x00000016;
        public static int ActionBar_progressBarPadding = 0x00000017;
        public static int ActionBar_progressBarStyle = 0x00000018;
        public static int ActionBar_subtitle = 0x00000019;
        public static int ActionBar_subtitleTextStyle = 0x0000001a;
        public static int ActionBar_title = 0x0000001b;
        public static int ActionBar_titleTextStyle = 0x0000001c;
        public static int ActionMenuItemView_android_minWidth = 0x00000000;
        public static int ActionMode_background = 0x00000000;
        public static int ActionMode_backgroundSplit = 0x00000001;
        public static int ActionMode_closeItemLayout = 0x00000002;
        public static int ActionMode_height = 0x00000003;
        public static int ActionMode_subtitleTextStyle = 0x00000004;
        public static int ActionMode_titleTextStyle = 0x00000005;
        public static int ActivityChooserView_expandActivityOverflowButtonDrawable = 0x00000000;
        public static int ActivityChooserView_initialActivityCount = 0x00000001;
        public static int AlertDialog_android_layout = 0x00000000;
        public static int AlertDialog_buttonIconDimen = 0x00000001;
        public static int AlertDialog_buttonPanelSideLayout = 0x00000002;
        public static int AlertDialog_listItemLayout = 0x00000003;
        public static int AlertDialog_listLayout = 0x00000004;
        public static int AlertDialog_multiChoiceItemLayout = 0x00000005;
        public static int AlertDialog_showTitle = 0x00000006;
        public static int AlertDialog_singleChoiceItemLayout = 0x00000007;
        public static int AnimatedStateListDrawableCompat_android_constantSize = 0x00000003;
        public static int AnimatedStateListDrawableCompat_android_dither = 0x00000000;
        public static int AnimatedStateListDrawableCompat_android_enterFadeDuration = 0x00000004;
        public static int AnimatedStateListDrawableCompat_android_exitFadeDuration = 0x00000005;
        public static int AnimatedStateListDrawableCompat_android_variablePadding = 0x00000002;
        public static int AnimatedStateListDrawableCompat_android_visible = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_drawable = 0x00000001;
        public static int AnimatedStateListDrawableItem_android_id = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_drawable = 0x00000000;
        public static int AnimatedStateListDrawableTransition_android_fromId = 0x00000002;
        public static int AnimatedStateListDrawableTransition_android_reversible = 0x00000003;
        public static int AnimatedStateListDrawableTransition_android_toId = 0x00000001;
        public static int AntelopKeypadView_antelopKeypadViewBulletDrawable = 0x00000000;
        public static int AntelopKeypadView_antelopKeypadViewColorPrimary = 0x00000001;
        public static int AntelopKeypadView_antelopKeypadViewColorSecondary = 0x00000002;
        public static int AntelopKeypadView_antelopKeypadViewDeleteDrawable = 0x00000003;
        public static int AntelopKeypadView_antelopKeypadViewEnableOverlayProtection = 0x00000004;
        public static int AntelopKeypadView_antelopKeypadViewExtraDrawable = 0x00000005;
        public static int AntelopKeypadView_antelopKeypadViewOverlayWarningMessage = 0x00000006;
        public static int AntelopKeypadView_antelopKeypadViewPinLength = 0x00000007;
        public static int AntelopKeypadView_antelopKeypadViewRandomizeKeyboard = 0x00000008;
        public static int AppBarLayoutStates_state_collapsed = 0x00000000;
        public static int AppBarLayoutStates_state_collapsible = 0x00000001;
        public static int AppBarLayoutStates_state_liftable = 0x00000002;
        public static int AppBarLayoutStates_state_lifted = 0x00000003;
        public static int AppBarLayout_Layout_layout_scrollFlags = 0x00000000;
        public static int AppBarLayout_Layout_layout_scrollInterpolator = 0x00000001;
        public static int AppBarLayout_android_background = 0x00000000;
        public static int AppBarLayout_android_keyboardNavigationCluster = 0x00000002;
        public static int AppBarLayout_android_touchscreenBlocksFocus = 0x00000001;
        public static int AppBarLayout_elevation = 0x00000003;
        public static int AppBarLayout_expanded = 0x00000004;
        public static int AppBarLayout_liftOnScroll = 0x00000005;
        public static int AppBarLayout_liftOnScrollTargetViewId = 0x00000006;
        public static int AppBarLayout_statusBarForeground = 0x00000007;
        public static int AppCompatImageView_android_src = 0x00000000;
        public static int AppCompatImageView_srcCompat = 0x00000001;
        public static int AppCompatImageView_tint = 0x00000002;
        public static int AppCompatImageView_tintMode = 0x00000003;
        public static int AppCompatSeekBar_android_thumb = 0x00000000;
        public static int AppCompatSeekBar_tickMark = 0x00000001;
        public static int AppCompatSeekBar_tickMarkTint = 0x00000002;
        public static int AppCompatSeekBar_tickMarkTintMode = 0x00000003;
        public static int AppCompatTextHelper_android_drawableBottom = 0x00000002;
        public static int AppCompatTextHelper_android_drawableEnd = 0x00000006;
        public static int AppCompatTextHelper_android_drawableLeft = 0x00000003;
        public static int AppCompatTextHelper_android_drawableRight = 0x00000004;
        public static int AppCompatTextHelper_android_drawableStart = 0x00000005;
        public static int AppCompatTextHelper_android_drawableTop = 0x00000001;
        public static int AppCompatTextHelper_android_textAppearance = 0x00000000;
        public static int AppCompatTextView_android_textAppearance = 0x00000000;
        public static int AppCompatTextView_autoSizeMaxTextSize = 0x00000001;
        public static int AppCompatTextView_autoSizeMinTextSize = 0x00000002;
        public static int AppCompatTextView_autoSizePresetSizes = 0x00000003;
        public static int AppCompatTextView_autoSizeStepGranularity = 0x00000004;
        public static int AppCompatTextView_autoSizeTextType = 0x00000005;
        public static int AppCompatTextView_drawableBottomCompat = 0x00000006;
        public static int AppCompatTextView_drawableEndCompat = 0x00000007;
        public static int AppCompatTextView_drawableLeftCompat = 0x00000008;
        public static int AppCompatTextView_drawableRightCompat = 0x00000009;
        public static int AppCompatTextView_drawableStartCompat = 0x0000000a;
        public static int AppCompatTextView_drawableTint = 0x0000000b;
        public static int AppCompatTextView_drawableTintMode = 0x0000000c;
        public static int AppCompatTextView_drawableTopCompat = 0x0000000d;
        public static int AppCompatTextView_emojiCompatEnabled = 0x0000000e;
        public static int AppCompatTextView_firstBaselineToTopHeight = 0x0000000f;
        public static int AppCompatTextView_fontFamily = 0x00000010;
        public static int AppCompatTextView_fontVariationSettings = 0x00000011;
        public static int AppCompatTextView_lastBaselineToBottomHeight = 0x00000012;
        public static int AppCompatTextView_lineHeight = 0x00000013;
        public static int AppCompatTextView_textAllCaps = 0x00000014;
        public static int AppCompatTextView_textLocale = 0x00000015;
        public static int AppCompatTheme_actionBarDivider = 0x00000002;
        public static int AppCompatTheme_actionBarItemBackground = 0x00000003;
        public static int AppCompatTheme_actionBarPopupTheme = 0x00000004;
        public static int AppCompatTheme_actionBarSize = 0x00000005;
        public static int AppCompatTheme_actionBarSplitStyle = 0x00000006;
        public static int AppCompatTheme_actionBarStyle = 0x00000007;
        public static int AppCompatTheme_actionBarTabBarStyle = 0x00000008;
        public static int AppCompatTheme_actionBarTabStyle = 0x00000009;
        public static int AppCompatTheme_actionBarTabTextStyle = 0x0000000a;
        public static int AppCompatTheme_actionBarTheme = 0x0000000b;
        public static int AppCompatTheme_actionBarWidgetTheme = 0x0000000c;
        public static int AppCompatTheme_actionButtonStyle = 0x0000000d;
        public static int AppCompatTheme_actionDropDownStyle = 0x0000000e;
        public static int AppCompatTheme_actionMenuTextAppearance = 0x0000000f;
        public static int AppCompatTheme_actionMenuTextColor = 0x00000010;
        public static int AppCompatTheme_actionModeBackground = 0x00000011;
        public static int AppCompatTheme_actionModeCloseButtonStyle = 0x00000012;
        public static int AppCompatTheme_actionModeCloseContentDescription = 0x00000013;
        public static int AppCompatTheme_actionModeCloseDrawable = 0x00000014;
        public static int AppCompatTheme_actionModeCopyDrawable = 0x00000015;
        public static int AppCompatTheme_actionModeCutDrawable = 0x00000016;
        public static int AppCompatTheme_actionModeFindDrawable = 0x00000017;
        public static int AppCompatTheme_actionModePasteDrawable = 0x00000018;
        public static int AppCompatTheme_actionModePopupWindowStyle = 0x00000019;
        public static int AppCompatTheme_actionModeSelectAllDrawable = 0x0000001a;
        public static int AppCompatTheme_actionModeShareDrawable = 0x0000001b;
        public static int AppCompatTheme_actionModeSplitBackground = 0x0000001c;
        public static int AppCompatTheme_actionModeStyle = 0x0000001d;
        public static int AppCompatTheme_actionModeTheme = 0x0000001e;
        public static int AppCompatTheme_actionModeWebSearchDrawable = 0x0000001f;
        public static int AppCompatTheme_actionOverflowButtonStyle = 0x00000020;
        public static int AppCompatTheme_actionOverflowMenuStyle = 0x00000021;
        public static int AppCompatTheme_activityChooserViewStyle = 0x00000022;
        public static int AppCompatTheme_alertDialogButtonGroupStyle = 0x00000023;
        public static int AppCompatTheme_alertDialogCenterButtons = 0x00000024;
        public static int AppCompatTheme_alertDialogStyle = 0x00000025;
        public static int AppCompatTheme_alertDialogTheme = 0x00000026;
        public static int AppCompatTheme_android_windowAnimationStyle = 0x00000001;
        public static int AppCompatTheme_android_windowIsFloating = 0x00000000;
        public static int AppCompatTheme_autoCompleteTextViewStyle = 0x00000027;
        public static int AppCompatTheme_borderlessButtonStyle = 0x00000028;
        public static int AppCompatTheme_buttonBarButtonStyle = 0x00000029;
        public static int AppCompatTheme_buttonBarNegativeButtonStyle = 0x0000002a;
        public static int AppCompatTheme_buttonBarNeutralButtonStyle = 0x0000002b;
        public static int AppCompatTheme_buttonBarPositiveButtonStyle = 0x0000002c;
        public static int AppCompatTheme_buttonBarStyle = 0x0000002d;
        public static int AppCompatTheme_buttonStyle = 0x0000002e;
        public static int AppCompatTheme_buttonStyleSmall = 0x0000002f;
        public static int AppCompatTheme_checkboxStyle = 0x00000030;
        public static int AppCompatTheme_checkedTextViewStyle = 0x00000031;
        public static int AppCompatTheme_colorAccent = 0x00000032;
        public static int AppCompatTheme_colorBackgroundFloating = 0x00000033;
        public static int AppCompatTheme_colorButtonNormal = 0x00000034;
        public static int AppCompatTheme_colorControlActivated = 0x00000035;
        public static int AppCompatTheme_colorControlHighlight = 0x00000036;
        public static int AppCompatTheme_colorControlNormal = 0x00000037;
        public static int AppCompatTheme_colorError = 0x00000038;
        public static int AppCompatTheme_colorPrimary = 0x00000039;
        public static int AppCompatTheme_colorPrimaryDark = 0x0000003a;
        public static int AppCompatTheme_colorSwitchThumbNormal = 0x0000003b;
        public static int AppCompatTheme_controlBackground = 0x0000003c;
        public static int AppCompatTheme_dialogCornerRadius = 0x0000003d;
        public static int AppCompatTheme_dialogPreferredPadding = 0x0000003e;
        public static int AppCompatTheme_dialogTheme = 0x0000003f;
        public static int AppCompatTheme_dividerHorizontal = 0x00000040;
        public static int AppCompatTheme_dividerVertical = 0x00000041;
        public static int AppCompatTheme_dropDownListViewStyle = 0x00000042;
        public static int AppCompatTheme_dropdownListPreferredItemHeight = 0x00000043;
        public static int AppCompatTheme_editTextBackground = 0x00000044;
        public static int AppCompatTheme_editTextColor = 0x00000045;
        public static int AppCompatTheme_editTextStyle = 0x00000046;
        public static int AppCompatTheme_homeAsUpIndicator = 0x00000047;
        public static int AppCompatTheme_imageButtonStyle = 0x00000048;
        public static int AppCompatTheme_listChoiceBackgroundIndicator = 0x00000049;
        public static int AppCompatTheme_listChoiceIndicatorMultipleAnimated = 0x0000004a;
        public static int AppCompatTheme_listChoiceIndicatorSingleAnimated = 0x0000004b;
        public static int AppCompatTheme_listDividerAlertDialog = 0x0000004c;
        public static int AppCompatTheme_listMenuViewStyle = 0x0000004d;
        public static int AppCompatTheme_listPopupWindowStyle = 0x0000004e;
        public static int AppCompatTheme_listPreferredItemHeight = 0x0000004f;
        public static int AppCompatTheme_listPreferredItemHeightLarge = 0x00000050;
        public static int AppCompatTheme_listPreferredItemHeightSmall = 0x00000051;
        public static int AppCompatTheme_listPreferredItemPaddingEnd = 0x00000052;
        public static int AppCompatTheme_listPreferredItemPaddingLeft = 0x00000053;
        public static int AppCompatTheme_listPreferredItemPaddingRight = 0x00000054;
        public static int AppCompatTheme_listPreferredItemPaddingStart = 0x00000055;
        public static int AppCompatTheme_panelBackground = 0x00000056;
        public static int AppCompatTheme_panelMenuListTheme = 0x00000057;
        public static int AppCompatTheme_panelMenuListWidth = 0x00000058;
        public static int AppCompatTheme_popupMenuStyle = 0x00000059;
        public static int AppCompatTheme_popupWindowStyle = 0x0000005a;
        public static int AppCompatTheme_radioButtonStyle = 0x0000005b;
        public static int AppCompatTheme_ratingBarStyle = 0x0000005c;
        public static int AppCompatTheme_ratingBarStyleIndicator = 0x0000005d;
        public static int AppCompatTheme_ratingBarStyleSmall = 0x0000005e;
        public static int AppCompatTheme_searchViewStyle = 0x0000005f;
        public static int AppCompatTheme_seekBarStyle = 0x00000060;
        public static int AppCompatTheme_selectableItemBackground = 0x00000061;
        public static int AppCompatTheme_selectableItemBackgroundBorderless = 0x00000062;
        public static int AppCompatTheme_spinnerDropDownItemStyle = 0x00000063;
        public static int AppCompatTheme_spinnerStyle = 0x00000064;
        public static int AppCompatTheme_switchStyle = 0x00000065;
        public static int AppCompatTheme_textAppearanceLargePopupMenu = 0x00000066;
        public static int AppCompatTheme_textAppearanceListItem = 0x00000067;
        public static int AppCompatTheme_textAppearanceListItemSecondary = 0x00000068;
        public static int AppCompatTheme_textAppearanceListItemSmall = 0x00000069;
        public static int AppCompatTheme_textAppearancePopupMenuHeader = 0x0000006a;
        public static int AppCompatTheme_textAppearanceSearchResultSubtitle = 0x0000006b;
        public static int AppCompatTheme_textAppearanceSearchResultTitle = 0x0000006c;
        public static int AppCompatTheme_textAppearanceSmallPopupMenu = 0x0000006d;
        public static int AppCompatTheme_textColorAlertDialogListItem = 0x0000006e;
        public static int AppCompatTheme_textColorSearchUrl = 0x0000006f;
        public static int AppCompatTheme_toolbarNavigationButtonStyle = 0x00000070;
        public static int AppCompatTheme_toolbarStyle = 0x00000071;
        public static int AppCompatTheme_tooltipForegroundColor = 0x00000072;
        public static int AppCompatTheme_tooltipFrameBackground = 0x00000073;
        public static int AppCompatTheme_viewInflaterClass = 0x00000074;
        public static int AppCompatTheme_windowActionBar = 0x00000075;
        public static int AppCompatTheme_windowActionBarOverlay = 0x00000076;
        public static int AppCompatTheme_windowActionModeOverlay = 0x00000077;
        public static int AppCompatTheme_windowFixedHeightMajor = 0x00000078;
        public static int AppCompatTheme_windowFixedHeightMinor = 0x00000079;
        public static int AppCompatTheme_windowFixedWidthMajor = 0x0000007a;
        public static int AppCompatTheme_windowFixedWidthMinor = 0x0000007b;
        public static int AppCompatTheme_windowMinWidthMajor = 0x0000007c;
        public static int AppCompatTheme_windowMinWidthMinor = 0x0000007d;
        public static int AppCompatTheme_windowNoTitle = 0x0000007e;
        public static int Badge_backgroundColor = 0x00000000;
        public static int Badge_badgeGravity = 0x00000001;
        public static int Badge_badgeTextColor = 0x00000002;
        public static int Badge_maxCharacterCount = 0x00000003;
        public static int Badge_number = 0x00000004;
        public static int BottomAppBar_backgroundTint = 0x00000000;
        public static int BottomAppBar_elevation = 0x00000001;
        public static int BottomAppBar_fabAlignmentMode = 0x00000002;
        public static int BottomAppBar_fabAnimationMode = 0x00000003;
        public static int BottomAppBar_fabCradleMargin = 0x00000004;
        public static int BottomAppBar_fabCradleRoundedCornerRadius = 0x00000005;
        public static int BottomAppBar_fabCradleVerticalOffset = 0x00000006;
        public static int BottomAppBar_hideOnScroll = 0x00000007;
        public static int BottomNavigationView_backgroundTint = 0x00000000;
        public static int BottomNavigationView_elevation = 0x00000001;
        public static int BottomNavigationView_itemBackground = 0x00000002;
        public static int BottomNavigationView_itemHorizontalTranslationEnabled = 0x00000003;
        public static int BottomNavigationView_itemIconSize = 0x00000004;
        public static int BottomNavigationView_itemIconTint = 0x00000005;
        public static int BottomNavigationView_itemRippleColor = 0x00000006;
        public static int BottomNavigationView_itemTextAppearanceActive = 0x00000007;
        public static int BottomNavigationView_itemTextAppearanceInactive = 0x00000008;
        public static int BottomNavigationView_itemTextColor = 0x00000009;
        public static int BottomNavigationView_labelVisibilityMode = 0x0000000a;
        public static int BottomNavigationView_menu = 0x0000000b;
        public static int BottomSheetBehavior_Layout_android_elevation = 0x00000000;
        public static int BottomSheetBehavior_Layout_backgroundTint = 0x00000001;
        public static int BottomSheetBehavior_Layout_behavior_expandedOffset = 0x00000002;
        public static int BottomSheetBehavior_Layout_behavior_fitToContents = 0x00000003;
        public static int BottomSheetBehavior_Layout_behavior_halfExpandedRatio = 0x00000004;
        public static int BottomSheetBehavior_Layout_behavior_hideable = 0x00000005;
        public static int BottomSheetBehavior_Layout_behavior_peekHeight = 0x00000006;
        public static int BottomSheetBehavior_Layout_behavior_saveFlags = 0x00000007;
        public static int BottomSheetBehavior_Layout_behavior_skipCollapsed = 0x00000008;
        public static int BottomSheetBehavior_Layout_shapeAppearance = 0x00000009;
        public static int BottomSheetBehavior_Layout_shapeAppearanceOverlay = 0x0000000a;
        public static int ButtonBarLayout_allowStacking = 0x00000000;
        public static int Capability_queryPatterns = 0x00000000;
        public static int Capability_shortcutMatchRequired = 0x00000001;
        public static int CardView_android_minHeight = 0x00000001;
        public static int CardView_android_minWidth = 0x00000000;
        public static int CardView_cardBackgroundColor = 0x00000002;
        public static int CardView_cardCornerRadius = 0x00000003;
        public static int CardView_cardElevation = 0x00000004;
        public static int CardView_cardMaxElevation = 0x00000005;
        public static int CardView_cardPreventCornerOverlap = 0x00000006;
        public static int CardView_cardUseCompatPadding = 0x00000007;
        public static int CardView_contentPadding = 0x00000008;
        public static int CardView_contentPaddingBottom = 0x00000009;
        public static int CardView_contentPaddingLeft = 0x0000000a;
        public static int CardView_contentPaddingRight = 0x0000000b;
        public static int CardView_contentPaddingTop = 0x0000000c;
        public static int CheckedTextView_android_checkMark = 0x00000000;
        public static int CheckedTextView_checkMarkCompat = 0x00000001;
        public static int CheckedTextView_checkMarkTint = 0x00000002;
        public static int CheckedTextView_checkMarkTintMode = 0x00000003;
        public static int ChipGroup_checkedChip = 0x00000000;
        public static int ChipGroup_chipSpacing = 0x00000001;
        public static int ChipGroup_chipSpacingHorizontal = 0x00000002;
        public static int ChipGroup_chipSpacingVertical = 0x00000003;
        public static int ChipGroup_singleLine = 0x00000004;
        public static int ChipGroup_singleSelection = 0x00000005;
        public static int Chip_android_checkable = 0x00000005;
        public static int Chip_android_ellipsize = 0x00000002;
        public static int Chip_android_maxWidth = 0x00000003;
        public static int Chip_android_text = 0x00000004;
        public static int Chip_android_textAppearance = 0x00000000;
        public static int Chip_android_textColor = 0x00000001;
        public static int Chip_checkedIcon = 0x00000006;
        public static int Chip_checkedIconEnabled = 0x00000007;
        public static int Chip_checkedIconVisible = 0x00000008;
        public static int Chip_chipBackgroundColor = 0x00000009;
        public static int Chip_chipCornerRadius = 0x0000000a;
        public static int Chip_chipEndPadding = 0x0000000b;
        public static int Chip_chipIcon = 0x0000000c;
        public static int Chip_chipIconEnabled = 0x0000000d;
        public static int Chip_chipIconSize = 0x0000000e;
        public static int Chip_chipIconTint = 0x0000000f;
        public static int Chip_chipIconVisible = 0x00000010;
        public static int Chip_chipMinHeight = 0x00000011;
        public static int Chip_chipMinTouchTargetSize = 0x00000012;
        public static int Chip_chipStartPadding = 0x00000013;
        public static int Chip_chipStrokeColor = 0x00000014;
        public static int Chip_chipStrokeWidth = 0x00000015;
        public static int Chip_chipSurfaceColor = 0x00000016;
        public static int Chip_closeIcon = 0x00000017;
        public static int Chip_closeIconEnabled = 0x00000018;
        public static int Chip_closeIconEndPadding = 0x00000019;
        public static int Chip_closeIconSize = 0x0000001a;
        public static int Chip_closeIconStartPadding = 0x0000001b;
        public static int Chip_closeIconTint = 0x0000001c;
        public static int Chip_closeIconVisible = 0x0000001d;
        public static int Chip_ensureMinTouchTargetSize = 0x0000001e;
        public static int Chip_hideMotionSpec = 0x0000001f;
        public static int Chip_iconEndPadding = 0x00000020;
        public static int Chip_iconStartPadding = 0x00000021;
        public static int Chip_rippleColor = 0x00000022;
        public static int Chip_shapeAppearance = 0x00000023;
        public static int Chip_shapeAppearanceOverlay = 0x00000024;
        public static int Chip_showMotionSpec = 0x00000025;
        public static int Chip_textEndPadding = 0x00000026;
        public static int Chip_textStartPadding = 0x00000027;
        public static int CollapsingToolbarLayout_Layout_layout_collapseMode = 0x00000000;
        public static int CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier = 0x00000001;
        public static int CollapsingToolbarLayout_collapsedTitleGravity = 0x00000000;
        public static int CollapsingToolbarLayout_collapsedTitleTextAppearance = 0x00000001;
        public static int CollapsingToolbarLayout_contentScrim = 0x00000002;
        public static int CollapsingToolbarLayout_expandedTitleGravity = 0x00000003;
        public static int CollapsingToolbarLayout_expandedTitleMargin = 0x00000004;
        public static int CollapsingToolbarLayout_expandedTitleMarginBottom = 0x00000005;
        public static int CollapsingToolbarLayout_expandedTitleMarginEnd = 0x00000006;
        public static int CollapsingToolbarLayout_expandedTitleMarginStart = 0x00000007;
        public static int CollapsingToolbarLayout_expandedTitleMarginTop = 0x00000008;
        public static int CollapsingToolbarLayout_expandedTitleTextAppearance = 0x00000009;
        public static int CollapsingToolbarLayout_scrimAnimationDuration = 0x0000000a;
        public static int CollapsingToolbarLayout_scrimVisibleHeightTrigger = 0x0000000b;
        public static int CollapsingToolbarLayout_statusBarScrim = 0x0000000c;
        public static int CollapsingToolbarLayout_title = 0x0000000d;
        public static int CollapsingToolbarLayout_titleEnabled = 0x0000000e;
        public static int CollapsingToolbarLayout_toolbarId = 0x0000000f;
        public static int ColorStateListItem_alpha = 0x00000003;
        public static int ColorStateListItem_android_alpha = 0x00000001;
        public static int ColorStateListItem_android_color = 0x00000000;
        public static int ColorStateListItem_android_lStar = 0x00000002;
        public static int ColorStateListItem_lStar = 0x00000004;
        public static int CompoundButton_android_button = 0x00000000;
        public static int CompoundButton_buttonCompat = 0x00000001;
        public static int CompoundButton_buttonTint = 0x00000002;
        public static int CompoundButton_buttonTintMode = 0x00000003;
        public static int ConstraintLayout_Layout_android_maxHeight = 0x00000002;
        public static int ConstraintLayout_Layout_android_maxWidth = 0x00000001;
        public static int ConstraintLayout_Layout_android_minHeight = 0x00000004;
        public static int ConstraintLayout_Layout_android_minWidth = 0x00000003;
        public static int ConstraintLayout_Layout_android_orientation = 0x00000000;
        public static int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 0x00000005;
        public static int ConstraintLayout_Layout_barrierDirection = 0x00000006;
        public static int ConstraintLayout_Layout_chainUseRtl = 0x00000007;
        public static int ConstraintLayout_Layout_constraintSet = 0x00000008;
        public static int ConstraintLayout_Layout_constraint_referenced_ids = 0x00000009;
        public static int ConstraintLayout_Layout_layout_constrainedHeight = 0x0000000a;
        public static int ConstraintLayout_Layout_layout_constrainedWidth = 0x0000000b;
        public static int ConstraintLayout_Layout_layout_constraintBaseline_creator = 0x0000000c;
        public static int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 0x0000000d;
        public static int ConstraintLayout_Layout_layout_constraintBottom_creator = 0x0000000e;
        public static int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 0x0000000f;
        public static int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 0x00000010;
        public static int ConstraintLayout_Layout_layout_constraintCircle = 0x00000011;
        public static int ConstraintLayout_Layout_layout_constraintCircleAngle = 0x00000012;
        public static int ConstraintLayout_Layout_layout_constraintCircleRadius = 0x00000013;
        public static int ConstraintLayout_Layout_layout_constraintDimensionRatio = 0x00000014;
        public static int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 0x00000015;
        public static int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 0x00000016;
        public static int ConstraintLayout_Layout_layout_constraintGuide_begin = 0x00000017;
        public static int ConstraintLayout_Layout_layout_constraintGuide_end = 0x00000018;
        public static int ConstraintLayout_Layout_layout_constraintGuide_percent = 0x00000019;
        public static int ConstraintLayout_Layout_layout_constraintHeight_default = 0x0000001a;
        public static int ConstraintLayout_Layout_layout_constraintHeight_max = 0x0000001b;
        public static int ConstraintLayout_Layout_layout_constraintHeight_min = 0x0000001c;
        public static int ConstraintLayout_Layout_layout_constraintHeight_percent = 0x0000001d;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 0x0000001e;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 0x0000001f;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 0x00000020;
        public static int ConstraintLayout_Layout_layout_constraintLeft_creator = 0x00000021;
        public static int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 0x00000022;
        public static int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 0x00000023;
        public static int ConstraintLayout_Layout_layout_constraintRight_creator = 0x00000024;
        public static int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 0x00000025;
        public static int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 0x00000026;
        public static int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 0x00000027;
        public static int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 0x00000028;
        public static int ConstraintLayout_Layout_layout_constraintTop_creator = 0x00000029;
        public static int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 0x0000002a;
        public static int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 0x0000002b;
        public static int ConstraintLayout_Layout_layout_constraintVertical_bias = 0x0000002c;
        public static int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 0x0000002d;
        public static int ConstraintLayout_Layout_layout_constraintVertical_weight = 0x0000002e;
        public static int ConstraintLayout_Layout_layout_constraintWidth_default = 0x0000002f;
        public static int ConstraintLayout_Layout_layout_constraintWidth_max = 0x00000030;
        public static int ConstraintLayout_Layout_layout_constraintWidth_min = 0x00000031;
        public static int ConstraintLayout_Layout_layout_constraintWidth_percent = 0x00000032;
        public static int ConstraintLayout_Layout_layout_editor_absoluteX = 0x00000033;
        public static int ConstraintLayout_Layout_layout_editor_absoluteY = 0x00000034;
        public static int ConstraintLayout_Layout_layout_goneMarginBottom = 0x00000035;
        public static int ConstraintLayout_Layout_layout_goneMarginEnd = 0x00000036;
        public static int ConstraintLayout_Layout_layout_goneMarginLeft = 0x00000037;
        public static int ConstraintLayout_Layout_layout_goneMarginRight = 0x00000038;
        public static int ConstraintLayout_Layout_layout_goneMarginStart = 0x00000039;
        public static int ConstraintLayout_Layout_layout_goneMarginTop = 0x0000003a;
        public static int ConstraintLayout_Layout_layout_optimizationLevel = 0x0000003b;
        public static int ConstraintLayout_placeholder_content = 0x00000000;
        public static int ConstraintLayout_placeholder_emptyVisibility = 0x00000001;
        public static int ConstraintSet_android_alpha = 0x0000000d;
        public static int ConstraintSet_android_elevation = 0x0000001a;
        public static int ConstraintSet_android_id = 0x00000001;
        public static int ConstraintSet_android_layout_height = 0x00000004;
        public static int ConstraintSet_android_layout_marginBottom = 0x00000008;
        public static int ConstraintSet_android_layout_marginEnd = 0x00000018;
        public static int ConstraintSet_android_layout_marginLeft = 0x00000005;
        public static int ConstraintSet_android_layout_marginRight = 0x00000007;
        public static int ConstraintSet_android_layout_marginStart = 0x00000017;
        public static int ConstraintSet_android_layout_marginTop = 0x00000006;
        public static int ConstraintSet_android_layout_width = 0x00000003;
        public static int ConstraintSet_android_maxHeight = 0x0000000a;
        public static int ConstraintSet_android_maxWidth = 0x00000009;
        public static int ConstraintSet_android_minHeight = 0x0000000c;
        public static int ConstraintSet_android_minWidth = 0x0000000b;
        public static int ConstraintSet_android_orientation = 0x00000000;
        public static int ConstraintSet_android_rotation = 0x00000014;
        public static int ConstraintSet_android_rotationX = 0x00000015;
        public static int ConstraintSet_android_rotationY = 0x00000016;
        public static int ConstraintSet_android_scaleX = 0x00000012;
        public static int ConstraintSet_android_scaleY = 0x00000013;
        public static int ConstraintSet_android_transformPivotX = 0x0000000e;
        public static int ConstraintSet_android_transformPivotY = 0x0000000f;
        public static int ConstraintSet_android_translationX = 0x00000010;
        public static int ConstraintSet_android_translationY = 0x00000011;
        public static int ConstraintSet_android_translationZ = 0x00000019;
        public static int ConstraintSet_android_visibility = 0x00000002;
        public static int ConstraintSet_barrierAllowsGoneWidgets = 0x0000001b;
        public static int ConstraintSet_barrierDirection = 0x0000001c;
        public static int ConstraintSet_chainUseRtl = 0x0000001d;
        public static int ConstraintSet_constraint_referenced_ids = 0x0000001e;
        public static int ConstraintSet_layout_constrainedHeight = 0x0000001f;
        public static int ConstraintSet_layout_constrainedWidth = 0x00000020;
        public static int ConstraintSet_layout_constraintBaseline_creator = 0x00000021;
        public static int ConstraintSet_layout_constraintBaseline_toBaselineOf = 0x00000022;
        public static int ConstraintSet_layout_constraintBottom_creator = 0x00000023;
        public static int ConstraintSet_layout_constraintBottom_toBottomOf = 0x00000024;
        public static int ConstraintSet_layout_constraintBottom_toTopOf = 0x00000025;
        public static int ConstraintSet_layout_constraintCircle = 0x00000026;
        public static int ConstraintSet_layout_constraintCircleAngle = 0x00000027;
        public static int ConstraintSet_layout_constraintCircleRadius = 0x00000028;
        public static int ConstraintSet_layout_constraintDimensionRatio = 0x00000029;
        public static int ConstraintSet_layout_constraintEnd_toEndOf = 0x0000002a;
        public static int ConstraintSet_layout_constraintEnd_toStartOf = 0x0000002b;
        public static int ConstraintSet_layout_constraintGuide_begin = 0x0000002c;
        public static int ConstraintSet_layout_constraintGuide_end = 0x0000002d;
        public static int ConstraintSet_layout_constraintGuide_percent = 0x0000002e;
        public static int ConstraintSet_layout_constraintHeight_default = 0x0000002f;
        public static int ConstraintSet_layout_constraintHeight_max = 0x00000030;
        public static int ConstraintSet_layout_constraintHeight_min = 0x00000031;
        public static int ConstraintSet_layout_constraintHeight_percent = 0x00000032;
        public static int ConstraintSet_layout_constraintHorizontal_bias = 0x00000033;
        public static int ConstraintSet_layout_constraintHorizontal_chainStyle = 0x00000034;
        public static int ConstraintSet_layout_constraintHorizontal_weight = 0x00000035;
        public static int ConstraintSet_layout_constraintLeft_creator = 0x00000036;
        public static int ConstraintSet_layout_constraintLeft_toLeftOf = 0x00000037;
        public static int ConstraintSet_layout_constraintLeft_toRightOf = 0x00000038;
        public static int ConstraintSet_layout_constraintRight_creator = 0x00000039;
        public static int ConstraintSet_layout_constraintRight_toLeftOf = 0x0000003a;
        public static int ConstraintSet_layout_constraintRight_toRightOf = 0x0000003b;
        public static int ConstraintSet_layout_constraintStart_toEndOf = 0x0000003c;
        public static int ConstraintSet_layout_constraintStart_toStartOf = 0x0000003d;
        public static int ConstraintSet_layout_constraintTop_creator = 0x0000003e;
        public static int ConstraintSet_layout_constraintTop_toBottomOf = 0x0000003f;
        public static int ConstraintSet_layout_constraintTop_toTopOf = 0x00000040;
        public static int ConstraintSet_layout_constraintVertical_bias = 0x00000041;
        public static int ConstraintSet_layout_constraintVertical_chainStyle = 0x00000042;
        public static int ConstraintSet_layout_constraintVertical_weight = 0x00000043;
        public static int ConstraintSet_layout_constraintWidth_default = 0x00000044;
        public static int ConstraintSet_layout_constraintWidth_max = 0x00000045;
        public static int ConstraintSet_layout_constraintWidth_min = 0x00000046;
        public static int ConstraintSet_layout_constraintWidth_percent = 0x00000047;
        public static int ConstraintSet_layout_editor_absoluteX = 0x00000048;
        public static int ConstraintSet_layout_editor_absoluteY = 0x00000049;
        public static int ConstraintSet_layout_goneMarginBottom = 0x0000004a;
        public static int ConstraintSet_layout_goneMarginEnd = 0x0000004b;
        public static int ConstraintSet_layout_goneMarginLeft = 0x0000004c;
        public static int ConstraintSet_layout_goneMarginRight = 0x0000004d;
        public static int ConstraintSet_layout_goneMarginStart = 0x0000004e;
        public static int ConstraintSet_layout_goneMarginTop = 0x0000004f;
        public static int CoordinatorLayout_Layout_android_layout_gravity = 0x00000000;
        public static int CoordinatorLayout_Layout_layout_anchor = 0x00000001;
        public static int CoordinatorLayout_Layout_layout_anchorGravity = 0x00000002;
        public static int CoordinatorLayout_Layout_layout_behavior = 0x00000003;
        public static int CoordinatorLayout_Layout_layout_dodgeInsetEdges = 0x00000004;
        public static int CoordinatorLayout_Layout_layout_insetEdge = 0x00000005;
        public static int CoordinatorLayout_Layout_layout_keyline = 0x00000006;
        public static int CoordinatorLayout_keylines = 0x00000000;
        public static int CoordinatorLayout_statusBarBackground = 0x00000001;
        public static int DrawerArrowToggle_arrowHeadLength = 0x00000000;
        public static int DrawerArrowToggle_arrowShaftLength = 0x00000001;
        public static int DrawerArrowToggle_barLength = 0x00000002;
        public static int DrawerArrowToggle_color = 0x00000003;
        public static int DrawerArrowToggle_drawableSize = 0x00000004;
        public static int DrawerArrowToggle_gapBetweenBars = 0x00000005;
        public static int DrawerArrowToggle_spinBars = 0x00000006;
        public static int DrawerArrowToggle_thickness = 0x00000007;
        public static int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide = 0x00000000;
        public static int ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink = 0x00000001;
        public static int ExtendedFloatingActionButton_elevation = 0x00000000;
        public static int ExtendedFloatingActionButton_extendMotionSpec = 0x00000001;
        public static int ExtendedFloatingActionButton_hideMotionSpec = 0x00000002;
        public static int ExtendedFloatingActionButton_showMotionSpec = 0x00000003;
        public static int ExtendedFloatingActionButton_shrinkMotionSpec = 0x00000004;
        public static int FloatingActionButton_Behavior_Layout_behavior_autoHide = 0x00000000;
        public static int FloatingActionButton_backgroundTint = 0x00000000;
        public static int FloatingActionButton_backgroundTintMode = 0x00000001;
        public static int FloatingActionButton_borderWidth = 0x00000002;
        public static int FloatingActionButton_elevation = 0x00000003;
        public static int FloatingActionButton_ensureMinTouchTargetSize = 0x00000004;
        public static int FloatingActionButton_fabCustomSize = 0x00000005;
        public static int FloatingActionButton_fabSize = 0x00000006;
        public static int FloatingActionButton_hideMotionSpec = 0x00000007;
        public static int FloatingActionButton_hoveredFocusedTranslationZ = 0x00000008;
        public static int FloatingActionButton_maxImageSize = 0x00000009;
        public static int FloatingActionButton_pressedTranslationZ = 0x0000000a;
        public static int FloatingActionButton_rippleColor = 0x0000000b;
        public static int FloatingActionButton_shapeAppearance = 0x0000000c;
        public static int FloatingActionButton_shapeAppearanceOverlay = 0x0000000d;
        public static int FloatingActionButton_showMotionSpec = 0x0000000e;
        public static int FloatingActionButton_useCompatPadding = 0x0000000f;
        public static int FlowLayout_itemSpacing = 0x00000000;
        public static int FlowLayout_lineSpacing = 0x00000001;
        public static int FontFamilyFont_android_font = 0x00000000;
        public static int FontFamilyFont_android_fontStyle = 0x00000002;
        public static int FontFamilyFont_android_fontVariationSettings = 0x00000004;
        public static int FontFamilyFont_android_fontWeight = 0x00000001;
        public static int FontFamilyFont_android_ttcIndex = 0x00000003;
        public static int FontFamilyFont_font = 0x00000005;
        public static int FontFamilyFont_fontStyle = 0x00000006;
        public static int FontFamilyFont_fontVariationSettings = 0x00000007;
        public static int FontFamilyFont_fontWeight = 0x00000008;
        public static int FontFamilyFont_ttcIndex = 0x00000009;
        public static int FontFamily_fontProviderAuthority = 0x00000000;
        public static int FontFamily_fontProviderCerts = 0x00000001;
        public static int FontFamily_fontProviderFetchStrategy = 0x00000002;
        public static int FontFamily_fontProviderFetchTimeout = 0x00000003;
        public static int FontFamily_fontProviderPackage = 0x00000004;
        public static int FontFamily_fontProviderQuery = 0x00000005;
        public static int FontFamily_fontProviderSystemFontFamily = 0x00000006;
        public static int ForegroundLinearLayout_android_foreground = 0x00000000;
        public static int ForegroundLinearLayout_android_foregroundGravity = 0x00000001;
        public static int ForegroundLinearLayout_foregroundInsidePadding = 0x00000002;
        public static int FragmentContainerView_android_name = 0x00000000;
        public static int FragmentContainerView_android_tag = 0x00000001;
        public static int Fragment_android_id = 0x00000001;
        public static int Fragment_android_name = 0x00000000;
        public static int Fragment_android_tag = 0x00000002;
        public static int GradientColorItem_android_color = 0x00000000;
        public static int GradientColorItem_android_offset = 0x00000001;
        public static int GradientColor_android_centerColor = 0x00000007;
        public static int GradientColor_android_centerX = 0x00000003;
        public static int GradientColor_android_centerY = 0x00000004;
        public static int GradientColor_android_endColor = 0x00000001;
        public static int GradientColor_android_endX = 0x0000000a;
        public static int GradientColor_android_endY = 0x0000000b;
        public static int GradientColor_android_gradientRadius = 0x00000005;
        public static int GradientColor_android_startColor = 0x00000000;
        public static int GradientColor_android_startX = 0x00000008;
        public static int GradientColor_android_startY = 0x00000009;
        public static int GradientColor_android_tileMode = 0x00000006;
        public static int GradientColor_android_type = 0x00000002;
        public static int LinearLayoutCompat_Layout_android_layout_gravity = 0x00000000;
        public static int LinearLayoutCompat_Layout_android_layout_height = 0x00000002;
        public static int LinearLayoutCompat_Layout_android_layout_weight = 0x00000003;
        public static int LinearLayoutCompat_Layout_android_layout_width = 0x00000001;
        public static int LinearLayoutCompat_android_baselineAligned = 0x00000002;
        public static int LinearLayoutCompat_android_baselineAlignedChildIndex = 0x00000003;
        public static int LinearLayoutCompat_android_gravity = 0x00000000;
        public static int LinearLayoutCompat_android_orientation = 0x00000001;
        public static int LinearLayoutCompat_android_weightSum = 0x00000004;
        public static int LinearLayoutCompat_divider = 0x00000005;
        public static int LinearLayoutCompat_dividerPadding = 0x00000006;
        public static int LinearLayoutCompat_measureWithLargestChild = 0x00000007;
        public static int LinearLayoutCompat_showDividers = 0x00000008;
        public static int ListPopupWindow_android_dropDownHorizontalOffset = 0x00000000;
        public static int ListPopupWindow_android_dropDownVerticalOffset = 0x00000001;
        public static int LoadingImageView_circleCrop = 0x00000000;
        public static int LoadingImageView_imageAspectRatio = 0x00000001;
        public static int LoadingImageView_imageAspectRatioAdjust = 0x00000002;
        public static int MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle = 0x00000000;
        public static int MaterialAlertDialogTheme_materialAlertDialogTheme = 0x00000001;
        public static int MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle = 0x00000002;
        public static int MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle = 0x00000003;
        public static int MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle = 0x00000004;
        public static int MaterialAlertDialog_backgroundInsetBottom = 0x00000000;
        public static int MaterialAlertDialog_backgroundInsetEnd = 0x00000001;
        public static int MaterialAlertDialog_backgroundInsetStart = 0x00000002;
        public static int MaterialAlertDialog_backgroundInsetTop = 0x00000003;
        public static int MaterialButtonToggleGroup_checkedButton = 0x00000000;
        public static int MaterialButtonToggleGroup_singleSelection = 0x00000001;
        public static int MaterialButton_android_checkable = 0x00000004;
        public static int MaterialButton_android_insetBottom = 0x00000003;
        public static int MaterialButton_android_insetLeft = 0x00000000;
        public static int MaterialButton_android_insetRight = 0x00000001;
        public static int MaterialButton_android_insetTop = 0x00000002;
        public static int MaterialButton_backgroundTint = 0x00000005;
        public static int MaterialButton_backgroundTintMode = 0x00000006;
        public static int MaterialButton_cornerRadius = 0x00000007;
        public static int MaterialButton_elevation = 0x00000008;
        public static int MaterialButton_icon = 0x00000009;
        public static int MaterialButton_iconGravity = 0x0000000a;
        public static int MaterialButton_iconPadding = 0x0000000b;
        public static int MaterialButton_iconSize = 0x0000000c;
        public static int MaterialButton_iconTint = 0x0000000d;
        public static int MaterialButton_iconTintMode = 0x0000000e;
        public static int MaterialButton_rippleColor = 0x0000000f;
        public static int MaterialButton_shapeAppearance = 0x00000010;
        public static int MaterialButton_shapeAppearanceOverlay = 0x00000011;
        public static int MaterialButton_strokeColor = 0x00000012;
        public static int MaterialButton_strokeWidth = 0x00000013;
        public static int MaterialCalendarItem_android_insetBottom = 0x00000003;
        public static int MaterialCalendarItem_android_insetLeft = 0x00000000;
        public static int MaterialCalendarItem_android_insetRight = 0x00000001;
        public static int MaterialCalendarItem_android_insetTop = 0x00000002;
        public static int MaterialCalendarItem_itemFillColor = 0x00000004;
        public static int MaterialCalendarItem_itemShapeAppearance = 0x00000005;
        public static int MaterialCalendarItem_itemShapeAppearanceOverlay = 0x00000006;
        public static int MaterialCalendarItem_itemStrokeColor = 0x00000007;
        public static int MaterialCalendarItem_itemStrokeWidth = 0x00000008;
        public static int MaterialCalendarItem_itemTextColor = 0x00000009;
        public static int MaterialCalendar_android_windowFullscreen = 0x00000000;
        public static int MaterialCalendar_dayInvalidStyle = 0x00000001;
        public static int MaterialCalendar_daySelectedStyle = 0x00000002;
        public static int MaterialCalendar_dayStyle = 0x00000003;
        public static int MaterialCalendar_dayTodayStyle = 0x00000004;
        public static int MaterialCalendar_rangeFillColor = 0x00000005;
        public static int MaterialCalendar_yearSelectedStyle = 0x00000006;
        public static int MaterialCalendar_yearStyle = 0x00000007;
        public static int MaterialCalendar_yearTodayStyle = 0x00000008;
        public static int MaterialCardView_android_checkable = 0x00000000;
        public static int MaterialCardView_cardForegroundColor = 0x00000001;
        public static int MaterialCardView_checkedIcon = 0x00000002;
        public static int MaterialCardView_checkedIconTint = 0x00000003;
        public static int MaterialCardView_rippleColor = 0x00000004;
        public static int MaterialCardView_shapeAppearance = 0x00000005;
        public static int MaterialCardView_shapeAppearanceOverlay = 0x00000006;
        public static int MaterialCardView_state_dragged = 0x00000007;
        public static int MaterialCardView_strokeColor = 0x00000008;
        public static int MaterialCardView_strokeWidth = 0x00000009;
        public static int MaterialCheckBox_buttonTint = 0x00000000;
        public static int MaterialCheckBox_useMaterialThemeColors = 0x00000001;
        public static int MaterialRadioButton_useMaterialThemeColors = 0x00000000;
        public static int MaterialShape_shapeAppearance = 0x00000000;
        public static int MaterialShape_shapeAppearanceOverlay = 0x00000001;
        public static int MaterialTextAppearance_android_lineHeight = 0x00000000;
        public static int MaterialTextAppearance_lineHeight = 0x00000001;
        public static int MaterialTextView_android_lineHeight = 0x00000001;
        public static int MaterialTextView_android_textAppearance = 0x00000000;
        public static int MaterialTextView_lineHeight = 0x00000002;
        public static int MenuGroup_android_checkableBehavior = 0x00000005;
        public static int MenuGroup_android_enabled = 0x00000000;
        public static int MenuGroup_android_id = 0x00000001;
        public static int MenuGroup_android_menuCategory = 0x00000003;
        public static int MenuGroup_android_orderInCategory = 0x00000004;
        public static int MenuGroup_android_visible = 0x00000002;
        public static int MenuItem_actionLayout = 0x0000000d;
        public static int MenuItem_actionProviderClass = 0x0000000e;
        public static int MenuItem_actionViewClass = 0x0000000f;
        public static int MenuItem_alphabeticModifiers = 0x00000010;
        public static int MenuItem_android_alphabeticShortcut = 0x00000009;
        public static int MenuItem_android_checkable = 0x0000000b;
        public static int MenuItem_android_checked = 0x00000003;
        public static int MenuItem_android_enabled = 0x00000001;
        public static int MenuItem_android_icon = 0x00000000;
        public static int MenuItem_android_id = 0x00000002;
        public static int MenuItem_android_menuCategory = 0x00000005;
        public static int MenuItem_android_numericShortcut = 0x0000000a;
        public static int MenuItem_android_onClick = 0x0000000c;
        public static int MenuItem_android_orderInCategory = 0x00000006;
        public static int MenuItem_android_title = 0x00000007;
        public static int MenuItem_android_titleCondensed = 0x00000008;
        public static int MenuItem_android_visible = 0x00000004;
        public static int MenuItem_contentDescription = 0x00000011;
        public static int MenuItem_iconTint = 0x00000012;
        public static int MenuItem_iconTintMode = 0x00000013;
        public static int MenuItem_numericModifiers = 0x00000014;
        public static int MenuItem_showAsAction = 0x00000015;
        public static int MenuItem_tooltipText = 0x00000016;
        public static int MenuView_android_headerBackground = 0x00000004;
        public static int MenuView_android_horizontalDivider = 0x00000002;
        public static int MenuView_android_itemBackground = 0x00000005;
        public static int MenuView_android_itemIconDisabledAlpha = 0x00000006;
        public static int MenuView_android_itemTextAppearance = 0x00000001;
        public static int MenuView_android_verticalDivider = 0x00000003;
        public static int MenuView_android_windowAnimationStyle = 0x00000000;
        public static int MenuView_preserveIconSpacing = 0x00000007;
        public static int MenuView_subMenuArrow = 0x00000008;
        public static int NavigationView_android_background = 0x00000000;
        public static int NavigationView_android_fitsSystemWindows = 0x00000001;
        public static int NavigationView_android_maxWidth = 0x00000002;
        public static int NavigationView_elevation = 0x00000003;
        public static int NavigationView_headerLayout = 0x00000004;
        public static int NavigationView_itemBackground = 0x00000005;
        public static int NavigationView_itemHorizontalPadding = 0x00000006;
        public static int NavigationView_itemIconPadding = 0x00000007;
        public static int NavigationView_itemIconSize = 0x00000008;
        public static int NavigationView_itemIconTint = 0x00000009;
        public static int NavigationView_itemMaxLines = 0x0000000a;
        public static int NavigationView_itemShapeAppearance = 0x0000000b;
        public static int NavigationView_itemShapeAppearanceOverlay = 0x0000000c;
        public static int NavigationView_itemShapeFillColor = 0x0000000d;
        public static int NavigationView_itemShapeInsetBottom = 0x0000000e;
        public static int NavigationView_itemShapeInsetEnd = 0x0000000f;
        public static int NavigationView_itemShapeInsetStart = 0x00000010;
        public static int NavigationView_itemShapeInsetTop = 0x00000011;
        public static int NavigationView_itemTextAppearance = 0x00000012;
        public static int NavigationView_itemTextColor = 0x00000013;
        public static int NavigationView_menu = 0x00000014;
        public static int PopupWindowBackgroundState_state_above_anchor = 0x00000000;
        public static int PopupWindow_android_popupAnimationStyle = 0x00000001;
        public static int PopupWindow_android_popupBackground = 0x00000000;
        public static int PopupWindow_overlapAnchor = 0x00000002;
        public static int RecycleListView_paddingBottomNoButtons = 0x00000000;
        public static int RecycleListView_paddingTopNoTitle = 0x00000001;
        public static int RecyclerView_android_clipToPadding = 0x00000001;
        public static int RecyclerView_android_descendantFocusability = 0x00000002;
        public static int RecyclerView_android_orientation = 0x00000000;
        public static int RecyclerView_fastScrollEnabled = 0x00000003;
        public static int RecyclerView_fastScrollHorizontalThumbDrawable = 0x00000004;
        public static int RecyclerView_fastScrollHorizontalTrackDrawable = 0x00000005;
        public static int RecyclerView_fastScrollVerticalThumbDrawable = 0x00000006;
        public static int RecyclerView_fastScrollVerticalTrackDrawable = 0x00000007;
        public static int RecyclerView_layoutManager = 0x00000008;
        public static int RecyclerView_reverseLayout = 0x00000009;
        public static int RecyclerView_spanCount = 0x0000000a;
        public static int RecyclerView_stackFromEnd = 0x0000000b;
        public static int ScrimInsetsFrameLayout_insetForeground = 0x00000000;
        public static int ScrollingViewBehavior_Layout_behavior_overlapTop = 0x00000000;
        public static int SearchView_android_focusable = 0x00000000;
        public static int SearchView_android_imeOptions = 0x00000003;
        public static int SearchView_android_inputType = 0x00000002;
        public static int SearchView_android_maxWidth = 0x00000001;
        public static int SearchView_closeIcon = 0x00000004;
        public static int SearchView_commitIcon = 0x00000005;
        public static int SearchView_defaultQueryHint = 0x00000006;
        public static int SearchView_goIcon = 0x00000007;
        public static int SearchView_iconifiedByDefault = 0x00000008;
        public static int SearchView_layout = 0x00000009;
        public static int SearchView_queryBackground = 0x0000000a;
        public static int SearchView_queryHint = 0x0000000b;
        public static int SearchView_searchHintIcon = 0x0000000c;
        public static int SearchView_searchIcon = 0x0000000d;
        public static int SearchView_submitBackground = 0x0000000e;
        public static int SearchView_suggestionRowLayout = 0x0000000f;
        public static int SearchView_voiceIcon = 0x00000010;
        public static int ShapeAppearance_cornerFamily = 0x00000000;
        public static int ShapeAppearance_cornerFamilyBottomLeft = 0x00000001;
        public static int ShapeAppearance_cornerFamilyBottomRight = 0x00000002;
        public static int ShapeAppearance_cornerFamilyTopLeft = 0x00000003;
        public static int ShapeAppearance_cornerFamilyTopRight = 0x00000004;
        public static int ShapeAppearance_cornerSize = 0x00000005;
        public static int ShapeAppearance_cornerSizeBottomLeft = 0x00000006;
        public static int ShapeAppearance_cornerSizeBottomRight = 0x00000007;
        public static int ShapeAppearance_cornerSizeTopLeft = 0x00000008;
        public static int ShapeAppearance_cornerSizeTopRight = 0x00000009;
        public static int SignInButton_buttonSize = 0x00000000;
        public static int SignInButton_colorScheme = 0x00000001;
        public static int SignInButton_scopeUris = 0x00000002;
        public static int SnackbarLayout_actionTextColorAlpha = 0x00000001;
        public static int SnackbarLayout_android_maxWidth = 0x00000000;
        public static int SnackbarLayout_animationMode = 0x00000002;
        public static int SnackbarLayout_backgroundOverlayColorAlpha = 0x00000003;
        public static int SnackbarLayout_elevation = 0x00000004;
        public static int SnackbarLayout_maxActionInlineWidth = 0x00000005;
        public static int Snackbar_snackbarButtonStyle = 0x00000000;
        public static int Snackbar_snackbarStyle = 0x00000001;
        public static int Spinner_android_dropDownWidth = 0x00000003;
        public static int Spinner_android_entries = 0x00000000;
        public static int Spinner_android_popupBackground = 0x00000001;
        public static int Spinner_android_prompt = 0x00000002;
        public static int Spinner_popupTheme = 0x00000004;
        public static int StateListDrawableItem_android_drawable = 0x00000000;
        public static int StateListDrawable_android_constantSize = 0x00000003;
        public static int StateListDrawable_android_dither = 0x00000000;
        public static int StateListDrawable_android_enterFadeDuration = 0x00000004;
        public static int StateListDrawable_android_exitFadeDuration = 0x00000005;
        public static int StateListDrawable_android_variablePadding = 0x00000002;
        public static int StateListDrawable_android_visible = 0x00000001;
        public static int SwitchCompat_android_textOff = 0x00000001;
        public static int SwitchCompat_android_textOn = 0x00000000;
        public static int SwitchCompat_android_thumb = 0x00000002;
        public static int SwitchCompat_showText = 0x00000003;
        public static int SwitchCompat_splitTrack = 0x00000004;
        public static int SwitchCompat_switchMinWidth = 0x00000005;
        public static int SwitchCompat_switchPadding = 0x00000006;
        public static int SwitchCompat_switchTextAppearance = 0x00000007;
        public static int SwitchCompat_thumbTextPadding = 0x00000008;
        public static int SwitchCompat_thumbTint = 0x00000009;
        public static int SwitchCompat_thumbTintMode = 0x0000000a;
        public static int SwitchCompat_track = 0x0000000b;
        public static int SwitchCompat_trackTint = 0x0000000c;
        public static int SwitchCompat_trackTintMode = 0x0000000d;
        public static int SwitchMaterial_useMaterialThemeColors = 0x00000000;
        public static int TabItem_android_icon = 0x00000000;
        public static int TabItem_android_layout = 0x00000001;
        public static int TabItem_android_text = 0x00000002;
        public static int TabLayout_tabBackground = 0x00000000;
        public static int TabLayout_tabContentStart = 0x00000001;
        public static int TabLayout_tabGravity = 0x00000002;
        public static int TabLayout_tabIconTint = 0x00000003;
        public static int TabLayout_tabIconTintMode = 0x00000004;
        public static int TabLayout_tabIndicator = 0x00000005;
        public static int TabLayout_tabIndicatorAnimationDuration = 0x00000006;
        public static int TabLayout_tabIndicatorColor = 0x00000007;
        public static int TabLayout_tabIndicatorFullWidth = 0x00000008;
        public static int TabLayout_tabIndicatorGravity = 0x00000009;
        public static int TabLayout_tabIndicatorHeight = 0x0000000a;
        public static int TabLayout_tabInlineLabel = 0x0000000b;
        public static int TabLayout_tabMaxWidth = 0x0000000c;
        public static int TabLayout_tabMinWidth = 0x0000000d;
        public static int TabLayout_tabMode = 0x0000000e;
        public static int TabLayout_tabPadding = 0x0000000f;
        public static int TabLayout_tabPaddingBottom = 0x00000010;
        public static int TabLayout_tabPaddingEnd = 0x00000011;
        public static int TabLayout_tabPaddingStart = 0x00000012;
        public static int TabLayout_tabPaddingTop = 0x00000013;
        public static int TabLayout_tabRippleColor = 0x00000014;
        public static int TabLayout_tabSelectedTextColor = 0x00000015;
        public static int TabLayout_tabTextAppearance = 0x00000016;
        public static int TabLayout_tabTextColor = 0x00000017;
        public static int TabLayout_tabUnboundedRipple = 0x00000018;
        public static int TextAppearance_android_fontFamily = 0x0000000a;
        public static int TextAppearance_android_shadowColor = 0x00000006;
        public static int TextAppearance_android_shadowDx = 0x00000007;
        public static int TextAppearance_android_shadowDy = 0x00000008;
        public static int TextAppearance_android_shadowRadius = 0x00000009;
        public static int TextAppearance_android_textColor = 0x00000003;
        public static int TextAppearance_android_textColorHint = 0x00000004;
        public static int TextAppearance_android_textColorLink = 0x00000005;
        public static int TextAppearance_android_textFontWeight = 0x0000000b;
        public static int TextAppearance_android_textSize = 0x00000000;
        public static int TextAppearance_android_textStyle = 0x00000002;
        public static int TextAppearance_android_typeface = 0x00000001;
        public static int TextAppearance_fontFamily = 0x0000000c;
        public static int TextAppearance_fontVariationSettings = 0x0000000d;
        public static int TextAppearance_textAllCaps = 0x0000000e;
        public static int TextAppearance_textLocale = 0x0000000f;
        public static int TextInputLayout_android_hint = 0x00000001;
        public static int TextInputLayout_android_textColorHint = 0x00000000;
        public static int TextInputLayout_boxBackgroundColor = 0x00000002;
        public static int TextInputLayout_boxBackgroundMode = 0x00000003;
        public static int TextInputLayout_boxCollapsedPaddingTop = 0x00000004;
        public static int TextInputLayout_boxCornerRadiusBottomEnd = 0x00000005;
        public static int TextInputLayout_boxCornerRadiusBottomStart = 0x00000006;
        public static int TextInputLayout_boxCornerRadiusTopEnd = 0x00000007;
        public static int TextInputLayout_boxCornerRadiusTopStart = 0x00000008;
        public static int TextInputLayout_boxStrokeColor = 0x00000009;
        public static int TextInputLayout_boxStrokeWidth = 0x0000000a;
        public static int TextInputLayout_boxStrokeWidthFocused = 0x0000000b;
        public static int TextInputLayout_counterEnabled = 0x0000000c;
        public static int TextInputLayout_counterMaxLength = 0x0000000d;
        public static int TextInputLayout_counterOverflowTextAppearance = 0x0000000e;
        public static int TextInputLayout_counterOverflowTextColor = 0x0000000f;
        public static int TextInputLayout_counterTextAppearance = 0x00000010;
        public static int TextInputLayout_counterTextColor = 0x00000011;
        public static int TextInputLayout_endIconCheckable = 0x00000012;
        public static int TextInputLayout_endIconContentDescription = 0x00000013;
        public static int TextInputLayout_endIconDrawable = 0x00000014;
        public static int TextInputLayout_endIconMode = 0x00000015;
        public static int TextInputLayout_endIconTint = 0x00000016;
        public static int TextInputLayout_endIconTintMode = 0x00000017;
        public static int TextInputLayout_errorEnabled = 0x00000018;
        public static int TextInputLayout_errorIconDrawable = 0x00000019;
        public static int TextInputLayout_errorIconTint = 0x0000001a;
        public static int TextInputLayout_errorIconTintMode = 0x0000001b;
        public static int TextInputLayout_errorTextAppearance = 0x0000001c;
        public static int TextInputLayout_errorTextColor = 0x0000001d;
        public static int TextInputLayout_helperText = 0x0000001e;
        public static int TextInputLayout_helperTextEnabled = 0x0000001f;
        public static int TextInputLayout_helperTextTextAppearance = 0x00000020;
        public static int TextInputLayout_helperTextTextColor = 0x00000021;
        public static int TextInputLayout_hintAnimationEnabled = 0x00000022;
        public static int TextInputLayout_hintEnabled = 0x00000023;
        public static int TextInputLayout_hintTextAppearance = 0x00000024;
        public static int TextInputLayout_hintTextColor = 0x00000025;
        public static int TextInputLayout_passwordToggleContentDescription = 0x00000026;
        public static int TextInputLayout_passwordToggleDrawable = 0x00000027;
        public static int TextInputLayout_passwordToggleEnabled = 0x00000028;
        public static int TextInputLayout_passwordToggleTint = 0x00000029;
        public static int TextInputLayout_passwordToggleTintMode = 0x0000002a;
        public static int TextInputLayout_shapeAppearance = 0x0000002b;
        public static int TextInputLayout_shapeAppearanceOverlay = 0x0000002c;
        public static int TextInputLayout_startIconCheckable = 0x0000002d;
        public static int TextInputLayout_startIconContentDescription = 0x0000002e;
        public static int TextInputLayout_startIconDrawable = 0x0000002f;
        public static int TextInputLayout_startIconTint = 0x00000030;
        public static int TextInputLayout_startIconTintMode = 0x00000031;
        public static int ThemeEnforcement_android_textAppearance = 0x00000000;
        public static int ThemeEnforcement_enforceMaterialTheme = 0x00000001;
        public static int ThemeEnforcement_enforceTextAppearance = 0x00000002;
        public static int Toolbar_android_gravity = 0x00000000;
        public static int Toolbar_android_minHeight = 0x00000001;
        public static int Toolbar_buttonGravity = 0x00000002;
        public static int Toolbar_collapseContentDescription = 0x00000003;
        public static int Toolbar_collapseIcon = 0x00000004;
        public static int Toolbar_contentInsetEnd = 0x00000005;
        public static int Toolbar_contentInsetEndWithActions = 0x00000006;
        public static int Toolbar_contentInsetLeft = 0x00000007;
        public static int Toolbar_contentInsetRight = 0x00000008;
        public static int Toolbar_contentInsetStart = 0x00000009;
        public static int Toolbar_contentInsetStartWithNavigation = 0x0000000a;
        public static int Toolbar_logo = 0x0000000b;
        public static int Toolbar_logoDescription = 0x0000000c;
        public static int Toolbar_maxButtonHeight = 0x0000000d;
        public static int Toolbar_menu = 0x0000000e;
        public static int Toolbar_navigationContentDescription = 0x0000000f;
        public static int Toolbar_navigationIcon = 0x00000010;
        public static int Toolbar_popupTheme = 0x00000011;
        public static int Toolbar_subtitle = 0x00000012;
        public static int Toolbar_subtitleTextAppearance = 0x00000013;
        public static int Toolbar_subtitleTextColor = 0x00000014;
        public static int Toolbar_title = 0x00000015;
        public static int Toolbar_titleMargin = 0x00000016;
        public static int Toolbar_titleMarginBottom = 0x00000017;
        public static int Toolbar_titleMarginEnd = 0x00000018;
        public static int Toolbar_titleMarginStart = 0x00000019;
        public static int Toolbar_titleMarginTop = 0x0000001a;
        public static int Toolbar_titleMargins = 0x0000001b;
        public static int Toolbar_titleTextAppearance = 0x0000001c;
        public static int Toolbar_titleTextColor = 0x0000001d;
        public static int ViewBackgroundHelper_android_background = 0x00000000;
        public static int ViewBackgroundHelper_backgroundTint = 0x00000001;
        public static int ViewBackgroundHelper_backgroundTintMode = 0x00000002;
        public static int ViewPager2_android_orientation = 0x00000000;
        public static int ViewStubCompat_android_id = 0x00000000;
        public static int ViewStubCompat_android_inflatedId = 0x00000002;
        public static int ViewStubCompat_android_layout = 0x00000001;
        public static int View_android_focusable = 0x00000001;
        public static int View_android_theme = 0x00000000;
        public static int View_paddingEnd = 0x00000002;
        public static int View_paddingStart = 0x00000003;
        public static int View_theme = 0x00000004;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_activityBackground_style = 0x00000000;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cardBackground_cornerRadius = 0x00000001;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cardBackground_image = 0x00000002;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cardBackground_style = 0x00000003;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cardMargin = 0x00000004;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_closeButton_description = 0x00000005;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_closeButton_enabled = 0x00000006;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_closeButton_icon = 0x00000007;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_closeButton_style = 0x00000008;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_background_color = 0x00000009;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_confirmation_label = 0x0000000a;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_description = 0x0000000b;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_icon = 0x0000000c;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_icon_color = 0x0000000d;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_startPercent = 0x0000000e;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_style = 0x0000000f;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_copyPanButton_topPercent = 0x00000010;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cvx2TextView_style = 0x00000011;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cvx2_endPercent = 0x00000012;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cvx2_startPercent = 0x00000013;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_cvx2_topPercent = 0x00000014;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_expiryDateTextView_style = 0x00000015;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_expiryDate_endPercent = 0x00000016;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_expiryDate_startPercent = 0x00000017;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_expiryDate_topPercent = 0x00000018;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_panTextView_style = 0x00000019;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_pan_endPercent = 0x0000001a;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_pan_startPercent = 0x0000001b;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_pan_topPercent = 0x0000001c;
        public static int antelopCardDisplayTheme_antelopCardDisplayTheme_screenTitle = 0x0000001d;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cameraScanButton_color = 0x00000000;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cameraScanButton_description = 0x00000001;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cameraScanButton_src = 0x00000002;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cameraScan_instructionText = 0x00000003;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cardholderNameTextInputLayout_errorMessage = 0x00000004;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cardholderNameTextInputLayout_hint = 0x00000005;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cardholderNameTextInputLayout_style = 0x00000006;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2DialogImage_src = 0x00000007;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2Dialog_buttonText = 0x00000008;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2Dialog_instructionText = 0x00000009;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2TextInputLayout_description = 0x0000000a;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2TextInputLayout_errorMessage = 0x0000000b;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2TextInputLayout_hint = 0x0000000c;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_cvx2TextInputLayout_style = 0x0000000d;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_errorDialog_buttonText = 0x0000000e;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateDialogImage_src = 0x0000000f;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateDialog_buttonText = 0x00000010;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateDialog_instructionText = 0x00000011;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateTextInputLayout_description = 0x00000012;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateTextInputLayout_errorMessage = 0x00000013;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateTextInputLayout_hint = 0x00000014;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_expiryDateTextInputLayout_style = 0x00000015;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_genericCard_src = 0x00000016;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_helpImage_color = 0x00000017;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_helpImage_src = 0x00000018;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_keypadViewCancelButton_src = 0x00000019;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_keypadViewDigitBackground_style = 0x0000001a;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_keypadViewDigit_style = 0x0000001b;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_keypadViewLayout_style = 0x0000001c;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_keypadViewSubmitButton_src = 0x0000001d;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_mastercardCard_src = 0x0000001e;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanAnimation_errorColor = 0x0000001f;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanAnimation_errorSrc = 0x00000020;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanAnimation_progressColor = 0x00000021;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanAnimation_successColor = 0x00000022;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanAnimation_successSrc = 0x00000023;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanError_cardNotSupportedText = 0x00000024;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanError_otherText = 0x00000025;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanError_signalLostText = 0x00000026;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanImage_src = 0x00000027;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanImage_style = 0x00000028;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanTextView_style = 0x00000029;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_nfcScanTextView_text = 0x0000002a;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_overlayProtection_enabled = 0x0000002b;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_overlayProtection_instructionText = 0x0000002c;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_panTextInputLayout_drawableStart = 0x0000002d;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_panTextInputLayout_errorMessage = 0x0000002e;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_panTextInputLayout_hint = 0x0000002f;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_panTextInputLayout_style = 0x00000030;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_submitButton_style = 0x00000031;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_submitButton_text = 0x00000032;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_title = 0x00000033;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_visaCard_src = 0x00000034;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_waitingLayout_style = 0x00000035;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_waitingProgressBar_style = 0x00000036;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_waitingTextView_style = 0x00000037;
        public static int antelopCardPromptActivityTheme_antelopCardPromptActivityTheme_waitingTextView_text = 0x00000038;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_activityBackground_style = 0x00000000;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_cardBackground_cornerRadius = 0x00000001;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_cardBackground_image = 0x00000002;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_cardBackground_style = 0x00000003;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_cardMargin = 0x00000004;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_closeButton_description = 0x00000005;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_closeButton_enabled = 0x00000006;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_closeButton_icon = 0x00000007;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_closeButton_style = 0x00000008;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_pinTextView_style = 0x00000009;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_screenTitle = 0x0000000a;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_titleTextView_style = 0x0000000b;
        public static int antelopPinDisplayTheme_antelopPinDisplayTheme_titleTextView_text = 0x0000000c;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_alphaStyle = 0x00000000;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_backgroundStyle = 0x00000001;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_bulletIcon = 0x00000002;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_closeIcon = 0x00000003;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_colorPrimary = 0x00000004;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_colorSecondary = 0x00000005;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_defaultSubTitle = 0x00000006;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_defaultTitle = 0x00000007;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_deleteIcon = 0x00000008;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_digitStyle = 0x00000009;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_displayAlpha = 0x0000000a;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_enableOverlayProtection = 0x0000000b;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_overlayWarningMessage = 0x0000000c;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_pinSize = 0x0000000d;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_pinsNotMatchingErrorDescription = 0x0000000e;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_randomizeKeyboard = 0x0000000f;
        public static int antelopSecurePinInputThemeInternal_antelopSecurePinInputThemeInternal_showCloseButton = 0x00000010;
        public static int[] ActionBar = {2130968758, 2130968765, 2130968766, 2130968897, 2130968898, 2130968899, 2130968900, 2130968901, 2130968902, 2130968928, 2130968941, 2130968942, 2130968961, 2130969025, 2130969031, 2130969037, 2130969038, 2130969040, 2130969052, 2130969065, 2130969167, 2130969200, 2130969225, 2130969230, 2130969231, 2130969298, 2130969301, 2130969373, 2130969383};
        public static int[] ActionBarLayout = {android.R.attr.layout_gravity};
        public static int[] ActionMenuItemView = {android.R.attr.minWidth};
        public static int[] ActionMenuView = new int[0];
        public static int[] ActionMode = {2130968758, 2130968765, 2130968863, 2130969025, 2130969301, 2130969383};
        public static int[] ActivityChooserView = {2130968983, 2130969053};
        public static int[] AlertDialog = {android.R.attr.layout, 2130968808, 2130968809, 2130969156, 2130969157, 2130969196, 2130969263, 2130969265};
        public static int[] AnimatedStateListDrawableCompat = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] AnimatedStateListDrawableItem = {android.R.attr.id, android.R.attr.drawable};
        public static int[] AnimatedStateListDrawableTransition = {android.R.attr.drawable, android.R.attr.toId, android.R.attr.fromId, android.R.attr.reversible};
        public static int[] AntelopKeypadView = {2130968709, 2130968710, 2130968711, 2130968712, 2130968713, 2130968714, 2130968715, 2130968716, 2130968717};
        public static int[] AppBarLayout = {android.R.attr.background, android.R.attr.touchscreenBlocksFocus, android.R.attr.keyboardNavigationCluster, 2130968961, 2130968984, 2130969148, 2130969149, 2130969292};
        public static int[] AppBarLayoutStates = {2130969286, 2130969287, 2130969289, 2130969290};
        public static int[] AppBarLayout_Layout = {2130969146, 2130969147};
        public static int[] AppCompatEmojiHelper = new int[0];
        public static int[] AppCompatImageView = {android.R.attr.src, 2130969276, 2130969371, 2130969372};
        public static int[] AppCompatSeekBar = {android.R.attr.thumb, 2130969368, 2130969369, 2130969370};
        public static int[] AppCompatTextHelper = {android.R.attr.textAppearance, android.R.attr.drawableTop, android.R.attr.drawableBottom, android.R.attr.drawableLeft, android.R.attr.drawableRight, android.R.attr.drawableStart, android.R.attr.drawableEnd};
        public static int[] AppCompatTextView = {android.R.attr.textAppearance, 2130968753, 2130968754, 2130968755, 2130968756, 2130968757, 2130968946, 2130968947, 2130968948, 2130968949, 2130968951, 2130968952, 2130968953, 2130968954, 2130968964, 2130969006, 2130969009, 2130969018, 2130969084, 2130969150, 2130969333, 2130969360};
        public static int[] AppCompatTheme = {android.R.attr.windowIsFloating, android.R.attr.windowAnimationStyle, 2130968577, 2130968578, 2130968579, 2130968580, 2130968581, 2130968582, 2130968583, 2130968584, 2130968585, 2130968586, 2130968587, 2130968588, 2130968589, 2130968591, 2130968592, 2130968593, 2130968594, 2130968595, 2130968596, 2130968597, 2130968598, 2130968599, 2130968600, 2130968601, 2130968602, 2130968603, 2130968604, 2130968605, 2130968606, 2130968607, 2130968608, 2130968609, 2130968613, 2130968614, 2130968615, 2130968616, 2130968617, 2130968752, 2130968786, 2130968801, 2130968802, 2130968803, 2130968804, 2130968805, 2130968811, 2130968812, 2130968827, 2130968834, 2130968869, 2130968870, 2130968871, 2130968872, 2130968873, 2130968874, 2130968875, 2130968882, 2130968883, 2130968891, 2130968909, 2130968938, 2130968939, 2130968940, 2130968943, 2130968945, 2130968956, 2130968957, 2130968958, 2130968959, 2130968960, 2130969037, 2130969051, 2130969152, 2130969153, 2130969154, 2130969155, 2130969158, 2130969159, 2130969160, 2130969161, 2130969162, 2130969163, 2130969164, 2130969165, 2130969166, 2130969211, 2130969212, 2130969213, 2130969224, 2130969226, 2130969235, 2130969237, 2130969238, 2130969239, 2130969249, 2130969250, 2130969251, 2130969252, 2130969272, 2130969273, 2130969305, 2130969344, 2130969346, 2130969347, 2130969348, 2130969350, 2130969351, 2130969352, 2130969353, 2130969356, 2130969357, 2130969385, 2130969386, 2130969387, 2130969388, 2130969397, 2130969399, 2130969400, 2130969401, 2130969402, 2130969403, 2130969404, 2130969405, 2130969406, 2130969407, 2130969408};
        public static int[] Badge = {2130968759, 2130968769, 2130968771, 2130969191, 2130969204};
        public static int[] BottomAppBar = {2130968767, 2130968961, 2130968994, 2130968995, 2130968996, 2130968997, 2130968998, 2130969032};
        public static int[] BottomNavigationView = {2130968767, 2130968961, 2130969057, 2130969060, 2130969062, 2130969063, 2130969066, 2130969078, 2130969079, 2130969080, 2130969083, 2130969194};
        public static int[] BottomSheetBehavior_Layout = {android.R.attr.elevation, 2130968767, 2130968777, 2130968778, 2130968779, 2130968780, 2130968782, 2130968783, 2130968784, 2130969253, 2130969256};
        public static int[] ButtonBarLayout = {2130968618};
        public static int[] Capability = {2130969234, 2130969258};
        public static int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, 2130968815, 2130968816, 2130968817, 2130968819, 2130968820, 2130968821, 2130968903, 2130968904, 2130968905, 2130968906, 2130968907};
        public static int[] CheckedTextView = {android.R.attr.checkMark, 2130968824, 2130968825, 2130968826};
        public static int[] Chip = {android.R.attr.textAppearance, android.R.attr.textColor, android.R.attr.ellipsize, android.R.attr.maxWidth, android.R.attr.text, android.R.attr.checkable, 2130968830, 2130968831, 2130968833, 2130968835, 2130968836, 2130968837, 2130968839, 2130968840, 2130968841, 2130968842, 2130968843, 2130968844, 2130968845, 2130968850, 2130968851, 2130968852, 2130968854, 2130968856, 2130968857, 2130968858, 2130968859, 2130968860, 2130968861, 2130968862, 2130968974, 2130969030, 2130969041, 2130969045, 2130969242, 2130969253, 2130969256, 2130969261, 2130969358, 2130969361};
        public static int[] ChipGroup = {2130968829, 2130968846, 2130968847, 2130968848, 2130969266, 2130969267};
        public static int[] CollapsingToolbarLayout = {2130968866, 2130968867, 2130968908, 2130968985, 2130968986, 2130968987, 2130968988, 2130968989, 2130968990, 2130968991, 2130969244, 2130969246, 2130969293, 2130969373, 2130969374, 2130969384};
        public static int[] CollapsingToolbarLayout_Layout = {2130969091, 2130969092};
        public static int[] ColorStateListItem = {android.R.attr.color, android.R.attr.alpha, android.R.attr.lStar, 2130968619, 2130969082};
        public static int[] CompoundButton = {android.R.attr.button, 2130968806, 2130968813, 2130968814};
        public static int[] ConstraintLayout_Layout = {android.R.attr.orientation, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, 2130968773, 2130968774, 2130968823, 2130968893, 2130968894, 2130969093, 2130969094, 2130969095, 2130969096, 2130969097, 2130969098, 2130969099, 2130969100, 2130969101, 2130969102, 2130969103, 2130969104, 2130969105, 2130969106, 2130969107, 2130969108, 2130969109, 2130969110, 2130969111, 2130969112, 2130969113, 2130969114, 2130969115, 2130969116, 2130969117, 2130969118, 2130969119, 2130969120, 2130969121, 2130969122, 2130969123, 2130969124, 2130969125, 2130969126, 2130969127, 2130969128, 2130969129, 2130969130, 2130969131, 2130969132, 2130969133, 2130969135, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142, 2130969145};
        public static int[] ConstraintLayout_placeholder = {2130968895, 2130968965};
        public static int[] ConstraintSet = {android.R.attr.orientation, android.R.attr.id, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.translationZ, android.R.attr.elevation, 2130968773, 2130968774, 2130968823, 2130968894, 2130969093, 2130969094, 2130969095, 2130969096, 2130969097, 2130969098, 2130969099, 2130969100, 2130969101, 2130969102, 2130969103, 2130969104, 2130969105, 2130969106, 2130969107, 2130969108, 2130969109, 2130969110, 2130969111, 2130969112, 2130969113, 2130969114, 2130969115, 2130969116, 2130969117, 2130969118, 2130969119, 2130969120, 2130969121, 2130969122, 2130969123, 2130969124, 2130969125, 2130969126, 2130969127, 2130969128, 2130969129, 2130969130, 2130969131, 2130969132, 2130969133, 2130969135, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142};
        public static int[] CoordinatorLayout = {2130969081, 2130969291};
        public static int[] CoordinatorLayout_Layout = {android.R.attr.layout_gravity, 2130969088, 2130969089, 2130969090, 2130969134, 2130969143, 2130969144};
        public static int[] DrawerArrowToggle = {2130968750, 2130968751, 2130968772, 2130968868, 2130968950, 2130969021, 2130969271, 2130969364};
        public static int[] ExtendedFloatingActionButton = {2130968961, 2130968992, 2130969030, 2130969261, 2130969264};
        public static int[] ExtendedFloatingActionButton_Behavior_Layout = {2130968775, 2130968776};
        public static int[] FloatingActionButton = {2130968767, 2130968768, 2130968785, 2130968961, 2130968974, 2130968999, 2130969000, 2130969030, 2130969039, 2130969192, 2130969229, 2130969242, 2130969253, 2130969256, 2130969261, 2130969395};
        public static int[] FloatingActionButton_Behavior_Layout = {2130968775};
        public static int[] FlowLayout = {2130969074, 2130969151};
        public static int[] FontFamily = {2130969010, 2130969011, 2130969012, 2130969013, 2130969014, 2130969015, 2130969016};
        public static int[] FontFamilyFont = {android.R.attr.font, android.R.attr.fontWeight, android.R.attr.fontStyle, android.R.attr.ttcIndex, android.R.attr.fontVariationSettings, 2130969008, 2130969017, 2130969018, 2130969019, 2130969393};
        public static int[] ForegroundLinearLayout = {android.R.attr.foreground, android.R.attr.foregroundGravity, 2130969020};
        public static int[] Fragment = {android.R.attr.name, android.R.attr.id, android.R.attr.tag};
        public static int[] FragmentContainerView = {android.R.attr.name, android.R.attr.tag};
        public static int[] GradientColor = {android.R.attr.startColor, android.R.attr.endColor, android.R.attr.type, android.R.attr.centerX, android.R.attr.centerY, android.R.attr.gradientRadius, android.R.attr.tileMode, android.R.attr.centerColor, android.R.attr.startX, android.R.attr.startY, android.R.attr.endX, android.R.attr.endY};
        public static int[] GradientColorItem = {android.R.attr.color, android.R.attr.offset};
        public static int[] LinearLayoutCompat = {android.R.attr.gravity, android.R.attr.orientation, android.R.attr.baselineAligned, android.R.attr.baselineAlignedChildIndex, android.R.attr.weightSum, 2130968942, 2130968944, 2130969193, 2130969260};
        public static int[] LinearLayoutCompat_Layout = {android.R.attr.layout_gravity, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_weight};
        public static int[] ListPopupWindow = {android.R.attr.dropDownHorizontalOffset, android.R.attr.dropDownVerticalOffset};
        public static int[] LoadingImageView = {2130968855, 2130969049, 2130969050};
        public static int[] MaterialAlertDialog = {2130968760, 2130968761, 2130968762, 2130968763};
        public static int[] MaterialAlertDialogTheme = {2130969169, 2130969170, 2130969171, 2130969172, 2130969173};
        public static int[] MaterialButton = {android.R.attr.insetLeft, android.R.attr.insetRight, android.R.attr.insetTop, android.R.attr.insetBottom, android.R.attr.checkable, 2130968767, 2130968768, 2130968916, 2130968961, 2130969040, 2130969042, 2130969043, 2130969044, 2130969046, 2130969047, 2130969242, 2130969253, 2130969256, 2130969294, 2130969295};
        public static int[] MaterialButtonToggleGroup = {2130968828, 2130969267};
        public static int[] MaterialCalendar = {android.R.attr.windowFullscreen, 2130968931, 2130968932, 2130968933, 2130968934, 2130969236, 2130969413, 2130969414, 2130969415};
        public static int[] MaterialCalendarItem = {android.R.attr.insetLeft, android.R.attr.insetRight, android.R.attr.insetTop, android.R.attr.insetBottom, 2130969058, 2130969067, 2130969068, 2130969075, 2130969076, 2130969080};
        public static int[] MaterialCardView = {android.R.attr.checkable, 2130968818, 2130968830, 2130968832, 2130969242, 2130969253, 2130969256, 2130969288, 2130969294, 2130969295};
        public static int[] MaterialCheckBox = {2130968813, 2130969396};
        public static int[] MaterialRadioButton = {2130969396};
        public static int[] MaterialShape = {2130969253, 2130969256};
        public static int[] MaterialTextAppearance = {android.R.attr.lineHeight, 2130969150};
        public static int[] MaterialTextView = {android.R.attr.textAppearance, android.R.attr.lineHeight, 2130969150};
        public static int[] MenuGroup = {android.R.attr.enabled, android.R.attr.id, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.checkableBehavior};
        public static int[] MenuItem = {android.R.attr.icon, android.R.attr.enabled, android.R.attr.id, android.R.attr.checked, android.R.attr.visible, android.R.attr.menuCategory, android.R.attr.orderInCategory, android.R.attr.title, android.R.attr.titleCondensed, android.R.attr.alphabeticShortcut, android.R.attr.numericShortcut, android.R.attr.checkable, android.R.attr.onClick, 2130968590, 2130968610, 2130968612, 2130968620, 2130968896, 2130969046, 2130969047, 2130969205, 2130969259, 2130969389};
        public static int[] MenuView = {android.R.attr.windowAnimationStyle, android.R.attr.itemTextAppearance, android.R.attr.horizontalDivider, android.R.attr.verticalDivider, android.R.attr.headerBackground, android.R.attr.itemBackground, android.R.attr.itemIconDisabledAlpha, 2130969228, 2130969296};
        public static int[] NavigationView = {android.R.attr.background, android.R.attr.fitsSystemWindows, android.R.attr.maxWidth, 2130968961, 2130969024, 2130969057, 2130969059, 2130969061, 2130969062, 2130969063, 2130969064, 2130969067, 2130969068, 2130969069, 2130969070, 2130969071, 2130969072, 2130969073, 2130969077, 2130969080, 2130969194};
        public static int[] PopupWindow = {android.R.attr.popupBackground, android.R.attr.popupAnimationStyle, 2130969206};
        public static int[] PopupWindowBackgroundState = {2130969285};
        public static int[] RecycleListView = {2130969207, 2130969210};
        public static int[] RecyclerView = {android.R.attr.orientation, android.R.attr.clipToPadding, android.R.attr.descendantFocusability, 2130969001, 2130969002, 2130969003, 2130969004, 2130969005, 2130969087, 2130969241, 2130969270, 2130969277};
        public static int[] ScrimInsetsFrameLayout = {2130969054};
        public static int[] ScrollingViewBehavior_Layout = {2130968781};
        public static int[] SearchView = {android.R.attr.focusable, android.R.attr.maxWidth, android.R.attr.inputType, android.R.attr.imeOptions, 2130968856, 2130968892, 2130968936, 2130969022, 2130969048, 2130969086, 2130969232, 2130969233, 2130969247, 2130969248, 2130969297, 2130969302, 2130969398};
        public static int[] ShapeAppearance = {2130968911, 2130968912, 2130968913, 2130968914, 2130968915, 2130968917, 2130968918, 2130968919, 2130968920, 2130968921};
        public static int[] SignInButton = {2130968810, 2130968887, 2130969243};
        public static int[] Snackbar = {2130969268, 2130969269};
        public static int[] SnackbarLayout = {android.R.attr.maxWidth, 2130968611, 2130968621, 2130968764, 2130968961, 2130969189};
        public static int[] Spinner = {android.R.attr.entries, android.R.attr.popupBackground, android.R.attr.prompt, android.R.attr.dropDownWidth, 2130969225};
        public static int[] StateListDrawable = {android.R.attr.dither, android.R.attr.visible, android.R.attr.variablePadding, android.R.attr.constantSize, android.R.attr.enterFadeDuration, android.R.attr.exitFadeDuration};
        public static int[] StateListDrawableItem = {android.R.attr.drawable};
        public static int[] SwitchCompat = {android.R.attr.textOn, android.R.attr.textOff, android.R.attr.thumb, 2130969262, 2130969275, 2130969303, 2130969304, 2130969306, 2130969365, 2130969366, 2130969367, 2130969390, 2130969391, 2130969392};
        public static int[] SwitchMaterial = {2130969396};
        public static int[] TabItem = {android.R.attr.icon, android.R.attr.layout, android.R.attr.text};
        public static int[] TabLayout = {2130969307, 2130969308, 2130969309, 2130969310, 2130969311, 2130969312, 2130969313, 2130969314, 2130969315, 2130969316, 2130969317, 2130969318, 2130969319, 2130969320, 2130969321, 2130969322, 2130969323, 2130969324, 2130969325, 2130969326, 2130969327, 2130969328, 2130969330, 2130969331, 2130969332};
        public static int[] TextAppearance = {android.R.attr.textSize, android.R.attr.typeface, android.R.attr.textStyle, android.R.attr.textColor, android.R.attr.textColorHint, android.R.attr.textColorLink, android.R.attr.shadowColor, android.R.attr.shadowDx, android.R.attr.shadowDy, android.R.attr.shadowRadius, android.R.attr.fontFamily, android.R.attr.textFontWeight, 2130969009, 2130969018, 2130969333, 2130969360};
        public static int[] TextInputLayout = {android.R.attr.textColorHint, android.R.attr.hint, 2130968791, 2130968792, 2130968793, 2130968794, 2130968795, 2130968796, 2130968797, 2130968798, 2130968799, 2130968800, 2130968922, 2130968923, 2130968924, 2130968925, 2130968926, 2130968927, 2130968966, 2130968967, 2130968968, 2130968969, 2130968970, 2130968971, 2130968976, 2130968977, 2130968978, 2130968979, 2130968980, 2130968981, 2130969026, 2130969027, 2130969028, 2130969029, 2130969033, 2130969034, 2130969035, 2130969036, 2130969214, 2130969215, 2130969216, 2130969217, 2130969218, 2130969253, 2130969256, 2130969279, 2130969280, 2130969281, 2130969282, 2130969283};
        public static int[] ThemeEnforcement = {android.R.attr.textAppearance, 2130968972, 2130968973};
        public static int[] Toolbar = {android.R.attr.gravity, android.R.attr.minHeight, 2130968807, 2130968864, 2130968865, 2130968897, 2130968898, 2130968899, 2130968900, 2130968901, 2130968902, 2130969167, 2130969168, 2130969190, 2130969194, 2130969198, 2130969199, 2130969225, 2130969298, 2130969299, 2130969300, 2130969373, 2130969375, 2130969376, 2130969377, 2130969378, 2130969379, 2130969380, 2130969381, 2130969382};
        public static int[] View = {android.R.attr.theme, android.R.attr.focusable, 2130969208, 2130969209, 2130969362};
        public static int[] ViewBackgroundHelper = {android.R.attr.background, 2130968767, 2130968768};
        public static int[] ViewPager2 = {android.R.attr.orientation};
        public static int[] ViewStubCompat = {android.R.attr.id, android.R.attr.layout, android.R.attr.inflatedId};
        public static int[] antelopCardDisplayTheme = {2130968622, 2130968623, 2130968624, 2130968625, 2130968626, 2130968627, 2130968628, 2130968629, 2130968630, 2130968631, 2130968632, 2130968633, 2130968634, 2130968635, 2130968636, 2130968637, 2130968638, 2130968639, 2130968640, 2130968641, 2130968642, 2130968643, 2130968644, 2130968645, 2130968646, 2130968647, 2130968648, 2130968649, 2130968650, 2130968651};
        public static int[] antelopCardPromptActivityTheme = {2130968652, 2130968653, 2130968654, 2130968655, 2130968656, 2130968657, 2130968658, 2130968659, 2130968660, 2130968661, 2130968662, 2130968663, 2130968664, 2130968665, 2130968666, 2130968667, 2130968668, 2130968669, 2130968670, 2130968671, 2130968672, 2130968673, 2130968674, 2130968675, 2130968676, 2130968677, 2130968678, 2130968679, 2130968680, 2130968681, 2130968682, 2130968683, 2130968684, 2130968685, 2130968686, 2130968687, 2130968688, 2130968689, 2130968690, 2130968691, 2130968692, 2130968693, 2130968694, 2130968695, 2130968696, 2130968697, 2130968698, 2130968699, 2130968700, 2130968701, 2130968702, 2130968703, 2130968704, 2130968705, 2130968706, 2130968707, 2130968708};
        public static int[] antelopPinDisplayTheme = {2130968718, 2130968719, 2130968720, 2130968721, 2130968722, 2130968723, 2130968724, 2130968725, 2130968726, 2130968727, 2130968728, 2130968729, 2130968730};
        public static int[] antelopSecurePinInputThemeInternal = {2130968731, 2130968732, 2130968733, 2130968734, 2130968735, 2130968736, 2130968737, 2130968738, 2130968739, 2130968740, 2130968741, 2130968742, 2130968743, 2130968744, 2130968745, 2130968746, 2130968747};

        private styleable() {
        }
    }

    private R() {
    }
}
