package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v6.smali */
public class v6 extends u6 {
    private BigInteger g;
    private BigInteger h;
    private BigInteger i;
    private BigInteger j;
    private BigInteger k;
    private BigInteger l;

    public v6(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4, BigInteger bigInteger5, BigInteger bigInteger6, BigInteger bigInteger7, BigInteger bigInteger8) {
        this(bigInteger, bigInteger2, bigInteger3, bigInteger4, bigInteger5, bigInteger6, bigInteger7, bigInteger8, false);
    }

    public BigInteger c() {
        return this.j;
    }

    public BigInteger d() {
        return this.k;
    }

    public BigInteger e() {
        return this.h;
    }

    public BigInteger f() {
        return this.g;
    }

    public BigInteger g() {
        return this.i;
    }

    public BigInteger h() {
        return this.l;
    }

    public v6(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4, BigInteger bigInteger5, BigInteger bigInteger6, BigInteger bigInteger7, BigInteger bigInteger8, boolean z) {
        super(true, bigInteger, bigInteger3, z);
        this.g = bigInteger2;
        this.h = bigInteger4;
        this.i = bigInteger5;
        this.j = bigInteger6;
        this.k = bigInteger7;
        this.l = bigInteger8;
    }
}
