package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import kotlin.Metadata;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\b\u0010\u0006R\u001a\u0010\u0007\u001a\u00020\u00028FX\u0087\u0004¢\u0006\f\u0012\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0003\u0010\u0004¨\u0006\t"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/d$a;", "", "", "a", "()Ljava/lang/String;", "getSettingsSecureKey$annotations", "()V", "settingsSecureKey", "<init>", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\d$a.smali */
public final class d$a {
    private d$a() {
    }

    public /* synthetic */ d$a(DefaultConstructorMarker defaultConstructorMarker) {
        this();
    }

    public final String a() {
        byte[] bArr = new byte[10];
        int[] iArr = {Opcodes.FDIV, 63, Opcodes.IF_ICMPEQ, 127, 79, 239, Opcodes.IF_ICMPEQ, 78, 239, Opcodes.IF_ICMPEQ};
        for (int i = 0; i < 10; i++) {
            int i2 = iArr[i] - 158;
            bArr[i] = (byte) (((((i2 & 255) >> 4) | (i2 << 4)) & 255) - 172);
        }
        return new String(bArr, Charsets.UTF_8);
    }
}
