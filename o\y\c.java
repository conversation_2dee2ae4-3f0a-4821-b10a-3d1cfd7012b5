package o.y;

import android.content.Context;
import android.telephony.cdma.CdmaCellLocation;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import o.cf.f;
import o.cf.g;
import o.cf.h;
import o.cf.i;
import o.cf.j;
import o.y.b;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\c.smali */
public abstract class c<C extends b<?>> extends a<C> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] d;
    private static int f;
    private static long h;
    private static int j;
    private final boolean a;
    private j b;
    private o.eg.b c;
    private byte[][] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f = 1;
        char[] cArr = new char[1330];
        ByteBuffer.wrap(",¬¦ª8\u0099²\u0095\u0004ÿ\u009e¼\u0010ØêÖ|$ö>HCÂ\u0017T}.q D:C\u008d\u00ad\u0007\u0081\u0099\u0095\u0013íå½\u007fÞñÚK?Ý$W\u0005)\u0000£v \u0081ª\u00974¿¾à\b\u009a\u0092\u0086\u001cúæêp(ú\u001fD:Î+XZ\"]¬n6\\\u0081\u008c\u000b¡\u0095µ\u001fàéÕsùýæG\u0011Ñ\u0018[),»¦\u00ad8\u0085²Ú\u0004 \u009e¼\u0010ÀêÐ|\u0007ö1H\nÂ\u001eTp.f B:f\u008d¶\u0007\u009b\u0099\u008f\u0013Úåï\u007fÃñÜK+Ý\"W\u0013)I£\"%\u0004¯\u00121:»e\r\u001f\u0097\u0003\u0019Uãou\u009a9¦³ª-\u0084§\u0093\u0011ò\u008bâ\u0005Àÿæi.ã+]\u001c×\u001dA|;j,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u009f\u0099\u009a\u0013æåñ\u007fÉñËKnÝ?W\u000f)\u0007£\"5g\u008fQ\u0001Y\u0094¨n\u0080à\u0096zì\u0093[\u0019W\u0087y\rn»\u000f!\u001f¯=U\u001bÃÓIÖ÷á}àë\u0081\u0091\u0097\u001fð\u0085ì2\u000e¸j&\u007f¬\u0018Z\u0018À{N?ôØbÊèû\u0096á\u001c\u0081\u008aÂ0¿¾¯+VÑy_fÅ\u0012s\nùzg$íÍ\u009bÝ\u0001¶\u008fð5\u009c£\u008c)¾×¦\\\u0010ÊUpfþ\u001ad\u001e\u00128\u0098j\u0006Ò\u008cÛ:¹ è.\u0098ÔÄB»È§uGãWi~\u0017\f\u009d\u0004\u000b|±.?Õ¥ÅSöÙìG\u0095Í\u0093{½áªn\\\u0087°\r¼\u0093\u0092\u0019\u0085¯ä5ô»ÖAð×8]=ã\ni\u000bÿj\u0085|\u000b\u001b\u0091\u0007&å¬\u009e2\u0094¸ùNïÔ\u0090ZÆà*v.ü\u0019\u0082\u001f\bj\u009e`$WªE?ú,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007º\u0099\u009e\u0013ûåè\u007fÉñÌK:ÝqW\u0010)\u0012£{5y\u008fK\u0001V\u0094¢nÉà\u0088zùÌÿFÝØÝR=$?¾\u00150\u0019\u008al\u001c|\u0096\u0005hRã¦u¿Ï\u0095AíÛÿ,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u009b\u0099\u0090\u0013ãåí\u007fÜñÖK Ý6W@)\u0011£c5v\u008fO\u0001R\u0094¨n\u008dàØzùÌÿFÞØÌR $0¾\u00120\u0015\u008a#\u001cb\u0096WhQã¤u³Ï\u008aAûÛò\u00adÄ'Ú,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007¡\u0099\u0095\u0013þåø\u007fÞñÍK;Ý!W\u0014)\u0016£f5P\u008f\\\u0001T\u0094£n\u0099à\u008czâÌõFÃØ\u009cR8$6¾\b0\u001c\u008af\u001c2\u0096WhQã¶u£Ï\u009cAûÛï\u00adÃ'Ó¹+3\u007f\u0085\u0019\u001f\u0010\u0091lk\u007fýGwAÊä\\¢Ö\u0096¨í\"ù´ß\u000eß,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u0098\u0099\u009a\u0013øåü\u007fÁñÚK:Ý4W\u0012)\u0000£\"5/\u008f\u0004,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u009a\u0099\u009e\u0013ùåí\u007fÃñÑK=Ý4W@)I£\",¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u009a\u0099\u009e\u0013ûåè\u007fÉñÌK:ÝqW\u0012)\u0016£h5p\u008fG\u0001C\u0094£n\u008dàØzíÌèFÂØÑRo$<¾\u00000\u0013\u008ah\u001cw\u0096KhPãçu°Ï\u0096AúÛ»\u00adÞ'Õ¹)3\u007f\u0085\u001c\u001f\u0014\u0091ak`ýMw[Êä\\íÖÆ,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u009d\u0099\u008b\u0013îåü\u007fØñÖK Ý6W@)\u0004£c5y\u008fH\u0001R\u0094²nÉà\u009bzäÌôFËØÕR($~¾\u00070\u0002\u008al\u001c\u007f\u0096\u0005hFã¢u¥Ï\u0089AçÛõ\u00adÙ'Ø,ª¦´8\u0084²\u008f\u0004é\u009eÑ\u0010ÊêÍ|2ö1H\u0004Â\u001bTk.s c:W\u008d\u00ad\u0007\u0089ÚrP~ÎPDGò&h6æ\u0014\u001c2\u008aú\u0000ÿ¾È4É¢¨Ø¾VÙÌÅ{'ñUoIå;\u00136\u0089\u0016\u0007A½ý+ê¡ÍßÞU½Ã¬y\u0093÷\u0087b\u007f\u0098\u0017\u0016B\u008c4:0°\u0012.B¤äÒðHÛÆÏ|©ê©`Û\u009e\u008e\u0015|\u0083|9B·5-1[\u0011Ñ\u0007,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u0082\u0099\u0088\u0013ååó\u007f\u008cñÚK6Ý2W\u0005)\u0003£v5|\u008fK\u0001Y\u0094æn\u0080à\u0096z«ÌùFÁØÓR:$:¾,0\u0015\u008ap\u001ca\u0096DhSã®u¸Ï\u009eAÌÛú\u00adÞ'Ü¹lÔS^_ÀqJfü\u0007f\u0017è5\u0012\u0013\u0084Û\u000eÞ°é:è¬\u0089Ö\u009fXøÂäu\u0006ÿ`aeë\u0019\u001d\u000e\u00876\t4³\u0091%Í¯ðÑâ[\u009bÍ\u0083w¼ù½lK\u0096w\u0018s\u0082\u001d4\n¾< cªÅÜÑFúÈîr\u0088ä\u0088nú\u0090\u00ad\u001bY\u008d@7j¹\u0012#\u0000UußxA\u0093ËÕ}áçêi\u009e\u0093\u0098\u0005¸\u008fê2X¤G.wP\u0010Ú\u000eL3öexÔâÂ\u0014ù\u009eí\u0000\u009b\u008a\u008b\u0087\u0092\r\u009e\u0093°\u0019§¯Æ5Ö»ôAÒ×\u001a]\u001fã(i)ÿH\u0085^\u000b9\u0091%&Ç¬°2¤¸ÝNÏÔçZóà\u0015vOü=\u0082\"\bQ\u009eF${ªg?\u009cÅ÷KëÑ\u0095gÔíòsðù\u0002\u008f\u0005\u0015\u001c\u009b!!P·A=zÃdH\u009dÞ®d¦êßpÉ\u0006á\u008cñ\u0012\u0017\u0098\".?´+:[À\rV&Ü+g;í7s\u0019ù\u000eOoÕ\u007f[]¡{7³½¶\u0003\u0081\u0089\u0080\u001fáe÷ë\u0090q\u008cÆnL\u0019Ò\rXt®f4NºZ\u0000¼\u0096æ\u001c\u0094b\u008bèø~ïÄÒJÎß5%^«B1<\u0087~\r_\u0093_\u0019øo¾õ\u0097{\u008bÁøWàÝÆ#\u0083¨#>5\u0084\u000f\nk\u0090yæNl\nò¾xºÎ\u008bT\u0089Úå ¤¶Ö<Í\u00817\u0017%\u009dQã$i/,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007\u008e\u0099\u009a\u0013ãåñ\u007fÙñÍK+ÝqW\u0003)\u001c£o5x\u008fE\u0001Y\u0094¢nÉàÕz«ÌüFÌØÐR#$<¾\u00000\u0013\u008ah\u001c2\u0096Qh[ãçu\u0084Ï\u009cAîÛî\u00adÙ'Ø¹(3\u0010\u0085\u001e\u001f\u0014\u0091rkrýVw\\Ê«\\¹,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007©\u0099\u008b\u0013úåñ\u007fÅñÜK/Ý%W\t)\u0005£g55\u008fA\u0001E\u0094´n\u0086à\u008az«Ì F\u008d,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007¾\u0099\u009a\u0013æåô\u007fÈñ\u009fK<Ý4W\u0013)\u0003£m5{\u008fW\u0001R\u0094æn\u008aà\u0097zïÌÿF\u0097Ø\u009c,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u001b\u008dù\u0007«\u0099\u0093\u0013ïåþ\u007fÇñ\u009fK(Ý>W\u0012)S£c5`\u008fP\u0001_\u0094æn\u009bà\u009dzøÌïFÁØÈs×ùÒgàíí[ Á\u0086O£µ´#R©[,¬¦ 8\u008e²\u0099\u0004ø\u009eè\u0010Êêì|$ö!H\u0016Â\u0017Tv.` \u0007:\u0016\u008dô\u0007È\u0099º\u0013ÿåé\u007fÄñÚK Ý%W\t)\u0010£c5a\u008fM\u0001X\u0094¨nÉà\u009ezêÌóFÁØÙR+$~¾[0P\u001b\u0011\u0091\u001d\u000f3\u0085$3E©U'wÝQK\u0099Á\u009c\u007f«õªcË\u0019Ý\u0097º\r¦ºD0\u0014®3$CÒHHtÆl|\u0087ê\u0085`¾\u001e¯\u0094Ë\u0002Á¸ö6ä£[Y'×0MUûDquïre\u0081\u0013Ã\u0089ñ\u0007í½Ý+À¡ö_ýÔ\u0013B\u0005ø1vP,ª¦´8\u008e²\u009b\u0004ã\u009eØ\u0010ÎêÊ| a\u0082ë\u009cu¦ÿ³IËÓð]æ§â1\b»X\u0005f\u008fz\u0019ncPíjw\u007fÀ\u009fJàÔ\u0080^Ç¨Ç2ò¼ò\u0006\u0014\u0090Y\u001a:d>î[xHÂiLlÙ\u009a#á\u00ad 7Â\u0081À\u000bä\u0095ù\u001f\u0002i\u0002ó,}*ÇXÜ@V^ÈdBqô\tn2à$\u001a \u008cÊ\u0006\u009a¸¤2¸¤¬Þ\u0092P¨Ê½}]÷\"iBã\u0005\u0015\u0019\u008f5\u0001<»Ð-Ò§üÙüSÈÅ\u009b\u007f¯ñ©dM,ª¦´8\u008e²\u009b\u0004ã\u009eØ\u0010ÎêÊ| öpHNÂRTF.x B:W\u008d·\u0007È\u0099®\u0013ùåø\u007fÞñ\u009fK-Ý#W\u0005)\u0017£g5{\u008fP\u0001^\u0094§n\u0085à\u008b,ê¦\u008a8\u008e²\u008b\u0004ø\u009eù\u0010ÜêÊ|\u0015ö1H\u0010Â\u0019".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1330);
        d = cArr;
        h = 3636946755596166872L;
    }

    private static void A(int i, byte b, int i2, Object[] objArr) {
        int i3 = 105 - i;
        int i4 = (i2 * 4) + 1;
        int i5 = 4 - (b * 3);
        byte[] bArr = $$a;
        byte[] bArr2 = new byte[i4];
        int i6 = -1;
        int i7 = i4 - 1;
        if (bArr == null) {
            int i8 = (-i5) + i7;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i5++;
            i3 = i8;
        }
        while (true) {
            int i9 = i6 + 1;
            bArr2[i9] = (byte) i3;
            if (i9 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i10 = i7;
            int i11 = i3;
            int i12 = i5;
            int i13 = (-bArr[i5]) + i11;
            i7 = i10;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i9;
            i5 = i12 + 1;
            i3 = i13;
        }
    }

    static void init$0() {
        $$a = new byte[]{99, 114, -3, -82};
        $$b = Opcodes.IF_ICMPGE;
    }

    public abstract void a(o.eg.b bVar) throws o.eg.d;

    public void b(o.eg.b bVar) throws o.eg.d {
        int i = j + 97;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    public abstract i c(Context context);

    public abstract void c(o.eg.b bVar) throws o.eg.d;

    public abstract byte[][] k();

    public abstract String l();

    public abstract o.eg.b m() throws o.eg.d;

    public abstract j n();

    public void q() {
        int i = j + 21;
        f = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 88 / 0;
                break;
        }
    }

    public void s() {
        int i = f + 47;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public void t() {
        int i = f + 97;
        j = i % 128;
        int i2 = i % 2;
    }

    public c(C c, boolean z) {
        super(c, z);
        this.b = null;
        this.e = null;
        this.a = true;
    }

    /* JADX WARN: Code restructure failed: missing block: B:27:0x0126, code lost:
    
        if (o.i.d.c().b(g(), f(), h()) != false) goto L39;
     */
    @Override // o.y.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected final void j() {
        /*
            Method dump skipped, instructions count: 416
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.c.j():void");
    }

    /* renamed from: o.y.c$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\c$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int c;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            e = 0;
            c = 1;
            int[] iArr = new int[f.values().length];
            d = iArr;
            try {
                iArr[f.e.ordinal()] = 1;
                int i = e;
                int i2 = (i & 77) + (i | 77);
                c = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[f.a.ordinal()] = 2;
                int i3 = c;
                int i4 = (i3 & 27) + (i3 | 27);
                e = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[f.d.ordinal()] = 3;
                int i6 = (e + 32) - 1;
                c = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:106:0x0856  */
    /* JADX WARN: Removed duplicated region for block: B:108:0x0860 A[Catch: d -> 0x0888, all -> 0x08d6, TryCatch #2 {d -> 0x0888, blocks: (B:90:0x0730, B:93:0x079c, B:95:0x07a2, B:96:0x07eb, B:97:0x07ee, B:98:0x081f, B:99:0x07f3, B:100:0x07fe, B:101:0x0809, B:102:0x0814, B:103:0x0826, B:104:0x0852, B:107:0x085d, B:108:0x0860, B:109:0x0871), top: B:89:0x0730, outer: #6 }] */
    /* JADX WARN: Removed duplicated region for block: B:110:0x085b  */
    /* JADX WARN: Removed duplicated region for block: B:92:0x0793  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void v() {
        /*
            Method dump skipped, instructions count: 2726
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.c.v():void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:19:0x00fb, code lost:
    
        r12.e = null;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void y() {
        /*
            Method dump skipped, instructions count: 358
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.c.y():void");
    }

    @Override // o.y.a
    public String c() {
        StringBuilder append = new StringBuilder().append(e().a());
        Object[] objArr = new Object[1];
        z((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 1318 - (ViewConfiguration.getScrollDefaultDelay() >> 16), 12 - (KeyEvent.getMaxKeyCode() >> 16), objArr);
        String obj = append.append(((String) objArr[0]).intern()).toString();
        int i = f + 79;
        j = i % 128;
        switch (i % 2 != 0 ? 'E' : '`') {
            case 'E':
                int i2 = 65 / 0;
                return obj;
            default:
                return obj;
        }
    }

    private static o.bb.a d(f fVar, h hVar) {
        int i = j + Opcodes.LMUL;
        f = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? 'K' : 'F') {
            case 'K':
                f fVar2 = f.d;
                throw null;
            default:
                if (fVar == f.d) {
                    return o.bb.a.s;
                }
                if (fVar == f.a) {
                    int i2 = f + 35;
                    j = i2 % 128;
                    if (i2 % 2 == 0) {
                        return o.bb.a.k;
                    }
                    o.bb.a aVar = o.bb.a.k;
                    obj.hashCode();
                    throw null;
                }
                if (hVar != null) {
                    int i3 = j + 97;
                    f = i3 % 128;
                    switch (i3 % 2 == 0 ? '5' : 'M') {
                        case 'M':
                            if (hVar == h.a) {
                                o.bb.a aVar2 = o.bb.a.f39o;
                                int i4 = f + 51;
                                j = i4 % 128;
                                int i5 = i4 % 2;
                                return aVar2;
                            }
                            break;
                        default:
                            h hVar2 = h.a;
                            obj.hashCode();
                            throw null;
                    }
                }
                return null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0011. Please report as an issue. */
    public boolean o() {
        int i = f + 51;
        int i2 = i % 128;
        j = i2;
        switch (i % 2 != 0 ? 'P' : '\n') {
        }
        int i3 = i2 + 19;
        f = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return true;
        }
    }

    public boolean r() {
        int i = f + 33;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        int i4 = i2 + Opcodes.LREM;
        f = i4 % 128;
        int i5 = i4 % 2;
        return true;
    }

    public o.bb.a d(g gVar) {
        int i = f + Opcodes.DDIV;
        j = i % 128;
        int i2 = i % 2;
        o.bb.a d2 = d(gVar.j(), gVar.h());
        switch (d2 == null) {
            case true:
                o.bb.a d3 = gVar.b().e().d();
                int i3 = f + 25;
                j = i3 % 128;
                int i4 = i3 % 2;
                return d3;
            default:
                int i5 = j + 83;
                f = i5 % 128;
                int i6 = i5 % 2;
                return d2;
        }
    }

    public o.bb.a c(int i) {
        int i2 = j + 71;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                return null;
        }
    }

    public boolean e(int i) {
        int i2 = f + 43;
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        int i5 = i3 + 37;
        f = i5 % 128;
        int i6 = i5 % 2;
        return false;
    }

    public boolean p() {
        int i = f;
        int i2 = i + 5;
        j = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 91;
        j = i4 % 128;
        int i5 = i4 % 2;
        return false;
    }

    public boolean u() {
        int i = f;
        int i2 = i + 63;
        j = i2 % 128;
        boolean z = false;
        switch (i2 % 2 == 0) {
            case false:
                z = true;
                break;
        }
        int i3 = i + 85;
        j = i3 % 128;
        int i4 = i3 % 2;
        return z;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void z(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 708
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.c.z(char, int, int, java.lang.Object[]):void");
    }
}
