package o.ef;

import java.io.File;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ef\a.smali */
public final class a {
    private static int c = 0;
    private static int d = 1;
    private final File a;
    private final b e;

    public a(File file, b bVar) {
        this.a = file;
        this.e = bVar;
    }

    public final File d() {
        File file;
        int i = c;
        int i2 = ((i | 43) << 1) - (i ^ 43);
        int i3 = i2 % 128;
        d = i3;
        switch (i2 % 2 == 0 ? '(' : 'O') {
            case '(':
                file = this.a;
                int i4 = 27 / 0;
                break;
            default:
                file = this.a;
                break;
        }
        int i5 = i3 + 11;
        c = i5 % 128;
        int i6 = i5 % 2;
        return file;
    }

    public final b b() {
        int i = c;
        int i2 = (i ^ 55) + ((i & 55) << 1);
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return this.e;
        }
    }
}
