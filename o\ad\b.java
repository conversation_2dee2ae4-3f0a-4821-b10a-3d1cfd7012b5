package o.ad;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.l;
import o.ee.g;
import o.h.d;
import o.i.i;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ad\b.smali */
public abstract class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static char[] d;
    private static char[] e;
    private static int f;
    private static int i;
    public final o.c.a a;
    public final o.j.b c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        a();
        int i2 = i + 63;
        f = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void a() {
        e = new char[]{50849, 50698, 50700, 50716, 50716, 50691, 50717, 50712, 50702, 50806, 50713, 50704, 50704, 50718, 50689, 50712, 50696, 50702, 50709, 50714, 50716, 50716, 50718, 50801, 50696, 50707, 50709, 50717, 50718, 50710, 50709, 50717, 50689, 50713, 50709, 50715, 50846, 50770, 50797, 50792, 50796, 50792, 50796, 50762, 50754, 50789, 50799, 50775, 50768, 50792, 50799, 50775, 50779, 50771, 50799, 50797, 50799, 50870, 50839, 50839, 50875, 50775, 50775, 50875, 50870, 50799, 50792, 50867, 50874, 50774, 50770, 50768, 50875, 50873, 50774, 50794, 50765, 50864, 50789, 50798, 50799, 50771, 50776, 50774, 50769, 50875, 50873, 50770, 50789, 50799, 50775, 50768, 50792, 50799, 50775, 50779, 50771, 50799, 50797, 50799, 50870, 50869, 50770, 50870, 50873, 50770, 50789, 50799, 50775, 50768, 50792, 50799, 50775, 50779, 50771, 50799, 50797, 50799, 50753, 50752, 50799, 50792, 50797, 50799, 50791, 50816, 50774, 50783, 50858, 50816, 50816, 50851, 50776, 50782, 50776, 50780, 50756, 50752, 50776, 50773, 50781, 50752, 50776, 50774, 50767, 50871, 50777, 50773, 50777, 50773, 50782, 50783, 50779, 50782, 50778, 50773, 50851, 50877, 50776, 50854, 50876, 50768, 50776, 50782, 50773, 50776, 50765, 50866, 50776, 50782, 50776, 50780, 50756, 50752, 50776, 50773, 50781, 50752, 50851, 50705, 50712, 50715, 50705, 50713, 50713, 50815, 50790, 50712, 50718, 50694, 50718, 50710, 50814, 50813, 50714, 50712, 50714, 50718, 50694, 50690, 50714, 50711, 50719, 50690, 50714, 50704, 50713, 50788, 50790, 50690, 50718, 50690, 50713, 50710, 50713, 50784, 50754, 50754, 50813, 50714, 50712, 50714, 50718, 50694, 50690, 50714, 50711, 50719, 50690, 50714, 50704, 50697, 50801, 50715, 50711, 50715, 50711, 50712, 50713, 50937, 50851, 50839, 50838, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50861, 50857, 50879, 50877, 50850, 50877, 50878, 50849, 50857, 50831, 50923, 50923, 50826, 50851, 50826, 50923, 50923, 50828, 50855, 50854, 50857, 50857, 50852, 50876, 50851, 50856, 50859, 50850, 50820, 50820, 50878, 50855, 50855, 50873, 50873, 50843, 50912, 50927, 50817, 50937, 50851, 50839, 50838, 50873, 50851, 50859, 50852, 50876, 50851, 50859, 50863, 50855, 50851, 50849, 50851, 50860, 50863, 50855, 50863, 50855, 50849, 50833, 50857, 50879, 50877, 50850, 50877, 50878, 50849, 50857, 50831, 50923, 50923, 50826, 50851, 50826, 50923, 50923, 50828, 50855, 50854, 50857, 50857, 50852, 50876, 50851, 50856, 50859, 50850, 50820, 50820, 50878, 50855, 50855, 50873, 50873, 50843, 50912, 50927, 50817};
        d = new char[]{35338, 26587, 20871, 17221, 15628, 11984, 6329, 2643, 58429, 53754, 50092, 48543, 44894, 39170, 35525, 25729, 22137, 16426, 15869, 12197, 6510, 2918, 58705, 54930, 49291, 45659, 44055, 39406, 35775, 25979, 22313, 16624, 12952, 11405, 7745, 2060, 58822, 55183, 49535, 45883, 44269, 40608, 34928, 31318, 21513, 16790, 13197, 11607, 7980, 2294, 64177, 54372, 50740, 46063, 44511, 40851, 35164, 31499, 21696, 18170, 12391, 8767, 8184, 2474, 64472, 54532, 51028, 23491, 46610, 32846, 37516, 60613, 65305, 51568, 56218, 13812, '3', 4709, 27734, 32407, 18635, 23308, 46408, 34736, 37347, 60468, 65132, 51367, 55983, 13464, 1883, 4436, 25481, 32200, 18475, 23145, 46266, 34534, 37183, 58132, 64851, 53213, 55746, 13340, 1627, 4285, 25342, 32047, 20339, 22948, 43920, 34264, 36875, 57932, 64644, 52991, 55671, 11131, 1452, 6140, 25121, 31761, 11451, 49514, 63286, 58868, 39869, 34913, 48648, 44258, 17036, 30539, 25885, 6958, 2543, 16307, 11380, 49712, 61640, 59035, 39756, 35092, 49119, 44503, 17376, 28707, 26159, 5374, 2745, 16194, 11525, 50055, 61852, 58950, 38013, 35367, 47328, 44725, 17253, 28990, 26574, 5506, 2637, 14358, 11994, 56549, 62177, 59250, 38189, 35831, 47496, 44635, 23552, 29339, 24707, 5458, 2940, 14646, 12284, 56748, 62049, 57419, 38599, 33928, 47432, 44829, 23853, 11451, 49514, 63286, 58868, 39869, 34913, 48648, 44258, 17036, 30539, 25885, 6958, 2543, 16307, 11380, 49712, 61640, 59035, 39756, 35092, 49119, 44503, 17376, 28707, 26164, 5366, 2726, 16216, 11528, 50121, 61850, 58899, 37992, 35386, 47345, 44723, 17268, 28985, 26585, 5514, 2650, 14366, 11969, 56546, 62126, 59241, 38269, 35814, 47513, 44619, 23556, 29391, 24724, 5399, 2943, 14630, 12264, 56746, 62064, 57368, 38613, 43927, 17990, 28698, 25304, 7313, 3917, 14628, 11214, 50592, 61543, 57905, 39938, 36547, 47263, 43864, 17692, 30692, 25015, 7264, 3640, 14579, 11003, 50380, 63247, 57600, 37853, 36252, 47231, 43581, 17646, 30386, 24939, 4928, 3335, 16265, 10646, 50248, 62991, 57577, 37546, 36219, 48935, 43504, 23492, 30092, 24671, 4632, 3280, 16043, 10531, 56124, 62951, 59321, 37498, 35925, 48650, 43157, 23169, 30044, 26422, 4600, 942, 15970, 10283, 55877, 62661, 59014, 37186, 33555, 48607};
        b = -2711740633814482673L;
    }

    static void init$0() {
        $$a = new byte[]{48, 67, 97, 27};
        $$b = Opcodes.LREM;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 4
            int r9 = r9 * 2
            int r9 = 1 - r9
            byte[] r0 = o.ad.b.$$a
            int r8 = 122 - r8
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1c:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L33:
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.b.l(int, byte, int, java.lang.Object[]):void");
    }

    protected abstract boolean c();

    protected abstract boolean e(d dVar);

    public b(Context context, o.c.a aVar) {
        this.a = aVar;
        this.c = o.j.c.d(context);
    }

    /* JADX WARN: Code restructure failed: missing block: B:77:0x00df, code lost:
    
        if (r4 != null) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x00e5, code lost:
    
        if (c() == false) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x00e7, code lost:
    
        o.ee.g.c();
        r4 = new java.lang.Object[1];
        g("\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000", new int[]{36, 89, 52, 0}, false, r4);
        o.ee.g.d(r0, ((java.lang.String) r4[0]).intern());
        r4 = o.ad.c.d(d());
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x010b, code lost:
    
        r4 = o.ad.c.i();
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x00d7, code lost:
    
        continue;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.ad.c c(o.h.d r18) {
        /*
            Method dump skipped, instructions count: 734
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.b.c(o.h.d):o.ad.c");
    }

    protected final boolean e() {
        int i2 = f + 109;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                o.j.b bVar = o.j.b.b;
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                switch (this.c != o.j.b.b ? '\n' : '8') {
                    case '8':
                        break;
                    default:
                        if (this.c != o.j.b.c) {
                            return true;
                        }
                        break;
                }
                g.c();
                Object[] objArr = new Object[1];
                g("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000", new int[]{0, 36, Opcodes.FMUL, 8}, false, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                g("\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{239, 55, 0, 0}, false, objArr2);
                g.d(intern, String.format(((String) objArr2[0]).intern(), this.c));
                int i3 = f + 23;
                i = i3 % 128;
                int i4 = i3 % 2;
                return false;
        }
    }

    public boolean b() {
        switch (this.c != o.j.b.a ? Typography.amp : 'W') {
            case '&':
                int i2 = i + 59;
                f = i2 % 128;
                if (i2 % 2 == 0) {
                    o.j.b bVar = o.j.b.c;
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                if (this.c != o.j.b.c) {
                    switch (this.c != o.j.b.d) {
                        case false:
                            break;
                        default:
                            g.c();
                            Object[] objArr = new Object[1];
                            g("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000", new int[]{0, 36, Opcodes.FMUL, 8}, false, objArr);
                            String intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            g("\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{294, 61, 0, 0}, false, objArr2);
                            g.d(intern, String.format(((String) objArr2[0]).intern(), this.c));
                            return false;
                    }
                }
                break;
        }
        int i3 = f + 49;
        i = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return true;
            default:
                int i4 = 13 / 0;
                return true;
        }
    }

    private static List<o.i.g> d() {
        int i2 = f + Opcodes.DMUL;
        i = i2 % 128;
        int i3 = i2 % 2;
        List<o.i.g> d2 = o.i.d.c().d(i.a);
        int i4 = i + Opcodes.DMUL;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 22 : (char) 21) {
            case 21:
                return d2;
            default:
                int i5 = 70 / 0;
                return d2;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v28, types: [byte[]] */
    private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
        int i2;
        char[] cArr;
        ?? r0 = str;
        switch (r0 != 0 ? 'L' : '\\') {
            case Base64.mimeLineLength /* 76 */:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i3 = 0;
        int i4 = iArr[0];
        int i5 = iArr[1];
        int i6 = 2;
        int i7 = iArr[2];
        int i8 = iArr[3];
        char[] cArr2 = e;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i9 = 0;
            while (true) {
                switch (i9 < length ? i3 : 1) {
                    case 1:
                        cArr2 = cArr3;
                        break;
                    default:
                        int i10 = $11 + 59;
                        $10 = i10 % 128;
                        if (i10 % i6 != 0) {
                        }
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i3] = Integer.valueOf(cArr2[i9]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr = cArr2;
                            } else {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 11, (char) (ViewConfiguration.getLongPressTimeout() >> 16), 44 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)));
                                byte b2 = (byte) i3;
                                byte b3 = (byte) (b2 + 2);
                                cArr = cArr2;
                                Object[] objArr3 = new Object[1];
                                l(b2, b3, (byte) (b3 - 2), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr3[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i9++;
                            cArr2 = cArr;
                            i3 = 0;
                            i6 = 2;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        char[] cArr4 = new char[i5];
        System.arraycopy(cArr2, i4, cArr4, 0, i5);
        if (bArr != null) {
            char[] cArr5 = new char[i5];
            lVar.d = 0;
            char c = 0;
            while (true) {
                switch (lVar.d < i5 ? '1' : (char) 27) {
                    case 27:
                        cArr4 = cArr5;
                        break;
                    default:
                        if (bArr[lVar.d] == 1) {
                            int i11 = $11 + 1;
                            $10 = i11 % 128;
                            if (i11 % 2 != 0) {
                                int i12 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = o.e.a.s.get(2016040108);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(((Process.getThreadPriority(0) + 20) >> 6) + 11, (char) TextUtils.getOffsetAfter("", 0), KeyEvent.getDeadChar(0, 0) + 448);
                                        byte b4 = (byte) 0;
                                        byte b5 = (byte) (b4 + 3);
                                        Object[] objArr5 = new Object[1];
                                        l(b4, b5, (byte) (b5 - 3), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj2);
                                    }
                                    cArr5[i12] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    Object obj3 = null;
                                    obj3.hashCode();
                                    throw null;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                            int i13 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                Object obj4 = o.e.a.s.get(2016040108);
                                if (obj4 == null) {
                                    Class cls3 = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 449 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)));
                                    byte b6 = (byte) 0;
                                    byte b7 = (byte) (b6 + 3);
                                    Object[] objArr7 = new Object[1];
                                    l(b6, b7, (byte) (b7 - 3), objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj4);
                                }
                                cArr5[i13] = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        } else {
                            int i14 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                Object obj5 = o.e.a.s.get(804049217);
                                if (obj5 == null) {
                                    Class cls4 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0', 0, 0) + 11, (char) Color.green(0), 206 - TextUtils.lastIndexOf("", '0', 0));
                                    byte b8 = (byte) 0;
                                    byte b9 = b8;
                                    Object[] objArr9 = new Object[1];
                                    l(b8, b9, b9, objArr9);
                                    obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(804049217, obj5);
                                }
                                cArr5[i14] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                        c = cArr5[lVar.d];
                        try {
                            Object[] objArr10 = {lVar, lVar};
                            Object obj6 = o.e.a.s.get(-2112603350);
                            if (obj6 == null) {
                                Class cls5 = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) Color.argb(0, 0, 0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 259);
                                byte b10 = (byte) 0;
                                Object[] objArr11 = new Object[1];
                                l(b10, (byte) (b10 | 56), b10, objArr11);
                                obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj6);
                            }
                            ((Method) obj6).invoke(null, objArr10);
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                }
            }
        }
        switch (i8 > 0 ? '\r' : (char) 31) {
            case 31:
                i2 = 0;
                break;
            default:
                char[] cArr6 = new char[i5];
                i2 = 0;
                System.arraycopy(cArr4, 0, cArr6, 0, i5);
                int i15 = i5 - i8;
                System.arraycopy(cArr6, 0, cArr4, i15, i8);
                System.arraycopy(cArr6, i8, cArr4, 0, i15);
                break;
        }
        if (z) {
            char[] cArr7 = new char[i5];
            lVar.d = i2;
            while (lVar.d < i5) {
                cArr7[lVar.d] = cArr4[(i5 - lVar.d) - 1];
                lVar.d++;
            }
            cArr4 = cArr7;
        }
        switch (i7 > 0 ? 'P' : '=') {
            case 'P':
                lVar.d = 0;
                while (lVar.d < i5) {
                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                    lVar.d++;
                    int i16 = $10 + 65;
                    $11 = i16 % 128;
                    int i17 = i16 % 2;
                }
                break;
        }
        objArr[0] = new String(cArr4);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ad.b.k(char, int, int, java.lang.Object[]):void");
    }
}
