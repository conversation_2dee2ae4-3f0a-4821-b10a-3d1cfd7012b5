package com.esotericsoftware.minlog;

import java.io.PrintWriter;
import java.io.StringWriter;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\minlog\Log.smali */
public class Log {
    public static final int LEVEL_DEBUG = 2;
    public static final int LEVEL_ERROR = 5;
    public static final int LEVEL_INFO = 3;
    public static final int LEVEL_NONE = 6;
    public static final int LEVEL_TRACE = 1;
    public static final int LEVEL_WARN = 4;
    private static int level = 3;
    public static boolean ERROR = true;
    public static boolean WARN = true;
    public static boolean INFO = true;
    public static boolean DEBUG = false;
    public static boolean TRACE = false;
    private static Logger logger = new Logger();

    public static void set(int level2) {
        level = level2;
        ERROR = level2 <= 5;
        WARN = level2 <= 4;
        INFO = level2 <= 3;
        DEBUG = level2 <= 2;
        TRACE = level2 <= 1;
    }

    public static void NONE() {
        set(6);
    }

    public static void ERROR() {
        set(5);
    }

    public static void WARN() {
        set(4);
    }

    public static void INFO() {
        set(3);
    }

    public static void DEBUG() {
        set(2);
    }

    public static void TRACE() {
        set(1);
    }

    public static void setLogger(Logger logger2) {
        logger = logger2;
    }

    public static void error(String message, Throwable ex) {
        if (ERROR) {
            logger.log(5, null, message, ex);
        }
    }

    public static void error(String category, String message, Throwable ex) {
        if (ERROR) {
            logger.log(5, category, message, ex);
        }
    }

    public static void error(String message) {
        if (ERROR) {
            logger.log(5, null, message, null);
        }
    }

    public static void error(String category, String message) {
        if (ERROR) {
            logger.log(5, category, message, null);
        }
    }

    public static void warn(String message, Throwable ex) {
        if (WARN) {
            logger.log(4, null, message, ex);
        }
    }

    public static void warn(String category, String message, Throwable ex) {
        if (WARN) {
            logger.log(4, category, message, ex);
        }
    }

    public static void warn(String message) {
        if (WARN) {
            logger.log(4, null, message, null);
        }
    }

    public static void warn(String category, String message) {
        if (WARN) {
            logger.log(4, category, message, null);
        }
    }

    public static void info(String message, Throwable ex) {
        if (INFO) {
            logger.log(3, null, message, ex);
        }
    }

    public static void info(String category, String message, Throwable ex) {
        if (INFO) {
            logger.log(3, category, message, ex);
        }
    }

    public static void info(String message) {
        if (INFO) {
            logger.log(3, null, message, null);
        }
    }

    public static void info(String category, String message) {
        if (INFO) {
            logger.log(3, category, message, null);
        }
    }

    public static void debug(String message, Throwable ex) {
        if (DEBUG) {
            logger.log(2, null, message, ex);
        }
    }

    public static void debug(String category, String message, Throwable ex) {
        if (DEBUG) {
            logger.log(2, category, message, ex);
        }
    }

    public static void debug(String message) {
        if (DEBUG) {
            logger.log(2, null, message, null);
        }
    }

    public static void debug(String category, String message) {
        if (DEBUG) {
            logger.log(2, category, message, null);
        }
    }

    public static void trace(String message, Throwable ex) {
        if (TRACE) {
            logger.log(1, null, message, ex);
        }
    }

    public static void trace(String category, String message, Throwable ex) {
        if (TRACE) {
            logger.log(1, category, message, ex);
        }
    }

    public static void trace(String message) {
        if (TRACE) {
            logger.log(1, null, message, null);
        }
    }

    public static void trace(String category, String message) {
        if (TRACE) {
            logger.log(1, category, message, null);
        }
    }

    private Log() {
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\minlog\Log$Logger.smali */
    public static class Logger {
        private final long firstLogTime = System.currentTimeMillis();

        public void log(int level, String category, String message, Throwable ex) {
            StringBuilder builder = new StringBuilder(256);
            long time = System.currentTimeMillis() - this.firstLogTime;
            long minutes = time / 60000;
            long seconds = (time / 1000) % 60;
            if (minutes <= 9) {
                builder.append('0');
            }
            builder.append(minutes);
            builder.append(':');
            if (seconds <= 9) {
                builder.append('0');
            }
            builder.append(seconds);
            switch (level) {
                case 1:
                    builder.append(" TRACE: ");
                    break;
                case 2:
                    builder.append(" DEBUG: ");
                    break;
                case 3:
                    builder.append("  INFO: ");
                    break;
                case 4:
                    builder.append("  WARN: ");
                    break;
                case 5:
                    builder.append(" ERROR: ");
                    break;
            }
            if (category != null) {
                builder.append('[');
                builder.append(category);
                builder.append("] ");
            }
            builder.append(message);
            if (ex != null) {
                StringWriter writer = new StringWriter(256);
                ex.printStackTrace(new PrintWriter(writer));
                builder.append('\n');
                builder.append(writer.toString().trim());
            }
            print(builder.toString());
        }

        protected void print(String message) {
            System.out.println(message);
        }
    }
}
