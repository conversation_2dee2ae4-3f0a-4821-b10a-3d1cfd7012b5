package fr.antelop.sdk.authentication.prompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\DeviceBiometricCustomerAuthenticationPrompt.smali */
public final class DeviceBiometricCustomerAuthenticationPrompt extends CustomerAuthenticationPrompt {
    private final String subtitle;
    private final String title;

    DeviceBiometricCustomerAuthenticationPrompt(String str, String str2) {
        this.title = str;
        this.subtitle = str2;
    }

    public final String getTitle() {
        return this.title;
    }

    public final String getSubtitle() {
        return this.subtitle;
    }
}
