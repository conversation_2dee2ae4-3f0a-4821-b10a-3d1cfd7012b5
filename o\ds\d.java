package o.ds;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\d.smali */
final class d implements b {
    private static char[] a;
    private static int c;
    private static int e = 1;

    static {
        c = 0;
        b();
        int i = e + 21;
        c = i % 128;
        int i2 = i % 2;
    }

    static void b() {
        a = new char[]{50767, 51145, 51151, 51145, 51145, 50737, 50738, 51145, 50722, 50724, 50743, 50739, 50736, 50738, 51150, 51150, 50746, 50750, 50738, 51145, 51148, 50751};
    }

    d() {
    }

    @Override // o.ds.b
    public final f d() {
        int i = c + 17;
        e = i % 128;
        int i2 = i % 2;
        f fVar = f.a;
        int i3 = e + 1;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return fVar;
            default:
                throw null;
        }
    }
}
