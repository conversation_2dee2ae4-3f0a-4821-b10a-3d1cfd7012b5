package o.bn;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.TelephonyManager;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\a.smali */
public final class a extends d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static char d;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        b = 1;
        d();
        View.getDefaultSize(0, 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        int i = b + 5;
        a = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        e = new char[]{17041, 30589, 30526, 30585, 17052, 30498, 30591, 30572, 17046, 17053, 17047, 30555, 30561, 30525, 30537, 30502, 30587, 30562, 30503, 30524, 30567, 30553, 17044, 30563, 17055, 17040, 30570, 30511, 30582, 30560, 17042, 17043, 30568, 30529, 30556, 30566};
        d = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bn.a.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r6 = r6 + 4
            int r8 = 73 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r6 = r6 + 1
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.a.i(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{114, -113, -41, 111};
        $$b = 63;
    }

    @Override // o.bn.d
    public final String c(Context context, boolean z) throws c {
        int i = b + 7;
        a = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(MotionEvent.axisFromString("") + 17, "\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\u0011\"\u000e\u001e\u0014\u0003", (byte) (3 - View.MeasureSpec.getSize(0)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 4, "\u0002 \u0011\u000f㗕", (byte) (42 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        String c = c(context);
        int i3 = a + 89;
        b = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    @Override // o.bn.d
    public final String b(Context context) throws c, o.bo.g {
        int i = b + 83;
        a = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(15 - TextUtils.lastIndexOf("", '0', 0, 0), "\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\u0011\"\u000e\u001e\u0014\u0003", (byte) (3 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(5 - TextUtils.indexOf("", ""), "\u0002 \u0011\u000f㗬", (byte) (Color.red(0) + 68), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        String a2 = a(context);
        switch (a2 != null ? (char) 14 : 'c') {
            case Opcodes.DADD /* 99 */:
                return "";
            default:
                int i3 = a + 25;
                b = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        int i4 = 15 / 0;
                        return a2;
                    default:
                        return a2;
                }
        }
    }

    @Override // o.bn.d
    public final String e(Context context) throws c {
        int i = b + 43;
        a = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(View.MeasureSpec.getSize(0) + 16, "\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\u0011\"\u000e\u001e\u0014\u0003", (byte) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 3), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(TextUtils.indexOf("", "", 0) + 5, "\u0002 \u0011\u000f㗠", (byte) (55 - (Process.myTid() >> 22)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.ee.e.a();
        String c = o.c(o.ee.c.j(context));
        int i3 = a + 49;
        b = i3 % 128;
        switch (i3 % 2 == 0 ? 'E' : (char) 19) {
            case 'E':
                int i4 = 17 / 0;
                return c;
            default:
                return c;
        }
    }

    @Override // o.bn.d
    public final int a() {
        int i = a + 13;
        int i2 = i % 128;
        b = i2;
        int i3 = i % 2;
        int i4 = i2 + 109;
        a = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 1 : ' ') {
            case 1:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return 1;
        }
    }

    private static String c(Context context) {
        int i = a + 77;
        b = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 16, "\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\u0011\"\u000e\u001e\u0014\u0003", (byte) (View.combineMeasuredStates(0, 0) + 3), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 18, "\u0002 \u0016\u0004\u0005\u0017\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\f\u0016㗕", (byte) (35 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f(Color.red(0) + 5, "\b\u0012\u0018\u0011㘞", (byte) (31 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), objArr3);
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(((String) objArr3[0]).intern());
        switch (telephonyManager == null ? '1' : (char) 28) {
            case '1':
                g.c();
                Object[] objArr4 = new Object[1];
                f(17 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\u0011\"\u000e\u001e\u0014\u0003", (byte) (TextUtils.indexOf("", "") + 3), objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                f(42 - Color.green(0), "\u0002 \u0016\u0004\u0005\u0017\u0011 \u000e\u001e\u0019\u0002\u0007\u0000\u001e\u0011\f\u0016\u0015!\u0003\u001d#\u001b\u001d\t\u001d\u0014\u0018\b\u0017\u001a\u0010\u0018\u001c!\u0019\u0002\u0005!\b\u0019", (byte) (79 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), objArr5);
                g.d(intern2, ((String) objArr5[0]).intern());
                int i3 = b + Opcodes.DDIV;
                a = i3 % 128;
                int i4 = i3 % 2;
                return "";
            default:
                switch (Build.VERSION.SDK_INT >= 29 ? 'E' : (char) 15) {
                    case 'E':
                        int i5 = a + 63;
                        b = i5 % 128;
                        int i6 = i5 % 2;
                        return telephonyManager.getTypeAllocationCode();
                    default:
                        return "";
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:124:0x001b, code lost:
    
        r1 = r27.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:129:0x0019, code lost:
    
        if (r27 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x00d0, code lost:
    
        r5 = r11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0013, code lost:
    
        if (r27 != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0020, code lost:
    
        r1 = r27;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r26, java.lang.String r27, byte r28, java.lang.Object[] r29) {
        /*
            Method dump skipped, instructions count: 1050
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.a.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
