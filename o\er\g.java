package o.er;

import android.graphics.ImageFormat;
import android.text.TextUtils;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import kotlin.text.Typography;
import o.ah.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\g.smali */
public final class g extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        b = 1;
        d();
        TextUtils.lastIndexOf("", '0');
        int i = b + 31;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void d() {
        e = 3062025765791188998L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 4
            int r6 = r6 * 2
            int r6 = 1 - r6
            int r7 = r7 * 2
            int r7 = 114 - r7
            byte[] r0 = o.er.g.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1c:
            int r8 = r8 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r4 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.g.g(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{105, 1, -115, -23};
        $$b = Opcodes.ISHL;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = b + 39;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 21 : '\f') {
            case '\f':
                boolean b2 = super.b();
                int i2 = b + 11;
                a = i2 % 128;
                switch (i2 % 2 != 0 ? '_' : '\r') {
                    case Opcodes.SWAP /* 95 */:
                        int i3 = 35 / 0;
                        return b2;
                    default:
                        return b2;
                }
            default:
                super.b();
                throw null;
        }
    }

    public g(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        a[] aVarArr;
        int i = a + Opcodes.LSHR;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                aVarArr = new a[]{this.d.c()};
                break;
            default:
                aVarArr = new a[0];
                aVarArr[1] = this.d.c();
                break;
        }
        int i2 = a + 19;
        b = i2 % 128;
        switch (i2 % 2 == 0 ? '8' : '0') {
            case '8':
                throw null;
            default:
                return aVarArr;
        }
    }

    private String a() {
        String d = this.d.c().d();
        switch (d == null ? '\'' : Typography.less) {
            case '<':
                break;
            default:
                int i = a + 89;
                b = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f("ໟ\uefdd첲궜詰歬䠩⛧ߚ\ue49f얍ꉲ荚怛廱㿄Ვﶆ\uda7e뭟頛盼埝", 57636 - ImageFormat.getBitsPerPixel(0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("\u0ef1ߜ\u1cbcᕚ⩟⌉㧤人䞀居唩毰惃禁蹭蜸鰈鋙ꮹꂟ륎츨쓮\uddc1튰\ueb60\ue03d句ྒӨᴴቲ⬘⇸㛊侗䑯嵽刘棐懮癶轅萇骢鎱ꢍꅄ똶켅엙\udab2팺\ue85d\ue11d\uf7fbಪքᩂጭ⦲㻋㞕䱪䔳婙僩榨纙睒谯苶鯏邅ꥥ븷띂춘실\udb42큕\ue92cﾸ\uf4dfඒɨ᭼ဋ⛛㾧㑱䵖䈞壱凴暉罙瑹贉菎颾酵ꘌ뼏뗯쪥쎔\ud856터\ue7fb", 2350 - TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f("\u0ef8쾢豿䫦ஏ젥蛜䞎Р싑荱䀜ẳ\udf7d鰅媬ᭈ", View.MeasureSpec.makeMeasureSpec(0, 0) + 49499, objArr3);
                d = ((String) objArr3[0]).intern();
                break;
        }
        int i3 = a + 67;
        b = i3 % 128;
        switch (i3 % 2 == 0 ? 'H' : ']') {
            case Opcodes.DUP2_X1 /* 93 */:
                return d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final o.v.i<o.dw.e> e() {
        String a2 = a();
        o.eo.e eVar = this.c;
        boolean b2 = b();
        e.a aVar = e.a.b;
        o.dw.b bVar = new o.dw.b() { // from class: o.er.g.2
            private static int d = 0;
            private static int a = 1;

            @Override // o.dw.b
            public final int getThemeResource(o.dw.e eVar2) {
                int i = d;
                int i2 = (i & 11) + (i | 11);
                a = i2 % 128;
                int i3 = i2 % 2;
                o.dw.e.a();
                int i4 = R.style.antelopSecureCardDisplayThemeInternal;
                int i5 = a;
                int i6 = (i5 ^ 17) + ((i5 & 17) << 1);
                d = i6 % 128;
                switch (i6 % 2 == 0) {
                    case true:
                        return i4;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }
        };
        Object[] objArr = new Object[1];
        f("໕ؼὲᒓⷾ┈㨧㍫䢢䇔夁湼杷粫痺贚艅鮈", View.getDefaultSize(0, 0) + 2251, objArr);
        o.v.i<o.dw.e> iVar = new o.v.i<>(a2, eVar, b2, aVar, bVar, ((String) objArr[0]).intern());
        int i = a + 51;
        b = i % 128;
        int i2 = i % 2;
        return iVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Removed duplicated region for block: B:66:0x0037  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0029  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 530
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.g.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
