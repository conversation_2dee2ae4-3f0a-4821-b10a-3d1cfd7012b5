package o.fn;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.PointerIconCompat;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.security.cert.X509Certificate;
import kotlin.text.Typography;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\i.smali */
public final class i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] k;
    private static int p;
    private static long r;
    private static int s;
    private String f;
    private String g;
    private String i;
    private String j;
    private X509Certificate l;
    private long m;
    private X509Certificate n;
    private d e = new d();
    private h d = new h();
    private a a = new a();
    private b c = new b();
    private j b = new j();
    private e h = new e();

    /* renamed from: o, reason: collision with root package name */
    private o.eg.b f88o = new o.eg.b();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        s = 0;
        p = 1;
        o();
        KeyEvent.keyCodeFromString("");
        PointF.length(0.0f, 0.0f);
        TextUtils.getOffsetBefore("", 0);
        View.getDefaultSize(0, 0);
        TextUtils.indexOf("", "", 0, 0);
        ViewConfiguration.getFadingEdgeLength();
        MotionEvent.axisFromString("");
        ViewConfiguration.getMinimumFlingVelocity();
        ExpandableListView.getPackedPositionForGroup(0);
        MotionEvent.axisFromString("");
        Color.blue(0);
        Color.rgb(0, 0, 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        ViewConfiguration.getScrollBarSize();
        TextUtils.indexOf("", "", 0);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ExpandableListView.getPackedPositionForChild(0, 0);
        TextUtils.getTrimmedLength("");
        ViewConfiguration.getEdgeSlop();
        ExpandableListView.getPackedPositionForGroup(0);
        TextUtils.lastIndexOf("", '0');
        SystemClock.elapsedRealtimeNanos();
        Color.alpha(0);
        ExpandableListView.getPackedPositionChild(0L);
        PointF.length(0.0f, 0.0f);
        TextUtils.getTrimmedLength("");
        ImageFormat.getBitsPerPixel(0);
        TextUtils.indexOf("", "", 0);
        View.resolveSizeAndState(0, 0, 0);
        View.resolveSizeAndState(0, 0, 0);
        KeyEvent.getDeadChar(0, 0);
        ExpandableListView.getPackedPositionForChild(0, 0);
        AudioTrack.getMinVolume();
        ViewConfiguration.getMaximumFlingVelocity();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        View.resolveSizeAndState(0, 0, 0);
        TextUtils.lastIndexOf("", '0', 0);
        View.MeasureSpec.getSize(0);
        ViewConfiguration.getScrollDefaultDelay();
        KeyEvent.getModifierMetaStateMask();
        TextUtils.getTrimmedLength("");
        ViewConfiguration.getDoubleTapTimeout();
        ExpandableListView.getPackedPositionChild(0L);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        ViewConfiguration.getEdgeSlop();
        Color.red(0);
        ExpandableListView.getPackedPositionChild(0L);
        AndroidCharacter.getMirror('0');
        int i = s + 43;
        p = i % 128;
        switch (i % 2 == 0 ? 'T' : (char) 27) {
            case Opcodes.BASTORE /* 84 */:
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$a = new byte[]{12, 95, -121};
        $$b = Opcodes.IFLT;
    }

    static void o() {
        char[] cArr = new char[1961];
        ByteBuffer.wrap(" ¯Dséw\u000eð³\u0087×U|\u001cáÝ\u0006æ«±Ï7t0\u0099Ç>\u0095¢\\Ç\u001dl&\u0091ñ6±Zrÿ\fdÍ\u0089\u0090-SR{÷ \u001cë\u0081¨%vJ\u0012ïÑ\u0014\u0090¹»ÝdB=çÎ\f\u0099°SÕ\u001cz×\u009fì\u0004³¨|Í?rÊ\u0097\u0084;J,¹HrÎ\u0019ªç\u0007®àf]\u00039Ì\u0092\u009d\u000fAèXE'!é\u009a½w_Ð\u0010LÙ)\u0097\u0082¹KA/\u008e\u0082Ñe\u0004Øa¼¡\u0017ô\u008a9m\u0012ÀE¤Ø\u001f\u009dòhUnÉ·¬°\u0007Ûú\u0005]L1\u0084\u0094á\u000f.â\u007fF£9È\u009cÒw\u001dêCN§!õ\u0084*\u007fsÒM¶\u0093)\u0098\u008c\u0003g|Û¯¾ê\u00115ô\fo\u0000ÃÂ¦\u0090\u0019;ütP·Ëà®Ø\u0001\täVX\u00973¨\u00960\tjí¿@\u008b;Å\u009e\u000bqCÕ¡Hî#?«¦Ïib6\u0085ã8\u0086\\F÷\u0013jÞ\u008dõ ¢D?ÿz\u0012\u008fµ\u0086)\u001fL\u0004ç*\u001aó½«Ñ~t\u0001ïÀ\u0002\u008c¦\u0017Ù}|\"\u0097ì\n¸®ZÁ\u0015dÜ\u009f\u00922¼V'É6lä\u0087Ï;T^\u000bñØ\u0014ý\u008f¢#{FwùÀ\u001c\u0089°\u001f+\u0013N&áô\u0004´=¨Ygô8\u0013í®\u0088ÊHa\u001düÐ\u001bû¶¬Ò1it\u0084\u0081#\u008d¿TÚ\u001aq3\u008cð+¡Gmâ\u0004yÍ\u0094Ñ0JOdê=\u0001å\u009c°8OW\u000eòÂ\tÙ¤³Àl_\"úö\u0011\u0094\u00ad[È\u0012gÜ\u0082ò\u0019éµ+Ðy,\u008cHlå)\u0002å¿\u0090Û\u0001p\níÔ\ný§µÃpx?\u0095Î2\u0092®\u0019Ë\u0003`,\u009dò:¶Vdó\u001bhÂ\u0085\u009c!B^)û%\u0010ü\u008d²)[F\u0018ãÉ\u0018\u0085µ¬Ñejö\u000e(£aD©ùÌ\u009d\u00036R«\u008eL\u0093áè\u0085'>nÓ\u008ctÂè\u001b\u008d{&jÛ¿|ø\u0010<µQ,ùH7, Hoå0\u0002å¿\u0080Û@p\u0015íØ\nó§¤Ã9x|\u0095\u00892\u0093®\\Ë\u0005`;\u009dè:¼Vgó\fhÅ\u0085Ù!B^lû5\u0010í\u008d¸)GF\u0006ãÊ\u0018Ñµ¯ÑnN+ëü\u0000\u0088¼UÙYvÇ\u0093ì\b³¤jÁ8~Æ\u009b\u008f7\u0019¬\u0018É:f¡\u0083¬?aTIñÕn\u0096\u008a\u0011'm\\ ùí\u0016´²\t/LD\u0099á\u0087\u001e¬ºs×*Løé\u0086\u0005O¢Yß\u008bt©, Hoå0\u0002å¿\u0080Û@p\u0015íØ\nó§¤Ã9x|\u0095\u00892\u0093®\\Ë\u0005`;\u009dè:¼Vgó\fhÅ\u0085Ù!B^lû5\u0010í\u008d¸)GF\u0006ãÊ\u0018Ñµ¯ÑnN+ëü\u0000\u0088¼UÙYvÇ\u0093ì\b³¤jÁ8~Æ\u009b\u008f7\u0019¬\u0018É:f¡\u0083¶?sT\u001añÎn\u0095\u008aT'}\\$ù¹\u0016ü²\t/\u0000DÛá\u009e\u001e»ºu×0Lÿé\u008e\u0005\u0001¢\u0010ßßtà\u0091µ\rpª0ÇÅ|\u0088\u0098C5\u0010R=Ïèd¶\u0080\u007f, Hoå0\u0002å¿\u0080Û@p\u0015íØ\nó§ Ãmx8\u0095Æ2\u008f®\u0019Ë\u0014`;\u009dó:¶VcóIh\u009b\u0085Ù!B^}û.\u0010ë\u008d´)MFAãÊ\u0018\u0094µ½ÑuN0ëÿ\u0000\u008e¼RÙYv×\u0093æ\b³¤tÁ0~Ý\u009bÁ7P¬\u0002Éifî\u0083»?bT\u0006ñÍn\u009c\u008aE'l\u0002\ffÃË\u009c,I\u0091,õì^¹Ãt$_\u0089\bí\u0095VÐ»%\u001c?\u0080ðå©N\u0097³D\u0014\u0010xËÝ Fi«u\u000fîpÀÕ\u0099>A£\u0014\u0007ëhªÍf6}\u009b\u0003ÿÂ`\u0087ÅP.$\u0092ù÷õXk½@&\u001f\u008aÆï\u0094Pjµ#\u0019µ\u0082´ç\u0096H\r\u00ad\u001b\u0011Òz±ß-@!¤ò\t\u0085r\u0089×T8\t\u009cà\u0001íj8Ï}0\u0013\u0094Èù\u0087bNÇ,+â\u008c»ñ=Z\u001f¿Mþ\u008a\u009aE7\u001aÐÏmª\tj¢??òØÙu\u008e\u0011\u0013ªVG£à¥||\u0019{²\u0010OÎè\u0087\u0084O!*ºåW´óh\u008c\u0003)\rÂÜ_\u0089ûn\u0094*1çÊûg\u0095\u0003N\u009c\u00019ÈÒªnd\u000b=¤»AÑÚ\u008evG\u0013\t¬êI®åe~>\u001b\u0007´\u008bQÞí\u001b\u0086\"#é¼¼XiõW\u008e\u0002+ÝÄ\u009c`#ý\"\u0096ý3²Ì\u0097hB\u0005\u0012\u009e×;ª×qp2\rï¦ÊC\u0084ß]±8Õ÷x¨\u009f}\"\u0018FØí\u008dp@\u0097k:8^õå \b^¯\u00173\u0081V\u008cý£\u0000k§.ËûnÑõ\u0003\u0018A¼ÇÃþfù\u008dr\u0010,´ÅÛ\u008d~H\u0085\u0007(6LêÓávo\u009d\u001e!ËD\u008cëH\u000ee\u0095y9÷\\¬ãC\u0006\nªÈ1\u0086T¿û9\u001e3¢ìÉ\u0085lKó\b\u0017ÌºçÁ¼deú÷\u009e83xÔ§iÒ\r\u001f¦J;ÆÜ´qå\u0015!®hCÞäÆx\u000b\u001dT¶mK¿ìý\u00802%W¾\u0098SÉ÷F\u0088--sÆº[òÿ\u0017\u0090X5\u0089ÎÕc¾\u0007$\u0098k=µÖÑj\u0003\u000f\\ \u0085E»Þå,\u009aHdå-\u0002å¿\u0080ÛOp\u001eíÂ\n©§³Ã|x\"\u0095Æ2\u0094®KË\u0012`,\u009dò:ùVró\bhÏ\u0085\u0097!^^}ûa\u0010û\u008d´)\tF\u0011ãË\u0018\u009eµªÑdN*ëâ\u0000\u008c¼EÙUv\u0091\u0093ç\b´¤uÁ=~\u0089\u009b\u008b7J¬\u001eÉ'f¡\u0083¶?sT\u0003ñÄn\u009a\u008aEh´\fj¡#Fëû\u008e\u009fA4\u0010©Ì,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§²Ã|x%\u0095Ý2\u0088®WË\u0016`:\u009d¡:·V~ó\rhÄ\u0085Ù!W^fû4\u0010÷\u008dµ)\tFLã\u0099\u0018\u0084µ¹ÑeN8ëå\u0000\u0080¼OÙ\u001ev\u0091\u0093ú\b¤¤mÁ%~À\u009b\u008f7^¬\u0002Z¹>}\u0093?tìÉ\u008e\u00adW\u0006\u0018\u009bÑJ\u0018.Õ\u0083\u0099dTÙ9½à\u0016ý\u008b8l\rÁ\u0006¥Ò\u001e\u0080ócT1Èø\u00ad§\u0006\u009eû\u0005\\\u00130Ú\u0095©\u000e`ã}Gó8Â\u009d\u0090vSë\u0011O\u00ad è\u0085=~ Ó\u001d·Á(\u009c\u008dAf$Úë¿º\u00105õNn\nÂÈ§\u009b\u0018yý QïÊ¦,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§¤Ãtx!\u0095Ý2\u0098®\u0019Ë\u0012`&\u009dô:·Veó\fhÓ\u0085\u008a!\u0011^hû3\u0010ë\u008d°)PFMã\u0099\u0018\u009fµ¦Ñ!N,ëá\u0000\u008d¼@Ù\rvÔò\u0012\u0096Ñ;\u0084ÜJa:\u0005Ú®®3gÔWy\u0010\u001dÆ¦¼Kaì=pà\u0015½¾\u0094C}ä\u0000\u0088Ý-´,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§´Ãix5\u0095È2\u0095®PË\u001f`.\u009d¡:ºVyó\fhÂ\u0085\u0092!\u0011^jû.\u0010÷\u008d·)@F\u0006ã\u0099\u0018\u0084µ¹ÑeN8ëå\u0000\u008c¼\u0001Ù\u001dvÐ\u0093ý\b¤,¾H`å5\u0002ý¿\u008cÛUp0íÕ~ú\u001a7·{P¶íÛ\u0089\u0002\"\u001f¿ÚXïõò\u0091/*sÇ\u008e`Óü\u0016\u0099Y2hÏçhè\u00046¡C:\u008b×Ús\u0003\fo©NB»,¹Hså6\u0002õ¿\u009cÛBp\ríÂ\nÚ§µÃxx%\u0095Ü2\u0092\u0086Ãâ\u000eOB¨\u008f\u0015âq;Ú&Gã Ö\rËi\u0016ÒJ?·\u0098ê\u0004/a`ÊQ7Þ\u0090Öü\u001cYyÂº/ó\u008b-ô\u0002Q\u001eº\u0095'Ú\u00837ìjI³²ý,¬Hmå0\u0002ö¿\u0080ÛCp\u0015íÔ\nÙ§³Ãvx5\u0095Ü2\u0082®MË\u0002,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§´Ãix5\u0095È2\u0095®PË\u001f`.\u009d¡:©Vcó\u0006hÅ\u0085\u008c!R^}ûa\u0010ü\u008d½)@F\u0006ãÐ\u0018\u0093µ ÑmN0ëå\u0000\u0090, Hrå*\u0002ä¿\u008cÛSp=íÐ\ný§ ì\u001a\u0088È%\u0090Â^\u007f6\u001bé°\u0094-jÊ_g\u0017\u0003Æ¸\u009fUZò?, Hrå*\u0002ä¿\u008cÛSp:íÝ\nà§¤Ãwx%\u0095à2\u0085Ï¿«r\u0006>áó\\\u009e8G\u0093Z\u000e\u009féªD\u0084 \u007f\u009b&vÉÑ\u008aM\u001a(\u0005\u0083+~îÙ¶µw\u0010\u001e\u008b\u0082fÕÂ\u0012½y\u0018'óùn§ÊX¥\u0007\u0000\u009aû\u0096V£2q\u00ad*\bþã\u008b_[:Z\u0095Ñpïë°Gn\";\u009dÌx\u008bÔYO\u0013*>\u0085ç`©,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§\u008fÃlx=\u0095Å2Á®nË\u0010`%\u009dí:¼VeóIhâ\u0085\u009c!C^}û(\u0010ÿ\u008d¸)JF\u0000ãÍ\u0018\u0094,äH,åt\u0002¼¿ÄÛcp<íö\nÀ§\u008fÃ9x\u0012\u0095ì2³®mË8`\u000f\u009dÈ:\u009aVPó=hä\u0085Ô!\u001c^$ûl\u0010´,äH,åt\u0002¼¿ÄÛdp7íõ\n©§\u0082Ã\\x\u0003\u0095ý2¨®\u007fË8`\n\u009dÀ:\u008dVTóDh\u008c\u0085Ô!\u001c^$:\u0085,Ã\u0013ów>Úr=¿\u0080Òä\u000bO\u0016ÒÓ5æ\u0098Ùü7Grª\u008a\rË\u0091\u0002ô\u001e_E¢«\u0005äi*ÌOW\u0088ºß\u001e\u001da'Äz/³²¤\u0016F@\r$³\u0089ðn=ÓL.KJ\u0086çÊ\u0000\u0007½jÙ³r®ïk\b^¥aÁ\u008fzÊ\u009720s¬ºÉ¦bý\u009f\u00138\\T\u0092ñ÷j0\u0087g#¥\\\u009fùÂ\u0012\u000b\u009cÇø\nUF²\u008b\u000fæk?À\"]çºÒ\u0017ôs\rÈ\n%\u0081\u0082ÿ\u001e!{\u007fÐ@-\u009f\u008a\u0082æ.C{Ø©5ò\u0091&î\u0013KC Â=é\u00997öhS¶¨ã\u0005Ôa\u0013þA[\u008b°æ\f?¸ôÜ9qu\u0096¸+ÕO\fä\u0011yÔ\u009eá3ÚW4ìz\u0001\u0094¦Û:\u0014_\u0019ôE\t ®âÂ)gMü\u0088\u0011ÈµYÊ\u0002ol\u0084£\u0019í½\bÒOw\u0098\u008cÚ!àE=Út\u007fã\u0094\u0081,¼Hqå=\u0002ð¿\u009dÛDpYí\u009c\n©§\u0092Ã|x2\u0095Ü2\u0093®\\ËQ`\r\u009dè:ªVaó\u0005hÀ\u0085\u0080!\u0011^Jû$\u0010ë\u008d¥)@F\u0007ãÐ\u0018\u0092µ¨ÑuN<,\u0083Hrå6\u0002ÿ¿¬ÛYp\u001aíÔ\nù§µÃpx>\u0095Ç2Á®NË\u0019` \u009dí:¼V1ó\u001chÑ\u0085\u009d!P^}û(\u0010÷\u008d¶)\tF\u0012ãÜ\u0018\u0085µ½ÑhN7ëö\u0000\u009a¼\u0001ÙTv\u0091,¼Hqå=\u0002ð¿\u009dÛDp*íÔ\ný§µÃpx?\u0095Î2\u0092vÄ\u0012\u0010¿EXÈåã\u0081=*t·¼P\u0099ýÖ\u0099\u0007\"[Ïðhûô/\u0091f:VÇ\u0091`Ç\f\u001d©b2¹ßô{!\u0004\u001f¡VJÀ×Ës1\u001cv¹®BçïÄ\u008bX\u0014B±\u008dZ°æ(\u0083r,§É\u0093RÝþ\u0013\u009b[$µÁümlö(\u0093^<\u008dÙÌe\u0004\u000e0«²4óÐ'}\u001e\u0006\u0018£\u008fLÊè:u}\u001e£»ü0³T~ù\"\u001eî£\u008bÇD,¥Hnå:\u0002ð¿\u0085ÛD\u0016?ráß¿8a\u0085\u001eáÍJ\u0088×M,¨Htå-\u0002ù¿\u008cÛOp\ríØ\nê§ Ãmx8\u0095Æ2\u008f®tË\u0014`=\u009dé:¶Vuó\u001a,¨Htå-\u0002ù¿\u008cÛOp\ríØ\nê§ Ãmx8\u0095Æ2\u008f®iË\u0010`=\u009dõ:¼Vcó\u0007hÒ,ªHnå4\u0002ü¿\u0080ÛUp-íÞ\nÍ§¨Ãjx:\u0095\u00892Ì®\u0019Ë\u0002`,\u009dó:°Vpó\u0005hÈ\u0085\u0083!T^mûa\u0010ó\u008d¢)FF\u000fã\u0099\u0018Ëµé,ªHnå4\u0002ü¿\u0080ÛUp-íÞ\nÍ§¨Ãjx:\u0095\u00892Ì®\u0019Ë\u0014`$\u009dñ:\u00adVhóIhÓ\u0085\u009c!B^fû4\u0010ë\u008d²)LF\u0012,ªHnå4\u0002ü¿\u0080ÛUp-íÞ\nÍ§¨Ãjx:\u0095\u00892Ì®\u0019Ë\u0002`=\u009dî:«Vxó\u0007hÆ\u0085Ù!R^`û1\u0010ñ\u008d´)[F\u0004ãÝ\u0018Ñµ£ÑrN6ëÿ\u0000É¼UÙ\u0016v\u0091\u0093í\b¨¤jÁ:\u0098~ü\u008fQË¶\u0002\u000b4o¹ÄüY/¾\u0011\u0013Lw\u0090ÌÅ!;\u0086r\u001aä\u007fûÔÜ)\u0015\u008eHâ\u0089G´Ü/1a\u0095¸ê\u0080OÕ¤\n9K\u009dôòëW%¬`\u0001Xe\u0099úÐ_L´]\b\u0098m¤Âv'T,¯Hmå,\u0002â¿\u0081ÛHp\u0017íÖ\n©§²Ã|x%\u0095Ý2\u0088®WË\u0016`:\u009d¡:¸V\u007fó\rh\u0081\u0085\u009a!^^|û/\u0010í\u008d´)[F\u0012,¯Hmå,\u0002â¿\u0081ÛHp\u0017íÖ\n©§¶Ãxx=\u0095Å2\u0084®MË8`-,¯Hmå,\u0002â¿\u0081ÛHp\u0017íÖ\n©§¨Ãjx\"\u0095Ü2\u0084®KË5`(\u009dõ:¸,¯Hmå,\u0002â¿\u0081ÛHp\u0017íÖ\n©§±Ãkx>\u0095Í2\u0094®ZË\u0005`i\u009dô:ªVpó\u000ehÄ\u0085Ù!\\^hû/\u0010ø\u008d¶)LF\u0013ÔH°\u008a\u001dËú\u0005Gf#¯\u0088ð\u00151òN_J;\u009f\u0080Åm:Ê&V½3ù\u0098Àe\u0000ÂW®\u0091\u000b®\u00903}nÙ²¦\u008f\u0003Òè\u001bu\u0016Ñ\u008a¾ç\u001b*às,¯Hmå,\u0002â¿\u0081ÛHp\u0017íÖ".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1961);
        k = cArr;
        r = 1420036009089779713L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(byte r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = o.fn.i.$$a
            int r8 = r8 + 102
            int r6 = r6 * 2
            int r6 = 3 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r7 = r7 + r4
            int r6 = r6 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.i.u(byte, short, short, java.lang.Object[]):void");
    }

    public final void d(Context context) throws o.ei.i {
        Object obj;
        Object[] objArr = new Object[1];
        q((char) (57987 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 49, (Process.myPid() >> 22) + 17, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) (3073 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), ViewConfiguration.getScrollDefaultDelay() >> 16, TextUtils.getOffsetBefore("", 0) + 47, objArr2);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr2[0]).intern(), 0);
        Object[] objArr3 = new Object[1];
        q((char) TextUtils.indexOf("", "", 0, 0), 46 - TextUtils.indexOf((CharSequence) "", '0'), 1 - TextUtils.indexOf((CharSequence) "", '0'), objArr3);
        String string = sharedPreferences.getString(((String) objArr3[0]).intern(), "");
        switch (string.isEmpty() ? 'Q' : 'F') {
            case Opcodes.FASTORE /* 81 */:
                int i = s + Opcodes.LREM;
                p = i % 128;
                switch (i % 2 == 0 ? (char) 31 : 'O') {
                    case 31:
                        g.c();
                        Object[] objArr4 = new Object[1];
                        q((char) (Drawable.resolveOpacity(1, 1) * 6722), (ViewConfiguration.getEdgeSlop() >>> 8) * 72, (SystemClock.uptimeMillis() > 1L ? 1 : (SystemClock.uptimeMillis() == 1L ? 0 : -1)) + 13, objArr4);
                        obj = objArr4[0];
                        break;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        q((char) (Drawable.resolveOpacity(0, 0) + 26593), 66 - (ViewConfiguration.getEdgeSlop() >> 16), 64 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr5);
                        obj = objArr5[0];
                        break;
                }
                g.d(intern, ((String) obj).intern());
                return;
            default:
                g.c();
                Object[] objArr6 = new Object[1];
                q((char) (View.MeasureSpec.getSize(0) + 34566), (-16777087) - Color.rgb(0, 0, 0), 51 - (ViewConfiguration.getScrollBarSize() >> 8), objArr6);
                g.d(intern, ((String) objArr6[0]).intern());
                String d = new o.dd.e(context).d(string);
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr7 = new Object[1];
                q((char) (Color.green(0) + 4360), 180 - (Process.myPid() >> 22), 43 - TextUtils.lastIndexOf("", '0', 0), objArr7);
                g.d(intern, sb.append(((String) objArr7[0]).intern()).append(d).toString());
                switch (d != null ? (char) 7 : '\'') {
                    case 7:
                        if (!d.isEmpty()) {
                            try {
                                o.eg.b bVar = new o.eg.b(d);
                                Object[] objArr8 = new Object[1];
                                q((char) (17996 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 306 - AndroidCharacter.getMirror('0'), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 21, objArr8);
                                if (!bVar.b(((String) objArr8[0]).intern())) {
                                    g.c();
                                    Object[] objArr9 = new Object[1];
                                    q((char) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 53802), 569 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 76 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr9);
                                    g.e(intern, ((String) objArr9[0]).intern());
                                    Object[] objArr10 = new Object[1];
                                    q((char) (40344 - (ViewConfiguration.getScrollBarSize() >> 8)), TextUtils.getCapsMode("", 0, 0) + 644, 59 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr10);
                                    throw new o.ei.i(((String) objArr10[0]).intern());
                                }
                                int i2 = p + Opcodes.LUSHR;
                                s = i2 % 128;
                                int i3 = i2 % 2;
                                Object[] objArr11 = new Object[1];
                                q((char) (TextUtils.indexOf("", "", 0) + 17996), 258 - View.MeasureSpec.getSize(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 21, objArr11);
                                String r2 = bVar.r(((String) objArr11[0]).intern());
                                Object[] objArr12 = new Object[1];
                                q((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), 279 - KeyEvent.normalizeMetaState(0), View.MeasureSpec.makeMeasureSpec(0, 0) + 2, objArr12);
                                if (r2.equals(((String) objArr12[0]).intern())) {
                                    g.c();
                                    StringBuilder sb2 = new StringBuilder();
                                    Object[] objArr13 = new Object[1];
                                    q((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 281 - TextUtils.getCapsMode("", 0, 0), 73 - ExpandableListView.getPackedPositionGroup(0L), objArr13);
                                    g.d(intern, sb2.append(((String) objArr13[0]).intern()).append(r2).toString());
                                } else {
                                    if (Integer.parseInt(r2) < 4) {
                                        g.c();
                                        Object[] objArr14 = new Object[1];
                                        q((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 354 - KeyEvent.getDeadChar(0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 84, objArr14);
                                        g.e(intern, ((String) objArr14[0]).intern());
                                        Object[] objArr15 = new Object[1];
                                        q((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 438, 57 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr15);
                                        throw new o.ei.i(((String) objArr15[0]).intern());
                                    }
                                    g.c();
                                    StringBuilder sb3 = new StringBuilder();
                                    Object[] objArr16 = new Object[1];
                                    q((char) (11947 - Process.getGidForName("")), 495 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 74 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr16);
                                    g.d(intern, sb3.append(((String) objArr16[0]).intern()).append(r2).toString());
                                }
                                b(context, bVar, true);
                                return;
                            } catch (o.eg.d e) {
                                Object[] objArr17 = new Object[1];
                                q((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 54872), 703 - Color.green(0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 41, objArr17);
                                throw new o.ei.i(((String) objArr17[0]).intern());
                            }
                        }
                        break;
                }
                Object[] objArr18 = new Object[1];
                q((char) (ViewConfiguration.getTouchSlop() >> 8), 223 - ExpandableListView.getPackedPositionChild(0L), Color.green(0) + 34, objArr18);
                throw new o.ei.i(((String) objArr18[0]).intern());
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:21:0x028d  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x0297  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x034c  */
    /* JADX WARN: Removed duplicated region for block: B:31:0x03de  */
    /* JADX WARN: Removed duplicated region for block: B:33:0x03e6 A[FALL_THROUGH] */
    /* JADX WARN: Removed duplicated region for block: B:39:0x054f  */
    /* JADX WARN: Removed duplicated region for block: B:41:0x0557 A[Catch: d -> 0x0a8e, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:44:0x05dc A[Catch: d -> 0x0a8e, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:47:0x0663  */
    /* JADX WARN: Removed duplicated region for block: B:50:0x06c4  */
    /* JADX WARN: Removed duplicated region for block: B:61:0x072e A[Catch: d -> 0x0a8e, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:64:0x08c4 A[Catch: d -> 0x0a8e, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:65:0x08f7 A[Catch: d -> 0x0a8e, TRY_LEAVE, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:72:0x075b A[Catch: d -> 0x0a8e, TRY_LEAVE, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:82:0x0552  */
    /* JADX WARN: Removed duplicated region for block: B:84:0x03e7 A[Catch: d -> 0x0a8e, TRY_LEAVE, TryCatch #2 {d -> 0x0a8e, blocks: (B:4:0x0062, B:8:0x0093, B:9:0x0096, B:10:0x009d, B:13:0x0177, B:15:0x019f, B:17:0x01fd, B:19:0x0261, B:24:0x02a1, B:25:0x0327, B:28:0x0355, B:29:0x03d8, B:32:0x03e3, B:34:0x0444, B:36:0x0470, B:37:0x0521, B:40:0x0554, B:41:0x0557, B:42:0x05af, B:44:0x05dc, B:45:0x0636, B:48:0x066e, B:51:0x06c6, B:53:0x06cb, B:55:0x06d0, B:57:0x06d5, B:59:0x06df, B:61:0x072e, B:62:0x08ae, B:64:0x08c4, B:65:0x08f7, B:67:0x09ed, B:71:0x0a2a, B:72:0x075b, B:74:0x0846, B:78:0x087e, B:84:0x03e7, B:89:0x0401, B:93:0x0227, B:97:0x0a65, B:98:0x0a8d), top: B:2:0x005d, inners: #0, #1 }] */
    /* JADX WARN: Removed duplicated region for block: B:91:0x03e1  */
    /* JADX WARN: Removed duplicated region for block: B:92:0x0290  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.fk.e b(android.content.Context r31, o.eg.b r32, boolean r33) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 2796
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.i.b(android.content.Context, o.eg.b, boolean):o.fk.e");
    }

    private boolean e(Context context, o.eg.b bVar) throws o.eg.d, o.ei.i {
        boolean z;
        int i = p + 79;
        s = i % 128;
        int i2 = i % 2;
        g.c();
        boolean z2 = true;
        Object[] objArr = new Object[1];
        q((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 57987), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 49, 18 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 1536 - View.combineMeasuredStates(0, 0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 13, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (bVar == null) {
            Object[] objArr3 = new Object[1];
            q((char) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 23161), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1550, 64 - ExpandableListView.getPackedPositionType(0L), objArr3);
            throw new o.ei.i(((String) objArr3[0]).intern());
        }
        new o.cz.a();
        Object[] objArr4 = new Object[1];
        q((char) (7190 - (ViewConfiguration.getScrollBarSize() >> 8)), (KeyEvent.getMaxKeyCode() >> 16) + 1614, 6 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr4);
        d c = o.cz.a.c(bVar.v(((String) objArr4[0]).intern()));
        d dVar = this.e;
        if (dVar != null && dVar.d()) {
            switch (this.e.a(c)) {
                case true:
                    z = false;
                    break;
                default:
                    z = true;
                    break;
            }
        } else {
            z = false;
        }
        this.e = c;
        new o.cz.b();
        Object[] objArr5 = new Object[1];
        q((char) (ViewConfiguration.getTapTimeout() >> 16), 1620 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 6, objArr5);
        a c2 = o.cz.b.c(bVar.v(((String) objArr5[0]).intern()));
        a aVar = this.a;
        if (aVar != null) {
            switch (aVar.d() ? (char) 4 : '4') {
                case '4':
                    break;
                default:
                    switch (z ? 'b' : 'O') {
                        case Opcodes.IASTORE /* 79 */:
                            switch (!this.a.c(c2)) {
                                case false:
                                    z = false;
                                    break;
                            }
                        default:
                            z = true;
                            break;
                    }
            }
        }
        this.a = c2;
        new o.cz.j();
        Object[] objArr6 = new Object[1];
        q((char) (14982 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 1626 - Gravity.getAbsoluteGravity(0, 0), 8 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr6);
        h b = o.cz.j.b(bVar.v(((String) objArr6[0]).intern()));
        h hVar = this.d;
        Object obj = null;
        if (hVar != null && hVar.d()) {
            int i3 = p + 25;
            int i4 = i3 % 128;
            s = i4;
            if (i3 % 2 != 0) {
                throw null;
            }
            if (!z) {
                int i5 = i4 + Opcodes.DREM;
                p = i5 % 128;
                int i6 = i5 % 2;
                if (this.d.a(b)) {
                    z = false;
                }
            }
            z = true;
        }
        this.d = b;
        new o.cz.d();
        Object[] objArr7 = new Object[1];
        q((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), KeyEvent.keyCodeFromString("") + 1634, (ViewConfiguration.getWindowTouchSlop() >> 8) + 21, objArr7);
        b a = o.cz.d.a(context, bVar.s(((String) objArr7[0]).intern()), this.e.a());
        b bVar2 = this.c;
        if (bVar2 != null && bVar2.d()) {
            if (!z) {
                int i7 = s + 45;
                p = i7 % 128;
                if (i7 % 2 == 0) {
                    this.c.a(a);
                    obj.hashCode();
                    throw null;
                }
                switch (!this.c.a(a)) {
                    case false:
                        z = false;
                        break;
                }
            }
            z = true;
        }
        this.c = a;
        new o.cz.c();
        Object[] objArr8 = new Object[1];
        q((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 1655 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 22 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr8);
        e b2 = o.cz.c.b(bVar.s(((String) objArr8[0]).intern()), a.a().keySet());
        e eVar = this.h;
        if (eVar != null && eVar.d()) {
            if (!z && this.h.a(b2)) {
                z2 = false;
            }
            z = z2;
        }
        this.h = b2;
        return z;
    }

    private boolean k() {
        int i = p + 21;
        s = i % 128;
        int i2 = i % 2;
        if (this.e.d() && this.a.d()) {
            int i3 = s + 91;
            p = i3 % 128;
            int i4 = i3 % 2;
            if (this.d.d()) {
                int i5 = p + 75;
                s = i5 % 128;
                int i6 = i5 % 2;
                switch (this.c.d() ? 'I' : (char) 31) {
                    case 31:
                        break;
                    default:
                        if (this.h.d()) {
                            int i7 = s + 21;
                            p = i7 % 128;
                            if (i7 % 2 == 0) {
                                throw null;
                            }
                            if (this.i != null) {
                                return true;
                            }
                        }
                        break;
                }
            }
        }
        int i8 = s + 55;
        p = i8 % 128;
        int i9 = i8 % 2;
        return false;
    }

    public final void a(Context context) {
        Object[] objArr = new Object[1];
        q((char) (3072 - (ViewConfiguration.getJumpTapTimeout() >> 16)), 1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 47 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        g.c();
        Object[] objArr2 = new Object[1];
        q((char) (Color.green(0) + 57987), 49 - Color.green(0), Gravity.getAbsoluteGravity(0, 0) + 17, objArr2);
        String intern = ((String) objArr2[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr3 = new Object[1];
        q((char) KeyEvent.getDeadChar(0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1677, (ViewConfiguration.getScrollBarSize() >> 8) + 33, objArr3);
        g.d(intern, sb.append(((String) objArr3[0]).intern()).append(this.f88o.b()).toString());
        switch (this.f88o.d() == 0 ? ':' : '+') {
            case Opcodes.ASTORE /* 58 */:
                int i = s + 19;
                p = i % 128;
                int i2 = i % 2;
                g.c();
                Object[] objArr4 = new Object[1];
                q((char) (57986 - TextUtils.indexOf((CharSequence) "", '0', 0)), 'a' - AndroidCharacter.getMirror('0'), 17 - (ViewConfiguration.getScrollBarSize() >> 8), objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                q((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1711, KeyEvent.getDeadChar(0, 0) + 30, objArr5);
                g.d(intern2, ((String) objArr5[0]).intern());
                SharedPreferences.Editor edit = sharedPreferences.edit();
                Object[] objArr6 = new Object[1];
                q((char) ExpandableListView.getPackedPositionType(0L), 47 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 2 - (ViewConfiguration.getTapTimeout() >> 16), objArr6);
                edit.putString(((String) objArr6[0]).intern(), "").commit();
                int i3 = s + 1;
                p = i3 % 128;
                int i4 = i3 % 2;
                break;
            default:
                String a = new o.dd.e(context).a(this.f88o.b());
                g.c();
                Object[] objArr7 = new Object[1];
                q((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 57987), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 48, 17 - TextUtils.getOffsetBefore("", 0), objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                q((char) (ViewConfiguration.getEdgeSlop() >> 16), 1741 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 43 - TextUtils.indexOf((CharSequence) "", '0'), objArr8);
                g.d(intern3, ((String) objArr8[0]).intern());
                SharedPreferences.Editor edit2 = sharedPreferences.edit();
                Object[] objArr9 = new Object[1];
                q((char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), ExpandableListView.getPackedPositionChild(0L) + 48, 1 - MotionEvent.axisFromString(""), objArr9);
                edit2.putString(((String) objArr9[0]).intern(), a).commit();
                int i5 = s + 77;
                p = i5 % 128;
                int i6 = i5 % 2;
                break;
        }
    }

    public final d d() {
        int i = s + 65;
        p = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return this.e;
        }
    }

    public final h c() {
        int i = s + 91;
        int i2 = i % 128;
        p = i2;
        int i3 = i % 2;
        h hVar = this.d;
        int i4 = i2 + 97;
        s = i4 % 128;
        int i5 = i4 % 2;
        return hVar;
    }

    public final a a() {
        a aVar;
        int i = p;
        int i2 = i + 95;
        s = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                aVar = this.a;
                int i3 = 76 / 0;
                break;
            default:
                aVar = this.a;
                break;
        }
        int i4 = i + 87;
        s = i4 % 128;
        int i5 = i4 % 2;
        return aVar;
    }

    public final e b() {
        int i = p + Opcodes.DSUB;
        s = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return this.h;
        }
    }

    public final j e() {
        int i = s + 1;
        int i2 = i % 128;
        p = i2;
        int i3 = i % 2;
        j jVar = this.b;
        int i4 = i2 + 87;
        s = i4 % 128;
        switch (i4 % 2 != 0 ? 'U' : (char) 5) {
            case Opcodes.CASTORE /* 85 */:
                int i5 = 75 / 0;
                return jVar;
            default:
                return jVar;
        }
    }

    public final Long g() {
        int i = p + 51;
        s = i % 128;
        int i2 = i % 2;
        Long valueOf = Long.valueOf(this.m);
        int i3 = s + 21;
        p = i3 % 128;
        int i4 = i3 % 2;
        return valueOf;
    }

    public final String i() {
        int i = s + 13;
        p = i % 128;
        switch (i % 2 == 0 ? Typography.greater : '3') {
            case '3':
                return this.i;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void b(String str) {
        o.eg.b bVar;
        String intern;
        int i = s + 53;
        p = i % 128;
        try {
            switch (i % 2 == 0 ? '9' : '4') {
                case '9':
                    this.i = str;
                    bVar = this.f88o;
                    Object[] objArr = new Object[1];
                    q((char) ExpandableListView.getPackedPositionType(1L), 7947 / (ViewConfiguration.getKeyRepeatTimeout() >> 89), 45 >>> Gravity.getAbsoluteGravity(1, 1), objArr);
                    intern = ((String) objArr[0]).intern();
                    break;
                default:
                    this.i = str;
                    bVar = this.f88o;
                    Object[] objArr2 = new Object[1];
                    q((char) ExpandableListView.getPackedPositionType(0L), 1016 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 8 - Gravity.getAbsoluteGravity(0, 0), objArr2);
                    intern = ((String) objArr2[0]).intern();
                    break;
            }
            bVar.d(intern, str);
            int i2 = p + 95;
            s = i2 % 128;
            int i3 = i2 % 2;
        } catch (o.eg.d e) {
            g.c();
            Object[] objArr3 = new Object[1];
            q((char) (57987 - (ViewConfiguration.getJumpTapTimeout() >> 16)), TextUtils.lastIndexOf("", '0', 0) + 50, 17 - Drawable.resolveOpacity(0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr4 = new Object[1];
            q((char) (46333 - (ViewConfiguration.getScrollBarSize() >> 8)), 1784 - (ViewConfiguration.getJumpTapTimeout() >> 16), 41 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr4);
            g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(e.getMessage()).toString());
        }
    }

    public final String h() {
        int i = s + Opcodes.LNEG;
        p = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 35 / 0;
                return this.j;
            default:
                return this.j;
        }
    }

    public final String f() {
        int i = p;
        int i2 = i + 77;
        s = i2 % 128;
        int i3 = i2 % 2;
        String str = this.f;
        int i4 = i + Opcodes.LSUB;
        s = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final String j() {
        int i = p + 109;
        int i2 = i % 128;
        s = i2;
        switch (i % 2 != 0 ? (char) 18 : (char) 23) {
            case 23:
                String str = this.g;
                int i3 = i2 + 33;
                p = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 14 : 'O') {
                    case 14:
                        int i4 = 58 / 0;
                        return str;
                    default:
                        return str;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final X509Certificate m() {
        int i = p + 45;
        int i2 = i % 128;
        s = i2;
        int i3 = i % 2;
        X509Certificate x509Certificate = this.n;
        int i4 = i2 + 99;
        p = i4 % 128;
        int i5 = i4 % 2;
        return x509Certificate;
    }

    public final X509Certificate l() {
        X509Certificate x509Certificate = this.l;
        switch (x509Certificate != null) {
            case false:
                X509Certificate x509Certificate2 = this.n;
                int i = p + 93;
                s = i % 128;
                int i2 = i % 2;
                return x509Certificate2;
            default:
                int i3 = s + 7;
                p = i3 % 128;
                int i4 = i3 % 2;
                return x509Certificate;
        }
    }

    private void b(Context context) {
        g.c();
        Object[] objArr = new Object[1];
        q((char) (TextUtils.getOffsetBefore("", 0) + 57987), 49 - View.resolveSize(0, 0), 17 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 1825, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 29, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.e = new d();
        this.d.e();
        this.d.d(context);
        this.d = new h();
        this.a = new a();
        o.eg.b bVar = this.f88o;
        Object[] objArr3 = new Object[1];
        q((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 17423), 801 - TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getEdgeSlop() >> 16) + 8, objArr3);
        bVar.c(((String) objArr3[0]).intern());
        o.eg.b bVar2 = this.f88o;
        Object[] objArr4 = new Object[1];
        q((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 30227), 857 - (ViewConfiguration.getFadingEdgeLength() >> 16), 8 - View.combineMeasuredStates(0, 0), objArr4);
        bVar2.c(((String) objArr4[0]).intern());
        int i = s + 5;
        p = i % 128;
        switch (i % 2 == 0 ? 'D' : 'P') {
            case 'D':
                throw null;
            default:
                return;
        }
    }

    private void n() {
        int i = s + 11;
        p = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        q((char) (AndroidCharacter.getMirror('0') + 57939), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 48, 17 - TextUtils.getOffsetAfter("", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((-1) - TextUtils.lastIndexOf("", '0')), 1855 - (ViewConfiguration.getTapTimeout() >> 16), KeyEvent.normalizeMetaState(0) + 17, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.i = null;
        o.eg.b bVar = this.f88o;
        Object[] objArr3 = new Object[1];
        q((char) TextUtils.getCapsMode("", 0, 0), View.MeasureSpec.getSize(0) + PointerIconCompat.TYPE_TOP_RIGHT_DIAGONAL_DOUBLE_ARROW, (Process.myPid() >> 22) + 8, objArr3);
        bVar.c(((String) objArr3[0]).intern());
        int i3 = p + Opcodes.DNEG;
        s = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                int i4 = 13 / 0;
                return;
        }
    }

    private void t() {
        int i = s + 81;
        p = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        q((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 57987), (KeyEvent.getMaxKeyCode() >> 16) + 49, 16 - TextUtils.indexOf((CharSequence) "", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), 1872 - Color.alpha(0), 19 - Color.alpha(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.j = null;
        o.eg.b bVar = this.f88o;
        Object[] objArr3 = new Object[1];
        q((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.indexOf("", "") + 1150, 9 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr3);
        bVar.c(((String) objArr3[0]).intern());
        int i3 = s + 19;
        p = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 4 : 'F') {
            case 4:
                int i4 = 23 / 0;
                return;
            default:
                return;
        }
    }

    private void r() {
        g.c();
        Object[] objArr = new Object[1];
        q((char) (57987 - ExpandableListView.getPackedPositionType(0L)), 49 - Color.red(0), 17 - (Process.myTid() >> 22), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.getOffsetAfter("", 0) + 1891, (ViewConfiguration.getWindowTouchSlop() >> 8) + 30, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.b = new j();
        o.eg.b bVar = this.f88o;
        Object[] objArr3 = new Object[1];
        q((char) Color.alpha(0), 1097 - (ViewConfiguration.getDoubleTapTimeout() >> 16), View.resolveSize(0, 0) + 16, objArr3);
        bVar.c(((String) objArr3[0]).intern());
        int i = p + 83;
        s = i % 128;
        int i2 = i % 2;
    }

    private void s() {
        int i = p + 73;
        s = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        q((char) (TextUtils.indexOf((CharSequence) "", '0') + 57988), Drawable.resolveOpacity(0, 0) + 49, 18 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 63719), 1920 - TextUtils.lastIndexOf("", '0'), 33 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.m = 0L;
        o.eg.b bVar = this.f88o;
        Object[] objArr3 = new Object[1];
        q((char) (57017 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), Color.alpha(0) + 953, (ViewConfiguration.getTouchSlop() >> 8) + 21, objArr3);
        bVar.c(((String) objArr3[0]).intern());
        int i3 = p + 61;
        s = i3 % 128;
        switch (i3 % 2 != 0 ? 'T' : (char) 25) {
            case 25:
                return;
            default:
                throw null;
        }
    }

    public final void c(Context context) {
        g.c();
        Object[] objArr = new Object[1];
        q((char) (57986 - Process.getGidForName("")), 49 - KeyEvent.normalizeMetaState(0), 17 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 1953, TextUtils.getOffsetAfter("", 0) + 8, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        b(context);
        n();
        t();
        s();
        r();
        this.f88o = new o.eg.b();
        this.n = null;
        this.l = null;
        int i = s + 73;
        p = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Removed duplicated region for block: B:51:0x01b5  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 716
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.i.q(char, int, int, java.lang.Object[]):void");
    }
}
