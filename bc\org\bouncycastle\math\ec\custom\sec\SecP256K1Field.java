package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP256K1Field.smali */
public class SecP256K1Field {
    static final int[] a = {-977, -2, -1, -1, -1, -1, -1, -1};
    private static final int[] b = {954529, 1954, 1, 0, 0, 0, 0, 0, -1954, -3, -1, -1, -1, -1, -1, -1};
    private static final int[] c = {-954529, -1955, -2, -1, -1, -1, -1, -1, 1953, 2};

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (w5.a(iArr, iArr2, iArr3) != 0 || (iArr3[7] == -1 && w5.c(iArr3, a))) {
            c6.a(8, 977, iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.a(16, iArr, iArr2, iArr3) != 0 || (iArr3[15] == -1 && c6.d(16, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(16, iArr3, iArr4.length);
            }
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(8, iArr, iArr2) != 0 || (iArr2[7] == -1 && w5.c(iArr2, a))) {
            c6.a(8, 977, iArr2);
        }
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = w5.a(bigInteger);
        if (a2[7] == -1) {
            int[] iArr = a;
            if (w5.c(a2, iArr)) {
                w5.e(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(8, iArr, 0, iArr2);
        } else {
            c6.d(8, iArr2, w5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 8; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] c2 = w5.c();
        w5.c(iArr, iArr2, c2);
        reduce(c2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (w5.e(iArr, iArr2, iArr3) != 0 || (iArr3[15] == -1 && c6.d(16, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(16, iArr3, iArr4.length);
            }
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            w5.f(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            w5.f(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[32];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 8);
        } while (c6.f(8, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        if (w5.a(977, w5.a(977, iArr, 8, iArr, 0, iArr2, 0), iArr2, 0) != 0 || (iArr2[7] == -1 && w5.c(iArr2, a))) {
            c6.a(8, 977, iArr2);
        }
    }

    public static void reduce32(int i, int[] iArr) {
        if ((i == 0 || w5.a(977, i, iArr, 0) == 0) && !(iArr[7] == -1 && w5.c(iArr, a))) {
            return;
        }
        c6.a(8, 977, iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] c2 = w5.c();
        w5.d(iArr, c2);
        reduce(c2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] c2 = w5.c();
        w5.d(iArr, c2);
        reduce(c2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            w5.d(iArr2, c2);
            reduce(c2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (w5.f(iArr, iArr2, iArr3) != 0) {
            c6.c(8, 977, iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(16, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            if (c6.g(iArr4.length, iArr4, iArr3) != 0) {
                c6.a(16, iArr3, iArr4.length);
            }
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(8, iArr, 0, iArr2) != 0 || (iArr2[7] == -1 && w5.c(iArr2, a))) {
            c6.a(8, 977, iArr2);
        }
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4) {
        w5.c(iArr, iArr2, iArr4);
        reduce(iArr4, iArr3);
    }

    public static void square(int[] iArr, int[] iArr2, int[] iArr3) {
        w5.d(iArr, iArr3);
        reduce(iArr3, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2, int[] iArr3) {
        w5.d(iArr, iArr3);
        reduce(iArr3, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            w5.d(iArr2, iArr3);
            reduce(iArr3, iArr2);
        }
    }
}
