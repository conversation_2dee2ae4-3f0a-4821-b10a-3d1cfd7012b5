package o.cl;

import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Iterator;
import java.util.List;
import o.eg.b;
import o.ei.i;
import o.et.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cl\c.smali */
public final class c extends d<j> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static byte[] c;
    private static int d;
    private static short[] e;
    private static int f;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        j = 1;
        c = new byte[]{114, -113, 126, 113, -125, 110, -106, -124, 120, -127, 113, -113, 96, -112};
        a = 909053593;
        b = 1675480756;
        d = -191957051;
    }

    static void init$0() {
        $$d = new byte[]{27, 43, 25, -109};
        $$e = Opcodes.PUTFIELD;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 108
            int r6 = r6 * 2
            int r6 = r6 + 4
            byte[] r0 = o.cl.c.$$d
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r8
            r4 = r2
            r8 = r7
            goto L2e
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2e:
            int r3 = -r3
            int r7 = r7 + r3
            int r6 = r6 + 1
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cl.c.l(short, byte, short, java.lang.Object[]):void");
    }

    @Override // o.cl.d, o.cc.e
    public final /* bridge */ /* synthetic */ List a(String str, String str2, int i, String str3, b bVar) throws i {
        int i2 = f + 49;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return super.a(str, str2, i, str3, bVar);
            default:
                super.a(str, str2, i, str3, bVar);
                throw null;
        }
    }

    /* JADX WARN: Type inference failed for: r3v1, types: [o.et.e, o.et.j] */
    @Override // o.cl.d
    public final /* bridge */ /* synthetic */ j c(String str, String str2, int i, String str3) {
        int i2 = j + 61;
        f = i2 % 128;
        int i3 = i2 % 2;
        ?? c2 = super.c(str, str2, i, str3);
        int i4 = f + 53;
        j = i4 % 128;
        int i5 = i4 % 2;
        return c2;
    }

    @Override // o.cl.d
    final /* synthetic */ j e(String str, String str2, int i, String str3) {
        int i2 = f + 3;
        j = i2 % 128;
        int i3 = i2 % 2;
        j a2 = a(str, str2, i, str3);
        int i4 = j + 25;
        f = i4 % 128;
        int i5 = i4 % 2;
        return a2;
    }

    private static j a(String str, String str2, int i, String str3) {
        j jVar = new j(str, str2, i, str3);
        int i2 = f + 59;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return jVar;
        }
    }

    @Override // o.cl.d
    final void d(List<j> list, b bVar) throws o.eg.d {
        int i = j + 39;
        f = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k((byte) ((-19) - View.MeasureSpec.makeMeasureSpec(0, 0)), 1029572266 - TextUtils.indexOf((CharSequence) "", '0', 0), (short) (Process.myTid() >> 22), 4 - TextUtils.indexOf((CharSequence) "", '0'), ExpandableListView.getPackedPositionType(0L) - 1441972178, objArr);
        bVar.s(((String) objArr[0]).intern());
        b();
        Iterator<j> it = list.iterator();
        while (true) {
            switch (!it.hasNext()) {
                case false:
                    it.next();
                default:
                    int i3 = f + 9;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                    return;
            }
        }
    }

    private static byte[] b() {
        int i = f;
        int i2 = i + 31;
        j = i2 % 128;
        int i3 = i2 % 2;
        byte[] bArr = new byte[0];
        int i4 = i + 41;
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return bArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:66:0x021a, code lost:
    
        if (r4 != false) goto L73;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r20, int r21, short r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 908
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cl.c.k(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
