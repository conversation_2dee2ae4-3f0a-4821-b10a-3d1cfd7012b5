package o.er;

import android.content.Context;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\k.smali */
public final class k {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] c;
    private static long d;
    private static int e;
    private static int i;
    private final o.eo.e a;
    private final Context b;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        i = 1;
        c();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        KeyEvent.getModifierMetaStateMask();
        ViewConfiguration.getLongPressTimeout();
        int i2 = e + 89;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        c = new char[]{24034, 1783, 60321, 19544, 12561, 39444, 32484, 9114, 33878, 26941, 53808, 46822, 7062, 64591, 41306, 2596, 61157, 21400, 13384, 39288, 16942, 9940, 35721, 27809, 53576, 47660, 7885, 50075, 42170, 2402, 61970, 36377, 54570, 14453, 40846, 58049, 18914, 44308, 61504, 22402, 47826, 474, 25903, 51268, 12163, 29352, 55792, 15625, 32832, 59282, 19118, 37371, 11405, 30610, 39626, 15670, 16501, 60242, 3977, 21204, 62776, 6265, 41817};
        d = -3127778981435312133L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r0 = o.er.k.$$a
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r9 = r9 + 102
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1d:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L34:
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.k.g(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{9, -87, 124, -45};
        $$b = Opcodes.DCMPL;
    }

    public k(Context context, o.eo.e eVar) {
        this.b = context;
        this.a = eVar;
    }

    /* JADX WARN: Removed duplicated region for block: B:28:0x00b5 A[FALL_THROUGH] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void c(final fr.antelop.sdk.util.OperationCallback<java.lang.Void> r11) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 378
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.k.c(fr.antelop.sdk.util.OperationCallback):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 718
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.k.f(char, int, int, java.lang.Object[]):void");
    }
}
