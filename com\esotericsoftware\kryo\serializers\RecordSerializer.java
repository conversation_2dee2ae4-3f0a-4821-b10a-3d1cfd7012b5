package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.RecordSerializer;
import com.esotericsoftware.minlog.Log;
import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Comparator;
import java.util.function.Function;
import java.util.function.IntFunction;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\RecordSerializer.smali */
public class RecordSerializer<T> extends ImmutableSerializer<T> {
    private static final Method GET_NAME;
    private static final Method GET_RECORD_COMPONENTS;
    private static final Method GET_TYPE;
    private static final Method IS_RECORD;
    private boolean fixedFieldTypes = false;

    static {
        Method isRecord;
        Method getRecordComponents;
        Method getName;
        Method getType;
        try {
            Class<?> c = Class.forName("java.lang.reflect.RecordComponent");
            isRecord = Class.class.getDeclaredMethod("isRecord", new Class[0]);
            getRecordComponents = Class.class.getMethod("getRecordComponents", new Class[0]);
            getName = c.getMethod("getName", new Class[0]);
            getType = c.getMethod("getType", new Class[0]);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            isRecord = null;
            getRecordComponents = null;
            getName = null;
            getType = null;
        }
        IS_RECORD = isRecord;
        GET_RECORD_COMPONENTS = getRecordComponents;
        GET_NAME = getName;
        GET_TYPE = getType;
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        Class<?> cls = object.getClass();
        if (!isRecord(cls)) {
            throw new KryoException(object + " is not a record");
        }
        for (RecordComponent rc : recordComponents(cls, Comparator.comparing(new RecordSerializer$$ExternalSyntheticLambda2()))) {
            Class<?> type = rc.type();
            String name = rc.name();
            try {
                if (Log.TRACE) {
                    Log.trace("kryo", "Write property: " + name + " (" + type.getName() + ")");
                }
                if (type.isPrimitive()) {
                    kryo.writeObject(output, componentValue(object, rc));
                } else {
                    if (!this.fixedFieldTypes && !kryo.isFinal(type)) {
                        kryo.writeClassAndObject(output, componentValue(object, rc));
                    }
                    kryo.writeObjectOrNull(output, componentValue(object, rc), type);
                }
            } catch (KryoException ex) {
                ex.addTrace(name + " (" + type.getName() + ")");
                throw ex;
            } catch (Throwable t) {
                KryoException ex2 = new KryoException(t);
                ex2.addTrace(name + " (" + type.getName() + ")");
                throw ex2;
            }
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> cls) {
        if (!isRecord(cls)) {
            throw new KryoException("Not a record (" + cls + ")");
        }
        RecordComponent[] recordComponents = recordComponents(cls, Comparator.comparing(new RecordSerializer$$ExternalSyntheticLambda2()));
        Object[] objArr = new Object[recordComponents.length];
        for (RecordComponent recordComponent : recordComponents) {
            String name = recordComponent.name();
            Class<?> type = recordComponent.type();
            try {
                if (Log.TRACE) {
                    Log.trace("kryo", "Read property: " + name + " (" + cls.getName() + ")");
                }
                if (type.isPrimitive()) {
                    objArr[recordComponent.index()] = kryo.readObject(input, type);
                } else {
                    if (!this.fixedFieldTypes && !kryo.isFinal(type)) {
                        objArr[recordComponent.index()] = kryo.readClassAndObject(input);
                    }
                    objArr[recordComponent.index()] = kryo.readObjectOrNull(input, type);
                }
            } catch (KryoException e) {
                e.addTrace(name + " (" + cls.getName() + ")");
                throw e;
            } catch (Throwable th) {
                KryoException kryoException = new KryoException(th);
                kryoException.addTrace(name + " (" + cls.getName() + ")");
                throw kryoException;
            }
        }
        Arrays.sort(recordComponents, Comparator.comparing(new Function() { // from class: com.esotericsoftware.kryo.serializers.RecordSerializer$$ExternalSyntheticLambda3
            @Override // java.util.function.Function
            public final Object apply(Object obj) {
                return Integer.valueOf(((RecordSerializer.RecordComponent) obj).index());
            }
        }));
        return (T) invokeCanonicalConstructor(cls, recordComponents, objArr);
    }

    private boolean isRecord(Class<?> type) {
        try {
            return ((Boolean) IS_RECORD.invoke(type, new Object[0])).booleanValue();
        } catch (Throwable th) {
            throw new KryoException("Could not determine type (" + type + ")");
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\RecordSerializer$RecordComponent.smali */
    static final class RecordComponent {
        private final int index;
        private final String name;
        private final Class<?> type;

        RecordComponent(String name, Class<?> type, int index) {
            this.name = name;
            this.type = type;
            this.index = index;
        }

        String name() {
            return this.name;
        }

        Class<?> type() {
            return this.type;
        }

        int index() {
            return this.index;
        }
    }

    private static <T> RecordComponent[] recordComponents(Class<T> type, Comparator<RecordComponent> comparator) {
        try {
            Object[] rawComponents = (Object[]) GET_RECORD_COMPONENTS.invoke(type, new Object[0]);
            RecordComponent[] recordComponents = new RecordComponent[rawComponents.length];
            for (int i = 0; i < rawComponents.length; i++) {
                Object comp = rawComponents[i];
                recordComponents[i] = new RecordComponent((String) GET_NAME.invoke(comp, new Object[0]), (Class) GET_TYPE.invoke(comp, new Object[0]), i);
            }
            if (comparator != null) {
                Arrays.sort(recordComponents, comparator);
            }
            return recordComponents;
        } catch (Throwable t) {
            KryoException ex = new KryoException(t);
            ex.addTrace("Could not retrieve record components (" + type.getName() + ")");
            throw ex;
        }
    }

    private static Object componentValue(Object recordObject, RecordComponent recordComponent) {
        try {
            Method get = recordObject.getClass().getDeclaredMethod(recordComponent.name(), new Class[0]);
            if (!get.canAccess(recordObject)) {
                get.setAccessible(true);
            }
            return get.invoke(recordObject, new Object[0]);
        } catch (Throwable t) {
            KryoException ex = new KryoException(t);
            ex.addTrace("Could not retrieve record components (" + recordObject.getClass().getName() + ")");
            throw ex;
        }
    }

    private static <T> T invokeCanonicalConstructor(Class<T> recordType, RecordComponent[] recordComponents, Object[] args) {
        Constructor<T> canonicalConstructor;
        try {
            Class<?>[] paramTypes = (Class[]) Arrays.stream(recordComponents).map(new Function() { // from class: com.esotericsoftware.kryo.serializers.RecordSerializer$$ExternalSyntheticLambda0
                @Override // java.util.function.Function
                public final Object apply(Object obj) {
                    return ((RecordSerializer.RecordComponent) obj).type();
                }
            }).toArray(new IntFunction() { // from class: com.esotericsoftware.kryo.serializers.RecordSerializer$$ExternalSyntheticLambda1
                @Override // java.util.function.IntFunction
                public final Object apply(int i) {
                    return RecordSerializer.lambda$invokeCanonicalConstructor$0(i);
                }
            });
            try {
                canonicalConstructor = recordType.getConstructor(paramTypes);
            } catch (NoSuchMethodException e) {
                Constructor<T> canonicalConstructor2 = recordType.getDeclaredConstructor(paramTypes);
                canonicalConstructor2.setAccessible(true);
                canonicalConstructor = canonicalConstructor2;
            }
            return canonicalConstructor.newInstance(args);
        } catch (Throwable t) {
            KryoException ex = new KryoException(t);
            ex.addTrace("Could not construct type (" + recordType.getName() + ")");
            throw ex;
        }
    }

    static /* synthetic */ Class[] lambda$invokeCanonicalConstructor$0(int x$0) {
        return new Class[x$0];
    }

    public void setFixedFieldTypes(boolean fixedFieldTypes) {
        this.fixedFieldTypes = fixedFieldTypes;
    }
}
