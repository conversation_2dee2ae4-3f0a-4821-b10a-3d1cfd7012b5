package o.s;

import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationPattern;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Objects;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.j;
import o.e.a;
import o.ee.d;
import o.ee.g;
import o.i.f;
import o.i.i;
import o.i.n;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\s\c.smali */
public final class c implements d<CustomerAuthenticationPattern> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] c;
    private static int d;
    private static boolean e;
    private static int f;
    private static int g;
    private static boolean j;
    private String a;
    private final f[][] b;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        g = 1;
        b();
        Process.myTid();
        int i = g + 45;
        f = i % 128;
        switch (i % 2 != 0 ? 'J' : '2') {
            case 'J':
                throw null;
            default:
                return;
        }
    }

    static void b() {
        c = new char[]{61614, 61649, 61642, 61653, 61606, 61658, 61659, 61647, 61646, 61636, 61638, 61648, 61623, 61652, 61621, 61643, 61662, 61583, 61582, 61575, 61586, 61655, 61651, 61660, 61650, 61602, 61576, 61587};
        e = true;
        j = true;
        d = 782102887;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 4
            byte[] r0 = o.s.c.$$a
            int r7 = r7 + 117
            int r6 = r6 * 4
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L19:
            r3 = r2
        L1a:
            r5 = r8
            r8 = r7
            r7 = r5
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = -r7
            int r8 = r8 + 1
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.s.c.i(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{91, -13, 111, 42};
        $$b = 20;
    }

    @Override // o.ee.d
    public final /* synthetic */ CustomerAuthenticationPattern a() {
        int i = g + 29;
        f = i % 128;
        switch (i % 2 == 0) {
            case false:
                c();
                throw null;
            default:
                return c();
        }
    }

    public c(String str, f[][] fVarArr) {
        this.a = str;
        this.b = fVarArr;
    }

    public final String d() {
        int i = f + 45;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        String str = this.a;
        int i4 = i2 + 17;
        f = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final f[] e(int i) {
        int i2 = f + 17;
        int i3 = i2 % 128;
        g = i3;
        int i4 = i2 % 2;
        int i5 = i + 1;
        f[][] fVarArr = this.b;
        switch (i5 <= fVarArr.length ? '/' : (char) 17) {
            case 17:
                int i6 = i3 + Opcodes.LSHL;
                f = i6 % 128;
                Object obj = null;
                switch (i6 % 2 == 0) {
                    case false:
                        obj.hashCode();
                        throw null;
                    default:
                        return null;
                }
            default:
                return fVarArr[i];
        }
    }

    public final boolean c(i iVar) {
        if (!o.ei.c.c().q()) {
            return false;
        }
        int i = 0;
        while (true) {
            switch (i < e() ? '[' : 'L') {
                case Opcodes.DUP_X2 /* 91 */:
                    f[] b = b(i, iVar);
                    switch (b != null ? '\r' : (char) 15) {
                        case '\r':
                            int i2 = g;
                            int i3 = i2 + 109;
                            f = i3 % 128;
                            int i4 = i3 % 2;
                            if (b.length == 0) {
                                int i5 = i2 + 71;
                                f = i5 % 128;
                                switch (i5 % 2 == 0) {
                                    case false:
                                        g.c();
                                        Object[] objArr = new Object[1];
                                        h(null, 15 >>> (ExpandableListView.getPackedPositionForChild(1, 1) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(1, 1) == 0L ? 0 : -1)), null, "\u0082\u0084\u0083\u0087\u0087\u008b\u008d\u0082\u008c\u0089\u0087\u008b\u008a\u0089\u0087\u0082\u0083\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
                                        String intern = ((String) objArr[0]).intern();
                                        Object[] objArr2 = new Object[1];
                                        h(null, 67 >>> ExpandableListView.getPackedPositionGroup(1L), null, "\u0097\u008b\u0082\u008c\u0089\u0087\u008b\u0084\u0083\u0096\u008c\u0094\u0087\u008c\u0082\u0094\u008e\u0089\u0094\u0082\u0084\u0083\u0087\u0087\u008b\u008d\u0094\u0095\u0094\u0093\u0092\u0091\u0090\u008b\u0083\u008f\u008e\u0089", objArr2);
                                        g.d(intern, ((String) objArr2[0]).intern());
                                        return true;
                                    default:
                                        g.c();
                                        Object[] objArr3 = new Object[1];
                                        h(null, 126 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), null, "\u0082\u0084\u0083\u0087\u0087\u008b\u008d\u0082\u008c\u0089\u0087\u008b\u008a\u0089\u0087\u0082\u0083\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr3);
                                        String intern2 = ((String) objArr3[0]).intern();
                                        Object[] objArr4 = new Object[1];
                                        h(null, 127 - ExpandableListView.getPackedPositionGroup(0L), null, "\u0097\u008b\u0082\u008c\u0089\u0087\u008b\u0084\u0083\u0096\u008c\u0094\u0087\u008c\u0082\u0094\u008e\u0089\u0094\u0082\u0084\u0083\u0087\u0087\u008b\u008d\u0094\u0095\u0094\u0093\u0092\u0091\u0090\u008b\u0083\u008f\u008e\u0089", objArr4);
                                        g.d(intern2, ((String) objArr4[0]).intern());
                                        return false;
                                }
                            }
                            i++;
                    }
                    break;
            }
        }
        return true;
    }

    public final f[] b(int i, i iVar) {
        f[] e2 = e(i);
        if (e2 == null) {
            int i2 = g + Opcodes.DREM;
            f = i2 % 128;
            if (i2 % 2 == 0) {
                return null;
            }
            int i3 = 95 / 0;
            return null;
        }
        ArrayList arrayList = new ArrayList();
        HashMap<f, o.i.g> b = o.i.d.c().b();
        for (f fVar : e2) {
            o.i.g gVar = b.get(fVar);
            switch (gVar == null) {
                case true:
                    break;
                default:
                    if (iVar != null) {
                        int i4 = f + 91;
                        g = i4 % 128;
                        int i5 = i4 % 2;
                        if (!gVar.h().contains(iVar)) {
                            break;
                        }
                    }
                    if (gVar.j() != o.i.c.c) {
                        int i6 = g + 1;
                        f = i6 % 128;
                        int i7 = i6 % 2;
                        if (gVar.i() != f.f) {
                            break;
                        }
                    }
                    if (gVar instanceof n) {
                        switch (((n) gVar).l() ? false : true) {
                        }
                    }
                    arrayList.add(gVar.i());
                    break;
            }
        }
        return (f[]) arrayList.toArray(new f[arrayList.size()]);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean b(o.i.f r6, int r7) {
        /*
            r5 = this;
            int r0 = o.s.c.g
            int r0 = r0 + 49
            int r1 = r0 % 128
            o.s.c.f = r1
            int r0 = r0 % 2
            o.i.f[] r7 = r5.e(r7)
            if (r7 != 0) goto L13
            r0 = 13
            goto L15
        L13:
            r0 = 28
        L15:
            r1 = 0
            switch(r0) {
                case 28: goto L24;
                default: goto L19;
            }
        L19:
            int r6 = o.s.c.f
            int r6 = r6 + 25
            int r7 = r6 % 128
            o.s.c.g = r7
            int r6 = r6 % 2
            goto L58
        L24:
            int r0 = r7.length
            r2 = r1
        L26:
            if (r2 >= r0) goto L2b
            r3 = 16
            goto L2d
        L2b:
            r3 = 61
        L2d:
            switch(r3) {
                case 16: goto L31;
                default: goto L30;
            }
        L30:
            goto L4d
        L31:
            r3 = r7[r2]
            if (r3 != r6) goto L38
            r3 = 50
            goto L3a
        L38:
            r3 = 35
        L3a:
            switch(r3) {
                case 50: goto L40;
                default: goto L3d;
            }
        L3d:
            int r2 = r2 + 1
            goto L42
        L40:
            r6 = 1
            return r6
        L42:
            int r3 = o.s.c.g
            int r3 = r3 + 63
            int r4 = r3 % 128
            o.s.c.f = r4
            int r3 = r3 % 2
            goto L26
        L4d:
            int r6 = o.s.c.f
            int r6 = r6 + 5
            int r7 = r6 % 128
            o.s.c.g = r7
            int r6 = r6 % 2
            return r1
        L58:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.s.c.b(o.i.f, int):boolean");
    }

    public final int e() {
        int i = g + 19;
        f = i % 128;
        switch (i % 2 != 0 ? Typography.amp : '7') {
            case '7':
                return this.b.length;
            default:
                int length = this.b.length;
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        switch (this == obj) {
            case true:
                int i = f + 33;
                g = i % 128;
                return i % 2 != 0;
            default:
                if (obj != null) {
                    int i2 = g + 47;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                    switch (getClass() != obj.getClass() ? (char) 4 : '/') {
                        case '/':
                            c cVar = (c) obj;
                            switch (this.a.equals(cVar.a) ? 'O' : '7') {
                                default:
                                    if (Arrays.equals(this.b, cVar.b)) {
                                        return true;
                                    }
                                case '7':
                                    return false;
                            }
                    }
                }
                return false;
        }
    }

    public final int hashCode() {
        int hash;
        int i = g + 37;
        f = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                objArr[1] = this.a;
                hash = (Objects.hash(objArr) * 114) - Arrays.hashCode(this.b);
                break;
            default:
                hash = (Objects.hash(this.a) * 31) + Arrays.hashCode(this.b);
                break;
        }
        int i2 = g + 77;
        f = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                throw null;
            default:
                return hash;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        h(null, (ViewConfiguration.getTouchSlop() >> 8) + 127, null, "\u009b\u009a\u0083\u0099\u008b\u0082\u0098\u0082\u0084\u0083\u0087\u0087\u008b\u008d\u0082\u008c\u0089\u0087\u008b\u008a\u0089\u0087\u0082\u0083\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.a).append('\'');
        Object[] objArr2 = new Object[1];
        h(null, TextUtils.lastIndexOf("", '0') + 128, null, "\u009a\u008e\u0096\u0083\u0087\u008e\u0094\u009c", objArr2);
        String obj = append.append(((String) objArr2[0]).intern()).append(Arrays.toString(this.b)).append('}').toString();
        int i = f + 5;
        g = i % 128;
        int i2 = i % 2;
        return obj;
    }

    private CustomerAuthenticationPattern c() {
        CustomerAuthenticationPattern customerAuthenticationPattern = new CustomerAuthenticationPattern(this);
        int i = f + Opcodes.LSHL;
        g = i % 128;
        int i2 = i % 2;
        return customerAuthenticationPattern;
    }

    private static void h(String str, int i, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        int i2;
        String str3 = str2;
        char c2 = str3 != null ? 'F' : (char) 17;
        char c3 = 2;
        byte[] bArr = str3;
        switch (c2) {
            case 17:
                break;
            default:
                int i3 = $11 + 15;
                $10 = i3 % 128;
                int i4 = i3 % 2;
                bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr2 = bArr;
        int i5 = 0;
        switch (str == null) {
            case true:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                int i6 = $10 + 23;
                $11 = i6 % 128;
                int i7 = i6 % 2;
                break;
        }
        char[] cArr2 = cArr;
        j jVar = new j();
        char[] cArr3 = c;
        Object obj = null;
        switch (cArr3 == null) {
            case true:
                break;
            default:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i8 = 0;
                while (true) {
                    switch (i8 < length ? i5 : 1) {
                        case 1:
                            cArr3 = cArr4;
                            break;
                        default:
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i5] = Integer.valueOf(cArr3[i8]);
                                Object obj2 = a.s.get(1085633688);
                                if (obj2 != null) {
                                    i2 = i5;
                                } else {
                                    Class cls = (Class) a.c(11 - View.resolveSizeAndState(i5, i5, i5), (char) View.combineMeasuredStates(i5, i5), TextUtils.indexOf((CharSequence) "", '0') + 494);
                                    byte length2 = (byte) $$a.length;
                                    Object[] objArr3 = new Object[1];
                                    i((byte) i5, length2, (byte) (length2 - 4), objArr3);
                                    i2 = 0;
                                    obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    a.s.put(1085633688, obj2);
                                }
                                cArr4[i8] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                                i8++;
                                obj = null;
                                i5 = i2;
                                c3 = 2;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                    }
                }
        }
        try {
            Object[] objArr4 = new Object[1];
            objArr4[i5] = Integer.valueOf(d);
            Object obj3 = a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls2 = (Class) a.c((ViewConfiguration.getPressedStateDuration() >> 16) + 10, (char) (8856 - TextUtils.getOffsetBefore("", i5)), KeyEvent.keyCodeFromString("") + 324);
                byte b = (byte) i5;
                byte b2 = (byte) (b + 1);
                Object[] objArr5 = new Object[1];
                i(b, b2, (byte) (b2 - 1), objArr5);
                String str4 = (String) objArr5[i5];
                Class<?>[] clsArr = new Class[1];
                clsArr[i5] = Integer.TYPE;
                obj3 = cls2.getMethod(str4, clsArr);
                a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(obj, objArr4)).intValue();
            switch (j ? '5' : (char) 25) {
                case 25:
                    switch (e ? 'L' : 'F') {
                        case Base64.mimeLineLength /* 76 */:
                            jVar.e = cArr2.length;
                            char[] cArr5 = new char[jVar.e];
                            jVar.c = i5;
                            while (jVar.c < jVar.e) {
                                cArr5[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i] - intValue);
                                try {
                                    Object[] objArr6 = {jVar, jVar};
                                    Object obj4 = a.s.get(745816316);
                                    if (obj4 == null) {
                                        Class cls3 = (Class) a.c(TextUtils.indexOf((CharSequence) "", '0', i5) + 11, (char) View.combineMeasuredStates(i5, i5), (TypedValue.complexToFraction(i5, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(i5, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 207);
                                        byte b3 = (byte) i5;
                                        byte b4 = b3;
                                        Object[] objArr7 = new Object[1];
                                        i(b3, b4, b4, objArr7);
                                        String str5 = (String) objArr7[i5];
                                        Class<?>[] clsArr2 = new Class[2];
                                        clsArr2[i5] = Object.class;
                                        clsArr2[1] = Object.class;
                                        obj4 = cls3.getMethod(str5, clsArr2);
                                        a.s.put(745816316, obj4);
                                    }
                                    ((Method) obj4).invoke(obj, objArr6);
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                            objArr[i5] = new String(cArr5);
                            return;
                        default:
                            jVar.e = iArr.length;
                            char[] cArr6 = new char[jVar.e];
                            jVar.c = i5;
                            while (true) {
                                switch (jVar.c < jVar.e ? 1 : i5) {
                                    case 1:
                                        cArr6[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                        jVar.c++;
                                        int i9 = $11 + 73;
                                        $10 = i9 % 128;
                                        int i10 = i9 % 2;
                                    default:
                                        objArr[i5] = new String(cArr6);
                                        return;
                                }
                            }
                    }
                default:
                    jVar.e = bArr2.length;
                    char[] cArr7 = new char[jVar.e];
                    jVar.c = i5;
                    while (jVar.c < jVar.e) {
                        cArr7[jVar.c] = (char) (cArr3[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj5 = a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls4 = (Class) a.c(TextUtils.indexOf((CharSequence) "", '0', i5) + 11, (char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 206);
                                byte b5 = (byte) i5;
                                byte b6 = b5;
                                Object[] objArr9 = new Object[1];
                                i(b5, b6, b6, objArr9);
                                String str6 = (String) objArr9[i5];
                                Class<?>[] clsArr3 = new Class[2];
                                clsArr3[i5] = Object.class;
                                clsArr3[1] = Object.class;
                                obj5 = cls4.getMethod(str6, clsArr3);
                                a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(obj, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    objArr[i5] = new String(cArr7);
                    return;
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
