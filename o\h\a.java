package o.h;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.h;
import o.bw.b;
import o.ee.g;
import o.f.c;
import o.f.e;
import o.i.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\h\a.smali */
public abstract class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static int d;
    private static int f;
    private final boolean a;
    final String b;
    private final i e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        f = 1;
        c = 874635383;
    }

    static void init$0() {
        $$a = new byte[]{7, -109, -85, 32};
        $$b = Opcodes.IREM;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = 109 - r7
            byte[] r0 = o.h.a.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.h.a.j(byte, byte, byte, java.lang.Object[]):void");
    }

    public a(String str, i iVar, boolean z) {
        this.b = str;
        this.e = iVar;
        this.a = z;
    }

    public final c e(Context context, final d dVar, final e eVar, final b bVar) {
        g.c();
        String str = this.b;
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i(View.MeasureSpec.getMode(0) + 9, "\u0012\u0018\r\u0005\u0010\u0017ￄ\uffdeￄ\u0010\u0013\u0005\b\uffe7\u0016\t\b\t\u0012\u0018\r\u0005\u0010\u0017ￄ\uffd1ￄ\u0007\u0016\t\b\t", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 32, 101 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), false, objArr);
        g.d(str, sb.append(((String) objArr[0]).intern()).append(eVar.b().name()).toString());
        o.i.d c2 = o.i.d.c();
        o.i.g a = c2.a(eVar);
        if (a == null) {
            int i = d + 91;
            f = i % 128;
            int i2 = i % 2;
            g.c();
            String str2 = this.b;
            Object[] objArr2 = new Object[1];
            i(View.resolveSizeAndState(0, 0, 0) + 7, "\u0004\u0011￢\u0003\u0000\u000e\u000b\u0003\u000e\u0007\u0013\u0004\f\uffbf\r\u0016\u000e\r\n\r\u0014\uffbfￌ\uffbf\u0012\u000b\u0000\b\u0013\r\u0004\u0003", 32 - (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.lastIndexOf("", '0', 0) + Opcodes.FMUL, true, objArr2);
            g.d(str2, ((String) objArr2[0]).intern());
            return c.a;
        }
        c e = a.e(eVar, this.e, this.a);
        if (e != c.d) {
            int i3 = d + Opcodes.DNEG;
            f = i3 % 128;
            int i4 = i3 % 2;
            g.c();
            String str3 = this.b;
            Object[] objArr3 = new Object[1];
            i(40 - Color.blue(0), "\u0002\u0012\u000f\u0006\uffc0\u0005\u0007\u0001\u0013\u0015\uffc0\u0013\f\u0001\t\u0014\u000e\u0005\u0004\u0005\u0012\u0003\uffc0ￍ\uffc0\u0013\f\u0001\t\u0014\u000e\u0005\u0004\u0005\u0012￣\u0004\u0001\u000f\f\u0004\u000f\b\u0014\u0005\r\uffc0\u0019\u0002\uffc0\u000e\u0005\u0004\u0004\t", ((Process.getThreadPriority(0) + 20) >> 6) + 55, 104 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), true, objArr3);
            g.e(str3, ((String) objArr3[0]).intern());
            return e;
        }
        g.c();
        String str4 = this.b;
        Object[] objArr4 = new Object[1];
        i((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 14, "\f\u000f\u0017\u0005\u0004\uffc0\u0002\u0019\uffc0\r\u0005\u0014\b\u000f\u0004\f\u000f\u0001\u0004￣\u0012\u0005\u0004\u0005\u000e\u0014\t\u0001\f\u0013\uffc0ￍ\uffc0\u0003\u0012\u0005\u0004\u0005\u000e\u0014\t\u0001\f\u0013\uffc0\u0015\u0013\u0001\u0007\u0005\uffc0\u0001\f", (ViewConfiguration.getTouchSlop() >> 8) + 53, View.combineMeasuredStates(0, 0) + 104, false, objArr4);
        g.d(str4, ((String) objArr4[0]).intern());
        switch (eVar.i() != e.d.c ? 'I' : (char) 16) {
            case 16:
                g.c();
                String str5 = this.b;
                Object[] objArr5 = new Object[1];
                i(Color.alpha(0) + 20, "\u000e\u000f\uffc0\u0016\u0001\f\t\u0004\t\u0014\u0019\uffc0\u0003\b\u0005\u0003\u000b\t\u000e\u0007\f\u000f\u0001\u0004￣\u0012\u0005\u0004\u0005\u000e\u0014\t\u0001\f\u0013\uffc0ￍ\uffc0\t\u000e\u0014\u0005\u0012\u000e\u0001\f\uffc0\u0003\u0012\u0005\u0004\u0005\u000e\u0014\t\u0001\f\u0013\uffc0\u0010\u0012\u000f\u0016\t\u0004\u0005\u0004ￌ\uffc0", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 69, 105 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), false, objArr5);
                g.d(str5, ((String) objArr5[0]).intern());
                dVar.c(eVar);
                switch (bVar != null ? ',' : '+') {
                    default:
                        bVar.d(eVar.b());
                    case '+':
                        return e;
                }
            default:
                a.d(context, eVar, new o.g.a() { // from class: o.h.a.2
                    public static final byte[] $$a = null;
                    public static final int $$b = 0;
                    private static int $10;
                    private static int $11;
                    private static int e;
                    private static int h;
                    private static int i;

                    static {
                        init$0();
                        $10 = 0;
                        $11 = 1;
                        i = 0;
                        h = 1;
                        e = 874635502;
                    }

                    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
                    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
                    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
                    /*
                        Code decompiled incorrectly, please refer to instructions dump.
                        To view partially-correct add '--show-bad-code' argument
                    */
                    private static void g(int r6, byte r7, short r8, java.lang.Object[] r9) {
                        /*
                            int r7 = r7 * 2
                            int r7 = 4 - r7
                            int r8 = r8 * 2
                            int r8 = 109 - r8
                            int r6 = r6 * 3
                            int r6 = r6 + 1
                            byte[] r0 = o.h.a.AnonymousClass2.$$a
                            byte[] r1 = new byte[r6]
                            int r6 = r6 + (-1)
                            r2 = 0
                            if (r0 != 0) goto L1c
                            r3 = r1
                            r4 = r2
                            r1 = r0
                            r0 = r9
                            r9 = r8
                            r8 = r7
                            goto L36
                        L1c:
                            r3 = r2
                        L1d:
                            byte r4 = (byte) r8
                            r1[r3] = r4
                            if (r3 != r6) goto L2a
                            java.lang.String r6 = new java.lang.String
                            r6.<init>(r1, r2)
                            r9[r2] = r6
                            return
                        L2a:
                            int r3 = r3 + 1
                            r4 = r0[r7]
                            r5 = r8
                            r8 = r7
                            r7 = r4
                            r4 = r3
                            r3 = r1
                            r1 = r0
                            r0 = r9
                            r9 = r5
                        L36:
                            int r7 = -r7
                            int r7 = r7 + r9
                            int r8 = r8 + 1
                            r9 = r0
                            r0 = r1
                            r1 = r3
                            r3 = r4
                            r5 = r8
                            r8 = r7
                            r7 = r5
                            goto L1d
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.h.a.AnonymousClass2.g(int, byte, short, java.lang.Object[]):void");
                    }

                    static void init$0() {
                        $$a = new byte[]{102, 46, -74, -23};
                        $$b = 68;
                    }

                    @Override // o.g.a
                    public final void d(e eVar2) {
                        g.c();
                        String str6 = a.this.b;
                        Object[] objArr6 = new Object[1];
                        f(69 - ((byte) KeyEvent.getModifierMetaStateMask()), "\r\u0013\b\u0000\u000b\u0012\uffbfￌ\uffbf\u0002\u0011\u0004\u0003\u0004\r\u0013\b\u0000\u000b\u0012\uffbf\u0015\u0000\u000b\b\u0003ￋ\uffbf\u0011\u0004\u0006\b\u0012\u0013\u0004\u0011\b\r\u0006\uffbf\u0013\u0007\u0004\f\uffbf\u0013\u000e\uffbf\u0000\u0014\u0013\u0007\u0004\r\u0013\b\u0002\u0000\u0013\b\u000e\r\uffbf\u0002\u000e\r\u0013\u0004\u0017\u0013\u000b\u000e\u0000\u0003￢\u0011\u0004\u0003\u0004", Color.blue(0) + 79, 242 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), false, objArr6);
                        g.d(str6, ((String) objArr6[0]).intern());
                        dVar.c(eVar2);
                        b bVar2 = bVar;
                        switch (bVar2 != null ? '\'' : 'D') {
                            case 'D':
                                break;
                            default:
                                int i5 = h + 17;
                                i = i5 % 128;
                                int i6 = i5 % 2;
                                bVar2.d(eVar2.b());
                                int i7 = h + 83;
                                i = i7 % 128;
                                if (i7 % 2 != 0) {
                                    break;
                                }
                                break;
                        }
                    }

                    @Override // o.g.a
                    public final void c(o.g.b bVar2) {
                        g.c();
                        String str6 = a.this.b;
                        StringBuilder sb2 = new StringBuilder();
                        Object[] objArr6 = new Object[1];
                        f(41 - (Process.myPid() >> 22), "\u0016ￄ\uffd1ￄ\b\r\u0010\u0005\u001a\u0012\rￄ\u0017\u0010\u0005\r\u0018\u0012\t\b\t\u0016\u0007ￄ\uffd1ￄ\u0017\u0010\u0005\r\u0018\u0012\t\b\t\u0016\uffe7\b\u0005\u0013\u0010ￄ\uffde\u0012\u0013\u0017\u0005\t", KeyEvent.keyCodeFromString("") + 48, ImageFormat.getBitsPerPixel(0) + 238, true, objArr6);
                        g.d(str6, sb2.append(((String) objArr6[0]).intern()).append(bVar2.name()).toString());
                        b bVar3 = bVar;
                        switch (bVar3 == null) {
                            case false:
                                int i5 = i + Opcodes.LUSHR;
                                h = i5 % 128;
                                int i6 = i5 % 2;
                                bVar3.c(eVar.b(), bVar2);
                                int i7 = h + 51;
                                i = i7 % 128;
                                int i8 = i7 % 2;
                                break;
                        }
                    }

                    private static void f(int i5, String str6, int i6, int i7, boolean z, Object[] objArr6) {
                        char[] charArray = str6 != null ? str6.toCharArray() : str6;
                        h hVar = new h();
                        char[] cArr = new char[i6];
                        hVar.a = 0;
                        while (hVar.a < i6) {
                            int i8 = $10 + 29;
                            $11 = i8 % 128;
                            int i9 = i8 % 2;
                            hVar.b = charArray[hVar.a];
                            cArr[hVar.a] = (char) (i7 + hVar.b);
                            int i10 = hVar.a;
                            try {
                                Object[] objArr7 = {Integer.valueOf(cArr[i10]), Integer.valueOf(e)};
                                Object obj = o.e.a.s.get(2038615114);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(((Process.getThreadPriority(0) + 20) >> 6) + 12, (char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), 459 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                                    byte b = (byte) 0;
                                    byte b2 = b;
                                    Object[] objArr8 = new Object[1];
                                    g(b, b2, (byte) (b2 + 1), objArr8);
                                    obj = cls.getMethod((String) objArr8[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2038615114, obj);
                                }
                                cArr[i10] = ((Character) ((Method) obj).invoke(null, objArr7)).charValue();
                                try {
                                    Object[] objArr9 = {hVar, hVar};
                                    Object obj2 = o.e.a.s.get(-1412673904);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(11 - View.resolveSizeAndState(0, 0, 0), (char) Color.green(0), 313 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                                        byte b3 = (byte) 0;
                                        byte b4 = b3;
                                        Object[] objArr10 = new Object[1];
                                        g(b3, b4, b4, objArr10);
                                        obj2 = cls2.getMethod((String) objArr10[0], Object.class, Object.class);
                                        o.e.a.s.put(-1412673904, obj2);
                                    }
                                    ((Method) obj2).invoke(null, objArr9);
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                        if (i5 > 0) {
                            hVar.c = i5;
                            char[] cArr2 = new char[i6];
                            System.arraycopy(cArr, 0, cArr2, 0, i6);
                            System.arraycopy(cArr2, 0, cArr, i6 - hVar.c, hVar.c);
                            System.arraycopy(cArr2, hVar.c, cArr, 0, i6 - hVar.c);
                            int i11 = $10 + 47;
                            $11 = i11 % 128;
                            int i12 = i11 % 2;
                        }
                        switch (z ? '1' : '7') {
                            case '7':
                                break;
                            default:
                                int i13 = $10 + 45;
                                $11 = i13 % 128;
                                int i14 = i13 % 2;
                                char[] cArr3 = new char[i6];
                                hVar.a = 0;
                                while (true) {
                                    switch (hVar.a < i6 ? 'D' : 'c') {
                                        case 'D':
                                            int i15 = $10 + 1;
                                            $11 = i15 % 128;
                                            int i16 = i15 % 2;
                                            cArr3[hVar.a] = cArr[(i6 - hVar.a) - 1];
                                            try {
                                                Object[] objArr11 = {hVar, hVar};
                                                Object obj3 = o.e.a.s.get(-1412673904);
                                                if (obj3 == null) {
                                                    Class cls3 = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0') + 12, (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), 313 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                                    byte b5 = (byte) 0;
                                                    byte b6 = b5;
                                                    Object[] objArr12 = new Object[1];
                                                    g(b5, b6, b6, objArr12);
                                                    obj3 = cls3.getMethod((String) objArr12[0], Object.class, Object.class);
                                                    o.e.a.s.put(-1412673904, obj3);
                                                }
                                                ((Method) obj3).invoke(null, objArr11);
                                            } catch (Throwable th3) {
                                                Throwable cause3 = th3.getCause();
                                                if (cause3 == null) {
                                                    throw th3;
                                                }
                                                throw cause3;
                                            }
                                        default:
                                            cArr = cArr3;
                                            break;
                                    }
                                }
                        }
                        objArr6[0] = new String(cArr);
                    }
                }, new o.i.b(c2, eVar.b()), this.a);
                return e;
        }
    }

    public final int hashCode() {
        int i = f + 9;
        d = i % 128;
        switch (i % 2 != 0) {
            case true:
                super.hashCode();
                throw null;
            default:
                return super.hashCode();
        }
    }

    public final boolean equals(Object obj) {
        int i = d + 1;
        f = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = f + Opcodes.LMUL;
        d = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return equals;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    public final String toString() {
        int i = f + 67;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                return super.toString();
            default:
                int i2 = 29 / 0;
                return super.toString();
        }
    }

    protected final void finalize() throws Throwable {
        int i = d + 93;
        f = i % 128;
        boolean z = i % 2 != 0;
        super.finalize();
        switch (z) {
            case false:
                int i2 = 33 / 0;
                return;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(int r19, java.lang.String r20, int r21, int r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 534
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.h.a.i(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
