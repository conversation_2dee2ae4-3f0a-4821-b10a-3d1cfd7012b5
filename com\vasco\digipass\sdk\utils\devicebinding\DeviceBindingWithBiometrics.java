package com.vasco.digipass.sdk.utils.devicebinding;

import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.devicebinding.obfuscated.i;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u0000 \u000b2\u00020\u0001:\u0001\u000bJ(\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tH&¨\u0006\f"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingWithBiometrics;", "", "fingerprint", "", "salt", "", "deviceBindingBiometricAuthenticationCallback", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;", "invalidateByBiometricEnrollment", "", "fallbackOnDeviceCredential", "Companion", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingWithBiometrics.smali */
public interface DeviceBindingWithBiometrics {

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = Companion.a;

    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002¢\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006¨\u0006\u0007"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingWithBiometrics$Companion;", "", "()V", "createDeviceBindingWithBiometrics", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingWithBiometrics;", "activity", "Landroidx/fragment/app/FragmentActivity;", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingWithBiometrics$Companion.smali */
    public static final class Companion {
        static final /* synthetic */ Companion a = new Companion();

        private Companion() {
        }

        public final DeviceBindingWithBiometrics createDeviceBindingWithBiometrics(FragmentActivity activity) {
            Intrinsics.checkNotNullParameter(activity, "activity");
            return new i(activity);
        }
    }

    void fingerprint(String salt, DeviceBindingBiometricAuthenticationCallback deviceBindingBiometricAuthenticationCallback, boolean invalidateByBiometricEnrollment, boolean fallbackOnDeviceCredential) throws DeviceBindingSDKException;
}
