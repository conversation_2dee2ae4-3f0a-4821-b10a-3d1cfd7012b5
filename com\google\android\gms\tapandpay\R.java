package com.google.android.gms.tapandpay;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$animator.smali */
    public static final class animator {
        public static int tp_progress_animation = 0x7f020015;

        private animator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$attr.smali */
    public static final class attr {
        public static int backgroundColor = 0x7f0400b7;
        public static int colorPrimaryVariantGoogle = 0x7f040136;
        public static int colorSecondary = 0x7f040138;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$color.smali */
    public static final class color {
        public static int tapandpay_sdk_default_color_background = 0x7f0600f3;
        public static int tapandpay_sdk_default_color_primary_variant = 0x7f0600f4;
        public static int tapandpay_sdk_default_color_secondary = 0x7f0600f5;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$drawable.smali */
    public static final class drawable {
        public static int tp_issuer_progress = 0x7f08016f;
        public static int tp_issuer_progress_animated = 0x7f080170;

        private drawable() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$string.smali */
    public static final class string {
        public static int tp_loading_spinner_content_desc = 0x7f12013b;

        private string() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\com\google\android\gms\tapandpay\R$style.smali */
    public static final class style {
        public static int TapAndPayTheme = 0x7f130141;

        private style() {
        }
    }

    private R() {
    }
}
