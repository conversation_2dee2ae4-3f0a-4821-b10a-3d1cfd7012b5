package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECCurve;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e8.smali */
public abstract class e8 {
    private ECCurve a;
    private X9ECParameters b;

    protected ECCurve a() {
        return b().getCurve();
    }

    protected abstract X9ECParameters b();

    public synchronized ECCurve c() {
        if (this.a == null) {
            this.a = a();
        }
        return this.a;
    }

    public synchronized X9ECParameters d() {
        if (this.b == null) {
            this.b = b();
        }
        return this.b;
    }
}
