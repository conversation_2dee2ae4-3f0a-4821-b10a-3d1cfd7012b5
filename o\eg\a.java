package o.eg;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\a.smali */
final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static boolean b;
    private static int c;
    private static char[] d;
    private static boolean e;
    private static int g;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        a = 5626000331349636571L;
        d = new char[]{61809, 61806, 61819, 61820, 61810, 61743, 61571, 61816, 61574, 61823, 61804, 61817, 61805, 61569, 61821, 61811, 61793, 61570, 61814, 61753};
        b = true;
        e = true;
        c = 782102799;
    }

    static void init$0() {
        $$a = new byte[]{45, 88, 59, 34};
        $$b = 204;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = r6 + 4
            byte[] r0 = o.eg.a.$$a
            int r7 = 121 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r6
            goto L37
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1c:
            byte r4 = (byte) r6
            int r7 = r7 + 1
            r1[r3] = r4
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L37:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.a.k(byte, int, byte, java.lang.Object[]):void");
    }

    a() {
    }

    static double b(double d2) throws d {
        int i = h + 95;
        g = i % 128;
        int i2 = i % 2;
        switch (!Double.isInfinite(d2) ? '/' : '2') {
            case '2':
                break;
            default:
                int i3 = h + 89;
                g = i3 % 128;
                switch (i3 % 2 != 0 ? '?' : 'c') {
                    case Opcodes.DADD /* 99 */:
                        if (!Double.isNaN(d2)) {
                            return d2;
                        }
                        break;
                    default:
                        Double.isNaN(d2);
                        throw null;
                }
        }
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i("勌ᒯ劊\ue9ae㦶懄ϔ曘蚵巟埒\u0acf廒ফ篨㻯⺑\ue5fe迤\ue2e3˯퇋펐際盰趎\ue793뫐ꪌ", ViewConfiguration.getFadingEdgeLength() >> 16, objArr);
        throw new d(sb.append(((String) objArr[0]).intern()).append(d2).toString());
    }

    static Boolean c(Object obj) {
        if (obj instanceof Boolean) {
            int i = h + 41;
            g = i % 128;
            int i2 = i % 2;
            return (Boolean) obj;
        }
        switch (!(obj instanceof String)) {
            case false:
                int i3 = g + 91;
                h = i3 % 128;
                int i4 = i3 % 2;
                String str = (String) obj;
                Object[] objArr = new Object[1];
                i("ꙛ㩓\ua62f惷⒯伥誊篆", TextUtils.lastIndexOf("", '0') + 1, objArr);
                switch (((String) objArr[0]).intern().equalsIgnoreCase(str)) {
                    case false:
                        Object[] objArr2 = new Object[1];
                        j(null, (ViewConfiguration.getTouchSlop() >> 8) + 127, null, "\u0085\u0084\u0083\u0082\u0081", objArr2);
                        if (((String) objArr2[0]).intern().equalsIgnoreCase(str)) {
                            return Boolean.FALSE;
                        }
                    default:
                        int i5 = h + Opcodes.LSUB;
                        g = i5 % 128;
                        if (i5 % 2 != 0) {
                            Boolean bool = Boolean.TRUE;
                            throw null;
                        }
                        Boolean bool2 = Boolean.TRUE;
                        int i6 = h + 99;
                        g = i6 % 128;
                        int i7 = i6 % 2;
                        return bool2;
                }
            default:
                return null;
        }
    }

    static Double d(Object obj) {
        int i = h;
        int i2 = i + 77;
        g = i2 % 128;
        int i3 = i2 % 2;
        switch (obj instanceof Double ? (char) 5 : 'H') {
            case 5:
                int i4 = i + 21;
                g = i4 % 128;
                int i5 = i4 % 2;
                return (Double) obj;
            default:
                switch (obj instanceof Number ? (char) 11 : 'T') {
                    case Opcodes.BASTORE /* 84 */:
                        if (obj instanceof String) {
                            try {
                                return Double.valueOf((String) obj);
                            } catch (NumberFormatException e2) {
                            }
                        }
                        int i6 = h + 1;
                        g = i6 % 128;
                        int i7 = i6 % 2;
                        return null;
                    default:
                        return Double.valueOf(((Number) obj).doubleValue());
                }
        }
    }

    static Integer a(Object obj) {
        int i = g + 21;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        switch (obj instanceof Integer ? '\b' : 'a') {
            case Opcodes.LADD /* 97 */:
                if (obj instanceof Number) {
                    Integer valueOf = Integer.valueOf(((Number) obj).intValue());
                    int i4 = g + 83;
                    h = i4 % 128;
                    if (i4 % 2 != 0) {
                        return valueOf;
                    }
                    throw null;
                }
                switch (obj instanceof String) {
                    case false:
                        break;
                    default:
                        try {
                            Integer valueOf2 = Integer.valueOf((int) Double.parseDouble((String) obj));
                            int i5 = g + 41;
                            h = i5 % 128;
                            int i6 = i5 % 2;
                            return valueOf2;
                        } catch (NumberFormatException e2) {
                            break;
                        }
                }
                int i7 = h + 63;
                g = i7 % 128;
                int i8 = i7 % 2;
                return null;
            default:
                int i9 = i2 + 45;
                g = i9 % 128;
                if (i9 % 2 != 0) {
                }
                return (Integer) obj;
        }
    }

    static Short b(Object obj) {
        int i = g + 55;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        switch (obj instanceof Short) {
            case true:
                int i4 = i2 + 39;
                g = i4 % 128;
                if (i4 % 2 == 0) {
                    return (Short) obj;
                }
                throw null;
            default:
                switch (obj instanceof Number) {
                    case false:
                        switch (obj instanceof String ? false : true) {
                            case false:
                                try {
                                    Short valueOf = Short.valueOf((String) obj);
                                    int i5 = h + 23;
                                    g = i5 % 128;
                                    switch (i5 % 2 != 0 ? '\\' : (char) 19) {
                                        case 19:
                                            return valueOf;
                                        default:
                                            int i6 = 65 / 0;
                                            return valueOf;
                                    }
                                } catch (NumberFormatException e2) {
                                }
                            default:
                                return null;
                        }
                    default:
                        int i7 = i2 + 77;
                        g = i7 % 128;
                        if (i7 % 2 != 0) {
                        }
                        return Short.valueOf(((Number) obj).shortValue());
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0012, code lost:
    
        if ((r5 instanceof java.lang.Long) != false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static java.lang.Long e(java.lang.Object r5) {
        /*
            int r0 = o.eg.a.g
            int r1 = r0 + 91
            int r2 = r1 % 128
            o.eg.a.h = r2
            int r1 = r1 % 2
            r2 = 1
            r3 = 0
            if (r1 != 0) goto L17
            boolean r1 = r5 instanceof java.lang.Long
            r4 = 2
            int r4 = r4 / r3
            if (r1 == 0) goto L21
            goto L22
        L15:
            r5 = move-exception
            throw r5
        L17:
            boolean r1 = r5 instanceof java.lang.Long
            if (r1 == 0) goto L1d
            r1 = r2
            goto L1e
        L1d:
            r1 = r3
        L1e:
            switch(r1) {
                case 1: goto L22;
                default: goto L21;
            }
        L21:
            goto L25
        L22:
            java.lang.Long r5 = (java.lang.Long) r5
            return r5
        L25:
            boolean r1 = r5 instanceof java.lang.Number
            if (r1 == 0) goto L2b
            r2 = r3
            goto L2c
        L2b:
        L2c:
            switch(r2) {
                case 1: goto L3a;
                default: goto L2f;
            }
        L2f:
            int r0 = r0 + 73
            int r1 = r0 % 128
            o.eg.a.h = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L67
            goto L67
        L3a:
            boolean r1 = r5 instanceof java.lang.String
            if (r1 == 0) goto L40
            r1 = 5
            goto L42
        L40:
            r1 = 18
        L42:
            switch(r1) {
                case 5: goto L46;
                default: goto L45;
            }
        L45:
            goto L5b
        L46:
            int r0 = r0 + 33
            int r1 = r0 % 128
            o.eg.a.h = r1
            int r0 = r0 % 2
            java.lang.String r5 = (java.lang.String) r5     // Catch: java.lang.NumberFormatException -> L5a
            double r0 = java.lang.Double.parseDouble(r5)     // Catch: java.lang.NumberFormatException -> L5a
            long r0 = (long) r0     // Catch: java.lang.NumberFormatException -> L5a
            java.lang.Long r5 = java.lang.Long.valueOf(r0)     // Catch: java.lang.NumberFormatException -> L5a
            return r5
        L5a:
            r5 = move-exception
        L5b:
            int r5 = o.eg.a.g
            int r5 = r5 + 117
            int r0 = r5 % 128
            o.eg.a.h = r0
            int r5 = r5 % 2
            r5 = 0
            return r5
        L67:
            java.lang.Number r5 = (java.lang.Number) r5
            long r0 = r5.longValue()
            java.lang.Long r5 = java.lang.Long.valueOf(r0)
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.a.e(java.lang.Object):java.lang.Long");
    }

    static String f(Object obj) {
        Object obj2 = null;
        switch (obj instanceof String ? 'S' : 'H') {
            case 'H':
                switch (obj != null ? '/' : 'A') {
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        int i = g + 53;
                        h = i % 128;
                        int i2 = i % 2;
                        return null;
                    default:
                        return String.valueOf(obj);
                }
            default:
                int i3 = g + Opcodes.LSHL;
                int i4 = i3 % 128;
                h = i4;
                if (i3 % 2 == 0) {
                    obj2.hashCode();
                    throw null;
                }
                String str = (String) obj;
                int i5 = i4 + Opcodes.LSHR;
                g = i5 % 128;
                int i6 = i5 % 2;
                return str;
        }
    }

    public static d b(Object obj, Object obj2, String str) throws d {
        int i = g + 97;
        int i2 = i % 128;
        h = i2;
        if (i % 2 == 0) {
            throw null;
        }
        if (obj2 != null) {
            int i3 = i2 + 19;
            g = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    if (obj2 != b.b) {
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr = new Object[1];
                        i("\udadd㔲\uda8bꚙᯍ䁗䳽䒴ຨ簆", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
                        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(obj2);
                        Object[] objArr2 = new Object[1];
                        j(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u0086\u0087\u0082\u0086", objArr2);
                        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(obj);
                        Object[] objArr3 = new Object[1];
                        j(null, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0086\u0085\u008a\u0089\u0087\u0086\u0081\u0088\u0086", objArr3);
                        StringBuilder append3 = append2.append(((String) objArr3[0]).intern()).append(obj2.getClass().getName());
                        Object[] objArr4 = new Object[1];
                        j(null, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 127, null, "\u0086\u0088\u0087\u0086\u0090\u0085\u0087\u008f\u0085\u008e\u008c\u0088\u008b\u0086\u0085\u008d\u0086\u0087\u0088\u008c\u008c\u0082\u008b\u0086", objArr4);
                        throw new d(append3.append(((String) objArr4[0]).intern()).append(str).toString());
                    }
                    break;
                default:
                    Object obj3 = b.b;
                    throw null;
            }
        }
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr5 = new Object[1];
        i("\udca0\ue3fe\udcf6睈\udede際鴬膧ࣕ\uaaca줱\uedb6璠", 1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr5);
        StringBuilder append4 = sb2.append(((String) objArr5[0]).intern()).append(obj);
        Object[] objArr6 = new Object[1];
        i("묜攅물ꋟ妴ၨ䢤ژ潢ⱤᲫ櫄ጒ", TextUtils.getCapsMode("", 0, 0), objArr6);
        throw new d(append4.append(((String) objArr6[0]).intern()).toString());
    }

    public static d a(Object obj, String str) throws d {
        boolean z;
        int i = g;
        int i2 = i + 91;
        h = i2 % 128;
        int i3 = i2 % 2;
        if (obj == null) {
            z = true;
        } else {
            z = false;
        }
        switch (z) {
            case true:
                break;
            default:
                int i4 = i + Opcodes.DSUB;
                h = i4 % 128;
                int i5 = i4 % 2;
                if (obj != b.b) {
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr = new Object[1];
                    i("\udadd㔲\uda8bꚙᯍ䁗䳽䒴ຨ簆", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
                    StringBuilder append = sb.append(((String) objArr[0]).intern()).append(obj);
                    Object[] objArr2 = new Object[1];
                    j(null, 127 - (Process.myPid() >> 22), null, "\u0086\u0085\u008a\u0089\u0087\u0086\u0081\u0088\u0086", objArr2);
                    StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(obj.getClass().getName());
                    Object[] objArr3 = new Object[1];
                    j(null, TextUtils.indexOf("", "", 0, 0) + 127, null, "\u0086\u0088\u0087\u0086\u0090\u0085\u0087\u008f\u0085\u008e\u008c\u0088\u008b\u0086\u0085\u008d\u0086\u0087\u0088\u008c\u008c\u0082\u008b\u0086", objArr3);
                    throw new d(append2.append(((String) objArr3[0]).intern()).append(str).toString());
                }
                break;
        }
        Object[] objArr4 = new Object[1];
        j(null, (Process.myTid() >> 22) + 127, null, "\u0094\u0083\u0083\u0092\u008c\u0086\u0084\u0093\u0086\u0085\u0092\u0083\u0082\u0091", objArr4);
        throw new d(((String) objArr4[0]).intern());
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 394
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.a.i(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void j(String str, int i, int[] iArr, String str2, Object[] objArr) {
        String str3 = str2;
        int i2 = 2;
        int i3 = 0;
        byte[] bArr = str3;
        if (str3 != null) {
            int i4 = $11 + 47;
            $10 = i4 % 128;
            switch (i4 % 2 != 0 ? '`' : 'c') {
                case Opcodes.IADD /* 96 */:
                    int i5 = 98 / 0;
                    bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
                default:
                    bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
            }
        }
        byte[] bArr2 = bArr;
        char[] charArray = str != null ? str.toCharArray() : str;
        o.a.j jVar = new o.a.j();
        char[] cArr = d;
        int i6 = -1;
        if (cArr != null) {
            int i7 = $11 + 1;
            $10 = i7 % 128;
            int i8 = i7 % 2;
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i9 = 0;
            while (i9 < length) {
                int i10 = $10 + 35;
                $11 = i10 % 128;
                if (i10 % i2 == 0) {
                    try {
                        Object[] objArr2 = new Object[1];
                        objArr2[i3] = Integer.valueOf(cArr[i9]);
                        Object obj = o.e.a.s.get(1085633688);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getJumpTapTimeout() >> 16), (char) View.combineMeasuredStates(i3, i3), Color.green(i3) + 493);
                            byte b2 = (byte) i6;
                            byte b3 = (byte) (b2 + 1);
                            Object[] objArr3 = new Object[1];
                            k(b2, b3, b3, objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1085633688, obj);
                        }
                        cArr2[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i9 *= 1;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } else {
                    try {
                        Object[] objArr4 = {Integer.valueOf(cArr[i9])};
                        Object obj2 = o.e.a.s.get(1085633688);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(11 - Gravity.getAbsoluteGravity(0, 0), (char) TextUtils.indexOf("", "", 0, 0), 493 - KeyEvent.keyCodeFromString(""));
                            byte b4 = (byte) (-1);
                            byte b5 = (byte) (b4 + 1);
                            Object[] objArr5 = new Object[1];
                            k(b4, b5, b5, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                            o.e.a.s.put(1085633688, obj2);
                        }
                        cArr2[i9] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                        i9++;
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                i2 = 2;
                i3 = 0;
                i6 = -1;
            }
            int i11 = $10 + 13;
            $11 = i11 % 128;
            int i12 = i11 % 2;
            cArr = cArr2;
        }
        try {
            Object[] objArr6 = {Integer.valueOf(c)};
            Object obj3 = o.e.a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 10, (char) (8856 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 324 - Color.blue(0));
                byte b6 = (byte) (-1);
                byte b7 = (byte) (b6 + 4);
                Object[] objArr7 = new Object[1];
                k(b6, b7, (byte) (b7 - 3), objArr7);
                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
            switch (e) {
                case false:
                    if (b) {
                        jVar.e = charArray.length;
                        char[] cArr3 = new char[jVar.e];
                        jVar.c = 0;
                        while (jVar.c < jVar.e) {
                            cArr3[jVar.c] = (char) (cArr[charArray[(jVar.e - 1) - jVar.c] - i] - intValue);
                            try {
                                Object[] objArr8 = {jVar, jVar};
                                Object obj4 = o.e.a.s.get(745816316);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(10 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), View.resolveSize(0, 0) + 207);
                                    byte length2 = (byte) $$a.length;
                                    Object[] objArr9 = new Object[1];
                                    k((byte) (-1), length2, (byte) (length2 - 4), objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(745816316, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        objArr[0] = new String(cArr3);
                        return;
                    }
                    jVar.e = iArr.length;
                    char[] cArr4 = new char[jVar.e];
                    int i13 = 0;
                    while (true) {
                        jVar.c = i13;
                        if (jVar.c >= jVar.e) {
                            String str4 = new String(cArr4);
                            int i14 = $11 + Opcodes.LSHL;
                            $10 = i14 % 128;
                            int i15 = i14 % 2;
                            objArr[0] = str4;
                            return;
                        }
                        cArr4[jVar.c] = (char) (cArr[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                        i13 = jVar.c + 1;
                    }
                default:
                    jVar.e = bArr2.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                        try {
                            Object[] objArr10 = {jVar, jVar};
                            Object obj5 = o.e.a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls5 = (Class) o.e.a.c(10 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) KeyEvent.normalizeMetaState(0), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 207);
                                byte length3 = (byte) $$a.length;
                                Object[] objArr11 = new Object[1];
                                k((byte) (-1), length3, (byte) (length3 - 4), objArr11);
                                obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(null, objArr10);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
