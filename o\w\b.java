package o.w;

import android.content.Context;
import android.text.TextUtils;
import com.esotericsoftware.asm.Opcodes;
import o.bb.a;
import o.p.g;
import o.z.c;
import o.z.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\w\b.smali */
public abstract class b extends e {
    private static int k;
    private static int l = 1;
    private static char[] m;

    /* renamed from: o, reason: collision with root package name */
    private static char f113o;
    String h;
    private final String i;

    static {
        k = 0;
        v();
        TextUtils.getOffsetAfter("", 0);
        TextUtils.lastIndexOf("", '0', 0, 0);
        int i = l + 21;
        k = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void v() {
        m = new char[]{30542, 30540, 30556, 30560, 30586, 30562, 30567, 30583, 30587, 30534, 30572, 30539, 30536, 30537, 30559, 30570, 30588, 30571, 30580, 30589, 30574, 30566, 30561, 30538, 30581};
        f113o = (char) 17040;
    }

    abstract String p();

    abstract byte[] s();

    abstract c t();

    abstract Long x();

    static /* synthetic */ g a(b bVar) {
        int i = l + Opcodes.DMUL;
        k = i % 128;
        int i2 = i % 2;
        g l2 = bVar.l();
        int i3 = k + 93;
        l = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return l2;
            default:
                int i4 = 18 / 0;
                return l2;
        }
    }

    static /* synthetic */ g b(b bVar) {
        int i = k + Opcodes.LMUL;
        l = i % 128;
        int i2 = i % 2;
        g l2 = bVar.l();
        int i3 = k + 9;
        l = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l2;
        }
    }

    static /* synthetic */ g c(b bVar) {
        int i = l + 19;
        k = i % 128;
        int i2 = i % 2;
        g l2 = bVar.l();
        int i3 = l + 33;
        k = i3 % 128;
        switch (i3 % 2 != 0 ? '-' : '8') {
            case '-':
                int i4 = 74 / 0;
                return l2;
            default:
                return l2;
        }
    }

    static /* synthetic */ g d(b bVar) {
        int i = l + Opcodes.DNEG;
        k = i % 128;
        switch (i % 2 != 0 ? 'I' : (char) 24) {
            case 'I':
                bVar.l();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bVar.l();
        }
    }

    static /* synthetic */ void e(b bVar) {
        int i = l + 61;
        k = i % 128;
        int i2 = i % 2;
        bVar.n();
        int i3 = k + 63;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    static /* synthetic */ g h(b bVar) {
        int i = k + 97;
        l = i % 128;
        switch (i % 2 == 0 ? '[' : (char) 11) {
            case 11:
                return bVar.l();
            default:
                bVar.l();
                throw null;
        }
    }

    static /* synthetic */ g i(b bVar) {
        int i = l + 53;
        k = i % 128;
        switch (i % 2 != 0 ? (char) 7 : (char) 27) {
            case 27:
                g l2 = bVar.l();
                int i2 = k + 9;
                l = i2 % 128;
                int i3 = i2 % 2;
                return l2;
            default:
                bVar.l();
                throw null;
        }
    }

    static /* synthetic */ g j(b bVar) {
        int i = l + Opcodes.LNEG;
        k = i % 128;
        int i2 = i % 2;
        g l2 = bVar.l();
        int i3 = k + 37;
        l = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return l2;
        }
    }

    b(String str, String str2) {
        super(str, false);
        this.i = str2;
    }

    protected final void b(Context context, o.ei.c cVar, o.h.d dVar, e.d dVar2, boolean z, Long l2) {
        new o.z.e(context, dVar2, cVar).c(dVar, o(), this.i, t(), p(), s(), z, l2);
        int i = l + 97;
        k = i % 128;
        int i2 = i % 2;
    }

    /* renamed from: o.w.b$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\w\b$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] b;
        private static int c;
        private static int d = 1;

        static {
            c = 0;
            int[] iArr = new int[a.values().length];
            b = iArr;
            try {
                iArr[a.aA.ordinal()] = 1;
                int i = d;
                int i2 = (i & 59) + (i | 59);
                c = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e) {
            }
        }
    }

    @Override // o.p.h
    public final void a(Context context, o.ei.c cVar, o.h.d dVar) {
        b(context, cVar, dVar, new e.d() { // from class: o.w.b.4
            private static int d = 0;
            private static int a = 1;

            @Override // o.z.e.d
            public final void d(String str) {
                int i = a;
                int i2 = (i & Opcodes.LMUL) + (i | Opcodes.LMUL);
                d = i2 % 128;
                int i3 = i2 % 2;
                b.this.h = str;
                switch (b.c(b.this) == null) {
                    case true:
                        break;
                    default:
                        int i4 = (d + 108) - 1;
                        a = i4 % 128;
                        switch (i4 % 2 == 0 ? (char) 11 : 'N') {
                            case 'N':
                                b.b(b.this).onProcessSuccess();
                                break;
                            default:
                                b.b(b.this).onProcessSuccess();
                                int i5 = 33 / 0;
                                break;
                        }
                }
                int i6 = (d + 34) - 1;
                a = i6 % 128;
                int i7 = i6 % 2;
            }

            /* JADX WARN: Code restructure failed: missing block: B:24:0x0063, code lost:
            
                if (r5 != false) goto L28;
             */
            /* JADX WARN: Code restructure failed: missing block: B:25:0x007d, code lost:
            
                o.w.b.h(r3.b).onAuthenticationDeclined();
             */
            /* JADX WARN: Code restructure failed: missing block: B:26:0x0086, code lost:
            
                return;
             */
            /* JADX WARN: Code restructure failed: missing block: B:27:0x006c, code lost:
            
                o.w.b.d(r3.b).onError(new o.bv.c(fr.antelop.sdk.AntelopErrorCode.CustomerCredentialsInvalid));
             */
            /* JADX WARN: Code restructure failed: missing block: B:28:0x007c, code lost:
            
                return;
             */
            /* JADX WARN: Code restructure failed: missing block: B:32:0x0069, code lost:
            
                if (r5 != false) goto L28;
             */
            /* JADX WARN: Failed to find 'out' block for switch in B:46:0x00be. Please report as an issue. */
            @Override // o.z.e.d
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void d(o.bb.d r4, boolean r5) {
                /*
                    r3 = this;
                    int r0 = o.w.b.AnonymousClass4.d
                    r1 = r0 ^ 33
                    r0 = r0 & 33
                    r2 = 1
                    int r0 = r0 << r2
                    int r1 = r1 + r0
                    int r0 = r1 % 128
                    o.w.b.AnonymousClass4.a = r0
                    int r1 = r1 % 2
                    if (r1 != 0) goto L15
                    r0 = 12
                    goto L17
                L15:
                    r0 = 23
                L17:
                    switch(r0) {
                        case 23: goto L28;
                        default: goto L1a;
                    }
                L1a:
                    int[] r5 = o.w.b.AnonymousClass3.b
                    o.bb.a r4 = r4.d()
                    int r4 = r4.ordinal()
                    r4 = r5[r4]
                    goto Lc4
                L28:
                    int[] r0 = o.w.b.AnonymousClass3.b
                    o.bb.a r1 = r4.d()
                    int r1 = r1.ordinal()
                    r0 = r0[r1]
                    r1 = 0
                    switch(r0) {
                        case 1: goto L39;
                        default: goto L38;
                    }
                L38:
                    goto L89
                L39:
                    o.w.b r4 = o.w.b.this
                    o.w.b.e(r4)
                    o.w.b r4 = o.w.b.this
                    o.p.g r4 = o.w.b.a(r4)
                    if (r4 == 0) goto L49
                    r4 = 87
                    goto L4b
                L49:
                    r4 = 62
                L4b:
                    switch(r4) {
                        case 62: goto L97;
                        default: goto L4e;
                    }
                L4e:
                    int r4 = o.w.b.AnonymousClass4.a
                    int r4 = r4 + 59
                    int r0 = r4 % 128
                    o.w.b.AnonymousClass4.d = r0
                    int r4 = r4 % 2
                    if (r4 == 0) goto L5d
                    r4 = 44
                    goto L5f
                L5d:
                    r4 = 15
                L5f:
                    switch(r4) {
                        case 15: goto L63;
                        default: goto L62;
                    }
                L62:
                    goto L66
                L63:
                    if (r5 == 0) goto L7d
                L65:
                    goto L6c
                L66:
                    r4 = 27
                    int r4 = r4 / r1
                    if (r5 == 0) goto L7d
                    goto L65
                L6c:
                    o.w.b r4 = o.w.b.this
                    o.p.g r4 = o.w.b.d(r4)
                    o.bv.c r5 = new o.bv.c
                    fr.antelop.sdk.AntelopErrorCode r0 = fr.antelop.sdk.AntelopErrorCode.CustomerCredentialsInvalid
                    r5.<init>(r0)
                    r4.onError(r5)
                    return
                L7d:
                    o.w.b r4 = o.w.b.this
                    o.p.g r4 = o.w.b.h(r4)
                    r4.onAuthenticationDeclined()
                    return
                L87:
                    r4 = move-exception
                    throw r4
                L89:
                    o.w.b r5 = o.w.b.this
                    o.p.g r5 = o.w.b.i(r5)
                    if (r5 == 0) goto L93
                    r5 = r1
                    goto L94
                L93:
                    r5 = r2
                L94:
                    switch(r5) {
                        case 0: goto L98;
                        default: goto L97;
                    }
                L97:
                    goto Lc3
                L98:
                    int r5 = o.w.b.AnonymousClass4.a
                    int r5 = r5 + 117
                    int r0 = r5 % 128
                    o.w.b.AnonymousClass4.d = r0
                    int r5 = r5 % 2
                    o.w.b r5 = o.w.b.this
                    o.p.g r5 = o.w.b.j(r5)
                    o.bv.c r4 = o.bv.c.c(r4)
                    r5.onError(r4)
                    int r4 = o.w.b.AnonymousClass4.d
                    int r4 = r4 + 124
                    int r4 = r4 - r2
                    int r5 = r4 % 128
                    o.w.b.AnonymousClass4.a = r5
                    int r4 = r4 % 2
                    if (r4 != 0) goto Lbd
                    goto Lbe
                Lbd:
                    r2 = r1
                Lbe:
                    switch(r2) {
                        case 0: goto Lc2;
                        default: goto Lc1;
                    }
                Lc1:
                    goto L97
                Lc2:
                Lc3:
                    return
                Lc4:
                    r4 = 0
                    r4.hashCode()     // Catch: java.lang.Throwable -> Lc9
                    throw r4     // Catch: java.lang.Throwable -> Lc9
                Lc9:
                    r4 = move-exception
                    throw r4
                */
                throw new UnsupportedOperationException("Method not decompiled: o.w.b.AnonymousClass4.d(o.bb.d, boolean):void");
            }
        }, false, x());
        int i = k + Opcodes.DDIV;
        l = i % 128;
        switch (i % 2 == 0 ? (char) 14 : (char) 1) {
            case 14:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    protected String w() {
        int i = k + 31;
        l = i % 128;
        switch (i % 2 == 0 ? '\\' : '-') {
            case Opcodes.DUP2 /* 92 */:
                int i2 = 98 / 0;
                return this.i;
            default:
                return this.i;
        }
    }
}
