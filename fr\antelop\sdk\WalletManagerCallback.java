package fr.antelop.sdk;

import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.CustomerCredentialsRequiredReason;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\WalletManagerCallback.smali */
public interface WalletManagerCallback {
    void onAsyncRequestError(AsyncRequestType asyncRequestType, AntelopError antelopError, Object obj);

    void onAsyncRequestSuccess(AsyncRequestType asyncRequestType, Object obj);

    void onConnectionError(AntelopError antelopError, Object obj);

    void onConnectionSuccess(Wallet wallet, Object obj);

    void onCredentialsRequired(CustomerCredentialsRequiredReason customerCredentialsRequiredReason, AntelopError antelopError, Object obj);

    void onLocalAuthenticationError(CustomerAuthenticationMethodType customerAuthenticationMethodType, LocalAuthenticationErrorReason localAuthenticationErrorReason, String str, Object obj);

    void onLocalAuthenticationSuccess(CustomerAuthenticationMethodType customerAuthenticationMethodType, Object obj);

    void onProvisioningRequired(Object obj);
}
