package o.ep;

import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\e.smali */
public final class e {
    private static int f = 0;
    private static int g = 1;
    private final String a;
    private final d b;
    private final String c;
    private final Long d;
    private Integer e;
    private final Boolean j;

    public e(String str, Long l, String str2) {
        this.c = str;
        this.d = l;
        this.a = str2;
        this.b = d.c;
        this.e = null;
        this.j = Boolean.FALSE;
    }

    public e(String str, Long l, String str2, Integer num, d dVar) {
        this.c = str;
        this.d = l;
        this.a = str2;
        this.e = num;
        this.b = dVar;
        this.j = Boolean.FALSE;
    }

    public e(String str, Long l, String str2, Boolean bool) {
        this.c = str;
        this.d = l;
        this.a = str2;
        this.b = d.c;
        this.e = null;
        this.j = bool;
    }

    public e(String str, Long l, String str2, Integer num, d dVar, Boolean bool) {
        this.c = str;
        this.d = l;
        this.a = str2;
        this.e = num;
        this.b = dVar;
        this.j = bool;
    }

    public final String c() {
        int i = f;
        int i2 = (i & 59) + (i | 59);
        g = i2 % 128;
        int i3 = i2 % 2;
        String str = this.c;
        int i4 = i + Opcodes.LNEG;
        g = i4 % 128;
        switch (i4 % 2 == 0 ? Typography.quote : (char) 5) {
            case 5:
                return str;
            default:
                int i5 = 92 / 0;
                return str;
        }
    }

    public final Long a() {
        int i = f;
        int i2 = (i ^ 67) + ((i & 67) << 1);
        g = i2 % 128;
        int i3 = i2 % 2;
        Long l = this.d;
        int i4 = (i ^ 33) + ((i & 33) << 1);
        g = i4 % 128;
        int i5 = i4 % 2;
        return l;
    }

    public final String d() {
        int i = g;
        int i2 = ((i | 97) << 1) - (i ^ 97);
        int i3 = i2 % 128;
        f = i3;
        boolean z = i2 % 2 == 0;
        String str = this.a;
        switch (z) {
            case true:
                break;
            default:
                int i4 = 42 / 0;
                break;
        }
        int i5 = i3 + 77;
        g = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final Integer b() {
        int i = f;
        int i2 = (i & 65) + (i | 65);
        int i3 = i2 % 128;
        g = i3;
        Object obj = null;
        switch (i2 % 2 == 0 ? 'c' : '-') {
            case '-':
                Integer num = this.e;
                int i4 = (i3 ^ 89) + ((i3 & 89) << 1);
                f = i4 % 128;
                switch (i4 % 2 != 0 ? 'G' : (char) 21) {
                    case 'G':
                        obj.hashCode();
                        throw null;
                    default:
                        return num;
                }
            default:
                throw null;
        }
    }

    public final d e() {
        int i = f;
        int i2 = (i ^ 51) + ((i & 51) << 1);
        g = i2 % 128;
        switch (i2 % 2 == 0 ? 'J' : ' ') {
            case 'J':
                int i3 = 72 / 0;
                return this.b;
            default:
                return this.b;
        }
    }

    public final Boolean f() {
        Boolean bool;
        int i = (g + 44) - 1;
        int i2 = i % 128;
        f = i2;
        switch (i % 2 != 0 ? 'Y' : '?') {
            case '?':
                bool = this.j;
                break;
            default:
                bool = this.j;
                int i3 = 89 / 0;
                break;
        }
        int i4 = i2 + 67;
        g = i4 % 128;
        int i5 = i4 % 2;
        return bool;
    }
}
