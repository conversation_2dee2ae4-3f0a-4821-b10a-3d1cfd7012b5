package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP128R1Point.smali */
public class SecP128R1Point extends ECPoint.AbstractFp {
    SecP128R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        super(eCCurve, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint add(ECPoint eCPoint) {
        int[] iArr;
        int[] iArr2;
        int[] iArr3;
        int[] iArr4;
        if (isInfinity()) {
            return eCPoint;
        }
        if (eCPoint.isInfinity()) {
            return this;
        }
        if (this == eCPoint) {
            return twice();
        }
        ECCurve curve = getCurve();
        SecP128R1FieldElement secP128R1FieldElement = (SecP128R1FieldElement) this.b;
        SecP128R1FieldElement secP128R1FieldElement2 = (SecP128R1FieldElement) this.c;
        SecP128R1FieldElement secP128R1FieldElement3 = (SecP128R1FieldElement) eCPoint.getXCoord();
        SecP128R1FieldElement secP128R1FieldElement4 = (SecP128R1FieldElement) eCPoint.getYCoord();
        SecP128R1FieldElement secP128R1FieldElement5 = (SecP128R1FieldElement) this.d[0];
        SecP128R1FieldElement secP128R1FieldElement6 = (SecP128R1FieldElement) eCPoint.getZCoord(0);
        int[] c = s5.c();
        int[] a = s5.a();
        int[] a2 = s5.a();
        int[] a3 = s5.a();
        boolean isOne = secP128R1FieldElement5.isOne();
        if (isOne) {
            iArr = secP128R1FieldElement3.a;
            iArr2 = secP128R1FieldElement4.a;
        } else {
            SecP128R1Field.square(secP128R1FieldElement5.a, a2);
            SecP128R1Field.multiply(a2, secP128R1FieldElement3.a, a);
            SecP128R1Field.multiply(a2, secP128R1FieldElement5.a, a2);
            SecP128R1Field.multiply(a2, secP128R1FieldElement4.a, a2);
            iArr = a;
            iArr2 = a2;
        }
        boolean isOne2 = secP128R1FieldElement6.isOne();
        if (isOne2) {
            iArr3 = secP128R1FieldElement.a;
            iArr4 = secP128R1FieldElement2.a;
        } else {
            SecP128R1Field.square(secP128R1FieldElement6.a, a3);
            SecP128R1Field.multiply(a3, secP128R1FieldElement.a, c);
            SecP128R1Field.multiply(a3, secP128R1FieldElement6.a, a3);
            SecP128R1Field.multiply(a3, secP128R1FieldElement2.a, a3);
            iArr3 = c;
            iArr4 = a3;
        }
        int[] a4 = s5.a();
        SecP128R1Field.subtract(iArr3, iArr, a4);
        SecP128R1Field.subtract(iArr4, iArr2, a);
        if (s5.b(a4)) {
            return s5.b(a) ? twice() : curve.getInfinity();
        }
        SecP128R1Field.square(a4, a2);
        int[] a5 = s5.a();
        SecP128R1Field.multiply(a2, a4, a5);
        SecP128R1Field.multiply(a2, iArr3, a2);
        SecP128R1Field.negate(a5, a5);
        s5.c(iArr4, a5, c);
        SecP128R1Field.reduce32(s5.b(a2, a2, a5), a5);
        SecP128R1FieldElement secP128R1FieldElement7 = new SecP128R1FieldElement(a3);
        SecP128R1Field.square(a, secP128R1FieldElement7.a);
        int[] iArr5 = secP128R1FieldElement7.a;
        SecP128R1Field.subtract(iArr5, a5, iArr5);
        SecP128R1FieldElement secP128R1FieldElement8 = new SecP128R1FieldElement(a5);
        SecP128R1Field.subtract(a2, secP128R1FieldElement7.a, secP128R1FieldElement8.a);
        SecP128R1Field.multiplyAddToExt(secP128R1FieldElement8.a, a, c);
        SecP128R1Field.reduce(c, secP128R1FieldElement8.a);
        SecP128R1FieldElement secP128R1FieldElement9 = new SecP128R1FieldElement(a4);
        if (!isOne) {
            int[] iArr6 = secP128R1FieldElement9.a;
            SecP128R1Field.multiply(iArr6, secP128R1FieldElement5.a, iArr6);
        }
        if (!isOne2) {
            int[] iArr7 = secP128R1FieldElement9.a;
            SecP128R1Field.multiply(iArr7, secP128R1FieldElement6.a, iArr7);
        }
        return new SecP128R1Point(curve, secP128R1FieldElement7, secP128R1FieldElement8, new ECFieldElement[]{secP128R1FieldElement9});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    protected ECPoint b() {
        return new SecP128R1Point(null, getAffineXCoord(), getAffineYCoord());
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint negate() {
        return isInfinity() ? this : new SecP128R1Point(this.a, this.b, this.c.negate(), this.d);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint threeTimes() {
        return (isInfinity() || this.c.isZero()) ? this : twice().add(this);
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twice() {
        if (isInfinity()) {
            return this;
        }
        ECCurve curve = getCurve();
        SecP128R1FieldElement secP128R1FieldElement = (SecP128R1FieldElement) this.c;
        if (secP128R1FieldElement.isZero()) {
            return curve.getInfinity();
        }
        SecP128R1FieldElement secP128R1FieldElement2 = (SecP128R1FieldElement) this.b;
        SecP128R1FieldElement secP128R1FieldElement3 = (SecP128R1FieldElement) this.d[0];
        int[] a = s5.a();
        int[] a2 = s5.a();
        int[] a3 = s5.a();
        SecP128R1Field.square(secP128R1FieldElement.a, a3);
        int[] a4 = s5.a();
        SecP128R1Field.square(a3, a4);
        boolean isOne = secP128R1FieldElement3.isOne();
        int[] iArr = secP128R1FieldElement3.a;
        if (!isOne) {
            SecP128R1Field.square(iArr, a2);
            iArr = a2;
        }
        SecP128R1Field.subtract(secP128R1FieldElement2.a, iArr, a);
        SecP128R1Field.add(secP128R1FieldElement2.a, iArr, a2);
        SecP128R1Field.multiply(a2, a, a2);
        SecP128R1Field.reduce32(s5.b(a2, a2, a2), a2);
        SecP128R1Field.multiply(a3, secP128R1FieldElement2.a, a3);
        SecP128R1Field.reduce32(c6.c(4, a3, 2, 0), a3);
        SecP128R1Field.reduce32(c6.a(4, a4, 3, 0, a), a);
        SecP128R1FieldElement secP128R1FieldElement4 = new SecP128R1FieldElement(a4);
        SecP128R1Field.square(a2, secP128R1FieldElement4.a);
        int[] iArr2 = secP128R1FieldElement4.a;
        SecP128R1Field.subtract(iArr2, a3, iArr2);
        int[] iArr3 = secP128R1FieldElement4.a;
        SecP128R1Field.subtract(iArr3, a3, iArr3);
        SecP128R1FieldElement secP128R1FieldElement5 = new SecP128R1FieldElement(a3);
        SecP128R1Field.subtract(a3, secP128R1FieldElement4.a, secP128R1FieldElement5.a);
        int[] iArr4 = secP128R1FieldElement5.a;
        SecP128R1Field.multiply(iArr4, a2, iArr4);
        int[] iArr5 = secP128R1FieldElement5.a;
        SecP128R1Field.subtract(iArr5, a, iArr5);
        SecP128R1FieldElement secP128R1FieldElement6 = new SecP128R1FieldElement(a2);
        SecP128R1Field.twice(secP128R1FieldElement.a, secP128R1FieldElement6.a);
        if (!isOne) {
            int[] iArr6 = secP128R1FieldElement6.a;
            SecP128R1Field.multiply(iArr6, secP128R1FieldElement3.a, iArr6);
        }
        return new SecP128R1Point(curve, secP128R1FieldElement4, secP128R1FieldElement5, new ECFieldElement[]{secP128R1FieldElement6});
    }

    @Override // bc.org.bouncycastle.math.ec.ECPoint
    public ECPoint twicePlus(ECPoint eCPoint) {
        return this == eCPoint ? threeTimes() : isInfinity() ? eCPoint : eCPoint.isInfinity() ? twice() : this.c.isZero() ? eCPoint : twice().add(eCPoint);
    }

    SecP128R1Point(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
