package o.j;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.g;
import o.e.a;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\j\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    private static int[] f;
    private static final /* synthetic */ b[] h;
    private static int i;
    private static int j;
    private final String e;

    static void c() {
        f = new int[]{2098524597, 1408950536, 1853589322, 80367295, 1477654243, 316407475, -1669304771, -941910568, -857068665, -1903342685, -1929536002, -513550824, -947130810, -353808574, -1316847999, -1592220504, 1727923372, -1207353175};
    }

    static void init$0() {
        $$a = new byte[]{62, 101, 29, 92};
        $$b = 244;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r5, short r6, byte r7, java.lang.Object[] r8) {
        /*
            byte[] r0 = o.j.b.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r7 = r7 * 3
            int r7 = r7 + 4
            int r5 = 116 - r5
            byte[] r1 = new byte[r6]
            r2 = -1
            int r6 = r6 + r2
            if (r0 != 0) goto L19
            r5 = r6
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r7
            goto L36
        L19:
            r4 = r7
            r7 = r5
        L1b:
            r5 = r4
            int r2 = r2 + 1
            byte r3 = (byte) r7
            r1[r2] = r3
            if (r2 != r6) goto L2c
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2c:
            r3 = r0[r5]
            r4 = r8
            r8 = r5
            r5 = r6
            r6 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r4
        L36:
            int r7 = r7 + r6
            int r6 = r8 + 1
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            r4 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.j.b.k(byte, short, byte, java.lang.Object[]):void");
    }

    private static /* synthetic */ b[] e() {
        int i2 = j;
        int i3 = i2 + 15;
        i = i3 % 128;
        int i4 = i3 % 2;
        b[] bVarArr = {b, a, c, d};
        int i5 = i2 + 83;
        i = i5 % 128;
        int i6 = i5 % 2;
        return bVarArr;
    }

    public static b valueOf(String str) {
        int i2 = j + 73;
        i = i2 % 128;
        boolean z = i2 % 2 == 0;
        b bVar = (b) Enum.valueOf(b.class, str);
        switch (z) {
            case false:
                return bVar;
            default:
                throw null;
        }
    }

    public static b[] values() {
        b[] bVarArr;
        int i2 = j + Opcodes.LSUB;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                bVarArr = (b[]) h.clone();
                break;
            default:
                bVarArr = (b[]) h.clone();
                int i3 = 13 / 0;
                break;
        }
        int i4 = j + 75;
        i = i4 % 128;
        int i5 = i4 % 2;
        return bVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        c();
        Object[] objArr = new Object[1];
        g(new int[]{-1237608365, 2104524630}, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 3, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(new int[]{-371416276, 1002227499}, TextUtils.getOffsetAfter("", 0) + 4, objArr2);
        b = new b(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(new int[]{1742876998, 1827211073}, View.MeasureSpec.makeMeasureSpec(0, 0) + 3, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g(new int[]{185451289, 491344487}, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 2, objArr4);
        a = new b(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        g(new int[]{-1205308561, -1971833285, 1918910411, 153146890}, TextUtils.indexOf((CharSequence) "", '0') + 7, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        g(new int[]{1377878229, 407478756, 185451289, 491344487}, 7 - Color.alpha(0), objArr6);
        c = new b(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        g(new int[]{1175765051, -1207095494, 570196688, 714958851, 938236419, -1292570545}, 11 - View.resolveSizeAndState(0, 0, 0), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        g(new int[]{-889968953, 391721826, 334616915, 337508822, 723955556, 973236181, -1779603140, 1852683725}, Color.alpha(0) + 13, objArr8);
        d = new b(intern4, 3, ((String) objArr8[0]).intern());
        h = e();
        int i2 = j + 81;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.amp : (char) 4) {
            case '&':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private b(String str, int i2, String str2) {
        this.e = str2;
    }

    public final String d() {
        int i2 = i;
        int i3 = i2 + 79;
        j = i3 % 128;
        int i4 = i3 % 2;
        String str = this.e;
        int i5 = i2 + 7;
        j = i5 % 128;
        switch (i5 % 2 != 0 ? '\\' : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                return str;
            default:
                int i6 = 36 / 0;
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.j.b d(java.lang.String r8) {
        /*
            o.j.b[] r0 = values()
            int r1 = r0.length
            int r2 = o.j.b.j
            int r2 = r2 + 17
            int r3 = r2 % 128
            o.j.b.i = r3
            int r2 = r2 % 2
            r2 = 0
            r3 = r2
        L12:
            r4 = 1
            if (r3 >= r1) goto L17
            r5 = r4
            goto L18
        L17:
            r5 = r2
        L18:
            r6 = 0
            switch(r5) {
                case 1: goto L1d;
                default: goto L1c;
            }
        L1c:
            goto L4d
        L1d:
            int r5 = o.j.b.i
            int r5 = r5 + 121
            int r7 = r5 % 128
            o.j.b.j = r7
            int r5 = r5 % 2
            r5 = r0[r3]
            java.lang.String r7 = r5.e
            boolean r7 = r8.equals(r7)
            if (r7 == 0) goto L4a
            int r8 = o.j.b.j
            int r8 = r8 + 43
            int r0 = r8 % 128
            o.j.b.i = r0
            int r8 = r8 % 2
            if (r8 != 0) goto L3e
            goto L3f
        L3e:
            r2 = r4
        L3f:
            switch(r2) {
                case 0: goto L43;
                default: goto L42;
            }
        L42:
            goto L49
        L43:
            r6.hashCode()     // Catch: java.lang.Throwable -> L47
            throw r6     // Catch: java.lang.Throwable -> L47
        L47:
            r8 = move-exception
            throw r8
        L49:
            return r5
        L4a:
            int r3 = r3 + 1
            goto L12
        L4d:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.j.b.d(java.lang.String):o.j.b");
    }

    private static void g(int[] iArr, int i2, Object[] objArr) {
        int[] iArr2;
        int i3;
        g gVar = new g();
        char[] cArr = new char[4];
        int i4 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = f;
        char c2 = '0';
        int i5 = -1667374059;
        int i6 = 1;
        int i7 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i8 = 0;
            while (true) {
                switch (i8 < length ? '1' : (char) 14) {
                    case 14:
                        int i9 = $11 + 47;
                        $10 = i9 % 128;
                        int i10 = i9 % i4;
                        iArr3 = iArr4;
                        break;
                    default:
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i7] = Integer.valueOf(iArr3[i8]);
                            Object obj = a.s.get(Integer.valueOf(i5));
                            if (obj == null) {
                                Class cls = (Class) a.c(10 - TextUtils.indexOf("", ""), (char) (8855 - TextUtils.indexOf("", c2, i7)), 324 - (CdmaCellLocation.convertQuartSecToDecDegrees(i7) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i7) == 0.0d ? 0 : -1)));
                                byte b2 = (byte) i7;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                k(b2, b3, b3, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                a.s.put(-1667374059, obj);
                            }
                            iArr4[i8] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                            i8++;
                            i4 = 2;
                            c2 = '0';
                            i5 = -1667374059;
                            i7 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = f;
        long j2 = 0;
        switch (iArr6 != null) {
            case false:
                break;
            default:
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i11 = 0;
                while (i11 < length3) {
                    try {
                        Object[] objArr4 = new Object[i6];
                        objArr4[0] = Integer.valueOf(iArr6[i11]);
                        Object obj2 = a.s.get(-1667374059);
                        if (obj2 != null) {
                            iArr2 = iArr6;
                            i3 = length3;
                        } else {
                            Class cls2 = (Class) a.c(9 - ExpandableListView.getPackedPositionChild(j2), (char) (TextUtils.lastIndexOf("", '0', 0) + 8857), Color.green(0) + 324);
                            byte b4 = (byte) 0;
                            byte b5 = b4;
                            iArr2 = iArr6;
                            i3 = length3;
                            Object[] objArr5 = new Object[1];
                            k(b4, b5, b5, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                            a.s.put(-1667374059, obj2);
                        }
                        iArr7[i11] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                        i11++;
                        iArr6 = iArr2;
                        length3 = i3;
                        j2 = 0;
                        i6 = 1;
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                iArr6 = iArr7;
                break;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            int i12 = $11 + 99;
            $10 = i12 % 128;
            int i13 = i12 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr5);
            for (int i14 = 0; i14 < 16; i14++) {
                gVar.e ^= iArr5[i14];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                    Object obj3 = a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) a.c(View.getDefaultSize(0, 0) + 11, (char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), View.MeasureSpec.getMode(0) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i15 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i15;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i16 = gVar.e;
            int i17 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) a.c(12 - ((Process.getThreadPriority(0) + 20) >> 6), (char) (55184 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 563 - AndroidCharacter.getMirror('0'));
                    byte b6 = (byte) 1;
                    byte b7 = (byte) (b6 - 1);
                    Object[] objArr8 = new Object[1];
                    k(b6, b7, b7, objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i2);
    }
}
