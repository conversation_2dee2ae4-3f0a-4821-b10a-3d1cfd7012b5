package bc.org.bouncycastle.math.ec.custom.sec;

import androidx.core.app.FrameMetricsAggregator;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.a6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP521R1Field.smali */
public class SecP521R1Field {
    static final int[] a = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, FrameMetricsAggregator.EVERY_DURATION};

    protected static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        a6.a(iArr, iArr2, iArr3);
        int i = iArr[16];
        int i2 = iArr2[16];
        iArr3[32] = c6.a(16, i, iArr2, i2, iArr, iArr3, 16) + (i * i2);
    }

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        int a2 = c6.a(16, iArr, iArr2, iArr3) + iArr[16] + iArr2[16];
        if (a2 > 511 || (a2 == 511 && c6.c(16, iArr3, a))) {
            a2 = (a2 + c6.c(16, iArr3)) & FrameMetricsAggregator.EVERY_DURATION;
        }
        iArr3[16] = a2;
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        int e = c6.e(16, iArr, iArr2) + iArr[16];
        if (e > 511 || (e == 511 && c6.c(16, iArr2, a))) {
            e = (e + c6.c(16, iArr2)) & FrameMetricsAggregator.EVERY_DURATION;
        }
        iArr2[16] = e;
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = c6.a(521, bigInteger);
        if (c6.c(17, a2, a)) {
            c6.g(17, a2);
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        int i = iArr[16];
        iArr2[16] = (c6.a(16, iArr, i, iArr2) >>> 23) | (i >>> 1);
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 17; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] a2 = c6.a(33);
        a(iArr, iArr2, a2);
        reduce(a2, iArr3);
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            c6.d(17, a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            c6.d(17, iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[68];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 17);
            iArr[16] = iArr[16] & FrameMetricsAggregator.EVERY_DURATION;
        } while (c6.f(17, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        int i = iArr[32];
        int a2 = (c6.a(16, iArr, 16, 9, i, iArr2, 0) >>> 23) + (i >>> 9) + c6.a(16, iArr, iArr2);
        if (a2 > 511 || (a2 == 511 && c6.c(16, iArr2, a))) {
            a2 = (a2 + c6.c(16, iArr2)) & FrameMetricsAggregator.EVERY_DURATION;
        }
        iArr2[16] = a2;
    }

    public static void reduce23(int[] iArr) {
        int i = iArr[16];
        int b = c6.b(16, i >>> 9, iArr) + (i & FrameMetricsAggregator.EVERY_DURATION);
        if (b > 511 || (b == 511 && c6.c(16, iArr, a))) {
            b = (b + c6.c(16, iArr)) & FrameMetricsAggregator.EVERY_DURATION;
        }
        iArr[16] = b;
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] a2 = c6.a(33);
        a(iArr, a2);
        reduce(a2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] a2 = c6.a(33);
        a(iArr, a2);
        reduce(a2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            a(iArr2, a2);
            reduce(a2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        int d = (c6.d(16, iArr, iArr2, iArr3) + iArr[16]) - iArr2[16];
        if (d < 0) {
            d = (d + c6.a(16, iArr3)) & FrameMetricsAggregator.EVERY_DURATION;
        }
        iArr3[16] = d;
    }

    public static void twice(int[] iArr, int[] iArr2) {
        int i = iArr[16];
        iArr2[16] = (c6.b(16, iArr, i << 23, iArr2) | (i << 1)) & FrameMetricsAggregator.EVERY_DURATION;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4) {
        a(iArr, iArr2, iArr4);
        reduce(iArr4, iArr3);
    }

    public static void square(int[] iArr, int[] iArr2, int[] iArr3) {
        a(iArr, iArr3);
        reduce(iArr3, iArr2);
    }

    protected static void a(int[] iArr, int[] iArr2) {
        a6.a(iArr, iArr2);
        int i = iArr[16];
        iArr2[32] = c6.b(16, i << 1, iArr, 0, iArr2, 16) + (i * i);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2, int[] iArr3) {
        a(iArr, iArr3);
        reduce(iArr3, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            a(iArr2, iArr3);
            reduce(iArr3, iArr2);
        }
    }
}
