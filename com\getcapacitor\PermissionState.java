package com.getcapacitor;

import java.util.Locale;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\PermissionState.smali */
public enum PermissionState {
    GRANTED("granted"),
    DENIED("denied"),
    PROMPT("prompt"),
    PROMPT_WITH_RATIONALE("prompt-with-rationale");

    private String state;

    PermissionState(String state) {
        this.state = state;
    }

    @Override // java.lang.Enum
    public String toString() {
        return this.state;
    }

    public static PermissionState byState(String state) {
        return valueOf(state.toUpperCase(Locale.ROOT).replace('-', '_'));
    }
}
