package org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.ExtendedDigest;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;
import org.bouncycastle.util.Arrays;
import org.bouncycastle.util.Memoable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\crypto\digests\GOST3411_2012Digest.smali */
public abstract class GOST3411_2012Digest implements ExtendedDigest, Memoable {
    private final byte[] IV;
    private final byte[] h;
    private static final byte[][] C = {new byte[]{-79, 8, 91, -38, 30, -54, -38, -23, -21, -53, 47, -127, -64, 101, 124, 31, 47, 106, 118, 67, 46, 69, -48, 22, 113, 78, -72, -115, 117, -123, -60, -4, 75, 124, -32, -111, -110, 103, 105, 1, -94, 66, 42, 8, -92, 96, -45, 21, 5, 118, 116, 54, -52, 116, 77, 35, -35, ByteCompanionObject.MIN_VALUE, 101, 89, -14, -90, 69, 7}, new byte[]{111, -93, -75, -118, -87, -99, 47, 26, 79, -29, -99, 70, 15, 112, -75, -41, -13, -2, -22, 114, 10, 35, 43, -104, 97, -43, 94, 15, 22, -75, 1, 49, -102, -75, 23, 107, 18, -42, -103, 88, 92, -75, 97, -62, -37, 10, -89, -54, 85, -35, -94, 27, -41, -53, -51, 86, -26, 121, 4, 112, 33, -79, -101, -73}, new byte[]{-11, 116, -36, -84, 43, -50, 47, -57, 10, 57, -4, 40, 106, Base64.padSymbol, -124, 53, 6, -15, 94, 95, 82, -100, 31, -117, -14, -22, 117, 20, -79, 41, 123, 123, -45, -30, 15, -28, -112, 53, -98, -79, -63, -55, 58, 55, 96, 98, -37, 9, -62, -74, -12, 67, -122, 122, -37, 49, -103, 30, -106, -11, 10, -70, 10, UtilitiesSDKConstants.SRP_LABEL_MAC}, new byte[]{-17, 31, -33, -77, -24, 21, 102, -46, -7, 72, -31, -96, 93, 113, -28, -35, 72, -114, -123, 126, 51, 92, 60, 125, -99, 114, 28, -83, 104, 94, 53, 63, -87, -41, 44, -126, -19, 3, -42, 117, -40, -73, 19, 51, -109, 82, 3, -66, 52, 83, -22, -95, -109, -24, 55, -15, 34, 12, -66, PSSSigner.TRAILER_IMPLICIT, -124, -29, -47, 46}, new byte[]{75, -22, 107, -84, -83, 71, 71, -103, -102, 63, 65, 12, 108, -87, 35, 99, ByteCompanionObject.MAX_VALUE, 21, 28, 31, 22, -122, Tnaf.POW_2_WIDTH, 74, 53, -98, 53, -41, ByteCompanionObject.MIN_VALUE, 15, -1, -67, -65, -51, 23, 71, 37, 58, -11, -93, -33, -1, 0, -73, 35, 39, 26, 22, 122, 86, -94, 126, -87, -22, 99, -11, 96, 23, 88, -3, 124, 108, -2, 87}, new byte[]{-82, 79, -82, -82, 29, 58, -45, -39, 111, -92, -61, 59, 122, 48, 57, -64, 45, 102, -60, -7, 81, 66, -92, 108, 24, ByteCompanionObject.MAX_VALUE, -102, -76, -102, -16, -114, -58, -49, -6, -90, -73, 28, -102, -73, -76, 10, -14, 31, 102, -62, -66, -58, -74, -65, 113, -59, 114, 54, -112, 79, 53, -6, 104, 64, 122, 70, 100, 125, 110}, new byte[]{-12, -57, 14, 22, -18, -86, -59, -20, 81, -84, -122, -2, -65, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 9, 84, 57, -98, -58, -57, -26, -65, -121, -55, -45, 71, 62, 51, 25, 122, -109, -55, 9, -110, -85, -59, 45, -126, 44, 55, 6, 71, 105, -125, 40, 74, 5, 4, 53, 23, 69, 76, -94, 60, 74, -13, -120, -122, 86, 77, 58, 20, -44, -109}, new byte[]{-101, 31, 91, 66, 77, -109, -55, -89, 3, -25, -86, 2, 12, 110, 65, 65, 78, -73, -8, 113, -100, 54, -34, 30, -119, -76, 68, 59, 77, -37, -60, -102, -12, -119, 43, -53, -110, -101, 6, -112, 105, -47, -115, 43, -47, -91, -60, 47, 54, -84, -62, 53, 89, 81, -88, -39, -92, ByteCompanionObject.MAX_VALUE, 13, -44, -65, 2, -25, 30}, new byte[]{55, -113, 90, 84, 22, 49, 34, -101, -108, 76, -102, -40, -20, 22, 95, -34, 58, 125, 58, 27, 37, -119, 66, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 60, -39, 85, -73, -32, 13, 9, -124, ByteCompanionObject.MIN_VALUE, 10, 68, 11, -37, UtilitiesSDKConstants.SRP_LABEL_MAC, -50, -79, 123, 43, -118, -102, -90, 7, -100, 84, 14, 56, -36, -110, -53, 31, 42, 96, 114, 97, 68, 81, -125, 35, 90, -37}, new byte[]{-85, -66, -34, -90, ByteCompanionObject.MIN_VALUE, 5, 111, 82, 56, 42, -27, 72, UtilitiesSDKConstants.SRP_LABEL_MAC, -28, -13, -13, -119, 65, -25, 28, -1, -118, 120, -37, 31, -1, -31, -118, 27, 51, 97, 3, -97, -25, 103, 2, -81, 105, 51, 75, 122, 30, 108, 48, 59, 118, 82, -12, 54, -104, -6, -47, 21, 59, -74, -61, 116, -76, -57, -5, -104, 69, -100, -19}, new byte[]{123, -51, -98, -48, -17, -56, -119, -5, 48, 2, -58, -51, 99, 90, -2, -108, -40, -6, 107, -69, -21, -85, 7, 97, 32, 1, ByteCompanionObject.MIN_VALUE, 33, 20, -124, 102, 121, -118, 29, 113, -17, -22, 72, -71, -54, -17, -70, -51, 29, 125, 71, 110, -104, -34, -94, 89, 74, -64, 111, -40, 93, 107, -54, -92, -51, -127, -13, 45, 27}, new byte[]{55, -114, -25, 103, -15, 22, 49, -70, -46, 19, ByteCompanionObject.MIN_VALUE, UtilitiesSDKConstants.SRP_LABEL_ENC, 4, 73, -79, 122, -51, -92, 60, 50, PSSSigner.TRAILER_IMPLICIT, -33, 29, 119, -8, 32, 18, -44, 48, 33, -97, -101, 93, ByteCompanionObject.MIN_VALUE, -17, -99, 24, -111, -52, -122, -25, 29, -92, -86, -120, -31, 40, 82, -6, -12, 23, -43, -39, UtilitiesSDKConstants.SRP_LABEL_MAC, 27, -103, 72, PSSSigner.TRAILER_IMPLICIT, -110, 74, -15, 27, -41, 32}};
    private static final byte[] Zero = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};
    private static final long[][] T = {new long[]{-1803552715625652272L, 2703135593145367062L, -4014430758819158872L, 6577092334268629354L, 806964168861892974L, -6063472769050256282L, -713936367173212554L, -730696462206134965L, 5215999108449717233L, -46053480858810939L, -3782322580877781099L, 7409386412115095689L, 3849627103271945136L, 8988319201874450849L, 3938119337751376013L, -7010230157848027178L, -8423426398366749129L, -7147294794814685941L, -4896405975937806502L, -1930949658557223699L, -3548053845353340974L, -1407348811753517218L, -5094482946371975395L, -8349570678062906195L, 8519831221648263471L, 6380786457702773335L, 4606327678483665726L, 1135139788101916873L, -1296744055254569597L, 1220450159802546598L, 6759235690777098768L, 5340123591806085420L, 6034809048673841977L, -5407401691594998519L, 6896344311240562893L, -1690488634981198087L, 9184934662348565148L, -4183792988264456516L, -6457042926775703950L, 3364644269219363704L, 989048157634869780L, -9056160079784030844L, 3387584785362250392L, 6665082552186727408L, 8806730920978768603L, -8943992543361557404L, -3499218176542634446L, -8832779848047991010L, -5384180315986760471L, -5116578973163071747L, 2007762480278943944L, 7071029175581714734L, -245446995445778696L, -7098459127742649109L, 5517131305049330262L, 2524355749569298796L, 3276936053954857029L, -833575805165058901L, -4774104785891107142L, 4227838725751020409L, -1108816769305830132L, 2880731531503622347L, -8547577275558851350L, 1904883134495025448L, -8015005427386181889L, 4850405589464713887L, -602049820529576042L, 2081972218582700626L, 1379356218675464859L, -6509842815274707633L, 3166351970529817407L, -2925410023548009017L, -6354163580723349614L, -2675396450584002605L, -8745932894798146525L, 4095269026725968292L, 7284836791436182452L, 178206167350026973L, -8587687698711814902L, -7849942517349325374L, 5686309239594266763L, 3626867272058218794L, 4695292606739097666L, -5584978786571663757L, 7805587275216445447L, 6553870956925915274L, 2247080073069027695L, -4360880972299377210L, 4136504802722867268L, 2992705483290644962L, 4655464135170259362L, -1641371007467190503L, 867873424238963700L, 6139766898342725699L, 5048711494641582808L, 2360957084007330385L, 7917754814463914471L, -6851982712723858168L, 8900603062938514235L, 4819584321579539327L, -2835569523292644082L, -4162535888497939071L, 2409792751347803057L, 7449496838093054313L, 2608138101170875382L, -3089532541034338534L, 6775169570724432173L, 2898833334747545602L, -8076149933667584112L, 1717647244194337596L, -2273374492424215382L, 8669102231478190086L, 7938185699155198682L, -8713704321949088317L, 3969222566346957165L, -5988784469396818180L, 9134972545477524348L, -4561983676892834053L, -443809933158089025L, 8321595681021526376L, 3740161260836255946L, -6223083854011302213L, -1981192628965753587L, -8189443922177186192L, 5892660270079857124L, 2502541675832561804L, -2804466300730962706L, 3575476887658224151L, -3200685872914850720L, 669897467106812851L, -7208553650918336623L, -3694119084499141361L, 3809516679850545744L, 5718256960103440747L, -2332953186743126124L, 2758712437335984427L, -4627477833875620761L, -2457079737070004919L, 4334551115747581955L, -7965043308780665057L, 7539375937215052192L, -5292836362296451116L, 1449546416188301313L, -7476662035618257748L, 7253733569142936148L, -4410843088901983194L, 4494160142329627358L, 5797380680492875780L, 5033684639528710629L, 6942380200648117235L, -311213848885666718L, -6820034994775438104L, 5452010138718157004L, 646676091767009875L, -4539043153625528549L, -6988416084379726282L, 471514214048988026L, -1312069221898101058L, 8614828720478738639L, 3471190102295415799L, -8245283892968532659L, -547814629229383841L, -1516815754241838044L, -6167477462292361850L, -7381664543912200884L, 0, -1067861853971886868L, 8143953700710785973L, -5825391166404688447L, -7494732077575095195L, 1847633384726895125L, -7672935903076573512L, -2570373723287288663L, -7766808051888107688L, -9211246677606207655L, -4304686731880476836L, -424225740051214974L, -944158789619597359L, 7698943044753169277L, 5558086220651709366L, 273485755470967613L, -2437239977867205004L, 3045458228384031455L, 8041346034719003450L, -3121761119376230662L, -4956810279513479744L, -7615625819989316219L, -1494719728652540476L, 378507382706538567L, -6621729356152879953L, -2037663147516029033L, -9108086336008503623L, -5865219631939627999L, -912211069378859471L, 7175315966437357774L, 5161723385538695992L, -6691429997208146379L, 8789995975362646502L, 122301190315135456L, 1339527752872090491L, 8500289773969701394L, -2635286024337609165L, 2206125151973814415L, 5912507865751560921L, -2158837887763724681L, -76874749012421595L, -3877320079976692107L, 7055674186596964883L, -1780331338551374288L, 3226973935617776037L, 1613642550683796188L, -6336095750669684901L, 2113919936262685618L, -5242593384495067596L, -3431887019008757923L, -205618523608504552L, 6290905146667117214L, 520631834711206554L, 6090649271097153955L, 6268809121346255742L, -4722757421728125561L, 9011259725410191425L, 4260067298331642521L, 7571604515825379392L, -5957963198686648548L, -5641402047464721074L, 4929679943725850629L, 1562558313115120097L, 8378270681804975090L, 6431029428379739063L, -1174442872063853469L, 7740178818457750173L, 4437711457076851171L, -6732384918571794475L, 1735746844117158901L, 1251271430781151302L, -5005927899907263456L, -3319719480029724995L, -5762576781129063250L, -8874015624313325826L, 8122139634094273109L, 1040986648309745961L, 356411355647007143L, 756721191328944270L, 5318878451677075985L, -8454529626693894185L, -3672305017614193425L, -3892411666385996472L, -7872883033760648158L, -4068663746806629279L, -2179221354102278838L, -3303846208210219648L, -7330572744817539471L, -5490825646511057005L, 8272760012479254664L, -2966645796984155097L}, new long[]{-4030255449738619426L, 7346976492199675417L, -626116132630139325L, -8818858909562055882L, 6533467991705271684L, -448178970784563971L, -6764453224091262193L, -3624289984018954899L, -9205540189538630779L, -5585462176368193220L, -7429905405277353951L, -4656326962959550440L, 1151735774811599048L, -6151769618714846568L, 6942284815962019498L, -703557647758734283L, 3304311495801540674L, -4831136898697919614L, 7559468737934159677L, -6359765666358135876L, 2957424603572281991L, -5197658860964970097L, -2809702287761406565L, -2956007109283531437L, -3067203235521704247L, -1571434816639601324L, 8085181091649676441L, -3591952015147155721L, -209953567916339656L, 7642183762405766865L, 6618156441656008690L, -3821456694203446222L, -5721068125790025520L, 6768646983239759067L, 1229560361235927109L, 2545497942825370364L, -4247812575351915270L, 8332055718082846467L, 9018811952317763005L, 4566156016530439069L, -8867871107904474816L, 4447555131281990257L, 1889868047608435272L, 9132310989383824935L, 8423405394594584156L, 1791576812070341540L, 895921998879640402L, -4136132749925103776L, 8686043850126826766L, -259527717632742322L, 3505479608743889749L, 3164974344323824491L, -2463388277714864290L, -7381315281960506793L, 1491256427646959862L, -4922222727044511011L, 5832729293860314623L, -1668591102639493960L, 5588041959860897512L, -4477392520457318849L, 8960651507453538251L, 3404044757246527918L, 7427396029290209269L, 1567782000193888896L, 2125332809319160599L, -2696297740110004735L, -8684132036093703462L, -3781732998293770684L, 405965478745608371L, 1334608620921465823L, -8162931759184195269L, 6009835698019100261L, 6362838478626120808L, -2915579863370524891L, -6253885823018934014L, -8424766350955232888L, -3400959220014064006L, 5388605995385671607L, 4789436484882904608L, 8273825248612433269L, 6807803589745610561L, -803141104343149095L, -8044273462534131274L, -8959238411213129697L, -4562432827018040247L, -6847432110282425629L, -6617847203449567194L, -5916573000348838969L, -5876878730644248483L, -4287606846705721716L, -5388300036355532701L, 447961557886364457L, -6806372268832745323L, 7989344413684106260L, 7745519667033183399L, -3162377592575071041L, -1888435642254047844L, -6670898553424985735L, 5873227033075236745L, -4286789270562860L, -7115147458626955118L, 2021362311995275617L, -1046827325327899286L, -7348342965477568051L, 4652678544566382540L, 1850721214614677970L, 3779734978877634960L, -2226104194499971239L, -5091849455076895951L, -7985129142130994240L, 1609153642657618202L, 4480481336861891051L, -8473849124741782530L, -8335690430216009513L, 4986581067319345023L, 209097870346312172L, -2276381894218689233L, -4447261871345863259L, 7798271984477724152L, 7243570012128126063L, -3333018702976353268L, -5544050972775074998L, -9018030957147481495L, -7191672837363402012L, -409121388910540953L, -7001735332191755512L, -5177453641450868409L, -7244442712347462725L, -4041310443283782378L, 1180970181547717171L, 5176105366933055123L, 7188535108749356336L, 3071419591516346653L, 8871593212975408788L, 7377047836268559747L, 745784333737205883L, 523712867556149599L, 3218003841565405748L, 2276692251301888763L, 4042724623974101698L, 6062869490421444410L, 1668368207247227756L, -107200675243772510L, 2916365256580759793L, -744980879059175505L, -8610870969725437422L, 6147487227497352524L, -2362538166529167694L, 347243151226876613L, 6999741710823961820L, 2806071973676585551L, -8788735839226091348L, -1183493220983118361L, 3821216675772636134L, -1789001426630173584L, -3217716098546522656L, -2430408108131136316L, -7873943064266053542L, -1149811373353720036L, -4750585183628141970L, -892221251657868154L, 5286400504730760237L, 7609746906341231947L, -2548006234371058392L, 944934528471361828L, 0, 6430778939292435998L, 3336736409998420952L, 7875361642996334478L, -1846528539907703290L, 8821948844834997474L, -6010057508961455695L, 706136312374746081L, -8506197141978474396L, 8606606803205598662L, 6252543065422558934L, 258180561991695258L, 3932361041049477244L, 2228031909561065613L, 3591083712976693539L, 8046871333151188578L, 4829160237163942998L, -6065391445415380754L, -5835889602077783509L, 4380598834454315015L, -7797487710135793108L, -4378688104862656557L, 5548248045526197406L, -5626880067174045018L, 4026619618737144330L, 8789027980293848952L, 5914590821894013971L, 5201360692628199003L, 1438508405363181993L, 1047132165489660606L, 5627682402975382898L, -3692934325936855269L, 1966322907280535614L, 2694302999865518549L, 2361664381868374374L, -4989108504807806805L, -941866114256437520L, -9129718635683421709L, -7638464970933853947L, 6673472819996841645L, -7559179876038510359L, -4789654982222634508L, 4917937056650714377L, -1335952497386210293L, 8472979738121466922L, -5455256526993655275L, -7607840574798439777L, -1233841633585051759L, 3621785006072287929L, -6321264663560567948L, -525690647958430069L, 2600818822502173603L, -350890450751740655L, 5457179844010077633L, 104674322205155958L, 2459119713154539658L, -8087180229934124211L, -1965524969523353622L, 8162692825194907375L, 2847061296053530681L, -1439358620438684035L, 2429042753721621264L, -2601053358443111305L, -6942520436352876162L, -3506841683981296511L, -2850200109117495315L, 4291817720210491736L, 4250406047929019182L, -1611134702235742514L, 6848231166907919671L, 5722504929196957444L, -5283333209384213511L, -8276968459716351327L, -3935503167711873112L, -2018290618604048715L, 804576823300162061L, -1494415651414517982L, -7742435248677485709L, 9209752147493311569L, -6531539192194323888L, 630314289831180695L, 5090994841956713701L, -3302404079808903274L, 6320413364043601056L, 4136912626218684596L, 3688671278293774543L, -2121626579602862909L, 7111516025665441606L, -6427073794025991734L, 4747430357904739770L, 8503693248481660848L}, new long[]{5022191610516858060L, -5800904701749453560L, 325196729119310435L, -7902632335432024366L, -1275020129832272985L, 1619663718359036237L, -355156193309366908L, 5335828035754758151L, -7067565944566412775L, -3798526433470663894L, -5359911709110765600L, -2532208113978446666L, 1287844858498292800L, -2071692252756128045L, -6862984376374597181L, 550964732710513933L, 8585995564513096669L, -2760271765158838268L, -5121668511014600044L, -816004031260028593L, 8916962000159598712L, -4254160368123066399L, 8560921352511315212L, 7426955371667046732L, 7096434795747339774L, 2543027200294554449L, -8026787735162470547L, 5721517685081291957L, 1937338776563641064L, -4710672869224480440L, -1339811264550726455L, 4899378514713126672L, -3673650654185164554L, -1748370356318745424L, 3710095511616993728L, 7340570904524980467L, 5918098101825092432L, -7642223448383253537L, -8716653984131915535L, 4697601972911171247L, -7966292763585932868L, 5151346661287437683L, 3331340938598661669L, -9051540132384224189L, -7204664438506969912L, 2915854855682031097L, 1492126639482712306L, 6290138385502410722L, -8595407305453562822L, 1856873279258002822L, 5831708560246045423L, 9075623880903535524L, 8815370023405405865L, -5279349609657657202L, 4378109956818586891L, -3439845308874787053L, -1139502944774581460L, 2668061343609997453L, -2889519392695838178L, 6825682518352077045L, -8922186867811676257L, 8172884461239939073L, -6254526210459476268L, 4603827798664618597L, 2867088101655710002L, 8332998285759836606L, -7775381576435942130L, 4672530790940461182L, 1134559637999613131L, 3897488771451098482L, -8137000561907140297L, -772950844530606178L, -1728738397008545669L, 7221495326637043234L, -1849396657442790815L, -9174585730886234628L, 7909827430729881909L, -2444081354433843959L, -4163290364782213538L, -7577480709912625999L, -2101268725598167038L, 137665338910158545L, -5931204152994046793L, -7457759296736876885L, 6746265954835287963L, -189638032760625576L, -1024927361995588099L, -1527455564586976828L, -1177935295216069258L, 5838760703968564286L, -4415126550633053123L, 780392312595241081L, -110204031714277066L, -5604537100301722899L, -4790021541885448666L, 7015947325939974800L, 3645098545556231953L, -561819241209197846L, -934053641427354558L, 2066784160966486324L, -5034171952356883669L, 2661572689932379740L, 1033530071105264154L, -5489401038314458052L, -4578618083660511870L, 3121291101747188375L, 6662680657495023946L, 4471231211228269748L, -3022117047982312241L, -5866468419329903655L, 4219404094166535895L, 4762278602948970945L, -8967953485935648110L, -8547850243595879701L, -3473479388290731703L, 453876631214605276L, 4407685011702703066L, 6616899736578362439L, -5443495875284215503L, -6027728514849410458L, 7990341289637279323L, -4004030578970043324L, -7230942222439593531L, -5114619702887027643L, 1373724075619900927L, -4663118841695326311L, -3086804419763741791L, -5694935987874440366L, 6083572248920684684L, 3484333688484746926L, -3924950286951517547L, -6508957907156220559L, 9139255992929895626L, -8224492325301274488L, -6190959931998490182L, 2189202321390233739L, 716843780901418775L, 8359149623152811187L, 2415494092048157422L, 5243176795453837752L, -236631585242642295L, 4154687863979303353L, 2272222198031926874L, 8690072078618429206L, 202427615156584895L, -8807048875170750130L, -31120709530589209L, 8942462418363155829L, 907753262429150117L, -6636063805428332883L, 7665181140788048952L, -7548467744294317472L, 3239326427400699802L, 6868174095610514980L, 3808219771792118989L, 7543278091629671815L, 9168270020997671451L, -6053858953487310997L, 968811775037617524L, -2306417983241649192L, 584245825585813958L, -2630487407999745605L, -4915095507370037766L, 8750568181363911111L, 1698743779072106396L, 0, -1502945925515520235L, 8236472591980224367L, -5236861298165303713L, 4035151075898375075L, -8144050755230148634L, -1591076393697569110L, 1948891778265760825L, -8727645643825394144L, -7392198592007632774L, -361643978915034283L, 390196158411648178L, 6501762612143760022L, 2496032298569901440L, 6037175610368660865L, -5683947345327655549L, -4242608233385727696L, -2298557723295997507L, -1404809561562629608L, 4925668384127994397L, -6279600722624450555L, 6410254435445760809L, -3116382805945223824L, -3593119323321426024L, 6421246412478107128L, -8389988771804185772L, -692478471504998160L, -2857359617058050347L, 3832728561082858012L, 1367237356757087022L, -3275211779544041812L, 5272189459801538409L, 3250881896629883211L, 8001331881476413578L, 6968391399550921793L, -3214151934877001603L, 809969847888836264L, -7004002273151654537L, -6769223851252275076L, -1974066071074724898L, -6610549085535179872L, -2695487635533189270L, -3680135972265730521L, 3078237133952334918L, -6978929124704531546L, -4495596308024530093L, -6384798519481826098L, 3445914570843754740L, 6242581091097786675L, -1913008689637290737L, 1539119056502398499L, 7794692769390540772L, 1168206872894059153L, -2507695994495548825L, 226938317788999534L, 7419906245093191581L, -8343571233385605543L, 3581420404919564415L, -7801043624446046205L, 7585766634561542998L, 2998877762491110184L, 7174951347083690799L, 8107322624236560080L, -4868539441222958857L, 5494625981129708507L, 8496251452686250594L, 5596216022932782346L, 5661024595188978276L, 2747447103066187747L, -2165963102470172820L, -3338817622691620414L, -4048717794990741361L, -8468508584617454203L, 5085786205181774754L, 4053661314365738856L, 2109836211124176869L, 2336412667751326783L, -6833968380723247342L, -9132096051380542675L, -945607710981729645L, 1742301027778505559L, -3845518068317741573L, 7748764524654516969L, -442177925414869957L, 5468987013722291926L, 6162091280534394461L, 4280460676255686662L, -6445294871958312417L, -609455332283285983L, -4372075848610567444L, -7327464649345800428L}, new long[]{412778415529865760L, 3577342882794069583L, -5641231359968545580L, -8360354891303721342L, -8815395659090061125L, -7417517676636177975L, -6818951615660906963L, 8540370589362648094L, -520997537338351428L, -4727129576760171852L, 7380585705851070183L, 5532990722829248072L, -7691855626550785470L, 2363770411641709807L, 4799219136276863089L, 886827555711267723L, -3685573774419866413L, -7576141180696842327L, -4392773903311365453L, -3287291658763706349L, 152494743391849906L, -3649364066632473462L, -6378955412281134618L, 3618765265895209892L, -1716595120412069307L, 6638666397703976113L, 6746862217775881448L, -6012164977874911745L, 6325409020385589416L, -6565038336846621683L, 4058193356686090212L, 7086287341885701278L, -2771917532509164534L, -7963598493027905477L, -6718493157917047866L, -1989525455461826098L, -642177714062068004L, -8668117067627047671L, -5832910844866177004L, -5472952995583047948L, 3891946276334412253L, -3179016605330421686L, -484727283259111195L, 8437586805018431989L, -7825470045915439735L, -7014338722660612517L, 5716202677831336867L, 3243582143937888061L, -2579911156235362774L, -1571570467631317001L, 2399609570378190006L, 7345426154655236876L, 7619693495944769671L, -2623928864797007432L, -3315748747819219464L, -9075084495080790231L, 7194324827586368711L, 4309013456660281871L, -8196421247265975134L, 4166240605864818621L, 1366177816882650955L, -6109201961362406803L, 6217414342487101681L, 7783606731388437159L, -1326567251088752539L, 9037712741102696455L, -1434402495440951236L, -2328508569491276863L, 8304362858347251262L, 8707177795482651175L, -3423592723977556575L, -5061374920767775450L, -2142416718843079163L, -8088294678366913285L, -371319742740009714L, -6682230743172060257L, -7272631905926302597L, -7308630489489105886L, 1945973140213898976L, 4200834197375652438L, 5435457027409418714L, 7756135292223372597L, -1729698296506932187L, 2508515451320396125L, -8923459624282869534L, -4128744637691189101L, 609168629503044082L, 3755788431048666223L, -1294151922135947890L, 5399380291345002883L, -6145392908131276236L, -6257184306882179105L, 8884253657276313548L, 7648167075477099884L, 1809525021741863762L, 6185017703168619802L, -3936060424307441933L, 2064551487375270258L, -2436564695348784232L, 3469367101608228374L, 1536322983539794137L, 5796100959046534970L, 1773386847580617483L, 108346540153546841L, 9001722862285623390L, 2544785687349641476L, -1185903330127508009L, 551619354788593554L, -1881618742578288233L, 6486896028513362810L, -5327214006029481146L, 717443701121057195L, 2214371934970162368L, -5219386590255766753L, 1140410460104840800L, 7488913346602372798L, -5017208032223676211L, -2034369453084459428L, 994812197283306450L, -8332306493105563376L, 2804228341285155620L, 260410206251574763L, -6978049983912956414L, -6292961768551667834L, 4835367340339408936L, -3510802620212598472L, 6348897592219414216L, -4866111242788671738L, -7999675210372286366L, 2250650893928301721L, 7511575676904462558L, -7453665862510849648L, -2879832976648862637L, 443413779826989003L, -1463594670408230994L, -1838018357033071492L, 1500526641193339008L, 8152292870049500044L, -8468208622468673829L, -6854800545328525708L, -5976114712830143066L, 858245894210178073L, -8224311796882886327L, -4619232891496756499L, -3000621152483837334L, 8743440194190385790L, 1218336215312585465L, 1032276353674575417L, -3546598980344807071L, -44273616367427794L, -7539923910483671056L, 5903954706119241571L, 3395223664118723215L, 5940852870679710344L, -5868829237233520563L, 4685830293981560218L, -5500138837814560410L, -9217998094083183973L, -6528916603821540268L, -1066837721018687209L, 9183300283681142197L, 4649831692234020291L, 9147178532201324012L, -3150721088639387688L, -960114386881264388L, 0, 6898510060154343770L, 1643589765241144626L, -5749199594764804979L, 1402114711967872786L, -4909020925620612972L, -8509612328854475984L, 4353588900056941981L, -4274472907005860575L, 5292673456375059560L, -3819786587920884968L, -2732275386223179295L, -8617879545117768855L, 3999781539141355908L, -7154578025966711831L, -2892636494603496909L, -6414945272642414145L, -7118720227070778448L, 6049120102845506257L, 7933416458737094421L, 4461908978498334148L, 6790446112744143107L, 3726873057198478333L, 7237529453753559893L, 3864037041512891446L, 7046626506133690741L, 304722305440127609L, -180302004885450084L, -4238193929723523720L, 2934657069911674141L, 6076891152586415427L, -3783717699158133951L, 4953135073593642938L, 5569207977274658321L, 4501008218251986991L, 4608914949056012406L, -4428912061564865814L, 4989423796032989155L, -7727641956932165093L, 3207522108247185252L, -924344479047095131L, 3108845752482162934L, -5608256674910105281L, 5256746322603637809L, 3359313093287303894L, 1679799455245938027L, 5680416363222969338L, -7861397160959494704L, 5089304188877242888L, 5125161971465923153L, 2663698829192085142L, -1103038706126747314L, -80402905395234953L, 2840208181611518845L, -4757783617672493217L, 6456820608799639185L, 7891434163331525374L, 8401668394735843756L, -2280687896300332105L, 3072644785963581615L, -789462627719562386L, -8967161494941511824L, -4569866251420565672L, 8268585413250313831L, 1254405120516291232L, 1909966976157497017L, 8848404743649085333L, -4092879008052510518L, -8776312905484698288L, -678237734114500987L, -1608487313068402148L, 8116101939860513749L, 750401899863958592L, -5169412425057929857L, 6938439418119057708L, 6595232530173053731L, -2172508620443159570L, 2970426959157404996L, -3971997302950668630L, -5364694639978582355L, -9109661608865496382L, -3042586965901860991L, -216281829035099451L, 8576420872327912519L, 2100417098697099563L, -335480565948299945L, -2471705565500754317L, 2699828134389257935L, 8041674830518265676L, -4533860103266674943L, -825373182915865801L}, new long[]{4535860555263248921L, -1593883792362675665L, 6140263643322089338L, -6967817572884739277L, -9068728722506311860L, -5119179363064467534L, -3726516434561966253L, 3976828915743059002L, -7597780219754517232L, 1674533312481288529L, -5886538279117927405L, -194983811068225421L, 1971238590622152549L, 3457832774275448914L, -2295860740536826868L, -6652315346019870505L, 4727861667757458726L, -360640838872845927L, 258683541723454221L, -4212320325738251920L, -4700056087296932696L, 2233564259985426290L, 1378062126584976310L, 595850907021173971L, 1148729542089166137L, 7220711707371485274L, 4275768676387371534L, 3496324401055548880L, 5165544922527804988L, -956350612060285622L, 8412095690608439521L, -3467835190780509090L, -1868861577046741226L, 8847930418030264104L, 3309372870217980335L, -6026061361088047634L, -3133105345988201669L, 4387469920445666276L, -3377435183918791892L, 7765336488733668707L, 7626271911434349726L, -7284186450594579676L, -3896518737231810748L, -2984469520196638010L, -4320040328300053632L, 4089712366057758979L, 2084822880951770767L, -3228798250034046255L, -2818438179677494001L, -5677050606033188463L, 8115270375799669254L, 5758785072471788271L, -1421612514850123208L, 6401431119593581502L, 2607396063788253068L, 149165799264492029L, -8017447770816002550L, -7703331937915472355L, -6791485476041195222L, 3161051439931945653L, 2862129545546507393L, -5714533272975881375L, 1786865009825524840L, -3262866899008497631L, 3531814185980970784L, 1489042636343605852L, 7953372014297400692L, 4127379132466308083L, 703148615521134115L, 3051392466055327813L, 8884721462095101400L, -7563314075847069728L, 8288941250235652363L, -82120263218953346L, 2902931453887900088L, 8254758264932555771L, 446716860198103834L, 6435790520947517774L, -4979585920924371377L, -3856260413246245815L, 9043176952977856194L, 5945702658036389520L, 5618980515518141202L, -7981861276107848454L, -2502052263308094184L, 4424111993825693972L, -4449788790258986854L, -2948734028505389002L, -3412032698246774308L, 2457938976092230806L, -1165748696601507531L, 1042563110803492809L, -3747776250095536455L, 1000057432295396548L, 298081034413880039L, -474645568105829740L, 9182311907627011903L, 4237995257073580286L, 7360269973856297383L, -5295414172587761541L, -6373502291430987216L, -4063613031681449843L, 6575665438039683251L, 6873491385755763284L, 5461089107914913800L, -3577775055518873938L, -8086901795456695416L, -9208569554887962959L, -919672336669616198L, -4944387025271606081L, -3097896520644005429L, -7107622129831046450L, -8928832832669205418L, -8475347371214605409L, 4759102407226695211L, -1068097064803381689L, 8551269127753525532L, 3200854107087625055L, -230332776909038973L, 2268055074172700034L, -1104598319098278729L, -2187201184074365188L, 7467557820838912855L, 5842407176390331805L, -4665450842291730856L, -6901417610102864934L, 406460662087397399L, -6198315592433244679L, 2713878548564622716L, -2632949749941649918L, -5537491248627724180L, -1759061912934011418L, -5083451602578264766L, -2353380153490207515L, -1273221880008422459L, 1340149631943889222L, -509101851032934300L, -5259045396831739253L, -4598494993368938137L, -7842091911972561657L, 8063408691889325956L, 2565552841162102374L, 4619087852284151766L, 5214791045043151621L, -6621485381509140163L, 8447401294454977041L, -7386369256688836567L, 2118998135578172543L, -46523907001319026L, 1638138058256147361L, -6512991288666475571L, -7423992098470976807L, 7923814141646626425L, 8745197289204327461L, -8195571213428113032L, 110046607829271280L, -1445494248467825710L, -4839123971720012459L, -5919462946550491362L, 852275931467612126L, -6166501560576966924L, 6838009262779170980L, -788230540367692640L, 1935925255570044309L, -1551942601407268574L, 0, -8822376749060931930L, 5055648419791580364L, 744521925918424366L, 8708792173461317333L, 3012835619118082888L, 2417128113004220315L, 1525826018450700460L, -8961158816430844484L, -4343200236693384598L, -7004328756879980093L, 555623154201897450L, 9144795202799799759L, 3942477176883605194L, -8788888637889305173L, -326429747316083863L, 5511376580148613602L, -2782077065878689793L, 2755821882842654321L, 7814304129864883337L, 5354596692910111480L, -2669767160806458126L, 6280103384783087239L, 1191443428809820347L, -8615256373451920798L, 3349066620751495842L, 7030425627006208077L, -6305639805172127991L, -2540001088809363480L, -1314034877561220920L, -6482311944371394368L, -4492292368662033513L, 4572398174778067177L, -6761929720943764953L, -6058791419533034492L, -826038134467559856L, 6698133237560285529L, 6924249334748056253L, 4915877947555788081L, -8335447238620784507L, -5398145166680982666L, 893432504920462900L, -5434972439063135866L, -7144308136435675074L, 3644679859973545005L, 1822496103478537880L, 4867631031866531035L, -7246386587163018796L, -9101106301180867519L, -2390870482918994411L, 5651355959728424991L, 6102500154237255050L, 5321106438415311349L, -5780327991925453085L, -677612315386943571L, 6541340121823556163L, -8226814104810431883L, -8505466570320058222L, -3616892129401250397L, 5982354661116236896L, 7656949129855104915L, 6733579076392590249L, -2147505281628267023L, 6242444349043369079L, -4170944906378075523L, -1907416263749389285L, -7737550691966192403L, 7063352394543305536L, 7169984984404332976L, -8365591653203775121L, 5805861825662078829L, -1700225492413652769L, 9005272120822338610L, -4005320658966647372L, 8149771120223764726L, -2017179644254701845L, -7877433215864021001L, -5575430213156515172L, 2309691269046898027L, 3794159109699761975L, -2038879809766744319L, 5025527060452063169L, -639979543376370339L, 7517355687739125358L, 7328458050963454634L, 8586890291714583532L, -8682537007609370789L, 1228969994608986699L, 3680135560314805981L, -4805045392508215387L, 3828474564935022023L}, new long[]{-8453070235243615635L, 4884127456122942266L, 633234397777501925L, 4175331866391974078L, -4485881859065591886L, 3179702086296496554L, -1512837584588167014L, -7752573287164883235L, 2821567796802171918L, -8429518127726092206L, 8620367719189969058L, -6534403333025349498L, 2483193514877514189L, -2626196862965158658L, 6709821147687032714L, 1033155461149792359L, -1270162888182207143L, -5769827404365637859L, 7895548116537857006L, -731649153213821910L, -713343999045677205L, -7647763973851249438L, -1429157854399929305L, -3576825843976268310L, -4260165995353210255L, 5087312367917758201L, 4403136284925796162L, -7001683542362597050L, 3775805186400799640L, 7039684027147830007L, -4097783045094489808L, -8749424154144572556L, -8707936849463168693L, -3223332091657620965L, 7393281011622612554L, -3839783834919855594L, -6564596451137123655L, 4010248068099147993L, 1391157335655481238L, -9045169530825444216L, 4570854599768479292L, -8189094146226821743L, 6677411409753660853L, 1869614486838700045L, 2692552123250949488L, 5803321545042218156L, 2939237684151739990L, -5883521838890148574L, 979419644422445862L, 3673082354135452954L, -6913296576561183419L, -415785719237910926L, -834198937845742059L, 8160101394646269472L, 8665997893524695802L, -4821127355493817676L, -211357278447627569L, 5384800866856167392L, -5530020817350821165L, -4854011018389182325L, 1572125733959825684L, -2126792025060737984L, 5756880660441440803L, 9141459606454807672L, 5643134515567552540L, -1831053266724195396L, 3878345071173861799L, -9219713781305836042L, 2223083610095396016L, 7793025783231257041L, 270525564056519489L, -599737493290351276L, -3630580481258694997L, -3980128841890065560L, -3372763326975378587L, 2096675571659106289L, -2722105626296801599L, -6792565267702591622L, -7982779848137766114L, 3546709370140069467L, -2740375726023973504L, 177299642257213822L, 5128824965654363334L, 3284371762775499669L, -5027102895479968247L, -1791810028540380797L, -43630025795953743L, -3929600256180692649L, -4338348378199077172L, -6738813919702414277L, -7350213990308637189L, 1469770529583346475L, -2180581718931620095L, 8788550027033796805L, 7435101501630170229L, -4431564408731826957L, 7273261710249789236L, -1324515669982335464L, -320272809040341939L, 4054715969654905473L, 7599903567200476178L, 5246485543787097758L, 2066061333143563726L, 8899911909190592955L, -7128040995085149689L, 6203347905963379246L, -5167386234789582985L, 8982737327710905606L, -5677756788191890515L, 4733860583378205252L, 8385890890472733667L, -8870358421544177142L, 1983784429268132211L, 1144615724587504216L, -8949243203173892425L, 874653248460230937L, 8806272569836118916L, -5323464818055102704L, 8490507753801150940L, 4996983704035276216L, -2547329411460620221L, -8242880533652363568L, 3101674511553930519L, 8063548039483480720L, 2782314667368316465L, 7188904195392064393L, -5211864924090600145L, 9015613289737121593L, -6285131514020389984L, 6918945025995344072L, -8075373294475235410L, 6314684983379722257L, -241532845093005072L, 516733887222336701L, 5295035016025023649L, 4372970618206377341L, 1367579971541251497L, -1675255866329961509L, 4788193436127426821L, -8033431620036738783L, -1920834373681827587L, 8286510523341552993L, 4528946180991933443L, -1115059448466290199L, 354332099228667900L, 1703685653761847402L, -2033127364819077505L, -554171441618135284L, 1749306493112033842L, 6593587578816598280L, -3139112079942074714L, 3696774096474081061L, 6359145787360465481L, -4695297660307955211L, 7551609152712119853L, -3421199581619080870L, -85565928058662514L, 5408360666650553823L, 1887903149406137164L, -6643353753801603580L, 1193308391354416343L, -6122745256225810207L, 7714012032021747052L, -940295458348941161L, 3338705706857455828L, 0, -3818308703608942551L, -1953665223426671934L, 8307846011238545246L, 7949845912698622127L, 123000737600621119L, -3465393072583450667L, 3896669045897085670L, 1241734746156300008L, 3450192318869875435L, -1624472154218651164L, 773588076271081371L, -5446358391519529362L, 4966387029739569031L, -6444585804073991225L, 9180589562619618887L, -7518677962122949220L, -2516687689508984196L, -4227713340001105842L, -8591376611877854445L, 5589364474795928413L, -5728452502948824686L, -5341733827254846383L, -9099519005106489399L, -5551364006150524692L, 795073098915667364L, -1066649621789244458L, -5937835998867923357L, 7690267545386164051L, 2659693781377578831L, -5044808816750465720L, -7856985207392810913L, 6500344023943830327L, -2390298213912958659L, -4608855104520647283L, -4933455855289899978L, 1589851584549665365L, 2429422383846896268L, 6482023338771290230L, -3036729392062401383L, 6185590308720085359L, -6236844791758827105L, -8839769439558879179L, 6080806279852835664L, 2576885667214146546L, -6962009294672981127L, 6001208834408615917L, 6836195239104500939L, -6040333039102815140L, -2905178410103177753L, 3080356643708727080L, -2860693619659558977L, 2306958247692105907L, -6402773006393882120L, 477155333442143874L, -3735336985853682540L, 386794645880882627L, 7093418736453091766L, 4288595764497750464L, -3246936939308599260L, 5979776620583929298L, -4137334106762671345L, -2303035963802815170L, 7303480121366170891L, -7473099242571571260L, -1534127960405515611L, -7331909943831013702L, -7160476017532022728L, 5486956530453307746L, -2348898833569914110L, -4656142419646682166L, -7835527709155294624L, 683790475565323482L, -8345280785751675665L, 8109430964352234527L, 4193092754259694591L, 4613640514157211771L, 6875861785975561972L, 2264473090495966863L, -433528061428458189L, 3498327026982732900L, -8542852432728676052L, -3019022379190732840L, -1158686167486945434L, -7630022740424964189L, -6146464449756130594L, 8508793109654811293L, 5853965616021964435L, -916593816117900632L, -7238641104478299003L, 2990031287613762665L}, new long[]{9094991057681989827L, 8606138803235823195L, -2286200986180536915L, -8724216130027185838L, -1649148858432656587L, 4026406244121087995L, -7635863337866995078L, 793466831469952157L, -875870498908510964L, 7069806061322532341L, 9025437343642800970L, 7902240744518357090L, -6962787910639909662L, -1403860579092282445L, 169507756355412485L, -6287132366789145831L, -396162960508142700L, -5352074804142321757L, 8708198706664012748L, -2075344634876451292L, -4084819757827458690L, 1669125631761648043L, -4407130716549885448L, 6740073072435942682L, -8819606099442431269L, 2010954567078870965L, -702210179433312886L, -3237789452609264804L, 734067863229058321L, -1715852844487206215L, -6780347474255882879L, -1234467515678383690L, -939753275567238016L, 919214179616323102L, -4836479128919502533L, 3253262042600145346L, 8178958160323126612L, 3337947751052939851L, -434695500023977955L, -7029504953396562578L, -3596390815882237978L, 1052577896389186587L, 2193586592470914867L, 2442179127876909020L, -3778948349093605536L, -8171637376153569722L, 5137708932055784227L, -6227768720359261547L, -8303875261599624125L, 4815958689639822245L, 84753878177706380L, 6315843737766598667L, -64743613097569518L, 3871579785502944766L, -3903411312947434139L, 5793075969118851720L, 249718177605522313L, -8430187655510548800L, -514890802992621167L, 5498396982908774334L, -5688626311019391319L, 7667703386526505697L, -1763570622529142994L, -9138337821371313199L, -311433546468111848L, 1929022448056995385L, 1586665506641377319L, -6475019125594218750L, 8445678447034858590L, 7274058453835599610L, 2629305166521655130L, -2637746980779504568L, 8787833086900898368L, 1545819354008644526L, 3789645467455986802L, 5856993792710681348L, -1103552092532705659L, 2091361543251510458L, 7459518732372467059L, 7397325080452181247L, -3111338451506714145L, 4059487177496226412L, 559362848516098691L, 3155811880282733261L, 339015512710764810L, 499436355210984207L, 4260721118560188901L, 673503137787469464L, -3365532740684676775L, 4141949501641595872L, -8664854682622924578L, -5969827484503727201L, 5626135598191838651L, -3026606838441060269L, -6860593217317067763L, -3647879656817937297L, -8883511003774190761L, -2378568021286198066L, 5444143179324534077L, -7294069234986242972L, 609631631123197716L, -5523727707549764436L, 5576337977799677490L, -1321411723963813313L, -618078659199583226L, -4151521544861120270L, 4387173184941765478L, -5082267926572090947L, -4572121610279439363L, 5708620050009289783L, 6430542613299891216L, -2988006129208833062L, 1851664366124113328L, -5434569976195760593L, -9054144591182708648L, -8004677230250270347L, -1044133607961229559L, -7755831154581784449L, 4978440652618840358L, 4581695294717042927L, -5164165273538705359L, 3091344129077791553L, 7147744857192531065L, 4652093626960253344L, -1156491198976405446L, -3430015610173165355L, -8613457947268510391L, -6533824529218007925L, -2204279587396795359L, -857080412230766705L, 1346746777927262509L, -2717416407949558332L, 2259738658050909887L, 6111753265489546114L, -5801516065654160998L, -1555388365253904196L, -1883419760710312661L, -5585908440206867168L, 7729369582102031213L, 8912221009216268357L, 4519477316101755235L, 1281193087194051236L, -3297738211024334128L, -8091393832113813558L, 8118974354974954712L, -8549025517874578235L, 8521442237103337431L, 8066035438451585639L, 4453855455643222762L, 5377403871458974897L, 6345848246193028508L, 6845086483143640723L, 7988624098924168171L, 8365446172989525458L, 1467823520972380706L, 2935396374752471364L, 2813067799104965843L, -7553963791875298314L, 403485462937118854L, 2688701935736925910L, -2483016523405101241L, -4245213868322808965L, 3468874151188153720L, -5196122377024721498L, 1132260792577222039L, 0, 3535028415789053172L, 2491459977799754837L, 1003369614229768082L, 6675627174024062102L, 2029743588748113206L, 4729507165508558892L, 3637180897245407101L, -4494147492598365583L, 1413426849607831713L, -5885706822078681066L, -2876680564085760063L, -6049474784002780653L, 2575613213387593177L, 5320806183970131768L, -7132290993934539033L, 3422699430205023175L, -6593786619212038905L, -7371988033351401491L, 5071567999032830639L, -2319147337689098942L, -2546901499085257013L, -5755341154755156187L, -5039803533475599820L, -1949596290422182745L, -4307443116932011273L, -7809662711198584068L, -8219159179136522801L, 3173053820371464270L, -2907808627218761130L, 2362498430710285904L, 4897844766612488745L, 6236211556551125383L, -9222526378770226595L, -777413184082125309L, 1774266358176803900L, -7678395900680418829L, -4962370478595122248L, 8283899003267266269L, 4916772258018216106L, -2152722576735774808L, -197091380988415721L, -3845122679784571156L, -129224283564645730L, -8974499490705067564L, -3514471616123578774L, -6132310581227493092L, 2749198491462122847L, -7943022304666578695L, 8966051432100897478L, 9158911080295248207L, -3965042186711950103L, -6410584497178874226L, 6195919557778536974L, 5258610333024763572L, 7340795562680270710L, 7820356866570772974L, -3725255399656372765L, -4703049166709492930L, 1218977307603845928L, -5274083438005724118L, -4770362110272610121L, 5926479337025089677L, -7449951293353308575L, -7211571863907705368L, -8490147546478973108L, -6648039220059424892L, -1493156917619619536L, -4324684060446515084L, -7194473925616724117L, 7508364738339027172L, 6905068089466205983L, -257037940377878373L, 3949006037152890487L, 2995320669031979208L, -6732757501548149240L, 5985863049541370113L, 4182723086503020649L, 7574503472341076328L, 8348347100700559185L, 8828052517901670857L, 6506238233651847065L, 6937453620375099376L, -1825203695318757726L, 3698801050773903089L, -2792546844826423731L, 7019935873167451772L, -7875781928866380944L, -6068403477874304880L, -4641396440150900046L, 6586468308675751445L}, new long[]{-3444757183192547354L, 1637721477308921125L, -6297248449898319672L, 7646921253694161755L, 7930897700415732235L, 7354091399752226219L, 8517004666874317814L, 5453187778286554101L, -1043976743468927160L, -4194833387358178817L, -7708955174336155445L, -8563204505528125338L, -5712286124442861259L, -6823037065440364932L, -8275920727835279050L, -2974881390760536930L, 3965582442916511115L, 818990539008627868L, 6511022660614307004L, -1320077589205483547L, -3262719323413484082L, 6797246517703968236L, 2154964243676750265L, -5954292490194018164L, 3398770582681996406L, 6305239209379988312L, 4529647259392179007L, -3901986219323627761L, -8953700191097190930L, -6484135588680797168L, 1169952615461543517L, 3200289132027606858L, 4147956921301236051L, 708224122457687732L, -3614636676010189217L, -459997231093475096L, -7147120204223564161L, -4852545587358263355L, 8231768351619019430L, 8694883466081947438L, 1455760652066973453L, -8866994048856881278L, -7431164614507622609L, -1132130964538483108L, -2635626018905855110L, 7243836256093423491L, 3660620939660078543L, 2208053939432577669L, -1611933249001666891L, -174895080934727240L, -4029722790011892197L, 3377712478417560930L, -551951845368008196L, -1501670271861746531L, 6217894297849621068L, 7825436358742779939L, 4987043392780078893L, -6063885576440067084L, -5030923433844245315L, 8746199649574856250L, 869662533239166704L, -3796286352345451225L, -1521602510851146359L, 2911244210510417434L, -64431099335674992L, -6948146172091560637L, -7618076501429580809L, 5928147536793457436L, -7233802500807019501L, 5738424067781505701L, 4545920254677684779L, -4088906883540786217L, -7905997789725412697L, -3210311977975446310L, 2538748850867346054L, -6590450082657491400L, -5425002303774099355L, -2455945396081426430L, 763205940277591432L, -4430134953212300397L, -8385457009380595122L, 3093811003563252786L, 4790004205042634049L, 8356374131917757362L, 525807322001048172L, -8472943208417328294L, 8060006530596508447L, -4140047660305118525L, 4971791225320957497L, -2090548950504402391L, -6769791891600257728L, 1866917567049488617L, -844781378265929972L, 4878334347704591445L, -2869059261100175290L, 5838476093026002976L, -1785299366336707987L, 585131888633540512L, 4258502335484898171L, 9216164226481474882L, 1051613531605566680L, -9135643629782968378L, -5660125035251783647L, 7592012931346331751L, -662303645644098268L, 1921896258116084693L, 3288584640079142494L, 182885720694182440L, -8015847144335122289L, 4347442274373970199L, -6191868403371105568L, 470303682846092152L, -2691269845522873746L, -4373505448783357305L, 7877737635941138743L, -263331292381624148L, 8981595325954651774L, -354518366482511168L, -4617554474609824519L, 7536474657577537907L, -3847708088685798349L, -4907725647172659799L, 998525038457700324L, 8121292137418072206L, -1983208384017991343L, -8292017801529169886L, -1230246063685679911L, 1689003026191473713L, 6955846591918243539L, -5477907817628009975L, -7995247020750796389L, 1104161065046732236L, 5822238831865856308L, 7770259743761115727L, -7724066638932354593L, 3109098905151763750L, 7477148998953467071L, 5270373732550072797L, 235152363269307196L, 2264823631868769169L, -6879983194860478616L, -3152901566336629066L, 129144564315678996L, -6010956629246162536L, 6107963510664673380L, 2809900646717377218L, -2402152012640863426L, 292565944326076752L, -1895757919103578043L, -933739123822025376L, 7062097245496290555L, -1699102754331938911L, -3738854843827800245L, 6021262719684655624L, 1972895747902540481L, 3945791491441759391L, 8801111098641915142L, -4746241057700133395L, -3503934099848459145L, -646875556056753104L, 5560617129282953357L, 6890080809738628344L, 2627701227983280362L, -8756221997843360342L, 5539875718724946329L, 0, -1412735959647742223L, -5369322743027527311L, -4483453897402251089L, 7299656588959035031L, 1583357035284794905L, -5141091716070003051L, -6246073038520247844L, 6400302286636641940L, 6420058603497817984L, 6691528990365472708L, -9046393196149773062L, -3316034865972881678L, 9038681643377794922L, -5314466647222146483L, 4440522822651396099L, 8411535559894538718L, -2350026657572040150L, 4238992274866487919L, -369665565213580332L, -4607954124316941893L, -8847097543996772714L, -4322851948414609173L, 5169063728564034821L, -5775964245203979612L, -7526087252531780893L, -5085131195746564223L, 2428230993188407470L, 3570877924549138163L, -3560844495143377565L, 4060646644394037319L, 3855408526229221283L, -1804949614949815431L, -2272807811582181375L, 8928671747737777170L, 1348049825574685813L, 3677174894530839771L, -8093047239747710178L, -2745544849441605294L, 8644847306339132130L, 2719514554751545854L, 8462570783810646218L, 1405101508380157281L, 3478079366649118695L, -6538992612182536404L, -2584948672878155498L, 6598086612559222184L, -5606876459814153443L, 3768380308409468599L, 6708048310613262032L, 1739040292966674941L, -7036336127295658921L, -7325510790119301881L, -6645608358396189612L, 9091840505278506070L, -8669017857895383874L, 4700331558701315709L, -7415771159533754821L, 4679309188558905193L, 2030323828767328429L, 5649811934742993841L, 415940168518455364L, -2179868550282598123L, -954655941116998540L, -5190986870181543079L, 7190925116101314031L, -7126238021551530133L, -2074417242192479427L, -3029668045542985822L, -753108823078777320L, 5077496726762238993L, 2339654594279481786L, -4800103607302455599L, -5900428737983865936L, -85066957045458300L, 6127437837148246384L, 3002776577694476046L, -6356433652615537404L, -8186022235176912374L, 1294312070793836361L, -2918955505047487094L, -1213832296285327923L, 5255402455948538057L, 2517972805691405202L, 7008429859484506055L, 5361338717396067041L, -9152021663061328174L, 8178097563976550810L, -7817526943664710733L, -8582820119530581646L, 2824906557929530326L}};
    private final byte[] N = new byte[64];
    private final byte[] Sigma = new byte[64];
    private final byte[] Ki = new byte[64];
    private final byte[] m = new byte[64];
    private final byte[] tmp = new byte[64];
    private final byte[] block = new byte[64];
    private int bOff = 64;

    public GOST3411_2012Digest(byte[] bArr) {
        byte[] bArr2 = new byte[64];
        this.IV = bArr2;
        byte[] bArr3 = new byte[64];
        this.h = bArr3;
        System.arraycopy(bArr, 0, bArr2, 0, 64);
        System.arraycopy(bArr, 0, bArr3, 0, 64);
    }

    private void E(byte[] bArr, byte[] bArr2) {
        System.arraycopy(bArr, 0, this.Ki, 0, 64);
        xor512(bArr, bArr2);
        F(bArr);
        for (int i = 0; i < 11; i++) {
            xor512(this.Ki, C[i]);
            F(this.Ki);
            xor512(bArr, this.Ki);
            F(bArr);
        }
        xor512(this.Ki, C[11]);
        F(this.Ki);
        xor512(bArr, this.Ki);
    }

    private void F(byte[] bArr) {
        long[][] jArr = T;
        long[] jArr2 = jArr[0];
        long j = jArr2[bArr[56] & 255] ^ 0;
        long[] jArr3 = jArr[1];
        long j2 = j ^ jArr3[bArr[48] & 255];
        long[] jArr4 = jArr[2];
        long j3 = j2 ^ jArr4[bArr[40] & 255];
        long[] jArr5 = jArr[3];
        long j4 = j3 ^ jArr5[bArr[32] & 255];
        long[] jArr6 = jArr[4];
        long j5 = j4 ^ jArr6[bArr[24] & 255];
        long[] jArr7 = jArr[5];
        long j6 = j5 ^ jArr7[bArr[16] & 255];
        long[] jArr8 = jArr[6];
        long j7 = j6 ^ jArr8[bArr[8] & 255];
        long[] jArr9 = jArr[7];
        long j8 = j7 ^ jArr9[bArr[0] & 255];
        long j9 = (((((((jArr2[bArr[57] & 255] ^ 0) ^ jArr3[bArr[49] & 255]) ^ jArr4[bArr[41] & 255]) ^ jArr5[bArr[33] & 255]) ^ jArr6[bArr[25] & 255]) ^ jArr7[bArr[17] & 255]) ^ jArr8[bArr[9] & 255]) ^ jArr9[bArr[1] & 255];
        long j10 = (((((((jArr2[bArr[58] & 255] ^ 0) ^ jArr3[bArr[50] & 255]) ^ jArr4[bArr[42] & 255]) ^ jArr5[bArr[34] & 255]) ^ jArr6[bArr[26] & 255]) ^ jArr7[bArr[18] & 255]) ^ jArr8[bArr[10] & 255]) ^ jArr9[bArr[2] & 255];
        long j11 = (((((((jArr2[bArr[59] & 255] ^ 0) ^ jArr3[bArr[51] & 255]) ^ jArr4[bArr[43] & 255]) ^ jArr5[bArr[35] & 255]) ^ jArr6[bArr[27] & 255]) ^ jArr7[bArr[19] & 255]) ^ jArr8[bArr[11] & 255]) ^ jArr9[bArr[3] & 255];
        long j12 = (((((((jArr2[bArr[60] & 255] ^ 0) ^ jArr3[bArr[52] & 255]) ^ jArr4[bArr[44] & 255]) ^ jArr5[bArr[36] & 255]) ^ jArr6[bArr[28] & 255]) ^ jArr7[bArr[20] & 255]) ^ jArr8[bArr[12] & 255]) ^ jArr9[bArr[4] & 255];
        long j13 = (((((((jArr2[bArr[61] & 255] ^ 0) ^ jArr3[bArr[53] & 255]) ^ jArr4[bArr[45] & 255]) ^ jArr5[bArr[37] & 255]) ^ jArr6[bArr[29] & 255]) ^ jArr7[bArr[21] & 255]) ^ jArr8[bArr[13] & 255]) ^ jArr9[bArr[5] & 255];
        long j14 = (((((((jArr2[bArr[62] & 255] ^ 0) ^ jArr3[bArr[54] & 255]) ^ jArr4[bArr[46] & 255]) ^ jArr5[bArr[38] & 255]) ^ jArr6[bArr[30] & 255]) ^ jArr7[bArr[22] & 255]) ^ jArr8[bArr[14] & 255]) ^ jArr9[bArr[6] & 255];
        long j15 = (((((((jArr2[bArr[63] & 255] ^ 0) ^ jArr3[bArr[55] & 255]) ^ jArr4[bArr[47] & 255]) ^ jArr5[bArr[39] & 255]) ^ jArr6[bArr[31] & 255]) ^ jArr7[bArr[23] & 255]) ^ jArr8[bArr[15] & 255]) ^ jArr9[bArr[7] & 255];
        bArr[7] = (byte) (j8 >> 56);
        bArr[6] = (byte) (j8 >> 48);
        bArr[5] = (byte) (j8 >> 40);
        bArr[4] = (byte) (j8 >> 32);
        bArr[3] = (byte) (j8 >> 24);
        bArr[2] = (byte) (j8 >> 16);
        bArr[1] = (byte) (j8 >> 8);
        bArr[0] = (byte) j8;
        bArr[15] = (byte) (j9 >> 56);
        bArr[14] = (byte) (j9 >> 48);
        bArr[13] = (byte) (j9 >> 40);
        bArr[12] = (byte) (j9 >> 32);
        bArr[11] = (byte) (j9 >> 24);
        bArr[10] = (byte) (j9 >> 16);
        bArr[9] = (byte) (j9 >> 8);
        bArr[8] = (byte) j9;
        bArr[23] = (byte) (j10 >> 56);
        bArr[22] = (byte) (j10 >> 48);
        bArr[21] = (byte) (j10 >> 40);
        bArr[20] = (byte) (j10 >> 32);
        bArr[19] = (byte) (j10 >> 24);
        bArr[18] = (byte) (j10 >> 16);
        bArr[17] = (byte) (j10 >> 8);
        bArr[16] = (byte) j10;
        bArr[31] = (byte) (j11 >> 56);
        bArr[30] = (byte) (j11 >> 48);
        bArr[29] = (byte) (j11 >> 40);
        bArr[28] = (byte) (j11 >> 32);
        bArr[27] = (byte) (j11 >> 24);
        bArr[26] = (byte) (j11 >> 16);
        bArr[25] = (byte) (j11 >> 8);
        bArr[24] = (byte) j11;
        bArr[39] = (byte) (j12 >> 56);
        bArr[38] = (byte) (j12 >> 48);
        bArr[37] = (byte) (j12 >> 40);
        bArr[36] = (byte) (j12 >> 32);
        bArr[35] = (byte) (j12 >> 24);
        bArr[34] = (byte) (j12 >> 16);
        bArr[33] = (byte) (j12 >> 8);
        bArr[32] = (byte) j12;
        bArr[47] = (byte) (j13 >> 56);
        bArr[46] = (byte) (j13 >> 48);
        bArr[45] = (byte) (j13 >> 40);
        bArr[44] = (byte) (j13 >> 32);
        bArr[43] = (byte) (j13 >> 24);
        bArr[42] = (byte) (j13 >> 16);
        bArr[41] = (byte) (j13 >> 8);
        bArr[40] = (byte) j13;
        bArr[55] = (byte) (j14 >> 56);
        bArr[54] = (byte) (j14 >> 48);
        bArr[53] = (byte) (j14 >> 40);
        bArr[52] = (byte) (j14 >> 32);
        bArr[51] = (byte) (j14 >> 24);
        bArr[50] = (byte) (j14 >> 16);
        bArr[49] = (byte) (j14 >> 8);
        bArr[48] = (byte) j14;
        bArr[63] = (byte) (j15 >> 56);
        bArr[62] = (byte) (j15 >> 48);
        bArr[61] = (byte) (j15 >> 40);
        bArr[60] = (byte) (j15 >> 32);
        bArr[59] = (byte) (j15 >> 24);
        bArr[58] = (byte) (j15 >> 16);
        bArr[57] = (byte) (j15 >> 8);
        bArr[56] = (byte) j15;
    }

    private void addMod512(byte[] bArr, int i) {
        int i2 = (bArr[63] & 255) + (i & 255);
        bArr[63] = (byte) i2;
        int i3 = (bArr[62] & 255) + ((i >> 8) & 255) + (i2 >> 8);
        bArr[62] = (byte) i3;
        for (int i4 = 61; i4 >= 0 && i3 > 0; i4--) {
            i3 = (bArr[i4] & 255) + (i3 >> 8);
            bArr[i4] = (byte) i3;
        }
    }

    private void addMod512(byte[] bArr, byte[] bArr2) {
        int i = 0;
        for (int i2 = 63; i2 >= 0; i2--) {
            i = (i >> 8) + (bArr[i2] & 255) + (bArr2[i2] & 255);
            bArr[i2] = (byte) i;
        }
    }

    private void g_N(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        System.arraycopy(bArr, 0, this.tmp, 0, 64);
        xor512(bArr, bArr2);
        F(bArr);
        E(bArr, bArr3);
        xor512(bArr, this.tmp);
        xor512(bArr, bArr3);
    }

    private void reverse(byte[] bArr, byte[] bArr2) {
        int length = bArr.length;
        for (int i = 0; i < length; i++) {
            bArr2[(length - 1) - i] = bArr[i];
        }
    }

    private void xor512(byte[] bArr, byte[] bArr2) {
        for (int i = 0; i < 64; i++) {
            bArr[i] = (byte) (bArr[i] ^ bArr2[i]);
        }
    }

    @Override // org.bouncycastle.util.Memoable
    public abstract Memoable copy();

    @Override // org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i) {
        int i2;
        int i3 = 64 - this.bOff;
        int i4 = 0;
        while (true) {
            i2 = 64 - i3;
            if (i4 == i2) {
                break;
            }
            this.m[i4] = 0;
            i4++;
        }
        byte[] bArr2 = this.m;
        bArr2[63 - i3] = 1;
        int i5 = this.bOff;
        if (i5 != 64) {
            System.arraycopy(this.block, i5, bArr2, i2, i3);
        }
        g_N(this.h, this.N, this.m);
        addMod512(this.N, i3 * 8);
        addMod512(this.Sigma, this.m);
        byte[] bArr3 = this.h;
        byte[] bArr4 = Zero;
        g_N(bArr3, bArr4, this.N);
        g_N(this.h, bArr4, this.Sigma);
        reverse(this.h, this.tmp);
        System.arraycopy(this.tmp, 0, bArr, i, 64);
        reset();
        return 64;
    }

    @Override // org.bouncycastle.crypto.Digest
    public abstract String getAlgorithmName();

    @Override // org.bouncycastle.crypto.ExtendedDigest
    public int getByteLength() {
        return 64;
    }

    @Override // org.bouncycastle.crypto.Digest
    public abstract int getDigestSize();

    @Override // org.bouncycastle.crypto.Digest
    public void reset() {
        this.bOff = 64;
        Arrays.fill(this.N, (byte) 0);
        Arrays.fill(this.Sigma, (byte) 0);
        System.arraycopy(this.IV, 0, this.h, 0, 64);
        Arrays.fill(this.block, (byte) 0);
    }

    @Override // org.bouncycastle.util.Memoable
    public void reset(Memoable memoable) {
        GOST3411_2012Digest gOST3411_2012Digest = (GOST3411_2012Digest) memoable;
        System.arraycopy(gOST3411_2012Digest.IV, 0, this.IV, 0, 64);
        System.arraycopy(gOST3411_2012Digest.N, 0, this.N, 0, 64);
        System.arraycopy(gOST3411_2012Digest.Sigma, 0, this.Sigma, 0, 64);
        System.arraycopy(gOST3411_2012Digest.Ki, 0, this.Ki, 0, 64);
        System.arraycopy(gOST3411_2012Digest.m, 0, this.m, 0, 64);
        System.arraycopy(gOST3411_2012Digest.h, 0, this.h, 0, 64);
        System.arraycopy(gOST3411_2012Digest.block, 0, this.block, 0, 64);
        this.bOff = gOST3411_2012Digest.bOff;
    }

    @Override // org.bouncycastle.crypto.Digest
    public void update(byte b) {
        byte[] bArr = this.block;
        int i = this.bOff - 1;
        this.bOff = i;
        bArr[i] = b;
        if (i == 0) {
            g_N(this.h, this.N, bArr);
            addMod512(this.N, 512);
            addMod512(this.Sigma, this.block);
            this.bOff = 64;
        }
    }

    @Override // org.bouncycastle.crypto.Digest
    public void update(byte[] bArr, int i, int i2) {
        while (this.bOff != 64 && i2 > 0) {
            update(bArr[i]);
            i2--;
            i++;
        }
        while (i2 >= 64) {
            System.arraycopy(bArr, i, this.tmp, 0, 64);
            reverse(this.tmp, this.block);
            g_N(this.h, this.N, this.block);
            addMod512(this.N, 512);
            addMod512(this.Sigma, this.block);
            i2 -= 64;
            i += 64;
        }
        while (i2 > 0) {
            update(bArr[i]);
            i2--;
            i++;
        }
    }
}
