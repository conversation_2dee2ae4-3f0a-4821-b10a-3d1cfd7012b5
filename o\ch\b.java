package o.ch;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import kotlin.jvm.internal.ByteCompanionObject;
import o.cc.e;
import o.ee.g;
import o.eg.d;
import o.ei.i;
import o.er.c;
import o.er.f;
import o.er.w;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ch\b.smali */
public final class b implements e<o.el.e> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static int b;
    private static char[] c;
    private static boolean d;
    private static int[] g;
    private static int h;
    private static int i;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        b();
        ViewConfiguration.getFadingEdgeLength();
        ViewConfiguration.getGlobalActionKeyTimeout();
        View.MeasureSpec.getSize(0);
        int i2 = h + 85;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void b() {
        c = new char[]{61716, 61759, 61753, 61764, 61751, 61756, 61728, 61766, 61761, 61754, 61755, 61734, 61748, 61936, 61710, 61717, 61765, 61760, 61775, 61762, 61771, 61752, 61732, 61721, 61757, 61767, 61731, 61763, 61770, 61749, 61729, 61738, 61733, 61719, 61722, 61750, 61737, 61699, 61739, 61744, 61727, 61768};
        a = true;
        d = true;
        b = 782102992;
        g = new int[]{663919688, 113452599, 1680769374, 726608554, 82441453, -45130713, 1020581601, -1643032910, 1603447620, -1229058752, 552230290, 103949572, 419952506, -341823729, 2097322584, 1458977684, 651702923, 1439128452};
    }

    static void init$0() {
        $$a = new byte[]{108, 119, -51, 110};
        $$b = 80;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 4
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r9 = 121 - r9
            byte[] r0 = o.ch.b.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r8 = r8 + r10
            int r7 = r7 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ch.b.k(short, short, byte, java.lang.Object[]):void");
    }

    @Override // o.cc.e
    public final /* synthetic */ o.el.e d(String str, String str2, int i2, String str3) {
        int i3 = h + 13;
        i = i3 % 128;
        boolean z = i3 % 2 == 0;
        o.er.e b2 = b(str);
        switch (z) {
            case true:
                break;
            default:
                int i4 = 35 / 0;
                break;
        }
        int i5 = i + 23;
        h = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return b2;
            default:
                int i6 = 99 / 0;
                return b2;
        }
    }

    public b(String str) {
        this.e = str;
    }

    @Override // o.cc.e
    public final List<o.el.e> a(String str, String str2, int i2, String str3, o.eg.b bVar) throws i {
        Object obj;
        g.c();
        Object[] objArr = new Object[1];
        f(null, 126 - ImageFormat.getBitsPerPixel(0), null, "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f(null, 127 - TextUtils.indexOf("", "", 0), null, "\u008e\u008f\u008e\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u008d\u0085\u008b\u0088", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
        String str4 = this.e;
        Object[] objArr3 = new Object[1];
        f(null, (ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u008d\u0088\u0085\u0090\u0086\u0085\u0084\u0082\u0083\u0082\u008d", objArr3);
        if (Objects.equals(str4, ((String) objArr3[0]).intern())) {
            Object[] objArr4 = new Object[1];
            f(null, 127 - TextUtils.getOffsetAfter("", 0), null, "\u008d\u0088\u0085\u0090\u0093\u0085\u0086\u0092\u0091\u0082\u008d", objArr4);
            o.er.i c2 = c(((String) objArr4[0]).intern(), bVar);
            Object[] objArr5 = new Object[1];
            f(null, TextUtils.indexOf("", "", 0, 0) + 127, null, "\u0094\u0082\u0087\u0093\u0085\u0086\u0092\u0091\u0082\u008d", objArr5);
            o.er.i c3 = c(((String) objArr5[0]).intern(), bVar);
            Object[] objArr6 = new Object[1];
            f(null, 127 - KeyEvent.getDeadChar(0, 0), null, "\u0093\u0085\u0087\u008b\u0086\u0083\u0089\u0089\u0098\u0089\u0097\u0096\u0091\u0095\u0092", objArr6);
            o.er.i c4 = c(((String) objArr6[0]).intern(), bVar);
            Object[] objArr7 = new Object[1];
            j(new int[]{-814120047, 724888225, 1085257357, 1354652778, 886246121, 2145379813, -1634686476, -701980611, 1437892022, -520629108, 1295913012, -1389753791}, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 20, objArr7);
            o.er.i c5 = c(((String) objArr7[0]).intern(), bVar);
            Object[] objArr8 = new Object[1];
            f(null, 127 - TextUtils.getCapsMode("", 0, 0), null, "\u0088\u0089\u0084\u0091\u008b\u0095\u009a\u008b\u008c\u0094\u008b\u0099\u0089\u0097\u0089\u0097\u0096\u0091\u0095\u0092", objArr8);
            o.er.i c6 = c(((String) objArr8[0]).intern(), bVar);
            Object[] objArr9 = new Object[1];
            f(null, (KeyEvent.getMaxKeyCode() >> 16) + 127, null, "\u0084\u0094\u008b\u009c\u008b\u0083\u0085\u0094\u0085\u009b\u0094\u008b\u0099\u0089\u0084", objArr9);
            f a2 = a(((String) objArr9[0]).intern(), bVar);
            Object[] objArr10 = new Object[1];
            f(null, 126 - ExpandableListView.getPackedPositionChild(0L), null, "\u0084\u0094\u008b\u009c\u008b\u0083\u0085\u0094\u0085\u009b\u0094\u009e\u009d", objArr10);
            f a3 = a(((String) objArr10[0]).intern(), bVar);
            Object[] objArr11 = new Object[1];
            f(null, 126 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), null, "\u0094\u009e \u0093\u0085\u0086\u0092\u0091\u0082\u0081\u0088\u009f\u008b\u0084\u0085\u008b\u0088\u009e", objArr11);
            o.er.i c7 = c(((String) objArr11[0]).intern(), bVar);
            Object[] objArr12 = new Object[1];
            j(new int[]{-1974970082, 1811659633, 1760123784, 858593993, -1874576319, 1616489455}, Color.red(0) + 11, objArr12);
            c b2 = b(((String) objArr12[0]).intern(), bVar);
            Object[] objArr13 = new Object[1];
            j(new int[]{1800401799, -611948496, -1544138111, -1258335057, -1540380720, 264528389}, 10 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr13);
            o.er.i c8 = c(((String) objArr13[0]).intern(), bVar);
            Object[] objArr14 = new Object[1];
            j(new int[]{1877179923, -1228453681, 448962958, -996752647, 4184907, 1850886246}, 12 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr14);
            w e = e(((String) objArr14[0]).intern(), bVar);
            Object[] objArr15 = new Object[1];
            f(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u0093\u0085\u0087\u0083\u0094\u0095\u0091\u009c\u0085¡\u0089\u0097\u0096\u0091\u0095\u0092", objArr15);
            obj = new o.er.e(str, c2, c3, c4, c5, c6, a2, a3, c7, b2, c8, e, c(((String) objArr15[0]).intern(), bVar));
        } else {
            String str5 = this.e;
            Object[] objArr16 = new Object[1];
            f(null, 127 - (Process.myTid() >> 22), null, "\u0084\u0094\u0095\u0089\u009e\u009e¢\u0086\u0085\u0084\u0082\u0083\u0082\u008d", objArr16);
            if (!Objects.equals(str5, ((String) objArr16[0]).intern())) {
                obj = null;
            } else {
                Object[] objArr17 = new Object[1];
                f(null, ((byte) KeyEvent.getModifierMetaStateMask()) + ByteCompanionObject.MIN_VALUE, null, "\u008d\u0088\u0085\u0090\u0093\u0085\u0086\u0092\u0091\u0082\u008d", objArr17);
                o.er.i c9 = c(((String) objArr17[0]).intern(), bVar);
                Object[] objArr18 = new Object[1];
                j(new int[]{-814120047, 724888225, 1085257357, 1354652778, 886246121, 2145379813, -1634686476, -701980611, 1437892022, -520629108, 1295913012, -1389753791}, ImageFormat.getBitsPerPixel(0) + 22, objArr18);
                o.er.i c10 = c(((String) objArr18[0]).intern(), bVar);
                Object[] objArr19 = new Object[1];
                f(null, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 127, null, "\u0084\u0094\u008b\u009c\u008b\u0083\u0085\u0094\u0085\u009b\u0094\u008b\u0099\u0089\u0084", objArr19);
                obj = new o.er.b(str, c9, c10, a(((String) objArr19[0]).intern(), bVar), a(bVar));
            }
        }
        List<o.el.e> singletonList = Collections.singletonList(obj);
        int i3 = h + 73;
        i = i3 % 128;
        switch (i3 % 2 != 0 ? 'P' : 'E') {
            case 'P':
                int i4 = 89 / 0;
                return singletonList;
            default:
                return singletonList;
        }
    }

    private static o.er.e b(String str) {
        o.er.e eVar = new o.er.e(str);
        int i2 = h + 33;
        i = i2 % 128;
        int i3 = i2 % 2;
        return eVar;
    }

    private static o.er.i c(String str, o.eg.b bVar) throws i {
        try {
            g.c();
            Object[] objArr = new Object[1];
            f(null, 127 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), null, "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            j(new int[]{-195485440, 1806844076, -251451302, -767684623, 544821101, 740712895, 1784338009, 158243397, 1927642643, -1964453610, -1405860091, 1924098351, 1036685907, -1679080049}, 26 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
            o.eg.b v = bVar.v(str);
            Object[] objArr3 = new Object[1];
            j(new int[]{-1974970082, 1811659633, 1760123784, 858593993, -1540380720, 264528389}, 9 - KeyEvent.getDeadChar(0, 0), objArr3);
            boolean booleanValue = v.g(((String) objArr3[0]).intern()).booleanValue();
            Object[] objArr4 = new Object[1];
            f(null, ((Process.getThreadPriority(0) + 20) >> 6) + 127, null, "\u0094\u0088\u008b\u0084\u0084\u0085\u0092", objArr4);
            o.er.i iVar = new o.er.i(booleanValue, v.q(((String) objArr4[0]).intern()));
            int i2 = h + 75;
            i = i2 % 128;
            int i3 = i2 % 2;
            return iVar;
        } catch (d e) {
            throw new i(e.getMessage());
        }
    }

    private static f a(String str, o.eg.b bVar) throws i {
        try {
            g.c();
            Object[] objArr = new Object[1];
            f(null, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 127, null, "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            j(new int[]{-195485440, 1806844076, 1090640212, 1408028665, 1782582651, 1091664347, 562673649, 1972615692, 890060214, -1939979944, 1784338009, 158243397, 1927642643, -1964453610, -1405860091, 1924098351, 1036685907, -1679080049}, TextUtils.indexOf((CharSequence) "", '0', 0) + 36, objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
            o.eg.b v = bVar.v(str);
            Object[] objArr3 = new Object[1];
            j(new int[]{-1974970082, 1811659633, 1760123784, 858593993, -1540380720, 264528389}, 9 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
            boolean booleanValue = v.g(((String) objArr3[0]).intern()).booleanValue();
            Object[] objArr4 = new Object[1];
            f(null, (ViewConfiguration.getTouchSlop() >> 8) + 127, null, "\u0094\u0088\u008b\u0084\u0084\u0085\u0087\u008b\u009c\u0095\u0091\u008b\u0088", objArr4);
            f fVar = new f(booleanValue, v.q(((String) objArr4[0]).intern()));
            int i2 = h + 73;
            i = i2 % 128;
            switch (i2 % 2 != 0 ? ' ' : '`') {
                case Opcodes.IADD /* 96 */:
                    return fVar;
                default:
                    throw null;
            }
        } catch (d e) {
            throw new i(e.getMessage());
        }
    }

    private static c b(String str, o.eg.b bVar) throws i {
        try {
            g.c();
            Object[] objArr = new Object[1];
            Object obj = null;
            f(null, 127 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), null, "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            f(null, ExpandableListView.getPackedPositionType(0L) + 127, null, "\u008e\u008f\u008e\u0094\u0089\u0082\u0084\u0092\u009f\u008b\u0088\u0095\u0084\u0085\u008b£\u0091\u009c\u0090\u008d\u0085\u008b\u0088", objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
            o.eg.b v = bVar.v(str);
            Object[] objArr3 = new Object[1];
            j(new int[]{-1974970082, 1811659633, 1760123784, 858593993, -1540380720, 264528389}, 9 - Drawable.resolveOpacity(0, 0), objArr3);
            boolean booleanValue = v.g(((String) objArr3[0]).intern()).booleanValue();
            Object[] objArr4 = new Object[1];
            f(null, 127 - TextUtils.indexOf("", ""), null, "\u0094\u0088\u008b\u0084\u0084\u0085\u0092", objArr4);
            String q = v.q(((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            j(new int[]{-1425103529, -2041076471, -400079353, 2104836481}, TextUtils.lastIndexOf("", '0', 0, 0) + 7, objArr5);
            c cVar = new c(booleanValue, q, v.b(((String) objArr5[0]).intern(), Boolean.FALSE).booleanValue());
            int i2 = h + 97;
            i = i2 % 128;
            switch (i2 % 2 != 0 ? '+' : '\'') {
                case '\'':
                    return cVar;
                default:
                    obj.hashCode();
                    throw null;
            }
        } catch (d e) {
            throw new i(e.getMessage());
        }
    }

    private static w e(String str, o.eg.b bVar) throws i {
        try {
            g.c();
            Object[] objArr = new Object[1];
            f(null, (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 127, null, "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            j(new int[]{-195485440, 1806844076, -251451302, -767684623, 544821101, 740712895, 1784338009, 158243397, 1927642643, -1964453610, -1405860091, 1924098351, 1036685907, -1679080049}, 27 - TextUtils.getOffsetBefore("", 0), objArr2);
            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
            o.eg.b v = bVar.v(str);
            List<o.es.b> c2 = c(v);
            Object[] objArr3 = new Object[1];
            j(new int[]{-1974970082, 1811659633, 1760123784, 858593993, -1540380720, 264528389}, 9 - KeyEvent.keyCodeFromString(""), objArr3);
            boolean booleanValue = v.g(((String) objArr3[0]).intern()).booleanValue();
            Object[] objArr4 = new Object[1];
            f(null, 127 - TextUtils.getTrimmedLength(""), null, "\u0094\u0088\u008b\u0084\u0084\u0085\u0092", objArr4);
            w wVar = new w(booleanValue, v.q(((String) objArr4[0]).intern()), c2);
            int i2 = i + 45;
            h = i2 % 128;
            int i3 = i2 % 2;
            return wVar;
        } catch (d e) {
            throw new i(e.getMessage());
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x00cd, code lost:
    
        return b(r4);
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.eo.a a(o.eg.b r8) throws o.ei.i {
        /*
            int r0 = o.ch.b.i
            int r0 = r0 + 5
            int r1 = r0 % 128
            o.ch.b.h = r1
            int r0 = r0 % 2
            if (r0 != 0) goto Lf
            r0 = 97
            goto L11
        Lf:
            r0 = 84
        L11:
            r1 = 16777235(0x1000013, float:2.350994E-38)
            r2 = 10
            java.lang.String r3 = "\u0088\u008b\u008d\u0085\u008b\u008c\u008b\u0086\u0082\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081"
            r4 = 0
            r5 = 1
            r6 = 0
            switch(r0) {
                case 97: goto L22;
                default: goto L1e;
            }
        L1e:
            o.ee.g.c()     // Catch: o.eg.d -> Lce
            goto L5d
        L22:
            o.ee.g.c()     // Catch: o.eg.d -> Lce
            int r0 = android.view.Gravity.getAbsoluteGravity(r5, r6)     // Catch: o.eg.d -> Lce
            int r0 = r0 * 51
            java.lang.Object[] r7 = new java.lang.Object[r5]     // Catch: o.eg.d -> Lce
            f(r4, r0, r4, r3, r7)     // Catch: o.eg.d -> Lce
            r0 = r7[r6]     // Catch: o.eg.d -> Lce
            java.lang.String r0 = (java.lang.String) r0     // Catch: o.eg.d -> Lce
            java.lang.String r0 = r0.intern()     // Catch: o.eg.d -> Lce
            int[] r2 = new int[r2]     // Catch: o.eg.d -> Lce
            r2 = {x00f2: FILL_ARRAY_DATA , data: [-195485440, 1806844076, -170778646, 1781902330, -321674329, -1792910398, -3587068, 653201454, 501649518, -737885498} // fill-array     // Catch: o.eg.d -> Lce
            int r3 = android.graphics.Color.rgb(r5, r5, r5)     // Catch: o.eg.d -> Lce
            int r1 = r1 % r3
            java.lang.Object[] r3 = new java.lang.Object[r5]     // Catch: o.eg.d -> Lce
            j(r2, r1, r3)     // Catch: o.eg.d -> Lce
            r1 = r3[r6]     // Catch: o.eg.d -> Lce
            java.lang.String r1 = (java.lang.String) r1     // Catch: o.eg.d -> Lce
            java.lang.String r1 = r1.intern()     // Catch: o.eg.d -> Lce
            o.ee.g.d(r0, r1)     // Catch: o.eg.d -> Lce
            if (r8 == 0) goto L57
            r0 = 39
            goto L59
        L57:
            r0 = 76
        L59:
            switch(r0) {
                case 76: goto L93;
                default: goto L5c;
            }     // Catch: o.eg.d -> Lce
        L5c:
            goto L92
        L5d:
            int r0 = android.view.Gravity.getAbsoluteGravity(r6, r6)     // Catch: o.eg.d -> Lce
            int r0 = r0 + 127
            java.lang.Object[] r7 = new java.lang.Object[r5]     // Catch: o.eg.d -> Lce
            f(r4, r0, r4, r3, r7)     // Catch: o.eg.d -> Lce
            r0 = r7[r6]     // Catch: o.eg.d -> Lce
            java.lang.String r0 = (java.lang.String) r0     // Catch: o.eg.d -> Lce
            java.lang.String r0 = r0.intern()     // Catch: o.eg.d -> Lce
            int[] r2 = new int[r2]     // Catch: o.eg.d -> Lce
            r2 = {x010a: FILL_ARRAY_DATA , data: [-195485440, 1806844076, -170778646, 1781902330, -321674329, -1792910398, -3587068, 653201454, 501649518, -737885498} // fill-array     // Catch: o.eg.d -> Lce
            int r3 = android.graphics.Color.rgb(r6, r6, r6)     // Catch: o.eg.d -> Lce
            int r3 = r3 + r1
            java.lang.Object[] r1 = new java.lang.Object[r5]     // Catch: o.eg.d -> Lce
            j(r2, r3, r1)     // Catch: o.eg.d -> Lce
            r1 = r1[r6]     // Catch: o.eg.d -> Lce
            java.lang.String r1 = (java.lang.String) r1     // Catch: o.eg.d -> Lce
            java.lang.String r1 = r1.intern()     // Catch: o.eg.d -> Lce
            o.ee.g.d(r0, r1)     // Catch: o.eg.d -> Lce
            if (r8 == 0) goto L8e
            r0 = r5
            goto L8f
        L8e:
            r0 = r6
        L8f:
            switch(r0) {
                case 0: goto L93;
                default: goto L92;
            }
        L92:
            goto La8
        L93:
            int r8 = o.ch.b.i
            int r8 = r8 + 31
            int r0 = r8 % 128
            o.ch.b.h = r0
            int r8 = r8 % 2
            if (r8 != 0) goto La2
            r8 = 80
            goto La4
        La2:
            r8 = 53
        La4:
            switch(r8) {
                case 53: goto La7;
                default: goto La7;
            }
        La7:
            goto Lc9
        La8:
            r0 = 8
            int[] r0 = new int[r0]     // Catch: o.eg.d -> Lce
            r0 = {x0122: FILL_ARRAY_DATA , data: [-560419520, -19531435, -321674329, -1792910398, -3587068, 653201454, 501649518, -737885498} // fill-array     // Catch: o.eg.d -> Lce
            float r1 = android.media.AudioTrack.getMinVolume()     // Catch: o.eg.d -> Lce
            r2 = 0
            int r1 = (r1 > r2 ? 1 : (r1 == r2 ? 0 : -1))
            int r1 = 15 - r1
            java.lang.Object[] r2 = new java.lang.Object[r5]     // Catch: o.eg.d -> Lce
            j(r0, r1, r2)     // Catch: o.eg.d -> Lce
            r0 = r2[r6]     // Catch: o.eg.d -> Lce
            java.lang.String r0 = (java.lang.String) r0     // Catch: o.eg.d -> Lce
            java.lang.String r0 = r0.intern()     // Catch: o.eg.d -> Lce
            o.eg.b r4 = r8.u(r0)     // Catch: o.eg.d -> Lce
        Lc9:
            o.eo.a r8 = b(r4)     // Catch: o.eg.d -> Lce
            return r8
        Lce:
            r8 = move-exception
            o.ei.i r0 = new o.ei.i
            java.lang.String r8 = r8.getMessage()
            r0.<init>(r8)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ch.b.a(o.eg.b):o.eo.a");
    }

    private static List<o.es.b> c(o.eg.b bVar) {
        Boolean h2;
        Object[] objArr = new Object[1];
        j(new int[]{-1682186947, -1963603233, 85553037, 1687905943}, 9 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        j(new int[]{1236213886, -1030995867, -8601237, 1989803831}, 7 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        ArrayList arrayList = new ArrayList();
        Object[] objArr3 = new Object[1];
        Object obj = null;
        f(null, 127 - Gravity.getAbsoluteGravity(0, 0), null, "\u0091\u008b\u0086\u0095\u0088", objArr3);
        o.eg.b u = bVar.u(((String) objArr3[0]).intern());
        if (u != null) {
            Object[] objArr4 = new Object[1];
            f(null, 128 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), null, "\u0086\u0085¤\u0089\u0086\u0083", objArr4);
            o.eg.b u2 = u.u(((String) objArr4[0]).intern());
            if (u2 != null) {
                Boolean h3 = u2.h(intern);
                if (h3 != null && h3.booleanValue()) {
                    arrayList.add(o.es.b.b);
                    int i2 = h + 27;
                    i = i2 % 128;
                    int i3 = i2 % 2;
                }
                Object[] objArr5 = new Object[1];
                j(new int[]{-2065325730, -1165370647, 327579073, 1439603451, -256398662, -2009175649, 2025284309, -155766815}, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 16, objArr5);
                Boolean h4 = u2.h(((String) objArr5[0]).intern());
                if (h4 != null && h4.booleanValue()) {
                    arrayList.add(o.es.b.c);
                }
                Object[] objArr6 = new Object[1];
                f(null, 127 - Drawable.resolveOpacity(0, 0), null, "\u008d\u0086\u0089\u0096\u0091\u008b\u0088\u0096\u0097\u0084\u0088\u008b\u0086\u0085", objArr6);
                Boolean h5 = u2.h(((String) objArr6[0]).intern());
                if (h5 != null && h5.booleanValue()) {
                    arrayList.add(o.es.b.a);
                }
                Object[] objArr7 = new Object[1];
                j(new int[]{1781013484, 2057636032, -2029852916, -1486018500, -596165710, -978268661, 2120588401, 836300843}, 13 - KeyEvent.keyCodeFromString(""), objArr7);
                Boolean h6 = u2.h(((String) objArr7[0]).intern());
                if (h6 != null) {
                    switch (h6.booleanValue() ? '0' : '2') {
                        case '0':
                            arrayList.add(o.es.b.d);
                            break;
                    }
                }
            }
            Object[] objArr8 = new Object[1];
            f(null, (ViewConfiguration.getJumpTapTimeout() >> 16) + 127, null, "\u008b\u0092\u0093\u0097\u0094\u0089\u0082\u0084\u009e\u0085\u0091\u0094\u0085\u0088\u0084", objArr8);
            o.eg.b u3 = u.u(((String) objArr8[0]).intern());
            switch (u3 != null ? (char) 28 : '6') {
                default:
                    Object[] objArr9 = new Object[1];
                    j(new int[]{-1763498780, -1828078181}, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 2, objArr9);
                    o.eg.b u4 = u3.u(((String) objArr9[0]).intern());
                    if (u4 != null) {
                        Boolean h7 = u4.h(intern);
                        if (h7 != null && h7.booleanValue()) {
                            arrayList.add(o.es.b.e);
                        }
                        Boolean h8 = u4.h(intern2);
                        if (h8 != null && h8.booleanValue()) {
                            arrayList.add(o.es.b.f);
                        }
                    }
                    Object[] objArr10 = new Object[1];
                    j(new int[]{-1490914424, 1446052984, 115030794, 1707835665, 64362055, -817285925}, View.MeasureSpec.getMode(0) + 9, objArr10);
                    o.eg.b u5 = u3.u(((String) objArr10[0]).intern());
                    if (u5 != null) {
                        int i4 = h + 39;
                        i = i4 % 128;
                        if (i4 % 2 != 0) {
                            u5.h(intern);
                            throw null;
                        }
                        Boolean h9 = u5.h(intern);
                        if (h9 != null && h9.booleanValue()) {
                            arrayList.add(o.es.b.j);
                        }
                        Boolean h10 = u5.h(intern2);
                        if (h10 != null && h10.booleanValue()) {
                            arrayList.add(o.es.b.g);
                        }
                    }
                    Object[] objArr11 = new Object[1];
                    j(new int[]{532291506, -1253698648, -1593136131, 238635099, -259751979, 292145741, -304859680, 954693872}, 13 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr11);
                    o.eg.b u6 = u3.u(((String) objArr11[0]).intern());
                    if (u6 != null) {
                        Boolean h11 = u6.h(intern);
                        if (h11 != null) {
                            int i5 = h + 67;
                            i = i5 % 128;
                            int i6 = i5 % 2;
                            if (h11.booleanValue()) {
                                arrayList.add(o.es.b.i);
                            }
                        }
                        Boolean h12 = u6.h(intern2);
                        if (h12 != null) {
                            int i7 = i + 79;
                            h = i7 % 128;
                            if (i7 % 2 == 0) {
                                h12.booleanValue();
                                obj.hashCode();
                                throw null;
                            }
                            if (h12.booleanValue()) {
                                arrayList.add(o.es.b.h);
                            }
                        }
                    }
                    Object[] objArr12 = new Object[1];
                    j(new int[]{1295962003, -1487108654, -1069468677, -1193486551, -15608107, -573087642}, 11 - View.getDefaultSize(0, 0), objArr12);
                    o.eg.b u7 = u3.u(((String) objArr12[0]).intern());
                    if (u7 != null) {
                        int i8 = h + 55;
                        i = i8 % 128;
                        int i9 = i8 % 2;
                        Boolean h13 = u7.h(intern);
                        if (h13 != null && h13.booleanValue()) {
                            arrayList.add(o.es.b.l);
                        }
                        Boolean h14 = u7.h(intern2);
                        if (h14 != null && h14.booleanValue()) {
                            arrayList.add(o.es.b.n);
                        }
                    }
                    Object[] objArr13 = new Object[1];
                    j(new int[]{-560419520, -19531435, 1653425529, -1148925951, -1679595441, -1759410331}, 8 - MotionEvent.axisFromString(""), objArr13);
                    o.eg.b u8 = u3.u(((String) objArr13[0]).intern());
                    if (u8 != null) {
                        int i10 = i + Opcodes.DREM;
                        h = i10 % 128;
                        if (i10 % 2 == 0) {
                            u8.h(intern);
                            obj.hashCode();
                            throw null;
                        }
                        Boolean h15 = u8.h(intern);
                        if (h15 != null && h15.booleanValue()) {
                            arrayList.add(o.es.b.f78o);
                        }
                        Boolean h16 = u8.h(intern2);
                        if (h16 != null && h16.booleanValue()) {
                            arrayList.add(o.es.b.m);
                        }
                    }
                    Object[] objArr14 = new Object[1];
                    f(null, 127 - (ViewConfiguration.getLongPressTimeout() >> 16), null, "\u0091\u0091\u008b\u0086\u0084\u009e\u0085\u0084\u0094\u0089\u009e", objArr14);
                    o.eg.b u9 = u3.u(((String) objArr14[0]).intern());
                    if (u9 != null) {
                        int i11 = i + 19;
                        h = i11 % 128;
                        if (i11 % 2 == 0) {
                            u9.h(intern);
                            obj.hashCode();
                            throw null;
                        }
                        Boolean h17 = u9.h(intern);
                        if (h17 != null && h17.booleanValue()) {
                            arrayList.add(o.es.b.k);
                        }
                        Boolean h18 = u9.h(intern2);
                        if (h18 != null && h18.booleanValue()) {
                            arrayList.add(o.es.b.t);
                        }
                    }
                    break;
                case Opcodes.ISTORE /* 54 */:
                    Object[] objArr15 = new Object[1];
                    j(new int[]{1787805910, 1485476731, 891926287, -1420836981, -584672572, -355906257}, Color.argb(0, 0, 0, 0) + 11, objArr15);
                    o.eg.b u10 = u.u(((String) objArr15[0]).intern());
                    switch (u10 == null) {
                        default:
                            Boolean h19 = u10.h(intern);
                            if (h19 != null && h19.booleanValue()) {
                                arrayList.add(o.es.b.p);
                            }
                            Boolean h20 = u10.h(intern2);
                            if (h20 != null && h20.booleanValue()) {
                                arrayList.add(o.es.b.r);
                            }
                            Object[] objArr16 = new Object[1];
                            f(null, Drawable.resolveOpacity(0, 0) + 127, null, "\u0084\u0091\u0082\u0086\u008b\u0084\u0082\u0096¥\u0091\u008b\u0082\u0088\u0084\u0094\u0095\u0089\u009e", objArr16);
                            Boolean h21 = u10.h(((String) objArr16[0]).intern());
                            if (h21 != null) {
                                switch (h21.booleanValue()) {
                                    case true:
                                        int i12 = i + Opcodes.DSUB;
                                        h = i12 % 128;
                                        if (i12 % 2 == 0) {
                                            arrayList.add(o.es.b.s);
                                            obj.hashCode();
                                            throw null;
                                        }
                                        arrayList.add(o.es.b.s);
                                    default:
                                        Object[] objArr17 = new Object[1];
                                        f(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u0084\u0091\u0082\u0086\u008b\u0084\u0082\u0096¥\u0091\u008b\u0084\u0085\u0084\u0091", objArr17);
                                        h2 = u10.h(((String) objArr17[0]).intern());
                                        if (h2 != null && h2.booleanValue()) {
                                            arrayList.add(o.es.b.q);
                                        }
                                        break;
                                }
                            }
                            Object[] objArr172 = new Object[1];
                            f(null, 126 - TextUtils.lastIndexOf("", '0'), null, "\u0084\u0091\u0082\u0086\u008b\u0084\u0082\u0096¥\u0091\u008b\u0084\u0085\u0084\u0091", objArr172);
                            h2 = u10.h(((String) objArr172[0]).intern());
                            if (h2 != null) {
                                arrayList.add(o.es.b.q);
                            }
                            break;
                        case true:
                            Object[] objArr18 = new Object[1];
                            f(null, 127 - ExpandableListView.getPackedPositionType(0L), null, "\u008b\u0092\u0093\u0097\u0084\u0094\u0085\u0096\u009e\u0088\u008b\u009c", objArr18);
                            o.eg.b u11 = u.u(((String) objArr18[0]).intern());
                            if (u11 != null) {
                                Boolean h22 = u11.h(intern);
                                if (h22 != null) {
                                    switch (!h22.booleanValue()) {
                                        case true:
                                            break;
                                        default:
                                            arrayList.add(o.es.b.w);
                                            break;
                                    }
                                }
                                Boolean h23 = u11.h(intern2);
                                if (h23 != null && h23.booleanValue()) {
                                    int i13 = i + 3;
                                    h = i13 % 128;
                                    int i14 = i13 % 2;
                                    arrayList.add(o.es.b.u);
                                }
                                Object[] objArr19 = new Object[1];
                                j(new int[]{-1384975863, 447657268, 250213297, -821032130}, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 5, objArr19);
                                Boolean h24 = u11.h(((String) objArr19[0]).intern());
                                switch (h24 != null ? 'E' : '\t') {
                                    case 'E':
                                        if (h24.booleanValue()) {
                                            arrayList.add(o.es.b.y);
                                            break;
                                        }
                                        break;
                                }
                            }
                            Object[] objArr20 = new Object[1];
                            j(new int[]{1044654936, 183674710, 720792367, -1822748970}, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 7, objArr20);
                            o.eg.b u12 = u.u(((String) objArr20[0]).intern());
                            switch (u12 != null) {
                                default:
                                    Object[] objArr21 = new Object[1];
                                    f(null, 126 - ExpandableListView.getPackedPositionChild(0L), null, "\u008d\u0089\u0082\u0088\u008b\u0092", objArr21);
                                    Boolean h25 = u12.h(((String) objArr21[0]).intern());
                                    switch (h25 != null ? '\f' : '\r') {
                                        case '\r':
                                            break;
                                        default:
                                            switch (h25.booleanValue()) {
                                                case false:
                                                    break;
                                                default:
                                                    arrayList.add(o.es.b.x);
                                                    break;
                                            }
                                    }
                                    Object[] objArr22 = new Object[1];
                                    j(new int[]{-1682186947, -1963603233, 1049080958, 15090858, -1243748179, -1232636464, 2117940408, -1973584136}, 13 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr22);
                                    Boolean h26 = u12.h(((String) objArr22[0]).intern());
                                    if (h26 != null) {
                                        switch (h26.booleanValue()) {
                                            case false:
                                                break;
                                            default:
                                                arrayList.add(o.es.b.v);
                                                break;
                                        }
                                    }
                                    Object[] objArr23 = new Object[1];
                                    f(null, TextUtils.lastIndexOf("", '0', 0, 0) + 128, null, "\u008d\u0086\u0089\u0096\u0091\u008b\u0088\u0096\u0097\u0084\u0088\u008b\u0086\u0085", objArr23);
                                    Boolean h27 = u12.h(((String) objArr23[0]).intern());
                                    if (h27 != null && h27.booleanValue()) {
                                        int i15 = h + 93;
                                        i = i15 % 128;
                                        int i16 = i15 % 2;
                                        arrayList.add(o.es.b.A);
                                    }
                                    Object[] objArr24 = new Object[1];
                                    j(new int[]{1109453844, 1743521577, -855922251, -24987297}, (-16777208) - Color.rgb(0, 0, 0), objArr24);
                                    Boolean h28 = u12.h(((String) objArr24[0]).intern());
                                    if (h28 != null && h28.booleanValue()) {
                                        arrayList.add(o.es.b.C);
                                    }
                                    break;
                                case false:
                                    return arrayList;
                            }
                    }
            }
        }
        return arrayList;
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x008c, code lost:
    
        r9 = r7;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.eo.a b(o.eg.b r15) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 578
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ch.b.b(o.eg.b):o.eo.a");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 862
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ch.b.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(int[] r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 1002
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ch.b.j(int[], int, java.lang.Object[]):void");
    }
}
