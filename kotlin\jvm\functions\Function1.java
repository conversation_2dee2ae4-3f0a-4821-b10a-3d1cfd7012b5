package kotlin.jvm.functions;

import kotlin.Function;
import kotlin.Metadata;

/* compiled from: Functions.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\bf\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u0000*\u0006\b\u0001\u0010\u0002 \u00012\b\u0012\u0004\u0012\u0002H\u00020\u0003J\u0016\u0010\u0004\u001a\u00028\u00012\u0006\u0010\u0005\u001a\u00028\u0000H¦\u0002¢\u0006\u0002\u0010\u0006¨\u0006\u0007"}, d2 = {"Lkotlin/jvm/functions/Function1;", "P1", "R", "Lkotlin/Function;", "invoke", "p1", "(Ljava/lang/Object;)Ljava/lang/Object;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\functions\Function1.smali */
public interface Function1<P1, R> extends Function<R> {
    R invoke(P1 p1);
}
