package o.cn;

import android.graphics.Color;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.dk.e;
import o.et.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cn\b.smali */
public final class b extends o.cq.d<i> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static boolean b;
    private static int c;
    private static boolean d;
    private static char[] e;
    private static int f;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        e();
        int i = f + 87;
        h = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        e = new char[]{51160, 51145, 50731, 51166, 51137, 51142, 51147, 51165, 51141, 51138, 51137, 51140, 51167, 51160, 50750, 51146, 50941, 50855, 50855, 50847, 50836, 50851, 50851, 50848, 50851, 50852, 51191, 51172, 51193, 51194, 51192, 51175, 51192, 51187, 51156, 51188, 51173, 50849, 50709, 50717, 50932, 50865, 50865, 50857, 50851, 50766, 50870, 50870, 50760, 50866, 50855, 50878, 50760, 50867, 50871, 50767, 50864, 50873, 50865, 50766, 50765, 50870, 50943, 50859, 50856, 50933, 50876, 50849, 50840, 50841, 50859, 50849, 50828, 50753, 50768, 50799, 50797, 50770, 50796, 50768, 50778, 50769, 50797, 50939, 50853, 50857, 50852, 50835, 50832, 50876, 50849, 50937, 50851, 50862, 50862, 50852, 50858, 50877, 50851, 50851, 50862, 50857, 50879, 50877, 50850, 50877, 50878, 50849, 50857, 50937, 50851, 50857, 50857, 50839, 50840, 50849, 50835, 50842, 50855, 50850, 50855, 50854, 50855, 50832, 50857, 50879, 50877, 50850, 50877, 50878, 50849, 50857, 50929, 50875, 50851, 50848, 50877, 50849, 50848, 50876, 50821, 50826, 50851, 50849, 50851, 50855, 50863, 50859, 50855, 50851, 50877, 50853, 50829, 50831, 50859, 50855, 50859, 50854, 50879, 50854, 50939, 50853, 50838, 50838, 50862, 50863, 50857, 50854, 50834, 50837, 50857, 50849, 50847, 50940, 50829, 50860, 50877, 50855, 50858, 50851, 50731, 50725, 50803, 50801, 50729, 50721, 50727, 50731, 50735, 50707, 50731, 50727, 50725, 50727, 50702, 50801, 50728, 50730, 50803, 50696, 50725, 50725, 50689, 50799, 50801, 50735, 50721, 50725, 50730, 50721, 50734, 50731, 50725, 50803, 50801, 50729, 50721, 50727, 50731, 50735, 50707, 50731, 50727, 50725, 50727, 50702, 50800, 50707, 50734, 50727, 50727, 50720, 50699, 50800, 50706, 50803, 50800, 50730, 50726, 50731, 50721, 50722, 50730, 50706, 50707, 50715, 50704, 50721, 50731, 50734, 50731, 50729, 50803, 50799, 50799, 50702, 50727, 50720, 50699, 50801, 50735, 50725, 50703, 50699, 50727, 50735, 50803, 50802, 50734, 50734, 50729, 50729, 50730, 50696, 50702, 50731, 50802, 50699, 50727, 50735, 50803, 50801, 50735, 50721, 50725, 50730, 50721, 50823, 50753, 50753, 50780, 50759, 50760, 50753, 50936, 50853, 50858};
        a = new char[]{61569, 61572, 61574, 61585, 61595, 61568, 61794, 61820, 61587, 61796, 61570, 61582, 61584, 61583, 61588, 61809, 61581, 61586, 61811, 61580, 61576, 61799, 61577, 61772, 61793, 61801, 61573, 61591, 61757, 61575, 61788, 61798, 61778, 61578, 61779, 61776, 61800, 61805};
        d = true;
        b = true;
        c = 782102813;
    }

    static void init$0() {
        $$a = new byte[]{62, -87, 120, -83};
        $$b = 34;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 66
            byte[] r0 = o.cn.b.$$a
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r8 = r8 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L33
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r8 = r8 + 1
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.b.m(short, int, short, java.lang.Object[]):void");
    }

    @Override // o.cq.d
    public final /* bridge */ /* synthetic */ i b(i iVar, o.eg.b bVar, int i, String str, o.eg.b bVar2) throws o.eg.d, o.ei.i {
        int i2 = f + 59;
        h = i2 % 128;
        int i3 = i2 % 2;
        i b2 = b2(iVar, bVar, i, str, bVar2);
        int i4 = h + Opcodes.DSUB;
        f = i4 % 128;
        int i5 = i4 % 2;
        return b2;
    }

    @Override // o.cq.d
    public final /* synthetic */ i c(String str, String str2, int i, String str3, o.eg.b bVar) throws o.eg.d, o.ei.i {
        int i2 = f + Opcodes.DREM;
        h = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                i b2 = b(str, str2, i, str3, bVar);
                int i3 = f + Opcodes.LSUB;
                h = i3 % 128;
                switch (i3 % 2 == 0 ? 'a' : '\t') {
                    case Opcodes.LADD /* 97 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return b2;
                }
            default:
                b(str, str2, i, str3, bVar);
                throw null;
        }
    }

    @Override // o.cc.e
    public final /* synthetic */ o.el.d d(String str, String str2, int i, String str3) {
        int i2 = f + 11;
        h = i2 % 128;
        int i3 = i2 % 2;
        i a2 = a(str, str2, i, str3);
        int i4 = h + 55;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return a2;
            default:
                throw null;
        }
    }

    private static i b(String str, String str2, int i, String str3, o.eg.b bVar) throws o.eg.d, o.ei.i {
        int intValue;
        i iVar = new i(str, str2, i, str3);
        Object[] objArr = new Object[1];
        g(null, new int[]{0, 16, Opcodes.IF_ICMPGT, 8}, true, objArr);
        o.eg.b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0000", new int[]{16, 10, 0, 0}, true, objArr2);
        o.eg.b v2 = v.v(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l(null, View.getDefaultSize(0, 0) + 127, null, "\u0081\u008a\u0081\u0089\u0088\u0087\u0081\u0086\u0085\u0082\u0084\u0082\u0083\u0082\u0081", objArr3);
        iVar.i(v2.r(((String) objArr3[0]).intern()));
        Object[] objArr4 = new Object[1];
        l(null, (ViewConfiguration.getJumpTapTimeout() >> 16) + 127, null, "\u0086\u0081\u008c\u0087\u008f\u0089\u0084\u008e\u008d\u008c\u0087\u0081\u0089\u0088\u008b", objArr4);
        iVar.i(v2.B(((String) objArr4[0]).intern()));
        Object[] objArr5 = new Object[1];
        g(null, new int[]{26, 11, Opcodes.MULTIANEWARRAY, 5}, true, objArr5);
        i.c.valueOf(v2.r(((String) objArr5[0]).intern()));
        Object[] objArr6 = new Object[1];
        l(null, TextUtils.indexOf((CharSequence) "", '0', 0) + 128, null, "\u0086\u0091\u008f\u0090\u0084\u008e\u008d\u008c\u008b\u008b\u0088", objArr6);
        i.d.valueOf(v2.r(((String) objArr6[0]).intern()));
        Object[] objArr7 = new Object[1];
        l(null, Gravity.getAbsoluteGravity(0, 0) + 127, null, "\u0081\u0086\u0089\u0082\u008d\u0094\u0086\u0093\u0081\u008a\u008e\u008c\u0082\u0084\u008b\u0088\u0092\u008e\u0088\u0089\u0090\u0092\u0082", objArr7);
        v2.g(((String) objArr7[0]).intern());
        Object[] objArr8 = new Object[1];
        g("\u0001\u0000\u0001", new int[]{37, 3, Opcodes.LMUL, 1}, true, objArr8);
        iVar.j(v2.r(((String) objArr8[0]).intern()));
        Object[] objArr9 = new Object[1];
        g("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{40, 22, 18, 0}, true, objArr9);
        o.eg.b v3 = v.v(((String) objArr9[0]).intern());
        Object[] objArr10 = new Object[1];
        g("\u0000\u0001\u0000", new int[]{62, 3, 0, 0}, true, objArr10);
        iVar.a(v3.B(((String) objArr10[0]).intern()));
        byte[] j = iVar.j();
        Object[] objArr11 = new Object[1];
        g("\u0000\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{65, 7, 0, 6}, false, objArr11);
        iVar.b(e.a(j, v3.B(((String) objArr11[0]).intern())));
        Object[] objArr12 = new Object[1];
        l(null, View.resolveSize(0, 0) + 127, null, "\u0082\u008b\u0096\u0084\u008e\u0086\u0095\u008f\u0088\u0091", objArr12);
        iVar.e(v3.B(((String) objArr12[0]).intern()));
        Object[] objArr13 = new Object[1];
        g("\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{72, 11, 49, 8}, false, objArr13);
        iVar.c(v3.B(((String) objArr13[0]).intern()));
        Object[] objArr14 = new Object[1];
        l(null, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 128, null, "\u009b\u0084\u0083\u008e\u0086\u009a\u0088\u0084\u0088\u0099\u0081\u0086\u0084\u0088\u0097\u0086\u0093\u0098\u0097\u008c\u0081\u008b", objArr14);
        Object e2 = v3.e(((String) objArr14[0]).intern());
        switch (e2 instanceof String ? ',' : '\f') {
            case ',':
                try {
                    intValue = Integer.valueOf((String) e2, 16).intValue();
                    int i2 = h + 91;
                    f = i2 % 128;
                    int i3 = i2 % 2;
                    break;
                } catch (NumberFormatException e3) {
                    Object[] objArr15 = new Object[1];
                    l(null, Color.green(0) + 127, null, "\u0083\u008e\u0082\u0089\u0084\u0092\u009d\u0088\u009d\u0092\u0088\u009d\u0086\u008d\u0097\u0088\u009c\u009d\u009b\u0084\u0083\u008e\u0086\u0097\u009d\u0088\u0084\u0088\u0081\u009d\u0081\u0086\u0084\u0088\u0097\u0086\u0089\u009d\u0097\u008c\u0081\u008b\u009d\u0081\u0082\u0097\u0088\u009c\u008e\u0082", objArr15);
                    throw new o.ei.i(((String) objArr15[0]).intern());
                }
            default:
                switch (e2 instanceof Integer ? '6' : 'E') {
                    case Opcodes.ISTORE /* 54 */:
                        intValue = ((Integer) e2).intValue();
                        int i4 = f + 59;
                        h = i4 % 128;
                        int i5 = i4 % 2;
                        break;
                    default:
                        Object[] objArr16 = new Object[1];
                        l(null, 127 - Gravity.getAbsoluteGravity(0, 0), null, "\u0084\u0088\u0095\u0089\u008c\u009e\u009d\u0081\u0082\u0097\u0088\u009c\u008e\u0082\u009d\u008e\u0088\u009d\u0092\u0088\u009b\u009d\u009b\u0084\u0083\u008e\u0086\u0097\u009d\u0088\u0084\u0088\u0081\u009d\u0081\u0086\u0084\u0088\u0097\u0086\u0089\u009d\u0097\u008c\u0081\u008b\u009d\u0081\u0082\u0097\u0088\u009c\u008e\u0082", objArr16);
                        throw new o.ei.i(((String) objArr16[0]).intern());
                }
        }
        iVar.d(intValue);
        Object[] objArr17 = new Object[1];
        l(null, 126 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), null, "\u0088\u0084\u0088\u0099\u008e\u008c\u0082\u0084\u0088\u008b\u0082\u0097\u0091\u0091\u009f\u0089\u0086\u008d\u0092\u0092\u0082", objArr17);
        iVar.j(v3.B(((String) objArr17[0]).intern()));
        Object[] objArr18 = new Object[1];
        g("\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{83, 8, 0, 0}, true, objArr18);
        i.e.valueOf(v3.r(((String) objArr18[0]).intern()));
        Object[] objArr19 = new Object[1];
        l(null, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 127, null, "\u008e\u008c\u0082\u0084\u0088\u0089\u0086\u008e\u0086 \u0081\u0095\u008d", objArr19);
        i.a.valueOf(v3.r(((String) objArr19[0]).intern()));
        Object[] objArr20 = new Object[1];
        l(null, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.IAND, null, "£¢\u008b\u0088\u0089\u0090¡\u008b\u009c\u0087\u009c\u008a\u008e\u0082\u0091", objArr20);
        v3.B(((String) objArr20[0]).intern());
        Object[] objArr21 = new Object[1];
        g("\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{91, 18, 0, 0}, false, objArr21);
        v3.g(((String) objArr21[0]).intern());
        Object[] objArr22 = new Object[1];
        g("\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{109, 23, 0, 0}, false, objArr22);
        v3.g(((String) objArr22[0]).intern());
        short d2 = e.d(iVar.j(), iVar.B());
        if (d2 == 0) {
            Object[] objArr23 = new Object[1];
            g("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001", new int[]{Opcodes.IINC, 28, 0, 0}, true, objArr23);
            throw new o.ei.i(((String) objArr23[0]).intern());
        }
        iVar.e(d2);
        byte[] C = iVar.C();
        Object[] objArr24 = new Object[1];
        l(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "\u0099£\u0096¤", objArr24);
        iVar.d(o.ej.d.b(C, ((String) objArr24[0]).intern()));
        return iVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x00a4, code lost:
    
        if (r5 != null) goto L18;
     */
    /* renamed from: b, reason: avoid collision after fix types in other method */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.et.i b2(o.et.i r15, o.eg.b r16, int r17, java.lang.String r18, o.eg.b r19) throws o.eg.d, o.ei.i {
        /*
            Method dump skipped, instructions count: 502
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.b.b2(o.et.i, o.eg.b, int, java.lang.String, o.eg.b):o.et.i");
    }

    @Override // o.cq.d
    public final boolean b(int i, o.eg.b bVar) throws o.eg.d {
        switch (i == 0) {
            case true:
                Object[] objArr = new Object[1];
                g(null, new int[]{0, 16, Opcodes.IF_ICMPGT, 8}, true, objArr);
                o.eg.b v = bVar.v(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                g("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{40, 22, 18, 0}, true, objArr2);
                o.eg.b v2 = v.v(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                l(null, 127 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), null, "\u0088\u0084\u0088\u0099\u0084\u008e\u0086\u0095\u008f\u0088¦\u0092\u0092\u0086\u0097\u0084\u008b\u0088\u0084\u008e\u008c\u0087\u0086\u0084\u0088\u008e\u0089\u0086\u0084\u0097\u0088", objArr3);
                return v2.C(((String) objArr3[0]).intern());
            default:
                int i2 = h;
                int i3 = i2 + Opcodes.LSUB;
                f = i3 % 128;
                if (i3 % 2 != 0) {
                }
                int i4 = i2 + Opcodes.LNEG;
                f = i4 % 128;
                switch (i4 % 2 != 0 ? '\'' : '7') {
                    case '7':
                        return false;
                    default:
                        throw null;
                }
        }
    }

    @Override // o.cq.d
    public final o.eg.b c(int i, o.eg.b bVar) throws o.eg.d {
        if (i != 0) {
            return null;
        }
        Object[] objArr = new Object[1];
        g(null, new int[]{0, 16, Opcodes.IF_ICMPGT, 8}, true, objArr);
        o.eg.b v = bVar.v(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        g("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{40, 22, 18, 0}, true, objArr2);
        o.eg.b v2 = v.v(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l(null, 128 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), null, "\u0088\u0084\u0088\u0099\u0084\u008e\u0086\u0095\u008f\u0088¦\u0092\u0092\u0086\u0097\u0084\u008b\u0088\u0084\u008e\u008c\u0087\u0086\u0084\u0088\u008e\u0089\u0086\u0084\u0097\u0088", objArr3);
        switch (v2.C(((String) objArr3[0]).intern()) ? '\b' : ':') {
            case '\b':
                int i2 = h + 77;
                f = i2 % 128;
                int i3 = i2 % 2;
                Object[] objArr4 = new Object[1];
                l(null, Color.red(0) + 127, null, "\u0088\u0084\u0088\u0099\u0084\u008e\u0086\u0095\u008f\u0088¦\u0092\u0092\u0086\u0097\u0084\u008b\u0088\u0084\u008e\u008c\u0087\u0086\u0084\u0088\u008e\u0089\u0086\u0084\u0097\u0088", objArr4);
                o.eg.b v3 = v2.v(((String) objArr4[0]).intern());
                int i4 = f + 5;
                h = i4 % 128;
                switch (i4 % 2 == 0 ? 'C' : '`') {
                    case 'C':
                        throw null;
                    default:
                        return v3;
                }
            default:
                return null;
        }
    }

    @Override // o.cq.d
    public final o.eg.e b(o.eg.b bVar) throws o.eg.d {
        o.eg.b v;
        Object obj;
        int i = f + 61;
        h = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                g(null, new int[]{0, 16, Opcodes.IF_ICMPGT, 8}, true, objArr);
                o.eg.b v2 = bVar.v(((String) objArr[0]).intern());
                Object[] objArr2 = new Object[1];
                g("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{40, 22, 18, 0}, true, objArr2);
                v = v2.v(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                g("\u0000\u0001\u0000\u0001\u0000\u0000\u0001", new int[]{287, 7, 33, 0}, true, objArr3);
                obj = objArr3[0];
                break;
            default:
                Object[] objArr4 = new Object[1];
                g(null, new int[]{0, 16, Opcodes.IF_ICMPGT, 8}, true, objArr4);
                o.eg.b v3 = bVar.v(((String) objArr4[0]).intern());
                Object[] objArr5 = new Object[1];
                g("\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000", new int[]{40, 22, 18, 0}, true, objArr5);
                v = v3.v(((String) objArr5[0]).intern());
                Object[] objArr6 = new Object[1];
                g("\u0000\u0001\u0000\u0001\u0000\u0000\u0001", new int[]{287, 7, 33, 0}, true, objArr6);
                obj = objArr6[0];
                break;
        }
        return v.s(((String) obj).intern());
    }

    private static i a(String str, String str2, int i, String str3) {
        i iVar = new i(str, str2, i, str3);
        int i2 = h + 1;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 18 : '3') {
            case '3':
                return iVar;
            default:
                int i3 = 65 / 0;
                return iVar;
        }
    }

    @Override // o.cq.d
    public final boolean a() {
        int i = f + 13;
        h = i % 128;
        switch (i % 2 != 0) {
            case true:
                return true;
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v34, types: [byte[]] */
    private static void g(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 926
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.b.g(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 748
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cn.b.l(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
