package o.es;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.TimePeriod;
import java.math.BigDecimal;
import java.util.TimeZone;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\es\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b A;
    public static final b C;
    private static long D;
    private static char E;
    private static int F;
    private static char G;
    private static char H;
    private static char I;
    private static int L;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    public static final b e;
    public static final b f;
    public static final b g;
    public static final b h;
    public static final b i;
    public static final b j;
    public static final b k;
    public static final b l;
    public static final b m;
    public static final b n;

    /* renamed from: o, reason: collision with root package name */
    public static final b f78o;
    public static final b p;
    public static final b q;
    public static final b r;
    public static final b s;
    public static final b t;
    public static final b u;
    public static final b v;
    public static final b w;
    public static final b x;
    public static final b y;
    private static final /* synthetic */ b[] z;
    private final Class<?> B;

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void M(byte r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = 4 - r7
            byte[] r0 = o.es.b.$$a
            int r9 = r9 * 3
            int r9 = 71 - r9
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L1b:
            r3 = r2
        L1c:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = -r7
            int r7 = r7 + r10
            int r8 = r8 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.es.b.M(byte, byte, short, java.lang.Object[]):void");
    }

    static void d() {
        D = -3912419271586308950L;
        G = (char) 25535;
        E = (char) 53164;
        I = (char) 20652;
        H = (char) 58667;
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = Opcodes.IF_ICMPEQ;
    }

    private static /* synthetic */ b[] c() {
        int i2 = F + 45;
        int i3 = i2 % 128;
        L = i3;
        int i4 = i2 % 2;
        b[] bVarArr = {b, c, a, d, e, f, j, g, i, h, l, n, f78o, m, k, t, p, r, s, q, w, u, y, x, v, A, C};
        int i5 = i3 + 47;
        F = i5 % 128;
        int i6 = i5 % 2;
        return bVarArr;
    }

    public static b valueOf(String str) {
        int i2 = F + 93;
        L = i2 % 128;
        int i3 = i2 % 2;
        b bVar = (b) Enum.valueOf(b.class, str);
        int i4 = L + Opcodes.LSHR;
        F = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return bVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static b[] values() {
        b[] bVarArr;
        int i2 = L + 47;
        F = i2 % 128;
        switch (i2 % 2 != 0 ? ',' : '\'') {
            case ',':
                bVarArr = (b[]) z.clone();
                int i3 = 81 / 0;
                break;
            default:
                bVarArr = (b[]) z.clone();
                break;
        }
        int i4 = F + 27;
        L = i4 % 128;
        switch (i4 % 2 == 0 ? 'X' : 'N') {
            case 'N':
                return bVarArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        F = 0;
        L = 1;
        d();
        Object[] objArr = new Object[1];
        J("쇽솺䊄麝帠奫\ue6a5챖끈ಡ瓁帚∹苶싑 铀猙倪", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 1, objArr);
        b = new b(((String) objArr[0]).intern(), 0, Boolean.class);
        Object[] objArr2 = new Object[1];
        K("僟캩\ue54c자鍻콇\u2d2aሊ\ue0a3䠱䫦㡴薑\ud950䛕閍Ι妢꿗讖⾰ފ뱝십", (ViewConfiguration.getJumpTapTimeout() >> 16) + 23, objArr2);
        c = new b(((String) objArr2[0]).intern(), 1, BigDecimal.class);
        Object[] objArr3 = new Object[1];
        K("僟캩\ue54c자鍻콇霪榗陓骜\uecd9㍃䛕閍Ι妢꿗讖⾰ފ뱝십", Process.getGidForName("") + 22, objArr3);
        a = new b(((String) objArr3[0]).intern(), 2, BigDecimal.class);
        Object[] objArr4 = new Object[1];
        K("僟캩\ue54c자鍻콇璧䥙¢౬烪ぉ亘ᚶ\udb89\ud866ࢸ㐾蓺꣫", Drawable.resolveOpacity(0, 0) + 20, objArr4);
        d = new b(((String) objArr4[0]).intern(), 3, Boolean.class);
        Object[] objArr5 = new Object[1];
        J("뀊끞鸓䈔\ufff7赲䝼ᡃ솭퀻픪訵友幡挋\uf421\ue50f꾒\uf1f4柶眛㶂羀톂襬详ඖ䎒᪙\u193f鱹쵉", -TextUtils.indexOf((CharSequence) "", '0', 0), objArr5);
        e = new b(((String) objArr5[0]).intern(), 4, Boolean.class);
        Object[] objArr6 = new Object[1];
        K("ꆂ㊂✭\ue9fc\uf226Ꚕ⏆㖾䶥옓饙飜ᴛ煉辐覯㵽\ue4f8岩\ued5e鐎\ufbcbꅆ␙㵽萁\ud866뽯", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 27, objArr6);
        f = new b(((String) objArr6[0]).intern(), 5, Boolean.class);
        Object[] objArr7 = new Object[1];
        J("Ὶᾎ쏀ῇ䓥瞕ﱮ\ue2a4湽跨游烒ﰛβ\ud819ໆ䫟\uf241䫦鴑\ud8d8恠쒼⭏⚌혫뚂른땅䓲❅㞮\u0379㫢鄸엿鄖ꢹ", View.MeasureSpec.getMode(0) + 1, objArr7);
        j = new b(((String) objArr7[0]).intern(), 6, Boolean.class);
        Object[] objArr8 = new Object[1];
        K("ꆂ㊂✭\ue9fc\uf226Ꚕ⏆㖾䶥옓饙飜ᴛ煉辐覯훞례㣘\udc12㸦\udd11퇧\uf5eaꦧ㷯鐎\ufbcbꅆ␙㵽萁\ud866뽯", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 33, objArr8);
        g = new b(((String) objArr8[0]).intern(), 7, Boolean.class);
        Object[] objArr9 = new Object[1];
        J("웆욒ꏆ翁쨢䏖犩훧띡\uedee\ue0ff䒑┇掴回㪅鏃鉇쐡ꥒǔq䩱Ἒﾉ똞㡂贱汳Ⓞꦲϵ\uda6b嫵Ή\uf1bf䠊좼跋恂웳罃猐", 1 - TextUtils.indexOf("", "", 0, 0), objArr9);
        i = new b(((String) objArr9[0]).intern(), 8, Boolean.class);
        Object[] objArr10 = new Object[1];
        J("Ｌｸㄘ\ued1f밐큟қ䕮躋缰雍휘᳭\uf16a⃬ꤌꨩ\u0099눓㫛㠾銯㱃貓왣Ⓚ买Ẹ喙똚\udf80遼\ue381젫槹戵燠婨ﯨ\uf3d4９\ued9dԢ", View.MeasureSpec.getSize(0) + 1, objArr10);
        h = new b(((String) objArr10[0]).intern(), 9, Boolean.class);
        Object[] objArr11 = new Object[1];
        J("\ude49\ude1d㒾\ue8b9兀㳻䊇꧊꿮窖탑㮼㶈\uf4cc曰䖨譌Կ\uf40f홿ᥚ霉穙性\ue71eⅥ\u086d\uf20a瓕뎶馜糳싇춓⿕躳傂忪뷪Ὠ", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 1, objArr11);
        l = new b(((String) objArr11[0]).intern(), 10, Boolean.class);
        Object[] objArr12 = new Object[1];
        K("ꆂ㊂✭\ue9fc\uf226Ꚕ⏆㖾䶥옓饙飜ᴛ煉辐覯팈샴霚\ude28煣暳\uf8d3⬚✴屏\u0e6f\uf304鐎\ufbcbꅆ␙㵽萁\ud866뽯", (ViewConfiguration.getJumpTapTimeout() >> 16) + 36, objArr12);
        n = new b(((String) objArr12[0]).intern(), 11, Boolean.class);
        Object[] objArr13 = new Object[1];
        J("蟭螹䟨鯯\uee83蝩嘈ቘ\uf64aী쑞耮搬螚牿︺틨癩\ue080淭䃸\ue46e滖\udbab뺤刔\u1cff䦉⵰샚责읒魎뻊㭞㔃डⲑ", (ViewConfiguration.getTouchSlop() >> 8) + 1, objArr13);
        f78o = new b(((String) objArr13[0]).intern(), 12, Boolean.class);
        Object[] objArr14 = new Object[1];
        J("꽭꼹\ue8c9㓎왋뫂绀⿳\udecaꛡ\uec96붅䲬⢻媷쎑難\ud948졈偆桸䭏䘞\ue600阤ﴵ㐷琢װ濻ꗨ\ufaf9도ᇺᎉࢨ↡莰", 1 - TextUtils.getOffsetAfter("", 0), objArr14);
        m = new b(((String) objArr14[0]).intern(), 13, Boolean.class);
        Object[] objArr15 = new Object[1];
        J("牥爱獳꽴譄\uee04㏏笵ς㵛ꆙ\ue943醤댁ី靗❠䋲蕇Ҁ땶탙ଐ닟䬠暉社⃫\ud8f8\uf46d\ue8d5긌滫詞庝屌ﲮᠧ첢춗", 1 - ((Process.getThreadPriority(0) + 20) >> 6), objArr15);
        k = new b(((String) objArr15[0]).intern(), 14, Boolean.class);
        Object[] objArr16 = new Object[1];
        K("ꆂ㊂✭\ue9fc\uf226Ꚕ⏆㖾䶥옓饙飜ᴛ煉辐覯\ue4a8藱퇵信鄩港䀟曆䄘쐊귴ꚺ鐎\ufbcbꅆ␙㵽萁\ud866뽯", 35 - TextUtils.lastIndexOf("", '0', 0, 0), objArr16);
        t = new b(((String) objArr16[0]).intern(), 15, Boolean.class);
        Object[] objArr17 = new Object[1];
        J("썐쌗ᦪ얺ᇾ懵ꥻ\uf4c6닫垀㬡暲ₑ\ud9d8贂\u18ad陮⠷ῷ譍ѫ먮醨㴶", 1 - Color.red(0), objArr17);
        p = new b(((String) objArr17[0]).intern(), 16, Boolean.class);
        Object[] objArr18 = new Object[1];
        J("衙蠞＃⌓昁物\ude84\ue75a梨넩䳞甮殘㽱\ufafd\u0b31\udd64캞栂飀佽岇\ue657⺪", -TextUtils.lastIndexOf("", '0', 0, 0), objArr18);
        r = new b(((String) objArr18[0]).intern(), 17, Boolean.class);
        Object[] objArr19 = new Object[1];
        J("纞给辰╡栽ญ킸鬾༥띛䋢ॊ鵟㤃\uf4c1睕⮡죯昮\ue4b8릺嫆\ue86e勇䟉\uecbf驛샧퐒繹ள丳戡D", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr19);
        s = new b(((String) objArr19[0]).intern(), 18, Integer[].class);
        Object[] objArr20 = new Object[1];
        J("ᄜᅛ甙꤉\uf0d1땾䡔⁍悧㬳\uda0e눹\uf2dd땫氭찦䐳䒜ﻖ忑혩횯炼\ue9b9⡑惴ʺ箑뮍\uf207鍇", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 1, objArr20);
        q = new b(((String) objArr20[0]).intern(), 19, String[].class);
        Object[] objArr21 = new Object[1];
        K("㩥눾ᒆ푩솋ἀ퇵信ꆍ襴\uea0a疀霭饜ខฏʬꎻ鐎\ufbcb礅衶", 22 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr21);
        w = new b(((String) objArr21[0]).intern(), 20, Boolean.class);
        Object[] objArr22 = new Object[1];
        K("㩥눾ᒆ푩솋ἀ퇵信ꆍ襴\uea0a疀霪榗陓骜\uecd9㍃鐎\ufbcb礅衶", Drawable.resolveOpacity(0, 0) + 21, objArr22);
        u = new b(((String) objArr22[0]).intern(), 21, Boolean.class);
        Object[] objArr23 = new Object[1];
        J("愸慵⊴ﺤ悯뻪\ud837⯖ႄ沜䩿릭苄\ue2d0ﱍ있㐛\u1311溰呁꘍脂", 1 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr23);
        y = new b(((String) objArr23[0]).intern(), 22, String[].class);
        Object[] objArr24 = new Object[1];
        J("뗇떑䊂麒㟫˖轭韦쑰ಢᴡ֜嘰苏\uab1c箣\ue0d2猜㧩", 1 - View.MeasureSpec.getSize(0), objArr24);
        x = new b(((String) objArr24[0]).intern(), 23, TimePeriod.class);
        Object[] objArr25 = new Object[1];
        J("\ude18\ude4eꃷ糧븅͝ڃ陭꾯\ueed7铏З㷯您⋻稵謇酭뀷\ue9ee᤺͗㹌徚\ue753딂䱯", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr25);
        v = new b(((String) objArr25[0]).intern(), 24, BigDecimal.class);
        Object[] objArr26 = new Object[1];
        K("絆얏ខฏ되\uf2c1맺㰉霪榗陓骜\uecd9㍃䛕閍Ι妢꿗讖⾰ފ뱝십", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 23, objArr26);
        A = new b(((String) objArr26[0]).intern(), 25, BigDecimal.class);
        Object[] objArr27 = new Object[1];
        J("ꋚꊌẜ슌Ռ訙뷊Ἡ퍭傼⾆赓䄭\uded5馷\uf373\uf7c3⼷\u0b45悬旯", 1 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr27);
        C = new b(((String) objArr27[0]).intern(), 26, TimeZone.class);
        z = c();
        int i2 = L + 73;
        F = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    private b(String str, int i2, Class cls) {
        this.B = cls;
    }

    public final Class<?> a() {
        int i2 = L + 37;
        F = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return this.B;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:12:0x004d  */
    /* JADX WARN: Removed duplicated region for block: B:8:0x002e  */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v2 */
    /* JADX WARN: Type inference failed for: r13v7, types: [char[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void J(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 362
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.es.b.J(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void K(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 562
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.es.b.K(java.lang.String, int, java.lang.Object[]):void");
    }
}
