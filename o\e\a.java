package o.e;

import com.esotericsoftware.asm.Opcodes;
import java.util.Map;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\e\a.smali */
public class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long A;
    private static int D;
    public static final Map<Integer, Object> s;
    private static Object u;
    private static final Map<String, Object> v;
    private static Object w;
    private static byte[] x;
    private static byte[] y;

    /* JADX WARN: Code restructure failed: missing block: B:16:0x007f, code lost:
    
        r9 = r6;
     */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0079  */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0085  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x00a9  */
    /* JADX WARN: Removed duplicated region for block: B:29:0x007b  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:28:0x00a9 -> B:12:0x0055). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static java.lang.String $$c(int r7, short r8, short r9) {
        /*
            Method dump skipped, instructions count: 204
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.e.a.$$c(int, short, short):java.lang.String");
    }

    public static int b(int i) {
        int i2 = $11;
        int i3 = (i2 & 67) + (i2 | 67);
        $10 = i3 % 128;
        int i4 = i3 % 2;
        Object obj = w;
        int i5 = i2 + 77;
        $10 = i5 % 128;
        int i6 = i5 % 2;
        int i7 = i2 + Opcodes.LUSHR;
        $10 = i7 % 128;
        int i8 = i7 % 2;
        try {
            Object[] objArr = {Integer.valueOf(i)};
            byte[] bArr = $$a;
            Class<?> cls = Class.forName($$c(bArr[494], bArr[35], (short) 273), true, (ClassLoader) u);
            byte b = bArr[214];
            byte b2 = bArr[375];
            return ((Integer) cls.getMethod($$c((byte) (((b | (-1)) << 1) - (b ^ (-1))), b2, (short) ((b2 ^ 648) | (b2 & 648))), Integer.TYPE).invoke(obj, objArr)).intValue();
        } catch (Throwable th) {
            Throwable cause = th.getCause();
            if (cause != null) {
                throw cause;
            }
            throw th;
        }
    }

    public static int b(Object obj) {
        int i = $10 + 57;
        int i2 = i % 128;
        $11 = i2;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                Object obj2 = w;
                int i3 = (i2 & 73) + (i2 | 73);
                $10 = i3 % 128;
                int i4 = i3 % 2;
                try {
                    byte[] bArr = $$a;
                    Class<?> cls = Class.forName($$c(bArr[494], bArr[35], (short) 273), true, (ClassLoader) u);
                    byte b = bArr[214];
                    byte b2 = bArr[375];
                    return ((Integer) cls.getMethod($$c((byte) (((b | (-1)) << 1) - (b ^ (-1))), b2, (short) (b2 | 648)), Object.class).invoke(obj2, obj)).intValue();
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
        }
    }

    public static Object c(int i, char c, int i2) {
        int i3 = ($10 + 114) - 1;
        $11 = i3 % 128;
        switch (i3 % 2 == 0 ? '5' : '6') {
            case Opcodes.ISTORE /* 54 */:
                Object obj = w;
                try {
                    Object invoke = Class.forName($$c(r7[494], r7[35], (short) 273), true, (ClassLoader) u).getMethod($$c((byte) (($$a[214] - 0) - 1), r7[92], (short) 658), Integer.TYPE, Character.TYPE, Integer.TYPE).invoke(obj, Integer.valueOf(i), Character.valueOf(c), Integer.valueOf(i2));
                    int i4 = $11;
                    int i5 = (i4 ^ 43) + ((i4 & 43) << 1);
                    $10 = i5 % 128;
                    int i6 = i5 % 2;
                    return invoke;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
            default:
                throw null;
        }
    }

    static void init$0() {
        int i = ($11 + 40) - 1;
        $10 = i % 128;
        int i2 = i % 2;
        byte[] bArr = new byte[930];
        System.arraycopy("\u000bÉ×\u0006\u0006è\u00120Â÷>éÊ\fýþð\nþ\u0018Øûøþ\u001eÜÿ\n\u0001ñ\u0006è\u00120¿\bð\u00046Ø×\u0003ü\fõë\u0000ý\nô÷0Îý\u0001\u0000\u0003ÿê\b÷þ\u0006è\u00120½\u0002÷>éÆ\u0002\f Ê\fýþð=Ç3Ë:²ñÿ\böø\u0006\u0015å\u0001ëû\u0000\nþ\u000eÜ\u0010ê\fïð\u0007ï\u0000\u0003\u00023µ\n\u0001ëFÕê\u0001ë1Ñþ\u0005úÿï\u0000\u000eê\b÷þð\u0007ï\u0000\u0003\u00023¼ùBéÊ\tú\u0005=Ë\u000eðü\u0007÷þ\u0006è\u00120Â÷>éÆ\u0002\f!Ìý\u000eå-Øûøþ\u001eÜÿ\n\u0001ñ\u0000òó\nû:¸÷\u0003ü\fõ<âØ\u001eåõûúö2Üê2Ô\bëý$Ú\u000búüð½=Ç3Ë1\n\u0001ú\u001bÎ\u0006ýðð\u0007ï\u0000\u0003\u00023µ\n\u0001ëFÕê\u0001ëÿî+Úú\u0004ï,Øô\u0002\u0006ò\fÿî.ßûø\u0000\u001eØô\n\u0001ú>Æ4Æ>ýñAÊð÷\föúü?¸\fö\u0000òó\nû:¸÷\u0003ü\fõ<çÜê/Úú\u0004ñ\bü\u0003ùÿûø\u0000\u0000òó\nû:¸÷\u0003ü\fõ<éÞë\u000b\u001eÜê2Ô\bëý$Ú\u000búüðÿî.Ñ\bü\u001fßûø\u0000\u001eØô÷\b\b\u0012ö\u0014õ·üL·\u0002òý\u0007þûõõP±\u0004üïH\u0012÷\u0013õ\u0012ù\u0011õ\u0012õ\u0015õ\u0006è\u00120¿\bð\u00046èÔ\bëý$Ú\u000búüðð\u0007ï\u0000\u0003\u00023Êîý?êÎý&Øú\nþòö\u000bî\u001fê\u0001ú\u0012Þÿðÿî$áø\u0015ä\u0000ô\u0005\u0005\u0006è\u00120Â÷>çàê\u0010\u0015Øûøþ\u001eÜÿ\n\u0001ñú\u000bú\u001dÜê\u0006è\u00120Â÷>éÆ\u0002\f!Ìý\u000eå'×þ\u0001øþ\u001eÜÿ\n\u0001ñð\u0007ï\u0000\u0003\u00023¼ùBØ×\u0003ü\fõ'Ôú\tõ\u0005ÿöÿî.Ô\bëý$Ú\u000búüðð\u0007ï\u0000\u0003\u00023¾\u0005þø\u00050êÉ\u000eø÷\u0018ß\u000eûõÈ\u0000ê\u0010/È\u0000ê\u0010/üö\u0004î\fÿî+ÿ\föé\u0013ø÷\nê\bð\u000e\u0016à\u0004í\u000eìö&ìê\t Ö\u0004õ\u0005ô÷þôúù\u000b\u0006è\u00120Â÷>·\u0004ú\tøô\u0002ù\bù\u0005\u0015áúý\u0000óÿî!Û\u0000ü\bðûøÿî0Üì\u0001\u0000ôþ\f\u0012ìê\tð\u0007ï\u0000\u0003\u00023Êîý?êÛì\bð\nòø\"éó\n\u0001ú\u0006è\u00120Â÷>åÚú\u0004\u0013×þ\u0001øþ\u001eÜÿ\n\u0001ñÿð\u0014â\u0006ò\f\u0006è\u00120Â÷>â÷\u0007Ê\u0012ûòù\b÷þ\fê\t\u0019àóüÿî(Ø\u0002ò\b\u0005ò(Îý\u0001\u0000\u0003ÿê\b÷þ\u0006è\u00120Â÷>åÚú\u0004\u0012ú\u0010õ=Ç3Ç>ýë\u0000ý\nô÷\u001dèù\u0005\u0015áúý\u0000ó\u0006è\u00120Â÷>åÚú\u0004\u001eÜï\rî\u0006öù\u0002ú\u0002*Æ\u0002\f!Ìý\u000eåú\u000bú\u001eÔ\bëý½=Ç3Ê2\u0002\u000eî\nê\bð\u000e\u0016à\u0004í\u000eìö2Øô\nÿì\u0002ú\u0006\u0001ï\u0006è\u00120Â÷>âØûøþ\u001eÜÿ\n\u0001ñÿî-Ò÷\u0010ó\u0004\u000eðî\u0019èù\u0005ó÷þ".getBytes(LocalizedMessage.DEFAULT_ENCODING), 0, bArr, 0, 930);
        $$a = bArr;
        $$b = Opcodes.IF_ICMPLE;
        int i3 = $10 + 69;
        $11 = i3 % 128;
        int i4 = i3 % 2;
    }

    private a() {
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.processExcHandler(ExcHandlersRegionMaker.java:144)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.collectHandlerRegions(ExcHandlersRegionMaker.java:77)
        	at jadx.core.dex.visitors.regions.maker.ExcHandlersRegionMaker.process(ExcHandlersRegionMaker.java:38)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:27)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:123:0x19aa A[Catch: Exception -> 0x1a69, TRY_ENTER, TRY_LEAVE, TryCatch #56 {Exception -> 0x1a69, blocks: (B:3:0x0026, B:7:0x0048, B:31:0x01f6, B:32:0x01f9, B:33:0x01fd, B:42:0x0276, B:48:0x02c9, B:50:0x02cf, B:52:0x02d0, B:54:0x02d1, B:57:0x0315, B:70:0x035f, B:73:0x0368, B:75:0x036e, B:84:0x038e, B:91:0x03a8, B:97:0x1a36, B:123:0x19aa, B:134:0x19c7, B:141:0x1a23, B:143:0x1a29, B:144:0x1a2a, B:145:0x19db, B:129:0x1a2b, B:1065:0x1a57, B:1067:0x1a5e, B:1068:0x1a5f, B:1071:0x1a61, B:1081:0x019d, B:1087:0x01e7, B:1089:0x01ed, B:1090:0x01ee, B:1117:0x004d, B:136:0x19ee, B:137:0x1a20, B:37:0x0245, B:35:0x020c, B:44:0x0290, B:1083:0x01bf), top: B:2:0x0026, inners: #1, #24, #28, #69, #88 }] */
    /* JADX WARN: Removed duplicated region for block: B:129:0x1a2b A[Catch: Exception -> 0x1a69, TryCatch #56 {Exception -> 0x1a69, blocks: (B:3:0x0026, B:7:0x0048, B:31:0x01f6, B:32:0x01f9, B:33:0x01fd, B:42:0x0276, B:48:0x02c9, B:50:0x02cf, B:52:0x02d0, B:54:0x02d1, B:57:0x0315, B:70:0x035f, B:73:0x0368, B:75:0x036e, B:84:0x038e, B:91:0x03a8, B:97:0x1a36, B:123:0x19aa, B:134:0x19c7, B:141:0x1a23, B:143:0x1a29, B:144:0x1a2a, B:145:0x19db, B:129:0x1a2b, B:1065:0x1a57, B:1067:0x1a5e, B:1068:0x1a5f, B:1071:0x1a61, B:1081:0x019d, B:1087:0x01e7, B:1089:0x01ed, B:1090:0x01ee, B:1117:0x004d, B:136:0x19ee, B:137:0x1a20, B:37:0x0245, B:35:0x020c, B:44:0x0290, B:1083:0x01bf), top: B:2:0x0026, inners: #1, #24, #28, #69, #88 }] */
    /* JADX WARN: Removed duplicated region for block: B:131:0x19b6 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:146:0x19b3 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:251:0x12e6 A[Catch: all -> 0x12e8, TryCatch #90 {all -> 0x12e8, blocks: (B:294:0x14fe, B:317:0x151c, B:456:0x1399, B:458:0x139f, B:459:0x13a0, B:464:0x135a, B:466:0x1360, B:467:0x1361, B:249:0x12e0, B:251:0x12e6, B:252:0x12e7), top: B:293:0x14fe }] */
    /* JADX WARN: Removed duplicated region for block: B:252:0x12e7 A[Catch: all -> 0x12e8, TRY_LEAVE, TryCatch #90 {all -> 0x12e8, blocks: (B:294:0x14fe, B:317:0x151c, B:456:0x1399, B:458:0x139f, B:459:0x13a0, B:464:0x135a, B:466:0x1360, B:467:0x1361, B:249:0x12e0, B:251:0x12e6, B:252:0x12e7), top: B:293:0x14fe }] */
    /* JADX WARN: Removed duplicated region for block: B:330:0x1677 A[Catch: all -> 0x17f7, TRY_ENTER, TryCatch #96 {all -> 0x17f7, blocks: (B:323:0x152c, B:325:0x162b, B:330:0x1677, B:333:0x16ae), top: B:322:0x152c }] */
    /* JADX WARN: Removed duplicated region for block: B:370:0x17af A[SYNTHETIC] */
    /* JADX WARN: Type inference failed for: r10v170 */
    /* JADX WARN: Type inference failed for: r10v45 */
    /* JADX WARN: Type inference failed for: r10v46, types: [int] */
    /* JADX WARN: Type inference failed for: r14v71, types: [java.lang.Object[]] */
    /* JADX WARN: Type inference failed for: r2v133, types: [java.lang.reflect.Method] */
    /* JADX WARN: Type inference failed for: r2v190, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v48, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r2v65, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r31v7 */
    /* JADX WARN: Type inference failed for: r4v149, types: [java.lang.Class] */
    /* JADX WARN: Type inference failed for: r5v112, types: [java.lang.reflect.Method] */
    /* JADX WARN: Type inference failed for: r5v131, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r5v93, types: [java.lang.reflect.Method] */
    /* JADX WARN: Type inference failed for: r6v100 */
    /* JADX WARN: Type inference failed for: r6v217 */
    /* JADX WARN: Type inference failed for: r6v99 */
    /* JADX WARN: Type inference failed for: r7v150, types: [java.lang.reflect.Method] */
    /* JADX WARN: Type inference failed for: r8v102 */
    /* JADX WARN: Type inference failed for: r8v103, types: [byte[]] */
    /* JADX WARN: Type inference failed for: r8v108 */
    /* JADX WARN: Type inference failed for: r8v111, types: [int] */
    /* JADX WARN: Type inference failed for: r8v118, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r8v123 */
    /* JADX WARN: Type inference failed for: r8v129, types: [java.lang.Class[]] */
    /* JADX WARN: Type inference failed for: r8v137, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r8v147, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r8v155 */
    /* JADX WARN: Type inference failed for: r8v260 */
    /* JADX WARN: Type inference failed for: r8v95 */
    /* JADX WARN: Type inference failed for: r8v96 */
    /* JADX WARN: Type inference failed for: r8v97 */
    /* JADX WARN: Type inference failed for: r9v101, types: [java.lang.Object[]] */
    /* JADX WARN: Type inference failed for: r9v134 */
    /* JADX WARN: Type inference failed for: r9v221 */
    /* JADX WARN: Type inference failed for: r9v264 */
    /* JADX WARN: Type inference failed for: r9v265 */
    /* JADX WARN: Type inference failed for: r9v29 */
    /* JADX WARN: Type inference failed for: r9v37 */
    static {
        /*
            Method dump skipped, instructions count: 6952
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.e.a.<clinit>():void");
    }
}
