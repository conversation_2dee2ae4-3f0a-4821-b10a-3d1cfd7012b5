package fr.antelop.sdk.card.emvapplication;

import android.content.Context;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.Product;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\emvapplication\EmvApplication.smali */
public interface EmvApplication {
    List<EmvApplicationActivationMethod> getActivationMethods();

    String getCardId();

    String getEmvApplicationGroupId();

    String getId();

    Product getProduct();

    EmvApplicationActivationMethod getSelectedActivationMethod();

    EmvApplicationStatus getStatus();

    EmvApplicationType getType();

    void selectActivationMethod(Context context, String str, AntelopCallback antelopCallback) throws WalletValidationException;

    void submitActivationCode(Context context, String str, AntelopCallback antelopCallback) throws WalletValidationException;
}
