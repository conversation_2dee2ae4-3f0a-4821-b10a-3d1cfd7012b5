package o.bb;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.auth.api.credentials.CredentialsApi;
import com.google.android.gms.auth.api.proxy.AuthApiStatusCodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.google.android.gms.tapandpay.TapAndPayStatusCodes;
import fr.antelop.sdk.AntelopErrorCode;
import java.nio.ByteBuffer;
import kotlin.io.encoding.Base64;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.i18n.LocalizedMessage;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bb\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final a A;

    @Deprecated
    public static final a B;

    @Deprecated
    public static final a C;
    public static final a D;
    public static final a E;
    public static final a F;
    public static final a G;
    public static final a H;

    @Deprecated
    public static final a I;
    public static final a J;
    public static final a K;
    public static final a L;
    public static final a M;
    public static final a N;
    public static final a O;
    public static final a P;
    public static final a Q;
    public static final a R;
    public static final a S;
    public static final a T;
    public static final a U;
    public static final a V;
    public static final a W;
    public static final a X;
    public static final a Y;
    public static final a Z;
    public static final a a;
    public static final a aA;
    public static final a aB;
    public static final a aC;
    public static final a aD;
    public static final a aE;
    public static final a aF;
    public static final a aG;
    private static char[] aH;
    private static final /* synthetic */ a[] aI;
    private static int aJ;
    private static long aK;
    private static int aQ;
    public static final a aa;
    public static final a ab;
    public static final a ac;
    public static final a ad;
    public static final a ae;
    public static final a af;
    public static final a ag;
    public static final a ah;
    public static final a ai;
    public static final a aj;
    public static final a ak;
    public static final a al;
    public static final a am;
    public static final a an;
    public static final a ao;
    public static final a ap;
    public static final a aq;
    public static final a ar;
    public static final a as;
    public static final a at;
    public static final a au;
    public static final a av;
    public static final a aw;
    public static final a ax;
    public static final a ay;
    public static final a az;
    public static final a b;
    public static final a c;
    public static final a d;
    public static final a e;

    @Deprecated
    public static final a f;
    public static final a g;
    public static final a h;
    public static final a i;
    public static final a j;
    public static final a k;
    public static final a l;

    @Deprecated
    public static final a m;
    public static final a n;

    /* renamed from: o, reason: collision with root package name */
    public static final a f39o;
    public static final a p;
    public static final a q;
    public static final a r;
    public static final a s;
    public static final a t;
    public static final a u;
    public static final a v;
    public static final a w;
    public static final a x;
    public static final a y;
    public static final a z;
    private final int aL;

    static void a() {
        char[] cArr = new char[2132];
        ByteBuffer.wrap(",\u0087\u0080>u¼)s\u009eÛr^'+FhêÓ\u001fyC\u0089ô/\u0018¼MÞþP\"\u008fW\u0003û¯,ÇQy\u0085æ68Z·\u008fÃ0^dþ\u0089'=¸n \u0093UÇð,\u0087\u00804u\u008d)v\u009eÆrC'2\u0094¤Hq=ò\u0091QF ;\u0087ï\u0016\\ü0då;Z£\u000e\u0016ãó,\u008b\u00800u\u009a)j\u009eÌr_'=\u0094³Hl=â\u0091IF.;\u0087ï\u0002\\ü0så,Z°\u000e\u001dãèWG\u0004Öù\u009c\u00ad\u0013\u0002ûö~«Ë³g\u001fÎêf¶§\u0001(íª¸Ó\u000bi×\u0094¢\u0017\u000e¢Ù×¤EpöÃ\u0013¯\u00adzÍÅx\u0091÷|\u0007Èª\u009b;fRÌ\u009d`4\u0095\u009aÉi~Ç\u0092XÇ:t\u0080¨eÝÓqX¦\"Û\u0082\u000f\u0014¼÷ÐE\u0005\fº£î\u000b\u0003î·[,\u008c\u0080=u\u0090)f\u009eÀrS'0\u0094\u008dH`=å\u0091@F\r;\u0086ï\u0002\\í D\fëù@¥«\u0012\u0005þ\u008b«â\u0018qÄ¦±+\u001d\u0094Êÿ·BcÇÐ)¼\u0090æÔJg¿Þã%T\u0095¸\u0010ía^ü\u00825÷¶[+\u008cdñÛ%K\u0096¦ú\u0013/x\u0090îÄO\u0086\u0085*,ß\u0084\u0083`4ÍØ_\u008d5>\u0098âm\u0097ó;Zì\u0005\u0091\u0080E2öû\u009aXO\u0000ð¹¤\u0012IåýJ®ÙS°\u0007%¨ê\\Q\u0001Õ²¢f\r\u000b\u009a¿q`ß\u0015°,\u0084\u00800u\u0097)e\u009eÈrE'6\u0094\u0093Hp=Á\u0091\\F3;\u0084ï\u0018\\ê0Rå Z¾\u000e\u0017ãÏWF\u0004Åù\u009e\u00ad\u0013\u0002èö\u007f«Í\u0018¤Ì\r,\u008a\u0080!u\u008c)@\u009eÛrR'1\u0094\u0088H}=ô\u0091ZF5;\u009cï\u0003\\ü0oå&Z¥\u000e*ãôWY\u0004Áù¶\u00ad\u0013\u0002ýöt«Ý,\u009e\u00800u\u0095)m\u009eÌrE'\u0015\u0094\u008eHj=ú\u0091\\F%,\u009e\u00800u\u0095)m\u009eÌrE'\u0015\u0094\u008eHj=ú\u0091\\F%;¨ï\u001f\\ý0då%Z¸\u000e\u001eãèWK\u0004Øùµ\u00ad\b\u0002ýöh«õ\u0018®Ì\u001a¡\u0085Ó\u008f\u007f!\u008a\u0084Ö|aÝ\u008dTØ\u0004k\u009f·\u007fÂçnM¹4Ä·\u0010\u0015£ü,\u008b\u0080=u\u0096)b\u009eÂrT'=\u0094£Hp=Å\u0091KF ;\u0087ï\u0002\\ø0Bå=Z¸\u000e\u0016ãï,\u008b\u0080=u\u0096)b\u009eÂrT'=\u0094£Hp=Ã\u0091\\F,;\u0086ï\u0005\\ü0lå(Z¿\u000e\u0018ãæWL\u0004Üù¼\u00ad\u000f\u0002ý\u0098\u00844;Á\u008b\u009dd*ÁÆ\\\u00939 ²ül\u0089ù%Qò \u008f\u0099[6èò\u0084KQ+î¼º\u001a,\u009e\u00800u\u0095)m\u009eÌrE'\u001d\u0094\u0084He=ô\u0091MF$;\u008d,\u009e\u0080#u\u0096)o\u009eÎrb'0\u0094\u008cHJ=ð\u0091KF%;ºï\u0005\\ø0Uå,|IÐç%ByºÎ\u001b\"\u0092wÀÄY\u0018ªm\u0014Á\u009b\u0016økP¿Ï\f `\u0091,\u009e\u00800u\u0095)m\u009eÌrE'\f\u0094\u0091Hm=ð\u0091MF$;«ï\u001d\\ö0Bå\"Z´\u000e\u001d\u0018\u0000´·A\u0018\u001déªCFÌ\u0013¦ \u001c|ï\tp¥Âr\u0080\u000f\tÛ\u008ah_\u0004ÀÑ¯n*:\u009f×ocÊ0WÍ,\u0099\u008b6bÐB|ý\u0089MÕ¢b\u0007\u008e\u009aÛÿh`´¤Á=m\u009dºêÇL\u0013Æ )Ì\u0082\u0019ÿ¦zòÔ\u001f-,\u008e\u00804u\u0097)d\u009eÛrX':\u0094¤H{=ã\u0091VF3,\u0080\u0080\"u\u008a)t\u009eÌrC'\u0010\u0094\u0085HG=þ\u0091MF\u0005;\u008cï\u0017\\ð0Oå,Zµ,\u008d\u00804u\u008f)h\u009eÊrT'\u0010\u0094\u008fHo=þ\u0091pF/;\u009fï\u0010\\õ0Hå-_Sóé\u0006VZ³í\u0004\u0001\u008bTöçS;½N$â²5ïHA\u009cÂ/\u000fC\u009f\u0096á)y}Ã\u0090=$\u009bw\u0004\u008aeÞóq6\u0085\u0098Ø\u0007kk¿ÇÒCf°¹\u001fÌv,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u008dHh=è\u0091jF$;\u009bï\u0007\\ð0Bå,Z¢\u000e+ãäWJ\u0004Þù¯\u00ad\u0004\u0002ûöp«Û\u0018\u00adÌ\f¡´\u0015kÊÓ¿¦\u0013#,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u008dHh=è\u0091jF$;\u009bï\u0007\\ð0Bå,Z¢\u000e,ãïW[\u0004Ôùº\u00ad\u000e\u0002ÿöt«Ë\u0018 Ì\u000b¡\u009d\u0015|Êä¿»\u0013#À\u0096´s,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u008dHh=è\u0091jF$;\u009bï\u0007\\ð0Bå,Z¢\u000e<ãùWJ\u0004Ôù©\u00ad\u0015\u0002àö~«×¤i\bÔýz¡\u0099\u0016\u0017ú®¯Ý\u001cxÀ\u009fµ\b\u0019ªÎÐ³mgèÔ\u0006¸¿méÒS\u0086æk\u0007ß°\u008c%qL%ã\u008a7~\u008e#=\u0090rDö)o\u009d\u008fB87^\u009bÔH{<\u0094á=,\u0099\u0080$u\u008a)i\u009eçr^'-\u0094\u0088Ho=ø\u0091ZF ;\u009dï\u0018\\ö0Oå\u0019Z£\u000e\u0016ã÷W@\u0004Õù¼\u00ad\u0013\u0002Üö\u007f«Ò\u0018¯Ì\u0006¡\u0086\u0015wÊä¿»\u0013#À\u0096´s\u00ad´\u0001\u0004ô¬¨\\\u001fÿón¦3\u0015·ÉR¼Ò\u0010PÇ\u001eº¡n=ÝÊ±xd\u0016Û\u0098\u008f\u000ebÒÖ`\u0085øx\u008a,5\u0083Ô,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u008dHh=è\u0091jF$;\u009bï\u0007\\ð0Bå,Z¢\u000e0ãïW_\u0004Ðùµ\u00ad\b\u0002í\u008cÖ fÕÎ\u0089>>\u009dÒ\f\u0087Q4Õè0\u009d°12æ|\u009bÃO_ü¨\u0090\u001aEtúú®eC°÷\u0002¤\u0088Yã\rU¢´V-\u0088a$ÑÑy\u008d\u0089:*Ö»\u0083æ0bì\u0087\u0099\u00075\u0085âË\u009ftKèø\u001f\u0094\u00adAÃþMªÃG\u001eó¢ ?]B\tç¦\bR\u0099,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u008dHh=è\u0091jF$;\u009bï\u0007\\ð0Bå,Z¢\u000e,ãñWM\u0004Ðù\u00ad\u00ad\u0004\u0002Ûöt«È\u0018´Ì\u0000¡\u0083\u0015|ÊÅÐý|@\u0089öÕ0b¼\u008e>ÛVhô´\u001bÁ\u008aIOåò\u0010DL\u0086û\t\u0017\u0089BöñC-®X;,\u0081\u0080<u\u008a)E\u009eÀrB'8\u0094\u0083He=ô\u0091],\u0081\u0080<u\u008a)T\u009eÙrU'8\u0094\u0095Hl=Ã\u0091\\F0;\u009cï\u0018\\ë0Då-,\u0081\u0080<u\u008a)O\u009eÆrE'\n\u0094\u0094Hy=á\u0091VF3;\u009dï\u0014\\ý+¦\u0087\u001cr©.H\u0099ÿu} \u000f\u0093±OX:Ç\u0096hA\u0019<\u0097è\"[Ô7{â\u0017]\u008a\t?äñPx\u0003Éþ\u0089ª7\u0005ØñIF$ê\u009e\u001f+CÊô}\u0018ÿM\u008dþ3\"ÚWEûê,\u009bQ\u0006\u0085©6WZì\u008f\u009b0\u0002d·\u0089Y=Ýnb\u0093\u0012Ç½hX\u009cÅÁ`r6¦§Ë#\u007fÊ ZÕ\u001by\u009eª)ÞÝ\u0003`Dmè×\u001dbA\u0083ö4\u001a¶OÄüz \u0093U\fù£.ÒSX\u0087÷4\u001fXº\u008dÏ2kfâ\u008b\u0001?´l#\u0091DÅðj\u0019\u009e£Ã?pZ¤ðÉV}\u0088¢'×K{À¨\u007fG\u0093ë)\u001e\u009cB}õÊ\u0019HL:ÿ\u0084#mVòú]-,Pª\u0084\u00157å[J\u008e/1²e\u0017\u0088È<LoÕ\u0092µÆ\u0002iñ\u009dvÀÒs¿§\nÊ\u0094~}¡èÔ¬x?«\u0096,\u0099\u0080#u\u0096)w\u009eÀrB'0\u0094\u008eHg=ø\u0091WF&;»ï\u0014\\ê0Qå&Z¿\u000e\nãäW~\u0004Øù\u00ad\u00ad\t\u0002æöd«Í\u0018\u0082Ì\u0006¡\u009c\u0015tÊÀ¿§\u00135À·´niÍÝTª\u001c\u0006¦ó\u0013¯ò\u0018EôÇ¡µ\u0012\u000bÎâ»}\u0017ÒÀ£½>i\u0091Úo¶Ôc£Ü:\u0088\u008feaÑâ\u0082[\u007f\f+\u0096\u0084cpð-I\u009e'J\u0098'5\u0093ðLH9#\u0095£F\u00192à,\u0099\u0080#u\u0096)w\u009eÀrB'0\u0094\u008eHg=ø\u0091WF&; ï\u001f\\ï0@å%Z¸\u000e\u001dãÀWJ\u0004Åù°\u00ad\u0017\u0002èöe«Ð\u0018®Ì\u0007¡²\u0015vÊÅ¿¬,Û\u0080auÔ)5\u009e\u0082r\u0000'r\u0094ÌH%=º\u0091\u0015Fd;îïK\\«0\nåyZö\u000e_ã\u0082W\b\u0004\u0087ùò\u00adU\u0002ªö'«\u0092\u0018ìÌE¡ð\u00154Ê\u0087¿î,\u0099\u0080#u\u0096)w\u009eÀrB'0\u0094\u008eHg=ø\u0091WF&;¥ï\u001e\\ú0Jå,Zµ\u000e8ãâW]\u0004Øù¯\u00ad\u0000\u0002ýöx«Ö\u0018¯Ì*¡\u009e\u0015}ÊÄsÑßk*Þv?Á\u0088-\nxxËÆ\u0017/b°Î\u001f\u0019ndà°U\u0003£o\fº`\u0005ýQH¼\u009c\b\u0012[\u009c¦õòh]¢©-ô\u0098Gÿ\u0093@þÍJ8\u0095\u0086àïLZ\u009fÞë-6\u0084,\u008a\u0080>u\u0097)o\u009eÌrR'-\u0094¶Hh=ý\u0091UF$;\u009dï?\\ö0Uå\u0019Z£\u000e\u0016ã÷W@\u0004Âù°\u00ad\u000e\u0002çöt«ÝøRTæ¡Oý·J\u0014¦\u008aóõ@l\u009c¿é\"E\u008f\u0092öïF;Ç\u0088\u0016ä\u00981ý\u008eeÚÄ7-\u0083¢Ð\u001d-`yÍÖ$\"º,\u0088\u00802u\u008d)h\u009eßrP'-\u0094\u0088Hf=ÿ\u0091kF$;\u009aï\u0001\\ö0Oå:Z´\u000e0ãïW_\u0004Ðùµ\u00ad\b\u0002íö[«Ê\u0018®Ì\u0007¡·\u0015vÊÓ¿¤\u00130À\u008d,\u0088\u00802u\u008d)h\u009eßrP'-\u0094\u0088Hf=ÿ\u0091nF(;\u009dï\u0019\\Î0Så&Z¿\u000e\u001eãÂW[\u0004Ôù½\u00ad\u0004\u0002çöe«Ð\u0018 Ì\u0005¡\u0082\tª¥\u0010P¯\fJ»ýWr\u0002\u000f±ªmD\u0018Ý´Xc\u0011\u001e®Ê7yÞ\u0015mÀ\u001f\u007f\u009a+:ÆÏrx!ÁÜ\u009e\u00882'ÞÓZ\u008eé=\u0086é/,÷\u0080Muò)\u0017\u009e r/'R\u0094÷H\u0019=\u0080\u0091\u0007F\\;ùï|\\\u00920;åR,\u0088\u00802u\u008d)h\u009eßrP'-\u0094\u0088Hf=ÿ\u0091uF2;\u0082ï#\\ü0Må(Z¥\u000e\u001cãåWo\u0004Ðù°\u00ad\r\u0002üöc«Ü9ö\u0095L`ó<\u0016\u008b¡g.2S\u0081ö]\u0018(\u0081\u0084\u0010SV.ãúgI²%1ðRO×\u001bwö\u009aB4\u0011»ìÂ¸{\u0017´ã\u001d¾¢\rÛÙr´á\u0000\u0013ß¶ªÖ\u0006CÕô±§\u001d\u001aè¦´[\u0003æïoº!\tµÕ\\ Ì\fkÛ\u001d¦\u0085r8ÁÍ\u00adtx\u0015Ç©\u00930~ßÊv\u0099ïd\u008c0.\u009fÛkK6î\u0085\u0089RUþê\u000bZWµà\u0010\f\u008dYèêu6©C0ï\u00848ñER\u0091Ð\"%N\u0097\u009bý$ppÅ\u009d;)\u0092z0\u0087cÓß|9\u0088ª\u008d}!ÂÔr\u0088\u009d?8Ó¥\u0086À5Pé\u009d\u009c\n0¡çß\u009amNïý\b\u0091¹DþûC¯æB,öµ¥>XE\fñ£\u00073\r\u009f°j\u00176ì\u0081BmÀ8²\u008b\u0005Wä\"S\u008eÞY«$\"ð\u0098C\u007f/Çú®E-\u0011²üfHÏ\u001bWæ4²\u008a\u001du\u0003´¯\tZ®\u0006U±û]y\b\u000b»¼g]\u0012ê¾gi\u0012\u0014\u0093À)sÄ\u001f|Ê\u001du\u0084,\u009d\u0080#u\u0098)o\u009eÚrP':\u0094\u0095H`=þ\u0091WF\u000e;\u009dï\u0019\\ü0Så\fZ£\u000e\u000bãîW[,\u009d\u0080#u\u0098)o\u009eÚrP':\u0094\u0095H`=þ\u0091WF\u000f;\u0086ï7\\ë0Då:Z¹\u000e2ãäWP\u0004Âù\u009c\u00ad\u0013\u0002ûö~«Ë¶\u009c\u001a\"ï\u0099³n\u0004ÛèQ½;\u000e\u0094Òa§ÿ\u000bVÜ\u0014¡\u008du\u0011ÆêªE\u007f,¸K\u0014ñáJ½¤\n&æ\u009f³ì\u0000rÜ\u00ad©$\u0005\u008aÒé¯M{ÆÈ=¤\u0084qÇÎ~\u009aüw%Ã\u0084\u0090\u0015ml9Å,\u0088\u00802u\u008d)h\u009eßrP'-\u0094\u0084HL=ü\u0091OF\u0000;\u0099ï\u0001\\õ0Hå*Z°\u000e\rãèWF\u0004ßù\u0098\u00ad\u0002\u0002ýöx«Ï\u0018 Ì\u001d¡\u0098\u0015vÊÏ¿\u008a\u0013>À\u009d´diàÝ_²/g\u0080Ûe\u0088ø|],\u0088\u00802u\u008d)h\u009eßrP'-\u0094\u0084HL=ü\u0091OF\u0000;\u0099ï\u0001\\õ0Hå*Z°\u000e\rãèWF\u0004ßù\u0098\u00ad\u0002\u0002ýöx«Ï\u0018 Ì\u001d¡\u0098\u0015vÊÏ¿\u009b\u00134À\u009f´tiÚÝT²=\u008c\u0091 +Õ\u0094\u0089q>ÆÒI\u008744\u009dèU\u009då1Væ\u0019\u009b\u0080O\u0018üì\u0090QE3ú©®\u0014Cñ÷_¤ÆY\u0081\r\u001b¢äVa\u000bÖ¸¹l\u0004\u0001\u0081µojÖ\u001f\u0093³'`\u0084\u0014}Éõ}P\u00120Ç\u0091{b(íÜDæMJ÷¿Lã¢T*¸\u009fíù^O\u0082º÷?[\u0084\u008cçñZ%ß\u00961ú\u0088/Ü\u0090sÄØ)3\u009d\u009dÎ\u00133zç\u0017K®¾\u0015âòUP¹Îì\u008d_\u0014\u0083çö_ZÊ\u008d¶ð\u0016$¸\u0097zûÕ.°\u0091#Å\u0091(t\u009cÝÏB29f\u009eÉw\u001b±·\u000bB°\u001e^©ÞEe\u0010\u0001£±\u007fW\nÎ,\u008a\u00800u\u008b)e\u009eírT'5\u0094\u0084H}=ô\u0091]\u0014\u008b¸5M\u008b\u0011d¦ýJT\u001f+¬\u0092p{\u0005è©Z~?\u0003\u009f×\ndý\b\u007fÝ0b°6\u000fÛúoW<ÂnËÂg7Îk*Ü\u008f0\u001cenÖË\n)\u007f³Ó\u000e\u0004gyé\u00adG\u001e©r\u0016§e\u0018ÿL_¡°\u0015)F\u0093»ôïA@¯´>é\u0096Zç\u008eN,\u0088\u0080$u\u008d)i\u009eÌr_'-\u0094\u0088Hj=ð\u0091MF$;ªï\u0004\\ê0Uå&Z¼\u000e\u001cãóWm\u0004Äù©\u00ad\r\u0002àör«Ø\u0018µÌ\f¡\u0095,\u0088\u0080$u\u008d)i\u009eÌr_'-\u0094\u0088Hj=ð\u0091MF$;ªï\u0004\\ê0Uå&Z¼\u000e\u001cãóW}\u0004Øù´\u00ad\u0004\u0002æöd«Í,\u009d\u00804u\u008b)l\u009eÚrp'7\u0094\u0085HJ=þ\u0091WF%;\u0080ï\u0005\\ð0Nå'Z¢\u000e8ãíW[\u0004Ôù¸\u00ad\u0005\u0002ðöC«Ü\u0018§Ì\u001c¡\u0082\u0015|ÊÅ,\u009d\u00804u\u008b)l\u009eÚrp'7\u0094\u0085HJ=þ\u0091WF%;\u0080ï\u0005\\ð0Nå'Z¢\u000e8ãíW[\u0004Ôù¸\u00ad\u0005\u0002ðöP«É\u0018±Ì\u001b¡\u009e\u0015oÊÄ¿\u00ad,\u008e\u0080>u\u0096)f\u009eÅrT'\t\u0094\u0080Hp=Æ\u0091XF-;\u0085ï\u0014\\í0oå&Z¥\u000e8ã÷WH\u0004Øùµ\u00ad\u0000\u0002ëö}«Ü$¿\u0088\u001e}±!d\u0096àzs/\u001a\u009c\u0092@_5Ó\u0099~N\u00133ªç\u0005TÚ8aí\u001aR\u0084\u0006:ëÃ".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 2132);
        aH = cArr;
        aK = 4197484116490027089L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x0027). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void aN(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r8 = 105 - r8
            int r7 = r7 * 3
            int r7 = r7 + 4
            byte[] r0 = o.bb.a.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r8
            r4 = r2
            r8 = r7
            goto L27
        L15:
            r3 = r2
        L16:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            r3 = r0[r7]
        L27:
            int r7 = r7 + 1
            int r8 = r8 + r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.a.aN(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -112, 34, 95};
        $$b = 230;
    }

    private static /* synthetic */ a[] e() {
        int i2 = aQ;
        int i3 = i2 + 41;
        aJ = i3 % 128;
        int i4 = i3 % 2;
        a[] aVarArr = {e, c, a, b, d, h, i, g, j, f, m, n, k, l, f39o, p, r, q, s, t, v, y, w, u, x, A, z, D, C, B, I, E, F, H, G, M, J, L, K, N, S, O, Q, P, R, V, X, T, W, U, ab, aa, ac, Z, Y, ah, ag, ae, af, ad, aj, al, am, ai, ak, ap, ao, an, aq, ar, av, aw, au, as, at, ay, az, aA, ax, aB, aC, aE, aG, aD, aF};
        int i5 = i2 + 21;
        aJ = i5 % 128;
        int i6 = i5 % 2;
        return aVarArr;
    }

    public static a valueOf(String str) {
        int i2 = aJ + 7;
        aQ = i2 % 128;
        boolean z2 = i2 % 2 == 0;
        a aVar = (a) Enum.valueOf(a.class, str);
        switch (z2) {
            case false:
                int i3 = aJ + 65;
                aQ = i3 % 128;
                int i4 = i3 % 2;
                return aVar;
            default:
                throw null;
        }
    }

    public static a[] values() {
        int i2 = aJ + 45;
        aQ = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                a[] aVarArr = (a[]) aI.clone();
                int i3 = aJ + 89;
                aQ = i3 % 128;
                int i4 = i3 % 2;
                return aVarArr;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        aJ = 0;
        aQ = 1;
        a();
        Object[] objArr = new Object[1];
        aM((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), TextUtils.getTrimmedLength(""), 6 - Process.getGidForName(""), objArr);
        e = new a(((String) objArr[0]).intern(), 0, 0);
        Object[] objArr2 = new Object[1];
        aM((char) (27364 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 6 - TextUtils.lastIndexOf("", '0'), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 25, objArr2);
        c = new a(((String) objArr2[0]).intern(), 1, 1000);
        Object[] objArr3 = new Object[1];
        aM((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (ViewConfiguration.getFadingEdgeLength() >> 16) + 31, Process.getGidForName("") + 21, objArr3);
        a = new a(((String) objArr3[0]).intern(), 2, 1001);
        Object[] objArr4 = new Object[1];
        aM((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 50 - TextUtils.lastIndexOf("", '0', 0), View.MeasureSpec.makeMeasureSpec(0, 0) + 27, objArr4);
        b = new a(((String) objArr4[0]).intern(), 3, 1002);
        Object[] objArr5 = new Object[1];
        aM((char) (40942 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), View.getDefaultSize(0, 0) + 78, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 22, objArr5);
        d = new a(((String) objArr5[0]).intern(), 4, 1050);
        Object[] objArr6 = new Object[1];
        aM((char) (View.MeasureSpec.makeMeasureSpec(0, 0) + 57344), 101 - (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.getCapsMode("", 0, 0) + 21, objArr6);
        h = new a(((String) objArr6[0]).intern(), 5, 1051);
        Object[] objArr7 = new Object[1];
        aM((char) (ViewConfiguration.getPressedStateDuration() >> 16), 122 - (ViewConfiguration.getTapTimeout() >> 16), ExpandableListView.getPackedPositionType(0L) + 15, objArr7);
        i = new a(((String) objArr7[0]).intern(), 6, 1052);
        Object[] objArr8 = new Object[1];
        aM((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 36062), TextUtils.indexOf((CharSequence) "", '0', 0) + Opcodes.L2D, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 15, objArr8);
        g = new a(((String) objArr8[0]).intern(), 7, 1053);
        Object[] objArr9 = new Object[1];
        aM((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 51796), (ViewConfiguration.getDoubleTapTimeout() >> 16) + Opcodes.IFEQ, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 19, objArr9);
        j = new a(((String) objArr9[0]).intern(), 8, CredentialsApi.CREDENTIAL_PICKER_REQUEST_CODE);
        Object[] objArr10 = new Object[1];
        aM((char) (43532 - ((byte) KeyEvent.getModifierMetaStateMask())), TextUtils.indexOf("", "", 0, 0) + Opcodes.IRETURN, Color.argb(0, 0, 0, 0) + 33, objArr10);
        f = new a(((String) objArr10[0]).intern(), 9, 2001);
        Object[] objArr11 = new Object[1];
        aM((char) Color.argb(0, 0, 0, 0), Drawable.resolveOpacity(0, 0) + 205, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 29, objArr11);
        m = new a(((String) objArr11[0]).intern(), 10, 2002);
        Object[] objArr12 = new Object[1];
        aM((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), 233 - ImageFormat.getBitsPerPixel(0), 27 - KeyEvent.normalizeMetaState(0), objArr12);
        n = new a(((String) objArr12[0]).intern(), 11, 2003);
        Object[] objArr13 = new Object[1];
        aM((char) View.resolveSize(0, 0), View.MeasureSpec.makeMeasureSpec(0, 0) + 261, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 12, objArr13);
        k = new a(((String) objArr13[0]).intern(), 12, 2005);
        Object[] objArr14 = new Object[1];
        aM((char) ((Process.getThreadPriority(0) + 20) >> 6), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 272, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 30, objArr14);
        l = new a(((String) objArr14[0]).intern(), 13, 2006);
        Object[] objArr15 = new Object[1];
        aM((char) (65298 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 303, 16 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr15);
        f39o = new a(((String) objArr15[0]).intern(), 14, 2007);
        Object[] objArr16 = new Object[1];
        aM((char) Color.green(0), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 318, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 19, objArr16);
        p = new a(((String) objArr16[0]).intern(), 15, 2008);
        Object[] objArr17 = new Object[1];
        aM((char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (ViewConfiguration.getTapTimeout() >> 16) + 338, ((Process.getThreadPriority(0) + 20) >> 6) + 25, objArr17);
        r = new a(((String) objArr17[0]).intern(), 16, 2009);
        Object[] objArr18 = new Object[1];
        aM((char) (46084 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), 364 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), TextUtils.lastIndexOf("", '0', 0, 0) + 20, objArr18);
        q = new a(((String) objArr18[0]).intern(), 17, 2010);
        Object[] objArr19 = new Object[1];
        aM((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), 383 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 12, objArr19);
        s = new a(((String) objArr19[0]).intern(), 18, 2011);
        Object[] objArr20 = new Object[1];
        aM((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), Color.argb(0, 0, 0, 0) + 395, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 16, objArr20);
        t = new a(((String) objArr20[0]).intern(), 19, 2012);
        Object[] objArr21 = new Object[1];
        aM((char) (View.getDefaultSize(0, 0) + 20695), 412 - KeyEvent.keyCodeFromString(""), 15 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr21);
        v = new a(((String) objArr21[0]).intern(), 20, 2013);
        Object[] objArr22 = new Object[1];
        aM((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 428 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 18, objArr22);
        y = new a(((String) objArr22[0]).intern(), 21, 2014);
        Object[] objArr23 = new Object[1];
        aM((char) (13454 - ((byte) KeyEvent.getModifierMetaStateMask())), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 447, View.MeasureSpec.getMode(0) + 25, objArr23);
        w = new a(((String) objArr23[0]).intern(), 22, 2015);
        Object[] objArr24 = new Object[1];
        aM((char) (64706 - Gravity.getAbsoluteGravity(0, 0)), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 472, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 19, objArr24);
        u = new a(((String) objArr24[0]).intern(), 23, 2016);
        Object[] objArr25 = new Object[1];
        aM((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), 492 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.indexOf("", "", 0, 0) + 12, objArr25);
        x = new a(((String) objArr25[0]).intern(), 24, 2017);
        Object[] objArr26 = new Object[1];
        aM((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 505 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 18 - ExpandableListView.getPackedPositionGroup(0L), objArr26);
        A = new a(((String) objArr26[0]).intern(), 25, 2018);
        Object[] objArr27 = new Object[1];
        aM((char) Color.alpha(0), View.combineMeasuredStates(0, 0) + 522, TextUtils.indexOf((CharSequence) "", '0', 0) + 18, objArr27);
        z = new a(((String) objArr27[0]).intern(), 26, 2019);
        Object[] objArr28 = new Object[1];
        aM((char) (29659 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), 539 - TextUtils.getOffsetBefore("", 0), KeyEvent.normalizeMetaState(0) + 33, objArr28);
        D = new a(((String) objArr28[0]).intern(), 27, 2020);
        Object[] objArr29 = new Object[1];
        aM((char) Color.alpha(0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 572, 33 - TextUtils.lastIndexOf("", '0', 0, 0), objArr29);
        C = new a(((String) objArr29[0]).intern(), 28, 3000);
        Object[] objArr30 = new Object[1];
        aM((char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), TextUtils.getTrimmedLength("") + 606, 36 - (Process.myTid() >> 22), objArr30);
        B = new a(((String) objArr30[0]).intern(), 29, AuthApiStatusCodes.AUTH_API_ACCESS_FORBIDDEN);
        Object[] objArr31 = new Object[1];
        aM((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), Color.red(0) + 642, (KeyEvent.getMaxKeyCode() >> 16) + 27, objArr31);
        I = new a(((String) objArr31[0]).intern(), 30, AuthApiStatusCodes.AUTH_API_CLIENT_ERROR);
        Object[] objArr32 = new Object[1];
        aM((char) (35056 - TextUtils.indexOf("", "")), 669 - (ViewConfiguration.getEdgeSlop() >> 16), 37 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr32);
        E = new a(((String) objArr32[0]).intern(), 31, AuthApiStatusCodes.AUTH_API_SERVER_ERROR);
        Object[] objArr33 = new Object[1];
        aM((char) View.MeasureSpec.makeMeasureSpec(0, 0), TextUtils.getOffsetAfter("", 0) + 706, (-16777180) - Color.rgb(0, 0, 0), objArr33);
        F = new a(((String) objArr33[0]).intern(), 32, AuthApiStatusCodes.AUTH_TOKEN_ERROR);
        Object[] objArr34 = new Object[1];
        aM((char) (((Process.getThreadPriority(0) + 20) >> 6) + 33082), ((byte) KeyEvent.getModifierMetaStateMask()) + 743, (KeyEvent.getMaxKeyCode() >> 16) + 25, objArr34);
        H = new a(((String) objArr34[0]).intern(), 33, AuthApiStatusCodes.AUTH_URL_RESOLUTION);
        Object[] objArr35 = new Object[1];
        aM((char) (ViewConfiguration.getJumpTapTimeout() >> 16), View.MeasureSpec.getSize(0) + 767, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 25, objArr35);
        G = new a(((String) objArr35[0]).intern(), 34, AuthApiStatusCodes.AUTH_APP_CERT_ERROR);
        Object[] objArr36 = new Object[1];
        aM((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 41047), 793 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), 26 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr36);
        M = new a(((String) objArr36[0]).intern(), 35, 3007);
        Object[] objArr37 = new Object[1];
        aM((char) (ExpandableListView.getPackedPositionGroup(0L) + 42223), 818 - View.MeasureSpec.getSize(0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 26, objArr37);
        J = new a(((String) objArr37[0]).intern(), 36, 3008);
        Object[] objArr38 = new Object[1];
        aM((char) View.MeasureSpec.getMode(0), 844 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 32 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr38);
        L = new a(((String) objArr38[0]).intern(), 37, 3009);
        Object[] objArr39 = new Object[1];
        aM((char) (TextUtils.getTrimmedLength("") + 64636), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 876, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 9, objArr39);
        K = new a(((String) objArr39[0]).intern(), 38, 3010);
        Object[] objArr40 = new Object[1];
        aM((char) ((ViewConfiguration.getTapTimeout() >> 16) + 26062), 886 - (ViewConfiguration.getTouchSlop() >> 8), KeyEvent.getDeadChar(0, 0) + 10, objArr40);
        N = new a(((String) objArr40[0]).intern(), 39, 3011);
        Object[] objArr41 = new Object[1];
        aM((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 896 - View.combineMeasuredStates(0, 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 11, objArr41);
        S = new a(((String) objArr41[0]).intern(), 40, 3012);
        Object[] objArr42 = new Object[1];
        aM((char) (ExpandableListView.getPackedPositionChild(0L) + 1), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 907, ((Process.getThreadPriority(0) + 20) >> 6) + 17, objArr42);
        O = new a(((String) objArr42[0]).intern(), 41, 3013);
        Object[] objArr43 = new Object[1];
        aM((char) TextUtils.getOffsetBefore("", 0), 924 - TextUtils.getOffsetAfter("", 0), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 15, objArr43);
        Q = new a(((String) objArr43[0]).intern(), 42, 3014);
        Object[] objArr44 = new Object[1];
        aM((char) (View.resolveSizeAndState(0, 0, 0) + 1855), TextUtils.getOffsetBefore("", 0) + 939, TextUtils.lastIndexOf("", '0', 0) + 27, objArr44);
        P = new a(((String) objArr44[0]).intern(), 43, 4000);
        Object[] objArr45 = new Object[1];
        aM((char) (27326 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 965 - View.resolveSizeAndState(0, 0, 0), 38 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr45);
        R = new a(((String) objArr45[0]).intern(), 44, 4001);
        Object[] objArr46 = new Object[1];
        aM((char) (26916 - AndroidCharacter.getMirror('0')), 1001 - ImageFormat.getBitsPerPixel(0), 35 - TextUtils.getOffsetAfter("", 0), objArr46);
        V = new a(((String) objArr46[0]).intern(), 45, 4002);
        Object[] objArr47 = new Object[1];
        aM((char) (27403 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), View.resolveSize(0, 0) + 1037, 35 - View.MeasureSpec.getSize(0), objArr47);
        X = new a(((String) objArr47[0]).intern(), 46, 4003);
        Object[] objArr48 = new Object[1];
        aM((char) View.MeasureSpec.makeMeasureSpec(0, 0), Color.green(0) + 1072, 38 - Color.blue(0), objArr48);
        T = new a(((String) objArr48[0]).intern(), 47, 4004);
        Object[] objArr49 = new Object[1];
        aM((char) (34437 - TextUtils.getTrimmedLength("")), 1110 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), TextUtils.indexOf("", "") + 36, objArr49);
        W = new a(((String) objArr49[0]).intern(), 48, 4005);
        Object[] objArr50 = new Object[1];
        aM((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 1147 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 33 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr50);
        U = new a(((String) objArr50[0]).intern(), 49, 4006);
        Object[] objArr51 = new Object[1];
        aM((char) (View.combineMeasuredStates(0, 0) + 66), ExpandableListView.getPackedPositionType(0L) + 1179, TextUtils.getTrimmedLength("") + 33, objArr51);
        ab = new a(((String) objArr51[0]).intern(), 50, 4007);
        Object[] objArr52 = new Object[1];
        aM((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 1212, 33 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr52);
        aa = new a(((String) objArr52[0]).intern(), 51, 4008);
        Object[] objArr53 = new Object[1];
        aM((char) (24392 - Gravity.getAbsoluteGravity(0, 0)), TextUtils.lastIndexOf("", '0', 0, 0) + 1245, ExpandableListView.getPackedPositionType(0L) + 37, objArr53);
        ac = new a(((String) objArr53[0]).intern(), 52, 4009);
        Object[] objArr54 = new Object[1];
        aM((char) Color.alpha(0), 1281 - Color.blue(0), 27 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr54);
        Z = new a(((String) objArr54[0]).intern(), 53, 6002);
        Object[] objArr55 = new Object[1];
        aM((char) (54488 - (ViewConfiguration.getEdgeSlop() >> 16)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 1308, Color.blue(0) + 26, objArr55);
        Y = new a(((String) objArr55[0]).intern(), 54, 6005);
        Object[] objArr56 = new Object[1];
        aM((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 1334 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), View.resolveSizeAndState(0, 0, 0) + 35, objArr56);
        ah = new a(((String) objArr56[0]).intern(), 55, 7000);
        Object[] objArr57 = new Object[1];
        aM((char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (Process.myTid() >> 22) + 1369, (-16777186) - Color.rgb(0, 0, 0), objArr57);
        ag = new a(((String) objArr57[0]).intern(), 56, 7001);
        Object[] objArr58 = new Object[1];
        aM((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 9505), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1399, View.getDefaultSize(0, 0) + 29, objArr58);
        ae = new a(((String) objArr58[0]).intern(), 57, 7002);
        Object[] objArr59 = new Object[1];
        aM((char) ((KeyEvent.getMaxKeyCode() >> 16) + 127), 1428 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 17 - (Process.myTid() >> 22), objArr59);
        af = new a(((String) objArr59[0]).intern(), 58, 7003);
        Object[] objArr60 = new Object[1];
        aM((char) ((-1) - ImageFormat.getBitsPerPixel(0)), 1445 - (Process.myTid() >> 22), 27 - TextUtils.indexOf("", "", 0), objArr60);
        ad = new a(((String) objArr60[0]).intern(), 59, 7004);
        Object[] objArr61 = new Object[1];
        aM((char) (ExpandableListView.getPackedPositionType(0L) + 5502), 1471 - TextUtils.lastIndexOf("", '0', 0, 0), 35 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr61);
        aj = new a(((String) objArr61[0]).intern(), 60, 7005);
        Object[] objArr62 = new Object[1];
        aM((char) (40250 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), ImageFormat.getBitsPerPixel(0) + 1508, 28 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr62);
        al = new a(((String) objArr62[0]).intern(), 61, 8000);
        Object[] objArr63 = new Object[1];
        aM((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 32469), 1535 - View.MeasureSpec.makeMeasureSpec(0, 0), 26 - TextUtils.indexOf("", ""), objArr63);
        am = new a(((String) objArr63[0]).intern(), 62, 9000);
        Object[] objArr64 = new Object[1];
        aM((char) ((-16735747) - Color.rgb(0, 0, 0)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1561, 25 - Color.blue(0), objArr64);
        ai = new a(((String) objArr64[0]).intern(), 63, 10000);
        Object[] objArr65 = new Object[1];
        aM((char) (Color.green(0) + 8072), 1586 - (ViewConfiguration.getEdgeSlop() >> 16), 25 - TextUtils.getCapsMode("", 0, 0), objArr65);
        ak = new a(((String) objArr65[0]).intern(), 64, 10001);
        Object[] objArr66 = new Object[1];
        aM((char) (MotionEvent.axisFromString("") + 12082), KeyEvent.normalizeMetaState(0) + 1611, ExpandableListView.getPackedPositionGroup(0L) + 18, objArr66);
        ap = new a(((String) objArr66[0]).intern(), 65, 10002);
        Object[] objArr67 = new Object[1];
        aM((char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), TextUtils.lastIndexOf("", '0', 0) + 1630, Drawable.resolveOpacity(0, 0) + 21, objArr67);
        ao = new a(((String) objArr67[0]).intern(), 66, 11001);
        Object[] objArr68 = new Object[1];
        aM((char) TextUtils.getTrimmedLength(""), Color.alpha(0) + 1650, 27 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr68);
        an = new a(((String) objArr68[0]).intern(), 67, 11002);
        Object[] objArr69 = new Object[1];
        aM((char) (39425 - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 1677 - View.resolveSize(0, 0), 17 - KeyEvent.normalizeMetaState(0), objArr69);
        aq = new a(((String) objArr69[0]).intern(), 68, 11003);
        Object[] objArr70 = new Object[1];
        aM((char) (38080 - TextUtils.indexOf((CharSequence) "", '0')), Drawable.resolveOpacity(0, 0) + 1694, 23 - ImageFormat.getBitsPerPixel(0), objArr70);
        ar = new a(((String) objArr70[0]).intern(), 69, 12000);
        Object[] objArr71 = new Object[1];
        aM((char) ((-1) - Process.getGidForName("")), 1719 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (ViewConfiguration.getTapTimeout() >> 16) + 43, objArr71);
        av = new a(((String) objArr71[0]).intern(), 70, 13001);
        Object[] objArr72 = new Object[1];
        aM((char) KeyEvent.keyCodeFromString(""), 1761 - (ViewConfiguration.getWindowTouchSlop() >> 8), 39 - (Process.myPid() >> 22), objArr72);
        aw = new a(((String) objArr72[0]).intern(), 71, 13002);
        Object[] objArr73 = new Object[1];
        aM((char) (TextUtils.indexOf((CharSequence) "", '0') + 40986), 1800 - KeyEvent.getDeadChar(0, 0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 43, objArr73);
        au = new a(((String) objArr73[0]).intern(), 72, 13003);
        Object[] objArr74 = new Object[1];
        aM((char) (51912 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), KeyEvent.keyCodeFromString("") + 1843, 24 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr74);
        as = new a(((String) objArr74[0]).intern(), 73, 14001);
        Object[] objArr75 = new Object[1];
        aM((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 52122), 1865 - ((byte) KeyEvent.getModifierMetaStateMask()), Process.getGidForName("") + 26, objArr75);
        at = new a(((String) objArr75[0]).intern(), 74, 14002);
        Object[] objArr76 = new Object[1];
        aM((char) (14139 - TextUtils.indexOf("", "", 0, 0)), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1891, 10 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr76);
        ay = new a(((String) objArr76[0]).intern(), 75, 14003);
        Object[] objArr77 = new Object[1];
        aM((char) Gravity.getAbsoluteGravity(0, 0), TextUtils.lastIndexOf("", '0', 0) + 1902, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 10, objArr77);
        az = new a(((String) objArr77[0]).intern(), 76, 14004);
        Object[] objArr78 = new Object[1];
        aM((char) (Color.argb(0, 0, 0, 0) + 14359), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 1911, 22 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr78);
        aA = new a(((String) objArr78[0]).intern(), 77, 15001);
        Object[] objArr79 = new Object[1];
        aM((char) (Drawable.resolveOpacity(0, 0) + 16963), 1934 - (KeyEvent.getMaxKeyCode() >> 16), TextUtils.indexOf((CharSequence) "", '0') + 30, objArr79);
        ax = new a(((String) objArr79[0]).intern(), 78, TapAndPayStatusCodes.TAP_AND_PAY_NO_ACTIVE_WALLET);
        Object[] objArr80 = new Object[1];
        aM((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 1963 - TextUtils.indexOf("", "", 0), (ViewConfiguration.getTapTimeout() >> 16) + 30, objArr80);
        aB = new a(((String) objArr80[0]).intern(), 79, TapAndPayStatusCodes.TAP_AND_PAY_TOKEN_NOT_FOUND);
        Object[] objArr81 = new Object[1];
        aM((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), 1993 - View.combineMeasuredStates(0, 0), 27 - Color.red(0), objArr81);
        aC = new a(((String) objArr81[0]).intern(), 80, TapAndPayStatusCodes.TAP_AND_PAY_INVALID_TOKEN_STATE);
        Object[] objArr82 = new Object[1];
        aM((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), 2020 - ExpandableListView.getPackedPositionType(0L), Color.argb(0, 0, 0, 0) + 32, objArr82);
        aE = new a(((String) objArr82[0]).intern(), 81, 16001);
        Object[] objArr83 = new Object[1];
        aM((char) Color.blue(0), 2052 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.getOffsetBefore("", 0) + 33, objArr83);
        aG = new a(((String) objArr83[0]).intern(), 82, 16002);
        Object[] objArr84 = new Object[1];
        aM((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), 2085 - Color.blue(0), Process.getGidForName("") + 28, objArr84);
        aD = new a(((String) objArr84[0]).intern(), 83, 17001);
        Object[] objArr85 = new Object[1];
        aM((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 2086), 2112 - Color.green(0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 19, objArr85);
        aF = new a(((String) objArr85[0]).intern(), 84, 18001);
        aI = e();
        int i2 = aJ + 25;
        aQ = i2 % 128;
        int i3 = i2 % 2;
    }

    private a(String str, int i2, int i3) {
        this.aL = i3;
    }

    public final int b() {
        int i2 = aQ + 73;
        aJ = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return this.aL;
        }
    }

    public final String c() {
        String obj = new StringBuilder().append(name()).append('(').append(this.aL).append(')').toString();
        int i2 = aJ + 35;
        aQ = i2 % 128;
        int i3 = i2 % 2;
        return obj;
    }

    /* renamed from: o.bb.a$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bb\a$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int e;

        static {
            b = 0;
            e = 1;
            int[] iArr = new int[a.values().length];
            a = iArr;
            try {
                iArr[a.e.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[a.c.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[a.a.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[a.b.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                a[a.d.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                a[a.h.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                a[a.i.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
            try {
                a[a.g.ordinal()] = 8;
            } catch (NoSuchFieldError e9) {
            }
            try {
                a[a.j.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
            try {
                a[a.f.ordinal()] = 10;
                int i = e;
                int i2 = (i ^ Opcodes.DSUB) + ((i & Opcodes.DSUB) << 1);
                b = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e11) {
            }
            try {
                a[a.m.ordinal()] = 11;
            } catch (NoSuchFieldError e12) {
            }
            try {
                a[a.n.ordinal()] = 12;
                int i3 = b;
                int i4 = (i3 ^ Opcodes.DDIV) + ((i3 & Opcodes.DDIV) << 1);
                e = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e13) {
            }
            try {
                a[a.k.ordinal()] = 13;
            } catch (NoSuchFieldError e14) {
            }
            try {
                a[a.l.ordinal()] = 14;
            } catch (NoSuchFieldError e15) {
            }
            try {
                a[a.f39o.ordinal()] = 15;
            } catch (NoSuchFieldError e16) {
            }
            try {
                a[a.p.ordinal()] = 16;
            } catch (NoSuchFieldError e17) {
            }
            try {
                a[a.r.ordinal()] = 17;
            } catch (NoSuchFieldError e18) {
            }
            try {
                a[a.q.ordinal()] = 18;
            } catch (NoSuchFieldError e19) {
            }
            try {
                a[a.s.ordinal()] = 19;
            } catch (NoSuchFieldError e20) {
            }
            try {
                a[a.t.ordinal()] = 20;
            } catch (NoSuchFieldError e21) {
            }
            try {
                a[a.v.ordinal()] = 21;
            } catch (NoSuchFieldError e22) {
            }
            try {
                a[a.y.ordinal()] = 22;
            } catch (NoSuchFieldError e23) {
            }
            try {
                a[a.w.ordinal()] = 23;
            } catch (NoSuchFieldError e24) {
            }
            try {
                a[a.u.ordinal()] = 24;
            } catch (NoSuchFieldError e25) {
            }
            try {
                a[a.x.ordinal()] = 25;
            } catch (NoSuchFieldError e26) {
            }
            try {
                a[a.A.ordinal()] = 26;
            } catch (NoSuchFieldError e27) {
            }
            try {
                a[a.z.ordinal()] = 27;
                int i6 = b + 37;
                e = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e28) {
            }
            try {
                a[a.D.ordinal()] = 28;
            } catch (NoSuchFieldError e29) {
            }
            try {
                a[a.C.ordinal()] = 29;
            } catch (NoSuchFieldError e30) {
            }
            try {
                a[a.B.ordinal()] = 30;
            } catch (NoSuchFieldError e31) {
            }
            try {
                a[a.I.ordinal()] = 31;
            } catch (NoSuchFieldError e32) {
            }
            try {
                a[a.E.ordinal()] = 32;
            } catch (NoSuchFieldError e33) {
            }
            try {
                a[a.F.ordinal()] = 33;
            } catch (NoSuchFieldError e34) {
            }
            try {
                a[a.H.ordinal()] = 34;
            } catch (NoSuchFieldError e35) {
            }
            try {
                a[a.G.ordinal()] = 35;
            } catch (NoSuchFieldError e36) {
            }
            try {
                a[a.M.ordinal()] = 36;
            } catch (NoSuchFieldError e37) {
            }
            try {
                a[a.J.ordinal()] = 37;
            } catch (NoSuchFieldError e38) {
            }
            try {
                a[a.L.ordinal()] = 38;
            } catch (NoSuchFieldError e39) {
            }
            try {
                a[a.K.ordinal()] = 39;
            } catch (NoSuchFieldError e40) {
            }
            try {
                a[a.S.ordinal()] = 40;
            } catch (NoSuchFieldError e41) {
            }
            try {
                a[a.N.ordinal()] = 41;
            } catch (NoSuchFieldError e42) {
            }
            try {
                a[a.Q.ordinal()] = 42;
            } catch (NoSuchFieldError e43) {
            }
            try {
                a[a.O.ordinal()] = 43;
            } catch (NoSuchFieldError e44) {
            }
            try {
                a[a.P.ordinal()] = 44;
            } catch (NoSuchFieldError e45) {
            }
            try {
                a[a.R.ordinal()] = 45;
            } catch (NoSuchFieldError e46) {
            }
            try {
                a[a.V.ordinal()] = 46;
            } catch (NoSuchFieldError e47) {
            }
            try {
                a[a.X.ordinal()] = 47;
            } catch (NoSuchFieldError e48) {
            }
            try {
                a[a.T.ordinal()] = 48;
            } catch (NoSuchFieldError e49) {
            }
            try {
                a[a.W.ordinal()] = 49;
            } catch (NoSuchFieldError e50) {
            }
            try {
                a[a.U.ordinal()] = 50;
            } catch (NoSuchFieldError e51) {
            }
            try {
                a[a.ab.ordinal()] = 51;
            } catch (NoSuchFieldError e52) {
            }
            try {
                a[a.aa.ordinal()] = 52;
            } catch (NoSuchFieldError e53) {
            }
            try {
                a[a.ac.ordinal()] = 53;
            } catch (NoSuchFieldError e54) {
            }
            try {
                a[a.Z.ordinal()] = 54;
            } catch (NoSuchFieldError e55) {
            }
            try {
                a[a.Y.ordinal()] = 55;
            } catch (NoSuchFieldError e56) {
            }
            try {
                a[a.ah.ordinal()] = 56;
            } catch (NoSuchFieldError e57) {
            }
            try {
                a[a.ag.ordinal()] = 57;
            } catch (NoSuchFieldError e58) {
            }
            try {
                a[a.ae.ordinal()] = 58;
            } catch (NoSuchFieldError e59) {
            }
            try {
                a[a.af.ordinal()] = 59;
            } catch (NoSuchFieldError e60) {
            }
            try {
                a[a.ad.ordinal()] = 60;
            } catch (NoSuchFieldError e61) {
            }
            try {
                a[a.aj.ordinal()] = 61;
            } catch (NoSuchFieldError e62) {
            }
            try {
                a[a.al.ordinal()] = 62;
            } catch (NoSuchFieldError e63) {
            }
            try {
                a[a.am.ordinal()] = 63;
                int i8 = e + 55;
                b = i8 % 128;
                int i9 = i8 % 2;
            } catch (NoSuchFieldError e64) {
            }
            try {
                a[a.ai.ordinal()] = 64;
            } catch (NoSuchFieldError e65) {
            }
            try {
                a[a.ak.ordinal()] = 65;
            } catch (NoSuchFieldError e66) {
            }
            try {
                a[a.ap.ordinal()] = 66;
            } catch (NoSuchFieldError e67) {
            }
            try {
                a[a.ao.ordinal()] = 67;
            } catch (NoSuchFieldError e68) {
            }
            try {
                a[a.an.ordinal()] = 68;
            } catch (NoSuchFieldError e69) {
            }
            try {
                a[a.aq.ordinal()] = 69;
            } catch (NoSuchFieldError e70) {
            }
            try {
                a[a.ar.ordinal()] = 70;
            } catch (NoSuchFieldError e71) {
            }
            try {
                a[a.av.ordinal()] = 71;
            } catch (NoSuchFieldError e72) {
            }
            try {
                a[a.aw.ordinal()] = 72;
            } catch (NoSuchFieldError e73) {
            }
            try {
                a[a.au.ordinal()] = 73;
            } catch (NoSuchFieldError e74) {
            }
            try {
                a[a.as.ordinal()] = 74;
                int i10 = b;
                int i11 = (i10 ^ 37) + ((i10 & 37) << 1);
                e = i11 % 128;
                if (i11 % 2 != 0) {
                }
            } catch (NoSuchFieldError e75) {
            }
            try {
                a[a.at.ordinal()] = 75;
            } catch (NoSuchFieldError e76) {
            }
            try {
                a[a.ay.ordinal()] = 76;
            } catch (NoSuchFieldError e77) {
            }
            try {
                a[a.az.ordinal()] = 77;
            } catch (NoSuchFieldError e78) {
            }
            try {
                a[a.aA.ordinal()] = 78;
            } catch (NoSuchFieldError e79) {
            }
            try {
                a[a.ax.ordinal()] = 79;
            } catch (NoSuchFieldError e80) {
            }
            try {
                a[a.aB.ordinal()] = 80;
            } catch (NoSuchFieldError e81) {
            }
            try {
                a[a.aC.ordinal()] = 81;
            } catch (NoSuchFieldError e82) {
            }
            try {
                a[a.aE.ordinal()] = 82;
            } catch (NoSuchFieldError e83) {
            }
            try {
                a[a.aG.ordinal()] = 83;
            } catch (NoSuchFieldError e84) {
            }
            try {
                a[a.aD.ordinal()] = 84;
                int i12 = b + 99;
                e = i12 % 128;
                if (i12 % 2 == 0) {
                }
            } catch (NoSuchFieldError e85) {
            }
            try {
                a[a.aF.ordinal()] = 85;
                int i13 = e;
                int i14 = ((i13 | 29) << 1) - (i13 ^ 29);
                b = i14 % 128;
                int i15 = i14 % 2;
            } catch (NoSuchFieldError e86) {
            }
        }
    }

    public final AntelopErrorCode d() {
        int i2 = aQ + 43;
        aJ = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                switch (AnonymousClass3.a[ordinal()]) {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                        return AntelopErrorCode.InternalError;
                    case 5:
                        return AntelopErrorCode.AppIntegrityCheckFailed;
                    case 6:
                    case 7:
                        return AntelopErrorCode.InternalError;
                    case 8:
                        return AntelopErrorCode.OperationRefused;
                    case 9:
                        return AntelopErrorCode.NetworkNotAvailable;
                    case 10:
                        return AntelopErrorCode.InternalError;
                    case 11:
                        return AntelopErrorCode.AndroidPermissionNotGranted;
                    case 12:
                        return AntelopErrorCode.DeviceArchitectureNotSupported;
                    case 13:
                    case 14:
                        return AntelopErrorCode.WalletLocked;
                    case 15:
                        return AntelopErrorCode.ActivationRequired;
                    case 16:
                        return AntelopErrorCode.TransactionOnGoing;
                    case 17:
                        return AntelopErrorCode.OperationOnGoing;
                    case 18:
                        return AntelopErrorCode.InternalError;
                    case 19:
                        return AntelopErrorCode.WalletDeleted;
                    case 20:
                        AntelopErrorCode antelopErrorCode = AntelopErrorCode.InternalError;
                        int i3 = aQ + 17;
                        aJ = i3 % 128;
                        switch (i3 % 2 != 0 ? '*' : 'L') {
                            case '*':
                                throw null;
                            default:
                                return antelopErrorCode;
                        }
                    case 21:
                        return AntelopErrorCode.ActivationRequired;
                    case 22:
                        return AntelopErrorCode.InternalError;
                    case 23:
                        AntelopErrorCode antelopErrorCode2 = AntelopErrorCode.InternalError;
                        int i4 = aJ + 81;
                        aQ = i4 % 128;
                        switch (i4 % 2 != 0) {
                            case false:
                                int i5 = 64 / 0;
                                return antelopErrorCode2;
                            default:
                                return antelopErrorCode2;
                        }
                    case 24:
                        return AntelopErrorCode.InvalidConfiguration;
                    case 25:
                        return AntelopErrorCode.InternalError;
                    case 26:
                        return AntelopErrorCode.IssuerIdNotDefined;
                    case 27:
                        return AntelopErrorCode.InternalError;
                    case 28:
                        return AntelopErrorCode.InternalError;
                    case 29:
                    case 30:
                    case 31:
                        return AntelopErrorCode.GooglePlayServiceError;
                    case 32:
                        return AntelopErrorCode.InternalError;
                    case 33:
                        return AntelopErrorCode.InternalError;
                    case 34:
                        return AntelopErrorCode.GooglePlayServicesMissing;
                    case 35:
                        return AntelopErrorCode.GooglePlayServicesInvalid;
                    case 36:
                        return AntelopErrorCode.GooglePlayServicesDisabled;
                    case 37:
                        return AntelopErrorCode.GooglePlayServicesUpdating;
                    case 38:
                        return AntelopErrorCode.GooglePlayServicesUpdateRequired;
                    case 39:
                        return AntelopErrorCode.HmsMissing;
                    case 40:
                        return AntelopErrorCode.HmsDisabled;
                    case 41:
                        return AntelopErrorCode.HmsInvalid;
                    case 42:
                        return AntelopErrorCode.HmsNotSupported;
                    case 43:
                        return AntelopErrorCode.HmsUpdateRequired;
                    case 44:
                        return AntelopErrorCode.OperationOnGoing;
                    case 45:
                        return AntelopErrorCode.InternalError;
                    case 46:
                        return AntelopErrorCode.InternalError;
                    case 47:
                        return AntelopErrorCode.InternalError;
                    case 48:
                        return AntelopErrorCode.InternalError;
                    case 49:
                        return AntelopErrorCode.NoProductAllowed;
                    case 50:
                        return AntelopErrorCode.InvalidActivationCode;
                    case 51:
                        return AntelopErrorCode.ExpiredActivationCode;
                    case 52:
                        return AntelopErrorCode.LockedActivationCode;
                    case Opcodes.SALOAD /* 53 */:
                        return AntelopErrorCode.AlreadyUsedActivationCode;
                    case Opcodes.ISTORE /* 54 */:
                        return AntelopErrorCode.InternalError;
                    case 55:
                        return AntelopErrorCode.InternalError;
                    case 56:
                        return AntelopErrorCode.InternalError;
                    case 57:
                        return AntelopErrorCode.InternalError;
                    case Opcodes.ASTORE /* 58 */:
                        return AntelopErrorCode.InternalError;
                    case 59:
                        return AntelopErrorCode.InternalError;
                    case 60:
                        return AntelopErrorCode.InternalError;
                    case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                        return AntelopErrorCode.CustomerCredentialsInvalid;
                    case 62:
                        return AntelopErrorCode.CustomerCredentialsInvalid;
                    case 63:
                        return AntelopErrorCode.CustomerCredentialsInvalid;
                    case 64:
                        return AntelopErrorCode.InternalError;
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        return AntelopErrorCode.InternalError;
                    case 66:
                        return AntelopErrorCode.InternalError;
                    case 67:
                        return AntelopErrorCode.InternalError;
                    case 68:
                        return AntelopErrorCode.InternalError;
                    case 69:
                        return AntelopErrorCode.InternalError;
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        return AntelopErrorCode.InternalError;
                    case 71:
                        return AntelopErrorCode.InvalidActivationCode;
                    case 72:
                        return AntelopErrorCode.OperationRefused;
                    case 73:
                        return AntelopErrorCode.ExpiredActivationCode;
                    case 74:
                        return AntelopErrorCode.OperationRefused;
                    case 75:
                        return AntelopErrorCode.InternalError;
                    case Base64.mimeLineLength /* 76 */:
                        return AntelopErrorCode.CardLocked;
                    case 77:
                        return AntelopErrorCode.CardDeleted;
                    case 78:
                        return AntelopErrorCode.InternalError;
                    case Opcodes.IASTORE /* 79 */:
                        return AntelopErrorCode.ScaCancelledFromBackend;
                    case 80:
                        return AntelopErrorCode.ScaDuplicated;
                    case Opcodes.FASTORE /* 81 */:
                        return AntelopErrorCode.ScaTimeout;
                    case Opcodes.DASTORE /* 82 */:
                        return AntelopErrorCode.InternalError;
                    case Opcodes.AASTORE /* 83 */:
                        return AntelopErrorCode.InternalError;
                    case Opcodes.BASTORE /* 84 */:
                        return AntelopErrorCode.GooglePayWalletNotAvailable;
                    case Opcodes.CASTORE /* 85 */:
                        return AntelopErrorCode.OperationRefused;
                    default:
                        return AntelopErrorCode.InternalError;
                }
            default:
                int i6 = AnonymousClass3.a[ordinal()];
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void aM(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 702
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bb.a.aM(char, int, int, java.lang.Object[]):void");
    }
}
