package bc.org.bouncycastle.math.ec.rfc8032;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.e5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\rfc8032\d.smali */
abstract class d {
    static void a(int i, int i2, int[] iArr, int[] iArr2, int[] iArr3) {
        int i3 = i;
        int i4 = i2 >>> 5;
        int i5 = i2 & 31;
        long j = 0;
        if (i5 == 0) {
            long j2 = 0;
            for (int i6 = i4; i6 <= i3; i6++) {
                int i7 = i6 - i4;
                long j3 = j2 + (iArr3[i6] & 4294967295L) + (iArr2[i7] & 4294967295L);
                iArr3[i6] = (int) j3;
                j2 = j3 >>> 32;
                long j4 = j + (iArr[i6] & 4294967295L) + (iArr3[i7] & 4294967295L) + (iArr3[i7] & 4294967295L);
                iArr[i6] = (int) j4;
                j = j4 >>> 32;
            }
            return;
        }
        int i8 = i4;
        int i9 = 0;
        int i10 = 0;
        int i11 = 0;
        long j5 = 0;
        while (i8 <= i3) {
            int i12 = i8 - i4;
            int i13 = iArr3[i12];
            int i14 = -i5;
            int i15 = i4;
            int i16 = iArr2[i12];
            long j6 = j5 + (iArr3[i8] & 4294967295L) + (((i16 << i5) | (i10 >>> i14)) & 4294967295L);
            iArr3[i8] = (int) j6;
            j5 = j6 >>> 32;
            int i17 = iArr3[i12];
            long j7 = j + (iArr[i8] & 4294967295L) + (((i13 << i5) | (i9 >>> i14)) & 4294967295L) + (((i11 >>> i14) | (i17 << i5)) & 4294967295L);
            iArr[i8] = (int) j7;
            j = j7 >>> 32;
            i8++;
            i10 = i16;
            i11 = i17;
            i9 = i13;
            i4 = i15;
            i3 = i;
        }
    }

    static int b(int i, int[] iArr) {
        while (i > 0 && iArr[i] == 0) {
            i--;
        }
        return ((i * 32) + 32) - e5.a(iArr[i]);
    }

    static void b(int i, int i2, int[] iArr, int[] iArr2, int[] iArr3) {
        int i3 = i;
        int i4 = i2 >>> 5;
        int i5 = i2 & 31;
        long j = 0;
        if (i5 == 0) {
            long j2 = 0;
            for (int i6 = i4; i6 <= i3; i6++) {
                int i7 = i6 - i4;
                long j3 = (j2 + (iArr3[i6] & 4294967295L)) - (iArr2[i7] & 4294967295L);
                iArr3[i6] = (int) j3;
                j2 = j3 >> 32;
                long j4 = ((j + (iArr[i6] & 4294967295L)) - (iArr3[i7] & 4294967295L)) - (iArr3[i7] & 4294967295L);
                iArr[i6] = (int) j4;
                j = j4 >> 32;
            }
            return;
        }
        int i8 = i4;
        int i9 = 0;
        int i10 = 0;
        int i11 = 0;
        long j5 = 0;
        while (i8 <= i3) {
            int i12 = i8 - i4;
            int i13 = iArr3[i12];
            int i14 = -i5;
            int i15 = i4;
            int i16 = iArr2[i12];
            long j6 = (j5 + (iArr3[i8] & 4294967295L)) - (((i16 << i5) | (i10 >>> i14)) & 4294967295L);
            iArr3[i8] = (int) j6;
            j5 = j6 >> 32;
            int i17 = iArr3[i12];
            long j7 = ((j + (iArr[i8] & 4294967295L)) - (((i13 << i5) | (i9 >>> i14)) & 4294967295L)) - (((i11 >>> i14) | (i17 << i5)) & 4294967295L);
            iArr[i8] = (int) j7;
            j = j7 >> 32;
            i8++;
            i10 = i16;
            i11 = i17;
            i9 = i13;
            i4 = i15;
            i3 = i;
        }
    }

    static void a(int i, int i2, int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4) {
        int i3 = i;
        int i4 = i2 >>> 5;
        int i5 = i2 & 31;
        long j = 0;
        if (i5 == 0) {
            long j2 = 0;
            for (int i6 = i4; i6 <= i3; i6++) {
                int i7 = i6 - i4;
                long j3 = j2 + (iArr[i6] & 4294967295L) + (iArr3[i7] & 4294967295L);
                long j4 = j + (iArr2[i6] & 4294967295L) + (iArr4[i7] & 4294967295L);
                iArr[i6] = (int) j3;
                j2 = j3 >>> 32;
                iArr2[i6] = (int) j4;
                j = j4 >>> 32;
            }
            return;
        }
        int i8 = i4;
        int i9 = 0;
        int i10 = 0;
        long j5 = 0;
        while (i8 <= i3) {
            int i11 = i8 - i4;
            int i12 = iArr3[i11];
            int i13 = iArr4[i11];
            long j6 = j5 + (iArr[i8] & 4294967295L);
            long j7 = j6 + (((i9 >>> (-i5)) | (i12 << i5)) & 4294967295L);
            long j8 = j + (iArr2[i8] & 4294967295L) + (((i10 >>> r3) | (i13 << i5)) & 4294967295L);
            iArr[i8] = (int) j7;
            j5 = j7 >>> 32;
            iArr2[i8] = (int) j8;
            j = j8 >>> 32;
            i8++;
            i10 = i13;
            i9 = i12;
            i4 = i4;
            i3 = i;
        }
    }

    static void b(int i, int i2, int[] iArr, int[] iArr2, int[] iArr3, int[] iArr4) {
        int i3 = i;
        int i4 = i2 >>> 5;
        int i5 = i2 & 31;
        long j = 0;
        if (i5 == 0) {
            long j2 = 0;
            for (int i6 = i4; i6 <= i3; i6++) {
                int i7 = i6 - i4;
                long j3 = (j2 + (iArr[i6] & 4294967295L)) - (iArr3[i7] & 4294967295L);
                long j4 = (j + (iArr2[i6] & 4294967295L)) - (iArr4[i7] & 4294967295L);
                iArr[i6] = (int) j3;
                j2 = j3 >> 32;
                iArr2[i6] = (int) j4;
                j = j4 >> 32;
            }
            return;
        }
        int i8 = i4;
        int i9 = 0;
        int i10 = 0;
        long j5 = 0;
        while (i8 <= i3) {
            int i11 = i8 - i4;
            int i12 = iArr3[i11];
            int i13 = iArr4[i11];
            long j6 = j5 + (iArr[i8] & 4294967295L);
            long j7 = j6 - (((i9 >>> (-i5)) | (i12 << i5)) & 4294967295L);
            long j8 = (j + (iArr2[i8] & 4294967295L)) - (((i10 >>> r3) | (i13 << i5)) & 4294967295L);
            iArr[i8] = (int) j7;
            j5 = j7 >> 32;
            iArr2[i8] = (int) j8;
            j = j8 >> 32;
            i8++;
            i10 = i13;
            i9 = i12;
            i4 = i4;
            i3 = i;
        }
    }

    static int a(int i, int[] iArr) {
        int i2 = iArr[i] >> 31;
        while (i > 0 && iArr[i] == i2) {
            i--;
        }
        return ((i * 32) + 32) - e5.a(iArr[i] ^ i2);
    }

    static boolean a(int i, int[] iArr, int[] iArr2) {
        do {
            int i2 = iArr[i] - 2147483648;
            int i3 = iArr2[i] - 2147483648;
            if (i2 < i3) {
                return true;
            }
            if (i2 > i3) {
                return false;
            }
            i--;
        } while (i >= 0);
        return false;
    }
}
