package o.fn;

import android.text.TextUtils;
import android.view.ViewConfiguration;
import java.util.HashMap;
import java.util.List;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\e.smali */
public final class e extends c<e> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int[] c;
    private final HashMap<String, o.s.c> e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        a();
        TextUtils.indexOf((CharSequence) "", '0');
        int i = b + 25;
        a = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        c = new int[]{-1771114709, -143639914, -155415637, 350779534, -414666390, -1459052366, -338605080, -94255970, 183961744, -1717534825, 2048880354, 583830190, -216799690, -658896670, -589057497, -1984068048, -855152227, -1019867667};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = 116 - r9
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = 4 - r7
            byte[] r0 = o.fn.e.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r9 = r7
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            goto L30
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r10
            r10 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L30:
            int r7 = r7 + 1
            int r9 = r9 + r8
            r8 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.e.g(int, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = 49;
    }

    e() {
        super(false);
        this.e = new HashMap<>();
    }

    public e(List<o.s.c> list) {
        super(true);
        HashMap<String, o.s.c> hashMap = new HashMap<>();
        this.e = hashMap;
        Object[] objArr = new Object[1];
        f(new int[]{-445635767, -2119370054, -98128261, -1135849919, 845366001, 2126619740, -11638216, -842022537, -697511805, -734376859}, 17 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(new int[]{-445635767, -2119370054, -98128261, -1135849919, 845366001, 2126619740, -11638216, -842022537, -697511805, -734376859}, TextUtils.indexOf("", "", 0, 0) + 17, objArr2);
        hashMap.put(intern, new o.s.c(((String) objArr2[0]).intern(), new f[0][]));
        for (o.s.c cVar : list) {
            this.e.put(cVar.d(), cVar);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean a(o.fn.e r9) {
        /*
            r8 = this;
            java.util.HashMap<java.lang.String, o.s.c> r0 = r8.e
            int r0 = r0.size()
            java.util.HashMap<java.lang.String, o.s.c> r1 = r9.e
            int r1 = r1.size()
            r2 = 0
            if (r0 == r1) goto L12
        L11:
            return r2
        L12:
            java.util.HashMap<java.lang.String, o.s.c> r0 = r8.e
            java.util.Set r0 = r0.keySet()
            java.util.HashMap<java.lang.String, o.s.c> r1 = r9.e
            java.util.Set r1 = r1.keySet()
            java.util.Iterator r1 = r1.iterator()
        L22:
            boolean r3 = r1.hasNext()
            r4 = 1
            if (r3 == 0) goto L2b
            r3 = r4
            goto L2c
        L2b:
            r3 = r2
        L2c:
            switch(r3) {
                case 0: goto L3d;
                default: goto L2f;
            }
        L2f:
            int r3 = o.fn.e.b
            int r3 = r3 + 119
            int r4 = r3 % 128
            o.fn.e.a = r4
            int r3 = r3 % 2
            if (r3 == 0) goto Lcf
            goto Lc0
        L3d:
            java.util.HashMap<java.lang.String, o.s.c> r0 = r8.e
            java.util.Set r0 = r0.entrySet()
            java.util.Iterator r0 = r0.iterator()
        L47:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L4f
            r1 = 6
            goto L51
        L4f:
            r1 = 35
        L51:
            switch(r1) {
                case 35: goto L61;
                default: goto L54;
            }
        L54:
            int r1 = o.fn.e.a
            int r1 = r1 + 93
            int r3 = r1 % 128
            o.fn.e.b = r3
            int r1 = r1 % 2
            if (r1 == 0) goto L62
            goto L62
        L61:
            return r4
        L62:
            java.lang.Object r1 = r0.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.util.HashMap<java.lang.String, o.s.c> r3 = r9.e
            java.lang.Object r5 = r1.getKey()
            java.lang.String r5 = (java.lang.String) r5
            java.lang.Object r3 = r3.get(r5)
            o.s.c r3 = (o.s.c) r3
            if (r3 != 0) goto L79
            return r2
        L79:
            java.lang.Object r1 = r1.getValue()
            o.s.c r1 = (o.s.c) r1
            int r5 = r1.e()
            int r6 = r3.e()
            if (r5 == r6) goto L8c
            r5 = 49
            goto L8d
        L8c:
            r5 = r2
        L8d:
            switch(r5) {
                case 0: goto L91;
                default: goto L90;
            }
        L90:
            return r2
        L91:
            int r5 = o.fn.e.a
            int r5 = r5 + 27
            int r6 = r5 % 128
            o.fn.e.b = r6
            int r5 = r5 % 2
            r5 = r2
        L9d:
            int r6 = r1.e()
            if (r5 >= r6) goto Lbf
            o.i.f[] r6 = r1.e(r5)
            o.i.f[] r7 = r3.e(r5)
            boolean r6 = java.util.Arrays.equals(r6, r7)
            if (r6 != 0) goto Lbc
            int r9 = o.fn.e.b
            int r9 = r9 + 57
            int r0 = r9 % 128
            o.fn.e.a = r0
            int r9 = r9 % 2
            return r2
        Lbc:
            int r5 = r5 + 1
            goto L9d
        Lbf:
            goto L47
        Lc0:
            java.lang.Object r3 = r1.next()
            java.lang.String r3 = (java.lang.String) r3
            boolean r3 = r0.contains(r3)
            if (r3 != 0) goto Lcd
            return r2
        Lcd:
            goto L22
        Lcf:
            java.lang.Object r9 = r1.next()
            java.lang.String r9 = (java.lang.String) r9
            r0.contains(r9)
            r9 = 0
            r9.hashCode()     // Catch: java.lang.Throwable -> Ldd
            throw r9     // Catch: java.lang.Throwable -> Ldd
        Ldd:
            r9 = move-exception
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.e.a(o.fn.e):boolean");
    }

    public final HashMap<String, o.s.c> e() {
        int i = a + 59;
        int i2 = i % 128;
        b = i2;
        int i3 = i % 2;
        HashMap<String, o.s.c> hashMap = this.e;
        int i4 = i2 + 11;
        a = i4 % 128;
        switch (i4 % 2 == 0 ? '0' : 'Z') {
            case 'Z':
                return hashMap;
            default:
                int i5 = 86 / 0;
                return hashMap;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 996
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.e.f(int[], int, java.lang.Object[]):void");
    }
}
