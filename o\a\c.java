package o.a;

import androidx.core.view.MotionEventCompat;
import androidx.core.view.ViewCompat;
import java.io.BufferedInputStream;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\a\c.smali */
public final class c extends FilterInputStream {
    private static final short e = (short) ((Math.sqrt(5.0d) - 1.0d) * Math.pow(2.0d, 15.0d));
    private byte[] a;
    private byte[] b;
    private int c;
    private byte[] d;
    private int f;
    private int g;
    private int h;
    private int i;
    private int j;
    private int l;
    private int m;

    /* renamed from: o, reason: collision with root package name */
    private int f33o;

    public c(InputStream inputStream, int[] iArr, int i, byte[] bArr, int i2, int i3) throws IOException {
        super(new BufferedInputStream(inputStream, 4096));
        this.j = Integer.MAX_VALUE;
        this.d = new byte[8];
        this.b = new byte[8];
        this.a = new byte[8];
        this.c = 8;
        this.g = 8;
        this.h = Math.min(Math.max(i2, 5), 16);
        this.f = i3;
        if (i3 == 3) {
            System.arraycopy(bArr, 0, this.b, 0, 8);
        }
        long j = ((iArr[0] & 4294967295L) << 32) | (4294967295L & iArr[1]);
        if (i != 0) {
            int i4 = (int) j;
            this.i = i4;
            this.f33o = i4 * i;
            this.l = i4 ^ i;
            this.m = (int) (j >> 32);
            return;
        }
        this.i = (int) j;
        long j2 = j >> 3;
        short s = e;
        this.f33o = (int) ((s * j2) >> 32);
        this.l = (int) (j >> 32);
        this.m = (int) (j2 + s);
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read() throws IOException {
        a();
        int i = this.c;
        if (i >= this.g) {
            return -1;
        }
        byte[] bArr = this.d;
        this.c = i + 1;
        return bArr[i] & 255;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int read(byte[] bArr, int i, int i2) throws IOException {
        int i3 = i + i2;
        for (int i4 = i; i4 < i3; i4++) {
            a();
            int i5 = this.c;
            if (i5 >= this.g) {
                if (i4 == i) {
                    return -1;
                }
                return i2 - (i3 - i4);
            }
            byte[] bArr2 = this.d;
            this.c = i5 + 1;
            bArr[i4] = bArr2[i5];
        }
        return i2;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final long skip(long j) throws IOException {
        long j2 = 0;
        while (j2 < j && read() != -1) {
            j2++;
        }
        return j2;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final int available() throws IOException {
        a();
        return this.g - this.c;
    }

    @Override // java.io.FilterInputStream, java.io.InputStream
    public final boolean markSupported() {
        return false;
    }

    private void c() {
        if (this.f == 3) {
            byte[] bArr = this.d;
            System.arraycopy(bArr, 0, this.a, 0, bArr.length);
        }
        byte[] bArr2 = this.d;
        int i = ((bArr2[0] << 24) & ViewCompat.MEASURED_STATE_MASK) + ((bArr2[1] << Tnaf.POW_2_WIDTH) & 16711680) + ((bArr2[2] << 8) & MotionEventCompat.ACTION_POINTER_INDEX_MASK) + (bArr2[3] & 255);
        int i2 = ((-16777216) & (bArr2[4] << 24)) + (16711680 & (bArr2[5] << Tnaf.POW_2_WIDTH)) + (65280 & (bArr2[6] << 8)) + (bArr2[7] & 255);
        int i3 = 0;
        while (true) {
            int i4 = this.h;
            if (i3 >= i4) {
                break;
            }
            short s = e;
            i2 -= ((((i4 - i3) * s) + i) ^ ((i << 4) + this.l)) ^ ((i >>> 5) + this.m);
            i -= (((i2 << 4) + this.i) ^ ((s * (i4 - i3)) + i2)) ^ ((i2 >>> 5) + this.f33o);
            i3++;
        }
        byte[] bArr3 = this.d;
        bArr3[0] = (byte) (i >> 24);
        bArr3[1] = (byte) (i >> 16);
        bArr3[2] = (byte) (i >> 8);
        bArr3[3] = (byte) i;
        bArr3[4] = (byte) (i2 >> 24);
        bArr3[5] = (byte) (i2 >> 16);
        bArr3[6] = (byte) (i2 >> 8);
        bArr3[7] = (byte) i2;
        if (this.f != 3) {
            return;
        }
        for (int i5 = 0; i5 < 8; i5++) {
            byte[] bArr4 = this.d;
            bArr4[i5] = (byte) (bArr4[i5] ^ this.b[i5]);
        }
        byte[] bArr5 = this.a;
        System.arraycopy(bArr5, 0, this.b, 0, bArr5.length);
    }

    private int a() throws IOException {
        if (this.j == Integer.MAX_VALUE) {
            this.j = ((FilterInputStream) this).in.read();
        }
        if (this.c == 8) {
            byte[] bArr = this.d;
            int i = this.j;
            bArr[0] = (byte) i;
            if (i < 0) {
                throw new IllegalStateException("unexpected block size");
            }
            int i2 = 1;
            do {
                int read = ((FilterInputStream) this).in.read(this.d, i2, 8 - i2);
                if (read <= 0) {
                    break;
                }
                i2 += read;
            } while (i2 < 8);
            if (i2 < 8) {
                throw new IllegalStateException("unexpected block size");
            }
            c();
            int read2 = ((FilterInputStream) this).in.read();
            this.j = read2;
            this.c = 0;
            this.g = read2 < 0 ? 8 - (this.d[7] & 255) : 8;
        }
        return this.g;
    }
}
