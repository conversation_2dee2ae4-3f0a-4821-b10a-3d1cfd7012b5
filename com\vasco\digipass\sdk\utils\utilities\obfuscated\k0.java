package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k0.smali */
abstract class k0 {
    final Class a;

    k0(Class cls) {
        this.a = cls;
    }

    public final boolean equals(Object obj) {
        return this == obj;
    }

    public final int hashCode() {
        return super.hashCode();
    }
}
