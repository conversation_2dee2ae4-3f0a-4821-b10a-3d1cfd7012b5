package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.app.Activity;
import android.content.Context;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import fr.antelop.sdk.digitalcard.SecureCardPushToSamsungPay;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.j;
import o.ee.g;
import o.eo.f;
import o.ep.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\d.smali */
public final class d extends o.er.d<o.ep.b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] e;
    private static boolean g;
    private static int h;
    private static int i;
    private static boolean j;
    private final boolean b;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        h = 1;
        h();
        PointF.length(0.0f, 0.0f);
        int i2 = i + 89;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    static void h() {
        e = new char[]{61756, 61774, 61786, 61788, 61794, 61785, 61776, 61759, 61798, 61778, 61789, 61793, 61782, 61772, 61750, 61795, 61783, 61740, 61779, 61784, 61742, 61711, 61722, 61762, 61773, 61787, 61791, 61777, 61768};
        g = true;
        j = true;
        a = 782103023;
    }

    static void init$0() {
        $$d = new byte[]{114, -113, -41, 111};
        $$e = Opcodes.GETFIELD;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = 3 - r6
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.$$d
            int r7 = 121 - r7
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            int r6 = r6 + 1
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.n(byte, byte, short, java.lang.Object[]):void");
    }

    static /* synthetic */ o.eo.e a(d dVar) {
        int i2 = i + 7;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.eo.e eVar = dVar.c;
        int i4 = h + Opcodes.LSUB;
        i = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    static /* synthetic */ o.eo.e b(d dVar) {
        int i2 = h + 55;
        i = i2 % 128;
        int i3 = i2 % 2;
        o.eo.e eVar = dVar.c;
        int i4 = h + 47;
        i = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    @Override // o.er.d
    public final /* synthetic */ o.ep.b e(Context context) {
        int i2 = i + Opcodes.DNEG;
        h = i2 % 128;
        boolean z = i2 % 2 != 0;
        o.ep.b d = d(context);
        switch (z) {
            case false:
                int i3 = 20 / 0;
            default:
                return d;
        }
    }

    public d(o.eo.e eVar, o.el.e eVar2, boolean z) {
        super(eVar, eVar2);
        this.b = z;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:13:0x0031. Please report as an issue. */
    public final o.ep.b d(Context context) {
        switch (this.b ? (char) 31 : (char) 0) {
            case 0:
                o.ee.c.a();
                o.ep.b p = o.ee.c.p(context);
                int i2 = i + 57;
                h = i2 % 128;
                switch (i2 % 2 == 0 ? '`' : 'X') {
                }
                return p;
            default:
                int i3 = i + 55;
                h = i3 % 128;
                if (i3 % 2 == 0) {
                }
                o.ee.c.a();
                return o.ee.c.s(context);
        }
    }

    @Override // o.er.h
    public final o.er.a[] i() {
        int i2 = i + 37;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.er.a[] aVarArr = {this.d.k()};
        int i4 = i + 11;
        h = i4 % 128;
        switch (i4 % 2 == 0 ? 'M' : 'O') {
            case 'M':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return aVarArr;
        }
    }

    @Override // o.er.d
    public final String c() {
        int i2 = h + 61;
        i = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        m(null, TextUtils.lastIndexOf("", '0', 0) + 128, null, "\u008a\u008e\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = h + 77;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return intern;
            default:
                throw null;
        }
    }

    @Override // o.er.d
    public final AntelopErrorCode a() {
        int i2 = i + 3;
        h = i2 % 128;
        int i3 = i2 % 2;
        AntelopErrorCode antelopErrorCode = AntelopErrorCode.SamsungPayWalletNotAvailable;
        int i4 = h + 15;
        i = i4 % 128;
        int i5 = i4 % 2;
        return antelopErrorCode;
    }

    @Override // o.er.d
    public final f.a d() {
        int i2 = i + 47;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '\\' : '4') {
            case Opcodes.DUP2 /* 92 */:
                f.a aVar = f.a.a;
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                f.a aVar2 = f.a.a;
                int i3 = i + 65;
                h = i3 % 128;
                int i4 = i3 % 2;
                return aVar2;
        }
    }

    @Override // o.er.d
    public final String e() {
        Object obj;
        int i2 = i + 73;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                m(null, 95 >> ExpandableListView.getPackedPositionType(0L), null, "\u008a\u008e\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u008a\u0086\u0086\u008f", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                m(null, ExpandableListView.getPackedPositionType(0L) + 127, null, "\u008a\u008e\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u008a\u0086\u0086\u008f", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    private String g() {
        int i2 = i + 95;
        h = i2 % 128;
        int i3 = i2 % 2;
        String d = this.d.k().d();
        switch (d == null) {
            case true:
                int i4 = h + 45;
                i = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Object[] objArr = new Object[1];
                m(null, 127 - Gravity.getAbsoluteGravity(0, 0), null, "\u008a\u008e\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008b\u008a\u0086\u0086\u008f", objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                m(null, 127 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), null, "\u0086\u008b\u008a\u0090\u0090\u0082\u009b\u0096\u0091\u0090\u0085\u0082\u0096\u0094\u0086\u0096\u0090\u009a\u0085\u0082\u009c\u008a\u0093\u0096\u008a\u0091\u0090\u0096\u008a\u0084\u0098\u0096\u0097\u0096\u0093\u008a\u0090\u0082\u008d\u008e\u0094\u0084\u0084\u0082\u0096\u008a\u0083\u0082\u0086\u0096\u0086\u008b\u008a\u0090\u0090\u0082\u009b\u0096\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u0096\u0090\u008a\u0087\u0096\u0094\u0090\u0096\u008a\u009a\u0099\u0082\u0086\u0098\u0096\u0097\u0096\u0086\u008b\u008a\u0090\u0090\u0082\u0088\u0086\u0094\u008d\u0090\u0082\u008e\u008d\u0090\u0086\u008a\u0091\u0090\u0085\u0095\u008b\u008a\u0083\u0094\u0090\u0084\u0085\u0092\u0093\u008b\u0082\u0092\u0091\u0084\u0085\u0088\u0090\u008a\u0087", objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                m(null, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 127, null, "\u0086\u0094\u008d\u0090\u0082\u008e\u008d\u0090\u0086\u008a\u0091\u0090\u0085\u0082\u009d\u0094\u0086", objArr3);
                d = ((String) objArr3[0]).intern();
                int i6 = h + Opcodes.DMUL;
                i = i6 % 128;
                if (i6 % 2 != 0) {
                }
            default:
                return d;
        }
    }

    public final SecureCardPushToSamsungPay f() {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        m(null, 127 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), null, "\u0091\u0084\u0085\u0088\u0093\u008b\u0082\u0092\u008a\u008b\u0085\u008e\u008a\u0081\u0090\u008a\u0087", objArr);
        g.d(e2, ((String) objArr[0]).intern());
        SecureCardPushToSamsungPay secureCardPushToSamsungPay = new SecureCardPushToSamsungPay(new o.v.f(g(), this.c, b(), this.b));
        int i2 = i + 33;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                throw null;
            default:
                return secureCardPushToSamsungPay;
        }
    }

    public final void b(Activity activity, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        Object obj = null;
        m(null, 127 - (Process.myTid() >> 22), null, "\u0093\u008b\u0082\u0092\u0091\u0084\u0085\u009b", objArr);
        g.d(e2, ((String) objArr[0]).intern());
        f().launch(activity, new CustomCustomerAuthenticatedProcessCallback() { // from class: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.3
            private static int e = 0;
            private static int a = 1;

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = e;
                int i3 = ((i2 | 23) << 1) - (i2 ^ 23);
                a = i3 % 128;
                int i4 = i3 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsInvalid(LocalAuthenticationErrorReason localAuthenticationErrorReason, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = a + 89;
                e = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = e;
                int i3 = ((i2 | 37) << 1) - (i2 ^ 37);
                a = i3 % 128;
                switch (i3 % 2 == 0 ? 'U' : 'H') {
                    case Opcodes.CASTORE /* 85 */:
                        int i4 = 72 / 0;
                        break;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsRequired(List<CustomerAuthenticationMethod> list, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerCredentialsInvalid).d());
                int i2 = (a + 70) - 1;
                e = i2 % 128;
                switch (i2 % 2 != 0 ? 'R' : ')') {
                    case ')':
                        return;
                    default:
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = e;
                int i3 = ((i2 | 15) << 1) - (i2 ^ 15);
                a = i3 % 128;
                int i4 = i3 % 2;
                operationCallback.onSuccess(null);
                int i5 = a;
                int i6 = ((i5 | 11) << 1) - (i5 ^ 11);
                e = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i2 = e;
                int i3 = (i2 & 85) + (i2 | 85);
                a = i3 % 128;
                int i4 = i3 % 2;
                operationCallback.onError(antelopError);
                int i5 = a;
                int i6 = (i5 ^ 93) + ((i5 & 93) << 1);
                e = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        int i7 = 46 / 0;
                        return;
                    default:
                        return;
                }
            }
        });
        int i2 = h + Opcodes.LUSHR;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void b(final Activity activity) {
        d((Context) activity).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.1
            private static int $10 = 0;
            private static int $11 = 1;
            private static int j = 0;
            private static int h = 1;
            private static char c = 25093;
            private static char d = 48025;
            private static char f = 56236;
            private static char e = 24961;

            @Override // o.ep.a.InterfaceC0042a
            public final /* bridge */ /* synthetic */ void e(List<o.ep.e> list) {
                int i2 = j + 1;
                h = i2 % 128;
                char c2 = i2 % 2 == 0 ? (char) 11 : '1';
                e2(list);
                switch (c2) {
                    case '1':
                        return;
                    default:
                        int i3 = 90 / 0;
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            /* renamed from: e, reason: avoid collision after fix types in other method */
            private void e2(java.util.List<o.ep.e> r8) {
                /*
                    r7 = this;
                    int r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.h
                    r1 = 1
                    int r0 = r0 + r1
                    int r2 = r0 % 128
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.j = r2
                    int r0 = r0 % 2
                    if (r0 == 0) goto L10
                    r0 = 56
                    goto L12
                L10:
                    r0 = 15
                L12:
                    r2 = 0
                    r3 = 0
                    switch(r0) {
                        case 56: goto L1e;
                        default: goto L18;
                    }
                L18:
                    java.util.Iterator r8 = r8.iterator()
                L1c:
                    r0 = r2
                    goto L29
                L1e:
                    java.util.Iterator r8 = r8.iterator()
                    r0 = 80
                    int r0 = r0 / r3
                    goto L1c
                L27:
                    r8 = move-exception
                    throw r8
                L29:
                    boolean r4 = r8.hasNext()
                    if (r4 == 0) goto L32
                    r4 = 16
                    goto L34
                L32:
                    r4 = 88
                L34:
                    switch(r4) {
                        case 88: goto L44;
                        default: goto L37;
                    }
                L37:
                    int r4 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.j
                    int r4 = r4 + 97
                    int r5 = r4 % 128
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.h = r5
                    int r4 = r4 % 2
                    if (r4 != 0) goto L76
                    goto L76
                L44:
                    if (r0 != 0) goto L68
                    o.ee.g.c()
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d r8 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.this
                    java.lang.String r8 = r8.e()
                    int r0 = android.view.View.resolveSizeAndState(r3, r3, r3)
                    int r0 = 51 - r0
                    java.lang.Object[] r1 = new java.lang.Object[r1]
                    java.lang.String r2 = "蚩ꯌⴕ빱戀寨녟剌ベ잴\udebf멫\ued32䇓䅧劉녟剌혍꙾芮\ue8bd榻䴣⒟妖튞脹髛춞餟Ꞥ㑭꒩㞬楆뵒\udf39ꕦ␝\udc44寡⸜Ṡ豚ﳅ\uecc7ҩ\ue8dcછ綉柮"
                    g(r2, r0, r1)
                    r0 = r1[r3]
                    java.lang.String r0 = (java.lang.String) r0
                    java.lang.String r0 = r0.intern()
                    o.ee.g.d(r8, r0)
                    return
                L68:
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d r8 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.this
                    android.app.Activity r1 = r2
                    o.ep.b r8 = r8.d(r1)
                    android.app.Activity r1 = r2
                    r8.c(r1, r0)
                    return
                L76:
                    java.lang.Object r4 = r8.next()
                    o.ep.e r4 = (o.ep.e) r4
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d r5 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.this
                    o.eo.e r5 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.a(r5)
                    o.eo.c r5 = r5.s()
                    if (r5 == 0) goto Lb7
                    java.lang.String r5 = r4.d()
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d r6 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.this
                    o.eo.e r6 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.b(r6)
                    o.eo.c r6 = r6.s()
                    java.lang.String r6 = r6.a()
                    boolean r5 = r5.equals(r6)
                    if (r5 == 0) goto Lb7
                    int r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.j
                    int r0 = r0 + 81
                    int r5 = r0 % 128
                    fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.h = r5
                    int r0 = r0 % 2
                    if (r0 == 0) goto Lb1
                    java.lang.String r0 = r4.c()
                    goto Lb7
                Lb1:
                    r4.c()
                    throw r2     // Catch: java.lang.Throwable -> Lb5
                Lb5:
                    r8 = move-exception
                    throw r8
                Lb7:
                    goto L29
                */
                throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.e2(java.util.List):void");
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                g.c();
                String e2 = d.this.e();
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                g("蚩ꯌⴕ빱戀寨녟剌ベ잴˭䠈痱뱒\udf73蠅㍾ȸ鉔桒ᘷ똢踽Ǻ졚▤ෆ\u0e7d\ue726鉏㍾ȸᰉ쵂뛁\u0ad3", 36 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
                g.d(e2, sb.append(((String) objArr[0]).intern()).append(cVar).toString());
                int i2 = j + 9;
                h = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 22 : (char) 25) {
                    case 25:
                        return;
                    default:
                        throw null;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void g(java.lang.String r24, int r25, java.lang.Object[] r26) {
                /*
                    Method dump skipped, instructions count: 590
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.d.AnonymousClass1.g(java.lang.String, int, java.lang.Object[]):void");
            }
        });
        int i2 = i + 13;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? '(' : Typography.amp) {
            case '&':
                return;
            default:
                int i3 = 56 / 0;
                return;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v2 */
    /* JADX WARN: Type inference failed for: r1v20, types: [byte[]] */
    private static void m(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        int length;
        char[] cArr2;
        int i3;
        ?? r1 = str2;
        int i4 = $10 + 75;
        $11 = i4 % 128;
        if (i4 % 2 == 0) {
            throw null;
        }
        switch (r1 != 0 ? 'J' : 'L') {
            case Base64.mimeLineLength /* 76 */:
                break;
            default:
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        int i5 = 0;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                int i6 = $11 + 71;
                $10 = i6 % 128;
                if (i6 % 2 != 0) {
                }
                cArr = str.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        j jVar = new j();
        char[] cArr4 = e;
        int i7 = 10;
        if (cArr4 != null) {
            int i8 = $10 + 85;
            $11 = i8 % 128;
            if (i8 % 2 == 0) {
                length = cArr4.length;
                cArr2 = new char[length];
                i3 = 1;
            } else {
                length = cArr4.length;
                cArr2 = new char[length];
                i3 = 0;
            }
            while (i3 < length) {
                try {
                    Object[] objArr2 = new Object[1];
                    objArr2[i5] = Integer.valueOf(cArr4[i3]);
                    Object obj = o.e.a.s.get(1085633688);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + i7, (char) (ViewConfiguration.getFadingEdgeLength() >> 16), (Process.myTid() >> 22) + 493);
                        byte b = (byte) i5;
                        byte b2 = b;
                        Object[] objArr3 = new Object[1];
                        n(b, b2, b2, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(1085633688, obj);
                    }
                    cArr2[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i3++;
                    i5 = 0;
                    i7 = 10;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr4 = cArr2;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(a)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (char) ((Process.myPid() >> 22) + 8856), Gravity.getAbsoluteGravity(0, 0) + 324);
                byte b3 = (byte) 0;
                byte b4 = (byte) (b3 + 3);
                Object[] objArr5 = new Object[1];
                n(b3, b4, (byte) (b4 - 3), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            switch (j ? 'M' : (char) 21) {
                case 'M':
                    jVar.e = bArr.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr5[jVar.c] = (char) (cArr4[bArr[(jVar.e - 1) - jVar.c] + i2] - intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj3 = o.e.a.s.get(745816316);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getJumpTapTimeout() >> 16) + 10, (char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 207 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                byte length2 = (byte) $$d.length;
                                Object[] objArr7 = new Object[1];
                                n((byte) 0, length2, (byte) (length2 - 4), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                            int i9 = $11 + 109;
                            $10 = i9 % 128;
                            int i10 = i9 % 2;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    objArr[0] = new String(cArr5);
                    return;
                default:
                    switch (g ? '_' : 'K') {
                        case 'K':
                            jVar.e = iArr.length;
                            char[] cArr6 = new char[jVar.e];
                            jVar.c = 0;
                            while (true) {
                                switch (jVar.c < jVar.e ? '\n' : 'R') {
                                    case '\n':
                                        cArr6[jVar.c] = (char) (cArr4[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                        jVar.c++;
                                        int i11 = $11 + 31;
                                        $10 = i11 % 128;
                                        int i12 = i11 % 2;
                                    default:
                                        objArr[0] = new String(cArr6);
                                        return;
                                }
                            }
                        default:
                            jVar.e = cArr3.length;
                            char[] cArr7 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                cArr7[jVar.c] = (char) (cArr4[cArr3[(jVar.e - 1) - jVar.c] - i2] - intValue);
                                try {
                                    Object[] objArr8 = {jVar, jVar};
                                    Object obj4 = o.e.a.s.get(745816316);
                                    if (obj4 == null) {
                                        Class cls4 = (Class) o.e.a.c(10 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), TextUtils.indexOf("", "") + 207);
                                        byte length3 = (byte) $$d.length;
                                        Object[] objArr9 = new Object[1];
                                        n((byte) 0, length3, (byte) (length3 - 4), objArr9);
                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                        o.e.a.s.put(745816316, obj4);
                                    }
                                    ((Method) obj4).invoke(null, objArr8);
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            objArr[0] = new String(cArr7);
                            return;
                    }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
