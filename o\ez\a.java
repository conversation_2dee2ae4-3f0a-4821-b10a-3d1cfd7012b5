package o.ez;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import o.eg.b;
import o.eg.e;
import o.ey.a;
import o.ff.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ez\a.smali */
public final class a extends o.ey.a<c, d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int k;
    private static int m;
    private static long n;

    /* renamed from: o, reason: collision with root package name */
    private static long f83o;
    private final String a;
    private final String b;
    private final String c;
    private final String d;
    private final String e;
    private final String f;
    private final String g;
    private final String h;
    private final String i;
    private final String j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        m = 1;
        b();
        ViewConfiguration.getDoubleTapTimeout();
        int i = m + 19;
        k = i % 128;
        switch (i % 2 != 0 ? (char) 11 : '/') {
            case '/':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void b() {
        n = -28523472863267231L;
        f83o = -4997410805878493741L;
    }

    static void init$0() {
        $$d = new byte[]{31, 57, -118, -60};
        $$e = Opcodes.NEW;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void t(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r6 = r6 * 4
            int r6 = 4 - r6
            int r7 = r7 + 68
            byte[] r0 = o.ez.a.$$d
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L34
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.a.t(byte, byte, short, java.lang.Object[]):void");
    }

    public a() {
        Object[] objArr = new Object[1];
        r("낂뚻볞ꈉ꠨깴閈鯝臊蜹赒\uf49a缾\ue0f7\ue611", 1583 - KeyEvent.normalizeMetaState(0), objArr);
        this.c = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        r("난⻗谇橴즌\ua7ebԶ\ue365䊧\u20fa鹹綉\udbc8뤈\u1776\uf6aa", 40520 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr2);
        this.a = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        s("欤㫊歀鞭\u05fc∿徍", ViewConfiguration.getJumpTapTimeout() >> 16, objArr3);
        this.b = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        s("ㅞ寢ㄵ\uf68bﳼ㱠ꚝ㬑蔝㪿\uf2d3蝑奶仧㻷厐ⶡ鈌設", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr4);
        this.e = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        s("\uf54fꈵ\uf52eཉ寿巎Ǝ", ViewConfiguration.getScrollBarSize() >> 8, objArr5);
        this.d = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        s("凨頥农㕛䳐蠰ᚩ轺\ue5ab兩䋻㌐㧡账軆\ue7fd䴜", KeyEvent.getDeadChar(0, 0), objArr6);
        this.f = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        r("낅ﺝⲖ媛袖㚜撎銿삩ຏ벦\ueaa5ᢢ䛞\uf4d3⋉", TextUtils.lastIndexOf("", '0') + 19974, objArr7);
        this.g = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        s("綥꩑緐ܳ슝ᮑ飵᳇짰쬉첼ꂦᖱ뽜\u0087瑹慐掃둀㡈괇ퟸ\ue837", TextUtils.indexOf("", "", 0, 0), objArr8);
        this.j = ((String) objArr8[0]).intern();
        Object[] objArr9 = new Object[1];
        s("㦽켁㧜批糆跡⚽", ViewConfiguration.getLongPressTimeout() >> 16, objArr9);
        this.i = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        r("낂琠㧚ﵪꈐ枻", View.combineMeasuredStates(0, 0) + 50341, objArr10);
        this.h = ((String) objArr10[0]).intern();
    }

    @Override // o.ey.a
    public final /* synthetic */ d b(b bVar) throws o.eg.d {
        int i = k + 15;
        m = i % 128;
        switch (i % 2 == 0 ? 'T' : (char) 31) {
            case Opcodes.BASTORE /* 84 */:
                d(bVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return d(bVar);
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ c b(o.fc.c cVar, short s) {
        int i = m + 73;
        k = i % 128;
        int i2 = i % 2;
        c d = d(false, cVar, s);
        int i3 = m + Opcodes.DREM;
        k = i3 % 128;
        switch (i3 % 2 != 0 ? 'c' : 'a') {
            case Opcodes.DADD /* 99 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return d;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ b e(d dVar) throws o.eg.d {
        int i = k + Opcodes.LSUB;
        m = i % 128;
        boolean z = i % 2 != 0;
        b d = d(dVar);
        switch (z) {
            case false:
                int i2 = 40 / 0;
                break;
        }
        int i3 = k + Opcodes.LSUB;
        m = i3 % 128;
        int i4 = i3 % 2;
        return d;
    }

    @Override // o.ey.a
    public final /* synthetic */ b e(c cVar) throws o.eg.d {
        int i = m + 23;
        k = i % 128;
        c cVar2 = cVar;
        switch (i % 2 != 0 ? 'a' : 'X') {
            case Opcodes.LADD /* 97 */:
                b(cVar2);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return b(cVar2);
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ d e(String str, String str2, boolean z) {
        int i = m + 61;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                return b(str, str2, z);
            default:
                b(str, str2, z);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ey.a
    public final /* synthetic */ c e(b bVar) throws o.eg.d {
        int i = k + 73;
        m = i % 128;
        int i2 = i % 2;
        c c = c(bVar);
        int i3 = k + 87;
        m = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    @Override // o.ey.a
    public final a.d d() {
        int i = m + Opcodes.DREM;
        k = i % 128;
        int i2 = i % 2;
        a.d dVar = a.d.b;
        int i3 = k + 77;
        m = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 24 : '5') {
            case Opcodes.SALOAD /* 53 */:
                return dVar;
            default:
                int i4 = 4 / 0;
                return dVar;
        }
    }

    private b d(d dVar) throws o.eg.d {
        int i = m + 79;
        k = i % 128;
        int i2 = i % 2;
        b e = super.e((a) dVar);
        Object[] objArr = new Object[1];
        r("낒⢿胵砲큸䦸⇧餧煌\uea9b䋾㨌鉖\u0a5d\ue3bf寥㌳\uab6e", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 38970, objArr);
        e.d(((String) objArr[0]).intern(), dVar.p());
        int i3 = m + 69;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return e;
            default:
                throw null;
        }
    }

    private d d(b bVar) throws o.eg.d {
        d dVar;
        Object obj;
        int i = k + Opcodes.LSHR;
        m = i % 128;
        switch (i % 2 != 0) {
            case true:
                dVar = (d) super.b(bVar);
                Object[] objArr = new Object[1];
                r("낒⢿胵砲큸䦸⇧餧煌\uea9b䋾㨌鉖\u0a5d\ue3bf寥㌳\uab6e", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 38971, objArr);
                obj = objArr[0];
                break;
            default:
                dVar = (d) super.b(bVar);
                Object[] objArr2 = new Object[1];
                r("낒⢿胵砲큸䦸⇧餧煌\uea9b䋾㨌鉖\u0a5d\ue3bf寥㌳\uab6e", 38971 >> (CdmaCellLocation.convertQuartSecToDecDegrees(1) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(1) == 0.0d ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        dVar.d(bVar.i(((String) obj).intern()).intValue());
        int i2 = k + 21;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 13 / 0;
                return dVar;
            default:
                return dVar;
        }
    }

    private static d b(String str, String str2, boolean z) {
        d dVar = new d(str, str2, z);
        int i = m + 3;
        k = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    private static c d(boolean z, o.fc.c cVar, short s) {
        c cVar2 = new c(false, cVar, s);
        int i = m + 13;
        k = i % 128;
        switch (i % 2 != 0 ? '-' : '`') {
            case Opcodes.IADD /* 96 */:
                return cVar2;
            default:
                throw null;
        }
    }

    private c c(b bVar) throws o.eg.d {
        ArrayList arrayList;
        int i = k + 13;
        m = i % 128;
        int i2 = i % 2;
        c cVar = (c) super.e(bVar);
        int i3 = 1;
        Object[] objArr = new Object[1];
        r("낂뚻볞ꈉ꠨깴閈鯝臊蜹赒\uf49a缾\ue0f7\ue611", TextUtils.indexOf("", "", 0) + 1583, objArr);
        cVar.a(bVar.i(((String) objArr[0]).intern()).intValue());
        Object[] objArr2 = new Object[1];
        r("난⻗谇橴즌\ua7ebԶ\ue365䊧\u20fa鹹綉\udbc8뤈\u1776\uf6aa", 40518 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
        cVar.c(bVar.k(((String) objArr2[0]).intern()).shortValue());
        Object[] objArr3 = new Object[1];
        s("\uf54fꈵ\uf52eཉ寿巎Ǝ", KeyEvent.keyCodeFromString(""), objArr3);
        cVar.a(bVar.r(((String) objArr3[0]).intern()));
        Object[] objArr4 = new Object[1];
        s("欤㫊歀鞭\u05fc∿徍", ViewConfiguration.getLongPressTimeout() >> 16, objArr4);
        cVar.c(bVar.D(((String) objArr4[0]).intern()).byteValue());
        long j = 0;
        Object[] objArr5 = new Object[1];
        s("ㅞ寢ㄵ\uf68bﳼ㱠ꚝ㬑蔝㪿\uf2d3蝑奶仧㻷厐ⶡ鈌設", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, objArr5);
        if (bVar.b(((String) objArr5[0]).intern())) {
            Object[] objArr6 = new Object[1];
            s("ㅞ寢ㄵ\uf68bﳼ㱠ꚝ㬑蔝㪿\uf2d3蝑奶仧㻷厐ⶡ鈌設", ExpandableListView.getPackedPositionChild(0L) + 1, objArr6);
            e s = bVar.s(((String) objArr6[0]).intern());
            arrayList = new ArrayList();
            int i4 = 0;
            while (true) {
                switch (i4 < s.d() ? '1' : (char) 3) {
                    case 3:
                        break;
                    default:
                        int i5 = m + 81;
                        k = i5 % 128;
                        int i6 = i5 % 2;
                        b b = s.b(i4);
                        Object[] objArr7 = new Object[i3];
                        s("凨頥农㕛䳐蠰ᚩ轺\ue5ab兩䋻㌐㧡账軆\ue7fd䴜", Color.blue(0), objArr7);
                        String r = b.r(((String) objArr7[0]).intern());
                        Object[] objArr8 = new Object[i3];
                        r("낅ﺝⲖ媛袖㚜撎銿삩ຏ벦\ueaa5ᢢ䛞\uf4d3⋉", View.combineMeasuredStates(0, 0) + 19973, objArr8);
                        long longValue = b.m(((String) objArr8[0]).intern()).longValue();
                        Object[] objArr9 = new Object[i3];
                        s("綥꩑緐ܳ슝ᮑ飵᳇짰쬉첼ꂦᖱ뽜\u0087瑹慐掃둀㡈괇ퟸ\ue837", TextUtils.indexOf((CharSequence) "", '0') + i3, objArr9);
                        byte[] B = b.B(((String) objArr9[0]).intern());
                        Object[] objArr10 = new Object[i3];
                        s("㦽켁㧜批糆跡⚽", (Process.getThreadPriority(0) + 20) >> 6, objArr10);
                        int intValue = b.i(((String) objArr10[0]).intern()).intValue();
                        try {
                            int i7 = 50342 - (ViewConfiguration.getZoomControlsTimeout() > j ? 1 : (ViewConfiguration.getZoomControlsTimeout() == j ? 0 : -1));
                            Object[] objArr11 = new Object[1];
                            r("낂琠㧚ﵪꈐ枻", i7, objArr11);
                            arrayList.add(new c.d(r, longValue, B, intValue, o.fc.c.a(b.r(((String) objArr11[0]).intern()))));
                            i4++;
                            i3 = 1;
                            j = 0;
                        } catch (IllegalArgumentException e) {
                            Object[] objArr12 = new Object[1];
                            s("ﮈ\ue2b9ﯡ俛蛧㧗\udc89㺒俔菬裋芣鎘\uf7b4䓦嘾\ue77d⭋\uf03bᩃ⬣鼐걆\uee13缌팼妟떦", 1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr12);
                            throw new o.eg.d(((String) objArr12[0]).intern());
                        }
                }
            }
        } else {
            arrayList = null;
        }
        cVar.d(arrayList);
        return cVar;
    }

    private b b(c cVar) throws o.eg.d {
        e eVar;
        b e = super.e((a) cVar);
        Object[] objArr = new Object[1];
        r("낂뚻볞ꈉ꠨깴閈鯝臊蜹赒\uf49a缾\ue0f7\ue611", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 1583, objArr);
        e.d(((String) objArr[0]).intern(), cVar.f());
        Object[] objArr2 = new Object[1];
        r("난⻗谇橴즌\ua7ebԶ\ue365䊧\u20fa鹹綉\udbc8뤈\u1776\uf6aa", (ViewConfiguration.getFadingEdgeLength() >> 16) + 40519, objArr2);
        e.d(((String) objArr2[0]).intern(), cVar.j());
        Object[] objArr3 = new Object[1];
        s("\uf54fꈵ\uf52eཉ寿巎Ǝ", (-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr3);
        e.d(((String) objArr3[0]).intern(), cVar.o());
        Object[] objArr4 = new Object[1];
        s("欤㫊歀鞭\u05fc∿徍", ExpandableListView.getPackedPositionGroup(0L), objArr4);
        String intern = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        r("냔軮첝ਤ", 15919 - View.getDefaultSize(0, 0), objArr5);
        e.d(intern, String.format(((String) objArr5[0]).intern(), Byte.valueOf(cVar.i())));
        if (cVar.h() == null) {
            eVar = null;
        } else {
            eVar = new e();
            for (c.d dVar : cVar.h()) {
                b bVar = new b();
                Object[] objArr6 = new Object[1];
                s("凨頥农㕛䳐蠰ᚩ轺\ue5ab兩䋻㌐㧡账軆\ue7fd䴜", (Process.getThreadPriority(0) + 20) >> 6, objArr6);
                bVar.d(((String) objArr6[0]).intern(), dVar.e());
                Object[] objArr7 = new Object[1];
                s("綥꩑緐ܳ슝ᮑ飵᳇짰쬉첼ꂦᖱ뽜\u0087瑹慐掃둀㡈괇ퟸ\ue837", TextUtils.getOffsetAfter("", 0), objArr7);
                bVar.d(((String) objArr7[0]).intern(), o.dk.b.e(dVar.a()));
                Object[] objArr8 = new Object[1];
                r("낅ﺝⲖ媛袖㚜撎銿삩ຏ벦\ueaa5ᢢ䛞\uf4d3⋉", 19973 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr8);
                bVar.d(((String) objArr8[0]).intern(), dVar.b());
                Object[] objArr9 = new Object[1];
                s("㦽켁㧜批糆跡⚽", Color.rgb(0, 0, 0) + 16777216, objArr9);
                bVar.d(((String) objArr9[0]).intern(), dVar.g());
                Object[] objArr10 = new Object[1];
                r("낂琠㧚ﵪꈐ枻", View.MeasureSpec.getSize(0) + 50341, objArr10);
                bVar.d(((String) objArr10[0]).intern(), dVar.d().c());
                eVar.b(bVar);
                int i = k + Opcodes.LSUB;
                m = i % 128;
                int i2 = i % 2;
            }
        }
        Object[] objArr11 = new Object[1];
        s("ㅞ寢ㄵ\uf68bﳼ㱠ꚝ㬑蔝㪿\uf2d3蝑奶仧㻷厐ⶡ鈌設", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr11);
        e.d(((String) objArr11[0]).intern(), eVar);
        int i3 = m + 43;
        k = i3 % 128;
        int i4 = i3 % 2;
        return e;
    }

    /* JADX WARN: Removed duplicated region for block: B:41:0x0149  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 620
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.a.r(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void s(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 384
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ez.a.s(java.lang.String, int, java.lang.Object[]):void");
    }
}
