package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y0.smali */
public class y0 implements y {
    private g0 b;

    y0(g0 g0Var) {
        this.b = g0Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return a(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.y
    public InputStream c() {
        return new m1(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0("IOException converting stream to byte array: " + e.getMessage(), e);
        }
    }

    static x0 a(g0 g0Var) throws IOException {
        return new x0(n7.a(new m1(g0Var)));
    }
}
