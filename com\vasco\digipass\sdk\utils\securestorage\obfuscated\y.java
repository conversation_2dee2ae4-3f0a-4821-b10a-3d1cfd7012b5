package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\y.smali */
public abstract class y {
    public static String a(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bArr) {
            String hexString = Integer.toHexString(b & 255);
            if (hexString.length() == 1) {
                sb.append('0');
            }
            sb.append(hexString);
        }
        return sb.toString().toUpperCase();
    }

    public static boolean b(String str) {
        if (str != null && str.length() != 0) {
            for (int i = 0; i < str.length(); i++) {
                char charAt = str.charAt(i);
                if ((charAt < '0' || charAt > '9') && ((charAt < 'A' || charAt > 'Z') && (charAt < 'a' || charAt > 'z'))) {
                    return false;
                }
            }
        }
        return true;
    }

    public static void b(byte[] bArr) {
        if (bArr != null) {
            Arrays.fill(bArr, (byte) 0);
        }
    }

    public static byte[] a(String str) {
        if (str == null) {
            return null;
        }
        byte[] bArr = new byte[str.length() / 2];
        int length = str.length() / 2;
        for (int i = 0; i < length; i++) {
            int i2 = i * 2;
            char charAt = str.charAt(i2);
            byte b = (byte) (charAt >= 'a' ? charAt - 'W' : charAt >= 'A' ? charAt - '7' : charAt - '0');
            char charAt2 = str.charAt(i2 + 1);
            bArr[i] = (byte) ((b << 4) + (((byte) (charAt2 >= 'a' ? charAt2 - 'W' : charAt2 >= 'A' ? charAt2 - '7' : charAt2 - '0')) & 255));
        }
        return bArr;
    }
}
