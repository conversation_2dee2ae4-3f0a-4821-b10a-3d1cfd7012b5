package o.o;

import android.os.SystemClock;
import android.view.ViewConfiguration;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationFailureReason;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\e.smali */
public final class e implements o.ee.a<CustomerAuthenticationFailureReason> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final e a;
    public static final e b;
    public static final e c;
    public static final e d;
    public static final e e;
    private static int f;
    private static int g;
    private static long h;
    private static final /* synthetic */ e[] i;
    public static final e j;

    static void c() {
        h = 5604625895373998169L;
    }

    static void init$0() {
        $$a = new byte[]{125, -17, -70, 109};
        $$b = 248;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.o.e.$$a
            int r8 = r8 * 2
            int r8 = 114 - r8
            int r6 = r6 * 4
            int r6 = 4 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = -r7
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.e.l(short, short, short, java.lang.Object[]):void");
    }

    private e(String str, int i2) {
    }

    private static /* synthetic */ e[] d() {
        int i2 = g;
        int i3 = i2 + 11;
        f = i3 % 128;
        int i4 = i3 % 2;
        e[] eVarArr = {a, c, d, e, b, j};
        int i5 = i2 + 35;
        f = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return eVarArr;
        }
    }

    public static e valueOf(String str) {
        int i2 = g + 55;
        f = i2 % 128;
        int i3 = i2 % 2;
        e eVar = (e) Enum.valueOf(e.class, str);
        int i4 = g + 43;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? '@' : 'H') {
            case 'H':
                return eVar;
            default:
                throw null;
        }
    }

    public static e[] values() {
        int i2 = f + 89;
        g = i2 % 128;
        int i3 = i2 % 2;
        e[] eVarArr = (e[]) i.clone();
        int i4 = f + 49;
        g = i4 % 128;
        int i5 = i4 % 2;
        return eVarArr;
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = g + 81;
        f = i2 % 128;
        int i3 = i2 % 2;
        CustomerAuthenticationFailureReason e2 = e();
        int i4 = f + 25;
        g = i4 % 128;
        int i5 = i4 % 2;
        return e2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        f = 1;
        c();
        Object[] objArr = new Object[1];
        k("캊ꍩᔥ蟩禨\uea60尣컫ꂥ", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 28097, objArr);
        a = new e(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        k("캅\ud8cf\ue278趙霈뺠", (ViewConfiguration.getPressedStateDuration() >> 16) + 5737, objArr2);
        c = new e(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        k("캝陳缂쓕귪犣\uda4f", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 22738, objArr3);
        d = new e(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        k("캁ັ争軦컚໕中踃칇้乚辶쾐\u0ffe俲", 49178 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr4);
        e = new e(((String) objArr4[0]).intern(), 3);
        Object[] objArr5 = new Object[1];
        k("캜膊僠⌻\uf20d䕘ᖨ\ue480럕ع\ud96f", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 20268, objArr5);
        b = new e(((String) objArr5[0]).intern(), 4);
        Object[] objArr6 = new Object[1];
        k("캀\uef92賗괳䩯梮খ⛖윤\ue466芩ꏡ䃇", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 8500, objArr6);
        j = new e(((String) objArr6[0]).intern(), 5);
        i = d();
        int i2 = g + 21;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    /* renamed from: o.o.e$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\e$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] d;

        static {
            a = 0;
            c = 1;
            int[] iArr = new int[e.values().length];
            d = iArr;
            try {
                iArr[e.c.ordinal()] = 1;
                int i = c + 27;
                a = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                d[e.a.ordinal()] = 2;
                int i2 = a + 91;
                c = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[e.d.ordinal()] = 3;
                int i4 = c + 7;
                a = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[e.b.ordinal()] = 4;
                int i5 = a;
                int i6 = ((i5 | 13) << 1) - (i5 ^ 13);
                c = i6 % 128;
                int i7 = i6 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[e.j.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
            try {
                d[e.e.ordinal()] = 6;
                int i8 = a;
                int i9 = ((i8 | 95) << 1) - (i8 ^ 95);
                c = i9 % 128;
                switch (i9 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    public final CustomerAuthenticationFailureReason e() {
        int i2 = g + 19;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? (char) 24 : (char) 1) {
            case 24:
                int i3 = AnonymousClass2.d[ordinal()];
                obj.hashCode();
                throw null;
            default:
                switch (AnonymousClass2.d[ordinal()]) {
                    case 1:
                        return CustomerAuthenticationFailureReason.Locked;
                    case 2:
                        return CustomerAuthenticationFailureReason.Cancelled;
                    case 3:
                        return CustomerAuthenticationFailureReason.Timeout;
                    case 4:
                        return CustomerAuthenticationFailureReason.Unsupported;
                    default:
                        CustomerAuthenticationFailureReason customerAuthenticationFailureReason = CustomerAuthenticationFailureReason.InternalError;
                        int i4 = f + 55;
                        g = i4 % 128;
                        switch (i4 % 2 != 0 ? (char) 3 : (char) 22) {
                            case 3:
                                throw null;
                            default:
                                return customerAuthenticationFailureReason;
                        }
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 492
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.e.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
