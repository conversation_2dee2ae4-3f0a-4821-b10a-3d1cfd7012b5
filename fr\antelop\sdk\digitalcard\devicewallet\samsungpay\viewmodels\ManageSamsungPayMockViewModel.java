package fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels;

import android.app.Application;
import android.view.MotionEvent;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\samsungpay\viewmodels\ManageSamsungPayMockViewModel.smali */
public class ManageSamsungPayMockViewModel extends ManageDeviceWalletViewModel {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final String TAG;
    private static int a;
    private static boolean b;
    private static boolean c;
    private static int d;
    private static char[] e;
    private static int h;

    static void c() {
        e = new char[]{61785, 61795, 61790};
        b = true;
        c = true;
        d = 782103022;
    }

    static void init$0() {
        $$a = new byte[]{124, 92, -85, -9};
        $$b = Opcodes.MONITORENTER;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 117
            byte[] r0 = fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels.ManageSamsungPayMockViewModel.$$a
            int r6 = r6 * 4
            int r6 = r6 + 4
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r7 = r7 + r4
            int r6 = r6 + 1
            r5 = r8
            r8 = r7
            r7 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels.ManageSamsungPayMockViewModel.j(int, short, short, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        h = 1;
        c();
        TAG = ManageSamsungPayMockViewModel.class.getSimpleName();
        int i = h + 81;
        a = i % 128;
        int i2 = i % 2;
    }

    public ManageSamsungPayMockViewModel(Application application) {
        super(application);
    }

    @Override // fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel
    public String getSharedPrefDeviceWalletKey() {
        Object obj;
        int i = h + 51;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 4 : '`') {
            case Opcodes.IADD /* 96 */:
                Object[] objArr = new Object[1];
                i(null, 126 - MotionEvent.axisFromString(""), null, "\u0083\u0082\u0081", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                i(null, 7 % MotionEvent.axisFromString(""), null, "\u0083\u0082\u0081", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel
    public String getTag() {
        int i = h + 7;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        String str = TAG;
        int i4 = i2 + Opcodes.LMUL;
        h = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v24, types: [byte[]] */
    private static void i(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 952
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels.ManageSamsungPayMockViewModel.i(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
