package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s7.smali */
public interface s7 {
    public static final w A;
    public static final w B;
    public static final w C;
    public static final w D;
    public static final w E;
    public static final w F;
    public static final w G;
    public static final w H;
    public static final w I;
    public static final w J;
    public static final w K;
    public static final w L;
    public static final w M;
    public static final w N;
    public static final w O;
    public static final w P;
    public static final w Q;
    public static final w R;
    public static final w a;
    public static final w b;
    public static final w c;
    public static final w d;
    public static final w e;
    public static final w f;
    public static final w g;
    public static final w h;
    public static final w i;
    public static final w j;
    public static final w k;
    public static final w l;
    public static final w m;
    public static final w n;

    /* renamed from: o, reason: collision with root package name */
    public static final w f29o;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.2.804.2.1.1.1");
        a = wVar;
        b = wVar.a("1.3.1.1");
        c = wVar.a("1.3.1.1.1.1");
        d = wVar.a("1.2.2.1");
        e = wVar.a("1.2.2.2");
        f = wVar.a("1.2.2.3");
        g = wVar.a("1.2.2.4");
        h = wVar.a("1.2.2.5");
        i = wVar.a("1.2.2.6");
        j = wVar.a("1.1.3.1.1");
        k = wVar.a("1.1.3.1.2");
        l = wVar.a("1.1.3.1.3");
        m = wVar.a("1.1.3.2.1");
        n = wVar.a("1.1.3.2.2");
        f29o = wVar.a("1.1.3.2.3");
        p = wVar.a("1.1.3.3.1");
        q = wVar.a("1.1.3.3.2");
        r = wVar.a("1.1.3.3.3");
        s = wVar.a("1.1.3.4.1");
        t = wVar.a("1.1.3.4.2");
        u = wVar.a("1.1.3.4.3");
        v = wVar.a("1.1.3.5.1");
        w = wVar.a("1.1.3.5.2");
        x = wVar.a("1.1.3.5.3");
        y = wVar.a("1.1.3.6.1");
        z = wVar.a("1.1.3.6.2");
        A = wVar.a("1.1.3.6.3");
        B = wVar.a("1.1.3.7.1");
        C = wVar.a("1.1.3.7.2");
        D = wVar.a("1.1.3.7.3");
        E = wVar.a("1.1.3.8.1");
        F = wVar.a("1.1.3.8.2");
        G = wVar.a("1.1.3.8.3");
        H = wVar.a("1.1.3.9.1");
        I = wVar.a("1.1.3.9.2");
        J = wVar.a("1.1.3.9.3");
        K = wVar.a("1.1.3.10.1");
        L = wVar.a("1.1.3.10.2");
        M = wVar.a("1.1.3.10.3");
        w a2 = wVar.a("1");
        N = a2;
        w a3 = a2.a("1");
        O = a3;
        P = a3.a("2");
        w a4 = a2.a("2");
        Q = a4;
        R = a4.a("1");
    }
}
