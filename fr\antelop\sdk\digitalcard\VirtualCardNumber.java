package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.R;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.Date;
import o.dw.b;
import o.eo.e;
import o.eo.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumber.smali */
public final class VirtualCardNumber {
    private final e digitalCard;
    private final o.er.e emvApplication;
    private final j innerVirtualCardNumber;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumber$Status.smali */
    public enum Status {
        Active,
        Suspended
    }

    public VirtualCardNumber(e eVar, o.er.e eVar2, j jVar) {
        this.digitalCard = eVar;
        this.emvApplication = eVar2;
        this.innerVirtualCardNumber = jVar;
    }

    public final String getId() {
        return this.innerVirtualCardNumber.e();
    }

    public final Status getStatus() {
        return this.innerVirtualCardNumber.a().d();
    }

    public final String getProviderVcnId() {
        return this.innerVirtualCardNumber.b();
    }

    public final String getBin() {
        return this.innerVirtualCardNumber.d();
    }

    public final String getLastDigits() {
        return this.innerVirtualCardNumber.c();
    }

    public final Date getExpiryDate() {
        return this.innerVirtualCardNumber.g();
    }

    public final String getName() {
        return this.innerVirtualCardNumber.f();
    }

    public final Date getCreationDate() {
        return this.innerVirtualCardNumber.j();
    }

    public final Integer getMaxPaymentNumber() {
        return this.innerVirtualCardNumber.i();
    }

    public final void suspend(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerVirtualCardNumber.e(context, this.emvApplication.g(), this.digitalCard, operationCallback);
    }

    public final void resume(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerVirtualCardNumber.d(context, this.emvApplication.g(), this.digitalCard, operationCallback);
    }

    public final SecureVirtualCardNumberResume getSecureVirtualCardNumberResume() {
        return new SecureVirtualCardNumberResume(this.innerVirtualCardNumber.e(this.emvApplication.g(), this.digitalCard));
    }

    public final void delete(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerVirtualCardNumber.a(context, this.emvApplication.g(), this.digitalCard, operationCallback);
    }

    public final SecureVirtualCardNumberDisplay getSecureVirtualCardNumberDisplay() {
        return new SecureVirtualCardNumberDisplay(this.innerVirtualCardNumber.b(this.emvApplication.f(), this.digitalCard, new b() { // from class: fr.antelop.sdk.digitalcard.VirtualCardNumber.1
            @Override // o.dw.b
            public int getThemeResource(o.dw.e eVar) {
                o.dw.e.a();
                return R.style.antelopSecureVirtualCardNumberDisplayThemeInternal;
            }
        }));
    }
}
