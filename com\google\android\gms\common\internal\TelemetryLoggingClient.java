package com.google.android.gms.common.internal;

import com.google.android.gms.common.api.Has<PERSON><PERSON>;
import com.google.android.gms.tasks.Task;
import com.google.errorprone.annotations.DoNotMock;
import com.google.errorprone.annotations.RestrictedInheritance;
import com.google.errorprone.annotations.ResultIgnorabilityUnspecified;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
@DoNotMock("Use canonical fakes instead. go/cheezhead-testing-methodology")
@RestrictedInheritance(allowedOnPath = ".*java.*/com/google/android/gms.*", explanation = "Use canonical fakes instead.", link = "go/gmscore-restrictedinheritance")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\internal\TelemetryLoggingClient.smali */
public interface TelemetryLoggingClient extends Has<PERSON><PERSON><PERSON>ey<TelemetryLoggingOptions> {
    @ResultIgnorabilityUnspecified
    Task<Void> log(TelemetryData telemetryData);
}
