package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.KryoSerializable;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Util;
import java.lang.reflect.Constructor;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Currency;
import java.util.Date;
import java.util.EnumSet;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentSkipListMap;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers.smali */
public class DefaultSerializers {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$VoidSerializer.smali */
    public static class VoidSerializer extends ImmutableSerializer {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Object object) {
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Object read(Kryo kryo, Input input, Class type) {
            return null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$BooleanSerializer.smali */
    public static class BooleanSerializer extends ImmutableSerializer<Boolean> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Boolean>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Boolean object) {
            output.writeBoolean(object.booleanValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Boolean read(Kryo kryo, Input input, Class<? extends Boolean> type) {
            return Boolean.valueOf(input.readBoolean());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$ByteSerializer.smali */
    public static class ByteSerializer extends ImmutableSerializer<Byte> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Byte>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Byte object) {
            output.writeByte(object.byteValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Byte read(Kryo kryo, Input input, Class<? extends Byte> type) {
            return Byte.valueOf(input.readByte());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CharSerializer.smali */
    public static class CharSerializer extends ImmutableSerializer<Character> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Character>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Character object) {
            output.writeChar(object.charValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Character read(Kryo kryo, Input input, Class<? extends Character> type) {
            return Character.valueOf(input.readChar());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$ShortSerializer.smali */
    public static class ShortSerializer extends ImmutableSerializer<Short> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Short>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Short object) {
            output.writeShort(object.shortValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Short read(Kryo kryo, Input input, Class<? extends Short> type) {
            return Short.valueOf(input.readShort());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$IntSerializer.smali */
    public static class IntSerializer extends ImmutableSerializer<Integer> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Integer>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Integer object) {
            output.writeInt(object.intValue(), false);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Integer read(Kryo kryo, Input input, Class<? extends Integer> type) {
            return Integer.valueOf(input.readInt(false));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$LongSerializer.smali */
    public static class LongSerializer extends ImmutableSerializer<Long> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Long>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Long object) {
            output.writeVarLong(object.longValue(), false);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Long read(Kryo kryo, Input input, Class<? extends Long> type) {
            return Long.valueOf(input.readVarLong(false));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$FloatSerializer.smali */
    public static class FloatSerializer extends ImmutableSerializer<Float> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Float>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Float object) {
            output.writeFloat(object.floatValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Float read(Kryo kryo, Input input, Class<? extends Float> type) {
            return Float.valueOf(input.readFloat());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$DoubleSerializer.smali */
    public static class DoubleSerializer extends ImmutableSerializer<Double> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Double>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Double object) {
            output.writeDouble(object.doubleValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Double read(Kryo kryo, Input input, Class<? extends Double> type) {
            return Double.valueOf(input.readDouble());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$StringSerializer.smali */
    public static class StringSerializer extends ImmutableSerializer<String> {
        public StringSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends String>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, String object) {
            output.writeString(object);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public String read(Kryo kryo, Input input, Class<? extends String> type) {
            return input.readString();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$BigIntegerSerializer.smali */
    public static class BigIntegerSerializer extends ImmutableSerializer<BigInteger> {
        public BigIntegerSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends BigInteger>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, BigInteger object) {
            if (object == null) {
                output.writeByte((byte) 0);
                return;
            }
            if (object == BigInteger.ZERO) {
                output.writeByte(2);
                output.writeByte(0);
            } else {
                byte[] bytes = object.toByteArray();
                output.writeVarInt(bytes.length + 1, true);
                output.writeBytes(bytes);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public BigInteger read(Kryo kryo, Input input, Class<? extends BigInteger> type) {
            int length = input.readVarInt(true);
            if (length == 0) {
                return null;
            }
            byte[] bytes = input.readBytes(length - 1);
            if (type != BigInteger.class && type != null) {
                try {
                    Constructor<? extends BigInteger> constructor = type.getConstructor(byte[].class);
                    if (!constructor.isAccessible()) {
                        try {
                            constructor.setAccessible(true);
                        } catch (SecurityException e) {
                        }
                    }
                    return constructor.newInstance(bytes);
                } catch (Exception ex) {
                    throw new KryoException(ex);
                }
            }
            if (length == 2) {
                switch (bytes[0]) {
                    case 0:
                        return BigInteger.ZERO;
                    case 1:
                        return BigInteger.ONE;
                    case 10:
                        return BigInteger.TEN;
                }
            }
            return new BigInteger(bytes);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$BigDecimalSerializer.smali */
    public static class BigDecimalSerializer extends ImmutableSerializer<BigDecimal> {
        private final BigIntegerSerializer bigIntegerSerializer = new BigIntegerSerializer();

        public BigDecimalSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends BigDecimal>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, BigDecimal object) {
            if (object == null) {
                output.writeByte((byte) 0);
            } else if (object == BigDecimal.ZERO) {
                this.bigIntegerSerializer.write(kryo, output, BigInteger.ZERO);
                output.writeInt(0, false);
            } else {
                this.bigIntegerSerializer.write(kryo, output, object.unscaledValue());
                output.writeInt(object.scale(), false);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public BigDecimal read(Kryo kryo, Input input, Class<? extends BigDecimal> type) {
            BigInteger unscaledValue = this.bigIntegerSerializer.read(kryo, input, BigInteger.class);
            if (unscaledValue == null) {
                return null;
            }
            int scale = input.readInt(false);
            if (type != BigDecimal.class && type != null) {
                try {
                    Constructor<? extends BigDecimal> constructor = type.getConstructor(BigInteger.class, Integer.TYPE);
                    if (!constructor.isAccessible()) {
                        try {
                            constructor.setAccessible(true);
                        } catch (SecurityException e) {
                        }
                    }
                    return constructor.newInstance(unscaledValue, Integer.valueOf(scale));
                } catch (Exception ex) {
                    throw new KryoException(ex);
                }
            }
            if (unscaledValue == BigInteger.ZERO && scale == 0) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(unscaledValue, scale);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$ClassSerializer.smali */
    public static class ClassSerializer extends ImmutableSerializer<Class> {
        public ClassSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Class>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Class type) {
            kryo.writeClass(output, type);
            if (type != null) {
                if (type.isPrimitive() || Util.isWrapperClass(type)) {
                    output.writeBoolean(type.isPrimitive());
                }
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Class read(Kryo kryo, Input input, Class<? extends Class> ignored) {
            Registration registration = kryo.readClass(input);
            if (registration == null) {
                return null;
            }
            Class type = registration.getType();
            if (!type.isPrimitive() || input.readBoolean()) {
                return type;
            }
            return Util.getWrapperClass(type);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$DateSerializer.smali */
    public static class DateSerializer extends Serializer<Date> {
        private Date create(Kryo kryo, Class<? extends Date> type, long time) throws KryoException {
            if (type == Date.class || type == null) {
                return new Date(time);
            }
            if (type == Timestamp.class) {
                return new Timestamp(time);
            }
            if (type == java.sql.Date.class) {
                return new java.sql.Date(time);
            }
            if (type == Time.class) {
                return new Time(time);
            }
            try {
                Constructor<? extends Date> constructor = type.getConstructor(Long.TYPE);
                if (!constructor.isAccessible()) {
                    try {
                        constructor.setAccessible(true);
                    } catch (SecurityException e) {
                    }
                }
                return constructor.newInstance(Long.valueOf(time));
            } catch (Exception e2) {
                Date d = (Date) kryo.newInstance(type);
                d.setTime(time);
                return d;
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Date object) {
            output.writeVarLong(object.getTime(), true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Date read(Kryo kryo, Input input, Class<? extends Date> type) {
            return create(kryo, type, input.readVarLong(true));
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // com.esotericsoftware.kryo.Serializer
        public Date copy(Kryo kryo, Date original) {
            return create(kryo, original.getClass(), original.getTime());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$EnumSerializer.smali */
    public static class EnumSerializer extends ImmutableSerializer<Enum> {
        private Object[] enumConstants;

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Enum>) cls);
        }

        public EnumSerializer(Class<? extends Enum> type) {
            setAcceptsNull(true);
            Object[] enumConstants = type.getEnumConstants();
            this.enumConstants = enumConstants;
            if (enumConstants == null && !Enum.class.equals(type)) {
                throw new IllegalArgumentException("The type must be an enum: " + type);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Enum object) {
            if (object == null) {
                output.writeVarInt(0, true);
            } else {
                output.writeVarInt(object.ordinal() + 1, true);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Enum read(Kryo kryo, Input input, Class<? extends Enum> type) {
            int ordinal = input.readVarInt(true);
            if (ordinal == 0) {
                return null;
            }
            int ordinal2 = ordinal - 1;
            if (ordinal2 >= 0) {
                Object[] objArr = this.enumConstants;
                if (ordinal2 <= objArr.length - 1) {
                    Object constant = objArr[ordinal2];
                    return (Enum) constant;
                }
            }
            throw new KryoException("Invalid ordinal for enum \"" + type.getName() + "\": " + ordinal2);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$EnumSetSerializer.smali */
    public static class EnumSetSerializer extends Serializer<EnumSet> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, EnumSet object) {
            Serializer serializer;
            if (object.isEmpty()) {
                EnumSet tmp = EnumSet.complementOf(object);
                if (tmp.isEmpty()) {
                    throw new KryoException("An EnumSet must have a defined Enum to be serialized.");
                }
                serializer = kryo.writeClass(output, tmp.iterator().next().getClass()).getSerializer();
            } else {
                serializer = kryo.writeClass(output, object.iterator().next().getClass()).getSerializer();
            }
            output.writeVarInt(object.size(), true);
            Iterator it = object.iterator();
            while (it.hasNext()) {
                Object element = it.next();
                serializer.write(kryo, output, element);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public EnumSet read(Kryo kryo, Input input, Class<? extends EnumSet> type) {
            Registration registration = kryo.readClass(input);
            EnumSet object = EnumSet.noneOf(registration.getType());
            Serializer serializer = registration.getSerializer();
            int length = input.readVarInt(true);
            for (int i = 0; i < length; i++) {
                object.add(serializer.read(kryo, input, null));
            }
            return object;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public EnumSet copy(Kryo kryo, EnumSet original) {
            return EnumSet.copyOf(original);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CurrencySerializer.smali */
    public static class CurrencySerializer extends ImmutableSerializer<Currency> {
        public CurrencySerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Currency>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Currency object) {
            output.writeString(object == null ? null : object.getCurrencyCode());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Currency read(Kryo kryo, Input input, Class<? extends Currency> type) {
            String currencyCode = input.readString();
            if (currencyCode == null) {
                return null;
            }
            return Currency.getInstance(currencyCode);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$StringBufferSerializer.smali */
    public static class StringBufferSerializer extends Serializer<StringBuffer> {
        public StringBufferSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, StringBuffer object) {
            output.writeString(object == null ? null : object.toString());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public StringBuffer read(Kryo kryo, Input input, Class<? extends StringBuffer> type) {
            String value = input.readString();
            if (value == null) {
                return null;
            }
            return new StringBuffer(value);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public StringBuffer copy(Kryo kryo, StringBuffer original) {
            return new StringBuffer(original);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$StringBuilderSerializer.smali */
    public static class StringBuilderSerializer extends Serializer<StringBuilder> {
        public StringBuilderSerializer() {
            setAcceptsNull(true);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, StringBuilder object) {
            output.writeString(object == null ? null : object.toString());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public StringBuilder read(Kryo kryo, Input input, Class<? extends StringBuilder> type) {
            return input.readStringBuilder();
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public StringBuilder copy(Kryo kryo, StringBuilder original) {
            return new StringBuilder(original);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$KryoSerializableSerializer.smali */
    public static class KryoSerializableSerializer extends Serializer<KryoSerializable> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, KryoSerializable object) {
            object.write(kryo, output);
        }

        /* JADX WARN: Can't rename method to resolve collision */
        @Override // com.esotericsoftware.kryo.Serializer
        public KryoSerializable read(Kryo kryo, Input input, Class<? extends KryoSerializable> type) {
            KryoSerializable object = (KryoSerializable) kryo.newInstance(type);
            kryo.reference(object);
            object.read(kryo, input);
            return object;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CollectionsEmptyListSerializer.smali */
    public static class CollectionsEmptyListSerializer extends ImmutableSerializer<Collection> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Collection>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Collection object) {
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Collection read(Kryo kryo, Input input, Class<? extends Collection> type) {
            return Collections.EMPTY_LIST;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CollectionsEmptyMapSerializer.smali */
    public static class CollectionsEmptyMapSerializer extends ImmutableSerializer<Map> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Map>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Map object) {
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Map read(Kryo kryo, Input input, Class<? extends Map> type) {
            return Collections.EMPTY_MAP;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CollectionsEmptySetSerializer.smali */
    public static class CollectionsEmptySetSerializer extends ImmutableSerializer<Set> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Set>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Set object) {
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Set read(Kryo kryo, Input input, Class<? extends Set> type) {
            return Collections.EMPTY_SET;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CollectionsSingletonMapSerializer.smali */
    public static class CollectionsSingletonMapSerializer extends Serializer<Map> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Map object) {
            Map.Entry entry = (Map.Entry) object.entrySet().iterator().next();
            kryo.writeClassAndObject(output, entry.getKey());
            kryo.writeClassAndObject(output, entry.getValue());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Map read(Kryo kryo, Input input, Class<? extends Map> type) {
            Object key = kryo.readClassAndObject(input);
            Object value = kryo.readClassAndObject(input);
            return Collections.singletonMap(key, value);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Map copy(Kryo kryo, Map original) {
            Map.Entry entry = (Map.Entry) original.entrySet().iterator().next();
            return Collections.singletonMap(kryo.copy(entry.getKey()), kryo.copy(entry.getValue()));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CollectionsSingletonSetSerializer.smali */
    public static class CollectionsSingletonSetSerializer extends Serializer<Set> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Set object) {
            kryo.writeClassAndObject(output, object.iterator().next());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Set read(Kryo kryo, Input input, Class<? extends Set> type) {
            return Collections.singleton(kryo.readClassAndObject(input));
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Set copy(Kryo kryo, Set original) {
            return Collections.singleton(kryo.copy(original.iterator().next()));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CalendarSerializer.smali */
    public static class CalendarSerializer extends Serializer<Calendar> {
        private static final long DEFAULT_GREGORIAN_CUTOVER = -12219292800000L;
        TimeZoneSerializer timeZoneSerializer = new TimeZoneSerializer();

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Calendar object) {
            this.timeZoneSerializer.write(kryo, output, object.getTimeZone());
            output.writeVarLong(object.getTimeInMillis(), true);
            output.writeBoolean(object.isLenient());
            output.writeInt(object.getFirstDayOfWeek(), true);
            output.writeInt(object.getMinimalDaysInFirstWeek(), true);
            if (object instanceof GregorianCalendar) {
                output.writeVarLong(((GregorianCalendar) object).getGregorianChange().getTime(), false);
            } else {
                output.writeVarLong(DEFAULT_GREGORIAN_CUTOVER, false);
            }
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Calendar read(Kryo kryo, Input input, Class<? extends Calendar> type) {
            Calendar result = Calendar.getInstance(this.timeZoneSerializer.read(kryo, input, TimeZone.class));
            result.setTimeInMillis(input.readVarLong(true));
            result.setLenient(input.readBoolean());
            result.setFirstDayOfWeek(input.readInt(true));
            result.setMinimalDaysInFirstWeek(input.readInt(true));
            long gregorianChange = input.readVarLong(false);
            if (gregorianChange != DEFAULT_GREGORIAN_CUTOVER && (result instanceof GregorianCalendar)) {
                ((GregorianCalendar) result).setGregorianChange(new Date(gregorianChange));
            }
            return result;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Calendar copy(Kryo kryo, Calendar original) {
            return (Calendar) original.clone();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$TreeMapSerializer.smali */
    public static class TreeMapSerializer extends MapSerializer<TreeMap> {
        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public void writeHeader(Kryo kryo, Output output, TreeMap treeSet) {
            kryo.writeClassAndObject(output, treeSet.comparator());
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public TreeMap create(Kryo kryo, Input input, Class<? extends TreeMap> type, int size) {
            return createTreeMap(type, (Comparator) kryo.readClassAndObject(input));
        }

        /* JADX INFO: Access modifiers changed from: protected */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public TreeMap createCopy(Kryo kryo, TreeMap original) {
            return createTreeMap(original.getClass(), original.comparator());
        }

        private TreeMap createTreeMap(Class<? extends TreeMap> type, Comparator comparator) {
            if (type == TreeMap.class || type == null) {
                return new TreeMap(comparator);
            }
            try {
                Constructor constructor = type.getConstructor(Comparator.class);
                if (!constructor.isAccessible()) {
                    try {
                        constructor.setAccessible(true);
                    } catch (SecurityException e) {
                    }
                }
                return constructor.newInstance(comparator);
            } catch (Exception ex) {
                throw new KryoException(ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$ConcurrentSkipListMapSerializer.smali */
    public static class ConcurrentSkipListMapSerializer extends MapSerializer<ConcurrentSkipListMap> {
        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public void writeHeader(Kryo kryo, Output output, ConcurrentSkipListMap concurrentSkipListMap) {
            kryo.writeClassAndObject(output, concurrentSkipListMap.comparator());
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public ConcurrentSkipListMap create(Kryo kryo, Input input, Class<? extends ConcurrentSkipListMap> type, int size) {
            return createConcurrentSkipListMap(type, (Comparator) kryo.readClassAndObject(input));
        }

        /* JADX INFO: Access modifiers changed from: protected */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // com.esotericsoftware.kryo.serializers.MapSerializer
        public ConcurrentSkipListMap createCopy(Kryo kryo, ConcurrentSkipListMap original) {
            return createConcurrentSkipListMap(original.getClass(), original.comparator());
        }

        private ConcurrentSkipListMap createConcurrentSkipListMap(Class<? extends ConcurrentSkipListMap> type, Comparator comparator) {
            if (type == ConcurrentSkipListMap.class || type == null) {
                return new ConcurrentSkipListMap(comparator);
            }
            try {
                Constructor constructor = type.getConstructor(Comparator.class);
                if (!constructor.isAccessible()) {
                    try {
                        constructor.setAccessible(true);
                    } catch (SecurityException e) {
                    }
                }
                return constructor.newInstance(comparator);
            } catch (Exception ex) {
                throw new KryoException(ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$TreeSetSerializer.smali */
    public static class TreeSetSerializer extends CollectionSerializer<TreeSet> {
        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public void writeHeader(Kryo kryo, Output output, TreeSet treeSet) {
            kryo.writeClassAndObject(output, treeSet.comparator());
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public TreeSet create(Kryo kryo, Input input, Class<? extends TreeSet> type, int size) {
            return createTreeSet(type, (Comparator) kryo.readClassAndObject(input));
        }

        /* JADX INFO: Access modifiers changed from: protected */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public TreeSet createCopy(Kryo kryo, TreeSet original) {
            return createTreeSet(original.getClass(), original.comparator());
        }

        private TreeSet createTreeSet(Class<? extends Collection> type, Comparator comparator) {
            if (type == TreeSet.class || type == null) {
                return new TreeSet(comparator);
            }
            try {
                Constructor constructor = type.getConstructor(Comparator.class);
                if (!constructor.isAccessible()) {
                    try {
                        constructor.setAccessible(true);
                    } catch (SecurityException e) {
                    }
                }
                return (TreeSet) constructor.newInstance(comparator);
            } catch (Exception ex) {
                throw new KryoException(ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$PriorityQueueSerializer.smali */
    public static class PriorityQueueSerializer extends CollectionSerializer<PriorityQueue> {
        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public void writeHeader(Kryo kryo, Output output, PriorityQueue queue) {
            kryo.writeClassAndObject(output, queue.comparator());
        }

        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public PriorityQueue create(Kryo kryo, Input input, Class<? extends PriorityQueue> type, int size) {
            return createPriorityQueue(type, size, (Comparator) kryo.readClassAndObject(input));
        }

        /* JADX INFO: Access modifiers changed from: protected */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public PriorityQueue createCopy(Kryo kryo, PriorityQueue original) {
            return createPriorityQueue(original.getClass(), original.size(), original.comparator());
        }

        private PriorityQueue createPriorityQueue(Class<? extends Collection> type, int size, Comparator comparator) {
            int initialCapacity = Math.max(size, 1);
            if (type == PriorityQueue.class || type == null) {
                return new PriorityQueue(initialCapacity, comparator);
            }
            try {
                Constructor constructor = type.getConstructor(Integer.TYPE, Comparator.class);
                if (!constructor.isAccessible()) {
                    try {
                        constructor.setAccessible(true);
                    } catch (SecurityException e) {
                    }
                }
                return (PriorityQueue) constructor.newInstance(Integer.valueOf(initialCapacity), comparator);
            } catch (Exception ex) {
                throw new KryoException(ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$LocaleSerializer.smali */
    public static class LocaleSerializer extends ImmutableSerializer<Locale> {
        public static final Locale SPANISH = new Locale("es", "", "");
        public static final Locale SPAIN = new Locale("es", "ES", "");

        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Locale>) cls);
        }

        protected Locale create(String language, String country, String variant) {
            Locale defaultLocale = Locale.getDefault();
            if (isSameLocale(defaultLocale, language, country, variant)) {
                return defaultLocale;
            }
            if (defaultLocale != Locale.US && isSameLocale(Locale.US, language, country, variant)) {
                return Locale.US;
            }
            if (isSameLocale(Locale.ENGLISH, language, country, variant)) {
                return Locale.ENGLISH;
            }
            if (isSameLocale(Locale.GERMAN, language, country, variant)) {
                return Locale.GERMAN;
            }
            Locale locale = SPANISH;
            if (isSameLocale(locale, language, country, variant)) {
                return locale;
            }
            if (isSameLocale(Locale.FRENCH, language, country, variant)) {
                return Locale.FRENCH;
            }
            if (isSameLocale(Locale.ITALIAN, language, country, variant)) {
                return Locale.ITALIAN;
            }
            if (isSameLocale(Locale.JAPANESE, language, country, variant)) {
                return Locale.JAPANESE;
            }
            if (isSameLocale(Locale.KOREAN, language, country, variant)) {
                return Locale.KOREAN;
            }
            if (isSameLocale(Locale.SIMPLIFIED_CHINESE, language, country, variant)) {
                return Locale.SIMPLIFIED_CHINESE;
            }
            if (isSameLocale(Locale.CHINESE, language, country, variant)) {
                return Locale.CHINESE;
            }
            if (isSameLocale(Locale.TRADITIONAL_CHINESE, language, country, variant)) {
                return Locale.TRADITIONAL_CHINESE;
            }
            if (isSameLocale(Locale.UK, language, country, variant)) {
                return Locale.UK;
            }
            if (isSameLocale(Locale.GERMANY, language, country, variant)) {
                return Locale.GERMANY;
            }
            Locale locale2 = SPAIN;
            return isSameLocale(locale2, language, country, variant) ? locale2 : isSameLocale(Locale.FRANCE, language, country, variant) ? Locale.FRANCE : isSameLocale(Locale.ITALY, language, country, variant) ? Locale.ITALY : isSameLocale(Locale.JAPAN, language, country, variant) ? Locale.JAPAN : isSameLocale(Locale.KOREA, language, country, variant) ? Locale.KOREA : isSameLocale(Locale.CANADA, language, country, variant) ? Locale.CANADA : isSameLocale(Locale.CANADA_FRENCH, language, country, variant) ? Locale.CANADA_FRENCH : new Locale(language, country, variant);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Locale l) {
            output.writeAscii(l.getLanguage());
            output.writeAscii(l.getCountry());
            output.writeString(l.getVariant());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Locale read(Kryo kryo, Input input, Class<? extends Locale> type) {
            String language = input.readString();
            String country = input.readString();
            String variant = input.readString();
            return create(language, country, variant);
        }

        protected static boolean isSameLocale(Locale locale, String language, String country, String variant) {
            return locale.getLanguage().equals(language) && locale.getCountry().equals(country) && locale.getVariant().equals(variant);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$CharsetSerializer.smali */
    public static class CharsetSerializer extends ImmutableSerializer<Charset> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends Charset>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, Charset object) {
            output.writeString(object.name());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public Charset read(Kryo kryo, Input input, Class<? extends Charset> type) {
            return Charset.forName(input.readString());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$URLSerializer.smali */
    public static class URLSerializer extends ImmutableSerializer<URL> {
        @Override // com.esotericsoftware.kryo.Serializer
        public /* bridge */ /* synthetic */ Object read(Kryo kryo, Input input, Class cls) {
            return read(kryo, input, (Class<? extends URL>) cls);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, URL object) {
            output.writeString(object.toExternalForm());
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public URL read(Kryo kryo, Input input, Class<? extends URL> type) {
            try {
                return new URL(input.readString());
            } catch (MalformedURLException ex) {
                throw new KryoException(ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$ArraysAsListSerializer.smali */
    public static class ArraysAsListSerializer extends CollectionSerializer<List> {
        /* JADX INFO: Access modifiers changed from: protected */
        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public List create(Kryo kryo, Input input, Class<? extends List> cls, int size) {
            return new ArrayList(size);
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer, com.esotericsoftware.kryo.Serializer
        public List read(Kryo kryo, Input input, Class type) {
            List list = (List) super.read(kryo, input, type);
            if (list == null) {
                return null;
            }
            return Arrays.asList(list.toArray());
        }

        @Override // com.esotericsoftware.kryo.serializers.CollectionSerializer
        public List copy(Kryo kryo, List original) {
            Object[] copyArr = new Object[original.size()];
            List<Object> copy = Arrays.asList(copyArr);
            kryo.reference(copy);
            for (int i = 0; i < original.size(); i++) {
                copyArr[i] = kryo.copy(original.get(i));
            }
            return copy;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\DefaultSerializers$BitSetSerializer.smali */
    public static class BitSetSerializer extends Serializer<BitSet> {
        @Override // com.esotericsoftware.kryo.Serializer
        public void write(Kryo kryo, Output output, BitSet set) {
            long[] values = set.toLongArray();
            output.writeVarInt(values.length, true);
            output.writeLongs(values, 0, values.length);
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public BitSet read(Kryo kryo, Input input, Class<? extends BitSet> cls) {
            int length = input.readVarInt(true);
            long[] values = input.readLongs(length);
            BitSet set = BitSet.valueOf(values);
            return set;
        }

        @Override // com.esotericsoftware.kryo.Serializer
        public BitSet copy(Kryo kryo, BitSet original) {
            return BitSet.valueOf(original.toLongArray());
        }
    }
}
