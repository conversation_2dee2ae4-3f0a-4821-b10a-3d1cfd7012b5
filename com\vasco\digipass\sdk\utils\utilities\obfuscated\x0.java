package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x0.smali */
public class x0 extends x {
    private final int L;
    private final x[] R;

    public x0(byte[] bArr) {
        this(bArr, 1000);
    }

    static byte[] a(x[] xVarArr) {
        int length = xVarArr.length;
        if (length == 0) {
            return x.C;
        }
        if (length == 1) {
            return xVarArr[0].b;
        }
        int i = 0;
        for (x xVar : xVarArr) {
            i += xVar.b.length;
        }
        byte[] bArr = new byte[i];
        int i2 = 0;
        for (x xVar2 : xVarArr) {
            byte[] bArr2 = xVar2.b;
            System.arraycopy(bArr2, 0, bArr, i2, bArr2.length);
            i2 += bArr2.length;
        }
        return bArr;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return true;
    }

    public x0(x[] xVarArr) {
        this(xVarArr, 1000);
    }

    public x0(byte[] bArr, int i) {
        this(bArr, null, i);
    }

    public x0(x[] xVarArr, int i) {
        this(a(xVarArr), xVarArr, i);
    }

    private x0(byte[] bArr, x[] xVarArr, int i) {
        super(bArr);
        this.R = xVarArr;
        this.L = i;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        int i = z ? 4 : 3;
        if (this.R == null) {
            int length = this.b.length;
            int i2 = this.L;
            int i3 = length / i2;
            int a = i + (f2.a(true, i2) * i3);
            int length2 = this.b.length - (i3 * this.L);
            return length2 > 0 ? a + f2.a(true, length2) : a;
        }
        int i4 = 0;
        while (true) {
            x[] xVarArr = this.R;
            if (i4 >= xVarArr.length) {
                return i;
            }
            i += xVarArr[i4].a(true);
            i4++;
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 36);
        zVar.c(128);
        x[] xVarArr = this.R;
        if (xVarArr == null) {
            int i = 0;
            while (true) {
                byte[] bArr = this.b;
                if (i >= bArr.length) {
                    break;
                }
                int min = Math.min(bArr.length - i, this.L);
                f2.a(zVar, true, this.b, i, min);
                i += min;
            }
        } else {
            zVar.a((b0[]) xVarArr);
        }
        zVar.c(0);
        zVar.c(0);
    }
}
