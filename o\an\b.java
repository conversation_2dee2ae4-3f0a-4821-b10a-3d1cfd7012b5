package o.an;

import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\b.smali */
public final class b implements a<C0023b, c> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static char c;
    private static int d;
    private static int e;
    private static int h;
    private final C0023b a;

    /* renamed from: o.an.b$b, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\b$b.smali */
    public static final class C0023b {
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        h = 1;
        a();
        Process.myTid();
        TextUtils.indexOf("", "", 0, 0);
        TextUtils.getOffsetBefore("", 0);
        int i = h + 39;
        d = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        e = 874635388;
        b = new char[]{30570, 30584, 29843, 30530, 30587, 30534, 30542, 29842, 29852, 30537, 30552, 30569, 30590, 29845, 30574, 29841, 30556, 30585, 30566, 30588, 30571, 29853, 30554, 30505, 30567, 30538, 30562, 30568, 29844, 30591, 30582, 30544, 30572, 30589, 29840, 30541, 30553, 30561, 30557, 30563, 29846, 29847, 30560, 30559, 30531, 30555, 30511, 30539, 30586};
        c = (char) 17042;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 3 - r8
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r7 = r7 + 69
            byte[] r0 = o.an.b.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L33
        L19:
            r3 = r2
        L1a:
            int r8 = r8 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L33:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.b.i(int, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{84, 72, 115, -24};
        $$b = 206;
    }

    @Override // o.an.a
    public final /* synthetic */ c a(o.eg.b bVar) throws o.eg.d {
        int i = d + 79;
        h = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                c(bVar);
                obj.hashCode();
                throw null;
            default:
                c c2 = c(bVar);
                int i2 = d + 53;
                h = i2 % 128;
                switch (i2 % 2 == 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return c2;
                }
        }
    }

    public b(C0023b c0023b) {
        this.a = c0023b;
    }

    @Override // o.an.a
    public final void b(o.eg.b bVar) throws o.eg.d {
        int i = h + 3;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 16, "￼\u000b￫\u0006\u0002￼\u0005￩￼\b\f￼\n\u000b\u0006\t￠\n\n\f￼\t￮\ufff8\u0003\u0003", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 25, 107 - Process.getGidForName(""), false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(TextUtils.getCapsMode("", 0, 0) + 14, "\u000f\u000e\u0011)\u0005\u0007*\u0006\u0012\u0005*\u0013\u0000\u0012", (byte) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 23), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(10 - ExpandableListView.getPackedPositionGroup(0L), "\u0000\u000f㙈㙈\u0001\u0005,\u001f\u001c\u0001", (byte) (130 - AndroidCharacter.getMirror('0')), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g(Color.argb(0, 0, 0, 0) + 13, "\u0002\u0013\u000f\u0017\u0018'&\u0011\u00020.\u0017㗑", (byte) (3 - (KeyEvent.getMaxKeyCode() >> 16)), objArr4);
        bVar.d(intern2, ((String) objArr4[0]).intern());
        int i3 = d + Opcodes.LMUL;
        h = i3 % 128;
        int i4 = i3 % 2;
    }

    private static c c(o.eg.b bVar) throws o.eg.d {
        c.EnumC0024b enumC0024b;
        byte[] decode;
        byte[] bArr;
        byte[] bArr2;
        int i = h + 69;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(15 - ((byte) KeyEvent.getModifierMetaStateMask()), "￼\u000b￫\u0006\u0002￼\u0005￩￼\b\f￼\n\u000b\u0006\t￠\n\n\f￼\t￮\ufff8\u0003\u0003", Color.argb(0, 0, 0, 0) + 26, 107 - MotionEvent.axisFromString(""), false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(View.combineMeasuredStates(0, 0) + 13, "\u0007\u0004\u000b'\u001f-\u0005\u000e\u001c+(\u0010㘘", (byte) (((Process.getThreadPriority(0) + 20) >> 6) + 25), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(22 - (ViewConfiguration.getLongPressTimeout() >> 16), "\u001b\u0013\u0005\u000e\r,)\u0010\u0010'\"\r㙋㙋+*'\u0002\u0002(\u0007.", (byte) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 82), objArr3);
        String q = bVar.q(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        g(View.MeasureSpec.getMode(0) + 7, "\u001b\u0013\u0005\u000e*\u0011㗭", (byte) (TextUtils.getTrimmedLength("") + 1), objArr4);
        String q2 = bVar.q(((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        g(24 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), "\u0012\u0003\u0012\u001a'\u001e*\u000f!\u0017\u0002#\u0005\u0006(\u0010\u0005 /\u001b\u0002#㙐", (byte) ((-16777118) - Color.rgb(0, 0, 0)), objArr5);
        String q3 = bVar.q(((String) objArr5[0]).intern());
        Object obj = null;
        switch (q != null) {
            case true:
                enumC0024b = c.EnumC0024b.d;
                decode = Base64.decode(q, 10);
                if (q2 == null) {
                    bArr = null;
                    break;
                } else {
                    int i3 = d + 85;
                    h = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            bArr = Base64.decode(q2, 10);
                            break;
                        default:
                            bArr = Base64.decode(q2, 61);
                            break;
                    }
                }
            default:
                decode = null;
                bArr = null;
                enumC0024b = null;
                break;
        }
        switch (q3 == null) {
            case true:
                bArr2 = null;
                break;
            default:
                if (enumC0024b != null) {
                    Object[] objArr6 = new Object[1];
                    g(41 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "*\u0000\u0003\u0019-\u0004.\u001a\u0012,\u0019,&+\u0012,\u000e\u000f\u0000\u0012/ \u0001\u0005/\"#\u0002\u00120\f /\u0015/\u0012\u0005\u001c\u000e\u0003㙑", (byte) (ExpandableListView.getPackedPositionGroup(0L) + Opcodes.LMUL), objArr6);
                    throw new o.eg.d(((String) objArr6[0]).intern());
                }
                enumC0024b = c.EnumC0024b.e;
                bArr2 = Base64.decode(q3, 10);
                int i4 = d + 53;
                h = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
        if (enumC0024b == null) {
            Object[] objArr7 = new Object[1];
            f(25 - Drawable.resolveOpacity(0, 0), "\u0018\u001c\u001f\u001e\u000f\u001cￊ\u000b\u001e\u000b\u000eￊ�\ufffe\u0000ￊ\uffd0ￊ�\uffef￮\ufff7ￊ\u0019\ufff8\u001c\u000f \u001c\u000f\u001dￊ\u0017\u0019\u001c\u0010ￊ\u000e\u000f", 38 - TextUtils.indexOf((CharSequence) "", '0'), 89 - (ViewConfiguration.getJumpTapTimeout() >> 16), true, objArr7);
            throw new o.eg.d(((String) objArr7[0]).intern());
        }
        c cVar = new c(enumC0024b, decode, bArr, bArr2);
        int i6 = d + 83;
        h = i6 % 128;
        if (i6 % 2 != 0) {
            return cVar;
        }
        obj.hashCode();
        throw null;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\b$c.smali */
    public static final class c {
        private static int e = 0;
        private static int j = 1;
        private final byte[] a;
        private final byte[] b;
        private final byte[] c;
        private final EnumC0024b d;

        public c(EnumC0024b enumC0024b, byte[] bArr, byte[] bArr2, byte[] bArr3) {
            this.d = enumC0024b;
            this.c = bArr;
            this.b = bArr2;
            this.a = bArr3;
        }

        public final EnumC0024b b() {
            int i = (e + Opcodes.IUSHR) - 1;
            int i2 = i % 128;
            j = i2;
            Object obj = null;
            switch (i % 2 != 0) {
                case false:
                    obj.hashCode();
                    throw null;
                default:
                    EnumC0024b enumC0024b = this.d;
                    int i3 = (i2 & Opcodes.LUSHR) + (i2 | Opcodes.LUSHR);
                    e = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            throw null;
                        default:
                            return enumC0024b;
                    }
            }
        }

        public final byte[] d() {
            int i = e;
            int i2 = (i ^ Opcodes.LUSHR) + ((i & Opcodes.LUSHR) << 1);
            j = i2 % 128;
            switch (i2 % 2 == 0 ? 'U' : '_') {
                case Opcodes.SWAP /* 95 */:
                    return this.c;
                default:
                    int i3 = 89 / 0;
                    return this.c;
            }
        }

        public final byte[] c() {
            int i = j;
            int i2 = (i ^ Opcodes.LSHL) + ((i & Opcodes.LSHL) << 1);
            int i3 = i2 % 128;
            e = i3;
            int i4 = i2 % 2;
            byte[] bArr = this.b;
            int i5 = ((i3 | 1) << 1) - (i3 ^ 1);
            j = i5 % 128;
            int i6 = i5 % 2;
            return bArr;
        }

        public final byte[] a() {
            int i = e;
            int i2 = i + Opcodes.DDIV;
            j = i2 % 128;
            int i3 = i2 % 2;
            byte[] bArr = this.a;
            int i4 = ((i | 85) << 1) - (i ^ 85);
            j = i4 % 128;
            int i5 = i4 % 2;
            return bArr;
        }

        /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
        /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
        /* renamed from: o.an.b$c$b, reason: collision with other inner class name */
        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\b$c$b.smali */
        public static final class EnumC0024b {
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static final /* synthetic */ EnumC0024b[] a;
            private static char[] b;
            private static int c;
            public static final EnumC0024b d;
            public static final EnumC0024b e;
            private static int j;

            static void d() {
                b = new char[]{50923, 50821, 50825, 50817, 50918, 50840, 50846};
            }

            private static void g(byte b2, short s, int i, Object[] objArr) {
                int i2 = b2 + 66;
                byte[] bArr = $$a;
                int i3 = 1 - (i * 3);
                int i4 = 4 - (s * 2);
                byte[] bArr2 = new byte[i3];
                int i5 = -1;
                int i6 = i3 - 1;
                if (bArr == null) {
                    int i7 = i4 + i6;
                    i6 = i6;
                    objArr = objArr;
                    bArr = bArr;
                    bArr2 = bArr2;
                    i5 = -1;
                    i4++;
                    i2 = i7;
                }
                while (true) {
                    int i8 = i5 + 1;
                    bArr2[i8] = (byte) i2;
                    if (i8 == i6) {
                        objArr[0] = new String(bArr2, 0);
                        return;
                    }
                    int i9 = i6;
                    int i10 = i2;
                    int i11 = i4;
                    int i12 = bArr[i4] + i10;
                    i6 = i9;
                    objArr = objArr;
                    bArr = bArr;
                    bArr2 = bArr2;
                    i5 = i8;
                    i4 = i11 + 1;
                    i2 = i12;
                }
            }

            static void init$0() {
                $$a = new byte[]{25, -41, -75, 3};
                $$b = 52;
            }

            private EnumC0024b(String str, int i) {
            }

            private static /* synthetic */ EnumC0024b[] a() {
                EnumC0024b[] enumC0024bArr;
                int i = j + 87;
                int i2 = i % 128;
                c = i2;
                switch (i % 2 != 0) {
                    case false:
                        enumC0024bArr = new EnumC0024b[]{d, e};
                        break;
                    default:
                        enumC0024bArr = new EnumC0024b[5];
                        enumC0024bArr[0] = d;
                        enumC0024bArr[1] = e;
                        break;
                }
                int i3 = i2 + 69;
                j = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 19 : (char) 30) {
                    case 30:
                        return enumC0024bArr;
                    default:
                        int i4 = 45 / 0;
                        return enumC0024bArr;
                }
            }

            public static EnumC0024b valueOf(String str) {
                int i = c + 17;
                j = i % 128;
                int i2 = i % 2;
                EnumC0024b enumC0024b = (EnumC0024b) Enum.valueOf(EnumC0024b.class, str);
                int i3 = j + Opcodes.LMUL;
                c = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return enumC0024b;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            public static EnumC0024b[] values() {
                int i = j + 71;
                c = i % 128;
                switch (i % 2 != 0 ? ':' : 'W') {
                    case Opcodes.ASTORE /* 58 */:
                        int i2 = 38 / 0;
                        return (EnumC0024b[]) a.clone();
                    default:
                        return (EnumC0024b[]) a.clone();
                }
            }

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                c = 0;
                j = 1;
                d();
                Object[] objArr = new Object[1];
                f("\u0001\u0001\u0001\u0000", new int[]{0, 4, 0, 0}, false, objArr);
                d = new EnumC0024b(((String) objArr[0]).intern(), 0);
                Object[] objArr2 = new Object[1];
                f("\u0000\u0000\u0001", new int[]{4, 3, 0, 0}, false, objArr2);
                e = new EnumC0024b(((String) objArr2[0]).intern(), 1);
                a = a();
                int i = c + 91;
                j = i % 128;
                switch (i % 2 != 0) {
                    case true:
                        return;
                    default:
                        int i2 = 72 / 0;
                        return;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:129:0x0364, code lost:
            
                r2 = r0;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
                /*
                    Method dump skipped, instructions count: 966
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.an.b.c.EnumC0024b.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
            }
        }
    }

    private static void f(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] charArray = str != null ? str.toCharArray() : str;
        o.a.h hVar = new o.a.h();
        char[] cArr = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            hVar.b = charArray[hVar.a];
            cArr[hVar.a] = (char) (i3 + hVar.b);
            int i4 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr[i4]), Integer.valueOf(e)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 11, (char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), KeyEvent.getDeadChar(0, 0) + 459);
                    byte b2 = (byte) 0;
                    Object[] objArr3 = new Object[1];
                    i(b2, (byte) (b2 | 38), b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr[i4] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 10, (char) ExpandableListView.getPackedPositionGroup(0L), Color.red(0) + 313);
                        byte b3 = (byte) 0;
                        Object[] objArr5 = new Object[1];
                        i(b3, (byte) (b3 | 40), b3, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        switch (i > 0 ? ':' : '_') {
            case Opcodes.SWAP /* 95 */:
                break;
            default:
                hVar.c = i;
                char[] cArr2 = new char[i2];
                System.arraycopy(cArr, 0, cArr2, 0, i2);
                System.arraycopy(cArr2, 0, cArr, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr2, hVar.c, cArr, 0, i2 - hVar.c);
                break;
        }
        switch (z) {
            case false:
                break;
            default:
                char[] cArr3 = new char[i2];
                hVar.a = 0;
                while (true) {
                    switch (hVar.a < i2) {
                        case true:
                            int i5 = $10 + 73;
                            $11 = i5 % 128;
                            int i6 = i5 % 2;
                            cArr3[hVar.a] = cArr[(i2 - hVar.a) - 1];
                            try {
                                Object[] objArr6 = {hVar, hVar};
                                Object obj3 = o.e.a.s.get(-1412673904);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(TextUtils.indexOf("", "") + 11, (char) Color.argb(0, 0, 0, 0), 313 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                                    byte b4 = (byte) 0;
                                    Object[] objArr7 = new Object[1];
                                    i(b4, (byte) (b4 | 40), b4, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr6);
                                int i7 = $11 + 109;
                                $10 = i7 % 128;
                                int i8 = i7 % 2;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        default:
                            cArr = cArr3;
                            break;
                    }
                }
        }
        objArr[0] = new String(cArr);
    }

    /* JADX WARN: Code restructure failed: missing block: B:101:0x03da, code lost:
    
        r7[r1] = (char) (r7[r1] ^ 13722);
        r1 = r1 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:105:0x03d0, code lost:
    
        r7[r1] = (char) (r7[r1] ^ 16901);
        r1 = r1 + 54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:107:0x03cb, code lost:
    
        r2 = 18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:109:0x03e4, code lost:
    
        r30[0] = new java.lang.String(r7);
     */
    /* JADX WARN: Code restructure failed: missing block: B:10:0x0037, code lost:
    
        r8 = 18;
        r10 = -1401577988;
     */
    /* JADX WARN: Code restructure failed: missing block: B:110:0x03eb, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:111:0x0151, code lost:
    
        r9 = r27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:112:0x0142, code lost:
    
        r9 = 'T';
     */
    /* JADX WARN: Code restructure failed: missing block: B:114:0x00e6, code lost:
    
        r2 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 17, (char) (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), android.graphics.Color.green(0) + 76);
        r10 = (byte) o.an.b.$$a.length;
        r13 = new java.lang.Object[1];
        i((byte) 0, r10, (byte) (r10 - 4), r13);
        r2 = r2.getMethod((java.lang.String) r13[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1401577988, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:115:0x03ec, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:116:0x03ed, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:117:0x03f1, code lost:
    
        if (r1 != null) goto L110;
     */
    /* JADX WARN: Code restructure failed: missing block: B:118:0x03f3, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:119:0x03f4, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x003e, code lost:
    
        switch(r7) {
            case 1: goto L39;
            default: goto L24;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:120:0x0036, code lost:
    
        r7 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:121:0x0022, code lost:
    
        r1 = r28.toCharArray();
     */
    /* JADX WARN: Code restructure failed: missing block: B:125:0x001c, code lost:
    
        if (r28 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0041, code lost:
    
        r7 = r6.length;
        r12 = new char[r7];
        r13 = o.an.b.$11 + 83;
        o.an.b.$10 = r13 % 128;
        r13 = r13 % 2;
        r13 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x004e, code lost:
    
        if (r13 >= r7) goto L122;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0052, code lost:
    
        r15 = new java.lang.Object[]{java.lang.Integer.valueOf(r6[r13])};
        r2 = o.e.a.s.get(java.lang.Integer.valueOf(r10));
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0064, code lost:
    
        if (r2 == null) goto L30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00b5, code lost:
    
        r12[r13] = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r15)).charValue();
        r13 = r13 + 1;
        r8 = 18;
        r10 = -1401577988;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0067, code lost:
    
        r2 = (java.lang.Class) o.e.a.c(((byte) android.view.KeyEvent.getModifierMetaStateMask()) + r8, (char) android.view.KeyEvent.keyCodeFromString(""), (android.view.ViewConfiguration.getEdgeSlop() >> 16) + 76);
        r14 = (byte) o.an.b.$$a.length;
        r10 = new java.lang.Object[1];
        i((byte) 0, r14, (byte) (r14 - 4), r10);
        r2 = r2.getMethod((java.lang.String) r10[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1401577988, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00c0, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00c1, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00c5, code lost:
    
        if (r1 != null) goto L36;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x00c7, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00c8, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00c9, code lost:
    
        r6 = r12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x00cd, code lost:
    
        r7 = new java.lang.Object[]{java.lang.Integer.valueOf(o.an.b.c)};
        r2 = o.e.a.s.get(-1401577988);
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x00e3, code lost:
    
        if (r2 == null) goto L43;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0129, code lost:
    
        r2 = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r7)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0138, code lost:
    
        r7 = new char[r27];
        r10 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x013e, code lost:
    
        if ((r27 % 2) == 0) goto L48;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0140, code lost:
    
        r9 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x0144, code lost:
    
        switch(r9) {
            case 84: goto L51;
            default: goto L50;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0147, code lost:
    
        r9 = r27 - 1;
        r7[r9] = (char) (r1[r9] - r29);
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0152, code lost:
    
        if (r9 <= 1) goto L96;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x0154, code lost:
    
        r5.b = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0158, code lost:
    
        if (r5.b >= r9) goto L123;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x015a, code lost:
    
        r5.e = r1[r5.b];
        r5.a = r1[r5.b + 1];
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x016b, code lost:
    
        if (r5.e != r5.a) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x016d, code lost:
    
        r12 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x0170, code lost:
    
        switch(r12) {
            case 0: goto L62;
            default: goto L61;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0173, code lost:
    
        r13 = r10;
        r7[r5.b] = (char) (r5.e - r29);
        r7[r5.b + 1] = (char) (r5.a - r29);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x03af, code lost:
    
        r5.b += 2;
        r10 = r13;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0190, code lost:
    
        r13 = new java.lang.Object[13];
        r13[12] = r5;
        r13[r10] = java.lang.Integer.valueOf(r2);
        r13[10] = r5;
        r13[9] = r5;
        r13[8] = java.lang.Integer.valueOf(r2);
        r13[7] = r5;
        r13[6] = r5;
        r13[5] = java.lang.Integer.valueOf(r2);
        r13[4] = r5;
        r13[3] = r5;
        r13[2] = java.lang.Integer.valueOf(r2);
        r13[1] = r5;
        r13[0] = r5;
        r8 = o.e.a.s.get(696901393);
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x01dd, code lost:
    
        if (r8 == null) goto L66;
     */
    /* JADX WARN: Code restructure failed: missing block: B:5:0x0016, code lost:
    
        if (r28 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0268, code lost:
    
        if (((java.lang.Integer) ((java.lang.reflect.Method) r8).invoke(null, r13)).intValue() != r5.h) goto L71;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x026a, code lost:
    
        r8 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x026d, code lost:
    
        switch(r8) {
            case 1: goto L76;
            default: goto L73;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0270, code lost:
    
        r13 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0277, code lost:
    
        if (r5.c != r5.d) goto L89;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x038c, code lost:
    
        r8 = (r5.c * r2) + r5.h;
        r10 = (r5.d * r2) + r5.i;
        r7[r5.b] = r6[r8];
        r7[r5.b + 1] = r6[r10];
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x035a, code lost:
    
        r5.i = ((r5.i + r2) - 1) % r2;
        r5.h = ((r5.h + r2) - 1) % r2;
        r8 = (r5.c * r2) + r5.i;
        r10 = (r5.d * r2) + r5.h;
        r7[r5.b] = r6[r8];
        r7[r5.b + 1] = r6[r10];
        r8 = o.an.b.$11 + 3;
        o.an.b.$10 = r8 % 128;
        r8 = r8 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0027, code lost:
    
        r1 = r28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x027f, code lost:
    
        r10 = new java.lang.Object[]{r5, r5, java.lang.Integer.valueOf(r2), java.lang.Integer.valueOf(r2), r5, r5, java.lang.Integer.valueOf(r2), java.lang.Integer.valueOf(r2), r5, java.lang.Integer.valueOf(r2), r5};
        r8 = o.e.a.s.get(1075449051);
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x02bb, code lost:
    
        if (r8 == null) goto L80;
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x02bd, code lost:
    
        r13 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x0330, code lost:
    
        r8 = ((java.lang.Integer) ((java.lang.reflect.Method) r8).invoke(null, r10)).intValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x033d, code lost:
    
        r10 = (r5.d * r2) + r5.h;
        r7[r5.b] = r6[r8];
        r7[r5.b + 1] = r6[r10];
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x02c0, code lost:
    
        r12 = (java.lang.Class) o.e.a.c((android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) (android.view.MotionEvent.axisFromString("") + 1), android.text.TextUtils.indexOf("", "", 0, 0) + 65);
        r13 = (byte) 0;
        r14 = (byte) (r13 + 1);
        r8 = new java.lang.Object[1];
        i(r13, r14, (byte) (r14 - 1), r8);
        r13 = 11;
        r8 = r12.getMethod((java.lang.String) r8[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
        o.e.a.s.put(1075449051, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x0351, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0029, code lost:
    
        r1 = r1;
        r5 = new o.a.m();
        r6 = o.an.b.b;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x0352, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0356, code lost:
    
        if (r1 != null) goto L86;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0358, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x0359, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x026c, code lost:
    
        r8 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:85:0x01e0, code lost:
    
        r8 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getPressedStateDuration() >> 16), (char) (8855 - android.view.MotionEvent.axisFromString("")), (android.view.ViewConfiguration.getTapTimeout() >> 16) + 324);
        r10 = (byte) 0;
        r14 = r10;
        r12 = new java.lang.Object[1];
        i(r10, r14, r14, r12);
        r8 = r8.getMethod((java.lang.String) r12[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
        o.e.a.s.put(696901393, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x03a6, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:88:0x03a7, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x03ab, code lost:
    
        if (r1 != null) goto L93;
     */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0032, code lost:
    
        if (r6 == null) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x03ad, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x03ae, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x016f, code lost:
    
        r12 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:94:0x03b9, code lost:
    
        r1 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:95:0x03ba, code lost:
    
        if (r1 >= r27) goto L128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:96:0x03bc, code lost:
    
        r2 = o.an.b.$10 + 43;
        o.an.b.$11 = r2 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x03c6, code lost:
    
        if ((r2 % 2) != 0) goto L101;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x03c8, code lost:
    
        r2 = '8';
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x03cd, code lost:
    
        switch(r2) {
            case 18: goto L129;
            default: goto L130;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0034, code lost:
    
        r7 = false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1050
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.b.g(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
