package bc.org.bouncycastle.math.ec.custom.gm;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\gm\SM2P256V1FieldElement.smali */
public class SM2P256V1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF"));
    protected int[] a;

    public SM2P256V1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SM2P256V1FieldElement");
        }
        this.a = SM2P256V1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SM2P256V1Field.add(this.a, ((SM2P256V1FieldElement) eCFieldElement).a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = w5.a();
        SM2P256V1Field.addOne(this.a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SM2P256V1Field.inv(((SM2P256V1FieldElement) eCFieldElement).a, a);
        SM2P256V1Field.multiply(a, this.a, a);
        return new SM2P256V1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SM2P256V1FieldElement) {
            return w5.b(this.a, ((SM2P256V1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SM2P256V1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 8);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = w5.a();
        SM2P256V1Field.inv(this.a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return w5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return w5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SM2P256V1Field.multiply(this.a, ((SM2P256V1FieldElement) eCFieldElement).a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = w5.a();
        SM2P256V1Field.negate(this.a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (w5.b(iArr) || w5.a(iArr)) {
            return this;
        }
        int[] a = w5.a();
        SM2P256V1Field.square(iArr, a);
        SM2P256V1Field.multiply(a, iArr, a);
        int[] a2 = w5.a();
        SM2P256V1Field.squareN(a, 2, a2);
        SM2P256V1Field.multiply(a2, a, a2);
        int[] a3 = w5.a();
        SM2P256V1Field.squareN(a2, 2, a3);
        SM2P256V1Field.multiply(a3, a, a3);
        SM2P256V1Field.squareN(a3, 6, a);
        SM2P256V1Field.multiply(a, a3, a);
        int[] a4 = w5.a();
        SM2P256V1Field.squareN(a, 12, a4);
        SM2P256V1Field.multiply(a4, a, a4);
        SM2P256V1Field.squareN(a4, 6, a);
        SM2P256V1Field.multiply(a, a3, a);
        SM2P256V1Field.square(a, a3);
        SM2P256V1Field.multiply(a3, iArr, a3);
        SM2P256V1Field.squareN(a3, 31, a4);
        SM2P256V1Field.multiply(a4, a3, a);
        SM2P256V1Field.squareN(a4, 32, a4);
        SM2P256V1Field.multiply(a4, a, a4);
        SM2P256V1Field.squareN(a4, 62, a4);
        SM2P256V1Field.multiply(a4, a, a4);
        SM2P256V1Field.squareN(a4, 4, a4);
        SM2P256V1Field.multiply(a4, a2, a4);
        SM2P256V1Field.squareN(a4, 32, a4);
        SM2P256V1Field.multiply(a4, iArr, a4);
        SM2P256V1Field.squareN(a4, 62, a4);
        SM2P256V1Field.square(a4, a2);
        if (w5.b(iArr, a2)) {
            return new SM2P256V1FieldElement(a4);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = w5.a();
        SM2P256V1Field.square(this.a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SM2P256V1Field.subtract(this.a, ((SM2P256V1FieldElement) eCFieldElement).a, a);
        return new SM2P256V1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return w5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return w5.c(this.a);
    }

    public SM2P256V1FieldElement() {
        this.a = w5.a();
    }

    protected SM2P256V1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
