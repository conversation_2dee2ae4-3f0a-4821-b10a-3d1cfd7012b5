package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k4.smali */
public class k4 extends AsymmetricKeyParameter {
    private m4 b;

    protected k4(boolean z, m4 m4Var) {
        super(z);
        this.b = m4Var;
    }

    public m4 a() {
        return this.b;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof k4)) {
            return false;
        }
        k4 k4Var = (k4) obj;
        m4 m4Var = this.b;
        return m4Var == null ? k4Var.a() == null : m4Var.equals(k4Var.a());
    }

    public int hashCode() {
        m4 m4Var = this.b;
        if (m4Var != null) {
            return m4Var.hashCode();
        }
        return 0;
    }
}
