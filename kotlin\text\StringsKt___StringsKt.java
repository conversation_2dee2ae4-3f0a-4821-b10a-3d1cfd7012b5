package kotlin.text;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Set;
import kotlin.Deprecated;
import kotlin.DeprecatedSinceKotlin;
import kotlin.Metadata;
import kotlin.Pair;
import kotlin.ReplaceWith;
import kotlin.TuplesKt;
import kotlin.UInt;
import kotlin.ULong;
import kotlin.Unit;
import kotlin.collections.CollectionsKt;
import kotlin.collections.Grouping;
import kotlin.collections.IndexedValue;
import kotlin.collections.IndexingIterable;
import kotlin.collections.MapsKt;
import kotlin.collections.SetsKt;
import kotlin.collections.SlidingWindowKt;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.functions.Function3;
import kotlin.jvm.internal.Intrinsics;
import kotlin.random.Random;
import kotlin.ranges.IntProgression;
import kotlin.ranges.IntRange;
import kotlin.ranges.RangesKt;
import kotlin.sequences.Sequence;
import kotlin.sequences.SequencesKt;

/* JADX INFO: Access modifiers changed from: package-private */
/* compiled from: _Strings.kt */
@Metadata(d1 = {"\u0000ö\u0001\n\u0000\n\u0002\u0010\u000b\n\u0002\u0010\r\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\f\n\u0002\b\u0002\n\u0002\u0010\u001c\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010%\n\u0002\b\b\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0010\u001f\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010!\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0010\u000f\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a$\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\n\u0010\u0006\u001a\u00020\u0001*\u00020\u0002\u001a$\u0010\u0006\u001a\u00020\u0001*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\u0010\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\b*\u00020\u0002\u001a\u0010\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00050\n*\u00020\u0002\u001aH\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u0002H\u000e0\f\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e*\u00020\u00022\u001e\u0010\u000f\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u0002H\u000e0\u00100\u0004H\u0086\bø\u0001\u0000\u001a6\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u00020\u00050\f\"\u0004\b\u0000\u0010\r*\u00020\u00022\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u0004H\u0086\bø\u0001\u0000\u001aP\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u0002H\u000e0\f\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e*\u00020\u00022\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u00042\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0086\bø\u0001\u0000\u001aQ\u0010\u0014\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\r\"\u0018\b\u0001\u0010\u0015*\u0012\u0012\u0006\b\u0000\u0012\u0002H\r\u0012\u0006\b\u0000\u0012\u00020\u00050\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001ak\u0010\u0014\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e\"\u0018\b\u0002\u0010\u0015*\u0012\u0012\u0006\b\u0000\u0012\u0002H\r\u0012\u0006\b\u0000\u0012\u0002H\u000e0\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u00042\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0019\u001ac\u0010\u001a\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e\"\u0018\b\u0002\u0010\u0015*\u0012\u0012\u0006\b\u0000\u0012\u0002H\r\u0012\u0006\b\u0000\u0012\u0002H\u000e0\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u001e\u0010\u000f\u001a\u001a\u0012\u0004\u0012\u00020\u0005\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u0002H\r\u0012\u0004\u0012\u0002H\u000e0\u00100\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001a6\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\f\"\u0004\b\u0000\u0010\u000e*\u00020\u00022\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0087\bø\u0001\u0000\u001aQ\u0010\u001d\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\u000e\"\u0018\b\u0001\u0010\u0015*\u0012\u0012\u0006\b\u0000\u0012\u00020\u0005\u0012\u0006\b\u0000\u0012\u0002H\u000e0\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001a\u001a\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020 0\u001f*\u00020\u00022\u0006\u0010!\u001a\u00020\"H\u0007\u001a4\u0010\u001e\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u0002H#0\u0004H\u0007\u001a\u001a\u0010$\u001a\b\u0012\u0004\u0012\u00020 0\n*\u00020\u00022\u0006\u0010!\u001a\u00020\"H\u0007\u001a4\u0010$\u001a\b\u0012\u0004\u0012\u0002H#0\n\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u0002H#0\u0004H\u0007\u001a\r\u0010%\u001a\u00020\"*\u00020\u0002H\u0087\b\u001a$\u0010%\u001a\u00020\"*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\u0012\u0010&\u001a\u00020\u0002*\u00020\u00022\u0006\u0010'\u001a\u00020\"\u001a\u0012\u0010&\u001a\u00020 *\u00020 2\u0006\u0010'\u001a\u00020\"\u001a\u0012\u0010(\u001a\u00020\u0002*\u00020\u00022\u0006\u0010'\u001a\u00020\"\u001a\u0012\u0010(\u001a\u00020 *\u00020 2\u0006\u0010'\u001a\u00020\"\u001a$\u0010)\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u0010)\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u0010*\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u0010*\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a,\u0010+\u001a\u00020\u0005*\u00020\u00022\u0006\u0010,\u001a\u00020\"2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00050\u0004H\u0087\bø\u0001\u0000\u001a\u001c\u0010.\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0006\u0010,\u001a\u00020\"H\u0087\b¢\u0006\u0002\u0010/\u001a$\u00100\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u00100\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a9\u00101\u001a\u00020\u0002*\u00020\u00022'\u0010\u0003\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000102H\u0086\bø\u0001\u0000\u001a9\u00101\u001a\u00020 *\u00020 2'\u0010\u0003\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000102H\u0086\bø\u0001\u0000\u001aT\u00105\u001a\u0002H6\"\f\b\u0000\u00106*\u000607j\u0002`8*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62'\u0010\u0003\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000102H\u0086\bø\u0001\u0000¢\u0006\u0002\u00109\u001a$\u0010:\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u0010:\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a?\u0010;\u001a\u0002H6\"\f\b\u0000\u00106*\u000607j\u0002`8*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010<\u001a?\u0010=\u001a\u0002H6\"\f\b\u0000\u00106*\u000607j\u0002`8*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010<\u001a+\u0010>\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a+\u0010@\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a\n\u0010A\u001a\u00020\u0005*\u00020\u0002\u001a$\u0010A\u001a\u00020\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a5\u0010B\u001a\u0002H#\"\b\b\u0000\u0010#*\u00020C*\u00020\u00022\u0014\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010D\u001a7\u0010E\u001a\u0004\u0018\u0001H#\"\b\b\u0000\u0010#*\u00020C*\u00020\u00022\u0014\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010D\u001a\u0011\u0010F\u001a\u0004\u0018\u00010\u0005*\u00020\u0002¢\u0006\u0002\u0010G\u001a+\u0010F\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a6\u0010H\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0018\u0010\u000f\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u0002H#0\b0\u0004H\u0086\bø\u0001\u0000\u001aP\u0010I\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022-\u0010\u000f\u001a)\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u0002H#0\b02H\u0087\bø\u0001\u0000¢\u0006\u0002\bJ\u001af\u0010K\u001a\u0002H6\"\u0004\b\u0000\u0010#\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62-\u0010\u000f\u001a)\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u0002H#0\b02H\u0087\bø\u0001\u0000¢\u0006\u0004\bM\u0010N\u001aO\u0010O\u001a\u0002H6\"\u0004\b\u0000\u0010#\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62\u0018\u0010\u000f\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\n\u0012\b\u0012\u0004\u0012\u0002H#0\b0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010P\u001aL\u0010Q\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2'\u0010S\u001a#\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#02H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aa\u0010V\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0WH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010X\u001aL\u0010Y\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2'\u0010S\u001a#\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u0002H#02H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010U\u001aa\u0010Z\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u0002H#0WH\u0086\bø\u0001\u0000¢\u0006\u0002\u0010X\u001a$\u0010[\u001a\u00020\\*\u00020\u00022\u0012\u0010]\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\\0\u0004H\u0086\bø\u0001\u0000\u001a9\u0010^\u001a\u00020\\*\u00020\u00022'\u0010]\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\\02H\u0086\bø\u0001\u0000\u001a,\u0010_\u001a\u00020\u0005*\u00020\u00022\u0006\u0010,\u001a\u00020\"2\u0012\u0010-\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00050\u0004H\u0087\bø\u0001\u0000\u001a\u0019\u0010`\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0006\u0010,\u001a\u00020\"¢\u0006\u0002\u0010/\u001a<\u0010a\u001a\u0014\u0012\u0004\u0012\u0002H\r\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u001f0\f\"\u0004\b\u0000\u0010\r*\u00020\u00022\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u0004H\u0086\bø\u0001\u0000\u001aV\u0010a\u001a\u0014\u0012\u0004\u0012\u0002H\r\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u000e0\u001f0\f\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e*\u00020\u00022\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u00042\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0086\bø\u0001\u0000\u001aU\u0010b\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\r\"\u001c\b\u0001\u0010\u0015*\u0016\u0012\u0006\b\u0000\u0012\u0002H\r\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050c0\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0018\u001ao\u0010b\u001a\u0002H\u0015\"\u0004\b\u0000\u0010\r\"\u0004\b\u0001\u0010\u000e\"\u001c\b\u0002\u0010\u0015*\u0016\u0012\u0006\b\u0000\u0012\u0002H\r\u0012\n\u0012\b\u0012\u0004\u0012\u0002H\u000e0c0\u0016*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H\u00152\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u00042\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\u000e0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010\u0019\u001a8\u0010d\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0e\"\u0004\b\u0000\u0010\r*\u00020\u00022\u0014\b\u0004\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H\r0\u0004H\u0087\bø\u0001\u0000\u001a$\u0010f\u001a\u00020\"*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a$\u0010g\u001a\u00020\"*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\n\u0010h\u001a\u00020\u0005*\u00020\u0002\u001a$\u0010h\u001a\u00020\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\u0011\u0010i\u001a\u0004\u0018\u00010\u0005*\u00020\u0002¢\u0006\u0002\u0010G\u001a+\u0010i\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a0\u0010j\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0086\bø\u0001\u0000\u001aE\u0010k\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022'\u0010\u000f\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#02H\u0086\bø\u0001\u0000\u001aK\u0010l\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\b\b\u0000\u0010#*\u00020C*\u00020\u00022)\u0010\u000f\u001a%\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#02H\u0086\bø\u0001\u0000\u001ad\u0010m\u001a\u0002H6\"\b\b\u0000\u0010#*\u00020C\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62)\u0010\u000f\u001a%\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#02H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010N\u001a^\u0010n\u001a\u0002H6\"\u0004\b\u0000\u0010#\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62'\u0010\u000f\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#02H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010N\u001a6\u0010o\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\b\b\u0000\u0010#*\u00020C*\u00020\u00022\u0014\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#0\u0004H\u0086\bø\u0001\u0000\u001aO\u0010p\u001a\u0002H6\"\b\b\u0000\u0010#*\u00020C\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62\u0014\u0010\u000f\u001a\u0010\u0012\u0004\u0012\u00020\u0005\u0012\u0006\u0012\u0004\u0018\u0001H#0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010P\u001aI\u0010q\u001a\u0002H6\"\u0004\b\u0000\u0010#\"\u0010\b\u0001\u00106*\n\u0012\u0006\b\u0000\u0012\u0002H#0L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H62\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010P\u001a\u0011\u0010r\u001a\u00020\u0005*\u00020\u0002H\u0007¢\u0006\u0002\bs\u001a9\u0010t\u001a\u00020\u0005\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\bw\u001a;\u0010x\u001a\u0004\u0018\u00010\u0005\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a9\u0010y\u001a\u0002H#\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010z\u001a$\u0010y\u001a\u00020{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000\u001a$\u0010y\u001a\u00020|*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020|0\u0004H\u0087\bø\u0001\u0000\u001a;\u0010}\u001a\u0004\u0018\u0001H#\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010z\u001a+\u0010}\u001a\u0004\u0018\u00010{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010~\u001a+\u0010}\u001a\u0004\u0018\u00010|*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020|0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010\u007f\u001aP\u0010\u0080\u0001\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H#0\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H#`\u0083\u00012\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u0084\u0001\u001aR\u0010\u0085\u0001\u001a\u0004\u0018\u0001H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H#0\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H#`\u0083\u00012\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u0084\u0001\u001a\u0014\u0010\u0086\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u0002H\u0007¢\u0006\u0002\u0010G\u001a2\u0010\u0087\u0001\u001a\u00020\u0005*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u00020\u00050\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u00020\u0005`\u0083\u0001H\u0007¢\u0006\u0003\b\u0088\u0001\u001a4\u0010\u0089\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u00020\u00050\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u00020\u0005`\u0083\u0001H\u0007¢\u0006\u0003\u0010\u008a\u0001\u001a\u0013\u0010\u008b\u0001\u001a\u00020\u0005*\u00020\u0002H\u0007¢\u0006\u0003\b\u008c\u0001\u001a;\u0010\u008d\u0001\u001a\u00020\u0005\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\b\u008e\u0001\u001a<\u0010\u008f\u0001\u001a\u0004\u0018\u00010\u0005\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a:\u0010\u0090\u0001\u001a\u0002H#\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010z\u001a%\u0010\u0090\u0001\u001a\u00020{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000\u001a%\u0010\u0090\u0001\u001a\u00020|*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020|0\u0004H\u0087\bø\u0001\u0000\u001a<\u0010\u0091\u0001\u001a\u0004\u0018\u0001H#\"\u000e\b\u0000\u0010#*\b\u0012\u0004\u0012\u0002H#0u*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010z\u001a,\u0010\u0091\u0001\u001a\u0004\u0018\u00010{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010~\u001a,\u0010\u0091\u0001\u001a\u0004\u0018\u00010|*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020|0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0002\u0010\u007f\u001aP\u0010\u0092\u0001\u001a\u0002H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H#0\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H#`\u0083\u00012\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u0084\u0001\u001aR\u0010\u0093\u0001\u001a\u0004\u0018\u0001H#\"\u0004\b\u0000\u0010#*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u0002H#0\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u0002H#`\u0083\u00012\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u0084\u0001\u001a\u0014\u0010\u0094\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u0002H\u0007¢\u0006\u0002\u0010G\u001a2\u0010\u0095\u0001\u001a\u00020\u0005*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u00020\u00050\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u00020\u0005`\u0083\u0001H\u0007¢\u0006\u0003\b\u0096\u0001\u001a4\u0010\u0097\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u001d\u0010\u0081\u0001\u001a\u0018\u0012\u0006\b\u0000\u0012\u00020\u00050\u0082\u0001j\u000b\u0012\u0006\b\u0000\u0012\u00020\u0005`\u0083\u0001H\u0007¢\u0006\u0003\u0010\u008a\u0001\u001a\u000b\u0010\u0098\u0001\u001a\u00020\u0001*\u00020\u0002\u001a%\u0010\u0098\u0001\u001a\u00020\u0001*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a8\u0010\u0099\u0001\u001a\u0003H\u009a\u0001\"\t\b\u0000\u0010\u009a\u0001*\u00020\u0002*\u0003H\u009a\u00012\u0012\u0010]\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\\0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009b\u0001\u001aM\u0010\u009c\u0001\u001a\u0003H\u009a\u0001\"\t\b\u0000\u0010\u009a\u0001*\u00020\u0002*\u0003H\u009a\u00012'\u0010]\u001a#\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\\02H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010\u009d\u0001\u001a1\u0010\u009e\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u0010*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a1\u0010\u009e\u0001\u001a\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020 0\u0010*\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\u000e\u0010\u009f\u0001\u001a\u00020\u0005*\u00020\u0002H\u0087\b\u001a\u0017\u0010\u009f\u0001\u001a\u00020\u0005*\u00020\u00022\b\u0010\u009f\u0001\u001a\u00030 \u0001H\u0007\u001a\u0015\u0010¡\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u0002H\u0087\b¢\u0006\u0002\u0010G\u001a\u001f\u0010¡\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\b\u0010\u009f\u0001\u001a\u00030 \u0001H\u0007¢\u0006\u0003\u0010¢\u0001\u001a:\u0010£\u0001\u001a\u00020\u0005*\u00020\u00022'\u0010S\u001a#\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000502H\u0086\bø\u0001\u0000\u001aO\u0010¤\u0001\u001a\u00020\u0005*\u00020\u00022<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050WH\u0086\bø\u0001\u0000\u001aW\u0010¥\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050WH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¦\u0001\u001aB\u0010§\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022'\u0010S\u001a#\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000502H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¨\u0001\u001a:\u0010©\u0001\u001a\u00020\u0005*\u00020\u00022'\u0010S\u001a#\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u000502H\u0086\bø\u0001\u0000\u001aO\u0010ª\u0001\u001a\u00020\u0005*\u00020\u00022<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u00050WH\u0086\bø\u0001\u0000\u001aW\u0010«\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u00050WH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¦\u0001\u001aB\u0010¬\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022'\u0010S\u001a#\u0012\u0004\u0012\u00020\u0005\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u000502H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¨\u0001\u001a\u000b\u0010\u00ad\u0001\u001a\u00020\u0002*\u00020\u0002\u001a\u000e\u0010\u00ad\u0001\u001a\u00020 *\u00020 H\u0087\b\u001aT\u0010®\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2'\u0010S\u001a#\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#02H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¯\u0001\u001ai\u0010°\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0WH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010±\u0001\u001a@\u0010²\u0001\u001a\b\u0012\u0004\u0012\u00020\u00050\u001f*\u00020\u00022'\u0010S\u001a#\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u000502H\u0087\bø\u0001\u0000\u001aU\u0010³\u0001\u001a\b\u0012\u0004\u0012\u00020\u00050\u001f*\u00020\u00022<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u00110\u0005¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050WH\u0087\bø\u0001\u0000\u001aT\u0010´\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2'\u0010S\u001a#\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#02H\u0087\bø\u0001\u0000¢\u0006\u0003\u0010¯\u0001\u001ai\u0010µ\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010R\u001a\u0002H#2<\u0010S\u001a8\u0012\u0013\u0012\u00110\"¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(,\u0012\u0013\u0012\u0011H#¢\u0006\f\b3\u0012\b\b4\u0012\u0004\b\b(T\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u0002H#0WH\u0087\bø\u0001\u0000¢\u0006\u0003\u0010±\u0001\u001a\u000b\u0010¶\u0001\u001a\u00020\u0005*\u00020\u0002\u001a%\u0010¶\u0001\u001a\u00020\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a\u0012\u0010·\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u0002¢\u0006\u0002\u0010G\u001a,\u0010·\u0001\u001a\u0004\u0018\u00010\u0005*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000¢\u0006\u0002\u0010?\u001a\u001a\u0010¸\u0001\u001a\u00020\u0002*\u00020\u00022\r\u0010¹\u0001\u001a\b\u0012\u0004\u0012\u00020\"0\b\u001a\u0015\u0010¸\u0001\u001a\u00020\u0002*\u00020\u00022\b\u0010¹\u0001\u001a\u00030º\u0001\u001a\u001d\u0010¸\u0001\u001a\u00020 *\u00020 2\r\u0010¹\u0001\u001a\b\u0012\u0004\u0012\u00020\"0\bH\u0087\b\u001a\u0015\u0010¸\u0001\u001a\u00020 *\u00020 2\b\u0010¹\u0001\u001a\u00030º\u0001\u001a%\u0010»\u0001\u001a\u00020\"*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\"0\u0004H\u0087\bø\u0001\u0000\u001a%\u0010¼\u0001\u001a\u00020{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000\u001a+\u0010½\u0001\u001a\u00020{*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020{0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\b¾\u0001\u001a+\u0010½\u0001\u001a\u00020\"*\u00020\u00022\u0012\u0010v\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\"0\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\b¿\u0001\u001a-\u0010½\u0001\u001a\u00030À\u0001*\u00020\u00022\u0013\u0010v\u001a\u000f\u0012\u0004\u0012\u00020\u0005\u0012\u0005\u0012\u00030À\u00010\u0004H\u0087\bø\u0001\u0000¢\u0006\u0003\bÁ\u0001\u001a3\u0010½\u0001\u001a\u00030Â\u0001*\u00020\u00022\u0013\u0010v\u001a\u000f\u0012\u0004\u0012\u00020\u0005\u0012\u0005\u0012\u00030Â\u00010\u0004H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\bÃ\u0001\u0010Ä\u0001\u001a3\u0010½\u0001\u001a\u00030Å\u0001*\u00020\u00022\u0013\u0010v\u001a\u000f\u0012\u0004\u0012\u00020\u0005\u0012\u0005\u0012\u00030Å\u00010\u0004H\u0087\bø\u0001\u0000ø\u0001\u0001¢\u0006\u0006\bÆ\u0001\u0010Ç\u0001\u001a\u0013\u0010È\u0001\u001a\u00020\u0002*\u00020\u00022\u0006\u0010'\u001a\u00020\"\u001a\u0013\u0010È\u0001\u001a\u00020 *\u00020 2\u0006\u0010'\u001a\u00020\"\u001a\u0013\u0010É\u0001\u001a\u00020\u0002*\u00020\u00022\u0006\u0010'\u001a\u00020\"\u001a\u0013\u0010É\u0001\u001a\u00020 *\u00020 2\u0006\u0010'\u001a\u00020\"\u001a%\u0010Ê\u0001\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a%\u0010Ê\u0001\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a%\u0010Ë\u0001\u001a\u00020\u0002*\u00020\u00022\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a%\u0010Ë\u0001\u001a\u00020 *\u00020 2\u0012\u0010\u0003\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u0004H\u0086\bø\u0001\u0000\u001a+\u0010Ì\u0001\u001a\u0002H6\"\u0010\b\u0000\u00106*\n\u0012\u0006\b\u0000\u0012\u00020\u00050L*\u00020\u00022\u0006\u0010\u0017\u001a\u0002H6¢\u0006\u0003\u0010Í\u0001\u001a\u001d\u0010Î\u0001\u001a\u0014\u0012\u0004\u0012\u00020\u00050Ï\u0001j\t\u0012\u0004\u0012\u00020\u0005`Ð\u0001*\u00020\u0002\u001a\u0011\u0010Ñ\u0001\u001a\b\u0012\u0004\u0012\u00020\u00050\u001f*\u00020\u0002\u001a\u0011\u0010Ò\u0001\u001a\b\u0012\u0004\u0012\u00020\u00050c*\u00020\u0002\u001a\u0012\u0010Ó\u0001\u001a\t\u0012\u0004\u0012\u00020\u00050Ô\u0001*\u00020\u0002\u001a1\u0010Õ\u0001\u001a\b\u0012\u0004\u0012\u00020 0\u001f*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\t\b\u0002\u0010Ö\u0001\u001a\u00020\"2\t\b\u0002\u0010×\u0001\u001a\u00020\u0001H\u0007\u001aK\u0010Õ\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\t\b\u0002\u0010Ö\u0001\u001a\u00020\"2\t\b\u0002\u0010×\u0001\u001a\u00020\u00012\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u0002H#0\u0004H\u0007\u001a1\u0010Ø\u0001\u001a\b\u0012\u0004\u0012\u00020 0\n*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\t\b\u0002\u0010Ö\u0001\u001a\u00020\"2\t\b\u0002\u0010×\u0001\u001a\u00020\u0001H\u0007\u001aK\u0010Ø\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\n\"\u0004\b\u0000\u0010#*\u00020\u00022\u0006\u0010!\u001a\u00020\"2\t\b\u0002\u0010Ö\u0001\u001a\u00020\"2\t\b\u0002\u0010×\u0001\u001a\u00020\u00012\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u0002H#0\u0004H\u0007\u001a\u0018\u0010Ù\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0004\u0012\u00020\u00050Ú\u00010\b*\u00020\u0002\u001a)\u0010Û\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u00100\u001f*\u00020\u00022\u0007\u0010Ü\u0001\u001a\u00020\u0002H\u0086\u0004\u001a`\u0010Û\u0001\u001a\b\u0012\u0004\u0012\u0002H\u000e0\u001f\"\u0004\b\u0000\u0010\u000e*\u00020\u00022\u0007\u0010Ü\u0001\u001a\u00020\u000228\u0010\u000f\u001a4\u0012\u0014\u0012\u00120\u0005¢\u0006\r\b3\u0012\t\b4\u0012\u0005\b\b(Ý\u0001\u0012\u0014\u0012\u00120\u0005¢\u0006\r\b3\u0012\t\b4\u0012\u0005\b\b(Þ\u0001\u0012\u0004\u0012\u0002H\u000e02H\u0086\bø\u0001\u0000\u001a\u001f\u0010ß\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u00100\u001f*\u00020\u0002H\u0007\u001aW\u0010ß\u0001\u001a\b\u0012\u0004\u0012\u0002H#0\u001f\"\u0004\b\u0000\u0010#*\u00020\u000228\u0010\u000f\u001a4\u0012\u0014\u0012\u00120\u0005¢\u0006\r\b3\u0012\t\b4\u0012\u0005\b\b(Ý\u0001\u0012\u0014\u0012\u00120\u0005¢\u0006\r\b3\u0012\t\b4\u0012\u0005\b\b(Þ\u0001\u0012\u0004\u0012\u0002H#02H\u0087\bø\u0001\u0000\u0082\u0002\u000b\n\u0005\b\u009920\u0001\n\u0002\b\u0019¨\u0006à\u0001"}, d2 = {"all", "", "", "predicate", "Lkotlin/Function1;", "", "any", "asIterable", "", "asSequence", "Lkotlin/sequences/Sequence;", "associate", "", "K", "V", "transform", "Lkotlin/Pair;", "associateBy", "keySelector", "valueTransform", "associateByTo", "M", "", "destination", "(Ljava/lang/CharSequence;Ljava/util/Map;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "(Ljava/lang/CharSequence;Ljava/util/Map;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)Ljava/util/Map;", "associateTo", "associateWith", "valueSelector", "associateWithTo", "chunked", "", "", "size", "", "R", "chunkedSequence", "count", "drop", "n", "dropLast", "dropLastWhile", "dropWhile", "elementAtOrElse", "index", "defaultValue", "elementAtOrNull", "(Ljava/lang/CharSequence;I)Ljava/lang/Character;", "filter", "filterIndexed", "Lkotlin/Function2;", "Lkotlin/ParameterName;", "name", "filterIndexedTo", "C", "Ljava/lang/Appendable;", "Lkotlin/text/Appendable;", "(Ljava/lang/CharSequence;Ljava/lang/Appendable;Lkotlin/jvm/functions/Function2;)Ljava/lang/Appendable;", "filterNot", "filterNotTo", "(Ljava/lang/CharSequence;Ljava/lang/Appendable;Lkotlin/jvm/functions/Function1;)Ljava/lang/Appendable;", "filterTo", "find", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Character;", "findLast", "first", "firstNotNullOf", "", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "firstNotNullOfOrNull", "firstOrNull", "(Ljava/lang/CharSequence;)Ljava/lang/Character;", "flatMap", "flatMapIndexed", "flatMapIndexedIterable", "flatMapIndexedTo", "", "flatMapIndexedIterableTo", "(Ljava/lang/CharSequence;Ljava/util/Collection;Lkotlin/jvm/functions/Function2;)Ljava/util/Collection;", "flatMapTo", "(Ljava/lang/CharSequence;Ljava/util/Collection;Lkotlin/jvm/functions/Function1;)Ljava/util/Collection;", "fold", "initial", "operation", "acc", "(Ljava/lang/CharSequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;", "foldIndexed", "Lkotlin/Function3;", "(Ljava/lang/CharSequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/lang/Object;", "foldRight", "foldRightIndexed", "forEach", "", "action", "forEachIndexed", "getOrElse", "getOrNull", "groupBy", "groupByTo", "", "groupingBy", "Lkotlin/collections/Grouping;", "indexOfFirst", "indexOfLast", "last", "lastOrNull", "map", "mapIndexed", "mapIndexedNotNull", "mapIndexedNotNullTo", "mapIndexedTo", "mapNotNull", "mapNotNullTo", "mapTo", "max", "maxOrThrow", "maxBy", "", "selector", "maxByOrThrow", "maxByOrNull", "maxOf", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Comparable;", "", "", "maxOfOrNull", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Double;", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/Float;", "maxOfWith", "comparator", "Ljava/util/Comparator;", "Lkotlin/Comparator;", "(Ljava/lang/CharSequence;Ljava/util/Comparator;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "maxOfWithOrNull", "maxOrNull", "maxWith", "maxWithOrThrow", "maxWithOrNull", "(Ljava/lang/CharSequence;Ljava/util/Comparator;)Ljava/lang/Character;", "min", "minOrThrow", "minBy", "minByOrThrow", "minByOrNull", "minOf", "minOfOrNull", "minOfWith", "minOfWithOrNull", "minOrNull", "minWith", "minWithOrThrow", "minWithOrNull", "none", "onEach", "S", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)Ljava/lang/CharSequence;", "onEachIndexed", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Ljava/lang/CharSequence;", "partition", "random", "Lkotlin/random/Random;", "randomOrNull", "(Ljava/lang/CharSequence;Lkotlin/random/Random;)Ljava/lang/Character;", "reduce", "reduceIndexed", "reduceIndexedOrNull", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function3;)Ljava/lang/Character;", "reduceOrNull", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function2;)Ljava/lang/Character;", "reduceRight", "reduceRightIndexed", "reduceRightIndexedOrNull", "reduceRightOrNull", "reversed", "runningFold", "(Ljava/lang/CharSequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/util/List;", "runningFoldIndexed", "(Ljava/lang/CharSequence;Ljava/lang/Object;Lkotlin/jvm/functions/Function3;)Ljava/util/List;", "runningReduce", "runningReduceIndexed", "scan", "scanIndexed", "single", "singleOrNull", "slice", "indices", "Lkotlin/ranges/IntRange;", "sumBy", "sumByDouble", "sumOf", "sumOfDouble", "sumOfInt", "", "sumOfLong", "Lkotlin/UInt;", "sumOfUInt", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)I", "Lkotlin/ULong;", "sumOfULong", "(Ljava/lang/CharSequence;Lkotlin/jvm/functions/Function1;)J", "take", "takeLast", "takeLastWhile", "takeWhile", "toCollection", "(Ljava/lang/CharSequence;Ljava/util/Collection;)Ljava/util/Collection;", "toHashSet", "Ljava/util/HashSet;", "Lkotlin/collections/HashSet;", "toList", "toMutableList", "toSet", "", "windowed", "step", "partialWindows", "windowedSequence", "withIndex", "Lkotlin/collections/IndexedValue;", "zip", "other", "a", "b", "zipWithNext", "kotlin-stdlib"}, k = 5, mv = {1, 9, 0}, xi = 49, xs = "kotlin/text/StringsKt")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\text\StringsKt___StringsKt.smali */
public class StringsKt___StringsKt extends StringsKt___StringsJvmKt {
    private static final char elementAtOrElse(CharSequence $this$elementAtOrElse, int index, Function1<? super Integer, Character> defaultValue) {
        Intrinsics.checkNotNullParameter($this$elementAtOrElse, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        return (index < 0 || index > StringsKt.getLastIndex($this$elementAtOrElse)) ? defaultValue.invoke(Integer.valueOf(index)).charValue() : $this$elementAtOrElse.charAt(index);
    }

    private static final Character elementAtOrNull(CharSequence $this$elementAtOrNull, int index) {
        Intrinsics.checkNotNullParameter($this$elementAtOrNull, "<this>");
        return StringsKt.getOrNull($this$elementAtOrNull, index);
    }

    private static final Character find(CharSequence $this$find, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$find, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$find.length(); i++) {
            char element$iv = $this$find.charAt(i);
            if (predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                return Character.valueOf(element$iv);
            }
        }
        return null;
    }

    private static final Character findLast(CharSequence $this$findLast, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$findLast, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$findLast.length() - 1;
        if (length >= 0) {
            do {
                int index$iv = length;
                length--;
                char element$iv = $this$findLast.charAt(index$iv);
                if (predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                    return Character.valueOf(element$iv);
                }
            } while (length >= 0);
        }
        return null;
    }

    public static final char first(CharSequence $this$first) {
        Intrinsics.checkNotNullParameter($this$first, "<this>");
        if ($this$first.length() == 0) {
            throw new NoSuchElementException("Char sequence is empty.");
        }
        return $this$first.charAt(0);
    }

    public static final char first(CharSequence $this$first, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$first, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$first.length(); i++) {
            char element = $this$first.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                return element;
            }
        }
        throw new NoSuchElementException("Char sequence contains no character matching the predicate.");
    }

    private static final <R> R firstNotNullOf(CharSequence $this$firstNotNullOf, Function1<? super Character, ? extends R> transform) {
        R r;
        Intrinsics.checkNotNullParameter($this$firstNotNullOf, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int i = 0;
        while (true) {
            if (i >= $this$firstNotNullOf.length()) {
                r = null;
                break;
            }
            r = transform.invoke(Character.valueOf($this$firstNotNullOf.charAt(i)));
            if (r != null) {
                break;
            }
            i++;
        }
        if (r != null) {
            return r;
        }
        throw new NoSuchElementException("No element of the char sequence was transformed to a non-null value.");
    }

    private static final <R> R firstNotNullOfOrNull(CharSequence $this$firstNotNullOfOrNull, Function1<? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$firstNotNullOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (int i = 0; i < $this$firstNotNullOfOrNull.length(); i++) {
            char element = $this$firstNotNullOfOrNull.charAt(i);
            R invoke = transform.invoke(Character.valueOf(element));
            if (invoke != null) {
                return invoke;
            }
        }
        return null;
    }

    public static final Character firstOrNull(CharSequence $this$firstOrNull) {
        Intrinsics.checkNotNullParameter($this$firstOrNull, "<this>");
        if ($this$firstOrNull.length() == 0) {
            return null;
        }
        return Character.valueOf($this$firstOrNull.charAt(0));
    }

    public static final Character firstOrNull(CharSequence $this$firstOrNull, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$firstOrNull, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$firstOrNull.length(); i++) {
            char element = $this$firstOrNull.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                return Character.valueOf(element);
            }
        }
        return null;
    }

    private static final char getOrElse(CharSequence $this$getOrElse, int index, Function1<? super Integer, Character> defaultValue) {
        Intrinsics.checkNotNullParameter($this$getOrElse, "<this>");
        Intrinsics.checkNotNullParameter(defaultValue, "defaultValue");
        return (index < 0 || index > StringsKt.getLastIndex($this$getOrElse)) ? defaultValue.invoke(Integer.valueOf(index)).charValue() : $this$getOrElse.charAt(index);
    }

    public static final Character getOrNull(CharSequence $this$getOrNull, int index) {
        Intrinsics.checkNotNullParameter($this$getOrNull, "<this>");
        if (index < 0 || index > StringsKt.getLastIndex($this$getOrNull)) {
            return null;
        }
        return Character.valueOf($this$getOrNull.charAt(index));
    }

    public static final int indexOfFirst(CharSequence $this$indexOfFirst, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$indexOfFirst, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$indexOfFirst.length();
        for (int index = 0; index < length; index++) {
            if (predicate.invoke(Character.valueOf($this$indexOfFirst.charAt(index))).booleanValue()) {
                return index;
            }
        }
        return -1;
    }

    public static final int indexOfLast(CharSequence $this$indexOfLast, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$indexOfLast, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$indexOfLast.length() - 1;
        if (length >= 0) {
            do {
                int index = length;
                length--;
                if (predicate.invoke(Character.valueOf($this$indexOfLast.charAt(index))).booleanValue()) {
                    return index;
                }
            } while (length >= 0);
        }
        return -1;
    }

    public static final char last(CharSequence $this$last) {
        Intrinsics.checkNotNullParameter($this$last, "<this>");
        if ($this$last.length() == 0) {
            throw new NoSuchElementException("Char sequence is empty.");
        }
        return $this$last.charAt(StringsKt.getLastIndex($this$last));
    }

    public static final char last(CharSequence $this$last, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$last, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$last.length() - 1;
        if (length >= 0) {
            do {
                int index = length;
                length--;
                char element = $this$last.charAt(index);
                if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                    return element;
                }
            } while (length >= 0);
        }
        throw new NoSuchElementException("Char sequence contains no character matching the predicate.");
    }

    public static final Character lastOrNull(CharSequence $this$lastOrNull) {
        Intrinsics.checkNotNullParameter($this$lastOrNull, "<this>");
        if ($this$lastOrNull.length() == 0) {
            return null;
        }
        return Character.valueOf($this$lastOrNull.charAt($this$lastOrNull.length() - 1));
    }

    public static final Character lastOrNull(CharSequence $this$lastOrNull, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$lastOrNull, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$lastOrNull.length() - 1;
        if (length >= 0) {
            do {
                int index = length;
                length--;
                char element = $this$lastOrNull.charAt(index);
                if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                    return Character.valueOf(element);
                }
            } while (length >= 0);
            return null;
        }
        return null;
    }

    private static final char random(CharSequence $this$random) {
        Intrinsics.checkNotNullParameter($this$random, "<this>");
        return StringsKt.random($this$random, Random.INSTANCE);
    }

    public static final char random(CharSequence $this$random, Random random) {
        Intrinsics.checkNotNullParameter($this$random, "<this>");
        Intrinsics.checkNotNullParameter(random, "random");
        if ($this$random.length() == 0) {
            throw new NoSuchElementException("Char sequence is empty.");
        }
        return $this$random.charAt(random.nextInt($this$random.length()));
    }

    private static final Character randomOrNull(CharSequence $this$randomOrNull) {
        Intrinsics.checkNotNullParameter($this$randomOrNull, "<this>");
        return StringsKt.randomOrNull($this$randomOrNull, Random.INSTANCE);
    }

    public static final Character randomOrNull(CharSequence $this$randomOrNull, Random random) {
        Intrinsics.checkNotNullParameter($this$randomOrNull, "<this>");
        Intrinsics.checkNotNullParameter(random, "random");
        if ($this$randomOrNull.length() == 0) {
            return null;
        }
        return Character.valueOf($this$randomOrNull.charAt(random.nextInt($this$randomOrNull.length())));
    }

    public static final char single(CharSequence $this$single) {
        Intrinsics.checkNotNullParameter($this$single, "<this>");
        switch ($this$single.length()) {
            case 0:
                throw new NoSuchElementException("Char sequence is empty.");
            case 1:
                return $this$single.charAt(0);
            default:
                throw new IllegalArgumentException("Char sequence has more than one element.");
        }
    }

    public static final char single(CharSequence $this$single, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$single, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Character single = null;
        boolean found = false;
        for (int i = 0; i < $this$single.length(); i++) {
            char element = $this$single.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                if (found) {
                    throw new IllegalArgumentException("Char sequence contains more than one matching element.");
                }
                single = Character.valueOf(element);
                found = true;
            }
        }
        if (!found) {
            throw new NoSuchElementException("Char sequence contains no character matching the predicate.");
        }
        Intrinsics.checkNotNull(single, "null cannot be cast to non-null type kotlin.Char");
        return single.charValue();
    }

    public static final Character singleOrNull(CharSequence $this$singleOrNull) {
        Intrinsics.checkNotNullParameter($this$singleOrNull, "<this>");
        if ($this$singleOrNull.length() == 1) {
            return Character.valueOf($this$singleOrNull.charAt(0));
        }
        return null;
    }

    public static final Character singleOrNull(CharSequence $this$singleOrNull, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$singleOrNull, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Character single = null;
        boolean found = false;
        for (int i = 0; i < $this$singleOrNull.length(); i++) {
            char element = $this$singleOrNull.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                if (found) {
                    return null;
                }
                single = Character.valueOf(element);
                found = true;
            }
        }
        if (found) {
            return single;
        }
        return null;
    }

    public static final CharSequence drop(CharSequence $this$drop, int n) {
        Intrinsics.checkNotNullParameter($this$drop, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
        }
        return $this$drop.subSequence(RangesKt.coerceAtMost(n, $this$drop.length()), $this$drop.length());
    }

    public static final String drop(String $this$drop, int n) {
        Intrinsics.checkNotNullParameter($this$drop, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
        }
        String substring = $this$drop.substring(RangesKt.coerceAtMost(n, $this$drop.length()));
        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String).substring(startIndex)");
        return substring;
    }

    public static final CharSequence dropLast(CharSequence $this$dropLast, int n) {
        Intrinsics.checkNotNullParameter($this$dropLast, "<this>");
        if (n >= 0) {
            return StringsKt.take($this$dropLast, RangesKt.coerceAtLeast($this$dropLast.length() - n, 0));
        }
        throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
    }

    public static final String dropLast(String $this$dropLast, int n) {
        Intrinsics.checkNotNullParameter($this$dropLast, "<this>");
        if (n >= 0) {
            return StringsKt.take($this$dropLast, RangesKt.coerceAtLeast($this$dropLast.length() - n, 0));
        }
        throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
    }

    public static final CharSequence dropLastWhile(CharSequence $this$dropLastWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$dropLastWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int index = StringsKt.getLastIndex($this$dropLastWhile); -1 < index; index--) {
            if (!predicate.invoke(Character.valueOf($this$dropLastWhile.charAt(index))).booleanValue()) {
                return $this$dropLastWhile.subSequence(0, index + 1);
            }
        }
        return "";
    }

    public static final String dropLastWhile(String $this$dropLastWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$dropLastWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int index = StringsKt.getLastIndex($this$dropLastWhile); -1 < index; index--) {
            if (!predicate.invoke(Character.valueOf($this$dropLastWhile.charAt(index))).booleanValue()) {
                String substring = $this$dropLastWhile.substring(0, index + 1);
                Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
                return substring;
            }
        }
        return "";
    }

    public static final CharSequence dropWhile(CharSequence $this$dropWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$dropWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$dropWhile.length();
        for (int index = 0; index < length; index++) {
            if (!predicate.invoke(Character.valueOf($this$dropWhile.charAt(index))).booleanValue()) {
                return $this$dropWhile.subSequence(index, $this$dropWhile.length());
            }
        }
        return "";
    }

    public static final String dropWhile(String $this$dropWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$dropWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$dropWhile.length();
        for (int index = 0; index < length; index++) {
            if (!predicate.invoke(Character.valueOf($this$dropWhile.charAt(index))).booleanValue()) {
                String substring = $this$dropWhile.substring(index);
                Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String).substring(startIndex)");
                return substring;
            }
        }
        return "";
    }

    public static final CharSequence filter(CharSequence $this$filter, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filter, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Appendable destination$iv = new StringBuilder();
        int length = $this$filter.length();
        for (int index$iv = 0; index$iv < length; index$iv++) {
            char element$iv = $this$filter.charAt(index$iv);
            if (predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                destination$iv.append(element$iv);
            }
        }
        return (CharSequence) destination$iv;
    }

    public static final String filter(String $this$filter, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filter, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        String $this$filterTo$iv = $this$filter;
        Appendable destination$iv = new StringBuilder();
        int length = $this$filterTo$iv.length();
        for (int index$iv = 0; index$iv < length; index$iv++) {
            char element$iv = $this$filterTo$iv.charAt(index$iv);
            if (predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                destination$iv.append(element$iv);
            }
        }
        String sb = ((StringBuilder) destination$iv).toString();
        Intrinsics.checkNotNullExpressionValue(sb, "filterTo(StringBuilder(), predicate).toString()");
        return sb;
    }

    public static final CharSequence filterIndexed(CharSequence $this$filterIndexed, Function2<? super Integer, ? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterIndexed, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Appendable destination$iv = new StringBuilder();
        int index$iv$iv = 0;
        int i = 0;
        while (i < $this$filterIndexed.length()) {
            char item$iv$iv = $this$filterIndexed.charAt(i);
            int index$iv$iv2 = index$iv$iv + 1;
            if (predicate.invoke(Integer.valueOf(index$iv$iv), Character.valueOf(item$iv$iv)).booleanValue()) {
                destination$iv.append(item$iv$iv);
            }
            i++;
            index$iv$iv = index$iv$iv2;
        }
        return (CharSequence) destination$iv;
    }

    public static final String filterIndexed(String $this$filterIndexed, Function2<? super Integer, ? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterIndexed, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        String $this$filterIndexedTo$iv = $this$filterIndexed;
        Appendable destination$iv = new StringBuilder();
        int index$iv$iv = 0;
        int i = 0;
        while (i < $this$filterIndexedTo$iv.length()) {
            char item$iv$iv = $this$filterIndexedTo$iv.charAt(i);
            int index$iv$iv2 = index$iv$iv + 1;
            if (predicate.invoke(Integer.valueOf(index$iv$iv), Character.valueOf(item$iv$iv)).booleanValue()) {
                destination$iv.append(item$iv$iv);
            }
            i++;
            index$iv$iv = index$iv$iv2;
        }
        String sb = ((StringBuilder) destination$iv).toString();
        Intrinsics.checkNotNullExpressionValue(sb, "filterIndexedTo(StringBu…(), predicate).toString()");
        return sb;
    }

    public static final <C extends Appendable> C filterIndexedTo(CharSequence $this$filterIndexedTo, C destination, Function2<? super Integer, ? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterIndexedTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int index$iv = 0;
        int i = 0;
        while (i < $this$filterIndexedTo.length()) {
            char item$iv = $this$filterIndexedTo.charAt(i);
            int index$iv2 = index$iv + 1;
            if (predicate.invoke(Integer.valueOf(index$iv), Character.valueOf(item$iv)).booleanValue()) {
                destination.append(item$iv);
            }
            i++;
            index$iv = index$iv2;
        }
        return destination;
    }

    public static final CharSequence filterNot(CharSequence $this$filterNot, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterNot, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        Appendable destination$iv = new StringBuilder();
        for (int i = 0; i < $this$filterNot.length(); i++) {
            char element$iv = $this$filterNot.charAt(i);
            if (!predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                destination$iv.append(element$iv);
            }
        }
        return (CharSequence) destination$iv;
    }

    public static final String filterNot(String $this$filterNot, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterNot, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        String $this$filterNotTo$iv = $this$filterNot;
        Appendable destination$iv = new StringBuilder();
        for (int i = 0; i < $this$filterNotTo$iv.length(); i++) {
            char element$iv = $this$filterNotTo$iv.charAt(i);
            if (!predicate.invoke(Character.valueOf(element$iv)).booleanValue()) {
                destination$iv.append(element$iv);
            }
        }
        String sb = ((StringBuilder) destination$iv).toString();
        Intrinsics.checkNotNullExpressionValue(sb, "filterNotTo(StringBuilder(), predicate).toString()");
        return sb;
    }

    public static final <C extends Appendable> C filterNotTo(CharSequence $this$filterNotTo, C destination, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterNotTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$filterNotTo.length(); i++) {
            char element = $this$filterNotTo.charAt(i);
            if (!predicate.invoke(Character.valueOf(element)).booleanValue()) {
                destination.append(element);
            }
        }
        return destination;
    }

    public static final <C extends Appendable> C filterTo(CharSequence $this$filterTo, C destination, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$filterTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$filterTo.length();
        for (int index = 0; index < length; index++) {
            char element = $this$filterTo.charAt(index);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                destination.append(element);
            }
        }
        return destination;
    }

    public static final CharSequence slice(CharSequence $this$slice, IntRange indices) {
        Intrinsics.checkNotNullParameter($this$slice, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        return indices.isEmpty() ? "" : StringsKt.subSequence($this$slice, indices);
    }

    public static final String slice(String $this$slice, IntRange indices) {
        Intrinsics.checkNotNullParameter($this$slice, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        return indices.isEmpty() ? "" : StringsKt.substring($this$slice, indices);
    }

    public static final CharSequence slice(CharSequence $this$slice, Iterable<Integer> indices) {
        Intrinsics.checkNotNullParameter($this$slice, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        int size = CollectionsKt.collectionSizeOrDefault(indices, 10);
        if (size == 0) {
            return "";
        }
        StringBuilder result = new StringBuilder(size);
        Iterator<Integer> it = indices.iterator();
        while (it.hasNext()) {
            int i = it.next().intValue();
            result.append($this$slice.charAt(i));
        }
        return result;
    }

    private static final String slice(String $this$slice, Iterable<Integer> indices) {
        Intrinsics.checkNotNullParameter($this$slice, "<this>");
        Intrinsics.checkNotNullParameter(indices, "indices");
        return StringsKt.slice((CharSequence) $this$slice, indices).toString();
    }

    public static final CharSequence take(CharSequence $this$take, int n) {
        Intrinsics.checkNotNullParameter($this$take, "<this>");
        if (n >= 0) {
            return $this$take.subSequence(0, RangesKt.coerceAtMost(n, $this$take.length()));
        }
        throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
    }

    public static final String take(String $this$take, int n) {
        Intrinsics.checkNotNullParameter($this$take, "<this>");
        if (n >= 0) {
            String substring = $this$take.substring(0, RangesKt.coerceAtMost(n, $this$take.length()));
            Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
            return substring;
        }
        throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
    }

    public static final CharSequence takeLast(CharSequence $this$takeLast, int n) {
        Intrinsics.checkNotNullParameter($this$takeLast, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
        }
        int length = $this$takeLast.length();
        return $this$takeLast.subSequence(length - RangesKt.coerceAtMost(n, length), length);
    }

    public static final String takeLast(String $this$takeLast, int n) {
        Intrinsics.checkNotNullParameter($this$takeLast, "<this>");
        if (!(n >= 0)) {
            throw new IllegalArgumentException(("Requested character count " + n + " is less than zero.").toString());
        }
        int length = $this$takeLast.length();
        String substring = $this$takeLast.substring(length - RangesKt.coerceAtMost(n, length));
        Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String).substring(startIndex)");
        return substring;
    }

    public static final CharSequence takeLastWhile(CharSequence $this$takeLastWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$takeLastWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int index = StringsKt.getLastIndex($this$takeLastWhile); -1 < index; index--) {
            if (!predicate.invoke(Character.valueOf($this$takeLastWhile.charAt(index))).booleanValue()) {
                return $this$takeLastWhile.subSequence(index + 1, $this$takeLastWhile.length());
            }
        }
        return $this$takeLastWhile.subSequence(0, $this$takeLastWhile.length());
    }

    public static final String takeLastWhile(String $this$takeLastWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$takeLastWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int index = StringsKt.getLastIndex($this$takeLastWhile); -1 < index; index--) {
            if (!predicate.invoke(Character.valueOf($this$takeLastWhile.charAt(index))).booleanValue()) {
                String substring = $this$takeLastWhile.substring(index + 1);
                Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String).substring(startIndex)");
                return substring;
            }
        }
        return $this$takeLastWhile;
    }

    public static final CharSequence takeWhile(CharSequence $this$takeWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$takeWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$takeWhile.length();
        for (int index = 0; index < length; index++) {
            if (!predicate.invoke(Character.valueOf($this$takeWhile.charAt(index))).booleanValue()) {
                return $this$takeWhile.subSequence(0, index);
            }
        }
        int index2 = $this$takeWhile.length();
        return $this$takeWhile.subSequence(0, index2);
    }

    public static final String takeWhile(String $this$takeWhile, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$takeWhile, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int length = $this$takeWhile.length();
        for (int index = 0; index < length; index++) {
            if (!predicate.invoke(Character.valueOf($this$takeWhile.charAt(index))).booleanValue()) {
                String substring = $this$takeWhile.substring(0, index);
                Intrinsics.checkNotNullExpressionValue(substring, "this as java.lang.String…ing(startIndex, endIndex)");
                return substring;
            }
        }
        return $this$takeWhile;
    }

    public static final CharSequence reversed(CharSequence $this$reversed) {
        Intrinsics.checkNotNullParameter($this$reversed, "<this>");
        StringBuilder reverse = new StringBuilder($this$reversed).reverse();
        Intrinsics.checkNotNullExpressionValue(reverse, "StringBuilder(this).reverse()");
        return reverse;
    }

    private static final String reversed(String $this$reversed) {
        Intrinsics.checkNotNullParameter($this$reversed, "<this>");
        return StringsKt.reversed((CharSequence) $this$reversed).toString();
    }

    public static final <K, V> Map<K, V> associate(CharSequence $this$associate, Function1<? super Character, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter($this$associate, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity($this$associate.length()), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (int i = 0; i < $this$associate.length(); i++) {
            char element$iv = $this$associate.charAt(i);
            Pair<? extends K, ? extends V> invoke = transform.invoke(Character.valueOf(element$iv));
            destination$iv.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination$iv;
    }

    public static final <K> Map<K, Character> associateBy(CharSequence $this$associateBy, Function1<? super Character, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter($this$associateBy, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity($this$associateBy.length()), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (int i = 0; i < $this$associateBy.length(); i++) {
            char element$iv = $this$associateBy.charAt(i);
            destination$iv.put(keySelector.invoke(Character.valueOf(element$iv)), Character.valueOf(element$iv));
        }
        return destination$iv;
    }

    public static final <K, V> Map<K, V> associateBy(CharSequence $this$associateBy, Function1<? super Character, ? extends K> keySelector, Function1<? super Character, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter($this$associateBy, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        int capacity = RangesKt.coerceAtLeast(MapsKt.mapCapacity($this$associateBy.length()), 16);
        Map destination$iv = new LinkedHashMap(capacity);
        for (int i = 0; i < $this$associateBy.length(); i++) {
            char element$iv = $this$associateBy.charAt(i);
            destination$iv.put(keySelector.invoke(Character.valueOf(element$iv)), valueTransform.invoke(Character.valueOf(element$iv)));
        }
        return destination$iv;
    }

    public static final <K, M extends Map<? super K, ? super Character>> M associateByTo(CharSequence $this$associateByTo, M destination, Function1<? super Character, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter($this$associateByTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (int i = 0; i < $this$associateByTo.length(); i++) {
            char element = $this$associateByTo.charAt(i);
            destination.put(keySelector.invoke(Character.valueOf(element)), Character.valueOf(element));
        }
        return destination;
    }

    public static final <K, V, M extends Map<? super K, ? super V>> M associateByTo(CharSequence $this$associateByTo, M destination, Function1<? super Character, ? extends K> keySelector, Function1<? super Character, ? extends V> valueTransform) {
        Intrinsics.checkNotNullParameter($this$associateByTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (int i = 0; i < $this$associateByTo.length(); i++) {
            char element = $this$associateByTo.charAt(i);
            destination.put(keySelector.invoke(Character.valueOf(element)), valueTransform.invoke(Character.valueOf(element)));
        }
        return destination;
    }

    public static final <K, V, M extends Map<? super K, ? super V>> M associateTo(CharSequence $this$associateTo, M destination, Function1<? super Character, ? extends Pair<? extends K, ? extends V>> transform) {
        Intrinsics.checkNotNullParameter($this$associateTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (int i = 0; i < $this$associateTo.length(); i++) {
            char element = $this$associateTo.charAt(i);
            Pair<? extends K, ? extends V> invoke = transform.invoke(Character.valueOf(element));
            destination.put(invoke.getFirst(), invoke.getSecond());
        }
        return destination;
    }

    public static final <V> Map<Character, V> associateWith(CharSequence $this$associateWith, Function1<? super Character, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter($this$associateWith, "<this>");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        LinkedHashMap result = new LinkedHashMap(RangesKt.coerceAtLeast(MapsKt.mapCapacity(RangesKt.coerceAtMost($this$associateWith.length(), 128)), 16));
        for (int i = 0; i < $this$associateWith.length(); i++) {
            char element$iv = $this$associateWith.charAt(i);
            result.put(Character.valueOf(element$iv), valueSelector.invoke(Character.valueOf(element$iv)));
        }
        return result;
    }

    public static final <V, M extends Map<? super Character, ? super V>> M associateWithTo(CharSequence $this$associateWithTo, M destination, Function1<? super Character, ? extends V> valueSelector) {
        Intrinsics.checkNotNullParameter($this$associateWithTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(valueSelector, "valueSelector");
        for (int i = 0; i < $this$associateWithTo.length(); i++) {
            char element = $this$associateWithTo.charAt(i);
            destination.put(Character.valueOf(element), valueSelector.invoke(Character.valueOf(element)));
        }
        return destination;
    }

    public static final <C extends Collection<? super Character>> C toCollection(CharSequence $this$toCollection, C destination) {
        Intrinsics.checkNotNullParameter($this$toCollection, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        for (int i = 0; i < $this$toCollection.length(); i++) {
            char item = $this$toCollection.charAt(i);
            destination.add(Character.valueOf(item));
        }
        return destination;
    }

    public static final HashSet<Character> toHashSet(CharSequence $this$toHashSet) {
        Intrinsics.checkNotNullParameter($this$toHashSet, "<this>");
        return (HashSet) StringsKt.toCollection($this$toHashSet, new HashSet(MapsKt.mapCapacity(RangesKt.coerceAtMost($this$toHashSet.length(), 128))));
    }

    public static final List<Character> toList(CharSequence $this$toList) {
        Intrinsics.checkNotNullParameter($this$toList, "<this>");
        switch ($this$toList.length()) {
            case 0:
                return CollectionsKt.emptyList();
            case 1:
                return CollectionsKt.listOf(Character.valueOf($this$toList.charAt(0)));
            default:
                return StringsKt.toMutableList($this$toList);
        }
    }

    public static final List<Character> toMutableList(CharSequence $this$toMutableList) {
        Intrinsics.checkNotNullParameter($this$toMutableList, "<this>");
        return (List) StringsKt.toCollection($this$toMutableList, new ArrayList($this$toMutableList.length()));
    }

    public static final Set<Character> toSet(CharSequence $this$toSet) {
        Intrinsics.checkNotNullParameter($this$toSet, "<this>");
        switch ($this$toSet.length()) {
            case 0:
                return SetsKt.emptySet();
            case 1:
                return SetsKt.setOf(Character.valueOf($this$toSet.charAt(0)));
            default:
                return (Set) StringsKt.toCollection($this$toSet, new LinkedHashSet(MapsKt.mapCapacity(RangesKt.coerceAtMost($this$toSet.length(), 128))));
        }
    }

    public static final <R> List<R> flatMap(CharSequence $this$flatMap, Function1<? super Character, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter($this$flatMap, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        for (int i = 0; i < $this$flatMap.length(); i++) {
            char element$iv = $this$flatMap.charAt(i);
            Iterable list$iv = transform.invoke(Character.valueOf(element$iv));
            CollectionsKt.addAll(destination$iv, list$iv);
        }
        return (List) destination$iv;
    }

    private static final <R> List<R> flatMapIndexedIterable(CharSequence $this$flatMapIndexed, Function2<? super Integer, ? super Character, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter($this$flatMapIndexed, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        ArrayList arrayList = new ArrayList();
        int i = 0;
        int i2 = 0;
        while (i < $this$flatMapIndexed.length()) {
            CollectionsKt.addAll(arrayList, transform.invoke(Integer.valueOf(i2), Character.valueOf($this$flatMapIndexed.charAt(i))));
            i++;
            i2++;
        }
        return arrayList;
    }

    private static final <R, C extends Collection<? super R>> C flatMapIndexedIterableTo(CharSequence $this$flatMapIndexedTo, C destination, Function2<? super Integer, ? super Character, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter($this$flatMapIndexedTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        int i = 0;
        while (i < $this$flatMapIndexedTo.length()) {
            char element = $this$flatMapIndexedTo.charAt(i);
            int index2 = index + 1;
            Iterable list = transform.invoke(Integer.valueOf(index), Character.valueOf(element));
            CollectionsKt.addAll(destination, list);
            i++;
            index = index2;
        }
        return destination;
    }

    public static final <R, C extends Collection<? super R>> C flatMapTo(CharSequence $this$flatMapTo, C destination, Function1<? super Character, ? extends Iterable<? extends R>> transform) {
        Intrinsics.checkNotNullParameter($this$flatMapTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (int i = 0; i < $this$flatMapTo.length(); i++) {
            char element = $this$flatMapTo.charAt(i);
            Iterable list = transform.invoke(Character.valueOf(element));
            CollectionsKt.addAll(destination, list);
        }
        return destination;
    }

    public static final <K> Map<K, List<Character>> groupBy(CharSequence $this$groupBy, Function1<? super Character, ? extends K> keySelector) {
        Object answer$iv$iv;
        Intrinsics.checkNotNullParameter($this$groupBy, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Map destination$iv = new LinkedHashMap();
        for (int i = 0; i < $this$groupBy.length(); i++) {
            char element$iv = $this$groupBy.charAt(i);
            K invoke = keySelector.invoke(Character.valueOf(element$iv));
            Object value$iv$iv = destination$iv.get(invoke);
            if (value$iv$iv == null) {
                answer$iv$iv = new ArrayList();
                destination$iv.put(invoke, answer$iv$iv);
            } else {
                answer$iv$iv = value$iv$iv;
            }
            List list$iv = (List) answer$iv$iv;
            list$iv.add(Character.valueOf(element$iv));
        }
        return destination$iv;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r11v1, types: [java.util.List] */
    /* JADX WARN: Type inference failed for: r9v0, types: [java.lang.Object] */
    public static final <K, V> Map<K, List<V>> groupBy(CharSequence $this$groupBy, Function1<? super Character, ? extends K> keySelector, Function1<? super Character, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter($this$groupBy, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        Map destination$iv = new LinkedHashMap();
        for (int i = 0; i < $this$groupBy.length(); i++) {
            char element$iv = $this$groupBy.charAt(i);
            K invoke = keySelector.invoke(Character.valueOf(element$iv));
            ?? r9 = destination$iv.get(invoke);
            if (r9 == 0) {
                v = new ArrayList();
                destination$iv.put(invoke, v);
            } else {
                v = r9;
            }
            List list$iv = (List) v;
            list$iv.add(valueTransform.invoke(Character.valueOf(element$iv)));
        }
        return destination$iv;
    }

    public static final <K, M extends Map<? super K, List<Character>>> M groupByTo(CharSequence $this$groupByTo, M destination, Function1<? super Character, ? extends K> keySelector) {
        Object answer$iv;
        Intrinsics.checkNotNullParameter($this$groupByTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        for (int i = 0; i < $this$groupByTo.length(); i++) {
            char element = $this$groupByTo.charAt(i);
            K invoke = keySelector.invoke(Character.valueOf(element));
            Object value$iv = destination.get(invoke);
            if (value$iv == null) {
                answer$iv = new ArrayList();
                destination.put(invoke, answer$iv);
            } else {
                answer$iv = value$iv;
            }
            List list = (List) answer$iv;
            list.add(Character.valueOf(element));
        }
        return destination;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r6v0, types: [java.lang.Object] */
    /* JADX WARN: Type inference failed for: r8v1, types: [java.util.List] */
    public static final <K, V, M extends Map<? super K, List<V>>> M groupByTo(CharSequence $this$groupByTo, M destination, Function1<? super Character, ? extends K> keySelector, Function1<? super Character, ? extends V> valueTransform) {
        V v;
        Intrinsics.checkNotNullParameter($this$groupByTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        Intrinsics.checkNotNullParameter(valueTransform, "valueTransform");
        for (int i = 0; i < $this$groupByTo.length(); i++) {
            char element = $this$groupByTo.charAt(i);
            K invoke = keySelector.invoke(Character.valueOf(element));
            ?? r6 = destination.get(invoke);
            if (r6 == 0) {
                v = new ArrayList();
                destination.put(invoke, v);
            } else {
                v = r6;
            }
            List list = (List) v;
            list.add(valueTransform.invoke(Character.valueOf(element)));
        }
        return destination;
    }

    public static final <K> Grouping<Character, K> groupingBy(final CharSequence $this$groupingBy, final Function1<? super Character, ? extends K> keySelector) {
        Intrinsics.checkNotNullParameter($this$groupingBy, "<this>");
        Intrinsics.checkNotNullParameter(keySelector, "keySelector");
        return new Grouping<Character, K>() { // from class: kotlin.text.StringsKt___StringsKt$groupingBy$1
            @Override // kotlin.collections.Grouping
            public /* bridge */ /* synthetic */ Object keyOf(Character ch) {
                return keyOf(ch.charValue());
            }

            @Override // kotlin.collections.Grouping
            public Iterator<Character> sourceIterator() {
                return StringsKt.iterator($this$groupingBy);
            }

            public K keyOf(char element) {
                return keySelector.invoke(Character.valueOf(element));
            }
        };
    }

    public static final <R> List<R> map(CharSequence $this$map, Function1<? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$map, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList($this$map.length());
        for (int i = 0; i < $this$map.length(); i++) {
            char item$iv = $this$map.charAt(i);
            destination$iv.add(transform.invoke(Character.valueOf(item$iv)));
        }
        return (List) destination$iv;
    }

    public static final <R> List<R> mapIndexed(CharSequence $this$mapIndexed, Function2<? super Integer, ? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapIndexed, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList($this$mapIndexed.length());
        int index$iv = 0;
        int i = 0;
        while (i < $this$mapIndexed.length()) {
            char item$iv = $this$mapIndexed.charAt(i);
            destination$iv.add(transform.invoke(Integer.valueOf(index$iv), Character.valueOf(item$iv)));
            i++;
            index$iv++;
        }
        return (List) destination$iv;
    }

    public static final <R> List<R> mapIndexedNotNull(CharSequence $this$mapIndexedNotNull, Function2<? super Integer, ? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapIndexedNotNull, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        int index$iv$iv = 0;
        int i = 0;
        while (i < $this$mapIndexedNotNull.length()) {
            char item$iv$iv = $this$mapIndexedNotNull.charAt(i);
            int index$iv$iv2 = index$iv$iv + 1;
            R invoke = transform.invoke(Integer.valueOf(index$iv$iv), Character.valueOf(item$iv$iv));
            if (invoke != null) {
                destination$iv.add(invoke);
            }
            i++;
            index$iv$iv = index$iv$iv2;
        }
        return (List) destination$iv;
    }

    public static final <R, C extends Collection<? super R>> C mapIndexedNotNullTo(CharSequence $this$mapIndexedNotNullTo, C destination, Function2<? super Integer, ? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapIndexedNotNullTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index$iv = 0;
        int i = 0;
        while (i < $this$mapIndexedNotNullTo.length()) {
            char item$iv = $this$mapIndexedNotNullTo.charAt(i);
            int index$iv2 = index$iv + 1;
            R invoke = transform.invoke(Integer.valueOf(index$iv), Character.valueOf(item$iv));
            if (invoke != null) {
                destination.add(invoke);
            }
            i++;
            index$iv = index$iv2;
        }
        return destination;
    }

    public static final <R, C extends Collection<? super R>> C mapIndexedTo(CharSequence $this$mapIndexedTo, C destination, Function2<? super Integer, ? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapIndexedTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int index = 0;
        int i = 0;
        while (i < $this$mapIndexedTo.length()) {
            char item = $this$mapIndexedTo.charAt(i);
            destination.add(transform.invoke(Integer.valueOf(index), Character.valueOf(item)));
            i++;
            index++;
        }
        return destination;
    }

    public static final <R> List<R> mapNotNull(CharSequence $this$mapNotNull, Function1<? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapNotNull, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        Collection destination$iv = new ArrayList();
        for (int i = 0; i < $this$mapNotNull.length(); i++) {
            char element$iv$iv = $this$mapNotNull.charAt(i);
            R invoke = transform.invoke(Character.valueOf(element$iv$iv));
            if (invoke != null) {
                destination$iv.add(invoke);
            }
        }
        return (List) destination$iv;
    }

    public static final <R, C extends Collection<? super R>> C mapNotNullTo(CharSequence $this$mapNotNullTo, C destination, Function1<? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapNotNullTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (int i = 0; i < $this$mapNotNullTo.length(); i++) {
            char element$iv = $this$mapNotNullTo.charAt(i);
            R invoke = transform.invoke(Character.valueOf(element$iv));
            if (invoke != null) {
                destination.add(invoke);
            }
        }
        return destination;
    }

    public static final <R, C extends Collection<? super R>> C mapTo(CharSequence $this$mapTo, C destination, Function1<? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$mapTo, "<this>");
        Intrinsics.checkNotNullParameter(destination, "destination");
        Intrinsics.checkNotNullParameter(transform, "transform");
        for (int i = 0; i < $this$mapTo.length(); i++) {
            char item = $this$mapTo.charAt(i);
            destination.add(transform.invoke(Character.valueOf(item)));
        }
        return destination;
    }

    public static final Iterable<IndexedValue<Character>> withIndex(final CharSequence $this$withIndex) {
        Intrinsics.checkNotNullParameter($this$withIndex, "<this>");
        return new IndexingIterable(new Function0<Iterator<? extends Character>>() { // from class: kotlin.text.StringsKt___StringsKt$withIndex$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            {
                super(0);
            }

            @Override // kotlin.jvm.functions.Function0
            public final Iterator<? extends Character> invoke() {
                return StringsKt.iterator($this$withIndex);
            }
        });
    }

    public static final boolean all(CharSequence $this$all, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$all, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$all.length(); i++) {
            char element = $this$all.charAt(i);
            if (!predicate.invoke(Character.valueOf(element)).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final boolean any(CharSequence $this$any) {
        Intrinsics.checkNotNullParameter($this$any, "<this>");
        return !($this$any.length() == 0);
    }

    public static final boolean any(CharSequence $this$any, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$any, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$any.length(); i++) {
            char element = $this$any.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                return true;
            }
        }
        return false;
    }

    private static final int count(CharSequence $this$count) {
        Intrinsics.checkNotNullParameter($this$count, "<this>");
        return $this$count.length();
    }

    public static final int count(CharSequence $this$count, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$count, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        int count = 0;
        for (int i = 0; i < $this$count.length(); i++) {
            char element = $this$count.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                count++;
            }
        }
        return count;
    }

    public static final <R> R fold(CharSequence charSequence, R r, Function2<? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        for (int i = 0; i < charSequence.length(); i++) {
            r2 = operation.invoke(r2, Character.valueOf(charSequence.charAt(i)));
        }
        return r2;
    }

    public static final <R> R foldIndexed(CharSequence charSequence, R r, Function3<? super Integer, ? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int i = 0;
        R r2 = r;
        int i2 = 0;
        while (i2 < charSequence.length()) {
            R r3 = r2;
            r2 = operation.invoke(Integer.valueOf(i), r3, Character.valueOf(charSequence.charAt(i2)));
            i2++;
            i++;
        }
        return r2;
    }

    public static final <R> R foldRight(CharSequence charSequence, R r, Function2<? super Character, ? super R, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        for (int lastIndex = StringsKt.getLastIndex(charSequence); lastIndex >= 0; lastIndex--) {
            r2 = operation.invoke(Character.valueOf(charSequence.charAt(lastIndex)), r2);
        }
        return r2;
    }

    public static final <R> R foldRightIndexed(CharSequence charSequence, R r, Function3<? super Integer, ? super Character, ? super R, ? extends R> operation) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        R r2 = r;
        for (int lastIndex = StringsKt.getLastIndex(charSequence); lastIndex >= 0; lastIndex--) {
            r2 = operation.invoke(Integer.valueOf(lastIndex), Character.valueOf(charSequence.charAt(lastIndex)), r2);
        }
        return r2;
    }

    public static final void forEach(CharSequence $this$forEach, Function1<? super Character, Unit> action) {
        Intrinsics.checkNotNullParameter($this$forEach, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        for (int i = 0; i < $this$forEach.length(); i++) {
            char element = $this$forEach.charAt(i);
            action.invoke(Character.valueOf(element));
        }
    }

    public static final void forEachIndexed(CharSequence $this$forEachIndexed, Function2<? super Integer, ? super Character, Unit> action) {
        Intrinsics.checkNotNullParameter($this$forEachIndexed, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        int index = 0;
        int i = 0;
        while (i < $this$forEachIndexed.length()) {
            char item = $this$forEachIndexed.charAt(i);
            action.invoke(Integer.valueOf(index), Character.valueOf(item));
            i++;
            index++;
        }
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final char maxOrThrow(CharSequence $this$max) {
        Intrinsics.checkNotNullParameter($this$max, "<this>");
        if ($this$max.length() == 0) {
            throw new NoSuchElementException();
        }
        char max = $this$max.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$max)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$max.charAt(i);
            if (Intrinsics.compare((int) max, (int) e) < 0) {
                max = e;
            }
        }
        return max;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final <R extends Comparable<? super R>> char maxByOrThrow(CharSequence $this$maxBy, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$maxBy, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxBy.length() == 0) {
            throw new NoSuchElementException();
        }
        char maxElem = $this$maxBy.charAt(0);
        int lastIndex = StringsKt.getLastIndex($this$maxBy);
        if (lastIndex == 0) {
            return maxElem;
        }
        Comparable maxValue = selector.invoke(Character.valueOf(maxElem));
        ?? it = new IntRange(1, lastIndex).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$maxBy.charAt(i);
            R invoke = selector.invoke(Character.valueOf(e));
            if (maxValue.compareTo(invoke) < 0) {
                maxElem = e;
                maxValue = invoke;
            }
        }
        return maxElem;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final <R extends Comparable<? super R>> Character maxByOrNull(CharSequence $this$maxByOrNull, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$maxByOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxByOrNull.length() == 0) {
            return null;
        }
        char maxElem = $this$maxByOrNull.charAt(0);
        int lastIndex = StringsKt.getLastIndex($this$maxByOrNull);
        if (lastIndex == 0) {
            return Character.valueOf(maxElem);
        }
        Comparable maxValue = selector.invoke(Character.valueOf(maxElem));
        ?? it = new IntRange(1, lastIndex).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$maxByOrNull.charAt(i);
            R invoke = selector.invoke(Character.valueOf(e));
            if (maxValue.compareTo(invoke) < 0) {
                maxElem = e;
                maxValue = invoke;
            }
        }
        return Character.valueOf(maxElem);
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [kotlin.collections.IntIterator] */
    private static final double maxOf(CharSequence $this$maxOf, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$maxOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOf.length() == 0) {
            throw new NoSuchElementException();
        }
        double maxValue = selector.invoke(Character.valueOf($this$maxOf.charAt(0))).doubleValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            double v = selector.invoke(Character.valueOf($this$maxOf.charAt(i))).doubleValue();
            maxValue = Math.max(maxValue, v);
        }
        return maxValue;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: maxOf, reason: collision with other method in class */
    private static final float m1529maxOf(CharSequence $this$maxOf, Function1<? super Character, Float> selector) {
        Intrinsics.checkNotNullParameter($this$maxOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOf.length() == 0) {
            throw new NoSuchElementException();
        }
        float maxValue = selector.invoke(Character.valueOf($this$maxOf.charAt(0))).floatValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            float v = selector.invoke(Character.valueOf($this$maxOf.charAt(i))).floatValue();
            maxValue = Math.max(maxValue, v);
        }
        return maxValue;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: maxOf, reason: collision with other method in class */
    private static final <R extends Comparable<? super R>> R m1530maxOf(CharSequence $this$maxOf, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$maxOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOf.length() == 0) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke(Character.valueOf($this$maxOf.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            R invoke2 = selector.invoke(Character.valueOf($this$maxOf.charAt(i)));
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r0v10, types: [kotlin.collections.IntIterator] */
    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final Double m1531maxOfOrNull(CharSequence $this$maxOfOrNull, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$maxOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOfOrNull.length() == 0) {
            return null;
        }
        double maxValue = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(0))).doubleValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            double v = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(i))).doubleValue();
            maxValue = Math.max(maxValue, v);
        }
        return Double.valueOf(maxValue);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: maxOfOrNull, reason: collision with other method in class */
    private static final Float m1532maxOfOrNull(CharSequence $this$maxOfOrNull, Function1<? super Character, Float> selector) {
        Intrinsics.checkNotNullParameter($this$maxOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOfOrNull.length() == 0) {
            return null;
        }
        float maxValue = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(0))).floatValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            float v = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(i))).floatValue();
            maxValue = Math.max(maxValue, v);
        }
        return Float.valueOf(maxValue);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R extends Comparable<? super R>> R maxOfOrNull(CharSequence $this$maxOfOrNull, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$maxOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$maxOfOrNull.length() == 0) {
            return null;
        }
        R invoke = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            R invoke2 = selector.invoke(Character.valueOf($this$maxOfOrNull.charAt(i)));
            if (invoke.compareTo(invoke2) < 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R> R maxOfWith(CharSequence charSequence, Comparator<? super R> comparator, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (charSequence.length() == 0) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke(Character.valueOf(charSequence.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex(charSequence)).iterator();
        while (it.hasNext()) {
            Object invoke2 = selector.invoke(Character.valueOf(charSequence.charAt(it.nextInt())));
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R> R maxOfWithOrNull(CharSequence charSequence, Comparator<? super R> comparator, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (charSequence.length() == 0) {
            return null;
        }
        R invoke = selector.invoke(Character.valueOf(charSequence.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex(charSequence)).iterator();
        while (it.hasNext()) {
            Object invoke2 = selector.invoke(Character.valueOf(charSequence.charAt(it.nextInt())));
            if (comparator.compare(invoke, invoke2) < 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final Character maxOrNull(CharSequence $this$maxOrNull) {
        Intrinsics.checkNotNullParameter($this$maxOrNull, "<this>");
        if ($this$maxOrNull.length() == 0) {
            return null;
        }
        char max = $this$maxOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$maxOrNull.charAt(i);
            if (Intrinsics.compare((int) max, (int) e) < 0) {
                max = e;
            }
        }
        return Character.valueOf(max);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final char maxWithOrThrow(CharSequence $this$maxWith, Comparator<? super Character> comparator) {
        Intrinsics.checkNotNullParameter($this$maxWith, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        if ($this$maxWith.length() == 0) {
            throw new NoSuchElementException();
        }
        char max = $this$maxWith.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxWith)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$maxWith.charAt(i);
            if (comparator.compare(Character.valueOf(max), Character.valueOf(e)) < 0) {
                max = e;
            }
        }
        return max;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final Character maxWithOrNull(CharSequence $this$maxWithOrNull, Comparator<? super Character> comparator) {
        Intrinsics.checkNotNullParameter($this$maxWithOrNull, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        if ($this$maxWithOrNull.length() == 0) {
            return null;
        }
        char max = $this$maxWithOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$maxWithOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$maxWithOrNull.charAt(i);
            if (comparator.compare(Character.valueOf(max), Character.valueOf(e)) < 0) {
                max = e;
            }
        }
        return Character.valueOf(max);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final char minOrThrow(CharSequence $this$min) {
        Intrinsics.checkNotNullParameter($this$min, "<this>");
        if ($this$min.length() == 0) {
            throw new NoSuchElementException();
        }
        char min = $this$min.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$min)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$min.charAt(i);
            if (Intrinsics.compare((int) min, (int) e) > 0) {
                min = e;
            }
        }
        return min;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final <R extends Comparable<? super R>> char minByOrThrow(CharSequence $this$minBy, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$minBy, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minBy.length() == 0) {
            throw new NoSuchElementException();
        }
        char minElem = $this$minBy.charAt(0);
        int lastIndex = StringsKt.getLastIndex($this$minBy);
        if (lastIndex == 0) {
            return minElem;
        }
        Comparable minValue = selector.invoke(Character.valueOf(minElem));
        ?? it = new IntRange(1, lastIndex).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$minBy.charAt(i);
            R invoke = selector.invoke(Character.valueOf(e));
            if (minValue.compareTo(invoke) > 0) {
                minElem = e;
                minValue = invoke;
            }
        }
        return minElem;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final <R extends Comparable<? super R>> Character minByOrNull(CharSequence $this$minByOrNull, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$minByOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minByOrNull.length() == 0) {
            return null;
        }
        char minElem = $this$minByOrNull.charAt(0);
        int lastIndex = StringsKt.getLastIndex($this$minByOrNull);
        if (lastIndex == 0) {
            return Character.valueOf(minElem);
        }
        Comparable minValue = selector.invoke(Character.valueOf(minElem));
        ?? it = new IntRange(1, lastIndex).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$minByOrNull.charAt(i);
            R invoke = selector.invoke(Character.valueOf(e));
            if (minValue.compareTo(invoke) > 0) {
                minElem = e;
                minValue = invoke;
            }
        }
        return Character.valueOf(minElem);
    }

    /* JADX WARN: Type inference failed for: r0v11, types: [kotlin.collections.IntIterator] */
    private static final double minOf(CharSequence $this$minOf, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$minOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOf.length() == 0) {
            throw new NoSuchElementException();
        }
        double minValue = selector.invoke(Character.valueOf($this$minOf.charAt(0))).doubleValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            double v = selector.invoke(Character.valueOf($this$minOf.charAt(i))).doubleValue();
            minValue = Math.min(minValue, v);
        }
        return minValue;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: minOf, reason: collision with other method in class */
    private static final float m1533minOf(CharSequence $this$minOf, Function1<? super Character, Float> selector) {
        Intrinsics.checkNotNullParameter($this$minOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOf.length() == 0) {
            throw new NoSuchElementException();
        }
        float minValue = selector.invoke(Character.valueOf($this$minOf.charAt(0))).floatValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            float v = selector.invoke(Character.valueOf($this$minOf.charAt(i))).floatValue();
            minValue = Math.min(minValue, v);
        }
        return minValue;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: minOf, reason: collision with other method in class */
    private static final <R extends Comparable<? super R>> R m1534minOf(CharSequence $this$minOf, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$minOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOf.length() == 0) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke(Character.valueOf($this$minOf.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOf)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            R invoke2 = selector.invoke(Character.valueOf($this$minOf.charAt(i)));
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r0v10, types: [kotlin.collections.IntIterator] */
    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final Double m1535minOfOrNull(CharSequence $this$minOfOrNull, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$minOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOfOrNull.length() == 0) {
            return null;
        }
        double minValue = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(0))).doubleValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            double v = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(i))).doubleValue();
            minValue = Math.min(minValue, v);
        }
        return Double.valueOf(minValue);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    /* renamed from: minOfOrNull, reason: collision with other method in class */
    private static final Float m1536minOfOrNull(CharSequence $this$minOfOrNull, Function1<? super Character, Float> selector) {
        Intrinsics.checkNotNullParameter($this$minOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOfOrNull.length() == 0) {
            return null;
        }
        float minValue = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(0))).floatValue();
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            float v = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(i))).floatValue();
            minValue = Math.min(minValue, v);
        }
        return Float.valueOf(minValue);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R extends Comparable<? super R>> R minOfOrNull(CharSequence $this$minOfOrNull, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter($this$minOfOrNull, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if ($this$minOfOrNull.length() == 0) {
            return null;
        }
        R invoke = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOfOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            R invoke2 = selector.invoke(Character.valueOf($this$minOfOrNull.charAt(i)));
            if (invoke.compareTo(invoke2) > 0) {
                invoke = invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R> R minOfWith(CharSequence charSequence, Comparator<? super R> comparator, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (charSequence.length() == 0) {
            throw new NoSuchElementException();
        }
        R invoke = selector.invoke(Character.valueOf(charSequence.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex(charSequence)).iterator();
        while (it.hasNext()) {
            Object invoke2 = selector.invoke(Character.valueOf(charSequence.charAt(it.nextInt())));
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    private static final <R> R minOfWithOrNull(CharSequence charSequence, Comparator<? super R> comparator, Function1<? super Character, ? extends R> selector) {
        Intrinsics.checkNotNullParameter(charSequence, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        Intrinsics.checkNotNullParameter(selector, "selector");
        if (charSequence.length() == 0) {
            return null;
        }
        R invoke = selector.invoke(Character.valueOf(charSequence.charAt(0)));
        ?? it = new IntRange(1, StringsKt.getLastIndex(charSequence)).iterator();
        while (it.hasNext()) {
            Object invoke2 = selector.invoke(Character.valueOf(charSequence.charAt(it.nextInt())));
            if (comparator.compare(invoke, invoke2) > 0) {
                invoke = (R) invoke2;
            }
        }
        return invoke;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final Character minOrNull(CharSequence $this$minOrNull) {
        Intrinsics.checkNotNullParameter($this$minOrNull, "<this>");
        if ($this$minOrNull.length() == 0) {
            return null;
        }
        char min = $this$minOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$minOrNull.charAt(i);
            if (Intrinsics.compare((int) min, (int) e) > 0) {
                min = e;
            }
        }
        return Character.valueOf(min);
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final char minWithOrThrow(CharSequence $this$minWith, Comparator<? super Character> comparator) {
        Intrinsics.checkNotNullParameter($this$minWith, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        if ($this$minWith.length() == 0) {
            throw new NoSuchElementException();
        }
        char min = $this$minWith.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minWith)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$minWith.charAt(i);
            if (comparator.compare(Character.valueOf(min), Character.valueOf(e)) > 0) {
                min = e;
            }
        }
        return min;
    }

    /* JADX WARN: Type inference failed for: r1v1, types: [kotlin.collections.IntIterator] */
    public static final Character minWithOrNull(CharSequence $this$minWithOrNull, Comparator<? super Character> comparator) {
        Intrinsics.checkNotNullParameter($this$minWithOrNull, "<this>");
        Intrinsics.checkNotNullParameter(comparator, "comparator");
        if ($this$minWithOrNull.length() == 0) {
            return null;
        }
        char min = $this$minWithOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$minWithOrNull)).iterator();
        while (it.hasNext()) {
            int i = it.nextInt();
            char e = $this$minWithOrNull.charAt(i);
            if (comparator.compare(Character.valueOf(min), Character.valueOf(e)) > 0) {
                min = e;
            }
        }
        return Character.valueOf(min);
    }

    public static final boolean none(CharSequence $this$none) {
        Intrinsics.checkNotNullParameter($this$none, "<this>");
        return $this$none.length() == 0;
    }

    public static final boolean none(CharSequence $this$none, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$none, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        for (int i = 0; i < $this$none.length(); i++) {
            char element = $this$none.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                return false;
            }
        }
        return true;
    }

    public static final <S extends CharSequence> S onEach(S s, Function1<? super Character, Unit> action) {
        Intrinsics.checkNotNullParameter(s, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        for (int i = 0; i < s.length(); i++) {
            char element = s.charAt(i);
            action.invoke(Character.valueOf(element));
        }
        return s;
    }

    public static final <S extends CharSequence> S onEachIndexed(S s, Function2<? super Integer, ? super Character, Unit> action) {
        Intrinsics.checkNotNullParameter(s, "<this>");
        Intrinsics.checkNotNullParameter(action, "action");
        int index$iv = 0;
        int i = 0;
        while (i < s.length()) {
            char item$iv = s.charAt(i);
            action.invoke(Integer.valueOf(index$iv), Character.valueOf(item$iv));
            i++;
            index$iv++;
        }
        return s;
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [kotlin.collections.IntIterator] */
    public static final char reduce(CharSequence $this$reduce, Function2<? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduce, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$reduce.length() == 0) {
            throw new UnsupportedOperationException("Empty char sequence can't be reduced.");
        }
        char accumulator = $this$reduce.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$reduce)).iterator();
        while (it.hasNext()) {
            int index = it.nextInt();
            accumulator = operation.invoke(Character.valueOf(accumulator), Character.valueOf($this$reduce.charAt(index))).charValue();
        }
        return accumulator;
    }

    /* JADX WARN: Type inference failed for: r2v2, types: [kotlin.collections.IntIterator] */
    public static final char reduceIndexed(CharSequence $this$reduceIndexed, Function3<? super Integer, ? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceIndexed, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$reduceIndexed.length() == 0) {
            throw new UnsupportedOperationException("Empty char sequence can't be reduced.");
        }
        char accumulator = $this$reduceIndexed.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$reduceIndexed)).iterator();
        while (it.hasNext()) {
            int index = it.nextInt();
            accumulator = operation.invoke(Integer.valueOf(index), Character.valueOf(accumulator), Character.valueOf($this$reduceIndexed.charAt(index))).charValue();
        }
        return accumulator;
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final Character reduceIndexedOrNull(CharSequence $this$reduceIndexedOrNull, Function3<? super Integer, ? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceIndexedOrNull, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$reduceIndexedOrNull.length() == 0) {
            return null;
        }
        char accumulator = $this$reduceIndexedOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$reduceIndexedOrNull)).iterator();
        while (it.hasNext()) {
            int index = it.nextInt();
            accumulator = operation.invoke(Integer.valueOf(index), Character.valueOf(accumulator), Character.valueOf($this$reduceIndexedOrNull.charAt(index))).charValue();
        }
        return Character.valueOf(accumulator);
    }

    /* JADX WARN: Type inference failed for: r2v1, types: [kotlin.collections.IntIterator] */
    public static final Character reduceOrNull(CharSequence $this$reduceOrNull, Function2<? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceOrNull, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$reduceOrNull.length() == 0) {
            return null;
        }
        char accumulator = $this$reduceOrNull.charAt(0);
        ?? it = new IntRange(1, StringsKt.getLastIndex($this$reduceOrNull)).iterator();
        while (it.hasNext()) {
            int index = it.nextInt();
            accumulator = operation.invoke(Character.valueOf(accumulator), Character.valueOf($this$reduceOrNull.charAt(index))).charValue();
        }
        return Character.valueOf(accumulator);
    }

    public static final char reduceRight(CharSequence $this$reduceRight, Function2<? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceRight, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int index = StringsKt.getLastIndex($this$reduceRight);
        if (index < 0) {
            throw new UnsupportedOperationException("Empty char sequence can't be reduced.");
        }
        char accumulator = $this$reduceRight.charAt(index);
        for (int index2 = index - 1; index2 >= 0; index2--) {
            accumulator = operation.invoke(Character.valueOf($this$reduceRight.charAt(index2)), Character.valueOf(accumulator)).charValue();
        }
        return accumulator;
    }

    public static final char reduceRightIndexed(CharSequence $this$reduceRightIndexed, Function3<? super Integer, ? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceRightIndexed, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int index = StringsKt.getLastIndex($this$reduceRightIndexed);
        if (index < 0) {
            throw new UnsupportedOperationException("Empty char sequence can't be reduced.");
        }
        char accumulator = $this$reduceRightIndexed.charAt(index);
        for (int index2 = index - 1; index2 >= 0; index2--) {
            accumulator = operation.invoke(Integer.valueOf(index2), Character.valueOf($this$reduceRightIndexed.charAt(index2)), Character.valueOf(accumulator)).charValue();
        }
        return accumulator;
    }

    public static final Character reduceRightIndexedOrNull(CharSequence $this$reduceRightIndexedOrNull, Function3<? super Integer, ? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceRightIndexedOrNull, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int index = StringsKt.getLastIndex($this$reduceRightIndexedOrNull);
        if (index < 0) {
            return null;
        }
        char accumulator = $this$reduceRightIndexedOrNull.charAt(index);
        for (int index2 = index - 1; index2 >= 0; index2--) {
            accumulator = operation.invoke(Integer.valueOf(index2), Character.valueOf($this$reduceRightIndexedOrNull.charAt(index2)), Character.valueOf(accumulator)).charValue();
        }
        return Character.valueOf(accumulator);
    }

    public static final Character reduceRightOrNull(CharSequence $this$reduceRightOrNull, Function2<? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$reduceRightOrNull, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        int index = StringsKt.getLastIndex($this$reduceRightOrNull);
        if (index < 0) {
            return null;
        }
        char accumulator = $this$reduceRightOrNull.charAt(index);
        for (int index2 = index - 1; index2 >= 0; index2--) {
            accumulator = operation.invoke(Character.valueOf($this$reduceRightOrNull.charAt(index2)), Character.valueOf(accumulator)).charValue();
        }
        return Character.valueOf(accumulator);
    }

    public static final <R> List<R> runningFold(CharSequence $this$runningFold, R r, Function2<? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter($this$runningFold, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$runningFold.length() == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result = new ArrayList($this$runningFold.length() + 1);
        result.add(r);
        Object accumulator = r;
        for (int i = 0; i < $this$runningFold.length(); i++) {
            char element = $this$runningFold.charAt(i);
            accumulator = operation.invoke(accumulator, Character.valueOf(element));
            result.add(accumulator);
        }
        return result;
    }

    public static final <R> List<R> runningFoldIndexed(CharSequence $this$runningFoldIndexed, R r, Function3<? super Integer, ? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter($this$runningFoldIndexed, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$runningFoldIndexed.length() == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result = new ArrayList($this$runningFoldIndexed.length() + 1);
        result.add(r);
        Object accumulator = r;
        int length = $this$runningFoldIndexed.length();
        for (int index = 0; index < length; index++) {
            accumulator = operation.invoke(Integer.valueOf(index), accumulator, Character.valueOf($this$runningFoldIndexed.charAt(index)));
            result.add(accumulator);
        }
        return result;
    }

    public static final List<Character> runningReduce(CharSequence $this$runningReduce, Function2<? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$runningReduce, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$runningReduce.length() == 0) {
            return CollectionsKt.emptyList();
        }
        char accumulator = $this$runningReduce.charAt(0);
        ArrayList result = new ArrayList($this$runningReduce.length());
        result.add(Character.valueOf(accumulator));
        int length = $this$runningReduce.length();
        for (int index = 1; index < length; index++) {
            accumulator = operation.invoke(Character.valueOf(accumulator), Character.valueOf($this$runningReduce.charAt(index))).charValue();
            result.add(Character.valueOf(accumulator));
        }
        return result;
    }

    public static final List<Character> runningReduceIndexed(CharSequence $this$runningReduceIndexed, Function3<? super Integer, ? super Character, ? super Character, Character> operation) {
        Intrinsics.checkNotNullParameter($this$runningReduceIndexed, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$runningReduceIndexed.length() == 0) {
            return CollectionsKt.emptyList();
        }
        char accumulator = $this$runningReduceIndexed.charAt(0);
        ArrayList result = new ArrayList($this$runningReduceIndexed.length());
        result.add(Character.valueOf(accumulator));
        int length = $this$runningReduceIndexed.length();
        for (int index = 1; index < length; index++) {
            accumulator = operation.invoke(Integer.valueOf(index), Character.valueOf(accumulator), Character.valueOf($this$runningReduceIndexed.charAt(index))).charValue();
            result.add(Character.valueOf(accumulator));
        }
        return result;
    }

    public static final <R> List<R> scan(CharSequence $this$scan, R r, Function2<? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter($this$scan, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$scan.length() == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result$iv = new ArrayList($this$scan.length() + 1);
        result$iv.add(r);
        Object accumulator$iv = r;
        for (int i = 0; i < $this$scan.length(); i++) {
            char element$iv = $this$scan.charAt(i);
            accumulator$iv = operation.invoke(accumulator$iv, Character.valueOf(element$iv));
            result$iv.add(accumulator$iv);
        }
        return result$iv;
    }

    public static final <R> List<R> scanIndexed(CharSequence $this$scanIndexed, R r, Function3<? super Integer, ? super R, ? super Character, ? extends R> operation) {
        Intrinsics.checkNotNullParameter($this$scanIndexed, "<this>");
        Intrinsics.checkNotNullParameter(operation, "operation");
        if ($this$scanIndexed.length() == 0) {
            return CollectionsKt.listOf(r);
        }
        ArrayList result$iv = new ArrayList($this$scanIndexed.length() + 1);
        result$iv.add(r);
        Object accumulator$iv = r;
        int length = $this$scanIndexed.length();
        for (int index$iv = 0; index$iv < length; index$iv++) {
            accumulator$iv = operation.invoke(Integer.valueOf(index$iv), accumulator$iv, Character.valueOf($this$scanIndexed.charAt(index$iv)));
            result$iv.add(accumulator$iv);
        }
        return result$iv;
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final int sumBy(CharSequence $this$sumBy, Function1<? super Character, Integer> selector) {
        Intrinsics.checkNotNullParameter($this$sumBy, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (int i = 0; i < $this$sumBy.length(); i++) {
            char element = $this$sumBy.charAt(i);
            sum += selector.invoke(Character.valueOf(element)).intValue();
        }
        return sum;
    }

    @Deprecated(message = "Use sumOf instead.", replaceWith = @ReplaceWith(expression = "this.sumOf(selector)", imports = {}))
    @DeprecatedSinceKotlin(warningSince = "1.5")
    public static final double sumByDouble(CharSequence $this$sumByDouble, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$sumByDouble, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (int i = 0; i < $this$sumByDouble.length(); i++) {
            char element = $this$sumByDouble.charAt(i);
            sum += selector.invoke(Character.valueOf(element)).doubleValue();
        }
        return sum;
    }

    private static final double sumOfDouble(CharSequence $this$sumOf, Function1<? super Character, Double> selector) {
        Intrinsics.checkNotNullParameter($this$sumOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        double sum = 0.0d;
        for (int i = 0; i < $this$sumOf.length(); i++) {
            char element = $this$sumOf.charAt(i);
            sum += selector.invoke(Character.valueOf(element)).doubleValue();
        }
        return sum;
    }

    private static final int sumOfInt(CharSequence $this$sumOf, Function1<? super Character, Integer> selector) {
        Intrinsics.checkNotNullParameter($this$sumOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = 0;
        for (int i = 0; i < $this$sumOf.length(); i++) {
            char element = $this$sumOf.charAt(i);
            sum += selector.invoke(Character.valueOf(element)).intValue();
        }
        return sum;
    }

    private static final long sumOfLong(CharSequence $this$sumOf, Function1<? super Character, Long> selector) {
        Intrinsics.checkNotNullParameter($this$sumOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = 0;
        for (int i = 0; i < $this$sumOf.length(); i++) {
            char element = $this$sumOf.charAt(i);
            sum += selector.invoke(Character.valueOf(element)).longValue();
        }
        return sum;
    }

    private static final int sumOfUInt(CharSequence $this$sumOf, Function1<? super Character, UInt> selector) {
        Intrinsics.checkNotNullParameter($this$sumOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        int sum = UInt.m332constructorimpl(0);
        for (int i = 0; i < $this$sumOf.length(); i++) {
            char element = $this$sumOf.charAt(i);
            sum = UInt.m332constructorimpl(selector.invoke(Character.valueOf(element)).getData() + sum);
        }
        return sum;
    }

    private static final long sumOfULong(CharSequence $this$sumOf, Function1<? super Character, ULong> selector) {
        Intrinsics.checkNotNullParameter($this$sumOf, "<this>");
        Intrinsics.checkNotNullParameter(selector, "selector");
        long sum = ULong.m411constructorimpl(0L);
        for (int i = 0; i < $this$sumOf.length(); i++) {
            char element = $this$sumOf.charAt(i);
            sum = ULong.m411constructorimpl(selector.invoke(Character.valueOf(element)).getData() + sum);
        }
        return sum;
    }

    public static final List<String> chunked(CharSequence $this$chunked, int size) {
        Intrinsics.checkNotNullParameter($this$chunked, "<this>");
        return StringsKt.windowed($this$chunked, size, size, true);
    }

    public static final <R> List<R> chunked(CharSequence $this$chunked, int size, Function1<? super CharSequence, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$chunked, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return StringsKt.windowed($this$chunked, size, size, true, transform);
    }

    public static final Sequence<String> chunkedSequence(CharSequence $this$chunkedSequence, int size) {
        Intrinsics.checkNotNullParameter($this$chunkedSequence, "<this>");
        return StringsKt.chunkedSequence($this$chunkedSequence, size, new Function1<CharSequence, String>() { // from class: kotlin.text.StringsKt___StringsKt$chunkedSequence$1
            @Override // kotlin.jvm.functions.Function1
            public final String invoke(CharSequence it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return it.toString();
            }
        });
    }

    public static final <R> Sequence<R> chunkedSequence(CharSequence $this$chunkedSequence, int size, Function1<? super CharSequence, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$chunkedSequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        return StringsKt.windowedSequence($this$chunkedSequence, size, size, true, transform);
    }

    public static final Pair<CharSequence, CharSequence> partition(CharSequence $this$partition, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$partition, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        StringBuilder first = new StringBuilder();
        StringBuilder second = new StringBuilder();
        for (int i = 0; i < $this$partition.length(); i++) {
            char element = $this$partition.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                first.append(element);
            } else {
                second.append(element);
            }
        }
        return new Pair<>(first, second);
    }

    public static final Pair<String, String> partition(String $this$partition, Function1<? super Character, Boolean> predicate) {
        Intrinsics.checkNotNullParameter($this$partition, "<this>");
        Intrinsics.checkNotNullParameter(predicate, "predicate");
        StringBuilder first = new StringBuilder();
        StringBuilder second = new StringBuilder();
        int length = $this$partition.length();
        for (int i = 0; i < length; i++) {
            char element = $this$partition.charAt(i);
            if (predicate.invoke(Character.valueOf(element)).booleanValue()) {
                first.append(element);
            } else {
                second.append(element);
            }
        }
        String sb = first.toString();
        Intrinsics.checkNotNullExpressionValue(sb, "first.toString()");
        String sb2 = second.toString();
        Intrinsics.checkNotNullExpressionValue(sb2, "second.toString()");
        return new Pair<>(sb, sb2);
    }

    public static /* synthetic */ List windowed$default(CharSequence charSequence, int i, int i2, boolean z, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return StringsKt.windowed(charSequence, i, i2, z);
    }

    public static final List<String> windowed(CharSequence $this$windowed, int size, int step, boolean partialWindows) {
        Intrinsics.checkNotNullParameter($this$windowed, "<this>");
        return StringsKt.windowed($this$windowed, size, step, partialWindows, new Function1<CharSequence, String>() { // from class: kotlin.text.StringsKt___StringsKt$windowed$1
            @Override // kotlin.jvm.functions.Function1
            public final String invoke(CharSequence it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return it.toString();
            }
        });
    }

    public static /* synthetic */ List windowed$default(CharSequence charSequence, int i, int i2, boolean z, Function1 function1, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return StringsKt.windowed(charSequence, i, i2, z, function1);
    }

    public static final <R> List<R> windowed(CharSequence $this$windowed, int size, int step, boolean partialWindows, Function1<? super CharSequence, ? extends R> transform) {
        int coercedEnd;
        Intrinsics.checkNotNullParameter($this$windowed, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        SlidingWindowKt.checkWindowSizeStep(size, step);
        int thisSize = $this$windowed.length();
        int resultCapacity = (thisSize / step) + (thisSize % step == 0 ? 0 : 1);
        ArrayList result = new ArrayList(resultCapacity);
        int index = 0;
        while (true) {
            if (!(index >= 0 && index < thisSize)) {
                break;
            }
            int end = index + size;
            if (end >= 0 && end <= thisSize) {
                coercedEnd = end;
            } else {
                if (!partialWindows) {
                    break;
                }
                coercedEnd = thisSize;
            }
            result.add(transform.invoke($this$windowed.subSequence(index, coercedEnd)));
            index += step;
        }
        return result;
    }

    public static /* synthetic */ Sequence windowedSequence$default(CharSequence charSequence, int i, int i2, boolean z, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return StringsKt.windowedSequence(charSequence, i, i2, z);
    }

    public static final Sequence<String> windowedSequence(CharSequence $this$windowedSequence, int size, int step, boolean partialWindows) {
        Intrinsics.checkNotNullParameter($this$windowedSequence, "<this>");
        return StringsKt.windowedSequence($this$windowedSequence, size, step, partialWindows, new Function1<CharSequence, String>() { // from class: kotlin.text.StringsKt___StringsKt$windowedSequence$1
            @Override // kotlin.jvm.functions.Function1
            public final String invoke(CharSequence it) {
                Intrinsics.checkNotNullParameter(it, "it");
                return it.toString();
            }
        });
    }

    public static /* synthetic */ Sequence windowedSequence$default(CharSequence charSequence, int i, int i2, boolean z, Function1 function1, int i3, Object obj) {
        if ((i3 & 2) != 0) {
            i2 = 1;
        }
        if ((i3 & 4) != 0) {
            z = false;
        }
        return StringsKt.windowedSequence(charSequence, i, i2, z, function1);
    }

    public static final <R> Sequence<R> windowedSequence(final CharSequence $this$windowedSequence, final int size, int step, boolean partialWindows, final Function1<? super CharSequence, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$windowedSequence, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        SlidingWindowKt.checkWindowSizeStep(size, step);
        IntProgression windows = RangesKt.step(partialWindows ? StringsKt.getIndices($this$windowedSequence) : RangesKt.until(0, ($this$windowedSequence.length() - size) + 1), step);
        return SequencesKt.map(CollectionsKt.asSequence(windows), new Function1<Integer, R>() { // from class: kotlin.text.StringsKt___StringsKt$windowedSequence$2
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public /* bridge */ /* synthetic */ Object invoke(Integer num) {
                return invoke(num.intValue());
            }

            public final R invoke(int index) {
                int end = size + index;
                int coercedEnd = (end < 0 || end > $this$windowedSequence.length()) ? $this$windowedSequence.length() : end;
                return transform.invoke($this$windowedSequence.subSequence(index, coercedEnd));
            }
        });
    }

    public static final List<Pair<Character, Character>> zip(CharSequence $this$zip, CharSequence other) {
        Intrinsics.checkNotNullParameter($this$zip, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        int length$iv = Math.min($this$zip.length(), other.length());
        ArrayList list$iv = new ArrayList(length$iv);
        for (int i$iv = 0; i$iv < length$iv; i$iv++) {
            char c1 = $this$zip.charAt(i$iv);
            char c2 = other.charAt(i$iv);
            list$iv.add(TuplesKt.to(Character.valueOf(c1), Character.valueOf(c2)));
        }
        return list$iv;
    }

    public static final <V> List<V> zip(CharSequence $this$zip, CharSequence other, Function2<? super Character, ? super Character, ? extends V> transform) {
        Intrinsics.checkNotNullParameter($this$zip, "<this>");
        Intrinsics.checkNotNullParameter(other, "other");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int length = Math.min($this$zip.length(), other.length());
        ArrayList list = new ArrayList(length);
        for (int i = 0; i < length; i++) {
            list.add(transform.invoke(Character.valueOf($this$zip.charAt(i)), Character.valueOf(other.charAt(i))));
        }
        return list;
    }

    public static final List<Pair<Character, Character>> zipWithNext(CharSequence $this$zipWithNext) {
        Intrinsics.checkNotNullParameter($this$zipWithNext, "<this>");
        int size$iv = $this$zipWithNext.length() - 1;
        if (size$iv < 1) {
            return CollectionsKt.emptyList();
        }
        ArrayList result$iv = new ArrayList(size$iv);
        for (int index$iv = 0; index$iv < size$iv; index$iv++) {
            char a = $this$zipWithNext.charAt(index$iv);
            char b = $this$zipWithNext.charAt(index$iv + 1);
            result$iv.add(TuplesKt.to(Character.valueOf(a), Character.valueOf(b)));
        }
        return result$iv;
    }

    public static final <R> List<R> zipWithNext(CharSequence $this$zipWithNext, Function2<? super Character, ? super Character, ? extends R> transform) {
        Intrinsics.checkNotNullParameter($this$zipWithNext, "<this>");
        Intrinsics.checkNotNullParameter(transform, "transform");
        int size = $this$zipWithNext.length() - 1;
        if (size < 1) {
            return CollectionsKt.emptyList();
        }
        ArrayList result = new ArrayList(size);
        for (int index = 0; index < size; index++) {
            result.add(transform.invoke(Character.valueOf($this$zipWithNext.charAt(index)), Character.valueOf($this$zipWithNext.charAt(index + 1))));
        }
        return result;
    }

    public static final Iterable<Character> asIterable(CharSequence $this$asIterable) {
        Intrinsics.checkNotNullParameter($this$asIterable, "<this>");
        if ($this$asIterable instanceof String) {
            if ($this$asIterable.length() == 0) {
                return CollectionsKt.emptyList();
            }
        }
        return new StringsKt___StringsKt$asIterable$$inlined$Iterable$1($this$asIterable);
    }

    public static final Sequence<Character> asSequence(final CharSequence $this$asSequence) {
        Intrinsics.checkNotNullParameter($this$asSequence, "<this>");
        if ($this$asSequence instanceof String) {
            if ($this$asSequence.length() == 0) {
                return SequencesKt.emptySequence();
            }
        }
        return new Sequence<Character>() { // from class: kotlin.text.StringsKt___StringsKt$asSequence$$inlined$Sequence$1
            @Override // kotlin.sequences.Sequence
            public Iterator<Character> iterator() {
                return StringsKt.iterator($this$asSequence);
            }
        };
    }
}
