package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j2.smali */
public class j2 extends e0 {
    private int C;

    public j2(h hVar) {
        super(hVar);
        this.C = -1;
    }

    private int p() throws IOException {
        if (this.C < 0) {
            int length = this.b.length;
            int i = 0;
            for (int i2 = 0; i2 < length; i2++) {
                i += this.b[i2].toASN1Primitive().f().a(true);
            }
            this.C = i;
        }
        return this.C;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        return z.a(z, p());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    d k() {
        return new w1(u0.a(h()), false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    l l() {
        return new x1(this);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    x m() {
        return new f2(x0.a(i()));
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    f0 n() {
        return new f3(false, o());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 48);
        h2 b = zVar.b();
        int length = this.b.length;
        int i = 0;
        if (this.C >= 0 || length > 16) {
            zVar.d(p());
            while (i < length) {
                this.b[i].toASN1Primitive().f().a(b, true);
                i++;
            }
            return;
        }
        b0[] b0VarArr = new b0[length];
        int i2 = 0;
        for (int i3 = 0; i3 < length; i3++) {
            b0 f = this.b[i3].toASN1Primitive().f();
            b0VarArr[i3] = f;
            i2 += f.a(true);
        }
        this.C = i2;
        zVar.d(i2);
        while (i < length) {
            b0VarArr[i].a(b, true);
            i++;
        }
    }

    public j2(i iVar) {
        super(iVar);
        this.C = -1;
    }

    j2(h[] hVarArr, boolean z) {
        super(hVarArr, z);
        this.C = -1;
    }
}
