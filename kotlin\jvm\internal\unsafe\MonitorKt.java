package kotlin.jvm.internal.unsafe;

import kotlin.Metadata;

/* compiled from: monitor.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0002\u001a\u0010\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0002¨\u0006\u0005"}, d2 = {"monitorEnter", "", "monitor", "", "monitorExit", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\internal\unsafe\MonitorKt.smali */
public final class MonitorKt {
    private static final void monitorEnter(Object monitor) {
        throw new UnsupportedOperationException("This function can only be used privately");
    }

    private static final void monitorExit(Object monitor) {
        throw new UnsupportedOperationException("This function can only be used privately");
    }
}
