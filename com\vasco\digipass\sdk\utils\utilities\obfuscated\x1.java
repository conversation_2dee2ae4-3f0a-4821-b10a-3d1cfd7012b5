package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\x1.smali */
public class x1 extends l {
    public x1(j2 j2Var) {
        super(j2Var);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.l
    e0 h() {
        i iVar = new i(4);
        w wVar = this.b;
        if (wVar != null) {
            iVar.a(wVar);
        }
        r rVar = this.x;
        if (rVar != null) {
            iVar.a(rVar);
        }
        b0 b0Var = this.C;
        if (b0Var != null) {
            iVar.a(b0Var.f());
        }
        int i = this.L;
        iVar.a(new m2(i == 0, i, this.R));
        return new j2(iVar);
    }

    public x1(w wVar, r rVar, b0 b0Var, int i, b0 b0Var2) {
        super(wVar, rVar, b0Var, i, b0Var2);
    }
}
