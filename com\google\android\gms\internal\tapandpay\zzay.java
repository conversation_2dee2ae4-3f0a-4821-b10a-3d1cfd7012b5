package com.google.android.gms.internal.tapandpay;

import java.util.Iterator;
import javax.annotation.CheckForNull;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\tapandpay\zzay.smali */
final class zzay extends zzau {
    static final zzay zza;
    private static final Object[] zzd;
    final transient Object[] zzb;
    final transient Object[] zzc;
    private final transient int zze;
    private final transient int zzf;
    private final transient int zzg;

    static {
        Object[] objArr = new Object[0];
        zzd = objArr;
        zza = new zzay(objArr, 0, objArr, 0, 0);
    }

    zzay(Object[] objArr, int i, Object[] objArr2, int i2, int i3) {
        this.zzb = objArr;
        this.zze = i;
        this.zzc = objArr2;
        this.zzf = i2;
        this.zzg = i3;
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.Set
    public final boolean contains(@CheckForNull Object obj) {
        Object[] objArr = this.zzc;
        if (obj == null || objArr.length == 0) {
            return false;
        }
        int zza2 = zzap.zza(obj.hashCode());
        while (true) {
            int i = zza2 & this.zzf;
            Object obj2 = objArr[i];
            if (obj2 == null) {
                return false;
            }
            if (obj2.equals(obj)) {
                return true;
            }
            zza2 = i + 1;
        }
    }

    @Override // com.google.android.gms.internal.tapandpay.zzau, java.util.Collection, java.util.Set
    public final int hashCode() {
        return this.zze;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzau, com.google.android.gms.internal.tapandpay.zzaq, java.util.AbstractCollection, java.util.Collection, java.lang.Iterable
    public final /* synthetic */ Iterator iterator() {
        return zzg().listIterator(0);
    }

    @Override // java.util.AbstractCollection, java.util.Collection, java.util.Set
    public final int size() {
        return this.zzg;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzaq
    final int zza(Object[] objArr, int i) {
        System.arraycopy(this.zzb, 0, objArr, 0, this.zzg);
        return this.zzg;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzaq
    final int zzb() {
        return this.zzg;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzaq
    final int zzc() {
        return 0;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzau, com.google.android.gms.internal.tapandpay.zzaq
    /* renamed from: zzd */
    public final zzba iterator() {
        return zzg().listIterator(0);
    }

    @Override // com.google.android.gms.internal.tapandpay.zzaq
    final Object[] zze() {
        return this.zzb;
    }

    @Override // com.google.android.gms.internal.tapandpay.zzau
    final zzat zzh() {
        return zzat.zzg(this.zzb, this.zzg);
    }

    @Override // com.google.android.gms.internal.tapandpay.zzau
    final boolean zzj() {
        return true;
    }
}
