package com.google.android.gms.auth;

import com.google.android.gms.common.Feature;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\auth\zze.smali */
public final class zze {
    public static final Feature zza;
    public static final Feature zzb;
    public static final Feature zzc;
    public static final Feature zzd;
    public static final Feature zze;
    public static final Feature zzf;
    public static final Feature zzg;
    public static final Feature zzh;
    public static final Feature zzi;
    public static final Feature zzj;
    public static final Feature zzk;
    public static final Feature zzl;
    public static final Feature[] zzm;

    static {
        Feature feature = new Feature("account_capability_api", 1L);
        zza = feature;
        Feature feature2 = new Feature("account_data_service", 6L);
        zzb = feature2;
        Feature feature3 = new Feature("account_data_service_legacy", 1L);
        zzc = feature3;
        Feature feature4 = new Feature("account_data_service_token", 8L);
        zzd = feature4;
        Feature feature5 = new Feature("account_data_service_visibility", 1L);
        zze = feature5;
        Feature feature6 = new Feature("config_sync", 1L);
        zzf = feature6;
        Feature feature7 = new Feature("device_account_api", 1L);
        zzg = feature7;
        Feature feature8 = new Feature("gaiaid_primary_email_api", 1L);
        zzh = feature8;
        Feature feature9 = new Feature("google_auth_service_accounts", 2L);
        zzi = feature9;
        Feature feature10 = new Feature("google_auth_service_token", 3L);
        zzj = feature10;
        Feature feature11 = new Feature("hub_mode_api", 1L);
        zzk = feature11;
        Feature feature12 = new Feature("work_account_client_is_whitelisted", 1L);
        zzl = feature12;
        zzm = new Feature[]{feature, feature2, feature3, feature4, feature5, feature6, feature7, feature8, feature9, feature10, feature11, feature12};
    }
}
