package fr.antelop.sdk.configuration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\configuration\AntelopConfigurationManager.smali */
public final class AntelopConfigurationManager {
    private static AntelopConfiguration CONFIGURATION = null;
    private static final Object LOCK = new Object();

    public static void set(AntelopConfiguration antelopConfiguration) {
        synchronized (LOCK) {
            CONFIGURATION = antelopConfiguration;
        }
    }

    public static void reset() {
        synchronized (LOCK) {
            CONFIGURATION = null;
        }
    }

    public static AntelopConfiguration get() {
        AntelopConfiguration antelopConfiguration;
        synchronized (LOCK) {
            antelopConfiguration = CONFIGURATION;
        }
        return antelopConfiguration;
    }
}
