package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.samsung.android.sdk.samsungpay.v2.PartnerInfo;
import com.samsung.android.sdk.samsungpay.v2.SamsungPay;
import com.samsung.android.sdk.samsungpay.v2.SpaySdk;
import com.samsung.android.sdk.samsungpay.v2.WatchManager;
import com.samsung.android.sdk.samsungpay.v2.card.CardManager;
import com.samsung.android.sdk.samsungpay.v2.payment.CardInfo;
import com.samsung.android.sdk.samsungpay.v2.payment.PaymentManager;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import java.lang.reflect.Method;
import o.a.m;
import o.ee.g;
import o.ee.i;
import o.ee.o;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\c.smali */
public abstract class c implements o.ep.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    protected static CardManager c;
    private static char[] f;
    protected static WatchManager j;
    private static int k;
    private static int l;
    private static char m;
    private static int n;
    protected SamsungPay a;
    protected PartnerInfo b;
    protected Context d;
    private String g;
    private final String h;
    private String i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        l = 1;
        f = new char[]{30557, 30536, 30562, 30532, 30580, 30585, 30559, 30572, 30586, 30542, 30589, 30588, 30531, 30535, 30533, 30582, 30560, 30534, 30543, 30541, 30537, 30539, 30561, 30570, 30568, 30552, 30554, 30528, 30538, 30497, 30566, 30553, 30571, 30569, 30544, 30583, 30556, 30574, 30530, 30563, 30581, 30555, 30529, 30540, 30567, 30584, 30587, 30591, 30558};
        m = (char) 17042;
        n = 874635450;
    }

    static void init$0() {
        $$a = new byte[]{13, -73, -57, -113};
        $$b = Opcodes.DCMPL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r6 = r6 * 2
            int r6 = 4 - r6
            int r8 = 109 - r8
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            r7 = r6
            goto L37
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1f:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L37:
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c.r(byte, int, short, java.lang.Object[]):void");
    }

    abstract String a();

    abstract void b(a.InterfaceC0042a<String> interfaceC0042a, Boolean bool, String str);

    abstract String e();

    public c() {
        Object[] objArr = new Object[1];
        p(KeyEvent.getDeadChar(0, 0) + 33, "\u001f\f\u001e$\u0019+\u0019%\u0013, \b,\t\f\t\u0017\u0019!0$\u0010 \r\u0018\t\u0002!\t\u0015\u001c\u001f㘞", (byte) (View.MeasureSpec.getSize(0) + 32), objArr);
        this.h = ((String) objArr[0]).intern();
        this.i = "";
        this.g = "";
    }

    protected void c(Context context) {
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        p((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 4, "\u001d\u0017 ,", (byte) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 58), objArr);
        g.d(a, ((String) objArr[0]).intern());
        this.d = context;
        try {
            Object[] objArr2 = new Object[1];
            p(33 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), "\u001f\f\u001e$\u0019+\u0019%\u0013, \b,\t\f\t\u0017\u0019!0$\u0010 \r\u0018\t\u0002!\t\u0015\u001c\u001f㘞", (byte) ((ViewConfiguration.getTapTimeout() >> 16) + 32), objArr2);
            String a2 = o.a(context, ((String) objArr2[0]).intern());
            Bundle bundle = new Bundle();
            Object[] objArr3 = new Object[1];
            q(18 - TextUtils.getOffsetAfter("", 0), "\ufffe\t\u0012￭\ufffe￼\u0002\u000f\u000b\ufffe￬\u000b\ufffe\u0007\r\u000b\ufffa￩", 18 - Drawable.resolveOpacity(0, 0), View.resolveSize(0, 0) + 300, true, objArr3);
            bundle.putString(((String) objArr3[0]).intern(), SpaySdk.ServiceType.APP2APP.toString());
            this.b = new PartnerInfo(a2, bundle);
            int i = k + 15;
            l = i % 128;
            int i2 = i % 2;
        } catch (PackageManager.NameNotFoundException e) {
            g.c();
            String a3 = a();
            Object[] objArr4 = new Object[1];
            q(View.MeasureSpec.getSize(0) + 22, "￬\u0013\u0004\u0006\uffbf\f\u000e\u0011\u0005\uffbf\u0011\u000e\u0011\u0011\u0004\uffbfￌ\uffbf\u0013\b\r\b\u0013\u0012\u0004\u0005\b\r\u0000￬\f\u000e\u0011￥\u0006\r\b\u0011\u0013\ufff2\u0000\u0013\u0000\u0003\u0000\u0013\u0004", TextUtils.indexOf((CharSequence) "", '0') + 48, 293 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), true, objArr4);
            g.a(a3, ((String) objArr4[0]).intern(), e);
        }
        this.a = new SamsungPay(context, this.b);
        d(context, e());
    }

    @Override // o.ep.a
    public final void b(a.InterfaceC0042a<String> interfaceC0042a) {
        int i = l + 59;
        k = i % 128;
        int i2 = i % 2;
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        q((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 1, "\u0003\u0000￥\u0010\u0001\b\b�\ufff3\u0015�￬\u0001\uffff\u0005\u0012\u0001￠\u0010\u0001", 20 - (Process.myTid() >> 22), 297 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), true, objArr);
        g.d(a, ((String) objArr[0]).intern());
        switch (!this.g.equals("") ? '=' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                Boolean bool = Boolean.TRUE;
                Object[] objArr2 = new Object[1];
                p(13 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), ",&㙘㙘\u0019,\u0019\f\u0018\t\u0012\u001f", (byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 98), objArr2);
                b(interfaceC0042a, bool, ((String) objArr2[0]).intern());
                break;
            default:
                int i3 = l + 79;
                k = i3 % 128;
                if (i3 % 2 != 0) {
                }
                interfaceC0042a.e((a.InterfaceC0042a<String>) this.g);
                int i4 = l + 25;
                k = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
    }

    protected final void d(String str) {
        int i = l + 39;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        switch (str != null ? 'L' : ':') {
            case Opcodes.ASTORE /* 58 */:
                break;
            default:
                int i4 = i2 + 95;
                l = i4 % 128;
                int i5 = i4 % 2;
                this.g = str;
                break;
        }
    }

    @Override // o.ep.a
    public final void a(a.InterfaceC0042a<String> interfaceC0042a) {
        int i = l + Opcodes.LREM;
        k = i % 128;
        int i2 = i % 2;
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        q(2 - TextUtils.indexOf("", "", 0), "\u0003\u0005\u0002\uffe7\u0003\u0001\u0007\u0014\u0003￢\u0017\uffff￮\u0003\u0001\u0007\u0014\u0003￢\u0012", KeyEvent.normalizeMetaState(0) + 20, 295 - TextUtils.getTrimmedLength(""), true, objArr);
        g.d(a, ((String) objArr[0]).intern());
        switch (this.i.equals("")) {
            case true:
                Boolean bool = Boolean.FALSE;
                Object[] objArr2 = new Object[1];
                p(Gravity.getAbsoluteGravity(0, 0) + 8, "\u001e\u0019\u0002!\t\u0015\u0012\u001f", (byte) (ImageFormat.getBitsPerPixel(0) + Opcodes.FNEG), objArr2);
                b(interfaceC0042a, bool, ((String) objArr2[0]).intern());
                return;
            default:
                int i3 = k + 39;
                l = i3 % 128;
                boolean z = i3 % 2 == 0;
                interfaceC0042a.e((a.InterfaceC0042a<String>) this.i);
                switch (z) {
                    case true:
                        throw null;
                    default:
                        return;
                }
        }
    }

    protected final void e(String str) {
        int i = k;
        int i2 = i + Opcodes.DREM;
        l = i2 % 128;
        Object obj = null;
        if (i2 % 2 == 0) {
            obj.hashCode();
            throw null;
        }
        switch (str != null ? '\r' : 'B') {
            case '\r':
                this.i = str;
                break;
        }
        int i3 = i + 21;
        l = i3 % 128;
        if (i3 % 2 != 0) {
        } else {
            throw null;
        }
    }

    @Override // o.ep.a
    public final void a(Activity activity, a.InterfaceC0042a<Object> interfaceC0042a, i iVar) {
        int i = l + Opcodes.DREM;
        k = i % 128;
        int i2 = i % 2;
        c(activity);
        int i3 = l + Opcodes.DDIV;
        k = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.ep.a
    public final void c(Activity activity) {
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        p('?' - AndroidCharacter.getMirror('0'), "\t\u000e\u001a\u001d\u001f\u0017\t\u000b\u0018\u001a&(%\u0019㘖", (byte) (View.resolveSizeAndState(0, 0, 0) + 40), objArr);
        g.d(a, ((String) objArr[0]).intern());
        this.a.getSamsungPayStatus(new SamsungPayConfigureWalletListener(this.a));
        int i = l + 23;
        k = i % 128;
        switch (i % 2 != 0 ? '4' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:28:0x0139  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected final com.samsung.android.sdk.samsungpay.v2.card.AddCardInfo b(o.an.h.d r18) {
        /*
            Method dump skipped, instructions count: 480
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c.b(o.an.h$d):com.samsung.android.sdk.samsungpay.v2.card.AddCardInfo");
    }

    @Override // o.ep.b
    public final void c(Activity activity, String str) {
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        p(8 - (ViewConfiguration.getEdgeSlop() >> 16), "\t.\u0011,,$\u000b\u001f", (byte) (113 - TextUtils.indexOf((CharSequence) "", '0')), objArr);
        g.d(a, ((String) objArr[0]).intern());
        Bundle bundle = new Bundle();
        Object[] objArr2 = new Object[1];
        p(16 - TextUtils.getOffsetAfter("", 0), ",(\u0014\u0016,\u001a\t&, \u000f\u0017$\u0014,\u001a", (byte) (124 - TextUtils.lastIndexOf("", '0')), objArr2);
        String intern = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        p(View.resolveSize(0, 0) + 7, ",(\u0010\u0001\u0018\u0017㘮", (byte) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 65), objArr3);
        bundle.putString(intern, ((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        q(11 - TextUtils.getCapsMode("", 0, 0), "\u0003\u0004\ufffe\t\ufff8\ufff6\b\u0003\ufff6\u0007\t\ufffa\u0005\u000e￩", 16 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 304, true, objArr4);
        bundle.putInt(((String) objArr4[0]).intern(), 3);
        g.c();
        String a2 = a();
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        q(4 - (ViewConfiguration.getScrollBarSize() >> 8), "\u0012ￎ￨ￎ!\u0016\u001d%\ufff1\u000f \u0012ￎￛￎ\u0011\u000f \u0012\ufff7", 'D' - AndroidCharacter.getMirror('0'), TextUtils.getOffsetAfter("", 0) + 279, false, objArr5);
        StringBuilder append = sb.append(((String) objArr5[0]).intern()).append(str);
        Object[] objArr6 = new Object[1];
        q(4 - TextUtils.getCapsMode("", 0, 0), "$ￗ￦ￗￗ\ufff1ￗ\u0018+\u0018\ufffb\u0018+\u001c", 14 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), Color.green(0) + 270, true, objArr6);
        g.d(a2, append.append(((String) objArr6[0]).intern()).append(bundle).toString());
        new PaymentManager(activity, this.b).startSimplePay(new CardInfo.Builder().setCardId(str).setCardMetaData(bundle).build(), new SamsungPayShowCardListener());
        int i = l + 67;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    private static void p(int i, String str, byte b, Object[] objArr) {
        int i2;
        char c2;
        int i3 = $11 + 73;
        $10 = i3 % 128;
        int i4 = i3 % 2;
        char[] charArray = str != null ? str.toCharArray() : str;
        m mVar = new m();
        char[] cArr = f;
        long j2 = 0;
        if (cArr != null) {
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i5 = 0;
            while (i5 < length) {
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr[i5])};
                    Object obj = o.e.a.s.get(-1401577988);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > j2 ? 1 : (SystemClock.elapsedRealtimeNanos() == j2 ? 0 : -1)) + 16, (char) View.combineMeasuredStates(0, 0), (ViewConfiguration.getLongPressTimeout() >> 16) + 76);
                        byte b2 = (byte) 0;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        r(b2, b3, (byte) (b3 | DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj);
                    }
                    cArr2[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i5++;
                    j2 = 0;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr = cArr2;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(m)};
            Object obj2 = o.e.a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(17 - View.MeasureSpec.getSize(0), (char) View.resolveSize(0, 0), 76 - ExpandableListView.getPackedPositionType(0L));
                byte b4 = (byte) 0;
                byte b5 = b4;
                Object[] objArr5 = new Object[1];
                r(b4, b5, (byte) (b5 | DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr3 = new char[i];
            char c3 = 11;
            switch (i % 2 != 0 ? 'T' : (char) 11) {
                case Opcodes.BASTORE /* 84 */:
                    int i6 = $11 + 21;
                    $10 = i6 % 128;
                    switch (i6 % 2 != 0) {
                        case false:
                            i2 = i - 1;
                            cArr3[i2] = (char) (charArray[i2] - b);
                            break;
                        default:
                            i2 = i + 38;
                            cArr3[i2] = (char) (charArray[i2] >>> b);
                            break;
                    }
                default:
                    i2 = i;
                    break;
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (true) {
                    switch (mVar.b >= i2) {
                        case false:
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                cArr3[mVar.b] = (char) (mVar.e - b);
                                cArr3[mVar.b + 1] = (char) (mVar.a - b);
                                c2 = c3;
                            } else {
                                try {
                                    Object[] objArr6 = new Object[13];
                                    objArr6[12] = mVar;
                                    objArr6[c3] = Integer.valueOf(charValue);
                                    objArr6[10] = mVar;
                                    objArr6[9] = mVar;
                                    objArr6[8] = Integer.valueOf(charValue);
                                    objArr6[7] = mVar;
                                    objArr6[6] = mVar;
                                    objArr6[5] = Integer.valueOf(charValue);
                                    objArr6[4] = mVar;
                                    objArr6[3] = mVar;
                                    objArr6[2] = Integer.valueOf(charValue);
                                    objArr6[1] = mVar;
                                    objArr6[0] = mVar;
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(TextUtils.getTrimmedLength("") + 10, (char) ((ViewConfiguration.getJumpTapTimeout() >> 16) + 8856), 324 - ((Process.getThreadPriority(0) + 20) >> 6));
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr7 = new Object[1];
                                        r(b6, b7, (byte) (b7 | 40), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    if (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 != null) {
                                                c2 = 11;
                                            } else {
                                                Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getTouchSlop() >> 8), (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (Process.myTid() >> 22) + 65);
                                                byte b8 = (byte) 0;
                                                byte b9 = b8;
                                                Object[] objArr9 = new Object[1];
                                                r(b8, b9, (byte) (b9 | 39), objArr9);
                                                c2 = 11;
                                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i7 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[intValue];
                                            cArr3[mVar.b + 1] = cArr[i7];
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else {
                                        c2 = 11;
                                        if (mVar.c == mVar.d) {
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i8 = (mVar.c * charValue) + mVar.i;
                                            int i9 = (mVar.d * charValue) + mVar.h;
                                            cArr3[mVar.b] = cArr[i8];
                                            cArr3[mVar.b + 1] = cArr[i9];
                                        } else {
                                            int i10 = (mVar.c * charValue) + mVar.h;
                                            int i11 = (mVar.d * charValue) + mVar.i;
                                            cArr3[mVar.b] = cArr[i10];
                                            cArr3[mVar.b + 1] = cArr[i11];
                                        }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                            c3 = c2;
                    }
                }
            }
            int i12 = 0;
            while (true) {
                switch (i12 >= i) {
                    case false:
                        cArr3[i12] = (char) (cArr3[i12] ^ 13722);
                        i12++;
                    default:
                        String str2 = new String(cArr3);
                        int i13 = $11 + 13;
                        $10 = i13 % 128;
                        int i14 = i13 % 2;
                        objArr[0] = str2;
                        return;
                }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(int r19, java.lang.String r20, int r21, int r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 546
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c.q(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
