package com.google.android.gms.tapandpay.quickaccesswallet;

import android.accounts.Account;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\GetQuickAccessWalletConfigRequest.smali */
public final class GetQuickAccessWalletConfigRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GetQuickAccessWalletConfigRequest> CREATOR = new zzd();
    private int zza;
    private Account zzb;

    private GetQuickAccessWalletConfigRequest() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof GetQuickAccessWalletConfigRequest) {
            GetQuickAccessWalletConfigRequest getQuickAccessWalletConfigRequest = (GetQuickAccessWalletConfigRequest) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(getQuickAccessWalletConfigRequest.zza)) && Objects.equal(this.zzb, getQuickAccessWalletConfigRequest.zzb)) {
                return true;
            }
        }
        return false;
    }

    public Account getAccount() {
        return this.zzb;
    }

    public int getSource() {
        return this.zza;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), this.zzb);
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 1, getSource());
        SafeParcelWriter.writeParcelable(dest, 2, getAccount(), flags, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    GetQuickAccessWalletConfigRequest(int i, Account account) {
        this.zza = i;
        this.zzb = account;
    }

    /* synthetic */ GetQuickAccessWalletConfigRequest(zzc zzcVar) {
    }
}
