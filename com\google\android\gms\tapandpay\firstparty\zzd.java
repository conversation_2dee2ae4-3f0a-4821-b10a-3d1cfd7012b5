package com.google.android.gms.tapandpay.firstparty;

import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelReader;
import java.util.ArrayList;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\firstparty\zzd.smali */
public final class zzd implements Parcelable.Creator {
    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ Object createFromParcel(Parcel parcel) {
        int validateObjectHeader = SafeParcelReader.validateObjectHeader(parcel);
        int i = 0;
        int i2 = 0;
        int i3 = 0;
        int i4 = 0;
        int i5 = 0;
        int i6 = 0;
        boolean z = false;
        boolean z2 = false;
        boolean z3 = false;
        boolean z4 = false;
        int i7 = 0;
        boolean z5 = false;
        int i8 = 0;
        boolean z6 = false;
        int i9 = 0;
        String str = null;
        String str2 = null;
        byte[] bArr = null;
        String str3 = null;
        String str4 = null;
        TokenStatus tokenStatus = null;
        String str5 = null;
        Uri uri = null;
        zzaj zzajVar = null;
        String str6 = null;
        zzaz zzazVar = null;
        String str7 = null;
        byte[] bArr2 = null;
        zzah zzahVar = null;
        zzaf zzafVar = null;
        String str8 = null;
        zzan[] zzanVarArr = null;
        ArrayList arrayList = null;
        String str9 = null;
        String str10 = null;
        zze zzeVar = null;
        String str11 = null;
        String str12 = null;
        long j = 0;
        long j2 = 0;
        long j3 = 0;
        long j4 = 0;
        while (parcel.dataPosition() < validateObjectHeader) {
            int readHeader = SafeParcelReader.readHeader(parcel);
            switch (SafeParcelReader.getFieldId(readHeader)) {
                case 2:
                    str = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 3:
                    bArr = SafeParcelReader.createByteArray(parcel, readHeader);
                    break;
                case 4:
                    str3 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 5:
                    str4 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 6:
                    i = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 7:
                    tokenStatus = (TokenStatus) SafeParcelReader.createParcelable(parcel, readHeader, TokenStatus.CREATOR);
                    break;
                case 8:
                    str5 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 9:
                    uri = (Uri) SafeParcelReader.createParcelable(parcel, readHeader, Uri.CREATOR);
                    break;
                case 10:
                    i2 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 11:
                    i3 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 12:
                    zzajVar = (zzaj) SafeParcelReader.createParcelable(parcel, readHeader, zzaj.CREATOR);
                    break;
                case 13:
                    str6 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 14:
                case 19:
                default:
                    SafeParcelReader.skipUnknownField(parcel, readHeader);
                    break;
                case 15:
                    zzazVar = (zzaz) SafeParcelReader.createParcelable(parcel, readHeader, zzaz.CREATOR);
                    break;
                case 16:
                    str7 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 17:
                    bArr2 = SafeParcelReader.createByteArray(parcel, readHeader);
                    break;
                case 18:
                    i4 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 20:
                    i5 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 21:
                    i6 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 22:
                    zzahVar = (zzah) SafeParcelReader.createParcelable(parcel, readHeader, zzah.CREATOR);
                    break;
                case 23:
                    zzafVar = (zzaf) SafeParcelReader.createParcelable(parcel, readHeader, zzaf.CREATOR);
                    break;
                case 24:
                    str8 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 25:
                    zzanVarArr = (zzan[]) SafeParcelReader.createTypedArray(parcel, readHeader, zzan.CREATOR);
                    break;
                case 26:
                    z = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 27:
                    arrayList = SafeParcelReader.createTypedList(parcel, readHeader, zzb.CREATOR);
                    break;
                case 28:
                    z2 = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 29:
                    z3 = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 30:
                    j = SafeParcelReader.readLong(parcel, readHeader);
                    break;
                case 31:
                    j2 = SafeParcelReader.readLong(parcel, readHeader);
                    break;
                case 32:
                    z4 = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 33:
                    j3 = SafeParcelReader.readLong(parcel, readHeader);
                    break;
                case 34:
                    str9 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 35:
                    str10 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 36:
                    zzeVar = (zze) SafeParcelReader.createParcelable(parcel, readHeader, zze.CREATOR);
                    break;
                case 37:
                    i7 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 38:
                    z5 = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 39:
                    str11 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 40:
                    i8 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 41:
                    z6 = SafeParcelReader.readBoolean(parcel, readHeader);
                    break;
                case 42:
                    j4 = SafeParcelReader.readLong(parcel, readHeader);
                    break;
                case 43:
                    str12 = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 44:
                    i9 = SafeParcelReader.readInt(parcel, readHeader);
                    break;
                case 45:
                    str2 = SafeParcelReader.createString(parcel, readHeader);
                    break;
            }
        }
        SafeParcelReader.ensureAtEnd(parcel, validateObjectHeader);
        return new CardInfo(str, str2, bArr, str3, str4, i, tokenStatus, str5, uri, i2, i3, zzajVar, str6, zzazVar, str7, bArr2, i4, i5, i6, zzahVar, zzafVar, str8, zzanVarArr, z, arrayList, z2, z3, j, j2, z4, j3, str9, str10, zzeVar, i7, z5, str11, i8, z6, j4, str12, i9);
    }

    @Override // android.os.Parcelable.Creator
    public final /* synthetic */ Object[] newArray(int i) {
        return new CardInfo[i];
    }
}
