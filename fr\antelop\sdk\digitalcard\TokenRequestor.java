package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.drawable.Drawable;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.Arrays;
import o.ee.o;
import o.eo.e;
import o.er.t;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\TokenRequestor.smali */
public final class TokenRequestor {
    private final e digitalCard;
    private final t innerTokenRequestor;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\TokenRequestor$Type.smali */
    public enum Type {
        Wallet,
        Ecommerce
    }

    public TokenRequestor(e eVar, t tVar) {
        this.digitalCard = eVar;
        this.innerTokenRequestor = tVar;
    }

    public final String getId() {
        return this.innerTokenRequestor.e();
    }

    public final Type[] getTypes() {
        return (Type[]) o.a(Type.class, this.innerTokenRequestor.c());
    }

    public final String getName() {
        return this.innerTokenRequestor.b();
    }

    public final Drawable getLogo(Context context) {
        return this.innerTokenRequestor.d(context);
    }

    public final void pushCard(Context context, String str, OperationCallback<CardPushUrl> operationCallback) throws WalletValidationException {
        this.innerTokenRequestor.e(context, str, this.digitalCard, operationCallback);
    }

    public final SecureCardPushToTokenRequestor getSecureCardPush() throws WalletValidationException {
        return this.innerTokenRequestor.b(this.digitalCard);
    }

    public final String toString() {
        return new StringBuilder("TokenRequestor{id='").append(getId()).append('\'').append(", types=").append(Arrays.toString(getTypes())).append(", name='").append(getName()).append('\'').append(", logo='").append(this.innerTokenRequestor.a()).append('\'').append('}').toString();
    }
}
