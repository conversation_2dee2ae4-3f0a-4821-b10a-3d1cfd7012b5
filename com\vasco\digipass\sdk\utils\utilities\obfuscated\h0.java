package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\h0.smali */
public abstract class h0 extends b0 {
    static final o0 x = new a(h0.class, 20);
    final byte[] b;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\h0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return h0.b(f2Var.h());
        }
    }

    h0(byte[] bArr, boolean z) {
        this.b = z ? Arrays.clone(bArr) : bArr;
    }

    static h0 b(byte[] bArr) {
        return new l2(bArr, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean e() {
        return false;
    }

    public final String h() {
        return o7.b(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public final int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public String toString() {
        return h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 20, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean a(b0 b0Var) {
        if (b0Var instanceof h0) {
            return Arrays.areEqual(this.b, ((h0) b0Var).b);
        }
        return false;
    }
}
