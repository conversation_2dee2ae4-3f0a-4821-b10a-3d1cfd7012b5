package o.du;

import android.graphics.Color;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import java.net.URI;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\h.smali */
public final class h implements d<o.dy.a, o.dx.d, o.dq.c, URI> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] b;
    private static int d;
    private static int e;
    private final o.dx.d a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        g();
        Color.green(0);
        int i = e + 63;
        d = i % 128;
        int i2 = i % 2;
    }

    static void g() {
        b = new int[]{77994494, 1937746415, -1013307881, 1017859004, 115236831, -1134971935, 1163569748, -211319053, 338147266, -1962066858, -355184117, -469255848, 639817663, 1143565756, -1560933695, -211961933, -459818750, -712466273};
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = Opcodes.I2B;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(short r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = 1 - r9
            int r7 = r7 + 115
            byte[] r0 = o.du.h.$$a
            int r8 = r8 * 3
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            int r9 = r9 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L35:
            int r9 = -r9
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.h.j(short, byte, short, java.lang.Object[]):void");
    }

    @Override // o.du.d
    public final /* synthetic */ o.dq.c a() {
        int i = d + 27;
        e = i % 128;
        int i2 = i % 2;
        o.dq.c f = f();
        int i3 = e + Opcodes.DMUL;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 74 / 0;
                return f;
            default:
                return f;
        }
    }

    @Override // o.du.d
    public final /* synthetic */ o.dy.a b() {
        int i = d + Opcodes.LSHL;
        e = i % 128;
        int i2 = i % 2;
        o.dy.a i3 = i();
        int i4 = d + 49;
        e = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return i3;
            default:
                throw null;
        }
    }

    @Override // o.du.d
    public final /* synthetic */ o.dx.d d() {
        int i = d + Opcodes.LNEG;
        e = i % 128;
        int i2 = i % 2;
        o.dx.d e2 = e();
        int i3 = d + 61;
        e = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    public h(String str) {
        this.a = new o.dx.d(str);
    }

    private static o.dy.a i() {
        o.dy.a aVar;
        int i = e + Opcodes.LSUB;
        d = i % 128;
        switch (i % 2 == 0 ? '`' : (char) 15) {
            case Opcodes.IADD /* 96 */:
                aVar = o.dy.a.a;
                int i2 = 26 / 0;
                break;
            default:
                aVar = o.dy.a.a;
                break;
        }
        int i3 = e + Opcodes.DSUB;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return aVar;
            default:
                int i4 = 72 / 0;
                return aVar;
        }
    }

    public final o.dx.d e() {
        int i = d + 71;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        o.dx.d dVar = this.a;
        int i4 = i2 + Opcodes.LSUB;
        d = i4 % 128;
        int i5 = i4 % 2;
        return dVar;
    }

    private static o.dq.c f() {
        int i = e + 9;
        d = i % 128;
        switch (i % 2 == 0 ? (char) 28 : (char) 31) {
            case 31:
                o.dq.c cVar = o.dq.c.b;
                int i2 = e + 75;
                d = i2 % 128;
                int i3 = i2 % 2;
                return cVar;
            default:
                o.dq.c cVar2 = o.dq.c.b;
                throw null;
        }
    }

    @Override // o.du.d
    public final String c() {
        int i = d + 77;
        e = i % 128;
        int i2 = i % 2;
        String c = e().c();
        int i3 = d + 51;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return c;
            default:
                throw null;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        h(new int[]{1243326463, -1628557101, 1660194993, -1569207158, -150802828, -1178677384, 480599686, 308028703, 1318076659, -1249413728, 688225028, -1150077467, -1258396493, -688482952, -1787081004, -92884680, -226121117, -716654555, 957817294, -1955296974, -782841024, 1828063569}, View.MeasureSpec.getMode(0) + 41, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(i().toString());
        Object[] objArr2 = new Object[1];
        h(new int[]{2078024057, 1268750266, 812237162, -243351308, 2111054948, 996783921}, Gravity.getAbsoluteGravity(0, 0) + 12, objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(e().toString());
        Object[] objArr3 = new Object[1];
        h(new int[]{2078024057, 1268750266, -176378365, -1524783971, -1587593365, -2024296502, -782841024, 1828063569}, 13 - KeyEvent.getDeadChar(0, 0), objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(f().toString()).append('}').toString();
        int i = d + 3;
        e = i % 128;
        int i2 = i % 2;
        return obj;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            int i = e + 97;
            d = i % 128;
            int i2 = i % 2;
            return true;
        }
        switch (obj != null) {
            case true:
                int i3 = e + 75;
                d = i3 % 128;
                int i4 = i3 % 2;
                switch (getClass() == obj.getClass() ? 'Y' : (char) 1) {
                    case 1:
                        break;
                    default:
                        return Objects.equals(this.a, ((h) obj).a);
                }
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(int[] r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1140
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.h.h(int[], int, java.lang.Object[]):void");
    }
}
