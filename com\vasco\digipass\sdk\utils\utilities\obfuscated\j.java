package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j.smali */
public class j extends b0 {
    static final o0 C = new a(j.class, 10);
    private static final j[] L = new j[12];
    private final byte[] b;
    private final int x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return j.a(f2Var.h(), false);
        }
    }

    j(byte[] bArr, boolean z) {
        if (r.c(bArr)) {
            throw new IllegalArgumentException("malformed enumerated");
        }
        if ((bArr[0] & ByteCompanionObject.MIN_VALUE) != 0) {
            throw new IllegalArgumentException("enumerated must be non-negative");
        }
        this.b = z ? Arrays.clone(bArr) : bArr;
        this.x = r.d(bArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return Arrays.hashCode(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 10, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (b0Var instanceof j) {
            return Arrays.areEqual(this.b, ((j) b0Var).b);
        }
        return false;
    }

    static j a(byte[] bArr, boolean z) {
        if (bArr.length > 1) {
            return new j(bArr, z);
        }
        if (bArr.length != 0) {
            int i = bArr[0] & 255;
            j[] jVarArr = L;
            if (i >= jVarArr.length) {
                return new j(bArr, z);
            }
            j jVar = jVarArr[i];
            if (jVar != null) {
                return jVar;
            }
            j jVar2 = new j(bArr, z);
            jVarArr[i] = jVar2;
            return jVar2;
        }
        throw new IllegalArgumentException("ENUMERATED has zero length");
    }
}
