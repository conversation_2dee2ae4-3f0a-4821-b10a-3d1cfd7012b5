package org.apache.cordova;

import android.content.Context;
import android.content.Intent;
import androidx.appcompat.app.AppCompatActivity;
import java.util.concurrent.ExecutorService;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\CordovaInterface.smali */
public interface CordovaInterface {
    AppCompatActivity getActivity();

    Context getContext();

    ExecutorService getThreadPool();

    boolean hasPermission(String permission);

    Object onMessage(String id, Object data);

    void requestPermission(CordovaPlugin plugin, int requestCode, String permission);

    void requestPermissions(CordovaPlugin plugin, int requestCode, String[] permissions);

    void setActivityResultCallback(CordovaPlugin plugin);

    void startActivityForResult(CordovaPlugin command, Intent intent, int requestCode);
}
