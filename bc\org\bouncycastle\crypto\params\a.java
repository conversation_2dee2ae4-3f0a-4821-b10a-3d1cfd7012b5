package bc.org.bouncycastle.crypto.params;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\params\a.smali */
public class a extends AsymmetricKeyParameter {
    private final ECDomainParameters b;

    protected a(boolean z, ECDomainParameters eCDomainParameters) {
        super(z);
        if (eCDomainParameters == null) {
            throw new NullPointerException("'parameters' cannot be null");
        }
        this.b = eCDomainParameters;
    }

    public ECDomainParameters getParameters() {
        return this.b;
    }
}
