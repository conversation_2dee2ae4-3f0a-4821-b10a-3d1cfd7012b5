package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\i.smali */
public class i {
    static final h[] d = new h[0];
    private h[] a;
    private int b;
    private boolean c;

    public i() {
        this(10);
    }

    public void a(h hVar) {
        if (hVar == null) {
            throw new NullPointerException("'element' cannot be null");
        }
        int length = this.a.length;
        int i = this.b + 1;
        if (this.c | (i > length)) {
            b(i);
        }
        this.a[this.b] = hVar;
        this.b = i;
    }

    public int b() {
        return this.b;
    }

    h[] c() {
        int i = this.b;
        if (i == 0) {
            return d;
        }
        h[] hVarArr = this.a;
        if (hVarArr.length == i) {
            this.c = true;
            return hVarArr;
        }
        h[] hVarArr2 = new h[i];
        System.arraycopy(hVarArr, 0, hVarArr2, 0, i);
        return hVarArr2;
    }

    public i(int i) {
        if (i < 0) {
            throw new IllegalArgumentException("'initialCapacity' must not be negative");
        }
        this.a = i == 0 ? d : new h[i];
        this.b = 0;
        this.c = false;
    }

    private void b(int i) {
        h[] hVarArr = new h[Math.max(this.a.length, i + (i >> 1))];
        System.arraycopy(this.a, 0, hVarArr, 0, this.b);
        this.a = hVarArr;
        this.c = false;
    }

    public h a(int i) {
        if (i < this.b) {
            return this.a[i];
        }
        throw new ArrayIndexOutOfBoundsException(i + " >= " + this.b);
    }

    h[] a() {
        int i = this.b;
        if (i == 0) {
            return d;
        }
        h[] hVarArr = new h[i];
        System.arraycopy(this.a, 0, hVarArr, 0, i);
        return hVarArr;
    }

    static h[] a(h[] hVarArr) {
        return hVarArr.length < 1 ? d : (h[]) hVarArr.clone();
    }
}
