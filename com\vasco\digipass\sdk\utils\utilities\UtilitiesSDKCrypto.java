package com.vasco.digipass.sdk.utils.utilities;

import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.Mac;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import org.bouncycastle.pqc.jcajce.spec.McElieceCCA2KeyGenParameterSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\UtilitiesSDKCrypto.smali */
public final class UtilitiesSDKCrypto {
    private static final SecureRandom a = new SecureRandom();

    private UtilitiesSDKCrypto() {
    }

    static byte[] a(boolean z, byte b, byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) throws UtilitiesSDKException {
        int length;
        if (b != 2 && b != 3) {
            return a.a(z, b, b2, bArr, bArr2, bArr3);
        }
        if (bArr2 != null) {
            try {
                length = bArr2.length;
            } catch (Exception e) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR, e);
            }
        } else {
            length = 0;
        }
        int i = length;
        return z ? a(1, b, b2, bArr, bArr2, i, bArr3) : a(2, b, b2, bArr, bArr2, i, bArr3);
    }

    static UtilitiesSDKCryptoResponse b(int i) {
        if (i <= 0) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.OUTPUT_DATA_INCORRECT_LENGTH);
        }
        try {
            return new UtilitiesSDKCryptoResponse(0, a(i));
        } catch (NoSuchAlgorithmException e) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static UtilitiesSDKCryptoResponse hash(byte b, byte[] bArr) {
        if (b != 2 && b != 3) {
            return a.a(b, bArr);
        }
        if (bArr == null) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.INPUT_DATA_NULL);
        }
        try {
            return b == 2 ? new UtilitiesSDKCryptoResponse(0, a(McElieceCCA2KeyGenParameterSpec.SHA1, bArr)) : new UtilitiesSDKCryptoResponse(0, a("SHA-256", bArr));
        } catch (NoSuchAlgorithmException e) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static UtilitiesSDKCryptoResponse hmac(byte b, byte[] bArr, byte[] bArr2) {
        if (bArr == null) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.INPUT_DATA_NULL);
        }
        if (bArr2 == null) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.KEY_NULL);
        }
        if (b != 3 && b != 2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID);
        }
        try {
            return new UtilitiesSDKCryptoResponse(0, b == 2 ? a("HmacSHA1", bArr2, bArr2.length, bArr) : a("HmacSHA256", bArr2, bArr2.length, bArr));
        } catch (Exception e) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    private static byte[] b(String str, byte[] bArr) throws NoSuchAlgorithmException {
        if (str.contains("SHA256")) {
            str = "SHA-256";
        } else if (str.contains("SHA384")) {
            str = McElieceCCA2KeyGenParameterSpec.SHA384;
        } else if (str.contains("SHA512")) {
            str = "SHA-512";
        } else if (str.contains("SHA1")) {
            str = McElieceCCA2KeyGenParameterSpec.SHA1;
        } else if (str.contains("MD5")) {
            str = "MD5";
        }
        byte[] bArr2 = new byte[bArr.length + 64];
        System.arraycopy(new byte[]{54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54}, 0, bArr2, 0, 64);
        System.arraycopy(bArr, 0, bArr2, 64, bArr.length);
        byte[] a2 = a(str, bArr2);
        byte[] bArr3 = new byte[a2.length + 64];
        System.arraycopy(new byte[]{92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92, 92}, 0, bArr3, 0, 64);
        System.arraycopy(a2, 0, bArr3, 64, a2.length);
        return a(str, bArr3);
    }

    private static byte[] a(int i) throws NoSuchAlgorithmException {
        byte[] bArr = new byte[i];
        a.nextBytes(bArr);
        return bArr;
    }

    private static String a(byte b) {
        if (b == 1) {
            return "DES";
        }
        if (b != 2) {
            return "AES";
        }
        return "DESede";
    }

    private static String a(byte b, byte b2) {
        StringBuilder sb = new StringBuilder();
        sb.append(a(b));
        sb.append("/");
        if (b2 == 1) {
            sb.append("ECB");
        } else if (b2 == 2) {
            sb.append("CBC");
        } else if (b2 != 3) {
            sb.append("CTR");
        } else {
            sb.append("CFB8");
        }
        sb.append("/NoPadding");
        return sb.toString();
    }

    private static byte[] a(int i, byte b, byte b2, byte[] bArr, byte[] bArr2, int i2, byte[] bArr3) throws NoSuchAlgorithmException, NoSuchPaddingException, BadPaddingException, IllegalBlockSizeException, InvalidAlgorithmParameterException, InvalidKeyException {
        IvParameterSpec ivParameterSpec;
        if (b == 2 && bArr.length != 24) {
            byte[] bArr4 = new byte[24];
            System.arraycopy(bArr, 0, bArr4, 0, 16);
            System.arraycopy(bArr, 0, bArr4, 16, 8);
            bArr = bArr4;
        }
        SecretKeySpec secretKeySpec = new SecretKeySpec(bArr, a(b));
        Cipher cipher = Cipher.getInstance(a(b, b2));
        if (bArr2 == null && b2 != 2 && b2 != 3) {
            cipher.init(i, secretKeySpec);
        } else {
            if (bArr2 != null) {
                ivParameterSpec = new IvParameterSpec(bArr2, 0, i2);
            } else {
                byte[] bArr5 = new byte[8];
                if (b == 3) {
                    bArr5 = new byte[16];
                }
                ivParameterSpec = new IvParameterSpec(bArr5);
            }
            cipher.init(i, secretKeySpec, ivParameterSpec);
        }
        return cipher.doFinal(bArr3);
    }

    private static byte[] a(String str, byte[] bArr) throws NoSuchAlgorithmException {
        return MessageDigest.getInstance(str).digest(bArr);
    }

    private static byte[] a(String str, byte[] bArr, int i, byte[] bArr2) throws NoSuchAlgorithmException, InvalidKeyException {
        if (i == 0) {
            return b(str, bArr2);
        }
        Mac mac = Mac.getInstance(str);
        mac.init(new SecretKeySpec(bArr, 0, i, str));
        return mac.doFinal(bArr2);
    }
}
