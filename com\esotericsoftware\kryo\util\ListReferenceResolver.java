package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.ReferenceResolver;
import java.util.ArrayList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\ListReferenceResolver.smali */
public class ListReferenceResolver implements ReferenceResolver {
    protected Kryo kryo;
    protected final ArrayList seenObjects = new ArrayList();

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setKryo(Kryo kryo) {
        this.kryo = kryo;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int addWrittenObject(Object object) {
        int id = this.seenObjects.size();
        this.seenObjects.add(object);
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int getWrittenId(Object object) {
        int n = this.seenObjects.size();
        for (int i = 0; i < n; i++) {
            if (this.seenObjects.get(i) == object) {
                return i;
            }
        }
        return -1;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public int nextReadId(Class type) {
        int id = this.seenObjects.size();
        this.seenObjects.add(null);
        return id;
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void setReadObject(int id, Object object) {
        this.seenObjects.set(id, object);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public Object getReadObject(Class type, int id) {
        return this.seenObjects.get(id);
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public void reset() {
        this.seenObjects.clear();
    }

    @Override // com.esotericsoftware.kryo.ReferenceResolver
    public boolean useReferences(Class type) {
        return (Util.isWrapperClass(type) || Util.isEnum(type)) ? false : true;
    }
}
