package o.ck;

import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.Gravity;
import com.esotericsoftware.asm.Opcodes;
import o.cc.e;
import o.eg.b;
import o.et.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ck\c.smali */
public final class c implements e<d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static int b;
    private static long c;
    private static boolean d;
    private static char[] e;
    private static int h;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        e();
        Gravity.getAbsoluteGravity(0, 0);
        int i = h + 51;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                break;
            default:
                int i2 = 9 / 0;
                break;
        }
    }

    static void e() {
        c = -6427404990657688745L;
        e = new char[]{61631, 61628, 61618, 61608, 61619, 61617, 61612, 61622, 61614, 61633, 61626, 61822, 61629, 61634, 61625, 61638, 61627, 61635, 61639, 61624, 61599, 61602, 61597, 61586, 61592, 61587, 61582, 61807, 61632, 61623, 61616, 61590, 61573};
        a = true;
        d = true;
        b = 782102863;
    }

    private static void i(int i, int i2, int i3, Object[] objArr) {
        int i4 = (i2 * 2) + 1;
        int i5 = i3 + 4;
        byte[] bArr = $$a;
        int i6 = 121 - i;
        byte[] bArr2 = new byte[i4];
        int i7 = -1;
        int i8 = i4 - 1;
        if (bArr == null) {
            i6 += i8;
            i8 = i8;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = -1;
        }
        while (true) {
            int i9 = i5 + 1;
            int i10 = i7 + 1;
            bArr2[i10] = (byte) i6;
            if (i10 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i6 += bArr[i9];
            i8 = i8;
            i5 = i9;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = i10;
        }
    }

    static void init$0() {
        $$a = new byte[]{91, -22, 50, -29};
        $$b = 83;
    }

    @Override // o.cc.e
    public final /* synthetic */ d d(String str, String str2, int i, String str3) {
        int i2 = h + 35;
        j = i2 % 128;
        int i3 = i2 % 2;
        d b2 = b(str, str2, i, str3);
        int i4 = h + 35;
        j = i4 % 128;
        switch (i4 % 2 != 0 ? 'U' : 'a') {
            case Opcodes.LADD /* 97 */:
                return b2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0094, code lost:
    
        r14 = new java.lang.Object[r10];
        g(null, 127 - (android.view.ViewConfiguration.getFadingEdgeLength() >> r8), null, "\u0082\u0086\u0083\u0085\u0084\u0083\u0082\u0081\u0081", r14);
        r12 = o.dk.e.d(r26.B(((java.lang.String) r14[r11]).intern()));
        r15 = new java.lang.Object[r10];
        f("䶕冫疶ᦳ㶑솎\ue582", android.graphics.Color.argb(r11, r11, r11, r11) + 7177, r15);
        r13 = b(r26.s(((java.lang.String) r15[r11]).intern()));
        r14 = r13.iterator();
        r15 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x00da, code lost:
    
        if (r14.hasNext() == false) goto L76;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x00dc, code lost:
    
        r15 = o.ck.c.h + 79;
        o.ck.c.j = r15 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00e6, code lost:
    
        if ((r15 % 2) == 0) goto L23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00e8, code lost:
    
        r15 = r14.next().a();
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00f4, code lost:
    
        r8 = 67 / r11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00f5, code lost:
    
        if (r15 == null) goto L77;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x010b, code lost:
    
        r14 = 36373 - android.view.KeyEvent.getDeadChar(r11, r11);
        r11 = new java.lang.Object[r10];
        f("䶔쎢冈\ue79b痺诈᧐꼷㴫댛셡坁", r14, r11);
        r0 = r26.v(((java.lang.String) r11[0]).intern());
        r14 = new java.lang.Object[r10];
        g(null, (android.view.ViewConfiguration.getTapTimeout() >> 16) + 127, null, "\u008c\u008b\u008a\u0087\u0084\u0087\u0089\u0088\u0087", r14);
        r0.B(((java.lang.String) r14[0]).intern());
        r14 = new java.lang.Object[1];
        f("䶤\ue9e3Լꅃ\udcac砥鑿㎱澝", (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)) + 42060, r14);
        r0.B(((java.lang.String) r14[0]).intern());
        r14 = new java.lang.Object[1];
        f("䶤᷄\ued48볋ౠ\udf9d꼔纾츻鹌槔", android.os.Process.getGidForName("") + 20594, r14);
        r0.B(((java.lang.String) r14[0]).intern());
        r14 = new java.lang.Object[1];
        g(null, 126 - android.text.TextUtils.lastIndexOf("", '0'), null, "\u0090\u0087\u008f\u0083\u008d\u008d\u008e\u0087\u0084\u008b\u008d\u0087", r14);
        r0.B(((java.lang.String) r14[0]).intern());
        r14 = new java.lang.Object[1];
        g(null, android.os.Process.getGidForName("") + 128, null, "\u0090\u0087\u008f\u0083\u008d\u008d\u008e\u0087\u0084\u008b\u008d\u0087", r14);
        r6 = o.ej.e.c(java.lang.Integer.parseInt(r0.r(((java.lang.String) r14[0]).intern()), 16));
        r8 = new java.lang.Object[1];
        g(null, (android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)) + com.esotericsoftware.asm.Opcodes.IAND, null, "\u0088\u0081\u0092\u0092\u0084\u0092\u0091\u008e\u0089\u0086\u0083\u0085", r8);
        r0.B(((java.lang.String) r8[0]).intern());
        r10 = new java.lang.Object[1];
        f("䶮ႅ\uf7d6娡㥦鱀抾쇼ꐦଓ\uee44䲨Ꮤ\uf6d9唟㡵黦", 23857 - android.view.KeyEvent.normalizeMetaState(0), r10);
        r0.B(((java.lang.String) r10[0]).intern());
        r10 = new java.lang.Object[1];
        f("䶮㮋ꇊ⼏镞ʎ裢瘒ﱖ斝폘夆읬䲗㫃ꀛ⸅", 30271 - android.graphics.drawable.Drawable.resolveOpacity(0, 0), r10);
        r0.B(((java.lang.String) r10[0]).intern());
        r11 = new java.lang.Object[1];
        f("䶪Დ\uefd5븇१\udbb6ꪦ痕쐂靵憠ヮ菏", 20790 - (android.media.AudioTrack.getMaxVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x022f, code lost:
    
        if (r0.b(((java.lang.String) r11[0]).intern()) == false) goto L30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x0231, code lost:
    
        r11 = new java.lang.Object[1];
        f("䶪Დ\uefd5븇१\udbb6ꪦ痕쐂靵憠ヮ菏", (android.view.ViewConfiguration.getScrollBarSize() >> 8) + 20789, r11);
        o.ej.a.a(r0.B(((java.lang.String) r11[0]).intern()), r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x024f, code lost:
    
        r8 = new java.lang.Object[1];
        g(null, android.text.TextUtils.lastIndexOf("", '0', 0) + 128, null, "\u0092\u008f\u008e\u0094\u008b\u0089\u0084\u0083\u008a\u0088\u0092\u0089\u0091\u008e\u008b\u008e\u0087\u0084\u0093\u0089\u008b", r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x026c, code lost:
    
        if (r0.b(((java.lang.String) r8[0]).intern()) == false) goto L41;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x026e, code lost:
    
        r5 = o.ck.c.j + com.esotericsoftware.asm.Opcodes.LSUB;
        o.ck.c.h = r5 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x027a, code lost:
    
        if ((r5 % 2) != 0) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x027e, code lost:
    
        r11 = new java.lang.Object[1];
        f("䶪廝歉矩Hⳏ㥋엩홺\ue2f7轼鯺ꑪ낎崘榭稃ڔ", 5771 >>> android.widget.ExpandableListView.getPackedPositionGroup(1), r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0299, code lost:
    
        if (r0.b(((java.lang.String) r11[0]).intern()) == false) goto L41;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x02b9, code lost:
    
        r11 = new java.lang.Object[1];
        g(null, 126 - android.text.TextUtils.lastIndexOf("", '0', 0), null, "\u0092\u008f\u008e\u0094\u008b\u0089\u0084\u0083\u008a\u0088\u0092\u0089\u0091\u008e\u008b\u008e\u0087\u0084\u0093\u0089\u008b", r11);
        o.ej.a.a(r0.B(((java.lang.String) r11[0]).intern()), r6);
        r10 = new java.lang.Object[1];
        f("䶪廝歉矩Hⳏ㥋엩홺\ue2f7轼鯺ꑪ낎崘榭稃ڔ", 4987 - android.text.TextUtils.getOffsetBefore("", 0), r10);
        java.lang.Short.parseShort(r0.r(((java.lang.String) r10[0]).intern()));
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x029c, code lost:
    
        r11 = new java.lang.Object[1];
        f("䶪廝歉矩Hⳏ㥋엩홺\ue2f7轼鯺ꑪ낎崘榭稃ڔ", android.widget.ExpandableListView.getPackedPositionGroup(0) + 4987, r11);
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x02b7, code lost:
    
        if (r0.b(((java.lang.String) r11[0]).intern()) == false) goto L41;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x02f5, code lost:
    
        r10 = new java.lang.Object[1];
        g(null, 126 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0, 0), null, "\u0091\u0094\u008d\u0092\u008f\u0094\u0087\u0084\u008f\u0094\u0088\u0092\u0089\u0087\u0088\u0091\u0081\u0081\u0089", r10);
        new o.dn.a(r0.B(((java.lang.String) r10[0]).intern()));
        r8 = new java.lang.Object[1];
        g(null, (android.view.ViewConfiguration.getEdgeSlop() >> 16) + 127, null, "\u0088\u0087\u0081", r8);
        r0.z(((java.lang.String) r8[0]).intern());
        r8 = new java.lang.Object[1];
        f("䶣앇屸", 35051 - (android.view.ViewConfiguration.getTapTimeout() >> 16), r8);
        r0.i(((java.lang.String) r8[0]).intern());
        r8 = new java.lang.Object[1];
        f("䶗铿ｏ쇅⠬犈唦뱒蛠\ue956㏄", 55661 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16), r8);
        r4 = r0.v(((java.lang.String) r8[0]).intern());
        r8 = new java.lang.Object[1];
        g(null, 127 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), null, "\u0081\u0088\u0089", r8);
        r4.B(((java.lang.String) r8[0]).intern());
        r10 = new java.lang.Object[1];
        f("䶦㛐뭉", android.graphics.Color.red(0) + 31601, r10);
        r4.B(((java.lang.String) r10[0]).intern());
        r6 = new java.lang.Object[1];
        g(null, 127 - (android.view.ViewConfiguration.getEdgeSlop() >> 16), null, "\u009b\u009a\u0099\u0084\u0098\u0097\u0096\u0095", r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x03c7, code lost:
    
        if (r0.b(((java.lang.String) r6[0]).intern()) == false) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x03c9, code lost:
    
        r6 = new java.lang.Object[1];
        g(null, (android.view.ViewConfiguration.getJumpTapTimeout() >> 16) + 127, null, "\u009b\u009a\u0099\u0084\u0098\u0097\u0096\u0095", r6);
        r0 = r0.v(((java.lang.String) r6[0]).intern());
        r5 = new java.lang.Object[1];
        g(null, 127 - android.text.TextUtils.getCapsMode("", 0, 0), null, "\u0081\u0088\u0089", r5);
        r0.B(((java.lang.String) r5[0]).intern());
        r5 = new java.lang.Object[1];
        f("䶦㛐뭉", android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0, 0) + 31602, r5);
        r0.B(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x041b, code lost:
    
        r0 = r9.iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x0423, code lost:
    
        if (r0.hasNext() == false) goto L79;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x0425, code lost:
    
        r1 = o.ck.c.h + 57;
        o.ck.c.j = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x042f, code lost:
    
        if ((r1 % 2) == 0) goto L55;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0431, code lost:
    
        r1 = (o.et.d) r0.next();
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x043b, code lost:
    
        if (r9.size() <= 0) goto L52;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x043d, code lost:
    
        r2 = '\f';
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x0442, code lost:
    
        switch(r2) {
            case 25: goto L58;
            default: goto L54;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0453, code lost:
    
        r1.b(o.dk.e.a(r1.j(), r12));
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0462, code lost:
    
        r1.a(r13);
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x0465, code lost:
    
        if (r15 == null) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0467, code lost:
    
        r1.g(o.dk.b.d(r15));
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x045f, code lost:
    
        r1.b(r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0440, code lost:
    
        r2 = 25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0446, code lost:
    
        r1 = (o.et.d) r0.next();
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x0451, code lost:
    
        if (r9.size() <= 1) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0471, code lost:
    
        return r9;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x00fb, code lost:
    
        r15 = r14.next().a();
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x0105, code lost:
    
        if (r15 == null) goto L78;
     */
    @Override // o.cc.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.util.List<o.et.d> a(java.lang.String r22, java.lang.String r23, int r24, java.lang.String r25, o.eg.b r26) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1228
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ck.c.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    private static d a(b bVar, String str, String str2, int i, String str3) throws o.eg.d {
        d dVar = new d(str, str2, i, str3);
        Object[] objArr = new Object[1];
        f("䶦뤓ꓙ", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 62652, objArr);
        dVar.a(bVar.B(((String) objArr[0]).intern()));
        Object[] objArr2 = new Object[1];
        f("䶡㏱넄", TextUtils.indexOf("", "") + 32341, objArr2);
        dVar.e(bVar.B(((String) objArr2[0]).intern()));
        byte[] C = dVar.C();
        Object[] objArr3 = new Object[1];
        f("䷲ै쑧菘", ImageFormat.getBitsPerPixel(0) + 17610, objArr3);
        dVar.d(o.ej.d.b(C, ((String) objArr3[0]).intern()));
        int i2 = h + 25;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return dVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<o.cc.a> b(o.eg.e r12) throws o.eg.d, o.ei.i {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            r1 = 0
            r2 = r1
        L8:
            int r3 = r12.d()
            r4 = 1
            if (r2 >= r3) goto L11
            r3 = r1
            goto L12
        L11:
            r3 = r4
        L12:
            r5 = 2
            switch(r3) {
                case 1: goto L22;
                default: goto L16;
            }
        L16:
            int r3 = o.ck.c.h
            int r3 = r3 + 91
            int r6 = r3 % 128
            o.ck.c.j = r6
            int r3 = r3 % r5
            if (r3 == 0) goto L2c
            goto L2c
        L22:
            int r12 = o.ck.c.j
            int r12 = r12 + 63
            int r1 = r12 % 128
            o.ck.c.h = r1
            int r12 = r12 % r5
            return r0
        L2c:
            o.eg.b r3 = r12.b(r2)
            int r6 = android.view.ViewConfiguration.getKeyRepeatTimeout()
            int r6 = r6 >> 16
            r7 = 33343(0x823f, float:4.6723E-41)
            int r6 = r6 + r7
            java.lang.Object[] r7 = new java.lang.Object[r4]
            java.lang.String r8 = "䶮쾜"
            f(r8, r6, r7)
            r6 = r7[r1]
            java.lang.String r6 = (java.lang.String) r6
            java.lang.String r6 = r6.intern()
            java.lang.String r6 = r3.r(r6)
            int r7 = r6.length()
            r8 = 4
            if (r7 != r8) goto L8e
            java.lang.String r7 = r6.substring(r1, r5)
            byte r7 = o.dk.b.c(r7)
            java.lang.String r5 = r6.substring(r5, r8)
            byte r5 = o.dk.b.c(r5)
            o.cc.a r6 = new o.cc.a
            long r8 = android.os.SystemClock.elapsedRealtimeNanos()
            r10 = 0
            int r8 = (r8 > r10 ? 1 : (r8 == r10 ? 0 : -1))
            int r8 = r8 + 126
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r9 = 0
            java.lang.String r10 = "\u0083\u008e\u0091\u0089\u008a"
            g(r9, r8, r9, r10, r4)
            r4 = r4[r1]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            byte[] r3 = r3.B(r4)
            r6.<init>(r7, r5, r3)
            r0.add(r6)
            int r2 = r2 + 1
            goto L8
        L8e:
            o.ei.i r12 = new o.ei.i
            r0 = 35363(0x8a23, float:4.9554E-41)
            int r2 = android.view.View.MeasureSpec.getSize(r1)
            int r0 = r0 - r2
            java.lang.Object[] r2 = new java.lang.Object[r4]
            java.lang.String r3 = "䶷입姳폝攮Ｚ煰譑Ჰ階⣽ꈵ㑃中쀍喣\uef99懢\ufbd0ല蜒\u197c鋥⒐뻪ハ䨦\udc04噷\ue879綹\uf7daো茡ᔏ꽩⅟몠"
            f(r3, r0, r2)
            r0 = r2[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r12.<init>(r0)
            throw r12
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ck.c.b(o.eg.e):java.util.List");
    }

    private static d b(String str, String str2, int i, String str3) {
        d dVar = new d(str, str2, i, str3);
        int i2 = h + 5;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                throw null;
            default:
                return dVar;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0047, code lost:
    
        r8 = '(';
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x004c, code lost:
    
        r9 = 9;
        r10 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0054, code lost:
    
        switch(r8) {
            case 59: goto L79;
            default: goto L25;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0057, code lost:
    
        r8 = r5.b;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x00e5, code lost:
    
        r11 = new java.lang.Object[]{java.lang.Integer.valueOf(r1[r5.b]), r5, r5};
        r9 = o.e.a.s.get(806930129);
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x00fe, code lost:
    
        if (r9 == null) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x015e, code lost:
    
        r7[r8] = ((java.lang.Long) ((java.lang.reflect.Method) r9).invoke(null, r11)).longValue() ^ (o.ck.c.c ^ (-5249873463433509232L));
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0169, code lost:
    
        r8 = new java.lang.Object[]{r5, r5};
        r9 = o.e.a.s.get(-10300751);
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x017a, code lost:
    
        if (r9 == null) goto L53;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x01c7, code lost:
    
        ((java.lang.reflect.Method) r9).invoke(null, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x01cd, code lost:
    
        r8 = o.ck.c.$11 + 77;
        o.ck.c.$10 = r8 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x01d6, code lost:
    
        if ((r8 % 2) == 0) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x01d8, code lost:
    
        r8 = '\b';
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x01dd, code lost:
    
        switch(r8) {
            case 8: goto L19;
            default: goto L19;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x01db, code lost:
    
        r8 = '\\';
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x017d, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(android.view.KeyEvent.getDeadChar(0, 0) + 12, (char) (55184 - android.graphics.ImageFormat.getBitsPerPixel(0)), 537 - (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForChild(0, 0) == 0 ? 0 : -1)));
        r11 = (byte) 0;
        r13 = new java.lang.Object[1];
        i((byte) 9, r11, (byte) (r11 - 1), r13);
        r9 = r9.getMethod((java.lang.String) r13[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-10300751, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x01e2, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x01e3, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x01e7, code lost:
    
        if (r1 != null) goto L64;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x01e9, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x01ea, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0101, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(android.text.TextUtils.indexOf("", "", 0, 0) + 10, (char) (60578 - (android.os.SystemClock.currentThreadTimeMillis() > (-1) ? 1 : (android.os.SystemClock.currentThreadTimeMillis() == (-1) ? 0 : -1))), 354 - android.widget.ExpandableListView.getPackedPositionType(0));
        r15 = (byte) 0;
        r14 = new java.lang.Object[1];
        i((byte) 7, r15, (byte) (r15 - 1), r14);
        r9 = r9.getMethod((java.lang.String) r14[0], java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(806930129, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x01eb, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x01ec, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x01f0, code lost:
    
        if (r1 != null) goto L69;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x01f2, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x01f3, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x005f, code lost:
    
        r0 = new char[r6];
        r5.b = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0067, code lost:
    
        if (r5.b >= r1.length) goto L82;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0069, code lost:
    
        r0[r5.b] = (char) r7[r5.b];
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0073, code lost:
    
        r6 = new java.lang.Object[]{r5, r5};
        r8 = o.e.a.s.get(-10300751);
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0081, code lost:
    
        if (r8 == null) goto L33;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x00c9, code lost:
    
        ((java.lang.reflect.Method) r8).invoke(null, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x00ce, code lost:
    
        r9 = 9;
        r10 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0084, code lost:
    
        r8 = (java.lang.Class) o.e.a.c(12 - (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16), (char) (55186 - (android.os.Process.getElapsedCpuTime() > r10 ? 1 : (android.os.Process.getElapsedCpuTime() == r10 ? 0 : -1))), 538 - android.view.View.MeasureSpec.getSize(0));
        r15 = (byte) 0;
        r10 = new java.lang.Object[1];
        i((byte) r9, r15, (byte) (r15 - 1), r10);
        r8 = r8.getMethod((java.lang.String) r10[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-10300751, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:5:0x0016, code lost:
    
        if (r22 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x00d3, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x00d4, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x00d8, code lost:
    
        if (r1 != null) goto L39;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x00da, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x00db, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x00dc, code lost:
    
        r24[0] = new java.lang.String(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x00e3, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:69:0x004a, code lost:
    
        r8 = ';';
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0030, code lost:
    
        r1 = r22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x0022, code lost:
    
        r1 = r22.toCharArray();
        r5 = o.ck.c.$11 + 47;
        o.ck.c.$10 = r5 % 128;
        r5 = r5 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x001c, code lost:
    
        if (r22 != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x0032, code lost:
    
        r1 = r1;
        r5 = new o.a.k();
        r5.a = r23;
        r6 = r1.length;
        r7 = new long[r6];
        r5.b = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x0045, code lost:
    
        if (r5.b >= r1.length) goto L22;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 518
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ck.c.f(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v26, types: [byte[]] */
    private static void g(java.lang.String r18, int r19, int[] r20, java.lang.String r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 940
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ck.c.g(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
