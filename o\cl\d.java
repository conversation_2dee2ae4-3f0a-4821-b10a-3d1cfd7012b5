package o.cl;

import android.graphics.Color;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.List;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.l;
import o.a.m;
import o.eg.b;
import o.et.e;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cl\d.smali */
abstract class d<T extends e> implements o.cc.e<T> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static char[] b;
    private static int c;
    private static char d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        a();
        int i = e + 51;
        c = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        a = new char[]{50928, 50856, 50833, 50853, 50834, 50858, 50875, 50877, 50848, 50877, 50851, 50860, 50860, 50852, 50853, 50855, 50876, 50834, 50842, 50877, 50868, 50868, 50850, 50853, 50932, 50854, 50862, 50863, 50839, 50860, 50877, 50855, 50858, 50855, 50853, 50831, 50912, 50912, 50845, 50797, 50768, 50771, 50789, 50784, 50785, 50784, 50789, 50753, 51163, 51157, 50745, 50746, 50737, 50938, 50859, 50858, 50853, 50755, 51176, 51176, 51136, 51139, 51180, 51183, 51183, 51180, 51154, 51176, 51153, 50854, 50691, 50689, 50940, 50863, 50856, 50859, 50853, 50876, 50849, 50860, 50940, 50862, 50862, 50860, 50860, 50863, 50856, 50859, 50940, 50849, 50879, 50695, 50689, 50689, 50691, 50717, 50690, 50711, 50691, 50695, 50729, 50691, 50708, 50717, 50704, 50704, 50733, 50691, 50940, 50863, 50860, 50861, 50860, 50852, 50879, 50876, 50876, 50878, 50872, 50849, 50860, 50852, 50852, 50857, 50758, 51153, 51162, 51138, 51159, 51154, 51156, 51153, 51180, 51183, 51158, 51156, 51163, 51166, 51157, 51156, 50758, 51155, 51155, 51155, 51158, 51158, 51181, 51152, 51158, 51153, 51177, 51179, 51155, 51155, 51152, 51157, 51162, 51158, 51153, 51159, 51156, 51159, 50816, 50769, 50772, 50903, 50939, 50876, 50707, 50705, 50711, 50709, 50729, 50735, 50699, 50804, 50802, 50938, 50853, 50863, 50938, 50852, 50857, 50838, 50863, 50854, 50854, 50765, 51161, 51178, 51177, 50736, 50741, 51177, 51182, 51159, 51181, 51173, 51177, 51183, 51177, 50736, 50741, 51154, 51155, 51154, 51173, 51178, 51178, 51183, 51180, 51180, 51159, 50741, 51148, 51176, 51155, 51181, 51155, 50741, 50738, 51180, 51156, 51157, 51153, 51180, 51181, 50740, 50726, 50737, 50741, 50730, 50731, 51139, 51179, 51177, 51181, 51153, 51157, 51181, 51177, 51183, 51177, 50736, 50721, 51165, 51177, 51172, 51149, 50734, 50734, 50713, 50690};
        b = new char[]{30588, 30534, 30584, 30570, 30528, 30562, 30557, 30587, 30568, 30537, 30559, 30565, 30563, 30582, 30572, 30566, 30591, 30560, 30573, 30574, 30544, 30542, 30531, 30498, 30589, 30571, 30585, 30564, 30539, 30561, 30511, 30590, 30567, 30519, 30525, 30586};
        d = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r8 = r8 + 66
            byte[] r0 = o.cl.d.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L39
        L19:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1d:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L39:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cl.d.i(short, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{11, -55, -41, 6};
        $$b = 75;
    }

    abstract void d(List<T> list, b bVar) throws o.eg.d;

    abstract T e(String str, String str2, int i, String str3);

    d() {
    }

    @Override // o.cc.e
    public final /* synthetic */ o.el.d d(String str, String str2, int i, String str3) {
        int i2 = e + 83;
        c = i2 % 128;
        boolean z = i2 % 2 != 0;
        T c2 = c(str, str2, i, str3);
        switch (z) {
            default:
                int i3 = 24 / 0;
            case true:
                return c2;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:29:0x00b5, code lost:
    
        r13 = new java.lang.Object[r5];
        g("\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001", new int[]{57, r12, com.esotericsoftware.asm.Opcodes.NEW, r12}, r5, r13);
        r9 = r0.v(((java.lang.String) r13[r6]).intern());
        r8 = new java.lang.Object[r5];
        h(5 - android.view.View.combineMeasuredStates(r6, r6), "\r\u0014\u0012\u0001㙠", (byte) (android.view.View.resolveSizeAndState(r6, r6, r6) + 109), r8);
        r9.B(((java.lang.String) r8[r6]).intern());
        r13 = new java.lang.Object[r5];
        g("\u0001\u0000\u0001", new int[]{69, 3, 100, r6}, r5, r13);
        r9.B(((java.lang.String) r13[r6]).intern());
        r14 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0000", new int[]{72, 8, r6, 4}, r5, r14);
        r9.B(((java.lang.String) r14[r6]).intern());
        r14 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{80, 8, r6, r6}, r5, r14);
        r9.B(((java.lang.String) r14[r6]).intern());
        r12 = new java.lang.Object[r5];
        h(24 - android.text.TextUtils.indexOf((java.lang.CharSequence) r4, '0', r6), "\f\u0003\u0005\u001e\u0000\u001b\u001a\u0014\u001d\u0002\u0012\u0002\u0000\u000f\u0002\u000f\t\r\u0017#\u0012\u000e\f\u0003㘖", (byte) (40 - android.view.View.MeasureSpec.getMode(r6)), r12);
        r9.D(((java.lang.String) r12[r6]).intern());
        r13 = new java.lang.Object[r5];
        g("\u0001\u0001\u0000", new int[]{88, 3, r6, r6}, r6, r13);
        r9.D(((java.lang.String) r13[r6]).intern());
        r12 = new java.lang.Object[r5];
        g(null, new int[]{91, 17, com.esotericsoftware.asm.Opcodes.DMUL, 4}, r5, r12);
        r8 = r9.q(((java.lang.String) r12[r6]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0192, code lost:
    
        if (r8 == null) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0194, code lost:
    
        r12 = '\\';
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x019b, code lost:
    
        switch(r12) {
            case 28: goto L18;
            default: goto L17;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x019e, code lost:
    
        o.ej.e.c(java.lang.Integer.parseInt(r8, 16));
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x01a5, code lost:
    
        r14 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001", new int[]{108, 16, r6, r6}, r6, r14);
        r9.z(((java.lang.String) r14[r6]).intern());
        r12 = new java.lang.Object[r5];
        g("\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000", new int[]{com.esotericsoftware.asm.Opcodes.IUSHR, 16, com.esotericsoftware.asm.Opcodes.FRETURN, 6}, r6, r12);
        r9.z(((java.lang.String) r12[r6]).intern());
        r14 = new java.lang.Object[r5];
        h((android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16) + 26, "#\u0017\u001a\u0014\u001a\r\u001d\u0002\u0015\u0014㘛㘛\u0005\u000b\u001e\u0011\u0019\r\f\u001d\u0012\u000e\u0011\u0003\r\t", (byte) (34 - (android.view.ViewConfiguration.getTapTimeout() >> 16)), r14);
        r9.z(((java.lang.String) r14[r6]).intern());
        r13 = new java.lang.Object[r5];
        h((android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)) + 21, "#\u0017\u001a\u0014\u001a\r\u001d\u0002\u001a\u0014\u0017\u0005\u0019\u000b\u0000\u001b\u0012\u000e\u0011\u0003\r\t", (byte) ((android.view.ViewConfiguration.getWindowTouchSlop() >> 8) + 22), r13);
        r9.z(((java.lang.String) r13[r6]).intern());
        r12 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001", new int[]{com.esotericsoftware.asm.Opcodes.F2L, 22, com.esotericsoftware.asm.Opcodes.GETSTATIC, 12}, r6, r12);
        r9.z(((java.lang.String) r12[r6]).intern());
        r12 = new java.lang.Object[r5];
        g("\u0001\u0000\u0001", new int[]{com.esotericsoftware.asm.Opcodes.IF_ICMPGE, 3, 50, r6}, r5, r12);
        r9.D(((java.lang.String) r12[r6]).intern());
        r13 = new java.lang.Object[r5];
        h(android.text.TextUtils.lastIndexOf(r4, '0') + 3, r3, (byte) (86 - (android.view.ViewConfiguration.getTouchSlop() >> 8)), r13);
        r10 = ((java.lang.String) r13[r6]).intern();
        r11 = new java.lang.Object[r5];
        h((android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)) + 10, "\r\u0016\u0011\u0001\u0005\u001b\b\u0013\u0013\u0003㗿", (byte) (53 - android.graphics.Color.blue(r6)), r11);
        new o.dh.c(r10, r9.B(((java.lang.String) r11[r6]).intern()));
        r11 = new java.lang.Object[r5];
        g(r2, new int[]{com.esotericsoftware.asm.Opcodes.IF_ACMPEQ, 2, r6, r5}, r6, r11);
        r11 = ((java.lang.String) r11[r6]).intern();
        r10 = new java.lang.Object[r5];
        h(10 - android.text.TextUtils.lastIndexOf(r4, '0'), "\r\u0016\u0011\u0001\u0005\u001b\b\u0013\u001b\u000f㙁", (byte) (107 - (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16)), r10);
        new o.dh.c(r11, r9.B(((java.lang.String) r10[r6]).intern()));
        r12 = new java.lang.Object[r5];
        h((android.view.ViewConfiguration.getTouchSlop() >> 8) + 10, "\u000f\u0000\u0007\u0014\u000f\u000e\u0015\u0016\u0004\u0007", (byte) (52 - android.graphics.Color.green(r6)), r12);
        r8 = r9.z(((java.lang.String) r12[r6]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x02f8, code lost:
    
        if (r8 == null) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x02fa, code lost:
    
        r15 = new java.lang.Object[r5];
        h((android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 2, r3, (byte) (86 - android.text.TextUtils.indexOf(r4, r4, r6)), r15);
        new o.dh.c(((java.lang.String) r15[r6]).intern(), r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x031c, code lost:
    
        r12 = new java.lang.Object[r5];
        g("\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000", new int[]{com.esotericsoftware.asm.Opcodes.GOTO, 10, com.esotericsoftware.asm.Opcodes.FNEG, r6}, r6, r12);
        r8 = r9.z(((java.lang.String) r12[r6]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0337, code lost:
    
        if (r8 == null) goto L29;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x0339, code lost:
    
        r12 = new java.lang.Object[r5];
        g(r2, new int[]{com.esotericsoftware.asm.Opcodes.IF_ACMPEQ, 2, r6, r5}, r6, r12);
        new o.dh.c(((java.lang.String) r12[r6]).intern(), r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0352, code lost:
    
        r8 = o.cl.d.c + 69;
        o.cl.d.e = r8 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x035c, code lost:
    
        if ((r8 % 2) == 0) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x035e, code lost:
    
        r8 = 28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x0363, code lost:
    
        switch(r8) {
            case 26: goto L29;
            default: goto L29;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0361, code lost:
    
        r8 = 26;
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x0366, code lost:
    
        r13 = new java.lang.Object[r5];
        h((android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 11, "\u0006\u0019\u0017\u0019\u0003\f\b\u0013\u0013\u0003㗫", (byte) (android.graphics.Color.blue(r6) + 33), r13);
        r8 = r9.z(((java.lang.String) r13[r6]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x0388, code lost:
    
        if (r8 == null) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x038a, code lost:
    
        r14 = new java.lang.Object[r5];
        h((android.os.Process.myTid() >> 22) + 2, r3, (byte) ((android.view.ViewConfiguration.getJumpTapTimeout() >> 16) + 86), r14);
        new o.dh.c(((java.lang.String) r14[r6]).intern(), r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x03af, code lost:
    
        r12 = new java.lang.Object[r5];
        h(android.widget.ExpandableListView.getPackedPositionGroup(0) + 11, "\u0006\u0019\u0017\u0019\u0003\f\b\u0013\u001b\u000f㙍", (byte) (android.view.View.MeasureSpec.getMode(r6) + com.esotericsoftware.asm.Opcodes.DNEG), r12);
        r3 = r9.z(((java.lang.String) r12[r6]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x03cf, code lost:
    
        if (r3 == null) goto L35;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x03d1, code lost:
    
        r12 = new java.lang.Object[r5];
        g(r2, new int[]{com.esotericsoftware.asm.Opcodes.IF_ACMPEQ, 2, r6, r5}, r6, r12);
        new o.dh.c(((java.lang.String) r12[r6]).intern(), r3);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x03ea, code lost:
    
        r10 = new java.lang.Object[r5];
        h((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 3, "\u0002\u000f㘢", (byte) (47 - (android.util.TypedValue.complexToFraction(r6, 0.0f, 0.0f) > 0.0f ? 1 : (android.util.TypedValue.complexToFraction(r6, 0.0f, 0.0f) == 0.0f ? 0 : -1))), r10);
        r9.z(((java.lang.String) r10[r6]).intern());
        r8 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000", new int[]{com.esotericsoftware.asm.Opcodes.RETURN, 3, r6, r5}, r6, r8);
        r9.z(((java.lang.String) r8[r6]).intern());
        r8 = new java.lang.Object[r5];
        h(13 - android.os.Process.getGidForName(r4), "\u0006\u001c\u000e\u001b\u0019\r\u0004\u0005\u001f\u0019\u0019\u0016\r\u0019", (byte) (33 - android.text.TextUtils.getOffsetBefore(r4, r6)), r8);
        r0.b(((java.lang.String) r8[r6]).intern());
        r4 = new java.lang.Object[r5];
        g("\u0001\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{com.esotericsoftware.asm.Opcodes.GETFIELD, 7, r6, 4}, r5, r4);
        r2 = c(r0.s(((java.lang.String) r4[r6]).intern()));
        r3 = r7.iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x046b, code lost:
    
        r4 = o.cl.d.c + 51;
        o.cl.d.e = r4 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0475, code lost:
    
        if ((r4 % 2) == 0) goto L39;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0477, code lost:
    
        r15 = '_';
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x047c, code lost:
    
        switch(r15) {
            case 47: goto L41;
            default: goto L41;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0483, code lost:
    
        if (r3.hasNext() == false) goto L70;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0485, code lost:
    
        ((o.et.e) r3.next()).a(r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x048f, code lost:
    
        r1.d(r7, r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0492, code lost:
    
        return r7;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x047a, code lost:
    
        r15 = '/';
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x0197, code lost:
    
        r12 = 28;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r5v0 */
    /* JADX WARN: Type inference failed for: r5v5 */
    /* JADX WARN: Type inference failed for: r6v0 */
    /* JADX WARN: Type inference failed for: r6v4 */
    @Override // o.cc.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public java.util.List<T> a(java.lang.String r26, java.lang.String r27, int r28, java.lang.String r29, o.eg.b r30) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1494
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cl.d.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<o.cc.a> c(o.eg.e r11) throws o.ei.i, o.eg.d {
        /*
            Method dump skipped, instructions count: 270
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cl.d.c(o.eg.e):java.util.List");
    }

    public T c(String str, String str2, int i, String str3) {
        int i2 = c + 51;
        e = i2 % 128;
        int i3 = i2 % 2;
        T e2 = e(str, str2, i, str3);
        int i4 = c + 99;
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 86 / 0;
                return e2;
            default:
                return e2;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v26, types: [byte[]] */
    private static void g(String str, int[] iArr, boolean z, Object[] objArr) {
        int length;
        char[] cArr;
        int i;
        char[] cArr2;
        ?? r0 = str;
        switch (r0 != 0 ? '=' : '4') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i2 = 0;
        int i3 = iArr[0];
        int i4 = 1;
        int i5 = iArr[1];
        int i6 = iArr[2];
        int i7 = iArr[3];
        char[] cArr3 = a;
        long j = 0;
        if (cArr3 != null) {
            int i8 = $10 + Opcodes.DMUL;
            $11 = i8 % 128;
            if (i8 % 2 == 0) {
                length = cArr3.length;
                cArr = new char[length];
                i = 0;
            } else {
                length = cArr3.length;
                cArr = new char[length];
                i = 0;
            }
            while (true) {
                switch (i < length ? i2 : i4) {
                    case 0:
                        try {
                            Object[] objArr2 = new Object[i4];
                            objArr2[i2] = Integer.valueOf(cArr3[i]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj != null) {
                                cArr2 = cArr3;
                            } else {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 11, (char) (CdmaCellLocation.convertQuartSecToDecDegrees(i2) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i2) == 0.0d ? 0 : -1)), 44 - (ViewConfiguration.getZoomControlsTimeout() > j ? 1 : (ViewConfiguration.getZoomControlsTimeout() == j ? 0 : -1)));
                                byte b2 = (byte) i2;
                                byte b3 = (byte) (b2 - 1);
                                cArr2 = cArr3;
                                Object[] objArr3 = new Object[1];
                                i(b2, b3, (byte) (b3 & 54), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr[i] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i++;
                            cArr3 = cArr2;
                            i2 = 0;
                            i4 = 1;
                            j = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        cArr3 = cArr;
                        break;
                }
            }
        }
        char[] cArr4 = new char[i5];
        System.arraycopy(cArr3, i3, cArr4, 0, i5);
        if (bArr != null) {
            char[] cArr5 = new char[i5];
            lVar.d = 0;
            char c2 = 0;
            while (lVar.d < i5) {
                switch (bArr[lVar.d] != 1) {
                    case true:
                        int i9 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                            Object obj2 = o.e.a.s.get(804049217);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 11, (char) Gravity.getAbsoluteGravity(0, 0), 207 - (KeyEvent.getMaxKeyCode() >> 16));
                                byte b4 = (byte) 0;
                                byte b5 = (byte) (b4 - 1);
                                Object[] objArr5 = new Object[1];
                                i(b4, b5, (byte) (b5 & 56), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(804049217, obj2);
                            }
                            cArr5[i9] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            int i10 = $11 + Opcodes.LSHR;
                            $10 = i10 % 128;
                            int i11 = i10 % 2;
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        int i12 = $11 + 15;
                        $10 = i12 % 128;
                        if (i12 % 2 != 0) {
                            int i13 = lVar.d;
                            try {
                                Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj3 = o.e.a.s.get(2016040108);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - (KeyEvent.getMaxKeyCode() >> 16), (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 448 - (ViewConfiguration.getScrollDefaultDelay() >> 16));
                                    byte b6 = (byte) 0;
                                    byte b7 = (byte) (b6 - 1);
                                    Object[] objArr7 = new Object[1];
                                    i(b6, b7, (byte) (b7 & 53), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj3);
                                }
                                cArr5[i13] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                int i14 = 80 / 0;
                                break;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        } else {
                            int i15 = lVar.d;
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c2)};
                                Object obj4 = o.e.a.s.get(2016040108);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 10, (char) (ViewConfiguration.getTouchSlop() >> 8), 448 - (ViewConfiguration.getTapTimeout() >> 16));
                                    byte b8 = (byte) 0;
                                    byte b9 = (byte) (b8 - 1);
                                    Object[] objArr9 = new Object[1];
                                    i(b8, b9, (byte) (b9 & 53), objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(2016040108, obj4);
                                }
                                cArr5[i15] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                break;
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                }
                c2 = cArr5[lVar.d];
                try {
                    Object[] objArr10 = {lVar, lVar};
                    Object obj5 = o.e.a.s.get(-2112603350);
                    if (obj5 == null) {
                        Class cls5 = (Class) o.e.a.c(11 - TextUtils.getOffsetAfter("", 0), (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 258);
                        byte b10 = (byte) 0;
                        byte b11 = (byte) (b10 - 1);
                        Object[] objArr11 = new Object[1];
                        i(b10, b11, (byte) (b11 + 1), objArr11);
                        obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                        o.e.a.s.put(-2112603350, obj5);
                    }
                    ((Method) obj5).invoke(null, objArr10);
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
            }
            cArr4 = cArr5;
        }
        if (i7 > 0) {
            char[] cArr6 = new char[i5];
            System.arraycopy(cArr4, 0, cArr6, 0, i5);
            int i16 = i5 - i7;
            System.arraycopy(cArr6, 0, cArr4, i16, i7);
            System.arraycopy(cArr6, i7, cArr4, 0, i16);
        }
        switch (z ? (char) 25 : '3') {
            case 25:
                char[] cArr7 = new char[i5];
                lVar.d = 0;
                while (lVar.d < i5) {
                    cArr7[lVar.d] = cArr4[(i5 - lVar.d) - 1];
                    lVar.d++;
                }
                cArr4 = cArr7;
                break;
        }
        if (i6 > 0) {
            int i17 = $11 + Opcodes.DMUL;
            $10 = i17 % 128;
            int i18 = i17 % 2 != 0 ? 0 : 0;
            while (true) {
                lVar.d = i18;
                if (lVar.d < i5) {
                    cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                    i18 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr4);
    }

    private static void h(int i, String str, byte b2, Object[] objArr) {
        char[] charArray;
        int i2;
        int i3 = $10 + 63;
        $11 = i3 % 128;
        if (i3 % 2 == 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (str != null ? (char) 31 : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                charArray = str;
                break;
            default:
                charArray = str.toCharArray();
                break;
        }
        char[] cArr = charArray;
        m mVar = new m();
        char[] cArr2 = b;
        long j = 0;
        if (cArr2 != null) {
            int length = cArr2.length;
            char[] cArr3 = new char[length];
            int i4 = 0;
            while (i4 < length) {
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr2[i4])};
                    Object obj2 = o.e.a.s.get(-1401577988);
                    if (obj2 == null) {
                        Class cls = (Class) o.e.a.c(17 - View.MeasureSpec.getSize(0), (char) View.MeasureSpec.makeMeasureSpec(0, 0), (SystemClock.elapsedRealtimeNanos() > j ? 1 : (SystemClock.elapsedRealtimeNanos() == j ? 0 : -1)) + 75);
                        byte b3 = (byte) 0;
                        byte b4 = (byte) (b3 - 1);
                        Object[] objArr3 = new Object[1];
                        i(b3, b4, (byte) (b4 & 7), objArr3);
                        obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj2);
                    }
                    cArr3[i4] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                    i4++;
                    j = 0;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr2 = cArr3;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(d)};
            Object obj3 = o.e.a.s.get(-1401577988);
            if (obj3 == null) {
                Class cls2 = (Class) o.e.a.c((-16777199) - Color.rgb(0, 0, 0), (char) TextUtils.getOffsetBefore("", 0), 76 - (ViewConfiguration.getMinimumFlingVelocity() >> 16));
                byte b5 = (byte) 0;
                byte b6 = (byte) (b5 - 1);
                Object[] objArr5 = new Object[1];
                i(b5, b6, (byte) (b6 & 7), objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1401577988, obj3);
            }
            char charValue = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
            char[] cArr4 = new char[i];
            if (i % 2 != 0) {
                i2 = i - 1;
                cArr4[i2] = (char) (cArr[i2] - b2);
            } else {
                i2 = i;
            }
            switch (i2 > 1) {
                case true:
                    int i5 = $11 + 63;
                    $10 = i5 % 128;
                    if (i5 % 2 != 0) {
                        mVar.b = 1;
                    } else {
                        mVar.b = 0;
                    }
                    while (mVar.b < i2) {
                        int i6 = $10 + 69;
                        $11 = i6 % 128;
                        int i7 = i6 % 2;
                        mVar.e = cArr[mVar.b];
                        mVar.a = cArr[mVar.b + 1];
                        if (mVar.e == mVar.a) {
                            cArr4[mVar.b] = (char) (mVar.e - b2);
                            cArr4[mVar.b + 1] = (char) (mVar.a - b2);
                        } else {
                            try {
                                Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                Object obj4 = o.e.a.s.get(696901393);
                                if (obj4 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (char) (8857 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), 325 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                                    byte b7 = (byte) 0;
                                    byte b8 = (byte) (b7 - 1);
                                    Object[] objArr7 = new Object[1];
                                    i(b7, b8, (byte) (b8 + 4), objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                    o.e.a.s.put(696901393, obj4);
                                }
                                switch (((Integer) ((Method) obj4).invoke(null, objArr6)).intValue() == mVar.h) {
                                    case false:
                                        if (mVar.c != mVar.d) {
                                            int i8 = (mVar.c * charValue) + mVar.h;
                                            int i9 = (mVar.d * charValue) + mVar.i;
                                            cArr4[mVar.b] = cArr2[i8];
                                            cArr4[mVar.b + 1] = cArr2[i9];
                                            break;
                                        } else {
                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                            int i10 = (mVar.c * charValue) + mVar.i;
                                            int i11 = (mVar.d * charValue) + mVar.h;
                                            cArr4[mVar.b] = cArr2[i10];
                                            cArr4[mVar.b + 1] = cArr2[i11];
                                            break;
                                        }
                                    default:
                                        int i12 = $11 + 13;
                                        $10 = i12 % 128;
                                        if (i12 % 2 != 0) {
                                        }
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj5 = o.e.a.s.get(1075449051);
                                            if (obj5 == null) {
                                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 11, (char) TextUtils.indexOf("", ""), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 65);
                                                byte b9 = (byte) 0;
                                                Object[] objArr9 = new Object[1];
                                                i(b9, (byte) (b9 - 1), (byte) $$a.length, objArr9);
                                                obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj5);
                                            }
                                            int intValue = ((Integer) ((Method) obj5).invoke(null, objArr8)).intValue();
                                            int i13 = (mVar.d * charValue) + mVar.h;
                                            cArr4[mVar.b] = cArr2[intValue];
                                            cArr4[mVar.b + 1] = cArr2[i13];
                                            break;
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                }
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        mVar.b += 2;
                    }
                    break;
            }
            int i14 = 0;
            while (true) {
                switch (i14 < i) {
                    case false:
                        objArr[0] = new String(cArr4);
                        return;
                    default:
                        cArr4[i14] = (char) (cArr4[i14] ^ 13722);
                        i14++;
                }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
