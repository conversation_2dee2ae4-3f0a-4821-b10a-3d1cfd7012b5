package o.bz;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.a.f;
import o.bm.e;
import o.ee.g;
import o.eg.b;
import o.ei.c;
import o.h.d;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bz\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int e;
    private static int f;
    private static int g;
    private static short[] h;
    private static byte[] i;
    private static int j;
    e b;
    private final Context c;
    private final c d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        f = 1;
        c();
        ExpandableListView.getPackedPositionChild(0L);
        View.getDefaultSize(0, 0);
        Gravity.getAbsoluteGravity(0, 0);
        ViewConfiguration.getMaximumFlingVelocity();
        ViewConfiguration.getMinimumFlingVelocity();
        int i2 = g + 5;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        i = new byte[]{37, -121, -42, -114, -39, -121, -96, -92, -37, -71, -41, -64, -79, -86, -115, -127, -44, -33, -114, -125, -117, -43, -39, 101, 62, 25, -19, 126, 107, 24, -17, 23, 97, 101, 49, 64, 96, ByteCompanionObject.MAX_VALUE, 19, ByteCompanionObject.MAX_VALUE, 27, 51, 85, 11, 66, 89, 88, 87, 54, 34, -56, Base64.padSymbol, -51, -59, 34, -125, 99, 54, -49, 60, -64, 55, 62, -15, 54, -59, UtilitiesSDKConstants.SRP_LABEL_MAC, Base64.padSymbol, 12, 56, -52, 58, -15, 56, -14, -125, 101, -52, -16, -121, 100, -53, -120, 101, -58, 56, -15, -54, -53, Base64.padSymbol, -36, -56, 27, -18, -35, -54, -62, 20, Tnaf.POW_2_WIDTH, -92, 59, -53, 21, 22, -34, 38, 80, 92, -55, -34, 47, 90, 82, -56, -60, 56, -1, -64, 4, -25, -4, 34, -52, -56, -58, 91, 46, -34, -34, 47, 46, -41, -61, -14, -59, -40, -63, -39, -49, -53, -65, -27, -49, -40, -59, -85, -16, -12, -59, -40, -63, -51, -58, -49, -122, 31, -35, -14, -16, -13, -39, -16, -116, 55, -69, -60, -35, -86, -59, -42, -38, -48, -81};
        a = 909053683;
        j = 292061957;
        e = -1820274333;
    }

    static void init$0() {
        $$a = new byte[]{116, -79, 3, -53};
        $$b = 209;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 3 - r8
            byte[] r0 = o.bz.a.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r7 = r7 * 2
            int r7 = 110 - r7
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r7
            int r8 = r8 + 1
            r1[r3] = r4
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bz.a.l(int, int, short, java.lang.Object[]):void");
    }

    public a(Context context, e eVar, c cVar) {
        this.c = context;
        this.b = eVar;
        this.d = cVar;
    }

    public final void e(d dVar, o.c.a aVar) throws WalletValidationException {
        int i2 = f + 91;
        g = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((byte) (View.MeasureSpec.makeMeasureSpec(0, 0) - 21), Color.alpha(0) + 1515200525, (short) (ExpandableListView.getPackedPositionChild(0L) - 48), (-100) - (ViewConfiguration.getTapTimeout() >> 16), (-659000658) - TextUtils.indexOf("", "", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((byte) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 74), 1515200550 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) (ExpandableListView.getPackedPositionChild(0L) - 59), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 100, View.getDefaultSize(0, 0) - 659000626, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (!this.d.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            k((byte) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 99), TextUtils.getTrimmedLength("") + 1515200566, (short) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 53), (-100) - TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 659000638, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((byte) ((-35) - (ViewConfiguration.getTapTimeout() >> 16)), TextUtils.indexOf((CharSequence) "", '0', 0) + 1515200573, (short) ((-16777092) - Color.rgb(0, 0, 0)), (KeyEvent.getMaxKeyCode() >> 16) - 100, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 659000639, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.bm.e(this.c, new e.b() { // from class: o.bz.a.5
            private static int $10 = 0;
            private static int $11 = 1;
            private static int g = 0;
            private static int i = 1;
            private static char c = 30409;
            private static char d = 28323;
            private static char a = 58874;
            private static char e = 49773;

            @Override // o.bm.e.b
            public final void d() {
                int i4 = g + Opcodes.LMUL;
                i = i4 % 128;
                int i5 = i4 % 2;
                switch (a.this.b != null ? (char) 17 : (char) 22) {
                    case 22:
                        break;
                    default:
                        int i6 = g + Opcodes.LUSHR;
                        i = i6 % 128;
                        int i7 = i6 % 2;
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f("帣㿶ꃈ솪銫詆⫵匤斀ଓ큚⬻\u2efc⣄\ue0d3䀺쮛㗟\uff00⏖䚸볽\u1978됁", 24 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        f("尛虍\uff00⏖폞ꅒ帣㿶ꃈ솪銫詆⫵匤斀ଓ誐鸗犟ꠄ慵송뺜︂萇숱䍡\uf6fd為褹\u0d84輖㐦쇷\u2efc⣄\ue0d3䀺롏斾饱쒸帬扜\ue1e4䑐蚅∓\ue039ꙗ쀅肪", 52 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        o.j.c.c();
                        a.this.b.l();
                        break;
                }
            }

            @Override // o.bm.e.b
            public final void c(o.bb.d dVar2) {
                switch (a.this.b != null ? '\\' : (char) 3) {
                    case 3:
                        break;
                    default:
                        int i4 = g + 49;
                        i = i4 % 128;
                        int i5 = i4 % 2;
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f("帣㿶ꃈ솪銫詆⫵匤斀ଓ큚⬻\u2efc⣄\ue0d3䀺쮛㗟\uff00⏖䚸볽\u1978됁", 23 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        f("尛虍\uff00⏖폞ꅒ帣㿶ꃈ솪銫詆⫵匤斀ଓ誐鸗犟ꠄ慵송뺜︂萇숱䍡\uf6fd為褹\u0d84輖㐦쇷\u2efc⣄\ue0d3䀺롏斾饱쒸帬扜䞐୮碊쓿寵䛪ݷ⮌", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 52, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        a.this.b.a(dVar2);
                        int i6 = g + 29;
                        i = i6 % 128;
                        if (i6 % 2 != 0) {
                            break;
                        }
                        break;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0018. Please report as an issue. */
            private static void f(java.lang.String r21, int r22, java.lang.Object[] r23) {
                /*
                    Method dump skipped, instructions count: 566
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.bz.a.AnonymousClass5.f(java.lang.String, int, java.lang.Object[]):void");
            }
        }, this.d).d(dVar, aVar, new o.bm.d() { // from class: o.bz.a.2
            private static int e = 0;
            private static int b = 1;

            @Override // o.bm.d
            public final b a() throws o.eg.d {
                int i4 = b + 1;
                int i5 = i4 % 128;
                e = i5;
                Object obj = null;
                switch (i4 % 2 == 0) {
                    case true:
                        int i6 = (i5 + 58) - 1;
                        b = i6 % 128;
                        switch (i6 % 2 == 0 ? '\b' : 'S') {
                            case Opcodes.AASTORE /* 83 */:
                                return null;
                            default:
                                int i7 = 58 / 0;
                                return null;
                        }
                    default:
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.bm.d
            public final o.bb.d a(o.bb.d dVar2) {
                int i4 = e;
                int i5 = (i4 ^ 21) + ((i4 & 21) << 1);
                b = i5 % 128;
                switch (i5 % 2 == 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return dVar2;
                }
            }
        });
        int i4 = g + 39;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? '+' : '\'') {
            case '+':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void d(d dVar) throws WalletValidationException {
        int i2 = f + 79;
        g = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((byte) ((-21) - View.combineMeasuredStates(0, 0)), 1515200525 - View.resolveSize(0, 0), (short) ((-49) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), (-101) - MotionEvent.axisFromString(""), (-659000658) - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((byte) (102 - MotionEvent.axisFromString("")), 1515200614 - TextUtils.indexOf("", ""), (short) (TextUtils.getOffsetBefore("", 0) + 20), View.MeasureSpec.getSize(0) - 100, (-659000626) - TextUtils.indexOf("", "", 0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (!this.d.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            k((byte) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 98), 1515200566 - Drawable.resolveOpacity(0, 0), (short) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 51), (-100) - KeyEvent.keyCodeFromString(""), (-659000638) - (ViewConfiguration.getPressedStateDuration() >> 16), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((byte) (ImageFormat.getBitsPerPixel(0) - 34), 1515200572 - (ViewConfiguration.getTapTimeout() >> 16), (short) (124 - (Process.myTid() >> 22)), (-100) - TextUtils.indexOf("", "", 0, 0), View.resolveSizeAndState(0, 0, 0) - 659000638, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.bm.e(this.c, new e.b() { // from class: o.bz.a.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int c;
            private static int d;
            private static char[] e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                c = 0;
                d = 1;
                e = new char[]{50932, 50854, 50859, 50857, 50858, 50858, 50842, 50836, 50849, 50855, 50863, 50855, 50863, 50857, 50850, 50859, 50856, 50851, 50876, 50852, 50857, 50857, 50854, 50839, 50940, 50856, 50859, 50857, 50858, 50842, 50839, 50854, 50857, 50857, 50852, 50876, 50851, 50856, 50859, 50850, 50820, 50923, 50923, 50828, 50863, 50859, 50849, 50858, 50860, 50863, 50858, 50824, 50912, 50912, 50826, 50851, 50860, 50863, 50855, 50863, 50855, 50849, 50841, 50836, 50851, 50855, 50858, 50853, 50832, 50857, 50849, 50862, 50857, 50849, 50878, 50764, 51145, 51144, 51150, 51147, 50747, 50740, 51143, 51150, 51150, 51141, 51165, 51136, 51145, 51144, 51139, 50725, 50696, 50696, 50733, 51148, 51144, 51142, 51147, 51149, 51148, 51147, 50729, 50689, 50689, 50731, 51136, 51149, 51148, 51140, 51148, 51140, 51142, 50750, 50741, 51136, 51140, 51147, 51146, 50744, 50751, 51145, 51140, 51138, 51167, 51143, 50732, 50689, 50689, 50727, 51166, 51138, 51136, 51140, 50735, 50733, 51140, 51143, 51150, 51150, 51141, 51165, 51136, 51145, 51144, 51139, 50936, 50824, 50912, 50912, 50826, 50851, 50860, 50863, 50855, 50863, 50855, 50849, 50841, 50836, 50851, 50855, 50858, 50853, 50843, 50846, 50856, 50855, 50877, 50878, 50854, 50831, 50912, 50912, 50826, 50876, 50851, 50859, 50854, 50820, 50831, 50854, 50879, 50877, 50877, 50855, 50856, 50859, 50857, 50858, 50842, 50839, 50854, 50857, 50857, 50852, 50876, 50851, 50856, 50859, 50850, 50820, 50923, 50923, 50828, 50863, 50859, 50849, 50858, 50860, 50863};
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(short r6, int r7, short r8, java.lang.Object[] r9) {
                /*
                    int r8 = r8 * 2
                    int r8 = r8 + 1
                    int r7 = r7 + 66
                    byte[] r0 = o.bz.a.AnonymousClass3.$$a
                    int r6 = r6 * 3
                    int r6 = r6 + 4
                    byte[] r1 = new byte[r8]
                    int r8 = r8 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r7 = r6
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L35
                L1a:
                    r3 = r2
                L1b:
                    byte r4 = (byte) r7
                    r1[r3] = r4
                    if (r3 != r8) goto L28
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L28:
                    r4 = r0[r6]
                    int r3 = r3 + 1
                    r5 = r7
                    r7 = r6
                    r6 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    r8 = r5
                L35:
                    int r6 = -r6
                    int r7 = r7 + 1
                    int r6 = r6 + r8
                    r8 = r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r7
                    r7 = r6
                    r6 = r5
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.bz.a.AnonymousClass3.f(short, int, short, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{40, 24, -45, -26};
                $$b = Opcodes.RET;
            }

            /* JADX WARN: Failed to find 'out' block for switch in B:10:0x005f. Please report as an issue. */
            @Override // o.bm.e.b
            public final void d() {
                int i4 = c + 45;
                d = i4 % 128;
                if (i4 % 2 == 0) {
                    e eVar = a.this.b;
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                switch (a.this.b != null ? 'X' : 'W') {
                    case Opcodes.POP /* 87 */:
                        return;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        a("\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, true, objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        a("\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{24, 51, 0, 0}, false, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        a.this.b.o();
                        int i5 = d + 51;
                        c = i5 % 128;
                        switch (i5 % 2 != 0) {
                        }
                        return;
                }
            }

            @Override // o.bm.e.b
            public final void c(o.bb.d dVar2) {
                String intern3;
                Object obj;
                String intern4;
                Object obj2;
                int i4 = c + Opcodes.LREM;
                d = i4 % 128;
                switch (i4 % 2 == 0 ? 'I' : Typography.less) {
                    case '<':
                        if (dVar2.d() == o.bb.a.al) {
                            switch (a.this.b == null) {
                                case true:
                                    return;
                                default:
                                    int i5 = d + Opcodes.DSUB;
                                    c = i5 % 128;
                                    if (i5 % 2 != 0) {
                                        g.c();
                                        Object[] objArr5 = new Object[1];
                                        a("\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, false, objArr5);
                                        intern4 = ((String) objArr5[0]).intern();
                                        Object[] objArr6 = new Object[1];
                                        a("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{75, 71, Opcodes.IF_ICMPEQ, 0}, true, objArr6);
                                        obj2 = objArr6[0];
                                    } else {
                                        g.c();
                                        Object[] objArr7 = new Object[1];
                                        a("\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, true, objArr7);
                                        intern4 = ((String) objArr7[0]).intern();
                                        Object[] objArr8 = new Object[1];
                                        a("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001", new int[]{75, 71, Opcodes.IF_ICMPEQ, 0}, false, objArr8);
                                        obj2 = objArr8[0];
                                    }
                                    g.d(intern4, ((String) obj2).intern());
                                    a.this.b.k();
                                    return;
                            }
                        }
                        if (a.this.b != null) {
                            int i6 = c + 15;
                            d = i6 % 128;
                            if (i6 % 2 == 0) {
                                g.c();
                                Object[] objArr9 = new Object[1];
                                a("\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, false, objArr9);
                                intern3 = ((String) objArr9[0]).intern();
                                Object[] objArr10 = new Object[1];
                                a("\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000", new int[]{Opcodes.I2C, 65, 0, 39}, false, objArr10);
                                obj = objArr10[0];
                            } else {
                                g.c();
                                Object[] objArr11 = new Object[1];
                                a("\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001", new int[]{0, 24, 0, 0}, true, objArr11);
                                intern3 = ((String) objArr11[0]).intern();
                                Object[] objArr12 = new Object[1];
                                a("\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000", new int[]{Opcodes.I2C, 65, 0, 39}, false, objArr12);
                                obj = objArr12[0];
                            }
                            g.d(intern3, ((String) obj).intern());
                            a.this.b.e(dVar2);
                            return;
                        }
                        return;
                    default:
                        dVar2.d();
                        o.bb.a aVar = o.bb.a.al;
                        throw null;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:122:0x0374, code lost:
            
                r2 = r0;
             */
            /* JADX WARN: Code restructure failed: missing block: B:57:0x018e, code lost:
            
                if (r0[r1.d] == 1) goto L68;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void a(java.lang.String r19, int[] r20, boolean r21, java.lang.Object[] r22) {
                /*
                    Method dump skipped, instructions count: 986
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.bz.a.AnonymousClass3.a(java.lang.String, int[], boolean, java.lang.Object[]):void");
            }
        }, this.d).d(dVar, null, new o.bm.d() { // from class: o.bz.a.4
            private static int d = 0;
            private static int a = 1;

            @Override // o.bm.d
            public final b a() throws o.eg.d {
                int i4 = d;
                int i5 = ((i4 | Opcodes.LSHR) << 1) - (i4 ^ Opcodes.LSHR);
                a = i5 % 128;
                Object obj = null;
                switch (i5 % 2 == 0) {
                    case false:
                        return null;
                    default:
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.bm.d
            public final o.bb.d a(o.bb.d dVar2) {
                int i4 = d;
                int i5 = (i4 + 2) - 1;
                a = i5 % 128;
                int i6 = i5 % 2;
                int i7 = i4 + 75;
                a = i7 % 128;
                switch (i7 % 2 == 0 ? '1' : '^') {
                    case Opcodes.DUP2_X2 /* 94 */:
                        return dVar2;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }
        });
        int i4 = f + 79;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:17:0x00d0, code lost:
    
        if (r7 == null) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x00d2, code lost:
    
        new o.bm.e(r18.c, new o.bz.a.AnonymousClass1(r18), r18.d).d(r19, new o.c.a(r7), new o.bz.a.AnonymousClass7(r18));
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00ef, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00f0, code lost:
    
        r6 = fr.antelop.sdk.exception.WalletValidationErrorCode.Unexpected;
        r2 = new java.lang.Object[1];
        k((byte) (android.view.KeyEvent.getDeadChar(0, 0) - 11), 1515200655 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), (short) ((-85) - (android.view.ViewConfiguration.getScrollBarFadeDuration() >> 16)), (-16777316) - android.graphics.Color.rgb(0, 0, 0), (-659000659) - android.text.TextUtils.lastIndexOf("", '0'), r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0131, code lost:
    
        throw new fr.antelop.sdk.exception.WalletValidationException(r6, ((java.lang.String) r2[0]).intern());
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void b(o.h.d r19) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 436
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bz.a.b(o.h.d):void");
    }

    public final void b() {
        int i2 = g + 99;
        f = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((byte) (Color.alpha(0) - 21), TextUtils.lastIndexOf("", '0', 0) + 1515200526, (short) ((-49) - (ViewConfiguration.getLongPressTimeout() >> 16)), (-101) - ImageFormat.getBitsPerPixel(0), (-659000657) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((byte) ((-14) - Color.green(0)), (ViewConfiguration.getPressedStateDuration() >> 16) + 1515200688, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 73), TextUtils.indexOf((CharSequence) "", '0') - 99, Drawable.resolveOpacity(0, 0) - 659000625, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.b = null;
        int i4 = f + 7;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    private static void k(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(a)};
            Object obj = o.e.a.s.get(-2120899312);
            long j2 = 0;
            if (obj == null) {
                Class cls = (Class) o.e.a.c(11 - Drawable.resolveOpacity(0, 0), (char) View.resolveSizeAndState(0, 0, 0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 66);
                byte b2 = (byte) 0;
                byte b3 = (byte) (b2 + 1);
                Object[] objArr3 = new Object[1];
                l(b2, b3, (byte) (b3 - 1), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z2 = intValue == -1;
            if (z2) {
                byte[] bArr = i;
                if (bArr != null) {
                    int length = bArr.length;
                    byte[] bArr2 = new byte[length];
                    int i5 = 0;
                    while (i5 < length) {
                        try {
                            Object[] objArr4 = {Integer.valueOf(bArr[i5])};
                            Object obj2 = o.e.a.s.get(494867332);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 19, (char) ((ViewConfiguration.getZoomControlsTimeout() > j2 ? 1 : (ViewConfiguration.getZoomControlsTimeout() == j2 ? 0 : -1)) + 16424), TextUtils.getOffsetBefore("", 0) + Opcodes.FCMPG);
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                Object[] objArr5 = new Object[1];
                                l(b4, b5, b5, objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                o.e.a.s.put(494867332, obj2);
                            }
                            bArr2[i5] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                            i5++;
                            j2 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    bArr = bArr2;
                }
                if (bArr != null) {
                    byte[] bArr3 = i;
                    try {
                        Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(e)};
                        Object obj3 = o.e.a.s.get(-2120899312);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), View.resolveSize(0, 0) + 65);
                            byte b6 = (byte) 0;
                            byte b7 = (byte) (b6 + 1);
                            Object[] objArr7 = new Object[1];
                            l(b6, b7, (byte) (b7 - 1), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-2120899312, obj3);
                        }
                        intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (a ^ (-5810760824076169584L))));
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } else {
                    intValue = (short) (((short) (h[i2 + ((int) (e ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (a ^ (-5810760824076169584L))));
                }
            }
            if (intValue > 0) {
                fVar.d = ((i2 + intValue) - 2) + ((int) (e ^ (-5810760824076169584L))) + (z2 ? 1 : 0);
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 12, (char) Color.red(0), ((Process.getThreadPriority(0) + 20) >> 6) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = i;
                    if (bArr4 != null) {
                        int i6 = $10 + 93;
                        $11 = i6 % 128;
                        int i7 = i6 % 2;
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        int i8 = 0;
                        while (i8 < length2) {
                            int i9 = $11 + Opcodes.DNEG;
                            $10 = i9 % 128;
                            if (i9 % 2 != 0) {
                                bArr5[i8] = (byte) (bArr4[i8] & (-5810760824076169584L));
                                i8 >>= 1;
                            } else {
                                bArr5[i8] = (byte) (bArr4[i8] ^ (-5810760824076169584L));
                                i8++;
                            }
                        }
                        bArr4 = bArr5;
                    }
                    switch (bArr4 != null ? '\'' : '\t') {
                        case '\t':
                            z = false;
                            break;
                        default:
                            z = true;
                            break;
                    }
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        int i10 = $10 + 87;
                        int i11 = i10 % 128;
                        $11 = i11;
                        int i12 = i10 % 2;
                        switch (z) {
                            case false:
                                short[] sArr = h;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                                break;
                            default:
                                int i13 = i11 + 17;
                                $10 = i13 % 128;
                                if (i13 % 2 == 0) {
                                    byte[] bArr6 = i;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                    break;
                                } else {
                                    byte[] bArr7 = i;
                                    fVar.d = fVar.d % 1;
                                    fVar.e = (char) (fVar.b % (((byte) (((byte) (bArr7[r8] / (-5810760824076169584L))) * s)) ^ b));
                                    break;
                                }
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
