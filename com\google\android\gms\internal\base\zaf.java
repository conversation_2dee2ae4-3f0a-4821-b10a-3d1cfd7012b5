package com.google.android.gms.internal.base;

import com.google.android.gms.common.Feature;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\base\zaf.smali */
public final class zaf {
    public static final Feature zaa;
    public static final Feature[] zab;

    static {
        Feature feature = new Feature("CLIENT_TELEMETRY", 1L);
        zaa = feature;
        zab = new Feature[]{feature};
    }
}
