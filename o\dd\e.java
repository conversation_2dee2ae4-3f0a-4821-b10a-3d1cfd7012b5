package o.dd;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.antelopsecurecmodule.ScmJni;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.f;
import o.bv.a;
import o.ee.g;
import o.eg.b;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dd\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int f;
    private static short[] g;
    private static byte[] h;
    private static int i;
    private static int j;
    private static int k;
    private static int l;
    private final ScmJni a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        k = 1;
        c();
        ViewConfiguration.getScrollBarFadeDuration();
        int i2 = k + 83;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        c = (char) 62396;
        b = (char) 8854;
        e = (char) 34428;
        d = (char) 41784;
        h = new byte[]{56, 118, -118, 41, 105, -107, 101, -108, 103, -105, -97, 110, -111, -91, 88, 105, -107, 101, -108, 103, -105, -97, 101, -103, -43, 62, -109, -103, -42, 45, -45, 41, 105, -107, 109, -112, -101, -114, 54, -110, -110, 107, 100, -99, 97, -106, -107, -39, 71, 107, -108, -70, 62, -109, -103, -42, 44, 111, 97, -125, 110, 96, 110, -97, -108, -125, -65, 44, -109, 98, -109, 99, -120, -127, 26, 85, 100, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 121, 100, -106, 20, 100, 47, -118, 44, -117, -71, 32, -112, -65, 91, -103, -104, -43, 42, 103, -33, 34, 111, -106, 101, -125, ByteCompanionObject.MAX_VALUE, -103, 105, 104, -125, -90, 81, 111, -106, 101, -108, -109, 104, -110, -107, 111, -100, -45, 55, -124, 106, -37, 42, 103, -33, 46, 105, 109, -36, 77, -77, 41, 105, -107, 96, -125, 109, 101, -107, -111, -34, 2, -117, 75, 111, -106, 101, -125, 110, 106, 109, 108, -112, -65, 91, -103, -104, 21, ByteCompanionObject.MAX_VALUE, -125, 85, -77, 46, 105, 109, -36, 33, 107, -60, 34, 111, -106, 101, -125, ByteCompanionObject.MAX_VALUE, -103, 105, 104, -125, -90, 81, 111, -106, 101, -108, -109, 104, -110, -107, 111, -100, -45, 55, -124, 106, -37, 41, 105, -107, 96, -125, 109, 101, -107, -111, -34, 43, 105, -109, -111, 97, -57, 34, 111, -106, 101, -108, -101, -110, 123, -125, -43, 34, 111, 108, -103, -38, 93, -34, -107, 118, -118, 47, 125, -125, 109, -44, 60, 111, -107, -110, -107, -39, 45, -110, 100, -35, 99, -99, Base64.padSymbol, -34, -107, 118, -118, 44, 107, -39, 32, -112, -97, -47, 58, -103, -104, -43, 99, -99, 40, 101, -98, 99, -100, -111, 99, -62, 45, -110, 100, -35, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 102, -110, -60, 42, 103, -33, 46, 105, 109, -36, 77, -77, 41, 105, -107, 96, -125, 109, 101, -107, -111, -34, 5, 118, -118, Base64.padSymbol, 110, -111, -125, 125, -79, 118, 103, -93, 75, 101, -98, 99, -100, -111, 99, 6, 110, -109, 104, 111, -100, -45, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -101, 104, 101, -101, 107, -107, -39, 60, -97, 98, -98, 99, 12, 110, -101, 102, -99, -97, 125, 99, 109, -126, 110, -126, -75, 37, -107, -101, 103, 109, -126, 110, 98, -43, 93, 123, -123, 103, 27, -98, 97, 107, -89, 94, 108, -125, 103};
        f = 909053678;
        j = -461268056;
        i = -1237931810;
    }

    static void init$0() {
        $$a = new byte[]{12, 98, 124, -66};
        $$b = Opcodes.IF_ICMPLE;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = r7 + 108
            byte[] r0 = o.dd.e.$$a
            int r6 = r6 * 3
            int r6 = 4 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dd.e.o(byte, int, byte, java.lang.Object[]):void");
    }

    public e(Context context) {
        this.a = new ScmJni(context);
    }

    /* JADX WARN: Code restructure failed: missing block: B:44:0x01b7, code lost:
    
        if (r1.length != 0) goto L60;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x01c0, code lost:
    
        r2 = new java.lang.String(r1, o.ee.j.c());
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x01be, code lost:
    
        if (r1.length != 0) goto L60;
     */
    /* JADX WARN: Removed duplicated region for block: B:28:0x0183  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String b(int r28, int r29, java.lang.String r30, byte[] r31, byte[] r32, byte[] r33, byte[][] r34) {
        /*
            Method dump skipped, instructions count: 692
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dd.e.b(int, int, java.lang.String, byte[], byte[], byte[], byte[][]):java.lang.String");
    }

    public final String a(String str) {
        int i2;
        int i3 = k + 87;
        l = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                i2 = 47;
                break;
            default:
                i2 = 12;
                break;
        }
        String b2 = b(i2, 5, str, null, null, null, null);
        int i4 = l + 33;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? '4' : (char) 15) {
            case '4':
                int i5 = 30 / 0;
                return b2;
            default:
                return b2;
        }
    }

    public final String d(String str) {
        String b2;
        int i2 = k + 49;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? ':' : '*') {
            case '*':
                b2 = b(12, 6, str, null, null, null, null);
                break;
            default:
                b2 = b(Opcodes.LNEG, 85, str, null, null, null, null);
                break;
        }
        int i3 = l + 27;
        k = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    public final void d(String str, int i2) {
        try {
            g.c();
            Object[] objArr = new Object[1];
            m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", 13 - TextUtils.indexOf("", ""), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            n((byte) TextUtils.getTrimmedLength(""), 2145802758 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getWindowTouchSlop() >> 8) - 127, 760310505 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            m("国傾銉臾秎驑컘\ue6a8", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 6, objArr3);
            g.d(intern, append.append(((String) objArr3[0]).intern()).append(i2).toString());
            b bVar = new b();
            Object[] objArr4 = new Object[1];
            n((byte) TextUtils.indexOf("", "", 0, 0), 2145802819 - (ViewConfiguration.getWindowTouchSlop() >> 8), (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), (ViewConfiguration.getPressedStateDuration() >> 16) - 127, 760310573 - View.MeasureSpec.getMode(0), objArr4);
            bVar.d(((String) objArr4[0]).intern(), str);
            Object[] objArr5 = new Object[1];
            n((byte) Color.green(0), 2145802835 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (-126) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), 760310569 - ((Process.getThreadPriority(0) + 20) >> 6), objArr5);
            bVar.d(((String) objArr5[0]).intern(), i2);
            b(50, 0, bVar.b(), null, null, null, null);
            int i3 = l + 51;
            k = i3 % 128;
            int i4 = i3 % 2;
        } catch (d e2) {
            g.c();
            Object[] objArr6 = new Object[1];
            m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", 13 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            n((byte) (ViewConfiguration.getScrollBarSize() >> 8), 2145802838 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (short) Drawable.resolveOpacity(0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 127, 760310504 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr7);
            g.a(intern2, ((String) objArr7[0]).intern(), e2);
        }
    }

    public final void c(String str, String str2) {
        try {
            g.c();
            Object[] objArr = new Object[1];
            m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", TextUtils.lastIndexOf("", '0', 0) + 14, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            n((byte) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 2145802905 - (ViewConfiguration.getEdgeSlop() >> 16), (short) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 127, 760310504 - TextUtils.getOffsetBefore("", 0), objArr2);
            g.d(intern, String.format(((String) objArr2[0]).intern(), str, str2));
            b bVar = new b();
            Object[] objArr3 = new Object[1];
            n((byte) (AndroidCharacter.getMirror('0') - '0'), 2145802819 + (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (short) ((-1) - Process.getGidForName("")), (-127) - (KeyEvent.getMaxKeyCode() >> 16), (Process.myTid() >> 22) + 760310573, objArr3);
            bVar.d(((String) objArr3[0]).intern(), str);
            Object[] objArr4 = new Object[1];
            m("⍕鍶⮏탣졍蘽\udc77釻ꕺ\udda0", (KeyEvent.getMaxKeyCode() >> 16) + 10, objArr4);
            bVar.d(((String) objArr4[0]).intern(), str2);
            b(50, 0, bVar.b(), null, null, null, null);
            int i2 = l + 109;
            k = i2 % 128;
            switch (i2 % 2 == 0 ? '2' : 'Y') {
                case Opcodes.DUP /* 89 */:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (d e2) {
            g.c();
            Object[] objArr5 = new Object[1];
            m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", 14 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            m("铷栿\uf784ᚌỿ패瀮㍛류\ue962㴰䶪잦ﺯỿ패苩枩䀇蓒䵵삫\u2458媟ㄗល䆬岋\ue4beᥝ殑噘\ue2ad煍ꙣ\uf0ac㪎鶏偭걹\ue175䑘졍蘽ꌡ肇졯囓ᰉ媝瘦⢥\uf3a8\uef14姤Ἳ", 56 - KeyEvent.normalizeMetaState(0), objArr6);
            g.a(intern2, ((String) objArr6[0]).intern(), e2);
        }
    }

    public final String c(String str) {
        g.c();
        Object[] objArr = new Object[1];
        m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", 12 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        n((byte) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 2145802980 - Color.argb(0, 0, 0, 0), (short) (TextUtils.indexOf((CharSequence) "", '0') + 1), (-127) - (Process.myPid() >> 22), 760310586 - TextUtils.indexOf("", "", 0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        String b2 = b(14, 0, str, null, null, null, null);
        int i2 = l + 23;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 36 / 0;
                return b2;
            default:
                return b2;
        }
    }

    public final byte[] b(byte[] bArr) {
        g.c();
        Object[] objArr = new Object[1];
        m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", 13 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        m("二䐺\uf6c4很흼隗\u0be3䷚祶儹\ue090㯭\ue4beᥝ秎驑컘\ue6a8", ExpandableListView.getPackedPositionGroup(0L) + 17, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(o.dk.b.e(bArr)).toString());
        byte[] c2 = c(bArr);
        byte[] d2 = d(this.a.d(c2));
        a.a(bArr);
        a.a(c2);
        int i2 = k + 39;
        l = i2 % 128;
        int i3 = i2 % 2;
        return d2;
    }

    public final void a() {
        int i2 = k + Opcodes.LUSHR;
        l = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㞪坧樷퍊쇂웚ﲏ恼﬩岥뾤脹倳铪", (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 13, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n((byte) TextUtils.getCapsMode("", 0, 0), 2145802999 - View.combineMeasuredStates(0, 0), (short) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 128, Color.blue(0) + 760310586, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.a.b();
        int i4 = k + 29;
        l = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return;
            default:
                int i5 = 96 / 0;
                return;
        }
    }

    private static byte[] c(byte[] bArr) {
        int i2 = l;
        int i3 = i2 + 53;
        k = i3 % 128;
        int i4 = i3 % 2;
        if (bArr == null || bArr.length == 0) {
            return null;
        }
        int i5 = i2 + 49;
        k = i5 % 128;
        int i6 = i5 % 2;
        int i7 = i2 + 1;
        k = i7 % 128;
        int i8 = i7 % 2;
        try {
            int i9 = 16;
            Object[] objArr = new Object[1];
            n((byte) (ViewConfiguration.getKeyRepeatDelay() >> 16), 2145803019 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (short) (KeyEvent.getMaxKeyCode() >> 16), (Process.myPid() >> 22) - 127, 760310577 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
            Object newInstance = Class.forName(((String) objArr[0]).intern()).getDeclaredConstructor(null).newInstance(null);
            byte[] bArr2 = new byte[16];
            try {
                Object[] objArr2 = new Object[1];
                n((byte) ExpandableListView.getPackedPositionType(0L), 2145803019 - Color.alpha(0), (short) TextUtils.indexOf("", "", 0, 0), Color.red(0) - 127, (KeyEvent.getMaxKeyCode() >> 16) + 760310578, objArr2);
                Class<?> cls = Class.forName(((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                n((byte) View.MeasureSpec.makeMeasureSpec(0, 0), 2145803045 - View.resolveSize(0, 0), (short) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getMaximumFlingVelocity() >> 16) - 127, 777087798 + Color.rgb(0, 0, 0), objArr3);
                cls.getMethod(((String) objArr3[0]).intern(), byte[].class).invoke(newInstance, bArr2);
                int length = bArr.length + 17;
                int i10 = length % 16;
                switch (i10 > 0 ? (char) 19 : 'J') {
                    case 19:
                        int i11 = l + 1;
                        k = i11 % 128;
                        if (i11 % 2 == 0) {
                            length /= 41 << (length + 67);
                            break;
                        } else {
                            length += 16 - i10;
                            break;
                        }
                }
                byte[] bArr3 = new byte[length];
                for (int i12 = 0; i12 < 16; i12++) {
                    bArr3[i12] = bArr2[i12];
                }
                int i13 = 0;
                while (i13 < bArr.length) {
                    bArr3[i13 + 16] = bArr[i13];
                    i13++;
                    int i14 = l + 81;
                    k = i14 % 128;
                    int i15 = i14 % 2;
                }
                bArr3[bArr.length + 16] = ByteCompanionObject.MIN_VALUE;
                for (int length2 = bArr.length + 17; length2 < length; length2++) {
                    bArr3[length2] = 0;
                }
                while (true) {
                    switch (i9 < length ? '[' : 'B') {
                        case Opcodes.DUP_X2 /* 91 */:
                            int i16 = l + 99;
                            k = i16 % 128;
                            int i17 = i16 % 2;
                            bArr3[i9] = (byte) ((bArr3[i9 - 16] ^ bArr3[i9]) ^ ((i9 + 26) % 256));
                            i9++;
                        default:
                            return bArr3;
                    }
                }
            } catch (Throwable th) {
                Throwable cause = th.getCause();
                if (cause != null) {
                    throw cause;
                }
                throw th;
            }
        } catch (Throwable th2) {
            Throwable cause2 = th2.getCause();
            if (cause2 != null) {
                throw cause2;
            }
            throw th2;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static byte[] d(byte[] r7) {
        /*
            r0 = 0
            if (r7 == 0) goto L9c
            int r1 = o.dd.e.k
            int r1 = r1 + 49
            int r2 = r1 % 128
            o.dd.e.l = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L98
            int r1 = r7.length
            if (r1 != 0) goto L15
            goto L9c
        L15:
            int r1 = r7.length
            int r1 = r1 + (-16)
            byte[] r2 = new byte[r1]
            r3 = 0
            r4 = r3
        L1c:
            r5 = 1
            if (r4 >= r1) goto L21
            r6 = r5
            goto L22
        L21:
            r6 = r3
        L22:
            switch(r6) {
                case 0: goto L32;
                default: goto L25;
            }
        L25:
            int r5 = o.dd.e.k
            int r5 = r5 + 65
            int r6 = r5 % 128
            o.dd.e.l = r6
            int r5 = r5 % 2
            if (r5 == 0) goto L86
            goto L74
        L32:
            int r1 = r1 - r5
        L33:
            if (r1 <= 0) goto L37
            r7 = r3
            goto L38
        L37:
            r7 = r5
        L38:
            switch(r7) {
                case 1: goto L57;
                default: goto L3b;
            }
        L3b:
            r7 = r2[r1]
            r4 = -128(0xffffffffffffff80, float:NaN)
            if (r7 == r4) goto L44
            r7 = 80
            goto L46
        L44:
            r7 = 86
        L46:
            switch(r7) {
                case 80: goto L4a;
                default: goto L49;
            }
        L49:
            goto L57
        L4a:
            int r7 = o.dd.e.k
            int r7 = r7 + 77
            int r4 = r7 % 128
            o.dd.e.l = r4
            int r7 = r7 % 2
            int r1 = r1 + (-1)
            goto L33
        L57:
            byte[] r7 = java.util.Arrays.copyOf(r2, r1)
            int r1 = o.dd.e.k
            int r1 = r1 + 89
            int r2 = r1 % 128
            o.dd.e.l = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L69
            r3 = r5
            goto L6a
        L69:
        L6a:
            switch(r3) {
                case 1: goto L6e;
                default: goto L6d;
            }
        L6d:
            return r7
        L6e:
            r0.hashCode()     // Catch: java.lang.Throwable -> L72
            throw r0     // Catch: java.lang.Throwable -> L72
        L72:
            r7 = move-exception
            throw r7
        L74:
            int r5 = r4 >> 116
            r5 = r7[r5]
            r6 = r7[r4]
            r5 = r5 & r6
            int r6 = r4 << 75
            int r6 = r6 + 16343
            r5 = r5 | r6
            byte r5 = (byte) r5
            r2[r4] = r5
            int r4 = r4 + 47
            goto L97
        L86:
            int r5 = r4 + 16
            r5 = r7[r5]
            r6 = r7[r4]
            r5 = r5 ^ r6
            int r6 = r4 + 42
            int r6 = r6 % 256
            r5 = r5 ^ r6
            byte r5 = (byte) r5
            r2[r4] = r5
            int r4 = r4 + 1
        L97:
            goto L1c
        L98:
            int r7 = r7.length
            throw r0     // Catch: java.lang.Throwable -> L9a
        L9a:
            r7 = move-exception
            throw r7
        L9c:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dd.e.d(byte[]):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dd.e.m(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void n(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        int i5;
        boolean z2;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        int i6 = 2;
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(f)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(11 - (Process.myPid() >> 22), (char) KeyEvent.keyCodeFromString(""), TextUtils.getTrimmedLength("") + 65);
                byte b3 = (byte) 0;
                byte b4 = b3;
                Object[] objArr3 = new Object[1];
                o(b3, b4, b4, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            switch (intValue == -1 ? '#' : 'W') {
                case Opcodes.POP /* 87 */:
                    z = false;
                    break;
                default:
                    z = true;
                    break;
            }
            if (z) {
                byte[] bArr = h;
                switch (bArr == null) {
                    default:
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i7 = 0;
                        while (i7 < length) {
                            int i8 = $10 + 109;
                            $11 = i8 % 128;
                            int i9 = i8 % i6;
                            try {
                                Object[] objArr4 = {Integer.valueOf(bArr[i7])};
                                Object obj2 = o.e.a.s.get(494867332);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(Color.red(0) + 19, (char) (16425 - (ViewConfiguration.getJumpTapTimeout() >> 16)), (KeyEvent.getMaxKeyCode() >> 16) + Opcodes.FCMPG);
                                    byte b5 = (byte) 0;
                                    byte b6 = (byte) (b5 + 1);
                                    Object[] objArr5 = new Object[1];
                                    o(b5, b6, (byte) (b6 - 1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(494867332, obj2);
                                }
                                bArr2[i7] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                i7++;
                                i6 = 2;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        bArr = bArr2;
                    case true:
                        if (bArr == null) {
                            intValue = (short) (((short) (g[i2 + ((int) (i ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (f ^ (-5810760824076169584L))));
                            break;
                        } else {
                            byte[] bArr3 = h;
                            try {
                                Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(i)};
                                Object obj3 = o.e.a.s.get(-2120899312);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(12 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (char) ExpandableListView.getPackedPositionType(0L), 66 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)));
                                    byte b7 = (byte) 0;
                                    byte b8 = b7;
                                    Object[] objArr7 = new Object[1];
                                    o(b7, b8, b8, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-2120899312, obj3);
                                }
                                intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (f ^ (-5810760824076169584L))));
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
            }
            switch (intValue > 0 ? ':' : '[') {
                case Opcodes.ASTORE /* 58 */:
                    int i10 = $11;
                    int i11 = i10 + 7;
                    $10 = i11 % 128;
                    int i12 = i11 % 2;
                    int i13 = ((i2 + intValue) - 2) + ((int) (i ^ (-5810760824076169584L)));
                    switch (!z) {
                        case false:
                            int i14 = i10 + 49;
                            $10 = i14 % 128;
                            int i15 = i14 % 2;
                            i5 = 1;
                            break;
                        default:
                            i5 = 0;
                            break;
                    }
                    fVar.d = i13 + i5;
                    try {
                        Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                        Object obj4 = o.e.a.s.get(160906762);
                        if (obj4 == null) {
                            obj4 = ((Class) o.e.a.c((ViewConfiguration.getTapTimeout() >> 16) + 11, (char) View.combineMeasuredStates(0, 0), MotionEvent.axisFromString("") + 604)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj4);
                        }
                        ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr4 = h;
                        switch (bArr4 != null) {
                            case false:
                                break;
                            default:
                                int length2 = bArr4.length;
                                byte[] bArr5 = new byte[length2];
                                for (int i16 = 0; i16 < length2; i16++) {
                                    bArr5[i16] = (byte) (bArr4[i16] ^ (-5810760824076169584L));
                                }
                                bArr4 = bArr5;
                                break;
                        }
                        if (bArr4 != null) {
                            int i17 = $11 + 109;
                            $10 = i17 % 128;
                            int i18 = i17 % 2;
                            z2 = true;
                        } else {
                            z2 = false;
                        }
                        fVar.c = 1;
                        while (fVar.c < intValue) {
                            if (z2) {
                                byte[] bArr6 = h;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                            } else {
                                short[] sArr = g;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                            }
                            sb.append(fVar.e);
                            fVar.b = fVar.e;
                            fVar.c++;
                        }
                        break;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
