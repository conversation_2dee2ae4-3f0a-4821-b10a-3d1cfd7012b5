package com.google.android.gms.tapandpay.quickaccesswallet;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\WalletCardIntent$Builder.smali */
public final class WalletCardIntent$Builder {
    private final WalletCardIntent zza;

    public WalletCardIntent$Builder() {
        this.zza = new WalletCardIntent((zzk) null);
    }

    public WalletCardIntent build() {
        return this.zza;
    }

    public WalletCardIntent$Builder setAction(String action) {
        WalletCardIntent.zzc(this.zza, action);
        return this;
    }

    public WalletCardIntent$Builder setClassName(String className) {
        WalletCardIntent.zzd(this.zza, className);
        return this;
    }

    public WalletCardIntent$Builder setExtras(WalletCardIntentExtra[] extras) {
        WalletCardIntent.zze(this.zza, extras);
        return this;
    }

    public WalletCardIntent$Builder(WalletCardIntent origin) {
        WalletCardIntent walletCardIntent = new WalletCardIntent((zzk) null);
        this.zza = walletCardIntent;
        WalletCardIntent.zzd(walletCardIntent, WalletCardIntent.zzb(origin));
        WalletCardIntent.zzc(walletCardIntent, WalletCardIntent.zza(origin));
        WalletCardIntent.zze(walletCardIntent, WalletCardIntent.zzf(origin));
    }
}
