package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w.smali */
public class w extends b0 {
    static final o0 C = new a(w.class, 6);
    private static final ConcurrentMap<b, w> L = new ConcurrentHashMap();
    private final String b;
    private byte[] x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return w.a(f2Var.h(), false);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w$b.smali */
    private static class b {
        private final int a;
        private final byte[] b;

        b(byte[] bArr) {
            this.a = Arrays.hashCode(bArr);
            this.b = bArr;
        }

        public boolean equals(Object obj) {
            if (obj instanceof b) {
                return Arrays.areEqual(this.b, ((b) obj).b);
            }
            return false;
        }

        public int hashCode() {
            return this.a;
        }
    }

    w(byte[] bArr, boolean z) {
        byte[] bArr2 = bArr;
        if (bArr2.length == 0) {
            throw new IllegalArgumentException("empty OBJECT IDENTIFIER with no sub-identifiers");
        }
        StringBuilder sb = new StringBuilder();
        boolean z2 = true;
        long j = 0;
        BigInteger bigInteger = null;
        for (int i = 0; i != bArr2.length; i++) {
            int i2 = bArr2[i] & 255;
            if (j <= 72057594037927808L) {
                long j2 = j + (i2 & 127);
                if ((i2 & 128) == 0) {
                    if (z2) {
                        if (j2 < 40) {
                            sb.append('0');
                        } else if (j2 < 80) {
                            sb.append('1');
                            j2 -= 40;
                        } else {
                            sb.append('2');
                            j2 -= 80;
                        }
                        z2 = false;
                    }
                    sb.append('.');
                    sb.append(j2);
                    j = 0;
                } else {
                    j = j2 << 7;
                }
            } else {
                BigInteger or = (bigInteger == null ? BigInteger.valueOf(j) : bigInteger).or(BigInteger.valueOf(i2 & 127));
                if ((i2 & 128) == 0) {
                    if (z2) {
                        sb.append('2');
                        or = or.subtract(BigInteger.valueOf(80L));
                        z2 = false;
                    }
                    sb.append('.');
                    sb.append(or);
                    j = 0;
                    bigInteger = null;
                } else {
                    bigInteger = or.shiftLeft(7);
                }
            }
        }
        this.b = sb.toString();
        this.x = z ? Arrays.clone(bArr) : bArr2;
    }

    public static w a(Object obj) {
        if (obj == null || (obj instanceof w)) {
            return (w) obj;
        }
        if (obj instanceof h) {
            b0 aSN1Primitive = ((h) obj).toASN1Primitive();
            if (aSN1Primitive instanceof w) {
                return (w) aSN1Primitive;
            }
        } else if (obj instanceof byte[]) {
            try {
                return (w) C.a((byte[]) obj);
            } catch (IOException e) {
                throw new IllegalArgumentException("failed to construct object identifier from byte[]: " + e.getMessage());
            }
        }
        throw new IllegalArgumentException("illegal object in getInstance: " + obj.getClass().getName());
    }

    private static boolean b(String str) {
        char charAt;
        if (str.length() < 3 || str.charAt(1) != '.' || (charAt = str.charAt(0)) < '0' || charAt > '2') {
            return false;
        }
        return d0.a(str, 2);
    }

    private synchronized byte[] h() {
        if (this.x == null) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            a(byteArrayOutputStream);
            this.x = byteArrayOutputStream.toByteArray();
        }
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return this.b.hashCode();
    }

    public String i() {
        return this.b;
    }

    public w j() {
        b bVar = new b(h());
        ConcurrentMap<b, w> concurrentMap = L;
        w wVar = concurrentMap.get(bVar);
        if (wVar != null) {
            return wVar;
        }
        synchronized (concurrentMap) {
            if (concurrentMap.containsKey(bVar)) {
                return concurrentMap.get(bVar);
            }
            concurrentMap.put(bVar, this);
            return this;
        }
    }

    public String toString() {
        return i();
    }

    public w a(String str) {
        return new w(this, str);
    }

    private void a(ByteArrayOutputStream byteArrayOutputStream) {
        d6 d6Var = new d6(this.b);
        int parseInt = Integer.parseInt(d6Var.b()) * 40;
        String b2 = d6Var.b();
        if (b2.length() <= 18) {
            d0.a(byteArrayOutputStream, parseInt + Long.parseLong(b2));
        } else {
            d0.a(byteArrayOutputStream, new BigInteger(b2).add(BigInteger.valueOf(parseInt)));
        }
        while (d6Var.a()) {
            String b3 = d6Var.b();
            if (b3.length() <= 18) {
                d0.a(byteArrayOutputStream, Long.parseLong(b3));
            } else {
                d0.a(byteArrayOutputStream, new BigInteger(b3));
            }
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, h().length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 6, h());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (b0Var == this) {
            return true;
        }
        if (b0Var instanceof w) {
            return this.b.equals(((w) b0Var).b);
        }
        return false;
    }

    static w a(byte[] bArr, boolean z) {
        w wVar = L.get(new b(bArr));
        return wVar == null ? new w(bArr, z) : wVar;
    }

    public w(String str) {
        if (str != null) {
            if (b(str)) {
                this.b = str;
                return;
            }
            throw new IllegalArgumentException("string " + str + " not an OID");
        }
        throw new NullPointerException("'identifier' cannot be null");
    }

    w(w wVar, String str) {
        if (d0.a(str, 0)) {
            this.b = wVar.i() + "." + str;
            return;
        }
        throw new IllegalArgumentException("string " + str + " not a valid OID branch");
    }
}
