package o.c;

import com.esotericsoftware.asm.Opcodes;
import o.f.e;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\c\a.smali */
public final class a {
    private final e b;
    private static int e = 0;
    private static int d = 1;

    public a(e eVar) {
        this.b = eVar;
    }

    public final f c() {
        f b;
        int i = e + Opcodes.DMUL;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                b = this.b.b();
                int i2 = 6 / 0;
                break;
            default:
                b = this.b.b();
                break;
        }
        int i3 = e;
        int i4 = (i3 ^ 43) + ((i3 & 43) << 1);
        d = i4 % 128;
        switch (i4 % 2 == 0 ? '^' : 'R') {
            case Opcodes.DASTORE /* 82 */:
                return b;
            default:
                int i5 = 51 / 0;
                return b;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0041  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final byte[] e() {
        /*
            r3 = this;
            int r0 = o.c.a.d
            r1 = r0 | 57
            int r1 = r1 << 1
            r0 = r0 ^ 57
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.c.a.e = r0
            int r1 = r1 % 2
            if (r1 == 0) goto L15
            r0 = 77
            goto L16
        L15:
            r0 = 4
        L16:
            r1 = 0
            switch(r0) {
                case 4: goto L21;
                default: goto L1a;
            }
        L1a:
            o.f.e r0 = r3.b
            boolean r0 = r0.g()
            goto L32
        L21:
            o.f.e r0 = r3.b
            boolean r0 = r0.g()
            if (r0 == 0) goto L2c
            r0 = 93
            goto L2e
        L2c:
            r0 = 38
        L2e:
            switch(r0) {
                case 93: goto L4c;
                default: goto L31;
            }
        L31:
            goto L41
        L32:
            r2 = 65
            int r2 = r2 / 0
            if (r0 == 0) goto L3b
            r0 = 37
            goto L3d
        L3b:
            r0 = 35
        L3d:
            switch(r0) {
                case 35: goto L31;
                default: goto L40;
            }
        L40:
            goto L4c
        L41:
            int r0 = o.c.a.d
            int r0 = r0 + 111
            int r2 = r0 % 128
            o.c.a.e = r2
            int r0 = r0 % 2
            return r1
        L4c:
            int r0 = o.c.a.e
            int r0 = r0 + 45
            int r2 = r0 % 128
            o.c.a.d = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L5b
            r0 = 82
            goto L5d
        L5b:
            r0 = 83
        L5d:
            switch(r0) {
                case 82: goto L67;
                default: goto L60;
            }
        L60:
            o.f.e r0 = r3.b
            byte[] r0 = r0.d()
            goto L72
        L67:
            o.f.e r0 = r3.b
            r0.d()
            r1.hashCode()     // Catch: java.lang.Throwable -> L70
            throw r1     // Catch: java.lang.Throwable -> L70
        L70:
            r0 = move-exception
            throw r0
        L72:
            int r1 = o.c.a.d
            int r1 = r1 + 4
            int r1 = r1 + (-1)
            int r2 = r1 % 128
            o.c.a.e = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L83
            r1 = 12
            goto L85
        L83:
            r1 = 63
        L85:
            switch(r1) {
                case 63: goto L89;
                default: goto L88;
            }
        L88:
            goto L8a
        L89:
            return r0
        L8a:
            r1 = 13
            int r1 = r1 / 0
            return r0
        L8f:
            r0 = move-exception
            throw r0
        L91:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.c.a.e():byte[]");
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x0038  */
    /* JADX WARN: Removed duplicated region for block: B:27:0x005a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String a() {
        /*
            r4 = this;
            int r0 = o.c.a.e
            r1 = r0 & 63
            r0 = r0 | 63
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.c.a.d = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L12
            r0 = 36
            goto L14
        L12:
            r0 = 89
        L14:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 89: goto L20;
                default: goto L19;
            }
        L19:
            o.f.e r0 = r4.b
            boolean r0 = r0.g()
            goto L2e
        L20:
            o.f.e r0 = r4.b
            boolean r0 = r0.g()
            if (r0 == 0) goto L29
            goto L2a
        L29:
            r2 = r1
        L2a:
            switch(r2) {
                case 0: goto L5a;
                default: goto L2d;
            }
        L2d:
            goto L38
        L2e:
            r3 = 16
            int r3 = r3 / r2
            if (r0 == 0) goto L34
            r2 = r1
        L34:
            switch(r2) {
                case 0: goto L2d;
                default: goto L37;
            }
        L37:
            goto L5a
        L38:
            int r0 = o.c.a.e
            r2 = r0 | 107(0x6b, float:1.5E-43)
            int r1 = r2 << 1
            r0 = r0 ^ 107(0x6b, float:1.5E-43)
            int r1 = r1 - r0
            int r0 = r1 % 128
            o.c.a.d = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L4c
            r0 = 68
            goto L4e
        L4c:
            r0 = 98
        L4e:
            r1 = 0
            switch(r0) {
                case 98: goto L53;
                default: goto L52;
            }
        L52:
            goto L54
        L53:
            return r1
        L54:
            r1.hashCode()     // Catch: java.lang.Throwable -> L58
            throw r1     // Catch: java.lang.Throwable -> L58
        L58:
            r0 = move-exception
            throw r0
        L5a:
            o.f.e r0 = r4.b
            java.lang.String r0 = r0.c()
            int r1 = o.c.a.e
            r2 = r1 & 55
            r1 = r1 | 55
            int r2 = r2 + r1
            int r1 = r2 % 128
            o.c.a.d = r1
            int r2 = r2 % 2
            return r0
        L6e:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.c.a.a():java.lang.String");
    }

    public final e d() {
        int i = e + 97;
        int i2 = i % 128;
        d = i2;
        int i3 = i % 2;
        e eVar = this.b;
        int i4 = (i2 + 64) - 1;
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return eVar;
            default:
                throw null;
        }
    }
}
