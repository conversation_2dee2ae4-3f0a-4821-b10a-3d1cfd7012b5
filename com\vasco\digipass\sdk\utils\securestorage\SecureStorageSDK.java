package com.vasco.digipass.sdk.utils.securestorage;

import android.content.Context;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.m;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.u;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.ReplaceWith;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

@Deprecated(message = "Instead of getting an instance from this class, call the recommended initialization function using the createSecureStorage call on the public SecureStorage interface.")
@Metadata(d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0001\u0018\u0000 \u00042\u00020\u0001:\u0001\u0005B\u0007¢\u0006\u0004\b\u0002\u0010\u0003¨\u0006\u0006"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorageSDK;", "Lcom/vasco/digipass/sdk/utils/securestorage/a;", "<init>", "()V", "Companion", "com/vasco/digipass/sdk/utils/securestorage/obfuscated/u", "lib_release"}, k = 1, mv = {1, 8, 0})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\SecureStorageSDK.smali */
public final class SecureStorageSDK extends a {
    public static final u Companion = new u();
    public static final String VERSION = "5.3.0";

    @Deprecated(message = "Get an instance properly, and then call remove using the public method: remove, or using SecureStorageImpl.delete({fileName}, {context}) remove the created instance.")
    @JvmStatic
    public static final void delete(String fileName, Context context) throws SecureStorageSDKException {
        Companion.getClass();
        Intrinsics.checkNotNullParameter(fileName, "fileName");
        Intrinsics.checkNotNullParameter(context, "context");
        a.Companion.getClass();
        m.a(fileName, context);
    }

    @Deprecated(level = DeprecationLevel.WARNING, message = "Instead of getting an instance from this class, call the recommended initialization function using the createSecureStorage call on the public SecureStorage interface.", replaceWith = @ReplaceWith(expression = "SecureStorage.createSecureStorage(fileName: String, fingerprint: String, iterationNumber: Int, context: Context)", imports = {}))
    @JvmStatic
    public static final SecureStorageSDK init(String fileName, String str, int i, Context context) throws SecureStorageSDKException {
        Companion.getClass();
        Intrinsics.checkNotNullParameter(fileName, "fileName");
        Intrinsics.checkNotNullParameter(context, "context");
        SecureStorageSDK secureStorageSDK = new SecureStorageSDK();
        secureStorageSDK.init$lib_release(fileName, str, i, context);
        return secureStorageSDK;
    }
}
