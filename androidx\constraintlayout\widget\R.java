package androidx.constraintlayout.widget;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\constraintlayout\widget\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\constraintlayout\widget\R$attr.smali */
    public static final class attr {
        public static int barrierAllowsGoneWidgets = 0x7f0400c5;
        public static int barrierDirection = 0x7f0400c6;
        public static int chainUseRtl = 0x7f0400f7;
        public static int constraintSet = 0x7f04013d;
        public static int constraint_referenced_ids = 0x7f04013e;
        public static int content = 0x7f04013f;
        public static int emptyVisibility = 0x7f040185;
        public static int layout_constrainedHeight = 0x7f040205;
        public static int layout_constrainedWidth = 0x7f040206;
        public static int layout_constraintBaseline_creator = 0x7f040207;
        public static int layout_constraintBaseline_toBaselineOf = 0x7f040208;
        public static int layout_constraintBottom_creator = 0x7f040209;
        public static int layout_constraintBottom_toBottomOf = 0x7f04020a;
        public static int layout_constraintBottom_toTopOf = 0x7f04020b;
        public static int layout_constraintCircle = 0x7f04020c;
        public static int layout_constraintCircleAngle = 0x7f04020d;
        public static int layout_constraintCircleRadius = 0x7f04020e;
        public static int layout_constraintDimensionRatio = 0x7f04020f;
        public static int layout_constraintEnd_toEndOf = 0x7f040210;
        public static int layout_constraintEnd_toStartOf = 0x7f040211;
        public static int layout_constraintGuide_begin = 0x7f040212;
        public static int layout_constraintGuide_end = 0x7f040213;
        public static int layout_constraintGuide_percent = 0x7f040214;
        public static int layout_constraintHeight_default = 0x7f040215;
        public static int layout_constraintHeight_max = 0x7f040216;
        public static int layout_constraintHeight_min = 0x7f040217;
        public static int layout_constraintHeight_percent = 0x7f040218;
        public static int layout_constraintHorizontal_bias = 0x7f040219;
        public static int layout_constraintHorizontal_chainStyle = 0x7f04021a;
        public static int layout_constraintHorizontal_weight = 0x7f04021b;
        public static int layout_constraintLeft_creator = 0x7f04021c;
        public static int layout_constraintLeft_toLeftOf = 0x7f04021d;
        public static int layout_constraintLeft_toRightOf = 0x7f04021e;
        public static int layout_constraintRight_creator = 0x7f04021f;
        public static int layout_constraintRight_toLeftOf = 0x7f040220;
        public static int layout_constraintRight_toRightOf = 0x7f040221;
        public static int layout_constraintStart_toEndOf = 0x7f040222;
        public static int layout_constraintStart_toStartOf = 0x7f040223;
        public static int layout_constraintTop_creator = 0x7f040224;
        public static int layout_constraintTop_toBottomOf = 0x7f040225;
        public static int layout_constraintTop_toTopOf = 0x7f040226;
        public static int layout_constraintVertical_bias = 0x7f040227;
        public static int layout_constraintVertical_chainStyle = 0x7f040228;
        public static int layout_constraintVertical_weight = 0x7f040229;
        public static int layout_constraintWidth_default = 0x7f04022a;
        public static int layout_constraintWidth_max = 0x7f04022b;
        public static int layout_constraintWidth_min = 0x7f04022c;
        public static int layout_constraintWidth_percent = 0x7f04022d;
        public static int layout_editor_absoluteX = 0x7f04022f;
        public static int layout_editor_absoluteY = 0x7f040230;
        public static int layout_goneMarginBottom = 0x7f040231;
        public static int layout_goneMarginEnd = 0x7f040232;
        public static int layout_goneMarginLeft = 0x7f040233;
        public static int layout_goneMarginRight = 0x7f040234;
        public static int layout_goneMarginStart = 0x7f040235;
        public static int layout_goneMarginTop = 0x7f040236;
        public static int layout_optimizationLevel = 0x7f040239;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\constraintlayout\widget\R$id.smali */
    public static final class id {
        public static int bottom = 0x7f0a0060;
        public static int end = 0x7f0a008e;
        public static int gone = 0x7f0a00a8;
        public static int invisible = 0x7f0a00b7;
        public static int left = 0x7f0a00bd;
        public static int packed = 0x7f0a00f1;
        public static int parent = 0x7f0a00f4;
        public static int percent = 0x7f0a00f9;
        public static int right = 0x7f0a00ff;
        public static int spread = 0x7f0a012b;
        public static int spread_inside = 0x7f0a012c;
        public static int start = 0x7f0a0131;
        public static int top = 0x7f0a0158;
        public static int wrap = 0x7f0a0173;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\constraintlayout\widget\R$styleable.smali */
    public static final class styleable {
        public static int ConstraintLayout_Layout_android_maxHeight = 0x00000002;
        public static int ConstraintLayout_Layout_android_maxWidth = 0x00000001;
        public static int ConstraintLayout_Layout_android_minHeight = 0x00000004;
        public static int ConstraintLayout_Layout_android_minWidth = 0x00000003;
        public static int ConstraintLayout_Layout_android_orientation = 0x00000000;
        public static int ConstraintLayout_Layout_barrierAllowsGoneWidgets = 0x00000005;
        public static int ConstraintLayout_Layout_barrierDirection = 0x00000006;
        public static int ConstraintLayout_Layout_chainUseRtl = 0x00000007;
        public static int ConstraintLayout_Layout_constraintSet = 0x00000008;
        public static int ConstraintLayout_Layout_constraint_referenced_ids = 0x00000009;
        public static int ConstraintLayout_Layout_layout_constrainedHeight = 0x0000000a;
        public static int ConstraintLayout_Layout_layout_constrainedWidth = 0x0000000b;
        public static int ConstraintLayout_Layout_layout_constraintBaseline_creator = 0x0000000c;
        public static int ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf = 0x0000000d;
        public static int ConstraintLayout_Layout_layout_constraintBottom_creator = 0x0000000e;
        public static int ConstraintLayout_Layout_layout_constraintBottom_toBottomOf = 0x0000000f;
        public static int ConstraintLayout_Layout_layout_constraintBottom_toTopOf = 0x00000010;
        public static int ConstraintLayout_Layout_layout_constraintCircle = 0x00000011;
        public static int ConstraintLayout_Layout_layout_constraintCircleAngle = 0x00000012;
        public static int ConstraintLayout_Layout_layout_constraintCircleRadius = 0x00000013;
        public static int ConstraintLayout_Layout_layout_constraintDimensionRatio = 0x00000014;
        public static int ConstraintLayout_Layout_layout_constraintEnd_toEndOf = 0x00000015;
        public static int ConstraintLayout_Layout_layout_constraintEnd_toStartOf = 0x00000016;
        public static int ConstraintLayout_Layout_layout_constraintGuide_begin = 0x00000017;
        public static int ConstraintLayout_Layout_layout_constraintGuide_end = 0x00000018;
        public static int ConstraintLayout_Layout_layout_constraintGuide_percent = 0x00000019;
        public static int ConstraintLayout_Layout_layout_constraintHeight_default = 0x0000001a;
        public static int ConstraintLayout_Layout_layout_constraintHeight_max = 0x0000001b;
        public static int ConstraintLayout_Layout_layout_constraintHeight_min = 0x0000001c;
        public static int ConstraintLayout_Layout_layout_constraintHeight_percent = 0x0000001d;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_bias = 0x0000001e;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle = 0x0000001f;
        public static int ConstraintLayout_Layout_layout_constraintHorizontal_weight = 0x00000020;
        public static int ConstraintLayout_Layout_layout_constraintLeft_creator = 0x00000021;
        public static int ConstraintLayout_Layout_layout_constraintLeft_toLeftOf = 0x00000022;
        public static int ConstraintLayout_Layout_layout_constraintLeft_toRightOf = 0x00000023;
        public static int ConstraintLayout_Layout_layout_constraintRight_creator = 0x00000024;
        public static int ConstraintLayout_Layout_layout_constraintRight_toLeftOf = 0x00000025;
        public static int ConstraintLayout_Layout_layout_constraintRight_toRightOf = 0x00000026;
        public static int ConstraintLayout_Layout_layout_constraintStart_toEndOf = 0x00000027;
        public static int ConstraintLayout_Layout_layout_constraintStart_toStartOf = 0x00000028;
        public static int ConstraintLayout_Layout_layout_constraintTop_creator = 0x00000029;
        public static int ConstraintLayout_Layout_layout_constraintTop_toBottomOf = 0x0000002a;
        public static int ConstraintLayout_Layout_layout_constraintTop_toTopOf = 0x0000002b;
        public static int ConstraintLayout_Layout_layout_constraintVertical_bias = 0x0000002c;
        public static int ConstraintLayout_Layout_layout_constraintVertical_chainStyle = 0x0000002d;
        public static int ConstraintLayout_Layout_layout_constraintVertical_weight = 0x0000002e;
        public static int ConstraintLayout_Layout_layout_constraintWidth_default = 0x0000002f;
        public static int ConstraintLayout_Layout_layout_constraintWidth_max = 0x00000030;
        public static int ConstraintLayout_Layout_layout_constraintWidth_min = 0x00000031;
        public static int ConstraintLayout_Layout_layout_constraintWidth_percent = 0x00000032;
        public static int ConstraintLayout_Layout_layout_editor_absoluteX = 0x00000033;
        public static int ConstraintLayout_Layout_layout_editor_absoluteY = 0x00000034;
        public static int ConstraintLayout_Layout_layout_goneMarginBottom = 0x00000035;
        public static int ConstraintLayout_Layout_layout_goneMarginEnd = 0x00000036;
        public static int ConstraintLayout_Layout_layout_goneMarginLeft = 0x00000037;
        public static int ConstraintLayout_Layout_layout_goneMarginRight = 0x00000038;
        public static int ConstraintLayout_Layout_layout_goneMarginStart = 0x00000039;
        public static int ConstraintLayout_Layout_layout_goneMarginTop = 0x0000003a;
        public static int ConstraintLayout_Layout_layout_optimizationLevel = 0x0000003b;
        public static int ConstraintLayout_placeholder_content = 0x00000000;
        public static int ConstraintLayout_placeholder_emptyVisibility = 0x00000001;
        public static int ConstraintSet_android_alpha = 0x0000000d;
        public static int ConstraintSet_android_elevation = 0x0000001a;
        public static int ConstraintSet_android_id = 0x00000001;
        public static int ConstraintSet_android_layout_height = 0x00000004;
        public static int ConstraintSet_android_layout_marginBottom = 0x00000008;
        public static int ConstraintSet_android_layout_marginEnd = 0x00000018;
        public static int ConstraintSet_android_layout_marginLeft = 0x00000005;
        public static int ConstraintSet_android_layout_marginRight = 0x00000007;
        public static int ConstraintSet_android_layout_marginStart = 0x00000017;
        public static int ConstraintSet_android_layout_marginTop = 0x00000006;
        public static int ConstraintSet_android_layout_width = 0x00000003;
        public static int ConstraintSet_android_maxHeight = 0x0000000a;
        public static int ConstraintSet_android_maxWidth = 0x00000009;
        public static int ConstraintSet_android_minHeight = 0x0000000c;
        public static int ConstraintSet_android_minWidth = 0x0000000b;
        public static int ConstraintSet_android_orientation = 0x00000000;
        public static int ConstraintSet_android_rotation = 0x00000014;
        public static int ConstraintSet_android_rotationX = 0x00000015;
        public static int ConstraintSet_android_rotationY = 0x00000016;
        public static int ConstraintSet_android_scaleX = 0x00000012;
        public static int ConstraintSet_android_scaleY = 0x00000013;
        public static int ConstraintSet_android_transformPivotX = 0x0000000e;
        public static int ConstraintSet_android_transformPivotY = 0x0000000f;
        public static int ConstraintSet_android_translationX = 0x00000010;
        public static int ConstraintSet_android_translationY = 0x00000011;
        public static int ConstraintSet_android_translationZ = 0x00000019;
        public static int ConstraintSet_android_visibility = 0x00000002;
        public static int ConstraintSet_barrierAllowsGoneWidgets = 0x0000001b;
        public static int ConstraintSet_barrierDirection = 0x0000001c;
        public static int ConstraintSet_chainUseRtl = 0x0000001d;
        public static int ConstraintSet_constraint_referenced_ids = 0x0000001e;
        public static int ConstraintSet_layout_constrainedHeight = 0x0000001f;
        public static int ConstraintSet_layout_constrainedWidth = 0x00000020;
        public static int ConstraintSet_layout_constraintBaseline_creator = 0x00000021;
        public static int ConstraintSet_layout_constraintBaseline_toBaselineOf = 0x00000022;
        public static int ConstraintSet_layout_constraintBottom_creator = 0x00000023;
        public static int ConstraintSet_layout_constraintBottom_toBottomOf = 0x00000024;
        public static int ConstraintSet_layout_constraintBottom_toTopOf = 0x00000025;
        public static int ConstraintSet_layout_constraintCircle = 0x00000026;
        public static int ConstraintSet_layout_constraintCircleAngle = 0x00000027;
        public static int ConstraintSet_layout_constraintCircleRadius = 0x00000028;
        public static int ConstraintSet_layout_constraintDimensionRatio = 0x00000029;
        public static int ConstraintSet_layout_constraintEnd_toEndOf = 0x0000002a;
        public static int ConstraintSet_layout_constraintEnd_toStartOf = 0x0000002b;
        public static int ConstraintSet_layout_constraintGuide_begin = 0x0000002c;
        public static int ConstraintSet_layout_constraintGuide_end = 0x0000002d;
        public static int ConstraintSet_layout_constraintGuide_percent = 0x0000002e;
        public static int ConstraintSet_layout_constraintHeight_default = 0x0000002f;
        public static int ConstraintSet_layout_constraintHeight_max = 0x00000030;
        public static int ConstraintSet_layout_constraintHeight_min = 0x00000031;
        public static int ConstraintSet_layout_constraintHeight_percent = 0x00000032;
        public static int ConstraintSet_layout_constraintHorizontal_bias = 0x00000033;
        public static int ConstraintSet_layout_constraintHorizontal_chainStyle = 0x00000034;
        public static int ConstraintSet_layout_constraintHorizontal_weight = 0x00000035;
        public static int ConstraintSet_layout_constraintLeft_creator = 0x00000036;
        public static int ConstraintSet_layout_constraintLeft_toLeftOf = 0x00000037;
        public static int ConstraintSet_layout_constraintLeft_toRightOf = 0x00000038;
        public static int ConstraintSet_layout_constraintRight_creator = 0x00000039;
        public static int ConstraintSet_layout_constraintRight_toLeftOf = 0x0000003a;
        public static int ConstraintSet_layout_constraintRight_toRightOf = 0x0000003b;
        public static int ConstraintSet_layout_constraintStart_toEndOf = 0x0000003c;
        public static int ConstraintSet_layout_constraintStart_toStartOf = 0x0000003d;
        public static int ConstraintSet_layout_constraintTop_creator = 0x0000003e;
        public static int ConstraintSet_layout_constraintTop_toBottomOf = 0x0000003f;
        public static int ConstraintSet_layout_constraintTop_toTopOf = 0x00000040;
        public static int ConstraintSet_layout_constraintVertical_bias = 0x00000041;
        public static int ConstraintSet_layout_constraintVertical_chainStyle = 0x00000042;
        public static int ConstraintSet_layout_constraintVertical_weight = 0x00000043;
        public static int ConstraintSet_layout_constraintWidth_default = 0x00000044;
        public static int ConstraintSet_layout_constraintWidth_max = 0x00000045;
        public static int ConstraintSet_layout_constraintWidth_min = 0x00000046;
        public static int ConstraintSet_layout_constraintWidth_percent = 0x00000047;
        public static int ConstraintSet_layout_editor_absoluteX = 0x00000048;
        public static int ConstraintSet_layout_editor_absoluteY = 0x00000049;
        public static int ConstraintSet_layout_goneMarginBottom = 0x0000004a;
        public static int ConstraintSet_layout_goneMarginEnd = 0x0000004b;
        public static int ConstraintSet_layout_goneMarginLeft = 0x0000004c;
        public static int ConstraintSet_layout_goneMarginRight = 0x0000004d;
        public static int ConstraintSet_layout_goneMarginStart = 0x0000004e;
        public static int ConstraintSet_layout_goneMarginTop = 0x0000004f;
        public static int LinearConstraintLayout_android_orientation;
        public static int[] ConstraintLayout_Layout = {android.R.attr.orientation, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, 2130968773, 2130968774, 2130968823, 2130968893, 2130968894, 2130969093, 2130969094, 2130969095, 2130969096, 2130969097, 2130969098, 2130969099, 2130969100, 2130969101, 2130969102, 2130969103, 2130969104, 2130969105, 2130969106, 2130969107, 2130969108, 2130969109, 2130969110, 2130969111, 2130969112, 2130969113, 2130969114, 2130969115, 2130969116, 2130969117, 2130969118, 2130969119, 2130969120, 2130969121, 2130969122, 2130969123, 2130969124, 2130969125, 2130969126, 2130969127, 2130969128, 2130969129, 2130969130, 2130969131, 2130969132, 2130969133, 2130969135, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142, 2130969145};
        public static int[] ConstraintLayout_placeholder = {2130968895, 2130968965};
        public static int[] ConstraintSet = {android.R.attr.orientation, android.R.attr.id, android.R.attr.visibility, android.R.attr.layout_width, android.R.attr.layout_height, android.R.attr.layout_marginLeft, android.R.attr.layout_marginTop, android.R.attr.layout_marginRight, android.R.attr.layout_marginBottom, android.R.attr.maxWidth, android.R.attr.maxHeight, android.R.attr.minWidth, android.R.attr.minHeight, android.R.attr.alpha, android.R.attr.transformPivotX, android.R.attr.transformPivotY, android.R.attr.translationX, android.R.attr.translationY, android.R.attr.scaleX, android.R.attr.scaleY, android.R.attr.rotation, android.R.attr.rotationX, android.R.attr.rotationY, android.R.attr.layout_marginStart, android.R.attr.layout_marginEnd, android.R.attr.translationZ, android.R.attr.elevation, 2130968773, 2130968774, 2130968823, 2130968894, 2130969093, 2130969094, 2130969095, 2130969096, 2130969097, 2130969098, 2130969099, 2130969100, 2130969101, 2130969102, 2130969103, 2130969104, 2130969105, 2130969106, 2130969107, 2130969108, 2130969109, 2130969110, 2130969111, 2130969112, 2130969113, 2130969114, 2130969115, 2130969116, 2130969117, 2130969118, 2130969119, 2130969120, 2130969121, 2130969122, 2130969123, 2130969124, 2130969125, 2130969126, 2130969127, 2130969128, 2130969129, 2130969130, 2130969131, 2130969132, 2130969133, 2130969135, 2130969136, 2130969137, 2130969138, 2130969139, 2130969140, 2130969141, 2130969142};
        public static int[] LinearConstraintLayout = {android.R.attr.orientation};

        private styleable() {
        }
    }

    private R() {
    }
}
