package o.bk;

import android.content.Context;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.bk.a;
import o.ee.g;
import o.ee.o;
import o.ef.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bk\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int b;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        e();
        ViewConfiguration.getKeyRepeatDelay();
        ViewConfiguration.getScrollDefaultDelay();
        View.getDefaultSize(0, 0);
        int i = b + 63;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    static void e() {
        a = new char[]{11400, 45244, 5284, 63668, 23740, 8350, 33926, 26779, 52357, 20611, 13565, 39137, 31952, 49395, 42181, 2241, 60638, 28891, 54469, 48474, 8519, 34172, 26964, 52556, 45432, 5488, 63862, 23918, 49519, 42241, 2315, 60693, 20749, 13570, 39215, 32051, 57652, 17698, 10692, 36299, 29125, 54733, 47435, 9558, 33133, 27973, 51549, 46441, 4449, 64871, 22911, 50558, 41232, 3354, 59652, 21788, 12563, 40254, 31010, 58661, 16691, 11733, 35290, 30164, 53724, 48547, 6567, 34221, 25073, 52717, 43412, 5510, 61826, 24027, 14773, 42413, 421, 60859, 18875, 13313, 36954, 31830, 55387, 17497, 8301, 35937, 26737, 54329, 45070, 7178, 63494, 25628, 49162, 44066, 2158, 62525, 20529, 15553, 39128, 1228, 57542, 19654, 10406, 38067, 28848, 56568, 47245, 9355, 32927, 27806, 51354, 46244, 4282, 64698, 22708, 50362, 41732, 3918, 60228, 22362, 13160, 40802, 31590, 59248, 17279, 12051, 35611, 30467, 54045, 48995, 6947, 34622, 25460, 53041, 43977, 6109, 62352, 24520, 15351, 42997, 1020, 61432, 19436, 14229, 37773, 32663, 11424, 45245, 5254, 63662, 23734, 8322, 33930, 26764, 52372, 20629, 13563, 39153, 31983, 49399, 42232, 2261, 60617, 28878, 54488, 47166, 7217, 57407, 17463, 10312, 35916, 4166, 62509, 22529, 15459, 32876, 25714, 51326, 44110, 12302, 37958, 30814, 56412, 41450, 1529, 59896, 11424, 45245, 5254, 63662, 23734, 8322, 33930, 26764, 52372, 20629, 13563, 39153, 31983, 49399, 42232, 2261, 60617, 28878, 54488, 47166, 7217, 57407, 17463, 10312, 35916, 4166, 62513, 22549, 15481, 32875, 25709, 51317, 44041, 12354, 37966, 30814, 56391, 41387, 1457, 59809, 19953, 53695, 46492, 6596, 64910, 16775, 9723, 35312, 28150, 61932, 21987, 14793, 40385, 11424, 45245, 5254, 63662, 23734, 8322, 33930, 26764, 52372, 20629, 13563, 39153, 31983, 49399, 42232, 2261, 60617, 28878, 54488, 47166, 7217, 57407, 17463, 10312, 35916, 4166, 62513, 22549, 15481, 32875, 25709, 51317, 44041, 12354, 37966, 30814, 56391, 41387, 1457, 59809, 19953, 53695, 46492, 6596, 64910, 16775, 9723, 35312, 28150, 61932, 21987, 14793, 40385, 24986, 50645, 43303, 3379, 37222, 29978, 55609, 48408, 270, 58650, 18692, 11622, 45436, 5492, 15847, 41466, 1473, 59881, 19953, 12741, 38349, 31179, 56787, 16850, 9660, 35254, 28072, 53680, 46527, 6546, 64910, 24969, 50591, 43385, 3446, 61816, 21872, 14607, 40203, 257, 58742, 18770, 11582, 37164, 29994, 55602, 48462, 8453, 34057, 26905, 52480, 45292, 5366, 63718, 23734, 49397, 42183, 2246, 60617, 20629, 13474, 39080, 31914, 57593, 17587, 10372, 35984, 28815, 54417, 47228, 7286, 32878, 25718, 51319, 44042, 4177, 62547, 22551, 15407, 41065, 1075, 59438, 19490, 12317, 37899, 30733, 56322, 16404, 10220, 35747, 28667, 54263, 47045, 7049, 65424, 25495};
        e = -2836860751967375154L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(byte r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = 4 - r7
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r0 = o.bk.c.$$a
            int r6 = 105 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = -r7
            int r8 = r8 + 1
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bk.c.f(byte, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -116, 103, 33};
        $$b = 10;
    }

    public static boolean b(Context context) {
        Object obj;
        Object[] objArr = new Object[1];
        c((char) TextUtils.getCapsMode("", 0, 0), ViewConfiguration.getKeyRepeatTimeout() >> 16, TextUtils.indexOf((CharSequence) "", '0', 0) + 20, objArr);
        String intern = ((String) objArr[0]).intern();
        g.c();
        Object[] objArr2 = new Object[1];
        c((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 37370), (ViewConfiguration.getTouchSlop() >> 8) + 19, 24 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        try {
            o.ef.a a2 = o.ef.c.a(context);
            int i = b + 67;
            d = i % 128;
            int i2 = i % 2;
            g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr3 = new Object[1];
            c((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (ViewConfiguration.getPressedStateDuration() >> 16) + Opcodes.I2C, 40 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
            g.d(intern, sb.append(((String) objArr3[0]).intern()).append(a2.b()).toString());
            switch (o.a.c(a2.b(), a.c.b) ? (char) 26 : '7') {
                case '7':
                    switch (o.a.c(a2.b(), a.c.c)) {
                        case false:
                            g.c();
                            Object[] objArr4 = new Object[1];
                            c((char) (TextUtils.getOffsetAfter("", 0) + 4423), View.combineMeasuredStates(0, 0) + 306, 82 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr4);
                            g.d(intern, ((String) objArr4[0]).intern());
                            int i3 = b + 83;
                            d = i3 % 128;
                            int i4 = i3 % 2;
                            return false;
                        default:
                            int i5 = d + 91;
                            b = i5 % 128;
                            if (i5 % 2 == 0) {
                            }
                            g.c();
                            Object[] objArr5 = new Object[1];
                            c((char) (Process.myPid() >> 22), 239 - KeyEvent.keyCodeFromString(""), 68 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr5);
                            g.d(intern, ((String) objArr5[0]).intern());
                            return true;
                    }
                default:
                    int i6 = d + 15;
                    b = i6 % 128;
                    if (i6 % 2 == 0) {
                        g.c();
                        Object[] objArr6 = new Object[1];
                        c((char) ExpandableListView.getPackedPositionType(1L), 5203 >>> (SystemClock.uptimeMillis() > 1L ? 1 : (SystemClock.uptimeMillis() == 1L ? 0 : -1)), 94 >> (ViewConfiguration.getScrollDefaultDelay() - 43), objArr6);
                        obj = objArr6[0];
                    } else {
                        g.c();
                        Object[] objArr7 = new Object[1];
                        c((char) ExpandableListView.getPackedPositionType(0L), 187 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 53 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr7);
                        obj = objArr7[0];
                    }
                    g.d(intern, ((String) obj).intern());
                    return true;
            }
        } catch (e e2) {
            g.c();
            Object[] objArr8 = new Object[1];
            c((char) (38379 - View.resolveSizeAndState(0, 0, 0)), 41 - Process.getGidForName(""), 104 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr8);
            g.d(intern, ((String) objArr8[0]).intern());
            return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void c(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 608
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bk.c.c(char, int, int, java.lang.Object[]):void");
    }
}
