package o.eo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.digitalcard.Token;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.a.m;
import o.p.g;
import o.v.k;
import o.v.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f.smali */
public final class f {
    private static int k = 0;
    private static int n = 1;
    private final String a;
    private final String b;
    private final String c;
    private final b d;
    private final String e;
    private final e f;
    private d g;
    private final a h;
    private final o.du.b i;
    private final String j;
    private final String l;
    private final String m;

    /* renamed from: o, reason: collision with root package name */
    private final Long f72o;

    public f(b bVar, String str, String str2, String str3, String str4, String str5, o.du.b bVar2, a aVar, e eVar, d dVar, Long l, String str6, String str7) {
        this.d = bVar;
        this.b = str;
        this.e = str2;
        this.a = str3;
        this.c = str4;
        this.j = str5;
        this.i = bVar2;
        this.h = aVar;
        this.f = eVar;
        this.g = dVar;
        this.f72o = l;
        this.l = str6;
        this.m = str7;
    }

    public final b a() {
        int i = n;
        int i2 = (i ^ 69) + ((i & 69) << 1);
        int i3 = i2 % 128;
        k = i3;
        int i4 = i2 % 2;
        b bVar = this.d;
        int i5 = ((i3 | 27) << 1) - (i3 ^ 27);
        n = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return bVar;
            default:
                int i6 = 96 / 0;
                return bVar;
        }
    }

    public final String b() {
        int i = (k + 12) - 1;
        int i2 = i % 128;
        n = i2;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                String str = this.b;
                int i3 = i2 + 9;
                k = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final String c() {
        int i = k;
        int i2 = (i ^ 15) + ((i & 15) << 1);
        int i3 = i2 % 128;
        n = i3;
        int i4 = i2 % 2;
        String str = this.e;
        int i5 = (i3 & 69) + (i3 | 69);
        k = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final String d() {
        int i = n;
        int i2 = i + 83;
        k = i2 % 128;
        int i3 = i2 % 2;
        String str = this.a;
        int i4 = (i + 16) - 1;
        k = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final String e() {
        int i = (k + 66) - 1;
        int i2 = i % 128;
        n = i2;
        Object obj = null;
        switch (i % 2 == 0 ? '[' : '-') {
            case '-':
                String str = this.c;
                int i3 = ((i2 | 11) << 1) - (i2 ^ 11);
                k = i3 % 128;
                switch (i3 % 2 != 0 ? '@' : (char) 15) {
                    case '@':
                        throw null;
                    default:
                        return str;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final String h() {
        int i = n;
        int i2 = i + Opcodes.LSUB;
        k = i2 % 128;
        int i3 = i2 % 2;
        String str = this.j;
        int i4 = (i ^ 77) + ((i & 77) << 1);
        k = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0034  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String f() {
        /*
            r5 = this;
            int r0 = o.eo.f.k
            r1 = r0 | 67
            r2 = 1
            int r1 = r1 << r2
            r3 = r0 ^ 67
            int r1 = r1 - r3
            int r3 = r1 % 128
            o.eo.f.n = r3
            int r1 = r1 % 2
            r3 = 0
            if (r1 != 0) goto L14
            r1 = r2
            goto L15
        L14:
            r1 = r3
        L15:
            switch(r1) {
                case 0: goto L1b;
                default: goto L18;
            }
        L18:
            o.du.b r1 = r5.i
            goto L28
        L1b:
            o.du.b r1 = r5.i
            if (r1 == 0) goto L22
            r1 = 91
            goto L24
        L22:
            r1 = 29
        L24:
            switch(r1) {
                case 29: goto L34;
                default: goto L27;
            }
        L27:
            goto L41
        L28:
            r4 = 63
            int r4 = r4 / r3
            if (r1 == 0) goto L2f
            r1 = r3
            goto L30
        L2f:
            r1 = r2
        L30:
            switch(r1) {
                case 1: goto L34;
                default: goto L33;
            }
        L33:
            goto L27
        L34:
            r1 = r0 & 11
            r0 = r0 | 11
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.eo.f.n = r0
            int r1 = r1 % 2
            r0 = 0
            return r0
        L41:
            o.du.b r0 = r5.i
            o.dx.d r0 = r0.e()
            java.lang.String r0 = r0.b()
            int r1 = o.eo.f.k
            r4 = r1 & 1
            r1 = r1 | r2
            int r4 = r4 + r1
            int r1 = r4 % 128
            o.eo.f.n = r1
            int r4 = r4 % 2
            if (r4 != 0) goto L5a
            goto L5b
        L5a:
            r2 = r3
        L5b:
            switch(r2) {
                case 0: goto L5f;
                default: goto L5e;
            }
        L5e:
            goto L60
        L5f:
            return r0
        L60:
            r1 = 27
            int r1 = r1 / r3
            return r0
        L64:
            r0 = move-exception
            throw r0
        L66:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.f.f():java.lang.String");
    }

    public final o.du.b i() {
        int i = k;
        int i2 = i + Opcodes.DNEG;
        n = i2 % 128;
        int i3 = i2 % 2;
        o.du.b bVar = this.i;
        int i4 = (i ^ Opcodes.DSUB) + ((i & Opcodes.DSUB) << 1);
        n = i4 % 128;
        int i5 = i4 % 2;
        return bVar;
    }

    public final Drawable c(Context context) {
        int i = k;
        int i2 = ((i | 13) << 1) - (i ^ 13);
        int i3 = i2 % 128;
        n = i3;
        int i4 = i2 % 2;
        o.du.b bVar = this.i;
        switch (bVar != null) {
            case false:
                int i5 = (i3 ^ 43) + ((i3 & 43) << 1);
                k = i5 % 128;
                switch (i5 % 2 != 0 ? 'E' : 'Z') {
                    case 'E':
                        int i6 = 20 / 0;
                        return null;
                    default:
                        return null;
                }
            default:
                int i7 = (i3 & 83) + (i3 | 83);
                k = i7 % 128;
                if (i7 % 2 != 0) {
                }
                return bVar.b(context);
        }
    }

    public final a g() {
        int i = k;
        int i2 = (i + 66) - 1;
        n = i2 % 128;
        int i3 = i2 % 2;
        a aVar = this.h;
        int i4 = ((i | 61) << 1) - (i ^ 61);
        n = i4 % 128;
        switch (i4 % 2 != 0 ? '#' : (char) 2) {
            case '#':
                return aVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final e j() {
        int i = n;
        int i2 = (i & 65) + (i | 65);
        k = i2 % 128;
        int i3 = i2 % 2;
        e eVar = this.f;
        int i4 = (i + Opcodes.FDIV) - 1;
        k = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    public final d n() {
        int i = (k + 92) - 1;
        n = i % 128;
        switch (i % 2 == 0 ? 'L' : (char) 18) {
            case 18:
                return this.g;
            default:
                throw null;
        }
    }

    public final void c(d dVar) {
        int i = n;
        int i2 = ((i | 47) << 1) - (i ^ 47);
        k = i2 % 128;
        int i3 = i2 % 2;
        this.g = dVar;
        int i4 = ((i | 69) << 1) - (i ^ 69);
        k = i4 % 128;
        switch (i4 % 2 != 0 ? ')' : (char) 1) {
            case 1:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final Long l() {
        Long l;
        int i = k;
        int i2 = (i & 45) + (i | 45);
        int i3 = i2 % 128;
        n = i3;
        switch (i2 % 2 == 0 ? '#' : (char) 7) {
            case 7:
                l = this.f72o;
                break;
            default:
                l = this.f72o;
                int i4 = 81 / 0;
                break;
        }
        int i5 = (i3 & 3) + (i3 | 3);
        k = i5 % 128;
        int i6 = i5 % 2;
        return l;
    }

    public final String o() {
        int i = k;
        int i2 = ((i | Opcodes.LSUB) << 1) - (i ^ Opcodes.LSUB);
        n = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 19 : 'X') {
            case Opcodes.POP2 /* 88 */:
                return this.l;
            default:
                int i3 = 0 / 0;
                return this.l;
        }
    }

    public final String k() {
        int i = k;
        int i2 = ((i | Opcodes.DDIV) << 1) - (i ^ Opcodes.DDIV);
        int i3 = i2 % 128;
        n = i3;
        int i4 = i2 % 2;
        String str = this.m;
        int i5 = ((i3 | 45) << 1) - (i3 ^ 45);
        k = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return str;
            default:
                throw null;
        }
    }

    public final k c(o.er.f fVar, o.eo.e eVar) {
        k kVar = new k(fVar.c(), fVar.b(), eVar, this);
        int i = n + Opcodes.DMUL;
        k = i % 128;
        switch (i % 2 != 0 ? ':' : 'M') {
            case 'M':
                return kVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void a(o.er.f fVar, o.eo.e eVar, Context context, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        c(fVar, eVar).a(context, new g() { // from class: o.eo.f.2
            private static int a = 0;
            private static int c = 1;

            @Override // o.p.g
            public final void abortPrompt() {
                int i = c + 53;
                a = i % 128;
                switch (i % 2 != 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onAuthenticationDeclined() {
                int i = (a + 20) - 1;
                c = i % 128;
                switch (i % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onCustomerCredentialsInvalid(o.g.b bVar) {
                int i = a;
                int i2 = (i & 13) + (i | 13);
                c = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.p.g
            public final void onProcessStart() {
                int i = c;
                int i2 = ((i | 79) << 1) - (i ^ 79);
                a = i2 % 128;
                switch (i2 % 2 != 0 ? '#' : '0') {
                    case '#':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i = a;
                int i2 = (i ^ 89) + ((i & 89) << 1);
                c = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.p.g
            public final void onProcessSuccess() {
                int i = c;
                int i2 = ((i | 61) << 1) - (i ^ 61);
                a = i2 % 128;
                boolean z = i2 % 2 == 0;
                Object obj = null;
                operationCallback.onSuccess(null);
                switch (z) {
                    case true:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.p.g
            public final void onError(o.bv.c cVar) {
                int i = (c + 96) - 1;
                a = i % 128;
                int i2 = i % 2;
                operationCallback.onError(cVar.d());
                int i3 = (c + 12) - 1;
                a = i3 % 128;
                switch (i3 % 2 != 0 ? 'P' : 'D') {
                    case 'D':
                        return;
                    default:
                        int i4 = 12 / 0;
                        return;
                }
            }
        });
        int i = n;
        int i2 = ((i | Opcodes.DDIV) << 1) - (i ^ Opcodes.DDIV);
        k = i2 % 128;
        int i3 = i2 % 2;
    }

    public final o a(o.er.f fVar, o.eo.e eVar) {
        o oVar = new o(fVar.b(), eVar, this);
        int i = n + 31;
        k = i % 128;
        int i2 = i % 2;
        return oVar;
    }

    public final void e(o.er.f fVar, o.eo.e eVar, Context context, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        a(fVar, eVar).e(context, new g() { // from class: o.eo.f.4
            private static int d = 0;
            private static int a = 1;

            @Override // o.p.g
            public final void abortPrompt() {
                int i = a;
                int i2 = (i ^ 35) + ((i & 35) << 1);
                d = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 24 : (char) 3) {
                    case 3:
                        break;
                    default:
                        int i3 = 6 / 0;
                        break;
                }
            }

            @Override // o.p.g
            public final void onAuthenticationDeclined() {
                int i = a + 17;
                d = i % 128;
                int i2 = i % 2;
            }

            @Override // o.p.g
            public final void onCustomerCredentialsInvalid(o.g.b bVar) {
                int i = d;
                int i2 = (i ^ 85) + ((i & 85) << 1);
                a = i2 % 128;
                switch (i2 % 2 == 0 ? Typography.greater : ' ') {
                    case '>':
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onProcessStart() {
                int i = a;
                int i2 = ((i | 39) << 1) - (i ^ 39);
                d = i2 % 128;
                switch (i2 % 2 != 0 ? 'P' : 'S') {
                    case Opcodes.AASTORE /* 83 */:
                        return;
                    default:
                        throw null;
                }
            }

            @Override // o.p.g
            public final void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i = d + 41;
                a = i % 128;
                switch (i % 2 == 0 ? 'V' : '1') {
                    case '1':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.p.g
            public final void onProcessSuccess() {
                int i = (d + 22) - 1;
                a = i % 128;
                int i2 = i % 2;
                operationCallback.onSuccess(null);
                int i3 = d + 27;
                a = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return;
                    default:
                        int i4 = 56 / 0;
                        return;
                }
            }

            @Override // o.p.g
            public final void onError(o.bv.c cVar) {
                int i = a;
                int i2 = (i & 15) + (i | 15);
                d = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onError(cVar.d());
                int i4 = a;
                int i5 = (i4 ^ 43) + ((i4 & 43) << 1);
                d = i5 % 128;
                int i6 = i5 % 2;
            }
        });
        int i = (k + 38) - 1;
        n = i % 128;
        int i2 = i % 2;
    }

    public final o.v.g b(o.er.f fVar, o.eo.e eVar) {
        o.v.g gVar = new o.v.g(fVar.b(), eVar, this);
        int i = (n + 84) - 1;
        k = i % 128;
        int i2 = i % 2;
        return gVar;
    }

    public final void d(o.er.f fVar, o.eo.e eVar, Context context, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        b(fVar, eVar).b(context, new g() { // from class: o.eo.f.1
            private static int e = 0;
            private static int b = 1;

            @Override // o.p.g
            public final void abortPrompt() {
                int i = b;
                int i2 = ((i | 7) << 1) - (i ^ 7);
                e = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.p.g
            public final void onAuthenticationDeclined() {
                int i = e;
                int i2 = ((i | 57) << 1) - (i ^ 57);
                b = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.p.g
            public final void onCustomerCredentialsInvalid(o.g.b bVar) {
                int i = b + 35;
                e = i % 128;
                int i2 = i % 2;
            }

            @Override // o.p.g
            public final void onProcessStart() {
                int i = (b + Opcodes.IAND) - 1;
                e = i % 128;
                int i2 = i % 2;
            }

            @Override // o.p.g
            public final void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i = b;
                int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
                e = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onProcessSuccess() {
                int i = (e + Opcodes.FNEG) - 1;
                b = i % 128;
                int i2 = i % 2;
                operationCallback.onSuccess(null);
                int i3 = e;
                int i4 = ((i3 | Opcodes.LNEG) << 1) - (i3 ^ Opcodes.LNEG);
                b = i4 % 128;
                int i5 = i4 % 2;
            }

            @Override // o.p.g
            public final void onError(o.bv.c cVar) {
                int i = b;
                int i2 = (i ^ 13) + ((i & 13) << 1);
                e = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onError(cVar.d());
                int i4 = (e + 38) - 1;
                b = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        int i5 = 59 / 0;
                        return;
                    default:
                        return;
                }
            }
        });
        int i = k;
        int i2 = (i & 45) + (i | 45);
        n = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f$b.smali */
    public static final class b implements o.ee.d<Token.TokenServiceProvider>, o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final b a;
        public static final b b;
        private static final /* synthetic */ b[] c;
        private static long e;
        private static int g;
        private static int j;
        private final String d;

        static void d() {
            e = 2462320956871109646L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0031  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0028  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0031 -> B:4:0x003b). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(byte r5, int r6, short r7, java.lang.Object[] r8) {
            /*
                int r7 = r7 * 2
                int r7 = 3 - r7
                int r5 = r5 * 3
                int r5 = r5 + 68
                int r6 = r6 * 3
                int r6 = r6 + 1
                byte[] r0 = o.eo.f.b.$$a
                byte[] r1 = new byte[r6]
                r2 = -1
                int r6 = r6 + r2
                if (r0 != 0) goto L1c
                r5 = r7
                r3 = r2
                r7 = r6
                r2 = r1
                r1 = r0
                r0 = r8
                r8 = r5
                goto L3b
            L1c:
                r4 = r7
                r7 = r5
                r5 = r4
            L1f:
                int r2 = r2 + 1
                int r5 = r5 + 1
                byte r3 = (byte) r7
                r1[r2] = r3
                if (r2 != r6) goto L31
                java.lang.String r5 = new java.lang.String
                r6 = 0
                r5.<init>(r1, r6)
                r8[r6] = r5
                return
            L31:
                r3 = r0[r5]
                r4 = r7
                r7 = r6
                r6 = r3
                r3 = r2
                r2 = r1
                r1 = r0
                r0 = r8
                r8 = r4
            L3b:
                int r6 = -r6
                int r6 = r6 + r8
                r8 = r0
                r0 = r1
                r1 = r2
                r2 = r3
                r4 = r7
                r7 = r6
                r6 = r4
                goto L1f
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.b.h(byte, int, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{45, 88, 59, 34};
            $$b = Opcodes.LSHL;
        }

        private static /* synthetic */ b[] c() {
            b[] bVarArr;
            int i = g;
            int i2 = i + 17;
            j = i2 % 128;
            switch (i2 % 2 == 0 ? '`' : 'G') {
                case 'G':
                    bVarArr = new b[]{b, a};
                    break;
                default:
                    bVarArr = new b[3];
                    bVarArr[1] = b;
                    bVarArr[0] = a;
                    break;
            }
            int i3 = i + 69;
            j = i3 % 128;
            switch (i3 % 2 == 0 ? 'L' : (char) 5) {
                case 5:
                    return bVarArr;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public static b valueOf(String str) {
            int i = g + Opcodes.DDIV;
            j = i % 128;
            int i2 = i % 2;
            b bVar = (b) Enum.valueOf(b.class, str);
            int i3 = g + 57;
            j = i3 % 128;
            int i4 = i3 % 2;
            return bVar;
        }

        public static b[] values() {
            int i = j + 109;
            g = i % 128;
            Object obj = null;
            switch (i % 2 != 0 ? 'D' : (char) 4) {
                case 'D':
                    obj.hashCode();
                    throw null;
                default:
                    b[] bVarArr = (b[]) c.clone();
                    int i2 = j + 39;
                    g = i2 % 128;
                    switch (i2 % 2 == 0) {
                        case true:
                            return bVarArr;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }

        @Override // o.ee.d
        public final /* synthetic */ Token.TokenServiceProvider a() {
            int i = g + 95;
            j = i % 128;
            int i2 = i % 2;
            Token.TokenServiceProvider b2 = b();
            int i3 = g + 89;
            j = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return b2;
                default:
                    int i4 = 80 / 0;
                    return b2;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            g = 0;
            j = 1;
            d();
            Object[] objArr = new Object[1];
            f("묪뭧经둕剚ꥂ\uedb2⽢", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f("묪뭧经둕剚ꥂ\uedb2⽢", (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1, objArr2);
            b = new b(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f("칈츞\uee8f솋숊顺ﻢ", -TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            f("칈츞\uee8f솋숊顺ﻢ", -(ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr4);
            a = new b(intern2, 1, ((String) objArr4[0]).intern());
            c = c();
            int i = g + 37;
            j = i % 128;
            switch (i % 2 == 0 ? (char) 18 : (char) 6) {
                case 6:
                    return;
                default:
                    throw null;
            }
        }

        private b(String str, int i, String str2) {
            this.d = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i = j;
            int i2 = i + 29;
            g = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0 ? 'D' : (char) 18) {
                case 18:
                    String str = this.d;
                    int i3 = i + 15;
                    g = i3 % 128;
                    switch (i3 % 2 != 0 ? 'L' : 'Z') {
                        case 'Z':
                            return str;
                        default:
                            obj.hashCode();
                            throw null;
                    }
                default:
                    throw null;
            }
        }

        public final Token.TokenServiceProvider b() {
            int i = j + Opcodes.LREM;
            g = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass3.d[ordinal()]) {
                case 1:
                    return Token.TokenServiceProvider.MDES;
                case 2:
                    Token.TokenServiceProvider tokenServiceProvider = Token.TokenServiceProvider.VTS;
                    int i3 = j + 7;
                    g = i3 % 128;
                    switch (i3 % 2 != 0 ? 'K' : '@') {
                        case '@':
                            return tokenServiceProvider;
                        default:
                            throw null;
                    }
                default:
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr = new Object[1];
                    f("졼젩澩滨䌖悫㜯\ue6a0筈ꍦ㮪\udf63꺑퀫\uf725\u0a0d퇑ק䐴䛺Ֆ뫗", KeyEvent.keyCodeFromString("") + 1, objArr);
                    throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Failed to find 'out' block for switch in B:52:0x003d. Please report as an issue. */
        private static void f(java.lang.String r17, int r18, java.lang.Object[] r19) {
            /*
                Method dump skipped, instructions count: 402
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.b.f(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f$a.smali */
    public static final class a implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final a a;
        public static final a c;
        public static final a d;
        public static final a e;
        private static final /* synthetic */ a[] f;
        private static int g;
        private static long h;
        private static char[] i;
        private static int j;
        private final String b;

        static void d() {
            i = new char[]{45392, 25563, 5141, 51539, 64412, 44259, 16684, 29310, 56269, 2406, 32424, 41966, 37153, 50801, 11136, 6363, 19973, 42521, 29835, 837, 56855, 60634, 48025, 22098, 25897, 13303, 11406, 65084, 35314, 21664, 26221, 12590, 56522, 61327, 47448, 17434, 59860, 15196, 19614, 37338, 41754, 62539, 6588, 10945, 31798, 33140, 18065, 37945, 58363, 16063, 3199, 23342, 46809, 34187, 54082, 11785, 32223, 55417, 2809, 32055, 41067, 37565, 50656, 10299, 6983, 19852, 45270, 58129, 54714, 11392, 65056, 35310, 21682, 26212, 12601, 56522, 61320, 47448, 17423, 6081, 8562, 52261};
            h = -3718499326570660237L;
        }

        static void init$0() {
            $$a = new byte[]{32, 0, 62, 110};
            $$b = 77;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r6, int r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 3
                int r7 = r7 + 4
                byte[] r0 = o.eo.f.a.$$a
                int r8 = 105 - r8
                int r6 = r6 * 2
                int r6 = 1 - r6
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L18
                r4 = r8
                r3 = r2
                r8 = r7
                r7 = r6
                goto L2e
            L18:
                r3 = r2
            L19:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r6) goto L26
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L26:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r7
                r7 = r6
                r6 = r8
                r8 = r5
            L2e:
                int r4 = -r4
                int r6 = r6 + r4
                int r8 = r8 + 1
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r5
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.a.l(byte, int, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ a[] c() {
            int i2 = g + Opcodes.LSUB;
            int i3 = i2 % 128;
            j = i3;
            int i4 = i2 % 2;
            a[] aVarArr = {c, e, a, d};
            int i5 = i3 + 35;
            g = i5 % 128;
            switch (i5 % 2 == 0) {
                case false:
                    return aVarArr;
                default:
                    int i6 = 20 / 0;
                    return aVarArr;
            }
        }

        public static a valueOf(String str) {
            int i2 = g + Opcodes.LREM;
            j = i2 % 128;
            int i3 = i2 % 2;
            a aVar = (a) Enum.valueOf(a.class, str);
            int i4 = j + Opcodes.LSHL;
            g = i4 % 128;
            int i5 = i4 % 2;
            return aVar;
        }

        public static a[] values() {
            int i2 = j + 35;
            g = i2 % 128;
            int i3 = i2 % 2;
            a[] aVarArr = (a[]) f.clone();
            int i4 = g + 71;
            j = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    throw null;
                default:
                    return aVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            g = 1;
            d();
            Object[] objArr = new Object[1];
            k((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 40407), KeyEvent.getDeadChar(0, 0), KeyEvent.normalizeMetaState(0) + 8, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) (TextUtils.getOffsetBefore("", 0) + 63301), (Process.myTid() >> 22) + 8, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 9, objArr2);
            c = new a(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k((char) (35480 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 18 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), 9 - TextUtils.indexOf("", "", 0, 0), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), 26 - (KeyEvent.getMaxKeyCode() >> 16), View.resolveSize(0, 0) + 10, objArr4);
            e = new a(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k((char) (50510 - ExpandableListView.getPackedPositionGroup(0L)), 35 - TextUtils.lastIndexOf("", '0'), 9 - ExpandableListView.getPackedPositionChild(0L), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            k((char) (27147 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (ViewConfiguration.getPressedStateDuration() >> 16) + 46, 11 - KeyEvent.keyCodeFromString(""), objArr6);
            a = new a(intern3, 2, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            k((char) (TextUtils.indexOf((CharSequence) "", '0') + 62714), 57 - ExpandableListView.getPackedPositionGroup(0L), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 12, objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            k((char) View.MeasureSpec.getSize(0), 68 - ImageFormat.getBitsPerPixel(0), 13 - ExpandableListView.getPackedPositionType(0L), objArr8);
            d = new a(intern4, 3, ((String) objArr8[0]).intern());
            f = c();
            int i2 = j + 35;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        private a(String str, int i2, String str2) {
            this.b = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = g + 109;
            int i3 = i2 % 128;
            j = i3;
            int i4 = i2 % 2;
            String str = this.b;
            int i5 = i3 + Opcodes.DMUL;
            g = i5 % 128;
            switch (i5 % 2 == 0 ? (char) 17 : ' ') {
                case 17:
                    int i6 = 81 / 0;
                    return str;
                default:
                    return str;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(char r18, int r19, int r20, java.lang.Object[] r21) {
            /*
                Method dump skipped, instructions count: 608
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.a.k(char, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f$e.smali */
    public static final class e implements o.ee.d<Token.Type>, o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final e a;
        public static final e b;
        public static final e c;
        public static final e d;
        public static final e e;
        private static int f;
        private static final /* synthetic */ e[] g;
        private static int h;
        private static int i;
        private static int k;
        private static short[] l;
        private static byte[] m;
        private static int n;

        /* renamed from: o, reason: collision with root package name */
        private static int f73o;
        private final String j;

        static void b() {
            f = 874635264;
            m = new byte[]{-6, -61, -76, -35, -119, 79, 79, 107, 97, -125, -126, 103, ByteCompanionObject.MIN_VALUE, 97, -124, 98, -50, -121, -14, 98, 18, 34, 57, -22, -112, -112, -112, -112, -112};
            i = 909053639;
            n = -1128972896;
            h = 1625066718;
        }

        static void init$0() {
            $$a = new byte[]{119, -13, -39, 23};
            $$b = 229;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002f). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void r(int r6, short r7, int r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 * 4
                int r7 = r7 + 4
                byte[] r0 = o.eo.f.e.$$a
                int r8 = r8 + 107
                int r6 = r6 * 4
                int r6 = r6 + 1
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r4 = r8
                r3 = r2
                r8 = r7
                goto L2f
            L17:
                r3 = r2
            L18:
                r5 = r8
                r8 = r7
                r7 = r5
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                int r3 = r3 + 1
                r4 = r0[r8]
                r5 = r8
                r8 = r7
                r7 = r5
            L2f:
                int r7 = r7 + 1
                int r8 = r8 + r4
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.e.r(int, short, int, java.lang.Object[]):void");
        }

        private static /* synthetic */ e[] d() {
            int i2 = k + 65;
            f73o = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return new e[]{d, c, a, e, b};
                default:
                    e[] eVarArr = {d, c, a};
                    eVarArr[2] = e;
                    eVarArr[5] = b;
                    return eVarArr;
            }
        }

        public static e valueOf(String str) {
            int i2 = f73o + 47;
            k = i2 % 128;
            char c2 = i2 % 2 == 0 ? 'C' : 'V';
            e eVar = (e) Enum.valueOf(e.class, str);
            switch (c2) {
                default:
                    int i3 = 40 / 0;
                case Opcodes.SASTORE /* 86 */:
                    return eVar;
            }
        }

        public static e[] values() {
            int i2 = f73o + 31;
            k = i2 % 128;
            switch (i2 % 2 == 0 ? ' ' : (char) 19) {
                case ' ':
                    throw null;
                default:
                    return (e[]) g.clone();
            }
        }

        @Override // o.ee.d
        public final /* synthetic */ Token.Type a() {
            int i2 = k + 79;
            f73o = i2 % 128;
            switch (i2 % 2 != 0 ? 'J' : (char) 18) {
                case 18:
                    return c();
                default:
                    c();
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f73o = 0;
            k = 1;
            b();
            Object[] objArr = new Object[1];
            p(Color.argb(0, 0, 0, 0) + 11, "\uffff\u0007\uffff\u0006\uffdf\uffff\f\u000f�\uffff￭\u000e\b", 13 - View.MeasureSpec.getMode(0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 228, true, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            p(2 - View.MeasureSpec.getSize(0), "\ufff9\u0007\b\u0002\ufff9\u0001\ufff9\u0000\ufff9\u0013\ufff9\u0006\t\ufff7", TextUtils.lastIndexOf("", '0', 0, 0) + 15, 203 - (ViewConfiguration.getScrollDefaultDelay() >> 16), true, objArr2);
            d = new e(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            q((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 61), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1458799183, (short) ((-46) - Drawable.resolveOpacity(0, 0)), (-85) - ExpandableListView.getPackedPositionChild(0L), 1969605913 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            q((byte) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + Opcodes.FMUL), (-1475576396) - Color.rgb(0, 0, 0), (short) (KeyEvent.getDeadChar(0, 0) + 68), (-84) - Drawable.resolveOpacity(0, 0), ImageFormat.getBitsPerPixel(0) + 1969605913, objArr4);
            c = new e(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            p(1 - Color.red(0), "\u0006￤\u0002\u0013\u0005\ufff0\u000f\uffe7\n\r", TextUtils.getOffsetBefore("", 0) + 10, View.getDefaultSize(0, 0) + 222, false, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            q((byte) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) - 25), (-1458799179) - Process.getGidForName(""), (short) (View.MeasureSpec.getSize(0) + 5), Drawable.resolveOpacity(0, 0) - 75, 1969605907 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr6);
            a = new e(intern3, 2, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            q((byte) (Process.getGidForName("") + 72), (-1458799167) - (ViewConfiguration.getDoubleTapTimeout() >> 16), (short) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 84), (-81) - View.MeasureSpec.getSize(0), 1969605921 - (ViewConfiguration.getTouchSlop() >> 8), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            p(5 - View.getDefaultSize(0, 0), "\ufffa\u0005\ufff9\b\u0007\ufffb", 5 - TextUtils.lastIndexOf("", '0', 0, 0), 200 - ((byte) KeyEvent.getModifierMetaStateMask()), true, objArr8);
            e = new e(intern4, 3, ((String) objArr8[0]).intern());
            Object[] objArr9 = new Object[1];
            p(1 - TextUtils.indexOf((CharSequence) "", '0', 0), "\uffff\f￩\u000e\u0002", TextUtils.indexOf((CharSequence) "", '0') + 6, 229 - (ViewConfiguration.getPressedStateDuration() >> 16), false, objArr9);
            String intern5 = ((String) objArr9[0]).intern();
            Object[] objArr10 = new Object[1];
            q((byte) (((Process.getThreadPriority(0) + 20) >> 6) - 26), (-1458799162) - (ViewConfiguration.getLongPressTimeout() >> 16), (short) (105 - ExpandableListView.getPackedPositionGroup(0L)), ((Process.getThreadPriority(0) + 20) >> 6) - 82, 1969605919 - Drawable.resolveOpacity(0, 0), objArr10);
            b = new e(intern5, 4, ((String) objArr10[0]).intern());
            g = d();
            int i2 = f73o + 37;
            k = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return;
                default:
                    int i3 = 21 / 0;
                    return;
            }
        }

        private e(String str, int i2, String str2) {
            this.j = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = k;
            int i3 = i2 + 95;
            f73o = i3 % 128;
            int i4 = i3 % 2;
            String str = this.j;
            int i5 = i2 + Opcodes.LNEG;
            f73o = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        public final Token.Type c() {
            int i2 = k + 23;
            f73o = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    switch (AnonymousClass3.a[ordinal()]) {
                        case 1:
                            return Token.Type.SecureElement;
                        case 2:
                            Token.Type type = Token.Type.Hce;
                            int i3 = f73o + 89;
                            k = i3 % 128;
                            int i4 = i3 % 2;
                            return type;
                        case 3:
                            return Token.Type.CardOnFile;
                        case 4:
                            return Token.Type.QRCode;
                        case 5:
                            return Token.Type.Other;
                        default:
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            p(Color.rgb(0, 0, 0) + 16777228, "\ufff9ￃ\u0007\b\u0017\u0006\b\u0013\u001b\b\u0011\ufff8ￃ\uffdd\b\u0018\u000f\u0004", 18 - View.resolveSize(0, 0), 219 - ExpandableListView.getPackedPositionChild(0L), true, objArr);
                            throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                    }
                default:
                    int i5 = AnonymousClass3.a[ordinal()];
                    throw null;
            }
        }

        private static void p(int i2, String str, int i3, int i4, boolean z, Object[] objArr) {
            int i5 = $10 + Opcodes.DNEG;
            $11 = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    char[] charArray = str != null ? str.toCharArray() : str;
                    o.a.h hVar = new o.a.h();
                    char[] cArr = new char[i3];
                    hVar.a = 0;
                    while (hVar.a < i3) {
                        hVar.b = charArray[hVar.a];
                        cArr[hVar.a] = (char) (i4 + hVar.b);
                        int i6 = hVar.a;
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr[i6]), Integer.valueOf(f)};
                            Object obj = o.e.a.s.get(2038615114);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(12 - (KeyEvent.getMaxKeyCode() >> 16), (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 459 - View.resolveSize(0, 0));
                                byte b2 = (byte) 0;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                r(b2, b3, b3, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2038615114, obj);
                            }
                            cArr[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            try {
                                Object[] objArr4 = {hVar, hVar};
                                Object obj2 = o.e.a.s.get(-1412673904);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, (char) ((-1) - ImageFormat.getBitsPerPixel(0)), (ViewConfiguration.getTapTimeout() >> 16) + 313);
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    r(b4, b5, (byte) (b5 + 2), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj2);
                                }
                                ((Method) obj2).invoke(null, objArr4);
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    if (i2 > 0) {
                        hVar.c = i2;
                        char[] cArr2 = new char[i3];
                        System.arraycopy(cArr, 0, cArr2, 0, i3);
                        System.arraycopy(cArr2, 0, cArr, i3 - hVar.c, hVar.c);
                        System.arraycopy(cArr2, hVar.c, cArr, 0, i3 - hVar.c);
                    }
                    if (z) {
                        char[] cArr3 = new char[i3];
                        hVar.a = 0;
                        while (true) {
                            switch (hVar.a >= i3) {
                                case true:
                                    cArr = cArr3;
                                    break;
                                default:
                                    int i7 = $10 + 15;
                                    $11 = i7 % 128;
                                    if (i7 % 2 == 0) {
                                    }
                                    cArr3[hVar.a] = cArr[(i3 - hVar.a) - 1];
                                    try {
                                        Object[] objArr6 = {hVar, hVar};
                                        Object obj3 = o.e.a.s.get(-1412673904);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c(Color.rgb(0, 0, 0) + 16777227, (char) Gravity.getAbsoluteGravity(0, 0), 313 - KeyEvent.normalizeMetaState(0));
                                            byte b6 = (byte) 0;
                                            byte b7 = b6;
                                            Object[] objArr7 = new Object[1];
                                            r(b6, b7, (byte) (b7 + 2), objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                            o.e.a.s.put(-1412673904, obj3);
                                        }
                                        ((Method) obj3).invoke(null, objArr6);
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                            }
                        }
                    }
                    objArr[0] = new String(cArr);
                    return;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:18:0x009d, code lost:
        
            if (r2 != null) goto L30;
         */
        /* JADX WARN: Code restructure failed: missing block: B:97:0x029e, code lost:
        
            r3 = r7;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void q(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 866
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.e.q(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f$d.smali */
    public static final class d implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final d b;
        public static final d c;
        public static final d d;
        public static final d e;
        private static int f;
        private static int g;
        private static char[] h;
        private static final /* synthetic */ d[] i;
        private static char j;
        private final String a;

        static void b() {
            h = new char[]{17047, 30586, 17043, 30529, 30554, 30574, 30534, 17053, 17040, 30561, 30553, 17041, 30555, 30583, 30539, 30585, 17042, 30587, 30588, 30572, 30591, 30563, 30556, 17046, 30538, 30540, 30570, 30542, 30559, 30566, 30531, 30571, 17044, 17052, 30517, 30511};
            j = (char) 17043;
        }

        static void init$0() {
            $$a = new byte[]{86, 121, 65, -9};
            $$b = Opcodes.IFLT;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002a). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r7, int r8, short r9, java.lang.Object[] r10) {
            /*
                int r9 = r9 * 4
                int r9 = r9 + 4
                int r8 = r8 + 69
                byte[] r0 = o.eo.f.d.$$a
                int r7 = r7 * 4
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L15
                r8 = r7
                r3 = r9
                r4 = r2
                goto L2a
            L15:
                r3 = r2
            L16:
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                if (r4 != r7) goto L25
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L25:
                r3 = r0[r9]
                r6 = r8
                r8 = r7
                r7 = r6
            L2a:
                int r9 = r9 + 1
                int r7 = r7 + r3
                r3 = r4
                r6 = r8
                r8 = r7
                r7 = r6
                goto L16
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.d.l(short, int, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ d[] a() {
            int i2 = g + 99;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            d[] dVarArr = {e, d, b, c};
            int i5 = i3 + 23;
            g = i5 % 128;
            int i6 = i5 % 2;
            return dVarArr;
        }

        public static d valueOf(String str) {
            int i2 = g + 11;
            f = i2 % 128;
            char c2 = i2 % 2 == 0 ? '5' : 'D';
            d dVar = (d) Enum.valueOf(d.class, str);
            switch (c2) {
                case Opcodes.SALOAD /* 53 */:
                    throw null;
                default:
                    return dVar;
            }
        }

        public static d[] values() {
            int i2 = g + Opcodes.LSUB;
            f = i2 % 128;
            int i3 = i2 % 2;
            d[] dVarArr = (d[]) i.clone();
            int i4 = f + 33;
            g = i4 % 128;
            switch (i4 % 2 != 0 ? 'H' : 'V') {
                case 'H':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVarArr;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            g = 0;
            f = 1;
            b();
            Object[] objArr = new Object[1];
            k(View.getDefaultSize(0, 0) + 8, "\u0007\n\u0001\u0017\u0017#\u000e\u001b", (byte) (Color.alpha(0) + 86), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k(8 - KeyEvent.keyCodeFromString(""), "\t\u0000\u001c\u001a\u0012\f\u0006\u001c", (byte) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 96), objArr2);
            e = new d(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k(7 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "\u0019\u0015\u0017#\u000e\u001b", (byte) ((ViewConfiguration.getTouchSlop() >> 8) + Opcodes.FDIV), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k(5 - TextUtils.lastIndexOf("", '0', 0), "\u001c\u001a\u0012\f\u0006\u001c", (byte) ((-16777198) - Color.rgb(0, 0, 0)), objArr4);
            d = new d(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            k(TextUtils.indexOf("", "", 0) + 9, "\u0013\u0004\u0013\u0015\u001b\b \u0019㙅", (byte) (71 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            k((ViewConfiguration.getJumpTapTimeout() >> 16) + 9, "\u001c\n\u001c\"\u001b\u0000\f\u001a㗽", (byte) (31 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), objArr6);
            b = new d(intern3, 2, ((String) objArr6[0]).intern());
            Object[] objArr7 = new Object[1];
            k(7 - View.MeasureSpec.getMode(0), "\u0014 \u0014\u001b\u000e\u001d㘪", (byte) (45 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            k(7 - Color.red(0), "\f\u001a\u0000\u001e\u0012\u001e㘬", (byte) (77 - ExpandableListView.getPackedPositionChild(0L)), objArr8);
            c = new d(intern4, 3, ((String) objArr8[0]).intern());
            i = a();
            int i2 = f + 29;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        private d(String str, int i2, String str2) {
            this.a = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = f + 43;
            int i3 = i2 % 128;
            g = i3;
            int i4 = i2 % 2;
            String str = this.a;
            int i5 = i3 + 27;
            f = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        /* JADX WARN: Removed duplicated region for block: B:11:0x0034  */
        /* JADX WARN: Removed duplicated region for block: B:14:0x0037  */
        /* JADX WARN: Removed duplicated region for block: B:16:0x003a  */
        /* JADX WARN: Removed duplicated region for block: B:18:0x0046  */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final fr.antelop.sdk.digitalcard.Token.Status d() {
            /*
                r7 = this;
                int r0 = o.eo.f.d.f
                int r0 = r0 + 33
                int r1 = r0 % 128
                o.eo.f.d.g = r1
                int r0 = r0 % 2
                if (r0 == 0) goto Lf
                r0 = 95
                goto L11
            Lf:
                r0 = 43
            L11:
                r1 = 1
                r2 = 0
                switch(r0) {
                    case 43: goto L1f;
                    default: goto L16;
                }
            L16:
                int[] r0 = o.eo.f.AnonymousClass3.b
                int r3 = r7.ordinal()
                r0 = r0[r3]
                goto L2b
            L1f:
                int[] r0 = o.eo.f.AnonymousClass3.b
                int r3 = r7.ordinal()
                r0 = r0[r3]
                switch(r0) {
                    case 1: goto L3a;
                    case 2: goto L37;
                    case 3: goto L34;
                    case 4: goto L32;
                    default: goto L2a;
                }
            L2a:
                goto L46
            L2b:
                r3 = 10
                int r3 = r3 / r2
                switch(r0) {
                    case 1: goto L3a;
                    case 2: goto L37;
                    case 3: goto L34;
                    case 4: goto L32;
                    default: goto L31;
                }
            L31:
                goto L2a
            L32:
                r0 = 0
                return r0
            L34:
                fr.antelop.sdk.digitalcard.Token$Status r0 = fr.antelop.sdk.digitalcard.Token.Status.Inactive
                return r0
            L37:
                fr.antelop.sdk.digitalcard.Token$Status r0 = fr.antelop.sdk.digitalcard.Token.Status.Suspended
                return r0
            L3a:
                fr.antelop.sdk.digitalcard.Token$Status r0 = fr.antelop.sdk.digitalcard.Token.Status.Active
                int r2 = o.eo.f.d.g
                int r2 = r2 + r1
                int r1 = r2 % 128
                o.eo.f.d.f = r1
                int r2 = r2 % 2
                return r0
            L46:
                java.lang.UnsupportedOperationException r0 = new java.lang.UnsupportedOperationException
                java.lang.StringBuilder r3 = new java.lang.StringBuilder
                r3.<init>()
                java.lang.String r4 = ""
                r5 = 48
                int r4 = android.text.TextUtils.lastIndexOf(r4, r5, r2, r2)
                int r4 = r4 + 19
                int r5 = android.view.Gravity.getAbsoluteGravity(r2, r2)
                int r5 = 93 - r5
                byte r5 = (byte) r5
                java.lang.Object[] r1 = new java.lang.Object[r1]
                java.lang.String r6 = "\u0003\n\u0019\u000e\u001a \u0017\r\u0019 \"\u000b\u0003\u0017\u0002\u0019#\u001e"
                k(r4, r6, r5, r1)
                r1 = r1[r2]
                java.lang.String r1 = (java.lang.String) r1
                java.lang.String r1 = r1.intern()
                java.lang.StringBuilder r1 = r3.append(r1)
                java.lang.String r2 = r7.name()
                java.lang.StringBuilder r1 = r1.append(r2)
                java.lang.String r1 = r1.toString()
                r0.<init>(r1)
                throw r0
            L81:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.f.d.d():fr.antelop.sdk.digitalcard.Token$Status");
        }

        private static void k(int i2, String str, byte b2, Object[] objArr) {
            char[] cArr;
            int i3;
            int i4 = $10 + 7;
            int i5 = i4 % 128;
            $11 = i5;
            int i6 = 2;
            if (i4 % 2 == 0) {
                Object obj = null;
                obj.hashCode();
                throw null;
            }
            if (str != null) {
                int i7 = i5 + 23;
                $10 = i7 % 128;
                int i8 = i7 % 2;
                cArr = str.toCharArray();
            } else {
                cArr = str;
            }
            char[] cArr2 = cArr;
            m mVar = new m();
            char[] cArr3 = h;
            switch (cArr3 != null ? 'I' : '^') {
                case Opcodes.DUP2_X2 /* 94 */:
                    break;
                default:
                    int length = cArr3.length;
                    char[] cArr4 = new char[length];
                    int i9 = 0;
                    while (i9 < length) {
                        int i10 = $10 + 59;
                        $11 = i10 % 128;
                        int i11 = i10 % i6;
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr3[i9])};
                            Object obj2 = o.e.a.s.get(-1401577988);
                            if (obj2 == null) {
                                Class cls = (Class) o.e.a.c(Drawable.resolveOpacity(0, 0) + 17, (char) Color.argb(0, 0, 0, 0), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 76);
                                byte length2 = (byte) $$a.length;
                                Object[] objArr3 = new Object[1];
                                l((byte) 0, length2, (byte) (length2 - 4), objArr3);
                                obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj2);
                            }
                            cArr4[i9] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                            i9++;
                            i6 = 2;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr3 = cArr4;
                    break;
            }
            try {
                Object[] objArr4 = {Integer.valueOf(j)};
                Object obj3 = o.e.a.s.get(-1401577988);
                if (obj3 == null) {
                    Class cls2 = (Class) o.e.a.c(17 - TextUtils.getTrimmedLength(""), (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 76 - KeyEvent.getDeadChar(0, 0));
                    byte length3 = (byte) $$a.length;
                    Object[] objArr5 = new Object[1];
                    l((byte) 0, length3, (byte) (length3 - 4), objArr5);
                    obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj3);
                }
                char charValue = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
                char[] cArr5 = new char[i2];
                if (i2 % 2 != 0) {
                    int i12 = $11;
                    int i13 = i12 + Opcodes.LMUL;
                    $10 = i13 % 128;
                    int i14 = i13 % 2;
                    i3 = i2 - 1;
                    cArr5[i3] = (char) (cArr2[i3] - b2);
                    int i15 = i12 + 57;
                    $10 = i15 % 128;
                    int i16 = i15 % 2;
                } else {
                    i3 = i2;
                }
                if (i3 > 1) {
                    int i17 = $11 + 51;
                    $10 = i17 % 128;
                    int i18 = i17 % 2;
                    mVar.b = 0;
                    while (mVar.b < i3) {
                        mVar.e = cArr2[mVar.b];
                        mVar.a = cArr2[mVar.b + 1];
                        if (mVar.e == mVar.a) {
                            int i19 = $11 + 51;
                            $10 = i19 % 128;
                            int i20 = i19 % 2;
                            cArr5[mVar.b] = (char) (mVar.e - b2);
                            cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                            int i21 = $11 + 1;
                            $10 = i21 % 128;
                            int i22 = i21 % 2;
                        } else {
                            try {
                                Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                Object obj4 = o.e.a.s.get(696901393);
                                if (obj4 == null) {
                                    Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 10, (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 8855), 323 - ExpandableListView.getPackedPositionChild(0L));
                                    byte b3 = (byte) 0;
                                    byte b4 = b3;
                                    Object[] objArr7 = new Object[1];
                                    l(b3, b4, b4, objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                    o.e.a.s.put(696901393, obj4);
                                }
                                if (((Integer) ((Method) obj4).invoke(null, objArr6)).intValue() == mVar.h) {
                                    try {
                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                        Object obj5 = o.e.a.s.get(1075449051);
                                        if (obj5 == null) {
                                            Class cls4 = (Class) o.e.a.c(11 - TextUtils.indexOf("", "", 0), (char) TextUtils.getOffsetBefore("", 0), 65 - KeyEvent.keyCodeFromString(""));
                                            byte b5 = (byte) 0;
                                            byte b6 = (byte) (b5 + 1);
                                            Object[] objArr9 = new Object[1];
                                            l(b5, b6, (byte) (b6 - 1), objArr9);
                                            obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(1075449051, obj5);
                                        }
                                        int intValue = ((Integer) ((Method) obj5).invoke(null, objArr8)).intValue();
                                        int i23 = (mVar.d * charValue) + mVar.h;
                                        cArr5[mVar.b] = cArr3[intValue];
                                        cArr5[mVar.b + 1] = cArr3[i23];
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                } else if (mVar.c == mVar.d) {
                                    int i24 = $11 + 3;
                                    $10 = i24 % 128;
                                    int i25 = i24 % 2;
                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                    int i26 = (mVar.c * charValue) + mVar.i;
                                    int i27 = (mVar.d * charValue) + mVar.h;
                                    cArr5[mVar.b] = cArr3[i26];
                                    cArr5[mVar.b + 1] = cArr3[i27];
                                } else {
                                    int i28 = (mVar.c * charValue) + mVar.h;
                                    int i29 = (mVar.d * charValue) + mVar.i;
                                    cArr5[mVar.b] = cArr3[i28];
                                    cArr5[mVar.b + 1] = cArr3[i29];
                                }
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        mVar.b += 2;
                    }
                }
                int i30 = $11 + 45;
                $10 = i30 % 128;
                int i31 = i30 % 2;
                int i32 = 0;
                while (true) {
                    switch (i32 >= i2) {
                        case false:
                            cArr5[i32] = (char) (cArr5[i32] ^ 13722);
                            i32++;
                        default:
                            objArr[0] = new String(cArr5);
                            return;
                    }
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /* renamed from: o.eo.f$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\f$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] a;
        static final /* synthetic */ int[] b;
        private static int c;
        static final /* synthetic */ int[] d;
        private static int e;

        /* JADX WARN: Failed to find 'out' block for switch in B:26:0x009f. Please report as an issue. */
        /* JADX WARN: Failed to find 'out' block for switch in B:32:0x00be. Please report as an issue. */
        static {
            e = 0;
            c = 1;
            int[] iArr = new int[d.values().length];
            b = iArr;
            try {
                iArr[d.d.ordinal()] = 1;
                int i = c;
                int i2 = (i ^ 3) + ((i & 3) << 1);
                e = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[d.b.ordinal()] = 2;
                int i4 = c;
                int i5 = ((i4 | 13) << 1) - (i4 ^ 13);
                e = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                b[d.e.ordinal()] = 3;
                int i7 = (e + 44) - 1;
                c = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                b[d.c.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            int[] iArr2 = new int[e.values().length];
            a = iArr2;
            try {
                iArr2[e.d.ordinal()] = 1;
            } catch (NoSuchFieldError e6) {
            }
            try {
                a[e.c.ordinal()] = 2;
            } catch (NoSuchFieldError e7) {
            }
            try {
                a[e.a.ordinal()] = 3;
                int i9 = e;
                int i10 = ((i9 | 29) << 1) - (i9 ^ 29);
                c = i10 % 128;
                switch (i10 % 2 == 0 ? (char) 6 : 'Y') {
                }
            } catch (NoSuchFieldError e8) {
            }
            try {
                a[e.e.ordinal()] = 4;
                int i11 = e;
                int i12 = (i11 ^ 69) + ((i11 & 69) << 1);
                c = i12 % 128;
                switch (i12 % 2 == 0) {
                }
            } catch (NoSuchFieldError e9) {
            }
            try {
                a[e.b.ordinal()] = 5;
            } catch (NoSuchFieldError e10) {
            }
            int[] iArr3 = new int[b.values().length];
            d = iArr3;
            try {
                iArr3[b.b.ordinal()] = 1;
            } catch (NoSuchFieldError e11) {
            }
            try {
                d[b.a.ordinal()] = 2;
            } catch (NoSuchFieldError e12) {
            }
        }
    }
}
