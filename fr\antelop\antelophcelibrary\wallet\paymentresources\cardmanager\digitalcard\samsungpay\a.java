package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Bundle;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.samsung.android.sdk.samsungpay.v2.card.CardManager;
import java.util.ArrayList;
import java.util.List;
import kotlin.text.Typography;
import o.an.h;
import o.ee.g;
import o.ee.i;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\a.smali */
public final class a extends c {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static final a f;
    private static long g;
    private static int h;
    private static char[] i;

    /* renamed from: o, reason: collision with root package name */
    private static int f32o;

    static void d() {
        i = new char[]{11418, 62061, 37166, 45301, 22440, 30078, 5168, 15354, 55936, 63581, 40748, 48863, 23961, 31908, 618, 8502, 11418, 62029, 37134, 45269, 22408, 30046, 5136, 15349, 55985, 63589, 40738, 48865, 23969, 31879, 580, 8455, 49367, 59279, 27184, 46327, 55209, 63055, 4386, 13282, 21157, 32081, 39947, 48883, 55691, 63558, 6916, 14966, 17596, 26620, 34416, 41251, 50145, 58020, 3414, 11274, 20121, 27102, 34831, 60421, 13007, 20879, 28673, 38740, 46469, 54427, 64380, 6701, 21668, 35427, 59709, 51400, 12210, 3436, 27700, 17347, 41614, 32894, 59152, 50893, 9640, 1187, 31337, 22820, 47350, 40866, 64842, 56360, 13254, 4766, 28760, 22275, 11438, 62057, 37175, 45251, 22451, 30050, 5176, 15302, 55949, 63553, 40735, 48874, 23962, 31907, 618, 8492, 49386, 49918, 7230, 32631, 24233, 47577, 39734, 64098, 54665, 13454, 5706};
        g = -2028177873800203764L;
    }

    static void init$0() {
        $$d = new byte[]{89, -101, -47, 112};
        $$e = 114;
    }

    private static void t(int i2, short s, byte b, Object[] objArr) {
        int i3 = (s * 3) + 1;
        int i4 = (i2 * 4) + 4;
        byte[] bArr = $$d;
        int i5 = 105 - b;
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            i5 = i7 + i4;
            i4++;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
        }
        while (true) {
            int i8 = i6 + 1;
            bArr2[i8] = (byte) i5;
            if (i8 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i5 += bArr[i4];
            i4++;
            i7 = i7;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i8;
        }
    }

    @Override // o.ep.a
    public final /* synthetic */ void e(Activity activity, a.c cVar, i iVar, o.eo.e eVar, o.ep.e eVar2, h.d dVar, o.ee.h hVar) {
        int i2 = f32o + 9;
        h = i2 % 128;
        int i3 = i2 % 2;
        d(activity, cVar, eVar, dVar);
        int i4 = h + 85;
        f32o = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f32o = 1;
        d();
        KeyEvent.normalizeMetaState(0);
        Process.myTid();
        TextUtils.indexOf("", "");
        f = new a();
        int i2 = h + 85;
        f32o = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    final String a() {
        Object obj;
        int i2 = f32o + Opcodes.LREM;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                s((char) Color.argb(0, 0, 0, 0), (-1) - TextUtils.indexOf((CharSequence) "", '0', 0), 16 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                s((char) Color.argb(1, 1, 1, 0), (-1) << TextUtils.indexOf((CharSequence) "", 'y', 1), 73 << (ViewConfiguration.getScrollBarSize() / Opcodes.DSUB), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = h + 9;
        f32o = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    final String e() {
        Object obj;
        int i2 = h + 9;
        f32o = i2 % 128;
        switch (i2 % 2 == 0 ? 'M' : 'V') {
            case Opcodes.SASTORE /* 86 */:
                Object[] objArr = new Object[1];
                s((char) TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 15, 19 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                s((char) TextUtils.indexOf("", "", 0, 1), Opcodes.LUSHR >> (ViewConfiguration.getGlobalActionKeyTimeout() > 1L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 1L ? 0 : -1)), Opcodes.FMUL << (AudioTrack.getMaxVolume() > 1.0f ? 1 : (AudioTrack.getMaxVolume() == 1.0f ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = h + 87;
        f32o = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    public static a d(Context context) {
        int i2 = h + 71;
        f32o = i2 % 128;
        int i3 = i2 % 2;
        switch (c != null) {
            case false:
                int i4 = f32o + Opcodes.DSUB;
                h = i4 % 128;
                switch (i4 % 2 != 0 ? Typography.dollar : 'T') {
                    case Opcodes.BASTORE /* 84 */:
                        f.c(context);
                        break;
                    default:
                        f.c(context);
                        int i5 = 18 / 0;
                        break;
                }
        }
        return f;
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    protected final void c(Context context) {
        super.c(context);
        c = new CardManager(context, this.b);
        int i2 = f32o + 67;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'c' : 'N') {
            case Opcodes.DADD /* 99 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.c
    public final void b(a.InterfaceC0042a<String> interfaceC0042a, Boolean bool, String str) {
        g.c();
        String a = a();
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        s((char) (18078 - View.MeasureSpec.getMode(0)), ((byte) KeyEvent.getModifierMetaStateMask()) + 35, ExpandableListView.getPackedPositionGroup(0L) + 25, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(bool);
        Object[] objArr2 = new Object[1];
        s((char) (TextUtils.getOffsetAfter("", 0) + 49388), 58 - TextUtils.indexOf((CharSequence) "", '0', 0), KeyEvent.keyCodeFromString("") + 9, objArr2);
        g.d(a, append.append(((String) objArr2[0]).intern()).append(str).toString());
        ArrayList arrayList = new ArrayList();
        arrayList.add(str);
        this.a.getWalletInfo(arrayList, new SamsungPayInfoListener(this, interfaceC0042a, bool, str));
        int i2 = f32o + 35;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // o.ep.a
    public final void e(a.InterfaceC0042a<a.b> interfaceC0042a) {
        g.c();
        String a = a();
        Object[] objArr = new Object[1];
        s((char) (30730 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 68, 24 - Color.alpha(0), objArr);
        g.d(a, ((String) objArr[0]).intern());
        switch (1) {
            case 1:
                break;
            default:
                int i2 = h + 93;
                f32o = i2 % 128;
                int i3 = i2 % 2;
                interfaceC0042a.e((a.InterfaceC0042a<a.b>) a.b.b);
                int i4 = f32o + 37;
                h = i4 % 128;
                if (i4 % 2 != 0) {
                    break;
                }
                break;
        }
        this.a.getSamsungPayStatus(new SamsungPayStatusListener(interfaceC0042a));
    }

    @Override // o.ep.a
    public final void d(a.InterfaceC0042a<List<o.ep.e>> interfaceC0042a) {
        g.c();
        Object[] objArr = new Object[1];
        s((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), Process.getGidForName("") + 1, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        s((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getJumpTapTimeout() >> 16) + 92, 17 - View.MeasureSpec.getMode(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        c.getAllCards(new Bundle(), new SamsungPayGetCardListener(this, interfaceC0042a));
        int i2 = f32o + 67;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 25 : (char) 14) {
            case 25:
                int i3 = 76 / 0;
                return;
            default:
                return;
        }
    }

    private void d(Activity activity, a.c cVar, o.eo.e eVar, h.d dVar) {
        g.c();
        Object[] objArr = new Object[1];
        s((char) View.combineMeasuredStates(0, 0), ViewConfiguration.getFadingEdgeLength() >> 16, Color.blue(0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        s((char) (60999 - KeyEvent.keyCodeFromString("")), 109 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.getOffsetAfter("", 0) + 10, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        c.addCard(b(dVar), new SamsungPayAddCardListener(this, activity, eVar.s().a(), cVar));
        int i2 = f32o + Opcodes.DMUL;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'Y' : Typography.quote) {
            case '\"':
                return;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void s(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 590
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.a.s(char, int, int, java.lang.Object[]):void");
    }
}
