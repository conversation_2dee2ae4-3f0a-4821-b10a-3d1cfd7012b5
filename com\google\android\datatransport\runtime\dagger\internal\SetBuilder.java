package com.google.android.datatransport.runtime.dagger.internal;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\dagger\internal\SetBuilder.smali */
public final class SetBuilder<T> {
    private static final String SET_CONTRIBUTIONS_CANNOT_BE_NULL = "Set contributions cannot be null";
    private final List<T> contributions;

    private SetBuilder(int estimatedSize) {
        this.contributions = new ArrayList(estimatedSize);
    }

    public static <T> SetBuilder<T> newSetBuilder(int estimatedSize) {
        return new SetBuilder<>(estimatedSize);
    }

    public SetBuilder<T> add(T t) {
        this.contributions.add(Preconditions.checkNotNull(t, SET_CONTRIBUTIONS_CANNOT_BE_NULL));
        return this;
    }

    public SetBuilder<T> addAll(Collection<? extends T> collection) {
        for (T item : collection) {
            Preconditions.checkNotNull(item, SET_CONTRIBUTIONS_CANNOT_BE_NULL);
        }
        this.contributions.addAll(collection);
        return this;
    }

    public Set<T> build() {
        switch (this.contributions.size()) {
            case 0:
                return Collections.emptySet();
            case 1:
                return Collections.singleton(this.contributions.get(0));
            default:
                return Collections.unmodifiableSet(new HashSet(this.contributions));
        }
    }
}
