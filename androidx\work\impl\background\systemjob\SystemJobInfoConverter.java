package androidx.work.impl.background.systemjob;

import android.app.job.JobInfo;
import android.content.ComponentName;
import android.content.Context;
import android.net.NetworkRequest;
import android.os.Build;
import android.os.PersistableBundle;
import androidx.work.BackoffPolicy;
import androidx.work.Constraints;
import androidx.work.Logger;
import androidx.work.NetworkType;
import androidx.work.impl.model.WorkSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\background\systemjob\SystemJobInfoConverter.smali */
class SystemJobInfoConverter {
    static final String EXTRA_IS_PERIODIC = "EXTRA_IS_PERIODIC";
    static final String EXTRA_WORK_SPEC_GENERATION = "EXTRA_WORK_SPEC_GENERATION";
    static final String EXTRA_WORK_SPEC_ID = "EXTRA_WORK_SPEC_ID";
    private static final String TAG = Logger.tagWithPrefix("SystemJobInfoConverter");
    private final ComponentName mWorkServiceComponent;

    SystemJobInfoConverter(Context context) {
        Context appContext = context.getApplicationContext();
        this.mWorkServiceComponent = new ComponentName(appContext, (Class<?>) SystemJobService.class);
    }

    JobInfo convert(WorkSpec workSpec, int jobId) {
        Constraints constraints = workSpec.constraints;
        PersistableBundle extras = new PersistableBundle();
        extras.putString(EXTRA_WORK_SPEC_ID, workSpec.id);
        extras.putInt(EXTRA_WORK_SPEC_GENERATION, workSpec.getGeneration());
        extras.putBoolean(EXTRA_IS_PERIODIC, workSpec.isPeriodic());
        JobInfo.Builder builder = new JobInfo.Builder(jobId, this.mWorkServiceComponent).setRequiresCharging(constraints.getRequiresCharging()).setRequiresDeviceIdle(constraints.getRequiresDeviceIdle()).setExtras(extras);
        setRequiredNetwork(builder, constraints.getRequiredNetworkType());
        if (!constraints.getRequiresDeviceIdle()) {
            int backoffPolicy = workSpec.backoffPolicy == BackoffPolicy.LINEAR ? 0 : 1;
            builder.setBackoffCriteria(workSpec.backoffDelayDuration, backoffPolicy);
        }
        long nextRunTime = workSpec.calculateNextRunTime();
        long now = System.currentTimeMillis();
        long offset = Math.max(nextRunTime - now, 0L);
        if (Build.VERSION.SDK_INT <= 28) {
            builder.setMinimumLatency(offset);
        } else if (offset > 0) {
            builder.setMinimumLatency(offset);
        } else if (!workSpec.expedited) {
            builder.setImportantWhileForeground(true);
        }
        if (constraints.hasContentUriTriggers()) {
            for (Constraints.ContentUriTrigger trigger : constraints.getContentUriTriggers()) {
                builder.addTriggerContentUri(convertContentUriTrigger(trigger));
            }
            builder.setTriggerContentUpdateDelay(constraints.getContentTriggerUpdateDelayMillis());
            builder.setTriggerContentMaxDelay(constraints.getContentTriggerMaxDelayMillis());
        }
        builder.setPersisted(false);
        builder.setRequiresBatteryNotLow(constraints.getRequiresBatteryNotLow());
        builder.setRequiresStorageNotLow(constraints.getRequiresStorageNotLow());
        boolean isRetry = workSpec.runAttemptCount > 0;
        boolean isDelayed = offset > 0;
        if (Build.VERSION.SDK_INT >= 31 && workSpec.expedited && !isRetry && !isDelayed) {
            builder.setExpedited(true);
        }
        return builder.build();
    }

    private static JobInfo.TriggerContentUri convertContentUriTrigger(Constraints.ContentUriTrigger trigger) {
        int flag = trigger.getIsTriggeredForDescendants() ? 1 : 0;
        return new JobInfo.TriggerContentUri(trigger.getUri(), flag);
    }

    static void setRequiredNetwork(JobInfo.Builder builder, NetworkType networkType) {
        if (Build.VERSION.SDK_INT >= 30 && networkType == NetworkType.TEMPORARILY_UNMETERED) {
            NetworkRequest networkRequest = new NetworkRequest.Builder().addCapability(25).build();
            builder.setRequiredNetwork(networkRequest);
        } else {
            builder.setRequiredNetworkType(convertNetworkType(networkType));
        }
    }

    /* renamed from: androidx.work.impl.background.systemjob.SystemJobInfoConverter$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\work\impl\background\systemjob\SystemJobInfoConverter$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] $SwitchMap$androidx$work$NetworkType;

        static {
            int[] iArr = new int[NetworkType.values().length];
            $SwitchMap$androidx$work$NetworkType = iArr;
            try {
                iArr[NetworkType.NOT_REQUIRED.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                $SwitchMap$androidx$work$NetworkType[NetworkType.CONNECTED.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                $SwitchMap$androidx$work$NetworkType[NetworkType.UNMETERED.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                $SwitchMap$androidx$work$NetworkType[NetworkType.NOT_ROAMING.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                $SwitchMap$androidx$work$NetworkType[NetworkType.METERED.ordinal()] = 5;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    static int convertNetworkType(NetworkType networkType) {
        switch (AnonymousClass1.$SwitchMap$androidx$work$NetworkType[networkType.ordinal()]) {
            case 1:
                break;
            case 2:
                break;
            case 3:
                break;
            case 4:
                break;
            case 5:
                break;
            default:
                Logger.get().debug(TAG, "API version too low. Cannot convert network type value " + networkType);
                break;
        }
        return 1;
    }
}
