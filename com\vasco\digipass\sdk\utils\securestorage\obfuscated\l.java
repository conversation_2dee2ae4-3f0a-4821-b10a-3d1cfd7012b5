package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import java.util.HashMap;
import java.util.Map;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\l.smali */
public final class l {
    public final Map a;
    public final Map b;

    public /* synthetic */ l() {
        this(new HashMap(), new HashMap());
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof l)) {
            return false;
        }
        l lVar = (l) obj;
        return Intrinsics.areEqual(this.a, lVar.a) && Intrinsics.areEqual(this.b, lVar.b);
    }

    public final int hashCode() {
        return this.b.hashCode() + (this.a.hashCode() * 31);
    }

    public final String toString() {
        return "SecureStorageData(stringsMap=" + this.a + ", bytesMap=" + this.b + ')';
    }

    public l(Map stringsMap, Map bytesMap) {
        Intrinsics.checkNotNullParameter(stringsMap, "stringsMap");
        Intrinsics.checkNotNullParameter(bytesMap, "bytesMap");
        this.a = stringsMap;
        this.b = bytesMap;
    }
}
