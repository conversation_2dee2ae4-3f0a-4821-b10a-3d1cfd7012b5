package o.de;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.ComponentCallbacks;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.IntentSender;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.database.DatabaseErrorHandler;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.os.Process;
import android.os.SystemClock;
import android.os.UserHandle;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Display;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.exposed.AntelopLifecycleService;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.lang.reflect.Method;
import java.util.concurrent.Executor;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.m;
import o.de.a;
import o.ee.g;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static short[] b;
    private static int c;
    private static byte[] d;
    private static int e;
    private static int g;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        j = 1;
        d();
        ViewConfiguration.getGlobalActionKeyTimeout();
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getMinimumFlingVelocity();
        TypedValue.complexToFloat(0);
        ViewConfiguration.getGlobalActionKeyTimeout();
        KeyEvent.getModifierMetaStateMask();
        SystemClock.elapsedRealtimeNanos();
        View.MeasureSpec.getMode(0);
        ExpandableListView.getPackedPositionType(0L);
        SystemClock.elapsedRealtimeNanos();
        KeyEvent.getMaxKeyCode();
        ViewConfiguration.getTapTimeout();
        TextUtils.indexOf("", "");
        ViewConfiguration.getKeyRepeatTimeout();
        ViewConfiguration.getEdgeSlop();
        KeyEvent.keyCodeFromString("");
        Color.blue(0);
        AudioTrack.getMinVolume();
        ViewConfiguration.getZoomControlsTimeout();
        TextUtils.indexOf("", "");
        int i = j + 85;
        g = i % 128;
        switch (i % 2 != 0 ? 'Y' : '?') {
            case Opcodes.DUP /* 89 */:
                int i2 = 92 / 0;
                break;
        }
    }

    static void d() {
        d = new byte[]{-79, -113, -31, -89, -74, -34, -90, -36, -58, -44, -92, -115, -85, -71, UtilitiesSDKConstants.SRP_LABEL_ENC, -45, -94, -60, -74, -71, -63, 121, 74, 100, 74, 115, 106, 91, 74, 98, 90, 100, 122, 108, 92, 117, -105, 73, 112, 111, 94, 124, 74, 73, 97, 30, 99, 121, 1, 105, 7, 9, 31, 111, 120, 22, 124, 115, 30, 109, 15, 121, 124, 4, -75, -21, -72, -31, -41, -24, -25, -35, 29, -31, -79, -18, -118, -30, -29, -27, -123, 27, -23, -121, -32, -67, -29, -32, -72, -36, 95, 86, 29, -48, 89, -111, 104, 83, 90, 105, 8, -82, 110, 82, 98, 89, 70, 122, 86, 8, 117, 113, -83, 104, 50, -123, 89, 70, 122, 86, 117, -41, 121, -59, 23, 48, -33, 78, -58, 77, -60, -89, -67, 98, -82, 15, -58, 78, 119, -64, -39, 118, 23, 56, -58, -43, 121, -59, 23, 53, 79, Tnaf.POW_2_WIDTH, 15, 77, -34, 77, 114, -60, -64, 75, -57, -64, 29, 119, -39, 10, 119, -95, -110, -58, -43, 121, -59, -112, -112, -112, -112, -112, -112};
        c = 909053676;
        a = -2066686794;
        e = -780710539;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = r6 * 2
            int r6 = r6 + 108
            int r7 = r7 * 4
            int r7 = 3 - r7
            byte[] r0 = o.de.e.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L38
        L1c:
            r3 = r2
        L1d:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L38:
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.e.h(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{81, -74, 18, 60};
        $$b = 114;
    }

    public static boolean c(Context context, b bVar, String str) {
        Bundle bundle = new Bundle();
        Object[] objArr = new Object[1];
        f((byte) ((-111) - ImageFormat.getBitsPerPixel(0)), 413645851 - Color.blue(0), (short) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 70), (-102) - KeyEvent.keyCodeFromString(""), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 1291857469, objArr);
        bundle.putString(((String) objArr[0]).intern(), bVar.b().toString());
        Object[] objArr2 = new Object[1];
        f((byte) ((-116) - TextUtils.getTrimmedLength("")), 413645871 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (short) ((-103) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), TextUtils.lastIndexOf("", '0') - 98, 1291857469 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        bundle.putString(((String) objArr2[0]).intern(), bVar.c().toString());
        Object[] objArr3 = new Object[1];
        f((byte) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) - 42), 413645896 - View.MeasureSpec.getSize(0), (short) (TextUtils.lastIndexOf("", '0', 0, 0) + 64), (-105) - TextUtils.indexOf((CharSequence) "", '0', 0), 1291857468 - ImageFormat.getBitsPerPixel(0), objArr3);
        bundle.putString(((String) objArr3[0]).intern(), str);
        Intent intent = new Intent(context, (Class<?>) AntelopLifecycleService.class);
        intent.putExtras(bundle);
        try {
            g.c();
            Object[] objArr4 = new Object[1];
            f((byte) ((-37) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), TextUtils.getOffsetAfter("", 0) + 413645915, (short) (Color.red(0) - 78), ExpandableListView.getPackedPositionGroup(0L) - 97, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1291857443, objArr4);
            String intern = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            f((byte) ((Process.myTid() >> 22) - 27), 413645940 - Process.getGidForName(""), (short) (TextUtils.lastIndexOf("", '0') + 31), (ViewConfiguration.getTouchSlop() >> 8) - 93, View.MeasureSpec.makeMeasureSpec(0, 0) + 1291857485, objArr5);
            g.d(intern, ((String) objArr5[0]).intern());
            context.startService(intent);
            int i = j + 89;
            g = i % 128;
            switch (i % 2 != 0 ? 'J' : (char) 30) {
                case 30:
                    return true;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (IllegalStateException e2) {
            g.c();
            Object[] objArr6 = new Object[1];
            f((byte) (((Process.getThreadPriority(0) + 20) >> 6) - 38), 413645915 - Color.blue(0), (short) ((-78) - View.MeasureSpec.makeMeasureSpec(0, 0)), (-97) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), Gravity.getAbsoluteGravity(0, 0) + 1291857443, objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            f((byte) ((-69) - TextUtils.lastIndexOf("", '0')), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 413645970, (short) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.DSUB), (-70) - View.MeasureSpec.getSize(0), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1291857485, objArr7);
            g.a(intern2, ((String) objArr7[0]).intern(), e2);
            return false;
        }
    }

    public static void b(Context context) {
        context.stopService(new Intent(context, (Class<?>) AntelopLifecycleService.class));
        int i = g + 63;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* renamed from: o.de.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\e$e.smali */
    public static class ServiceC0036e extends Service implements a.e {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char a;
        private static int b;
        private static int c;
        private static char[] d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            b = 1;
            c = 874635472;
            d = new char[]{30498, 30570, 30591, 30584, 30565, 30562, 30534, 30509, 30574, 30556, 30531, 30571, 30589, 30572, 30539, 30563, 30511, 30573, 30567, 30564, 30568, 30569, 30588, 30587, 30510, 30508, 30561, 30533, 30554, 30530, 30560, 30566, 30555, 30540, 30582, 30586};
            a = (char) 17043;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(short r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 4
                int r6 = r6 + 1
                byte[] r0 = o.de.e.ServiceC0036e.$$a
                int r7 = r7 + 69
                int r8 = r8 * 3
                int r8 = 4 - r8
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r7 = r6
                r3 = r8
                r4 = r2
                goto L2c
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r7
                r1[r3] = r4
                int r4 = r3 + 1
                if (r3 != r6) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                r3 = r0[r8]
                r5 = r7
                r7 = r6
                r6 = r5
            L2c:
                int r8 = r8 + 1
                int r3 = -r3
                int r6 = r6 + r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.de.e.ServiceC0036e.h(short, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{99, 114, -3, -82};
            $$b = 23;
        }

        @Override // android.app.Service
        public int onStartCommand(Intent intent, int i, int i2) {
            Object[] objArr = new Object[1];
            f((ViewConfiguration.getPressedStateDuration() >> 16) + 13, "\u0014\ufffe\u0007\u0000￥\n�￭\u0010\t\t\u0000\r￤\b\b\u0000\uffff\u0004￼\u000f\u0000\uffe7\u0004\u0001\u0000\ufffe", TextUtils.getTrimmedLength("") + 27, 276 - Color.alpha(0), false, objArr);
            String intern = ((String) objArr[0]).intern();
            if (intent == null) {
                o.av.a aVar = o.av.a.d;
                Object[] objArr2 = new Object[1];
                f(2 - TextUtils.indexOf("", "", 0, 0), "\ufff9\u0006\u0006\ufff9\n\u0003\ufff7", ((byte) KeyEvent.getModifierMetaStateMask()) + 8, 283 - View.resolveSize(0, 0), true, objArr2);
                new a(this, this, aVar, ((String) objArr2[0]).intern(), f.k).d();
                return 2;
            }
            switch (intent.getExtras() != null) {
                case false:
                    g.c();
                    Object[] objArr3 = new Object[1];
                    g(37 - KeyEvent.normalizeMetaState(0), " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u000f\u001c#\f\u000e\u0004\u0006\u000e\u000b\u0002\u0005\u0013\u0000\r\u001c\u0016\u0006\u000e\u0004\r\u001d 㘵㘵㗹", (byte) (63 - Drawable.resolveOpacity(0, 0)), objArr3);
                    g.e(intern, ((String) objArr3[0]).intern());
                    stopSelf();
                    break;
                default:
                    Bundle extras = intent.getExtras();
                    Object[] objArr4 = new Object[1];
                    g(22 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "\f\u0013\u0007\u0013\u0016\u0019\u0005\b\u000b\u0014\u0005\u0019\u0004\u0013\u0014\n\u0013\u0002!#\u0003\u0002", (byte) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 90), objArr4);
                    o.av.a b2 = o.av.a.b(extras.getString(((String) objArr4[0]).intern()));
                    switch (b2 != null) {
                        case false:
                            int i3 = e + 95;
                            b = i3 % 128;
                            if (i3 % 2 != 0) {
                                g.c();
                                Object[] objArr5 = new Object[1];
                                g(49 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u000f\"\u0013\u0000\u0013\u0019\u0011\"\u0005\b\u000b\u0014\u0004\r\u0000\u0002㘈㘈\u000e\u001a\u0004\r\u0016#\u0003\u0002\u000e\u001c#\u0012\u000f\u0016\u001f\u001e\u001d\b", (byte) (30 - MotionEvent.axisFromString("")), objArr5);
                                g.e(intern, ((String) objArr5[0]).intern());
                                stopSelf();
                                break;
                            } else {
                                g.c();
                                Object[] objArr6 = new Object[1];
                                g(Opcodes.FMUL << (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u000f\"\u0013\u0000\u0013\u0019\u0011\"\u0005\b\u000b\u0014\u0004\r\u0000\u0002㘈㘈\u000e\u001a\u0004\r\u0016#\u0003\u0002\u000e\u001c#\u0012\u000f\u0016\u001f\u001e\u001d\b", (byte) (19 - MotionEvent.axisFromString("")), objArr6);
                                g.e(intern, ((String) objArr6[0]).intern());
                                stopSelf();
                                break;
                            }
                        default:
                            Bundle extras2 = intent.getExtras();
                            Object[] objArr7 = new Object[1];
                            g(Color.rgb(0, 0, 0) + 16777241, "\f\u0013\u0007\u0013\u0016\u0019\u0005\b\u000b\u0014\u0005\u0019\u0004\u0013\u0014\n\u0013\u0002\u001e\u000e \u0013\u0013\u0002㘕", (byte) (45 - Color.argb(0, 0, 0, 0)), objArr7);
                            f a2 = f.a(extras2.getString(((String) objArr7[0]).intern()));
                            if (a2 != null) {
                                Bundle extras3 = intent.getExtras();
                                Object[] objArr8 = new Object[1];
                                f(TextUtils.indexOf((CharSequence) "", '0', 0) + 4, "\u0001\u0004\uffff\u0000￥\u0001\u0003�\u000f\u000f\u0001￩\u0001\u0010�\u0000\f\ufff1\u0007\uffff", (ViewConfiguration.getTapTimeout() >> 16) + 20, ExpandableListView.getPackedPositionType(0L) + 275, true, objArr8);
                                String string = extras3.getString(((String) objArr8[0]).intern());
                                switch (string == null) {
                                    case false:
                                        o.db.b.a().d(this, b2, a2);
                                        g.c();
                                        Object[] objArr9 = new Object[1];
                                        g((Process.myTid() >> 22) + 29, " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u0016\u001c\u0014\u000b\u0011\u0012 \u0019\u0016\u000e\u0019\u0002\u0004\u000f\u0000\"㙍", (byte) (85 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), objArr9);
                                        g.d(intern, ((String) objArr9[0]).intern());
                                        new a(this, this, b2, string, a2).c();
                                        break;
                                    default:
                                        g.c();
                                        Object[] objArr10 = new Object[1];
                                        g(KeyEvent.normalizeMetaState(0) + 29, " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u000f\u001c#\f\f\n\u000f\u0011\u0018 \u0016\u0011\u0012! \u001d㘜", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 30), objArr10);
                                        g.e(intern, ((String) objArr10[0]).intern());
                                        stopSelf();
                                        break;
                                }
                            } else {
                                int i4 = b + 81;
                                e = i4 % 128;
                                if (i4 % 2 == 0) {
                                    g.c();
                                    Object[] objArr11 = new Object[1];
                                    g((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 45, " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u0016\u0010!\u0013\u0007\u0013\u001f\u0010\r\u0003\u0016\n#\f\u0011\u0016\r\u001e㘬㘬\u0000\r\u000e\u001c#\u0012\u000f\u0016\u001f\u001e\u001d\b", (byte) (Color.green(0) + 47), objArr11);
                                    g.e(intern, ((String) objArr11[0]).intern());
                                    stopSelf();
                                    break;
                                } else {
                                    g.c();
                                    Object[] objArr12 = new Object[1];
                                    g(65 % (ExpandableListView.getPackedPositionForChild(0, 1) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 1) == 0L ? 0 : -1)), " \u0018\u000b\u0015\u0006\u000e\u0015\u001d#\f\f\u0004\u0016\u0010!\u0013\u0007\u0013\u001f\u0010\r\u0003\u0016\n#\f\u0011\u0016\r\u001e㘬㘬\u0000\r\u000e\u001c#\u0012\u000f\u0016\u001f\u001e\u001d\b", (byte) (93 - Color.green(0)), objArr12);
                                    g.e(intern, ((String) objArr12[0]).intern());
                                    stopSelf();
                                    break;
                                }
                            }
                    }
            }
            return 2;
        }

        @Override // android.app.Service
        public IBinder onBind(Intent intent) {
            int i = b + Opcodes.LSUB;
            e = i % 128;
            Object obj = null;
            switch (i % 2 != 0 ? '0' : ' ') {
                case '0':
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        @Override // o.de.a.e
        public void onJobEnd(o.bb.d dVar, a aVar, o.bv.g gVar) {
            int i = b + 45;
            e = i % 128;
            int i2 = i % 2;
            o.db.b.a().b(this, dVar, aVar.b(), false, gVar);
            stopSelf();
            int i3 = e + 39;
            b = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.Service
        public final void onCreate() {
            int i = b + 53;
            e = i % 128;
            int i2 = i % 2;
            super.onCreate();
            int i3 = b + 97;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.Service
        public final void onDestroy() {
            int i = e + 81;
            b = i % 128;
            int i2 = i % 2;
            super.onDestroy();
            int i3 = e + 5;
            b = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.Service
        public final void onStart(Intent intent, int i) {
            int i2 = b + Opcodes.LSHR;
            e = i2 % 128;
            char c2 = i2 % 2 != 0 ? 'R' : '3';
            Object obj = null;
            super.onStart(intent, i);
            switch (c2) {
                case '3':
                    int i3 = e + 69;
                    b = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
                default:
                    throw null;
            }
        }

        @Override // android.app.Service, android.content.ComponentCallbacks
        public final void onConfigurationChanged(Configuration configuration) {
            int i = e + 49;
            b = i % 128;
            int i2 = i % 2;
            super.onConfigurationChanged(configuration);
            int i3 = b + 5;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? '\\' : 'W') {
                case Opcodes.POP /* 87 */:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.app.Service
        public final boolean onUnbind(Intent intent) {
            int i = b + 91;
            e = i % 128;
            char c2 = i % 2 != 0 ? (char) 19 : (char) 30;
            boolean onUnbind = super.onUnbind(intent);
            switch (c2) {
                case 19:
                    int i2 = 22 / 0;
                    break;
            }
            int i3 = e + Opcodes.DDIV;
            b = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    int i4 = 87 / 0;
                    return onUnbind;
                default:
                    return onUnbind;
            }
        }

        @Override // android.app.Service
        public final void onRebind(Intent intent) {
            int i = b + Opcodes.DREM;
            e = i % 128;
            char c2 = i % 2 != 0 ? Typography.quote : '\\';
            super.onRebind(intent);
            switch (c2) {
                case '\"':
                    throw null;
                default:
                    int i2 = e + 67;
                    b = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        @Override // android.app.Service
        public final void onTaskRemoved(Intent intent) {
            int i = e + 89;
            b = i % 128;
            int i2 = i % 2;
            super.onTaskRemoved(intent);
            int i3 = e + 55;
            b = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.app.Service
        protected final void dump(FileDescriptor fileDescriptor, PrintWriter printWriter, String[] strArr) {
            int i = b + 47;
            e = i % 128;
            char c2 = i % 2 != 0 ? '\t' : (char) 20;
            super.dump(fileDescriptor, printWriter, strArr);
            switch (c2) {
                case 20:
                    int i2 = e + 21;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? '\f' : '9') {
                        case '9':
                            return;
                        default:
                            int i3 = 44 / 0;
                            return;
                    }
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.app.Service, android.content.ComponentCallbacks
        public final void onLowMemory() {
            int i = b + 39;
            e = i % 128;
            boolean z = i % 2 == 0;
            super.onLowMemory();
            switch (z) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.app.Service, android.content.ComponentCallbacks2
        public final void onTrimMemory(int i) {
            int i2 = e + Opcodes.LREM;
            b = i2 % 128;
            int i3 = i2 % 2;
            super.onTrimMemory(i);
            int i4 = b + 55;
            e = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // android.app.Service, android.content.ContextWrapper
        protected final void attachBaseContext(Context context) {
            int i = e + 13;
            b = i % 128;
            char c2 = i % 2 == 0 ? '9' : (char) 4;
            Object obj = null;
            super.attachBaseContext(context);
            switch (c2) {
                case '9':
                    obj.hashCode();
                    throw null;
                default:
                    int i2 = e + 97;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? '2' : (char) 0) {
                        case 0:
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }

        @Override // android.content.ContextWrapper
        public final Context getBaseContext() {
            int i = e + 1;
            b = i % 128;
            switch (i % 2 == 0 ? (char) 20 : (char) 30) {
                case 30:
                    Context baseContext = super.getBaseContext();
                    int i2 = e + 19;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? '1' : Typography.dollar) {
                        case '$':
                            return baseContext;
                        default:
                            int i3 = 54 / 0;
                            return baseContext;
                    }
                default:
                    super.getBaseContext();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final AssetManager getAssets() {
            int i = e + 99;
            b = i % 128;
            switch (i % 2 == 0) {
                case true:
                    int i2 = 23 / 0;
                    return super.getAssets();
                default:
                    return super.getAssets();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Resources getResources() {
            int i = e + 99;
            b = i % 128;
            int i2 = i % 2;
            Resources resources = super.getResources();
            int i3 = b + Opcodes.LREM;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    int i4 = 9 / 0;
                    return resources;
                default:
                    return resources;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final PackageManager getPackageManager() {
            int i = b + Opcodes.LUSHR;
            e = i % 128;
            int i2 = i % 2;
            PackageManager packageManager = super.getPackageManager();
            int i3 = e + 39;
            b = i3 % 128;
            int i4 = i3 % 2;
            return packageManager;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final ContentResolver getContentResolver() {
            int i = b + Opcodes.LUSHR;
            e = i % 128;
            int i2 = i % 2;
            ContentResolver contentResolver = super.getContentResolver();
            int i3 = b + Opcodes.DNEG;
            e = i3 % 128;
            int i4 = i3 % 2;
            return contentResolver;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Looper getMainLooper() {
            int i = b + 47;
            e = i % 128;
            int i2 = i % 2;
            Looper mainLooper = super.getMainLooper();
            int i3 = e + 79;
            b = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    return mainLooper;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Executor getMainExecutor() {
            int i = b + Opcodes.DDIV;
            e = i % 128;
            switch (i % 2 != 0 ? '\t' : '!') {
                case '\t':
                    super.getMainExecutor();
                    throw null;
                default:
                    Executor mainExecutor = super.getMainExecutor();
                    int i2 = b + Opcodes.DDIV;
                    e = i2 % 128;
                    int i3 = i2 % 2;
                    return mainExecutor;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context getApplicationContext() {
            int i = e + 45;
            b = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return super.getApplicationContext();
                default:
                    super.getApplicationContext();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void setTheme(int i) {
            int i2 = e + 67;
            b = i2 % 128;
            int i3 = i2 % 2;
            super.setTheme(i);
            int i4 = e + 97;
            b = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Resources.Theme getTheme() {
            int i = b + Opcodes.DDIV;
            e = i % 128;
            int i2 = i % 2;
            Resources.Theme theme = super.getTheme();
            int i3 = b + 27;
            e = i3 % 128;
            int i4 = i3 % 2;
            return theme;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final ClassLoader getClassLoader() {
            int i = e + 39;
            b = i % 128;
            int i2 = i % 2;
            ClassLoader classLoader = super.getClassLoader();
            int i3 = e + 11;
            b = i3 % 128;
            int i4 = i3 % 2;
            return classLoader;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String getPackageName() {
            int i = e + 91;
            b = i % 128;
            switch (i % 2 == 0 ? '4' : '3') {
                case '3':
                    return super.getPackageName();
                default:
                    super.getPackageName();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final ApplicationInfo getApplicationInfo() {
            int i = e + 11;
            b = i % 128;
            int i2 = i % 2;
            ApplicationInfo applicationInfo = super.getApplicationInfo();
            int i3 = e + 29;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 14 : '*') {
                case '*':
                    return applicationInfo;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String getPackageResourcePath() {
            int i = e + 35;
            b = i % 128;
            switch (i % 2 == 0) {
                case true:
                    int i2 = 9 / 0;
                    return super.getPackageResourcePath();
                default:
                    return super.getPackageResourcePath();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String getPackageCodePath() {
            int i = e + 61;
            b = i % 128;
            int i2 = i % 2;
            String packageCodePath = super.getPackageCodePath();
            int i3 = b + 53;
            e = i3 % 128;
            int i4 = i3 % 2;
            return packageCodePath;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final SharedPreferences getSharedPreferences(String str, int i) {
            int i2 = b + 27;
            e = i2 % 128;
            int i3 = i2 % 2;
            SharedPreferences sharedPreferences = super.getSharedPreferences(str, i);
            int i4 = e + 3;
            b = i4 % 128;
            switch (i4 % 2 != 0 ? 'a' : (char) 2) {
                case 2:
                    int i5 = 15 / 0;
                    return sharedPreferences;
                default:
                    return sharedPreferences;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean moveSharedPreferencesFrom(Context context, String str) {
            int i = b + Opcodes.DDIV;
            e = i % 128;
            char c2 = i % 2 != 0 ? '9' : ']';
            boolean moveSharedPreferencesFrom = super.moveSharedPreferencesFrom(context, str);
            switch (c2) {
                case '9':
                    int i2 = 78 / 0;
                default:
                    return moveSharedPreferencesFrom;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean deleteSharedPreferences(String str) {
            int i = b + 63;
            e = i % 128;
            switch (i % 2 == 0) {
                case true:
                    return super.deleteSharedPreferences(str);
                default:
                    super.deleteSharedPreferences(str);
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final FileInputStream openFileInput(String str) throws FileNotFoundException {
            int i = b + Opcodes.LSHR;
            e = i % 128;
            boolean z = i % 2 != 0;
            FileInputStream openFileInput = super.openFileInput(str);
            switch (z) {
                case true:
                    int i2 = 85 / 0;
                    break;
            }
            int i3 = b + 57;
            e = i3 % 128;
            int i4 = i3 % 2;
            return openFileInput;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final FileOutputStream openFileOutput(String str, int i) throws FileNotFoundException {
            int i2 = b + 3;
            e = i2 % 128;
            int i3 = i2 % 2;
            FileOutputStream openFileOutput = super.openFileOutput(str, i);
            int i4 = b + 87;
            e = i4 % 128;
            int i5 = i4 % 2;
            return openFileOutput;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean deleteFile(String str) {
            int i = e + 87;
            b = i % 128;
            char c2 = i % 2 == 0 ? '\t' : '1';
            boolean deleteFile = super.deleteFile(str);
            switch (c2) {
                case '\t':
                    int i2 = 37 / 0;
                    break;
            }
            int i3 = b + 85;
            e = i3 % 128;
            int i4 = i3 % 2;
            return deleteFile;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getFileStreamPath(String str) {
            int i = b + 79;
            e = i % 128;
            int i2 = i % 2;
            File fileStreamPath = super.getFileStreamPath(str);
            int i3 = b + 55;
            e = i3 % 128;
            int i4 = i3 % 2;
            return fileStreamPath;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String[] fileList() {
            int i = e + 33;
            b = i % 128;
            int i2 = i % 2;
            String[] fileList = super.fileList();
            int i3 = e + 49;
            b = i3 % 128;
            int i4 = i3 % 2;
            return fileList;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getDataDir() {
            int i = b + 45;
            e = i % 128;
            int i2 = i % 2;
            File dataDir = super.getDataDir();
            int i3 = b + 1;
            e = i3 % 128;
            int i4 = i3 % 2;
            return dataDir;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getFilesDir() {
            int i = b + 65;
            e = i % 128;
            switch (i % 2 != 0 ? (char) 7 : '2') {
                case 7:
                    super.getFilesDir();
                    throw null;
                default:
                    File filesDir = super.getFilesDir();
                    int i2 = e + 81;
                    b = i2 % 128;
                    switch (i2 % 2 == 0) {
                        case false:
                            return filesDir;
                        default:
                            throw null;
                    }
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getNoBackupFilesDir() {
            int i = b + 13;
            e = i % 128;
            int i2 = i % 2;
            File noBackupFilesDir = super.getNoBackupFilesDir();
            int i3 = e + Opcodes.DMUL;
            b = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return noBackupFilesDir;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getExternalFilesDir(String str) {
            int i = e + 85;
            b = i % 128;
            switch (i % 2 == 0 ? (char) 18 : 'E') {
                case 'E':
                    return super.getExternalFilesDir(str);
                default:
                    super.getExternalFilesDir(str);
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File[] getExternalFilesDirs(String str) {
            int i = b + 37;
            e = i % 128;
            switch (i % 2 != 0 ? (char) 18 : (char) 19) {
                case 19:
                    return super.getExternalFilesDirs(str);
                default:
                    super.getExternalFilesDirs(str);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getObbDir() {
            File obbDir;
            int i = b + 77;
            e = i % 128;
            switch (i % 2 != 0) {
                case false:
                    obbDir = super.getObbDir();
                    break;
                default:
                    obbDir = super.getObbDir();
                    int i2 = 76 / 0;
                    break;
            }
            int i3 = e + Opcodes.DMUL;
            b = i3 % 128;
            int i4 = i3 % 2;
            return obbDir;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File[] getObbDirs() {
            int i = e + Opcodes.DDIV;
            b = i % 128;
            switch (i % 2 == 0) {
                case false:
                    return super.getObbDirs();
                default:
                    int i2 = 55 / 0;
                    return super.getObbDirs();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getCacheDir() {
            int i = b + 97;
            e = i % 128;
            int i2 = i % 2;
            File cacheDir = super.getCacheDir();
            int i3 = e + 83;
            b = i3 % 128;
            int i4 = i3 % 2;
            return cacheDir;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getCodeCacheDir() {
            int i = e + 85;
            b = i % 128;
            switch (i % 2 == 0 ? (char) 17 : (char) 0) {
                case 0:
                    return super.getCodeCacheDir();
                default:
                    int i2 = 87 / 0;
                    return super.getCodeCacheDir();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getExternalCacheDir() {
            int i = e + 57;
            b = i % 128;
            int i2 = i % 2;
            File externalCacheDir = super.getExternalCacheDir();
            int i3 = b + 81;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return externalCacheDir;
                default:
                    int i4 = 91 / 0;
                    return externalCacheDir;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File[] getExternalCacheDirs() {
            File[] externalCacheDirs;
            int i = e + Opcodes.DREM;
            b = i % 128;
            switch (i % 2 == 0 ? '`' : '8') {
                case Opcodes.IADD /* 96 */:
                    externalCacheDirs = super.getExternalCacheDirs();
                    int i2 = 47 / 0;
                    break;
                default:
                    externalCacheDirs = super.getExternalCacheDirs();
                    break;
            }
            int i3 = e + Opcodes.LUSHR;
            b = i3 % 128;
            int i4 = i3 % 2;
            return externalCacheDirs;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File[] getExternalMediaDirs() {
            int i = e + 21;
            b = i % 128;
            int i2 = i % 2;
            File[] externalMediaDirs = super.getExternalMediaDirs();
            int i3 = b + 97;
            e = i3 % 128;
            int i4 = i3 % 2;
            return externalMediaDirs;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getDir(String str, int i) {
            int i2 = b + Opcodes.DREM;
            e = i2 % 128;
            switch (i2 % 2 != 0 ? '4' : (char) 15) {
                case 15:
                    return super.getDir(str, i);
                default:
                    super.getDir(str, i);
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final SQLiteDatabase openOrCreateDatabase(String str, int i, SQLiteDatabase.CursorFactory cursorFactory) {
            int i2 = b + 21;
            e = i2 % 128;
            int i3 = i2 % 2;
            SQLiteDatabase openOrCreateDatabase = super.openOrCreateDatabase(str, i, cursorFactory);
            int i4 = b + 79;
            e = i4 % 128;
            int i5 = i4 % 2;
            return openOrCreateDatabase;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final SQLiteDatabase openOrCreateDatabase(String str, int i, SQLiteDatabase.CursorFactory cursorFactory, DatabaseErrorHandler databaseErrorHandler) {
            int i2 = e + 73;
            b = i2 % 128;
            boolean z = i2 % 2 != 0;
            SQLiteDatabase openOrCreateDatabase = super.openOrCreateDatabase(str, i, cursorFactory, databaseErrorHandler);
            switch (z) {
                default:
                    int i3 = 72 / 0;
                case true:
                    return openOrCreateDatabase;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean moveDatabaseFrom(Context context, String str) {
            int i = b + 43;
            e = i % 128;
            boolean z = i % 2 == 0;
            boolean moveDatabaseFrom = super.moveDatabaseFrom(context, str);
            switch (z) {
                case false:
                    int i2 = 11 / 0;
                default:
                    return moveDatabaseFrom;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean deleteDatabase(String str) {
            int i = b + Opcodes.LSHR;
            e = i % 128;
            char c2 = i % 2 != 0 ? 'J' : (char) 23;
            boolean deleteDatabase = super.deleteDatabase(str);
            switch (c2) {
                case 23:
                    break;
                default:
                    int i2 = 81 / 0;
                    break;
            }
            int i3 = e + 11;
            b = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    return deleteDatabase;
                default:
                    int i4 = 83 / 0;
                    return deleteDatabase;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final File getDatabasePath(String str) {
            int i = e + 79;
            b = i % 128;
            char c2 = i % 2 == 0 ? '.' : '@';
            File databasePath = super.getDatabasePath(str);
            switch (c2) {
                default:
                    int i2 = 22 / 0;
                case '@':
                    return databasePath;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String[] databaseList() {
            int i = b + 37;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    super.databaseList();
                    throw null;
                default:
                    return super.databaseList();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Drawable getWallpaper() {
            int i = b + 85;
            e = i % 128;
            int i2 = i % 2;
            Drawable wallpaper = super.getWallpaper();
            int i3 = b + 55;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? 'B' : (char) 22) {
                case 22:
                    return wallpaper;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Drawable peekWallpaper() {
            int i = e + 91;
            b = i % 128;
            switch (i % 2 == 0 ? (char) 18 : 'a') {
                case 18:
                    super.peekWallpaper();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return super.peekWallpaper();
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int getWallpaperDesiredMinimumWidth() {
            int i = b + 49;
            e = i % 128;
            int i2 = i % 2;
            int wallpaperDesiredMinimumWidth = super.getWallpaperDesiredMinimumWidth();
            int i3 = b + 3;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? ',' : '.') {
                case '.':
                    return wallpaperDesiredMinimumWidth;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int getWallpaperDesiredMinimumHeight() {
            int wallpaperDesiredMinimumHeight;
            int i = e + Opcodes.LSHL;
            b = i % 128;
            switch (i % 2 == 0 ? 'Z' : '5') {
                case 'Z':
                    wallpaperDesiredMinimumHeight = super.getWallpaperDesiredMinimumHeight();
                    int i2 = 93 / 0;
                    break;
                default:
                    wallpaperDesiredMinimumHeight = super.getWallpaperDesiredMinimumHeight();
                    break;
            }
            int i3 = e + 11;
            b = i3 % 128;
            int i4 = i3 % 2;
            return wallpaperDesiredMinimumHeight;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void setWallpaper(Bitmap bitmap) throws IOException {
            int i = b + 11;
            e = i % 128;
            boolean z = i % 2 == 0;
            super.setWallpaper(bitmap);
            switch (z) {
                case false:
                    int i2 = 54 / 0;
                    break;
            }
            int i3 = b + Opcodes.LMUL;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void setWallpaper(InputStream inputStream) throws IOException {
            int i = b + 49;
            e = i % 128;
            int i2 = i % 2;
            super.setWallpaper(inputStream);
            int i3 = b + 43;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void clearWallpaper() throws IOException {
            int i = b + 39;
            e = i % 128;
            boolean z = i % 2 == 0;
            super.clearWallpaper();
            switch (z) {
                case false:
                    int i2 = 37 / 0;
                    break;
            }
            int i3 = b + 65;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startActivity(Intent intent) {
            int i = b + 5;
            e = i % 128;
            int i2 = i % 2;
            super.startActivity(intent);
            int i3 = e + 31;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 16 : (char) 3) {
                case 3:
                    return;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startActivity(Intent intent, Bundle bundle) {
            int i = b + 15;
            e = i % 128;
            boolean z = i % 2 != 0;
            super.startActivity(intent, bundle);
            switch (z) {
                case false:
                    int i2 = e + 93;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? (char) 23 : (char) 6) {
                        case 23:
                            int i3 = 85 / 0;
                            return;
                        default:
                            return;
                    }
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startActivities(Intent[] intentArr) {
            int i = e + Opcodes.LREM;
            b = i % 128;
            int i2 = i % 2;
            super.startActivities(intentArr);
            int i3 = b + 83;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    int i4 = 42 / 0;
                    return;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startActivities(Intent[] intentArr, Bundle bundle) {
            int i = e + 77;
            b = i % 128;
            int i2 = i % 2;
            super.startActivities(intentArr, bundle);
            int i3 = b + 89;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startIntentSender(IntentSender intentSender, Intent intent, int i, int i2, int i3) throws IntentSender.SendIntentException {
            int i4 = e + Opcodes.LMUL;
            b = i4 % 128;
            boolean z = i4 % 2 != 0;
            super.startIntentSender(intentSender, intent, i, i2, i3);
            switch (z) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void startIntentSender(IntentSender intentSender, Intent intent, int i, int i2, int i3, Bundle bundle) throws IntentSender.SendIntentException {
            int i4 = e + 91;
            b = i4 % 128;
            boolean z = i4 % 2 == 0;
            super.startIntentSender(intentSender, intent, i, i2, i3, bundle);
            switch (z) {
                case true:
                    throw null;
                default:
                    int i5 = e + 27;
                    b = i5 % 128;
                    switch (i5 % 2 == 0 ? 'T' : 'I') {
                        case 'I':
                            return;
                        default:
                            throw null;
                    }
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendBroadcast(Intent intent) {
            int i = e + 35;
            b = i % 128;
            char c2 = i % 2 == 0 ? (char) 24 : 'W';
            super.sendBroadcast(intent);
            switch (c2) {
                case 24:
                    throw null;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendBroadcast(Intent intent, String str) {
            int i = e + 65;
            b = i % 128;
            boolean z = i % 2 != 0;
            Object obj = null;
            super.sendBroadcast(intent, str);
            switch (z) {
                case true:
                    int i2 = e + Opcodes.LREM;
                    b = i2 % 128;
                    switch (i2 % 2 != 0) {
                        case true:
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendOrderedBroadcast(Intent intent, String str) {
            int i = b + 17;
            e = i % 128;
            int i2 = i % 2;
            super.sendOrderedBroadcast(intent, str);
            int i3 = e + Opcodes.LSUB;
            b = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendOrderedBroadcast(Intent intent, String str, BroadcastReceiver broadcastReceiver, Handler handler, int i, String str2, Bundle bundle) {
            int i2 = b + 99;
            e = i2 % 128;
            int i3 = i2 % 2;
            super.sendOrderedBroadcast(intent, str, broadcastReceiver, handler, i, str2, bundle);
            int i4 = e + 27;
            b = i4 % 128;
            switch (i4 % 2 != 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendBroadcastAsUser(Intent intent, UserHandle userHandle) {
            int i = e + Opcodes.LUSHR;
            b = i % 128;
            char c2 = i % 2 == 0 ? '=' : (char) 20;
            super.sendBroadcastAsUser(intent, userHandle);
            switch (c2) {
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    int i2 = 37 / 0;
                    return;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendBroadcastAsUser(Intent intent, UserHandle userHandle, String str) {
            int i = e + 21;
            b = i % 128;
            boolean z = i % 2 != 0;
            super.sendBroadcastAsUser(intent, userHandle, str);
            switch (z) {
                case true:
                    int i2 = b + 35;
                    e = i2 % 128;
                    int i3 = i2 % 2;
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendOrderedBroadcastAsUser(Intent intent, UserHandle userHandle, String str, BroadcastReceiver broadcastReceiver, Handler handler, int i, String str2, Bundle bundle) {
            int i2 = b + Opcodes.DDIV;
            e = i2 % 128;
            boolean z = i2 % 2 != 0;
            super.sendOrderedBroadcastAsUser(intent, userHandle, str, broadcastReceiver, handler, i, str2, bundle);
            switch (z) {
                case false:
                    int i3 = e + 53;
                    b = i3 % 128;
                    switch (i3 % 2 == 0 ? (char) 16 : 'b') {
                        case Opcodes.FADD /* 98 */:
                            return;
                        default:
                            int i4 = 6 / 0;
                            return;
                    }
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendStickyBroadcast(Intent intent) {
            int i = e + Opcodes.DMUL;
            b = i % 128;
            char c2 = i % 2 == 0 ? '-' : 'O';
            super.sendStickyBroadcast(intent);
            switch (c2) {
                case Opcodes.IASTORE /* 79 */:
                    return;
                default:
                    int i2 = 64 / 0;
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendStickyOrderedBroadcast(Intent intent, BroadcastReceiver broadcastReceiver, Handler handler, int i, String str, Bundle bundle) {
            int i2 = b + 13;
            e = i2 % 128;
            boolean z = i2 % 2 != 0;
            super.sendStickyOrderedBroadcast(intent, broadcastReceiver, handler, i, str, bundle);
            switch (z) {
                case false:
                    return;
                default:
                    int i3 = 4 / 0;
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void removeStickyBroadcast(Intent intent) {
            int i = e + 89;
            b = i % 128;
            char c2 = i % 2 == 0 ? 'F' : (char) 0;
            super.removeStickyBroadcast(intent);
            switch (c2) {
                case 0:
                    int i2 = e + 89;
                    b = i2 % 128;
                    int i3 = i2 % 2;
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendStickyBroadcastAsUser(Intent intent, UserHandle userHandle) {
            int i = b + 15;
            e = i % 128;
            int i2 = i % 2;
            super.sendStickyBroadcastAsUser(intent, userHandle);
            int i3 = e + Opcodes.DSUB;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? '[' : Typography.dollar) {
                case '$':
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void sendStickyOrderedBroadcastAsUser(Intent intent, UserHandle userHandle, BroadcastReceiver broadcastReceiver, Handler handler, int i, String str, Bundle bundle) {
            int i2 = e + Opcodes.LSUB;
            b = i2 % 128;
            char c2 = i2 % 2 == 0 ? ':' : 'E';
            Object obj = null;
            super.sendStickyOrderedBroadcastAsUser(intent, userHandle, broadcastReceiver, handler, i, str, bundle);
            switch (c2) {
                case Opcodes.ASTORE /* 58 */:
                    obj.hashCode();
                    throw null;
                default:
                    int i3 = e + 13;
                    b = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case true:
                            throw null;
                        default:
                            return;
                    }
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void removeStickyBroadcastAsUser(Intent intent, UserHandle userHandle) {
            int i = e + 71;
            b = i % 128;
            int i2 = i % 2;
            super.removeStickyBroadcastAsUser(intent, userHandle);
            int i3 = e + 41;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 16 : '8') {
                case '8':
                    return;
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Intent registerReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter) {
            int i = e + 59;
            b = i % 128;
            int i2 = i % 2;
            Intent registerReceiver = super.registerReceiver(broadcastReceiver, intentFilter);
            int i3 = b + 109;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? Typography.greater : 'L') {
                case '>':
                    throw null;
                default:
                    return registerReceiver;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Intent registerReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter, int i) {
            int i2 = b + 79;
            e = i2 % 128;
            switch (i2 % 2 != 0 ? 'T' : Typography.less) {
                case Opcodes.BASTORE /* 84 */:
                    super.registerReceiver(broadcastReceiver, intentFilter, i);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    Intent registerReceiver = super.registerReceiver(broadcastReceiver, intentFilter, i);
                    int i3 = e + Opcodes.LSHL;
                    b = i3 % 128;
                    int i4 = i3 % 2;
                    return registerReceiver;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Intent registerReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter, String str, Handler handler) {
            int i = b + 37;
            e = i % 128;
            switch (i % 2 == 0) {
                case false:
                    super.registerReceiver(broadcastReceiver, intentFilter, str, handler);
                    throw null;
                default:
                    return super.registerReceiver(broadcastReceiver, intentFilter, str, handler);
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Intent registerReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter, String str, Handler handler, int i) {
            int i2 = b + 19;
            e = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 24 : '?') {
                case '?':
                    Intent registerReceiver = super.registerReceiver(broadcastReceiver, intentFilter, str, handler, i);
                    int i3 = b + 7;
                    e = i3 % 128;
                    int i4 = i3 % 2;
                    return registerReceiver;
                default:
                    super.registerReceiver(broadcastReceiver, intentFilter, str, handler, i);
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void unregisterReceiver(BroadcastReceiver broadcastReceiver) {
            int i = b + 17;
            e = i % 128;
            char c2 = i % 2 != 0 ? (char) 21 : 'G';
            super.unregisterReceiver(broadcastReceiver);
            switch (c2) {
                case 'G':
                    int i2 = e + 3;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? (char) 23 : 'I') {
                        case 'I':
                            return;
                        default:
                            int i3 = 30 / 0;
                            return;
                    }
                default:
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final ComponentName startService(Intent intent) {
            int i = b + 35;
            e = i % 128;
            switch (i % 2 != 0 ? '\r' : (char) 2) {
                case 2:
                    return super.startService(intent);
                default:
                    super.startService(intent);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final ComponentName startForegroundService(Intent intent) {
            int i = b + 49;
            e = i % 128;
            int i2 = i % 2;
            ComponentName startForegroundService = super.startForegroundService(intent);
            int i3 = e + 27;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? '.' : (char) 18) {
                case '.':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return startForegroundService;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean stopService(Intent intent) {
            int i = e + Opcodes.LSHR;
            b = i % 128;
            switch (i % 2 != 0) {
                case true:
                    boolean stopService = super.stopService(intent);
                    int i2 = e + 93;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? 'C' : '\n') {
                        case '\n':
                            return stopService;
                        default:
                            int i3 = 50 / 0;
                            return stopService;
                    }
                default:
                    super.stopService(intent);
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean bindService(Intent intent, ServiceConnection serviceConnection, int i) {
            int i2 = b + 79;
            e = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    super.bindService(intent, serviceConnection, i);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return super.bindService(intent, serviceConnection, i);
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void unbindService(ServiceConnection serviceConnection) {
            int i = b + Opcodes.LSHR;
            e = i % 128;
            char c2 = i % 2 != 0 ? 'V' : '\n';
            super.unbindService(serviceConnection);
            switch (c2) {
                case Opcodes.SASTORE /* 86 */:
                    throw null;
                default:
                    int i2 = b + Opcodes.DMUL;
                    e = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean startInstrumentation(ComponentName componentName, String str, Bundle bundle) {
            int i = e + 43;
            b = i % 128;
            int i2 = i % 2;
            boolean startInstrumentation = super.startInstrumentation(componentName, str, bundle);
            int i3 = b + 55;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? 'Y' : (char) 22) {
                case Opcodes.DUP /* 89 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return startInstrumentation;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Object getSystemService(String str) {
            int i = b + Opcodes.DDIV;
            e = i % 128;
            char c2 = i % 2 != 0 ? 'A' : '_';
            Object systemService = super.getSystemService(str);
            switch (c2) {
                default:
                    int i2 = 33 / 0;
                case Opcodes.SWAP /* 95 */:
                    return systemService;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final String getSystemServiceName(Class<?> cls) {
            int i = e + Opcodes.LSHR;
            b = i % 128;
            int i2 = i % 2;
            String systemServiceName = super.getSystemServiceName(cls);
            int i3 = e + Opcodes.DSUB;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 1 : '4') {
                case 1:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return systemServiceName;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkPermission(String str, int i, int i2) {
            int i3 = b + 81;
            e = i3 % 128;
            int i4 = i3 % 2;
            int checkPermission = super.checkPermission(str, i, i2);
            int i5 = e + Opcodes.LNEG;
            b = i5 % 128;
            switch (i5 % 2 == 0) {
                case true:
                    int i6 = 3 / 0;
                    return checkPermission;
                default:
                    return checkPermission;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkCallingPermission(String str) {
            int i = b + 29;
            e = i % 128;
            int i2 = i % 2;
            int checkCallingPermission = super.checkCallingPermission(str);
            int i3 = e + 69;
            b = i3 % 128;
            int i4 = i3 % 2;
            return checkCallingPermission;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkCallingOrSelfPermission(String str) {
            int i = b + 7;
            e = i % 128;
            switch (i % 2 != 0 ? 'O' : '\'') {
                case '\'':
                    return super.checkCallingOrSelfPermission(str);
                default:
                    super.checkCallingOrSelfPermission(str);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkSelfPermission(String str) {
            int i = b + 93;
            e = i % 128;
            switch (i % 2 != 0 ? 'W' : (char) 29) {
                case Opcodes.POP /* 87 */:
                    super.checkSelfPermission(str);
                    throw null;
                default:
                    int checkSelfPermission = super.checkSelfPermission(str);
                    int i2 = b + 23;
                    e = i2 % 128;
                    switch (i2 % 2 != 0) {
                        case false:
                            return checkSelfPermission;
                        default:
                            int i3 = 81 / 0;
                            return checkSelfPermission;
                    }
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforcePermission(String str, int i, int i2, String str2) {
            int i3 = b + 11;
            e = i3 % 128;
            boolean z = i3 % 2 == 0;
            super.enforcePermission(str, i, i2, str2);
            switch (z) {
                case true:
                    break;
                default:
                    int i4 = 14 / 0;
                    break;
            }
            int i5 = b + 63;
            e = i5 % 128;
            switch (i5 % 2 == 0) {
                case true:
                    return;
                default:
                    int i6 = 5 / 0;
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceCallingPermission(String str, String str2) {
            int i = b + 73;
            e = i % 128;
            int i2 = i % 2;
            super.enforceCallingPermission(str, str2);
            int i3 = b + 69;
            e = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceCallingOrSelfPermission(String str, String str2) {
            int i = e + 55;
            b = i % 128;
            char c2 = i % 2 == 0 ? 'b' : 'X';
            super.enforceCallingOrSelfPermission(str, str2);
            switch (c2) {
                case Opcodes.FADD /* 98 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void grantUriPermission(String str, Uri uri, int i) {
            int i2 = b + 37;
            e = i2 % 128;
            int i3 = i2 % 2;
            super.grantUriPermission(str, uri, i);
            int i4 = b + 51;
            e = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void revokeUriPermission(Uri uri, int i) {
            int i2 = e + 17;
            b = i2 % 128;
            char c2 = i2 % 2 != 0 ? (char) 0 : (char) 17;
            super.revokeUriPermission(uri, i);
            switch (c2) {
                case 17:
                    int i3 = 24 / 0;
                    return;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void revokeUriPermission(String str, Uri uri, int i) {
            int i2 = b + 73;
            e = i2 % 128;
            int i3 = i2 % 2;
            super.revokeUriPermission(str, uri, i);
            int i4 = e + Opcodes.LSHR;
            b = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkUriPermission(Uri uri, int i, int i2, int i3) {
            int i4 = e + 43;
            b = i4 % 128;
            int i5 = i4 % 2;
            int checkUriPermission = super.checkUriPermission(uri, i, i2, i3);
            int i6 = e + 1;
            b = i6 % 128;
            int i7 = i6 % 2;
            return checkUriPermission;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkCallingUriPermission(Uri uri, int i) {
            int i2 = e + 79;
            b = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return super.checkCallingUriPermission(uri, i);
                default:
                    super.checkCallingUriPermission(uri, i);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkCallingOrSelfUriPermission(Uri uri, int i) {
            int i2 = e + 31;
            b = i2 % 128;
            int i3 = i2 % 2;
            int checkCallingOrSelfUriPermission = super.checkCallingOrSelfUriPermission(uri, i);
            int i4 = b + Opcodes.DMUL;
            e = i4 % 128;
            int i5 = i4 % 2;
            return checkCallingOrSelfUriPermission;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final int checkUriPermission(Uri uri, String str, String str2, int i, int i2, int i3) {
            int i4 = b + 23;
            e = i4 % 128;
            char c2 = i4 % 2 != 0 ? '\b' : 'T';
            int checkUriPermission = super.checkUriPermission(uri, str, str2, i, i2, i3);
            switch (c2) {
                default:
                    int i5 = 32 / 0;
                case Opcodes.BASTORE /* 84 */:
                    return checkUriPermission;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceUriPermission(Uri uri, int i, int i2, int i3, String str) {
            int i4 = b + 109;
            e = i4 % 128;
            int i5 = i4 % 2;
            super.enforceUriPermission(uri, i, i2, i3, str);
            int i6 = e + 25;
            b = i6 % 128;
            int i7 = i6 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceCallingUriPermission(Uri uri, int i, String str) {
            int i2 = e + 97;
            b = i2 % 128;
            boolean z = i2 % 2 != 0;
            super.enforceCallingUriPermission(uri, i, str);
            switch (z) {
                case true:
                    int i3 = e + 11;
                    b = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceCallingOrSelfUriPermission(Uri uri, int i, String str) {
            int i2 = e + 69;
            b = i2 % 128;
            int i3 = i2 % 2;
            super.enforceCallingOrSelfUriPermission(uri, i, str);
            int i4 = e + 19;
            b = i4 % 128;
            int i5 = i4 % 2;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void enforceUriPermission(Uri uri, String str, String str2, int i, int i2, int i3, String str3) {
            int i4 = e + 93;
            b = i4 % 128;
            char c2 = i4 % 2 == 0 ? (char) 6 : 'X';
            super.enforceUriPermission(uri, str, str2, i, i2, i3, str3);
            switch (c2) {
                case Opcodes.POP2 /* 88 */:
                    break;
                default:
                    int i5 = 44 / 0;
                    break;
            }
            int i6 = b + 93;
            e = i6 % 128;
            switch (i6 % 2 != 0 ? (char) 17 : '7') {
                case '7':
                    return;
                default:
                    int i7 = 73 / 0;
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context createPackageContext(String str, int i) throws PackageManager.NameNotFoundException {
            int i2 = b + 93;
            e = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    super.createPackageContext(str, i);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    Context createPackageContext = super.createPackageContext(str, i);
                    int i3 = e + 43;
                    b = i3 % 128;
                    int i4 = i3 % 2;
                    return createPackageContext;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context createContextForSplit(String str) throws PackageManager.NameNotFoundException {
            int i = e + 69;
            b = i % 128;
            switch (i % 2 == 0) {
                case true:
                    super.createContextForSplit(str);
                    throw null;
                default:
                    return super.createContextForSplit(str);
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context createConfigurationContext(Configuration configuration) {
            int i = e + 31;
            b = i % 128;
            int i2 = i % 2;
            Context createConfigurationContext = super.createConfigurationContext(configuration);
            int i3 = b + Opcodes.DREM;
            e = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return createConfigurationContext;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context createDisplayContext(Display display) {
            int i = b + 77;
            e = i % 128;
            int i2 = i % 2;
            Context createDisplayContext = super.createDisplayContext(display);
            int i3 = e + 23;
            b = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 6 : '!') {
                case 6:
                    throw null;
                default:
                    return createDisplayContext;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean isRestricted() {
            int i = b + 35;
            e = i % 128;
            switch (i % 2 != 0) {
                case false:
                    return super.isRestricted();
                default:
                    super.isRestricted();
                    throw null;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final Context createDeviceProtectedStorageContext() {
            Context createDeviceProtectedStorageContext;
            int i = b + 53;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    createDeviceProtectedStorageContext = super.createDeviceProtectedStorageContext();
                    int i2 = 56 / 0;
                    break;
                default:
                    createDeviceProtectedStorageContext = super.createDeviceProtectedStorageContext();
                    break;
            }
            int i3 = b + 81;
            e = i3 % 128;
            int i4 = i3 % 2;
            return createDeviceProtectedStorageContext;
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final boolean isDeviceProtectedStorage() {
            int i = b + 25;
            e = i % 128;
            int i2 = i % 2;
            boolean isDeviceProtectedStorage = super.isDeviceProtectedStorage();
            int i3 = e + 21;
            b = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return isDeviceProtectedStorage;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void registerComponentCallbacks(ComponentCallbacks componentCallbacks) {
            int i = e + 17;
            b = i % 128;
            int i2 = i % 2;
            super.registerComponentCallbacks(componentCallbacks);
            int i3 = b + 35;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        @Override // android.content.ContextWrapper, android.content.Context
        public final void unregisterComponentCallbacks(ComponentCallbacks componentCallbacks) {
            int i = b + 53;
            e = i % 128;
            int i2 = i % 2;
            super.unregisterComponentCallbacks(componentCallbacks);
            int i3 = b + 7;
            e = i3 % 128;
            switch (i3 % 2 != 0 ? 'W' : 'J') {
                case Opcodes.POP /* 87 */:
                    int i4 = 16 / 0;
                    return;
                default:
                    return;
            }
        }

        public int hashCode() {
            int i = b + 1;
            e = i % 128;
            switch (i % 2 == 0) {
                case true:
                    return super.hashCode();
                default:
                    super.hashCode();
                    throw null;
            }
        }

        public final boolean equals(Object obj) {
            int i = e + 25;
            b = i % 128;
            switch (i % 2 == 0) {
                case false:
                    return super.equals(obj);
                default:
                    super.equals(obj);
                    Object obj2 = null;
                    obj2.hashCode();
                    throw null;
            }
        }

        protected final Object clone() throws CloneNotSupportedException {
            throw new CloneNotSupportedException();
        }

        public final String toString() {
            int i = e + Opcodes.DMUL;
            b = i % 128;
            switch (i % 2 != 0) {
                case true:
                    String obj = super.toString();
                    int i2 = e + 81;
                    b = i2 % 128;
                    int i3 = i2 % 2;
                    return obj;
                default:
                    super.toString();
                    throw null;
            }
        }

        protected final void finalize() throws Throwable {
            int i = e + Opcodes.DNEG;
            b = i % 128;
            char c2 = i % 2 == 0 ? 'C' : (char) 17;
            super.finalize();
            switch (c2) {
                case 17:
                    int i2 = e + 27;
                    b = i2 % 128;
                    switch (i2 % 2 == 0 ? 'B' : 'N') {
                        case 'B':
                            int i3 = 27 / 0;
                            return;
                        default:
                            return;
                    }
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        private static void f(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
            char[] cArr;
            int i4 = $11;
            int i5 = i4 + 63;
            $10 = i5 % 128;
            int i6 = i5 % 2;
            switch (str != null) {
                case false:
                    cArr = str;
                    break;
                default:
                    int i7 = i4 + Opcodes.DREM;
                    $10 = i7 % 128;
                    switch (i7 % 2 != 0 ? ']' : 'Z') {
                        case Opcodes.DUP2_X1 /* 93 */:
                            str.toCharArray();
                            throw null;
                        default:
                            cArr = str.toCharArray();
                            break;
                    }
            }
            char[] cArr2 = cArr;
            o.a.h hVar = new o.a.h();
            char[] cArr3 = new char[i2];
            hVar.a = 0;
            while (hVar.a < i2) {
                hVar.b = cArr2[hVar.a];
                cArr3[hVar.a] = (char) (i3 + hVar.b);
                int i8 = hVar.a;
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr3[i8]), Integer.valueOf(c)};
                    Object obj = o.e.a.s.get(2038615114);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c('<' - AndroidCharacter.getMirror('0'), (char) TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 458);
                        byte b2 = (byte) 0;
                        Object[] objArr3 = new Object[1];
                        h(b2, (byte) (b2 | 38), b2, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                        o.e.a.s.put(2038615114, obj);
                    }
                    cArr3[i8] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    try {
                        Object[] objArr4 = {hVar, hVar};
                        Object obj2 = o.e.a.s.get(-1412673904);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 10, (char) View.combineMeasuredStates(0, 0), View.resolveSize(0, 0) + 313);
                            byte b3 = (byte) 0;
                            Object[] objArr5 = new Object[1];
                            h(b3, (byte) (b3 | 40), b3, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj2);
                        }
                        ((Method) obj2).invoke(null, objArr4);
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            if (i > 0) {
                hVar.c = i;
                char[] cArr4 = new char[i2];
                System.arraycopy(cArr3, 0, cArr4, 0, i2);
                System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
            }
            switch (z ? (char) 17 : 'L') {
                case 17:
                    int i9 = $11 + 43;
                    $10 = i9 % 128;
                    int i10 = i9 % 2;
                    char[] cArr5 = new char[i2];
                    hVar.a = 0;
                    while (hVar.a < i2) {
                        cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj3 = o.e.a.s.get(-1412673904);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 12, (char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 313 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                                byte b4 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                h(b4, (byte) (b4 | 40), b4, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    cArr3 = cArr5;
                    break;
            }
            objArr[0] = new String(cArr3);
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:111:0x002b. Please report as an issue. */
        private static void g(int i, String str, byte b2, Object[] objArr) {
            char[] charArray;
            int i2;
            int i3 = $10 + 39;
            $11 = i3 % 128;
            int i4 = i3 % 2;
            switch (str != null) {
                case true:
                    charArray = str.toCharArray();
                    int i5 = $11 + 57;
                    $10 = i5 % 128;
                    switch (i5 % 2 == 0) {
                    }
                default:
                    charArray = str;
                    break;
            }
            char[] cArr = charArray;
            m mVar = new m();
            char[] cArr2 = d;
            int i6 = -1401577988;
            if (cArr2 != null) {
                int length = cArr2.length;
                char[] cArr3 = new char[length];
                int i7 = 0;
                while (i7 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr2[i7])};
                        Object obj = o.e.a.s.get(Integer.valueOf(i6));
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(17 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) ((-1) - MotionEvent.axisFromString("")), 76 - (ViewConfiguration.getTouchSlop() >> 8));
                            byte length2 = (byte) $$a.length;
                            Object[] objArr3 = new Object[1];
                            h((byte) 0, length2, (byte) (length2 - 4), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1401577988, obj);
                        }
                        cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i7++;
                        i6 = -1401577988;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr2 = cArr3;
            }
            try {
                Object[] objArr4 = {Integer.valueOf(a)};
                Object obj2 = o.e.a.s.get(-1401577988);
                if (obj2 == null) {
                    Class cls2 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 17, (char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), 76 - TextUtils.indexOf("", ""));
                    byte length3 = (byte) $$a.length;
                    Object[] objArr5 = new Object[1];
                    h((byte) 0, length3, (byte) (length3 - 4), objArr5);
                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj2);
                }
                char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                char[] cArr4 = new char[i];
                if (i % 2 != 0) {
                    int i8 = $11 + 97;
                    $10 = i8 % 128;
                    int i9 = i8 % 2;
                    i2 = i - 1;
                    cArr4[i2] = (char) (cArr[i2] - b2);
                } else {
                    i2 = i;
                }
                if (i2 > 1) {
                    mVar.b = 0;
                    while (true) {
                        switch (mVar.b >= i2) {
                            case false:
                                int i10 = $11 + Opcodes.LSHR;
                                $10 = i10 % 128;
                                int i11 = i10 % 2;
                                mVar.e = cArr[mVar.b];
                                mVar.a = cArr[mVar.b + 1];
                                if (mVar.e == mVar.a) {
                                    int i12 = $10 + 69;
                                    $11 = i12 % 128;
                                    if (i12 % 2 == 0) {
                                        cArr4[mVar.b] = (char) (mVar.e >>> b2);
                                        cArr4[mVar.b - 1] = (char) (mVar.a % b2);
                                    } else {
                                        cArr4[mVar.b] = (char) (mVar.e - b2);
                                        cArr4[mVar.b + 1] = (char) (mVar.a - b2);
                                    }
                                } else {
                                    try {
                                        Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                        Object obj3 = o.e.a.s.get(696901393);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c(9 - TextUtils.lastIndexOf("", '0'), (char) (8856 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), 324 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                                            byte b3 = (byte) 0;
                                            byte b4 = b3;
                                            Object[] objArr7 = new Object[1];
                                            h(b3, b4, b4, objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                            o.e.a.s.put(696901393, obj3);
                                        }
                                        switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                            case false:
                                                if (mVar.c == mVar.d) {
                                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                    int i13 = (mVar.c * charValue) + mVar.i;
                                                    int i14 = (mVar.d * charValue) + mVar.h;
                                                    cArr4[mVar.b] = cArr2[i13];
                                                    cArr4[mVar.b + 1] = cArr2[i14];
                                                } else {
                                                    int i15 = (mVar.c * charValue) + mVar.h;
                                                    int i16 = (mVar.d * charValue) + mVar.i;
                                                    cArr4[mVar.b] = cArr2[i15];
                                                    cArr4[mVar.b + 1] = cArr2[i16];
                                                }
                                                break;
                                            default:
                                                int i17 = $10 + 61;
                                                $11 = i17 % 128;
                                                if (i17 % 2 == 0) {
                                                }
                                                try {
                                                    Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                    Object obj4 = o.e.a.s.get(1075449051);
                                                    if (obj4 == null) {
                                                        Class cls4 = (Class) o.e.a.c(View.resolveSize(0, 0) + 11, (char) View.MeasureSpec.getSize(0), (ViewConfiguration.getWindowTouchSlop() >> 8) + 65);
                                                        byte b5 = (byte) 0;
                                                        byte b6 = (byte) (b5 + 1);
                                                        Object[] objArr9 = new Object[1];
                                                        h(b5, b6, (byte) (b6 - 1), objArr9);
                                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                        o.e.a.s.put(1075449051, obj4);
                                                    }
                                                    int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                    int i18 = (mVar.d * charValue) + mVar.h;
                                                    cArr4[mVar.b] = cArr2[intValue];
                                                    cArr4[mVar.b + 1] = cArr2[i18];
                                                    break;
                                                } catch (Throwable th2) {
                                                    Throwable cause2 = th2.getCause();
                                                    if (cause2 == null) {
                                                        throw th2;
                                                    }
                                                    throw cause2;
                                                }
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                                mVar.b += 2;
                        }
                    }
                }
                for (int i19 = 0; i19 < i; i19++) {
                    int i20 = $10 + Opcodes.LSHL;
                    $11 = i20 % 128;
                    int i21 = i20 % 2;
                    cArr4[i19] = (char) (cArr4[i19] ^ 13722);
                }
                objArr[0] = new String(cArr4);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:102:0x0307, code lost:
    
        r0 = r5;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1004
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.e.f(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
