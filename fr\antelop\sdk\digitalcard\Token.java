package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import o.eo.e;
import o.eo.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\Token.smali */
public final class Token {
    private final e digitalCard;
    private final o.el.e emvApplication;
    private final f innerToken;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\Token$Status.smali */
    public enum Status {
        Active,
        Suspended,
        Inactive
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\Token$Type.smali */
    public enum Type {
        SecureElement,
        Hce,
        CardOnFile,
        QRCode,
        Other
    }

    public Token(e eVar, o.el.e eVar2, f fVar) {
        this.digitalCard = eVar;
        this.emvApplication = eVar2;
        this.innerToken = fVar;
    }

    public final TokenServiceProvider getTokenServiceProvider() {
        if (this.innerToken.a() != null) {
            return this.innerToken.a().b();
        }
        return null;
    }

    public final String getId() {
        return this.innerToken.b();
    }

    public final String getTokenRequestorId() {
        return this.innerToken.e();
    }

    public final String getTokenRequestorName() {
        return this.innerToken.h();
    }

    public final Drawable getTokenRequestorLogo(Context context) {
        return this.innerToken.c(context);
    }

    public final String getDeviceName() {
        return this.innerToken.o();
    }

    public final boolean isApplePayToken() {
        return this.innerToken.g() != null && this.innerToken.g() == f.a.c;
    }

    public final boolean isGooglePayToken() {
        return this.innerToken.g() != null && this.innerToken.g() == f.a.e;
    }

    public final boolean isAntelopToken() {
        return this.innerToken.g() != null && this.innerToken.g() == f.a.d;
    }

    public final Type getType() {
        if (this.innerToken.j() != null) {
            return this.innerToken.j().c();
        }
        return null;
    }

    public final Status getStatus() {
        if (this.innerToken.n() != null) {
            return this.innerToken.n().d();
        }
        return null;
    }

    public final SecureTokenResume getSecureTokenResume() {
        return new SecureTokenResume(this.innerToken.c(this.emvApplication.i(), this.digitalCard));
    }

    public final void resume(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerToken.a(this.emvApplication.i(), this.digitalCard, context, operationCallback);
    }

    public final SecureTokenSuspend getSecureTokenSuspend() {
        return new SecureTokenSuspend(this.innerToken.a(this.emvApplication.i(), this.digitalCard));
    }

    public final void suspend(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerToken.e(this.emvApplication.i(), this.digitalCard, context, operationCallback);
    }

    public final SecureTokenDelete getSecureTokenDelete() {
        return new SecureTokenDelete(this.innerToken.b(this.emvApplication.i(), this.digitalCard));
    }

    public final void delete(Context context, OperationCallback<Void> operationCallback) throws WalletValidationException {
        this.innerToken.d(this.emvApplication.i(), this.digitalCard, context, operationCallback);
    }

    public final String toString() {
        return new StringBuilder("Token{tokenServiceProvider=").append(getTokenServiceProvider()).append(", tokenId='").append(getId()).append('\'').append(", tokenRequestorId='").append(getTokenRequestorId()).append('\'').append(", tokenRequestorName='").append(getTokenRequestorName()).append('\'').append(", tokenRequestorLogoUrl=").append(this.innerToken.f()).append(", tokenRequestorType=").append(this.innerToken.g()).append(", type=").append(getType()).append(", status=").append(getStatus()).append('}').toString();
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\Token$TokenServiceProvider.smali */
    public static final class TokenServiceProvider {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static final /* synthetic */ TokenServiceProvider[] $VALUES;
        public static final TokenServiceProvider MDES;
        public static final TokenServiceProvider VTS;
        private static long a;
        private static int b;
        private static int e;

        static void d() {
            a = -177942266400215984L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void f(int r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r8 = r8 * 2
                int r8 = r8 + 112
                int r6 = r6 * 2
                int r6 = 4 - r6
                byte[] r0 = fr.antelop.sdk.digitalcard.Token.TokenServiceProvider.$$a
                int r7 = r7 * 4
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r4 = r8
                r3 = r2
                r8 = r7
                goto L2e
            L19:
                r3 = r2
            L1a:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L27
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L27:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r5
            L2e:
                int r7 = r7 + r4
                int r6 = r6 + 1
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1a
            */
            throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.Token.TokenServiceProvider.f(int, byte, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{105, 1, -115, -23};
            $$b = Opcodes.IFLT;
        }

        private static /* synthetic */ TokenServiceProvider[] $values() {
            TokenServiceProvider[] tokenServiceProviderArr;
            int i = e;
            int i2 = i + 99;
            b = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    tokenServiceProviderArr = new TokenServiceProvider[5];
                    tokenServiceProviderArr[0] = MDES;
                    tokenServiceProviderArr[1] = VTS;
                    break;
                default:
                    tokenServiceProviderArr = new TokenServiceProvider[]{MDES, VTS};
                    break;
            }
            int i3 = i + 43;
            b = i3 % 128;
            int i4 = i3 % 2;
            return tokenServiceProviderArr;
        }

        private TokenServiceProvider(String str, int i) {
        }

        public static TokenServiceProvider valueOf(String str) {
            int i = e + 39;
            b = i % 128;
            int i2 = i % 2;
            TokenServiceProvider tokenServiceProvider = (TokenServiceProvider) Enum.valueOf(TokenServiceProvider.class, str);
            int i3 = e + 13;
            b = i3 % 128;
            int i4 = i3 % 2;
            return tokenServiceProvider;
        }

        public static TokenServiceProvider[] values() {
            int i = e + Opcodes.LSHL;
            b = i % 128;
            switch (i % 2 != 0) {
                case false:
                    return (TokenServiceProvider[]) $VALUES.clone();
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            b = 0;
            e = 1;
            d();
            Object[] objArr = new Object[1];
            c("⪍㷳ѫ泶", (-16771209) - Color.rgb(0, 0, 0), objArr);
            MDES = new TokenServiceProvider(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            c("⪖\ue671덙", 52453 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
            VTS = new TokenServiceProvider(((String) objArr2[0]).intern(), 1);
            $VALUES = $values();
            int i = e + 15;
            b = i % 128;
            int i2 = i % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void c(java.lang.String r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 880
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.Token.TokenServiceProvider.c(java.lang.String, int, java.lang.Object[]):void");
        }
    }
}
