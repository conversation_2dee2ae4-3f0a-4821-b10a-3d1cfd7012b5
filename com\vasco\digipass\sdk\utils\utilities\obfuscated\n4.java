package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\n4.smali */
public class n4 extends k4 {
    private BigInteger c;

    public n4(BigInteger bigInteger, m4 m4Var) {
        super(true, m4Var);
        this.c = bigInteger;
    }

    public BigInteger b() {
        return this.c;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k4
    public boolean equals(Object obj) {
        if ((obj instanceof n4) && ((n4) obj).b().equals(this.c)) {
            return super.equals(obj);
        }
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.k4
    public int hashCode() {
        return b().hashCode();
    }
}
