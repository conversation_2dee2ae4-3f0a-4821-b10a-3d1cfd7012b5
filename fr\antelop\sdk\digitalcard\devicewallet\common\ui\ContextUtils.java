package fr.antelop.sdk.digitalcard.devicewallet.common.ui;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\ContextUtils.smali */
public final class ContextUtils {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\ContextUtils$AlertDialogClickListener.smali */
    public interface AlertDialogClickListener {
        void onNegativeButtonClicked();

        void onPositiveButtonClicked();
    }

    public static AlertDialog buildDialog(Context context, int i, int i2, int i3, Integer num, final AlertDialogClickListener alertDialogClickListener) {
        AlertDialog.Builder positiveButton = new AlertDialog.Builder(context).setTitle(i).setMessage(i2).setPositiveButton(i3, new DialogInterface.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils$$ExternalSyntheticLambda0
            @Override // android.content.DialogInterface.OnClickListener
            public final void onClick(DialogInterface dialogInterface, int i4) {
                ContextUtils.lambda$buildDialog$0(ContextUtils.AlertDialogClickListener.this, dialogInterface, i4);
            }
        });
        if (num != null) {
            positiveButton.setNegativeButton(num.intValue(), new DialogInterface.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils$$ExternalSyntheticLambda1
                @Override // android.content.DialogInterface.OnClickListener
                public final void onClick(DialogInterface dialogInterface, int i4) {
                    ContextUtils.lambda$buildDialog$1(ContextUtils.AlertDialogClickListener.this, dialogInterface, i4);
                }
            });
        }
        return positiveButton.create();
    }

    static /* synthetic */ void lambda$buildDialog$0(AlertDialogClickListener alertDialogClickListener, DialogInterface dialogInterface, int i) {
        dialogInterface.dismiss();
        if (alertDialogClickListener != null) {
            alertDialogClickListener.onPositiveButtonClicked();
        }
    }

    static /* synthetic */ void lambda$buildDialog$1(AlertDialogClickListener alertDialogClickListener, DialogInterface dialogInterface, int i) {
        dialogInterface.dismiss();
        if (alertDialogClickListener != null) {
            alertDialogClickListener.onNegativeButtonClicked();
        }
    }
}
