package o.et;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\e.smali */
public class e extends c {
    private static int d = 0;
    private static int a = 1;

    @Override // o.el.d
    public final /* synthetic */ o.ey.e a(String str) {
        int i = a;
        int i2 = (i & 43) + (i | 43);
        d = i2 % 128;
        int i3 = i2 % 2;
        o.ev.c c = c(str);
        int i4 = a + 83;
        d = i4 % 128;
        int i5 = i4 % 2;
        return c;
    }

    public e(String str, String str2, int i, String str3) {
        super(str, o.dp.b.j, str2, i, str3);
    }

    @Override // o.et.c
    protected c c(String str, String str2, int i, String str3) {
        e eVar = new e(str, str2, i, str3);
        int i2 = a;
        int i3 = (i2 & 31) + (i2 | 31);
        d = i3 % 128;
        int i4 = i3 % 2;
        return eVar;
    }

    private o.ev.c c(String str) {
        o.ev.c cVar = new o.ev.c(n(), str, false);
        int i = (a + 82) - 1;
        d = i % 128;
        int i2 = i % 2;
        return cVar;
    }

    @Override // o.et.c
    public final EmvApplicationType e() {
        int i = a;
        int i2 = (i ^ 29) + ((i & 29) << 1);
        d = i2 % 128;
        int i3 = i2 % 2;
        EmvApplicationType emvApplicationType = EmvApplicationType.HceJcb;
        int i4 = a;
        int i5 = (i4 & 53) + (i4 | 53);
        d = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 30 : (char) 1) {
            case 1:
                int i6 = 83 / 0;
                return emvApplicationType;
            default:
                return emvApplicationType;
        }
    }

    @Override // o.et.c
    public final byte[] E() {
        int i = (a + 16) - 1;
        int i2 = i % 128;
        d = i2;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                int i3 = ((i2 | 15) << 1) - (i2 ^ 15);
                a = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 6 : '6') {
                    case Opcodes.ISTORE /* 54 */:
                        return null;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                throw null;
        }
    }
}
