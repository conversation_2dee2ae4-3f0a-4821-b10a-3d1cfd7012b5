package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.models.SecureChannelMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\SecureChannelParseResponse.smali */
public class SecureChannelParseResponse extends DigipassResponse {
    private SecureChannelMessage c;

    public SecureChannelParseResponse(int i) {
        super(i);
    }

    public SecureChannelMessage getMessage() {
        return this.c;
    }

    public SecureChannelParseResponse(int i, Throwable th) {
        super(i, th);
    }

    public SecureChannelParseResponse(int i, SecureChannelMessage secureChannelMessage) {
        super(i);
        this.c = secureChannelMessage;
    }
}
