package o.bu;

import android.content.Context;
import android.graphics.Color;
import android.location.Location;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import com.google.android.gms.location.LocationServices;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bu\a.smali */
final class a extends o.bs.e<o.bs.b> implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static long c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        e();
        TypedValue.complexToFloat(0);
        int i = b + 53;
        e = i % 128;
        switch (i % 2 == 0 ? (char) 11 : Typography.quote) {
            case 11:
                throw null;
            default:
                return;
        }
    }

    static void e() {
        c = 7975755511440538630L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bu.a.$$a
            int r8 = r8 * 3
            int r8 = 71 - r8
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r7 = r7 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bu.a.g(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{105, 1, -115, -23};
        $$b = 47;
    }

    a(o.bs.b bVar) {
        super(bVar);
    }

    @Override // o.bu.c
    public final Location d(Context context) {
        switch (!e(context)) {
            case true:
                int i = b + 71;
                e = i % 128;
                int i2 = i % 2;
                g.c();
                Object[] objArr = new Object[1];
                f("춙췞焖춠䩺㎧콡轢㺑緸✼\ue6e2⬰湸⩓퉗ះ嫓\u19cd\udfb5l䞅ൗ쬙ഌ뀙炵㡱禯볁搂◭橓\ua95c歰ᅃ囋", Color.rgb(0, 0, 0) + 16777217, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("흺휝喷\ue90bš碧\uded9\ue81e⑱奕氖\uf742㇛䫅慵쎪ൻ縮勘츁᪅挅䙏\udaa7ឮ钶㮫⧇捛頚⼘㑟炴跨\u2066ý䰿뺁ᖓ༜妑ꈢॷ᮷囤\ud7ab朗曆ꉗ\udb4c\ueff3畫", 1 - TextUtils.indexOf("", "", 0, 0), objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                return null;
            default:
                Location location = (Location) n.c(LocationServices.getFusedLocationProviderClient(context).getLastLocation());
                switch (location == null ? 'C' : '\n') {
                    case 'C':
                        int i3 = b + 91;
                        e = i3 % 128;
                        int i4 = i3 % 2;
                        g.c();
                        Object[] objArr3 = new Object[1];
                        f("춙췞焖춠䩺㎧콡轢㺑緸✼\ue6e2⬰湸⩓퉗ះ嫓\u19cd\udfb5l䞅ൗ쬙ഌ뀙炵㡱禯볁搂◭橓\ua95c歰ᅃ囋", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 1, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f("ㄯㅈ墵\ue409渇េ\ue722퇥숤呗Ͱ캹힎䟇ณ祝\ueb2e猬㶷\uf7faﲟ渌⤪\ue35a\uf1ba馰哈ဲ蔙锘䁿ඤ雠胳伅㤓ꩪ뎔", (ViewConfiguration.getJumpTapTimeout() >> 16) + 1, objArr4);
                        g.d(intern2, ((String) objArr4[0]).intern());
                        return location;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f("춙췞焖춠䩺㎧콡轢㺑緸✼\ue6e2⬰湸⩓퉗ះ嫓\u19cd\udfb5l䞅ൗ쬙ഌ뀙炵㡱禯볁搂◭橓\ua95c歰ᅃ囋", AndroidCharacter.getMirror('0') - '/', objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr6 = new Object[1];
                        f("\ue7d2\ue7b5樹횅Ჰ敶檎屉ᓙ曛燇䌕ų畋粤矽㷓䆠伂穖⨡岍宆滼❉ꬦ☶鶃可Ꟈ㋕老䀘뉵㶺듩糈腜", -TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr6);
                        g.d(intern3, sb.append(((String) objArr6[0]).intern()).append(location.toString()).toString());
                        return location;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 372
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bu.a.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
