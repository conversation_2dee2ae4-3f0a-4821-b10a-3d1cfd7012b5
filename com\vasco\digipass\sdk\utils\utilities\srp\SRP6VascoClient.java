package com.vasco.digipass.sdk.utils.utilities.srp;

import bc.org.bouncycastle.crypto.CryptoException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.d7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g7;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\srp\SRP6VascoClient.smali */
public class SRP6VascoClient extends d7 {
    private boolean n = false;

    private static void a(BigInteger bigInteger, BigInteger bigInteger2) throws UtilitiesSDKException {
        if (bigInteger == null) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_PRIVATE_KEY_NULL_OR_EMPTY);
        }
        BigInteger bigInteger3 = BigInteger.ZERO;
        if (bigInteger3.equals(bigInteger) || bigInteger.mod(bigInteger2).equals(bigInteger3)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_PRIVATE_KEY_INVALID);
        }
        if (f1.a(bigInteger).length > 256) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_PRIVATE_KEY_INCORRECT_LENGTH);
        }
    }

    public UtilitiesSDKCryptoResponse calculateHashedSecret(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        try {
            if (!this.n) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SRP_CLIENT_NOT_INITIALIZED);
            }
            SRP6VascoUtils.b(bigInteger, this.a);
            SRP6VascoUtils.a(bigInteger2, this.a);
            a(bigInteger3, this.a);
            SRP6VascoUtils.e(bArr);
            SRP6VascoUtils.b(bArr2);
            SRP6VascoUtils.d(bArr3);
            this.c = bigInteger3;
            this.d = bigInteger2;
            this.f = g7.calculateX(this.l, this.a, bArr, bArr2, bArr3);
            calculateSecret(bigInteger);
            return new UtilitiesSDKCryptoResponse(0, SRP6VascoUtils.a(this.l, this.h));
        } catch (CryptoException e) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        } catch (UtilitiesSDKException e2) {
            return new UtilitiesSDKCryptoResponse(e2.getReturnErrorCode());
        }
    }

    public BigInteger generateClientCredentials() throws UtilitiesSDKException {
        try {
            if (!this.n) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SRP_CLIENT_NOT_INITIALIZED);
            }
            BigInteger b = b();
            this.c = b;
            BigInteger modPow = this.b.modPow(b, this.a);
            this.d = modPow;
            return modPow;
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public BigInteger getPrivA() {
        return this.c;
    }

    public void init(BigInteger bigInteger, BigInteger bigInteger2, byte b, SecureRandom secureRandom) throws UtilitiesSDKException {
        try {
            SRP6VascoUtils.c(bigInteger);
            SRP6VascoUtils.b(bigInteger2);
            SRP6VascoUtils.a(b);
            if (secureRandom == null) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SECURE_RANDOM_NULL_OR_EMPTY);
            }
            super.init(bigInteger, bigInteger2, SRP6VascoUtils.b(b), secureRandom);
            this.n = true;
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public boolean verifyServerEvidenceMessage(byte[] bArr, byte[] bArr2, BigInteger bigInteger, byte[] bArr3) throws UtilitiesSDKException {
        try {
            if (!this.n) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SRP_CLIENT_NOT_INITIALIZED);
            }
            a(bArr);
            SRP6VascoUtils.a(bArr2);
            SRP6VascoUtils.a(bigInteger, this.a);
            SRP6VascoUtils.c(bArr3);
            UtilitiesSDKCryptoResponse calculateServerEvidenceMessage = SRP6VascoUtils.calculateServerEvidenceMessage(SRP6VascoUtils.a(this.l), bArr2, bigInteger, bArr3);
            int returnCode = calculateServerEvidenceMessage.getReturnCode();
            if (returnCode == 0) {
                return Arrays.equals(bArr, calculateServerEvidenceMessage.getOutputData());
            }
            throw new UtilitiesSDKException(returnCode);
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    private static void a(byte[] bArr) throws UtilitiesSDKException {
        if (bArr != null) {
            if (bArr.length != 32) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_MESSAGE_INCORRECT_LENGTH);
            }
            return;
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_MESSAGE_NULL_OR_EMPTY);
    }
}
