package o.et;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;
import kotlin.io.encoding.Base64;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\h.smali */
public class h extends c {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] l;
    private static int m;

    /* renamed from: o, reason: collision with root package name */
    private static int f80o;
    private String a;
    private byte[] b;
    private byte[] c;
    private byte[] d;
    private int e;
    private String f;
    private byte[] g;
    private boolean h;
    private boolean i;
    private boolean j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f80o = 0;
        m = 1;
        S();
        int i = m + 79;
        f80o = i % 128;
        switch (i % 2 != 0 ? (char) 24 : 'A') {
            case 24:
                throw null;
            default:
                return;
        }
    }

    static void S() {
        l = new char[]{50753, 51157, 51163, 51157, 51161, 51137, 51165, 51161, 51157, 51159, 51151, 50737, 51160, 51166, 51137, 51161, 51160, 51163, 51152, 51161, 51148, 50840, 50795, 50798, 50764, 50762, 50814, 50790, 50795, 50761, 50765, 50794, 50789, 50785, 50787, 50791, 50787, 50788, 50789, 50763, 50862, 50862, 50761, 50795, 50782, 50774, 50814, 50790, 50779, 50771, 50788, 50798, 50942, 50859, 50849, 50860, 50836, 50851, 50875, 50835, 50843, 50852, 50826, 50912, 50912, 50826, 50852, 50851, 50875, 50823, 50825, 50859, 50852, 50826, 50912, 50912};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void V(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 66
            int r7 = r7 * 3
            int r7 = 3 - r7
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r0 = o.et.h.$$d
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L35
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.et.h.V(byte, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{71, 41, -111, Base64.padSymbol};
        $$e = Opcodes.DRETURN;
    }

    @Override // o.el.d
    public /* synthetic */ o.ey.e a(String str) {
        int i = f80o + 81;
        m = i % 128;
        int i2 = i % 2;
        o.ey.b<? extends o.fg.a> c = c(str);
        int i3 = m + 11;
        f80o = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return c;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public h(String str, String str2, int i, String str3) {
        super(str, o.dp.b.a, str2, i, str3);
        this.i = false;
        this.j = true;
        this.h = true;
    }

    @Override // o.et.c
    protected c c(String str, String str2, int i, String str3) {
        h hVar = new h(str, str2, i, str3);
        int i2 = f80o + 77;
        m = i2 % 128;
        switch (i2 % 2 == 0 ? ':' : '\'') {
            case '\'':
                return hVar;
            default:
                throw null;
        }
    }

    public final String G() {
        int i = f80o;
        int i2 = i + 31;
        m = i2 % 128;
        int i3 = i2 % 2;
        String str = this.a;
        int i4 = i + Opcodes.LSUB;
        m = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 5 : '^') {
            case 5:
                throw null;
            default:
                return str;
        }
    }

    public final void i(String str) {
        int i = f80o;
        int i2 = i + Opcodes.LSUB;
        m = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.a = str;
        switch (z) {
            case false:
                int i3 = 70 / 0;
                break;
        }
        int i4 = i + 17;
        m = i4 % 128;
        int i5 = i4 % 2;
    }

    public final byte[] M() {
        int i = f80o + 63;
        m = i % 128;
        switch (i % 2 == 0 ? 'R' : (char) 19) {
            case 19:
                return this.b;
            default:
                int i2 = 96 / 0;
                return this.b;
        }
    }

    public final void c(byte[] bArr) {
        int i = f80o;
        int i2 = i + 93;
        m = i2 % 128;
        char c = i2 % 2 == 0 ? '7' : '9';
        this.b = bArr;
        switch (c) {
            case '7':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i3 = i + 35;
                m = i3 % 128;
                int i4 = i3 % 2;
                return;
        }
    }

    public final byte[] N() {
        int i = f80o + 47;
        int i2 = i % 128;
        m = i2;
        int i3 = i % 2;
        byte[] bArr = this.d;
        int i4 = i2 + 5;
        f80o = i4 % 128;
        switch (i4 % 2 != 0 ? 'Q' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                return bArr;
            default:
                throw null;
        }
    }

    public final void h(byte[] bArr) {
        int i = f80o;
        int i2 = i + 57;
        m = i2 % 128;
        char c = i2 % 2 == 0 ? (char) 31 : 'J';
        this.d = bArr;
        switch (c) {
            case 'J':
                int i3 = i + 31;
                m = i3 % 128;
                int i4 = i3 % 2;
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final int J() {
        int i = f80o + 43;
        m = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return this.e;
        }
    }

    public final void d(int i) {
        int i2 = m + Opcodes.LSUB;
        int i3 = i2 % 128;
        f80o = i3;
        int i4 = i2 % 2;
        this.e = i;
        int i5 = i3 + 49;
        m = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 3 : '@') {
            case '@':
                return;
            default:
                throw null;
        }
    }

    public final byte[] L() {
        int i = f80o;
        int i2 = i + 57;
        m = i2 % 128;
        int i3 = i2 % 2;
        byte[] bArr = this.g;
        int i4 = i + 61;
        m = i4 % 128;
        int i5 = i4 % 2;
        return bArr;
    }

    public final void i(byte[] bArr) {
        int i = f80o + Opcodes.DREM;
        m = i % 128;
        boolean z = i % 2 == 0;
        this.g = bArr;
        switch (z) {
            case false:
                return;
            default:
                int i2 = 81 / 0;
                return;
        }
    }

    public final byte[] K() {
        int i = m;
        int i2 = i + Opcodes.LMUL;
        f80o = i2 % 128;
        int i3 = i2 % 2;
        byte[] bArr = this.c;
        int i4 = i + Opcodes.LUSHR;
        f80o = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bArr;
        }
    }

    public final void j(byte[] bArr) {
        int i = f80o + Opcodes.DREM;
        int i2 = i % 128;
        m = i2;
        char c = i % 2 == 0 ? (char) 31 : (char) 5;
        this.c = bArr;
        switch (c) {
            case 5:
                break;
            default:
                int i3 = 27 / 0;
                break;
        }
        int i4 = i2 + 95;
        f80o = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String O() {
        int i = m;
        int i2 = i + 73;
        f80o = i2 % 128;
        int i3 = i2 % 2;
        String str = this.f;
        int i4 = i + Opcodes.LNEG;
        f80o = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final void j(String str) {
        int i = m + 109;
        f80o = i % 128;
        int i2 = i % 2;
        this.f = str.toLowerCase(o.ee.j.a());
        g(o.dk.b.d(str));
        int i3 = f80o + Opcodes.LSHL;
        m = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.et.c
    public final byte[] E() {
        int i = m + 3;
        f80o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        U("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000", new int[]{0, 21, Opcodes.TABLESWITCH, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        U("\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{21, 31, 61, 0}, true, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.fc.e b = o.ei.c.c().a().f().b(this);
        if (b != null) {
            switch (b instanceof o.fg.a) {
                case false:
                    break;
                default:
                    o.fg.a aVar = (o.fg.a) b;
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    U("\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000", new int[]{0, 21, Opcodes.TABLESWITCH, 0}, true, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    U("\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{52, 24, 0, 0}, false, objArr4);
                    o.ee.g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(o.dk.b.e(aVar.f())).toString());
                    return aVar.f();
            }
        }
        int i3 = f80o + 89;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 71 / 0;
                return null;
            default:
                return null;
        }
    }

    public o.ey.b<? extends o.fg.a> c(String str) {
        o.eu.d dVar = new o.eu.d(n(), str, false);
        int i = f80o + 23;
        m = i % 128;
        int i2 = i % 2;
        return dVar;
    }

    @Override // o.et.c
    public EmvApplicationType e() {
        int i = f80o + Opcodes.DSUB;
        m = i % 128;
        switch (i % 2 != 0) {
            case true:
                return EmvApplicationType.HceIssuerMastercard;
            default:
                EmvApplicationType emvApplicationType = EmvApplicationType.HceIssuerMastercard;
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:40:0x00f9, code lost:
    
        if (r0[r6] == 0) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0192, code lost:
    
        r6 = r3.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x0199, code lost:
    
        r11 = new java.lang.Object[]{java.lang.Integer.valueOf(r1[r3.d]), java.lang.Integer.valueOf(r2)};
        r2 = o.e.a.s.get(804049217);
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x01b6, code lost:
    
        if (r2 == null) goto L63;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x0210, code lost:
    
        r4[r6] = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r11)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x0212, code lost:
    
        r2 = r4[r3.d];
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0216, code lost:
    
        r6 = new java.lang.Object[]{r3, r3};
        r9 = o.e.a.s.get(-2112603350);
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0227, code lost:
    
        if (r9 == null) goto L70;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x022a, code lost:
    
        r9 = (java.lang.Class) o.e.a.c(11 - (android.view.ViewConfiguration.getTapTimeout() >> 16), (char) (android.text.TextUtils.lastIndexOf("", '0', 0) + 1), 259 - (android.view.ViewConfiguration.getScrollDefaultDelay() >> 16));
        r14 = (byte) 0;
        r15 = r14;
        r12 = new java.lang.Object[1];
        V(r14, r15, r15, r12);
        r9 = r9.getMethod((java.lang.String) r12[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-2112603350, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x027f, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x0280, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0284, code lost:
    
        if (r1 != null) goto L76;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x0286, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0287, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x01b9, code lost:
    
        r10 = (java.lang.Class) o.e.a.c(android.view.View.getDefaultSize(0, 0) + 10, (char) (android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0) + 1), 206 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0));
        r12 = (byte) 0;
        r14 = r12;
        r2 = new java.lang.Object[1];
        V(r12, r14, (byte) (r14 | 56), r2);
        r2 = r10.getMethod((java.lang.String) r2[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(804049217, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0288, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0289, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x028d, code lost:
    
        if (r1 != null) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x028f, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x0290, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x0102, code lost:
    
        r6 = r3.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:70:0x0109, code lost:
    
        r11 = new java.lang.Object[]{java.lang.Integer.valueOf(r1[r3.d]), java.lang.Integer.valueOf(r2)};
        r2 = o.e.a.s.get(2016040108);
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x0126, code lost:
    
        if (r2 == null) goto L51;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x0185, code lost:
    
        r4[r6] = ((java.lang.Character) ((java.lang.reflect.Method) r2).invoke(null, r11)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0129, code lost:
    
        r2 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 10, (char) (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1)) + 447);
        r14 = (byte) 0;
        r15 = r14;
        r12 = new java.lang.Object[1];
        V(r14, r15, (byte) (r15 | 53), r12);
        r2 = r2.getMethod((java.lang.String) r12[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(2016040108, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0189, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x018a, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x018e, code lost:
    
        if (r1 != null) goto L57;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x0190, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0191, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x00ff, code lost:
    
        if (r0[r6] == 1) goto L47;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v25, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void U(java.lang.String r22, int[] r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 820
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.et.h.U(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
