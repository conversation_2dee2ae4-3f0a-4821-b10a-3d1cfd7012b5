package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.WNafUtil;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a.smali */
public class a {
    static e8 a = new C0011a();
    static final Hashtable b = new Hashtable();
    static final Hashtable c = new Hashtable();
    static final Hashtable d = new Hashtable();

    /* renamed from: com.vasco.digipass.sdk.utils.utilities.obfuscated.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a$a.smali */
    class C0011a extends e8 {
        C0011a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return a.b(new ECCurve.Fp(a.b("F1FD178C0B3AD58F10126DE8CE42435B3961ADBCABC8CA6DE8FCF353D86E9C03"), a.b("F1FD178C0B3AD58F10126DE8CE42435B3961ADBCABC8CA6DE8FCF353D86E9C00"), a.b("EE353FCA5428A9300D4ABA754A44C00FDFEC0C9AE4B1A1803075ED967B7BB73F"), a.b("F1FD178C0B3AD58F10126DE8CE42435B53DC67E140D2BF941FFDD459C6D655E1"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve a = a();
            return new X9ECParameters(a, a.b(a, "04B6B3D4C356C139EB31183D4749D423958C27D2DCAF98B70164C97A2DD98F5CFF6142E0F7C8B204911F9271F0F3ECEF8C2701C307E8E4C9E183115A1554062CFB"), a.getOrder(), a.getCofactor(), null);
        }
    }

    static {
        a("FRP256v1", b.a, a);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, String str) {
        f8 f8Var = new f8(eCCurve, z4.a(str));
        WNafUtil.configureBasepoint(f8Var.e());
        return f8Var;
    }

    public static X9ECParameters c(String str) {
        w e = e(str);
        if (e == null) {
            return null;
        }
        return a(e);
    }

    public static e8 d(String str) {
        w e = e(str);
        if (e == null) {
            return null;
        }
        return b(e);
    }

    public static w e(String str) {
        return (w) b.get(o7.b(str));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BigInteger b(String str) {
        return new BigInteger(1, z4.a(str));
    }

    static void a(String str, w wVar, e8 e8Var) {
        b.put(o7.b(str), wVar);
        d.put(wVar, str);
        c.put(wVar, e8Var);
    }

    public static e8 b(w wVar) {
        return (e8) c.get(wVar);
    }

    public static X9ECParameters a(w wVar) {
        e8 b2 = b(wVar);
        if (b2 == null) {
            return null;
        }
        return b2.d();
    }

    public static Enumeration a() {
        return d.elements();
    }
}
