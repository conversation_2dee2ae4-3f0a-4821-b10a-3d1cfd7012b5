package com.capacitorjs.plugins.pushnotifications;

import android.app.Notification;
import android.app.NotificationManager;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.service.notification.StatusBarNotification;
import androidx.core.app.NotificationCompat;
import com.getcapacitor.Bridge;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.PermissionState;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginHandle;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;
import com.getcapacitor.annotation.PermissionCallback;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.CommonNotificationBuilder;
import com.google.firebase.messaging.Constants;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.NotificationParams;
import com.google.firebase.messaging.RemoteMessage;
import java.util.Arrays;
import org.bouncycastle.i18n.MessageBundle;
import org.json.JSONException;
import org.json.JSONObject;

@CapacitorPlugin(name = "PushNotifications", permissions = {@Permission(alias = PushNotificationsPlugin.PUSH_NOTIFICATIONS, strings = {"android.permission.POST_NOTIFICATIONS"})})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\pushnotifications\PushNotificationsPlugin.smali */
public class PushNotificationsPlugin extends Plugin {
    private static final String EVENT_TOKEN_CHANGE = "registration";
    private static final String EVENT_TOKEN_ERROR = "registrationError";
    static final String PUSH_NOTIFICATIONS = "receive";
    public MessagingService firebaseMessagingService;
    private NotificationChannelManager notificationChannelManager;
    public NotificationManager notificationManager;
    public static Bridge staticBridge = null;
    public static RemoteMessage lastMessage = null;

    @Override // com.getcapacitor.Plugin
    public void load() {
        this.notificationManager = (NotificationManager) getActivity().getSystemService("notification");
        this.firebaseMessagingService = new MessagingService();
        staticBridge = this.bridge;
        RemoteMessage remoteMessage = lastMessage;
        if (remoteMessage != null) {
            fireNotification(remoteMessage);
            lastMessage = null;
        }
        this.notificationChannelManager = new NotificationChannelManager(getActivity(), this.notificationManager, getConfig());
    }

    @Override // com.getcapacitor.Plugin
    protected void handleOnNewIntent(Intent data) {
        super.handleOnNewIntent(data);
        Bundle bundle = data.getExtras();
        if (bundle != null && bundle.containsKey(Constants.MessagePayloadKeys.MSGID)) {
            JSObject notificationJson = new JSObject();
            JSObject dataObject = new JSObject();
            for (String key : bundle.keySet()) {
                if (key.equals(Constants.MessagePayloadKeys.MSGID)) {
                    notificationJson.put("id", bundle.getString(key));
                } else {
                    String valueStr = bundle.getString(key);
                    dataObject.put(key, valueStr);
                }
            }
            notificationJson.put(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, (Object) dataObject);
            JSObject actionJson = new JSObject();
            actionJson.put("actionId", "tap");
            actionJson.put("notification", (Object) notificationJson);
            notifyListeners("pushNotificationActionPerformed", actionJson, true);
        }
    }

    @Override // com.getcapacitor.Plugin
    @PluginMethod
    public void checkPermissions(PluginCall call) {
        if (Build.VERSION.SDK_INT < 33) {
            JSObject permissionsResultJSON = new JSObject();
            permissionsResultJSON.put(PUSH_NOTIFICATIONS, "granted");
            call.resolve(permissionsResultJSON);
            return;
        }
        super.checkPermissions(call);
    }

    @Override // com.getcapacitor.Plugin
    @PluginMethod
    public void requestPermissions(PluginCall call) {
        if (Build.VERSION.SDK_INT < 33 || getPermissionState(PUSH_NOTIFICATIONS) == PermissionState.GRANTED) {
            JSObject permissionsResultJSON = new JSObject();
            permissionsResultJSON.put(PUSH_NOTIFICATIONS, "granted");
            call.resolve(permissionsResultJSON);
            return;
        }
        requestPermissionForAlias(PUSH_NOTIFICATIONS, call, "permissionsCallback");
    }

    @PluginMethod
    public void register(PluginCall call) {
        FirebaseMessaging.getInstance().setAutoInitEnabled(true);
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener() { // from class: com.capacitorjs.plugins.pushnotifications.PushNotificationsPlugin$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.tasks.OnCompleteListener
            public final void onComplete(Task task) {
                PushNotificationsPlugin.this.lambda$register$0(task);
            }
        });
        call.resolve();
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$register$0(Task task) {
        if (!task.isSuccessful()) {
            sendError(task.getException().getLocalizedMessage());
        } else {
            sendToken((String) task.getResult());
        }
    }

    @PluginMethod
    public void unregister(PluginCall call) {
        FirebaseMessaging.getInstance().setAutoInitEnabled(false);
        FirebaseMessaging.getInstance().deleteToken();
        call.resolve();
    }

    @PluginMethod
    public void getDeliveredNotifications(PluginCall pluginCall) {
        JSArray jSArray = new JSArray();
        StatusBarNotification[] activeNotifications = this.notificationManager.getActiveNotifications();
        for (StatusBarNotification notif : activeNotifications) {
            JSObject jsNotif = new JSObject();
            jsNotif.put("id", notif.getId());
            jsNotif.put("tag", notif.getTag());
            Notification notification = notif.getNotification();
            if (notification != null) {
                jsNotif.put(MessageBundle.TITLE_ENTRY, notification.extras.getCharSequence(NotificationCompat.EXTRA_TITLE));
                jsNotif.put("body", notification.extras.getCharSequence(NotificationCompat.EXTRA_TEXT));
                jsNotif.put("group", notification.getGroup());
                jsNotif.put("groupSummary", (notification.flags & 512) != 0);
                JSObject extras = new JSObject();
                for (String key : notification.extras.keySet()) {
                    extras.put(key, notification.extras.getString(key));
                }
                jsNotif.put(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, (Object) extras);
            }
            jSArray.put(jsNotif);
        }
        JSObject jSObject = new JSObject();
        jSObject.put("notifications", (Object) jSArray);
        pluginCall.resolve(jSObject);
    }

    @PluginMethod
    public void removeDeliveredNotifications(PluginCall call) {
        JSArray notifications = call.getArray("notifications");
        try {
            for (Object o2 : notifications.toList()) {
                if (o2 instanceof JSONObject) {
                    JSObject notif = JSObject.fromJSONObject((JSONObject) o2);
                    String tag = notif.getString("tag");
                    Integer id = notif.getInteger("id");
                    if (tag == null) {
                        this.notificationManager.cancel(id.intValue());
                    } else {
                        this.notificationManager.cancel(tag, id.intValue());
                    }
                } else {
                    call.reject("Expected notifications to be a list of notification objects");
                }
            }
        } catch (JSONException e) {
            call.reject(e.getMessage());
        }
        call.resolve();
    }

    @PluginMethod
    public void removeAllDeliveredNotifications(PluginCall call) {
        this.notificationManager.cancelAll();
        call.resolve();
    }

    @PluginMethod
    public void createChannel(PluginCall call) {
        this.notificationChannelManager.createChannel(call);
    }

    @PluginMethod
    public void deleteChannel(PluginCall call) {
        this.notificationChannelManager.deleteChannel(call);
    }

    @PluginMethod
    public void listChannels(PluginCall call) {
        this.notificationChannelManager.listChannels(call);
    }

    public void sendToken(String token) {
        JSObject data = new JSObject();
        data.put("value", token);
        notifyListeners(EVENT_TOKEN_CHANGE, data, true);
    }

    public void sendError(String error) {
        JSObject data = new JSObject();
        data.put(Constants.IPC_BUNDLE_KEY_SEND_ERROR, error);
        notifyListeners(EVENT_TOKEN_ERROR, data, true);
    }

    public static void onNewToken(String newToken) {
        PushNotificationsPlugin pushPlugin = getPushNotificationsInstance();
        if (pushPlugin != null) {
            pushPlugin.sendToken(newToken);
        }
    }

    public static void sendRemoteMessage(RemoteMessage remoteMessage) {
        PushNotificationsPlugin pushPlugin = getPushNotificationsInstance();
        if (pushPlugin != null) {
            pushPlugin.fireNotification(remoteMessage);
        } else {
            lastMessage = remoteMessage;
        }
    }

    public void fireNotification(RemoteMessage remoteMessage) {
        JSObject remoteMessageData = new JSObject();
        JSObject data = new JSObject();
        remoteMessageData.put("id", remoteMessage.getMessageId());
        for (String key : remoteMessage.getData().keySet()) {
            Object value = remoteMessage.getData().get(key);
            data.put(key, value);
        }
        remoteMessageData.put(Constants.ScionAnalytics.MessageType.DATA_MESSAGE, (Object) data);
        RemoteMessage.Notification notification = remoteMessage.getNotification();
        if (notification != null) {
            String title = notification.getTitle();
            String body = notification.getBody();
            String[] presentation = getConfig().getArray("presentationOptions");
            if (presentation != null && Arrays.asList(presentation).contains("alert")) {
                Bundle bundle = null;
                if (Build.VERSION.SDK_INT >= 33) {
                    try {
                        ApplicationInfo applicationInfo = getContext().getPackageManager().getApplicationInfo(getContext().getPackageName(), PackageManager.ApplicationInfoFlags.of(128L));
                        bundle = applicationInfo.metaData;
                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                } else {
                    bundle = getBundleLegacy();
                }
                if (bundle != null) {
                    NotificationParams params = new NotificationParams(remoteMessage.toIntent().getExtras());
                    String channelId = CommonNotificationBuilder.getOrCreateChannel(getContext(), params.getNotificationChannelId(), bundle);
                    CommonNotificationBuilder.DisplayNotificationInfo notificationInfo = CommonNotificationBuilder.createNotificationInfo(getContext(), getContext(), params, channelId, bundle);
                    this.notificationManager.notify(notificationInfo.tag, notificationInfo.id, notificationInfo.notificationBuilder.build());
                }
            }
            remoteMessageData.put(MessageBundle.TITLE_ENTRY, title);
            remoteMessageData.put("body", body);
            remoteMessageData.put("click_action", notification.getClickAction());
            Uri link = notification.getLink();
            if (link != null) {
                remoteMessageData.put("link", link.toString());
            }
        }
        notifyListeners("pushNotificationReceived", remoteMessageData, true);
    }

    public static PushNotificationsPlugin getPushNotificationsInstance() {
        PluginHandle handle;
        Bridge bridge = staticBridge;
        if (bridge == null || bridge.getWebView() == null || (handle = staticBridge.getPlugin("PushNotifications")) == null) {
            return null;
        }
        return (PushNotificationsPlugin) handle.getInstance();
    }

    @PermissionCallback
    private void permissionsCallback(PluginCall call) {
        checkPermissions(call);
    }

    private Bundle getBundleLegacy() {
        try {
            ApplicationInfo applicationInfo = getContext().getPackageManager().getApplicationInfo(getContext().getPackageName(), 128);
            return applicationInfo.metaData;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return null;
        }
    }
}
