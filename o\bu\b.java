package o.bu;

import android.content.Context;
import android.location.Location;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.huawei.hms.location.LocationServices;
import o.ee.g;
import o.ee.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bu\b.smali */
final class b extends o.bs.e<o.bs.a> implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] a;
    private static int c;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        e();
        ViewConfiguration.getScrollBarFadeDuration();
        int i = c + 81;
        d = i % 128;
        switch (i % 2 != 0 ? '\f' : '\b') {
            case '\f':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        a = new int[]{-576065847, -774876593, 858419381, -1556675378, -1103420834, -1609751725, -1703161873, -936547867, -360884577, -1613712955, 1927068332, 675588358, -854233930, -2058271315, 2098597615, -576899683, -628994136, -1940363664};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.bu.b.$$a
            int r7 = r7 * 2
            int r7 = 4 - r7
            int r8 = r8 + 115
            int r9 = r9 * 4
            int r9 = r9 + 1
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L17:
            r3 = r2
        L18:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r9) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r9 = -r9
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bu.b.g(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -112, 34, 95};
        $$b = Opcodes.DNEG;
    }

    b(o.bs.a aVar) {
        super(aVar);
    }

    @Override // o.bu.c
    public final Location d(Context context) {
        String intern;
        Object obj;
        int i = d + Opcodes.DDIV;
        c = i % 128;
        int i2 = i % 2;
        switch (e(context)) {
            case true:
                Location location = (Location) n.e(LocationServices.getFusedLocationProviderClient(context).getLastLocation());
                switch (location == null) {
                    case true:
                        int i3 = d + 71;
                        c = i3 % 128;
                        if (i3 % 2 == 0) {
                            g.c();
                            Object[] objArr = new Object[1];
                            f(new int[]{-631983096, **********, 647298634, 474732400, -**********, **********, 476090669, -169584353, -**********, -**********, -61790871, **********, -227851806, 622185313, -881238780, -245170873, **********, **********}, MotionEvent.axisFromString("") * 114, objArr);
                            intern = ((String) objArr[0]).intern();
                            Object[] objArr2 = new Object[1];
                            f(new int[]{395543795, -189260839, -**********, **********, -780179343, -**********, -994333455, 712786237, -**********, -**********, **********, -**********, -**********, -847457124, 903307516, 1632492632, 1187940647, -1973150105}, 27 / KeyEvent.keyCodeFromString(""), objArr2);
                            obj = objArr2[0];
                        } else {
                            g.c();
                            Object[] objArr3 = new Object[1];
                            f(new int[]{-631983096, **********, 647298634, 474732400, -**********, **********, 476090669, -169584353, -**********, -**********, -61790871, **********, -227851806, 622185313, -881238780, -245170873, **********, **********}, 35 - MotionEvent.axisFromString(""), objArr3);
                            intern = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            f(new int[]{395543795, -189260839, -**********, **********, -780179343, -**********, -994333455, 712786237, -**********, -**********, **********, -**********, -**********, -847457124, 903307516, 1632492632, 1187940647, -1973150105}, KeyEvent.keyCodeFromString("") + 34, objArr4);
                            obj = objArr4[0];
                        }
                        g.d(intern, ((String) obj).intern());
                        return location;
                    default:
                        g.c();
                        Object[] objArr5 = new Object[1];
                        f(new int[]{-631983096, **********, 647298634, 474732400, -**********, **********, 476090669, -169584353, -**********, -**********, -61790871, **********, -227851806, 622185313, -881238780, -245170873, **********, **********}, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 36, objArr5);
                        String intern2 = ((String) objArr5[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr6 = new Object[1];
                        f(new int[]{395543795, -189260839, -**********, **********, -780179343, -**********, -1199937040, -560944371, 749839580, -829464653, -1793411858, -1287532267, -552744699, -501705104, -1858414140, 1805389, -286235565, -966159889}, 34 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr6);
                        g.d(intern2, sb.append(((String) objArr6[0]).intern()).append(location.toString()).toString());
                        return location;
                }
            default:
                g.c();
                Object[] objArr7 = new Object[1];
                f(new int[]{-631983096, **********, 647298634, 474732400, -**********, **********, 476090669, -169584353, -**********, -**********, -61790871, **********, -227851806, 622185313, -881238780, -245170873, **********, **********}, 36 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                f(new int[]{395543795, -189260839, -**********, **********, -780179343, -**********, -1854975632, -1718437833, 1614614013, 125857034, 1359906872, -2076746288, 316369687, -1712413789, 482844064, -641290054, -**********, -**********, 1532862265, 1210174475, 791135277, 164355019, -492018813, -31191537, -365057121, -351965716}, TextUtils.getTrimmedLength("") + 50, objArr8);
                g.e(intern3, ((String) objArr8[0]).intern());
                return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int[] r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 996
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bu.b.f(int[], int, java.lang.Object[]):void");
    }
}
