package o.dw;

import android.content.Context;
import android.graphics.Color;
import android.os.CancellationSignal;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dw\d.smali */
public abstract class d<P> extends j {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        b = 874635309;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dw.d.$$d
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = r7 + 107
            int r9 = r9 * 3
            int r9 = r9 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r8
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r9]
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r8 = -r8
            int r8 = r8 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.d.i(byte, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$d = new byte[]{37, -65, 15, -4};
        $$e = 239;
    }

    public abstract void launch(Context context, P p, String str, CancellationSignal cancellationSignal);

    @Override // o.dw.j
    public void onDisplayImpossible(o.bv.c cVar) {
        int i = d + Opcodes.DDIV;
        e = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        h('<' - AndroidCharacter.getMirror('0'), "\ufffb\u0006\n\r\u0003\uffde\uffff\f\u000f�\uffff￭\u0006\uffff\ufffe\t\uffe7\u0011\uffff\u0003\ufff0\u0013", (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 22, 184 - (Process.myPid() >> 22), true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h(65 - View.getDefaultSize(0, 0), "\u0014\n￥\uffc1\u0006\u0004\u0002\u0007\u0013\u0006\u0015\u000f￪\u0006\u0013\u0016\u0004\u0006\ufff4\uffc1\u0006\u0013\u0016\u0015\u0002\u0006\uffe7\uffc1ￛ\uffc1\u0005\u0006\u0015\u0013\u0010\u0011\u0011\u0016\u0014\uffc1\u0015\u0010\uffef\uffc1ￎ\uffc1\u0006\r\u0003\n\u0014\u0014\u0010\u0011\u000e￪\u001a\u0002\r\u0011\u0014\n￥\u000f\u0010\u0005\u0006\r\u0003\u0002", 70 - (ViewConfiguration.getEdgeSlop() >> 16), 177 - Color.alpha(0), true, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = d + Opcodes.LMUL;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? ':' : ' ') {
            case Opcodes.ASTORE /* 58 */:
                throw null;
            default:
                return;
        }
    }

    @Override // o.dw.j
    public void onDisplayCancelled() {
        int i = e + 55;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        h(12 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "\ufffb\u0006\n\r\u0003\uffde\uffff\f\u000f�\uffff￭\u0006\uffff\ufffe\t\uffe7\u0011\uffff\u0003\ufff0\u0013", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 22, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + Opcodes.INVOKESTATIC, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h(TextUtils.indexOf("", "") + 35, "ￂￜￂ￨\u0007\u0003\u0016\u0017\u0014\u0007ￂ\ufff5\u0007\u0005\u0017\u0014\u0007￫\u0010\u0016\u0007\u0014\b\u0003\u0005\u0007ￂ￦\u000b\u0015\u0003\u0004\u000e\u0007\u0006\u0011\u0010￦\u000b\u0015\u0012\u000e\u0003\u001b￥\u0003\u0010\u0005\u0007\u000e\u000e\u0007\u0006ￂￏￂ\ufff0\u0011\u0016ￂ\u0015\u0017\u0012\u0012\u0011\u0014\u0016\u0007\u0006", 69 - Color.blue(0), View.combineMeasuredStates(0, 0) + Opcodes.ARETURN, false, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = d + Opcodes.DSUB;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.dw.j
    public void onDisplaySuccess() {
        int i = e + 109;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        h(12 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), "\ufffb\u0006\n\r\u0003\uffde\uffff\f\u000f�\uffff￭\u0006\uffff\ufffe\t\uffe7\u0011\uffff\u0003\ufff0\u0013", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 23, 184 - KeyEvent.normalizeMetaState(0), true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h(67 - (ViewConfiguration.getFadingEdgeLength() >> 16), "\u0006\u0007\u000e\u0004\u0003\u0015\u000b￦ￂ\u0007\u0005\u0003\b\u0014\u0007\u0016\u0010￫\u0007\u0014\u0017\u0005\u0007\ufff5ￂ\u0007\u0014\u0017\u0016\u0003\u0007￨ￂￜￂ\u0006\u0007\u0016\u0014\u0011\u0012\u0012\u0017\u0015ￂ\u0016\u0011\ufff0ￂￏￂ\u0015\u0015\u0007\u0005\u0005\u0017\ufff5\u001b\u0003\u000e\u0012\u0015\u000b￦\u0010\u0011", 67 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.DRETURN, true, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = d + Opcodes.LREM;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 7 : '-') {
            case '-':
                return;
            default:
                int i4 = 7 / 0;
                return;
        }
    }

    public void setProcessCallback(o.p.g gVar) {
        int i = e + 67;
        d = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        h(12 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "\ufffb\u0006\n\r\u0003\uffde\uffff\f\u000f�\uffff￭\u0006\uffff\ufffe\t\uffe7\u0011\uffff\u0003\ufff0\u0013", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 21, 184 - (ViewConfiguration.getDoubleTapTimeout() >> 16), true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h(48 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "\ufff0\u0011\u0016ￂ\u0015\u0017\u0012\u0012\u0011\u0014\u0016\u0007\u0006ￂￜￂ￨\u0007\u0003\u0016\u0017\u0014\u0007ￂ\ufff5\u0007\u0005\u0017\u0014\u0007￫\u0010\u0016\u0007\u0014\b\u0003\u0005\u0007ￂ￦\u000b\u0015\u0003\u0004\u000e\u0007\u0006\u0015\u0007\u0016\ufff2\u0014\u0011\u0005\u0007\u0015\u0015￥\u0003\u000e\u000e\u0004\u0003\u0005\rￂￏￂ", 69 - TextUtils.getTrimmedLength(""), 176 - View.combineMeasuredStates(0, 0), false, objArr2);
        g.e(intern, ((String) objArr2[0]).intern());
        int i3 = d + 31;
        e = i3 % 128;
        switch (i3 % 2 != 0 ? '\"' : (char) 15) {
            case '\"':
                int i4 = 34 / 0;
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 514
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.d.h(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
