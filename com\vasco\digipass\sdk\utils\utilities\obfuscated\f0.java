package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;
import java.util.Iterator;
import org.bouncycastle.asn1.ASN1Encoding;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f0.smali */
public abstract class f0 extends b0 implements Iterable {
    static final o0 C = new a(f0.class, 17);
    protected final h[] b;
    protected h[] x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\f0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(e0 e0Var) {
            return e0Var.n();
        }
    }

    protected f0() {
        h[] hVarArr = i.d;
        this.b = hVarArr;
        this.x = hVarArr;
    }

    public static f0 a(j0 j0Var, boolean z) {
        return (f0) C.a(j0Var, z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return true;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        if (this.x == null) {
            h[] hVarArr = (h[]) this.b.clone();
            this.x = hVarArr;
            a(hVarArr);
        }
        return new k2(true, this.x);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new f3(this.b, this.x);
    }

    public h[] h() {
        return i.a(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        int length = this.b.length;
        int i = length + 1;
        while (true) {
            length--;
            if (length < 0) {
                return i;
            }
            i += this.b[length].toASN1Primitive().hashCode();
        }
    }

    @Override // java.lang.Iterable
    public Iterator<h> iterator() {
        return new Arrays.a(h());
    }

    public int size() {
        return this.b.length;
    }

    public String toString() {
        int size = size();
        if (size == 0) {
            return "[]";
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append('[');
        int i = 0;
        while (true) {
            stringBuffer.append(this.b[i]);
            i++;
            if (i >= size) {
                stringBuffer.append(']');
                return stringBuffer.toString();
            }
            stringBuffer.append(", ");
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (!(b0Var instanceof f0)) {
            return false;
        }
        f0 f0Var = (f0) b0Var;
        int size = size();
        if (f0Var.size() != size) {
            return false;
        }
        k2 k2Var = (k2) f();
        k2 k2Var2 = (k2) f0Var.f();
        for (int i = 0; i < size; i++) {
            b0 aSN1Primitive = k2Var.b[i].toASN1Primitive();
            b0 aSN1Primitive2 = k2Var2.b[i].toASN1Primitive();
            if (aSN1Primitive != aSN1Primitive2 && !aSN1Primitive.a(aSN1Primitive2)) {
                return false;
            }
        }
        return true;
    }

    protected f0(i iVar, boolean z) {
        h[] c;
        if (iVar != null) {
            if (z && iVar.b() >= 2) {
                c = iVar.a();
                a(c);
            } else {
                c = iVar.c();
            }
            this.b = c;
            if (!z && c.length >= 2) {
                c = null;
            }
            this.x = c;
            return;
        }
        throw new NullPointerException("'elementVector' cannot be null");
    }

    f0(boolean z, h[] hVarArr) {
        this.b = hVarArr;
        if (!z && hVarArr.length >= 2) {
            hVarArr = null;
        }
        this.x = hVarArr;
    }

    private static byte[] a(h hVar) {
        try {
            return hVar.toASN1Primitive().getEncoded(ASN1Encoding.DER);
        } catch (IOException e) {
            throw new IllegalArgumentException("cannot encode object added to SET");
        }
    }

    f0(h[] hVarArr, h[] hVarArr2) {
        this.b = hVarArr;
        this.x = hVarArr2;
    }

    private static boolean a(byte[] bArr, byte[] bArr2) {
        int i = bArr[0] & 223;
        int i2 = bArr2[0] & 223;
        if (i != i2) {
            return i < i2;
        }
        int min = Math.min(bArr.length, bArr2.length) - 1;
        for (int i3 = 1; i3 < min; i3++) {
            byte b = bArr[i3];
            byte b2 = bArr2[i3];
            if (b != b2) {
                return (b & 255) < (b2 & 255);
            }
        }
        return (bArr[min] & 255) <= (bArr2[min] & 255);
    }

    private static void a(h[] hVarArr) {
        int length = hVarArr.length;
        if (length < 2) {
            return;
        }
        h hVar = hVarArr[0];
        h hVar2 = hVarArr[1];
        byte[] a2 = a(hVar);
        byte[] a3 = a(hVar2);
        if (a(a3, a2)) {
            hVar2 = hVar;
            hVar = hVar2;
            a3 = a2;
            a2 = a3;
        }
        for (int i = 2; i < length; i++) {
            h hVar3 = hVarArr[i];
            byte[] a4 = a(hVar3);
            if (a(a3, a4)) {
                hVarArr[i - 2] = hVar;
                hVar = hVar2;
                a2 = a3;
                hVar2 = hVar3;
                a3 = a4;
            } else if (a(a2, a4)) {
                hVarArr[i - 2] = hVar;
                hVar = hVar3;
                a2 = a4;
            } else {
                int i2 = i - 1;
                while (true) {
                    i2--;
                    if (i2 <= 0) {
                        break;
                    }
                    h hVar4 = hVarArr[i2 - 1];
                    if (a(a(hVar4), a4)) {
                        break;
                    } else {
                        hVarArr[i2] = hVar4;
                    }
                }
                hVarArr[i2] = hVar3;
            }
        }
        hVarArr[length - 2] = hVar;
        hVarArr[length - 1] = hVar2;
    }
}
