package fr.antelop.sdk.digitalcard;

import fr.antelop.sdk.exception.WalletValidationDomain;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumberOption.smali */
public final class VirtualCardNumberOption {
    private final Integer maxPaymentNumber;
    private final String name;
    private final String validityDurationFormat;

    VirtualCardNumberOption(String str, String str2, Integer num) {
        this.name = str;
        this.validityDurationFormat = str2;
        this.maxPaymentNumber = num;
    }

    public final String getName() {
        return this.name;
    }

    public final String getValidityDurationFormat() {
        return this.validityDurationFormat;
    }

    public final Integer getMaxPaymentNumber() {
        return this.maxPaymentNumber;
    }

    public final void validate() throws WalletValidationException {
        String str = this.name;
        if (str != null && !o.a.a(str, 0, 64)) {
            throw new WalletValidationException(WalletValidationErrorCode.InvalidFormat, WalletValidationDomain.VIRTUAL_CARD_NUMBER_OPTION, "`name` must contain between 0 and 64 char");
        }
        String str2 = this.validityDurationFormat;
        if (str2 != null && !o.a.a(str2, 0, 10)) {
            throw new WalletValidationException(WalletValidationErrorCode.InvalidFormat, WalletValidationDomain.VIRTUAL_CARD_NUMBER_OPTION, "`validityDurationFormat` must contain between 0 and 10 char");
        }
        Integer num = this.maxPaymentNumber;
        if (num != null && num.intValue() > 10000) {
            throw new WalletValidationException(WalletValidationErrorCode.InvalidFormat, WalletValidationDomain.VIRTUAL_CARD_NUMBER_OPTION, "`maxPaymentNumber` must be equals or below 10000");
        }
    }

    public final String toString() {
        return new StringBuilder("VirtualCardNumberOption{name='").append(this.name).append('\'').append(", validityDurationFormat='").append(this.validityDurationFormat).append('\'').append(", maxPaymentNumber=").append(this.maxPaymentNumber).append('}').toString();
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumberOption$Builder.smali */
    public static final class Builder {
        private Integer maxPaymentNumber;
        private String name;
        private String validityDurationFormat;

        public final Builder setName(String str) {
            this.name = str;
            return this;
        }

        public final Builder setValidityDuration(ValidityUnit validityUnit, int i) {
            this.validityDurationFormat = new StringBuilder().append(i).append(validityUnit.format).toString();
            return this;
        }

        public final Builder setMaxPaymentNumber(Integer num) {
            this.maxPaymentNumber = num;
            return this;
        }

        public final VirtualCardNumberOption build() {
            return new VirtualCardNumberOption(this.name, this.validityDurationFormat, this.maxPaymentNumber);
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumberOption$Builder$ValidityUnit.smali */
        public enum ValidityUnit {
            Hour("h"),
            Day("d"),
            Year("y");

            final String format;

            ValidityUnit(String str) {
                this.format = str;
            }
        }
    }
}
