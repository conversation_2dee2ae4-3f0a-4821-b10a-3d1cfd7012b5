package com.google.android.datatransport.runtime;

import com.google.android.datatransport.Priority;
import com.google.android.datatransport.Transport;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\ForcedSender.smali */
public final class ForcedSender {
    public static void sendBlocking(Transport<?> transport, Priority priority) {
        TransportContext context = getTransportContextOrThrow(transport).withPriority(priority);
        TransportRuntime.getInstance().getUploader().logAndUpdateState(context, 1);
    }

    private static TransportContext getTransportContextOrThrow(Transport<?> transport) {
        if (transport instanceof TransportImpl) {
            return ((TransportImpl) transport).getTransportContext();
        }
        throw new IllegalArgumentException("Expected instance of TransportImpl.");
    }

    private ForcedSender() {
    }
}
