package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y4.smali */
class y4 implements n6 {
    protected final r4 a;
    protected final m6 b;

    y4(r4 r4Var, m6 m6Var) {
        this.a = r4Var;
        this.b = m6Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.n6
    public m6 a() {
        return this.b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r4
    public int b() {
        return this.a.b() * this.b.b();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.r4
    public BigInteger c() {
        return this.a.c();
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof y4)) {
            return false;
        }
        y4 y4Var = (y4) obj;
        return this.a.equals(y4Var.a) && this.b.equals(y4Var.b);
    }

    public int hashCode() {
        return this.a.hashCode() ^ e5.a(this.b.hashCode(), 16);
    }
}
