package o.et;

import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\d.smali */
public final class d extends g {
    private static int a = 0;
    private static int c = 1;

    public d(String str, String str2, int i, String str3) {
        super(str, str2, i, str3);
        b(o.dp.b.d);
    }

    @Override // o.et.g, o.et.c
    public final EmvApplicationType e() {
        int i = c;
        int i2 = ((i | 81) << 1) - (i ^ 81);
        a = i2 % 128;
        int i3 = i2 % 2;
        EmvApplicationType emvApplicationType = EmvApplicationType.HceIdemiaPure;
        int i4 = (a + 14) - 1;
        c = i4 % 128;
        int i5 = i4 % 2;
        return emvApplicationType;
    }
}
