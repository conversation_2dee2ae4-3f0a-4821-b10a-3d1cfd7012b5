package fr.antelop.sdk.hms;

import android.content.Context;
import com.huawei.hms.push.RemoteMessage;
import o.bo.a;
import o.bo.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\hms\AntelopHmsMessageUtil.smali */
public final class AntelopHmsMessageUtil {
    public static void onTokenRefresh(Context context) {
        a.e(context);
    }

    public static boolean onMessageReceived(Context context, RemoteMessage remoteMessage) {
        return a.e(context, c.a(remoteMessage));
    }
}
