package androidx.loader.content;

import androidx.core.os.OperationCanceledException;
import java.util.concurrent.CountDownLatch;

/* JADX INFO: Add missing generic type declarations: [D] */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\loader\content\AsyncTaskLoader$LoadTask.smali */
final class AsyncTaskLoader$LoadTask<D> extends ModernAsyncTask<Void, Void, D> implements Runnable {
    private final CountDownLatch mDone = new CountDownLatch(1);
    final /* synthetic */ AsyncTaskLoader this$0;
    boolean waiting;

    AsyncTaskLoader$LoadTask(AsyncTaskLoader this$0) {
        this.this$0 = this$0;
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // androidx.loader.content.ModernAsyncTask
    public D doInBackground(Void... voidArr) {
        try {
            return (D) this.this$0.onLoadInBackground();
        } catch (OperationCanceledException e) {
            if (!isCancelled()) {
                throw e;
            }
            return null;
        }
    }

    @Override // androidx.loader.content.ModernAsyncTask
    protected void onPostExecute(D data) {
        try {
            this.this$0.dispatchOnLoadComplete(this, data);
        } finally {
            this.mDone.countDown();
        }
    }

    @Override // androidx.loader.content.ModernAsyncTask
    protected void onCancelled(D data) {
        try {
            this.this$0.dispatchOnCancelled(this, data);
        } finally {
            this.mDone.countDown();
        }
    }

    @Override // java.lang.Runnable
    public void run() {
        this.waiting = false;
        this.this$0.executePendingTask();
    }

    public void waitForLoader() {
        try {
            this.mDone.await();
        } catch (InterruptedException e) {
        }
    }
}
