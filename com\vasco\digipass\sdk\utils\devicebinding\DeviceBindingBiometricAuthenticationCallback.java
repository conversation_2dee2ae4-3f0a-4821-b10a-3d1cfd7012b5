package com.vasco.digipass.sdk.utils.devicebinding;

import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0007\u001a\u00020\bH&¨\u0006\t"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;", "", "onAuthenticationFailed", "", "exception", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDKException;", "onAuthenticationSucceeded", "fingerprint", "", "lib_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingBiometricAuthenticationCallback.smali */
public interface DeviceBindingBiometricAuthenticationCallback {
    void onAuthenticationFailed(DeviceBindingSDKException exception);

    void onAuthenticationSucceeded(String fingerprint);
}
