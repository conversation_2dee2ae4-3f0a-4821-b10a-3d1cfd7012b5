package o.ac;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationDomain;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\a.smali */
public interface a {
    default <T> void d(T t, String str) throws WalletValidationException {
        switch (t == null ? 'Y' : (char) 7) {
            case 7:
                return;
            default:
                throw d(str);
        }
    }

    default void e(int i, int i2, int i3, String str) throws WalletValidationException {
        switch (!o.a.a(i, i2, i3) ? 'Q' : 'Y') {
            case Opcodes.DUP /* 89 */:
                return;
            default:
                throw d(str);
        }
    }

    default void c(String str, int i, int i2, String str2) throws WalletValidationException {
        switch (str == null) {
            case false:
                switch (!o.a.a(str, i, i2) ? (char) 25 : Typography.quote) {
                    case 25:
                        throw d(str2);
                    default:
                        return;
                }
            default:
                return;
        }
    }

    default void d(byte[] bArr, int i, String str) throws WalletValidationException {
        boolean z = false;
        if (!o.a.b(bArr, 0, i)) {
            z = true;
        }
        switch (z) {
            case false:
                return;
            default:
                throw d(str);
        }
    }

    default WalletValidationException d(String str) {
        return new WalletValidationException(WalletValidationErrorCode.InvalidFormat, WalletValidationDomain.CREATE_CARD_REQUEST_BUILDER, str);
    }
}
