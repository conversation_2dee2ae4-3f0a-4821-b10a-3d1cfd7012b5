package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\m1.smali */
class m1 extends InputStream {
    private InputStream C;
    private final g0 b;
    private boolean x = true;

    m1(g0 g0Var) {
        this.b = g0Var;
    }

    private y a() throws IOException {
        h a = this.b.a();
        if (a == null) {
            return null;
        }
        if (a instanceof y) {
            return (y) a;
        }
        throw new IOException("unknown object encountered: " + a.getClass());
    }

    @Override // java.io.InputStream
    public int read(byte[] bArr, int i, int i2) throws IOException {
        y a;
        int i3 = 0;
        if (this.C == null) {
            if (!this.x || (a = a()) == null) {
                return -1;
            }
            this.x = false;
            this.C = a.c();
        }
        while (true) {
            int read = this.C.read(bArr, i + i3, i2 - i3);
            if (read >= 0) {
                i3 += read;
                if (i3 == i2) {
                    return i3;
                }
            } else {
                y a2 = a();
                if (a2 == null) {
                    this.C = null;
                    if (i3 < 1) {
                        return -1;
                    }
                    return i3;
                }
                this.C = a2.c();
            }
        }
    }

    @Override // java.io.InputStream
    public int read() throws IOException {
        y a;
        if (this.C == null) {
            if (!this.x || (a = a()) == null) {
                return -1;
            }
            this.x = false;
            this.C = a.c();
        }
        while (true) {
            int read = this.C.read();
            if (read >= 0) {
                return read;
            }
            y a2 = a();
            if (a2 == null) {
                this.C = null;
                return -1;
            }
            this.C = a2.c();
        }
    }
}
