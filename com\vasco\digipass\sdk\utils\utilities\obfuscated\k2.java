package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k2.smali */
public class k2 extends f0 {
    private int L;

    public k2() {
        this.L = -1;
    }

    private static boolean b(boolean z) {
        if (z) {
            return z;
        }
        throw new IllegalStateException("DERSet elements should always be in sorted order");
    }

    private int i() throws IOException {
        if (this.L < 0) {
            int length = this.b.length;
            int i = 0;
            for (int i2 = 0; i2 < length; i2++) {
                i += this.b[i2].toASN1Primitive().f().a(true);
            }
            this.L = i;
        }
        return this.L;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        return z.a(z, i());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.f0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this.x != null ? this : super.f();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.f0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.b(z, 49);
        h2 b = zVar.b();
        int length = this.b.length;
        int i = 0;
        if (this.L >= 0 || length > 16) {
            zVar.d(i());
            while (i < length) {
                this.b[i].toASN1Primitive().f().a(b, true);
                i++;
            }
            return;
        }
        b0[] b0VarArr = new b0[length];
        int i2 = 0;
        for (int i3 = 0; i3 < length; i3++) {
            b0 f = this.b[i3].toASN1Primitive().f();
            b0VarArr[i3] = f;
            i2 += f.a(true);
        }
        this.L = i2;
        zVar.d(i2);
        while (i < length) {
            b0VarArr[i].a(b, true);
            i++;
        }
    }

    k2(boolean z, h[] hVarArr) {
        super(b(z), hVarArr);
        this.L = -1;
    }
}
