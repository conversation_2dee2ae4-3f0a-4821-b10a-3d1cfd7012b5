package com.vasco.digipass.sdk.obfuscated;

import bc.org.bouncycastle.crypto.params.DESParameters;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.obfuscated.l;
import com.vasco.digipass.sdk.responses.ActivationResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a.smali */
public final class a implements DigipassSDKConstants {

    /* renamed from: com.vasco.digipass.sdk.obfuscated.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a$a.smali */
    static class C0010a {
        final int a;
        String b;
        String c;

        public C0010a(int i) {
            this(i, null, null);
        }

        public C0010a(int i, String str, String str2) {
            this.a = i;
            this.b = str;
            this.c = str2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a$b.smali */
    static class b {
        final int a;
        String b;

        public b(int i) {
            this(i, null);
        }

        public b(int i, String str) {
            this.a = i;
            this.b = str;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a$c.smali */
    static class c {
        String a;
        String b;

        public c(String str, String str2) {
            this.a = str;
            this.b = str2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a$d.smali */
    static class d {
        final int a;
        final byte[] b;

        public d(int i) {
            this(i, null);
        }

        public d(int i, byte[] bArr) {
            this.a = i;
            this.b = bArr;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\a$e.smali */
    static class e {
        final int a;
        final byte[] b;
        final String c;
        final String d;

        public e(int i) {
            this(i, null, null, null);
        }

        public e(int i, byte[] bArr, String str, String str2) {
            this.a = i;
            this.b = bArr;
            this.c = str;
            this.d = str2;
        }
    }

    public static ActivationResponse a(String str, String str2, String str3, String str4, CharSequence charSequence, byte[] bArr, int i, byte[] bArr2, boolean z, boolean z2, String str5, byte[] bArr3) {
        String upperCase = str2 == null ? null : str2.toUpperCase();
        try {
            l.a(bArr, i);
            d b2 = b(str, upperCase, str3, charSequence, bArr3);
            int i2 = b2.a;
            return i2 != 0 ? a(i2) : a(upperCase, str3, str4, charSequence, null, bArr, i, bArr2, z, z2, str5, b2.b, bArr3);
        } catch (h e2) {
            return a(e2.a());
        } catch (Exception e3) {
            return a(DigipassSDKReturnCodes.UNKNOWN_ERROR, e3);
        }
    }

    private static d b(String str, String str2, String str3, CharSequence charSequence, byte[] bArr) {
        int a = a(str, str2, str3, charSequence, bArr);
        return a != 0 ? new d(a) : new d(0, q.a(str));
    }

    private static e b(String str, CharSequence charSequence, String str2, byte[] bArr) {
        int a = a(str, charSequence, str2, bArr);
        if (a != 0) {
            return new e(a);
        }
        try {
            byte[] b2 = j.b(str);
            return new e(a, b2, b(str, b2), a(str, b2));
        } catch (h e2) {
            return new e(e2.a());
        }
    }

    private static String b(String str, byte[] bArr) {
        byte b2 = bArr[1];
        int i = b2 <= 7 ? 2 : 1;
        int i2 = b2 > 7 ? 0 : 1;
        int length = bArr.length * 2;
        int i3 = i * 7;
        int i4 = length + i3;
        if (str.length() >= i4) {
            String substring = str.substring(length, i4);
            StringBuilder sb = new StringBuilder();
            for (int i5 = 0; i5 < i3; i5 += i) {
                sb.append(substring.charAt(i5 + i2));
            }
            return sb.toString();
        }
        throw new h(DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH);
    }

    private static int a(String str, String str2, String str3, CharSequence charSequence, byte[] bArr) {
        if (str == null) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_NULL;
        }
        if (str.length() <= 4) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH;
        }
        if (((byte) (str.charAt(3) - '0')) <= 7) {
            if (str.length() != 112) {
                return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH;
            }
            if (bArr != null && bArr.length != 56) {
                return DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH;
            }
        } else {
            if (str.length() < 182 || str.length() > 1916) {
                return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH;
            }
            if (bArr != null && (bArr.length < 80 || bArr.length > 306)) {
                return DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH;
            }
        }
        if (!q.c(str)) {
            return DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT;
        }
        if (str2 == null) {
            return -4009;
        }
        if (str2.length() != 10 && str2.length() != 7) {
            return -4010;
        }
        if (str3 == null) {
            return DigipassSDKReturnCodes.ACTIVATION_CODE_NULL;
        }
        if (((charSequence == null || charSequence.toString().isEmpty()) ? 0 : charSequence.length()) > 512) {
            return DigipassSDKReturnCodes.SHARED_SECRET_TOO_LONG;
        }
        return 0;
    }

    private static ActivationResponse a(String str, String str2, String str3, CharSequence charSequence, String str4, byte[] bArr, int i, byte[] bArr2, boolean z, boolean z2, String str5, byte[] bArr3, byte[] bArr4) {
        com.vasco.digipass.sdk.obfuscated.e a = a(bArr3, bArr4, str);
        if (a.c.t) {
            a.f();
            return a(DigipassSDKReturnCodes.MULTI_DEVICE_ACTIVATION_ENABLED);
        }
        l.a a2 = a(a, bArr, i, bArr2, z, str5);
        if (a2.getReturnCode() != 0) {
            a.f();
            return a(a2.getReturnCode());
        }
        byte[] bArr5 = a2.c;
        boolean z3 = ((charSequence == null || charSequence.toString().isEmpty()) && q.d(str4)) ? false : true;
        int a3 = a(a, str, str2, str3, z3, z2);
        if (a3 != 0) {
            a.f();
            q.g(bArr5);
            return a(a3);
        }
        c a4 = a(a, str2, str3);
        C0010a a5 = a(a, a4, charSequence, str4, z2);
        if (a5.a != 0) {
            a.f();
            q.g(bArr5);
            a4.a = null;
            a4.b = null;
            return a(a5.a);
        }
        ActivationResponse a6 = a(a, bArr5, a5, z3, z2, !q.f(bArr));
        if (a6.getReturnCode() == 0) {
            a6.setStaticVector(bArr3);
        }
        a.f();
        q.g(bArr5);
        a4.a = null;
        a4.b = null;
        a5.b = null;
        a5.c = null;
        return a6;
    }

    static com.vasco.digipass.sdk.obfuscated.e a(byte[] bArr, byte[] bArr2, String str) {
        com.vasco.digipass.sdk.obfuscated.e b2;
        if (bArr2 == null) {
            b2 = p.b(bArr);
            g gVar = b2.d;
            gVar.a = (byte) 7;
            if (b2.c.a <= 7) {
                gVar.l = (byte) 0;
            } else {
                gVar.l = (byte) 7;
            }
            gVar.b = (byte) 7;
        } else {
            b2 = p.b(bArr, bArr2);
        }
        if (str.length() == 10) {
            b2.c.b = str;
        } else {
            b2.c.b = b2.c.b.substring(0, 3) + str;
        }
        return b2;
    }

    static l.a a(com.vasco.digipass.sdk.obfuscated.e eVar, byte[] bArr, int i, byte[] bArr2, boolean z, String str) {
        byte[] bArr3;
        if (z) {
            int a = j.a(bArr2);
            if (a != 0) {
                return new l.a(a, null);
            }
            bArr3 = new byte[16];
            System.arraycopy(bArr2, 0, bArr3, 0, 16);
            l.a(eVar, (byte[]) null, 0, (byte[]) null, 0, bArr2, (String) null);
        } else {
            l.a a2 = l.a(bArr, i, eVar, str);
            if (a2.getReturnCode() != 0) {
                return a2;
            }
            bArr3 = a2.c;
        }
        return new l.a(0, bArr3);
    }

    private static int a(com.vasco.digipass.sdk.obfuscated.e eVar, String str, String str2, String str3, boolean z, boolean z2) {
        boolean b2;
        boolean b3;
        i iVar = eVar.c;
        boolean z3 = true;
        if (str2.length() != ((iVar.q ? 16 : 20) * ((!z2 ? iVar.f18o : iVar.p) ? 1 : 2)) + (iVar.s ? 1 : 0)) {
            return DigipassSDKReturnCodes.ACTIVATION_CODE_INCORRECT_LENGTH;
        }
        if (eVar.c.q) {
            b2 = q.c(str2);
        } else {
            b2 = q.b(str2);
        }
        if (!b2) {
            return DigipassSDKReturnCodes.ACTIVATION_CODE_INCORRECT_FORMAT;
        }
        if (eVar.c.s) {
            if (str.length() == 10) {
                str = str.substring(3);
            }
            if (!a(str + str2, eVar.c.q)) {
                return DigipassSDKReturnCodes.ACTIVATION_CODE_INVALID;
            }
        }
        if (q.d(str3)) {
            return 0;
        }
        if (eVar.c.q) {
            b3 = q.c(str3);
        } else {
            b3 = q.b(str3);
        }
        if (!b3) {
            return z ? DigipassSDKReturnCodes.XERC_INCORRECT_FORMAT : DigipassSDKReturnCodes.ERC_INCORRECT_FORMAT;
        }
        i iVar2 = eVar.c;
        byte b4 = iVar2.a;
        if (b4 < 8 && z) {
            return 0;
        }
        boolean z4 = iVar2.q;
        if (b4 <= 7 && !z4) {
            str3 = com.vasco.digipass.sdk.obfuscated.c.a(str3);
        } else {
            z3 = z4;
        }
        if (a(str3, z3)) {
            return 0;
        }
        return DigipassSDKReturnCodes.ERC_INVALID;
    }

    private static boolean a(String str, boolean z) {
        char charAt = str.charAt(str.length() - 1);
        String substring = str.substring(0, str.length() - 1);
        return charAt == ((char) j.a(substring.getBytes(), substring.length(), z ^ true));
    }

    private static c a(com.vasco.digipass.sdk.obfuscated.e eVar, String str, String str2) {
        if (eVar.c.s) {
            str = str.substring(0, str.length() - 1);
        }
        if (!q.d(str2) && eVar.c.a >= 8) {
            str2 = str2.substring(0, str2.length() - 1);
        }
        if (!eVar.c.q) {
            str = com.vasco.digipass.sdk.obfuscated.c.a(str);
            if (!q.d(str2)) {
                str2 = com.vasco.digipass.sdk.obfuscated.c.a(str2);
            }
        }
        if (!q.d(str2) && eVar.c.a <= 7) {
            str2 = str2.substring(0, str2.length() - 1);
        }
        return new c(str, str2);
    }

    private static C0010a a(com.vasco.digipass.sdk.obfuscated.e eVar, c cVar, CharSequence charSequence, String str, boolean z) {
        String str2 = cVar.a;
        String str3 = cVar.b;
        if (((charSequence == null || charSequence.toString().isEmpty()) ? 0 : charSequence.length()) + (q.d(str) ? 0 : str.length()) != 0) {
            if (q.d(str)) {
                str = "";
            }
            StringBuilder append = new StringBuilder().append(str);
            if (charSequence == null || charSequence.toString().isEmpty()) {
                charSequence = "";
            }
            byte[] outputData = UtilitiesSDK.hash((byte) 2, append.append((Object) charSequence).toString().getBytes()).getOutputData();
            byte[] bArr = new byte[16];
            System.arraycopy(outputData, 0, bArr, 0, 16);
            q.g(outputData);
            i iVar = eVar.c;
            if (iVar.a <= 7) {
                byte[] outputData2 = UtilitiesSDK.decrypt((byte) 2, (byte) 1, bArr, null, iVar.c).getOutputData();
                System.arraycopy(outputData2, 0, eVar.c.c, 0, outputData2.length);
                q.g(outputData2);
                String a = a(str2, bArr, (byte) 1, 0, 16);
                if (eVar.c.f18o) {
                    a = a + a(str2, bArr, (byte) 1, 16, 16);
                }
                str2 = a;
            } else {
                str2 = a(str2, bArr, (byte) 2, 0, str2.length());
            }
            b a2 = a(eVar, str3, bArr, z);
            q.g(bArr);
            if (a2.a != 0) {
                q.g(bArr);
                return new C0010a(a2.a);
            }
            str3 = a2.b;
            q.g(bArr);
            a2.b = null;
        }
        return new C0010a(0, str2, str3);
    }

    private static String a(String str, byte[] bArr, byte b2, int i, int i2) {
        byte[] a = q.a(str.substring(i, i2 + i));
        byte[] outputData = UtilitiesSDK.decrypt((byte) 2, b2, bArr, null, a).getOutputData();
        String a2 = q.a(outputData);
        q.g(a);
        q.g(outputData);
        return a2;
    }

    private static b a(com.vasco.digipass.sdk.obfuscated.e eVar, String str, byte[] bArr, boolean z) {
        byte b2;
        String str2 = null;
        if (!q.d(str)) {
            if (z) {
                b2 = 1;
            } else {
                com.vasco.digipass.sdk.obfuscated.d[] a = eVar.a();
                b2 = 0;
                for (int i = 0; i < eVar.a; i++) {
                    com.vasco.digipass.sdk.obfuscated.d dVar = a[i];
                    if (dVar.a && !dVar.b && dVar.e.n) {
                        b2 = (byte) (b2 + 1);
                    }
                }
            }
            if (str.length() < b2) {
                return new b(DigipassSDKReturnCodes.XERC_INCORRECT_LENGTH);
            }
            String substring = str.substring(0, b2);
            String substring2 = str.substring(b2);
            boolean z2 = substring2.length() % 2 != 0;
            if (z2) {
                substring2 = substring2 + "0";
            }
            byte[] a2 = q.a(substring2);
            byte b3 = eVar.c.a;
            if (b3 <= 7 && a2.length != 32) {
                q.g(a2);
                return new b(DigipassSDKReturnCodes.XERC_INCORRECT_LENGTH);
            }
            byte[] outputData = UtilitiesSDK.decrypt((byte) 2, b3 <= 7 ? (byte) 2 : (byte) 3, bArr, null, a2).getOutputData();
            String a3 = q.a(outputData);
            if (z2) {
                a3 = a3.substring(0, a3.length() - 1);
            }
            StringBuilder sb = new StringBuilder(substring);
            int i2 = 0;
            int i3 = 0;
            while (i2 < b2) {
                int parseInt = Integer.parseInt("" + substring.charAt(i2), 16) + i3;
                if (parseInt > a3.length()) {
                    q.g(a2);
                    q.g(outputData);
                    if (eVar.c.a <= 7) {
                        return new b(0, str);
                    }
                    return new b(DigipassSDKReturnCodes.XERC_INCORRECT_LENGTH);
                }
                sb.append(a3.substring(i3, parseInt));
                i2++;
                i3 = parseInt;
            }
            str2 = sb.toString();
            q.g(a2);
            q.g(outputData);
        }
        return new b(0, str2);
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x0048, code lost:
    
        r7 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static com.vasco.digipass.sdk.responses.ActivationResponse a(com.vasco.digipass.sdk.obfuscated.e r10, byte[] r11, com.vasco.digipass.sdk.obfuscated.a.C0010a r12, boolean r13, boolean r14, boolean r15) {
        /*
            java.lang.String r0 = r12.b
            java.lang.String r12 = r12.c
            byte[] r0 = com.vasco.digipass.sdk.obfuscated.q.a(r0)
            r1 = 8
            byte[] r2 = new byte[r1]
            r3 = 0
            java.lang.System.arraycopy(r0, r3, r2, r3, r1)
            byte[] r4 = a(r10, r2, r3)
            com.vasco.digipass.sdk.responses.ActivationResponse r5 = a(r4, r10)
            if (r5 == 0) goto L24
            com.vasco.digipass.sdk.obfuscated.q.g(r0)
            com.vasco.digipass.sdk.obfuscated.q.g(r2)
            com.vasco.digipass.sdk.obfuscated.q.g(r4)
            return r5
        L24:
            r5 = 16
            byte[] r6 = new byte[r5]
            int r7 = r4.length
            java.lang.System.arraycopy(r4, r3, r6, r3, r7)
            boolean r7 = r10.c()
            if (r7 == 0) goto L7f
            boolean r7 = r10.b()
            if (r7 == 0) goto L7f
            if (r14 == 0) goto L42
            com.vasco.digipass.sdk.obfuscated.i r7 = r10.c
            boolean r7 = r7.p
            if (r7 == 0) goto L48
            goto L4a
        L42:
            com.vasco.digipass.sdk.obfuscated.i r7 = r10.c
            boolean r7 = r7.f18o
            if (r7 != 0) goto L4a
        L48:
            r7 = 1
            goto L4b
        L4a:
            r7 = r3
        L4b:
            byte[] r8 = new byte[r1]
            if (r7 != 0) goto L52
            java.lang.System.arraycopy(r0, r1, r8, r3, r1)
        L52:
            if (r7 == 0) goto L56
            r1 = r2
            goto L57
        L56:
            r1 = r8
        L57:
            byte[] r1 = a(r10, r1, r7)
            com.vasco.digipass.sdk.responses.ActivationResponse r7 = a(r1, r10)
            if (r7 == 0) goto L74
            com.vasco.digipass.sdk.obfuscated.q.g(r0)
            com.vasco.digipass.sdk.obfuscated.q.g(r2)
            com.vasco.digipass.sdk.obfuscated.q.g(r4)
            com.vasco.digipass.sdk.obfuscated.q.g(r6)
            com.vasco.digipass.sdk.obfuscated.q.g(r8)
            com.vasco.digipass.sdk.obfuscated.q.g(r1)
            return r7
        L74:
            int r7 = r4.length
            int r9 = r1.length
            java.lang.System.arraycopy(r1, r3, r6, r7, r9)
            com.vasco.digipass.sdk.obfuscated.q.g(r8)
            com.vasco.digipass.sdk.obfuscated.q.g(r1)
        L7f:
            int r12 = a(r12, r10, r13, r14)
            if (r12 == 0) goto L96
            com.vasco.digipass.sdk.obfuscated.q.g(r0)
            com.vasco.digipass.sdk.obfuscated.q.g(r2)
            com.vasco.digipass.sdk.obfuscated.q.g(r4)
            com.vasco.digipass.sdk.obfuscated.q.g(r6)
            com.vasco.digipass.sdk.responses.ActivationResponse r10 = a(r12)
            return r10
        L96:
            r12 = 3
            r13 = 2
            r1 = 0
            com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse r11 = com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK.encrypt(r12, r13, r11, r1, r6)
            byte[] r11 = r11.getOutputData()
            if (r14 == 0) goto La9
            com.vasco.digipass.sdk.obfuscated.g r12 = r10.d
            byte[] r12 = r12.j
            goto Lad
        La9:
            com.vasco.digipass.sdk.obfuscated.g r12 = r10.d
            byte[] r12 = r12.h
        Lad:
            java.lang.System.arraycopy(r11, r3, r12, r3, r5)
            com.vasco.digipass.sdk.obfuscated.g r11 = r10.d
            com.vasco.digipass.sdk.obfuscated.i r12 = r10.c
            byte r13 = r12.i
            r11.e = r13
            byte r12 = r12.j
            r11.f = r12
            r11.k = r15
            com.vasco.digipass.sdk.responses.ActivationResponse r10 = a(r3, r10)
            com.vasco.digipass.sdk.obfuscated.q.g(r0)
            com.vasco.digipass.sdk.obfuscated.q.g(r2)
            com.vasco.digipass.sdk.obfuscated.q.g(r4)
            com.vasco.digipass.sdk.obfuscated.q.g(r6)
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.obfuscated.a.a(com.vasco.digipass.sdk.obfuscated.e, byte[], com.vasco.digipass.sdk.obfuscated.a$a, boolean, boolean, boolean):com.vasco.digipass.sdk.responses.ActivationResponse");
    }

    private static byte[] a(com.vasco.digipass.sdk.obfuscated.e eVar, byte[] bArr, boolean z) {
        byte[] bytes = eVar.c.b.getBytes();
        byte b2 = bytes[7];
        byte b3 = bytes[8];
        byte[] bArr2 = {bytes[0], bytes[1], bytes[2], (byte) (bytes[3] + bytes[4]), (byte) (bytes[5] + bytes[6]), (byte) (b2 + b3), b3, bytes[9]};
        if (z) {
            byte[] outputData = UtilitiesSDK.encrypt((byte) 2, (byte) 2, eVar.c.c, bArr2, bArr).getOutputData();
            if (eVar.c.a > 7) {
                return outputData;
            }
            DESParameters.setOddParity(outputData);
            return outputData;
        }
        return UtilitiesSDK.decrypt((byte) 2, (byte) 2, eVar.c.c, bArr2, bArr).getOutputData();
    }

    private static ActivationResponse a(byte[] bArr, com.vasco.digipass.sdk.obfuscated.e eVar) {
        if (eVar.c.a < 8 && !a(bArr)) {
            if (eVar.d()) {
                return a(DigipassSDKReturnCodes.ACTIVATION_CODE_INVALID);
            }
            i iVar = eVar.c;
            if (iVar.j == 0) {
                return a(DigipassSDKReturnCodes.ACTIVATION_CODE_INVALID);
            }
            g gVar = eVar.d;
            byte b2 = gVar.f;
            if (b2 > 0) {
                byte b3 = (byte) (b2 - 1);
                gVar.f = b3;
                return a(DigipassSDKReturnCodes.ACTIVATION_CODE_INVALID, eVar, b3);
            }
            if (iVar.k == 0) {
                eVar.f();
                eVar.d.c = (byte) 2;
                return a(DigipassSDKReturnCodes.REACTIVATION_LOCK, eVar);
            }
            gVar.c = (byte) 3;
            return null;
        }
        eVar.d.c = (byte) 1;
        return null;
    }

    private static boolean a(byte[] bArr) {
        byte[] bArr2 = new byte[bArr.length];
        System.arraycopy(bArr, 0, bArr2, 0, bArr.length);
        DESParameters.setOddParity(bArr2);
        for (int i = 0; i < bArr.length; i++) {
            if (bArr[i] != bArr2[i]) {
                q.g(bArr2);
                return false;
            }
        }
        q.g(bArr2);
        return true;
    }

    private static int a(String str, com.vasco.digipass.sdk.obfuscated.e eVar, boolean z, boolean z2) {
        if (!q.d(str)) {
            byte[] bArr = new byte[8];
            com.vasco.digipass.sdk.obfuscated.d[] a = eVar.a();
            byte b2 = 0;
            for (int i = 0; i < eVar.a; i++) {
                com.vasco.digipass.sdk.obfuscated.d dVar = a[i];
                if (dVar.a && dVar.e.n && ((z2 && dVar.b) || (!z2 && !dVar.b))) {
                    bArr[b2] = dVar.c;
                    b2 = (byte) (b2 + 1);
                }
            }
            if (str.length() < b2) {
                return DigipassSDKReturnCodes.ERC_INCORRECT_LENGTH;
            }
            String substring = str.substring(0, b2);
            String substring2 = str.substring(b2);
            int i2 = 0;
            int i3 = 0;
            while (i2 < b2) {
                int parseInt = Integer.parseInt("" + substring.charAt(i2), 16) + i3;
                if (parseInt > substring2.length()) {
                    if (eVar.c.a > 7 || !z) {
                        return DigipassSDKReturnCodes.ERC_INCORRECT_LENGTH;
                    }
                    return 0;
                }
                String substring3 = substring2.substring(i3, parseInt);
                byte b3 = bArr[i2];
                long parseLong = Long.parseLong(substring3, 16);
                if (parseLong > eVar.a(b3).n) {
                    eVar.a(b3).n = parseLong;
                }
                i2++;
                i3 = parseInt;
            }
        }
        return 0;
    }

    public static ActivationResponse a(String str, String str2, CharSequence charSequence, String str3, byte[] bArr, int i, byte[] bArr2, boolean z, boolean z2, String str4, byte[] bArr3) {
        try {
            l.a(bArr, i);
            e b2 = b(str, charSequence, str3, bArr3);
            int i2 = b2.a;
            if (i2 != 0) {
                return a(i2);
            }
            return a(b2.c, b2.d, str2, charSequence, str3, bArr, i, bArr2, z, z2, str4, b2.b, bArr3);
        } catch (h e2) {
            return a(e2.a());
        } catch (Exception e3) {
            return a(DigipassSDKReturnCodes.UNKNOWN_ERROR, e3);
        }
    }

    private static int a(String str, CharSequence charSequence, String str2, byte[] bArr) {
        int a = j.a(str);
        if (a != 0) {
            return a;
        }
        if (((byte) (str.charAt(3) - '0')) <= 7) {
            if (bArr != null && bArr.length != 56) {
                return DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH;
            }
        } else if (bArr != null && (bArr.length < 80 || bArr.length > 306)) {
            return DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH;
        }
        if (((charSequence == null || charSequence.toString().isEmpty()) ? 0 : charSequence.length()) + (q.d(str2) ? 0 : str2.length()) > 512) {
            return DigipassSDKReturnCodes.SHARED_SECRET_TOO_LONG;
        }
        return 0;
    }

    private static String a(String str, byte[] bArr) {
        int length = (bArr.length * 2) + ((bArr[1] <= 7 ? 2 : 1) * 7);
        if (str.length() >= length) {
            return str.substring(length);
        }
        throw new h(DigipassSDKReturnCodes.XFAD_INCORRECT_LENGTH);
    }

    private static ActivationResponse a(int i) {
        return new ActivationResponse(i);
    }

    private static ActivationResponse a(int i, Throwable th) {
        return new ActivationResponse(i, th);
    }

    private static ActivationResponse a(int i, com.vasco.digipass.sdk.obfuscated.e eVar) {
        return a(i, eVar, 0);
    }

    private static ActivationResponse a(int i, com.vasco.digipass.sdk.obfuscated.e eVar, int i2) {
        return new ActivationResponse(i, eVar.d.c, p.a(eVar), i2);
    }
}
