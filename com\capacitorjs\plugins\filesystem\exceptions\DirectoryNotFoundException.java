package com.capacitorjs.plugins.filesystem.exceptions;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\filesystem\exceptions\DirectoryNotFoundException.smali */
public class DirectoryNotFoundException extends Exception {
    public DirectoryNotFoundException(String s) {
        super(s);
    }

    public DirectoryNotFoundException(Throwable t) {
        super(t);
    }

    public DirectoryNotFoundException(String s, Throwable t) {
        super(s, t);
    }
}
