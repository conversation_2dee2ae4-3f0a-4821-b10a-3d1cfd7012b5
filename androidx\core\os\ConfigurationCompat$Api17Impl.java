package androidx.core.os;

import android.content.res.Configuration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\os\ConfigurationCompat$Api17Impl.smali */
class ConfigurationCompat$Api17Impl {
    private ConfigurationCompat$Api17Impl() {
    }

    static void setLocale(Configuration configuration, LocaleListCompat locales) {
        if (!locales.isEmpty()) {
            configuration.setLocale(locales.get(0));
        }
    }
}
