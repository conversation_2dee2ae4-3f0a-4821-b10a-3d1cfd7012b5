package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\m2.smali */
public class m2 extends j0 {
    public m2(boolean z, int i, h hVar) {
        super(z, i, hVar);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        b0 f = this.L.toASN1Primitive().f();
        boolean k = k();
        int a = f.a(k);
        if (k) {
            a += z.a(a);
        }
        return a + (z ? z.b(this.C) : 0);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.j0
    e0 c(b0 b0Var) {
        return new j2(b0Var);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return k() || this.L.toASN1Primitive().f().e();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.j0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.j0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return this;
    }

    m2(int i, int i2, int i3, h hVar) {
        super(i, i2, i3, hVar);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        b0 f = this.L.toASN1Primitive().f();
        boolean k = k();
        if (z) {
            int i = this.x;
            if (k || f.e()) {
                i |= 32;
            }
            zVar.a(true, i, this.C);
        }
        if (k) {
            zVar.d(f.a(true));
        }
        f.a(zVar.b(), k);
    }
}
