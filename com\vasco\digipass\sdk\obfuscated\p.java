package com.vasco.digipass.sdk.obfuscated;

import android.support.v4.media.session.PlaybackStateCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import java.io.ByteArrayOutputStream;
import java.util.Vector;
import kotlin.io.encoding.Base64;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\p.smali */
public final class p implements DigipassSDKConstants {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\p$b.smali */
    private static class b {
        public byte a;
        public int b;
        public byte[] c;

        private b() {
        }
    }

    public static e a(String str) {
        int a2 = j.a(str);
        if (a2 == 0) {
            return b(j.b(str));
        }
        throw new h(a2);
    }

    public static e b(byte[] bArr, byte[] bArr2) {
        a(bArr, bArr2);
        return c(bArr, bArr2);
    }

    private static e c(byte[] bArr, byte[] bArr2) {
        e eVar = new e();
        if (bArr[1] <= 7) {
            a(bArr, bArr2, eVar);
        } else {
            b(bArr, bArr2, eVar);
        }
        return eVar;
    }

    public static e b(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        a(bArr, bArr2, bArr3);
        return c(bArr, bArr2);
    }

    public static e b(byte[] bArr) {
        a(bArr);
        if (bArr[1] <= 7) {
            return c(bArr, new byte[56]);
        }
        e eVar = new e();
        b(bArr, eVar);
        return eVar;
    }

    public static com.vasco.digipass.sdk.obfuscated.b c(byte[] bArr) {
        com.vasco.digipass.sdk.obfuscated.b bVar = new com.vasco.digipass.sdk.obfuscated.b();
        System.arraycopy(bArr, 0, bVar.a, 0, 4);
        long b2 = c.b(bArr);
        bVar.i = q.a(b2, -2147483648L);
        bVar.f15o = q.a(b2, 1073741824L);
        bVar.u = q.a(b2, 536870912L);
        bVar.A = q.a(b2, 268435456L);
        bVar.m = q.a(b2, 134217728L);
        bVar.q = q.a(b2, 67108864L);
        bVar.v = q.a(b2, 33554432L);
        bVar.p = q.a(b2, 8388608L);
        bVar.s = q.a(b2, 256L);
        bVar.w = q.a(b2, 16777216L);
        bVar.x = q.a(b2, 4194304L);
        bVar.y = (3145728 & b2) >> 20;
        bVar.b = (786432 & b2) >> 18;
        bVar.f = q.a(b2, PlaybackStateCompat.ACTION_PREPARE_FROM_URI);
        bVar.g = q.a(b2, PlaybackStateCompat.ACTION_PREPARE_FROM_SEARCH);
        bVar.d = (983040 & b2) >> 16;
        bVar.l = q.a(b2, PlaybackStateCompat.ACTION_PREPARE_FROM_MEDIA_ID);
        bVar.t = q.a(b2, PlaybackStateCompat.ACTION_PREPARE_FROM_MEDIA_ID);
        bVar.k = q.a(b2, PlaybackStateCompat.ACTION_PREPARE);
        bVar.n = q.a(b2, PlaybackStateCompat.ACTION_PLAY_FROM_URI);
        bVar.j = q.a(b2, PlaybackStateCompat.ACTION_SKIP_TO_QUEUE_ITEM);
        bVar.h = q.a(b2, PlaybackStateCompat.ACTION_PLAY_FROM_SEARCH);
        bVar.z = q.a(b2, PlaybackStateCompat.ACTION_PLAY_FROM_MEDIA_ID);
        boolean a2 = q.a(b2, 512L);
        bVar.r = a2;
        bVar.c = (240 & b2) >> 4;
        bVar.e = b2 & 15;
        bVar.B = bVar.x && bVar.q && !a2;
        bVar.C = bVar.w && bVar.z;
        if (bVar.q && a2) {
            bVar.q = false;
            bVar.r = false;
            bVar.D = true;
        }
        if (bVar.v && bVar.p) {
            bVar.v = false;
            bVar.p = false;
            bVar.E = true;
            bVar.F = !bVar.s;
        }
        return bVar;
    }

    public static void a(byte[] bArr) {
        if (bArr != null) {
            if (bArr.length > 1) {
                if (bArr[1] <= 7) {
                    if (bArr.length == 56) {
                        int b2 = j.b(bArr);
                        if (b2 != 0) {
                            throw new h(b2);
                        }
                        return;
                    }
                    throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH);
                }
                if (bArr.length < 91 || bArr.length > 958) {
                    throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH);
                }
                return;
            }
            throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH);
        }
        throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_NULL);
    }

    private static void b(byte[] bArr, byte[] bArr2, e eVar) {
        b(bArr, eVar);
        a(bArr2, eVar);
    }

    private static void b(byte[] bArr, e eVar) {
        byte[] bArr2 = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, Tnaf.POW_2_WIDTH, 17};
        byte[] bArr3 = {13};
        byte[] bArr4 = {12};
        byte[] bArr5 = {Base64.padSymbol};
        i iVar = eVar.c;
        byte b2 = bArr[1];
        iVar.a = b2;
        if (b2 <= 8) {
            int i = 2;
            if (((bArr[2] & 255) << 8) + (bArr[3] & 255) == bArr.length - 4) {
                Vector a2 = a(bArr, 4, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                i iVar2 = eVar.c;
                iVar2.m = (byte) 0;
                iVar2.u = (byte) 32;
                iVar2.v = (byte) 0;
                iVar2.w = false;
                int i2 = 0;
                boolean z = false;
                while (i2 < a2.size()) {
                    b bVar = (b) a2.elementAt(i2);
                    byte b3 = bVar.a;
                    if (b3 == 56) {
                        eVar.c.m = a(bVar, 0, 255, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    } else if (b3 == 70) {
                        eVar.c.v = a(bVar, 3, 10, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    } else if (b3 != 71) {
                        switch (b3) {
                            case 1:
                                eVar.c.b = c(bVar, 3, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 2:
                                eVar.c.c = a(bVar, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 3:
                                eVar.c.d = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 4:
                                eVar.c.e = c(bVar, 1, 255, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 5:
                                eVar.c.f = c(bVar, 1, 255, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 6:
                                eVar.c.n = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 7:
                                eVar.c.g = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 8:
                                eVar.c.i = a(bVar, 1, 255, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 9:
                                eVar.c.l = c(bVar, 0, 255, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 10:
                                eVar.c.k = a(bVar, 0, 1, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 11:
                                eVar.c.h = a(bVar, 0, i, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 12:
                                eVar.c.f18o = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 13:
                                eVar.c.p = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 14:
                                eVar.c.q = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 15:
                                eVar.c.r = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 16:
                                eVar.c.s = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 17:
                                z = b(bVar.c, eVar, false);
                                break;
                            default:
                                switch (b3) {
                                    case 60:
                                        eVar.c.t = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                    case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                                        b(bVar.c, eVar, true);
                                        break;
                                    case 62:
                                        eVar.c.u = a(bVar, 8, 32, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                }
                        }
                    } else {
                        eVar.c.w = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    }
                    i2++;
                    i = 2;
                }
                int i3 = 16;
                i iVar3 = eVar.c;
                iVar3.j = (byte) 0;
                byte[] bArr6 = new byte[15 + (z ? 1 : 0) + ((!z || eVar.a > 1) ? 1 : 0) + (iVar3.t ? 1 : 0)];
                System.arraycopy(bArr2, 0, bArr6, 0, 15);
                if (!z) {
                    i3 = 15;
                } else {
                    System.arraycopy(bArr3, 0, bArr6, 15, 1);
                }
                if (!z || eVar.a > 1) {
                    System.arraycopy(bArr4, 0, bArr6, i3, 1);
                    i3++;
                }
                if (eVar.c.t) {
                    System.arraycopy(bArr5, 0, bArr6, i3, 1);
                }
                a(a2, bArr6, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                return;
            }
            throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_LENGTH);
        }
        throw new h(DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
    }

    private static void a(byte[] bArr, byte[] bArr2) {
        a(bArr);
        if (bArr2 != null) {
            if (bArr[1] <= 7) {
                if (bArr2.length != 56) {
                    throw new h(DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH);
                }
                return;
            } else {
                if (bArr2.length < 80 || bArr2.length > 306) {
                    throw new h(DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_LENGTH);
                }
                return;
            }
        }
        throw new h(DigipassSDKReturnCodes.DYNAMIC_VECTOR_NULL);
    }

    private static String c(b bVar, int i, int i2) {
        return new String(a(bVar, i, i2));
    }

    private static int c(b bVar, int i, int i2, int i3) {
        int b2 = b(bVar, i3) & 255;
        if (b2 < i || b2 > i2) {
            throw new h(i3);
        }
        return b2;
    }

    private static byte[] c(e eVar) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        a(byteArrayOutputStream, (byte) 45, 7);
        a(byteArrayOutputStream, (byte) 46, eVar.d.c);
        a(byteArrayOutputStream, (byte) 73, eVar.c.b.getBytes());
        a(byteArrayOutputStream, (byte) 48, eVar.d.d);
        a(byteArrayOutputStream, (byte) 49, eVar.d.e);
        a(byteArrayOutputStream, (byte) 50, eVar.d.h);
        a(byteArrayOutputStream, (byte) 51, eVar.d.j);
        a(byteArrayOutputStream, (byte) 55, eVar.d.k ? 1 : 0);
        a(byteArrayOutputStream, (byte) 57, eVar.d.l);
        a(byteArrayOutputStream, (byte) 58, eVar.d.m ? 1 : 0);
        a(byteArrayOutputStream, (byte) 59, eVar.d.n ? 1 : 0);
        for (int i = 0; i < eVar.a; i++) {
            a(byteArrayOutputStream, (byte) 17, eVar.a()[i]);
        }
        if (!eVar.c.t) {
            g gVar = eVar.d;
            gVar.f17o = (byte) 1;
            gVar.q = new byte[16];
            gVar.r = (byte) 0;
            gVar.t = new byte[16];
            gVar.u = (byte) 0;
        }
        a(byteArrayOutputStream, (byte) 63, eVar.d.f17o);
        a(byteArrayOutputStream, (byte) 64, eVar.d.q);
        a(byteArrayOutputStream, (byte) 65, eVar.d.r);
        a(byteArrayOutputStream, (byte) 66, eVar.d.t);
        a(byteArrayOutputStream, (byte) 67, eVar.d.u);
        a(byteArrayOutputStream, (byte) 69, eVar.d.v ? 1 : 0);
        a(byteArrayOutputStream, Base64.padSymbol, eVar.e);
        a(byteArrayOutputStream, (byte) 74, eVar.d.b);
        return byteArrayOutputStream.toByteArray();
    }

    private static void a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        a(bArr, bArr2);
        int a2 = j.a(bArr3);
        if (a2 != 0) {
            throw new h(a2);
        }
    }

    private static void a(byte[] bArr, byte[] bArr2, e eVar) {
        eVar.c.a = bArr[1];
        eVar.c.b = new String(bArr, 2, 3) + new String(bArr2, 2, 7);
        System.arraycopy(bArr, 5, eVar.c.c, 0, 16);
        i iVar = eVar.c;
        byte b2 = bArr[23];
        iVar.d = b2 != 15;
        iVar.e = bArr[24];
        iVar.f = 255;
        iVar.g = bArr[25] == 2;
        iVar.h = b2;
        iVar.i = bArr[26];
        iVar.j = (byte) (bArr[50] & 15);
        iVar.k = bArr[30];
        iVar.l = (byte) (15 & bArr[29]);
        iVar.m = (byte) 0;
        iVar.n = false;
        iVar.f18o = q.a(bArr[22], 32L);
        i iVar2 = eVar.c;
        iVar2.p = false;
        iVar2.q = !q.a(bArr[22], 2L);
        i iVar3 = eVar.c;
        iVar3.r = false;
        iVar3.s = false;
        iVar3.t = false;
        iVar3.u = (byte) 32;
        g gVar = eVar.d;
        byte b3 = bArr2[0];
        gVar.a = b3;
        gVar.b = b3;
        gVar.c = bArr2[1];
        System.arraycopy(bArr2, 50, gVar.d, 0, 4);
        g gVar2 = eVar.d;
        gVar2.e = bArr2[54];
        gVar2.f = bArr2[9];
        gVar2.g = new byte[16];
        System.arraycopy(bArr2, 10, gVar2.h, 0, 16);
        g gVar3 = eVar.d;
        gVar3.i = new byte[16];
        gVar3.j = new byte[16];
        gVar3.k = eVar.c.d;
        gVar3.l = (byte) 0;
        gVar3.m = false;
        gVar3.n = false;
        gVar3.f17o = (byte) 0;
        gVar3.p = new byte[16];
        gVar3.q = new byte[16];
        gVar3.r = (byte) 0;
        gVar3.s = new byte[16];
        gVar3.t = new byte[16];
        gVar3.u = (byte) 0;
        gVar3.v = false;
        eVar.a = 2;
        eVar.a()[0] = a(bArr, bArr2, 1, eVar);
        eVar.a()[1] = a(bArr, bArr2, 2, eVar);
    }

    private static d a(byte[] bArr, byte[] bArr2, int i, e eVar) {
        d dVar = new d();
        int i2 = i == 1 ? 0 : 9;
        int i3 = i == 1 ? 0 : 8;
        byte b2 = bArr[i2 + 38];
        if (i != 1 && eVar.c.a >= 7) {
            dVar.a = q.a(b2, 1L);
        } else {
            dVar.a = b2 != 0;
        }
        dVar.b = false;
        dVar.c = (byte) i;
        dVar.d = "";
        byte[] bArr3 = new byte[4];
        System.arraycopy(bArr, i2 + 31, bArr3, 0, 4);
        com.vasco.digipass.sdk.obfuscated.b c = c(bArr3);
        dVar.e = c;
        if (i == 1) {
            dVar.g = (byte) ((c.f || c.g) ? 1 : 0);
        } else {
            dVar.g = bArr[48];
        }
        dVar.h = new byte[8];
        dVar.i = new byte[8];
        for (int i4 = 0; i4 < 8; i4++) {
            dVar.h[i4] = 0;
            dVar.i[i4] = Tnaf.POW_2_WIDTH;
        }
        dVar.f = bArr[i2 + 35] != 0;
        byte b3 = bArr[i2 + 36];
        byte b4 = (byte) (b3 & 15);
        dVar.j = b4;
        if (b4 == 0) {
            dVar.j = 16;
        }
        dVar.k = (b3 & 240) >> 4;
        dVar.l = bArr[i2 + 37] != 0;
        dVar.m = bArr[i == 1 ? '\'' : '1'];
        byte[] bArr4 = new byte[4];
        System.arraycopy(bArr2, i3 + 34, bArr4, 0, 4);
        dVar.n = c.b(bArr4);
        dVar.f16o = 0L;
        dVar.p = j.c(dVar);
        return dVar;
    }

    private static void a(byte[] bArr, e eVar) {
        byte[] bArr2 = {45, 46, 48, 49, 50, 51, 17};
        eVar.d.n = true;
        Vector a2 = a(bArr, 0, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
        StringBuilder sb = new StringBuilder(eVar.c.b);
        for (int i = 0; i < a2.size(); i++) {
            b bVar = (b) a2.elementAt(i);
            byte b2 = bVar.a;
            if (b2 == 17) {
                a(bVar.c, eVar, false);
            } else if (b2 == 55) {
                eVar.d.k = a(bVar, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
            } else if (b2 == 61) {
                a(bVar.c, eVar, true);
            } else if (b2 == 69) {
                eVar.d.v = a(bVar, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
            } else if (b2 == 73) {
                eVar.c.b = c(bVar, 10, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
            } else if (b2 != 74) {
                switch (b2) {
                    case 45:
                        eVar.d.a = a(bVar, 1, 7, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case 46:
                        eVar.d.c = a(bVar, 0, 4, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case 47:
                        sb.append(c(bVar, 7, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT));
                        eVar.c.b = sb.toString();
                        break;
                    case 48:
                        eVar.d.d = a(bVar, 4, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case 49:
                        eVar.d.e = a(bVar, 0, 255, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case 50:
                        eVar.d.h = a(bVar, 16, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case 51:
                        eVar.d.j = a(bVar, 16, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    default:
                        switch (b2) {
                            case 57:
                                eVar.d.l = a(bVar, 1, 7, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case Opcodes.ASTORE /* 58 */:
                                eVar.d.m = a(bVar, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            case 59:
                                eVar.d.n = a(bVar, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                break;
                            default:
                                switch (b2) {
                                    case 63:
                                        eVar.d.f17o = a(bVar, 1, 31, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                    case 64:
                                        eVar.d.q = a(bVar, 16, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                        eVar.d.r = a(bVar, 0, 99, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                    case 66:
                                        eVar.d.t = b(bVar, 16, 16, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                    case 67:
                                        eVar.d.u = a(bVar, 0, 0, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                                        break;
                                }
                        }
                }
            } else {
                eVar.d.b = a(bVar, 1, 7, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
            }
        }
        g gVar = eVar.d;
        gVar.f = (byte) 0;
        gVar.g = new byte[16];
        gVar.i = new byte[16];
        gVar.p = new byte[16];
        gVar.s = new byte[16];
        a(a2, bArr2, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
        g gVar2 = eVar.d;
        byte b3 = gVar2.a;
        if (b3 < 2) {
            gVar2.k = eVar.c.d;
        }
        if (b3 < 3) {
            gVar2.l = (byte) 2;
            gVar2.m = true;
            gVar2.n = false;
        }
        if (b3 < 7) {
            gVar2.b = b3;
        }
    }

    private static boolean b(byte[] bArr, e eVar, boolean z) {
        char c;
        byte[] bArr2 = {19, 18, 20, 21, 24, 23, 22, 41, 42, 43, 44};
        d dVar = new d();
        if (z) {
            eVar.e = dVar;
        } else {
            d[] a2 = eVar.a();
            int i = eVar.a;
            eVar.a = i + 1;
            a2[i] = dVar;
        }
        for (int i2 = 0; i2 < 8; i2++) {
            dVar.h[i2] = 0;
            dVar.i[i2] = Tnaf.POW_2_WIDTH;
        }
        Vector a3 = a(bArr, 0, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
        boolean z2 = false;
        for (int i3 = 0; i3 < a3.size(); i3++) {
            b bVar = (b) a3.elementAt(i3);
            switch (bVar.a) {
                case 18:
                    c = '\b';
                    boolean a4 = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    dVar.b = a4;
                    if (a4) {
                        z2 = true;
                        break;
                    } else {
                        break;
                    }
                case 19:
                    c = '\b';
                    dVar.a = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    break;
                case 20:
                    if (z) {
                        dVar.c = (byte) 100;
                        c = '\b';
                        break;
                    } else {
                        c = '\b';
                        dVar.c = a(bVar, 1, 8, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    }
                case 21:
                    dVar.d = c(bVar, 12, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 22:
                    dVar.f = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 23:
                    dVar.e = c(a(bVar, 4, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT));
                    c = '\b';
                    break;
                case 24:
                    dVar.g = a(bVar, 0, 8, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 25:
                    dVar.h[0] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 26:
                    dVar.h[1] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 27:
                    dVar.h[2] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 28:
                    dVar.h[3] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 29:
                    dVar.h[4] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 30:
                    dVar.h[5] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 31:
                    dVar.h[6] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 32:
                    dVar.h[7] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 33:
                    dVar.i[0] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 34:
                    dVar.i[1] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 35:
                    dVar.i[2] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 36:
                    dVar.i[3] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 37:
                    dVar.i[4] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 38:
                    dVar.i[5] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 39:
                    dVar.i[6] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 40:
                    dVar.i[7] = a(bVar, 0, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 41:
                    dVar.j = c(bVar, 3, 16, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 42:
                    dVar.k = c(bVar, 0, 10, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 43:
                    dVar.l = a(bVar, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                case 44:
                    dVar.m = a(bVar, 0, 2, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
                    c = '\b';
                    break;
                default:
                    c = '\b';
                    break;
            }
        }
        a(a3, bArr2, DigipassSDKReturnCodes.STATIC_VECTOR_INCORRECT_FORMAT);
        dVar.p = j.c(dVar);
        return z2;
    }

    private static void a(byte[] bArr, e eVar, boolean z) {
        byte[] bArr2 = {20, 52, 54, 53};
        Vector a2 = a(bArr, 0, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
        long j = 0;
        long j2 = 0;
        byte b2 = 0;
        boolean z2 = false;
        for (int i = 0; i < a2.size(); i++) {
            b bVar = (b) a2.elementAt(i);
            byte b3 = bVar.a;
            if (b3 != 20) {
                switch (b3) {
                    case 52:
                        z2 = a(bVar, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case Opcodes.SALOAD /* 53 */:
                        j2 = b(bVar, 4, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                    case Opcodes.ISTORE /* 54 */:
                        j = b(bVar, 4, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
                        break;
                }
            } else {
                b2 = z ? (byte) 100 : a(bVar, 1, 8, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
            }
        }
        a(a2, bArr2, DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
        d a3 = eVar.a(b2);
        if (a3 != null) {
            a3.a = z2;
            a3.n = j;
            a3.f16o = j2;
            return;
        }
        throw new h(DigipassSDKReturnCodes.DYNAMIC_VECTOR_INCORRECT_FORMAT);
    }

    private static Vector a(byte[] bArr, int i, int i2) {
        Vector vector = new Vector();
        if (!q.f(bArr)) {
            while (i < bArr.length) {
                b bVar = new b();
                int i3 = i + 1;
                bVar.a = bArr[i];
                if (i3 < bArr.length) {
                    int i4 = i3 + 1;
                    int i5 = bArr[i3] & 255;
                    bVar.b = i5;
                    if (i4 < bArr.length) {
                        if (i4 + i5 <= bArr.length) {
                            byte[] bArr2 = new byte[i5];
                            bVar.c = bArr2;
                            System.arraycopy(bArr, i4, bArr2, 0, i5);
                            i = i4 + bVar.b;
                            vector.addElement(bVar);
                        } else {
                            throw new h(i2);
                        }
                    } else {
                        throw new h(i2);
                    }
                } else {
                    throw new h(i2);
                }
            }
        }
        return vector;
    }

    private static byte[] a(b bVar, int i, int i2) {
        if (bVar.b == i) {
            return bVar.c;
        }
        throw new h(i2);
    }

    private static boolean a(b bVar, int i) {
        return a(bVar, 0, 1, i) != 0;
    }

    private static byte a(b bVar, int i, int i2, int i3) {
        int b2 = b(bVar, i3) & 255;
        if (b2 < i || b2 > i2) {
            throw new h(i3);
        }
        return (byte) b2;
    }

    private static void a(Vector vector, byte[] bArr, int i) {
        boolean z;
        for (byte b2 : bArr) {
            int i2 = 0;
            while (true) {
                if (i2 >= vector.size()) {
                    z = false;
                    break;
                } else {
                    if (b2 == ((b) vector.elementAt(i2)).a) {
                        z = true;
                        break;
                    }
                    i2++;
                }
            }
            if (!z) {
                throw new h(i);
            }
        }
    }

    public static byte[] a(e eVar) {
        if (eVar.c.a <= 7) {
            return b(eVar);
        }
        return c(eVar);
    }

    private static void a(byte[] bArr, int i, int i2, e eVar) {
        System.arraycopy(c.a(eVar.a(i).n), 0, bArr, i2 + 34, 4);
    }

    private static void a(ByteArrayOutputStream byteArrayOutputStream, byte b2, int i) {
        byteArrayOutputStream.write(b2);
        byteArrayOutputStream.write(1);
        byteArrayOutputStream.write(i);
    }

    private static void a(ByteArrayOutputStream byteArrayOutputStream, byte b2, byte[] bArr) {
        a(byteArrayOutputStream, b2, bArr, 0, bArr.length);
    }

    private static void a(ByteArrayOutputStream byteArrayOutputStream, byte b2, byte[] bArr, int i, int i2) {
        byteArrayOutputStream.write(b2);
        byteArrayOutputStream.write(i2);
        byteArrayOutputStream.write(bArr, i, i2);
    }

    private static void a(ByteArrayOutputStream byteArrayOutputStream, byte b2, d dVar) {
        ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
        a(byteArrayOutputStream2, (byte) 20, dVar.c);
        a(byteArrayOutputStream2, (byte) 52, dVar.a ? 1 : 0);
        a(byteArrayOutputStream2, (byte) 54, c.a(dVar.n));
        a(byteArrayOutputStream2, (byte) 53, c.a(dVar.f16o));
        byteArrayOutputStream.write(b2);
        byteArrayOutputStream.write(byteArrayOutputStream2.size());
        byteArrayOutputStream.write(byteArrayOutputStream2.toByteArray(), 0, byteArrayOutputStream2.size());
    }

    private static byte[] b(b bVar, int i, int i2, int i3) {
        int i4 = bVar.b;
        if (i4 >= i && i4 <= i2) {
            return bVar.c;
        }
        throw new h(i3);
    }

    private static byte b(b bVar, int i) {
        if (bVar.b == 1) {
            return bVar.c[0];
        }
        throw new h(i);
    }

    private static long b(b bVar, int i, int i2) {
        return c.b(a(bVar, i, i2));
    }

    private static byte[] b(e eVar) {
        byte[] bArr = new byte[56];
        g gVar = eVar.d;
        bArr[0] = gVar.a;
        bArr[1] = gVar.c;
        System.arraycopy(eVar.c.b.getBytes(), 3, bArr, 2, 7);
        g gVar2 = eVar.d;
        bArr[9] = gVar2.f;
        System.arraycopy(gVar2.h, 0, bArr, 10, 16);
        a(bArr, 1, 0, eVar);
        a(bArr, 2, 8, eVar);
        System.arraycopy(eVar.d.d, 0, bArr, 50, 4);
        bArr[54] = eVar.d.e;
        return bArr;
    }
}
