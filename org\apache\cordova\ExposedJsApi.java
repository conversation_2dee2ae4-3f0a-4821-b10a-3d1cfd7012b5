package org.apache.cordova;

import org.json.JSONException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\ExposedJsApi.smali */
public interface ExposedJsApi {
    String exec(int bridgeSecret, String service, String action, String callbackId, String arguments) throws JSONEx<PERSON>, IllegalAccessException;

    String retrieveJsMessages(int bridgeSecret, boolean fromOnlineEvent) throws IllegalAccessException;

    void setNativeToJsBridgeMode(int bridgeSecret, int value) throws IllegalAccessException;
}
