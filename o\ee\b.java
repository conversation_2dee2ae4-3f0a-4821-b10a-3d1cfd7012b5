package o.ee;

import android.media.AudioTrack;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\b.smali */
public final class b extends Handler {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        e = 1;
        a = new char[]{50820, 50763, 50762, 50783, 50773, 50778, 50776, 50771, 50774, 50782, 50850, 50846, 50846, 50875, 50773, 50776, 50776, 50779, 50777, 50764, 50758, 50797, 50796, 50797, 50797, 50771, 50763, 50767, 50782, 50780, 50778, 50797, 50943, 50854, 50879, 50877, 50877, 50820, 50822, 50850, 50853, 50855, 50853, 50831, 50831, 50859, 50851, 50876, 50853, 50855, 50854, 50856, 50853, 50854, 50855, 50830, 50827, 50852, 50849, 50878, 50855, 50857, 50859, 50831, 50828, 50848, 50817, 50823, 50851, 50859, 50831, 50827, 50848, 50850, 50850, 50855, 50854};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r7 = 122 - r7
            byte[] r0 = o.ee.b.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.b.f(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{1, 25, 123, 58};
        $$b = 74;
    }

    public b(Looper looper) {
        super(looper);
    }

    public b() {
    }

    @Override // android.os.Handler
    public final void dispatchMessage(Message message) {
        int i = d + 33;
        e = i % 128;
        Object obj = null;
        try {
            switch (i % 2 != 0) {
                case false:
                    super.dispatchMessage(message);
                    throw null;
                default:
                    super.dispatchMessage(message);
                    int i2 = e + Opcodes.LSUB;
                    d = i2 % 128;
                    switch (i2 % 2 != 0 ? (char) 24 : Typography.amp) {
                        case 24:
                            obj.hashCode();
                            throw null;
                        default:
                            return;
                    }
            }
        } catch (RuntimeException e2) {
            g.c();
            Object[] objArr = new Object[1];
            b("\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0000", new int[]{0, 32, 45, 27}, true, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            b("\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001", new int[]{32, 45, 0, 0}, false, objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e2);
        }
    }

    private static void b(String str, int[] iArr, boolean z, Object[] objArr) {
        int length;
        char[] cArr;
        int i;
        char[] cArr2;
        int i2;
        int i3;
        int i4;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        o.a.l lVar = new o.a.l();
        int i5 = 0;
        int i6 = iArr[0];
        int i7 = 1;
        int i8 = iArr[1];
        int i9 = iArr[2];
        int i10 = iArr[3];
        char[] cArr3 = a;
        switch (cArr3 == null) {
            case false:
                int i11 = $11 + 27;
                $10 = i11 % 128;
                if (i11 % 2 != 0) {
                    length = cArr3.length;
                    cArr = new char[length];
                    i = 1;
                } else {
                    length = cArr3.length;
                    cArr = new char[length];
                    i = 0;
                }
                while (true) {
                    switch (i < length ? i7 : i5) {
                        case 1:
                            try {
                                Object[] objArr2 = new Object[i7];
                                objArr2[i5] = Integer.valueOf(cArr3[i]);
                                Object obj = o.e.a.s.get(1951085128);
                                if (obj != null) {
                                    cArr2 = cArr3;
                                    i2 = length;
                                } else {
                                    Class cls = (Class) o.e.a.c(11 - KeyEvent.getDeadChar(i5, i5), (char) TextUtils.getOffsetBefore("", i5), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 43);
                                    byte b = (byte) ($$a[i5] - 1);
                                    byte b2 = (byte) (b + 2);
                                    cArr2 = cArr3;
                                    i2 = length;
                                    Object[] objArr3 = new Object[1];
                                    f(b, b2, (byte) (b2 - 2), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1951085128, obj);
                                }
                                cArr[i] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i++;
                                cArr3 = cArr2;
                                length = i2;
                                i5 = 0;
                                i7 = 1;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr3 = cArr;
                            break;
                    }
                }
        }
        char[] cArr4 = new char[i8];
        System.arraycopy(cArr3, i6, cArr4, 0, i8);
        if (bArr2 != null) {
            int i12 = $11 + 71;
            $10 = i12 % 128;
            int i13 = i12 % 2;
            char[] cArr5 = new char[i8];
            lVar.d = 0;
            int i14 = $10 + 3;
            $11 = i14 % 128;
            int i15 = i14 % 2;
            char c = 0;
            while (lVar.d < i8) {
                if (bArr2[lVar.d] == 1) {
                    int i16 = $11 + 61;
                    $10 = i16 % 128;
                    if (i16 % 2 != 0) {
                        int i17 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                            Object obj2 = o.e.a.s.get(2016040108);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(12 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 449);
                                byte b3 = (byte) ($$a[0] - 1);
                                byte b4 = (byte) (b3 + 3);
                                Object[] objArr5 = new Object[1];
                                f(b3, b4, (byte) (b4 - 3), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2016040108, obj2);
                            }
                            cArr5[i17] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            Object obj3 = null;
                            obj3.hashCode();
                            throw null;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    int i18 = lVar.d;
                    try {
                        Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                        Object obj4 = o.e.a.s.get(2016040108);
                        if (obj4 == null) {
                            Class cls3 = (Class) o.e.a.c(12 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 448 - View.MeasureSpec.getMode(0));
                            byte b5 = (byte) ($$a[0] - 1);
                            byte b6 = (byte) (b5 + 3);
                            Object[] objArr7 = new Object[1];
                            f(b5, b6, (byte) (b6 - 3), objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(2016040108, obj4);
                        }
                        cArr5[i18] = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } else {
                    int i19 = lVar.d;
                    try {
                        Object[] objArr8 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                        Object obj5 = o.e.a.s.get(804049217);
                        if (obj5 == null) {
                            Class cls4 = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), 207 - TextUtils.getOffsetBefore("", 0));
                            byte b7 = (byte) ($$a[0] - 1);
                            byte b8 = b7;
                            Object[] objArr9 = new Object[1];
                            f(b7, b8, b8, objArr9);
                            obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(804049217, obj5);
                        }
                        cArr5[i19] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                c = cArr5[lVar.d];
                try {
                    Object[] objArr10 = {lVar, lVar};
                    Object obj6 = o.e.a.s.get(-2112603350);
                    if (obj6 == null) {
                        Class cls5 = (Class) o.e.a.c((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 10, (char) (ViewConfiguration.getPressedStateDuration() >> 16), 259 - View.MeasureSpec.getSize(0));
                        byte b9 = $$a[0];
                        byte b10 = (byte) (b9 - 1);
                        Object[] objArr11 = new Object[1];
                        f(b10, (byte) (b10 | 56), (byte) (b9 - 1), objArr11);
                        obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                        o.e.a.s.put(-2112603350, obj6);
                    }
                    ((Method) obj6).invoke(null, objArr10);
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
            }
            cArr4 = cArr5;
        }
        switch (i10 > 0) {
            case false:
                i3 = 0;
                break;
            default:
                char[] cArr6 = new char[i8];
                i3 = 0;
                System.arraycopy(cArr4, 0, cArr6, 0, i8);
                int i20 = i8 - i10;
                System.arraycopy(cArr6, 0, cArr4, i20, i10);
                System.arraycopy(cArr6, i10, cArr4, 0, i20);
                break;
        }
        if (z) {
            char[] cArr7 = new char[i8];
            while (true) {
                lVar.d = i3;
                if (lVar.d < i8) {
                    cArr7[lVar.d] = cArr4[(i8 - lVar.d) - 1];
                    i3 = lVar.d + 1;
                } else {
                    cArr4 = cArr7;
                }
            }
        }
        switch (i9 > 0) {
            case true:
                int i21 = $11 + Opcodes.DDIV;
                $10 = i21 % 128;
                switch (i21 % 2 != 0 ? ' ' : (char) 21) {
                    case ' ':
                        i4 = 1;
                        break;
                    default:
                        i4 = 0;
                        break;
                }
                while (true) {
                    lVar.d = i4;
                    if (lVar.d >= i8) {
                        break;
                    } else {
                        cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                        i4 = lVar.d + 1;
                    }
                }
        }
        objArr[0] = new String(cArr4);
    }
}
