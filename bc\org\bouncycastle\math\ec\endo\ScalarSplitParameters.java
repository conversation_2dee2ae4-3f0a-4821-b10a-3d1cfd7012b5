package bc.org.bouncycastle.math.ec.endo;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\ScalarSplitParameters.smali */
public class ScalarSplitParameters {
    protected final BigInteger a;
    protected final BigInteger b;
    protected final BigInteger c;
    protected final BigInteger d;
    protected final BigInteger e;
    protected final BigInteger f;
    protected final int g;

    public ScalarSplitParameters(BigInteger[] bigIntegerArr, BigInteger[] bigIntegerArr2, BigInteger bigInteger, BigInteger bigInteger2, int i) {
        a(bigIntegerArr, "v1");
        a(bigIntegerArr2, "v2");
        this.a = bigIntegerArr[0];
        this.b = bigIntegerArr[1];
        this.c = bigIntegerArr2[0];
        this.d = bigIntegerArr2[1];
        this.e = bigInteger;
        this.f = bigInteger2;
        this.g = i;
    }

    private static void a(BigInteger[] bigIntegerArr, String str) {
        if (bigIntegerArr == null || bigIntegerArr.length != 2 || bigIntegerArr[0] == null || bigIntegerArr[1] == null) {
            throw new IllegalArgumentException("'" + str + "' must consist of exactly 2 (non-null) values");
        }
    }

    public int getBits() {
        return this.g;
    }

    public BigInteger getG1() {
        return this.e;
    }

    public BigInteger getG2() {
        return this.f;
    }

    public BigInteger getV1A() {
        return this.a;
    }

    public BigInteger getV1B() {
        return this.b;
    }

    public BigInteger getV2A() {
        return this.c;
    }

    public BigInteger getV2B() {
        return this.d;
    }
}
