package com.rolster.capacitor.otp.google;

import android.app.Activity;
import android.content.IntentFilter;
import com.getcapacitor.PluginCall;
import com.google.android.gms.auth.api.phone.SmsRetriever;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.rolster.capacitor.otp.OtpManagerResolver;
import com.rolster.capacitor.otp.OtpReceiveListener;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\rolster\capacitor\otp\google\GoogleOtpManagerResolver.smali */
public class GoogleOtpManagerResolver implements OtpManagerResolver {
    @Override // com.rolster.capacitor.otp.OtpManagerResolver
    public void execute(final OtpReceiveListener listener, Activity activity, final PluginCall call) {
        String senderCode = call.getString("senderCode");
        SmsRetriever.getClient(activity).startSmsUserConsent(senderCode).addOnSuccessListener(new OnSuccessListener() { // from class: com.rolster.capacitor.otp.google.GoogleOtpManagerResolver$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.tasks.OnSuccessListener
            public final void onSuccess(Object obj) {
                GoogleOtpManagerResolver.lambda$execute$0(OtpReceiveListener.this, call, (Void) obj);
            }
        }).addOnFailureListener(new OnFailureListener() { // from class: com.rolster.capacitor.otp.google.GoogleOtpManagerResolver$$ExternalSyntheticLambda1
            @Override // com.google.android.gms.tasks.OnFailureListener
            public final void onFailure(Exception exc) {
                PluginCall.this.reject(exc.getMessage());
            }
        });
    }

    static /* synthetic */ void lambda$execute$0(OtpReceiveListener listener, PluginCall call, Void command) {
        IntentFilter intent = new IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION);
        listener.registerReceiverSms(new GoogleBroadcastReceiver(listener), intent);
        call.resolve();
    }
}
