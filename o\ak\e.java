package o.ak;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import kotlin.text.Typography;
import o.a.o;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.ei.c;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ak\e.smali */
public final class e extends b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int g;
    private static char[] h;
    private static int j;
    String a;
    Map<o.es.b, Object> b;
    o.h.d c;
    Map<o.es.b, Object> d;
    String e;
    String f;
    Date i;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ak\e$d.smali */
    public interface d {
        void c(o.bb.d dVar);

        void e(Map<o.es.b, Object> map, String str, Date date);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        j = 1;
        o();
        int i = j + 9;
        g = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{73, -116, 106, -1};
        $$e = 42;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.ak.e.$$d
            int r7 = r7 + 66
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r6 = r6 * 4
            int r6 = 3 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r6 = r6 + 1
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = -r7
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ak.e.l(short, byte, byte, java.lang.Object[]):void");
    }

    static void o() {
        h = new char[]{50939, 50848, 50877, 50878, 50876, 50851, 50836, 50837, 50851, 50849, 50851, 50854, 50863, 50855, 50877, 50858, 50852, 50862, 50833, 50849, 50855, 50863, 50855, 50863, 50862, 50861, 50839, 50849, 50855, 50862, 50854, 50839, 50908, 50941, 50941, 50824, 50851, 50849, 50856, 50825, 50817, 50878, 50848, 50827, 50817, 50879, 50877, 50878, 50875, 50873, 50876, 50833, 50838, 50876, 50850, 50876, 50851, 50856, 50848, 50878, 50855, 50849, 50859, 50928, 50824, 50850, 50848, 50856, 50848, 50872, 50816, 50817, 50878, 50823, 50824, 50850, 50848, 50859, 50851, 50848, 50825, 50916, 50916, 50820, 50877, 50878, 50875, 50873, 50876, 50833, 50842, 50850, 50848, 50856, 50848, 50856, 50859, 50862, 50832, 50850, 50848, 50859, 50851, 50832, 50833, 50849, 50940, 50855, 50854, 50862, 50855, 50849, 50839, 50861, 50862, 50863, 50855, 50863, 50855, 50849, 50841, 50847, 50852, 50854, 50846, 50836, 50851, 50876, 50878, 50877, 50848};
    }

    public e(Context context, d dVar, c cVar) {
        super(context, dVar, cVar, o.bb.e.z);
        this.d = new HashMap();
        this.f = "";
        this.i = new Date();
    }

    @Override // o.y.b
    public final String a() {
        int i = j + Opcodes.LREM;
        g = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k("\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{0, 32, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = g + 11;
        j = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    @Override // o.y.b
    public final a<?> b() {
        AsyncTaskC0019e asyncTaskC0019e = new AsyncTaskC0019e(this);
        int i = j + 11;
        g = i % 128;
        switch (i % 2 != 0 ? (char) 23 : (char) 29) {
            case 29:
                return asyncTaskC0019e;
            default:
                int i2 = 96 / 0;
                return asyncTaskC0019e;
        }
    }

    public final void c(String str, Map<o.es.b, Object> map, o.h.d dVar, String str2) {
        g.c();
        Object[] objArr = new Object[1];
        k("\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001", new int[]{0, 32, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("\u0001\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001", new int[]{32, 74, 3, 0}, true, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        this.a = str;
        this.b = map;
        this.c = dVar;
        this.e = str2;
        c();
        int i = g + 25;
        j = i % 128;
        switch (i % 2 == 0 ? '[' : 'W') {
            case Opcodes.DUP_X2 /* 91 */:
                throw null;
            default:
                return;
        }
    }

    /* renamed from: o.ak.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ak\e$e.smali */
    static final class AsyncTaskC0019e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static char a;
        private static long b;
        private static int c;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            c = 1;
            a = (char) 7574;
            d = 161105445;
            b = 6565854932352255525L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(short r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                int r8 = 106 - r8
                int r7 = r7 * 3
                int r7 = 1 - r7
                int r6 = r6 * 4
                int r6 = r6 + 4
                byte[] r0 = o.ak.e.AsyncTaskC0019e.$$d
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1b
                r8 = r7
                r3 = r1
                r4 = r2
                r7 = r6
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L1b:
                r3 = r2
            L1c:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r7
                r7 = r6
                r6 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r8 = r8 + r6
                int r6 = r7 + 1
                r7 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1c
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ak.e.AsyncTaskC0019e.B(short, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{71, -50, -52, -118};
            $$e = 54;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = e + 15;
            c = i % 128;
            int i2 = i % 2;
        }

        AsyncTaskC0019e(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = c + 1;
            e = i % 128;
            switch (i % 2 != 0) {
                case true:
                    Object[] objArr = new Object[1];
                    w((-312084339) - (ViewConfiguration.getEdgeSlop() / 102), "\ud805㙝㶤滍䇝⥤⫌糘뛥燩爲뿭랚蝕⥂묒ᖵፗꌻ嘚セ큽↧\uec3b妑", (char) (TypedValue.complexToFraction(1, 2.0f, 0.0f) > 1.0f ? 1 : (TypedValue.complexToFraction(1, 2.0f, 0.0f) == 1.0f ? 0 : -1)), "跾旸韭䩣", "\u0000\u0000\u0000\u0000", objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w((-312084339) - (ViewConfiguration.getEdgeSlop() >> 16), "\ud805㙝㶤滍䇝⥤⫌糘뛥燩爲뿭랚蝕⥂묒ᖵፗꌻ嘚セ큽↧\uec3b妑", (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "跾旸韭䩣", "\u0000\u0000\u0000\u0000", objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(TextUtils.indexOf("", "") + 1678166678, "㤷遠觃㘷꼗펷\uf417䖯㪷\uddf1㖔흎핿㱉劷줡\ued88寡豔", (char) (63680 - ((byte) KeyEvent.getModifierMetaStateMask())), "陵ۊ셤㟸", "\u0000\u0000\u0000\u0000", objArr);
            o.cf.d dVar = new o.cf.d(context, 45, ((String) objArr[0]).intern());
            int i = c + 17;
            e = i % 128;
            switch (i % 2 != 0 ? (char) 3 : Typography.quote) {
                case '\"':
                    return dVar;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w('0' - AndroidCharacter.getMirror('0'), "䟳扆䯟ᔊ棬꯬", (char) (35243 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), "ꅤ羁ꫀ슉", "\u0000\u0000\u0000\u0000", objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).a);
            o.eg.b b2 = o.aj.a.b(((e) e()).b);
            Object[] objArr2 = new Object[1];
            w((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1186595717, "䏋銣႘\ued3c蒕", (char) (28833 - Color.alpha(0)), "薜먃ꅆ艰", "\u0000\u0000\u0000\u0000", objArr2);
            bVar.d(((String) objArr2[0]).intern(), b2);
            int i = e + 89;
            c = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            j jVar = new j(((e) e()).e, false, ((e) e()).c);
            int i = e + 33;
            c = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = c + 87;
            e = i % 128;
            Object obj = null;
            switch (i % 2 != 0 ? 'E' : 'X') {
                case 'E':
                    obj.hashCode();
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = e + 51;
            c = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(Gravity.getAbsoluteGravity(0, 0) + 606769485, "쩓\u08d2埮⦘爜㙐퓎넭冫녹醜", (char) (ExpandableListView.getPackedPositionChild(0L) + 1), "䶒⪑洤ᰮ", "\u0000\u0000\u0000\u0000", objArr);
            o.eg.b v = bVar.v(((String) objArr[0]).intern());
            e eVar = (e) e();
            Object[] objArr2 = new Object[1];
            w((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, "ᒧ詽", (char) (52987 - TextUtils.getCapsMode("", 0, 0)), "㡙聭\ufb07䷎", "\u0000\u0000\u0000\u0000", objArr2);
            eVar.f = v.r(((String) objArr2[0]).intern());
            e eVar2 = (e) e();
            Object[] objArr3 = new Object[1];
            w(Drawable.resolveOpacity(0, 0), "\ue7cc鰋춙䖗賀陜劽泛\u0a7c䃄餠鯨", (char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1), "돋ﰇ\uf1d8گ", "\u0000\u0000\u0000\u0000", objArr3);
            eVar2.i = v.e(((String) objArr3[0]).intern(), false);
            Object[] objArr4 = new Object[1];
            w((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 1186595717, "䏋銣႘\ued3c蒕", (char) ((ViewConfiguration.getTouchSlop() >> 8) + 28833), "薜먃ꅆ艰", "\u0000\u0000\u0000\u0000", objArr4);
            ((e) e()).d = o.aj.a.b(v.v(((String) objArr4[0]).intern()));
            int i3 = c + Opcodes.LSUB;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = c + 85;
            e = i2 % 128;
            int i3 = i2 % 2;
            switch (i) {
                case 5001:
                    o.bb.a aVar = o.bb.a.ay;
                    int i4 = e + 61;
                    c = i4 % 128;
                    int i5 = i4 % 2;
                    return aVar;
                case 5002:
                    o.bb.a aVar2 = o.bb.a.az;
                    int i6 = c + 33;
                    e = i6 % 128;
                    switch (i6 % 2 == 0) {
                        case false:
                            throw null;
                        default:
                            return aVar2;
                    }
                default:
                    return super.c(i);
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = e + Opcodes.LSUB;
            c = i % 128;
            switch (i % 2 == 0 ? 'O' : (char) 26) {
                case Opcodes.IASTORE /* 79 */:
                    int i2 = AnonymousClass5.e[h().d().ordinal()];
                    throw null;
                default:
                    switch (AnonymousClass5.e[h().d().ordinal()]) {
                        case 1:
                            f().c(g(), ((e) e()).a);
                            return;
                        case 2:
                            f().e(g(), ((e) e()).a);
                            int i3 = e + 43;
                            c = i3 % 128;
                            switch (i3 % 2 == 0 ? '%' : '\\') {
                                case Opcodes.DUP2 /* 92 */:
                                    return;
                                default:
                                    int i4 = 73 / 0;
                                    return;
                            }
                        default:
                            super.t();
                            return;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = e + Opcodes.DDIV;
            c = i % 128;
            switch (i % 2 == 0 ? 'Q' : Typography.less) {
                case Opcodes.FASTORE /* 81 */:
                    ((e) e()).j().e(((e) e()).d, ((e) e()).f, ((e) e()).i);
                    throw null;
                default:
                    ((e) e()).j().e(((e) e()).d, ((e) e()).f, ((e) e()).i);
                    int i2 = e + 43;
                    c = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = c + Opcodes.LREM;
            e = i % 128;
            int i2 = i % 2;
            ((e) e()).j().c(dVar);
            int i3 = c + 13;
            e = i3 % 128;
            switch (i3 % 2 == 0) {
                case true:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        private static void w(int i, String str, char c2, String str2, String str3, Object[] objArr) {
            char[] charArray;
            char[] charArray2;
            char[] cArr;
            int i2;
            int i3 = 2;
            switch (str3 != null ? 'S' : (char) 22) {
                case Opcodes.AASTORE /* 83 */:
                    int i4 = $11 + 83;
                    $10 = i4 % 128;
                    int i5 = i4 % 2;
                    charArray = str3.toCharArray();
                    break;
                default:
                    charArray = str3;
                    break;
            }
            char[] cArr2 = charArray;
            int i6 = 0;
            switch (str2 == null) {
                case false:
                    int i7 = $11 + Opcodes.DDIV;
                    $10 = i7 % 128;
                    int i8 = i7 % 2;
                    charArray2 = str2.toCharArray();
                    break;
                default:
                    charArray2 = str2;
                    break;
            }
            char[] cArr3 = charArray2;
            switch (str != null ? '!' : '\b') {
                case '\b':
                    cArr = str;
                    break;
                default:
                    cArr = str.toCharArray();
                    break;
            }
            o oVar = new o();
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int length2 = cArr2.length;
            char[] cArr5 = new char[length2];
            System.arraycopy(cArr3, 0, cArr4, 0, length);
            System.arraycopy(cArr2, 0, cArr5, 0, length2);
            cArr4[0] = (char) (cArr4[0] ^ c2);
            cArr5[2] = (char) (cArr5[2] + ((char) i));
            int length3 = cArr.length;
            char[] cArr6 = new char[length3];
            oVar.e = 0;
            while (oVar.e < length3) {
                int i9 = $11 + 53;
                $10 = i9 % 128;
                int i10 = i9 % i3;
                try {
                    Object[] objArr2 = {oVar};
                    Object obj = o.e.a.s.get(-429442487);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (char) (20954 - (ViewConfiguration.getLongPressTimeout() >> 16)), TextUtils.indexOf((CharSequence) "", '0') + 345);
                        byte b2 = (byte) i6;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        B(b2, b3, (byte) (b3 | 7), objArr3);
                        String str4 = (String) objArr3[i6];
                        Class<?>[] clsArr = new Class[1];
                        clsArr[i6] = Object.class;
                        obj = cls.getMethod(str4, clsArr);
                        o.e.a.s.put(-429442487, obj);
                    }
                    int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                    try {
                        Object[] objArr4 = {oVar};
                        Object obj2 = o.e.a.s.get(-515165572);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(View.MeasureSpec.getMode(i6) + 10, (char) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 207 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)));
                            byte b4 = (byte) i6;
                            byte b5 = b4;
                            Object[] objArr5 = new Object[1];
                            B(b4, b5, (byte) (b5 + 5), objArr5);
                            String str5 = (String) objArr5[i6];
                            Class<?>[] clsArr2 = new Class[1];
                            clsArr2[i6] = Object.class;
                            obj2 = cls2.getMethod(str5, clsArr2);
                            o.e.a.s.put(-515165572, obj2);
                        }
                        int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                        int i11 = cArr4[oVar.e % 4] * 32718;
                        try {
                            Object[] objArr6 = new Object[3];
                            objArr6[2] = Integer.valueOf(cArr5[intValue]);
                            objArr6[1] = Integer.valueOf(i11);
                            objArr6[i6] = oVar;
                            Object obj3 = o.e.a.s.get(-1614232674);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 11, (char) (ViewConfiguration.getWindowTouchSlop() >> 8), 281 - (ExpandableListView.getPackedPositionForGroup(i6) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(i6) == 0L ? 0 : -1)));
                                byte b6 = (byte) i6;
                                byte b7 = b6;
                                Object[] objArr7 = new Object[1];
                                B(b6, b7, (byte) (b7 + 3), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-1614232674, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                            try {
                                Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                                Object obj4 = o.e.a.s.get(406147795);
                                if (obj4 != null) {
                                    i2 = 2;
                                } else {
                                    Class cls4 = (Class) o.e.a.c(19 - View.MeasureSpec.getSize(0), (char) (14686 - TextUtils.indexOf((CharSequence) "", '0', 0)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + Opcodes.DDIV);
                                    byte b8 = (byte) 0;
                                    byte b9 = b8;
                                    Object[] objArr9 = new Object[1];
                                    B(b8, b9, b9, objArr9);
                                    i2 = 2;
                                    obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(406147795, obj4);
                                }
                                cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                                cArr4[intValue2] = oVar.d;
                                cArr6[oVar.e] = (char) ((((cArr4[intValue2] ^ r6[oVar.e]) ^ (b ^ 6565854932352255525L)) ^ ((int) (d ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                                oVar.e++;
                                i3 = i2;
                                i6 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            objArr[0] = new String(cArr6);
        }
    }

    /* renamed from: o.ak.e$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ak\e$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int a = 1;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            c = 0;
            int[] iArr = new int[o.bb.a.values().length];
            e = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = a;
                int i2 = ((i | 21) << 1) - (i ^ 21);
                c = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.bb.a.az.ordinal()] = 2;
                int i4 = a;
                int i5 = ((i4 | 97) << 1) - (i4 ^ 97);
                c = i5 % 128;
                switch (i5 % 2 != 0 ? '=' : 'E') {
                    case 'E':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:84:0x031b, code lost:
    
        r1 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r20, int[] r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 914
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ak.e.k(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
