package o.cg;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import java.nio.ByteBuffer;
import kotlin.text.Typography;
import o.du.c;
import o.ee.g;
import o.eg.b;
import o.eg.d;
import o.eo.h;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cg\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int c;
    private static char[] d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        a();
        AndroidCharacter.getMirror('0');
        TextUtils.indexOf((CharSequence) "", '0');
        ViewConfiguration.getScrollDefaultDelay();
        int i = c + 95;
        a = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        char[] cArr = new char[1245];
        ByteBuffer.wrap("p Ñ\u008e2\u0089\u0093£ô\u0081Uº¶Ê\u0017ÓxæÙý,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085fáÇ? 6\u00016â\u0004C\u000e<z\u009dl~\u007fßJ¸\u0019\u0019[û¤Tª5É\u0096\u0086w\u0090Ðÿ±ý,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085fÒÇ\" 7\u0001&â\u0015C\u000f<|\u009df~eßD¸W\u0019RûáTº5\u009b\u0096\u008aw\u0084Ðý±ê\u0012µóÖ¬Ô\r=î-O8(\u0003\u0089Yj\u0001Ëi¤x\u0005\tæFGP!¿\u0082½,®\u008d·n¾Ï\u0098¨\u0089\t\u0086,\u008e\u008d·n¾Ï\u0098¨\u0089\tÕêÀKï$Û\u0085ÄfÈÇm 0\u0001&âAC\u0018<d\u009du~eßT,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085f\u0094Ç) y\u00012â\u0013C\u0012<|\u009du~9ß^¸\u0010\u0019\u0015ûµT²5É\u0096\u0095w\u0083Ðâ±ú\u0012ðóÒ¬Î$F\u0085BfMÇF c\u0001oâ\u0007C\u001e, \u008d.n/ÏÎ¨Ü\tÑêø,\u0088\u008dµn¡Ï\u0081¨\u0090\t\u0096êàKé$À\u0085ÊfßÇm \u0018\u0001'â\u0013C\u001c<p\u009d%~xß^¸\u0019\u0019Pû¬T\u00ad5\u009d\u0096\u009c6#\u0097-t(Õ#²\u0015\u0013\nðhQ0>\t\u009f\b|_Ý¯ºº\u001b«ø\u0098Y\u0082&ñ\u0087ëdèÅÉ¢Ú\u0003ßálN\u0015/\t\u008c\u001em\\Êa«d\bhé@¶Y\u0017§ô©U¨2\u0089\u0093\u009bp\u0096Ñÿ¾°\u001fÓüÁ]È;(\u0098=y6ÆL§\u0004\u0004låmB<#G\u0080FaWÎ¹¯ ,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085fÐÇ= )\u00019â\bC\u001e<h\u009dq~xßB¸W\u0019\u0015ûûTý5Ì\u0096\u0096wÑÐì±ý\u0012ñóÄ¬Ù\riî1O>(M\u0089\u001ej\u0007Ën¤h\u0005Yá\u009d@²£¾\u0002Íe\u0098Ä\u0085'ñ\u0086ñéÀHÆ«Ð\n9m0Ì:/\u000f\u008e\u000eñ)Pr³x\u0012YuQÔ\u00156µ\u0099µø\u008c[Åº\u0082\u001dì|ôßð>\u0081aôÀ\r#e\u00820å\u001fD\u001c§U\u0006hisÈ\t+Q\u008aYì¨Où®¦\u0011\u0080p\u0090Óì2¥\u0095öôßWÖ¶À\u00191xtèxIvªq\u000b[l{ÍU.<\u008f)à\u0012A\u001b¢\u0006,¬\u008d¦n¾Ï\u0080¨ª\t\u0081êàKé$À\u0085ÆfåÇ\" 2\u00010â\u000fâ}Cs v\u0001}fKÇT$6\u0085nêWKV¨\f\tñnªÏç,Ñ\u008dÚò³S °§\u0011Þv\u0082×\u00855w\u009a.û_X[¹T\u001e~\u007f+Ü6=\u0002bNÃó ø\u0081¢æÙGØ¤É\u0005§j¾ËÚ(Û\u0089ÂïpLe\u00ad&\u0012Us\\Ð51#\u00962÷^T\u001aµ\u0016\u001aá{ëØ\u009a9Ò\u009eÇÿ¸\\£½¨â\u009bC\u009a¡s\u0006yglÄ\u001e%\u0007\u008a\u0006,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085fÖÇ? 6\u0001 â\u0011C]<`\u009dv~1ßH¸T\u0019EûµT¤5É\u0096ßwÑÐã±ö\u0012áó\u0081¬Ü\r-î!O4(\t\u0089Yj\u0001Ën¤=\u0005NæWG^!¸\u0082©cõÜ\u008c½\u009c\u001eùÿ¥,®\u008d n¥Ï®¨\u0098\t\u0087êåK½$\u0084\u0085\u0085f\u0083Çm \u001e\u0001'â\u000eC\b<y\u009dv~1ßL¸K\u0019PûáT®5\u0081\u0096\u0084w\u0083Ðä±÷\u0012òó\u0081¬É\r!î Oq(\u001e\u0089\u0018j\u0018Ëd¤=\u0005`æaG\u0011,û\u008dån¶Ï\u009f¨\u0096\t\u0080êñKî$\u0089\u0085ÖfÙÇ, +\u00010âAC\t<a\u009d`~1ß^¸X\u0019Xû¤Tý5 \u0096¡¢e\u0003kànAe&S\u0087Ld.ÅvªO\u000bNè=Iô.ý\u008fëlÚÍ\u0096²£\u0013ªð¾Q\u00836\u0096\u0097Þu~Úy»\u0002\u0018ZùR^#?r\u009c9}\u0018\"\u0019\u0083÷`þÁº¦Ë\u0007ÓäÎbëÃå à\u0081ëæÝGÂ¤ \u0005øjÁËÀ(\u0097\u0089iînOt¬\u0004\r_r>Ó/0!\u0091\u0018ö\\W\u001dµå\u001aè{\u008cØÉ9Ç\u009eèÿ¹\\½½\u0094â\u008cCu  \u0001.f\bÇN$U\u00850ê-K\u001e¨\u000e\t\u001doæÌû-°\u0092ÊóÍP ±¬,¨\u008d¦n²Ï\u0082¨\u008c\t\u009bêõKÓ$Ü\u0085ÈfÓÇ( +×ivb\u0095c4JS_òUsUÒ[1^\u0090U÷cV|µ\u001e\u0014F{\u007fÚ~9\"\u0098×ÿÑ^ë½ù\u001céc\u009fÂ\u00ad!\u009e\u0080·ç¶F§¤Y\u000brj}Éu(o\u008f\u0018îBM\u001a¬(ó3R×, \u008d¶n¢Ï\u0098¨\u009c\t\u0087êÀKþ$Ê\u0085ÊfÄÇ# -\u0001\u001câ\u0005g\u009dÆ\u0093%\u0087\u0084·ã¹B®¡À\u0000äoýÎò-á\u008c\u0014\u00adu\frïa,¬\u008d½n¡Ï\u0084¨\u008b\t\u008cêÅKü$Ý\u0085ÀeÃÄÂ'Ä\u0086ÿáÛ@ú£\u0080\u0002\u0092m»Ì°\u0080Ý!ôÂýcÀ\u0004Ý¥\u0096F\u0086ç¿\u0088\u009e)\u0083ÊÒkH\fu\u00addNOï_\u0090>(\u0097\u0089\u0081j\u0095Ë¯¬«\r°îõOË ì\u0081öbÏÃ\u001eWþöð\u0015õ´þÓÈr×\u0091µ,\u0083\u008d¶n¾Ï\u0083¨Ù\t\u0090êùKþ$Ì\u0085ÕfÅÇ$ 6\u0001;âAC\u0018<g\u009df~~ßX¸W\u0019Aû¤T¯5\u008c\u0096\u0081wÑÐú±ñ\u0012üóÍ¬Ø\riî7O4(\f\u0089\u001dj\u001cËo¤z\u0005\tæFGP!¿\u0082½cõÜÛ½Ý,\u008a\u008dªn¿Ï\u009e¨\u008d\t\u0087êôKþ$Ý\u0085ÌfßÇ* y\u0001;â\u0004C\n<)\u009dF~pß_¸]\u0019\u001bûïTó, \u008d¡,ª\u008dªn¤Ï\u0083¨\u008d\t\u0087êøKÞ$Æ\u0085ÁfÔ,\u009e\u008d·n¾Ï\u0083¨\u009e\tÕêâKò$Ü\u0085ËfÅÇ?  \u0001uâ\u0002C\u0012<m\u009d`~1ßC¸L\u0019Xû£T¸5\u009b\u0096Åw\u0097Ðâ±ë\u0012øóÀ¬É,\u00ad\u008d n¢Ï\u008e¨\u008b\t\u009cêñKé$À\u0085Êfß,¥\u008d¤n³Ï\u0088¨\u0095P+ñ#\u0012;³\u0001Ô\u0017u,\u0096o7p,½\u008d¶n¡Ï¤¨\u0094\t\u0094êæKø$ü\u0085×fÝ,½\u008d¶n¡Ï¡¨\u0098\t\u0097êäKñ,½\u008d¶n¡Ï ¨\u009c\t\u0081êàKù$È\u0085ÑfÐÁÌ`Í\u0083Ú\"áEüäß\u0007\u0087¦\u0098É¯h¾Ïó,½\u008d¶n¡Ï¹¨\u0097\t\u0096êÔKï$Å6:\u0097,t5Õ9²\n\u0013\u0013ðrQo>], \u008d¶n¢Ï\u0098¨\u009c\t\u0087êÅKü$Ý\u0085Ä,ª\u008d¤n£Ï\u0089¨Ù\t¼êÅK½,é\u008dµn°Ï\u009f¨\u0098\t\u0098êäKé$Ì\u0085×fÂÇm 8\u0001'â\u0004C]<z\u009d`~eß\rQñðÿ\u0013ø²ÒÕÊtÁ\u0097¶6¢Y\u0097ø\u008c\u001b¤ºwÝo|k,»\u008d n Ï\u0098¨\u0090\t\u0087êäKî$è\u0085ÆfÅÇ$ /\u00014â\u0015C\u0014<f\u009dk`-Á0\"3\u0083\u000bä\nE\u0017¦A\u0007hhAÉW*F\u008bêìóMò®\u0085\u000f\u0095pàÑñ2â\u0093ØôËUÑ·2\u00183y\u0000Ú\u0005;V\u009cdý{^e¿\u0006à}A¼¢\u00ad\u0003£d\u009aÅÐ&Ü\u0087¨,ª\u008d·n´Ï\u0089¨\u0090\t\u0081±À\u0010ÝóÞRæ5ç\u0094úw¬Ö\u0085¹¬\u0018ºû«Z\u0007=\u001e\u009c\u001f\u007fLÞe¡\f\u0000\u001aã\u000bBg%\u001a\u0084\u001bf\u008bC\u0089â\u0093\u0001\u0096 \u0099Ç¸f©\u0085Ç$ÞKÊêæ\tñ¨\u001bÏJnK\u008dr,\u001eSjòe\u0011g°>×ev`\u0094Ò;\u0089Z¨ù¹\u0018·¿ÎÞ\u008a}\u0083\u009cáÃ®b\u0013\u0081\u0005 BG,æ/\u0005%¤]Ë@ji\u0089b(pN\u008bí\u0089\f\u0092³·Òª,º\u008d n¥Ïª¨\u008b\t\u009aêôKí$ù\u0085ÕfÂÇ( y\u0001xâAC-<Y\u009dV~Tß\r¸V\u0019SûáTº5\u009b\u0096\u008aw\u0084Ðý±¹\u0012°óÒ¬\u009d\r î6Oq(\t\u0089\u001cj\u0013Ëh¤s\u0005LæAG\u0011!¯\u0082 cõÜ\u0095½\u0095\u001eìÿ¥X÷9Ä\u009aË{ÆÔ5µ}\u0016\b÷\u0006P\u00051d\u0092osp,\u0001\u008dUoªÈ ©ñ\n\u0088ë\u0094D\u0083%¡\u0086ügÙÀÕ¡Ý\u0002$ã:\\4=\u0015\u009e\u0014\u007ffØk".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1245);
        d = cArr;
        b = -1377266574944989755L;
    }

    private static void g(int i, short s, int i2, Object[] objArr) {
        byte[] bArr = $$a;
        int i3 = (i * 3) + 1;
        int i4 = (s * 4) + 4;
        int i5 = 105 - i2;
        byte[] bArr2 = new byte[i3];
        int i6 = -1;
        int i7 = i3 - 1;
        if (bArr == null) {
            i5 = i7 + i5;
            i7 = i7;
            i4++;
        }
        while (true) {
            i6++;
            bArr2[i6] = (byte) i5;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i5 += bArr[i4];
            i7 = i7;
            i4++;
        }
    }

    static void init$0() {
        $$a = new byte[]{91, -22, 50, -29};
        $$b = 108;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r15v10 */
    /* JADX WARN: Type inference failed for: r15v11, types: [boolean] */
    /* JADX WARN: Type inference failed for: r15v13 */
    /* JADX WARN: Type inference failed for: r15v14 */
    /* JADX WARN: Type inference failed for: r15v15 */
    /* JADX WARN: Type inference failed for: r15v16 */
    /* JADX WARN: Type inference failed for: r15v17 */
    /* JADX WARN: Type inference failed for: r15v18 */
    /* JADX WARN: Type inference failed for: r15v9 */
    public static o.eo.e e(o.eg.b r28) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 2328
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cg.e.e(o.eg.b):o.eo.e");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x00c0. Please report as an issue. */
    private static o.eo.e a(b bVar) throws d {
        h hVar;
        c cVar;
        String str;
        String str2;
        String str3;
        String str4;
        o.du.h hVar2;
        String q;
        int i = a + 17;
        c = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) (KeyEvent.normalizeMetaState(0) + 23594), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), ExpandableListView.getPackedPositionType(0L) + 10, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (ViewConfiguration.getTouchSlop() >> 8), TextUtils.lastIndexOf("", '0', 0) + 826, Color.rgb(0, 0, 0) + 16777240, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 849 - KeyEvent.keyCodeFromString(""), ExpandableListView.getPackedPositionGroup(0L) + 2, objArr3);
        String r = bVar.r(((String) objArr3[0]).intern());
        try {
            Object[] objArr4 = new Object[1];
            f((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), (ViewConfiguration.getTapTimeout() >> 16) + 851, ';' - AndroidCharacter.getMirror('0'), objArr4);
            byte[] a2 = o.ej.b.a(bVar.k(((String) objArr4[0]).intern()).shortValue());
            int i3 = a + 43;
            c = i3 % 128;
            switch (i3 % 2 != 0 ? 'K' : Typography.quote) {
            }
            h hVar3 = h.a;
            Object[] objArr5 = new Object[1];
            f((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), View.resolveSize(0, 0) + 894, 11 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr5);
            b u = bVar.u(((String) objArr5[0]).intern());
            switch (u == null) {
                case true:
                    hVar = hVar3;
                    cVar = null;
                    str = null;
                    str2 = null;
                    str3 = null;
                    str4 = null;
                    hVar2 = null;
                    break;
                default:
                    int i4 = a + 47;
                    c = i4 % 128;
                    if (i4 % 2 != 0) {
                    }
                    Object[] objArr6 = new Object[1];
                    f((char) (ViewConfiguration.getEdgeSlop() >> 16), (Process.myPid() >> 22) + 905, Color.alpha(0) + 5, objArr6);
                    String q2 = u.q(((String) objArr6[0]).intern());
                    Object[] objArr7 = new Object[1];
                    f((char) (31883 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), 910 - TextUtils.indexOf("", "", 0, 0), KeyEvent.getDeadChar(0, 0) + 8, objArr7);
                    String q3 = u.q(((String) objArr7[0]).intern());
                    Object[] objArr8 = new Object[1];
                    f((char) View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 918, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 10, objArr8);
                    String q4 = u.q(((String) objArr8[0]).intern());
                    c cVar2 = q4 != null ? new c(q4) : null;
                    Object[] objArr9 = new Object[1];
                    f((char) (Process.getGidForName("") + 1), TextUtils.lastIndexOf("", '0', 0, 0) + 930, 8 - View.MeasureSpec.getSize(0), objArr9);
                    String q5 = u.q(((String) objArr9[0]).intern());
                    Object[] objArr10 = new Object[1];
                    f((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), 936 - TextUtils.lastIndexOf("", '0', 0, 0), ExpandableListView.getPackedPositionChild(0L) + 12, objArr10);
                    b u2 = u.u(((String) objArr10[0]).intern());
                    switch (u2 != null ? '<' : (char) 22) {
                        case '<':
                            Object[] objArr11 = new Object[1];
                            f((char) (60777 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), 947 - TextUtils.lastIndexOf("", '0', 0), 10 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr11);
                            q = u2.q(((String) objArr11[0]).intern());
                            if (q != null) {
                                StringBuilder sb = new StringBuilder();
                                Object[] objArr12 = new Object[1];
                                f((char) (View.combineMeasuredStates(0, 0) + 58137), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 959, TextUtils.indexOf("", "") + 1, objArr12);
                                q = sb.append(((String) objArr12[0]).intern()).append(q).toString();
                                break;
                            }
                            break;
                        default:
                            q = null;
                            break;
                    }
                    Object[] objArr13 = new Object[1];
                    f((char) Color.blue(0), ExpandableListView.getPackedPositionType(0L) + 959, 8 - TextUtils.indexOf((CharSequence) "", '0'), objArr13);
                    String q6 = u.q(((String) objArr13[0]).intern());
                    o.du.h hVar4 = q6 != null ? new o.du.h(q6) : null;
                    Object[] objArr14 = new Object[1];
                    f((char) (6791 - (ViewConfiguration.getScrollBarSize() >> 8)), View.resolveSize(0, 0) + 968, 9 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr14);
                    hVar = (h) u.d(h.class, ((String) objArr14[0]).intern(), h.a);
                    str2 = q5;
                    str3 = q;
                    hVar2 = hVar4;
                    cVar = cVar2;
                    str = q2;
                    str4 = q3;
                    break;
            }
            Object[] objArr15 = new Object[1];
            f((char) Drawable.resolveOpacity(0, 0), ExpandableListView.getPackedPositionGroup(0L) + 977, 10 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr15);
            String q7 = bVar.q(((String) objArr15[0]).intern());
            Object[] objArr16 = new Object[1];
            f((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 1079), TextUtils.indexOf((CharSequence) "", '0') + 759, 12 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr16);
            String q8 = bVar.q(((String) objArr16[0]).intern());
            g.c();
            Object[] objArr17 = new Object[1];
            f((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 23595), KeyEvent.getMaxKeyCode() >> 16, View.MeasureSpec.getMode(0) + 10, objArr17);
            String intern2 = ((String) objArr17[0]).intern();
            StringBuilder sb2 = new StringBuilder();
            c cVar3 = cVar;
            Object[] objArr18 = new Object[1];
            f((char) ((-1) - ImageFormat.getBitsPerPixel(0)), 987 - Drawable.resolveOpacity(0, 0), 9 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr18);
            StringBuilder append = sb2.append(((String) objArr18[0]).intern()).append(r);
            Object[] objArr19 = new Object[1];
            f((char) View.MeasureSpec.makeMeasureSpec(0, 0), 996 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), View.MeasureSpec.makeMeasureSpec(0, 0) + 20, objArr19);
            g.d(intern2, append.append(((String) objArr19[0]).intern()).toString());
            Object[] objArr20 = new Object[1];
            f((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 32090), 1015 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), Drawable.resolveOpacity(0, 0) + 14, objArr20);
            String q9 = bVar.q(((String) objArr20[0]).intern());
            Object[] objArr21 = new Object[1];
            f((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), Gravity.getAbsoluteGravity(0, 0) + 1029, 18 - TextUtils.indexOf("", "", 0, 0), objArr21);
            return new o.eo.e(r, null, str, str2, str3, str4, q7, q8, bVar.b(((String) objArr21[0]).intern(), Boolean.FALSE), a2, q9, cVar3, hVar2, hVar, true);
        } catch (IllegalArgumentException e) {
            Object[] objArr22 = new Object[1];
            f((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 862, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 33, objArr22);
            throw new d(((String) objArr22[0]).intern());
        }
    }

    private static o.eo.d a(String str, b bVar) throws d {
        g.c();
        Object[] objArr = new Object[1];
        f((char) (23593 - TextUtils.indexOf((CharSequence) "", '0')), ViewConfiguration.getLongPressTimeout() >> 16, ExpandableListView.getPackedPositionType(0L) + 10, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (19591 - TextUtils.indexOf("", "")), (ViewConfiguration.getScrollBarSize() >> 8) + 1047, MotionEvent.axisFromString("") + 40, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.eo.d dVar = new o.eo.d(str);
        Object[] objArr3 = new Object[1];
        f((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 850, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 1, objArr3);
        dVar.d(bVar.r(((String) objArr3[0]).intern()));
        Object[] objArr4 = new Object[1];
        f((char) (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 905, View.resolveSize(0, 0) + 5, objArr4);
        dVar.c(bVar.r(((String) objArr4[0]).intern()));
        Object[] objArr5 = new Object[1];
        f((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), KeyEvent.getDeadChar(0, 0) + 1086, 6 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr5);
        dVar.b(bVar.g(((String) objArr5[0]).intern()).booleanValue());
        g.c();
        Object[] objArr6 = new Object[1];
        f((char) (Drawable.resolveOpacity(0, 0) + 23594), TextUtils.indexOf("", "", 0), 10 - KeyEvent.keyCodeFromString(""), objArr6);
        String intern2 = ((String) objArr6[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr7 = new Object[1];
        f((char) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 40298), 1093 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 23 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr7);
        StringBuilder append = sb.append(((String) objArr7[0]).intern()).append(dVar.h());
        Object[] objArr8 = new Object[1];
        f((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), 995 - TextUtils.getOffsetBefore("", 0), 20 - (KeyEvent.getMaxKeyCode() >> 16), objArr8);
        g.d(intern2, append.append(((String) objArr8[0]).intern()).toString());
        int i = c + 35;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                return dVar;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void d(o.eo.d r8, boolean r9) {
        /*
            Method dump skipped, instructions count: 290
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cg.e.d(o.eo.d, boolean):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 576
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cg.e.f(char, int, int, java.lang.Object[]):void");
    }
}
