package o.i;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import java.lang.reflect.Method;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\f.smali */
public final class f implements o.ee.a<CustomerAuthenticationMethodType>, o.ei.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final f a;
    public static final f b;
    public static final f c;
    public static final f d;
    public static final f e;
    public static final f f;
    private static final /* synthetic */ f[] g;
    private static long h;
    private static int i;
    private static long k;
    private static int l;
    private static char[] m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static char f89o;
    private final String j;

    static void c() {
        f89o = (char) 17957;
        i = 437561548;
        h = 6565854932352255525L;
        m = new char[]{11402, 58621, 48150, 30132, 3533, 50465, 40624, 22223, 11393, 58600, 48155, 30131, 3520, 50453, 40585, 22216, 28263, 27251, 41530, 64201, 13153, 19218, 33735, 55412, 4099, 10418, 24941, 11405, 58612, 48143, 30120, 3530, 50452, 40603, 22216, 28262, 10172, 65500, 46965, 18587, 216, 55418, 11402, 58622, 48151, 30130, 3532, 50463, 40621, 11402, 58590, 48183, 30098, 3564, 50495, 40589};
        k = -4442631848899779439L;
    }

    static void init$0() {
        $$a = new byte[]{119, -13, -39, 23};
        $$b = 203;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r0 = o.i.f.$$a
            int r7 = 106 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L35
        L16:
            r3 = r2
        L17:
            r6 = r8
            r8 = r7
            r7 = r6
            int r9 = r9 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.f.r(int, short, byte, java.lang.Object[]):void");
    }

    private static /* synthetic */ f[] d() {
        int i2 = l + 21;
        int i3 = i2 % 128;
        n = i3;
        int i4 = i2 % 2;
        f[] fVarArr = {d, e, a, b, c, f};
        int i5 = i3 + 51;
        l = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return fVarArr;
            default:
                throw null;
        }
    }

    public static f valueOf(String str) {
        int i2 = l + 39;
        n = i2 % 128;
        int i3 = i2 % 2;
        f fVar = (f) Enum.valueOf(f.class, str);
        int i4 = n + 1;
        l = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public static f[] values() {
        int i2 = n + Opcodes.LSHL;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? '\'' : Typography.amp) {
            case '\'':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return (f[]) g.clone();
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = n + 57;
        l = i2 % 128;
        int i3 = i2 % 2;
        CustomerAuthenticationMethodType b2 = b();
        int i4 = n + 97;
        l = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return b2;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        n = 1;
        c();
        Object[] objArr = new Object[1];
        q((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), View.MeasureSpec.makeMeasureSpec(0, 0), ExpandableListView.getPackedPositionType(0L) + 8, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((-926941337) - ExpandableListView.getPackedPositionChild(0L), "멾萎䁴ᙷ\ue101눪睩ꄢ絞", (char) ExpandableListView.getPackedPositionGroup(0L), "桋뿿忈ᕆ", "\u0000\u0000\u0000\u0000", objArr2);
        d = new f(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        q((char) View.resolveSize(0, 0), 8 - (ViewConfiguration.getScrollBarSize() >> 8), 9 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        q((char) (18162 - Drawable.resolveOpacity(0, 0)), (ViewConfiguration.getLongPressTimeout() >> 16) + 17, TextUtils.lastIndexOf("", '0', 0) + 11, objArr4);
        e = new f(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        q((char) Color.blue(0), 27 - ExpandableListView.getPackedPositionType(0L), 15 - TextUtils.indexOf("", "", 0, 0), objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        p(ExpandableListView.getPackedPositionGroup(0L), "䄞ꔮ푩戭獭᧦雥\uf5ed鮌搇\udbc3絕凝覱웓\uf7de", (char) (51567 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), "ꝕ\u0b9d漝ᓉ", "\u0000\u0000\u0000\u0000", objArr6);
        a = new f(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        p(ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), "闭켨犵[ҁ\ue416貵拀ʴ\uee8f诺\uebc8", (char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), "ߚ邼혢㱉", "\u0000\u0000\u0000\u0000", objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        p(TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 1828985675, "叙愨㵪끔䷈ߝШ㽵\ue481ᵲ説遭Ⳙ", (char) (50886 - (ViewConfiguration.getScrollBarSize() >> 8)), "둔ﯤ욒裆", "\u0000\u0000\u0000\u0000", objArr8);
        b = new f(intern4, 3, ((String) objArr8[0]).intern());
        Object[] objArr9 = new Object[1];
        q((char) View.resolveSizeAndState(0, 0, 0), 42 - (KeyEvent.getMaxKeyCode() >> 16), 7 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        q((char) (Process.myTid() >> 22), TextUtils.indexOf((CharSequence) "", '0') + 50, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 7, objArr10);
        c = new f(intern5, 4, ((String) objArr10[0]).intern());
        Object[] objArr11 = new Object[1];
        p(Color.argb(0, 0, 0, 0), "柘琰ΰ휓", (char) ((ViewConfiguration.getJumpTapTimeout() >> 16) + 47637), "\uf0d9鿏ᗒʺ", "\u0000\u0000\u0000\u0000", objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        p((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1, "\udfb6뾪祧钯", (char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 53446), "맀\uee08잰꿐", "\u0000\u0000\u0000\u0000", objArr12);
        f = new f(intern6, 5, ((String) objArr12[0]).intern());
        g = d();
        int i2 = l + 27;
        n = i2 % 128;
        int i3 = i2 % 2;
    }

    private f(String str, int i2, String str2) {
        this.j = str2;
    }

    @Override // java.lang.Enum
    public final String toString() {
        int i2 = n + 87;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 6 : (char) 22) {
            case 22:
                return this.j;
            default:
                throw null;
        }
    }

    public final CustomerAuthenticationMethodType b() {
        int i2 = n + 77;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? 'E' : '_') {
            case Opcodes.SWAP /* 95 */:
                switch (AnonymousClass3.a[ordinal()]) {
                    case 1:
                    case 2:
                        return CustomerAuthenticationMethodType.Pin;
                    case 3:
                        return CustomerAuthenticationMethodType.DeviceBiometric;
                    case 4:
                        return CustomerAuthenticationMethodType.ScreenUnlock;
                    case 5:
                        CustomerAuthenticationMethodType customerAuthenticationMethodType = CustomerAuthenticationMethodType.Consent;
                        int i3 = n + 75;
                        l = i3 % 128;
                        int i4 = i3 % 2;
                        return customerAuthenticationMethodType;
                    case 6:
                        return CustomerAuthenticationMethodType.None;
                    default:
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr = new Object[1];
                        p(ImageFormat.getBitsPerPixel(0) + 1, "ࠐ癒\uf415ⴂ\ued3b\u2002ᖰ럮龊荥疘䰒ʾ庠곗乧⼾쪩", (char) (8481 - (ViewConfiguration.getWindowTouchSlop() >> 8)), "牣뱎⇫С", "\u0000\u0000\u0000\u0000", objArr);
                        throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                }
            default:
                int i5 = AnonymousClass3.a[ordinal()];
                throw null;
        }
    }

    /* renamed from: o.i.f$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\f$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] a;
        static final /* synthetic */ int[] c;
        private static int d;
        private static int e;

        /* JADX WARN: Failed to find 'out' block for switch in B:13:0x0045. Please report as an issue. */
        static {
            e = 0;
            d = 1;
            int[] iArr = new int[CustomerAuthenticationMethodType.values().length];
            c = iArr;
            try {
                iArr[CustomerAuthenticationMethodType.Pin.ordinal()] = 1;
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[CustomerAuthenticationMethodType.DeviceBiometric.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[CustomerAuthenticationMethodType.ScreenUnlock.ordinal()] = 3;
                int i = e;
                int i2 = (i & 47) + (i | 47);
                d = i2 % 128;
                switch (i2 % 2 == 0 ? '#' : 'R') {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                c[CustomerAuthenticationMethodType.Consent.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                c[CustomerAuthenticationMethodType.None.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            int[] iArr2 = new int[f.values().length];
            a = iArr2;
            try {
                iArr2[f.d.ordinal()] = 1;
                int i3 = (d + 28) - 1;
                e = i3 % 128;
                if (i3 % 2 != 0) {
                }
            } catch (NoSuchFieldError e7) {
            }
            try {
                a[f.e.ordinal()] = 2;
            } catch (NoSuchFieldError e8) {
            }
            try {
                a[f.a.ordinal()] = 3;
                int i4 = d;
                int i5 = (i4 & 23) + (i4 | 23);
                e = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e9) {
            }
            try {
                a[f.b.ordinal()] = 4;
            } catch (NoSuchFieldError e10) {
            }
            try {
                a[f.c.ordinal()] = 5;
                int i6 = d;
                int i7 = ((i6 | 35) << 1) - (35 ^ i6);
                e = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e11) {
            }
            try {
                a[f.f.ordinal()] = 6;
                int i9 = d;
                int i10 = ((i9 | 89) << 1) - (i9 ^ 89);
                e = i10 % 128;
                switch (i10 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e12) {
            }
        }
    }

    public static f b(CustomerAuthenticationMethodType customerAuthenticationMethodType) {
        int i2 = n + Opcodes.DSUB;
        l = i2 % 128;
        int i3 = i2 % 2;
        switch (AnonymousClass3.c[customerAuthenticationMethodType.ordinal()]) {
            case 1:
                return d.c().f();
            case 2:
                f fVar = a;
                int i4 = l + 13;
                n = i4 % 128;
                int i5 = i4 % 2;
                return fVar;
            case 3:
                return b;
            case 4:
                return c;
            case 5:
                return f;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                p(Process.myTid() >> 22, "ࠐ癒\uf415ⴂ\ued3b\u2002ᖰ럮龊荥疘䰒ʾ庠곗乧⼾쪩", (char) (Drawable.resolveOpacity(0, 0) + 8481), "牣뱎⇫С", "\u0000\u0000\u0000\u0000", objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(customerAuthenticationMethodType.name()).toString());
        }
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = l;
        int i3 = i2 + 57;
        n = i3 % 128;
        int i4 = i3 % 2;
        String str = this.j;
        int i5 = i2 + 45;
        n = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return str;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.i.f a(java.lang.String r15) {
        /*
            o.i.f[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L8:
            r4 = 1
            if (r3 >= r1) goto Ld
            r5 = r4
            goto Le
        Ld:
            r5 = r2
        Le:
            switch(r5) {
                case 0: goto L1e;
                default: goto L11;
            }
        L11:
            int r5 = o.i.f.l
            int r5 = r5 + 81
            int r6 = r5 % 128
            o.i.f.n = r6
            int r5 = r5 % 2
            if (r5 != 0) goto L5c
            goto L5c
        L1e:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            long r5 = android.widget.ExpandableListView.getPackedPositionForChild(r2, r2)
            r7 = 0
            int r3 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            int r9 = (-1) - r3
            java.lang.String r10 = "\udec2쾄鿙\udeaf嵻헶깧ƨ\uf449⏗\u0e85ἁ⯤튢\udf6e귉볚嗈瓄䕭陙묡便ᶉ⦇愸쏚⡘\udc5c佡랷귀쾅뤘ꑊꮉ\ude0a狇볿⪩࠸䡷蹴\uefaa\uf512嫦怵눁\uf3c5顔ή\uf350"
            r3 = 64298(0xfb2a, float:9.01E-41)
            int r5 = android.widget.ExpandableListView.getPackedPositionChild(r7)
            int r3 = r3 - r5
            char r11 = (char) r3
            java.lang.String r12 = "㎓ଔ⮳諻"
            java.lang.String r13 = "\u0000\u0000\u0000\u0000"
            java.lang.Object[] r3 = new java.lang.Object[r4]
            r14 = r3
            p(r9, r10, r11, r12, r13, r14)
            r2 = r3[r2]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.StringBuilder r15 = r1.append(r15)
            java.lang.String r15 = r15.toString()
            r0.<init>(r15)
            throw r0
        L5c:
            r5 = r0[r3]
            java.lang.String r6 = r5.j
            boolean r6 = r6.equalsIgnoreCase(r15)
            if (r6 == 0) goto L67
            goto L68
        L67:
            r4 = r2
        L68:
            switch(r4) {
                case 0: goto L76;
                default: goto L6b;
            }
        L6b:
            int r15 = o.i.f.l
            int r15 = r15 + 49
            int r0 = r15 % 128
            o.i.f.n = r0
            int r15 = r15 % 2
            goto L83
        L76:
            int r3 = r3 + 1
            int r4 = o.i.f.l
            int r4 = r4 + 109
            int r5 = r4 % 128
            o.i.f.n = r5
            int r4 = r4 % 2
            goto L8
        L83:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.f.a(java.lang.String):o.i.f");
    }

    private static void p(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char[] cArr3;
        if (str3 != null) {
            int i3 = $11 + 65;
            $10 = i3 % 128;
            int i4 = i3 % 2;
            cArr = str3.toCharArray();
        } else {
            cArr = str3;
        }
        char[] cArr4 = cArr;
        switch (str2 != null ? 'H' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                cArr2 = str2;
                break;
            default:
                cArr2 = str2.toCharArray();
                int i5 = $11 + 73;
                $10 = i5 % 128;
                int i6 = i5 % 2;
                break;
        }
        char[] cArr5 = cArr2;
        switch (str != null ? '1' : Typography.less) {
            case '<':
                cArr3 = str;
                break;
            default:
                cArr3 = str.toCharArray();
                break;
        }
        o.a.o oVar = new o.a.o();
        int length = cArr5.length;
        char[] cArr6 = new char[length];
        int length2 = cArr4.length;
        char[] cArr7 = new char[length2];
        System.arraycopy(cArr5, 0, cArr6, 0, length);
        System.arraycopy(cArr4, 0, cArr7, 0, length2);
        cArr6[0] = (char) (cArr6[0] ^ c2);
        cArr7[2] = (char) (cArr7[2] + ((char) i2));
        int length3 = cArr3.length;
        char[] cArr8 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10, (char) (View.resolveSizeAndState(0, 0, 0) + 20954), 344 - Drawable.resolveOpacity(0, 0));
                    byte b2 = (byte) 0;
                    Object[] objArr3 = new Object[1];
                    r((byte) 7, b2, (byte) (b2 - 1), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Object.class);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getTouchSlop() >> 8) + 10, (char) Color.green(0), 207 - View.MeasureSpec.makeMeasureSpec(0, 0));
                        byte b3 = (byte) 5;
                        byte b4 = (byte) (b3 - 5);
                        Object[] objArr5 = new Object[1];
                        r(b3, b4, (byte) (b4 - 1), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    try {
                        Object[] objArr6 = {oVar, Integer.valueOf(cArr6[oVar.e % 4] * 32718), Integer.valueOf(cArr7[intValue])};
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 11, (char) Color.red(0), Color.alpha(0) + 281);
                            byte b5 = (byte) ($$b & 7);
                            byte b6 = (byte) (b5 - 3);
                            Object[] objArr7 = new Object[1];
                            r(b5, b6, (byte) (b6 - 1), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr6[intValue2] * 32718), Integer.valueOf(cArr7[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((Process.myPid() >> 22) + 19, (char) (Color.alpha(0) + 14687), 112 - Color.green(0));
                                byte b7 = (byte) 0;
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                r(b7, b8, (byte) (b8 - 1), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr7[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr6[intValue2] = oVar.d;
                            cArr8[oVar.e] = (char) ((((cArr6[intValue2] ^ r3[oVar.e]) ^ (h ^ 6565854932352255525L)) ^ ((int) (i ^ 6565854932352255525L))) ^ ((char) (f89o ^ 6565854932352255525L)));
                            oVar.e++;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        String str4 = new String(cArr8);
        int i7 = $11 + 61;
        $10 = i7 % 128;
        if (i7 % 2 == 0) {
            objArr[0] = str4;
        } else {
            Object obj5 = null;
            obj5.hashCode();
            throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(char r23, int r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 1122
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.f.q(char, int, int, java.lang.Object[]):void");
    }
}
