package o.i;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.CustomerConsentPrompt;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\e.smali */
public final class e extends g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        e = 1;
        b();
        int i = c + 87;
        e = i % 128;
        switch (i % 2 == 0 ? 'G' : 'Y') {
            case 'G':
                throw null;
            default:
                return;
        }
    }

    static void b() {
        a = new char[]{50754, 51181, 51153, 51156, 51180, 51165, 51140, 51153, 51155, 51158, 51156, 51150, 51137, 51177, 51182, 51180, 51155, 51156, 51158, 51143, 51140, 50762, 51142, 51140, 51140, 51141, 51165, 51161, 51138, 51136, 50738, 51147, 51160, 51166, 51166, 51162, 50722, 50694, 50694, 50724, 51165, 51162, 51165, 51142, 51138, 51142, 50730, 50715, 51148, 51156, 51157, 51163, 51166, 51139, 51165, 50739, 51149, 51156, 51166, 51142, 51139, 51163, 51166, 51142, 51146, 51138, 51166, 51164, 51166, 51145, 51147, 51160, 51166, 51166, 51162, 50722, 50720, 51163, 51160, 51162, 50935, 50879, 50851, 50851, 50877, 50860, 50834, 50851, 50849, 50851, 50855, 50863, 50859, 50851, 50876, 50852, 50859, 50851, 50873, 50838, 50836, 50854, 50852, 50851, 50876, 50878, 50873, 50833};
    }

    static void init$0() {
        $$a = new byte[]{117, 56, 99, 31};
        $$b = Opcodes.IFNULL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r7 = r7 + 66
            int r8 = r8 * 2
            int r8 = 3 - r8
            byte[] r0 = o.i.e.$$a
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            if (r3 != r6) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.e.n(short, byte, int, java.lang.Object[]):void");
    }

    public e() {
        super(f.c);
    }

    @Override // o.i.g
    protected final o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException {
        int i = c + Opcodes.DSUB;
        e = i % 128;
        int i2 = i % 2;
        if (!(customerAuthenticationPrompt instanceof CustomerConsentPrompt)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l("\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000", new int[]{0, 21, Opcodes.ARETURN, 11}, false, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001", new int[]{21, 59, Opcodes.IF_ACMPEQ, 4}, false, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr3 = new Object[1];
            l("\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0000", new int[]{80, 28, 0, 0}, true, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        CustomerConsentPrompt customerConsentPrompt = (CustomerConsentPrompt) customerAuthenticationPrompt;
        o.l.a aVar = new o.l.a(context, customerConsentPrompt.getTitle(), customerConsentPrompt.getSubtitle(), this);
        int i3 = e + 1;
        c = i3 % 128;
        int i4 = i3 % 2;
        return aVar;
    }

    private static void l(String str, int[] iArr, boolean z, Object[] objArr) {
        String str2 = str;
        int i = $10 + 9;
        $11 = i % 128;
        int i2 = i % 2;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        o.a.l lVar = new o.a.l();
        int i3 = 0;
        int i4 = iArr[0];
        int i5 = 1;
        int i6 = iArr[1];
        int i7 = iArr[2];
        int i8 = iArr[3];
        char[] cArr = a;
        long j = 0;
        if (cArr != null) {
            int i9 = $10 + 59;
            $11 = i9 % 128;
            int i10 = i9 % 2;
            int length = cArr.length;
            char[] cArr2 = new char[length];
            int i11 = 0;
            while (true) {
                switch (i11 < length ? 'a' : '\f') {
                    case Opcodes.LADD /* 97 */:
                        try {
                            Object[] objArr2 = new Object[i5];
                            objArr2[i3] = Integer.valueOf(cArr[i11]);
                            Object obj = o.e.a.s.get(1951085128);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((CdmaCellLocation.convertQuartSecToDecDegrees(i3) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i3) == 0.0d ? 0 : -1)) + 11, (char) (1 - (Process.getElapsedCpuTime() > j ? 1 : (Process.getElapsedCpuTime() == j ? 0 : -1))), 43 - (TypedValue.complexToFraction(i3, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(i3, 0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                byte b = (byte) i3;
                                Object[] objArr3 = new Object[1];
                                n(b, (byte) (b | 54), b, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1951085128, obj);
                            }
                            cArr2[i11] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i11++;
                            i3 = 0;
                            i5 = 1;
                            j = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    default:
                        cArr = cArr2;
                        break;
                }
            }
        }
        char[] cArr3 = new char[i6];
        System.arraycopy(cArr, i4, cArr3, 0, i6);
        int i12 = 16;
        switch (bArr2 != null ? (char) 16 : '\t') {
            case '\t':
                break;
            default:
                int i13 = $11 + 5;
                $10 = i13 % 128;
                int i14 = i13 % 2;
                char[] cArr4 = new char[i6];
                lVar.d = 0;
                char c2 = 0;
                while (lVar.d < i6) {
                    if (bArr2[lVar.d] == 1) {
                        int i15 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                            Object obj2 = o.e.a.s.get(2016040108);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(ImageFormat.getBitsPerPixel(0) + 12, (char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 448 - (ViewConfiguration.getFadingEdgeLength() >> i12));
                                byte b2 = (byte) 0;
                                Object[] objArr5 = new Object[1];
                                n(b2, (byte) (b2 | 53), b2, objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2016040108, obj2);
                            }
                            cArr4[i15] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } else {
                        int i16 = lVar.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr3[lVar.d]), Integer.valueOf(c2)};
                            Object obj3 = o.e.a.s.get(804049217);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(':' - AndroidCharacter.getMirror('0'), (char) (1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 206);
                                byte b3 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                n(b3, $$a[1], b3, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(804049217, obj3);
                            }
                            cArr4[i16] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    c2 = cArr4[lVar.d];
                    try {
                        Object[] objArr8 = {lVar, lVar};
                        Object obj4 = o.e.a.s.get(-2112603350);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(11 - TextUtils.getOffsetAfter("", 0), (char) (ViewConfiguration.getTouchSlop() >> 8), Color.argb(0, 0, 0, 0) + 259);
                            byte b4 = (byte) 0;
                            byte b5 = b4;
                            Object[] objArr9 = new Object[1];
                            n(b4, b5, b5, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(-2112603350, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                        i12 = 16;
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        if (i8 > 0) {
            char[] cArr5 = new char[i6];
            System.arraycopy(cArr3, 0, cArr5, 0, i6);
            int i17 = i6 - i8;
            System.arraycopy(cArr5, 0, cArr3, i17, i8);
            System.arraycopy(cArr5, i8, cArr3, 0, i17);
        }
        if (z) {
            int i18 = $10 + 41;
            $11 = i18 % 128;
            int i19 = i18 % 2;
            char[] cArr6 = new char[i6];
            int i20 = 0;
            while (true) {
                lVar.d = i20;
                if (lVar.d < i6) {
                    cArr6[lVar.d] = cArr3[(i6 - lVar.d) - 1];
                    i20 = lVar.d + 1;
                } else {
                    cArr3 = cArr6;
                }
            }
        }
        if (i7 > 0) {
            int i21 = 0;
            while (true) {
                lVar.d = i21;
                if (lVar.d < i6) {
                    int i22 = $11 + 53;
                    $10 = i22 % 128;
                    int i23 = i22 % 2;
                    cArr3[lVar.d] = (char) (cArr3[lVar.d] - iArr[2]);
                    i21 = lVar.d + 1;
                }
            }
        }
        objArr[0] = new String(cArr3);
    }
}
