package o.fn;

import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.settings.WalletSettingsRights;
import fr.antelop.sdk.settings.WalletSettingsValue;
import java.lang.reflect.Method;
import java.util.Currency;
import kotlin.text.Typography;
import o.a.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\a.smali */
public final class a extends c<a> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static long d;
    private final o.ej.e c;
    private final Currency e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        a();
        SystemClock.elapsedRealtime();
        int i = a + 37;
        b = i % 128;
        switch (i % 2 == 0 ? '.' : (char) 1) {
            case '.':
                break;
            default:
                int i2 = 87 / 0;
                break;
        }
    }

    static void a() {
        d = 2744220045798451753L;
    }

    private static void g(short s, short s2, short s3, Object[] objArr) {
        byte[] bArr = $$a;
        int i = 4 - (s3 * 4);
        int i2 = 71 - (s * 3);
        int i3 = 1 - (s2 * 2);
        byte[] bArr2 = new byte[i3];
        int i4 = -1;
        int i5 = i3 - 1;
        if (bArr == null) {
            i2 += i5;
            i5 = i5;
            i++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
        }
        while (true) {
            int i6 = i4 + 1;
            bArr2[i6] = (byte) i2;
            if (i6 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i2 += bArr[i];
            i5 = i5;
            i++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = i6;
        }
    }

    static void init$0() {
        $$a = new byte[]{18, UtilitiesSDKConstants.SRP_LABEL_MAC, -55, -33};
        $$b = 47;
    }

    public a(Currency currency) {
        super(true);
        this.e = currency;
        this.c = o.ej.e.c(currency.getCurrencyCode());
    }

    a() {
        super(false);
        this.e = null;
        this.c = null;
    }

    public final WalletSettingsValue<Currency> b() {
        int i = a + 55;
        b = i % 128;
        Currency currency = null;
        try {
            switch (i % 2 != 0) {
                case false:
                    currency = e();
                    break;
                default:
                    e();
                    currency.hashCode();
                    throw null;
            }
        } catch (o.ei.j e) {
        }
        WalletSettingsValue<Currency> walletSettingsValue = new WalletSettingsValue<>(currency, WalletSettingsRights.ReadOnly);
        int i2 = a + 61;
        b = i2 % 128;
        int i3 = i2 % 2;
        return walletSettingsValue;
    }

    private Currency e() throws o.ei.j {
        Object obj;
        int i = b + Opcodes.DSUB;
        a = i % 128;
        switch (i % 2 == 0 ? 'X' : 'W') {
            case Opcodes.POP2 /* 88 */:
                Object[] objArr = new Object[1];
                f("ᴍᵁ鬝熄郞漊뚹\ue72f䕑쒵꾉썒䫏㍇棭띵Ừ龎鰅殊눃\uebbb쁩\udfa2䙔㟆琜돩᩸菦", TextUtils.indexOf("", "", 0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f("ᴍᵁ鬝熄郞漊뚹\ue72f䕑쒵꾉썒䫏㍇棭띵Ừ龎鰅殊눃\uebbb쁩\udfa2䙔㟆琜돩᩸菦", TextUtils.indexOf("", "", 0), objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        Currency currency = this.e;
        int i2 = b + Opcodes.LSHL;
        a = i2 % 128;
        int i3 = i2 % 2;
        return currency;
    }

    public final boolean c(a aVar) {
        int i = a + 59;
        b = i % 128;
        int i2 = i % 2;
        boolean equals = this.e.getCurrencyCode().equals(aVar.e.getCurrencyCode());
        int i3 = a + 7;
        b = i3 % 128;
        switch (i3 % 2 != 0 ? 'K' : Typography.dollar) {
            case 'K':
                throw null;
            default:
                return equals;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x002a. Please report as an issue. */
    private static void f(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2 = $10 + 7;
        $11 = i2 % 128;
        if (i2 % 2 == 0) {
            throw null;
        }
        switch (str != null) {
            case true:
                cArr = str.toCharArray();
                int i3 = $10 + 25;
                $11 = i3 % 128;
                switch (i3 % 2 == 0) {
                }
            default:
                cArr = str;
                break;
        }
        n nVar = new n();
        char[] b2 = n.b(d ^ 8632603938177761503L, cArr, i);
        nVar.c = 4;
        while (nVar.c < b2.length) {
            int i4 = $11 + 19;
            $10 = i4 % 128;
            int i5 = i4 % 2;
            nVar.e = nVar.c - 4;
            int i6 = nVar.c;
            try {
                Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % 4]), Long.valueOf(nVar.e), Long.valueOf(d)};
                Object obj = o.e.a.s.get(-1945790373);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(11 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) (Process.myTid() >> 22), 44 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)));
                    byte b3 = (byte) ($$b & 1);
                    byte b4 = (byte) (b3 - 1);
                    Object[] objArr3 = new Object[1];
                    g(b3, b4, b4, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                    o.e.a.s.put(-1945790373, obj);
                }
                b2[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {nVar, nVar};
                    Object obj2 = o.e.a.s.get(-341518981);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(Gravity.getAbsoluteGravity(0, 0) + 10, (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 249 - TextUtils.getTrimmedLength(""));
                        byte b5 = (byte) 0;
                        byte b6 = b5;
                        Object[] objArr5 = new Object[1];
                        g(b5, b6, b6, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-341518981, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        String str2 = new String(b2, 4, b2.length - 4);
        int i7 = $11 + 25;
        $10 = i7 % 128;
        int i8 = i7 % 2;
        objArr[0] = str2;
    }
}
