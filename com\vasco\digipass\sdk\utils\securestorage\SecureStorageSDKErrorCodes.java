package com.vasco.digipass.sdk.utils.securestorage;

import kotlin.Metadata;

@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0002\b#\bÆ\u0002\u0018\u00002\u00020\u0001R\u0014\u0010\u0003\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0003\u0010\u0004R\u0014\u0010\u0005\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0005\u0010\u0004R\u0014\u0010\u0006\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0006\u0010\u0004R\u0014\u0010\u0007\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0007\u0010\u0004R\u0014\u0010\b\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\b\u0010\u0004R\u0014\u0010\t\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\t\u0010\u0004R\u0014\u0010\n\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\n\u0010\u0004R\u0014\u0010\u000b\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u000b\u0010\u0004R\u0014\u0010\f\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\f\u0010\u0004R\u0014\u0010\r\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\r\u0010\u0004R\u0014\u0010\u000e\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u000e\u0010\u0004R\u0014\u0010\u000f\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u000f\u0010\u0004R\u0014\u0010\u0010\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0010\u0010\u0004R\u0014\u0010\u0011\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0011\u0010\u0004R\u0014\u0010\u0012\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0012\u0010\u0004R\u0014\u0010\u0013\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0013\u0010\u0004R\u0014\u0010\u0014\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0014\u0010\u0004R\u0014\u0010\u0015\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0015\u0010\u0004R\u0014\u0010\u0016\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0016\u0010\u0004R\u0014\u0010\u0017\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0017\u0010\u0004R\u0014\u0010\u0018\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0018\u0010\u0004R\u0014\u0010\u0019\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0019\u0010\u0004R\u0014\u0010\u001a\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001a\u0010\u0004R\u0014\u0010\u001b\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001b\u0010\u0004R\u0014\u0010\u001c\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001c\u0010\u0004R\u0014\u0010\u001d\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001d\u0010\u0004R\u0014\u0010\u001e\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001e\u0010\u0004R\u0014\u0010\u001f\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u001f\u0010\u0004R\u0014\u0010 \u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b \u0010\u0004R\u0014\u0010!\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b!\u0010\u0004R\u0014\u0010\"\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\"\u0010\u0004R\u0014\u0010#\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b#\u0010\u0004R\u0014\u0010$\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b$\u0010\u0004¨\u0006%"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorageSDKErrorCodes;", "", "", "INTERNAL_ERROR", "I", "STORAGE_NAME_NULL", "STORAGE_NAME_INCORRECT_LENGTH", "STORAGE_NAME_INCORRECT_FORMAT", "UNKNOWN_STORAGE", "UNREADABLE_STORAGE", "CONTEXT_NULL", "ITERATION_COUNT_INCORRECT", "KEY_NULL", "KEY_INCORRECT_LENGTH", "KEY_INCORRECT_FORMAT", "UNKNOWN_KEY", "VALUE_NULL", "VALUE_INCORRECT_FORMAT", "INCORRECT_GETTER", "STORAGE_CORRUPTED_HMAC_INCONSISTENT", "UNRECOVERABLE_KEY", "UNSUPPORTED_VERSION", "ERROR_ANDROID_KEY_STORE", "ERROR_LOADING_ANDROID_KEY_STORE", "ERROR_NO_SUCH_ALGORITHM_KEY_STORE", "ERROR_IO_KEY_STORE", "ERROR_INVALID_ALGORITHM_KEYSTORE", "ERROR_NO_SUCH_PROVIDER_KEYSTORE", "STORAGE_CORRUPTED_KEY_CORRUPTED", "INVALID_TIMEOUT_VALUE", "BIOMETRIC_AUTHENTICATION_FAILED", "BIOMETRIC_AUTH_NOT_ENROLLED_ERROR", "BIOMETRIC_NO_HARDWARE_ERROR", "BIOMETRIC_UNSPECIFIED_ERROR", "BIOMETRIC_KEY_NOT_FOUND", "BIOMETRIC_AUTHENTICATION_ERROR", "INITIALIZATION_FRAGMENTACTIVITY_NULL", "lib_release"}, k = 1, mv = {1, 8, 0})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\SecureStorageSDKErrorCodes.smali */
public final class SecureStorageSDKErrorCodes {
    public static final int BIOMETRIC_AUTHENTICATION_ERROR = -4332;
    public static final int BIOMETRIC_AUTHENTICATION_FAILED = -4327;
    public static final int BIOMETRIC_AUTH_NOT_ENROLLED_ERROR = -4328;
    public static final int BIOMETRIC_KEY_NOT_FOUND = -4331;
    public static final int BIOMETRIC_NO_HARDWARE_ERROR = -4329;
    public static final int BIOMETRIC_UNSPECIFIED_ERROR = -4330;
    public static final int CONTEXT_NULL = -4306;
    public static final int ERROR_ANDROID_KEY_STORE = -4319;
    public static final int ERROR_INVALID_ALGORITHM_KEYSTORE = -4323;
    public static final int ERROR_IO_KEY_STORE = -4322;
    public static final int ERROR_LOADING_ANDROID_KEY_STORE = -4320;
    public static final int ERROR_NO_SUCH_ALGORITHM_KEY_STORE = -4321;
    public static final int ERROR_NO_SUCH_PROVIDER_KEYSTORE = -4324;
    public static final int INCORRECT_GETTER = -4315;
    public static final int INITIALIZATION_FRAGMENTACTIVITY_NULL = -4333;
    public static final SecureStorageSDKErrorCodes INSTANCE = new SecureStorageSDKErrorCodes();
    public static final int INTERNAL_ERROR = -4300;
    public static final int INVALID_TIMEOUT_VALUE = -4326;
    public static final int ITERATION_COUNT_INCORRECT = -4307;
    public static final int KEY_INCORRECT_FORMAT = -4310;
    public static final int KEY_INCORRECT_LENGTH = -4309;
    public static final int KEY_NULL = -4308;
    public static final int STORAGE_CORRUPTED_HMAC_INCONSISTENT = -4316;
    public static final int STORAGE_CORRUPTED_KEY_CORRUPTED = -4325;
    public static final int STORAGE_NAME_INCORRECT_FORMAT = -4303;
    public static final int STORAGE_NAME_INCORRECT_LENGTH = -4302;
    public static final int STORAGE_NAME_NULL = -4301;
    public static final int UNKNOWN_KEY = -4311;
    public static final int UNKNOWN_STORAGE = -4304;
    public static final int UNREADABLE_STORAGE = -4305;
    public static final int UNRECOVERABLE_KEY = -4317;
    public static final int UNSUPPORTED_VERSION = -4318;
    public static final int VALUE_INCORRECT_FORMAT = -4314;
    public static final int VALUE_NULL = -4312;
}
