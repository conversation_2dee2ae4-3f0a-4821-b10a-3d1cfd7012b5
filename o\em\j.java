package o.em;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import fr.antelop.sdk.TimePeriod;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import o.am.c;
import o.em.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\j.smali */
public final class j extends d<o.es.a<?>> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static int[] d;
    private static int e;
    o.eo.e a;
    Date b = new Date();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        f();
        Drawable.resolveOpacity(0, 0);
        PointF.length(0.0f, 0.0f);
        KeyEvent.getMaxKeyCode();
        TextUtils.getCapsMode("", 0, 0);
        int i = c + 31;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void f() {
        d = new int[]{-668610415, -715032706, -1321270621, -1932152136, 1289621178, -1234003016, 1505956327, 843132217, -1725584562, -837645336, 542803961, 2048342985, 2135534059, -962020642, -390928620, 291153926, -1958911387, 653627280};
    }

    static void init$0() {
        $$a = new byte[]{15, -30, 44, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE};
        $$b = Opcodes.LSHL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.em.j.$$a
            int r8 = r8 * 4
            int r8 = r8 + 4
            int r6 = r6 + 115
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r8
            r4 = r2
            goto L26
        L14:
            r3 = r2
        L15:
            int r4 = r3 + 1
            byte r5 = (byte) r6
            r1[r3] = r5
            if (r4 != r7) goto L24
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L24:
            r3 = r0[r8]
        L26:
            int r8 = r8 + 1
            int r3 = -r3
            int r6 = r6 + r3
            r3 = r4
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.j.m(int, int, int, java.lang.Object[]):void");
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ long a() {
        long a;
        int i = c + Opcodes.LSHR;
        e = i % 128;
        switch (i % 2 != 0 ? '6' : 'T') {
            case Opcodes.BASTORE /* 84 */:
                a = super.a();
                break;
            default:
                a = super.a();
                int i2 = 44 / 0;
                break;
        }
        int i3 = c + 93;
        e = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.em.d
    public final /* bridge */ /* synthetic */ List<o.es.a<?>> d() {
        int i = e + 17;
        c = i % 128;
        int i2 = i % 2;
        List<o.es.a<?>> d2 = super.d();
        int i3 = c + 45;
        e = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    @Override // o.em.d
    protected final /* bridge */ /* synthetic */ o.eg.b d(o.es.a<?> aVar) throws o.eg.d {
        int i = c + 15;
        e = i % 128;
        int i2 = i % 2;
        o.eg.b d2 = d2(aVar);
        int i3 = e + 21;
        c = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    @Override // o.em.d
    protected final /* synthetic */ o.es.a<?> e(o.eg.b bVar) throws o.eg.d {
        int i = c + 49;
        e = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                o.es.a<?> c2 = c(bVar);
                int i2 = c + 33;
                e = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return c2;
                }
            default:
                c(bVar);
                obj.hashCode();
                throw null;
        }
    }

    /* renamed from: d, reason: avoid collision after fix types in other method */
    private static o.eg.b d2(o.es.a<?> aVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        l(new int[]{66917808, -1448866390, 367762272, -497588514, 2125426797, -806579862, -1352085116, 966469276, 973264394, 361382266, -1019141963, -1740681119}, 21 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        o.eg.b bVar = new o.eg.b();
        Object[] objArr2 = new Object[1];
        l(new int[]{66917808, -1448866390, 367762272, -497588514, 2125426797, -806579862, -1352085116, 966469276, -299254658, 1305562736}, ((byte) KeyEvent.getModifierMetaStateMask()) + 21, objArr2);
        bVar.d(((String) objArr2[0]).intern(), aVar.e());
        if (aVar.e().a() != Boolean.class) {
            switch (aVar.e().a() == BigDecimal.class ? 'I' : '\t') {
                case '\t':
                    String str = "";
                    switch (aVar.e().a() == TimePeriod.class ? (char) 4 : ']') {
                        case Opcodes.DUP2_X1 /* 93 */:
                            Object obj = null;
                            if (aVar.e().a() == TimeZone.class) {
                                int i = e + 77;
                                c = i % 128;
                                switch (i % 2 == 0) {
                                    case false:
                                        TimeZone timeZone = (TimeZone) aVar.d();
                                        if (timeZone == null) {
                                            bVar.d(intern, "");
                                            break;
                                        } else {
                                            bVar.d(intern, timeZone.getID());
                                            break;
                                        }
                                    default:
                                        obj.hashCode();
                                        throw null;
                                }
                            } else {
                                if (aVar.e().a() != String[].class) {
                                    int i2 = c + 9;
                                    e = i2 % 128;
                                    switch (i2 % 2 != 0) {
                                        case false:
                                            if (aVar.e().a() != Integer[].class) {
                                                StringBuilder sb = new StringBuilder();
                                                Object[] objArr3 = new Object[1];
                                                l(new int[]{36267618, -274516210, 122416918, -174914938, 964260359, -672052824, -231080193, 717913513, 239935350, -210773522, -1671506806, 856252657, -1436816041, -730655996, 380460004, 406605423, 789430526, 1843410437, 1528573832, -1043747747, 461685024, -621206388, -1975729086, -2138544787}, 47 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr3);
                                                throw new o.eg.d(sb.append(((String) objArr3[0]).intern()).append(aVar.e().a().getSimpleName()).toString());
                                            }
                                            break;
                                        default:
                                            aVar.e().a();
                                            throw null;
                                    }
                                }
                                if (aVar.d() != null) {
                                    bVar.d(intern, new o.eg.e(aVar.d()));
                                }
                            }
                            return bVar;
                        default:
                            TimePeriod timePeriod = (TimePeriod) aVar.d();
                            if (timePeriod != null) {
                                str = timePeriod.getString();
                            } else {
                                int i3 = e + 35;
                                c = i3 % 128;
                                int i4 = i3 % 2;
                            }
                            bVar.d(intern, str);
                            return bVar;
                    }
            }
        }
        bVar.d(intern, aVar.d());
        return bVar;
    }

    private o.es.a<?> c(o.eg.b bVar) throws o.eg.d {
        Object[] objArr = new Object[1];
        l(new int[]{66917808, -1448866390, 367762272, -497588514, 2125426797, -806579862, -1352085116, 966469276, 973264394, 361382266, -1019141963, -1740681119}, Color.blue(0) + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(new int[]{66917808, -1448866390, 367762272, -497588514, 2125426797, -806579862, -1352085116, 966469276, -299254658, 1305562736}, 20 - View.resolveSizeAndState(0, 0, 0), objArr2);
        o.es.b valueOf = o.es.b.valueOf(bVar.r(((String) objArr2[0]).intern()));
        if (bVar.d(intern) == null) {
            return new o.es.a<>(null, valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == Boolean.class) {
            return new o.es.a<>(bVar.g(intern), valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == BigDecimal.class) {
            return new o.es.a<>(bVar.l(intern), valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == String[].class) {
            return new o.es.a<>(bVar.t(intern), valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == Integer[].class) {
            return new o.es.a<>(bVar.y(intern), valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == TimePeriod.class) {
            return new o.es.a<>(TimePeriod.getPeriodFromString(bVar.r(intern)), valueOf, Boolean.TRUE, this.a);
        }
        if (valueOf.a() == TimeZone.class) {
            String r = bVar.r(intern);
            return new o.es.a<>(r.equals("") ? null : TimeZone.getTimeZone(r), valueOf, Boolean.TRUE, this.a);
        }
        Object[] objArr3 = new Object[1];
        l(new int[]{36267618, -274516210, 122416918, -174914938, 964260359, -672052824, -231080193, 717913513, 239935350, -210773522, -1671506806, 856252657, -1436816041, -730655996, 380460004, 406605423, 789430526, 1843410437, 1528573832, -1043747747, 461685024, -621206388, -1019141963, -1740681119}, TextUtils.getTrimmedLength("") + 45, objArr3);
        throw new o.eg.d(((String) objArr3[0]).intern());
    }

    public final void b(Context context, String str, final d.e<o.es.a<?>> eVar) throws WalletValidationException {
        int i = c + 13;
        e = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        l(new int[]{1766252846, 426024996, 74977859, -34293833, -1293817773, 188342048, 1299943896, -972001538, 1570267988, 344271452, -1869967509, -379784986, -494928425, -1966666856, 265061535, -181177392, -1873495700, -85394481}, ImageFormat.getBitsPerPixel(0) + 34, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l(new int[]{-936566291, -123770233, -878451084, 10888957}, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 7, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        o.ei.c c2 = o.ei.c.c();
        this.a = c2.i().get(str);
        if (!c2.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            l(new int[]{1821641245, -1156104412, -1062360792, 2070407023}, (ViewConfiguration.getFadingEdgeLength() >> 16) + 6, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l(new int[]{1821641245, -1156104412, -1727862100, 117397528, -432921760, -2056671250, -502507378, 710441511, 159128000, -1338867758, 1829846831, -233350624, 1217172979, 1076560141, -1551557357, 2115983802, -1610403971, -1923551943, -1144262309, -1059027431, 548101495, -986018684}, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 42, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        new o.am.c(context, new c.InterfaceC0021c() { // from class: o.em.j.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int b;
            private static int d;
            private static int[] e;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                d = 0;
                b = 1;
                e = new int[]{-1907060317, 2107996196, 2117856380, -446682345, -705030100, -1834311751, -120399199, -744761275, -1002673090, -1014625268, 988106743, -90121233, -1673527770, -126778665, 452485529, 383204797, -1056286975, -1594584401};
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r6, int r7, byte r8, java.lang.Object[] r9) {
                /*
                    byte[] r0 = o.em.j.AnonymousClass4.$$a
                    int r8 = r8 * 3
                    int r8 = r8 + 4
                    int r7 = 116 - r7
                    int r6 = r6 * 3
                    int r6 = 1 - r6
                    byte[] r1 = new byte[r6]
                    int r6 = r6 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L19
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L34
                L19:
                    r3 = r2
                    r5 = r8
                    r8 = r7
                    r7 = r5
                L1d:
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    int r4 = r3 + 1
                    if (r3 != r6) goto L2c
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2c:
                    r3 = r0[r7]
                    r5 = r9
                    r9 = r7
                    r7 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r5
                L34:
                    int r7 = -r7
                    int r8 = r8 + r7
                    int r7 = r9 + 1
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1d
                */
                throw new UnsupportedOperationException("Method not decompiled: o.em.j.AnonymousClass4.g(int, int, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{86, 121, 65, -9};
                $$b = 240;
            }

            @Override // o.am.c.InterfaceC0021c
            public final void e(o.aj.d dVar) {
                Date date;
                List<o.es.a<?>> arrayList = new ArrayList<>();
                switch (j.this.a != null ? (char) 15 : 'C') {
                    case 'C':
                        break;
                    default:
                        int i3 = b + 85;
                        d = i3 % 128;
                        int i4 = i3 % 2;
                        switch (dVar != null ? '8' : (char) 3) {
                            default:
                                if (!dVar.a().isEmpty()) {
                                    o.ee.g.c();
                                    Object[] objArr5 = new Object[1];
                                    f(new int[]{1305111170, -1310276011, 352211058, -1459373406, -1597804572, -1284934959, -1041821362, 1971051457, 1977396135, 280965876, 640594174, 959733041, -167970553, 695405770, -759935866, -475600618, 1364153203, 1707700885}, Drawable.resolveOpacity(0, 0) + 33, objArr5);
                                    String intern3 = ((String) objArr5[0]).intern();
                                    Object[] objArr6 = new Object[1];
                                    f(new int[]{-1182469214, 1592848772, -705066755, 219106563, 808889646, 834756857, -760804310, 1128426082, 1326451207, -1712540683, -1322746584, -955345582, 182441020, 1052266070, 1592691840, -520277499, 1168240901, -1811809790, 183214772, 1568147964, -700497632, -1507521985, -976746284, 671656446, 2041112428, -1872871647, -1581106191, -390787768, 620375131, 1231192231, 1592691840, -520277499, -645501285, 1406979841}, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 67, objArr6);
                                    o.ee.g.d(intern3, ((String) objArr6[0]).intern());
                                    Map<o.es.b, Object> a = dVar.a();
                                    for (o.es.b bVar : o.es.b.values()) {
                                        arrayList.add(new o.es.a<>(a.get(bVar), bVar, Boolean.TRUE, j.this.a));
                                    }
                                    j.this.a(arrayList, new Date().getTime());
                                    break;
                                }
                            case 3:
                                o.ee.g.c();
                                Object[] objArr7 = new Object[1];
                                f(new int[]{1305111170, -1310276011, 352211058, -1459373406, -1597804572, -1284934959, -1041821362, 1971051457, 1977396135, 280965876, 640594174, 959733041, -167970553, 695405770, -759935866, -475600618, 1364153203, 1707700885}, 33 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr7);
                                String intern4 = ((String) objArr7[0]).intern();
                                Object[] objArr8 = new Object[1];
                                f(new int[]{-1182469214, 1592848772, -705066755, 219106563, 808889646, 834756857, -760804310, 1128426082, 1326451207, -1712540683, -1322746584, -955345582, 182441020, 1052266070, 1592691840, -520277499, 1168240901, -1811809790, 183214772, 1568147964, -700497632, -1507521985, -976746284, 671656446, 778306125, 1806813252, -425521802, -579754083, -1020602298, -394270533, 1007961616, -1743209564, 955629679, 478576048}, 64 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr8);
                                o.ee.g.d(intern4, ((String) objArr8[0]).intern());
                                arrayList = j.this.g();
                                break;
                        }
                        j jVar = j.this;
                        if (dVar != null) {
                            date = dVar.b();
                            int i5 = b + Opcodes.DNEG;
                            d = i5 % 128;
                            int i6 = i5 % 2;
                        } else {
                            date = null;
                        }
                        jVar.b = date;
                        eVar.e(arrayList);
                        break;
                }
            }

            @Override // o.am.c.InterfaceC0021c
            public final void a(o.bb.d dVar) {
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                f(new int[]{1305111170, -1310276011, 352211058, -1459373406, -1597804572, -1284934959, -1041821362, 1971051457, 1977396135, 280965876, 640594174, 959733041, -167970553, 695405770, -759935866, -475600618, 1364153203, 1707700885}, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 33, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr6 = new Object[1];
                f(new int[]{-1182469214, 1592848772, -705066755, 219106563, 808889646, 834756857, -760804310, 1128426082, 1326451207, -1712540683, -1322746584, -955345582, 182441020, 1052266070, 1592691840, -520277499, 66181849, 1645668563, 1053300605, 2146060305, -981055895, 195305585}, ExpandableListView.getPackedPositionType(0L) + 43, objArr6);
                o.ee.g.d(intern3, sb.append(((String) objArr6[0]).intern()).append(dVar.e()).toString());
                eVar.e(o.bv.c.c(dVar).d());
                int i3 = d + Opcodes.DNEG;
                b = i3 % 128;
                int i4 = i3 % 2;
            }

            private static void f(int[] iArr, int i3, Object[] objArr5) {
                int length;
                int[] iArr2;
                int[] iArr3;
                int i4;
                o.a.g gVar = new o.a.g();
                char[] cArr = new char[4];
                int i5 = 2;
                char[] cArr2 = new char[iArr.length * 2];
                int[] iArr4 = e;
                int i6 = -1667374059;
                int i7 = 1;
                int i8 = 0;
                switch (iArr4 != null ? (char) 16 : (char) 22) {
                    case 16:
                        int length2 = iArr4.length;
                        int[] iArr5 = new int[length2];
                        int i9 = 0;
                        while (true) {
                            switch (i9 < length2 ? 1 : i8) {
                                case 0:
                                    iArr4 = iArr5;
                                    break;
                                default:
                                    int i10 = $11 + 15;
                                    $10 = i10 % 128;
                                    if (i10 % i5 != 0) {
                                    }
                                    try {
                                        Object[] objArr6 = new Object[1];
                                        objArr6[i8] = Integer.valueOf(iArr4[i9]);
                                        Object obj = o.e.a.s.get(Integer.valueOf(i6));
                                        if (obj == null) {
                                            Class cls = (Class) o.e.a.c(10 - View.resolveSize(i8, i8), (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 8855), 324 - (ExpandableListView.getPackedPositionForGroup(i8) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(i8) == 0L ? 0 : -1)));
                                            byte b2 = (byte) i8;
                                            byte b3 = b2;
                                            Object[] objArr7 = new Object[1];
                                            g(b2, b3, b3, objArr7);
                                            obj = cls.getMethod((String) objArr7[0], Integer.TYPE);
                                            o.e.a.s.put(-1667374059, obj);
                                        }
                                        iArr5[i9] = ((Integer) ((Method) obj).invoke(null, objArr6)).intValue();
                                        i9++;
                                        i5 = 2;
                                        i6 = -1667374059;
                                        i8 = 0;
                                    } catch (Throwable th) {
                                        Throwable cause = th.getCause();
                                        if (cause == null) {
                                            throw th;
                                        }
                                        throw cause;
                                    }
                            }
                        }
                }
                int length3 = iArr4.length;
                int[] iArr6 = new int[length3];
                int[] iArr7 = e;
                if (iArr7 != null) {
                    int i11 = $10 + 47;
                    $11 = i11 % 128;
                    switch (i11 % 2 == 0 ? 'L' : 'b') {
                        case Opcodes.FADD /* 98 */:
                            length = iArr7.length;
                            iArr2 = new int[length];
                            break;
                        default:
                            length = iArr7.length;
                            iArr2 = new int[length];
                            break;
                    }
                    int i12 = 0;
                    while (i12 < length) {
                        try {
                            Object[] objArr8 = new Object[i7];
                            objArr8[0] = Integer.valueOf(iArr7[i12]);
                            Object obj2 = o.e.a.s.get(-1667374059);
                            if (obj2 != null) {
                                iArr3 = iArr7;
                                i4 = length;
                            } else {
                                Class cls2 = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10, (char) (8856 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), View.MeasureSpec.getSize(0) + 324);
                                byte b4 = (byte) 0;
                                byte b5 = b4;
                                iArr3 = iArr7;
                                i4 = length;
                                Object[] objArr9 = new Object[1];
                                g(b4, b5, b5, objArr9);
                                obj2 = cls2.getMethod((String) objArr9[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj2);
                            }
                            iArr2[i12] = ((Integer) ((Method) obj2).invoke(null, objArr8)).intValue();
                            i12++;
                            iArr7 = iArr3;
                            length = i4;
                            i7 = 1;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    iArr7 = iArr2;
                }
                System.arraycopy(iArr7, 0, iArr6, 0, length3);
                gVar.a = 0;
                int i13 = $10 + 79;
                $11 = i13 % 128;
                int i14 = i13 % 2;
                while (gVar.a < iArr.length) {
                    int i15 = $10 + 11;
                    $11 = i15 % 128;
                    int i16 = i15 % 2;
                    cArr[0] = (char) (iArr[gVar.a] >> 16);
                    cArr[1] = (char) iArr[gVar.a];
                    cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
                    cArr[3] = (char) iArr[gVar.a + 1];
                    gVar.e = (cArr[0] << 16) + cArr[1];
                    gVar.c = (cArr[2] << 16) + cArr[3];
                    o.a.g.d(iArr6);
                    int i17 = 0;
                    for (int i18 = 16; i17 < i18; i18 = 16) {
                        gVar.e ^= iArr6[i17];
                        try {
                            Object[] objArr10 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                            Object obj3 = o.e.a.s.get(-2036901605);
                            if (obj3 == null) {
                                obj3 = ((Class) o.e.a.c(Color.alpha(0) + 11, (char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 572 - View.getDefaultSize(0, 0))).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                                o.e.a.s.put(-2036901605, obj3);
                            }
                            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr10)).intValue();
                            gVar.e = gVar.c;
                            gVar.c = intValue;
                            i17++;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    int i19 = gVar.e;
                    gVar.e = gVar.c;
                    gVar.c = i19;
                    gVar.c ^= iArr6[16];
                    gVar.e ^= iArr6[17];
                    int i20 = gVar.e;
                    int i21 = gVar.c;
                    cArr[0] = (char) (gVar.e >>> 16);
                    cArr[1] = (char) gVar.e;
                    cArr[2] = (char) (gVar.c >>> 16);
                    cArr[3] = (char) gVar.c;
                    o.a.g.d(iArr6);
                    cArr2[gVar.a * 2] = cArr[0];
                    cArr2[(gVar.a * 2) + 1] = cArr[1];
                    cArr2[(gVar.a * 2) + 2] = cArr[2];
                    cArr2[(gVar.a * 2) + 3] = cArr[3];
                    try {
                        Object[] objArr11 = {gVar, gVar};
                        Object obj4 = o.e.a.s.get(-331007466);
                        if (obj4 == null) {
                            Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getScrollBarSize() >> 8), (char) (55182 - TextUtils.lastIndexOf("", '0')), TextUtils.lastIndexOf("", '0', 0, 0) + 516);
                            byte b6 = (byte) 0;
                            byte b7 = (byte) (b6 + 1);
                            Object[] objArr12 = new Object[1];
                            g(b6, b7, (byte) (b7 - 1), objArr12);
                            obj4 = cls3.getMethod((String) objArr12[0], Object.class, Object.class);
                            o.e.a.s.put(-331007466, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr11);
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                objArr5[0] = new String(cArr2, 0, i3);
            }
        }, o.ei.c.c()).b(str);
        int i3 = c + Opcodes.DREM;
        e = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    @Override // o.em.d
    final void b(o.eg.b bVar) throws o.eg.d {
        Object obj;
        int i = c + 93;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                super.b(bVar);
                Object[] objArr = new Object[1];
                l(new int[]{1260802738, -58931671, -231080193, 717913513, 392872938, 433536050}, 12 - View.resolveSize(0, 0), objArr);
                obj = objArr[0];
                break;
            default:
                super.b(bVar);
                Object[] objArr2 = new Object[1];
                l(new int[]{1260802738, -58931671, -231080193, 717913513, 392872938, 433536050}, 12 / View.resolveSize(0, 1), objArr2);
                obj = objArr2[0];
                break;
        }
        this.b = bVar.b(((String) obj).intern(), false);
    }

    @Override // o.em.d
    final o.eg.b e() throws o.eg.d {
        int i = c + 1;
        e = i % 128;
        int i2 = i % 2;
        o.eg.b e2 = super.e();
        Object[] objArr = new Object[1];
        l(new int[]{1260802738, -58931671, -231080193, 717913513, 392872938, 433536050}, TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 13, objArr);
        e2.d(((String) objArr[0]).intern(), this.b);
        int i3 = c + 9;
        e = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    @Override // o.em.d
    public final void b() {
        super.b();
        this.b = new Date();
        int i = c + 57;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 37 / 0;
                return;
            default:
                return;
        }
    }

    public final Date j() {
        Date date;
        int i = c + 21;
        int i2 = i % 128;
        e = i2;
        switch (i % 2 != 0 ? ' ' : 'S') {
            case ' ':
                date = this.b;
                int i3 = 28 / 0;
                break;
            default:
                date = this.b;
                break;
        }
        int i4 = i2 + 31;
        c = i4 % 128;
        int i5 = i4 % 2;
        return date;
    }

    public final void a(Date date) {
        int i = c + Opcodes.DMUL;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        this.b = date;
        int i4 = i2 + Opcodes.LNEG;
        c = i4 % 128;
        int i5 = i4 % 2;
    }

    public final List<o.es.a<?>> g() {
        ArrayList arrayList = new ArrayList();
        arrayList.add(new o.es.a(null, o.es.b.b, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.c, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.a, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.d, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.e, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.f, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.j, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.g, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.i, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.h, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.l, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.n, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.f78o, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.m, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.k, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.t, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.p, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.r, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.s, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.q, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.w, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.u, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.y, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.x, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.v, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.A, Boolean.TRUE, this.a));
        arrayList.add(new o.es.a(null, o.es.b.C, Boolean.TRUE, this.a));
        int i = c + 67;
        e = i % 128;
        int i2 = i % 2;
        return arrayList;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(int[] r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 1002
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.j.l(int[], int, java.lang.Object[]):void");
    }
}
