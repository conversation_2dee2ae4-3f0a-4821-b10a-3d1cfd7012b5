package o.ac;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.CreateCardRequestBuilder;
import fr.antelop.sdk.card.CreateCardRequestPanSource;
import java.nio.ByteBuffer;
import o.ea.e;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ac\b.smali */
public final class b implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long u;
    private static int v;
    private static int x;
    private static char[] y;
    private final String a;
    private final int b;
    private final String c;
    private final e d;
    private final String e;
    private final String f;
    private final CreateCardRequestPanSource g;
    private final e h;
    private final String i;
    private final String j;
    private final byte[] k;
    private final String l;
    private final byte[] m;
    private final String n;

    /* renamed from: o, reason: collision with root package name */
    private final byte[] f34o;
    private final byte[] p;
    private final boolean q;
    private final String r;
    private final String s;
    private final String t;
    private final String w;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        v = 0;
        x = 1;
        char[] cArr = new char[2269];
        ByteBuffer.wrap("\"trÕ\u0083rÓæ`\u0003°\u0082Á\u0014\u0011¦¦Å÷C\u0007ÅTbä\u00865\u0012E²\u009a4+C{å\u0088bØîi\u001b¹\u0083Î2\u001eµ,\u008b|6\u008d\u009dÝ\u0011n©¾>ÏÏ\u001fP¨'ù¸\t\u0007Z\u009aê`;øKE\u0094ø%ªu:\u0086\u0086Ö\fgç·mÀç\u0010L¡$ò»\u0002\fS\u008bã.\f¹\\Hí×>\u00adNy\u009fÎ/\u001bxà\u0088wÙ\u008ei\u0019ºfËù\u001bN¤\u009côq\u0005éU@æË7°G\u001d\u0090\u0088 \rqì\u0081>Ò\u0089b\u0016³iÜþl\u0005½\u0098Íz\u001eí®mÿÐ\b®X0é\u009d9\nJ®\u009a9+Ï{P\u0084,Õµe\r¶\u008aÆ)\u0017ý§Lðß\u0001 Q7â\u008c2\u001d\u0002 R\u009d£6óº@\u0002\u0090\u0095ád1û\u0086\u008c×\u0013'¬t1ÄË\u0015SeîºS\u000b\u0001[\u0091¨-ø§IL\u0099ÆîL>ç\u008f\u008fÜ\u0010,§} Í\u0085\"\u0012rãÃ|\u0010\u0006`Ò±e\u0001°VK¦Ü÷%G²\u0094ÍåR5å\u008a7ÚÚ+B{ëÈ`\u0019\u001bi¶¾#\u000e¦_G¯\u0095ü\"L½\u009dÂòUB®\u00933ãÑ0F\u0080ÆÑ{&\u0005v\u009bÇ6\u0017¡d\u0005´\u0092\u0005dUûª\u0087û\u001eK¦\u0098!è\u00829G\u0089ìÞv/\u0007\u007f\u0094Ì+\u001c¼mG½ÖÇe\u0097ófL6Û\u0085$U»$\u0006ô\u009cCä\u0012yâä±V\u0001¦Ð: \u0090\u007f\u001bÎq\u009eÛmP=Ø\u008c'\\°+\u0017û\u0095J¥\u0019xéÐ¸F\b±çu·\u0086\u0006\u001aÕk¥átDÄÜ\u0093+cõ2\u0007\u0082\u0090Qñ bðÀOP\u001f«îu¾Ô\rUÜd¬û{AË\u0095\u009asjá9E\u0089\u0096Xí7t\u0087×,î|4\u008d\u008dÝ\u001cnú¾_ÏÜ\u001fW¨-ù°\t\u0007Z\u009eêH;úKJ\u0094Ö%¼u7\u0086\u009dÖ0gç·\u007fÀÆ\u0010\u001e¡iò¿\u0002\u0000S\u009cãe\fý\\Zí\u0099>¼N7\u009f\u008d/\u001cxï\u0088pÙÇi\\º-,©|4\u008d\u008dÝ\u001cnú¾_ÏÜ\u001fW¨-ù°\t\u0007Z\u009eêH;úKJ\u0094Ö%¼u7\u0086\u009dÖ0gç·\u007fÀÆ\u0010Y¡iò´\u0002\u001cS\u008aã}\f¹\\KíÜ>éN;\u009f\u008c/\rxþ\u0088|ÙÌiWºiËé\u001bI¤\u0098ôg\u0005ýU\tæ\u008c7ûGm\u0090Û Aq±\u00819ÒÅbV³'Ü¾,©|0\u008d\u009aÝ\nnü¾|ÏÛ\u001fz¨(ù«\t\rZ°êm;ùK\t\u0094Ô%¼u*\u0086\u009dÖYgê·vÀÇ\u0010M¡(ò°\u0002\u0007SÙãk\fü\\]íÎ>¬N<\u009f\u0087/Yx¹\u00889ÙÈiWº-Ëù\u001bZ¤Ëô)\u0005úUAæØ7»,\u008b|6\u008d\u009dÝ\u0011n©¾yÏË\u001fP¨'ù¹\tIZ\u0098êg;ýK\t\u0094Ù%«u0\u0086\u0087Ö5gì·wÀÎ\u0010M¡!ò¹\u0002IS\u009fã`\fü\\EíÝ>ºNy\u009f\u008d/\u001cxï\u0088pÙÇi\\º-,©|;\u008d\u0080Ý\u0017né¾9ÏÄ\u001fL¨:ù\u00ad\tIZ\u009aêf;÷K]\u0094Ø% u7\u0086ÉÖ\u001bgì·mÀÞ\u0010\\¡,ò·\u0002ISÉã)\fø\\GíÝ>éNa\u009fÉ/\u001axá\u0088xÙÛ\u0006\u0010V\u0082§9÷®D|\u0094Åå~5ç\u0082\u0084Ó\b#°p`ÀÝ\u0011Uaã¾t\u000fP_\u0082¬5üàMR\u009dÅêd:÷\u008b\u0095Ø\u0005(¾y`É\u0080&\u0000vñÇn\u0014\u0014dÀµh,©|5\u008d\u0088Ý\nný¾]ÏÀ\u001f^¨ ù\u00ad\t\u001aZ\u0099ê);ôK\\\u0094Ê%½uy\u0086\u008aÖ\u0016gç·mÀÈ\u0010P¡'òù\u0002\u000bS\u009cã}\fî\\LíÜ>§Ny\u009fÙ/Yxè\u0088wÙÍi\u0019º}Ëù\u001b\n¤\u0091ôh\u0005ë,\u009d|1\u008d\u008cÝYnÚ¾]Ïâ\u001f\u0019¨-ù¶\t\fZ\u008aêg;¾K]\u0094\u0099%ºu,\u0086\u0099Ö\tgæ·kÀÝ\u0010\u0019¡*òµ\u0002\fS\u0098ã{\f¹\\IíÉ>¨N7\u009f\u0089/Yxà\u0088wÙÃi\\º*Ë\u00ad\u001b\u0000¤\u0096ôg\u0005·,\u008b|6\u008d\u009dÝ\u0011n©¾yÏÙ\u001fX¨'ù¹\tIZ\u0098êg;ýK\t\u0094Ù%ºu<\u0086\u008aÖ\fgû·|Àù\u0010X¡'ò¹\u0002IS\u0098ã{\fü\\\tíÝ>¬N?\u009f\u0080/\u0017xì\u0088},©|)\u008d\u0088Ý\u0017nÚ¾vÏÜ\u001fK¨*ù¼\t\tZÙêd;ìKZ\u0094Í%éu;\u0086\u008cÖYgí·|ÀÏ\u0010P¡'ò¼\u0002\r¦ä©¥ù#\b\u0095X\u0013ëä;cJæ\u009aG-8b®2/Ã\u0090,©|y\u008d\u0084Ý\fnú¾mÏ\u0089\u001fZ¨&ù·\t\u001dZ\u0098ê`;÷K\t\u0094Û%¬u-\u0086\u009eÖ\u001cgì·wÀ\u0089\u001a\u0083JR»íëwXÃÒ\u000f\u0082Üsg#þ\u0090\u001dô¡¤\rU°\u0005e¶æfa\u0017ÞÇ%p\u0011!\u008aÑ0\u0082¶2[ã\u0082\u0093aL¥ý\u0086\u00ad\u0010^¥\u000e5¿ÚoW\u0018áÈ%y\u0016*\u0089Ú0\u008b¤;GÔ\u0085\u0084u5ææ\u0083\u0096\u001dGç÷% \u0095PL\u0001û±ob\u0010\u0013\u0086Ã!|¬,ZÝË\u008d;,\u0086|7\u008d\u008cÝYnæ¾\u007fÏ\u0089\u001fY¨*ù¯\t\u0011ZËêi;¹KF\u0094Ë%éu9\u0086\u009aÖ\u001cgê·lÀÛ\u0010\\¡\nò¯\u0002\u0011SËãi\f¹\\DíÌ>ºN-\u009fÉ/\u001bxì\u00889ÙÍi\\º/Ë°\u001b\u0007¤\u009côm sðê\u0001[QÖâ=2¬C\u001f\u0093\u0088$ìue\u0085Í×\u000b\u0087§v\u001a&Ï\u0095LEË4tä\u008fS»\u0002 ò\u009a¡\u001c\u0011ñÀ(°Ëo\u000fÞ,\u008eº}\u000f-\u009f\u009cpLý;Kë\u008fZº\t!ù\u009b¨O\u0018ë÷`§\u009f\u0016JÅ1µ«d_Ô\u008a\u0083qsì\"M\u0092ÖA¯0;à\u0096_\u0000\u000fñþ#®\u009f\u001dLÌ>¼¡k\u0011Û\u0080\u008akz¯)V\u0099ÁHµ'*\u0097\u009cF\u001b6¿ålUÉ\u0004Wóm*üzo\u008bÊÛThî¸,ÉÜ\u0019\b®sÿé\u000fO\\Âì{=¸M\\\u0092\u0082#ósx\u0080\u009cÐOa³±\"Æ\u0088\u0016\r§uôâ\u0004\u001cUÃå2\n Z\u0005ëÌ8òHy\u0099Ñ)N~¹\u008e>ß\u008fkÃ;VÊû\u009ac)\u008aù\u0001\u0088ºX\u0017ïB¾ÇNf\u001dó\u00adC|\u009e\f6Ó b×2\u0013Áá\u0091v Ãð\u0017\u0087¦W5æJµÝEf\u0014÷,©|<\u008d\u0091Ý\tnà¾kÏÐ\u001f}¨(ù\u00ad\t\fZ\u0099ê);ðKZ\u0094\u0099% u7\u0086ÉÖ\rgá·|À\u0089\u0010I¡(òª\u0002\u001dSÙãf\fë\\\tíÝ>¦N<\u009f\u009a/Yxç\u0088vÙÝi\u0019º/Ë¶\u001b\u0005¤\u0095ôf\u0005îU\tæà7\u0090G\u0014\u0090¤ Yqï\u0081vÒÛbT³(Ü\u00adYü\teøÏ¨_\u001b©Ë)º\u008ej(Ý}\u008cø|]/Ì\u009f|N¡>\tá\u009fPè\u0000,óß£C\u0012²Â8µ\u009de\u0005Ôr\u0087¬w^&É\u0096(y»)\u0019\u0098\u0089Kò;,ê\u008cZ\f\r½ý\"¬\u0098\u001cLÏ-¾¼n\u000eÑ\u0098\u0081|p¯ \u0014\u0093\u008dBîÍ:\u009d©l\u001b<\u0098\u008f~_â.UþÆI¾\u0018/è\u0088»$\u000bûÚgªßuJÄz\u0094§g\u000f7\u0099\u0086nVª!YñÅ@´\u0013>ã\u009b²\u0003\u0002ôí*½Ø\fOß.¯½~\u001fÎ\u008f\u0099tiª8\n\u0088\u008a[»*$ú\u009eEJ\u0015«ä:´\u008a\u0007\nÖ9¦¢q\u001bÁ\u0098,\u009f|\r\u008dº,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099ê\u007f;íKZ\u0094ü%§u:\u0086¹Ö\u0018gð·tÀÌ\u0010W¡=ò\u0090\u0002\u0007S\u008aã}\fë\\\\íÔ>¬N7\u009f\u009d/\u0019x¥\u00889ÙÙiXº'Ëù\u001b\u0000¤\u008aô)\u0005øUEæË7¬G8\u0090\u008d \u0000q©\u0081}ÒÌb_³ Ü·l\f½\u009d,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099ê\u007f;íKZ\u0094ü%§u:\u0086¹Ö\u0018gð·tÀÌ\u0010W¡=ò\u0090\u0002\u0007S\u008aã}\fë\\\\íÔ>¬N7\u009f\u009d/\u0019x©\u0088pÙÏi\u0019º=Ë±\u001b\f¤Ùôi\u0005ûU@æ×7©Gy\u0090\u008d \u0016qì\u0081jÒ\u0089bW³&Ü\u00adlI½\u0094Íh\u001eí®JÿÑ\béX\u000fé 9*JÈ\u009a9+Ë{P\u0084'±îáh\u0010Ú@Mó\u008b#0R\u008d\u0082.5odç\u0094CÇÛw ¦ªÖ'\t\u0090¸ýèj\u001bÜKKú£*;]\u0080\u008d\n<no¾\u009fCÎË~=\u0091ªÁNp\u009c£ëÓ>\u0002Ì²[åº\u0015)D\u008bô\u001b'`V¾\u0086\u001e9\u009ei/\u0098°È\n{Þª¶Ú/\r\u0097½\fìî\u001c2O\u0081ÿ\u0010.i·Wçå\u0016ZFÊõ;%°TT\u0084\u00973ñbp\u0092\u0094ÁDq¹  Ð\u0091\u000f\u0017¾Rîñ\u001dZMÀü=,ª[\u0013\u008b¥:÷ig\u0099ÛÈQxº\u00970Ç½v\n¥rÕë\u0004T´\u0088ãt\u0013¤\u0019¼Ih¸ÝèX[½\u008b,úÜ*\r\u009dpÌþ<YoÍß8\u000eµ~\\¡\u0088\u0010ù@j³ÕãBR¹\u0082(,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099êd;ýKL\u0094Ê%\u008fu,\u0086\u0087Ö\u001dgà·wÀÎ\u0010x¡*òº\u0002\u0006S\u008cãg\fí\\`í×>¯N6\u009f\u0089/Ux©\u0088iÙÈiWºiË°\u001b\u001a¤Ùôh\u0005õU[æÜ7¨G=\u0090\u0090 Yqí\u0081|ÒÏbP³'Ü¼l\r²×âe\u0013ÚCJð» 0QÔ\u0081\u00176qgð\u0097\u0014ÄÄt9¥ Õ\u0011\n\u0097»Òëq\u0018ÚH@ù½)*^\u0093\u008e%?wlç\u009c[ÍÑ}:\u0092°Â=s\u008a òÐk\u0001Ô±\u0004æ½\u0016\"GÔ÷\u0010$|Uá\u0085\u0014:Äj6\u009b\u00adË\u001ax\u0084©´Ù`\u000eÛ¾Aï§\u001fdL\u009aü\u000b-`B¤òY#ÅS \u0080§0\u001caÄ\u0096ÙÆEwç§pÔ\u0091\u0004\u0016µ·å%\u001aFKÀû\u0014(ÆX=\u0089ªTÔ\u0004Mõü¥z,©|4\u008d\u008dÝ\u001cnú¾MÏÈ\u001fO¨)ùù\t\u0004Z\u008cêz;íK\t\u0094Û%¬uy\u0086\u008bÖ\u001cgý·nÀÌ\u0010\\¡'òù\u0002YSÙãh\f÷\\Mí\u0099>ûNi\u009fÝ/Ax©\u0088uÙÆiWº.,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099êd;ýKL\u0094Ê%\u009du8\u0086\u009fÖ\u0019g¥·9ÀÉ\u0010T¡-ò¼\u0002\u001aS¿ã|\f÷\\MíÐ>§N>\u009f¨/\u001axê\u0088vÙÜiWº=Ë\u0090\u001b\u0007¤\u009fôf\u0005ùU\tæÐ7ºGy\u0090\u0087 \u0016qý\u00819ÒÍb\\³/Ü°l\u0007½\u009cÍm,©|\u001a\u008d\u0088Ý\u0017nç¾vÏÝ\u001f\u0019¨:ù¼\t\u001dZÙêi;ôKM\u0094Ü%ºu\t\u0086\u009cÖ\ngá·XÀÊ\u0010Z¡&ò¬\u0002\u0007S\u008dã[\fü\\JíÜ> N)\u009f\u009d/\u0019x©\u0088pÙÏi\u0019º)Ë´\u001b\r¤\u009côz\u0005ßU\\æ×7\u00adG0\u0090\u0087 \u001eqÈ\u0081zÒÊbV³<Ü·l\u001d½°Íg\u001eÿ®FÿÙ\béX0é\u009a9YJè\u009au+Û{\\\u0084(Õ½e\u0010¶ÙÆm\u0017ü§OðÐ\u0001§Q<â\u008d,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099êd;ýKL\u0094Ê%\u0099u,\u0086\u009aÖ\u0011gÈ·zÀÊ\u0010V¡<ò·\u0002\u001dS«ãl\fú\\LíÐ>¹N-\u009f\u0089/Ux©\u0088y,\u008a|8\u008d\u0087Ý\u0017næ¾mÏ\u0089\u001fJ¨,ù\u00ad\tIZ\u0099êd;ýKL\u0094Ê%\u0099u,\u0086\u009aÖ\u0011gÈ·zÀÊ\u0010V¡<ò·\u0002\u001dS«ãl\fú\\LíÐ>¹N-\u009f\u0089/Ux©\u0088iÙÈiWºiË°\u001b\u001a¤Ùôh\u0005õU[æÜ7¨G=\u0090\u0090 Yqí\u0081|ÒÏbP³'Ü¼l\r\u001fñOC¾üîl]\u009d\u008d\u0016üò,1\u009bWÊÖ:2iâÙ\u001f\b\u0086x7§±\u0016âFWµáåjT³\u0084\u0001ó±#-\u0092GÁÌ1f`ÐÐ\u0017?\u0081o7Þ«\rÂ}V¬ò\u001c\"K\u009b»\u0004êòZ6\u0089ZøÇ(2\u0097âÇ\u00106\u008bf<Õ¢\u0004\u0092tF£ý\u0013gB\u0081²Bá¼Q-\u0080Fï\u0082_\u007f\u008eãþ\u0006-\u0081\u009d:Ìâ;ÿkcÚÁ\nVy·©0\u0018\u0091H\u0003·`ææV2\u0085àõ\u001b$\u008c,©|4\u008d\u008dÝ\u001cnú¾IÏÜ\u001fJ¨!ù\u0098\t\nZ\u009aêf;ìKG\u0094Í%\u009bu<\u0086\u008aÖ\u001cgà·iÀÝ\u0010Y¡iò´\u0002\u001cS\u008aã}\f¹\\KíÜ>éN;\u009f\u008c/\rxþ\u0088|ÙÌiWºiËé\u001bI¤\u0098ôg\u0005ýU\tæ\u008f7ýGy\u0090\u0085 \u0016qç\u0081~@)\u0010\u009bá$±´\u0002EÒÎ£*séÄ\u008f\u0095\u000eeê6:\u0086ãW~'ÏøWI#\u0019»êjº¾\u000bKÛÎ¬k|úÍÆ\u009eZnº?;\u008fÄ`\u001a0ã\u0081iRJ\"\u009bó&C¨\u0014OäÛµn\u0005ãÖÊ§\u001ew¯È<\u0098ÃiT9ï\u008a~H*\u0018\u0098é'¹·\nFÚÍ«){êÌ\u008c\u009d\rmé>9\u008eà_}/ÌðTA \u0011¸âi²½\u0003HÓÍ¤htùÅÅ\u0096Yf©,\u0080|\u001d\u008d¬Ý4nÀ¾X\u0099\u009dÉ\u00048¹h(ÛÐ\u000bDzüªN\u001d\u0014L\u009d¼5ï¨_O\u008eÈþy!Î\u0090\u009cÀ\u001f3¹c\u0004ÒÓ\u0002Kuò¥\u007f\u0014\u0010G\u008c·)æ¤VR¹Ãé}X\u00ad\u008b\u0090û\u0018*®\u009a9Í\u009d=OløÜ-\u000f\u001f~\u0088®)\u0011ºAX°ÈàsS\u00ad\u0082ÍòM%¼\u0095#ÄÙ4\rg¥×<\u0006DißÙ}\b¡xR«Ã\u001bz,©|0\u008d\u008dÝ\u001cnä¾pÏÈ\u001fx¨<ù\u00ad\t\u0001Zºêf;ýKL\u0094Ù%éu4\u0086\u009cÖ\ngý·9ÀÊ\u0010V¡'ò\u00ad\u0002\bS\u0090ãg\f¹\\KíÜ>½N.\u009f\u008c/\u001cxç\u00889Ù\u0099i\u0019º(Ë·\u001b\r¤Ùô=\u0005©U\u0010æ\u008f7éG5\u0090\u0086 \u0017qîkó;jÊ×\u009aF)¾ù*\u0088\u0092X\"ïf¾÷N[\u001dà\u00ad<|§\f\u0016Ó\u0083b³2qÁÖ\u0091R ¦ð*\u0087\u0081W\u0006æ`µ£EG\u0014Ì¤sK§\u001b\u0016ª\u0085yú\tmØÖh\u0003?³Ï*\u009e\u0097.\u0006ý~\u008cê\\Rãà³:B³\u0012\u001b¡\u0086pá\u0000f××g`6²Æ1\u0095\u0097%*ô}\u009bå+\\úÑ\u008a>Y¢é\u0007¸\u008aOü\u001fm®Ó,©|)\u008d\u0088Ý\u0017né¾9ÏÆ\u001fK¨iù¹\t\u001dZ\u008aêy;¹KM\u0094Ø%½u8\u0086\u0089ÖYgä·lÀÚ\u0010M¡iò»\u0002\fSÙãm\fü\\OíÐ>§N<\u009f\u008d,©|5\u008d\u0088Ý\nný¾]ÏÀ\u001f^¨ ù\u00ad\t\u001aZ\u0099ê);ôK\\\u0094Ê%½uy\u0086\u008bÖ\u001cg©·}ÀÌ\u0010_¡ ò·\u0002\fS\u009d,©|;\u008d\u0080Ý\u0017né¾9ÏÄ\u001fL¨:ù\u00ad\tIZ\u009bêl;¹KM\u0094Ü%¯u0\u0086\u0087Ö\u001cgí".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 2269);
        y = cArr;
        u = 2436704127252986969L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void A(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 + 4
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.ac.b.$$a
            int r8 = r8 + 102
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r7
            goto L34
        L16:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1a:
            byte r4 = (byte) r7
            int r5 = r3 + 1
            r1[r3] = r4
            int r9 = r9 + 1
            if (r5 != r8) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.b.A(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{75, 105, 70, 99};
        $$b = Opcodes.DREM;
    }

    private b(String str, int i, String str2, String str3, e eVar, CreateCardRequestPanSource createCardRequestPanSource, String str4, String str5, e eVar2, String str6, String str7, String str8, byte[] bArr, byte[] bArr2, byte[] bArr3, String str9, byte[] bArr4, String str10, boolean z, String str11, String str12) {
        this.e = str;
        this.b = i;
        this.a = str2;
        this.c = str3;
        this.d = eVar;
        this.g = createCardRequestPanSource;
        this.f = str4;
        this.j = str5;
        this.h = eVar2;
        this.i = str6;
        this.n = str7;
        this.l = str8;
        this.m = bArr;
        this.k = bArr2;
        this.f34o = bArr3;
        this.s = str9;
        this.p = bArr4;
        this.r = str10;
        this.q = z;
        this.t = str11;
        this.w = str12;
    }

    public b(CreateCardRequestBuilder createCardRequestBuilder) {
        this(createCardRequestBuilder.getBin(), createCardRequestBuilder.getBinLength(), createCardRequestBuilder.getLastDigits(), createCardRequestBuilder.getPan(), createCardRequestBuilder.getSecurePan(), createCardRequestBuilder.getPanSource(), createCardRequestBuilder.getExpiryDate(), createCardRequestBuilder.getCvx2(), createCardRequestBuilder.getSecureCvx2(), createCardRequestBuilder.getIssuerCardId(), createCardRequestBuilder.getIssuerData(), createCardRequestBuilder.getCardholderName(), createCardRequestBuilder.getVtsEncPaymentInstrument(), createCardRequestBuilder.getMdesTav(), createCardRequestBuilder.getMdesFundingAccountInfo(), createCardRequestBuilder.getMdesPushAccountReceipt(), createCardRequestBuilder.getIdemiaCipheredCardInformation(), createCardRequestBuilder.getIdemiaAuthCode(), createCardRequestBuilder.requiresTermsAndConditionsApproval(), createCardRequestBuilder.getFinancialAccountNumber(), createCardRequestBuilder.getFinancialAccountLabel());
    }

    final String d() {
        int i = x + 23;
        v = i % 128;
        switch (i % 2 != 0 ? 'C' : (char) 1) {
            case 1:
                return this.e;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    final int b() {
        int i = x + 43;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        int i4 = this.b;
        int i5 = i2 + 81;
        x = i5 % 128;
        int i6 = i5 % 2;
        return i4;
    }

    final String a() {
        String str;
        int i = x + 65;
        int i2 = i % 128;
        v = i2;
        switch (i % 2 != 0 ? 'Q' : '6') {
            case Opcodes.ISTORE /* 54 */:
                str = this.a;
                break;
            default:
                str = this.a;
                int i3 = 75 / 0;
                break;
        }
        int i4 = i2 + Opcodes.LSHL;
        x = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    final String c() {
        int i = v + 27;
        int i2 = i % 128;
        x = i2;
        Object obj = null;
        switch (i % 2 == 0 ? (char) 29 : 'R') {
            case 29:
                obj.hashCode();
                throw null;
            default:
                String str = this.c;
                int i3 = i2 + 49;
                v = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        throw null;
                    default:
                        return str;
                }
        }
    }

    final e e() {
        int i = v;
        int i2 = i + 27;
        x = i2 % 128;
        int i3 = i2 % 2;
        e eVar = this.d;
        int i4 = i + Opcodes.LREM;
        x = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return eVar;
        }
    }

    public final CreateCardRequestPanSource j() {
        int i = x + 13;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                return this.g;
            default:
                throw null;
        }
    }

    final String f() {
        int i = x;
        int i2 = i + 17;
        v = i2 % 128;
        int i3 = i2 % 2;
        String str = this.f;
        int i4 = i + 53;
        v = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return str;
            default:
                throw null;
        }
    }

    public final String i() {
        int i = x + Opcodes.LUSHR;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                return this.j;
            default:
                throw null;
        }
    }

    final e h() {
        int i = x + Opcodes.DMUL;
        v = i % 128;
        switch (i % 2 != 0 ? (char) 0 : '\r') {
            case 0:
                throw null;
            default:
                return this.h;
        }
    }

    final String g() {
        int i = x + 99;
        int i2 = i % 128;
        v = i2;
        switch (i % 2 == 0) {
            case true:
                String str = this.i;
                int i3 = i2 + 29;
                x = i3 % 128;
                switch (i3 % 2 == 0 ? 'U' : '%') {
                    case Opcodes.CASTORE /* 85 */:
                        int i4 = 10 / 0;
                        return str;
                    default:
                        return str;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    final String o() {
        int i = v;
        int i2 = i + 65;
        x = i2 % 128;
        int i3 = i2 % 2;
        String str = this.l;
        int i4 = i + 81;
        x = i4 % 128;
        switch (i4 % 2 == 0 ? '@' : 'c') {
            case Opcodes.DADD /* 99 */:
                return str;
            default:
                int i5 = 17 / 0;
                return str;
        }
    }

    final String l() {
        String str;
        int i = v + Opcodes.LNEG;
        int i2 = i % 128;
        x = i2;
        switch (i % 2 == 0) {
            case false:
                str = this.n;
                break;
            default:
                str = this.n;
                int i3 = 45 / 0;
                break;
        }
        int i4 = i2 + 95;
        v = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    final byte[] m() {
        int i = x;
        int i2 = i + 89;
        v = i2 % 128;
        int i3 = i2 % 2;
        byte[] bArr = this.m;
        int i4 = i + 57;
        v = i4 % 128;
        switch (i4 % 2 != 0 ? 'E' : ',') {
            case 'E':
                throw null;
            default:
                return bArr;
        }
    }

    final byte[] n() {
        byte[] bArr;
        int i = x;
        int i2 = i + 71;
        v = i2 % 128;
        switch (i2 % 2 != 0 ? 'S' : 'M') {
            case Opcodes.AASTORE /* 83 */:
                bArr = this.k;
                int i3 = 74 / 0;
                break;
            default:
                bArr = this.k;
                break;
        }
        int i4 = i + 33;
        v = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return bArr;
            default:
                int i5 = 58 / 0;
                return bArr;
        }
    }

    final byte[] k() {
        int i = x + 87;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                return this.f34o;
            default:
                int i2 = 75 / 0;
                return this.f34o;
        }
    }

    final String p() {
        int i = x;
        int i2 = i + Opcodes.LSHR;
        v = i2 % 128;
        int i3 = i2 % 2;
        String str = this.s;
        int i4 = i + 95;
        v = i4 % 128;
        switch (i4 % 2 != 0 ? '!' : '6') {
            case Opcodes.ISTORE /* 54 */:
                return str;
            default:
                int i5 = 59 / 0;
                return str;
        }
    }

    public final byte[] q() {
        int i = x;
        int i2 = i + 87;
        v = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                byte[] bArr = this.p;
                int i3 = i + 19;
                v = i3 % 128;
                int i4 = i3 % 2;
                return bArr;
        }
    }

    public final String t() {
        int i = v + 23;
        x = i % 128;
        switch (i % 2 == 0) {
            case false:
                return this.r;
            default:
                int i2 = 89 / 0;
                return this.r;
        }
    }

    public final boolean r() {
        int i = v;
        int i2 = i + 109;
        x = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.q;
        int i4 = i + 23;
        x = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return z;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String s() {
        int i = x;
        int i2 = i + Opcodes.DDIV;
        v = i2 % 128;
        int i3 = i2 % 2;
        String str = this.t;
        int i4 = i + 33;
        v = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String v() {
        int i = x + Opcodes.DDIV;
        int i2 = i % 128;
        v = i2;
        switch (i % 2 != 0 ? 'X' : 'C') {
            case Opcodes.POP2 /* 88 */:
                throw null;
            default:
                String str = this.w;
                int i3 = i2 + Opcodes.DMUL;
                x = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:101:0x055e, code lost:
    
        if (r2 != o.ej.c.b) goto L132;
     */
    /* JADX WARN: Code restructure failed: missing block: B:102:0x0560, code lost:
    
        r8 = r24.m;
        r12 = new java.lang.Object[1];
        z((char) (40264 - (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1))), android.graphics.Color.argb(0, 0, 0, 0) + 1249, 58 - (android.os.SystemClock.uptimeMillis() > 0 ? 1 : (android.os.SystemClock.uptimeMillis() == 0 ? 0 : -1)), r12);
        d(r8, 8192, ((java.lang.String) r12[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:127:0x07a4, code lost:
    
        if (r24.f34o == null) goto L182;
     */
    /* JADX WARN: Code restructure failed: missing block: B:128:0x08c0, code lost:
    
        r5 = new java.lang.Object[1];
        z((char) (android.widget.ExpandableListView.getPackedPositionChild(0) + 1), 1609 - (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), 84 - (android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)), r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:129:0x08e7, code lost:
    
        throw d(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0046, code lost:
    
        if (r24.a == null) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:130:0x07ae, code lost:
    
        if (r5 != null) goto L194;
     */
    /* JADX WARN: Code restructure failed: missing block: B:131:0x07b0, code lost:
    
        if (r6 == false) goto L185;
     */
    /* JADX WARN: Code restructure failed: missing block: B:132:0x07b2, code lost:
    
        r5 = '@';
     */
    /* JADX WARN: Code restructure failed: missing block: B:133:0x07b7, code lost:
    
        switch(r5) {
            case 17: goto L189;
            default: goto L187;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:134:0x07ba, code lost:
    
        r5 = new java.lang.Object[1];
        z((char) (android.view.ViewConfiguration.getTouchSlop() >> 8), 1730 - (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), android.text.TextUtils.lastIndexOf("", '0', 0, 0) + 60, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:135:0x07e2, code lost:
    
        throw d(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:137:0x07e5, code lost:
    
        if (r2 != o.ej.c.c) goto L192;
     */
    /* JADX WARN: Code restructure failed: missing block: B:138:0x07e7, code lost:
    
        r9 = new java.lang.Object[1];
        z((char) ((android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16) + 30800), 1503 - android.graphics.drawable.Drawable.resolveOpacity(0, 0), android.view.KeyEvent.keyCodeFromString("") + 4, r9);
        r5 = ((java.lang.String) r9[0]).intern();
        r2 = r24.s;
        r12 = new java.lang.Object[1];
        z((char) android.graphics.Color.red(0), 1867 - (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16), android.text.TextUtils.indexOf("", "") + 54, r12);
        c(r2, 0, 64, ((java.lang.String) r12[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:139:0x0832, code lost:
    
        r5 = new java.lang.Object[1];
        z((char) (13179 - android.text.TextUtils.getOffsetAfter("", 0)), android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0) + 1790, (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 78, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:140:0x085b, code lost:
    
        throw d(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:141:0x07b5, code lost:
    
        r5 = 17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:142:0x085c, code lost:
    
        r0 = new java.lang.StringBuilder();
        r8 = new java.lang.Object[1];
        z((char) (android.os.Process.myTid() >> 22), (android.view.ViewConfiguration.getZoomControlsTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getZoomControlsTimeout() == 0 ? 0 : -1)) + 1691, 38 - (android.view.ViewConfiguration.getMaximumFlingVelocity() >> 16), r8);
        r0 = r0.append(((java.lang.String) r8[0]).intern()).append(r5);
        r6 = new java.lang.Object[1];
        z((char) (13654 - (android.os.Process.getElapsedCpuTime() > 0 ? 1 : (android.os.Process.getElapsedCpuTime() == 0 ? 0 : -1))), 1344 - (android.view.ViewConfiguration.getLongPressTimeout() >> 16), (android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 22, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:143:0x08bf, code lost:
    
        throw d(r0.append(((java.lang.String) r6[0]).intern()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:149:0x07ac, code lost:
    
        if (r24.f34o == null) goto L182;
     */
    /* JADX WARN: Code restructure failed: missing block: B:190:0x058e, code lost:
    
        r5 = new java.lang.Object[1];
        z((char) android.view.View.MeasureSpec.makeMeasureSpec(0, 0), ((byte) android.view.KeyEvent.getModifierMetaStateMask()) + 1177, 73 - (android.view.ViewConfiguration.getScrollBarSize() >> 8), r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:191:0x05b2, code lost:
    
        throw d(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:193:0x055a, code lost:
    
        if (r6 == false) goto L129;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x0535, code lost:
    
        if (r6 == false) goto L129;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x05b3, code lost:
    
        r5 = new java.lang.Object[1];
        z((char) android.text.TextUtils.getOffsetAfter("", 0), (android.view.ViewConfiguration.getFadingEdgeLength() >> 16) + 1116, 59 - android.view.MotionEvent.axisFromString(""), r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x05d7, code lost:
    
        throw d(((java.lang.String) r5[0]).intern());
     */
    /* JADX WARN: Removed duplicated region for block: B:213:0x041b  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void x() throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 3068
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.b.x():void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void z(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 590
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ac.b.z(char, int, int, java.lang.Object[]):void");
    }
}
