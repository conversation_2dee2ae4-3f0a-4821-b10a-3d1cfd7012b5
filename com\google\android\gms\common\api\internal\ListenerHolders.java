package com.google.android.gms.common.api.internal;

import android.os.Looper;
import com.google.android.gms.common.api.internal.ListenerHolder;
import com.google.android.gms.common.internal.Preconditions;
import java.util.Collections;
import java.util.Iterator;
import java.util.Set;
import java.util.WeakHashMap;
import java.util.concurrent.Executor;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\api\internal\ListenerHolders.smali */
public class ListenerHolders {
    private final Set zaa = Collections.newSetFromMap(new WeakHashMap());

    public static <L> ListenerHolder<L> createListenerHolder(L l, Looper looper, String listenerType) {
        Preconditions.checkNotNull(l, "Listener must not be null");
        Preconditions.checkNotNull(looper, "Looper must not be null");
        Preconditions.checkNotNull(listenerType, "Listener type must not be null");
        return new ListenerHolder<>(looper, l, listenerType);
    }

    public static <L> ListenerHolder.ListenerKey<L> createListenerKey(L l, String listenerType) {
        Preconditions.checkNotNull(l, "Listener must not be null");
        Preconditions.checkNotNull(listenerType, "Listener type must not be null");
        Preconditions.checkNotEmpty(listenerType, "Listener type must not be empty");
        return new ListenerHolder.ListenerKey<>(l, listenerType);
    }

    public final ListenerHolder zaa(Object obj, Looper looper, String str) {
        Set set = this.zaa;
        ListenerHolder createListenerHolder = createListenerHolder(obj, looper, "NO_TYPE");
        set.add(createListenerHolder);
        return createListenerHolder;
    }

    public final void zab() {
        Iterator it = this.zaa.iterator();
        while (it.hasNext()) {
            ((ListenerHolder) it.next()).clear();
        }
        this.zaa.clear();
    }

    public static <L> ListenerHolder<L> createListenerHolder(L l, Executor executor, String listenerType) {
        Preconditions.checkNotNull(l, "Listener must not be null");
        Preconditions.checkNotNull(executor, "Executor must not be null");
        Preconditions.checkNotNull(listenerType, "Listener type must not be null");
        return new ListenerHolder<>(executor, l, listenerType);
    }
}
