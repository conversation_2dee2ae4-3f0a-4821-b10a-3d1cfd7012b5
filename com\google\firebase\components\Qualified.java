package com.google.firebase.components;

import java.lang.annotation.Annotation;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\components\Qualified.smali */
public final class Qualified<T> {
    private final Class<? extends Annotation> qualifier;
    private final Class<T> type;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\components\Qualified$Unqualified.smali */
    private @interface Unqualified {
    }

    public Qualified(Class<? extends Annotation> qualifier, Class<T> type) {
        this.qualifier = qualifier;
        this.type = type;
    }

    public static <T> Qualified<T> unqualified(Class<T> type) {
        return new Qualified<>(Unqualified.class, type);
    }

    public static <T> Qualified<T> qualified(Class<? extends Annotation> qualifier, Class<T> type) {
        return new Qualified<>(qualifier, type);
    }

    public boolean equals(Object o2) {
        if (this == o2) {
            return true;
        }
        if (o2 == null || getClass() != o2.getClass()) {
            return false;
        }
        Qualified<?> qualified = (Qualified) o2;
        if (!this.type.equals(qualified.type)) {
            return false;
        }
        return this.qualifier.equals(qualified.qualifier);
    }

    public int hashCode() {
        int result = this.type.hashCode();
        return (result * 31) + this.qualifier.hashCode();
    }

    public String toString() {
        if (this.qualifier == Unqualified.class) {
            return this.type.getName();
        }
        return "@" + this.qualifier.getName() + " " + this.type.getName();
    }
}
