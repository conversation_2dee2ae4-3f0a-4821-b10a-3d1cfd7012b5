package com.ryltsov.alex.plugins.file.opener;

import android.content.ContentResolver;
import android.net.Uri;
import android.webkit.MimeTypeMap;
import com.getcapacitor.Plugin;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "FileOpener")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes3\com\ryltsov\alex\plugins\file\opener\FileOpenerPlugin.smali */
public class FileOpenerPlugin extends Plugin {
    /* JADX WARN: Code restructure failed: missing block: B:66:0x0114, code lost:
    
        if (r4.trim().equals("") != false) goto L47;
     */
    /* JADX WARN: Removed duplicated region for block: B:13:0x00bc A[Catch: Exception -> 0x00cc, ActivityNotFoundException -> 0x00d7, TRY_ENTER, TRY_LEAVE, TryCatch #7 {ActivityNotFoundException -> 0x00d7, Exception -> 0x00cc, blocks: (B:9:0x006d, B:13:0x00bc, B:39:0x00cb, B:38:0x00c8, B:18:0x0073, B:21:0x007b, B:23:0x008c, B:25:0x009b, B:26:0x00ae, B:27:0x00a3, B:28:0x0087, B:11:0x00b5, B:33:0x00c2), top: B:8:0x006d, inners: #0, #4 }] */
    /* JADX WARN: Removed duplicated region for block: B:16:? A[RETURN, SYNTHETIC] */
    @com.getcapacitor.PluginMethod
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public void open(com.getcapacitor.PluginCall r27) {
        /*
            Method dump skipped, instructions count: 399
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.ryltsov.alex.plugins.file.opener.FileOpenerPlugin.open(com.getcapacitor.PluginCall):void");
    }

    private String getMimeType(String url) {
        String extension = MimeTypeMap.getFileExtensionFromUrl(url);
        if (extension == null) {
            return null;
        }
        String type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
        return type;
    }

    private String getMimeType(Uri uri) {
        if (uri.getScheme().equals("content")) {
            ContentResolver cr = getActivity().getContentResolver();
            String type = cr.getType(uri);
            return type;
        }
        String extension = MimeTypeMap.getFileExtensionFromUrl(uri.toString());
        if (extension == null) {
            return null;
        }
        String type2 = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
        return type2;
    }
}
