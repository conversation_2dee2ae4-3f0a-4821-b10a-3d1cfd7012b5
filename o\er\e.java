package o.er;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import java.lang.reflect.Method;
import java.util.ArrayList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\e.smali */
public final class e extends o.el.e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int[] m;
    private static int q;
    private static int r;
    private final i a;
    private final i b;
    private final i c;
    private final i d;
    private final i e;
    private final f f;
    private final i g;
    private final c h;
    private final f i;
    private final i j;
    private final i k;
    private final i l;
    private final w n;

    /* renamed from: o, reason: collision with root package name */
    private final i f77o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        q = 1;
        m = new int[]{638584846, -408032802, 1764337053, 1210769415, -301786853, -61638642, 1011546907, -2098813696, -1670267695, -1431207267, -1651160312, 86057089, 71272905, -1161167142, -1297198828, -2128368189, -2000057014, -1918701833};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void C(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            byte[] r0 = o.er.e.$$a
            int r8 = r8 + 115
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2e
        L16:
            r3 = r2
        L17:
            int r6 = r6 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.e.C(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{117, -111, 19, -37};
        $$b = 236;
    }

    @Override // o.ee.d
    public final /* synthetic */ EmvApplication a() {
        int i = r + 43;
        q = i % 128;
        switch (i % 2 == 0) {
            case true:
                B();
                throw null;
            default:
                return B();
        }
    }

    public e(String str, i iVar, i iVar2, i iVar3, i iVar4, i iVar5, f fVar, f fVar2, i iVar6, c cVar, i iVar7, w wVar, i iVar8) {
        super(str);
        this.e = iVar;
        this.b = iVar2;
        this.c = iVar3;
        this.d = iVar4;
        this.a = iVar5;
        this.f = fVar;
        this.i = fVar2;
        this.g = iVar6;
        this.h = cVar;
        this.j = iVar7;
        this.l = new i(false, null);
        this.f77o = new i(true, null);
        this.n = wVar;
        this.k = iVar8;
    }

    public e(String str) {
        super(str);
        this.e = new i(false, null);
        this.b = new i(false, null);
        this.c = new i(false, null);
        this.d = new i(false, null);
        this.a = new i(false, null);
        this.f = new f(false, null);
        this.i = new f(false, null);
        this.g = new i(false, null);
        this.h = new c(false, null, false);
        this.j = new i(false, null);
        this.l = new i(false, null);
        this.f77o = new i(false, null);
        this.n = new w(false, null, new ArrayList());
        this.k = new i(false, null);
    }

    @Override // o.el.d
    public final o.el.d b(String str) {
        e eVar = new e(str);
        int i = r + 49;
        q = i % 128;
        switch (i % 2 == 0) {
            case false:
                return eVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.el.e
    public final i c() {
        int i = r + 75;
        q = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.e;
        }
    }

    @Override // o.el.e
    public final i b() {
        int i = q + 79;
        int i2 = i % 128;
        r = i2;
        int i3 = i % 2;
        i iVar = this.b;
        int i4 = i2 + 85;
        q = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    @Override // o.el.e
    public final i d() {
        int i = q;
        int i2 = i + 59;
        r = i2 % 128;
        int i3 = i2 % 2;
        i iVar = this.c;
        int i4 = i + 31;
        r = i4 % 128;
        switch (i4 % 2 != 0 ? '(' : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return iVar;
            default:
                int i5 = 43 / 0;
                return iVar;
        }
    }

    @Override // o.el.e
    public final i e() {
        int i = q + Opcodes.LNEG;
        r = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 38 / 0;
                return this.d;
            default:
                return this.d;
        }
    }

    @Override // o.el.e
    public final i j() {
        int i = q + Opcodes.LUSHR;
        int i2 = i % 128;
        r = i2;
        int i3 = i % 2;
        i iVar = this.a;
        int i4 = i2 + 31;
        q = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    @Override // o.el.e
    public final f i() {
        int i = q + 5;
        r = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return this.f;
        }
    }

    @Override // o.el.e
    public final f g() {
        int i = q;
        int i2 = i + 67;
        r = i2 % 128;
        switch (i2 % 2 != 0 ? '(' : '`') {
            case '(':
                throw null;
            default:
                f fVar = this.i;
                int i3 = i + 27;
                r = i3 % 128;
                int i4 = i3 % 2;
                return fVar;
        }
    }

    @Override // o.el.e
    public final i f() {
        i iVar;
        int i = r;
        int i2 = i + 45;
        q = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                iVar = this.g;
                int i3 = 38 / 0;
                break;
            default:
                iVar = this.g;
                break;
        }
        int i4 = i + Opcodes.DSUB;
        q = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    @Override // o.el.e
    public final c h() {
        int i = q;
        int i2 = i + 35;
        r = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.h;
        int i4 = i + 69;
        r = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    @Override // o.el.e
    public final i l() {
        int i = r + 55;
        int i2 = i % 128;
        q = i2;
        switch (i % 2 == 0 ? (char) 11 : 'E') {
            case 'E':
                i iVar = this.j;
                int i3 = i2 + Opcodes.LSHL;
                r = i3 % 128;
                int i4 = i3 % 2;
                return iVar;
            default:
                throw null;
        }
    }

    @Override // o.el.e
    public final w m() {
        int i = r + Opcodes.LSHL;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        w wVar = this.n;
        int i4 = i2 + Opcodes.DDIV;
        r = i4 % 128;
        int i5 = i4 % 2;
        return wVar;
    }

    @Override // o.el.e
    public final i k() {
        int i = q + 97;
        r = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.k;
            default:
                int i2 = 57 / 0;
                return this.k;
        }
    }

    @Override // o.el.d
    public final o.ey.e<? extends o.fc.d> a(String str) {
        int i = q;
        int i2 = i + Opcodes.DNEG;
        r = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + Opcodes.DDIV;
        r = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.el.d
    public final boolean o() {
        int i = r + 31;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        int i4 = i2 + 9;
        r = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return false;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.el.d
    public final String r() {
        Object[] objArr = new Object[1];
        A(new int[]{1767653777, 2025572476, -750089096, -424115802, -341615050, 853263394, 1802001494, 1693371234, 1131705736, -1137473655, 2020854949, -216387716, 1124453933, -2016323896, 1230553835, -406697773, 2106673834, -1666067228, -28661330, 1092930835, -715264067, -284855266, 1723940879, 1801076644, -299677482, -895415207, 1594653032, 434435017, -1676867733, -711873234}, (Process.myPid() >> 22) + 59, objArr);
        throw new RuntimeException(((String) objArr[0]).intern());
    }

    @Override // o.el.d
    public final o.ei.a z() {
        int i = q + Opcodes.LREM;
        r = i % 128;
        int i2 = i % 2;
        o.ei.a aVar = o.ei.a.b;
        int i3 = r + 41;
        q = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                throw null;
            default:
                return aVar;
        }
    }

    private static EmvApplication B() {
        Object[] objArr = new Object[1];
        A(new int[]{-1221334126, -1702519198, 1952489818, -1154373592, -650541772, 594249044, 2135887681, 1771336375, -797171763, 1990184559, 1159100417, 515079094, 1855034462, 7521071, -999274125, 280286144, -1219530840, -1700713941, 491186867, -850815473, -22190590, -1196725880, -37215678, -603972753, 142264846, -895392120, 361680779, 498955270, 1032665037, 778520517}, 60 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr);
        throw new RuntimeException(((String) objArr[0]).intern());
    }

    private static void A(int[] iArr, int i, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        int i2;
        int length;
        int[] iArr2;
        int i3;
        o.a.g gVar = new o.a.g();
        char[] cArr3 = new char[4];
        int i4 = 2;
        char[] cArr4 = new char[iArr.length * 2];
        int[] iArr3 = m;
        int i5 = -1667374059;
        int i6 = 16;
        int i7 = 1;
        int i8 = 0;
        if (iArr3 != null) {
            int i9 = $10 + 91;
            $11 = i9 % 128;
            switch (i9 % 2 == 0 ? (char) 17 : (char) 1) {
                case 1:
                    length = iArr3.length;
                    iArr2 = new int[length];
                    i3 = 0;
                    break;
                default:
                    length = iArr3.length;
                    iArr2 = new int[length];
                    i3 = 0;
                    break;
            }
            while (true) {
                switch (i3 < length ? i8 : 1) {
                    case 1:
                        iArr3 = iArr2;
                        break;
                    default:
                        int i10 = $11 + 85;
                        $10 = i10 % 128;
                        if (i10 % i4 != 0) {
                            try {
                                Object[] objArr2 = new Object[1];
                                objArr2[i8] = Integer.valueOf(iArr3[i3]);
                                Object obj = o.e.a.s.get(Integer.valueOf(i5));
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c((ViewConfiguration.getPressedStateDuration() >> i6) + 10, (char) (8855 - TextUtils.lastIndexOf("", '0', i8, i8)), 325 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)));
                                    byte b = (byte) (-1);
                                    byte b2 = (byte) (b + 1);
                                    Object[] objArr3 = new Object[1];
                                    C(b, b2, (byte) (b2 + 1), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj);
                                }
                                iArr2[i3] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                                i3 >>= 0;
                                i4 = 2;
                                i8 = 0;
                                i5 = -1667374059;
                                i6 = 16;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } else {
                            try {
                                Object[] objArr4 = {Integer.valueOf(iArr3[i3])};
                                Object obj2 = o.e.a.s.get(-1667374059);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 8857), KeyEvent.getDeadChar(0, 0) + 324);
                                    byte b3 = (byte) (-1);
                                    byte b4 = (byte) (b3 + 1);
                                    Object[] objArr5 = new Object[1];
                                    C(b3, b4, (byte) (b4 + 1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(-1667374059, obj2);
                                }
                                iArr2[i3] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                                i3++;
                                i4 = 2;
                                i8 = 0;
                                i5 = -1667374059;
                                i6 = 16;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
            }
        }
        int length2 = iArr3.length;
        int[] iArr4 = new int[length2];
        int[] iArr5 = m;
        if (iArr5 != null) {
            int i11 = $11 + 17;
            $10 = i11 % 128;
            int i12 = i11 % 2;
            int length3 = iArr5.length;
            int[] iArr6 = new int[length3];
            int i13 = 0;
            while (i13 < length3) {
                int i14 = $11 + 29;
                $10 = i14 % 128;
                switch (i14 % 2 != 0 ? i7 : 0) {
                    case 0:
                        try {
                            Object[] objArr6 = new Object[i7];
                            objArr6[0] = Integer.valueOf(iArr5[i13]);
                            Object obj3 = o.e.a.s.get(-1667374059);
                            if (obj3 != null) {
                                cArr2 = cArr4;
                                i2 = length3;
                            } else {
                                Class cls3 = (Class) o.e.a.c(ExpandableListView.getPackedPositionType(0L) + 10, (char) (8857 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 324 - ((Process.getThreadPriority(0) + 20) >> 6));
                                byte b5 = (byte) (-1);
                                byte b6 = (byte) (b5 + 1);
                                cArr2 = cArr4;
                                i2 = length3;
                                Object[] objArr7 = new Object[1];
                                C(b5, b6, (byte) (b6 + 1), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj3);
                            }
                            iArr6[i13] = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                            i13++;
                            length3 = i2;
                            cArr4 = cArr2;
                            i7 = 1;
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    default:
                        char[] cArr5 = cArr4;
                        int i15 = length3;
                        try {
                            Object[] objArr8 = {Integer.valueOf(iArr5[i13])};
                            Object obj4 = o.e.a.s.get(-1667374059);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getTapTimeout() >> 16) + 10, (char) ((Process.myTid() >> 22) + 8856), 324 - (ViewConfiguration.getPressedStateDuration() >> 16));
                                byte b7 = (byte) (-1);
                                byte b8 = (byte) (b7 + 1);
                                Object[] objArr9 = new Object[1];
                                C(b7, b8, (byte) (b8 + 1), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj4);
                            }
                            iArr6[i13] = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                            i13 <<= 0;
                            length3 = i15;
                            cArr4 = cArr5;
                            i7 = 1;
                            break;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
            cArr = cArr4;
            iArr5 = iArr6;
        } else {
            cArr = cArr4;
        }
        System.arraycopy(iArr5, 0, iArr4, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            cArr3[0] = (char) (iArr[gVar.a] >> 16);
            cArr3[1] = (char) iArr[gVar.a];
            cArr3[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr3[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr3[0] << 16) + cArr3[1];
            gVar.c = (cArr3[2] << 16) + cArr3[3];
            o.a.g.d(iArr4);
            int i16 = 0;
            for (int i17 = 16; i16 < i17; i17 = 16) {
                int i18 = $11 + 63;
                $10 = i18 % 128;
                if (i18 % 2 != 0) {
                    gVar.e ^= iArr4[i16];
                    try {
                        Object[] objArr10 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                        Object obj5 = o.e.a.s.get(-2036901605);
                        if (obj5 == null) {
                            obj5 = ((Class) o.e.a.c(TextUtils.getOffsetBefore("", 0) + 11, (char) (1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), KeyEvent.normalizeMetaState(0) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                            o.e.a.s.put(-2036901605, obj5);
                        }
                        int intValue = ((Integer) ((Method) obj5).invoke(null, objArr10)).intValue();
                        gVar.e = gVar.c;
                        gVar.c = intValue;
                        i16 += 17;
                    } catch (Throwable th5) {
                        Throwable cause5 = th5.getCause();
                        if (cause5 == null) {
                            throw th5;
                        }
                        throw cause5;
                    }
                } else {
                    gVar.e ^= iArr4[i16];
                    try {
                        Object[] objArr11 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                        Object obj6 = o.e.a.s.get(-2036901605);
                        if (obj6 == null) {
                            obj6 = ((Class) o.e.a.c(11 - ExpandableListView.getPackedPositionGroup(0L), (char) (KeyEvent.getMaxKeyCode() >> 16), Color.argb(0, 0, 0, 0) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                            o.e.a.s.put(-2036901605, obj6);
                        }
                        int intValue2 = ((Integer) ((Method) obj6).invoke(null, objArr11)).intValue();
                        gVar.e = gVar.c;
                        gVar.c = intValue2;
                        i16++;
                    } catch (Throwable th6) {
                        Throwable cause6 = th6.getCause();
                        if (cause6 == null) {
                            throw th6;
                        }
                        throw cause6;
                    }
                }
            }
            int i19 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i19;
            gVar.c ^= iArr4[16];
            gVar.e ^= iArr4[17];
            int i20 = gVar.e;
            int i21 = gVar.c;
            cArr3[0] = (char) (gVar.e >>> 16);
            cArr3[1] = (char) gVar.e;
            cArr3[2] = (char) (gVar.c >>> 16);
            cArr3[3] = (char) gVar.c;
            o.a.g.d(iArr4);
            cArr[gVar.a * 2] = cArr3[0];
            cArr[(gVar.a * 2) + 1] = cArr3[1];
            cArr[(gVar.a * 2) + 2] = cArr3[2];
            cArr[(gVar.a * 2) + 3] = cArr3[3];
            try {
                Object[] objArr12 = {gVar, gVar};
                Object obj7 = o.e.a.s.get(-331007466);
                if (obj7 == null) {
                    Class cls5 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 12, (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 55184), 514 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)));
                    byte b9 = (byte) (-1);
                    byte b10 = (byte) (b9 + 1);
                    Object[] objArr13 = new Object[1];
                    C(b9, b10, b10, objArr13);
                    obj7 = cls5.getMethod((String) objArr13[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, obj7);
                }
                ((Method) obj7).invoke(null, objArr12);
                int i22 = $10 + 9;
                $11 = i22 % 128;
                int i23 = i22 % 2;
            } catch (Throwable th7) {
                Throwable cause7 = th7.getCause();
                if (cause7 == null) {
                    throw th7;
                }
                throw cause7;
            }
        }
        objArr[0] = new String(cArr, 0, i);
    }
}
