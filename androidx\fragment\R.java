package androidx.fragment;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\fragment\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\fragment\R$anim.smali */
    public static final class anim {
        public static int fragment_fast_out_extra_slow_in = 0x7f01001c;

        private anim() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\fragment\R$animator.smali */
    public static final class animator {
        public static int fragment_close_enter = 0x7f020003;
        public static int fragment_close_exit = 0x7f020004;
        public static int fragment_fade_enter = 0x7f020005;
        public static int fragment_fade_exit = 0x7f020006;
        public static int fragment_open_enter = 0x7f020007;
        public static int fragment_open_exit = 0x7f020008;

        private animator() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\fragment\R$id.smali */
    public static final class id {
        public static int fragment_container_view_tag = 0x7f0a00a5;
        public static int special_effects_controller_view_tag = 0x7f0a0128;
        public static int visible_removing_fragment_view_tag = 0x7f0a016e;

        private id() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\fragment\R$styleable.smali */
    public static final class styleable {
        public static int[] Fragment = {android.R.attr.name, android.R.attr.id, android.R.attr.tag};
        public static int[] FragmentContainerView = {android.R.attr.name, android.R.attr.tag};
        public static int FragmentContainerView_android_name = 0x00000000;
        public static int FragmentContainerView_android_tag = 0x00000001;
        public static int Fragment_android_id = 0x00000001;
        public static int Fragment_android_name = 0x00000000;
        public static int Fragment_android_tag = 0x00000002;

        private styleable() {
        }
    }

    private R() {
    }
}
