package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\z0.smali */
public class z0 extends e0 {
    public z0() {
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        int i = z ? 4 : 3;
        int length = this.b.length;
        for (int i2 = 0; i2 < length; i2++) {
            i += this.b[i2].toASN1Primitive().a(true);
        }
        return i;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    d k() {
        return new u0(h());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    l l() {
        return ((e0) g()).l();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    x m() {
        return new x0(i());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    f0 n() {
        return new b1(false, o());
    }

    public z0(h hVar) {
        super(hVar);
    }

    public z0(i iVar) {
        super(iVar);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 48, this.b);
    }
}
