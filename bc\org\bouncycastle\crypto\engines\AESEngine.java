package bc.org.bouncycastle.crypto.engines;

import androidx.recyclerview.widget.ItemTouchHelper;
import bc.org.bouncycastle.crypto.CipherParameters;
import bc.org.bouncycastle.crypto.DataLengthException;
import bc.org.bouncycastle.crypto.params.KeyParameter;
import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v3;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w3;
import java.lang.reflect.Array;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\engines\AESEngine.smali */
public class AESEngine extends v3 {
    private static final byte[] e = {99, 124, 119, 123, -14, 107, 111, -59, 48, 1, 103, 43, -2, -41, -85, 118, -54, -126, -55, 125, -6, 89, 71, -16, -83, -44, -94, -81, -100, -92, 114, -64, -73, -3, -109, 38, 54, 63, -9, -52, 52, -91, -27, -15, 113, -40, 49, 21, 4, -57, 35, -61, 24, -106, 5, -102, 7, 18, ByteCompanionObject.MIN_VALUE, -30, -21, 39, UtilitiesSDKConstants.SRP_LABEL_MAC, 117, 9, -125, 44, 26, 27, 110, 90, -96, 82, 59, -42, -77, 41, -29, 47, -124, 83, -47, 0, -19, 32, -4, -79, 91, 106, -53, -66, 57, 74, 76, 88, -49, -48, -17, -86, -5, 67, 77, 51, -123, 69, -7, 2, ByteCompanionObject.MAX_VALUE, 80, 60, -97, -88, 81, -93, 64, -113, -110, -99, 56, -11, PSSSigner.TRAILER_IMPLICIT, -74, -38, 33, Tnaf.POW_2_WIDTH, -1, -13, -46, -51, 12, 19, -20, 95, -105, 68, 23, -60, -89, 126, Base64.padSymbol, 100, 93, 25, 115, 96, -127, 79, -36, 34, 42, -112, -120, 70, -18, -72, 20, -34, 94, 11, -37, -32, 50, 58, 10, 73, 6, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 92, -62, -45, -84, 98, -111, -107, -28, 121, -25, -56, 55, 109, -115, -43, 78, -87, 108, 86, -12, -22, 101, 122, -82, 8, -70, 120, 37, 46, 28, -90, -76, -58, -24, -35, 116, 31, 75, -67, -117, -118, 112, 62, -75, 102, 72, 3, -10, 14, 97, 53, 87, -71, -122, -63, 29, -98, -31, -8, -104, 17, 105, -39, -114, -108, -101, 30, -121, -23, -50, 85, 40, -33, -116, -95, -119, 13, -65, -26, 66, 104, 65, -103, 45, 15, UtilitiesSDKConstants.SRP_LABEL_ENC, 84, -69, 22};
    private static final byte[] f = {82, 9, 106, -43, 48, 54, -91, 56, -65, 64, -93, -98, -127, -13, -41, -5, 124, -29, 57, -126, -101, 47, -1, -121, 52, -114, 67, 68, -60, -34, -23, -53, 84, 123, -108, 50, -90, -62, 35, Base64.padSymbol, -18, 76, -107, 11, 66, -6, -61, 78, 8, 46, -95, 102, 40, -39, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, UtilitiesSDKConstants.SRP_LABEL_MAC, 118, 91, -94, 73, 109, -117, -47, 37, 114, -8, -10, 100, -122, 104, -104, 22, -44, -92, 92, -52, 93, 101, -74, -110, 108, 112, 72, 80, -3, -19, -71, -38, 94, 21, 70, 87, -89, -115, -99, -124, -112, -40, -85, 0, -116, PSSSigner.TRAILER_IMPLICIT, -45, 10, -9, -28, 88, 5, -72, -77, 69, 6, -48, 44, 30, -113, -54, 63, 15, 2, -63, -81, -67, 3, 1, 19, -118, 107, 58, -111, 17, 65, 79, 103, -36, -22, -105, -14, -49, -50, -16, -76, -26, 115, -106, -84, 116, 34, -25, -83, 53, -123, -30, -7, 55, -24, 28, 117, -33, 110, 71, -15, 26, 113, 29, 41, -59, -119, 111, -73, 98, 14, -86, 24, -66, 27, -4, 86, 62, 75, -58, -46, 121, 32, -102, -37, -64, -2, 120, -51, 90, -12, 31, -35, -88, 51, -120, 7, -57, 49, -79, 18, Tnaf.POW_2_WIDTH, 89, 39, ByteCompanionObject.MIN_VALUE, -20, 95, 96, 81, ByteCompanionObject.MAX_VALUE, -87, 25, -75, 74, 13, 45, -27, 122, -97, -109, -55, -100, -17, -96, -32, 59, 77, -82, 42, -11, UtilitiesSDKConstants.SRP_LABEL_ENC, -56, -21, -69, 60, -125, 83, -103, 97, 23, 43, 4, 126, -70, 119, -42, 38, -31, 105, 20, 99, 85, 33, 12, 125};
    private static final int[] g = {1, 2, 4, 8, 16, 32, 64, 128, 27, 54, 108, 216, Opcodes.LOOKUPSWITCH, 77, Opcodes.IFNE, 47, 94, 188, 99, Opcodes.IFNULL, Opcodes.DCMPL, 53, Opcodes.FMUL, 212, Opcodes.PUTSTATIC, Opcodes.LUSHR, ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION, 239, Opcodes.MULTIANEWARRAY, Opcodes.I2B};
    private static final int[] h = {-1520213050, -2072216328, -1720223762, -1921287178, 234025727, -1117033514, -1318096930, 1422247313, 1345335392, 50397442, -1452841010, 2099981142, 436141799, 1658312629, -424957107, -1703512340, 1170918031, -1652391393, 1086966153, -2021818886, 368769775, -346465870, -918075506, 200339707, -324162239, 1742001331, -39673249, -357585083, -1080255453, -140204973, -1770884380, 1539358875, -1028147339, 486407649, -1366060227, 1780885068, 1513502316, 1094664062, 49805301, 1338821763, 1546925160, -190470831, 887481809, 150073849, -1821281822, 1943591083, 1395732834, 1058346282, 201589768, 1388824469, 1696801606, 1589887901, 672667696, -1583966665, 251987210, -1248159185, 151455502, 907153956, -1686077413, 1038279391, 652995533, 1764173646, -843926913, -1619692054, 453576978, -1635548387, 1949051992, 773462580, 756751158, -1301385508, -296068428, -73359269, -162377052, 1295727478, 1641469623, -827083907, 2066295122, 1055122397, 1898917726, -1752923117, -179088474, 1758581177, 0, 753790401, 1612718144, 536673507, -927878791, -312779850, -1100322092, 1187761037, -641810841, 1262041458, -565556588, -733197160, -396863312, 1255133061, 1808847035, 720367557, -441800113, 385612781, -985447546, -682799718, 1429418854, -1803188975, -817543798, 284817897, 100794884, -2122350594, -263171936, 1144798328, -1163944155, -475486133, -212774494, -22830243, -1069531008, -1970303227, -1382903233, -1130521311, 1211644016, 83228145, -541279133, -1044990345, 1977277103, 1663115586, 806359072, 452984805, 250868733, 1842533055, 1288555905, 336333848, 890442534, 804056259, -513843266, -1567123659, -867941240, 957814574, 1472513171, -223893675, -2105639172, 1195195770, -1402706744, -413311558, 723065138, -1787595802, -1604296512, -1736343271, -783331426, 2145180835, 1713513028, 2116692564, -1416589253, -2088204277, -901364084, 703524551, -742868885, 1007948840, 2044649127, -497131844, 487262998, 1994120109, 1004593371, 1446130276, 1312438900, 503974420, -615954030, 168166924, 1814307912, -463709000, 1573044895, 1859376061, -273896381, -1503501628, -1466855111, -1533700815, 937747667, -1954973198, 854058965, 1137232011, 1496790894, -1217565222, -1936880383, 1691735473, -766620004, -525751991, -1267962664, -95005012, 133494003, 636152527, -1352309302, -1904575756, -374428089, 403179536, -709182865, -2005370640, 1864705354, 1915629148, 605822008, -240736681, -944458637, 1371981463, 602466507, 2094914977, -1670089496, 555687742, -582268010, -591544991, -2037675251, -2054518257, -1871679264, 1111375484, -994724495, -1436129588, -666351472, 84083462, 32962295, 302911004, -1553899070, 1597322602, -111716434, -793134743, -1853454825, 1489093017, 656219450, -1180787161, 954327513, 335083755, -1281845205, 856756514, -1150719534, 1893325225, -1987146233, -1483434957, -1231316179, 572399164, -1836611819, 552200649, 1238290055, -11184726, 2015897680, 2061492133, -1886614525, -123625127, -2138470135, 386731290, -624967835, 837215959, -968736124, -1201116976, -1019133566, -1332111063, 1999449434, 286199582, -877612933, -61582168, -692339859, 974525996};
    private static final int[] i = {1353184337, 1399144830, -1012656358, -1772214470, -882136261, -247096033, -1420232020, -1828461749, 1442459680, -160598355, -1854485368, 625738485, -52959921, -674551099, -2143013594, -1885117771, 1230680542, 1729870373, -1743852987, -507445667, 41234371, 317738113, -1550367091, -956705941, -413167869, -1784901099, -344298049, -631680363, 763608788, -752782248, 694804553, 1154009486, 1787413109, 2021232372, 1799248025, -579749593, -1236278850, 397248752, 1722556617, -1271214467, 407560035, -2110711067, 1613975959, 1165972322, -529046351, -2068943941, 480281086, -1809118983, 1483229296, 436028815, -2022908268, -1208452270, 601060267, -503166094, 1468997603, 715871590, 120122290, 63092015, -1703164538, -1526188077, -226023376, -1297760477, -1167457534, 1552029421, 723308426, -1833666137, -252573709, -1578997426, -839591323, -708967162, 526529745, -1963022652, -1655493068, -1604979806, 853641733, 1978398372, 971801355, -1427152832, 111112542, 1360031421, -108388034, 1023860118, -1375387939, 1186850381, -1249028975, 90031217, 1876166148, -15380384, 620468249, -1746289194, -868007799, 2006899047, -1119688528, -2004121337, 945494503, -605108103, 1191869601, -384875908, -920746760, 0, -2088337399, 1223502642, -1401941730, 1316117100, -67170563, 1446544655, 517320253, 658058550, 1691946762, 564550760, -783000677, 976107044, -1318647284, 266819475, -761860428, -1634624741, 1338359936, -1574904735, 1766553434, 370807324, 179999714, -450191168, 1138762300, 488053522, 185403662, -1379431438, -1180125651, -928440812, -2061897385, 1275557295, -1143105042, -44007517, -1624899081, -1124765092, -985962940, 880737115, 1982415755, -590994485, 1761406390, 1676797112, -891538985, 277177154, 1076008723, 538035844, 2099530373, -130171950, 288553390, 1839278535, 1261411869, -214912292, -330136051, -790380169, 1813426987, -1715900247, -95906799, 577038663, -997393240, 440397984, -668172970, -275762398, -951170681, -1043253031, -22885748, 906744984, -813566554, 685669029, 646887386, -1530942145, -459458004, 227702864, -1681105046, 1648787028, -1038905866, -390539120, 1593260334, -173030526, -1098883681, 2090061929, -1456614033, -1290656305, 999926984, -1484974064, 1852021992, 2075868123, 158869197, -199730834, 28809964, -1466282109, 1701746150, 2129067946, 147831841, -420997649, -644094022, -835293366, -737566742, -696471511, -1347247055, 824393514, 815048134, -1067015627, 935087732, -1496677636, -1328508704, 366520115, 1251476721, -136647615, 240176511, 804688151, -1915335306, 1303441219, 1414376140, -553347356, -474623586, 461924940, -1205916479, 2136040774, 82468509, 1563790337, 1937016826, 776014843, 1511876531, 1389550482, 861278441, 323475053, -1939744870, 2047648055, -1911228327, -1992551445, -299390514, 902390199, -303751967, 1018251130, 1507840668, 1064563285, 2043548696, -1086863501, -355600557, 1537932639, 342834655, -2032450440, -2114736182, 1053059257, 741614648, 1598071746, 1925389590, 203809468, -1958134744, 1100287487, 1895934009, -558691320, -1662733096, -1866377628, 1636092795, 1890988757, 1952214088, 1113045200};
    private int a;
    private int[][] b = null;
    private boolean c;
    private byte[] d;

    public AESEngine() {
        t1.a(new w3(getAlgorithmName(), 256));
    }

    private static int a(int i2) {
        return (((i2 & (-2139062144)) >>> 7) * 27) ^ ((2139062143 & i2) << 1);
    }

    private static int a(int i2, int i3) {
        return (i2 << (-i3)) | (i2 >>> i3);
    }

    private int[][] a(byte[] bArr, boolean z) {
        int length = bArr.length;
        if (length < 16 || length > 32 || (length & 7) != 0) {
            throw new IllegalArgumentException("Key length not 128/192/256 bits.");
        }
        int i2 = length >>> 2;
        int i3 = i2 + 6;
        this.a = i3;
        int[][] iArr = (int[][]) Array.newInstance((Class<?>) Integer.TYPE, i3 + 1, 4);
        int i4 = 8;
        if (i2 == 4) {
            int c = j6.c(bArr, 0);
            iArr[0][0] = c;
            int c2 = j6.c(bArr, 4);
            iArr[0][1] = c2;
            int c3 = j6.c(bArr, 8);
            iArr[0][2] = c3;
            int c4 = j6.c(bArr, 12);
            iArr[0][3] = c4;
            for (int i5 = 1; i5 <= 10; i5++) {
                c ^= d(a(c4, 8)) ^ g[i5 - 1];
                int[] iArr2 = iArr[i5];
                iArr2[0] = c;
                c2 ^= c;
                iArr2[1] = c2;
                c3 ^= c2;
                iArr2[2] = c3;
                c4 ^= c3;
                iArr2[3] = c4;
            }
        } else if (i2 == 6) {
            int c5 = j6.c(bArr, 0);
            iArr[0][0] = c5;
            int c6 = j6.c(bArr, 4);
            iArr[0][1] = c6;
            int c7 = j6.c(bArr, 8);
            iArr[0][2] = c7;
            int c8 = j6.c(bArr, 12);
            iArr[0][3] = c8;
            int c9 = j6.c(bArr, 16);
            int c10 = j6.c(bArr, 20);
            int i6 = 1;
            int i7 = 1;
            while (true) {
                int[] iArr3 = iArr[i6];
                iArr3[0] = c9;
                iArr3[1] = c10;
                int d = d(a(c10, 8)) ^ i7;
                int i8 = i7 << 1;
                int i9 = c5 ^ d;
                int[] iArr4 = iArr[i6];
                iArr4[2] = i9;
                int i10 = c6 ^ i9;
                iArr4[3] = i10;
                int i11 = c7 ^ i10;
                int[] iArr5 = iArr[i6 + 1];
                iArr5[0] = i11;
                int i12 = c8 ^ i11;
                iArr5[1] = i12;
                int i13 = c9 ^ i12;
                iArr5[2] = i13;
                int i14 = c10 ^ i13;
                iArr5[3] = i14;
                int d2 = d(a(i14, 8)) ^ i8;
                i7 = i8 << 1;
                c5 = i9 ^ d2;
                int[] iArr6 = iArr[i6 + 2];
                iArr6[0] = c5;
                c6 = i10 ^ c5;
                iArr6[1] = c6;
                c7 = i11 ^ c6;
                iArr6[2] = c7;
                c8 = i12 ^ c7;
                iArr6[3] = c8;
                i6 += 3;
                if (i6 >= 13) {
                    break;
                }
                c9 = i13 ^ c8;
                c10 = i14 ^ c9;
            }
        } else {
            if (i2 != 8) {
                throw new IllegalStateException("Should never get here");
            }
            int c11 = j6.c(bArr, 0);
            iArr[0][0] = c11;
            int c12 = j6.c(bArr, 4);
            iArr[0][1] = c12;
            int c13 = j6.c(bArr, 8);
            iArr[0][2] = c13;
            int c14 = j6.c(bArr, 12);
            iArr[0][3] = c14;
            int c15 = j6.c(bArr, 16);
            iArr[1][0] = c15;
            int c16 = j6.c(bArr, 20);
            iArr[1][1] = c16;
            int c17 = j6.c(bArr, 24);
            iArr[1][2] = c17;
            int c18 = j6.c(bArr, 28);
            iArr[1][3] = c18;
            int i15 = 2;
            int i16 = 1;
            while (true) {
                int d3 = d(a(c18, i4)) ^ i16;
                i16 <<= 1;
                c11 ^= d3;
                int[] iArr7 = iArr[i15];
                iArr7[0] = c11;
                c12 ^= c11;
                iArr7[1] = c12;
                c13 ^= c12;
                iArr7[2] = c13;
                c14 ^= c13;
                iArr7[3] = c14;
                int i17 = i15 + 1;
                if (i17 >= 15) {
                    break;
                }
                c15 ^= d(c14);
                int[] iArr8 = iArr[i17];
                iArr8[0] = c15;
                c16 ^= c15;
                iArr8[1] = c16;
                c17 ^= c16;
                iArr8[2] = c17;
                c18 ^= c17;
                iArr8[3] = c18;
                i15 = i17 + 1;
                i4 = 8;
            }
        }
        if (!z) {
            for (int i18 = 1; i18 < this.a; i18++) {
                for (int i19 = 0; i19 < 4; i19++) {
                    int[] iArr9 = iArr[i18];
                    iArr9[i19] = c(iArr9[i19]);
                }
            }
        }
        return iArr;
    }

    private static int b(int i2) {
        int i3 = (1061109567 & i2) << 2;
        int i4 = i2 & (-1061109568);
        int i5 = i4 ^ (i4 >>> 1);
        return (i5 >>> 5) ^ (i3 ^ (i5 >>> 2));
    }

    private void b(byte[] bArr, int i2, byte[] bArr2, int i3, int[][] iArr) {
        int c = j6.c(bArr, i2 + 0);
        int c2 = j6.c(bArr, i2 + 4);
        int c3 = j6.c(bArr, i2 + 8);
        int c4 = j6.c(bArr, i2 + 12);
        char c5 = 0;
        int[] iArr2 = iArr[0];
        int i4 = c ^ iArr2[0];
        int i5 = 1;
        int i6 = c2 ^ iArr2[1];
        int i7 = c3 ^ iArr2[2];
        int i8 = c4 ^ iArr2[3];
        int i9 = 1;
        while (i9 < this.a - i5) {
            int[] iArr3 = h;
            int a = (((a(iArr3[(i6 >> 8) & 255], 24) ^ iArr3[i4 & 255]) ^ a(iArr3[(i7 >> 16) & 255], 16)) ^ a(iArr3[(i8 >> 24) & 255], 8)) ^ iArr[i9][c5];
            int a2 = (((a(iArr3[(i7 >> 8) & 255], 24) ^ iArr3[i6 & 255]) ^ a(iArr3[(i8 >> 16) & 255], 16)) ^ a(iArr3[(i4 >> 24) & 255], 8)) ^ iArr[i9][i5];
            int a3 = (((a(iArr3[(i8 >> 8) & 255], 24) ^ iArr3[i7 & 255]) ^ a(iArr3[(i4 >> 16) & 255], 16)) ^ a(iArr3[(i6 >> 24) & 255], 8)) ^ iArr[i9][2];
            int a4 = ((iArr3[i8 & 255] ^ a(iArr3[(i4 >> 8) & 255], 24)) ^ a(iArr3[(i6 >> 16) & 255], 16)) ^ a(iArr3[(i7 >> 24) & 255], 8);
            int i10 = i9 + 1;
            int i11 = a4 ^ iArr[i9][3];
            int a5 = (((iArr3[a & 255] ^ a(iArr3[(a2 >> 8) & 255], 24)) ^ a(iArr3[(a3 >> 16) & 255], 16)) ^ a(iArr3[(i11 >> 24) & 255], 8)) ^ iArr[i10][0];
            int a6 = (((iArr3[a2 & 255] ^ a(iArr3[(a3 >> 8) & 255], 24)) ^ a(iArr3[(i11 >> 16) & 255], 16)) ^ a(iArr3[(a >> 24) & 255], 8)) ^ iArr[i10][1];
            int a7 = (((iArr3[a3 & 255] ^ a(iArr3[(i11 >> 8) & 255], 24)) ^ a(iArr3[(a >> 16) & 255], 16)) ^ a(iArr3[(a2 >> 24) & 255], 8)) ^ iArr[i10][2];
            int a8 = ((iArr3[i11 & 255] ^ a(iArr3[(a >> 8) & 255], 24)) ^ a(iArr3[(a2 >> 16) & 255], 16)) ^ a(iArr3[(a3 >> 24) & 255], 8);
            int i12 = i10 + 1;
            i8 = a8 ^ iArr[i10][3];
            i4 = a5;
            i6 = a6;
            i7 = a7;
            i5 = 1;
            i9 = i12;
            c5 = 0;
        }
        int[] iArr4 = h;
        int a9 = (((iArr4[i4 & 255] ^ a(iArr4[(i6 >> 8) & 255], 24)) ^ a(iArr4[(i7 >> 16) & 255], 16)) ^ a(iArr4[(i8 >> 24) & 255], 8)) ^ iArr[i9][0];
        int a10 = (((iArr4[i6 & 255] ^ a(iArr4[(i7 >> 8) & 255], 24)) ^ a(iArr4[(i8 >> 16) & 255], 16)) ^ a(iArr4[(i4 >> 24) & 255], 8)) ^ iArr[i9][1];
        int a11 = (((iArr4[i7 & 255] ^ a(iArr4[(i8 >> 8) & 255], 24)) ^ a(iArr4[(i4 >> 16) & 255], 16)) ^ a(iArr4[(i6 >> 24) & 255], 8)) ^ iArr[i9][2];
        int a12 = (((iArr4[i8 & 255] ^ a(iArr4[(i4 >> 8) & 255], 24)) ^ a(iArr4[(i6 >> 16) & 255], 16)) ^ a(iArr4[(i7 >> 24) & 255], 8)) ^ iArr[i9][3];
        byte[] bArr3 = e;
        int i13 = (bArr3[a9 & 255] & 255) ^ ((bArr3[(a10 >> 8) & 255] & 255) << 8);
        byte[] bArr4 = this.d;
        int i14 = (i13 ^ ((bArr4[(a11 >> 16) & 255] & 255) << 16)) ^ (bArr4[(a12 >> 24) & 255] << 24);
        int[] iArr5 = iArr[i9 + 1];
        int i15 = i14 ^ iArr5[0];
        int i16 = ((((bArr4[a10 & 255] & 255) ^ ((bArr3[(a11 >> 8) & 255] & 255) << 8)) ^ ((bArr3[(a12 >> 16) & 255] & 255) << 16)) ^ (bArr4[(a9 >> 24) & 255] << 24)) ^ iArr5[1];
        int i17 = (((((bArr3[(a12 >> 8) & 255] & 255) << 8) ^ (bArr4[a11 & 255] & 255)) ^ ((bArr3[(a9 >> 16) & 255] & 255) << 16)) ^ (bArr3[(a10 >> 24) & 255] << 24)) ^ iArr5[2];
        int i18 = ((((bArr4[a12 & 255] & 255) ^ ((bArr4[(a9 >> 8) & 255] & 255) << 8)) ^ ((bArr4[(a10 >> 16) & 255] & 255) << 16)) ^ (bArr3[(a11 >> 24) & 255] << 24)) ^ iArr5[3];
        j6.b(i15, bArr2, i3 + 0);
        j6.b(i16, bArr2, i3 + 4);
        j6.b(i17, bArr2, i3 + 8);
        j6.b(i18, bArr2, i3 + 12);
    }

    private static int c(int i2) {
        int a = a(i2, 8) ^ i2;
        int a2 = i2 ^ a(a);
        int b = a ^ b(a2);
        return a2 ^ (b ^ a(b, 16));
    }

    private static int d(int i2) {
        byte[] bArr = e;
        return (bArr[(i2 >> 24) & 255] << 24) | (bArr[i2 & 255] & 255) | ((bArr[(i2 >> 8) & 255] & 255) << 8) | ((bArr[(i2 >> 16) & 255] & 255) << 16);
    }

    public static o5 newInstance() {
        return new AESEngine();
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public String getAlgorithmName() {
        return "AES";
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int getBlockSize() {
        return 16;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void init(boolean z, CipherParameters cipherParameters) {
        if (!(cipherParameters instanceof KeyParameter)) {
            throw new IllegalArgumentException("invalid parameter passed to AES init - " + cipherParameters.getClass().getName());
        }
        this.b = a(((KeyParameter) cipherParameters).getKey(), z);
        this.c = z;
        if (z) {
            this.d = Arrays.clone(e);
        } else {
            this.d = Arrays.clone(f);
        }
        t1.a(new w3(getAlgorithmName(), a(), cipherParameters, b.a(z)));
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public int processBlock(byte[] bArr, int i2, byte[] bArr2, int i3) {
        int[][] iArr = this.b;
        if (iArr == null) {
            throw new IllegalStateException("AES engine not initialised");
        }
        if (i2 > bArr.length - 16) {
            throw new DataLengthException("input buffer too short");
        }
        if (i3 > bArr2.length - 16) {
            throw new g6("output buffer too short");
        }
        if (this.c) {
            b(bArr, i2, bArr2, i3, iArr);
        } else {
            a(bArr, i2, bArr2, i3, iArr);
        }
        return 16;
    }

    @Override // bc.org.bouncycastle.crypto.BlockCipher
    public void reset() {
    }

    private void a(byte[] bArr, int i2, byte[] bArr2, int i3, int[][] iArr) {
        int c = j6.c(bArr, i2 + 0);
        int c2 = j6.c(bArr, i2 + 4);
        int c3 = j6.c(bArr, i2 + 8);
        int c4 = j6.c(bArr, i2 + 12);
        int i4 = this.a;
        int[] iArr2 = iArr[i4];
        char c5 = 0;
        int i5 = c ^ iArr2[0];
        int i6 = c2 ^ iArr2[1];
        int i7 = c3 ^ iArr2[2];
        int i8 = i4 - 1;
        int i9 = c4 ^ iArr2[3];
        for (int i10 = 1; i8 > i10; i10 = 1) {
            int[] iArr3 = i;
            int a = (((a(iArr3[(i9 >> 8) & 255], 24) ^ iArr3[i5 & 255]) ^ a(iArr3[(i7 >> 16) & 255], 16)) ^ a(iArr3[(i6 >> 24) & 255], 8)) ^ iArr[i8][c5];
            int a2 = (((a(iArr3[(i5 >> 8) & 255], 24) ^ iArr3[i6 & 255]) ^ a(iArr3[(i9 >> 16) & 255], 16)) ^ a(iArr3[(i7 >> 24) & 255], 8)) ^ iArr[i8][i10];
            int a3 = (((a(iArr3[(i6 >> 8) & 255], 24) ^ iArr3[i7 & 255]) ^ a(iArr3[(i5 >> 16) & 255], 16)) ^ a(iArr3[(i9 >> 24) & 255], 8)) ^ iArr[i8][2];
            int a4 = ((iArr3[i9 & 255] ^ a(iArr3[(i7 >> 8) & 255], 24)) ^ a(iArr3[(i6 >> 16) & 255], 16)) ^ a(iArr3[(i5 >> 24) & 255], 8);
            int i11 = i8 - 1;
            int i12 = a4 ^ iArr[i8][3];
            int a5 = (((iArr3[a & 255] ^ a(iArr3[(i12 >> 8) & 255], 24)) ^ a(iArr3[(a3 >> 16) & 255], 16)) ^ a(iArr3[(a2 >> 24) & 255], 8)) ^ iArr[i11][0];
            int a6 = (((iArr3[a2 & 255] ^ a(iArr3[(a >> 8) & 255], 24)) ^ a(iArr3[(i12 >> 16) & 255], 16)) ^ a(iArr3[(a3 >> 24) & 255], 8)) ^ iArr[i11][1];
            int a7 = (((iArr3[a3 & 255] ^ a(iArr3[(a2 >> 8) & 255], 24)) ^ a(iArr3[(a >> 16) & 255], 16)) ^ a(iArr3[(i12 >> 24) & 255], 8)) ^ iArr[i11][2];
            int i13 = i11 - 1;
            i9 = (((iArr3[i12 & 255] ^ a(iArr3[(a3 >> 8) & 255], 24)) ^ a(iArr3[(a2 >> 16) & 255], 16)) ^ a(iArr3[(a >> 24) & 255], 8)) ^ iArr[i11][3];
            i5 = a5;
            i6 = a6;
            i7 = a7;
            i8 = i13;
            c5 = 0;
        }
        int[] iArr4 = i;
        int a8 = (((iArr4[i5 & 255] ^ a(iArr4[(i9 >> 8) & 255], 24)) ^ a(iArr4[(i7 >> 16) & 255], 16)) ^ a(iArr4[(i6 >> 24) & 255], 8)) ^ iArr[i8][0];
        int a9 = (((iArr4[i6 & 255] ^ a(iArr4[(i5 >> 8) & 255], 24)) ^ a(iArr4[(i9 >> 16) & 255], 16)) ^ a(iArr4[(i7 >> 24) & 255], 8)) ^ iArr[i8][1];
        int a10 = (((iArr4[i7 & 255] ^ a(iArr4[(i6 >> 8) & 255], 24)) ^ a(iArr4[(i5 >> 16) & 255], 16)) ^ a(iArr4[(i9 >> 24) & 255], 8)) ^ iArr[i8][2];
        int a11 = (((iArr4[i9 & 255] ^ a(iArr4[(i7 >> 8) & 255], 24)) ^ a(iArr4[(i6 >> 16) & 255], 16)) ^ a(iArr4[(i5 >> 24) & 255], 8)) ^ iArr[i8][3];
        byte[] bArr3 = f;
        int i14 = bArr3[a8 & 255] & 255;
        byte[] bArr4 = this.d;
        int i15 = ((i14 ^ ((bArr4[(a11 >> 8) & 255] & 255) << 8)) ^ ((bArr4[(a10 >> 16) & 255] & 255) << 16)) ^ (bArr3[(a9 >> 24) & 255] << 24);
        int[] iArr5 = iArr[0];
        int i16 = i15 ^ iArr5[0];
        int i17 = ((((bArr4[a9 & 255] & 255) ^ ((bArr4[(a8 >> 8) & 255] & 255) << 8)) ^ ((bArr3[(a11 >> 16) & 255] & 255) << 16)) ^ (bArr4[(a10 >> 24) & 255] << 24)) ^ iArr5[1];
        int i18 = (((((bArr3[(a9 >> 8) & 255] & 255) << 8) ^ (bArr4[a10 & 255] & 255)) ^ ((bArr3[(a8 >> 16) & 255] & 255) << 16)) ^ (bArr4[(a11 >> 24) & 255] << 24)) ^ iArr5[2];
        int i19 = ((((bArr3[a11 & 255] & 255) ^ ((bArr4[(a10 >> 8) & 255] & 255) << 8)) ^ ((bArr4[(a9 >> 16) & 255] & 255) << 16)) ^ (bArr4[(a8 >> 24) & 255] << 24)) ^ iArr5[3];
        j6.b(i16, bArr2, i3 + 0);
        j6.b(i17, bArr2, i3 + 4);
        j6.b(i18, bArr2, i3 + 8);
        j6.b(i19, bArr2, i3 + 12);
    }

    private int a() {
        if (this.b == null) {
            return 256;
        }
        return (r0.length - 7) << 5;
    }
}
