package fr.antelop.sdk.sca;

import android.content.Context;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\sca\DefaultPushAuthenticationRequestListener.smali */
public class DefaultPushAuthenticationRequestListener implements PushAuthenticationRequestListener {
    @Override // fr.antelop.sdk.sca.PushAuthenticationRequestListener
    public void onRequestReceived(Context context, PushAuthenticationRequest pushAuthenticationRequest) {
    }

    @Override // fr.antelop.sdk.sca.PushAuthenticationRequestListener
    public void onRequestCancelled(Context context, String str, CancellationReason cancellationReason) {
    }
}
