package o.cw;

import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import o.ct.b;
import o.eg.d;
import o.fc.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cw\e.smali */
public final class e implements b<o.fe.e> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        a = 1;
        a();
        ExpandableListView.getPackedPositionForChild(0, 0);
        int i = d + 5;
        a = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        e = -9141143195260059130L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 1
            byte[] r0 = o.cw.e.$$a
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r8 = r8 * 2
            int r8 = 114 - r8
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cw.e.f(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{18, UtilitiesSDKConstants.SRP_LABEL_MAC, -55, -33};
        $$b = Opcodes.I2B;
    }

    @Override // o.ct.b
    public final /* synthetic */ o.fe.e b(o.eg.b bVar) throws d {
        int i = d + 33;
        a = i % 128;
        char c = i % 2 == 0 ? (char) 4 : '\'';
        o.fe.e d2 = d(bVar);
        switch (c) {
            case '\'':
                break;
            default:
                int i2 = 13 / 0;
                break;
        }
        int i3 = d + 69;
        a = i3 % 128;
        switch (i3 % 2 == 0 ? '@' : 'D') {
            case '@':
                throw null;
            default:
                return d2;
        }
    }

    private static o.fe.e d(o.eg.b bVar) throws d {
        Object[] objArr = new Object[1];
        c("䓷ợ\uf0f7", 23041 - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
        int intValue = bVar.i(((String) objArr[0]).intern()).intValue();
        Object[] objArr2 = new Object[1];
        c("䓿ᜑ\ue320뽈\u0b4a", (KeyEvent.getMaxKeyCode() >> 16) + 21481, objArr2);
        o.fe.e eVar = new o.fe.e(true, c.b, bVar.k(((String) objArr2[0]).intern()).shortValue());
        eVar.a(intValue);
        int i = d + 21;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                return eVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:11:0x0035. Please report as an issue. */
    private static void c(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 790
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cw.e.c(java.lang.String, int, java.lang.Object[]):void");
    }
}
