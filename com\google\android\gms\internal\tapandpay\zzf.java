package com.google.android.gms.internal.tapandpay;

import android.os.Bundle;
import android.os.IInterface;
import android.os.RemoteException;
import com.google.android.gms.common.api.Status;
import com.google.android.gms.tapandpay.firstparty.RetrieveInAppPaymentCredentialResponse;
import com.google.android.gms.tapandpay.globalactions.GetGlobalActionCardsResponse;
import com.google.android.gms.tapandpay.issuer.PushProvisionSessionContext;
import com.google.android.gms.tapandpay.issuer.TokenInfo;
import com.google.android.gms.tapandpay.issuer.TokenStatus;
import com.google.android.gms.tapandpay.quickaccesswallet.QuickAccessWalletConfig;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\tapandpay\zzf.smali */
public interface zzf extends IInterface {
    void zzA(Status status, com.google.android.gms.tapandpay.firstparty.zzz zzzVar) throws RemoteException;

    void zzB(Status status) throws RemoteException;

    void zzC(Status status) throws RemoteException;

    void zzD(Status status) throws RemoteException;

    void zzE(Status status, byte[] bArr) throws RemoteException;

    void zzF(Status status, PushProvisionSessionContext pushProvisionSessionContext) throws RemoteException;

    void zzG(Status status, QuickAccessWalletConfig quickAccessWalletConfig) throws RemoteException;

    void zzH(Status status, boolean z) throws RemoteException;

    void zzI(Status status) throws RemoteException;

    void zzJ(Status status, com.google.android.gms.tapandpay.firstparty.zzap zzapVar) throws RemoteException;

    void zzK(Status status, com.google.android.gms.tapandpay.firstparty.zzab zzabVar) throws RemoteException;

    void zzL(Status status, com.google.android.gms.tapandpay.firstparty.zzad zzadVar) throws RemoteException;

    void zzM(Status status) throws RemoteException;

    void zzN(Status status, String str) throws RemoteException;

    void zzO(Status status) throws RemoteException;

    void zzP(Status status) throws RemoteException;

    void zzQ(Status status) throws RemoteException;

    void zzR(Status status, TokenStatus tokenStatus) throws RemoteException;

    void zzS(Status status, boolean z) throws RemoteException;

    void zza() throws RemoteException;

    void zzb(Status status) throws RemoteException;

    void zzc(Status status, com.google.android.gms.tapandpay.firstparty.zzj zzjVar) throws RemoteException;

    void zzd(Status status) throws RemoteException;

    void zze(Status status, com.google.android.gms.tapandpay.firstparty.zzl zzlVar) throws RemoteException;

    void zzf(Status status, com.google.android.gms.tapandpay.firstparty.zzn zznVar) throws RemoteException;

    void zzg(Status status, String str) throws RemoteException;

    void zzh(Status status, com.google.android.gms.tapandpay.firstparty.zzp zzpVar) throws RemoteException;

    void zzi(Status status, String str) throws RemoteException;

    void zzj(Status status, com.google.android.gms.tapandpay.firstparty.zzr zzrVar) throws RemoteException;

    void zzk(Status status, com.google.android.gms.tapandpay.firstparty.zzg zzgVar) throws RemoteException;

    void zzl(Status status, com.google.android.gms.tapandpay.firstparty.zzt zztVar) throws RemoteException;

    void zzm(Status status, boolean z) throws RemoteException;

    void zzn(Status status, boolean z) throws RemoteException;

    void zzo(Status status) throws RemoteException;

    void zzp(Status status, String str) throws RemoteException;

    void zzq(Status status, com.google.android.gms.tapandpay.firstparty.zzv zzvVar) throws RemoteException;

    void zzr(Status status) throws RemoteException;

    void zzs(Status status, GetGlobalActionCardsResponse getGlobalActionCardsResponse) throws RemoteException;

    void zzt(Status status, Bundle bundle) throws RemoteException;

    void zzu(Status status, RetrieveInAppPaymentCredentialResponse retrieveInAppPaymentCredentialResponse) throws RemoteException;

    void zzv(Status status) throws RemoteException;

    void zzw(Status status, boolean z) throws RemoteException;

    void zzx(Status status, com.google.android.gms.tapandpay.firstparty.zzx zzxVar) throws RemoteException;

    void zzy(Status status, String str) throws RemoteException;

    void zzz(Status status, TokenInfo[] tokenInfoArr) throws RemoteException;
}
