package o.aa;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.o;
import o.cf.i;
import o.cf.j;
import o.ei.c;
import o.eo.e;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aa\a.smali */
public final class a extends b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long f;
    private static int g;
    private static int i;
    private static char j;
    private static int n;
    Boolean a;
    e b;
    byte[] c;
    String d;
    o.h.d e;
    private final j h;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aa\a$d.smali */
    public interface d {
        void e(Boolean bool);

        void e(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        n = 1;
        m();
        ViewConfiguration.getTouchSlop();
        TextUtils.getOffsetAfter("", 0);
        Color.green(0);
        KeyEvent.keyCodeFromString("");
        int i2 = n + 91;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                break;
            default:
                int i3 = 58 / 0;
                break;
        }
    }

    static void init$0() {
        $$d = new byte[]{106, 35, -45, 57};
        $$e = 201;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0023  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001b  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0023 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 99
            byte[] r0 = o.aa.a.$$d
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L15:
            r3 = r2
        L16:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L23
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L23:
            int r6 = r6 + 1
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r4 = -r4
            int r7 = r7 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.aa.a.l(short, byte, byte, java.lang.Object[]):void");
    }

    static void m() {
        j = (char) 17957;
        i = -1808101087;
        f = 6565854932352255525L;
    }

    public a(Context context, d dVar, c cVar) {
        super(context, dVar, cVar, o.bb.e.w);
        this.h = null;
        this.a = Boolean.FALSE;
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        AsyncTaskC0014a asyncTaskC0014a = new AsyncTaskC0014a(this);
        int i2 = n + 15;
        g = i2 % 128;
        int i3 = i2 % 2;
        return asyncTaskC0014a;
    }

    public final void c(byte[] bArr, o.h.d dVar, String str, e eVar) {
        int i2 = n + 33;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                this.c = bArr;
                this.e = dVar;
                this.d = str;
                this.b = eVar;
                c();
                throw null;
            default:
                this.c = bArr;
                this.e = dVar;
                this.d = str;
                this.b = eVar;
                c();
                return;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i2 = n + 37;
        g = i2 % 128;
        switch (i2 % 2 != 0 ? 'Y' : (char) 25) {
            case Opcodes.DUP /* 89 */:
                Object[] objArr = new Object[1];
                k(ViewConfiguration.getMaximumDrawingCacheSize() * 0, "\uee30老㕣ງ䷐㊳⍆崍⳽쏾禢鯝ᷚ\ue45e\ue147\uf07f椳뱺南騀럆餣ፎ윙표ᗲ", (char) (49951 << (ViewConfiguration.getMaximumDrawingCacheSize() << 37)), "㋷\uec85ώ퓃", "\u0000\u0000\u0000\u0000", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k(ViewConfiguration.getMaximumDrawingCacheSize() >> 24, "\uee30老㕣ງ䷐㊳⍆崍⳽쏾禢鯝ᷚ\ue45e\ue147\uf07f椳뱺南騀럆餣ፎ윙표ᗲ", (char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 49951), "㋷\uec85ώ퓃", "\u0000\u0000\u0000\u0000", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* renamed from: o.aa.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aa\a$a.smali */
    static final class AsyncTaskC0014a extends o.y.c<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static char b;
        private static long c;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            d = 1;
            b = (char) 12205;
            e = 161105445;
            c = 6565854932352255525L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002c). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(byte r7, byte r8, byte r9, java.lang.Object[] r10) {
            /*
                int r9 = r9 + 99
                byte[] r0 = o.aa.a.AsyncTaskC0014a.$$d
                int r8 = r8 * 2
                int r8 = 4 - r8
                int r7 = r7 * 4
                int r7 = r7 + 1
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L16
                r9 = r8
                r3 = r9
                r4 = r2
                r8 = r7
                goto L2c
            L16:
                r3 = r2
            L17:
                int r4 = r3 + 1
                byte r5 = (byte) r9
                r1[r3] = r5
                if (r4 != r7) goto L26
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L26:
                r3 = r0[r8]
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L2c:
                int r7 = r7 + r3
                int r9 = r9 + 1
                r3 = r4
                r6 = r9
                r9 = r7
                r7 = r8
                r8 = r6
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aa.a.AsyncTaskC0014a.B(byte, byte, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{32, 0, 62, 110};
            $$e = Opcodes.IF_ICMPEQ;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 23;
            a = i % 128;
            int i2 = i % 2;
        }

        AsyncTaskC0014a(a aVar) {
            super(aVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = d + Opcodes.LSUB;
            a = i % 128;
            switch (i % 2 == 0) {
                case true:
                    Object[] objArr = new Object[1];
                    w((KeyEvent.getMaxKeyCode() >> 16) + 1159789635, "괻봂幍桗㆔璗籦櫓┻霶ߔ섗匦㰇멬밞\udf2d徖㯡", (char) (TextUtils.indexOf("", "") + 31751), "䌩\u20fc݅歼", "\u0000\u0000\u0000\u0000", objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(1159789635 / (KeyEvent.getMaxKeyCode() << 76), "괻봂幍桗㆔璗籦櫓┻霶ߔ섗匦㰇멬밞\udf2d徖㯡", (char) (26143 - TextUtils.indexOf("", "")), "䌩\u20fc݅歼", "\u0000\u0000\u0000\u0000", objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w((ViewConfiguration.getScrollBarSize() >> 8) + 738305847, "ᔅ㼮儨\u0870䓢➼⮎ㅥ\uf223ꀊ鄂斈䈾屛\ue16bﯷ灚䧟㤶", (char) (Color.blue(0) + 54997), "㜆Ƨ픬僖", "\u0000\u0000\u0000\u0000", objArr);
            o.cf.d dVar = new o.cf.d(context, 42, ((String) objArr[0]).intern());
            int i = a + 81;
            d = i % 128;
            switch (i % 2 == 0 ? (char) 16 : '7') {
                case '7':
                    return dVar;
                default:
                    int i2 = 7 / 0;
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w(AndroidCharacter.getMirror('0') - '0', "愯恠쓟읢鎲恻", (char) (21925 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), "洓세ꔧ챕", "\u0000\u0000\u0000\u0000", objArr);
            bVar.d(((String) objArr[0]).intern(), ((a) e()).b.e());
            switch (((a) e()).c != null ? '^' : ';') {
                default:
                    int i = a + 7;
                    d = i % 128;
                    int i2 = i % 2;
                    Object[] objArr2 = new Object[1];
                    w((-898256124) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), "쓍䝉籲唪锹䎻롐", (char) (TextUtils.lastIndexOf("", '0') + 1), "Պ疳ӊ\ue20d", "\u0000\u0000\u0000\u0000", objArr2);
                    bVar.d(((String) objArr2[0]).intern(), 0);
                    int i3 = d + 45;
                    a = i3 % 128;
                    if (i3 % 2 != 0) {
                    }
                case ';':
                    return bVar;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:33:0x002e, code lost:
        
            if (((o.aa.a) e()).d != null) goto L15;
         */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.cf.j n() {
            /*
                r4 = this;
                int r0 = o.aa.a.AsyncTaskC0014a.d
                int r0 = r0 + 29
                int r1 = r0 % 128
                o.aa.a.AsyncTaskC0014a.a = r1
                int r0 = r0 % 2
                r1 = 0
                r2 = 0
                if (r0 == 0) goto L26
                o.y.b r0 = r4.e()
                o.aa.a r0 = (o.aa.a) r0
                java.lang.String r0 = r0.d
                r3 = 91
                int r3 = r3 / r2
                if (r0 == 0) goto L1e
                r0 = 67
                goto L20
            L1e:
                r0 = 32
            L20:
                switch(r0) {
                    case 32: goto L71;
                    default: goto L23;
                }
            L23:
                goto L30
            L24:
                r0 = move-exception
                throw r0
            L26:
                o.y.b r0 = r4.e()
                o.aa.a r0 = (o.aa.a) r0
                java.lang.String r0 = r0.d
                if (r0 == 0) goto L71
            L30:
                int r0 = o.aa.a.AsyncTaskC0014a.d
                int r0 = r0 + 119
                int r3 = r0 % 128
                o.aa.a.AsyncTaskC0014a.a = r3
                int r0 = r0 % 2
                if (r0 == 0) goto L3e
                r0 = r2
                goto L3f
            L3e:
                r0 = 1
            L3f:
                switch(r0) {
                    case 1: goto L4b;
                    default: goto L42;
                }
            L42:
                o.y.b r0 = r4.e()
                o.aa.a r0 = (o.aa.a) r0
                o.h.d r0 = r0.e
                goto L6b
            L4b:
                o.y.b r0 = r4.e()
                o.aa.a r0 = (o.aa.a) r0
                o.h.d r0 = r0.e
                if (r0 == 0) goto L71
                o.cf.j r0 = new o.cf.j
                o.y.b r1 = r4.e()
                o.aa.a r1 = (o.aa.a) r1
                java.lang.String r1 = r1.d
                o.y.b r3 = r4.e()
                o.aa.a r3 = (o.aa.a) r3
                o.h.d r3 = r3.e
                r0.<init>(r1, r2, r3)
                return r0
            L6b:
                r1.hashCode()     // Catch: java.lang.Throwable -> L6f
                throw r1     // Catch: java.lang.Throwable -> L6f
            L6f:
                r0 = move-exception
                throw r0
            L71:
                int r0 = o.aa.a.AsyncTaskC0014a.d
                int r0 = r0 + 37
                int r2 = r0 % 128
                o.aa.a.AsyncTaskC0014a.a = r2
                int r0 = r0 % 2
                return r1
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aa.a.AsyncTaskC0014a.n():o.cf.j");
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:22:0x003a  */
        /* JADX WARN: Removed duplicated region for block: B:9:0x003c  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final byte[][] k() {
            /*
                r5 = this;
                int r0 = o.aa.a.AsyncTaskC0014a.a
                int r0 = r0 + 7
                int r1 = r0 % 128
                o.aa.a.AsyncTaskC0014a.d = r1
                int r0 = r0 % 2
                r1 = 1
                r2 = 0
                r3 = 0
                if (r0 != 0) goto L29
            L11:
                o.y.b r0 = r5.e()
                o.aa.a r0 = (o.aa.a) r0
                byte[] r0 = r0.c
                r4 = 59
                int r4 = r4 / r3
                if (r0 == 0) goto L21
                r0 = 95
                goto L23
            L21:
                r0 = 65
            L23:
                switch(r0) {
                    case 95: goto L3c;
                    default: goto L26;
                }
            L26:
                goto L3a
            L27:
                r0 = move-exception
                throw r0
            L29:
                o.y.b r0 = r5.e()
                o.aa.a r0 = (o.aa.a) r0
                byte[] r0 = r0.c
                if (r0 == 0) goto L36
                r0 = r1
                goto L37
            L36:
                r0 = r3
            L37:
                switch(r0) {
                    case 1: goto L3c;
                    default: goto L3a;
                }
            L3a:
                r0 = r2
                goto L61
            L3c:
                int r0 = o.aa.a.AsyncTaskC0014a.a
                int r0 = r0 + 67
                int r4 = r0 % 128
                o.aa.a.AsyncTaskC0014a.d = r4
                int r0 = r0 % 2
                if (r0 != 0) goto L55
                byte[][] r0 = new byte[r1][]
                o.y.b r1 = r5.e()
                o.aa.a r1 = (o.aa.a) r1
                byte[] r1 = r1.c
                r0[r3] = r1
                goto L61
            L55:
                byte[][] r0 = new byte[r1][]
                o.y.b r1 = r5.e()
                o.aa.a r1 = (o.aa.a) r1
                byte[] r1 = r1.c
                r0[r3] = r1
            L61:
                int r1 = o.aa.a.AsyncTaskC0014a.a
                int r1 = r1 + 37
                int r3 = r1 % 128
                o.aa.a.AsyncTaskC0014a.d = r3
                int r1 = r1 % 2
                if (r1 == 0) goto L6f
                return r0
            L6f:
                throw r2     // Catch: java.lang.Throwable -> L70
            L70:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aa.a.AsyncTaskC0014a.k():byte[][]");
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = d + 61;
            a = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    switch (i) {
                        case 5001:
                            o.bb.a aVar = o.bb.a.ay;
                            int i3 = a + Opcodes.LREM;
                            d = i3 % 128;
                            int i4 = i3 % 2;
                            return aVar;
                        case 5002:
                            return o.bb.a.az;
                        default:
                            return super.c(i);
                    }
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = d + 65;
            a = i % 128;
            int i2 = i % 2;
            a aVar = (a) e();
            Object[] objArr = new Object[1];
            w((-1) - ((byte) KeyEvent.getModifierMetaStateMask()), "\ud838ᢰ짠楜럀ນ\uf2b2닳ᛯ訇믿卺늢짥", (char) (43663 - KeyEvent.getDeadChar(0, 0)), "╂뻼輆뺪", "\u0000\u0000\u0000\u0000", objArr);
            aVar.a = bVar.b(((String) objArr[0]).intern(), Boolean.FALSE);
            i().e(f().b(g(), ((a) e()).b.I()));
            int i3 = d + 91;
            a = i3 % 128;
            switch (i3 % 2 == 0) {
                case false:
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = d + 53;
            a = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass1.b[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((a) e()).b.e());
                    int i3 = d + Opcodes.LSHR;
                    a = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                case 2:
                    f().e(g(), ((a) e()).b.e());
                    break;
                default:
                    super.t();
                    break;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 99;
            a = i % 128;
            int i2 = i % 2;
            ((a) e()).j().e(((a) e()).a);
            int i3 = d + 23;
            a = i3 % 128;
            switch (i3 % 2 == 0 ? 'V' : (char) 23) {
                case 23:
                    int i4 = 56 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = a + 89;
            d = i % 128;
            int i2 = i % 2;
            ((a) e()).j().e(dVar);
            int i3 = a + 79;
            d = i3 % 128;
            switch (i3 % 2 == 0 ? '+' : 'M') {
                case '+':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
            /*
                Method dump skipped, instructions count: 722
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.aa.a.AsyncTaskC0014a.w(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.aa.a$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\aa\a$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        static final /* synthetic */ int[] b;
        private static int c;
        private static int e;

        static {
            e = 0;
            c = 1;
            int[] iArr = new int[o.bb.a.values().length];
            b = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = c;
                int i2 = (i & Opcodes.LSHR) + (i | Opcodes.LSHR);
                e = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[o.bb.a.az.ordinal()] = 2;
                int i3 = e;
                int i4 = (i3 ^ 25) + ((i3 & 25) << 1);
                c = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    private static void k(int i2, String str, char c, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] charArray;
        char[] charArray2;
        char c2;
        if (str3 != null) {
            int i3 = $10 + 87;
            $11 = i3 % 128;
            int i4 = i3 % 2;
            cArr = str3.toCharArray();
        } else {
            cArr = str3;
        }
        char[] cArr2 = cArr;
        switch (str2 != null ? 'H' : '#') {
            case 'H':
                charArray = str2.toCharArray();
                break;
            default:
                charArray = str2;
                break;
        }
        char[] cArr3 = charArray;
        switch (str != null ? '1' : 'H') {
            case '1':
                int i5 = $11 + 33;
                $10 = i5 % 128;
                int i6 = i5 % 2;
                charArray2 = str.toCharArray();
                break;
            default:
                charArray2 = str;
                break;
        }
        o oVar = new o();
        int length = cArr3.length;
        char[] cArr4 = new char[length];
        int length2 = cArr2.length;
        char[] cArr5 = new char[length2];
        int i7 = 0;
        System.arraycopy(cArr3, 0, cArr4, 0, length);
        System.arraycopy(cArr2, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c);
        cArr5[2] = (char) (cArr5[2] + ((char) i2));
        int length3 = charArray2.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(10 - (ViewConfiguration.getFadingEdgeLength() >> 16), (char) (ExpandableListView.getPackedPositionChild(0L) + 20955), 344 - (ViewConfiguration.getScrollBarFadeDuration() >> 16));
                    byte b = (byte) (-1);
                    byte b2 = (byte) (b + 1);
                    Object[] objArr3 = new Object[1];
                    l(b, b2, b2, objArr3);
                    String str4 = (String) objArr3[i7];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i7] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - TextUtils.indexOf("", ""), (char) TextUtils.indexOf("", "", i7), Drawable.resolveOpacity(i7, i7) + 207);
                        byte b3 = (byte) (-1);
                        byte b4 = (byte) (b3 + 1);
                        Object[] objArr5 = new Object[1];
                        l(b3, b4, (byte) (b4 + 2), objArr5);
                        String str5 = (String) objArr5[i7];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i7] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i8 = cArr4[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr5[intValue]);
                        objArr6[1] = Integer.valueOf(i8);
                        objArr6[i7] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) Color.alpha(i7), 281 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                            byte b5 = (byte) (-1);
                            Object[] objArr7 = new Object[1];
                            l(b5, (byte) (b5 + 1), (byte) $$d.length, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 != null) {
                                c2 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(TextUtils.indexOf("", "", 0) + 19, (char) (Drawable.resolveOpacity(0, 0) + 14687), 112 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                                byte b6 = (byte) (-1);
                                byte b7 = (byte) (b6 + 1);
                                Object[] objArr9 = new Object[1];
                                l(b6, b7, (byte) (b7 | 7), objArr9);
                                c2 = 2;
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((cArr4[intValue2] ^ r2[oVar.e]) ^ (f ^ 6565854932352255525L)) ^ ((int) (i ^ 6565854932352255525L))) ^ ((char) (j ^ 6565854932352255525L)));
                            oVar.e++;
                            i7 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }
}
