package o.o;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Build;
import android.os.Bundle;
import android.os.CancellationSignal;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.appcompat.app.AppCompatActivity;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import fr.antelop.sdk.WalletManager;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.ui.LocaleManager;
import kotlin.text.Typography;
import o.ee.g;
import o.o.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\d.smali */
public final class d extends AppCompatActivity {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    static final a d;
    private static int g;
    private static long h;
    private static int i;
    private static long j;
    private CancellationSignal a;
    long b;
    private WalletManager c;
    private c e;
    private boolean f = true;

    static void b() {
        h = -8223365328762789165L;
        j = 6496877536865701407L;
    }

    static void init$0() {
        $$a = new byte[]{89, -101, -47, 112};
        $$b = Opcodes.JSR;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.o.d.$$a
            int r6 = 114 - r6
            int r8 = r8 * 4
            int r8 = r8 + 1
            int r7 = r7 * 2
            int r7 = 3 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.d.m(byte, int, short, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        g = 1;
        b();
        KeyEvent.getMaxKeyCode();
        KeyEvent.normalizeMetaState(0);
        d = new a();
        int i2 = i + 33;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? Typography.less : '+') {
            case '<':
                throw null;
            default:
                return;
        }
    }

    public static void d(Context context, c cVar, b bVar, CancellationSignal cancellationSignal) {
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 57163, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("搧扱梆眢絃篥䈘䡮囩崟宠⇚⡳㛼㲔㬶", 1627 - TextUtils.getCapsMode("", 0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(cVar.b()).toString());
        Intent intent = new Intent(context, (Class<?>) d.class);
        Object[] objArr3 = new Object[1];
        l("浑텙젒핪洐\udfec헦壟嘴飷郦뉃ᬲ嗸實睃\udc3e滷۟ࡏ脥⯱쇽쵎䨁\ue4eb賽虇༡ꇭ럓孉\uf025竰狤᱃딥㟠㶼텫縄\uf0cd\uf8da\uea6f⌟跍ꏛ꽩\ue410䛍滛恥ꤟφ⧂╸鈞\udcd4퓂ﹾ圎駐鿖", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr3);
        intent.putExtra(((String) objArr3[0]).intern(), cVar.e());
        d.a(cVar, bVar, cancellationSignal);
        intent.addFlags(536870912);
        switch (!(context instanceof Activity)) {
            case false:
                break;
            default:
                int i2 = i + 19;
                g = i2 % 128;
                int i3 = i2 % 2;
                intent.addFlags(268435456);
                int i4 = g + 85;
                i = i4 % 128;
                if (i4 % 2 != 0) {
                    break;
                }
                break;
        }
        context.startActivity(intent);
    }

    @Override // androidx.appcompat.app.AppCompatActivity, android.app.Activity, android.view.ContextThemeWrapper, android.content.ContextWrapper
    protected final void attachBaseContext(Context context) {
        int i2 = i + 75;
        g = i2 % 128;
        int i3 = i2 % 2;
        super.attachBaseContext(LocaleManager.getInstance().getLocalizedContext(context));
        int i4 = i + 71;
        g = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                int i5 = 13 / 0;
                return;
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected final void onCreate(Bundle bundle) {
        int i2 = g + 37;
        i = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57164 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("搬ສ넎室츺炁ᬝ趗", (ViewConfiguration.getPressedStateDuration() >> 16) + 27271, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        getWindow().addFlags(524288);
        getWindow().addFlags(2097152);
        getWindow().addFlags(4194304);
        getWindow().addFlags(128);
        super.onCreate(bundle);
        switch (Build.VERSION.SDK_INT != 26) {
            case true:
                setRequestedOrientation(1);
                break;
        }
        b(getIntent());
        setContentView(R.layout.antelop_activity_authentication_method_prompt);
        getWindow().getDecorView().setLayoutDirection(0);
        this.f = true;
        int i4 = i + 77;
        g = i4 % 128;
        switch (i4 % 2 == 0 ? '7' : 'L') {
            case '7':
                int i5 = 54 / 0;
                return;
            default:
                return;
        }
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    protected final void onNewIntent(Intent intent) {
        int i2 = g + 53;
        i = i2 % 128;
        int i3 = i2 % 2;
        super.onNewIntent(intent);
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 57163, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("搬㏈쯇掉㮠퍳歳ʹ\udb0e猠ૅ", View.combineMeasuredStates(0, 0) + 22501, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.f = true;
        b(intent);
        e();
        this.f = false;
        int i4 = i + 59;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    private void b(Intent intent) {
        int i2 = g + 73;
        i = i2 % 128;
        int i3 = i2 % 2;
        switch (getIntent() != null) {
            case true:
                break;
            default:
                int i4 = i + 63;
                g = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Object[] objArr = new Object[1];
                k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57163 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                l("ぬ㕊ឌ㸿〃㯤\u0a4fልଉ糫佸多䙌놧萬鰑脃說\ud965\ue311\udc18쿯Ṣ☋ᝌú卾洐刚䗣桨뀚괈麦괬\uf71e\ue80e폥\ue27e㨋⌅ᓤ❫", TextUtils.indexOf("", ""), objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                this.f = false;
                finish();
                int i6 = i + Opcodes.DDIV;
                g = i6 % 128;
                int i7 = i6 % 2;
                break;
        }
        Object[] objArr3 = new Object[1];
        l("浑텙젒핪洐\udfec헦壟嘴飷郦뉃ᬲ嗸實睃\udc3e滷۟ࡏ脥⯱쇽쵎䨁\ue4eb賽虇༡ꇭ럓孉\uf025竰狤᱃딥㟠㶼텫縄\uf0cd\uf8da\uea6f⌟跍ꏛ꽩\ue410䛍滛恥ꤟφ⧂╸鈞\udcd4퓂ﹾ圎駐鿖", ViewConfiguration.getScrollDefaultDelay() >> 16, objArr3);
        long longExtra = intent.getLongExtra(((String) objArr3[0]).intern(), -1L);
        this.b = longExtra;
        a aVar = d;
        c e = aVar.e(longExtra);
        this.e = e;
        switch (e == null ? '@' : ';') {
            case '@':
                g.c();
                Object[] objArr4 = new Object[1];
                k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", KeyEvent.normalizeMetaState(0) + 57163, objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                k("搬ꗠ\ue79a⅖挒괣\ueef9⢽樋둛\uf661㟙熱덋ﴗ㼬磶뫾쑝؛䁧臠쎸൜伉褯쫴ᒪ噊遚툵Ᏽ嶍齊\ud910ᬪ⓰曂ꁝ\ue20aⰤ添꾑\ue958", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 49612, objArr5);
                g.e(intern2, ((String) objArr5[0]).intern());
                this.f = false;
                finish();
                break;
        }
        switch (this.e.c() ? 'N' : '%') {
            case '%':
                break;
            default:
                g.c();
                Object[] objArr6 = new Object[1];
                k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57162 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr6);
                String intern3 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                k("搳\u17ee莒㾽ꭚ❫팊伓𥉉盠\ue290麸\u0a43蘰㉜깲姀헩䆙\ufdee楋\ue56d鄏ഫ룞㓨ꂑ峦졓䑨\uf004氯ῆ", 29663 - ((Process.getThreadPriority(0) + 20) >> 6), objArr7);
                g.d(intern3, ((String) objArr7[0]).intern());
                setTheme(R.style.Theme_Antelop_Sdk_Prompt);
                getTheme().applyStyle(R.style.Theme_Antelop_Sdk_Prompt, true);
                break;
        }
        CancellationSignal c = aVar.c(this.b);
        this.a = c;
        if (c != null) {
            c.setOnCancelListener(new CancellationSignal.OnCancelListener() { // from class: o.o.d$$ExternalSyntheticLambda0
                @Override // android.os.CancellationSignal.OnCancelListener
                public final void onCancel() {
                    d.this.c();
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void c() {
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", TextUtils.getTrimmedLength("") + 57163, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("搬\udae2ᦞ塏鼑\ude2b\u1cfc历鈛턩ၵ", 48847 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.e.b()).toString());
        finish();
        int i2 = g + 35;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onStart() {
        super.onStart();
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 57163, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("搬朆扆涶梎毦眵牎紶磠", AndroidCharacter.getMirror('0') + 763, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(getIntent()).toString());
        if (this.f) {
            overridePendingTransition(0, 0);
            return;
        }
        int i2 = g + 99;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onResume() {
        super.onResume();
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", ((byte) KeyEvent.getModifierMetaStateMask()) + 57164, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("搬蝠ꊋ췁\ue904ᒷ㟠匽縋駛蕡", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 58189, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.f).toString());
        if (this.f) {
            if (this.c == null) {
                try {
                    WalletManager walletManager = new WalletManager(this, null, null);
                    this.c = walletManager;
                    walletManager.connect();
                    return;
                } catch (WalletValidationException e) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 57163, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    k("搬ꎈ\ueb5b㋉窤舏짰ᆥ奋患ꠑ\uf001㾑䝃輧횄Ṷ▖涭딓ﲇң䰃诹퍏\u1b7e⋖橅눣咽Ű䣌邮\ud867\ue7c7⾭眐뻿왏", 51157 - AndroidCharacter.getMirror('0'), objArr4);
                    g.a(intern2, ((String) objArr4[0]).intern(), e);
                }
            }
            int i2 = i + 61;
            g = i2 % 128;
            int i3 = i2 % 2;
            return;
        }
        int i4 = i + 1;
        g = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onPause() {
        int i2 = g + 67;
        i = i2 % 128;
        int i3 = i2 % 2;
        super.onPause();
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", TextUtils.lastIndexOf("", '0', 0) + 57164, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k("搬샨\u2d99詭\uf722叩뢸", 42180 - MotionEvent.axisFromString(""), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (this.f) {
            overridePendingTransition(0, 0);
            int i4 = g + Opcodes.LMUL;
            i = i4 % 128;
            int i5 = i4 % 2;
        }
    }

    @Override // android.app.Activity, android.view.Window.Callback
    public final void onWindowFocusChanged(boolean z) {
        super.onWindowFocusChanged(z);
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57162 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        l("ﳒ୬\u20c9딡ﲽׂ㴞餈잼䋈砦툖誔迃댪᜔䶡듯\uee21栀Ⴜ\uf1cb⤬괅\udbf2㺁摩\ue609麳篟彩㬇憽ꃏ騼簒⓲\ued96합", ExpandableListView.getPackedPositionType(0L), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z).toString());
        if (!this.f) {
            int i2 = i + 5;
            g = i2 % 128;
            int i3 = i2 % 2;
            return;
        }
        switch (z ? (char) 15 : 'K') {
            case 'K':
                break;
            default:
                e();
                this.f = false;
                break;
        }
        int i4 = g + 59;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 87 / 0;
                return;
            default:
                return;
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onStop() {
        int i2 = i + 75;
        g = i2 % 128;
        int i3 = i2 % 2;
        super.onStop();
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57163 - (Process.myPid() >> 22), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("舌詤ﾟ廣艣蓊\ue24c狗륣쏔", Color.alpha(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = i + 51;
        g = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected final void onDestroy() {
        super.onDestroy();
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57163 - TextUtils.indexOf("", "", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("㘍飔룬\ue364㙢険ꔨ콁ൾ텠\ue01e葋䁴", ImageFormat.getBitsPerPixel(0) + 1, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        WalletManager walletManager = this.c;
        switch (walletManager != null) {
            case false:
                break;
            default:
                walletManager.disconnect();
                this.c = null;
                break;
        }
        CancellationSignal cancellationSignal = this.a;
        switch (cancellationSignal == null) {
            case false:
                int i2 = i + Opcodes.DMUL;
                g = i2 % 128;
                int i3 = i2 % 2;
                cancellationSignal.setOnCancelListener(null);
                int i4 = i + 73;
                g = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
        d.a(this.b);
        this.e = null;
        this.a = null;
    }

    private void e() {
        int i2 = i + Opcodes.DSUB;
        g = i2 % 128;
        int i3 = i2 % 2;
        if (this.f) {
            g.c();
            Object[] objArr = new Object[1];
            k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", View.MeasureSpec.makeMeasureSpec(0, 0) + 57163, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l("헏鼮ڤᄕ햫醇᭗㴥\ueea3횏幝瘅ꎽᮁ镉댥撻", TextUtils.indexOf((CharSequence) "", '0', 0) + 1, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            this.e.d(this, R.id.container, this.a, new b() { // from class: o.o.d.5
                private static int e = 0;
                private static int a = 1;

                @Override // o.o.b
                public final void b(o.f.e eVar, c cVar) {
                    int i4 = e;
                    int i5 = ((i4 | 49) << 1) - (i4 ^ 49);
                    a = i5 % 128;
                    int i6 = i5 % 2;
                    b b = d.d.b(d.this.b);
                    switch (b != null ? 'J' : '6') {
                        case Opcodes.ISTORE /* 54 */:
                            break;
                        default:
                            int i7 = a + 47;
                            e = i7 % 128;
                            switch (i7 % 2 != 0) {
                                case true:
                                    d.d.a(d.this.b);
                                    d.this.finish();
                                    b.b(eVar, cVar);
                                    Object obj = null;
                                    obj.hashCode();
                                    throw null;
                                default:
                                    d.d.a(d.this.b);
                                    d.this.finish();
                                    b.b(eVar, cVar);
                                    break;
                            }
                    }
                    int i8 = e;
                    int i9 = ((i8 | 67) << 1) - (i8 ^ 67);
                    a = i9 % 128;
                    int i10 = i9 % 2;
                }

                @Override // o.o.b
                public final void c(e eVar, c cVar) {
                    int i4 = e + 13;
                    a = i4 % 128;
                    Object obj = null;
                    switch (i4 % 2 != 0) {
                        case false:
                            d.d.b(d.this.b);
                            obj.hashCode();
                            throw null;
                        default:
                            b b = d.d.b(d.this.b);
                            switch (b == null) {
                                case true:
                                    break;
                                default:
                                    int i5 = a + 93;
                                    e = i5 % 128;
                                    int i6 = i5 % 2;
                                    d.d.a(d.this.b);
                                    d.this.finish();
                                    b.c(eVar, cVar);
                                    break;
                            }
                            int i7 = a;
                            int i8 = (i7 & 7) + (i7 | 7);
                            e = i8 % 128;
                            switch (i8 % 2 != 0) {
                                case true:
                                    obj.hashCode();
                                    throw null;
                                default:
                                    return;
                            }
                    }
                }

                @Override // o.o.b
                public final void e(c cVar) {
                    int i4 = e;
                    int i5 = (i4 ^ 41) + ((i4 & 41) << 1);
                    a = i5 % 128;
                    switch (i5 % 2 != 0) {
                        case true:
                            b b = d.d.b(d.this.b);
                            switch (b == null) {
                                case true:
                                    break;
                                default:
                                    int i6 = a;
                                    int i7 = ((i6 | Opcodes.DSUB) << 1) - (i6 ^ Opcodes.DSUB);
                                    e = i7 % 128;
                                    switch (i7 % 2 != 0 ? 'X' : '*') {
                                        case Opcodes.POP2 /* 88 */:
                                            d.d.a(d.this.b);
                                            b.e(cVar);
                                            throw null;
                                        default:
                                            d.d.a(d.this.b);
                                            b.e(cVar);
                                            break;
                                    }
                            }
                            int i8 = e;
                            int i9 = (i8 ^ 69) + ((i8 & 69) << 1);
                            a = i9 % 128;
                            int i10 = i9 % 2;
                            return;
                        default:
                            d.d.b(d.this.b);
                            throw null;
                    }
                }
            });
            int i4 = i + 49;
            g = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    int i5 = 2 / 0;
                    return;
                default:
                    return;
            }
        }
    }

    @Override // androidx.activity.ComponentActivity, android.app.Activity
    public final void onBackPressed() {
        g.c();
        Object[] objArr = new Object[1];
        k("搀뭽\udaa6淪ᤀ㡙忤缼鹚붕\udcd9ﰒᎢ㋢刭煏邐럙흱\uf6bbᗰ㔊呡殌謤ꩽ즭\ue8de࠶⽟份渿赕겁쏁\ue37b", 57163 - ExpandableListView.getPackedPositionType(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l("芙䴔莠齗苶䎺鹢덶맺ҿ\udb70\uf865\uf4fc즧ၓ㵲㏽", ViewConfiguration.getKeyRepeatTimeout() >> 16, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        a.b a = d.a(this.b);
        CancellationSignal cancellationSignal = this.a;
        switch (cancellationSignal != null ? '@' : '.') {
            case '.':
                break;
            default:
                int i2 = g + Opcodes.LNEG;
                i = i2 % 128;
                int i3 = i2 % 2;
                switch (cancellationSignal.isCanceled()) {
                    case false:
                        int i4 = i + 61;
                        g = i4 % 128;
                        if (i4 % 2 == 0) {
                            this.a.cancel();
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        }
                        this.a.cancel();
                        break;
                }
        }
        if (a != null) {
            int i5 = i + 39;
            g = i5 % 128;
            int i6 = i5 % 2;
            a.c().c(e.a, a.b());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 636
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.d.k(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 376
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.d.l(java.lang.String, int, java.lang.Object[]):void");
    }
}
