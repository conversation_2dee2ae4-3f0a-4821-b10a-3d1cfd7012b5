package com.vasco.digipass.sdk.utils.devicebinding;

import android.content.Context;
import kotlin.Deprecated;
import kotlin.Metadata;

@Deprecated(message = "This class is deprecated and will be removed in a future release.")
@Metadata(bv = {}, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0013\b\u0007\u0018\u00002\u00020\u0001B\u001b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\u0010\u001a\u0004\u0018\u00010\n¢\u0006\u0004\b\"\u0010#R\"\u0010\t\u001a\u0004\u0018\u00010\u00028\u0006X\u0087\u0004¢\u0006\u0012\n\u0004\b\u0003\u0010\u0004\u0012\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006R\"\u0010\u0010\u001a\u0004\u0018\u00010\n8\u0006X\u0087\u0004¢\u0006\u0012\n\u0004\b\u000b\u0010\f\u0012\u0004\b\u000f\u0010\b\u001a\u0004\b\r\u0010\u000eR0\u0010\u0015\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00118\u0006@GX\u0087\u000e¢\u0006\u0018\n\u0004\b\u0013\u0010\u0014\u0012\u0004\b\u0019\u0010\b\u001a\u0004\b\u0015\u0010\u0016\"\u0004\b\u0017\u0010\u0018R0\u0010\u001b\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00118\u0006@GX\u0087\u000e¢\u0006\u0018\n\u0004\b\u001a\u0010\u0014\u0012\u0004\b\u001d\u0010\b\u001a\u0004\b\u001b\u0010\u0016\"\u0004\b\u001c\u0010\u0018R0\u0010\u001f\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u00118\u0006@GX\u0087\u000e¢\u0006\u0018\n\u0004\b\u001e\u0010\u0014\u0012\u0004\b!\u0010\b\u001a\u0004\b\u001f\u0010\u0016\"\u0004\b \u0010\u0018¨\u0006$"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingSDKParams;", "", "", "a", "Ljava/lang/String;", "getSalt", "()Ljava/lang/String;", "getSalt$annotations", "()V", "salt", "Landroid/content/Context;", "b", "Landroid/content/Context;", "getContext", "()Landroid/content/Context;", "getContext$annotations", "context", "", "<set-?>", "c", "Z", "isAndroidIdUsed", "()Z", "useAndroidId", "(Z)V", "isAndroidIdUsed$annotations", "d", "isSerialUsed", "useSerial", "isSerialUsed$annotations", "e", "isHardwareUsed", "useHardware", "isHardwareUsed$annotations", "<init>", "(Ljava/lang/String;Landroid/content/Context;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\DeviceBindingSDKParams.smali */
public final class DeviceBindingSDKParams {

    /* renamed from: a, reason: from kotlin metadata */
    private final String salt;

    /* renamed from: b, reason: from kotlin metadata */
    private final Context context;

    /* renamed from: c, reason: from kotlin metadata */
    private boolean isAndroidIdUsed;

    /* renamed from: d, reason: from kotlin metadata */
    private boolean isSerialUsed;

    /* renamed from: e, reason: from kotlin metadata */
    private boolean isHardwareUsed;

    public DeviceBindingSDKParams(String str, Context context) {
        this.salt = str;
        this.context = context;
    }

    @Deprecated(message = "This field is deprecated and should no longer be used")
    public static /* synthetic */ void getContext$annotations() {
    }

    @Deprecated(message = "This field is deprecated and should no longer be used")
    public static /* synthetic */ void getSalt$annotations() {
    }

    @Deprecated(message = "This field is deprecated and should no longer be used")
    public static /* synthetic */ void isAndroidIdUsed$annotations() {
    }

    @Deprecated(message = "This field is deprecated and should no longer be used")
    public static /* synthetic */ void isHardwareUsed$annotations() {
    }

    @Deprecated(message = "The serial number is no longer available starting with android Q. Devices already using this method before upgrading to Android Q will still be able to use\n      the serial to generate the device Fingerprint. Otherwise a random ID will be used in its place.\n      ")
    public static /* synthetic */ void isSerialUsed$annotations() {
    }

    public final Context getContext() {
        return this.context;
    }

    public final String getSalt() {
        return this.salt;
    }

    /* renamed from: isAndroidIdUsed, reason: from getter */
    public final boolean getIsAndroidIdUsed() {
        return this.isAndroidIdUsed;
    }

    /* renamed from: isHardwareUsed, reason: from getter */
    public final boolean getIsHardwareUsed() {
        return this.isHardwareUsed;
    }

    /* renamed from: isSerialUsed, reason: from getter */
    public final boolean getIsSerialUsed() {
        return this.isSerialUsed;
    }

    public final void useAndroidId(boolean z) {
        this.isAndroidIdUsed = z;
    }

    public final void useHardware(boolean z) {
        this.isHardwareUsed = z;
    }

    public final void useSerial(boolean z) {
        this.isSerialUsed = z;
    }
}
