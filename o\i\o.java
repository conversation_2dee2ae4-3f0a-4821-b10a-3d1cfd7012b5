package o.i;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.ScreenUnlockCustomerAuthenticationPrompt;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\o.smali */
public final class o extends k<o.r.c> {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char b;
    private static int[] c;
    private static int d;
    private static long e;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        i = 1;
        b();
        View.resolveSizeAndState(0, 0, 0);
        int i2 = a + 83;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void b() {
        c = new int[]{568889878, 154044353, -79616343, -180788244, -752457356, -1617264679, 1094953717, -440990702, 1160091203, 1351581779, 744274499, 2069667754, -1960722693, 1168848184, -1661121562, -1137789451, -886693678, -1917927545};
        b = (char) 1210;
        d = 161105445;
        e = 6565854932352255525L;
    }

    static void init$0() {
        $$g = new byte[]{0, -16, -96, 75};
        $$h = 244;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r6 = 116 - r6
            byte[] r0 = o.i.o.$$g
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.o.w(short, int, int, java.lang.Object[]):void");
    }

    public o() {
        super(f.b, new o.r.c());
    }

    @Override // o.i.g
    public final void d(Context context, o.f.e eVar, o.g.a aVar, b bVar, boolean z) {
        try {
            byte[] d2 = e().d();
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, 39 - MotionEvent.axisFromString(""), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t(ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), "㇆ɀక\uf3ef\ue420딆⌱ℐ✚╾欀쀆\ue81a嘁ᵛ䡲瘇陇䌀㌪\ua6fa藺넠鸱숳\u0cd7樢倷ༀ꜄쑕뵧혹剽쑷뺋ࡦઘ頋ᐙ륂䞥鐨\uec0b晳녚涣༪漱뒀ⴇ﹍䈅ぜ㸇䩎熀ꀷ젵蹜\ueb76W鰏뽮㰷\uf5be", (char) ((ViewConfiguration.getScrollBarSize() >> 8) + 50007), "꾊\uede8坬苃", "\u0000\u0000\u0000\u0000", objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            aVar.d(new o.f.j(e.d.c, new Date(), new o.f.d(d2)));
            int i2 = i + 53;
            a = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    int i3 = 38 / 0;
                    return;
                default:
                    return;
            }
        } catch (o.r.b e2) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 39, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            t((-1) - TextUtils.indexOf((CharSequence) "", '0', 0), "༮ꆂ⩽궯炚궴\ueb45뒝灝ଌႦ剀\uf30e溬⩝芻뚫孡◱㜘ⳳ䭳踶〈ᜮ\udbdc\ud86f鋼팝ࣕ僚埇쵅輢䶺ﱱ翩鈽", (char) (ViewConfiguration.getTapTimeout() >> 16), "㼓\uf325帳컷", "\u0000\u0000\u0000\u0000", objArr4);
            o.ee.g.a(intern2, ((String) objArr4[0]).intern(), e2);
            aVar.c(o.g.b.a);
        } catch (o.r.d e3) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 40, objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            l(new int[]{1607665966, -523176643, -284966380, 53997174, -1297990633, 304825542, 1216921593, -2076853623, 229138121, 1399004024, -61927347, 1964835700, -1892615828, 581477033, 1193922245, -1441315218, -1711023497, 840166188, 808290383, 300372445, 1574600185, 1594878394, -1922297020, -366519970, -1336804695, 1389484795, 2007712544, 647056547, -1198827474, -192004663, 332502715, 1770329100, 1671139566, 328635232, -570372505, -391277465, 1408885941, -1502296432}, TextUtils.lastIndexOf("", '0', 0) + 75, objArr6);
            o.ee.g.d(intern3, ((String) objArr6[0]).intern());
            aVar.c(o.g.b.e);
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x003a. Please report as an issue. */
    public final boolean c(Context context, o.h.a aVar, o.h.d dVar) {
        final CountDownLatch countDownLatch = new CountDownLatch(1);
        final AtomicBoolean atomicBoolean = new AtomicBoolean(false);
        aVar.e(context, dVar, new o.f.j(e.d.b, new Date(), new o.f.d(new byte[0])), new o.bw.b() { // from class: o.i.o.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static long b;
            private static int c;
            private static int g;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                c = 0;
                g = 1;
                b = 5784165310316937293L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x0037). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void h(int r7, byte r8, byte r9, java.lang.Object[] r10) {
                /*
                    int r9 = r9 * 2
                    int r9 = 3 - r9
                    int r7 = r7 * 4
                    int r7 = 1 - r7
                    int r8 = r8 * 3
                    int r8 = 71 - r8
                    byte[] r0 = o.i.o.AnonymousClass2.$$a
                    byte[] r1 = new byte[r7]
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r8 = r7
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    goto L37
                L1a:
                    r3 = r2
                    r6 = r8
                    r8 = r7
                    r7 = r6
                L1e:
                    int r4 = r3 + 1
                    byte r5 = (byte) r7
                    r1[r3] = r5
                    int r9 = r9 + 1
                    if (r4 != r8) goto L2f
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2f:
                    r3 = r0[r9]
                    r6 = r10
                    r10 = r9
                    r9 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r6
                L37:
                    int r9 = -r9
                    int r7 = r7 + r9
                    r9 = r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    goto L1e
                */
                throw new UnsupportedOperationException("Method not decompiled: o.i.o.AnonymousClass2.h(int, byte, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{27, 43, 25, -109};
                $$b = 75;
            }

            @Override // o.bw.b
            public final void d(f fVar) {
                String intern;
                Object obj;
                int i2 = g + Opcodes.DDIV;
                c = i2 % 128;
                switch (i2 % 2 != 0 ? (char) 15 : '/') {
                    case '/':
                        o.ee.g.c();
                        Object[] objArr = new Object[1];
                        f("鷇鶔彑≇ᾠ偧ꌑ醴鿪ᷥꅾ鏷餻\u1a1cꞐ陊魜ᡎꗈ额针ᚎꡦ髃雮ᓞ꺿鴑递ᅽ곧齨鉜ྺ댯膠迨෭녮菴褻ਣ랄蘽", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr);
                        intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        f("皷盛흟ȹ鞢岥荼鵷璼闷脰鼿牂鈓蟹骊瀎遙薶鑭翥麂蠐阡綺鳆軐釕筳饈貃鎯礠螶鍎贫操薭鄯輶扜舠韻諯恇聈閣葏激躖顮虋涧賈黏臕歰褗鲘莞椣", TextUtils.getTrimmedLength(""), objArr2);
                        obj = objArr2[0];
                        break;
                    default:
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        f("鷇鶔彑≇ᾠ偧ꌑ醴鿪ᷥꅾ鏷餻\u1a1cꞐ陊魜ᡎꗈ额针ᚎꡦ髃雮ᓞ꺿鴑递ᅽ곧齨鉜ྺ댯膠迨෭녮菴褻ਣ랄蘽", ViewConfiguration.getKeyRepeatDelay() + 81, objArr3);
                        intern = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f("皷盛흟ȹ鞢岥荼鵷璼闷脰鼿牂鈓蟹骊瀎遙薶鑭翥麂蠐阡綺鳆軐釕筳饈貃鎯礠螶鍎贫操薭鄯輶扜舠韻諯恇聈閣葏激躖顮虋涧賈黏臕歰褗鲘莞椣", TextUtils.getTrimmedLength(""), objArr4);
                        obj = objArr4[0];
                        break;
                }
                o.ee.g.d(intern, ((String) obj).intern());
                atomicBoolean.set(true);
                countDownLatch.countDown();
            }

            @Override // o.bw.b
            public final void c(f fVar, o.g.b bVar) {
                String intern;
                Object obj;
                int i2 = c + 37;
                g = i2 % 128;
                switch (i2 % 2 == 0 ? '1' : 'O') {
                    case Opcodes.IASTORE /* 79 */:
                        o.ee.g.c();
                        Object[] objArr = new Object[1];
                        f("鷇鶔彑≇ᾠ偧ꌑ醴鿪ᷥꅾ鏷餻\u1a1cꞐ陊魜ᡎꗈ额针ᚎꡦ髃雮ᓞ꺿鴑递ᅽ곧齨鉜ྺ댯膠迨෭녮菴褻ਣ랄蘽", (-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr);
                        intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        f("ⱧⰋ䊱鞀Ɍ㭭ᛅ謹\u2e6c\u0019ᒉ\uf8f7⢒߽ቀ\ufd42⫞ַဏ\uf3a5┵୬ᶩ\uf1e9❪न᭩\uf61d↣ದ᤺\uf467⏰ቘ۷\ueae3㸝၃Ҋ\ue8f4㣏៙ɉ\ued30㫒ᖽ\u0000\ue3cb㔊᭵ැ\ue1cc㝤ᤨତ\ue60bㆪ᳹प\ue45c㏥≞㛱\udaaf", ViewConfiguration.getScrollBarFadeDuration() >> 16, objArr2);
                        obj = objArr2[0];
                        break;
                    default:
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        f("鷇鶔彑≇ᾠ偧ꌑ醴鿪ᷥꅾ鏷餻\u1a1cꞐ陊魜ᡎꗈ额针ᚎꡦ髃雮ᓞ꺿鴑递ᅽ곧齨鉜ྺ댯膠迨෭녮菴褻ਣ랄蘽", TextUtils.indexOf((CharSequence) "", '*', 0, 0) * (-1), objArr3);
                        intern = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        f("ⱧⰋ䊱鞀Ɍ㭭ᛅ謹\u2e6c\u0019ᒉ\uf8f7⢒߽ቀ\ufd42⫞ַဏ\uf3a5┵୬ᶩ\uf1e9❪न᭩\uf61d↣ದ᤺\uf467⏰ቘ۷\ueae3㸝၃Ҋ\ue8f4㣏៙ɉ\ued30㫒ᖽ\u0000\ue3cb㔊᭵ැ\ue1cc㝤ᤨତ\ue60bㆪ᳹प\ue45c㏥≞㛱\udaaf", ViewConfiguration.getScrollBarFadeDuration() << 104, objArr4);
                        obj = objArr4[0];
                        break;
                }
                o.ee.g.d(intern, ((String) obj).intern());
                countDownLatch.countDown();
                int i3 = g + 61;
                c = i3 % 128;
                int i4 = i3 % 2;
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
                /*
                    Method dump skipped, instructions count: 358
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.i.o.AnonymousClass2.f(java.lang.String, int, java.lang.Object[]):void");
            }
        });
        try {
            countDownLatch.await();
            int i2 = i + 1;
            a = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 5 : (char) 24) {
            }
        } catch (InterruptedException e2) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 40, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l(new int[]{-56143993, 1181493966, -1297990633, 304825542, 1216921593, -2076853623, -971304559, 1400139494, 409942115, 1081944599, 1366771152, 1247216927, 1727129121, -347317279, 1901402710, 760468167, -1773682824, -850786178, 1116833537, -663480823, -1818464946, 1700097583, 1901402710, 760468167, -1993271260, -1070369318, 370795980, -649670384, -49537775, -1053975141, 181398388, -848706171, -1886810532, 1953448280}, 68 - Color.argb(0, 0, 0, 0), objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
        }
        return atomicBoolean.get();
    }

    @Override // o.i.g
    protected final o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException {
        int i2 = a + 35;
        i = i2 % 128;
        int i3 = i2 % 2;
        if (!(customerAuthenticationPrompt instanceof ScreenUnlockCustomerAuthenticationPrompt)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, View.getDefaultSize(0, 0) + 40, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l(new int[]{1751793311, -982796326, 863944343, 1069933453, 1530457193, 1042961397, 1750996482, 984055475, 1607665966, -523176643, -649989631, -1520253095, -1549865278, -850498408, -2118221403, 682184987, 376075767, -1993539249, 2073113243, 1249469067, 2029498626, -1540816716, -629578282, -1605487538, -921846436, -871939807, -2135850463, -2082809812, -473424890, 2134956347}, TextUtils.getOffsetBefore("", 0) + 59, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.InvalidFormat;
            Object[] objArr3 = new Object[1];
            l(new int[]{-690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, 261745100, 1523731354, -1066440905, 1444138478}, View.resolveSize(0, 0) + 28, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        ScreenUnlockCustomerAuthenticationPrompt screenUnlockCustomerAuthenticationPrompt = (ScreenUnlockCustomerAuthenticationPrompt) customerAuthenticationPrompt;
        o.m.c cVar = new o.m.c(context, screenUnlockCustomerAuthenticationPrompt.getTitle(), screenUnlockCustomerAuthenticationPrompt.getSubtitle(), this);
        int i4 = i + 67;
        a = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    @Override // o.i.g
    public final void c(int i2) {
        int i3 = i + 31;
        a = i3 % 128;
        int i4 = i3 % 2;
        super.c(i2);
        e().a(i2);
        int i5 = i + Opcodes.LNEG;
        a = i5 % 128;
        switch (i5 % 2 != 0 ? '!' : (char) 25) {
            case 25:
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:21:0x0111. Please report as an issue. */
    @Override // o.i.g
    final void e(Context context, g gVar) {
        int i2 = a + 19;
        i = i2 % 128;
        int i3 = i2 % 2;
        Integer g = g();
        super.e(context, gVar);
        switch (!Objects.equals(g, g()) ? ' ' : (char) 7) {
            case ' ':
                int i4 = i + 3;
                a = i4 % 128;
                switch (i4 % 2 != 0 ? '<' : '2') {
                    case '<':
                        g();
                        throw null;
                    default:
                        switch (g() != null ? ';' : '\r') {
                            case '\r':
                                return;
                            default:
                                o.ee.g.c();
                                Object[] objArr = new Object[1];
                                l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, 40 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                l(new int[]{-494346677, -14733039, -1640351161, 1301054167, 117109967, 1441788103, -1531063802, 648857243, -1758508415, -1810884557, 1571660888, -713874638, 1216921593, -2076853623, -1414356551, 165015591, 970417908, -1262477891, -76274120, 1690375065, -1426650577, 943586790, -375700518, 557164320, 1207156985, 947254258, -416528358, 1886950877, -1922297020, -366519970}, 60 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr2);
                                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                                e().a(g().intValue());
                                if (e().c()) {
                                    int i5 = i + Opcodes.LNEG;
                                    a = i5 % 128;
                                    int i6 = i5 % 2;
                                    o.ee.g.c();
                                    Object[] objArr3 = new Object[1];
                                    l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, Color.red(0) + 40, objArr3);
                                    String intern2 = ((String) objArr3[0]).intern();
                                    Object[] objArr4 = new Object[1];
                                    t(2124479306 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "⿑㽋㹓\uf39eﰇ夎끚ᆜ눫㣵⠾␛䕦醵\u1778絰鬪묉戒䱾鐡弹悱䜝蓉֗⨬莐\uf6c1\ue372ｭཱི枔睺݉恠辊\ud932⡛ꤌ돈蟓\uef70卋ய⢠랏\ue94b㧠\uef48㵛汝苸", (char) (44820 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), "䥸ꃻᑾ㲯", "\u0000\u0000\u0000\u0000", objArr4);
                                    o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                                    try {
                                        d();
                                        int i7 = i + 99;
                                        a = i7 % 128;
                                        switch (i7 % 2 != 0) {
                                        }
                                    } catch (a e2) {
                                        o.ee.g.c();
                                        Object[] objArr5 = new Object[1];
                                        l(new int[]{1579250210, 532062852, -1722687778, 1855599960, 963302299, 292846602, -690697015, 1976074552, -1202647526, -1141694627, 1653066300, 710492860, 1216921593, -2076853623, -1414356551, 165015591, -2116240371, 789595393, -1651756911, -1962090612}, 41 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr5);
                                        String intern3 = ((String) objArr5[0]).intern();
                                        Object[] objArr6 = new Object[1];
                                        l(new int[]{-494346677, -14733039, -1640351161, 1301054167, 117109967, 1441788103, -1531063802, 648857243, -996657604, 1149784878}, 17 - ((Process.getThreadPriority(0) + 20) >> 6), objArr6);
                                        o.ee.g.a(intern3, ((String) objArr6[0]).intern(), e2);
                                    }
                                    a(context, o.ei.c.c().d().c().a(i()));
                                    return;
                                }
                                return;
                        }
                }
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(int[] r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 1022
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.o.l(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void t(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 712
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.o.t(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
