package o.br;

import android.app.Application;
import android.graphics.PointF;
import android.os.AsyncTask;
import android.view.ViewConfiguration;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.CardDisplay;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import java.util.ArrayList;
import java.util.List;
import kotlin.text.Typography;
import o.bq.c;
import o.dr.a;
import o.dr.d;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\br\e.smali */
public final class e extends AndroidViewModel {
    private static long a;
    private static int f;
    private static int h;
    private static int i = 0;
    private static char j;
    private final int b;
    MutableLiveData<List<HceTransaction>> c;
    private final int d;
    private final c e;

    static {
        f = 1;
        d();
        PointF.length(0.0f, 0.0f);
        ViewConfiguration.getScrollBarSize();
        int i2 = i + 63;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    static void d() {
        j = (char) 64236;
        h = 161105445;
        a = 6565854932352255525L;
    }

    public e(Application application, c cVar, int i2, int i3) {
        super(application);
        this.e = cVar;
        this.b = i2;
        this.d = i3;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0029. Please report as an issue. */
    public final MutableLiveData<List<HceTransaction>> c() {
        int i2 = i + 95;
        f = i2 % 128;
        int i3 = i2 % 2;
        if (this.c == null) {
            this.c = new MutableLiveData<>();
            e();
            int i4 = f + 5;
            i = i4 % 128;
            switch (i4 % 2 != 0 ? 'M' : (char) 18) {
            }
        }
        return this.c;
    }

    private void e() {
        new b(this).execute(new Void[0]);
        int i2 = i + 87;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    final List<HceTransaction> a() {
        o.bq.c cVar = new o.bq.c(getApplication());
        ArrayList arrayList = new ArrayList();
        c.e c = cVar.c(getApplication(), b(), this.b, this.d);
        while (c.hasNext()) {
            try {
                a d = c.d();
                switch (d != null) {
                    case false:
                        break;
                    default:
                        int i2 = i + 61;
                        f = i2 % 128;
                        int i3 = i2 % 2;
                        d(d);
                        arrayList.add(d.L());
                        break;
                }
            } catch (Throwable th) {
                try {
                    c.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
                throw th;
            }
        }
        c.close();
        int i4 = f + 93;
        i = i4 % 128;
        switch (i4 % 2 != 0 ? '#' : (char) 3) {
            case 3:
                return arrayList;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\br\e$b.smali */
    static final class b extends AsyncTask<Void, Void, List<HceTransaction>> {
        private final e c;
        private static int d = 0;
        private static int a = 1;

        @Override // android.os.AsyncTask
        protected final /* synthetic */ List<HceTransaction> doInBackground(Void[] voidArr) {
            int i = a;
            int i2 = ((i | 41) << 1) - (i ^ 41);
            d = i2 % 128;
            switch (i2 % 2 == 0 ? (char) 30 : (char) 1) {
                case 30:
                    List<HceTransaction> c = c();
                    int i3 = a + 69;
                    d = i3 % 128;
                    switch (i3 % 2 != 0 ? (char) 6 : '\\') {
                        case Opcodes.DUP2 /* 92 */:
                            return c;
                        default:
                            throw null;
                    }
                default:
                    c();
                    throw null;
            }
        }

        @Override // android.os.AsyncTask
        protected final /* synthetic */ void onPostExecute(List<HceTransaction> list) {
            int i = d;
            int i2 = (i ^ Opcodes.LUSHR) + ((i & Opcodes.LUSHR) << 1);
            a = i2 % 128;
            int i3 = i2 % 2;
            b(list);
            int i4 = a;
            int i5 = (i4 & 35) + (i4 | 35);
            d = i5 % 128;
            switch (i5 % 2 != 0 ? (char) 24 : Typography.less) {
                case '<':
                    return;
                default:
                    int i6 = 82 / 0;
                    return;
            }
        }

        b(e eVar) {
            this.c = eVar;
        }

        private List<HceTransaction> c() {
            int i = (d + Opcodes.FDIV) - 1;
            a = i % 128;
            switch (i % 2 != 0) {
                case false:
                    this.c.a();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    List<HceTransaction> a2 = this.c.a();
                    int i2 = (a + 32) - 1;
                    d = i2 % 128;
                    int i3 = i2 % 2;
                    return a2;
            }
        }

        private void b(List<HceTransaction> list) {
            int i = a;
            int i2 = (i & 37) + (i | 37);
            d = i2 % 128;
            switch (i2 % 2 != 0 ? (char) 4 : 'E') {
                case 'E':
                    this.c.c.setValue(list);
                    int i3 = a + 43;
                    d = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case false:
                            return;
                        default:
                            throw null;
                    }
                default:
                    this.c.c.setValue(list);
                    throw null;
            }
        }
    }

    private static o.bq.e[] b() {
        int i2 = i + 51;
        f = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return new o.bq.e[]{o.bq.e.e, o.bq.e.a};
            default:
                o.bq.e[] eVarArr = new o.bq.e[5];
                eVarArr[1] = o.bq.e.e;
                eVarArr[0] = o.bq.e.a;
                return eVarArr;
        }
    }

    private void d(a aVar) {
        CardDisplay cardDisplay;
        o.eo.e eVar = this.e.a().j().get(aVar.m());
        Object obj = null;
        if (eVar == null) {
            int i2 = f + 9;
            i = i2 % 128;
            if (i2 % 2 != 0) {
                obj.hashCode();
                throw null;
            }
            cardDisplay = null;
        } else {
            cardDisplay = new CardDisplay(eVar);
        }
        aVar.c(cardDisplay);
        switch (aVar.F() != d.d ? 'I' : ' ') {
            case ' ':
                return;
            default:
                int i3 = i + Opcodes.DMUL;
                f = i3 % 128;
                if (i3 % 2 == 0) {
                    aVar.d();
                    obj.hashCode();
                    throw null;
                }
                switch (aVar.d() != null ? (char) 3 : (char) 30) {
                    case 30:
                        return;
                    default:
                        aVar.d(aVar.d().negate());
                        return;
                }
        }
    }
}
