package o.fj;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fj\c.smali */
public final class c {
    private static int a = 0;
    private static int j = 1;
    private final String b;
    private final Date c;
    private int d;
    private final Date e;

    public c(String str, int i, Date date, Date date2) {
        this.b = str;
        this.d = i;
        this.c = date;
        this.e = date2;
    }

    public final String e() {
        int i = (j + 30) - 1;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 26 : (char) 24) {
            case 26:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final int b() {
        int i = j + Opcodes.LUSHR;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 5 : 'V') {
            case 5:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.d;
        }
    }

    public final void b(int i) {
        int i2 = a + 5;
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        this.d = i;
        int i5 = (i3 + Opcodes.ISHL) - 1;
        a = i5 % 128;
        int i6 = i5 % 2;
    }

    public final Date d() {
        int i = a;
        int i2 = (i & 21) + (i | 21);
        j = i2 % 128;
        int i3 = i2 % 2;
        Date date = this.c;
        int i4 = (i ^ 91) + ((i & 91) << 1);
        j = i4 % 128;
        int i5 = i4 % 2;
        return date;
    }

    public final Date a() {
        int i = a;
        int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
        j = i2 % 128;
        int i3 = i2 % 2;
        Date date = this.e;
        int i4 = (i + Opcodes.FNEG) - 1;
        j = i4 % 128;
        int i5 = i4 % 2;
        return date;
    }
}
