package o.er;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\o.smali */
public final class o extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static long e;
    private static int g;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        g = 1;
        c();
        KeyEvent.normalizeMetaState(0);
        AudioTrack.getMinVolume();
        ExpandableListView.getPackedPositionChild(0L);
        int i = a + 53;
        g = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        b = new char[]{11392, 8271, 13687, 2580, 7963, 27665, 25040, 30463, 19420, 22673, 44477, 41296, 46685, 35684, 38954, 60724, 57915, 63447, 50416, 55698, 11916, 11438, 8260, 13677, 2608, 7964, 27701, 25041, 30452, 19431, 22677, 44464, 41298, 46664, 35701, 38928, 60734, 57895, 63473, 50424, 55685, 11933, 9124, 14155, 1151, 6471, 28160, 25396, 28884, 17801, 23212, 45017, 48260, 45479, 34112, 39547, 61213, 64524, 61793, 50893, 56318, 10409, 15750, 12988, 1605, 6921, 26737, 32016, 29247, 18281, 21716, 43497, 48789, 45960, 32949, 37980, 59697, 65145, 62208, 49197, 54725, 10956, 16371, 3223, 497, 5799, 27200, 32628, 19476, 16713, 22048, 43978, 47330, 36326, 33410, 38832, 60240, 63581, 52580, 49693, 55153, 9316, 14721, 3788, 898, 4236, 26081, 31053, 20089, 17260, 20545, 42301, 47828, 36815, 40160, 37260, 59069, 64445, 52993, 56439, 53534, 9801, 15136, 2252, 7653, 4833, 26561, 29865, 18512, 23901, 21109, 42780, 46115, 35111, 51946, 50691, 54027, 60509, 63825, 35448, 34716, 37049, 44458, 48856, 19453, 18207, 20485, 27960, 32349, 2931, 1130};
        e = -5928017851031871455L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r5, int r6, int r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 2
            int r5 = r5 + 1
            int r6 = r6 * 4
            int r6 = 4 - r6
            int r7 = r7 + 102
            byte[] r0 = o.er.o.$$a
            byte[] r1 = new byte[r5]
            int r5 = r5 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r4 = r6
            r3 = r2
            goto L28
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r5) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            int r3 = r3 + 1
            r4 = r0[r6]
        L28:
            int r6 = r6 + 1
            int r7 = r7 + r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.o.h(short, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{116, -79, 3, -53};
        $$b = 51;
    }

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = a + 21;
        g = i % 128;
        int i2 = i % 2;
        boolean b2 = super.b();
        int i3 = g + 19;
        a = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 15 : 'D') {
            case 'D':
                return b2;
            default:
                int i4 = 63 / 0;
                return b2;
        }
    }

    public o(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    @Override // o.er.h
    public final a[] i() {
        a[] aVarArr;
        int i = a + 55;
        g = i % 128;
        switch (i % 2 == 0 ? '(' : '#') {
            case '(':
                aVarArr = new a[0];
                aVarArr[1] = this.d.l();
                break;
            default:
                aVarArr = new a[]{this.d.l()};
                break;
        }
        int i2 = a + 39;
        g = i2 % 128;
        int i3 = i2 % 2;
        return aVarArr;
    }

    private String a() throws WalletValidationException {
        boolean z;
        String d = this.d.l().d();
        if (d != null) {
            z = true;
        } else {
            z = false;
        }
        switch (z) {
            case true:
                return d;
            default:
                int i = a + 89;
                g = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), Process.myPid() >> 22, 21 - TextUtils.getOffsetAfter("", 0), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 22, Drawable.resolveOpacity(0, 0) + Opcodes.LREM, objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                f((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 58957), 134 - Color.alpha(0), ExpandableListView.getPackedPositionType(0L) + 17, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                int i3 = a + 21;
                g = i3 % 128;
                int i4 = i3 % 2;
                return intern2;
        }
    }

    public final o.v.p e() throws WalletValidationException {
        o.v.p pVar = new o.v.p(a(), this.c, b());
        int i = g + 7;
        a = i % 128;
        switch (i % 2 != 0 ? 'Y' : (char) 27) {
            case 27:
                return pVar;
            default:
                int i2 = 28 / 0;
                return pVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1002
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.o.f(char, int, int, java.lang.Object[]):void");
    }
}
