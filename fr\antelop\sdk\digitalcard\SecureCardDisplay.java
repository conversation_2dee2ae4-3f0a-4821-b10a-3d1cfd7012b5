package fr.antelop.sdk.digitalcard;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.view.View;
import fr.antelop.sdk.CancellationSignal;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.dw.e;
import o.ee.o;
import o.v.i;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCardDisplay.smali */
public final class SecureCardDisplay implements CustomerAuthenticatedProcess {
    private CustomerAuthenticatedProcessActivityCallback activityCallback;
    private Rect anchorCardViewRect;
    private Drawable cardDrawable = null;
    private Integer cardForegroundColor = null;
    private final i<e> innerSecureDigitalCardDisplay;

    public SecureCardDisplay(i<e> iVar) {
        this.innerSecureDigitalCardDisplay = iVar;
    }

    public final SecureCardDisplay setCardBackground(Drawable drawable) {
        this.cardDrawable = drawable;
        return this;
    }

    public final SecureCardDisplay setCardForegroundColor(Integer num) {
        this.cardForegroundColor = num;
        return this;
    }

    public final SecureCardDisplay setAnchorCardView(View view) {
        if (view == null) {
            throw new RuntimeException("Null provided");
        }
        Rect rect = new Rect();
        view.getGlobalVisibleRect(rect);
        this.anchorCardViewRect = rect;
        return this;
    }

    public final SecureCardDisplay setAnchorCardView(Rect rect) {
        if (rect == null) {
            throw new RuntimeException("Null provided");
        }
        this.anchorCardViewRect = rect;
        return this;
    }

    public final SecureCardDisplay setActivityCallback(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.activityCallback = customerAuthenticatedProcessActivityCallback;
        return this;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardDisplay.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardDisplay.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardDisplay.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardDisplay.k();
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        i<e> iVar = this.innerSecureDigitalCardDisplay;
        o.p.e eVar = new o.p.e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardDisplay);
        this.innerSecureDigitalCardDisplay.a();
        iVar.e(context, eVar, new e());
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        if (this.innerSecureDigitalCardDisplay.t() != null) {
            this.cardDrawable = this.innerSecureDigitalCardDisplay.t().b(context);
        }
        i<e> iVar = this.innerSecureDigitalCardDisplay;
        o.p.i iVar2 = new o.p.i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardDisplay);
        this.innerSecureDigitalCardDisplay.a();
        iVar.e(context, iVar2, new e());
    }

    public final CancellationSignal getCancellationSignal() {
        return this.innerSecureDigitalCardDisplay.s();
    }

    public final String getMessage() {
        return null;
    }
}
