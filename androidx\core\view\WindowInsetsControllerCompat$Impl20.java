package androidx.core.view;

import android.os.CancellationSignal;
import android.view.View;
import android.view.Window;
import android.view.animation.Interpolator;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\view\WindowInsetsControllerCompat$Impl20.smali */
public class WindowInsetsControllerCompat$Impl20 extends WindowInsetsControllerCompat$Impl {
    private final SoftwareKeyboardControllerCompat mSoftwareKeyboardControllerCompat;
    protected final Window mWindow;

    WindowInsetsControllerCompat$Impl20(Window window, SoftwareKeyboardControllerCompat softwareKeyboardControllerCompat) {
        this.mWindow = window;
        this.mSoftwareKeyboardControllerCompat = softwareKeyboardControllerCompat;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void show(int typeMask) {
        for (int i = 1; i <= 256; i <<= 1) {
            if ((typeMask & i) != 0) {
                showForType(i);
            }
        }
    }

    private void showForType(int type) {
        switch (type) {
            case 1:
                unsetSystemUiFlag(4);
                unsetWindowFlag(1024);
                break;
            case 2:
                unsetSystemUiFlag(2);
                break;
            case 8:
                this.mSoftwareKeyboardControllerCompat.show();
                break;
        }
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void hide(int typeMask) {
        for (int i = 1; i <= 256; i <<= 1) {
            if ((typeMask & i) != 0) {
                hideForType(i);
            }
        }
    }

    private void hideForType(int type) {
        switch (type) {
            case 1:
                setSystemUiFlag(4);
                break;
            case 2:
                setSystemUiFlag(2);
                break;
            case 8:
                this.mSoftwareKeyboardControllerCompat.hide();
                break;
        }
    }

    protected void setSystemUiFlag(int systemUiFlag) {
        View decorView = this.mWindow.getDecorView();
        decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() | systemUiFlag);
    }

    protected void unsetSystemUiFlag(int systemUiFlag) {
        View decorView = this.mWindow.getDecorView();
        decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & (~systemUiFlag));
    }

    protected void setWindowFlag(int windowFlag) {
        this.mWindow.addFlags(windowFlag);
    }

    protected void unsetWindowFlag(int windowFlag) {
        this.mWindow.clearFlags(windowFlag);
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void controlWindowInsetsAnimation(int types, long durationMillis, Interpolator interpolator, CancellationSignal cancellationSignal, WindowInsetsAnimationControlListenerCompat listener) {
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void setSystemBarsBehavior(int behavior) {
        switch (behavior) {
            case 0:
                unsetSystemUiFlag(6144);
                break;
            case 1:
                unsetSystemUiFlag(4096);
                setSystemUiFlag(2048);
                break;
            case 2:
                unsetSystemUiFlag(2048);
                setSystemUiFlag(4096);
                break;
        }
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    int getSystemBarsBehavior() {
        return 0;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void addOnControllableInsetsChangedListener(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void removeOnControllableInsetsChangedListener(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
    }
}
