package com.google.android.gms.internal.auth;

import java.util.Iterator;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzgm.smali */
final class zzgm implements Iterable {
    zzgm() {
    }

    @Override // java.lang.Iterable
    public final Iterator iterator() {
        Iterator it;
        it = zzgn.zza;
        return it;
    }
}
