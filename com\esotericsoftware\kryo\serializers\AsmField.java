package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.util.Generics;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField.smali */
class Asm<PERSON>ield extends ReflectField {
    public AsmField(Field field, FieldSerializer serializer, Generics.GenericType genericType) {
        super(field, serializer, genericType);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField
    public Object get(Object object) throws IllegalAccessException {
        return this.access.get(object, this.accessIndex);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField
    public void set(Object object, Object value) throws IllegalAccessException {
        this.access.set(object, this.accessIndex, value);
    }

    @Override // com.esotericsoftware.kryo.serializers.ReflectField, com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
    public void copy(Object original, Object copy) {
        try {
            this.access.set(copy, this.accessIndex, this.fieldSerializer.kryo.copy(this.access.get(original, this.accessIndex)));
        } catch (KryoException ex) {
            ex.addTrace(this + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex;
        } catch (Throwable t) {
            KryoException ex2 = new KryoException(t);
            ex2.addTrace(this + " (" + this.fieldSerializer.type.getName() + ")");
            throw ex2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$IntAsmField.smali */
    static final class IntAsmField extends FieldSerializer.CachedField {
        public IntAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            if (this.varEncoding) {
                output.writeVarInt(this.access.getInt(object, this.accessIndex), false);
            } else {
                output.writeInt(this.access.getInt(object, this.accessIndex));
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            if (this.varEncoding) {
                this.access.setInt(object, this.accessIndex, input.readVarInt(false));
            } else {
                this.access.setInt(object, this.accessIndex, input.readInt());
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setInt(copy, this.accessIndex, this.access.getInt(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$FloatAsmField.smali */
    static final class FloatAsmField extends FieldSerializer.CachedField {
        public FloatAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeFloat(this.access.getFloat(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setFloat(object, this.accessIndex, input.readFloat());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setFloat(copy, this.accessIndex, this.access.getFloat(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$ShortAsmField.smali */
    static final class ShortAsmField extends FieldSerializer.CachedField {
        public ShortAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeShort(this.access.getShort(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setShort(object, this.accessIndex, input.readShort());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setShort(copy, this.accessIndex, this.access.getShort(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$ByteAsmField.smali */
    static final class ByteAsmField extends FieldSerializer.CachedField {
        public ByteAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeByte(this.access.getByte(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setByte(object, this.accessIndex, input.readByte());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setByte(copy, this.accessIndex, this.access.getByte(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$BooleanAsmField.smali */
    static final class BooleanAsmField extends FieldSerializer.CachedField {
        public BooleanAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeBoolean(this.access.getBoolean(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setBoolean(object, this.accessIndex, input.readBoolean());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setBoolean(copy, this.accessIndex, this.access.getBoolean(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$CharAsmField.smali */
    static final class CharAsmField extends FieldSerializer.CachedField {
        public CharAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeChar(this.access.getChar(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setChar(object, this.accessIndex, input.readChar());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setChar(copy, this.accessIndex, this.access.getChar(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$LongAsmField.smali */
    static final class LongAsmField extends FieldSerializer.CachedField {
        public LongAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            if (this.varEncoding) {
                output.writeVarLong(this.access.getLong(object, this.accessIndex), false);
            } else {
                output.writeLong(this.access.getLong(object, this.accessIndex));
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            if (this.varEncoding) {
                this.access.setLong(object, this.accessIndex, input.readVarLong(false));
            } else {
                this.access.setLong(object, this.accessIndex, input.readLong());
            }
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setLong(copy, this.accessIndex, this.access.getLong(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$DoubleAsmField.smali */
    static final class DoubleAsmField extends FieldSerializer.CachedField {
        public DoubleAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeDouble(this.access.getDouble(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.setDouble(object, this.accessIndex, input.readDouble());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.setDouble(copy, this.accessIndex, this.access.getDouble(original, this.accessIndex));
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\AsmField$StringAsmField.smali */
    static final class StringAsmField extends FieldSerializer.CachedField {
        public StringAsmField(Field field) {
            super(field);
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void write(Output output, Object object) {
            output.writeString(this.access.getString(object, this.accessIndex));
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void read(Input input, Object object) {
            this.access.set(object, this.accessIndex, input.readString());
        }

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.CachedField
        public void copy(Object original, Object copy) {
            this.access.set(copy, this.accessIndex, this.access.getString(original, this.accessIndex));
        }
    }
}
