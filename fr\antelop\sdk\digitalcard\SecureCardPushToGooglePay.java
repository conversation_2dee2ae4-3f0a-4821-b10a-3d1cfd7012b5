package fr.antelop.sdk.digitalcard;

import android.app.Activity;
import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.Address;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import java.util.List;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCardPushToGooglePay.smali */
public final class SecureCardPushToGooglePay implements CustomerAuthenticatedProcess {
    private final h innerSecureDigitalCardPushToGooglePayProcess;

    public SecureCardPushToGooglePay(h hVar) {
        this.innerSecureDigitalCardPushToGooglePayProcess = hVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardPushToGooglePayProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardPushToGooglePayProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardPushToGooglePayProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardPushToGooglePayProcess.k();
    }

    public final void setUserAddress(Address address) throws WalletValidationException {
        this.innerSecureDigitalCardPushToGooglePayProcess.c(address);
    }

    public final AndroidActivityResultCallback launch(Activity activity, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        return this.innerSecureDigitalCardPushToGooglePayProcess.c(activity, new e(activity, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToGooglePayProcess)).e();
    }

    public final AndroidActivityResultCallback launch(Activity activity, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        return this.innerSecureDigitalCardPushToGooglePayProcess.c(activity, new i(activity, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToGooglePayProcess)).e();
    }

    public final String getMessage() {
        return null;
    }
}
