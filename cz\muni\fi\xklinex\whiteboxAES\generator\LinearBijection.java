package cz.muni.fi.xklinex.whiteboxAES.generator;

import java.io.Serializable;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\generator\LinearBijection.smali */
public class LinearBijection implements Serializable {
    private static final long serialVersionUID = 1495183229858485109L;
    private final GF2MatrixEx mb = new GF2MatrixEx();
    private final GF2MatrixEx inv = new GF2MatrixEx();

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        LinearBijection linearBijection = (LinearBijection) obj;
        return Objects.equals(this.mb, linearBijection.mb) && Objects.equals(this.inv, linearBijection.inv);
    }

    public GF2MatrixEx getInv() {
        return this.inv;
    }

    public GF2MatrixEx getMb() {
        return this.mb;
    }
}
