package org.chromium.support_lib_boundary;

import java.lang.reflect.InvocationHandler;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\chromium\support_lib_boundary\WebMessageCallbackBoundaryInterface.smali */
public interface WebMessageCallbackBoundaryInterface extends FeatureFlagHolderBoundaryInterface {
    void onMessage(InvocationHand<PERSON> invocationHandler, InvocationHandler invocationHandler2);
}
