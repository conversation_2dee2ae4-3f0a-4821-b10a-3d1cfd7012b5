package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s5;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecT113FieldElement.smali */
public class SecT113FieldElement extends ECFieldElement.AbstractF2m {
    protected long[] a;

    public SecT113FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.bitLength() > 113) {
            throw new IllegalArgumentException("x value invalid for SecT113FieldElement");
        }
        this.a = SecT113Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        long[] b = s5.b();
        SecT113Field.add(this.a, ((SecT113FieldElement) eCFieldElement).a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        long[] b = s5.b();
        SecT113Field.addOne(this.a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        return multiply(eCFieldElement.invert());
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecT113FieldElement) {
            return s5.b(this.a, ((SecT113FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecT113Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Opcodes.LREM;
    }

    public int getK1() {
        return 9;
    }

    public int getK2() {
        return 0;
    }

    public int getK3() {
        return 0;
    }

    public int getM() {
        return Opcodes.LREM;
    }

    public int getRepresentation() {
        return 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public ECFieldElement halfTrace() {
        long[] b = s5.b();
        SecT113Field.halfTrace(this.a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public boolean hasFastTrace() {
        return true;
    }

    public int hashCode() {
        return Arrays.hashCode(this.a, 0, 2) ^ 113009;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        long[] b = s5.b();
        SecT113Field.invert(this.a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return s5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return s5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        long[] b = s5.b();
        SecT113Field.multiply(this.a, ((SecT113FieldElement) eCFieldElement).a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        return multiplyPlusProduct(eCFieldElement, eCFieldElement2, eCFieldElement3);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT113FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT113FieldElement) eCFieldElement2).a;
        long[] jArr4 = ((SecT113FieldElement) eCFieldElement3).a;
        long[] d = s5.d();
        SecT113Field.multiplyAddToExt(jArr, jArr2, d);
        SecT113Field.multiplyAddToExt(jArr3, jArr4, d);
        long[] b = s5.b();
        SecT113Field.reduce(d, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        return this;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        long[] b = s5.b();
        SecT113Field.sqrt(this.a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        long[] b = s5.b();
        SecT113Field.square(this.a, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return squarePlusProduct(eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT113FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT113FieldElement) eCFieldElement2).a;
        long[] d = s5.d();
        SecT113Field.squareAddToExt(jArr, d);
        SecT113Field.multiplyAddToExt(jArr2, jArr3, d);
        long[] b = s5.b();
        SecT113Field.reduce(d, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePow(int i) {
        if (i < 1) {
            return this;
        }
        long[] b = s5.b();
        SecT113Field.squareN(this.a, i, b);
        return new SecT113FieldElement(b);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        return add(eCFieldElement);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return (this.a[0] & 1) != 0;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return s5.c(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public int trace() {
        return SecT113Field.trace(this.a);
    }

    public SecT113FieldElement() {
        this.a = s5.b();
    }

    protected SecT113FieldElement(long[] jArr) {
        this.a = jArr;
    }
}
