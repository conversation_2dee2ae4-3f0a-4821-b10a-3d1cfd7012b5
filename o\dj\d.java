package o.dj;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.SystemClock;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.transaction.TransactionDecision;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import fr.antelop.sdk.transaction.hce.HceTransactionDeclineReason;
import fr.antelop.sdk.transaction.hce.HceTransactionStatus;
import fr.antelop.sdk.transaction.hce.HceTransactionStep;
import fr.antelop.sdk.transaction.hce.HceTransactionUnknownReason;
import fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback;
import java.util.Date;
import java.util.List;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dj\d.smali */
abstract class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static long d;
    private final WalletHceTransactionCallback a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        c();
        ViewConfiguration.getPressedStateDuration();
        ViewConfiguration.getMaximumDrawingCacheSize();
        int i = c + 89;
        b = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void c() {
        d = 3806684888048016104L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x003b). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r5, byte r6, short r7, java.lang.Object[] r8) {
        /*
            byte[] r0 = o.dj.d.$$a
            int r5 = r5 * 3
            int r5 = 3 - r5
            int r6 = r6 * 4
            int r6 = 1 - r6
            int r7 = r7 * 2
            int r7 = r7 + 112
            byte[] r1 = new byte[r6]
            r2 = -1
            int r6 = r6 + r2
            if (r0 != 0) goto L1b
            r7 = r5
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r6
            goto L3b
        L1b:
            r4 = r6
            r6 = r5
            r5 = r7
            r7 = r4
        L1f:
            int r2 = r2 + 1
            byte r3 = (byte) r5
            r1[r2] = r3
            if (r2 != r7) goto L2f
            java.lang.String r5 = new java.lang.String
            r6 = 0
            r5.<init>(r1, r6)
            r8[r6] = r5
            return
        L2f:
            int r6 = r6 + 1
            r3 = r0[r6]
            r4 = r7
            r7 = r6
            r6 = r3
            r3 = r2
            r2 = r1
            r1 = r0
            r0 = r8
            r8 = r4
        L3b:
            int r5 = r5 + r6
            r6 = r7
            r7 = r8
            r8 = r0
            r0 = r1
            r1 = r2
            r2 = r3
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.d.g(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{71, -50, -52, -118};
        $$b = 80;
    }

    d(Context context) {
        this.a = e(context);
    }

    private WalletHceTransactionCallback e(Context context) {
        Object e;
        int i = c + 51;
        b = i % 128;
        try {
            switch (i % 2 == 0 ? '!' : Typography.amp) {
                case '&':
                    Object[] objArr = new Object[1];
                    f("\udc1e掍ꍘ\ue28c∊憯ꄷ\ue0a5 枷꜐\ue6dd♏旆ꕎ\ue4e3⑩毡ꭵ\ueb1c⪗樟ꦋ\ue936⢾栄꾫\uef37⻊湚귉\ued44", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 49031, objArr);
                    String a = o.a(context, ((String) objArr[0]).intern());
                    Object[] objArr2 = new Object[1];
                    f("\udc1e掍ꍘ\ue28c∊憯ꄷ\ue0a5 枷꜐\ue6dd♏旆ꕎ\ue4e3⑩毡ꭵ\ueb1c⪗樟ꦋ\ue936⢾栄꾫\uef37⻊湚귉\ued44", 49031 - View.resolveSize(0, 0), objArr2);
                    e = o.e(WalletHceTransactionCallback.class, a, ((String) objArr2[0]).intern());
                    break;
                default:
                    Object[] objArr3 = new Object[1];
                    f("\udc1e掍ꍘ\ue28c∊憯ꄷ\ue0a5 枷꜐\ue6dd♏旆ꕎ\ue4e3⑩毡ꭵ\ueb1c⪗樟ꦋ\ue936⢾栄꾫\uef37⻊湚귉\ued44", (ViewConfiguration.getDoubleTapTimeout() - 92) * 49031, objArr3);
                    String a2 = o.a(context, ((String) objArr3[0]).intern());
                    Object[] objArr4 = new Object[1];
                    f("\udc1e掍ꍘ\ue28c∊憯ꄷ\ue0a5 枷꜐\ue6dd♏旆ꕎ\ue4e3⑩毡ꭵ\ueb1c⪗樟ꦋ\ue936⢾栄꾫\uef37⻊湚귉\ued44", 49031 >> View.resolveSize(0, 1), objArr4);
                    e = o.e(WalletHceTransactionCallback.class, a2, ((String) objArr4[0]).intern());
                    break;
            }
            return (WalletHceTransactionCallback) e;
        } catch (PackageManager.NameNotFoundException | RuntimeException e2) {
            g.c();
            Object[] objArr5 = new Object[1];
            f("\udc31㒫൬昪绾垁꡵脶駄\uf2af쭻\udc09㓗\u0d80晍缟埁ꢚ腜騼\uf2dd쮕\udc2a㓡ඡ普缡埖ꢶ腢", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 59580, objArr5);
            g.e(((String) objArr5[0]).intern(), e2.getMessage());
            return new WalletHceTransactionCallback() { // from class: o.dj.d.3
                private static int e = 0;
                private static int b = 1;

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onCustomerCredentialsRequired(Context context2, List<CustomerAuthenticationMethod> list, HceTransaction hceTransaction) {
                    int i2 = (b + 104) - 1;
                    e = i2 % 128;
                    switch (i2 % 2 != 0) {
                        case false:
                            break;
                        default:
                            int i3 = 87 / 0;
                            break;
                    }
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onDecline(Context context2, HceTransactionDeclineReason hceTransactionDeclineReason, AntelopError antelopError, HceTransaction hceTransaction) {
                    int i2 = b;
                    int i3 = (i2 & 63) + (i2 | 63);
                    e = i3 % 128;
                    switch (i3 % 2 == 0) {
                        case false:
                            int i4 = 99 / 0;
                            break;
                    }
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onPending(Context context2, HceTransaction hceTransaction) {
                    int i2 = b;
                    int i3 = (i2 & 71) + (i2 | 71);
                    e = i3 % 128;
                    switch (i3 % 2 != 0 ? '6' : 'Z') {
                        case Opcodes.ISTORE /* 54 */:
                            int i4 = 7 / 0;
                            break;
                    }
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onProgress(Context context2, HceTransactionStep hceTransactionStep) {
                    int i2 = b;
                    int i3 = ((i2 | 31) << 1) - (i2 ^ 31);
                    e = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            throw null;
                        default:
                            return;
                    }
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onSuccess(Context context2, HceTransactionStatus hceTransactionStatus, HceTransaction hceTransaction) {
                    int i2 = b;
                    int i3 = (i2 ^ Opcodes.DSUB) + ((i2 & Opcodes.DSUB) << 1);
                    e = i3 % 128;
                    switch (i3 % 2 != 0 ? (char) 28 : '\t') {
                        case 28:
                            throw null;
                        default:
                            return;
                    }
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final void onUnknownResult(Context context2, HceTransactionUnknownReason hceTransactionUnknownReason, HceTransaction hceTransaction) {
                    int i2 = e;
                    int i3 = ((i2 | 39) << 1) - (i2 ^ 39);
                    b = i3 % 128;
                    int i4 = i3 % 2;
                }

                @Override // fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback
                public final TransactionDecision onFinalization(Context context2, CustomerAuthenticationMethod customerAuthenticationMethod, Date date, HceTransaction hceTransaction) {
                    int i2 = b;
                    int i3 = ((i2 | 53) << 1) - (i2 ^ 53);
                    int i4 = i3 % 128;
                    e = i4;
                    int i5 = i3 % 2;
                    int i6 = (i4 & 59) + (i4 | 59);
                    b = i6 % 128;
                    int i7 = i6 % 2;
                    return null;
                }
            };
        }
    }

    private WalletHceTransactionCallback b() {
        int i = c;
        int i2 = i + 47;
        b = i2 % 128;
        int i3 = i2 % 2;
        WalletHceTransactionCallback walletHceTransactionCallback = this.a;
        if (walletHceTransactionCallback == null) {
            Object[] objArr = new Object[1];
            f("\udc30ヒ֏ᨃ漈䏧傯ꕩ멃輈\ue3c1\uf0af약\uda22⻨ΟႫ敀稶仿ꎮ끤蕝騜\uee80쎷큽┿㦤ໍ掄灀䔨姴꺸药遅\ue555劉캉⍹〽ӫ᧚溘䍄倜ꓺ리", (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 60616, objArr);
            throw new RuntimeException(((String) objArr[0]).intern());
        }
        int i4 = i + 3;
        b = i4 % 128;
        int i5 = i4 % 2;
        return walletHceTransactionCallback;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public void e(android.content.Context r5, java.util.List<o.dr.a> r6) {
        /*
            r4 = this;
            int r0 = o.dj.d.c
            int r0 = r0 + 73
            int r1 = r0 % 128
            o.dj.d.b = r1
            int r0 = r0 % 2
            fr.antelop.sdk.transaction.hce.WalletHceTransactionCallback r0 = r4.b()
            boolean r1 = r0 instanceof o.dj.b
            if (r1 == 0) goto L74
            java.util.HashMap r1 = new java.util.HashMap
            r1.<init>()
            java.util.Iterator r6 = r6.iterator()
            int r2 = o.dj.d.b
            int r2 = r2 + 53
            int r3 = r2 % 128
            o.dj.d.c = r3
            int r2 = r2 % 2
        L27:
            boolean r2 = r6.hasNext()
            if (r2 == 0) goto L2f
            r2 = 1
            goto L30
        L2f:
            r2 = 0
        L30:
            switch(r2) {
                case 1: goto L39;
                default: goto L33;
            }
        L33:
            o.dj.b r0 = (o.dj.b) r0
            r0.onTransactionsUpdated(r5, r1)
            goto L74
        L39:
            int r2 = o.dj.d.b
            int r2 = r2 + 43
            int r3 = r2 % 128
            o.dj.d.c = r3
            int r2 = r2 % 2
            if (r2 == 0) goto L48
            r2 = 85
            goto L49
        L48:
            r2 = 5
        L49:
            switch(r2) {
                case 5: goto L5e;
                default: goto L4c;
            }
        L4c:
            java.lang.Object r5 = r6.next()
            o.dr.a r5 = (o.dr.a) r5
            java.lang.String r6 = r5.x()
            fr.antelop.sdk.transaction.hce.HceTransaction r5 = r5.L()
            r1.put(r6, r5)
            goto L70
        L5e:
            java.lang.Object r2 = r6.next()
            o.dr.a r2 = (o.dr.a) r2
            java.lang.String r3 = r2.x()
            fr.antelop.sdk.transaction.hce.HceTransaction r2 = r2.L()
            r1.put(r3, r2)
            goto L27
        L70:
            r5 = 0
            throw r5     // Catch: java.lang.Throwable -> L72
        L72:
            r5 = move-exception
            throw r5
        L74:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.d.e(android.content.Context, java.util.List):void");
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /* JADX WARN: Removed duplicated region for block: B:42:0x0138  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r18, int r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 482
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dj.d.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
