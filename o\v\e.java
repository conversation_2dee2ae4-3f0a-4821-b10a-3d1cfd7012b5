package o.v;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.SecurePinInput;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import kotlin.text.Typography;
import o.aa.a;
import o.ed.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\e.smali */
public final class e extends d {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long m;
    private static int p;
    private static int q;
    boolean h;
    CustomerAuthenticatedProcessActivityCallback i;
    private final o.eo.e k;
    private final boolean l;

    /* renamed from: o, reason: collision with root package name */
    private SecurePinInput f102o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        p = 0;
        q = 1;
        s();
        Color.rgb(0, 0, 0);
        int i = p + Opcodes.DSUB;
        q = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{13, -73, -57, -113};
        $$e = 16;
    }

    static void s() {
        m = 3385255724940447628L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            byte[] r0 = o.v.e.$$d
            int r8 = r8 * 2
            int r8 = r8 + 112
            int r6 = r6 * 2
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r8
            r4 = r2
            r8 = r7
            r7 = r6
            goto L30
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L30:
            int r3 = -r3
            int r6 = r6 + r3
            int r8 = r8 + 1
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.e.v(short, int, short, java.lang.Object[]):void");
    }

    static /* synthetic */ void b(e eVar) {
        int i = q + Opcodes.DREM;
        p = i % 128;
        char c = i % 2 != 0 ? (char) 1 : '\n';
        eVar.n();
        switch (c) {
            case 1:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final boolean a() {
        int i = p + 55;
        q = i % 128;
        char c = i % 2 == 0 ? 'O' : '7';
        boolean z = this.h;
        switch (c) {
            default:
                int i2 = 69 / 0;
            case '7':
                return z;
        }
    }

    public e(String str, o.eo.e eVar, boolean z) {
        super(str, eVar);
        this.l = z;
        this.k = eVar;
    }

    @Override // o.p.h
    public final String d() {
        int i = q + 3;
        p = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        u("य़ꂨ嫝\uf4d6긛塕\uf263궅䞵\uf1c9\uab0f䔠～ꥊ䂧龎铺东\uf83d鉢", TextUtils.lastIndexOf("", '0', 0, 0) + 43482, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = q + 39;
        p = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x007e  */
    @Override // o.p.h
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(final android.content.Context r10, final o.ei.c r11, final o.h.d r12) {
        /*
            r9 = this;
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getMinimumFlingVelocity()
            int r0 = r0 >> 16
            r1 = 47491(0xb983, float:6.6549E-41)
            int r0 = r0 + r1
            r1 = 1
            java.lang.Object[] r2 = new java.lang.Object[r1]
            java.lang.String r3 = "ॕ냱穴◰\uef62雀偫ᯪ앱賵㙧\uf1fe뭕拈ⱷퟒ酘壆ɜ췄睔㻊\uf831ꎷ"
            u(r3, r0, r2)
            r0 = 0
            r2 = r2[r0]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.String r3 = ""
            r4 = 48
            int r3 = android.text.TextUtils.indexOf(r3, r4, r0)
            r4 = 63210(0xf6ea, float:8.8576E-41)
            int r4 = r4 - r3
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r3 = "८ﾂ\ue4a4\ued8d틂\udbe4샽줔븷Ꜭ"
            u(r3, r4, r1)
            r1 = r1[r0]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r2, r1)
            o.p.g r1 = r9.l()
            o.v.e$2 r2 = new o.v.e$2
            r3 = r2
            r4 = r9
            r5 = r1
            r6 = r10
            r7 = r11
            r8 = r12
            r3.<init>()
            fr.antelop.sdk.digitalcard.SecurePinInput r3 = r9.f102o
            if (r3 == 0) goto L88
            int r4 = o.v.e.q
            int r4 = r4 + 109
            int r5 = r4 % 128
            o.v.e.p = r5
            int r4 = r4 % 2
            if (r4 == 0) goto L6f
            fr.antelop.sdk.digitalcard.SecurePinInput$NewPinInputProperties r3 = r3.getNewPinInputProperties()
            r4 = 5
            int r4 = r4 / r0
            if (r3 == 0) goto L67
            r3 = 18
            goto L69
        L67:
            r3 = 15
        L69:
            switch(r3) {
                case 15: goto L7d;
                default: goto L6c;
            }
        L6c:
            goto L7e
        L6d:
            r10 = move-exception
            throw r10
        L6f:
            fr.antelop.sdk.digitalcard.SecurePinInput$NewPinInputProperties r3 = r3.getNewPinInputProperties()
            if (r3 == 0) goto L78
            r3 = 51
            goto L7a
        L78:
            r3 = 14
        L7a:
            switch(r3) {
                case 51: goto L7e;
                default: goto L7d;
            }
        L7d:
            goto L88
        L7e:
            fr.antelop.sdk.digitalcard.SecurePinInput r11 = r9.f102o
            fr.antelop.sdk.digitalcard.SecurePinInput$NewPinInputProperties r11 = r11.getNewPinInputProperties()
            r9.c(r10, r11, r2)
            return
        L88:
            r4 = 0
            r3 = r9
            r5 = r10
            r6 = r1
            r7 = r11
            r8 = r12
            r3.a(r4, r5, r6, r7, r8)
            int r10 = o.v.e.q
            int r10 = r10 + 25
            int r11 = r10 % 128
            o.v.e.p = r11
            int r10 = r10 % 2
            if (r10 == 0) goto La0
            r10 = 36
            goto La2
        La0:
            r10 = 41
        La2:
            switch(r10) {
                case 41: goto La6;
                default: goto La5;
            }
        La5:
            goto La7
        La6:
            return
        La7:
            r10 = 88
            int r10 = r10 / r0
            return
        Lab:
            r10 = move-exception
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.e.a(android.content.Context, o.ei.c, o.h.d):void");
    }

    final void a(byte[] bArr, Context context, final o.p.g gVar, o.ei.c cVar, o.h.d dVar) {
        new o.aa.a(context, new a.d() { // from class: o.v.e.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int c;
            private static int d;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                c = 0;
                a = 1;
                d = 874635497;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(byte r8, int r9, byte r10, java.lang.Object[] r11) {
                /*
                    int r10 = r10 * 4
                    int r10 = r10 + 4
                    int r8 = r8 * 2
                    int r8 = 109 - r8
                    byte[] r0 = o.v.e.AnonymousClass4.$$a
                    int r9 = r9 * 2
                    int r9 = r9 + 1
                    byte[] r1 = new byte[r9]
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r8 = r9
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r11
                    r11 = r10
                    goto L35
                L1a:
                    r3 = r2
                L1b:
                    int r4 = r3 + 1
                    byte r5 = (byte) r8
                    r1[r3] = r5
                    if (r4 != r9) goto L2a
                    java.lang.String r8 = new java.lang.String
                    r8.<init>(r1, r2)
                    r11[r2] = r8
                    return
                L2a:
                    r3 = r0[r10]
                    r6 = r9
                    r9 = r8
                    r8 = r6
                    r7 = r11
                    r11 = r10
                    r10 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r7
                L35:
                    int r10 = -r10
                    int r9 = r9 + r10
                    int r10 = r11 + 1
                    r11 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r6 = r9
                    r9 = r8
                    r8 = r6
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.e.AnonymousClass4.g(byte, int, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{59, -48, 125, -69};
                $$b = 214;
            }

            @Override // o.aa.a.d
            public final void e(Boolean bool) {
                int i = c + 25;
                a = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f((KeyEvent.getMaxKeyCode() >> 16) + 20, "\f￭\uffff�\u000f\f\uffff\uffdd\u0007\rￛ�\u000e\u0003\u0010\ufffb\u000e\u0003\t\b￣\b\b\uffff", 24 - Gravity.getAbsoluteGravity(0, 0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 252, false, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f(3 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), "\t\n\u000e\u000e\u0000\ufffe\ufffe\u0010￮\uffff\r￼\uffde\u0007￼\u000f\u0004\u0002\u0004\uffdf\u0000\u000f￼\u0011\u0004\u000f\ufffeￜ", 27 - ((byte) KeyEvent.getModifierMetaStateMask()), 251 - (ViewConfiguration.getPressedStateDuration() >> 16), true, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                e.this.h = bool.booleanValue();
                o.p.g gVar2 = gVar;
                switch (gVar2 != null ? '*' : '2') {
                    case '2':
                        break;
                    default:
                        int i3 = c + 23;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                        gVar2.onProcessSuccess();
                        break;
                }
            }

            @Override // o.aa.a.d
            public final void e(o.bb.d dVar2) {
                int i = c + 93;
                a = i % 128;
                int i2 = i % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f(20 - View.resolveSize(0, 0), "\f￭\uffff�\u000f\f\uffff\uffdd\u0007\rￛ�\u000e\u0003\u0010\ufffb\u000e\u0003\t\b￣\b\b\uffff", 23 - ((byte) KeyEvent.getModifierMetaStateMask()), Drawable.resolveOpacity(0, 0) + 252, false, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f(28 - (ViewConfiguration.getScrollBarSize() >> 8), "\u0000\r\u0010\u0007\u0004￼￡\uffff\r￼\uffde\u0007￼\u000f\u0004\u0002\u0004\uffdf\u0000\u000f￼\u0011\u0004\u000f\ufffeￜ\t\n", 27 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 299 - AndroidCharacter.getMirror('0'), true, objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                o.bv.c c2 = o.bv.c.c(dVar2);
                switch (gVar != null ? (char) 21 : '.') {
                    case 21:
                        int i3 = a + 83;
                        c = i3 % 128;
                        int i4 = i3 % 2;
                        switch (dVar2.d() == o.bb.a.aA ? 'A' : (char) 7) {
                            case 7:
                                gVar.onError(c2);
                                return;
                            default:
                                int i5 = a + 41;
                                c = i5 % 128;
                                switch (i5 % 2 == 0 ? '+' : (char) 27) {
                                    case '+':
                                        e.b(e.this);
                                        gVar.onAuthenticationDeclined();
                                        return;
                                    default:
                                        e.b(e.this);
                                        gVar.onAuthenticationDeclined();
                                        throw null;
                                }
                        }
                    default:
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(int r18, java.lang.String r19, int r20, int r21, boolean r22, java.lang.Object[] r23) {
                /*
                    Method dump skipped, instructions count: 534
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.v.e.AnonymousClass4.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
            }
        }, cVar).c(bArr, dVar, o(), this.k);
        int i = q + 55;
        p = i % 128;
        switch (i % 2 != 0 ? (char) 6 : '?') {
            case 6:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = p + 15;
        q = i % 128;
        int i2 = i % 2;
        if (!this.l) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            u("य़ꂨ嫝\uf4d6긛塕\uf263궅䞵\uf1c9\uab0f䔠～ꥊ䂧龎铺东\uf83d鉢", 43480 - TextUtils.indexOf((CharSequence) "", '0'), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            u("ै爫ￇ笡\ue424憮\ued41囬펐弪\ud8c6䑊섉䪽뙊㎭벌", Color.red(0) + 31583, objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(this.k.e());
            Object[] objArr3 = new Object[1];
            u("ॼ⭟䶾湚胵ꊀ읮劉ᩫ㰓廢献闍뙫\ue806ા⽞䇻揊萤ꛃ\udb77ﷷᾏ〵勃睷ꥍ쮬\uec42\u0ee1₈䔈枾顖몵\udc93\uf132Ꮬ㑭", TextUtils.getOffsetBefore("", 0) + 8803, objArr3);
            throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
        }
        switch (this.k.z() == CardStatus.Active ? 'c' : 'T') {
            case Opcodes.BASTORE /* 84 */:
                break;
            default:
                if (this.k.a().booleanValue()) {
                    int i3 = p + Opcodes.DNEG;
                    q = i3 % 128;
                    switch (i3 % 2 == 0 ? 'B' : ',') {
                        case ',':
                            return;
                        default:
                            throw null;
                    }
                }
                break;
        }
        WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
        Object[] objArr4 = new Object[1];
        u("य़\ue8f4쩼귣", TextUtils.lastIndexOf("", '0', 0, 0) + 57738, objArr4);
        throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
    }

    private void c(final Context context, SecurePinInput.NewPinInputProperties newPinInputProperties, c.b bVar) {
        new o.ed.c(bVar, null, new c.e(context, newPinInputProperties), new CustomerAuthenticatedProcessActivityCallback() { // from class: o.v.e.5
            private static int a = 0;
            private static int e = 1;

            @Override // fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback
            public final void onActivityStart(Context context2) {
                int i = e;
                int i2 = ((i | 77) << 1) - (i ^ 77);
                a = i2 % 128;
                int i3 = i2 % 2;
                switch (e.this.i != null ? (char) 29 : 'W') {
                    case 29:
                        int i4 = a;
                        int i5 = (i4 & Opcodes.LUSHR) + (i4 | Opcodes.LUSHR);
                        e = i5 % 128;
                        char c = i5 % 2 == 0 ? Typography.quote : 'M';
                        e.this.i.onActivityStart(context);
                        switch (c) {
                            case 'M':
                                int i6 = (a + 68) - 1;
                                e = i6 % 128;
                                if (i6 % 2 == 0) {
                                    return;
                                } else {
                                    return;
                                }
                            default:
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                        }
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback
            public final void onActivityStop() {
                int i = e;
                int i2 = ((i | 61) << 1) - (i ^ 61);
                a = i2 % 128;
                int i3 = i2 % 2;
                switch (e.this.i == null) {
                    case false:
                        int i4 = a;
                        int i5 = ((i4 | 27) << 1) - (i4 ^ 27);
                        e = i5 % 128;
                        int i6 = i5 % 2;
                        e.this.i.onActivityStop();
                        int i7 = e;
                        int i8 = ((i7 | 43) << 1) - (i7 ^ 43);
                        a = i8 % 128;
                        int i9 = i8 % 2;
                        break;
                }
                int i10 = a;
                int i11 = ((i10 | 89) << 1) - (i10 ^ 89);
                e = i11 % 128;
                int i12 = i11 % 2;
            }
        }).b(context);
        int i = p + Opcodes.LNEG;
        q = i % 128;
        int i2 = i % 2;
    }

    public final void b(Context context, SecurePinInput securePinInput, o.p.g gVar) throws WalletValidationException {
        int i = q + 45;
        p = i % 128;
        switch (i % 2 != 0) {
            case true:
                d(securePinInput);
                d(context, gVar);
                throw null;
            default:
                d(securePinInput);
                d(context, gVar);
                int i2 = p + 99;
                q = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
        }
    }

    public final void e(Context context, SecurePinInput securePinInput, o.p.i iVar) throws WalletValidationException {
        int i = q + 11;
        p = i % 128;
        switch (i % 2 != 0 ? '_' : '4') {
            case Opcodes.SWAP /* 95 */:
                d(securePinInput);
                d(context, iVar);
                int i2 = 58 / 0;
                return;
            default:
                d(securePinInput);
                d(context, iVar);
                return;
        }
    }

    private void d(SecurePinInput securePinInput) throws WalletValidationException {
        int i = p + 95;
        q = i % 128;
        switch (i % 2 == 0) {
            case false:
                this.f102o = securePinInput;
                boolean d = ((o.er.e) this.k.H()).h().d();
                if (securePinInput != null) {
                    if (!d) {
                        WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
                        Object[] objArr = new Object[1];
                        u("ॏ♞圱萜뗲\ue2baᎦ䍤灊ꄊ\udef4࿁㲽涓", 12070 - Process.getGidForName(""), objArr);
                        throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                    }
                    if (securePinInput.getCurrentPinInputProperties() != null) {
                        WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.InvalidFormat;
                        Object[] objArr2 = new Object[1];
                        u("ॏ♞圱萜뗲\ue2baᎦ䍤灊ꄊ\udef4࿁㲽涓", TextUtils.indexOf("", "", 0) + 12071, objArr2);
                        throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
                    }
                } else if (d) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr3 = new Object[1];
                    u("ॏ♞圱萜뗲\ue2baᎦ䍤灊ꄊ\udef4࿁㲽涓", (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 12071, objArr3);
                    throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr3[0]).intern());
                }
                int i2 = p + 39;
                q = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                this.f102o = securePinInput;
                ((o.er.e) this.k.H()).h().d();
                throw null;
        }
    }

    public final void d(CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        int i = p;
        int i2 = i + 9;
        q = i2 % 128;
        int i3 = i2 % 2;
        this.i = customerAuthenticatedProcessActivityCallback;
        int i4 = i + 79;
        q = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 494
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.e.u(java.lang.String, int, java.lang.Object[]):void");
    }
}
