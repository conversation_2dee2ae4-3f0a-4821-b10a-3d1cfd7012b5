package androidx.navigation;

import android.os.Bundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\navigation\NavBackStackEntry.smali */
final class NavBackStackEntry {
    private final Bundle mArgs;
    private final NavDestination mDestination;

    NavBackStackEntry(NavDestination destination, Bundle args) {
        this.mDestination = destination;
        this.mArgs = args;
    }

    public NavDestination getDestination() {
        return this.mDestination;
    }

    public Bundle getArguments() {
        return this.mArgs;
    }
}
