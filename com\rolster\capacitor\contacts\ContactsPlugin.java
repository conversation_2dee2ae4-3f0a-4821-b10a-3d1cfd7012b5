package com.rolster.capacitor.contacts;

import android.content.ContentResolver;
import android.database.Cursor;
import android.provider.ContactsContract;
import android.util.Base64;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.PermissionState;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;
import com.getcapacitor.annotation.PermissionCallback;
import java.util.HashMap;
import org.json.JSONException;

@CapacitorPlugin(name = "Contacts", permissions = {@Permission(alias = ContactsPlugin.READ_CONTACTS, strings = {"android.permission.READ_CONTACTS"})})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\rolster\capacitor\contacts\ContactsPlugin.smali */
public class ContactsPlugin extends Plugin {
    private static final String BIRTHDAY = "birthday";
    private static final String CONTACT_ID = "contactId";
    private static final String DISPLAY_NAME = "displayName";
    private static final String EMAILS = "emails";
    private static final String EMAIL_ADDRESS = "address";
    private static final String EMAIL_LABEL = "label";
    private static final String ORGANIZATION_NAME = "organizationName";
    private static final String ORGANIZATION_ROLE = "organizationRole";
    private static final String PHONE_LABEL = "label";
    private static final String PHONE_NUMBER = "number";
    private static final String PHONE_NUMBERS = "phoneNumbers";
    private static final String PHOTO_THUMBNAIL = "photoThumbnail";
    public static final String READ_CONTACTS = "readContacts";

    @PluginMethod
    public void checkPermission(PluginCall call) {
        if (!canReadContactsPermisionGranted()) {
            requestPermissions(call);
            return;
        }
        JSObject result = new JSObject();
        result.put("granted", true);
        call.resolve(result);
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    @PluginMethod
    public void request(PluginCall call) {
        JSArray jsContacts;
        String[] selectionArgs;
        ContentResolver contentResolver;
        JSObject jsContact;
        JSArray jsContacts2;
        HashMap<Object, JSObject> contactsById;
        String selection;
        char c;
        JSArray jsContacts3 = new JSArray();
        ContentResolver contentResolver2 = getContext().getContentResolver();
        String[] projection = {"mimetype", "data4", "_id", "contact_id", "display_name", "data15", "data1", "data2", "data3"};
        String selection2 = "mimetype in (?, ?, ?, ?, ?)";
        String[] selectionArgs2 = {"vnd.android.cursor.item/email_v2", "vnd.android.cursor.item/phone_v2", "vnd.android.cursor.item/contact_event", "vnd.android.cursor.item/organization", "vnd.android.cursor.item/photo"};
        Cursor contactsCursor = contentResolver2.query(ContactsContract.Data.CONTENT_URI, projection, "mimetype in (?, ?, ?, ?, ?)", selectionArgs2, null);
        if (contactsCursor == null || contactsCursor.getCount() <= 0) {
            jsContacts = jsContacts3;
        } else {
            HashMap<Object, JSObject> contactsById2 = new HashMap<>();
            while (contactsCursor.moveToNext()) {
                contactsCursor.getString(contactsCursor.getColumnIndex("_id"));
                String contactId = contactsCursor.getString(contactsCursor.getColumnIndex("contact_id"));
                JSObject jsContact2 = new JSObject();
                String[] projection2 = projection;
                if (!contactsById2.containsKey(contactId)) {
                    selectionArgs = selectionArgs2;
                    jsContact2.put(CONTACT_ID, contactId);
                    String displayName = contactsCursor.getString(contactsCursor.getColumnIndex("display_name"));
                    contentResolver = contentResolver2;
                    jsContact2.put(DISPLAY_NAME, displayName);
                    JSArray jsPhoneNumbers = new JSArray();
                    jsContact2.put(PHONE_NUMBERS, (Object) jsPhoneNumbers);
                    JSArray jsEmailAddresses = new JSArray();
                    jsContact2.put(EMAILS, (Object) jsEmailAddresses);
                    jsContacts3.put(jsContact2);
                    jsContact = jsContact2;
                } else {
                    selectionArgs = selectionArgs2;
                    contentResolver = contentResolver2;
                    jsContact = contactsById2.get(contactId);
                }
                if (jsContact == null) {
                    jsContacts2 = jsContacts3;
                    contactsById = contactsById2;
                    selection = selection2;
                } else {
                    String mimeType = contactsCursor.getString(contactsCursor.getColumnIndex("mimetype"));
                    selection = selection2;
                    String data = contactsCursor.getString(contactsCursor.getColumnIndex("data1"));
                    jsContacts2 = jsContacts3;
                    int type = contactsCursor.getInt(contactsCursor.getColumnIndex("data2"));
                    HashMap<Object, JSObject> contactsById3 = contactsById2;
                    String label = contactsCursor.getString(contactsCursor.getColumnIndex("data3"));
                    switch (mimeType.hashCode()) {
                        case -1569536764:
                            if (mimeType.equals("vnd.android.cursor.item/email_v2")) {
                                c = 0;
                                break;
                            }
                            c = 65535;
                            break;
                        case -1328682538:
                            if (mimeType.equals("vnd.android.cursor.item/contact_event")) {
                                c = 2;
                                break;
                            }
                            c = 65535;
                            break;
                        case 684173810:
                            if (mimeType.equals("vnd.android.cursor.item/phone_v2")) {
                                c = 1;
                                break;
                            }
                            c = 65535;
                            break;
                        case 689862072:
                            if (mimeType.equals("vnd.android.cursor.item/organization")) {
                                c = 3;
                                break;
                            }
                            c = 65535;
                            break;
                        case 905843021:
                            if (mimeType.equals("vnd.android.cursor.item/photo")) {
                                c = 4;
                                break;
                            }
                            c = 65535;
                            break;
                        default:
                            c = 65535;
                            break;
                    }
                    switch (c) {
                        case 0:
                            try {
                                JSArray emailAddresses = (JSArray) jsContact.get(EMAILS);
                                JSObject jsEmail = new JSObject();
                                jsEmail.put("label", mapEmailTypeToLabel(type, label));
                                jsEmail.put(EMAIL_ADDRESS, data);
                                emailAddresses.put(jsEmail);
                                break;
                            } catch (JSONException e) {
                                e.printStackTrace();
                                break;
                            }
                        case 1:
                            try {
                                JSArray jsPhoneNumbers2 = (JSArray) jsContact.get(PHONE_NUMBERS);
                                JSObject jsPhone = new JSObject();
                                jsPhone.put("label", mapPhoneTypeToLabel(type, label));
                                jsPhone.put(PHONE_NUMBER, data);
                                jsPhoneNumbers2.put(jsPhone);
                                break;
                            } catch (JSONException e2) {
                                e2.printStackTrace();
                                break;
                            }
                        case 2:
                            int eventType = contactsCursor.getInt(contactsCursor.getColumnIndex("data2"));
                            if (eventType == 3) {
                                jsContact.put(BIRTHDAY, data);
                                break;
                            }
                            break;
                        case 3:
                            jsContact.put(ORGANIZATION_NAME, data);
                            String organizationRole = contactsCursor.getString(contactsCursor.getColumnIndex("data4"));
                            if (organizationRole != null) {
                                jsContact.put(ORGANIZATION_ROLE, organizationRole);
                                break;
                            }
                            break;
                        case 4:
                            byte[] thumbnailPhoto = contactsCursor.getBlob(contactsCursor.getColumnIndex("data15"));
                            if (thumbnailPhoto != null) {
                                String encodedThumbnailPhoto = Base64.encodeToString(thumbnailPhoto, 2);
                                jsContact.put(PHOTO_THUMBNAIL, "data:image/png;base64," + encodedThumbnailPhoto);
                                break;
                            }
                            break;
                    }
                    contactsById = contactsById3;
                    contactsById.put(contactId, jsContact);
                }
                contactsById2 = contactsById;
                selectionArgs2 = selectionArgs;
                projection = projection2;
                contentResolver2 = contentResolver;
                selection2 = selection;
                jsContacts3 = jsContacts2;
            }
            jsContacts = jsContacts3;
        }
        if (contactsCursor != null) {
            contactsCursor.close();
        }
        JSObject result = new JSObject();
        result.put("contacts", (Object) jsContacts);
        call.resolve(result);
    }

    @PermissionCallback
    private void readContactsPermissionCallback(PluginCall call) {
        JSObject result = new JSObject();
        if (canReadContactsPermisionGranted()) {
            result.put("granted", true);
        } else {
            result.put("granted", false);
        }
        call.resolve(result);
    }

    private String mapPhoneTypeToLabel(int type, String defaultLabel) {
        switch (type) {
            case 1:
                return "home";
            case 2:
                return "mobile";
            case 3:
                return "work";
            case 4:
                return "fax work";
            case 5:
                return "fax home";
            case 6:
                return "pager";
            case 7:
                return "other";
            case 8:
                return PluginMethod.RETURN_CALLBACK;
            case 9:
                return "car";
            case 10:
                return "company main";
            case 11:
                return "isdn";
            case 12:
                return "main";
            case 13:
                return "other fax";
            case 14:
                return "radio";
            case 15:
                return "telex";
            case 16:
                return "tty";
            case 17:
                return "work mobile";
            case 18:
                return "work pager";
            case 19:
                return "assistant";
            case 20:
                return "mms";
            default:
                return defaultLabel;
        }
    }

    private String mapEmailTypeToLabel(int type, String defaultLabel) {
        switch (type) {
            case 1:
                return "home";
            case 2:
                return "work";
            case 3:
                return "other";
            case 4:
                return "mobile";
            default:
                return defaultLabel;
        }
    }

    private boolean canReadContactsPermisionGranted() {
        return getPermissionState(READ_CONTACTS) == PermissionState.GRANTED;
    }
}
