package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import androidx.core.view.InputDeviceCompat;
import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.NoSuchElementException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e0.smali */
public abstract class e0 extends b0 implements Iterable {
    static final o0 x = new a(e0.class, 16);
    h[] b;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(e0 e0Var) {
            return e0Var;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e0$b.smali */
    class b implements Enumeration {
        private int a = 0;

        b() {
        }

        @Override // java.util.Enumeration
        public boolean hasMoreElements() {
            return this.a < e0.this.b.length;
        }

        @Override // java.util.Enumeration
        public Object nextElement() {
            int i = this.a;
            h[] hVarArr = e0.this.b;
            if (i >= hVarArr.length) {
                throw new NoSuchElementException();
            }
            this.a = i + 1;
            return hVarArr[i];
        }
    }

    protected e0() {
        this.b = i.d;
    }

    public static e0 a(Object obj) {
        if (obj == null || (obj instanceof e0)) {
            return (e0) obj;
        }
        if (obj instanceof h) {
            b0 aSN1Primitive = ((h) obj).toASN1Primitive();
            if (aSN1Primitive instanceof e0) {
                return (e0) aSN1Primitive;
            }
        } else if (obj instanceof byte[]) {
            try {
                return (e0) x.a((byte[]) obj);
            } catch (IOException e) {
                throw new IllegalArgumentException("failed to construct sequence from byte[]: " + e.getMessage());
            }
        }
        throw new IllegalArgumentException("unknown object in getInstance: " + obj.getClass().getName());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return true;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return new j2(this.b, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new d3(this.b, false);
    }

    d[] h() {
        int size = size();
        d[] dVarArr = new d[size];
        for (int i = 0; i < size; i++) {
            dVarArr[i] = d.a(this.b[i]);
        }
        return dVarArr;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        int length = this.b.length;
        int i = length + 1;
        while (true) {
            length--;
            if (length < 0) {
                return i;
            }
            i = (i * InputDeviceCompat.SOURCE_KEYBOARD) ^ this.b[length].toASN1Primitive().hashCode();
        }
    }

    x[] i() {
        int size = size();
        x[] xVarArr = new x[size];
        for (int i = 0; i < size; i++) {
            xVarArr[i] = x.a(this.b[i]);
        }
        return xVarArr;
    }

    @Override // java.lang.Iterable
    public Iterator<h> iterator() {
        return new Arrays.a(this.b);
    }

    public Enumeration j() {
        return new b();
    }

    abstract d k();

    abstract l l();

    abstract x m();

    abstract f0 n();

    h[] o() {
        return this.b;
    }

    public int size() {
        return this.b.length;
    }

    public String toString() {
        int size = size();
        if (size == 0) {
            return "[]";
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append('[');
        int i = 0;
        while (true) {
            stringBuffer.append(this.b[i]);
            i++;
            if (i >= size) {
                stringBuffer.append(']');
                return stringBuffer.toString();
            }
            stringBuffer.append(", ");
        }
    }

    protected e0(h hVar) {
        if (hVar != null) {
            this.b = new h[]{hVar};
            return;
        }
        throw new NullPointerException("'element' cannot be null");
    }

    protected e0(i iVar) {
        if (iVar != null) {
            this.b = iVar.c();
            return;
        }
        throw new NullPointerException("'elementVector' cannot be null");
    }

    e0(h[] hVarArr, boolean z) {
        this.b = z ? i.a(hVarArr) : hVarArr;
    }

    public h a(int i) {
        return this.b[i];
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (!(b0Var instanceof e0)) {
            return false;
        }
        e0 e0Var = (e0) b0Var;
        int size = size();
        if (e0Var.size() != size) {
            return false;
        }
        for (int i = 0; i < size; i++) {
            b0 aSN1Primitive = this.b[i].toASN1Primitive();
            b0 aSN1Primitive2 = e0Var.b[i].toASN1Primitive();
            if (aSN1Primitive != aSN1Primitive2 && !aSN1Primitive.a(aSN1Primitive2)) {
                return false;
            }
        }
        return true;
    }
}
