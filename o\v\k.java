package o.v;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import o.al.c;
import o.eo.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\k.smali */
public final class k extends b {
    public static final byte[] $$j = null;
    public static final int $$k = 0;
    private static int $10;
    private static int $11;
    private static int h;
    private static long i;
    private static int l;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        l = 1;
        i = 6322469819906634212L;
    }

    static void init$0() {
        $$j = new byte[]{86, 121, 65, -9};
        $$k = 30;
    }

    private static void y(byte b, int i2, int i3, Object[] objArr) {
        int i4 = 71 - (i3 * 3);
        byte[] bArr = $$j;
        int i5 = (b * 2) + 4;
        int i6 = (i2 * 3) + 1;
        byte[] bArr2 = new byte[i6];
        int i7 = -1;
        int i8 = i6 - 1;
        if (bArr == null) {
            i5++;
            i4 = i8 + i4;
            i8 = i8;
        }
        while (true) {
            i7++;
            bArr2[i7] = (byte) i4;
            if (i7 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i9 = i4;
            int i10 = i8;
            i5++;
            i4 = i9 + bArr[i5];
            i8 = i10;
        }
    }

    public k(String str, boolean z, o.eo.e eVar, o.eo.f fVar) {
        super(str, eVar, z, fVar);
    }

    @Override // o.v.b
    public final c.e a() {
        int i2 = h + 43;
        l = i2 % 128;
        int i3 = i2 % 2;
        c.e eVar = c.e.b;
        int i4 = l + 13;
        h = i4 % 128;
        switch (i4 % 2 != 0 ? 'A' : (char) 23) {
            case 23:
                return eVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i2 = h + Opcodes.DNEG;
        l = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object[] objArr = new Object[1];
                w("⿍㮉⾞䚭趁쫗꒸幅\ueb53跋\ue19bᕳ꙾䃿⪍큚慬Ѕ矢꾙㰘", -TextUtils.lastIndexOf("", (char) 26, 1, 1), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                w("⿍㮉⾞䚭趁쫗꒸幅\ueb53跋\ue19bᕳ꙾䃿⪍큚慬Ѕ矢꾙㰘", -TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i3 = h + 55;
        l = i3 % 128;
        switch (i3 % 2 == 0 ? 'S' : (char) 17) {
            case Opcodes.AASTORE /* 83 */:
                throw null;
            default:
                return intern;
        }
    }

    public final void a(Context context, o.p.g gVar) throws WalletValidationException {
        int i2 = l + 37;
        h = i2 % 128;
        int i3 = i2 % 2;
        if (t().n() != f.d.b) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            w("䣻ﭙ䢯枋ಟ\u0a0d薖\udf4b豹", 1 - Gravity.getAbsoluteGravity(0, 0), objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        d(context, gVar);
        int i4 = h + 69;
        l = i4 % 128;
        switch (i4 % 2 == 0 ? '+' : '\n') {
            case '+':
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void w(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 384
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.k.w(java.lang.String, int, java.lang.Object[]):void");
    }
}
