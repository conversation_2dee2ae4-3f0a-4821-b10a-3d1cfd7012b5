package o.m;

import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.fragment.app.Fragment;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import kotlin.io.encoding.Base64;
import o.ee.g;
import o.ee.k;
import o.o.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\a.smali */
public final class a extends Fragment {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long d;
    private static int j;
    private final c b;
    private final boolean c;
    private final o.m.c e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\m\a$c.smali */
    interface c {
        void a();

        void a(e eVar);

        void d();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        j = 1;
        c();
        ViewConfiguration.getWindowTouchSlop();
        int i = j + 37;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    static void c() {
        d = 7456962585992169473L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 68
            int r9 = r9 * 4
            int r9 = r9 + 1
            int r8 = r8 * 3
            int r8 = 4 - r8
            byte[] r0 = o.m.a.$$a
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L1a:
            r3 = r2
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L34:
            int r8 = -r8
            int r7 = r7 + 1
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.m.a.g(int, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{Base64.padSymbol, 89, 45, -101};
        $$b = 21;
    }

    public a(o.m.c cVar, boolean z, c cVar2) {
        this.e = cVar;
        this.b = cVar2;
        this.c = z;
    }

    @Override // androidx.fragment.app.Fragment
    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        int i = a + 69;
        j = i % 128;
        if (i % 2 == 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (this.c) {
            case true:
                View a2 = a(layoutInflater, viewGroup);
                int i2 = j + 69;
                a = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return a2;
                    default:
                        int i3 = 5 / 0;
                        return a2;
                }
            default:
                a(getContext());
                return new FrameLayout(layoutInflater.getContext());
        }
    }

    private void a(Context context) {
        int i = a + Opcodes.DREM;
        j = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f("곈겛纳䤥✈㘎迕娶\ud8ebﷷ涊\u038b\uea54릯$\udc16웣蔧⒢뢅ⅉ\ue0c4윱闿ᷱ챟鮒癠硶⯔", TextUtils.indexOf((CharSequence) "", '0', 0) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("甿畍콍Ꞥ瓣蟶嘢\uebd3㙩긌茩偋㎡ࡔ\ueeaa迥Ἴ㓶쨿\ueb61\uf8b2儝⦛옚쐈綣甖▛ꆋ騋傆Ĕ贒욵밃", ViewConfiguration.getPressedStateDuration() >> 16, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f("ۣڈ빖蝘嬛\uf6ed◮髚\u169d臦ꏛ羐", Color.argb(0, 0, 0, 0), objArr3);
        KeyguardManager keyguardManager = (KeyguardManager) context.getSystemService(((String) objArr3[0]).intern());
        if (keyguardManager == null) {
            g.c();
            Object[] objArr4 = new Object[1];
            f("곈겛纳䤥✈㘎迕娶\ud8ebﷷ涊\u038b\uea54릯$\udc16웣蔧⒢뢅ⅉ\ue0c4윱闿ᷱ챟鮒癠硶⯔", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            f("探提십\uf27cᔾ詖䂿\ue673掱쿑훱㆖┼״뭲\uee38ড㥖鿧誼\uee2f岽籃Ꟈ튕瀃\u20ce䑆뜖鞫՞惉鮏쬕\ue9dbᴓ硏\ueee8캣㧾島Ȭ덥홈ŷ↡鞺\uf2ee\ue5eb䔢琱꽞쩣硆墌䮌껟鿒", 1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr5);
            g.e(intern2, ((String) objArr5[0]).intern());
            this.b.a(e.e);
            return;
        }
        Intent createConfirmDeviceCredentialIntent = keyguardManager.createConfirmDeviceCredentialIntent(this.e.j(), this.e.i());
        switch (createConfirmDeviceCredentialIntent != null ? 'B' : (char) 7) {
            case 'B':
                startActivityForResult(createConfirmDeviceCredentialIntent, 1042);
                return;
            default:
                this.b.a(e.e);
                int i3 = j + 21;
                a = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    @Override // androidx.fragment.app.Fragment
    public final void onActivityResult(int i, int i2, Intent intent) {
        switch (i == 1042 ? '.' : '/') {
            case '.':
                switch (i2 == -1) {
                    case true:
                        int i3 = a + 5;
                        j = i3 % 128;
                        int i4 = i3 % 2;
                        this.b.d();
                        break;
                    default:
                        this.b.a();
                        int i5 = a + 9;
                        j = i5 % 128;
                        int i6 = i5 % 2;
                        break;
                }
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void a(View view) {
        int i = j + 33;
        a = i % 128;
        int i2 = i % 2;
        requireActivity().finish();
        int i3 = a + Opcodes.DSUB;
        j = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void e(View view) {
        int i = a + 37;
        j = i % 128;
        int i2 = i % 2;
        this.b.a();
        int i3 = j + 77;
        a = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0081. Please report as an issue. */
    private View a(LayoutInflater layoutInflater, ViewGroup viewGroup) {
        int i = j + 31;
        a = i % 128;
        int i2 = i % 2;
        View inflate = layoutInflater.inflate(R.layout.antelop_screenunlock_prompt_fragment, viewGroup, false);
        Button button = (Button) inflate.findViewById(R.id.antelop_screenunlock_prompt_button_submit);
        button.setText(this.e.f());
        button.setOnClickListener(new View.OnClickListener() { // from class: o.m.a$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                a.this.a(view);
            }
        });
        ((TextView) inflate.findViewById(R.id.antelop_screenunlock_prompt_title)).setText(this.e.j());
        ((TextView) inflate.findViewById(R.id.antelop_screenunlock_prompt_subtitle)).setText(this.e.i());
        ((ImageView) inflate.findViewById(R.id.antelop_screenunlock_prompt_cancel)).setOnClickListener(new View.OnClickListener() { // from class: o.m.a$$ExternalSyntheticLambda1
            @Override // android.view.View.OnClickListener
            public final void onClick(View view) {
                a.this.e(view);
            }
        });
        if (requireContext().getResources().getBoolean(R.bool.antelopScreenUnlockPromptEnableOverlayProtection)) {
            button.setOnTouchListener(new k() { // from class: o.m.a.1
                private static int e = 0;
                private static int c = 1;

                /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0011. Please report as an issue. */
                @Override // o.ee.k
                public final void b(Context context) {
                    int i3 = (c + 38) - 1;
                    e = i3 % 128;
                    switch (i3 % 2 == 0) {
                    }
                    Toast.makeText(context, a.this.requireContext().getString(R.string.antelopScreenUnlockPromptOverlayWarningMessage), 1).show();
                    int i4 = c;
                    int i5 = (i4 ^ 87) + ((i4 & 87) << 1);
                    e = i5 % 128;
                    int i6 = i5 % 2;
                }
            });
            int i3 = j + 99;
            a = i3 % 128;
            switch (i3 % 2 != 0) {
            }
        }
        return inflate;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 358
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.m.a.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
