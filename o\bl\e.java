package o.bl;

import android.content.Context;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.configuration.AntelopConfiguration;
import fr.antelop.sdk.configuration.AntelopConfigurationManager;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bl\e.smali */
public final class e implements a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static byte[] b;
    private static int c;
    private static short[] d;
    private static int e;
    private static int i;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        i = 1;
        f();
        Process.myPid();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        View.combineMeasuredStates(0, 0);
        TextUtils.indexOf("", "", 0);
        KeyEvent.getMaxKeyCode();
        int i2 = i + Opcodes.LSHL;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void f() {
        b = new byte[]{75, -99, -111, 107, 99, -105, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 114, 111, -106, 101, -125, ByteCompanionObject.MAX_VALUE, 109, -98, 110, -109, 104, 111, PSSSigner.TRAILER_IMPLICIT, 78, 104, -108, 101, -106, 105, -77, 114, -112, -65};
        e = 909053652;
        c = -513816572;
        a = 519992369;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x0039). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.bl.e.$$a
            int r9 = r9 * 2
            int r9 = r9 + 1
            int r8 = r8 * 3
            int r8 = 3 - r8
            int r7 = r7 * 2
            int r7 = 110 - r7
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L39
        L1a:
            r3 = r2
        L1b:
            r6 = r8
            r8 = r7
            r7 = r6
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            int r7 = r7 + 1
            if (r4 != r9) goto L2f
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2f:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L39:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.e.h(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{43, 59, -40, 18};
        $$b = Opcodes.ANEWARRAY;
    }

    @Override // o.bl.a
    public final void c(Context context) {
        int i2 = i + 11;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '@' : (char) 15) {
            case 15:
                break;
            default:
                int i3 = 28 / 0;
                break;
        }
    }

    @Override // o.bl.a
    public final String e() {
        int i2 = j + 93;
        i = i2 % 128;
        int i3 = i2 % 2;
        AntelopConfiguration antelopConfiguration = AntelopConfigurationManager.get();
        switch (antelopConfiguration == null) {
            case true:
                return null;
            default:
                String applicationId = antelopConfiguration.getApplicationId();
                int i4 = i + 65;
                j = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return applicationId;
                }
        }
    }

    @Override // o.bl.a
    public final String b() {
        AntelopConfiguration antelopConfiguration = AntelopConfigurationManager.get();
        switch (antelopConfiguration != null) {
            case true:
                String issuerId = antelopConfiguration.getIssuerId();
                int i2 = j + 21;
                i = i2 % 128;
                int i3 = i2 % 2;
                return issuerId;
            default:
                int i4 = i + 99;
                j = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    @Override // o.bl.a
    public final o.bi.a a() {
        int i2 = j;
        int i3 = i2 + 9;
        i = i3 % 128;
        switch (i3 % 2 == 0 ? 'W' : 'M') {
            case Opcodes.POP /* 87 */:
                throw null;
            default:
                int i4 = i2 + 39;
                i = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    @Override // o.bl.a
    public final o.bi.d c() {
        int i2 = j;
        int i3 = i2 + Opcodes.DMUL;
        i = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = i2 + Opcodes.LSHL;
                i = i4 % 128;
                switch (i4 % 2 == 0 ? (char) 17 : (char) 4) {
                    case 17:
                        int i5 = 84 / 0;
                        return null;
                    default:
                        return null;
                }
            default:
                throw null;
        }
    }

    @Override // o.bl.a
    public final String d() {
        int i2 = i + 7;
        j = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        g((byte) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (-684817057) - TextUtils.getOffsetAfter("", 0), (short) (ViewConfiguration.getTouchSlop() >> 8), (Process.myTid() >> 22) - 69, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 680475052, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = i + 85;
        j = i4 % 128;
        switch (i4 % 2 != 0 ? '#' : (char) 20) {
            case '#':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:151:0x0344, code lost:
    
        r4 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:153:0x0342, code lost:
    
        if (r4 != false) goto L94;
     */
    /* JADX WARN: Code restructure failed: missing block: B:98:0x0330, code lost:
    
        if (r4 != false) goto L94;
     */
    /* JADX WARN: Code restructure failed: missing block: B:99:0x0346, code lost:
    
        r4 = 0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r20, int r21, short r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1218
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bl.e.g(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
