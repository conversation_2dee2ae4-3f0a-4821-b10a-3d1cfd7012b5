package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l0.smali */
public class l0 extends b0 {
    static final o0 x = new a(l0.class, 23);
    final byte[] b;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return l0.b(f2Var.h());
        }
    }

    l0(byte[] bArr) {
        if (bArr.length < 2) {
            throw new IllegalArgumentException("UTCTime string too short");
        }
        this.b = bArr;
        if (!a(0) || !a(1)) {
            throw new IllegalArgumentException("illegal characters in UTCTime string");
        }
    }

    private boolean a(int i) {
        byte b;
        byte[] bArr = this.b;
        return bArr.length > i && (b = bArr[i]) >= 48 && b <= 57;
    }

    static l0 b(byte[] bArr) {
        return new l0(bArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public String toString() {
        return o7.b(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 23, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (b0Var instanceof l0) {
            return Arrays.areEqual(this.b, ((l0) b0Var).b);
        }
        return false;
    }
}
