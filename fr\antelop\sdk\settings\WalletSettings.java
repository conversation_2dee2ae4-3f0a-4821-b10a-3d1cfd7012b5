package fr.antelop.sdk.settings;

import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodUsage;
import fr.antelop.sdk.authentication.CustomerAuthenticationPattern;
import java.math.BigDecimal;
import java.util.Currency;
import java.util.Map;
import o.ee.o;
import o.fn.a;
import o.fn.d;
import o.fn.e;
import o.fn.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\settings\WalletSettings.smali */
public final class WalletSettings {
    private final Limits limits;
    private final Locale locale;
    private final Security security;

    public WalletSettings(Limits limits, Security security, Locale locale) {
        this.limits = limits;
        this.security = security;
        this.locale = locale;
    }

    public final Limits limits() {
        return this.limits;
    }

    public final Security security() {
        return this.security;
    }

    public final Locale locale() {
        return this.locale;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\settings\WalletSettings$Limits.smali */
    public static final class Limits {
        private final d innerLimits;

        public Limits(d dVar) {
            this.innerLimits = dVar;
        }

        public final WalletSettingsValue<BigDecimal> getMaxPaymentAmount() {
            return this.innerLimits.c();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\settings\WalletSettings$Locale.smali */
    public static final class Locale {
        private final a innerLocale;

        public Locale(a aVar) {
            this.innerLocale = aVar;
        }

        public final WalletSettingsValue<Currency> getCurrency() {
            return this.innerLocale.b();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\settings\WalletSettings$Security.smali */
    public static final class Security {
        private final e authenticationPatterns;
        private final h innerSecurity;

        public Security(h hVar, e eVar) {
            this.innerSecurity = hVar;
            this.authenticationPatterns = eVar;
        }

        public final WalletSettingsValue<Boolean> arePaymentsGeolocated() {
            return this.innerSecurity.b();
        }

        public final WalletSettingsValue<Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> getCustomerAuthenticationMethods() {
            return this.innerSecurity.c();
        }

        public final WalletSettingsValue<Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> getCustomerAuthenticationMethods(CustomerAuthenticationMethodUsage... customerAuthenticationMethodUsageArr) {
            return this.innerSecurity.c(customerAuthenticationMethodUsageArr);
        }

        public final WalletSettingsValue<TransactionStartCondition> getTransactionStartCondition() {
            return this.innerSecurity.j();
        }

        public final WalletSettingsValue<Map<String, CustomerAuthenticationPattern>> getCustomerAuthenticationPatterns() {
            return new WalletSettingsValue<>(o.d(this.authenticationPatterns.e()), WalletSettingsRights.ReadOnly);
        }
    }
}
