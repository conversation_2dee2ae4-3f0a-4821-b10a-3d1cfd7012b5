package com.vasco.digipass.sdk.obfuscated;

import com.vasco.digipass.sdk.DigipassSDKConstants;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\e.smali */
public final class e implements DigipassSDKConstants {
    public int a;
    public final i c = new i();
    public final g d = new g();
    private final d[] b = new d[8];
    public d e = new d();

    public d a(int i) {
        if (i == 100) {
            return this.e;
        }
        for (int i2 = 0; i2 < this.a; i2++) {
            d dVar = this.b[i2];
            if (dVar.c == i) {
                return dVar;
            }
        }
        return null;
    }

    public boolean b() {
        return this.d.c == 1;
    }

    public boolean c() {
        for (int i = 0; i < this.a; i++) {
            if (this.b[i].f) {
                return true;
            }
        }
        return false;
    }

    public boolean d() {
        return this.d.c == 0;
    }

    public boolean e() {
        byte b = this.d.c;
        return b == 1 || b == 3;
    }

    public void f() {
        q.g(this.d.g);
        q.g(this.d.h);
        q.g(this.d.i);
        q.g(this.d.j);
        q.g(this.c.c);
        q.g(this.d.p);
        q.g(this.d.q);
        q.g(this.d.s);
        q.g(this.d.t);
    }

    public d[] a() {
        return this.b;
    }
}
