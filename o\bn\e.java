package o.bn;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.a.f;
import o.a.m;
import o.ee.g;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final e c;
    private static final List<d> e;
    private static int f;
    private static int g;
    private static int[] h;
    private static int i;
    private static byte[] j;
    private static int m;
    private static short[] n;

    /* renamed from: o, reason: collision with root package name */
    private static int f42o;
    private boolean a = false;
    d d = null;
    C0033e b = null;

    static void c() {
        h = new int[]{2054586610, 135293816, -205196717, -641048771, -531846643, 598990503, -1283166042, 1660585243, -343572891, 1066084975, 26766047, 1039615452, 1020183722, -1611258967, -1774317698, -35099674, 1288721630, 1615222893};
        j = new byte[]{Base64.padSymbol, -95, 119, -45, 65, -48, -37, 65, -47, 75, 66, 14, 10, -13, 15, -7, 11, -3, 7, -7, 10, 23, -31, -3, -7, 23, -1, -13, 20, -26, 5, 23, -19, 24, -1, -5, 5, 10, -31, -32, 7, 9, 5, -9, 6, 11, 57, -66, 7, 9, 5, -9, 6, 11, 57, PSSSigner.TRAILER_IMPLICIT, 12, 45, -106, -123, -99, -108, Tnaf.POW_2_WIDTH, -127, -82, -106, -85, -127, -120, -74, -43, -103, -87, -106, -103, -89, -102, -82, -127, -82, -43, -103, 123, 55, -38, -4, -35, 97, -2, -14, 76, -7, 73, 113, -14, -109, -70, 68, 66, -18, 74, -10, -6, -104, -76, 68, -113, -7, -67, 68, 79, -12, 82, -8, -79, -70, -32, -7, -103, -48, 75, -116, -77, -2, 71, -9, 68, 112, -18, -105, PSSSigner.TRAILER_IMPLICIT, -4, 72, -8, 71, 72, -10, 67, -1, 112, -1, -4, 72, 66, -121, -47, 71, 76, -14, 69, -32, 83, -126, -14, 112, -70, -22, 84, -32, 74, -3, -8, 74, -6, 72, 98, 80, 84, 58, 85, 37, Base64.padSymbol, 84, -13, 108, 34, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 64, 60, 88, 44, -10, 85, 105, 34, 63, 82, 52, 86, -3, 108, 46, 85, -11, -99, 38, 34, -1, -98, 59, -6, -109, 80, 39, 87, 34, 62, 64, -9, 106, 42, 38, 86, 39, 38, 88, 35, 47, 62, 47, 42, 38, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -25, -99, 39, 58, 84, 33, 46, 51, -28, 84, 62, 108, 92, 50, 46, 60, 41, 86, 60, 44, 38, 78, -60, -49, -2, -50, -56, -56, -13, -6, -15, -7, -104, 33, -1, -6, -55, -62, -12, -103, Base64.padSymbol, -121, 62, -57, -8, -60, -5, -16, -14, -110, 58, -3, -2, -31, -55, -49, -14, -53, -13, -106, 33, -57, -55, -19, -15, -11, -31, -109, -7, -53, 33, 17, -41, -5, -15, -2, -13, -15, -31, -61, 67, -30, 73, -8, 86, 18, 78, -31, -1, UtilitiesSDKConstants.SRP_LABEL_ENC, -69, 68, -11, 73, -32, 77, -9, -105, -65, -30, -5, -2, 70, 76, -9, 112, -8, -109, -66, 68, 70, 18, 78, -6, -2, -104, -10, 112, -66, -18, 84, -32, 78, -5, -8, 78, -2, 72};
        g = 909053645;
        i = 1183467007;
        f = 117404321;
    }

    static void init$0() {
        $$a = new byte[]{119, -13, -39, 23};
        $$b = Opcodes.L2F;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(short r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 108
            int r9 = r9 + 4
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.bn.e.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            int r9 = r9 + 1
            int r3 = r3 + 1
            r4 = r0[r9]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.e.p(short, short, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f42o = 0;
        m = 1;
        c();
        ViewConfiguration.getPressedStateDuration();
        e = Arrays.asList(new b(), new a());
        c = new e();
        int i2 = f42o + 41;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    public static e b() {
        int i2 = f42o;
        int i3 = i2 + 23;
        m = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0 ? '%' : 'H') {
            case 'H':
                e eVar = c;
                int i4 = i2 + Opcodes.LSUB;
                m = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        throw null;
                    default:
                        return eVar;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    private e() {
    }

    public final void a(Context context, boolean z, boolean z2) {
        d next;
        Object[] objArr = new Object[1];
        k(new int[]{-583512693, -1934324915, 2076635636, 1754424124, 79950121, -977148603, 460606032, 1773570327, -282634373, -250074466, 2099233537, 1484074123}, 21 - View.resolveSizeAndState(0, 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        if (this.a) {
            return;
        }
        g.c();
        Object[] objArr2 = new Object[1];
        l((byte) (Color.argb(0, 0, 0, 0) - 55), (-818969648) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (short) ((-15) - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), View.MeasureSpec.makeMeasureSpec(0, 0) - 94, (-1889880838) - (ViewConfiguration.getPressedStateDuration() >> 16), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l((byte) (KeyEvent.keyCodeFromString("") - 107), (-818969640) - TextUtils.lastIndexOf("", '0', 0), (short) ((ViewConfiguration.getScrollBarSize() >> 8) - 3), (-95) - ExpandableListView.getPackedPositionChild(0L), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1889880841, objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        Object[] objArr4 = new Object[1];
        l((byte) (AndroidCharacter.getMirror('0') - 'J'), (ViewConfiguration.getScrollBarSize() >> 8) - 818969592, (short) ((ViewConfiguration.getTapTimeout() >> 16) - 38), (-94) - (Process.myPid() >> 22), (-1889880873) - Color.green(0), objArr4);
        int i2 = sharedPreferences.getInt(((String) objArr4[0]).intern(), -1);
        boolean z3 = i2 != -1;
        char c2 = Typography.greater;
        switch (z3) {
            case true:
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr5 = new Object[1];
                k(new int[]{694039943, 2071363372, -1519434545, 1596895389, 1588076257, 1855545275, -236671123, 2098947220, 2065904567, 1299120299, -619438810, -679738632, 1464017249, -239828066, -1665241073, 1667773739}, 32 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr5);
                g.d(intern, sb.append(((String) objArr5[0]).intern()).append(i2).toString());
                break;
            default:
                g.c();
                Object[] objArr6 = new Object[1];
                k(new int[]{694039943, 2071363372, -1519434545, 1596895389, 1588076257, 1855545275, -1151328636, -1869744485, 2054925096, 1537011776, -237448405, 1868040190, -1076570537, -403370093, -1042665828, 1449505455, -1805173842, -576540742, -110654617, 2019587592, 989857225, -1928053700, -636904975, -718331783, 432641739, 30459602, -1096686645, -608230945, -728182000, -161882937, -116146612, 1111800703, -1346027698, 182100614, 631589124, -1194436212, 698087216, 855292288, 834958107, 1235392157, -228402566, -1901840827, -125428779, -1941478927, -1452885593, -201513227, -1960131943, 1363325980, 1534965831, 745833506, 969701300, 405957939, 930084103, -467913425, 548514964, 78172523, -1077516026, 379015321, -1412100487, -1934672564, 17064415, -818547069}, ImageFormat.getBitsPerPixel(0) + Opcodes.LUSHR, objArr6);
                g.d(intern, ((String) objArr6[0]).intern());
                i2 = 0;
                break;
        }
        this.d = null;
        Iterator<d> it = e.iterator();
        while (it.hasNext()) {
            int i3 = m + 91;
            f42o = i3 % 128;
            if (i3 % 2 == 0) {
                next = it.next();
                switch (next.a() != i2) {
                    case true:
                        break;
                    default:
                        this.d = next;
                        break;
                }
            } else {
                next = it.next();
                int i4 = 60 / 0;
                if (next.a() == i2) {
                    this.d = next;
                }
            }
        }
        if (this.d == null) {
            Object[] objArr7 = new Object[1];
            l((byte) ((ViewConfiguration.getScrollBarSize() >> 8) - 101), (-818969566) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) ((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 78), TextUtils.indexOf("", "", 0, 0) - 94, (-1889880863) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr7);
            throw new RuntimeException(((String) objArr7[0]).intern());
        }
        List<d> list = e;
        d dVar = list.get(list.size() - 1);
        if (this.d.a() != dVar.a()) {
            c2 = '\t';
        }
        switch (c2) {
            case '\t':
                g.c();
                Object[] objArr8 = new Object[1];
                l((byte) (Color.red(0) + Opcodes.ISHL), (-818969484) - Color.green(0), (short) ((-57) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), (-94) - TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) - 1889880838, objArr8);
                g.d(intern, ((String) objArr8[0]).intern());
                switch (z2) {
                    case false:
                        if (!z) {
                            if (o.b.c.e(context) != o.b.e.b) {
                                g.c();
                                Object[] objArr9 = new Object[1];
                                k(new int[]{694039943, 2071363372, -1519434545, 1596895389, 1588076257, 1855545275, 1925722564, -725101599, 651605676, 197276631, 2126649379, -116576295, -307489572, -967669975, 657841434, -2098273763, -1707403654, 1363757788, -2116036983, -2066455312, -1855982974, 1219365173, -1223184126, 2134250501, 604498191, -565065511, -151271616, 918758013, 989857225, -1928053700, -636904975, -718331783, -733063741, 1338799511, -2033308622, 761488813, -1223184126, 2134250501, 1947367133, -372373222, -660571040, 205342515}, TextUtils.indexOf("", "", 0) + 83, objArr9);
                                g.d(intern, ((String) objArr9[0]).intern());
                                break;
                            } else {
                                int i5 = f42o + 51;
                                m = i5 % 128;
                                int i6 = i5 % 2;
                                g.c();
                                Object[] objArr10 = new Object[1];
                                k(new int[]{694039943, 2071363372, -1519434545, 1596895389, 1588076257, 1855545275, 1925722564, -725101599, 651605676, 197276631, 2126649379, -116576295, -307489572, -967669975, -1202024181, 498184648, 66410435, 1848276698, -263035203, -1588951332, -749998921, 312519192, -239772424, 2063958199, -84049780, 994142305, -1904020594, 937396867, 1509438067, -346398561, 1379020714, -1423735507, -110654617, 2019587592, 481883818, 1254751684, -994971715, 395428411, -217424245, 681740692, 1057235149, -1417233992, 2053472456, 134957126, 692169970, -389116892, 1212055426, -461900173, -1902087853, -1296837566}, 97 - KeyEvent.normalizeMetaState(0), objArr10);
                                g.d(intern, ((String) objArr10[0]).intern());
                                this.d = dVar;
                                SharedPreferences.Editor edit = sharedPreferences.edit();
                                Object[] objArr11 = new Object[1];
                                l((byte) ((-26) - (ViewConfiguration.getTouchSlop() >> 8)), (-818969592) - TextUtils.indexOf("", ""), (short) ((-38) - (ViewConfiguration.getPressedStateDuration() >> 16)), TextUtils.lastIndexOf("", '0', 0) - 93, (-1889880873) - View.MeasureSpec.getSize(0), objArr11);
                                edit.putInt(((String) objArr11[0]).intern(), this.d.a()).commit();
                                this.b = null;
                                break;
                            }
                        } else {
                            g.c();
                            Object[] objArr12 = new Object[1];
                            l((byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 47), (-818969347) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (short) (TextUtils.lastIndexOf("", '0', 0, 0) + 94), (-95) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), MotionEvent.axisFromString("") - 1889880837, objArr12);
                            g.d(intern, ((String) objArr12[0]).intern());
                            this.b = new C0033e(this, dVar);
                            break;
                        }
                    default:
                        int i7 = f42o + 37;
                        m = i7 % 128;
                        if (i7 % 2 == 0) {
                        }
                        g.c();
                        Object[] objArr13 = new Object[1];
                        l((byte) ((-12) - (ViewConfiguration.getEdgeSlop() >> 16)), View.MeasureSpec.getSize(0) - 818969405, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 99), (-93) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (-1889880839) - ImageFormat.getBitsPerPixel(0), objArr13);
                        g.d(intern, ((String) objArr13[0]).intern());
                        this.d = dVar;
                        SharedPreferences.Editor edit2 = sharedPreferences.edit();
                        Object[] objArr14 = new Object[1];
                        l((byte) (Drawable.resolveOpacity(0, 0) - 26), (-818969592) - ExpandableListView.getPackedPositionGroup(0L), (short) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) - 38), (-94) - (ViewConfiguration.getScrollBarFadeDuration() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 1889880872, objArr14);
                        edit2.putInt(((String) objArr14[0]).intern(), this.d.a()).commit();
                        this.b = null;
                        break;
                }
            default:
                int i8 = f42o + 19;
                m = i8 % 128;
                if (i8 % 2 == 0) {
                }
                g.c();
                Object[] objArr15 = new Object[1];
                l((byte) (51 - Color.argb(0, 0, 0, 0)), TextUtils.indexOf("", "") - 818969562, (short) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 94), (-95) - TextUtils.lastIndexOf("", '0', 0, 0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1889880838, objArr15);
                g.d(intern, ((String) objArr15[0]).intern());
                break;
        }
        this.a = true;
    }

    public final void e() {
        int i2 = f42o + 109;
        m = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        k(new int[]{-583512693, -1934324915, 2076635636, 1754424124, 79950121, -977148603, 460606032, 1773570327, -282634373, -250074466, 2099233537, 1484074123}, View.MeasureSpec.getMode(0) + 21, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(new int[]{-1453130882, -1001916587, -1390375806, 1746399219}, (ViewConfiguration.getWindowTouchSlop() >> 8) + 5, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.a = false;
        Object obj = null;
        this.d = null;
        this.b = null;
        int i4 = m + Opcodes.DMUL;
        f42o = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final d d() {
        int i2 = f42o + 21;
        int i3 = i2 % 128;
        m = i3;
        int i4 = i2 % 2;
        d dVar = this.d;
        int i5 = i3 + Opcodes.DSUB;
        f42o = i5 % 128;
        int i6 = i5 % 2;
        return dVar;
    }

    public final C0033e a() {
        int i2 = f42o;
        int i3 = i2 + 55;
        m = i3 % 128;
        int i4 = i3 % 2;
        C0033e c0033e = this.b;
        int i5 = i2 + 77;
        m = i5 % 128;
        int i6 = i5 % 2;
        return c0033e;
    }

    /* renamed from: o.bn.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\e$e.smali */
    public static final class C0033e {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] e;
        private static int f;
        private static char g;
        private static int h;
        private final d a;
        private final e c;
        private boolean d = false;
        private boolean b = false;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            h = 1;
            c();
            SystemClock.currentThreadTimeMillis();
            Process.getGidForName("");
            int i = f + Opcodes.LSUB;
            h = i % 128;
            int i2 = i % 2;
        }

        static void c() {
            e = new char[]{30563, 30562, 30511, 30585, 30591, 30571, 30573, 30589, 30582, 30569, 30497, 30544, 30572, 30498, 30566, 30588, 30560, 30567, 30568, 30570, 30574, 30561, 30537, 30587, 30530};
            g = (char) 17040;
        }

        static void init$0() {
            $$a = new byte[]{126, -85, 96, -58};
            $$b = 27;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void j(int r7, int r8, int r9, java.lang.Object[] r10) {
            /*
                int r7 = r7 * 3
                int r7 = 4 - r7
                byte[] r0 = o.bn.e.C0033e.$$a
                int r8 = r8 + 69
                int r9 = r9 * 4
                int r9 = r9 + 1
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L18
                r8 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                goto L32
            L18:
                r3 = r2
            L19:
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                if (r4 != r9) goto L28
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L28:
                r3 = r0[r7]
                r6 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r9
                r9 = r6
            L32:
                int r7 = r7 + r9
                int r8 = r8 + 1
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r6 = r8
                r8 = r7
                r7 = r6
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bn.e.C0033e.j(int, int, int, java.lang.Object[]):void");
        }

        public C0033e(e eVar, d dVar) {
            this.c = eVar;
            this.a = dVar;
        }

        public final d b() {
            int i = h;
            int i2 = i + 5;
            f = i2 % 128;
            int i3 = i2 % 2;
            d dVar = this.a;
            int i4 = i + 109;
            f = i4 % 128;
            switch (i4 % 2 != 0 ? 'W' : 'G') {
                case Opcodes.POP /* 87 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVar;
            }
        }

        public final boolean d() {
            int i = f;
            int i2 = i + 109;
            h = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    boolean z = this.d;
                    int i3 = i + 35;
                    h = i3 % 128;
                    switch (i3 % 2 == 0 ? '[' : (char) 5) {
                        case 5:
                            return z;
                        default:
                            throw null;
                    }
                default:
                    throw null;
            }
        }

        public final void a() {
            int i = f + Opcodes.LNEG;
            h = i % 128;
            int i2 = i % 2;
            g.c();
            Object[] objArr = new Object[1];
            i(View.MeasureSpec.getSize(0) + 31, "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (101 - Gravity.getAbsoluteGravity(0, 0)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            i((ViewConfiguration.getFadingEdgeLength() >> 16) + 7, "\u0004\u000b\u0011\b\u0015\u0018㘻", (byte) (60 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            switch (this.d) {
                case false:
                    switch (!this.b) {
                        case false:
                            int i3 = f + 1;
                            h = i3 % 128;
                            int i4 = i3 % 2;
                            g.c();
                            Object[] objArr3 = new Object[1];
                            i((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 31, "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (101 - TextUtils.getOffsetBefore("", 0)), objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            i(37 - KeyEvent.getDeadChar(0, 0), "\u0004\u000b\u0011\b\u0015\u0018\u0011\u0004\f\u0003\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013\u0016\u0001\u0000\u0005\t\u0011\u0000\n\u0007\u0003\u000b\u0011㙊㙊\r\u0018\u0018\u0012㙑", (byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 83), objArr4);
                            g.e(intern2, ((String) objArr4[0]).intern());
                            int i5 = h + 93;
                            f = i5 % 128;
                            int i6 = i5 % 2;
                            break;
                        default:
                            this.d = true;
                            break;
                    }
                default:
                    int i7 = h + 41;
                    f = i7 % 128;
                    if (i7 % 2 != 0) {
                    }
                    g.c();
                    Object[] objArr5 = new Object[1];
                    i((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 31, "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (Color.rgb(0, 0, 0) + 16777317), objArr5);
                    String intern3 = ((String) objArr5[0]).intern();
                    Object[] objArr6 = new Object[1];
                    i(35 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), "\u0004\u000b\u0011\b\u0015\u0018\u0011\u0004\f\u0003\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013\u0016\u0001\u0000\u0005\t\u0011\u0000\n\u0007\u0003\u0012\u0014\u0016\u0005\u0018\u0012㙀", (byte) (View.combineMeasuredStates(0, 0) + 66), objArr6);
                    g.e(intern3, ((String) objArr6[0]).intern());
                    break;
            }
        }

        public final void b(Context context) {
            int i = f + 27;
            h = i % 128;
            int i2 = i % 2;
            g.c();
            Object[] objArr = new Object[1];
            i((ViewConfiguration.getDoubleTapTimeout() >> 16) + 31, "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (TextUtils.lastIndexOf("", '0', 0) + 102), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            i(6 - TextUtils.indexOf("", "", 0, 0), "\u000b\u0011㙭㙭\r\u0018", (byte) (119 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            switch (this.d) {
                case true:
                    switch (this.b ? (char) 24 : ',') {
                        case 24:
                            g.c();
                            Object[] objArr3 = new Object[1];
                            i(31 - View.combineMeasuredStates(0, 0), "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (Color.rgb(0, 0, 0) + 16777317), objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            i((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 35, "\u000b\u0011㙡㙡\r\u0018\u0003\f\u0004\u0016\r\u0013\u0005\u0016\u0018\r\u0015\u0001\u0000\u0016\u0002\u0005\u000f\u0018\u0006\t\u0007\u0011\u0015\u0006\u0004\u000b㙘㙘\u000f\t", (byte) ((-16777110) - Color.rgb(0, 0, 0)), objArr4);
                            g.e(intern2, ((String) objArr4[0]).intern());
                            break;
                        default:
                            this.b = true;
                            Object[] objArr5 = new Object[1];
                            i(47 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "\u0005\b\u000f\u0000\u0016\u0018\u000f\u0004\u0013\u0001\u000f\u0000\u0016\u0018\u000f\u0004\u0013\u0001\u0016\u0011\u000f\u0004\u000b\t\u0005\u0016\b\t\n\u0010\u000f\u0016\t\u0011\u0006\n\u0002\t\u0018\u000e\u0011\t\u0010\u0018\u000e\u0011㗸", (byte) (14 - Process.getGidForName("")), objArr5);
                            SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr5[0]).intern(), 0).edit();
                            Object[] objArr6 = new Object[1];
                            i(26 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\b\u0013\u0000\u0004", (byte) (30 - TextUtils.indexOf("", "", 0, 0)), objArr6);
                            edit.putInt(((String) objArr6[0]).intern(), this.a.a()).commit();
                            this.c.d = this.a;
                            this.c.b = null;
                            break;
                    }
                default:
                    int i3 = f + 43;
                    h = i3 % 128;
                    if (i3 % 2 == 0) {
                    }
                    g.c();
                    Object[] objArr7 = new Object[1];
                    i(31 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\u0018\f\u0017\u0010\u0011\t\u0002\t\u000b\u0018\u0018\r\u0017\u0010\u0014\u0015\u0016\u0015\u0013\u000f\u0005\f\u0004\u0013\u0011\b\u0015\u0018\u000b\u0013㙙", (byte) (101 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), objArr7);
                    String intern3 = ((String) objArr7[0]).intern();
                    Object[] objArr8 = new Object[1];
                    i(KeyEvent.keyCodeFromString("") + 30, "\u000b\u0011㘵㘵\r\u0018\u0003\f\u0004\u0016\r\u0013\u0005\u0016\u0018\r\u0015\u0001\u0001\u0016\u0012\u0015\u0000\u0011\u0018\u0015\b\u0016\u000f\t", (byte) (62 - KeyEvent.normalizeMetaState(0)), objArr8);
                    g.e(intern3, ((String) objArr8[0]).intern());
                    break;
            }
        }

        private static void i(int i, String str, byte b, Object[] objArr) {
            char[] cArr;
            int i2;
            int i3 = $11 + 37;
            $10 = i3 % 128;
            int i4 = i3 % 2;
            if (str != null) {
                cArr = str.toCharArray();
                int i5 = $10 + Opcodes.LMUL;
                $11 = i5 % 128;
                int i6 = i5 % 2;
            } else {
                cArr = str;
            }
            char[] cArr2 = cArr;
            m mVar = new m();
            char[] cArr3 = e;
            switch (cArr3 != null ? (char) 11 : '\b') {
                case '\b':
                    break;
                default:
                    int i7 = $10 + 57;
                    $11 = i7 % 128;
                    int i8 = i7 % 2;
                    int length = cArr3.length;
                    char[] cArr4 = new char[length];
                    for (int i9 = 0; i9 < length; i9++) {
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr3[i9])};
                            Object obj = o.e.a.s.get(-1401577988);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(Color.alpha(0) + 17, (char) Color.blue(0), (Process.myTid() >> 22) + 76);
                                byte length2 = (byte) $$a.length;
                                Object[] objArr3 = new Object[1];
                                j((byte) 0, length2, (byte) (length2 - 4), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr3 = cArr4;
                    break;
            }
            try {
                Object[] objArr4 = {Integer.valueOf(g)};
                Object obj2 = o.e.a.s.get(-1401577988);
                if (obj2 == null) {
                    Class cls2 = (Class) o.e.a.c(17 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) ExpandableListView.getPackedPositionGroup(0L), 76 - KeyEvent.getDeadChar(0, 0));
                    byte length3 = (byte) $$a.length;
                    Object[] objArr5 = new Object[1];
                    j((byte) 0, length3, (byte) (length3 - 4), objArr5);
                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj2);
                }
                char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                char[] cArr5 = new char[i];
                switch (i % 2 != 0) {
                    case false:
                        i2 = i;
                        break;
                    default:
                        i2 = i - 1;
                        cArr5[i2] = (char) (cArr2[i2] - b);
                        break;
                }
                if (i2 > 1) {
                    int i10 = $10 + 79;
                    $11 = i10 % 128;
                    int i11 = i10 % 2;
                    mVar.b = 0;
                    while (mVar.b < i2) {
                        mVar.e = cArr2[mVar.b];
                        mVar.a = cArr2[mVar.b + 1];
                        if (mVar.e == mVar.a) {
                            cArr5[mVar.b] = (char) (mVar.e - b);
                            cArr5[mVar.b + 1] = (char) (mVar.a - b);
                        } else {
                            try {
                                Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                Object obj3 = o.e.a.s.get(696901393);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(View.MeasureSpec.getMode(0) + 10, (char) (8856 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), (ViewConfiguration.getTouchSlop() >> 8) + 324);
                                    byte b2 = (byte) 0;
                                    byte b3 = b2;
                                    Object[] objArr7 = new Object[1];
                                    j(b2, b3, b3, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                    o.e.a.s.put(696901393, obj3);
                                }
                                switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h) {
                                    case false:
                                        switch (mVar.c == mVar.d) {
                                            case false:
                                                int i12 = (mVar.c * charValue) + mVar.h;
                                                int i13 = (mVar.d * charValue) + mVar.i;
                                                cArr5[mVar.b] = cArr3[i12];
                                                cArr5[mVar.b + 1] = cArr3[i13];
                                                break;
                                            default:
                                                mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                int i14 = (mVar.c * charValue) + mVar.i;
                                                int i15 = (mVar.d * charValue) + mVar.h;
                                                cArr5[mVar.b] = cArr3[i14];
                                                cArr5[mVar.b + 1] = cArr3[i15];
                                                break;
                                        }
                                    default:
                                        try {
                                            Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                            Object obj4 = o.e.a.s.get(1075449051);
                                            if (obj4 == null) {
                                                Class cls4 = (Class) o.e.a.c(View.combineMeasuredStates(0, 0) + 11, (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), View.MeasureSpec.getMode(0) + 65);
                                                byte b4 = (byte) 0;
                                                byte b5 = (byte) (b4 + 1);
                                                Object[] objArr9 = new Object[1];
                                                j(b4, b5, (byte) (b5 - 1), objArr9);
                                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                o.e.a.s.put(1075449051, obj4);
                                            }
                                            int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                            int i16 = (mVar.d * charValue) + mVar.h;
                                            cArr5[mVar.b] = cArr3[intValue];
                                            cArr5[mVar.b + 1] = cArr3[i16];
                                            break;
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                }
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        mVar.b += 2;
                    }
                }
                for (int i17 = 0; i17 < i; i17++) {
                    int i18 = $10 + 47;
                    $11 = i18 % 128;
                    int i19 = i18 % 2;
                    cArr5[i17] = (char) (cArr5[i17] ^ 13722);
                }
                objArr[0] = new String(cArr5);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int[] r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 1010
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.e.k(int[], int, java.lang.Object[]):void");
    }

    private static void l(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        int i5 = 2;
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(g)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getFadingEdgeLength() >> 16), (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 65 - TextUtils.getOffsetAfter("", 0));
                byte b2 = (byte) 0;
                byte b3 = b2;
                Object[] objArr3 = new Object[1];
                p(b2, b3, (byte) (b3 - 1), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            if (intValue == -1) {
                int i6 = $10 + 87;
                $11 = i6 % 128;
                int i7 = i6 % 2;
                z = true;
            } else {
                z = false;
            }
            if (z) {
                byte[] bArr = j;
                switch (bArr == null) {
                    case false:
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i8 = 0;
                        while (i8 < length) {
                            try {
                                Object[] objArr4 = {Integer.valueOf(bArr[i8])};
                                Object obj2 = o.e.a.s.get(494867332);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(19 - (KeyEvent.getMaxKeyCode() >> 16), (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 16426), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + Opcodes.FCMPG);
                                    byte b4 = (byte) i5;
                                    byte b5 = (byte) (b4 - 2);
                                    Object[] objArr5 = new Object[1];
                                    p(b4, b5, (byte) (b5 - 1), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(494867332, obj2);
                                }
                                bArr2[i8] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                i8++;
                                i5 = 2;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        bArr = bArr2;
                    default:
                        switch (bArr != null ? (char) 22 : 'V') {
                            case 22:
                                int i9 = $11 + Opcodes.LREM;
                                $10 = i9 % 128;
                                int i10 = i9 % 2;
                                byte[] bArr3 = j;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(f)};
                                    Object obj3 = o.e.a.s.get(-2120899312);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(10 - ExpandableListView.getPackedPositionChild(0L), (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 66 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)));
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr7 = new Object[1];
                                        p(b6, b7, (byte) (b7 - 1), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj3);
                                    }
                                    intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (g ^ (-5810760824076169584L))));
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                intValue = (short) (((short) (n[i2 + ((int) (f ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (g ^ (-5810760824076169584L))));
                                break;
                        }
                }
            }
            if (intValue > 0) {
                fVar.d = ((i2 + intValue) - 2) + ((int) (f ^ (-5810760824076169584L))) + (z ? 1 : 0);
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(i), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 11, (char) Drawable.resolveOpacity(0, 0), 603 - Color.red(0))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = j;
                    if (bArr4 != null) {
                        int i11 = $11 + 41;
                        $10 = i11 % 128;
                        int i12 = i11 % 2;
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        for (int i13 = 0; i13 < length2; i13++) {
                            bArr5[i13] = (byte) (bArr4[i13] ^ (-5810760824076169584L));
                        }
                        int i14 = $11 + 3;
                        $10 = i14 % 128;
                        int i15 = i14 % 2;
                        bArr4 = bArr5;
                    }
                    boolean z2 = bArr4 != null;
                    fVar.c = 1;
                    while (fVar.c < intValue) {
                        switch (!z2) {
                            case false:
                                byte[] bArr6 = j;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                                break;
                            default:
                                short[] sArr = n;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b));
                                break;
                        }
                        sb.append(fVar.e);
                        fVar.b = fVar.e;
                        fVar.c++;
                        int i16 = $11 + 81;
                        $10 = i16 % 128;
                        int i17 = i16 % 2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
