package androidx.biometric;

import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import androidx.fragment.app.FragmentActivity;
import org.bouncycastle.i18n.MessageBundle;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\biometric\Utils.smali */
class Utils {
    private Utils() {
    }

    static boolean isUnknownError(int errMsgId) {
        switch (errMsgId) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
                return false;
            case 6:
            default:
                return true;
        }
    }

    static void launchDeviceCredentialConfirmation(String loggingTag, FragmentActivity activity, Bundle bundle, Runnable onLaunch) {
        CharSequence title;
        CharSequence subtitle;
        if (!(activity instanceof DeviceCredentialHandlerActivity)) {
            Log.e(loggingTag, "Failed to check device credential. Parent handler not found.");
            return;
        }
        DeviceCredentialHandlerActivity handlerActivity = (DeviceCredentialHandlerActivity) activity;
        KeyguardManager keyguardManager = (KeyguardManager) handlerActivity.getSystemService(KeyguardManager.class);
        if (keyguardManager == null) {
            Log.e(loggingTag, "Failed to check device credential. KeyguardManager was null.");
            handlerActivity.handleDeviceCredentialResult(0);
            return;
        }
        if (bundle != null) {
            title = bundle.getCharSequence(MessageBundle.TITLE_ENTRY);
            subtitle = bundle.getCharSequence("subtitle");
        } else {
            title = null;
            subtitle = null;
        }
        Intent intent = keyguardManager.createConfirmDeviceCredentialIntent(title, subtitle);
        if (intent == null) {
            Log.e(loggingTag, "Failed to check device credential. Got null intent from Keyguard.");
            handlerActivity.handleDeviceCredentialResult(0);
            return;
        }
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstance();
        bridge.setConfirmingDeviceCredential(true);
        bridge.startIgnoringReset();
        if (onLaunch != null) {
            onLaunch.run();
        }
        intent.setFlags(134742016);
        handlerActivity.startActivityForResult(intent, 0);
    }

    static void maybeFinishHandler(FragmentActivity activity) {
        if ((activity instanceof DeviceCredentialHandlerActivity) && !activity.isFinishing()) {
            activity.finish();
        }
    }

    static boolean shouldUseFingerprintForCrypto(Context context, String vendor, String model) {
        if (Build.VERSION.SDK_INT != 28) {
            return false;
        }
        return isVendorInList(context, vendor, R.array.crypto_fingerprint_fallback_vendors) || isModelInPrefixList(context, model, R.array.crypto_fingerprint_fallback_prefixes);
    }

    static boolean shouldHideFingerprintDialog(Context context, String model) {
        if (Build.VERSION.SDK_INT != 28) {
            return false;
        }
        return isModelInPrefixList(context, model, R.array.hide_fingerprint_instantly_prefixes);
    }

    private static boolean isVendorInList(Context context, String vendor, int resId) {
        if (vendor == null) {
            return false;
        }
        String[] vendorNames = context.getResources().getStringArray(resId);
        for (String vendorName : vendorNames) {
            if (vendor.equalsIgnoreCase(vendorName)) {
                return true;
            }
        }
        return false;
    }

    private static boolean isModelInPrefixList(Context context, String model, int resId) {
        if (model == null) {
            return false;
        }
        String[] modelPrefixes = context.getResources().getStringArray(resId);
        for (String modelPrefix : modelPrefixes) {
            if (model.startsWith(modelPrefix)) {
                return true;
            }
        }
        return false;
    }

    static boolean isConfirmingDeviceCredential() {
        DeviceCredentialHandlerBridge bridge = DeviceCredentialHandlerBridge.getInstanceIfNotNull();
        return bridge != null && bridge.isConfirmingDeviceCredential();
    }
}
