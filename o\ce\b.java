package o.ce;

import android.media.AudioTrack;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import kotlin.text.Typography;
import o.a.m;
import o.e.a;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ce\b.smali */
public final class b extends SSLSocketFactory {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static char d;
    private static char[] e;
    private final SSLSocketFactory c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        a = 1;
        e = new char[]{30585, 30497, 30555, 30526, 30587, 30525, 30556, 30584, 30531};
        d = (char) 17046;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r8 = 73 - r8
            int r9 = r9 + 4
            byte[] r0 = o.ce.b.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L30
        L15:
            r3 = r2
        L16:
            int r9 = r9 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L30:
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ce.b.g(byte, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -116, 4, 37};
        $$b = Opcodes.IF_ACMPEQ;
    }

    public b(TrustManager[] trustManagerArr) throws KeyManagementException, NoSuchAlgorithmException, IOException, KeyStoreException, CertificateException {
        Object[] objArr = new Object[1];
        f(7 - KeyEvent.normalizeMetaState(0), "\u0005\u0002\u0000\u0003\u0004\u0000㘏", (byte) (104 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), objArr);
        SSLContext sSLContext = SSLContext.getInstance(((String) objArr[0]).intern());
        sSLContext.init(null, trustManagerArr, null);
        this.c = sSLContext.getSocketFactory();
    }

    @Override // javax.net.ssl.SSLSocketFactory
    public final String[] getDefaultCipherSuites() {
        int i = a + 25;
        b = i % 128;
        switch (i % 2 != 0 ? 'P' : Typography.dollar) {
            case 'P':
                this.c.getDefaultCipherSuites();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c.getDefaultCipherSuites();
        }
    }

    @Override // javax.net.ssl.SSLSocketFactory
    public final String[] getSupportedCipherSuites() {
        int i = b + 79;
        a = i % 128;
        switch (i % 2 == 0 ? '5' : '(') {
            case '(':
                return this.c.getSupportedCipherSuites();
            default:
                this.c.getSupportedCipherSuites();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // javax.net.SocketFactory
    public final Socket createSocket() throws IOException {
        int i = b + 47;
        a = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? Typography.quote : '%') {
            case '%':
                Socket d2 = d(this.c.createSocket());
                int i2 = b + 25;
                a = i2 % 128;
                switch (i2 % 2 != 0 ? '8' : (char) 25) {
                    case '8':
                        return d2;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                d(this.c.createSocket());
                throw null;
        }
    }

    @Override // javax.net.ssl.SSLSocketFactory
    public final Socket createSocket(Socket socket, String str, int i, boolean z) throws IOException {
        int i2 = b + 81;
        a = i2 % 128;
        int i3 = i2 % 2;
        Socket d2 = d(this.c.createSocket(socket, str, i, z));
        int i4 = b + 81;
        a = i4 % 128;
        int i5 = i4 % 2;
        return d2;
    }

    @Override // javax.net.SocketFactory
    public final Socket createSocket(String str, int i) throws IOException, UnknownHostException {
        int i2 = b + 31;
        a = i2 % 128;
        int i3 = i2 % 2;
        Socket d2 = d(this.c.createSocket(str, i));
        int i4 = a + 51;
        b = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return d2;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // javax.net.SocketFactory
    public final Socket createSocket(String str, int i, InetAddress inetAddress, int i2) throws IOException, UnknownHostException {
        int i3 = a + 9;
        b = i3 % 128;
        int i4 = i3 % 2;
        Socket d2 = d(this.c.createSocket(str, i, inetAddress, i2));
        int i5 = a + Opcodes.DNEG;
        b = i5 % 128;
        int i6 = i5 % 2;
        return d2;
    }

    @Override // javax.net.SocketFactory
    public final Socket createSocket(InetAddress inetAddress, int i) throws IOException {
        int i2 = b + 1;
        a = i2 % 128;
        boolean z = i2 % 2 == 0;
        Socket createSocket = this.c.createSocket(inetAddress, i);
        switch (z) {
            case true:
                d(createSocket);
                throw null;
            default:
                return d(createSocket);
        }
    }

    @Override // javax.net.SocketFactory
    public final Socket createSocket(InetAddress inetAddress, int i, InetAddress inetAddress2, int i2) throws IOException {
        int i3 = b + Opcodes.DNEG;
        a = i3 % 128;
        switch (i3 % 2 == 0 ? 'Y' : '\f') {
            case '\f':
                return d(this.c.createSocket(inetAddress, i, inetAddress2, i2));
            default:
                d(this.c.createSocket(inetAddress, i, inetAddress2, i2));
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static Socket d(Socket socket) {
        switch (!(socket instanceof SSLSocket)) {
            case false:
                int i = b + 89;
                a = i % 128;
                switch (i % 2 == 0 ? 'O' : '\t') {
                    case Opcodes.IASTORE /* 79 */:
                        String[] strArr = new String[0];
                        Object[] objArr = new Object[1];
                        f((ViewConfiguration.getJumpTapTimeout() << Opcodes.IREM) * 11, "\u0005\u0002\u0000\u0003\u0004\u0000㘏", (byte) (Opcodes.DMUL / TextUtils.indexOf("", "")), objArr);
                        strArr[0] = ((String) objArr[0]).intern();
                        ((SSLSocket) socket).setEnabledProtocols(strArr);
                        break;
                    default:
                        Object[] objArr2 = new Object[1];
                        f((ViewConfiguration.getJumpTapTimeout() >> 16) + 7, "\u0005\u0002\u0000\u0003\u0004\u0000㘏", (byte) (TextUtils.indexOf("", "") + Opcodes.DSUB), objArr2);
                        ((SSLSocket) socket).setEnabledProtocols(new String[]{((String) objArr2[0]).intern()});
                        break;
                }
        }
        int i2 = b + 21;
        a = i2 % 128;
        int i3 = i2 % 2;
        return socket;
    }

    private static void f(int i, String str, byte b2, Object[] objArr) {
        char[] cArr;
        int i2;
        char c;
        switch (str != null ? (char) 28 : '6') {
            case Opcodes.ISTORE /* 54 */:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        m mVar = new m();
        char[] cArr3 = e;
        int i3 = -1401577988;
        int i4 = 2;
        if (cArr3 != null) {
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int i5 = 0;
            while (i5 < length) {
                int i6 = $10 + Opcodes.LSHR;
                $11 = i6 % 128;
                int i7 = i6 % i4;
                try {
                    Object[] objArr2 = {Integer.valueOf(cArr3[i5])};
                    Object obj = a.s.get(Integer.valueOf(i3));
                    if (obj == null) {
                        Class cls = (Class) a.c(16 - MotionEvent.axisFromString(""), (char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), (Process.myPid() >> 22) + 76);
                        byte b3 = (byte) 0;
                        byte b4 = b3;
                        Object[] objArr3 = new Object[1];
                        g(b3, b4, (byte) (b4 - 1), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        a.s.put(-1401577988, obj);
                    }
                    cArr4[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i5++;
                    i3 = -1401577988;
                    i4 = 2;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            int i8 = $11 + 95;
            $10 = i8 % 128;
            int i9 = i8 % 2;
            cArr3 = cArr4;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(d)};
            Object obj2 = a.s.get(-1401577988);
            if (obj2 == null) {
                Class cls2 = (Class) a.c(17 - (ViewConfiguration.getEdgeSlop() >> 16), (char) (AndroidCharacter.getMirror('0') - '0'), 76 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                byte b5 = (byte) 0;
                byte b6 = b5;
                Object[] objArr5 = new Object[1];
                g(b5, b6, (byte) (b6 - 1), objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                a.s.put(-1401577988, obj2);
            }
            char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
            char[] cArr5 = new char[i];
            if (i % 2 != 0) {
                i2 = i - 1;
                cArr5[i2] = (char) (cArr2[i2] - b2);
            } else {
                i2 = i;
            }
            if (i2 > 1) {
                mVar.b = 0;
                while (true) {
                    switch (mVar.b < i2 ? '3' : 'F') {
                        case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                            break;
                        default:
                            mVar.e = cArr2[mVar.b];
                            mVar.a = cArr2[mVar.b + 1];
                            switch (mVar.e == mVar.a) {
                                case false:
                                    try {
                                        Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                        Object obj3 = a.s.get(696901393);
                                        if (obj3 != null) {
                                            c = '\r';
                                        } else {
                                            Class cls3 = (Class) a.c(10 - TextUtils.indexOf("", ""), (char) (8856 - (ViewConfiguration.getTouchSlop() >> 8)), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 323);
                                            byte length2 = (byte) $$a.length;
                                            Object[] objArr7 = new Object[1];
                                            g((byte) 0, length2, (byte) (length2 - 5), objArr7);
                                            c = '\r';
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                            a.s.put(696901393, obj3);
                                        }
                                        switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() == mVar.h ? 'U' : c) {
                                            case Opcodes.CASTORE /* 85 */:
                                                try {
                                                    Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                    Object obj4 = a.s.get(1075449051);
                                                    if (obj4 == null) {
                                                        Class cls4 = (Class) a.c(11 - ExpandableListView.getPackedPositionGroup(0L), (char) (ViewConfiguration.getLongPressTimeout() >> 16), TextUtils.indexOf("", "", 0, 0) + 65);
                                                        byte b7 = (byte) 0;
                                                        byte b8 = (byte) (b7 + 3);
                                                        Object[] objArr9 = new Object[1];
                                                        g(b7, b8, (byte) (b8 - 4), objArr9);
                                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                        a.s.put(1075449051, obj4);
                                                    }
                                                    int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                    int i10 = (mVar.d * charValue) + mVar.h;
                                                    cArr5[mVar.b] = cArr3[intValue];
                                                    cArr5[mVar.b + 1] = cArr3[i10];
                                                    break;
                                                } catch (Throwable th2) {
                                                    Throwable cause2 = th2.getCause();
                                                    if (cause2 == null) {
                                                        throw th2;
                                                    }
                                                    throw cause2;
                                                }
                                            default:
                                                if (mVar.c != mVar.d) {
                                                    int i11 = (mVar.c * charValue) + mVar.h;
                                                    int i12 = (mVar.d * charValue) + mVar.i;
                                                    cArr5[mVar.b] = cArr3[i11];
                                                    cArr5[mVar.b + 1] = cArr3[i12];
                                                    break;
                                                } else {
                                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                    int i13 = (mVar.c * charValue) + mVar.i;
                                                    int i14 = (mVar.d * charValue) + mVar.h;
                                                    cArr5[mVar.b] = cArr3[i13];
                                                    cArr5[mVar.b + 1] = cArr3[i14];
                                                    break;
                                                }
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                default:
                                    cArr5[mVar.b] = (char) (mVar.e - b2);
                                    cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                    break;
                            }
                            mVar.b += 2;
                    }
                }
            }
            int i15 = 0;
            while (true) {
                switch (i15 < i ? '\\' : '.') {
                    case Opcodes.DUP2 /* 92 */:
                        int i16 = $11 + 75;
                        $10 = i16 % 128;
                        int i17 = i16 % 2;
                        cArr5[i15] = (char) (cArr5[i15] ^ 13722);
                        i15++;
                    default:
                        objArr[0] = new String(cArr5);
                        return;
                }
            }
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }
}
