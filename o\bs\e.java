package o.bs;

import android.content.Context;
import o.bs.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\e.smali */
public abstract class e<T extends c> {
    private final T c;
    private static int b = 0;
    private static int a = 1;

    public e(T t) {
        this.c = t;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0033. Please report as an issue. */
    protected final boolean e(Context context) {
        int i = b + 47;
        a = i % 128;
        int i2 = i % 2;
        switch (this.c.d(context) == i.a ? '(' : (char) 21) {
            case '(':
                int i3 = b;
                int i4 = ((i3 | 45) << 1) - (i3 ^ 45);
                a = i4 % 128;
                switch (i4 % 2 == 0 ? '!' : ':') {
                }
                return true;
            default:
                int i5 = a;
                int i6 = (i5 & 81) + (i5 | 81);
                b = i6 % 128;
                switch (i6 % 2 != 0 ? (char) 30 : 'W') {
                    case 30:
                        int i7 = 80 / 0;
                        return false;
                    default:
                        return false;
                }
        }
    }
}
