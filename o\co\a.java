package o.co;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import fr.antelop.sdk.card.EmvApplicationActivationMethodType;
import o.ee.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\co\a.smali */
public final class a implements d<EmvApplicationActivationMethod> {
    private final String a;
    private final String b;
    private final String c;
    private final e d;
    private final String e;
    private static int i = 0;
    private static int g = 1;

    @Override // o.ee.d
    public final /* synthetic */ EmvApplicationActivationMethod a() {
        int i2 = i;
        int i3 = (i2 ^ 67) + ((i2 & 67) << 1);
        g = i3 % 128;
        int i4 = i3 % 2;
        EmvApplicationActivationMethod h = h();
        int i5 = i;
        int i6 = (i5 ^ 89) + ((i5 & 89) << 1);
        g = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                return h;
            default:
                int i7 = 14 / 0;
                return h;
        }
    }

    a(String str, e eVar, String str2, String str3, String str4) {
        this.c = str;
        this.d = eVar;
        this.b = str2;
        this.a = str3;
        this.e = str4;
    }

    public final a c() {
        a aVar = new a(this.c, this.d, this.b, this.a, this.e);
        int i2 = g;
        int i3 = ((i2 | 41) << 1) - (i2 ^ 41);
        i = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return aVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String d() {
        int i2 = g;
        int i3 = (i2 & Opcodes.LSHR) + (i2 | Opcodes.LSHR);
        i = i3 % 128;
        int i4 = i3 % 2;
        String str = this.c;
        int i5 = (i2 ^ 81) + ((i2 & 81) << 1);
        i = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 7 : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final EmvApplicationActivationMethodType e() {
        int i2 = i + Opcodes.LSHL;
        g = i2 % 128;
        int i3 = i2 % 2;
        EmvApplicationActivationMethodType c = this.d.c();
        int i4 = i;
        int i5 = (i4 & Opcodes.LUSHR) + (i4 | Opcodes.LUSHR);
        g = i5 % 128;
        int i6 = i5 % 2;
        return c;
    }

    public final String b() {
        int i2 = g;
        int i3 = (i2 + 94) - 1;
        i = i3 % 128;
        int i4 = i3 % 2;
        String str = this.a;
        int i5 = i2 + 91;
        i = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String f() {
        int i2 = g;
        int i3 = ((i2 | 63) << 1) - (i2 ^ 63);
        i = i3 % 128;
        int i4 = i3 % 2;
        String str = this.e;
        int i5 = ((i2 | 49) << 1) - (i2 ^ 49);
        i = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String g() {
        int i2 = i + 65;
        int i3 = i2 % 128;
        g = i3;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                String str = this.b;
                int i4 = (i3 + 22) - 1;
                i = i4 % 128;
                int i5 = i4 % 2;
                return str;
        }
    }

    private EmvApplicationActivationMethod h() {
        EmvApplicationActivationMethod emvApplicationActivationMethod = new EmvApplicationActivationMethod(this);
        int i2 = g;
        int i3 = ((i2 | 33) << 1) - (i2 ^ 33);
        i = i3 % 128;
        int i4 = i3 % 2;
        return emvApplicationActivationMethod;
    }
}
