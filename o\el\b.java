package o.el;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationStatus;
import kotlin.text.Typography;
import o.ee.a;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\el\b.smali */
public final class b implements a<EmvApplicationStatus> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    public static final b e;
    public static final b g;
    private static int[] h;
    private static final /* synthetic */ b[] j;
    private static char[] k;
    private static int l;
    private static char m;

    /* renamed from: o, reason: collision with root package name */
    private static int f69o;
    private final String f;
    private final int i;

    static void c() {
        h = new int[]{-1057233583, 1562719966, 314159410, 2020111343, 1695609675, 707650048, -209430884, 1891362753, -924130380, 622844827, -1584371441, 391128932, -2088839530, 1272721292, -2093757777, 480112997, -913123190, -2055503478};
        k = new char[]{30531, 30572, 30532, 30585, 30564, 30560, 30542, 30566, 30570, 30571, 30563, 30540, 30528, 30587, 30539, 30538};
        m = (char) 17041;
    }

    static void init$0() {
        $$a = new byte[]{106, 35, -45, 57};
        $$b = Opcodes.GETFIELD;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 4
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r9 = r9 + 69
            byte[] r0 = o.el.b.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r8
            r8 = r7
            goto L34
        L16:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1b:
            byte r4 = (byte) r7
            int r5 = r3 + 1
            r1[r3] = r4
            int r8 = r8 + 1
            if (r5 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L34:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.b.q(int, short, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ b[] b() {
        int i = l + Opcodes.LREM;
        f69o = i % 128;
        switch (i % 2 != 0) {
            case true:
                return new b[]{d, a, e, c, b, g};
            default:
                b[] bVarArr = new b[102];
                bVarArr[0] = d;
                bVarArr[1] = a;
                bVarArr[4] = e;
                bVarArr[5] = c;
                bVarArr[4] = b;
                bVarArr[3] = g;
                return bVarArr;
        }
    }

    public static b valueOf(String str) {
        int i = f69o + 5;
        l = i % 128;
        int i2 = i % 2;
        b bVar = (b) Enum.valueOf(b.class, str);
        int i3 = f69o + 37;
        l = i3 % 128;
        int i4 = i3 % 2;
        return bVar;
    }

    public static b[] values() {
        int i = f69o + 73;
        l = i % 128;
        int i2 = i % 2;
        b[] bVarArr = (b[]) j.clone();
        int i3 = f69o + 31;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 3 : (char) 29) {
            case 29:
                return bVarArr;
            default:
                int i4 = 9 / 0;
                return bVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i = l + Opcodes.LREM;
        f69o = i % 128;
        int i2 = i % 2;
        EmvApplicationStatus d2 = d();
        int i3 = f69o + 93;
        l = i3 % 128;
        int i4 = i3 % 2;
        return d2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        f69o = 1;
        c();
        Object[] objArr = new Object[1];
        p(6 - (ViewConfiguration.getPressedStateDuration() >> 16), "\u0005\u0002\u000f\u0005\u0000\u000b", (byte) (((Process.getThreadPriority(0) + 20) >> 6) + 33), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        n(new int[]{133429554, 2140030862, 127532773, -408114569}, Color.green(0) + 6, objArr2);
        d = new b(intern, 0, ((String) objArr2[0]).intern(), 0);
        Object[] objArr3 = new Object[1];
        p(Color.blue(0) + 6, "\u0001\u0004\u0000\u0005\t\n", (byte) (((Process.getThreadPriority(0) + 20) >> 6) + 81), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        p(6 - (ViewConfiguration.getDoubleTapTimeout() >> 16), "\u0004\u0000\n\u0003\f\u000f", (byte) (ExpandableListView.getPackedPositionGroup(0L) + 81), objArr4);
        a = new b(intern2, 1, ((String) objArr4[0]).intern(), 4);
        Object[] objArr5 = new Object[1];
        n(new int[]{-2045695147, 1797051214, -1627158399, -2142156746, -1883305034, -1258441633, -1548174791, 417966763, -1148304880, 971758751}, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 18, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        n(new int[]{-1047744414, 1971154197, -1755485865, 1409951257, -611793367, 1099606170, 134010466, -1206617526}, (KeyEvent.getMaxKeyCode() >> 16) + 16, objArr6);
        e = new b(intern3, 2, ((String) objArr6[0]).intern(), 1);
        Object[] objArr7 = new Object[1];
        n(new int[]{-2045695147, 1797051214, -1627158399, -2142156746, -1499728099, 1963357932}, 9 - ExpandableListView.getPackedPositionChild(0L), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        n(new int[]{1689970833, -522430942, -1496836855, 537524813}, 5 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr8);
        c = new b(intern4, 3, ((String) objArr8[0]).intern(), 2);
        Object[] objArr9 = new Object[1];
        p(7 - (KeyEvent.getMaxKeyCode() >> 16), "\f\n\u000b\t\f\t㙵", (byte) (TextUtils.lastIndexOf("", '0', 0) + Opcodes.ISHL), objArr9);
        String intern5 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        n(new int[]{2100226548, 1607450985, -178969890, -208802426}, Drawable.resolveOpacity(0, 0) + 7, objArr10);
        b = new b(intern5, 4, ((String) objArr10[0]).intern(), 5);
        Object[] objArr11 = new Object[1];
        n(new int[]{-2045695147, 1797051214, -1627158399, -2142156746, -1883305034, -1258441633, 1119192220, -546367788, 1175785856, -613854245}, View.resolveSize(0, 0) + 17, objArr11);
        String intern6 = ((String) objArr11[0]).intern();
        Object[] objArr12 = new Object[1];
        n(new int[]{133429554, 2140030862, -8504011, 882338622, -1163420039, -90219547, -1661203224, -1504761152, 1319282482, -971261680}, 17 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr12);
        g = new b(intern6, 5, ((String) objArr12[0]).intern(), 3);
        j = b();
        int i = l + 97;
        f69o = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    private b(String str, int i, String str2, int i2) {
        this.f = str2;
        this.i = i2;
    }

    public final String e() {
        int i = f69o + Opcodes.LREM;
        l = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.f;
            default:
                throw null;
        }
    }

    public static b b(String str) {
        b[] values;
        int length;
        int i;
        int i2 = f69o + 25;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? '.' : Typography.less) {
            case '<':
                values = values();
                length = values.length;
                i = 0;
                break;
            default:
                values = values();
                length = values.length;
                i = 1;
                break;
        }
        while (i < length) {
            int i3 = l + 91;
            f69o = i3 % 128;
            int i4 = i3 % 2;
            b bVar = values[i];
            switch (str.equals(bVar.f) ? 'U' : '4') {
                case Opcodes.CASTORE /* 85 */:
                    int i5 = l + 89;
                    f69o = i5 % 128;
                    int i6 = i5 % 2;
                    return bVar;
                default:
                    i++;
            }
        }
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        n(new int[]{1885485903, 1296590928, 736400896, 880671491, -553088184, 2133178911, -1505585941, 2055519646, -1042528983, -201092654, -957235181, 729990717, 1183864465, -1133927649, -10226443, 1664072976, 1532112630, 1356234843}, AndroidCharacter.getMirror('0') - 15, objArr);
        throw new IllegalArgumentException(sb.append(((String) objArr[0]).intern()).append(str).toString());
    }

    public static b d(b bVar, b bVar2) {
        int i = l;
        int i2 = i + Opcodes.LUSHR;
        int i3 = i2 % 128;
        f69o = i3;
        int i4 = i2 % 2;
        switch (bVar == null) {
            case false:
                if (bVar2 == null) {
                    return bVar;
                }
                switch (bVar.i >= bVar2.i ? Typography.greater : (char) 23) {
                    case '>':
                        int i5 = i3 + 3;
                        l = i5 % 128;
                        int i6 = i5 % 2;
                        return bVar;
                    default:
                        int i7 = i + 97;
                        f69o = i7 % 128;
                        switch (i7 % 2 != 0) {
                            case true:
                                return bVar2;
                            default:
                                int i8 = 12 / 0;
                                return bVar2;
                        }
                }
            default:
                return bVar2;
        }
    }

    /* renamed from: o.el.b$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\el\b$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        static final /* synthetic */ int[] a;
        private static int c;
        private static int d = 1;

        static {
            c = 0;
            int[] iArr = new int[b.values().length];
            a = iArr;
            try {
                iArr[b.d.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                a[b.a.ordinal()] = 2;
                int i = d + 37;
                c = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[b.e.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[b.c.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[b.g.ordinal()] = 5;
                int i2 = d;
                int i3 = (i2 & Opcodes.DNEG) + (i2 | Opcodes.DNEG);
                c = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                a[b.b.ordinal()] = 6;
            } catch (NoSuchFieldError e6) {
            }
        }
    }

    public final EmvApplicationStatus d() {
        int i = l + Opcodes.DNEG;
        f69o = i % 128;
        int i2 = i % 2;
        switch (AnonymousClass2.a[ordinal()]) {
            case 1:
                EmvApplicationStatus emvApplicationStatus = EmvApplicationStatus.Active;
                int i3 = f69o + Opcodes.LUSHR;
                l = i3 % 128;
                switch (i3 % 2 != 0 ? 'L' : (char) 15) {
                    case 15:
                        return emvApplicationStatus;
                    default:
                        throw null;
                }
            case 2:
                return EmvApplicationStatus.Locked;
            case 3:
                return EmvApplicationStatus.ActivationRequired;
            case 4:
                return EmvApplicationStatus.Activating;
            case 5:
                return EmvApplicationStatus.ActivationRefused;
            case 6:
                Object[] objArr = new Object[1];
                n(new int[]{-903600934, -1676230526, -469849548, -965005376, 2102539732, 1099928139, -395749325, 1249596149, -925607526, -1974850679, -1323974958, 661370244, -1372976707, 269909891, 1182347331, -1812326946, -936165731, -437663727, -436614554, 2062744538, 639867043, 1243586724}, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 41, objArr);
                throw new RuntimeException(((String) objArr[0]).intern());
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                n(new int[]{-246588532, 190043383, 1263049697, -1582221550, -594088917, 1971631262, -434355668, -1260572247, -1764714031, 1758636440}, TextUtils.indexOf((CharSequence) "", '0') + 19, objArr2);
                throw new UnsupportedOperationException(sb.append(((String) objArr2[0]).intern()).append(name()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void n(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 868
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.b.n(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1024
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.el.b.p(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
