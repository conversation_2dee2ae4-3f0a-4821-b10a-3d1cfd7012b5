package cz.muni.fi.xklinex.whiteboxAES;

import androidx.core.app.FrameMetricsAggregator;
import java.io.Serializable;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\TBox8to128.smali */
public class TBox8to128 implements Serializable {
    public static final int IWIDTH = 1;
    public static final int ROWS = 256;
    private static final long serialVersionUID = 5273192538806301472L;
    protected State[] tbl = null;

    public TBox8to128() {
        init();
    }

    public static State[] initNew() {
        return new State[256];
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.deepEquals(this.tbl, ((TBox8to128) obj).tbl);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.deepHashCode(this.tbl) + FrameMetricsAggregator.EVERY_DURATION;
    }

    public final void init() {
        this.tbl = initNew();
        for (int i = 0; i < 256; i++) {
            this.tbl[i] = new State();
        }
    }

    public State lookup(byte b) {
        return this.tbl[AES.posIdx(b)];
    }

    public String toString() {
        StringBuilder append = new StringBuilder().append("TBox8to128{tbl=").append(Arrays.toString(this.tbl)).append("; size=");
        State[] stateArr = this.tbl;
        return append.append(stateArr != null ? stateArr.length : -1).append("}").toString();
    }
}
