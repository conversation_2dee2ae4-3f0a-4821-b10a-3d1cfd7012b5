package com.avaldigitallabs.mbocc.wallet;

import com.avaldigitallabs.mbocc.wallet.interfaces.DigitalWalletListener;
import com.avaldigitallabs.mbocc.wallet.types.WalletStatus;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AsyncRequestType;
import fr.antelop.sdk.Wallet;
import fr.antelop.sdk.WalletManagerCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.CustomerCredentialsRequiredReason;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalWalletCallback.smali */
public class DigitalWalletCallback implements WalletManagerCallback {
    private DigitalWalletListener listener;

    DigitalWalletCallback(DigitalWalletListener listener) {
        this.listener = listener;
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onConnectionError(AntelopError antelopError, Object o2) {
        this.listener.onWalletStatus(WalletStatus.CONNECTION_ERROR, null);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onConnectionSuccess(Wallet wallet, Object o2) {
        this.listener.onWalletStatus(WalletStatus.CONNECTION_SUCCESS, wallet);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onCredentialsRequired(CustomerCredentialsRequiredReason customerCredentialsRequiredReason, AntelopError antelopError, Object o2) {
        this.listener.onWalletStatus(WalletStatus.CREDENTIALS_REQUIRED, null);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onProvisioningRequired(Object o2) {
        this.listener.onWalletStatus(WalletStatus.PROVISION_REQUIRED, null);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onAsyncRequestSuccess(AsyncRequestType asyncRequestType, Object o2) {
        if (asyncRequestType == AsyncRequestType.Delete) {
            this.listener.onWalletStatus(WalletStatus.DESTROY_SUCCESS, null);
        } else {
            this.listener.onWalletStatus(WalletStatus.ACTIVITY_IGNORE, null);
        }
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onAsyncRequestError(AsyncRequestType asyncRequestType, AntelopError antelopError, Object o2) {
        this.listener.onWalletStatus(WalletStatus.ACTIVITY_IGNORE, null);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onLocalAuthenticationSuccess(CustomerAuthenticationMethodType customerAuthenticationMethodType, Object o2) {
        this.listener.onWalletStatus(WalletStatus.ACTIVITY_IGNORE, null);
    }

    @Override // fr.antelop.sdk.WalletManagerCallback
    public void onLocalAuthenticationError(CustomerAuthenticationMethodType customerAuthenticationMethodType, LocalAuthenticationErrorReason localAuthenticationErrorReason, String s, Object o2) {
        this.listener.onWalletStatus(WalletStatus.ACTIVITY_IGNORE, null);
    }
}
