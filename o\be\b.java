package o.be;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.EligibilityDenialReason;
import java.lang.reflect.Method;
import o.a.o;
import o.ee.a;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\b.smali */
final class b implements a<EligibilityDenialReason> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final b a;
    public static final b b;
    public static final b c;
    public static final b d;
    public static final b e;
    public static final b f;
    public static final b g;
    public static final b h;
    public static final b j;
    private static int k;
    private static int m;
    private static final /* synthetic */ b[] n;

    /* renamed from: o, reason: collision with root package name */
    private static long f41o;
    private static int q;
    private static int r;
    private static char t;
    private final int i;
    private final int l;

    static void e() {
        m = 874635302;
        t = (char) 17957;
        k = 161105445;
        f41o = -1226832382912361980L;
    }

    static void init$0() {
        $$a = new byte[]{73, -116, 106, -1};
        $$b = Opcodes.I2C;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(int r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.be.b.$$a
            int r6 = r6 * 3
            int r6 = r6 + 1
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r8 = 109 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r4 = r8
            r3 = r2
            r8 = r7
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            r4 = r0[r7]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r8 = r8 + 1
            int r6 = r6 + r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.be.b.u(int, int, short, java.lang.Object[]):void");
    }

    private static /* synthetic */ b[] d() {
        int i = q;
        int i2 = i + 1;
        r = i2 % 128;
        int i3 = i2 % 2;
        b[] bVarArr = {c, a, b, d, e, j, h, g, f};
        int i4 = i + 77;
        r = i4 % 128;
        switch (i4 % 2 != 0 ? '`' : 'R') {
            case Opcodes.DASTORE /* 82 */:
                return bVarArr;
            default:
                throw null;
        }
    }

    public static b valueOf(String str) {
        int i = q + 51;
        r = i % 128;
        char c2 = i % 2 != 0 ? 'Q' : 'R';
        b bVar = (b) Enum.valueOf(b.class, str);
        switch (c2) {
            case Opcodes.DASTORE /* 82 */:
                return bVar;
            default:
                throw null;
        }
    }

    public static b[] values() {
        int i = r + 77;
        q = i % 128;
        int i2 = i % 2;
        b[] bVarArr = (b[]) n.clone();
        int i3 = r + 83;
        q = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 17 : 'Q') {
            case 17:
                int i4 = 12 / 0;
                return bVarArr;
            default:
                return bVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i = q + 47;
        r = i % 128;
        int i2 = i % 2;
        EligibilityDenialReason c2 = c();
        int i3 = r + 9;
        q = i3 % 128;
        int i4 = i3 % 2;
        return c2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        q = 1;
        e();
        Object[] objArr = new Object[1];
        s(ViewConfiguration.getMinimumFlingVelocity() >> 16, "臌ꭞ琬ﻻ⢏䧘⥣힍ಉ汭鏸\ue4e3\udb41⳦", (char) (34851 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), "쯍\uebbc⏀\uf888", "䰡ꠊ\uf034뗧", objArr);
        c = new b(((String) objArr[0]).intern(), 0, 4301, 10);
        Object[] objArr2 = new Object[1];
        p(1 - Color.red(0), "￦\ufffb￼\u000b\t\u0006\u0007\u0007\f￪\u000b\u0006￥\u0005\u0006\u0000\n\t￼￭\n", (-16777195) - Color.rgb(0, 0, 0), Color.green(0) + Opcodes.MONITORENTER, true, objArr2);
        a = new b(((String) objArr2[0]).intern(), 1, 4302, 9);
        Object[] objArr3 = new Object[1];
        p((ViewConfiguration.getPressedStateDuration() >> 16) + 3, "\b\u0007\u0012\uffe7\b￭\ufffe\u0005\ufffe\t\u0001", View.MeasureSpec.makeMeasureSpec(0, 0) + 11, 192 - KeyEvent.normalizeMetaState(0), false, objArr3);
        b = new b(((String) objArr3[0]).intern(), 2, 4303, 8);
        Object[] objArr4 = new Object[1];
        p(14 - TextUtils.indexOf("", "", 0, 0), "\ufffe\r\u000b\b\t\t\u000e￬\r\b\uffe7￼\uffff\uffe7�", 15 - TextUtils.indexOf("", "", 0), 193 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), true, objArr4);
        d = new b(((String) objArr4[0]).intern(), 3, 4304, 3);
        Object[] objArr5 = new Object[1];
        p((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 1, "�￢\ufffe\uffff\u000e\f\t\n\n\u000f￭\u000e\t￨\uffff", 14 - TextUtils.indexOf((CharSequence) "", '0'), 190 - TextUtils.lastIndexOf("", '0', 0), true, objArr5);
        e = new b(((String) objArr5[0]).intern(), 4, 4305, 2);
        Object[] objArr6 = new Object[1];
        p((KeyEvent.getMaxKeyCode() >> 16) + 5, "\b\u000b\u0013\u0001\u0000￠\u0001\u0012\u0005\uffff\u0001￪\u000b\u0010\uffdd\b", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 15, 188 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), false, objArr6);
        j = new b(((String) objArr6[0]).intern(), 5, 4306, 7);
        Object[] objArr7 = new Object[1];
        s(ViewConfiguration.getMinimumFlingVelocity() >> 16, "\u0099絡嶶▢岕紮퇇ଃ\uea86䖛脄쁚獘\ued30㵓祿塄ྦ\uf797ᑜ", (char) TextUtils.getOffsetAfter("", 0), "롎馡⌝찗", "䰡ꠊ\uf034뗧", objArr7);
        h = new b(((String) objArr7[0]).intern(), 6, 4307, 5);
        Object[] objArr8 = new Object[1];
        s(ViewConfiguration.getJumpTapTimeout() >> 16, "♭\uf52c杊ퟎ풥꿥覣옭访䥝翥\uf535طꩲ", (char) (55267 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), "ﺜ䄔\ue31a鷗", "䰡ꠊ\uf034뗧", objArr8);
        g = new b(((String) objArr8[0]).intern(), 7, 4308, 6);
        Object[] objArr9 = new Object[1];
        s(Color.green(0), "㑡⯎싧늚꽀橪✿ዀ刵\ue01fࡖ鲅銅깢㤿䞏杭疋榧錦ᐁ踪㲜\u31e8", (char) TextUtils.getTrimmedLength(""), "즉汈\ud85c꽿", "䰡ꠊ\uf034뗧", objArr9);
        f = new b(((String) objArr9[0]).intern(), 8, 4309, 4);
        n = d();
        int i = r + 49;
        q = i % 128;
        int i2 = i % 2;
    }

    private b(String str, int i, int i2, int i3) {
        this.i = i2;
        this.l = i3;
    }

    final int b() {
        int i = r + 11;
        int i2 = i % 128;
        q = i2;
        Object obj = null;
        switch (i % 2 == 0 ? '^' : '5') {
            case Opcodes.SALOAD /* 53 */:
                int i3 = this.l;
                int i4 = i2 + Opcodes.DREM;
                r = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 1 : '0') {
                    case '0':
                        return i3;
                    default:
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.be.b c(int r8) {
        /*
            int r0 = o.be.b.r
            int r0 = r0 + 35
            int r1 = r0 % 128
            o.be.b.q = r1
            int r0 = r0 % 2
            r1 = 95
            if (r0 != 0) goto L12
            r0 = 71
            goto L13
        L12:
            r0 = r1
        L13:
            r2 = 0
            switch(r0) {
                case 95: goto L1e;
                default: goto L17;
            }
        L17:
            o.be.b[] r0 = values()
            int r3 = r0.length
        L1c:
            r4 = r2
            goto L24
        L1e:
            o.be.b[] r0 = values()
            int r3 = r0.length
            goto L1c
        L24:
            if (r4 >= r3) goto L28
            r5 = 1
            goto L29
        L28:
            r5 = r2
        L29:
            r6 = 0
            switch(r5) {
                case 1: goto L2e;
                default: goto L2d;
            }
        L2d:
            goto L4f
        L2e:
            r5 = r0[r4]
            int r7 = r5.i
            if (r7 != r8) goto L4c
            int r8 = o.be.b.q
            int r8 = r8 + r1
            int r0 = r8 % 128
            o.be.b.r = r0
            int r8 = r8 % 2
            if (r8 == 0) goto L42
            r8 = 11
            goto L44
        L42:
            r8 = 45
        L44:
            switch(r8) {
                case 45: goto L48;
                default: goto L47;
            }
        L47:
            goto L49
        L48:
            return r5
        L49:
            throw r6     // Catch: java.lang.Throwable -> L4a
        L4a:
            r8 = move-exception
            throw r8
        L4c:
            int r4 = r4 + 1
            goto L24
        L4f:
            int r8 = o.be.b.q
            int r8 = r8 + 103
            int r0 = r8 % 128
            o.be.b.r = r0
            int r8 = r8 % 2
            if (r8 != 0) goto L5c
            return r6
        L5c:
            throw r6     // Catch: java.lang.Throwable -> L5d
        L5d:
            r8 = move-exception
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.be.b.c(int):o.be.b");
    }

    /* renamed from: o.be.b$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\be\b$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int b;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            b = 0;
            d = 1;
            int[] iArr = new int[b.values().length];
            e = iArr;
            try {
                iArr[b.c.ordinal()] = 1;
                int i = b;
                int i2 = (i & 73) + (i | 73);
                d = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[b.a.ordinal()] = 2;
                int i3 = (d + 96) - 1;
                b = i3 % 128;
                if (i3 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[b.d.ordinal()] = 3;
                int i4 = b + 77;
                d = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[b.e.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[b.b.ordinal()] = 5;
                int i6 = b;
                int i7 = ((i6 | 31) << 1) - (i6 ^ 31);
                d = i7 % 128;
                if (i7 % 2 == 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[b.j.ordinal()] = 6;
                int i8 = d;
                int i9 = ((i8 | 49) << 1) - (i8 ^ 49);
                b = i9 % 128;
                if (i9 % 2 != 0) {
                }
            } catch (NoSuchFieldError e7) {
            }
            try {
                e[b.h.ordinal()] = 7;
                int i10 = b;
                int i11 = (i10 & Opcodes.LUSHR) + (i10 | Opcodes.LUSHR);
                d = i11 % 128;
                int i12 = i11 % 2;
            } catch (NoSuchFieldError e8) {
            }
            try {
                e[b.g.ordinal()] = 8;
            } catch (NoSuchFieldError e9) {
            }
            try {
                e[b.f.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
        }
    }

    public final EligibilityDenialReason c() {
        Object obj = null;
        switch (AnonymousClass5.e[ordinal()]) {
            case 1:
                return EligibilityDenialReason.OsNotSupported;
            case 2:
                return EligibilityDenialReason.OsVersionNotSupported;
            case 3:
                return EligibilityDenialReason.NfcNotSupported;
            case 4:
                return EligibilityDenialReason.HceNotSupported;
            case 5:
            case 6:
                return EligibilityDenialReason.DeviceNotAllowed;
            case 7:
                return EligibilityDenialReason.DeviceSupportExpired;
            case 8:
                EligibilityDenialReason eligibilityDenialReason = EligibilityDenialReason.RootNotAllowed;
                int i = r + 65;
                q = i % 128;
                switch (i % 2 == 0 ? 'P' : '#') {
                    case 'P':
                        obj.hashCode();
                        throw null;
                    default:
                        return eligibilityDenialReason;
                }
            case 9:
                EligibilityDenialReason eligibilityDenialReason2 = EligibilityDenialReason.ProductNotSupportedBySdk;
                int i2 = q + 61;
                r = i2 % 128;
                switch (i2 % 2 == 0) {
                    case true:
                        return eligibilityDenialReason2;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                p('B' - AndroidCharacter.getMirror('0'), "ￃ\uffdd\b\u0018\u000f\u0004\ufff9ￃ\u0007\b\u0017\u0006\b\u0013\u001b\b\u0011\ufff8", 18 - (ViewConfiguration.getFadingEdgeLength() >> 16), 182 - TextUtils.indexOf("", "", 0), true, objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 538
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.be.b.p(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:15:0x0051. Please report as an issue. */
    private static void s(int i, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] charArray;
        char[] cArr2;
        if (str3 != null) {
            int i2 = $10 + 73;
            $11 = i2 % 128;
            int i3 = i2 % 2;
            cArr = str3.toCharArray();
        } else {
            cArr = str3;
        }
        char[] cArr3 = cArr;
        int i4 = 0;
        switch (str2 != null) {
            case true:
                int i5 = $10 + 27;
                $11 = i5 % 128;
                if (i5 % 2 == 0) {
                    str2.toCharArray();
                    throw null;
                }
                charArray = str2.toCharArray();
                break;
            default:
                charArray = str2;
                break;
        }
        char[] cArr4 = charArray;
        if (str != null) {
            cArr2 = str.toCharArray();
            int i6 = $11 + 7;
            $10 = i6 % 128;
            switch (i6 % 2 != 0) {
            }
        } else {
            cArr2 = str;
        }
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = cArr2.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getScrollBarSize() >> 8) + 10, (char) (20954 - (ViewConfiguration.getPressedStateDuration() >> 16)), TextUtils.getTrimmedLength("") + 344);
                    byte b2 = (byte) ($$a[3] + 1);
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    u(b2, b3, (byte) (b3 | 10), objArr3);
                    String str4 = (String) objArr3[i4];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i4] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(ImageFormat.getBitsPerPixel(i4) + 11, (char) ExpandableListView.getPackedPositionGroup(0L), 207 - View.MeasureSpec.getMode(i4));
                        byte b4 = (byte) ($$a[3] + 1);
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        u(b4, b5, (byte) (b5 | 8), objArr5);
                        String str5 = (String) objArr5[i4];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i4] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i7 = cArr5[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr6[intValue]);
                        objArr6[1] = Integer.valueOf(i7);
                        objArr6[i4] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c(Color.red(i4) + 11, (char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ExpandableListView.getPackedPositionForChild(i4, i4) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(i4, i4) == 0L ? 0 : -1)) + 282);
                            byte b6 = (byte) ($$a[3] + 1);
                            byte b7 = b6;
                            Object[] objArr7 = new Object[1];
                            u(b6, b7, (byte) (b7 | 6), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 19, (char) (TextUtils.indexOf((CharSequence) "", '0') + 14688), 111 - TextUtils.lastIndexOf("", '0', 0));
                                byte b8 = (byte) ($$a[3] + 1);
                                byte b9 = b8;
                                Object[] objArr9 = new Object[1];
                                u(b8, b9, (byte) (b9 + 3), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r6[oVar.e]) ^ (f41o ^ 6565854932352255525L)) ^ ((int) (k ^ 6565854932352255525L))) ^ ((char) (t ^ 6565854932352255525L)));
                            oVar.e++;
                            int i8 = $10 + 43;
                            $11 = i8 % 128;
                            switch (i8 % 2 != 0) {
                                case true:
                                default:
                                    i4 = 0;
                            }
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
