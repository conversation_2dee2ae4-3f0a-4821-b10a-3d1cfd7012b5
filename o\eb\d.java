package o.eb;

import android.graphics.drawable.Drawable;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.TimeZone;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eb\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static final TimeZone b;
    private static char[] c;
    private static char d;
    private static long e;
    private static int f;
    private static int h;
    private static char i;
    private static char j;

    static void b() {
        c = new char[]{11399, 52610, 61089, 36817, 43056, 18704, 27252, 2837, 9619, 50914, 59343, 32800, 41305, 17012, 25439, 7601, 16096, 57294, 63520, 39185, 47734, 23375, 11490, 52701, 61105, 36757, 43113, 11497, 52618, 61160, 36819, 43068, 18707, 27197, 2837, 9627, 50920, 59346, 32810, 41237, 17003, 25428, 7590, 16041, 57305, 63534, 39237, 11392, 52611, 61175, 36804, 43061, 18708, 27253, 2837, 9629, 50916, 59340, 32800, 41305, 16999, 25438, 7611, 16108, 57229, 63528, 39179, 47741, 23380, 30130, 5780, 14301, 53282, 61715, 37413, 45854, 41581};
        e = 2328897662096494061L;
        d = (char) 2911;
        i = (char) 44991;
        j = (char) 62573;
        a = (char) 18000;
    }

    static void init$0() {
        $$a = new byte[]{38, -75, -91, -62};
        $$b = Opcodes.FNEG;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.eb.d.$$a
            int r6 = r6 + 102
            int r8 = r8 * 4
            int r8 = r8 + 4
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r6 = r6 + r7
            int r7 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.l(short, int, byte, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f = 1;
        b();
        Drawable.resolveOpacity(0, 0);
        Object[] objArr = new Object[1];
        k("奭ᑀ슒\u0c4e", ExpandableListView.getPackedPositionChild(0L) + 4, objArr);
        b = TimeZone.getTimeZone(((String) objArr[0]).intern());
        int i2 = f + Opcodes.DDIV;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '[' : 'E') {
            case 'E':
                return;
            default:
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:77:0x021b, code lost:
    
        if (((java.lang.String) r12[0]).intern().equals(r0) != false) goto L107;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0240, code lost:
    
        r6 = new java.lang.StringBuilder();
        r12 = new java.lang.Object[1];
        k("奭ᑀ슒\u0c4e", 3 - (android.view.ViewConfiguration.getTouchSlop() >> 8), r12);
        r0 = r6.append(((java.lang.String) r12[0]).intern()).append(r0).toString();
        r6 = java.util.TimeZone.getTimeZone(r0);
        r7 = r6.getID();
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x0276, code lost:
    
        if (r7.equals(r0) != false) goto L106;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x0278, code lost:
    
        r14 = new java.lang.Object[1];
        k("\ua87e\uf753", 1 - android.view.KeyEvent.normalizeMetaState(0), r14);
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0296, code lost:
    
        if (r7.replace(((java.lang.String) r14[0]).intern(), "").equals(r0) == false) goto L104;
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0299, code lost:
    
        r3 = new java.lang.StringBuilder();
        r7 = new java.lang.Object[1];
        k("ꦷﾩꭥ䵌塶⧃犳ਰܯ低ᬐ瑗ᝯ긏ᇄ琯䨓ࠠ뿿쇄\ue0b9ꄅܯ低㗝붺尳\ueab8莤ժ\ue4b9貒嫽ẅ", android.view.MotionEvent.axisFromString("") + 34, r7);
        r0 = r3.append(((java.lang.String) r7[0]).intern()).append(r0);
        r7 = new java.lang.Object[1];
        g((char) (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), android.view.View.MeasureSpec.makeMeasureSpec(0, 0) + 27, (android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (android.util.TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 20, r7);
     */
    /* JADX WARN: Code restructure failed: missing block: B:83:0x02f5, code lost:
    
        throw new java.lang.IllegalArgumentException(r0.append(((java.lang.String) r7[0]).intern()).append(r6.getID()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x02f6, code lost:
    
        r0 = r6;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x023c, code lost:
    
        if (((java.lang.String) r12[0]).intern().equals(r0) != false) goto L107;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.util.Date d(java.lang.String r19) {
        /*
            Method dump skipped, instructions count: 892
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.d(java.lang.String):java.util.Date");
    }

    /* JADX WARN: Code restructure failed: missing block: B:33:0x0022, code lost:
    
        if (r5 < r4.length()) goto L20;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static boolean a(java.lang.String r4, int r5, char r6) {
        /*
            int r0 = o.eb.d.f
            int r0 = r0 + 85
            int r1 = r0 % 128
            o.eb.d.h = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 0: goto L1b;
                default: goto L14;
            }
        L14:
            int r0 = r4.length()
            if (r5 >= r0) goto L2a
            goto L27
        L1b:
            int r0 = r4.length()
            r3 = 61
            int r3 = r3 / r2
            if (r5 >= r0) goto L4c
        L24:
            goto L30
        L25:
            r4 = move-exception
            throw r4
        L27:
            r0 = 54
            goto L2c
        L2a:
            r0 = 72
        L2c:
            switch(r0) {
                case 54: goto L24;
                default: goto L2f;
            }
        L2f:
            goto L4c
        L30:
            char r4 = r4.charAt(r5)
            if (r4 != r6) goto L39
            r4 = 42
            goto L3b
        L39:
            r4 = 87
        L3b:
            switch(r4) {
                case 87: goto L4c;
                default: goto L3e;
            }
        L3e:
            int r4 = o.eb.d.f
            int r4 = r4 + 83
            int r5 = r4 % 128
            o.eb.d.h = r5
            int r4 = r4 % 2
            if (r4 == 0) goto L4b
            return r2
        L4b:
            return r1
        L4c:
            int r4 = o.eb.d.f
            int r4 = r4 + 5
            int r5 = r4 % 128
            o.eb.d.h = r5
            int r4 = r4 % 2
            if (r4 != 0) goto L59
            return r2
        L59:
            r4 = 0
            r4.hashCode()     // Catch: java.lang.Throwable -> L5e
            throw r4     // Catch: java.lang.Throwable -> L5e
        L5e:
            r4 = move-exception
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.a(java.lang.String, int, char):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static int c(java.lang.String r9, int r10, int r11) throws java.lang.NumberFormatException {
        /*
            int r0 = o.eb.d.f
            int r0 = r0 + 125
            int r1 = r0 % 128
            o.eb.d.h = r1
            int r0 = r0 % 2
            r0 = 0
            r1 = 1
            if (r10 < 0) goto L10
            r2 = r0
            goto L11
        L10:
            r2 = r1
        L11:
            switch(r2) {
                case 0: goto L16;
                default: goto L14;
            }
        L14:
            goto Lcd
        L16:
            int r2 = r9.length()
            if (r11 > r2) goto L1f
            r2 = 80
            goto L21
        L1f:
            r2 = 67
        L21:
            switch(r2) {
                case 67: goto L14;
                default: goto L24;
            }
        L24:
            if (r10 > r11) goto Lcd
        L28:
            if (r10 >= r11) goto L2c
            r2 = r1
            goto L2d
        L2c:
            r2 = r0
        L2d:
            java.lang.String r3 = "坊맶唩㽹\ue0ce㜁Ģ\ueb4b\ue9a9瓓\udb8b厫艓Ȁ䒚좨"
            r4 = 10
            switch(r2) {
                case 1: goto L37;
                default: goto L34;
            }
        L34:
            r2 = r10
            r5 = r0
            goto L77
        L37:
            int r2 = r10 + 1
            char r5 = r9.charAt(r10)
            int r5 = java.lang.Character.digit(r5, r4)
            if (r5 < 0) goto L45
            int r5 = -r5
            goto L77
        L45:
            java.lang.NumberFormatException r2 = new java.lang.NumberFormatException
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            long r5 = android.os.SystemClock.currentThreadTimeMillis()
            r7 = -1
            int r5 = (r5 > r7 ? 1 : (r5 == r7 ? 0 : -1))
            int r5 = r5 + 15
            java.lang.Object[] r1 = new java.lang.Object[r1]
            k(r3, r5, r1)
            r0 = r1[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r0 = r4.append(r0)
            java.lang.String r9 = r9.substring(r10, r11)
            java.lang.StringBuilder r9 = r0.append(r9)
            java.lang.String r9 = r9.toString()
            r2.<init>(r9)
            throw r2
        L77:
            if (r2 >= r11) goto L7c
            r6 = 24
            goto L7e
        L7c:
            r6 = 56
        L7e:
            switch(r6) {
                case 24: goto L83;
                default: goto L81;
            }
        L81:
            int r9 = -r5
            goto Lc2
        L83:
            int r6 = r2 + 1
            char r2 = r9.charAt(r2)
            int r2 = java.lang.Character.digit(r2, r4)
            if (r2 < 0) goto L94
            int r5 = r5 * 10
            int r5 = r5 - r2
            r2 = r6
            goto L77
        L94:
            java.lang.NumberFormatException r2 = new java.lang.NumberFormatException
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            r4.<init>()
            int r5 = android.view.KeyEvent.normalizeMetaState(r0)
            int r5 = 16 - r5
            java.lang.Object[] r1 = new java.lang.Object[r1]
            k(r3, r5, r1)
            r0 = r1[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            java.lang.StringBuilder r0 = r4.append(r0)
            java.lang.String r9 = r9.substring(r10, r11)
            java.lang.StringBuilder r9 = r0.append(r9)
            java.lang.String r9 = r9.toString()
            r2.<init>(r9)
            throw r2
        Lc2:
            int r10 = o.eb.d.h
            int r10 = r10 + 111
            int r11 = r10 % 128
            o.eb.d.f = r11
            int r10 = r10 % 2
            return r9
        Lcd:
            java.lang.NumberFormatException r10 = new java.lang.NumberFormatException
            r10.<init>(r9)
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.c(java.lang.String, int, int):int");
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x0030  */
    /* JADX WARN: Removed duplicated region for block: B:13:0x0035 A[LOOP:0: B:1:0x0000->B:13:0x0035, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:14:0x0038 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static int b(java.lang.String r4, int r5) {
        /*
        L0:
            int r0 = r4.length()
            if (r5 >= r0) goto L44
            int r0 = o.eb.d.h
            int r0 = r0 + 111
            int r1 = r0 % 128
            o.eb.d.f = r1
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            if (r0 != 0) goto L24
            char r0 = r4.charAt(r5)
            r3 = 14
            if (r0 < r3) goto L1f
            r3 = r2
            goto L20
        L1f:
            r3 = r1
        L20:
            switch(r3) {
                case 0: goto L38;
                default: goto L23;
            }
        L23:
            goto L2c
        L24:
            char r0 = r4.charAt(r5)
            r3 = 48
            if (r0 < r3) goto L38
        L2c:
            r3 = 57
            if (r0 <= r3) goto L31
            r1 = r2
        L31:
            switch(r1) {
                case 0: goto L35;
                default: goto L34;
            }
        L34:
            goto L38
        L35:
            int r5 = r5 + 1
            goto L0
        L38:
            int r4 = o.eb.d.f
            int r4 = r4 + 91
            int r0 = r4 % 128
            o.eb.d.h = r0
            int r4 = r4 % 2
            return r5
        L44:
            int r4 = r4.length()
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.b(java.lang.String, int):int");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 738
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.g(char, int, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x0063, code lost:
    
        r7 = 58224;
        r9 = r5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0069, code lost:
    
        if (r9 >= 16) goto L28;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x006b, code lost:
    
        r11 = r5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0070, code lost:
    
        switch(r11) {
            case 0: goto L32;
            default: goto L78;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0086, code lost:
    
        r11 = r6[r8];
        r13 = r6[r5];
        r16 = r6;
        r5 = (r13 + r7) ^ ((r13 << 4) + ((char) (o.eb.d.i ^ 8439748517800462901L)));
        r6 = r13 >>> 5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00a4, code lost:
    
        r14 = new java.lang.Object[4];
        r14[3] = java.lang.Integer.valueOf(o.eb.d.j);
        r14[2] = java.lang.Integer.valueOf(r6);
        r14[r8] = java.lang.Integer.valueOf(r5);
        r14[0] = java.lang.Integer.valueOf(r11);
        r5 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00cf, code lost:
    
        if (r5 == null) goto L37;
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x00d2, code lost:
    
        r0 = (java.lang.Class) o.e.a.c((android.os.Process.myTid() >> 22) + 11, (char) (android.widget.ExpandableListView.getPackedPositionForGroup(0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForGroup(0) == 0 ? 0 : -1)), android.graphics.Color.alpha(0) + 603);
        r5 = new java.lang.Class[4];
        r5[0] = java.lang.Integer.TYPE;
        r5[r8] = java.lang.Integer.TYPE;
        r5[2] = java.lang.Integer.TYPE;
        r5[3] = java.lang.Integer.TYPE;
        r5 = r0.getMethod("C", r5);
        o.e.a.s.put(-1512468642, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0114, code lost:
    
        r0 = ((java.lang.Character) ((java.lang.reflect.Method) r5).invoke(null, r14)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0121, code lost:
    
        r16[r8] = r0;
        r21 = r9;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0139, code lost:
    
        r9 = new java.lang.Object[]{java.lang.Integer.valueOf(r16[0]), java.lang.Integer.valueOf((r0 + r7) ^ ((r0 << 4) + ((char) (o.eb.d.a ^ 8439748517800462901L)))), java.lang.Integer.valueOf(r0 >>> 5), java.lang.Integer.valueOf(o.eb.d.d)};
        r0 = o.e.a.s.get(-1512468642);
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0162, code lost:
    
        if (r0 == null) goto L43;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x01b4, code lost:
    
        r16[0] = ((java.lang.Character) ((java.lang.reflect.Method) r0).invoke(null, r9)).charValue();
        r7 = r7 - 40503;
        r9 = r21 + 1;
        r0 = o.eb.d.$10 + 59;
        o.eb.d.$11 = r0 % 128;
        r0 = r0 % 2;
        r6 = r16;
        r5 = 0;
        r8 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0165, code lost:
    
        r0 = ((java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getPressedStateDuration() >> 16) + 11, (char) ((-1) - android.view.MotionEvent.axisFromString("")), ((byte) android.view.KeyEvent.getModifierMetaStateMask()) + 604)).getMethod("C", java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(-1512468642, r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x01cd, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x01ce, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x01d2, code lost:
    
        if (r1 != null) goto L49;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x01d4, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x01d5, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x01d6, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x01d7, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x01db, code lost:
    
        if (r1 != null) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x01dd, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x01de, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0073, code lost:
    
        r16 = r6;
        r4[r3.b] = r16[0];
        r4[r3.b + 1] = r16[1];
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x01df, code lost:
    
        r0 = new java.lang.Object[]{r3, r3};
        r5 = o.e.a.s.get(2062727845);
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x01f0, code lost:
    
        if (r5 == null) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x022e, code lost:
    
        ((java.lang.reflect.Method) r5).invoke(null, r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x0234, code lost:
    
        r6 = r16;
        r5 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x01f3, code lost:
    
        r5 = ((java.lang.Class) o.e.a.c((android.os.Process.myPid() >> 22) + 10, (char) (30725 - android.text.TextUtils.getOffsetBefore("", 0)), (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 614)).getMethod("A", java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(2062727845, r5);
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x023a, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x023b, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x023f, code lost:
    
        if (r1 != null) goto L65;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x0241, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x0242, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x006d, code lost:
    
        r11 = r8;
     */
    /* JADX WARN: Removed duplicated region for block: B:11:0x0039  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 608
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eb.d.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
