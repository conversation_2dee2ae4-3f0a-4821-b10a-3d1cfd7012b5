package com.vasco.digipass.sdk.utils.utilities.wbc;

import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import cz.muni.fi.xklinex.whiteboxAES.AES;
import cz.muni.fi.xklinex.whiteboxAES.State;
import cz.muni.fi.xklinex.whiteboxAES.generator.ExternalBijections;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\wbc\UtilitiesSDKWhiteBoxCryptography.smali */
public class UtilitiesSDKWhiteBoxCryptography {
    private UtilitiesSDKWhiteBoxCryptography() {
    }

    private static void a(byte[] bArr) throws UtilitiesSDKException {
        if (bArr == null) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.INPUT_DATA_NULL);
        }
        if (bArr.length == 0) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH);
        }
    }

    private static void b(byte[] bArr) throws UtilitiesSDKException {
        if (bArr == null) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.INITIAL_VECTOR_NULL);
        }
        if (bArr.length != 16) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.INITIAL_VECTOR_INCORRECT_LENGTH);
        }
    }

    public static UtilitiesSDKCryptoResponse decryptAESCTR(WBCTable wBCTable, byte[] bArr, byte[] bArr2) {
        return encryptAESCTR(wBCTable, bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse decryptAESCTRWithMasterKey(byte[] bArr, byte[] bArr2) {
        return encryptAESCTRWithMasterKey(bArr, bArr2);
    }

    public static UtilitiesSDKCryptoResponse encryptAESBlock(WBCTable wBCTable, byte[] bArr) {
        try {
            a(bArr);
            if (bArr.length != 16) {
                return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.INPUT_DATA_INCORRECT_LENGTH);
            }
            a(wBCTable);
            return new UtilitiesSDKCryptoResponse(0, a(wBCTable, true, bArr));
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static UtilitiesSDKCryptoResponse encryptAESCTR(WBCTable wBCTable, byte[] bArr, byte[] bArr2) {
        try {
            a(bArr2);
            b(bArr);
            a(wBCTable);
            AES aes = new AES();
            aes.setEncrypt(true);
            aes.init(wBCTable);
            byte[] bArr3 = new byte[bArr2.length];
            byte[] copyOf = Arrays.copyOf(bArr, 16);
            for (int i = 0; i < bArr2.length; i += 16) {
                byte[] a = a(aes, wBCTable.getExternalBijections(), copyOf);
                for (int i2 = 0; i2 < Math.min(16, bArr2.length - i); i2++) {
                    int i3 = i + i2;
                    bArr3[i3] = (byte) (bArr2[i3] ^ a[i2]);
                }
                for (int i4 = 15; i4 >= 0; i4--) {
                    byte b = (byte) (copyOf[i4] + 1);
                    copyOf[i4] = b;
                    if (b != 0) {
                        break;
                    }
                }
            }
            return new UtilitiesSDKCryptoResponse(0, bArr3);
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static UtilitiesSDKCryptoResponse encryptAESCTRWithMasterKey(byte[] bArr, byte[] bArr2) {
        try {
            return encryptAESCTR(new Serializer().deserializeResource("/encrypt_master_key_tables.dat"), bArr, bArr2);
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        }
    }

    private static void a(WBCTable wBCTable) throws UtilitiesSDKException {
        if (wBCTable != null) {
            return;
        }
        try {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID);
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID);
        }
    }

    private static void a(State state, AES aes, ExternalBijections externalBijections) {
        state.transpose();
        AES.applyExternalEnc(state, externalBijections, true);
        aes.crypt(state);
        AES.applyExternalEnc(state, externalBijections, false);
    }

    private static byte[] a(AES aes, ExternalBijections externalBijections, byte[] bArr) {
        State state = new State(bArr, true, false);
        a(state, aes, externalBijections);
        return state.getState();
    }

    private static byte[] a(WBCTable wBCTable, boolean z, byte[] bArr) {
        AES aes = new AES();
        aes.setEncrypt(z);
        aes.init(wBCTable);
        return a(aes, wBCTable.getExternalBijections(), bArr);
    }
}
