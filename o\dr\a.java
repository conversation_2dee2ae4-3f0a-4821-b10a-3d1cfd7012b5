package o.dr;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.CardDisplay;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import fr.antelop.sdk.transaction.hce.HceTransactionStatus;
import fr.antelop.sdk.transaction.hce.TransactionType;
import java.math.BigDecimal;
import java.util.Currency;
import java.util.Date;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.ej.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dr\a.smali */
public final class a {
    private boolean A;
    private boolean B;
    private boolean C;
    private boolean D;
    private CardDisplay E;
    private String H;
    private String a;
    private String b;
    private String c;
    private BigDecimal d;
    private e e;
    private String k;
    private String l;
    private String m;
    private String n;

    /* renamed from: o, reason: collision with root package name */
    private String f57o;
    private String q;
    private Date s;
    private String t;
    private String u;
    private c v;
    private int w;
    private String x;
    private d y;
    private boolean z;
    private static int I = 0;
    private static int G = 1;
    private String g = null;
    private String h = null;
    private String f = null;
    private String i = null;
    private String j = null;
    private double r = -1.0d;
    private double p = -1.0d;

    public final String b() {
        int i = (G + 44) - 1;
        int i2 = i % 128;
        I = i2;
        int i3 = i % 2;
        String str = this.c;
        int i4 = ((i2 | 39) << 1) - (i2 ^ 39);
        G = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final void d(String str) {
        int i = I;
        int i2 = ((i | 109) << 1) - (i ^ 109);
        G = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.c = str;
        switch (z) {
            case true:
                break;
            default:
                int i3 = 92 / 0;
                break;
        }
        int i4 = i + 79;
        G = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    public final String e() {
        String str;
        int i = G;
        int i2 = ((i | 85) << 1) - (i ^ 85);
        int i3 = i2 % 128;
        I = i3;
        switch (i2 % 2 != 0 ? Typography.less : Typography.amp) {
            case '&':
                str = this.b;
                break;
            default:
                str = this.b;
                int i4 = 90 / 0;
                break;
        }
        int i5 = (i3 ^ 13) + ((i3 & 13) << 1);
        G = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final Currency c() {
        int i = (I + Opcodes.IUSHR) - 1;
        int i2 = i % 128;
        G = i2;
        Object obj = null;
        switch (i % 2 == 0 ? (char) 14 : (char) 28) {
            case 28:
                e eVar = this.e;
                switch (eVar == null) {
                    case true:
                        int i3 = i2 + 7;
                        I = i3 % 128;
                        switch (i3 % 2 == 0) {
                            default:
                                int i4 = 33 / 0;
                            case true:
                                return null;
                        }
                    default:
                        Currency currency = Currency.getInstance(eVar.b());
                        int i5 = G;
                        int i6 = (i5 & 63) + (i5 | 63);
                        I = i6 % 128;
                        int i7 = i6 % 2;
                        return currency;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final BigDecimal d() {
        int i = G;
        int i2 = (i ^ 23) + ((i & 23) << 1);
        I = i2 % 128;
        switch (i2 % 2 != 0 ? '_' : Typography.greater) {
            case '>':
                BigDecimal bigDecimal = this.d;
                int i3 = ((i | 93) << 1) - (i ^ 93);
                I = i3 % 128;
                int i4 = i3 % 2;
                return bigDecimal;
            default:
                throw null;
        }
    }

    public final void d(BigDecimal bigDecimal) {
        int i = G;
        int i2 = (i & 37) + (i | 37);
        int i3 = i2 % 128;
        I = i3;
        boolean z = i2 % 2 != 0;
        this.d = bigDecimal;
        switch (z) {
            case false:
                break;
            default:
                int i4 = 45 / 0;
                break;
        }
        int i5 = (i3 ^ Opcodes.DNEG) + ((i3 & Opcodes.DNEG) << 1);
        G = i5 % 128;
        switch (i5 % 2 == 0 ? 'M' : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                return;
            default:
                throw null;
        }
    }

    public final String a() {
        int i = G;
        int i2 = (i & 63) + (i | 63);
        I = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                String str = this.q;
                switch (str == null) {
                    case false:
                        int i3 = ((i | 33) << 1) - (i ^ 33);
                        I = i3 % 128;
                        int i4 = i3 % 2;
                        return str;
                    default:
                        int i5 = (i + Opcodes.FDIV) - 1;
                        I = i5 % 128;
                        int i6 = i5 % 2;
                        int i7 = (i & 17) + (i | 17);
                        I = i7 % 128;
                        switch (i7 % 2 != 0) {
                            case true:
                                int i8 = 8 / 0;
                                return "";
                            default:
                                return "";
                        }
                }
        }
    }

    public final void a(String str) {
        int i = I;
        int i2 = ((i | 29) << 1) - (i ^ 29);
        int i3 = i2 % 128;
        G = i3;
        int i4 = i2 % 2;
        this.q = str;
        int i5 = (i3 & 47) + (i3 | 47);
        I = i5 % 128;
        int i6 = i5 % 2;
    }

    public final double i() {
        int i = G + 93;
        int i2 = i % 128;
        I = i2;
        int i3 = i % 2;
        double d = this.p;
        int i4 = i2 + 3;
        G = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return d;
        }
    }

    public final void d(double d) {
        int i = G;
        int i2 = (i ^ 55) + ((i & 55) << 1);
        I = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.p = d;
        switch (z) {
            case false:
                int i3 = i + 23;
                I = i3 % 128;
                int i4 = i3 % 2;
                return;
            default:
                throw null;
        }
    }

    public final double g() {
        int i = G;
        int i2 = ((i | 19) << 1) - (i ^ 19);
        I = i2 % 128;
        int i3 = i2 % 2;
        double d = this.r;
        int i4 = (i + 104) - 1;
        I = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 11 : 'T') {
            case Opcodes.BASTORE /* 84 */:
                return d;
            default:
                int i5 = 8 / 0;
                return d;
        }
    }

    public final void e(double d) {
        int i = I + 55;
        G = i % 128;
        char c = i % 2 == 0 ? '^' : '5';
        this.r = d;
        switch (c) {
            case Opcodes.DUP2_X2 /* 94 */:
                int i2 = 49 / 0;
                return;
            default:
                return;
        }
    }

    public final boolean f() {
        int i = I;
        int i2 = ((i | 49) << 1) - (i ^ 49);
        int i3 = i2 % 128;
        G = i3;
        int i4 = i2 % 2;
        switch (this.p == -1.0d ? 'R' : (char) 5) {
            default:
                int i5 = (i3 + 2) - 1;
                I = i5 % 128;
                switch (i5 % 2 != 0 ? 'Z' : (char) 29) {
                    case 29:
                        switch (this.r == -1.0d) {
                            case true:
                                int i6 = (i3 & Opcodes.LMUL) + (i3 | Opcodes.LMUL);
                                I = i6 % 128;
                                switch (i6 % 2 != 0 ? 'V' : (char) 22) {
                                    case 22:
                                        return false;
                                    default:
                                        throw null;
                                }
                        }
                    default:
                        throw null;
                }
            case 5:
                return true;
        }
    }

    public final String h() {
        int i = G;
        int i2 = (i + 20) - 1;
        I = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 21 : ',') {
            case 21:
                throw null;
            default:
                String str = this.a;
                int i3 = ((i | 11) << 1) - (i ^ 11);
                I = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    public final void e(String str) {
        int i = G;
        int i2 = ((i | 89) << 1) - (i ^ 89);
        I = i2 % 128;
        boolean z = i2 % 2 == 0;
        this.a = str;
        switch (z) {
            case false:
                int i3 = 94 / 0;
                return;
            default:
                return;
        }
    }

    public final String j() {
        int i = (G + 68) - 1;
        I = i % 128;
        switch (i % 2 != 0 ? '\t' : '@') {
            case '\t':
                throw null;
            default:
                return this.g;
        }
    }

    public final void b(String str) {
        int i = G;
        int i2 = ((i | 65) << 1) - (i ^ 65);
        int i3 = i2 % 128;
        I = i3;
        int i4 = i2 % 2;
        this.g = str;
        int i5 = i3 + 61;
        G = i5 % 128;
        switch (i5 % 2 == 0 ? '%' : '\t') {
            case '%':
                throw null;
            default:
                return;
        }
    }

    public final String o() {
        int i = I;
        int i2 = ((i | 73) << 1) - (i ^ 73);
        G = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                String str = this.h;
                int i3 = (i + 76) - 1;
                G = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return str;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final void c(String str) {
        int i = G;
        int i2 = (i ^ Opcodes.DDIV) + ((i & Opcodes.DDIV) << 1);
        int i3 = i2 % 128;
        I = i3;
        char c = i2 % 2 != 0 ? 'A' : (char) 1;
        this.h = str;
        switch (c) {
            case 1:
                int i4 = (i3 + Opcodes.IUSHR) - 1;
                G = i4 % 128;
                int i5 = i4 % 2;
                return;
            default:
                throw null;
        }
    }

    public final void h(String str) {
        int i = I + 77;
        G = i % 128;
        boolean z = i % 2 == 0;
        this.f = str;
        switch (z) {
            case false:
                return;
            default:
                int i2 = 15 / 0;
                return;
        }
    }

    public final String l() {
        int i = G;
        int i2 = ((i | 51) << 1) - (i ^ 51);
        I = i2 % 128;
        switch (i2 % 2 != 0 ? 'R' : '2') {
            case '2':
                return this.f;
            default:
                int i3 = 16 / 0;
                return this.f;
        }
    }

    public final void j(String str) {
        int i = G;
        int i2 = i + Opcodes.LUSHR;
        I = i2 % 128;
        char c = i2 % 2 != 0 ? (char) 5 : (char) 4;
        this.i = str;
        switch (c) {
            case 4:
                int i3 = (i + 4) - 1;
                I = i3 % 128;
                switch (i3 % 2 != 0 ? 'a' : (char) 16) {
                    case Opcodes.LADD /* 97 */:
                        int i4 = 27 / 0;
                        return;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    public final String k() {
        int i = G;
        int i2 = (i & 91) + (i | 91);
        int i3 = i2 % 128;
        I = i3;
        int i4 = i2 % 2;
        String str = this.i;
        int i5 = (i3 & 1) + (i3 | 1);
        G = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    public final void g(String str) {
        int i = G;
        int i2 = ((i | 35) << 1) - (i ^ 35);
        I = i2 % 128;
        char c = i2 % 2 != 0 ? '`' : '2';
        this.j = str;
        switch (c) {
            case Opcodes.IADD /* 96 */:
                int i3 = 71 / 0;
                break;
        }
        int i4 = (i + 74) - 1;
        I = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    public final String n() {
        int i = G;
        int i2 = (i & Opcodes.DNEG) + (i | Opcodes.DNEG);
        I = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.j;
        }
    }

    public final String m() {
        int i = G + Opcodes.DNEG;
        I = i % 128;
        switch (i % 2 != 0 ? '\n' : '0') {
            case '0':
                return this.l;
            default:
                int i2 = 60 / 0;
                return this.l;
        }
    }

    public final void f(String str) {
        int i = I;
        int i2 = (i & 55) + (i | 55);
        G = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.l = str;
        switch (z) {
            case true:
                return;
            default:
                int i3 = 8 / 0;
                return;
        }
    }

    public final String r() {
        String str;
        int i = G;
        int i2 = (i ^ 53) + ((i & 53) << 1);
        int i3 = i2 % 128;
        I = i3;
        switch (i2 % 2 != 0) {
            case false:
                str = this.k;
                break;
            default:
                str = this.k;
                int i4 = 61 / 0;
                break;
        }
        int i5 = (i3 ^ 37) + ((i3 & 37) << 1);
        G = i5 % 128;
        switch (i5 % 2 == 0 ? 'E' : ';') {
            case 'E':
                throw null;
            default:
                return str;
        }
    }

    public final void i(String str) {
        int i = (G + 40) - 1;
        I = i % 128;
        boolean z = i % 2 != 0;
        this.k = str;
        switch (z) {
            case true:
                throw null;
            default:
                return;
        }
    }

    public final String t() {
        String str;
        int i = I + 13;
        int i2 = i % 128;
        G = i2;
        switch (i % 2 == 0 ? 'L' : 'a') {
            case Base64.mimeLineLength /* 76 */:
                str = this.n;
                int i3 = 81 / 0;
                break;
            default:
                str = this.n;
                break;
        }
        int i4 = (i2 + 10) - 1;
        I = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String s() {
        int i = I;
        int i2 = i + Opcodes.DDIV;
        G = i2 % 128;
        int i3 = i2 % 2;
        String str = this.f57o;
        int i4 = ((i | 47) << 1) - (i ^ 47);
        G = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final Date q() {
        Date date = new Date(this.s.getTime());
        int i = G;
        int i2 = (i ^ 59) + ((i & 59) << 1);
        I = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 24 : (char) 16) {
            case 24:
                throw null;
            default:
                return date;
        }
    }

    public final void d(Date date) {
        int i = I + 35;
        int i2 = i % 128;
        G = i2;
        int i3 = i % 2;
        this.s = date;
        int i4 = (i2 & 53) + (i2 | 53);
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final e p() {
        e eVar;
        int i = G;
        int i2 = ((i | 31) << 1) - (i ^ 31);
        int i3 = i2 % 128;
        I = i3;
        switch (i2 % 2 != 0 ? 'D' : (char) 23) {
            case 23:
                eVar = this.e;
                break;
            default:
                eVar = this.e;
                int i4 = 4 / 0;
                break;
        }
        int i5 = i3 + 41;
        G = i5 % 128;
        int i6 = i5 % 2;
        return eVar;
    }

    public final void a(e eVar) {
        int i = G;
        int i2 = (i & Opcodes.DNEG) + (i | Opcodes.DNEG);
        I = i2 % 128;
        char c = i2 % 2 != 0 ? 'c' : (char) 7;
        this.e = eVar;
        switch (c) {
            case 7:
                return;
            default:
                int i3 = 18 / 0;
                return;
        }
    }

    public final String u() {
        int i = G + 109;
        int i2 = i % 128;
        I = i2;
        int i3 = i % 2;
        String str = this.m;
        int i4 = i2 + 93;
        G = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return str;
            default:
                int i5 = 3 / 0;
                return str;
        }
    }

    public final void l(String str) {
        int i = G;
        int i2 = i + 13;
        I = i2 % 128;
        int i3 = i2 % 2;
        this.m = str;
        int i4 = (i ^ Opcodes.DREM) + ((i & Opcodes.DREM) << 1);
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void m(String str) {
        int i = I;
        int i2 = i + 33;
        G = i2 % 128;
        int i3 = i2 % 2;
        this.n = str;
        int i4 = (i + 18) - 1;
        G = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void k(String str) {
        int i = G + 39;
        I = i % 128;
        boolean z = i % 2 == 0;
        this.f57o = str;
        switch (z) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final String w() {
        int i = I;
        int i2 = (i + 64) - 1;
        G = i2 % 128;
        int i3 = i2 % 2;
        String str = this.t;
        int i4 = ((i | 71) << 1) - (i ^ 71);
        G = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void n(String str) {
        int i = G;
        int i2 = (i + Opcodes.IUSHR) - 1;
        I = i2 % 128;
        int i3 = i2 % 2;
        this.t = str;
        int i4 = i + 65;
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String v() {
        int i = I;
        int i2 = ((i | 109) << 1) - (i ^ 109);
        G = i2 % 128;
        boolean z = i2 % 2 == 0;
        String str = this.x;
        switch (z) {
            case true:
                int i3 = 37 / 0;
            default:
                return str;
        }
    }

    public final void o(String str) {
        int i = G;
        int i2 = i + 77;
        I = i2 % 128;
        int i3 = i2 % 2;
        this.x = str;
        int i4 = i + 61;
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String y() {
        int i = G;
        int i2 = (i ^ 83) + ((i & 83) << 1);
        int i3 = i2 % 128;
        I = i3;
        int i4 = i2 % 2;
        String str = this.u;
        int i5 = (i3 & 55) + (i3 | 55);
        G = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void s(String str) {
        int i = I;
        int i2 = (i & 53) + (i | 53);
        int i3 = i2 % 128;
        G = i3;
        boolean z = i2 % 2 != 0;
        this.u = str;
        switch (z) {
            case true:
                int i4 = i3 + Opcodes.DDIV;
                I = i4 % 128;
                int i5 = i4 % 2;
                return;
            default:
                throw null;
        }
    }

    public final String x() {
        int i = G;
        int i2 = (i ^ 23) + ((i & 23) << 1);
        int i3 = i2 % 128;
        I = i3;
        int i4 = i2 % 2;
        String str = this.b;
        int i5 = i3 + 43;
        G = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 31 : Typography.greater) {
            case 31:
                int i6 = 58 / 0;
                return str;
            default:
                return str;
        }
    }

    public final void q(String str) {
        int i = G;
        int i2 = (i + 12) - 1;
        I = i2 % 128;
        int i3 = i2 % 2;
        this.b = str;
        int i4 = i + 45;
        I = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                int i5 = 14 / 0;
                return;
        }
    }

    public final int A() {
        int i = (G + 20) - 1;
        I = i % 128;
        switch (i % 2 != 0 ? ',' : Typography.greater) {
            case ',':
                int i2 = 77 / 0;
                return this.w;
            default:
                return this.w;
        }
    }

    public final void b(int i) {
        int i2 = I;
        int i3 = ((i2 | 29) << 1) - (i2 ^ 29);
        int i4 = i3 % 128;
        G = i4;
        boolean z = i3 % 2 != 0;
        this.w = i;
        switch (z) {
            case true:
                break;
            default:
                int i5 = 56 / 0;
                break;
        }
        int i6 = ((i4 | 109) << 1) - (i4 ^ 109);
        I = i6 % 128;
        switch (i6 % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final c D() {
        int i = G;
        int i2 = ((i | 47) << 1) - (i ^ 47);
        I = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.v;
        int i4 = (i + 58) - 1;
        I = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public final void a(c cVar) {
        int i = (I + 56) - 1;
        int i2 = i % 128;
        G = i2;
        int i3 = i % 2;
        this.v = cVar;
        int i4 = (i2 ^ Opcodes.LMUL) + ((i2 & Opcodes.LMUL) << 1);
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final HceTransactionStatus B() {
        int i = (G + 96) - 1;
        I = i % 128;
        int i2 = i % 2;
        HceTransactionStatus d = this.v.d();
        int i3 = I;
        int i4 = (i3 & Opcodes.LSHR) + (i3 | Opcodes.LSHR);
        G = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return d;
        }
    }

    public final int z() {
        int i = I;
        int i2 = (i + Opcodes.ISHL) - 1;
        G = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.v;
        switch (cVar == null ? 'R' : '5') {
            case Opcodes.SALOAD /* 53 */:
                return cVar.b();
            default:
                int i4 = (i & 63) + (i | 63);
                int i5 = i4 % 128;
                G = i5;
                if (i4 % 2 == 0) {
                }
                int i6 = i5 + 99;
                I = i6 % 128;
                switch (i6 % 2 != 0) {
                    case false:
                        return -1;
                    default:
                        throw null;
                }
        }
    }

    public final TransactionType C() {
        int i = I;
        int i2 = ((i | 19) << 1) - (i ^ 19);
        G = i2 % 128;
        switch (i2 % 2 == 0 ? 'Q' : '3') {
            case '3':
                return this.y.b();
            default:
                this.y.b();
                throw null;
        }
    }

    public final void d(d dVar) {
        int i = I;
        int i2 = ((i | 29) << 1) - (i ^ 29);
        int i3 = i2 % 128;
        G = i3;
        int i4 = i2 % 2;
        this.y = dVar;
        int i5 = (i3 ^ 89) + ((i3 & 89) << 1);
        I = i5 % 128;
        switch (i5 % 2 != 0 ? '_' : '*') {
            case '*':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final d F() {
        int i = I;
        int i2 = (i & 87) + (i | 87);
        G = i2 % 128;
        int i3 = i2 % 2;
        d dVar = this.y;
        int i4 = (i & 17) + (i | 17);
        G = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 21 : (char) 31) {
            case 21:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return dVar;
        }
    }

    public final boolean G() {
        int i = I;
        int i2 = (i & 109) + (i | 109);
        G = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.B;
        }
    }

    public final boolean I() {
        int i = G;
        int i2 = (i & 57) + (i | 57);
        I = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.A;
        int i4 = ((i | 77) << 1) - (i ^ 77);
        I = i4 % 128;
        switch (i4 % 2 != 0 ? '4' : ',') {
            case '4':
                throw null;
            default:
                return z;
        }
    }

    public final void e(boolean z) {
        int i = I;
        int i2 = (i ^ 45) + ((i & 45) << 1);
        G = i2 % 128;
        int i3 = i2 % 2;
        this.A = z;
        int i4 = i + 7;
        G = i4 % 128;
        int i5 = i4 % 2;
    }

    public final boolean E() {
        int i = G;
        int i2 = i + 97;
        I = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.D;
        int i4 = i + 1;
        I = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 15 : '.') {
            case 15:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return z;
        }
    }

    public final void b(boolean z) {
        int i = G;
        int i2 = (i ^ 23) + ((i & 23) << 1);
        I = i2 % 128;
        int i3 = i2 % 2;
        this.D = z;
        int i4 = (i ^ 89) + ((i & 89) << 1);
        I = i4 % 128;
        int i5 = i4 % 2;
    }

    public final boolean H() {
        int i = G;
        int i2 = (i & 27) + (i | 27);
        int i3 = i2 % 128;
        I = i3;
        switch (i2 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                boolean z = this.C;
                int i4 = i3 + Opcodes.LSHL;
                G = i4 % 128;
                int i5 = i4 % 2;
                return z;
        }
    }

    public final void a(boolean z) {
        int i = (G + 86) - 1;
        int i2 = i % 128;
        I = i2;
        int i3 = i % 2;
        this.C = z;
        int i4 = (i2 & 81) + (i2 | 81);
        G = i4 % 128;
        int i5 = i4 % 2;
    }

    public final boolean J() {
        int i = I;
        int i2 = (i ^ 3) + ((i & 3) << 1);
        G = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.z;
        int i4 = ((i | Opcodes.DSUB) << 1) - (i ^ Opcodes.DSUB);
        G = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    public final void d(boolean z) {
        int i = I + 61;
        G = i % 128;
        boolean z2 = i % 2 != 0;
        this.z = z;
        switch (z2) {
            case true:
                return;
            default:
                int i2 = 6 / 0;
                return;
        }
    }

    public final String K() {
        int i = G;
        int i2 = (i & 87) + (i | 87);
        I = i2 % 128;
        int i3 = i2 % 2;
        String str = this.H;
        int i4 = (i ^ 79) + ((i & 79) << 1);
        I = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final void r(String str) {
        int i = I;
        int i2 = i + 39;
        G = i2 % 128;
        char c = i2 % 2 == 0 ? ',' : Typography.dollar;
        this.H = str;
        switch (c) {
            case '$':
                break;
            default:
                int i3 = 57 / 0;
                break;
        }
        int i4 = i + Opcodes.LMUL;
        G = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final CardDisplay N() {
        int i = G + 5;
        int i2 = i % 128;
        I = i2;
        int i3 = i % 2;
        CardDisplay cardDisplay = this.E;
        int i4 = (i2 ^ 35) + ((i2 & 35) << 1);
        G = i4 % 128;
        switch (i4 % 2 == 0 ? '[' : Typography.dollar) {
            case '$':
                return cardDisplay;
            default:
                throw null;
        }
    }

    public final void c(CardDisplay cardDisplay) {
        int i = G;
        int i2 = (i & Opcodes.LSHR) + (i | Opcodes.LSHR);
        int i3 = i2 % 128;
        I = i3;
        char c = i2 % 2 != 0 ? '+' : (char) 2;
        this.E = cardDisplay;
        switch (c) {
            case '+':
                int i4 = 67 / 0;
                break;
        }
        int i5 = (i3 & 33) + (i3 | 33);
        G = i5 % 128;
        switch (i5 % 2 == 0 ? '1' : Typography.amp) {
            case '1':
                int i6 = 95 / 0;
                return;
            default:
                return;
        }
    }

    public final HceTransaction L() {
        HceTransaction hceTransaction = new HceTransaction(this);
        int i = G + 21;
        I = i % 128;
        int i2 = i % 2;
        return hceTransaction;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
