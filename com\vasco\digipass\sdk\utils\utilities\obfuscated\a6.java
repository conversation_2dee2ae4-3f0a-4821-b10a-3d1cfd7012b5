package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\a6.smali */
public abstract class a6 {
    public static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        w5.c(iArr, iArr2, iArr3);
        w5.b(iArr, 8, iArr2, 8, iArr3, 16);
        int a = w5.a(iArr3, 8, iArr3, 16);
        int a2 = a + w5.a(iArr3, 24, iArr3, 16, w5.a(iArr3, 0, iArr3, 8, 0) + a);
        int[] a3 = w5.a();
        int[] a4 = w5.a();
        boolean z = w5.a(iArr, 8, iArr, 0, a3, 0) != w5.a(iArr2, 8, iArr2, 0, a4, 0);
        int[] c = w5.c();
        w5.c(a3, a4, c);
        c6.a(32, a2 + (z ? c6.a(16, c, 0, iArr3, 8) : c6.c(16, c, 0, iArr3, 8)), iArr3, 24);
    }

    public static void a(int[] iArr, int[] iArr2) {
        w5.d(iArr, iArr2);
        w5.d(iArr, 8, iArr2, 16);
        int a = w5.a(iArr2, 8, iArr2, 16);
        int a2 = a + w5.a(iArr2, 24, iArr2, 16, w5.a(iArr2, 0, iArr2, 8, 0) + a);
        int[] a3 = w5.a();
        w5.a(iArr, 8, iArr, 0, a3, 0);
        int[] c = w5.c();
        w5.d(a3, c);
        c6.a(32, a2 + c6.c(16, c, 0, iArr2, 8), iArr2, 24);
    }
}
