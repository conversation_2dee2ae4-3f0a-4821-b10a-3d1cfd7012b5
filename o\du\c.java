package o.du;

import android.graphics.drawable.Drawable;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\c.smali */
public final class c implements d<o.dy.b, o.dx.d, o.dq.e, Drawable> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static long d;
    private final o.dx.d a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        e();
        ExpandableListView.getPackedPositionChild(0L);
        int i = b + 17;
        c = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void e() {
        d = -866585125602618690L;
    }

    static void init$0() {
        $$a = new byte[]{12, 95, -121};
        $$b = 12;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = 114 - r8
            int r7 = r7 * 2
            int r7 = r7 + 3
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r0 = o.du.c.$$a
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r4 = r8
            r3 = r2
            r7 = r6
            goto L2e
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r6) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L2e:
            int r4 = -r4
            int r6 = r6 + r4
            int r8 = r8 + 1
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.c.j(byte, short, int, java.lang.Object[]):void");
    }

    @Override // o.du.d
    public final /* synthetic */ o.dq.e a() {
        int i = c + 43;
        b = i % 128;
        switch (i % 2 == 0) {
            case false:
                o.dq.e f = f();
                int i2 = c + 83;
                b = i2 % 128;
                switch (i2 % 2 == 0 ? 'T' : Typography.dollar) {
                    case '$':
                        return f;
                    default:
                        int i3 = 82 / 0;
                        return f;
                }
            default:
                f();
                throw null;
        }
    }

    @Override // o.du.d
    public final /* synthetic */ o.dy.b b() {
        int i = b + 17;
        c = i % 128;
        int i2 = i % 2;
        o.dy.b h = h();
        int i3 = b + 81;
        c = i3 % 128;
        int i4 = i3 % 2;
        return h;
    }

    @Override // o.du.d
    public final /* synthetic */ o.dx.d d() {
        int i = b + 93;
        c = i % 128;
        int i2 = i % 2;
        o.dx.d i3 = i();
        int i4 = b + 75;
        c = i4 % 128;
        switch (i4 % 2 != 0 ? ',' : ')') {
            case ')':
                return i3;
            default:
                throw null;
        }
    }

    public c(String str) {
        this.a = new o.dx.d(str);
    }

    private static o.dy.b h() {
        int i = b + 109;
        c = i % 128;
        switch (i % 2 != 0 ? 'B' : ')') {
            case ')':
                return o.dy.b.d;
            default:
                o.dy.b bVar = o.dy.b.d;
                throw null;
        }
    }

    private o.dx.d i() {
        int i = b;
        int i2 = i + 11;
        c = i2 % 128;
        int i3 = i2 % 2;
        o.dx.d dVar = this.a;
        int i4 = i + 23;
        c = i4 % 128;
        switch (i4 % 2 != 0 ? 'X' : 'C') {
            case 'C':
                return dVar;
            default:
                throw null;
        }
    }

    private static o.dq.e f() {
        int i = c + 23;
        b = i % 128;
        int i2 = i % 2;
        o.dq.e eVar = o.dq.e.e;
        int i3 = c + 25;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return eVar;
        }
    }

    @Override // o.du.d
    public final String c() {
        int i = b + 35;
        int i2 = i % 128;
        c = i2;
        int i3 = i % 2;
        int i4 = i2 + 47;
        b = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 47 / 0;
                return null;
            default:
                return null;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        g("呭혴傪팻嶃\ud83b媸씡䞓숐䲏켓䦏\uf443盱\uf168珱ﹰ磺ﭬ旗\ue042招\ued46濒\uea79ᐩ隧ᄿ鏼", 33403 - KeyEvent.getDeadChar(0, 0), objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(h().toString());
        Object[] objArr2 = new Object[1];
        g("吂晗・썀鴾꿀穗㐴요酬ꌱ緀", (ViewConfiguration.getWindowTouchSlop() >> 8) + 12889, objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(i().toString());
        Object[] objArr3 = new Object[1];
        g("吂恛㳣좴蔎凃涵㨞\uf6e9芷弙毻⟯", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 13397, objArr3);
        String obj = append2.append(((String) objArr3[0]).intern()).append(f().toString()).append('}').toString();
        int i = b + Opcodes.LSUB;
        c = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    public final boolean equals(Object obj) {
        switch (this == obj ? (char) 7 : Typography.greater) {
            case '>':
                switch (obj != null) {
                    case true:
                        int i = c + 37;
                        b = i % 128;
                        int i2 = i % 2;
                        if (getClass() == obj.getClass()) {
                            boolean equals = Objects.equals(this.a, ((c) obj).a);
                            int i3 = b + 43;
                            c = i3 % 128;
                            int i4 = i3 % 2;
                            return equals;
                        }
                    default:
                        return false;
                }
            default:
                int i5 = b;
                int i6 = i5 + 33;
                c = i6 % 128;
                if (i6 % 2 != 0) {
                }
                int i7 = i5 + 81;
                c = i7 % 128;
                if (i7 % 2 == 0) {
                    return true;
                }
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:66:0x0022. Please report as an issue. */
    private static void g(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 490
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.c.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
