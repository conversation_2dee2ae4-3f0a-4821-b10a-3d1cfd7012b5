package o.i;

import android.content.Context;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.HashMap;
import java.util.Set;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\g.smali */
public abstract class g implements o.ee.d<CustomerAuthenticationMethod> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int[] d;
    private static boolean f;
    private static int g;
    private static char[] h;
    private static boolean i;
    private static int j;
    private static int k;
    private final HashMap<i, o.q.c> a = new HashMap<>();
    private f b;
    private Integer c;
    private c e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        k = 1;
        m();
        View.resolveSize(0, 0);
        ViewConfiguration.getFadingEdgeLength();
        PointF.length(0.0f, 0.0f);
        int i2 = j + Opcodes.DREM;
        k = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{0, ByteCompanionObject.MIN_VALUE, -60, 102, -85};
        $$e = 53;
    }

    static void m() {
        d = new int[]{-1838489963, -1741223417, -384691056, 1388480539, 151966458, 1648265481, -557034737, -1984655465, -244113396, -669686480, -1323689038, 167282368, -344507289, -333614169, 2068260121, -1835580416, 567952978, -268510738};
        h = new char[]{61710, 61745, 61738, 61749, 61700, 61754, 61748, 61755, 61744, 61746, 61702, 61743, 61742, 61732, 61734, 61714, 61739, 61753, 61747, 61722, 61736, 61927, 61938, 61737, 61733, 61752, 61758, 61949, 61930, 61751, 61740, 61712, 61717};
        f = true;
        i = true;
        g = 782102983;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void v(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = r6 + 5
            byte[] r0 = o.i.g.$$d
            int r8 = r8 + 115
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = -r7
            int r6 = r6 + 1
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.v(short, short, short, java.lang.Object[]):void");
    }

    protected abstract o.o.c e(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt) throws WalletValidationException;

    @Override // o.ee.d
    public final /* synthetic */ CustomerAuthenticationMethod a() {
        int i2 = k + 47;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                b();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                CustomerAuthenticationMethod b = b();
                int i3 = k + 33;
                j = i3 % 128;
                int i4 = i3 % 2;
                return b;
        }
    }

    public g(f fVar) {
        this.b = fVar;
    }

    public final f i() {
        int i2 = k + 7;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '+' : 'Y') {
            case Opcodes.DUP /* 89 */:
                return this.b;
            default:
                throw null;
        }
    }

    public final Set<i> h() {
        Set<i> keySet;
        int i2 = k + 21;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? 'Q' : 'G') {
            case Opcodes.FASTORE /* 81 */:
                keySet = this.a.keySet();
                int i3 = 4 / 0;
                break;
            default:
                keySet = this.a.keySet();
                break;
        }
        int i4 = k + 55;
        j = i4 % 128;
        int i5 = i4 % 2;
        return keySet;
    }

    public final boolean e(i iVar) {
        int i2 = k + 83;
        j = i2 % 128;
        int i3 = i2 % 2;
        boolean contains = this.a.keySet().contains(iVar);
        int i4 = k + 53;
        j = i4 % 128;
        int i5 = i4 % 2;
        return contains;
    }

    public final void d(i iVar, o.q.c cVar) {
        int i2 = j + 49;
        k = i2 % 128;
        int i3 = i2 % 2;
        this.a.put(iVar, cVar);
        int i4 = k + 31;
        j = i4 % 128;
        switch (i4 % 2 != 0 ? 'X' : (char) 16) {
            case 16:
                return;
            default:
                int i5 = 43 / 0;
                return;
        }
    }

    private o.q.c a(i iVar) {
        int i2 = k + Opcodes.LNEG;
        j = i2 % 128;
        int i3 = i2 % 2;
        o.q.c cVar = this.a.get(iVar);
        int i4 = k + Opcodes.DMUL;
        j = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public final o.q.d f() {
        int i2 = k + 99;
        j = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? (char) 24 : '3') {
            case 24:
                throw null;
            default:
                o.q.d dVar = (o.q.d) a(i.e);
                int i3 = k + 85;
                j = i3 % 128;
                switch (i3 % 2 != 0 ? '\f' : 'L') {
                    case Base64.mimeLineLength /* 76 */:
                        return dVar;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final c j() {
        int i2 = k + 67;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return this.e;
            default:
                int i3 = 98 / 0;
                return this.e;
        }
    }

    public final void d(c cVar) {
        int i2 = j + 109;
        k = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.e = cVar;
        switch (z) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final Integer g() {
        int i2 = k;
        int i3 = i2 + 31;
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Integer num = this.c;
                int i4 = i2 + 59;
                j = i4 % 128;
                int i5 = i4 % 2;
                return num;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public void c(int i2) {
        int i3 = j + 37;
        k = i3 % 128;
        int i4 = i3 % 2;
        this.c = Integer.valueOf(i2);
        int i5 = k + 35;
        j = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return;
            default:
                int i6 = 98 / 0;
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    void e(android.content.Context r7, o.i.g r8) {
        /*
            r6 = this;
            int r7 = o.i.g.j
            r0 = 49
            int r7 = r7 + r0
            int r1 = r7 % 128
            o.i.g.k = r1
            int r7 = r7 % 2
            java.lang.Integer r7 = r8.c
            r6.c = r7
            java.util.HashMap<o.i.i, o.q.c> r7 = r8.a
            java.util.Set r7 = r7.entrySet()
            java.util.Iterator r7 = r7.iterator()
            int r1 = o.i.g.k
            int r1 = r1 + 91
            int r2 = r1 % 128
            o.i.g.j = r2
            int r1 = r1 % 2
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L29
            r1 = r2
            goto L2a
        L29:
            r1 = r3
        L2a:
            switch(r1) {
                case 0: goto L2d;
                default: goto L2d;
            }
        L2d:
            boolean r1 = r7.hasNext()
            r4 = 12
            if (r1 == 0) goto L37
            r1 = r4
            goto L39
        L37:
            r1 = 90
        L39:
            switch(r1) {
                case 12: goto L47;
                default: goto L3c;
            }
        L3c:
            java.util.HashMap<o.i.i, o.q.c> r7 = r6.a
            java.util.Set r7 = r7.entrySet()
            java.util.Iterator r7 = r7.iterator()
            goto L7d
        L47:
            java.lang.Object r1 = r7.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.util.HashMap<o.i.i, o.q.c> r4 = r6.a
            java.lang.Object r5 = r1.getKey()
            java.lang.Object r4 = r4.get(r5)
            o.q.c r4 = (o.q.c) r4
            if (r4 != 0) goto L5d
            r5 = r3
            goto L5e
        L5d:
            r5 = r2
        L5e:
            switch(r5) {
                case 0: goto L6b;
                default: goto L61;
            }
        L61:
            java.lang.Object r1 = r1.getValue()
            o.q.c r1 = (o.q.c) r1
            r4.d(r1)
            goto L2d
        L6b:
            java.util.HashMap<o.i.i, o.q.c> r4 = r6.a
            java.lang.Object r5 = r1.getKey()
            o.i.i r5 = (o.i.i) r5
            java.lang.Object r1 = r1.getValue()
            o.q.c r1 = (o.q.c) r1
            r4.put(r5, r1)
            goto L2d
        L7d:
            boolean r1 = r7.hasNext()
            if (r1 == 0) goto L86
            r1 = r2
            goto L87
        L86:
            r1 = r3
        L87:
            switch(r1) {
                case 0: goto L9f;
                default: goto L8a;
            }
        L8a:
            java.lang.Object r1 = r7.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r1 = r1.getKey()
            o.i.i r1 = (o.i.i) r1
            java.util.HashMap<o.i.i, o.q.c> r5 = r8.a
            boolean r1 = r5.containsKey(r1)
            if (r1 != 0) goto La2
            goto La0
        L9f:
            return
        La0:
            r1 = r4
            goto La3
        La2:
            r1 = r0
        La3:
            switch(r1) {
                case 49: goto L7d;
                default: goto La6;
            }
        La6:
            int r1 = o.i.g.j
            int r1 = r1 + 29
            int r5 = r1 % 128
            o.i.g.k = r5
            int r1 = r1 % 2
            r7.remove()
            int r1 = o.i.g.k
            int r1 = r1 + 107
            int r5 = r1 % 128
            o.i.g.j = r5
            int r1 = r1 % 2
            goto L7d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.e(android.content.Context, o.i.g):void");
    }

    public final o.eg.b k() throws o.eg.d {
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        s(new int[]{1979418220, 921262724}, 4 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr);
        bVar.d(((String) objArr[0]).intern(), o());
        Object[] objArr2 = new Object[1];
        s(new int[]{-926631183, -205852229, 1231498, -994159720, -1615415024, -316113416}, TextUtils.indexOf((CharSequence) "", '0', 0) + 10, objArr2);
        bVar.d(((String) objArr2[0]).intern(), this.e.equals(c.c));
        int i2 = j + 85;
        k = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:31:0x00ef, code lost:
    
        if ((r8 instanceof o.q.a) != false) goto L37;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public o.f.c e(o.f.e r7, o.i.i r8, boolean r9) {
        /*
            Method dump skipped, instructions count: 688
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.e(o.f.e, o.i.i, boolean):o.f.c");
    }

    public void d(Context context, o.f.e eVar, o.g.a aVar, b bVar, boolean z) {
        int i2 = k + 87;
        j = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, TextUtils.indexOf("", "") + 127, null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(null, Process.getGidForName("") + 128, null, "\u0096\u0087\u009d\u0096\u009c\u0096\u0091\u0089\u008c\u0088\u0083\u008a\u0096\u0097\u0096\u0087\u0093\u008f\u008d\u0088\u0082\u0083\u0091\u0083\u0084\u0085\u0083\u0088\u008f\u0091\u008d\u0093\u008f\u0092", objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i()));
        aVar.d(eVar);
        int i4 = k + 77;
        j = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:23:0x0068, code lost:
    
        if (r13.a(r10) != false) goto L28;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:41:0x008a. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void c(final android.content.Context r9, fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt r10, final fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptCallback r11, fr.antelop.sdk.authentication.CustomerAuthenticationMethodType r12, fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt r13, final android.os.CancellationSignal r14) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            Method dump skipped, instructions count: 668
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.c(android.content.Context, fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt, fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptCallback, fr.antelop.sdk.authentication.CustomerAuthenticationMethodType, fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt, android.os.CancellationSignal):void");
    }

    /* JADX WARN: Removed duplicated region for block: B:30:0x0097 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 0
            r1 = 1
            if (r4 != r5) goto L7
            r2 = r0
            goto L8
        L7:
            r2 = r1
        L8:
            switch(r2) {
                case 1: goto Ld;
                default: goto Lb;
            }
        Lb:
            goto La4
        Ld:
            if (r5 == 0) goto L11
            r2 = r0
            goto L12
        L11:
            r2 = r1
        L12:
            switch(r2) {
                case 1: goto L98;
                default: goto L15;
            }
        L15:
            int r2 = o.i.g.j
            int r2 = r2 + 89
            int r3 = r2 % 128
            o.i.g.k = r3
            int r2 = r2 % 2
            java.lang.Class r2 = r4.getClass()
            java.lang.Class r3 = r5.getClass()
            if (r2 == r3) goto L2b
            r2 = r1
            goto L2c
        L2b:
            r2 = r0
        L2c:
            switch(r2) {
                case 0: goto L30;
                default: goto L2f;
            }
        L2f:
            goto L98
        L30:
            o.i.g r5 = (o.i.g) r5
            o.i.f r2 = r4.b
            o.i.f r3 = r5.b
            if (r2 != r3) goto L3b
            r2 = 64
            goto L3d
        L3b:
            r2 = 50
        L3d:
            switch(r2) {
                case 50: goto L97;
                default: goto L40;
            }
        L40:
            o.i.c r2 = r4.e
            o.i.c r3 = r5.e
            if (r2 != r3) goto L97
            o.i.i r2 = o.i.i.e
            o.q.c r2 = r4.a(r2)
            o.i.i r3 = o.i.i.e
            o.q.c r3 = r5.a(r3)
            boolean r2 = java.util.Objects.equals(r2, r3)
            if (r2 == 0) goto L97
            int r2 = o.i.g.k
            int r2 = r2 + r1
            int r3 = r2 % 128
            o.i.g.j = r3
            int r2 = r2 % 2
            o.i.i r2 = o.i.i.a
            o.q.c r2 = r4.a(r2)
            o.i.i r3 = o.i.i.a
            o.q.c r3 = r5.a(r3)
            boolean r2 = java.util.Objects.equals(r2, r3)
            if (r2 == 0) goto L75
            r2 = r0
            goto L76
        L75:
            r2 = r1
        L76:
            switch(r2) {
                case 0: goto L7a;
                default: goto L79;
            }
        L79:
            goto L97
        L7a:
            o.i.i r2 = o.i.i.d
            o.q.c r2 = r4.a(r2)
            o.i.i r3 = o.i.i.d
            o.q.c r5 = r5.a(r3)
            boolean r5 = java.util.Objects.equals(r2, r5)
            if (r5 == 0) goto L97
            int r5 = o.i.g.k
            int r5 = r5 + 73
            int r0 = r5 % 128
            o.i.g.j = r0
            int r5 = r5 % 2
            return r1
        L97:
            return r0
        L98:
            int r5 = o.i.g.j
            int r5 = r5 + 27
            int r1 = r5 % 128
            o.i.g.k = r1
            int r5 = r5 % 2
            return r0
        La4:
            int r5 = o.i.g.k
            int r5 = r5 + 41
            int r0 = r5 % 128
            o.i.g.j = r0
            int r5 = r5 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.equals(java.lang.Object):boolean");
    }

    final String o() {
        int i2 = j + 87;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                String obj = i().toString();
                int i3 = j + 109;
                k = i3 % 128;
                int i4 = i3 % 2;
                return obj;
            default:
                i().toString();
                throw null;
        }
    }

    public void c(Context context, boolean z, o.eg.b bVar) {
        switch (z ? '0' : 'E') {
            case 'E':
                d(c.a);
                break;
            default:
                int i2 = k + 17;
                j = i2 % 128;
                if (i2 % 2 != 0) {
                }
                d(c.c);
                int i3 = k + 99;
                j = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, (ViewConfiguration.getLongPressTimeout() >> 16) + 127, null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(null, View.resolveSizeAndState(0, 0, 0) + 127, null, "\u0087\u009d\u0096\u009c\u0096\u0087\u0086\u0088\u008f\u0088\u0087\u0096\u0097\u0096\u0087\u009d\u0096\u009c\u0096\u0091\u0089\u008c\u0088\u0083\u008a\u0096\u0097\u0096\u0088\u008d\u0082\u0081\u0082 \u008c\u0087\u0083\u0084\u0098\u0083\u0084", objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        switch (bVar != null ? (char) 15 : 'C') {
            case 15:
                for (o.q.c cVar : this.a.values()) {
                    cVar.d(bVar.u(cVar.b()));
                    int i5 = j + 99;
                    k = i5 % 128;
                    int i6 = i5 % 2;
                }
                break;
        }
    }

    boolean e(Context context, boolean z, o.bb.d dVar) {
        int i2 = k + 61;
        j = i2 % 128;
        int i3 = i2 % 2;
        switch (z) {
            case true:
                d(c.c);
                break;
            default:
                d(c.a);
                int i4 = k + 49;
                j = i4 % 128;
                if (i4 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, 127 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        s(new int[]{1413130801, 1559917225, 461258441, 1366175353, -36725924, 15948381, -1921586477, 1824673782, -2123674515, 696831954, 167291404, 765999945, -1143004101, -1504712305, 1418731079, -1287447448, -848501678, -1117531562, -1814207915, -788415770, -1370676243, 1272184503, -987506781, 1184668040}, 47 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        int i5 = j + 51;
        k = i5 % 128;
        int i6 = i5 % 2;
        return false;
    }

    void a(Context context, boolean z) {
        int i2 = j + 91;
        k = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, 127 - TextUtils.getOffsetBefore("", 0), null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(null, 127 - View.MeasureSpec.getSize(0), null, "\u0087\u009d\u0096\u009c\u0096\u0087\u0086\u0088\u008f\u0088\u0087\u0096\u0097\u0096\u0087\u009d\u0096\u009c\u0096\u008a\u0092\u008e\u0096\u0097\u0096\u0083\u008a\u0086\u0087\u0083¡\u0082 \u008c\u0087\u0083\u0084\u0098\u0083\u0084", objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        int i4 = k + 23;
        j = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    void c(Context context) {
        int i2 = j + 73;
        k = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u(null, 127 - ((Process.getThreadPriority(0) + 20) >> 6), null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(null, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0087\u009d\u0096\u009c\u0096\u0087\u0086\u0088\u008f\u0088\u0087\u0096\u0097\u0096\u0087\u009d\u0096\u009c\u0096\u0091\u0089\u008c\u0088\u0083\u008a\u0096\u0097\u0096\u0082\u0089\u008d\u0088\u008f\u0088\u0086\u009e\u008a\u0089\u0085\u0089\u0088\u009e\u009b\u0084\u0085\u0082\u0089\u009e\u0094\u0087\u0091\u0089\u008c\u0088\u0083\u0090\u008c\u0087\u0083\u0084\u0098\u0083\u0084", objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        int i4 = k + 13;
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:13:0x0024, code lost:
    
        o.ee.g.c();
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x004c, code lost:
    
        r6 = new java.lang.Object[1];
        u(null, (android.view.ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081", r6);
        r5 = ((java.lang.String) r6[0]).intern();
        r9 = new java.lang.Object[1];
        u(null, ((byte) android.view.KeyEvent.getModifierMetaStateMask()) + kotlin.jvm.internal.ByteCompanionObject.MIN_VALUE, null, "\u0087\u009d\u0096\u009c\u0096\u0082\u0089\u008d\u0088\u008f\u0084\u0086\u0095\u008d\u0098\u0082\u0089\u008e\u0096\u0091\u0083\u0088\u0082\u008d\u0084\u009e\u0096\u0097\u0096\u0087\u009d\u0096\u009c\u0096\u0091\u0089\u008c\u0088\u0083\u008a\u0096\u0097\u0096\u0082\u0089\u008d\u0088\u008f\u0084\u0086\u0095\u008d\u0098\u0082\u0089\u0085\u0088\u0082\u008d\u0084\u009e", r9);
        o.ee.g.d(r5, java.lang.String.format(((java.lang.String) r9[0]).intern(), i(), r4));
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0088, code lost:
    
        return r4;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected o.eg.b a_() {
        /*
            r10 = this;
            java.lang.String r0 = "\u0091\u0089\u008c\u0088\u0083\u0090\u0082\u0089\u008d\u0088\u008f\u008e\u008d\u0088\u0082\u0083\u008c\u0088\u0086\u008b\u0084\u0083\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0082\u0081"
            r1 = 1
            r2 = 0
            r3 = 0
            o.eg.b r4 = new o.eg.b     // Catch: o.eg.d -> L89
            r4.<init>()     // Catch: o.eg.d -> L89
            java.util.HashMap<o.i.i, o.q.c> r5 = r10.a     // Catch: o.eg.d -> L89
            java.util.Collection r5 = r5.values()     // Catch: o.eg.d -> L89
            java.util.Iterator r5 = r5.iterator()     // Catch: o.eg.d -> L89
        L15:
            boolean r6 = r5.hasNext()     // Catch: o.eg.d -> L89
            if (r6 == 0) goto L1e
            r6 = 24
            goto L20
        L1e:
            r6 = 13
        L20:
            r7 = 2
            switch(r6) {
                case 24: goto L28;
                default: goto L24;
            }     // Catch: o.eg.d -> L89
        L24:
            o.ee.g.c()     // Catch: o.eg.d -> L89
            goto L4c
        L28:
            int r6 = o.i.g.k
            int r6 = r6 + 19
            int r8 = r6 % 128
            o.i.g.j = r8
            int r6 = r6 % r7
            java.lang.Object r6 = r5.next()     // Catch: o.eg.d -> L89
            o.q.c r6 = (o.q.c) r6     // Catch: o.eg.d -> L89
            java.lang.String r8 = r6.b()     // Catch: o.eg.d -> L89
            o.eg.b r6 = r6.e()     // Catch: o.eg.d -> L89
            r4.d(r8, r6)     // Catch: o.eg.d -> L89
            int r6 = o.i.g.k
            int r6 = r6 + 99
            int r8 = r6 % 128
            o.i.g.j = r8
            int r6 = r6 % r7
            goto L15
        L4c:
            int r5 = android.view.ViewConfiguration.getPressedStateDuration()     // Catch: o.eg.d -> L89
            int r5 = r5 >> 16
            int r5 = r5 + 127
            java.lang.Object[] r6 = new java.lang.Object[r1]     // Catch: o.eg.d -> L89
            u(r3, r5, r3, r0, r6)     // Catch: o.eg.d -> L89
            r5 = r6[r2]     // Catch: o.eg.d -> L89
            java.lang.String r5 = (java.lang.String) r5     // Catch: o.eg.d -> L89
            java.lang.String r5 = r5.intern()     // Catch: o.eg.d -> L89
            int r6 = android.view.KeyEvent.getModifierMetaStateMask()     // Catch: o.eg.d -> L89
            byte r6 = (byte) r6     // Catch: o.eg.d -> L89
            int r6 = r6 + 128
            java.lang.String r8 = "\u0087\u009d\u0096\u009c\u0096\u0082\u0089\u008d\u0088\u008f\u0084\u0086\u0095\u008d\u0098\u0082\u0089\u008e\u0096\u0091\u0083\u0088\u0082\u008d\u0084\u009e\u0096\u0097\u0096\u0087\u009d\u0096\u009c\u0096\u0091\u0089\u008c\u0088\u0083\u008a\u0096\u0097\u0096\u0082\u0089\u008d\u0088\u008f\u0084\u0086\u0095\u008d\u0098\u0082\u0089\u0085\u0088\u0082\u008d\u0084\u009e"
            java.lang.Object[] r9 = new java.lang.Object[r1]     // Catch: o.eg.d -> L89
            u(r3, r6, r3, r8, r9)     // Catch: o.eg.d -> L89
            r6 = r9[r2]     // Catch: o.eg.d -> L89
            java.lang.String r6 = (java.lang.String) r6     // Catch: o.eg.d -> L89
            java.lang.String r6 = r6.intern()     // Catch: o.eg.d -> L89
            java.lang.Object[] r7 = new java.lang.Object[r7]     // Catch: o.eg.d -> L89
            o.i.f r8 = r10.i()     // Catch: o.eg.d -> L89
            r7[r2] = r8     // Catch: o.eg.d -> L89
            r7[r1] = r4     // Catch: o.eg.d -> L89
            java.lang.String r6 = java.lang.String.format(r6, r7)     // Catch: o.eg.d -> L89
            o.ee.g.d(r5, r6)     // Catch: o.eg.d -> L89
            return r4
        L89:
            r4 = move-exception
            o.ee.g.c()
            int r5 = android.view.View.getDefaultSize(r2, r2)
            int r5 = 127 - r5
            java.lang.Object[] r6 = new java.lang.Object[r1]
            u(r3, r5, r3, r0, r6)
            r0 = r6[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            float r5 = android.view.ViewConfiguration.getScrollFriction()
            r6 = 0
            int r5 = (r5 > r6 ? 1 : (r5 == r6 ? 0 : -1))
            int r5 = r5 + 126
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r6 = "\u0082\u0089\u008d\u0088\u008f\u0084\u0086\u0095\u008d\u0098\u0082\u0089\u0085\u0088\u0082\u008d\u0084\u009e"
            u(r3, r5, r3, r6, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.a(r0, r1, r4)
            o.eg.b r0 = new o.eg.b
            r0.<init>()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.a_():o.eg.b");
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    private CustomerAuthenticationMethod b() {
        CustomerAuthenticationMethod customerAuthenticationMethod = new CustomerAuthenticationMethod(this);
        int i2 = j + 71;
        k = i2 % 128;
        int i3 = i2 % 2;
        return customerAuthenticationMethod;
    }

    public final int hashCode() {
        int i2 = j + 23;
        k = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = k + Opcodes.LNEG;
        j = i4 % 128;
        switch (i4 % 2 != 0 ? 'F' : ')') {
            case ')':
                return hashCode;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    protected final void finalize() throws Throwable {
        int i2 = j + 51;
        k = i2 % 128;
        int i3 = i2 % 2;
        super.finalize();
        int i4 = j + 99;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 21 : (char) 18) {
            case 18:
                return;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void s(int[] r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 978
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.s(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v22, types: [byte[]] */
    private static void u(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 748
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.g.u(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
