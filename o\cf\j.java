package o.cf;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\j.smali */
public final class j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] b;
    private static int g;
    private static char h;
    private static int i;
    private static int j;
    private final String a;
    private final String c;
    private final List<o.f.e> d = new ArrayList();
    private final boolean e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        j = 1;
        a();
        TextUtils.getTrimmedLength("");
        Process.getThreadPriority(0);
        int i2 = j + 57;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void a() {
        b = new char[]{30574, 29846, 30568, 30560, 30567, 30586, 30540, 30571, 30591, 30570, 29853, 29847, 30566, 30587, 29844, 30529, 30539, 30498, 30582, 30589, 30553, 30559, 30511, 30563, 30517, 30561, 29841, 30554, 30562, 30572, 30588, 29842, 29843, 30569, 29845, 29840};
        h = (char) 17043;
        g = 874635385;
    }

    static void init$0() {
        $$a = new byte[]{75, 105, 70, 99};
        $$b = Opcodes.IFNULL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 3
            int r9 = r9 + 1
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r0 = o.cf.j.$$a
            int r8 = r8 + 69
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L18:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r6
        L1c:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            int r8 = r8 + 1
            if (r4 != r9) goto L2d
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2d:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L35:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.l(byte, short, short, java.lang.Object[]):void");
    }

    public j(String str, boolean z, o.h.d dVar) {
        this.e = z;
        this.c = str;
        this.a = dVar.d();
        for (o.f.e eVar : dVar.b()) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f(Color.alpha(0) + 15, "\u0018!\u0007\u0015\u0007\u0012\n\b\u0007\u001b\u000e\r\u0005\u0012㘡", (byte) (TextUtils.lastIndexOf("", '0', 0) + 57), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            f(53 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "!\u0006\u000f\u0019!\u0006\u0012\u0007\u0015\u0007\b\n\u001f\u0013\u0012\u0006\u0012#\u0015\t\b\n\u0017\u0010\u0017\u0012\u0004\u0001\u0006\r\u001a\u0001\u0017\u001c\u0015\u0007\b\n\u001f\u0013\u0012\u0006\u0012#\u0015\"\u0015\u0001\"\u001c\u001c\u0012", (byte) (Process.getGidForName("") + 88), objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(eVar.b()).toString());
            a(eVar);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.List<o.i.f> d() {
        /*
            r5 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.List<o.f.e> r1 = r5.d
            java.util.Iterator r1 = r1.iterator()
        Lc:
            boolean r2 = r1.hasNext()
            r3 = 0
            if (r2 == 0) goto L15
            r2 = 1
            goto L16
        L15:
            r2 = r3
        L16:
            switch(r2) {
                case 0: goto L26;
                default: goto L19;
            }
        L19:
            int r2 = o.cf.j.i
            int r2 = r2 + 77
            int r4 = r2 % 128
            o.cf.j.j = r4
            int r2 = r2 % 2
            if (r2 != 0) goto L2a
            goto L27
        L26:
            return r0
        L27:
            r2 = 12
            goto L2c
        L2a:
            r2 = 84
        L2c:
            switch(r2) {
                case 84: goto L3d;
                default: goto L2f;
            }
        L2f:
            java.lang.Object r2 = r1.next()
            o.f.e r2 = (o.f.e) r2
            o.i.f r2 = r2.b()
            r0.add(r2)
            goto L4b
        L3d:
            java.lang.Object r2 = r1.next()
            o.f.e r2 = (o.f.e) r2
            o.i.f r2 = r2.b()
            r0.add(r2)
            goto L4e
        L4b:
            r2 = 49
            int r2 = r2 / r3
        L4e:
            goto Lc
        L4f:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.d():java.util.List");
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0029, code lost:
    
        r4.d.add(r5);
        r5 = o.cf.j.j + 61;
        o.cf.j.i = r5 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0038, code lost:
    
        if ((r5 % 2) == 0) goto L17;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x003a, code lost:
    
        r5 = 30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x003f, code lost:
    
        switch(r5) {
            case 82: goto L20;
            default: goto L27;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0043, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0044, code lost:
    
        r2 = 98 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0045, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x003d, code lost:
    
        r5 = 'R';
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0026, code lost:
    
        if (r4.d.size() != 10) goto L14;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001b, code lost:
    
        if (r4.d.size() != 98) goto L14;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void a(o.f.e r5) {
        /*
            r4 = this;
            int r0 = o.cf.j.i
            int r0 = r0 + 71
            int r1 = r0 % 128
            o.cf.j.j = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 != 0) goto Lf
            r0 = r1
            goto L10
        Lf:
            r0 = 1
        L10:
            r2 = 98
            switch(r0) {
                case 1: goto L1e;
                default: goto L15;
            }
        L15:
            java.util.List<o.f.e> r0 = r4.d
            int r0 = r0.size()
            if (r0 == r2) goto L48
        L1d:
            goto L29
        L1e:
            java.util.List<o.f.e> r0 = r4.d
            int r0 = r0.size()
            r3 = 10
            if (r0 == r3) goto L48
            goto L1d
        L29:
            java.util.List<o.f.e> r0 = r4.d
            r0.add(r5)
            int r5 = o.cf.j.j
            int r5 = r5 + 61
            int r0 = r5 % 128
            o.cf.j.i = r0
            int r5 = r5 % 2
            if (r5 == 0) goto L3d
            r5 = 30
            goto L3f
        L3d:
            r5 = 82
        L3f:
            switch(r5) {
                case 82: goto L43;
                default: goto L42;
            }
        L42:
            goto L44
        L43:
            return
        L44:
            int r2 = r2 / r1
            return
        L46:
            r5 = move-exception
            throw r5
        L48:
            java.lang.ArrayIndexOutOfBoundsException r5 = new java.lang.ArrayIndexOutOfBoundsException
            r5.<init>()
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.a(o.f.e):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:15:0x016d, code lost:
    
        if (r9.b().equals(o.i.f.b) != false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.cf.j.e d(int r29) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 772
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.d(int):o.cf.j$e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void e() {
        /*
            r5 = this;
            int r0 = o.cf.j.j
            int r0 = r0 + 15
            int r1 = r0 % 128
            o.cf.j.i = r1
            int r0 = r0 % 2
            java.util.List<o.f.e> r0 = r5.d
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.cf.j.i
            int r1 = r1 + 55
            int r2 = r1 % 128
            o.cf.j.j = r2
            int r1 = r1 % 2
            r2 = 1
            r3 = 0
            if (r1 != 0) goto L20
            r1 = r2
            goto L21
        L20:
            r1 = r3
        L21:
            switch(r1) {
                case 1: goto L24;
                default: goto L24;
            }
        L24:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L2c
            r1 = r2
            goto L2d
        L2c:
            r1 = r3
        L2d:
            switch(r1) {
                case 0: goto L3d;
                default: goto L30;
            }
        L30:
            int r1 = o.cf.j.i
            int r1 = r1 + 103
            int r4 = r1 % 128
            o.cf.j.j = r4
            int r1 = r1 % 2
            if (r1 != 0) goto L3e
            goto L3e
        L3d:
            return
        L3e:
            java.lang.Object r1 = r0.next()
            o.f.e r1 = (o.f.e) r1
            byte[] r1 = r1.a()
            if (r1 == 0) goto L4c
            r4 = 5
            goto L4e
        L4c:
            r4 = 41
        L4e:
            switch(r4) {
                case 41: goto L24;
                default: goto L51;
            }
        L51:
            java.util.Arrays.fill(r1, r3)
            goto L24
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.e():void");
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\j$e.smali */
    static final class e {
        private static int a = 0;
        private static int d = 1;
        private final o.eg.b b;
        private final byte[][] e;

        e(o.eg.b bVar, byte[][] bArr) {
            this.b = bVar;
            this.e = bArr;
        }

        public final o.eg.b b() {
            int i = (d + 64) - 1;
            int i2 = i % 128;
            a = i2;
            int i3 = i % 2;
            o.eg.b bVar = this.b;
            int i4 = (i2 & 39) + (i2 | 39);
            d = i4 % 128;
            int i5 = i4 % 2;
            return bVar;
        }

        public final byte[][] e() {
            int i = a;
            int i2 = (i & 19) + (i | 19);
            int i3 = i2 % 128;
            d = i3;
            int i4 = i2 % 2;
            byte[][] bArr = this.e;
            int i5 = (i3 & 35) + (i3 | 35);
            a = i5 % 128;
            int i6 = i5 % 2;
            return bArr;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:57:0x01ac  */
    /* JADX WARN: Removed duplicated region for block: B:60:0x01c2  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1076
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.j.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }

    private static void k(int i2, String str, int i3, int i4, boolean z, Object[] objArr) {
        char[] charArray;
        switch (str != null ? ')' : (char) 25) {
            case ')':
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr = charArray;
        o.a.h hVar = new o.a.h();
        char[] cArr2 = new char[i3];
        hVar.a = 0;
        while (hVar.a < i3) {
            hVar.b = cArr[hVar.a];
            cArr2[hVar.a] = (char) (i4 + hVar.b);
            int i5 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr2[i5]), Integer.valueOf(g)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c((ViewConfiguration.getTouchSlop() >> 8) + 12, (char) (Process.myPid() >> 22), View.combineMeasuredStates(0, 0) + 459);
                    byte b2 = (byte) 0;
                    Object[] objArr3 = new Object[1];
                    l(b2, (byte) (b2 | 38), b2, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr2[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(12 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), (char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), 313 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
                        byte b3 = (byte) 0;
                        Object[] objArr5 = new Object[1];
                        l(b3, (byte) (b3 | 40), b3, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i2 > 0) {
            int i6 = $11 + 85;
            $10 = i6 % 128;
            int i7 = i6 % 2;
            hVar.c = i2;
            char[] cArr3 = new char[i3];
            System.arraycopy(cArr2, 0, cArr3, 0, i3);
            System.arraycopy(cArr3, 0, cArr2, i3 - hVar.c, hVar.c);
            System.arraycopy(cArr3, hVar.c, cArr2, 0, i3 - hVar.c);
            int i8 = $10 + 79;
            $11 = i8 % 128;
            int i9 = i8 % 2;
        }
        if (z) {
            int i10 = $10 + Opcodes.LSHR;
            $11 = i10 % 128;
            int i11 = i10 % 2;
            char[] cArr4 = new char[i3];
            hVar.a = 0;
            while (true) {
                switch (hVar.a < i3) {
                    case true:
                        cArr4[hVar.a] = cArr2[(i3 - hVar.a) - 1];
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj3 = o.e.a.s.get(-1412673904);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(10 - ExpandableListView.getPackedPositionChild(0L), (char) (ViewConfiguration.getLongPressTimeout() >> 16), (Process.myTid() >> 22) + 313);
                                byte b4 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                l(b4, (byte) (b4 | 40), b4, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    default:
                        int i12 = $10 + 91;
                        $11 = i12 % 128;
                        int i13 = i12 % 2;
                        cArr2 = cArr4;
                        break;
                }
            }
        }
        String str2 = new String(cArr2);
        int i14 = $11 + 13;
        $10 = i14 % 128;
        int i15 = i14 % 2;
        objArr[0] = str2;
    }
}
