package o.fn;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodUsage;
import fr.antelop.sdk.settings.TransactionStartCondition;
import fr.antelop.sdk.settings.WalletSettingsRights;
import fr.antelop.sdk.settings.WalletSettingsValue;
import java.util.Iterator;
import java.util.Map;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.ds.f;
import o.ee.g;
import o.ee.o;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fn\h.smali */
public final class h extends c<h> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static long h;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static int f87o;
    private boolean a;
    private boolean b;
    private final o.i.d c;
    private boolean d;
    private boolean e;
    private int g;
    private f i;
    private o.ds.c j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f87o = 0;
        n = 1;
        h();
        ViewConfiguration.getScrollBarSize();
        ViewConfiguration.getMaximumFlingVelocity();
        View.getDefaultSize(0, 0);
        SystemClock.elapsedRealtime();
        Process.myTid();
        PointF.length(0.0f, 0.0f);
        TextUtils.lastIndexOf("", '0', 0, 0);
        KeyEvent.normalizeMetaState(0);
        PointF.length(0.0f, 0.0f);
        int i = n + 43;
        f87o = i % 128;
        int i2 = i % 2;
    }

    static void h() {
        f = new char[]{1065, 36594, 4598, 42225, 12258, 45791, 17901, 51431, 21493, 59086, 27089, 64704, 34761, 59189, 28099, 62174, 18388, 52427, 20980, 42718, 11214, 45248, 1531, 35556, 8181, 25830, 59884, 59409, 25319, 65018, 18672, 50159, 24280, 43497, 9470, 49108, 2756, 34241, 4293, 27598, 59100, 1198, 36452, 4454, 42108, 12159, 45688, 17761, 51296, 21309, 58952, 26966, 64521, 34627, 2654, 40257, Typography.rightSingleQuote, 43860, 15919, 49452, 21565, 57124, 25136, 62777, 30768, 807, 38404, 6401, 11432, 42567, 14676, 35952, 1884, 39510, 27986, 57420, 31566, 52848, 16738, 54362, 44924, 8810, 46445, 2146, 33642, 5652, 59653, 31768, 63261, 19013, 56652, 20557, 11034, 48688, 12594, 33832, 7979, 37420, 25909, 63540, 29545, 50908, 22978, 11421, 42967, 15050, 36309, 141, 39872, 28411, 57848, 29929, 53232, 17124, 54765, 43236, 9203, 46736, 2453, 40145, 6105, 60055, 32132, 61579, 19340, 56999, 20899, 9396, 49079, 12962, 34273, 6329, 37798, 25877, 63580, 29532, 50775, 22860, 11335, 42824, 14938, 36193, '1', 39801, 28284, 57699, 29800, 53091, 17004, 54545, 43089, 8971, 46616, 2313, 39956, 5896, 11439, 42567, 14623, 35932, 1879, 39505, 27972, 57409, 31558, 52837, 16703, 54388, 44919, 8812, 46453, 2148, 33640, 5657, 59708, 31768, 63242, 18966, 56576, 20490, 11020, 48658, 12596, 33842, 7989, 37418, 25890, 63532, 29501, 50908, 23006, 11475, 11432, 42567, 14676, 35952, 1884, 39510, 27986, 57420, 31566, 52848, 16738, 54362, 44924, 8810, 46445, 2146, 33642, 5652, 59653, 31768, 63261, 19013, 56652, 20557, 11012, 48692, 12607, 33844, 7999, 37408, 25906, 63545, 29545, 50883, 22992, 11473, 42956, 15040, 36225, 195, 39878, 28385, 57777, 29947, 53238, 17136, 54767, 43241, 9129, 46808, 2513, 40072, 6026, 60044, 32143, 61578, 19401, 57009, 20916, 9403, 49080, 12976, 34221, 6329, 37865, 25923, 63568, 29521, 50764, 22848};
        h = -378842236371229131L;
    }

    static void init$0() {
        $$a = new byte[]{77, -11, 68, UtilitiesSDKConstants.SRP_LABEL_MAC};
        $$b = 18;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 102
            int r7 = r7 * 2
            int r7 = 4 - r7
            byte[] r0 = o.fn.h.$$a
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L30
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L30:
            int r8 = -r8
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.h.l(short, int, byte, java.lang.Object[]):void");
    }

    public h(boolean z, boolean z2, boolean z3, boolean z4, f fVar, int i) {
        super(true);
        this.c = o.i.d.c();
        this.b = z;
        this.e = z2;
        this.d = z3;
        this.a = z4;
        this.i = fVar;
        this.j = new o.ds.c();
        o.ds.c.b(fVar);
        this.g = i;
    }

    h() {
        super(false);
        this.c = o.i.d.c();
    }

    final void e() {
        int i = f87o + 27;
        n = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 10410), TextUtils.getCapsMode("", 0, 0), 13 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (52122 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), 13 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + 14, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.c.e();
        int i3 = n + 47;
        f87o = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    final void d(Context context) {
        int i = n + 27;
        f87o = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        k((char) (10408 - ImageFormat.getBitsPerPixel(0)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, (ViewConfiguration.getPressedStateDuration() >> 16) + 13, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k((char) (TextUtils.indexOf((CharSequence) "", '0') + 50367), 27 - Color.blue(0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 14, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.c.d(context);
        int i3 = n + 7;
        f87o = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    private boolean f() throws o.ei.j {
        Object obj;
        int i = f87o + 73;
        n = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                k((char) (10292 - View.MeasureSpec.getSize(0)), 42 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), 28 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k((char) (13660 - View.MeasureSpec.getSize(0)), Opcodes.LUSHR << (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (Process.getElapsedCpuTime() > 1L ? 1 : (Process.getElapsedCpuTime() == 1L ? 0 : -1)) + 51, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        boolean z = this.b;
        int i2 = f87o + 65;
        n = i2 % 128;
        int i3 = i2 % 2;
        return z;
    }

    public final int a() {
        int i = f87o + 11;
        n = i % 128;
        switch (i % 2 == 0 ? '?' : Typography.dollar) {
            case '$':
                return this.g;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:7:0x001e. Please report as an issue. */
    public final boolean b(Context context) {
        int i = f87o + 53;
        n = i % 128;
        int i2 = i % 2;
        try {
            boolean f2 = f();
            int i3 = n + 93;
            f87o = i3 % 128;
            switch (i3 % 2 != 0) {
            }
            return f2;
        } catch (o.ei.j e) {
            g.c();
            Object[] objArr = new Object[1];
            k((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 10408), KeyEvent.normalizeMetaState(0), 13 - TextUtils.getOffsetAfter("", 0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 69, 88 - TextUtils.indexOf("", "", 0), objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            try {
                Object[] objArr3 = new Object[1];
                k((char) Gravity.getAbsoluteGravity(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + Opcodes.IFGT, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 36, objArr3);
                return Boolean.parseBoolean(o.a(context, ((String) objArr3[0]).intern()));
            } catch (PackageManager.NameNotFoundException e2) {
                g.c();
                Object[] objArr4 = new Object[1];
                k((char) (10408 - TextUtils.lastIndexOf("", '0')), (Process.getThreadPriority(0) + 20) >> 6, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 13, objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                Object[] objArr5 = new Object[1];
                k((char) (ViewConfiguration.getLongPressTimeout() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + 192, (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 70, objArr5);
                g.d(intern2, ((String) objArr5[0]).intern());
                return false;
            }
        }
    }

    public final WalletSettingsValue<Boolean> b() {
        boolean g;
        int i = n + 89;
        f87o = i % 128;
        boolean z = false;
        try {
            switch (i % 2 == 0) {
                case true:
                    g = g();
                    break;
                default:
                    g = g();
                    int i2 = 33 / 0;
                    break;
            }
            z = g;
            int i3 = f87o + 35;
            n = i3 % 128;
            int i4 = i3 % 2;
        } catch (o.ei.j e) {
        }
        return new WalletSettingsValue<>(Boolean.valueOf(z), WalletSettingsRights.ReadOnly);
    }

    private boolean g() throws o.ei.j {
        Object obj;
        int i = f87o + Opcodes.DSUB;
        n = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                k((char) (22610 >>> (ViewConfiguration.getMaximumFlingVelocity() - 87)), TextUtils.getTrimmedLength("") + 85, 59 >>> View.MeasureSpec.getSize(0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k((char) (10292 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), 41 - TextUtils.getTrimmedLength(""), View.MeasureSpec.getSize(0) + 27, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        boolean z = this.e;
        int i2 = n + 49;
        f87o = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 15 : (char) 17) {
            case 15:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return z;
        }
    }

    public final WalletSettingsValue<Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> c() {
        int i = f87o + 51;
        n = i % 128;
        Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod> map = null;
        try {
            switch (i % 2 != 0) {
                case false:
                    i();
                    map.hashCode();
                    throw null;
                default:
                    map = i();
                    break;
            }
        } catch (o.ei.j e) {
        }
        WalletSettingsValue<Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> walletSettingsValue = new WalletSettingsValue<>(map, WalletSettingsRights.ReadOnly);
        int i2 = n + 73;
        f87o = i2 % 128;
        int i3 = i2 % 2;
        return walletSettingsValue;
    }

    public final WalletSettingsValue<Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> c(CustomerAuthenticationMethodUsage... customerAuthenticationMethodUsageArr) {
        Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod> map;
        boolean z;
        try {
            map = i();
            Iterator<Map.Entry<CustomerAuthenticationMethodType, CustomerAuthenticationMethod>> it = map.entrySet().iterator();
            while (it.hasNext()) {
                Iterator<CustomerAuthenticationMethodUsage> it2 = it.next().getValue().getUsages().iterator();
                while (true) {
                    if (it2.hasNext()) {
                        int i = n + 61;
                        f87o = i % 128;
                        int i2 = i % 2;
                        switch (o.a.c(it2.next(), customerAuthenticationMethodUsageArr) ? (char) 3 : ',') {
                            case 3:
                                z = true;
                                break;
                        }
                    } else {
                        z = false;
                    }
                }
                switch (!z ? (char) 21 : 'X') {
                    case 21:
                        it.remove();
                        break;
                }
            }
            int i3 = f87o + 3;
            n = i3 % 128;
            int i4 = i3 % 2;
        } catch (Exception e) {
            map = null;
        }
        return new WalletSettingsValue<>(map, WalletSettingsRights.ReadOnly);
    }

    private Map<CustomerAuthenticationMethodType, CustomerAuthenticationMethod> i() throws o.ei.j {
        Object obj;
        int i = f87o + 61;
        n = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object[] objArr = new Object[1];
                k((char) (10292 - View.getDefaultSize(0, 0)), 40 - TextUtils.lastIndexOf("", '0'), 27 - View.MeasureSpec.getSize(0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k((char) (7314 % View.getDefaultSize(1, 0)), 19 >>> TextUtils.lastIndexOf("", 'g'), View.MeasureSpec.getSize(0) * 3, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        return this.c.d();
    }

    public final WalletSettingsValue<TransactionStartCondition> j() {
        TransactionStartCondition transactionStartCondition;
        int i = f87o + 13;
        n = i % 128;
        Object obj = null;
        try {
            switch (i % 2 == 0 ? ';' : 'H') {
                case 'H':
                    transactionStartCondition = f.c(o());
                    break;
                default:
                    f.c(o());
                    obj.hashCode();
                    throw null;
            }
        } catch (o.ei.j e) {
            transactionStartCondition = null;
        }
        WalletSettingsValue<TransactionStartCondition> walletSettingsValue = new WalletSettingsValue<>(transactionStartCondition, WalletSettingsRights.ReadOnly);
        int i2 = n + 59;
        f87o = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                throw null;
            default:
                return walletSettingsValue;
        }
    }

    private f o() throws o.ei.j {
        Object obj;
        int i = n + 75;
        f87o = i % 128;
        switch (i % 2 != 0 ? '=' : ':') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                Object[] objArr = new Object[1];
                k((char) (545 / ExpandableListView.getPackedPositionGroup(0L)), 29 - View.combineMeasuredStates(0, 0), Opcodes.FDIV / TextUtils.lastIndexOf("", 'w', 0, 0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k((char) (10292 - ExpandableListView.getPackedPositionGroup(0L)), 41 - View.combineMeasuredStates(0, 0), TextUtils.lastIndexOf("", '0', 0, 0) + 28, objArr2);
                obj = objArr2[0];
                break;
        }
        d(((String) obj).intern());
        f c = o.ds.c.c();
        int i2 = f87o + 53;
        n = i2 % 128;
        switch (i2 % 2 == 0 ? '_' : (char) 25) {
            case Opcodes.SWAP /* 95 */:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return c;
        }
    }

    final boolean e(o.eg.e eVar) throws o.ei.i {
        int i = n + 9;
        f87o = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k((char) (10292 - (ViewConfiguration.getFadingEdgeLength() >> 16)), 41 - ExpandableListView.getPackedPositionGroup(0L), (Process.myTid() >> 22) + 27, objArr);
        d(((String) objArr[0]).intern());
        boolean e = this.c.e(eVar);
        int i3 = f87o + 43;
        n = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return e;
            default:
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x001a, code lost:
    
        r0 = true;
     */
    /* JADX WARN: Removed duplicated region for block: B:19:0x0045  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0063  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(o.fn.h r5) {
        /*
            r4 = this;
            boolean r0 = r4.b
            boolean r1 = r5.b
            r2 = 1
            r3 = 0
            if (r0 != r1) goto L1c
            int r0 = o.fn.h.f87o
            int r0 = r0 + 45
            int r1 = r0 % 128
            o.fn.h.n = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L16
            r0 = r3
            goto L17
        L16:
            r0 = r2
        L17:
            switch(r0) {
                case 1: goto L1a;
                default: goto L1a;
            }
        L1a:
            r0 = r2
            goto L1d
        L1c:
            r0 = r3
        L1d:
            if (r0 == 0) goto L27
            boolean r0 = r4.e
            boolean r1 = r5.e
            if (r0 != r1) goto L27
            r0 = r2
            goto L28
        L27:
            r0 = r3
        L28:
            if (r0 == 0) goto L42
            boolean r0 = r4.d
            boolean r1 = r5.d
            if (r0 != r1) goto L32
            r0 = r3
            goto L33
        L32:
            r0 = r2
        L33:
            switch(r0) {
                case 1: goto L42;
                default: goto L36;
            }
        L36:
            int r0 = o.fn.h.f87o
            int r0 = r0 + 97
            int r1 = r0 % 128
            o.fn.h.n = r1
            int r0 = r0 % 2
            r0 = r2
            goto L43
        L42:
            r0 = r3
        L43:
            if (r0 == 0) goto L60
            int r0 = o.fn.h.n
            int r0 = r0 + 99
            int r1 = r0 % 128
            o.fn.h.f87o = r1
            int r0 = r0 % 2
            boolean r0 = r4.a
            boolean r1 = r5.a
            if (r0 != r1) goto L58
            r0 = 18
            goto L5a
        L58:
            r0 = 82
        L5a:
            switch(r0) {
                case 18: goto L5e;
                default: goto L5d;
            }
        L5d:
            goto L60
        L5e:
            r0 = r2
            goto L61
        L60:
            r0 = r3
        L61:
            if (r0 == 0) goto L77
            o.ds.f r0 = r4.i
            o.ds.f r5 = r5.i
            if (r0 != r5) goto L77
            int r5 = o.fn.h.f87o
            int r5 = r5 + 95
            int r0 = r5 % 128
            o.fn.h.n = r0
            int r5 = r5 % 2
            if (r5 != 0) goto L76
            r2 = r3
        L76:
            return r2
        L77:
            int r5 = o.fn.h.f87o
            int r5 = r5 + 19
            int r0 = r5 % 128
            o.fn.h.n = r0
            int r5 = r5 % 2
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.h.a(o.fn.h):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 602
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fn.h.k(char, int, int, java.lang.Object[]):void");
    }
}
