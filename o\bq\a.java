package o.bq;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bq\a.smali */
final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static char b;
    private static char c;
    private static char d;
    private static int e;
    private static int g;
    private static int h;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        h = 1;
        d = (char) 59395;
        b = (char) 60952;
        c = (char) 22339;
        a = (char) 19767;
        e = 874635376;
    }

    static void init$0() {
        $$a = new byte[]{108, 119, -51, 110};
        $$b = Opcodes.IXOR;
    }

    private static void j(int i, byte b2, short s, Object[] objArr) {
        int i2 = (s * 2) + Opcodes.DMUL;
        int i3 = (i * 4) + 4;
        int i4 = (b2 * 3) + 1;
        byte[] bArr = $$a;
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            i3++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i2 = i6 + i2;
            i6 = i6;
        }
        while (true) {
            int i7 = i5 + 1;
            bArr2[i7] = (byte) i2;
            if (i7 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i3];
            i3++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = i7;
            i2 = b3 + i2;
            i6 = i6;
        }
    }

    a() {
    }

    static o.dr.a a(o.eg.b bVar, String str) throws d {
        Object obj;
        int i = h + 27;
        g = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("癇\uf0f0\ue6cc茞뫙췍퀃씧", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 9, objArr);
        int intValue = bVar.e(((String) objArr[0]).intern(), (Integer) 0).intValue();
        if (intValue != 0) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            i(17 - TextUtils.getOffsetAfter("", 0), "\uffdd\b\u0018\u000f\u0004\ufff9ￃ\u0007\b\u0017\u0006\b\u0013\u001b\b\u0011\ufff8ￃ", 18 - View.MeasureSpec.getMode(0), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 108, true, objArr2);
            throw new UnsupportedOperationException(sb.append(((String) objArr2[0]).intern()).append(intValue).toString());
        }
        o.dr.a aVar = new o.dr.a();
        aVar.d(str);
        Object[] objArr3 = new Object[1];
        i((ViewConfiguration.getLongPressTimeout() >> 16) + 4, "\u0007\u0001\uffff\ufff3\u0006\u0000", TextUtils.lastIndexOf("", '0', 0) + 7, View.getDefaultSize(0, 0) + Opcodes.LUSHR, true, objArr3);
        if (bVar.b(((String) objArr3[0]).intern())) {
            int i3 = h + Opcodes.LMUL;
            g = i3 % 128;
            char c2 = i3 % 2 != 0 ? 'W' : 'M';
            long globalActionKeyTimeout = ViewConfiguration.getGlobalActionKeyTimeout();
            switch (c2) {
                case Opcodes.POP /* 87 */:
                    Object[] objArr4 = new Object[1];
                    i(4 % (globalActionKeyTimeout > 1L ? 1 : (globalActionKeyTimeout == 1L ? 0 : -1)), "\u0007\u0001\uffff\ufff3\u0006\u0000", 114 % (ViewConfiguration.getKeyRepeatTimeout() << Opcodes.DSUB), (Process.myPid() / 80) + 53, false, objArr4);
                    obj = objArr4[0];
                    break;
                default:
                    Object[] objArr5 = new Object[1];
                    i((globalActionKeyTimeout > 0L ? 1 : (globalActionKeyTimeout == 0L ? 0 : -1)) + 3, "\u0007\u0001\uffff\ufff3\u0006\u0000", 6 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 125 - (Process.myPid() >> 22), true, objArr5);
                    obj = objArr5[0];
                    break;
            }
            aVar.d(bVar.l(((String) obj).intern()).abs());
        }
        Object[] objArr6 = new Object[1];
        f("퍎综飵䢘\u0b5b㻜\udd66⼪", 8 - Color.blue(0), objArr6);
        switch (bVar.b(((String) objArr6[0]).intern()) ? 12 : 17) {
            case true:
                Object[] objArr7 = new Object[1];
                f("퍎综飵䢘\u0b5b㻜\udd66⼪", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 8, objArr7);
                aVar.a(o.ej.e.c(bVar.i(((String) objArr7[0]).intern()).intValue()));
                int i4 = g + 61;
                h = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
        Object[] objArr8 = new Object[1];
        i(3 - (ViewConfiguration.getEdgeSlop() >> 16), "\ufffa\r\ufffe�", 4 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getScrollBarSize() >> 8) + Opcodes.FNEG, false, objArr8);
        aVar.d(bVar.b(((String) objArr8[0]).intern(), true));
        Object[] objArr9 = new Object[1];
        f("䙓\u0bfd㦬籏᧳稃폭噵뵞짼뾆濌浒ᇕ", ((byte) KeyEvent.getModifierMetaStateMask()) + 14, objArr9);
        aVar.a(bVar.q(((String) objArr9[0]).intern()));
        Object[] objArr10 = new Object[1];
        i(17 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\t\uffdd\u0013\f\t\u0001\uffff\u000e\ufffb\uffdd\u000e\b\ufffb\u0002�\f\uffff\u0007\uffff\ufffe", 19 - TextUtils.lastIndexOf("", '0'), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.INEG, true, objArr10);
        aVar.s(bVar.q(((String) objArr10[0]).intern()));
        Object[] objArr11 = new Object[1];
        i(ExpandableListView.getPackedPositionGroup(0L) + 2, "￼\ufffe\uffff\u0004\ufffa\uffff\r", View.MeasureSpec.getSize(0) + 7, 116 - Color.blue(0), true, objArr11);
        aVar.f(bVar.q(((String) objArr11[0]).intern()));
        Object[] objArr12 = new Object[1];
        i(ExpandableListView.getPackedPositionChild(0L) + 5, "\uffff\r￼\ufffe\u0007\u0000�￼\u0007\ufffa", Color.green(0) + 10, TextUtils.indexOf((CharSequence) "", '0', 0) + Opcodes.LNEG, true, objArr12);
        aVar.e(bVar.q(((String) objArr12[0]).intern()));
        Object[] objArr13 = new Object[1];
        i(-TextUtils.lastIndexOf("", '0'), "�\b\u0003￼\ufff9\ufffe\f\ufffb", 8 - TextUtils.indexOf("", ""), 117 - (ViewConfiguration.getDoubleTapTimeout() >> 16), true, objArr13);
        aVar.b(bVar.q(((String) objArr13[0]).intern()));
        Object[] objArr14 = new Object[1];
        f("癇\uf0f0Ùﰑꅕὑ剟쀿\ue3f3螶\uf733⬉\u244e米\ue21e킢", 16 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr14);
        aVar.c(bVar.q(((String) objArr14[0]).intern()));
        Object[] objArr15 = new Object[1];
        i(20 - (ViewConfiguration.getTapTimeout() >> 16), "\ufff8\t\ufffb\ufff6\ufffe\t\ufff8\u0007\uffff\u0000\ufffa\ufff6\t￼\n\u0006\f\t\ufffa￼\ufffa", TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 22, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + Opcodes.ISHL, false, objArr15);
        aVar.i(bVar.q(((String) objArr15[0]).intern()));
        Object[] objArr16 = new Object[1];
        i(4 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), "\u0005\ufff4\ufffe\ufff9￼\u0007\u0004\n", 7 - TextUtils.lastIndexOf("", '0'), Gravity.getAbsoluteGravity(0, 0) + Opcodes.ISHR, false, objArr16);
        aVar.k(bVar.q(((String) objArr16[0]).intern()));
        Object[] objArr17 = new Object[1];
        f("ൈ☈\u171f岯\ufd90僑뼓☯㏐鬽\ue094ⅺ", MotionEvent.axisFromString("") + 12, objArr17);
        aVar.m(bVar.q(((String) objArr17[0]).intern()));
        Object[] objArr18 = new Object[1];
        i((ViewConfiguration.getScrollDefaultDelay() >> 16) + 18, "￼\u0004\r\ufff6\ufff8\u0007\u0007\u0003\u0000\ufffa\ufff8\u000b\u0000\u0006\u0005\ufff6\u0000\ufffb", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 18, 119 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), false, objArr18);
        aVar.l(bVar.q(((String) objArr18[0]).intern()));
        Object[] objArr19 = new Object[1];
        i(-Process.getGidForName(""), "\ufffa\u0001\ufff6\t\ufffe\t\n\ufff9", (-16777208) - Color.rgb(0, 0, 0), (Process.myPid() >> 22) + Opcodes.ISHR, false, objArr19);
        aVar.e(bVar.c(((String) objArr19[0]).intern(), Double.valueOf(-1.0d)).doubleValue());
        Object[] objArr20 = new Object[1];
        i(4 - View.combineMeasuredStates(0, 0), "￼\u0003\u0004\u0001\ufffa\ufff9\n\t\ufffe", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 9, 122 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), true, objArr20);
        aVar.d(bVar.c(((String) objArr20[0]).intern(), Double.valueOf(-1.0d)).doubleValue());
        Object[] objArr21 = new Object[1];
        f("\uf03cᅘ绎敶俍쩱⟼槃ᴼ⸚컆ﰮ", Color.argb(0, 0, 0, 0) + 12, objArr21);
        aVar.q(bVar.c(((String) objArr21[0]).intern(), str));
        Object[] objArr22 = new Object[1];
        i(Color.alpha(0) + 2, "\ufff6\u0003\u0005�\u0006\u0004", View.resolveSizeAndState(0, 0, 0) + 6, TextUtils.indexOf((CharSequence) "", '0') + 127, true, objArr22);
        switch (bVar.b(((String) objArr22[0]).intern()) ? 'N' : '1') {
            case 'N':
                int i6 = h + Opcodes.LNEG;
                g = i6 % 128;
                int i7 = i6 % 2;
                Object[] objArr23 = new Object[1];
                i((ViewConfiguration.getPressedStateDuration() >> 16) + 2, "\ufff6\u0003\u0005�\u0006\u0004", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 6, 125 - MotionEvent.axisFromString(""), true, objArr23);
                aVar.a(o.dr.c.e(bVar.i(((String) objArr23[0]).intern()).intValue()));
                int i8 = h + 33;
                g = i8 % 128;
                int i9 = i8 % 2;
                break;
        }
        Object[] objArr24 = new Object[1];
        f("㯟ⲵ睿ᇈ", View.MeasureSpec.getSize(0) + 3, objArr24);
        if (bVar.b(((String) objArr24[0]).intern())) {
            Object[] objArr25 = new Object[1];
            f("㯟ⲵ睿ᇈ", 3 - ((Process.getThreadPriority(0) + 20) >> 6), objArr25);
            aVar.b(bVar.i(((String) objArr25[0]).intern()).intValue());
        }
        Object[] objArr26 = new Object[1];
        f("Ŗ촟죂뻘", 4 - KeyEvent.getDeadChar(0, 0), objArr26);
        switch (bVar.b(((String) objArr26[0]).intern()) ? '^' : ' ') {
            case Opcodes.DUP2_X2 /* 94 */:
                Object[] objArr27 = new Object[1];
                f("Ŗ촟죂뻘", 4 - TextUtils.getOffsetBefore("", 0), objArr27);
                aVar.d(o.dr.d.a(bVar.i(((String) objArr27[0]).intern()).intValue()));
                break;
        }
        Object[] objArr28 = new Object[1];
        i((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1, "\f￼\uffff\u0000\ufffe\uffff\ufff6\r\ufff8\u0003", 10 - View.resolveSize(0, 0), 120 - KeyEvent.normalizeMetaState(0), false, objArr28);
        aVar.e(bVar.b(((String) objArr28[0]).intern(), Boolean.FALSE).booleanValue());
        Object[] objArr29 = new Object[1];
        f("\u0a56㦊푎洹줺ῃ랅\ud86c儓䞼뫫﹤㬥ۙ자ꮄ\ue5b6\ude3a踑䃐", 19 - (Process.myTid() >> 22), objArr29);
        aVar.b(bVar.b(((String) objArr29[0]).intern(), Boolean.FALSE).booleanValue());
        Object[] objArr30 = new Object[1];
        i((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 5, "\u0004\uffff\u0002\u0004\u0005\ufffa\ufffb\b\uffff\u000b\u0007\ufffb\b\ufff5\u0004\uffff\u0006\ufff5\ufffb", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 19, 120 - MotionEvent.axisFromString(""), true, objArr30);
        aVar.a(bVar.b(((String) objArr30[0]).intern(), Boolean.FALSE).booleanValue());
        Object[] objArr31 = new Object[1];
        i(9 - View.MeasureSpec.getMode(0), "\ufffa\u0007\n\t\ufff6\u0003￼\ufffe\b\ufff9\ufffa\u0007\ufffe\n\u0006\ufffa\u0007\ufff4", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 17, 122 - Color.argb(0, 0, 0, 0), true, objArr31);
        aVar.d(bVar.b(((String) objArr31[0]).intern(), Boolean.FALSE).booleanValue());
        Object[] objArr32 = new Object[1];
        f("\ud8dd\ueaaeꜱ哱줺ῃ俬㯵ꠏ虗", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 10, objArr32);
        aVar.r(bVar.q(((String) objArr32[0]).intern()));
        Object[] objArr33 = new Object[1];
        f("䙓\u0bfd㦬籏᧳稃폭噵ǝ\uf54a⟦䲚ᴼ⸚㷕泓鄧រ", 18 - Color.blue(0), objArr33);
        aVar.o(bVar.q(((String) objArr33[0]).intern()));
        return aVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r25, int r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 566
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.a.f(java.lang.String, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 572
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.a.i(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
