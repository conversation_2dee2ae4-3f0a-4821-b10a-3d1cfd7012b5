package androidx.core.view;

import android.os.CancellationSignal;
import android.view.View;
import android.view.Window;
import android.view.WindowInsetsAnimationControlListener;
import android.view.WindowInsetsAnimationController;
import android.view.WindowInsetsController;
import android.view.animation.Interpolator;
import androidx.collection.SimpleArrayMap;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\view\WindowInsetsControllerCompat$Impl30.smali */
public class WindowInsetsControllerCompat$Impl30 extends WindowInsetsControllerCompat$Impl {
    final WindowInsetsControllerCompat mCompatController;
    final WindowInsetsController mInsetsController;
    private final SimpleArrayMap<WindowInsetsControllerCompat$OnControllableInsetsChangedListener, WindowInsetsController.OnControllableInsetsChangedListener> mListeners;
    final SoftwareKeyboardControllerCompat mSoftwareKeyboardControllerCompat;
    protected Window mWindow;

    WindowInsetsControllerCompat$Impl30(Window window, WindowInsetsControllerCompat compatController, SoftwareKeyboardControllerCompat softwareKeyboardControllerCompat) {
        this(window.getInsetsController(), compatController, softwareKeyboardControllerCompat);
        this.mWindow = window;
    }

    WindowInsetsControllerCompat$Impl30(WindowInsetsController insetsController, WindowInsetsControllerCompat compatController, SoftwareKeyboardControllerCompat softwareKeyboardControllerCompat) {
        this.mListeners = new SimpleArrayMap<>();
        this.mInsetsController = insetsController;
        this.mCompatController = compatController;
        this.mSoftwareKeyboardControllerCompat = softwareKeyboardControllerCompat;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void show(int types) {
        if ((types & 8) != 0) {
            this.mSoftwareKeyboardControllerCompat.show();
        }
        this.mInsetsController.show(types & (-9));
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void hide(int types) {
        if ((types & 8) != 0) {
            this.mSoftwareKeyboardControllerCompat.hide();
        }
        this.mInsetsController.hide(types & (-9));
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public boolean isAppearanceLightStatusBars() {
        return (this.mInsetsController.getSystemBarsAppearance() & 8) != 0;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public void setAppearanceLightStatusBars(boolean isLight) {
        if (isLight) {
            if (this.mWindow != null) {
                setSystemUiFlag(8192);
            }
            this.mInsetsController.setSystemBarsAppearance(8, 8);
        } else {
            if (this.mWindow != null) {
                unsetSystemUiFlag(8192);
            }
            this.mInsetsController.setSystemBarsAppearance(0, 8);
        }
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public boolean isAppearanceLightNavigationBars() {
        return (this.mInsetsController.getSystemBarsAppearance() & 16) != 0;
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    public void setAppearanceLightNavigationBars(boolean isLight) {
        if (isLight) {
            if (this.mWindow != null) {
                setSystemUiFlag(16);
            }
            this.mInsetsController.setSystemBarsAppearance(16, 16);
        } else {
            if (this.mWindow != null) {
                unsetSystemUiFlag(16);
            }
            this.mInsetsController.setSystemBarsAppearance(0, 16);
        }
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void controlWindowInsetsAnimation(int types, long durationMillis, Interpolator interpolator, CancellationSignal cancellationSignal, final WindowInsetsAnimationControlListenerCompat listener) {
        WindowInsetsAnimationControlListener fwListener = new WindowInsetsAnimationControlListener() { // from class: androidx.core.view.WindowInsetsControllerCompat$Impl30.1
            private WindowInsetsAnimationControllerCompat mCompatAnimController = null;

            @Override // android.view.WindowInsetsAnimationControlListener
            public void onReady(WindowInsetsAnimationController controller, int types2) {
                WindowInsetsAnimationControllerCompat windowInsetsAnimationControllerCompat = new WindowInsetsAnimationControllerCompat(controller);
                this.mCompatAnimController = windowInsetsAnimationControllerCompat;
                listener.onReady(windowInsetsAnimationControllerCompat, types2);
            }

            @Override // android.view.WindowInsetsAnimationControlListener
            public void onFinished(WindowInsetsAnimationController controller) {
                listener.onFinished(this.mCompatAnimController);
            }

            @Override // android.view.WindowInsetsAnimationControlListener
            public void onCancelled(WindowInsetsAnimationController controller) {
                listener.onCancelled(controller == null ? null : this.mCompatAnimController);
            }
        };
        this.mInsetsController.controlWindowInsetsAnimation(types, durationMillis, interpolator, cancellationSignal, fwListener);
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void setSystemBarsBehavior(int behavior) {
        this.mInsetsController.setSystemBarsBehavior(behavior);
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    int getSystemBarsBehavior() {
        return this.mInsetsController.getSystemBarsBehavior();
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void addOnControllableInsetsChangedListener(final WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
        if (this.mListeners.containsKey(listener)) {
            return;
        }
        WindowInsetsController.OnControllableInsetsChangedListener fwListener = new WindowInsetsController.OnControllableInsetsChangedListener() { // from class: androidx.core.view.WindowInsetsControllerCompat$Impl30$$ExternalSyntheticLambda0
            @Override // android.view.WindowInsetsController.OnControllableInsetsChangedListener
            public final void onControllableInsetsChanged(WindowInsetsController windowInsetsController, int i) {
                WindowInsetsControllerCompat$Impl30.this.m41xe96d8c51(listener, windowInsetsController, i);
            }
        };
        this.mListeners.put(listener, fwListener);
        this.mInsetsController.addOnControllableInsetsChangedListener(fwListener);
    }

    /* renamed from: lambda$addOnControllableInsetsChangedListener$0$androidx-core-view-WindowInsetsControllerCompat$Impl30, reason: not valid java name */
    /* synthetic */ void m41xe96d8c51(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener, WindowInsetsController controller, int typeMask) {
        if (this.mInsetsController == controller) {
            listener.onControllableInsetsChanged(this.mCompatController, typeMask);
        }
    }

    @Override // androidx.core.view.WindowInsetsControllerCompat$Impl
    void removeOnControllableInsetsChangedListener(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
        WindowInsetsController.OnControllableInsetsChangedListener fwListener = this.mListeners.remove(listener);
        if (fwListener != null) {
            this.mInsetsController.removeOnControllableInsetsChangedListener(fwListener);
        }
    }

    protected void unsetSystemUiFlag(int systemUiFlag) {
        View decorView = this.mWindow.getDecorView();
        decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() & (~systemUiFlag));
    }

    protected void setSystemUiFlag(int systemUiFlag) {
        View decorView = this.mWindow.getDecorView();
        decorView.setSystemUiVisibility(decorView.getSystemUiVisibility() | systemUiFlag);
    }
}
