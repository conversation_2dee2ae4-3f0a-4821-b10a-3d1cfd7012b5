package fr.antelop.sdk.authentication.prompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\DeviceBiometricCustomerAuthenticationPromptBuilder.smali */
public final class DeviceBiometricCustomerAuthenticationPromptBuilder extends CustomerAuthenticationPromptBuilder {
    private String subtitle;
    private String title;

    public final DeviceBiometricCustomerAuthenticationPromptBuilder setTitle(String str) {
        this.title = str;
        return this;
    }

    public final DeviceBiometricCustomerAuthenticationPromptBuilder setSubtitle(String str) {
        this.subtitle = str;
        return this;
    }

    @Override // fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder
    public final DeviceBiometricCustomerAuthenticationPrompt build() {
        return new DeviceBiometricCustomerAuthenticationPrompt(this.title, this.subtitle);
    }
}
