package kotlinx.coroutines.internal;

import kotlin.Metadata;

/* compiled from: Atomic.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0005\"\u0016\u0010\u0000\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0002\u0010\u0003\"\u0016\u0010\u0004\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0005\u0010\u0003¨\u0006\u0006"}, d2 = {"NO_DECISION", "", "getNO_DECISION$annotations", "()V", "RETRY_ATOMIC", "getRETRY_ATOMIC$annotations", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\internal\AtomicKt.smali */
public final class AtomicKt {
    public static final Object NO_DECISION = new Symbol("NO_DECISION");
    public static final Object RETRY_ATOMIC = new Symbol("RETRY_ATOMIC");

    public static /* synthetic */ void getNO_DECISION$annotations() {
    }

    public static /* synthetic */ void getRETRY_ATOMIC$annotations() {
    }
}
