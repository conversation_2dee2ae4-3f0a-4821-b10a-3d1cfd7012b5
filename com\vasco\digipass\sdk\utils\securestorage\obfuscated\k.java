package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\k.smali */
public abstract class k {
    public static final String a;
    public static final String b;
    public static final String c;
    public static final String d;

    static {
        String c2 = w.c();
        Intrinsics.checkNotNullExpressionValue(c2, "getSeparatorKeyValuePairs()");
        a = c2;
        String b2 = w.b();
        Intrinsics.checkNotNullExpressionValue(b2, "getSeparatorKeyValue()");
        b = b2;
        byte[] bArr = new byte[4];
        int[] iArr = {217, 217, 217, 223};
        for (int i = 0; i < 4; i++) {
            bArr[i] = (byte) (((iArr[i] - 169) - i) + i);
        }
        String str = new String(bArr);
        Intrinsics.checkNotNullExpressionValue(str, "getStorageVersion0006()");
        c = str;
        String a2 = w.a();
        Intrinsics.checkNotNullExpressionValue(a2, "getAndroidKeyStoreType()");
        d = a2;
    }
}
