package com.capacitorjs.plugins.network;

import androidx.core.os.EnvironmentCompat;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\NetworkStatus.smali */
public class NetworkStatus {
    public boolean connected = false;
    public ConnectionType connectionType = ConnectionType.NONE;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\NetworkStatus$ConnectionType.smali */
    public enum ConnectionType {
        WIFI("wifi"),
        CELLULAR("cellular"),
        NONE("none"),
        UNKNOWN(EnvironmentCompat.MEDIA_UNKNOWN);

        private String connectionType;

        ConnectionType(String connectionType) {
            this.connectionType = connectionType;
        }

        public String getConnectionType() {
            return this.connectionType;
        }
    }
}
