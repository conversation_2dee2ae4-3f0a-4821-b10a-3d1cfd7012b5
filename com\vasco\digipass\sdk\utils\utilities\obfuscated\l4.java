package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\l4.smali */
public class l4 extends u {
    r b;
    r x;

    private l4(e0 e0Var) {
        Enumeration j = e0Var.j();
        this.b = (r) j.nextElement();
        this.x = (r) j.nextElement();
    }

    public static l4 a(Object obj) {
        if (obj instanceof l4) {
            return (l4) obj;
        }
        if (obj != null) {
            return new l4(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.x.h();
    }

    public BigInteger f() {
        return this.b.h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(this.b);
        iVar.a(this.x);
        return new j2(iVar);
    }
}
