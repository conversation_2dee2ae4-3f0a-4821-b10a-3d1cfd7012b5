package com.google.firebase.heartbeatinfo;

import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\heartbeatinfo\AutoValue_HeartBeatResult.smali */
final class AutoValue_HeartBeatResult extends HeartBeatResult {
    private final List<String> usedDates;
    private final String userAgent;

    AutoValue_HeartBeatResult(String userAgent, List<String> usedDates) {
        if (userAgent == null) {
            throw new NullPointerException("Null userAgent");
        }
        this.userAgent = userAgent;
        if (usedDates == null) {
            throw new NullPointerException("Null usedDates");
        }
        this.usedDates = usedDates;
    }

    @Override // com.google.firebase.heartbeatinfo.HeartBeatResult
    public String getUserAgent() {
        return this.userAgent;
    }

    @Override // com.google.firebase.heartbeatinfo.HeartBeatResult
    public List<String> getUsedDates() {
        return this.usedDates;
    }

    public String toString() {
        return "HeartBeatResult{userAgent=" + this.userAgent + ", usedDates=" + this.usedDates + "}";
    }

    public boolean equals(Object o2) {
        if (o2 == this) {
            return true;
        }
        if (!(o2 instanceof HeartBeatResult)) {
            return false;
        }
        HeartBeatResult that = (HeartBeatResult) o2;
        return this.userAgent.equals(that.getUserAgent()) && this.usedDates.equals(that.getUsedDates());
    }

    public int hashCode() {
        int h$ = 1 * 1000003;
        return ((h$ ^ this.userAgent.hashCode()) * 1000003) ^ this.usedDates.hashCode();
    }
}
