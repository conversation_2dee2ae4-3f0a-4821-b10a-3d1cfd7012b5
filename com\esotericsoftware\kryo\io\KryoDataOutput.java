package com.esotericsoftware.kryo.io;

import java.io.DataOutput;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\io\KryoDataOutput.smali */
public class KryoDataOutput implements DataOutput, AutoCloseable {
    protected Output output;

    public KryoDataOutput(Output output) {
        this.output = output;
    }

    public void setOutput(Output output) {
        this.output = output;
    }

    @Override // java.io.DataOutput
    public void write(int b) throws IOException {
        this.output.write(b);
    }

    @Override // java.io.DataOutput
    public void write(byte[] b) throws IOException {
        this.output.write(b);
    }

    @Override // java.io.DataOutput
    public void write(byte[] b, int off, int len) throws IOException {
        this.output.write(b, off, len);
    }

    @Override // java.io.DataOutput
    public void writeBoolean(boolean v) throws IOException {
        this.output.writeBoolean(v);
    }

    @Override // java.io.DataOutput
    public void writeByte(int v) throws IOException {
        this.output.writeByte(v);
    }

    @Override // java.io.DataOutput
    public void writeShort(int v) throws IOException {
        this.output.writeShort(v);
    }

    @Override // java.io.DataOutput
    public void writeChar(int v) throws IOException {
        this.output.writeChar((char) v);
    }

    @Override // java.io.DataOutput
    public void writeInt(int v) throws IOException {
        this.output.writeInt(v);
    }

    @Override // java.io.DataOutput
    public void writeLong(long v) throws IOException {
        this.output.writeLong(v);
    }

    @Override // java.io.DataOutput
    public void writeFloat(float v) throws IOException {
        this.output.writeFloat(v);
    }

    @Override // java.io.DataOutput
    public void writeDouble(double v) throws IOException {
        this.output.writeDouble(v);
    }

    @Override // java.io.DataOutput
    public void writeBytes(String s) throws IOException {
        int len = s.length();
        for (int i = 0; i < len; i++) {
            this.output.write((byte) s.charAt(i));
        }
    }

    @Override // java.io.DataOutput
    public void writeChars(String s) throws IOException {
        int len = s.length();
        for (int i = 0; i < len; i++) {
            int v = s.charAt(i);
            this.output.write(v & 255);
            this.output.write((v >>> 8) & 255);
        }
    }

    @Override // java.io.DataOutput
    public void writeUTF(String s) throws IOException {
        this.output.writeString(s);
    }

    @Override // java.lang.AutoCloseable
    public void close() throws Exception {
        this.output.close();
    }
}
