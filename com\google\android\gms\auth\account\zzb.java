package com.google.android.gms.auth.account;

import android.accounts.Account;
import android.os.IInterface;
import android.os.RemoteException;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\auth\account\zzb.smali */
public interface zzb extends IInterface {
    void zzb(Account account) throws RemoteException;

    void zzc(boolean z) throws RemoteException;
}
