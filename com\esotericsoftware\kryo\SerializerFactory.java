package com.esotericsoftware.kryo;

import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.serializers.CompatibleFieldSerializer;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.serializers.TaggedFieldSerializer;
import com.esotericsoftware.kryo.serializers.VersionFieldSerializer;
import com.esotericsoftware.kryo.util.Util;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory.smali */
public interface SerializerFactory<T extends Serializer> {
    boolean isSupported(Class cls);

    T newSerializer(Kryo kryo, Class cls);

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$BaseSerializerFactory.smali */
    public static abstract class BaseSerializerFactory<T extends Serializer> implements SerializerFactory<T> {
        @Override // com.esotericsoftware.kryo.SerializerFactory
        public boolean isSupported(Class type) {
            return true;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$ReflectionSerializerFactory.smali */
    public static class ReflectionSerializerFactory<T extends Serializer> extends BaseSerializerFactory<T> {
        private final Class<T> serializerClass;

        public ReflectionSerializerFactory(Class<T> serializerClass) {
            this.serializerClass = serializerClass;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public T newSerializer(Kryo kryo, Class cls) {
            return (T) newSerializer(kryo, this.serializerClass, cls);
        }

        public static <T extends Serializer> T newSerializer(Kryo kryo, Class<T> serializerClass, Class type) {
            try {
                try {
                    return serializerClass.getConstructor(Kryo.class, Class.class).newInstance(kryo, type);
                } catch (NoSuchMethodException e) {
                    try {
                        return serializerClass.getConstructor(Kryo.class).newInstance(kryo);
                    } catch (NoSuchMethodException e2) {
                        try {
                            return serializerClass.getConstructor(Class.class).newInstance(type);
                        } catch (NoSuchMethodException e3) {
                            return serializerClass.newInstance();
                        }
                    }
                }
            } catch (Exception ex) {
                throw new IllegalArgumentException("Unable to create serializer \"" + serializerClass.getName() + "\" for class: " + Util.className(type), ex);
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$SingletonSerializerFactory.smali */
    public static class SingletonSerializerFactory<T extends Serializer> extends BaseSerializerFactory<T> {
        private final T serializer;

        public SingletonSerializerFactory(T serializer) {
            this.serializer = serializer;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public T newSerializer(Kryo kryo, Class type) {
            return this.serializer;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$FieldSerializerFactory.smali */
    public static class FieldSerializerFactory extends BaseSerializerFactory<FieldSerializer> {
        private final FieldSerializer.FieldSerializerConfig config;

        public FieldSerializerFactory() {
            this.config = new FieldSerializer.FieldSerializerConfig();
        }

        public FieldSerializerFactory(FieldSerializer.FieldSerializerConfig config) {
            this.config = config;
        }

        public FieldSerializer.FieldSerializerConfig getConfig() {
            return this.config;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public FieldSerializer newSerializer(Kryo kryo, Class type) {
            return new FieldSerializer(kryo, type, this.config.mo108clone());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$TaggedFieldSerializerFactory.smali */
    public static class TaggedFieldSerializerFactory extends BaseSerializerFactory<TaggedFieldSerializer> {
        private final TaggedFieldSerializer.TaggedFieldSerializerConfig config;

        public TaggedFieldSerializerFactory() {
            this.config = new TaggedFieldSerializer.TaggedFieldSerializerConfig();
        }

        public TaggedFieldSerializerFactory(TaggedFieldSerializer.TaggedFieldSerializerConfig config) {
            this.config = config;
        }

        public TaggedFieldSerializer.TaggedFieldSerializerConfig getConfig() {
            return this.config;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public TaggedFieldSerializer newSerializer(Kryo kryo, Class type) {
            return new TaggedFieldSerializer(kryo, type, this.config.mo108clone());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$VersionFieldSerializerFactory.smali */
    public static class VersionFieldSerializerFactory extends BaseSerializerFactory<VersionFieldSerializer> {
        private final VersionFieldSerializer.VersionFieldSerializerConfig config;

        public VersionFieldSerializerFactory() {
            this.config = new VersionFieldSerializer.VersionFieldSerializerConfig();
        }

        public VersionFieldSerializerFactory(VersionFieldSerializer.VersionFieldSerializerConfig config) {
            this.config = config;
        }

        public VersionFieldSerializer.VersionFieldSerializerConfig getConfig() {
            return this.config;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public VersionFieldSerializer newSerializer(Kryo kryo, Class type) {
            return new VersionFieldSerializer(kryo, type, this.config.mo108clone());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\SerializerFactory$CompatibleFieldSerializerFactory.smali */
    public static class CompatibleFieldSerializerFactory extends BaseSerializerFactory<CompatibleFieldSerializer> {
        private final CompatibleFieldSerializer.CompatibleFieldSerializerConfig config;

        public CompatibleFieldSerializerFactory() {
            this.config = new CompatibleFieldSerializer.CompatibleFieldSerializerConfig();
        }

        public CompatibleFieldSerializerFactory(CompatibleFieldSerializer.CompatibleFieldSerializerConfig config) {
            this.config = config;
        }

        public CompatibleFieldSerializer.CompatibleFieldSerializerConfig getConfig() {
            return this.config;
        }

        @Override // com.esotericsoftware.kryo.SerializerFactory
        public CompatibleFieldSerializer newSerializer(Kryo kryo, Class type) {
            return new CompatibleFieldSerializer(kryo, type, this.config.mo108clone());
        }
    }
}
