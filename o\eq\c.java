package o.eq;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.DeviceWalletMockActivity;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.an.c;
import o.ee.g;
import o.ee.h;
import o.ee.i;
import o.ee.o;
import o.eo.e;
import o.ep.a;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eq\c.smali */
public final class c implements o.ep.c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static c b;
    private static long f;
    private static int g;
    private static int h;
    private static short[] i;
    private static byte[] j;
    private static int k;
    private static int n;
    private final Context c;
    private final boolean d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        n = 1;
        b();
        KeyEvent.getMaxKeyCode();
        ExpandableListView.getPackedPositionType(0L);
        AndroidCharacter.getMirror('0');
        int i2 = n + 85;
        k = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 7 : 'T') {
            case 7:
                throw null;
            default:
                return;
        }
    }

    static void b() {
        byte[] bArr = new byte[561];
        System.arraycopy("ðü\u000b÷\ró\u0001ÿ\rüï\u0005\u0001\rï\u0007\u000bê\u0018ùï\u0011î\u0007\u0003ùü\u0005\u0006ÿýù\u000føóÍ@ÿýù\u000føóÍBò\u0092a_\u0091K½DON\u009a\\U¯½A¼Dl\u0097Kº{\u0084b\u009a\u0093qA«xq\u008b\u0099e\u0098`H³o\u009e««°PA±©QXºªN\u00ad³Z¢C¿£_©E«?3ÀÆ!ÊÅÃ:ÇÇ>\u0013ë5Ä\u009a_i\u008a\u0085@ne\u009eï\u0019àëê\u0012\u0013\u001dî\u001eã\u0018\u001fìª[¥ª@°X\\ªSX¹«IQ© BRd\u0087\u0088Z\u009e\u0091c'Ø-!Ü}\u009cÑÒ#i\u0093Ôf\u009d&ÕÓÔ{\u0097*À Ði\u009eÔ{\u0094Ö%.Ü6\u001aÜ\"\u0083'Ø-!<ö*$Ü\"Ú>\rüÐ`Ü\"\u0093Ý>1à'Ø;Ô.\u000eþ\u001dâ Ý\u009aªg§u\u0086\u0080\u0087(\u008fqÐt\u008b~ro¥yw\u008fq\u0089m^¯\u00833\u008fqÀ\u008emb³t\u008bh\u0087}]\u00adN±s\u008e\u0086oÎ%\u009cdm\u008f\u009fÚ-`c\u0092Ø\"aibc\u009f\u0091mÌ0\u009dc\u009e\u0093Û,i\u0096\u009f\u009dc\u0087«m\u00932\u0096i\u009c\u0090\u008dG\u009b\u0095m\u0093k\u008f¼MaÑm\u0093\"l\u008f\u0080Q\u0096i\u008ae\u009f¿O¬S\u0091lö\tüð\r¬M\u0000\u0003ò¸B\u0005·L÷\u0004\u0002\u0005ªPó\u000eüþô·Fû\u0011ñ\u0001¸O\u0005ªE\u0007ôÿ\rçË\róRö\tüðí'ûõ\ró\u000bïÜ-\u0001±\róB\fïà%\u000b\u0000ûÆö;û)ÚÜÛtÓ-\u008c(×\".3ù%+Ó-Õ1\u0002óßoÓ-\u009cÒ1>ûÕÞ%\u0094}Ü7u\u0089t\u008c\u0084Ë?rq\u0080Ê0s{pq\u008d\u0083\u007fÞ\"\u008fq\u008c\u0081É>{\u0084\u008d\u008fq\u0095¹\u007f\u0081 \u0084{\u008e\u0082\u009fU\u0089\u0087\u007f\u0081y\u009d®_sÃ\u007f\u00810~\u009d\u0092Wyr\u0089\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090\u0090".getBytes(LocalizedMessage.DEFAULT_ENCODING), 0, bArr, 0, 561);
        j = bArr;
        h = 909053661;
        g = 38622617;
        a = -1432482171;
        f = 5964613782832210477L;
    }

    static void init$0() {
        $$a = new byte[]{112, 25, 2, PSSSigner.TRAILER_IMPLICIT};
        $$b = Opcodes.LSHL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(byte r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 68
            byte[] r0 = o.eq.c.$$a
            int r6 = r6 * 2
            int r6 = r6 + 1
            int r8 = r8 * 4
            int r8 = 3 - r8
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r8 = r8 + 1
            int r4 = r3 + 1
            if (r3 != r6) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.c.o(byte, int, int, java.lang.Object[]):void");
    }

    @Override // o.ep.a
    public final /* synthetic */ void e(Activity activity, a.c cVar, i iVar, e eVar, o.ep.e eVar2, c.e eVar3, h hVar) {
        int i2 = n + 51;
        k = i2 % 128;
        int i3 = i2 % 2;
        a(activity, cVar, iVar, eVar);
        int i4 = n + 3;
        k = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                int i5 = 61 / 0;
                return;
        }
    }

    public static c b(Context context) {
        if (b == null) {
            b = new c(context);
            int i2 = n + 57;
            k = i2 % 128;
            int i3 = i2 % 2;
        }
        c cVar = b;
        switch (cVar.d ? (char) 24 : '\n') {
            case 24:
                int i4 = k + Opcodes.LREM;
                n = i4 % 128;
                switch (i4 % 2 == 0 ? 'S' : (char) 7) {
                    case Opcodes.AASTORE /* 83 */:
                        throw null;
                    default:
                        return cVar;
                }
            default:
                return null;
        }
    }

    private c(Context context) {
        boolean a2 = a(context);
        this.d = a2;
        this.c = context;
        if (!a2) {
            e(context);
        }
        Object[] objArr = new Object[1];
        l((byte) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + Opcodes.DDIV), 1666121707 + (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (short) (ViewConfiguration.getLongPressTimeout() >> 16), Process.getGidForName("") - 29, (-878860963) + (ViewConfiguration.getJumpTapTimeout() >> 16), objArr);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        m("ⶌⷨࡍデ\uaada畅价\ua631Ꙁ⚛\ue11f튁㩨늠淘ꙃ躺乘\ud992⨊", ViewConfiguration.getScrollBarFadeDuration() >> 16, objArr2);
        sharedPreferences.getString(((String) objArr2[0]).intern(), "");
    }

    private static boolean a(Context context) {
        int i2 = k + Opcodes.DMUL;
        n = i2 % 128;
        int i3 = i2 % 2;
        try {
            Object[] objArr = new Object[1];
            m("̹͟䯻쥍\ue97b貇\ue186袟ऱ攵ᢄ緃ᓆ\uf109鐗ऺꀎි‚蕀Ȿ馆밦Ạ립ᙄ짞\uaaff䗨ꈠ", Drawable.resolveOpacity(0, 0), objArr);
            boolean parseBoolean = Boolean.parseBoolean(o.a(context, ((String) objArr[0]).intern()));
            int i4 = n + Opcodes.LSHR;
            k = i4 % 128;
            int i5 = i4 % 2;
            return parseBoolean;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }

    private static void e(Context context) {
        int i2 = n + 1;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", ((byte) KeyEvent.getModifierMetaStateMask()) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m("대덦\ue635\ue589䒫ꀘ庆㢠똣", TextUtils.indexOf("", ""), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l((byte) (Color.green(0) + Opcodes.FDIV), 1666121707 - (Process.myPid() >> 22), (short) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), (-31) - TextUtils.indexOf((CharSequence) "", '0'), (-878860964) - ImageFormat.getBitsPerPixel(0), objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        l((byte) (10 - ImageFormat.getBitsPerPixel(0)), 1666121753 + (ViewConfiguration.getJumpTapTimeout() >> 16), (short) Gravity.getAbsoluteGravity(0, 0), TextUtils.getTrimmedLength("") - 74, TextUtils.lastIndexOf("", '0', 0, 0) - 878860955, objArr4);
        edit.remove(((String) objArr4[0]).intern()).apply();
        int i4 = n + 29;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.ep.a
    public final void b(a.InterfaceC0042a<String> interfaceC0042a) {
        int i2 = k + 87;
        n = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", ViewConfiguration.getMaximumFlingVelocity() >> 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) ((-44) - (ViewConfiguration.getScrollDefaultDelay() >> 16)), 1666121755 - KeyEvent.getDeadChar(0, 0), (short) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 58, KeyEvent.getDeadChar(0, 0) - 878860962, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        m("强彥\ude1d㺾粰笗⦉풱섐\uf0ec\uef4d뗉䣺擞掓섩ﰵ頖ퟎ䵲灟\u0c5b䯥횯\ue596莘㸭拱", ViewConfiguration.getDoubleTapTimeout() >> 16, objArr3);
        interfaceC0042a.e((a.InterfaceC0042a<String>) ((String) objArr3[0]).intern());
        int i4 = n + Opcodes.LUSHR;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.ep.a
    public final void a(a.InterfaceC0042a<String> interfaceC0042a) {
        int i2 = n + 59;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", TextUtils.indexOf("", ""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 16), Color.blue(0) + 1666121774, (short) (ViewConfiguration.getEdgeSlop() >> 16), (-57) - TextUtils.getTrimmedLength(""), (-878860962) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        l((byte) (ImageFormat.getBitsPerPixel(0) + 60), 1666121793 - View.combineMeasuredStates(0, 0), (short) (KeyEvent.getMaxKeyCode() >> 16), TextUtils.lastIndexOf("", '0', 0, 0) - 52, MotionEvent.axisFromString("") - 878860969, objArr3);
        interfaceC0042a.e((a.InterfaceC0042a<String>) ((String) objArr3[0]).intern());
        int i4 = k + Opcodes.LUSHR;
        n = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    @Override // o.ep.a
    public final void e(a.InterfaceC0042a<a.b> interfaceC0042a) {
        String intern;
        Object obj;
        int i2 = n + 41;
        k = i2 % 128;
        switch (i2 % 2 != 0 ? 'M' : ',') {
            case 'M':
                g.c();
                Object[] objArr = new Object[1];
                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", (TypedValue.complexToFloat(1) > 0.0f ? 1 : (TypedValue.complexToFloat(1) == 0.0f ? 0 : -1)), objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                m("\uf108\uf16f蠇ⰾ⪐榮⋡窯쩰ꛒﷵ뺂\ue6fd㋕焫쩯切츬앮䘲\ude4d婡奩\uddf2䮁햩ⲇ榽", KeyEvent.normalizeMetaState(1), objArr2);
                obj = objArr2[0];
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                m("\uf108\uf16f蠇ⰾ⪐榮⋡窯쩰ꛒﷵ뺂\ue6fd㋕焫쩯切츬앮䘲\ude4d婡奩\uddf2䮁햩ⲇ榽", KeyEvent.normalizeMetaState(0), objArr4);
                obj = objArr4[0];
                break;
        }
        g.d(intern, ((String) obj).intern());
        interfaceC0042a.e((a.InterfaceC0042a<a.b>) a.b.c);
        int i3 = n + Opcodes.LUSHR;
        k = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                int i4 = 47 / 0;
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.ep.a
    public final void d(o.ep.a.InterfaceC0042a<java.util.List<o.ep.e>> r15) {
        /*
            Method dump skipped, instructions count: 358
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.c.d(o.ep.a$a):void");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void a(a.c cVar, int i2, Intent intent) {
        int i3 = k + Opcodes.DDIV;
        n = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0 ? 'F' : ')') {
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                throw null;
            default:
                if (i2 == 0) {
                    cVar.d(new o.bv.c(AntelopErrorCode.UserCancelled));
                    return;
                }
                switch (i2 == -1) {
                    case true:
                        switch (intent != null ? '(' : Typography.less) {
                            case '<':
                                break;
                            default:
                                Object[] objArr = new Object[1];
                                m("෦ඃ६幓ꯦᯃ嵦虏뗂➉辖섯ᨅ뎛͂뗖껡佒뜀㦇⊣\udb10⬈ꉝ띪", ViewConfiguration.getScrollBarSize() >> 8, objArr);
                                if (intent.hasExtra(((String) objArr[0]).intern())) {
                                    Object[] objArr2 = new Object[1];
                                    m("෦ඃ६幓ꯦᯃ嵦虏뗂➉辖섯ᨅ뎛͂뗖껡佒뜀㦇⊣\udb10⬈ꉝ띪", View.combineMeasuredStates(0, 0), objArr2);
                                    String stringExtra = intent.getStringExtra(((String) objArr2[0]).intern());
                                    g.c();
                                    Object[] objArr3 = new Object[1];
                                    m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", ViewConfiguration.getTapTimeout() >> 16, objArr3);
                                    String intern = ((String) objArr3[0]).intern();
                                    StringBuilder sb = new StringBuilder();
                                    Object[] objArr4 = new Object[1];
                                    l((byte) ((-80) - View.MeasureSpec.getMode(0)), (ViewConfiguration.getPressedStateDuration() >> 16) + 1666122147, (short) KeyEvent.keyCodeFromString(""), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 37, (-878860953) - View.resolveSize(0, 0), objArr4);
                                    g.d(intern, sb.append(((String) objArr4[0]).intern()).append(stringExtra).toString());
                                    cVar.a(stringExtra);
                                    int i4 = n + 91;
                                    k = i4 % 128;
                                    if (i4 % 2 == 0) {
                                        return;
                                    }
                                    obj.hashCode();
                                    throw null;
                                }
                                break;
                        }
                        g.c();
                        Object[] objArr5 = new Object[1];
                        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", ExpandableListView.getPackedPositionType(0L), objArr5);
                        String intern2 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        l((byte) (110 - View.resolveSizeAndState(0, 0, 0)), 1666122074 - TextUtils.lastIndexOf("", '0', 0), (short) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (-4) - View.MeasureSpec.getSize(0), View.MeasureSpec.getMode(0) - 878860953, objArr6);
                        g.e(intern2, ((String) objArr6[0]).intern());
                        cVar.d(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
                        return;
                    default:
                        g.c();
                        Object[] objArr7 = new Object[1];
                        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", View.MeasureSpec.getMode(0), objArr7);
                        String intern3 = ((String) objArr7[0]).intern();
                        Object[] objArr8 = new Object[1];
                        l((byte) (TextUtils.lastIndexOf("", '0', 0) + 29), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1666122184, (short) (KeyEvent.getMaxKeyCode() >> 16), MotionEvent.axisFromString("") - 8, (-878860953) - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr8);
                        g.e(intern3, ((String) objArr8[0]).intern());
                        cVar.d(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable));
                        int i5 = k + 27;
                        n = i5 % 128;
                        int i6 = i5 % 2;
                        return;
                }
        }
    }

    private static void a(Activity activity, final a.c cVar, i iVar, e eVar) {
        int i2 = n + 85;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", (-1) - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) (11 - (ViewConfiguration.getKeyRepeatTimeout() >> 16)), Color.green(0) + 1666121832, (short) ((-1) - MotionEvent.axisFromString("")), (-67) - TextUtils.indexOf("", ""), View.MeasureSpec.makeMeasureSpec(0, 0) - 878860953, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        switch (iVar != null) {
            case true:
                iVar.e(50, new i.a() { // from class: o.eq.c$$ExternalSyntheticLambda0
                    @Override // o.ee.i.a
                    public final void onActivityResult(int i4, Intent intent) {
                        c.a(a.c.this, i4, intent);
                    }
                });
                Intent intent = new Intent(activity, (Class<?>) DeviceWalletMockActivity.class);
                Object[] objArr3 = new Object[1];
                m("㥡㤠\uf08b狝刽㝽\ueaf7", 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr3);
                Intent action = intent.setAction(((String) objArr3[0]).intern());
                Object[] objArr4 = new Object[1];
                m("㟁㞍皙痌퐪ほ鎛뱖笙塧ꐩ࿀‘챏⣫", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, objArr4);
                activity.startActivityForResult(action.putExtra(((String) objArr4[0]).intern(), eVar.s().a()), 50);
                int i4 = n + 19;
                k = i4 % 128;
                int i5 = i4 % 2;
                break;
            default:
                g.c();
                Object[] objArr5 = new Object[1];
                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", ((byte) KeyEvent.getModifierMetaStateMask()) + 1, objArr5);
                String intern2 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                m("甛畫䧶綆\ueb71㠑ꤪﺐ䆔札걘㕮抣\uf35d⃒䇟홣\u0fdd铙췎婒鮒࣫嘖쾊ᑾ累\ue259㏞ꀸ\uf166湑꜂㳰斶視⬺䢧\ud9c9ڢ齺앿削錸®充왇Ὓ瓱\ued19㪞", ViewConfiguration.getWindowTouchSlop() >> 8, objArr6);
                g.e(intern2, ((String) objArr6[0]).intern());
                break;
        }
    }

    @Override // o.ep.a
    public final void a(Activity activity, a.InterfaceC0042a<Object> interfaceC0042a, i iVar) {
        int i2 = n + 95;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", Color.rgb(0, 0, 0) + 16777216, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) (112 - View.resolveSize(0, 0)), 1666121841 + (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getMaximumFlingVelocity() >> 16) - 62, (-878860966) + (ViewConfiguration.getEdgeSlop() >> 16), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = k + 93;
        n = i4 % 128;
        switch (i4 % 2 == 0 ? '%' : '1') {
            case '1':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ep.a
    public final void c(Activity activity) {
        int i2 = n + 67;
        k = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", TextUtils.lastIndexOf("", '0', 0, 0) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) (113 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), TextUtils.lastIndexOf("", '0', 0, 0) + 1666121842, (short) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (-62) - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (-878860967) - TextUtils.lastIndexOf("", '0'), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = n + 13;
        k = i4 % 128;
        switch (i4 % 2 != 0 ? '=' : (char) 14) {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                throw null;
            default:
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void a(int i2, Intent intent) {
        int i3 = k + 17;
        int i4 = i3 % 128;
        n = i4;
        int i5 = i3 % 2;
        switch (i2 == -1 ? '9' : (char) 25) {
            case '9':
                int i6 = i4 + 1;
                k = i6 % 128;
                int i7 = i6 % 2;
                switch (intent == null) {
                    case false:
                        Object[] objArr = new Object[1];
                        m("ⶌⷨࡍデ\uaada畅价\ua631Ꙁ⚛\ue11f튁㩨늠淘ꙃ躺乘\ud992⨊", ((byte) KeyEvent.getModifierMetaStateMask()) + 1, objArr);
                        switch (!intent.hasExtra(((String) objArr[0]).intern()) ? 'H' : '/') {
                            case 'H':
                                break;
                            default:
                                Object[] objArr2 = new Object[1];
                                m("ⶌⷨࡍデ\uaada畅价\ua631Ꙁ⚛\ue11f튁㩨늠淘ꙃ躺乘\ud992⨊", 1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr2);
                                String stringExtra = intent.getStringExtra(((String) objArr2[0]).intern());
                                g.c();
                                Object[] objArr3 = new Object[1];
                                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
                                String intern = ((String) objArr3[0]).intern();
                                StringBuilder sb = new StringBuilder();
                                Object[] objArr4 = new Object[1];
                                l((byte) (Color.rgb(0, 0, 0) + 16777196), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 1666121953, (short) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (-30) - (KeyEvent.getMaxKeyCode() >> 16), TextUtils.lastIndexOf("", '0') - 878860949, objArr4);
                                g.d(intern, sb.append(((String) objArr4[0]).intern()).append(stringExtra).toString());
                                Context context = this.c;
                                Object[] objArr5 = new Object[1];
                                l((byte) (Color.blue(0) + Opcodes.FDIV), View.MeasureSpec.makeMeasureSpec(0, 0) + 1666121707, (short) ((-1) - TextUtils.lastIndexOf("", '0')), TextUtils.getTrimmedLength("") - 30, (-878860962) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr5);
                                SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr5[0]).intern(), 0).edit();
                                Object[] objArr6 = new Object[1];
                                m("ⶌⷨࡍデ\uaada畅价\ua631Ꙁ⚛\ue11f튁㩨늠淘ꙃ躺乘\ud992⨊", Process.myPid() >> 22, objArr6);
                                edit.putString(((String) objArr6[0]).intern(), stringExtra).commit();
                                break;
                        }
                }
                g.c();
                Object[] objArr7 = new Object[1];
                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", View.combineMeasuredStates(0, 0), objArr7);
                String intern2 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                l((byte) ((-65) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), 1666121881 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (short) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), View.resolveSizeAndState(0, 0, 0) - 3, View.combineMeasuredStates(0, 0) - 878860950, objArr8);
                g.e(intern2, ((String) objArr8[0]).intern());
                break;
            default:
                g.c();
                Object[] objArr9 = new Object[1];
                m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", View.combineMeasuredStates(0, 0), objArr9);
                String intern3 = ((String) objArr9[0]).intern();
                Object[] objArr10 = new Object[1];
                l((byte) (14 - (ViewConfiguration.getPressedStateDuration() >> 16)), 1666122000 - (KeyEvent.getMaxKeyCode() >> 16), (short) (ViewConfiguration.getKeyRepeatTimeout() >> 16), KeyEvent.normalizeMetaState(0) - 1, (-878860949) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr10);
                g.e(intern3, ((String) objArr10[0]).intern());
                break;
        }
    }

    @Override // o.ep.c
    public final void a(Activity activity, o.ep.e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", Color.alpha(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m("햽헎럚༟ᕍ䪏\uee15帆ڂ餤\uded6牺쉌ഭ切ڐ皦\uf1f1\ue651諈", ViewConfiguration.getWindowTouchSlop() >> 8, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        new i().e(53, new i.a() { // from class: o.eq.c$$ExternalSyntheticLambda1
            @Override // o.ee.i.a
            public final void onActivityResult(int i2, Intent intent) {
                c.this.a(i2, intent);
            }
        });
        Intent intent = new Intent(activity, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr3 = new Object[1];
        m("覲觡酉ꏢ㏾\ue652蓼Ⱦ汵뾶爈ᢪ鹷⮇ﻂ", TextUtils.indexOf("", "", 0), objArr3);
        Intent action = intent.setAction(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        l((byte) (TextUtils.lastIndexOf("", '0', 0) - 60), 1666121856 + ExpandableListView.getPackedPositionChild(0L), (short) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), TextUtils.lastIndexOf("", '0', 0) - 56, TextUtils.indexOf("", "", 0, 0) - 878860997, objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        m("丽乺偞ꌐ\uf2e3\ue6bbᯝ얹\uf34c纡狣螂姬\uea85", (-1) - TextUtils.lastIndexOf("", '0'), objArr5);
        activity.startActivityForResult(action.putExtra(intern2, ((String) objArr5[0]).intern()), 53);
        int i2 = k + 75;
        n = i2 % 128;
        switch (i2 % 2 == 0 ? 'P' : Typography.quote) {
            case '\"':
                return;
            default:
                int i3 = 97 / 0;
                return;
        }
    }

    @Override // o.ep.c
    public final void d(Activity activity, o.ep.e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", 1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        m("䟹䞝犤\ue19a퀳ꐒ쭟책⏬屻ふ圓倛졂", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Intent intent = new Intent(activity, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr3 = new Object[1];
        l((byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) - 61), 1666121855 - (ViewConfiguration.getScrollBarSize() >> 8), (short) (TextUtils.indexOf((CharSequence) "", '0') + 1), (-56) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (-878860997) - (ViewConfiguration.getTouchSlop() >> 8), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        m("丽乺偞ꌐ\uf2e3\ue6bbᯝ얹\uf34c纡狣螂姬\uea85", View.resolveSizeAndState(0, 0, 0), objArr4);
        activity.startActivityForResult(intent.putExtra(intern2, ((String) objArr4[0]).intern()), 54);
        int i2 = k + 17;
        n = i2 % 128;
        switch (i2 % 2 != 0 ? '!' : '6') {
            case '!':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.ep.c
    public final void c(Activity activity, o.ep.e eVar) {
        g.c();
        Object[] objArr = new Object[1];
        m("㲎㳃헝㱖着科\uda1b뜣㊦ﬃ\uedbd䙗⭱漸慎㊥龆鏶픓뻟Ꮟޣ䤾┵蘒衎㳷酨穒", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        l((byte) (7 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 1666121874 - Gravity.getAbsoluteGravity(0, 0), (short) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), ExpandableListView.getPackedPositionType(0L) - 69, (-878860951) + (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Intent intent = new Intent(activity, (Class<?>) DeviceWalletMockActivity.class);
        Object[] objArr3 = new Object[1];
        l((byte) ((-61) - Gravity.getAbsoluteGravity(0, 0)), 1666121855 + (ViewConfiguration.getMaximumFlingVelocity() >> 16), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), (Process.myTid() >> 22) - 57, (-878860997) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        m("丽乺偞ꌐ\uf2e3\ue6bbᯝ얹\uf34c纡狣螂姬\uea85", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, objArr4);
        activity.startActivity(intent.putExtra(intern2, ((String) objArr4[0]).intern()));
        int i2 = n + Opcodes.DSUB;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int i3 = 73 / 0;
                return;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:123:0x029d, code lost:
    
        r4 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:125:0x029b, code lost:
    
        if (r4 != false) goto L76;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x0289, code lost:
    
        if (r4 != false) goto L76;
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x029f, code lost:
    
        r4 = 0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1016
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.c.l(byte, int, short, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r13v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r13v1 */
    /* JADX WARN: Type inference failed for: r13v6, types: [char[]] */
    private static void m(java.lang.String r13, int r14, java.lang.Object[] r15) {
        /*
            Method dump skipped, instructions count: 384
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.c.m(java.lang.String, int, java.lang.Object[]):void");
    }
}
