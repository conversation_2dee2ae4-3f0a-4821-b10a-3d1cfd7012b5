package com.esotericsoftware.kryo.unsafe;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.ByteBufferOutput;
import java.io.OutputStream;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import sun.misc.Unsafe;
import sun.nio.ch.DirectBuffer;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\unsafe\UnsafeByteBufferOutput.smali */
public class UnsafeByteBufferOutput extends ByteBufferOutput {
    private long bufferAddress;

    public UnsafeByteBufferOutput() {
    }

    public UnsafeByteBufferOutput(int bufferSize) {
        super(bufferSize);
        updateBufferAddress();
    }

    public UnsafeByteBufferOutput(int bufferSize, int maxBufferSize) {
        super(bufferSize, maxBufferSize);
        updateBufferAddress();
    }

    public UnsafeByteBufferOutput(OutputStream outputStream) {
        super(outputStream);
        updateBufferAddress();
    }

    public UnsafeByteBufferOutput(OutputStream outputStream, int bufferSize) {
        super(outputStream, bufferSize);
        updateBufferAddress();
    }

    public UnsafeByteBufferOutput(long address, int size) {
        super(UnsafeUtil.newDirectBuffer(address, size));
        updateBufferAddress();
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput
    public void setBuffer(ByteBuffer buffer, int maxBufferSize) {
        if (!(buffer instanceof DirectBuffer)) {
            throw new IllegalArgumentException("buffer must be direct.");
        }
        if (buffer != this.byteBuffer) {
            UnsafeUtil.dispose(this.byteBuffer);
        }
        super.setBuffer(buffer, maxBufferSize);
        updateBufferAddress();
    }

    private void updateBufferAddress() {
        this.bufferAddress = this.byteBuffer.address();
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    protected boolean require(int required) throws KryoException {
        ByteBuffer oldBuffer = this.byteBuffer;
        boolean result = super.require(required);
        if (this.byteBuffer != oldBuffer) {
            UnsafeUtil.dispose(oldBuffer);
            updateBufferAddress();
        }
        return result;
    }

    public void dispose() {
        UnsafeUtil.dispose(this.byteBuffer);
        this.byteBuffer = null;
        this.bufferAddress = 0L;
    }

    private void setBufferPosition(Buffer buffer, int position) {
        buffer.position(position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output, java.io.OutputStream
    public void write(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(j + i, (byte) value);
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeByte(byte value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(j + i, value);
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeByte(int value) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(j + i, (byte) value);
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeInt(int value) throws KryoException {
        require(4);
        UnsafeUtil.unsafe.putInt(this.bufferAddress + this.position, value);
        this.position += 4;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeLong(long value) throws KryoException {
        require(8);
        UnsafeUtil.unsafe.putLong(this.bufferAddress + this.position, value);
        this.position += 8;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeFloat(float value) throws KryoException {
        require(4);
        UnsafeUtil.unsafe.putFloat(this.bufferAddress + this.position, value);
        this.position += 4;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeDouble(double value) throws KryoException {
        require(8);
        UnsafeUtil.unsafe.putDouble(this.bufferAddress + this.position, value);
        this.position += 8;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeShort(int value) throws KryoException {
        require(2);
        UnsafeUtil.unsafe.putShort(this.bufferAddress + this.position, (short) value);
        this.position += 2;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeChar(char value) throws KryoException {
        require(2);
        UnsafeUtil.unsafe.putChar(this.bufferAddress + this.position, value);
        this.position += 2;
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeBoolean(boolean z) throws KryoException {
        if (this.position == this.capacity) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        unsafe.putByte(j + i, z ? (byte) 1 : (byte) 0);
        setBufferPosition(this.byteBuffer, this.position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeInts(int[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.intArrayBaseOffset, array.length << 2);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeLongs(long[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.longArrayBaseOffset, array.length << 3);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeFloats(float[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.floatArrayBaseOffset, array.length << 2);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeDoubles(double[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.doubleArrayBaseOffset, array.length << 3);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeShorts(short[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.shortArrayBaseOffset, array.length << 1);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeChars(char[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.charArrayBaseOffset, array.length << 1);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeBooleans(boolean[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.booleanArrayBaseOffset, array.length);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferOutput, com.esotericsoftware.kryo.io.Output
    public void writeBytes(byte[] array, int offset, int count) throws KryoException {
        writeBytes(array, UnsafeUtil.byteArrayBaseOffset + offset, count);
    }

    public void writeBytes(Object from, long offset, int count) throws KryoException {
        int copyCount = Math.min(this.capacity - this.position, count);
        while (true) {
            UnsafeUtil.unsafe.copyMemory(from, offset, (Object) null, this.position + this.bufferAddress, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(this.capacity, count);
                require(copyCount);
            } else {
                setBufferPosition(this.byteBuffer, this.position);
                return;
            }
        }
    }
}
