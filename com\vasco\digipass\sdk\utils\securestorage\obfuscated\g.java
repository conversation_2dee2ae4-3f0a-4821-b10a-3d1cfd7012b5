package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import android.content.Context;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKErrorCodes;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import kotlin.io.CloseableKt;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\g.smali */
public abstract class g {
    public static final String a;

    static {
        byte[] bArr = new byte[4];
        int[] iArr = {86, 68, 83, 95};
        for (int i = 0; i < 4; i++) {
            bArr[i] = (byte) iArr[i];
        }
        a = new String(bArr);
    }

    public static final boolean a(String str, Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        File[] listFiles = context.getNoBackupFilesDir().listFiles();
        if (listFiles != null) {
            if (!(listFiles.length == 0)) {
                for (File file : listFiles) {
                    String a2 = a(str);
                    if (file.exists() && Intrinsics.areEqual(file.getName(), a2)) {
                        long length = file.length();
                        String str2 = x.a;
                        if (length >= x.c.length()) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public static final boolean b(String str, Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        String[] list = context.getNoBackupFilesDir().list();
        if (list != null) {
            if (!(list.length == 0)) {
                for (String str2 : list) {
                    if (Intrinsics.areEqual(str2, a(str))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static final String c(String str, Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        byte[] bArr = new byte[1024];
        try {
            FileInputStream fileInputStream = new FileInputStream(new File(context.getNoBackupFilesDir(), a(str)));
            while (true) {
                try {
                    int read = fileInputStream.read(bArr);
                    if (read == -1) {
                        byte[] byteArray = byteArrayOutputStream.toByteArray();
                        Intrinsics.checkNotNullExpressionValue(byteArray, "bos.toByteArray()");
                        String str2 = new String(byteArray, Charsets.UTF_8);
                        CloseableKt.closeFinally(fileInputStream, null);
                        return str2;
                    }
                    byteArrayOutputStream.write(bArr, 0, read);
                } finally {
                }
            }
        } catch (Exception e) {
            throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.UNREADABLE_STORAGE, e);
        }
    }

    public static final String a(String str) {
        return a + str;
    }
}
