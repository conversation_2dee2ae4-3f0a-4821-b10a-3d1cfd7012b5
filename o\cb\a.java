package o.cb;

import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerCredentialsRequiredReason;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cb\a.smali */
public final class a implements o.ee.a<CustomerCredentialsRequiredReason> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final a a;
    public static final a b;
    private static final /* synthetic */ a[] c;
    public static final a d;
    private static char[] e;
    private static long f;
    private static int g;
    private static int i;

    static void b() {
        e = new char[]{11420, 43636, 8458, 47304, 14325, 36531, 1112, 33656, 6708, 37318, 26839, 59278, 32076, 62482, 29494, 51953, 16835, 55530, 14387, 48849, 13747, 44141, 9049, 39425, 4342, 38876, 3721, 34167, 31805, 62263, 27125, 57515, 26515, 56905, 21793, 52231, 17103, 14774, 45154, 11402, 43624, 8458, 47316, 14304, 36536, 1103, 33637, 6704, 37326, 26756, 59292, 32072, 62488, 29482, 51962, 16784, 55486, 22134, 11535, 42203, 20487, 55013, 23943, 50265, 19309, 62005, 30914, 65512, 26301, 60739, 5129, 39702, 456, 34962, 4000, 46718, 15633};
        f = 6078598886124005914L;
    }

    static void init$0() {
        $$a = new byte[]{8, 72, -108, -33};
        $$b = Opcodes.IF_ICMPLE;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(short r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 102
            int r6 = r6 + 4
            byte[] r0 = o.cb.a.$$a
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L31
        L17:
            r3 = r2
        L18:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L31:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cb.a.j(short, short, byte, java.lang.Object[]):void");
    }

    private a(String str, int i2) {
    }

    private static /* synthetic */ a[] c() {
        int i2 = g + 3;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return new a[]{d, a, b};
            default:
                a[] aVarArr = new a[5];
                aVarArr[0] = d;
                aVarArr[0] = a;
                aVarArr[2] = b;
                return aVarArr;
        }
    }

    public static a valueOf(String str) {
        int i2 = g + Opcodes.DSUB;
        i = i2 % 128;
        int i3 = i2 % 2;
        a aVar = (a) Enum.valueOf(a.class, str);
        int i4 = i + 31;
        g = i4 % 128;
        int i5 = i4 % 2;
        return aVar;
    }

    public static a[] values() {
        int i2 = g + 47;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                a[] aVarArr = (a[]) c.clone();
                int i3 = g + Opcodes.LMUL;
                i = i3 % 128;
                int i4 = i3 % 2;
                return aVarArr;
            default:
                throw null;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i2 = i + 67;
        g = i2 % 128;
        int i3 = i2 % 2;
        CustomerCredentialsRequiredReason d2 = d();
        int i4 = g + 59;
        i = i4 % 128;
        int i5 = i4 % 2;
        return d2;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        g = 1;
        b();
        Object[] objArr = new Object[1];
        h((char) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 5305), AndroidCharacter.getMirror('0') - 30, 21 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        d = new a(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        h((char) Color.alpha(0), 38 - ExpandableListView.getPackedPositionChild(0L), 21 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr2);
        a = new a(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        h((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 31885), 61 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getTouchSlop() >> 8) + 17, objArr3);
        b = new a(((String) objArr3[0]).intern(), 2);
        c = c();
        int i2 = i + 25;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    /* renamed from: o.cb.a$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cb\a$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        static final /* synthetic */ int[] c;
        private static int e;

        static {
            e = 0;
            b = 1;
            int[] iArr = new int[a.values().length];
            c = iArr;
            try {
                iArr[a.d.ordinal()] = 1;
                int i = (b + 90) - 1;
                e = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[a.a.ordinal()] = 2;
                int i2 = e + 45;
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[a.b.ordinal()] = 3;
                int i3 = e + 13;
                b = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    public final CustomerCredentialsRequiredReason d() {
        int i2 = g + 79;
        i = i2 % 128;
        int i3 = i2 % 2;
        switch (AnonymousClass4.c[ordinal()]) {
            case 1:
                CustomerCredentialsRequiredReason customerCredentialsRequiredReason = CustomerCredentialsRequiredReason.ValidationNeeded;
                int i4 = g + 21;
                i = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 19 : ')') {
                    case ')':
                        return customerCredentialsRequiredReason;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            case 2:
                return CustomerCredentialsRequiredReason.NotSet;
            case 3:
                return CustomerCredentialsRequiredReason.ToBeChanged;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                h((char) TextUtils.getOffsetAfter("", 0), (Process.getThreadPriority(0) + 20) >> 6, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 18, objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1164
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cb.a.h(char, int, int, java.lang.Object[]):void");
    }
}
