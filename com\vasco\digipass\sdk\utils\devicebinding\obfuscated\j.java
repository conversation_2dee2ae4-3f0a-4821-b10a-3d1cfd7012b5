package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.provider.Settings;
import androidx.core.content.ContextCompat;
import androidx.core.os.EnvironmentCompat;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.nio.charset.Charset;
import java.util.Locale;
import java.util.UUID;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\f\u0010\rJ\u0012\u0010\u0005\u001a\u0004\u0018\u00010\u00042\u0006\u0010\u0003\u001a\u00020\u0002H\u0003J\u0010\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0003\u001a\u00020\u0002H\u0002J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\t\u001a\u00020\bH\u0002J\b\u0010\u0007\u001a\u00020\u0004H\u0002J\u0010\u0010\u000b\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0004H\u0016J\u0006\u0010\u0005\u001a\u00020\u0004¨\u0006\u000e"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/j;", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding;", "Landroid/content/Context;", "context", "", "b", "", "a", "Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/o;", "storage", "salt", "fingerprint", "<init>", "(Landroid/content/Context;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\j.smali */
public final class j implements DeviceBinding {
    private final Context a;

    public j(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        this.a = context;
    }

    private final void a(Context context) throws DeviceBindingSDKException {
        if (ContextCompat.checkSelfPermission(context, "android.permission.READ_PHONE_STATE") != 0) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.PERMISSION_DENIED, null, 2, null);
        }
    }

    public final String b() throws DeviceBindingSDKException {
        String b;
        String a = a();
        SharedPreferences sharedPreferences = this.a.getSharedPreferences(o.g.a(), 0);
        Intrinsics.checkNotNullExpressionValue(sharedPreferences, "sharedPreferences");
        o oVar = new o(sharedPreferences, a);
        if (Build.VERSION.SDK_INT > 28) {
            b = a(oVar);
        } else {
            b = b(this.a);
            if (b == null || Intrinsics.areEqual(b, EnvironmentCompat.MEDIA_UNKNOWN)) {
                b = a(oVar);
            }
        }
        oVar.c(b);
        Locale US = Locale.US;
        Intrinsics.checkNotNullExpressionValue(US, "US");
        String upperCase = b.toUpperCase(US);
        Intrinsics.checkNotNullExpressionValue(upperCase, "this as java.lang.String).toUpperCase(locale)");
        return upperCase;
    }

    @Override // com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding
    public String fingerprint(String salt) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        try {
            if (salt.length() == 0) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_NULL, null, 2, null);
            }
            if (salt.length() < 64) {
                throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_TOO_SHORT, null, 2, null);
            }
            String str = b() + salt + n.a.a();
            Charset forName = Charset.forName("UTF-8");
            Intrinsics.checkNotNullExpressionValue(forName, "forName(charsetName)");
            byte[] bytes = str.getBytes(forName);
            Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
            UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, bytes);
            if (hash.getReturnCode() == 0) {
                return p.a(hash.getOutputData());
            }
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
        } catch (DeviceBindingSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, e2);
        }
    }

    private final String a(o storage) throws DeviceBindingSDKException {
        String c = storage.c();
        if (c != null) {
            return c;
        }
        String uuid = UUID.randomUUID().toString();
        Intrinsics.checkNotNullExpressionValue(uuid, "randomUUID().toString()");
        return uuid;
    }

    private final String a() {
        String string = Settings.Secure.getString(this.a.getContentResolver(), n.a.b());
        Intrinsics.checkNotNullExpressionValue(string, "getString(\n            c…ingsSecureKey()\n        )");
        return string;
    }

    private final String b(Context context) throws DeviceBindingSDKException {
        try {
            return Build.getSerial();
        } catch (SecurityException e) {
            a(context);
            return null;
        }
    }
}
