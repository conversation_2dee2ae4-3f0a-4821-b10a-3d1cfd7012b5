package o.dz;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextThemeWrapper;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import android.widget.GridLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.core.widget.TextViewCompat;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import kotlin.text.Typography;
import o.ee.k;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dz\a.smali */
public abstract class a extends LinearLayout {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    static final char[] NUM_ARRAY;
    private static long a;
    static final String[] alphaArray;
    private static int c;
    private static int e;
    private View backButtonView;
    private b[] buttonArray;
    private InterfaceC0038a defaultKeypadCallback;
    private View extraButtonView;
    private GridLayout grid;
    private k obscuredWindowOnTouchListener;
    private int[] scramble;
    private d theming;

    /* renamed from: o.dz.a$a, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dz\a$a.smali */
    public interface InterfaceC0038a {
        void b();

        void c();

        void c(char c);
    }

    static void c() {
        a = -7776931228291280202L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = r6 + 68
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r8 = r8 + 4
            byte[] r0 = o.dz.a.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L37
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            int r8 = r8 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dz.a.f(int, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{7, -109, -85, 32};
        $$b = Opcodes.DRETURN;
    }

    public abstract void customizeView();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        c = 1;
        c();
        Object[] objArr = new Object[1];
        d("뻥뺤쵈\udf63鶑뤀뫹", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        d("▹◽䲓庿\uee95쨁뮢", 1 - ExpandableListView.getPackedPositionGroup(0L), objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        d("㋛㊜悋犪䍻柠⩕", 1 - KeyEvent.normalizeMetaState(0), objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        d("볃벉楲筐댉鞗띥", 1 - Color.green(0), objArr4);
        String intern4 = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        d("祩礤ៗװ\u2d7eৣ廟", -TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr5);
        String intern5 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        d("ퟄ힔웨퓐㊺ᘺ䇉皡", ExpandableListView.getPackedPositionGroup(0L) + 1, objArr6);
        String intern6 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        d("牍爙\udf75쵉锚놞\udbd2", 1 - (Process.myPid() >> 22), objArr7);
        String intern7 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        d("ꂾꃩ嘬䐝卵\udd97啓戲", (ViewConfiguration.getTapTimeout() >> 16) + 1, objArr8);
        alphaArray = new String[]{"", "", intern, intern2, intern3, intern4, intern5, intern6, intern7, ((String) objArr8[0]).intern()};
        NUM_ARRAY = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        int i = c + Opcodes.LSHR;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dz\a$d.smali */
    public static class d {
        public final int alphaStyle;
        public final int backgroundStyle;
        final int deleteButtonDrawable;
        public final int digitsStyle;
        public final boolean displayAlpha;
        final int extraButtonDrawable;

        public d(int i, int i2, int i3, boolean z, int i4, int i5) {
            this.digitsStyle = i;
            this.alphaStyle = i2;
            this.backgroundStyle = i3;
            this.displayAlpha = z;
            this.deleteButtonDrawable = i4;
            this.extraButtonDrawable = i5;
        }
    }

    public a(Context context) {
        super(context);
        this.scramble = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    }

    public a(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.scramble = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    }

    public a(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.scramble = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
    }

    public void initializeView(InterfaceC0038a interfaceC0038a, d dVar) {
        int i = e + 7;
        c = i % 128;
        switch (i % 2 != 0) {
            case false:
                this.theming = dVar;
                this.defaultKeypadCallback = interfaceC0038a;
                generateView();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.theming = dVar;
                this.defaultKeypadCallback = interfaceC0038a;
                generateView();
                int i2 = e + 35;
                c = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    b[] getButtonArray() {
        int i = c + 87;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        b[] bVarArr = this.buttonArray;
        int i4 = i2 + 57;
        c = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return bVarArr;
            default:
                int i5 = 3 / 0;
                return bVarArr;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x001f, code lost:
    
        if (r4.length != 10) goto L10;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void scramble(int[] r4) {
        /*
            r3 = this;
            int r0 = o.dz.a.e
            int r1 = r0 + 37
            int r2 = r1 % 128
            o.dz.a.c = r2
            int r1 = r1 % 2
            if (r4 == 0) goto Lf
            r1 = 94
            goto L11
        Lf:
            r1 = 48
        L11:
            r2 = 10
            switch(r1) {
                case 48: goto L21;
                default: goto L16;
            }
        L16:
            int r0 = r0 + 69
            int r1 = r0 % 128
            o.dz.a.c = r1
            int r0 = r0 % 2
            int r0 = r4.length
            if (r0 == r2) goto L26
        L21:
            int[] r4 = getNewScrambledVector()
        L26:
            r3.scramble = r4
            r4 = 0
        L29:
            if (r4 >= r2) goto L2e
            r0 = 14
            goto L30
        L2e:
            r0 = 37
        L30:
            switch(r0) {
                case 37: goto L41;
                default: goto L33;
            }
        L33:
            o.dz.a$b[] r0 = r3.buttonArray
            r0 = r0[r4]
            int[] r1 = r3.scramble
            r1 = r1[r4]
            r0.setIndex(r1)
            int r4 = r4 + 1
            goto L29
        L41:
            int r4 = o.dz.a.e
            int r4 = r4 + 113
            int r0 = r4 % 128
            o.dz.a.c = r0
            int r4 = r4 % 2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dz.a.scramble(int[]):void");
    }

    public void scramble() {
        int i = e + 23;
        c = i % 128;
        switch (i % 2 == 0) {
            case false:
                scramble(getNewScrambledVector());
                int i2 = e + 1;
                c = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                scramble(getNewScrambledVector());
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static int[] getNewScrambledVector() {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r1 = 10
            r0.<init>(r1)
            r2 = 0
            r3 = r2
        Lb:
            if (r3 >= r1) goto L10
            r4 = 53
            goto L12
        L10:
            r4 = 87
        L12:
            switch(r4) {
                case 87: goto L22;
                default: goto L15;
            }
        L15:
            int r4 = o.dz.a.e
            int r4 = r4 + 3
            int r5 = r4 % 128
            o.dz.a.c = r5
            int r4 = r4 % 2
            if (r4 != 0) goto L4a
            goto L4a
        L22:
            java.util.Collections.shuffle(r0)
            int[] r3 = new int[r1]
            r4 = r2
        L28:
            if (r4 >= r1) goto L2c
            r5 = r2
            goto L2d
        L2c:
            r5 = 1
        L2d:
            switch(r5) {
                case 0: goto L31;
                default: goto L30;
            }
        L30:
            return r3
        L31:
            int r5 = o.dz.a.c
            int r5 = r5 + 105
            int r6 = r5 % 128
            o.dz.a.e = r6
            int r5 = r5 % 2
            java.lang.Object r5 = r0.get(r4)
            java.lang.Integer r5 = (java.lang.Integer) r5
            int r5 = r5.intValue()
            r3[r4] = r5
            int r4 = r4 + 1
            goto L28
        L4a:
            java.lang.Integer r4 = java.lang.Integer.valueOf(r3)
            r0.add(r4)
            int r3 = r3 + 1
            goto Lb
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dz.a.getNewScrambledVector():int[]");
    }

    public void setExtraButtonVisible(boolean z) {
        View view = this.extraButtonView;
        int i = 0;
        switch (view != null) {
            case true:
                int i2 = c + 83;
                int i3 = i2 % 128;
                e = i3;
                int i4 = i2 % 2;
                switch (z) {
                    case true:
                        int i5 = i3 + 75;
                        c = i5 % 128;
                        int i6 = i5 % 2;
                        break;
                    default:
                        i = 4;
                        break;
                }
                view.setVisibility(i);
                int i7 = c + 13;
                e = i7 % 128;
                int i8 = i7 % 2;
                break;
        }
    }

    public void enableOverlayProtection(final String str) {
        setObscuredWindowOnTouchListener(new k() { // from class: o.dz.a.1
            private static int d = 0;
            private static int a = 1;

            /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
            @Override // o.ee.k
            public final void b(Context context) {
                int i = a + Opcodes.LREM;
                d = i % 128;
                switch (i % 2 != 0) {
                }
                Toast.makeText(context, str, 1).show();
                int i2 = a;
                int i3 = ((i2 | 11) << 1) - (i2 ^ 11);
                d = i3 % 128;
                int i4 = i3 % 2;
            }
        });
        int i = c + 21;
        e = i % 128;
        int i2 = i % 2;
    }

    public final void setObscuredWindowOnTouchListener(k kVar) {
        int i = c + 7;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        this.obscuredWindowOnTouchListener = kVar;
        int i4 = i2 + 35;
        c = i4 % 128;
        int i5 = i4 % 2;
    }

    /* renamed from: lambda$generateView$0$o-dz-a, reason: not valid java name */
    /* synthetic */ void m1817lambda$generateView$0$odza(View view) {
        int i = c + 7;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                this.defaultKeypadCallback.c(NUM_ARRAY[((b) view).e()]);
                int i2 = e + 71;
                c = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            default:
                this.defaultKeypadCallback.c(NUM_ARRAY[((b) view).e()]);
                throw null;
        }
    }

    /* renamed from: lambda$generateView$1$o-dz-a, reason: not valid java name */
    /* synthetic */ void m1818lambda$generateView$1$odza(View view) {
        int i = e + 21;
        c = i % 128;
        int i2 = i % 2;
        this.defaultKeypadCallback.c();
        int i3 = c + 37;
        e = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* renamed from: lambda$generateView$2$o-dz-a, reason: not valid java name */
    /* synthetic */ void m1819lambda$generateView$2$odza(View view) {
        int i = e + 53;
        c = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case true:
                InterfaceC0038a interfaceC0038a = this.defaultKeypadCallback;
                if (interfaceC0038a != null) {
                    interfaceC0038a.b();
                }
                int i2 = c + 29;
                e = i2 % 128;
                switch (i2 % 2 != 0 ? Typography.dollar : '.') {
                    case '$':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private void generateView() {
        /*
            Method dump skipped, instructions count: 350
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dz.a.generateView():void");
    }

    private View generateButtonView(int i, int i2) {
        switch (i == 0 ? (char) 6 : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                View inflate = View.inflate(new ContextThemeWrapper(getContext(), this.theming.backgroundStyle), R.layout.antelop_keypadview_imagebutton, null);
                switch (inflate instanceof ImageView ? false : true) {
                    case false:
                        ImageView imageView = (ImageView) inflate;
                        imageView.setImageResource(i);
                        imageView.setColorFilter(i2);
                        int i3 = c + 53;
                        e = i3 % 128;
                        int i4 = i3 % 2;
                        return inflate;
                    default:
                        return null;
                }
            default:
                int i5 = e + 95;
                c = i5 % 128;
                switch (i5 % 2 == 0 ? 'F' : '4') {
                    case '4':
                        return null;
                    default:
                        int i6 = 37 / 0;
                        return null;
                }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dz\a$b.smali */
    public static final class b extends LinearLayout {
        private int a;
        private final TextView d;
        final TextView e;
        private static int c = 0;
        private static int b = 1;

        public b(Context context, int i, d dVar) {
            super(context);
            this.a = i;
            View.inflate(new ContextThemeWrapper(context, dVar.backgroundStyle), R.layout.antelop_keypadview_digitbutton, this);
            RelativeLayout relativeLayout = (RelativeLayout) findViewById(R.id.container);
            TextView textView = new TextView(context, null);
            this.e = textView;
            TextViewCompat.setTextAppearance(textView, dVar.digitsStyle);
            RelativeLayout.LayoutParams layoutParams = new RelativeLayout.LayoutParams(-2, -2);
            layoutParams.addRule(dVar.displayAlpha ? 10 : 15);
            layoutParams.addRule(14);
            textView.setLayoutParams(layoutParams);
            textView.setText(a.NUM_ARRAY, i, 1);
            relativeLayout.addView(textView);
            if (dVar.displayAlpha) {
                TextView textView2 = new TextView(context, null);
                this.d = textView2;
                TextViewCompat.setTextAppearance(textView2, dVar.alphaStyle);
                RelativeLayout.LayoutParams layoutParams2 = new RelativeLayout.LayoutParams(-2, -2);
                layoutParams2.addRule(12);
                layoutParams2.addRule(14);
                textView2.setLayoutParams(layoutParams2);
                textView2.setText(a.alphaArray[i]);
                relativeLayout.addView(textView2);
                return;
            }
            this.d = null;
        }

        public final void setIndex(int i) {
            int i2 = b + Opcodes.DREM;
            c = i2 % 128;
            int i3 = i2 % 2;
            this.a = i;
            this.e.setText(a.NUM_ARRAY, i, 1);
            TextView textView = this.d;
            switch (textView != null) {
                case false:
                    break;
                default:
                    int i4 = c;
                    int i5 = (i4 ^ 85) + ((i4 & 85) << 1);
                    b = i5 % 128;
                    int i6 = i5 % 2;
                    textView.setText(a.alphaArray[i]);
                    int i7 = b;
                    int i8 = (i7 ^ 1) + ((i7 & 1) << 1);
                    c = i8 % 128;
                    int i9 = i8 % 2;
                    break;
            }
            int i10 = c;
            int i11 = ((i10 | Opcodes.DSUB) << 1) - (i10 ^ Opcodes.DSUB);
            b = i11 % 128;
            int i12 = i11 % 2;
        }

        public final int e() {
            int i = c;
            int i2 = (i & 99) + (i | 99);
            b = i2 % 128;
            switch (i2 % 2 == 0 ? 'S' : '5') {
                case Opcodes.SALOAD /* 53 */:
                    int i3 = this.a;
                    int i4 = ((i | 7) << 1) - (i ^ 7);
                    b = i4 % 128;
                    switch (i4 % 2 != 0) {
                        case true:
                            return i3;
                        default:
                            throw null;
                    }
                default:
                    throw null;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void d(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 396
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dz.a.d(java.lang.String, int, java.lang.Object[]):void");
    }
}
