package o.er;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.DigitalCardServiceStatus;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import fr.antelop.sdk.util.OperationCallback;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.eo.f;
import o.ep.a;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\d.smali */
public abstract class d<T extends o.ep.a<?>> extends h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        a = 1;
        b = 874635458;
    }

    static void init$0() {
        $$a = new byte[]{96, 104, -93, 9};
        $$b = 57;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = r6 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r8 = r8 * 2
            int r8 = 109 - r8
            byte[] r0 = o.er.d.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            goto L35
        L19:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1d:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L35:
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.d.l(short, short, int, java.lang.Object[]):void");
    }

    public abstract AntelopErrorCode a();

    public abstract String c();

    public abstract f.a d();

    public abstract String e();

    public abstract T e(Context context);

    @Override // o.er.h
    public final /* bridge */ /* synthetic */ boolean b() {
        int i = e + Opcodes.LSUB;
        a = i % 128;
        switch (i % 2 == 0) {
            case false:
                return super.b();
            default:
                int i2 = 2 / 0;
                return super.b();
        }
    }

    public d(o.eo.e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    public final void b(Context context, final OperationCallback<DigitalCardServiceStatus> operationCallback) {
        o.ee.g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        k(2 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), "\ufffa￼\b\n\t\ufff6\t￨\t", 8 - TextUtils.indexOf((CharSequence) "", '0', 0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 295, true, objArr);
        o.ee.g.d(e2, ((String) objArr[0]).intern());
        e(context).e(new a.InterfaceC0042a<a.b>() { // from class: o.er.d.2
            private static int $10 = 0;
            private static int $11 = 1;
            private static int i = 0;
            private static int g = 1;
            private static char e = 4850;
            private static char b = 22609;
            private static char f = 29453;
            private static char d = 59561;

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(a.b bVar) {
                int i2 = i + 25;
                g = i2 % 128;
                int i3 = i2 % 2;
                c(bVar);
                int i4 = g + 57;
                i = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        return;
                    default:
                        int i5 = 53 / 0;
                        return;
                }
            }

            private void c(a.b bVar) {
                o.ee.g.c();
                String e3 = d.this.e();
                StringBuilder sb = new StringBuilder();
                Object[] objArr2 = new Object[1];
                h("蕳骶\uf1faㆭᄀ豫콃惧儆䰶砹〫\uf778\u20fd뚐ᤗ\u087b㥱咫Ⲃ儆䰶砹〫䠖\udcd5蜫푞拼樨뻂\uedd7㯯\udc88", ExpandableListView.getPackedPositionType(0L) + 33, objArr2);
                o.ee.g.d(e3, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
                Object obj = null;
                switch (AnonymousClass10.a[bVar.ordinal()]) {
                    case 1:
                        if (d.this.b()) {
                            int i2 = g + 59;
                            i = i2 % 128;
                            int i3 = i2 % 2;
                            operationCallback.onSuccess(DigitalCardServiceStatus.Active);
                            return;
                        }
                        operationCallback.onSuccess(DigitalCardServiceStatus.Disabled);
                        int i4 = g + 67;
                        i = i4 % 128;
                        if (i4 % 2 == 0) {
                            return;
                        } else {
                            throw null;
                        }
                    case 2:
                        switch (d.this.b() ? false : true) {
                            case true:
                                operationCallback.onSuccess(DigitalCardServiceStatus.Disabled);
                                return;
                            default:
                                int i5 = i + 109;
                                g = i5 % 128;
                                switch (i5 % 2 == 0 ? '\r' : (char) 29) {
                                    case 29:
                                        operationCallback.onSuccess(DigitalCardServiceStatus.NotConfiguredByUser);
                                        return;
                                    default:
                                        operationCallback.onSuccess(DigitalCardServiceStatus.NotConfiguredByUser);
                                        obj.hashCode();
                                        throw null;
                                }
                        }
                    default:
                        operationCallback.onSuccess(DigitalCardServiceStatus.NotSupportedByDevice);
                        return;
                }
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                String e3;
                Object obj;
                int i2 = i + 75;
                g = i2 % 128;
                switch (i2 % 2 == 0 ? (char) 30 : '.') {
                    case 30:
                        o.ee.g.c();
                        e3 = d.this.e();
                        Object[] objArr2 = new Object[1];
                        h("蕳骶\uf1faㆭᄀ豫콃惧儆䰶砹〫\uf778\u20fdᑽ跢봦槅\ue23a鏧", 11 >>> (ViewConfiguration.getMaximumFlingVelocity() >> 3), objArr2);
                        obj = objArr2[0];
                        break;
                    default:
                        o.ee.g.c();
                        e3 = d.this.e();
                        Object[] objArr3 = new Object[1];
                        h("蕳骶\uf1faㆭᄀ豫콃惧儆䰶砹〫\uf778\u20fdᑽ跢봦槅\ue23a鏧", 19 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr3);
                        obj = objArr3[0];
                        break;
                }
                o.ee.g.d(e3, ((String) obj).intern());
                operationCallback.onError(cVar.d());
            }

            private static void h(String str, int i2, Object[] objArr2) {
                char[] charArray;
                int i3;
                switch (str != null ? '2' : 'P') {
                    case '2':
                        int i4 = $10 + 61;
                        $11 = i4 % 128;
                        if (i4 % 2 == 0) {
                            str.toCharArray();
                            throw null;
                        }
                        charArray = str.toCharArray();
                        break;
                    default:
                        charArray = str;
                        break;
                }
                char[] cArr = charArray;
                o.a.i iVar = new o.a.i();
                char[] cArr2 = new char[cArr.length];
                int i5 = 0;
                iVar.b = 0;
                char[] cArr3 = new char[2];
                while (iVar.b < cArr.length) {
                    int i6 = $10 + 83;
                    $11 = i6 % 128;
                    int i7 = 58224;
                    switch (i6 % 2 == 0 ? '7' : (char) 24) {
                        case '7':
                            cArr3[i5] = cArr[iVar.b];
                            cArr3[1] = cArr[iVar.b * i5];
                            i3 = 1;
                            break;
                        default:
                            cArr3[i5] = cArr[iVar.b];
                            cArr3[1] = cArr[iVar.b + 1];
                            i3 = i5;
                            break;
                    }
                    while (i3 < 16) {
                        char c = cArr3[1];
                        char c2 = cArr3[i5];
                        int i8 = (c2 + i7) ^ ((c2 << 4) + ((char) (b ^ 8439748517800462901L)));
                        int i9 = c2 >>> 5;
                        try {
                            Object[] objArr3 = new Object[4];
                            objArr3[3] = Integer.valueOf(f);
                            objArr3[2] = Integer.valueOf(i9);
                            objArr3[1] = Integer.valueOf(i8);
                            objArr3[i5] = Integer.valueOf(c);
                            Object obj = o.e.a.s.get(-1512468642);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0', i5) + 12, (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 603);
                                Class<?>[] clsArr = new Class[4];
                                clsArr[i5] = Integer.TYPE;
                                clsArr[1] = Integer.TYPE;
                                clsArr[2] = Integer.TYPE;
                                clsArr[3] = Integer.TYPE;
                                obj = cls.getMethod("C", clsArr);
                                o.e.a.s.put(-1512468642, obj);
                            }
                            char charValue = ((Character) ((Method) obj).invoke(null, objArr3)).charValue();
                            cArr3[1] = charValue;
                            char[] cArr4 = cArr3;
                            try {
                                Object[] objArr4 = {Integer.valueOf(cArr3[i5]), Integer.valueOf((charValue + i7) ^ ((charValue << 4) + ((char) (d ^ 8439748517800462901L)))), Integer.valueOf(charValue >>> 5), Integer.valueOf(e)};
                                Object obj2 = o.e.a.s.get(-1512468642);
                                if (obj2 == null) {
                                    obj2 = ((Class) o.e.a.c(11 - Gravity.getAbsoluteGravity(0, 0), (char) ExpandableListView.getPackedPositionType(0L), 604 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)))).getMethod("C", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-1512468642, obj2);
                                }
                                cArr4[0] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                i7 -= 40503;
                                i3++;
                                cArr3 = cArr4;
                                i5 = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    char[] cArr5 = cArr3;
                    cArr2[iVar.b] = cArr5[0];
                    cArr2[iVar.b + 1] = cArr5[1];
                    try {
                        Object[] objArr5 = {iVar, iVar};
                        Object obj3 = o.e.a.s.get(2062727845);
                        if (obj3 == null) {
                            obj3 = ((Class) o.e.a.c(Color.alpha(0) + 10, (char) (TextUtils.lastIndexOf("", '0', 0) + 30726), 614 - View.combineMeasuredStates(0, 0))).getMethod("A", Object.class, Object.class);
                            o.e.a.s.put(2062727845, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr5);
                        int i10 = $11 + 71;
                        $10 = i10 % 128;
                        int i11 = i10 % 2;
                        cArr3 = cArr5;
                        i5 = 0;
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                }
                objArr2[0] = new String(cArr2, 0, i2);
            }
        });
        int i = a + Opcodes.LSUB;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                int i2 = 0 / 0;
                return;
        }
    }

    /* renamed from: o.er.d$10, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\d$10.smali */
    static /* synthetic */ class AnonymousClass10 {
        static final /* synthetic */ int[] a;
        private static int c = 0;
        private static int e;

        static {
            e = 1;
            int[] iArr = new int[a.b.values().length];
            a = iArr;
            try {
                iArr[a.b.c.ordinal()] = 1;
                int i = c;
                int i2 = ((i | 11) << 1) - (i ^ 11);
                e = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[a.b.a.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[a.b.b.ordinal()] = 3;
                int i3 = c;
                int i4 = (i3 & 79) + (i3 | 79);
                e = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    @Deprecated
    public final AndroidActivityResultCallback d(final Activity activity, final OperationCallback<Void> operationCallback) {
        final o.ee.i iVar = new o.ee.i();
        e(activity).e(new a.InterfaceC0042a<a.b>() { // from class: o.er.d.1
            private static int d = 0;
            private static int f = 1;

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(a.b bVar) {
                int i = f;
                int i2 = ((i | 55) << 1) - (i ^ 55);
                d = i2 % 128;
                char c = i2 % 2 != 0 ? '%' : 'J';
                e2(bVar);
                switch (c) {
                    case '%':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        int i3 = d;
                        int i4 = (i3 & 1) + (i3 | 1);
                        f = i4 % 128;
                        switch (i4 % 2 == 0 ? '@' : (char) 7) {
                            case '@':
                                int i5 = 11 / 0;
                                return;
                            default:
                                return;
                        }
                }
            }

            /* renamed from: e, reason: avoid collision after fix types in other method */
            private void e2(a.b bVar) {
                int i = d;
                int i2 = (i ^ 83) + ((i & 83) << 1);
                f = i2 % 128;
                int i3 = i2 % 2;
                Object obj = null;
                switch (AnonymousClass10.a[bVar.ordinal()]) {
                    case 1:
                        operationCallback.onSuccess(null);
                        int i4 = (f + 4) - 1;
                        d = i4 % 128;
                        int i5 = i4 % 2;
                        return;
                    case 2:
                        d.this.e(activity).a(activity, new a.InterfaceC0042a<Object>() { // from class: o.er.d.1.4
                            private static int a = 0;
                            private static int e = 1;

                            @Override // o.ep.a.InterfaceC0042a
                            public final void e(Object obj2) {
                                int i6 = e;
                                int i7 = (i6 & 87) + (i6 | 87);
                                a = i7 % 128;
                                int i8 = i7 % 2;
                                operationCallback.onSuccess(null);
                                int i9 = a;
                                int i10 = (i9 ^ Opcodes.DREM) + ((i9 & Opcodes.DREM) << 1);
                                e = i10 % 128;
                                switch (i10 % 2 == 0 ? '#' : 'Z') {
                                    case 'Z':
                                        return;
                                    default:
                                        int i11 = 67 / 0;
                                        return;
                                }
                            }

                            @Override // o.ep.a.InterfaceC0042a
                            public final void e(o.bv.c cVar) {
                                int i6 = (a + Opcodes.FDIV) - 1;
                                e = i6 % 128;
                                switch (i6 % 2 == 0 ? 'S' : '4') {
                                    case '4':
                                        operationCallback.onError(cVar.d());
                                        break;
                                    default:
                                        operationCallback.onError(cVar.d());
                                        int i7 = 50 / 0;
                                        break;
                                }
                                int i8 = e + 87;
                                a = i8 % 128;
                                int i9 = i8 % 2;
                            }
                        }, iVar);
                        int i6 = (f + 36) - 1;
                        d = i6 % 128;
                        int i7 = i6 % 2;
                        break;
                    case 3:
                        operationCallback.onError(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable).d());
                        int i8 = f + 37;
                        d = i8 % 128;
                        switch (i8 % 2 != 0 ? (char) 20 : (char) 6) {
                            case 6:
                                return;
                            default:
                                obj.hashCode();
                                throw null;
                        }
                }
                int i9 = f;
                int i10 = (i9 & 19) + (i9 | 19);
                d = i10 % 128;
                switch (i10 % 2 == 0) {
                    case true:
                        return;
                    default:
                        throw null;
                }
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i = d;
                int i2 = ((i | Opcodes.DSUB) << 1) - (i ^ Opcodes.DSUB);
                f = i2 % 128;
                boolean z = i2 % 2 != 0;
                operationCallback.onError(cVar.d());
                switch (z) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }
        });
        AndroidActivityResultCallback e2 = iVar.e();
        int i = e + 29;
        a = i % 128;
        int i2 = i % 2;
        return e2;
    }

    public final void d(final Activity activity) {
        e(activity).e(new a.InterfaceC0042a<a.b>() { // from class: o.er.d.5
            private static int e = 0;
            private static int a = 1;

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i = e;
                int i2 = (i & Opcodes.DDIV) + (i | Opcodes.DDIV);
                a = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(a.b bVar) {
                int i = a + 97;
                e = i % 128;
                int i2 = i % 2;
                c(bVar);
                int i3 = a + 5;
                e = i3 % 128;
                int i4 = i3 % 2;
            }

            private void c(a.b bVar) {
                int i = a;
                int i2 = (i & 59) + (i | 59);
                e = i2 % 128;
                int i3 = i2 % 2;
                Object obj = null;
                switch (AnonymousClass10.a[bVar.ordinal()]) {
                    case 1:
                    case 3:
                        int i4 = a + 29;
                        e = i4 % 128;
                        switch (i4 % 2 == 0) {
                            case true:
                                return;
                            default:
                                obj.hashCode();
                                throw null;
                        }
                    case 2:
                        d.this.e(activity).c(activity);
                        break;
                }
                int i5 = e;
                int i6 = ((i5 | 53) << 1) - (i5 ^ 53);
                a = i6 % 128;
                switch (i6 % 2 == 0 ? '%' : 'L') {
                    case '%':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }
        });
        int i = e + 85;
        a = i % 128;
        int i2 = i % 2;
    }

    public final void c(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        int i = e + 95;
        a = i % 128;
        int i2 = i % 2;
        if (this.c.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            k(4 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "\u0006\u0014\u0003￥", 4 - (ViewConfiguration.getEdgeSlop() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + 283, true, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (!b()) {
            throw new WalletValidationException(WalletValidationErrorCode.WrongState, c());
        }
        d(context, operationCallback, false);
        int i3 = a + 99;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                int i4 = 79 / 0;
                return;
        }
    }

    /* renamed from: o.er.d$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\d$4.smali */
    final class AnonymousClass4 implements a.InterfaceC0042a<a.b> {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int f;
        private static int[] g;
        private static int j;
        final /* synthetic */ OperationCallback a;
        final /* synthetic */ boolean b;
        final /* synthetic */ o.ep.a c;
        final /* synthetic */ Context d;
        final /* synthetic */ o.em.e e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            f = 1;
            g = new int[]{999343774, 1006695593, 529131696, 1269399124, 674642365, -901460385, -1886792829, 472593446, -1234180683, 2056564567, 434564943, -1358754902, -1535754223, 305346459, 1117791447, 2045799347, -1513189281, -576030087};
        }

        static void init$0() {
            $$a = new byte[]{52, -61, 40, Tnaf.POW_2_WIDTH};
            $$b = Opcodes.RETURN;
        }

        private static void k(short s, int i, byte b, Object[] objArr) {
            int i2 = 3 - (s * 2);
            int i3 = (b * 2) + 1;
            int i4 = 116 - i;
            byte[] bArr = $$a;
            byte[] bArr2 = new byte[i3];
            int i5 = -1;
            int i6 = i3 - 1;
            if (bArr == null) {
                i6 = i6;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = -1;
                i4 = i2 + i4;
                i2 = i2;
            }
            while (true) {
                int i7 = i2 + 1;
                int i8 = i5 + 1;
                bArr2[i8] = (byte) i4;
                if (i8 == i6) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b2 = bArr[i7];
                i6 = i6;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i5 = i8;
                i4 = b2 + i4;
                i2 = i7;
            }
        }

        AnonymousClass4(OperationCallback operationCallback, o.ep.a aVar, o.em.e eVar, boolean z, Context context) {
            this.a = operationCallback;
            this.c = aVar;
            this.e = eVar;
            this.b = z;
            this.d = context;
        }

        @Override // o.ep.a.InterfaceC0042a
        public final /* synthetic */ void e(a.b bVar) {
            int i = j + 5;
            f = i % 128;
            int i2 = i % 2;
            c(bVar);
            int i3 = f + Opcodes.LNEG;
            j = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return;
                default:
                    int i4 = 57 / 0;
                    return;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:12:0x008f, code lost:
        
            r6 = 87 / 0;
         */
        /* JADX WARN: Code restructure failed: missing block: B:13:0x0090, code lost:
        
            return;
         */
        /* JADX WARN: Code restructure failed: missing block: B:18:0x0093, code lost:
        
            return;
         */
        /* JADX WARN: Code restructure failed: missing block: B:19:0x0024, code lost:
        
            o.ee.g.c();
            r6 = r5.i.e();
            r4 = new java.lang.Object[1];
            h(new int[]{-214072014, -841130191, 1083574347, -1061628600, 479585176, -595495853, -551634369, 410480688, -2023751722, -75350390, 317741377, -150130575, 1958529305, -1995317574, 1661885324, 1277447232, 1308935184, -463222179, 845036012, -239819827, 567174027, 1378730534, -654591148, -590296313, 1248490343, -16661809}, 50 - (android.media.AudioTrack.getMaxVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), r4);
            o.ee.g.d(r6, ((java.lang.String) r4[0]).intern());
            r5.a.onError(new o.bv.c(r5.i.a()).d());
            r6 = o.er.d.AnonymousClass4.j + 93;
            o.er.d.AnonymousClass4.f = r6 % 128;
         */
        /* JADX WARN: Code restructure failed: missing block: B:20:0x006c, code lost:
        
            if ((r6 % 2) != 0) goto L20;
         */
        /* JADX WARN: Code restructure failed: missing block: B:21:0x006e, code lost:
        
            r1 = true;
         */
        /* JADX WARN: Code restructure failed: missing block: B:22:0x006f, code lost:
        
            switch(r1) {
                case 1: goto L22;
                default: goto L21;
            };
         */
        /* JADX WARN: Code restructure failed: missing block: B:23:0x0072, code lost:
        
            return;
         */
        /* JADX WARN: Code restructure failed: missing block: B:26:0x0074, code lost:
        
            throw null;
         */
        /* JADX WARN: Code restructure failed: missing block: B:33:0x001f, code lost:
        
            if (r6 != o.ep.a.b.c) goto L17;
         */
        /* JADX WARN: Code restructure failed: missing block: B:7:0x0017, code lost:
        
            if (r6 != o.ep.a.b.c) goto L17;
         */
        /* JADX WARN: Code restructure failed: missing block: B:8:0x0077, code lost:
        
            r5.c.d(new o.er.d.AnonymousClass4.AnonymousClass2(r5));
            r6 = o.er.d.AnonymousClass4.f + 33;
            o.er.d.AnonymousClass4.j = r6 % 128;
         */
        /* JADX WARN: Code restructure failed: missing block: B:9:0x008b, code lost:
        
            if ((r6 % 2) == 0) goto L33;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private void c(o.ep.a.b r6) {
            /*
                r5 = this;
                int r0 = o.er.d.AnonymousClass4.f
                int r0 = r0 + 79
                int r1 = r0 % 128
                o.er.d.AnonymousClass4.j = r1
                int r0 = r0 % 2
                if (r0 == 0) goto Lf
                r0 = 51
                goto L11
            Lf:
                r0 = 31
            L11:
                r1 = 0
                switch(r0) {
                    case 51: goto L1a;
                    default: goto L15;
                }
            L15:
                o.ep.a$b r0 = o.ep.a.b.c
                if (r6 == r0) goto L77
            L19:
                goto L24
            L1a:
                o.ep.a$b r0 = o.ep.a.b.c
                r2 = 89
                int r2 = r2 / r1
                if (r6 == r0) goto L77
                goto L19
            L22:
                r6 = move-exception
                throw r6
            L24:
                o.ee.g.c()
                o.er.d r6 = o.er.d.this
                java.lang.String r6 = r6.e()
                r0 = 26
                int[] r0 = new int[r0]
                r0 = {x00a0: FILL_ARRAY_DATA , data: [-214072014, -841130191, 1083574347, -1061628600, 479585176, -595495853, -551634369, 410480688, -2023751722, -75350390, 317741377, -150130575, 1958529305, -1995317574, 1661885324, 1277447232, 1308935184, -463222179, 845036012, -239819827, 567174027, 1378730534, -654591148, -590296313, 1248490343, -16661809} // fill-array
                float r2 = android.media.AudioTrack.getMaxVolume()
                r3 = 0
                int r2 = (r2 > r3 ? 1 : (r2 == r3 ? 0 : -1))
                int r2 = 50 - r2
                r3 = 1
                java.lang.Object[] r4 = new java.lang.Object[r3]
                h(r0, r2, r4)
                r0 = r4[r1]
                java.lang.String r0 = (java.lang.String) r0
                java.lang.String r0 = r0.intern()
                o.ee.g.d(r6, r0)
                fr.antelop.sdk.util.OperationCallback r6 = r5.a
                o.bv.c r0 = new o.bv.c
                o.er.d r2 = o.er.d.this
                fr.antelop.sdk.AntelopErrorCode r2 = r2.a()
                r0.<init>(r2)
                fr.antelop.sdk.AntelopError r0 = r0.d()
                r6.onError(r0)
                int r6 = o.er.d.AnonymousClass4.j
                int r6 = r6 + 93
                int r0 = r6 % 128
                o.er.d.AnonymousClass4.f = r0
                int r6 = r6 % 2
                if (r6 != 0) goto L6f
                r1 = r3
            L6f:
                switch(r1) {
                    case 1: goto L73;
                    default: goto L72;
                }
            L72:
                return
            L73:
                r6 = 0
                throw r6     // Catch: java.lang.Throwable -> L75
            L75:
                r6 = move-exception
                throw r6
            L77:
                o.ep.a r6 = r5.c
                o.er.d$4$2 r0 = new o.er.d$4$2
                r0.<init>()
                r6.d(r0)
                int r6 = o.er.d.AnonymousClass4.f
                int r6 = r6 + 33
                int r0 = r6 % 128
                o.er.d.AnonymousClass4.j = r0
                int r6 = r6 % 2
                if (r6 == 0) goto L93
                r6 = 87
                int r6 = r6 / r1
                return
            L91:
                r6 = move-exception
                throw r6
            L93:
                return
            */
            throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass4.c(o.ep.a$b):void");
        }

        @Override // o.ep.a.InterfaceC0042a
        public final void e(o.bv.c cVar) {
            int i = f + 1;
            j = i % 128;
            switch (i % 2 != 0 ? ',' : (char) 24) {
                case 24:
                    this.a.onError(cVar.d());
                    return;
                default:
                    this.a.onError(cVar.d());
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void h(int[] r25, int r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 986
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass4.h(int[], int, java.lang.Object[]):void");
        }
    }

    final void d(Context context, OperationCallback<Boolean> operationCallback, boolean z) {
        o.ee.g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        k(7 - Color.argb(0, 0, 0, 0), "\uffe7\u0002\u0010\uffff￡\u0011\u0007\u0017\uffff￮\u0003\u0001\u0007\u0014\u0003￢\f", 17 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), Color.alpha(0) + 287, true, objArr);
        o.ee.g.d(e2, ((String) objArr[0]).intern());
        T e3 = e(context);
        e3.e(new AnonymousClass4(operationCallback, e3, o.ei.c.c().j(), z, context));
        int i = e + Opcodes.LSHL;
        a = i % 128;
        int i2 = i % 2;
    }

    public final void a(Context context, final OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        int i = e + 91;
        a = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        k(TextUtils.indexOf((CharSequence) "", '0', 0) + 8, "\uffe7\u0002\u0010\uffff￡\u0011\u0007\u0017\uffff￮\u0003\u0001\u0007\u0014\u0003￢\f", 16 - ExpandableListView.getPackedPositionChild(0L), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 288, true, objArr);
        o.ee.g.d(e2, ((String) objArr[0]).intern());
        if (this.c.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr2 = new Object[1];
            k(4 - Drawable.resolveOpacity(0, 0), "\u0006\u0014\u0003￥", 4 - View.getDefaultSize(0, 0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 283, true, objArr2);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr2[0]).intern());
        }
        if (!b()) {
            throw new WalletValidationException(WalletValidationErrorCode.WrongState, c());
        }
        final T e3 = e(context);
        e3.e(new a.InterfaceC0042a<a.b>() { // from class: o.er.d.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static long e;
            private static int g;
            private static int h;
            private static char i;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                g = 0;
                h = 1;
                i = (char) 11866;
                a = 161105445;
                e = 6565854932352255525L;
            }

            static void init$0() {
                $$a = new byte[]{29, -23, 98, 29};
                $$b = Opcodes.IUSHR;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void j(int r7, short r8, int r9, java.lang.Object[] r10) {
                /*
                    int r7 = r7 * 4
                    int r7 = 4 - r7
                    int r8 = r8 + 99
                    byte[] r0 = o.er.d.AnonymousClass3.$$a
                    int r9 = r9 * 3
                    int r9 = 1 - r9
                    byte[] r1 = new byte[r9]
                    r2 = 0
                    if (r0 != 0) goto L19
                    r3 = r1
                    r5 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    r9 = r8
                    r8 = r7
                    goto L33
                L19:
                    r3 = r2
                L1a:
                    byte r4 = (byte) r8
                    int r5 = r3 + 1
                    r1[r3] = r4
                    if (r5 != r9) goto L29
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L29:
                    r3 = r0[r7]
                    r6 = r8
                    r8 = r7
                    r7 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    r9 = r6
                L33:
                    int r7 = -r7
                    int r8 = r8 + 1
                    int r7 = r7 + r9
                    r9 = r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r5
                    r6 = r8
                    r8 = r7
                    r7 = r6
                    goto L1a
                */
                throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass3.j(int, short, int, java.lang.Object[]):void");
            }

            @Override // o.ep.a.InterfaceC0042a
            public final /* bridge */ /* synthetic */ void e(a.b bVar) {
                int i3 = g + 71;
                h = i3 % 128;
                char c = i3 % 2 == 0 ? 'C' : 'W';
                e2(bVar);
                switch (c) {
                    case Opcodes.POP /* 87 */:
                        break;
                    default:
                        int i4 = 80 / 0;
                        break;
                }
                int i5 = h + 25;
                g = i5 % 128;
                int i6 = i5 % 2;
            }

            /* renamed from: e, reason: avoid collision after fix types in other method */
            private void e2(a.b bVar) {
                int i3 = g + 73;
                h = i3 % 128;
                Object obj = null;
                switch (i3 % 2 == 0 ? (char) 18 : (char) 21) {
                    case 18:
                        a.b bVar2 = a.b.c;
                        obj.hashCode();
                        throw null;
                    default:
                        if (bVar == a.b.c) {
                            e3.d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.er.d.3.3
                                public static final byte[] $$a = null;
                                public static final int $$b = 0;
                                private static int $10;
                                private static int $11;
                                private static int b;
                                private static int c;
                                private static char d;
                                private static char[] e;

                                static {
                                    init$0();
                                    $10 = 0;
                                    $11 = 1;
                                    c = 0;
                                    b = 1;
                                    e = new char[]{30588, 30571, 30570, 30565, 29844, 30569, 30562, 30589, 30574, 30573, 30587, 30567, 30517, 30564, 30584, 30513, 30568, 30575, 30528, 29845, 30540, 30498, 30566, 30560, 30585, 30511, 30563, 30582, 30572, 30591, 30559, 30586, 30539, 30561, 30590, 30534};
                                    d = (char) 17043;
                                }

                                private static void g(short s, short s2, short s3, Object[] objArr3) {
                                    int i4 = 73 - s;
                                    int i5 = 3 - (s2 * 4);
                                    int i6 = (s3 * 3) + 1;
                                    byte[] bArr = $$a;
                                    byte[] bArr2 = new byte[i6];
                                    int i7 = -1;
                                    int i8 = i6 - 1;
                                    if (bArr == null) {
                                        int i9 = i5 + i8;
                                        i8 = i8;
                                        objArr3 = objArr3;
                                        bArr = bArr;
                                        bArr2 = bArr2;
                                        i7 = -1;
                                        i5 = i5;
                                        i4 = i9;
                                    }
                                    while (true) {
                                        int i10 = i7 + 1;
                                        bArr2[i10] = (byte) i4;
                                        if (i10 == i8) {
                                            objArr3[0] = new String(bArr2, 0);
                                            return;
                                        }
                                        int i11 = i5 + 1;
                                        i8 = i8;
                                        objArr3 = objArr3;
                                        bArr = bArr;
                                        bArr2 = bArr2;
                                        i7 = i10;
                                        i5 = i11;
                                        i4 = bArr[i11] + i4;
                                    }
                                }

                                static void init$0() {
                                    $$a = new byte[]{25, -41, -75, 3};
                                    $$b = Opcodes.L2F;
                                }

                                @Override // o.ep.a.InterfaceC0042a
                                public final /* synthetic */ void e(List<o.ep.e> list) {
                                    int i4 = c + 19;
                                    b = i4 % 128;
                                    boolean z = i4 % 2 == 0;
                                    a(list);
                                    switch (z) {
                                        case false:
                                            break;
                                        default:
                                            int i5 = 16 / 0;
                                            break;
                                    }
                                    int i6 = b + Opcodes.DSUB;
                                    c = i6 % 128;
                                    int i7 = i6 % 2;
                                }

                                /* JADX WARN: Code restructure failed: missing block: B:24:0x012e, code lost:
                                
                                    o.ee.g.c();
                                    r0 = r8.a.d.e();
                                    r5 = new java.lang.StringBuilder();
                                    r4 = new java.lang.Object[1];
                                    f(79 - android.text.TextUtils.lastIndexOf("", '0'), "\u0012\u0004\u001a\u000e\r\u0007\u001e\"\u0002\b\u001c\u0012\u001a\u0004 \u0006\u0018\u0015㘪㘪\u001c\u0014 \u0003\u001b\u0013\u0018\u0007\t\u000b\u001d\n\u001b\u0007\u0004\b\u0014\b\u0003 \u001a\u0007\u001a\r\u000e  \b\u0007\u001c\u000b\u0016\u000e\u0001\u001f\u001b\t \u0007\u001f\u000b\u0006\u0001\u001a\u0004\u0013\u0016\u001c\u000b\t\u001b\u001a\u001a\n\r\u0007\u001b\u0013\r\u001b", (byte) (45 - android.os.Process.getGidForName("")), r4);
                                    o.ee.g.d(r0, r5.append(((java.lang.String) r4[0]).intern()).append(r9).toString());
                                    r2.onSuccess(java.lang.Boolean.valueOf(r9));
                                    r9 = o.er.d.AnonymousClass3.C00433.b + com.esotericsoftware.asm.Opcodes.DMUL;
                                    o.er.d.AnonymousClass3.C00433.c = r9 % 128;
                                 */
                                /* JADX WARN: Code restructure failed: missing block: B:25:0x0180, code lost:
                                
                                    if ((r9 % 2) != 0) goto L38;
                                 */
                                /* JADX WARN: Code restructure failed: missing block: B:26:0x0182, code lost:
                                
                                    return;
                                 */
                                /* JADX WARN: Code restructure failed: missing block: B:28:0x0183, code lost:
                                
                                    r3.hashCode();
                                 */
                                /* JADX WARN: Code restructure failed: missing block: B:29:0x0186, code lost:
                                
                                    throw null;
                                 */
                                /*
                                    Code decompiled incorrectly, please refer to instructions dump.
                                    To view partially-correct add '--show-bad-code' argument
                                */
                                private void a(java.util.List<o.ep.e> r9) {
                                    /*
                                        Method dump skipped, instructions count: 400
                                        To view this dump add '--comments-level debug' option
                                    */
                                    throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass3.C00433.a(java.util.List):void");
                                }

                                @Override // o.ep.a.InterfaceC0042a
                                public final void e(o.bv.c cVar) {
                                    o.ee.g.c();
                                    String e4 = d.this.e();
                                    StringBuilder sb = new StringBuilder();
                                    Object[] objArr3 = new Object[1];
                                    f(63 - TextUtils.getOffsetAfter("", 0), "\u0012\u0004\u001a\u000e\r\u0007\u001e\"\u0002\b\u001c\u0012\u001a\u0004 \u0006\u001c\u001a\u0013\u001b\u0004\u0013\u0004\u0006\t \u0007\u001c\u001a\u000b\u001c\u001a\u0014\u000e㘗㘗\u0004\b\u001c\u0007\u0013\u0011\u0003 \u001d\u0001\u0004\b\u001d\n\u0015\"\r\u001c\u0002\u000b\u0014\u001c\u0003\u0002\u0018\r㗛", (byte) (33 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), objArr3);
                                    o.ee.g.e(e4, sb.append(((String) objArr3[0]).intern()).append(cVar.c()).toString());
                                    operationCallback.onError(cVar.d());
                                    int i4 = c + 7;
                                    b = i4 % 128;
                                    int i5 = i4 % 2;
                                }

                                private static void f(int i4, String str, byte b2, Object[] objArr3) {
                                    char[] cArr;
                                    int i5;
                                    Object obj2 = null;
                                    if (str != null) {
                                        int i6 = $10 + 91;
                                        $11 = i6 % 128;
                                        if (i6 % 2 == 0) {
                                            str.toCharArray();
                                            obj2.hashCode();
                                            throw null;
                                        }
                                        cArr = str.toCharArray();
                                    } else {
                                        cArr = str;
                                    }
                                    char[] cArr2 = cArr;
                                    o.a.m mVar = new o.a.m();
                                    char[] cArr3 = e;
                                    if (cArr3 != null) {
                                        int length = cArr3.length;
                                        char[] cArr4 = new char[length];
                                        for (int i7 = 0; i7 < length; i7++) {
                                            try {
                                                Object[] objArr4 = {Integer.valueOf(cArr3[i7])};
                                                Object obj3 = o.e.a.s.get(-1401577988);
                                                if (obj3 == null) {
                                                    Class cls = (Class) o.e.a.c(17 - Color.blue(0), (char) View.resolveSize(0, 0), (KeyEvent.getMaxKeyCode() >> 16) + 76);
                                                    byte b3 = (byte) 0;
                                                    byte b4 = b3;
                                                    Object[] objArr5 = new Object[1];
                                                    g(b3, b4, b4, objArr5);
                                                    obj3 = cls.getMethod((String) objArr5[0], Integer.TYPE);
                                                    o.e.a.s.put(-1401577988, obj3);
                                                }
                                                cArr4[i7] = ((Character) ((Method) obj3).invoke(null, objArr4)).charValue();
                                            } catch (Throwable th) {
                                                Throwable cause = th.getCause();
                                                if (cause == null) {
                                                    throw th;
                                                }
                                                throw cause;
                                            }
                                        }
                                        cArr3 = cArr4;
                                    }
                                    try {
                                        Object[] objArr6 = {Integer.valueOf(d)};
                                        Object obj4 = o.e.a.s.get(-1401577988);
                                        if (obj4 == null) {
                                            Class cls2 = (Class) o.e.a.c(17 - (Process.myPid() >> 22), (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 76);
                                            byte b5 = (byte) 0;
                                            byte b6 = b5;
                                            Object[] objArr7 = new Object[1];
                                            g(b5, b6, b6, objArr7);
                                            obj4 = cls2.getMethod((String) objArr7[0], Integer.TYPE);
                                            o.e.a.s.put(-1401577988, obj4);
                                        }
                                        char charValue = ((Character) ((Method) obj4).invoke(null, objArr6)).charValue();
                                        char[] cArr5 = new char[i4];
                                        if (i4 % 2 != 0) {
                                            i5 = i4 - 1;
                                            cArr5[i5] = (char) (cArr2[i5] - b2);
                                        } else {
                                            i5 = i4;
                                        }
                                        switch (i5 > 1 ? 'P' : ' ') {
                                            case ' ':
                                                break;
                                            default:
                                                mVar.b = 0;
                                                while (true) {
                                                    switch (mVar.b < i5 ? (char) 1 : '\\') {
                                                        case 1:
                                                            mVar.e = cArr2[mVar.b];
                                                            mVar.a = cArr2[mVar.b + 1];
                                                            switch (mVar.e == mVar.a ? 'c' : (char) 20) {
                                                                case Opcodes.DADD /* 99 */:
                                                                    cArr5[mVar.b] = (char) (mVar.e - b2);
                                                                    cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                                                    break;
                                                                default:
                                                                    try {
                                                                        Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                                                        Object obj5 = o.e.a.s.get(696901393);
                                                                        if (obj5 == null) {
                                                                            Class cls3 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 10, (char) (8856 - (Process.myPid() >> 22)), 323 - Process.getGidForName(""));
                                                                            byte length2 = (byte) $$a.length;
                                                                            byte b7 = (byte) (length2 - 4);
                                                                            Object[] objArr9 = new Object[1];
                                                                            g(length2, b7, b7, objArr9);
                                                                            obj5 = cls3.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                                            o.e.a.s.put(696901393, obj5);
                                                                        }
                                                                        if (((Integer) ((Method) obj5).invoke(null, objArr8)).intValue() == mVar.h) {
                                                                            try {
                                                                                Object[] objArr10 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                                                Object obj6 = o.e.a.s.get(1075449051);
                                                                                if (obj6 == null) {
                                                                                    Class cls4 = (Class) o.e.a.c(View.resolveSizeAndState(0, 0, 0) + 11, (char) (ViewConfiguration.getScrollDefaultDelay() >> 16), KeyEvent.getDeadChar(0, 0) + 65);
                                                                                    byte b8 = $$a[3];
                                                                                    byte b9 = (byte) (b8 - 3);
                                                                                    Object[] objArr11 = new Object[1];
                                                                                    g(b8, b9, b9, objArr11);
                                                                                    obj6 = cls4.getMethod((String) objArr11[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                                                    o.e.a.s.put(1075449051, obj6);
                                                                                }
                                                                                int intValue = ((Integer) ((Method) obj6).invoke(null, objArr10)).intValue();
                                                                                int i8 = (mVar.d * charValue) + mVar.h;
                                                                                cArr5[mVar.b] = cArr3[intValue];
                                                                                cArr5[mVar.b + 1] = cArr3[i8];
                                                                                break;
                                                                            } catch (Throwable th2) {
                                                                                Throwable cause2 = th2.getCause();
                                                                                if (cause2 == null) {
                                                                                    throw th2;
                                                                                }
                                                                                throw cause2;
                                                                            }
                                                                        } else if (mVar.c == mVar.d) {
                                                                            int i9 = $10 + 1;
                                                                            $11 = i9 % 128;
                                                                            int i10 = i9 % 2;
                                                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                                            int i11 = (mVar.c * charValue) + mVar.i;
                                                                            int i12 = (mVar.d * charValue) + mVar.h;
                                                                            cArr5[mVar.b] = cArr3[i11];
                                                                            cArr5[mVar.b + 1] = cArr3[i12];
                                                                            break;
                                                                        } else {
                                                                            int i13 = (mVar.c * charValue) + mVar.h;
                                                                            int i14 = (mVar.d * charValue) + mVar.i;
                                                                            cArr5[mVar.b] = cArr3[i13];
                                                                            cArr5[mVar.b + 1] = cArr3[i14];
                                                                            int i15 = $10 + Opcodes.LSHL;
                                                                            $11 = i15 % 128;
                                                                            switch (i15 % 2 == 0 ? 'b' : 'A') {
                                                                            }
                                                                        }
                                                                    } catch (Throwable th3) {
                                                                        Throwable cause3 = th3.getCause();
                                                                        if (cause3 == null) {
                                                                            throw th3;
                                                                        }
                                                                        throw cause3;
                                                                    }
                                                                    break;
                                                            }
                                                            mVar.b += 2;
                                                            break;
                                                    }
                                                }
                                        }
                                        for (int i16 = 0; i16 < i4; i16++) {
                                            cArr5[i16] = (char) (cArr5[i16] ^ 13722);
                                        }
                                        objArr3[0] = new String(cArr5);
                                    } catch (Throwable th4) {
                                        Throwable cause4 = th4.getCause();
                                        if (cause4 == null) {
                                            throw th4;
                                        }
                                        throw cause4;
                                    }
                                }
                            });
                            return;
                        }
                        o.ee.g.c();
                        String e4 = d.this.e();
                        Object[] objArr3 = new Object[1];
                        f((ViewConfiguration.getTapTimeout() >> 16) - 597689774, "ᇧ갦욦玧\ue9f0ᇣ\ued92\udcb7ၰƏ⚡\ue836ߐ溙ق\ue122ᖧ㗸\u1259и삮䶞탒좧鷌状鞔묽첦\uec96큎삥命䏬䬵쌳ञ륁ᐐꏱ㊤\ud9d8ሪ丅祤榺蘠㍣꧴", (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), "勁忺\ue0dcꙧ", "\u0000\u0000\u0000\u0000", objArr3);
                        o.ee.g.d(e4, ((String) objArr3[0]).intern());
                        operationCallback.onError(new o.bv.c(d.this.a()).d());
                        int i4 = h + 37;
                        g = i4 % 128;
                        switch (i4 % 2 == 0) {
                            case false:
                                obj.hashCode();
                                throw null;
                            default:
                                return;
                        }
                }
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i3 = g + 79;
                h = i3 % 128;
                int i4 = i3 % 2;
                operationCallback.onError(cVar.d());
                int i5 = h + 55;
                g = i5 % 128;
                int i6 = i5 % 2;
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
                /*
                    Method dump skipped, instructions count: 754
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass3.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
            }
        });
        int i3 = a + 9;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void d(Context context, final OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        c(context, new OperationCallback<Boolean>() { // from class: o.er.d.9
            private static int b = 0;
            private static int e = 1;

            @Override // fr.antelop.sdk.util.OperationCallback
            public final /* synthetic */ void onSuccess(Boolean bool) {
                int i = e;
                int i2 = (i ^ 25) + ((i & 25) << 1);
                b = i2 % 128;
                int i3 = i2 % 2;
                c(bool);
                int i4 = b;
                int i5 = ((i4 | 91) << 1) - (i4 ^ 91);
                e = i5 % 128;
                switch (i5 % 2 == 0 ? (char) 3 : ')') {
                    case 3:
                        int i6 = 91 / 0;
                        return;
                    default:
                        return;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:15:0x0040, code lost:
            
                r1 = r6;
             */
            /* JADX WARN: Failed to find 'out' block for switch in B:24:0x0054. Please report as an issue. */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private void c(java.lang.Boolean r6) {
                /*
                    r5 = this;
                    int r0 = o.er.d.AnonymousClass9.b
                    int r0 = r0 + 91
                    int r1 = r0 % 128
                    o.er.d.AnonymousClass9.e = r1
                    int r0 = r0 % 2
                    fr.antelop.sdk.util.OperationCallback r0 = r2
                    boolean r6 = r6.booleanValue()
                    if (r6 != 0) goto L14
                    r6 = 5
                    goto L16
                L14:
                    r6 = 12
                L16:
                    r1 = 0
                    r2 = 1
                    switch(r6) {
                        case 5: goto L1c;
                        default: goto L1b;
                    }
                L1b:
                    goto L42
                L1c:
                    int r6 = o.er.d.AnonymousClass9.b
                    int r6 = r6 + 57
                    int r3 = r6 % 128
                    o.er.d.AnonymousClass9.e = r3
                    int r6 = r6 % 2
                    if (r6 != 0) goto L2a
                    r6 = r2
                    goto L2b
                L2a:
                    r6 = r1
                L2b:
                    switch(r6) {
                        case 0: goto L30;
                        default: goto L2e;
                    }
                L2e:
                    r6 = r1
                    goto L31
                L30:
                    r6 = r2
                L31:
                    int r3 = r3 + 117
                    int r4 = r3 % 128
                    o.er.d.AnonymousClass9.b = r4
                    int r3 = r3 % 2
                    if (r3 == 0) goto L3c
                    goto L3d
                L3c:
                    r1 = r2
                L3d:
                    switch(r1) {
                        case 0: goto L40;
                        default: goto L40;
                    }
                L40:
                    r1 = r6
                    goto L57
                L42:
                    int r6 = o.er.d.AnonymousClass9.e
                    int r6 = r6 + 76
                    int r6 = r6 - r2
                    int r3 = r6 % 128
                    o.er.d.AnonymousClass9.b = r3
                    int r6 = r6 % 2
                    if (r6 == 0) goto L52
                    r6 = 28
                    goto L54
                L52:
                    r6 = 21
                L54:
                    switch(r6) {
                        case 21: goto L57;
                        default: goto L57;
                    }
                L57:
                    java.lang.Boolean r6 = java.lang.Boolean.valueOf(r1)
                    r0.onSuccess(r6)
                    int r6 = o.er.d.AnonymousClass9.b
                    r0 = r6 ^ 101(0x65, float:1.42E-43)
                    r6 = r6 & 101(0x65, float:1.42E-43)
                    int r6 = r6 << r2
                    int r0 = r0 + r6
                    int r6 = r0 % 128
                    o.er.d.AnonymousClass9.e = r6
                    int r0 = r0 % 2
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass9.c(java.lang.Boolean):void");
            }

            @Override // fr.antelop.sdk.util.OperationCallback
            public final void onError(AntelopError antelopError) {
                int i = b + 63;
                e = i % 128;
                int i2 = i % 2;
                operationCallback.onError(antelopError);
                int i3 = e + 93;
                b = i3 % 128;
                int i4 = i3 % 2;
            }
        });
        int i = a + 75;
        e = i % 128;
        int i2 = i % 2;
    }

    public final void e(Context context, final OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        a(context, new OperationCallback<Boolean>() { // from class: o.er.d.8
            private static int b = 0;
            private static int c = 1;

            @Override // fr.antelop.sdk.util.OperationCallback
            public final /* synthetic */ void onSuccess(Boolean bool) {
                int i = c + 89;
                b = i % 128;
                int i2 = i % 2;
                e(bool);
                int i3 = c;
                int i4 = (i3 ^ 77) + ((i3 & 77) << 1);
                b = i4 % 128;
                int i5 = i4 % 2;
            }

            private void e(Boolean bool) {
                boolean z;
                int i = (b + 2) - 1;
                c = i % 128;
                int i2 = i % 2;
                OperationCallback operationCallback2 = operationCallback;
                switch (!bool.booleanValue() ? (char) 23 : ',') {
                    case ',':
                        int i3 = c;
                        int i4 = (i3 ^ Opcodes.DSUB) + ((i3 & Opcodes.DSUB) << 1);
                        b = i4 % 128;
                        int i5 = i4 % 2;
                        z = false;
                        break;
                    default:
                        int i6 = c;
                        int i7 = (i6 ^ Opcodes.DSUB) + ((i6 & Opcodes.DSUB) << 1);
                        b = i7 % 128;
                        if (i7 % 2 != 0) {
                        }
                        z = true;
                        break;
                }
                operationCallback2.onSuccess(Boolean.valueOf(z));
                int i8 = (c + 70) - 1;
                b = i8 % 128;
                int i9 = i8 % 2;
            }

            @Override // fr.antelop.sdk.util.OperationCallback
            public final void onError(AntelopError antelopError) {
                int i = b;
                int i2 = (i & 67) + (i | 67);
                c = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onError(antelopError);
                int i4 = c;
                int i5 = (i4 ^ 69) + ((i4 & 69) << 1);
                b = i5 % 128;
                int i6 = i5 % 2;
            }
        });
        int i = a + 83;
        e = i % 128;
        int i2 = i % 2;
    }

    public final void h(Context context, final OperationCallback<String> operationCallback) throws WalletValidationException {
        int i = e + 61;
        a = i % 128;
        switch (i % 2 == 0 ? 'K' : '\t') {
            case 'K':
                this.c.z();
                CardStatus cardStatus = CardStatus.Active;
                throw null;
            default:
                if (this.c.z() != CardStatus.Active) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    k(5 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "\u0006\u0014\u0003￥", Color.alpha(0) + 4, 284 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), true, objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                if (!b()) {
                    throw new WalletValidationException(WalletValidationErrorCode.WrongState, c());
                }
                e(context).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.er.d.7
                    public static final byte[] $$a = null;
                    public static final int $$b = 0;
                    private static int $10;
                    private static int $11;
                    private static int[] a;
                    private static int b;
                    private static int e;

                    static {
                        init$0();
                        $10 = 0;
                        $11 = 1;
                        b = 0;
                        e = 1;
                        a = new int[]{-32887081, 1343681193, -445949924, -1802829861, -101416006, 1040633649, 421045988, -623803449, -914616763, -1477157140, 860134501, 1321642426, 806880930, 844160693, 253839093, 408117533, 247914714, -1225623079};
                    }

                    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
                    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
                    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
                    /*
                        Code decompiled incorrectly, please refer to instructions dump.
                        To view partially-correct add '--show-bad-code' argument
                    */
                    private static void g(int r6, byte r7, int r8, java.lang.Object[] r9) {
                        /*
                            byte[] r0 = o.er.d.AnonymousClass7.$$a
                            int r8 = r8 * 3
                            int r8 = r8 + 1
                            int r7 = r7 * 3
                            int r7 = 4 - r7
                            int r6 = r6 + 115
                            byte[] r1 = new byte[r8]
                            int r8 = r8 + (-1)
                            r2 = 0
                            if (r0 != 0) goto L1a
                            r6 = r7
                            r3 = r1
                            r4 = r2
                            r1 = r0
                            r0 = r9
                            r9 = r8
                            goto L35
                        L1a:
                            r3 = r2
                        L1b:
                            byte r4 = (byte) r6
                            r1[r3] = r4
                            if (r3 != r8) goto L28
                            java.lang.String r6 = new java.lang.String
                            r6.<init>(r1, r2)
                            r9[r2] = r6
                            return
                        L28:
                            r4 = r0[r7]
                            int r3 = r3 + 1
                            r5 = r8
                            r8 = r6
                            r6 = r7
                            r7 = r4
                            r4 = r3
                            r3 = r1
                            r1 = r0
                            r0 = r9
                            r9 = r5
                        L35:
                            int r7 = -r7
                            int r6 = r6 + 1
                            int r7 = r7 + r8
                            r8 = r9
                            r9 = r0
                            r0 = r1
                            r1 = r3
                            r3 = r4
                            r5 = r7
                            r7 = r6
                            r6 = r5
                            goto L1b
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass7.g(int, byte, int, java.lang.Object[]):void");
                    }

                    static void init$0() {
                        $$a = new byte[]{13, -73, -57, -113};
                        $$b = Opcodes.INSTANCEOF;
                    }

                    @Override // o.ep.a.InterfaceC0042a
                    public final /* synthetic */ void e(List<o.ep.e> list) {
                        int i2 = e + 71;
                        b = i2 % 128;
                        char c = i2 % 2 != 0 ? (char) 4 : '/';
                        b(list);
                        switch (c) {
                            case '/':
                                break;
                            default:
                                int i3 = 6 / 0;
                                break;
                        }
                        int i4 = b + 41;
                        e = i4 % 128;
                        int i5 = i4 % 2;
                    }

                    private void b(List<o.ep.e> list) {
                        String str;
                        int i2 = b + 27;
                        e = i2 % 128;
                        int i3 = i2 % 2;
                        o.ee.g.c();
                        String e2 = d.this.e();
                        Object[] objArr2 = new Object[1];
                        f(new int[]{-693629476, 880379739, 2138779831, -1801344359, 408654405, 97412593, 1576872679, -179207752, -346264440, 1779306274, -424236164, 171107400, -152446584, -794975555, 725844251, -200974996, 971504791, -827945854, -1209240406, 2053625890, -161444018, -245041134, 92836426, -1485397476}, 45 - ((Process.getThreadPriority(0) + 20) >> 6), objArr2);
                        o.ee.g.d(e2, ((String) objArr2[0]).intern());
                        Iterator<o.ep.e> it = list.iterator();
                        int i4 = b + 69;
                        e = i4 % 128;
                        switch (i4 % 2 == 0) {
                        }
                        while (true) {
                            if (it.hasNext()) {
                                o.ep.e next = it.next();
                                switch (d.this.c.s() != null ? '@' : Typography.amp) {
                                    case '@':
                                        if (!next.d().equals(d.this.c.s().a())) {
                                            break;
                                        } else {
                                            o.ee.g.c();
                                            String e3 = d.this.e();
                                            StringBuilder sb = new StringBuilder();
                                            Object[] objArr3 = new Object[1];
                                            f(new int[]{-693629476, 880379739, 2138779831, -1801344359, 408654405, 97412593, 1576872679, -179207752, -1165650913, 864550376, -677950124, -379972946, -1054252599, 392512989, -403987192, -1859959645, 2084888252, -687263778, -971415103, 1859560831, -1627024230, 16365720}, View.MeasureSpec.makeMeasureSpec(0, 0) + 41, objArr3);
                                            StringBuilder append = sb.append(((String) objArr3[0]).intern()).append(d.this.c.e());
                                            Object[] objArr4 = new Object[1];
                                            f(new int[]{1034686658, -2096981252, -1416778543, -1213209557, 655993220, 466848315, 1860958297, -1264344770, 1148739886, -337187503}, 20 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr4);
                                            o.ee.g.d(e3, append.append(((String) objArr4[0]).intern()).toString());
                                            str = next.c();
                                            break;
                                        }
                                }
                                int i5 = e + 65;
                                b = i5 % 128;
                                int i6 = i5 % 2;
                            } else {
                                str = null;
                            }
                        }
                        operationCallback.onSuccess(str);
                    }

                    @Override // o.ep.a.InterfaceC0042a
                    public final void e(o.bv.c cVar) {
                        o.ee.g.c();
                        String e2 = d.this.e();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr2 = new Object[1];
                        f(new int[]{-693629476, 880379739, 2138779831, -1801344359, 408654405, 97412593, 1576872679, -179207752, 511490983, -1513122219, -1997814577, -1359062856, 326356387, -480183581, -346264440, 1779306274, -424236164, 171107400, -152446584, -794975555, 725844251, -200974996, 971504791, -827945854, -1487062840, 1343475576, -1406302173, 97360205, -606656750, -737371319}, TextUtils.indexOf("", "", 0) + 59, objArr2);
                        o.ee.g.e(e2, sb.append(((String) objArr2[0]).intern()).append(cVar.c()).toString());
                        operationCallback.onError(cVar.d());
                        int i2 = e + 11;
                        b = i2 % 128;
                        int i3 = i2 % 2;
                    }

                    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                        */
                    private static void f(int[] r20, int r21, java.lang.Object[] r22) {
                        /*
                            Method dump skipped, instructions count: 980
                            To view this dump add '--comments-level debug' option
                        */
                        throw new UnsupportedOperationException("Method not decompiled: o.er.d.AnonymousClass7.f(int[], int, java.lang.Object[]):void");
                    }
                });
                int i2 = e + 35;
                a = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    private static void k(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        char[] charArray = str != null ? str.toCharArray() : str;
        o.a.h hVar = new o.a.h();
        char[] cArr2 = new char[i2];
        hVar.a = 0;
        while (true) {
            if (hVar.a >= i2) {
                break;
            }
            hVar.b = charArray[hVar.a];
            cArr2[hVar.a] = (char) (i3 + hVar.b);
            int i4 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr2[i4]), Integer.valueOf(b)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(12 - Color.green(0), (char) (ViewConfiguration.getKeyRepeatDelay() >> 16), 459 - View.combineMeasuredStates(0, 0));
                    byte b2 = (byte) 0;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    l(b2, b3, (byte) (b3 + 1), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr2[i4] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(View.MeasureSpec.makeMeasureSpec(0, 0) + 11, (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0) + 314);
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        l(b4, b5, b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        if (i > 0) {
            hVar.c = i;
            char[] cArr3 = new char[i2];
            System.arraycopy(cArr2, 0, cArr3, 0, i2);
            System.arraycopy(cArr3, 0, cArr2, i2 - hVar.c, hVar.c);
            System.arraycopy(cArr3, hVar.c, cArr2, 0, i2 - hVar.c);
        }
        if (z) {
            int i5 = $11 + 99;
            $10 = i5 % 128;
            switch (i5 % 2 != 0 ? (char) 15 : 'H') {
                case 'H':
                    cArr = new char[i2];
                    hVar.a = 0;
                    break;
                default:
                    cArr = new char[i2];
                    hVar.a = 1;
                    break;
            }
            while (true) {
                switch (hVar.a < i2 ? '`' : Typography.less) {
                    case Opcodes.IADD /* 96 */:
                        int i6 = $10 + 63;
                        $11 = i6 % 128;
                        switch (i6 % 2 != 0) {
                            case true:
                                cArr[hVar.a] = cArr2[(i2 - hVar.a) - 1];
                                try {
                                    Object[] objArr6 = {hVar, hVar};
                                    Object obj3 = o.e.a.s.get(-1412673904);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 10, (char) ExpandableListView.getPackedPositionType(0L), 313 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                                        byte b6 = (byte) 0;
                                        byte b7 = b6;
                                        Object[] objArr7 = new Object[1];
                                        l(b6, b7, b7, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                        o.e.a.s.put(-1412673904, obj3);
                                    }
                                    ((Method) obj3).invoke(null, objArr6);
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            default:
                                cArr[hVar.a] = cArr2[(i2 - hVar.a) - 1];
                                try {
                                    Object[] objArr8 = {hVar, hVar};
                                    Object obj4 = o.e.a.s.get(-1412673904);
                                    if (obj4 == null) {
                                        Class cls4 = (Class) o.e.a.c(11 - TextUtils.getOffsetBefore("", 0), (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 313);
                                        byte b8 = (byte) 0;
                                        byte b9 = b8;
                                        Object[] objArr9 = new Object[1];
                                        l(b8, b9, b9, objArr9);
                                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                        o.e.a.s.put(-1412673904, obj4);
                                    }
                                    ((Method) obj4).invoke(null, objArr8);
                                    break;
                                } catch (Throwable th4) {
                                    Throwable cause4 = th4.getCause();
                                    if (cause4 == null) {
                                        throw th4;
                                    }
                                    throw cause4;
                                }
                        }
                    default:
                        cArr2 = cArr;
                        break;
                }
            }
        }
        String str2 = new String(cArr2);
        int i7 = $10 + 99;
        $11 = i7 % 128;
        switch (i7 % 2 == 0) {
            case true:
                throw null;
            default:
                objArr[0] = str2;
                return;
        }
    }
}
