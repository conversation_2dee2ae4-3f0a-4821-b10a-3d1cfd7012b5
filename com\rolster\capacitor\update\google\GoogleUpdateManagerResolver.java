package com.rolster.capacitor.update.google;

import android.content.Context;
import androidx.core.app.NotificationCompat;
import com.getcapacitor.JSObject;
import com.getcapacitor.PluginCall;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.play.core.appupdate.AppUpdateInfo;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.firebase.messaging.Constants;
import com.rolster.capacitor.update.UpdateManagerResolver;
import com.rolster.capacitor.update.UpdateManagerUtils;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\rolster\capacitor\update\google\GoogleUpdateManagerResolver.smali */
public class GoogleUpdateManagerResolver implements UpdateManagerResolver {
    private AppUpdateManager appUpdateGoogle;
    private final Context context;

    public GoogleUpdateManagerResolver(Context context) {
        this.context = context;
    }

    @Override // com.rolster.capacitor.update.UpdateManagerResolver
    public void execute(final PluginCall call, final int numberApp) {
        this.appUpdateGoogle = AppUpdateManagerFactory.create(this.context);
        final JSObject result = new JSObject();
        this.appUpdateGoogle.getAppUpdateInfo().addOnSuccessListener(new OnSuccessListener() { // from class: com.rolster.capacitor.update.google.GoogleUpdateManagerResolver$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.tasks.OnSuccessListener
            public final void onSuccess(Object obj) {
                GoogleUpdateManagerResolver.lambda$execute$0(PluginCall.this, result, numberApp, (AppUpdateInfo) obj);
            }
        }).addOnFailureListener(new OnFailureListener() { // from class: com.rolster.capacitor.update.google.GoogleUpdateManagerResolver$$ExternalSyntheticLambda1
            @Override // com.google.android.gms.tasks.OnFailureListener
            public final void onFailure(Exception exc) {
                GoogleUpdateManagerResolver.lambda$execute$1(JSObject.this, call, exc);
            }
        });
    }

    static /* synthetic */ void lambda$execute$0(PluginCall call, JSObject result, int numberApp, AppUpdateInfo appInfoGoogle) {
        if (appInfoGoogle.updateAvailability() == 2) {
            try {
                int numberStore = appInfoGoogle.availableVersionCode();
                int splitCount = call.getInt("splitCount", 2).intValue();
                int minorMandatory = call.getInt("minorMandatory", 2).intValue();
                int patchMandatory = call.getInt("patchMandatory", 4).intValue();
                result.put(NotificationCompat.CATEGORY_STATUS, UpdateManagerUtils.getStatusUpdate(numberStore, numberApp, minorMandatory, patchMandatory, splitCount));
                result.put("versionNumber", numberStore);
            } catch (Exception error) {
                result.put(NotificationCompat.CATEGORY_STATUS, Constants.IPC_BUNDLE_KEY_SEND_ERROR);
                result.put("msgError", error.getMessage());
            }
        } else {
            result.put(NotificationCompat.CATEGORY_STATUS, "unnecessary");
        }
        call.resolve(result);
    }

    static /* synthetic */ void lambda$execute$1(JSObject result, PluginCall call, Exception error) {
        result.put(NotificationCompat.CATEGORY_STATUS, Constants.IPC_BUNDLE_KEY_SEND_ERROR);
        result.put("msgError", error.getMessage());
        call.resolve(result);
    }
}
