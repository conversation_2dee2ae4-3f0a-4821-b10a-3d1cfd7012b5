package com.vasco.digipass.sdk.utils.utilities.wbc;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import cz.muni.fi.xklinex.whiteboxAES.State;
import cz.muni.fi.xklinex.whiteboxAES.T1Box;
import cz.muni.fi.xklinex.whiteboxAES.T2Box;
import cz.muni.fi.xklinex.whiteboxAES.T3Box;
import cz.muni.fi.xklinex.whiteboxAES.XORBox;
import cz.muni.fi.xklinex.whiteboxAES.XORBoxState;
import cz.muni.fi.xklinex.whiteboxAES.XORCascade;
import cz.muni.fi.xklinex.whiteboxAES.XORCascadeState;
import cz.muni.fi.xklinex.whiteboxAES.generator.Bijection4x4;
import cz.muni.fi.xklinex.whiteboxAES.generator.ExternalBijections;
import cz.muni.fi.xklinex.whiteboxAES.generator.GF2MatrixEx;
import cz.muni.fi.xklinex.whiteboxAES.generator.LinearBijection;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\wbc\Serializer.smali */
public class Serializer {
    private final Kryo a;

    public Serializer() {
        Kryo kryo = new Kryo();
        this.a = kryo;
        kryo.register(WBCTable.class);
        kryo.register(ExternalBijections.class);
        kryo.register(LinearBijection[].class);
        kryo.register(LinearBijection.class);
        kryo.register(GF2MatrixEx.class);
        kryo.register(int[][].class);
        kryo.register(int[].class);
        kryo.register(byte[][].class);
        kryo.register(byte[].class);
        kryo.register(long[].class);
        kryo.register(Bijection4x4[][].class);
        kryo.register(Bijection4x4[].class);
        kryo.register(Bijection4x4.class);
        kryo.register(T1Box[][].class);
        kryo.register(T1Box[].class);
        kryo.register(T1Box.class);
        kryo.register(T2Box[][].class);
        kryo.register(T2Box[].class);
        kryo.register(T2Box.class);
        kryo.register(T3Box[][].class);
        kryo.register(T3Box[].class);
        kryo.register(T3Box.class);
        kryo.register(State[].class);
        kryo.register(State.class);
        kryo.register(XORCascade[][].class);
        kryo.register(XORCascade[].class);
        kryo.register(XORCascade.class);
        kryo.register(XORCascadeState[].class);
        kryo.register(XORCascadeState.class);
        kryo.register(XORBox[].class);
        kryo.register(XORBox.class);
        kryo.register(XORBoxState[].class);
        kryo.register(XORBoxState.class);
    }

    public WBCTable deserialize(String str) throws UtilitiesSDKException {
        try {
            FileInputStream fileInputStream = new FileInputStream(str);
            try {
                WBCTable deserialize = deserialize(fileInputStream);
                fileInputStream.close();
                return deserialize;
            } finally {
            }
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID, e);
        }
    }

    public WBCTable deserializeResource(String str) throws UtilitiesSDKException {
        try {
            InputStream resourceAsStream = getClass().getResourceAsStream(str);
            try {
                if (resourceAsStream == null) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID);
                }
                WBCTable deserialize = deserialize(resourceAsStream);
                resourceAsStream.close();
                return deserialize;
            } finally {
            }
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID, e);
        }
    }

    public void serialize(WBCTable wBCTable, String str) throws UtilitiesSDKException {
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(str);
            try {
                serialize(wBCTable, fileOutputStream);
                fileOutputStream.close();
            } finally {
            }
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID, e);
        }
    }

    public WBCTable deserialize(InputStream inputStream) throws UtilitiesSDKException {
        try {
            Input input = new Input(inputStream);
            try {
                WBCTable wBCTable = (WBCTable) this.a.readObject(input, WBCTable.class);
                input.close();
                return wBCTable;
            } finally {
            }
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID, e);
        }
    }

    public void serialize(WBCTable wBCTable, OutputStream outputStream) throws UtilitiesSDKException {
        try {
            Output output = new Output(outputStream);
            try {
                this.a.writeObject(output, wBCTable);
                output.flush();
                output.close();
            } finally {
            }
        } catch (Exception e) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.WBC_INPUT_ARRAY_INVALID, e);
        }
    }
}
