package o.dy;

import android.text.TextUtils;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dy\a.smali */
public final class a implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final a a;
    private static int c;
    private static int d;
    private static int e;

    static void a() {
        e = 874635460;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = 109 - r9
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r0 = o.dy.a.$$a
            int r7 = r7 * 2
            int r7 = 4 - r7
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r9
            r4 = r2
            r9 = r8
            r8 = r7
            goto L2e
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2e:
            int r3 = -r3
            int r7 = r7 + r3
            int r8 = r8 + 1
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.a.f(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{79, Tnaf.POW_2_WIDTH, 60, 65};
        $$b = 218;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        a();
        a = new a();
        int i = c + 27;
        d = i % 128;
        switch (i % 2 != 0 ? '\\' : (char) 21) {
            case 21:
                return;
            default:
                int i2 = 28 / 0;
                return;
        }
    }

    private a() {
    }

    @Override // o.dy.e
    public final String e() {
        Object obj;
        int i = d + 25;
        c = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                b(0 / (ViewConfiguration.getKeyRepeatTimeout() << 114), "\ufff7\b\u0002", 2 / TextUtils.indexOf((CharSequence) "", '\\', 0, 0), 2081 >>> (ViewConfiguration.getEdgeSlop() << 10), true, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                b((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 1, "\ufff7\b\u0002", TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 4, (ViewConfiguration.getEdgeSlop() >> 16) + 295, false, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = d + 7;
        c = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return intern;
            default:
                int i3 = 25 / 0;
                return intern;
        }
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        b(43 - ExpandableListView.getPackedPositionChild(0L), "\u0001\u0015\uffff\n\u0013￮\uffff�\f\u000f\t￭\uffff�\f\u000f\t\r\uffff￬\uffff\u000e\t\u0007\uffff￬\r\b\t\u0003\u000e\u0003\ufffe\b\t\uffdd\ufffe\bￛ\r\u0007\f\uffff￮\uffc1ￗ\u0012\u0003\u0000\uffff\f￪\u000e\uffff", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 53, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 289, true, objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(e()).append('\'').append('}').toString();
        int i = c + 47;
        d = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void b(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 504
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dy.a.b(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
