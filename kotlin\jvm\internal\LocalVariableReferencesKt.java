package kotlin.jvm.internal;

import kotlin.Metadata;

/* compiled from: localVariableReferences.kt */
@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0010\u0001\n\u0000\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0002¨\u0006\u0002"}, d2 = {"notSupportedError", "", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\internal\LocalVariableReferencesKt.smali */
public final class LocalVariableReferencesKt {
    /* JADX INFO: Access modifiers changed from: private */
    public static final Void notSupportedError() {
        throw new UnsupportedOperationException("Not supported for local property reference.");
    }
}
