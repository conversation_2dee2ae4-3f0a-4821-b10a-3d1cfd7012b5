package com.google.android.gms.tapandpay.quickaccesswallet;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\QuickAccessWalletConfig.smali */
public final class QuickAccessWalletConfig extends AbstractSafeParcelable {
    public static final Parcelable.Creator<QuickAccessWalletConfig> CREATOR = new zzh();
    private int zza;
    private int zzb;
    private int zzc;
    private String[] zzd;

    private QuickAccessWalletConfig() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof QuickAccessWalletConfig) {
            QuickAccessWalletConfig quickAccessWalletConfig = (QuickAccessWalletConfig) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(quickAccessWalletConfig.zza)) && Objects.equal(Integer.valueOf(this.zzb), Integer.valueOf(quickAccessWalletConfig.zzb)) && Objects.equal(Integer.valueOf(this.zzc), Integer.valueOf(quickAccessWalletConfig.zzc)) && Arrays.equals(this.zzd, quickAccessWalletConfig.zzd)) {
                return true;
            }
        }
        return false;
    }

    public int getCardHeightPx() {
        return this.zzb;
    }

    public int getCardWidthPx() {
        return this.zza;
    }

    public String[] getCurrentWalletCardIds() {
        return this.zzd;
    }

    public int getMaxCards() {
        return this.zzc;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), Integer.valueOf(this.zzb), Integer.valueOf(this.zzc), Integer.valueOf(Arrays.hashCode(this.zzd)));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 1, getCardWidthPx());
        SafeParcelWriter.writeInt(dest, 2, getCardHeightPx());
        SafeParcelWriter.writeInt(dest, 3, getMaxCards());
        SafeParcelWriter.writeStringArray(dest, 5, getCurrentWalletCardIds(), false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    QuickAccessWalletConfig(int i, int i2, int i3, String[] strArr) {
        this.zza = i;
        this.zzb = i2;
        this.zzc = i3;
        this.zzd = strArr;
    }

    /* synthetic */ QuickAccessWalletConfig(zzg zzgVar) {
    }
}
