package com.google.android.gms.internal.auth;

import android.accounts.Account;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\internal\auth\zzah.smali */
class zzah extends com.google.android.gms.auth.account.zza {
    zzah() {
    }

    public void zzb(Account account) {
        throw new UnsupportedOperationException();
    }

    public void zzc(boolean z) {
        throw new UnsupportedOperationException();
    }
}
