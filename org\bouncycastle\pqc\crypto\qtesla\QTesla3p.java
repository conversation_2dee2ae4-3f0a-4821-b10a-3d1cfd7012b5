package org.bouncycastle.pqc.crypto.qtesla;

import android.support.v4.media.session.PlaybackStateCompat;
import androidx.work.Data;
import com.esotericsoftware.asm.Opcodes;
import java.security.SecureRandom;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import org.bouncycastle.util.Arrays;
import org.bouncycastle.util.Pack;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\pqc\crypto\qtesla\QTesla3p.smali */
class QTesla3p {
    static final int CRYPTO_BYTES = 5664;
    private static final int CRYPTO_C_BYTES = 32;
    static final int CRYPTO_PUBLICKEYBYTES = 38432;
    private static final int CRYPTO_RANDOMBYTES = 32;
    static final int CRYPTO_SECRETKEYBYTES = 12392;
    private static final int CRYPTO_SEEDBYTES = 32;
    private static final int HM_BYTES = 40;
    private static final int PARAM_B = 2097151;
    private static final int PARAM_BARR_DIV = 32;
    private static final long PARAM_BARR_MULT = 5;
    private static final int PARAM_B_BITS = 21;
    private static final int PARAM_D = 24;
    private static final int PARAM_E = 901;
    private static final int PARAM_GEN_A = 180;
    private static final int PARAM_H = 40;
    private static final int PARAM_K = 5;
    private static final int PARAM_KEYGEN_BOUND_E = 901;
    private static final int PARAM_KEYGEN_BOUND_S = 901;
    private static final int PARAM_N = 2048;
    private static final int PARAM_Q = 856145921;
    private static final long PARAM_QINV = 587710463;
    private static final int PARAM_Q_LOG = 30;
    private static final int PARAM_R2_INVN = 513161157;
    private static final int PARAM_S = 901;
    private static final int RADIX32 = 32;
    private static final int maskb1 = 4194303;
    private static int NBLOCKS_SHAKE = 56;
    private static int BPLUS1BYTES = 3;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\pqc\crypto\qtesla\QTesla3p$Gaussian.smali */
    static class Gaussian {
        private static final int CDT_COLS = 4;
        private static final int CDT_ROWS = 111;
        private static final int CHUNK_SIZE = 512;
        private static final long[] cdt_v = {0, 0, 0, 0, 100790826, 671507412, 773522316, 511048871, 300982266, 372236861, 1077361076, 1059759409, 497060329, 1131554536, 291412424, 1757704870, 686469725, 80027618, 334905296, 509001536, 866922278, 352172656, 1712883727, 2032986430, 1036478428, 1164298591, 2125219728, 373247385, 1193606242, 860014474, 956855218, 329329358, 1337215220, 1378472045, 161925195, 487836429, 1466664345, 1948467327, 310898223, 2073068886, 1581745882, 839957238, 1753315676, 333401945, 1682648210, 1125857606, 1676773593, 581940958, 1769902286, 2009293507, 1611293310, 1722736534, 1844317078, 664324558, 343578873, 658669348, 1906909508, 1466301668, 267406484, 1947745304, 1958834133, 506071440, 334189663, 484025005, 2001317010, 234057451, 1095117285, 1717635665, 2035597220, 671584905, 701870375, 699831809, 2062878330, 786178128, 726164822, 1136608868, 2084290940, 306011770, 1674894630, 991835989, 2100866422, 714310105, 488689716, 2009111637, 2113521119, 243698855, 593655431, 2022065187, 2123049658, 417712144, 2089137196, 2060028876, 2130125692, 9470578, 1028331278, 1020621539, 2135308229, 1840927013, 1526892012, 1181097657, 2139051783, 1246948843, 1357704369, 1938086978, 2141718732, 589890968, 1884256845, 139665748, 2143592579, 1774056148, 1941338919, 987903917, 2144891082, 1109874007, 1865080884, 1313538965, 2145778525, 1056451611, 627977904, 165948177, 2146376698, 1812177762, 120802260, 582398303, 2146774350, 829172876, 477603011, 473601870, 2147035066, 313414830, 1824672086, 1000973284, 2147203651, 1956430050, 72036456, 1645155546, 2147311165, 1160031633, 838355487, 1294184273, 2147378788, 1398244788, 1748803166, 735598559, 2147420737, 187242113, 481611375, 850597049, 2147446401, 321666415, 1283944908, 1732385397, 2147461886, 1304194029, 1317422290, 1364763144, 2147471101, 2048797972, 754517418, 2042604505, 2147476510, 1282326805, 761564954, 1258404414, 2147479641, 831849416, 750696810, 1392469358, 2147481428, 1574767936, 164655622, 1811513013, 2147482435, 194943010, 1532513248, 1232399747, 2147482993, 1991776993, 1016296796, 929006971, 2147483299, 2120655340, 1043603591, 1156388502, 2147483465, 653713808, 2117477881, 793729097, 2147483553, 799217300, 223606404, 1502813197, 2147483599, 1380433608, 1556219701, 964487526, 2147483623, 1329670086, 1687316360, 30404454, 2147483635, 1873439229, 648445379, 536487208, 2147483642, 103862386, 2057355703, 2123410150, 2147483645, 254367675, 987554278, 1513167912, 2147483646, 1339200561, 1680472557, 73742693, 2147483647L, 754636301, 1391297233, 642078849, 2147483647L, 1499965743, 1552263981, 411055038, 2147483647L, 1850514942, 1987677871, 230734437, 2147483647L, 2013121736, 424671660, 742369365, 2147483647L, 2087512222, 33167986, 1815987211, 2147483647L, 2121077102, 1602401106, 1471578062, 2147483647L, 2136013361, 830825881, 1469956683, 2147483647L, 2142568585, 1139166133, 2135526978, 2147483647L, 2145405996, 1456208623, 2143928671, 2147483647L, 2146617280, 1565562420, 1104980011, 2147483647L, 2147127267, 620102157, 2069447681, 2147483647L, 2147339035, 757681075, 487993791, 2147483647L, 2147425761, 1567995652, 842732286, 2147483647L, 2147460790, 2123906694, 590947894, 2147483647L, 2147474745, 43829804, 56586263, 2147483647L, 2147480227, 543014779, 389906320, 2147483647L, 2147482351, 1064899675, 1680801885, 2147483647L, 2147483163, 598919140, 1822561999, 2147483647L, 2147483469, 513121855, 1244248835, 2147483647L, 2147483582, 2082722777, 1910280932, 2147483647L, 2147483624, 1427178629, 236458978, 2147483647L, 2147483639, 1589410683, 771680546, 2147483647L, 2147483645, 249235829, 211752505, 2147483647L, 2147483647L, 14524833, 1843948192, 2147483647L, 2147483647L, 1422880135, 37691207, 2147483647L, 2147483647L, 1904672618, 1534181260, 2147483647L, 2147483647L, 2067226300, 1987742172, 2147483647L, 2147483647L, 2121317001, 1156304554, 2147483647L, 2147483647L, 2139068597, 1154016330, 2147483647L, 2147483647L, 2144814274, 1150215588, 2147483647L, 2147483647L, 2146648421, 8934864, 2147483647L, 2147483647L, 2147225872, 1967808694, 2147483647L, 2147483647L, 2147405175, 1153330506, 2147483647L, 2147483647L, 2147460084, 1946199870, 2147483647L, 2147483647L, 2147476669, 289637131, 2147483647L, 2147483647L, 2147481609, 439898201, 2147483647L, 2147483647L, 2147483060, 1103759252, 2147483647L, 2147483647L, 2147483481, 50312709, 2147483647L, 2147483647L, 2147483601, 406195853, 2147483647L, 2147483647L, 2147483635, 120196762, 2147483647L, 2147483647L, 2147483644, 1008467632, 2147483647L, 2147483647L, 2147483647L, 107920605, 2147483647L, 2147483647L, 2147483647L, 1606293274, 2147483647L, 2147483647L, 2147483647L, 2005841951, 2147483647L, 2147483647L, 2147483647L, 2110919166, 2147483647L, 2147483647L, 2147483647L, 2138173553, 2147483647L, 2147483647L, 2147483647L, 2145145487, 2147483647L, 2147483647L, 2147483647L, 2146904460, 2147483647L, 2147483647L, 2147483647L, 2147342137, 2147483647L, 2147483647L, 2147483647L, 2147449546, 2147483647L, 2147483647L, 2147483647L, 2147475542, 2147483647L, 2147483647L, 2147483647L, 2147481747, 2147483647L, 2147483647L, 2147483647L, 2147483208, 2147483647L, 2147483647L, 2147483647L, 2147483548, 2147483647L, 2147483647L, 2147483647L, 2147483625, 2147483647L, 2147483647L, 2147483647L, 2147483643, 2147483647L, 2147483647L, 2147483647L, 2147483647L};

        Gaussian() {
        }

        static void sample_gauss_poly(int i, byte[] bArr, int i2, long[] jArr, int i3) {
            int i4 = i << 8;
            byte[] bArr2 = new byte[8192];
            int[] iArr = new int[4];
            int i5 = 0;
            int i6 = 0;
            while (i6 < 2048) {
                int i7 = i4 + 1;
                HashUtils.customizableSecureHashAlgorithmKECCAK256Simple(bArr2, 0, 8192, (short) i4, bArr, i2, 32);
                int i8 = i5;
                while (i8 < 512) {
                    int i9 = i3 + i6 + i8;
                    jArr[i9] = 0;
                    int i10 = 1;
                    while (i10 < 111) {
                        int i11 = 3;
                        int i12 = i5;
                        while (i11 >= 0) {
                            int at = (int) ((QTesla3p.at(bArr2, i5, (i8 * 4) + i11) & Integer.MAX_VALUE) - (cdt_v[(i10 * 4) + i11] + i12));
                            iArr[i11] = at;
                            i12 = at >> 31;
                            i11--;
                            i6 = i6;
                            i5 = 0;
                        }
                        jArr[i9] = jArr[i9] + ((~i12) & 1);
                        i10++;
                        i5 = 0;
                    }
                    int at2 = QTesla3p.at(bArr2, 0, i8 * 4) >> 31;
                    long j = jArr[i9];
                    jArr[i9] = (at2 & (-j)) | (j & (~at2));
                    i8++;
                    i5 = 0;
                    i6 = i6;
                }
                i6 += 512;
                i5 = i5;
                i4 = i7;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\pqc\crypto\qtesla\QTesla3p$QTesla3PPolynomial.smali */
    static class QTesla3PPolynomial {
        private static final long[] zeta = {147314272, 762289503, 284789571, 461457674, 723990704, 123382358, 685457283, 458774590, 644795450, 723622678, 441493948, 676062368, 648739792, 214990524, 261899220, 138474554, 205277234, 788000393, 541334956, 769530525, 786231394, 812002793, 251385069, 152717354, 674883688, 458756880, 323745289, 823881240, 686340396, 716163820, 107735873, 144028791, 586327243, 71257244, 739303131, 487030542, 313626215, 396596783, 664640087, 728258996, 854656117, 567834989, 2315110, 210792230, 795895843, 433034260, 432732757, 480454055, 750130006, 47628047, 2271301, 98590211, 729637734, 683553815, 476917424, 121851414, 296210757, 820475433, 403416438, 605633242, 804828963, 435181077, 781182803, 276684653, 329135201, 697859430, 248472020, 396579594, 109340098, 97605675, 755271019, 565755143, 534799496, 378374148, 85686225, 298978496, 650100484, 712463562, 818417023, 283716467, 269132585, 153024538, 223768950, 331863760, 761523727, 586019306, 805044248, 810909760, 77905343, 401203343, 162625701, 616243024, 659789238, 385270982, 720521140, 545633566, 688663167, 740046782, 257189758, 115795491, 101106443, 409863172, 622399622, 405606434, 498832246, 730567206, 350755879, 41236295, 561547732, 525723591, 18655497, 3396399, 289694332, 221478904, 738940554, 769726362, 32128402, 693016435, 275431006, 65292213, 601823865, 469363520, 480544944, 607230206, 473150754, 267072604, 463615065, 412972775, 197544577, 770873783, 189036815, 407973558, 110878446, 442760341, 667560342, 756992079, 663708407, 585601880, 763637579, 660019224, 424935088, 249313490, 844593983, 664952705, 274981537, 40233161, 655530034, 742724096, 8926394, 67709207, 616610795, 539664358, 306118645, 741629065, 283521858, 621397947, 369041534, 162477412, 258256937, 269480966, 75469364, 815614830, 724060729, 510819743, 489239410, 265607303, 103024793, 434961090, 474838542, 234701483, 505818866, 450427360, 188113529, 650423376, 599263141, 720479782, 755079140, 469798456, 745591660, 432033717, 530128582, 94480771, 722477467, 169342233, 35413255, 89769525, 424389771, 240236288, 360665614, 66702784, 76128663, 565345206, 605031892, 393503210, 249841967, 485930917, 45880284, 746120091, 684031522, 537926896, 408749937, 608644803, 692593939, 515424474, 748771159, 155377700, 347101257, 393516280, 708186062, 809233270, 562547654, 768251664, 651110951, 574473323, 588028067, 352359235, 646902518, 410726541, 134129459, 460099853, 829152883, 819102028, 7270760, 562515302, 419641762, 347973450, 161011009, 401974733, 619807719, 559105457, 276126568, 165473862, 380215069, 356617900, 347744328, 615885981, 824819772, 811367929, 6451967, 515345658, 648239021, 56427040, 709160497, 71545092, 390921213, 17177139, 194174898, 825533429, 497469884, 88988508, 64227614, 641021859, 159258883, 529265733, 823190295, 567280997, 414094239, 238392498, 695610059, 416342151, 90807038, 206865379, 568337348, 168011486, 844375038, 777332780, 147582038, 199025846, 396231915, 151630666, 466807217, 12672521, 570774644, 764098787, 283719496, 779154504, 383628791, 851035387, 395488461, 291115871, 52707730, 776449280, 479801706, 73403989, 402014636, 255214342, 56904698, 446531030, 639487570, 848061696, 202732901, 739018922, 653983847, 453022791, 391722680, 584290855, 270911670, 390838431, 653070075, 535876472, 83207555, 131151682, 505677504, 778583044, 472363568, 734419459, 768500943, 321131696, 371745445, 751887879, 51797676, 157604159, 838805925, 358099697, 763440819, 776721566, 719570904, 304610785, 656838485, 239522278, 796234199, 659506535, 825373307, 674901303, 250484891, 54612517, 410236408, 111976920, 728940855, 720463104, 559960962, 514189554, 637176165, 436151981, 485801800, 802811374, 549456481, 808832355, 112672706, 199163132, 807410080, 645955491, 365378122, 222316474, 381896744, 693909930, 402130292, 199856804, 277639257, 6848838, 648262319, 601521139, 108516632, 392382841, 563420106, 475932203, 249861415, 99274558, 152886431, 744977783, 269184267, 562674804, 760959275, 733098096, 771348891, 674288361, 631521272, 513632066, 476339117, 621937967, 206834230, 507101607, 420341698, 528715580, 853092790, 580174958, 278044321, 432350205, 603769437, 144426940, 733518338, 365468467, 848983278, 385382826, 846062026, 593903051, 216589699, 219997638, 350708517, 733669279, 624754239, 499821820, 772548008, 199677439, 287505007, 144199205, 215073292, 825467700, 101591831, 571728784, 841898341, 420897808, 61323616, 823475752, 72494861, 89946011, 236594097, 379582577, 539401967, 221244669, 479250487, 100726882, 263096036, 647161225, 491060387, 419890898, 816149055, 546441322, 690509770, 215789647, 5870948, 821456387, 294091098, 783700004, 278643020, 520754327, 813718894, 123610053, 157045201, 265331664, 807174256, 258134244, 703519669, 300265991, 41892125, 662173055, 439638698, 494124024, 700655120, 535348417, 37146186, 379568907, 644973451, 554904963, 594757858, 477812802, 266085643, 46337543, 454847754, 496027901, 701947604, 5722633, 790588605, 233501932, 728956461, 462020148, 214013660, 155806979, 159935426, 423504958, 638889309, 602641304, 277759403, 71654804, 710920410, 108337831, 641924564, 252946326, 463082282, 23277660, 142056200, 263317553, 9044238, 367816044, 349695658, 291597086, 230031083, 385106216, 281069679, 644033142, 134221740, 212497862, 686686078, 787489098, 781698667, 748299513, 774414792, 380836293, 114027649, 766161763, 10536612, 707355910, 100516219, 637517297, 21478533, 769067854, 668364559, 410803198, 64949715, 643421522, 525590993, 585289785, 423839840, 554109325, 450599860, 295350132, 435789550, 306634115, 611298620, 777817576, 553655202, 804525538, 794474290, 138542076, 780958763, 62228371, 738032107, 684994110, 661486955, 67099069, 68865906, 32413094, 358393763, 205008770, 849715545, 289798348, 384767209, 787328590, 823677120, 47455925, 706001331, 612392717, 487804928, 731804935, 520572665, 442307581, 351275150, 726042356, 667657829, 254929787, 459520026, 625393223, 319307882, 77267096, 815224795, 335964550, 408353208, 604252110, 574953308, 563501897, 515015302, 313600371, 178773384, 417549087, 510834475, 167049599, 488791556, 664276219, 82933775, 822541833, 17111190, 409659978, 96304098, 500484311, 269766378, 327037310, 584926256, 538611363, 404132255, 170931824, 744460626, 154011192, 322194096, 215888234, 258344560, 702851111, 192046250, 738511820, 530780560, 57197515, 335425579, 410968369, 830078545, 448351649, 208921555, 356653676, 718038774, 424362596, 158929491, 420096666, 387056270, 797383293, 381201911, 466480709, 373815662, 84912008, 4969808, 524614597, 93448903, 559481007, 400813998, 665223025, 601707338, 466022707, 192709574, 615503265, 822863744, 639854175, 158713505, 12757666, 389196370, 823105438, 682974863, 468401586, 93508626, 402414043, 806357152, 180544963, 27876186, 321527031, 329857607, 669501423, 829809824, 333202822, 106923493, 368991112, 282317903, 790323774, 517381333, 548329656, 236147848, 700119793, 404187488, 343578810, 798813301, 497964535, 656188346, 678161787, 736817175, 518031339, 716647183, 674797219, 308643560, 714308544, 516103468, 605229646, 564549717, 47650358, 706404486, 494887760, 152496104, 54954356, 271435602, 76951527, 136123931, 601823638, 329273401, 252710411, 754980731, 351648254, 49239731, 837833233, 88830509, 598216539, 155534490, 669603727, 418388693, 79322074, 636251444, 703683994, 796989459, 126497707, 644863316, 730359063, 265213001, 64483814, 552208981, 8135537, 782474322, 780853310, 733976806, 395661138, 128188419, 266691358, 407092046, 447349747, 526245954, 119272088, 359659635, 812410956, 669835517, 565139408, 248981831, 139910745, 685462294, 406991131, 709944045, 589819925, 714299787, 72923680, 648836181, 145321778, 392775383, 243093077, 412955839, 174619485, 310936394, 699727061, 421087619, 745421519, 539546394, 29471558, 116471631, 852650639, 443777703, 773131303, 81618669, 756719012, 702785073, 847088653, 851830586, 300908692, 430974543, 463215976, 668971423, 414271988, 108350516, 345933325, 716417649, 174980945, 679092437, 384030489, 814050910, 506580116, 249434097, 178438885, 146797119, 10369463, 296359082, 215645133, 149545847, 483689845, 322009569, 308978588, 38531178, 328571637, 815396967, 709744233, 765487128, 645413104, 564779557, 213794315, 280607549, 124792697, 423470554, 631348430, 21223627, 220718413, 598791979, 47797633, 734556299, 590321944, 168292920, 484802055, 340999812, 769601438, 42675060, 116026587, 227462622, 543574607, 444066479, 467277895, 278798674, 597413704, 350168725, 301936652, 82885511, 656047519, 765110538, 52228202, 533005731, 621989298, 148235931, 317833915, 118463894, 522391939, 451332724, 548031654, 73854149, 527786213, 583308898, 840663438, 275278054, 362931963, 587861579, 830807449, 431695707, 178004048, 75513216, 60681147, 638603143, 470791469, 490903319, 527370962, 102981857, 224220555, 756514239, 293859807, 797926303, 620196520, 466126507, 646136763, 265504163, 213257337, 92270416, 398713724, 91810366, 724247342, 855386762, 631553083, 376095634, 833728623, 636218061, 510719408, 378530670, 737821436, 127781731, 3443282, 770116208, 769633348, 430675947, 40370755, 52361322, 844601468, 442556599, 128290354, 494328514, 405616679, 651440882, 421541290, 171560170, 386143493, 284277254, 450756213, 248305939, 526718005, 300780198, 714218239, 68021827, 527353904, 236472015, 309320156, 683815803, 527980097, 598849444, 779607597, 339852811, 845420163, 96001931, 326760873, 609319751, 520803868, 140143851, 766988701, 844896794, 532008178, 388459130, 574799295, 760406065, 773758517, 453271555, 134636434, 155747417, 105505251, 796987277, 399016325, 71156680, 709579308, 274279004, 96962867, 476741915, 585319990, 709143538, 721328791, 293159344, 640577897, 138404614, 572892015, 394460832, 465897068, 325895331, 413861636, 447337182, 376950267, 721061932, 181671909, 272138750, 247768905, 634973622, 280653872, 165108426, 134241779, 15142090, 153256717, 783424845, 773227607, 172477802, 504458250, 349868083, 461422806, 487725644, 586146740, 561546455, 815406759, 468110471, 126476456, 285774551, 522013234, 801943660, 79684345, 654558548, 188038414, 249923934, 551812615, 562560206, 407120348, 384535446, 176837117, 433155458, 82591339, 459412819, 435604627, 312211805, 98158590, 752137480, 446017293, 666480139, 60261988, 275386848, 642778031, 8582401, 677484160, 819506256, 333441964, 25465219, 190315429, 91529631, 754681170, 563660271, 167135649, 20270015, 115773732, 658954441, 132923202, 844102455, 453432758, 250487209, 423813160, 632223296, 537494486, 158265753, 327949044, 494109748, 659672289, 67984726, 422358258, 345141182, 164372996, 338500924, 41400311, 207638305, 832074651, 50853458, 228267776, 621895888, 635834787, 484972544, 181125024, 558134871, 282159878, 788157855, 145576343, 194837894, 501440949, 63641414, 252098681, 835930645, 662856247, 456140980, 206147937, 565198503, 449503819, 684013129, 494002381, 793836418, 649296754, 444313288, 136544068, 540002286, 355912945, 
        613175147, 134541429, 843111781, 672612536, 541098995, 734996181, 211869705, 620777828, 756152791, 242128346, 795442420, 73925532, 735232214, 738668090, 530800757, 266183732, 97165934, 803231879, 10057267, 175942047, 181460965, 320684297, 637472526, 213840116, 182671953, 152704513, 388004388, 597349323, 473851493, 445333546, 679315863, 267078568, 46538491, 530171754, 698082287, 75308587, 266467406, 96440883, 759196579, 470119952, 381731475, 428392158, 10628712, 173921356, 116809433, 323843928, 812172630, 403459283, 655501128, 261944441, 774418023, 790520709, 589149480, 264133112, 806274256, 752372117, 66236193, 713859568, 90804933, 551864345, 843839891, 600244073, 719230074, 803646506, 254956426, 138935723, 738829647, 109576220, 105819621, 249706947, 110623114, 10002331, 795710911, 547062229, 721440199, 820747461, 397666160, 685179945, 463869301, 470338753, 641244231, 652990696, 698429485, 41147155, 638072709, 515832968, 241130026, 314161759, 526815813, 529167244, 53391331, 782008115, 822962086, 337706389, 648197286, 209496506, 760818531, 781900302, 717270807, 709143641, 740503641, 734328409, 514061476, 844010670, 67993787, 712083588, 319801387, 338260400, 48758556, 304195768, 478833380, 841413917, 710197685, 196321647, 777595184, 775983866, 147506314, 620961439, 399972264, 398715644, 684489092, 659918078, 664075287, 723890579, 643103903, 508525962, 375409248, 501237729, 740609783, 639854810, 510797913, 521151016, 421045341, 193698327, 800266392, 93518128, 443879633, 699245445, 194001794, 123905867, 75572337, 242620749, 463111940, 755239011, 31718790, 162155292, 386689240, 381413538, 745322913, 367897558, 343088005, 31706107, 10842029, 404961623, 537521191, 281624684, 372852160, 55286017, 534907560, 264398082, 667644310, 486871690, 716964533, 734731419, 143593638, 293949413, 760014789, 594443755, 147804127, 537704286, 460110740, 596458323, 577775570, 333025386, 260094086, 711487611, 359384182, 323339045, 716675075, 248179763, 525311626, 76326208, 559009987, 548139736, 541721430, 31450329, 653923741, 676193285, 295171241, 558845563, 387079118, 403184480, 807941436, 501042343, 284608894, 705710380, 82388415, 763336555, 126077422, 438548854, 606252517, 144569238, 126964439, 809559381, 263253751, 547929033, 236704198, 377978058, 59501955, 749500335, 254242336, 605755194, 408388953, 116242711, 116340056, 691021496, 48100285, 371076069, 638156108, 211570763, 185945242, 653505761, 667569173, 335131755, 736662207, 572078378, 755939949, 840393623, 322934679, 520522390, 252068808, 491370519, 200565770, 552637112, 182345569, 394747039, 822229467, 817698102, 644484388, 156591766, 729600982, 695826242, 509682463, 785132583, 746139100, 188369785, 628995003, 406654440, 650660075, 676485042, 540766742, 493428142, 753346328, 82608613, 670846442, 145894970, 770907988, 621807160, 14676199, 793865193, 36579515, 619741404, 303691972, 794920577, 134684826, 190038753, 538889970, 836657477, 643017556, 316870164, 464572481, 305395359, 446406992, 587814221, 423552502, 122802120, 146043780, 173756097, 130720237, 445515559, 109884833, 133119099, 804139234, 834841519, 458514524, 74213698, 490363622, 119287122, 165016718, 351506713, 433750226, 439149867, 348281119, 319795826, 320785867, 446561207, 705678831, 714536161, 172299381, 552925586, 635421942, 851853231, 208071525, 142303096, 93164236, 207534795, 655906672, 558127940, 98870558, 388322132, 87475979, 835970665, 61996500, 298060757, 256194194, 563529863, 249184704, 451295997, 73892211, 559049908, 44006160, 832886345, 720732161, 255948582, 827295342, 629663637, 323103159, 155698755, 598913314, 586685341, 761273875, 135225209, 324099714, 391112815, 493469140, 796490769, 667498514, 148390126, 721802249, 781884558, 309264043, 603401759, 503111668, 563611748, 363342598, 383209405, 108340736, 758017880, 145907493, 312330194, 608895549, 45540348, 143092704, 772401556, 806068040, 853177536, 662120004, 463347842, 495085709, 560431884, 274002454, 76985308, 519320299, 253092838, 727478114, 593752634, 490277266, 206283832, 701277908, 504787112, 816832531, 730997507, 27807749, 58254704, 584933136, 515463756, 241104222, 251881934, 566567573, 592887586, 528932268, 88111104, 523103099, 448331392, 351083975, 157811347, 758866581, 802151021, 843579185, 481417280, 507414106, 462708367, 461501222, 790988186, 462220673, 727683888, 159759683, 59757110, 310746434, 326369241, 305829588, 457718309, 529317279, 503631310, 661769334, 343160359, 472216278, 740498212, 11312284, 760170115, 513391009, 538224236, 710934956, 491998229, 539829044, 610387964, 86624968, 72542777, 493966272, 132327984, 371526334, 182549152, 51622114, 173997077, 550633787, 205437301, 435219235, 406409162, 414751325, 33371226, 40899348, 77245052, 763383124, 817701136, 598256078, 357440859, 468418959, 353612800, 721601331, 262567156, 521577430, 232027892, 75986872, 443113391, 107360999, 482079354, 563502258, 782475535, 402866161, 515580626, 742688144, 677398836, 425899303, 42066550, 537192943, 430672016, 115368023, 64053241, 92008456, 74327791, 572607165, 681138002, 378104858, 695786430, 844827190, 436817825, 751393351, 142965259, 81300919, 688342617, 433082724, 221191094, 712003270, 301076404, 747091407, 514191589, 814985450, 260951422, 187161058, 22316970, 806106670, 759397054, 158423624, 419813636, 462241316, 438231460, 108466764, 212745115, 386264342, 176072326, 767127195, 399981627, 762991681, 173125691, 464627163, 770046798, 179369718, 829917528, 693004603, 178596003, 422852852, 182684967, 662425026, 713404098, 766206683, 130088738, 321282752, 134898541, 86701214, 120555423, 464987852, 82865891, 758340585, 138256323, 308997895, 659614345, 510091933, 822699180, 464631718, 819896232, 120792059, 160708255, 462868879, 72974246, 260451492, 120601343, 228097712, 369436704, 155304088, 74380537, 732305166, 203294189, 307421597, 96510570, 634243454, 486539430, 16204477, 241987531, 317824421, 510180366, 794475492, 262770124, 441034891, 741864347, 205569410, 684844547, 340863522, 440616421, 454438375, 26285496, 141886125, 648947081, 3791510, 529746935, 317826713, 411458050, 661690316, 45696331, 679684665, 184597094, 829228068, 375683582, 591739456, 855242340, 628594662, 30968619, 363932244, 103091463, 614269714, 465960778, 791477766, 332731888, 853151007, 266045534, 132189407, 435008168, 65667470, 669304246, 760035868, 481409581, 36650645, 523634336, 702968013, 351902214, 284360680, 34261165, 593134528, 337534074, 239112910, 710342799, 163287447, 20209506, 780785984, 480727309, 125776519, 691236193, 603228570, 48261672, 183120677, 73638683, 3430616, 568026489, 808739797, 298585898, 64471573, 724550960, 568093636, 187449517, 655699449, 672689645, 829049456, 263525899, 612969883, 621652807, 186362075, 731851539, 377104257, 39335761, 210768226, 253965025, 201921517, 715681274, 369453531, 18897741, 612559390, 660723864, 476963596, 585483298, 318614839, 227626072, 298891387, 110505944, 814885802, 177563961, 443724544, 374856237, 577963338, 617516835, 475669105, 633353115, 12579943, 796644307, 569746680, 22381253, 343603333, 724567543, 845363898, 4023795, 801359177, 347489967, 214644600, 78674056, 131782857, 284041623, 660502381, 161470286, 668158595, 765738294, 715872268, 678418089, 280458288, 758715787, 9311288, 490771912, 757112000, 253990619, 698573830, 390611635, 52593584, 421202448, 494394112, 386893540, 29349323, 533111491, 774401558, 108660117, 405990553, 143728136, 852741683, 354532633, 440222591, 663461253, 593338391, 298882952, 758170600, 660294062, 332348846, 541714172, 77716403, 169377728, 71932929, 110210904, 776771173, 645222398, 162195941, 792388932, 502165627, 146897021, 243625970, 139123400, 462352793, 409369440, 247509680, 270865496, 539140627, 16949766, 245869282, 637926655, 37386603, 383033875, 316560876, 707909555, 367315004, 173821041, 529529257, 227507318, 831716891, 830055847, 228911074, 205127100, 178872273, 819938491, 129875615, 764680417, 97028082, 560682982, 433649390, 727508847, 494848582, 81279272, 435186566, 174468080, 69172161, 241860102, 692179355, 333985572, 788895276, 469576414, 594155471, 157828532, 182105752, 310394758, 673085082, 695719789, 39004854, 251000641, 98748282, 744318650, 815050298, 622456803, 240419561, 403871914, 202214044, 627433637, 649505808, 668918393, 334630440, 386856024, 352649543, 135139523, 216499252, 736376783, 269223150, 468318208, 801808348, 180378366, 640086372, 672618369, 291378195, 732195369, 805632553, 518515631, 603280165, 629836417, 59712833, 531020081, 708771168, 539819295, 179149444, 552251927, 458994127, 584987693, 238644928, 640603619, 46728500, 843989005, 688747457, 236924093, 261539965, 705411056, 765907765, 38095657, 382461698, 146650814, 351462947, 749417520, 628887925, 800857475, 790554154, 695483946, 160495923, 40896482, 471385785, 535516195, 197056285, 622795937, 368016917, 696525353, 377315918, 58087122, 246518254, 431338589, 795949654, 611141265, 406307405, 365750089, 396243561, 843849531, 33802729, 573076974, 557841126, 411725124, 109489622, 370935707, 372610558, 769825999, 367932152, 231499145, 240819898, 22648665, 418344529, 142438794, 552806180, 669450690, 614608056, 784369586, 258710636, 474742428, 166021530, 805595815, 603578176, 686703780, 412868426, 26588048, 379895115, 77550061, 751188758, 294447541, 433574579, 234362222, 821492181, 23912038, 681093196, 483584545, 404339808, 396405029, 744756742, 702481685, 413127074, 204115019, 187381271, 633523978, 433629465, 628184183, 783160918, 268799033, 646479372, 160458176, 602612912, 644506365, 391554011, 676966578, 386430153, 98736426, 412745127, 296141927, 685909285, 355152260, 361415843, 127323093, 586337666, 1734791, 368678692, 155431915, 597290023, 109507713, 291804866, 135016081, 144077689, 35054937, 16808265, 431962815, 534195521, 629326143, 309352001, 319948849, 443083246, 336744161, 100845182, 314804947, 476736581, 468528479, 416978018, 35141019, 43314058, 384847955, 665126798, 295857628, 768013680, 741182796, 157855570, 695547618, 145251639, 818473396, 708640763, 87460130, 736400748, 465173936, 376720282, 437268868, 137236663, 693860377, 247960644, 402124416, 656418852, 231401654, 248187016, 628418583, 224261112, 120581342, 49749199, 588812480, 309599954, 111357387, 14507354, 754564049, 513444423, 816496110, 509193085, 361635970, 190608265, 697367838, 230953561, 140447357, 27745100, 163340427, 607823059, 325305463, 383028479, 269707244, 475022415, 708990989, 738971809, 797646021, 126610937, 589310701, 191123172, 819715815, 337443183, 432224976, 337343783, 257301390, 172631141, 560659319, 646332329, 55110483, 467212803, 442977895, 311159578, 569890333, 669396086, 536323022, 542648615, 366162176, 88951009, 408335586, 276237497, 384733042, 525960156, 74199534, 338209206, 676233089, 264342641, 241682204, 226505461, 165013960, 129858819, 664852498, 432090291, 165700308, 382150900, 537002255, 368893910, 61006155, 238726881, 92317627, 632392147, 404715651, 802622348, 126100061, 
        306024238, 397891265, 214661020, 211132870, 783722518, 149847645, 665379914, 624725195, 85864665, 496272723, 304811252, 29995710, 410500887, 756406394, 31206753, 647154006, 596539568, 783214792, 286381882, 24560691, 681500270, 774933112, 506538708, 850347997, 611696036, 512607061, 251719669, 367108021, 456442965, 636694730, 399940257, 73870039, 85190759, 264953709, 238854238, 395048514, 612738126, 27417876, 652695826, 188238483, 324168828, 736238139, 789061724, 529275445, 382304068, 176318391, 709989466, 14237691};
        private static final long[] zetainv = {146156455, 679827530, 473841853, 326870476, 67084197, 119907782, 531977093, 667907438, 203450095, 828728045, 243407795, 461097407, 617291683, 591192212, 770955162, 782275882, 456205664, 219451191, 399702956, 489037900, 604426252, 343538860, 244449885, 5797924, 349607213, 81212809, 174645651, 831585230, 569764039, 72931129, 259606353, 208991915, 824939168, 99739527, 445645034, 826150211, 551334669, 359873198, 770281256, 231420726, 190766007, 706298276, 72423403, 645013051, 641484901, 458254656, 550121683, 730045860, 53523573, 451430270, 223753774, 763828294, 617419040, 795139766, 487252011, 319143666, 473995021, 690445613, 424055630, 191293423, 726287102, 691131961, 629640460, 614463717, 591803280, 179912832, 517936715, 781946387, 330185765, 471412879, 579908424, 447810335, 767194912, 489983745, 313497306, 319822899, 186749835, 286255588, 544986343, 413168026, 388933118, 801035438, 209813592, 295486602, 683514780, 598844531, 518802138, 423920945, 518702738, 36430106, 665022749, 266835220, 729534984, 58499900, 117174112, 147154932, 381123506, 586438677, 473117442, 530840458, 248322862, 692805494, 828400821, 715698564, 625192360, 158778083, 665537656, 494509951, 346952836, 39649811, 342701498, 101581872, 841638567, 744788534, 546545967, 267333441, 806396722, 735564579, 631884809, 227727338, 607958905, 624744267, 199727069, 454021505, 608185277, 162285544, 718909258, 418877053, 479425639, 390971985, 119745173, 768685791, 147505158, 37672525, 710894282, 160598303, 698290351, 114963125, 88132241, 560288293, 191019123, 471297966, 812831863, 821004902, 439167903, 387617442, 379409340, 541340974, 755300739, 519401760, 413062675, 536197072, 546793920, 226819778, 321950400, 424183106, 839337656, 821090984, 712068232, 721129840, 564341055, 746638208, 258855898, 700714006, 487467229, 854411130, 269808255, 728822828, 494730078, 500993661, 170236636, 560003994, 443400794, 757409495, 469715768, 179179343, 464591910, 211639556, 253533009, 695687745, 209666549, 587346888, 72985003, 227961738, 422516456, 222621943, 668764650, 652030902, 443018847, 153664236, 111389179, 459740892, 451806113, 372561376, 175052725, 832233883, 34653740, 621783699, 422571342, 561698380, 104957163, 778595860, 476250806, 829557873, 443277495, 169442141, 252567745, 50550106, 690124391, 381403493, 597435285, 71776335, 241537865, 186695231, 303339741, 713707127, 437801392, 833497256, 615326023, 624646776, 488213769, 86319922, 483535363, 485210214, 746656299, 444420797, 298304795, 283068947, 822343192, 12296390, 459902360, 490395832, 449838516, 245004656, 60196267, 424807332, 609627667, 798058799, 478830003, 159620568, 488129004, 233349984, 659089636, 320629726, 384760136, 815249439, 695649998, 160661975, 65591767, 55288446, 227257996, 106728401, 504682974, 709495107, 473684223, 818050264, 90238156, 150734865, 594605956, 619221828, 167398464, 12156916, 809417421, 215542302, 617500993, 271158228, 397151794, 303893994, 676996477, 316326626, 147374753, 325125840, 796433088, 226309504, 252865756, 337630290, 50513368, 123950552, 564767726, 183527552, 216059549, 675767555, 54337573, 387827713, 586922771, 119769138, 639646669, 721006398, 503496378, 469289897, 521515481, 187227528, 206640113, 228712284, 653931877, 452274007, 615726360, 233689118, 41095623, 111827271, 757397639, 605145280, 817141067, 160426132, 183060839, 545751163, 674040169, 698317389, 261990450, 386569507, 67250645, 522160349, 163966566, 614285819, 786973760, 681677841, 420959355, 774866649, 361297339, 128637074, 422496531, 295462939, 759117839, 91465504, 726270306, 36207430, 677273648, 651018821, 627234847, 26090074, 24429030, 628638603, 326616664, 682324880, 488830917, 148236366, 539585045, 473112046, 818759318, 218219266, 610276639, 839196155, 317005294, 585280425, 608636241, 446776481, 393793128, 717022521, 612519951, 709248900, 353980294, 63756989, 693949980, 210923523, 79374748, 745935017, 784212992, 686768193, 778429518, 314431749, 523797075, 195851859, 97975321, 557262969, 262807530, 192684668, 415923330, 501613288, 3404238, 712417785, 450155368, 747485804, 81744363, 323034430, 826796598, 469252381, 361751809, 434943473, 803552337, 465534286, 157572091, 602155302, 99033921, 365374009, 846834633, 97430134, 575687633, 177727832, 140273653, 90407627, 187987326, 694675635, 195643540, 572104298, 724363064, 777471865, 641501321, 508655954, 54786744, 852122126, 10782023, 131578378, 512542588, 833764668, 286399241, 59501614, 843565978, 222792806, 380476816, 238629086, 278182583, 481289684, 412421377, 678581960, 41260119, 745639977, 557254534, 628519849, 537531082, 270662623, 379182325, 195422057, 243586531, 837248180, 486692390, 140464647, 654224404, 602180896, 645377695, 816810160, 479041664, 124294382, 669783846, 234493114, 243176038, 592620022, 27096465, 183456276, 200446472, 668696404, 288052285, 131594961, 791674348, 557560023, 47406124, 288119432, 852715305, 782507238, 673025244, 807884249, 252917351, 164909728, 730369402, 375418612, 75359937, 835936415, 692858474, 145803122, 617033011, 518611847, 263011393, 821884756, 571785241, 504243707, 153177908, 332511585, 819495276, 374736340, 96110053, 186841675, 790478451, 421137753, 723956514, 590100387, 2994914, 523414033, 64668155, 390185143, 241876207, 753054458, 492213677, 825177302, 227551259, 903581, 264406465, 480462339, 26917853, 671548827, 176461256, 810449590, 194455605, 444687871, 538319208, 326398986, 852354411, 207198840, 714259796, 829860425, 401707546, 415529500, 515282399, 171301374, 650576511, 114281574, 415111030, 593375797, 61670429, 345965555, 538321500, 614158390, 839941444, 369606491, 221902467, 759635351, 548724324, 652851732, 123840755, 781765384, 700841833, 486709217, 628048209, 735544578, 595694429, 783171675, 393277042, 695437666, 735353862, 36249689, 391514203, 33446741, 346053988, 196531576, 547148026, 717889598, 97805336, 773280030, 391158069, 735590498, 769444707, 721247380, 534863169, 726057183, 89939238, 142741823, 193720895, 673460954, 433293069, 677549918, 163141318, 26228393, 676776203, 86099123, 391518758, 683020230, 93154240, 456164294, 89018726, 680073595, 469881579, 643400806, 747679157, 417914461, 393904605, 436332285, 697722297, 96748867, 50039251, 833828951, 668984863, 595194499, 41160471, 341954332, 109054514, 555069517, 144142651, 634954827, 423063197, 167803304, 774845002, 713180662, 104752570, 419328096, 11318731, 160359491, 478041063, 175007919, 283538756, 781818130, 764137465, 792092680, 740777898, 425473905, 318952978, 814079371, 430246618, 178747085, 113457777, 340565295, 453279760, 73670386, 292643663, 374066567, 748784922, 413032530, 780159049, 624118029, 334568491, 593578765, 134544590, 502533121, 387726962, 498705062, 257889843, 38444785, 92762797, 778900869, 815246573, 822774695, 441394596, 449736759, 420926686, 650708620, 305512134, 682148844, 804523807, 673596769, 484619587, 723817937, 362179649, 783603144, 769520953, 245757957, 316316877, 364147692, 145210965, 317921685, 342754912, 95975806, 844833637, 115647709, 383929643, 512985562, 194376587, 352514611, 326828642, 398427612, 550316333, 529776680, 545399487, 796388811, 696386238, 128462033, 393925248, 65157735, 394644699, 393437554, 348731815, 374728641, 12566736, 53994900, 97279340, 698334574, 505061946, 407814529, 333042822, 768034817, 327213653, 263258335, 289578348, 604263987, 615041699, 340682165, 271212785, 797891217, 828338172, 125148414, 39313390, 351358809, 154868013, 649862089, 365868655, 262393287, 128667807, 603053083, 336825622, 779160613, 582143467, 295714037, 361060212, 392798079, 194025917, 2968385, 50077881, 83744365, 713053217, 810605573, 247250372, 543815727, 710238428, 98128041, 747805185, 472936516, 492803323, 292534173, 353034253, 252744162, 546881878, 74261363, 134343672, 707755795, 188647407, 59655152, 362676781, 465033106, 532046207, 720920712, 94872046, 269460580, 257232607, 700447166, 533042762, 226482284, 28850579, 600197339, 135413760, 23259576, 812139761, 297096013, 782253710, 404849924, 606961217, 292616058, 599951727, 558085164, 794149421, 20175256, 768669942, 467823789, 757275363, 298017981, 200239249, 648611126, 762981685, 713842825, 648074396, 4292690, 220723979, 303220335, 683846540, 141609760, 150467090, 409584714, 535360054, 536350095, 507864802, 416996054, 422395695, 504639208, 691129203, 736858799, 365782299, 781932223, 397631397, 21304402, 52006687, 723026822, 746261088, 410630362, 725425684, 682389824, 710102141, 733343801, 432593419, 268331700, 409738929, 550750562, 391573440, 539275757, 213128365, 19488444, 317255951, 666107168, 721461095, 61225344, 552453949, 236404517, 819566406, 62280728, 841469722, 234338761, 85237933, 710250951, 185299479, 773537308, 102799593, 362717779, 315379179, 179660879, 205485846, 449491481, 227150918, 667776136, 110006821, 71013338, 346463458, 160319679, 126544939, 699554155, 211661533, 38447819, 33916454, 461398882, 673800352, 303508809, 655580151, 364775402, 604077113, 335623531, 533211242, 15752298, 100205972, 284067543, 119483714, 521014166, 188576748, 202640160, 670200679, 644575158, 217989813, 485069852, 808045636, 165124425, 739805865, 739903210, 447756968, 250390727, 601903585, 106645586, 796643966, 478167863, 619441723, 308216888, 592892170, 46586540, 729181482, 711576683, 249893404, 417597067, 730068499, 92809366, 773757506, 150435541, 571537027, 355103578, 48204485, 452961441, 469066803, 297300358, 560974680, 179952636, 202222180, 824695592, 314424491, 308006185, 297135934, 779819713, 330834295, 607966158, 139470846, 532806876, 496761739, 144658310, 596051835, 523120535, 278370351, 259687598, 396035181, 318441635, 708341794, 261702166, 96131132, 562196508, 712552283, 121414502, 139181388, 369274231, 188501611, 591747839, 321238361, 800859904, 483293761, 574521237, 318624730, 451184298, 845303892, 824439814, 513057916, 488248363, 110823008, 474732383, 469456681, 693990629, 824427131, 100906910, 393033981, 613525172, 780573584, 732240054, 662144127, 156900476, 412266288, 762627793, 55879529, 662447594, 435100580, 334994905, 345348008, 216291111, 115536138, 354908192, 480736673, 347619959, 213042018, 132255342, 192070634, 196227843, 171656829, 457430277, 456173657, 235184482, 708639607, 80162055, 78550737, 659824274, 145948236, 14732004, 377312541, 551950153, 807387365, 517885521, 536344534, 144062333, 788152134, 12135251, 342084445, 121817512, 115642280, 147002280, 138875114, 74245619, 95327390, 646649415, 207948635, 518439532, 33183835, 74137806, 802754590, 326978677, 329330108, 541984162, 615015895, 340312953, 218073212, 814998766, 157716436, 203155225, 214901690, 385807168, 392276620, 170965976, 458479761, 35398460, 134705722, 309083692, 60435010, 846143590, 745522807, 606438974, 750326300, 746569701, 117316274, 717210198, 601189495, 52499415, 136915847, 255901848, 12306030, 304281576, 765340988, 142286353, 789909728, 103773804, 49871665, 592012809, 266996441, 65625212, 81727898, 594201480, 200644793, 452686638, 
        43973291, 532301993, 739336488, 682224565, 845517209, 427753763, 474414446, 386025969, 96949342, 759705038, 589678515, 780837334, 158063634, 325974167, 809607430, 589067353, 176830058, 410812375, 382294428, 258796598, 468141533, 703441408, 673473968, 642305805, 218673395, 535461624, 674684956, 680203874, 846088654, 52914042, 758979987, 589962189, 325345164, 117477831, 120913707, 782220389, 60703501, 614017575, 99993130, 235368093, 644276216, 121149740, 315046926, 183533385, 13034140, 721604492, 242970774, 500232976, 316143635, 719601853, 411832633, 206849167, 62309503, 362143540, 172132792, 406642102, 290947418, 649997984, 400004941, 193289674, 20215276, 604047240, 792504507, 354704972, 661308027, 710569578, 67988066, 573986043, 298011050, 675020897, 371173377, 220311134, 234250033, 627878145, 805292463, 24071270, 648507616, 814745610, 517644997, 691772925, 511004739, 433787663, 788161195, 196473632, 362036173, 528196877, 697880168, 318651435, 223922625, 432332761, 605658712, 402713163, 12043466, 723222719, 197191480, 740372189, 835875906, 689010272, 292485650, 101464751, 764616290, 665830492, 830680702, 522703957, 36639665, 178661761, 847563520, 213367890, 580759073, 795883933, 189665782, 410128628, 104008441, 757987331, 543934116, 420541294, 396733102, 773554582, 422990463, 679308804, 471610475, 449025573, 293585715, 304333306, 606221987, 668107507, 201587373, 776461576, 54202261, 334132687, 570371370, 729669465, 388035450, 40739162, 294599466, 269999181, 368420277, 394723115, 506277838, 351687671, 683668119, 82918314, 72721076, 702889204, 841003831, 721904142, 691037495, 575492049, 221172299, 608377016, 584007171, 674474012, 135083989, 479195654, 408808739, 442284285, 530250590, 390248853, 461685089, 283253906, 717741307, 215568024, 562986577, 134817130, 147002383, 270825931, 379404006, 759183054, 581866917, 146566613, 784989241, 457129596, 59158644, 750640670, 700398504, 721509487, 402874366, 82387404, 95739856, 281346626, 467686791, 324137743, 11249127, 89157220, 716002070, 335342053, 246826170, 529385048, 760143990, 10725758, 516293110, 76538324, 257296477, 328165824, 172330118, 546825765, 619673906, 328792017, 788124094, 141927682, 555365723, 329427916, 607839982, 405389708, 571868667, 470002428, 684585751, 434604631, 204705039, 450529242, 361817407, 727855567, 413589322, 11544453, 803784599, 815775166, 425469974, 86512573, 86029713, 852702639, 728364190, 118324485, 477615251, 345426513, 219927860, 22417298, 480050287, 224592838, 759159, 131898579, 764335555, 457432197, 763875505, 642888584, 590641758, 210009158, 390019414, 235949401, 58219618, 562286114, 99631682, 631925366, 753164064, 328774959, 365242602, 385354452, 217542778, 795464774, 780632705, 678141873, 424450214, 25338472, 268284342, 493213958, 580867867, 15482483, 272837023, 328359708, 782291772, 308114267, 404813197, 333753982, 737682027, 538312006, 707909990, 234156623, 323140190, 803917719, 91035383, 200098402, 773260410, 554209269, 505977196, 258732217, 577347247, 388868026, 412079442, 312571314, 628683299, 740119334, 813470861, 86544483, 515146109, 371343866, 687853001, 265823977, 121589622, 808348288, 257353942, 635427508, 834922294, 224797491, 432675367, 731353224, 575538372, 642351606, 291366364, 210732817, 90658793, 146401688, 40748954, 527574284, 817614743, 547167333, 534136352, 372456076, 706600074, 640500788, 559786839, 845776458, 709348802, 677707036, 606711824, 349565805, 42095011, 472115432, 177053484, 681164976, 139728272, 510212596, 747795405, 441873933, 187174498, 392929945, 425171378, 555237229, 4315335, 9057268, 153360848, 99426909, 774527252, 83014618, 412368218, 3495282, 739674290, 826674363, 316599527, 110724402, 435058302, 156418860, 545209527, 681526436, 443190082, 613052844, 463370538, 710824143, 207309740, 783222241, 141846134, 266325996, 146201876, 449154790, 170683627, 716235176, 607164090, 291006513, 186310404, 43734965, 496486286, 736873833, 329899967, 408796174, 449053875, 589454563, 727957502, 460484783, 122169115, 75292611, 73671599, 848010384, 303936940, 791662107, 590932920, 125786858, 211282605, 729648214, 59156462, 152461927, 219894477, 776823847, 437757228, 186542194, 700611431, 257929382, 767315412, 18312688, 806906190, 504497667, 101165190, 603435510, 526872520, 254322283, 720021990, 779194394, 584710319, 801191565, 703649817, 361258161, 149741435, 808495563, 291596204, 250916275, 340042453, 141837377, 547502361, 181348702, 139498738, 338114582, 119328746, 177984134, 199957575, 358181386, 57332620, 512567111, 451958433, 156026128, 619998073, 307816265, 338764588, 65822147, 573828018, 487154809, 749222428, 522943099, 26336097, 186644498, 526288314, 534618890, 828269735, 675600958, 49788769, 453731878, 762637295, 387744335, 173171058, 33040483, 466949551, 843388255, 697432416, 216291746, 33282177, 240642656, 663436347, 390123214, 254438583, 190922896, 455331923, 296664914, 762697018, 331531324, 851176113, 771233913, 482330259, 389665212, 474944010, 58762628, 469089651, 436049255, 697216430, 431783325, 138107147, 499492245, 647224366, 407794272, 26067376, 445177552, 520720342, 798948406, 325365361, 117634101, 664099671, 153294810, 597801361, 640257687, 533951825, 702134729, 111685295, 685214097, 452013666, 317534558, 271219665, 529108611, 586379543, 355661610, 759841823, 446485943, 839034731, 33604088, 773212146, 191869702, 367354365, 689096322, 345311446, 438596834, 677372537, 542545550, 341130619, 292644024, 281192613, 251893811, 447792713, 520181371, 40921126, 778878825, 536838039, 230752698, 396625895, 601216134, 188488092, 130103565, 504870771, 413838340, 335573256, 124340986, 368340993, 243753204, 150144590, 808689996, 32468801, 68817331, 471378712, 566347573, 6430376, 651137151, 497752158, 823732827, 787280015, 789046852, 194658966, 171151811, 118113814, 793917550, 75187158, 717603845, 61671631, 51620383, 302490719, 78328345, 244847301, 549511806, 420356371, 560795789, 405546061, 302036596, 432306081, 270856136, 330554928, 212724399, 791196206, 445342723, 187781362, 87078067, 834667388, 218628624, 755629702, 148790011, 845609309, 89984158, 742118272, 475309628, 81731129, 107846408, 74447254, 68656823, 169459843, 643648059, 721924181, 212112779, 575076242, 471039705, 626114838, 564548835, 506450263, 488329877, 847101683, 592828368, 714089721, 832868261, 393063639, 603199595, 214221357, 747808090, 145225511, 784491117, 578386518, 253504617, 217256612, 432640963, 696210495, 700338942, 642132261, 394125773, 127189460, 622643989, 65557316, 850423288, 154198317, 360118020, 401298167, 809808378, 590060278, 378333119, 261388063, 301240958, 211172470, 476577014, 818999735, 320797504, 155490801, 362021897, 416507223, 193972866, 814253796, 555879930, 152626252, 598011677, 48971665, 590814257, 699100720, 732535868, 42427027, 335391594, 577502901, 72445917, 562054823, 34689534, 850274973, 640356274, 165636151, 309704599, 39996866, 436255023, 365085534, 208984696, 593049885, 755419039, 376895434, 634901252, 316743954, 476563344, 619551824, 766199910, 783651060, 32670169, 794822305, 435248113, 14247580, 284417137, 754554090, 30678221, 641072629, 711946716, 568640914, 656468482, 83597913, 356324101, 231391682, 122476642, 505437404, 636148283, 639556222, 262242870, 10083895, 470763095, 7162643, 490677454, 122627583, 711718981, 252376484, 423795716, 578101600, 275970963, 3053131, 327430341, 435804223, 349044314, 649311691, 234207954, 379806804, 342513855, 224624649, 181857560, 84797030, 123047825, 95186646, 293471117, 586961654, 111168138, 703259490, 756871363, 606284506, 380213718, 292725815, 463763080, 747629289, 254624782, 207883602, 849297083, 578506664, 656289117, 454015629, 162235991, 474249177, 633829447, 490767799, 210190430, 48735841, 656982789, 743473215, 47313566, 306689440, 53334547, 370344121, 419993940, 218969756, 341956367, 296184959, 135682817, 127205066, 744169001, 445909513, 801533404, 605661030, 181244618, 30772614, 196639386, 59911722, 616623643, 199307436, 551535136, 136575017, 79424355, 92705102, 498046224, 17339996, 698541762, 804348245, 104258042, 484400476, 535014225, 87644978, 121726462, 383782353, 77562877, 350468417, 724994239, 772938366, 320269449, 203075846, 465307490, 585234251, 271855066, 464423241, 403123130, 202162074, 117126999, 653413020, 8084225, 216658351, 409614891, 799241223, 600931579, 454131285, 782741932, 376344215, 79696641, 803438191, 565030050, 460657460, 5110534, 472517130, 76991417, 572426425, 92047134, 285371277, 843473400, 389338704, 704515255, 459914006, 657120075, 708563883, 78813141, 11770883, 688134435, 287808573, 649280542, 765338883, 439803770, 160535862, 617753423, 442051682, 288864924, 32955626, 326880188, 696887038, 215124062, 791918307, 767157413, 358676037, 30612492, 661971023, 838968782, 465224708, 784600829, 146985424, 799718881, 207906900, 340800263, 849693954, 44777992, 31326149, 240259940, 508401593, 499528021, 475930852, 690672059, 580019353, 297040464, 236338202, 454171188, 695134912, 508172471, 436504159, 293630619, 848875161, 37043893, 26993038, 396046068, 722016462, 445419380, 209243403, 503786686, 268117854, 281672598, 205034970, 87894257, 293598267, 46912651, 147959859, 462629641, 509044664, 700768221, 107374762, 340721447, 163551982, 247501118, 447395984, 318219025, 172114399, 110025830, 810265637, 370215004, 606303954, 462642711, 251114029, 290800715, 780017258, 789443137, 495480307, 615909633, 431756150, 766376396, 820732666, 686803688, 133668454, 761665150, 326017339, 424112204, 110554261, 386347465, 101066781, 135666139, 256882780, 205722545, 668032392, 405718561, 350327055, 621444438, 381307379, 421184831, 753121128, 590538618, 366906511, 345326178, 132085192, 40531091, 780676557, 586664955, 597888984, 693668509, 487104387, 234747974, 572624063, 114516856, 550027276, 316481563, 239535126, 788436714, 847219527, 113421825, 200615887, 815912760, 581164384, 191193216, 11551938, 606832431, 431210833, 196126697, 92508342, 270544041, 192437514, 99153842, 188585579, 413385580, 745267475, 448172363, 667109106, 85272138, 658601344, 443173146, 392530856, 589073317, 382995167, 248915715, 375600977, 386782401, 254322056, 790853708, 580714915, 163129486, 824017519, 86419559, 117205367, 634667017, 566451589, 852749522, 837490424, 330422330, 294598189, 814909626, 505390042, 125578715, 357313675, 450539487, 233746299, 446282749, 755039478, 740350430, 598956163, 116099139, 167482754, 310512355, 135624781, 470874939, 196356683, 239902897, 693520220, 454942578, 778240578, 45236161, 51101673, 270126615, 94622194, 524282161, 632376971, 703121383, 587013336, 572429454, 37728898, 143682359, 206045437, 557167425, 770459696, 477771773, 321346425, 290390778, 100874902, 758540246, 746805823, 459566327, 607673901, 158286491, 527010720, 579461268, 74963118, 420964844, 51316958, 250512679, 452729483, 35670488, 559935164, 734294507, 379228497, 172592106, 126508187, 757555710, 853874620, 808517874, 106015915, 375691866, 
        423413164, 423111661, 60250078, 645353691, 853830811, 288310932, 1489804, 127886925, 191505834, 459549138, 542519706, 369115379, 116842790, 784888677, 269818678, 712117130, 748410048, 139982101, 169805525, 32264681, 532400632, 397389041, 181262233, 703428567, 604760852, 44143128, 69914527, 86615396, 314810965, 68145528, 650868687, 717671367, 594246701, 641155397, 207406129, 180083553, 414651973, 132523243, 211350471, 397371331, 170688638, 732763563, 132155217, 394688247, 571356350, 93856418, 708831649, 841908230};

        QTesla3PPolynomial() {
        }

        static long barr_reduce(long j) {
            return j - (((5 * j) >> 32) * 856145921);
        }

        static void ntt(long[] jArr, long[] jArr2) {
            int i = 0;
            for (int i2 = 1024; i2 > 0; i2 >>= 1) {
                int i3 = 0;
                while (i3 < 2048) {
                    int i4 = i + 1;
                    int i5 = (int) jArr2[i];
                    int i6 = i3;
                    while (i6 < i3 + i2) {
                        int i7 = i6 + i2;
                        long barr_reduce = barr_reduce(reduce(i5 * jArr[i7]));
                        jArr[i7] = barr_reduce(jArr[i6] + (1712291842 - barr_reduce));
                        jArr[i6] = barr_reduce(barr_reduce + jArr[i6]);
                        i6++;
                    }
                    i3 = i6 + i2;
                    i = i4;
                }
            }
        }

        static void nttinv(long[] jArr, int i, long[] jArr2) {
            int i2 = 0;
            for (int i3 = 1; i3 < 2048; i3 *= 2) {
                int i4 = 0;
                while (i4 < 2048) {
                    int i5 = i2 + 1;
                    int i6 = (int) jArr2[i2];
                    int i7 = i4;
                    while (i7 < i4 + i3) {
                        int i8 = i + i7;
                        long j = jArr[i8];
                        int i9 = i8 + i3;
                        jArr[i8] = barr_reduce(jArr[i9] + j);
                        jArr[i9] = barr_reduce(reduce(i6 * (j + (1712291842 - jArr[i9]))));
                        i7++;
                    }
                    i4 = i7 + i3;
                    i2 = i5;
                }
            }
        }

        static void nttinv(long[] jArr, long[] jArr2) {
            int i = 0;
            for (int i2 = 1; i2 < 2048; i2 *= 2) {
                int i3 = 0;
                while (i3 < 2048) {
                    int i4 = i + 1;
                    int i5 = (int) jArr2[i];
                    int i6 = i3;
                    while (i6 < i3 + i2) {
                        long j = jArr[i6];
                        int i7 = i6 + i2;
                        jArr[i6] = barr_reduce(jArr[i7] + j);
                        jArr[i7] = barr_reduce(reduce(i5 * (j + (1712291842 - jArr[i7]))));
                        i6++;
                    }
                    i3 = i6 + i2;
                    i = i4;
                }
            }
        }

        static void poly_add(long[] jArr, long[] jArr2, long[] jArr3) {
            for (int i = 0; i < 2048; i++) {
                jArr[i] = jArr2[i] + jArr3[i];
            }
        }

        static void poly_add_correct(long[] jArr, int i, long[] jArr2, int i2, long[] jArr3, int i3) {
            for (int i4 = 0; i4 < 2048; i4++) {
                int i5 = i + i4;
                long j = jArr2[i2 + i4] + jArr3[i3 + i4];
                jArr[i5] = j;
                long j2 = j - 856145921;
                jArr[i5] = j2;
                jArr[i5] = j2 + (856145921 & (j2 >> 31));
            }
        }

        static void poly_mul(long[] jArr, int i, long[] jArr2, int i2, long[] jArr3) {
            poly_pointwise(jArr, i, jArr2, i2, jArr3);
            nttinv(jArr, i, zetainv);
        }

        static void poly_mul(long[] jArr, long[] jArr2, long[] jArr3) {
            poly_pointwise(jArr, jArr2, jArr3);
            nttinv(jArr, zetainv);
        }

        static void poly_ntt(long[] jArr, long[] jArr2) {
            for (int i = 0; i < 2048; i++) {
                jArr[i] = jArr2[i];
            }
            ntt(jArr, zeta);
        }

        static void poly_pointwise(long[] jArr, int i, long[] jArr2, int i2, long[] jArr3) {
            for (int i3 = 0; i3 < 2048; i3++) {
                jArr[i3 + i] = reduce(jArr2[i3 + i2] * jArr3[i3]);
            }
        }

        static void poly_pointwise(long[] jArr, long[] jArr2, long[] jArr3) {
            for (int i = 0; i < 2048; i++) {
                jArr[i] = reduce(jArr2[i] * jArr3[i]);
            }
        }

        static void poly_sub(long[] jArr, int i, long[] jArr2, int i2, long[] jArr3, int i3) {
            for (int i4 = 0; i4 < 2048; i4++) {
                jArr[i + i4] = barr_reduce(jArr2[i2 + i4] - jArr3[i3 + i4]);
            }
        }

        static void poly_sub_correct(int[] iArr, int[] iArr2, int[] iArr3) {
            for (int i = 0; i < 2048; i++) {
                int i2 = iArr2[i] - iArr3[i];
                iArr[i] = i2;
                iArr[i] = i2 + ((i2 >> 31) & QTesla3p.PARAM_Q);
            }
        }

        static void poly_uniform(long[] jArr, byte[] bArr, int i) {
            byte[] bArr2 = new byte[30240];
            HashUtils.customizableSecureHashAlgorithmKECCAK128Simple(bArr2, 0, 30240, (short) 0, bArr, i, 32);
            int i2 = 180;
            short s = (short) 1;
            int i3 = 0;
            int i4 = 0;
            while (i4 < 10240) {
                if (i3 > (i2 * Opcodes.JSR) - 16) {
                    HashUtils.customizableSecureHashAlgorithmKECCAK128Simple(bArr2, 0, 30240, s, bArr, i, 32);
                    i2 = 1;
                    s = (short) (s + 1);
                    i3 = 0;
                }
                int littleEndianToInt = Pack.littleEndianToInt(bArr2, i3) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
                int i5 = i3 + 4;
                int littleEndianToInt2 = Pack.littleEndianToInt(bArr2, i5) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
                int i6 = i5 + 4;
                int littleEndianToInt3 = Pack.littleEndianToInt(bArr2, i6) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
                int i7 = i6 + 4;
                int littleEndianToInt4 = 1073741823 & Pack.littleEndianToInt(bArr2, i7);
                i3 = i7 + 4;
                if (littleEndianToInt < QTesla3p.PARAM_Q && i4 < 10240) {
                    jArr[i4] = reduce(littleEndianToInt * 513161157);
                    i4++;
                }
                if (littleEndianToInt2 < QTesla3p.PARAM_Q && i4 < 10240) {
                    jArr[i4] = reduce(littleEndianToInt2 * 513161157);
                    i4++;
                }
                if (littleEndianToInt3 < QTesla3p.PARAM_Q && i4 < 10240) {
                    jArr[i4] = reduce(littleEndianToInt3 * 513161157);
                    i4++;
                }
                if (littleEndianToInt4 < QTesla3p.PARAM_Q && i4 < 10240) {
                    jArr[i4] = reduce(littleEndianToInt4 * 513161157);
                    i4++;
                }
            }
        }

        static long reduce(long j) {
            return (j + (((QTesla3p.PARAM_QINV * j) & 4294967295L) * 856145921)) >> 32;
        }

        static void sparse_mul16(int[] iArr, int[] iArr2, int[] iArr3, short[] sArr) {
            for (int i = 0; i < 2048; i++) {
                iArr[i] = 0;
            }
            for (int i2 = 0; i2 < 40; i2++) {
                int i3 = iArr3[i2];
                for (int i4 = 0; i4 < i3; i4++) {
                    iArr[i4] = iArr[i4] - (sArr[i2] * iArr2[(i4 + 2048) - i3]);
                }
                for (int i5 = i3; i5 < 2048; i5++) {
                    iArr[i5] = iArr[i5] + (sArr[i2] * iArr2[i5 - i3]);
                }
            }
        }

        static void sparse_mul32(int[] iArr, int[] iArr2, int[] iArr3, short[] sArr) {
            for (int i = 0; i < 2048; i++) {
                iArr[i] = 0;
            }
            for (int i2 = 0; i2 < 40; i2++) {
                int i3 = iArr3[i2];
                for (int i4 = 0; i4 < i3; i4++) {
                    iArr[i4] = iArr[i4] - (sArr[i2] * iArr2[(i4 + 2048) - i3]);
                }
                for (int i5 = i3; i5 < 2048; i5++) {
                    iArr[i5] = iArr[i5] + (sArr[i2] * iArr2[i5 - i3]);
                }
            }
        }

        static void sparse_mul32(long[] jArr, int i, int[] iArr, int i2, int[] iArr2, short[] sArr) {
            for (int i3 = 0; i3 < 2048; i3++) {
                jArr[i + i3] = 0;
            }
            for (int i4 = 0; i4 < 40; i4++) {
                int i5 = iArr2[i4];
                for (int i6 = 0; i6 < i5; i6++) {
                    int i7 = i + i6;
                    jArr[i7] = jArr[i7] - (sArr[i4] * iArr[((i2 + i6) + 2048) - i5]);
                }
                for (int i8 = i5; i8 < 2048; i8++) {
                    int i9 = i + i8;
                    jArr[i9] = jArr[i9] + (sArr[i4] * iArr[(i2 + i8) - i5]);
                }
            }
        }

        static void sparse_mul8(long[] jArr, int i, byte[] bArr, int i2, int[] iArr, short[] sArr) {
            for (int i3 = 0; i3 < 2048; i3++) {
                jArr[i + i3] = 0;
            }
            for (int i4 = 0; i4 < 40; i4++) {
                int i5 = iArr[i4];
                for (int i6 = 0; i6 < i5; i6++) {
                    int i7 = i + i6;
                    jArr[i7] = jArr[i7] - (sArr[i4] * bArr[((i2 + i6) + 2048) - i5]);
                }
                for (int i8 = i5; i8 < 2048; i8++) {
                    int i9 = i + i8;
                    jArr[i9] = jArr[i9] + (sArr[i4] * bArr[(i2 + i8) - i5]);
                }
            }
        }

        static void sparse_mul8(long[] jArr, byte[] bArr, int[] iArr, short[] sArr) {
            for (int i = 0; i < 2048; i++) {
                jArr[i] = 0;
            }
            for (int i2 = 0; i2 < 40; i2++) {
                int i3 = iArr[i2];
                for (int i4 = 0; i4 < i3; i4++) {
                    jArr[i4] = jArr[i4] - (sArr[i2] * bArr[(i4 + 2048) - i3]);
                }
                for (int i5 = i3; i5 < 2048; i5++) {
                    jArr[i5] = jArr[i5] + (sArr[i2] * bArr[i5 - i3]);
                }
            }
        }
    }

    QTesla3p() {
    }

    private static int absolute(int i) {
        int i2 = i >> 31;
        return (i ^ i2) - i2;
    }

    private static long absolute(long j) {
        long j2 = j >> 63;
        return (j ^ j2) - j2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static int at(byte[] bArr, int i, int i2) {
        int i3 = (i * 4) + (i2 * 4);
        int i4 = bArr[i3] & 255;
        int i5 = i3 + 1;
        int i6 = i4 | ((bArr[i5] & 255) << 8);
        int i7 = i5 + 1;
        return (bArr[i7 + 1] << 24) | i6 | ((bArr[i7] & 255) << 16);
    }

    private static void at(byte[] bArr, int i, int i2, int i3) {
        Pack.intToLittleEndian(i3, bArr, (i * 4) + (i2 * 4));
    }

    private static boolean checkPolynomial(long[] jArr, int i, int i2) {
        int i3;
        int i4 = 2048;
        long[] jArr2 = new long[2048];
        for (int i5 = 0; i5 < 2048; i5++) {
            jArr2[i5] = absolute((int) jArr[i + i5]);
        }
        int i6 = 0;
        int i7 = 0;
        while (i6 < 40) {
            int i8 = 0;
            while (true) {
                i3 = i4 - 1;
                if (i8 < i3) {
                    int i9 = i8 + 1;
                    long j = jArr2[i9];
                    long j2 = jArr2[i8];
                    long j3 = (j - j2) >> 31;
                    long j4 = ~j3;
                    jArr2[i9] = (j4 & j) | (j2 & j3);
                    jArr2[i8] = (j & j3) | (j2 & j4);
                    i6 = i6;
                    i8 = i9;
                }
            }
            i7 += (int) jArr2[i3];
            i4--;
            i6++;
        }
        return i7 > i2;
    }

    static void decodePublicKey(int[] iArr, byte[] bArr, int i, byte[] bArr2) {
        int i2 = 0;
        for (int i3 = 0; i3 < 10240; i3 += 16) {
            iArr[i3] = at(bArr2, i2, 0) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 1] = ((at(bArr2, i2, 0) >>> 30) | (at(bArr2, i2, 1) << 2)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 2] = ((at(bArr2, i2, 1) >>> 28) | (at(bArr2, i2, 2) << 4)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 3] = ((at(bArr2, i2, 2) >>> 26) | (at(bArr2, i2, 3) << 6)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 4] = ((at(bArr2, i2, 3) >>> 24) | (at(bArr2, i2, 4) << 8)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 5] = ((at(bArr2, i2, 4) >>> 22) | (at(bArr2, i2, 5) << 10)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 6] = ((at(bArr2, i2, 5) >>> 20) | (at(bArr2, i2, 6) << 12)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 7] = ((at(bArr2, i2, 6) >>> 18) | (at(bArr2, i2, 7) << 14)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 8] = ((at(bArr2, i2, 7) >>> 16) | (at(bArr2, i2, 8) << 16)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 9] = ((at(bArr2, i2, 8) >>> 14) | (at(bArr2, i2, 9) << 18)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 10] = ((at(bArr2, i2, 9) >>> 12) | (at(bArr2, i2, 10) << 20)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 11] = ((at(bArr2, i2, 10) >>> 10) | (at(bArr2, i2, 11) << 22)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 12] = ((at(bArr2, i2, 11) >>> 8) | (at(bArr2, i2, 12) << 24)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 13] = ((at(bArr2, i2, 12) >>> 6) | (at(bArr2, i2, 13) << 26)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 14] = ((at(bArr2, i2, 13) >>> 4) | (at(bArr2, i2, 14) << 28)) & LockFreeTaskQueueCore.MAX_CAPACITY_MASK;
            iArr[i3 + 15] = 1073741823 & (at(bArr2, i2, 14) >>> 2);
            i2 += 15;
        }
        System.arraycopy(bArr2, 38400, bArr, i, 32);
    }

    static void decodeSignature(byte[] bArr, long[] jArr, byte[] bArr2, int i) {
        int i2 = 0;
        int i3 = 0;
        int i4 = 0;
        while (i3 < 2048) {
            int at = at(bArr2, i4, i2);
            int at2 = at(bArr2, i4, 1);
            int at3 = at(bArr2, i4, 2);
            int at4 = at(bArr2, i4, 3);
            int at5 = at(bArr2, i4, 4);
            int at6 = at(bArr2, i4, 5);
            int at7 = at(bArr2, i4, 6);
            int at8 = at(bArr2, i4, 7);
            int at9 = at(bArr2, i4, 8);
            int at10 = at(bArr2, i4, 9);
            int at11 = at(bArr2, i4, 10);
            jArr[i3] = (at << 10) >> 10;
            jArr[i3 + 1] = (at >>> 22) | ((at2 << 20) >> 10);
            jArr[i3 + 2] = (at2 >>> 12) | ((at3 << 30) >> 10);
            jArr[i3 + 3] = (at3 << 8) >> 10;
            jArr[i3 + 4] = (at3 >>> 24) | ((at4 << 18) >> 10);
            jArr[i3 + 5] = (at4 >>> 14) | ((at5 << 28) >> 10);
            jArr[i3 + 6] = (at5 << 6) >> 10;
            jArr[i3 + 7] = (at5 >>> 26) | ((at6 << 16) >> 10);
            jArr[i3 + 8] = (at6 >>> 16) | ((at7 << 26) >> 10);
            jArr[i3 + 9] = (at7 << 4) >> 10;
            jArr[i3 + 10] = (at7 >>> 28) | ((at8 << 14) >> 10);
            jArr[i3 + 11] = (at8 >>> 18) | ((at9 << 24) >> 10);
            jArr[i3 + 12] = (at9 << 2) >> 10;
            jArr[i3 + 13] = (at9 >>> 30) | ((at10 << 12) >> 10);
            jArr[i3 + 14] = (at10 >>> 20) | ((at11 << 22) >> 10);
            jArr[i3 + 15] = at11 >> 10;
            i4 += 11;
            i3 += 16;
            i2 = 0;
        }
        System.arraycopy(bArr2, i + 5632, bArr, 0, 32);
    }

    static void encodeC(int[] iArr, short[] sArr, byte[] bArr, int i) {
        short s;
        short[] sArr2 = new short[2048];
        byte[] bArr2 = new byte[Opcodes.JSR];
        short s2 = 1;
        HashUtils.customizableSecureHashAlgorithmKECCAK128Simple(bArr2, 0, Opcodes.JSR, (short) 0, bArr, i, 32);
        Arrays.fill(sArr2, (short) 0);
        int i2 = 0;
        int i3 = 0;
        short s3 = (short) 1;
        while (i2 < 40) {
            if (i3 > 165) {
                s = s2;
                HashUtils.customizableSecureHashAlgorithmKECCAK128Simple(bArr2, 0, Opcodes.JSR, s3, bArr, i, 32);
                s3 = (short) (s3 + 1);
                i3 = 0;
            } else {
                s = s2;
            }
            int i4 = ((bArr2[i3] << 8) | (bArr2[i3 + 1] & 255)) & 2047;
            if (sArr2[i4] == 0) {
                if ((bArr2[i3 + 2] & s) == s) {
                    sArr2[i4] = -1;
                } else {
                    sArr2[i4] = s;
                }
                iArr[i2] = i4;
                sArr[i2] = sArr2[i4];
                i2++;
            }
            i3 += 3;
            s2 = s;
        }
    }

    static void encodePrivateKey(byte[] bArr, long[] jArr, long[] jArr2, byte[] bArr2, int i, byte[] bArr3) {
        for (int i2 = 0; i2 < 2048; i2++) {
            bArr[0 + i2] = (byte) jArr[i2];
        }
        for (int i3 = 0; i3 < 5; i3++) {
            for (int i4 = 0; i4 < 2048; i4++) {
                bArr[2048 + (i3 * 2048) + i4] = (byte) jArr2[r3];
            }
        }
        System.arraycopy(bArr2, i, bArr, 12288, 64);
        HashUtils.secureHashAlgorithmKECCAK256(bArr, 12352, 40, bArr3, 0, 38400);
    }

    static void encodePublicKey(byte[] bArr, long[] jArr, byte[] bArr2, int i) {
        int i2 = 0;
        int i3 = 0;
        int i4 = 0;
        while (i3 < 9600) {
            int i5 = i4 + 1;
            at(bArr, i3, i2, (int) (jArr[i4] | (jArr[i5] << 30)));
            int i6 = i4 + 2;
            at(bArr, i3, 1, (int) ((jArr[i5] >> 2) | (jArr[i6] << 28)));
            int i7 = i4 + 3;
            at(bArr, i3, 2, (int) ((jArr[i6] >> 4) | (jArr[i7] << 26)));
            int i8 = i4 + 4;
            at(bArr, i3, 3, (int) ((jArr[i7] >> 6) | (jArr[i8] << 24)));
            int i9 = i4 + 5;
            at(bArr, i3, 4, (int) ((jArr[i8] >> 8) | (jArr[i9] << 22)));
            int i10 = i4 + 6;
            at(bArr, i3, 5, (int) ((jArr[i9] >> 10) | (jArr[i10] << 20)));
            int i11 = i4 + 7;
            at(bArr, i3, 6, (int) ((jArr[i10] >> 12) | (jArr[i11] << 18)));
            int i12 = i4 + 8;
            at(bArr, i3, 7, (int) ((jArr[i11] >> 14) | (jArr[i12] << 16)));
            long j = jArr[i12] >> 16;
            int i13 = i4 + 9;
            at(bArr, i3, 8, (int) (j | (jArr[i13] << 14)));
            int i14 = i4 + 10;
            at(bArr, i3, 9, (int) ((jArr[i13] >> 18) | (jArr[i14] << 12)));
            long j2 = jArr[i14] >> 20;
            int i15 = i4 + 11;
            at(bArr, i3, 10, (int) (j2 | (jArr[i15] << 10)));
            int i16 = i4 + 12;
            at(bArr, i3, 11, (int) ((jArr[i15] >> 22) | (jArr[i16] << 8)));
            int i17 = i4 + 13;
            at(bArr, i3, 12, (int) ((jArr[i16] >> 24) | (jArr[i17] << 6)));
            int i18 = i4 + 14;
            at(bArr, i3, 13, (int) ((jArr[i17] >> 26) | (jArr[i18] << 4)));
            at(bArr, i3, 14, (int) ((jArr[i18] >> 28) | (jArr[i4 + 15] << 2)));
            i4 += 16;
            i3 += 15;
            i2 = 0;
        }
        System.arraycopy(bArr2, i, bArr, 38400, 32);
    }

    static void encodeSignature(byte[] bArr, int i, byte[] bArr2, int i2, long[] jArr) {
        int i3 = 0;
        int i4 = 0;
        int i5 = 0;
        while (i4 < 1408) {
            int i6 = i5 + 1;
            at(bArr, i4, i3, (int) ((jArr[i5 + 0] & 4194303) | (jArr[i6] << 22)));
            int i7 = i5 + 2;
            at(bArr, i4, 1, (int) (((jArr[i6] >>> 10) & 4095) | (jArr[i7] << 12)));
            int i8 = i5 + 4;
            at(bArr, i4, 2, (int) (((jArr[i7] >>> 20) & 3) | ((jArr[i5 + 3] & 4194303) << 2) | (jArr[i8] << 24)));
            int i9 = i5 + 5;
            at(bArr, i4, 3, (int) (((jArr[i8] >>> 8) & 16383) | (jArr[i9] << 14)));
            int i10 = i5 + 7;
            at(bArr, i4, 4, (int) (((jArr[i9] >>> 18) & 15) | ((jArr[i5 + 6] & 4194303) << 4) | (jArr[i10] << 26)));
            int i11 = i5 + 8;
            at(bArr, i4, 5, (int) (((jArr[i10] >>> 6) & 65535) | (jArr[i11] << 16)));
            long j = ((jArr[i11] >>> 16) & 63) | ((jArr[i5 + 9] & 4194303) << 6);
            int i12 = i5 + 10;
            at(bArr, i4, 6, (int) (j | (jArr[i12] << 28)));
            int i13 = i5 + 11;
            at(bArr, i4, 7, (int) (((jArr[i12] >>> 4) & 262143) | (jArr[i13] << 18)));
            long j2 = ((jArr[i13] >>> 14) & 255) | ((4194303 & jArr[i5 + 12]) << 8);
            int i14 = i5 + 13;
            at(bArr, i4, 8, (int) (j2 | (jArr[i14] << 30)));
            long j3 = (jArr[i14] >>> 2) & 1048575;
            int i15 = i5 + 14;
            at(bArr, i4, 9, (int) (j3 | (jArr[i15] << 20)));
            at(bArr, i4, 10, (int) (((jArr[i15] >>> 12) & 1023) | (jArr[i5 + 15] << 10)));
            i5 += 16;
            i4 += 11;
            i3 = 0;
        }
        System.arraycopy(bArr2, i2, bArr, i + 5632, 32);
    }

    static int generateKeyPair(byte[] bArr, byte[] bArr2, SecureRandom secureRandom) {
        int i;
        byte[] bArr3 = new byte[32];
        byte[] bArr4 = new byte[256];
        long[] jArr = new long[2048];
        long[] jArr2 = new long[Data.MAX_DATA_BYTES];
        long[] jArr3 = new long[Data.MAX_DATA_BYTES];
        long[] jArr4 = new long[Data.MAX_DATA_BYTES];
        long[] jArr5 = new long[2048];
        secureRandom.nextBytes(bArr3);
        HashUtils.secureHashAlgorithmKECCAK256(bArr4, 0, 256, bArr3, 0, 32);
        int i2 = 0;
        for (int i3 = 0; i3 < 5; i3++) {
            do {
                i2++;
                i = i3 * 2048;
                Gaussian.sample_gauss_poly(i2, bArr4, i3 * 32, jArr2, i);
            } while (checkPolynomial(jArr2, i, 901));
        }
        do {
            i2++;
            Gaussian.sample_gauss_poly(i2, bArr4, Opcodes.IF_ICMPNE, jArr, 0);
        } while (checkPolynomial(jArr, 0, 901));
        QTesla3PPolynomial.poly_uniform(jArr3, bArr4, 192);
        QTesla3PPolynomial.poly_ntt(jArr5, jArr);
        int i4 = 0;
        while (i4 < 5) {
            int i5 = i4 * 2048;
            QTesla3PPolynomial.poly_mul(jArr4, i5, jArr3, i5, jArr5);
            QTesla3PPolynomial.poly_add_correct(jArr4, i5, jArr4, i5, jArr2, i5);
            i4++;
            jArr4 = jArr4;
            jArr3 = jArr3;
        }
        encodePublicKey(bArr, jArr4, bArr4, 192);
        encodePrivateKey(bArr2, jArr, jArr2, bArr4, 192, bArr);
        return 0;
    }

    static int generateSignature(byte[] bArr, byte[] bArr2, int i, int i2, byte[] bArr3, SecureRandom secureRandom) {
        int i3;
        long[] jArr;
        long[] jArr2;
        short[] sArr;
        int[] iArr;
        int i4;
        long[] jArr3;
        int i5;
        long[] jArr4;
        byte[] bArr4 = bArr3;
        byte[] bArr5 = new byte[32];
        byte[] bArr6 = new byte[32];
        byte[] bArr7 = new byte[Opcodes.D2F];
        int[] iArr2 = new int[40];
        short[] sArr2 = new short[40];
        long[] jArr5 = new long[2048];
        long[] jArr6 = new long[Data.MAX_DATA_BYTES];
        long[] jArr7 = new long[Data.MAX_DATA_BYTES];
        long[] jArr8 = new long[Data.MAX_DATA_BYTES];
        System.arraycopy(bArr4, 12320, bArr7, 0, 32);
        byte[] bArr8 = new byte[32];
        secureRandom.nextBytes(bArr8);
        System.arraycopy(bArr8, 0, bArr7, 32, 32);
        long[] jArr9 = jArr8;
        long[] jArr10 = jArr6;
        long[] jArr11 = new long[2048];
        long[] jArr12 = new long[2048];
        long[] jArr13 = new long[2048];
        HashUtils.secureHashAlgorithmKECCAK256(bArr7, 64, 40, bArr2, 0, i2);
        HashUtils.secureHashAlgorithmKECCAK256(bArr6, 0, 32, bArr7, 0, 104);
        System.arraycopy(bArr4, 12352, bArr7, 104, 40);
        QTesla3PPolynomial.poly_uniform(jArr9, bArr4, 12288);
        int i6 = 0;
        boolean z = false;
        while (true) {
            i6++;
            sample_y(jArr5, bArr6, 0, i6);
            QTesla3PPolynomial.poly_ntt(jArr13, jArr5);
            int i7 = 0;
            while (true) {
                i3 = 5;
                if (i7 >= 5) {
                    break;
                }
                int i8 = i7 * 2048;
                QTesla3PPolynomial.poly_mul(jArr10, i8, jArr9, i8, jArr13);
                i7++;
            }
            int i9 = 0;
            hashFunction(bArr5, 0, jArr10, bArr7, 64);
            encodeC(iArr2, sArr2, bArr5, 0);
            long[] jArr14 = jArr11;
            QTesla3PPolynomial.sparse_mul8(jArr14, bArr4, iArr2, sArr2);
            long[] jArr15 = jArr12;
            QTesla3PPolynomial.poly_add(jArr15, jArr5, jArr14);
            if (testRejection(jArr15)) {
                jArr11 = jArr14;
                jArr12 = jArr15;
            } else {
                while (true) {
                    if (i9 >= i3) {
                        jArr = jArr9;
                        jArr2 = jArr5;
                        sArr = sArr2;
                        iArr = iArr2;
                        i4 = i6;
                        jArr3 = jArr15;
                        i5 = 0;
                        jArr4 = jArr13;
                        break;
                    }
                    int i10 = i9 * 2048;
                    i9++;
                    long[] jArr16 = jArr10;
                    jArr = jArr9;
                    i5 = 0;
                    i4 = i6;
                    jArr3 = jArr15;
                    jArr4 = jArr13;
                    jArr2 = jArr5;
                    sArr = sArr2;
                    iArr = iArr2;
                    QTesla3PPolynomial.sparse_mul8(jArr7, i10, bArr3, i9 * 2048, iArr2, sArr);
                    QTesla3PPolynomial.poly_sub(jArr16, i10, jArr16, i10, jArr7, i10);
                    jArr10 = jArr16;
                    z = test_correctness(jArr10, i10);
                    if (z) {
                        break;
                    }
                    sArr2 = sArr;
                    jArr13 = jArr4;
                    jArr9 = jArr;
                    jArr5 = jArr2;
                    iArr2 = iArr;
                    i3 = 5;
                    jArr15 = jArr3;
                    i6 = i4;
                }
                if (!z) {
                    encodeSignature(bArr, i5, bArr5, i5, jArr3);
                    return i5;
                }
                bArr4 = bArr3;
                jArr12 = jArr3;
                sArr2 = sArr;
                jArr13 = jArr4;
                jArr9 = jArr;
                jArr5 = jArr2;
                iArr2 = iArr;
                i6 = i4;
                jArr11 = jArr14;
            }
        }
    }

    private static void hashFunction(byte[] bArr, int i, long[] jArr, byte[] bArr2, int i2) {
        byte[] bArr3 = new byte[10320];
        for (int i3 = 0; i3 < 5; i3++) {
            int i4 = i3 * 2048;
            int i5 = 0;
            while (i5 < 2048) {
                int i6 = (int) jArr[i4];
                int i7 = (428072960 - i6) >> 31;
                int i8 = (i6 & (~i7)) | ((i6 - PARAM_Q) & i7);
                int i9 = 16777215 & i8;
                int i10 = (8388608 - i9) >> 31;
                bArr3[i4] = (byte) ((i8 - ((i9 & (~i10)) | ((i9 - 16777216) & i10))) >> 24);
                i5++;
                i4++;
            }
        }
        System.arraycopy(bArr2, i2, bArr3, Data.MAX_DATA_BYTES, 80);
        HashUtils.secureHashAlgorithmKECCAK256(bArr, i, 32, bArr3, 0, 10320);
    }

    static int lE24BitToInt(byte[] bArr, int i) {
        int i2 = bArr[i] & 255;
        int i3 = i + 1;
        return ((bArr[i3 + 1] & 255) << 16) | i2 | ((bArr[i3] & 255) << 8);
    }

    static boolean memoryEqual(byte[] bArr, int i, byte[] bArr2, int i2, int i3) {
        if (i + i3 > bArr.length || i2 + i3 > bArr2.length) {
            return false;
        }
        for (int i4 = 0; i4 < i3; i4++) {
            if (bArr[i + i4] != bArr2[i2 + i4]) {
                return false;
            }
        }
        return true;
    }

    static void sample_y(long[] jArr, byte[] bArr, int i, int i2) {
        int i3 = BPLUS1BYTES;
        byte[] bArr2 = new byte[(i3 * 2048) + 1];
        short s = (short) (i2 << 8);
        int i4 = i3 * 2048;
        HashUtils.customizableSecureHashAlgorithmKECCAK256Simple(bArr2, 0, i4, s, bArr, i, 32);
        short s2 = (short) (s + 1);
        int i5 = 0;
        int i6 = 0;
        int i7 = 2048;
        while (i6 < 2048) {
            if (i5 >= i7 * i3) {
                int i8 = NBLOCKS_SHAKE;
                HashUtils.customizableSecureHashAlgorithmKECCAK256Simple(bArr2, 0, i4, s2, bArr, i, 32);
                i5 = 0;
                i7 = i8;
                s2 = (short) (s2 + 1);
            }
            long lE24BitToInt = lE24BitToInt(bArr2, i5) & maskb1;
            jArr[i6] = lE24BitToInt;
            long j = lE24BitToInt - 2097151;
            jArr[i6] = j;
            if (j != PlaybackStateCompat.ACTION_SET_SHUFFLE_MODE) {
                i6++;
            }
            i5 += i3;
        }
    }

    private static boolean testRejection(long[] jArr) {
        int i = 0;
        for (int i2 = 0; i2 < 2048; i2++) {
            i = (int) (i | (2096250 - absolute(jArr[i2])));
        }
        return (i >>> 31) > 0;
    }

    private static boolean testZ(long[] jArr) {
        for (int i = 0; i < 2048; i++) {
            long j = jArr[i];
            if (j < -2096250 || j > 2096250) {
                return true;
            }
        }
        return false;
    }

    static boolean test_correctness(long[] jArr, int i) {
        for (int i2 = 0; i2 < 2048; i2++) {
            long j = jArr[i + i2];
            int i3 = (int) ((j & (~r4)) | ((j - 856145921) & (((int) (428072960 - j)) >> 31)));
            if ((((~(absolute(i3 - ((((8388608 + i3) - 1) >> 24) << 24)) - 8387707)) >>> 31) | ((~(absolute(i3) - 428072059)) >>> 31)) == 1) {
                return true;
            }
        }
        return false;
    }

    static int verifying(byte[] bArr, byte[] bArr2, int i, int i2, byte[] bArr3) {
        byte[] bArr4 = new byte[32];
        byte[] bArr5 = new byte[32];
        byte[] bArr6 = new byte[32];
        byte[] bArr7 = new byte[80];
        int[] iArr = new int[40];
        short[] sArr = new short[40];
        int[] iArr2 = new int[Data.MAX_DATA_BYTES];
        long[] jArr = new long[Data.MAX_DATA_BYTES];
        long[] jArr2 = new long[Data.MAX_DATA_BYTES];
        long[] jArr3 = new long[Data.MAX_DATA_BYTES];
        long[] jArr4 = new long[2048];
        long[] jArr5 = new long[2048];
        if (i2 != CRYPTO_BYTES) {
            return -1;
        }
        decodeSignature(bArr4, jArr4, bArr2, i);
        if (testZ(jArr4)) {
            return -2;
        }
        decodePublicKey(iArr2, bArr6, 0, bArr3);
        HashUtils.secureHashAlgorithmKECCAK256(bArr7, 0, 40, bArr, 0, bArr.length);
        HashUtils.secureHashAlgorithmKECCAK256(bArr7, 40, 40, bArr3, 0, 38400);
        int i3 = 0;
        QTesla3PPolynomial.poly_uniform(jArr2, bArr6, 0);
        encodeC(iArr, sArr, bArr4, 0);
        QTesla3PPolynomial.poly_ntt(jArr5, jArr4);
        int i4 = 0;
        while (i4 < 5) {
            int i5 = i4 * 2048;
            int[] iArr3 = iArr;
            int[] iArr4 = iArr;
            long[] jArr6 = jArr2;
            QTesla3PPolynomial.sparse_mul32(jArr3, i5, iArr2, i5, iArr3, sArr);
            QTesla3PPolynomial.poly_mul(jArr, i5, jArr6, i5, jArr5);
            QTesla3PPolynomial.poly_sub(jArr, i5, jArr, i5, jArr3, i5);
            i4++;
            jArr2 = jArr6;
            iArr = iArr4;
            i3 = 0;
        }
        hashFunction(bArr5, i3, jArr, bArr7, i3);
        if (memoryEqual(bArr4, i3, bArr5, i3, 32)) {
            return i3;
        }
        return -3;
    }
}
