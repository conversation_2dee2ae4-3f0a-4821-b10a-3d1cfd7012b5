package o.p;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.f;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static short[] a;
    private static int b;
    private static byte[] c;
    private static int d;
    private static int e;
    private static boolean f;
    private static char[] g;
    private static int h;
    private static boolean i;
    private static int j;

    /* renamed from: o, reason: collision with root package name */
    private static int f94o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        f94o = 1;
        c();
        SystemClock.elapsedRealtimeNanos();
        AudioTrack.getMinVolume();
        ViewConfiguration.getLongPressTimeout();
        TextUtils.lastIndexOf("", '0', 0);
        TextUtils.lastIndexOf("", '0', 0, 0);
        Color.rgb(0, 0, 0);
        Color.green(0);
        SystemClock.currentThreadTimeMillis();
        Color.alpha(0);
        AndroidCharacter.getMirror('0');
        View.resolveSize(0, 0);
        TextUtils.lastIndexOf("", '0', 0, 0);
        SystemClock.elapsedRealtime();
        View.resolveSizeAndState(0, 0, 0);
        TextUtils.indexOf("", "", 0, 0);
        AudioTrack.getMinVolume();
        ViewConfiguration.getGlobalActionKeyTimeout();
        SystemClock.uptimeMillis();
        ViewConfiguration.getMaximumDrawingCacheSize();
        ImageFormat.getBitsPerPixel(0);
        Color.argb(0, 0, 0, 0);
        ViewConfiguration.getScrollFriction();
        TextUtils.indexOf("", "");
        TextUtils.getOffsetAfter("", 0);
        View.MeasureSpec.getSize(0);
        TextUtils.getTrimmedLength("");
        Process.getThreadPriority(0);
        ViewConfiguration.getScrollDefaultDelay();
        TextUtils.indexOf("", "");
        TypedValue.complexToFloat(0);
        int i2 = f94o + 15;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        c = new byte[]{102, 13, -13, 29, -27, 23, 20, 31, -12, 73, -77, 65, UtilitiesSDKConstants.SRP_LABEL_MAC, 73, 94, -103, -71, 64, -77, 85, -72, PSSSigner.TRAILER_IMPLICIT, -77, 64, 79, -69, UtilitiesSDKConstants.SRP_LABEL_MAC, -71, 82, 30, -22, -25, 27, -15, 14, -13, 29, 11, -24, 31, -12, -112, -112, -112, -112, -112, -112};
        e = 909053619;
        d = 1872586212;
        b = -1076171016;
        g = new char[]{61771, 61770, 61757, 61768, 61753, 61774, 61761, 61764, 61729, 61773, 61775, 61752, 61762, 61755, 61721, 61742, 61735, 61725, 61745, 61759, 61946, 61767, 61758};
        i = true;
        f = true;
        h = 782103002;
    }

    static void init$0() {
        $$a = new byte[]{114, 12, -103, 122};
        $$b = 100;
    }

    private static void m(int i2, byte b2, int i3, Object[] objArr) {
        int i4 = i2 + 108;
        int i5 = 3 - (i3 * 2);
        int i6 = 1 - (b2 * 4);
        byte[] bArr = $$a;
        byte[] bArr2 = new byte[i6];
        int i7 = -1;
        int i8 = i6 - 1;
        if (bArr == null) {
            int i9 = i8 + i5;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = -1;
            i8 = i8;
            i4 = i9;
        }
        while (true) {
            int i10 = i7 + 1;
            bArr2[i10] = (byte) i4;
            int i11 = i5 + 1;
            if (i10 == i8) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b3 = bArr[i11];
            int i12 = i4;
            int i13 = i8;
            int i14 = b3 + i12;
            i5 = i11;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i7 = i10;
            i8 = i13;
            i4 = i14;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.p.b c(java.lang.String r32) throws o.eg.d, java.lang.IllegalArgumentException {
        /*
            Method dump skipped, instructions count: 690
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.a.c(java.lang.String):o.p.b");
    }

    private static void k(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        int i5;
        char c2;
        int length;
        byte[] bArr;
        int i6;
        long j2;
        f fVar = new f();
        StringBuilder sb = new StringBuilder();
        int i7 = 2;
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(e)};
            Object obj = o.e.a.s.get(-2120899312);
            long j3 = 0;
            if (obj == null) {
                Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), 65 - KeyEvent.getDeadChar(0, 0));
                byte b3 = (byte) 0;
                byte b4 = b3;
                Object[] objArr3 = new Object[1];
                m(b3, b4, b4, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z = intValue == -1;
            switch (!z) {
                case false:
                    int i8 = $10;
                    int i9 = i8 + 35;
                    $11 = i9 % 128;
                    int i10 = i9 % 2;
                    byte[] bArr2 = c;
                    if (bArr2 != null) {
                        int i11 = i8 + 69;
                        $11 = i11 % 128;
                        switch (i11 % 2 == 0 ? '[' : 'A') {
                            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                                length = bArr2.length;
                                bArr = new byte[length];
                                i6 = 0;
                                break;
                            default:
                                length = bArr2.length;
                                bArr = new byte[length];
                                i6 = 0;
                                break;
                        }
                        while (true) {
                            switch (i6 >= length) {
                                case true:
                                    bArr2 = bArr;
                                    break;
                                default:
                                    int i12 = $10 + 25;
                                    $11 = i12 % 128;
                                    if (i12 % i7 == 0) {
                                        try {
                                            Object[] objArr4 = {Integer.valueOf(bArr2[i6])};
                                            Object obj2 = o.e.a.s.get(494867332);
                                            if (obj2 == null) {
                                                Class cls2 = (Class) o.e.a.c(19 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (char) ((SystemClock.elapsedRealtimeNanos() > j3 ? 1 : (SystemClock.elapsedRealtimeNanos() == j3 ? 0 : -1)) + 16424), 149 - ((byte) KeyEvent.getModifierMetaStateMask()));
                                                byte b5 = (byte) i7;
                                                byte b6 = (byte) (b5 - 2);
                                                Object[] objArr5 = new Object[1];
                                                m(b5, b6, b6, objArr5);
                                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                                o.e.a.s.put(494867332, obj2);
                                            }
                                            bArr[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                            i6 >>>= 1;
                                            i7 = 2;
                                            j3 = 0;
                                        } catch (Throwable th) {
                                            Throwable cause = th.getCause();
                                            if (cause == null) {
                                                throw th;
                                            }
                                            throw cause;
                                        }
                                    } else {
                                        try {
                                            Object[] objArr6 = {Integer.valueOf(bArr2[i6])};
                                            Object obj3 = o.e.a.s.get(494867332);
                                            if (obj3 != null) {
                                                j2 = 0;
                                            } else {
                                                j2 = 0;
                                                Class cls3 = (Class) o.e.a.c(19 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (char) (16425 - KeyEvent.getDeadChar(0, 0)), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + Opcodes.FCMPL);
                                                byte b7 = (byte) 2;
                                                byte b8 = (byte) (b7 - 2);
                                                Object[] objArr7 = new Object[1];
                                                m(b7, b8, b8, objArr7);
                                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                                                o.e.a.s.put(494867332, obj3);
                                            }
                                            bArr[i6] = ((Byte) ((Method) obj3).invoke(null, objArr6)).byteValue();
                                            i6++;
                                            j3 = j2;
                                            i7 = 2;
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    }
                            }
                        }
                    }
                    if (bArr2 == null) {
                        intValue = (short) (((short) (a[i2 + ((int) (b ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L))));
                        break;
                    } else {
                        byte[] bArr3 = c;
                        try {
                            Object[] objArr8 = {Integer.valueOf(i2), Integer.valueOf(b)};
                            Object obj4 = o.e.a.s.get(-2120899312);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getFadingEdgeLength() >> 16) + 11, (char) KeyEvent.normalizeMetaState(0), ((Process.getThreadPriority(0) + 20) >> 6) + 65);
                                byte b9 = (byte) 0;
                                byte b10 = b9;
                                Object[] objArr9 = new Object[1];
                                m(b9, b10, b10, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-2120899312, obj4);
                            }
                            intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L))));
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
            }
            switch (intValue > 0) {
                case true:
                    int i13 = ((i2 + intValue) - 2) + ((int) (b ^ (-5810760824076169584L)));
                    switch (z ? '=' : 'a') {
                        case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                            i5 = 1;
                            break;
                        default:
                            i5 = 0;
                            break;
                    }
                    fVar.d = i13 + i5;
                    try {
                        Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(d), sb};
                        Object obj5 = o.e.a.s.get(160906762);
                        if (obj5 != null) {
                            c2 = 16;
                        } else {
                            c2 = 16;
                            obj5 = ((Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 603)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj5);
                        }
                        ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr4 = c;
                        switch (bArr4 != null ? '1' : '_') {
                            case '1':
                                int length2 = bArr4.length;
                                byte[] bArr5 = new byte[length2];
                                for (int i14 = 0; i14 < length2; i14++) {
                                    int i15 = $10 + Opcodes.DNEG;
                                    $11 = i15 % 128;
                                    int i16 = i15 % 2;
                                    bArr5[i14] = (byte) (bArr4[i14] ^ (-5810760824076169584L));
                                }
                                bArr4 = bArr5;
                                break;
                        }
                        boolean z2 = bArr4 != null;
                        fVar.c = 1;
                        while (fVar.c < intValue) {
                            int i17 = $11;
                            int i18 = i17 + 65;
                            $10 = i18 % 128;
                            int i19 = i18 % 2;
                            switch (z2 ? (char) 17 : c2) {
                                case 16:
                                    short[] sArr = a;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                    break;
                                default:
                                    int i20 = i17 + 15;
                                    $10 = i20 % 128;
                                    switch (i20 % 2 != 0 ? '/' : (char) 31) {
                                        case '/':
                                            byte[] bArr6 = c;
                                            fVar.d = fVar.d >>> 1;
                                            fVar.e = (char) (fVar.b % (((byte) (((byte) (bArr6[r8] - 5810760824076169584L)) + s)) ^ b2));
                                            break;
                                        default:
                                            byte[] bArr7 = c;
                                            fVar.d = fVar.d - 1;
                                            fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                                            break;
                                    }
                            }
                            sb.append(fVar.e);
                            fVar.b = fVar.e;
                            fVar.c++;
                        }
                        break;
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v29, types: [byte[]] */
    private static void l(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        int length;
        char[] cArr2;
        int i3;
        ?? r1 = str2;
        switch (r1 == 0) {
            case false:
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        o.a.j jVar = new o.a.j();
        char[] cArr4 = g;
        Object obj = null;
        switch (cArr4 != null ? (char) 31 : 'T') {
            case Opcodes.BASTORE /* 84 */:
                break;
            default:
                int i4 = $10 + 19;
                $11 = i4 % 128;
                if (i4 % 2 == 0) {
                    length = cArr4.length;
                    cArr2 = new char[length];
                    i3 = 0;
                } else {
                    length = cArr4.length;
                    cArr2 = new char[length];
                    i3 = 0;
                }
                while (true) {
                    switch (i3 >= length) {
                        case true:
                            cArr4 = cArr2;
                            break;
                        default:
                            int i5 = $10 + 99;
                            $11 = i5 % 128;
                            if (i5 % 2 == 0) {
                            }
                            try {
                                Object[] objArr2 = {Integer.valueOf(cArr4[i3])};
                                Object obj2 = o.e.a.s.get(1085633688);
                                if (obj2 == null) {
                                    Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), (char) KeyEvent.getDeadChar(0, 0), Color.rgb(0, 0, 0) + 16777709);
                                    byte b2 = (byte) 0;
                                    Object[] objArr3 = new Object[1];
                                    m((byte) ($$a[1] + 1), b2, b2, objArr3);
                                    obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1085633688, obj2);
                                }
                                obj = null;
                                cArr2[i3] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                                i3++;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                    }
                }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(h)};
            Object obj3 = o.e.a.s.get(-1667314477);
            if (obj3 == null) {
                Class cls2 = (Class) o.e.a.c(10 - TextUtils.getCapsMode("", 0, 0), (char) ((ViewConfiguration.getTouchSlop() >> 8) + 8856), 324 - KeyEvent.normalizeMetaState(0));
                byte b3 = (byte) 0;
                Object[] objArr5 = new Object[1];
                m((byte) 10, b3, b3, objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(obj, objArr4)).intValue();
            int i6 = 9;
            if (f) {
                jVar.e = bArr.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    int i7 = $11 + 29;
                    $10 = i7 % 128;
                    if (i7 % 2 != 0) {
                        cArr5[jVar.c] = (char) (cArr4[bArr[(jVar.e - 0) % jVar.c] % i2] << intValue);
                        try {
                            Object[] objArr6 = {jVar, jVar};
                            Object obj4 = o.e.a.s.get(745816316);
                            if (obj4 == null) {
                                Class cls3 = (Class) o.e.a.c(((byte) KeyEvent.getModifierMetaStateMask()) + 11, (char) (ViewConfiguration.getTouchSlop() >> 8), 206 - Process.getGidForName(""));
                                byte b4 = (byte) 0;
                                Object[] objArr7 = new Object[1];
                                m((byte) i6, b4, b4, objArr7);
                                obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj4);
                            }
                            ((Method) obj4).invoke(obj, objArr6);
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    } else {
                        cArr5[jVar.c] = (char) (cArr4[bArr[(jVar.e - 1) - jVar.c] + i2] - intValue);
                        try {
                            Object[] objArr8 = {jVar, jVar};
                            Object obj5 = o.e.a.s.get(745816316);
                            if (obj5 == null) {
                                Class cls4 = (Class) o.e.a.c(10 - Color.red(0), (char) TextUtils.indexOf("", ""), TextUtils.getOffsetAfter("", 0) + 207);
                                byte b5 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                m((byte) 9, b5, b5, objArr9);
                                obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj5);
                            }
                            ((Method) obj5).invoke(obj, objArr8);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    i6 = 9;
                }
                objArr[0] = new String(cArr5);
                return;
            }
            if (!i) {
                jVar.e = iArr.length;
                char[] cArr6 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr6[jVar.c] = (char) (cArr4[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                    jVar.c++;
                }
                String str3 = new String(cArr6);
                int i8 = $10 + 29;
                $11 = i8 % 128;
                int i9 = i8 % 2;
                objArr[0] = str3;
                return;
            }
            jVar.e = cArr3.length;
            char[] cArr7 = new char[jVar.e];
            jVar.c = 0;
            while (jVar.c < jVar.e) {
                int i10 = $11 + 109;
                $10 = i10 % 128;
                switch (i10 % 2 != 0) {
                    case false:
                        cArr7[jVar.c] = (char) (cArr4[cArr3[(jVar.e - 1) - jVar.c] - i2] - intValue);
                        try {
                            Object[] objArr10 = {jVar, jVar};
                            Object obj6 = o.e.a.s.get(745816316);
                            if (obj6 == null) {
                                Class cls5 = (Class) o.e.a.c(MotionEvent.axisFromString("") + 11, (char) (ViewConfiguration.getMaximumFlingVelocity() >> 16), (Process.myTid() >> 22) + 207);
                                byte b6 = (byte) 0;
                                Object[] objArr11 = new Object[1];
                                m((byte) 9, b6, b6, objArr11);
                                obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj6);
                            }
                            ((Method) obj6).invoke(obj, objArr10);
                            break;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    default:
                        cArr7[jVar.c] = (char) (cArr4[cArr3[(jVar.e >> 0) / jVar.c] >>> i2] % intValue);
                        try {
                            Object[] objArr12 = {jVar, jVar};
                            Object obj7 = o.e.a.s.get(745816316);
                            if (obj7 == null) {
                                Class cls6 = (Class) o.e.a.c(9 - Process.getGidForName(""), (char) (ViewConfiguration.getEdgeSlop() >> 16), TextUtils.indexOf("", "") + 207);
                                byte b7 = (byte) 0;
                                Object[] objArr13 = new Object[1];
                                m((byte) 9, b7, b7, objArr13);
                                obj7 = cls6.getMethod((String) objArr13[0], Object.class, Object.class);
                                o.e.a.s.put(745816316, obj7);
                            }
                            ((Method) obj7).invoke(obj, objArr12);
                            break;
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                }
            }
            objArr[0] = new String(cArr7);
        } catch (Throwable th6) {
            Throwable cause6 = th6.getCause();
            if (cause6 == null) {
                throw th6;
            }
            throw cause6;
        }
    }
}
