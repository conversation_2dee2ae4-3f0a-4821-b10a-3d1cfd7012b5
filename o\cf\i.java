package o.cf;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.List;
import o.cf.j;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\i.smali */
public abstract class i {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int p;
    private static long q;
    private static char[] r;
    private static int s;
    public boolean a;
    private final Boolean b;
    public final Context c;
    private int d;
    private final String e;
    private String f;
    private String g;
    private final o.eg.b h;
    private Long i;
    private o.eg.b j;
    private Location k;
    private o.c.a l;
    private o.ad.c m;
    private byte[][] n;

    /* renamed from: o, reason: collision with root package name */
    private int f49o;
    private boolean t;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        s = 0;
        p = 1;
        r();
        TextUtils.getCapsMode("", 0, 0);
        View.resolveSize(0, 0);
        KeyEvent.keyCodeFromString("");
        View.getDefaultSize(0, 0);
        TextUtils.getOffsetAfter("", 0);
        ExpandableListView.getPackedPositionType(0L);
        int i = s + 41;
        p = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{99, 114, -3, -82};
        $$e = Opcodes.IF_ICMPGE;
    }

    static void r() {
        char[] cArr = new char[3221];
        ByteBuffer.wrap(",\u009a\f¸l\u0093L\u0083¬ü\u008cßìãÌ ,\u0018\f\bldLf¬M\u0088Q¨RÈxèp\b\u001f(1H>hî\u0088ó¨çÈ\u0087è\u0096\b°)FIOif\u0089\f©\u0019É\u0002é;\t\u0093)ªI»i¡\u0089¬©£Ê\u000bêY\n|*\u0012J\u0015j+\u009f\u008d¿\u0084ß¼ÿ \u001fÚ?é_à\u007f\u0018\u009f)\b½(¿H\u0092h¶\u0088û¨ÜÈßè!\b\u000b\u000f0/&O\u0011o\u000e\u008fu¯RÏ[ï¾\u000f³/\u0086Oéoâ\u008fÄ®6Î\u0006î\u0015\u000eq.lNMnF\u008e¦®\u0087Î\u0086îô\u000eÙ.\u008bMvmk\u008dJ\u00ad3Ímí^\r\u0091-¢M\u008cm\u009f\u008d§\u00ad\u008cÍ\u008f\u001e\u0080>\u0096^¡~¾\u009eÅ¾âÞëþ\u000e\u001e\u0003>6^Y~R\u009et¿\u0086ß¶ÿ¥\u001fÁ?Ü_ý\u007fö\u009f\u0016¿7ß6ÿD\u001fi?;\\Æ|Û\u009cú¼\u0083ÜÞüû\u001c\u000e<S\\#|>\u009cA¼fÜs,¨\f\u00adl\u0088L¹¬ü\u008cÛìÔÌ),®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîóÎ×.(\u000e3n\u0005,«\f¯l\u0080L\u009b¬ý\u0016ÿ6éVÞvÁ\u0096º¶\u009dÖ\u0094öq\u0016|6IV&v-\u0096\u000b·ù×É÷Ú\u0017¾7£W\u0082w\u0089\u0097i·H×I÷;\u0017\u00167DT¹t¤\u0094\u0085´üÔ°ô\u0086\u0014w4hTEtG\u0094<´\\Ô-õû\u0015Ü5ÉU¼³Ì\u0093ÚóûÓä3\u0099\u0013»s°S}³s\u0093ló\u0011Ó\fæ\u001dÆ\u000b¦<\u0086#fXF\u007f&v\u0006\u0093æ\u009eÆ«¦Ä\u0086ÏféG\u001b'+\u00078ç\\ÇA§`\u0087kg\u008bGª'«\u0007ÙçôÇ¦¤[\u0084FdgD\u001e$g\u0004{ä\u008fÄ\u0082¤³\u0084²dÅDì,¬\f°l\u0094L\u0099¬ø\u008cÙìÞÌ7[G{Q\u001bf;yÛ\u0002û%\u009b,»É[Ä{ñ\u001b\u009e;\u0095Û³úA\u009aqºbZ\u0006z\u001b\u001a::1ÚÑúð\u009añº\u0083Z®zü\u0019\u00019\u001cÙ=ùD\u00999¹\"YÄyÆ\u0019ç9õÙ\u0094ùä\u0099\u0091¸hX x9\u0018H82Ø%øÈ\u0098Ô,\u0088\f³l\u0085L\u0087¬ö\u008cÄìÕÌe, \f9l!L{¬L\u008d¡í½æ]ÆK¦|\u0086cf\u0018F?&6\u0006ÓæÞÆë¦\u0084\u0086\u008ff©G['k\u0007xç\u001cÇ\u0001§ \u0087+gËGê'ë\u0007\u0099ç´Çæ¤\u001b\u0084\u0006d'D^$#\u00048äÞÄÜ¤ý\u0084ïd\u008eDþ$\u008b\u0005rÜ^üE\u009cs¼q\\\u0000|2\u001c#<úÜû,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîþÎÖ.i\u000e+n\u0004N\u0007®j\u008eDî^Ï«°V\u0090^ðGÐ`0\u001b\u0010.p(PÚ°÷,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîáÎÉ.(\u000e)n\u0007N\u001a®k\u008e@î\u0011Ï«/\u0088\u000f\u0090oä¢&\u0082.,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷ÎÌ.'\u000e:n\u0004N\u0007®i\u008e_îXÏ«/\u009d\u000fÝoòOð¯×\u008f>ï>Ï\u0017/)\u000f|oWOT¨°\u0088\u0081è\u0090Èç(À\bÑh(H!¨\u0000,¯\f´l\u008fL\u0092¬ü\u008cßìÁÌ7,\u0000\f\u0013luLF¬\\\u008d£í¢Í\u008a-û,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîøÎÖ.i\u000e/n\u000eN\u001a®m\u008eHîU,\u009c\f³l\u0080L\u0097¬õ\u008cÈì\u0091Ì1,\u0006\f]lqLp¬K\u008d«í¾Í\u0097-ä\r½mÓMÚ\u00ad6\u008d\u0019íQÍa-L\rIn¤N¶®\u008d\u008eäîþÎË.i\u000e4n\u000fNU®*\u008e\u001dîBÏ /\u008a,\u009b\f²l\u008eL\u0081¬¹\u008céìÔÌ1,\f\f\u001eluL|¬V\u008d£íñÍÈ-©\rïmÎMÚ\u00ad-\u008d?í\u0014Ív-\\\rQnµNï,»\f²l\u008eL\u0081,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîùÎÆ.,§\u008a\u0087\u0095ç¯ÂÏâÙ\u0082î¢ñB\u008ab\u00ad\u0002¤\"AÂLây\u0082\u0016¢\u001dB;cÉ\u0003ù#êÃ\u008eã\u0093\u0083²£¹CYcx\u0003y#\u000bÃ&ãt\u0080\u0089 \u0094@µ`Ì\u0000\u009e ¢ÀK\u0000\u0094 \u0088@±,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷ÎÌ.'\u000e:n\u0004N\u0007®i\u008e_îXÏ«/\u009d\u000fÝo¬Oµ¯÷\u008f\"ï%ÏE/`\u000fsoHOA¨°\u0088\u008cè\u009dÈì(Ó\bØh%ÿ\u0095ß\u0083¿´\u009f«\u007fÐ_÷?þ\u001f\u001bÿ\u0016ß#¿L\u009fG\u007fa^\u0093>£\u001e°þÔÞÉ¾è\u009eã~\u0003^\">#\u001eQþ|Þ.½Ó\u009dÎ}ï]\u0096=Ì\u001d÷ý\u001cÝ\u0001½?\u009d<}R]d=c\u001c\u0090ü¦,¯\f´l\u008fL\u0092¬ü\u008cßìÁÌ7,\u0000\f\u0013lu,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷Î\u0094,¯\fì,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷Î\u0097,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîòÎÄ.'\u000e3n\u000eN\u0001®9\u008eJîTÏ±/É\u000f\u009foàOö¯Ò\u008f(ï?Ï\u0001/)\u000fioNO^¨¼\u0088\u0083lùL¹F1f'\u0006\u0010&\u000fÆtæS\u0086Z¦¿F²f\u0087\u0006è&ãÆÅç7\u0087\u0007§\u0014Gpgm\u0007L'GÇ§ç\u0086\u0087\u0087§õGØg\u008a\u0004w$jÄKä2\u0084h¤\t\u008cA¬\u0000,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷Î\u0091$Ü\u0004\u009a,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷Î\u0090¿Ö\u009f\u0091,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adî÷Î\u0093\u009aïº«H\u008eh\u0098\b¯(°ÈËèì\u0088å¨\u0000H\rh8\bW(\\Èzé\u0088\u0089¸©«IÏiÒ\tó)øÉ\u0018é9\u00898©JIgi5\nÈ*ÕÊôê\u008d\u008aÁª÷J\u0006j\u0019\n4*6ÊMê-\u008aw«\u008cK§kº\u000bÄ+ÇËéë\u001f\u008b\u0018«+K],¹\f¯l\u008eL\u0091¬ì\u008cÎìÅÌ\u0003,\u0000\f\u0013lfLp¬K\u008d½í£Í\u008c-ç\réALaZ\u0001m!rÁ\tá.\u0081'¡ÂAÏaú\u0001\u0095!\u009eÁ¸àJ\u0080z i@\r`\u0010\u00001 :ÀÚàû\u0080ú \u0088@¥`÷\u0003\n#\u0017Ã6ãO\u00837£\"CÝcÖ\u0003à#òÃÛã\u0086\u0083\u0097¢\u0007Bjbt\u0002\u0002\"YÂ{âî\u0082ý¢ÃB¹b°\u0002\u008a\"\u0093ÅdåF\u0085W\u008c¶¬¥Ì\u008cì\u008d\fü,ÂLùl,\u008c\u0013¬\u0018Ìnì|\f|-¥,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîÕÎÀ.?\u000e4n\u0002N\u0010®9\u008eCîPÏ¨/\u008c\u000fÝoçOü¯×\u008f*ï4Ï\u0017/y\u000fooHO[¨\u00ad,\u00ad\f¸l\u0097L\u009c¬ú\u008cÈìÿÌ$,\u0004\f\u0018lGL|¬W\u008dªí´Í\u0097-ù\rïmÈMÛ\u00ad-,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîÒÎÐ.;\u000e/n\u0004N\u001b®m\u008e\rî]Ï¬/\u008b\u000f\u009eo¡Oý¯Ø\u008f>ï9ÏE/{\u000fxoQOG¨¼\u0088\u009eè\u0094Èë(Ý\bÜh5H<¨\u0016\u0088cè1È\r)¯\tìiÑIÄ©°,¯\fìlÑLÄ,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîÒÎÊ.;\u000e/n\u0004N\u0016®m\u008e\rî]Ï¬/\u008b\u000f\u009eo¡Oý¯Ø\u008f>ï9ÏE/{\u000fxoQOG¨¼\u0088\u009eè\u0094Èë(Ý\bÜh5H<¨\u0016\u0088cè1È\r)¯\tìiÑIÇ©°,¯\fìlÑLÇ,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîÝÎö.\u0002\u000e}n\u0011N\u0000®{\u008eAîXÏ¦/É\u000f\u0096oäOì¯\u0099\u008f.ï4Ï\u0017/}\u000ftoGO\\¨º\u0088\u008cè\u0085Èà(\u0089\bÞh)H4¨\u0010\u0088cMÿmå\rÅ-ÛÍ°í\u009f\u008d\u009e\u00ad}MwmE\r.-<Í\rìö\u008cå¬ÛLµl´\f\u0099,«ÌlìQ\u008cE¬6\u00992¹$Ù\u0017ù\u0003\u0019n9AY\ty»\u0099\u0094¹\u0091Ùúùå\u0019\u008189X:x\u0016\u00981¸fØ\\ø_\u0018µ8\u009cX\u008fxô\u0098Ò¸ÄÛ-û(\u001bA;v[a{\\\u009b¸»«\u001d\u0094=\u0082]µ}ª\u009dÑ½öÝÿý\u001a\u001d\u0017=\"]M}F\u009d`¼\u0092Ü¢ü±\u001cÕ<È\\é|â\u009c\u0002¼#Ü\"üP\u001c}</_Ò\u007fÏ\u009fî¿\u0097ßûÿê\u001f\u0000?\u000f_{\u007f\"\u009fF¿dßxþ\u009e\u001e´>®^Õ~È\u009e£¾\u0013Þ\u0002þ,\u001eR>E^w~j\u0099\u0087¹÷Ù©ùÆ\u0019³9åY\u001ay\f\u0099(¹RÙEù{\u0018Ó8\u0088X©xï\u0098Í¸øØÿø_\u0018#85X^x\\\u0098f»\u0099Û\u009fûÿ\u001bÚ;É[»{Ü\u009b'»\u001c,®\f¸l\u008fL\u0090¬ë\u008cÌìÅÌ ,-\f\u0018lwL|¬Z\u008d¨í\u0098Í\u008b-ï\ròmÓMØ\u00ad8\u008d\u0019í\u0018Íj-G\r\u0015nèNõ®Ô\u008e\u00adîáÎÐ.:\u000e5nAN;®v\u008eYîXÏ£/\u0080\u000f\u009eoàOá¯Ð\u008f\"ï?ÏE/]\u000froJOP¨·\u0088ÍèËÈ¥(x\bnhYHF¨=\u0088\u001aè\u0013Èö(û\bÎh¡Hª¨\u008c\u0089~éNÉ])9\t$i\u0005I\u000e©î\u0089ÏéÎÉ¼)\u0091\tÃj>J#ª\u0002\u008a{ê7Ê\u0006*ì\nãj\u0097Jíª \u008a\u008fê\u008eËu+V\u000bHk6K7«\u0006\u008bôëéË\u0093+\u008f\u000b¹k\u0098K\u0095¬f\u008c_ìBÌ!,_\f%löLî¬Ê\u008cûìýÌÓb\u008aB\u0091\"®\u0002 âÝÂÀ¢ô\u0082\u0016b:B<\"F\u0002\\âwÃ\u008a£¸\u0083¡ê~ÊeªZ\u008aTj)J4*\u0000\nâêÎÊÈª²\u008a¨j\u0083K~+U\u000bCë2Ë?«\u001c\u008b\u0005kèKË ³\u0000¥`\u0092@\u008d ö\u0080ÑàØÀ= 0\u0000\u0005`j@a G\u0081µá\u0085Á\u0096!ò\u0001ïaÎAÅ¡%\u0081\u0004á\u0005Áw!Z\u0001\bbõBè¢É\u0082°âéÂÎ\"1\u00022b\u0005B\u001c¢l\u0082YâBÃ¿#Ô\u0003\u008fc÷\u0003\u008c#\u0089Cµc\u0093\u0083È£àÃùã\u0005\u0003\u0003#,CHcS\u0083x¢\u009bo«O®/\u0092\u000f´ïïÏÇ¯Þ\u008f\"o%O\u000e/v\u000f\u007fïUÎ ®¡5N\u0015Mu~UWµ\b\u0095+õ6ÕØ5ò\u0015ç,¿\føl\u0092LØ¬¼\u008cÞì\u009cÌ`,\u001a\fPl$Lf¬\u0014\u008dèí¢µø\u0095éz´Z¤5\u0096è\tÈ\u001b\u0006\u0014&\u0002,¥\f²l\u0086,½\f®l\u0091L\u0086\u0096Ï¶ÙÖøöç\u0016\u009a6¸V³v@\u009bÛ»ÀÛí\u009e\u009a¾\u008bÞ¬þ¦\u001eË>à^â~\u0016\u009e'¾/ÞSþD\u001e9?Ê_Ð\u007fµ\u0089K©PÉ`é|\u009f²¿¢ß\u008aÿ\u0093\u001fú?Á_\u0096\u007f-\u009f\u0002¿\u0019ßhÿp\u001fP>¤^²~\u008b\u009eæ¾ùÞßþÚ,®\f¸l\u0095L´¬é\u008cÝìóÌ0,\u0000\f\u0011leLZ¬I\u008d¹í¸Í\u008a-ç\r½m\u008cM\u0095\u00ad\f\u008d\u0003í\u0010Íg-E\rXnáN¡®\u0096\u008e\u00adîòÎÊ.$\u000e-n\u0014N\u0001®|\u008e\rîPÏµ/\u0099\u000fÝoãOà¯Ð\u008f!ï5ÏE/f\u000fmoUO\\¨¶\u0088\u0083è\u0082,û\fól×LÛ¬\u00adPêpê\u0010×0ßÐ\u0098ð\u0080\u0090\u0097°bPxp|\u0010\u0000PÅpÜ\u0010ò0áÐºð¢\u0090¿°CP`pz\u001040\u0005Ð=ñÌ\u0091Å±àQ\u00adq\u009c\u0011µ1°ð\u0093Ð\u008a°£\u0097Í·Ú×ç\u001bÖ;É[ø{ý\u009b\u009a»§Û¿ûo\u001br;~[\u0017{\u0000\u009b\"ºÓÚ\u008bú²\u001aÓ:\u0097Z©z \u009a@ºrÚxú\f\u001a::)YÜy\u008f\u0099â¹\u0082Ù\u009fù·\u0019V9IYoyf\u0099\u0000¹6Ù?øÖ\u0018ü8éXÛx\u009a\u0098³¸SØJøk\u0018\u00168GX=x&\u009fÆ¿ûßïÿ\u008c\u009eY¾FÞhþ[\u001e\u0006> ^<~Ø\u009eø¾çÞ\u009a,¬\f³l\u0082L\u0087¬à\u008cÝìÅÌ\u0015,\b\f\u0004lmLz¬X\u008d©íñÍÈ-©\rímÓMÚ\u00ad:\u008d\bí\u0002Ív-@\rSn¦Nõ®\u0098\u008eøîåÎÍ.,\u000e3n\u0015N\u001c®z\u008eLîEÏ¬/\u0086\u000f\u0093o¡Oó¯Ð\u008f(ï=Ï\u0001/z,¹\f¼l\u0092L\u0086¬ú\u008cÂìÕÌ ,¹\f¼l\u0092L\u0086¬ú\u008cÂìÕÌ ,-\f\u001cluLt,¥\f¾l\u0085L\u0096¬ï\u008cÀìòÌ7,\u0010\f\rluLz¬^\u008d¿í°Í\u0088,¼\f®l\u0084L\u0087¬Ú\u008cßìÔÌ!,\f\f\u0013luL|¬X\u008d¡í¢\u0087_§@Çqçt\u0007\u0013'.G6gæ\u0087û§÷Ç\u009eç\u0089\u0007«&ZF\u0002f;\u0086Z¦\u001eÆ3æ?\u0006Æ&ñFãf\u0092\u0086ú¦ºÅ]å\u0006\u0005o%\u0010E\u0001e$\u0085Ã¥ÞÅæå¼\u0005Ê,¬\f³l\u0082L\u0087¬à\u008cÝìÅÌ\u0015,\b\f\u0004lmLz¬X\u008d©íñÍÈ-©\rømÏMÖ\u00ad+\u008d\u0014í\u0001Íq-L\rYnáN¥®\u0098\u008eôîýÎÊ.(\u000e9nANO®9Ë~ëa\u008bP«UK2k\u000f\u000b\u0017+ÇËÚëÖ\u008b¿«¨K\u008aj{\n#*\u001aÊ{ê*\u008a\u001dª\u0004JùjÆ\nÓ*£Ê\u009eê\u008b\u00893©wIJi&\t/)\u0018Éúéë\u0089\u0093©ÎI¸iß\t\u008d(bÈWèC\u0088s¨}HKhú\bñ(ÅÈ´è½,¬\f³l\u0082L\u0087¬à\u008cÝìÅÌ\u0015,\b\f\u0004lmLz¬X\u008d©íñÍÈ-©\r×mòMú\u00ad\u0017\u008d(í\tÍf-L\rMnµN¼®\u0096\u008eãeHEC%c\u0005p,\u0088\f¾l\u0082L\u0090¬é\u008cÙì\u009cÌ\u0000,\u0007\f\u001elnLq¬P\u008d£í¶, \f¹l\u0084L\u009b¬í\u008cÄìÅÌ<,\u008a\f²l\u008fL\u0081¬ü\u008cÃìÅÌh,=\f\u0004lqLp,¨\f\u00adl\u0091L\u0099¬ð\u008cÎìÐÌ1,\u0000\f\u0012loL:¬S\u008d¾í¾Í\u008b,\u0091\fðl¨L\u0086¬ê\u008cØìÔÌ7,D\f4le,¤\f²l\u0083L\u009c¬õ\u008cÈì\u009c,\u0091\fðl´L\u0086¬ü\u008cßì\u009cÌ\f,\r\nÊ*«JèjË\u008a³ª\u0083Ê\u008fêm\nF*\u000bJ\u0013j*,º\f¸l\u008fL\u0091¬Ý\u008cÌìÅÌ$,=\f\u0012lCLt¬Z\u008d¦í´Í\u008b-í\r½m\u008cM\u0095\u00ad\u000b\u008d\bí\u0000Íp-L\rNnµNõ®°\u008eÉî±\u0002\\\"^Bibw\u0082;¢*Â#âÂ\u0002Û\"ôB¥b\u0092\u0082¼£@ÃRãm\u0003\u000b#[C}cs\u0083Í£îÃäã\u0093\u0003 #µ@T`V\u0080? \bÀ\u0018à'\u0000Ê \u009b@½`³aNAL!{\u0001eá)Á8¡1\u0081ÐaÉAæ!·\u0001\u0080á®ÀR @\u0080\u007f`\u0019@I o\u0000aàßÀü ö\u0080\u0081`²@§#F\u0003Dã-Ã\u0014£\u0000\u0083\"cÎCÈ#ò\u0003äãÍÃã£åe\rE\u000f%8\u0005&åjÅ{¥r\u0085\u0093e\u008aE¥%ô\u0005ÃåíÄ\u0011¤\u0003\u0084<dZD\n$;\u0004\"ä\u008cÄ»¤¢\u0084\u0092döDþ'\u0002\u0007\u0012çnÇH§C\u0087ag\u008eG\u0085'¸\u0007±çËÇº§å\u0086\u001df:F/&\u0016\u0006\u0018æ.,º\f¸l\u008fL\u0091¬Ý\u008cÌìÅÌ$,=\f\u0012lCLt¬Z\u008d¦í´Í\u008b-í\r½m\u008cM\u0095\u00ad\u0010\u008d\"í4Í}-J\rXn±N¡®\u0090\u008eâîÿ,¬\f¥l\u0084L\u0096¬ì\u008cÙìÔÌ\u0015,\u0006\f\u000eluL=¬\u0010\u008dííüÍÅ-Û\rømÐMÀ\u00ad<\u008d\u001eí\u0005Í%-Y\r\\n¸N¹®\u0096\u008eìîõÎ\u0085.9\u000e/n\u0004N\u0005®x\u008e_îPÏ±/\u0080\u000f\u0092oïOµ¯ß\u008f,ï8Ï\t/l\u000fyo\u0001O\u000f¨ù\u0088§è\u0082Èê(Ç\b\u009dh\u0004H-¨\u001a\u0088hèaÈQ) \t²i\u008f,\u009b\f¸l\u0090L\u0080¬ü\u008cÞìÅÌe,\u0019\f\u001clxLy¬V\u008d¬íµÍÅ-ù\rïmÄMÅ\u00ad8\u008d\u001fí\u0010Íq-@\rRn¯Nõ®\u009f\u008eìîøÎÉ.,\u000e9nMNU®S\u008e^î^Ï«/É\u000f¸oùOö¯Ü\u008f=ï%Ï\f/f\u000fsb¾B·\"\u0096\u0002\u0084âþÂË¢Æ\u0082\u0007b\u0014B\u001c\"g\u0002/â\u0002Ãÿ£î\u0083×cÕCê#Ç\u0003Ðã$Ã\r£\b\u0083{c^C\\  \u0000çà\u008eÀç à\u0080ß`:@! \u0014\u0000\u0002,¬\f¥l\u0084L\u0096¬ì\u008cÙìÔÌ\u0015,\u0006\f\u000eluL=¬\u0010\u008dííüÍÅ-Û\rømÐMÀ\u00ad<\u008d\u001eí\u0005Í%-Y\r\\n¸N¹®\u0096\u008eìîõÎ\u0085.,\u000e3n\u0002N\u0007®`\u008e]îEÏ¬/\u0086\u000f\u0093o¡Oó¯Ø\u008f$ï=Ï\u0000/m~k^b>C\u001eQþ+Þ\u001e¾\u0013\u009eÒ~Á^É>²\u001eúþ×ß*¿;\u009f\u0002\u007f\u001c_??\u0017\u001f\u0007ÿûßÙ¿Â\u009fâ\u007f\u009e_\u0088<c\u001cbü_Ü8¼7\u009c\u0016|ç\\õ<È\u001c\u0092ü¸Ü\u0083¼\u0098\u009dc}B]S=<\u001d3ý\nÝã½ù\u009dÌ}î]¼=\u0087\u001d\u009búrÚOºR,¬\f¥l\u0084L\u0096¬ì\u008cÙìÔÌ\u0015,\u0006\f\u000eluL=¬\u0010\u008dííüÍÅ-Ç\ròm\u0081Mû\u00ad<\u008d\u0019í\u0006Íj-[\rVnáN\u0096®\u0096\u008eãîÿÎÀ.*\u000e)n\bN\u0003®p\u008eYîHÏå/\u0088\u000f\u008boàOü¯Õ\u008f,ï3Ï\t/l7¬\u0017\u0099wêW\u0090·×\u0097ò÷í×\u000170\u0017=w\nW}·}\u0096\u0088ö\u0094Ö«6Á\u0016ÂvãVè¶\u001b\u00962ö#Ö\u000e6c\u0016`u\u008bU\u0097µ¾\u0095ÇõØÕâ5\u0007G½g¸\u0007\u0084'\u008cÇåçÛ\u0087Å§$G\u0015g\u0007\u0007z'IÇh,¯\f´l\u008fL\u009c¬ê\u008cÅìõÌ$,\u001d\f\u001clQLg¬\\\u008d½í°Í\u0097-è\rémÈMÚ\u00ad7\u008dMí\\Í%-Y\rOn¤N¥®\u0098\u008eÿîôÎ\u0085.(\u000e>n\u0015N\u001c®v\u008eCî\u0011Ï¬/\u008d,¯\f´l\u008fL\u009c¬ê\u008cÅìõÌ$,\u001d\f\u001clQLg¬\\\u008d½í°Í\u0097-è\rémÈMÚ\u00ad7\u008dMí\\Í%-H\r^nµN¼®\u0096\u008eãî±ÎÌ.-\u000e}n\u0011N\u0007®|\u008e]îPÏ·/\u008c\u000f\u0099o¡O¯¯\u0099,ª,\u0088\f\u0093l¥L§¬Ö\u008cäìõ¹.\u0099+ù\u0015Ù\t9o\u0019]yCY\u009c¹\u009f\u0099\u0087ùó,¯\f´l\u008fL\u009c¬ê\u008cÅìõÌ$,\u001d\f\u001clQLg¬\\\u008d½í°Í\u0097-è\rémÈMÚ\u00ad7\u008dEíXÍ%-\u0004\r\u001dn\u008bN\u0086®¶\u008eÃîÔÎÝ.*\u000e8n\u0011N\u0001®p\u008eBî_,ä\fì, \f³l\u0097L\u0094¬õ\u008cÄìÕÌe,\n\f\u000fldLq¬\\\u008d£í¥Í\u008c-è\rñmÒ".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 3221);
        r = cArr;
        q = -3086594881857450787L;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Type inference failed for: r7v2, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void z(short r5, short r6, byte r7, java.lang.Object[] r8) {
        /*
            int r5 = r5 * 4
            int r5 = r5 + 1
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r6 = r6 + 102
            byte[] r0 = o.cf.i.$$d
            byte[] r1 = new byte[r5]
            int r5 = r5 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r4 = r7
            r3 = r2
            goto L28
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r5) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            int r3 = r3 + 1
            r4 = r0[r7]
        L28:
            int r7 = r7 + 1
            int r6 = r6 + r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.z(short, short, byte, java.lang.Object[]):void");
    }

    public abstract void b() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d;

    public abstract String c();

    protected final void finalize() throws Throwable {
        int i = s + Opcodes.DDIV;
        p = i % 128;
        int i2 = i % 2;
    }

    public i(Context context, int i, Boolean bool) {
        this.t = true;
        this.c = context;
        this.d = i;
        this.b = bool;
        this.e = d(context);
        this.h = new o.eg.b();
    }

    public i(Context context, int i) {
        this(context, i, null);
    }

    private static String d(Context context) {
        int i = s + 83;
        p = i % 128;
        Object obj = null;
        try {
            switch (i % 2 == 0 ? 'I' : '\f') {
                case 'I':
                    o.bi.e.d(context).a();
                    obj.hashCode();
                    throw null;
                default:
                    return o.bi.e.d(context).a();
            }
        } catch (o.bi.c e) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            y((char) (ViewConfiguration.getWindowTouchSlop() >> 8), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1, (ViewConfiguration.getJumpTapTimeout() >> 16) + 13, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            y((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 42217), 12 - ((byte) KeyEvent.getModifierMetaStateMask()), 33 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            return null;
        }
    }

    private Context e() {
        int i = p + 89;
        s = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.c;
        }
    }

    public final String f() {
        int i = p + 43;
        s = i % 128;
        switch (i % 2 != 0 ? (char) 14 : 'D') {
            case 'D':
                return this.g;
            default:
                throw null;
        }
    }

    public final int i() {
        int i = p;
        int i2 = i + 19;
        s = i2 % 128;
        int i3 = i2 % 2;
        int i4 = this.d;
        int i5 = i + 37;
        s = i5 % 128;
        int i6 = i5 % 2;
        return i4;
    }

    public final void j() {
        int i;
        int i2 = s + Opcodes.DNEG;
        p = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                i = 16;
                break;
            default:
                i = 83;
                break;
        }
        this.d = i;
    }

    public final o.eg.b g() {
        int i = s + 5;
        p = i % 128;
        switch (i % 2 == 0 ? (char) 18 : '!') {
            case '!':
                return this.h;
            default:
                int i2 = 93 / 0;
                return this.h;
        }
    }

    public final void c(int i) {
        int i2 = s + 7;
        p = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.f49o = i;
        switch (z) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void d(Long l) {
        int i = s;
        int i2 = i + 49;
        p = i2 % 128;
        int i3 = i2 % 2;
        this.i = l;
        int i4 = i + 99;
        p = i4 % 128;
        switch (i4 % 2 == 0 ? 'W' : '\b') {
            case '\b':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void e(Location location) {
        int i = s;
        int i2 = i + 9;
        p = i2 % 128;
        int i3 = i2 % 2;
        this.k = location;
        int i4 = i + Opcodes.DDIV;
        p = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void a(o.ad.c cVar) {
        int i = s + 75;
        int i2 = i % 128;
        p = i2;
        char c2 = i % 2 == 0 ? (char) 6 : 'D';
        this.m = cVar;
        switch (c2) {
            case 'D':
                int i3 = i2 + 109;
                s = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return;
                    default:
                        int i4 = 80 / 0;
                        return;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void e(o.c.a aVar) {
        int i = s + 19;
        int i2 = i % 128;
        p = i2;
        int i3 = i % 2;
        this.l = aVar;
        int i4 = i2 + 93;
        s = i4 % 128;
        switch (i4 % 2 != 0 ? '/' : (char) 5) {
            case '/':
                throw null;
            default:
                return;
        }
    }

    public final void e(j jVar, byte[][] bArr) throws o.eg.d {
        byte[][] bArr2;
        switch (bArr != null ? '\r' : (char) 25) {
            case '\r':
                this.n = bArr;
                break;
        }
        int i = 1;
        switch (jVar != null) {
            case false:
                break;
            default:
                int i2 = s + 41;
                p = i2 % 128;
                if (i2 % 2 == 0) {
                    bArr2 = new byte[1][];
                    switch (this.n == null) {
                        case false:
                            bArr2 = this.n;
                            i = bArr2.length;
                            break;
                    }
                    j.e d = jVar.d(i);
                    this.j = d.b();
                    byte[][] bArr3 = new byte[bArr2.length + d.e().length][];
                    System.arraycopy(bArr2, 0, bArr3, 0, bArr2.length);
                    System.arraycopy(d.e(), 0, bArr3, bArr2.length, d.e().length);
                    this.n = bArr3;
                    int i3 = s + 41;
                    p = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                } else {
                    bArr2 = new byte[0][];
                    if (this.n == null) {
                        i = 0;
                        j.e d2 = jVar.d(i);
                        this.j = d2.b();
                        byte[][] bArr32 = new byte[bArr2.length + d2.e().length][];
                        System.arraycopy(bArr2, 0, bArr32, 0, bArr2.length);
                        System.arraycopy(d2.e(), 0, bArr32, bArr2.length, d2.e().length);
                        this.n = bArr32;
                        int i32 = s + 41;
                        p = i32 % 128;
                        int i42 = i32 % 2;
                    }
                    bArr2 = this.n;
                    i = bArr2.length;
                    j.e d22 = jVar.d(i);
                    this.j = d22.b();
                    byte[][] bArr322 = new byte[bArr2.length + d22.e().length][];
                    System.arraycopy(bArr2, 0, bArr322, 0, bArr2.length);
                    System.arraycopy(d22.e(), 0, bArr322, bArr2.length, d22.e().length);
                    this.n = bArr322;
                    int i322 = s + 41;
                    p = i322 % 128;
                    int i422 = i322 % 2;
                }
        }
    }

    protected final void c(boolean z) throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        int i = s + 1;
        p = i % 128;
        int i2 = i % 2;
        o.ee.e.a();
        long j = o.ee.c.j();
        o.eg.b bVar = this.h;
        Object[] objArr = new Object[1];
        y((char) (TextUtils.indexOf("", "", 0) + 45872), (ViewConfiguration.getTapTimeout() >> 16) + 45, View.MeasureSpec.getMode(0) + 9, objArr);
        bVar.d(((String) objArr[0]).intern(), String.valueOf(j));
        o.eg.b bVar2 = this.h;
        Object[] objArr2 = new Object[1];
        y((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 9223), 55 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 9, objArr2);
        bVar2.d(((String) objArr2[0]).intern(), c(z, j));
        int i3 = p + 81;
        s = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final void h() throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        int i = s + 43;
        p = i % 128;
        int i2 = i % 2;
        c(false);
        int i3 = s + 99;
        p = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:47:0x09d1. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:36:0x0a61  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private o.eg.b c(boolean r22, long r23) throws o.eg.d, o.bt.d, o.bn.c, o.bp.d {
        /*
            Method dump skipped, instructions count: 3122
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.c(boolean, long):o.eg.b");
    }

    protected final void m() throws o.eg.d {
        int i = p + Opcodes.LSHR;
        s = i % 128;
        int i2 = i % 2;
        o.eg.b bVar = this.h;
        Object[] objArr = new Object[1];
        y((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 12068), AndroidCharacter.getMirror('0') + 1841, 14 - KeyEvent.getDeadChar(0, 0), objArr);
        bVar.d(((String) objArr[0]).intern(), s());
        int i3 = p + Opcodes.DMUL;
        s = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    protected final void l() throws o.eg.d {
        o.eg.b bVar;
        Object obj;
        int i = p + 47;
        s = i % 128;
        switch (i % 2 != 0 ? '5' : (char) 11) {
            case 11:
                bVar = this.h;
                Object[] objArr = new Object[1];
                y((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 17155), (ViewConfiguration.getLongPressTimeout() >> 16) + 1903, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 14, objArr);
                obj = objArr[0];
                break;
            default:
                bVar = this.h;
                Object[] objArr2 = new Object[1];
                y((char) (15833 - (ViewConfiguration.getMaximumFlingVelocity() << 51)), 18748 << (ViewConfiguration.getLongPressTimeout() + 20), Opcodes.FNEG >>> (SystemClock.elapsedRealtimeNanos() > 1L ? 1 : (SystemClock.elapsedRealtimeNanos() == 1L ? 0 : -1)), objArr2);
                obj = objArr2[0];
                break;
        }
        bVar.d(((String) obj).intern(), q());
    }

    protected final void n() throws o.eg.d {
        int i = p + 27;
        s = i % 128;
        int i2 = i % 2;
        o.eg.b bVar = this.h;
        Object[] objArr = new Object[1];
        y((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 6643), 1966 - AndroidCharacter.getMirror('0'), Gravity.getAbsoluteGravity(0, 0) + 10, objArr);
        bVar.d(((String) objArr[0]).intern(), u());
        int i3 = p + 79;
        s = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private static String s() {
        int i = s + 41;
        p = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        y((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 1928 - ExpandableListView.getPackedPositionType(0L), 15 - TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        y((char) (39170 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 1943, 2 - View.MeasureSpec.getMode(0), objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        y((char) (22093 - View.MeasureSpec.getMode(0)), KeyEvent.getDeadChar(0, 0) + 1945, 2 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        y((char) (6496 - TextUtils.getOffsetBefore("", 0)), (ViewConfiguration.getPressedStateDuration() >> 16) + 1947, 1 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr4);
        String intern4 = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        y((char) (50416 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), MotionEvent.axisFromString("") + 1949, TextUtils.indexOf((CharSequence) "", '0') + 3, objArr5);
        String intern5 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        y((char) (10989 - (ViewConfiguration.getEdgeSlop() >> 16)), KeyEvent.normalizeMetaState(0) + 1950, (ViewConfiguration.getFadingEdgeLength() >> 16) + 2, objArr6);
        String format = String.format(intern, intern2, intern3, intern4, intern5, ((String) objArr6[0]).intern());
        int i3 = s + Opcodes.LREM;
        p = i3 % 128;
        switch (i3 % 2 == 0 ? '4' : '!') {
            case '!':
                return format;
            default:
                int i4 = 6 / 0;
                return format;
        }
    }

    private static o.eg.b q() {
        o.eg.b bVar = new o.eg.b();
        try {
            o.ee.e.a();
            Object[] objArr = new Object[1];
            y((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 1953, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 2, objArr);
            bVar.d(((String) objArr[0]).intern(), true);
            Object[] objArr2 = new Object[1];
            y((char) (ViewConfiguration.getWindowTouchSlop() >> 8), TextUtils.getCapsMode("", 0, 0) + 1955, 4 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
            bVar.d(((String) objArr2[0]).intern(), new o.eg.e((List<?>) o.e(e.e, o.ee.c.i())));
            Object[] objArr3 = new Object[1];
            y((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 47734), Color.argb(0, 0, 0, 0) + 1959, View.resolveSize(0, 0) + 8, objArr3);
            bVar.d(((String) objArr3[0]).intern(), new o.eg.e((List<?>) o.e(e.d, o.ee.c.b())));
            Object[] objArr4 = new Object[1];
            y((char) (46964 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), 1967 - (ViewConfiguration.getWindowTouchSlop() >> 8), KeyEvent.keyCodeFromString("") + 3, objArr4);
            String intern = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            y((char) (Color.alpha(0) + 45667), 2018 - AndroidCharacter.getMirror('0'), 15 - ImageFormat.getBitsPerPixel(0), objArr5);
            bVar.d(intern, ((String) objArr5[0]).intern());
            Object[] objArr6 = new Object[1];
            y((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 42475), 1985 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), (ViewConfiguration.getTapTimeout() >> 16) + 4, objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            y((char) (45834 - ExpandableListView.getPackedPositionType(0L)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 1990, 21 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr7);
            bVar.d(intern2, ((String) objArr7[0]).intern());
            int i = s + 95;
            p = i % 128;
            int i2 = i % 2;
        } catch (o.eg.d e) {
            o.ee.g.c();
            Object[] objArr8 = new Object[1];
            y((char) ExpandableListView.getPackedPositionGroup(0L), '0' - AndroidCharacter.getMirror('0'), 13 - TextUtils.indexOf("", ""), objArr8);
            String intern3 = ((String) objArr8[0]).intern();
            Object[] objArr9 = new Object[1];
            y((char) Gravity.getAbsoluteGravity(0, 0), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 2010, 55 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr9);
            o.ee.g.a(intern3, ((String) objArr9[0]).intern(), e);
        }
        int i3 = s + Opcodes.LMUL;
        p = i3 % 128;
        int i4 = i3 % 2;
        return bVar;
    }

    private static String u() {
        Object obj;
        int i = p + Opcodes.LSHL;
        s = i % 128;
        switch (i % 2 != 0 ? 'V' : (char) 5) {
            case Opcodes.SASTORE /* 86 */:
                Object[] objArr = new Object[1];
                y((char) (Process.myTid() << Opcodes.DNEG), 10776 / TextUtils.indexOf((CharSequence) "", '`', 1), 2 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                y((char) (Process.myTid() >> 22), 2064 - TextUtils.indexOf((CharSequence) "", '0', 0), 4 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0077, code lost:
    
        o.ee.g.c();
        r5 = new java.lang.Object[1];
        y((char) (android.widget.ExpandableListView.getPackedPositionForGroup(0) > 0 ? 1 : (android.widget.ExpandableListView.getPackedPositionForGroup(0) == 0 ? 0 : -1)), android.view.ViewConfiguration.getEdgeSlop() >> 16, 13 - android.view.View.MeasureSpec.makeMeasureSpec(0, 0), r5);
        r0 = ((java.lang.String) r5[0]).intern();
        r1 = new java.lang.Object[1];
        y((char) (((byte) android.view.KeyEvent.getModifierMetaStateMask()) + 1), 757 - (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16), 58 - android.view.MotionEvent.axisFromString(""), r1);
        o.ee.g.d(r0, ((java.lang.String) r1[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x00ca, code lost:
    
        throw new o.bn.c(o.bn.c.e.c);
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x002e, code lost:
    
        r0 = r7.h;
        r1 = new java.lang.Object[1];
        y((char) (31810 - (android.view.ViewConfiguration.getKeyRepeatDelay() >> 16)), (android.view.ViewConfiguration.getWindowTouchSlop() >> 8) + 2070, (android.view.ViewConfiguration.getMinimumFlingVelocity() >> 16) + 11, r1);
        r0.d(((java.lang.String) r1[0]).intern(), o.bn.d.d(r7.c));
        r0 = o.cf.i.p + com.esotericsoftware.asm.Opcodes.LNEG;
        o.cf.i.s = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0069, code lost:
    
        if ((r0 % 2) == 0) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x006c, code lost:
    
        r2 = '1';
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x006e, code lost:
    
        switch(r2) {
            case 49: goto L21;
            default: goto L22;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0072, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x0074, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0025, code lost:
    
        if (o.bn.e.b().d() != null) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x002b, code lost:
    
        if (o.bn.e.b().d() != null) goto L15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected final void o() throws o.eg.d, o.bn.c {
        /*
            r7 = this;
            int r0 = o.cf.i.s
            int r0 = r0 + 9
            int r1 = r0 % 128
            o.cf.i.p = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r2
            goto L11
        L10:
            r0 = r1
        L11:
            switch(r0) {
                case 1: goto L1d;
                default: goto L14;
            }
        L14:
            o.bn.e r0 = o.bn.e.b()
            o.bn.d r0 = r0.d()
            goto L28
        L1d:
            o.bn.e r0 = o.bn.e.b()
            o.bn.d r0 = r0.d()
            if (r0 == 0) goto L77
        L27:
            goto L2e
        L28:
            r3 = 93
            int r3 = r3 / r2
            if (r0 == 0) goto L77
            goto L27
        L2e:
            o.eg.b r0 = r7.h
            int r3 = android.view.ViewConfiguration.getKeyRepeatDelay()
            int r3 = r3 >> 16
            int r3 = 31810 - r3
            char r3 = (char) r3
            int r4 = android.view.ViewConfiguration.getWindowTouchSlop()
            int r4 = r4 >> 8
            int r4 = r4 + 2070
            int r5 = android.view.ViewConfiguration.getMinimumFlingVelocity()
            int r5 = r5 >> 16
            int r5 = r5 + 11
            java.lang.Object[] r1 = new java.lang.Object[r1]
            y(r3, r4, r5, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            android.content.Context r3 = r7.c
            java.lang.String r3 = o.bn.d.d(r3)
            r0.d(r1, r3)
            int r0 = o.cf.i.p
            int r0 = r0 + 117
            int r1 = r0 % 128
            o.cf.i.s = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L6c
            goto L6e
        L6c:
            r2 = 49
        L6e:
            switch(r2) {
                case 49: goto L72;
                default: goto L71;
            }
        L71:
            goto L73
        L72:
            return
        L73:
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L75
        L75:
            r0 = move-exception
            throw r0
        L77:
            o.ee.g.c()
            long r3 = android.widget.ExpandableListView.getPackedPositionForGroup(r2)
            r5 = 0
            int r0 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            char r0 = (char) r0
            int r3 = android.view.ViewConfiguration.getEdgeSlop()
            int r3 = r3 >> 16
            int r4 = android.view.View.MeasureSpec.makeMeasureSpec(r2, r2)
            int r4 = 13 - r4
            java.lang.Object[] r5 = new java.lang.Object[r1]
            y(r0, r3, r4, r5)
            r0 = r5[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r3 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r3 = (byte) r3
            int r3 = r3 + r1
            char r3 = (char) r3
            int r4 = android.view.ViewConfiguration.getKeyRepeatDelay()
            int r4 = r4 >> 16
            int r4 = 757 - r4
            java.lang.String r5 = ""
            int r5 = android.view.MotionEvent.axisFromString(r5)
            int r5 = 58 - r5
            java.lang.Object[] r1 = new java.lang.Object[r1]
            y(r3, r4, r5, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            o.bn.c r0 = new o.bn.c
            o.bn.c$e r1 = o.bn.c.e.c
            r0.<init>(r1)
            throw r0
        Lcb:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.o():void");
    }

    protected final void k() throws o.eg.d {
        int i = s + 43;
        p = i % 128;
        switch (i % 2 == 0) {
            case false:
                switch (this.i != null) {
                    case true:
                        o.eg.b bVar = this.h;
                        Object[] objArr = new Object[1];
                        y((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 31839), 2081 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), ((Process.getThreadPriority(0) + 20) >> 6) + 20, objArr);
                        bVar.d(((String) objArr[0]).intern(), this.i);
                        int i2 = s + Opcodes.LSHL;
                        p = i2 % 128;
                        switch (i2 % 2 == 0 ? 'J' : (char) 27) {
                            case 'J':
                                throw null;
                            default:
                                return;
                        }
                    default:
                        o.eg.b bVar2 = this.h;
                        Object[] objArr2 = new Object[1];
                        y((char) (31840 - (ViewConfiguration.getTouchSlop() >> 8)), 2081 - TextUtils.getOffsetAfter("", 0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 20, objArr2);
                        bVar2.d(((String) objArr2[0]).intern(), o.eg.b.b);
                        return;
                }
            default:
                throw null;
        }
    }

    protected final void t() throws o.eg.d {
        int i = s + 57;
        p = i % 128;
        int i2 = i % 2;
        switch (this.k != null ? '3' : 'U') {
            case Opcodes.CASTORE /* 85 */:
                break;
            default:
                o.eg.b bVar = this.h;
                Object[] objArr = new Object[1];
                y((char) (KeyEvent.normalizeMetaState(0) + 56374), Color.blue(0) + 2101, 4 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
                bVar.c(((String) objArr[0]).intern(), this.k.getLatitude());
                o.eg.b bVar2 = this.h;
                Object[] objArr2 = new Object[1];
                y((char) (View.resolveSize(0, 0) + 47976), 2103 - TextUtils.indexOf((CharSequence) "", '0'), 2 - MotionEvent.axisFromString(""), objArr2);
                bVar2.c(((String) objArr2[0]).intern(), this.k.getLongitude());
                int i3 = p + 93;
                s = i3 % 128;
                if (i3 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:30:0x02c7  */
    /* JADX WARN: Removed duplicated region for block: B:32:0x02cf  */
    /* JADX WARN: Removed duplicated region for block: B:43:0x02f5 A[FALL_THROUGH, RETURN] */
    /* JADX WARN: Removed duplicated region for block: B:44:0x02ca  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public boolean a() {
        /*
            Method dump skipped, instructions count: 782
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.a():boolean");
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x02a4, code lost:
    
        if (r8 < 300) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x02ab, code lost:
    
        r0 = r3.getInputStream();
        r8 = new byte[1024];
        r9 = new java.lang.StringBuilder();
        r10 = new java.io.BufferedInputStream(r0);
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x02bd, code lost:
    
        r0 = r10.read(r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x02c2, code lost:
    
        if (r0 == (-1)) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x02c4, code lost:
    
        r9.append(new java.lang.String(r8, 0, r0, o.ee.j.c()));
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x02d1, code lost:
    
        r10.close();
        r0 = r9.toString();
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x02d8, code lost:
    
        r2 = o.cf.i.p + 95;
        o.cf.i.s = r2 % 128;
        r2 = r2 % 2;
        r3.disconnect();
        r2 = o.cf.i.s + 83;
        o.cf.i.p = r2 % 128;
        r2 = r2 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x02f8, code lost:
    
        return new o.cf.i.c(c(r0), false);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x02a9, code lost:
    
        if (r8 < 7104) goto L22;
     */
    /* JADX WARN: Finally extract failed */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public o.cf.i.c c_() throws o.cf.c, o.bi.c {
        /*
            Method dump skipped, instructions count: 930
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.c_():o.cf.i$c");
    }

    public final void c(String str, o.eg.b bVar) throws o.eg.d {
        int i = s + 89;
        p = i % 128;
        switch (i % 2 == 0 ? '[' : (char) 22) {
            case Opcodes.DUP_X2 /* 91 */:
                this.f = str;
                this.h.d(str, bVar);
                throw null;
            default:
                this.f = str;
                this.h.d(str, bVar);
                int i2 = p + 27;
                s = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    public final c p() throws o.bt.d, o.bn.c, o.cf.c, o.bi.c, o.bp.d {
        Object[] objArr = new Object[1];
        y((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 13 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        try {
            b();
            int i = s + Opcodes.LSHL;
            p = i % 128;
            switch (i % 2 == 0) {
                case false:
                    if (!a()) {
                        if (this.a) {
                            o.ee.g.c();
                            Object[] objArr2 = new Object[1];
                            y((char) (19986 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), 2820 - ExpandableListView.getPackedPositionChild(0L), 35 - ExpandableListView.getPackedPositionChild(0L), objArr2);
                            o.ee.g.d(intern, ((String) objArr2[0]).intern());
                            return new c(c(this.g), true);
                        }
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        y((char) (ViewConfiguration.getTapTimeout() >> 16), (ViewConfiguration.getFadingEdgeLength() >> 16) + 2857, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 49, objArr3);
                        o.ee.g.d(intern, ((String) objArr3[0]).intern());
                        throw new o.cf.c(a.c);
                    }
                    if (!w()) {
                        o.ee.g.c();
                        Object[] objArr4 = new Object[1];
                        y((char) (Gravity.getAbsoluteGravity(0, 0) + 21191), 2906 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), 55 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr4);
                        o.ee.g.d(intern, ((String) objArr4[0]).intern());
                        throw new o.cf.c(a.b);
                    }
                    o.ee.e.a();
                    if (o.ee.c.g(this.c)) {
                        c c_ = c_();
                        int i2 = s + Opcodes.LMUL;
                        p = i2 % 128;
                        int i3 = i2 % 2;
                        return c_;
                    }
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    y((char) (ViewConfiguration.getFadingEdgeLength() >> 16), Color.green(0) + 2961, 48 - TextUtils.lastIndexOf("", '0', 0), objArr5);
                    o.ee.g.d(intern, ((String) objArr5[0]).intern());
                    a aVar = a.a;
                    Object[] objArr6 = new Object[1];
                    y((char) ((-16770261) - Color.rgb(0, 0, 0)), 3010 - (ViewConfiguration.getLongPressTimeout() >> 16), 34 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr6);
                    throw new o.cf.c(aVar, ((String) objArr6[0]).intern());
                default:
                    a();
                    throw null;
            }
        } catch (o.eg.d e) {
            o.ee.g.c();
            Object[] objArr7 = new Object[1];
            y((char) ExpandableListView.getPackedPositionGroup(0L), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 2705, 66 - TextUtils.lastIndexOf("", '0', 0), objArr7);
            o.ee.g.d(intern, ((String) objArr7[0]).intern());
            a aVar2 = a.e;
            Object[] objArr8 = new Object[1];
            y((char) View.getDefaultSize(0, 0), ((Process.getThreadPriority(0) + 20) >> 6) + 2771, Color.rgb(0, 0, 0) + 16777266, objArr8);
            throw new o.cf.c(aVar2, ((String) objArr8[0]).intern());
        }
    }

    private boolean w() {
        int i = s + 95;
        p = i % 128;
        int i2 = i % 2;
        if (this.g != null) {
            try {
                o.eg.b bVar = new o.eg.b(this.g);
                switch (this.e != null ? (char) 31 : '\n') {
                    case 31:
                        int i3 = s + 33;
                        p = i3 % 128;
                        int i4 = i3 % 2;
                        Object[] objArr = new Object[1];
                        y((char) (View.combineMeasuredStates(0, 0) + 27413), 3043 - (ViewConfiguration.getJumpTapTimeout() >> 16), 13 - TextUtils.getOffsetAfter("", 0), objArr);
                        bVar.d(((String) objArr[0]).intern(), this.e);
                        break;
                }
                o.ee.g.c();
                Object[] objArr2 = new Object[1];
                y((char) KeyEvent.normalizeMetaState(0), ViewConfiguration.getJumpTapTimeout() >> 16, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 12, objArr2);
                String intern = ((String) objArr2[0]).intern();
                Object[] objArr3 = new Object[1];
                y((char) TextUtils.getCapsMode("", 0, 0), Drawable.resolveOpacity(0, 0) + 3056, 40 - TextUtils.lastIndexOf("", '0', 0, 0), objArr3);
                o.ee.g.d(intern, ((String) objArr3[0]).intern());
                byte[] bytes = c().getBytes(o.ee.j.c());
                byte[] bArr = new byte[bytes.length + 1];
                bArr[0] = 1;
                System.arraycopy(bytes, 0, bArr, 1, bytes.length);
                String encodeToString = Base64.encodeToString(bArr, 10);
                o.ee.g.c();
                Object[] objArr4 = new Object[1];
                y((char) KeyEvent.normalizeMetaState(0), ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0), 13 - TextUtils.indexOf("", "", 0, 0), objArr4);
                String intern2 = ((String) objArr4[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr5 = new Object[1];
                y((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 3097 - Gravity.getAbsoluteGravity(0, 0), 45 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr5);
                o.ee.g.d(intern2, sb.append(((String) objArr5[0]).intern()).append(encodeToString).toString());
                Object[] objArr6 = new Object[1];
                y((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 3143 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), View.getDefaultSize(0, 0) + 1, objArr6);
                bVar.d(((String) objArr6[0]).intern(), encodeToString);
                Object[] objArr7 = new Object[1];
                y((char) (36481 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 492, 2 - View.MeasureSpec.getMode(0), objArr7);
                String intern3 = ((String) objArr7[0]).intern();
                Object[] objArr8 = new Object[1];
                y((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 3142, (ViewConfiguration.getPressedStateDuration() >> 16) + 7, objArr8);
                bVar.d(intern3, ((String) objArr8[0]).intern());
                Object[] objArr9 = new Object[1];
                y((char) (38295 - Color.alpha(0)), 3150 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 10, objArr9);
                bVar.d(((String) objArr9[0]).intern(), this.c.getPackageName());
                this.g = bVar.b();
                int i5 = s + 91;
                p = i5 % 128;
                switch (i5 % 2 == 0) {
                    case false:
                        return true;
                    default:
                        throw null;
                }
            } catch (o.eg.d e) {
                o.ee.g.c();
                Object[] objArr10 = new Object[1];
                y((char) (AndroidCharacter.getMirror('0') - '0'), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 13, objArr10);
                String intern4 = ((String) objArr10[0]).intern();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr11 = new Object[1];
                y((char) TextUtils.indexOf("", ""), TextUtils.indexOf((CharSequence) "", '0') + 3162, 39 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr11);
                o.ee.g.d(intern4, sb2.append(((String) objArr11[0]).intern()).append(e).toString());
            }
        }
        return false;
    }

    private static o.eg.b c(String str) throws o.cf.c {
        int i = s + 53;
        p = i % 128;
        int i2 = i % 2;
        switch (str != null ? 'H' : (char) 0) {
            case 0:
                break;
            default:
                if (!str.isEmpty()) {
                    Object[] objArr = new Object[1];
                    y((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), (ViewConfiguration.getEdgeSlop() >> 16) + 3200, 2 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
                    if (((String) objArr[0]).intern().equals(str)) {
                        a aVar = a.f47o;
                        Object[] objArr2 = new Object[1];
                        y((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0) + 3203, 19 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr2);
                        throw new o.cf.c(aVar, ((String) objArr2[0]).intern());
                    }
                    try {
                        o.eg.b bVar = new o.eg.b(str);
                        int i3 = p + 81;
                        s = i3 % 128;
                        int i4 = i3 % 2;
                        return bVar;
                    } catch (o.eg.d e) {
                        throw new o.cf.c(a.g, e.getMessage());
                    }
                }
                break;
        }
        throw new o.cf.c(a.f);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cf\i$c.smali */
    public static final class c {
        private final boolean c;
        private final o.eg.b e;
        private static int d = 0;
        private static int b = 1;

        public c(o.eg.b bVar, boolean z) {
            this.e = bVar;
            this.c = z;
        }

        public final o.eg.b e() {
            int i = d;
            int i2 = (i & 23) + (i | 23);
            int i3 = i2 % 128;
            b = i3;
            int i4 = i2 % 2;
            o.eg.b bVar = this.e;
            int i5 = (i3 ^ Opcodes.LREM) + ((i3 & Opcodes.LREM) << 1);
            d = i5 % 128;
            switch (i5 % 2 != 0 ? '8' : ']') {
                case '8':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return bVar;
            }
        }

        public final boolean c() {
            int i = (d + 82) - 1;
            b = i % 128;
            switch (i % 2 == 0) {
                case false:
                    return this.c;
                default:
                    throw null;
            }
        }
    }

    public final int hashCode() {
        int i = p + 85;
        s = i % 128;
        int i2 = i % 2;
        int hashCode = super.hashCode();
        int i3 = p + 23;
        s = i3 % 128;
        switch (i3 % 2 != 0 ? 'c' : (char) 31) {
            case 31:
                return hashCode;
            default:
                int i4 = 30 / 0;
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = p + 11;
        s = i % 128;
        char c2 = i % 2 != 0 ? (char) 26 : '5';
        boolean equals = super.equals(obj);
        switch (c2) {
            case 26:
                int i2 = 52 / 0;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i = s + 5;
        p = i % 128;
        switch (i % 2 != 0) {
            case true:
                return super.toString();
            default:
                super.toString();
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void y(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1002
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cf.i.y(char, int, int, java.lang.Object[]):void");
    }
}
