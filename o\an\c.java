package o.an;

import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Base64;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;
import o.ep.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\c.smali */
public final class c<T extends o.ep.c> implements o.an.a<a, e> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static char[] d;
    private static char e;
    private static int g;
    private final T c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        g = 1;
        c();
        int i = a + 25;
        g = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    static void c() {
        b = new char[]{50760, 51154, 51198, 51170, 51168, 51169, 51155, 51153, 51196, 51175, 51176, 51196, 51169, 51168, 51177, 51154, 51170, 51194, 51196, 51197, 51194, 51192, 51193, 50838, 50801, 50699, 50805, 50811, 50803, 50807, 50803, 50805, 50803, 50811, 50693, 50813, 50802, 50691, 50809, 50806, 50811, 50803, 50803, 50933, 50850, 50851, 50877, 50849, 50856, 50856, 50849, 50854, 50856, 50832, 50838, 50849, 50782, 51179, 51168, 51169, 51179, 51155, 51153, 51168, 51199, 51197, 51170, 51196, 51168, 51150, 50730, 50730, 51145, 51169, 51197, 51169, 51175, 51172, 51158, 51156, 51171, 51176, 51181, 51175, 51151, 50723, 50723};
        d = new char[]{30559, 30589, 30585, 30569, 30590, 30561, 30564, 30540, 30563, 30566, 30567, 30529, 30517, 30568, 30552, 30588, 30572, 30582, 30574, 30571, 30534, 30560, 30511, 30570, 30591, 30587, 30586, 30558, 30528, 30557, 30498, 30553, 30539, 30554, 30584, 30555};
        e = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.an.c.$$a
            int r8 = r8 + 66
            int r7 = r7 * 3
            int r7 = r7 + 4
            int r6 = r6 * 3
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r3 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = r6 + r9
            int r8 = r8 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.c.i(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{38, 115, -18, 59};
        $$b = 86;
    }

    @Override // o.an.a
    public final /* synthetic */ e a(o.eg.b bVar) throws o.eg.d {
        int i = a + 95;
        g = i % 128;
        switch (i % 2 == 0 ? 'Y' : 'B') {
            case Opcodes.DUP /* 89 */:
                d(bVar);
                throw null;
            default:
                return d(bVar);
        }
    }

    public c(T t) {
        this.c = t;
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x00a0, code lost:
    
        if (r8 != null) goto L21;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x00a8, code lost:
    
        r7 = new o.an.c.a(r1, r8);
        r11 = new java.lang.Object[1];
        f("\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{23, 10, 82, 0}, true, r11);
        r1 = ((java.lang.String) r11[0]).intern();
        r9 = new java.lang.Object[1];
        f(null, new int[]{33, 10, com.esotericsoftware.asm.Opcodes.DDIV, 1}, true, r9);
        r13.d(r1, ((java.lang.String) r9[0]).intern());
        r8 = new java.lang.Object[1];
        h(23 - android.text.TextUtils.getTrimmedLength(""), "\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\u000e#\u0014\u0005\n\u000f\u0014\u0011\u0014\u0006\u000b\u0014\u001a\u0013㙕", (byte) (88 - (android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), r8);
        r13.d(((java.lang.String) r8[0]).intern(), r7.c());
        r4 = new java.lang.Object[1];
        h(22 - android.view.MotionEvent.axisFromString(""), "\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\r\u000b\t\n\u001d\u000b\u001a\u001f\u0014\u0005\n\u000f\u0012\u0015㙻", (byte) ((android.view.ViewConfiguration.getMaximumFlingVelocity() >> 16) + com.esotericsoftware.asm.Opcodes.LUSHR), r4);
        r13.d(((java.lang.String) r4[0]).intern(), r7.b());
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0124, code lost:
    
        r13 = o.an.c.g + 35;
        o.an.c.a = r13 % 128;
        r13 = r13 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x012d, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00a6, code lost:
    
        if (r8 != null) goto L21;
     */
    @Override // o.an.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void b(o.eg.b r13) throws o.eg.d, o.an.a.C0022a {
        /*
            Method dump skipped, instructions count: 356
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.c.b(o.eg.b):void");
    }

    private static e d(o.eg.b bVar) throws o.eg.d {
        g.c();
        Object[] objArr = new Object[1];
        f("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{0, 23, 196, 0}, false, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000", new int[]{43, 13, 0, 5}, false, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        h(ImageFormat.getBitsPerPixel(0) + 13, "\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\u0010\u001d\u001c\f", (byte) (76 - TextUtils.indexOf("", "", 0)), objArr3);
        byte[] decode = Base64.decode(bVar.r(((String) objArr3[0]).intern()), 10);
        Object[] objArr4 = new Object[1];
        h(TextUtils.indexOf("", "", 0, 0) + 20, "\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\r\u000b\u0013\u0000\u0017\u0007\u0013\u001d!\u0016\u0000\u0007", (byte) (12 - View.getDefaultSize(0, 0)), objArr4);
        String r = bVar.r(((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        h(23 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), "\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\u0017\u0005\u0012\t\u001d\u000b\u0001\u0002\u0014\u0003\u0007\u0015\u0013\u0005", (byte) (126 - ExpandableListView.getPackedPositionType(0L)), objArr5);
        String r2 = bVar.r(((String) objArr5[0]).intern());
        g.c();
        Object[] objArr6 = new Object[1];
        f("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{0, 23, 196, 0}, false, objArr6);
        String intern2 = ((String) objArr6[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr7 = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000", new int[]{56, 31, Opcodes.INSTANCEOF, 31}, false, objArr7);
        g.d(intern2, sb.append(((String) objArr7[0]).intern()).append(decode).toString());
        Object[] objArr8 = new Object[1];
        f("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{0, 23, 196, 0}, false, objArr8);
        String intern3 = ((String) objArr8[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr9 = new Object[1];
        h((ViewConfiguration.getPressedStateDuration() >> 16) + 39, "\u0005\u0015\u001c\r\u000b\u001c\u0015\u0011\u001b\u0012\u0003\u0011\u0012\u0017\"\u0012\u000f\u0013\u0013\u000f\u000b\u0014\u0006\u0018\r\u000b\u0013\u0000\u0017\u0007\u0013\u001d!\u0016\u0000\u0007\u0012\u0010㗘", (byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 29), objArr9);
        g.d(intern3, sb2.append(((String) objArr9[0]).intern()).append(r).toString());
        Object[] objArr10 = new Object[1];
        f("\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0000\u0000\u0000\u0000\u0001\u0001\u0001", new int[]{0, 23, 196, 0}, false, objArr10);
        String intern4 = ((String) objArr10[0]).intern();
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr11 = new Object[1];
        h(Gravity.getAbsoluteGravity(0, 0) + 32, "\u0005\u0015\u001c\r\u000b\u001c\u0015\u0011\u001b\u0012\u0003\u0011\u0012\u0017\"\u0012\u001b\u0013\u000b\u0012\u0000\u0001\u0003\u0013\u0003\b\u0014\u0012\u0004\u0013\u0010\u0012", (byte) (51 - TextUtils.lastIndexOf("", '0', 0)), objArr11);
        g.d(intern4, sb3.append(((String) objArr11[0]).intern()).append(r2).toString());
        e eVar = new e(decode, r, r2);
        int i = g + 53;
        a = i % 128;
        int i2 = i % 2;
        return eVar;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\c$a.smali */
    public static final class a {
        private final String c;
        private final String d;
        private static int e = 0;
        private static int b = 1;

        public a(String str, String str2) {
            this.d = str;
            this.c = str2;
        }

        public final String c() {
            int i = e + 51;
            b = i % 128;
            switch (i % 2 == 0) {
                case true:
                    throw null;
                default:
                    return this.d;
            }
        }

        public final String b() {
            String str;
            int i = e;
            int i2 = ((i | Opcodes.LNEG) << 1) - (i ^ Opcodes.LNEG);
            int i3 = i2 % 128;
            b = i3;
            switch (i2 % 2 != 0) {
                case false:
                    str = this.c;
                    int i4 = 10 / 0;
                    break;
                default:
                    str = this.c;
                    break;
            }
            int i5 = (i3 + 60) - 1;
            e = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\an\c$e.smali */
    public static final class e {
        private static int a = 0;
        private static int b = 1;
        private final String c;
        private final byte[] d;
        private final String e;

        public e(byte[] bArr, String str, String str2) {
            this.d = bArr;
            this.e = str;
            this.c = str2;
        }

        public final byte[] e() {
            int i = a;
            int i2 = ((i | 45) << 1) - (i ^ 45);
            int i3 = i2 % 128;
            b = i3;
            int i4 = i2 % 2;
            byte[] bArr = this.d;
            int i5 = (i3 & 57) + (i3 | 57);
            a = i5 % 128;
            switch (i5 % 2 != 0 ? 'H' : '^') {
                case 'H':
                    int i6 = 39 / 0;
                    return bArr;
                default:
                    return bArr;
            }
        }

        public final String c() {
            int i = a;
            int i2 = ((i | Opcodes.DDIV) << 1) - (i ^ Opcodes.DDIV);
            int i3 = i2 % 128;
            b = i3;
            int i4 = i2 % 2;
            String str = this.e;
            int i5 = ((i3 | 65) << 1) - (i3 ^ 65);
            a = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }

        public final String a() {
            int i = b;
            int i2 = (i & 85) + (i | 85);
            int i3 = i2 % 128;
            a = i3;
            int i4 = i2 % 2;
            String str = this.c;
            int i5 = (i3 + 96) - 1;
            b = i5 % 128;
            int i6 = i5 % 2;
            return str;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:96:0x02d9, code lost:
    
        r1 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r18, int[] r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 830
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.c.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x004b, code lost:
    
        r13 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x004c, code lost:
    
        if (r13 >= r11) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x004e, code lost:
    
        r14 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0051, code lost:
    
        switch(r14) {
            case 1: goto L113;
            default: goto L27;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x005a, code lost:
    
        r15 = new java.lang.Object[]{java.lang.Integer.valueOf(r5[r13])};
        r1 = o.e.a.s.get(java.lang.Integer.valueOf(r8));
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x006c, code lost:
    
        if (r1 == null) goto L32;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00b9, code lost:
    
        r12[r13] = ((java.lang.Character) ((java.lang.reflect.Method) r1).invoke(null, r15)).charValue();
        r13 = r13 + 1;
        r6 = '0';
        r8 = -1401577988;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x006f, code lost:
    
        r1 = (java.lang.Class) o.e.a.c(17 - android.text.TextUtils.indexOf("", "", 0), (char) (android.text.TextUtils.indexOf("", r6, 0) + 1), 76 - android.view.KeyEvent.keyCodeFromString(""));
        r6 = (byte) 0;
        r14 = r6;
        r8 = new java.lang.Object[1];
        i(r6, r14, (byte) (r14 | 7), r8);
        r1 = r1.getMethod((java.lang.String) r8[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1401577988, r1);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00c4, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00c5, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00c9, code lost:
    
        if (r1 != null) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00cb, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x00cc, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x0057, code lost:
    
        r5 = r12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0050, code lost:
    
        r14 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1020
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.an.c.h(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
