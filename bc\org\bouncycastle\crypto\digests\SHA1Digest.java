package bc.org.bouncycastle.crypto.digests;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.m5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.p1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import org.bouncycastle.pqc.jcajce.spec.McElieceCCA2KeyGenParameterSpec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\digests\SHA1Digest.smali */
public class SHA1Digest extends a {
    private int e;
    private int f;
    private int g;
    private int h;
    private int i;
    private int[] j;
    private int k;

    public SHA1Digest() {
        this(q1.ANY);
    }

    private int a(int i, int i2, int i3) {
        return ((~i) & i3) | (i2 & i);
    }

    private void a(SHA1Digest sHA1Digest) {
        this.e = sHA1Digest.e;
        this.f = sHA1Digest.f;
        this.g = sHA1Digest.g;
        this.h = sHA1Digest.h;
        this.i = sHA1Digest.i;
        int[] iArr = sHA1Digest.j;
        System.arraycopy(iArr, 0, this.j, 0, iArr.length);
        this.k = sHA1Digest.k;
    }

    private int b(int i, int i2, int i3) {
        return (i & i3) | (i & i2) | (i2 & i3);
    }

    private int c(int i, int i2, int i3) {
        return (i ^ i2) ^ i3;
    }

    protected p1 b() {
        return g.a(this, 128, this.a);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public m5 copy() {
        return new SHA1Digest(this);
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int doFinal(byte[] bArr, int i) {
        finish();
        j6.a(this.e, bArr, i);
        j6.a(this.f, bArr, i + 4);
        j6.a(this.g, bArr, i + 8);
        j6.a(this.h, bArr, i + 12);
        j6.a(this.i, bArr, i + 16);
        reset();
        return 20;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public String getAlgorithmName() {
        return McElieceCCA2KeyGenParameterSpec.SHA1;
    }

    @Override // bc.org.bouncycastle.crypto.Digest
    public int getDigestSize() {
        return 20;
    }

    public byte[] getEncodedState() {
        int i = (this.k * 4) + 40 + 1;
        byte[] bArr = new byte[i];
        super.a(bArr);
        j6.a(this.e, bArr, 16);
        j6.a(this.f, bArr, 20);
        j6.a(this.g, bArr, 24);
        j6.a(this.h, bArr, 28);
        j6.a(this.i, bArr, 32);
        j6.a(this.k, bArr, 36);
        for (int i2 = 0; i2 != this.k; i2++) {
            j6.a(this.j[i2], bArr, (i2 * 4) + 40);
        }
        bArr[i - 1] = (byte) this.a.ordinal();
        return bArr;
    }

    @Override // bc.org.bouncycastle.crypto.digests.a, bc.org.bouncycastle.crypto.Digest
    public void reset() {
        super.reset();
        this.e = 1732584193;
        this.f = -271733879;
        this.g = -1732584194;
        this.h = 271733878;
        this.i = -1009589776;
        this.k = 0;
        int i = 0;
        while (true) {
            int[] iArr = this.j;
            if (i == iArr.length) {
                return;
            }
            iArr[i] = 0;
            i++;
        }
    }

    public SHA1Digest(q1 q1Var) {
        super(q1Var);
        this.j = new int[80];
        t1.a(b());
        reset();
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(byte[] bArr, int i) {
        this.j[this.k] = j6.a(bArr, i);
        int i2 = this.k + 1;
        this.k = i2;
        if (i2 == 16) {
            a();
        }
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.m5
    public void reset(m5 m5Var) {
        SHA1Digest sHA1Digest = (SHA1Digest) m5Var;
        super.a((a) sHA1Digest);
        a(sHA1Digest);
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a(long j) {
        if (this.k > 14) {
            a();
        }
        int[] iArr = this.j;
        iArr[14] = (int) (j >>> 32);
        iArr[15] = (int) j;
    }

    @Override // bc.org.bouncycastle.crypto.digests.a
    protected void a() {
        for (int i = 16; i < 80; i++) {
            int[] iArr = this.j;
            int i2 = ((iArr[i - 3] ^ iArr[i - 8]) ^ iArr[i - 14]) ^ iArr[i - 16];
            iArr[i] = (i2 >>> 31) | (i2 << 1);
        }
        int i3 = this.e;
        int i4 = this.f;
        int i5 = this.g;
        int i6 = this.h;
        int i7 = this.i;
        int i8 = 0;
        int i9 = 0;
        while (i8 < 4) {
            int i10 = i9 + 1;
            int a = i7 + ((i3 << 5) | (i3 >>> 27)) + a(i4, i5, i6) + this.j[i9] + 1518500249;
            int i11 = (i4 >>> 2) | (i4 << 30);
            int i12 = i10 + 1;
            int a2 = i6 + ((a << 5) | (a >>> 27)) + a(i3, i11, i5) + this.j[i10] + 1518500249;
            int i13 = (i3 >>> 2) | (i3 << 30);
            int i14 = i12 + 1;
            int a3 = i5 + ((a2 << 5) | (a2 >>> 27)) + a(a, i13, i11) + this.j[i12] + 1518500249;
            i7 = (a >>> 2) | (a << 30);
            int i15 = i14 + 1;
            i4 = i11 + ((a3 << 5) | (a3 >>> 27)) + a(a2, i7, i13) + this.j[i14] + 1518500249;
            i6 = (a2 >>> 2) | (a2 << 30);
            i3 = i13 + ((i4 << 5) | (i4 >>> 27)) + a(a3, i6, i7) + this.j[i15] + 1518500249;
            i5 = (a3 >>> 2) | (a3 << 30);
            i8++;
            i9 = i15 + 1;
        }
        int i16 = 0;
        while (i16 < 4) {
            int i17 = i9 + 1;
            int c = i7 + ((i3 << 5) | (i3 >>> 27)) + c(i4, i5, i6) + this.j[i9] + 1859775393;
            int i18 = (i4 >>> 2) | (i4 << 30);
            int i19 = i17 + 1;
            int c2 = i6 + ((c << 5) | (c >>> 27)) + c(i3, i18, i5) + this.j[i17] + 1859775393;
            int i20 = (i3 >>> 2) | (i3 << 30);
            int i21 = i19 + 1;
            int c3 = i5 + ((c2 << 5) | (c2 >>> 27)) + c(c, i20, i18) + this.j[i19] + 1859775393;
            i7 = (c >>> 2) | (c << 30);
            int i22 = i21 + 1;
            i4 = i18 + ((c3 << 5) | (c3 >>> 27)) + c(c2, i7, i20) + this.j[i21] + 1859775393;
            i6 = (c2 >>> 2) | (c2 << 30);
            i3 = i20 + ((i4 << 5) | (i4 >>> 27)) + c(c3, i6, i7) + this.j[i22] + 1859775393;
            i5 = (c3 >>> 2) | (c3 << 30);
            i16++;
            i9 = i22 + 1;
        }
        int i23 = 0;
        while (i23 < 4) {
            int b = i7 + (((((i3 << 5) | (i3 >>> 27)) + b(i4, i5, i6)) + this.j[i9]) - 1894007588);
            int b2 = i6 + (((((b << 5) | (b >>> 27)) + b(i3, r2, i5)) + this.j[r12]) - 1894007588);
            int b3 = i5 + (((((b2 << 5) | (b2 >>> 27)) + b(b, r1, r2)) + this.j[r13]) - 1894007588);
            i7 = (b >>> 2) | (b << 30);
            i4 = ((i4 >>> 2) | (i4 << 30)) + (((((b3 << 5) | (b3 >>> 27)) + b(b2, i7, r1)) + this.j[r12]) - 1894007588);
            i6 = (b2 >>> 2) | (b2 << 30);
            i3 = ((i3 >>> 2) | (i3 << 30)) + (((((i4 << 5) | (i4 >>> 27)) + b(b3, i6, i7)) + this.j[r13]) - 1894007588);
            i5 = (b3 >>> 2) | (b3 << 30);
            i23++;
            i9 = i9 + 1 + 1 + 1 + 1 + 1;
        }
        int i24 = 0;
        while (i24 <= 3) {
            int c4 = i7 + (((((i3 << 5) | (i3 >>> 27)) + c(i4, i5, i6)) + this.j[i9]) - 899497514);
            int c5 = i6 + (((((c4 << 5) | (c4 >>> 27)) + c(i3, r2, i5)) + this.j[r11]) - 899497514);
            int c6 = i5 + (((((c5 << 5) | (c5 >>> 27)) + c(c4, r1, r2)) + this.j[r12]) - 899497514);
            i7 = (c4 >>> 2) | (c4 << 30);
            i4 = ((i4 >>> 2) | (i4 << 30)) + (((((c6 << 5) | (c6 >>> 27)) + c(c5, i7, r1)) + this.j[r11]) - 899497514);
            i6 = (c5 >>> 2) | (c5 << 30);
            i3 = ((i3 >>> 2) | (i3 << 30)) + (((((i4 << 5) | (i4 >>> 27)) + c(c6, i6, i7)) + this.j[r12]) - 899497514);
            i5 = (c6 >>> 2) | (c6 << 30);
            i24++;
            i9 = i9 + 1 + 1 + 1 + 1 + 1;
        }
        this.e += i3;
        this.f += i4;
        this.g += i5;
        this.h += i6;
        this.i += i7;
        this.k = 0;
        for (int i25 = 0; i25 < 16; i25++) {
            this.j[i25] = 0;
        }
    }

    public SHA1Digest(SHA1Digest sHA1Digest) {
        super(sHA1Digest);
        this.j = new int[80];
        t1.a(b());
        a(sHA1Digest);
    }

    public SHA1Digest(byte[] bArr) {
        super(bArr);
        this.j = new int[80];
        t1.a(b());
        this.e = j6.a(bArr, 16);
        this.f = j6.a(bArr, 20);
        this.g = j6.a(bArr, 24);
        this.h = j6.a(bArr, 28);
        this.i = j6.a(bArr, 32);
        this.k = j6.a(bArr, 36);
        for (int i = 0; i != this.k; i++) {
            this.j[i] = j6.a(bArr, (i * 4) + 40);
        }
    }
}
