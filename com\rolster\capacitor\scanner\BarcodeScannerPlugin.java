package com.rolster.capacitor.scanner;

import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.activity.result.ActivityResult;
import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.PermissionState;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;
import com.getcapacitor.annotation.PermissionCallback;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.ResultPoint;
import com.journeyapps.barcodescanner.BarcodeCallback;
import com.journeyapps.barcodescanner.BarcodeResult;
import com.journeyapps.barcodescanner.BarcodeView;
import com.journeyapps.barcodescanner.DefaultDecoderFactory;
import com.journeyapps.barcodescanner.camera.CameraSettings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONException;

@CapacitorPlugin(name = "BarcodeScanner", permissions = {@Permission(alias = BarcodeScannerPlugin.OPEN_CAMERA, strings = {BarcodeScannerPlugin.PERMISSION_NAME})})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes10\com\rolster\capacitor\scanner\BarcodeScannerPlugin.smali */
public class BarcodeScannerPlugin extends Plugin implements BarcodeCallback {
    private static final String ASKED = "asked";
    private static final String DENIED = "denied";
    private static final String GRANTED = "granted";
    private static final String NEVER_ASKED = "neverAsked";
    public static final String OPEN_CAMERA = "openCamera";
    private static final String PERMISSION_NAME = "android.permission.CAMERA";
    private static final String PREFS_PERMISSION_FIRST_TIME_ASKING = "PREFS_PERMISSION_FIRST_TIME_ASKING";
    private static final String TAG_PERMISSION = "permission";
    private static final Map<String, BarcodeFormat> supportedFormats = supportedFormats();
    private BarcodeView mBarcodeView;
    private JSObject resultOpenCamera;
    private boolean isScanning = false;
    private boolean shouldRunScan = false;
    private boolean didRunCameraSetup = false;
    private boolean didRunCameraPrepare = false;
    private boolean isBackgroundHidden = false;
    private boolean isTorchOn = false;
    private boolean scanningPaused = false;
    private String lastScanResult = null;

    private static Map<String, BarcodeFormat> supportedFormats() {
        Map<String, BarcodeFormat> map = new HashMap<>();
        map.put("UPC_A", BarcodeFormat.UPC_A);
        map.put("UPC_E", BarcodeFormat.UPC_E);
        map.put("UPC_EAN_EXTENSION", BarcodeFormat.UPC_EAN_EXTENSION);
        map.put("EAN_8", BarcodeFormat.EAN_8);
        map.put("EAN_13", BarcodeFormat.EAN_13);
        map.put("CODE_39", BarcodeFormat.CODE_39);
        map.put("CODE_93", BarcodeFormat.CODE_93);
        map.put("CODE_128", BarcodeFormat.CODE_128);
        map.put("CODABAR", BarcodeFormat.CODABAR);
        map.put("ITF", BarcodeFormat.ITF);
        map.put("AZTEC", BarcodeFormat.AZTEC);
        map.put("DATA_MATRIX", BarcodeFormat.DATA_MATRIX);
        map.put("MAXICODE", BarcodeFormat.MAXICODE);
        map.put("PDF_417", BarcodeFormat.PDF_417);
        map.put("QR_CODE", BarcodeFormat.QR_CODE);
        map.put("RSS_14", BarcodeFormat.RSS_14);
        map.put("RSS_EXPANDED", BarcodeFormat.RSS_EXPANDED);
        return Collections.unmodifiableMap(map);
    }

    private boolean hasCamera() {
        return getActivity().getPackageManager().hasSystemFeature("android.hardware.camera.any");
    }

    private void setupCamera(final String cameraDirection) {
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda5
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$setupCamera$0(cameraDirection);
            }
        });
        this.didRunCameraSetup = true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$setupCamera$0(String str) {
        this.mBarcodeView = new BarcodeView(getActivity());
        CameraSettings cameraSettings = new CameraSettings();
        cameraSettings.setRequestedCameraId("front".equals(str) ? 1 : 0);
        cameraSettings.setContinuousFocusEnabled(true);
        this.mBarcodeView.setCameraSettings(cameraSettings);
        ((ViewGroup) this.bridge.getWebView().getParent()).addView(this.mBarcodeView, new FrameLayout.LayoutParams(-2, -2));
        this.bridge.getWebView().bringToFront();
        this.mBarcodeView.resume();
    }

    private void dismantleCamera() {
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$dismantleCamera$1();
            }
        });
        this.isScanning = false;
        this.didRunCameraSetup = false;
        this.didRunCameraPrepare = false;
        if (getSavedCall() != null && !this.shouldRunScan) {
            freeSavedCall();
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$dismantleCamera$1() {
        BarcodeView barcodeView = this.mBarcodeView;
        if (barcodeView != null) {
            barcodeView.pause();
            this.mBarcodeView.stopDecoding();
            ((ViewGroup) this.bridge.getWebView().getParent()).removeView(this.mBarcodeView);
            this.mBarcodeView = null;
        }
    }

    private void _prepare(PluginCall call) {
        dismantleCamera();
        setupCamera(call.getString("cameraDirection", "back"));
        this.didRunCameraPrepare = true;
        if (this.shouldRunScan) {
            scan();
        }
    }

    private void destroy() {
        showBackground();
        dismantleCamera();
        setTorch(false);
    }

    private void configureCamera() {
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$configureCamera$2();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$configureCamera$2() {
        PluginCall call = getSavedCall();
        if (call == null || this.mBarcodeView == null) {
            Log.d("scanner", "Something went wrong with configuring the BarcodeScanner.");
            return;
        }
        DefaultDecoderFactory defaultDecoderFactory = new DefaultDecoderFactory(null, null, null, 2);
        if (call.hasOption("targetedFormats")) {
            JSArray targetedFormats = call.getArray("targetedFormats");
            ArrayList<BarcodeFormat> formatList = new ArrayList<>();
            if (targetedFormats != null && targetedFormats.length() > 0) {
                for (int i = 0; i < targetedFormats.length(); i++) {
                    try {
                        String targetedFormat = targetedFormats.getString(i);
                        BarcodeFormat targetedBarcodeFormat = supportedFormats.get(targetedFormat);
                        if (targetedBarcodeFormat != null) {
                            formatList.add(targetedBarcodeFormat);
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
            int i2 = formatList.size();
            if (i2 <= 0) {
                Log.d("scanner", "The property targetedFormats was not set correctly.");
            } else {
                defaultDecoderFactory = new DefaultDecoderFactory(formatList, null, null, 2);
            }
        }
        this.mBarcodeView.setDecoderFactory(defaultDecoderFactory);
    }

    private void scan() {
        if (!this.didRunCameraPrepare) {
            if (hasCamera()) {
                if (!hasPermission(PERMISSION_NAME)) {
                    Log.d("scanner", "No permission to use camera. Did you request it yet?");
                    return;
                } else {
                    this.shouldRunScan = true;
                    _prepare(getSavedCall());
                    return;
                }
            }
            return;
        }
        this.didRunCameraPrepare = false;
        this.shouldRunScan = false;
        configureCamera();
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda2
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$scan$3(this);
            }
        });
        hideBackground();
        this.isScanning = true;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$scan$3(BarcodeCallback b) {
        if (this.mBarcodeView != null) {
            PluginCall call = getSavedCall();
            if (call != null && call.isKeptAlive()) {
                this.mBarcodeView.decodeContinuous(b);
            } else {
                this.mBarcodeView.decodeSingle(b);
            }
        }
    }

    private void hideBackground() {
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda4
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$hideBackground$4();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$hideBackground$4() {
        this.bridge.getWebView().setBackgroundColor(0);
        this.bridge.getWebView().loadUrl("javascript:document.documentElement.style.backgroundColor = 'transparent';void(0);");
        this.isBackgroundHidden = true;
    }

    private void showBackground() {
        getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda3
            @Override // java.lang.Runnable
            public final void run() {
                BarcodeScannerPlugin.this.lambda$showBackground$5();
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$showBackground$5() {
        this.bridge.getWebView().setBackgroundColor(-1);
        this.bridge.getWebView().loadUrl("javascript:document.documentElement.style.backgroundColor = '';void(0);");
        this.isBackgroundHidden = false;
    }

    @Override // com.journeyapps.barcodescanner.BarcodeCallback
    public void barcodeResult(BarcodeResult barcodeResult) {
        JSObject result = new JSObject();
        if (barcodeResult.getText() != null) {
            result.put("hasContent", true);
            result.put("content", barcodeResult.getText());
            result.put("format", barcodeResult.getBarcodeFormat().name());
        } else {
            result.put("hasContent", false);
        }
        PluginCall call = getSavedCall();
        if (call != null) {
            if (call.isKeptAlive()) {
                if (!this.scanningPaused && barcodeResult.getText() != null && !barcodeResult.getText().equals(this.lastScanResult)) {
                    this.lastScanResult = barcodeResult.getText();
                    call.resolve(result);
                    return;
                }
                return;
            }
            call.resolve(result);
            destroy();
            return;
        }
        destroy();
    }

    @Override // com.getcapacitor.Plugin
    public void handleOnPause() {
        BarcodeView barcodeView = this.mBarcodeView;
        if (barcodeView != null) {
            barcodeView.pause();
        }
    }

    @Override // com.getcapacitor.Plugin
    public void handleOnResume() {
        BarcodeView barcodeView = this.mBarcodeView;
        if (barcodeView != null) {
            barcodeView.resume();
        }
    }

    @Override // com.journeyapps.barcodescanner.BarcodeCallback
    public void possibleResultPoints(List<ResultPoint> resultPoints) {
    }

    @PluginMethod
    public void prepare(PluginCall call) {
        _prepare(call);
        call.resolve();
    }

    @PluginMethod
    public void hideBackground(PluginCall call) {
        hideBackground();
        call.resolve();
    }

    @PluginMethod
    public void showBackground(PluginCall call) {
        showBackground();
        call.resolve();
    }

    @PluginMethod
    public void startScan(PluginCall call) {
        saveCall(call);
        scan();
    }

    @PluginMethod
    public void stopScan(PluginCall call) {
        Boolean resolveScan;
        if (call.hasOption("resolveScan") && getSavedCall() != null && (resolveScan = call.getBoolean("resolveScan", false)) != null && resolveScan.booleanValue()) {
            JSObject result = new JSObject();
            result.put("hasContent", false);
            getSavedCall().resolve(result);
        }
        destroy();
        call.resolve();
    }

    @PluginMethod(returnType = PluginMethod.RETURN_CALLBACK)
    public void startScanning(PluginCall call) {
        call.setKeepAlive(true);
        this.lastScanResult = null;
        saveCall(call);
        this.scanningPaused = false;
        scan();
    }

    @PluginMethod
    public void pauseScanning(PluginCall call) {
        this.scanningPaused = true;
        call.resolve();
    }

    @PluginMethod
    public void resumeScanning(PluginCall call) {
        this.lastScanResult = null;
        this.scanningPaused = false;
        call.resolve();
    }

    private void setPermissionFirstTimeAsking(String permission, boolean isFirstTime) {
        SharedPreferences sharedPreference = getActivity().getSharedPreferences(PREFS_PERMISSION_FIRST_TIME_ASKING, 0);
        sharedPreference.edit().putBoolean(permission, isFirstTime).apply();
    }

    private boolean isPermissionFirstTimeAsking(String permission) {
        return getActivity().getSharedPreferences(PREFS_PERMISSION_FIRST_TIME_ASKING, 0).getBoolean(permission, true);
    }

    @PermissionCallback
    private void openCameraPermissionCallback(PluginCall call) {
        if (this.resultOpenCamera == null) {
            return;
        }
        setPermissionFirstTimeAsking(PERMISSION_NAME, false);
        boolean granted = canOpenCameraPermisionGranted();
        this.resultOpenCamera.put(ASKED, true);
        if (granted) {
            Log.d(TAG_PERMISSION, "Asked. Granted");
        } else if (getActivity().shouldShowRequestPermissionRationale(PERMISSION_NAME)) {
            Log.d(TAG_PERMISSION, "Asked. Denied For Now");
        } else {
            Log.d(TAG_PERMISSION, "Asked. Denied");
            this.resultOpenCamera.put(DENIED, true);
        }
        this.resultOpenCamera.put(GRANTED, granted);
        call.resolve(this.resultOpenCamera);
        this.resultOpenCamera = null;
    }

    @PluginMethod
    public void checkPermission(PluginCall call) {
        if (!canOpenCameraPermisionGranted()) {
            requestPermissions(call);
            return;
        }
        JSObject result = new JSObject();
        result.put(GRANTED, true);
        call.resolve(result);
    }

    private boolean canOpenCameraPermisionGranted() {
        return getPermissionState(OPEN_CAMERA) == PermissionState.GRANTED;
    }

    private void _checkPermission(PluginCall call, boolean force) {
        this.resultOpenCamera = new JSObject();
        boolean neverAsked = isPermissionFirstTimeAsking(PERMISSION_NAME);
        if (neverAsked) {
            this.resultOpenCamera.put(NEVER_ASKED, true);
        }
        if (!neverAsked && !getActivity().shouldShowRequestPermissionRationale(PERMISSION_NAME)) {
            this.resultOpenCamera.put(DENIED, true);
        } else if (force) {
            requestPermissionForAlias(OPEN_CAMERA, call, "openCameraPermissionCallback");
            return;
        }
        call.resolve(this.resultOpenCamera);
    }

    @PluginMethod
    public void openAppSettings(PluginCall call) {
        Intent intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS", Uri.fromParts("package", getAppId(), null));
        intent.addFlags(268435456);
        startActivityForResult(call, intent, "openSettingsResult");
    }

    @ActivityCallback
    private void openSettingsResult(PluginCall call, ActivityResult result) {
        call.resolve();
    }

    private void setTorch(final boolean on) {
        if (on != this.isTorchOn) {
            this.isTorchOn = on;
            getActivity().runOnUiThread(new Runnable() { // from class: com.rolster.capacitor.scanner.BarcodeScannerPlugin$$ExternalSyntheticLambda6
                @Override // java.lang.Runnable
                public final void run() {
                    BarcodeScannerPlugin.this.lambda$setTorch$6(on);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void lambda$setTorch$6(boolean on) {
        BarcodeView barcodeView = this.mBarcodeView;
        if (barcodeView != null) {
            barcodeView.setTorch(on);
        }
    }

    @PluginMethod
    public void enableTorch(PluginCall call) {
        setTorch(true);
        call.resolve();
    }

    @PluginMethod
    public void disableTorch(PluginCall call) {
        setTorch(false);
        call.resolve();
    }

    @PluginMethod
    public void toggleTorch(PluginCall call) {
        setTorch(!this.isTorchOn);
        call.resolve();
    }

    @PluginMethod
    public void getTorchState(PluginCall call) {
        JSObject result = new JSObject();
        result.put("isEnabled", this.isTorchOn);
        call.resolve(result);
    }
}
