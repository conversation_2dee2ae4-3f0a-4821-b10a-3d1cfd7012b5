package com.capacitorjs.plugins.filesystem.exceptions;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes14\com\capacitorjs\plugins\filesystem\exceptions\DirectoryExistsException.smali */
public class DirectoryExistsException extends Exception {
    public DirectoryExistsException(String s) {
        super(s);
    }

    public DirectoryExistsException(Throwable t) {
        super(t);
    }

    public DirectoryExistsException(String s, Throwable t) {
        super(s, t);
    }
}
