package o.eg;

import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Array;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.a.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eg\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static boolean b;
    private static char[] c;
    private static long d;
    private static int g;
    private static boolean h;
    private static int i;
    private final List<Object> e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        d = 5516175073835730997L;
        c = new char[]{61710, 61723, 61728, 61743, 61727, 61908, 61736, 61699, 61734, 61720, 61740, 61923, 61705, 61738, 61731, 61725, 61698, 61701, 61702, 61722, 61730, 61721};
        b = true;
        h = true;
        a = 782102964;
    }

    static void init$0() {
        $$a = new byte[]{78, -3, -72, 11};
        $$b = 8;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r6 = 121 - r6
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r0 = o.eg.e.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.e.k(byte, short, short, java.lang.Object[]):void");
    }

    public e() {
        this.e = new ArrayList();
    }

    public e(List<?> list) {
        this.e = new ArrayList(list);
    }

    public e(Collection<?> collection) {
        this();
        if (collection != null) {
            Iterator<?> it = collection.iterator();
            while (it.hasNext()) {
                b(b.d(it.next()));
            }
        }
    }

    private e(j jVar) throws d {
        Object c2 = jVar.c();
        if (c2 instanceof e) {
            this.e = ((e) c2).e;
        } else {
            Object[] objArr = new Object[1];
            f("쾮쿤俱읈镮蓵넊\uec47⯺\ue311ꁠ闽袇", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1, objArr);
            throw a.a(c2, ((String) objArr[0]).intern());
        }
    }

    public e(String str) throws d {
        this(new j(str));
    }

    public e(Object obj) throws d {
        if (!obj.getClass().isArray()) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            f("…\u2068嵛헞祟棿ݻϯ鷥\uf1e9䱓⏱期贌‒羟䪷껏\u05f6顳껧䪣\ud959듚鈗晓뵣", KeyEvent.keyCodeFromString(""), objArr);
            throw new d(sb.append(((String) objArr[0]).intern()).append(obj.getClass()).toString());
        }
        int length = Array.getLength(obj);
        this.e = new ArrayList(length);
        for (int i2 = 0; i2 < length; i2++) {
            b(b.d(Array.get(obj, i2)));
        }
    }

    public final int d() {
        int i2 = i + 57;
        g = i2 % 128;
        int i3 = i2 % 2;
        int size = this.e.size();
        int i4 = i + 23;
        g = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 1 : ')') {
            case ')':
                return size;
            default:
                int i5 = 38 / 0;
                return size;
        }
    }

    public final e b(Object obj) {
        int i2 = g + 9;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                this.e.add(obj);
                return this;
            default:
                this.e.add(obj);
                throw null;
        }
    }

    public final e e(int i2, Object obj) throws d {
        while (true) {
            switch (this.e.size() <= i2) {
                case true:
                    this.e.add(null);
                    int i3 = g + Opcodes.LNEG;
                    i = i3 % 128;
                    int i4 = i3 % 2;
                default:
                    this.e.set(i2, obj);
                    int i5 = g + 95;
                    i = i5 % 128;
                    switch (i5 % 2 == 0) {
                        case false:
                            return this;
                        default:
                            int i6 = 21 / 0;
                            return this;
                    }
            }
        }
    }

    public final Object e(int i2) throws d {
        int i3 = g + 21;
        i = i3 % 128;
        int i4 = i3 % 2;
        try {
            Object obj = this.e.get(i2);
            if (obj == null) {
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                j(null, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.IAND, null, "\u0086\u0087\u0082\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
                StringBuilder append = sb.append(((String) objArr[0]).intern()).append(i2);
                Object[] objArr2 = new Object[1];
                f("䘶䘖웓乐똎ꞩ욧旰尹樴茞\ue233ň", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
                throw new d(append.append(((String) objArr2[0]).intern()).toString());
            }
            int i5 = g + Opcodes.LSUB;
            i = i5 % 128;
            int i6 = i5 % 2;
            return obj;
        } catch (IndexOutOfBoundsException e) {
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr3 = new Object[1];
            j(null, TextUtils.getCapsMode("", 0, 0) + 127, null, "\u0086\u008b\u0085\u008a\u0089\u0088", objArr3);
            StringBuilder append2 = sb2.append(((String) objArr3[0]).intern()).append(i2);
            Object[] objArr4 = new Object[1];
            f("㐟㐿ྩ蜬㉈⏩孊ភ솀ꍔݒ翆猽\udff2欂⏩庂ﱫ仟쐆몑᠍", ViewConfiguration.getTouchSlop() >> 8, objArr4);
            StringBuilder append3 = append2.append(((String) objArr4[0]).intern()).append(this.e.size());
            Object[] objArr5 = new Object[1];
            j(null, (ViewConfiguration.getLongPressTimeout() >> 16) + 127, null, "\u008c", objArr5);
            throw new d(append3.append(((String) objArr5[0]).intern()).toString(), e);
        }
    }

    public final Integer c(int i2) throws d {
        int i3 = i + 91;
        g = i3 % 128;
        int i4 = i3 % 2;
        Object e = e(i2);
        Integer a2 = a.a(e);
        switch (a2 == null ? 'I' : '[') {
            case 'I':
                Integer valueOf = Integer.valueOf(i2);
                Object[] objArr = new Object[1];
                f("ꠦꡏ螝༙Ἷຟ능", (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
                throw a.b(valueOf, e, ((String) objArr[0]).intern());
            default:
                int i5 = g + 49;
                i = i5 % 128;
                int i6 = i5 % 2;
                return a2;
        }
    }

    public final Long a(int i2) throws d {
        int i3 = g + 73;
        i = i3 % 128;
        int i4 = i3 % 2;
        Object e = e(i2);
        Long e2 = a.e(e);
        switch (e2 == null ? (char) 1 : '?') {
            case '?':
                return e2;
            default:
                int i5 = i + 19;
                g = i5 % 128;
                if (i5 % 2 != 0) {
                }
                Integer valueOf = Integer.valueOf(i2);
                Object[] objArr = new Object[1];
                f("啡唍ᠩ邬锊蒰朂\ufddb", Process.myPid() >> 22, objArr);
                throw a.b(valueOf, e, ((String) objArr[0]).intern());
        }
    }

    public final String d(int i2) throws d {
        Object e = e(i2);
        String f = a.f(e);
        Object obj = null;
        switch (f == null ? '\t' : 'B') {
            case '\t':
                int i3 = i + 57;
                g = i3 % 128;
                int i4 = i3 % 2;
                Integer valueOf = Integer.valueOf(i2);
                Object[] objArr = new Object[1];
                j(null, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 127, null, "\u0090\u0089\u008f\u008e\u0087\u008d", objArr);
                throw a.b(valueOf, e, ((String) objArr[0]).intern());
            default:
                int i5 = g + 9;
                i = i5 % 128;
                switch (i5 % 2 == 0 ? '\n' : 'K') {
                    case 'K':
                        return f;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public final b b(int i2) throws d {
        int i3 = g + 31;
        i = i3 % 128;
        int i4 = i3 % 2;
        Object e = e(i2);
        switch (e instanceof b ? Typography.quote : '#') {
            case '\"':
                int i5 = i + Opcodes.LREM;
                int i6 = i5 % 128;
                g = i6;
                b bVar = (b) e;
                switch (i5 % 2 != 0 ? 'E' : '/') {
                    case '/':
                        break;
                    default:
                        int i7 = 45 / 0;
                        break;
                }
                int i8 = i6 + 29;
                i = i8 % 128;
                int i9 = i8 % 2;
                return bVar;
            default:
                Integer valueOf = Integer.valueOf(i2);
                Object[] objArr = new Object[1];
                j(null, View.MeasureSpec.getMode(0) + 127, null, "\u0087\u0096\u0085\u0095\u0094\u0092\u0093\u0092\u008d\u0091", objArr);
                throw a.b(valueOf, e, ((String) objArr[0]).intern());
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0028. Please report as an issue. */
    public final boolean equals(Object obj) {
        if (obj instanceof e) {
            switch (((e) obj).e.equals(this.e) ? 'J' : (char) 20) {
                case 'J':
                    int i2 = g;
                    int i3 = i2 + 43;
                    i = i3 % 128;
                    switch (i3 % 2 == 0) {
                    }
                    int i4 = i2 + Opcodes.LSHL;
                    i = i4 % 128;
                    int i5 = i4 % 2;
                    return true;
            }
        }
        return false;
    }

    public final int hashCode() {
        int hashCode;
        int i2 = i + Opcodes.DSUB;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                hashCode = this.e.hashCode();
                int i3 = 28 / 0;
                break;
            default:
                hashCode = this.e.hashCode();
                break;
        }
        int i4 = i + Opcodes.LMUL;
        g = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return hashCode;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    final void b(o.eg.c r6) throws o.eg.d {
        /*
            r5 = this;
            int r0 = o.eg.e.g
            int r0 = r0 + 99
            int r1 = r0 % 128
            o.eg.e.i = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 != 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            switch(r0) {
                case 1: goto L1e;
                default: goto L14;
            }
        L14:
            r6.b()
            java.util.List<java.lang.Object> r0 = r5.e
            java.util.Iterator r0 = r0.iterator()
            goto L2d
        L1e:
            r6.b()
            java.util.List<java.lang.Object> r0 = r5.e
            java.util.Iterator r0 = r0.iterator()
            r3 = 19
            int r3 = r3 / r2
            goto L2d
        L2b:
            r6 = move-exception
            throw r6
        L2d:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L36
            r3 = r1
            goto L37
        L36:
            r3 = r2
        L37:
            switch(r3) {
                case 1: goto L3e;
                default: goto L3a;
            }
        L3a:
            r6.a()
            return
        L3e:
            java.lang.Object r3 = r0.next()
            r6.c(r3)
            int r3 = o.eg.e.i
            int r3 = r3 + 65
            int r4 = r3 % 128
            o.eg.e.g = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L54
            r3 = 19
            goto L56
        L54:
            r3 = 44
        L56:
            switch(r3) {
                case 19: goto L2d;
                default: goto L59;
            }
        L59:
            goto L2d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.e.b(o.eg.c):void");
    }

    public final String a() {
        try {
            c cVar = new c();
            b(cVar);
            String d2 = cVar.d();
            int i2 = g + 63;
            i = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    return d2;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (d e) {
            return "";
        }
    }

    private static void f(String str, int i2, Object[] objArr) {
        char[] cArr;
        int i3 = $10;
        int i4 = i3 + 5;
        $11 = i4 % 128;
        int i5 = i4 % 2;
        Object obj = null;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                int i6 = i3 + 41;
                $11 = i6 % 128;
                if (i6 % 2 == 0) {
                    str.toCharArray();
                    obj.hashCode();
                    throw null;
                }
                cArr = str.toCharArray();
                break;
        }
        n nVar = new n();
        char[] b2 = n.b(d ^ 8632603938177761503L, cArr, i2);
        int i7 = 4;
        nVar.c = 4;
        while (nVar.c < b2.length) {
            int i8 = $11 + 83;
            $10 = i8 % 128;
            int i9 = i8 % 2;
            nVar.e = nVar.c - i7;
            int i10 = nVar.c;
            try {
                Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % i7]), Long.valueOf(nVar.e), Long.valueOf(d)};
                Object obj2 = o.e.a.s.get(-1945790373);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c(11 - Gravity.getAbsoluteGravity(0, 0), (char) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getScrollBarSize() >> 8) + 43);
                    byte b3 = (byte) (-1);
                    Object[] objArr3 = new Object[1];
                    k((byte) 53, b3, (byte) (b3 + 1), objArr3);
                    obj2 = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                    o.e.a.s.put(-1945790373, obj2);
                }
                b2[i10] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {nVar, nVar};
                    Object obj3 = o.e.a.s.get(-341518981);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getScrollBarSize() >> 8), (char) (((byte) KeyEvent.getModifierMetaStateMask()) + 1), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 249);
                        byte b4 = (byte) (-1);
                        Object[] objArr5 = new Object[1];
                        k((byte) 50, b4, (byte) (b4 + 1), objArr5);
                        obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-341518981, obj3);
                    }
                    ((Method) obj3).invoke(null, objArr4);
                    int i11 = $10 + 85;
                    $11 = i11 % 128;
                    switch (i11 % 2 == 0 ? (char) 1 : 'B') {
                        case 1:
                        default:
                            i7 = 4;
                    }
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        objArr[0] = new String(b2, 4, b2.length - 4);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void j(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 708
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eg.e.j(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
