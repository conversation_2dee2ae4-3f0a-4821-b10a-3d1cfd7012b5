package o.y;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Handler;
import android.os.Looper;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.db.c;
import o.ee.g;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\a.smali */
public abstract class a<C extends b<?>> extends AsyncTask<Void, Void, Void> {
    private final C b;
    private final Handler c = new o.ee.b(Looper.myLooper());
    private final boolean d;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int j = 0;
    private static int g = 1;
    private static char a = 32451;
    private static char i = 38558;
    private static char f = 56654;
    private static char e = 61821;

    public void a() {
        int i2 = g + 11;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                break;
            default:
                int i3 = 35 / 0;
                break;
        }
    }

    protected abstract void a(o.bb.d dVar);

    protected abstract void e(o.bb.d dVar);

    protected abstract void j();

    @Override // android.os.AsyncTask
    protected /* synthetic */ Void doInBackground(Void[] voidArr) {
        int i2 = g + 91;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '4' : '(') {
            case '4':
                o();
                throw null;
            default:
                return o();
        }
    }

    public a(C c, boolean z) {
        this.b = c;
        this.d = z;
    }

    private Void o() {
        int i2 = g + 85;
        j = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        String c = c();
        Object[] objArr = new Object[1];
        x("싾\u1ae5蒺惯㤏\ude2aγ蓉纝賹䀜蝊緢蒔㵁\ue020ガ\ued5bᦣ\ue395⒪壽", 21 - TextUtils.getOffsetBefore("", 0), objArr);
        g.d(c, ((String) objArr[0]).intern());
        Object obj = null;
        if (this.d) {
            int i4 = g + 97;
            j = i4 % 128;
            switch (i4 % 2 != 0 ? 'P' : 'D') {
                case 'P':
                    b();
                    obj.hashCode();
                    throw null;
                default:
                    switch (!b()) {
                        case true:
                            return null;
                    }
            }
        }
        a();
        try {
            j();
        } catch (Exception e2) {
            g.c();
            String c2 = c();
            Object[] objArr2 = new Object[1];
            x("싾\u1ae5蒺惯㤏\ude2aγ蓉纝賹䀜蝊緢蒔㵁\ue020ガ\ued5bᦣ\ue395侘ጞ魑㖧ᒍ暏ガ\ued5bѨ圱", 30 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr2);
            g.a(c2, ((String) objArr2[0]).intern(), e2);
            h().c(o.bb.a.x);
        }
        switch (this.d ? Typography.less : (char) 21) {
            case 21:
                break;
            default:
                int i5 = j + 85;
                g = i5 % 128;
                int i6 = i5 % 2;
                d();
                int i7 = g + 27;
                j = i7 % 128;
                int i8 = i7 % 2;
                break;
        }
        g.c();
        String c3 = c();
        Object[] objArr3 = new Object[1];
        x("싾\u1ae5蒺惯㤏\ude2a笰沐ᦣ\ue395ᑮ玳ᴌ䁼蝞ꑗᶛ攞駹秿", 21 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
        g.d(c3, ((String) objArr3[0]).intern());
        this.c.post(new Runnable() { // from class: o.y.a$$ExternalSyntheticLambda0
            @Override // java.lang.Runnable
            public final void run() {
                a.this.m();
            }
        });
        return null;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void m() {
        int i2 = j + 85;
        g = i2 % 128;
        int i3 = i2 % 2;
        if (!h().b()) {
            g.c();
            String c = c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            x("싾\u1ae5蒺惯㤏\ude2aγ蓉뻌갅贆扑퓗Ṫΐᄹ跕䐡랣뀿緢蒔宆苊魑㖧浩婈蓰\ue6cf饥鐮랳麨᪦╹", (ViewConfiguration.getPressedStateDuration() >> 16) + 36, objArr);
            g.d(c, sb.append(((String) objArr[0]).intern()).append(h().d()).toString());
            e(h());
            int i4 = g + Opcodes.LREM;
            j = i4 % 128;
            switch (i4 % 2 != 0 ? 'S' : '+') {
                case Opcodes.AASTORE /* 83 */:
                    throw null;
                default:
                    return;
            }
        }
        g.c();
        String c2 = c();
        Object[] objArr2 = new Object[1];
        x("싾\u1ae5蒺惯㤏\ude2aγ蓉뻌갅贆扑퓗Ṫΐᄹ跕䐡랣뀿緢蒔宆苊魑㖧⅌\uf84b\uf103㏋뤭\uf304\u12d7쮀", 32 - Process.getGidForName(""), objArr2);
        g.d(c2, ((String) objArr2[0]).intern());
        a(h());
    }

    /* renamed from: o.y.a$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\y\a$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int b;
        private static int c;
        static final /* synthetic */ int[] d;

        static {
            c = 0;
            b = 1;
            int[] iArr = new int[c.EnumC0035c.values().length];
            d = iArr;
            try {
                iArr[c.EnumC0035c.b.ordinal()] = 1;
                int i = c;
                int i2 = ((i | 79) << 1) - (i ^ 79);
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                d[c.EnumC0035c.c.ordinal()] = 2;
                int i3 = b;
                int i4 = (i3 ^ 41) + ((i3 & 41) << 1);
                c = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        return;
                    default:
                        int i5 = 43 / 0;
                        return;
                }
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:14:0x0068. Please report as an issue. */
    protected final boolean b() {
        int i2 = j + 61;
        g = i2 % 128;
        int i3 = i2 % 2;
        try {
            o.db.b.a().d();
            return true;
        } catch (o.db.c e2) {
            g.c();
            String c = c();
            Object[] objArr = new Object[1];
            x("싾\u1ae5蒺惯㤏\ude2a笰沐ꟊ鲞쎤ᗯ랳麨ꔳ憒៕锵楑䴝긠볒俇靂笰沐\uf33e\uf2fbﱢ娴Ẹ\udee4鄌⁷䜞\uf80d䓡⺜ｍ蹨杉㗗䐏鯀❞景", 45 - Drawable.resolveOpacity(0, 0), objArr);
            g.a(c, ((String) objArr[0]).intern(), e2);
            switch (AnonymousClass2.d[e2.b().ordinal()]) {
                case 1:
                    h().c(o.bb.a.p);
                    int i4 = j + Opcodes.LUSHR;
                    g = i4 % 128;
                    switch (i4 % 2 == 0 ? '^' : '[') {
                    }
                case 2:
                    h().c(o.bb.a.r);
                    break;
            }
            int i5 = g + 43;
            j = i5 % 128;
            switch (i5 % 2 != 0 ? (char) 11 : '\'') {
                case 11:
                    int i6 = 35 / 0;
                    return false;
                default:
                    return false;
            }
        }
    }

    protected final void d() {
        int i2 = j + 61;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                o.db.b.a().d(g(), h(), i());
                int i3 = g + 97;
                j = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        int i4 = 21 / 0;
                        return;
                    default:
                        return;
                }
            default:
                o.db.b.a().d(g(), h(), i());
                throw null;
        }
    }

    public final C e() {
        int i2 = g + 99;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? 'P' : '_') {
            case Opcodes.SWAP /* 95 */:
                return this.b;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public String c() {
        StringBuilder append = new StringBuilder().append(e().a());
        Object[] objArr = new Object[1];
        x("ὐ㮹믨쿘秠幚", 5 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr);
        String obj = append.append(((String) objArr[0]).intern()).toString();
        int i2 = j + 11;
        g = i2 % 128;
        int i3 = i2 % 2;
        return obj;
    }

    protected final Context g() {
        int i2 = j + 51;
        g = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 17 : (char) 27) {
            case 27:
                return e().e();
            default:
                e().e();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    protected final o.bb.d h() {
        int i2 = g + 51;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '@' : '!') {
            case '@':
                e().d();
                throw null;
            default:
                o.bb.d d = e().d();
                int i3 = g + Opcodes.LUSHR;
                j = i3 % 128;
                int i4 = i3 % 2;
                return d;
        }
    }

    protected final o.ei.c f() {
        int i2 = g + 27;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return e().g();
            default:
                e().g();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    protected final o.bv.g i() {
        int i2 = j + 3;
        g = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                e().h();
                throw null;
            default:
                return e().h();
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void x(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 576
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.y.a.x(java.lang.String, int, java.lang.Object[]):void");
    }
}
