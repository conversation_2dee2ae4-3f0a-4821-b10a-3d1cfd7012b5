package bc.org.bouncycastle.asn1.x9;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.WNafUtil;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves.smali */
public class X962NamedCurves {
    static e8 a = new k();
    static e8 b = new p();
    static e8 c = new q();
    static e8 d = new r();
    static e8 e = new s();
    static e8 f = new t();
    static e8 g = new u();
    static e8 h = new v();
    static e8 i = new w();
    static e8 j = new a();
    static e8 k = new b();
    static e8 l = new c();
    static e8 m = new d();
    static e8 n = new e();

    /* renamed from: o, reason: collision with root package name */
    static e8 f4o = new f();
    static e8 p = new g();
    static e8 q = new h();
    static e8 r = new i();
    static e8 s = new j();
    static e8 t = new l();
    static e8 u = new m();
    static e8 v = new n();
    static e8 w = new o();
    static final Hashtable x = new Hashtable();
    static final Hashtable y = new Hashtable();
    static final Hashtable z = new Hashtable();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$a.smali */
    class a extends e8 {
        a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.IF_ICMPGT, 1, 2, 8, X962NamedCurves.b("07A526C63D3E25A256A007699F5447E32AE456B50E"), X962NamedCurves.b("03F7061798EB99E238FD6F1BF95B48FEEB4854252B"), X962NamedCurves.b("03FFFFFFFFFFFFFFFFFFFE1AEE140F110AFF961309"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0202F9F87B7C574D0BDECF8A22E6524775F98CDEBDCB"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$b.smali */
    class b extends e8 {
        b() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.ARETURN, 1, 2, 43, X962NamedCurves.b("E4E6DB2995065C407D9D39B8D0967B96704BA8E9C90B"), X962NamedCurves.b("5DDA470ABE6414DE8EC133AE28E9BBD7FCEC0AE0FFF2"), X962NamedCurves.b("010092537397ECA4F6145799D62B0A19CE06FE26AD"), BigInteger.valueOf(65390L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "038D16C2866798B600F9F08BB4A8E860F3298CE04A5798"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$c.smali */
    class c extends e8 {
        c() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.ATHROW, 9, X962NamedCurves.b("2866537B676752636A68F56554E12640276B649EF7526267"), X962NamedCurves.b("2E45EF571F00786F67B0081B9495A3D95462F5DE0AA185EC"), X962NamedCurves.b("40000000000000000000000004A20E90C39067C893BBB9A5"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("4E13CA542744D696E67687561517552F279A8C84");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0236B3DAF8A23206F9C4F299D7B21A9C369137F2C84AE1AA0D"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$d.smali */
    class d extends e8 {
        d() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.ATHROW, 9, X962NamedCurves.b("401028774D7777C7B7666D1366EA432071274F89FF01E718"), X962NamedCurves.b("0620048D28BCBD03B6249C99182B7C8CD19700C362C46A01"), X962NamedCurves.b("20000000000000000000000050508CB89F652824E06B8173"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "023809B2B7CC1B28CC5A87926AAD83FD28789E81E2C9E3BF10"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$e.smali */
    class e extends e8 {
        e() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.ATHROW, 9, X962NamedCurves.b("6C01074756099122221056911C77D77E77A777E7E7E77FCB"), X962NamedCurves.b("71FE1AF926CF847989EFEF8DB459F66394D90F32AD3F15E8"), X962NamedCurves.b("155555555555555555555555610C0B196812BFB6288A3EA3"), BigInteger.valueOf(6L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "03375D4CE24FDE434489DE8746E71786015009E66E38A926DD"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$f.smali */
    class f extends e8 {
        f() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(208, 1, 2, 83, BigInteger.valueOf(0L), X962NamedCurves.b("C8619ED45A62E6212E1160349E2BFA844439FAFC2A3FD1638F9E"), X962NamedCurves.b("0101BAF95C9723C57B6C21DA2EFF2D5ED588BDD5717E212F9D"), BigInteger.valueOf(65096L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0289FDFBE4ABE193DF9559ECF07AC0CE78554E2784EB8C1ED1A57A"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$g.smali */
    class g extends e8 {
        g() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(239, 36, X962NamedCurves.b("32010857077C5431123A46B808906756F543423E8D27877578125778AC76"), X962NamedCurves.b("790408F2EEDAF392B012EDEFB3392F30F4327C0CA3F31FC383C422AA8C16"), X962NamedCurves.b("2000000000000000000000000000000F4D42FFE1492A4993F1CAD666E447"), BigInteger.valueOf(4L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0257927098FA932E7C0A96D3FD5B706EF7E5F5C156E16B7E7C86038552E91D"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$h.smali */
    class h extends e8 {
        h() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(239, 36, X962NamedCurves.b("4230017757A767FAE42398569B746325D45313AF0766266479B75654E65F"), X962NamedCurves.b("5037EA654196CFF0CD82B2C14A2FCF2E3FF8775285B545722F03EACDB74B"), X962NamedCurves.b("1555555555555555555555555555553C6F2885259C31E3FCDF154624522D"), BigInteger.valueOf(6L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0228F9D04E900069C8DC47A08534FE76D2B900B7D7EF31F5709F200C4CA205"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$i.smali */
    class i extends e8 {
        i() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(239, 36, X962NamedCurves.b("01238774666A67766D6676F778E676B66999176666E687666D8766C66A9F"), X962NamedCurves.b("6A941977BA9F6A435199ACFC51067ED587F519C5ECB541B8E44111DE1D40"), X962NamedCurves.b("0CCCCCCCCCCCCCCCCCCCCCCCCCCCCCAC4912D2D9DF903EF9888B8A0E4CFF"), BigInteger.valueOf(10L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0370F6E9D04D289C4E89913CE3530BFDE903977D42B146D539BF1BDE4E9C92"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$j.smali */
    class j extends e8 {
        j() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(272, 1, 3, 56, X962NamedCurves.b("91A091F03B5FBA4AB2CCF49C4EDD220FB028712D42BE752B2C40094DBACDB586FB20"), X962NamedCurves.b("7167EFC92BB2E3CE7C8AAAFF34E12A9C557003D7C73A6FAF003F99F6CC8482E540F7"), X962NamedCurves.b("0100FAF51354E0E39E4892DF6E319C72C8161603FA45AA7B998A167B8F1E629521"), BigInteger.valueOf(65286L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "026108BABB2CEEBCF787058A056CBE0CFE622D7723A289E08A07AE13EF0D10D171DD8D"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$k.smali */
    class k extends e8 {
        k() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(X962NamedCurves.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF"), X962NamedCurves.b("fffffffffffffffffffffffffffffffefffffffffffffffc"), X962NamedCurves.b("64210519e59c80e70fa7e9ab72243049feb8deecc146b9b1"), X962NamedCurves.b("ffffffffffffffffffffffff99def836146bc9b1b4d22831"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("3045AE6FC8422f64ED579528D38120EAE12196D5");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "03188da80eb03090f67cbf20eb43a18800f4ff0afd82ff1012"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$l.smali */
    class l extends e8 {
        l() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(304, 1, 2, 11, X962NamedCurves.b("FD0D693149A118F651E6DCE6802085377E5F882D1B510B44160074C1288078365A0396C8E681"), X962NamedCurves.b("BDDB97E555A50A908E43B01C798EA5DAA6788F1EA2794EFCF57166B8C14039601E55827340BE"), X962NamedCurves.b("0101D556572AABAC800101D556572AABAC8001022D5C91DD173F8FB561DA6899164443051D"), BigInteger.valueOf(65070L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "02197B07845E9BE2D96ADB0F5F3C7F2CFFBD7A3EB8B6FEC35C7FD67F26DDF6285A644F740A2614"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$m.smali */
    class m extends e8 {
        m() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(359, 68, X962NamedCurves.b("5667676A654B20754F356EA92017D946567C46675556F19556A04616B567D223A5E05656FB549016A96656A557"), X962NamedCurves.b("2472E2D0197C49363F1FE7F5B6DB075D52B6947D135D8CA445805D39BC345626089687742B6329E70680231988"), X962NamedCurves.b("01AF286BCA1AF286BCA1AF286BCA1AF286BCA1AF286BC9FB8F6B85C556892C20A7EB964FE7719E74F490758D3B"), BigInteger.valueOf(76L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "033C258EF3047767E7EDE0F1FDAA79DAEE3841366A132E163ACED4ED2401DF9C6BDCDE98E8E707C07A2239B1B097"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$n.smali */
    class n extends e8 {
        n() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(368, 1, 2, 85, X962NamedCurves.b("E0D2EE25095206F5E2A4F9ED229F1F256E79A0E2B455970D8D0D865BD94778C576D62F0AB7519CCD2A1A906AE30D"), X962NamedCurves.b("FC1217D4320A90452C760A58EDCD30C8DD069B3C34453837A34ED50CB54917E1C2112D84D164F444F8F74786046A"), X962NamedCurves.b("010090512DA9AF72B08349D98A5DD4C7B0532ECA51CE03E2D10F3B7AC579BD87E909AE40A6F131E9CFCE5BD967"), BigInteger.valueOf(65392L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "021085E2755381DCCCE3C1557AFA10C2F0C0C2825646C5B34A394CBCFA8BC16B22E7E789E927BE216F02E1FB136A5F"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$o.smali */
    class o extends e8 {
        o() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(431, Opcodes.ISHL, X962NamedCurves.b("1A827EF00DD6FC0E234CAF046C6A5D8A85395B236CC4AD2CF32A0CADBDC9DDF620B0EB9906D0957F6C6FEACD615468DF104DE296CD8F"), X962NamedCurves.b("10D9B4A3D9047D8B154359ABFB1B7F5485B04CEB868237DDC9DEDA982A679A5A919B626D4E50A8DD731B107A9962381FB5D807BF2618"), X962NamedCurves.b("0340340340340340340340340340340340340340340340340340340323C313FAB50589703B5EC68D3587FEC60D161CC149C1AD4A91"), BigInteger.valueOf(10080L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "02120FC05D3C67A99DE161D2F4092622FECA701BE4F50F4758714E8A87BBF2A658EF8C21E7C5EFE965361F6C2999C0C247B0DBD70CE6B7"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$p.smali */
    class p extends e8 {
        p() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(X962NamedCurves.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF"), X962NamedCurves.b("fffffffffffffffffffffffffffffffefffffffffffffffc"), X962NamedCurves.b("cc22d6dfb95c6b25e49c0d6364a4e5980c393aa21668d953"), X962NamedCurves.b("fffffffffffffffffffffffe5fb1a724dc80418648d8dd31"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("31a92ee2029fd10d901b113e990710f0d21ac6b6");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "03eea2bae7e1497842f2de7769cfe9c989c072ad696f48034a"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$q.smali */
    class q extends e8 {
        q() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(X962NamedCurves.b("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF"), X962NamedCurves.b("fffffffffffffffffffffffffffffffefffffffffffffffc"), X962NamedCurves.b("22123dc2395a05caa7423daeccc94760a7d462256bd56916"), X962NamedCurves.b("ffffffffffffffffffffffff7a62d031c83f4294f640ec13"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("c469684435deb378c4b65ca9591e2a5763059a2e");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "027d29778100c65a1da1783716588dce2b8b4aee8e228f1896"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$r.smali */
    class r extends e8 {
        r() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(new BigInteger("883423532389192164791648750360308885314476597252960362792450860609699839"), X962NamedCurves.b("7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"), X962NamedCurves.b("6b016c3bdcf18941d0d654921475ca71a9db2fb27d1d37796185c2942c0a"), X962NamedCurves.b("7fffffffffffffffffffffff7fffff9e5e9a9f5d9071fbd1522688909d0b"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("e43bb460f0b80cc0c0b075798e948060f8321b7d");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "020ffa963cdca8816ccc33b8642bedf905c3d358573d3f27fbbd3b3cb9aaaf"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$s.smali */
    class s extends e8 {
        s() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(new BigInteger("883423532389192164791648750360308885314476597252960362792450860609699839"), X962NamedCurves.b("7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"), X962NamedCurves.b("617fab6832576cbbfed50d99f0249c3fee58b94ba0038c7ae84c8c832f2c"), X962NamedCurves.b("7fffffffffffffffffffffff800000cfa7e8594377d414c03821bc582063"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("e8b4011604095303ca3b8099982be09fcb9ae616");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0238af09d98727705120c921bb5e9e26296a3cdcf2f35757a0eafd87b830e7"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$t.smali */
    class t extends e8 {
        t() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(new BigInteger("883423532389192164791648750360308885314476597252960362792450860609699839"), X962NamedCurves.b("7fffffffffffffffffffffff7fffffffffff8000000000007ffffffffffc"), X962NamedCurves.b("255705fa2a306654b1f4cb03d6a750a30c250102d4988717d9ba15ab6d3e"), X962NamedCurves.b("7fffffffffffffffffffffff7fffff975deb41b3a6057c3c432146526551"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("7d7374168ffe3471b60a857686a19475d3bfa2ff");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "036768ae8e18bb92cfcf005c949aa2c6d94853d0e660bbf854b1c9505fe95a"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$u.smali */
    class u extends e8 {
        u() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.Fp(new BigInteger("115792089210356248762697446949407573530086143415290314195533631308867097853951"), X962NamedCurves.b("ffffffff00000001000000000000000000000000fffffffffffffffffffffffc"), X962NamedCurves.b("5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b"), X962NamedCurves.b("ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("c49d360886e704936a6678e1139d26b7819f7e90");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "036b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$v.smali */
    class v extends e8 {
        v() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.IF_ICMPGT, 1, 2, 8, X962NamedCurves.b("072546B5435234A422E0789675F432C89435DE5242"), X962NamedCurves.b("00C9517D06D5240D3CFF38C74B20B6CD4D6F9DD4D9"), X962NamedCurves.b("0400000000000000000001E60FC8821CC74DAEAFC1"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            byte[] a = z4.a("D2C0FB15760860DEF1EEF4D696E6768756151754");
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "0307AF69989546103D79329FCC3D74880F33BBE803CB"), c.getOrder(), c.getCofactor(), a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\asn1\x9\X962NamedCurves$w.smali */
    class w extends e8 {
        w() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return X962NamedCurves.b(new ECCurve.F2m(Opcodes.IF_ICMPGT, 1, 2, 8, X962NamedCurves.b("0108B39E77C4B108BED981ED0E890E117C511CF072"), X962NamedCurves.b("0667ACEB38AF4E488C407433FFAE4F1C811638DF20"), X962NamedCurves.b("03FFFFFFFFFFFFFFFFFFFDF64DE1151ADBB78F10A7"), BigInteger.valueOf(2L)));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, X962NamedCurves.b(c, "030024266E4EB5106D0A964D92C4860E2671DB9B6CC5"), c.getOrder(), c.getCofactor(), null);
        }
    }

    static {
        a("prime192v1", j8.S, a);
        a("prime192v2", j8.T, b);
        a("prime192v3", j8.U, c);
        a("prime239v1", j8.V, d);
        a("prime239v2", j8.W, e);
        a("prime239v3", j8.X, f);
        a("prime256v1", j8.Y, g);
        a("c2pnb163v1", j8.t, h);
        a("c2pnb163v2", j8.u, i);
        a("c2pnb163v3", j8.v, j);
        a("c2pnb176w1", j8.w, k);
        a("c2tnb191v1", j8.y, l);
        a("c2tnb191v2", j8.z, m);
        a("c2tnb191v3", j8.A, n);
        a("c2pnb208w1", j8.E, f4o);
        a("c2tnb239v1", j8.F, p);
        a("c2tnb239v2", j8.G, q);
        a("c2tnb239v3", j8.H, r);
        a("c2pnb272w1", j8.K, s);
        a("c2pnb304w1", j8.M, t);
        a("c2tnb359v1", j8.N, u);
        a("c2pnb368w1", j8.O, v);
        a("c2tnb431r1", j8.P, w);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, String str) {
        f8 f8Var = new f8(eCCurve, z4.a(str));
        WNafUtil.configureBasepoint(f8Var.e());
        return f8Var;
    }

    public static X9ECParameters getByName(String str) {
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w oid = getOID(str);
        if (oid == null) {
            return null;
        }
        return getByOID(oid);
    }

    public static e8 getByNameLazy(String str) {
        com.vasco.digipass.sdk.utils.utilities.obfuscated.w oid = getOID(str);
        if (oid == null) {
            return null;
        }
        return getByOIDLazy(oid);
    }

    public static X9ECParameters getByOID(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        e8 byOIDLazy = getByOIDLazy(wVar);
        if (byOIDLazy == null) {
            return null;
        }
        return byOIDLazy.d();
    }

    public static e8 getByOIDLazy(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        return (e8) y.get(wVar);
    }

    public static String getName(com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar) {
        return (String) z.get(wVar);
    }

    public static Enumeration getNames() {
        return x.keys();
    }

    public static com.vasco.digipass.sdk.utils.utilities.obfuscated.w getOID(String str) {
        return (com.vasco.digipass.sdk.utils.utilities.obfuscated.w) x.get(o7.b(str));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BigInteger b(String str) {
        return new BigInteger(1, z4.a(str));
    }

    static void a(String str, com.vasco.digipass.sdk.utils.utilities.obfuscated.w wVar, e8 e8Var) {
        x.put(str, wVar);
        z.put(wVar, str);
        y.put(wVar, e8Var);
    }
}
