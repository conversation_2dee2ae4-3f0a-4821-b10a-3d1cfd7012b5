package o.i;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.r.e;
import org.bouncycastle.crypto.signers.PSSSigner;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\k.smali */
public abstract class k<Provider extends o.r.e> extends g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int c;
    private static byte[] d;
    private static int f;
    private static short[] g;
    private static int h;
    private static char[] i;
    private static long j;
    private final Provider e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        n();
        SystemClock.uptimeMillis();
        KeyEvent.getDeadChar(0, 0);
        TextUtils.getOffsetAfter("", 0);
        ViewConfiguration.getScrollFriction();
        MotionEvent.axisFromString("");
        int i2 = f + 97;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$a = new byte[]{124, 92, -85, -9};
        $$b = 196;
    }

    static void n() {
        d = new byte[]{89, -85, 88, -93, -76, 115, 83, -86, 89, -65, 82, 86, 89, -86, -91, 81, 88, 83, -104, 99, -95, 84, 82, 87, -83, 82, -98, 123, -89, 95, 80, -95, 93, -86, -119, 82, -95, 82, 93, -85, -81, 84, -88, -81, -30, 25, 84, -83, 94, -72, 64, -90, 94, -70, -87, 109, -105, -112, -105, -37, 41, 97, -111, 105, -109, 104, -122, 104, -39, 59, ByteCompanionObject.MIN_VALUE, 101, -36, 43, 107, -105, -105, -111, 96, -111, -34, 46, 109, 99, -127, 121, -97, 103, -125, -112, -45, 63, -104, -37, 63, -36, -105, 97, -97, 62, 109, -105, -112, -73, 118, 97, -111, 105, -109, 104, -122, -120, 69, -99, 96, -100, 97, 44, -45, -63, 63, -45, -127, 1, 37, -63, -15, 102, -46, 37, -11, -4, 52, -56, 126, 45, -41, -48, -9, 54, 33, -47, 41, -45, 40, -58, -56, 5, -35, 32, -36, 33, 40, -46, -43, -46, -98, 108, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -44, 44, -42, 45, -61, 45, -100, 110, 46, -46, -42, 34, -39, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -37, -42, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -123, 35, Tnaf.POW_2_WIDTH, 38, -60, 58, -42, -124, 107, 40, 38, -60, 60, -38, 34, -58, -11, -10, 101, -46, -98, 123, -46, -42, -103, 112, -38, 46, 41, -47, -47, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -58, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -43, -121, 122, -59, Base64.padSymbol, ByteCompanionObject.MIN_VALUE, 122, -103, -46, 49, -51, 107, 34, -48, 35, -40, 47, -102, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -38, 108, 47, 47, -43, -39, -60, 51, -56, 48, 34, -39, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -37, -42, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, -31, -86, 73, -75, 2, 81, -82, PSSSigner.TRAILER_IMPLICIT, 66, -82, -4, 92, -94, 2, -31, -86, 73, -75, 28, 88, PSSSigner.TRAILER_IMPLICIT, -20, 92, -94, 20, 87, 87, -83, -95, PSSSigner.TRAILER_IMPLICIT, 75, UtilitiesSDKConstants.SRP_LABEL_ENC, 72, 90, -95, 92, -93, -82, 92, -112, -112, -112, -112, -112, -112};
        b = 909053633;
        a = 856796731;
        c = -1108278611;
        i = new char[]{910, 48473, 32271, 16339, 63632, 47690, 31513, 13559, 62873, 46917, 28687, 12744, 62099, 44110, 27929, 12015, 61320, 43357, 27144, 11209, 58511, 42524, 26449, 8348, 57817, 41807, 23644, 7569, 57052, 38991, 22792, 6877, 56200, 38217, 22031, 6044, 53465, 37455, 56437, 25279, 41446, 57376, 10107, 26042, 42237, 60212, 10803, 26810, 45054, 60963, 11644, 29600, 45792, 61754, 12401, 30399, 46582, 50051, 32084, 48642, 65492, 14469, 31354, 47892, 62664, 13698, 30533, 45086, 61891, 12948, 27768, 44319, 61121, 12164, 26949, 43601, 60299, 9425, 26232, 42781, 57565, 8596, 25430, 39952, 56797, 7856, 22595, 39190, 56004, 7068, 21844, 38431, 55237, 4276, 21065, 37650, 52436, 3457, 20293, 34840, 51678, 2719, 37213, 12170, 60636, 44298, 27227, 10404, 59850, 42518, 26460, 9627, 58048, 41757, 24650, 16038, 65473, 48159, 32090, 15259, 63631, 47426, 30223, 13514, 62940, 45647, 29510, 12700, 52879, 36609, 19520, 2715, 52111, 34830, 18764, 1947, 50374, 34073, 16974, 155, 49610, 40459, 11451, 37484, 20783, 4347, 55212, 38266, 21537, 7110, 55975, 38987, 24360, 7914, 56738, 33628, 16953, 493, 49320, 34429, 17708, 1193, 52196, 35113, 18468, 4076, 52925, 35937, 29478, 13037, 61929, 46899, 30313, 13740, 62650, 47657, 31076, 14505, 65446, 48511, 31788, 9211, 58043, 41056, 26413, 9965, 58796, 43879, 27241, 10746, 59581, 44648, 27965, 11516, 37818, 20777, 4211, 55209, 38636, 21626, 11451, 37484, 20783, 4347, 55212, 38266, 21537, 7110, 55975, 38976, 24359, 7904, 56765, 33577, 16996, 425, 49316, 34412, 17725, 1249, 52134, 35181, 18537, 4019, 52969, 35884, 29498, 12969, 61924, 46889, 30246, 13823, 62636, 47739, 31035, 14560, 65453, 48493, 31788, 9191, 58089, 41082, 26429, 9960, 58813, 43900, 27194, 10665, 59635, 44585, 28012, 11514};
        j = 3886707717771399689L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(short r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = 110 - r9
            byte[] r0 = o.i.k.$$a
            int r7 = r7 + 4
            int r8 = r8 * 3
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r9
            r4 = r2
            r9 = r8
            r8 = r7
            goto L2c
        L14:
            r3 = r2
        L15:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L24
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L24:
            int r7 = r7 + 1
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2c:
            int r3 = -r3
            int r7 = r7 + r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.k.r(short, int, int, java.lang.Object[]):void");
    }

    k(f fVar, Provider provider) {
        super(fVar);
        this.e = provider;
    }

    private c e(Context context) {
        c cVar;
        int i2 = f + 71;
        h = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case false:
                this.e.a(context);
                obj.hashCode();
                throw null;
            default:
                switch (!this.e.a(context)) {
                    case false:
                        if (!this.e.e(context)) {
                            cVar = c.b;
                            break;
                        } else {
                            int i3 = f + Opcodes.DDIV;
                            h = i3 % 128;
                            switch (i3 % 2 == 0 ? '/' : (char) 18) {
                                case 18:
                                    if (!this.e.c()) {
                                        cVar = c.a;
                                        break;
                                    } else {
                                        cVar = c.c;
                                        break;
                                    }
                                default:
                                    this.e.c();
                                    obj.hashCode();
                                    throw null;
                            }
                        }
                    default:
                        cVar = c.d;
                        break;
                }
                o.ee.g.c();
                Object[] objArr = new Object[1];
                p((byte) (60 - KeyEvent.getDeadChar(0, 0)), ********** - TextUtils.getCapsMode("", 0, 0), (short) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (-45) - (ViewConfiguration.getDoubleTapTimeout() >> 16), (-87990371) - TextUtils.lastIndexOf("", '0'), objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                q((char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 12085), ViewConfiguration.getScrollDefaultDelay() >> 16, (ViewConfiguration.getTouchSlop() >> 8) + 38, objArr2);
                o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), cVar));
                return cVar;
        }
    }

    final byte[] b(Context context) throws a {
        int i2 = f + Opcodes.LUSHR;
        h = i2 % 128;
        try {
            switch (i2 % 2 != 0) {
                case true:
                    byte[] c2 = this.e.c(context);
                    int i3 = h + 79;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                    return c2;
                default:
                    this.e.c(context);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (o.r.b e) {
            Object[] objArr = new Object[1];
            p((byte) (59 - (ViewConfiguration.getTapTimeout() >> 16)), 1948382182 - View.combineMeasuredStates(0, 0), (short) (ViewConfiguration.getKeyRepeatTimeout() >> 16), (ViewConfiguration.getJumpTapTimeout() >> 16) - 60, (-87990346) + (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr);
            throw new a(((String) objArr[0]).intern(), e);
        }
    }

    final void d() throws a {
        int i2 = f + 51;
        h = i2 % 128;
        try {
            switch (i2 % 2 == 0 ? '(' : (char) 23) {
                case 23:
                    this.e.e();
                    return;
                default:
                    this.e.e();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        } catch (o.r.b e) {
            Object[] objArr = new Object[1];
            q((char) (61658 - (ViewConfiguration.getScrollBarSize() >> 8)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 38, 19 - TextUtils.getTrimmedLength(""), objArr);
            throw new a(((String) objArr[0]).intern(), e);
        }
    }

    public final Provider e() {
        int i2 = f + Opcodes.LREM;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? 'J' : '@') {
            case 'J':
                int i3 = 11 / 0;
                return this.e;
            default:
                return this.e;
        }
    }

    private boolean a(Context context) {
        int i2 = f + 75;
        h = i2 % 128;
        int i3 = i2 % 2;
        if (j() != c.c) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            p((byte) ('l' - AndroidCharacter.getMirror('0')), Color.red(0) + **********, (short) (ViewConfiguration.getKeyRepeatTimeout() >> 16), (-45) - (ViewConfiguration.getTapTimeout() >> 16), (-87990371) - ExpandableListView.getPackedPositionChild(0L), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            q((char) (ExpandableListView.getPackedPositionGroup(0L) + 48614), 102 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 40, objArr2);
            o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i()));
            return true;
        }
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        p((byte) (59 - ImageFormat.getBitsPerPixel(0)), ********** - View.MeasureSpec.getMode(0), (short) TextUtils.getTrimmedLength(""), (-44) - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (-87990370) - View.resolveSizeAndState(0, 0, 0), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        p((byte) (3 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), 1948382203 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), (short) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 19, (-87990330) - ((byte) KeyEvent.getModifierMetaStateMask()), objArr4);
        o.ee.g.d(intern2, String.format(((String) objArr4[0]).intern(), i()));
        String c2 = new o.dd.e(context).c(o());
        switch (c2 == null) {
            case true:
                o.ee.g.c();
                Object[] objArr5 = new Object[1];
                p((byte) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 61), ********** - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) View.combineMeasuredStates(0, 0), (-45) - (ViewConfiguration.getWindowTouchSlop() >> 8), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 87990371, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                p((byte) (66 - (ViewConfiguration.getTouchSlop() >> 8)), TextUtils.lastIndexOf("", '0') + 1948382265, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getTouchSlop() >> 8) - 45, (-87990329) - (ViewConfiguration.getTapTimeout() >> 16), objArr6);
                o.ee.g.e(intern3, ((String) objArr6[0]).intern());
                return false;
            default:
                try {
                    try {
                        this.e.c(o.dk.b.b(c2));
                        int i4 = f + 59;
                        h = i4 % 128;
                        switch (i4 % 2 == 0) {
                            case true:
                                throw null;
                            default:
                                return true;
                        }
                    } catch (IllegalArgumentException e) {
                        e = e;
                        o.ee.g.c();
                        Object[] objArr7 = new Object[1];
                        p((byte) (60 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), View.combineMeasuredStates(0, 0) + **********, (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (-45) - View.resolveSizeAndState(0, 0, 0), View.MeasureSpec.getMode(0) - 87990370, objArr7);
                        String intern4 = ((String) objArr7[0]).intern();
                        Object[] objArr8 = new Object[1];
                        q((char) (61240 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), 56 - ExpandableListView.getPackedPositionChild(0L), KeyEvent.keyCodeFromString("") + 45, objArr8);
                        o.ee.g.a(intern4, ((String) objArr8[0]).intern(), e);
                        return false;
                    }
                } catch (IllegalArgumentException e2) {
                    e = e2;
                }
        }
    }

    @Override // o.i.g
    public final boolean e(Context context, boolean z, o.bb.d dVar) {
        int i2 = f + 83;
        h = i2 % 128;
        int i3 = i2 % 2;
        boolean e = super.e(context, z, dVar);
        c e2 = e(context);
        switch (e2 == c.c) {
            case false:
                d(e2);
                break;
            default:
                int i4 = h + 65;
                f = i4 % 128;
                if (i4 % 2 != 0) {
                    throw null;
                }
                switch (z) {
                    case false:
                        d(c.a);
                        break;
                    default:
                        d(c.c);
                        int i5 = f + 79;
                        h = i5 % 128;
                        int i6 = i5 % 2;
                        break;
                }
        }
        switch (!a(context)) {
            case true:
                d(c.a);
                break;
        }
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) ((ViewConfiguration.getTouchSlop() >> 8) + 60), ********** - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (short) Color.blue(0), (-46) - TextUtils.indexOf((CharSequence) "", '0', 0), View.resolveSize(0, 0) - 87990370, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), View.resolveSize(0, 0) + Opcodes.D2I, TextUtils.indexOf((CharSequence) "", '0', 0) + 59, objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        return e;
    }

    @Override // o.i.g
    public final void c(Context context, boolean z, o.eg.b bVar) {
        int i2 = h + Opcodes.DSUB;
        f = i2 % 128;
        int i3 = i2 % 2;
        super.c(context, z, bVar);
        c e = e(context);
        switch (e == c.c) {
            case false:
                d(e);
                break;
            default:
                int i4 = f;
                int i5 = i4 + 67;
                h = i5 % 128;
                if (i5 % 2 == 0) {
                }
                if (!z) {
                    d(c.a);
                    break;
                } else {
                    int i6 = i4 + 17;
                    h = i6 % 128;
                    switch (i6 % 2 == 0 ? (char) 5 : 'Y') {
                        case Opcodes.DUP /* 89 */:
                            d(c.c);
                            break;
                        default:
                            d(c.c);
                            int i7 = 44 / 0;
                            break;
                    }
                    int i8 = f + 35;
                    h = i8 % 128;
                    int i9 = i8 % 2;
                    break;
                }
        }
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) (60 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), ********** - (Process.myPid() >> 22), (short) Color.argb(0, 0, 0, 0), (-45) - (ViewConfiguration.getMaximumFlingVelocity() >> 16), Color.blue(0) - 87990370, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q((char) Color.argb(0, 0, 0, 0), 201 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 52 - View.combineMeasuredStates(0, 0), objArr2);
        o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i(), j()));
        int i10 = f + 27;
        h = i10 % 128;
        switch (i10 % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    @Override // o.i.g
    public final void a(Context context, boolean z) {
        int i2 = h + Opcodes.LUSHR;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                c j2 = j();
                c e = e(context);
                if (e == c.c) {
                    int i3 = h + 1;
                    int i4 = i3 % 128;
                    f = i4;
                    int i5 = i3 % 2;
                    if (z) {
                        int i6 = i4 + 85;
                        h = i6 % 128;
                        int i7 = i6 % 2;
                        d(c.c);
                        int i8 = h + 35;
                        f = i8 % 128;
                        int i9 = i8 % 2;
                    } else {
                        d(c.a);
                    }
                } else {
                    d(e);
                }
                switch (j() == c.c ? (char) 16 : (char) 24) {
                    case 16:
                        switch (j2 != c.c ? 'X' : (char) 1) {
                            case 1:
                                break;
                            default:
                                int i10 = h + 9;
                                f = i10 % 128;
                                int i11 = i10 % 2;
                                o.ee.g.c();
                                Object[] objArr = new Object[1];
                                p((byte) (60 - Color.argb(0, 0, 0, 0)), ********** - View.MeasureSpec.getMode(0), (short) (KeyEvent.getMaxKeyCode() >> 16), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 45, (ViewConfiguration.getTouchSlop() >> 8) - 87990370, objArr);
                                String intern = ((String) objArr[0]).intern();
                                Object[] objArr2 = new Object[1];
                                p((byte) (Color.blue(0) + 71), 1948382299 + (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (short) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 12 - TextUtils.indexOf((CharSequence) "", '0'), (ViewConfiguration.getTapTimeout() >> 16) - 87990329, objArr2);
                                o.ee.g.d(intern, String.format(((String) objArr2[0]).intern(), i()));
                                if (!a(context)) {
                                    d(c.a);
                                    break;
                                }
                                break;
                        }
                }
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                p((byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 59), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + **********, (short) (Process.myTid() >> 22), (-45) - Color.alpha(0), Gravity.getAbsoluteGravity(0, 0) - 87990370, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                p((byte) (TextUtils.indexOf("", "", 0) + 63), TextUtils.lastIndexOf("", '0', 0) + 1948382393, (short) KeyEvent.normalizeMetaState(0), (-40) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (-87990329) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr4);
                o.ee.g.d(intern2, String.format(((String) objArr4[0]).intern(), i(), j()));
                return;
            default:
                j();
                e(context);
                c cVar = c.c;
                throw null;
        }
    }

    @Override // o.i.g
    final void c(Context context) {
        int i2 = h + 85;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 7 : 'L') {
            case 7:
                super.c(context);
                a(context);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                super.c(context);
                switch (!a(context)) {
                    case true:
                        int i3 = h + 85;
                        f = i3 % 128;
                        int i4 = i3 % 2;
                        d(c.a);
                        return;
                    default:
                        return;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x00b2, code lost:
    
        if (r2 != null) goto L38;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 904
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.k.p(byte, int, short, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void q(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1000
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.k.q(char, int, int, java.lang.Object[]):void");
    }
}
