package o.dt;

import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.dr.a;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dt\b.smali */
final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static char[] b;
    private static boolean c;
    private static int d;
    private static int e;
    private static int f;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        f = 1;
        c();
        ViewConfiguration.getJumpTapTimeout();
        int i = d + 27;
        f = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        b = new char[]{61772, 61801, 61807, 61784, 61818, 61803, 61814, 61817, 61816, 61811, 61813, 61783, 61804, 61806, 61800, 61769, 61732, 61751, 61808, 61823, 61821, 61762};
        c = true;
        a = true;
        e = 782102788;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = 121 - r7
            byte[] r0 = o.dt.b.$$a
            int r9 = r9 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r7 = r8
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L32
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r7
            int r9 = r9 + 1
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r9]
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L32:
            int r8 = -r8
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dt.b.h(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{17, -116, 103, 33};
        $$b = Opcodes.MONITORENTER;
    }

    b() {
    }

    static a b(a aVar, List<a> list) {
        Iterator<a> it = list.iterator();
        int i = d + 95;
        f = i % 128;
        int i2 = i % 2;
        a aVar2 = null;
        int i3 = 0;
        while (true) {
            switch (it.hasNext() ? '5' : 'S') {
                case Opcodes.SALOAD /* 53 */:
                    int i4 = d + 23;
                    f = i4 % 128;
                    int i5 = i4 % 2;
                    a next = it.next();
                    int d2 = d(aVar, next);
                    switch (d2 == 100 ? (char) 3 : (char) 21) {
                        case 21:
                            switch (d2 != 0) {
                                case true:
                                    if (d2 > i3) {
                                        aVar2 = next;
                                        i3 = d2;
                                    }
                                    int i6 = f + Opcodes.DREM;
                                    d = i6 % 128;
                                    int i7 = i6 % 2;
                                    break;
                            }
                            break;
                        default:
                            int i8 = d + 81;
                            f = i8 % 128;
                            if (i8 % 2 == 0) {
                            }
                            aVar2 = next;
                            i3 = 100;
                            break;
                    }
            }
        }
        if (aVar2 == null) {
            g.c();
            Object[] objArr = new Object[1];
            g(null, 127 - Drawable.resolveOpacity(0, 0), null, "\u0085\u0083\u008d\u0082\u0089\u0086\u008c\u0087\u008b\u008a\u0089\u0082\u0086\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            g(null, 126 - TextUtils.lastIndexOf("", '0', 0), null, "\u008f\u0087\u0094\u008b\u008e\u0091\u0083\u0089\u0086\u008f\u008a\u008f\u0087\u0086\u0082\u0091\u0087\u008b\u008a\u0089\u0082\u0086\u0088\u0087\u0086\u0085\u0089\u0091\u0093\u0086\u0082\u008b\u0093\u0091\u008b\u0087\u0091\u0092\u0091\u0083\u0089\u0086\u008f\u008a\u008f\u0087\u0086\u0090\u008f\u0087\u008a\u008e", objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            return null;
        }
        g.c();
        Object[] objArr3 = new Object[1];
        g(null, 127 - Drawable.resolveOpacity(0, 0), null, "\u0085\u0083\u008d\u0082\u0089\u0086\u008c\u0087\u008b\u008a\u0089\u0082\u0086\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        g(null, TextUtils.getOffsetBefore("", 0) + 127, null, "\u0091\u0096\u0091\u0083\u0085\u008b\u0082\u0088\u0091\u008d\u0089\u008a\u0095\u0091\u008f\u0087\u0094\u008b\u008e\u0091\u0083\u0089\u0086\u008f\u008a\u008f\u0087\u0086\u0082\u0091\u0087\u008b\u008a\u0089\u0082\u0086\u0088\u0087\u0086\u0085\u0089\u0091\u0093\u0086\u0082\u008b\u0093\u0091\u0092\u0091\u0083\u0089\u0086\u008f\u008a\u008f\u0087\u0086\u0090\u008f\u0087\u008a\u008e", objArr4);
        g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(i3).toString());
        return aVar2;
    }

    private static int d(a aVar, a aVar2) {
        if (aVar.u() != null) {
            switch (aVar2.u() != null ? Typography.amp : 'b') {
                case '&':
                    if (aVar.u().equals(aVar2.u())) {
                        if (aVar2.K() != null) {
                            switch (aVar.K() != null ? 'X' : '_') {
                                case Opcodes.SWAP /* 95 */:
                                    break;
                                default:
                                    int i = f + 33;
                                    d = i % 128;
                                    int i2 = i % 2;
                                    switch (aVar2.K().equals(aVar.K()) ? '\'' : Typography.less) {
                                        case '<':
                                            break;
                                        default:
                                            int i3 = d + 43;
                                            f = i3 % 128;
                                            int i4 = i3 % 2;
                                            return 100;
                                    }
                            }
                        }
                        if (aVar2.A() != 0 && aVar.A() != 0) {
                            int i5 = d + 81;
                            f = i5 % 128;
                            int i6 = i5 % 2;
                            if (aVar2.A() == aVar.A()) {
                                int i7 = f + 3;
                                d = i7 % 128;
                                switch (i7 % 2 == 0) {
                                    case false:
                                        return 33;
                                    default:
                                        return 100;
                                }
                            }
                        }
                        if (aVar.d() != null) {
                            int i8 = d + 17;
                            f = i8 % 128;
                            switch (i8 % 2 == 0 ? (char) 27 : 'A') {
                                case 27:
                                    aVar.p();
                                    throw null;
                                default:
                                    if (aVar.p() != null) {
                                        aVar.q();
                                        if (aVar2.d() != null) {
                                            switch (aVar2.p() != null) {
                                                case false:
                                                    break;
                                                default:
                                                    int i9 = f + 67;
                                                    d = i9 % 128;
                                                    if (i9 % 2 != 0) {
                                                        aVar2.q();
                                                        aVar.d().compareTo(aVar2.d());
                                                        throw null;
                                                    }
                                                    aVar2.q();
                                                    if (aVar.d().compareTo(aVar2.d()) != 0 || aVar.p() != aVar2.p()) {
                                                        int i10 = f + 65;
                                                        d = i10 % 128;
                                                        int i11 = i10 % 2;
                                                        return 0;
                                                    }
                                                    switch (Math.abs((aVar.q().getTime() - aVar2.q().getTime()) / 1000) > 172800 ? 'G' : '!') {
                                                        case '!':
                                                            return (int) (((172800 - r6) / 172800.0d) * 100.0d);
                                                        default:
                                                            int i12 = d + 47;
                                                            f = i12 % 128;
                                                            int i13 = i12 % 2;
                                                            return 0;
                                                    }
                                            }
                                        }
                                    }
                                    break;
                            }
                        }
                        return 0;
                    }
                    break;
                default:
                    return 0;
            }
        }
        return 0;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 796
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dt.b.g(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
