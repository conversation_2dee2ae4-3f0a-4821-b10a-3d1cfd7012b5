package o.df;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import androidx.work.BackoffPolicy;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\df\c.smali */
public final class c implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        e();
        ViewConfiguration.getWindowTouchSlop();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        Color.rgb(0, 0, 0);
        int i = b + 9;
        c = i % 128;
        switch (i % 2 != 0 ? 'A' : 'X') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void e() {
        e = 874635490;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 107
            int r7 = r7 * 3
            int r7 = 4 - r7
            byte[] r0 = o.df.c.$$a
            int r6 = r6 * 4
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r6) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r4 = r0[r7]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r8 = r8 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.c.g(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{1, 25, 123, 58};
        $$b = 206;
    }

    public static boolean d(o.bb.d dVar) {
        switch (!dVar.b() ? 'B' : '#') {
            case 'B':
                int i = c + 63;
                b = i % 128;
                int i2 = i % 2;
                switch (dVar.d() == o.bb.a.t) {
                    case false:
                        break;
                    default:
                        return true;
                }
        }
        int i3 = b + 71;
        c = i3 % 128;
        int i4 = i3 % 2;
        return false;
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0024, code lost:
    
        if (r4 == o.bb.a.t) goto L18;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x002d, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x002b, code lost:
    
        if (r4 == o.bb.a.t) goto L18;
     */
    @Override // o.df.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(o.bb.d r4, o.de.b r5) {
        /*
            r3 = this;
            boolean r5 = r4.b()
            r0 = 1
            r1 = 0
            if (r5 != 0) goto La
            r5 = r1
            goto Lb
        La:
            r5 = r0
        Lb:
            switch(r5) {
                case 0: goto Lf;
                default: goto Le;
            }
        Le:
            goto L2e
        Lf:
            int r5 = o.df.c.c
            int r5 = r5 + 29
            int r2 = r5 % 128
            o.df.c.b = r2
            int r5 = r5 % 2
            o.bb.a r4 = r4.d()
            if (r5 != 0) goto L29
            o.bb.a r5 = o.bb.a.t
            r2 = 43
            int r2 = r2 / r1
            if (r4 != r5) goto Le
            goto L2d
        L27:
            r4 = move-exception
            throw r4
        L29:
            o.bb.a r5 = o.bb.a.t
            if (r4 != r5) goto Le
        L2d:
            return r0
        L2e:
            int r4 = o.df.c.b
            int r4 = r4 + 79
            int r5 = r4 % 128
            o.df.c.c = r5
            int r4 = r4 % 2
            if (r4 == 0) goto L3b
            goto L3c
        L3b:
            r0 = r1
        L3c:
            switch(r0) {
                case 0: goto L40;
                default: goto L3f;
            }
        L3f:
            goto L41
        L40:
            return r1
        L41:
            r4 = 0
            r4.hashCode()     // Catch: java.lang.Throwable -> L46
            throw r4     // Catch: java.lang.Throwable -> L46
        L46:
            r4 = move-exception
            throw r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.c.a(o.bb.d, o.de.b):boolean");
    }

    @Override // o.df.e
    public final BackoffPolicy b() {
        BackoffPolicy backoffPolicy;
        int i = c + Opcodes.LSUB;
        b = i % 128;
        switch (i % 2 == 0) {
            case true:
                backoffPolicy = BackoffPolicy.LINEAR;
                int i2 = 75 / 0;
                break;
            default:
                backoffPolicy = BackoffPolicy.LINEAR;
                break;
        }
        int i3 = b + 95;
        c = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return backoffPolicy;
            default:
                throw null;
        }
    }

    @Override // o.df.e
    public final int d() {
        int i = b;
        int i2 = i + 25;
        c = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + Opcodes.LUSHR;
        c = i4 % 128;
        int i5 = i4 % 2;
        return 30;
    }

    @Override // o.df.e
    public final int d(int i) {
        int i2 = c;
        int i3 = i2 + 39;
        b = i3 % 128;
        int i4 = i3 % 2;
        int i5 = i2 + 35;
        b = i5 % 128;
        switch (i5 % 2 != 0) {
            case true:
                return 30;
            default:
                int i6 = 2 / 0;
                return 30;
        }
    }

    @Override // o.df.e
    public final String a() {
        Object obj;
        int i = c + Opcodes.LSHR;
        b = i % 128;
        switch (i % 2 == 0 ? '#' : (char) 2) {
            case '#':
                Object[] objArr = new Object[1];
                f(1 >>> (ViewConfiguration.getScrollBarSize() + 58), "\u0012\uffde\u0011\t\ufffe￼\r\u0002\u0007\u0000￬\u0002\u0006ￜ\ufffa\u000b�￮\u0007\u0005\b￼\u0004￫\ufffe\r\u000b\u0012￬\r\u000b\ufffa\r\ufffe\u0000", 10 / TextUtils.getOffsetBefore("", 0), 7944 >> KeyEvent.normalizeMetaState(0), false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f((ViewConfiguration.getScrollBarSize() >> 8) + 1, "\u0012\uffde\u0011\t\ufffe￼\r\u0002\u0007\u0000￬\u0002\u0006ￜ\ufffa\u000b�￮\u0007\u0005\b￼\u0004￫\ufffe\r\u000b\u0012￬\r\u000b\ufffa\r\ufffe\u0000", 35 - TextUtils.getOffsetBefore("", 0), KeyEvent.normalizeMetaState(0) + 260, false, objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.df.e
    public final f c(o.bb.d dVar) {
        int i = c + 77;
        b = i % 128;
        int i2 = i % 2;
        f fVar = f.s;
        int i3 = c + 47;
        b = i3 % 128;
        int i4 = i3 % 2;
        return fVar;
    }

    @Override // o.df.e
    public final boolean b(f fVar) {
        boolean z;
        switch (fVar == f.s ? '[' : (char) 19) {
            case Opcodes.DUP_X2 /* 91 */:
                int i = b + 57;
                int i2 = i % 128;
                c = i2;
                switch (i % 2 != 0 ? '8' : '2') {
                    case '2':
                        z = true;
                        break;
                    default:
                        z = false;
                        break;
                }
                int i3 = i2 + 11;
                b = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        int i4 = 49 / 0;
                        return z;
                    default:
                        return z;
                }
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r20, java.lang.String r21, int r22, int r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 638
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.c.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
