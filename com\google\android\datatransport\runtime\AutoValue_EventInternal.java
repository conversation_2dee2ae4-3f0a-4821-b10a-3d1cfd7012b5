package com.google.android.datatransport.runtime;

import com.google.android.datatransport.runtime.EventInternal;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\AutoValue_EventInternal.smali */
final class AutoValue_EventInternal extends EventInternal {
    private final Map<String, String> autoMetadata;
    private final Integer code;
    private final EncodedPayload encodedPayload;
    private final long eventMillis;
    private final String transportName;
    private final long uptimeMillis;

    private AutoValue_EventInternal(String transportName, Integer code, EncodedPayload encodedPayload, long eventMillis, long uptimeMillis, Map<String, String> autoMetadata) {
        this.transportName = transportName;
        this.code = code;
        this.encodedPayload = encodedPayload;
        this.eventMillis = eventMillis;
        this.uptimeMillis = uptimeMillis;
        this.autoMetadata = autoMetadata;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    public String getTransportName() {
        return this.transportName;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    public Integer getCode() {
        return this.code;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    public EncodedPayload getEncodedPayload() {
        return this.encodedPayload;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    public long getEventMillis() {
        return this.eventMillis;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    public long getUptimeMillis() {
        return this.uptimeMillis;
    }

    @Override // com.google.android.datatransport.runtime.EventInternal
    protected Map<String, String> getAutoMetadata() {
        return this.autoMetadata;
    }

    public String toString() {
        return "EventInternal{transportName=" + this.transportName + ", code=" + this.code + ", encodedPayload=" + this.encodedPayload + ", eventMillis=" + this.eventMillis + ", uptimeMillis=" + this.uptimeMillis + ", autoMetadata=" + this.autoMetadata + "}";
    }

    public boolean equals(Object o2) {
        Integer num;
        if (o2 == this) {
            return true;
        }
        if (!(o2 instanceof EventInternal)) {
            return false;
        }
        EventInternal that = (EventInternal) o2;
        return this.transportName.equals(that.getTransportName()) && ((num = this.code) != null ? num.equals(that.getCode()) : that.getCode() == null) && this.encodedPayload.equals(that.getEncodedPayload()) && this.eventMillis == that.getEventMillis() && this.uptimeMillis == that.getUptimeMillis() && this.autoMetadata.equals(that.getAutoMetadata());
    }

    public int hashCode() {
        int h$ = 1 * 1000003;
        int h$2 = (h$ ^ this.transportName.hashCode()) * 1000003;
        Integer num = this.code;
        int h$3 = (((h$2 ^ (num == null ? 0 : num.hashCode())) * 1000003) ^ this.encodedPayload.hashCode()) * 1000003;
        long j = this.eventMillis;
        int h$4 = (h$3 ^ ((int) (j ^ (j >>> 32)))) * 1000003;
        long j2 = this.uptimeMillis;
        return ((h$4 ^ ((int) (j2 ^ (j2 >>> 32)))) * 1000003) ^ this.autoMetadata.hashCode();
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\AutoValue_EventInternal$Builder.smali */
    static final class Builder extends EventInternal.Builder {
        private Map<String, String> autoMetadata;
        private Integer code;
        private EncodedPayload encodedPayload;
        private Long eventMillis;
        private String transportName;
        private Long uptimeMillis;

        Builder() {
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal.Builder setTransportName(String transportName) {
            if (transportName == null) {
                throw new NullPointerException("Null transportName");
            }
            this.transportName = transportName;
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal.Builder setCode(Integer code) {
            this.code = code;
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal.Builder setEncodedPayload(EncodedPayload encodedPayload) {
            if (encodedPayload == null) {
                throw new NullPointerException("Null encodedPayload");
            }
            this.encodedPayload = encodedPayload;
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal.Builder setEventMillis(long eventMillis) {
            this.eventMillis = Long.valueOf(eventMillis);
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal.Builder setUptimeMillis(long uptimeMillis) {
            this.uptimeMillis = Long.valueOf(uptimeMillis);
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        protected EventInternal.Builder setAutoMetadata(Map<String, String> autoMetadata) {
            if (autoMetadata == null) {
                throw new NullPointerException("Null autoMetadata");
            }
            this.autoMetadata = autoMetadata;
            return this;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        protected Map<String, String> getAutoMetadata() {
            Map<String, String> map = this.autoMetadata;
            if (map == null) {
                throw new IllegalStateException("Property \"autoMetadata\" has not been set");
            }
            return map;
        }

        @Override // com.google.android.datatransport.runtime.EventInternal.Builder
        public EventInternal build() {
            String missing = this.transportName == null ? " transportName" : "";
            if (this.encodedPayload == null) {
                missing = missing + " encodedPayload";
            }
            if (this.eventMillis == null) {
                missing = missing + " eventMillis";
            }
            if (this.uptimeMillis == null) {
                missing = missing + " uptimeMillis";
            }
            if (this.autoMetadata == null) {
                missing = missing + " autoMetadata";
            }
            if (!missing.isEmpty()) {
                throw new IllegalStateException("Missing required properties:" + missing);
            }
            return new AutoValue_EventInternal(this.transportName, this.code, this.encodedPayload, this.eventMillis.longValue(), this.uptimeMillis.longValue(), this.autoMetadata);
        }
    }
}
