package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\s0.smali */
public class s0 extends u {
    private w b;
    private h x;

    public s0(w wVar, h hVar) {
        this.b = wVar;
        this.x = hVar;
    }

    public static s0 a(Object obj) {
        if (obj instanceof s0) {
            return (s0) obj;
        }
        if (obj != null) {
            return new s0(e0.a(obj));
        }
        return null;
    }

    public w e() {
        return this.b;
    }

    public h f() {
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(this.b);
        h hVar = this.x;
        if (hVar != null) {
            iVar.a(hVar);
        }
        return new j2(iVar);
    }

    private s0(e0 e0Var) {
        if (e0Var.size() >= 1 && e0Var.size() <= 2) {
            this.b = w.a(e0Var.a(0));
            if (e0Var.size() == 2) {
                this.x = e0Var.a(1);
                return;
            } else {
                this.x = null;
                return;
            }
        }
        throw new IllegalArgumentException("Bad sequence size: " + e0Var.size());
    }
}
