package o.dk;

import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import kotlin.io.encoding.Base64;
import o.a.l;
import o.e.a;
import o.ej.d;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dk\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static boolean a;
    private static char[] b;
    private static boolean c;
    private static char[] d;
    private static int e;
    private static int g;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        j = 1;
        c();
        SystemClock.elapsedRealtimeNanos();
        int i = g + 93;
        j = i % 128;
        int i2 = i % 2;
    }

    static void c() {
        d = new char[]{61568, 61821, 61598, 61630, 61635, 61617, 61590, 61626, 61628, 61581, 61569, 61574, 61570};
        a = true;
        c = true;
        e = 782102862;
        b = new char[]{50823, 50777, 50778, 50778, 50772, 50778, 50773, 50775, 50761, 50759, 50771, 50797, 50797, 50755, 50755, 50770, 50775, 50877, 50841, 50841, 50876, 50775, 50771, 50797, 50797, 50875, 50838, 50838, 50845, 50871, 50874, 50841, 50841, 50875, 50771, 50770, 50775, 50877, 50838, 50838, 50845, 50871, 50846, 50768, 50820, 50774, 50769, 50777, 50759, 50781, 50796, 50769, 50879, 50843, 50843, 50879, 50772, 50796, 50870, 50877, 50773, 50797, 50869, 50832, 50832, 50847, 50865, 50868, 50843, 50843, 50847, 50865, 50768, 50779, 50772, 50772, 50774, 50772, 50775, 50769, 50758, 50758, 50769, 50798, 50798, 50768, 50861, 50811, 50805, 50700, 50760, 51151, 50902, 50938};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dk.e.$$a
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r7 = 122 - r7
            int r9 = r9 * 2
            int r9 = 4 - r9
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r9]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L36:
            int r9 = -r9
            int r8 = r8 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.e.i(int, short, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{0, -16, -96, 75};
        $$b = Opcodes.F2L;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static byte[] e(o.eo.d r11) {
        /*
            Method dump skipped, instructions count: 320
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.e.e(o.eo.d):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static byte[] a(byte[] r13, byte[] r14) {
        /*
            Method dump skipped, instructions count: 368
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.e.a(byte[], byte[]):byte[]");
    }

    private static byte[] d(byte[]... bArr) {
        int i = g + 39;
        j = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        h("\u0000\u0001\u0000\u0000", new int[]{86, 4, Opcodes.LUSHR, 0}, true, objArr);
        byte[] d2 = d(d.e(((String) objArr[0]).intern(), bArr));
        int i3 = j + 89;
        g = i3 % 128;
        switch (i3 % 2 != 0 ? 'L' : '!') {
            case Base64.mimeLineLength /* 76 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return d2;
        }
    }

    public static byte[] d(byte[] bArr) {
        int i = g + 17;
        j = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(null, (Process.myTid() >> 22) + 127, null, "\u008b\u008a", objArr);
        byte[] e2 = d.e(((String) objArr[0]).intern(), bArr);
        Object[] objArr2 = new Object[1];
        f(null, AndroidCharacter.getMirror('0') + 'O', null, "\u008d\u008c", objArr2);
        byte[] e3 = d.e(((String) objArr2[0]).intern(), c.e());
        Object[] objArr3 = new Object[1];
        h("\u0000\u0000", new int[]{90, 2, 196, 1}, false, objArr3);
        byte[] e4 = d.e(((String) objArr3[0]).intern(), e3, e2);
        int i3 = j + 33;
        g = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return e4;
            default:
                int i4 = 65 / 0;
                return e4;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:51:0x00d2, code lost:
    
        continue;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static short d(byte[] r13, byte[] r14) {
        /*
            Method dump skipped, instructions count: 264
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.e.d(byte[], byte[]):short");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r17, int r18, int[] r19, java.lang.String r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 802
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.e.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }

    private static void h(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        int i;
        char[] cArr2;
        int i2;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        char c2 = 0;
        int i3 = iArr[0];
        int i4 = 1;
        int i5 = iArr[1];
        int i6 = 2;
        int i7 = iArr[2];
        int i8 = iArr[3];
        char[] cArr3 = b;
        float f = 0.0f;
        switch (cArr3 != null ? 'T' : '(') {
            case Opcodes.BASTORE /* 84 */:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i9 = 0;
                while (i9 < length) {
                    int i10 = $10 + 21;
                    $11 = i10 % 128;
                    int i11 = i10 % i6;
                    try {
                        Object[] objArr2 = new Object[i4];
                        objArr2[c2] = Integer.valueOf(cArr3[i9]);
                        Object obj = a.s.get(1951085128);
                        if (obj != null) {
                            cArr = cArr3;
                        } else {
                            Class cls = (Class) a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 11, (char) (KeyEvent.getMaxKeyCode() >> 16), 43 - (PointF.length(f, f) > f ? 1 : (PointF.length(f, f) == f ? 0 : -1)));
                            byte b2 = $$a[c2];
                            cArr = cArr3;
                            Object[] objArr3 = new Object[1];
                            i((byte) i6, b2, b2, objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            a.s.put(1951085128, obj);
                        }
                        cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i9++;
                        cArr3 = cArr;
                        c2 = 0;
                        i4 = 1;
                        i6 = 2;
                        f = 0.0f;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr4;
                break;
        }
        char[] cArr5 = new char[i5];
        System.arraycopy(cArr3, i3, cArr5, 0, i5);
        if (bArr2 != null) {
            int i12 = $11 + 23;
            $10 = i12 % 128;
            if (i12 % 2 != 0) {
                cArr2 = new char[i5];
                i2 = 1;
            } else {
                cArr2 = new char[i5];
                i2 = 0;
            }
            lVar.d = i2;
            char c3 = 0;
            while (lVar.d < i5) {
                switch (bArr2[lVar.d] == 1) {
                    case false:
                        int i13 = lVar.d;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c3)};
                            Object obj2 = a.s.get(804049217);
                            if (obj2 == null) {
                                Class cls2 = (Class) a.c(9 - MotionEvent.axisFromString(""), (char) TextUtils.indexOf("", ""), 207 - TextUtils.getOffsetAfter("", 0));
                                byte b3 = $$a[0];
                                byte b4 = b3;
                                Object[] objArr5 = new Object[1];
                                i(b3, b4, b4, objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                a.s.put(804049217, obj2);
                            }
                            cArr2[i13] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                            break;
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    default:
                        int i14 = lVar.d;
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c3)};
                            Object obj3 = a.s.get(2016040108);
                            if (obj3 == null) {
                                Class cls3 = (Class) a.c(View.resolveSize(0, 0) + 11, (char) View.getDefaultSize(0, 0), (Process.myPid() >> 22) + 448);
                                byte b5 = $$a[0];
                                Object[] objArr7 = new Object[1];
                                i((byte) 3, b5, b5, objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                a.s.put(2016040108, obj3);
                            }
                            cArr2[i14] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                }
                c3 = cArr2[lVar.d];
                try {
                    Object[] objArr8 = {lVar, lVar};
                    Object obj4 = a.s.get(-2112603350);
                    if (obj4 == null) {
                        Class cls4 = (Class) a.c(11 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (char) ((Process.getThreadPriority(0) + 20) >> 6), TextUtils.lastIndexOf("", '0') + 260);
                        byte b6 = $$a[0];
                        Object[] objArr9 = new Object[1];
                        i((byte) 56, b6, b6, objArr9);
                        obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                        a.s.put(-2112603350, obj4);
                    }
                    ((Method) obj4).invoke(null, objArr8);
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
            cArr5 = cArr2;
        }
        if (i8 > 0) {
            char[] cArr6 = new char[i5];
            System.arraycopy(cArr5, 0, cArr6, 0, i5);
            int i15 = i5 - i8;
            System.arraycopy(cArr6, 0, cArr5, i15, i8);
            System.arraycopy(cArr6, i8, cArr5, 0, i15);
        }
        if (z) {
            char[] cArr7 = new char[i5];
            int i16 = 0;
            while (true) {
                lVar.d = i16;
                if (lVar.d < i5) {
                    cArr7[lVar.d] = cArr5[(i5 - lVar.d) - 1];
                    i16 = lVar.d + 1;
                } else {
                    cArr5 = cArr7;
                }
            }
        }
        if (i7 > 0) {
            lVar.d = 0;
            while (true) {
                switch (lVar.d < i5) {
                    case false:
                        break;
                    default:
                        int i17 = $11 + 53;
                        $10 = i17 % 128;
                        switch (i17 % 2 != 0) {
                            case false:
                                cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                                i = lVar.d + 1;
                                break;
                            default:
                                cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                                i = lVar.d / 0;
                                break;
                        }
                        lVar.d = i;
                        int i18 = $10 + 57;
                        $11 = i18 % 128;
                        int i19 = i18 % 2;
                }
            }
        }
        objArr[0] = new String(cArr5);
    }
}
