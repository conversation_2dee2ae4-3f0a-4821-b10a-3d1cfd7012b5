package fr.antelop.sdk.exception;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\exception\WalletValidationDomain.smali */
public final class WalletValidationDomain {
    public static final String ACTIVATION_CODE = "ActivationCode";
    public static final String ADDRESS = "Address";
    public static final String APPLE_PAY_SERVICE = "ApplePayService";
    public static final String CARD = "Card";
    public static final String CARD_DISPLAY_SERVICE = "CardDisplayService";
    public static final String CARD_SECURE_DISPLAY = "CardSecureDisplay";
    public static final String CARD_SECURE_INPUT = "CardSecureInput";
    public static final String CLIENT_ID = "ClientId";
    public static final String CMS_ACTIVATION_SERVICE = "CmsActivationService";
    public static final String CONTEXT = "Context";
    public static final String CREATE_CARD_REQUEST_BUILDER = "CreateCardRequestBuilder";
    public static final String CUSTOMER_AUTHENTICATED_PROCESS = "CustomerAuthenticatedProcess";
    public static final String CUSTOMER_AUTHENTICATED_PROCESS_CALLBACK = "CustomerAuthenticatedProcessCallback";
    public static final String CUSTOMER_AUTHENTICATED_SIGNATURE = "CustomerAuthenticatedSignature";
    public static final String CUSTOMER_AUTHENTICATION_CREDENTIALS = "CustomerAuthenticationCredentials";
    public static final String CUSTOMER_AUTHENTICATION_METHOD = "CustomerAuthenticationMethod";
    public static final String CUSTOMER_AUTHENTICATION_PATTERN = "CustomerAuthenticationPattern";
    public static final String CUSTOMER_AUTHENTICATION_PROMPT = "CustomerAuthenticationPrompt";
    public static final String DIGITAL_CARD = "DigitalCard";
    public static final String DIGITAL_CARD_ENROLLMENT_DATA = "DigitalCardEnrollmentData";
    public static final String EMV_APPLICATION = "EmvApplication";
    public static final String EMV_APPLICATION_ACTIVATION_METHOD = "EmvApplicationActivationMethod";
    public static final String GOOGLE_PAY_SERVICE = "GooglePayService";
    public static final String GROUP = "Group";
    public static final String ISSUER_ACTIVATION_CODE = "IssuerActivationCode";
    public static final String ISSUER_ACTIVATION_ID = "IssuerActivationId";
    public static final String MSISDN = "Msisdn";
    public static final String PIN_DISPLAY_SERVICE = "PinDisplayService";
    public static final String PIN_SECURE_DISPLAY = "PinSecureDisplay";
    public static final String PIN_UPDATE_SERVICE = "PinUpdateService";
    public static final String PRODUCT = "Product";
    public static final String PUSH_AUTHENTICATION_REQUEST = "PushAuthenticationRequest";
    public static final String RETURN_URL = "ReturnUrl";
    public static final String SAMSUNG_PAY_SERVICE = "SamsungPayService";
    public static final String SECURE_CARD_DISPLAY = "SecureDisplay";
    public static final String SECURE_CARD_PUSH_TO_GOOGLE_PAY = "SecureCardPushToGooglePay";
    public static final String SECURE_CARD_PUSH_TO_ISSUER_NFC_WALLET = "SecureCardPushToIssuerNfcWallet";
    public static final String SECURE_CARD_PUSH_TO_SAMSUNG_PAY = "SecureCardPushToSamsungPay";
    public static final String SECURE_CARD_PUSH_TO_TOKEN_REQUESTOR = "SecureCardPushToTokenRequestor";
    public static final String SECURE_PIN_INPUT = "SecurePinInput";
    public static final String SECURE_TOKEN_DELETE = "SecureTokenDelete";
    public static final String SECURE_TOKEN_RESUME = "SecureTokenResume";
    public static final String SECURE_TOKEN_SUSPEND = "SecureTokenSuspend";
    public static final String SETTINGS_PROFILE_ID = "SettingsProfileId";
    public static final String TOKEN = "Token";
    public static final String TOKEN_MANAGEMENT_SERVICE = "TokenManagementService";
    public static final String TRANSACTION_CONTROL = "transactionControl";
    public static final String TRANSACTION_CONTROL_SERVICE = "transactionControlService";
    public static final String VIRTUAL_CARD_NUMBER = "VirtualCardNumber";
    public static final String VIRTUAL_CARD_NUMBER_OPTION = "VirtualCardNumberOption";
    public static final String VIRTUAL_CARD_NUMBER_SERVICE = "VirtualCardNumberService";
    public static final String WALLET = "Wallet";
    public static final String WALLET_ID = "WalletId";
    public static final String WALLET_LOCK_REASON = "WalletLockReason";
    public static final String WALLET_MANAGER = "WalletManager";
    public static final String WALLET_PROVISIONING = "WalletProvisioning";
}
