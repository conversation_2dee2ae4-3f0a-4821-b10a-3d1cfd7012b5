package o.ee;

import android.content.Context;
import android.widget.Toast;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\l.smali */
public final class l {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static final l b;
    private static int c;
    private static char[] e;
    private boolean d = false;

    static void d() {
        e = new char[]{50908, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50926, 50909, 50925, 50925, 50925, 50924, 50924, 50924, 50924, 50924, 50925, 50925, 50925, 50925, 50941, 50826, 50844, 50817, 50821, 50816, 50818, 50933, 50932, 50822, 50826, 50936, 50942, 50823, 50845, 50932, 50943, 50825, 50830, 50822, 50819, 50934, 50932, 50825, 50816, 50934, 50933, 50847, 50846, 50844, 50818, 50844, 50817, 50943, 50925, 50821, 50779, 50768, 50771, 50760, 50761, 50773, 50764, 50866, 50772, 50796, 50762, 50762, 50771, 50774, 50761, 50761, 50779, 50866, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50851, 50851, 50851, 50851, 50851, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50848, 50761, 50774, 50772, 50774, 50773, 50772, 50696, 50798, 50696, 50703, 50772, 50805, 50804, 50809, 50795, 50696, 50805, 50702, 50802, 50805, 50774, 50810, 50703};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = 122 - r7
            byte[] r0 = o.ee.l.$$a
            int r6 = r6 * 3
            int r6 = 3 - r6
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.l.g(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -116, 4, 37};
        $$b = 196;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        d();
        b = new l();
        int i = c + 71;
        a = i % 128;
        int i2 = i % 2;
    }

    private l() {
    }

    public static void d(Context context) {
        int i = a;
        int i2 = i + Opcodes.DMUL;
        c = i2 % 128;
        int i3 = i2 % 2;
        l lVar = b;
        if (!lVar.d) {
            lVar.c(context);
            int i4 = c + Opcodes.LNEG;
            a = i4 % 128;
            int i5 = i4 % 2;
            return;
        }
        int i6 = i + 95;
        c = i6 % 128;
        switch (i6 % 2 != 0) {
            case true:
                int i7 = 11 / 0;
                return;
            default:
                return;
        }
    }

    private void c(Context context) {
        int i = a;
        int i2 = i + 71;
        c = i2 % 128;
        int i3 = i2 % 2;
        Object obj = null;
        if (!this.d) {
            boolean z = true;
            this.d = true;
            g.c();
            Object[] objArr = new Object[1];
            f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{0, 48, 0, 0}, false, objArr);
            g.d(((String) objArr[0]).intern());
            Object[] objArr2 = new Object[1];
            f("\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000", new int[]{48, 48, 0, 6}, false, objArr2);
            g.d(((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f("\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000", new int[]{96, 48, 77, 30}, true, objArr3);
            g.d(((String) objArr3[0]).intern());
            Object[] objArr4 = new Object[1];
            f("\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{0, 48, 0, 0}, false, objArr4);
            g.d(((String) objArr4[0]).intern());
            Toast.makeText(context, R.string.antelopDebugToastMessage, 1).show();
            int i4 = c + 73;
            a = i4 % 128;
            if (i4 % 2 == 0) {
                z = false;
            }
            switch (z) {
                case true:
                    return;
                default:
                    throw null;
            }
        }
        int i5 = i + 41;
        c = i5 % 128;
        switch (i5 % 2 != 0 ? Typography.greater : 'a') {
            case Opcodes.LADD /* 97 */:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x010a, code lost:
    
        if (r0[r1.d] == 1) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x01a2, code lost:
    
        r6 = r1.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x01a9, code lost:
    
        r12 = new java.lang.Object[]{java.lang.Integer.valueOf(r2[r1.d]), java.lang.Integer.valueOf(r4)};
        r4 = o.e.a.s.get(804049217);
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x01c6, code lost:
    
        if (r4 == null) goto L66;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0220, code lost:
    
        r3[r6] = ((java.lang.Character) ((java.lang.reflect.Method) r4).invoke(null, r12)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0222, code lost:
    
        r4 = r3[r1.d];
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x0226, code lost:
    
        r6 = new java.lang.Object[]{r1, r1};
        r9 = o.e.a.s.get(-2112603350);
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0237, code lost:
    
        if (r9 == null) goto L73;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x0284, code lost:
    
        ((java.lang.reflect.Method) r9).invoke(null, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x023a, code lost:
    
        r9 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getTouchSlop() >> 8) + 11, (char) ((-1) - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0')), android.text.AndroidCharacter.getMirror('0') + 211);
        r14 = (byte) 0;
        r10 = new java.lang.Object[1];
        g(r14, (byte) (r14 | 56), r14, r10);
        r9 = r9.getMethod((java.lang.String) r10[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-2112603350, r9);
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x028c, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x028d, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0291, code lost:
    
        if (r1 != null) goto L79;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x0293, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:41:0x0294, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x01c9, code lost:
    
        r4 = (java.lang.Class) o.e.a.c(9 - ((byte) android.view.KeyEvent.getModifierMetaStateMask()), (char) android.view.KeyEvent.normalizeMetaState(0), (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 207);
        r13 = (byte) 0;
        r14 = r13;
        r10 = new java.lang.Object[1];
        g(r13, r14, r14, r10);
        r4 = r4.getMethod((java.lang.String) r10[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(804049217, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x0295, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:45:0x0296, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:46:0x029a, code lost:
    
        if (r1 != null) goto L84;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x029c, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x029d, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x0114, code lost:
    
        r6 = r1.d;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x011b, code lost:
    
        r12 = new java.lang.Object[]{java.lang.Integer.valueOf(r2[r1.d]), java.lang.Integer.valueOf(r4)};
        r4 = o.e.a.s.get(2016040108);
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0138, code lost:
    
        if (r4 == null) goto L54;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0195, code lost:
    
        r3[r6] = ((java.lang.Character) ((java.lang.reflect.Method) r4).invoke(null, r12)).charValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x013b, code lost:
    
        r4 = (java.lang.Class) o.e.a.c(android.view.KeyEvent.getDeadChar(0, 0) + 11, (char) (android.view.ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (android.view.ViewConfiguration.getMaximumFlingVelocity() >> 16) + 448);
        r13 = (byte) 0;
        r10 = (byte) (r13 + 3);
        r9 = new java.lang.Object[1];
        g(r13, r10, (byte) (r10 - 3), r9);
        r4 = r4.getMethod((java.lang.String) r9[0], java.lang.Integer.TYPE, java.lang.Integer.TYPE);
        o.e.a.s.put(2016040108, r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x0199, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x019a, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x019e, code lost:
    
        if (r1 != null) goto L60;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x01a0, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x01a1, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0111, code lost:
    
        if (r0[r1.d] == 0) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x02fd, code lost:
    
        r2 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 874
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.l.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
