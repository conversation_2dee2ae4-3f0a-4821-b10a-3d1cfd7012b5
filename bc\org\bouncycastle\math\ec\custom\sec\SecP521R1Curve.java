package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.AbstractECLookupTable;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.math.ec.ECLookupTable;
import bc.org.bouncycastle.math.ec.ECPoint;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP521R1Curve.smali */
public class SecP521R1Curve extends ECCurve.AbstractFp {
    protected SecP521R1Point i;
    public static final BigInteger q = SecP521R1FieldElement.Q;
    private static final ECFieldElement[] j = {new SecP521R1FieldElement(ECConstants.ONE)};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP521R1Curve$a.smali */
    class a extends AbstractECLookupTable {
        final /* synthetic */ int a;
        final /* synthetic */ int[] b;

        a(int i, int[] iArr) {
            this.a = i;
            this.b = iArr;
        }

        private ECPoint a(int[] iArr, int[] iArr2) {
            return SecP521R1Curve.this.a(new SecP521R1FieldElement(iArr), new SecP521R1FieldElement(iArr2), SecP521R1Curve.j);
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public int getSize() {
            return this.a;
        }

        @Override // bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookup(int i) {
            int[] a = c6.a(17);
            int[] a2 = c6.a(17);
            int i2 = 0;
            for (int i3 = 0; i3 < this.a; i3++) {
                int i4 = ((i3 ^ i) - 1) >> 31;
                for (int i5 = 0; i5 < 17; i5++) {
                    int i6 = a[i5];
                    int[] iArr = this.b;
                    a[i5] = i6 ^ (iArr[i2 + i5] & i4);
                    a2[i5] = a2[i5] ^ (iArr[(i2 + 17) + i5] & i4);
                }
                i2 += 34;
            }
            return a(a, a2);
        }

        @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
        public ECPoint lookupVar(int i) {
            int[] a = c6.a(17);
            int[] a2 = c6.a(17);
            int i2 = i * 17 * 2;
            for (int i3 = 0; i3 < 17; i3++) {
                int i4 = a[i3];
                int[] iArr = this.b;
                a[i3] = i4 ^ iArr[i2 + i3];
                a2[i3] = a2[i3] ^ iArr[(i2 + 17) + i3];
            }
            return a(a, a2);
        }
    }

    public SecP521R1Curve() {
        super(q);
        this.i = new SecP521R1Point(this, null, null);
        this.b = fromBigInteger(new BigInteger(1, z4.a("01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFC")));
        this.c = fromBigInteger(new BigInteger(1, z4.a("0051953EB9618E1C9A1F929A21A0B68540EEA2DA725B99B315F3B8B489918EF109E156193951EC7E937B1652C0BD3BB1BF073573DF883D2C34F1EF451FD46B503F00")));
        this.d = new BigInteger(1, z4.a("01FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFA51868783BF2F966B7FCC0148F709A5D03BB5C9B8899C47AEBB6FB71E91386409"));
        this.e = BigInteger.valueOf(1L);
        this.f = 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECCurve a() {
        return new SecP521R1Curve();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECLookupTable createCacheSafeLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        int[] iArr = new int[i2 * 17 * 2];
        int i3 = 0;
        for (int i4 = 0; i4 < i2; i4++) {
            ECPoint eCPoint = eCPointArr[i + i4];
            c6.b(17, ((SecP521R1FieldElement) eCPoint.getRawXCoord()).a, 0, iArr, i3);
            int i5 = i3 + 17;
            c6.b(17, ((SecP521R1FieldElement) eCPoint.getRawYCoord()).a, 0, iArr, i5);
            i3 = i5 + 17;
        }
        return new a(i2, iArr);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement fromBigInteger(BigInteger bigInteger) {
        return new SecP521R1FieldElement(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public int getFieldSize() {
        return q.bitLength();
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public ECPoint getInfinity() {
        return this.i;
    }

    public BigInteger getQ() {
        return q;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElement(SecureRandom secureRandom) {
        int[] a2 = c6.a(17);
        SecP521R1Field.random(secureRandom, a2);
        return new SecP521R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve.AbstractFp, bc.org.bouncycastle.math.ec.ECCurve
    public ECFieldElement randomFieldElementMult(SecureRandom secureRandom) {
        int[] a2 = c6.a(17);
        SecP521R1Field.randomMult(secureRandom, a2);
        return new SecP521R1FieldElement(a2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    public boolean supportsCoordinateSystem(int i) {
        return i == 2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return new SecP521R1Point(this, eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECCurve
    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        return new SecP521R1Point(this, eCFieldElement, eCFieldElement2, eCFieldElementArr);
    }
}
