package o.fj;

import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.util.Date;
import o.eg.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fj\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        d = 1;
        d();
        ExpandableListView.getPackedPositionType(0L);
        TextUtils.getOffsetAfter("", 0);
        ViewConfiguration.getMaximumDrawingCacheSize();
        View.MeasureSpec.getMode(0);
        ExpandableListView.getPackedPositionChild(0L);
        int i = a + 59;
        d = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        e = 2956549188238155886L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = 71 - r8
            int r9 = r9 * 2
            int r9 = r9 + 1
            byte[] r0 = o.fj.d.$$a
            int r7 = r7 * 2
            int r7 = r7 + 4
            byte[] r1 = new byte[r9]
            int r9 = r9 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L37
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r9) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r5
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L37:
            int r9 = -r9
            int r7 = r7 + r9
            int r8 = r8 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fj.d.f(int, short, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{12, -43, 42, 57};
        $$b = Opcodes.IFLE;
    }

    public static c b(b bVar) throws o.eg.d {
        String q;
        int i = d + 31;
        a = i % 128;
        switch (i % 2 != 0 ? (char) 4 : 'W') {
            case Opcodes.POP /* 87 */:
                Object[] objArr = new Object[1];
                c("\ud9a0\ud9cb䊟쩥ꉋ\u0b7e唀\uf740嬁셊休폳", AndroidCharacter.getMirror('0') - '/', objArr);
                q = bVar.q(((String) objArr[0]).intern());
                switch (q == null ? 'S' : 'A') {
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        break;
                    default:
                        return null;
                }
            default:
                Object[] objArr2 = new Object[1];
                c("\ud9a0\ud9cb䊟쩥ꉋ\u0b7e唀\uf740嬁셊休폳", '5' / AndroidCharacter.getMirror((char) 22), objArr2);
                q = bVar.q(((String) objArr2[0]).intern());
                switch (q == null ? 'B' : '*') {
                    case '*':
                        break;
                    default:
                        return null;
                }
        }
        Object[] objArr3 = new Object[1];
        c("웢욉ʃ째\ue257ࣣﵤ弤䑃腖䲑箛쌘Ћ칺\uf8d7仁諆䄩", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 1, objArr3);
        int intValue = bVar.e(((String) objArr3[0]).intern(), (Integer) 0).intValue();
        Object[] objArr4 = new Object[1];
        c("闒閹ꡃ骦䢗宽\ud825穥ᝳ\u2b96῞廊逪껇鴥\udd87ᷚ\u200bቧ偟", -((byte) KeyEvent.getModifierMetaStateMask()), objArr4);
        Date b = bVar.b(((String) objArr4[0]).intern(), true);
        Object[] objArr5 = new Object[1];
        c("ꆈꇣ㧢\ud9c9\ud936ᣒ\uea7d䠽〈먷岦沏ꑦ㽽\ude5d\uefd5⦬놏儝或\uaafd", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 1, objArr5);
        c cVar = new c(q, intValue, b, bVar.b(((String) objArr5[0]).intern(), true));
        int i2 = a + Opcodes.DNEG;
        d = i2 % 128;
        int i3 = i2 % 2;
        return cVar;
    }

    public static b a(c cVar) throws o.eg.d {
        boolean z;
        boolean z2;
        Object obj;
        b bVar = new b();
        Object[] objArr = new Object[1];
        c("\ud9a0\ud9cb䊟쩥ꉋ\u0b7e唀\uf740嬁셊休폳", 1 - KeyEvent.getDeadChar(0, 0), objArr);
        bVar.d(((String) objArr[0]).intern(), cVar.e());
        Object[] objArr2 = new Object[1];
        c("웢욉ʃ째\ue257ࣣﵤ弤䑃腖䲑箛쌘Ћ칺\uf8d7仁諆䄩", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 1, objArr2);
        bVar.d(((String) objArr2[0]).intern(), cVar.b());
        if (cVar.d() == null) {
            z = false;
        } else {
            z = true;
        }
        switch (z) {
            case false:
                break;
            default:
                int i = a + 91;
                d = i % 128;
                if (i % 2 != 0) {
                    Object[] objArr3 = new Object[1];
                    c("闒閹ꡃ骦䢗宽\ud825穥ᝳ\u2b96῞廊逪껇鴥\udd87ᷚ\u200bቧ偟", Gravity.getAbsoluteGravity(0, 0) + 1, objArr3);
                    obj = objArr3[0];
                } else {
                    Object[] objArr4 = new Object[1];
                    c("闒閹ꡃ骦䢗宽\ud825穥ᝳ\u2b96῞廊逪껇鴥\udd87ᷚ\u200bቧ偟", 0 - Gravity.getAbsoluteGravity(0, 1), objArr4);
                    obj = objArr4[0];
                }
                bVar.d(((String) obj).intern(), cVar.d().getTime());
                break;
        }
        if (cVar.a() == null) {
            z2 = false;
        } else {
            z2 = true;
        }
        switch (z2) {
            case true:
                int i2 = d + 57;
                a = i2 % 128;
                int i3 = i2 % 2;
                Object[] objArr5 = new Object[1];
                c("ꆈꇣ㧢\ud9c9\ud936ᣒ\uea7d䠽〈먷岦沏ꑦ㽽\ude5d\uefd5⦬놏儝或\uaafd", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr5);
                bVar.d(((String) objArr5[0]).intern(), cVar.a().getTime());
            default:
                return bVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void c(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 374
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fj.d.c(java.lang.String, int, java.lang.Object[]):void");
    }
}
