package com.google.android.gms.internal.p000authapi;

import com.google.android.gms.auth.api.credentials.Credential;
import com.google.android.gms.common.api.Status;

/* compiled from: com.google.android.gms:play-services-auth@@20.6.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth-api\zbd.smali */
public class zbd extends zbr {
    @Override // com.google.android.gms.internal.p000authapi.zbs
    public void zbb(Status status, Credential credential) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.p000authapi.zbs
    public void zbc(Status status) {
        throw new UnsupportedOperationException();
    }

    @Override // com.google.android.gms.internal.p000authapi.zbs
    public final void zbd(Status status, String str) {
        throw new UnsupportedOperationException();
    }
}
