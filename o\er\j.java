package o.er;

import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\j.smali */
public final class j {
    private static int d = 0;
    private static int g = 1;
    private final String a;
    private boolean b;
    private final String c;
    private final String e;

    public j(String str, String str2, String str3, boolean z) {
        this.c = str;
        this.a = str2;
        this.e = str3;
        this.b = z;
    }

    public final String b() {
        int i = g;
        int i2 = ((i | 61) << 1) - (i ^ 61);
        int i3 = i2 % 128;
        d = i3;
        int i4 = i2 % 2;
        String str = this.c;
        int i5 = ((i3 | Opcodes.LUSHR) << 1) - (i3 ^ Opcodes.LUSHR);
        g = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String a() {
        int i = d + 13;
        g = i % 128;
        switch (i % 2 == 0 ? (char) 30 : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return this.a;
            default:
                throw null;
        }
    }

    public final String c() {
        int i = (d + 54) - 1;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        String str = this.e;
        int i4 = (i2 & 17) + (i2 | 17);
        d = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final boolean d() {
        int i = d;
        int i2 = (i ^ 17) + ((i & 17) << 1);
        int i3 = i2 % 128;
        g = i3;
        switch (i2 % 2 == 0 ? (char) 23 : (char) 16) {
            case 23:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                boolean z = this.b;
                int i4 = (i3 ^ Opcodes.LSUB) + ((i3 & Opcodes.LSUB) << 1);
                d = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 22 : (char) 3) {
                    case 3:
                        return z;
                    default:
                        int i5 = 72 / 0;
                        return z;
                }
        }
    }

    public final j e() {
        int i = d;
        int i2 = (i ^ 99) + ((i & 99) << 1);
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                this.b = true;
                break;
            default:
                this.b = false;
                break;
        }
        int i3 = (i + 40) - 1;
        g = i3 % 128;
        int i4 = i3 % 2;
        return this;
    }

    public final int hashCode() {
        int hash;
        int i = g;
        int i2 = (i ^ 61) + ((i & 61) << 1);
        d = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                hash = Objects.hash(this.a, this.c, this.e);
                break;
            default:
                hash = Objects.hash(this.c, this.a, this.e);
                break;
        }
        int i3 = d;
        int i4 = ((i3 | Opcodes.LSUB) << 1) - (i3 ^ Opcodes.LSUB);
        g = i4 % 128;
        switch (i4 % 2 == 0 ? '9' : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return hash;
            default:
                int i5 = 90 / 0;
                return hash;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:46:0x00b6 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean equals(java.lang.Object r7) {
        /*
            Method dump skipped, instructions count: 236
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.j.equals(java.lang.Object):boolean");
    }
}
