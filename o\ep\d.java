package o.ep;

import android.os.Process;
import android.os.SystemClock;
import android.util.TypedValue;
import com.esotericsoftware.asm.Opcodes;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ep\d.smali */
public final class d {
    private static char a;
    public static final d b;
    public static final d c;
    private static final /* synthetic */ d[] d;
    public static final d e;
    private static int f;
    private static char g;
    private static char i;
    private static char j;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 0;

    static void b() {
        j = (char) 11312;
        g = (char) 5587;
        i = (char) 16007;
        a = (char) 7007;
    }

    private d(String str, int i2) {
    }

    private static /* synthetic */ d[] d() {
        int i2 = f;
        int i3 = i2 + 1;
        h = i3 % 128;
        int i4 = i3 % 2;
        d[] dVarArr = {c, e, b};
        int i5 = i2 + Opcodes.LREM;
        h = i5 % 128;
        int i6 = i5 % 2;
        return dVarArr;
    }

    public static d valueOf(String str) {
        int i2 = f + 9;
        h = i2 % 128;
        int i3 = i2 % 2;
        d dVar = (d) Enum.valueOf(d.class, str);
        int i4 = h + Opcodes.LSHL;
        f = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                return dVar;
            default:
                int i5 = 82 / 0;
                return dVar;
        }
    }

    public static d[] values() {
        d[] dVarArr;
        int i2 = f + 109;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '\'' : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                dVarArr = (d[]) d.clone();
                break;
            default:
                dVarArr = (d[]) d.clone();
                int i3 = 7 / 0;
                break;
        }
        int i4 = f + 1;
        h = i4 % 128;
        int i5 = i4 % 2;
        return dVarArr;
    }

    static {
        f = 1;
        b();
        Object[] objArr = new Object[1];
        k("舂ᔟ펈꿷\uea19룪", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 6, objArr);
        c = new d(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        k("ꪣ隟짙붷⼁墵⦤鳅\ue636爓홱\ue0af", (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 10, objArr2);
        e = new d(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        k("ꪣ隟\uea19룪午푼磬럦⸿괕", 11 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr3);
        b = new d(((String) objArr3[0]).intern(), 2);
        d = d();
        int i2 = h + 87;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 562
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ep.d.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
