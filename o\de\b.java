package o.de;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\de\b.smali */
public final class b {
    private static int h = 0;
    private static int j = 1;
    private o.df.e a;
    private o.av.a b;
    private f c;
    private Date d;
    private final boolean e;

    public b(Date date, o.av.a aVar, o.df.e eVar, f fVar, boolean z) {
        this.d = date;
        this.b = aVar;
        this.a = eVar;
        this.c = fVar;
        this.e = z;
    }

    public b(b bVar) {
        this.d = new Date(bVar.d.getTime());
        this.b = bVar.b;
        this.a = bVar.a;
        this.c = bVar.c;
        this.e = bVar.e;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0014. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0045  */
    /* JADX WARN: Removed duplicated region for block: B:25:0x0047  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean d(o.de.b r5) {
        /*
            r4 = this;
            int r0 = o.de.b.h
            int r0 = r0 + 104
            r1 = 1
            int r0 = r0 - r1
            int r2 = r0 % 128
            o.de.b.j = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L11
            r0 = 69
            goto L13
        L11:
            r0 = 13
        L13:
            r2 = 0
            switch(r0) {
                case 69: goto L27;
                default: goto L18;
            }
        L18:
            java.util.Date r0 = r5.a()
            java.util.Date r3 = r4.a()
            boolean r0 = r0.before(r3)
            if (r0 == 0) goto L41
            goto L3f
        L27:
            java.util.Date r0 = r5.a()
            java.util.Date r3 = r4.a()
            boolean r0 = r0.before(r3)
            if (r0 == 0) goto L39
            r0 = 33
            goto L3b
        L39:
            r0 = 53
        L3b:
            switch(r0) {
                case 53: goto L45;
                default: goto L3e;
            }
        L3e:
            goto L47
        L3f:
            r0 = r2
            goto L42
        L41:
            r0 = r1
        L42:
            switch(r0) {
                case 0: goto L47;
                default: goto L45;
            }
        L45:
            r1 = r2
            goto L69
        L47:
            java.util.Date r0 = r5.a()
            r4.a(r0)
            o.df.e r0 = r5.a
            r4.a = r0
            o.de.f r0 = r5.c
            r4.c = r0
            o.av.a r5 = r5.b
            r4.b = r5
            int r5 = o.de.b.j
            r0 = r5 | 109(0x6d, float:1.53E-43)
            int r0 = r0 << r1
            r5 = r5 ^ 109(0x6d, float:1.53E-43)
            int r0 = r0 - r5
            int r5 = r0 % 128
            o.de.b.h = r5
            int r0 = r0 % 2
        L69:
            int r5 = o.de.b.h
            r0 = r5 & 97
            r5 = r5 | 97
            int r0 = r0 + r5
            int r5 = r0 % 128
            o.de.b.j = r5
            int r0 = r0 % 2
            if (r0 != 0) goto L7b
            r5 = 68
            goto L7d
        L7b:
            r5 = 99
        L7d:
            switch(r5) {
                case 99: goto L81;
                default: goto L80;
            }
        L80:
            goto L82
        L81:
            return r1
        L82:
            r5 = 0
            r5.hashCode()     // Catch: java.lang.Throwable -> L87
            throw r5     // Catch: java.lang.Throwable -> L87
        L87:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.de.b.d(o.de.b):boolean");
    }

    public final Date a() {
        int i = j + 47;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        Date date = this.d;
        int i4 = ((i2 | 109) << 1) - (i2 ^ 109);
        j = i4 % 128;
        int i5 = i4 % 2;
        return date;
    }

    public final void a(Date date) {
        int i = j;
        int i2 = (i & 15) + (i | 15);
        h = i2 % 128;
        char c = i2 % 2 != 0 ? '\'' : 'N';
        this.d = date;
        switch (c) {
            case 'N':
                return;
            default:
                int i3 = 20 / 0;
                return;
        }
    }

    public final o.av.a b() {
        int i = (h + 62) - 1;
        j = i % 128;
        switch (i % 2 == 0) {
            case false:
                return this.b;
            default:
                throw null;
        }
    }

    public final o.df.e d() {
        int i = (j + 86) - 1;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        o.df.e eVar = this.a;
        int i4 = (i2 + 24) - 1;
        j = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    public final f c() {
        int i = (j + 12) - 1;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        f fVar = this.c;
        int i4 = (i2 & Opcodes.LMUL) + (i2 | Opcodes.LMUL);
        j = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    public final boolean e() {
        int i = h;
        int i2 = ((i | Opcodes.LREM) << 1) - (i ^ Opcodes.LREM);
        j = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? 'K' : 'P') {
            case 'K':
                obj.hashCode();
                throw null;
            default:
                boolean z = this.e;
                int i3 = (i ^ 23) + ((i & 23) << 1);
                j = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        obj.hashCode();
                        throw null;
                    default:
                        return z;
                }
        }
    }
}
