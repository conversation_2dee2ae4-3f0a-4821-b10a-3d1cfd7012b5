package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CipherParameters;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\m4.smali */
public class m4 implements CipherParameters {
    private BigInteger a;
    private BigInteger b;
    private int c;

    public m4(BigInteger bigInteger, BigInteger bigInteger2) {
        this(bigInteger, bigInteger2, 0);
    }

    public BigInteger a() {
        return this.a;
    }

    public int b() {
        return this.c;
    }

    public BigInteger c() {
        return this.b;
    }

    public boolean equals(Object obj) {
        if (!(obj instanceof m4)) {
            return false;
        }
        m4 m4Var = (m4) obj;
        return m4Var.c().equals(this.b) && m4Var.a().equals(this.a) && m4Var.b() == this.c;
    }

    public int hashCode() {
        return (c().hashCode() ^ a().hashCode()) + this.c;
    }

    public m4(BigInteger bigInteger, BigInteger bigInteger2, int i) {
        this.a = bigInteger2;
        this.b = bigInteger;
        this.c = i;
    }
}
