package com.vasco.digipass.sdk.obfuscated;

import androidx.recyclerview.widget.ItemTouchHelper;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import com.vasco.digipass.sdk.DigipassSDKReturnCodes;
import com.vasco.digipass.sdk.responses.DigipassResponse;
import com.vasco.digipass.sdk.responses.GenerateDerivationCodeResponse;
import com.vasco.digipass.sdk.responses.GenerationResponse;
import com.vasco.digipass.sdk.responses.ValidationResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import java.io.ByteArrayOutputStream;
import java.math.BigInteger;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import kotlin.jvm.internal.ByteCompanionObject;
import org.bouncycastle.asn1.BERTags;
import org.bouncycastle.math.Primes;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\k.smali */
public final class k implements DigipassSDKConstants {
    private static final int[] a = {Opcodes.I2C, Opcodes.IXOR, 85, 35, Opcodes.D2F, Opcodes.LREM, 34, 99, 55, 37, 254, 255, ItemTouchHelper.Callback.DEFAULT_SWIPE_ANIMATION_DURATION, 251, 252, 253, 89, 83, 6, 68, Opcodes.LSHL, Opcodes.LNEG, Opcodes.L2I, 19, 100, 54, 239, 234, 235, 236, 237, 238, 52, 70, 53, 33, 87, 39, 32, Opcodes.LSUB, Opcodes.DNEG, 3, 218, 219, 220, 221, 222, 223, 16, Opcodes.ISHL, Opcodes.LOR, 73, Opcodes.IINC, 1, 50, Opcodes.FCMPG, 17, 2, 203, 204, 205, 206, 207, 202, 4, 36, 0, 84, 69, 114, Opcodes.I2D, 9, Opcodes.DREM, Opcodes.LXOR, 188, Opcodes.ANEWARRAY, Opcodes.ARRAYLENGTH, Opcodes.ATHROW, Opcodes.INVOKEDYNAMIC, Opcodes.NEW, Opcodes.FNEG, Opcodes.DCMPG, 18, 66, 56, 51, Opcodes.LCMP, 5, Opcodes.I2B, Opcodes.I2F, Opcodes.LRETURN, Opcodes.FRETURN, Opcodes.DRETURN, Opcodes.TABLESWITCH, Opcodes.LOOKUPSWITCH, Opcodes.IRETURN, 40, 57, 104, 71, 21, 86, 96, 23, Opcodes.IFEQ, 7, Opcodes.IFLE, Opcodes.IF_ICMPEQ, Opcodes.IFNE, Opcodes.IFLT, Opcodes.IFGE, Opcodes.IFGT, 38, 24, 80, Opcodes.INEG, Opcodes.I2S, Opcodes.L2F, Opcodes.IREM, 97, 49, 88, Opcodes.D2L, Opcodes.L2D, Opcodes.F2I, Opcodes.F2L, Opcodes.F2D, Opcodes.D2I, 22, Opcodes.LMUL, 48, 8, 67, Opcodes.I2L, Opcodes.DSUB, 98, Opcodes.FCMPL, 72, Opcodes.ISHR, Opcodes.LSHR, Opcodes.IUSHR, Opcodes.LUSHR, Opcodes.IAND, 127, 82, 102, 20, 41, 25, Opcodes.DCMPL, 81, 64, 128, 65, Opcodes.DMUL, 108, 109, Opcodes.FDIV, Opcodes.DDIV, Opcodes.FMUL, 229, 244, Opcodes.IF_ICMPGT, Opcodes.GETSTATIC, Opcodes.INSTANCEOF, 208, 233, 248, Opcodes.GOTO, Opcodes.INVOKEVIRTUAL, 92, 93, 94, 95, 90, 91, 245, Opcodes.IF_ICMPLE, Opcodes.PUTSTATIC, Opcodes.MONITORENTER, 209, BERTags.FLAGS, 249, Opcodes.JSR, Opcodes.INVOKESPECIAL, Opcodes.IFNULL, 77, 78, 79, 74, 75, 76, Opcodes.IF_ACMPEQ, Opcodes.GETFIELD, Opcodes.MONITOREXIT, 210, 225, 240, Opcodes.RET, Opcodes.INVOKESTATIC, Opcodes.IFNONNULL, 214, 62, 63, 58, 59, 60, 61, Opcodes.PUTFIELD, 196, Primes.SMALL_FACTOR_LIMIT, 226, 241, Opcodes.IF_ICMPNE, Opcodes.INVOKEINTERFACE, 200, 215, 230, 47, 42, 43, 44, 45, 46, Opcodes.MULTIANEWARRAY, 212, 227, 242, Opcodes.IF_ICMPLT, Opcodes.ARETURN, 201, 216, 231, 246, 26, 27, 28, 29, 30, 31, 213, 228, 243, Opcodes.IF_ICMPGE, Opcodes.RETURN, 192, 217, 232, 247, Opcodes.IF_ACMPNE, 11, 12, 13, 14, 15, 10};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\k$a.smali */
    public static class a {
        final String a;
        final String b;

        public a(String str, String str2) {
            this.a = str;
            this.b = str2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\k$b.smali */
    public static class b extends DigipassResponse {
        final e c;
        final String[] d;
        final int e;

        public b(int i, e eVar, String[] strArr, int i2) {
            super(i);
            this.c = eVar;
            this.d = strArr;
            this.e = i2;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\k$c.smali */
    public static class c {
        final byte[] a;
        final byte b;

        public c(byte[] bArr, byte b) {
            this.a = bArr;
            this.b = b;
        }
    }

    private static int a(byte b2, int i) {
        return (b2 & 255) << i;
    }

    public static GenerationResponse a(byte[] bArr, byte[] bArr2, long j, int i, byte[] bArr3, int i2, byte[] bArr4, boolean z, String str, boolean z2, String[] strArr, boolean z3, String str2, String str3, boolean z4, byte b2, boolean z5) {
        try {
            l.a(bArr3, i2);
            b a2 = a(bArr, bArr2, i, bArr3, i2, bArr4, z, str, z2, strArr, z3, str3, z4, str2, b2, z5);
            if (a2.getReturnCode() == 0) {
                GenerationResponse a3 = a(a2.c, i, a2.d, str2, j, false, b2);
                a2.c.f();
                return a3;
            }
            int returnCode = a2.getReturnCode();
            e eVar = a2.c;
            GenerationResponse generationResponse = new GenerationResponse(returnCode, eVar.d.c, p.a(eVar), a2.e, null, null);
            a2.c.f();
            return generationResponse;
        } catch (h e) {
            return a(e.a());
        } catch (Exception e2) {
            return a(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static byte b(byte b2, int i) {
        int i2 = b2 - (b2 > 57 ? (byte) 55 : (byte) 48);
        if ((i & 1) == 0) {
            i2 <<= 4;
        }
        return (byte) i2;
    }

    private static long b(long j) {
        if (j == 0) {
            return 1L;
        }
        if (j == 1) {
            return 8L;
        }
        if (j == 2) {
            return 16L;
        }
        if (j == 3) {
            return 30L;
        }
        if (j == 4) {
            return 32L;
        }
        return j == 5 ? 60L : 1L;
    }

    private static boolean b(d dVar, String str) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        String upperCase = (bVar.B || bVar.E) ? str : str.toUpperCase();
        for (int i = 0; i < upperCase.length(); i++) {
            char charAt = upperCase.charAt(i);
            if (bVar.B || bVar.E) {
                long j = bVar.b;
                if (j == 1 || bVar.E) {
                    if ((charAt < '%' || charAt > '?') && ((charAt < 'A' || charAt > 'Z') && charAt != '_' && (charAt < 'a' || charAt > 'z'))) {
                        return false;
                    }
                } else if (j == 2) {
                    char upperCase2 = Character.toUpperCase(charAt);
                    if ((upperCase2 < '0' || upperCase2 > '9') && (upperCase2 < 'A' || upperCase2 > 'F')) {
                        return false;
                    }
                } else if (charAt < '0' || charAt > '9') {
                    return false;
                }
            } else {
                if (!bVar.x) {
                    long j2 = bVar.b;
                    if (j2 != 1 && j2 != 2) {
                        if ((charAt < '0' || charAt > '9') && (charAt < 'A' || charAt > 'F')) {
                            return false;
                        }
                    }
                }
                if ((charAt < '%' || charAt > '?') && ((charAt < 'A' || charAt > 'Z') && charAt != '_')) {
                    return false;
                }
            }
        }
        return true;
    }

    private static void c(byte[] bArr, byte[] bArr2) {
        byte[] bArr3;
        byte[] a2 = q.a(bArr2, 0, 8);
        if (bArr.length == 16) {
            bArr3 = q.a(bArr, 0, bArr.length / 2);
        } else {
            byte[] bArr4 = new byte[8];
            System.arraycopy(bArr, 0, bArr4, 0, 8);
            bArr3 = bArr4;
        }
        byte[] outputData = UtilitiesSDK.encrypt((byte) 1, (byte) 2, a2, null, bArr3).getOutputData();
        System.arraycopy(outputData, 0, bArr, 0, outputData.length);
        q.g(a2);
    }

    private static void d(byte[] bArr, byte[] bArr2) {
        byte[] bArr3 = new byte[32];
        System.arraycopy(bArr, 0, bArr3, 0, 32);
        long a2 = a(bArr3[0], 24) | a(bArr3[1], 16) | a(bArr3[2], 8) | a(bArr3[3], 0);
        long a3 = a(bArr3[4], 24) | a(bArr3[5], 16) | a(bArr3[6], 8) | a(bArr3[7], 0);
        long a4 = a(bArr3[8], 24) | a(bArr3[9], 16) | a(bArr3[10], 8) | a(bArr3[11], 0);
        long a5 = a(bArr3[12], 24) | a(bArr3[13], 16) | a(bArr3[14], 8) | a(bArr3[15], 0);
        long a6 = a(bArr3[16], 24) | a(bArr3[17], 16) | a(bArr3[18], 8) | a(bArr3[19], 0);
        byte[] a7 = com.vasco.digipass.sdk.obfuscated.c.a(a3 + a2 + a4 + a5 + a6 + (a(bArr3[20], 24) | a(bArr3[21], 16) | a(bArr3[22], 8) | a(bArr3[23], 0)) + (a(bArr3[24], 24) | a(bArr3[25], 16) | a(bArr3[26], 8) | a(bArr3[27], 0)) + (a(bArr3[28], 24) | a(bArr3[29], 16) | a(bArr3[30], 8) | a(bArr3[31], 0)));
        System.arraycopy(a7, 0, bArr2, 0, 4);
        q.g(bArr3);
        q.g(a7);
    }

    static String e() {
        byte[] bArr = new byte[2];
        int[] iArr = {Opcodes.IF_ACMPEQ, Opcodes.L2D};
        for (int i = 0; i < 2; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) ((((i2 & 255) >> 5) | (i2 << 3)) & 255);
        }
        return new String(bArr);
    }

    static String f() {
        return new String(new byte[]{(byte) 77});
    }

    static String g() {
        byte[] bArr = new byte[3];
        int[] iArr = {63, 63, 63};
        for (int i = 0; i < 3; i++) {
            bArr[i] = (byte) iArr[i];
        }
        return new String(bArr);
    }

    private static void c(d dVar, byte[] bArr) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        byte b2 = (byte) (dVar.n % 10);
        byte b3 = dVar.m;
        if (b3 == 1) {
            bArr[0] = (byte) ((bArr[0] & 240) + b2);
            return;
        }
        if (b3 == 0) {
            bArr[1] = (byte) (b2 + 48);
        } else if (b3 == 2 && bVar.f15o) {
            bArr[1] = (byte) Integer.toString(b2).charAt(0);
        }
    }

    static String d() {
        return new String(new byte[]{(byte) 83});
    }

    private static a c(d dVar, String str) {
        String substring;
        String substring2;
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        int i = dVar.j;
        int i2 = dVar.k;
        if (bVar.r) {
            substring2 = str.substring(0, i2);
            substring = str.substring(i2);
        } else {
            substring = str.substring(0, i);
            substring2 = str.substring(i);
        }
        return new a(substring, substring2);
    }

    static String c() {
        byte[] bArr = new byte[2];
        int[] iArr = {Opcodes.INVOKEDYNAMIC, 209};
        for (int i = 0; i < 2; i++) {
            bArr[i] = (byte) (iArr[i] + 128);
        }
        return new String(bArr);
    }

    private static b a(byte[] bArr, byte[] bArr2, int i, byte[] bArr3, int i2, byte[] bArr4, boolean z, String str, boolean z2, String[] strArr, boolean z3, String str2, boolean z4, String str3, byte b2, boolean z5) {
        e b3;
        byte[] bArr5;
        if (z) {
            b3 = p.b(bArr, bArr2, bArr4);
            if (!b3.e()) {
                return new b(DigipassSDKReturnCodes.STATUS_INVALID, b3, null, 0);
            }
            l.a(b3, (byte[]) null, 0, (byte[]) null, 0, bArr4, (String) null);
        } else {
            b3 = p.b(bArr, bArr2);
            if (!b3.e()) {
                return new b(DigipassSDKReturnCodes.STATUS_INVALID, b3, null, 0);
            }
        }
        if (i != 100 && (i < 1 || i > b3.a)) {
            return new b(DigipassSDKReturnCodes.CRYPTO_APPLICATION_INDEX_INVALID, b3, null, 0);
        }
        d a2 = b3.a(i);
        if (!a2.a) {
            return new b(DigipassSDKReturnCodes.APPLICATION_DISABLED, b3, null, 0);
        }
        if (b3.c.t && q.d(str3)) {
            return new b(DigipassSDKReturnCodes.PLATFORM_FINGERPRINT_NOT_DEFINED, b3, null, 0);
        }
        if (!z) {
            ValidationResponse b4 = l.b(bArr3, i2, b3, str3);
            if (b4.getReturnCode() != 0) {
                return new b(b4.getReturnCode(), b3, null, b4.getAttemptLeft());
            }
            bArr5 = b4.getEncryptionKey();
        } else {
            byte[] bArr6 = new byte[16];
            System.arraycopy(bArr4, 0, bArr6, 0, 16);
            bArr5 = bArr6;
        }
        a(b3, a2, bArr5);
        b a3 = a(b3, a2, str, z2, strArr, z3, str2, z4);
        if (!z5 || a2.e.D) {
            return (b2 < 0 || b2 > 7) ? new b(DigipassSDKReturnCodes.SCORE_INVALID, b3, null, 0) : a3;
        }
        return new b(DigipassSDKReturnCodes.CLIENT_SCORE_DISABLED, b3, null, 0);
    }

    private static String b(String str) {
        byte[] outputData = UtilitiesSDK.hash((byte) 3, str.getBytes()).getOutputData();
        b(outputData, 16);
        b(outputData, 8);
        return q.a(outputData).substring(0, 16);
    }

    private static void b(byte[] bArr, int i) {
        a(bArr, bArr, i, i);
    }

    private static void b(d dVar) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        if (bVar.x || !bVar.f) {
            return;
        }
        boolean z = false;
        int i = 0;
        while (true) {
            if (i >= dVar.g) {
                break;
            }
            if (dVar.i[i] > 8) {
                z = true;
                break;
            }
            i++;
        }
        if (z) {
            long j = bVar.b;
            if ((j == 0 || j == 3) && !bVar.g) {
                bVar.g = true;
                bVar.C = true;
            }
            if ((j == 1 || j == 2) && bVar.g) {
                bVar.x = true;
                bVar.y = 1L;
                bVar.C = true;
                bVar.c = 13L;
                bVar.d = 13L;
            }
        }
    }

    private static void b(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int length = bArr3.length + bArr2.length;
        if (length < 32) {
            length = 32;
        }
        byte[] bArr4 = new byte[length];
        System.arraycopy(bArr3, 0, bArr4, 0, bArr3.length);
        System.arraycopy(bArr2, 0, bArr4, bArr3.length, bArr2.length);
        byte[] outputData = UtilitiesSDK.hash((byte) 4, bArr4).getOutputData();
        q.g(bArr4);
        byte[] bArr5 = new byte[4];
        d(outputData, bArr5);
        q.g(outputData);
        q.g(bArr);
        System.arraycopy(bArr5, 0, bArr, 4, 4);
        q.g(bArr5);
    }

    private static void b(byte[] bArr, byte[] bArr2) {
        byte[] bArr3 = new byte[16];
        System.arraycopy(bArr, 0, bArr3, 0, 8);
        System.arraycopy(bArr, 0, bArr3, 8, 8);
        byte[] outputData = UtilitiesSDK.encrypt((byte) 3, (byte) 2, bArr2, null, bArr3).getOutputData();
        for (int i = 0; i < 8; i++) {
            bArr[i] = (byte) (outputData[i] ^ outputData[i + 8]);
        }
    }

    private static void a(e eVar, d dVar, byte[] bArr) {
        g gVar = eVar.d;
        byte[] bArr2 = gVar.g;
        byte[] bArr3 = gVar.h;
        if (dVar.b) {
            bArr2 = gVar.i;
            bArr3 = gVar.j;
        } else if (dVar.c == 100) {
            bArr2 = gVar.p;
            bArr3 = gVar.q;
        }
        byte[] outputData = UtilitiesSDK.decrypt((byte) 3, (byte) 2, bArr, null, bArr3).getOutputData();
        System.arraycopy(outputData, 0, bArr2, 0, 16);
        q.g(outputData);
        q.g(bArr);
    }

    private static void b(d dVar, byte[] bArr, c cVar) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        byte b2 = cVar.b;
        byte b3 = dVar.m;
        if (b3 == 1) {
            bArr[0] = (byte) ((bArr[0] & 15) + (b2 << 4));
            return;
        }
        if (b3 == 0) {
            bArr[0] = (byte) (b2 + 48);
        } else if (b3 == 2 && bVar.m) {
            bArr[0] = (byte) Integer.toString(b2).charAt(0);
        }
    }

    private static a b(d dVar, byte[] bArr) {
        String substring;
        String str;
        String str2;
        String substring2;
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        int i = dVar.j;
        int i2 = dVar.k;
        int i3 = i + i2;
        byte b2 = dVar.m;
        if (b2 == 1) {
            StringBuilder sb = new StringBuilder();
            for (byte b3 : bArr) {
                String hexString = Integer.toHexString(b3 & 255);
                if (!bVar.r && hexString.length() == 1) {
                    sb.append('0');
                }
                sb.append(hexString);
            }
            String upperCase = sb.toString().toUpperCase();
            if (bVar.r && i3 < 14) {
                substring2 = upperCase.substring(14 - i3, 14);
            } else {
                substring2 = upperCase.substring(0, i3);
            }
            return c(dVar, substring2);
        }
        if (b2 == 0) {
            if (bVar.r && i3 < 14) {
                str2 = new String(bArr, 14 - i3, i3);
            } else {
                str2 = new String(bArr, 0, i3);
            }
            return c(dVar, str2);
        }
        if (!bVar.f15o && !bVar.m) {
            String a2 = q.a(bArr);
            String substring3 = a2.substring(a2.length() - i3);
            substring = substring3.substring(0, i2);
            str = substring3.substring(i2);
        } else {
            String str3 = new String(bArr, 0, i3);
            if (bVar.r) {
                substring = str3.substring(0, i2);
                str = str3.substring(i2);
            } else {
                String substring4 = str3.substring(0, i);
                substring = str3.substring(i);
                str = substring4;
            }
        }
        return new a(str, substring);
    }

    private static b a(e eVar, d dVar, String str, boolean z, String[] strArr, boolean z2, String str2, boolean z3) {
        String[] strArr2;
        int i = dVar.p;
        if (i == 2 && !z && !z2) {
            return new b(DigipassSDKReturnCodes.CHALLENGE_NULL, eVar, null, 0);
        }
        if (i == 3 && !z && !z2) {
            return new b(DigipassSDKReturnCodes.DATA_FIELD_NULL, eVar, null, 0);
        }
        if (!z) {
            strArr2 = null;
        } else {
            if (str == null) {
                return new b(DigipassSDKReturnCodes.CHALLENGE_NULL, eVar, null, 0);
            }
            if (str.length() >= dVar.h[0] && str.length() <= dVar.i[0]) {
                if (!b(dVar, str)) {
                    return new b(DigipassSDKReturnCodes.CHALLENGE_CHARACTER_INVALID, eVar, null, 0);
                }
                strArr2 = new String[]{str};
            } else {
                return new b(DigipassSDKReturnCodes.CHALLENGE_INCORRECT_LENGTH, eVar, null, 0);
            }
        }
        if (!z2) {
            strArr = strArr2;
        } else {
            if (strArr == null) {
                return new b(DigipassSDKReturnCodes.DATA_FIELDS_ARRAY_NULL, eVar, null, 0);
            }
            if (strArr.length > dVar.g) {
                return new b(DigipassSDKReturnCodes.DATA_FIELDS_NUMBER_INVALID, eVar, null, 0);
            }
            if (!z3) {
                boolean z4 = false;
                for (int i2 = 0; i2 < dVar.g; i2++) {
                    if (i2 < strArr.length) {
                        String str3 = strArr[i2];
                        if (str3 == null) {
                            return new b(DigipassSDKReturnCodes.DATA_FIELD_NULL, eVar, null, 0);
                        }
                        if (str3.length() >= dVar.h[i2] && str3.length() <= dVar.i[i2]) {
                            if (!b(dVar, str3)) {
                                return new b(DigipassSDKReturnCodes.CHALLENGE_CHARACTER_INVALID, eVar, null, 0);
                            }
                            if (z4 && !q.d(str3)) {
                                return new b(DigipassSDKReturnCodes.DATA_FIELDS_NOT_CONTIGUOUS, eVar, null, 0);
                            }
                            z4 = q.d(str3);
                        } else {
                            return new b(DigipassSDKReturnCodes.DATA_FIELD_INCORRECT_LENGTH, eVar, null, 0);
                        }
                    } else if (dVar.h[i2] > 0) {
                        return new b(DigipassSDKReturnCodes.DATA_FIELDS_NUMBER_INVALID, eVar, null, 0);
                    }
                }
            } else {
                b a2 = a(eVar, strArr);
                if (a2.getReturnCode() != 0) {
                    return a2;
                }
                strArr = a2.d;
            }
        }
        if (str2 != null) {
            b a3 = a(eVar, str2, strArr);
            if (a3.getReturnCode() != 0) {
                return a3;
            }
            strArr = a3.d;
        }
        return new b(0, eVar, strArr, 0);
    }

    static String b() {
        byte[] bArr = new byte[4];
        int[] iArr = {58, 68, 47, 84};
        for (int i = 0; i < 4; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }

    private static b a(e eVar, String[] strArr) {
        int length = strArr.length;
        int i = 0;
        int i2 = 0;
        loop0: while (true) {
            String[] strArr2 = null;
            if (i >= length) {
                if (i2 != 0) {
                    StringBuilder sb = new StringBuilder();
                    for (String str : strArr) {
                        sb.append(str);
                    }
                    strArr2 = new String[]{b(sb.toString())};
                }
                return new b(0, eVar, strArr2, 0);
            }
            String str2 = strArr[i];
            if (str2 == null) {
                return new b(DigipassSDKReturnCodes.DATA_FIELD_NULL, eVar, null, 0);
            }
            if (str2.length() > 32000) {
                return new b(DigipassSDKReturnCodes.DATA_FIELD_INCORRECT_LENGTH, eVar, null, 0);
            }
            for (int i3 = 0; i3 < str2.length(); i3++) {
                char charAt = str2.charAt(i3);
                if (charAt < ' ' || ((charAt > ' ' && charAt < '%') || ((charAt > '?' && charAt < 'A') || ((charAt > 'Z' && charAt < '_') || ((charAt > '_' && charAt < 'a') || charAt > 'z'))))) {
                    break loop0;
                }
            }
            i2 += str2.length();
            i++;
        }
        return new b(DigipassSDKReturnCodes.CHALLENGE_CHARACTER_INVALID, eVar, null, 0);
    }

    private static void a(byte[] bArr, byte[] bArr2, int i, int i2) {
        for (int i3 = 0; i3 < i2; i3++) {
            bArr[i3] = (byte) (bArr[i3] ^ bArr2[i3 + i]);
        }
    }

    private static b a(e eVar, String str, String[] strArr) {
        if (str.length() > 1024) {
            return new b(DigipassSDKReturnCodes.SERVER_PUBLIC_KEY_INCORRECT_LENGTH, eVar, null, 0);
        }
        StringBuilder sb = new StringBuilder();
        sb.append(str.toUpperCase());
        if (strArr != null && strArr.length != 0) {
            sb.append(strArr[0]);
        }
        return new b(0, eVar, new String[]{b(sb.toString())}, 0);
    }

    private static GenerationResponse a(e eVar, int i, String[] strArr, String str, long j, boolean z, byte b2) {
        d a2 = eVar.a(i);
        b(a2);
        a(eVar, a2);
        byte[] a3 = j.a(eVar, a2, str, z);
        c a4 = a(eVar, a2, j, a3, b2);
        byte[] bArr = a4.a;
        byte[] a5 = a(eVar, a2, a3, bArr, strArr);
        q.g(a3);
        a(eVar, a2, bArr, a5, false);
        q.g(a5);
        byte[] a6 = a(a2, bArr, a4);
        q.g(bArr);
        a b3 = b(a2, a6);
        q.g(a6);
        return a(0, eVar, a(a2, a(b3.a, b2, a2)), b3.b);
    }

    private static void a(e eVar, d dVar) {
        byte b2;
        byte[] a2;
        if (dVar.b || (b2 = dVar.c) == 1 || b2 == 100) {
            return;
        }
        i iVar = eVar.c;
        if (iVar.r || iVar.t) {
            if (!iVar.t) {
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byteArrayOutputStream.write(eVar.d.g, 0, 16);
                byteArrayOutputStream.write(eVar.c.b.getBytes(), 0, 10);
                byteArrayOutputStream.write(dVar.d.getBytes(), 0, 12);
                byte[] byteArray = byteArrayOutputStream.toByteArray();
                a2 = UtilitiesSDK.hash((byte) 3, byteArray).getOutputData();
                byteArrayOutputStream.reset();
                q.g(byteArray);
            } else {
                StringBuilder sb = new StringBuilder();
                sb.append(dVar.d.substring(0, 10));
                byte b3 = eVar.d.r;
                if (b3 < 10) {
                    sb.append('0');
                }
                sb.append((int) b3);
                a2 = o.a(eVar.d.g, sb.toString().getBytes());
            }
            System.arraycopy(a2, 0, eVar.d.g, 0, 16);
            q.g(a2);
        }
    }

    private static c a(e eVar, d dVar, long j, byte[] bArr, byte b2) {
        byte b3;
        long j2;
        long j3;
        int i;
        char c2;
        byte[] bArr2 = new byte[16];
        long a2 = a(j);
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        if (!bVar.E) {
            if (bVar.D) {
                byte[] bArr3 = new byte[8];
                bArr3[7] = b2;
                a(eVar, dVar, bArr2, bArr3, true);
            }
            if (bArr != null) {
                byte[] bArr4 = new byte[4];
                System.arraycopy(bArr2, 4, bArr4, 0, 4);
                a(bArr4, bArr, 0, 4);
                System.arraycopy(bArr4, 0, bArr2, 4, 4);
            }
            if (bVar.C && bVar.l && bVar.j) {
                byte[] bArr5 = new byte[8];
                System.arraycopy(bArr2, 0, bArr5, 0, 8);
                a(eVar, dVar, bArr2, bArr5, true);
            }
        }
        if (bVar.r) {
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
            calendar.setTime(new Date(a2 * 1000));
            int i2 = calendar.get(1) - 1900;
            int i3 = calendar.get(2) + 1;
            int i4 = calendar.get(5);
            int i5 = calendar.get(11);
            int i6 = ((calendar.get(12) * 60) + calendar.get(13)) / 36;
            byte[] bArr6 = {0, 0, 0, (byte) (((i2 / 10) * 16) + (i2 % 10)), (byte) (((i3 / 10) * 16) + (i3 % 10)), (byte) (((i4 / 10) * 16) + (i4 % 10)), (byte) (((i5 / 10) * 16) + (i5 % 10)), (byte) (((i6 / 10) * 16) + (i6 % 10))};
            if (!bVar.j) {
                i = 4;
            } else {
                i = 4;
                System.arraycopy(bArr6, 4, bArr2, 4, 4);
            }
            if (bVar.k) {
                System.arraycopy(bArr6, 0, bArr2, 0, i);
            }
            if (!bVar.t) {
                c2 = 7;
            } else {
                c2 = 7;
                bArr2[7] = bArr2[6];
            }
            b3 = bArr2[c2];
        } else {
            if (bVar.n) {
                long j4 = dVar.n + 1;
                if (j4 == 4294967296L) {
                    j4 = 0;
                }
                if (!bVar.B && !bVar.E) {
                    a(bArr2, com.vasco.digipass.sdk.obfuscated.c.a(j4), 0, 4);
                }
                dVar.n = j4;
            }
            if (!bVar.l) {
                b3 = 0;
            } else {
                long j5 = bVar.e;
                if (bVar.q) {
                    if (j5 > 7) {
                        a2 /= 15;
                        j3 = j5 % 8;
                    } else {
                        j3 = j5 + 3;
                    }
                    j2 = a2 >> ((int) j3);
                    bArr2[0] = (byte) (bArr2[0] ^ ((byte) ((j2 >> 24) & 255)));
                    bArr2[1] = (byte) (bArr2[1] ^ ((byte) ((j2 >> 16) & 255)));
                    bArr2[2] = (byte) (bArr2[2] ^ ((byte) ((j2 >> 8) & 255)));
                    bArr2[3] = (byte) (bArr2[3] ^ ((byte) (j2 & 255)));
                } else if (bVar.E) {
                    long b4 = b(j5);
                    j2 = a2 / (b4 != 0 ? b4 : 1L);
                    bArr2[4] = (byte) (bArr2[4] ^ ((byte) ((j2 >> 24) & 255)));
                    bArr2[5] = (byte) (((byte) ((j2 >> 16) & 255)) ^ bArr2[5]);
                    bArr2[6] = (byte) (bArr2[6] ^ ((byte) ((j2 >> 8) & 255)));
                    bArr2[7] = (byte) (bArr2[7] ^ ((byte) (j2 & 255)));
                } else {
                    j2 = a2 >> ((int) (j5 + 3));
                    bArr2[4] = (byte) (bArr2[4] ^ ((byte) ((j2 >> 24) & 255)));
                    bArr2[5] = (byte) (((byte) ((j2 >> 16) & 255)) ^ bArr2[5]);
                    bArr2[6] = (byte) (bArr2[6] ^ ((byte) ((j2 >> 8) & 255)));
                    bArr2[7] = (byte) (bArr2[7] ^ ((byte) (j2 & 255)));
                }
                b3 = (byte) (j2 % 10);
            }
        }
        if (bVar.C && ((bVar.k || bVar.l || bVar.j) && (bVar.x || bVar.f || bVar.g))) {
            byte[] bArr7 = new byte[8];
            System.arraycopy(bArr2, 0, bArr7, 0, 8);
            a(eVar, dVar, bArr2, bArr7, true);
        }
        dVar.f16o = System.currentTimeMillis() / 1000;
        return new c(bArr2, b3);
    }

    private static long a(long j) {
        return (System.currentTimeMillis() / 1000) + j;
    }

    private static byte[] a(e eVar, d dVar, byte[] bArr, byte[] bArr2, String[] strArr) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        boolean z = bVar.f || bVar.g || bVar.x;
        if (z && strArr == null) {
            strArr = new String[]{""};
        }
        if (bVar.E) {
            a(dVar, bArr, bArr2, strArr, byteArrayOutputStream);
        } else if (z) {
            if (bVar.q) {
                byteArrayOutputStream.write(bArr2, 0, 8);
                q.g(bArr2);
            }
            if (bVar.B) {
                a(dVar, strArr, byteArrayOutputStream);
            } else {
                a(eVar, dVar, bArr2, byteArrayOutputStream, strArr);
            }
        } else {
            byteArrayOutputStream.write(bArr2, 0, 8);
            q.g(bArr2);
        }
        return byteArrayOutputStream.toByteArray();
    }

    private static void a(d dVar, byte[] bArr, byte[] bArr2, String[] strArr, ByteArrayOutputStream byteArrayOutputStream) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        if (bVar.l) {
            byteArrayOutputStream.write(bArr2, 0, 8);
        }
        if (bVar.n) {
            byte[] a2 = com.vasco.digipass.sdk.obfuscated.c.a(dVar.n);
            byteArrayOutputStream.write(a2, 0, a2.length);
            q.g(a2);
        }
        if (bVar.j) {
            if (bArr != null) {
                byteArrayOutputStream.write(bArr, 0, bArr.length);
            } else {
                byteArrayOutputStream.write(new byte[4], 0, 4);
            }
        }
        for (int i = 0; strArr != null && i < strArr.length && i < dVar.g && strArr[i].getBytes().length > 0; i++) {
            if (i != 0) {
                byteArrayOutputStream.write(0);
            }
            byteArrayOutputStream.write(strArr[i].getBytes(), 0, strArr[i].getBytes().length);
        }
    }

    private static void a(d dVar, String[] strArr, ByteArrayOutputStream byteArrayOutputStream) {
        byte[] bArr;
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        String a2 = a(dVar);
        byteArrayOutputStream.write(a2.getBytes(), 0, a2.length());
        byteArrayOutputStream.write(0);
        if (bVar.n) {
            byte[] a3 = com.vasco.digipass.sdk.obfuscated.c.a(dVar.n);
            for (int i = 0; i < 4; i++) {
                byteArrayOutputStream.write(0);
            }
            byteArrayOutputStream.write(a3, 0, a3.length);
        }
        String str = strArr[0];
        if (bVar.b == 0) {
            bArr = a(str);
        } else {
            byte[] bArr2 = new byte[16];
            System.arraycopy(str.getBytes(), 0, bArr2, 0, str.length());
            bArr = bArr2;
        }
        byteArrayOutputStream.write(bArr, 0, bArr.length);
        for (int i2 = 0; i2 < 128 - bArr.length; i2++) {
            byteArrayOutputStream.write(0);
        }
        if (bVar.l) {
            for (int i3 = 0; i3 < 4; i3++) {
                byteArrayOutputStream.write(0);
            }
            byteArrayOutputStream.write(byteArrayOutputStream.toByteArray(), 0, 4);
        }
    }

    private static String a(d dVar) {
        String c2;
        long j;
        String str;
        StringBuilder sb = new StringBuilder();
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        sb.append(a());
        sb.append(dVar.j);
        if (bVar.n) {
            c2 = b();
        } else {
            c2 = c();
        }
        sb.append(c2);
        sb.append(bVar.b == 1 ? 'A' : 'N');
        byte b2 = dVar.i[0];
        if (b2 < 10) {
            sb.append('0');
        }
        sb.append((int) b2);
        if (bVar.l) {
            long j2 = bVar.e;
            if (j2 > 7) {
                j = 15 << ((int) (j2 % 8));
            } else {
                j = j2 < 3 ? 8 << ((int) j2) : 0L;
            }
            if (j == 0) {
                str = g();
            } else if (j < 60) {
                str = j + d();
            } else {
                str = (j / 60) + f();
            }
            sb.append(e());
            sb.append(str);
        }
        return sb.toString();
    }

    private static byte[] a(String str) {
        if (str.length() > 16) {
            return null;
        }
        if (str.length() == 0) {
            return new byte[16];
        }
        String upperCase = new BigInteger(str, 10).toString(16).toUpperCase();
        if (upperCase.length() == 1) {
            upperCase = "0" + upperCase;
        }
        if (upperCase.length() % 2 != 0) {
            upperCase = upperCase + "0";
        }
        byte[] a2 = q.a(upperCase);
        byte[] bArr = new byte[16];
        System.arraycopy(a2, 0, bArr, 0, a2.length);
        return bArr;
    }

    private static void a(e eVar, d dVar, byte[] bArr, ByteArrayOutputStream byteArrayOutputStream, String[] strArr) {
        int i;
        byte[] bArr2;
        int i2;
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        byte[] bArr3 = new byte[8];
        byte b2 = 0;
        int i3 = 0;
        int i4 = 0;
        while (i3 < strArr.length) {
            String str = strArr[i3];
            if (q.d(str)) {
                if (i3 != 0) {
                    i = i3;
                    i3 = i + 1;
                    b2 = 0;
                } else {
                    str = "";
                }
            }
            String upperCase = str.toUpperCase();
            byte[] bArr4 = new byte[32];
            if (bVar.x) {
                System.arraycopy(bArr3, b2, bArr4, b2, i4);
                System.arraycopy(upperCase.getBytes(), b2, bArr4, i4, upperCase.length());
                int length = i4 + upperCase.length();
                if (i3 == strArr.length - 1 || q.d(strArr[i3 + 1])) {
                    bArr2 = bArr3;
                    int i5 = b2;
                    while (true) {
                        int i6 = i5 + length;
                        if (i6 % 8 == 0) {
                            break;
                        }
                        bArr4[i6] = (byte) ((bVar.d << 4) + bVar.c);
                        i5++;
                        i3 = i3;
                    }
                    i = i3;
                    if (bVar.q || bVar.r || !((bVar.p || bVar.v || !dVar.f) && bVar.C && i == 0 && length == 0)) {
                        i2 = i5;
                    } else {
                        for (int length2 = upperCase.length(); length2 < 16; length2++) {
                            if ((length2 & 1) == 1) {
                                bArr4[length2 >> 1] = (byte) (bArr4[r6] + bVar.c);
                            } else {
                                bArr4[length2 >> 1] = (byte) (((byte) bVar.c) << 4);
                            }
                        }
                        i2 = 8;
                    }
                } else {
                    i2 = b2;
                    while (true) {
                        bArr2 = bArr3;
                        if (i2 >= bVar.y) {
                            break;
                        }
                        bArr4[i2 + length] = b2;
                        i2++;
                        bArr3 = bArr2;
                    }
                    i = i3;
                }
                int i7 = length + i2;
                int i8 = i7 % 8;
                while (true) {
                    if (i7 < 8) {
                        break;
                    }
                    int i9 = 0;
                    for (int i10 = 8; i9 < i10; i10 = 8) {
                        bArr[i9] = (byte) (bArr[i9] ^ bArr4[i9]);
                        i9++;
                    }
                    for (int i11 = 8; i11 < i7; i11++) {
                        bArr4[i11 - 8] = bArr4[i11];
                    }
                    i7 -= 8;
                    byteArrayOutputStream.write(bArr, 0, 8);
                    q.g(bArr);
                }
                if (i8 == 0) {
                    bArr3 = bArr2;
                } else {
                    bArr3 = bArr2;
                    System.arraycopy(bArr4, 0, bArr3, 0, i7);
                }
                if (byteArrayOutputStream.size() == 0 && i8 == 0) {
                    byteArrayOutputStream.write(bArr, 0, 8);
                    q.g(bArr);
                }
                i4 = i8;
            } else {
                i = i3;
                if (bVar.f || bVar.g) {
                    bArr4 = a(bVar, upperCase);
                }
                if (bVar.f) {
                    bArr[0] = (byte) (bArr[0] ^ bArr4[0]);
                    bArr[1] = (byte) (bArr[1] ^ bArr4[1]);
                    bArr[2] = (byte) (bArr[2] ^ bArr4[2]);
                    bArr[3] = (byte) (bArr[3] ^ bArr4[3]);
                }
                if (bVar.g) {
                    bArr[4] = (byte) (bArr[4] ^ bArr4[4]);
                    bArr[5] = (byte) (bArr[5] ^ bArr4[5]);
                    bArr[6] = (byte) (bArr[6] ^ bArr4[6]);
                    bArr[7] = (byte) (bArr[7] ^ bArr4[7]);
                }
                byteArrayOutputStream.write(bArr, 0, 8);
                q.g(bArr);
            }
            i3 = i + 1;
            b2 = 0;
        }
        if (bVar.C) {
            if (bVar.x || bVar.f || bVar.g) {
                if (bVar.p || bVar.v || !dVar.f) {
                    byte[] bArr5 = {(byte) (((byte) (r4 >> 56)) & 255), (byte) (((byte) (r4 >> 48)) & 255), (byte) (((byte) (r4 >> 40)) & 255), (byte) (((byte) (r4 >> 32)) & 255), (byte) (((byte) (r4 >> 24)) & 255), (byte) (((byte) (r4 >> 16)) & 255), (byte) (((byte) (r4 >> 8)) & 255), r0};
                    a(eVar, dVar, bArr5, bArr5, false);
                    byte b3 = (byte) (((byte) ((((((((((r0 & 255) << 56) + ((bArr5[1] & 255) << 48)) + ((bArr5[2] & 255) << 40)) + ((bArr5[3] & 255) << 32)) + ((bArr5[4] & 255) << 24)) + ((bArr5[5] & 255) << 16)) + ((bArr5[6] & 255) << 8)) + (bArr5[7] & 255)) << 1)) & 255);
                    if (((byte) (bArr5[0] & ByteCompanionObject.MIN_VALUE)) != 0) {
                        bArr5[7] = (byte) (b3 ^ 27);
                    }
                    byte[] byteArray = byteArrayOutputStream.toByteArray();
                    byte[] bArr6 = new byte[8];
                    System.arraycopy(byteArray, byteArray.length - 8, bArr6, 0, 8);
                    a(bArr6, bArr5, 0, 8);
                    byteArrayOutputStream.reset();
                    byteArrayOutputStream.write(byteArray, 0, byteArray.length - 8);
                    byteArrayOutputStream.write(bArr6, 0, 8);
                }
            }
        }
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0077, code lost:
    
        return r1;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static byte[] a(com.vasco.digipass.sdk.obfuscated.b r6, java.lang.String r7) {
        /*
            byte[] r7 = r7.getBytes()
            r0 = 16
            byte[] r1 = new byte[r0]
            long r2 = r6.b
            int r2 = (int) r2
            r3 = 0
            switch(r2) {
                case 0: goto L50;
                case 1: goto L46;
                case 2: goto L3f;
                case 3: goto L10;
                default: goto Lf;
            }
        Lf:
            goto L77
        L10:
            int r2 = r7.length
            int r2 = 16 - r2
            if (r3 >= r2) goto L24
            int r2 = r3 >> 1
            r4 = r1[r2]
            byte r5 = a(r6, r3)
            r4 = r4 ^ r5
            byte r4 = (byte) r4
            r1[r2] = r4
            int r3 = r3 + 1
            goto L10
        L24:
            int r6 = r7.length
            int r6 = 16 - r6
        L27:
            if (r6 >= r0) goto L77
            int r2 = r6 >> 1
            r3 = r1[r2]
            int r4 = r7.length
            int r4 = 16 - r4
            int r4 = r6 - r4
            r4 = r7[r4]
            byte r4 = b(r4, r6)
            r3 = r3 ^ r4
            byte r3 = (byte) r3
            r1[r2] = r3
            int r6 = r6 + 1
            goto L27
        L3f:
            r0 = 48
            a(r1, r7, r6, r0)
            goto L77
        L46:
            long r2 = r6.c
            r0 = 4
            long r2 = r2 << r0
            int r0 = (int) r2
            byte r0 = (byte) r0
            a(r1, r7, r6, r0)
            goto L77
        L50:
        L51:
            int r2 = r7.length
            if (r3 >= r2) goto L65
            int r2 = r3 >> 1
            r4 = r1[r2]
            r5 = r7[r3]
            byte r5 = b(r5, r3)
            r4 = r4 ^ r5
            byte r4 = (byte) r4
            r1[r2] = r4
            int r3 = r3 + 1
            goto L51
        L65:
            int r7 = r7.length
        L66:
            if (r7 >= r0) goto L77
            int r2 = r7 >> 1
            r3 = r1[r2]
            byte r4 = a(r6, r7)
            r3 = r3 ^ r4
            byte r3 = (byte) r3
            r1[r2] = r3
            int r7 = r7 + 1
            goto L66
        L77:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.obfuscated.k.a(com.vasco.digipass.sdk.obfuscated.b, java.lang.String):byte[]");
    }

    private static byte a(com.vasco.digipass.sdk.obfuscated.b bVar, int i) {
        return (byte) ((i & 1) == 0 ? bVar.c << 4 : bVar.c);
    }

    private static void a(byte[] bArr, byte[] bArr2, com.vasco.digipass.sdk.obfuscated.b bVar, byte b2) {
        for (int i = 0; i < bArr2.length; i++) {
            bArr[i] = (byte) (bArr[i] ^ ((byte) (bArr2[i] & 63)));
            if (i == 7) {
                break;
            }
        }
        for (int length = bArr2.length; length < 8; length++) {
            bArr[length] = (byte) (bArr[length] ^ ((byte) (bVar.c | b2)));
        }
    }

    private static void a(e eVar, d dVar, byte[] bArr, byte[] bArr2, boolean z) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        g gVar = eVar.d;
        byte[] bArr3 = gVar.g;
        if (dVar.b) {
            bArr3 = gVar.i;
        } else if (dVar.c == 100) {
            bArr3 = gVar.p;
        }
        if (bVar.E) {
            b(bArr, bArr2, bArr3);
            return;
        }
        if (bVar.q) {
            a(bArr, bArr2, bArr3);
        } else {
            System.arraycopy(bArr2, 0, bArr, 0, 8);
            for (int i = 8; i <= bArr2.length; i += 8) {
                if (bVar.p) {
                    b(bArr, bArr3);
                } else {
                    c(bArr, bArr3);
                    if (bVar.v) {
                        a(bArr, bArr3);
                    }
                }
                if (i < bArr2.length) {
                    a(bArr, bArr2, i, 8);
                }
            }
        }
        if (bVar.v || bVar.p || bVar.q) {
            return;
        }
        if (dVar.f && !z) {
            a(bArr, bArr3);
        } else if (bVar.s) {
            c(bArr, bArr3);
        }
    }

    private static void a(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        byte[] bArr4;
        if (bArr2.length == 8) {
            bArr4 = new byte[8];
            System.arraycopy(bArr2, 0, bArr4, 4, 4);
        } else {
            int length = bArr2.length - 8;
            byte[] bArr5 = new byte[length];
            System.arraycopy(bArr2, 8, bArr5, 0, length);
            bArr4 = bArr5;
        }
        byte[] bArr6 = new byte[20];
        System.arraycopy(bArr, 4, bArr6, 0, 4);
        System.arraycopy(bArr3, 0, bArr6, 4, bArr3.length);
        byte[] outputData = UtilitiesSDK.hmac((byte) 2, bArr4, bArr6).getOutputData();
        q.g(bArr);
        System.arraycopy(outputData, outputData[19] & 15, bArr, 4, 4);
        bArr[4] = (byte) (bArr[4] & ByteCompanionObject.MAX_VALUE);
        q.g(bArr6);
    }

    private static void a(byte[] bArr, byte[] bArr2) {
        byte[] bArr3;
        byte[] a2 = q.a(bArr2, 0, 8);
        byte[] a3 = q.a(bArr2, 8, 8);
        if (bArr.length == 16) {
            bArr3 = q.a(bArr, 0, bArr.length / 2);
        } else {
            byte[] bArr4 = new byte[8];
            System.arraycopy(bArr, 0, bArr4, 0, 8);
            bArr3 = bArr4;
        }
        byte[] outputData = UtilitiesSDK.encrypt((byte) 1, (byte) 2, a2, null, UtilitiesSDK.decrypt((byte) 1, (byte) 2, a3, null, bArr3).getOutputData()).getOutputData();
        System.arraycopy(outputData, 0, bArr, 0, outputData.length);
        q.g(a2);
        q.g(a3);
    }

    private static byte[] a(d dVar, byte[] bArr, c cVar) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        if (bVar.h) {
            bArr[0] = (byte) (bArr[0] ^ bArr[4]);
            bArr[1] = (byte) (bArr[1] ^ bArr[5]);
            bArr[2] = (byte) (bArr[2] ^ bArr[6]);
            bArr[3] = (byte) (bArr[3] ^ bArr[7]);
        }
        if (dVar.m == 2 && !bVar.m && !bVar.f15o) {
            return a(dVar, bArr);
        }
        if (bVar.r) {
            bArr[2] = (byte) (bArr[2] ^ bArr[0]);
            bArr[3] = (byte) (bArr[3] ^ bArr[1]);
            bArr[4] = (byte) (bArr[4] ^ bArr[6]);
            bArr[5] = (byte) (bArr[5] ^ bArr[7]);
            byte[] a2 = a(dVar, bArr);
            if (dVar.m == 2) {
                System.arraycopy(a2, 2, a2, 0, 8);
            }
            a2[6] = cVar.b;
            a(a2);
            return a2;
        }
        byte[] a3 = a(dVar, bArr);
        if (bVar.l && (dVar.m != 2 || bVar.m)) {
            b(dVar, a3, cVar);
        }
        if (bVar.n && (dVar.m != 2 || bVar.f15o)) {
            c(dVar, a3);
        }
        return a3;
    }

    private static byte[] a(d dVar, byte[] bArr) {
        com.vasco.digipass.sdk.obfuscated.b bVar = dVar.e;
        byte[] bArr2 = new byte[16];
        byte b2 = dVar.m;
        if (b2 == 0) {
            a(bArr, bVar.r ? 6 : 10);
            for (int i = 0; i < 8; i++) {
                int i2 = i * 2;
                bArr2[i2] = (byte) (((bArr[i] >> 4) & 15) + 48);
                bArr2[i2 + 1] = (byte) ((bArr[i] & 15) + 48);
            }
            return bArr2;
        }
        if (b2 != 2) {
            if (b2 != 1) {
                return bArr2;
            }
            System.arraycopy(bArr, 0, bArr2, 0, 8);
            return bArr2;
        }
        String substring = com.vasco.digipass.sdk.obfuscated.c.a(q.a(bArr, 0, bArr.length / 2)).substring(4);
        if (bVar.f15o || bVar.m) {
            System.arraycopy(substring.getBytes(), 0, bArr2, 0, 16);
            return bArr2;
        }
        byte[] bArr3 = new byte[8];
        System.arraycopy(q.a(substring), 0, bArr3, 0, 8);
        return bArr3;
    }

    private static void a(byte[] bArr) {
        for (int i = 0; i < 6; i++) {
            for (int i2 = 0; i2 < 3; i2++) {
                int i3 = i2 + 4;
                byte b2 = bArr[i3];
                bArr[i3] = (byte) a[(((byte) ((b2 & 240) >> 4)) * Tnaf.POW_2_WIDTH) + ((byte) (b2 & 15))];
            }
            byte b3 = bArr[6];
            byte b4 = bArr[5];
            bArr[6] = (byte) (((b4 & 15) << 4) + ((b3 & 240) >> 4));
            byte b5 = bArr[4];
            bArr[5] = (byte) (((b5 & 15) << 4) + ((b4 & 240) >> 4));
            bArr[4] = (byte) ((((byte) (b3 & 15)) << 4) + ((b5 & 240) >> 4));
        }
    }

    private static void a(byte[] bArr, int i) {
        for (int i2 = 0; i2 < 8; i2++) {
            int i3 = bArr[i2];
            if ((i3 & 255) >= 160) {
                bArr[i2] = (byte) (i3 - (i * 16));
            }
            int i4 = bArr[i2];
            if ((i4 & 15) >= 10) {
                bArr[i2] = (byte) (i4 - i);
            }
        }
    }

    private static String a(d dVar, String str) {
        if (!dVar.l) {
            return str;
        }
        boolean z = dVar.m != 1;
        byte[] bytes = str.getBytes();
        byte a2 = j.a(bytes, bytes.length, z);
        if (z) {
            return str + (a2 - 48);
        }
        return str + Integer.toHexString(com.vasco.digipass.sdk.obfuscated.c.a(a2)).toUpperCase();
    }

    private static String a(String str, byte b2, d dVar) {
        int i;
        if (!dVar.e.D) {
            return str;
        }
        int i2 = 0;
        for (int i3 = 0; i3 < str.length(); i3++) {
            if (i3 != 2) {
                i2 += Integer.parseInt(str.substring(i3, i3 + 1), 16);
            }
        }
        int i4 = i2 + b2;
        byte b3 = dVar.m;
        if (b3 != 0 && b3 != 2) {
            i = i4 % 16;
        } else {
            i = i4 % 10;
        }
        return str.substring(0, 2) + Integer.toHexString(i).toUpperCase() + str.substring(3);
    }

    private static GenerationResponse a(int i) {
        return new GenerationResponse(i);
    }

    private static GenerationResponse a(int i, Throwable th) {
        return new GenerationResponse(i, th);
    }

    private static GenerationResponse a(int i, e eVar, String str, String str2) {
        return new GenerationResponse(i, eVar.d.c, p.a(eVar), 0, str, str2);
    }

    public static GenerateDerivationCodeResponse a(byte[] bArr, byte[] bArr2, long j, int i, byte[] bArr3, int i2, byte[] bArr4, boolean z, String str, String str2) {
        String str3;
        try {
            l.a(bArr3, i2);
            if (str != null) {
                str3 = str;
            } else {
                str3 = "";
            }
            b a2 = a(bArr, bArr2, i, bArr3, i2, bArr4, z, str3, true, null, false, null, false, str2, (byte) 0, false);
            String str4 = null;
            if (a2.getReturnCode() != 0) {
                a2.c.f();
                return a(a2.getReturnCode(), a2.c, (String) null, a2.e);
            }
            e eVar = a2.c;
            d a3 = eVar.a(i);
            if (!j.a(eVar, a3.b)) {
                a2.c.f();
                return a(DigipassSDKReturnCodes.TOKEN_DERIVATION_NOT_SUPPORTED, eVar, (String) null, 0);
            }
            byte b2 = j.b(a3);
            if (b2 != 5 && (b2 != 3 || a3.g <= 1)) {
                if (q.d(str2)) {
                    a2.c.f();
                    return a(DigipassSDKReturnCodes.PLATFORM_FINGERPRINT_NOT_DEFINED, eVar, (String) null, 0);
                }
                GenerationResponse a4 = a(eVar, i, a2.d, str2, j, true, (byte) 0);
                if (a2.getReturnCode() == 0) {
                    str4 = a(eVar, str2, a4.getResponse(), i);
                    eVar.d.m = true;
                }
                GenerateDerivationCodeResponse a5 = a(a2.getReturnCode(), eVar, str4, 0);
                a2.c.f();
                return a5;
            }
            a2.c.f();
            return a(DigipassSDKReturnCodes.TOKEN_DERIVATION_NOT_SUPPORTED, eVar, (String) null, 0);
        } catch (h e) {
            return new GenerateDerivationCodeResponse(e.a());
        } catch (Exception e2) {
            return new GenerateDerivationCodeResponse(DigipassSDKReturnCodes.UNKNOWN_ERROR, e2);
        }
    }

    private static String a(e eVar, String str, String str2, int i) {
        int parseInt;
        int length;
        d a2 = eVar.a(i);
        StringBuilder sb = new StringBuilder(Long.toString(com.vasco.digipass.sdk.obfuscated.c.b(j.a(eVar, a2, str, true))));
        byte b2 = eVar.c.v;
        if (b2 != 0 && (length = b2 - sb.length()) > 0) {
            for (int i2 = 0; i2 < length; i2++) {
                sb.insert(0, "0");
            }
        }
        StringBuilder sb2 = new StringBuilder();
        int length2 = a2.l ? str2.length() - 1 : str2.length();
        int i3 = 0;
        while (i3 < sb.length()) {
            int i4 = i3 < length2 ? i3 : i3 % length2;
            String substring = str2.substring(i4, i4 + 1);
            if (a2.m == 1) {
                parseInt = com.vasco.digipass.sdk.obfuscated.c.b(substring);
            } else {
                parseInt = Integer.parseInt(substring);
            }
            sb2.append(((sb.charAt(i3) - '0') + parseInt) % 10);
            i3++;
        }
        return a(a2, ((Object) sb2) + str2.substring(0, length2));
    }

    private static GenerateDerivationCodeResponse a(int i, e eVar, String str, int i2) {
        return new GenerateDerivationCodeResponse(i, eVar.d.c, p.a(eVar), i2, str);
    }

    static String a() {
        byte[] bArr = new byte[17];
        int[] iArr = {79, 68, 84, 68, 49, 54, 64, 79, 87, 93, 90, 56, 95, 85, 79, 64, 61};
        for (int i = 0; i < 17; i++) {
            bArr[i] = (byte) (iArr[i] - i);
        }
        return new String(bArr);
    }
}
