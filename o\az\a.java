package o.az;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\az\a.smali */
final class a extends o.ad.b {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int e;
    private static int h;
    private static char i;
    private static int j;
    private final boolean d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        d();
        Process.myPid();
        ViewConfiguration.getTouchSlop();
        int i2 = j + 61;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'T' : 'b') {
            case Opcodes.FADD /* 98 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void d() {
        i = (char) 33923;
        e = 161105445;
        b = 6565854932352255525L;
    }

    static void init$0() {
        $$d = new byte[]{57, -45, 96, 7};
        $$e = 19;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 + 99
            int r9 = r9 * 3
            int r9 = 1 - r9
            int r7 = r7 * 4
            int r7 = 3 - r7
            byte[] r0 = o.az.a.$$d
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L34:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.a.n(byte, int, int, java.lang.Object[]):void");
    }

    a(Context context, o.c.a aVar, boolean z) {
        super(context, aVar);
        this.d = z;
    }

    @Override // o.ad.b
    public final boolean c() {
        switch (e() ? '+' : (char) 15) {
            case 15:
                return false;
            default:
                int i2 = j + 57;
                h = i2 % 128;
                if (i2 % 2 != 0) {
                }
                if (this.d) {
                    g.c();
                    Object[] objArr = new Object[1];
                    m(ViewConfiguration.getMaximumFlingVelocity() >> 16, "귵\uddad\ue04d纉肥읩櫒畔蘑柅샟웇㍍盬䭷轌溚ᨖ鸏㍅無ꎭ\uf7a5⅐挋䟲\uf012ᇴ\ued96㹀걈덊홻笅잛\uf60c\u09b1쏛", (char) (ViewConfiguration.getDoubleTapTimeout() >> 16), "⻣泘ꩰ嚤", "\u0000\u0000\u0000\u0000", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    m(Color.rgb(0, 0, 0) + 16777216, "厪\ue7d8\ue1a4伍祋㗔愵覨뷎殸⫻缟∯閄巸뀓㽁\ueaf8등ț㌩擭྇똖纞ս祡앵勈䩊\u2429슸墐\ue0c8溧姠ꫨ㐂厏爮囄\udc81ҿ櫖쬦껁찂㐧겙檍㝁爢괽\uf268\udda9᪢₊\ue6bb絑贄ᴕꓚ\ue498墶⠫䗀醯\uf052珢鉖뵋╆삺\ue9c3", (char) View.resolveSizeAndState(0, 0, 0), "ȼ嘧쮗鮎", "\u0000\u0000\u0000\u0000", objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    return true;
                }
                switch (this.a == null) {
                    case true:
                        g.c();
                        Object[] objArr3 = new Object[1];
                        m(TextUtils.lastIndexOf("", '0', 0) + 1, "귵\uddad\ue04d纉肥읩櫒畔蘑柅샟웇㍍盬䭷轌溚ᨖ鸏㍅無ꎭ\uf7a5⅐挋䟲\uf012ᇴ\ued96㹀걈덊홻笅잛\uf60c\u09b1쏛", (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), "⻣泘ꩰ嚤", "\u0000\u0000\u0000\u0000", objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        m((-1963463720) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "ꇈ༥톭ᜄ蝾둓佗则㦝A硴\uec70正⛖᩼쾒뺤⽎ḡꁲ啷䚇⚠\udadd\ud991惦\uda8b矅\ue111⑻ష\udf64\uf299첛\uf6aa刎ற뜋쨇殙䀘燊렴磾\uf472㮜锈忹\ueb88捒픤湷䲿", (char) (20783 - Color.blue(0)), "\ud815\uf7eb⾊籑", "\u0000\u0000\u0000\u0000", objArr4);
                        g.d(intern2, ((String) objArr4[0]).intern());
                        return false;
                    default:
                        int i3 = h + 25;
                        j = i3 % 128;
                        if (i3 % 2 == 0) {
                        }
                        g.c();
                        Object[] objArr5 = new Object[1];
                        m(ViewConfiguration.getScrollDefaultDelay() >> 16, "귵\uddad\ue04d纉肥읩櫒畔蘑柅샟웇㍍盬䭷轌溚ᨖ鸏㍅無ꎭ\uf7a5⅐挋䟲\uf012ᇴ\ued96㹀걈덊홻笅잛\uf60c\u09b1쏛", (char) TextUtils.getTrimmedLength(""), "⻣泘ꩰ嚤", "\u0000\u0000\u0000\u0000", objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        m(Gravity.getAbsoluteGravity(0, 0) - 642264490, "£뒲\ue21dŮ㎛쉽〈⩡緉ୄ䆻㸬㥍ソ㸵\ud8fe忾偷ꕃ\ue213淤藠㛕ᘁ褻\ue788봨阆Ô뜔\ueddf痽견蝵෪磘鵯橜\uea51ꚉ蚅闩ⷉ龪骯⦼\ude2b⦐\ueabc폀㟂砮\ueaa9\uf696㈳\ue4cd籵봧禙啫靫櫄鷩\u0cf5躧\uf47d녃⫌႔跖篧佢毟\udeb1甎\ue30a郼觎쁙딄劅櫰㦢媚\uf1ad\ue9bb鼯쪣➜㍽㘽\u20fb\u0b04퇛縬㇕Ɋ", (char) (TextUtils.indexOf("", "", 0, 0) + 19628), "嚂럒곙͌", "\u0000\u0000\u0000\u0000", objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        return true;
                }
        }
    }

    @Override // o.ad.b
    public final boolean e(o.h.d dVar) {
        int i2 = j + 25;
        h = i2 % 128;
        Object obj = null;
        if (i2 % 2 != 0) {
            b();
            obj.hashCode();
            throw null;
        }
        switch (b() ? 'b' : (char) 3) {
            case 3:
                return false;
            default:
                int i3 = h + 61;
                j = i3 % 128;
                if (i3 % 2 == 0) {
                    throw null;
                }
                if (this.d) {
                    g.c();
                    Object[] objArr = new Object[1];
                    m(Color.rgb(0, 0, 0) + 16777216, "귵\uddad\ue04d纉肥읩櫒畔蘑柅샟웇㍍盬䭷轌溚ᨖ鸏㍅無ꎭ\uf7a5⅐挋䟲\uf012ᇴ\ued96㹀걈덊홻笅잛\uf60c\u09b1쏛", (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "⻣泘ꩰ嚤", "\u0000\u0000\u0000\u0000", objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    m((-1166332367) - ImageFormat.getBitsPerPixel(0), "닺僪\ud8c7짹ꃐ\ue801\ue2a1\udb5dﱑ⼻懏묎쬻婩ꢞ␝穥㟜\ueb1b䄵퍲٪㐄溳秜诧좪\u1257\uecb8᠕醉䫕嗚䕺착⟝䘠ꓵ믹醉氙醉嘙꼬첔䳶☫성\uf773蟏췣턌\ue7aa漣\u0c3cਅ꠬\udb42샭ﬥ㌹Ҧᬸ笝苿這㷩䎛馘嵐\ue745閈\ue838ꠞ縘耂䟅팹ৄ\ue6f9\uf034䏾졿伃헢엤醎", (char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "㉢笮劺쟅", "\u0000\u0000\u0000\u0000", objArr2);
                    g.d(intern, ((String) objArr2[0]).intern());
                    return true;
                }
                if (dVar == null) {
                    return false;
                }
                g.c();
                Object[] objArr3 = new Object[1];
                m(ViewConfiguration.getScrollBarSize() >> 8, "귵\uddad\ue04d纉肥읩櫒畔蘑柅샟웇㍍盬䭷轌溚ᨖ鸏㍅無ꎭ\uf7a5⅐挋䟲\uf012ᇴ\ued96㹀걈덊홻笅잛\uf60c\u09b1쏛", (char) (TextUtils.lastIndexOf("", '0') + 1), "⻣泘ꩰ嚤", "\u0000\u0000\u0000\u0000", objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                m(Drawable.resolveOpacity(0, 0) - 1534176912, "釒ꞝ妾瘲祠嵤稨삷렝\udeee離\udff3툊땻ꪾ⫳⯚鞒\u16fb\udf46뙹꽫めᩧ铇ꃙှ覗꯳껕揈勥熥勍酄\ude0b\u2d9c瀡\ufae9ℨ럦堤Ҹ낮㾺饆\uec41ꤏ엥뷛ᑾ\uf0c5怗䅏촢륻楧砫૧ᐐ堎总ጿヒꕸ灱䆐迣鴰ᧂ각\uf438ࠈ耑褅쇎灶罈졼\udab3髑Ԑ懟뺦뙒柎鄶ִᙄ썟艸쓡嗧\ua4cc྆Ⅷல", (char) (27810 - ExpandableListView.getPackedPositionGroup(0L)), "烋蹑ꊤ푬", "\u0000\u0000\u0000\u0000", objArr4);
                g.d(intern2, ((String) objArr4[0]).intern());
                int i4 = h + 17;
                j = i4 % 128;
                if (i4 % 2 != 0) {
                    return true;
                }
                throw null;
        }
    }

    @Override // o.ad.b
    public final boolean b() {
        int i2 = j + 59;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                super.b();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                switch (super.b()) {
                    default:
                        switch (!this.c.equals(o.j.b.a)) {
                            case true:
                                int i3 = h + Opcodes.LMUL;
                                j = i3 % 128;
                                int i4 = i3 % 2;
                                return true;
                        }
                    case false:
                        return false;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void m(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 672
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.az.a.m(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
