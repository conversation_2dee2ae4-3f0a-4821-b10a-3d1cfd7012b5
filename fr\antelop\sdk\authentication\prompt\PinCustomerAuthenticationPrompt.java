package fr.antelop.sdk.authentication.prompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\PinCustomerAuthenticationPrompt.smali */
public final class PinCustomerAuthenticationPrompt extends CustomerAuthenticationPrompt {
    private final InvalidPinMessageProvider invalidPinMessageProvider;
    private boolean pinCheckDisabled;
    private final String subtitle;
    private final String title;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\PinCustomerAuthenticationPrompt$InvalidPinMessageProvider.smali */
    public interface InvalidPinMessageProvider {
        String getMessage(int i);
    }

    PinCustomerAuthenticationPrompt(String str, String str2, InvalidPinMessageProvider invalidPinMessageProvider, boolean z) {
        this.title = str;
        this.subtitle = str2;
        this.invalidPinMessageProvider = invalidPinMessageProvider;
        this.pinCheckDisabled = z;
    }

    public final String getTitle() {
        return this.title;
    }

    public final String getSubtitle() {
        return this.subtitle;
    }

    public final InvalidPinMessageProvider getInvalidPinMessageProvider() {
        return this.invalidPinMessageProvider;
    }

    public final boolean isPinCheckDisabled() {
        return this.pinCheckDisabled;
    }

    public final void setPinCheckDisabled(boolean z) {
        this.pinCheckDisabled = z;
    }
}
