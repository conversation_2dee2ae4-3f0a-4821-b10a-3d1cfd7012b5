package com.google.firebase.installations.remote;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\installations\remote\AutoValue_TokenResult.smali */
final class AutoValue_TokenResult extends TokenResult {
    private final TokenResult$ResponseCode responseCode;
    private final String token;
    private final long tokenExpirationTimestamp;

    private AutoValue_TokenResult(String token, long tokenExpirationTimestamp, TokenResult$ResponseCode responseCode) {
        this.token = token;
        this.tokenExpirationTimestamp = tokenExpirationTimestamp;
        this.responseCode = responseCode;
    }

    public String getToken() {
        return this.token;
    }

    public long getTokenExpirationTimestamp() {
        return this.tokenExpirationTimestamp;
    }

    public TokenResult$ResponseCode getResponseCode() {
        return this.responseCode;
    }

    public String toString() {
        return "TokenResult{token=" + this.token + ", tokenExpirationTimestamp=" + this.tokenExpirationTimestamp + ", responseCode=" + this.responseCode + "}";
    }

    public boolean equals(Object o2) {
        if (o2 == this) {
            return true;
        }
        if (!(o2 instanceof TokenResult)) {
            return false;
        }
        TokenResult that = (TokenResult) o2;
        String str = this.token;
        if (str != null ? str.equals(that.getToken()) : that.getToken() == null) {
            if (this.tokenExpirationTimestamp == that.getTokenExpirationTimestamp()) {
                TokenResult$ResponseCode tokenResult$ResponseCode = this.responseCode;
                if (tokenResult$ResponseCode == null) {
                    if (that.getResponseCode() == null) {
                        return true;
                    }
                } else if (tokenResult$ResponseCode.equals(that.getResponseCode())) {
                    return true;
                }
            }
        }
        return false;
    }

    public int hashCode() {
        int h$ = 1 * 1000003;
        String str = this.token;
        int hashCode = str == null ? 0 : str.hashCode();
        long j = this.tokenExpirationTimestamp;
        int h$2 = (((h$ ^ hashCode) * 1000003) ^ ((int) (j ^ (j >>> 32)))) * 1000003;
        TokenResult$ResponseCode tokenResult$ResponseCode = this.responseCode;
        return h$2 ^ (tokenResult$ResponseCode != null ? tokenResult$ResponseCode.hashCode() : 0);
    }

    public TokenResult$Builder toBuilder() {
        return new Builder(this);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\installations\remote\AutoValue_TokenResult$Builder.smali */
    static final class Builder extends TokenResult$Builder {
        private TokenResult$ResponseCode responseCode;
        private String token;
        private Long tokenExpirationTimestamp;

        Builder() {
        }

        private Builder(TokenResult source) {
            this.token = source.getToken();
            this.tokenExpirationTimestamp = Long.valueOf(source.getTokenExpirationTimestamp());
            this.responseCode = source.getResponseCode();
        }

        @Override // com.google.firebase.installations.remote.TokenResult$Builder
        public TokenResult$Builder setToken(String token) {
            this.token = token;
            return this;
        }

        @Override // com.google.firebase.installations.remote.TokenResult$Builder
        public TokenResult$Builder setTokenExpirationTimestamp(long tokenExpirationTimestamp) {
            this.tokenExpirationTimestamp = Long.valueOf(tokenExpirationTimestamp);
            return this;
        }

        @Override // com.google.firebase.installations.remote.TokenResult$Builder
        public TokenResult$Builder setResponseCode(TokenResult$ResponseCode responseCode) {
            this.responseCode = responseCode;
            return this;
        }

        @Override // com.google.firebase.installations.remote.TokenResult$Builder
        public TokenResult build() {
            String missing = this.tokenExpirationTimestamp == null ? " tokenExpirationTimestamp" : "";
            if (!missing.isEmpty()) {
                throw new IllegalStateException("Missing required properties:" + missing);
            }
            return new AutoValue_TokenResult(this.token, this.tokenExpirationTimestamp.longValue(), this.responseCode);
        }
    }
}
