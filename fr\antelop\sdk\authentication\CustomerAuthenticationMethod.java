package fr.antelop.sdk.authentication;

import android.content.Context;
import android.os.CancellationSignal;
import fr.antelop.sdk.ForeignCurrencySupport;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.math.BigDecimal;
import java.util.Set;
import javax.crypto.Cipher;
import o.i.g;
import o.i.i;
import o.i.j;
import o.i.n;
import o.q.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationMethod.smali */
public final class CustomerAuthenticationMethod {
    public static final short CURRENT_ATTEMPT_COUNT_NOT_SET = -1;
    public static final short MAXIMUM_ATTEMPT_COUNT_NOT_SET = -1;

    @Deprecated
    public static final String NO_AUTHENTICATION_METHODE_NAME = "noAuthentication";
    private final g innerCustomerAuthenticationMethod;

    public CustomerAuthenticationMethod(g gVar) {
        this.innerCustomerAuthenticationMethod = gVar;
    }

    public final Short getStrength() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return Short.valueOf(f.a());
    }

    public final BigDecimal getMaxPaymentAmount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.d();
    }

    public final BigDecimal getMaxPaymentCumulativeAmount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.c();
    }

    public final BigDecimal getCurrentPaymentCumulativeAmount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.i();
    }

    public final Integer getMaxPaymentCount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.h();
    }

    public final Integer getCurrentPaymentCount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.f();
    }

    public final BigDecimal getAllowedPaymentAmount() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.j();
    }

    public final Integer getAuthenticationDuration() {
        return this.innerCustomerAuthenticationMethod.g();
    }

    public final short getCurrentAttemptsCount() {
        g gVar = this.innerCustomerAuthenticationMethod;
        if (!(gVar instanceof n)) {
            return (short) -1;
        }
        return ((n) gVar).e();
    }

    public final short getMaximumAttemptsCount() {
        g gVar = this.innerCustomerAuthenticationMethod;
        if (!(gVar instanceof n)) {
            return (short) -1;
        }
        return ((n) gVar).b();
    }

    public final CryptoObject getAuthenticationCryptoObject() {
        Cipher b;
        g gVar = this.innerCustomerAuthenticationMethod;
        if (!(gVar instanceof j) || (b = ((j) gVar).b()) == null) {
            return null;
        }
        return new CryptoObject(b);
    }

    public final ForeignCurrencySupport getForeignCurrencySupport() {
        d f = this.innerCustomerAuthenticationMethod.f();
        if (f == null) {
            return null;
        }
        return f.g().d();
    }

    public final CustomerAuthenticationMethodStatus getStatus() {
        return this.innerCustomerAuthenticationMethod.j().e();
    }

    public final CustomerAuthenticationMethodType getType() {
        return this.innerCustomerAuthenticationMethod.i().b();
    }

    public final Set<CustomerAuthenticationMethodUsage> getUsages() {
        return i.d(this.innerCustomerAuthenticationMethod.h());
    }

    @Deprecated
    public final void promptCustomer(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt, CustomerAuthenticationPromptCallback customerAuthenticationPromptCallback) throws WalletValidationException {
        this.innerCustomerAuthenticationMethod.c(context, customerAuthenticationPrompt, customerAuthenticationPromptCallback, null, null, null);
    }

    public final void promptCustomer(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt, CustomerAuthenticationPromptCallback customerAuthenticationPromptCallback, CancellationSignal cancellationSignal) throws WalletValidationException {
        this.innerCustomerAuthenticationMethod.c(context, customerAuthenticationPrompt, customerAuthenticationPromptCallback, null, null, cancellationSignal);
    }

    @Deprecated
    public final void promptCustomer(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt, CustomerAuthenticationPromptCallback customerAuthenticationPromptCallback, CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationPrompt customerAuthenticationPrompt2) throws WalletValidationException {
        this.innerCustomerAuthenticationMethod.c(context, customerAuthenticationPrompt, customerAuthenticationPromptCallback, customerAuthenticationMethodType, customerAuthenticationPrompt2, null);
    }

    public final void promptCustomer(Context context, CustomerAuthenticationPrompt customerAuthenticationPrompt, CustomerAuthenticationPromptCallback customerAuthenticationPromptCallback, CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationPrompt customerAuthenticationPrompt2, CancellationSignal cancellationSignal) throws WalletValidationException {
        this.innerCustomerAuthenticationMethod.c(context, customerAuthenticationPrompt, customerAuthenticationPromptCallback, customerAuthenticationMethodType, customerAuthenticationPrompt2, cancellationSignal);
    }

    public final String toString() {
        return new StringBuilder("CustomerAuthenticationMethod{strength=").append(getStrength() == null ? "" : getStrength()).append(", maxPaymentAmount=").append(getMaxPaymentAmount() == null ? "" : getMaxPaymentAmount()).append(", maxPaymentCumulativeAmount=").append(getMaxPaymentCumulativeAmount() == null ? "" : getMaxPaymentCumulativeAmount()).append(", currentPaymentCumulativeAmount=").append(getCurrentPaymentCumulativeAmount() == null ? "" : getCurrentPaymentCumulativeAmount()).append(", maxPaymentCount=").append(getMaxPaymentCount() == null ? "" : getMaxPaymentCount()).append(", currentPaymentCount=").append(getCurrentPaymentCount() == null ? "" : getCurrentPaymentCount()).append(", allowedPaymentAmount=").append(getAllowedPaymentAmount() == null ? "" : getAllowedPaymentAmount()).append(", authenticationDuration=").append(getAuthenticationDuration() == null ? "" : getAuthenticationDuration()).append(", currentAttemptsCount=").append((int) getCurrentAttemptsCount()).append(", maximumAttempsCount=").append((int) getMaximumAttemptsCount()).append(", authenticationCryptoObject=").append(getAuthenticationCryptoObject() == null ? "" : getAuthenticationCryptoObject().toString()).append(", foreignCurrencySupport=").append(getForeignCurrencySupport() != null ? getForeignCurrencySupport() : "").append(", status=").append(getStatus()).append(", type=").append(getType()).append(", usages=").append(getUsages()).append('}').toString();
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationMethod$CryptoObject.smali */
    public static final class CryptoObject {
        private final Cipher cipher;

        public CryptoObject(Cipher cipher) {
            this.cipher = cipher;
        }

        public final Cipher getCipher() {
            return this.cipher;
        }

        public final String toString() {
            return new StringBuilder("CryptoObject{cipher=").append(this.cipher).append('}').toString();
        }
    }
}
