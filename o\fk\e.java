package o.fk;

import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\e.smali */
public final class e {
    private boolean b;
    private boolean c;
    private boolean e;
    private static int d = 0;
    private static int a = 1;

    public final void a() {
        int i = a;
        int i2 = (i + 92) - 1;
        d = i2 % 128;
        switch (i2 % 2 != 0 ? '5' : '9') {
            case Opcodes.SALOAD /* 53 */:
                this.e = false;
                break;
            default:
                this.e = true;
                break;
        }
        int i3 = ((i | 35) << 1) - (i ^ 35);
        d = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 14 : 'S') {
            case Opcodes.AASTORE /* 83 */:
                return;
            default:
                throw null;
        }
    }

    public final boolean b() {
        boolean z;
        int i = d;
        int i2 = (i ^ 85) + ((i & 85) << 1);
        a = i2 % 128;
        switch (i2 % 2 == 0 ? '%' : 'H') {
            case '%':
                z = this.e;
                int i3 = 29 / 0;
                break;
            default:
                z = this.e;
                break;
        }
        int i4 = ((i | 55) << 1) - (i ^ 55);
        a = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return z;
        }
    }

    public final void c() {
        int i = d;
        int i2 = ((i | 91) << 1) - (i ^ 91);
        a = i2 % 128;
        switch (i2 % 2 == 0 ? ']' : Typography.less) {
            case Opcodes.DUP2_X1 /* 93 */:
                this.b = false;
                break;
            default:
                this.b = true;
                break;
        }
        int i3 = (i + Opcodes.IREM) - 1;
        a = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    public final boolean d() {
        int i = a + 37;
        int i2 = i % 128;
        d = i2;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                boolean z = this.b;
                int i3 = (i2 + 80) - 1;
                a = i3 % 128;
                switch (i3 % 2 == 0 ? ')' : '\b') {
                    case '\b':
                        return z;
                    default:
                        throw null;
                }
        }
    }

    public final void e() {
        int i = d;
        int i2 = ((i | 95) << 1) - (i ^ 95);
        int i3 = i2 % 128;
        a = i3;
        int i4 = i2 % 2;
        this.c = true;
        int i5 = i3 + 109;
        d = i5 % 128;
        int i6 = i5 % 2;
    }

    public final boolean g() {
        boolean z;
        int i = a;
        int i2 = (i ^ 1) + ((i & 1) << 1);
        d = i2 % 128;
        switch (i2 % 2 != 0 ? 'D' : (char) 18) {
            case 'D':
                z = this.c;
                int i3 = 54 / 0;
                break;
            default:
                z = this.c;
                break;
        }
        int i4 = ((i | Opcodes.DSUB) << 1) - (i ^ Opcodes.DSUB);
        d = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 4 : (char) 1) {
            case 1:
                return z;
            default:
                int i5 = 51 / 0;
                return z;
        }
    }
}
