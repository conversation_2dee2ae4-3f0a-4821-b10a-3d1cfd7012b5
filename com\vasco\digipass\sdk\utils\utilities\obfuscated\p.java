package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p.smali */
public abstract class p extends b0 {
    static final o0 x = new a(p.class, 22);
    final byte[] b;

    p(byte[] bArr, boolean z) {
        this.b = z ? Arrays.clone(bArr) : bArr;
    }

    static p b(byte[] bArr) {
        return new c2(bArr, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final int a(boolean z) {
        return z.a(z, this.b.length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean e() {
        return false;
    }

    public final String h() {
        return o7.b(this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public final int hashCode() {
        return Arrays.hashCode(this.b);
    }

    public String toString() {
        return h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 22, this.b);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean a(b0 b0Var) {
        if (b0Var instanceof p) {
            return Arrays.areEqual(this.b, ((p) b0Var).b);
        }
        return false;
    }
}
