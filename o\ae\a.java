package o.ae;

import android.content.Context;
import android.media.AudioTrack;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.n;
import o.bb.d;
import o.cf.i;
import o.cf.j;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ae\a.smali */
public final class a extends o.y.b<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static int c;
    String d;
    String e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ae\a$e.smali */
    public interface e {
        void a(d dVar);

        void c();
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        a = 1;
        l();
        TextUtils.getTrimmedLength("");
        View.resolveSize(0, 0);
        int i = a + 99;
        c = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    static void init$0() {
        $$d = new byte[]{117, -111, 19, -37};
        $$e = 77;
    }

    static void l() {
        b = 4550667665353720192L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.ae.a.$$d
            int r6 = r6 * 2
            int r6 = r6 + 112
            int r8 = r8 * 3
            int r8 = 3 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r8 = r8 + 1
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ae.a.m(byte, byte, short, java.lang.Object[]):void");
    }

    public a(Context context, e eVar, c cVar) {
        super(context, eVar, cVar, o.bb.e.D);
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = c + 53;
        a = i % 128;
        switch (i % 2 == 0 ? '4' : 'D') {
            case '4':
                Object[] objArr = new Object[1];
                k("ݔ辺ᛢ鴘\u2458ꭾ㎏뫚䄇젺录\ue781滅\uf5e7簫͒讴ዀ駵\u2028띒㾨욵䷤퐕孆\ue278檡", 35023 << (ViewConfiguration.getScrollBarSize() - 93), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                k("ݔ辺ᛢ鴘\u2458ꭾ㎏뫚䄇젺录\ue781滅\uf5e7簫͒讴ዀ駵\u2028띒㾨욵䷤퐕孆\ue278檡", 35023 - (ViewConfiguration.getScrollBarSize() >> 8), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = c + 19;
        a = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        b bVar = new b(this);
        int i = c + 9;
        a = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    public final void a(String str, String str2) {
        int i = c + 57;
        a = i % 128;
        switch (i % 2 == 0) {
            case true:
                this.e = str;
                this.d = str2;
                c();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                this.e = str;
                this.d = str2;
                c();
                return;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ae\a$b.smali */
    static final class b extends o.y.c<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int c;
        private static int d;
        private static long e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            c = 1;
            e = -4636687069256185624L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002f  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0027  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002f -> B:4:0x0037). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(short r7, byte r8, int r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.ae.a.b.$$d
                int r8 = r8 * 4
                int r8 = 3 - r8
                int r9 = r9 * 4
                int r9 = r9 + 1
                int r7 = r7 * 3
                int r7 = r7 + 68
                byte[] r1 = new byte[r9]
                r2 = 0
                if (r0 != 0) goto L1a
                r7 = r8
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r9
                goto L37
            L1a:
                r3 = r2
                r6 = r8
                r8 = r7
                r7 = r6
            L1e:
                int r4 = r3 + 1
                byte r5 = (byte) r8
                r1[r3] = r5
                int r7 = r7 + 1
                if (r4 != r9) goto L2f
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L2f:
                r3 = r0[r7]
                r6 = r10
                r10 = r9
                r9 = r3
                r3 = r1
                r1 = r0
                r0 = r6
            L37:
                int r8 = r8 + r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1e
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ae.a.b.B(short, byte, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{12, 98, 124, -66};
            $$e = 210;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 1;
            c = i % 128;
            switch (i % 2 == 0 ? 'V' : '_') {
                case Opcodes.SWAP /* 95 */:
                    break;
                default:
                    int i2 = 94 / 0;
                    break;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = c + Opcodes.DSUB;
            d = i % 128;
            int i2 = i % 2;
        }

        b(a aVar) {
            super(aVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = c + Opcodes.LUSHR;
            d = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("✴\udf02\uf02c❐⩽ᝐ怮犽ޜ㙴䄣原曣횀≙끔䗁\uf5bd͇鄧ꐐ铊\ue399\uf60d茖", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 1, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = c + 5;
            d = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("瓇뻱村瓴ꗩ盷\uf708ﵽ含埚혭\udc5a㕋뜧땂㾁ᙡ鐃鑧ợ\uf786\uf56f璷", View.getDefaultSize(0, 0) + 1, objArr);
            o.cf.d dVar = new o.cf.d(context, 46, ((String) objArr[0]).intern());
            int i = c + 37;
            d = i % 128;
            switch (i % 2 != 0 ? (char) 17 : 'c') {
                case Opcodes.DADD /* 99 */:
                    return dVar;
                default:
                    int i2 = 52 / 0;
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("\ue39d쌢\ueac4\ue3fe㟟୴竘漞쌈⩕", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 1, objArr);
            bVar.d(((String) objArr[0]).intern(), ((a) e()).e);
            int i = d + 93;
            c = i % 128;
            switch (i % 2 != 0) {
                case false:
                    int i2 = 65 / 0;
                    return bVar;
                default:
                    return bVar;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = c + 51;
            d = i % 128;
            switch (i % 2 != 0 ? (char) 11 : '+') {
                case '+':
                    return null;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = c + 67;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.LNEG;
            c = i4 % 128;
            switch (i4 % 2 != 0) {
                case false:
                    throw null;
                default:
                    return null;
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:11:0x0026  */
        /* JADX WARN: Removed duplicated region for block: B:6:0x0020  */
        /* JADX WARN: Removed duplicated region for block: B:9:0x0023  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.bb.a c(int r3) {
            /*
                r2 = this;
                int r0 = o.ae.a.b.c
                int r0 = r0 + 53
                int r1 = r0 % 128
                o.ae.a.b.d = r1
                int r0 = r0 % 2
                r1 = 0
                if (r0 == 0) goto Lf
                r0 = 1
                goto L10
            Lf:
                r0 = r1
            L10:
                switch(r0) {
                    case 1: goto L17;
                    default: goto L13;
                }
            L13:
                switch(r3) {
                    case 5001: goto L23;
                    case 5002: goto L20;
                    default: goto L16;
                }
            L16:
                goto L26
            L17:
                r0 = 58
                int r0 = r0 / r1
                switch(r3) {
                    case 5001: goto L23;
                    case 5002: goto L20;
                    default: goto L1d;
                }
            L1d:
                goto L16
            L1e:
                r3 = move-exception
                throw r3
            L20:
                o.bb.a r3 = o.bb.a.az
                return r3
            L23:
                o.bb.a r3 = o.bb.a.ay
                return r3
            L26:
                o.bb.a r3 = super.c(r3)
                int r0 = o.ae.a.b.d
                int r0 = r0 + 41
                int r1 = r0 % 128
                o.ae.a.b.c = r1
                int r0 = r0 % 2
                return r3
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ae.a.b.c(int):o.bb.a");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + 43;
            d = i % 128;
            switch (i % 2 == 0) {
                case false:
                    int i2 = AnonymousClass1.e[h().d().ordinal()];
                    throw null;
                default:
                    switch (AnonymousClass1.e[h().d().ordinal()]) {
                        case 1:
                            f().c(g(), ((a) e()).e);
                            return;
                        case 2:
                            f().e(g(), ((a) e()).e);
                            return;
                        default:
                            super.t();
                            int i3 = c + 41;
                            d = i3 % 128;
                            int i4 = i3 % 2;
                            return;
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            int i = c + 97;
            d = i % 128;
            int i2 = i % 2;
            f().a().i().a(((a) e()).e, ((a) e()).d, o.eo.b.c);
            f().d(g());
            int i3 = d + 51;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(d dVar) {
            int i = d + 65;
            c = i % 128;
            switch (i % 2 == 0 ? ',' : '1') {
                case ',':
                    ((a) e()).j().c();
                    int i2 = 10 / 0;
                    break;
                default:
                    ((a) e()).j().c();
                    break;
            }
            int i3 = c + 91;
            d = i3 % 128;
            switch (i3 % 2 != 0) {
                case true:
                    int i4 = 63 / 0;
                    return;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(d dVar) {
            int i = d + Opcodes.DMUL;
            c = i % 128;
            switch (i % 2 == 0) {
                case false:
                    ((a) e()).j().a(dVar);
                    return;
                default:
                    ((a) e()).j().a(dVar);
                    int i2 = 16 / 0;
                    return;
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:43:0x002c. Please report as an issue. */
        private static void w(String str, int i, Object[] objArr) {
            char[] charArray;
            switch (str == null) {
                case false:
                    int i2 = $11 + 23;
                    $10 = i2 % 128;
                    int i3 = i2 % 2;
                    charArray = str.toCharArray();
                    int i4 = $10 + 25;
                    $11 = i4 % 128;
                    switch (i4 % 2 != 0) {
                    }
                default:
                    charArray = str;
                    break;
            }
            n nVar = new n();
            char[] b = n.b(e ^ 8632603938177761503L, charArray, i);
            nVar.c = 4;
            while (nVar.c < b.length) {
                nVar.e = nVar.c - 4;
                int i5 = nVar.c;
                try {
                    Object[] objArr2 = {Long.valueOf(b[nVar.c] ^ b[nVar.c % 4]), Long.valueOf(nVar.e), Long.valueOf(e)};
                    Object obj = o.e.a.s.get(-1945790373);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(12 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 42 - ((byte) KeyEvent.getModifierMetaStateMask()));
                        byte b2 = (byte) 0;
                        byte b3 = b2;
                        Object[] objArr3 = new Object[1];
                        B(b2, b3, b3, objArr3);
                        obj = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                        o.e.a.s.put(-1945790373, obj);
                    }
                    b[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    try {
                        Object[] objArr4 = {nVar, nVar};
                        Object obj2 = o.e.a.s.get(-341518981);
                        if (obj2 == null) {
                            Class cls2 = (Class) o.e.a.c(10 - ExpandableListView.getPackedPositionType(0L), (char) TextUtils.indexOf("", ""), 248 - TextUtils.indexOf((CharSequence) "", '0', 0));
                            byte b4 = (byte) 1;
                            byte b5 = (byte) (b4 - 1);
                            Object[] objArr5 = new Object[1];
                            B(b4, b5, b5, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                            o.e.a.s.put(-341518981, obj2);
                        }
                        ((Method) obj2).invoke(null, objArr4);
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            objArr[0] = new String(b, 4, b.length - 4);
        }
    }

    /* renamed from: o.ae.a$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ae\a$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int c;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            d = 0;
            c = 1;
            int[] iArr = new int[o.bb.a.values().length];
            e = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = d;
                int i2 = ((i | 67) << 1) - (i ^ 67);
                c = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.bb.a.az.ordinal()] = 2;
                int i3 = c;
                int i4 = (i3 ^ 63) + ((i3 & 63) << 1);
                d = i4 % 128;
                switch (i4 % 2 == 0 ? '\'' : (char) 0) {
                    case 0:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:41:0x015a  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 526
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ae.a.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
