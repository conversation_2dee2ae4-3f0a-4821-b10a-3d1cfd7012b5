package fr.antelop.sdk.authentication.prompt;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\prompt\CustomerAuthenticationPromptBuilder.smali */
public abstract class CustomerAuthenticationPromptBuilder {
    public abstract CustomerAuthenticationPrompt build();

    public final int hashCode() {
        return super.hashCode();
    }

    public final boolean equals(Object obj) {
        return super.equals(obj);
    }

    public final String toString() {
        return super.toString();
    }

    protected final void finalize() throws Throwable {
        super.finalize();
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
