package com.google.android.play.core.appupdate;

import android.content.Context;

/* compiled from: com.google.android.play:app-update@@2.1.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\appupdate\AppUpdateManagerFactory.smali */
public final class AppUpdateManagerFactory {
    private AppUpdateManagerFactory() {
    }

    public static AppUpdateManager create(Context context) {
        return zzb.zza(context).zza();
    }
}
