package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfk.smali */
abstract class zzfk {
    private static final zzfk zza = new zzfg(null);
    private static final zzfk zzb = new zzfi(0 == true ? 1 : 0);

    /* synthetic */ zzfk(zzfj zzfjVar) {
    }

    static zzfk zzc() {
        return zza;
    }

    static zzfk zzd() {
        return zzb;
    }

    abstract void zza(Object obj, long j);

    abstract void zzb(Object obj, Object obj2, long j);
}
