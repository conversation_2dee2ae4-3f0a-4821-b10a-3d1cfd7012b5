package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.os.Bundle;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.samsung.android.sdk.samsungpay.v2.card.GetCardListener;
import fr.antelop.sdk.AntelopErrorCode;
import java.util.List;
import o.ee.g;
import o.ep.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayGetCardListener.smali */
public class SamsungPayGetCardListener implements GetCardListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int d;
    private static int e;
    private final a.InterfaceC0042a<List<o.ep.e>> a;
    private final c c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        e = 1;
        d();
        ViewConfiguration.getTapTimeout();
        int i = e + 35;
        d = i % 128;
        int i2 = i % 2;
    }

    static void d() {
        b = -3613941206622018502L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 1
            int r7 = r7 + 4
            int r6 = r6 * 2
            int r6 = 114 - r6
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayGetCardListener.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r5
        L1c:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            int r6 = r6 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayGetCardListener.g(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{91, -22, 50, -29};
        $$b = 95;
    }

    protected SamsungPayGetCardListener(c cVar, a.InterfaceC0042a<List<o.ep.e>> interfaceC0042a) {
        this.c = cVar;
        this.a = interfaceC0042a;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public void onSuccess(java.util.List<com.samsung.android.sdk.samsungpay.v2.card.Card> r15) {
        /*
            Method dump skipped, instructions count: 484
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayGetCardListener.onSuccess(java.util.List):void");
    }

    public void onFail(int i, Bundle bundle) {
        String obj;
        g.c();
        Object[] objArr = new Object[1];
        f("⋹ꭞ\u31ed빦ҋ购᮳\ue1e9湣\uf4ee紫쮹倷\ude4fꓹⵢ뮶&躣ᓑ鵫毽\uf001纻", View.getDefaultSize(0, 0) + 35221, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("⋍㽈᧐空哘녻鏯\uec77컾⭰ֈ昳䂑崚뾭頭睊흽ㇹᎏ汉仗ꭶ藪\ue66b샩\udd3c㾺ᡎ窄圊놁鈉\ueca2줠⮺Ѷ易䂀", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 7559, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        f("⊊橊댐\uf8afǫ仅阀\udf5d撧궿朗Ʌ", 18637 - KeyEvent.getDeadChar(0, 0), objArr3);
        StringBuilder append2 = append.append(((String) objArr3[0]).intern());
        if (bundle == null) {
            int i2 = d + 109;
            e = i2 % 128;
            switch (i2 % 2 == 0 ? 'G' : (char) 25) {
                case 25:
                    Object[] objArr4 = new Object[1];
                    f("⋄溔멐옧", KeyEvent.keyCodeFromString("") + 19531, objArr4);
                    obj = ((String) objArr4[0]).intern();
                    break;
                default:
                    Object[] objArr5 = new Object[1];
                    f("⋄溔멐옧", 13217 >>> KeyEvent.keyCodeFromString(""), objArr5);
                    obj = ((String) objArr5[0]).intern();
                    break;
            }
        } else {
            obj = bundle.toString();
        }
        g.d(intern, append2.append(obj).toString());
        this.a.e(new o.bv.c(AntelopErrorCode.SamsungPayWalletNotAvailable));
        int i3 = e + 63;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0027. Please report as an issue. */
    private static void f(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 514
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayGetCardListener.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
