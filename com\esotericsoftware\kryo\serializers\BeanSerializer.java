package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.minlog.Log;
import com.esotericsoftware.reflectasm.MethodAccess;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\BeanSerializer.smali */
public class BeanSerializer<T> extends Serializer<T> {
    static final Object[] noArgs = new Object[0];
    Object access;
    private CachedProperty[] properties;

    public BeanSerializer(Kryo kryo, Class type) {
        try {
            BeanInfo info = Introspector.getBeanInfo(type);
            PropertyDescriptor[] descriptors = info.getPropertyDescriptors();
            Arrays.sort(descriptors, new Comparator<PropertyDescriptor>() { // from class: com.esotericsoftware.kryo.serializers.BeanSerializer.1
                @Override // java.util.Comparator
                public int compare(PropertyDescriptor o1, PropertyDescriptor o2) {
                    return o1.getName().compareTo(o2.getName());
                }
            });
            ArrayList<CachedProperty> cachedProperties = new ArrayList<>(descriptors.length);
            for (PropertyDescriptor property : descriptors) {
                String name = property.getName();
                if (!name.equals("class")) {
                    Method getMethod = property.getReadMethod();
                    Method setMethod = property.getWriteMethod();
                    if (getMethod != null && setMethod != null) {
                        Class returnType = getMethod.getReturnType();
                        Serializer serializer = kryo.isFinal(returnType) ? kryo.getRegistration(returnType).getSerializer() : null;
                        CachedProperty cachedProperty = new CachedProperty();
                        cachedProperty.name = name;
                        cachedProperty.getMethod = getMethod;
                        cachedProperty.setMethod = setMethod;
                        cachedProperty.serializer = serializer;
                        cachedProperty.setMethodType = setMethod.getParameterTypes()[0];
                        cachedProperties.add(cachedProperty);
                    }
                }
            }
            int i = cachedProperties.size();
            this.properties = (CachedProperty[]) cachedProperties.toArray(new CachedProperty[i]);
            try {
                this.access = MethodAccess.get(type);
                int n = this.properties.length;
                for (int i2 = 0; i2 < n; i2++) {
                    CachedProperty property2 = this.properties[i2];
                    property2.getterAccessIndex = ((MethodAccess) this.access).getIndex(property2.getMethod.getName(), property2.getMethod.getParameterTypes());
                    property2.setterAccessIndex = ((MethodAccess) this.access).getIndex(property2.setMethod.getName(), property2.setMethod.getParameterTypes());
                }
            } catch (Throwable th) {
            }
        } catch (IntrospectionException ex) {
            throw new KryoException("Error getting bean info.", ex);
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        Class type = object.getClass();
        int n = this.properties.length;
        for (int i = 0; i < n; i++) {
            CachedProperty property = this.properties[i];
            try {
                if (Log.TRACE) {
                    Log.trace("kryo", "Write property: " + property + " (" + type.getName() + ")");
                }
                Object value = property.get(object);
                Serializer serializer = property.serializer;
                if (serializer != null) {
                    kryo.writeObjectOrNull(output, value, serializer);
                } else {
                    kryo.writeClassAndObject(output, value);
                }
            } catch (KryoException ex) {
                ex.addTrace(property + " (" + type.getName() + ")");
                throw ex;
            } catch (IllegalAccessException ex2) {
                throw new KryoException("Error accessing getter method: " + property + " (" + type.getName() + ")", ex2);
            } catch (InvocationTargetException ex3) {
                throw new KryoException("Error invoking getter method: " + property + " (" + type.getName() + ")", ex3);
            } catch (Throwable t) {
                KryoException ex4 = new KryoException(t);
                ex4.addTrace(property + " (" + type.getName() + ")");
                throw ex4;
            }
        }
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> cls) {
        Object readClassAndObject;
        T t = (T) kryo.newInstance(cls);
        kryo.reference(t);
        int length = this.properties.length;
        for (int i = 0; i < length; i++) {
            CachedProperty cachedProperty = this.properties[i];
            try {
                if (Log.TRACE) {
                    Log.trace("kryo", "Read property: " + cachedProperty + " (" + t.getClass() + ")");
                }
                Serializer serializer = cachedProperty.serializer;
                if (serializer != null) {
                    readClassAndObject = kryo.readObjectOrNull(input, cachedProperty.setMethodType, serializer);
                } else {
                    readClassAndObject = kryo.readClassAndObject(input);
                }
                cachedProperty.set(t, readClassAndObject);
            } catch (KryoException e) {
                e.addTrace(cachedProperty + " (" + t.getClass().getName() + ")");
                throw e;
            } catch (IllegalAccessException e2) {
                throw new KryoException("Error accessing setter method: " + cachedProperty + " (" + t.getClass().getName() + ")", e2);
            } catch (InvocationTargetException e3) {
                throw new KryoException("Error invoking setter method: " + cachedProperty + " (" + t.getClass().getName() + ")", e3);
            } catch (Throwable th) {
                KryoException kryoException = new KryoException(th);
                kryoException.addTrace(cachedProperty + " (" + t.getClass().getName() + ")");
                throw kryoException;
            }
        }
        return t;
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T copy(Kryo kryo, T t) {
        T t2 = (T) kryo.newInstance(t.getClass());
        int length = this.properties.length;
        for (int i = 0; i < length; i++) {
            CachedProperty cachedProperty = this.properties[i];
            try {
                cachedProperty.set(t2, cachedProperty.get(t));
            } catch (KryoException e) {
                e.addTrace(cachedProperty + " (" + t2.getClass().getName() + ")");
                throw e;
            } catch (Exception e2) {
                throw new KryoException("Error copying bean property: " + cachedProperty + " (" + t2.getClass().getName() + ")", e2);
            } catch (Throwable th) {
                KryoException kryoException = new KryoException(th);
                kryoException.addTrace(cachedProperty + " (" + t2.getClass().getName() + ")");
                throw kryoException;
            }
        }
        return t2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\BeanSerializer$CachedProperty.smali */
    class CachedProperty<X> {
        Method getMethod;
        int getterAccessIndex;
        String name;
        Serializer serializer;
        Method setMethod;
        Class setMethodType;
        int setterAccessIndex;

        CachedProperty() {
        }

        public String toString() {
            return this.name;
        }

        Object get(Object object) throws IllegalAccessException, InvocationTargetException {
            return BeanSerializer.this.access != null ? ((MethodAccess) BeanSerializer.this.access).invoke(object, this.getterAccessIndex, new Object[0]) : this.getMethod.invoke(object, BeanSerializer.noArgs);
        }

        void set(Object object, Object value) throws IllegalAccessException, InvocationTargetException {
            if (BeanSerializer.this.access != null) {
                ((MethodAccess) BeanSerializer.this.access).invoke(object, this.setterAccessIndex, value);
            } else {
                this.setMethod.invoke(object, value);
            }
        }
    }
}
