package com.google.android.gms.common.config;

import com.google.android.gms.common.internal.Preconditions;

/* compiled from: com.google.android.gms:play-services-basement@@18.3.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\config\zze.smali */
final class zze extends GservicesValue {
    zze(String str, String str2) {
        super(str, str2);
    }

    @Override // com.google.android.gms.common.config.GservicesValue
    protected final /* bridge */ /* synthetic */ Object zza(String str) {
        Preconditions.checkNotNull(null);
        throw null;
    }
}
