package o.v;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.an.c;
import o.eo.f;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\h.smali */
public final class h extends c<c.e, c.a, o.an.c<o.ep.c>, o.ep.c> {
    public static final byte[] $$j = null;
    public static final int $$k = 0;
    private static int $10;
    private static int $11;
    private static int l;
    private static char[] m;
    private static boolean p;
    private static int r;
    private static boolean s;
    private static int t;
    private o.ee.i k;

    /* renamed from: o, reason: collision with root package name */
    private Activity f104o;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        t = 1;
        w();
        TextUtils.lastIndexOf("", '0');
        int i = t + Opcodes.LSUB;
        r = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void F(short r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 2
            int r9 = 4 - r9
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r8 = 121 - r8
            byte[] r0 = o.v.h.$$j
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L18:
            r3 = r2
            r6 = r9
            r9 = r8
        L1b:
            r8 = r6
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L35:
            int r9 = r9 + 1
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.h.F(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$j = new byte[]{1, 28, -75, 69};
        $$k = 2;
    }

    static void w() {
        m = new char[]{61779, 61814, 61807, 61818, 61785, 61801, 61823, 61768, 61811, 61805, 61816, 61803, 61808, 61769, 61800, 61780, 61817, 61804, 61784, 61813, 61773, 61571, 61732, 61822, 61796, 61812, 61821, 61776, 61802};
        p = true;
        s = true;
        l = 782102788;
    }

    @Override // o.v.c
    final /* synthetic */ o.ep.c b(Context context) {
        int i = r + 65;
        t = i % 128;
        int i2 = i % 2;
        o.ep.c c = c(context);
        int i3 = r + 23;
        t = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    @Override // o.v.c
    final /* bridge */ /* synthetic */ o.an.c<o.ep.c> e(o.ep.c cVar) {
        int i = r + 83;
        t = i % 128;
        o.ep.c cVar2 = cVar;
        switch (i % 2 == 0) {
            case false:
                return e2(cVar2);
            default:
                e2(cVar2);
                throw null;
        }
    }

    public h(String str, o.eo.e eVar, boolean z) {
        super(str, eVar, z);
    }

    @Override // o.v.c
    final String a() {
        int i = t + 41;
        r = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        Object obj = null;
        u(null, 126 - ImageFormat.getBitsPerPixel(0), null, "\u0091\u0091\u0083\u0086\u0094\u0084\u0090\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095\u0094\u0093\u0092\u0091\u0087\u0090\u008f\u0084\u008c\u008e\u008d\u008c\u008b\u0089\u008a\u0089\u0088\u0083\u0084\u0087\u0086\u0083\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = r + 23;
        t = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    @Override // o.v.c
    final String t() {
        Object obj;
        int i = t + 55;
        r = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object[] objArr = new Object[1];
                u(null, 127 - (ViewConfiguration.getJumpTapTimeout() >> 16), null, "\u0096\u008c\u0090\u0097\u0083\u008d\u008a\u0094\u0094\u0095", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                u(null, 31 / (ViewConfiguration.getJumpTapTimeout() * Opcodes.DSUB), null, "\u0096\u008c\u0090\u0097\u0083\u008d\u008a\u0094\u0094\u0095", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.v.c
    final AntelopErrorCode s() {
        int i = r + 89;
        t = i % 128;
        int i2 = i % 2;
        AntelopErrorCode antelopErrorCode = AntelopErrorCode.GooglePayWalletNotAvailable;
        int i3 = r + 55;
        t = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                throw null;
            default:
                return antelopErrorCode;
        }
    }

    @Override // o.v.d
    final void b_() throws WalletValidationException {
        int i = r + 39;
        t = i % 128;
        int i2 = i % 2;
        Object obj = null;
        if (!((c) this).h) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            u(null, 127 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), null, "\u0083\u0086\u0089\u0098\u0084\u0083\u0085\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095", objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            u(null, 127 - Color.red(0), null, "\u0099\u0097\u008f\u0084\u008c\u008e\u0097\u008d\u008c\u008b\u0089\u008a\u0089\u0088\u0097\u0083\u0092\u0093", objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(((d) this).n.e());
            Object[] objArr3 = new Object[1];
            u(null, MotionEvent.axisFromString("") + 128, null, "\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095\u0097\u0094\u008b\u0097\u0092\u0091\u0087\u009a\u0097\u008b\u0084\u0094\u009a\u009a\u0087\u0091\u0097\u008b\u0094\u0082\u0097\u0091\u0083\u0094\u008f\u0097\u0099", objArr3);
            throw new WalletValidationException(walletValidationErrorCode, intern, append.append(((String) objArr3[0]).intern()).toString());
        }
        switch (((d) this).n.j() != null) {
            case true:
                break;
            default:
                int i3 = t + 25;
                r = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        if (((d) this).n.i() == null) {
                            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
                            Object[] objArr4 = new Object[1];
                            u(null, View.resolveSizeAndState(0, 0, 0) + 127, null, "\u0083\u0086\u0089\u0098\u0084\u0083\u0085\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095", objArr4);
                            String intern2 = ((String) objArr4[0]).intern();
                            Object[] objArr5 = new Object[1];
                            u(null, KeyEvent.normalizeMetaState(0) + 127, null, "\u008d\u0083\u009d\u008c\u008d\u0097\u0084\u0094\u0097\u008d\u0083\u009d\u008c\u009c\u009a\u0091\u008b\u0097\u008b\u0087\u0094\u0092\u008b\u0089\u009b\u0097\u008f\u0084\u008c\u0086\u0097\u008c\u0097\u0096\u008c\u009a\u0097\u0083\u008d\u008a\u0094\u0094\u008a\u0097\u0094\u008b\u0097\u0092\u0091\u0087\u009a\u0097\u008b\u0094\u0082\u0082\u008c\u008e", objArr5);
                            throw new WalletValidationException(walletValidationErrorCode2, intern2, ((String) objArr5[0]).intern());
                        }
                        break;
                    default:
                        ((d) this).n.i();
                        obj.hashCode();
                        throw null;
                }
        }
        switch (((d) this).n.s() != null ? '^' : '-') {
            case Opcodes.DUP2_X2 /* 94 */:
                if (((d) this).n.s().a() == null) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
                    Object[] objArr6 = new Object[1];
                    u(null, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + Opcodes.IAND, null, "\u0083\u0086\u0089\u0098\u0084\u0083\u0085\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095", objArr6);
                    String intern3 = ((String) objArr6[0]).intern();
                    Object[] objArr7 = new Object[1];
                    u(null, AndroidCharacter.getMirror('0') + 'O', null, "\u0091\u008b\u0089\u008a\u0089\u008f\u0097\u008b\u0091\u008c\u008d\u0097\u008b\u0087\u0094\u0092\u008b\u0089\u009b\u0097\u008f\u0084\u008c\u0086\u0097\u008c\u0097\u0096\u008c\u009a\u0097\u0083\u008d\u008a\u0094\u0094\u008a\u0097\u0094\u008b\u0097\u0092\u0091\u0087\u009a\u0097\u008b\u0094\u0082\u0082\u008c\u008e", objArr7);
                    throw new WalletValidationException(walletValidationErrorCode3, intern3, ((String) objArr7[0]).intern());
                }
                break;
        }
        int i4 = r + 89;
        t = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.v.c
    final Activity y() {
        int i = t + 17;
        int i2 = i % 128;
        r = i2;
        Object obj = null;
        switch (i % 2 != 0 ? Typography.greater : 'G') {
            case '>':
                obj.hashCode();
                throw null;
            default:
                Activity activity = this.f104o;
                int i3 = i2 + Opcodes.DSUB;
                t = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        throw null;
                    default:
                        return activity;
                }
        }
    }

    @Override // o.p.h
    public final String d() {
        Object obj;
        int i = r + 79;
        t = i % 128;
        char c = i % 2 == 0 ? (char) 1 : 'R';
        long uptimeMillis = SystemClock.uptimeMillis();
        switch (c) {
            case Opcodes.DASTORE /* 82 */:
                Object[] objArr = new Object[1];
                u(null, 128 - (uptimeMillis > 0L ? 1 : (uptimeMillis == 0L ? 0 : -1)), null, "\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095\u0094\u0093\u0092\u0091\u0087\u0090\u008f\u0084\u008c\u008e\u0083\u0084\u0087\u0086\u0083\u0085", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                u(null, 22613 >>> (uptimeMillis > 1L ? 1 : (uptimeMillis == 1L ? 0 : -1)), null, "\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095\u0094\u0093\u0092\u0091\u0087\u0090\u008f\u0084\u008c\u008e\u0083\u0084\u0087\u0086\u0083\u0085", objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    /* renamed from: e, reason: avoid collision after fix types in other method */
    private static o.an.c<o.ep.c> e2(o.ep.c cVar) {
        o.an.c<o.ep.c> cVar2 = new o.an.c<>(cVar);
        int i = t + 67;
        r = i % 128;
        switch (i % 2 != 0 ? 'D' : 'b') {
            case 'D':
                throw null;
            default:
                return cVar2;
        }
    }

    @Override // o.v.c
    final f.a p() {
        int i = t + Opcodes.DNEG;
        r = i % 128;
        switch (i % 2 != 0 ? '+' : '\'') {
            case '\'':
                return f.a.e;
            default:
                f.a aVar = f.a.e;
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private static o.ep.c c(Context context) {
        int i = t + 97;
        r = i % 128;
        int i2 = i % 2;
        o.ee.e.a();
        o.ep.c k = o.ee.c.k(context);
        int i3 = t + 49;
        r = i3 % 128;
        switch (i3 % 2 != 0 ? '[' : 'N') {
            case 'N':
                return k;
            default:
                throw null;
        }
    }

    @Override // o.v.c
    final o.ee.i v() {
        int i = r + 77;
        t = i % 128;
        switch (i % 2 == 0 ? 'b' : (char) 5) {
            case 5:
                return this.k;
            default:
                throw null;
        }
    }

    public final o.ee.i c(Activity activity, o.p.g gVar) throws WalletValidationException {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        Object obj = null;
        u(null, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 127, null, "\u0091\u0091\u0083\u0086\u0094\u0084\u0090\u0096\u008c\u0090\u0083\u008d\u008a\u0094\u0094\u0095\u0094\u0093\u0092\u0091\u0087\u0090\u008f\u0084\u008c\u008e\u008d\u008c\u008b\u0089\u008a\u0089\u0088\u0083\u0084\u0087\u0086\u0083\u0085\u0084\u0083\u0082\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u(null, 127 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), null, "\u0086\u0089\u008d\u009d\u0087\u0090\u0092\u0086\u0082\u0087\u008c\u008d", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.f104o = activity;
        this.k = new o.ee.i();
        d(activity, gVar);
        o.ee.i iVar = this.k;
        int i = t + 65;
        r = i % 128;
        switch (i % 2 != 0 ? 'T' : (char) 19) {
            case Opcodes.BASTORE /* 84 */:
                obj.hashCode();
                throw null;
            default:
                return iVar;
        }
    }

    private static void u(String str, int i, int[] iArr, String str2, Object[] objArr) {
        Object charArray;
        String str3 = str2;
        int i2 = $11 + Opcodes.LSUB;
        $10 = i2 % 128;
        int i3 = 2;
        int i4 = 1;
        int i5 = 0;
        Object obj = null;
        byte[] bArr = str3;
        switch (i2 % 2 == 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                if (str3 != null) {
                    bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                }
                byte[] bArr2 = bArr;
                switch (str != null ? '-' : 'E') {
                    case '-':
                        int i6 = $11 + 7;
                        $10 = i6 % 128;
                        int i7 = i6 % 2;
                        charArray = str.toCharArray();
                        break;
                    default:
                        charArray = str;
                        break;
                }
                char[] cArr = (char[]) charArray;
                o.a.j jVar = new o.a.j();
                char[] cArr2 = m;
                long j = 0;
                if (cArr2 != null) {
                    int length = cArr2.length;
                    char[] cArr3 = new char[length];
                    int i8 = 0;
                    while (i8 < length) {
                        try {
                            Object[] objArr2 = new Object[i4];
                            objArr2[i5] = Integer.valueOf(cArr2[i8]);
                            Object obj2 = o.e.a.s.get(1085633688);
                            if (obj2 == null) {
                                Class cls = (Class) o.e.a.c(11 - ExpandableListView.getPackedPositionType(j), (char) Color.alpha(i5), 492 - TextUtils.indexOf((CharSequence) "", '0', i5));
                                byte b = (byte) ($$k - i3);
                                byte b2 = b;
                                Object[] objArr3 = new Object[1];
                                F(b, b2, b2, objArr3);
                                obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1085633688, obj2);
                            }
                            cArr3[i8] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                            i8++;
                            i3 = 2;
                            i4 = 1;
                            i5 = 0;
                            j = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr2 = cArr3;
                }
                try {
                    Object[] objArr4 = {Integer.valueOf(l)};
                    Object obj3 = o.e.a.s.get(-1667314477);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 9, (char) (8857 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 324);
                        byte b3 = (byte) ($$k - 2);
                        byte b4 = (byte) (b3 + 3);
                        Object[] objArr5 = new Object[1];
                        F(b3, b4, (byte) (b4 - 3), objArr5);
                        obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                        o.e.a.s.put(-1667314477, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                    switch (s ? '^' : '!') {
                        case Opcodes.DUP2_X2 /* 94 */:
                            jVar.e = bArr2.length;
                            char[] cArr4 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                int i9 = $11 + 15;
                                $10 = i9 % 128;
                                if (i9 % 2 != 0) {
                                    cArr4[jVar.c] = (char) (cArr2[bArr2[(jVar.e * 1) / jVar.c] + i] << intValue);
                                    try {
                                        Object[] objArr6 = {jVar, jVar};
                                        Object obj4 = o.e.a.s.get(745816316);
                                        if (obj4 == null) {
                                            Class cls3 = (Class) o.e.a.c(TextUtils.getOffsetAfter("", 0) + 10, (char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 207 - (ViewConfiguration.getFadingEdgeLength() >> 16));
                                            byte b5 = (byte) ($$k - 2);
                                            byte length2 = (byte) $$j.length;
                                            Object[] objArr7 = new Object[1];
                                            F(b5, length2, (byte) (length2 - 4), objArr7);
                                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                            o.e.a.s.put(745816316, obj4);
                                        }
                                        ((Method) obj4).invoke(null, objArr6);
                                    } catch (Throwable th2) {
                                        Throwable cause2 = th2.getCause();
                                        if (cause2 == null) {
                                            throw th2;
                                        }
                                        throw cause2;
                                    }
                                } else {
                                    cArr4[jVar.c] = (char) (cArr2[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                                    try {
                                        Object[] objArr8 = {jVar, jVar};
                                        Object obj5 = o.e.a.s.get(745816316);
                                        if (obj5 == null) {
                                            Class cls4 = (Class) o.e.a.c(11 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) ((Process.getThreadPriority(0) + 20) >> 6), 207 - View.MeasureSpec.makeMeasureSpec(0, 0));
                                            byte b6 = (byte) ($$k - 2);
                                            byte length3 = (byte) $$j.length;
                                            Object[] objArr9 = new Object[1];
                                            F(b6, length3, (byte) (length3 - 4), objArr9);
                                            obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                            o.e.a.s.put(745816316, obj5);
                                        }
                                        ((Method) obj5).invoke(null, objArr8);
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                }
                            }
                            objArr[0] = new String(cArr4);
                            return;
                        default:
                            if (p) {
                                jVar.e = cArr.length;
                                char[] cArr5 = new char[jVar.e];
                                jVar.c = 0;
                                while (jVar.c < jVar.e) {
                                    cArr5[jVar.c] = (char) (cArr2[cArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                    try {
                                        Object[] objArr10 = {jVar, jVar};
                                        Object obj6 = o.e.a.s.get(745816316);
                                        if (obj6 == null) {
                                            Class cls5 = (Class) o.e.a.c(10 - View.combineMeasuredStates(0, 0), (char) Color.argb(0, 0, 0, 0), 207 - View.MeasureSpec.makeMeasureSpec(0, 0));
                                            byte b7 = (byte) ($$k - 2);
                                            byte length4 = (byte) $$j.length;
                                            Object[] objArr11 = new Object[1];
                                            F(b7, length4, (byte) (length4 - 4), objArr11);
                                            obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                            o.e.a.s.put(745816316, obj6);
                                        }
                                        ((Method) obj6).invoke(null, objArr10);
                                        int i10 = $10 + 43;
                                        $11 = i10 % 128;
                                        int i11 = i10 % 2;
                                    } catch (Throwable th4) {
                                        Throwable cause4 = th4.getCause();
                                        if (cause4 == null) {
                                            throw th4;
                                        }
                                        throw cause4;
                                    }
                                }
                                objArr[0] = new String(cArr5);
                                return;
                            }
                            jVar.e = iArr.length;
                            char[] cArr6 = new char[jVar.e];
                            int i12 = 0;
                            while (true) {
                                jVar.c = i12;
                                if (jVar.c >= jVar.e) {
                                    objArr[0] = new String(cArr6);
                                    return;
                                } else {
                                    cArr6[jVar.c] = (char) (cArr2[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                    i12 = jVar.c + 1;
                                }
                            }
                    }
                } catch (Throwable th5) {
                    Throwable cause5 = th5.getCause();
                    if (cause5 == null) {
                        throw th5;
                    }
                    throw cause5;
                }
        }
    }
}
