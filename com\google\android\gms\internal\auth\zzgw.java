package com.google.android.gms.internal.auth;

import com.esotericsoftware.asm.Opcodes;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzgw.smali */
final class zzgw {
    static String zza(zzee zzeeVar) {
        StringBuilder sb = new StringBuilder(zzeeVar.zzd());
        for (int i = 0; i < zzeeVar.zzd(); i++) {
            byte zza = zzeeVar.zza(i);
            switch (zza) {
                case 7:
                    sb.append("\\a");
                    break;
                case 8:
                    sb.append("\\b");
                    break;
                case 9:
                    sb.append("\\t");
                    break;
                case 10:
                    sb.append("\\n");
                    break;
                case 11:
                    sb.append("\\v");
                    break;
                case 12:
                    sb.append("\\f");
                    break;
                case 13:
                    sb.append("\\r");
                    break;
                case 34:
                    sb.append("\\\"");
                    break;
                case 39:
                    sb.append("\\'");
                    break;
                case Opcodes.DUP2 /* 92 */:
                    sb.append("\\\\");
                    break;
                default:
                    if (zza < 32 || zza > 126) {
                        sb.append('\\');
                        sb.append((char) (((zza >>> 6) & 3) + 48));
                        sb.append((char) (((zza >>> 3) & 7) + 48));
                        sb.append((char) ((zza & 7) + 48));
                        break;
                    } else {
                        sb.append((char) zza);
                        break;
                    }
                    break;
            }
        }
        return sb.toString();
    }
}
