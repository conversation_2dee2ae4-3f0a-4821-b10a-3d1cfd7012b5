package com.google.android.gms.tapandpay.globalactions;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GetGlobalActionCardsResponse.smali */
public final class GetGlobalActionCardsResponse extends AbstractSafeParcelable {
    public static final Parcelable.Creator<GetGlobalActionCardsResponse> CREATOR = new zzd();
    private GlobalActionCard[] zza;
    private int zzb;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\globalactions\GetGlobalActionCardsResponse$Builder.smali */
    public static final class Builder {
        private final GetGlobalActionCardsResponse zza;

        public Builder() {
            this.zza = new GetGlobalActionCardsResponse(null);
        }

        public GetGlobalActionCardsResponse build() {
            return this.zza;
        }

        public Builder setCards(GlobalActionCard[] cards) {
            this.zza.zza = cards;
            return this;
        }

        public Builder setSelectedIndex(int selectedIndex) {
            this.zza.zzb = selectedIndex;
            return this;
        }

        public Builder(GetGlobalActionCardsResponse origin) {
            GetGlobalActionCardsResponse getGlobalActionCardsResponse = new GetGlobalActionCardsResponse(null);
            this.zza = getGlobalActionCardsResponse;
            getGlobalActionCardsResponse.zza = origin.zza;
            getGlobalActionCardsResponse.zzb = origin.zzb;
        }
    }

    private GetGlobalActionCardsResponse() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof GetGlobalActionCardsResponse) {
            GetGlobalActionCardsResponse getGlobalActionCardsResponse = (GetGlobalActionCardsResponse) other;
            if (Arrays.equals(this.zza, getGlobalActionCardsResponse.zza) && Objects.equal(Integer.valueOf(this.zzb), Integer.valueOf(getGlobalActionCardsResponse.zzb))) {
                return true;
            }
        }
        return false;
    }

    public GlobalActionCard[] getCards() {
        return this.zza;
    }

    public int getSelectedIndex() {
        return this.zzb;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(Arrays.hashCode(this.zza)), Integer.valueOf(this.zzb));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeTypedArray(dest, 1, getCards(), flags, false);
        SafeParcelWriter.writeInt(dest, 2, getSelectedIndex());
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    /* synthetic */ GetGlobalActionCardsResponse(zzc zzcVar) {
    }

    GetGlobalActionCardsResponse(GlobalActionCard[] globalActionCardArr, int i) {
        this.zza = globalActionCardArr;
        this.zzb = i;
    }
}
