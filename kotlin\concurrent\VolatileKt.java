package kotlin.concurrent;

import kotlin.Metadata;

/* compiled from: Volatile.kt */
@Metadata(d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003*\u001a\b\u0007\u0010\u0000\"\u00020\u00012\u00020\u0001B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004¨\u0006\u0005"}, d2 = {"Volatile", "Lkotlin/jvm/Volatile;", "Lkotlin/SinceKotlin;", "version", "1.9", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\concurrent\VolatileKt.smali */
public final class VolatileKt {
    public static /* synthetic */ void Volatile$annotations() {
    }
}
