package bc.org.bouncycastle.math.ec;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\SimpleLookupTable.smali */
public class SimpleLookupTable extends AbstractECLookupTable {
    private final ECPoint[] a;

    public SimpleLookupTable(ECPoint[] eCPointArr, int i, int i2) {
        this.a = a(eCPointArr, i, i2);
    }

    private static ECPoint[] a(ECPoint[] eCPointArr, int i, int i2) {
        ECPoint[] eCPointArr2 = new ECPoint[i2];
        for (int i3 = 0; i3 < i2; i3++) {
            eCPointArr2[i3] = eCPointArr[i + i3];
        }
        return eCPointArr2;
    }

    @Override // bc.org.bouncycastle.math.ec.ECLookupTable
    public int getSize() {
        return this.a.length;
    }

    @Override // bc.org.bouncycastle.math.ec.ECLookupTable
    public ECPoint lookup(int i) {
        throw new UnsupportedOperationException("Constant-time lookup not supported");
    }

    @Override // bc.org.bouncycastle.math.ec.AbstractECLookupTable, bc.org.bouncycastle.math.ec.ECLookupTable
    public ECPoint lookupVar(int i) {
        return this.a[i];
    }
}
