package o.ds;

import android.view.ViewConfiguration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\e.smali */
final class e implements b {
    private static boolean a;
    private static boolean b;
    private static int c;
    private static char[] d;
    private static int e;
    private static int h = 1;

    static {
        c = 0;
        a();
        ViewConfiguration.getWindowTouchSlop();
        int i = h + 53;
        c = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        d = new char[]{61613, 61629, 61646, 61635, 61642, 61609, 61597, 61641, 61628, 61639, 61644};
        a = true;
        b = true;
        e = 782102872;
    }

    e() {
    }

    @Override // o.ds.b
    public final f d() {
        int i = h + 45;
        c = i % 128;
        int i2 = i % 2;
        f fVar = f.e;
        int i3 = c + 95;
        h = i3 % 128;
        int i4 = i3 % 2;
        return fVar;
    }
}
