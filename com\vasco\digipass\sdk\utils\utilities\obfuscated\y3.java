package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\y3.smali */
public class y3 extends u {
    private byte[] b;
    private s0 x;

    public y3(s0 s0Var, byte[] bArr) {
        this.b = Arrays.clone(bArr);
        this.x = s0Var;
    }

    public static y3 a(Object obj) {
        if (obj instanceof y3) {
            return (y3) obj;
        }
        if (obj != null) {
            return new y3(e0.a(obj));
        }
        return null;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(this.x);
        iVar.a(new f2(this.b));
        return new j2(iVar);
    }

    public y3(e0 e0Var) {
        Enumeration j = e0Var.j();
        this.x = s0.a(j.nextElement());
        this.b = x.a(j.nextElement()).h();
    }
}
