package com.vasco.digipass.sdk.utils.utilities;

import bc.org.bouncycastle.crypto.digests.SHA256Digest;
import bc.org.bouncycastle.crypto.macs.HMac;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\PBKDF2_HMAC_SHA256.smali */
public class PBKDF2_HMAC_SHA256 {
    private final l5 a;
    private final int b;
    private byte[] c;

    public PBKDF2_HMAC_SHA256() {
        HMac hMac = new HMac(new SHA256Digest());
        this.a = hMac;
        int macSize = hMac.getMacSize();
        this.b = macSize;
        this.c = new byte[macSize];
    }

    private void a(int i, byte[] bArr) {
        bArr[0] = (byte) (i >>> 24);
        bArr[1] = (byte) (i >>> 16);
        bArr[2] = (byte) (i >>> 8);
        bArr[3] = (byte) i;
    }

    public byte[] generateDerivedKey(byte[] bArr, byte[] bArr2, int i, int i2) {
        int i3 = this.b;
        int i4 = ((i2 + i3) - 1) / i3;
        byte[] bArr3 = new byte[4];
        byte[] bArr4 = new byte[i3 * i4];
        for (int i5 = 1; i5 <= i4; i5++) {
            a(i5, bArr3);
            a(bArr, bArr2, i, bArr3, bArr4, (i5 - 1) * this.b);
        }
        byte[] bArr5 = new byte[i2];
        System.arraycopy(bArr4, 0, bArr5, 0, i2);
        return bArr5;
    }

    private void a(byte[] bArr, byte[] bArr2, int i, byte[] bArr3, byte[] bArr4, int i2) {
        byte[] bArr5 = new byte[bArr2.length + bArr3.length];
        System.arraycopy(bArr2, 0, bArr5, 0, bArr2.length);
        System.arraycopy(bArr3, 0, bArr5, bArr2.length, bArr3.length);
        byte[] outputData = UtilitiesSDK.hmac((byte) 3, bArr5, bArr).getOutputData();
        this.c = outputData;
        System.arraycopy(outputData, 0, bArr4, i2, outputData.length);
        for (int i3 = 1; i3 < i; i3++) {
            this.c = UtilitiesSDK.hmac((byte) 3, this.c, bArr).getOutputData();
            int i4 = 0;
            while (true) {
                byte[] bArr6 = this.c;
                if (i4 != bArr6.length) {
                    int i5 = i2 + i4;
                    bArr4[i5] = (byte) (bArr6[i4] ^ bArr4[i5]);
                    i4++;
                }
            }
        }
    }
}
