package o.es;

import java.util.HashMap;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\es\c.smali */
public final class c {
    private static int a = 0;
    private static int c = 1;
    private final Map<b, Boolean> e = new HashMap(b.values().length);

    public c() {
        b[] values = b.values();
        int length = values.length;
        for (int i = 0; i < length; i = (i + 2) - 1) {
            this.e.put(values[i], Boolean.FALSE);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final void c(java.util.List<o.es.b> r6) {
        /*
            r5 = this;
            int r0 = o.es.c.a
            r1 = r0 ^ 27
            r0 = r0 & 27
            r2 = 1
            int r0 = r0 << r2
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.es.c.c = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L13
            r0 = 7
            goto L15
        L13:
            r0 = 57
        L15:
            switch(r0) {
                case 57: goto L1d;
                default: goto L18;
            }
        L18:
            r6.iterator()
            goto L9c
        L1d:
            java.util.Iterator r6 = r6.iterator()
            int r0 = o.es.c.a
            r1 = r0 & 85
            r0 = r0 | 85
            int r1 = r1 + r0
            int r0 = r1 % 128
            o.es.c.c = r0
            int r1 = r1 % 2
            if (r1 != 0) goto L33
            r0 = 80
            goto L35
        L33:
            r0 = 89
        L35:
            switch(r0) {
                case 89: goto L38;
                default: goto L39;
            }
        L38:
        L39:
            boolean r0 = r6.hasNext()
            r1 = 95
            if (r0 == 0) goto L43
            r0 = r1
            goto L45
        L43:
            r0 = 79
        L45:
            r3 = 0
            switch(r0) {
                case 95: goto L4a;
                default: goto L49;
            }
        L49:
            goto L7d
        L4a:
            int r0 = o.es.c.c
            int r0 = r0 + 21
            int r1 = r0 % 128
            o.es.c.a = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L58
            r0 = r2
            goto L59
        L58:
            r0 = r3
        L59:
            switch(r0) {
                case 1: goto L6a;
                default: goto L5c;
            }
        L5c:
            java.lang.Object r0 = r6.next()
            o.es.b r0 = (o.es.b) r0
            java.util.Map<o.es.b, java.lang.Boolean> r1 = r5.e
            java.lang.Boolean r3 = java.lang.Boolean.TRUE
            r1.put(r0, r3)
            goto L39
        L6a:
            java.lang.Object r0 = r6.next()
            o.es.b r0 = (o.es.b) r0
            java.util.Map<o.es.b, java.lang.Boolean> r1 = r5.e
            java.lang.Boolean r4 = java.lang.Boolean.TRUE
            r1.put(r0, r4)
            r0 = 69
            int r0 = r0 / r3
            goto L38
        L7b:
            r6 = move-exception
            throw r6
        L7d:
            int r6 = o.es.c.c
            r0 = r6 ^ 25
            r6 = r6 & 25
            int r6 = r6 << r2
            int r0 = r0 + r6
            int r6 = r0 % 128
            o.es.c.a = r6
            int r0 = r0 % 2
            if (r0 == 0) goto L90
            r1 = 60
            goto L91
        L90:
        L91:
            switch(r1) {
                case 95: goto L95;
                default: goto L94;
            }
        L94:
            goto L96
        L95:
            return
        L96:
            r6 = 72
            int r6 = r6 / r3
            return
        L9a:
            r6 = move-exception
            throw r6
        L9c:
            r6 = 0
            throw r6     // Catch: java.lang.Throwable -> L9e
        L9e:
            r6 = move-exception
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: o.es.c.c(java.util.List):void");
    }

    public final Boolean c(b bVar) {
        int i = c;
        int i2 = (i ^ 37) + ((i & 37) << 1);
        a = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 5 : (char) 4) {
            case 5:
                int i3 = 75 / 0;
                return this.e.get(bVar);
            default:
                return this.e.get(bVar);
        }
    }
}
