package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\h8.smali */
public class h8 extends u implements j8 {
    private w b;
    private b0 x;

    public h8(BigInteger bigInteger) {
        this.b = j8.d;
        this.x = new r(bigInteger);
    }

    public static h8 a(Object obj) {
        if (obj instanceof h8) {
            return (h8) obj;
        }
        if (obj != null) {
            return new h8(e0.a(obj));
        }
        return null;
    }

    public w e() {
        return this.b;
    }

    public b0 f() {
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(2);
        iVar.a(this.b);
        iVar.a(this.x);
        return new j2(iVar);
    }

    public h8(int i, int i2) {
        this(i, i2, 0, 0);
    }

    public h8(int i, int i2, int i3, int i4) {
        this.b = j8.e;
        i iVar = new i(3);
        iVar.a(new r(i));
        if (i3 == 0) {
            if (i4 == 0) {
                iVar.a(j8.g);
                iVar.a(new r(i2));
            } else {
                throw new IllegalArgumentException("inconsistent k values");
            }
        } else if (i3 > i2 && i4 > i3) {
            iVar.a(j8.h);
            i iVar2 = new i(3);
            iVar2.a(new r(i2));
            iVar2.a(new r(i3));
            iVar2.a(new r(i4));
            iVar.a(new j2(iVar2));
        } else {
            throw new IllegalArgumentException("inconsistent k values");
        }
        this.x = new j2(iVar);
    }

    private h8(e0 e0Var) {
        this.b = w.a(e0Var.a(0));
        this.x = e0Var.a(1).toASN1Primitive();
    }
}
