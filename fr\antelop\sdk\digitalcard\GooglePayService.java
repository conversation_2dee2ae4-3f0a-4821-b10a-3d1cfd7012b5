package fr.antelop.sdk.digitalcard;

import android.app.Activity;
import android.content.Context;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.Address;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import fr.antelop.sdk.util.OperationCallback;
import o.eo.e;
import o.eq.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\GooglePayService.smali */
public final class GooglePayService {
    private final d innerGooglePayService;

    GooglePayService(e eVar) throws WalletValidationException {
        this.innerGooglePayService = new d(eVar, (o.el.e) eVar.H());
    }

    public final void getStatus(Context context, OperationCallback<DigitalCardServiceStatus> operationCallback) {
        this.innerGooglePayService.b(context, operationCallback);
    }

    public final void getTspTokenId(Context context, OperationCallback<String> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.h(context, operationCallback);
    }

    public final void isCardInGooglePay(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.c(context, operationCallback);
    }

    public final void isCardInGooglePayOffline(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.a(context, operationCallback);
    }

    public final void canPushCard(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.d(context, operationCallback);
    }

    public final void canPushCardOffline(Context context, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.e(context, operationCallback);
    }

    public final AndroidActivityResultCallback pushCard(Activity activity, OperationCallback<Void> operationCallback) throws WalletValidationException {
        return this.innerGooglePayService.b(activity, operationCallback);
    }

    public final AndroidActivityResultCallback pushCard(Activity activity, OperationCallback<Void> operationCallback, Address address) throws WalletValidationException {
        return this.innerGooglePayService.a(activity, operationCallback, address);
    }

    @Deprecated
    public final AndroidActivityResultCallback configureWallet(Activity activity, OperationCallback<Void> operationCallback) {
        return this.innerGooglePayService.d(activity, operationCallback);
    }

    public final void configureWallet(Activity activity) {
        this.innerGooglePayService.d(activity);
    }

    public final SecureCardPushToGooglePay getSecureCardPush() {
        return this.innerGooglePayService.h();
    }

    public final AndroidActivityResultCallback setAsDefaultCard(Activity activity, OperationCallback<Void> operationCallback) {
        return this.innerGooglePayService.e(activity, operationCallback);
    }

    public final void isDefaultPaymentCard(Activity activity, OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        this.innerGooglePayService.a(activity, operationCallback);
    }

    public final AndroidActivityResultCallback deleteCard(Activity activity, OperationCallback<Void> operationCallback) {
        return this.innerGooglePayService.c(activity, operationCallback);
    }

    public final void showCard(Activity activity) {
        this.innerGooglePayService.a(activity);
    }
}
