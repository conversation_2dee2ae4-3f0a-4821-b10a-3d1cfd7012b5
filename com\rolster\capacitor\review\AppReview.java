package com.rolster.capacitor.review;

import androidx.appcompat.app.AppCompatActivity;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;
import com.rolster.capacitor.review.types.AppReviewStatus;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\rolster\capacitor\review\AppReview.smali */
public class AppReview {
    public static void request(final AppCompatActivity activity, final AppReviewResolve resolve) {
        final ReviewManager manager = ReviewManagerFactory.create(activity);
        Task<ReviewInfo> requestReview = manager.requestReviewFlow();
        requestReview.addOnCompleteListener(new OnCompleteListener() { // from class: com.rolster.capacitor.review.AppReview$$ExternalSyntheticLambda0
            @Override // com.google.android.gms.tasks.OnCompleteListener
            public final void onComplete(Task task) {
                AppReview.lambda$request$3(ReviewManager.this, activity, resolve, task);
            }
        });
        requestReview.addOnFailureListener(new OnFailureListener() { // from class: com.rolster.capacitor.review.AppReview$$ExternalSyntheticLambda1
            @Override // com.google.android.gms.tasks.OnFailureListener
            public final void onFailure(Exception exc) {
                AppReviewResolve.this.onFailure(AppReviewStatus.ERROR, exc.getMessage());
            }
        });
    }

    static /* synthetic */ void lambda$request$3(ReviewManager manager, AppCompatActivity activity, final AppReviewResolve resolve, Task task) {
        if (task.isSuccessful()) {
            Task<Void> reviewFlow = manager.launchReviewFlow(activity, (ReviewInfo) task.getResult());
            reviewFlow.addOnCompleteListener(new OnCompleteListener() { // from class: com.rolster.capacitor.review.AppReview$$ExternalSyntheticLambda2
                @Override // com.google.android.gms.tasks.OnCompleteListener
                public final void onComplete(Task task2) {
                    AppReviewResolve.this.onComplete(AppReviewStatus.COMPLETE);
                }
            });
            reviewFlow.addOnSuccessListener(new OnSuccessListener() { // from class: com.rolster.capacitor.review.AppReview$$ExternalSyntheticLambda3
                @Override // com.google.android.gms.tasks.OnSuccessListener
                public final void onSuccess(Object obj) {
                    AppReviewResolve.this.onComplete(AppReviewStatus.SUCCESS);
                }
            });
            reviewFlow.addOnFailureListener(new OnFailureListener() { // from class: com.rolster.capacitor.review.AppReview$$ExternalSyntheticLambda4
                @Override // com.google.android.gms.tasks.OnFailureListener
                public final void onFailure(Exception exc) {
                    AppReviewResolve.this.onFailure(AppReviewStatus.ERROR, exc.getMessage());
                }
            });
            return;
        }
        resolve.onFailure(AppReviewStatus.FAILURE, "Request review task Failed");
    }
}
