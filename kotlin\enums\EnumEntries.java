package kotlin.enums;

import java.lang.Enum;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.internal.markers.KMappedMarker;

/* compiled from: EnumEntries.kt */
@Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0010\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\bw\u0018\u0000*\u000e\b\u0000\u0010\u0001*\b\u0012\u0004\u0012\u0002H\u00010\u00022\b\u0012\u0004\u0012\u0002H\u00010\u0003\u0082\u0001\u0001\u0004¨\u0006\u0005"}, d2 = {"Lkotlin/enums/EnumEntries;", "E", "", "", "Lkotlin/enums/EnumEntriesList;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\enums\EnumEntries.smali */
public interface EnumEntries<E extends Enum<E>> extends List<E>, KMappedMarker {
}
