package o.fm;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.HashMap;
import java.util.Map;
import kotlin.text.Typography;
import o.eg.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fm\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static int g;
    private static char j;
    private static int k;
    private static int m;
    private String c;
    private String d;
    private boolean b = false;
    private final HashMap<String, String> e = new HashMap<>();
    private final HashMap<String, HashMap<String, String>> a = new HashMap<>();
    private final HashMap<String, String> h = new HashMap<>();
    private final HashMap<String, HashMap<String, String>> i = new HashMap<>();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        k = 0;
        m = 1;
        i();
        TextUtils.indexOf("", "", 0);
        ExpandableListView.getPackedPositionType(0L);
        PointF.length(0.0f, 0.0f);
        Gravity.getAbsoluteGravity(0, 0);
        View.getDefaultSize(0, 0);
        Process.getThreadPriority(0);
        TextUtils.getOffsetAfter("", 0);
        Process.getGidForName("");
        Process.myTid();
        ViewConfiguration.getWindowTouchSlop();
        Color.red(0);
        TextUtils.indexOf("", "", 0, 0);
        View.MeasureSpec.getSize(0);
        ViewConfiguration.getZoomControlsTimeout();
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        Process.getThreadPriority(0);
        KeyEvent.getModifierMetaStateMask();
        ExpandableListView.getPackedPositionChild(0L);
        TextUtils.getCapsMode("", 0, 0);
        ViewConfiguration.getTouchSlop();
        KeyEvent.getModifierMetaStateMask();
        View.resolveSize(0, 0);
        ViewConfiguration.getFadingEdgeLength();
        KeyEvent.getDeadChar(0, 0);
        SystemClock.elapsedRealtime();
        View.combineMeasuredStates(0, 0);
        int i = k + 69;
        m = i % 128;
        int i2 = i % 2;
    }

    static void i() {
        g = 874635272;
        f = new char[]{30563, 30572, 30571, 30566, 30511, 30560, 30588, 30517, 30589, 30570, 30561, 30565, 30574, 30569, 30568, 30591};
        j = (char) 17041;
    }

    static void init$0() {
        $$a = new byte[]{91, -13, 111, 42};
        $$b = 29;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(byte r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = 4 - r7
            byte[] r0 = o.fm.b.$$a
            int r8 = r8 + 69
            int r9 = r9 * 3
            int r9 = 1 - r9
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r9) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r7]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r7 = r7 + 1
            int r8 = r8 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.o(byte, int, short, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0130, code lost:
    
        r5 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0135, code lost:
    
        if (r5 >= r4.d()) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0137, code lost:
    
        r10 = '5';
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x013c, code lost:
    
        switch(r10) {
            case 53: goto L22;
            default: goto L52;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0140, code lost:
    
        r10 = o.fm.b.k + 57;
        o.fm.b.m = r10 % 128;
        r10 = r10 % 2;
        r10 = r4.b(r5);
        r11 = r22.e;
        r13 = new java.lang.Object[1];
        l((android.os.SystemClock.elapsedRealtimeNanos() > 0 ? 1 : (android.os.SystemClock.elapsedRealtimeNanos() == 0 ? 0 : -1)), "\u0000", (android.view.ViewConfiguration.getWindowTouchSlop() >> 8) + 1, android.view.View.MeasureSpec.getSize(0) + 218, true, r13);
        r12 = r10.r(((java.lang.String) r13[0]).intern());
        r14 = new java.lang.Object[1];
        l((android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)), "\u0000", (android.media.AudioTrack.getMinVolume() > 0.0f ? 1 : (android.media.AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1, 222 - (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), true, r14);
        r11.put(r12, r10.r(((java.lang.String) r14[0]).intern()));
        r5 = r5 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x013a, code lost:
    
        r10 = '\\';
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x0272, code lost:
    
        r13 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x0277, code lost:
    
        if (r13 >= r10.d()) goto L44;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0279, code lost:
    
        r14 = 30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:39:0x027e, code lost:
    
        switch(r14) {
            case 30: goto L47;
            default: goto L54;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x0289, code lost:
    
        r14 = r10.b(r13);
        r6 = new java.lang.Object[1];
        l((android.graphics.PointF.length(r6, r6) > r6 ? 1 : (android.graphics.PointF.length(r6, r6) == r6 ? 0 : -1)) + 1, "\u0000", (android.view.ViewConfiguration.getWindowTouchSlop() >> 8) + 1, (android.view.ViewConfiguration.getTouchSlop() >> 8) + 222, true, r6);
        r6 = r14.r(((java.lang.String) r6[0]).intern());
        r7 = new java.lang.Object[1];
        n(-android.text.TextUtils.lastIndexOf("", r4, 0, 0), "㘶", (byte) (59 - android.graphics.drawable.Drawable.resolveOpacity(0, 0)), r7);
        r12.put(r6, r14.r(((java.lang.String) r7[0]).intern()));
        r13 = r13 + 1;
        r4 = '0';
        r6 = 0.0f;
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x0281, code lost:
    
        r22.a.put(r11, r12);
        r5 = r5 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:43:0x02e8, code lost:
    
        r4 = '0';
        r6 = 0.0f;
        r7 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x027c, code lost:
    
        r14 = '8';
     */
    /* JADX WARN: Removed duplicated region for block: B:28:0x01ee  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(o.eg.b r23) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 836
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.d(o.eg.b):void");
    }

    final o.eg.b b() throws o.eg.d {
        Object obj;
        o.eg.b bVar = new o.eg.b();
        Object[] objArr = new Object[1];
        n(2 - KeyEvent.keyCodeFromString(""), "\u0003\u0002", (byte) (ImageFormat.getBitsPerPixel(0) + Opcodes.LREM), objArr);
        bVar.d(((String) objArr[0]).intern(), this.c);
        e eVar = new e();
        for (Map.Entry<String, String> entry : this.e.entrySet()) {
            o.eg.b bVar2 = new o.eg.b();
            Object[] objArr2 = new Object[1];
            l(1 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "\u0000", 1 - View.combineMeasuredStates(0, 0), ((Process.getThreadPriority(0) + 20) >> 6) + 218, true, objArr2);
            bVar2.d(((String) objArr2[0]).intern(), entry.getKey());
            Object[] objArr3 = new Object[1];
            l(View.resolveSizeAndState(0, 0, 0) + 1, "\u0000", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), TextUtils.lastIndexOf("", '0') + 223, true, objArr3);
            bVar2.d(((String) objArr3[0]).intern(), entry.getValue());
            eVar.b(bVar2);
        }
        switch (eVar.d() != 0) {
            case false:
                break;
            default:
                int i = k + 49;
                m = i % 128;
                switch (i % 2 == 0 ? (char) 25 : (char) 29) {
                    case 29:
                        Object[] objArr4 = new Object[1];
                        n(View.resolveSizeAndState(0, 0, 0) + 2, "\u0006\u0002", (byte) (View.MeasureSpec.getMode(0) + 55), objArr4);
                        obj = objArr4[0];
                        break;
                    default:
                        Object[] objArr5 = new Object[1];
                        n(View.resolveSizeAndState(0, 0, 0) * 4, "\u0006\u0002", (byte) (Opcodes.INEG / View.MeasureSpec.getMode(1)), objArr5);
                        obj = objArr5[0];
                        break;
                }
                bVar.d(((String) obj).intern(), eVar);
                break;
        }
        e eVar2 = new e();
        int i2 = m + 39;
        k = i2 % 128;
        int i3 = i2 % 2;
        for (Map.Entry<String, HashMap<String, String>> entry2 : this.a.entrySet()) {
            int i4 = k + 17;
            m = i4 % 128;
            int i5 = i4 % 2;
            if (entry2.getValue().size() != 0) {
                o.eg.b bVar3 = new o.eg.b();
                e eVar3 = new e();
                Object[] objArr6 = new Object[1];
                l((ViewConfiguration.getPressedStateDuration() >> 16) + 1, "\u0000", 1 - (ViewConfiguration.getScrollBarSize() >> 8), 218 - TextUtils.indexOf("", ""), true, objArr6);
                bVar3.d(((String) objArr6[0]).intern(), entry2.getKey());
                for (Map.Entry<String, String> entry3 : entry2.getValue().entrySet()) {
                    o.eg.b bVar4 = new o.eg.b();
                    Object[] objArr7 = new Object[1];
                    l((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "\u0000", -ExpandableListView.getPackedPositionChild(0L), 222 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), true, objArr7);
                    bVar4.d(((String) objArr7[0]).intern(), entry3.getKey());
                    Object[] objArr8 = new Object[1];
                    n(-TextUtils.lastIndexOf("", '0'), "㘶", (byte) (60 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), objArr8);
                    bVar4.d(((String) objArr8[0]).intern(), entry3.getValue());
                    eVar3.b(bVar4);
                    int i6 = k + 33;
                    m = i6 % 128;
                    int i7 = i6 % 2;
                }
                Object[] objArr9 = new Object[1];
                n((ViewConfiguration.getScrollBarSize() >> 8) + 2, "\f\u0002", (byte) (120 - ImageFormat.getBitsPerPixel(0)), objArr9);
                bVar3.d(((String) objArr9[0]).intern(), eVar3);
                eVar2.b(bVar3);
            }
        }
        if (eVar2.d() != 0) {
            Object[] objArr10 = new Object[1];
            l(1 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), "\uffff\u0002", TextUtils.getOffsetAfter("", 0) + 2, 217 - (ViewConfiguration.getLongPressTimeout() >> 16), false, objArr10);
            bVar.d(((String) objArr10[0]).intern(), eVar2);
        }
        return bVar;
    }

    /* JADX WARN: Removed duplicated region for block: B:118:0x01eb A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:124:0x01c5  */
    /* JADX WARN: Removed duplicated region for block: B:126:0x01c9 A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:98:0x01f0 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void e(java.util.LinkedHashMap<java.lang.String, o.eo.e> r10) {
        /*
            Method dump skipped, instructions count: 766
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.e(java.util.LinkedHashMap):void");
    }

    public final void c() {
        int i = k + 5;
        m = i % 128;
        int i2 = i % 2;
        this.b = true;
        this.d = null;
        this.h.clear();
        this.i.clear();
        this.c = null;
        this.e.clear();
        this.a.clear();
        int i3 = m + 59;
        k = i3 % 128;
        int i4 = i3 % 2;
    }

    public final boolean a() {
        int i = k + 19;
        m = i % 128;
        switch (i % 2 == 0 ? 'Z' : Typography.amp) {
            case 'Z':
                int i2 = 47 / 0;
                return this.b;
            default:
                return this.b;
        }
    }

    public final void e() {
        int i = m;
        int i2 = i + 87;
        k = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                this.b = false;
                break;
            default:
                this.b = true;
                break;
        }
        int i3 = i + 39;
        k = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x000e. Please report as an issue. */
    public final void d(String str) {
        int i = k + 65;
        m = i % 128;
        switch (i % 2 == 0 ? (char) 1 : (char) 2) {
        }
        this.b = true;
        this.c = str;
    }

    public final String d() {
        int i = k + 53;
        m = i % 128;
        switch (i % 2 != 0) {
            case true:
                return this.c;
            default:
                int i2 = 54 / 0;
                return this.c;
        }
    }

    public final void e(String str, String str2) {
        int i = m + Opcodes.LNEG;
        k = i % 128;
        int i2 = i % 2;
        this.b = true;
        this.e.put(str, str2);
        int i3 = k + Opcodes.LUSHR;
        m = i3 % 128;
        int i4 = i3 % 2;
    }

    public final String a(String str) {
        int i = m + 9;
        k = i % 128;
        int i2 = i % 2;
        String str2 = this.e.get(str);
        int i3 = k + Opcodes.DSUB;
        m = i3 % 128;
        int i4 = i3 % 2;
        return str2;
    }

    public final String c(String str, String str2) {
        int i = m + 57;
        k = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? '-' : 'W') {
            case Opcodes.POP /* 87 */:
                HashMap<String, String> hashMap = this.a.get(str);
                switch (hashMap == null ? '.' : 'B') {
                    case '.':
                        return null;
                    default:
                        String str3 = hashMap.get(str2);
                        int i2 = k + 71;
                        m = i2 % 128;
                        int i3 = i2 % 2;
                        return str3;
                }
            default:
                this.a.get(str);
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x002a, code lost:
    
        r3.a.put(r4, new java.util.HashMap<>());
        r0 = o.fm.b.m + 109;
        o.fm.b.k = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x003d, code lost:
    
        if ((r0 % 2) == 0) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x003f, code lost:
    
        r0 = 'b';
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0044, code lost:
    
        switch(r0) {
            case 23: goto L19;
            default: goto L19;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0042, code lost:
    
        r0 = 23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0028, code lost:
    
        if (r3.a.get(r4) == null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x001c, code lost:
    
        if (r3.a.get(r4) == null) goto L12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(java.lang.String r4, java.lang.String r5, java.lang.String r6) {
        /*
            r3 = this;
            int r0 = o.fm.b.m
            int r0 = r0 + 5
            int r1 = r0 % 128
            o.fm.b.k = r1
            r1 = 2
            int r0 = r0 % r1
            if (r0 == 0) goto Lf
            r0 = 85
            goto L10
        Lf:
            r0 = r1
        L10:
            switch(r0) {
                case 2: goto L1f;
                default: goto L13;
            }
        L13:
            r0 = 0
            r3.b = r0
            java.util.HashMap<java.lang.String, java.util.HashMap<java.lang.String, java.lang.String>> r0 = r3.a
            java.lang.Object r0 = r0.get(r4)
            if (r0 != 0) goto L49
            goto L2a
        L1f:
            r0 = 1
            r3.b = r0
            java.util.HashMap<java.lang.String, java.util.HashMap<java.lang.String, java.lang.String>> r0 = r3.a
            java.lang.Object r0 = r0.get(r4)
            if (r0 != 0) goto L49
        L2a:
            java.util.HashMap<java.lang.String, java.util.HashMap<java.lang.String, java.lang.String>> r0 = r3.a
            java.util.HashMap r2 = new java.util.HashMap
            r2.<init>()
            r0.put(r4, r2)
            int r0 = o.fm.b.m
            int r0 = r0 + 109
            int r2 = r0 % 128
            o.fm.b.k = r2
            int r0 = r0 % r1
            if (r0 == 0) goto L42
            r0 = 98
            goto L44
        L42:
            r0 = 23
        L44:
            switch(r0) {
                case 23: goto L48;
                default: goto L47;
            }
        L47:
            goto L49
        L48:
        L49:
            java.util.HashMap<java.lang.String, java.util.HashMap<java.lang.String, java.lang.String>> r0 = r3.a
            java.lang.Object r4 = r0.get(r4)
            java.util.HashMap r4 = (java.util.HashMap) r4
            r4.put(r5, r6)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.a(java.lang.String, java.lang.String, java.lang.String):void");
    }

    public final void d(String str, String str2) {
        int i = m + 27;
        k = i % 128;
        int i2 = i % 2;
        this.b = true;
        switch (this.a.get(str) != null ? 'I' : (char) 25) {
            case 'I':
                this.a.get(str).remove(str2);
                int i3 = m + 81;
                k = i3 % 128;
                if (i3 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    public final String h() {
        int i = k;
        int i2 = i + 3;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                String str = this.d;
                int i3 = i + 109;
                m = i3 % 128;
                int i4 = i3 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void b(String str) {
        int i = m + 1;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        this.d = str;
        int i4 = i2 + 45;
        m = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void g() {
        int i = k;
        int i2 = i + Opcodes.LMUL;
        m = i2 % 128;
        int i3 = i2 % 2;
        this.d = null;
        int i4 = i + Opcodes.LSHL;
        m = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String c(String str) {
        int i = k + 51;
        m = i % 128;
        int i2 = i % 2;
        String str2 = this.h.get(str);
        int i3 = k + 15;
        m = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                int i4 = 74 / 0;
                return str2;
            default:
                return str2;
        }
    }

    public final void b(String str, String str2) {
        int i = k + 37;
        m = i % 128;
        int i2 = i % 2;
        this.h.put(str, str2);
        int i3 = m + 29;
        k = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final void e(String str) {
        int i = m + 61;
        k = i % 128;
        int i2 = i % 2;
        this.h.remove(str);
        int i3 = k + 23;
        m = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                int i4 = 28 / 0;
                return;
            default:
                return;
        }
    }

    public final void c(String str, String str2, String str3) {
        int i = k + 93;
        m = i % 128;
        int i2 = i % 2;
        if (this.i.get(str) == null) {
            this.i.put(str, new HashMap<>());
        }
        this.i.get(str).put(str2, str3);
        int i3 = k + 27;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final String a(String str, String str2) {
        int i = k + 35;
        m = i % 128;
        int i2 = i % 2;
        HashMap<String, String> hashMap = this.i.get(str);
        switch (hashMap == null) {
            case false:
                return hashMap.get(str2);
            default:
                int i3 = m + 71;
                k = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        int i4 = 93 / 0;
                        return null;
                    default:
                        return null;
                }
        }
    }

    public final void f(String str, String str2) {
        int i = m + 99;
        k = i % 128;
        int i2 = i % 2;
        HashMap<String, String> hashMap = this.i.get(str);
        switch (hashMap != null) {
            case false:
                break;
            default:
                hashMap.remove(str2);
                int i3 = m + 37;
                k = i3 % 128;
                int i4 = i3 % 2;
                break;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void l(int r18, java.lang.String r19, int r20, int r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 508
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.l(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void n(int r24, java.lang.String r25, byte r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 1120
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fm.b.n(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
