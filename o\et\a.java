package o.et;

import android.os.SystemClock;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\a.smali */
public final class a extends h {
    private int a;
    private byte d;
    private int e;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int j = 0;
    private static int i = 1;
    private static char c = 10772;
    private static char f = 26675;
    private static char h = 25734;
    private static char b = 35318;

    @Override // o.et.h, o.el.d
    public final /* synthetic */ o.ey.e a(String str) {
        int i2 = j + 81;
        i = i2 % 128;
        int i3 = i2 % 2;
        o.eu.d h2 = h(str);
        int i4 = i + 23;
        j = i4 % 128;
        int i5 = i4 % 2;
        return h2;
    }

    @Override // o.et.h
    public final /* synthetic */ o.ey.b c(String str) {
        int i2 = i + 75;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                h(str);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                o.eu.d h2 = h(str);
                int i3 = i + 89;
                j = i3 % 128;
                int i4 = i3 % 2;
                return h2;
        }
    }

    public a(String str, String str2, int i2, String str3) {
        super(str, str2, i2, str3);
        b(o.dp.b.c);
        Object[] objArr = new Object[1];
        Q("˭愗\uf8fc\uf38d蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷蚣曷쑺ᨚ", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 35, objArr);
        j(o.dk.b.b(((String) objArr[0]).intern()));
    }

    @Override // o.et.h, o.et.c
    protected final c c(String str, String str2, int i2, String str3) {
        a aVar = new a(str, str2, i2, str3);
        int i3 = i + 1;
        j = i3 % 128;
        int i4 = i3 % 2;
        return aVar;
    }

    private o.eu.d h(String str) {
        o.eu.d dVar = new o.eu.d(n(), str, false);
        int i2 = i + 61;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? '(' : 'G') {
            case '(':
                throw null;
            default:
                return dVar;
        }
    }

    @Override // o.et.h, o.et.c
    public final EmvApplicationType e() {
        int i2 = i + 33;
        j = i2 % 128;
        int i3 = i2 % 2;
        EmvApplicationType emvApplicationType = EmvApplicationType.HceIdemiaMchip;
        int i4 = j + 95;
        i = i4 % 128;
        switch (i4 % 2 == 0 ? '3' : 'A') {
            case '3':
                throw null;
            default:
                return emvApplicationType;
        }
    }

    public final int c() {
        int i2 = i + 95;
        j = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 98 / 0;
                return this.e;
            default:
                return this.e;
        }
    }

    public final void a(int i2) {
        int i3 = i + 39;
        j = i3 % 128;
        char c2 = i3 % 2 != 0 ? '_' : '#';
        this.e = i2;
        switch (c2) {
            case Opcodes.SWAP /* 95 */:
                throw null;
            default:
                return;
        }
    }

    public final int d() {
        int i2 = i;
        int i3 = i2 + 65;
        j = i3 % 128;
        int i4 = i3 % 2;
        int i5 = this.a;
        int i6 = i2 + 37;
        j = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final void e(int i2) {
        int i3 = i + 5;
        j = i3 % 128;
        char c2 = i3 % 2 != 0 ? 'S' : 'A';
        this.a = i2;
        switch (c2) {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final byte b() {
        int i2 = i + 37;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return this.d;
            default:
                throw null;
        }
    }

    public final void c(byte b2) {
        int i2 = j;
        int i3 = i2 + 93;
        i = i3 % 128;
        int i4 = i3 % 2;
        this.d = b2;
        int i5 = i2 + 53;
        i = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 15 : (char) 22) {
            case 15:
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void Q(java.lang.String r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 554
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.et.a.Q(java.lang.String, int, java.lang.Object[]):void");
    }
}
