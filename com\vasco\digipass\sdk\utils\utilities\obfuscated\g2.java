package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\g2.smali */
public class g2 implements y {
    private x3 b;

    g2(x3 x3Var) {
        this.b = x3Var;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return new f2(this.b.c());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.y
    public InputStream c() {
        return this.b;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        try {
            return a();
        } catch (IOException e) {
            throw new a0("IOException converting stream to byte array: " + e.getMessage(), e);
        }
    }
}
