package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\XORCascadeState.smali */
public class XORCascadeState implements Serializable {
    public static final int BOXES = 15;
    public static final int WIDTH = 16;
    private static final long serialVersionUID = -7203348462332125659L;
    protected final XORBoxState[] x = new XORBoxState[15];

    public XORCascadeState() {
        for (int i = 0; i < 15; i++) {
            this.x[i] = new XORBoxState();
        }
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.deepEquals(this.x, ((XORCascadeState) obj).x);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.deepHashCode(this.x) + 201;
    }

    public State xor(State[] stateArr) {
        return xor(this.x, stateArr);
    }

    public static State xor(XORBoxState[] xORBoxStateArr, State[] stateArr) {
        int i = 0;
        for (int i2 = 0; i2 < 4; i2++) {
            int i3 = 1 << (3 - i2);
            int i4 = 1 << i2;
            for (int i5 = 0; i5 < i3; i5++) {
                int i6 = i5 * 2 * i4;
                xORBoxStateArr[i + i5].xorA(stateArr[i6], stateArr[i6 + i4]);
            }
            i += i3;
        }
        return stateArr[0];
    }
}
