package androidx.core.content;

import android.content.UriMatcher;
import android.net.Uri;
import androidx.core.util.Predicate;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\content\UriMatcherCompat$$ExternalSyntheticLambda0.smali */
public final /* synthetic */ class UriMatcherCompat$$ExternalSyntheticLambda0 implements Predicate {
    public final /* synthetic */ UriMatcher f$0;

    @Override // androidx.core.util.Predicate
    public final boolean test(Object obj) {
        return UriMatcherCompat.lambda$asPredicate$0(this.f$0, (Uri) obj);
    }
}
