package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import android.security.keystore.KeyGenParameterSpec;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\b¢\u0006\u0004\b\n\u0010\u000bJ\b\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\f"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/b;", "", "Landroid/security/keystore/KeyGenParameterSpec$Builder;", "a", "Landroid/content/Context;", "context", "", "keyAlias", "", "invalidateByBiometricEnrollment", "<init>", "(Landroid/content/Context;Ljava/lang/String;Z)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\b.smali */
public final class b {
    private final Context a;
    private final String b;
    private final boolean c;

    public b(Context context, String keyAlias, boolean z) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        this.a = context;
        this.b = keyAlias;
        this.c = z;
    }

    public KeyGenParameterSpec.Builder a() {
        KeyGenParameterSpec.Builder a = new l(this.a, this.b).a();
        a.setUserAuthenticationRequired(true);
        a.setInvalidatedByBiometricEnrollment(this.c);
        return a;
    }
}
