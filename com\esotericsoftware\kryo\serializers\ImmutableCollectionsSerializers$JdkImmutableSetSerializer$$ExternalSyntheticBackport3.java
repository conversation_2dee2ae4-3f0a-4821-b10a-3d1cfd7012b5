package com.esotericsoftware.kryo.serializers;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import java.util.Set;

/* compiled from: D8$$SyntheticClass */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport3.smali */
public final /* synthetic */ class ImmutableCollectionsSerializers$JdkImmutableSetSerializer$$ExternalSyntheticBackport3 {
    public static /* synthetic */ Set m(Collection collection) {
        HashSet hashSet = new HashSet(collection.size());
        Iterator it = collection.iterator();
        while (it.hasNext()) {
            hashSet.add(Objects.requireNonNull(it.next()));
        }
        return Collections.unmodifiableSet(hashSet);
    }
}
