package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.samsung.android.sdk.samsungpay.v2.SamsungPay;
import com.samsung.android.sdk.samsungpay.v2.StatusListener;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayConfigureWalletListener.smali */
public class SamsungPayConfigureWalletListener implements StatusListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static char c;
    private static int d;
    private static int i;
    private SamsungPay e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        i = 1;
        e();
        View.resolveSize(0, 0);
        Drawable.resolveOpacity(0, 0);
        int i2 = b + 39;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 3 : 'Q') {
            case 3:
                throw null;
            default:
                return;
        }
    }

    static void e() {
        c = (char) 17957;
        d = 161105445;
        a = 5701702382306571069L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 99
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayConfigureWalletListener.$$a
            int r8 = r8 * 4
            int r8 = 1 - r8
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L33
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r8) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayConfigureWalletListener.g(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{114, 12, -103, 122};
        $$b = 40;
    }

    protected SamsungPayConfigureWalletListener(SamsungPay samsungPay) {
        this.e = samsungPay;
    }

    public void onSuccess(int i2, Bundle bundle) {
        g.c();
        Object[] objArr = new Object[1];
        f((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 2106625486, "辱ᯩ姴痐핲霤\ue7a6啒젯Ṯ캫\ud84b꤬狕䍆뜌꾇蜿Ἄ㏨烞괨뫗\ue85a렾꧲ﱼ섳턏Ꮹ海踥焼", (char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), "쿜邍艽쐫", "\uf518釻ṝᐾ", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((KeyEvent.getMaxKeyCode() >> 16) + 679790845, "璴躀훆巔᩠꽀喎ꊳ閉⸙䗾摥㟻筗ᗶ챭㸄ؗ䖉卶排\ud95f锳報㉑堻慴", (char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 39375), "ﶟ蓈켨写", "\uf518釻ṝᐾ", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        switch (i2 == 1) {
            case false:
                break;
            default:
                Object[] objArr3 = new Object[1];
                f((-15907956) - TextUtils.getTrimmedLength(""), "寀ꆐ椧䦆⨓歱퓓抎낦ᤒ駩", (char) KeyEvent.keyCodeFromString(""), "貫ൃ鯿쬱", "\uf518釻ṝᐾ", objArr3);
                if (bundle.getInt(((String) objArr3[0]).intern()) != -357) {
                    this.e.activateSamsungPay();
                    break;
                } else {
                    int i3 = b + 55;
                    i = i3 % 128;
                    switch (i3 % 2 == 0 ? Typography.less : 'K') {
                        case 'K':
                            this.e.goToUpdatePage();
                            return;
                        default:
                            this.e.goToUpdatePage();
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                    }
                }
        }
        int i4 = i + 1;
        b = i4 % 128;
        int i5 = i4 % 2;
    }

    public void onFail(int i2, Bundle bundle) {
        int i3 = i + 33;
        b = i3 % 128;
        int i4 = i3 % 2;
        g.c();
        Object[] objArr = new Object[1];
        f(2106625487 - (Process.myPid() >> 22), "辱ᯩ姴痐핲霤\ue7a6啒젯Ṯ캫\ud84b꤬狕䍆뜌꾇蜿Ἄ㏨烞괨뫗\ue85a렾꧲ﱼ섳턏Ꮹ海踥焼", (char) (KeyEvent.getMaxKeyCode() >> 16), "쿜邍艽쐫", "\uf518釻ṝᐾ", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1, "뗣쟫憀䎅錤\ua7ee싵\uf5b1ᄽ虧讦ీ䥮鳁ﲬᔓｉ蹿ꤺ\u0897\uecc6夿匩칟", (char) TextUtils.getOffsetAfter("", 0), "ﲸ椁強Æ", "\uf518釻ṝᐾ", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i5 = b + 13;
        i = i5 % 128;
        int i6 = i5 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r21, java.lang.String r22, char r23, java.lang.String r24, java.lang.String r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 704
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayConfigureWalletListener.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
