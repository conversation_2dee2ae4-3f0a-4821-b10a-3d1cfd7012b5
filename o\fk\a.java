package o.fk;

import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\a.smali */
public final class a {
    private final String a;
    private final String c;
    private final c d;
    private static int e = 0;
    private static int b = 1;

    public a(String str, String str2, c cVar) {
        this.a = str;
        this.c = str2;
        this.d = cVar;
    }

    public final String e() {
        int i = (e + 76) - 1;
        b = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.a;
        }
    }

    public final String d() {
        int i = b;
        int i2 = i + 25;
        e = i2 % 128;
        int i3 = i2 % 2;
        String str = this.c;
        int i4 = i + Opcodes.DMUL;
        e = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 1 : (char) 29) {
            case 1:
                int i5 = 14 / 0;
                return str;
            default:
                return str;
        }
    }

    public final c c() {
        int i = e;
        int i2 = ((i | 91) << 1) - (i ^ 91);
        b = i2 % 128;
        switch (i2 % 2 == 0 ? 'L' : (char) 7) {
            case 7:
                return this.d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:37:0x0095, code lost:
    
        if (r0 == r8) goto L47;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean equals(java.lang.Object r8) {
        /*
            r7 = this;
            int r0 = o.fk.a.b
            int r1 = r0 + 99
            int r2 = r1 % 128
            o.fk.a.e = r2
            int r1 = r1 % 2
            r1 = 60
            r3 = 0
            r4 = 1
            if (r7 != r8) goto L21
            int r0 = r0 + 71
            int r8 = r0 % 128
            o.fk.a.e = r8
            int r0 = r0 % 2
            if (r0 == 0) goto L1c
            r1 = 67
        L1c:
            switch(r1) {
                case 67: goto L20;
                default: goto L1f;
            }
        L1f:
            return r4
        L20:
            return r3
        L21:
            if (r8 == 0) goto L25
            r0 = r3
            goto L26
        L25:
            r0 = r4
        L26:
            switch(r0) {
                case 0: goto L2b;
                default: goto L29;
            }
        L29:
            goto Lac
        L2b:
            int r2 = r2 + 11
            int r0 = r2 % 128
            o.fk.a.b = r0
            int r2 = r2 % 2
            java.lang.Class r0 = r7.getClass()
            java.lang.Class r2 = r8.getClass()
            r5 = 85
            if (r0 == r2) goto L41
            r0 = r5
            goto L43
        L41:
            r0 = 63
        L43:
            switch(r0) {
                case 63: goto L47;
                default: goto L46;
            }
        L46:
            goto L29
        L47:
            o.fk.a r8 = (o.fk.a) r8
            java.lang.String r0 = r7.a
            java.lang.String r2 = r8.a
            boolean r0 = r0.equals(r2)
            r2 = 39
            if (r0 == 0) goto L58
            r0 = 93
            goto L59
        L58:
            r0 = r2
        L59:
            switch(r0) {
                case 39: goto La0;
                default: goto L5c;
            }
        L5c:
            int r0 = o.fk.a.b
            int r0 = r0 + 112
            int r0 = r0 - r4
            int r6 = r0 % 128
            o.fk.a.e = r6
            int r0 = r0 % 2
            java.lang.String r0 = r7.c
            java.lang.String r6 = r8.c
            boolean r0 = java.util.Objects.equals(r0, r6)
            if (r0 == 0) goto L73
            r0 = r4
            goto L74
        L73:
            r0 = r3
        L74:
            switch(r0) {
                case 0: goto La0;
                default: goto L77;
            }
        L77:
            int r0 = o.fk.a.e
            r6 = r0 ^ 117(0x75, float:1.64E-43)
            r0 = r0 & 117(0x75, float:1.64E-43)
            int r0 = r0 << r4
            int r6 = r6 + r0
            int r0 = r6 % 128
            o.fk.a.b = r0
            int r6 = r6 % 2
            if (r6 != 0) goto L88
            r2 = r1
        L88:
            o.fk.c r0 = r7.d
            o.fk.c r8 = r8.d
            switch(r2) {
                case 60: goto L92;
                default: goto L8f;
            }
        L8f:
            if (r0 != r8) goto L9a
            goto L9b
        L92:
            r1 = 13
            int r1 = r1 / r3
            if (r0 != r8) goto La0
            goto L9f
        L98:
            r8 = move-exception
            throw r8
        L9a:
            r1 = r5
        L9b:
            switch(r1) {
                case 60: goto L9f;
                default: goto L9e;
            }
        L9e:
            goto La0
        L9f:
            return r4
        La0:
            int r8 = o.fk.a.b
            int r8 = r8 + 78
            int r8 = r8 - r4
            int r0 = r8 % 128
            o.fk.a.e = r0
            int r8 = r8 % 2
            return r3
        Lac:
            int r8 = o.fk.a.e
            int r8 = r8 + 81
            int r0 = r8 % 128
            o.fk.a.b = r0
            int r8 = r8 % 2
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fk.a.equals(java.lang.Object):boolean");
    }

    public final int hashCode() {
        int hash;
        int i = b;
        int i2 = (i & 59) + (i | 59);
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object[] objArr = new Object[3];
                objArr[1] = this.a;
                objArr[1] = this.c;
                objArr[2] = this.d;
                hash = Objects.hash(objArr);
                break;
            default:
                hash = Objects.hash(this.a, this.c, this.d);
                break;
        }
        int i3 = b;
        int i4 = ((i3 | 55) << 1) - (i3 ^ 55);
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return hash;
        }
    }
}
