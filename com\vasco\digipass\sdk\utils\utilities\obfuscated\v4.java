package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.WNafUtil;
import java.math.BigInteger;
import java.util.Enumeration;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v4.smali */
public class v4 {
    static e8 a = new a();
    static e8 b = new b();
    static final Hashtable c = new Hashtable();
    static final Hashtable d = new Hashtable();
    static final Hashtable e = new Hashtable();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v4$a.smali */
    class a extends e8 {
        a() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return v4.b(new ECCurve.Fp(v4.b("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF"), v4.b("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC"), v4.b("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93"), v4.b("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, v4.b(c, "0432C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0"), c.getOrder(), c.getCofactor(), null);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v4$b.smali */
    class b extends e8 {
        b() {
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected ECCurve a() {
            return v4.b(new ECCurve.Fp(v4.b("BDB6F4FE3E8B1D9E0DA8C0D46F4C318CEFE4AFE3B6B8551F"), v4.b("BB8E5E8FBC115E139FE6A814FE48AAA6F0ADA1AA5DF91985"), v4.b("1854BEBDC31B21B7AEFC80AB0ECD10D5B1B3308E6DBF11C1"), v4.b("BDB6F4FE3E8B1D9E0DA8C0D40FC962195DFAE76F56564677"), BigInteger.valueOf(1L), true));
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e8
        protected X9ECParameters b() {
            ECCurve c = c();
            return new X9ECParameters(c, v4.b(c, "044AD5F7048DE709AD51236DE65E4D4B482C836DC6E410664002BB3A02D4AAADACAE24817A4CA3A1B014B5270432DB27D2"), c.getOrder(), c.getCofactor(), null);
        }
    }

    static {
        a("wapip192v1", w4.J, b);
        a("wapi192v1", w4.K, b);
        a("sm2p256v1", w4.F, a);
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static ECCurve b(ECCurve eCCurve) {
        return eCCurve;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static f8 b(ECCurve eCCurve, String str) {
        f8 f8Var = new f8(eCCurve, z4.a(str));
        WNafUtil.configureBasepoint(f8Var.e());
        return f8Var;
    }

    public static X9ECParameters c(String str) {
        w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return a(e2);
    }

    public static e8 d(String str) {
        w e2 = e(str);
        if (e2 == null) {
            return null;
        }
        return b(e2);
    }

    public static w e(String str) {
        return (w) c.get(o7.b(str));
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static BigInteger b(String str) {
        return new BigInteger(1, z4.a(str));
    }

    static void a(String str, w wVar, e8 e8Var) {
        c.put(o7.b(str), wVar);
        e.put(wVar, str);
        d.put(wVar, e8Var);
    }

    public static e8 b(w wVar) {
        return (e8) d.get(wVar);
    }

    public static X9ECParameters a(w wVar) {
        e8 b2 = b(wVar);
        if (b2 == null) {
            return null;
        }
        return b2.d();
    }

    public static Enumeration a() {
        return e.elements();
    }
}
