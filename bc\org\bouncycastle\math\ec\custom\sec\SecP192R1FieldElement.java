package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192R1FieldElement.smali */
public class SecP192R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF"));
    protected int[] a;

    public SecP192R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP192R1FieldElement");
        }
        this.a = SecP192R1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192R1Field.add(this.a, ((SecP192R1FieldElement) eCFieldElement).a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = u5.a();
        SecP192R1Field.addOne(this.a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192R1Field.inv(((SecP192R1FieldElement) eCFieldElement).a, a);
        SecP192R1Field.multiply(a, this.a, a);
        return new SecP192R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP192R1FieldElement) {
            return u5.a(this.a, ((SecP192R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP192R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 6);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = u5.a();
        SecP192R1Field.inv(this.a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return u5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return u5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192R1Field.multiply(this.a, ((SecP192R1FieldElement) eCFieldElement).a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = u5.a();
        SecP192R1Field.negate(this.a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (u5.b(iArr) || u5.a(iArr)) {
            return this;
        }
        int[] a = u5.a();
        int[] a2 = u5.a();
        SecP192R1Field.square(iArr, a);
        SecP192R1Field.multiply(a, iArr, a);
        SecP192R1Field.squareN(a, 2, a2);
        SecP192R1Field.multiply(a2, a, a2);
        SecP192R1Field.squareN(a2, 4, a);
        SecP192R1Field.multiply(a, a2, a);
        SecP192R1Field.squareN(a, 8, a2);
        SecP192R1Field.multiply(a2, a, a2);
        SecP192R1Field.squareN(a2, 16, a);
        SecP192R1Field.multiply(a, a2, a);
        SecP192R1Field.squareN(a, 32, a2);
        SecP192R1Field.multiply(a2, a, a2);
        SecP192R1Field.squareN(a2, 64, a);
        SecP192R1Field.multiply(a, a2, a);
        SecP192R1Field.squareN(a, 62, a);
        SecP192R1Field.square(a, a2);
        if (u5.a(iArr, a2)) {
            return new SecP192R1FieldElement(a);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = u5.a();
        SecP192R1Field.square(this.a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = u5.a();
        SecP192R1Field.subtract(this.a, ((SecP192R1FieldElement) eCFieldElement).a, a);
        return new SecP192R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return u5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return u5.c(this.a);
    }

    public SecP192R1FieldElement() {
        this.a = u5.a();
    }

    protected SecP192R1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
