package com.vasco.digipass.sdk.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\obfuscated\q.smali */
public final class q {
    private static byte a(char c) {
        int i;
        char c2 = 'a';
        if (c < 'a') {
            c2 = 'A';
            if (c < 'A') {
                i = c - '0';
                return (byte) i;
            }
        }
        i = (c - c2) + 10;
        return (byte) i;
    }

    public static boolean a(long j, long j2) {
        return (j & j2) == j2;
    }

    public static byte[] a(byte[] bArr, int i, int i2) {
        if (bArr == null || i < 0 || i >= bArr.length || i2 < 0 || i + i2 > bArr.length) {
            throw new IllegalArgumentException(a());
        }
        byte[] bArr2 = new byte[i2];
        System.arraycopy(bArr, i, bArr2, 0, i2);
        return bArr2;
    }

    public static boolean b(String str) {
        byte[] bytes = str != null ? str.getBytes() : null;
        boolean e = e(bytes);
        g(bytes);
        return e;
    }

    public static byte[] c(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        byte[] bArr2 = new byte[bArr.length];
        System.arraycopy(bArr, 0, bArr2, 0, bArr.length);
        return bArr2;
    }

    public static boolean d(String str) {
        return str == null || str.length() == 0;
    }

    public static boolean e(byte[] bArr) {
        if (f(bArr)) {
            return true;
        }
        for (byte b : bArr) {
            char c = (char) b;
            if (c < '0' || c > '9') {
                return false;
            }
        }
        return true;
    }

    public static boolean f(byte[] bArr) {
        if (bArr != null && bArr.length != 0) {
            for (byte b : bArr) {
                if (b != 0) {
                    return false;
                }
            }
        }
        return true;
    }

    public static void g(byte[] bArr) {
        if (bArr != null) {
            Arrays.fill(bArr, (byte) 0);
        }
    }

    public static boolean d(byte[] bArr) {
        if (f(bArr)) {
            return true;
        }
        if (((bArr[0] == 48) ^ (bArr[bArr.length - 1] == 48)) && b(bArr) == bArr.length - 1) {
            return true;
        }
        int length = bArr.length - 1;
        int[] iArr = new int[length];
        int i = 0;
        while (i < length) {
            int i2 = i + 1;
            iArr[i] = bArr[i] - bArr[i2];
            i = i2;
        }
        int i3 = 0;
        while (i3 < length - 1) {
            int i4 = iArr[i3];
            i3++;
            if (i4 != iArr[i3]) {
                return false;
            }
        }
        return true;
    }

    public static boolean c(String str) {
        if (d(str)) {
            return true;
        }
        for (int i = 0; i < str.length(); i++) {
            char charAt = str.charAt(i);
            if ((charAt < '0' || charAt > '9') && ((charAt < 'A' || charAt > 'F') && (charAt < 'a' || charAt > 'f'))) {
                return false;
            }
        }
        return true;
    }

    public static boolean b(byte[] bArr, int i) {
        if (f(bArr) || i < 4 || a(bArr, i)) {
            return true;
        }
        return d(bArr);
    }

    private static int b(byte[] bArr) {
        int i = 0;
        for (byte b : bArr) {
            if (b == 48) {
                i++;
            }
        }
        return i;
    }

    public static String a(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bArr) {
            String hexString = Integer.toHexString(b & 255);
            if (hexString.length() == 1) {
                sb.append('0');
            }
            sb.append(hexString);
        }
        return sb.toString().toUpperCase();
    }

    public static int a(String str, byte[] bArr, int i) {
        int length = str.length() / 2;
        for (int i2 = 0; i2 < length; i2++) {
            int i3 = i2 * 2;
            bArr[i2 + i] = (byte) ((a(str.charAt(i3)) << 4) + (a(str.charAt(i3 + 1)) & 255));
        }
        return length;
    }

    public static byte[] a(String str) {
        if (str == null) {
            return null;
        }
        byte[] bArr = new byte[str.length() / 2];
        a(str, bArr, 0);
        return bArr;
    }

    private static boolean a(byte[] bArr, int i) {
        return !f(bArr) && i > bArr.length;
    }

    static String a() {
        byte[] bArr = new byte[18];
        int[] iArr = {74, Opcodes.DMUL, Opcodes.IF_ICMPGT, 242, 67, 35, 242, 200, 67, Opcodes.MONITORENTER, 67, Opcodes.GETSTATIC, 11, Opcodes.MONITORENTER, 51, Opcodes.GETSTATIC, 19, 19};
        for (int i = 0; i < 18; i++) {
            int i2 = iArr[i];
            bArr[i] = (byte) (((((i2 & 255) >> 3) | (i2 << 5)) & 255) + i);
        }
        return new String(bArr);
    }
}
