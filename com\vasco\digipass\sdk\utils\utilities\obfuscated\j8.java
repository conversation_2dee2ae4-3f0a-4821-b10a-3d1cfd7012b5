package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j8.smali */
public interface j8 {
    public static final w A;
    public static final w B;
    public static final w D;
    public static final w E;
    public static final w F;
    public static final w G;
    public static final w H;
    public static final w I;
    public static final w J;
    public static final w K;
    public static final w M;
    public static final w N;
    public static final w O;
    public static final w P;
    public static final w Q;
    public static final w S;
    public static final w T;
    public static final w U;
    public static final w V;
    public static final w W;
    public static final w X;
    public static final w Y;
    public static final w Z;
    public static final w a;
    public static final w a0;
    public static final w b0;
    public static final w c;
    public static final w c0;
    public static final w d;
    public static final w d0;
    public static final w e;
    public static final w e0;
    public static final w f;
    public static final w f0;
    public static final w g;
    public static final w g0;
    public static final w h;
    public static final w h0;
    public static final w i;
    public static final w i0;
    public static final w j;
    public static final w j0;
    public static final w k;
    public static final w k0;
    public static final w l;
    public static final w l0;
    public static final w m;
    public static final w m0;
    public static final w n;
    public static final w n0;

    /* renamed from: o, reason: collision with root package name */
    public static final w f24o;
    public static final w o0;
    public static final w p;
    public static final w p0;
    public static final w q;
    public static final w q0;
    public static final w r;
    public static final w r0;
    public static final w s;
    public static final w s0;
    public static final w t;
    public static final w t0;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.2.840.10045");
        a = wVar;
        w a2 = wVar.a("1");
        c = a2;
        d = a2.a("1");
        w a3 = a2.a("2");
        e = a3;
        f = a3.a("3.1");
        g = a3.a("3.2");
        h = a3.a("3.3");
        w a4 = wVar.a("4");
        i = a4;
        j = a4.a("1");
        w a5 = wVar.a("2");
        k = a5;
        l = a5.a("1");
        w a6 = a4.a("3");
        m = a6;
        n = a6.a("1");
        f24o = a6.a("2");
        p = a6.a("3");
        q = a6.a("4");
        w a7 = wVar.a("3");
        r = a7;
        w a8 = a7.a("0");
        s = a8;
        t = a8.a("1");
        u = a8.a("2");
        v = a8.a("3");
        w = a8.a("4");
        y = a8.a("5");
        z = a8.a("6");
        A = a8.a("7");
        B = a8.a("8");
        D = a8.a("9");
        E = a8.a("10");
        F = a8.a("11");
        G = a8.a("12");
        H = a8.a("13");
        I = a8.a("14");
        J = a8.a("15");
        K = a8.a("16");
        M = a8.a("17");
        N = a8.a("18");
        O = a8.a("19");
        P = a8.a("20");
        w a9 = a7.a("1");
        Q = a9;
        S = a9.a("1");
        T = a9.a("2");
        U = a9.a("3");
        V = a9.a("4");
        W = a9.a("5");
        X = a9.a("6");
        Y = a9.a("7");
        Z = new w("1.2.840.10040.4.1");
        a0 = new w("1.2.840.10040.4.3");
        w wVar2 = new w("1.3.133.16.840.63.0");
        b0 = wVar2;
        c0 = wVar2.a("2");
        d0 = wVar2.a("3");
        e0 = wVar2.a("16");
        w wVar3 = new w("1.2.840.10046");
        f0 = wVar3;
        g0 = wVar3.a("2.1");
        w a10 = wVar3.a("3");
        h0 = a10;
        i0 = a10.a("1");
        j0 = a10.a("2");
        k0 = a10.a("3");
        l0 = a10.a("4");
        m0 = a10.a("5");
        n0 = a10.a("6");
        o0 = a10.a("7");
        p0 = a10.a("8");
        w wVar4 = new w("1.3.133.16.840.9.44");
        q0 = wVar4;
        w a11 = wVar4.a("1");
        r0 = a11;
        s0 = a11.a("1");
        t0 = a11.a("2");
    }
}
