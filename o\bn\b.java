package o.bn;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.SubscriptionInfo;
import android.telephony.SubscriptionManager;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.Iterator;
import java.util.List;
import o.bn.c;
import o.ee.g;
import o.ee.j;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bn\b.smali */
public final class b extends d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static char[] c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        c();
        ViewConfiguration.getTapTimeout();
        TextUtils.indexOf((CharSequence) "", '0');
        View.resolveSize(0, 0);
        int i = b + 71;
        e = i % 128;
        switch (i % 2 != 0 ? 'H' : (char) 28) {
            case 28:
                return;
            default:
                throw null;
        }
    }

    static void c() {
        char[] cArr = new char[1353];
        ByteBuffer.wrap("\u0087\u008a\u009dp²\b×Ôìý\u0001\u0097'B<mQ\rvß\u008bê \u0082Æ^Ûzð<\u0015\u0087,®6y\u0019\u0017|ðG¬Ð^Ê\u0089åç\u0080\u0000»_ôüî+ÁE¤¢\u009fü0&*ñ\u0005\u009f`m[|¶\u0005\u0090ù\u008bûæ\u0087Á[<v\u0017\u0014qÍlâG\u008e¢T\u009deøDÒ\u0096Í®(\u0093\u0003Y~cY\u0017³Ü®¬\u0089Ùä\u0016,¹6t\u0019\f|ØGø\u0016ï\f8#VF¤}µ\u0090Ì¶0\u00ad2ÀNç\u0092\u001a¿1ÝW\u0004J+aG\u0084\u009d»¬Þúô\u001bë3\u000eD%¾Xª\u007fÏ\u00951\u00885¯CÂ\u008cùì\u001c\u00802^)nL\bc³\u0086\u00ad½·Ó(ö$íz\u0000\u009e'°Zýp\u0015\u0097!\u008am¡ÙÄ\u009dûö\u0011\n4;+{N\u0084e©WÚM\rbc\u0007\u0091<\u0080Ñù÷\u0005ì\u0007\u0081{¦§[\u008apè\u00161\u000b\u001e rÅ¨ú\u0099\u009fÏµ.ª\u0006Oqd\u008b\u0019\u009f>úÔ\u0004É\u0000îv\u0083¹¸Ù]µskh[\r=\"»Ç\u009eüÏ\u0092\u001a·\u0000¬BAºf\u0090\u001b\u00801uÖZ,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fÿbëE\u008e¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÏ¼ê\u0087»é\u001dÌc×6:È\u001då`ôJR\u00ad}°u\u009bÈþýÁ·+J\u000ei\u0011=tÒ_\u00ad¢²\u0084Rï~òqÕÍ8ÿ\u0003þeLH{S\u007f¶Ì\u0099æü¨Æ\u0003)\u0004\f8\u0017Áz\u0093]£§\u0001\u008aNí{ðÏÛ\u0097>·\u0000]k\u0006N0QÂ´\u0094,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fÿbëE\u008e¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÚ¼ê\u0087¸éZÌe×%:Ê\u001dó`½JU\u00adz°<\u009bÖþèÁò+Z\u000em\u0011=t\u0086_ï¢µ\u0084\u0007ïyò=ÕË8ü\u0003þeDHfS;¶\u0082\u0099úüµÆN)V\f>\u0017Áz\u0085]¾§\u0001\u008a\u001dí(ð\u008eÛ\u0087>½\u0000Nk\u0016N \u00945\u008eô¡×ÄMÿi\u0012\u000e4È/ìB\u0094e^\u0098/³\u0015ÕÁÈöã\u0090\u0006D9l\\\u0006vÁiÿ\u008c\u0092§FÚtý\u0012\u0017Ù\nÿ-\u0083@]{@\u009e\u0001°Í«ùÎ\u0081áC\u0004}?\u0013Q×tèo¨\u0082F¥~Ø<òÄ\u0015ú\b¬#GFf,\u008c6O\u0019 |ð,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fÿbëE\u008e¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÙ¼î\u0087¦éIÌy×w:Ü\u001dè`±JW\u00adj°&\u009b\u0098þáÁ½+M\u000e,\u00116tÞ_ý¢µ\u0084Dï~ò4ÕÀ8«\u0003ªeJH(S=¶Ç\u0099©ü®ÆF)\u0002\f(\u0017Òz\u0099]¿§E\u008aTí9ð×ÛÕ>«\u0000Fk\u001fNyQÏ´\u0092\u009f´áI,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fÿbëE\u008e¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÏ¼ì\u0087»éXÌ ×$:Ó\u001dì`ôJX\u00ado°'\u009bÜþ¯Á´+P\u000ei\u0011?tÂ_þ¢ð\u0084Fïxò4Õ\u00848î\u0003³eUH|S&¶\u0082\u0099þü´ÆJ)\u001a\f8\u0017\u0080z¤]³§L\u008aTí8ðÏÛ\u0087>¼\u0000\u000fk\u001bN*Q\u008c´\u0081\u009f£áLÄ\u0014/>2\u008a\u0015ËxäB\\¥\u001f\u0088,\u0093Üö\u0096Ù¬#N\fP\u0016\u00879é\\\u001bg\n\u008as¬\u008f·\u008dÚñý-\u0000\u0000+bM»P\u0094{ø\u009e\"¡\u0013ÄEî¤ñ\u008c\u0014û?\u0001B\u0015ep\u008f\u008e\u0092\u008aµüØ3ãS\u0006?(á3ÑV·y1\u009c\u0014§EÉãì\u009d÷È\u001a6=\u001b@\nj£\u008d\u0099\u0090Î»*Þ\u0015á\f\u000b°.\u00931ÄT,\u007f\u001a\u0082@¤¾ÏÔÒÛõ3\u0018\u0018#EE´h\u0083sÕ¢N¸\u0099\u0097÷ò\u0005É\u0014$m\u0002\u0091\u0019\u0093tïS3®\u001e\u0085|ã¥þ\u008aÕæ0<\u000f\rj[@º_\u0092ºå\u0091\u001fì\u000bËn!\u0090<\u0094\u001bâv-MM¨!\u0086ÿ\u009dÏø©×/2\f\t[g¸BÀYÄ´3\u0093\fî\u0014Ä¸#\u008f>Ç\u0015<pOOT¥°\u0080\u0089\u009fßú\"Ñ\u001e,\u0010\n¦a\u0098|Ô[d¶\u0018\u008dJë¬Æ\u0084ÝÓ8b\u0017\frQH³§â\u0082Ä\u0099`ôvÓ\\)µ\u0004ñcÉ~nUb°Y\u008e¦åæÀÐß\":t\u0011\u0006o÷J°¡Ã¼/\u009bgöMÌ¨+û\u0006\u0085\u001d!xlW\u0002\u00ad§\u0088óçÔÂVÙx4X\u0012\u0087iÿDÂ£\u0000¾~\u0095JóÕÎì%À\u0000R\u001fkzIP\u0087¯ó\u008aßá\u001eü'ÛD1\u0084\fèkÇF^]v¸A\u0096\u0092í¢ÈÊ'\u001d\u0002q\u0018²wÝRæ©>\u0084\u001fãmù°Ô\u0088,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fÿbëE\u008e¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÎ¼æ\u0087·éYÌi×9:Ý\u001d¡`µJ\\\u00ado°<\u009bÖþ¯Á¡+P\u000ea\u0011stÅ_ì¢¢\u0084Cï*ò7ÕÍ8î\u0003²eAH{Ar[¥tË\u00119*(ÇQá\u00adú¯\u0097Ó°\u000fM\"f@\u0000\u0099\u001d¶6ÚÓ\u0000ì1\u0089g£\u0086¼®YÙr\"\u000f>(AÂ¬ß¨øÞ\u0095\u0011®qK\u001d,º6\u007f\u0019\u000b,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fþbâE\u009d¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÒ¼ì\u0087öénÌI×\u001a:\u009a\u001dâ`µJI\u00adj°u\u009bÈþýÁ·+J\u000ei\u0011=tÒ_\u00ad¢¹\u0084Iï*ò%ÕÌ8î\u0003þeAHmS)¶Ë\u0099êü¹,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fþbâE\u009d¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÝ¼£\u0087\u0085étÌM×w:Ù\u001dà`¦J_\u00ad.°<\u009bËþ¯Á +\\\u000ek\u0011:tÕ_ù¢µ\u0084Uïoò5\u001dÄ\u0007\u0013(}M\u008fv\u009e\u009bç½\u001b¦\u0019Ëeì¹\u0011\u0094:ö\\/A\u0000jl\u008f¶°\u0087ÕÑÿ0à\u0018\u0005o.\u0094S\u0088t÷\u009e\u001a\u0083\u001e¤hÉ§òÇ\u0017«9u\"EG#h»\u008d\u0088¶ÈØ4ý\u0002æ\u001d\u000b¶,\u0084QË{?\u009c\u0000\u0081\u001fª¥Ï\u008cðÌ\u001a;?F ME¤n\u0082\u0093\u009aµ?Þ\u0005Ã\\ä§\t\u00922ÀT*y\u0010bP\u0087¬¨ÃÍå÷\u0000\u0018Q=\u0017&©KülÂ\u0096/,®6y\u0019\u0017|åGôª\u008d\u008cq\u0097sú\u000fÝÓ þ\u000b\u009cmEpj[\u0006¾Ü\u0081íä»ÎZÑr4\u0005\u001fþbâE\u009d¯p²t\u0095\u0002øÍÃ\u00ad&Á\b\u001f\u0013/vIYÒ¼ì\u0087öéPÌa×#:Ù\u001dé`ôJ]\u00ada° \u009bÖþëÁò+N\u000ee\u0011'tÎ_\u00ad¢¤\u0084OïoòqÕÖ8î\u0003¹eLH{S+¶Ç\u0099ûü¹ÆG)V\f\u000e\u0017ézº]ú§B\u008a\u0015í)ðÊ+\u00861R\u001e>{ô@Õ\u00ad®\u008b]\u0090]ý&Úð'Ç\f¶jMwZ\\)¹Ð\u0086Üã¡ÉwÖ\u007f3#\u0018úeÈB¨¨tµH\u0092`ÿ¸Ä\u008e!¡\u000ff\u0014Hq,^ó»\u0088\u0080Çî6Ë\u000eÐ\u000f=±\u001a\u0087gßM}ªJ·\u001a\u009cöùÈÆÙ,(\t\u0007\u0016]sþ,®6s\u0019\f|ÑGñª\u0085,\u00876y\u0019\u001b|ÃGîªÀ\u008c\u0003¦\u008b¼u\u0093\u0017öÏÍâ Ì\u0006\u000e\u008cÆ\u00968¹ZÜ\u0082ç¯\n\u0081,@,¨6r\u0019\u0007|ÄGòª\u0089\u008cS\u00974ú\u0011ÝÑ é\u000b\u0083m\\pk[\u001c¾Û\u0081öä\u0082Î\u001dÑT4(\u001fñbÃEµ¯a²L\u0095$øðÃÀ&·\bl\u0013Vv(Yè¼Æ´\u0018®Ï\u0081¡äDßN2 \u0014è\u000fÏb²EK¸@\u0093=õêèèÃ°&j\u0019H|?V÷IÀ¬©\u0087oú_Ý(7§*\u009f\rý`X[V¾,\u0090ä\u008bÝî¬Áy$\\\u001f\u000fqåT\u0096O\u008c¢e\u0085Dø\u0011Òä5Ö(\u0084\u0003.f\u0003YD³î\u0096Ô\u0089\u0081ìbÇT:\u000f\u001cõw\u0092j\u0097Mw O\u009b\u0005ýúÐÍË\u009a.}\u0001Pd\u0004^»±\u0092\u0094®\u008fWâ\u0005Å3?Ç\u0012\u008au¢hVC\u0006¦1\u0098Êó\u0090Ö®ÉN,\u0000Ë[Ñ\u008cþâ\u009b\u0007 \rMck«p\u008c\u001dñ:\bÇ\u0003ì~\u008a©\u0097«¼óY)f\u000b\u0003|)´6\u0083Óêø,\u0085\u001c¢kHìUØr¾\u001ff$P".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1353);
        c = cArr;
        a = -6766635306527672804L;
    }

    static void init$0() {
        $$a = new byte[]{121, 45, -42, -114};
        $$b = 4;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = 1 - r8
            byte[] r0 = o.bn.b.$$a
            int r7 = r7 + 4
            int r6 = 105 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            int r7 = r7 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.b.j(byte, byte, short, java.lang.Object[]):void");
    }

    @Override // o.bn.d
    public final String c(Context context, boolean z) throws c {
        int i = e + 109;
        b = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        i((char) (43780 - ((byte) KeyEvent.getModifierMetaStateMask())), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), View.resolveSize(0, 0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        i((char) (MotionEvent.axisFromString("") + 1), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 15, 5 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        String a2 = a(context, z);
        int i3 = e + Opcodes.LSHR;
        b = i3 % 128;
        int i4 = i3 % 2;
        return a2;
    }

    @Override // o.bn.d
    public final String b(Context context) throws c, o.bo.g {
        int i = b + 49;
        e = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        i((char) (43780 - MotionEvent.axisFromString("")), ViewConfiguration.getTapTimeout() >> 16, View.getDefaultSize(0, 0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        i((char) (64752 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), (ViewConfiguration.getTapTimeout() >> 16) + 21, 6 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        String a2 = a(context);
        switch (a2 == null) {
            case false:
                int i3 = b + 31;
                e = i3 % 128;
                int i4 = i3 % 2;
                return a2;
            default:
                return "";
        }
    }

    @Override // o.bn.d
    public final String e(Context context) throws c {
        int i = e + Opcodes.DMUL;
        b = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        i((char) (43780 - ExpandableListView.getPackedPositionChild(0L)), ViewConfiguration.getLongPressTimeout() >> 16, Color.blue(0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        i((char) (55378 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), 25 - MotionEvent.axisFromString(""), 5 - Drawable.resolveOpacity(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        String f = f(context);
        int i3 = e + Opcodes.LUSHR;
        b = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return f;
            default:
                int i4 = 38 / 0;
                return f;
        }
    }

    @Override // o.bn.d
    public final int a() {
        int i = b + 5;
        int i2 = i % 128;
        e = i2;
        int i3 = i % 2;
        int i4 = i2 + 65;
        b = i4 % 128;
        int i5 = i4 % 2;
        return 0;
    }

    private static String a(Context context, boolean z) throws c {
        String c2;
        int i = b + 15;
        e = i % 128;
        switch (i % 2 != 0 ? 'S' : (char) 31) {
            default:
                switch (97) {
                    case Opcodes.DUP2_X1 /* 93 */:
                        c2 = d(context, z);
                        break;
                }
            case Opcodes.AASTORE /* 83 */:
                c2 = c(context);
                int i2 = e + 25;
                b = i2 % 128;
                int i3 = i2 % 2;
                break;
        }
        g.c();
        Object[] objArr = new Object[1];
        i((char) (TextUtils.getOffsetBefore("", 0) + 43781), Color.green(0), 16 - Color.green(0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        i((char) (Color.alpha(0) + 7304), (Process.myPid() >> 22) + 31, 27 - TextUtils.lastIndexOf("", '0', 0, 0), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(c2).toString());
        switch (c2.length() != 0) {
            case false:
                return c2;
            default:
                return o.c(c2);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:55:0x0289 A[FALL_THROUGH] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static java.lang.String d(android.content.Context r21, boolean r22) throws o.bn.c {
        /*
            Method dump skipped, instructions count: 1014
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.b.d(android.content.Context, boolean):java.lang.String");
    }

    private static String c(Context context) throws c {
        Object obj;
        Object[] objArr = new Object[1];
        i((char) (43781 - KeyEvent.keyCodeFromString("")), ExpandableListView.getPackedPositionType(0L), 16 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        SubscriptionManager subscriptionManager = (SubscriptionManager) context.getSystemService(SubscriptionManager.class);
        g.c();
        Object[] objArr2 = new Object[1];
        i((char) (28124 - KeyEvent.keyCodeFromString("")), (ViewConfiguration.getTouchSlop() >> 8) + 822, 29 - TextUtils.lastIndexOf("", '0'), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        i((char) (47258 - KeyEvent.getDeadChar(0, 0)), 324 - ImageFormat.getBitsPerPixel(0), 47 - TextUtils.getTrimmedLength(""), objArr3);
        SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
        Object[] objArr4 = new Object[1];
        i((char) TextUtils.getOffsetBefore("", 0), View.resolveSize(0, 0) + 852, 3 - Color.argb(0, 0, 0, 0), objArr4);
        String string = sharedPreferences.getString(((String) objArr4[0]).intern(), "");
        SubscriptionInfo subscriptionInfo = null;
        if (string.isEmpty()) {
            List<SubscriptionInfo> activeSubscriptionInfoList = subscriptionManager.getActiveSubscriptionInfoList();
            switch (activeSubscriptionInfoList != null) {
                case false:
                    break;
                default:
                    Iterator<SubscriptionInfo> it = activeSubscriptionInfoList.iterator();
                    while (true) {
                        switch (it.hasNext() ? ';' : '5') {
                            case ';':
                                SubscriptionInfo next = it.next();
                                switch (next == null) {
                                    case true:
                                    default:
                                        subscriptionInfo = next;
                                        break;
                                }
                        }
                    }
                    break;
            }
            switch (subscriptionInfo == null) {
                case true:
                    int i = e + Opcodes.LSHR;
                    b = i % 128;
                    if (i % 2 == 0) {
                        g.c();
                        Object[] objArr5 = new Object[1];
                        i((char) (0 - TextUtils.lastIndexOf("", (char) 11)), 12261 - (Process.myPid() >>> 73), 100 << TextUtils.indexOf((CharSequence) "", (char) 27), objArr5);
                        obj = objArr5[0];
                    } else {
                        g.c();
                        Object[] objArr6 = new Object[1];
                        i((char) (TextUtils.lastIndexOf("", '0') + 1), 855 - (Process.myPid() >> 22), 65 - TextUtils.indexOf((CharSequence) "", '0'), objArr6);
                        obj = objArr6[0];
                    }
                    g.d(intern, ((String) obj).intern());
                    return "";
                default:
                    String d = d(subscriptionInfo.getIccId());
                    SharedPreferences.Editor edit = sharedPreferences.edit();
                    Object[] objArr7 = new Object[1];
                    i((char) (ViewConfiguration.getJumpTapTimeout() >> 16), Color.blue(0) + 852, 3 - (Process.myPid() >> 22), objArr7);
                    edit.putString(((String) objArr7[0]).intern(), d).commit();
                    return subscriptionInfo.getIccId();
            }
        }
        g.c();
        Object[] objArr8 = new Object[1];
        i((char) TextUtils.indexOf("", "", 0, 0), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 921, Color.rgb(0, 0, 0) + 16777273, objArr8);
        g.d(intern, ((String) objArr8[0]).intern());
        List<SubscriptionInfo> activeSubscriptionInfoList2 = subscriptionManager.getActiveSubscriptionInfoList();
        if (activeSubscriptionInfoList2 == null) {
            g.c();
            Object[] objArr9 = new Object[1];
            i((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), 855 - (ViewConfiguration.getEdgeSlop() >> 16), 66 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr9);
            g.d(intern, ((String) objArr9[0]).intern());
            SharedPreferences.Editor edit2 = sharedPreferences.edit();
            Object[] objArr10 = new Object[1];
            i((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 851 - TextUtils.lastIndexOf("", '0'), 3 - (ViewConfiguration.getTouchSlop() >> 8), objArr10);
            edit2.putString(((String) objArr10[0]).intern(), "").commit();
            return "";
        }
        Iterator<SubscriptionInfo> it2 = activeSubscriptionInfoList2.iterator();
        while (it2.hasNext()) {
            SubscriptionInfo next2 = it2.next();
            switch (next2 == null) {
                case true:
                    break;
                default:
                    if (!d(next2.getIccId()).equals(string)) {
                        break;
                    } else {
                        int i2 = e + 43;
                        b = i2 % 128;
                        int i3 = i2 % 2;
                        g.c();
                        Object[] objArr11 = new Object[1];
                        i((char) (12650 - (KeyEvent.getMaxKeyCode() >> 16)), 979 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 73 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr11);
                        g.d(intern, ((String) objArr11[0]).intern());
                        return next2.getIccId();
                    }
            }
        }
        g.c();
        Object[] objArr12 = new Object[1];
        i((char) TextUtils.getCapsMode("", 0, 0), 1051 - TextUtils.getCapsMode("", 0, 0), KeyEvent.getDeadChar(0, 0) + 76, objArr12);
        g.d(intern, ((String) objArr12[0]).intern());
        Iterator<SubscriptionInfo> it3 = activeSubscriptionInfoList2.iterator();
        while (true) {
            switch (!it3.hasNext()) {
                case false:
                    SubscriptionInfo next3 = it3.next();
                    if (next3 != null) {
                        subscriptionInfo = next3;
                        break;
                    }
            }
        }
        if (subscriptionInfo != null) {
            String d2 = d(subscriptionInfo.getIccId());
            SharedPreferences.Editor edit3 = sharedPreferences.edit();
            Object[] objArr13 = new Object[1];
            i((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), 852 - Color.argb(0, 0, 0, 0), 3 - View.combineMeasuredStates(0, 0), objArr13);
            edit3.putString(((String) objArr13[0]).intern(), d2).commit();
            return subscriptionInfo.getIccId();
        }
        int i4 = e + Opcodes.LSUB;
        b = i4 % 128;
        int i5 = i4 % 2;
        g.c();
        Object[] objArr14 = new Object[1];
        i((char) ExpandableListView.getPackedPositionType(0L), 855 - Color.green(0), 66 - Color.alpha(0), objArr14);
        g.d(intern, ((String) objArr14[0]).intern());
        return "";
    }

    private static boolean e() {
        String str = Build.BRAND;
        String str2 = Build.MODEL;
        g.c();
        Object[] objArr = new Object[1];
        i((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 43781), TextUtils.getTrimmedLength(""), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        i((char) (1835 - (ViewConfiguration.getTapTimeout() >> 16)), 1127 - Color.blue(0), 52 - View.resolveSizeAndState(0, 0, 0), objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), str, str2));
        Object[] objArr3 = new Object[1];
        i((char) TextUtils.getOffsetAfter("", 0), 1180 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), 6 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr3);
        switch (str.equals(((String) objArr3[0]).intern()) ? '#' : (char) 14) {
            case '#':
                Object[] objArr4 = new Object[1];
                i((char) ((Process.getThreadPriority(0) + 20) >> 6), View.getDefaultSize(0, 0) + 1185, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 6, objArr4);
                if (str2.equals(((String) objArr4[0]).intern())) {
                    int i = b + Opcodes.LUSHR;
                    e = i % 128;
                    switch (i % 2 == 0) {
                        case true:
                            return false;
                        default:
                            throw null;
                    }
                }
                break;
        }
        Object[] objArr5 = new Object[1];
        i((char) ((Process.getThreadPriority(0) + 20) >> 6), TextUtils.lastIndexOf("", '0', 0) + 1180, 6 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr5);
        if (str.equals(((String) objArr5[0]).intern())) {
            Object[] objArr6 = new Object[1];
            i((char) (TextUtils.indexOf("", "") + 35340), 1193 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), TextUtils.lastIndexOf("", '0', 0, 0) + 8, objArr6);
            if (str2.equals(((String) objArr6[0]).intern())) {
                int i2 = e + 33;
                b = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return true;
                    default:
                        return false;
                }
            }
        }
        Object[] objArr7 = new Object[1];
        i((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 1179 - View.resolveSize(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 7, objArr7);
        switch (str.equals(((String) objArr7[0]).intern()) ? (char) 17 : 'I') {
            default:
                Object[] objArr8 = new Object[1];
                i((char) (41025 - (ViewConfiguration.getScrollBarFadeDuration() >> 16)), TextUtils.indexOf((CharSequence) "", '0', 0) + 1200, Gravity.getAbsoluteGravity(0, 0) + 7, objArr8);
                switch (str2.equals(((String) objArr8[0]).intern()) ? 'J' : '`') {
                    case 'J':
                        int i3 = b + 15;
                        e = i3 % 128;
                        int i4 = i3 % 2;
                        return false;
                }
            case 'I':
                return true;
        }
    }

    private static String d(String str) {
        int i = b + 45;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                return Base64.encodeToString(o.ec.e.d(str.getBytes(j.c())), 10);
            default:
                return Base64.encodeToString(o.ec.e.d(str.getBytes(j.c())), Opcodes.INEG);
        }
    }

    private static String f(Context context) throws c {
        int i = e + 33;
        b = i % 128;
        int i2 = i % 2;
        String str = "";
        Object[] objArr = new Object[1];
        i((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 1206, Gravity.getAbsoluteGravity(0, 0) + 35, objArr);
        if (!o.bt.b.e(context, ((String) objArr[0]).intern())) {
            g.c();
            Object[] objArr2 = new Object[1];
            i((char) (AndroidCharacter.getMirror('0') + 43733), (-1) - TextUtils.lastIndexOf("", '0'), 15 - ExpandableListView.getPackedPositionChild(0L), objArr2);
            String intern = ((String) objArr2[0]).intern();
            Object[] objArr3 = new Object[1];
            i((char) (View.getDefaultSize(0, 0) + 39094), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 1240, 82 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr3);
            g.e(intern, ((String) objArr3[0]).intern());
            throw new c(c.e.a);
        }
        o.ee.e.a();
        String b2 = o.ee.c.b(context);
        g.c();
        Object[] objArr4 = new Object[1];
        i((char) (TextUtils.indexOf("", "") + 43781), (-1) - TextUtils.lastIndexOf("", '0', 0), 16 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr5 = new Object[1];
        i((char) (59380 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 1324, Drawable.resolveOpacity(0, 0) + 29, objArr5);
        g.d(intern2, sb.append(((String) objArr5[0]).intern()).append(b2).toString());
        switch (b2 == null ? (char) 21 : (char) 4) {
            case 21:
                int i3 = b + 77;
                e = i3 % 128;
                int i4 = i3 % 2;
                break;
            default:
                str = b2;
                break;
        }
        String c2 = o.c(str);
        int i5 = e + 7;
        b = i5 % 128;
        switch (i5 % 2 != 0 ? ']' : (char) 29) {
            case 29:
                throw null;
            default:
                return c2;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 762
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bn.b.i(char, int, int, java.lang.Object[]):void");
    }
}
