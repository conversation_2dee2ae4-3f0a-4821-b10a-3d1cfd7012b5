package o.dc;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\h.smali */
public final class h {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final /* synthetic */ h[] a;
    public static final h b;
    public static final h c;
    private static char[] d;
    private static int g;
    private static int i;
    private static char j;
    private final String e;

    static void d() {
        d = new char[]{30556, 30540, 30530, 30539, 30588, 30571, 30538, 30570, 30542};
        j = (char) 17046;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0030  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0028  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0030 -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.dc.h.$$a
            int r6 = r6 * 2
            int r6 = 3 - r6
            int r8 = 73 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            r7 = r6
            goto L38
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1f:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            int r4 = r3 + 1
            if (r3 != r8) goto L30
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L30:
            r3 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L38:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.h.h(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{84, 72, 115, -24};
        $$b = 243;
    }

    private static /* synthetic */ h[] c() {
        h[] hVarArr;
        int i2 = i + 11;
        int i3 = i2 % 128;
        g = i3;
        switch (i2 % 2 != 0 ? (char) 0 : ';') {
            case ';':
                hVarArr = new h[]{c, b};
                break;
            default:
                hVarArr = new h[3];
                hVarArr[1] = c;
                hVarArr[1] = b;
                break;
        }
        int i4 = i3 + Opcodes.DNEG;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return hVarArr;
        }
    }

    public static h valueOf(String str) {
        int i2 = i + 95;
        g = i2 % 128;
        boolean z = i2 % 2 == 0;
        h hVar = (h) Enum.valueOf(h.class, str);
        switch (z) {
            case false:
                int i3 = 58 / 0;
                break;
        }
        int i4 = i + 85;
        g = i4 % 128;
        int i5 = i4 % 2;
        return hVar;
    }

    public static h[] values() {
        int i2 = g + 37;
        i = i2 % 128;
        int i3 = i2 % 2;
        h[] hVarArr = (h[]) a.clone();
        int i4 = g + 13;
        i = i4 % 128;
        int i5 = i4 % 2;
        return hVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        d();
        Object[] objArr = new Object[1];
        f(3 - TextUtils.lastIndexOf("", '0', 0, 0), "\u0000\u0005\u0000\u0003", (byte) (123 - TextUtils.indexOf((CharSequence) "", '0')), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 4, "\u0005\b\u0001\u0007", (byte) (45 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), objArr2);
        c = new h(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        f(3 - (ViewConfiguration.getScrollBarSize() >> 8), "\u0001\u0002㘃", (byte) (40 - Color.red(0)), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        f(4 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), "\u0001\u0002㘃", (byte) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 39), objArr4);
        b = new h(intern2, 1, ((String) objArr4[0]).intern());
        a = c();
        int i2 = i + 65;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    private h(String str, int i2, String str2) {
        this.e = str2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:46:0x016a, code lost:
    
        if (r4.e == r4.a) goto L55;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r28, java.lang.String r29, byte r30, java.lang.Object[] r31) {
        /*
            Method dump skipped, instructions count: 1026
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.h.f(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
