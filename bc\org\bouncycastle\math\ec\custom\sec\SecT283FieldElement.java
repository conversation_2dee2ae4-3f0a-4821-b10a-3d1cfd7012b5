package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.x5;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecT283FieldElement.smali */
public class SecT283FieldElement extends ECFieldElement.AbstractF2m {
    protected long[] a;

    public SecT283FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.bitLength() > 283) {
            throw new IllegalArgumentException("x value invalid for SecT283FieldElement");
        }
        this.a = SecT283Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        long[] a = x5.a();
        SecT283Field.add(this.a, ((SecT283FieldElement) eCFieldElement).a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        long[] a = x5.a();
        SecT283Field.addOne(this.a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        return multiply(eCFieldElement.invert());
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecT283FieldElement) {
            return x5.b(this.a, ((SecT283FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecT283Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return 283;
    }

    public int getK1() {
        return 5;
    }

    public int getK2() {
        return 7;
    }

    public int getK3() {
        return 12;
    }

    public int getM() {
        return 283;
    }

    public int getRepresentation() {
        return 3;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public ECFieldElement halfTrace() {
        long[] a = x5.a();
        SecT283Field.halfTrace(this.a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public boolean hasFastTrace() {
        return true;
    }

    public int hashCode() {
        return Arrays.hashCode(this.a, 0, 5) ^ 2831275;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        long[] a = x5.a();
        SecT283Field.invert(this.a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return x5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return x5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        long[] a = x5.a();
        SecT283Field.multiply(this.a, ((SecT283FieldElement) eCFieldElement).a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        return multiplyPlusProduct(eCFieldElement, eCFieldElement2, eCFieldElement3);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiplyPlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement eCFieldElement3) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT283FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT283FieldElement) eCFieldElement2).a;
        long[] jArr4 = ((SecT283FieldElement) eCFieldElement3).a;
        long[] b = c6.b(9);
        SecT283Field.multiplyAddToExt(jArr, jArr2, b);
        SecT283Field.multiplyAddToExt(jArr3, jArr4, b);
        long[] a = x5.a();
        SecT283Field.reduce(b, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        return this;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        long[] a = x5.a();
        SecT283Field.sqrt(this.a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        long[] a = x5.a();
        SecT283Field.square(this.a, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squareMinusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return squarePlusProduct(eCFieldElement, eCFieldElement2);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePlusProduct(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        long[] jArr = this.a;
        long[] jArr2 = ((SecT283FieldElement) eCFieldElement).a;
        long[] jArr3 = ((SecT283FieldElement) eCFieldElement2).a;
        long[] b = c6.b(9);
        SecT283Field.squareAddToExt(jArr, b);
        SecT283Field.multiplyAddToExt(jArr2, jArr3, b);
        long[] a = x5.a();
        SecT283Field.reduce(b, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement squarePow(int i) {
        if (i < 1) {
            return this;
        }
        long[] a = x5.a();
        SecT283Field.squareN(this.a, i, a);
        return new SecT283FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        return add(eCFieldElement);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return (this.a[0] & 1) != 0;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return x5.c(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement.AbstractF2m
    public int trace() {
        return SecT283Field.trace(this.a);
    }

    public SecT283FieldElement() {
        this.a = x5.a();
    }

    protected SecT283FieldElement(long[] jArr) {
        this.a = jArr;
    }
}
