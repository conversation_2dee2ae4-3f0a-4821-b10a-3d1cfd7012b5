package com.esotericsoftware.kryo;

import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.serializers.ClosureSerializer;
import com.esotericsoftware.kryo.serializers.CollectionSerializer;
import com.esotericsoftware.kryo.serializers.DefaultArraySerializers;
import com.esotericsoftware.kryo.serializers.DefaultSerializers;
import com.esotericsoftware.kryo.serializers.ImmutableCollectionsSerializers;
import com.esotericsoftware.kryo.serializers.MapSerializer;
import com.esotericsoftware.kryo.serializers.OptionalSerializers;
import com.esotericsoftware.kryo.serializers.RecordSerializer;
import com.esotericsoftware.kryo.serializers.TimeSerializers;
import com.esotericsoftware.kryo.util.DefaultClassResolver;
import com.esotericsoftware.kryo.util.DefaultGenerics;
import com.esotericsoftware.kryo.util.DefaultInstantiatorStrategy;
import com.esotericsoftware.kryo.util.Generics;
import com.esotericsoftware.kryo.util.IdentityMap;
import com.esotericsoftware.kryo.util.IntArray;
import com.esotericsoftware.kryo.util.MapReferenceResolver;
import com.esotericsoftware.kryo.util.NoGenerics;
import com.esotericsoftware.kryo.util.ObjectMap;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Modifier;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.BitSet;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.ConcurrentModificationException;
import java.util.Currency;
import java.util.Date;
import java.util.EnumSet;
import java.util.Locale;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentSkipListMap;
import org.objenesis.instantiator.ObjectInstantiator;
import org.objenesis.strategy.InstantiatorStrategy;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\Kryo.smali */
public class Kryo {
    private static final int DEFAULT_SERIALIZER_SIZE = 68;
    public static final byte NOT_NULL = 1;
    private static final int NO_REF = -2;
    public static final byte NULL = 0;
    private static final int REF = -1;
    private boolean autoReset;
    private ClassLoader classLoader;
    private final ClassResolver classResolver;
    private ObjectMap context;
    private int copyDepth;
    private boolean copyReferences;
    private boolean copyShallow;
    private SerializerFactory defaultSerializer;
    private final ArrayList<DefaultSerializerEntry> defaultSerializers;
    private int depth;
    private Generics generics;
    private ObjectMap graphContext;
    private final int lowPriorityDefaultSerializerCount;
    private int maxDepth;
    private Object needsCopyReference;
    private int nextRegisterID;
    private IdentityMap originalToCopy;
    private Object readObject;
    private final IntArray readReferenceIds;
    private ReferenceResolver referenceResolver;
    private boolean references;
    private boolean registrationRequired;
    private InstantiatorStrategy strategy;
    private volatile Thread thread;
    private boolean warnUnregisteredClasses;

    public Kryo() {
        this(new DefaultClassResolver(), null);
    }

    public Kryo(ReferenceResolver referenceResolver) {
        this(new DefaultClassResolver(), referenceResolver);
    }

    public Kryo(ClassResolver classResolver, ReferenceResolver referenceResolver) {
        this.defaultSerializer = new SerializerFactory.FieldSerializerFactory();
        ArrayList<DefaultSerializerEntry> arrayList = new ArrayList<>(DEFAULT_SERIALIZER_SIZE);
        this.defaultSerializers = arrayList;
        this.classLoader = getClass().getClassLoader();
        this.strategy = new DefaultInstantiatorStrategy();
        this.registrationRequired = true;
        this.maxDepth = Integer.MAX_VALUE;
        this.autoReset = true;
        this.readReferenceIds = new IntArray(0);
        this.copyReferences = true;
        this.generics = new DefaultGenerics(this);
        if (classResolver == null) {
            throw new IllegalArgumentException("classResolver cannot be null.");
        }
        this.classResolver = classResolver;
        classResolver.setKryo(this);
        this.referenceResolver = referenceResolver;
        if (referenceResolver != null) {
            referenceResolver.setKryo(this);
            this.references = true;
        }
        addDefaultSerializer(byte[].class, DefaultArraySerializers.ByteArraySerializer.class);
        addDefaultSerializer(char[].class, DefaultArraySerializers.CharArraySerializer.class);
        addDefaultSerializer(short[].class, DefaultArraySerializers.ShortArraySerializer.class);
        addDefaultSerializer(int[].class, DefaultArraySerializers.IntArraySerializer.class);
        addDefaultSerializer(long[].class, DefaultArraySerializers.LongArraySerializer.class);
        addDefaultSerializer(float[].class, DefaultArraySerializers.FloatArraySerializer.class);
        addDefaultSerializer(double[].class, DefaultArraySerializers.DoubleArraySerializer.class);
        addDefaultSerializer(boolean[].class, DefaultArraySerializers.BooleanArraySerializer.class);
        addDefaultSerializer(String[].class, DefaultArraySerializers.StringArraySerializer.class);
        addDefaultSerializer(Object[].class, DefaultArraySerializers.ObjectArraySerializer.class);
        addDefaultSerializer(BigInteger.class, DefaultSerializers.BigIntegerSerializer.class);
        addDefaultSerializer(BigDecimal.class, DefaultSerializers.BigDecimalSerializer.class);
        addDefaultSerializer(Class.class, DefaultSerializers.ClassSerializer.class);
        addDefaultSerializer(Date.class, DefaultSerializers.DateSerializer.class);
        addDefaultSerializer(Enum.class, DefaultSerializers.EnumSerializer.class);
        addDefaultSerializer(EnumSet.class, DefaultSerializers.EnumSetSerializer.class);
        addDefaultSerializer(Currency.class, DefaultSerializers.CurrencySerializer.class);
        addDefaultSerializer(StringBuffer.class, DefaultSerializers.StringBufferSerializer.class);
        addDefaultSerializer(StringBuilder.class, DefaultSerializers.StringBuilderSerializer.class);
        addDefaultSerializer(Collections.EMPTY_LIST.getClass(), DefaultSerializers.CollectionsEmptyListSerializer.class);
        addDefaultSerializer(Collections.EMPTY_MAP.getClass(), DefaultSerializers.CollectionsEmptyMapSerializer.class);
        addDefaultSerializer(Collections.EMPTY_SET.getClass(), DefaultSerializers.CollectionsEmptySetSerializer.class);
        addDefaultSerializer(Collections.singletonList(null).getClass(), DefaultSerializers.CollectionsSingletonListSerializer.class);
        addDefaultSerializer(Collections.singletonMap(null, null).getClass(), DefaultSerializers.CollectionsSingletonMapSerializer.class);
        addDefaultSerializer(Collections.singleton(null).getClass(), DefaultSerializers.CollectionsSingletonSetSerializer.class);
        addDefaultSerializer(TreeSet.class, DefaultSerializers.TreeSetSerializer.class);
        addDefaultSerializer(Collection.class, CollectionSerializer.class);
        addDefaultSerializer(ConcurrentSkipListMap.class, DefaultSerializers.ConcurrentSkipListMapSerializer.class);
        addDefaultSerializer(TreeMap.class, DefaultSerializers.TreeMapSerializer.class);
        addDefaultSerializer(Map.class, MapSerializer.class);
        addDefaultSerializer(TimeZone.class, DefaultSerializers.TimeZoneSerializer.class);
        addDefaultSerializer(Calendar.class, DefaultSerializers.CalendarSerializer.class);
        addDefaultSerializer(Locale.class, DefaultSerializers.LocaleSerializer.class);
        addDefaultSerializer(Charset.class, DefaultSerializers.CharsetSerializer.class);
        addDefaultSerializer(URL.class, DefaultSerializers.URLSerializer.class);
        addDefaultSerializer(Arrays.asList(new Object[0]).getClass(), DefaultSerializers.ArraysAsListSerializer.class);
        addDefaultSerializer(Void.TYPE, new DefaultSerializers.VoidSerializer());
        addDefaultSerializer(PriorityQueue.class, new DefaultSerializers.PriorityQueueSerializer());
        addDefaultSerializer(BitSet.class, new DefaultSerializers.BitSetSerializer());
        addDefaultSerializer(KryoSerializable.class, DefaultSerializers.KryoSerializableSerializer.class);
        OptionalSerializers.addDefaultSerializers(this);
        TimeSerializers.addDefaultSerializers(this);
        ImmutableCollectionsSerializers.addDefaultSerializers(this);
        if (Util.isClassAvailable("java.lang.Record")) {
            addDefaultSerializer("java.lang.Record", RecordSerializer.class);
        }
        this.lowPriorityDefaultSerializerCount = arrayList.size();
        register(Integer.TYPE, new DefaultSerializers.IntSerializer());
        register(String.class, new DefaultSerializers.StringSerializer());
        register(Float.TYPE, new DefaultSerializers.FloatSerializer());
        register(Boolean.TYPE, new DefaultSerializers.BooleanSerializer());
        register(Byte.TYPE, new DefaultSerializers.ByteSerializer());
        register(Character.TYPE, new DefaultSerializers.CharSerializer());
        register(Short.TYPE, new DefaultSerializers.ShortSerializer());
        register(Long.TYPE, new DefaultSerializers.LongSerializer());
        register(Double.TYPE, new DefaultSerializers.DoubleSerializer());
    }

    public void setDefaultSerializer(SerializerFactory serializer) {
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        this.defaultSerializer = serializer;
    }

    public void setDefaultSerializer(Class<? extends Serializer> serializer) {
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        this.defaultSerializer = new SerializerFactory.ReflectionSerializerFactory(serializer);
    }

    public void addDefaultSerializer(Class type, Serializer serializer) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        insertDefaultSerializer(type, new SerializerFactory.SingletonSerializerFactory(serializer));
    }

    public void addDefaultSerializer(Class type, SerializerFactory serializerFactory) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (serializerFactory == null) {
            throw new IllegalArgumentException("serializerFactory cannot be null.");
        }
        insertDefaultSerializer(type, serializerFactory);
    }

    private void addDefaultSerializer(String className, Class<? extends Serializer> serializer) {
        try {
            addDefaultSerializer(Class.forName(className), serializer);
        } catch (ClassNotFoundException e) {
            throw new KryoException("default serializer cannot be added: " + className);
        }
    }

    public void addDefaultSerializer(Class type, Class<? extends Serializer> serializerClass) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (serializerClass == null) {
            throw new IllegalArgumentException("serializerClass cannot be null.");
        }
        insertDefaultSerializer(type, new SerializerFactory.ReflectionSerializerFactory(serializerClass));
    }

    private int insertDefaultSerializer(Class type, SerializerFactory factory) {
        int lowest = 0;
        int n = this.defaultSerializers.size() - this.lowPriorityDefaultSerializerCount;
        for (int i = 0; i < n; i++) {
            if (type.isAssignableFrom(this.defaultSerializers.get(i).type)) {
                lowest = i + 1;
            }
        }
        this.defaultSerializers.add(lowest, new DefaultSerializerEntry(type, factory));
        return lowest;
    }

    public Serializer getDefaultSerializer(Class type) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        Serializer serializerForAnnotation = getDefaultSerializerForAnnotatedType(type);
        if (serializerForAnnotation != null) {
            return serializerForAnnotation;
        }
        int n = this.defaultSerializers.size();
        for (int i = 0; i < n; i++) {
            DefaultSerializerEntry entry = this.defaultSerializers.get(i);
            if (entry.type.isAssignableFrom(type) && entry.serializerFactory.isSupported(type)) {
                return entry.serializerFactory.newSerializer(this, type);
            }
        }
        return newDefaultSerializer(type);
    }

    protected Serializer getDefaultSerializerForAnnotatedType(Class type) {
        if (type.isAnnotationPresent(DefaultSerializer.class)) {
            DefaultSerializer annotation = (DefaultSerializer) type.getAnnotation(DefaultSerializer.class);
            return Util.newFactory(annotation.serializerFactory(), annotation.value()).newSerializer(this, type);
        }
        return null;
    }

    protected Serializer newDefaultSerializer(Class type) {
        return this.defaultSerializer.newSerializer(this, type);
    }

    public Registration register(Class type) {
        Registration registration = this.classResolver.getRegistration(type);
        return registration != null ? registration : register(type, getDefaultSerializer(type));
    }

    public Registration register(Class type, int id) {
        Registration registration = this.classResolver.getRegistration(type);
        return registration != null ? registration : register(type, getDefaultSerializer(type), id);
    }

    public Registration register(Class type, Serializer serializer) {
        Registration registration = this.classResolver.getRegistration(type);
        if (registration != null) {
            registration.setSerializer(serializer);
            return registration;
        }
        return this.classResolver.register(new Registration(type, serializer, getNextRegistrationId()));
    }

    public Registration register(Class type, Serializer serializer, int id) {
        if (id < 0) {
            throw new IllegalArgumentException("id must be >= 0: " + id);
        }
        return register(new Registration(type, serializer, id));
    }

    public Registration register(Registration registration) {
        int id = registration.getId();
        if (id < 0) {
            throw new IllegalArgumentException("id must be > 0: " + id);
        }
        Registration existing = this.classResolver.unregister(id);
        if (Log.DEBUG && existing != null && existing.getType() != registration.getType()) {
            Log.debug("kryo", "Registration overwritten: " + existing + " -> " + registration);
        }
        return this.classResolver.register(registration);
    }

    public int getNextRegistrationId() {
        while (true) {
            int i = this.nextRegisterID;
            if (i != -2) {
                if (this.classResolver.getRegistration(i) == null) {
                    return this.nextRegisterID;
                }
                this.nextRegisterID++;
            } else {
                throw new KryoException("No registration IDs are available.");
            }
        }
    }

    public Registration getRegistration(Class type) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        Registration registration = this.classResolver.getRegistration(type);
        if (registration == null) {
            if (Proxy.isProxyClass(type)) {
                registration = getRegistration(InvocationHandler.class);
            } else if (!type.isEnum() && Enum.class.isAssignableFrom(type) && type != Enum.class) {
                while (true) {
                    type = type.getSuperclass();
                    if (type == null) {
                        break;
                    }
                    if (type.isEnum()) {
                        registration = this.classResolver.getRegistration(type);
                        break;
                    }
                }
            } else if (EnumSet.class.isAssignableFrom(type)) {
                registration = this.classResolver.getRegistration(EnumSet.class);
            } else if (isClosure(type)) {
                registration = this.classResolver.getRegistration(ClosureSerializer.Closure.class);
            }
            if (registration == null) {
                if (this.registrationRequired) {
                    throw new IllegalArgumentException(unregisteredClassMessage(type));
                }
                if (Log.WARN && this.warnUnregisteredClasses) {
                    Log.warn(unregisteredClassMessage(type));
                }
                return this.classResolver.registerImplicit(type);
            }
            return registration;
        }
        return registration;
    }

    protected String unregisteredClassMessage(Class type) {
        return "Class is not registered: " + Util.className(type) + "\nNote: To register this class use: kryo.register(" + Util.canonicalName(type) + ".class);";
    }

    public Registration getRegistration(int classID) {
        return this.classResolver.getRegistration(classID);
    }

    public Serializer getSerializer(Class type) {
        return getRegistration(type).getSerializer();
    }

    public Registration writeClass(Output output, Class type) {
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        try {
            return this.classResolver.writeClass(output, type);
        } finally {
            if (this.depth == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public void writeObject(Output output, Object object) {
        int i;
        boolean z;
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        if (object == null) {
            throw new IllegalArgumentException("object cannot be null.");
        }
        beginObject();
        try {
            if (this.references && writeReferenceOrNull(output, object, false)) {
                if (i == 0) {
                    if (z) {
                        return;
                    } else {
                        return;
                    }
                }
                return;
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", object, output.position());
            }
            getRegistration(object.getClass()).getSerializer().write(this, output, object);
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 != 0 || !this.autoReset) {
                return;
            }
            reset();
        } finally {
            i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public void writeObject(Output output, Object object, Serializer serializer) {
        int i;
        boolean z;
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        if (object == null) {
            throw new IllegalArgumentException("object cannot be null.");
        }
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        beginObject();
        try {
            if (this.references && writeReferenceOrNull(output, object, false)) {
                if (i == 0) {
                    if (z) {
                        return;
                    } else {
                        return;
                    }
                }
                return;
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", object, output.position());
            }
            serializer.write(this, output, object);
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 != 0 || !this.autoReset) {
                return;
            }
            reset();
        } finally {
            i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public void writeObjectOrNull(Output output, Object object, Class type) {
        int i;
        boolean z;
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        beginObject();
        try {
            Serializer serializer = getRegistration(type).getSerializer();
            if (this.references) {
                if (writeReferenceOrNull(output, object, true)) {
                    if (i == 0) {
                        if (z) {
                            return;
                        } else {
                            return;
                        }
                    }
                    return;
                }
            } else if (!serializer.getAcceptsNull()) {
                if (object == null) {
                    if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                        Util.log("Write", object, output.position());
                    }
                    output.writeByte((byte) 0);
                    int i2 = this.depth - 1;
                    this.depth = i2;
                    if (i2 == 0 && this.autoReset) {
                        reset();
                        return;
                    }
                    return;
                }
                if (Log.TRACE) {
                    Log.trace("kryo", "Write: <not null>" + Util.pos(output.position()));
                }
                output.writeByte((byte) 1);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", object, output.position());
            }
            serializer.write(this, output, object);
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        } finally {
            i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public void writeObjectOrNull(Output output, Object object, Serializer serializer) {
        int i;
        boolean z;
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        beginObject();
        try {
            if (this.references) {
                if (writeReferenceOrNull(output, object, true)) {
                    if (i == 0) {
                        if (z) {
                            return;
                        } else {
                            return;
                        }
                    }
                    return;
                }
            } else if (!serializer.getAcceptsNull()) {
                if (object == null) {
                    if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                        Util.log("Write", null, output.position());
                    }
                    output.writeByte((byte) 0);
                    int i2 = this.depth - 1;
                    this.depth = i2;
                    if (i2 == 0 && this.autoReset) {
                        reset();
                        return;
                    }
                    return;
                }
                if (Log.TRACE) {
                    Log.trace("kryo", "Write: <not null>" + Util.pos(output.position()));
                }
                output.writeByte((byte) 1);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", object, output.position());
            }
            serializer.write(this, output, object);
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        } finally {
            i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public void writeClassAndObject(Output output, Object object) {
        int i;
        boolean z;
        if (output == null) {
            throw new IllegalArgumentException("output cannot be null.");
        }
        beginObject();
        try {
            if (object == null) {
                writeClass(output, null);
                if (i == 0) {
                    if (z) {
                        return;
                    } else {
                        return;
                    }
                }
                return;
            }
            Registration registration = writeClass(output, object.getClass());
            if (this.references && writeReferenceOrNull(output, object, false)) {
                int i2 = this.depth - 1;
                this.depth = i2;
                if (i2 == 0 && this.autoReset) {
                    reset();
                    return;
                }
                return;
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", object, output.position());
            }
            registration.getSerializer().write(this, output, object);
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        } finally {
            i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
        }
    }

    boolean writeReferenceOrNull(Output output, Object object, boolean mayBeNull) {
        if (object == null) {
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Write", null, output.position());
            }
            output.writeByte((byte) 0);
            return true;
        }
        if (!this.referenceResolver.useReferences(object.getClass())) {
            if (mayBeNull) {
                if (Log.TRACE) {
                    Log.trace("kryo", "Write: <not null>" + Util.pos(output.position()));
                }
                output.writeByte((byte) 1);
            }
            return false;
        }
        int id = this.referenceResolver.getWrittenId(object);
        if (id != -1) {
            if (Log.DEBUG) {
                Log.debug("kryo", "Write reference " + id + ": " + Util.string(object) + Util.pos(output.position()));
            }
            output.writeVarInt(id + 2, true);
            return true;
        }
        int id2 = this.referenceResolver.addWrittenObject(object);
        if (Log.TRACE) {
            Log.trace("kryo", "Write: <not null>" + Util.pos(output.position()));
        }
        output.writeByte((byte) 1);
        if (Log.TRACE) {
            Log.trace("kryo", "Write initial reference " + id2 + ": " + Util.string(object) + Util.pos(output.position()));
        }
        return false;
    }

    public Registration readClass(Input input) {
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        try {
            return this.classResolver.readClass(input);
        } finally {
            if (this.depth == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public <T> T readObject(Input input, Class<T> cls) {
        T t;
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        if (cls == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        beginObject();
        try {
            if (this.references) {
                int readReferenceOrNull = readReferenceOrNull(input, cls, false);
                if (readReferenceOrNull == -1) {
                    return (T) this.readObject;
                }
                t = (T) getRegistration(cls).getSerializer().read(this, input, cls);
                if (readReferenceOrNull == this.readReferenceIds.size) {
                    reference(t);
                }
            } else {
                t = (T) getRegistration(cls).getSerializer().read(this, input, cls);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Read", t, input.position());
            }
            int i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
            return t;
        } finally {
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public <T> T readObject(Input input, Class<T> cls, Serializer serializer) {
        T t;
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        if (cls == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        beginObject();
        try {
            if (this.references) {
                int readReferenceOrNull = readReferenceOrNull(input, cls, false);
                if (readReferenceOrNull == -1) {
                    return (T) this.readObject;
                }
                t = (T) serializer.read(this, input, cls);
                if (readReferenceOrNull == this.readReferenceIds.size) {
                    reference(t);
                }
            } else {
                t = (T) serializer.read(this, input, cls);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Read", t, input.position());
            }
            int i = this.depth - 1;
            this.depth = i;
            if (i == 0 && this.autoReset) {
                reset();
            }
            return t;
        } finally {
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public <T> T readObjectOrNull(Input input, Class<T> cls) {
        T t;
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        if (cls == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        beginObject();
        try {
            if (this.references) {
                int readReferenceOrNull = readReferenceOrNull(input, cls, true);
                if (readReferenceOrNull == -1) {
                    return (T) this.readObject;
                }
                t = (T) getRegistration(cls).getSerializer().read(this, input, cls);
                if (readReferenceOrNull == this.readReferenceIds.size) {
                    reference(t);
                }
            } else {
                Serializer serializer = getRegistration(cls).getSerializer();
                if (!serializer.getAcceptsNull() && input.readByte() == 0) {
                    if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                        Util.log("Read", null, input.position());
                    }
                    int i = this.depth - 1;
                    this.depth = i;
                    if (i == 0 && this.autoReset) {
                        reset();
                    }
                    return null;
                }
                t = (T) serializer.read(this, input, cls);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Read", t, input.position());
            }
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 == 0 && this.autoReset) {
                reset();
            }
            return t;
        } finally {
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public <T> T readObjectOrNull(Input input, Class<T> cls, Serializer serializer) {
        T t;
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        if (cls == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (serializer == null) {
            throw new IllegalArgumentException("serializer cannot be null.");
        }
        beginObject();
        try {
            if (this.references) {
                int readReferenceOrNull = readReferenceOrNull(input, cls, true);
                if (readReferenceOrNull == -1) {
                    return (T) this.readObject;
                }
                t = (T) serializer.read(this, input, cls);
                if (readReferenceOrNull == this.readReferenceIds.size) {
                    reference(t);
                }
            } else {
                if (!serializer.getAcceptsNull() && input.readByte() == 0) {
                    if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                        Util.log("Read", null, input.position());
                    }
                    int i = this.depth - 1;
                    this.depth = i;
                    if (i == 0 && this.autoReset) {
                        reset();
                    }
                    return null;
                }
                t = (T) serializer.read(this, input, cls);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Read", t, input.position());
            }
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 == 0 && this.autoReset) {
                reset();
            }
            return t;
        } finally {
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        }
    }

    public Object readClassAndObject(Input input) {
        Object object;
        if (input == null) {
            throw new IllegalArgumentException("input cannot be null.");
        }
        beginObject();
        try {
            Registration registration = readClass(input);
            if (registration == null) {
                return null;
            }
            Class type = registration.getType();
            if (this.references) {
                int stackSize = readReferenceOrNull(input, type, false);
                if (stackSize == -1) {
                    Object obj = this.readObject;
                    int i = this.depth - 1;
                    this.depth = i;
                    if (i == 0 && this.autoReset) {
                        reset();
                    }
                    return obj;
                }
                object = registration.getSerializer().read(this, input, type);
                if (stackSize == this.readReferenceIds.size) {
                    reference(object);
                }
            } else {
                object = registration.getSerializer().read(this, input, type);
            }
            if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                Util.log("Read", object, input.position());
            }
            int i2 = this.depth - 1;
            this.depth = i2;
            if (i2 == 0 && this.autoReset) {
                reset();
            }
            return object;
        } finally {
            int i3 = this.depth - 1;
            this.depth = i3;
            if (i3 == 0 && this.autoReset) {
                reset();
            }
        }
    }

    int readReferenceOrNull(Input input, Class type, boolean mayBeNull) {
        int id;
        if (type.isPrimitive()) {
            type = Util.getWrapperClass(type);
        }
        boolean referencesSupported = this.referenceResolver.useReferences(type);
        if (mayBeNull) {
            id = input.readVarInt(true);
            if (id == 0) {
                if (Log.TRACE || (Log.DEBUG && this.depth == 1)) {
                    Util.log("Read", null, input.position());
                }
                this.readObject = null;
                return -1;
            }
            if (!referencesSupported) {
                this.readReferenceIds.add(-2);
                return this.readReferenceIds.size;
            }
        } else {
            if (!referencesSupported) {
                this.readReferenceIds.add(-2);
                return this.readReferenceIds.size;
            }
            id = input.readVarInt(true);
        }
        if (id == 1) {
            if (Log.TRACE) {
                Log.trace("kryo", "Read: <not null>" + Util.pos(input.position()));
            }
            int id2 = this.referenceResolver.nextReadId(type);
            if (Log.TRACE) {
                Log.trace("kryo", "Read initial reference " + id2 + ": " + Util.className(type) + Util.pos(input.position()));
            }
            this.readReferenceIds.add(id2);
            return this.readReferenceIds.size;
        }
        int id3 = id - 2;
        try {
            this.readObject = this.referenceResolver.getReadObject(type, id3);
            if (Log.DEBUG) {
                Log.debug("kryo", "Read reference " + id3 + ": " + Util.string(this.readObject) + Util.pos(input.position()));
            }
            return -1;
        } catch (Exception e) {
            throw new KryoException("Unable to resolve reference for " + Util.className(type) + " with id: " + id3, e);
        }
    }

    public void reference(Object object) {
        int id;
        if (this.copyDepth > 0) {
            Object obj = this.needsCopyReference;
            if (obj != null) {
                if (object == null) {
                    throw new IllegalArgumentException("object cannot be null.");
                }
                this.originalToCopy.put(obj, object);
                this.needsCopyReference = null;
                return;
            }
            return;
        }
        if (!this.references || object == null || (id = this.readReferenceIds.pop()) == -2) {
            return;
        }
        this.referenceResolver.setReadObject(id, object);
    }

    public void reset() {
        this.depth = 0;
        ObjectMap objectMap = this.graphContext;
        if (objectMap != null) {
            objectMap.clear(2048);
        }
        this.classResolver.reset();
        if (this.references) {
            this.referenceResolver.reset();
            this.readObject = null;
        }
        this.copyDepth = 0;
        IdentityMap identityMap = this.originalToCopy;
        if (identityMap != null) {
            identityMap.clear(2048);
        }
        if (Log.TRACE) {
            Log.trace("kryo", "Object graph complete.");
        }
    }

    public <T> T copy(T t) {
        T t2;
        if (t == null) {
            return null;
        }
        if (this.copyShallow) {
            return t;
        }
        this.copyDepth++;
        try {
            if (this.originalToCopy == null) {
                this.originalToCopy = new IdentityMap();
            }
            T t3 = (T) this.originalToCopy.get(t);
            if (t3 != null) {
                return t3;
            }
            if (this.copyReferences) {
                this.needsCopyReference = t;
            }
            if (t instanceof KryoCopyable) {
                t2 = (T) ((KryoCopyable) t).copy(this);
            } else {
                t2 = (T) getSerializer(t.getClass()).copy(this, t);
            }
            if (this.needsCopyReference != null) {
                reference(t2);
            }
            if (Log.TRACE || (Log.DEBUG && this.copyDepth == 1)) {
                Util.log("Copy", t2, -1);
            }
            int i = this.copyDepth - 1;
            this.copyDepth = i;
            if (i == 0) {
                reset();
            }
            return t2;
        } finally {
            int i2 = this.copyDepth - 1;
            this.copyDepth = i2;
            if (i2 == 0) {
                reset();
            }
        }
    }

    public <T> T copy(T t, Serializer serializer) {
        T t2;
        if (t == null) {
            return null;
        }
        if (this.copyShallow) {
            return t;
        }
        this.copyDepth++;
        try {
            if (this.originalToCopy == null) {
                this.originalToCopy = new IdentityMap();
            }
            T t3 = (T) this.originalToCopy.get(t);
            if (t3 != null) {
                return t3;
            }
            if (this.copyReferences) {
                this.needsCopyReference = t;
            }
            if (t instanceof KryoCopyable) {
                t2 = (T) ((KryoCopyable) t).copy(this);
            } else {
                t2 = (T) serializer.copy(this, t);
            }
            if (this.needsCopyReference != null) {
                reference(t2);
            }
            if (Log.TRACE || (Log.DEBUG && this.copyDepth == 1)) {
                Util.log("Copy", t2, -1);
            }
            int i = this.copyDepth - 1;
            this.copyDepth = i;
            if (i == 0) {
                reset();
            }
            return t2;
        } finally {
            int i2 = this.copyDepth - 1;
            this.copyDepth = i2;
            if (i2 == 0) {
                reset();
            }
        }
    }

    public <T> T copyShallow(T t) {
        T t2;
        if (t == null) {
            return null;
        }
        this.copyDepth++;
        this.copyShallow = true;
        try {
            if (this.originalToCopy == null) {
                this.originalToCopy = new IdentityMap();
            }
            T t3 = (T) this.originalToCopy.get(t);
            if (t3 != null) {
                return t3;
            }
            if (this.copyReferences) {
                this.needsCopyReference = t;
            }
            if (t instanceof KryoCopyable) {
                t2 = (T) ((KryoCopyable) t).copy(this);
            } else {
                t2 = (T) getSerializer(t.getClass()).copy(this, t);
            }
            if (this.needsCopyReference != null) {
                reference(t2);
            }
            if (Log.TRACE || (Log.DEBUG && this.copyDepth == 1)) {
                Util.log("Shallow copy", t2, -1);
            }
            this.copyShallow = false;
            int i = this.copyDepth - 1;
            this.copyDepth = i;
            if (i == 0) {
                reset();
            }
            return t2;
        } finally {
            this.copyShallow = false;
            int i2 = this.copyDepth - 1;
            this.copyDepth = i2;
            if (i2 == 0) {
                reset();
            }
        }
    }

    public <T> T copyShallow(T t, Serializer serializer) {
        T t2;
        if (t == null) {
            return null;
        }
        this.copyDepth++;
        this.copyShallow = true;
        try {
            if (this.originalToCopy == null) {
                this.originalToCopy = new IdentityMap();
            }
            T t3 = (T) this.originalToCopy.get(t);
            if (t3 != null) {
                return t3;
            }
            if (this.copyReferences) {
                this.needsCopyReference = t;
            }
            if (t instanceof KryoCopyable) {
                t2 = (T) ((KryoCopyable) t).copy(this);
            } else {
                t2 = (T) serializer.copy(this, t);
            }
            if (this.needsCopyReference != null) {
                reference(t2);
            }
            if (Log.TRACE || (Log.DEBUG && this.copyDepth == 1)) {
                Util.log("Shallow copy", t2, -1);
            }
            this.copyShallow = false;
            int i = this.copyDepth - 1;
            this.copyDepth = i;
            if (i == 0) {
                reset();
            }
            return t2;
        } finally {
            this.copyShallow = false;
            int i2 = this.copyDepth - 1;
            this.copyDepth = i2;
            if (i2 == 0) {
                reset();
            }
        }
    }

    private void beginObject() {
        if (Log.DEBUG) {
            if (this.depth == 0) {
                this.thread = Thread.currentThread();
            } else if (this.thread != Thread.currentThread()) {
                throw new ConcurrentModificationException("Kryo must not be accessed concurrently by multiple threads.");
            }
        }
        int i = this.depth;
        if (i == this.maxDepth) {
            throw new KryoException("Max depth exceeded: " + this.depth);
        }
        this.depth = i + 1;
    }

    public ClassResolver getClassResolver() {
        return this.classResolver;
    }

    public ReferenceResolver getReferenceResolver() {
        return this.referenceResolver;
    }

    public void setClassLoader(ClassLoader classLoader) {
        if (classLoader == null) {
            throw new IllegalArgumentException("classLoader cannot be null.");
        }
        this.classLoader = classLoader;
    }

    public ClassLoader getClassLoader() {
        return this.classLoader;
    }

    public void setRegistrationRequired(boolean registrationRequired) {
        this.registrationRequired = registrationRequired;
        if (Log.TRACE) {
            Log.trace("kryo", "Registration required: " + registrationRequired);
        }
    }

    public boolean isRegistrationRequired() {
        return this.registrationRequired;
    }

    public void setWarnUnregisteredClasses(boolean warnUnregisteredClasses) {
        this.warnUnregisteredClasses = warnUnregisteredClasses;
        if (Log.TRACE) {
            Log.trace("kryo", "Warn unregistered classes: " + warnUnregisteredClasses);
        }
    }

    public boolean getWarnUnregisteredClasses() {
        return this.warnUnregisteredClasses;
    }

    public boolean setReferences(boolean references) {
        boolean old = this.references;
        if (references == old) {
            return references;
        }
        if (old) {
            this.referenceResolver.reset();
            this.readObject = null;
        }
        this.references = references;
        if (references && this.referenceResolver == null) {
            this.referenceResolver = new MapReferenceResolver();
        }
        if (Log.TRACE) {
            Log.trace("kryo", "References: " + references);
        }
        return !references;
    }

    public void setCopyReferences(boolean copyReferences) {
        this.copyReferences = copyReferences;
    }

    public void setReferenceResolver(ReferenceResolver referenceResolver) {
        if (referenceResolver == null) {
            throw new IllegalArgumentException("referenceResolver cannot be null.");
        }
        this.references = true;
        this.referenceResolver = referenceResolver;
        if (Log.TRACE) {
            Log.trace("kryo", "Reference resolver: " + referenceResolver.getClass().getName());
        }
    }

    public boolean getReferences() {
        return this.references;
    }

    public void setInstantiatorStrategy(InstantiatorStrategy strategy) {
        this.strategy = strategy;
    }

    public InstantiatorStrategy getInstantiatorStrategy() {
        return this.strategy;
    }

    protected ObjectInstantiator newInstantiator(Class type) {
        return this.strategy.newInstantiatorOf(type);
    }

    public <T> T newInstance(Class<T> cls) {
        Registration registration = getRegistration(cls);
        ObjectInstantiator instantiator = registration.getInstantiator();
        if (instantiator == null) {
            instantiator = newInstantiator(cls);
            registration.setInstantiator(instantiator);
        }
        return (T) instantiator.newInstance();
    }

    public ObjectMap getContext() {
        if (this.context == null) {
            this.context = new ObjectMap();
        }
        return this.context;
    }

    public ObjectMap getGraphContext() {
        if (this.graphContext == null) {
            this.graphContext = new ObjectMap();
        }
        return this.graphContext;
    }

    public int getDepth() {
        return this.depth;
    }

    public IdentityMap getOriginalToCopyMap() {
        return this.originalToCopy;
    }

    public void setAutoReset(boolean autoReset) {
        this.autoReset = autoReset;
    }

    public void setMaxDepth(int maxDepth) {
        if (maxDepth <= 0) {
            throw new IllegalArgumentException("maxDepth must be > 0.");
        }
        this.maxDepth = maxDepth;
    }

    public boolean isFinal(Class type) {
        if (type != null) {
            return type.isArray() ? Modifier.isFinal(Util.getElementClass(type).getModifiers()) : Modifier.isFinal(type.getModifiers());
        }
        throw new IllegalArgumentException("type cannot be null.");
    }

    public boolean isClosure(Class type) {
        if (type != null) {
            return type.getName().indexOf(47) >= 0;
        }
        throw new IllegalArgumentException("type cannot be null.");
    }

    public Generics getGenerics() {
        return this.generics;
    }

    public void setOptimizedGenerics(boolean optimizedGenerics) {
        this.generics = optimizedGenerics ? new DefaultGenerics(this) : NoGenerics.INSTANCE;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\Kryo$DefaultSerializerEntry.smali */
    static final class DefaultSerializerEntry {
        final SerializerFactory serializerFactory;
        final Class type;

        DefaultSerializerEntry(Class type, SerializerFactory serializerFactory) {
            this.type = type;
            this.serializerFactory = serializerFactory;
        }
    }
}
