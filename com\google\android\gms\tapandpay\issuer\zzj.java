package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelReader;
import com.google.android.gms.tapandpay.issuer.ServerPushProvisionRequest;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\zzj.smali */
public final class zzj implements Parcelable.Creator {
    @Override // android.os.Parcelable.Creator
    public final /* bridge */ /* synthetic */ Object createFromParcel(Parcel parcel) {
        int validateObjectHeader = SafeParcelReader.validateObjectHeader(parcel);
        ServerPushProvisionRequest.ExtraOptions defaultOptions = ServerPushProvisionRequest.ExtraOptions.defaultOptions();
        PushProvisionSessionContext pushProvisionSessionContext = null;
        String str = null;
        UserAddress userAddress = null;
        while (parcel.dataPosition() < validateObjectHeader) {
            int readHeader = SafeParcelReader.readHeader(parcel);
            switch (SafeParcelReader.getFieldId(readHeader)) {
                case 1:
                    pushProvisionSessionContext = (PushProvisionSessionContext) SafeParcelReader.createParcelable(parcel, readHeader, PushProvisionSessionContext.CREATOR);
                    break;
                case 2:
                    str = SafeParcelReader.createString(parcel, readHeader);
                    break;
                case 3:
                    userAddress = (UserAddress) SafeParcelReader.createParcelable(parcel, readHeader, UserAddress.CREATOR);
                    break;
                case 4:
                    defaultOptions = (ServerPushProvisionRequest.ExtraOptions) SafeParcelReader.createParcelable(parcel, readHeader, ServerPushProvisionRequest.ExtraOptions.CREATOR);
                    break;
                default:
                    SafeParcelReader.skipUnknownField(parcel, readHeader);
                    break;
            }
        }
        SafeParcelReader.ensureAtEnd(parcel, validateObjectHeader);
        return new ServerPushProvisionRequest(pushProvisionSessionContext, str, userAddress, defaultOptions);
    }

    @Override // android.os.Parcelable.Creator
    public final /* synthetic */ Object[] newArray(int i) {
        return new ServerPushProvisionRequest[i];
    }
}
