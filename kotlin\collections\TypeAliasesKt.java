package kotlin.collections;

import kotlin.Metadata;

/* compiled from: TypeAliases.kt */
@Metadata(d1 = {"\u0000.\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000*,\b\u0007\u0010\u0000\u001a\u0004\b\u0000\u0010\u0001\"\b\u0012\u0004\u0012\u0002H\u00010\u00022\b\u0012\u0004\u0012\u0002H\u00010\u0002B\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005*>\b\u0007\u0010\u0006\u001a\u0004\b\u0000\u0010\u0007\u001a\u0004\b\u0001\u0010\b\"\u000e\u0012\u0004\u0012\u0002H\u0007\u0012\u0004\u0012\u0002H\b0\t2\u000e\u0012\u0004\u0012\u0002H\u0007\u0012\u0004\u0012\u0002H\b0\tB\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005*,\b\u0007\u0010\n\u001a\u0004\b\u0000\u0010\u0001\"\b\u0012\u0004\u0012\u0002H\u00010\u000b2\b\u0012\u0004\u0012\u0002H\u00010\u000bB\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005*>\b\u0007\u0010\f\u001a\u0004\b\u0000\u0010\u0007\u001a\u0004\b\u0001\u0010\b\"\u000e\u0012\u0004\u0012\u0002H\u0007\u0012\u0004\u0012\u0002H\b0\r2\u000e\u0012\u0004\u0012\u0002H\u0007\u0012\u0004\u0012\u0002H\b0\rB\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005*,\b\u0007\u0010\u000e\u001a\u0004\b\u0000\u0010\u0001\"\b\u0012\u0004\u0012\u0002H\u00010\u000f2\b\u0012\u0004\u0012\u0002H\u00010\u000fB\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005*\u001a\b\u0007\u0010\u0010\"\u00020\u00112\u00020\u0011B\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005¨\u0006\u0012"}, d2 = {"ArrayList", "E", "Ljava/util/ArrayList;", "Lkotlin/SinceKotlin;", "version", "1.1", "HashMap", "K", "V", "Ljava/util/HashMap;", "HashSet", "Ljava/util/HashSet;", "LinkedHashMap", "Ljava/util/LinkedHashMap;", "LinkedHashSet", "Ljava/util/LinkedHashSet;", "RandomAccess", "Ljava/util/RandomAccess;", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\collections\TypeAliasesKt.smali */
public final class TypeAliasesKt {
    public static /* synthetic */ void ArrayList$annotations() {
    }

    public static /* synthetic */ void HashMap$annotations() {
    }

    public static /* synthetic */ void HashSet$annotations() {
    }

    public static /* synthetic */ void LinkedHashMap$annotations() {
    }

    public static /* synthetic */ void LinkedHashSet$annotations() {
    }

    public static /* synthetic */ void RandomAccess$annotations() {
    }
}
