package o.dk;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.l;
import o.e.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dk\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final byte[] a;
    private static char[] b;
    private static int d;
    private static int e;

    static void a() {
        b = new char[]{50929, 50868, 50862, 50816, 50837, 50857, 50852, 50713, 50714, 50709, 50714, 50714, 50714, 50714, 50706, 50707, 50712, 50714, 50711, 50711, 50714, 50714, 50733, 50707, 50709, 50711, 50715, 50712, 50712, 50712, 50715, 50712, 50712, 50712};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 + 4
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r9 = 122 - r9
            byte[] r0 = o.dk.c.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            r8 = r7
            goto L33
        L17:
            r3 = r2
        L18:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            int r8 = r8 + 1
            if (r4 != r7) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r8]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L33:
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dk.c.g(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{85, 91, 121, -13};
        $$b = 233;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        a();
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000", new int[]{6, 28, Opcodes.IF_ICMPGT, 26}, true, objArr);
        a = b.b(((String) objArr[0]).intern());
        int i = e + Opcodes.DSUB;
        d = i % 128;
        switch (i % 2 == 0) {
            case false:
                return;
            default:
                int i2 = 48 / 0;
                return;
        }
    }

    public static byte[] e() {
        int i = e + 51;
        d = i % 128;
        int i2 = i % 2;
        byte[] bArr = (byte[]) a.clone();
        int i3 = d + 5;
        e = i3 % 128;
        int i4 = i3 % 2;
        return bArr;
    }

    private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        char c;
        char[] cArr2;
        int i;
        String str2 = str;
        byte[] bArr = str2;
        if (str2 != null) {
            bArr = str2.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        l lVar = new l();
        int i2 = 0;
        int i3 = iArr[0];
        int i4 = 1;
        int i5 = iArr[1];
        int i6 = iArr[2];
        int i7 = iArr[3];
        char[] cArr3 = b;
        if (cArr3 != null) {
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int i8 = $10 + 49;
            $11 = i8 % 128;
            int i9 = i8 % 2;
            int i10 = 0;
            while (i10 < length) {
                try {
                    Object[] objArr2 = new Object[i4];
                    objArr2[i2] = Integer.valueOf(cArr3[i10]);
                    Object obj = a.s.get(1951085128);
                    if (obj != null) {
                        cArr2 = cArr3;
                        i = length;
                    } else {
                        Class cls = (Class) a.c(11 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) View.combineMeasuredStates(i2, i2), ExpandableListView.getPackedPositionType(0L) + 43);
                        byte b2 = (byte) i2;
                        byte b3 = (byte) (b2 - 1);
                        cArr2 = cArr3;
                        i = length;
                        Object[] objArr3 = new Object[1];
                        g(b2, b3, (byte) (b3 + 3), objArr3);
                        obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                        a.s.put(1951085128, obj);
                    }
                    cArr4[i10] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                    i10++;
                    cArr3 = cArr2;
                    length = i;
                    i2 = 0;
                    i4 = 1;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            }
            cArr3 = cArr4;
        }
        char[] cArr5 = new char[i5];
        System.arraycopy(cArr3, i3, cArr5, 0, i5);
        if (bArr2 != null) {
            int i11 = $10 + 43;
            $11 = i11 % 128;
            switch (i11 % 2 == 0 ? 'V' : '8') {
                case Opcodes.SASTORE /* 86 */:
                    cArr = new char[i5];
                    lVar.d = 1;
                    c = 1;
                    break;
                default:
                    cArr = new char[i5];
                    lVar.d = 0;
                    c = 0;
                    break;
            }
            while (true) {
                switch (lVar.d < i5 ? (char) 7 : 'C') {
                    case 'C':
                        cArr5 = cArr;
                        break;
                    default:
                        int i12 = $11 + 109;
                        $10 = i12 % 128;
                        if (i12 % 2 != 0) {
                        }
                        switch (bArr2[lVar.d] != 1) {
                            case true:
                                int i13 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = a.s.get(804049217);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 10, (char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1), 207 - (ViewConfiguration.getScrollBarSize() >> 8));
                                        byte b4 = (byte) 0;
                                        byte b5 = (byte) (b4 - 1);
                                        Object[] objArr5 = new Object[1];
                                        g(b4, b5, (byte) (b5 + 1), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(804049217, obj2);
                                    }
                                    cArr[i13] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                int i14 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr5[lVar.d]), Integer.valueOf(c)};
                                    Object obj3 = a.s.get(2016040108);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) a.c(ImageFormat.getBitsPerPixel(0) + 12, (char) TextUtils.indexOf("", "", 0, 0), 448 - (Process.myTid() >> 22));
                                        byte b6 = (byte) 0;
                                        byte b7 = (byte) (b6 - 1);
                                        Object[] objArr7 = new Object[1];
                                        g(b6, b7, (byte) (b7 + 4), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        a.s.put(2016040108, obj3);
                                    }
                                    cArr[i14] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                        c = cArr[lVar.d];
                        try {
                            Object[] objArr8 = {lVar, lVar};
                            Object obj4 = a.s.get(-2112603350);
                            if (obj4 == null) {
                                Class cls4 = (Class) a.c(Color.rgb(0, 0, 0) + 16777227, (char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), View.MeasureSpec.getSize(0) + 259);
                                byte b8 = (byte) 0;
                                byte b9 = (byte) (b8 - 1);
                                Object[] objArr9 = new Object[1];
                                g(b8, b9, (byte) (b9 & 56), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                a.s.put(-2112603350, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                }
            }
        }
        if (i7 > 0) {
            char[] cArr6 = new char[i5];
            System.arraycopy(cArr5, 0, cArr6, 0, i5);
            int i15 = i5 - i7;
            System.arraycopy(cArr6, 0, cArr5, i15, i7);
            System.arraycopy(cArr6, i7, cArr5, 0, i15);
        }
        switch (z) {
            case false:
                break;
            default:
                char[] cArr7 = new char[i5];
                lVar.d = 0;
                while (lVar.d < i5) {
                    cArr7[lVar.d] = cArr5[(i5 - lVar.d) - 1];
                    lVar.d++;
                }
                cArr5 = cArr7;
                break;
        }
        switch (i6 > 0 ? 'B' : 'O') {
            case Opcodes.IASTORE /* 79 */:
                break;
            default:
                lVar.d = 0;
                while (true) {
                    switch (lVar.d < i5) {
                        case true:
                            cArr5[lVar.d] = (char) (cArr5[lVar.d] - iArr[2]);
                            lVar.d++;
                    }
                }
                break;
        }
        objArr[0] = new String(cArr5);
    }
}
