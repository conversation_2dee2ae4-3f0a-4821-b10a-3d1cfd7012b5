package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\b1.smali */
public class b1 extends f0 {
    public b1() {
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        int i = z ? 4 : 3;
        int length = this.b.length;
        for (int i2 = 0; i2 < length; i2++) {
            i += this.b[i2].toASN1Primitive().a(true);
        }
        return i;
    }

    public b1(i iVar) {
        super(iVar, false);
    }

    b1(boolean z, h[] hVarArr) {
        super(z, hVarArr);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 49, this.b);
    }
}
