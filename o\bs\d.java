package o.bs;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\d.smali */
public abstract class d {
    private static int d = 0;
    private static int e = 1;

    public static c e(Context context) {
        c b = new j().b(context);
        int i = e;
        int i2 = ((i | 67) << 1) - (i ^ 67);
        d = i2 % 128;
        int i3 = i2 % 2;
        return b;
    }

    public final int hashCode() {
        int i = e;
        int i2 = ((i | 43) << 1) - (i ^ 43);
        d = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return super.hashCode();
            default:
                super.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = d;
        int i2 = (i & 39) + (i | 39);
        e = i2 % 128;
        boolean z = i2 % 2 == 0;
        boolean equals = super.equals(obj);
        switch (z) {
            case false:
                break;
            default:
                int i3 = 16 / 0;
                break;
        }
        int i4 = e;
        int i5 = ((i4 | 29) << 1) - (i4 ^ 29);
        d = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 11 : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return equals;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    public final String toString() {
        int i = e + Opcodes.LMUL;
        d = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = e;
        int i4 = (i3 ^ 79) + ((i3 & 79) << 1);
        d = i4 % 128;
        switch (i4 % 2 != 0 ? '(' : (char) 29) {
            case '(':
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    protected final void finalize() throws Throwable {
        int i = d;
        int i2 = (i ^ Opcodes.DREM) + ((i & Opcodes.DREM) << 1);
        e = i2 % 128;
        int i3 = i2 % 2;
        super.finalize();
        int i4 = d;
        int i5 = ((i4 | 71) << 1) - (i4 ^ 71);
        e = i5 % 128;
        switch (i5 % 2 == 0 ? (char) 5 : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                return;
            default:
                int i6 = 97 / 0;
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
