package o.eo;

import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\a.smali */
public final class a {
    private final o.du.c a;
    private final String b;
    private final String c;
    private final String d;
    private final String e;
    private final String h;
    private final String i;
    private b j;
    private static int g = 0;
    private static int f = 1;

    public a(String str, String str2, String str3, o.du.c cVar, String str4, String str5, String str6, b bVar) {
        this.d = str;
        this.e = str2;
        this.b = str3;
        this.a = cVar;
        this.c = str4;
        this.i = str5;
        this.h = str6;
        this.j = bVar;
    }

    public final String e() {
        int i = g;
        int i2 = ((i | Opcodes.LMUL) << 1) - (i ^ Opcodes.LMUL);
        f = i2 % 128;
        switch (i2 % 2 == 0 ? 'H' : '6') {
            case 'H':
                throw null;
            default:
                return this.d;
        }
    }

    public final String c() {
        int i = f + 59;
        int i2 = i % 128;
        g = i2;
        switch (i % 2 != 0 ? '\t' : (char) 0) {
            case '\t':
                throw null;
            default:
                String str = this.e;
                int i3 = ((i2 | Opcodes.DDIV) << 1) - (i2 ^ Opcodes.DDIV);
                f = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 19 : ',') {
                    case ',':
                        return str;
                    default:
                        int i4 = 46 / 0;
                        return str;
                }
        }
    }

    public final String b() {
        int i = g;
        int i2 = i + 43;
        f = i2 % 128;
        int i3 = i2 % 2;
        String str = this.b;
        int i4 = i + 109;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final o.du.c d() {
        int i = g;
        int i2 = (i ^ 65) + ((i & 65) << 1);
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        o.du.c cVar = this.a;
        int i5 = i3 + Opcodes.DDIV;
        g = i5 % 128;
        int i6 = i5 % 2;
        return cVar;
    }

    public final String a() {
        int i = g;
        int i2 = (i ^ 61) + ((i & 61) << 1);
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                throw null;
            default:
                return this.c;
        }
    }

    public final String g() {
        int i = g + 23;
        int i2 = i % 128;
        f = i2;
        int i3 = i % 2;
        String str = this.i;
        int i4 = i2 + 55;
        g = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String i() {
        int i = g;
        int i2 = ((i | 19) << 1) - (i ^ 19);
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0) {
            case true:
                String str = this.h;
                int i3 = (i + 80) - 1;
                f = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        obj.hashCode();
                        throw null;
                    default:
                        return str;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final b f() {
        int i = f;
        int i2 = ((i | 57) << 1) - (i ^ 57);
        g = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 25 : '\r') {
            case '\r':
                return this.j;
            default:
                throw null;
        }
    }

    public final void c(b bVar) {
        int i = f + 45;
        int i2 = i % 128;
        g = i2;
        boolean z = i % 2 == 0;
        this.j = bVar;
        switch (z) {
            case true:
                break;
            default:
                int i3 = 12 / 0;
                break;
        }
        int i4 = (i2 & 95) + (i2 | 95);
        f = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                int i5 = 74 / 0;
                return;
            default:
                return;
        }
    }

    public final int hashCode() {
        int i = (g + Opcodes.ISHR) - 1;
        f = i % 128;
        int i2 = i % 2;
        int hash = Objects.hash(this.d, this.e, this.b, this.a, this.c, this.i, this.h, this.j);
        int i3 = (f + 46) - 1;
        g = i3 % 128;
        switch (i3 % 2 != 0 ? '\n' : (char) 21) {
            case '\n':
                int i4 = 92 / 0;
                return hash;
            default:
                return hash;
        }
    }

    public final boolean equals(Object obj) {
        int i = (g + 26) - 1;
        int i2 = i % 128;
        f = i2;
        switch (i % 2 == 0 ? 'H' : 'S') {
            case 'H':
                boolean z = obj instanceof a;
                throw null;
            default:
                switch (!(obj instanceof a) ? (char) 15 : 'I') {
                    case 15:
                        int i3 = ((i2 | 81) << 1) - (i2 ^ 81);
                        g = i3 % 128;
                        int i4 = i3 % 2;
                        int i5 = (i2 + 54) - 1;
                        g = i5 % 128;
                        int i6 = i5 % 2;
                        return false;
                    default:
                        a aVar = (a) obj;
                        if (Objects.equals(this.d, aVar.e()) && Objects.equals(this.e, aVar.c()) && Objects.equals(this.b, aVar.b())) {
                            int i7 = f + 1;
                            g = i7 % 128;
                            int i8 = i7 % 2;
                            if (Objects.equals(this.a, aVar.d())) {
                                switch (Objects.equals(this.c, aVar.a()) ? '1' : '\r') {
                                    case '1':
                                        if (Objects.equals(this.i, aVar.g()) && Objects.equals(this.h, aVar.i())) {
                                            int i9 = g;
                                            int i10 = (i9 ^ 95) + ((i9 & 95) << 1);
                                            f = i10 % 128;
                                            int i11 = i10 % 2;
                                            switch (Objects.equals(this.j, aVar.f()) ? 'A' : '\t') {
                                                case '\t':
                                                    break;
                                                default:
                                                    int i12 = g;
                                                    int i13 = i12 + Opcodes.LSHR;
                                                    f = i13 % 128;
                                                    int i14 = i13 % 2;
                                                    int i15 = ((i12 | 69) << 1) - (i12 ^ 69);
                                                    f = i15 % 128;
                                                    int i16 = i15 % 2;
                                                    return true;
                                            }
                                        }
                                        break;
                                    default:
                                        return false;
                                }
                            }
                        }
                        return false;
                }
        }
    }
}
