package fr.antelop.sdk;

import android.content.Context;
import java.util.Date;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\DefaultWalletEventListener.smali */
public class DefaultWalletEventListener implements WalletEventListener {
    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletLoaded(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletLocked(Context context, WalletLockReason walletLockReason) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletUnlocked(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletLogout(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletDeleted(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletSettingUpdated(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletProductsUpdated(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onWalletCountersUpdated(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onCustomerCredentialsReset(Context context) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onAppSunsetScheduled(Context context, Date date) {
    }

    @Override // fr.antelop.sdk.WalletEventListener
    public void onDeviceEligibilityLost(Context context) {
    }
}
