package o.bo;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\j.smali */
public final class j extends o.bs.d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static boolean b;
    private static b c;
    private static char[] d;
    private static final Object e;
    private static int g;
    private static int i;
    private static boolean j;

    static void e() {
        d = new char[]{61773, 61808, 61810, 61797, 61768, 61792, 61788, 61798, 61796, 61807, 61778, 61811, 61815, 61794, 61806, 61793, 61767, 61809, 61812, 61725, 61736, 61805, 61801, 61799, 61795, 61800};
        b = true;
        j = true;
        a = 782103037;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(byte r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r7 = 121 - r7
            byte[] r0 = o.bo.j.$$a
            int r9 = r9 + 4
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L15:
            r3 = r2
        L16:
            int r9 = r9 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r9]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r9 = -r9
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.j.h(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{29, -23, 98, 29};
        $$b = 221;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        e();
        ViewConfiguration.getMinimumFlingVelocity();
        e = new Object();
        int i2 = g + 63;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    public final b c(Context context) {
        synchronized (e) {
            b bVar = c;
            if (bVar != null) {
                return bVar;
            }
            d(context);
            return c;
        }
    }

    public static void d(Context context) {
        String intern;
        Object obj;
        b cVar;
        int i2 = i + 23;
        g = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        f(null, 127 - (Process.myTid() >> 22), null, "\u0093\u008c\u008f\u0092\u008e\u0087\u0091\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern2 = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f(null, 127 - TextUtils.getTrimmedLength(""), null, "\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081\u0084\u0083\u0086\u008c\u0098\u0086\u008c", objArr2);
        o.ee.g.d(intern2, ((String) objArr2[0]).intern());
        o.bs.c e2 = e(context);
        switch (e2 == null ? 'P' : 'J') {
            case 'P':
                int i4 = i + 29;
                g = i4 % 128;
                if (i4 % 2 != 0) {
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    f(null, 57 / (ViewConfiguration.getScrollBarFadeDuration() << Opcodes.INEG), null, "\u0093\u008c\u008f\u0092\u008e\u0087\u0091\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081", objArr3);
                    intern = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    f(null, 37 << (PointF.length(1.0f, 2.0f) > 2.0f ? 1 : (PointF.length(1.0f, 2.0f) == 2.0f ? 0 : -1)), null, "\u0086\u0097\u0099\u0087\u0097\u0089\u0087\u008d\u0087\u0094\u0086\u008e\u0089\u008d\u008c\u0086\u0083\u0094\u0083\u008f\u0094\u008f\u008a\u0094\u0095\u0094\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081\u0084\u0083\u0086\u008c\u0098\u0086\u008c", objArr4);
                    obj = objArr4[0];
                } else {
                    o.ee.g.c();
                    Object[] objArr5 = new Object[1];
                    f(null, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 127, null, "\u0093\u008c\u008f\u0092\u008e\u0087\u0091\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081", objArr5);
                    intern = ((String) objArr5[0]).intern();
                    Object[] objArr6 = new Object[1];
                    f(null, (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 127, null, "\u0086\u0097\u0099\u0087\u0097\u0089\u0087\u008d\u0087\u0094\u0086\u008e\u0089\u008d\u008c\u0086\u0083\u0094\u0083\u008f\u0094\u008f\u008a\u0094\u0095\u0094\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081\u0084\u0083\u0086\u008c\u0098\u0086\u008c", objArr6);
                    obj = objArr6[0];
                }
                o.ee.g.e(intern, ((String) obj).intern());
                return;
            default:
                o.bi.e d2 = o.bi.e.d(context);
                o.bi.a d3 = d2.d();
                o.bi.d c2 = d2.c();
                switch (e2 instanceof o.bs.b ? 'A' : 'C') {
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        int i5 = g + 47;
                        i = i5 % 128;
                        switch (i5 % 2 != 0) {
                            case false:
                                throw null;
                            default:
                                if (d3 != null) {
                                    cVar = new e(context, (o.bs.b) e2, d3);
                                    c = cVar;
                                    return;
                                }
                        }
                    default:
                        switch (e2 instanceof o.bs.a ? '-' : 'W') {
                            default:
                                int i6 = i + 33;
                                g = i6 % 128;
                                int i7 = i6 % 2;
                                if (c2 != null) {
                                    cVar = new c(context, (o.bs.a) e2, c2);
                                    c = cVar;
                                    return;
                                }
                            case Opcodes.POP /* 87 */:
                                o.ee.g.c();
                                Object[] objArr7 = new Object[1];
                                f(null, ImageFormat.getBitsPerPixel(0) + 128, null, "\u0093\u008c\u008f\u0092\u008e\u0087\u0091\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081", objArr7);
                                String intern3 = ((String) objArr7[0]).intern();
                                Object[] objArr8 = new Object[1];
                                f(null, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.IAND, null, "\u0090\u0086\u008a\u0089\u0098\u0086\u0090\u0094\u008a\u008f\u0089\u0092\u0087\u008c\u0082\u0088\u0089\u0098\u008a\u008f\u008e\u0094\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u009a\u0094\u0084\u0083\u0082\u0096\u0094\u008f\u008a\u0094\u0095\u0094\u008c\u0086\u0090\u0089\u008d\u008f\u008c\u0081\u0086\u008e\u0089\u008d\u008c\u0086\u008b\u0088\u008a\u0089\u0088\u0087\u0083\u0083\u0086\u0085\u0084\u0083\u0082\u0081\u0084\u0083\u0086\u008c\u0098\u0086\u008c", objArr8);
                                o.ee.g.d(intern3, ((String) objArr8[0]).intern());
                                return;
                        }
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r20, int r21, int[] r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 820
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.j.f(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
