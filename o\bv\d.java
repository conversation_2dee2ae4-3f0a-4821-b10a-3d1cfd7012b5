package o.bv;

import android.media.AudioTrack;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Arrays;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\d.smali */
public abstract class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final char[] c;
    private static boolean f;
    private static int g;
    private static boolean i;
    private static char[] j;
    private static int l;
    private static int n;
    private final int a;
    private byte[] b;
    private byte[] d;
    public boolean e;
    private int h;

    static void e() {
        j = new char[]{61585, 61578, 61597, 61781, 61592, 61582, 61576, 61598, 61593, 61586, 61599, 61602, 61816, 61817, 61589, 61583, 61588, 61590, 61603, 61810, 61807, 61800};
        i = true;
        f = true;
        g = 782102827;
    }

    static void init$0() {
        $$a = new byte[]{0, ByteCompanionObject.MIN_VALUE, 58, -118, -96};
        $$b = 220;
    }

    private static void m(short s, short s2, byte b, Object[] objArr) {
        byte[] bArr = $$a;
        int i2 = 5 - (b * 2);
        int i3 = s2 + Opcodes.LNEG;
        int i4 = (s * 2) + 1;
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            int i7 = i6 + i2;
            i2++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = -1;
            i3 = i7;
            i6 = i6;
        }
        while (true) {
            int i8 = i5 + 1;
            bArr2[i8] = (byte) i3;
            if (i8 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i9 = i3;
            int i10 = i6;
            i2++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i5 = i8;
            i3 = i9 + bArr[i2];
            i6 = i10;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        n = 0;
        l = 1;
        e();
        ViewConfiguration.getTouchSlop();
        c = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        int i2 = n + Opcodes.LSHR;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    public d(byte[] bArr) {
        if (bArr == null) {
            this.d = new byte[0];
            this.b = new byte[0];
            this.h = 0;
            this.a = 0;
            this.e = false;
            return;
        }
        this.d = new byte[bArr.length];
        this.b = new byte[bArr.length];
        this.a = bArr.length;
        this.e = a(bArr);
    }

    private boolean a(byte[] bArr) {
        switch (bArr != null) {
            case true:
                switch (bArr.length > this.a ? (char) 25 : (char) 11) {
                    case 11:
                        int i2 = 0;
                        while (i2 < bArr.length) {
                            int i3 = n + 13;
                            l = i3 % 128;
                            int i4 = i3 % 2;
                            try {
                                Object[] objArr = new Object[1];
                                k(null, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 127, null, "\u0092\u0091\u0090\u008f\u0082\u008e\u0086\u0089\u0088\u0087\u0086\u008d\u0084\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr);
                                Object newInstance = Class.forName(((String) objArr[0]).intern()).getDeclaredConstructor(null).newInstance(null);
                                try {
                                    Object[] objArr2 = new Object[1];
                                    k(null, 126 - ((byte) KeyEvent.getModifierMetaStateMask()), null, "\u0092\u0091\u0090\u008f\u0082\u008e\u0086\u0089\u0088\u0087\u0086\u008d\u0084\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0082\u0083\u0082\u0081", objArr2);
                                    Class<?> cls = Class.forName(((String) objArr2[0]).intern());
                                    Object[] objArr3 = new Object[1];
                                    k(null, 127 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), null, "\u008b\u008f\u0094\u008b\u0093\u0086\u008f", objArr3);
                                    byte intValue = (byte) ((Integer) cls.getMethod(((String) objArr3[0]).intern(), null).invoke(newInstance, null)).intValue();
                                    this.d[i2] = (byte) (bArr[i2] ^ intValue);
                                    this.b[i2] = intValue;
                                    i2++;
                                    int i5 = l + Opcodes.LSHR;
                                    n = i5 % 128;
                                    int i6 = i5 % 2;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause != null) {
                                        throw cause;
                                    }
                                    throw th;
                                }
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 != null) {
                                    throw cause2;
                                }
                                throw th2;
                            }
                        }
                        int length = bArr.length;
                        while (true) {
                            switch (length >= this.h) {
                                case true:
                                    this.h = bArr.length;
                                    return d(bArr);
                                default:
                                    this.d[length] = 0;
                                    this.b[length] = 0;
                                    length++;
                            }
                        }
                    default:
                        int i7 = n + 23;
                        l = i7 % 128;
                        int i8 = i7 % 2;
                        return false;
                }
            default:
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static boolean d(byte[] r7) {
        /*
            int r0 = r7.length
            r1 = 0
            r2 = r1
        L4:
            r3 = 1
            if (r2 >= r0) goto L9
            r4 = r3
            goto La
        L9:
            r4 = r1
        La:
            switch(r4) {
                case 0: goto L1b;
                default: goto Ld;
            }
        Ld:
            int r4 = o.bv.d.l
            r5 = 91
            int r4 = r4 + r5
            int r6 = r4 % 128
            o.bv.d.n = r6
            int r4 = r4 % 2
            if (r4 == 0) goto L1f
            goto L1c
        L1b:
            return r3
        L1c:
            r5 = 67
            goto L20
        L1f:
        L20:
            switch(r5) {
                case 91: goto L2b;
                default: goto L23;
            }
        L23:
            r3 = r7[r2]
            char r3 = (char) r3
            boolean r3 = c(r3)
            goto L39
        L2b:
            r4 = r7[r2]
            char r4 = (char) r4
            boolean r4 = c(r4)
            if (r4 != 0) goto L35
            r3 = r1
        L35:
            switch(r3) {
                case 0: goto L49;
                default: goto L38;
            }
        L38:
            goto L46
        L39:
            r4 = 6
            int r4 = r4 / r1
            if (r3 != 0) goto L40
            r3 = 78
            goto L42
        L40:
            r3 = 12
        L42:
            switch(r3) {
                case 12: goto L38;
                default: goto L45;
            }
        L45:
            goto L49
        L46:
            int r2 = r2 + 1
            goto L4
        L49:
            int r7 = o.bv.d.n
            int r7 = r7 + 109
            int r0 = r7 % 128
            o.bv.d.l = r0
            int r7 = r7 % 2
            return r1
        L54:
            r7 = move-exception
            throw r7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.d.d(byte[]):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static boolean c(char r7) {
        /*
            int r0 = o.bv.d.n
            int r0 = r0 + 113
            int r1 = r0 % 128
            o.bv.d.l = r1
            int r0 = r0 % 2
            char[] r0 = o.bv.d.c
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L10:
            if (r3 >= r1) goto L15
            r4 = 97
            goto L17
        L15:
            r4 = 99
        L17:
            switch(r4) {
                case 99: goto L20;
                default: goto L1a;
            }
        L1a:
            char r4 = r0[r3]
            r5 = 1
            if (r4 != r7) goto L23
            goto L21
        L20:
            return r2
        L21:
            r4 = r2
            goto L24
        L23:
            r4 = r5
        L24:
            switch(r4) {
                case 1: goto L34;
                default: goto L27;
            }
        L27:
            int r7 = o.bv.d.l
            int r7 = r7 + 3
            int r0 = r7 % 128
            o.bv.d.n = r0
            int r7 = r7 % 2
            if (r7 == 0) goto L4a
            goto L49
        L34:
            int r3 = r3 + 1
            int r4 = o.bv.d.l
            int r4 = r4 + 113
            int r6 = r4 % 128
            o.bv.d.n = r6
            int r4 = r4 % 2
            if (r4 == 0) goto L44
            r5 = r2
            goto L45
        L44:
        L45:
            switch(r5) {
                case 1: goto L48;
                default: goto L48;
            }
        L48:
            goto L10
        L49:
            return r2
        L4a:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.d.c(char):boolean");
    }

    public final int d() {
        int i2 = n;
        int i3 = i2 + 67;
        l = i3 % 128;
        int i4 = i3 % 2;
        int i5 = this.h;
        int i6 = i2 + 29;
        l = i6 % 128;
        switch (i6 % 2 != 0) {
            case false:
                int i7 = 63 / 0;
                return i5;
            default:
                return i5;
        }
    }

    public final a a() {
        byte[] bArr = this.d;
        byte[] bArr2 = this.b;
        this.d = Arrays.copyOf(bArr, this.h);
        this.b = Arrays.copyOf(this.b, this.h);
        Arrays.fill(bArr, (byte) 0);
        Arrays.fill(bArr2, (byte) 0);
        a aVar = new a(this.d, this.b);
        int i2 = n + Opcodes.DMUL;
        l = i2 % 128;
        int i3 = i2 % 2;
        return aVar;
    }

    public final int hashCode() {
        int i2 = l + 65;
        n = i2 % 128;
        int i3 = i2 % 2;
        int hashCode = super.hashCode();
        int i4 = l + 35;
        n = i4 % 128;
        int i5 = i4 % 2;
        return hashCode;
    }

    public final boolean equals(Object obj) {
        int i2 = n + 43;
        l = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = l + 1;
        n = i4 % 128;
        switch (i4 % 2 != 0 ? ']' : (char) 31) {
            case Opcodes.DUP2_X1 /* 93 */:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i2 = l + Opcodes.DNEG;
        n = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return super.toString();
            default:
                int i3 = 35 / 0;
                return super.toString();
        }
    }

    protected final void finalize() throws Throwable {
        int i2 = l + 99;
        n = i2 % 128;
        char c2 = i2 % 2 != 0 ? (char) 1 : (char) 21;
        super.finalize();
        switch (c2) {
            case 1:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int i3 = n + 55;
                l = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 30 : '\t') {
                    case 30:
                        int i4 = 7 / 0;
                        return;
                    default:
                        return;
                }
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r16, int r17, int[] r18, java.lang.String r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 748
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.d.k(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
