package com.google.android.gms.tapandpay.issuer;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\ViewTokenRequest.smali */
public class ViewTokenRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<ViewTokenRequest> CREATOR = new zzn();
    final String zza;
    final int zzb;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\ViewTokenRequest$Builder.smali */
    public static class Builder {
        private String zza;
        private int zzb;

        public ViewTokenRequest build() {
            return new ViewTokenRequest(this.zza, this.zzb);
        }

        public Builder setIssuerTokenId(String str) {
            this.zza = str;
            return this;
        }

        public Builder setTokenServiceProvider(int i) {
            this.zzb = i;
            return this;
        }
    }

    ViewTokenRequest(String str, int i) {
        this.zza = str;
        this.zzb = i;
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, this.zza, false);
        SafeParcelWriter.writeInt(dest, 2, this.zzb);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }
}
