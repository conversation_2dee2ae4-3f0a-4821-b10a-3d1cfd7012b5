package com.google.android.datatransport.runtime.dagger.internal;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.inject.Provider;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\datatransport\runtime\dagger\internal\AbstractMapFactory$Builder.smali */
public abstract class AbstractMapFactory$Builder<K, V, V2> {
    final LinkedHashMap<K, Provider<V>> map;

    AbstractMapFactory$Builder(int size) {
        this.map = DaggerCollections.newLinkedHashMapWithExpectedSize(size);
    }

    /* JADX WARN: Multi-variable type inference failed */
    AbstractMapFactory$Builder<K, V, V2> put(K k, Provider<V> provider) {
        this.map.put(Preconditions.checkNotNull(k, "key"), Preconditions.checkNotNull(provider, "provider"));
        return this;
    }

    AbstractMapFactory$Builder<K, V, V2> putAll(Provider<Map<K, V2>> mapOfProviders) {
        if (mapOfProviders instanceof DelegateFactory) {
            DelegateFactory<Map<K, V2>> asDelegateFactory = (DelegateFactory) mapOfProviders;
            return putAll(asDelegateFactory.getDelegate());
        }
        AbstractMapFactory<K, V, ?> asAbstractMapFactory = (AbstractMapFactory) mapOfProviders;
        this.map.putAll(AbstractMapFactory.access$000(asAbstractMapFactory));
        return this;
    }
}
