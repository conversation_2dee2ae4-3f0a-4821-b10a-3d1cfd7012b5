package com.avaldigitallabs.mbocc.wallet;

import android.content.Context;
import android.util.Log;
import com.avaldigitallabs.mbocc.wallet.types.EnrollStatus;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.WalletNotificationServiceCallback;
import fr.antelop.sdk.card.EmvApplicationActivationMethod;
import java.util.Date;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\avaldigitallabs\mbocc\wallet\DigitalWalletNotificationServiceCallback.smali */
public class DigitalWalletNotificationServiceCallback implements WalletNotificationServiceCallback {
    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onCardsUpdated(Context context) {
        Log.i("BoccWalletNotification", "OnCardsUpdated");
        DigitalEnrollCallback.emitStatus(EnrollStatus.CARDS_UPDATED);
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onEmvApplicationActivationRequired(Context context, String s, List<EmvApplicationActivationMethod> list) {
        Log.i("BoccWalletNotification", "onEmvApplicationActivationRequired");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onEmvApplicationCredentialsUpdated(Context context) {
        Log.i("BoccWalletNotification", "onEmvApplicationCredentialsUpdated");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onWalletLoaded(Context context) {
        Log.i("BoccWalletNotification", "onWalletLoaded");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onWalletLocked(Context context, WalletLockReason walletLockReason) {
        Log.i("BoccWalletNotification", "onWalletLocked");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onWalletUnlocked(Context context) {
        Log.i("BoccWalletNotification", "onWalletUnlocked");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onLogout(Context context) {
        Log.i("BoccWalletNotification", "onLogout");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onWalletDeleted(Context context) {
        Log.i("BoccWalletNotification", "onWalletDeleted");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onSettingsUpdated(Context context) {
        Log.i("BoccWalletNotification", "onSettingsUpdated");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onCountersUpdated(Context context) {
        Log.i("BoccWalletNotification", "onCountersUpdated");
        DigitalEnrollCallback.emitStatus(EnrollStatus.COUNTERS_UPDATED);
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onCustomerCredentialsReset(Context context) {
        Log.i("BoccWalletNotification", "onCustomerCredentialsReset");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onSunsetScheduled(Context context, Date date) {
        Log.i("BoccWalletNotification", "onSunsetScheduled");
    }

    @Override // fr.antelop.sdk.WalletNotificationServiceCallback
    public void onLostEligibility(Context context) {
        Log.i("BoccWalletNotification", "onLostEligibility");
    }
}
