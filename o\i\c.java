package o.i;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodStatus;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\c.smali */
public final class c implements o.ee.a<CustomerAuthenticationMethodStatus> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c b;
    public static final c c;
    public static final c d;
    private static final /* synthetic */ c[] e;
    private static long f;
    private static char[] g;
    private static int h;
    private static int j;

    static void b() {
        g = new char[]{11420, 5976, 23378, 40780, 49989, 1879, 19280, 36676, 62292, 14170, 31519, 49002, 58204, 10070, 27470, 44893, 37635, 55046, 11399, 5977, 23363, 40807, 49984, 1858, 19267, 36703, 62275, 14154, 31578, 48984, 11399, 5977, 23363, 40823, 50010, 1884, 19285, 36697, 62294, 14155, 31565, 48985, 58201, 11402, 5977, 23385, 40786, 50012, 1877, 19270, 36674, 62292, 14170, 3497, 13940, 31330, 48764, 57954, 9842, 27238, 44660, 53876};
        f = 5487093386164967222L;
    }

    static void init$0() {
        $$a = new byte[]{70, -127, -25, -85};
        $$b = 71;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 + 4
            byte[] r0 = o.i.c.$$a
            int r7 = 105 - r7
            int r6 = r6 * 4
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L17:
            r3 = r2
        L18:
            int r8 = r8 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r8]
            int r3 = r3 + 1
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r8 = -r8
            int r7 = r7 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.c.k(short, byte, byte, java.lang.Object[]):void");
    }

    private c(String str, int i) {
    }

    private static /* synthetic */ c[] c() {
        c[] cVarArr;
        int i = h + Opcodes.DSUB;
        int i2 = i % 128;
        j = i2;
        switch (i % 2 == 0) {
            case true:
                cVarArr = new c[]{d, b, a, c};
                break;
            default:
                c[] cVarArr2 = new c[3];
                cVarArr2[0] = d;
                cVarArr2[1] = b;
                cVarArr2[3] = a;
                cVarArr2[2] = c;
                cVarArr = cVarArr2;
                break;
        }
        int i3 = i2 + 9;
        h = i3 % 128;
        int i4 = i3 % 2;
        return cVarArr;
    }

    public static c valueOf(String str) {
        int i = j + 69;
        h = i % 128;
        int i2 = i % 2;
        c cVar = (c) Enum.valueOf(c.class, str);
        int i3 = h + Opcodes.DMUL;
        j = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                throw null;
            default:
                return cVar;
        }
    }

    public static c[] values() {
        int i = j + 23;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                c[] cVarArr = (c[]) e.clone();
                int i2 = h + Opcodes.LMUL;
                j = i2 % 128;
                int i3 = i2 % 2;
                return cVarArr;
        }
    }

    @Override // o.ee.d
    public final /* synthetic */ Object a() {
        int i = j + 81;
        h = i % 128;
        switch (i % 2 == 0 ? (char) 28 : ':') {
            case 28:
                e();
                throw null;
            default:
                return e();
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        b();
        Object[] objArr = new Object[1];
        i((char) (ViewConfiguration.getKeyRepeatDelay() >> 16), TextUtils.indexOf("", "", 0, 0) + 18, 12 - TextUtils.indexOf("", "", 0, 0), objArr);
        d = new c(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        i((char) View.combineMeasuredStates(0, 0), 30 - Color.argb(0, 0, 0, 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 13, objArr2);
        b = new c(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        i((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 43 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (ViewConfiguration.getTouchSlop() >> 8) + 10, objArr3);
        a = new c(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        i((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 8480), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 52, 9 - Drawable.resolveOpacity(0, 0), objArr4);
        c = new c(((String) objArr4[0]).intern(), 3);
        e = c();
        int i = h + 93;
        j = i % 128;
        int i2 = i % 2;
    }

    /* renamed from: o.i.c$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\i\c$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int e;

        static {
            e = 0;
            b = 1;
            int[] iArr = new int[c.values().length];
            a = iArr;
            try {
                iArr[c.d.ordinal()] = 1;
                int i = b + 63;
                e = i % 128;
                if (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[c.b.ordinal()] = 2;
                int i2 = b;
                int i3 = (i2 ^ 15) + ((i2 & 15) << 1);
                e = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[c.a.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[c.c.ordinal()] = 4;
                int i5 = e;
                int i6 = (i5 ^ 37) + ((i5 & 37) << 1);
                b = i6 % 128;
                switch (i6 % 2 != 0) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public final CustomerAuthenticationMethodStatus e() {
        switch (AnonymousClass5.a[ordinal()]) {
            case 1:
                return CustomerAuthenticationMethodStatus.NotSupported;
            case 2:
                CustomerAuthenticationMethodStatus customerAuthenticationMethodStatus = CustomerAuthenticationMethodStatus.NotConfigured;
                int i = j + 69;
                h = i % 128;
                int i2 = i % 2;
                return customerAuthenticationMethodStatus;
            case 3:
                CustomerAuthenticationMethodStatus customerAuthenticationMethodStatus2 = CustomerAuthenticationMethodStatus.Configured;
                int i3 = h + 53;
                j = i3 % 128;
                int i4 = i3 % 2;
                return customerAuthenticationMethodStatus2;
            case 4:
                return CustomerAuthenticationMethodStatus.Activated;
            default:
                StringBuilder sb = new StringBuilder();
                Object[] objArr = new Object[1];
                i((char) Color.blue(0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1, 18 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
                throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 606
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.i.c.i(char, int, int, java.lang.Object[]):void");
    }
}
