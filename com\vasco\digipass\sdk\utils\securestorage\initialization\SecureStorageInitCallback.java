package com.vasco.digipass.sdk.utils.securestorage.initialization;

import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorage;
import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import kotlin.Metadata;

@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH&J\u0010\u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\fH&R\u0014\u0010\u0002\u001a\u0004\u0018\u00010\u0003X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0004\u0010\u0005¨\u0006\r"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/initialization/SecureStorageInitCallback;", "", "fragmentActivity", "Landroidx/fragment/app/FragmentActivity;", "getFragmentActivity", "()Landroidx/fragment/app/FragmentActivity;", "onInitFailed", "", "secureStorageException", "Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorageSDKException;", "onInitSuccess", "secureStorage", "Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorage;", "lib_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\initialization\SecureStorageInitCallback.smali */
public interface SecureStorageInitCallback {
    FragmentActivity getFragmentActivity();

    void onInitFailed(SecureStorageSDKException secureStorageException);

    void onInitSuccess(SecureStorage secureStorage);
}
