package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import androidx.core.view.InputDeviceCompat;
import bc.org.bouncycastle.util.Arrays;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d.smali */
public abstract class d extends b0 implements e {
    final byte[] b;
    static final o0 x = new a(d.class, 3);
    private static final char[] C = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return d.b(f2Var.h());
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(e0 e0Var) {
            return e0Var.k();
        }
    }

    d(byte[] bArr, int i) {
        if (bArr == null) {
            throw new NullPointerException("'data' cannot be null");
        }
        if (bArr.length == 0 && i != 0) {
            throw new IllegalArgumentException("zero length data with non-zero pad bits");
        }
        if (i > 7 || i < 0) {
            throw new IllegalArgumentException("pad bits cannot be greater than 7 or less than 0");
        }
        this.b = Arrays.prepend(bArr, (byte) i);
    }

    public static d a(Object obj) {
        if (obj == null || (obj instanceof d)) {
            return (d) obj;
        }
        if (obj instanceof h) {
            b0 aSN1Primitive = ((h) obj).toASN1Primitive();
            if (aSN1Primitive instanceof d) {
                return (d) aSN1Primitive;
            }
        } else if (obj instanceof byte[]) {
            try {
                return (d) x.a((byte[]) obj);
            } catch (IOException e) {
                throw new IllegalArgumentException("failed to construct BIT STRING from byte[]: " + e.getMessage());
            }
        }
        throw new IllegalArgumentException("illegal object in getInstance: " + obj.getClass().getName());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public InputStream b() throws IOException {
        byte[] bArr = this.b;
        return new ByteArrayInputStream(bArr, 1, bArr.length - 1);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e
    public int d() {
        return this.b[0] & 255;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return new w1(this.b, false);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new y2(this.b, false);
    }

    public byte[] h() {
        byte[] bArr = this.b;
        if (bArr.length == 1) {
            return x.C;
        }
        int i = bArr[0] & 255;
        byte[] copyOfRange = Arrays.copyOfRange(bArr, 1, bArr.length);
        int length = copyOfRange.length - 1;
        copyOfRange[length] = (byte) (((byte) (255 << i)) & copyOfRange[length]);
        return copyOfRange;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        byte[] bArr = this.b;
        if (bArr.length < 2) {
            return 1;
        }
        int i = bArr[0] & 255;
        int length = bArr.length - 1;
        return (Arrays.hashCode(bArr, 0, length) * InputDeviceCompat.SOURCE_KEYBOARD) ^ ((byte) ((255 << i) & bArr[length]));
    }

    public byte[] i() {
        byte[] bArr = this.b;
        if (bArr[0] == 0) {
            return Arrays.copyOfRange(bArr, 1, bArr.length);
        }
        throw new IllegalStateException("attempt to get non-octet aligned data from BIT STRING");
    }

    public String j() {
        try {
            byte[] encoded = getEncoded();
            StringBuffer stringBuffer = new StringBuffer((encoded.length * 2) + 1);
            stringBuffer.append('#');
            for (int i = 0; i != encoded.length; i++) {
                byte b = encoded[i];
                char[] cArr = C;
                stringBuffer.append(cArr[(b >>> 4) & 15]);
                stringBuffer.append(cArr[b & 15]);
            }
            return stringBuffer.toString();
        } catch (IOException e) {
            throw new a0("Internal error encoding BitString: " + e.getMessage(), e);
        }
    }

    public String toString() {
        return j();
    }

    static d b(byte[] bArr) {
        int length = bArr.length;
        if (length < 1) {
            throw new IllegalArgumentException("truncated BIT STRING detected");
        }
        int i = bArr[0] & 255;
        if (i > 0) {
            if (i > 7 || length < 2) {
                throw new IllegalArgumentException("invalid pad bits detected");
            }
            byte b = bArr[length - 1];
            if (b != ((byte) ((255 << i) & b))) {
                return new y2(bArr, false);
            }
        }
        return new w1(bArr, false);
    }

    d(byte[] bArr, boolean z) {
        if (z) {
            if (bArr != null) {
                if (bArr.length >= 1) {
                    int i = bArr[0] & 255;
                    if (i > 0) {
                        if (bArr.length < 2) {
                            throw new IllegalArgumentException("zero length data with non-zero pad bits");
                        }
                        if (i > 7) {
                            throw new IllegalArgumentException("pad bits cannot be greater than 7 or less than 0");
                        }
                    }
                } else {
                    throw new IllegalArgumentException("'contents' cannot be empty");
                }
            } else {
                throw new NullPointerException("'contents' cannot be null");
            }
        }
        this.b = bArr;
    }

    public static d a(j0 j0Var, boolean z) {
        return (d) x.a(j0Var, z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (!(b0Var instanceof d)) {
            return false;
        }
        byte[] bArr = this.b;
        byte[] bArr2 = ((d) b0Var).b;
        int length = bArr.length;
        if (bArr2.length != length) {
            return false;
        }
        if (length == 1) {
            return true;
        }
        int i = length - 1;
        for (int i2 = 0; i2 < i; i2++) {
            if (bArr[i2] != bArr2[i2]) {
                return false;
            }
        }
        int i3 = 255 << (bArr[0] & 255);
        return ((byte) (bArr[i] & i3)) == ((byte) (bArr2[i] & i3));
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() {
        return toASN1Primitive();
    }
}
