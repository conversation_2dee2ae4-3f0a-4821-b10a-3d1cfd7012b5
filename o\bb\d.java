package o.bb;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bb\d.smali */
public final class d {
    private a a;
    private final e c;
    private String e;
    private static int j = 0;
    private static int i = 1;
    private boolean d = true;
    private final c b = new c();

    public d(e eVar) {
        this.c = eVar;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:5:0x0016. Please report as an issue. */
    public final void c(a aVar) {
        int i2 = i;
        int i3 = ((i2 | 53) << 1) - (i2 ^ 53);
        j = i3 % 128;
        Object obj = null;
        switch (i3 % 2 == 0) {
        }
        this.d = false;
        this.a = aVar;
        this.e = null;
        int i4 = ((i2 | 3) << 1) - (i2 ^ 3);
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void c(a aVar, String str) {
        int i2 = j;
        int i3 = ((i2 | 69) << 1) - (i2 ^ 69);
        int i4 = i3 % 128;
        i = i4;
        switch (i3 % 2 == 0 ? 'S' : 'Q') {
            case Opcodes.AASTORE /* 83 */:
                this.d = true;
                break;
            default:
                this.d = false;
                break;
        }
        this.a = aVar;
        this.e = str;
        int i5 = (i4 & 71) + (i4 | 71);
        j = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:37:0x00cc. Please report as an issue. */
    public final void d(Context context, o.ei.c cVar, o.fk.d dVar) {
        int i2 = i + 59;
        j = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                switch (!dVar.d() ? 'a' : (char) 4) {
                    case Opcodes.LADD /* 97 */:
                        this.d = false;
                        this.a = a.q;
                        break;
                }
                this.b.b(dVar.c());
                switch (dVar.b() ? '\n' : '7') {
                    case '\n':
                        int i3 = j;
                        int i4 = (i3 & 59) + (i3 | 59);
                        i = i4 % 128;
                        int i5 = i4 % 2;
                        this.b.e(context, cVar, f.y);
                        int i6 = j;
                        int i7 = ((i6 | 61) << 1) - (i6 ^ 61);
                        i = i7 % 128;
                        int i8 = i7 % 2;
                        return;
                    default:
                        if (dVar.i().g()) {
                            int i9 = j;
                            int i10 = ((i9 | 1) << 1) - (i9 ^ 1);
                            i = i10 % 128;
                            int i11 = i10 % 2;
                            this.b.e(context, cVar, f.c);
                            return;
                        }
                        switch (!dVar.i().c() ? (char) 3 : 'c') {
                            case 3:
                                int i12 = i;
                                int i13 = ((i12 | 49) << 1) - (i12 ^ 49);
                                j = i13 % 128;
                                int i14 = i13 % 2;
                                switch (dVar.a()) {
                                    case false:
                                        return;
                                }
                        }
                        this.b.e(context, cVar, f.b);
                        int i15 = j + 33;
                        i = i15 % 128;
                        switch (i15 % 2 == 0 ? '2' : '3') {
                        }
                        return;
                }
            default:
                dVar.d();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final e c() {
        e eVar;
        int i2 = j;
        int i3 = (i2 + 32) - 1;
        i = i3 % 128;
        switch (i3 % 2 == 0 ? 'K' : 'Q') {
            case 'K':
                eVar = this.c;
                int i4 = 13 / 0;
                break;
            default:
                eVar = this.c;
                break;
        }
        int i5 = (i2 & Opcodes.LREM) + (i2 | Opcodes.LREM);
        i = i5 % 128;
        switch (i5 % 2 == 0 ? 'M' : 'Q') {
            case 'M':
                int i6 = 82 / 0;
                return eVar;
            default:
                return eVar;
        }
    }

    public final c a() {
        int i2 = i;
        int i3 = ((i2 | Opcodes.LSUB) << 1) - (i2 ^ Opcodes.LSUB);
        j = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return this.b;
            default:
                throw null;
        }
    }

    public final boolean b() {
        int i2 = j + 71;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return this.d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final a d() {
        int i2 = (i + 2) - 1;
        j = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 11 : 'X') {
            case Opcodes.POP2 /* 88 */:
                return this.a;
            default:
                throw null;
        }
    }

    public final String e() {
        int i2 = j;
        int i3 = i2 + Opcodes.DNEG;
        int i4 = i3 % 128;
        i = i4;
        switch (i3 % 2 == 0 ? 'W' : '^') {
            case Opcodes.DUP2_X2 /* 94 */:
                String str = this.e;
                switch (str != null) {
                    case false:
                        int i5 = i2 + 5;
                        int i6 = i5 % 128;
                        i = i6;
                        int i7 = i5 % 2;
                        int i8 = (i6 & Opcodes.LSHL) + (i6 | Opcodes.LSHL);
                        j = i8 % 128;
                        int i9 = i8 % 2;
                        return "";
                    default:
                        int i10 = (i4 + Opcodes.IAND) - 1;
                        j = i10 % 128;
                        int i11 = i10 % 2;
                        return str;
                }
            default:
                throw null;
        }
    }

    public final void f() {
        int i2 = i;
        int i3 = (i2 ^ Opcodes.DREM) + ((i2 & Opcodes.DREM) << 1);
        j = i3 % 128;
        int i4 = i3 % 2;
        this.d = true;
        Object obj = null;
        this.a = null;
        this.b.h();
        int i5 = j;
        int i6 = (i5 ^ 53) + ((i5 & 53) << 1);
        i = i6 % 128;
        switch (i6 % 2 == 0 ? (char) 5 : '!') {
            case '!':
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }
}
