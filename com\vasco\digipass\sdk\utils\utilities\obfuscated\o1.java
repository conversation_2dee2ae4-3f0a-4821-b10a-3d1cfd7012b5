package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\o1.smali */
public interface o1 {
    public static final w A;
    public static final w B;
    public static final w C;
    public static final w D;
    public static final w a;
    public static final w b;
    public static final w c;
    public static final w d;
    public static final w e;
    public static final w f;
    public static final w g;
    public static final w h;
    public static final w i;
    public static final w j;
    public static final w k;
    public static final w l;
    public static final w m;
    public static final w n;

    /* renamed from: o, reason: collision with root package name */
    public static final w f25o;
    public static final w p;
    public static final w q;
    public static final w r;
    public static final w s;
    public static final w t;
    public static final w u;
    public static final w v;
    public static final w w;
    public static final w x;
    public static final w y;
    public static final w z;

    static {
        w wVar = new w("1.2.643.2.2");
        a = wVar;
        b = wVar.a("9");
        c = wVar.a("10");
        d = wVar.a("13.0");
        e = wVar.a("13.1");
        f = wVar.a("21");
        g = wVar.a("31.0");
        h = wVar.a("31.1");
        i = wVar.a("31.2");
        j = wVar.a("31.3");
        k = wVar.a("31.4");
        l = wVar.a("20");
        m = wVar.a("19");
        n = wVar.a("4");
        f25o = wVar.a("3");
        p = wVar.a("30.1");
        q = wVar.a("32.2");
        r = wVar.a("32.3");
        s = wVar.a("32.4");
        t = wVar.a("32.5");
        u = wVar.a("33.1");
        v = wVar.a("33.2");
        w = wVar.a("33.3");
        x = wVar.a("35.1");
        y = wVar.a("35.2");
        z = wVar.a("35.3");
        A = wVar.a("36.0");
        B = wVar.a("36.1");
        C = wVar.a("96");
        D = wVar.a("98");
    }
}
