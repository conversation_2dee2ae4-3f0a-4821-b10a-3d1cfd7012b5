package fr.antelop.sdk.digitalcard;

import o.du.c;
import o.eo.a;
import o.eo.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\EcomStaticToken.smali */
public final class EcomStaticToken {
    private final a innerEcomStaticToken;

    public EcomStaticToken(a aVar) {
        this.innerEcomStaticToken = aVar;
    }

    public final String getTspTokenId() {
        return this.innerEcomStaticToken.e();
    }

    public final String getTspCardId() {
        return this.innerEcomStaticToken.c();
    }

    public final String getTspShortDescription() {
        return this.innerEcomStaticToken.b();
    }

    public final c getTspImageUrl() {
        return this.innerEcomStaticToken.d();
    }

    public final String getBin() {
        return this.innerEcomStaticToken.a();
    }

    public final String getLastDigits() {
        return this.innerEcomStaticToken.g();
    }

    public final String getExpiryDate() {
        return this.innerEcomStaticToken.i();
    }

    public final b getStatus() {
        return this.innerEcomStaticToken.f();
    }
}
