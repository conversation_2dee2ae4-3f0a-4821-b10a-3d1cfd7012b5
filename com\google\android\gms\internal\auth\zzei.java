package com.google.android.gms.internal.auth;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzei.smali */
public class zzei {
    public static final /* synthetic */ int zza = 0;
    private static volatile int zzb = 100;

    /* synthetic */ zzei(zzeh zzehVar) {
    }

    public static int zzb(int i) {
        return (-(i & 1)) ^ (i >>> 1);
    }

    public static long zzc(long j) {
        return (-(j & 1)) ^ (j >>> 1);
    }
}
