package fr.antelop.sdk;

import android.content.Context;
import fr.antelop.sdk.card.Card;
import fr.antelop.sdk.card.CreateCardRequestBuilder;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import fr.antelop.sdk.digitalcard.DigitalCard;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.settings.WalletSettings;
import fr.antelop.sdk.util.OperationCallback;
import java.security.cert.X509Certificate;
import java.util.List;
import java.util.Map;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\Wallet.smali */
public final class Wallet {
    private final c innerWallet;

    public Wallet(c cVar) {
        this.innerWallet = cVar;
    }

    public final void setDefaultCard(String str) throws WalletValidationException {
        this.innerWallet.a(str);
    }

    public final String getDefaultCardId() {
        return this.innerWallet.h();
    }

    public final void setNextTransactionCard(String str) throws WalletValidationException {
        this.innerWallet.e(str);
    }

    public final String getNextTransactionCardId() {
        return this.innerWallet.g();
    }

    public final void resetNextTransactionCard() throws WalletValidationException {
        this.innerWallet.f();
    }

    public final Map<String, Card> cards(boolean z) {
        return this.innerWallet.b(z);
    }

    public final Map<String, DigitalCard> digitalCards(boolean z) {
        return this.innerWallet.d(z);
    }

    public final void createCard(Context context, CreateCardRequestBuilder createCardRequestBuilder, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerWallet.b(context, createCardRequestBuilder, antelopCallback);
    }

    public final void enrollDigitalCard(Context context, String str, AntelopCallback antelopCallback) throws WalletValidationException {
        this.innerWallet.c(context, str, antelopCallback);
    }

    public final WalletSettings settings() {
        return this.innerWallet.n();
    }

    public final List<Product> availableProducts() {
        return this.innerWallet.r();
    }

    public final Card getCard(String str) throws WalletValidationException {
        return this.innerWallet.c(str);
    }

    public final DigitalCard getDigitalCard(String str) throws WalletValidationException {
        return this.innerWallet.b(str);
    }

    public final EmvApplication getEmvApplication(String str) throws WalletValidationException {
        return this.innerWallet.d(str);
    }

    public final String getWalletId() {
        return this.innerWallet.o();
    }

    public final String getIssuerData() {
        return this.innerWallet.k();
    }

    public final String getIssuerWalletId() {
        return this.innerWallet.l();
    }

    public final String getIssuerClientId() {
        return this.innerWallet.m();
    }

    public final String getVtsClientDeviceId() {
        return this.innerWallet.w();
    }

    public final String getVtsClientWalletAccountId() {
        return this.innerWallet.y();
    }

    public final String getMdesPaymentAppInstanceId() {
        return this.innerWallet.u();
    }

    public final X509Certificate getCertificate() {
        return this.innerWallet.x();
    }

    public final X509Certificate getSecureDisplayCertificate() {
        return this.innerWallet.v();
    }

    public final void refreshDigitalCards(Context context, OperationCallback<Void> operationCallback) {
        this.innerWallet.e(context, operationCallback);
    }

    public final String toString() {
        return new StringBuilder("Wallet{id=").append(getWalletId()).append(", defaultCardId=").append(getDefaultCardId() == null ? "" : getDefaultCardId()).append(", nextTransactionCardId=").append(getNextTransactionCardId() == null ? "" : getNextTransactionCardId()).append(", issuerData=").append(getIssuerData() == null ? "" : getIssuerData()).append(", issuerWalletId=").append(getIssuerWalletId() == null ? "" : getIssuerWalletId()).append(", issuerClientId=").append(getIssuerClientId() == null ? "" : getIssuerClientId()).append(", vtsClientDeviceId=").append(getVtsClientDeviceId()).append(", vtsClientWalletAccountId=").append(getVtsClientWalletAccountId()).append(", mdesPaymentAppInstanceId=").append(getMdesPaymentAppInstanceId() != null ? getMdesPaymentAppInstanceId() : "").append('}').toString();
    }
}
