package kotlinx.coroutines.internal;

import kotlin.Metadata;

/* compiled from: LockFreeLinkedList.common.kt */
@Metadata(d1 = {"\u0000\n\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\"\u0016\u0010\u0000\u001a\u00020\u00018\u0000X\u0081\u0004¢\u0006\b\n\u0000\u0012\u0004\b\u0002\u0010\u0003¨\u0006\u0004"}, d2 = {"REMOVE_PREPARED", "", "getREMOVE_PREPARED$annotations", "()V", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\internal\LockFreeLinkedList_commonKt.smali */
public final class LockFreeLinkedList_commonKt {
    public static final Object REMOVE_PREPARED = new Symbol("REMOVE_PREPARED");

    public static /* synthetic */ void getREMOVE_PREPARED$annotations() {
    }
}
