package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.v5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP224K1FieldElement.smali */
public class SecP224K1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFE56D"));
    private static final int[] b = {868209154, -587542221, 579297866, -1014948952, -1470801668, 514782679, -1897982644};
    protected int[] a;

    public SecP224K1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP224K1FieldElement");
        }
        this.a = SecP224K1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224K1Field.add(this.a, ((SecP224K1FieldElement) eCFieldElement).a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = v5.a();
        SecP224K1Field.addOne(this.a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224K1Field.inv(((SecP224K1FieldElement) eCFieldElement).a, a);
        SecP224K1Field.multiply(a, this.a, a);
        return new SecP224K1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP224K1FieldElement) {
            return v5.b(this.a, ((SecP224K1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP224K1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 7);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = v5.a();
        SecP224K1Field.inv(this.a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return v5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return v5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224K1Field.multiply(this.a, ((SecP224K1FieldElement) eCFieldElement).a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = v5.a();
        SecP224K1Field.negate(this.a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (v5.b(iArr) || v5.a(iArr)) {
            return this;
        }
        int[] a = v5.a();
        SecP224K1Field.square(iArr, a);
        SecP224K1Field.multiply(a, iArr, a);
        SecP224K1Field.square(a, a);
        SecP224K1Field.multiply(a, iArr, a);
        int[] a2 = v5.a();
        SecP224K1Field.square(a, a2);
        SecP224K1Field.multiply(a2, iArr, a2);
        int[] a3 = v5.a();
        SecP224K1Field.squareN(a2, 4, a3);
        SecP224K1Field.multiply(a3, a2, a3);
        int[] a4 = v5.a();
        SecP224K1Field.squareN(a3, 3, a4);
        SecP224K1Field.multiply(a4, a, a4);
        SecP224K1Field.squareN(a4, 8, a4);
        SecP224K1Field.multiply(a4, a3, a4);
        SecP224K1Field.squareN(a4, 4, a3);
        SecP224K1Field.multiply(a3, a2, a3);
        SecP224K1Field.squareN(a3, 19, a2);
        SecP224K1Field.multiply(a2, a4, a2);
        int[] a5 = v5.a();
        SecP224K1Field.squareN(a2, 42, a5);
        SecP224K1Field.multiply(a5, a2, a5);
        SecP224K1Field.squareN(a5, 23, a2);
        SecP224K1Field.multiply(a2, a3, a2);
        SecP224K1Field.squareN(a2, 84, a3);
        SecP224K1Field.multiply(a3, a5, a3);
        SecP224K1Field.squareN(a3, 20, a3);
        SecP224K1Field.multiply(a3, a4, a3);
        SecP224K1Field.squareN(a3, 3, a3);
        SecP224K1Field.multiply(a3, iArr, a3);
        SecP224K1Field.squareN(a3, 2, a3);
        SecP224K1Field.multiply(a3, iArr, a3);
        SecP224K1Field.squareN(a3, 4, a3);
        SecP224K1Field.multiply(a3, a, a3);
        SecP224K1Field.square(a3, a3);
        SecP224K1Field.square(a3, a5);
        if (v5.b(iArr, a5)) {
            return new SecP224K1FieldElement(a3);
        }
        SecP224K1Field.multiply(a3, b, a3);
        SecP224K1Field.square(a3, a5);
        if (v5.b(iArr, a5)) {
            return new SecP224K1FieldElement(a3);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = v5.a();
        SecP224K1Field.square(this.a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = v5.a();
        SecP224K1Field.subtract(this.a, ((SecP224K1FieldElement) eCFieldElement).a, a);
        return new SecP224K1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return v5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return v5.c(this.a);
    }

    public SecP224K1FieldElement() {
        this.a = v5.a();
    }

    protected SecP224K1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
