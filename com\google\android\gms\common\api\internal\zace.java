package com.google.android.gms.common.api.internal;

import com.google.android.gms.common.internal.MethodInvocation;

/* compiled from: com.google.android.gms:play-services-base@@18.4.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\common\api\internal\zace.smali */
final class zace {
    final MethodInvocation zaa;
    final int zab;
    final long zac;
    final int zad;

    zace(MethodInvocation methodInvocation, int i, long j, int i2) {
        this.zaa = methodInvocation;
        this.zab = i;
        this.zac = j;
        this.zad = i2;
    }
}
