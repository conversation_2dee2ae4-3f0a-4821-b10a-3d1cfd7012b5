package kotlin.jvm.functions;

import kotlin.Function;
import kotlin.Metadata;

/* compiled from: Functions.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u000b\bf\u0018\u0000*\u0006\b\u0000\u0010\u0001 \u0000*\u0006\b\u0001\u0010\u0002 \u0000*\u0006\b\u0002\u0010\u0003 \u0000*\u0006\b\u0003\u0010\u0004 \u0000*\u0006\b\u0004\u0010\u0005 \u0000*\u0006\b\u0005\u0010\u0006 \u0000*\u0006\b\u0006\u0010\u0007 \u0000*\u0006\b\u0007\u0010\b \u0000*\u0006\b\b\u0010\t \u00012\b\u0012\u0004\u0012\u0002H\t0\nJN\u0010\u000b\u001a\u00028\b2\u0006\u0010\f\u001a\u00028\u00002\u0006\u0010\r\u001a\u00028\u00012\u0006\u0010\u000e\u001a\u00028\u00022\u0006\u0010\u000f\u001a\u00028\u00032\u0006\u0010\u0010\u001a\u00028\u00042\u0006\u0010\u0011\u001a\u00028\u00052\u0006\u0010\u0012\u001a\u00028\u00062\u0006\u0010\u0013\u001a\u00028\u0007H¦\u0002¢\u0006\u0002\u0010\u0014¨\u0006\u0015"}, d2 = {"Lkotlin/jvm/functions/Function8;", "P1", "P2", "P3", "P4", "P5", "P6", "P7", "P8", "R", "Lkotlin/Function;", "invoke", "p1", "p2", "p3", "p4", "p5", "p6", "p7", "p8", "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", "kotlin-stdlib"}, k = 1, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\jvm\functions\Function8.smali */
public interface Function8<P1, P2, P3, P4, P5, P6, P7, P8, R> extends Function<R> {
    R invoke(P1 p1, P2 p2, P3 p3, P4 p4, P5 p5, P6 p6, P7 p7, P8 p8);
}
