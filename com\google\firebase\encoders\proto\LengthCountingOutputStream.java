package com.google.firebase.encoders.proto;

import java.io.OutputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\encoders\proto\LengthCountingOutputStream.smali */
final class LengthCountingOutputStream extends OutputStream {
    private long length = 0;

    LengthCountingOutputStream() {
    }

    @Override // java.io.OutputStream
    public void write(int b) {
        this.length++;
    }

    @Override // java.io.OutputStream
    public void write(byte[] b) {
        this.length += b.length;
    }

    @Override // java.io.OutputStream
    public void write(byte[] b, int off, int len) {
        if (off < 0 || off > b.length || len < 0 || off + len > b.length || off + len < 0) {
            throw new IndexOutOfBoundsException();
        }
        this.length += len;
    }

    long getLength() {
        return this.length;
    }
}
