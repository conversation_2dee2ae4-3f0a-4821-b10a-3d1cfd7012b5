package o.fb;

import android.content.Context;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ey.a;
import o.ey.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fb\c.smali */
public final class c extends e<o.fh.b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char c;
    private static char d;
    private static int[] e;
    private static int f;
    private static int h;
    private static char i;
    private int a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        r();
        ViewConfiguration.getFadingEdgeLength();
        int i2 = f + 65;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{124, 92, -85, -9};
        $$e = 63;
    }

    static void r() {
        e = new int[]{-342381197, 1237419155, 1886071571, -154240964, 2064594216, 1346672100, -289455340, -1825102115, -1181279852, 137425831, -692475987, 936789617, 581470100, -849874252, 405233074, -212301889, 1381947808, 356754988};
        b = (char) 52865;
        d = (char) 10807;
        i = (char) 13018;
        c = (char) 28833;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Type inference failed for: r5v2, types: [int] */
    /* JADX WARN: Type inference failed for: r5v3 */
    /* JADX WARN: Type inference failed for: r5v5, types: [int] */
    /* JADX WARN: Type inference failed for: r5v6, types: [int] */
    /* JADX WARN: Type inference failed for: r6v1, types: [int] */
    /* JADX WARN: Type inference failed for: r6v4, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x0029). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void x(byte r5, byte r6, int r7, java.lang.Object[] r8) {
        /*
            byte[] r0 = o.fb.c.$$d
            int r6 = 116 - r6
            int r5 = r5 * 2
            int r5 = 4 - r5
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r6
            r4 = r2
            r6 = r5
            goto L29
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L27
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L27:
            r3 = r0[r5]
        L29:
            int r5 = r5 + 1
            int r6 = r6 + r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.x(byte, byte, int, java.lang.Object[]):void");
    }

    @Override // o.ey.e
    public final void b() {
        int i2 = f + 75;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    @Override // o.ey.e
    public final /* synthetic */ boolean a(Context context, o.fh.b bVar) {
        int i2 = h + 55;
        f = i2 % 128;
        int i3 = i2 % 2;
        boolean c2 = c(bVar);
        int i4 = h + 5;
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return c2;
        }
    }

    @Override // o.ey.e
    public final /* synthetic */ a e() {
        int i2 = f + 85;
        h = i2 % 128;
        int i3 = i2 % 2;
        b t = t();
        int i4 = h + Opcodes.LREM;
        f = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 22 : (char) 25) {
            case 22:
                int i5 = 93 / 0;
                return t;
            default:
                return t;
        }
    }

    public c(String str, String str2, boolean z) {
        super(str, str2, z);
        this.a = 1;
    }

    public final int p() {
        int i2 = f + 25;
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        int i5 = this.a;
        int i6 = i3 + 81;
        f = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final void e(int i2) {
        int i3 = f + 67;
        h = i3 % 128;
        char c2 = i3 % 2 == 0 ? '/' : '!';
        this.a = i2;
        switch (c2) {
            case '!':
                return;
            default:
                throw null;
        }
    }

    private static b t() {
        b bVar = new b();
        int i2 = h + 79;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 0 : '\'') {
            case 0:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bVar;
        }
    }

    @Override // o.ey.e
    public final o.ct.b<o.fh.b> c() {
        o.da.e eVar = new o.da.e();
        int i2 = h + 31;
        f = i2 % 128;
        int i3 = i2 % 2;
        return eVar;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private boolean c(o.fh.b r7) {
        /*
            r6 = this;
            int r0 = o.fb.c.f
            int r0 = r0 + 65
            int r1 = r0 % 128
            o.fb.c.h = r1
            int r0 = r0 % 2
            java.util.List r0 = r6.f()
            if (r0 != 0) goto L18
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            r6.c(r0)
        L18:
            java.util.List r0 = r6.f()
            java.util.ListIterator r0 = r0.listIterator()
        L20:
            boolean r1 = r0.hasNext()
            r2 = 0
            r3 = 1
            if (r1 == 0) goto L2a
            r1 = r2
            goto L2b
        L2a:
            r1 = r3
        L2b:
            switch(r1) {
                case 0: goto L36;
                default: goto L2e;
            }
        L2e:
            java.util.List r0 = r6.f()
            r0.add(r7)
            goto La5
        L36:
            java.lang.Object r1 = r0.next()
            o.fh.b r1 = (o.fh.b) r1
            int r1 = r1.h()
            int r4 = r7.h()
            if (r1 != r4) goto L48
            r1 = r2
            goto L49
        L48:
            r1 = r3
        L49:
            switch(r1) {
                case 0: goto L4d;
                default: goto L4c;
            }
        L4c:
            goto L20
        L4d:
            int r1 = o.fb.c.f
            int r1 = r1 + 125
            int r4 = r1 % 128
            o.fb.c.h = r4
            int r1 = r1 % 2
            o.ee.g.c()
            r1 = 12
            int[] r1 = new int[r1]
            r1 = {x00b2: FILL_ARRAY_DATA , data: [-1719572936, 718222049, 409703510, -343026487, 568863252, -1312535897, -1652806003, 1973965674, -1712177544, 958061924, 218241924, -736759385} // fill-array
            java.lang.String r4 = ""
            int r4 = android.text.TextUtils.indexOf(r4, r4)
            int r4 = 23 - r4
            java.lang.Object[] r5 = new java.lang.Object[r3]
            u(r1, r4, r5)
            r1 = r5[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            r4 = 34
            int[] r4 = new int[r4]
            r4 = {x00ce: FILL_ARRAY_DATA , data: [634808144, 511904144, -1506091660, 1110313171, -1046637028, -1598668964, 1429542379, 1232816354, 1540963138, 253586494, 1367942108, -1475945083, 562360520, 169759991, -185078846, -739959680, 2121793045, -1934475402, 933753849, -862826746, 1406037682, 739300733, -1343912168, -1707475160, -284121528, -23293481, 1669315146, 1301141763, -2128934259, 418765930, -1524950770, -193388145, -1191846142, -1928352739} // fill-array
            int r5 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r5 = (byte) r5
            int r5 = 65 - r5
            java.lang.Object[] r3 = new java.lang.Object[r3]
            u(r4, r5, r3)
            r3 = r3[r2]
            java.lang.String r3 = (java.lang.String) r3
            java.lang.String r3 = r3.intern()
            java.lang.String r4 = r6.d()
            java.lang.Object[] r4 = new java.lang.Object[]{r4}
            java.lang.String r3 = java.lang.String.format(r3, r4)
            o.ee.g.d(r1, r3)
            r0.set(r7)
            return r2
        La5:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.c(o.fh.b):boolean");
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0074, code lost:
    
        if (j() != null) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0076, code lost:
    
        r12 = new o.eg.e();
        r4 = r11.a;
        r5 = f().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0089, code lost:
    
        if (r5.hasNext() == false) goto L62;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x008b, code lost:
    
        r6 = o.fb.c.f + 77;
        o.fb.c.h = r6 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0095, code lost:
    
        if ((r6 % 2) != 0) goto L22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0097, code lost:
    
        r6 = '\\';
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x009c, code lost:
    
        switch(r6) {
            case 92: goto L63;
            default: goto L24;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x009f, code lost:
    
        r6 = r5.next();
        r7 = r6.e(r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x00a9, code lost:
    
        if (r7 == null) goto L64;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x00b9, code lost:
    
        r8 = o.fb.c.f + 7;
        o.fb.c.h = r8 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00c4, code lost:
    
        if ((r8 % 2) != 0) goto L34;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00c6, code lost:
    
        r8 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00c9, code lost:
    
        switch(r8) {
            case 1: goto L36;
            default: goto L36;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00cd, code lost:
    
        r8 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00d2, code lost:
    
        if (r8 >= r7.d()) goto L65;
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x00d4, code lost:
    
        r12.b(r7.b(r8));
        r8 = r8 + 1;
        r9 = o.fb.c.h + 29;
        o.fb.c.f = r9 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00e7, code lost:
    
        if ((r9 % 2) == 0) goto L42;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x00e9, code lost:
    
        r9 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x00ec, code lost:
    
        switch(r9) {
            case 0: goto L37;
            default: goto L37;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x00eb, code lost:
    
        r9 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x00f1, code lost:
    
        r4 = r4 + r6.i();
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00c8, code lost:
    
        r8 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x00ac, code lost:
    
        r5.next().e(r4);
     */
    /* JADX WARN: Code restructure failed: missing block: B:42:0x00b6, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:47:0x009a, code lost:
    
        r6 = 'I';
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x00f7, code lost:
    
        r7 = new java.lang.Object[1];
        u(new int[]{-506505501, -1764251032, -805199650, -1380399621, 2012710720, 1363789591}, android.widget.ExpandableListView.getPackedPositionChild(0) + 12, r7);
        r0.d(((java.lang.String) r7[0]).intern(), r12);
        r5 = new java.lang.Object[1];
        v("뛟য়伅ȯ톇䎡鉞팵ꒁ﹄覤嫑笥ዩ眢\ud965", 16 - android.text.TextUtils.getCapsMode("", 0, 0), r5);
        r0.d(((java.lang.String) r5[0]).intern(), j().e());
        r12 = j().d();
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x013e, code lost:
    
        if (r12 == null) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0140, code lost:
    
        r5 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:52:0x0143, code lost:
    
        switch(r5) {
            case 1: goto L53;
            default: goto L56;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x0147, code lost:
    
        r6 = new java.lang.Object[1];
        u(new int[]{-1958702307, 819638147, 1906619594, 876288274, -847862837, -1013074721}, android.graphics.ImageFormat.getBitsPerPixel(0) + 11, r6);
        r0.d(((java.lang.String) r6[0]).intern(), r12.getTime() / 1000);
        r12 = o.fb.c.h + 77;
        o.fb.c.f = r12 % 128;
        r12 = r12 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0142, code lost:
    
        r5 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x006e, code lost:
    
        if (j() != null) goto L16;
     */
    @Override // o.ey.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.eg.b b(o.ek.b r12) throws o.eg.d {
        /*
            Method dump skipped, instructions count: 564
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.b(o.ek.b):o.eg.b");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.ey.e
    public final o.fc.e k() {
        /*
            Method dump skipped, instructions count: 662
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.k():o.fc.e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 990
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.u(int[], int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void v(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 622
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fb.c.v(java.lang.String, int, java.lang.Object[]):void");
    }
}
