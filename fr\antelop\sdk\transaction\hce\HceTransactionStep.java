package fr.antelop.sdk.transaction.hce;

import o.dp.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\transaction\hce\HceTransactionStep.smali */
public final class HceTransactionStep {
    public static final int TRANSACTION_FIRST_STEP_INDEX = 0;
    public static final int TRANSACTION_LAST_STEP_INDEX = 5;
    private final d innerStep;

    public HceTransactionStep(d dVar) {
        this.innerStep = dVar;
    }

    public final HceTransactionStepName getName() {
        return this.innerStep.c();
    }

    public final int getIndex() {
        return this.innerStep.e();
    }

    public final String toString() {
        return new StringBuilder("HceTransactionStep{name=").append(getName()).append(",index=").append(getIndex()).append('}').toString();
    }
}
