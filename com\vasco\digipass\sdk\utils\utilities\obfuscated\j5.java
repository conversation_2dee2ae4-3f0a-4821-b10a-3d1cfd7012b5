package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.InputStream;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j5.smali */
abstract class j5 extends InputStream {
    protected final InputStream b;
    private int x;

    j5(InputStream inputStream, int i) {
        this.b = inputStream;
        this.x = i;
    }

    int a() {
        return this.x;
    }

    protected void a(boolean z) {
        InputStream inputStream = this.b;
        if (inputStream instanceof c5) {
            ((c5) inputStream).b(z);
        }
    }
}
