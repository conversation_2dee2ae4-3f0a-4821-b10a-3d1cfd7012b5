package cz.muni.fi.xklinex.whiteboxAES;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.r5;
import com.vasco.digipass.sdk.utils.utilities.wbc.WBCTable;
import cz.muni.fi.xklinex.whiteboxAES.generator.ExternalBijections;
import cz.muni.fi.xklinex.whiteboxAES.generator.GF2MatrixEx;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\AES.smali */
public class AES implements Serializable {
    public static final int BYTES = 16;
    public static final int ROUNDS = 10;
    public static final int T1BOXES = 2;
    private static final long serialVersionUID = 7018750917462672211L;
    public static final int[] shiftRows = {0, 1, 2, 3, 5, 6, 7, 4, 10, 11, 8, 9, 15, 12, 13, 14};
    public static final int[] shiftRowsInv = {0, 1, 2, 3, 7, 4, 5, 6, 10, 11, 8, 9, 13, 14, 15, 12};
    protected T1Box[][] t1 = (T1Box[][]) Array.newInstance((Class<?>) T1Box.class, 2, 16);
    protected XORCascadeState[] xorState = new XORCascadeState[2];
    protected T2Box[][] t2 = (T2Box[][]) Array.newInstance((Class<?>) T2Box.class, 10, 16);
    protected T3Box[][] t3 = (T3Box[][]) Array.newInstance((Class<?>) T3Box.class, 10, 16);
    protected XORCascade[][] xor = (XORCascade[][]) Array.newInstance((Class<?>) XORCascade.class, 10, 8);
    private boolean encrypt = true;

    public static byte HI(byte b) {
        return (byte) ((b >>> 4) & 15);
    }

    public static byte HILO(byte b, byte b2) {
        return (byte) (((b & 15) << 4) | (b2 & 15));
    }

    public static byte LO(byte b) {
        return (byte) (b & 15);
    }

    public static void applyExternalEnc(State state, ExternalBijections externalBijections, boolean z) {
        if (z) {
            GF2MatrixEx gF2MatrixEx = new GF2MatrixEx(128, 1);
            for (int i = 0; i < 16; i++) {
                r5.a(gF2MatrixEx, state.get(i), i * 8, 0);
            }
            GF2MatrixEx gF2MatrixEx2 = (GF2MatrixEx) externalBijections.getIODM()[0].getMb().rightMultiply(gF2MatrixEx);
            for (int i2 = 0; i2 < 16; i2++) {
                state.set(r5.a(gF2MatrixEx2, i2 * 8, 0), i2);
            }
            for (int i3 = 0; i3 < 16; i3++) {
                int i4 = i3 * 2;
                state.set(HILO((byte) (externalBijections.getLfC()[0][i4 + 1].coding[HI(state.get(i3))] & 255), (byte) (externalBijections.getLfC()[0][i4].coding[LO(state.get(i3))] & 255)), i3);
            }
            return;
        }
        for (int i5 = 0; i5 < 16; i5++) {
            int i6 = i5 * 2;
            state.set(HILO((byte) (externalBijections.getLfC()[1][i6 + 1].invCoding[HI(state.get(i5))] & 255), (byte) (externalBijections.getLfC()[1][i6].invCoding[LO(state.get(i5))] & 255)), i5);
        }
        GF2MatrixEx gF2MatrixEx3 = new GF2MatrixEx(128, 1);
        for (int i7 = 0; i7 < 16; i7++) {
            r5.a(gF2MatrixEx3, state.get(i7), i7 * 8, 0);
        }
        GF2MatrixEx gF2MatrixEx4 = (GF2MatrixEx) externalBijections.getIODM()[1].getInv().rightMultiply(gF2MatrixEx3);
        for (int i8 = 0; i8 < 16; i8++) {
            state.set(r5.a(gF2MatrixEx4, i8 * 8, 0), i8);
        }
    }

    public static int[] getShift(boolean z) {
        return z ? shiftRows : shiftRowsInv;
    }

    public static int posIdx(byte b) {
        return b & 255;
    }

    public State crypt(State state) {
        W32b[] w32bArr = new W32b[16];
        State[] stateArr = new State[16];
        for (int i = 0; i < 16; i++) {
            w32bArr[i] = new W32b();
            stateArr[i] = new State();
        }
        for (int i2 = 0; i2 < 16; i2++) {
            stateArr[i2].loadFrom(this.t1[0][i2].lookup(state.get(i2)));
        }
        this.xorState[0].xor(stateArr);
        state.loadFrom(stateArr[0]);
        for (int i3 = 0; i3 < 9; i3++) {
            for (int i4 = 0; i4 < 16; i4++) {
                w32bArr[i4].set(this.t2[i3][i4].lookup(state.get(shift(i4))));
            }
            for (int i5 = 0; i5 < 4; i5++) {
                W32b w32b = w32bArr[i5];
                int i6 = i5 * 2;
                int i7 = i5 + 4;
                int i8 = i5 + 8;
                int i9 = i5 + 12;
                w32b.set(this.xor[i3][i6].xor(w32b.getLong(), w32bArr[i7].getLong(), w32bArr[i8].getLong(), w32bArr[i9].getLong()));
                byte[] bArr = w32bArr[i5].get();
                w32bArr[i9].set(this.t3[i3][i9].lookup(bArr[3]));
                w32bArr[i8].set(this.t3[i3][i8].lookup(bArr[2]));
                w32bArr[i7].set(this.t3[i3][i7].lookup(bArr[1]));
                w32bArr[i5].set(this.t3[i3][i5].lookup(bArr[0]));
                W32b w32b2 = w32bArr[i5];
                w32b2.set(this.xor[i3][i6 + 1].xor(w32b2.getLong(), w32bArr[i7].getLong(), w32bArr[i8].getLong(), w32bArr[i9].getLong()));
                state.setColumn(w32bArr[i5], i5);
            }
        }
        for (int i10 = 0; i10 < 16; i10++) {
            stateArr[i10].loadFrom(this.t1[1][i10].lookup(state.get(shift(i10))));
        }
        this.xorState[1].xor(stateArr);
        state.loadFrom(stateArr[0]);
        return state;
    }

    public boolean equals(Object obj) {
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AES aes = (AES) obj;
        return Arrays.deepEquals(this.t1, aes.t1) && Arrays.deepEquals(this.xorState, aes.xorState) && Arrays.deepEquals(this.t2, aes.t2) && Arrays.deepEquals(this.t3, aes.t3) && Arrays.deepEquals(this.xor, aes.xor) && this.encrypt == aes.encrypt;
    }

    public T1Box[][] getT1() {
        return this.t1;
    }

    public T2Box[][] getT2() {
        return this.t2;
    }

    public T3Box[][] getT3() {
        return this.t3;
    }

    public XORCascade[][] getXor() {
        return this.xor;
    }

    public XORCascadeState[] getXorState() {
        return this.xorState;
    }

    public int hashCode() {
        return ((((((((((Arrays.deepHashCode(this.t1) + 623) * 89) + Arrays.deepHashCode(this.xorState)) * 89) + Arrays.deepHashCode(this.t2)) * 89) + Arrays.deepHashCode(this.t3)) * 89) + Arrays.deepHashCode(this.xor)) * 89) + (this.encrypt ? 1 : 0);
    }

    public void init(WBCTable wBCTable) {
        this.t1 = wBCTable.getT1();
        this.xorState = wBCTable.getXorState();
        this.t2 = wBCTable.getT2();
        this.t3 = wBCTable.getT3();
        this.xor = wBCTable.getXor();
    }

    public void setEncrypt(boolean z) {
        this.encrypt = z;
    }

    public int shift(int i) {
        return getShift(this.encrypt)[i];
    }
}
