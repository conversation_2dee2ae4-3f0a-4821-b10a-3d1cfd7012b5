package o.ee;

import android.content.ContentResolver;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.content.pm.SigningInfo;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.nfc.NfcAdapter;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.content.pm.PackageInfoCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.common.GoogleApiAvailability;
import com.huawei.hms.api.HuaweiApiAvailability;
import java.lang.reflect.Method;
import java.util.Date;
import java.util.concurrent.TimeoutException;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\c.smali */
public abstract class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static c c;
    private static int d;
    private static int e;
    private static short[] f;
    private static long g;
    private static byte[] h;
    private static char i;
    private static int j;
    private static int k;
    private static int l;
    private Integer b = null;

    static {
        init$1();
        $10 = 0;
        $11 = 1;
        init$0();
        k = 0;
        l = 1;
        f();
        ViewConfiguration.getTouchSlop();
        ViewConfiguration.getTapTimeout();
        ViewConfiguration.getKeyRepeatTimeout();
        ExpandableListView.getPackedPositionChild(0L);
        AudioTrack.getMinVolume();
        int i2 = k + 47;
        l = i2 % 128;
        switch (i2 % 2 == 0 ? '0' : (char) 31) {
            case 31:
                break;
            default:
                int i3 = 52 / 0;
                break;
        }
    }

    static void f() {
        h = new byte[]{77, 107, 106, 109, -98, 102, -67, 124, -99, 101, -112, -97, ByteCompanionObject.MAX_VALUE, -117, 99, 107, 106, 109, -98, 102, -67, 93, -127, -110, ByteCompanionObject.MAX_VALUE, 110, -111, -127, -79, 99, -101, -110, 110, -75, -76, 37, -104, 110, 109, -75, -77, 41, 105, -107, 102, -112, -102, -116, -67, 99, -99, 39, -108, 104, -76, 69, -97, 110, 122, -108, 104, -76, -71, 41, 105, 111, 109, -117, -89, 99, -99, 39, -108, 104, -76, 69, -97, 110, 116, 104, -100, 99, -97, 103, 100, -107, -112, 107, -97, 96, -103, -102, -112, 102, 105, -106, -103, -42, 43, 97, -125, 110, 106, -109, 109, 101, -110, -99, -110, -45, 33, -79, -66, 99, -99, 40, 101, -126, -119, 115, 99, 109, -111, -125, 99, -105, 110, -122, 115, -112, -65, 93, -97, 110, 71, -100, -99, -109, 101, 104, -105, -34, 34, 111, -106, 102, -111, -99, ByteCompanionObject.MAX_VALUE, -58, 32, -112, -97, -47, 60, -97, 110, -41, 33, 107, -60, 43, 105, -102, -111, 99, -119, -91, 99, -99, 62, -99, -109, 101, 104, -73, 112, 111, -106, 102, -111, -99, -97, 118, -112, -65, 93, -97, 110, -121, -111, -98, 111, 109, 107, -99, 100, -35, 34, -107, -39, 44, 102, 105, -106, -103, -42, 60, -107, -111, -34, Base64.padSymbol, -34, -107, 47, 125, -125, 109, -109, 125, -97, 104, -35, 99, -99, 60, -111, -98, 111, 109, 107, -99, -124, 112, 110, 109, PSSSigner.TRAILER_IMPLICIT, 79, 105, -107, 103, 110, -79, 98, 125, -125, 109, -109, 125, -97, -120, 73, -97, 110};
        e = 909053626;
        a = 1614666905;
        d = 436714091;
        i = (char) 1933;
        j = 161105445;
        g = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{71, -50, -52, -118};
        $$b = Opcodes.JSR;
    }

    static void init$1() {
        $$d = new byte[]{81, -74, 18, 60};
        $$e = 228;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0030). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void t(int r7, short r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.ee.c.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            int r8 = r8 * 3
            int r8 = 4 - r8
            int r9 = r9 * 3
            int r9 = r9 + 101
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r9
            r4 = r2
            r9 = r7
            goto L30
        L17:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1c:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L2b
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2b:
            r3 = r0[r9]
            r6 = r9
            r9 = r8
            r8 = r6
        L30:
            int r7 = r7 + r3
            int r8 = r8 + 1
            r3 = r4
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.c.t(int, short, byte, java.lang.Object[]):void");
    }

    private static void u(byte b, short s, int i2, Object[] objArr) {
        int i3 = (b * 2) + 4;
        int i4 = i2 + 99;
        int i5 = 1 - (s * 2);
        byte[] bArr = $$d;
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            i3++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i4 = i7 + i4;
            i7 = i7;
        }
        while (true) {
            int i8 = i6 + 1;
            bArr2[i8] = (byte) i4;
            if (i8 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            byte b2 = bArr[i3];
            i3++;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i8;
            i4 = b2 + i4;
            i7 = i7;
        }
    }

    public static c a() {
        int i2 = l + 1;
        k = i2 % 128;
        int i3 = i2 % 2;
        if (c == null) {
            c = new e();
            int i4 = k + 79;
            l = i4 % 128;
            int i5 = i4 % 2;
        }
        c cVar = c;
        int i6 = k + 51;
        l = i6 % 128;
        switch (i6 % 2 == 0 ? ']' : 'P') {
            case Opcodes.DUP2_X1 /* 93 */:
                int i7 = 76 / 0;
                return cVar;
            default:
                return cVar;
        }
    }

    protected c() {
    }

    public static String e() {
        Object obj;
        int i2 = k + 95;
        l = i2 % 128;
        switch (i2 % 2 == 0 ? '%' : 'Y') {
            case '%':
                Object[] objArr = new Object[1];
                q((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '+')), (-740865275) >>> (ViewConfiguration.getScrollDefaultDelay() / 6), (short) Color.alpha(1), 26 / (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (-1444073929) >>> ExpandableListView.getPackedPositionChild(1L), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                q((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), (ViewConfiguration.getScrollDefaultDelay() >> 16) - 740865275, (short) Color.alpha(0), (-42) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (-1444073929) - ExpandableListView.getPackedPositionChild(0L), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    public static String c() {
        int i2 = k + 31;
        l = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return Build.VERSION.RELEASE;
            default:
                String str = Build.VERSION.RELEASE;
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static String b(Context context) {
        switch (context == null ? '`' : 'L') {
            case Opcodes.IADD /* 96 */:
                int i2 = k + Opcodes.LREM;
                l = i2 % 128;
                int i3 = i2 % 2;
                return "";
            default:
                switch (Build.VERSION.SDK_INT >= 29 ? '=' : '`') {
                    case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                        Object[] objArr = new Object[1];
                        r(504573842 - View.MeasureSpec.makeMeasureSpec(0, 0), "⤹ɱ扱࿆ｖ茿묲浫䈷ꚪ熱ݕ⎁灇̣", (char) (26049 - View.resolveSizeAndState(0, 0, 0)), "鋗ጯ섞\uf365", "\u0000\u0000\u0000\u0000", objArr);
                        String intern = ((String) objArr[0]).intern();
                        int i4 = k + 71;
                        l = i4 % 128;
                        if (i4 % 2 != 0) {
                            return intern;
                        }
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        Object[] objArr2 = new Object[1];
                        r(TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 973794291, "行\ude15쪯梸\ud8de", (char) (51713 - View.combineMeasuredStates(0, 0)), "\u0c4f\uf514ǅۊ", "\u0000\u0000\u0000\u0000", objArr2);
                        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(((String) objArr2[0]).intern());
                        if (telephonyManager == null) {
                            int i5 = l + 43;
                            k = i5 % 128;
                            int i6 = i5 % 2;
                            g.c();
                            Object[] objArr3 = new Object[1];
                            q((byte) ((Process.getThreadPriority(0) + 20) >> 6), (-740865267) - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) ExpandableListView.getPackedPositionGroup(0L), (-44) - ((byte) KeyEvent.getModifierMetaStateMask()), (-1460851144) - Color.rgb(0, 0, 0), objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            r(1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "︡ⰱ팪ᣭ䈴듩뻄䟌葊珆ם﹣\u088a\uf0b7䬏䗛홇丶䵄崙걃ꇏ㇝\ud922䐠翕\u200f팁趗\ud935", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 45455), "갭\uebc8邹\u0ab1", "\u0000\u0000\u0000\u0000", objArr4);
                            g.d(intern2, ((String) objArr4[0]).intern());
                            return "";
                        }
                        String deviceId = telephonyManager.getDeviceId();
                        if (deviceId.length() != 14) {
                            return deviceId;
                        }
                        g.c();
                        Object[] objArr5 = new Object[1];
                        q((byte) (ViewConfiguration.getKeyRepeatDelay() >> 16), (-740865268) - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (short) (Process.myPid() >> 22), View.combineMeasuredStates(0, 0) - 43, (-1444073929) - TextUtils.lastIndexOf("", '0', 0), objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        q((byte) KeyEvent.normalizeMetaState(0), (-740865246) - TextUtils.indexOf("", ""), (short) TextUtils.getCapsMode("", 0, 0), (-43) - Color.blue(0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 1444073890, objArr6);
                        g.d(intern3, ((String) objArr6[0]).intern());
                        int length = deviceId.length();
                        int[] iArr = new int[length];
                        for (int i7 = 0; i7 < length; i7++) {
                            char charAt = deviceId.charAt(i7);
                            switch (!Character.isDigit(deviceId.charAt(i7))) {
                                case false:
                                    iArr[i7] = charAt - '0';
                                default:
                                    int i8 = l + 99;
                                    k = i8 % 128;
                                    if (i8 % 2 != 0) {
                                    }
                                    g.c();
                                    Object[] objArr7 = new Object[1];
                                    q((byte) KeyEvent.keyCodeFromString(""), (-740865269) - ExpandableListView.getPackedPositionChild(0L), (short) View.resolveSize(0, 0), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 42, (-1444073929) - ((byte) KeyEvent.getModifierMetaStateMask()), objArr7);
                                    String intern4 = ((String) objArr7[0]).intern();
                                    Object[] objArr8 = new Object[1];
                                    q((byte) TextUtils.indexOf("", ""), (-757642433) - Color.rgb(0, 0, 0), (short) (Color.rgb(0, 0, 0) + 16777216), (-42) - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), (-1444073889) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr8);
                                    g.e(intern4, ((String) objArr8[0]).intern());
                                    Object[] objArr9 = new Object[1];
                                    r(12226 - AndroidCharacter.getMirror('0'), "⤹ɱ扱࿆ｖ茿묲浫䈷ꚪ熱ݕ⎁灇̣", (char) (26049 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), "鋗ጯ섞\uf365", "\u0000\u0000\u0000\u0000", objArr9);
                                    return ((String) objArr9[0]).intern();
                            }
                        }
                        int b = o.b(iArr);
                        char c2 = (char) (b + 48);
                        g.c();
                        Object[] objArr10 = new Object[1];
                        q((byte) Color.blue(0), (-740865268) - TextUtils.indexOf("", "", 0, 0), (short) (Process.myPid() >> 22), (-43) - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ViewConfiguration.getKeyRepeatDelay() >> 16) - 1444073928, objArr10);
                        String intern5 = ((String) objArr10[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr11 = new Object[1];
                        r((-468739688) - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "٨읁*\ued11⅛⍥鍣㏆͂㑈国빧㖟\ueb48賺췔앨伩\bƀꕞ혡卹\uaa3a﹡镟櫇Ɑ铉係盀ꗀ㩣", (char) ((Process.getThreadPriority(0) + 20) >> 6), "鞢ྙ짤ᕒ", "\u0000\u0000\u0000\u0000", objArr11);
                        g.d(intern5, sb.append(((String) objArr11[0]).intern()).append(b).toString());
                        return new StringBuilder().append(deviceId).append(c2).toString();
                }
        }
    }

    public static boolean a(Context context) {
        switch (context == null ? '9' : (char) 18) {
            case 18:
                PackageManager packageManager = context.getPackageManager();
                Object[] objArr = new Object[1];
                r(ViewConfiguration.getScrollBarFadeDuration() >> 16, "川젠چ\uf8e2㟹Ӧ㖬܅㲠ﮨ䇾먼\ud891癨\ue429᪒綝\uf42a넛牞䇟䵤ꂳꛋ", (char) View.MeasureSpec.getMode(0), "響乗䂽徶", "\u0000\u0000\u0000\u0000", objArr);
                boolean hasSystemFeature = packageManager.hasSystemFeature(((String) objArr[0]).intern());
                int i2 = k + 13;
                l = i2 % 128;
                int i3 = i2 % 2;
                return hasSystemFeature;
            default:
                int i4 = l + 53;
                k = i4 % 128;
                int i5 = i4 % 2;
                return false;
        }
    }

    public static boolean e(Context context) {
        boolean z;
        switch (context == null ? (char) 0 : 'R') {
            case Opcodes.DASTORE /* 82 */:
                if (c(context) != null) {
                    int i2 = l + 5;
                    k = i2 % 128;
                    if (i2 % 2 == 0) {
                        z = false;
                    } else {
                        z = true;
                    }
                    switch (z) {
                        case false:
                            return true;
                        default:
                            return false;
                    }
                }
                int i3 = l + 97;
                k = i3 % 128;
                int i4 = i3 % 2;
                return false;
            default:
                int i5 = l + 99;
                k = i5 % 128;
                switch (i5 % 2 != 0 ? 'S' : 'C') {
                    case 'C':
                        return false;
                    default:
                        return true;
                }
        }
    }

    public static NfcAdapter c(Context context) {
        int i2 = l + 7;
        k = i2 % 128;
        int i3 = i2 % 2;
        NfcAdapter defaultAdapter = NfcAdapter.getDefaultAdapter(context);
        int i4 = l + 59;
        k = i4 % 128;
        int i5 = i4 % 2;
        return defaultAdapter;
    }

    public static String d(Context context) {
        int i2 = l + 71;
        k = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 4 : 'S') {
            case Opcodes.AASTORE /* 83 */:
                switch (context == null ? 'T' : '\\') {
                    case Opcodes.BASTORE /* 84 */:
                        return "";
                }
            default:
                int i3 = 43 / 0;
                switch (context == null ? ' ' : (char) 23) {
                    case 23:
                        break;
                    default:
                        return "";
                }
        }
        String e2 = o.e((CharSequence) context.getApplicationInfo().nativeLibraryDir);
        int i4 = l + 57;
        k = i4 % 128;
        int i5 = i4 % 2;
        return e2;
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x002c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static boolean i(android.content.Context r3) {
        /*
            r0 = 0
            if (r3 == 0) goto L49
            int r1 = o.ee.c.k
            int r1 = r1 + 105
            int r2 = r1 % 128
            o.ee.c.l = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L13
            r1 = 29
            goto L14
        L13:
            r1 = 5
        L14:
            switch(r1) {
                case 5: goto L1e;
                default: goto L17;
            }
        L17:
            int r1 = android.os.Build.VERSION.SDK_INT
            r2 = 123(0x7b, float:1.72E-43)
            if (r1 >= r2) goto L26
            goto L23
        L1e:
            switch(r0) {
                case 1: goto L49;
                default: goto L22;
            }
        L22:
            goto L2c
        L23:
            r1 = 67
            goto L28
        L26:
            r1 = 91
        L28:
            switch(r1) {
                case 67: goto L49;
                default: goto L2b;
            }
        L2b:
            goto L22
        L2c:
            java.lang.Class<android.hardware.fingerprint.FingerprintManager> r1 = android.hardware.fingerprint.FingerprintManager.class
            java.lang.Object r3 = r3.getSystemService(r1)
            android.hardware.fingerprint.FingerprintManager r3 = (android.hardware.fingerprint.FingerprintManager) r3
            if (r3 == 0) goto L48
            boolean r3 = r3.isHardwareDetected()
            if (r3 == 0) goto L48
            int r3 = o.ee.c.l
            int r3 = r3 + 109
            int r0 = r3 % 128
            o.ee.c.k = r0
            int r3 = r3 % 2
            r3 = 1
            return r3
        L48:
            return r0
        L49:
            int r3 = o.ee.c.k
            int r3 = r3 + 53
            int r1 = r3 % 128
            o.ee.c.l = r1
            int r3 = r3 % 2
            if (r3 != 0) goto L5c
            r3 = 34
            int r3 = r3 / r0
            return r0
        L5a:
            r3 = move-exception
            throw r3
        L5c:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.c.i(android.content.Context):boolean");
    }

    public static int f(Context context) throws TimeoutException {
        int i2 = l + 5;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int c2 = o.bj.a.a().c(context);
                int i3 = l + 109;
                k = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        return c2;
                    default:
                        int i4 = 50 / 0;
                        return c2;
                }
            default:
                o.bj.a.a().c(context);
                throw null;
        }
    }

    public static int d() {
        int i2 = k + Opcodes.DDIV;
        l = i2 % 128;
        int i3 = i2 % 2;
        int e2 = o.bj.a.a().e();
        int i4 = l + 91;
        k = i4 % 128;
        int i5 = i4 % 2;
        return e2;
    }

    public static boolean g(Context context) {
        int i2 = l + 61;
        k = i2 % 128;
        int i3 = i2 % 2;
        boolean d2 = o.d(context);
        int i4 = l + 61;
        k = i4 % 128;
        int i5 = i4 % 2;
        return d2;
    }

    public final boolean h(Context context) {
        boolean z;
        switch (this.b == null) {
            case true:
                try {
                    Object[] objArr = {context, 1};
                    Object obj = o.e.a.s.get(202025064);
                    if (obj == null) {
                        Class cls = (Class) o.e.a.c(12 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (char) ((-1) - TextUtils.lastIndexOf("", '0')), (ViewConfiguration.getScrollBarSize() >> 8) + 54);
                        byte b = (byte) 0;
                        byte b2 = b;
                        Object[] objArr2 = new Object[1];
                        t(b, b2, b2, objArr2);
                        obj = cls.getMethod((String) objArr2[0], Context.class, Integer.TYPE);
                        o.e.a.s.put(202025064, obj);
                    }
                    this.b = Integer.valueOf(((Integer) ((Method) obj).invoke(null, objArr)).intValue());
                    int i2 = k + 79;
                    l = i2 % 128;
                    int i3 = i2 % 2;
                    break;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause != null) {
                        throw cause;
                    }
                    throw th;
                }
        }
        switch (this.b.intValue() != 1) {
            case false:
                int i4 = k + 53;
                l = i4 % 128;
                int i5 = i4 % 2;
                z = false;
                break;
            default:
                int i6 = k + 53;
                l = i6 % 128;
                if (i6 % 2 == 0) {
                }
                z = true;
                break;
        }
        g.c();
        Object[] objArr3 = new Object[1];
        q((byte) ExpandableListView.getPackedPositionGroup(0L), (-740865268) - (ViewConfiguration.getEdgeSlop() >> 16), (short) Gravity.getAbsoluteGravity(0, 0), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) - 43, ImageFormat.getBitsPerPixel(0) - 1444073927, objArr3);
        String intern = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        r((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1, "名㋵묜墑䄒웾ᒈᖂ\ua6fd䍚릌㟑挭\u3130敨\ufdd8꓁믤ᬨҏ㹽鴍", (char) ((-1) - TextUtils.lastIndexOf("", '0', 0, 0)), "氪ꃃ賔ላ", "\u0000\u0000\u0000\u0000", objArr4);
        g.d(intern, sb.append(((String) objArr4[0]).intern()).append(z).toString());
        return z;
    }

    public static String j(Context context) {
        Object obj;
        int i2 = l + 35;
        k = i2 % 128;
        boolean z = i2 % 2 == 0;
        ContentResolver contentResolver = context.getContentResolver();
        switch (z) {
            case true:
                Object[] objArr = new Object[1];
                r(TextUtils.indexOf((CharSequence) "", '0', 0) + 1, "உꅊ\ue94c婈\ue3db퇱媖멢涞䛎", (char) TextUtils.getOffsetAfter("", 0), "ꤪ\uee5dᪿ\uefc9", "\u0000\u0000\u0000\u0000", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                r(TextUtils.indexOf((CharSequence) "", (char) 3, 0) * 1, "உꅊ\ue94c婈\ue3db퇱媖멢涞䛎", (char) TextUtils.getOffsetAfter("", 1), "ꤪ\uee5dᪿ\uefc9", "\u0000\u0000\u0000\u0000", objArr2);
                obj = objArr2[0];
                break;
        }
        return Settings.Secure.getString(contentResolver, ((String) obj).intern());
    }

    /* JADX WARN: Removed duplicated region for block: B:17:0x0075  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static byte[] m(android.content.Context r13) {
        /*
            Method dump skipped, instructions count: 272
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.c.m(android.content.Context):byte[]");
    }

    public static String[] b() {
        int i2 = l + 37;
        k = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return fr.antelop.sdk.b.e;
            default:
                String[] strArr = fr.antelop.sdk.b.e;
                throw null;
        }
    }

    public static String[] i() {
        int i2 = k + 81;
        l = i2 % 128;
        int i3 = i2 % 2;
        String[] strArr = fr.antelop.sdk.b.a;
        int i4 = k + 69;
        l = i4 % 128;
        int i5 = i4 % 2;
        return strArr;
    }

    public static String h() {
        int i2 = l + 55;
        k = i2 % 128;
        int i3 = i2 % 2;
        String str = Build.MODEL;
        int i4 = k + 65;
        l = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    private static byte[] e(Context context, String str) {
        Signature signature;
        Object obj = null;
        try {
            switch (Build.VERSION.SDK_INT >= 28) {
                case false:
                    PackageInfo packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 64);
                    if (packageInfo.signatures.length != 0) {
                        signature = packageInfo.signatures[0];
                        break;
                    } else {
                        g.c();
                        Object[] objArr = new Object[1];
                        q((byte) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), View.getDefaultSize(0, 0) - 740865268, (short) Color.alpha(0), (-43) - View.resolveSizeAndState(0, 0, 0), (-1444073929) - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        q((byte) TextUtils.indexOf("", "", 0, 0), ExpandableListView.getPackedPositionGroup(0L) - 740865183, (short) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getJumpTapTimeout() >> 16) - 43, ImageFormat.getBitsPerPixel(0) - 1444073889, objArr2);
                        g.e(intern, ((String) objArr2[0]).intern());
                        return null;
                    }
                default:
                    int i2 = l + 59;
                    k = i2 % 128;
                    if (i2 % 2 != 0) {
                    }
                    SigningInfo signingInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 134217728).signingInfo;
                    if (!signingInfo.hasMultipleSigners()) {
                        int length = signingInfo.getSigningCertificateHistory().length;
                        if (length != 0) {
                            signature = signingInfo.getSigningCertificateHistory()[length - 1];
                            break;
                        } else {
                            g.c();
                            Object[] objArr3 = new Object[1];
                            q((byte) View.MeasureSpec.getSize(0), (-740865269) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (short) (ViewConfiguration.getScrollBarSize() >> 8), (ViewConfiguration.getEdgeSlop() >> 16) - 43, (-1444073928) - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            q((byte) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), (-740865183) - TextUtils.indexOf("", ""), (short) ('0' - AndroidCharacter.getMirror('0')), (-43) - (ViewConfiguration.getScrollDefaultDelay() >> 16), (-1444073890) - Gravity.getAbsoluteGravity(0, 0), objArr4);
                            g.e(intern2, ((String) objArr4[0]).intern());
                            return null;
                        }
                    } else {
                        int i3 = l + 73;
                        k = i3 % 128;
                        int i4 = i3 % 2;
                        g.c();
                        Object[] objArr5 = new Object[1];
                        q((byte) (ViewConfiguration.getEdgeSlop() >> 16), ((Process.getThreadPriority(0) + 20) >> 6) - 740865268, (short) (ViewConfiguration.getTapTimeout() >> 16), TextUtils.getOffsetBefore("", 0) - 43, (ViewConfiguration.getScrollBarSize() >> 8) - 1444073928, objArr5);
                        String intern3 = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        r((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 1683268659, "\ufd4b䩳\ue9f0\uf77f氞颢\uf643㯢న텤ཾ誢і⾨껽甾䮾ꌚ뿫냬聢ḿ藊셉㝊끍ዓ㒽䮽櫨Ӵ\ue2ea〉푽䱧쏡ẻ佥庩咻⛧\ueb3eꆆ墛䦺靶ⰰ釤㮺˳\ue9e0촏\uaaca碣⟱", (char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 23269), "㍡咤\ue664퍚", "\u0000\u0000\u0000\u0000", objArr6);
                        g.e(intern3, ((String) objArr6[0]).intern());
                        return null;
                    }
            }
            byte[] c2 = o.ec.e.c(signature.toByteArray(), str);
            int i5 = l + 35;
            k = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    obj.hashCode();
                    throw null;
                default:
                    return c2;
            }
        } catch (PackageManager.NameNotFoundException e2) {
            g.c();
            Object[] objArr7 = new Object[1];
            q((byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), View.resolveSize(0, 0) - 740865268, (short) (ViewConfiguration.getScrollBarSize() >> 8), Process.getGidForName("") - 42, (-1444073928) - (KeyEvent.getMaxKeyCode() >> 16), objArr7);
            String intern4 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            r((KeyEvent.getMaxKeyCode() >> 16) - 1522279101, "ൢ⋎砖辷㋹롴\u0be2ᠣ懚\u2450媨ㄴ즗էᰈ\uf2a1Ⰼ\ue3bb룜㒫뉋䨻歈ᘜ㚕\uf67a䣛䄼\ue6b8맊\uecdd\uf12d䧈鬃鈍\u0898䣊켮൙挂䃩瑀쾡괞\uf621㟼蛼", (char) (ViewConfiguration.getWindowTouchSlop() >> 8), "䎑䏝ゥҚ", "\u0000\u0000\u0000\u0000", objArr8);
            g.a(intern4, ((String) objArr8[0]).intern(), e2);
            return null;
        }
    }

    public static String b(Context context, String str) {
        int i2 = l + Opcodes.DNEG;
        k = i2 % 128;
        int i3 = i2 % 2;
        byte[] e2 = e(context, str);
        Object[] objArr = new Object[1];
        q((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0')), AndroidCharacter.getMirror('0') - 46245, (short) View.MeasureSpec.makeMeasureSpec(0, 0), (-42) - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), KeyEvent.normalizeMetaState(0) - 1444073935, objArr);
        String e3 = o.dk.b.e(e2, ((String) objArr[0]).intern());
        int i4 = l + 43;
        k = i4 % 128;
        int i5 = i4 % 2;
        return e3;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:8:0x0031. Please report as an issue. */
    public static long j() {
        int i2 = k + 75;
        l = i2 % 128;
        int i3 = i2 % 2;
        long g2 = g();
        Long d2 = o.bp.b.b().d();
        switch (d2 != null ? (char) 15 : '0') {
            case 15:
                int i4 = k + 59;
                l = i4 % 128;
                switch (i4 % 2 == 0) {
                }
                return g2 + d2.longValue();
            default:
                return g2;
        }
    }

    private static long g() {
        long time = new Date().getTime() / 1000;
        int i2 = l + 43;
        k = i2 % 128;
        int i3 = i2 % 2;
        return time;
    }

    public static long o(Context context) {
        PackageInfo packageInfo;
        int i2 = l + 7;
        k = i2 % 128;
        try {
            switch (i2 % 2 == 0) {
                case false:
                    packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 1);
                    break;
                default:
                    packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
                    break;
            }
            return PackageInfoCompat.getLongVersionCode(packageInfo);
        } catch (PackageManager.NameNotFoundException e2) {
            g.c();
            Object[] objArr = new Object[1];
            q((byte) ExpandableListView.getPackedPositionType(0L), (-740865268) + (ViewConfiguration.getLongPressTimeout() >> 16), (short) (TextUtils.indexOf((CharSequence) "", '0') + 1), MotionEvent.axisFromString("") - 42, (-1444073928) - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            q((byte) KeyEvent.getDeadChar(0, 0), (-740865141) - ImageFormat.getBitsPerPixel(0), (short) (ViewConfiguration.getKeyRepeatDelay() >> 16), (-43) - (ViewConfiguration.getEdgeSlop() >> 16), (-1444073890) - KeyEvent.normalizeMetaState(0), objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e2);
            return 0L;
        }
    }

    public static int l(Context context) {
        int i2 = k + 45;
        l = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(context);
                throw null;
            default:
                return GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(context);
        }
    }

    public static int n(Context context) {
        int i2 = k + 85;
        l = i2 % 128;
        int i3 = i2 % 2;
        int isHuaweiMobileServicesAvailable = HuaweiApiAvailability.getInstance().isHuaweiMobileServicesAvailable(context);
        int i4 = l + 63;
        k = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return isHuaweiMobileServicesAvailable;
            default:
                throw null;
        }
    }

    public static o.ep.c k(Context context) {
        int i2 = l + 79;
        k = i2 % 128;
        int i3 = i2 % 2;
        o.eq.c b = o.eq.c.b(context);
        boolean z = false;
        switch (b == null) {
            case true:
                return o.eq.a.b(context);
            default:
                int i4 = l + 69;
                k = i4 % 128;
                if (i4 % 2 != 0) {
                    z = true;
                }
                switch (z) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return b;
                }
        }
    }

    public static o.ep.b p(Context context) {
        int i2 = k + 43;
        l = i2 % 128;
        int i3 = i2 % 2;
        fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b b = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.b.b(context);
        switch (b != null ? '\t' : (char) 5) {
            case 5:
                fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.a d2 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.a.d(context);
                int i4 = l + 7;
                k = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return d2;
                }
            default:
                int i5 = l + 51;
                k = i5 % 128;
                switch (i5 % 2 != 0) {
                    case true:
                        int i6 = 85 / 0;
                        return b;
                    default:
                        return b;
                }
        }
    }

    public static o.ep.b s(Context context) {
        int i2 = k + 55;
        l = i2 % 128;
        int i3 = i2 % 2;
        fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.e a2 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.e.a(context);
        int i4 = k + 85;
        l = i4 % 128;
        int i5 = i4 % 2;
        return a2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:33:0x002e, code lost:
    
        if (r12 == null) goto L11;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static java.lang.String c(android.content.Context r12, java.lang.String r13) {
        /*
            Method dump skipped, instructions count: 274
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.c.c(android.content.Context, java.lang.String):java.lang.String");
    }

    public final boolean equals(Object obj) {
        int i2 = k + 37;
        l = i2 % 128;
        int i3 = i2 % 2;
        boolean equals = super.equals(obj);
        int i4 = k + 61;
        l = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 31 : (char) 4) {
            case 31:
                int i5 = 26 / 0;
                return equals;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i2 = l + 79;
        k = i2 % 128;
        int i3 = i2 % 2;
        String obj = super.toString();
        int i4 = l + 47;
        k = i4 % 128;
        int i5 = i4 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i2 = k + 13;
        l = i2 % 128;
        char c2 = i2 % 2 == 0 ? '-' : ']';
        super.finalize();
        switch (c2) {
            case '-':
                int i3 = 61 / 0;
                break;
        }
        int i4 = l + 69;
        k = i4 % 128;
        int i5 = i4 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    private static void q(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        int i5;
        int length;
        byte[] bArr;
        int i6;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(e)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(TextUtils.lastIndexOf("", '0') + 12, (char) (ExpandableListView.getPackedPositionChild(0L) + 1), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 65);
                byte b2 = (byte) 0;
                byte b3 = b2;
                Object[] objArr3 = new Object[1];
                u(b2, b3, (byte) (b3 | 9), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z = intValue == -1;
            if (z) {
                int i7 = $11 + 59;
                int i8 = i7 % 128;
                $10 = i8;
                int i9 = i7 % 2;
                byte[] bArr2 = h;
                switch (bArr2 != null) {
                    case true:
                        int i10 = i8 + 87;
                        $11 = i10 % 128;
                        switch (i10 % 2 == 0) {
                            case true:
                                length = bArr2.length;
                                bArr = new byte[length];
                                i6 = 1;
                                break;
                            default:
                                length = bArr2.length;
                                bArr = new byte[length];
                                i6 = 0;
                                break;
                        }
                        while (i6 < length) {
                            try {
                                Object[] objArr4 = {Integer.valueOf(bArr2[i6])};
                                Object obj2 = o.e.a.s.get(494867332);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c((ViewConfiguration.getScrollBarSize() >> 8) + 19, (char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 16425), Color.red(0) + Opcodes.FCMPG);
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    u(b4, b5, (byte) (b5 | 11), objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(494867332, obj2);
                                }
                                bArr[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                i6++;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        bArr2 = bArr;
                    default:
                        if (bArr2 == null) {
                            intValue = (short) (((short) (f[i2 + ((int) (d ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L))));
                            break;
                        } else {
                            int i11 = $11 + 89;
                            $10 = i11 % 128;
                            int i12 = i11 % 2;
                            byte[] bArr3 = h;
                            try {
                                Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(d)};
                                Object obj3 = o.e.a.s.get(-2120899312);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getEdgeSlop() >> 16), (char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 65);
                                    byte b6 = (byte) 0;
                                    byte b7 = b6;
                                    Object[] objArr7 = new Object[1];
                                    u(b6, b7, (byte) (b7 | 9), objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                    o.e.a.s.put(-2120899312, obj3);
                                }
                                intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (e ^ (-5810760824076169584L))));
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        }
                }
            }
            if (intValue > 0) {
                int i13 = ((i2 + intValue) - 2) + ((int) (d ^ (-5810760824076169584L)));
                if (z) {
                    int i14 = $11 + 71;
                    $10 = i14 % 128;
                    int i15 = i14 % 2;
                    i5 = 1;
                } else {
                    i5 = 0;
                }
                fVar.d = i13 + i5;
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(a), sb};
                    Object obj4 = o.e.a.s.get(160906762);
                    if (obj4 == null) {
                        obj4 = ((Class) o.e.a.c(10 - TextUtils.lastIndexOf("", '0', 0), (char) View.resolveSize(0, 0), 603 - (ViewConfiguration.getDoubleTapTimeout() >> 16))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj4);
                    }
                    ((StringBuilder) ((Method) obj4).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = h;
                    if (bArr4 != null) {
                        int i16 = $10 + Opcodes.DMUL;
                        $11 = i16 % 128;
                        int i17 = i16 % 2;
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        for (int i18 = 0; i18 < length2; i18++) {
                            bArr5[i18] = (byte) (bArr4[i18] ^ (-5810760824076169584L));
                        }
                        bArr4 = bArr5;
                    }
                    boolean z2 = bArr4 != null;
                    fVar.c = 1;
                    int i19 = $11 + 57;
                    $10 = i19 % 128;
                    int i20 = i19 % 2;
                    while (true) {
                        switch (fVar.c >= intValue) {
                            case true:
                                break;
                            default:
                                if (z2) {
                                    int i21 = $10 + 91;
                                    $11 = i21 % 128;
                                    int i22 = i21 % 2;
                                    byte[] bArr6 = h;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                } else {
                                    short[] sArr = f;
                                    fVar.d = fVar.d - 1;
                                    fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                        }
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void r(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 666
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.c.r(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
