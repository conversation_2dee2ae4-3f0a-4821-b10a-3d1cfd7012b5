package com.google.android.gms.internal.auth;

import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import sun.misc.Unsafe;

/* compiled from: com.google.android.gms:play-services-auth-base@@18.0.4 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\auth\zzfz.smali */
final class zzfz<T> implements zzgh<T> {
    private static final int[] zza = new int[0];
    private static final Unsafe zzb = zzhi.zzg();
    private final int[] zzc;
    private final Object[] zzd;
    private final int zze;
    private final int zzf;
    private final zzfw zzg;
    private final boolean zzh;
    private final int[] zzi;
    private final int zzj;
    private final int zzk;
    private final zzfk zzl;
    private final zzgy zzm;
    private final zzel zzn;
    private final zzgb zzo;
    private final zzfr zzp;

    private zzfz(int[] iArr, Object[] objArr, int i, int i2, zzfw zzfwVar, boolean z, boolean z2, int[] iArr2, int i3, int i4, zzgb zzgbVar, zzfk zzfkVar, zzgy zzgyVar, zzel zzelVar, zzfr zzfrVar, byte[] bArr) {
        this.zzc = iArr;
        this.zzd = objArr;
        this.zze = i;
        this.zzf = i2;
        this.zzh = z;
        this.zzi = iArr2;
        this.zzj = i3;
        this.zzk = i4;
        this.zzo = zzgbVar;
        this.zzl = zzfkVar;
        this.zzm = zzgyVar;
        this.zzn = zzelVar;
        this.zzg = zzfwVar;
        this.zzp = zzfrVar;
    }

    private static Field zzA(Class cls, String str) {
        try {
            return cls.getDeclaredField(str);
        } catch (NoSuchFieldException e) {
            Field[] declaredFields = cls.getDeclaredFields();
            for (Field field : declaredFields) {
                if (str.equals(field.getName())) {
                    return field;
                }
            }
            throw new RuntimeException("Field " + str + " for " + cls.getName() + " not found. Known fields are " + Arrays.toString(declaredFields));
        }
    }

    private final void zzB(Object obj, Object obj2, int i) {
        long zzv = zzv(i) & 1048575;
        if (zzG(obj2, i)) {
            Object zzf = zzhi.zzf(obj, zzv);
            Object zzf2 = zzhi.zzf(obj2, zzv);
            if (zzf != null && zzf2 != null) {
                zzhi.zzp(obj, zzv, zzez.zzg(zzf, zzf2));
                zzD(obj, i);
            } else if (zzf2 != null) {
                zzhi.zzp(obj, zzv, zzf2);
                zzD(obj, i);
            }
        }
    }

    private final void zzC(Object obj, Object obj2, int i) {
        int zzv = zzv(i);
        int i2 = this.zzc[i];
        long j = zzv & 1048575;
        if (zzJ(obj2, i2, i)) {
            Object zzf = zzJ(obj, i2, i) ? zzhi.zzf(obj, j) : null;
            Object zzf2 = zzhi.zzf(obj2, j);
            if (zzf != null && zzf2 != null) {
                zzhi.zzp(obj, j, zzez.zzg(zzf, zzf2));
                zzE(obj, i2, i);
            } else if (zzf2 != null) {
                zzhi.zzp(obj, j, zzf2);
                zzE(obj, i2, i);
            }
        }
    }

    private final void zzD(Object obj, int i) {
        int zzs = zzs(i);
        long j = 1048575 & zzs;
        if (j == 1048575) {
            return;
        }
        zzhi.zzn(obj, j, (1 << (zzs >>> 20)) | zzhi.zzc(obj, j));
    }

    private final void zzE(Object obj, int i, int i2) {
        zzhi.zzn(obj, zzs(i2) & 1048575, i);
    }

    private final boolean zzF(Object obj, Object obj2, int i) {
        return zzG(obj, i) == zzG(obj2, i);
    }

    private final boolean zzG(Object obj, int i) {
        int zzs = zzs(i);
        long j = zzs & 1048575;
        if (j != 1048575) {
            return (zzhi.zzc(obj, j) & (1 << (zzs >>> 20))) != 0;
        }
        int zzv = zzv(i);
        long j2 = zzv & 1048575;
        switch (zzu(zzv)) {
            case 0:
                return Double.doubleToRawLongBits(zzhi.zza(obj, j2)) != 0;
            case 1:
                return Float.floatToRawIntBits(zzhi.zzb(obj, j2)) != 0;
            case 2:
                return zzhi.zzd(obj, j2) != 0;
            case 3:
                return zzhi.zzd(obj, j2) != 0;
            case 4:
                return zzhi.zzc(obj, j2) != 0;
            case 5:
                return zzhi.zzd(obj, j2) != 0;
            case 6:
                return zzhi.zzc(obj, j2) != 0;
            case 7:
                return zzhi.zzt(obj, j2);
            case 8:
                Object zzf = zzhi.zzf(obj, j2);
                if (zzf instanceof String) {
                    return !((String) zzf).isEmpty();
                }
                if (zzf instanceof zzee) {
                    return !zzee.zzb.equals(zzf);
                }
                throw new IllegalArgumentException();
            case 9:
                return zzhi.zzf(obj, j2) != null;
            case 10:
                return !zzee.zzb.equals(zzhi.zzf(obj, j2));
            case 11:
                return zzhi.zzc(obj, j2) != 0;
            case 12:
                return zzhi.zzc(obj, j2) != 0;
            case 13:
                return zzhi.zzc(obj, j2) != 0;
            case 14:
                return zzhi.zzd(obj, j2) != 0;
            case 15:
                return zzhi.zzc(obj, j2) != 0;
            case 16:
                return zzhi.zzd(obj, j2) != 0;
            case 17:
                return zzhi.zzf(obj, j2) != null;
            default:
                throw new IllegalArgumentException();
        }
    }

    private final boolean zzH(Object obj, int i, int i2, int i3, int i4) {
        return i2 == 1048575 ? zzG(obj, i) : (i3 & i4) != 0;
    }

    private static boolean zzI(Object obj, int i, zzgh zzghVar) {
        return zzghVar.zzi(zzhi.zzf(obj, i & 1048575));
    }

    private final boolean zzJ(Object obj, int i, int i2) {
        return zzhi.zzc(obj, (long) (zzs(i2) & 1048575)) == i;
    }

    static zzgz zzc(Object obj) {
        zzeu zzeuVar = (zzeu) obj;
        zzgz zzgzVar = zzeuVar.zzc;
        if (zzgzVar != zzgz.zza()) {
            return zzgzVar;
        }
        zzgz zzc = zzgz.zzc();
        zzeuVar.zzc = zzc;
        return zzc;
    }

    static zzfz zzj(Class cls, zzft zzftVar, zzgb zzgbVar, zzfk zzfkVar, zzgy zzgyVar, zzel zzelVar, zzfr zzfrVar) {
        if (zzftVar instanceof zzgg) {
            return zzk((zzgg) zzftVar, zzgbVar, zzfkVar, zzgyVar, zzelVar, zzfrVar);
        }
        throw null;
    }

    static zzfz zzk(zzgg zzggVar, zzgb zzgbVar, zzfk zzfkVar, zzgy zzgyVar, zzel zzelVar, zzfr zzfrVar) {
        int i;
        int charAt;
        int charAt2;
        int charAt3;
        int[] iArr;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        char charAt4;
        int i7;
        char charAt5;
        int i8;
        char charAt6;
        int i9;
        char charAt7;
        int i10;
        char charAt8;
        int i11;
        char charAt9;
        int i12;
        char charAt10;
        int i13;
        char charAt11;
        int i14;
        int i15;
        int i16;
        int[] iArr2;
        int i17;
        int i18;
        int i19;
        int objectFieldOffset;
        Object[] objArr;
        String str;
        int i20;
        int i21;
        int i22;
        int i23;
        Field zzA;
        char charAt12;
        int i24;
        Field zzA2;
        Field zzA3;
        int i25;
        char charAt13;
        int i26;
        char charAt14;
        int i27;
        char charAt15;
        int i28;
        char charAt16;
        boolean z = zzggVar.zzc() == 2;
        String zzd = zzggVar.zzd();
        int length = zzd.length();
        char c = 55296;
        if (zzd.charAt(0) >= 55296) {
            int i29 = 1;
            while (true) {
                i = i29 + 1;
                if (zzd.charAt(i29) < 55296) {
                    break;
                }
                i29 = i;
            }
        } else {
            i = 1;
        }
        int i30 = i + 1;
        int charAt17 = zzd.charAt(i);
        if (charAt17 >= 55296) {
            int i31 = charAt17 & 8191;
            int i32 = 13;
            while (true) {
                i28 = i30 + 1;
                charAt16 = zzd.charAt(i30);
                if (charAt16 < 55296) {
                    break;
                }
                i31 |= (charAt16 & 8191) << i32;
                i32 += 13;
                i30 = i28;
            }
            charAt17 = i31 | (charAt16 << i32);
            i30 = i28;
        }
        if (charAt17 == 0) {
            charAt = 0;
            i5 = 0;
            charAt2 = 0;
            i4 = 0;
            charAt3 = 0;
            i2 = 0;
            iArr = zza;
            i3 = 0;
        } else {
            int i33 = i30 + 1;
            int charAt18 = zzd.charAt(i30);
            if (charAt18 >= 55296) {
                int i34 = charAt18 & 8191;
                int i35 = 13;
                while (true) {
                    i13 = i33 + 1;
                    charAt11 = zzd.charAt(i33);
                    if (charAt11 < 55296) {
                        break;
                    }
                    i34 |= (charAt11 & 8191) << i35;
                    i35 += 13;
                    i33 = i13;
                }
                charAt18 = i34 | (charAt11 << i35);
                i33 = i13;
            }
            int i36 = i33 + 1;
            int charAt19 = zzd.charAt(i33);
            if (charAt19 >= 55296) {
                int i37 = charAt19 & 8191;
                int i38 = 13;
                while (true) {
                    i12 = i36 + 1;
                    charAt10 = zzd.charAt(i36);
                    if (charAt10 < 55296) {
                        break;
                    }
                    i37 |= (charAt10 & 8191) << i38;
                    i38 += 13;
                    i36 = i12;
                }
                charAt19 = i37 | (charAt10 << i38);
                i36 = i12;
            }
            int i39 = i36 + 1;
            charAt = zzd.charAt(i36);
            if (charAt >= 55296) {
                int i40 = charAt & 8191;
                int i41 = 13;
                while (true) {
                    i11 = i39 + 1;
                    charAt9 = zzd.charAt(i39);
                    if (charAt9 < 55296) {
                        break;
                    }
                    i40 |= (charAt9 & 8191) << i41;
                    i41 += 13;
                    i39 = i11;
                }
                charAt = i40 | (charAt9 << i41);
                i39 = i11;
            }
            int i42 = i39 + 1;
            int charAt20 = zzd.charAt(i39);
            if (charAt20 >= 55296) {
                int i43 = charAt20 & 8191;
                int i44 = 13;
                while (true) {
                    i10 = i42 + 1;
                    charAt8 = zzd.charAt(i42);
                    if (charAt8 < 55296) {
                        break;
                    }
                    i43 |= (charAt8 & 8191) << i44;
                    i44 += 13;
                    i42 = i10;
                }
                charAt20 = i43 | (charAt8 << i44);
                i42 = i10;
            }
            int i45 = i42 + 1;
            charAt2 = zzd.charAt(i42);
            if (charAt2 >= 55296) {
                int i46 = charAt2 & 8191;
                int i47 = 13;
                while (true) {
                    i9 = i45 + 1;
                    charAt7 = zzd.charAt(i45);
                    if (charAt7 < 55296) {
                        break;
                    }
                    i46 |= (charAt7 & 8191) << i47;
                    i47 += 13;
                    i45 = i9;
                }
                charAt2 = i46 | (charAt7 << i47);
                i45 = i9;
            }
            int i48 = i45 + 1;
            int charAt21 = zzd.charAt(i45);
            if (charAt21 >= 55296) {
                int i49 = charAt21 & 8191;
                int i50 = 13;
                while (true) {
                    i8 = i48 + 1;
                    charAt6 = zzd.charAt(i48);
                    if (charAt6 < 55296) {
                        break;
                    }
                    i49 |= (charAt6 & 8191) << i50;
                    i50 += 13;
                    i48 = i8;
                }
                charAt21 = i49 | (charAt6 << i50);
                i48 = i8;
            }
            int i51 = i48 + 1;
            int charAt22 = zzd.charAt(i48);
            if (charAt22 >= 55296) {
                int i52 = charAt22 & 8191;
                int i53 = 13;
                while (true) {
                    i7 = i51 + 1;
                    charAt5 = zzd.charAt(i51);
                    if (charAt5 < 55296) {
                        break;
                    }
                    i52 |= (charAt5 & 8191) << i53;
                    i53 += 13;
                    i51 = i7;
                }
                charAt22 = i52 | (charAt5 << i53);
                i51 = i7;
            }
            int i54 = i51 + 1;
            charAt3 = zzd.charAt(i51);
            if (charAt3 >= 55296) {
                int i55 = charAt3 & 8191;
                int i56 = 13;
                while (true) {
                    i6 = i54 + 1;
                    charAt4 = zzd.charAt(i54);
                    if (charAt4 < 55296) {
                        break;
                    }
                    i55 |= (charAt4 & 8191) << i56;
                    i56 += 13;
                    i54 = i6;
                }
                charAt3 = i55 | (charAt4 << i56);
                i54 = i6;
            }
            iArr = new int[charAt3 + charAt21 + charAt22];
            i2 = charAt18 + charAt18 + charAt19;
            i3 = charAt18;
            i30 = i54;
            int i57 = charAt21;
            i4 = charAt20;
            i5 = i57;
        }
        Unsafe unsafe = zzb;
        Object[] zze = zzggVar.zze();
        Class<?> cls = zzggVar.zza().getClass();
        int[] iArr3 = new int[charAt2 * 3];
        Object[] objArr2 = new Object[charAt2 + charAt2];
        int i58 = charAt3 + i5;
        int i59 = charAt3;
        int i60 = i58;
        int i61 = 0;
        int i62 = 0;
        while (i30 < length) {
            int i63 = i30 + 1;
            int charAt23 = zzd.charAt(i30);
            if (charAt23 >= c) {
                int i64 = charAt23 & 8191;
                int i65 = i63;
                int i66 = 13;
                while (true) {
                    i27 = i65 + 1;
                    charAt15 = zzd.charAt(i65);
                    if (charAt15 < c) {
                        break;
                    }
                    i64 |= (charAt15 & 8191) << i66;
                    i66 += 13;
                    i65 = i27;
                }
                charAt23 = i64 | (charAt15 << i66);
                i14 = i27;
            } else {
                i14 = i63;
            }
            int i67 = i14 + 1;
            int charAt24 = zzd.charAt(i14);
            if (charAt24 >= c) {
                int i68 = charAt24 & 8191;
                int i69 = i67;
                int i70 = 13;
                while (true) {
                    i26 = i69 + 1;
                    charAt14 = zzd.charAt(i69);
                    i15 = length;
                    if (charAt14 < 55296) {
                        break;
                    }
                    i68 |= (charAt14 & 8191) << i70;
                    i70 += 13;
                    i69 = i26;
                    length = i15;
                }
                charAt24 = i68 | (charAt14 << i70);
                i16 = i26;
            } else {
                i15 = length;
                i16 = i67;
            }
            int i71 = charAt24 & 255;
            int i72 = charAt3;
            if ((charAt24 & 1024) != 0) {
                iArr[i62] = i61;
                i62++;
            }
            if (i71 >= 51) {
                int i73 = i16 + 1;
                int charAt25 = zzd.charAt(i16);
                if (charAt25 >= 55296) {
                    int i74 = charAt25 & 8191;
                    int i75 = i73;
                    int i76 = 13;
                    while (true) {
                        i25 = i75 + 1;
                        charAt13 = zzd.charAt(i75);
                        i18 = i4;
                        if (charAt13 < 55296) {
                            break;
                        }
                        i74 |= (charAt13 & 8191) << i76;
                        i76 += 13;
                        i75 = i25;
                        i4 = i18;
                    }
                    charAt25 = i74 | (charAt13 << i76);
                    i24 = i25;
                } else {
                    i18 = i4;
                    i24 = i73;
                }
                int i77 = i71 - 51;
                i22 = i24;
                if (i77 == 9 || i77 == 17) {
                    int i78 = i61 / 3;
                    objArr2[i78 + i78 + 1] = zze[i2];
                    i2++;
                } else if (i77 == 12 && !z) {
                    int i79 = i61 / 3;
                    objArr2[i79 + i79 + 1] = zze[i2];
                    i2++;
                }
                int i80 = charAt25 + charAt25;
                Object obj = zze[i80];
                if (obj instanceof Field) {
                    zzA2 = (Field) obj;
                } else {
                    zzA2 = zzA(cls, (String) obj);
                    zze[i80] = zzA2;
                }
                iArr2 = iArr3;
                i17 = charAt;
                int objectFieldOffset2 = (int) unsafe.objectFieldOffset(zzA2);
                int i81 = i80 + 1;
                Object obj2 = zze[i81];
                if (obj2 instanceof Field) {
                    zzA3 = (Field) obj2;
                } else {
                    zzA3 = zzA(cls, (String) obj2);
                    zze[i81] = zzA3;
                }
                str = zzd;
                i21 = (int) unsafe.objectFieldOffset(zzA3);
                objArr = objArr2;
                objectFieldOffset = objectFieldOffset2;
                i23 = 0;
            } else {
                iArr2 = iArr3;
                i17 = charAt;
                i18 = i4;
                int i82 = i2 + 1;
                Field zzA4 = zzA(cls, (String) zze[i2]);
                if (i71 != 9 && i71 != 17) {
                    if (i71 == 27 || i71 == 49) {
                        int i83 = i61 / 3;
                        objArr2[i83 + i83 + 1] = zze[i82];
                        i82++;
                    } else if (i71 == 12 || i71 == 30 || i71 == 44) {
                        if (!z) {
                            int i84 = i61 / 3;
                            objArr2[i84 + i84 + 1] = zze[i82];
                            i82++;
                        }
                    } else if (i71 == 50) {
                        int i85 = i59 + 1;
                        iArr[i59] = i61;
                        int i86 = i61 / 3;
                        int i87 = i86 + i86;
                        int i88 = i82 + 1;
                        objArr2[i87] = zze[i82];
                        if ((charAt24 & 2048) != 0) {
                            i82 = i88 + 1;
                            objArr2[i87 + 1] = zze[i88];
                            i59 = i85;
                        } else {
                            i59 = i85;
                            i82 = i88;
                        }
                    }
                    i19 = i82;
                    objectFieldOffset = (int) unsafe.objectFieldOffset(zzA4);
                    objArr = objArr2;
                    if ((charAt24 & 4096) == 4096 || i71 > 17) {
                        str = zzd;
                        i20 = i19;
                        i21 = 1048575;
                        i22 = i16;
                        i23 = 0;
                    } else {
                        int i89 = i16 + 1;
                        int charAt26 = zzd.charAt(i16);
                        if (charAt26 >= 55296) {
                            int i90 = charAt26 & 8191;
                            int i91 = 13;
                            while (true) {
                                i22 = i89 + 1;
                                charAt12 = zzd.charAt(i89);
                                if (charAt12 < 55296) {
                                    break;
                                }
                                i90 |= (charAt12 & 8191) << i91;
                                i91 += 13;
                                i89 = i22;
                            }
                            charAt26 = i90 | (charAt12 << i91);
                        } else {
                            i22 = i89;
                        }
                        int i92 = i3 + i3 + (charAt26 / 32);
                        Object obj3 = zze[i92];
                        str = zzd;
                        if (obj3 instanceof Field) {
                            zzA = (Field) obj3;
                        } else {
                            zzA = zzA(cls, (String) obj3);
                            zze[i92] = zzA;
                        }
                        i20 = i19;
                        i21 = (int) unsafe.objectFieldOffset(zzA);
                        i23 = charAt26 % 32;
                    }
                    if (i71 >= 18 || i71 > 49) {
                        i2 = i20;
                    } else {
                        iArr[i60] = objectFieldOffset;
                        i2 = i20;
                        i60++;
                    }
                }
                int i93 = i61 / 3;
                objArr2[i93 + i93 + 1] = zzA4.getType();
                i19 = i82;
                objectFieldOffset = (int) unsafe.objectFieldOffset(zzA4);
                objArr = objArr2;
                if ((charAt24 & 4096) == 4096) {
                }
                str = zzd;
                i20 = i19;
                i21 = 1048575;
                i22 = i16;
                i23 = 0;
                if (i71 >= 18) {
                }
                i2 = i20;
            }
            int i94 = i61 + 1;
            iArr2[i61] = charAt23;
            int i95 = i94 + 1;
            iArr2[i94] = ((charAt24 & 256) != 0 ? 268435456 : 0) | ((charAt24 & 512) != 0 ? 536870912 : 0) | (i71 << 20) | objectFieldOffset;
            i61 = i95 + 1;
            iArr2[i95] = (i23 << 20) | i21;
            charAt = i17;
            charAt3 = i72;
            i30 = i22;
            length = i15;
            objArr2 = objArr;
            zzd = str;
            iArr3 = iArr2;
            i4 = i18;
            c = 55296;
        }
        return new zzfz(iArr3, objArr2, charAt, i4, zzggVar.zza(), z, false, iArr, charAt3, i58, zzgbVar, zzfkVar, zzgyVar, zzelVar, zzfrVar, null);
    }

    private static int zzl(Object obj, long j) {
        return ((Integer) zzhi.zzf(obj, j)).intValue();
    }

    private final int zzm(Object obj, byte[] bArr, int i, int i2, int i3, long j, zzds zzdsVar) throws IOException {
        Unsafe unsafe = zzb;
        Object zzz = zzz(i3);
        Object object = unsafe.getObject(obj, j);
        if (!((zzfq) object).zze()) {
            zzfq zzb2 = zzfq.zza().zzb();
            zzfr.zza(zzb2, object);
            unsafe.putObject(obj, j, zzb2);
        }
        throw null;
    }

    private final int zzn(Object obj, byte[] bArr, int i, int i2, int i3, int i4, int i5, int i6, int i7, long j, int i8, zzds zzdsVar) throws IOException {
        Unsafe unsafe = zzb;
        long j2 = this.zzc[i8 + 2] & 1048575;
        switch (i7) {
            case 51:
                if (i5 == 1) {
                    unsafe.putObject(obj, j, Double.valueOf(Double.longBitsToDouble(zzdt.zzn(bArr, i))));
                    unsafe.putInt(obj, j2, i4);
                    return i + 8;
                }
                break;
            case 52:
                if (i5 == 5) {
                    unsafe.putObject(obj, j, Float.valueOf(Float.intBitsToFloat(zzdt.zzb(bArr, i))));
                    unsafe.putInt(obj, j2, i4);
                    return i + 4;
                }
                break;
            case Opcodes.SALOAD /* 53 */:
            case Opcodes.ISTORE /* 54 */:
                if (i5 == 0) {
                    int zzm = zzdt.zzm(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, Long.valueOf(zzdsVar.zzb));
                    unsafe.putInt(obj, j2, i4);
                    return zzm;
                }
                break;
            case 55:
            case 62:
                if (i5 == 0) {
                    int zzj = zzdt.zzj(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, Integer.valueOf(zzdsVar.zza));
                    unsafe.putInt(obj, j2, i4);
                    return zzj;
                }
                break;
            case 56:
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                if (i5 == 1) {
                    unsafe.putObject(obj, j, Long.valueOf(zzdt.zzn(bArr, i)));
                    unsafe.putInt(obj, j2, i4);
                    return i + 8;
                }
                break;
            case 57:
            case 64:
                if (i5 == 5) {
                    unsafe.putObject(obj, j, Integer.valueOf(zzdt.zzb(bArr, i)));
                    unsafe.putInt(obj, j2, i4);
                    return i + 4;
                }
                break;
            case Opcodes.ASTORE /* 58 */:
                if (i5 == 0) {
                    int zzm2 = zzdt.zzm(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, Boolean.valueOf(zzdsVar.zzb != 0));
                    unsafe.putInt(obj, j2, i4);
                    return zzm2;
                }
                break;
            case 59:
                if (i5 == 2) {
                    int zzj2 = zzdt.zzj(bArr, i, zzdsVar);
                    int i9 = zzdsVar.zza;
                    if (i9 == 0) {
                        unsafe.putObject(obj, j, "");
                    } else {
                        if ((i6 & 536870912) != 0 && !zzhm.zzd(bArr, zzj2, zzj2 + i9)) {
                            throw zzfa.zzb();
                        }
                        unsafe.putObject(obj, j, new String(bArr, zzj2, i9, zzez.zzb));
                        zzj2 += i9;
                    }
                    unsafe.putInt(obj, j2, i4);
                    return zzj2;
                }
                break;
            case 60:
                if (i5 == 2) {
                    int zzd = zzdt.zzd(zzy(i8), bArr, i, i2, zzdsVar);
                    Object object = unsafe.getInt(obj, j2) == i4 ? unsafe.getObject(obj, j) : null;
                    if (object == null) {
                        unsafe.putObject(obj, j, zzdsVar.zzc);
                    } else {
                        unsafe.putObject(obj, j, zzez.zzg(object, zzdsVar.zzc));
                    }
                    unsafe.putInt(obj, j2, i4);
                    return zzd;
                }
                break;
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                if (i5 == 2) {
                    int zza2 = zzdt.zza(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, zzdsVar.zzc);
                    unsafe.putInt(obj, j2, i4);
                    return zza2;
                }
                break;
            case 63:
                if (i5 == 0) {
                    int zzj3 = zzdt.zzj(bArr, i, zzdsVar);
                    int i10 = zzdsVar.zza;
                    zzex zzx = zzx(i8);
                    if (zzx == null || zzx.zza()) {
                        unsafe.putObject(obj, j, Integer.valueOf(i10));
                        unsafe.putInt(obj, j2, i4);
                    } else {
                        zzc(obj).zzf(i3, Long.valueOf(i10));
                    }
                    return zzj3;
                }
                break;
            case 66:
                if (i5 == 0) {
                    int zzj4 = zzdt.zzj(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, Integer.valueOf(zzei.zzb(zzdsVar.zza)));
                    unsafe.putInt(obj, j2, i4);
                    return zzj4;
                }
                break;
            case 67:
                if (i5 == 0) {
                    int zzm3 = zzdt.zzm(bArr, i, zzdsVar);
                    unsafe.putObject(obj, j, Long.valueOf(zzei.zzc(zzdsVar.zzb)));
                    unsafe.putInt(obj, j2, i4);
                    return zzm3;
                }
                break;
            case 68:
                if (i5 == 3) {
                    int zzc = zzdt.zzc(zzy(i8), bArr, i, i2, (i3 & (-8)) | 4, zzdsVar);
                    Object object2 = unsafe.getInt(obj, j2) == i4 ? unsafe.getObject(obj, j) : null;
                    if (object2 == null) {
                        unsafe.putObject(obj, j, zzdsVar.zzc);
                    } else {
                        unsafe.putObject(obj, j, zzez.zzg(object2, zzdsVar.zzc));
                    }
                    unsafe.putInt(obj, j2, i4);
                    return zzc;
                }
                break;
        }
        return i;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r3v23, types: [int] */
    private final int zzo(Object obj, byte[] bArr, int i, int i2, zzds zzdsVar) throws IOException {
        byte b;
        int i3;
        int i4;
        int i5;
        int i6;
        Unsafe unsafe;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        zzfz<T> zzfzVar = this;
        Object obj2 = obj;
        byte[] bArr2 = bArr;
        int i13 = i2;
        zzds zzdsVar2 = zzdsVar;
        Unsafe unsafe2 = zzb;
        int i14 = 1048575;
        int i15 = -1;
        int i16 = i;
        int i17 = -1;
        int i18 = 1048575;
        int i19 = 0;
        int i20 = 0;
        while (i16 < i13) {
            int i21 = i16 + 1;
            byte b2 = bArr2[i16];
            if (b2 < 0) {
                i3 = zzdt.zzk(b2, bArr2, i21, zzdsVar2);
                b = zzdsVar2.zza;
            } else {
                b = b2;
                i3 = i21;
            }
            int i22 = b >>> 3;
            int i23 = b & 7;
            int zzr = i22 > i17 ? zzfzVar.zzr(i22, i19 / 3) : zzfzVar.zzq(i22);
            if (zzr == i15) {
                i4 = i3;
                i5 = i22;
                i6 = i15;
                unsafe = unsafe2;
                i7 = 0;
            } else {
                int[] iArr = zzfzVar.zzc;
                int i24 = iArr[zzr + 1];
                int zzu = zzu(i24);
                long j = i24 & i14;
                if (zzu <= 17) {
                    int i25 = iArr[zzr + 2];
                    int i26 = 1 << (i25 >>> 20);
                    int i27 = i25 & 1048575;
                    if (i27 != i18) {
                        if (i18 != 1048575) {
                            unsafe2.putInt(obj2, i18, i20);
                        }
                        if (i27 != 1048575) {
                            i20 = unsafe2.getInt(obj2, i27);
                        }
                        i18 = i27;
                    }
                    switch (zzu) {
                        case 0:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i9 = i3;
                            i5 = i22;
                            if (i23 != 1) {
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                zzhi.zzl(obj2, j, Double.longBitsToDouble(zzdt.zzn(bArr2, i9)));
                                i16 = i9 + 8;
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 1:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i9 = i3;
                            i5 = i22;
                            if (i23 != 5) {
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                zzhi.zzm(obj2, j, Float.intBitsToFloat(zzdt.zzb(bArr2, i9)));
                                i16 = i9 + 4;
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 2:
                        case 3:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i9 = i3;
                            i5 = i22;
                            if (i23 != 0) {
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                int zzm = zzdt.zzm(bArr2, i9, zzdsVar2);
                                unsafe2.putLong(obj, j, zzdsVar2.zzb);
                                i20 |= i26;
                                i16 = zzm;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 4:
                        case 11:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i9 = i3;
                            i5 = i22;
                            if (i23 != 0) {
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                i16 = zzdt.zzj(bArr2, i9, zzdsVar2);
                                unsafe2.putInt(obj2, j, zzdsVar2.zza);
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 5:
                        case 14:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 1) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                unsafe2.putLong(obj, j, zzdt.zzn(bArr2, i3));
                                i16 = i3 + 8;
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 6:
                        case 13:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 5) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                unsafe2.putInt(obj2, j, zzdt.zzb(bArr2, i3));
                                i16 = i3 + 4;
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 7:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 0) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                i16 = zzdt.zzm(bArr2, i3, zzdsVar2);
                                zzhi.zzk(obj2, j, zzdsVar2.zzb != 0);
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 8:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 2) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                i16 = (536870912 & i24) == 0 ? zzdt.zzg(bArr2, i3, zzdsVar2) : zzdt.zzh(bArr2, i3, zzdsVar2);
                                unsafe2.putObject(obj2, j, zzdsVar2.zzc);
                                i20 |= i26;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 9:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 2) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                int zzd = zzdt.zzd(zzfzVar.zzy(i8), bArr2, i3, i13, zzdsVar2);
                                Object object = unsafe2.getObject(obj2, j);
                                if (object == null) {
                                    unsafe2.putObject(obj2, j, zzdsVar2.zzc);
                                } else {
                                    unsafe2.putObject(obj2, j, zzez.zzg(object, zzdsVar2.zzc));
                                }
                                i20 |= i26;
                                i16 = zzd;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 10:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 2) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                int zza2 = zzdt.zza(bArr2, i3, zzdsVar2);
                                unsafe2.putObject(obj2, j, zzdsVar2.zzc);
                                i20 |= i26;
                                i16 = zza2;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 12:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 0) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                int zzj = zzdt.zzj(bArr2, i3, zzdsVar2);
                                unsafe2.putInt(obj2, j, zzdsVar2.zza);
                                i20 |= i26;
                                i16 = zzj;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 15:
                            zzdsVar2 = zzdsVar;
                            i8 = zzr;
                            i5 = i22;
                            if (i23 != 0) {
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                int zzj2 = zzdt.zzj(bArr2, i3, zzdsVar2);
                                unsafe2.putInt(obj2, j, zzei.zzb(zzdsVar2.zza));
                                i20 |= i26;
                                i16 = zzj2;
                                i19 = i8;
                                i17 = i5;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        case 16:
                            if (i23 != 0) {
                                i5 = i22;
                                i8 = zzr;
                                i9 = i3;
                                i4 = i9;
                                unsafe = unsafe2;
                                i7 = i8;
                                i6 = -1;
                                break;
                            } else {
                                zzdsVar2 = zzdsVar;
                                int zzm2 = zzdt.zzm(bArr2, i3, zzdsVar2);
                                unsafe2.putLong(obj, j, zzei.zzc(zzdsVar2.zzb));
                                i20 |= i26;
                                i16 = zzm2;
                                i19 = zzr;
                                i17 = i22;
                                i14 = 1048575;
                                i15 = -1;
                                break;
                            }
                        default:
                            i5 = i22;
                            i8 = zzr;
                            i9 = i3;
                            i4 = i9;
                            unsafe = unsafe2;
                            i7 = i8;
                            i6 = -1;
                            break;
                    }
                } else {
                    zzdsVar2 = zzdsVar;
                    int i28 = zzr;
                    int i29 = i3;
                    i5 = i22;
                    if (zzu == 27) {
                        if (i23 == 2) {
                            zzey zzeyVar = (zzey) unsafe2.getObject(obj2, j);
                            if (!zzeyVar.zzc()) {
                                int size = zzeyVar.size();
                                zzeyVar = zzeyVar.zzd(size == 0 ? 10 : size + size);
                                unsafe2.putObject(obj2, j, zzeyVar);
                            }
                            i16 = zzdt.zze(zzfzVar.zzy(i28), b, bArr, i29, i2, zzeyVar, zzdsVar);
                            i20 = i20;
                            i19 = i28;
                            i17 = i5;
                            i14 = 1048575;
                            i15 = -1;
                        } else {
                            i10 = i29;
                            i11 = i18;
                            i12 = i20;
                            unsafe = unsafe2;
                            i7 = i28;
                            i6 = -1;
                            i4 = i10;
                            i18 = i11;
                            i20 = i12;
                        }
                    } else if (zzu <= 49) {
                        int i30 = i20;
                        int i31 = i18;
                        i6 = -1;
                        unsafe = unsafe2;
                        i7 = i28;
                        i16 = zzp(obj, bArr, i29, i2, b, i5, i23, i28, i24, zzu, j, zzdsVar);
                        if (i16 != i29) {
                            obj2 = obj;
                            bArr2 = bArr;
                            i13 = i2;
                            zzdsVar2 = zzdsVar;
                            i18 = i31;
                            i15 = -1;
                            i17 = i5;
                            i20 = i30;
                            i19 = i7;
                            unsafe2 = unsafe;
                            i14 = 1048575;
                            zzfzVar = this;
                        } else {
                            i4 = i16;
                            i18 = i31;
                            i20 = i30;
                        }
                    } else {
                        i10 = i29;
                        i12 = i20;
                        i11 = i18;
                        unsafe = unsafe2;
                        i7 = i28;
                        i6 = -1;
                        if (zzu != 50) {
                            i16 = zzn(obj, bArr, i10, i2, b, i5, i23, i24, zzu, j, i7, zzdsVar);
                            if (i16 != i10) {
                                obj2 = obj;
                                bArr2 = bArr;
                                i13 = i2;
                                zzdsVar2 = zzdsVar;
                                i18 = i11;
                                i15 = -1;
                                i17 = i5;
                                i20 = i12;
                                i19 = i7;
                                unsafe2 = unsafe;
                                i14 = 1048575;
                                zzfzVar = this;
                            } else {
                                i4 = i16;
                                i18 = i11;
                                i20 = i12;
                            }
                        } else if (i23 == 2) {
                            i16 = zzm(obj, bArr, i10, i2, i7, j, zzdsVar);
                            if (i16 != i10) {
                                obj2 = obj;
                                bArr2 = bArr;
                                i13 = i2;
                                zzdsVar2 = zzdsVar;
                                i18 = i11;
                                i15 = -1;
                                i17 = i5;
                                i20 = i12;
                                i19 = i7;
                                unsafe2 = unsafe;
                                i14 = 1048575;
                                zzfzVar = this;
                            } else {
                                i4 = i16;
                                i18 = i11;
                                i20 = i12;
                            }
                        } else {
                            i4 = i10;
                            i18 = i11;
                            i20 = i12;
                        }
                    }
                }
            }
            i16 = zzdt.zzi(b, bArr, i4, i2, zzc(obj), zzdsVar);
            zzfzVar = this;
            obj2 = obj;
            bArr2 = bArr;
            i13 = i2;
            zzdsVar2 = zzdsVar;
            i15 = i6;
            i17 = i5;
            i19 = i7;
            unsafe2 = unsafe;
            i14 = 1048575;
        }
        int i32 = i20;
        int i33 = i18;
        Unsafe unsafe3 = unsafe2;
        if (i33 != 1048575) {
            unsafe3.putInt(obj, i33, i32);
        }
        if (i16 == i2) {
            return i16;
        }
        throw zzfa.zzd();
    }

    private final int zzp(Object obj, byte[] bArr, int i, int i2, int i3, int i4, int i5, int i6, long j, int i7, long j2, zzds zzdsVar) throws IOException {
        int zzl;
        Unsafe unsafe = zzb;
        zzey zzeyVar = (zzey) unsafe.getObject(obj, j2);
        if (!zzeyVar.zzc()) {
            int size = zzeyVar.size();
            zzeyVar = zzeyVar.zzd(size == 0 ? 10 : size + size);
            unsafe.putObject(obj, j2, zzeyVar);
        }
        switch (i7) {
            case 18:
            case 35:
                if (i5 == 2) {
                    zzej zzejVar = (zzej) zzeyVar;
                    int zzj = zzdt.zzj(bArr, i, zzdsVar);
                    int i8 = zzdsVar.zza + zzj;
                    while (zzj < i8) {
                        zzejVar.zze(Double.longBitsToDouble(zzdt.zzn(bArr, zzj)));
                        zzj += 8;
                    }
                    if (zzj == i8) {
                        return zzj;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 1) {
                    zzej zzejVar2 = (zzej) zzeyVar;
                    zzejVar2.zze(Double.longBitsToDouble(zzdt.zzn(bArr, i)));
                    int i9 = i + 8;
                    while (i9 < i2) {
                        int zzj2 = zzdt.zzj(bArr, i9, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return i9;
                        }
                        zzejVar2.zze(Double.longBitsToDouble(zzdt.zzn(bArr, zzj2)));
                        i9 = zzj2 + 8;
                    }
                    return i9;
                }
                break;
            case 19:
            case 36:
                if (i5 == 2) {
                    zzeq zzeqVar = (zzeq) zzeyVar;
                    int zzj3 = zzdt.zzj(bArr, i, zzdsVar);
                    int i10 = zzdsVar.zza + zzj3;
                    while (zzj3 < i10) {
                        zzeqVar.zze(Float.intBitsToFloat(zzdt.zzb(bArr, zzj3)));
                        zzj3 += 4;
                    }
                    if (zzj3 == i10) {
                        return zzj3;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 5) {
                    zzeq zzeqVar2 = (zzeq) zzeyVar;
                    zzeqVar2.zze(Float.intBitsToFloat(zzdt.zzb(bArr, i)));
                    int i11 = i + 4;
                    while (i11 < i2) {
                        int zzj4 = zzdt.zzj(bArr, i11, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return i11;
                        }
                        zzeqVar2.zze(Float.intBitsToFloat(zzdt.zzb(bArr, zzj4)));
                        i11 = zzj4 + 4;
                    }
                    return i11;
                }
                break;
            case 20:
            case 21:
            case 37:
            case 38:
                if (i5 == 2) {
                    zzfl zzflVar = (zzfl) zzeyVar;
                    int zzj5 = zzdt.zzj(bArr, i, zzdsVar);
                    int i12 = zzdsVar.zza + zzj5;
                    while (zzj5 < i12) {
                        zzj5 = zzdt.zzm(bArr, zzj5, zzdsVar);
                        zzflVar.zze(zzdsVar.zzb);
                    }
                    if (zzj5 == i12) {
                        return zzj5;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 0) {
                    zzfl zzflVar2 = (zzfl) zzeyVar;
                    int zzm = zzdt.zzm(bArr, i, zzdsVar);
                    zzflVar2.zze(zzdsVar.zzb);
                    while (zzm < i2) {
                        int zzj6 = zzdt.zzj(bArr, zzm, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzm;
                        }
                        zzm = zzdt.zzm(bArr, zzj6, zzdsVar);
                        zzflVar2.zze(zzdsVar.zzb);
                    }
                    return zzm;
                }
                break;
            case 22:
            case 29:
            case 39:
            case 43:
                if (i5 == 2) {
                    return zzdt.zzf(bArr, i, zzeyVar, zzdsVar);
                }
                if (i5 == 0) {
                    return zzdt.zzl(i3, bArr, i, i2, zzeyVar, zzdsVar);
                }
                break;
            case 23:
            case 32:
            case 40:
            case 46:
                if (i5 == 2) {
                    zzfl zzflVar3 = (zzfl) zzeyVar;
                    int zzj7 = zzdt.zzj(bArr, i, zzdsVar);
                    int i13 = zzdsVar.zza + zzj7;
                    while (zzj7 < i13) {
                        zzflVar3.zze(zzdt.zzn(bArr, zzj7));
                        zzj7 += 8;
                    }
                    if (zzj7 == i13) {
                        return zzj7;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 1) {
                    zzfl zzflVar4 = (zzfl) zzeyVar;
                    zzflVar4.zze(zzdt.zzn(bArr, i));
                    int i14 = i + 8;
                    while (i14 < i2) {
                        int zzj8 = zzdt.zzj(bArr, i14, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return i14;
                        }
                        zzflVar4.zze(zzdt.zzn(bArr, zzj8));
                        i14 = zzj8 + 8;
                    }
                    return i14;
                }
                break;
            case 24:
            case 31:
            case 41:
            case 45:
                if (i5 == 2) {
                    zzev zzevVar = (zzev) zzeyVar;
                    int zzj9 = zzdt.zzj(bArr, i, zzdsVar);
                    int i15 = zzdsVar.zza + zzj9;
                    while (zzj9 < i15) {
                        zzevVar.zze(zzdt.zzb(bArr, zzj9));
                        zzj9 += 4;
                    }
                    if (zzj9 == i15) {
                        return zzj9;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 5) {
                    zzev zzevVar2 = (zzev) zzeyVar;
                    zzevVar2.zze(zzdt.zzb(bArr, i));
                    int i16 = i + 4;
                    while (i16 < i2) {
                        int zzj10 = zzdt.zzj(bArr, i16, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return i16;
                        }
                        zzevVar2.zze(zzdt.zzb(bArr, zzj10));
                        i16 = zzj10 + 4;
                    }
                    return i16;
                }
                break;
            case 25:
            case 42:
                if (i5 == 2) {
                    zzdu zzduVar = (zzdu) zzeyVar;
                    int zzj11 = zzdt.zzj(bArr, i, zzdsVar);
                    int i17 = zzdsVar.zza + zzj11;
                    while (zzj11 < i17) {
                        zzj11 = zzdt.zzm(bArr, zzj11, zzdsVar);
                        zzduVar.zze(zzdsVar.zzb != 0);
                    }
                    if (zzj11 == i17) {
                        return zzj11;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 0) {
                    zzdu zzduVar2 = (zzdu) zzeyVar;
                    int zzm2 = zzdt.zzm(bArr, i, zzdsVar);
                    zzduVar2.zze(zzdsVar.zzb != 0);
                    while (zzm2 < i2) {
                        int zzj12 = zzdt.zzj(bArr, zzm2, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzm2;
                        }
                        zzm2 = zzdt.zzm(bArr, zzj12, zzdsVar);
                        zzduVar2.zze(zzdsVar.zzb != 0);
                    }
                    return zzm2;
                }
                break;
            case 26:
                if (i5 == 2) {
                    if ((j & 536870912) == 0) {
                        int zzj13 = zzdt.zzj(bArr, i, zzdsVar);
                        int i18 = zzdsVar.zza;
                        if (i18 < 0) {
                            throw zzfa.zzc();
                        }
                        if (i18 == 0) {
                            zzeyVar.add("");
                        } else {
                            zzeyVar.add(new String(bArr, zzj13, i18, zzez.zzb));
                            zzj13 += i18;
                        }
                        while (zzj13 < i2) {
                            int zzj14 = zzdt.zzj(bArr, zzj13, zzdsVar);
                            if (i3 != zzdsVar.zza) {
                                return zzj13;
                            }
                            zzj13 = zzdt.zzj(bArr, zzj14, zzdsVar);
                            int i19 = zzdsVar.zza;
                            if (i19 < 0) {
                                throw zzfa.zzc();
                            }
                            if (i19 == 0) {
                                zzeyVar.add("");
                            } else {
                                zzeyVar.add(new String(bArr, zzj13, i19, zzez.zzb));
                                zzj13 += i19;
                            }
                        }
                        return zzj13;
                    }
                    int zzj15 = zzdt.zzj(bArr, i, zzdsVar);
                    int i20 = zzdsVar.zza;
                    if (i20 < 0) {
                        throw zzfa.zzc();
                    }
                    if (i20 == 0) {
                        zzeyVar.add("");
                    } else {
                        int i21 = zzj15 + i20;
                        if (!zzhm.zzd(bArr, zzj15, i21)) {
                            throw zzfa.zzb();
                        }
                        zzeyVar.add(new String(bArr, zzj15, i20, zzez.zzb));
                        zzj15 = i21;
                    }
                    while (zzj15 < i2) {
                        int zzj16 = zzdt.zzj(bArr, zzj15, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzj15;
                        }
                        zzj15 = zzdt.zzj(bArr, zzj16, zzdsVar);
                        int i22 = zzdsVar.zza;
                        if (i22 < 0) {
                            throw zzfa.zzc();
                        }
                        if (i22 == 0) {
                            zzeyVar.add("");
                        } else {
                            int i23 = zzj15 + i22;
                            if (!zzhm.zzd(bArr, zzj15, i23)) {
                                throw zzfa.zzb();
                            }
                            zzeyVar.add(new String(bArr, zzj15, i22, zzez.zzb));
                            zzj15 = i23;
                        }
                    }
                    return zzj15;
                }
                break;
            case 27:
                if (i5 == 2) {
                    return zzdt.zze(zzy(i6), i3, bArr, i, i2, zzeyVar, zzdsVar);
                }
                break;
            case 28:
                if (i5 == 2) {
                    int zzj17 = zzdt.zzj(bArr, i, zzdsVar);
                    int i24 = zzdsVar.zza;
                    if (i24 < 0) {
                        throw zzfa.zzc();
                    }
                    if (i24 > bArr.length - zzj17) {
                        throw zzfa.zzf();
                    }
                    if (i24 == 0) {
                        zzeyVar.add(zzee.zzb);
                    } else {
                        zzeyVar.add(zzee.zzk(bArr, zzj17, i24));
                        zzj17 += i24;
                    }
                    while (zzj17 < i2) {
                        int zzj18 = zzdt.zzj(bArr, zzj17, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzj17;
                        }
                        zzj17 = zzdt.zzj(bArr, zzj18, zzdsVar);
                        int i25 = zzdsVar.zza;
                        if (i25 < 0) {
                            throw zzfa.zzc();
                        }
                        if (i25 > bArr.length - zzj17) {
                            throw zzfa.zzf();
                        }
                        if (i25 == 0) {
                            zzeyVar.add(zzee.zzb);
                        } else {
                            zzeyVar.add(zzee.zzk(bArr, zzj17, i25));
                            zzj17 += i25;
                        }
                    }
                    return zzj17;
                }
                break;
            case 30:
            case 44:
                if (i5 == 2) {
                    zzl = zzdt.zzf(bArr, i, zzeyVar, zzdsVar);
                } else if (i5 == 0) {
                    zzl = zzdt.zzl(i3, bArr, i, i2, zzeyVar, zzdsVar);
                }
                zzeu zzeuVar = (zzeu) obj;
                zzgz zzgzVar = zzeuVar.zzc;
                if (zzgzVar == zzgz.zza()) {
                    zzgzVar = null;
                }
                Object zzd = zzgj.zzd(i4, zzeyVar, zzx(i6), zzgzVar, this.zzm);
                if (zzd == null) {
                    return zzl;
                }
                zzeuVar.zzc = (zzgz) zzd;
                return zzl;
            case 33:
            case 47:
                if (i5 == 2) {
                    zzev zzevVar3 = (zzev) zzeyVar;
                    int zzj19 = zzdt.zzj(bArr, i, zzdsVar);
                    int i26 = zzdsVar.zza + zzj19;
                    while (zzj19 < i26) {
                        zzj19 = zzdt.zzj(bArr, zzj19, zzdsVar);
                        zzevVar3.zze(zzei.zzb(zzdsVar.zza));
                    }
                    if (zzj19 == i26) {
                        return zzj19;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 0) {
                    zzev zzevVar4 = (zzev) zzeyVar;
                    int zzj20 = zzdt.zzj(bArr, i, zzdsVar);
                    zzevVar4.zze(zzei.zzb(zzdsVar.zza));
                    while (zzj20 < i2) {
                        int zzj21 = zzdt.zzj(bArr, zzj20, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzj20;
                        }
                        zzj20 = zzdt.zzj(bArr, zzj21, zzdsVar);
                        zzevVar4.zze(zzei.zzb(zzdsVar.zza));
                    }
                    return zzj20;
                }
                break;
            case 34:
            case 48:
                if (i5 == 2) {
                    zzfl zzflVar5 = (zzfl) zzeyVar;
                    int zzj22 = zzdt.zzj(bArr, i, zzdsVar);
                    int i27 = zzdsVar.zza + zzj22;
                    while (zzj22 < i27) {
                        zzj22 = zzdt.zzm(bArr, zzj22, zzdsVar);
                        zzflVar5.zze(zzei.zzc(zzdsVar.zzb));
                    }
                    if (zzj22 == i27) {
                        return zzj22;
                    }
                    throw zzfa.zzf();
                }
                if (i5 == 0) {
                    zzfl zzflVar6 = (zzfl) zzeyVar;
                    int zzm3 = zzdt.zzm(bArr, i, zzdsVar);
                    zzflVar6.zze(zzei.zzc(zzdsVar.zzb));
                    while (zzm3 < i2) {
                        int zzj23 = zzdt.zzj(bArr, zzm3, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzm3;
                        }
                        zzm3 = zzdt.zzm(bArr, zzj23, zzdsVar);
                        zzflVar6.zze(zzei.zzc(zzdsVar.zzb));
                    }
                    return zzm3;
                }
                break;
            default:
                if (i5 == 3) {
                    zzgh zzy = zzy(i6);
                    int i28 = (i3 & (-8)) | 4;
                    int zzc = zzdt.zzc(zzy, bArr, i, i2, i28, zzdsVar);
                    zzeyVar.add(zzdsVar.zzc);
                    while (zzc < i2) {
                        int zzj24 = zzdt.zzj(bArr, zzc, zzdsVar);
                        if (i3 != zzdsVar.zza) {
                            return zzc;
                        }
                        zzc = zzdt.zzc(zzy, bArr, zzj24, i2, i28, zzdsVar);
                        zzeyVar.add(zzdsVar.zzc);
                    }
                    return zzc;
                }
                break;
        }
        return i;
    }

    private final int zzq(int i) {
        if (i < this.zze || i > this.zzf) {
            return -1;
        }
        return zzt(i, 0);
    }

    private final int zzr(int i, int i2) {
        if (i < this.zze || i > this.zzf) {
            return -1;
        }
        return zzt(i, i2);
    }

    private final int zzs(int i) {
        return this.zzc[i + 2];
    }

    private final int zzt(int i, int i2) {
        int length = (this.zzc.length / 3) - 1;
        while (i2 <= length) {
            int i3 = (length + i2) >>> 1;
            int i4 = i3 * 3;
            int i5 = this.zzc[i4];
            if (i == i5) {
                return i4;
            }
            if (i < i5) {
                length = i3 - 1;
            } else {
                i2 = i3 + 1;
            }
        }
        return -1;
    }

    private static int zzu(int i) {
        return (i >>> 20) & 255;
    }

    private final int zzv(int i) {
        return this.zzc[i + 1];
    }

    private static long zzw(Object obj, long j) {
        return ((Long) zzhi.zzf(obj, j)).longValue();
    }

    private final zzex zzx(int i) {
        int i2 = i / 3;
        return (zzex) this.zzd[i2 + i2 + 1];
    }

    private final zzgh zzy(int i) {
        int i2 = i / 3;
        int i3 = i2 + i2;
        zzgh zzghVar = (zzgh) this.zzd[i3];
        if (zzghVar != null) {
            return zzghVar;
        }
        zzgh zzb2 = zzge.zza().zzb((Class) this.zzd[i3 + 1]);
        this.zzd[i3] = zzb2;
        return zzb2;
    }

    private final Object zzz(int i) {
        int i2 = i / 3;
        return this.zzd[i2 + i2];
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final int zza(Object obj) {
        int length = this.zzc.length;
        int i = 0;
        for (int i2 = 0; i2 < length; i2 += 3) {
            int zzv = zzv(i2);
            int i3 = this.zzc[i2];
            long j = 1048575 & zzv;
            switch (zzu(zzv)) {
                case 0:
                    i = (i * 53) + zzez.zzc(Double.doubleToLongBits(zzhi.zza(obj, j)));
                    break;
                case 1:
                    i = (i * 53) + Float.floatToIntBits(zzhi.zzb(obj, j));
                    break;
                case 2:
                    i = (i * 53) + zzez.zzc(zzhi.zzd(obj, j));
                    break;
                case 3:
                    i = (i * 53) + zzez.zzc(zzhi.zzd(obj, j));
                    break;
                case 4:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 5:
                    i = (i * 53) + zzez.zzc(zzhi.zzd(obj, j));
                    break;
                case 6:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 7:
                    i = (i * 53) + zzez.zza(zzhi.zzt(obj, j));
                    break;
                case 8:
                    i = (i * 53) + ((String) zzhi.zzf(obj, j)).hashCode();
                    break;
                case 9:
                    Object zzf = zzhi.zzf(obj, j);
                    i = (i * 53) + (zzf != null ? zzf.hashCode() : 37);
                    break;
                case 10:
                    i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                    break;
                case 11:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 12:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 13:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 14:
                    i = (i * 53) + zzez.zzc(zzhi.zzd(obj, j));
                    break;
                case 15:
                    i = (i * 53) + zzhi.zzc(obj, j);
                    break;
                case 16:
                    i = (i * 53) + zzez.zzc(zzhi.zzd(obj, j));
                    break;
                case 17:
                    Object zzf2 = zzhi.zzf(obj, j);
                    i = (i * 53) + (zzf2 != null ? zzf2.hashCode() : 37);
                    break;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                    break;
                case 50:
                    i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                    break;
                case 51:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(Double.doubleToLongBits(((Double) zzhi.zzf(obj, j)).doubleValue()));
                        break;
                    } else {
                        break;
                    }
                case 52:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + Float.floatToIntBits(((Float) zzhi.zzf(obj, j)).floatValue());
                        break;
                    } else {
                        break;
                    }
                case Opcodes.SALOAD /* 53 */:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(zzw(obj, j));
                        break;
                    } else {
                        break;
                    }
                case Opcodes.ISTORE /* 54 */:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(zzw(obj, j));
                        break;
                    } else {
                        break;
                    }
                case 55:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case 56:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(zzw(obj, j));
                        break;
                    } else {
                        break;
                    }
                case 57:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case Opcodes.ASTORE /* 58 */:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zza(((Boolean) zzhi.zzf(obj, j)).booleanValue());
                        break;
                    } else {
                        break;
                    }
                case 59:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + ((String) zzhi.zzf(obj, j)).hashCode();
                        break;
                    } else {
                        break;
                    }
                case 60:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                        break;
                    } else {
                        break;
                    }
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                        break;
                    } else {
                        break;
                    }
                case 62:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case 63:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case 64:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(zzw(obj, j));
                        break;
                    } else {
                        break;
                    }
                case 66:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzl(obj, j);
                        break;
                    } else {
                        break;
                    }
                case 67:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzez.zzc(zzw(obj, j));
                        break;
                    } else {
                        break;
                    }
                case 68:
                    if (zzJ(obj, i3, i2)) {
                        i = (i * 53) + zzhi.zzf(obj, j).hashCode();
                        break;
                    } else {
                        break;
                    }
            }
        }
        return (i * 53) + this.zzm.zza(obj).hashCode();
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:111:0x0092. Please report as an issue. */
    final int zzb(Object obj, byte[] bArr, int i, int i2, int i3, zzds zzdsVar) throws IOException {
        Unsafe unsafe;
        int i4;
        Object obj2;
        int i5;
        int i6;
        int i7;
        int i8;
        int i9;
        int i10;
        int i11;
        int i12;
        int i13;
        int i14;
        int i15;
        int i16;
        zzfz<T> zzfzVar = this;
        Object obj3 = obj;
        byte[] bArr2 = bArr;
        int i17 = i2;
        int i18 = i3;
        zzds zzdsVar2 = zzdsVar;
        Unsafe unsafe2 = zzb;
        int i19 = i;
        int i20 = 0;
        int i21 = 0;
        int i22 = 0;
        int i23 = -1;
        int i24 = 1048575;
        while (true) {
            if (i19 < i17) {
                int i25 = i19 + 1;
                byte b = bArr2[i19];
                if (b < 0) {
                    int zzk = zzdt.zzk(b, bArr2, i25, zzdsVar2);
                    i5 = zzdsVar2.zza;
                    i25 = zzk;
                } else {
                    i5 = b;
                }
                int i26 = i5 >>> 3;
                int i27 = i5 & 7;
                int zzr = i26 > i23 ? zzfzVar.zzr(i26, i21 / 3) : zzfzVar.zzq(i26);
                if (zzr == -1) {
                    i6 = i26;
                    i7 = i25;
                    i8 = i5;
                    i9 = i22;
                    unsafe = unsafe2;
                    i10 = 0;
                } else {
                    int[] iArr = zzfzVar.zzc;
                    int i28 = iArr[zzr + 1];
                    int zzu = zzu(i28);
                    i6 = i26;
                    int i29 = i25;
                    long j = i28 & 1048575;
                    if (zzu <= 17) {
                        int i30 = iArr[zzr + 2];
                        int i31 = 1 << (i30 >>> 20);
                        int i32 = i30 & 1048575;
                        if (i32 != i24) {
                            if (i24 != 1048575) {
                                i11 = i5;
                                unsafe2.putInt(obj3, i24, i22);
                            } else {
                                i11 = i5;
                            }
                            i12 = i32;
                            i13 = unsafe2.getInt(obj3, i32);
                        } else {
                            i11 = i5;
                            i12 = i24;
                            i13 = i22;
                        }
                        switch (zzu) {
                            case 0:
                                i14 = zzr;
                                i15 = i29;
                                i8 = i11;
                                if (i27 != 1) {
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    zzhi.zzl(obj3, j, Double.longBitsToDouble(zzdt.zzn(bArr2, i15)));
                                    i19 = i15 + 8;
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i8;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 1:
                                i14 = zzr;
                                i15 = i29;
                                i8 = i11;
                                if (i27 != 5) {
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    zzhi.zzm(obj3, j, Float.intBitsToFloat(zzdt.zzb(bArr2, i15)));
                                    i19 = i15 + 4;
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i8;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 2:
                            case 3:
                                i14 = zzr;
                                i15 = i29;
                                i8 = i11;
                                if (i27 != 0) {
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zzm = zzdt.zzm(bArr2, i15, zzdsVar2);
                                    unsafe2.putLong(obj, j, zzdsVar2.zzb);
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i19 = zzm;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i8;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 4:
                            case 11:
                                i14 = zzr;
                                i15 = i29;
                                i8 = i11;
                                if (i27 != 0) {
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    i19 = zzdt.zzj(bArr2, i15, zzdsVar2);
                                    unsafe2.putInt(obj3, j, zzdsVar2.zza);
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i8;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 5:
                            case 14:
                                i14 = zzr;
                                int i33 = i11;
                                if (i27 != 1) {
                                    i15 = i29;
                                    i8 = i33;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    unsafe2.putLong(obj, j, zzdt.zzn(bArr2, i29));
                                    i19 = i29 + 8;
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i33;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 6:
                            case 13:
                                i14 = zzr;
                                int i34 = i11;
                                if (i27 != 5) {
                                    i15 = i29;
                                    i8 = i34;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    unsafe2.putInt(obj3, j, zzdt.zzb(bArr2, i29));
                                    i19 = i29 + 4;
                                    i22 = i13 | i31;
                                    i20 = i34;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 7:
                                i14 = zzr;
                                int i35 = i11;
                                if (i27 != 0) {
                                    i15 = i29;
                                    i8 = i35;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    i19 = zzdt.zzm(bArr2, i29, zzdsVar2);
                                    zzhi.zzk(obj3, j, zzdsVar2.zzb != 0);
                                    i22 = i13 | i31;
                                    i20 = i35;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 8:
                                i14 = zzr;
                                int i36 = i11;
                                if (i27 != 2) {
                                    i15 = i29;
                                    i8 = i36;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zzg = (536870912 & i28) == 0 ? zzdt.zzg(bArr2, i29, zzdsVar2) : zzdt.zzh(bArr2, i29, zzdsVar2);
                                    unsafe2.putObject(obj3, j, zzdsVar2.zzc);
                                    i22 = i13 | i31;
                                    i19 = zzg;
                                    i20 = i36;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 9:
                                i14 = zzr;
                                int i37 = i11;
                                if (i27 != 2) {
                                    i15 = i29;
                                    i8 = i37;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zzd = zzdt.zzd(zzfzVar.zzy(i14), bArr2, i29, i17, zzdsVar2);
                                    if ((i13 & i31) == 0) {
                                        unsafe2.putObject(obj3, j, zzdsVar2.zzc);
                                    } else {
                                        unsafe2.putObject(obj3, j, zzez.zzg(unsafe2.getObject(obj3, j), zzdsVar2.zzc));
                                    }
                                    i22 = i13 | i31;
                                    i19 = zzd;
                                    i20 = i37;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 10:
                                i14 = zzr;
                                int i38 = i11;
                                if (i27 != 2) {
                                    i15 = i29;
                                    i8 = i38;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zza2 = zzdt.zza(bArr2, i29, zzdsVar2);
                                    unsafe2.putObject(obj3, j, zzdsVar2.zzc);
                                    i22 = i13 | i31;
                                    i19 = zza2;
                                    i20 = i38;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 12:
                                i14 = zzr;
                                int i39 = i11;
                                if (i27 != 0) {
                                    i15 = i29;
                                    i8 = i39;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zzj = zzdt.zzj(bArr2, i29, zzdsVar2);
                                    int i40 = zzdsVar2.zza;
                                    zzex zzx = zzfzVar.zzx(i14);
                                    if (zzx == null || zzx.zza()) {
                                        unsafe2.putInt(obj3, j, i40);
                                        i22 = i13 | i31;
                                        i19 = zzj;
                                        i20 = i39;
                                        i21 = i14;
                                        i23 = i6;
                                        i24 = i12;
                                        i18 = i3;
                                    } else {
                                        zzc(obj).zzf(i39, Long.valueOf(i40));
                                        i19 = zzj;
                                        i20 = i39;
                                        i22 = i13;
                                        i21 = i14;
                                        i23 = i6;
                                        i24 = i12;
                                        i18 = i3;
                                    }
                                }
                                break;
                            case 15:
                                i14 = zzr;
                                int i41 = i11;
                                if (i27 != 0) {
                                    i15 = i29;
                                    i8 = i41;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    i19 = zzdt.zzj(bArr2, i29, zzdsVar2);
                                    unsafe2.putInt(obj3, j, zzei.zzb(zzdsVar2.zza));
                                    i22 = i13 | i31;
                                    i20 = i41;
                                    i21 = i14;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            case 16:
                                if (i27 != 0) {
                                    i14 = zzr;
                                    i15 = i29;
                                    i8 = i11;
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    int zzm2 = zzdt.zzm(bArr2, i29, zzdsVar2);
                                    unsafe2.putLong(obj, j, zzei.zzc(zzdsVar2.zzb));
                                    i22 = i13 | i31;
                                    i19 = zzm2;
                                    i20 = i11;
                                    i21 = zzr;
                                    i23 = i6;
                                    i24 = i12;
                                    i18 = i3;
                                }
                            default:
                                i14 = zzr;
                                i15 = i29;
                                i8 = i11;
                                if (i27 != 3) {
                                    i9 = i13;
                                    unsafe = unsafe2;
                                    i10 = i14;
                                    i7 = i15;
                                    i24 = i12;
                                    break;
                                } else {
                                    i19 = zzdt.zzc(zzfzVar.zzy(i14), bArr, i15, i2, (i6 << 3) | 4, zzdsVar);
                                    if ((i13 & i31) == 0) {
                                        unsafe2.putObject(obj3, j, zzdsVar2.zzc);
                                    } else {
                                        unsafe2.putObject(obj3, j, zzez.zzg(unsafe2.getObject(obj3, j), zzdsVar2.zzc));
                                    }
                                    i22 = i13 | i31;
                                    i17 = i2;
                                    i21 = i14;
                                    i23 = i6;
                                    i20 = i8;
                                    i24 = i12;
                                    i18 = i3;
                                }
                        }
                    } else {
                        int i42 = i5;
                        int i43 = zzr;
                        i8 = i42;
                        if (zzu != 27) {
                            i10 = i43;
                            i9 = i22;
                            i12 = i24;
                            if (zzu <= 49) {
                                unsafe = unsafe2;
                                i19 = zzp(obj, bArr, i29, i2, i8, i6, i27, i10, i28, zzu, j, zzdsVar);
                                if (i19 != i29) {
                                    zzfzVar = this;
                                    obj3 = obj;
                                    bArr2 = bArr;
                                    i17 = i2;
                                    i18 = i3;
                                    zzdsVar2 = zzdsVar;
                                    i21 = i10;
                                    i23 = i6;
                                    i20 = i8;
                                    i22 = i9;
                                    i24 = i12;
                                    unsafe2 = unsafe;
                                } else {
                                    i7 = i19;
                                    i24 = i12;
                                }
                            } else {
                                unsafe = unsafe2;
                                i16 = i29;
                                if (zzu != 50) {
                                    i19 = zzn(obj, bArr, i16, i2, i8, i6, i27, i28, zzu, j, i10, zzdsVar);
                                    if (i19 != i16) {
                                        zzfzVar = this;
                                        obj3 = obj;
                                        bArr2 = bArr;
                                        i17 = i2;
                                        i18 = i3;
                                        zzdsVar2 = zzdsVar;
                                        i21 = i10;
                                        i23 = i6;
                                        i20 = i8;
                                        i22 = i9;
                                        i24 = i12;
                                        unsafe2 = unsafe;
                                    } else {
                                        i7 = i19;
                                        i24 = i12;
                                    }
                                } else if (i27 == 2) {
                                    i19 = zzm(obj, bArr, i16, i2, i10, j, zzdsVar);
                                    if (i19 != i16) {
                                        zzfzVar = this;
                                        obj3 = obj;
                                        bArr2 = bArr;
                                        i17 = i2;
                                        i18 = i3;
                                        zzdsVar2 = zzdsVar;
                                        i21 = i10;
                                        i23 = i6;
                                        i20 = i8;
                                        i22 = i9;
                                        i24 = i12;
                                        unsafe2 = unsafe;
                                    } else {
                                        i7 = i19;
                                        i24 = i12;
                                    }
                                } else {
                                    i7 = i16;
                                }
                            }
                        } else if (i27 == 2) {
                            zzey zzeyVar = (zzey) unsafe2.getObject(obj3, j);
                            if (!zzeyVar.zzc()) {
                                int size = zzeyVar.size();
                                zzeyVar = zzeyVar.zzd(size == 0 ? 10 : size + size);
                                unsafe2.putObject(obj3, j, zzeyVar);
                            }
                            i20 = i8;
                            i19 = zzdt.zze(zzfzVar.zzy(i43), i20, bArr, i29, i2, zzeyVar, zzdsVar);
                            i17 = i2;
                            i18 = i3;
                            i21 = i43;
                            i23 = i6;
                            i22 = i22;
                            i24 = i24;
                        } else {
                            i10 = i43;
                            i9 = i22;
                            i12 = i24;
                            unsafe = unsafe2;
                            i16 = i29;
                            i7 = i16;
                        }
                        i24 = i12;
                    }
                }
                i4 = i3;
                int i44 = i8;
                if (i44 != i4 || i4 == 0) {
                    i19 = zzdt.zzi(i44, bArr, i7, i2, zzc(obj), zzdsVar);
                    zzfzVar = this;
                    obj3 = obj;
                    bArr2 = bArr;
                    i17 = i2;
                    zzdsVar2 = zzdsVar;
                    i18 = i4;
                    i20 = i44;
                    i21 = i10;
                    i23 = i6;
                    i22 = i9;
                    unsafe2 = unsafe;
                } else {
                    i19 = i7;
                    i20 = i44;
                    i22 = i9;
                }
            } else {
                unsafe = unsafe2;
                i4 = i18;
            }
        }
        if (i24 != 1048575) {
            long j2 = i24;
            obj2 = obj;
            unsafe.putInt(obj2, j2, i22);
        } else {
            obj2 = obj;
        }
        for (int i45 = this.zzj; i45 < this.zzk; i45++) {
            int i46 = this.zzi[i45];
            int i47 = this.zzc[i46];
            Object zzf = zzhi.zzf(obj2, zzv(i46) & 1048575);
            if (zzf != null && zzx(i46) != null) {
                throw null;
            }
        }
        if (i4 == 0) {
            if (i19 != i2) {
                throw zzfa.zzd();
            }
        } else if (i19 > i2 || i20 != i4) {
            throw zzfa.zzd();
        }
        return i19;
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final Object zzd() {
        return ((zzeu) this.zzg).zzi(4, null, null);
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final void zze(Object obj) {
        int i;
        int i2 = this.zzj;
        while (true) {
            i = this.zzk;
            if (i2 >= i) {
                break;
            }
            long zzv = zzv(this.zzi[i2]) & 1048575;
            Object zzf = zzhi.zzf(obj, zzv);
            if (zzf != null) {
                ((zzfq) zzf).zzc();
                zzhi.zzp(obj, zzv, zzf);
            }
            i2++;
        }
        int length = this.zzi.length;
        while (i < length) {
            this.zzl.zza(obj, this.zzi[i]);
            i++;
        }
        this.zzm.zze(obj);
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final void zzf(Object obj, Object obj2) {
        if (obj2 == null) {
            throw null;
        }
        for (int i = 0; i < this.zzc.length; i += 3) {
            int zzv = zzv(i);
            long j = 1048575 & zzv;
            int i2 = this.zzc[i];
            switch (zzu(zzv)) {
                case 0:
                    if (zzG(obj2, i)) {
                        zzhi.zzl(obj, j, zzhi.zza(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 1:
                    if (zzG(obj2, i)) {
                        zzhi.zzm(obj, j, zzhi.zzb(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 2:
                    if (zzG(obj2, i)) {
                        zzhi.zzo(obj, j, zzhi.zzd(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 3:
                    if (zzG(obj2, i)) {
                        zzhi.zzo(obj, j, zzhi.zzd(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 4:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 5:
                    if (zzG(obj2, i)) {
                        zzhi.zzo(obj, j, zzhi.zzd(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 6:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 7:
                    if (zzG(obj2, i)) {
                        zzhi.zzk(obj, j, zzhi.zzt(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 8:
                    if (zzG(obj2, i)) {
                        zzhi.zzp(obj, j, zzhi.zzf(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 9:
                    zzB(obj, obj2, i);
                    break;
                case 10:
                    if (zzG(obj2, i)) {
                        zzhi.zzp(obj, j, zzhi.zzf(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 11:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 12:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 13:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 14:
                    if (zzG(obj2, i)) {
                        zzhi.zzo(obj, j, zzhi.zzd(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 15:
                    if (zzG(obj2, i)) {
                        zzhi.zzn(obj, j, zzhi.zzc(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 16:
                    if (zzG(obj2, i)) {
                        zzhi.zzo(obj, j, zzhi.zzd(obj2, j));
                        zzD(obj, i);
                        break;
                    } else {
                        break;
                    }
                case 17:
                    zzB(obj, obj2, i);
                    break;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    this.zzl.zzb(obj, obj2, j);
                    break;
                case 50:
                    zzgj.zzi(this.zzp, obj, obj2, j);
                    break;
                case 51:
                case 52:
                case Opcodes.SALOAD /* 53 */:
                case Opcodes.ISTORE /* 54 */:
                case 55:
                case 56:
                case 57:
                case Opcodes.ASTORE /* 58 */:
                case 59:
                    if (zzJ(obj2, i2, i)) {
                        zzhi.zzp(obj, j, zzhi.zzf(obj2, j));
                        zzE(obj, i2, i);
                        break;
                    } else {
                        break;
                    }
                case 60:
                    zzC(obj, obj2, i);
                    break;
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                case 62:
                case 63:
                case 64:
                case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                case 66:
                case 67:
                    if (zzJ(obj2, i2, i)) {
                        zzhi.zzp(obj, j, zzhi.zzf(obj2, j));
                        zzE(obj, i2, i);
                        break;
                    } else {
                        break;
                    }
                case 68:
                    zzC(obj, obj2, i);
                    break;
            }
        }
        zzgj.zzf(this.zzm, obj, obj2);
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final void zzg(Object obj, byte[] bArr, int i, int i2, zzds zzdsVar) throws IOException {
        if (this.zzh) {
            zzo(obj, bArr, i, i2, zzdsVar);
        } else {
            zzb(obj, bArr, i, i2, 0, zzdsVar);
        }
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final boolean zzh(Object obj, Object obj2) {
        boolean zzh;
        int length = this.zzc.length;
        for (int i = 0; i < length; i += 3) {
            int zzv = zzv(i);
            long j = zzv & 1048575;
            switch (zzu(zzv)) {
                case 0:
                    if (zzF(obj, obj2, i) && Double.doubleToLongBits(zzhi.zza(obj, j)) == Double.doubleToLongBits(zzhi.zza(obj2, j))) {
                        continue;
                    }
                    return false;
                case 1:
                    if (zzF(obj, obj2, i) && Float.floatToIntBits(zzhi.zzb(obj, j)) == Float.floatToIntBits(zzhi.zzb(obj2, j))) {
                        continue;
                    }
                    return false;
                case 2:
                    if (zzF(obj, obj2, i) && zzhi.zzd(obj, j) == zzhi.zzd(obj2, j)) {
                        continue;
                    }
                    return false;
                case 3:
                    if (zzF(obj, obj2, i) && zzhi.zzd(obj, j) == zzhi.zzd(obj2, j)) {
                        continue;
                    }
                    return false;
                case 4:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 5:
                    if (zzF(obj, obj2, i) && zzhi.zzd(obj, j) == zzhi.zzd(obj2, j)) {
                        continue;
                    }
                    return false;
                case 6:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 7:
                    if (zzF(obj, obj2, i) && zzhi.zzt(obj, j) == zzhi.zzt(obj2, j)) {
                        continue;
                    }
                    return false;
                case 8:
                    if (zzF(obj, obj2, i) && zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j))) {
                        continue;
                    }
                    return false;
                case 9:
                    if (zzF(obj, obj2, i) && zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j))) {
                        continue;
                    }
                    return false;
                case 10:
                    if (zzF(obj, obj2, i) && zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j))) {
                        continue;
                    }
                    return false;
                case 11:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 12:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 13:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 14:
                    if (zzF(obj, obj2, i) && zzhi.zzd(obj, j) == zzhi.zzd(obj2, j)) {
                        continue;
                    }
                    return false;
                case 15:
                    if (zzF(obj, obj2, i) && zzhi.zzc(obj, j) == zzhi.zzc(obj2, j)) {
                        continue;
                    }
                    return false;
                case 16:
                    if (zzF(obj, obj2, i) && zzhi.zzd(obj, j) == zzhi.zzd(obj2, j)) {
                        continue;
                    }
                    return false;
                case 17:
                    if (zzF(obj, obj2, i) && zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j))) {
                        continue;
                    }
                    return false;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    zzh = zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j));
                    break;
                case 50:
                    zzh = zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j));
                    break;
                case 51:
                case 52:
                case Opcodes.SALOAD /* 53 */:
                case Opcodes.ISTORE /* 54 */:
                case 55:
                case 56:
                case 57:
                case Opcodes.ASTORE /* 58 */:
                case 59:
                case 60:
                case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                case 62:
                case 63:
                case 64:
                case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                case 66:
                case 67:
                case 68:
                    long zzs = zzs(i) & 1048575;
                    if (zzhi.zzc(obj, zzs) == zzhi.zzc(obj2, zzs) && zzgj.zzh(zzhi.zzf(obj, j), zzhi.zzf(obj2, j))) {
                        continue;
                    }
                    return false;
                default:
            }
            if (!zzh) {
                return false;
            }
        }
        return this.zzm.zza(obj).equals(this.zzm.zza(obj2));
    }

    @Override // com.google.android.gms.internal.auth.zzgh
    public final boolean zzi(Object obj) {
        int i;
        int i2;
        int i3 = 1048575;
        int i4 = 0;
        int i5 = 0;
        while (i5 < this.zzj) {
            int i6 = this.zzi[i5];
            int i7 = this.zzc[i6];
            int zzv = zzv(i6);
            int i8 = this.zzc[i6 + 2];
            int i9 = i8 & 1048575;
            int i10 = 1 << (i8 >>> 20);
            if (i9 == i3) {
                i = i3;
                i2 = i4;
            } else if (i9 != 1048575) {
                i2 = zzb.getInt(obj, i9);
                i = i9;
            } else {
                i2 = i4;
                i = i9;
            }
            if ((268435456 & zzv) != 0 && !zzH(obj, i6, i, i2, i10)) {
                return false;
            }
            switch (zzu(zzv)) {
                case 9:
                case 17:
                    if (zzH(obj, i6, i, i2, i10) && !zzI(obj, zzv, zzy(i6))) {
                        return false;
                    }
                    break;
                case 27:
                case 49:
                    List list = (List) zzhi.zzf(obj, zzv & 1048575);
                    if (!list.isEmpty()) {
                        zzgh zzy = zzy(i6);
                        for (int i11 = 0; i11 < list.size(); i11++) {
                            if (!zzy.zzi(list.get(i11))) {
                                return false;
                            }
                        }
                        break;
                    } else {
                        continue;
                    }
                case 50:
                    if (!((zzfq) zzhi.zzf(obj, zzv & 1048575)).isEmpty()) {
                        throw null;
                    }
                    break;
                case 60:
                case 68:
                    if (zzJ(obj, i7, i6) && !zzI(obj, zzv, zzy(i6))) {
                        return false;
                    }
                    break;
            }
            i5++;
            i3 = i;
            i4 = i2;
        }
        return true;
    }
}
