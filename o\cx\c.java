package o.cx;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import java.util.ArrayList;
import kotlin.io.encoding.Base64;
import o.ct.b;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cx\c.smali */
public final class c implements b<o.ff.c> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static char b;
    private static int c;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        a = new char[]{30566, 30570, 30587, 30574, 30571, 30563, 30561, 30564, 30562, 30583, 30572, 30560, 30559, 30588, 30573, 30591};
        b = (char) 17041;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 4
            int r9 = r9 + 4
            int r8 = 73 - r8
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r0 = o.cx.c.$$a
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L33
        L17:
            r3 = r2
        L18:
            r6 = r9
            r9 = r8
            r8 = r6
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r9 = r9 + 1
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cx.c.f(int, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{2, Base64.padSymbol, -41, 17};
        $$b = 210;
    }

    @Override // o.ct.b
    public final /* synthetic */ o.ff.c b(o.eg.b bVar) throws d {
        int i = c + 65;
        d = i % 128;
        switch (i % 2 != 0 ? 'P' : '\b') {
            case 'P':
                d(bVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return d(bVar);
        }
    }

    private static o.ff.c d(o.eg.b bVar) throws d {
        Object[] objArr = new Object[1];
        e(ImageFormat.getBitsPerPixel(0) + 6, "\u0002\u0004\u0005\u0000㙑", (byte) (111 - Color.red(0)), objArr);
        short shortValue = bVar.k(((String) objArr[0]).intern()).shortValue();
        Object[] objArr2 = new Object[1];
        e(View.MeasureSpec.getSize(0) + 3, "\u0007\u0003㗻", (byte) (8 - KeyEvent.normalizeMetaState(0)), objArr2);
        String r = bVar.r(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        e(TextUtils.getTrimmedLength("") + 2, "\u000e\t", (byte) (TextUtils.getCapsMode("", 0, 0) + 66), objArr3);
        int intValue = bVar.e(((String) objArr3[0]).intern(), (Integer) (-1)).intValue();
        Object[] objArr4 = new Object[1];
        e(Color.green(0) + 7, "\u000b\u0000\b\r\n\u0000㙑", (byte) (103 - ImageFormat.getBitsPerPixel(0)), objArr4);
        short shortValue2 = (short) (bVar.k(((String) objArr4[0]).intern()).shortValue() + 1);
        Object[] objArr5 = new Object[1];
        e((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 3, "\u0005\u0004㙚", (byte) (151 - AndroidCharacter.getMirror('0')), objArr5);
        byte byteValue = bVar.D(((String) objArr5[0]).intern()).byteValue();
        o.ff.c cVar = new o.ff.c(true, o.fc.c.b, shortValue);
        cVar.a(intValue);
        cVar.c(shortValue2);
        cVar.c(byteValue);
        cVar.a(r);
        cVar.d(new ArrayList());
        int i = c + 37;
        d = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return cVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void e(int r27, java.lang.String r28, byte r29, java.lang.Object[] r30) {
        /*
            Method dump skipped, instructions count: 1050
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cx.c.e(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
