package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.math.ec.ECCurve;
import com.esotericsoftware.asm.Opcodes;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k1.smali */
public class k1 {
    public static int a(int i) {
        if (i < 2048) {
            return i >= 1024 ? 80 : 20;
        }
        if (i < 3072) {
            return Opcodes.IREM;
        }
        if (i >= 7680) {
            return i >= 15360 ? 256 : 192;
        }
        return 128;
    }

    public static int a(BigInteger bigInteger) {
        return a(bigInteger.bitLength());
    }

    public static int a(ECCurve eCCurve) {
        int fieldSize = (eCCurve.getFieldSize() + 1) / 2;
        if (fieldSize > 256) {
            return 256;
        }
        return fieldSize;
    }
}
