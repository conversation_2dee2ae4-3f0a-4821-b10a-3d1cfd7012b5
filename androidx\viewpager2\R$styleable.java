package androidx.viewpager2;

import android.R;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\viewpager2\R$styleable.smali */
public final class R$styleable {
    public static int ColorStateListItem_alpha = 3;
    public static int ColorStateListItem_android_alpha = 1;
    public static int ColorStateListItem_android_color = 0;
    public static int ColorStateListItem_android_lStar = 2;
    public static int ColorStateListItem_lStar = 4;
    public static int FontFamilyFont_android_font = 0;
    public static int FontFamilyFont_android_fontStyle = 2;
    public static int FontFamilyFont_android_fontVariationSettings = 4;
    public static int FontFamilyFont_android_fontWeight = 1;
    public static int FontFamilyFont_android_ttcIndex = 3;
    public static int FontFamilyFont_font = 5;
    public static int FontFamilyFont_fontStyle = 6;
    public static int FontFamilyFont_fontVariationSettings = 7;
    public static int FontFamilyFont_fontWeight = 8;
    public static int FontFamilyFont_ttcIndex = 9;
    public static int FontFamily_fontProviderAuthority = 0;
    public static int FontFamily_fontProviderCerts = 1;
    public static int FontFamily_fontProviderFetchStrategy = 2;
    public static int FontFamily_fontProviderFetchTimeout = 3;
    public static int FontFamily_fontProviderPackage = 4;
    public static int FontFamily_fontProviderQuery = 5;
    public static int FontFamily_fontProviderSystemFontFamily = 6;
    public static int GradientColorItem_android_color = 0;
    public static int GradientColorItem_android_offset = 1;
    public static int GradientColor_android_centerColor = 7;
    public static int GradientColor_android_centerX = 3;
    public static int GradientColor_android_centerY = 4;
    public static int GradientColor_android_endColor = 1;
    public static int GradientColor_android_endX = 10;
    public static int GradientColor_android_endY = 11;
    public static int GradientColor_android_gradientRadius = 5;
    public static int GradientColor_android_startColor = 0;
    public static int GradientColor_android_startX = 8;
    public static int GradientColor_android_startY = 9;
    public static int GradientColor_android_tileMode = 6;
    public static int GradientColor_android_type = 2;
    public static int RecyclerView_android_clipToPadding = 1;
    public static int RecyclerView_android_descendantFocusability = 2;
    public static int RecyclerView_android_orientation = 0;
    public static int RecyclerView_fastScrollEnabled = 3;
    public static int RecyclerView_fastScrollHorizontalThumbDrawable = 4;
    public static int RecyclerView_fastScrollHorizontalTrackDrawable = 5;
    public static int RecyclerView_fastScrollVerticalThumbDrawable = 6;
    public static int RecyclerView_fastScrollVerticalTrackDrawable = 7;
    public static int RecyclerView_layoutManager = 8;
    public static int RecyclerView_reverseLayout = 9;
    public static int RecyclerView_spanCount = 10;
    public static int RecyclerView_stackFromEnd = 11;
    public static int ViewPager2_android_orientation;
    public static int[] ColorStateListItem = {R.attr.color, R.attr.alpha, R.attr.lStar, 2130968619, 2130969082};
    public static int[] FontFamily = {2130969010, 2130969011, 2130969012, 2130969013, 2130969014, 2130969015, 2130969016};
    public static int[] FontFamilyFont = {R.attr.font, R.attr.fontWeight, R.attr.fontStyle, R.attr.ttcIndex, R.attr.fontVariationSettings, 2130969008, 2130969017, 2130969018, 2130969019, 2130969393};
    public static int[] GradientColor = {R.attr.startColor, R.attr.endColor, R.attr.type, R.attr.centerX, R.attr.centerY, R.attr.gradientRadius, R.attr.tileMode, R.attr.centerColor, R.attr.startX, R.attr.startY, R.attr.endX, R.attr.endY};
    public static int[] GradientColorItem = {R.attr.color, R.attr.offset};
    public static int[] RecyclerView = {R.attr.orientation, R.attr.clipToPadding, R.attr.descendantFocusability, 2130969001, 2130969002, 2130969003, 2130969004, 2130969005, 2130969087, 2130969241, 2130969270, 2130969277};
    public static int[] ViewPager2 = {R.attr.orientation};

    private R$styleable() {
    }
}
