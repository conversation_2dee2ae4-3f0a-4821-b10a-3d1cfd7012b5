package fr.antelop.sdk.digitalcard.transactioncontrol;

import android.content.Context;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import o.es.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControlUpdateRegister.smali */
public final class TransactionControlUpdateRegister {
    private final e innerTransactionControlUpdateRegister;

    public TransactionControlUpdateRegister(e eVar) {
        this.innerTransactionControlUpdateRegister = eVar;
    }

    public final TransactionControlUpdateRegister add(TransactionControlUpdate<?> transactionControlUpdate) {
        this.innerTransactionControlUpdateRegister.b(transactionControlUpdate.getInner());
        return this;
    }

    public final void commit(Context context, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        if (this.innerTransactionControlUpdateRegister.e().isEmpty()) {
            return;
        }
        new SecureTransactionControlUpdateCommit(this.innerTransactionControlUpdateRegister.a()).launch(context, new DefaultCustomerAuthenticatedProcessCallback() { // from class: fr.antelop.sdk.digitalcard.transactioncontrol.TransactionControlUpdateRegister.1
            @Override // fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback
            public CustomerAuthenticationPrompt buildCustomerAuthenticationPrompt(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationPromptBuilder customerAuthenticationPromptBuilder) {
                return customerAuthenticationPromptBuilder.build();
            }

            @Override // fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback
            public void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
            }

            @Override // fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback
            public void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onSuccess(null);
            }

            @Override // fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback
            public void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(antelopError);
            }

            @Override // fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback
            public void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
            }
        });
    }

    public final SecureTransactionControlUpdateCommit getSecureTransactionControlUpdateCommit() throws WalletValidationException {
        if (this.innerTransactionControlUpdateRegister.e().isEmpty()) {
            return null;
        }
        return new SecureTransactionControlUpdateCommit(this.innerTransactionControlUpdateRegister.a());
    }
}
