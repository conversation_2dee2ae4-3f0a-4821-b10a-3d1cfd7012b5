package bc.org.bouncycastle.math.ec.endo;

import bc.org.bouncycastle.math.ec.ECPoint;
import bc.org.bouncycastle.math.ec.PreCompInfo;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\EndoPreCompInfo.smali */
public class EndoPreCompInfo implements PreCompInfo {
    protected ECEndomorphism a;
    protected ECPoint b;

    public ECEndomorphism getEndomorphism() {
        return this.a;
    }

    public ECPoint getMappedPoint() {
        return this.b;
    }

    public void setEndomorphism(ECEndomorphism eCEndomorphism) {
        this.a = eCEndomorphism;
    }

    public void setMappedPoint(ECPoint eCPoint) {
        this.b = eCPoint;
    }
}
