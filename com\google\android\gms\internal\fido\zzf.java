package com.google.android.gms.internal.fido;

import android.os.Parcel;
import android.os.RemoteException;
import com.google.android.gms.common.api.Status;
import java.util.ArrayList;

/* compiled from: com.google.android.gms:play-services-fido@@20.0.1 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\internal\fido\zzf.smali */
public abstract class zzf extends zzb implements zzg {
    public zzf() {
        super("com.google.android.gms.fido.fido2.api.ICredentialListCallback");
    }

    @Override // com.google.android.gms.internal.fido.zzb
    protected final boolean zza(int i, Parcel parcel, Parcel parcel2, int i2) throws RemoteException {
        switch (i) {
            case 1:
                ArrayList zzb = zzc.zzb(parcel);
                zzc.zzc(parcel);
                zzb(zzb);
                break;
            case 2:
                Status status = (Status) zzc.zza(parcel, Status.CREATOR);
                zzc.zzc(parcel);
                zzc(status);
                break;
            default:
                return false;
        }
        parcel2.writeNoException();
        return true;
    }
}
