package org.apache.cordova;

import android.util.Pair;
import android.util.SparseArray;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\apache\cordova\CallbackMap.smali */
public class CallbackMap {
    private int currentCallbackId = 0;
    private SparseArray<Pair<CordovaPlugin, Integer>> callbacks = new SparseArray<>();

    public synchronized int registerCallback(CordovaPlugin receiver, int requestCode) {
        int mappedId;
        mappedId = this.currentCallbackId;
        this.currentCallbackId = mappedId + 1;
        this.callbacks.put(mappedId, new Pair<>(receiver, Integer.valueOf(requestCode)));
        return mappedId;
    }

    public synchronized Pair<CordovaPlugin, Integer> getAndRemoveCallback(int mappedId) {
        Pair<CordovaPlugin, Integer> callback;
        callback = this.callbacks.get(mappedId);
        this.callbacks.remove(mappedId);
        return callback;
    }
}
