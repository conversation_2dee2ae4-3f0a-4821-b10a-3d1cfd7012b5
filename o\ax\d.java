package o.ax;

import android.content.Context;
import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import java.lang.reflect.Method;
import o.a.f;
import o.bb.e;
import o.cf.i;
import o.cf.j;
import o.ei.c;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ax\d.smali */
public final class d extends o.y.b<b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static char[] c;
    private static int e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ax\d$b.smali */
    public interface b {
        void b(o.bb.d dVar);

        void e(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        b = 1;
        o();
        int i = e + 23;
        b = i % 128;
        int i2 = i % 2;
    }

    static void init$0() {
        $$d = new byte[]{21, -38, 51, PSSSigner.TRAILER_IMPLICIT};
        $$e = Opcodes.IF_ICMPLE;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.ax.d.$$d
            int r6 = r6 + 4
            int r8 = 122 - r8
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r8 = r7
            r3 = r1
            r4 = r2
            r7 = r6
            r1 = r0
            r0 = r9
            r9 = r8
            goto L37
        L17:
            r3 = r2
            r5 = r8
            r8 = r7
            r7 = r5
        L1b:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L37:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ax.d.m(int, short, short, java.lang.Object[]):void");
    }

    static void o() {
        c = new char[]{50863, 50710, 50750, 50728, 50731, 50749, 50726, 50727, 50724, 50723, 50723, 50730, 50704, 50706, 50723, 50720, 50709, 50705, 50748, 50749, 50727, 50727, 50721, 50799, 50788, 50793, 50790};
    }

    public d(Context context, b bVar, c cVar) {
        super(context, bVar, cVar, e.m);
    }

    public final void k() {
        int i = b + 11;
        e = i % 128;
        char c2 = i % 2 != 0 ? (char) 27 : '=';
        c();
        switch (c2) {
            case 27:
                throw null;
            default:
                return;
        }
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        a aVar = new a(this);
        int i = b + 67;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                return aVar;
            default:
                throw null;
        }
    }

    @Override // o.y.b
    public final String a() {
        Object obj;
        int i = e + 75;
        b = i % 128;
        switch (i % 2 == 0 ? (char) 5 : (char) 16) {
            case 5:
                Object[] objArr = new Object[1];
                l("\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000", new int[]{0, 23, Opcodes.LXOR, 0}, false, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                l("\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0001\u0000", new int[]{0, 23, Opcodes.LXOR, 0}, false, objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = e + 83;
        b = i2 % 128;
        int i3 = i2 % 2;
        return intern;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ax\d$a.smali */
    static final class a extends o.y.c<d> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static short[] b;
        private static int c;
        private static byte[] d;
        private static int e;
        private static int g;
        private static int j;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            g = 1;
            d = new byte[]{-69, -89, -69, 79, 74, 116, 73, 117, 74, 74, 117, 77, 79, 75, 74, 79, 113, 74, 113, 79, 112, -34, -32, -54, -63, -24, -34, -52, -36, -6, -15, -49, -53, -32, -85, -50, -1, -62, -104, -70, -124, -101, -94, -104, -103, -72, -91, 126, UtilitiesSDKConstants.SRP_LABEL_MAC, -108, UtilitiesSDKConstants.SRP_LABEL_MAC, -119, -123, -84, 102, -119, -104, -118, UtilitiesSDKConstants.SRP_LABEL_ENC, -24, -21, 29, -9, 11, -8, -29, -19, 17, -3, -20, -112, -112, -112, -112, -112};
            c = 909053632;
            a = 1254638938;
            e = -760072807;
        }

        private static void B(byte b2, short s, byte b3, Object[] objArr) {
            byte[] bArr = $$d;
            int i = 110 - (s * 2);
            int i2 = (b3 * 4) + 4;
            int i3 = (b2 * 2) + 1;
            byte[] bArr2 = new byte[i3];
            int i4 = -1;
            int i5 = i3 - 1;
            if (bArr == null) {
                i2++;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = -1;
                i = (-i5) + i2;
                i5 = i5;
            }
            while (true) {
                int i6 = i4 + 1;
                bArr2[i6] = (byte) i;
                if (i6 == i5) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b4 = bArr[i2];
                i2++;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = i6;
                i = (-b4) + i;
                i5 = i5;
            }
        }

        static void init$0() {
            $$d = new byte[]{30, 126, 60, -105};
            $$e = Opcodes.F2D;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = j + 3;
            g = i % 128;
            switch (i % 2 == 0 ? '2' : '\'') {
                case '2':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = j + Opcodes.LMUL;
            g = i % 128;
            int i2 = i % 2;
        }

        a(d dVar) {
            super(dVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = g + 1;
            j = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), 459457783 - View.combineMeasuredStates(0, 0), (short) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) - 50), TextUtils.lastIndexOf("", '0', 0, 0) - 75, (-2095532890) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = g + 19;
            j = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w((byte) TextUtils.getCapsMode("", 0, 0), Color.blue(0) + 459457786, (short) (34 - KeyEvent.normalizeMetaState(0)), (Process.myPid() >> 22) - 61, (-2095532951) + (ViewConfiguration.getEdgeSlop() >> 16), objArr);
            o.cf.d dVar = new o.cf.d(context, 3, ((String) objArr[0]).intern());
            int i = g + 53;
            j = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w((byte) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 459457804 - (ViewConfiguration.getKeyRepeatDelay() >> 16), (short) ((-93) - (Process.myPid() >> 22)), (-63) - TextUtils.lastIndexOf("", '0'), (-2095532894) + (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
            bVar.d(((String) objArr[0]).intern(), f().e().g());
            Object[] objArr2 = new Object[1];
            w((byte) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 459457821, (short) ((-22) - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (-58) - View.MeasureSpec.getMode(0), (-2095532900) - TextUtils.indexOf("", ""), objArr2);
            bVar.d(((String) objArr2[0]).intern(), true);
            int i = j + Opcodes.LSUB;
            g = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return bVar;
                default:
                    int i2 = 5 / 0;
                    return bVar;
            }
        }

        @Override // o.y.c
        public final void b(o.eg.b bVar) throws o.eg.d {
            int i = g + 77;
            j = i % 128;
            int i2 = i % 2;
            o.eg.b e2 = f().a().e();
            Object[] objArr = new Object[1];
            w((byte) (AndroidCharacter.getMirror('0') - '0'), 459457842 - Color.green(0), (short) ((-122) - View.MeasureSpec.makeMeasureSpec(0, 0)), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 67, (-2095532893) - (ViewConfiguration.getTapTimeout() >> 16), objArr);
            bVar.d(((String) objArr[0]).intern(), e2);
            int i3 = j + 87;
            g = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // o.y.c
        public final void s() {
            o.dc.c.b(g());
            f().a().f().e(new o.dd.e(g()));
            int i = g + 29;
            j = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final j n() {
            int i = g;
            int i2 = i + 39;
            j = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0 ? '*' : 'J') {
                case '*':
                    throw null;
                default:
                    int i3 = i + Opcodes.DSUB;
                    j = i3 % 128;
                    switch (i3 % 2 != 0) {
                        case true:
                            obj.hashCode();
                            throw null;
                        default:
                            return null;
                    }
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = j + 39;
            int i2 = i % 128;
            g = i2;
            switch (i % 2 == 0 ? 'L' : (char) 24) {
                case 24:
                    break;
                default:
                    int i3 = 44 / 0;
                    break;
            }
            int i4 = i2 + 51;
            j = i4 % 128;
            switch (i4 % 2 != 0 ? 'Y' : '\f') {
                case '\f':
                    return null;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final void q() {
            f().a().f().e(new o.dd.e(g()));
            int i = j + 85;
            g = i % 128;
            switch (i % 2 == 0 ? (char) 1 : '-') {
                case 1:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = g + 15;
            j = i % 128;
            int i2 = i % 2;
            ((d) e()).j().e(dVar);
            int i3 = g + 39;
            j = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = j + 85;
            g = i % 128;
            int i2 = i % 2;
            ((d) e()).j().b(dVar);
            int i3 = g + 51;
            j = i3 % 128;
            switch (i3 % 2 != 0) {
                case false:
                    return;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        private static void w(byte b2, int i, short s, int i2, int i3, Object[] objArr) {
            boolean z;
            int i4;
            int i5;
            f fVar = new f();
            StringBuilder sb = new StringBuilder();
            try {
                Object[] objArr2 = {Integer.valueOf(i2), Integer.valueOf(c)};
                Object obj = o.e.a.s.get(-2120899312);
                long j2 = 0;
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(11 - ExpandableListView.getPackedPositionGroup(0L), (char) ExpandableListView.getPackedPositionType(0L), 65 - (ViewConfiguration.getJumpTapTimeout() >> 16));
                    byte b3 = (byte) 0;
                    byte b4 = (byte) (b3 + 1);
                    Object[] objArr3 = new Object[1];
                    B(b3, b4, (byte) (b4 - 1), objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(-2120899312, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                switch (intValue == -1 ? '(' : 'b') {
                    case Opcodes.FADD /* 98 */:
                        z = false;
                        break;
                    default:
                        int i6 = $11 + 25;
                        int i7 = i6 % 128;
                        $10 = i7;
                        int i8 = i6 % 2;
                        int i9 = i7 + 83;
                        $11 = i9 % 128;
                        int i10 = i9 % 2;
                        z = true;
                        break;
                }
                if (z) {
                    byte[] bArr = d;
                    switch (bArr == null ? (char) 14 : 'b') {
                        case Opcodes.FADD /* 98 */:
                            int length = bArr.length;
                            byte[] bArr2 = new byte[length];
                            int i11 = 0;
                            while (i11 < length) {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr[i11])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(20 - (SystemClock.uptimeMillis() > j2 ? 1 : (SystemClock.uptimeMillis() == j2 ? 0 : -1)), (char) (KeyEvent.keyCodeFromString("") + 16425), 150 - (ViewConfiguration.getMinimumFlingVelocity() >> 16));
                                        byte b5 = (byte) 0;
                                        byte b6 = b5;
                                        Object[] objArr5 = new Object[1];
                                        B(b5, b6, b6, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr2[i11] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i11++;
                                    j2 = 0;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            }
                            bArr = bArr2;
                        default:
                            switch (bArr != null) {
                                case true:
                                    int i12 = $10 + 65;
                                    $11 = i12 % 128;
                                    if (i12 % 2 == 0) {
                                        byte[] bArr3 = d;
                                        try {
                                            Object[] objArr6 = {Integer.valueOf(i), Integer.valueOf(e)};
                                            Object obj3 = o.e.a.s.get(-2120899312);
                                            if (obj3 == null) {
                                                Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 10, (char) (1 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 64);
                                                byte b7 = (byte) 0;
                                                byte b8 = (byte) (b7 + 1);
                                                Object[] objArr7 = new Object[1];
                                                B(b7, b8, (byte) (b8 - 1), objArr7);
                                                obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                                o.e.a.s.put(-2120899312, obj3);
                                            }
                                            i5 = ((byte) (bArr3[((Integer) ((Method) obj3).invoke(null, objArr6)).intValue()] % (-5810760824076169584L))) >>> ((int) (c - 5810760824076169584L));
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } else {
                                        byte[] bArr4 = d;
                                        try {
                                            Object[] objArr8 = {Integer.valueOf(i), Integer.valueOf(e)};
                                            Object obj4 = o.e.a.s.get(-2120899312);
                                            if (obj4 == null) {
                                                Class cls4 = (Class) o.e.a.c(View.resolveSizeAndState(0, 0, 0) + 11, (char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 65 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                                                byte b9 = (byte) 0;
                                                byte b10 = (byte) (b9 + 1);
                                                Object[] objArr9 = new Object[1];
                                                B(b9, b10, (byte) (b10 - 1), objArr9);
                                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                                o.e.a.s.put(-2120899312, obj4);
                                            }
                                            i5 = ((byte) (bArr4[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L)));
                                        } catch (Throwable th3) {
                                            Throwable cause3 = th3.getCause();
                                            if (cause3 == null) {
                                                throw th3;
                                            }
                                            throw cause3;
                                        }
                                    }
                                    intValue = (byte) i5;
                                    break;
                                default:
                                    intValue = (short) (((short) (b[i + ((int) (e ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (c ^ (-5810760824076169584L))));
                                    int i13 = $10 + 61;
                                    $11 = i13 % 128;
                                    int i14 = i13 % 2;
                                    break;
                            }
                    }
                }
                if (intValue > 0) {
                    int i15 = ((i + intValue) - 2) + ((int) (e ^ (-5810760824076169584L)));
                    switch (!z) {
                        case false:
                            int i16 = $11 + 91;
                            $10 = i16 % 128;
                            int i17 = i16 % 2;
                            i4 = 1;
                            break;
                        default:
                            int i18 = $11 + 83;
                            $10 = i18 % 128;
                            int i19 = i18 % 2;
                            i4 = 0;
                            break;
                    }
                    fVar.d = i15 + i4;
                    try {
                        Object[] objArr10 = {fVar, Integer.valueOf(i3), Integer.valueOf(a), sb};
                        Object obj5 = o.e.a.s.get(160906762);
                        if (obj5 == null) {
                            obj5 = ((Class) o.e.a.c(10 - Process.getGidForName(""), (char) KeyEvent.normalizeMetaState(0), MotionEvent.axisFromString("") + 604)).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj5);
                        }
                        ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr5 = d;
                        if (bArr5 != null) {
                            int length2 = bArr5.length;
                            byte[] bArr6 = new byte[length2];
                            for (int i20 = 0; i20 < length2; i20++) {
                                bArr6[i20] = (byte) (bArr5[i20] ^ (-5810760824076169584L));
                            }
                            bArr5 = bArr6;
                        }
                        boolean z2 = bArr5 != null;
                        fVar.c = 1;
                        while (fVar.c < intValue) {
                            int i21 = $10 + Opcodes.LUSHR;
                            $11 = i21 % 128;
                            int i22 = i21 % 2;
                            if (z2) {
                                byte[] bArr7 = d;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr7[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                            } else {
                                short[] sArr = b;
                                fVar.d = fVar.d - 1;
                                fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b2));
                            }
                            sb.append(fVar.e);
                            fVar.b = fVar.e;
                            fVar.c++;
                        }
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
                objArr[0] = sb.toString();
            } catch (Throwable th5) {
                Throwable cause5 = th5.getCause();
                if (cause5 == null) {
                    throw th5;
                }
                throw cause5;
            }
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:21:0x0194  */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v2 */
    /* JADX WARN: Type inference failed for: r0v27, types: [byte[]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 998
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ax.d.l(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
