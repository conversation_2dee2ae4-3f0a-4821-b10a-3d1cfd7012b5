package com.getcapacitor;

import android.webkit.RenderProcessGoneDetail;
import android.webkit.WebView;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\WebViewListener.smali */
public abstract class WebViewListener {
    public void onPageLoaded(WebView webView) {
    }

    public void onReceivedError(WebView webView) {
    }

    public void onReceivedHttpError(WebView webView) {
    }

    public void onPageStarted(WebView webView) {
    }

    public boolean onRenderProcessGone(WebView webView, RenderProcessGoneDetail detail) {
        return false;
    }
}
