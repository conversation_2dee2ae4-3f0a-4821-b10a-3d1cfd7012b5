package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.CryptoException;
import bc.org.bouncycastle.crypto.Digest;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d7.smali */
public class d7 {
    protected BigInteger a;
    protected BigInteger b;
    protected BigInteger c;
    protected BigInteger d;
    protected BigInteger e;
    protected BigInteger f;
    protected BigInteger g;
    protected BigInteger h;
    protected BigInteger i;
    protected BigInteger j;
    protected BigInteger k;
    protected Digest l;
    protected SecureRandom m;

    private BigInteger a() {
        BigInteger calculateK = g7.calculateK(this.l, this.a, this.b);
        return this.e.subtract(this.b.modPow(this.f, this.a).multiply(calculateK).mod(this.a)).mod(this.a).modPow(this.g.multiply(this.f).add(this.c), this.a);
    }

    protected BigInteger b() {
        return g7.generatePrivateValue(this.l, this.a, this.b, this.m);
    }

    public BigInteger calculateClientEvidenceMessage() throws CryptoException {
        BigInteger bigInteger;
        BigInteger bigInteger2;
        BigInteger bigInteger3 = this.d;
        if (bigInteger3 == null || (bigInteger = this.e) == null || (bigInteger2 = this.h) == null) {
            throw new CryptoException("Impossible to compute M1: some data are missing from the previous operations (A,B,S)");
        }
        BigInteger calculateM1 = g7.calculateM1(this.l, this.a, bigInteger3, bigInteger, bigInteger2);
        this.i = calculateM1;
        return calculateM1;
    }

    public BigInteger calculateSecret(BigInteger bigInteger) throws CryptoException {
        BigInteger validatePublicValue = g7.validatePublicValue(this.a, bigInteger);
        this.e = validatePublicValue;
        this.g = g7.calculateU(this.l, this.a, this.d, validatePublicValue);
        BigInteger a = a();
        this.h = a;
        return a;
    }

    public BigInteger calculateSessionKey() throws CryptoException {
        BigInteger bigInteger = this.h;
        if (bigInteger == null || this.i == null || this.j == null) {
            throw new CryptoException("Impossible to compute Key: some data are missing from the previous operations (S,M1,M2)");
        }
        BigInteger calculateKey = g7.calculateKey(this.l, this.a, bigInteger);
        this.k = calculateKey;
        return calculateKey;
    }

    public BigInteger generateClientCredentials(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        this.f = g7.calculateX(this.l, this.a, bArr, bArr2, bArr3);
        BigInteger b = b();
        this.c = b;
        BigInteger modPow = this.b.modPow(b, this.a);
        this.d = modPow;
        return modPow;
    }

    public void init(BigInteger bigInteger, BigInteger bigInteger2, Digest digest, SecureRandom secureRandom) {
        this.a = bigInteger;
        this.b = bigInteger2;
        this.l = digest;
        this.m = secureRandom;
    }

    public boolean verifyServerEvidenceMessage(BigInteger bigInteger) throws CryptoException {
        BigInteger bigInteger2;
        BigInteger bigInteger3;
        BigInteger bigInteger4 = this.d;
        if (bigInteger4 == null || (bigInteger2 = this.i) == null || (bigInteger3 = this.h) == null) {
            throw new CryptoException("Impossible to compute and verify M2: some data are missing from the previous operations (A,M1,S)");
        }
        if (!g7.calculateM2(this.l, this.a, bigInteger4, bigInteger2, bigInteger3).equals(bigInteger)) {
            return false;
        }
        this.j = bigInteger;
        return true;
    }

    public void init(e7 e7Var, Digest digest, SecureRandom secureRandom) {
        throw null;
    }
}
