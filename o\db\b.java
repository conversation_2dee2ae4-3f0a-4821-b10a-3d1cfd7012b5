package o.db;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import java.nio.ByteBuffer;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;
import o.av.a;
import o.db.c;
import o.de.d;
import o.de.f;
import o.de.h;
import o.de.j;
import o.ee.g;
import o.ee.o;
import o.ei.i;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\db\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final b c;
    private static int k;
    private static char[] l;
    private static int m;
    private static long n;
    private d a;
    private o.de.b b;
    private boolean f;
    private a h;
    private boolean i;
    private final h d = new h();
    private final o.df.a e = new o.df.a();
    private final j g = new j();
    private AtomicBoolean j = null;

    static void c() {
        char[] cArr = new char[3137];
        ByteBuffer.wrap("\u009cÜ\u0083\u0099¢\u0002Â\u008fá\u001a\u0001\u0085 &@ªg-\u0087\u0094¦6ÆÃåJ\u0005Æ$ZDÇ,»3Ö\u0012LrÒQD±Ø\u0090aðÍ×|7î\u0016bv\u0083U\u0014µ¶\u0094\u0004ô\u0081Û\b;´\u001a8zªY$¹¥\u0098ÁÿlßÕ>L\u001eÙ,»3Ö\u0012LrÒQD±Ø\u0090aðÍ×|7î\u0016bv\u0083U\u0014µ¶\u0094\u0004ô\u0081Û\b;´\u001a8zªY$¹¥\u0098ÁÿlßÕ>L\u001eÙ}7]¼¼;\u009cñã}Ãè\"\u001d\u0002\u008ea\u0006A\u0082 \u001f\u0080¼ç0Ç·&c\u0006¤eÄD\u0011¤Ô\u008bKë\u008fÊN*ü\ttiéHf¨«\u008fOïßÎ\u000b.\u0086\r\u0000m¸L%¬¾³e\u0093¢ò(Ñ]1Ü\u0010@pÄWF·ð\u0096qöíÕ#5ä\u0014\u0004tÑ[\u001d»\u008a\u009a\u001dú\u008bÙ:9¹\u0018#x¤_%,½3Á\u0012\\rÉQR±Ê\u0090vðë×p7ì\u0016cv×U\u0018µ\u0088\u0094Eô\u0080Û\u0007;ó\u001a:z¨Y(¹¥\u0098Òÿ\u001fß\u0083>\u0003\u001eß}r]ü¼t\u009cñãjÃ©\"\u001e\u0002\u009ca\tA\u0080 \f\u0080°ç2Ç¼&-\u0006¹e\u0097DX¤È\u008b\u0005ëÉÊF*á\t\u007fiîHe¨ï\u008f\u0010ï\u0091¸\"§O\u0086ÕæKÅÝ%A\u0004ødTCå£w\u0082ûâ\u001aÁ\u008d!/\u0000\u009d`\u0018O\u0091¯-\u008e¡î3Í½-<\fXkõKLªÕ\u008a@é®É%(¢\b}wøW\u007f¶\u009e\u0096\fõ\u009bÕ\n4Ò\u0014>s£S-²µ\u0092 ñKÐ\u00880O\u001fÝ\u007fX^Ñ¾m\u009dáýsÜý<|\u001b\u0098{FZ\u0089º\t\u0099Ôù!Ø¦8b'»\u00079f¹EÄ¥C\u0084\u009eä\u0002Ã\u0092#~\u0002ãbmAõ¡`\u0080\u008bàHÏ\u008f/\u001d\u000e\u0098n\u0011M\u00ad\u00ad!\u008c³ì=Ë¼*Ø\n\u0006iÉII¨\u0094\u0088h÷ç×p6þ\u0016\u007fuôU\u000e´\u0081\u0094\u0010\u000bJ\u0014'5¡U9v¤\u0096?·Ä×\u0003ð\u0089\u0010\u001c1\u009dQarå\u0092g³ñÓpüì\u001c\u0002=Å]E~\u0090\u009eU¿*Øîø/\u0019½95Z\u0088z\u0007\u009bÊ»NÄÞä\n\u0005ç%aFùfd\u0087ÿ§\u0004ÀÃàI\u0001Ü!]B!c¥\u0083'¬±Ì0í¬\rB.\u0085N\u0005oÐ\u008f\u001c¨ëÈ|éê\t{*øJBkÅ\u008bD,»3Ö\u0012LrÒQD±Ø\u0090aðÍ×|7î\u0016bv\u0083U\u0014µ¶\u0094\u0004ô\u0081Û\b;´\u001a8zªY$¹¥\u0098ÁÿlßÕ>L\u001eÙ}7]¼¼;\u009c÷ãjÃä\"\u001c\u0002\u0089a\u0002AÁ \u0006\u0080´ç1Ç¸&$\u0006¨eÚDT¤Õ\u008bQë\u008fÊ@*à\t=iæHm¨ç\u008f\u001aï\u0088Î\u001c.\u0087,¬3Ý\u0012YrõQD±Æ\u0090zðë×|7Î\u0016lv\u0099U\u0010µ\u009c\u0094\u0000ô\u0082Û\f;½\u001a)z\u0094Y-¹¤\u0098Á\u000en\u0011\u00150¿P\u0000s\u008b\u00930²©Ò6õ£\u0015?u\u0002jyKÓ+l\bçè\\ÉÅ©Z\u008eÏnSO\u0089/~\fõì5Í®\u00ad)\u0082íb\u0005C\u009c#\u0010\u0000\u0091à\u000eÁc¦ï\u0086xgãG)$Õ\u0004GåÐÅLº\u008b\u009a^{®[*8·\u0018 ù¢ÙQ¾\u0090\u009e\u0014\u007f\u008b_\u0005<;#@\u0002êbUAÞ¡s\u0080æàf\u0012ç\r\u009c,6L\u0089o\u0002\u008f¯®:Îºéx\tï(lHÁkQ\u008bÖªHÊËå\\\u0005²$uDõg \u0087ä¦\u009bÁ\náØ\u0000\u0003 \u008fC\"c¹\u0082,¢¡Ýbýè\u001c@<Ù_U\u007fÐ\u009eE¾úÙmùý\u0018\"8ÿ[\u0082z\u0011\u009a\u008eµ\u0011Õ\u009dôH\u0014è7|Wãv3Àqß\nþ \u009e\u001f½\u0094]9|¬\u001c,;îÛyúú\u009aJ¹ÉYNx\u0092\u0018P7ß×wöª\u0096rµóUyt\f\u0013È3\u0005Ò\u009dò\u0016\u0091¬±#P¨pr\u000fº/'Î\u0084îY\u008dÉ\u00adELÈlg\u000bå,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u0098U\u001fµ¼\u0094\nô\u0086Û\u0007;´\u001a\u0017z¨Y#¹ë\u0098ÜÿLß\u0099>M\u001eØ}{]ý¼;\u009c¿ã/Ãè\"\u0011\u0002\u0092a\u0015A\u0095 \u0002\u0080»ç8Çù&3\u0006¿eØDR¤Þ\u008bVëÜÊ@*ý\tz,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u009dU\u001eµ\u0099\u0094Eô\u0087Û\b; \u001a}z¥Y$¹®\u0098Ûÿ\u001fßÛ>O\u001eÂ}t]ú¼~\u009cáã/Ãë\"\n\u0002Ýa(A\u0091 \u000e\u0080§ç>Ç\u00ad&*\u0006¢eÙD\u001d¤\u009b\u008bWëÊÊN*ú\tnióHd¨ù\u008f\u001cï\u0091Î\u001e.Ã\r\u0004m£L\"¬û³1\u0093¶ò9ÑV,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u009dU\u001eµ\u0099\u0094Eô\u009dÛ\f;§\u001a/z¾Ya¹¹\u0098ÐÿNßÌ>J\u001eß}r]õÜ]Ã&â\u008c\u00823¡¸A\u0015`\u0080\u0000\u0000'ÂÇUæÖ\u0086~¥ïEtdì\u0004m+²ËFêÓ\u008aQ©ØIUh<\u000fä/xÎøîs\u008d\u009f\u00adJLÍl^\u0013\u00863\u0017Òüòt\u0091å±:PôpK\u0017È7CÖÁö\u0016\u0095d´¹T%{½\u001b}:òÚRùÆ\u0099Y¸\u0089XP\u007f£\u001f$>ðÞ}ýâ\u009d^¼Ó\\\u0000CÍcW\u0002Ú!\u00adÁ\"à©\u00806§µG\nfÄ\u0006\u0003%\u008cÅVä©\u0084y,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u009dU\u001eµ\u0099\u0094Eô\u009dÛ\f;§\u001a/z¾Ya¹¥\u0098ÚÿKß\u0099>Q\u001eÈ}f]ä¼r\u009c÷ãjÃí\"q=\n\u001c |\u001f_\u0094¿9\u009e¬þ,Ùî9y\u0018úxR[Ã»X\u009aÀúAÕ\u009e5w\u0014þtbW÷·h\u0096\u0007ñ\u008fÑ\u00170Ô\u0010\bs¥S+²\u00ad\u0092;í¶Í-,\u0084\f^oØOS®\u009c\u008eqééÉc(ñ\b:k\u0001J\u0080ª\u0018\u0085\u0097å\nÄÞ$.\u0007¥g2Fö¦?\u0081ÍáEÀÞ X\u0003ßctBï¢c½ü\u009d8ü¤ßÄ,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u0085U\u0014µ\u008f\u0094\u0017ô\u0096ÛI; \u001a)zµY ¹¿\u0098ÐÿXßÀ>\u0003\u001eÅ}v]â¼;\u009cæãgÃè\"\u001d\u0002\u009aa\u0002A\u0085 K\u0080¢ç6Ç\u00ad&+\u0006íeÝD^¤Ù\u008b\u0005ëÌÊF*þ\tmiëHd¨ÿ\u008f\u001cï\u0090Î\u0017.Ã\rWm÷,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u0080U\u0010µ\u0092\u0094\u0011ô\u0086Û\u0007;´\u001a}z\u00adY.¹©\u0098\u0095ÿ[ßÜ>W\u001eÈ}t]å¼~\u009cá,¦3Ý\u0012wrÈQC±î\u0090{ðû×97®\u0016-v\u0087U\u001eµ\u0088\u0094\u0011ôÏÛ\f;«\u001a8z¤Y4¹¿\u0098ÜÿPß×>\u0003\u001eÇ}x]ó¼;\u009cñã`Ã©\"\u0011\u0002\u0098aGA\u0092 \u000e\u0080»ç;\\íC\u0096b<\u0002\u0083!\bÁ¥à0\u0080°§rGåff\u0006Ì%_ÅÞäJ\u0084Í«LKÿj6\nü)xÉïè\u0088\u008f\u001d¯\u0081N\u0001n\u0089\r2-úÌ$ì¡\u0093/³§RVr\u0096\u0011X1ÅÐ\u0000ðü\u0097q·²V{vã\u0015\u00924\u000eUÆJ½k\u0017\u000b¨(#È\u008eé\u001b\u0089\u009b®YNÎoM\u000fù,tÌìí%\u008då¢fBÑc\u001d\u0003Ô BÀÃá°\u0086;¦¬G/g¨\u0004\u0013$ÑÅ\u001aå\u0091\u009aO,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5åùú\u008aÛ\u000b»\u009b\u0098\u0015x\u0096Y\u00029·\u001e$þ©ß4¿Û\u009c@|Ì]S=å\u0012TòøÓp³ó\u0090mp³QÀ6G\u0016\u0096÷\u001a×\u0099´#\u0094¬u7Uý*>\n¢ë\u000bËË¨P\u0088Íi\u0013Iì.d\u000eõïrÏã¬\u008a\u008dEmÃB\u000f\"\u0092\u0003\u0002ã»À* ±\u0081*a¶F\r&Ô\u0007UçÚÄA¤ú\u0085ze£z'Z·;4\u0018\u0018\u0002é\u001d\u009a<\u001b\\\u008b\u007f\u0005\u009f\u0086¾\u0012Þ§ù4\u0019¹8$XË{P\u009bÜºCÚõõD\u0015è4`Tãw}\u0097£¶ÐÑWñ\u0094\u0010\u00130\u0095S6s«\u00926²©Ígí«\fT,×O\u000foÌ\u008eM®þÉxéä\be(ñK\u009aj\u000b\u008a\u0096¥\tÅÇäL\u0004û'&G»f(\u0086±¡IÁ\u0097àU\u0000Ê#QCúb9\u0082ä\u009dl½ôÜ!ÿ^\u001f\u0086>O^ÄyC\u0099¯¸2Ø¢û?\u001bª:MZÐu]\u0095Ê´\u0007ÔÈ÷o,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5¹ë\u0098\u0098ÿ\u001fßÖ>S\u001eÈ}e]ð¼o\u009cìã`Ãç\"S\u0002\u008aa\u0006A\u0092 K\u0080´ç\u007fÇ\u00ad&&\u0006¬eÅDT¤ß\u008b\u0005ëÛÊ[*ò\tsiôH`¨è\u008f\u0001ï\u0096Î\u0016.\u008d\rMmíLq¬µ³*\u0093ïò;ÑV1É\u0010UpØW\u000b·æ\u0096köëÕb5ù\u0014\u0012t\u0096[\u0002»Å\u009a\u001aú\u0099Ù79¼\u00183x¤,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5¹ë\u0098\u0098ÿ\u001fßË>F\u001eÙ}e]è¼;\u009cöã{Ãû\"\u0012\u0002\u0089a\u0002A\u0086 \u0012\u0080õç-Ç¼&.\u0006¬eÞD_¤È\u008b\u0005ëÛÊA*ö\t=iôH`¨æ\u008f\u0010ïßÎ\u0018.\u0085\r\u0019m²L#¬û³*\u0093¿ò,ÑA1Ü\u0010SpÈWD·û\u0096?öúÕl5à\u0014\u0007t\u009d[\u001e»\u0091\u009a\u0006ú\u0086Ù=9ý\u0018oxä_8¾\u001c,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5¹ë\u0098\u0098ÿ\u001fß×>F\u001eÚ}7]ã¼~\u009cñã}Ãð\"S\u0002\u008ea\u0013A\u0093 \n\u0080¡ç:Ç¾&:\u0006íeÅDT¤×\u008bDëÛÊL*÷\t=iíHn¨é\u008fUï\u008cÎ\u001a.\u008b\r\bm³L$¬·³,\u0093¡ò.Ñ\u00131Ï\u0010BpÐW^·ð\u0096löíMÂR±s0\u0013 0.Ð\u00adñ9\u0091\u008c¶\u001fV\u0092w\u000f\u0017à4{Ô÷õh\u0095ÞºoZÃ{K\u001bÈ8VØ\u0088ùû\u009e|¾¨_%\u007fº\u001c\u0006<\u008bÝXý\u0095\u0082\u0018¢\u0098Cqcê\u0000a åÁqá\u0096\u0086T¦ÛGSg\u008e\u0004·%:Å¹ê(\u008a««/K\u0094h^\b\u0085)\u0004É\u009cîs\u008eî¯:Oïl~\fÑ-@ÍÙÒRòÅ\u0093E°>Pþq'\u0011\u00ad6%Ö\u0086÷\u0010\u0097\u009f´\u0014T\u0087u{\u0015ü:8Ú¼û,¥\u0005ºv\u009b÷ûgØé8j\u0019þyK^Ø¾U\u009fÈÿ'Ü¼<0\u001d¯}\u0019R¨²\u0004\u0093\u008có\u000fÐ\u00910O\u0011<v»Vo·â\u0097}ôÁÔL5\u009f\u0015RjßJ_«¶\u008b-è¦È\")¶\tQn\u0093N\u001c¯\u0094\u008fIìpÍý-~\u0002ïblCè£S\u0080\u0099àBÁÃ![\u0006´f)Gý§(\u0084¹ä\u0016Å\u0087%\u001e:\u0095\u001a\u0002{\u0082Xù¸9\u0099àùjÞâ>A\u001f×\u007fX\\Ó¼@\u009d¼ý;Òÿ2 \u0013¥s)P×°\n\u0091\u0080ñ\rÖ\u008a7õ\u0017ntñTbµí\u0095\u0013êßÊP+Ã\u000b\u000bhÄH$©ù\u00891î Î#/\u0090\u000f\u000fl\u0098L\u0003\u00adÉ\u0082çâzÃ¿#u\u0000ã`hA\u0097¡V\u0086ÏæAÇ\u008f'#\u0004¾d)Eµ¥0ºÓ\u009a\u0006û\u008bÛ\u00138\u008a\u0018\u0019yr^þ¾z\u009f¥ÿ5Ü\u0091<X\u001dÜ}IRÊ²6\u0093¹ó3Ð¨0%\u0011ªqWV\u0090¶\u0017,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5¹ë\u0098\u0098ÿ\u001fßÉ>L\u001eÞ}c]±¼~\u009cýãjÃê\"\u0006\u0002\u0089a\u000eA\u008e \u0005\u0080õç5Ç¶&!\u0006íeÃD^¤\u009b\u008bGëÊÊ\t*÷\triéHd,¡3Ò\u0012SrÃQM±Î\u0090Zðï×|7ñ\u0016lv\u0083U\u0018µ\u0094\u0094\u000bô½Û\f; \u001a(z«Y5¹ë\u0098\u0098ÿ\u001fßÛ>O\u001eÂ}t]ú¼~\u009cáã/Ãã\"\u001c\u0002\u009faGA\u0085 \u000e\u0080¡ç:Çº&7\u0006¨eÓ'\u00898ú\u0019{yëZeºæ\u009brûÇÜT<Ù\u001dD}«^0¾¼\u009f#ÿ\u0095Ð$0\u0088\u0011\u0000q\u0083R\u001d²Ã\u0093°ô7Ôã5n\u0015âvVVÊ·G\u0097ÈèUÈÈ)5\t²joJ¨«6\u008b\u0089ì\u001fÌ\u0094-\u0005\r\u0091nöOz¯ò\u0080yàîÁn!Õ\u0002Fb\u008fC]£Ì\u0084}ä¤Å9%ª\u00067f\u009aGY§É¸M,äaè~\u009b_\u001a?\u008a\u001c\u0004ü\u0087Ý\u0013½¦\u009a5z¸[%;Ê\u0018QøÝÙB¹ô\u0096EvéWa7â\u0014|ô¢ÕÑ²V\u0092\u0093s\u0006S\u00810?\u0010¶ñ;Ñ¢®!\u008eàoIOÀ,G\fËíIÍåª6\u008aþkeKð(\u0097\t\u001eé\u009bÆ\u000f¦\u0087\u0087\u0014g³D;$ \u0005håøÂ\u001cEÖZ¥{$\u001b´8:Ø¹ù-\u0099\u0098¾\u000b^\u0086\u007f\u001b\u001fô<oÜãý|\u009dÊ²{R×s_\u0013Ü0BÐ\u009cñï\u0096h¶\u0099W5w¶\u0014\f4\u0083Õ\u0018õ±\u008a\u0017ª\u0090Kbkã\bw(ãÉnéÃ\u008e\\®ÇO[oÔ\f\u0085->Í¯â7\u0082¨£*C\u008d`\u0005\u0000\u009e!VÁÆ,º3Ð\u0012UrÂQE±Þ\u0090yðú×S7ì\u0016ov×U\\µÛ\u0094\u0016ô\u008cÛ\u0001;¶\u001a9z²Y-¹¢\u0098ÛÿXß\u0099>@\u001eÅ}r]ò¼p\u009c¥ãzÃù\"\u0017\u0002\u009ca\u0013A\u0084 K\u0080¼ç,Çù&-\u0006¢eÃD\u0011¤Ú\u008bIëÃÊF*ä\txiã,º3Ð\u0012UrÂQE±Þ\u0090yðú×S7ì\u0016ov×U\\µÛ\u0094\u000fô\u0080Û\u000b;ó\u001a2z¤Y\"¹¾\u0098ÇÿMßÜ>M\u001eÎ}r]±¼x\u009céã`Ãú\"\u0016\u0002Ýa\u0002A\u008f \u0004\u0080 ç8Ç±&o\u0006íeÃDC¤Â\u008bLëÁÊN*³\tiièH!¨þ\u008f\u0006ï\u009aÎY.ª\r\u0000mºL4¬¿³,\u0093®ò=ÑV1ñ\u0010NpÇWN·ö\u0096föúÕo5è\u0014=t\u009e[\u0019»·\u009a\u001aú\u0087Ù=9¸\u00185,º3Ð\u0012UrÂQE±Þ\u0090yðú×S7ì\u0016ov×U\\µÛ\u0094,ô\u0082Û\u0004;¶\u001a9z®Y ¹¿\u0098ÐÿsßÐ>E\u001eÈ}t]è¼x\u009céãjÃÃ\"\u001c\u0002\u009fa5A\u0094 \u0005\u0080»ç:Ç«&c\u0006®eÖD_¤Õ\u008bJëÛÊ\t*ñ\txi§Ht¨ø\u008f\u0010ï\u009bÎU.Ã\r\u000bm¶L=¬·³'\u0093®ò*ÑX1\u009d\u0010RpÒWB·û\u0096xö¹ÕG5è\u0014\u001bt\u0090[\u0002»\u0080\u009a\u000bú¥Ù:9»\u0018\"x¢_2¾V\u009eÓý\\Ýé<B\u001cõcCCî¢k\u0082áálÁ\u0081,º3Ð\u0012UrÂQE±Þ\u0090yðú×S7ì\u0016ov×U\\µÛ\u0094\u000fô\u0080Û\u000b;ó\u001a2z¤Y\"¹¾\u0098ÇÿMßÜ>M\u001eÎ}r]±¼x\u009céã`Ãú\"\u0016\u0002Ýa\u0002A\u008f \u0004\u0080 ç8Ç±&c\u0006¯eÂDE¤\u009b\u008blëÂÊD*ö\tyiîH`¨ÿ\u008f\u0010ï³Î\u0010.\u0085\r\bm´L(¬¸³)\u0093ªò\u0003Ñ\\1ß\u0010upÔWE·û\u0096zöëÕ#5ã\u0014\u0018tÑ[\u001a»\u0089\u009a\u0003ú\u0086Ù$9¸\u0018#xí_k¾S\u009eÞýUÝÏ<O\u001cöcrCð¢%\u0082úázÁ\u009a \u0013\u0000\u0080gAG¯¦0\u0086³å8Åº$(\u000bSkýJRªÃ\u0089JéÊÈj(þ\u000fkoäNA®\u009a\u008d\u001dí«Ì\u0016,\u008339\u0013´r)N\u001bQvpì\u0010r3äÓxòÁ\u0092uµÖUAtþ\u001447¹×>ö¡\u0096:¹¥Y\u001ax\u0093\u0018\u0000;ÁÛFú5\u009dñ½|\\ô|-\u001fÅ?TÞÊþP\u0081Ê¡Z@§`}\u0003ê#aÂ¿â\f\u0085\u008f¥\u001cDÃdW\u00077&´Æhé¥\u0089\"¨©HGkÏ\u000bN*ÆÊLí°\u008d-¬ùLyoí\u000fR.\u0082,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿUßÖ>A\u001e\u008d}~]â¼;\u009cêãaÃ©\"\u0014\u0002\u0092a\u000eA\u008f \f\u0080õçeÇù&6\u0006½eÓDP¤Ï\u008bLëÁÊN*³\tjiæHh¨ÿ\u008f\u001cï\u0091Î\u001e.Ã\r\u0007m¸L3¬û³4\u0093ºò,ÑF1Ø,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿQßÖ>\u0003\u001eÇ}x]ó¼;\u009cêãaÃ©\"\u0014\u0002\u0092a\u000eA\u008f \f\u0080õçeÇù&6\u0006½eÓDP¤Ï\u008bLëÁÊN*³\tniäHi¨î\u008f\u0011ï\u008aÎ\u0015.\u0086\r\tm÷L;¬´³'\u0093ïò8ÑF1Ø\u0010RpÄ,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿZßÁ>S\u001eÄ}e]ô¼\u007f\u009c¥ãeÃæ\"\u0011\u0002Ýa\u0002A\u008f \b\u0080ºç*Ç·&7\u0006¨eÅDT¤ß\u008b\u0005ë\u0082Ê\t*à\tiiæHs¨ÿ\u008fUï\u009bÎ\u0018.\u0097\r\bm÷L&¬º³6\u0093ïòlÑ@1\u0091\u0010\u0007pÓWN·æ\u0096kööÕq5ä\u0014\u0019t\u0096[[»\u008c\u009a\u001b,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿQßÖ>\u0003\u001eÇ}x]ó¼;\u009cöãlÃá\"\u0016\u0002\u0099a\u0012A\u008d \u000e\u0080±ç\u007fÇã&c\u0006¾eÔDY¤Þ\u008bAëÚÊE*ú\tsiàH!¨å\u008f\u0010ï\u0088ÎY.\u0089\r\u0002mµ,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿQßÜ>T\u001e\u008d}}]þ¼y\u009c¥ã|Ãê\"\u001b\u0002\u0098a\u0003A\u0094 \u0007\u0080°ç;Çù&\"\u0006¹e\u0097,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿKßÑ>F\u001e\u008d}d]ò¼s\u009càãkÃü\"\u001f\u0002\u0098a\u0003AÁ \u0001\u0080ºç=Çù&+\u0006¬eÄD\u0011¤Ù\u008b@ëÊÊG*³\thi÷He¨ê\u008f\u0001ï\u009aÎ\u001d.Ã\r@m÷L?¬¾³2\u0093ïò#Ñ\\1ß\u0010\u0007pÒWH·ý\u0096zöýÕv5á\u0014\u0012t\u0095[[»\u0084\u009a\u001búÉ,»3Ö\u0012LrÒQD±Ø\u0090aðÕ×v7á\u0016^v\u0094U\u0019µ\u009e\u0094\u0001ô\u009aÛ\u0005;º\u001a3z Ya¹æ\u0098\u0095ÿKßÑ>F\u001e\u008d}d]ò¼s\u009càãkÃü\"\u001f\u0002\u0098a\u0003AÁ \u0001\u0080ºç=Çù&+\u0006¬eÄD\u0011¤Õ\u008bJëÛÊ\t*ñ\txiâHo¨«\u008f\u0000ï\u008fÎ\u001d.\u0082\r\u0019m²L5\u0099\u0015\u0086x§âÇ|äê\u0004v%ÏEbbÔ\u0082E£ÆÃ=àª\u00009!¢A/n \u008e]¯ÞÏIì\u009d\f\u0000-jJäjr\u008bþ«wÈóèP\t×)xVÂvO\u0097¸·7Ô¼ô#\u0015¬5\u0015R\u0096rW\u0093×H\u0015Wmvì\u0016v5ñÕ`ô\u008a\u0094M³ÃSNrÕ\u0012-1îÑ.ðµ\u00902¿ö_\u001b~\u008b\u001e\f=\u0096ÝTük\u009b »cZäzw\u0019Ë9[ØÐøS\u0087ß§XFìf&\u0005½%2Äµä\u0013\u0083À£\bB\u0093b\u0006\u0001( ýÀaïî,¯3ß\u0012HrÔQI±ä\u0090eðú×k7â\u0016yv\u009eU\u001eµ\u0095\u0094\u0016,ª3Û\u0012XrÄQJ±þ\u0090eðû×x7÷\u0016hv¥U\u0014µ\u0088\u0094\u0015ô\u0080Û\u0007; \u001a8z\u0097Y3¹¤\u0098ÖÿZßÊ>P\u001eÄ}y]ö¼T\u009cëãHÃæ\"\u001a\u0002\u0093a\u0000|\u0002clB¾\"k\u0001âárÀÝ ^\u0087Ûg^F\u008e&;\u0005²å\"Ä\u00ad¤.\u008b«k\u000eJ\u0098*\t\t\u0089é\nÈq¯ð\u008ffnïNr-Ã\rcìÅÌ@³Ã\u0093Vr»R41\u0095\u0011<ð´Ð\u001d·\u0094\u0097\u0011v\u009cV\u00055t\u0014ÿôsÛû,¹3Á\u0012RrÄQD±Ø\u0090fðö×w7ä\u0016Bv\u0099U6µ\u0094\u0094\fô\u0081Û\u000e{Sd,E¶%)\u0006ºæ!Çª§\u0002\u0080\u0099`\u000fA\u0087!k\u0002íâ}Ãä£g\u008cÉlRMõ-G\u000eÇîJÏ=¨ð\u0088{iì".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 3137);
        l = cArr;
        n = 2059482877252875187L;
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = 21;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.db.b.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r8 = r8 + 102
            int r9 = r9 * 3
            int r9 = r9 + 4
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L17:
            r3 = r2
        L18:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r9]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r8 = r8 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.b.p(short, int, byte, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        m = 0;
        k = 1;
        c();
        KeyEvent.normalizeMetaState(0);
        ViewConfiguration.getScrollBarSize();
        AudioTrack.getMaxVolume();
        c = new b();
        int i = m + 83;
        k = i % 128;
        switch (i % 2 == 0 ? 'R' : '\f') {
            case '\f':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private b() {
    }

    public static b a() {
        int i = m + 19;
        int i2 = i % 128;
        k = i2;
        int i3 = i % 2;
        b bVar = c;
        int i4 = i2 + 11;
        m = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                throw null;
            default:
                return bVar;
        }
    }

    public final synchronized void d() throws c {
        int i = m + 73;
        k = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        o((char) (KeyEvent.normalizeMetaState(0) + 45146), ViewConfiguration.getScrollDefaultDelay() >> 16, 15 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o((char) (ViewConfiguration.getEdgeSlop() >> 16), MotionEvent.axisFromString("") + 17, 26 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (this.i) {
            g.c();
            Object[] objArr3 = new Object[1];
            o((char) (TextUtils.lastIndexOf("", '0', 0) + 45147), (-1) - TextUtils.indexOf((CharSequence) "", '0', 0, 0), Drawable.resolveOpacity(0, 0) + 16, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            o((char) (38041 - View.MeasureSpec.makeMeasureSpec(0, 0)), TextUtils.lastIndexOf("", '0') + Opcodes.INVOKEDYNAMIC, TextUtils.getCapsMode("", 0, 0) + 100, objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            c.EnumC0035c enumC0035c = c.EnumC0035c.c;
            Object[] objArr5 = new Object[1];
            o((char) (10225 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 285, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 61, objArr5);
            throw new c(enumC0035c, ((String) objArr5[0]).intern());
        }
        g.c();
        Object[] objArr6 = new Object[1];
        o((char) (MotionEvent.axisFromString("") + 45147), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1, 16 - Color.green(0), objArr6);
        String intern3 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        o((char) KeyEvent.keyCodeFromString(""), 346 - TextUtils.lastIndexOf("", '0', 0, 0), TextUtils.lastIndexOf("", '0', 0) + 59, objArr7);
        g.d(intern3, ((String) objArr7[0]).intern());
        this.i = true;
        int i3 = m + 109;
        k = i3 % 128;
        int i4 = i3 % 2;
    }

    public final synchronized void d(Context context, o.bb.d dVar, o.bv.g gVar) {
        g.c();
        Object[] objArr = new Object[1];
        o((char) (45146 - (ViewConfiguration.getTouchSlop() >> 8)), Color.blue(0), 16 - ExpandableListView.getPackedPositionGroup(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), MotionEvent.axisFromString("") + 406, KeyEvent.normalizeMetaState(0) + 23, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        this.i = false;
        d(context, dVar);
        gVar.b(context);
        Iterator<d> it = gVar.h().iterator();
        while (true) {
            switch (it.hasNext() ? (char) 15 : ')') {
                case 15:
                    e(context, it.next(), true);
                default:
                    switch (gVar.g() == null) {
                        case false:
                            int i = k + Opcodes.LSUB;
                            m = i % 128;
                            int i2 = i % 2;
                            switch (gVar.f() != null) {
                                case false:
                                    o.dc.a.b(context, gVar.g());
                                    int i3 = k + 45;
                                    m = i3 % 128;
                                    int i4 = i3 % 2;
                                    break;
                                default:
                                    int i5 = m + Opcodes.LUSHR;
                                    k = i5 % 128;
                                    int i6 = i5 % 2;
                                    o.dc.a.e(gVar.g(), gVar.f());
                                    return;
                            }
                    }
                    return;
            }
        }
    }

    public final synchronized void d(Context context, a aVar, f fVar) {
        int i = k + 75;
        m = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        o((char) (45145 - TextUtils.lastIndexOf("", '0')), TextUtils.indexOf("", "", 0), 16 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o((char) (MotionEvent.axisFromString("") + 8905), (ViewConfiguration.getScrollBarSize() >> 8) + 428, 10 - TextUtils.getOffsetBefore("", 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.de.b b = this.d.b(context);
        this.b = b;
        if (b == null) {
            g.c();
            Object[] objArr3 = new Object[1];
            o((char) (45146 - TextUtils.getOffsetAfter("", 0)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 16 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            o((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 22947), 438 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), Process.getGidForName("") + 44, objArr4);
            g.d(intern2, ((String) objArr4[0]).intern());
            this.b = new o.de.b(new Date(), aVar, this.e.d(context), fVar, false);
            this.e.e(context);
            int i3 = k + Opcodes.LSHR;
            m = i3 % 128;
            switch (i3 % 2 != 0 ? 'D' : 'N') {
            }
        }
        this.d.c(context);
    }

    /* JADX WARN: Removed duplicated region for block: B:203:0x031b A[Catch: all -> 0x080a, i -> 0x080d, TryCatch #0 {i -> 0x080d, blocks: (B:7:0x0032, B:9:0x0064, B:10:0x0067, B:14:0x0073, B:15:0x0076, B:16:0x00ec, B:18:0x00f0, B:33:0x015e, B:35:0x0162, B:48:0x01be, B:55:0x01ec, B:58:0x01f8, B:60:0x01fc, B:64:0x021d, B:65:0x0220, B:66:0x0229, B:68:0x0230, B:69:0x03e1, B:71:0x03ec, B:73:0x0428, B:74:0x044c, B:75:0x0451, B:78:0x0465, B:80:0x049c, B:84:0x04ba, B:85:0x04bf, B:87:0x04c5, B:89:0x04d0, B:93:0x0530, B:96:0x053a, B:99:0x06e3, B:117:0x0543, B:119:0x0549, B:146:0x05eb, B:170:0x063f, B:201:0x02d4, B:203:0x031b, B:204:0x03a1, B:207:0x03b2, B:208:0x03b5, B:210:0x03bf, B:211:0x03c6, B:215:0x0360, B:219:0x0780, B:220:0x0786, B:221:0x0787, B:249:0x007b), top: B:6:0x0032, outer: #4 }] */
    /* JADX WARN: Removed duplicated region for block: B:206:0x03ad  */
    /* JADX WARN: Removed duplicated region for block: B:208:0x03b5 A[Catch: all -> 0x080a, i -> 0x080d, TryCatch #0 {i -> 0x080d, blocks: (B:7:0x0032, B:9:0x0064, B:10:0x0067, B:14:0x0073, B:15:0x0076, B:16:0x00ec, B:18:0x00f0, B:33:0x015e, B:35:0x0162, B:48:0x01be, B:55:0x01ec, B:58:0x01f8, B:60:0x01fc, B:64:0x021d, B:65:0x0220, B:66:0x0229, B:68:0x0230, B:69:0x03e1, B:71:0x03ec, B:73:0x0428, B:74:0x044c, B:75:0x0451, B:78:0x0465, B:80:0x049c, B:84:0x04ba, B:85:0x04bf, B:87:0x04c5, B:89:0x04d0, B:93:0x0530, B:96:0x053a, B:99:0x06e3, B:117:0x0543, B:119:0x0549, B:146:0x05eb, B:170:0x063f, B:201:0x02d4, B:203:0x031b, B:204:0x03a1, B:207:0x03b2, B:208:0x03b5, B:210:0x03bf, B:211:0x03c6, B:215:0x0360, B:219:0x0780, B:220:0x0786, B:221:0x0787, B:249:0x007b), top: B:6:0x0032, outer: #4 }] */
    /* JADX WARN: Removed duplicated region for block: B:213:0x03ba  */
    /* JADX WARN: Removed duplicated region for block: B:214:0x03b0  */
    /* JADX WARN: Removed duplicated region for block: B:215:0x0360 A[Catch: all -> 0x080a, i -> 0x080d, TryCatch #0 {i -> 0x080d, blocks: (B:7:0x0032, B:9:0x0064, B:10:0x0067, B:14:0x0073, B:15:0x0076, B:16:0x00ec, B:18:0x00f0, B:33:0x015e, B:35:0x0162, B:48:0x01be, B:55:0x01ec, B:58:0x01f8, B:60:0x01fc, B:64:0x021d, B:65:0x0220, B:66:0x0229, B:68:0x0230, B:69:0x03e1, B:71:0x03ec, B:73:0x0428, B:74:0x044c, B:75:0x0451, B:78:0x0465, B:80:0x049c, B:84:0x04ba, B:85:0x04bf, B:87:0x04c5, B:89:0x04d0, B:93:0x0530, B:96:0x053a, B:99:0x06e3, B:117:0x0543, B:119:0x0549, B:146:0x05eb, B:170:0x063f, B:201:0x02d4, B:203:0x031b, B:204:0x03a1, B:207:0x03b2, B:208:0x03b5, B:210:0x03bf, B:211:0x03c6, B:215:0x0360, B:219:0x0780, B:220:0x0786, B:221:0x0787, B:249:0x007b), top: B:6:0x0032, outer: #4 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final synchronized boolean b(android.content.Context r33, o.bb.d r34, o.av.a r35, boolean r36, o.bv.g r37) {
        /*
            Method dump skipped, instructions count: 2284
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.b.b(android.content.Context, o.bb.d, o.av.a, boolean, o.bv.g):boolean");
    }

    private void d(Context context, o.bb.d dVar) {
        Object obj;
        d dVar2;
        f fVar;
        Object[] objArr = new Object[1];
        o((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 45146), TextUtils.getOffsetBefore("", 0), Gravity.getAbsoluteGravity(0, 0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        try {
            try {
                g.c();
                Object[] objArr2 = new Object[1];
                o((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (-16776104) - Color.rgb(0, 0, 0), TextUtils.indexOf("", "", 0) + 21, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                obj = null;
            } catch (i e) {
                g.c();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                o((char) (26999 - ExpandableListView.getPackedPositionType(0L)), ((Process.getThreadPriority(0) + 20) >> 6) + 1970, Color.green(0) + 54, objArr3);
                g.e(intern, sb.append(((String) objArr3[0]).intern()).append(e.getMessage()).toString());
                d(context);
                if (!dVar.a().b()) {
                    return;
                }
            }
            if (b(dVar)) {
                int i = k + 95;
                m = i % 128;
                int i2 = i % 2;
                g.c();
                Locale a = o.ee.j.a();
                Object[] objArr4 = new Object[1];
                o((char) (51543 - TextUtils.indexOf((CharSequence) "", '0')), 1133 - (ViewConfiguration.getLongPressTimeout() >> 16), MotionEvent.axisFromString("") + 67, objArr4);
                g.d(intern, String.format(a, ((String) objArr4[0]).intern(), dVar.d()));
                d(context);
                if (dVar.a().b()) {
                    int i3 = m + 93;
                    k = i3 % 128;
                    if (i3 % 2 != 0) {
                        o.ei.c.c();
                        o.ei.c.p();
                        return;
                    } else {
                        o.ei.c.c();
                        o.ei.c.p();
                        obj.hashCode();
                        throw null;
                    }
                }
                return;
            }
            o.ei.c c2 = o.ei.c.c();
            o.de.b b = this.d.b(context);
            if (b != null && b.a().before(new Date())) {
                int i4 = m + 37;
                k = i4 % 128;
                int i5 = i4 % 2;
                g.c();
                Locale a2 = o.ee.j.a();
                Object[] objArr5 = new Object[1];
                o((char) (TextUtils.getOffsetAfter("", 0) + 11848), TextUtils.getOffsetBefore("", 0) + 1199, TextUtils.indexOf("", "") + 82, objArr5);
                g.d(intern, String.format(a2, ((String) objArr5[0]).intern(), b.a()));
            }
            o.df.e d = this.e.d(context);
            if (dVar.c() == o.bb.e.d && dVar.d() == o.bb.a.aq) {
                int i6 = m + 7;
                k = i6 % 128;
                int i7 = i6 % 2;
                g.c();
                Object[] objArr6 = new Object[1];
                o((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 1280, 85 - (Process.myPid() >> 22), objArr6);
                g.d(intern, ((String) objArr6[0]).intern());
            } else {
                this.e.e(context, dVar);
            }
            o.df.e d2 = this.e.d(context);
            if (d.getClass().equals(d2.getClass())) {
                g.c();
                Locale a3 = o.ee.j.a();
                Object[] objArr7 = new Object[1];
                o((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 1365 - TextUtils.lastIndexOf("", '0'), 87 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr7);
                g.d(intern, String.format(a3, ((String) objArr7[0]).intern(), d2.a()));
                g.c();
                Object[] objArr8 = new Object[1];
                o((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 1453 - (ViewConfiguration.getTapTimeout() >> 16), 73 - (ViewConfiguration.getTouchSlop() >> 8), objArr8);
                g.d(intern, ((String) objArr8[0]).intern());
                dVar2 = new d(a.d, d2.c(dVar));
            } else {
                g.c();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr9 = new Object[1];
                o((char) (24931 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 1526 - KeyEvent.normalizeMetaState(0), 79 - TextUtils.lastIndexOf("", '0', 0, 0), objArr9);
                g.d(intern, sb2.append(((String) objArr9[0]).intern()).append(d2.a()).toString());
                switch (b != null) {
                    case false:
                        break;
                    default:
                        int i8 = m + 51;
                        k = i8 % 128;
                        int i9 = i8 % 2;
                        switch (!d.b(b.c())) {
                            case false:
                                g.c();
                                Object[] objArr10 = new Object[1];
                                o((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 35236), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 1605, 148 - KeyEvent.keyCodeFromString(""), objArr10);
                                g.d(intern, ((String) objArr10[0]).intern());
                                c(context);
                                break;
                        }
                }
                dVar2 = new d(a.d, d2.c(dVar));
            }
            f a4 = dVar.a().a();
            if (a4 != null) {
                g.c();
                Object[] objArr11 = new Object[1];
                o((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 1754 - KeyEvent.getDeadChar(0, 0), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 52, objArr11);
                g.d(intern, ((String) objArr11[0]).intern());
                dVar2 = a(context, dVar2, new d(a.d, a4), c2);
            }
            if (this.h != null) {
                g.c();
                Object[] objArr12 = new Object[1];
                o((char) TextUtils.getOffsetBefore("", 0), 1806 - TextUtils.indexOf((CharSequence) "", '0', 0), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 44, objArr12);
                g.d(intern, ((String) objArr12[0]).intern());
                switch (dVar.c() == o.bb.e.d ? (char) 0 : 'T') {
                    case Opcodes.BASTORE /* 84 */:
                        fVar = f.x;
                        int i10 = m + Opcodes.DNEG;
                        k = i10 % 128;
                        int i11 = i10 % 2;
                        break;
                    default:
                        if (!dVar.b()) {
                            fVar = f.t;
                            break;
                        } else {
                            fVar = f.r;
                            break;
                        }
                }
                dVar2 = a(context, dVar2, new d(this.h, fVar), c2);
                this.h = null;
            }
            e(context, dVar2, false);
            if (dVar.a().f() != null) {
                g.c();
                StringBuilder sb3 = new StringBuilder();
                Object[] objArr13 = new Object[1];
                o((char) (2856 - Color.green(0)), Color.red(0) + 1851, Color.alpha(0) + 63, objArr13);
                StringBuilder append = sb3.append(((String) objArr13[0]).intern());
                Object[] objArr14 = new Object[1];
                o((char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), 1914 - (ViewConfiguration.getJumpTapTimeout() >> 16), 1 - (Process.myTid() >> 22), objArr14);
                g.d(intern, append.append(o.d(((String) objArr14[0]).intern(), (Iterator) dVar.a().f().iterator())).toString());
                new o.t.c().b(dVar.a().f());
            }
            if (dVar.a().g() != null) {
                g.c();
                StringBuilder sb4 = new StringBuilder();
                Object[] objArr15 = new Object[1];
                o((char) (19784 - TextUtils.lastIndexOf("", '0', 0, 0)), TextUtils.getTrimmedLength("") + 1915, 55 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr15);
                g.d(intern, sb4.append(((String) objArr15[0]).intern()).append(dVar.a().g()).toString());
                o.dc.c.d(context, dVar.a().g());
            }
            if (dVar.a().b()) {
                int i12 = k + 95;
                m = i12 % 128;
                int i13 = i12 % 2;
                o.ei.c.c();
                o.ei.c.p();
            }
        } catch (Throwable th) {
            if (dVar.a().b()) {
                o.ei.c.c();
                o.ei.c.p();
            }
            throw th;
        }
    }

    private void a(Context context, o.de.b bVar) {
        this.e.a(context);
        Date a = bVar.a();
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(13, 10);
        if (a.before(calendar.getTime()) && bVar.c() != f.l) {
            if (bVar.e()) {
                g.c();
                Object[] objArr = new Object[1];
                o((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 45147), (-1) - ExpandableListView.getPackedPositionChild(0L), View.getDefaultSize(0, 0) + 16, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                o((char) TextUtils.getCapsMode("", 0, 0), View.MeasureSpec.makeMeasureSpec(0, 0) + 2076, TextUtils.indexOf("", "", 0) + 84, objArr2);
                g.d(intern, ((String) objArr2[0]).intern());
                new o.de.e();
                switch (o.de.e.c(context, bVar, String.valueOf(new Date().getTime()))) {
                    case false:
                        g.c();
                        Object[] objArr3 = new Object[1];
                        o((char) (45146 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), Color.green(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 16, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        Object[] objArr4 = new Object[1];
                        o((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), 2160 - (ViewConfiguration.getWindowTouchSlop() >> 8), 97 - TextUtils.indexOf((CharSequence) "", '0'), objArr4);
                        g.d(intern2, ((String) objArr4[0]).intern());
                        break;
                    default:
                        int i = m + 83;
                        k = i % 128;
                        switch (i % 2 == 0 ? (char) 21 : '.') {
                            case '.':
                                this.d.c(context, bVar);
                                return;
                            default:
                                this.d.c(context, bVar);
                                throw null;
                        }
                }
            } else {
                g.c();
                Object[] objArr5 = new Object[1];
                o((char) (ExpandableListView.getPackedPositionGroup(0L) + 45146), 1 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), ((Process.getThreadPriority(0) + 20) >> 6) + 16, objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                o((char) (ViewConfiguration.getLongPressTimeout() >> 16), 2259 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), 125 - TextUtils.indexOf((CharSequence) "", '0'), objArr6);
                g.d(intern3, ((String) objArr6[0]).intern());
            }
        }
        new o.de.c();
        o.de.c.c(context, bVar, String.valueOf(new Date().getTime()));
        this.d.c(context, bVar);
        int i2 = k + 59;
        m = i2 % 128;
        switch (i2 % 2 != 0 ? '\\' : 'N') {
            case 'N':
                return;
            default:
                int i3 = 66 / 0;
                return;
        }
    }

    private void c(Context context) {
        new o.de.c();
        new Date().getTime();
        o.de.c.d(context);
        new o.de.e();
        new Date().getTime();
        o.de.e.b(context);
        this.d.c(context);
        int i = k + 39;
        m = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:47:0x01d5, code lost:
    
        if (r6 != false) goto L43;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x0223, code lost:
    
        o.ee.g.c();
        r8 = new java.lang.Object[1];
        o((char) (android.view.ViewConfiguration.getWindowTouchSlop() >> 8), (android.view.ViewConfiguration.getMaximumFlingVelocity() >> 16) + 2846, 61 - (android.os.Process.myTid() >> 22), r8);
        o.ee.g.d(r4, ((java.lang.String) r8[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x01db, code lost:
    
        if (r6 != false) goto L43;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final synchronized void e(android.content.Context r21, o.de.d r22, boolean r23) {
        /*
            Method dump skipped, instructions count: 752
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.b.e(android.content.Context, o.de.d, boolean):void");
    }

    private d a(Context context, d dVar, d dVar2, o.ei.c cVar) throws i {
        int i = m + 29;
        k = i % 128;
        switch (i % 2 != 0) {
            case true:
                long a = j.a(context, cVar, dVar.e());
                long a2 = j.a(context, cVar, dVar2.e());
                if (a == -1 || a2 == -2) {
                    Object[] objArr = new Object[1];
                    o((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 25790), (ViewConfiguration.getTapTimeout() >> 16) + 2949, 47 - (ViewConfiguration.getEdgeSlop() >> 16), objArr);
                    throw new i(((String) objArr[0]).intern());
                }
                if (a >= a2) {
                    return new d(dVar2.c(), dVar2.e());
                }
                d dVar3 = new d(dVar.c(), dVar.e());
                int i2 = m + 17;
                k = i2 % 128;
                int i3 = i2 % 2;
                return dVar3;
            default:
                j.a(context, cVar, dVar.e());
                j.a(context, cVar, dVar2.e());
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    private synchronized void d(Context context) {
        int i = k + Opcodes.LSHL;
        m = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        o((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 45146), Drawable.resolveOpacity(0, 0), 16 - ExpandableListView.getPackedPositionType(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 2996, 15 - Color.red(0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        switch (this.d.b(context) != null) {
            case true:
                int i3 = m + 73;
                k = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        c(context);
                        this.b = null;
                        this.a = null;
                        this.h = null;
                        this.e.b(context);
                        break;
                    default:
                        c(context);
                        int i4 = 94 / 0;
                        this.b = null;
                        this.a = null;
                        this.h = null;
                        this.e.b(context);
                        break;
                }
            default:
                this.b = null;
                this.a = null;
                this.h = null;
                this.e.b(context);
                break;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0030, code lost:
    
        if (r4 != o.bb.a.m) goto L25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x00a7, code lost:
    
        if (r4 == o.bb.a.ak) goto L74;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0070, code lost:
    
        if (r4 != o.bb.a.ae) goto L45;
     */
    /* JADX WARN: Removed duplicated region for block: B:73:0x00b0 A[FALL_THROUGH] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static boolean b(o.bb.d r4) {
        /*
            Method dump skipped, instructions count: 232
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.b.b(o.bb.d):boolean");
    }

    public final boolean b(Context context) {
        int i = k + 61;
        m = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        o((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 45147), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1, Gravity.getAbsoluteGravity(0, 0) + 16, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        o((char) (ViewConfiguration.getScrollBarSize() >> 8), 3011 - View.MeasureSpec.getMode(0), Color.argb(0, 0, 0, 0) + 36, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (this.j == null) {
            Object[] objArr3 = new Object[1];
            o((char) (View.resolveSize(0, 0) + 20653), (ViewConfiguration.getEdgeSlop() >> 16) + 3047, 47 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr3);
            SharedPreferences sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
            Object[] objArr4 = new Object[1];
            o((char) Color.red(0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 3094, TextUtils.getOffsetAfter("", 0) + 17, objArr4);
            this.j = new AtomicBoolean(sharedPreferences.getBoolean(((String) objArr4[0]).intern(), false));
        }
        boolean z = this.j.get();
        int i3 = k + 57;
        m = i3 % 128;
        int i4 = i3 % 2;
        return z;
    }

    public final void a(Context context, boolean z) {
        g.c();
        Object[] objArr = new Object[1];
        o((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 45147), ExpandableListView.getPackedPositionGroup(0L), 16 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        o((char) (Color.blue(0) + 22511), 3111 - View.combineMeasuredStates(0, 0), (ViewConfiguration.getFadingEdgeLength() >> 16) + 26, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(z).toString());
        Object[] objArr3 = new Object[1];
        o((char) (ImageFormat.getBitsPerPixel(0) + 20654), 3047 - (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 47, objArr3);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit();
        Object[] objArr4 = new Object[1];
        o((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), Drawable.resolveOpacity(0, 0) + 3094, TextUtils.getCapsMode("", 0, 0) + 17, objArr4);
        edit.putBoolean(((String) objArr4[0]).intern(), z).apply();
        AtomicBoolean atomicBoolean = this.j;
        if (atomicBoolean != null) {
            atomicBoolean.set(z);
            return;
        }
        this.j = new AtomicBoolean(z);
        int i = m + 109;
        k = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Removed duplicated region for block: B:2:0x000d A[LOOP_START] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 740
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.db.b.o(char, int, int, java.lang.Object[]):void");
    }
}
