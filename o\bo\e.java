package o.bo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.PointerIconCompat;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import com.google.firebase.FirebaseApp;
import com.google.firebase.installations.FirebaseInstallations;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.RemoteMessage;
import java.nio.ByteBuffer;
import java.util.Locale;
import java.util.Objects;
import kotlin.text.Typography;
import o.ee.n;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\e.smali */
public final class e extends o.bs.e<o.bs.b> implements b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long b;
    private static int c;
    private static char[] d;
    private static int h;
    private final FirebaseApp a;
    private final o.bi.a e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        h = 1;
        b();
        ViewConfiguration.getLongPressTimeout();
        SystemClock.elapsedRealtime();
        TypedValue.complexToFloat(0);
        MotionEvent.axisFromString("");
        TextUtils.lastIndexOf("", '0', 0);
        ViewConfiguration.getPressedStateDuration();
        KeyEvent.getModifierMetaStateMask();
        Process.myPid();
        KeyEvent.getModifierMetaStateMask();
        MotionEvent.axisFromString("");
        KeyEvent.getModifierMetaStateMask();
        PointF.length(0.0f, 0.0f);
        int i = h + 61;
        c = i % 128;
        switch (i % 2 != 0 ? '.' : ']') {
            case Opcodes.DUP2_X1 /* 93 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void b() {
        char[] cArr = new char[PointerIconCompat.TYPE_ZOOM_IN];
        ByteBuffer.wrap(",\u008fZ\u0082ÀÿNÊô#b\u0002èv\u0016B\u009d\u0089\u000b\u008e±î?×¥\u001fÓ\u001cYaÇ^N\u008fôâbÎè.\u0016\u0015\u009cj\nJ±©?ª¥þÓÏY)Ç\u0018MpûPb\u0087èû\u0016Ä\u009c;\n\u0006°e>F¥·ÒM¤~>\u000b°(\nÐ\u009cÙ\u0016\u009eè§cDõiO\u0000Á([Ä-ó§\u00889¬°s\n_\u009ct\u0016\u009bèûb\u0098ôñOWÁl[\u001b-(§\u008b9ì³\u0089\u0005á\u009cW\u0016\u0015è:b\u0099ôöN\u0090À¤[B-r§\n9*³\u0085\u0005«\u009f\u0084\u0011¥èNbqô)NÖÀ÷Z\u009c¬\u0086Úµ@ÀÎãt\u001bâ\u0012hU\u0096l\u001d\u008f\u008b¢1Ë¿ã%\u000fS8ÙCGgÎ¸t\u0094â¿hP\u00960\u001cS\u008an1\u0091¿ %ÍSáÙ\u0001G:ÍE{eâ\u0086h\u0096\u0096ä\u001c\u0013\u008a\"0M¾u%\u0094S¿Ù\u0086G÷Í\u0017{#áMoi\u0096\u0099\u001c»D¼2\u0091¨ì&Ù\u009c0\n\u0011\u0080e~Qq\u0005\u0007=\u009dx\u0013\u007f©\u0097?®µîKíÀ\bV7ì^bsø\u0097\u008e·\u0004Ä\u009aû\u0013#©@?[µ\u008fKªÁÂWåì\u0010b\u000føK\u008eh\u0004\u008a\u009a»\u0010Ä¦ä?\u0017µHK.ÁÅWêíÉcãø\u0013\u008e1\u0004]\u009ay\u0010\u009d¦ú<Ç²óK\u001eÁ6Wií\u009ccèùÃ\u008f÷\u0004F\u009a4\u0010J¦y<Þ²·HÔÞñWVí3c]ù\u0082\u008f¨\u0005Á\u009bí\u0010\u0011¦4<E²fHÜÞ¸T×êècTù\"\u008fX\u0005w\u009bì\u0011Ý§ì<\u0001, Z\u0098ÀÝNÚô2b\u000bèK\u0016H\u009d\u00ad\u000b\u0092±û?Ö¥2Ó\u0012YaÇ^N\u0086ôåbþè*\u0016\u000f\u009cg\n@±µ?ª¥îÓÍY/Ç\u001eMaûAb²èí\u0016\u008b\u009c`\nO°l>F¥¶Ó\u0094YøÇÜM8û_abïV\u0016»\u009c\u0093\nÌ°9>M¤fÒRYãÇ\u008bMèûÍa{ï\t\u0015w\u0083T\nó°\u009a>ù¤,ÒKXnÆ@M¯û\u0085aìïÀ\u0015,\u0083\t\tx·[>ñ¤\u0095ÒúXÅÆIL\u007fúEaªïÁ\u0015ð\u0083Á\t,Hò>Ò¤¥*µ\u0090t\u0006Q\u008c>r\u001eù÷o×Õ³[\u008aÁc·[= £\u0005*Ò\u0090\u009e\u0006\u0095\u008c3r\u0010ø\u007fn\u001eÕô[ÊÁ ·\u008d=f£\r)?\u009f\u0005\u0006ê\u008c¬r×øbnVÔ/Z\tÁð·Ø= £\u0094)!\u009fM\u0005\"\u008b\u001br©øÊn\u0083ÔvZXÀ?¶\u001c=ý£Õ)¾\u001c.j\u0004ðb~HÄ\u00adR\u0086Ø¥&×\u00ad5;\u001a\u0081d\u000f\u001f\u0095¢ã\u0096iç÷Á~\u0000ÄhRHØ¼&Á¬í:Ê\u00813\u000fY\u0095zãKi¾÷\u0098}ÿËÔR5Øe&N&æPÆÊ±D¡þ`hEâ*\u001c\n\u0097ã\u0001Ã»§5\u009e¯wÙOS4Í\u0011DÆþ\u008ah\u0081â'\u001c\u0004\u0096k\u0000\u0004»á5Ç¯²Ù\u0099S~Í]G{ñ\u001bhöâ³\u001c\u0086\u0096g\u0000Fº:4\u000e¯\u00adÙÌS¾Í\u009dGsñ^k>å\u000e\u001cï\u0096Þ\u0000\u0095ºj4J®), Z\u0085ÀûNÎô-b\nèa\u0016\u0007\u009d¿\u000b\u0092±ï?Ú¥3Ó\u0012YfÇRNÉôèbÂè!\u0016\u0007\u009cj\nB±²?\u008b¥úÓÉY6Ç\u001eM},ªZ\u008aÀãNÁô.b\u0017è%\u0016U\u009d¼\u000b\u0088±ò?Ó¥'Ó\u0016Y5ÇQN\u0080ôùbÈè-\u0016\u0000\u009cp\n@±ç?\u0090¥ÿ\u0084\u0099ò¸hÏæú\\\u001fÊ\u0001@\\¾z5\u008a££\u0019\u008b\u0097¤\rG{\"ñLonæ¸\\ÑÊþ@Y¾'4Y¢r\u0019\u0088\u0097ï\rÞ{îñ\u001bo1åLS`Ê\u0084@Ì¾½4\u0015¢6\u0018C\u00965\r\u0092{§ñÎoäå\u0007S(ÉEGi¾\u0086,¯Z\u008eÀùNÌô)b7èj\u0016L\u009d¼\u000b\u0095±½?\u0092¥qÓ\u001aY{ÇAN\u0088ôçbÄè+\u0016A\u009ce\nL±µ?\u009c¥ùÓÜY,Ç\u0014M3ûVb¸èç\u0016Í\u009c$\n\b°t>Q¥¤Ó\u0093YðÇÔM3,¯Z\u008eÀùNÌô)b7èj\u0016L\u009d¼\u000b\u0095±Â?Ö¥?Ó\u0007YpÇEN\u0087ôêbÁèo\u0016L\u009c#\nQ±¨?\u0092¥þÓÓY\u007fÇ\u0017MvûAb´èá\u0016Î\u009c)\nO°;>\u0003¥àÓ\u0094\u0000\u008bvªìÝbèØ\rN\u0013ÄN:h±\u0098'±\u009d\u0099\u0013¶\u0089Uÿ9u^ë3b¹ØÀNâÄ\u000e:+°\u0007&g\u009d\u0086\u0013©\u0089Üÿñu\u001eë1a\u001b×1N\u0086ÄÃ:ä°\u0007&$\u009cR\u0012i\u0089Áÿ¥uÜëöa\u0015×.MGÃr:Ñ°¡&è\u009c\u000e\u0012:\u0088Dþk,ªZ\u008aÀãNÁô.b\u0017è%\u0016U\u009d¼\u000b\u0088±ò?Ó¥'Ó\u0016Y5ÇQN\u0080ôùbÈè-\u0016\u0000\u009cp\n@±ç?\u008d¥ôÓÖY:Ç\u001f,¯Z\u0082ÀÿNÊô#b\u0002èv\u0016B,®Z\u008eÀùNéô(b\u0011è`\u0016E\u009d¸\u000b\u0088±ø?þ¥!Ó\u0003Y5Ç\u001aNÉôíbÄè=\u0016\u0004\u009ca\nD±´?\u009c¥»ÓÞY0Ç\u001fMuû\\b°èü\u0016Ù\u009c,\n\u001b°h>L¥«ÓÇY÷ÇÔM)û_abïV\u0016¡\u009aAìav\u0016ø\u0006BÇÔþ^\u008f ª+W½g\u0007\u0017\u0089\u0011\u0013Îeìï\u0096q±øeB\u0005Ô6^É á*\u0082¼ê\u0007\u0005\u00896\u0013\u001ae=ï\u0090qøû\u0095M¨Ô]^\u0004 %*Ñ¼å\u0006Î\u0088\u008d\u0013ZexïVq=ûÁM°×\u009aY¹ \\*q¼(\u0006Á\u0088æ\u0012Àd·ïIq~ûHMl×\u0094Yû£\u009e5·¼H\u0006s\u0088\u0019\u0012Êdíî\u0098p©û@Mk×JY<£Þ5ñ¿Ò\u0001´\u0088[\u0012zd\u001bî-pêú\u0090Lâ×OY`£\t!ÎWøÍÂC¯ùNovå\u0001\u001b*\u0090×\u0006ê¼Ò2¸¨YÞ`T\u0011Ê4Céù\u0099o©å\u0000\u001bs\u0091\u0007\u0007'¼É2ö¨\u009eÞ½TLÊi,®Z\u008eÀùNéô(b\u0011è`\u0016E\u009d¸\u000b\u0088±ø?þ¥!Ó\u0003YyÇ^N\u008aôêbÙè&\u0016\u000e\u009cm\n\u0005±ê?Ù¥ÉÓØY*Ç\u0002Mvû\u0015b\u0084èì\u0016È\u009c\"\n\u0001°e>B¥·Ó\u009eY¹ÇýM4û\ratïQ\u0016´\u009c\u0084\nÌ°\n>\u001d¤\u007fÒ\u0001YùÇÅ,®Z\u008eÀùNéô(b\u0011è`\u0016E\u009d¸\u000b\u0088±ø?þ¥!Ó\u0003YyÇ^N\u008aôêbÙè&\u0016\u000e\u009cm\n\u0005±ê?Ù¥ÒÓÓY6Ç\u0005MzûTb»èà\u0016Ñ\u009c(\nO°o>F¥²ÓÇYÊÇÞM>û\u0010a\u007fïW\u0016´\u009c\u0085\nÐ°k>+¤fÒSY¦Ç\u0087MæûÊa>ï<\u0015o\u0083A\nó°Ï>·".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, PointerIconCompat.TYPE_ZOOM_IN);
        d = cArr;
        b = -6604245468063835413L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0037). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r0 = o.bo.e.$$a
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r8 = 105 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            r7 = r6
            goto L37
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1f:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L37:
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.e.g(byte, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{79, 74, -126, -127};
        $$b = Opcodes.IFGT;
    }

    public e(Context context, o.bs.b bVar, o.bi.a aVar) {
        super(bVar);
        this.e = aVar;
        this.a = b(context);
    }

    public static i a(RemoteMessage remoteMessage) {
        int i = h + 31;
        c = i % 128;
        int i2 = i % 2;
        switch (remoteMessage.getData().isEmpty() ? '%' : (char) 6) {
            case '%':
                int i3 = c + 57;
                h = i3 % 128;
                int i4 = i3 % 2;
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f((char) (KeyEvent.getMaxKeyCode() >> 16), 1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 39, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((char) (65268 - (Process.myTid() >> 22)), 39 - (ViewConfiguration.getKeyRepeatDelay() >> 16), Color.rgb(0, 0, 0) + 16777268, objArr2);
                o.ee.g.e(intern, ((String) objArr2[0]).intern());
                return null;
            default:
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f((char) ('0' - AndroidCharacter.getMirror('0')), (-1) - TextUtils.indexOf((CharSequence) "", '0'), 39 - TextUtils.indexOf("", ""), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((char) (32830 - TextUtils.lastIndexOf("", '0', 0, 0)), View.MeasureSpec.getMode(0) + 91, (ViewConfiguration.getFadingEdgeLength() >> 16) + 48, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                return new i(remoteMessage.getData(), remoteMessage);
        }
    }

    @Override // o.bo.b
    public final String a() {
        Object obj;
        int i = c + 95;
        h = i % 128;
        switch (i % 2 == 0 ? (char) 21 : Typography.less) {
            case 21:
                Object[] objArr = new Object[1];
                f((char) (16178 << (Process.myTid() * 47)), 17753 >> (TypedValue.complexToFloat(0) > 2.0f ? 1 : (TypedValue.complexToFloat(0) == 2.0f ? 0 : -1)), TextUtils.lastIndexOf("", '@', 0) + 127, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f((char) (26675 - (Process.myTid() >> 22)), 139 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 7 - TextUtils.lastIndexOf("", '0', 0), objArr2);
                obj = objArr2[0];
                break;
        }
        return ((String) obj).intern();
    }

    @Override // o.bo.b
    public final boolean d(i iVar) {
        int i = h + Opcodes.DSUB;
        c = i % 128;
        if (i % 2 != 0) {
            Objects.equals(this.e.e(), o.e((CharSequence) ((RemoteMessage) iVar.e()).getFrom()));
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (Objects.equals(this.e.e(), o.e((CharSequence) ((RemoteMessage) iVar.e()).getFrom())) ? Typography.less : '+') {
            case '+':
                o.ee.g.c();
                Object[] objArr = new Object[1];
                f((char) View.MeasureSpec.makeMeasureSpec(0, 0), View.resolveSize(0, 0), TextUtils.indexOf("", "", 0, 0) + 39, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f((char) (ViewConfiguration.getKeyRepeatDelay() >> 16), 231 - KeyEvent.normalizeMetaState(0), 88 - View.MeasureSpec.getMode(0), objArr2);
                o.ee.g.d(intern, ((String) objArr2[0]).intern());
                return false;
            default:
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                f((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (-1) - MotionEvent.axisFromString(""), (ViewConfiguration.getPressedStateDuration() >> 16) + 39, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((char) (AndroidCharacter.getMirror('0') + 23925), 147 - View.resolveSize(0, 0), 84 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                int i2 = c + 35;
                h = i2 % 128;
                if (i2 % 2 != 0) {
                    return true;
                }
                int i3 = 2 / 0;
                return true;
        }
    }

    @Override // o.bo.b
    public final String d(Context context) throws g {
        if (!e(context)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f((char) (1 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), Color.alpha(0), 39 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 25692), 319 - TextUtils.getCapsMode("", 0, 0), View.MeasureSpec.getSize(0) + 56, objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f((char) (12417 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), Color.rgb(0, 0, 0) + 16777591, ExpandableListView.getPackedPositionChild(0L) + 35, objArr3);
            throw new g(((String) objArr3[0]).intern());
        }
        try {
            String str = (String) n.c(FirebaseInstallations.getInstance(this.a).getId());
            int i = c;
            int i2 = i + Opcodes.DNEG;
            h = i2 % 128;
            int i3 = i2 % 2;
            switch (str != null) {
                case false:
                    Object[] objArr4 = new Object[1];
                    f((char) (ViewConfiguration.getWindowTouchSlop() >> 8), TextUtils.indexOf("", "") + 491, Color.argb(0, 0, 0, 0) + 26, objArr4);
                    throw new g(((String) objArr4[0]).intern());
                default:
                    int i4 = i + 63;
                    h = i4 % 128;
                    if (i4 % 2 == 0) {
                    }
                    String e = o.e((CharSequence) str);
                    int i5 = c + 77;
                    h = i5 % 128;
                    int i6 = i5 % 2;
                    return e;
            }
        } catch (IllegalArgumentException e2) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            f((char) (ViewConfiguration.getKeyRepeatDelay() >> 16), ViewConfiguration.getMinimumFlingVelocity() >> 16, TextUtils.getTrimmedLength("") + 39, objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            f((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 2632), 410 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), 53 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr6);
            o.ee.g.a(intern2, ((String) objArr6[0]).intern(), e2);
            Object[] objArr7 = new Object[1];
            f((char) View.MeasureSpec.getSize(0), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 461, 30 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr7);
            throw new g(((String) objArr7[0]).intern());
        }
    }

    @Override // o.bo.b
    public final String a(Context context) throws g {
        int i = h + 51;
        c = i % 128;
        int i2 = i % 2;
        if (!e(context)) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f((char) TextUtils.getTrimmedLength(""), Gravity.getAbsoluteGravity(0, 0), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 39, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 43062), 517 - (Process.myPid() >> 22), (-16777169) - Color.rgb(0, 0, 0), objArr2);
            o.ee.g.e(intern, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f((char) (TextUtils.lastIndexOf("", '0') + 12417), Color.alpha(0) + 375, View.resolveSizeAndState(0, 0, 0) + 34, objArr3);
            throw new g(((String) objArr3[0]).intern());
        }
        try {
            String str = (String) n.c(((FirebaseMessaging) this.a.get(FirebaseMessaging.class)).getToken());
            switch (str != null ? Typography.less : ']') {
                case '<':
                    int i3 = c + 37;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    f((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), TextUtils.getOffsetAfter("", 0), 39 - View.resolveSize(0, 0), objArr4);
                    String intern2 = ((String) objArr4[0]).intern();
                    Locale a = o.ee.j.a();
                    Object[] objArr5 = new Object[1];
                    f((char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getTouchSlop() >> 8) + 607, 39 - TextUtils.lastIndexOf("", '0'), objArr5);
                    o.ee.g.d(intern2, String.format(a, ((String) objArr5[0]).intern(), str));
                    return o.e((CharSequence) str);
                default:
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    f((char) View.combineMeasuredStates(0, 0), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 39 - (Process.myTid() >> 22), objArr6);
                    String intern3 = ((String) objArr6[0]).intern();
                    Object[] objArr7 = new Object[1];
                    f((char) (11300 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), 647 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), ((byte) KeyEvent.getModifierMetaStateMask()) + 54, objArr7);
                    o.ee.g.e(intern3, ((String) objArr7[0]).intern());
                    Object[] objArr8 = new Object[1];
                    f((char) (ViewConfiguration.getDoubleTapTimeout() >> 16), Color.red(0) + 700, Color.red(0) + 29, objArr8);
                    throw new g(((String) objArr8[0]).intern());
            }
        } catch (IllegalArgumentException e) {
            o.ee.g.c();
            Object[] objArr9 = new Object[1];
            f((char) (ViewConfiguration.getKeyRepeatDelay() >> 16), TextUtils.indexOf((CharSequence) "", '0') + 1, 39 - (ViewConfiguration.getScrollBarSize() >> 8), objArr9);
            String intern4 = ((String) objArr9[0]).intern();
            Object[] objArr10 = new Object[1];
            f((char) (ViewConfiguration.getPressedStateDuration() >> 16), TextUtils.getCapsMode("", 0, 0) + 564, (ViewConfiguration.getFadingEdgeLength() >> 16) + 43, objArr10);
            o.ee.g.a(intern4, ((String) objArr10[0]).intern(), e);
            Object[] objArr11 = new Object[1];
            f((char) (ViewConfiguration.getEdgeSlop() >> 16), 461 - (ViewConfiguration.getScrollBarSize() >> 8), 29 - Process.getGidForName(""), objArr11);
            throw new g(((String) objArr11[0]).intern());
        }
    }

    @Override // o.bo.b
    public final String e() {
        Object obj;
        int i = h + Opcodes.DSUB;
        c = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                f((char) ExpandableListView.getPackedPositionGroup(0L), 21118 - (ViewConfiguration.getFadingEdgeLength() % 56), TextUtils.getOffsetAfter("", 0) * 71, objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f((char) ExpandableListView.getPackedPositionGroup(0L), (ViewConfiguration.getFadingEdgeLength() >> 16) + 729, 8 - TextUtils.getOffsetAfter("", 0), objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = c + 87;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return intern;
            default:
                int i3 = 50 / 0;
                return intern;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:20:0x0074, code lost:
    
        if (r5.isEmpty() == false) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0083, code lost:
    
        r2 = new com.google.firebase.FirebaseOptions.Builder().setGcmSenderId(r2).setApplicationId(r3).setProjectId(r4).setApiKey(r5).build();
        r3 = com.google.firebase.FirebaseApp.getApps(r18);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00a5, code lost:
    
        if (r3.isEmpty() == false) goto L41;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00a7, code lost:
    
        o.ee.g.c();
        r6 = new java.lang.Object[1];
        f((char) (android.view.MotionEvent.axisFromString("") + 1), android.widget.ExpandableListView.getPackedPositionGroup(0), 39 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), r6);
        r3 = ((java.lang.String) r6[0]).intern();
        r7 = new java.lang.Object[1];
        f((char) ((android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (android.telephony.cdma.CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 46831), (android.view.ViewConfiguration.getGlobalActionKeyTimeout() > 0 ? 1 : (android.view.ViewConfiguration.getGlobalActionKeyTimeout() == 0 ? 0 : -1)) + 783, (android.view.ViewConfiguration.getTouchSlop() >> 8) + 86, r7);
        o.ee.g.d(r3, ((java.lang.String) r7[0]).intern());
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:?, code lost:
    
        return com.google.firebase.FirebaseApp.initializeApp(r18, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:26:0x00fc, code lost:
    
        r3 = r3.iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x0106, code lost:
    
        if (r3.hasNext() == false) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0108, code lost:
    
        r4 = r3.next();
        r6 = r4.getName();
        r13 = (char) (3425 - (android.graphics.PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (android.graphics.PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
        r14 = 870 - android.text.TextUtils.indexOf("", "", 0, 0);
        r15 = (android.os.Process.getElapsedCpuTime() > r8 ? 1 : (android.os.Process.getElapsedCpuTime() == r8 ? 0 : -1)) + 28;
        r8 = new java.lang.Object[1];
        f(r13, r14, r15, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x013a, code lost:
    
        if (r6.equals(((java.lang.String) r8[0]).intern()) == false) goto L47;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x01a6, code lost:
    
        r8 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:33:0x013d, code lost:
    
        o.ee.g.c();
        r9 = new java.lang.Object[1];
        f((char) ((-1) - android.text.TextUtils.lastIndexOf("", '0', 0, 0)), (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), android.text.TextUtils.indexOf("", "", 0) + 39, r9);
        r3 = ((java.lang.String) r9[0]).intern();
        r6 = new java.lang.StringBuilder();
        r14 = new java.lang.Object[1];
        f((char) android.widget.ExpandableListView.getPackedPositionGroup(0), android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0') + 900, 55 - (android.view.ViewConfiguration.getJumpTapTimeout() >> 16), r14);
        o.ee.g.d(r3, r6.append(((java.lang.String) r14[0]).intern()).append(r4.getName()).toString());
        r3 = o.bo.e.c + 99;
        o.bo.e.h = r3 % 128;
        r3 = r3 % 2;
        r3 = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:34:0x01ac, code lost:
    
        if (r3 != null) goto L51;
     */
    /* JADX WARN: Code restructure failed: missing block: B:35:0x01ae, code lost:
    
        r8 = new java.lang.Object[1];
        f((char) (android.graphics.Color.argb(0, 0, 0, 0) + 3425), (android.view.ViewConfiguration.getJumpTapTimeout() >> 16) + 870, 29 - android.view.View.resolveSize(0, 0), r8);
        r0 = com.google.firebase.FirebaseApp.initializeApp(r18, r2, ((java.lang.String) r8[0]).intern());
        o.ee.g.c();
        r5 = new java.lang.Object[1];
        f((char) ((android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), android.text.TextUtils.lastIndexOf("", '0') + 1, 39 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), r5);
        r2 = ((java.lang.String) r5[0]).intern();
        r3 = new java.lang.StringBuilder();
        r7 = new java.lang.Object[1];
        f((char) (android.view.ViewConfiguration.getScrollBarSize() >> 8), android.view.View.MeasureSpec.getMode(0) + 954, android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0', 0) + 65, r7);
        o.ee.g.d(r2, r3.append(((java.lang.String) r7[0]).intern()).append(r0.getName()).toString());
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:?, code lost:
    
        return r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x0234, code lost:
    
        return r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:40:0x01ab, code lost:
    
        r3 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x007d, code lost:
    
        if (r5.isEmpty() == false) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x004e, code lost:
    
        if (r3.isEmpty() == false) goto L18;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private com.google.firebase.FirebaseApp b(android.content.Context r18) {
        /*
            Method dump skipped, instructions count: 698
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.e.b(android.content.Context):com.google.firebase.FirebaseApp");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 620
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.e.f(char, int, int, java.lang.Object[]):void");
    }
}
