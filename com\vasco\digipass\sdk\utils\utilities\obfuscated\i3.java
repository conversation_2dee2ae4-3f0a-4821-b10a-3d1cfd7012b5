package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\i3.smali */
class i3 extends e1 {
    private final boolean L;

    i3(int i, int i2, boolean z, g0 g0Var) {
        super(i, i2, g0Var);
        this.L = z;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e1, com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public b0 a() throws IOException {
        return this.C.a(this.b, this.x, this.L);
    }
}
