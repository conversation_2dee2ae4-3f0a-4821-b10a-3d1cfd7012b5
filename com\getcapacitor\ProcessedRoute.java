package com.getcapacitor;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes11\com\getcapacitor\ProcessedRoute.smali */
public class ProcessedRoute {
    private boolean ignoreAssetPath;
    private boolean isAsset;
    private String path;

    public String getPath() {
        return this.path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isAsset() {
        return this.isAsset;
    }

    public void setAsset(boolean asset) {
        this.isAsset = asset;
    }

    public boolean isIgnoreAssetPath() {
        return this.ignoreAssetPath;
    }

    public void setIgnoreAssetPath(boolean ignoreAssetPath) {
        this.ignoreAssetPath = ignoreAssetPath;
    }
}
