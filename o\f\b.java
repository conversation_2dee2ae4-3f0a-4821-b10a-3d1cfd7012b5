package o.f;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import kotlin.text.Typography;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\b.smali */
public final class b extends e {
    private static int e = 0;
    private static int c = 1;

    public b(e.d dVar, Date date) {
        super(dVar, date);
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = c;
        int i2 = (i & 55) + (i | 55);
        e = i2 % 128;
        int i3 = i2 % 2;
        o.i.f fVar = o.i.f.c;
        int i4 = e + 33;
        c = i4 % 128;
        int i5 = i4 % 2;
        return fVar;
    }

    @Override // o.f.e
    public final String c() {
        int i = c;
        int i2 = ((i | Opcodes.LSHR) << 1) - (i ^ Opcodes.LSHR);
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = (i + 56) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                throw null;
        }
    }

    @Override // o.f.e
    public final byte[] d() {
        int i = (e + Opcodes.IREM) - 1;
        int i2 = i % 128;
        c = i2;
        switch (i % 2 == 0 ? ';' : '+') {
            case ';':
                int i3 = 79 / 0;
                break;
        }
        int i4 = (i2 & 37) + (i2 | 37);
        e = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = e;
        int i2 = (i + 98) - 1;
        c = i2 % 128;
        switch (i2 % 2 == 0 ? '-' : 'S') {
            case '-':
                throw null;
            default:
                int i3 = (i + 28) - 1;
                c = i3 % 128;
                int i4 = i3 % 2;
                return null;
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = c;
        int i2 = (i + 72) - 1;
        e = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? '@' : Typography.less) {
            case '<':
                int i3 = ((i | 33) << 1) - (i ^ 33);
                e = i3 % 128;
                switch (i3 % 2 != 0 ? '\\' : 'Z') {
                    case 'Z':
                        return null;
                    default:
                        int i4 = 24 / 0;
                        return null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final String j() {
        int i = e;
        int i2 = (i & Opcodes.DSUB) + (i | Opcodes.DSUB);
        c = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0 ? '\n' : (char) 7) {
            case 7:
                int i3 = (i + Opcodes.ISHR) - 1;
                c = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                obj.hashCode();
                throw null;
        }
    }
}
