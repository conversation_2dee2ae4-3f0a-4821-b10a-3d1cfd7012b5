package o.ah;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.DigipassSDKConstants;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.ei.c;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ah\e.smali */
public final class e extends b<d> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int g;
    private static int i;
    private static long j;
    a a;
    o.h.d b;
    String c;
    String d;
    String e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ah\e$d.smali */
    public interface d {
        void b(o.bb.d dVar);

        void e(String str);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        g = 0;
        i = 1;
        o();
        SystemClock.elapsedRealtime();
        ExpandableListView.getPackedPositionType(0L);
        int i2 = g + 53;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{118, -84, -110, 65};
        $$e = Opcodes.F2L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 2
            int r8 = r8 + 4
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r6 = r6 * 2
            int r6 = r6 + 112
            byte[] r0 = o.ah.e.$$d
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L35
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r7 = -r7
            int r6 = r6 + r7
            int r7 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ah.e.l(byte, byte, short, java.lang.Object[]):void");
    }

    static void o() {
        j = -7157148555246599672L;
    }

    @Override // o.y.b
    public final /* synthetic */ o.y.a b() {
        int i2 = i + 79;
        g = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return m();
            default:
                m();
                throw null;
        }
    }

    public e(Context context, d dVar, c cVar) {
        super(context, dVar, cVar, o.bb.e.y);
        this.d = null;
    }

    public final void a(o.h.d dVar, String str, String str2, a aVar) {
        g.c();
        Object[] objArr = new Object[1];
        k("䓛撴Ѱ␛엿\ue57c蔵꛶䚂晈ߞ➾읷\ue701胂ꁺ䀱懆Ɲ⅂싱\ue2a8艨ꈗ", 8269 - TextUtils.getOffsetAfter("", 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k("䓼殼ᩍ줘阮ꢋ弉\u0ff0㺣\ued4e鰄䳄獘∾틱膍끄朂ឧ옩\uf569ꖟ咉筄⯢\udaaf褦렅棃ῇ츶ﻤ궋屃̂㎸\ue26d酯䆀烕", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 12106, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str2).toString());
        this.c = str;
        this.b = dVar;
        this.e = str2;
        this.a = aVar;
        c();
        int i2 = g + 5;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    private AsyncTaskC0018e m() {
        AsyncTaskC0018e asyncTaskC0018e = new AsyncTaskC0018e(this);
        int i2 = g + Opcodes.LNEG;
        i = i2 % 128;
        int i3 = i2 % 2;
        return asyncTaskC0018e;
    }

    @Override // o.y.b
    public final String a() {
        int i2 = i + 65;
        g = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        k("䓛撴Ѱ␛엿\ue57c蔵꛶䚂晈ߞ➾읷\ue701胂ꁺ䀱懆Ɲ⅂싱\ue2a8艨ꈗ", TextUtils.indexOf((CharSequence) "", '0', 0) + 8270, objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = g + 1;
        i = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    /* renamed from: o.ah.e$e, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ah\e$e.smali */
    static final class AsyncTaskC0018e extends o.y.c<e> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int c;
        private static int d;
        private static long e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            c = 0;
            d = 1;
            e = 390108418141470749L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(short r7, byte r8, int r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.ah.e.AsyncTaskC0018e.$$d
                int r9 = r9 * 2
                int r9 = 114 - r9
                int r7 = r7 * 4
                int r7 = 3 - r7
                int r8 = r8 * 3
                int r8 = 1 - r8
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L17
                r3 = r9
                r5 = r2
                r9 = r8
                goto L2e
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r9
                int r5 = r3 + 1
                r1[r3] = r4
                int r7 = r7 + 1
                if (r5 != r8) goto L29
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L29:
                r3 = r0[r7]
                r6 = r9
                r9 = r8
                r8 = r6
            L2e:
                int r8 = r8 + r3
                r3 = r5
                r6 = r9
                r9 = r8
                r8 = r6
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ah.e.AsyncTaskC0018e.B(short, byte, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{43, -103, 93, -106};
            $$e = Opcodes.INVOKEINTERFACE;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 65;
            c = i % 128;
            switch (i % 2 != 0 ? (char) 31 : '1') {
                case 31:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }

        AsyncTaskC0018e(e eVar) {
            super(eVar, true);
        }

        @Override // o.y.c
        public final String l() {
            int i = d + 79;
            c = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w("蛮섧३冈駲\ue01f⠬灵뢧Ë䬧鍝\udb7a⎲毻눉梅", View.MeasureSpec.getSize(0) + 18379, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = d + 91;
            c = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w("蚹旱䀢⽙உ\uf63d핱놧鳑笍枻䋳℣ౕ\ue88c휾뉭麠緗", View.MeasureSpec.getSize(0) + 58189, objArr);
            o.cf.d dVar = new o.cf.d(context, 31, ((String) objArr[0]).intern());
            int i = c + 21;
            d = i % 128;
            int i2 = i % 2;
            return dVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w("蛮ǻ裑Ꭼ骘▚", (ViewConfiguration.getJumpTapTimeout() >> 16) + 34583, objArr);
            bVar.d(((String) objArr[0]).intern(), ((e) e()).e);
            Object[] objArr2 = new Object[1];
            w("蛹\ue927奛즑", (ViewConfiguration.getTouchSlop() >> 8) + 28627, objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((e) e()).a.e());
            int i = c + 27;
            d = i % 128;
            switch (i % 2 == 0 ? 'V' : 'N') {
                case 'N':
                    return bVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final j n() {
            j jVar = new j(((e) e()).c, true, ((e) e()).b);
            int i = c + 93;
            d = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = c;
            int i2 = i + Opcodes.LREM;
            d = i2 % 128;
            int i3 = i2 % 2;
            int i4 = i + 51;
            d = i4 % 128;
            switch (i4 % 2 == 0 ? '2' : 'Y') {
                case Opcodes.DUP /* 89 */:
                    return null;
                default:
                    throw null;
            }
        }

        /* JADX WARN: Removed duplicated region for block: B:11:0x0025  */
        /* JADX WARN: Removed duplicated region for block: B:6:0x001f  */
        /* JADX WARN: Removed duplicated region for block: B:9:0x0022  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final o.bb.a c(int r3) {
            /*
                r2 = this;
                int r0 = o.ah.e.AsyncTaskC0018e.d
                int r0 = r0 + 107
                int r1 = r0 % 128
                o.ah.e.AsyncTaskC0018e.c = r1
                int r0 = r0 % 2
                if (r0 == 0) goto Lf
                r0 = 13
                goto L10
            Lf:
                r0 = 5
            L10:
                switch(r0) {
                    case 13: goto L17;
                    default: goto L13;
                }
            L13:
                switch(r3) {
                    case 5001: goto L22;
                    case 5002: goto L1f;
                    default: goto L16;
                }
            L16:
                goto L25
            L17:
                r0 = 0
                int r0 = r0 / r0
                switch(r3) {
                    case 5001: goto L22;
                    case 5002: goto L1f;
                    default: goto L1c;
                }
            L1c:
                goto L16
            L1d:
                r3 = move-exception
                throw r3
            L1f:
                o.bb.a r3 = o.bb.a.az
                return r3
            L22:
                o.bb.a r3 = o.bb.a.ay
                return r3
            L25:
                o.bb.a r3 = super.c(r3)
                int r0 = o.ah.e.AsyncTaskC0018e.d
                int r0 = r0 + 59
                int r1 = r0 % 128
                o.ah.e.AsyncTaskC0018e.c = r1
                int r0 = r0 % 2
                return r3
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ah.e.AsyncTaskC0018e.c(int):o.bb.a");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            e eVar;
            Object obj;
            int i = d + 37;
            c = i % 128;
            switch (i % 2 == 0) {
                case true:
                    eVar = (e) e();
                    Object[] objArr = new Object[1];
                    w("蛩쳝ኌ塖긅\uf5f1㮢腆휤\u1af8惖", 19001 - Color.green(0), objArr);
                    obj = objArr[0];
                    break;
                default:
                    eVar = (e) e();
                    Object[] objArr2 = new Object[1];
                    w("蛩쳝ኌ塖긅\uf5f1㮢腆휤\u1af8惖", 31638 >> Color.green(1), objArr2);
                    obj = objArr2[0];
                    break;
            }
            eVar.d = bVar.r(((String) obj).intern());
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = c + 83;
            d = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass4.d[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((e) e()).e);
                    int i3 = d + 43;
                    c = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                case 2:
                    f().e(g(), ((e) e()).e);
                    break;
                default:
                    super.t();
                    break;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = d + 87;
            c = i % 128;
            int i2 = i % 2;
            ((e) e()).j().e(((e) e()).d);
            int i3 = d + 99;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = d + 73;
            c = i % 128;
            switch (i % 2 == 0) {
                case false:
                    ((e) e()).j().b(dVar);
                    int i2 = 8 / 0;
                    break;
                default:
                    ((e) e()).j().b(dVar);
                    break;
            }
            int i3 = d + 7;
            c = i3 % 128;
            int i4 = i3 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(java.lang.String r17, int r18, java.lang.Object[] r19) {
            /*
                Method dump skipped, instructions count: 480
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ah.e.AsyncTaskC0018e.w(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.ah.e$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ah\e$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int b;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            b = 0;
            e = 1;
            int[] iArr = new int[o.bb.a.values().length];
            d = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = b + 43;
                e = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.bb.a.az.ordinal()] = 2;
                int i3 = e + 33;
                b = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        int i4 = 48 / 0;
                        return;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ah\e$a.smali */
    public static final class a implements o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static final /* synthetic */ a[] a;
        public static final a b;
        public static final a c;
        private static int e;
        private static short[] f;
        private static byte[] g;
        private static int h;
        private static int i;
        private static int j;
        private static int l;
        private final String d;

        static void d() {
            g = new byte[]{71, -15, -51, -14, -41, -7, -21, -86, -36, -5, -24, 72, -8, -12, -7, -34, -32, -14, -42, -20, -61, -30, -49, 67, 54, 10, 65, -22, 58, 46, 68, DigipassSDKConstants.SECURE_CHANNEL_MESSAGE_TYPE_INFORMATION_MESSAGE, 56, 47, 7, 84, 40, 60};
            j = 909053604;
            i = -1239342293;
            e = 1686157595;
        }

        static void init$0() {
            $$a = new byte[]{24, -81, 39, 82};
            $$b = 235;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0037). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void m(int r6, byte r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 + 4
                byte[] r0 = o.ah.e.a.$$a
                int r8 = r8 * 2
                int r8 = 110 - r8
                int r6 = r6 * 3
                int r6 = r6 + 1
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L19
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r7
                goto L37
            L19:
                r3 = r2
                r5 = r8
                r8 = r7
                r7 = r5
            L1d:
                byte r4 = (byte) r7
                r1[r3] = r4
                if (r3 != r6) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                int r8 = r8 + 1
                r4 = r0[r8]
                int r3 = r3 + 1
                r5 = r9
                r9 = r8
                r8 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r5
            L37:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ah.e.a.m(int, byte, short, java.lang.Object[]):void");
        }

        private static /* synthetic */ a[] c() {
            int i2 = l + 45;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? ' ' : (char) 25) {
                case 25:
                    return new a[]{b, c};
                default:
                    a[] aVarArr = new a[4];
                    aVarArr[0] = b;
                    aVarArr[1] = c;
                    return aVarArr;
            }
        }

        public static a valueOf(String str) {
            int i2 = h + 9;
            l = i2 % 128;
            boolean z = i2 % 2 == 0;
            a aVar = (a) Enum.valueOf(a.class, str);
            switch (z) {
                case false:
                    int i3 = h + 9;
                    l = i3 % 128;
                    int i4 = i3 % 2;
                    return aVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public static a[] values() {
            int i2 = h + Opcodes.LMUL;
            l = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return (a[]) a.clone();
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            h = 0;
            l = 1;
            d();
            Object[] objArr = new Object[1];
            k((byte) (Process.getGidForName("") + 1), (-1387247499) - TextUtils.getTrimmedLength(""), (short) ((-91) - TextUtils.lastIndexOf("", '0')), (-53) - Color.red(0), 2146554505 + TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            k((byte) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1387247489, (short) ((-97) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 53, 2146554504 + (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr2);
            b = new a(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            k((byte) (Process.myTid() >> 22), Color.alpha(0) - 1387247476, (short) (MotionEvent.axisFromString("") + 92), (-52) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), 2146554518 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            k((byte) KeyEvent.keyCodeFromString(""), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) - 1387247469, (short) (77 - (ViewConfiguration.getJumpTapTimeout() >> 16)), TextUtils.getOffsetAfter("", 0) - 53, 2146554516 - ImageFormat.getBitsPerPixel(0), objArr4);
            c = new a(intern2, 1, ((String) objArr4[0]).intern());
            a = c();
            int i2 = l + 69;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return;
                default:
                    int i3 = 47 / 0;
                    return;
            }
        }

        private a(String str, int i2, String str2) {
            this.d = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = l;
            int i3 = i2 + 13;
            h = i3 % 128;
            int i4 = i3 % 2;
            String str = this.d;
            int i5 = i2 + 61;
            h = i5 % 128;
            switch (i5 % 2 == 0) {
                case true:
                    return str;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:86:0x02b7, code lost:
        
            r3 = r8;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void k(byte r16, int r17, short r18, int r19, int r20, java.lang.Object[] r21) {
            /*
                Method dump skipped, instructions count: 870
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ah.e.a.k(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 878
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ah.e.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
