package com.google.android.gms.tapandpay.quickaccesswallet;

import android.accounts.Account;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\SetQuickAccessWalletCardsRequest.smali */
public final class SetQuickAccessWalletCardsRequest extends AbstractSafeParcelable {
    public static final Parcelable.Creator<SetQuickAccessWalletCardsRequest> CREATOR = new zzj();
    private int zza;
    private Account zzb;
    private QuickAccessWalletCard[] zzc;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\SetQuickAccessWalletCardsRequest$Builder.smali */
    public static final class Builder {
        private final SetQuickAccessWalletCardsRequest zza;

        public Builder() {
            this.zza = new SetQuickAccessWalletCardsRequest(null);
        }

        public SetQuickAccessWalletCardsRequest build() {
            return this.zza;
        }

        public Builder setAccount(Account account) {
            this.zza.zzb = account;
            return this;
        }

        public Builder setCards(QuickAccessWalletCard[] cards) {
            this.zza.zzc = cards;
            return this;
        }

        public Builder setSource(int source) {
            this.zza.zza = source;
            return this;
        }

        public Builder(SetQuickAccessWalletCardsRequest origin) {
            SetQuickAccessWalletCardsRequest setQuickAccessWalletCardsRequest = new SetQuickAccessWalletCardsRequest(null);
            this.zza = setQuickAccessWalletCardsRequest;
            setQuickAccessWalletCardsRequest.zza = origin.zza;
            setQuickAccessWalletCardsRequest.zzb = origin.zzb;
            setQuickAccessWalletCardsRequest.zzc = origin.zzc;
        }
    }

    private SetQuickAccessWalletCardsRequest() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof SetQuickAccessWalletCardsRequest) {
            SetQuickAccessWalletCardsRequest setQuickAccessWalletCardsRequest = (SetQuickAccessWalletCardsRequest) other;
            if (Objects.equal(Integer.valueOf(this.zza), Integer.valueOf(setQuickAccessWalletCardsRequest.zza)) && Objects.equal(this.zzb, setQuickAccessWalletCardsRequest.zzb) && Arrays.equals(this.zzc, setQuickAccessWalletCardsRequest.zzc)) {
                return true;
            }
        }
        return false;
    }

    public Account getAccount() {
        return this.zzb;
    }

    public QuickAccessWalletCard[] getCards() {
        return this.zzc;
    }

    public int getSource() {
        return this.zza;
    }

    public int hashCode() {
        return Objects.hashCode(Integer.valueOf(this.zza), this.zzb, Integer.valueOf(Arrays.hashCode(this.zzc)));
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeInt(dest, 1, getSource());
        SafeParcelWriter.writeParcelable(dest, 2, getAccount(), flags, false);
        SafeParcelWriter.writeTypedArray(dest, 3, getCards(), flags, false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    SetQuickAccessWalletCardsRequest(int i, Account account, QuickAccessWalletCard[] quickAccessWalletCardArr) {
        this.zza = i;
        this.zzb = account;
        this.zzc = quickAccessWalletCardArr;
    }

    /* synthetic */ SetQuickAccessWalletCardsRequest(zzi zziVar) {
    }
}
