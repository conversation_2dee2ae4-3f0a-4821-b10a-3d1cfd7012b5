package o.f;

import android.graphics.ImageFormat;
import android.os.Process;
import android.text.TextUtils;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import kotlin.io.encoding.Base64;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final c a;
    public static final c b;
    public static final c c;
    public static final c d;
    public static final c e;
    private static int f;
    private static long h;
    private static int i;
    private static final /* synthetic */ c[] j;

    static void d() {
        h = 9197971912712999700L;
    }

    static void init$0() {
        $$a = new byte[]{2, Base64.padSymbol, -41, 17};
        $$b = 72;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r7, int r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.f.c.$$a
            int r7 = r7 * 2
            int r7 = r7 + 112
            int r9 = r9 * 3
            int r9 = r9 + 1
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L18:
            r3 = r2
        L19:
            int r8 = r8 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r7 = r7 + r8
            r8 = r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.c.k(byte, int, int, java.lang.Object[]):void");
    }

    private c(String str, int i2) {
    }

    private static /* synthetic */ c[] c() {
        c[] cVarArr;
        int i2 = i;
        int i3 = i2 + 73;
        f = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                cVarArr = new c[4];
                cVarArr[0] = d;
                cVarArr[1] = a;
                cVarArr[5] = b;
                cVarArr[3] = e;
                cVarArr[2] = c;
                break;
            default:
                cVarArr = new c[]{d, a, b, e, c};
                break;
        }
        int i4 = i2 + 1;
        f = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 14 : ';') {
            case ';':
                return cVarArr;
            default:
                int i5 = 82 / 0;
                return cVarArr;
        }
    }

    public static c valueOf(String str) {
        int i2 = i + 51;
        f = i2 % 128;
        int i3 = i2 % 2;
        c cVar = (c) Enum.valueOf(c.class, str);
        int i4 = f + 43;
        i = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public static c[] values() {
        int i2 = i + Opcodes.DMUL;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 20 : 'W') {
            case Opcodes.POP /* 87 */:
                return (c[]) j.clone();
            default:
                throw null;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        i = 1;
        d();
        Object[] objArr = new Object[1];
        g("闑ෞꖷ嶘\uf545洈Ԟ볷咣첂摻ᰣ", ImageFormat.getBitsPerPixel(0) + 38954, objArr);
        d = new c(((String) objArr[0]).intern(), 0);
        Object[] objArr2 = new Object[1];
        g("闑Ｃ䁽햱㻏耞ᕜ纶쎩員븶͈钌", 27337 - View.getDefaultSize(0, 0), objArr2);
        a = new c(((String) objArr2[0]).intern(), 1);
        Object[] objArr3 = new Object[1];
        g("闑釸鷻駎藝膁趱覙떯녶뵢륑ꕟꄵ괢꤀픐턹\udcf3\ud8d4쓍샋첦좲\uf488", 1040 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr3);
        b = new c(((String) objArr3[0]).intern(), 2);
        Object[] objArr4 = new Object[1];
        g("闑ⷸ\ue5fb뷎痝ඉ얱鶟喞\ued6aꕶ絅㕕촩蔔崜ᔹ괞擾㳱\uf4c7賛䒝Ჩ풍沇⑷ﱢ", View.resolveSize(0, 0) + 47119, objArr4);
        e = new c(((String) objArr4[0]).intern(), 3);
        Object[] objArr5 = new Object[1];
        g("闑㔮푗睨ᚅ뇿僽\uf019錮㉌\udd9a粳῍뻯帘壘顙㮈\udab2旷ԟꐭ䝡\ue694膩⃛쏺挂Ɋ굢", TextUtils.getCapsMode("", 0, 0) + 41177, objArr5);
        c = new c(((String) objArr5[0]).intern(), 4);
        j = c();
        int i2 = f + 95;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r17, int r18, java.lang.Object[] r19) {
        /*
            Method dump skipped, instructions count: 520
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.c.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
