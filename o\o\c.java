package o.o;

import android.content.Context;
import android.os.CancellationSignal;
import androidx.fragment.app.FragmentActivity;
import com.esotericsoftware.asm.Opcodes;
import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\c.smali */
public abstract class c {
    private static int e = 0;
    private static int g = 1;
    private c a;
    private final String b;
    private final int c;
    private final long d = Objects.hash(this);

    public abstract boolean c();

    public abstract void d(FragmentActivity fragmentActivity, int i, CancellationSignal cancellationSignal, b bVar);

    public c(String str, int i) {
        this.b = str;
        this.c = i;
    }

    public final void e(Context context, b bVar, CancellationSignal cancellationSignal) {
        int i = (g + 34) - 1;
        e = i % 128;
        int i2 = i % 2;
        d.d(context, this, bVar, cancellationSignal);
        int i3 = g + 59;
        e = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                int i4 = 86 / 0;
                return;
            default:
                return;
        }
    }

    public final long e() {
        int i = e;
        int i2 = (i ^ Opcodes.LSUB) + ((i & Opcodes.LSUB) << 1);
        g = i2 % 128;
        switch (i2 % 2 == 0 ? 'a' : (char) 7) {
            case 7:
                return this.d;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String b() {
        int i = g;
        int i2 = (i & 95) + (i | 95);
        e = i2 % 128;
        switch (i2 % 2 != 0 ? 'G' : 'D') {
            case 'D':
                String str = this.b;
                int i3 = (i ^ 67) + ((i & 67) << 1);
                e = i3 % 128;
                int i4 = i3 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final int d() {
        int i = g;
        int i2 = ((i | Opcodes.DMUL) << 1) - (i ^ Opcodes.DMUL);
        int i3 = i2 % 128;
        e = i3;
        int i4 = i2 % 2;
        int i5 = this.c;
        int i6 = (i3 ^ 9) + ((i3 & 9) << 1);
        g = i6 % 128;
        int i7 = i6 % 2;
        return i5;
    }

    public final c a() {
        int i = (e + Opcodes.FMUL) - 1;
        int i2 = i % 128;
        g = i2;
        int i3 = i % 2;
        c cVar = this.a;
        int i4 = (i2 + 64) - 1;
        e = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 26 : 'D') {
            case 'D':
                return cVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:37:0x007f. Please report as an issue. */
    public boolean a(c cVar) {
        int i = e;
        int i2 = i + Opcodes.LSHL;
        g = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case true:
                obj.hashCode();
                throw null;
            default:
                switch (cVar != null) {
                    case true:
                        switch (cVar.getClass().equals(getClass())) {
                            case false:
                                this.a = cVar;
                                int i3 = g + 81;
                                e = i3 % 128;
                                switch (i3 % 2 == 0) {
                                    case true:
                                        return true;
                                    default:
                                        obj.hashCode();
                                        throw null;
                                }
                            default:
                                int i4 = g;
                                int i5 = i4 + 25;
                                e = i5 % 128;
                                switch (i5 % 2 != 0) {
                                    case true:
                                        r2 = true;
                                        break;
                                }
                                int i6 = i4 + Opcodes.LREM;
                                e = i6 % 128;
                                int i7 = i6 % 2;
                                return r2;
                        }
                    default:
                        int i8 = i + 47;
                        g = i8 % 128;
                        switch (i8 % 2 != 0) {
                        }
                        this.a = null;
                        return true;
                }
        }
    }

    public final int hashCode() {
        int i = g + 47;
        e = i % 128;
        switch (i % 2 != 0) {
            case false:
                int hashCode = super.hashCode();
                int i2 = g + 77;
                e = i2 % 128;
                int i3 = i2 % 2;
                return hashCode;
            default:
                super.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = e + 9;
        g = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = g;
        int i4 = (i3 & Opcodes.LSUB) + (i3 | Opcodes.LSUB);
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i = e + 89;
        g = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = e + 51;
        g = i3 % 128;
        switch (i3 % 2 == 0 ? '(' : (char) 23) {
            case '(':
                throw null;
            default:
                return obj;
        }
    }

    protected final void finalize() throws Throwable {
        int i = g;
        int i2 = (i ^ Opcodes.LNEG) + ((i & Opcodes.LNEG) << 1);
        e = i2 % 128;
        int i3 = i2 % 2;
        super.finalize();
        int i4 = g;
        int i5 = (i4 ^ Opcodes.LSHL) + ((i4 & Opcodes.LSHL) << 1);
        e = i5 % 128;
        switch (i5 % 2 != 0 ? (char) 17 : '?') {
            case '?':
                return;
            default:
                int i6 = 67 / 0;
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
