package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import com.esotericsoftware.asm.Opcodes;
import java.math.BigInteger;
import java.security.SecureRandom;
import org.bouncycastle.math.Primes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p6.smali */
public abstract class p6 {
    private static final BigInteger a = BigInteger.valueOf(1);
    private static final BigInteger b = BigInteger.valueOf(2);
    private static final BigInteger c = BigInteger.valueOf(3);

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\p6$a.smali */
    public static class a {
        private boolean a;
        private BigInteger b;

        private a(boolean z, BigInteger bigInteger) {
            this.a = z;
            this.b = bigInteger;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static a d() {
            return new a(false, null);
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static a e() {
            return new a(true, null);
        }

        public boolean c() {
            return this.a;
        }

        /* JADX INFO: Access modifiers changed from: private */
        public static a b(BigInteger bigInteger) {
            return new a(true, bigInteger);
        }
    }

    public static a a(BigInteger bigInteger, SecureRandom secureRandom, int i) {
        boolean z;
        BigInteger bigInteger2;
        a(bigInteger, "candidate");
        if (secureRandom == null) {
            throw new IllegalArgumentException("'random' cannot be null");
        }
        if (i < 1) {
            throw new IllegalArgumentException("'iterations' must be > 0");
        }
        if (bigInteger.bitLength() == 2) {
            return a.d();
        }
        if (!bigInteger.testBit(0)) {
            return a.b(b);
        }
        BigInteger subtract = bigInteger.subtract(a);
        BigInteger subtract2 = bigInteger.subtract(b);
        int lowestSetBit = subtract.getLowestSetBit();
        BigInteger shiftRight = subtract.shiftRight(lowestSetBit);
        for (int i2 = 0; i2 < i; i2++) {
            BigInteger a2 = f1.a(b, subtract2, secureRandom);
            BigInteger gcd = a2.gcd(bigInteger);
            BigInteger bigInteger3 = a;
            if (gcd.compareTo(bigInteger3) > 0) {
                return a.b(gcd);
            }
            BigInteger modPow = a2.modPow(shiftRight, bigInteger);
            if (!modPow.equals(bigInteger3) && !modPow.equals(subtract)) {
                int i3 = 1;
                while (true) {
                    if (i3 >= lowestSetBit) {
                        z = false;
                        bigInteger2 = modPow;
                        break;
                    }
                    bigInteger2 = modPow.modPow(b, bigInteger);
                    if (bigInteger2.equals(subtract)) {
                        z = true;
                        break;
                    }
                    if (bigInteger2.equals(a)) {
                        z = false;
                        break;
                    }
                    i3++;
                    modPow = bigInteger2;
                }
                if (!z) {
                    BigInteger bigInteger4 = a;
                    if (!bigInteger2.equals(bigInteger4)) {
                        modPow = bigInteger2.modPow(b, bigInteger);
                        if (modPow.equals(bigInteger4)) {
                            modPow = bigInteger2;
                        }
                    }
                    BigInteger gcd2 = modPow.subtract(bigInteger4).gcd(bigInteger);
                    return gcd2.compareTo(bigInteger4) > 0 ? a.b(gcd2) : a.e();
                }
            }
        }
        return a.d();
    }

    public static boolean b(BigInteger bigInteger, SecureRandom secureRandom, int i) {
        a(bigInteger, "candidate");
        if (secureRandom == null) {
            throw new IllegalArgumentException("'random' cannot be null");
        }
        if (i < 1) {
            throw new IllegalArgumentException("'iterations' must be > 0");
        }
        if (bigInteger.bitLength() == 2) {
            return true;
        }
        if (!bigInteger.testBit(0)) {
            return false;
        }
        BigInteger subtract = bigInteger.subtract(a);
        BigInteger subtract2 = bigInteger.subtract(b);
        int lowestSetBit = subtract.getLowestSetBit();
        BigInteger shiftRight = subtract.shiftRight(lowestSetBit);
        for (int i2 = 0; i2 < i; i2++) {
            if (!a(bigInteger, subtract, shiftRight, lowestSetBit, f1.a(b, subtract2, secureRandom))) {
                return false;
            }
        }
        return true;
    }

    private static boolean b(BigInteger bigInteger) {
        int intValue = bigInteger.mod(BigInteger.valueOf(223092870)).intValue();
        if (intValue % 2 != 0 && intValue % 3 != 0 && intValue % 5 != 0 && intValue % 7 != 0 && intValue % 11 != 0 && intValue % 13 != 0 && intValue % 17 != 0 && intValue % 19 != 0 && intValue % 23 != 0) {
            int intValue2 = bigInteger.mod(BigInteger.valueOf(58642669)).intValue();
            if (intValue2 % 29 != 0 && intValue2 % 31 != 0 && intValue2 % 37 != 0 && intValue2 % 41 != 0 && intValue2 % 43 != 0) {
                int intValue3 = bigInteger.mod(BigInteger.valueOf(600662303)).intValue();
                if (intValue3 % 47 != 0 && intValue3 % 53 != 0 && intValue3 % 59 != 0 && intValue3 % 61 != 0 && intValue3 % 67 != 0) {
                    int intValue4 = bigInteger.mod(BigInteger.valueOf(33984931)).intValue();
                    if (intValue4 % 71 != 0 && intValue4 % 73 != 0 && intValue4 % 79 != 0 && intValue4 % 83 != 0) {
                        int intValue5 = bigInteger.mod(BigInteger.valueOf(89809099)).intValue();
                        if (intValue5 % 89 != 0 && intValue5 % 97 != 0 && intValue5 % Opcodes.LSUB != 0 && intValue5 % Opcodes.DSUB != 0) {
                            int intValue6 = bigInteger.mod(BigInteger.valueOf(167375713)).intValue();
                            if (intValue6 % Opcodes.DMUL != 0 && intValue6 % 109 != 0 && intValue6 % Opcodes.LREM != 0 && intValue6 % 127 != 0) {
                                int intValue7 = bigInteger.mod(BigInteger.valueOf(371700317)).intValue();
                                if (intValue7 % Opcodes.LXOR != 0 && intValue7 % Opcodes.L2F != 0 && intValue7 % Opcodes.F2I != 0 && intValue7 % Opcodes.FCMPL != 0) {
                                    int intValue8 = bigInteger.mod(BigInteger.valueOf(645328247)).intValue();
                                    if (intValue8 % Opcodes.DCMPL != 0 && intValue8 % Opcodes.IFGT != 0 && intValue8 % Opcodes.IF_ICMPGT != 0 && intValue8 % Opcodes.GOTO != 0) {
                                        int intValue9 = bigInteger.mod(BigInteger.valueOf(1070560157)).intValue();
                                        if (intValue9 % Opcodes.LRETURN != 0 && intValue9 % Opcodes.PUTSTATIC != 0 && intValue9 % Opcodes.PUTFIELD != 0 && intValue9 % Opcodes.ATHROW != 0) {
                                            int intValue10 = bigInteger.mod(BigInteger.valueOf(1596463769)).intValue();
                                            if (intValue10 % Opcodes.INSTANCEOF != 0 && intValue10 % Opcodes.MULTIANEWARRAY != 0 && intValue10 % Opcodes.IFNONNULL != 0 && intValue10 % Primes.SMALL_FACTOR_LIMIT != 0) {
                                                return false;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    public static boolean a(BigInteger bigInteger) {
        a(bigInteger, "candidate");
        return b(bigInteger);
    }

    private static void a(BigInteger bigInteger, String str) {
        if (bigInteger == null || bigInteger.signum() < 1 || bigInteger.bitLength() < 2) {
            throw new IllegalArgumentException("'" + str + "' must be non-null and >= 2");
        }
    }

    private static boolean a(BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, int i, BigInteger bigInteger4) {
        BigInteger modPow = bigInteger4.modPow(bigInteger3, bigInteger);
        if (modPow.equals(a) || modPow.equals(bigInteger2)) {
            return true;
        }
        for (int i2 = 1; i2 < i; i2++) {
            modPow = modPow.modPow(b, bigInteger);
            if (modPow.equals(bigInteger2)) {
                return true;
            }
            if (modPow.equals(a)) {
                return false;
            }
        }
        return false;
    }
}
