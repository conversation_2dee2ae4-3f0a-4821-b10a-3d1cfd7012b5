package o.dw;

import android.content.Context;
import android.os.CancellationSignal;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.n;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dw\b.smali */
public abstract class b extends d<e> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        a = -739697542314667235L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.dw.b.$$a
            int r9 = r9 * 4
            int r9 = 4 - r9
            int r8 = r8 * 3
            int r8 = 71 - r8
            int r7 = r7 * 2
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L34
        L19:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1d:
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r7) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r8]
            r6 = r10
            r10 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L34:
            int r8 = -r8
            int r9 = r9 + r8
            int r8 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.b.g(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{20, -126, 34, 119};
        $$b = Opcodes.GETSTATIC;
    }

    public abstract int getThemeResource(e eVar);

    @Override // o.dw.d
    public /* bridge */ /* synthetic */ void launch(Context context, e eVar, String str, CancellationSignal cancellationSignal) {
        int i = b + 1;
        d = i % 128;
        boolean z = i % 2 != 0;
        launch2(context, eVar, str, cancellationSignal);
        switch (z) {
            case true:
                throw null;
            default:
                int i2 = b + 53;
                d = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    /* renamed from: launch, reason: avoid collision after fix types in other method */
    public final void launch2(Context context, e eVar, String str, CancellationSignal cancellationSignal) {
        String intern;
        Object obj;
        int i = b + 87;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                g.c();
                Object[] objArr = new Object[1];
                f("ユゥ棴\uedd2濂ﭗ쨤퓠羽詛鬽旹꺄奃⠂㛽\udd8d\ue85e濫쟽ಿꝀ踟飁뮹癓弑⧈\ueab3ժ", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1, objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                f("轤輈ﴮ債㶯溍睄蚇쀏ᾌ☙㟌ᅔ첲镎撍扜綇䑜閁댴㊣㍣쪽Щ\ue3a0\ue239篻啴邚兤ꢸ꘨䆡{\ud9b4\uf704\uf17f켔๊䡙Ꙗ븜뽨饚坈洄\uec4b\uea5aѕ\udc0aᵔ㬤땈謸剺豭橦稵荤\udd70", AndroidCharacter.getMirror('0') - '0', objArr2);
                obj = objArr2[0];
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                f("ユゥ棴\uedd2濂ﭗ쨤퓠羽詛鬽旹꺄奃⠂㛽\udd8d\ue85e濫쟽ಿꝀ踟飁뮹癓弑⧈\ueab3ժ", (-1) - (ViewConfiguration.getGlobalActionKeyTimeout() > 1L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 1L ? 0 : -1)), objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f("轤輈ﴮ債㶯溍睄蚇쀏ᾌ☙㟌ᅔ첲镎撍扜綇䑜閁댴㊣㍣쪽Щ\ue3a0\ue239篻啴邚兤ꢸ꘨䆡{\ud9b4\uf704\uf17f켔๊䡙Ꙗ븜뽨饚坈洄\uec4b\uea5aѕ\udc0aᵔ㬤땈謸剺豭橦稵荤\udd70", 'S' / AndroidCharacter.getMirror('!'), objArr4);
                obj = objArr4[0];
                break;
        }
        g.e(intern, ((String) obj).intern());
    }

    private static void f(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2 = $11 + 87;
        $10 = i2 % 128;
        Object obj = null;
        if (i2 % 2 != 0) {
            obj.hashCode();
            throw null;
        }
        switch (str == null) {
            case false:
                cArr = str.toCharArray();
                break;
            default:
                cArr = str;
                break;
        }
        n nVar = new n();
        char[] b2 = n.b(a ^ 8632603938177761503L, cArr, i);
        nVar.c = 4;
        while (nVar.c < b2.length) {
            int i3 = $10 + 3;
            $11 = i3 % 128;
            int i4 = i3 % 2;
            nVar.e = nVar.c - 4;
            int i5 = nVar.c;
            try {
                Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % 4]), Long.valueOf(nVar.e), Long.valueOf(a)};
                Object obj2 = o.e.a.s.get(-1945790373);
                if (obj2 == null) {
                    Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (char) (ViewConfiguration.getEdgeSlop() >> 16), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 42);
                    byte b3 = (byte) 0;
                    byte b4 = (byte) (b3 + 1);
                    Object[] objArr3 = new Object[1];
                    g(b3, b4, (byte) (b4 - 1), objArr3);
                    obj2 = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                    o.e.a.s.put(-1945790373, obj2);
                }
                b2[i5] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {nVar, nVar};
                    Object obj3 = o.e.a.s.get(-341518981);
                    if (obj3 == null) {
                        Class cls2 = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatDelay() >> 16) + 10, (char) KeyEvent.normalizeMetaState(0), 297 - AndroidCharacter.getMirror('0'));
                        byte b5 = (byte) 0;
                        byte b6 = b5;
                        Object[] objArr5 = new Object[1];
                        g(b5, b6, b6, objArr5);
                        obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-341518981, obj3);
                    }
                    ((Method) obj3).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        objArr[0] = new String(b2, 4, b2.length - 4);
    }
}
