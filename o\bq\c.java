package o.bq;

import android.content.ContentValues;
import android.content.Context;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.graphics.Color;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Iterator;
import kotlin.text.Typography;
import o.a.i;
import o.ee.g;
import o.ee.o;
import o.eg.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bq\c.smali */
public final class c extends SQLiteOpenHelper {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static long f;
    private static int h;
    private static int j;
    private final long a;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        e();
        ExpandableListView.getPackedPositionForGroup(0);
        TextUtils.getCapsMode("", 0, 0);
        Color.rgb(0, 0, 0);
        int i = h + 91;
        j = i % 128;
        switch (i % 2 != 0 ? '_' : ';') {
            case ';':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void e() {
        e = (char) 8649;
        d = (char) 62198;
        c = (char) 46450;
        b = (char) 59094;
        f = 193023631315692963L;
    }

    static void init$0() {
        $$a = new byte[]{31, 2, -38, 71};
        $$b = Opcodes.IF_ICMPLT;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002b  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0023  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002b -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 4
            int r8 = r8 * 3
            int r8 = 71 - r8
            byte[] r0 = o.bq.c.$$a
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1d
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1d:
            r3 = r2
        L1e:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2b
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2b:
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r7 = r7 + 1
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1e
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.c.k(short, byte, short, java.lang.Object[]):void");
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onUpgrade(SQLiteDatabase sQLiteDatabase, int i, int i2) {
        int i3 = j + Opcodes.LMUL;
        h = i3 % 128;
        switch (i3 % 2 == 0 ? 'G' : (char) 0) {
            case 0:
                break;
            default:
                int i4 = 73 / 0;
                break;
        }
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public c(android.content.Context r5) {
        /*
            r4 = this;
            int r0 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r0 = (byte) r0
            int r0 = 15 - r0
            r1 = 1
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r2 = "俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~큻葸젳\ud94b"
            g(r2, r0, r1)
            r0 = 0
            r0 = r1[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r1 = 0
            r2 = 11
            r4.<init>(r5, r0, r1, r2)
            int r5 = e(r5)
            long r0 = (long) r5
            r4.a = r0
            r2 = -1
            int r5 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r5 == 0) goto L36
            long r2 = r4.b()
            int r5 = (r2 > r0 ? 1 : (r2 == r0 ? 0 : -1))
            if (r5 <= 0) goto L36
            r4.c()
        L36:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.c.<init>(android.content.Context):void");
    }

    @Override // android.database.sqlite.SQLiteOpenHelper
    public final void onCreate(SQLiteDatabase sQLiteDatabase) {
        Object obj;
        int i = h + 97;
        j = i % 128;
        switch (i % 2 != 0 ? '&' : (char) 4) {
            case 4:
                Object[] objArr = new Object[1];
                g("㭨ᅵ劬ꕥ瀰녞\u2072㼼䊡蹂\ud9bd諠㩯䧏蘞눎Ἠ䗸㤄鞾ଅ\ueebe뙵\uec1e\u0897\uf770憞蘽⢄起\uedda彍툷߸䞔ᳶ㈐题䃺\udfe8鮬⇸壢⾂鮬⇸⥱ӣ驵ꟹᭊ\ua8c9\ueb9a伳졨\ud9d7㯔잽㒯䖫顲⧤懡Բ툷߸䞔ᳶ샗菋綟⏏\u2072㼼ꙟ拁蛃荢\u0ff3榦\uddda띱㤄鞾쇼ኝ\u1f16ꪏ뢩岚쥫鱑授挴탳뜚\u0ef6\u0a29\uf0ed삸懡Բ툷߸䞔ᳶ蹏䒞\udde6⤯授挴탳뜚\u0ef6\u0a29\uf0ed삸උ穔", 119 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                g("㭨ᅵ劬ꕥ瀰녞\u2072㼼䊡蹂\ud9bd諠㩯䧏蘞눎Ἠ䗸㤄鞾ଅ\ueebe뙵\uec1e\u0897\uf770憞蘽⢄起\uedda彍툷߸䞔ᳶ㈐题䃺\udfe8鮬⇸壢⾂鮬⇸⥱ӣ驵ꟹᭊ\ua8c9\ueb9a伳졨\ud9d7㯔잽㒯䖫顲⧤懡Բ툷߸䞔ᳶ샗菋綟⏏\u2072㼼ꙟ拁蛃荢\u0ff3榦\uddda띱㤄鞾쇼ኝ\u1f16ꪏ뢩岚쥫鱑授挴탳뜚\u0ef6\u0a29\uf0ed삸懡Բ툷߸䞔ᳶ蹏䒞\udde6⤯授挴탳뜚\u0ef6\u0a29\uf0ed삸උ穔", 38 % TextUtils.indexOf((CharSequence) "", 'a', 0), objArr2);
                obj = objArr2[0];
                break;
        }
        sQLiteDatabase.execSQL(((String) obj).intern());
        int i2 = h + 65;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    private static int e(Context context) {
        int i = h + Opcodes.DMUL;
        j = i % 128;
        int i2 = i % 2;
        try {
            Object[] objArr = new Object[1];
            i("\ude57㶠\ude31邮\ud84e軓毉芘庸蛆鈕㣎뗘⒌䑮\uf2fa￤\uf289︄곰৶룲끓暓厄۸橙ႍ鶙쳴ᱩ쪡➳髃홢蒻熚愽袘ㅡ뭎⼶䊕", AndroidCharacter.getMirror('0') - '0', objArr);
            int parseInt = Integer.parseInt(o.a(context, ((String) objArr[0]).intern()));
            switch (parseInt < 0 ? (char) 14 : '9') {
                case 14:
                    int i3 = h + 35;
                    j = i3 % 128;
                    int i4 = i3 % 2;
                    parseInt = -1;
                    break;
            }
            g.c();
            Object[] objArr2 = new Object[1];
            g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", 25 - Drawable.resolveOpacity(0, 0), objArr2);
            String intern = ((String) objArr2[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr3 = new Object[1];
            g("㣏\udd78༱錕㨱䛹☔㒭촁䅝䊨挑혺峱姿\uee24\uf464妭Ꞃ\u1afe幆滃ౣ\ue1d5Ꮃ璭㧯쥑쐻ᚥ꾳電⯄\uf725לּ椝䞖纏", 37 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr3);
            g.d(intern, sb.append(((String) objArr3[0]).intern()).append(parseInt).toString());
            return parseInt;
        } catch (PackageManager.NameNotFoundException e2) {
            g.c();
            Object[] objArr4 = new Object[1];
            g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", 25 - Drawable.resolveOpacity(0, 0), objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            g("㣏\udd78༱錕㨱䛹☔㒭촁䅝䊨挑혺峱姿\uee24\uf464妭Ꞃ\u1afe幆滃ౣ\ue1d5Ꮃ璭㧯쥑쐻ᚥ꾳電⯄\uf725\uecb5褏妰鈶\uf3a5檖涞㼌⬁잃䈰邸肗᷼퇁\ue21bꘇ濔Ⓡ熈\ue7f5ⅸ墖ᜀꐺ픯쐻ᚥ꾳電⯄\uf725", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 65, objArr5);
            g.d(intern2, ((String) objArr5[0]).intern());
            return -1;
        }
    }

    public final void a(Context context, o.dr.a aVar, o.bq.e eVar) {
        int i = h + 1;
        j = i % 128;
        int i2 = i % 2;
        try {
            o.eg.b b2 = b.b(aVar);
            int i3 = h + 57;
            j = i3 % 128;
            int i4 = i3 % 2;
            String a = new o.dd.e(context).a(b2.b());
            ContentValues contentValues = new ContentValues();
            Object[] objArr = new Object[1];
            g("툷߸䞔ᳶ㈐题", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 6, objArr);
            contentValues.put(((String) objArr[0]).intern(), aVar.b());
            Object[] objArr2 = new Object[1];
            g("툷߸䞔ᳶ샗菋綟⏏", 9 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr2);
            contentValues.put(((String) objArr2[0]).intern(), a);
            Object[] objArr3 = new Object[1];
            g("툷߸䞔ᳶ憞蘽ꚿⱕ㧯쥑쟉䑧椣ꐇ", 14 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr3);
            contentValues.put(((String) objArr3[0]).intern(), Long.valueOf(aVar.q().getTime()));
            Object[] objArr4 = new Object[1];
            i("ᱱ\uf5a6ᰒ墵嘩촥꧵ಽ隳씎ᰥ笏", 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr4);
            contentValues.put(((String) objArr4[0]).intern(), Integer.valueOf(eVar.c()));
            SQLiteDatabase writableDatabase = getWritableDatabase();
            try {
                Object[] objArr5 = new Object[1];
                g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 17 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr5);
                writableDatabase.insert(((String) objArr5[0]).intern(), null, contentValues);
                switch (writableDatabase == null) {
                    case false:
                        writableDatabase.close();
                        break;
                }
                if (this.a == -1 || b() <= this.a) {
                    return;
                }
                writableDatabase = getWritableDatabase();
                try {
                    Object[] objArr6 = new Object[1];
                    g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", View.MeasureSpec.getMode(0) + 17, objArr6);
                    String intern = ((String) objArr6[0]).intern();
                    Object[] objArr7 = new Object[1];
                    g("툷߸䞔ᳶ㈐题", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 6, objArr7);
                    String[] strArr = {((String) objArr7[0]).intern()};
                    Object[] objArr8 = new Object[1];
                    i("㺋⦽㻨蒮勄跻謏ࡐ䪸藐ᣕ㯑唘ゕ컩\uf1e9Ἣ\ue6d1瓙꿇\ue908", Color.alpha(0), objArr8);
                    String intern2 = ((String) objArr8[0]).intern();
                    Object[] objArr9 = new Object[1];
                    g("䞅천", (ViewConfiguration.getWindowTouchSlop() >> 8) + 1, objArr9);
                    Cursor query = writableDatabase.query(intern, strArr, null, null, null, null, intern2, ((String) objArr9[0]).intern());
                    switch (query != null ? Typography.dollar : (char) 18) {
                        default:
                            if (query.moveToNext()) {
                                Object[] objArr10 = new Object[1];
                                g("툷߸䞔ᳶ㈐题", 6 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr10);
                                String string = query.getString(query.getColumnIndex(((String) objArr10[0]).intern()));
                                Object[] objArr11 = new Object[1];
                                g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 16 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr11);
                                String intern3 = ((String) objArr11[0]).intern();
                                Object[] objArr12 = new Object[1];
                                i("븈㜜빫騏駂규\u0b91썖吔ꗷ펞ᮮ헕\u2e60ֱ", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr12);
                                writableDatabase.delete(intern3, ((String) objArr12[0]).intern(), new String[]{string});
                                if (writableDatabase != null) {
                                    writableDatabase.close();
                                    return;
                                }
                                return;
                            }
                        case 18:
                            if (writableDatabase != null) {
                                writableDatabase.close();
                            }
                            int i5 = h + Opcodes.LSHL;
                            j = i5 % 128;
                            int i6 = i5 % 2;
                            return;
                    }
                } finally {
                }
            } finally {
            }
        } catch (d e2) {
            g.c();
            Object[] objArr13 = new Object[1];
            g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", View.resolveSizeAndState(0, 0, 0) + 25, objArr13);
            String intern4 = ((String) objArr13[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr14 = new Object[1];
            i("頲볯顅ᇡ\uea96휚\u2da7뀇\udfd7\udf1aꂝ感\uf3bcꗀ皾ꬭ릖珊철\uf530俒㧾芟㽏ᗠ螬壝䥊\udbfb䶝\u2ef8錢憂ᮩ\ue4dc\udd51㟼\ue056먇梍ﴧ깳瀛늗茽瑝왿ﲡ䤌숃鰼ڻἀ蠺剞僂ꕫ嘹⡎髊死ᰜﹱ⒮ㄘ\uea43", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1, objArr14);
            g.e(intern4, sb.append(((String) objArr14[0]).intern()).append(e2).toString());
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:18:0x002f. Please report as an issue. */
    public final void e(Context context, o.dr.a aVar) {
        int i = j + 95;
        h = i % 128;
        try {
            switch (i % 2 == 0) {
                case false:
                    o.eg.b b2 = b.b(aVar);
                    int i2 = j + 21;
                    h = i2 % 128;
                    switch (i2 % 2 == 0 ? 'G' : 'W') {
                    }
                    String a = new o.dd.e(context).a(b2.b());
                    ContentValues contentValues = new ContentValues();
                    Object[] objArr = new Object[1];
                    g("툷߸䞔ᳶ샗菋綟⏏", 8 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
                    contentValues.put(((String) objArr[0]).intern(), a);
                    Object[] objArr2 = new Object[1];
                    g("툷߸䞔ᳶ憞蘽ꚿⱕ㧯쥑쟉䑧椣ꐇ", (ViewConfiguration.getEdgeSlop() >> 16) + 13, objArr2);
                    contentValues.put(((String) objArr2[0]).intern(), Long.valueOf(aVar.q().getTime()));
                    Object[] objArr3 = new Object[1];
                    g("툷߸䞔ᳶ㈐题⮞䝫폕\ue35f", 11 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
                    String intern = ((String) objArr3[0]).intern();
                    String[] strArr = {aVar.b()};
                    SQLiteDatabase writableDatabase = getWritableDatabase();
                    try {
                        Object[] objArr4 = new Object[1];
                        g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 16 - TextUtils.lastIndexOf("", '0', 0), objArr4);
                        writableDatabase.update(((String) objArr4[0]).intern(), contentValues, intern, strArr);
                        if (writableDatabase != null) {
                            writableDatabase.close();
                            return;
                        }
                        int i3 = j + 49;
                        h = i3 % 128;
                        if (i3 % 2 != 0) {
                            return;
                        } else {
                            throw null;
                        }
                    } catch (Throwable th) {
                        if (writableDatabase != null) {
                            try {
                                writableDatabase.close();
                            } catch (Throwable th2) {
                                th.addSuppressed(th2);
                            }
                        }
                        throw th;
                    }
                default:
                    b.b(aVar);
                    throw null;
            }
        } catch (d e2) {
            g.c();
            Object[] objArr5 = new Object[1];
            g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", 25 - View.resolveSize(0, 0), objArr5);
            String intern2 = ((String) objArr5[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr6 = new Object[1];
            i("頲볯顅ᇡ\uea96휚\u2da7뀇\udfd7\udf1aꂝ感\uf3bcꗀ皾ꬭ릖珊철\uf530俒㧾芟㽏ᗠ螬壝䥊\udbfb䶝\u2ef8錢憂ᮩ\ue4dc\udd51㟼\ue056먇梍ﴧ깳瀛늗茽瑝왿ﲡ䤌숃鰼ڻἀ蠺剞僂ꕫ嘹⡎髊死ᰜﹱ⒮ㄘ\uea43", View.resolveSizeAndState(0, 0, 0), objArr6);
            g.e(intern2, sb.append(((String) objArr6[0]).intern()).append(e2).toString());
        }
    }

    public final void e(String str, o.bq.e eVar) {
        ContentValues contentValues = new ContentValues();
        Object[] objArr = new Object[1];
        i("ᱱ\uf5a6ᰒ墵嘩촥꧵ಽ隳씎ᰥ笏", 1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
        contentValues.put(((String) objArr[0]).intern(), Integer.valueOf(eVar.c()));
        Object[] objArr2 = new Object[1];
        g("툷߸䞔ᳶ㈐题⮞䝫폕\ue35f", 10 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr2);
        String intern = ((String) objArr2[0]).intern();
        String[] strArr = {str};
        SQLiteDatabase writableDatabase = getWritableDatabase();
        try {
            Object[] objArr3 = new Object[1];
            g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 17 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr3);
            writableDatabase.update(((String) objArr3[0]).intern(), contentValues, intern, strArr);
            switch (writableDatabase != null ? (char) 31 : 'Z') {
                case 31:
                    int i = j + 79;
                    h = i % 128;
                    switch (i % 2 != 0) {
                        case false:
                            writableDatabase.close();
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        default:
                            writableDatabase.close();
                            return;
                    }
                default:
                    int i2 = h + 63;
                    j = i2 % 128;
                    int i3 = i2 % 2;
                    return;
            }
        } catch (Throwable th) {
            if (writableDatabase != null) {
                try {
                    writableDatabase.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    public final void a(String str) {
        int i = j + 31;
        h = i % 128;
        int i2 = i % 2;
        SQLiteDatabase writableDatabase = getWritableDatabase();
        try {
            Object[] objArr = new Object[1];
            g("툷߸䞔ᳶ㈐题⮞䝫폕\ue35f", 9 - TextUtils.lastIndexOf("", '0', 0, 0), objArr);
            Object[] objArr2 = new Object[1];
            g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 17 - TextUtils.getTrimmedLength(""), objArr2);
            writableDatabase.delete(((String) objArr2[0]).intern(), ((String) objArr[0]).intern(), new String[]{str});
            switch (writableDatabase != null) {
                case false:
                    int i3 = j + 57;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                    return;
                default:
                    writableDatabase.close();
                    return;
            }
        } catch (Throwable th) {
            if (writableDatabase != null) {
                try {
                    writableDatabase.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:36:0x00d4, code lost:
    
        if (r10 != null) goto L38;
     */
    /* JADX WARN: Code restructure failed: missing block: B:37:0x00da, code lost:
    
        return null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:38:0x00d7, code lost:
    
        r10.close();
     */
    /* JADX WARN: Code restructure failed: missing block: B:44:0x00ce, code lost:
    
        if (r10 != null) goto L38;
     */
    /* JADX WARN: Removed duplicated region for block: B:57:0x013a A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:89:0x01e4 A[EXC_TOP_SPLITTER, SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:96:? A[DONT_GENERATE, FINALLY_INSNS, SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.dr.a b(android.content.Context r15, java.lang.String r16) {
        /*
            Method dump skipped, instructions count: 530
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.c.b(android.content.Context, java.lang.String):o.dr.a");
    }

    public final e c(Context context, o.bq.e[] eVarArr, int i, int i2) {
        int i3;
        String obj;
        String intern;
        String intern2;
        SQLiteDatabase writableDatabase = getWritableDatabase();
        try {
            Object[] objArr = new Object[1];
            g("툷߸䞔ᳶ㈐题", 6 - KeyEvent.keyCodeFromString(""), objArr);
            int i4 = 0;
            Object[] objArr2 = new Object[1];
            i("ᱱ\uf5a6ᰒ墵嘩촥꧵ಽ隳씎ᰥ笏", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, objArr2);
            Object[] objArr3 = new Object[1];
            g("툷߸䞔ᳶ샗菋綟⏏", (ViewConfiguration.getWindowTouchSlop() >> 8) + 8, objArr3);
            String[] strArr = {((String) objArr[0]).intern(), ((String) objArr2[0]).intern(), ((String) objArr3[0]).intern()};
            Object[] objArr4 = new Object[1];
            i("贄ឈ赧몛\uf4b4衬㢀긠璍聇뺥㹆\ue697ຠ梙\uf47e겤\ud8e4튬ꩆ媗鋷", ViewConfiguration.getKeyRepeatDelay() >> 16, objArr4);
            String intern3 = ((String) objArr4[0]).intern();
            Object obj2 = null;
            if (i2 <= 0) {
                int i5 = j;
                int i6 = i5 + Opcodes.LREM;
                h = i6 % 128;
                int i7 = i6 % 2;
                switch (i2 == -1 ? (char) 18 : 'E') {
                    case 'E':
                        break;
                    default:
                        int i8 = i5 + 59;
                        h = i8 % 128;
                        switch (i8 % 2 == 0) {
                            case false:
                                g.c();
                                Object[] objArr5 = new Object[1];
                                g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", (KeyEvent.getMaxKeyCode() >> 16) + 25, objArr5);
                                intern = ((String) objArr5[0]).intern();
                                Object[] objArr6 = new Object[1];
                                i("鵯핀鴝硙퐐찡⣋躉뙞쐱鸀稻\uf6fc챽䠲뀑볖ᩣ\uf22f\uee06䪏偑밑⑵Ⴞ\uee1f晒剼\udea1\u243bေ蠈擎牬\uda60왆㋏觐蒘玨\uf876쟘仁ꦼ虼ᶼ\uf8bf\ue78a䱋ꮬꊳᶐᩌ\ue193泜䯨ꀺ㾂ᛅ臰渫痰삱㿉㐐莭諥疕숛\ud94e㜐ꌫ觬ᝍ\ue102\ud921埦굳ꬿ\u1716ᶟﭥ唯䵕ꯍㄝὅﭦ熷佌쥈ㅻ㾼蔨獴潄얛퍬㵣ꕌ鏏滉\ue782튬奱\ua4cb釁ࢺ\ue769\uf2fa客䚀굋ࢬקﳕ筃䚕쿜⫬ī鳌科惴켽⪽⏼黀锋悩\uedf3퓆", ViewConfiguration.getTapTimeout() >> 16, objArr6);
                                intern2 = ((String) objArr6[0]).intern();
                                break;
                            default:
                                g.c();
                                Object[] objArr7 = new Object[1];
                                g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", Opcodes.LUSHR / (KeyEvent.getMaxKeyCode() >> 11), objArr7);
                                intern = ((String) objArr7[0]).intern();
                                Object[] objArr8 = new Object[1];
                                i("鵯핀鴝硙퐐찡⣋躉뙞쐱鸀稻\uf6fc챽䠲뀑볖ᩣ\uf22f\uee06䪏偑밑⑵Ⴞ\uee1f晒剼\udea1\u243bေ蠈擎牬\uda60왆㋏觐蒘玨\uf876쟘仁ꦼ虼ᶼ\uf8bf\ue78a䱋ꮬꊳᶐᩌ\ue193泜䯨ꀺ㾂ᛅ臰渫痰삱㿉㐐莭諥疕숛\ud94e㜐ꌫ觬ᝍ\ue102\ud921埦굳ꬿ\u1716ᶟﭥ唯䵕ꯍㄝὅﭦ熷佌쥈ㅻ㾼蔨獴潄얛퍬㵣ꕌ鏏滉\ue782튬奱\ua4cb釁ࢺ\ue769\uf2fa客䚀굋ࢬקﳕ筃䚕쿜⫬ī鳌科惴켽⪽⏼黀锋悩\uedf3퓆", ViewConfiguration.getTapTimeout() * 24, objArr8);
                                intern2 = ((String) objArr8[0]).intern();
                                break;
                        }
                        g.d(intern, intern2);
                        break;
                }
                obj = null;
            } else {
                if (i < 0) {
                    g.c();
                    Object[] objArr9 = new Object[1];
                    g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", 26 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr9);
                    String intern4 = ((String) objArr9[0]).intern();
                    Object[] objArr10 = new Object[1];
                    g("萛퇇䢠铃俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀뙵\uec1e䈰邸瘻遢㚻⿐蚦ᛜ둠\ude42㨱䛹\uecb5褏\ue85aꪾꢍ｛ꨧ볢\uf464妭Ꞃ\u1afeﶵ\uf4afꮚጐ\u0897\uf770\uf464妭蕐ꓟ坓軮嗼念", TextUtils.indexOf((CharSequence) "", '0', 0) + 55, objArr10);
                    g.d(intern4, ((String) objArr10[0]).intern());
                    i3 = 0;
                } else {
                    i3 = i;
                }
                StringBuilder append = new StringBuilder().append(i3);
                Object[] objArr11 = new Object[1];
                i("쪶\ue0dc쪚⍜ใ", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr11);
                obj = append.append(((String) objArr11[0]).intern()).append(i2).toString();
            }
            String[] strArr2 = new String[eVarArr.length];
            Object[] objArr12 = new Object[1];
            g("授挴ৢ\u2fe0\u0a0e㋒", 5 - Color.green(0), objArr12);
            StringBuilder sb = new StringBuilder(((String) objArr12[0]).intern());
            int i9 = 0;
            while (i9 < eVarArr.length) {
                strArr2[i9] = String.valueOf(eVarArr[i9].c());
                int i10 = -(ExpandableListView.getPackedPositionForChild(i4, i4) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(i4, i4) == 0L ? 0 : -1));
                Object[] objArr13 = new Object[1];
                g("ꢪ♖", i10, objArr13);
                sb.append(((String) objArr13[0]).intern());
                if (i9 < eVarArr.length - 1) {
                    Object[] objArr14 = new Object[1];
                    i("쪶\ue0dc쪚⍜ใ", TextUtils.getCapsMode("", 0, 0), objArr14);
                    sb.append(((String) objArr14[0]).intern());
                }
                i9++;
                i4 = 0;
            }
            Object[] objArr15 = new Object[1];
            g("\ue926麡", 1 - Drawable.resolveOpacity(0, 0), objArr15);
            sb.append(((String) objArr15[0]).intern());
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr16 = new Object[1];
            i("ᱱ\uf5a6ᰒ墵嘩촥꧵ಽ隳씎ᰥ笏", Color.red(0), objArr16);
            String obj3 = sb2.append(((String) objArr16[0]).intern()).append((Object) sb).toString();
            Object[] objArr17 = new Object[1];
            g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", Color.alpha(0) + 17, objArr17);
            Cursor query = writableDatabase.query(((String) objArr17[0]).intern(), strArr, obj3, strArr2, null, null, intern3, obj);
            if (!query.moveToFirst()) {
                e eVar = new e(context);
                if (writableDatabase != null) {
                    int i11 = j + Opcodes.LSHR;
                    h = i11 % 128;
                    if (i11 % 2 == 0) {
                        writableDatabase.close();
                        obj2.hashCode();
                        throw null;
                    }
                    writableDatabase.close();
                }
                return eVar;
            }
            Object[] objArr18 = new Object[1];
            i("ᱱ\uf5a6ᰒ墵嘩촥꧵ಽ隳씎ᰥ笏", KeyEvent.getDeadChar(0, 0), objArr18);
            int columnIndex = query.getColumnIndex(((String) objArr18[0]).intern());
            Object[] objArr19 = new Object[1];
            g("툷߸䞔ᳶ샗菋綟⏏", (ViewConfiguration.getJumpTapTimeout() >> 16) + 8, objArr19);
            int columnIndex2 = query.getColumnIndex(((String) objArr19[0]).intern());
            Object[] objArr20 = new Object[1];
            g("툷߸䞔ᳶ㈐题", 6 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr20);
            e eVar2 = new e(context, query, columnIndex, columnIndex2, query.getColumnIndex(((String) objArr20[0]).intern()));
            switch (writableDatabase != null) {
                case true:
                    writableDatabase.close();
                default:
                    return eVar2;
            }
        } catch (Throwable th) {
            if (writableDatabase == null) {
                throw th;
            }
            try {
                writableDatabase.close();
                throw th;
            } catch (Throwable th2) {
                th.addSuppressed(th2);
                throw th;
            }
        }
    }

    private void c() {
        SQLiteDatabase readableDatabase = getReadableDatabase();
        try {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            i("奯訲夼✋ޚヘ\uecdc崮\ue90a㣩䷦蚧㊯錨鮤䳲磲䕞→ዌ軍ར濳\ud882풫녬뗧꺣᪼筏쏕璉ꂖⵑৈ㫍\uf6a0횜園轘㱍飾鴄啔䈯䊍⬙᭑衠\uf48a焏\ue140\ude4a뻽뽢뜼搲惮씦紉ꨊ⫽፵썝\uf05f\udcf2奯褠ئ蘚\ue4f6徬䶿䠑㊀○鏜\uf22b碢\ueb9d", (-1) - TextUtils.indexOf((CharSequence) "", '0'), objArr);
            Cursor rawQuery = readableDatabase.rawQuery(sb.append(((String) objArr[0]).intern()).append(this.a).toString(), null);
            switch (rawQuery != null ? '\\' : (char) 1) {
                default:
                    int i = j + 51;
                    h = i % 128;
                    int i2 = i % 2;
                    switch (rawQuery.getCount() == 0 ? '[' : 'O') {
                        case Opcodes.IASTORE /* 79 */:
                            Object[] objArr2 = new Object[1];
                            g("툷߸䞔ᳶ憞蘽ꚿⱕ㧯쥑쟉䑧椣ꐇ", 13 - KeyEvent.getDeadChar(0, 0), objArr2);
                            long j2 = rawQuery.moveToNext() ? rawQuery.getLong(rawQuery.getColumnIndex(((String) objArr2[0]).intern())) : 0L;
                            rawQuery.close();
                            Object[] objArr3 = new Object[1];
                            g("뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5", 17 - ((Process.getThreadPriority(0) + 20) >> 6), objArr3);
                            String intern = ((String) objArr3[0]).intern();
                            StringBuilder sb2 = new StringBuilder();
                            Object[] objArr4 = new Object[1];
                            i("凯䫸册\ue7eb澝㐻\ue46b㔉⧽㰐▌舑㩼叐\uf3b0䠩灏薔䧽ᙩ蘏", ViewConfiguration.getTouchSlop() >> 8, objArr4);
                            int delete = readableDatabase.delete(intern, sb2.append(((String) objArr4[0]).intern()).append(j2).toString(), null);
                            g.c();
                            Object[] objArr5 = new Object[1];
                            g("俶ⶑ္ꋕ喐鑓ᶬ䪝ᕫ銀辤~ꢍ｛蘞눎榯ぁ旉᪖䡬驘\udde6⤯崦돔", MotionEvent.axisFromString("") + 26, objArr5);
                            String intern2 = ((String) objArr5[0]).intern();
                            StringBuilder sb3 = new StringBuilder();
                            Object[] objArr6 = new Object[1];
                            i("뺐熂뻳\udc8c쬇膡ନ醐ኇ覥脋㞵픟梬圱\ufdd1齭뻮\ued3cꎄ椼\uf4dbꍜ槴㍄䪎祔Ῠﵞ胻ཻ양䜺횮", KeyEvent.keyCodeFromString(""), objArr6);
                            g.d(intern2, sb3.append(((String) objArr6[0]).intern()).append(delete).toString());
                            if (readableDatabase == null) {
                                int i3 = h + Opcodes.DMUL;
                                j = i3 % 128;
                                int i4 = i3 % 2;
                                return;
                            } else {
                                readableDatabase.close();
                                int i5 = h + 5;
                                j = i5 % 128;
                                int i6 = i5 % 2;
                                return;
                            }
                    }
                case 1:
                    switch (readableDatabase != null ? 'W' : '8') {
                        case '8':
                            return;
                        default:
                            int i7 = h + 109;
                            j = i7 % 128;
                            if (i7 % 2 == 0) {
                                readableDatabase.close();
                                return;
                            } else {
                                readableDatabase.close();
                                int i8 = 41 / 0;
                                return;
                            }
                    }
            }
        } catch (Throwable th) {
            if (readableDatabase != null) {
                try {
                    readableDatabase.close();
                } catch (Throwable th2) {
                    th.addSuppressed(th2);
                }
            }
            throw th;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:28:0x003b, code lost:
    
        if (r0 != null) goto L12;
     */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v11, types: [int] */
    /* JADX WARN: Type inference failed for: r0v15 */
    /* JADX WARN: Type inference failed for: r0v3 */
    /* JADX WARN: Type inference failed for: r0v4 */
    /* JADX WARN: Type inference failed for: r0v5, types: [android.database.sqlite.SQLiteDatabase] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private long b() {
        /*
            r8 = this;
            int r0 = o.bq.c.j
            int r0 = r0 + 113
            int r1 = r0 % 128
            o.bq.c.h = r1
            int r0 = r0 % 2
            r1 = 1
            if (r0 != 0) goto Lf
            r0 = r1
            goto L11
        Lf:
            r0 = 22
        L11:
            r2 = 0
            r3 = -1
            java.lang.String r5 = "뢩岚샗菋屲三⇳ꨊ္ꋕ喐鑓ᶬ䪝ᕫ銀벣\ue8f5"
            switch(r0) {
                case 22: goto L1e;
                default: goto L19;
            }
        L19:
            android.database.sqlite.SQLiteDatabase r0 = r8.getReadableDatabase()
            goto L3e
        L1e:
            android.database.sqlite.SQLiteDatabase r0 = r8.getReadableDatabase()
            long r6 = android.os.SystemClock.currentThreadTimeMillis()     // Catch: java.lang.Throwable -> L79
            int r3 = (r6 > r3 ? 1 : (r6 == r3 ? 0 : -1))
            int r3 = r3 + 16
            java.lang.Object[] r1 = new java.lang.Object[r1]     // Catch: java.lang.Throwable -> L79
            g(r5, r3, r1)     // Catch: java.lang.Throwable -> L79
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L79
            java.lang.String r1 = (java.lang.String) r1     // Catch: java.lang.Throwable -> L79
            java.lang.String r1 = r1.intern()     // Catch: java.lang.Throwable -> L79
            long r1 = android.database.DatabaseUtils.queryNumEntries(r0, r1)     // Catch: java.lang.Throwable -> L79
            if (r0 == 0) goto L67
        L3d:
            goto L63
        L3e:
            long r6 = android.os.SystemClock.currentThreadTimeMillis()     // Catch: java.lang.Throwable -> L79
            int r3 = (r6 > r3 ? 1 : (r6 == r3 ? 0 : -1))
            r4 = 127(0x7f, float:1.78E-43)
            int r4 = r4 % r3
            java.lang.Object[] r1 = new java.lang.Object[r1]     // Catch: java.lang.Throwable -> L79
            g(r5, r4, r1)     // Catch: java.lang.Throwable -> L79
            r1 = r1[r2]     // Catch: java.lang.Throwable -> L79
            java.lang.String r1 = (java.lang.String) r1     // Catch: java.lang.Throwable -> L79
            java.lang.String r1 = r1.intern()     // Catch: java.lang.Throwable -> L79
            long r1 = android.database.DatabaseUtils.queryNumEntries(r0, r1)     // Catch: java.lang.Throwable -> L79
            if (r0 == 0) goto L5d
            r3 = 95
            goto L5f
        L5d:
            r3 = 96
        L5f:
            switch(r3) {
                case 96: goto L67;
                default: goto L62;
            }
        L62:
            goto L3d
        L63:
            r0.close()
        L67:
            int r0 = o.bq.c.h
            int r0 = r0 + 119
            int r3 = r0 % 128
            o.bq.c.j = r3
            int r0 = r0 % 2
            if (r0 != 0) goto L75
            return r1
        L75:
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L77
        L77:
            r0 = move-exception
            throw r0
        L79:
            r1 = move-exception
            if (r0 == 0) goto L84
            r0.close()     // Catch: java.lang.Throwable -> L80
            goto L84
        L80:
            r0 = move-exception
            r1.addSuppressed(r0)
        L84:
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.c.b():long");
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bq\c$e.smali */
    public static final class e implements AutoCloseable, Iterator<o.dr.a> {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int f;
        private static int g;
        private static char[] h;
        private static char i;
        private final int a;
        private final Cursor b;
        private final Context c;
        private final int d;
        private final int e;
        private o.bq.e j;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            g = 1;
            h = new char[]{30568, 30562, 30539, 30591, 30583, 30572, 30498, 30561, 30570, 30567, 30517, 30535, 30588, 30586, 30589, 30573, 30587, 30555, 30569, 30571, 30574, 30563, 30560, 30566, 30511};
            i = (char) 17040;
        }

        static void init$0() {
            $$a = new byte[]{71, -50, -52, -118};
            $$b = Opcodes.ARETURN;
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
        /* JADX WARN: Type inference failed for: r7v2, types: [int] */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(short r5, short r6, short r7, java.lang.Object[] r8) {
            /*
                int r6 = r6 * 4
                int r6 = r6 + 1
                byte[] r0 = o.bq.c.e.$$a
                int r7 = r7 * 2
                int r7 = 4 - r7
                int r5 = 73 - r5
                byte[] r1 = new byte[r6]
                int r6 = r6 + (-1)
                r2 = 0
                if (r0 != 0) goto L16
                r4 = r7
                r3 = r2
                goto L28
            L16:
                r3 = r2
            L17:
                byte r4 = (byte) r5
                r1[r3] = r4
                if (r3 != r6) goto L24
                java.lang.String r5 = new java.lang.String
                r5.<init>(r1, r2)
                r8[r2] = r5
                return
            L24:
                int r3 = r3 + 1
                r4 = r0[r7]
            L28:
                int r7 = r7 + 1
                int r5 = r5 + r4
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bq.c.e.l(short, short, short, java.lang.Object[]):void");
        }

        @Override // java.util.Iterator
        public final /* synthetic */ o.dr.a next() {
            int i2 = f + 95;
            g = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    o.dr.a d = d();
                    int i3 = g + 65;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                    return d;
                default:
                    d();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        e(Context context) {
            this(context, null, 0, 0, 0);
        }

        e(Context context, Cursor cursor, int i2, int i3, int i4) {
            this.c = context;
            this.b = cursor;
            this.e = i2;
            this.a = i3;
            this.d = i4;
        }

        public final o.bq.e a() {
            int i2 = g + 83;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            o.bq.e eVar = this.j;
            int i5 = i3 + 43;
            g = i5 % 128;
            int i6 = i5 % 2;
            return eVar;
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:12:0x002d. Please report as an issue. */
        @Override // java.util.Iterator
        public final boolean hasNext() {
            Cursor cursor = this.b;
            switch (cursor == null) {
                default:
                    switch (!cursor.isAfterLast() ? '`' : 'M') {
                        case 'M':
                            break;
                        default:
                            int i2 = g;
                            int i3 = i2 + 97;
                            f = i3 % 128;
                            switch (i3 % 2 != 0 ? (char) 20 : '(') {
                            }
                            int i4 = i2 + 5;
                            f = i4 % 128;
                            if (i4 % 2 == 0) {
                                return true;
                            }
                            throw null;
                    }
                case true:
                    return false;
            }
        }

        public final o.dr.a d() {
            int i2 = g + 45;
            int i3 = i2 % 128;
            f = i3;
            int i4 = i2 % 2;
            Cursor cursor = this.b;
            switch (cursor != null ? '#' : '8') {
                case '#':
                    int i5 = i3 + 39;
                    g = i5 % 128;
                    o.dr.a aVar = null;
                    switch (i5 % 2 != 0) {
                        case true:
                            if (!cursor.isAfterLast()) {
                                o.dd.e eVar = new o.dd.e(this.c);
                                this.j = o.bq.e.a(this.b.getInt(this.e));
                                String d = eVar.d(this.b.getString(this.a));
                                String string = this.b.getString(this.d);
                                if (this.j == null || d == null || d.isEmpty()) {
                                    g.c();
                                    Object[] objArr = new Object[1];
                                    k(TextUtils.getOffsetBefore("", 0) + 25, "\u0013\f\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\f\u0007\u0015\u000f\u0000\u0014\u0016\n\u0006\r\u0006\u0017\b\r㘉", (byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 33), objArr);
                                    String intern = ((String) objArr[0]).intern();
                                    Object[] objArr2 = new Object[1];
                                    k(Color.red(0) + 67, "\r\t\u0018\u000f\u0013\f\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\t\u0016\t\u0015\u0013\u000b\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\t\u0016\n\u0000㙪㙪\u0015\u0011\u0014\u0013\t\u0017\r\t\u000b\u0011\u0018\f\t\u0012\u0017\u0013\f\u0018\u0004\u0015\b\u0014\u0004\b\t\r\t\u0012\u0014\u0013\u0016\u0017㙮", (byte) (118 - Color.argb(0, 0, 0, 0)), objArr2);
                                    g.e(intern, ((String) objArr2[0]).intern());
                                    int i6 = g + 21;
                                    f = i6 % 128;
                                    int i7 = i6 % 2;
                                } else {
                                    try {
                                        aVar = a.a(new o.eg.b(d), string);
                                    } catch (d e) {
                                        g.c();
                                        Object[] objArr3 = new Object[1];
                                        k(Color.rgb(0, 0, 0) + 16777241, "\u0013\f\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\f\u0007\u0015\u000f\u0000\u0014\u0016\n\u0006\r\u0006\u0017\b\r㘉", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 34), objArr3);
                                        String intern2 = ((String) objArr3[0]).intern();
                                        Object[] objArr4 = new Object[1];
                                        k((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 49, "\r\t\u0018\u000f\u0013\f\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\t\u0016\t\u0015\u0013\u000b\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\t\u0016\u0000\u0017\n\r\u0016\b\u0004\u0014\t\u0003\u0006\t\u0001\u0012\u0018\u0017\t\u0016\u000e\u0014", (byte) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 26), objArr4);
                                        g.a(intern2, ((String) objArr4[0]).intern(), e);
                                    }
                                }
                                this.b.moveToNext();
                                int i8 = f + 45;
                                g = i8 % 128;
                                int i9 = i8 % 2;
                                return aVar;
                            }
                            break;
                        default:
                            cursor.isAfterLast();
                            throw null;
                    }
            }
            Object[] objArr5 = new Object[1];
            k(Gravity.getAbsoluteGravity(0, 0) + 34, "\u0013\u000b\u0016\u0005\n\u0016\u0006\u000f\u0018\u0017\t\u0016\u0015\u0012\t\r\u0015\u000f\u0018\f\u0014\u0017\u000b\u0012\u0014\u0017\u0013\u0017\u0011\u0014\f\b\u0011\u000e", (byte) (112 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1))), objArr5);
            throw new IndexOutOfBoundsException(((String) objArr5[0]).intern());
        }

        @Override // java.lang.AutoCloseable
        public final void close() {
            Cursor cursor = this.b;
            switch (cursor != null) {
                case false:
                    return;
                default:
                    int i2 = g + 25;
                    f = i2 % 128;
                    switch (i2 % 2 == 0) {
                        case true:
                            if (cursor.isClosed()) {
                                return;
                            }
                            break;
                        default:
                            int i3 = 63 / 0;
                            switch (cursor.isClosed() ? false : true) {
                                case false:
                                    return;
                            }
                    }
                    int i4 = g + 55;
                    f = i4 % 128;
                    if (i4 % 2 == 0) {
                        this.b.close();
                        return;
                    } else {
                        this.b.close();
                        int i5 = 27 / 0;
                        return;
                    }
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void k(int r23, java.lang.String r24, byte r25, java.lang.Object[] r26) {
            /*
                Method dump skipped, instructions count: 1088
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.bq.c.e.k(int, java.lang.String, byte, java.lang.Object[]):void");
        }
    }

    private static void g(String str, int i, Object[] objArr) {
        char[] charArray;
        int i2 = $10 + 13;
        int i3 = i2 % 128;
        $11 = i3;
        int i4 = i2 % 2;
        int i5 = 0;
        Object obj = null;
        switch (str != null) {
            case true:
                int i6 = i3 + 33;
                $10 = i6 % 128;
                if (i6 % 2 != 0) {
                    str.toCharArray();
                    obj.hashCode();
                    throw null;
                }
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr = charArray;
        i iVar = new i();
        char[] cArr2 = new char[cArr.length];
        iVar.b = 0;
        char[] cArr3 = new char[2];
        int i7 = $11 + 61;
        $10 = i7 % 128;
        switch (i7 % 2 == 0) {
        }
        while (iVar.b < cArr.length) {
            cArr3[i5] = cArr[iVar.b];
            cArr3[1] = cArr[iVar.b + 1];
            int i8 = 58224;
            int i9 = i5;
            while (i9 < 16) {
                int i10 = $10 + 1;
                $11 = i10 % 128;
                int i11 = i10 % 2;
                char c2 = cArr3[1];
                char c3 = cArr3[i5];
                char[] cArr4 = cArr2;
                int i12 = (c3 + i8) ^ ((c3 << 4) + ((char) (d ^ 8439748517800462901L)));
                int i13 = c3 >>> 5;
                try {
                    Object[] objArr2 = new Object[4];
                    objArr2[3] = Integer.valueOf(c);
                    objArr2[2] = Integer.valueOf(i13);
                    objArr2[1] = Integer.valueOf(i12);
                    objArr2[i5] = Integer.valueOf(c2);
                    Object obj2 = o.e.a.s.get(-1512468642);
                    if (obj2 == null) {
                        Class cls = (Class) o.e.a.c(TextUtils.indexOf("", "", i5, i5) + 11, (char) Color.red(i5), 602 - Process.getGidForName(""));
                        Class<?>[] clsArr = new Class[4];
                        clsArr[i5] = Integer.TYPE;
                        clsArr[1] = Integer.TYPE;
                        clsArr[2] = Integer.TYPE;
                        clsArr[3] = Integer.TYPE;
                        obj2 = cls.getMethod("C", clsArr);
                        o.e.a.s.put(-1512468642, obj2);
                    }
                    char charValue = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                    cArr3[1] = charValue;
                    i iVar2 = iVar;
                    try {
                        Object[] objArr3 = {Integer.valueOf(cArr3[i5]), Integer.valueOf((charValue + i8) ^ ((charValue << 4) + ((char) (b ^ 8439748517800462901L)))), Integer.valueOf(charValue >>> 5), Integer.valueOf(e)};
                        Object obj3 = o.e.a.s.get(-1512468642);
                        if (obj3 == null) {
                            obj3 = ((Class) o.e.a.c((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 11, (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 603)).getMethod("C", Integer.TYPE, Integer.TYPE, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1512468642, obj3);
                        }
                        cArr3[0] = ((Character) ((Method) obj3).invoke(null, objArr3)).charValue();
                        i8 -= 40503;
                        i9++;
                        int i14 = $11 + 99;
                        $10 = i14 % 128;
                        switch (i14 % 2 == 0) {
                            case true:
                            default:
                                cArr2 = cArr4;
                                iVar = iVar2;
                                i5 = 0;
                        }
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            i iVar3 = iVar;
            char[] cArr5 = cArr2;
            cArr5[iVar3.b] = cArr3[0];
            cArr5[iVar3.b + 1] = cArr3[1];
            try {
                Object[] objArr4 = {iVar3, iVar3};
                Object obj4 = o.e.a.s.get(2062727845);
                if (obj4 == null) {
                    obj4 = ((Class) o.e.a.c(10 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 30726), (ViewConfiguration.getPressedStateDuration() >> 16) + 614)).getMethod("A", Object.class, Object.class);
                    o.e.a.s.put(2062727845, obj4);
                }
                ((Method) obj4).invoke(null, objArr4);
                iVar = iVar3;
                cArr2 = cArr5;
                i5 = 0;
            } catch (Throwable th3) {
                Throwable cause3 = th3.getCause();
                if (cause3 == null) {
                    throw th3;
                }
                throw cause3;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 398
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.c.i(java.lang.String, int, java.lang.Object[]):void");
    }
}
