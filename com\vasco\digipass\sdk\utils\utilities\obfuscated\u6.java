package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u6.smali */
public class u6 extends AsymmetricKeyParameter {
    private static final f1.a d = new f1.a();
    private static final BigInteger e = new BigInteger("8138e8a0fcf3a4e84a771d40fd305d7f4aa59306d7251de54d98af8fe95729a1f73d893fa424cd2edc8636a6c3285e022b0e3866a565ae8108eed8591cd4fe8d2ce86165a978d719ebf647f362d33fca29cd179fb42401cbaf3df0c614056f9c8f3cfd51e474afb6bc6974f78db8aba8e9e517fded658591ab7502bd41849462f", 16);
    private static final BigInteger f = BigInteger.valueOf(1);
    private BigInteger b;
    private BigInteger c;

    public u6(boolean z, BigInteger bigInteger, BigInteger bigInteger2) {
        this(z, bigInteger, bigInteger2, false);
    }

    private static int a(int i) {
        if (i >= 1536) {
            return 3;
        }
        if (i >= 1024) {
            return 4;
        }
        return i >= 512 ? 7 : 50;
    }

    private BigInteger a(BigInteger bigInteger, boolean z) {
        if (z) {
            d.a(bigInteger);
            return bigInteger;
        }
        if ((bigInteger.intValue() & 1) == 0) {
            throw new IllegalArgumentException("RSA modulus is even");
        }
        if (r6.b("bc.org.bouncycastle.rsa.allow_unsafe_mod")) {
            return bigInteger;
        }
        if (r6.a("bc.org.bouncycastle.rsa.max_size", 15360) < bigInteger.bitLength()) {
            throw new IllegalArgumentException("modulus value out of range");
        }
        if (!bigInteger.gcd(e).equals(f)) {
            throw new IllegalArgumentException("RSA modulus has a small prime factor");
        }
        int a = r6.a("bc.org.bouncycastle.rsa.max_mr_tests", a(bigInteger.bitLength() / 2));
        if (a > 0 && !p6.a(bigInteger, t1.b(), a).c()) {
            throw new IllegalArgumentException("RSA modulus is not composite");
        }
        d.a(bigInteger);
        return bigInteger;
    }

    public BigInteger b() {
        return this.b;
    }

    public u6(boolean z, BigInteger bigInteger, BigInteger bigInteger2, boolean z2) {
        super(z);
        if (!z && (bigInteger2.intValue() & 1) == 0) {
            throw new IllegalArgumentException("RSA publicExponent is even");
        }
        this.b = d.b(bigInteger) ? bigInteger : a(bigInteger, z2);
        this.c = bigInteger2;
    }

    public BigInteger a() {
        return this.c;
    }
}
