package o.p002do;

import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\do\d.smali */
public final class d {
    private static int c = 0;
    private static int e = 1;

    public final int hashCode() {
        int hashCode;
        int i = c;
        int i2 = ((i | 7) << 1) - (i ^ 7);
        e = i2 % 128;
        switch (i2 % 2 == 0 ? (char) 20 : Typography.dollar) {
            case 20:
                hashCode = super.hashCode();
                int i3 = 60 / 0;
                break;
            default:
                hashCode = super.hashCode();
                break;
        }
        int i4 = (e + 28) - 1;
        c = i4 % 128;
        switch (i4 % 2 != 0 ? '?' : '9') {
            case '?':
                throw null;
            default:
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = (c + 78) - 1;
        e = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = c + Opcodes.LUSHR;
        e = i3 % 128;
        int i4 = i3 % 2;
        return equals;
    }

    public final String toString() {
        int i = c + 51;
        e = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = c;
        int i4 = ((i3 | 69) << 1) - (i3 ^ 69);
        e = i4 % 128;
        int i5 = i4 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i = e;
        int i2 = ((i | 27) << 1) - (i ^ 27);
        c = i2 % 128;
        int i3 = i2 % 2;
        super.finalize();
        int i4 = (e + 10) - 1;
        c = i4 % 128;
        int i5 = i4 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
