package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP192R1Field.smali */
public class SecP192R1Field {
    static final int[] a = {-1, -1, -2, -1, -1, -1};
    private static final int[] b = {1, 0, 2, 0, 1, 0, -2, -1, -3, -1, -1, -1};
    private static final int[] c = {-1, -1, -3, -1, -2, -1, 1, 0, 2};

    private static void a(int[] iArr) {
        long j = (iArr[0] & 4294967295L) + 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            j2 = j3 >> 32;
        }
        long j4 = j2 + (4294967295L & iArr[2]) + 1;
        iArr[2] = (int) j4;
        if ((j4 >> 32) != 0) {
            c6.c(6, iArr, 3);
        }
    }

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.a(iArr, iArr2, iArr3) != 0 || (iArr3[5] == -1 && u5.b(iArr3, a))) {
            a(iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.a(12, iArr, iArr2, iArr3) != 0 || (iArr3[11] == -1 && c6.d(12, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(12, iArr3, iArr4.length);
            }
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(6, iArr, iArr2) != 0 || (iArr2[5] == -1 && u5.b(iArr2, a))) {
            a(iArr2);
        }
    }

    private static void b(int[] iArr) {
        long j = (iArr[0] & 4294967295L) - 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            j2 = j3 >> 32;
        }
        long j4 = j2 + ((4294967295L & iArr[2]) - 1);
        iArr[2] = (int) j4;
        if ((j4 >> 32) != 0) {
            c6.a(6, iArr, 3);
        }
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = u5.a(bigInteger);
        if (a2[5] == -1) {
            int[] iArr = a;
            if (u5.b(a2, iArr)) {
                u5.d(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(6, iArr, 0, iArr2);
        } else {
            c6.d(6, iArr2, u5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 6; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] c2 = u5.c();
        u5.c(iArr, iArr2, c2);
        reduce(c2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.d(iArr, iArr2, iArr3) != 0 || (iArr3[11] == -1 && c6.d(12, iArr3, b))) {
            int[] iArr4 = c;
            if (c6.a(iArr4.length, iArr4, iArr3) != 0) {
                c6.c(12, iArr3, iArr4.length);
            }
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            u5.e(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            u5.e(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[24];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 6);
        } while (c6.f(6, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        long j = iArr[6] & 4294967295L;
        long j2 = iArr[7] & 4294967295L;
        long j3 = (iArr[10] & 4294967295L) + j;
        long j4 = (iArr[11] & 4294967295L) + j2;
        long j5 = (iArr[0] & 4294967295L) + j3 + 0;
        int i = (int) j5;
        long j6 = (j5 >> 32) + (iArr[1] & 4294967295L) + j4;
        int i2 = (int) j6;
        iArr2[1] = i2;
        long j7 = j3 + (iArr[8] & 4294967295L);
        long j8 = j4 + (iArr[9] & 4294967295L);
        long j9 = (j6 >> 32) + (iArr[2] & 4294967295L) + j7;
        long j10 = j9 & 4294967295L;
        long j11 = (j9 >> 32) + (iArr[3] & 4294967295L) + j8;
        iArr2[3] = (int) j11;
        long j12 = (j11 >> 32) + (iArr[4] & 4294967295L) + (j7 - j);
        iArr2[4] = (int) j12;
        long j13 = (j12 >> 32) + (iArr[5] & 4294967295L) + (j8 - j2);
        iArr2[5] = (int) j13;
        long j14 = j13 >> 32;
        long j15 = j10 + j14;
        long j16 = j14 + (i & 4294967295L);
        iArr2[0] = (int) j16;
        long j17 = j16 >> 32;
        if (j17 != 0) {
            long j18 = j17 + (4294967295L & i2);
            iArr2[1] = (int) j18;
            j15 += j18 >> 32;
        }
        iArr2[2] = (int) j15;
        if (((j15 >> 32) == 0 || c6.c(6, iArr2, 3) == 0) && !(iArr2[5] == -1 && u5.b(iArr2, a))) {
            return;
        }
        a(iArr2);
    }

    public static void reduce32(int i, int[] iArr) {
        long j;
        if (i != 0) {
            long j2 = i & 4294967295L;
            long j3 = (iArr[0] & 4294967295L) + j2 + 0;
            iArr[0] = (int) j3;
            long j4 = j3 >> 32;
            if (j4 != 0) {
                long j5 = j4 + (iArr[1] & 4294967295L);
                iArr[1] = (int) j5;
                j4 = j5 >> 32;
            }
            long j6 = j4 + (4294967295L & iArr[2]) + j2;
            iArr[2] = (int) j6;
            j = j6 >> 32;
        } else {
            j = 0;
        }
        if ((j == 0 || c6.c(6, iArr, 3) == 0) && !(iArr[5] == -1 && u5.b(iArr, a))) {
            return;
        }
        a(iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] c2 = u5.c();
        u5.c(iArr, c2);
        reduce(c2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] c2 = u5.c();
        u5.c(iArr, c2);
        reduce(c2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            u5.c(iArr2, c2);
            reduce(c2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (u5.e(iArr, iArr2, iArr3) != 0) {
            b(iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(12, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            if (c6.g(iArr4.length, iArr4, iArr3) != 0) {
                c6.a(12, iArr3, iArr4.length);
            }
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(6, iArr, 0, iArr2) != 0 || (iArr2[5] == -1 && u5.b(iArr2, a))) {
            a(iArr2);
        }
    }
}
