package o.ba;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ba\b.smali */
public final class b extends e {
    private static int c = 0;
    private static int e = 1;
    private String a;
    private String b;
    private String d;

    public final String e() {
        int i = e;
        int i2 = (i & 79) + (i | 79);
        c = i2 % 128;
        switch (i2 % 2 != 0 ? '\n' : '*') {
            case '\n':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final String b() {
        String str;
        int i = e;
        int i2 = i + 79;
        c = i2 % 128;
        switch (i2 % 2 != 0 ? 'H' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                str = this.d;
                break;
            default:
                str = this.d;
                int i3 = 63 / 0;
                break;
        }
        int i4 = (i ^ 27) + ((i & 27) << 1);
        c = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String a() {
        int i = c + Opcodes.DREM;
        e = i % 128;
        switch (i % 2 == 0 ? '-' : '[') {
            case '-':
                int i2 = 12 / 0;
                return this.a;
            default:
                return this.a;
        }
    }

    public final void a(String str) {
        int i = c;
        int i2 = ((i | 95) << 1) - (i ^ 95);
        e = i2 % 128;
        int i3 = i2 % 2;
        this.b = str;
        int i4 = i + 7;
        e = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    public final void c(String str) {
        int i = e;
        int i2 = ((i | 25) << 1) - (i ^ 25);
        c = i2 % 128;
        char c2 = i2 % 2 != 0 ? 'G' : 'c';
        this.d = str;
        switch (c2) {
            case Opcodes.DADD /* 99 */:
                break;
            default:
                int i3 = 90 / 0;
                break;
        }
        int i4 = (i + 26) - 1;
        c = i4 % 128;
        switch (i4 % 2 != 0 ? 'M' : '-') {
            case '-':
                return;
            default:
                throw null;
        }
    }

    public final void d(String str) {
        int i = e;
        int i2 = (i & 75) + (i | 75);
        c = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.a = str;
        switch (z) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }
}
