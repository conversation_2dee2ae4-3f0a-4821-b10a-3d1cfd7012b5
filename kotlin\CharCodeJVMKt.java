package kotlin;

/* compiled from: CharCodeJVM.kt */
@Metadata(d1 = {"\u0000\u0010\n\u0000\n\u0002\u0010\f\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001b\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0087\bø\u0001\u0000¢\u0006\u0004\b\u0004\u0010\u0005\u0082\u0002\u0004\n\u0002\b\u0019¨\u0006\u0006"}, d2 = {"Char", "", "code", "Lkotlin/UShort;", "Char-xj2QHRw", "(S)C", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\CharCodeJVMKt.smali */
public final class CharCodeJVMKt {
    /* renamed from: Char-xj2QHRw, reason: not valid java name */
    private static final char m233Charxj2QHRw(short code) {
        return (char) (65535 & code);
    }
}
