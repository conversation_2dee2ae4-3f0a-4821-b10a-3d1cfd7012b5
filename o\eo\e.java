package o.eo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.AntelopCallback;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.card.emvapplication.EmvApplicationGroup;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] s;
    private static int u;
    private static char v;
    private static int y;
    private final String a;
    private final String b;
    private o.ei.a c;
    private final String d;
    private final String e;
    private final boolean f;
    private final String g;
    private final String h;
    private final byte[] i;
    private final String j;
    private final o.ca.a k = new o.ca.a(o.ei.c.c());
    private final o.du.h l;
    private final o.du.c m;
    private h n;

    /* renamed from: o, reason: collision with root package name */
    private final String f71o;
    private LinkedHashMap<String, d> p;
    private c q;
    private o.er.j r;
    private final boolean t;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        y = 0;
        u = 1;
        E();
        PointF.length(0.0f, 0.0f);
        ViewConfiguration.getScrollDefaultDelay();
        KeyEvent.getMaxKeyCode();
        KeyEvent.normalizeMetaState(0);
        int i = u + 9;
        y = i % 128;
        switch (i % 2 != 0 ? (char) 22 : (char) 16) {
            case 16:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void E() {
        s = new char[]{30498, 30555, 30534, 30589, 30509, 30583, 30569, 30561, 30587, 30517, 30572, 30499, 30586, 30539, 30585, 30563, 30566, 30505, 30540, 30511, 30506, 30588, 30574, 30529, 30568, 30504, 30508, 30571, 30591, 30510, 30536, 30552, 30590, 30560, 30570, 30562};
        v = (char) 17043;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void J(int r7, byte r8, short r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.eo.e.$$a
            int r9 = r9 + 69
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r9
            r4 = r2
            r9 = r8
            r8 = r7
            goto L2c
        L14:
            r3 = r2
        L15:
            int r7 = r7 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r9
            r1[r3] = r5
            if (r4 != r8) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            r3 = r0[r7]
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L2c:
            int r7 = r7 + r3
            r3 = r4
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r6
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.J(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{13, -73, -57, -113};
        $$b = Opcodes.IFGT;
    }

    public final /* synthetic */ Object clone() throws CloneNotSupportedException {
        int i = y + 39;
        u = i % 128;
        switch (i % 2 == 0 ? (char) 30 : '_') {
            case Opcodes.SWAP /* 95 */:
                return d();
            default:
                d();
                throw null;
        }
    }

    public e(String str, o.ei.a aVar, String str2, String str3, String str4, String str5, String str6, String str7, Boolean bool, byte[] bArr, String str8, o.du.c cVar, o.du.h hVar, h hVar2, boolean z) {
        this.d = str;
        this.c = aVar;
        this.a = str2;
        this.b = str3;
        this.e = str4;
        this.j = str5;
        this.h = str6;
        this.g = str7;
        this.f = bool.booleanValue();
        this.i = bArr;
        this.f71o = str8;
        this.m = cVar;
        this.l = hVar;
        this.n = hVar2;
        this.t = z;
    }

    public final e c() {
        int i = y + 21;
        u = i % 128;
        int i2 = i % 2;
        e b = b(o.el.b.a);
        int i3 = y + 19;
        u = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 22 : 'L') {
            case Base64.mimeLineLength /* 76 */:
                return b;
            default:
                int i4 = 30 / 0;
                return b;
        }
    }

    public final e b() {
        int i = y + 99;
        u = i % 128;
        int i2 = i % 2;
        e b = b(o.el.b.b);
        int i3 = y + 83;
        u = i3 % 128;
        int i4 = i3 % 2;
        return b;
    }

    public final e d() {
        e eVar = new e(this.d, this.c, this.a, this.b, this.e, this.j, this.h, this.g, Boolean.valueOf(this.f), this.i, this.f71o, this.m, this.l, this.n, this.t);
        eVar.p = this.p;
        eVar.q = this.q;
        eVar.r = this.r;
        int i = y + 41;
        u = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return eVar;
        }
    }

    public final Boolean a() {
        int i = y + 27;
        u = i % 128;
        int i2 = i % 2;
        Boolean valueOf = Boolean.valueOf(this.f);
        int i3 = u + 21;
        y = i3 % 128;
        int i4 = i3 % 2;
        return valueOf;
    }

    private e b(o.el.b bVar) {
        e eVar;
        e eVar2 = r15;
        e eVar3 = new e(this.d, this.c, this.a, this.b, this.e, this.j, this.h, this.g, Boolean.valueOf(this.f), this.i, this.f71o, this.m, this.l, this.n, this.t);
        if (this.p != null) {
            LinkedHashMap<String, d> linkedHashMap = new LinkedHashMap<>();
            Iterator<Map.Entry<String, d>> it = this.p.entrySet().iterator();
            while (true) {
                switch (it.hasNext() ? 'R' : 'Y') {
                    case Opcodes.DUP /* 89 */:
                        eVar = eVar2;
                        eVar.p = linkedHashMap;
                        int i = u + 3;
                        y = i % 128;
                        int i2 = i % 2;
                        break;
                    default:
                        e eVar4 = eVar2;
                        int i3 = y + 73;
                        u = i3 % 128;
                        if (i3 % 2 == 0) {
                        }
                        Map.Entry<String, d> next = it.next();
                        linkedHashMap.put(next.getKey(), next.getValue().a(bVar));
                        eVar2 = eVar4;
                }
            }
        } else {
            eVar = eVar2;
        }
        eVar.q = this.q;
        eVar.r = this.r;
        return eVar;
    }

    public static e c(String str, o.ei.a aVar, String str2) {
        e eVar = new e(str, aVar, null, null, null, null, null, str2, Boolean.FALSE, null, null, null, null, null, false);
        eVar.d(new LinkedHashMap<>());
        eVar.q = new c(null, null, null, null, str2);
        int i = u + 51;
        y = i % 128;
        int i2 = i % 2;
        return eVar;
    }

    public final String e() {
        int i = u + 37;
        y = i % 128;
        switch (i % 2 != 0 ? 'F' : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                return this.d;
            default:
                int i2 = 50 / 0;
                return this.d;
        }
    }

    public final o.ei.a h() {
        int i = y + Opcodes.LMUL;
        int i2 = i % 128;
        u = i2;
        int i3 = i % 2;
        o.ei.a aVar = this.c;
        int i4 = i2 + 29;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? ']' : (char) 2) {
            case 2:
                return aVar;
            default:
                int i5 = 1 / 0;
                return aVar;
        }
    }

    public final void e(o.ei.a aVar) {
        int i = u;
        int i2 = i + 1;
        y = i2 % 128;
        int i3 = i2 % 2;
        this.c = aVar;
        int i4 = i + Opcodes.LREM;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? ' ' : '2') {
            case '2':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String i() {
        int i = u + 59;
        y = i % 128;
        switch (i % 2 == 0) {
            case false:
                int i2 = 60 / 0;
                return this.a;
            default:
                return this.a;
        }
    }

    public final String j() {
        int i = u + 5;
        y = i % 128;
        switch (i % 2 != 0 ? 'Q' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return this.b;
            default:
                int i2 = 80 / 0;
                return this.b;
        }
    }

    public final String g() {
        String str;
        int i = y + Opcodes.DMUL;
        int i2 = i % 128;
        u = i2;
        switch (i % 2 != 0) {
            case false:
                str = this.e;
                int i3 = 77 / 0;
                break;
            default:
                str = this.e;
                break;
        }
        int i4 = i2 + 57;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? '0' : (char) 24) {
            case 24:
                return str;
            default:
                throw null;
        }
    }

    public final String f() {
        int i = u + 93;
        y = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return this.j;
        }
    }

    public final String m() {
        int i = u + 29;
        int i2 = i % 128;
        y = i2;
        switch (i % 2 != 0) {
            case false:
                String str = this.h;
                int i3 = i2 + 47;
                u = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return str;
                    default:
                        int i4 = 27 / 0;
                        return str;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String n() {
        int i = y + 55;
        int i2 = i % 128;
        u = i2;
        int i3 = i % 2;
        String str = this.g;
        int i4 = i2 + 57;
        y = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final Short k() {
        int i = y + 51;
        u = i % 128;
        int i2 = i % 2;
        byte[] bArr = this.i;
        switch (bArr != null) {
            case false:
                return null;
            default:
                Short valueOf = Short.valueOf(o.ej.b.c(bArr));
                int i3 = u + 85;
                y = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return valueOf;
                }
        }
    }

    public final Drawable b(Context context) {
        o.du.c cVar = this.m;
        switch (cVar == null) {
            case false:
                Drawable b = cVar.b(context);
                int i = u + Opcodes.LUSHR;
                y = i % 128;
                if (i % 2 == 0) {
                    return b;
                }
                int i2 = 8 / 0;
                return b;
            default:
                int i3 = y + 23;
                u = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return null;
                }
        }
    }

    public final o.du.c l() {
        int i = y + 61;
        int i2 = i % 128;
        u = i2;
        int i3 = i % 2;
        o.du.c cVar = this.m;
        int i4 = i2 + 63;
        y = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                int i5 = 25 / 0;
                return cVar;
            default:
                return cVar;
        }
    }

    public final o.du.h o() {
        int i = u;
        int i2 = i + 63;
        y = i2 % 128;
        int i3 = i2 % 2;
        o.du.h hVar = this.l;
        int i4 = i + 79;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? '4' : '9') {
            case '4':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return hVar;
        }
    }

    public final h t() {
        int i = u + 19;
        int i2 = i % 128;
        y = i2;
        switch (i % 2 != 0 ? 'H' : (char) 17) {
            case 17:
                h hVar = this.n;
                int i3 = i2 + 21;
                u = i3 % 128;
                int i4 = i3 % 2;
                return hVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void a(h hVar) {
        int i = u;
        int i2 = i + 19;
        y = i2 % 128;
        boolean z = i2 % 2 != 0;
        this.n = hVar;
        switch (z) {
            case true:
                int i3 = 81 / 0;
                break;
        }
        int i4 = i + 23;
        y = i4 % 128;
        int i5 = i4 % 2;
    }

    public final c s() {
        int i = y + 85;
        u = i % 128;
        switch (i % 2 == 0 ? 'Q' : '0') {
            case '0':
                return this.q;
            default:
                int i2 = 34 / 0;
                return this.q;
        }
    }

    public final o.er.j q() {
        o.er.j jVar;
        int i = u;
        int i2 = i + Opcodes.LNEG;
        y = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 4 : (char) 11) {
            case 11:
                jVar = this.r;
                break;
            default:
                jVar = this.r;
                int i3 = 16 / 0;
                break;
        }
        int i4 = i + 39;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? '^' : (char) 22) {
            case Opcodes.DUP2_X2 /* 94 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return jVar;
        }
    }

    public final int p() {
        int i = u + Opcodes.DMUL;
        int i2 = i % 128;
        y = i2;
        int i3 = i % 2;
        LinkedHashMap<String, d> linkedHashMap = this.p;
        switch (linkedHashMap == null) {
            case false:
                int i4 = i2 + 43;
                u = i4 % 128;
                int i5 = i4 % 2;
                int size = linkedHashMap.size();
                int i6 = u + 41;
                y = i6 % 128;
                int i7 = i6 % 2;
                return size;
            default:
                return 0;
        }
    }

    public final d c(String str) {
        int i = y;
        int i2 = i + Opcodes.LMUL;
        u = i2 % 128;
        int i3 = i2 % 2;
        LinkedHashMap<String, d> linkedHashMap = this.p;
        switch (linkedHashMap == null) {
            case true:
                int i4 = i + 99;
                u = i4 % 128;
                int i5 = i4 % 2;
                return null;
            default:
                return linkedHashMap.get(str);
        }
    }

    public final String r() {
        int i = y + Opcodes.LNEG;
        u = i % 128;
        int i2 = i % 2;
        o.ei.c c = o.ei.c.c();
        switch (!c.q() ? (char) 18 : (char) 29) {
            case 18:
                int i3 = u + 83;
                y = i3 % 128;
                int i4 = i3 % 2;
                return null;
            default:
                String c2 = c.d().e().c(this.d);
                switch (c2 != null ? (char) 20 : 'P') {
                    case 20:
                        LinkedHashMap<String, d> linkedHashMap = this.p;
                        switch (linkedHashMap != null ? (char) 28 : 'V') {
                            case Opcodes.SASTORE /* 86 */:
                                break;
                            default:
                                int i5 = y + Opcodes.LREM;
                                u = i5 % 128;
                                int i6 = i5 % 2;
                                switch (!linkedHashMap.containsKey(c2)) {
                                    case false:
                                        return c2;
                                }
                        }
                }
                return u();
        }
    }

    public final void b(String str) throws WalletValidationException {
        int i = u + Opcodes.DNEG;
        y = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        F(9 - Color.argb(0, 0, 0, 0), "\u0001\b\n\u001f\u0000\u0015\u0015\u0004㘷", (byte) ((ViewConfiguration.getDoubleTapTimeout() >> 16) + 57), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        F((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 54, "\u0016!\u000b\u0014#\u0004\u0007\u0002\u0004\u0015\t\u0013\u001c\u0010\n\u000e\u001f\t!\u0000\u001e\u000f\u0019\u0016\u0001\u0012\u0013\u0017\t!\u0016\r\u0019\u0015\u0007\u0015\u0015\u0016\u0012\u0001\u0012\u001f\t\u0003\u0010\u0018\u0016\r\u0019\u0015\u0007\u0015\u0015\u0016", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 123), objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), this.d, str));
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            F(5 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), "!\u0000\u001e\u000f㙃", (byte) (View.MeasureSpec.getMode(0) + 89), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (this.p == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unknown;
            Object[] objArr4 = new Object[1];
            F(KeyEvent.keyCodeFromString("") + 5, "!\u0000\u001e\u000f㙃", (byte) (KeyEvent.normalizeMetaState(0) + 89), objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        G();
        d dVar = this.p.get(str);
        if (dVar == null) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Unknown;
            Object[] objArr5 = new Object[1];
            F(Color.alpha(0) + 5, "!\u0000\u001e\u000f㙃", (byte) (89 - View.combineMeasuredStates(0, 0)), objArr5);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr5[0]).intern());
        }
        if (!dVar.e()) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            F(5 - TextUtils.getOffsetBefore("", 0), "!\u0000\u001e\u000f㙃", (byte) (88 - ExpandableListView.getPackedPositionChild(0L)), objArr6);
            throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr6[0]).intern());
        }
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode5 = WalletValidationErrorCode.WrongState;
            Object[] objArr7 = new Object[1];
            F(View.MeasureSpec.makeMeasureSpec(0, 0) + 6, "\"\u0013㙄㙄 \n", (byte) (77 - ImageFormat.getBitsPerPixel(0)), objArr7);
            String intern2 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            F(42 - (ViewConfiguration.getKeyRepeatDelay() >> 16), "\"\u0013㘴㘴 \n\u0016\r\u0016\u0014\t\u001f\u0007\u0014\u0000\u000f㘲㘲\r\n\u001d\u0006\u0016\u0007\u001f\t\n\u001f\u000b\t\u000f\"\r\u0019\u0004!\u001e\u000e\u000f\u0004!\u001c", (byte) (61 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), objArr8);
            throw new WalletValidationException(walletValidationErrorCode5, intern2, ((String) objArr8[0]).intern());
        }
        c.d().e().b(this.d, str);
        int i3 = u + Opcodes.LSHL;
        y = i3 % 128;
        switch (i3 % 2 == 0 ? 'K' : '6') {
            case Opcodes.ISTORE /* 54 */:
                int i4 = 65 / 0;
                return;
            default:
                return;
        }
    }

    public final void x() throws WalletValidationException {
        int i = y + 79;
        u = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        F(9 - TextUtils.getOffsetAfter("", 0), "\u0001\b\n\u001f\u0000\u0015\u0015\u0004㘷", (byte) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 56), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        F(AndroidCharacter.getMirror('0') - 23, "\u0004!\u0016!\u000b\u0014#\u0004\u0007\u0002\u0004\u0015\t\u0013\u001c\u0010\n\u000e\u001f\t!\u0000\u001e\u000f㘟", (byte) (AndroidCharacter.getMirror('0') + 5), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        G();
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            F(TextUtils.indexOf("", "") + 6, "\"\u0013㙄㙄 \n", (byte) ((ViewConfiguration.getLongPressTimeout() >> 16) + 78), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            F((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 41, "\"\u0013㘴㘴 \n\u0016\r\u0016\u0014\t\u001f\u0007\u0014\u0000\u000f㘲㘲\r\n\u001d\u0006\u0016\u0007\u001f\t\n\u001f\u000b\t\u000f\"\r\u0019\u0004!\u001e\u000e\u000f\u0004!\u001c", (byte) (62 - Color.red(0)), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        c.d().e().e(this.d);
        int i3 = u + Opcodes.DMUL;
        y = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void e(String str) throws WalletValidationException {
        int i = y + 59;
        u = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        F(9 - KeyEvent.getDeadChar(0, 0), "\u0001\b\n\u001f\u0000\u0015\u0015\u0004㘷", (byte) (58 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        F(Color.red(0) + 46, "\u0016!\u0007\u000e\u001e\n\u0012\u0010\u000e\t!\u0000\u001e\u000f\u0019\u0016\u0001\u0012\u0013\u0017\t!\u0016\r\u0019\u0015\u0007\u0015\u0015\u0016\u0012\u0001\u0012\u001f\t\u0003\u0010\u0018\u0016\r\u0019\u0015\u0007\u0015\u0015\u0016", (byte) (106 - Color.alpha(0)), objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), this.d, str));
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            F(6 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "!\u0000\u001e\u000f㙃", (byte) (89 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        if (this.p == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unknown;
            Object[] objArr4 = new Object[1];
            F(((Process.getThreadPriority(0) + 20) >> 6) + 5, "!\u0000\u001e\u000f㙃", (byte) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 88), objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        G();
        d dVar = this.p.get(str);
        if (dVar == null) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Unknown;
            Object[] objArr5 = new Object[1];
            F(6 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "!\u0000\u001e\u000f㙃", (byte) (TextUtils.getTrimmedLength("") + 89), objArr5);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr5[0]).intern());
        }
        if (!dVar.e()) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            F(KeyEvent.keyCodeFromString("") + 5, "!\u0000\u001e\u000f㙃", (byte) (Gravity.getAbsoluteGravity(0, 0) + 89), objArr6);
            throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr6[0]).intern());
        }
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode5 = WalletValidationErrorCode.WrongState;
            Object[] objArr7 = new Object[1];
            F(6 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), "\"\u0013㙄㙄 \n", (byte) (TextUtils.getCapsMode("", 0, 0) + 78), objArr7);
            String intern2 = ((String) objArr7[0]).intern();
            Object[] objArr8 = new Object[1];
            F(42 - TextUtils.getCapsMode("", 0, 0), "\"\u0013㘴㘴 \n\u0016\r\u0016\u0014\t\u001f\u0007\u0014\u0000\u000f㘲㘲\r\n\u001d\u0006\u0016\u0007\u001f\t\n\u001f\u000b\t\u000f\"\r\u0019\u0004!\u001e\u000e\u000f\u0004!\u001c", (byte) (View.MeasureSpec.makeMeasureSpec(0, 0) + 62), objArr8);
            throw new WalletValidationException(walletValidationErrorCode5, intern2, ((String) objArr8[0]).intern());
        }
        c.d().e().e(this.d, str);
        int i3 = u + 87;
        y = i3 % 128;
        switch (i3 % 2 == 0 ? '^' : (char) 1) {
            case 1:
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.lang.String u() {
        /*
            r8 = this;
            int r0 = o.eo.e.u
            int r0 = r0 + 73
            int r1 = r0 % 128
            o.eo.e.y = r1
            int r0 = r0 % 2
            o.ei.c r0 = o.ei.c.c()
            boolean r1 = r0.q()
            r2 = 1
            r3 = 0
            if (r1 != 0) goto L19
            r1 = r2
            goto L1a
        L19:
            r1 = r3
        L1a:
            r4 = 0
            switch(r1) {
                case 0: goto L1f;
                default: goto L1e;
            }
        L1e:
            return r4
        L1f:
            o.fm.c r0 = r0.d()
            o.fm.b r0 = r0.e()
            java.lang.String r1 = r8.d
            java.lang.String r0 = r0.a(r1)
            r1 = 24
            if (r0 == 0) goto L5e
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r5 = r8.p
            if (r5 == 0) goto L37
            r6 = r2
            goto L38
        L37:
            r6 = r3
        L38:
            switch(r6) {
                case 1: goto L3c;
                default: goto L3b;
            }
        L3b:
            goto L5e
        L3c:
            boolean r5 = r5.containsKey(r0)
            if (r5 == 0) goto L44
            r5 = r1
            goto L45
        L44:
            r5 = r2
        L45:
            switch(r5) {
                case 1: goto L3b;
                default: goto L48;
            }
        L48:
            int r1 = o.eo.e.y
            int r1 = r1 + 85
            int r5 = r1 % 128
            o.eo.e.u = r5
            int r1 = r1 % 2
            if (r1 != 0) goto L56
            r2 = r3
            goto L57
        L56:
        L57:
            switch(r2) {
                case 0: goto L5b;
                default: goto L5a;
            }
        L5a:
            return r0
        L5b:
            throw r4     // Catch: java.lang.Throwable -> L5c
        L5c:
            r0 = move-exception
            throw r0
        L5e:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r8.p
            r5 = 4
            if (r0 == 0) goto L65
            r1 = r5
            goto L66
        L65:
        L66:
            switch(r1) {
                case 24: goto L74;
                default: goto L6a;
            }
        L6a:
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
            r1 = r4
            goto L75
        L74:
            return r4
        L75:
            boolean r6 = r0.hasNext()
            if (r6 == 0) goto L7d
            r6 = r3
            goto L7e
        L7d:
            r6 = r2
        L7e:
            switch(r6) {
                case 0: goto L84;
                default: goto L81;
            }
        L81:
            if (r1 == 0) goto Lba
            goto La1
        L84:
            java.lang.Object r6 = r0.next()
            o.eo.d r6 = (o.eo.d) r6
            if (r1 != 0) goto L8d
            r1 = r6
        L8d:
            boolean r7 = r6.e()
            if (r7 == 0) goto L96
            r7 = 15
            goto L98
        L96:
            r7 = 11
        L98:
            switch(r7) {
                case 15: goto L9c;
                default: goto L9b;
            }
        L9b:
            goto L75
        L9c:
            java.lang.String r0 = r6.h()
            return r0
        La1:
            int r0 = o.eo.e.y
            int r0 = r0 + 111
            int r2 = r0 % 128
            o.eo.e.u = r2
            int r0 = r0 % 2
            if (r0 != 0) goto Lb5
            java.lang.String r0 = r1.h()
            int r5 = r5 / r3
            goto Lb9
        Lb3:
            r0 = move-exception
            throw r0
        Lb5:
            java.lang.String r0 = r1.h()
        Lb9:
            return r0
        Lba:
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.u():java.lang.String");
    }

    public final Iterator<d> w() {
        int i = u + Opcodes.LMUL;
        y = i % 128;
        switch (i % 2 == 0) {
            case true:
                LinkedHashMap<String, d> linkedHashMap = this.p;
                switch (linkedHashMap == null) {
                    case false:
                        return linkedHashMap.values().iterator();
                    default:
                        Iterator<d> emptyIterator = Collections.emptyIterator();
                        int i2 = u + 57;
                        y = i2 % 128;
                        int i3 = i2 % 2;
                        return emptyIterator;
                }
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x002c, code lost:
    
        r2 = 'Q';
     */
    /* JADX WARN: Code restructure failed: missing block: B:11:0x0031, code lost:
    
        switch(r2) {
            case 11: goto L37;
            default: goto L19;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0034, code lost:
    
        r2 = o.eo.e.y + 41;
        o.eo.e.u = r2 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x003e, code lost:
    
        if ((r2 % 2) == 0) goto L39;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x004c, code lost:
    
        if (r0.next().e() == false) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0050, code lost:
    
        r2 = o.eo.e.y + 97;
        o.eo.e.u = r2 % 128;
        r2 = r2 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x004e, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x005c, code lost:
    
        r0.next().e();
        r0 = null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x0066, code lost:
    
        r0.hashCode();
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0069, code lost:
    
        throw null;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x0041, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x002f, code lost:
    
        r2 = 11;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x001b, code lost:
    
        return false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0019, code lost:
    
        if (r4.p == null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0012, code lost:
    
        if (r4.p == null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:7:0x001c, code lost:
    
        r0 = r4.p.values().iterator();
     */
    /* JADX WARN: Code restructure failed: missing block: B:9:0x002a, code lost:
    
        if (r0.hasNext() == false) goto L17;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean y() {
        /*
            r4 = this;
            int r0 = o.eo.e.u
            int r0 = r0 + 83
            int r1 = r0 % 128
            o.eo.e.y = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 == 0) goto L17
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r4.p
            r2 = 82
            int r2 = r2 / r1
            if (r0 != 0) goto L1c
            goto L1b
        L15:
            r0 = move-exception
            throw r0
        L17:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r4.p
            if (r0 != 0) goto L1c
        L1b:
            return r1
        L1c:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r4.p
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L26:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L2f
            r2 = 81
            goto L31
        L2f:
            r2 = 11
        L31:
            switch(r2) {
                case 11: goto L41;
                default: goto L34;
            }
        L34:
            int r2 = o.eo.e.y
            int r2 = r2 + 41
            int r3 = r2 % 128
            o.eo.e.u = r3
            int r2 = r2 % 2
            if (r2 == 0) goto L5c
            goto L42
        L41:
            return r1
        L42:
            java.lang.Object r2 = r0.next()
            o.eo.d r2 = (o.eo.d) r2
            boolean r2 = r2.e()
            if (r2 == 0) goto L50
            r0 = 1
            return r0
        L50:
            int r2 = o.eo.e.y
            int r2 = r2 + 97
            int r3 = r2 % 128
            o.eo.e.u = r3
            int r2 = r2 % 2
            goto L26
        L5c:
            java.lang.Object r0 = r0.next()
            o.eo.d r0 = (o.eo.d) r0
            r0.e()
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L6a
            throw r0     // Catch: java.lang.Throwable -> L6a
        L6a:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.y():boolean");
    }

    public final Map<String, EmvApplicationGroup> v() {
        switch (this.p == null) {
            case true:
                int i = y + 63;
                u = i % 128;
                int i2 = i % 2;
                Map<String, EmvApplicationGroup> emptyMap = Collections.emptyMap();
                int i3 = u + 37;
                y = i3 % 128;
                int i4 = i3 % 2;
                return emptyMap;
            default:
                HashMap hashMap = new HashMap();
                for (String str : this.p.keySet()) {
                    hashMap.put(str, new EmvApplicationGroup(this.p.get(str)));
                }
                return hashMap;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final fr.antelop.sdk.card.emvapplication.EmvApplicationGroup d(java.lang.String r5) {
        /*
            r4 = this;
            int r0 = o.eo.e.y
            int r0 = r0 + 33
            int r1 = r0 % 128
            o.eo.e.u = r1
            int r0 = r0 % 2
            r2 = 0
            if (r0 == 0) goto L47
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r4.p
            if (r0 != 0) goto L1b
            int r1 = r1 + 89
            int r5 = r1 % 128
            o.eo.e.y = r5
            int r1 = r1 % 2
            return r2
        L1b:
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L23:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L2b
            r1 = 0
            goto L2c
        L2b:
            r1 = 1
        L2c:
            switch(r1) {
                case 0: goto L30;
                default: goto L2f;
            }
        L2f:
            return r2
        L30:
            java.lang.Object r1 = r0.next()
            o.eo.d r1 = (o.eo.d) r1
            java.lang.String r3 = r1.h()
            boolean r3 = r3.equals(r5)
            if (r3 == 0) goto L46
            fr.antelop.sdk.card.emvapplication.EmvApplicationGroup r5 = new fr.antelop.sdk.card.emvapplication.EmvApplicationGroup
            r5.<init>(r1)
            return r5
        L46:
            goto L23
        L47:
            r2.hashCode()     // Catch: java.lang.Throwable -> L4b
            throw r2     // Catch: java.lang.Throwable -> L4b
        L4b:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.d(java.lang.String):fr.antelop.sdk.card.emvapplication.EmvApplicationGroup");
    }

    public final void e(Context context, final AntelopCallback antelopCallback) throws WalletValidationException {
        G();
        this.k.b(context, this.d, new o.ca.b() { // from class: o.eo.e.4
            private static int c = 0;
            private static int a = 1;

            /* JADX WARN: Failed to find 'out' block for switch in B:11:0x0040. Please report as an issue. */
            /* JADX WARN: Removed duplicated region for block: B:8:0x002a  */
            @Override // o.ca.b
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            public final void d() {
                /*
                    r4 = this;
                    int r0 = o.eo.e.AnonymousClass4.a
                    int r0 = r0 + 109
                    int r1 = r0 % 128
                    o.eo.e.AnonymousClass4.c = r1
                    int r0 = r0 % 2
                    r1 = 1
                    r2 = 0
                    if (r0 == 0) goto L10
                    r0 = r1
                    goto L11
                L10:
                    r0 = r2
                L11:
                    switch(r0) {
                        case 1: goto L19;
                        default: goto L14;
                    }
                L14:
                    fr.antelop.sdk.AntelopCallback r0 = r2
                    if (r0 == 0) goto L27
                    goto L26
                L19:
                    fr.antelop.sdk.AntelopCallback r0 = r2
                    r3 = 7
                    int r3 = r3 / r2
                    if (r0 == 0) goto L20
                    r1 = r2
                L20:
                    switch(r1) {
                        case 1: goto L45;
                        default: goto L23;
                    }
                L23:
                    goto L2a
                L24:
                    r0 = move-exception
                    throw r0
                L26:
                    r1 = r2
                L27:
                    switch(r1) {
                        case 1: goto L45;
                        default: goto L2a;
                    }
                L2a:
                    fr.antelop.sdk.AntelopCallback r0 = r2
                    r0.onSuccess()
                    int r0 = o.eo.e.AnonymousClass4.a
                    int r0 = r0 + 47
                    int r1 = r0 % 128
                    o.eo.e.AnonymousClass4.c = r1
                    int r0 = r0 % 2
                    if (r0 == 0) goto L3e
                    r0 = 16
                    goto L40
                L3e:
                    r0 = 75
                L40:
                    switch(r0) {
                        case 75: goto L44;
                        default: goto L43;
                    }
                L43:
                    goto L45
                L44:
                L45:
                    int r0 = o.eo.e.AnonymousClass4.a
                    int r0 = r0 + 45
                    int r1 = r0 % 128
                    o.eo.e.AnonymousClass4.c = r1
                    int r0 = r0 % 2
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eo.e.AnonymousClass4.d():void");
            }

            @Override // o.ca.b
            public final void d(o.bb.d dVar) {
                int i = c;
                int i2 = (i ^ 97) + ((i & 97) << 1);
                int i3 = i2 % 128;
                a = i3;
                int i4 = i2 % 2;
                AntelopCallback antelopCallback2 = antelopCallback;
                switch (antelopCallback2 != null ? (char) 6 : (char) 7) {
                    case 7:
                        break;
                    default:
                        int i5 = (i3 & Opcodes.DSUB) + (i3 | Opcodes.DSUB);
                        c = i5 % 128;
                        char c2 = i5 % 2 != 0 ? (char) 26 : (char) 23;
                        antelopCallback2.onError(o.bv.c.c(dVar).d());
                        switch (c2) {
                            case 26:
                                Object obj = null;
                                obj.hashCode();
                                throw null;
                            default:
                                int i6 = c;
                                int i7 = ((i6 | 21) << 1) - (i6 ^ 21);
                                a = i7 % 128;
                                if (i7 % 2 != 0) {
                                    break;
                                } else {
                                    break;
                                }
                        }
                }
                int i8 = a;
                int i9 = ((i8 | 79) << 1) - (i8 ^ 79);
                c = i9 % 128;
                switch (i9 % 2 != 0 ? ']' : (char) 7) {
                    case 7:
                        return;
                    default:
                        int i10 = 74 / 0;
                        return;
                }
            }
        });
        int i = u + 87;
        y = i % 128;
        switch (i % 2 != 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:17:0x0045. Please report as an issue. */
    public final boolean a(String str) {
        LinkedHashMap<String, d> linkedHashMap = this.p;
        if (linkedHashMap == null) {
            return false;
        }
        Iterator<d> it = linkedHashMap.values().iterator();
        while (it.hasNext()) {
            switch (!it.next().g(str)) {
                case true:
                    int i = u + 73;
                    y = i % 128;
                    int i2 = i % 2;
                default:
                    int i3 = y + 7;
                    u = i3 % 128;
                    switch (i3 % 2 == 0 ? '[' : ')') {
                    }
                    return true;
            }
        }
        int i4 = y + 25;
        u = i4 % 128;
        int i5 = i4 % 2;
        return false;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean i(java.lang.String r6) {
        /*
            r5 = this;
            int r0 = o.eo.e.y
            int r1 = r0 + 31
            int r2 = r1 % 128
            o.eo.e.u = r2
            int r1 = r1 % 2
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r1 = r5.p
            r2 = 1
            r3 = 0
            if (r1 != 0) goto L13
            r4 = r3
            goto L14
        L13:
            r4 = r2
        L14:
            switch(r4) {
                case 1: goto L20;
                default: goto L17;
            }
        L17:
            int r0 = r0 + 121
            int r6 = r0 % 128
            o.eo.e.u = r6
            int r0 = r0 % 2
            goto L6d
        L20:
            java.util.Collection r0 = r1.values()
            java.util.Iterator r1 = r0.iterator()
        L28:
            boolean r0 = r1.hasNext()
            if (r0 == 0) goto L31
            r0 = 96
            goto L33
        L31:
            r0 = 88
        L33:
            r4 = 3
            switch(r0) {
                case 88: goto L48;
                default: goto L37;
            }
        L37:
            java.lang.Object r0 = r1.next()
            o.eo.d r0 = (o.eo.d) r0
            java.lang.String r0 = r0.h()
            boolean r0 = r0.equals(r6)
            if (r0 == 0) goto L6c
            goto L62
        L48:
            int r6 = o.eo.e.y
            int r6 = r6 + 97
            int r0 = r6 % 128
            o.eo.e.u = r0
            int r6 = r6 % 2
            if (r6 != 0) goto L57
            r4 = 77
        L57:
            switch(r4) {
                case 77: goto L5b;
                default: goto L5a;
            }
        L5a:
            return r3
        L5b:
            r6 = 0
            r6.hashCode()     // Catch: java.lang.Throwable -> L60
            throw r6     // Catch: java.lang.Throwable -> L60
        L60:
            r6 = move-exception
            throw r6
        L62:
            int r6 = o.eo.e.u
            int r6 = r6 + r4
            int r0 = r6 % 128
            o.eo.e.y = r0
            int r6 = r6 % 2
            return r2
        L6c:
            goto L28
        L6d:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.i(java.lang.String):boolean");
    }

    private void G() throws WalletValidationException {
        int i = u + Opcodes.LMUL;
        y = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                B();
                throw null;
            default:
                if (!B()) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    F(View.resolveSize(0, 0) + 4, "\u0013\u0017\t!", (byte) (65 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                int i2 = y + 85;
                u = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:10:0x0033, code lost:
    
        if (java.util.Objects.equals(r7.n, o.eo.h.a) == false) goto L16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:14:0x0036, code lost:
    
        r7.k.a(r8, r7, r9, new o.eo.e.AnonymousClass5(r7));
        r8 = o.eo.e.y + 75;
        o.eo.e.u = r8 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x004a, code lost:
    
        if ((r8 % 2) != 0) goto L19;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x004c, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x004d, code lost:
    
        switch(r1) {
            case 0: goto L21;
            default: goto L20;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0050, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0053, code lost:
    
        r8 = 52 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0054, code lost:
    
        return;
     */
    /* JADX WARN: Code restructure failed: missing block: B:30:0x002d, code lost:
    
        if (java.util.Objects.equals(r7.n, o.eo.h.a) == false) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void a(android.content.Context r8, boolean r9, final fr.antelop.sdk.AntelopCallback r10) throws fr.antelop.sdk.exception.WalletValidationException {
        /*
            r7 = this;
            int r0 = o.eo.e.u
            int r0 = r0 + 41
            int r1 = r0 % 128
            o.eo.e.y = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 43
            goto L11
        Lf:
            r0 = 21
        L11:
            r1 = 1
            r2 = 0
            switch(r0) {
                case 21: goto L22;
                default: goto L16;
            }
        L16:
            r7.G()
            o.eo.h r0 = r7.n
            o.eo.h r3 = o.eo.h.a
            boolean r0 = java.util.Objects.equals(r0, r3)
            goto L30
        L22:
            r7.G()
            o.eo.h r0 = r7.n
            o.eo.h r3 = o.eo.h.a
            boolean r0 = java.util.Objects.equals(r0, r3)
            if (r0 != 0) goto L57
        L2f:
            goto L36
        L30:
            r3 = 88
            int r3 = r3 / r2
            if (r0 != 0) goto L57
            goto L2f
        L36:
            o.ca.a r0 = r7.k
            o.eo.e$5 r3 = new o.eo.e$5
            r3.<init>()
            r0.a(r8, r7, r9, r3)
            int r8 = o.eo.e.y
            int r8 = r8 + 75
            int r9 = r8 % 128
            o.eo.e.u = r9
            int r8 = r8 % 2
            if (r8 != 0) goto L4d
            r1 = r2
        L4d:
            switch(r1) {
                case 0: goto L51;
                default: goto L50;
            }
        L50:
            return
        L51:
            r8 = 52
            int r8 = r8 / r2
            return
        L55:
            r8 = move-exception
            throw r8
        L57:
            fr.antelop.sdk.exception.WalletValidationException r8 = new fr.antelop.sdk.exception.WalletValidationException
            fr.antelop.sdk.exception.WalletValidationErrorCode r9 = fr.antelop.sdk.exception.WalletValidationErrorCode.WrongState
            int r10 = android.view.KeyEvent.normalizeMetaState(r2)
            int r10 = r10 + 4
            long r3 = android.os.SystemClock.uptimeMillis()
            r5 = 0
            int r0 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            int r0 = r0 + 65
            byte r0 = (byte) r0
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r3 = "\u0013\u0017\t!"
            F(r10, r3, r0, r1)
            r10 = r1[r2]
            java.lang.String r10 = (java.lang.String) r10
            java.lang.String r10 = r10.intern()
            r8.<init>(r9, r10)
            throw r8
        L7f:
            r8 = move-exception
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.a(android.content.Context, boolean, fr.antelop.sdk.AntelopCallback):void");
    }

    public final LinkedHashMap<String, d> A() {
        int i = u + 11;
        y = i % 128;
        switch (i % 2 != 0 ? '[' : 'N') {
            case 'N':
                return this.p;
            default:
                throw null;
        }
    }

    public final void d(LinkedHashMap<String, d> linkedHashMap) {
        int i = u + Opcodes.LREM;
        int i2 = i % 128;
        y = i2;
        char c = i % 2 != 0 ? 'V' : '#';
        Object obj = null;
        this.p = linkedHashMap;
        switch (c) {
            case '#':
                int i3 = i2 + 61;
                u = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    public final void d(c cVar) {
        int i = u;
        int i2 = i + 39;
        y = i2 % 128;
        int i3 = i2 % 2;
        this.q = cVar;
        int i4 = i + Opcodes.LREM;
        y = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void e(o.er.j jVar) {
        int i = y + 17;
        u = i % 128;
        char c = i % 2 == 0 ? 'V' : '_';
        this.r = jVar;
        switch (c) {
            case Opcodes.SASTORE /* 86 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final boolean B() {
        int i = y + 45;
        int i2 = i % 128;
        u = i2;
        int i3 = i % 2;
        boolean z = this.t;
        int i4 = i2 + Opcodes.DNEG;
        y = i4 % 128;
        switch (i4 % 2 != 0 ? '0' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return z;
            default:
                throw null;
        }
    }

    /* renamed from: o.eo.e$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\e$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int d;
        static final /* synthetic */ int[] e;

        static {
            a = 0;
            d = 1;
            int[] iArr = new int[o.el.b.values().length];
            e = iArr;
            try {
                iArr[o.el.b.d.ordinal()] = 1;
                int i = (a + 84) - 1;
                d = i % 128;
                if (i % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[o.el.b.e.ordinal()] = 2;
                int i2 = a;
                int i3 = (i2 ^ 83) + ((i2 & 83) << 1);
                d = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[o.el.b.c.ordinal()] = 3;
                int i5 = d + 75;
                a = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[o.el.b.g.ordinal()] = 4;
                int i7 = d + 13;
                a = i7 % 128;
                if (i7 % 2 != 0) {
                }
            } catch (NoSuchFieldError e5) {
            }
            try {
                e[o.el.b.a.ordinal()] = 5;
            } catch (NoSuchFieldError e6) {
            }
            try {
                e[o.el.b.b.ordinal()] = 6;
                int i8 = d;
                int i9 = ((i8 | Opcodes.LSHL) << 1) - (i8 ^ Opcodes.LSHL);
                a = i9 % 128;
                switch (i9 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e7) {
            }
        }
    }

    public final CardStatus z() {
        switch (this.p != null) {
            case false:
                return null;
            default:
                h hVar = this.n;
                if (hVar != null) {
                    int i = y + 29;
                    u = i % 128;
                    int i2 = i % 2;
                    switch (hVar != h.c) {
                        case false:
                            int i3 = y + 13;
                            u = i3 % 128;
                            int i4 = i3 % 2;
                            return CardStatus.TermsAndConditionsValidationRequired;
                    }
                }
                Iterator<d> it = this.p.values().iterator();
                o.el.b bVar = null;
                while (it.hasNext()) {
                    int i5 = u + 53;
                    y = i5 % 128;
                    switch (i5 % 2 != 0 ? '0' : 'a') {
                        case Opcodes.LADD /* 97 */:
                            bVar = o.el.b.d(bVar, it.next().f());
                        default:
                            o.el.b.d(bVar, it.next().f());
                            throw null;
                    }
                }
                switch (bVar != null) {
                    case true:
                        switch (AnonymousClass2.e[bVar.ordinal()]) {
                            case 1:
                                CardStatus cardStatus = CardStatus.Active;
                                int i6 = y + 1;
                                u = i6 % 128;
                                switch (i6 % 2 == 0 ? (char) 14 : 'W') {
                                    case 14:
                                        throw null;
                                    default:
                                        return cardStatus;
                                }
                            case 2:
                                return CardStatus.ActivationRequired;
                            case 3:
                                return CardStatus.Activating;
                            case 4:
                                return CardStatus.ActivationRefused;
                            case 5:
                                return CardStatus.Locked;
                            default:
                                return null;
                        }
                    default:
                        return null;
                }
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:34:0x0020, code lost:
    
        if (r6.p == null) goto L15;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final int C() {
        /*
            r6 = this;
            int r0 = o.eo.e.y
            int r0 = r0 + 93
            int r1 = r0 % 128
            o.eo.e.u = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 != 0) goto L1e
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r6.p
            r2 = 80
            int r2 = r2 / r1
            if (r0 != 0) goto L16
            r0 = 6
            goto L18
        L16:
            r0 = 27
        L18:
            switch(r0) {
                case 27: goto L23;
                default: goto L1b;
            }
        L1b:
            goto L22
        L1c:
            r0 = move-exception
            throw r0
        L1e:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r6.p
            if (r0 != 0) goto L23
        L22:
            return r1
        L23:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r6.p
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
            r2 = -1
            r3 = r2
        L30:
            boolean r4 = r0.hasNext()
            if (r4 == 0) goto L58
            java.lang.Object r4 = r0.next()
            o.eo.d r4 = (o.eo.d) r4
            int r4 = r4.g()
            if (r3 == r2) goto L4b
            if (r4 >= r3) goto L46
            r5 = 1
            goto L47
        L46:
            r5 = r1
        L47:
            switch(r5) {
                case 1: goto L4b;
                default: goto L4a;
            }
        L4a:
            goto L57
        L4b:
            int r3 = o.eo.e.y
            int r3 = r3 + 83
            int r5 = r3 % 128
            o.eo.e.u = r5
            int r3 = r3 % 2
            r3 = r4
        L57:
            goto L30
        L58:
            int r0 = java.lang.Math.max(r1, r3)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.C():int");
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0035  */
    /* JADX WARN: Removed duplicated region for block: B:23:0x004d  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String D() {
        /*
            r6 = this;
            int r0 = o.eo.e.u
            int r1 = r0 + 77
            int r2 = r1 % 128
            o.eo.e.y = r2
            int r1 = r1 % 2
            if (r1 == 0) goto Lf
            r1 = 61
            goto L11
        Lf:
            r1 = 48
        L11:
            r2 = 0
            r3 = 1
            r4 = 0
            switch(r1) {
                case 61: goto L1c;
                default: goto L17;
            }
        L17:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r1 = r6.p
            if (r1 != 0) goto L30
            goto L2e
        L1c:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r1 = r6.p
            r5 = 38
            int r5 = r5 / r4
            if (r1 != 0) goto L26
            r1 = 99
            goto L28
        L26:
            r1 = 23
        L28:
            switch(r1) {
                case 99: goto L35;
                default: goto L2b;
            }
        L2b:
            goto L34
        L2c:
            r0 = move-exception
            throw r0
        L2e:
            r1 = r4
            goto L31
        L30:
            r1 = r3
        L31:
            switch(r1) {
                case 0: goto L35;
                default: goto L34;
            }
        L34:
            goto L4d
        L35:
            int r0 = r0 + 41
            int r1 = r0 % 128
            o.eo.e.y = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L41
            goto L43
        L41:
            r3 = 79
        L43:
            switch(r3) {
                case 1: goto L47;
                default: goto L46;
            }
        L46:
            return r2
        L47:
            r0 = 17
            int r0 = r0 / r4
            return r2
        L4b:
            r0 = move-exception
            throw r0
        L4d:
            java.util.LinkedHashMap<java.lang.String, o.eo.d> r0 = r6.p
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.eo.e.y
            int r1 = r1 + 121
            int r3 = r1 % 128
            o.eo.e.u = r3
            int r1 = r1 % 2
        L61:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto La1
            java.lang.Object r1 = r0.next()
            o.eo.d r1 = (o.eo.d) r1
            java.util.List r1 = r1.d()
            java.util.Iterator r1 = r1.iterator()
        L76:
            boolean r3 = r1.hasNext()
            if (r3 == 0) goto L95
            java.lang.Object r3 = r1.next()
            o.et.c r3 = (o.et.c) r3
            byte[] r4 = r3.H()
            if (r4 == 0) goto L94
            java.lang.String r0 = new java.lang.String
            byte[] r1 = r3.H()
            java.nio.charset.Charset r2 = java.nio.charset.StandardCharsets.UTF_8
            r0.<init>(r1, r2)
            return r0
        L94:
            goto L76
        L95:
            int r1 = o.eo.e.u
            int r1 = r1 + 107
            int r3 = r1 % 128
            o.eo.e.y = r3
            int r1 = r1 % 2
            goto L61
        La1:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.D():java.lang.String");
    }

    public final o.el.d H() throws WalletValidationException {
        int i = y + 109;
        u = i % 128;
        int i2 = i % 2;
        switch (w().hasNext() ? Typography.dollar : (char) 23) {
            case 23:
                WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
                Object[] objArr = new Object[1];
                F(4 - MotionEvent.axisFromString(""), "!\u0000\u001e\u000f㙃", (byte) (89 - TextUtils.indexOf("", "")), objArr);
                throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
            default:
                int i3 = u + 41;
                y = i3 % 128;
                boolean z = i3 % 2 == 0;
                o.el.d next = w().next().iterator().next();
                switch (z) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        int i4 = u + Opcodes.LUSHR;
                        y = i4 % 128;
                        int i5 = i4 % 2;
                        return next;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final <T extends o.el.d> T b(java.lang.Class<T> r8) {
        /*
            r7 = this;
            java.util.Iterator r0 = r7.w()
            int r1 = o.eo.e.y
            int r1 = r1 + 43
            int r2 = r1 % 128
            o.eo.e.u = r2
            int r1 = r1 % 2
        L10:
            boolean r1 = r0.hasNext()
            r2 = 0
            r3 = 1
            if (r1 == 0) goto L1a
            r1 = r2
            goto L1b
        L1a:
            r1 = r3
        L1b:
            r4 = 0
            switch(r1) {
                case 1: goto L2c;
                default: goto L1f;
            }
        L1f:
            int r1 = o.eo.e.y
            int r1 = r1 + 35
            int r5 = r1 % 128
            o.eo.e.u = r5
            int r1 = r1 % 2
            if (r1 != 0) goto L2d
            goto L2d
        L2c:
            return r4
        L2d:
            java.lang.Object r1 = r0.next()
            o.eo.d r1 = (o.eo.d) r1
            java.util.Iterator r1 = r1.iterator()
        L37:
            boolean r5 = r1.hasNext()
            if (r5 == 0) goto L62
            java.lang.Object r5 = r1.next()
            o.el.d r5 = (o.el.d) r5
            boolean r6 = r8.isInstance(r5)
            if (r6 == 0) goto L60
            int r8 = o.eo.e.y
            int r8 = r8 + 109
            int r0 = r8 % 128
            o.eo.e.u = r0
            int r8 = r8 % 2
            if (r8 != 0) goto L57
            r2 = r3
            goto L58
        L57:
        L58:
            switch(r2) {
                case 0: goto L5c;
                default: goto L5b;
            }
        L5b:
            goto L5d
        L5c:
            return r5
        L5d:
            throw r4     // Catch: java.lang.Throwable -> L5e
        L5e:
            r8 = move-exception
            throw r8
        L60:
            goto L37
        L62:
            goto L10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.b(java.lang.Class):o.el.d");
    }

    /* JADX WARN: Code restructure failed: missing block: B:47:0x00d7, code lost:
    
        if (java.util.Objects.equals(r4.f71o, r5.f71o) != false) goto L58;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x00da, code lost:
    
        r1 = o.eo.e.u + 93;
        o.eo.e.y = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x00e4, code lost:
    
        if ((r1 % 2) == 0) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x00e6, code lost:
    
        r1 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x00e9, code lost:
    
        switch(r1) {
            case 0: goto L66;
            default: goto L63;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x00f4, code lost:
    
        if (java.util.Objects.equals(r4.n, r5.n) == false) goto L92;
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x0107, code lost:
    
        r1 = o.eo.e.u + 97;
        o.eo.e.y = r1 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:55:0x0111, code lost:
    
        if ((r1 % 2) == 0) goto L80;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x011d, code lost:
    
        r1 = 42 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:59:0x011e, code lost:
    
        if (java.util.Objects.equals(r4.l, r5.l) == false) goto L92;
     */
    /* JADX WARN: Code restructure failed: missing block: B:60:0x0133, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x012b, code lost:
    
        if (java.util.Objects.equals(r4.l, r5.l) == false) goto L83;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x012d, code lost:
    
        r5 = false;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x0130, code lost:
    
        switch(r5) {
            case 1: goto L92;
            default: goto L85;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x012f, code lost:
    
        r5 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x0101, code lost:
    
        r2 = 41 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:72:0x0102, code lost:
    
        if (java.util.Objects.equals(r4.n, r5.n) == false) goto L92;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x00e8, code lost:
    
        r1 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x00d1, code lost:
    
        if (java.util.Objects.equals(r4.f71o, r5.f71o) != false) goto L58;
     */
    /* JADX WARN: Removed duplicated region for block: B:93:0x0141 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean a(o.eo.e r5) {
        /*
            Method dump skipped, instructions count: 364
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.a(o.eo.e):boolean");
    }

    public final e I() {
        e eVar = new e(this.d, this.c, this.a, this.b, this.e, this.j, this.h, this.g, Boolean.FALSE, this.i, this.f71o, this.m, this.l, this.n, this.t);
        eVar.p = this.p;
        eVar.q = this.q;
        eVar.r = this.r;
        int i = y + 85;
        u = i % 128;
        switch (i % 2 == 0 ? '(' : (char) 0) {
            case 0:
                return eVar;
            default:
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:53:0x0167, code lost:
    
        if (r4.e == r4.a) goto L61;
     */
    /* JADX WARN: Code restructure failed: missing block: B:56:0x019c, code lost:
    
        r13 = new java.lang.Object[13];
        r13[12] = r4;
        r13[11] = java.lang.Integer.valueOf(r2);
        r13[10] = r4;
        r13[9] = r4;
        r13[r8] = java.lang.Integer.valueOf(r2);
        r13[7] = r4;
        r13[6] = r4;
        r13[r11] = java.lang.Integer.valueOf(r2);
        r13[4] = r4;
        r13[3] = r4;
        r13[2] = java.lang.Integer.valueOf(r2);
        r13[1] = r4;
        r13[0] = r4;
        r8 = o.e.a.s.get(696901393);
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x01e7, code lost:
    
        if (r8 == null) goto L66;
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x01e9, code lost:
    
        r25 = 22;
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x0280, code lost:
    
        if (((java.lang.Integer) ((java.lang.reflect.Method) r8).invoke(null, r13)).intValue() != r4.h) goto L82;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x0286, code lost:
    
        r11 = new java.lang.Object[]{r4, r4, java.lang.Integer.valueOf(r2), java.lang.Integer.valueOf(r2), r4, r4, java.lang.Integer.valueOf(r2), java.lang.Integer.valueOf(r2), r4, java.lang.Integer.valueOf(r2), r4};
        r8 = o.e.a.s.get(1075449051);
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x02c5, code lost:
    
        if (r8 == null) goto L74;
     */
    /* JADX WARN: Code restructure failed: missing block: B:66:0x02c7, code lost:
    
        r15 = '\b';
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x0341, code lost:
    
        r8 = ((java.lang.Integer) ((java.lang.reflect.Method) r8).invoke(null, r11)).intValue();
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x034e, code lost:
    
        r11 = (r4.d * r2) + r4.h;
        r3[r4.b] = r5[r8];
        r3[r4.b + 1] = r5[r11];
        r8 = o.eo.e.$11 + 63;
        o.eo.e.$10 = r8 % 128;
        r8 = r8 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:71:0x02ca, code lost:
    
        r8 = (java.lang.Class) o.e.a.c(10 - android.text.TextUtils.indexOf((java.lang.CharSequence) "", '0'), (char) (android.util.TypedValue.complexToFloat(0) > 0.0f ? 1 : (android.util.TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), android.widget.ExpandableListView.getPackedPositionChild(0) + 66);
        r13 = (byte) (-1);
        r14 = (byte) (r13 + 1);
        r12 = new java.lang.Object[1];
        J(r13, r14, (byte) (r14 + 1), r12);
        r15 = '\b';
        r8 = r8.getMethod((java.lang.String) r12[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
        o.e.a.s.put(1075449051, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:73:0x036c, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:74:0x036d, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:75:0x0371, code lost:
    
        if (r1 != null) goto L80;
     */
    /* JADX WARN: Code restructure failed: missing block: B:76:0x0373, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:77:0x0374, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:78:0x0375, code lost:
    
        r15 = '\b';
     */
    /* JADX WARN: Code restructure failed: missing block: B:79:0x037b, code lost:
    
        if (r4.c != r4.d) goto L85;
     */
    /* JADX WARN: Code restructure failed: missing block: B:80:0x037d, code lost:
    
        r8 = r25;
     */
    /* JADX WARN: Code restructure failed: missing block: B:81:0x0382, code lost:
    
        switch(r8) {
            case 22: goto L88;
            default: goto L87;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:82:0x0385, code lost:
    
        r8 = (r4.c * r2) + r4.h;
        r11 = (r4.d * r2) + r4.i;
        r3[r4.b] = r5[r8];
        r3[r4.b + 1] = r5[r11];
     */
    /* JADX WARN: Code restructure failed: missing block: B:84:0x039f, code lost:
    
        r4.i = ((r4.i + r2) - 1) % r2;
        r4.h = ((r4.h + r2) - 1) % r2;
        r8 = (r4.c * r2) + r4.i;
        r11 = (r4.d * r2) + r4.h;
        r3[r4.b] = r5[r8];
        r3[r4.b + 1] = r5[r11];
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x0380, code lost:
    
        r8 = 16;
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x01ed, code lost:
    
        r25 = 22;
        r8 = (java.lang.Class) o.e.a.c(10 - (android.view.ViewConfiguration.getFadingEdgeLength() >> 16), (char) (8856 - (android.os.Process.myTid() >> 22)), (android.os.SystemClock.currentThreadTimeMillis() > (-1) ? 1 : (android.os.SystemClock.currentThreadTimeMillis() == (-1) ? 0 : -1)) + 323);
        r15 = (byte) (-1);
        r14 = (byte) (r15 + 1);
        r12 = new java.lang.Object[1];
        J(r15, r14, r14, r12);
        r8 = r8.getMethod((java.lang.String) r12[0], java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class, java.lang.Object.class, java.lang.Integer.TYPE, java.lang.Object.class);
        o.e.a.s.put(696901393, r8);
     */
    /* JADX WARN: Code restructure failed: missing block: B:89:0x03d1, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:90:0x03d2, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:91:0x03d6, code lost:
    
        if (r1 != null) goto L94;
     */
    /* JADX WARN: Code restructure failed: missing block: B:92:0x03d8, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:93:0x03d9, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x017b, code lost:
    
        if (r4.e == r4.a) goto L61;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void F(int r30, java.lang.String r31, byte r32, java.lang.Object[] r33) {
        /*
            Method dump skipped, instructions count: 1074
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.e.F(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
