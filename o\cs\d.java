package o.cs;

import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import o.cr.a;
import o.ee.g;
import o.fc.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cs\d.smali */
public final class d extends a<o.fi.a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int c;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        a();
        TextUtils.getOffsetAfter("", 0);
        int i = d + 45;
        c = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        e = -1634273400916958605L;
    }

    private static void i(short s, short s2, byte b, Object[] objArr) {
        int i = 71 - (s2 * 3);
        byte[] bArr = $$d;
        int i2 = 1 - (b * 4);
        int i3 = 4 - (s * 4);
        byte[] bArr2 = new byte[i2];
        int i4 = -1;
        int i5 = i2 - 1;
        if (bArr == null) {
            int i6 = i3 + (-i5);
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i4 = -1;
            i = i6;
            i3++;
        }
        while (true) {
            int i7 = i4 + 1;
            bArr2[i7] = (byte) i;
            if (i7 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i8 = i;
            int i9 = i3;
            Object[] objArr2 = objArr;
            int i10 = i5;
            byte[] bArr3 = bArr2;
            byte[] bArr4 = bArr;
            int i11 = i8 + (-bArr[i3]);
            int i12 = i9 + 1;
            i5 = i10;
            objArr = objArr2;
            bArr = bArr4;
            bArr2 = bArr3;
            i4 = i7;
            i = i11;
            i3 = i12;
        }
    }

    static void init$0() {
        $$d = new byte[]{12, -43, 42, 57};
        $$e = 219;
    }

    @Override // o.cr.a
    public final /* synthetic */ o.fi.a b(short s, int i, byte[] bArr) {
        int i2 = d + Opcodes.LUSHR;
        c = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case true:
                e(s, i, bArr);
                throw null;
            default:
                o.fi.a e2 = e(s, i, bArr);
                int i3 = d + 77;
                c = i3 % 128;
                switch (i3 % 2 == 0 ? ';' : 'S') {
                    case ';':
                        obj.hashCode();
                        throw null;
                    default:
                        return e2;
                }
        }
    }

    private static o.fi.a e(short s, int i, byte[] bArr) {
        g.c();
        Object[] objArr = new Object[1];
        h("Ω댺Ϥ凲穘뽥亨\ue6df襉\ude07⨩籱ᚬ䭘ꂔ\uf387ꏜㇿὢۙ⤈븲訥鰞", (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h("筪籺笈麣ﲥ㦔횖绾\uf1beᅶ곌\ue45b湧萓♳殆\udb31ﺣ馴", View.resolveSize(0, 0), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.fi.a aVar = new o.fi.a(true, c.b, s);
        aVar.a(i);
        aVar.a(bArr);
        int i2 = c + Opcodes.DDIV;
        d = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                return aVar;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 382
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cs.d.h(java.lang.String, int, java.lang.Object[]):void");
    }
}
