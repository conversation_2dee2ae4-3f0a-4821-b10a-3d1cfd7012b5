package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Serializer;
import com.esotericsoftware.kryo.SerializerFactory;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.util.Generics;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import com.esotericsoftware.reflectasm.FieldAccess;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer.smali */
public class FieldSerializer<T> extends Serializer<T> {
    final CachedFields cachedFields;
    final FieldSerializerConfig config;
    private final Generics.GenericsHierarchy genericsHierarchy;
    final Kryo kryo;
    final Class type;

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer$Bind.smali */
    public @interface Bind {
        boolean canBeNull() default true;

        boolean optimizePositive() default false;

        Class<? extends Serializer> serializer() default Serializer.class;

        Class<? extends SerializerFactory> serializerFactory() default SerializerFactory.class;

        Class valueClass() default Object.class;

        boolean variableLengthEncoding() default true;
    }

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer$NotNull.smali */
    public @interface NotNull {
    }

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer$Optional.smali */
    public @interface Optional {
        String value();
    }

    public FieldSerializer(Kryo kryo, Class type) {
        this(kryo, type, new FieldSerializerConfig());
    }

    public FieldSerializer(Kryo kryo, Class type, FieldSerializerConfig config) {
        if (type == null) {
            throw new IllegalArgumentException("type cannot be null.");
        }
        if (type.isPrimitive()) {
            throw new IllegalArgumentException("type cannot be a primitive class: " + type);
        }
        if (config == null) {
            throw new IllegalArgumentException("config cannot be null.");
        }
        this.kryo = kryo;
        this.type = type;
        this.config = config;
        this.genericsHierarchy = new Generics.GenericsHierarchy(type);
        CachedFields cachedFields = new CachedFields(this);
        this.cachedFields = cachedFields;
        cachedFields.rebuild();
    }

    protected void initializeCachedFields() {
    }

    public FieldSerializerConfig getFieldSerializerConfig() {
        return this.config;
    }

    public void updateFields() {
        if (Log.TRACE) {
            Log.trace("kryo", "Update fields: " + Util.className(this.type));
        }
        this.cachedFields.rebuild();
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        int pop = pushTypeVariables();
        CachedField[] fields = this.cachedFields.fields;
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            if (Log.TRACE) {
                log("Write", fields[i], output.position());
            }
            try {
                fields[i].write(output, object);
            } catch (KryoException e) {
                throw e;
            } catch (Exception e2) {
                e = e2;
                throw new KryoException("Error writing " + fields[i] + " at position " + output.position(), e);
            } catch (OutOfMemoryError e3) {
                e = e3;
                throw new KryoException("Error writing " + fields[i] + " at position " + output.position(), e);
            }
        }
        popTypeVariables(pop);
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        int pop = pushTypeVariables();
        T object = create(kryo, input, type);
        kryo.reference(object);
        CachedField[] fields = this.cachedFields.fields;
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            if (Log.TRACE) {
                log("Read", fields[i], input.position());
            }
            try {
                fields[i].read(input, object);
            } catch (KryoException e) {
                throw e;
            } catch (Exception e2) {
                e = e2;
                throw new KryoException("Error reading " + fields[i] + " at position " + input.position(), e);
            } catch (OutOfMemoryError e3) {
                e = e3;
                throw new KryoException("Error reading " + fields[i] + " at position " + input.position(), e);
            }
        }
        popTypeVariables(pop);
        return object;
    }

    protected int pushTypeVariables() {
        Generics.GenericType[] genericTypes = this.kryo.getGenerics().nextGenericTypes();
        if (genericTypes == null) {
            return 0;
        }
        int pop = this.kryo.getGenerics().pushTypeVariables(this.genericsHierarchy, genericTypes);
        if (Log.TRACE && pop > 0) {
            Log.trace("kryo", "Generics: " + this.kryo.getGenerics());
        }
        return pop;
    }

    protected void popTypeVariables(int pop) {
        Generics generics = this.kryo.getGenerics();
        if (pop > 0) {
            generics.popTypeVariables(pop);
        }
        generics.popGenericType();
    }

    protected T create(Kryo kryo, Input input, Class<? extends T> cls) {
        return (T) kryo.newInstance(cls);
    }

    protected void log(String prefix, CachedField cachedField, int position) {
        String fieldClassName;
        if (cachedField instanceof ReflectField) {
            ReflectField reflectField = (ReflectField) cachedField;
            Class fieldClass = reflectField.resolveFieldClass();
            if (fieldClass == null) {
                fieldClass = cachedField.field.getType();
            }
            fieldClassName = Util.simpleName(fieldClass, reflectField.genericType);
        } else if (cachedField.valueClass != null) {
            fieldClassName = cachedField.valueClass.getSimpleName();
        } else {
            fieldClassName = cachedField.field.getType().getSimpleName();
        }
        Log.trace("kryo", prefix + " field " + fieldClassName + ": " + cachedField.name + " (" + Util.className(cachedField.field.getDeclaringClass()) + ')' + Util.pos(position));
    }

    public CachedField getField(String fieldName) {
        for (CachedField cachedField : this.cachedFields.fields) {
            if (cachedField.name.equals(fieldName)) {
                return cachedField;
            }
        }
        throw new IllegalArgumentException("Field \"" + fieldName + "\" not found on class: " + this.type.getName());
    }

    public void removeField(String fieldName) {
        this.cachedFields.removeField(fieldName);
    }

    public void removeField(CachedField field) {
        this.cachedFields.removeField(field);
    }

    public CachedField[] getFields() {
        return this.cachedFields.fields;
    }

    public CachedField[] getCopyFields() {
        return this.cachedFields.copyFields;
    }

    public Class getType() {
        return this.type;
    }

    public Kryo getKryo() {
        return this.kryo;
    }

    protected T createCopy(Kryo kryo, T t) {
        return (T) kryo.newInstance(t.getClass());
    }

    @Override // com.esotericsoftware.kryo.Serializer
    public T copy(Kryo kryo, T original) {
        T copy = createCopy(kryo, original);
        kryo.reference(copy);
        int n = this.cachedFields.copyFields.length;
        for (int i = 0; i < n; i++) {
            this.cachedFields.copyFields[i].copy(original, copy);
        }
        return copy;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer$CachedField.smali */
    public static abstract class CachedField {
        FieldAccess access;
        boolean canBeNull;
        final Field field;
        String name;
        long offset;
        boolean optimizePositive;
        Serializer serializer;
        int tag;
        Class valueClass;
        boolean varEncoding = true;
        boolean reuseSerializer = true;
        int accessIndex = -1;

        public abstract void copy(Object obj, Object obj2);

        public abstract void read(Input input, Object obj);

        public abstract void write(Output output, Object obj);

        public CachedField(Field field) {
            this.field = field;
        }

        public void setValueClass(Class valueClass) {
            this.valueClass = valueClass;
        }

        public Class getValueClass() {
            return this.valueClass;
        }

        public void setValueClass(Class valueClass, Serializer serializer) {
            this.valueClass = valueClass;
            this.serializer = serializer;
        }

        public void setSerializer(Serializer serializer) {
            this.serializer = serializer;
        }

        public Serializer getSerializer() {
            return this.serializer;
        }

        public void setCanBeNull(boolean canBeNull) {
            this.canBeNull = canBeNull;
        }

        public boolean getCanBeNull() {
            return this.canBeNull;
        }

        public void setVariableLengthEncoding(boolean varEncoding) {
            this.varEncoding = varEncoding;
        }

        public boolean getVariableLengthEncoding() {
            return this.varEncoding;
        }

        public void setOptimizePositive(boolean optimizePositive) {
            this.optimizePositive = optimizePositive;
        }

        public boolean getOptimizePositive() {
            return this.optimizePositive;
        }

        void setReuseSerializer(boolean reuseSerializer) {
            this.reuseSerializer = reuseSerializer;
        }

        boolean getReuseSerializer() {
            return this.reuseSerializer;
        }

        public String getName() {
            return this.name;
        }

        public Field getField() {
            return this.field;
        }

        public String toString() {
            return this.name;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\FieldSerializer$FieldSerializerConfig.smali */
    public static class FieldSerializerConfig implements Cloneable {
        boolean extendedFieldNames;
        boolean fixedFieldTypes;
        boolean serializeTransient;
        boolean fieldsCanBeNull = true;
        boolean setFieldsAsAccessible = true;
        boolean ignoreSyntheticFields = true;
        boolean copyTransient = true;
        boolean varEncoding = true;

        @Override // 
        /* renamed from: clone, reason: merged with bridge method [inline-methods] */
        public FieldSerializerConfig mo108clone() {
            try {
                return (FieldSerializerConfig) super.clone();
            } catch (CloneNotSupportedException ex) {
                throw new KryoException(ex);
            }
        }

        public void setFieldsCanBeNull(boolean fieldsCanBeNull) {
            this.fieldsCanBeNull = fieldsCanBeNull;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig fieldsCanBeNull: " + fieldsCanBeNull);
            }
        }

        public boolean getFieldsCanBeNull() {
            return this.fieldsCanBeNull;
        }

        public void setFieldsAsAccessible(boolean setFieldsAsAccessible) {
            this.setFieldsAsAccessible = setFieldsAsAccessible;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig setFieldsAsAccessible: " + setFieldsAsAccessible);
            }
        }

        public boolean getSetFieldsAsAccessible() {
            return this.setFieldsAsAccessible;
        }

        public void setIgnoreSyntheticFields(boolean ignoreSyntheticFields) {
            this.ignoreSyntheticFields = ignoreSyntheticFields;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig ignoreSyntheticFields: " + ignoreSyntheticFields);
            }
        }

        public boolean getIgnoreSyntheticFields() {
            return this.ignoreSyntheticFields;
        }

        public void setFixedFieldTypes(boolean fixedFieldTypes) {
            this.fixedFieldTypes = fixedFieldTypes;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig fixedFieldTypes: " + fixedFieldTypes);
            }
        }

        public boolean getFixedFieldTypes() {
            return this.fixedFieldTypes;
        }

        public void setCopyTransient(boolean copyTransient) {
            this.copyTransient = copyTransient;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig copyTransient: " + copyTransient);
            }
        }

        public boolean getCopyTransient() {
            return this.copyTransient;
        }

        public void setSerializeTransient(boolean serializeTransient) {
            this.serializeTransient = serializeTransient;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig serializeTransient: " + serializeTransient);
            }
        }

        public boolean getSerializeTransient() {
            return this.serializeTransient;
        }

        public void setVariableLengthEncoding(boolean varEncoding) {
            this.varEncoding = varEncoding;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig variable length encoding: " + varEncoding);
            }
        }

        public boolean getVariableLengthEncoding() {
            return this.varEncoding;
        }

        public void setExtendedFieldNames(boolean extendedFieldNames) {
            this.extendedFieldNames = extendedFieldNames;
            if (Log.TRACE) {
                Log.trace("kryo", "FieldSerializerConfig extendedFieldNames: " + extendedFieldNames);
            }
        }

        public boolean getExtendedFieldNames() {
            return this.extendedFieldNames;
        }
    }
}
