package androidx.core.util;

import java.util.Objects;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\util\ObjectsCompat.smali */
public class ObjectsCompat {
    private ObjectsCompat() {
    }

    public static boolean equals(Object a, Object b) {
        return Api19Impl.equals(a, b);
    }

    public static int hashCode(Object o2) {
        if (o2 != null) {
            return o2.hashCode();
        }
        return 0;
    }

    public static int hash(Object... values) {
        return Api19Impl.hash(values);
    }

    public static String toString(Object o2, String nullDefault) {
        return o2 != null ? o2.toString() : nullDefault;
    }

    public static <T> T requireNonNull(T obj) {
        if (obj == null) {
            throw new NullPointerException();
        }
        return obj;
    }

    public static <T> T requireNonNull(T obj, String message) {
        if (obj == null) {
            throw new NullPointerException(message);
        }
        return obj;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\util\ObjectsCompat$Api19Impl.smali */
    static class Api19Impl {
        private Api19Impl() {
        }

        static boolean equals(Object a, Object b) {
            return Objects.equals(a, b);
        }

        static int hash(Object... values) {
            return Objects.hash(values);
        }
    }
}
