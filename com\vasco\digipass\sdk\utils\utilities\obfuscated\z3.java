package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;
import java.util.Enumeration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\z3.smali */
public class z3 extends u {
    private final r C;
    private final r L;
    private final v7 R;
    private final r b;
    private final r x;

    private z3(e0 e0Var) {
        if (e0Var.size() < 3 || e0Var.size() > 5) {
            throw new IllegalArgumentException("Bad sequence size: " + e0Var.size());
        }
        Enumeration j = e0Var.j();
        this.b = r.a(j.nextElement());
        this.x = r.a(j.nextElement());
        this.C = r.a(j.nextElement());
        h a = a(j);
        if (a == null || !(a instanceof r)) {
            this.L = null;
        } else {
            this.L = r.a(a);
            a = a(j);
        }
        if (a != null) {
            this.R = v7.a(a.toASN1Primitive());
        } else {
            this.R = null;
        }
    }

    public static z3 a(Object obj) {
        if (obj instanceof z3) {
            return (z3) obj;
        }
        if (obj != null) {
            return new z3(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return this.x.h();
    }

    public BigInteger f() {
        r rVar = this.L;
        if (rVar == null) {
            return null;
        }
        return rVar.h();
    }

    public BigInteger g() {
        return this.b.h();
    }

    public BigInteger h() {
        return this.C.h();
    }

    public v7 i() {
        return this.R;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        i iVar = new i(5);
        iVar.a(this.b);
        iVar.a(this.x);
        iVar.a(this.C);
        r rVar = this.L;
        if (rVar != null) {
            iVar.a(rVar);
        }
        v7 v7Var = this.R;
        if (v7Var != null) {
            iVar.a((h) v7Var);
        }
        return new j2(iVar);
    }

    private static h a(Enumeration enumeration) {
        if (enumeration.hasMoreElements()) {
            return (h) enumeration.nextElement();
        }
        return null;
    }
}
