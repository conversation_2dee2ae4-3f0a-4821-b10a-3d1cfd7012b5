package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\u7.smali */
public class u7 {
    private static byte a(char c) {
        int i;
        char c2 = 'a';
        if (c < 'a') {
            c2 = 'A';
            if (c < 'A') {
                i = c - '0';
                return (byte) i;
            }
        }
        i = (c - c2) + 10;
        return (byte) i;
    }

    public static String a(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (byte b : bArr) {
            String hexString = Integer.toHexString(b & 255);
            if (hexString.length() == 1) {
                sb.append('0');
            }
            sb.append(hexString);
        }
        return sb.toString().toUpperCase();
    }

    private static boolean b(byte[] bArr) {
        if (c(bArr)) {
            return true;
        }
        for (byte b : bArr) {
            char c = (char) b;
            if (c < '0' || c > '9') {
                return false;
            }
        }
        return true;
    }

    public static boolean c(byte[] bArr) {
        if (bArr != null && bArr.length != 0) {
            for (byte b : bArr) {
                if (b != 0) {
                    return false;
                }
            }
        }
        return true;
    }

    public static void d(byte[] bArr) {
        if (bArr != null) {
            Arrays.fill(bArr, (byte) 0);
        }
    }

    private static boolean d(String str) {
        return str == null || str.length() == 0;
    }

    public static boolean c(String str) {
        if (d(str)) {
            return true;
        }
        for (int i = 0; i < str.length(); i++) {
            char charAt = str.charAt(i);
            if ((charAt < '0' || charAt > '9') && ((charAt < 'A' || charAt > 'F') && (charAt < 'a' || charAt > 'f'))) {
                return false;
            }
        }
        return true;
    }

    public static boolean b(String str) {
        byte[] bytes = str != null ? str.getBytes() : null;
        boolean b = b(bytes);
        d(bytes);
        return b;
    }

    public static byte[] a(String str) {
        if (str == null) {
            return null;
        }
        byte[] bArr = new byte[str.length() / 2];
        a(str, bArr);
        return bArr;
    }

    private static void a(String str, byte[] bArr) {
        int length = str.length() / 2;
        for (int i = 0; i < length; i++) {
            int i2 = i * 2;
            bArr[i] = (byte) ((a(str.charAt(i2)) << 4) + (a(str.charAt(i2 + 1)) & 255));
        }
    }
}
