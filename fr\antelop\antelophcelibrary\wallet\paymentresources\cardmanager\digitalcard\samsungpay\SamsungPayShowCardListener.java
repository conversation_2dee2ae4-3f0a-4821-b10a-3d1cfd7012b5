package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import com.samsung.android.sdk.samsungpay.v2.StatusListener;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.a.j;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayShowCardListener.smali */
public class SamsungPayShowCardListener implements StatusListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static boolean b;
    private static boolean c;
    private static char[] d;
    private static int e;
    private static int f;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        f = 1;
        b();
        SystemClock.currentThreadTimeMillis();
        int i = a + Opcodes.LSUB;
        f = i % 128;
        switch (i % 2 == 0 ? '5' : 'Q') {
            case Opcodes.SALOAD /* 53 */:
                throw null;
            default:
                return;
        }
    }

    static void b() {
        d = new char[]{61815, 61569, 61581, 61591, 61589, 61580, 61579, 61810, 61593, 61578, 61587, 61595, 61799, 61584, 61574, 61806, 61577, 61590, 61573, 61762, 61773, 61575, 61568, 61582};
        c = true;
        b = true;
        e = 782102818;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r7, int r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = 121 - r7
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayShowCardListener.$$a
            int r9 = r9 * 2
            int r9 = 4 - r9
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r9]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r7 = r7 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayShowCardListener.h(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{113, -79, 113, -105};
        $$b = 44;
    }

    public void onSuccess(int i, Bundle bundle) {
        int i2 = a + Opcodes.DREM;
        f = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        g(null, TextUtils.getTrimmedLength("") + 127, null, "\u008e\u0093\u0086\u0093\u0092\u0084\u0091\u0090\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(null, (ViewConfiguration.getPressedStateDuration() >> 16) + 127, null, "\u0084\u0084\u0093\u0096\u0096\u0085\u0084\u0094\u0095\u0094\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0084", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        int i4 = f + 97;
        a = i4 % 128;
        int i5 = i4 % 2;
    }

    public void onFail(int i, Bundle bundle) {
        String intern;
        Object obj;
        int i2 = f + 63;
        a = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.less : (char) 6) {
            case 6:
                g.c();
                Object[] objArr = new Object[1];
                g(null, View.MeasureSpec.getMode(0) + 127, null, "\u008e\u0093\u0086\u0093\u0092\u0084\u0091\u0090\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
                intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                g(null, 127 - (Process.myTid() >> 22), null, "\u008f\u008e\u0082\u0096\u0094\u0084\u0091\u008a\u0092\u0094\u008c\u008b\u008a\u0084\u0094\u008b\u0092\u0094\u0093\u0098\u0097\u0082\u0086\u0085\u0094\u0084\u0091\u0094\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0084\u0094\u0095\u0094\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0084", objArr2);
                obj = objArr2[0];
                break;
            default:
                g.c();
                Object[] objArr3 = new Object[1];
                g(null, 11 << View.MeasureSpec.getMode(0), null, "\u008e\u0093\u0086\u0093\u0092\u0084\u0091\u0090\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0081\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr3);
                intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                g(null, Opcodes.ISHR >> (Process.myTid() >>> 32), null, "\u008f\u008e\u0082\u0096\u0094\u0084\u0091\u008a\u0092\u0094\u008c\u008b\u008a\u0084\u0094\u008b\u0092\u0094\u0093\u0098\u0097\u0082\u0086\u0085\u0094\u0084\u0091\u0094\u0089\u0082\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0084\u0094\u0095\u0094\u008f\u008e\u0082\u008d\u008c\u008b\u008a\u0084", objArr4);
                obj = objArr4[0];
                break;
        }
        g.d(intern, ((String) obj).intern());
        int i3 = f + 69;
        a = i3 % 128;
        int i4 = i3 % 2;
    }

    private static void g(String str, int i, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        String str3 = str2;
        int i2 = $11;
        int i3 = i2 + 29;
        $10 = i3 % 128;
        int i4 = i3 % 2;
        char c2 = str3 != null ? 'N' : 'V';
        byte[] bArr = str3;
        switch (c2) {
            case Opcodes.SASTORE /* 86 */:
                break;
            default:
                int i5 = i2 + 29;
                $10 = i5 % 128;
                if (i5 % 2 == 0) {
                    bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
                } else {
                    int i6 = 55 / 0;
                    bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                    break;
                }
        }
        byte[] bArr2 = bArr;
        if (str != null) {
            int i7 = $10 + Opcodes.DMUL;
            $11 = i7 % 128;
            int i8 = i7 % 2;
            cArr = str.toCharArray();
        } else {
            cArr = str;
        }
        char[] cArr2 = cArr;
        j jVar = new j();
        char[] cArr3 = d;
        switch (cArr3 != null ? '\\' : (char) 22) {
            case Opcodes.DUP2 /* 92 */:
                int length = cArr3.length;
                char[] cArr4 = new char[length];
                int i9 = 0;
                while (true) {
                    switch (i9 >= length) {
                        case false:
                            try {
                                Object[] objArr2 = {Integer.valueOf(cArr3[i9])};
                                Object obj = o.e.a.s.get(1085633688);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 493 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24));
                                    byte b2 = (byte) 0;
                                    byte b3 = b2;
                                    Object[] objArr3 = new Object[1];
                                    h(b2, b3, b3, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                    o.e.a.s.put(1085633688, obj);
                                }
                                cArr4[i9] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                                i9++;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        default:
                            cArr3 = cArr4;
                            break;
                    }
                }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(e)};
            Object obj2 = o.e.a.s.get(-1667314477);
            long j = 0;
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (char) (8857 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 323);
                byte b4 = (byte) 3;
                byte b5 = (byte) (b4 - 3);
                Object[] objArr5 = new Object[1];
                h(b4, b5, b5, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            switch (b) {
                case true:
                    jVar.e = bArr2.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        int i10 = $11 + Opcodes.DNEG;
                        $10 = i10 % 128;
                        if (i10 % 2 != 0) {
                            cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e >>> 1) + jVar.c] >> i] % intValue);
                            try {
                                Object[] objArr6 = {jVar, jVar};
                                Object obj3 = o.e.a.s.get(745816316);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 9, (char) ((SystemClock.elapsedRealtime() > j ? 1 : (SystemClock.elapsedRealtime() == j ? 0 : -1)) - 1), 207 - (Process.myPid() >> 22));
                                    byte length2 = (byte) $$a.length;
                                    byte b6 = (byte) (length2 - 4);
                                    Object[] objArr7 = new Object[1];
                                    h(length2, b6, b6, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(745816316, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr6);
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        } else {
                            cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                            try {
                                Object[] objArr8 = {jVar, jVar};
                                Object obj4 = o.e.a.s.get(745816316);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c(11 - (SystemClock.uptimeMillis() > j ? 1 : (SystemClock.uptimeMillis() == j ? 0 : -1)), (char) (ViewConfiguration.getWindowTouchSlop() >> 8), (ViewConfiguration.getPressedStateDuration() >> 16) + 207);
                                    byte length3 = (byte) $$a.length;
                                    byte b7 = (byte) (length3 - 4);
                                    Object[] objArr9 = new Object[1];
                                    h(length3, b7, b7, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(745816316, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        }
                        j = 0;
                    }
                    objArr[0] = new String(cArr5);
                    return;
                default:
                    switch (c ? '?' : '\b') {
                        case '?':
                            int i11 = $10 + 31;
                            $11 = i11 % 128;
                            int i12 = i11 % 2;
                            jVar.e = cArr2.length;
                            char[] cArr6 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i] - intValue);
                                try {
                                    Object[] objArr10 = {jVar, jVar};
                                    Object obj5 = o.e.a.s.get(745816316);
                                    if (obj5 == null) {
                                        Class cls5 = (Class) o.e.a.c(10 - View.resolveSize(0, 0), (char) Color.alpha(0), ((byte) KeyEvent.getModifierMetaStateMask()) + 208);
                                        byte length4 = (byte) $$a.length;
                                        byte b8 = (byte) (length4 - 4);
                                        Object[] objArr11 = new Object[1];
                                        h(length4, b8, b8, objArr11);
                                        obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                        o.e.a.s.put(745816316, obj5);
                                    }
                                    ((Method) obj5).invoke(null, objArr10);
                                } catch (Throwable th4) {
                                    Throwable cause4 = th4.getCause();
                                    if (cause4 == null) {
                                        throw th4;
                                    }
                                    throw cause4;
                                }
                            }
                            objArr[0] = new String(cArr6);
                            return;
                        default:
                            jVar.e = iArr.length;
                            char[] cArr7 = new char[jVar.e];
                            jVar.c = 0;
                            while (jVar.c < jVar.e) {
                                cArr7[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                jVar.c++;
                            }
                            objArr[0] = new String(cArr7);
                            return;
                    }
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
