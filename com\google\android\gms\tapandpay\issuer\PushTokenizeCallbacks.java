package com.google.android.gms.tapandpay.issuer;

import android.os.RemoteException;
import android.util.Log;
import com.google.android.gms.common.internal.Preconditions;
import com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks;
import com.google.firebase.messaging.Constants;
import java.util.ArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\issuer\PushTokenizeCallbacks.smali */
public final class PushTokenizeCallbacks extends IPushTokenizeRequestCallbacks.Stub {
    private final int[] zza;
    private final Executor zzb;
    private final WalletAvailabilityChecker zzc;
    private final PaymentCredentialsGenerator zzd;

    private PushTokenizeCallbacks(int[] iArr, Executor executor, WalletAvailabilityChecker walletAvailabilityChecker, PaymentCredentialsGenerator paymentCredentialsGenerator) {
        this.zza = iArr;
        this.zzb = executor;
        this.zzc = walletAvailabilityChecker;
        this.zzd = paymentCredentialsGenerator;
    }

    public static PushTokenizeCallbacks tryCreate(Executor executor, WalletAvailabilityChecker walletAvailabilityChecker, PaymentCredentialsGenerator generator) {
        if (walletAvailabilityChecker == null && generator == null) {
            return null;
        }
        ArrayList arrayList = new ArrayList();
        if (walletAvailabilityChecker != null) {
            arrayList.add(1);
        }
        if (generator != null) {
            arrayList.add(2);
            if (generator.getGoogleOpaquePaymentCardSupported()) {
                arrayList.add(3);
            }
        }
        Object[] array = arrayList.toArray();
        int length = array.length;
        int[] iArr = new int[length];
        for (int i = 0; i < length; i++) {
            Object obj = array[i];
            if (obj == null) {
                throw null;
            }
            iArr[i] = ((Number) obj).intValue();
        }
        if (executor == null) {
            executor = Executors.newSingleThreadExecutor();
        }
        return new PushTokenizeCallbacks(iArr, executor, walletAvailabilityChecker, generator);
    }

    @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks
    public void generatePaymentCredentials(final GetPaymentCredentialsRequest request, final IPushTokenizeResponseCallbacks callback) {
        this.zzb.execute(new Runnable() { // from class: com.google.android.gms.tapandpay.issuer.zzg
            @Override // java.lang.Runnable
            public final void run() {
                PushTokenizeCallbacks.this.zza(request, callback);
            }
        });
    }

    public int[] getSupportedCallbackRequestTypes() {
        return this.zza;
    }

    @Override // com.google.android.gms.tapandpay.issuer.IPushTokenizeRequestCallbacks
    public void isWalletAvailable(final String walletId, final IPushTokenizeResponseCallbacks callback) {
        this.zzb.execute(new Runnable() { // from class: com.google.android.gms.tapandpay.issuer.zzh
            @Override // java.lang.Runnable
            public final void run() {
                PushTokenizeCallbacks.this.zzb(walletId, callback);
            }
        });
    }

    final /* synthetic */ void zza(GetPaymentCredentialsRequest getPaymentCredentialsRequest, IPushTokenizeResponseCallbacks iPushTokenizeResponseCallbacks) {
        try {
            Preconditions.checkNotNull(this.zzd);
            iPushTokenizeResponseCallbacks.onPaymentCredentialsResponse(this.zzd.generate(getPaymentCredentialsRequest));
        } catch (Exception e) {
            Log.e("PushTokenizeCallbacks", Constants.IPC_BUNDLE_KEY_SEND_ERROR, e);
            try {
                iPushTokenizeResponseCallbacks.onError(0);
            } catch (RemoteException e2) {
                Log.e("PushTokenizeCallbacks", "Remote Exception", e2);
            }
        }
    }

    final /* synthetic */ void zzb(String str, IPushTokenizeResponseCallbacks iPushTokenizeResponseCallbacks) {
        try {
            Preconditions.checkNotNull(this.zzc);
            iPushTokenizeResponseCallbacks.onWalletAvailableResponse(this.zzc.isAvailable(str));
        } catch (Exception e) {
            Log.e("PushTokenizeCallbacks", Constants.IPC_BUNDLE_KEY_SEND_ERROR, e);
            try {
                iPushTokenizeResponseCallbacks.onError(0);
            } catch (RemoteException e2) {
                Log.e("PushTokenizeCallbacks", "Remote Exception", e2);
            }
        }
    }
}
