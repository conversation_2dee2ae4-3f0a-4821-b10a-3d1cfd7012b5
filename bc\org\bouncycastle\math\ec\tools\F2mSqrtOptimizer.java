package bc.org.bouncycastle.math.ec.tools;

import bc.org.bouncycastle.math.ec.ECAlgorithms;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.c4;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.e8;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u1;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.TreeSet;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\tools\F2mSqrtOptimizer.smali */
public class F2mSqrtOptimizer {
    private static void a(ECCurve eCCurve) {
        ECFieldElement fromBigInteger = eCCurve.fromBigInteger(BigInteger.valueOf(2L));
        ECFieldElement sqrt = fromBigInteger.sqrt();
        System.out.println(sqrt.toBigInteger().toString(16).toUpperCase());
        if (!sqrt.square().equals(fromBigInteger)) {
            throw new IllegalStateException("Optimized-sqrt sanity check failed");
        }
    }

    public static void main(String[] strArr) {
        TreeSet<String> treeSet = new TreeSet(a(c4.a()));
        treeSet.addAll(a(u1.a()));
        for (String str : treeSet) {
            e8 b = u1.b(str);
            if (b == null) {
                b = c4.b(str);
            }
            if (b != null) {
                ECCurve c = b.c();
                if (ECAlgorithms.isF2mCurve(c)) {
                    System.out.print(str + ":");
                    a(c);
                }
            }
        }
    }

    public static void printRootZ(ECCurve eCCurve) {
        if (!ECAlgorithms.isF2mCurve(eCCurve)) {
            throw new IllegalArgumentException("Sqrt optimization only defined over characteristic-2 fields");
        }
        a(eCCurve);
    }

    private static ArrayList a(Enumeration enumeration) {
        ArrayList arrayList = new ArrayList();
        while (enumeration.hasMoreElements()) {
            arrayList.add(enumeration.nextElement());
        }
        return arrayList;
    }
}
