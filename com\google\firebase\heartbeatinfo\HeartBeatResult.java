package com.google.firebase.heartbeatinfo;

import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\firebase\heartbeatinfo\HeartBeatResult.smali */
public abstract class HeartBeatResult {
    public abstract List<String> getUsedDates();

    public abstract String getUserAgent();

    public static HeartBeatResult create(String userAgent, List<String> dateList) {
        return new AutoValue_HeartBeatResult(userAgent, dateList);
    }
}
