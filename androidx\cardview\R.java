package androidx.cardview;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R.smali */
public final class R {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R$attr.smali */
    public static final class attr {
        public static int cardBackgroundColor = 0x7f0400ef;
        public static int cardCornerRadius = 0x7f0400f0;
        public static int cardElevation = 0x7f0400f1;
        public static int cardMaxElevation = 0x7f0400f3;
        public static int cardPreventCornerOverlap = 0x7f0400f4;
        public static int cardUseCompatPadding = 0x7f0400f5;
        public static int cardViewStyle = 0x7f0400f6;
        public static int contentPadding = 0x7f040147;
        public static int contentPaddingBottom = 0x7f040148;
        public static int contentPaddingLeft = 0x7f040149;
        public static int contentPaddingRight = 0x7f04014a;
        public static int contentPaddingTop = 0x7f04014b;

        private attr() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R$color.smali */
    public static final class color {
        public static int cardview_dark_background = 0x7f06004f;
        public static int cardview_light_background = 0x7f060050;
        public static int cardview_shadow_end_color = 0x7f060051;
        public static int cardview_shadow_start_color = 0x7f060052;

        private color() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R$dimen.smali */
    public static final class dimen {
        public static int cardview_compat_inset_shadow = 0x7f07008b;
        public static int cardview_default_elevation = 0x7f07008c;
        public static int cardview_default_radius = 0x7f07008d;

        private dimen() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R$style.smali */
    public static final class style {
        public static int Base_CardView = 0x7f13002b;
        public static int CardView = 0x7f1300fe;
        public static int CardView_Dark = 0x7f1300ff;
        public static int CardView_Light = 0x7f130100;

        private style() {
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\cardview\R$styleable.smali */
    public static final class styleable {
        public static int[] CardView = {android.R.attr.minWidth, android.R.attr.minHeight, 2130968815, 2130968816, 2130968817, 2130968819, 2130968820, 2130968821, 2130968903, 2130968904, 2130968905, 2130968906, 2130968907};
        public static int CardView_android_minHeight = 0x00000001;
        public static int CardView_android_minWidth = 0x00000000;
        public static int CardView_cardBackgroundColor = 0x00000002;
        public static int CardView_cardCornerRadius = 0x00000003;
        public static int CardView_cardElevation = 0x00000004;
        public static int CardView_cardMaxElevation = 0x00000005;
        public static int CardView_cardPreventCornerOverlap = 0x00000006;
        public static int CardView_cardUseCompatPadding = 0x00000007;
        public static int CardView_contentPadding = 0x00000008;
        public static int CardView_contentPaddingBottom = 0x00000009;
        public static int CardView_contentPaddingLeft = 0x0000000a;
        public static int CardView_contentPaddingRight = 0x0000000b;
        public static int CardView_contentPaddingTop = 0x0000000c;

        private styleable() {
        }
    }

    private R() {
    }
}
