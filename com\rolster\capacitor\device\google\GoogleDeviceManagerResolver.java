package com.rolster.capacitor.device.google;

import android.content.Context;
import com.google.android.gms.common.GoogleApiAvailability;
import com.rolster.capacitor.device.DeviceManagerResolver;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes6\com\rolster\capacitor\device\google\GoogleDeviceManagerResolver.smali */
public class GoogleDeviceManagerResolver implements DeviceManagerResolver {
    private final Context context;

    public GoogleDeviceManagerResolver(Context context) {
        this.context = context;
    }

    @Override // com.rolster.capacitor.device.DeviceManagerResolver
    public boolean hasGoogle() {
        GoogleApiAvailability services = GoogleApiAvailability.getInstance();
        int status = services.isGooglePlayServicesAvailable(this.context);
        return status == 0;
    }

    @Override // com.rolster.capacitor.device.DeviceManagerResolver
    public boolean hasHuawei() {
        return false;
    }
}
