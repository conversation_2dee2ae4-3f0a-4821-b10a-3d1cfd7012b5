package o.v;

import android.graphics.ImageFormat;
import android.os.Process;
import android.util.TypedValue;
import android.view.Gravity;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\v\d.smali */
abstract class d extends o.p.h<o.dl.d> {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static long h;
    private static int i;
    private static int l;
    protected final o.eo.e n;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        l = 1;
        x();
        Gravity.getAbsoluteGravity(0, 0);
        int i2 = l + 29;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 25 : '`') {
            case 25:
                int i3 = 67 / 0;
                break;
        }
    }

    private static void E(short s, byte b, short s2, Object[] objArr) {
        byte[] bArr = $$g;
        int i2 = (b * 3) + 4;
        int i3 = 71 - (s2 * 3);
        int i4 = (s * 3) + 1;
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            i3 = i6 + i3;
            i6 = i6;
            i2++;
        }
        while (true) {
            i5++;
            bArr2[i5] = (byte) i3;
            if (i5 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i3 += bArr[i2];
            i6 = i6;
            i2++;
        }
    }

    static void init$0() {
        $$g = new byte[]{118, -84, -110, 65};
        $$h = 43;
    }

    static void x() {
        h = -7119964211861730075L;
    }

    abstract void b_() throws WalletValidationException;

    @Override // o.p.h
    public final /* synthetic */ o.dl.d r() {
        int i2 = i + 25;
        l = i2 % 128;
        int i3 = i2 % 2;
        o.dl.d a = a();
        int i4 = i + 59;
        l = i4 % 128;
        int i5 = i4 % 2;
        return a;
    }

    d(String str, o.eo.e eVar) {
        super(o.i.i.b, str, false);
        this.n = eVar;
    }

    private static o.dl.d a() {
        o.dl.d dVar = new o.dl.d();
        int i2 = l + 109;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int i3 = 20 / 0;
                return dVar;
            default:
                return dVar;
        }
    }

    @Override // o.p.h
    public final o.at.d b() {
        int i2 = l + 35;
        i = i2 % 128;
        int i3 = i2 % 2;
        o.at.d dVar = o.at.d.d;
        int i4 = l + 7;
        i = i4 % 128;
        switch (i4 % 2 != 0 ? '\f' : 'I') {
            case 'I':
                return dVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.p.h
    public final String e() {
        int i2 = i + Opcodes.LSHR;
        int i3 = i2 % 128;
        l = i3;
        int i4 = i2 % 2;
        int i5 = i3 + 5;
        i = i5 % 128;
        int i6 = i5 % 2;
        return "";
    }

    @Override // o.p.h
    public final void d(o.ei.c cVar) throws WalletValidationException {
        int i2 = l + 69;
        i = i2 % 128;
        int i3 = i2 % 2;
        if (!cVar.e().e().b(o.ei.a.b)) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Unexpected;
            Object[] objArr = new Object[1];
            C("᾽῭犰ﻸรᣲᘸ밸⼠쿱弐", (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            C("\ue0b4\ue0e0檌\ue6de肕쁝预擓퀘\ud7c7톿넬脐蓧\ue2ac拮牭琌㏌厕⍄┤䓿\u0cf9ᑉ\uea2d鐌ﷅ얭\udb55ꕀ꺣뚃衪\uf668龦极禆݀佒壮\u2e9a䡽㡡\u0984ᾤ饹\ue94d露쳖ꪍ\uda55ꨇ뷒ﮛ", (-1) - ImageFormat.getBitsPerPixel(0), objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
        if (this.n.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            C("䡴䠷潣\ue338䄮䝈夨\ue382", 1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr3);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
        }
        b_();
        int i4 = i + 11;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    @Override // o.p.h
    public final int a(o.ei.c cVar) {
        int i2 = l + 27;
        int i3 = i2 % 128;
        i = i3;
        int i4 = 0;
        switch (i2 % 2 == 0) {
            case false:
                i4 = 1;
                break;
        }
        int i5 = i3 + 87;
        l = i5 % 128;
        int i6 = i5 % 2;
        return i4;
    }

    /* JADX WARN: Code restructure failed: missing block: B:59:0x0022, code lost:
    
        if (r16 != null) goto L16;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void C(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 400
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.v.d.C(java.lang.String, int, java.lang.Object[]):void");
    }
}
