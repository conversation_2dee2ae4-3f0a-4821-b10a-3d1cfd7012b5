package o.dp;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.transaction.hce.HceTransactionStepName;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dp\d.smali */
public final class d {
    private final HceTransactionStepName a;
    private final int b;
    private static int e = 0;
    private static int c = 1;

    public final HceTransactionStepName c() {
        int i = e;
        int i2 = ((i | 69) << 1) - (i ^ 69);
        int i3 = i2 % 128;
        c = i3;
        int i4 = i2 % 2;
        HceTransactionStepName hceTransactionStepName = this.a;
        int i5 = i3 + Opcodes.LNEG;
        e = i5 % 128;
        int i6 = i5 % 2;
        return hceTransactionStepName;
    }

    public final int e() {
        int i = c;
        int i2 = (i & 3) + (i | 3);
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }
}
