package o.cr;

import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ct.b;
import o.fc.d;
import o.fg.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cr\a.smali */
public abstract class a<T extends o.fg.a> implements b<T> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static long b;
    private static char[] c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        c = new char[]{65010, 4793, 9036, 11424, 50169, 61969, 58038, 37321, 4897, 64626, 52634, 63296, 6163, 10747, 14643, 18972, 23466, 27470, 31983, 36313, 40229, 44736, 48675, 52992, 53435, 57357, 61927, 652, 4694, 9190, 13447, 17521, 21895, 25904, 30233, 34720, 38739, 47328, 51651, 55597, 60096, 64099, 2828};
        b = -121949474103639145L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r7 = r7 * 4
            int r7 = 3 - r7
            byte[] r0 = o.cr.a.$$a
            int r6 = 105 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L19:
            r3 = r2
        L1a:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L34:
            int r8 = -r8
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cr.a.g(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{38, -75, -91, -62};
        $$b = 209;
    }

    protected abstract T b(short s, int i, byte[] bArr);

    @Override // o.ct.b
    public final /* synthetic */ d b(o.eg.b bVar) throws o.eg.d {
        int i = e + 31;
        a = i % 128;
        switch (i % 2 != 0) {
            case true:
                c(bVar);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                T c2 = c(bVar);
                int i2 = a + Opcodes.DMUL;
                e = i2 % 128;
                switch (i2 % 2 == 0 ? '#' : (char) 26) {
                    case 26:
                        return c2;
                    default:
                        int i3 = 32 / 0;
                        return c2;
                }
        }
    }

    private T c(o.eg.b bVar) throws o.eg.d {
        int i = e + Opcodes.LREM;
        a = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f((char) (53594 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1, TextUtils.indexOf("", "") + 3, objArr);
        int intValue = bVar.i(((String) objArr[0]).intern()).intValue();
        Object[] objArr2 = new Object[1];
        f((char) (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 2, MotionEvent.axisFromString("") + 6, objArr2);
        short shortValue = bVar.k(((String) objArr2[0]).intern()).shortValue();
        Object[] objArr3 = new Object[1];
        f((char) (16256 - TextUtils.lastIndexOf("", '0', 0)), 8 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), TextUtils.getOffsetBefore("", 0) + 3, objArr3);
        byte[] B = bVar.B(((String) objArr3[0]).intern());
        if (B.length < 8) {
            Object[] objArr4 = new Object[1];
            f((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 56256), (Process.myPid() >> 22) + 11, ((byte) KeyEvent.getModifierMetaStateMask()) + 33, objArr4);
            throw new o.eg.d(((String) objArr4[0]).intern());
        }
        T b2 = b(shortValue, intValue, B);
        int i3 = a + 53;
        e = i3 % 128;
        int i4 = i3 % 2;
        return b2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 602
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cr.a.f(char, int, int, java.lang.Object[]):void");
    }
}
