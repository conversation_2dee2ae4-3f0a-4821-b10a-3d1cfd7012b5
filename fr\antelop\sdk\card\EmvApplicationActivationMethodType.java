package fr.antelop.sdk.card;

import o.co.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-******** (1)\smali_classes17\fr\antelop\sdk\card\EmvApplicationActivationMethodType.smali */
public enum EmvApplicationActivationMethodType {
    Sms(e.e.e()),
    Email(e.b.e()),
    OnlineBanking(e.c.e()),
    CustomerService(e.d.e()),
    App(e.a.e()),
    OutboundCall(e.i.e()),
    Ivr(e.g.e());

    private final boolean hasToSubmitActivationCode;

    EmvApplicationActivationMethodType(boolean z) {
        this.hasToSubmitActivationCode = z;
    }

    public final boolean hasToSubmitActivationCode() {
        return this.hasToSubmitActivationCode;
    }
}
