package fr.antelop.sdk.transaction.hce;

import fr.antelop.sdk.card.CardDisplay;
import java.math.BigDecimal;
import java.util.Currency;
import java.util.Date;
import o.dr.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\transaction\hce\HceTransaction.smali */
public final class HceTransaction {
    private final a innerTransaction;

    public HceTransaction(a aVar) {
        this.innerTransaction = aVar;
    }

    public final String getId() {
        return this.innerTransaction.e();
    }

    public final Currency getCurrency() {
        return this.innerTransaction.c();
    }

    public final BigDecimal getAmount() {
        return this.innerTransaction.d();
    }

    public final String getMerchantName() {
        return this.innerTransaction.a();
    }

    public final String getMerchantCategoryCode() {
        return this.innerTransaction.y();
    }

    public final double getLongitude() {
        return this.innerTransaction.i();
    }

    public final double getLatitude() {
        return this.innerTransaction.g();
    }

    public final boolean isGeolocated() {
        return this.innerTransaction.f();
    }

    public final String getCardLabel() {
        return this.innerTransaction.h();
    }

    public final String getCardBin() {
        return this.innerTransaction.j();
    }

    public final String getCardLastDigits() {
        return this.innerTransaction.o();
    }

    public final String getCardId() {
        return this.innerTransaction.m();
    }

    public final String getCardGraphicResource() {
        return this.innerTransaction.r();
    }

    public final String getEmvApplicationGroupLabel() {
        return this.innerTransaction.t();
    }

    public final String getEmvApplicationGroupId() {
        return this.innerTransaction.s();
    }

    public final String getEmvApplicationId() {
        return this.innerTransaction.u();
    }

    public final Date getDate() {
        return this.innerTransaction.q();
    }

    public final CardDisplay getCardDisplay() {
        return this.innerTransaction.N();
    }

    public final HceTransactionStatus getStatus() {
        return this.innerTransaction.B();
    }

    public final TransactionType getTransactionType() {
        return this.innerTransaction.C();
    }

    public final boolean isTransit() {
        return this.innerTransaction.G();
    }

    public final boolean isHighValueTransaction() {
        return this.innerTransaction.I();
    }

    public final boolean isOfflineTransaction() {
        return this.innerTransaction.E();
    }

    public final boolean isSignatureRequired() {
        return this.innerTransaction.J();
    }

    public final boolean isOnlinePinRequired() {
        return this.innerTransaction.H();
    }

    public final String getMerchantPostalCode() {
        return this.innerTransaction.v();
    }

    public final String getAccountNumber() {
        return this.innerTransaction.l();
    }

    public final String getIssuerAccountId() {
        return this.innerTransaction.k();
    }

    public final String getAccountLabel() {
        return this.innerTransaction.n();
    }

    public final String toString() {
        return new StringBuilder("HceTransaction{id=").append(getId()).append(", currency=").append(getCurrency()).append(", amount=").append(getAmount()).append(", merchantName=").append(getMerchantName()).append(", merchantCategoryCode").append(getMerchantCategoryCode() == null ? "" : getMerchantCategoryCode()).append(", longitude=").append(getLongitude()).append(", latitude=").append(getLatitude()).append(", isGeolocated=").append(isGeolocated()).append(", cardLabel=").append(getCardLabel()).append(", cardBin=").append(getCardBin()).append(", cardLastDigits=").append(getCardLastDigits()).append(", accountNumber=").append(getAccountNumber()).append(", issuerAccountId=").append(getIssuerAccountId()).append(", accountLabel=").append(getAccountLabel()).append(", cardId=").append(getCardId()).append(", emvApplicationGroupLabel=").append(getEmvApplicationGroupLabel()).append(", emvApplicationGroupId=").append(getEmvApplicationGroupId()).append(", date=").append(getDate()).append(", cardGraphicResource=").append(getCardGraphicResource()).append(", cardDisplay=").append(getCardDisplay()).append(", status=").append(getStatus()).append(", transactionType=").append(getTransactionType()).append(", typeTransit=").append(isTransit()).append(", isHighValueTransaction=").append(isHighValueTransaction()).append(", isOfflineTransaction=").append(isOfflineTransaction()).append(", isSignatureRequired=").append(isSignatureRequired()).append(", isOnlinePinRequired=").append(isOnlinePinRequired()).append(", merchantPostalCode=").append(getMerchantPostalCode() != null ? getMerchantPostalCode() : "").append('}').toString();
    }
}
