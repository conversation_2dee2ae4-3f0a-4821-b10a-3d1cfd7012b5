package o.p;

import android.content.Context;
import android.os.Process;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\c.smali */
public final class c implements o.b.a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static boolean f;
    private static boolean g;
    private static int h;
    private static int i;
    private static int j;
    private final d b;
    private final o.b.c c;
    private final g d;
    private final Context e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\c$d.smali */
    public interface d {
        void runWithWallet(Context context, o.ei.c cVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        b();
        ViewConfiguration.getScrollBarSize();
        int i2 = h + 69;
        j = i2 % 128;
        int i3 = i2 % 2;
    }

    static void b() {
        a = new char[]{61807, 61823, 61817, 61785, 61572, 61582, 61821, 61574, 61579, 61578, 61811, 61797, 61571, 61576, 61791, 61569, 61816, 61575, 61822, 61762, 61761, 61788, 61803, 61580, 61795};
        f = true;
        g = true;
        i = 782102810;
    }

    static void init$0() {
        $$a = new byte[]{30, 126, 60, -105};
        $$b = 26;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(byte r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 117
            int r8 = r8 * 2
            int r8 = r8 + 4
            byte[] r0 = o.p.c.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r6 = r7
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L34
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r8]
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = r7 + r8
            int r8 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.c.l(byte, short, short, java.lang.Object[]):void");
    }

    @Override // o.b.a
    public final void a() {
        int i2 = j + 9;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 6 : '.') {
            case 6:
                throw null;
            default:
                return;
        }
    }

    c(d dVar, Context context, g gVar) {
        this.b = dVar;
        this.e = context;
        this.d = gVar;
        this.c = new o.b.c(context);
    }

    @Override // o.b.a
    public final void c(o.ei.c cVar, o.bb.d dVar, o.bv.g gVar) {
        int i2 = h + 89;
        j = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k(null, 127 - View.resolveSize(0, 0), null, "\u0092\u0082\u0083\u0091\u0088\u0088\u0083\u008f\u0085\u0089\u0090\u0086\u0082\u0087\u0085\u0085\u0089\u008f\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, 127 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), null, "\u0095\u0094\u0093\u0087\u0086\u0082\u0087\u0085\u0085\u0089\u008f\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0085\u0089", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.b.runWithWallet(this.e, cVar);
        int i4 = h + 49;
        j = i4 % 128;
        int i5 = i4 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:16:0x0085, code lost:
    
        if (r10.d != null) goto L18;
     */
    @Override // o.b.a
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void d(o.bb.d r11) {
        /*
            r10 = this;
            int r0 = o.p.c.j
            int r0 = r0 + 99
            int r1 = r0 % 128
            o.p.c.h = r1
            int r0 = r0 % 2
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            java.lang.String r3 = "\u0095\u0094\u0093\u0087\u0088\u0090\u0083\u0096\u0085\u0089\u0090\u0086\u0082\u0087\u0085\u0085\u0089\u008f\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0085\u0089"
            java.lang.String r4 = "\u0092\u0082\u0083\u0091\u0088\u0088\u0083\u008f\u0085\u0089\u0090\u0086\u0082\u0087\u0085\u0085\u0089\u008f\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081"
            r5 = 0
            switch(r0) {
                case 0: goto L51;
                default: goto L19;
            }
        L19:
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r0 = r0 * 66
            int r0 = r0 + 8
            java.lang.Object[] r6 = new java.lang.Object[r1]
            k(r5, r0, r5, r4, r6)
            r0 = r6[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            long r6 = android.os.Process.getElapsedCpuTime()
            r8 = 1
            int r4 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            r6 = 3354(0xd1a, float:4.7E-42)
            int r6 = r6 % r4
            java.lang.Object[] r1 = new java.lang.Object[r1]
            k(r5, r6, r5, r3, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            o.p.g r0 = r10.d
            if (r0 == 0) goto L8b
            goto L88
        L51:
            o.ee.g.c()
            int r0 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r0 = r0 >> 16
            int r0 = r0 + 127
            java.lang.Object[] r6 = new java.lang.Object[r1]
            k(r5, r0, r5, r4, r6)
            r0 = r6[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            long r6 = android.os.Process.getElapsedCpuTime()
            r8 = 0
            int r4 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            int r4 = 128 - r4
            java.lang.Object[] r1 = new java.lang.Object[r1]
            k(r5, r4, r5, r3, r1)
            r1 = r1[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            o.p.g r0 = r10.d
            if (r0 == 0) goto L9a
        L87:
            goto L91
        L88:
            r0 = 38
            goto L8d
        L8b:
            r0 = 94
        L8d:
            switch(r0) {
                case 94: goto L9a;
                default: goto L90;
            }
        L90:
            goto L87
        L91:
            o.p.g r0 = r10.d
            o.bv.c r11 = o.bv.c.c(r11)
            r0.onError(r11)
        L9a:
            int r11 = o.p.c.h
            int r11 = r11 + 33
            int r0 = r11 % 128
            o.p.c.j = r0
            int r11 = r11 % 2
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.c.d(o.bb.d):void");
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0020. Please report as an issue. */
    @Override // o.b.a
    public final void d(o.cb.a aVar, o.g.b bVar, o.bb.d dVar) {
        g gVar = this.d;
        if (gVar != null) {
            gVar.onError(new o.bv.c(AntelopErrorCode.WalletNotActivated));
            int i2 = j + 51;
            h = i2 % 128;
            switch (i2 % 2 != 0 ? 'O' : ']') {
            }
        }
        int i3 = h + 35;
        j = i3 % 128;
        switch (i3 % 2 == 0 ? '8' : 'c') {
            case Opcodes.DADD /* 99 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.b.a
    public final void e() {
        int i2 = j + 19;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        k(null, TextUtils.indexOf((CharSequence) "", '0') + 128, null, "\u0092\u0082\u0083\u0091\u0088\u0088\u0083\u008f\u0085\u0089\u0090\u0086\u0082\u0087\u0085\u0085\u0089\u008f\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        k(null, (ViewConfiguration.getScrollBarSize() >> 8) + 127, null, "\u008d\u0085\u0090\u0089\u0099\u0085\u0097\u0085\u0089\u0090\u0086\u0083\u0098\u0090\u0086\u0082\u0084\u0087\u0085\u0090\u0088\u0085\u0097\u008e\u0087\u008d\u0083\u0085\u0083\u008c\u0086\u0087\u0088\u0088\u0083\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0085\u0089", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        g gVar = this.d;
        if (gVar != null) {
            gVar.onError(new o.bv.c(AntelopErrorCode.WalletNotActivated));
            int i4 = j + 7;
            h = i4 % 128;
            int i5 = i4 % 2;
        }
        this.c.a(this.e);
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v17, types: [byte[]] */
    private static void k(String str, int i2, int[] iArr, String str2, Object[] objArr) {
        char[] charArray;
        int length;
        char[] cArr;
        int i3;
        ?? r1 = str2;
        switch (r1 != 0 ? Typography.greater : 'K') {
            case '>':
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        switch (str != null ? '0' : ' ') {
            case '0':
                int i4 = $11 + 53;
                $10 = i4 % 128;
                int i5 = i4 % 2;
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr2 = charArray;
        o.a.j jVar = new o.a.j();
        char[] cArr3 = a;
        long j2 = 0;
        switch (cArr3 != null ? (char) 24 : 'a') {
            case 24:
                int i6 = $10 + Opcodes.LNEG;
                $11 = i6 % 128;
                if (i6 % 2 == 0) {
                    length = cArr3.length;
                    cArr = new char[length];
                    i3 = 1;
                } else {
                    length = cArr3.length;
                    cArr = new char[length];
                    i3 = 0;
                }
                while (i3 < length) {
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr3[i3])};
                        Object obj = o.e.a.s.get(1085633688);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) View.resolveSize(0, 0), 494 - (ViewConfiguration.getGlobalActionKeyTimeout() > j2 ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == j2 ? 0 : -1)));
                            byte length2 = (byte) $$a.length;
                            byte b = (byte) (length2 - 4);
                            Object[] objArr3 = new Object[1];
                            l(length2, b, b, objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1085633688, obj);
                        }
                        cArr[i3] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i3++;
                        j2 = 0;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr3 = cArr;
                break;
        }
        try {
            Object[] objArr4 = {Integer.valueOf(i)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) (8857 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), KeyEvent.normalizeMetaState(0) + 324);
                byte b2 = (byte) 1;
                byte b3 = (byte) (b2 - 1);
                Object[] objArr5 = new Object[1];
                l(b2, b3, b3, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            if (!g) {
                if (!f) {
                    jVar.e = iArr.length;
                    char[] cArr4 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr4[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i2] - intValue);
                        jVar.c++;
                    }
                    objArr[0] = new String(cArr4);
                    return;
                }
                jVar.e = cArr2.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr5[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i2] - intValue);
                    try {
                        Object[] objArr6 = {jVar, jVar};
                        Object obj3 = o.e.a.s.get(745816316);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getScrollBarSize() >> 8) + 10, (char) TextUtils.getOffsetAfter("", 0), ExpandableListView.getPackedPositionType(0L) + 207);
                            byte b4 = (byte) 0;
                            byte b5 = b4;
                            Object[] objArr7 = new Object[1];
                            l(b4, b5, b5, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(cArr5);
                return;
            }
            jVar.e = bArr.length;
            char[] cArr6 = new char[jVar.e];
            jVar.c = 0;
            while (jVar.c < jVar.e) {
                int i7 = $11 + 57;
                $10 = i7 % 128;
                if (i7 % 2 != 0) {
                    cArr6[jVar.c] = (char) (cArr3[bArr[(jVar.e << 1) >>> jVar.c] / i2] / intValue);
                    try {
                        Object[] objArr8 = {jVar, jVar};
                        Object obj4 = o.e.a.s.get(745816316);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(MotionEvent.axisFromString("") + 11, (char) (ViewConfiguration.getEdgeSlop() >> 16), TextUtils.indexOf((CharSequence) "", '0') + 208);
                            byte b6 = (byte) 0;
                            byte b7 = b6;
                            Object[] objArr9 = new Object[1];
                            l(b6, b7, b7, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } else {
                    cArr6[jVar.c] = (char) (cArr3[bArr[(jVar.e - 1) - jVar.c] + i2] - intValue);
                    try {
                        Object[] objArr10 = {jVar, jVar};
                        Object obj5 = o.e.a.s.get(745816316);
                        if (obj5 == null) {
                            Class cls5 = (Class) o.e.a.c(10 - View.resolveSize(0, 0), (char) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1), 206 - Process.getGidForName(""));
                            byte b8 = (byte) 0;
                            byte b9 = b8;
                            Object[] objArr11 = new Object[1];
                            l(b8, b9, b9, objArr11);
                            obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj5);
                        }
                        ((Method) obj5).invoke(null, objArr10);
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
            }
            objArr[0] = new String(cArr6);
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
