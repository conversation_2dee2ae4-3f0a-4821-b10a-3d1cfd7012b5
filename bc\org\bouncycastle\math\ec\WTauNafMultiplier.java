package bc.org.bouncycastle.math.ec;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPoint;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\WTauNafMultiplier.smali */
public class WTauNafMultiplier extends AbstractECMultiplier {

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\WTauNafMultiplier$a.smali */
    class a implements PreCompCallback {
        final /* synthetic */ ECPoint.AbstractF2m a;
        final /* synthetic */ byte b;

        a(ECPoint.AbstractF2m abstractF2m, byte b) {
            this.a = abstractF2m;
            this.b = b;
        }

        public PreCompInfo precompute(PreCompInfo preCompInfo) {
            if (preCompInfo instanceof WTauNafPreCompInfo) {
                return preCompInfo;
            }
            WTauNafPreCompInfo wTauNafPreCompInfo = new WTauNafPreCompInfo();
            wTauNafPreCompInfo.setPreComp(c.a(this.a, this.b));
            return wTauNafPreCompInfo;
        }
    }

    @Override // bc.org.bouncycastle.math.ec.AbstractECMultiplier
    protected ECPoint a(ECPoint eCPoint, BigInteger bigInteger) {
        if (!(eCPoint instanceof ECPoint.AbstractF2m)) {
            throw new IllegalArgumentException("Only ECPoint.AbstractF2m can be used in WTauNafMultiplier");
        }
        ECPoint.AbstractF2m abstractF2m = (ECPoint.AbstractF2m) eCPoint;
        ECCurve.AbstractF2m abstractF2m2 = (ECCurve.AbstractF2m) abstractF2m.getCurve();
        byte byteValue = abstractF2m2.getA().toBigInteger().byteValue();
        byte a2 = c.a(byteValue);
        return a(abstractF2m, c.a(abstractF2m2, bigInteger, byteValue, a2, (byte) 10), byteValue, a2);
    }

    private ECPoint.AbstractF2m a(ECPoint.AbstractF2m abstractF2m, e eVar, byte b, byte b2) {
        return a(abstractF2m, c.a(b2, eVar, 4, c.a(b2, 4).intValue(), b == 0 ? c.d : c.f));
    }

    private static ECPoint.AbstractF2m a(ECPoint.AbstractF2m abstractF2m, byte[] bArr) {
        ECCurve.AbstractF2m abstractF2m2 = (ECCurve.AbstractF2m) abstractF2m.getCurve();
        ECPoint.AbstractF2m[] preComp = ((WTauNafPreCompInfo) abstractF2m2.precompute(abstractF2m, "bc_wtnaf", new a(abstractF2m, abstractF2m2.getA().toBigInteger().byteValue()))).getPreComp();
        ECPoint.AbstractF2m[] abstractF2mArr = new ECPoint.AbstractF2m[preComp.length];
        for (int i = 0; i < preComp.length; i++) {
            abstractF2mArr[i] = (ECPoint.AbstractF2m) preComp[i].negate();
        }
        ECPoint.AbstractF2m abstractF2m3 = (ECPoint.AbstractF2m) abstractF2m.getCurve().getInfinity();
        int i2 = 0;
        for (int length = bArr.length - 1; length >= 0; length--) {
            i2++;
            byte b = bArr[length];
            if (b != 0) {
                abstractF2m3 = (ECPoint.AbstractF2m) abstractF2m3.tauPow(i2).add(b > 0 ? preComp[b >>> 1] : abstractF2mArr[(-b) >>> 1]);
                i2 = 0;
            }
        }
        return i2 > 0 ? abstractF2m3.tauPow(i2) : abstractF2m3;
    }
}
