package o.dw;

import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dw\j.smali */
public abstract class j {
    public static final byte[] $$g = null;
    public static final int $$h = 0;
    private static int $10;
    private static int $11;
    private static final String TAG;
    private static int a;
    private static int c;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        c = 1;
        d();
        Object[] objArr = new Object[1];
        k((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 2, "￼\ufff2\u0007\u0000\uffff\n￨\u0012\u0000\u0004\ufff1\u000f\u0000\u0007\u0007", TextUtils.indexOf("", "", 0, 0) + 15, ((Process.getThreadPriority(0) + 20) >> 6) + 208, true, objArr);
        TAG = ((String) objArr[0]).intern();
        int i = c + 51;
        a = i % 128;
        switch (i % 2 == 0 ? ')' : (char) 0) {
            case 0:
                throw null;
            default:
                return;
        }
    }

    static void d() {
        e = 874635284;
    }

    static void init$0() {
        $$g = new byte[]{57, -45, 96, 7};
        $$h = 83;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 * 2
            int r7 = r7 + 107
            int r6 = r6 + 4
            byte[] r0 = o.dw.j.$$g
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L36
        L1a:
            r3 = r2
        L1b:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.j.p(short, int, short, java.lang.Object[]):void");
    }

    public abstract void onDisplayCancelled();

    public abstract void onDisplayImpossible(o.bv.c cVar);

    public abstract void onDisplaySuccess();

    public final int hashCode() {
        int i = a + 35;
        c = i % 128;
        int i2 = i % 2;
        int hashCode = super.hashCode();
        int i3 = a + Opcodes.LSHL;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return hashCode;
        }
    }

    public final boolean equals(Object obj) {
        int i = c + Opcodes.LREM;
        a = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = a + Opcodes.LSHR;
        c = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 31 : '3') {
            case 31:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return equals;
        }
    }

    public final String toString() {
        int i = a + 19;
        c = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                super.toString();
                obj.hashCode();
                throw null;
            default:
                String obj2 = super.toString();
                int i2 = c + 47;
                a = i2 % 128;
                switch (i2 % 2 != 0 ? '^' : '_') {
                    case Opcodes.DUP2_X2 /* 94 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return obj2;
                }
        }
    }

    protected final void finalize() throws Throwable {
        int i = c + 23;
        a = i % 128;
        boolean z = i % 2 != 0;
        super.finalize();
        switch (z) {
            case true:
                int i2 = 10 / 0;
                break;
        }
        int i3 = a + 93;
        c = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r18, java.lang.String r19, int r20, int r21, boolean r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 518
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dw.j.k(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
