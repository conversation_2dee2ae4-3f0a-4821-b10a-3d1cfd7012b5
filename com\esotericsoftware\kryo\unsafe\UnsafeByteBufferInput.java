package com.esotericsoftware.kryo.unsafe;

import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.io.ByteBufferInput;
import java.io.InputStream;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import sun.misc.Unsafe;
import sun.nio.ch.DirectBuffer;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\unsafe\UnsafeByteBufferInput.smali */
public class UnsafeByteBufferInput extends ByteBufferInput {
    private long bufferAddress;

    public UnsafeByteBufferInput() {
    }

    public UnsafeByteBufferInput(int bufferSize) {
        super(bufferSize);
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(byte[] bytes) {
        super(bytes);
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(byte[] bytes, int offset, int count) {
        super(bytes, offset, count);
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(ByteBuffer buffer) {
        super(buffer);
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(long address, int size) {
        super(UnsafeUtil.newDirectBuffer(address, size));
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(InputStream inputStream) {
        super(inputStream);
        updateBufferAddress();
    }

    public UnsafeByteBufferInput(InputStream inputStream, int bufferSize) {
        super(inputStream, bufferSize);
        updateBufferAddress();
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput
    public void setBuffer(ByteBuffer buffer) {
        if (!(buffer instanceof DirectBuffer)) {
            throw new IllegalArgumentException("buffer must be direct.");
        }
        if (buffer != this.byteBuffer) {
            UnsafeUtil.dispose(this.byteBuffer);
        }
        super.setBuffer(buffer);
        updateBufferAddress();
    }

    private void updateBufferAddress() {
        this.bufferAddress = this.byteBuffer.address();
    }

    private void setBufferPosition(Buffer buffer, int position) {
        buffer.position(position);
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input, java.io.InputStream
    public int read() throws KryoException {
        if (optional(1) <= 0) {
            return -1;
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        int result = unsafe.getByte(j + i) & 255;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public byte readByte() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        byte result = unsafe.getByte(j + i);
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public int readByteUnsigned() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        int result = unsafe.getByte(j + i) & 255;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public int readInt() throws KryoException {
        require(4);
        int result = UnsafeUtil.unsafe.getInt(this.bufferAddress + this.position);
        this.position += 4;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public long readLong() throws KryoException {
        require(8);
        long result = UnsafeUtil.unsafe.getLong(this.bufferAddress + this.position);
        this.position += 8;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public float readFloat() throws KryoException {
        require(4);
        float result = UnsafeUtil.unsafe.getFloat(this.bufferAddress + this.position);
        this.position += 4;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public double readDouble() throws KryoException {
        require(8);
        double result = UnsafeUtil.unsafe.getDouble(this.bufferAddress + this.position);
        this.position += 8;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public short readShort() throws KryoException {
        require(2);
        short result = UnsafeUtil.unsafe.getShort(this.bufferAddress + this.position);
        this.position += 2;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public char readChar() throws KryoException {
        require(2);
        char result = UnsafeUtil.unsafe.getChar(this.bufferAddress + this.position);
        this.position += 2;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public boolean readBoolean() throws KryoException {
        if (this.position == this.limit) {
            require(1);
        }
        Unsafe unsafe = UnsafeUtil.unsafe;
        long j = this.bufferAddress;
        int i = this.position;
        this.position = i + 1;
        boolean result = unsafe.getByte(j + ((long) i)) != 0;
        setBufferPosition(this.byteBuffer, this.position);
        return result;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public int[] readInts(int length) throws KryoException {
        int[] array = new int[length];
        readBytes(array, UnsafeUtil.intArrayBaseOffset, length << 2);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public long[] readLongs(int length) throws KryoException {
        long[] array = new long[length];
        readBytes(array, UnsafeUtil.longArrayBaseOffset, length << 3);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public float[] readFloats(int length) throws KryoException {
        float[] array = new float[length];
        readBytes(array, UnsafeUtil.floatArrayBaseOffset, length << 2);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public double[] readDoubles(int length) throws KryoException {
        double[] array = new double[length];
        readBytes(array, UnsafeUtil.doubleArrayBaseOffset, length << 3);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public short[] readShorts(int length) throws KryoException {
        short[] array = new short[length];
        readBytes(array, UnsafeUtil.shortArrayBaseOffset, length << 1);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public char[] readChars(int length) throws KryoException {
        char[] array = new char[length];
        readBytes(array, UnsafeUtil.charArrayBaseOffset, length << 1);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public boolean[] readBooleans(int length) throws KryoException {
        boolean[] array = new boolean[length];
        readBytes(array, UnsafeUtil.booleanArrayBaseOffset, length);
        return array;
    }

    @Override // com.esotericsoftware.kryo.io.ByteBufferInput, com.esotericsoftware.kryo.io.Input
    public void readBytes(byte[] bytes, int offset, int count) throws KryoException {
        readBytes(bytes, UnsafeUtil.byteArrayBaseOffset + offset, count);
    }

    public void readBytes(Object to, long offset, int count) throws KryoException {
        int copyCount = Math.min(this.limit - this.position, count);
        while (true) {
            UnsafeUtil.unsafe.copyMemory((Object) null, this.bufferAddress + this.position, to, offset, copyCount);
            this.position += copyCount;
            count -= copyCount;
            if (count != 0) {
                offset += copyCount;
                copyCount = Math.min(count, this.capacity);
                require(copyCount);
            } else {
                setBufferPosition(this.byteBuffer, this.position);
                return;
            }
        }
    }
}
