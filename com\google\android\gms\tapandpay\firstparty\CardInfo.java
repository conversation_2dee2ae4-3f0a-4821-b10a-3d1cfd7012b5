package com.google.android.gms.tapandpay.firstparty;

import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.ReflectedParcelable;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;
import java.util.List;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\firstparty\CardInfo.smali */
public final class CardInfo extends AbstractSafeParcelable implements ReflectedParcelable {
    public static final Parcelable.Creator<CardInfo> CREATOR = new zzd();
    private static final com.google.android.gms.internal.tapandpay.zzau zzQ = com.google.android.gms.internal.tapandpay.zzau.zzi(10, 9);
    final boolean zzA;
    final long zzB;
    final long zzC;
    final boolean zzD;
    final long zzE;
    final String zzF;
    final String zzG;
    final zze zzH;
    final int zzI;
    final boolean zzJ;
    final String zzK;
    final int zzL;
    final boolean zzM;
    final long zzN;
    final String zzO;
    final int zzP;
    final String zza;
    final String zzb;
    final byte[] zzc;
    final String zzd;
    final String zze;
    final int zzf;
    final TokenStatus zzg;
    final String zzh;
    final Uri zzi;
    final int zzj;
    final int zzk;
    final zzaj zzl;
    final String zzm;
    final zzaz zzn;
    final String zzo;
    final byte[] zzp;
    final int zzq;
    final int zzr;
    final int zzs;
    final zzah zzt;
    final zzaf zzu;
    final String zzv;
    final zzan[] zzw;
    final boolean zzx;
    final List zzy;
    final boolean zzz;

    CardInfo(String str, String str2, byte[] bArr, String str3, String str4, int i, TokenStatus tokenStatus, String str5, Uri uri, int i2, int i3, zzaj zzajVar, String str6, zzaz zzazVar, String str7, byte[] bArr2, int i4, int i5, int i6, zzah zzahVar, zzaf zzafVar, String str8, zzan[] zzanVarArr, boolean z, List list, boolean z2, boolean z3, long j, long j2, boolean z4, long j3, String str9, String str10, zze zzeVar, int i7, boolean z5, String str11, int i8, boolean z6, long j4, String str12, int i9) {
        this.zza = str;
        this.zzb = str2;
        this.zzc = bArr;
        this.zzd = str3;
        this.zze = str4;
        this.zzf = i;
        this.zzg = tokenStatus;
        this.zzh = str5;
        this.zzi = uri;
        this.zzj = i2;
        this.zzk = i3;
        this.zzl = zzajVar;
        this.zzm = str6;
        this.zzn = zzazVar;
        this.zzo = str7;
        this.zzp = bArr2;
        this.zzq = i4;
        this.zzr = i5;
        this.zzs = i6;
        this.zzt = zzahVar;
        this.zzu = zzafVar;
        this.zzv = str8;
        this.zzw = zzanVarArr;
        this.zzx = z;
        this.zzy = list;
        this.zzz = z2;
        this.zzA = z3;
        this.zzB = j;
        this.zzC = j2;
        this.zzD = z4;
        this.zzE = j3;
        this.zzF = str9;
        this.zzG = str10;
        this.zzH = zzeVar;
        this.zzI = i7;
        this.zzJ = z5;
        this.zzK = str11;
        this.zzL = i8;
        this.zzM = z6;
        this.zzN = j4;
        this.zzO = str12;
        this.zzP = i9;
    }

    public final boolean equals(Object obj) {
        if (obj instanceof CardInfo) {
            CardInfo cardInfo = (CardInfo) obj;
            if (Objects.equal(this.zza, cardInfo.zza) && Objects.equal(this.zzb, cardInfo.zzb) && Arrays.equals(this.zzc, cardInfo.zzc) && Objects.equal(this.zzd, cardInfo.zzd) && Objects.equal(this.zze, cardInfo.zze) && this.zzf == cardInfo.zzf && Objects.equal(this.zzg, cardInfo.zzg) && Objects.equal(this.zzh, cardInfo.zzh) && Objects.equal(this.zzi, cardInfo.zzi) && this.zzj == cardInfo.zzj && this.zzk == cardInfo.zzk && Objects.equal(this.zzl, cardInfo.zzl) && Objects.equal(this.zzm, cardInfo.zzm) && Objects.equal(this.zzn, cardInfo.zzn) && this.zzq == cardInfo.zzq && this.zzr == cardInfo.zzr && this.zzs == cardInfo.zzs && Objects.equal(this.zzt, cardInfo.zzt) && Objects.equal(this.zzu, cardInfo.zzu) && Objects.equal(this.zzv, cardInfo.zzv) && Arrays.equals(this.zzw, cardInfo.zzw) && this.zzx == cardInfo.zzx && Objects.equal(this.zzy, cardInfo.zzy) && this.zzz == cardInfo.zzz && this.zzA == cardInfo.zzA && this.zzB == cardInfo.zzB && this.zzD == cardInfo.zzD && this.zzE == cardInfo.zzE && Objects.equal(this.zzF, cardInfo.zzF) && Objects.equal(this.zzG, cardInfo.zzG) && Objects.equal(this.zzH, cardInfo.zzH) && this.zzI == cardInfo.zzI && this.zzJ == cardInfo.zzJ && this.zzL == cardInfo.zzL && this.zzM == cardInfo.zzM && this.zzP == cardInfo.zzP && this.zzN == cardInfo.zzN && Objects.equal(this.zzO, cardInfo.zzO)) {
                return true;
            }
        }
        return false;
    }

    public final int hashCode() {
        return Objects.hashCode(this.zza, this.zzb, this.zzc, this.zzd, this.zze, Integer.valueOf(this.zzf), this.zzg, this.zzh, this.zzi, Integer.valueOf(this.zzj), Integer.valueOf(this.zzk), this.zzm, this.zzn, Integer.valueOf(this.zzq), Integer.valueOf(this.zzr), Integer.valueOf(this.zzs), this.zzt, this.zzu, this.zzv, this.zzw, Boolean.valueOf(this.zzx), this.zzy, Boolean.valueOf(this.zzz), Boolean.valueOf(this.zzA), Long.valueOf(this.zzB), Boolean.valueOf(this.zzD), Long.valueOf(this.zzE), this.zzF, this.zzG, this.zzH, Integer.valueOf(this.zzI), Boolean.valueOf(this.zzJ), Integer.valueOf(this.zzL), Boolean.valueOf(this.zzM), Long.valueOf(this.zzN), this.zzO, Integer.valueOf(this.zzP));
    }

    public final String toString() {
        Objects.ToStringHelper add = Objects.toStringHelper(this).add("billingCardId", this.zza).add("auxClientTokenId", this.zzb);
        byte[] bArr = this.zzc;
        Objects.ToStringHelper add2 = add.add("serverToken", bArr == null ? null : Arrays.toString(bArr)).add("cardholderName", this.zzd).add("displayName", this.zze).add("cardNetwork", Integer.valueOf(this.zzf)).add("tokenStatus", this.zzg).add("panLastDigits", this.zzh).add("cardImageUrl", this.zzi).add("cardColor", Integer.valueOf(this.zzj)).add("overlayTextColor", Integer.valueOf(this.zzk));
        zzaj zzajVar = this.zzl;
        Objects.ToStringHelper add3 = add2.add("issuerInfo", zzajVar == null ? null : zzajVar.toString()).add("tokenLastDigits", this.zzm).add("transactionInfo", this.zzn).add("issuerTokenId", this.zzo);
        byte[] bArr2 = this.zzp;
        Objects.ToStringHelper add4 = add3.add("inAppCardToken", bArr2 == null ? null : Arrays.toString(bArr2)).add("cachedEligibility", Integer.valueOf(this.zzq)).add("paymentProtocol", Integer.valueOf(this.zzr)).add("tokenType", Integer.valueOf(this.zzs)).add("inStoreCvmConfig", this.zzt).add("inAppCvmConfig", this.zzu).add("tokenDisplayName", this.zzv);
        zzan[] zzanVarArr = this.zzw;
        return add4.add("onlineAccountCardLinkInfos", zzanVarArr != null ? Arrays.toString(zzanVarArr) : null).add("allowAidSelection", Boolean.valueOf(this.zzx)).add("badges", "[" + TextUtils.join(", ", this.zzy) + "]").add("upgradeAvailable", Boolean.valueOf(this.zzz)).add("requiresSignature", Boolean.valueOf(this.zzA)).add("googleTokenId", Long.valueOf(this.zzB)).add("isTransit", Boolean.valueOf(this.zzD)).add("googleWalletId", Long.valueOf(this.zzE)).add("devicePaymentMethodId", this.zzF).add("cloudPaymentMethodId", this.zzG).add("auxiliaryGoogleTokenId", Long.valueOf(this.zzN)).add("auxiliaryIssuerTokenId", this.zzO).add("auxiliaryNetwork", Integer.valueOf(this.zzP)).toString();
    }

    @Override // android.os.Parcelable
    public final void writeToParcel(Parcel parcel, int i) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(parcel);
        SafeParcelWriter.writeString(parcel, 2, this.zza, false);
        SafeParcelWriter.writeByteArray(parcel, 3, this.zzc, false);
        SafeParcelWriter.writeString(parcel, 4, this.zzd, false);
        SafeParcelWriter.writeString(parcel, 5, this.zze, false);
        SafeParcelWriter.writeInt(parcel, 6, this.zzf);
        SafeParcelWriter.writeParcelable(parcel, 7, this.zzg, i, false);
        SafeParcelWriter.writeString(parcel, 8, this.zzh, false);
        SafeParcelWriter.writeParcelable(parcel, 9, this.zzi, i, false);
        SafeParcelWriter.writeInt(parcel, 10, this.zzj);
        SafeParcelWriter.writeInt(parcel, 11, this.zzk);
        SafeParcelWriter.writeParcelable(parcel, 12, this.zzl, i, false);
        SafeParcelWriter.writeString(parcel, 13, this.zzm, false);
        SafeParcelWriter.writeParcelable(parcel, 15, this.zzn, i, false);
        SafeParcelWriter.writeString(parcel, 16, this.zzo, false);
        SafeParcelWriter.writeByteArray(parcel, 17, this.zzp, false);
        SafeParcelWriter.writeInt(parcel, 18, this.zzq);
        SafeParcelWriter.writeInt(parcel, 20, this.zzr);
        SafeParcelWriter.writeInt(parcel, 21, this.zzs);
        SafeParcelWriter.writeParcelable(parcel, 22, this.zzt, i, false);
        SafeParcelWriter.writeParcelable(parcel, 23, this.zzu, i, false);
        SafeParcelWriter.writeString(parcel, 24, this.zzv, false);
        SafeParcelWriter.writeTypedArray(parcel, 25, this.zzw, i, false);
        SafeParcelWriter.writeBoolean(parcel, 26, this.zzx);
        SafeParcelWriter.writeTypedList(parcel, 27, this.zzy, false);
        SafeParcelWriter.writeBoolean(parcel, 28, this.zzz);
        SafeParcelWriter.writeBoolean(parcel, 29, this.zzA);
        SafeParcelWriter.writeLong(parcel, 30, this.zzB);
        SafeParcelWriter.writeLong(parcel, 31, this.zzC);
        SafeParcelWriter.writeBoolean(parcel, 32, this.zzD);
        SafeParcelWriter.writeLong(parcel, 33, this.zzE);
        SafeParcelWriter.writeString(parcel, 34, this.zzF, false);
        SafeParcelWriter.writeString(parcel, 35, this.zzG, false);
        SafeParcelWriter.writeParcelable(parcel, 36, this.zzH, i, false);
        SafeParcelWriter.writeInt(parcel, 37, this.zzI);
        SafeParcelWriter.writeBoolean(parcel, 38, this.zzJ);
        SafeParcelWriter.writeString(parcel, 39, this.zzK, false);
        SafeParcelWriter.writeInt(parcel, 40, this.zzL);
        SafeParcelWriter.writeBoolean(parcel, 41, this.zzM);
        SafeParcelWriter.writeLong(parcel, 42, this.zzN);
        SafeParcelWriter.writeString(parcel, 43, this.zzO, false);
        SafeParcelWriter.writeInt(parcel, 44, this.zzP);
        SafeParcelWriter.writeString(parcel, 45, this.zzb, false);
        SafeParcelWriter.finishObjectHeader(parcel, beginObjectHeader);
    }
}
