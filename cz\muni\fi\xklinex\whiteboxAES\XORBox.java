package cz.muni.fi.xklinex.whiteboxAES;

import java.io.Serializable;
import java.lang.reflect.Array;
import java.util.Arrays;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\cz\muni\fi\xklinex\whiteboxAES\XORBox.smali */
public class XORBox implements Serializable {
    public static final int BOXES = 8;
    public static final int WIDTH = 4;
    private static final long serialVersionUID = -5355889365547416446L;
    protected byte[][] xor = null;

    public XORBox() {
        init();
    }

    public boolean equals(Object obj) {
        if (obj != null && getClass() == obj.getClass()) {
            return Arrays.deepEquals(this.xor, ((XORBox) obj).xor);
        }
        return false;
    }

    public int hashCode() {
        return Arrays.deepHashCode(this.xor) + 371;
    }

    public final void init() {
        this.xor = (byte[][]) Array.newInstance((Class<?>) Byte.TYPE, 8, 256);
    }

    public long xor(long j, long j2) {
        return xor(this.xor, j, j2);
    }

    public static long xor(byte[][] bArr, long j, long j2) {
        long j3 = 0;
        for (int i = 0; i < 8; i++) {
            j3 |= bArr[i][(int) ((((j >>> r4) & 15) << 4) | (15 & (j2 >>> r4)))] << (i * 4);
        }
        return j3;
    }
}
