package com.getcapacitor.util;

import android.graphics.Color;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes8\com\getcapacitor\util\WebColor.smali */
public class WebColor {
    public static int parseColor(String colorString) {
        String formattedColor = colorString;
        if (colorString.charAt(0) != '#') {
            formattedColor = "#" + formattedColor;
        }
        if (formattedColor.length() != 7 && formattedColor.length() != 9) {
            throw new IllegalArgumentException("The encoded color space is invalid or unknown");
        }
        if (formattedColor.length() == 7) {
            return Color.parseColor(formattedColor);
        }
        return Color.parseColor("#" + formattedColor.substring(7) + formattedColor.substring(1, 7));
    }
}
