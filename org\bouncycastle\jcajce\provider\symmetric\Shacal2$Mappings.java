package org.bouncycastle.jcajce.provider.symmetric;

import org.bouncycastle.jcajce.provider.config.ConfigurableProvider;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\jcajce\provider\symmetric\Shacal2$Mappings.smali */
public class Shacal2$Mappings extends SymmetricAlgorithmProvider {
    private static final String PREFIX = Shacal2.class.getName();

    @Override // org.bouncycastle.jcajce.provider.util.AlgorithmProvider
    public void configure(ConfigurableProvider configurableProvider) {
        StringBuilder sb = new StringBuilder();
        String str = PREFIX;
        configurableProvider.addAlgorithm("Mac.Shacal-2CMAC", sb.append(str).append("$CMAC").toString());
        configurableProvider.addAlgorithm("Cipher.Shacal2", str + "$ECB");
        configurableProvider.addAlgorithm("Cipher.SHACAL-2", str + "$ECB");
        configurableProvider.addAlgorithm("KeyGenerator.Shacal2", str + "$KeyGen");
        configurableProvider.addAlgorithm("AlgorithmParameterGenerator.Shacal2", str + "$AlgParamGen");
        configurableProvider.addAlgorithm("AlgorithmParameters.Shacal2", str + "$AlgParams");
        configurableProvider.addAlgorithm("KeyGenerator.SHACAL-2", str + "$KeyGen");
        configurableProvider.addAlgorithm("AlgorithmParameterGenerator.SHACAL-2", str + "$AlgParamGen");
        configurableProvider.addAlgorithm("AlgorithmParameters.SHACAL-2", str + "$AlgParams");
    }
}
