package o.p;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.CancellationSignal;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationFailureReason;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPrompt;
import fr.antelop.sdk.authentication.prompt.CustomerAuthenticationPromptBuilder;
import java.lang.reflect.Method;
import kotlin.text.Typography;
import o.i.f;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\i.smali */
public final class i implements g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static byte[] g;
    private static int h;
    private static short[] i;
    private static int j;
    private static char[] k;
    private static int l;
    private static int m;
    private static boolean n;

    /* renamed from: o, reason: collision with root package name */
    private static boolean f97o;
    private static int q;
    private final DefaultCustomerAuthenticatedProcessCallback a;
    final Context b;
    private final h<?> c;
    final CustomerAuthenticatedProcess d;
    private CancellationSignal e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        q = 1;
        c();
        ExpandableListView.getPackedPositionForGroup(0);
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        ViewConfiguration.getScrollFriction();
        ViewConfiguration.getKeyRepeatDelay();
        View.combineMeasuredStates(0, 0);
        int i2 = q + 21;
        l = i2 % 128;
        int i3 = i2 % 2;
    }

    static void c() {
        g = new byte[]{34, -91, -65, PSSSigner.TRAILER_IMPLICIT, -77, -67, -88, -37, 109, -67, -85, -65, -79, -70, -33, -119, PSSSigner.TRAILER_IMPLICIT, -114, -48, -69, -73, UtilitiesSDKConstants.SRP_LABEL_MAC, -93, -90, -70, -79, PSSSigner.TRAILER_IMPLICIT, -15, 108, -86, -75, -69, -72, -66, -69, -49, 108, -91, -76, -47, -72, -66, -34, 111, -86, -76, -67, -62, 53, 51, -58, 54, 53, 48, 15, 84, 20, 49, 56, 7, 37, 48, 12, 7, 56, 59, 15, 6, 49, 70, -31, 63, 10, 48, 13, 51, 48, 68, 17, 10, 53, 6, 37, 2, -50, -127, -49, -2, -38, -18, -83, -51, -5, -49, -63, -54, -17, -81, -52, 30, -34, -8, -62, -52, -127, -50, -56, -53, 24, -94, -54, 12, -51, UtilitiesSDKConstants.SRP_LABEL_ENC, -53, -61, -15, -55, -11, -54, -19, -94, -53, -14, -63, -1, -54, -58, -63, -14, -11, -55, -64, -53, 0, -65, -53, 31, -66, 113, -95, -96, -69, -70, -33, -103, -65, -96, -86, -66};
        j = 909053678;
        f = 703487026;
        h = 2074747698;
        k = new char[]{61936, 61937, 61892, 61946, 61940, 61947, 61938, 61930, 61941, 61931, 61934, 61926, 61939, 61909, 61942, 61871, 61870, 61863, 61874, 61905, 61945, 61935, 61914, 61925, 61929, 61924, 61911, 61943, 61893, 61902, 61885, 61908};
        f97o = true;
        n = true;
        m = 782102919;
    }

    static void init$0() {
        $$a = new byte[]{24, -81, 39, 82};
        $$b = Opcodes.GETFIELD;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void s(short r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = 121 - r7
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = r6 * 3
            int r6 = r6 + 4
            byte[] r0 = o.p.i.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L34
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = -r6
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.i.s(short, byte, int, java.lang.Object[]):void");
    }

    public i(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback, CustomerAuthenticatedProcess customerAuthenticatedProcess, h<?> hVar) {
        this.b = context;
        this.a = defaultCustomerAuthenticatedProcessCallback;
        this.d = customerAuthenticatedProcess;
        this.c = hVar;
    }

    /* renamed from: o.p.i$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\i$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int a;
        private static int b;
        static final /* synthetic */ int[] c;
        static final /* synthetic */ int[] d;

        /* JADX WARN: Failed to find 'out' block for switch in B:7:0x0028. Please report as an issue. */
        static {
            b = 0;
            a = 1;
            int[] iArr = new int[f.values().length];
            c = iArr;
            try {
                iArr[f.c.ordinal()] = 1;
                int i = b + 45;
                a = i % 128;
                switch (i % 2 == 0 ? '+' : 'a') {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                c[f.e.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                c[f.d.ordinal()] = 3;
            } catch (NoSuchFieldError e3) {
            }
            try {
                c[f.b.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            try {
                c[f.a.ordinal()] = 5;
                int i2 = b + 49;
                a = i2 % 128;
                int i3 = i2 % 2;
            } catch (NoSuchFieldError e5) {
            }
            try {
                c[f.f.ordinal()] = 6;
                int i4 = a;
                int i5 = ((i4 | 65) << 1) - (i4 ^ 65);
                b = i5 % 128;
                int i6 = i5 % 2;
            } catch (NoSuchFieldError e6) {
            }
            int[] iArr2 = new int[CustomerAuthenticationFailureReason.values().length];
            d = iArr2;
            try {
                iArr2[CustomerAuthenticationFailureReason.Cancelled.ordinal()] = 1;
                int i7 = b;
                int i8 = (i7 ^ 3) + ((i7 & 3) << 1);
                a = i8 % 128;
                if (i8 % 2 == 0) {
                }
            } catch (NoSuchFieldError e7) {
            }
            try {
                d[CustomerAuthenticationFailureReason.InternalError.ordinal()] = 2;
            } catch (NoSuchFieldError e8) {
            }
            try {
                d[CustomerAuthenticationFailureReason.Timeout.ordinal()] = 3;
            } catch (NoSuchFieldError e9) {
            }
            try {
                d[CustomerAuthenticationFailureReason.Locked.ordinal()] = 4;
                int i9 = (b + 26) - 1;
                a = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e10) {
            }
            try {
                d[CustomerAuthenticationFailureReason.Unsupported.ordinal()] = 5;
            } catch (NoSuchFieldError e11) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.p.g
    public final void onCustomerCredentialsRequired(final java.util.List<o.i.g> r23) {
        /*
            Method dump skipped, instructions count: 894
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.i.onCustomerCredentialsRequired(java.util.List):void");
    }

    @Override // o.p.g
    public final void onCustomerCredentialsInvalid(o.g.b bVar) {
        this.e = null;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) TextUtils.getTrimmedLength(""), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 1300573602, (short) ((-46) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), (-128) - Process.getGidForName(""), (-532763225) - (Process.myPid() >> 22), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        r(null, 127 - TextUtils.getOffsetBefore("", 0), null, "\u0092\u009f\u0082\u0081\u0085\u008c\u0088\u008e\u0092\u0093\u0092\u0091\u0090\u008a\u008b\u008d\u008c\u0095\u0082\u009e\u0085\u008d\u008c\u008b\u0086\u0082\u0088\u008a\u0088\u0089\u0083\u0089\u0088\u0087\u0081\u0086\u0085\u0084\u0083\u0082\u0081", objArr2);
        o.ee.g.e(intern, sb.append(((String) objArr2[0]).intern()).append(bVar).toString());
        onError(new o.bv.c(AntelopErrorCode.InternalError));
        int i2 = l + 65;
        q = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    private CustomerAuthenticationPrompt b(f fVar, CustomerAuthenticationPromptBuilder customerAuthenticationPromptBuilder) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) Drawable.resolveOpacity(0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) - 1300573601, (short) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) - 45), (-127) - Color.alpha(0), (-532763225) + (ViewConfiguration.getEdgeSlop() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((byte) TextUtils.getCapsMode("", 0, 0), (-1300573554) - TextUtils.indexOf("", "", 0, 0), (short) (93 - Process.getGidForName("")), (-128) - TextUtils.lastIndexOf("", '0'), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 532763201, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback = this.a;
        switch (defaultCustomerAuthenticatedProcessCallback != null ? '\r' : 'M') {
            case 'M':
                CustomerAuthenticationPrompt build = customerAuthenticationPromptBuilder.build();
                int i2 = q + 91;
                l = i2 % 128;
                int i3 = i2 % 2;
                return build;
            default:
                CustomerAuthenticationPrompt buildCustomerAuthenticationPrompt = defaultCustomerAuthenticatedProcessCallback.buildCustomerAuthenticationPrompt(fVar.b(), customerAuthenticationPromptBuilder);
                int i4 = l + 3;
                q = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return buildCustomerAuthenticationPrompt;
                    default:
                        int i5 = 21 / 0;
                        return buildCustomerAuthenticationPrompt;
                }
        }
    }

    @Override // o.p.g
    public final void onProcessStart() {
        this.e = null;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) View.combineMeasuredStates(0, 0), (-1300573603) - MotionEvent.axisFromString(""), (short) ((-45) - (ViewConfiguration.getPressedStateDuration() >> 16)), (-127) - (ViewConfiguration.getKeyRepeatDelay() >> 16), AndroidCharacter.getMirror('0') - 21129, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((byte) (ViewConfiguration.getTapTimeout() >> 16), (-1300573519) - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (short) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 94), AndroidCharacter.getMirror('0') - 175, ((byte) KeyEvent.getModifierMetaStateMask()) - 532763186, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback = this.a;
        switch (defaultCustomerAuthenticatedProcessCallback == null) {
            case false:
                int i2 = l + Opcodes.DMUL;
                q = i2 % 128;
                int i3 = i2 % 2;
                defaultCustomerAuthenticatedProcessCallback.onProcessStart(this.d);
                break;
        }
        int i4 = q + Opcodes.DSUB;
        l = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 15 : '-') {
            case 15:
                int i5 = 73 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.p.g
    public final void onProcessSuccess() {
        int i2 = q + 27;
        l = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) (ViewConfiguration.getMinimumFlingVelocity() >> 16), (-1300573603) + (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 46), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 128, (-532763225) - TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        r(null, 127 - View.combineMeasuredStates(0, 0), null, "\u0091\u0090\u0085\u0085\u0088\u009a\u009a\u0084 \u0085\u0085\u0088\u009a\u0081\u0089\u009b\u0082\u0081", objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback = this.a;
        switch (defaultCustomerAuthenticatedProcessCallback != null) {
            case true:
                defaultCustomerAuthenticatedProcessCallback.onProcessSuccess(this.d);
                int i4 = q + 57;
                l = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
    }

    @Override // o.p.g
    public final void onError(o.bv.c cVar) {
        this.e = null;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) ExpandableListView.getPackedPositionGroup(0L), (-1300573601) - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (short) ((-45) - ExpandableListView.getPackedPositionType(0L)), ((Process.getThreadPriority(0) + 20) >> 6) - 127, (-532763225) - TextUtils.getTrimmedLength(""), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        p((byte) (ViewConfiguration.getJumpTapTimeout() >> 16), TextUtils.getCapsMode("", 0, 0) - 1300573503, (short) ((-90) - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), View.resolveSize(0, 0) - 127, TextUtils.getOffsetBefore("", 0) - 532763187, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(cVar).toString());
        this.c.a(this.b, cVar);
        DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback = this.a;
        switch (defaultCustomerAuthenticatedProcessCallback == null) {
            case false:
                int i2 = q + 73;
                l = i2 % 128;
                int i3 = i2 % 2;
                defaultCustomerAuthenticatedProcessCallback.onError(cVar.d(), this.d);
                int i4 = q + Opcodes.DREM;
                l = i4 % 128;
                if (i4 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    @Override // o.p.g
    public final void onAuthenticationDeclined() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (-1300573602) - View.resolveSizeAndState(0, 0, 0), (short) (TextUtils.lastIndexOf("", '0', 0) - 44), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 127, (-532763224) + ExpandableListView.getPackedPositionChild(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((byte) ((-1) - MotionEvent.axisFromString("")), (ViewConfiguration.getKeyRepeatTimeout() >> 16) - 1300573491, (short) (View.resolveSize(0, 0) - 92), (-126) - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), ((byte) KeyEvent.getModifierMetaStateMask()) - 532763186, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback = this.a;
        switch (defaultCustomerAuthenticatedProcessCallback != null ? '\n' : (char) 23) {
            case 23:
                break;
            default:
                int i2 = q + 93;
                l = i2 % 128;
                int i3 = i2 % 2;
                defaultCustomerAuthenticatedProcessCallback.onAuthenticationDeclined(this.d);
                break;
        }
        int i4 = l + 11;
        q = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 21 : '\b') {
            case 21:
                throw null;
            default:
                return;
        }
    }

    @Override // o.p.g
    public final void abortPrompt() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        p((byte) View.combineMeasuredStates(0, 0), (-1300573602) - (ViewConfiguration.getWindowTouchSlop() >> 8), (short) ((-45) - (ViewConfiguration.getJumpTapTimeout() >> 16)), (-127) - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (Process.myPid() >> 22) - 532763225, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((byte) (ImageFormat.getBitsPerPixel(0) + 1), KeyEvent.getDeadChar(0, 0) - 1300573465, (short) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) - 45), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) - 127, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) - 532763201, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        CancellationSignal cancellationSignal = this.e;
        switch (cancellationSignal != null ? 'P' : Typography.quote) {
            case '\"':
                break;
            default:
                int i2 = l + 73;
                q = i2 % 128;
                int i3 = i2 % 2;
                cancellationSignal.cancel();
                int i4 = q + Opcodes.LSUB;
                l = i4 % 128;
                if (i4 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:85:0x02ac. Please report as an issue. */
    private static void p(byte b, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        int i5;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(j)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c(KeyEvent.keyCodeFromString("") + 11, (char) (Process.getGidForName("") + 1), TextUtils.lastIndexOf("", '0', 0, 0) + 66);
                byte b2 = (byte) 0;
                Object[] objArr3 = new Object[1];
                s(b2, (byte) (b2 | 13), b2, objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            Object obj2 = null;
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            if (intValue == -1) {
                z = true;
            } else {
                int i6 = $10 + 87;
                $11 = i6 % 128;
                int i7 = i6 % 2;
                z = false;
            }
            if (z) {
                int i8 = $10 + Opcodes.LMUL;
                $11 = i8 % 128;
                if (i8 % 2 == 0) {
                    obj2.hashCode();
                    throw null;
                }
                byte[] bArr = g;
                switch (bArr == null) {
                    case false:
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        for (int i9 = 0; i9 < length; i9++) {
                            try {
                                Object[] objArr4 = {Integer.valueOf(bArr[i9])};
                                Object obj3 = o.e.a.s.get(494867332);
                                if (obj3 == null) {
                                    Class cls2 = (Class) o.e.a.c((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 18, (char) (16426 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 150 - (ViewConfiguration.getMaximumFlingVelocity() >> 16));
                                    byte b3 = (byte) 0;
                                    Object[] objArr5 = new Object[1];
                                    s(b3, (byte) (b3 | 11), b3, objArr5);
                                    obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                    o.e.a.s.put(494867332, obj3);
                                }
                                bArr2[i9] = ((Byte) ((Method) obj3).invoke(null, objArr4)).byteValue();
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        bArr = bArr2;
                    default:
                        switch (bArr == null) {
                            case false:
                                byte[] bArr3 = g;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(i2), Integer.valueOf(h)};
                                    Object obj4 = o.e.a.s.get(-2120899312);
                                    if (obj4 == null) {
                                        Class cls3 = (Class) o.e.a.c((ViewConfiguration.getFadingEdgeLength() >> 16) + 11, (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), View.resolveSizeAndState(0, 0, 0) + 65);
                                        byte b4 = (byte) 0;
                                        Object[] objArr7 = new Object[1];
                                        s(b4, (byte) (b4 | 13), b4, objArr7);
                                        obj4 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(-2120899312, obj4);
                                    }
                                    intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj4).invoke(null, objArr6)).intValue()] ^ (-5810760824076169584L))) + ((int) (j ^ (-5810760824076169584L))));
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                intValue = (short) (((short) (i[i2 + ((int) (h ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (j ^ (-5810760824076169584L))));
                                break;
                        }
                }
            }
            if (intValue > 0) {
                int i10 = $10;
                int i11 = i10 + 59;
                $11 = i11 % 128;
                int i12 = i11 % 2;
                int i13 = ((i2 + intValue) - 2) + ((int) (h ^ (-5810760824076169584L)));
                if (z) {
                    int i14 = i10 + 23;
                    $11 = i14 % 128;
                    int i15 = i14 % 2;
                    i5 = 1;
                } else {
                    i5 = 0;
                }
                fVar.d = i13 + i5;
                try {
                    Object[] objArr8 = {fVar, Integer.valueOf(i4), Integer.valueOf(f), sb};
                    Object obj5 = o.e.a.s.get(160906762);
                    if (obj5 == null) {
                        obj5 = ((Class) o.e.a.c((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 11, (char) View.MeasureSpec.getMode(0), 603 - Drawable.resolveOpacity(0, 0))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                        o.e.a.s.put(160906762, obj5);
                    }
                    ((StringBuilder) ((Method) obj5).invoke(null, objArr8)).append(fVar.e);
                    fVar.b = fVar.e;
                    byte[] bArr4 = g;
                    if (bArr4 != null) {
                        int length2 = bArr4.length;
                        byte[] bArr5 = new byte[length2];
                        for (int i16 = 0; i16 < length2; i16++) {
                            int i17 = $10 + 71;
                            $11 = i17 % 128;
                            int i18 = i17 % 2;
                            bArr5[i16] = (byte) (bArr4[i16] ^ (-5810760824076169584L));
                        }
                        bArr4 = bArr5;
                    }
                    boolean z2 = bArr4 != null;
                    fVar.c = 1;
                    while (true) {
                        switch (fVar.c >= intValue) {
                            case true:
                                break;
                            default:
                                int i19 = $11;
                                int i20 = i19 + Opcodes.DREM;
                                $10 = i20 % 128;
                                int i21 = i20 % 2;
                                switch (z2 ? (char) 21 : '1') {
                                    case '1':
                                        short[] sArr = i;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                        break;
                                    default:
                                        int i22 = i19 + 41;
                                        $10 = i22 % 128;
                                        if (i22 % 2 != 0) {
                                        }
                                        byte[] bArr6 = g;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r8] ^ (-5810760824076169584L))) + s)) ^ b));
                                        break;
                                }
                                sb.append(fVar.e);
                                fVar.b = fVar.e;
                                fVar.c++;
                        }
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            objArr[0] = sb.toString();
        } catch (Throwable th4) {
            Throwable cause4 = th4.getCause();
            if (cause4 == null) {
                throw th4;
            }
            throw cause4;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void r(java.lang.String r19, int r20, int[] r21, java.lang.String r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 876
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.i.r(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
