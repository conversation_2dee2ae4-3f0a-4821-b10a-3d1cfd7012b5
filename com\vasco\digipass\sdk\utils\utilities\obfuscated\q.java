package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.ByteArrayInputStream;
import java.io.EOFException;
import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\q.smali */
public class q extends FilterInputStream {
    private final byte[][] C;
    private final int b;
    private final boolean x;

    public q(InputStream inputStream) {
        this(inputStream, m7.a(inputStream));
    }

    int a() {
        return this.b;
    }

    protected int b() throws IOException {
        return a((InputStream) this, this.b, false);
    }

    public b0 c() throws IOException {
        int read = read();
        if (read <= 0) {
            if (read != 0) {
                return null;
            }
            throw new IOException("unexpected end-of-contents marker");
        }
        int a = a(this, read);
        int b = b();
        if (b >= 0) {
            try {
                return a(read, a, b);
            } catch (IllegalArgumentException e) {
                throw new k("corrupted stream detected", e);
            }
        }
        if ((read & 32) == 0) {
            throw new IOException("indefinite-length primitive encoding encountered");
        }
        g0 g0Var = new g0(new c5(this, this.b), this.b, this.C);
        int i = read & 192;
        if (i != 0) {
            return g0Var.a(i, a);
        }
        if (a == 3) {
            return v0.a(g0Var);
        }
        if (a == 4) {
            return y0.a(g0Var);
        }
        if (a == 8) {
            return y1.a(g0Var);
        }
        if (a == 16) {
            return a1.a(g0Var);
        }
        if (a == 17) {
            return c1.a(g0Var);
        }
        throw new IOException("unknown BER object encountered");
    }

    i d() throws IOException {
        b0 c = c();
        if (c == null) {
            return new i(0);
        }
        i iVar = new i();
        do {
            iVar.a(c);
            c = c();
        } while (c != null);
        return iVar;
    }

    public q(byte[] bArr) {
        this(new ByteArrayInputStream(bArr), bArr.length);
    }

    protected b0 a(int i, int i2, int i3) throws IOException {
        x3 x3Var = new x3(this, i3, this.b);
        if ((i & BERTags.FLAGS) == 0) {
            return a(i2, x3Var, this.C);
        }
        int i4 = i & 192;
        if (i4 != 0) {
            return a(i4, i2, (i & 32) != 0, x3Var);
        }
        if (i2 == 3) {
            return a(b(x3Var));
        }
        if (i2 == 4) {
            return b(b(x3Var));
        }
        if (i2 == 8) {
            return b3.a(b(x3Var)).l();
        }
        if (i2 == 16) {
            return x3Var.b() < 1 ? b3.a : this.x ? new i5(x3Var.c()) : b3.a(b(x3Var));
        }
        if (i2 == 17) {
            return b3.b(b(x3Var));
        }
        throw new IOException("unknown tag " + i2 + " encountered");
    }

    x b(i iVar) throws IOException {
        int b = iVar.b();
        x[] xVarArr = new x[b];
        for (int i = 0; i != b; i++) {
            h a = iVar.a(i);
            if (!(a instanceof x)) {
                throw new k("unknown object encountered in constructed OCTET STRING: " + a.getClass());
            }
            xVarArr[i] = (x) a;
        }
        return new x0(xVarArr);
    }

    public q(byte[] bArr, boolean z) {
        this(new ByteArrayInputStream(bArr), bArr.length, z);
    }

    public q(InputStream inputStream, int i) {
        this(inputStream, i, false);
    }

    public q(InputStream inputStream, int i, boolean z) {
        this(inputStream, i, z, new byte[11][]);
    }

    private q(InputStream inputStream, int i, boolean z, byte[][] bArr) {
        super(inputStream);
        this.b = i;
        this.x = z;
        this.C = bArr;
    }

    i b(x3 x3Var) throws IOException {
        int b = x3Var.b();
        if (b < 1) {
            return new i(0);
        }
        return new q(x3Var, b, this.x, this.C).d();
    }

    d a(i iVar) throws IOException {
        int b = iVar.b();
        d[] dVarArr = new d[b];
        for (int i = 0; i != b; i++) {
            h a = iVar.a(i);
            if (a instanceof d) {
                dVarArr[i] = (d) a;
            } else {
                throw new k("unknown object encountered in constructed BIT STRING: " + a.getClass());
            }
        }
        return new u0(dVarArr);
    }

    b0 a(int i, int i2, boolean z, x3 x3Var) throws IOException {
        if (!z) {
            return j0.a(i, i2, x3Var.c());
        }
        return j0.a(i, i2, b(x3Var));
    }

    static int a(InputStream inputStream, int i) throws IOException {
        int i2 = i & 31;
        if (i2 != 31) {
            return i2;
        }
        int read = inputStream.read();
        if (read < 31) {
            if (read < 0) {
                throw new EOFException("EOF found inside tag value.");
            }
            throw new IOException("corrupted stream - high tag number < 31 found");
        }
        int i3 = read & 127;
        if (i3 == 0) {
            throw new IOException("corrupted stream - invalid high tag number found");
        }
        while ((read & 128) != 0) {
            if ((i3 >>> 24) == 0) {
                int i4 = i3 << 7;
                int read2 = inputStream.read();
                if (read2 < 0) {
                    throw new EOFException("EOF found inside tag value.");
                }
                i3 = i4 | (read2 & 127);
                read = read2;
            } else {
                throw new IOException("Tag number more than 31 bits");
            }
        }
        return i3;
    }

    static int a(InputStream inputStream, int i, boolean z) throws IOException {
        int read = inputStream.read();
        if ((read >>> 7) == 0) {
            return read;
        }
        if (128 == read) {
            return -1;
        }
        if (read < 0) {
            throw new EOFException("EOF found when length expected");
        }
        if (255 == read) {
            throw new IOException("invalid long form definite-length 0xFF");
        }
        int i2 = read & 127;
        int i3 = 0;
        int i4 = 0;
        do {
            int read2 = inputStream.read();
            if (read2 < 0) {
                throw new EOFException("EOF found reading length");
            }
            if ((i3 >>> 23) != 0) {
                throw new IOException("long form definite-length more than 31 bits");
            }
            i3 = (i3 << 8) + read2;
            i4++;
        } while (i4 < i2);
        if (i3 < i || z) {
            return i3;
        }
        throw new IOException("corrupted stream - out of bounds length found: " + i3 + " >= " + i);
    }

    private static byte[] a(x3 x3Var, byte[][] bArr) throws IOException {
        int b = x3Var.b();
        if (b >= bArr.length) {
            return x3Var.c();
        }
        byte[] bArr2 = bArr[b];
        if (bArr2 == null) {
            bArr2 = new byte[b];
            bArr[b] = bArr2;
        }
        x3Var.a(bArr2);
        return bArr2;
    }

    private static char[] a(x3 x3Var) throws IOException {
        int i;
        int b = x3Var.b();
        if ((b & 1) == 0) {
            int i2 = b / 2;
            char[] cArr = new char[i2];
            byte[] bArr = new byte[8];
            int i3 = 0;
            int i4 = 0;
            while (b >= 8) {
                if (n7.a(x3Var, bArr, 0, 8) == 8) {
                    cArr[i4] = (char) ((bArr[0] << 8) | (bArr[1] & 255));
                    cArr[i4 + 1] = (char) ((bArr[2] << 8) | (bArr[3] & 255));
                    cArr[i4 + 2] = (char) ((bArr[4] << 8) | (bArr[5] & 255));
                    cArr[i4 + 3] = (char) ((bArr[6] << 8) | (bArr[7] & 255));
                    i4 += 4;
                    b -= 8;
                } else {
                    throw new EOFException("EOF encountered in middle of BMPString");
                }
            }
            if (b > 0) {
                if (n7.a(x3Var, bArr, 0, b) != b) {
                    throw new EOFException("EOF encountered in middle of BMPString");
                }
                while (true) {
                    int i5 = i3 + 1;
                    int i6 = i5 + 1;
                    i = i4 + 1;
                    cArr[i4] = (char) ((bArr[i3] << 8) | (bArr[i5] & 255));
                    if (i6 >= b) {
                        break;
                    }
                    i3 = i6;
                    i4 = i;
                }
                i4 = i;
            }
            if (x3Var.b() == 0 && i2 == i4) {
                return cArr;
            }
            throw new IllegalStateException();
        }
        throw new IOException("malformed BMPString encoding encountered");
    }

    static b0 a(int i, x3 x3Var, byte[][] bArr) throws IOException {
        try {
            switch (i) {
                case 1:
                    return f.b(a(x3Var, bArr));
                case 2:
                    return r.b(x3Var.c());
                case 3:
                    return d.b(x3Var.c());
                case 4:
                    return x.b(x3Var.c());
                case 5:
                    return s.b(x3Var.c());
                case 6:
                    return w.a(a(x3Var, bArr), true);
                case 7:
                    return v.b(x3Var.c());
                case 8:
                case 9:
                case 11:
                case 15:
                case 16:
                case 17:
                case 29:
                default:
                    throw new IOException("unknown tag " + i + " encountered");
                case 10:
                    return j.a(a(x3Var, bArr), true);
                case 12:
                    return m0.b(x3Var.c());
                case 13:
                    return d0.a(x3Var.c(), false);
                case 14:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                    throw new IOException("unsupported tag " + i + " encountered");
                case 18:
                    return t.b(x3Var.c());
                case 19:
                    return c0.b(x3Var.c());
                case 20:
                    return h0.b(x3Var.c());
                case 21:
                    return q0.b(x3Var.c());
                case 22:
                    return p.b(x3Var.c());
                case 23:
                    return l0.b(x3Var.c());
                case 24:
                    return n.b(x3Var.c());
                case 25:
                    return o.b(x3Var.c());
                case 26:
                    return r0.b(x3Var.c());
                case 27:
                    return m.b(x3Var.c());
                case 28:
                    return n0.b(x3Var.c());
                case 30:
                    return c.a(a(x3Var));
            }
        } catch (IllegalArgumentException e) {
            throw new k(e.getMessage(), e);
        } catch (IllegalStateException e2) {
            throw new k(e2.getMessage(), e2);
        }
    }
}
