package o.u;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\u\b.smali */
public final class b extends e {
    private static b c;
    private static int d = 0;
    private static int a = 1;

    private b(Context context) {
        super(context);
    }

    public static synchronized b a(Context context) {
        b bVar;
        synchronized (b.class) {
            int i = a;
            int i2 = (i & Opcodes.DREM) + (i | Opcodes.DREM);
            d = i2 % 128;
            switch (i2 % 2 != 0 ? 'S' : ' ') {
                case Opcodes.AASTORE /* 83 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    switch (c == null ? Typography.less : (char) 3) {
                        case '<':
                            c(context);
                            int i3 = d;
                            int i4 = (i3 ^ 29) + ((i3 & 29) << 1);
                            a = i4 % 128;
                            switch (i4 % 2 == 0 ? '\'' : '(') {
                            }
                    }
                    bVar = c;
                    int i5 = d + 57;
                    a = i5 % 128;
                    int i6 = i5 % 2;
                    break;
            }
        }
        return bVar;
    }

    private static synchronized void c(Context context) {
        synchronized (b.class) {
            c = new b(context);
            int i = a + Opcodes.DDIV;
            d = i % 128;
            int i2 = i % 2;
        }
    }
}
