package o.cq;

import android.view.KeyEvent;
import android.view.View;
import com.esotericsoftware.asm.Opcodes;
import o.et.h;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cq\b.smali */
public final class b extends e<h> {
    private static char a;
    private static int b = 0;
    private static int c;
    private static long d;
    private static int e;

    static {
        e = 1;
        d();
        View.MeasureSpec.getMode(0);
        KeyEvent.getModifierMetaStateMask();
        int i = b + 83;
        e = i % 128;
        switch (i % 2 == 0 ? '\t' : (char) 11) {
            case '\t':
                throw null;
            default:
                return;
        }
    }

    static void d() {
        a = (char) 46675;
        c = 161105445;
        d = 6565854932352255525L;
    }

    @Override // o.cq.e
    public final h a(String str, String str2, int i, String str3) {
        h hVar = new h(str, str2, i, str3);
        int i2 = e + Opcodes.DREM;
        b = i2 % 128;
        int i3 = i2 % 2;
        return hVar;
    }
}
