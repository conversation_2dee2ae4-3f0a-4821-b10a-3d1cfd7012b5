package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.asn1.x9.X962NamedCurves;
import bc.org.bouncycastle.asn1.x9.X9ECParameters;
import java.util.Enumeration;
import java.util.Vector;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c4.smali */
public class c4 {
    public static X9ECParameters a(String str) {
        X9ECParameters byName = X962NamedCurves.getByName(str);
        if (byName == null) {
            byName = a7.c(str);
        }
        if (byName == null) {
            byName = p5.a(str);
        }
        if (byName == null) {
            byName = q7.c(str);
        }
        if (byName == null) {
            byName = a.c(str);
        }
        if (byName == null) {
            byName = a4.d(str);
        }
        return byName == null ? v4.c(str) : byName;
    }

    public static e8 b(String str) {
        e8 byNameLazy = X962NamedCurves.getByNameLazy(str);
        if (byNameLazy == null) {
            byNameLazy = a7.d(str);
        }
        if (byNameLazy == null) {
            byNameLazy = p5.b(str);
        }
        if (byNameLazy == null) {
            byNameLazy = q7.d(str);
        }
        if (byNameLazy == null) {
            byNameLazy = a.d(str);
        }
        if (byNameLazy == null) {
            byNameLazy = a4.c(str);
        }
        return byNameLazy == null ? v4.d(str) : byNameLazy;
    }

    public static X9ECParameters a(w wVar) {
        X9ECParameters byOID = X962NamedCurves.getByOID(wVar);
        if (byOID == null) {
            byOID = a7.a(wVar);
        }
        if (byOID == null) {
            byOID = q7.a(wVar);
        }
        if (byOID == null) {
            byOID = a.a(wVar);
        }
        if (byOID == null) {
            byOID = a4.b(wVar);
        }
        return byOID == null ? v4.a(wVar) : byOID;
    }

    public static Enumeration a() {
        Vector vector = new Vector();
        a(vector, X962NamedCurves.getNames());
        a(vector, a7.a());
        a(vector, p5.a());
        a(vector, q7.a());
        a(vector, a.a());
        a(vector, a4.a());
        a(vector, v4.a());
        return vector.elements();
    }

    private static void a(Vector vector, Enumeration enumeration) {
        while (enumeration.hasMoreElements()) {
            vector.addElement(enumeration.nextElement());
        }
    }
}
