package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\z5.smali */
public abstract class z5 {
    public static void a(long[] jArr, long[] jArr2) {
        jArr2[0] = jArr[0];
        jArr2[1] = jArr[1];
        jArr2[2] = jArr[2];
        jArr2[3] = jArr[3];
        jArr2[4] = jArr[4];
        jArr2[5] = jArr[5];
        jArr2[6] = jArr[6];
    }

    public static long[] b() {
        return new long[14];
    }

    public static BigInteger c(long[] jArr) {
        byte[] bArr = new byte[56];
        for (int i = 0; i < 7; i++) {
            long j = jArr[i];
            if (j != 0) {
                j6.a(j, bArr, (6 - i) << 3);
            }
        }
        return new BigInteger(1, bArr);
    }

    public static boolean b(long[] jArr, long[] jArr2) {
        for (int i = 6; i >= 0; i--) {
            if (jArr[i] != jArr2[i]) {
                return false;
            }
        }
        return true;
    }

    public static boolean b(long[] jArr) {
        for (int i = 0; i < 7; i++) {
            if (jArr[i] != 0) {
                return false;
            }
        }
        return true;
    }

    public static void a(long[] jArr, int i, long[] jArr2, int i2) {
        jArr2[i2 + 0] = jArr[i + 0];
        jArr2[i2 + 1] = jArr[i + 1];
        jArr2[i2 + 2] = jArr[i + 2];
        jArr2[i2 + 3] = jArr[i + 3];
        jArr2[i2 + 4] = jArr[i + 4];
        jArr2[i2 + 5] = jArr[i + 5];
        jArr2[i2 + 6] = jArr[i + 6];
    }

    public static long[] a() {
        return new long[7];
    }

    public static boolean a(long[] jArr) {
        if (jArr[0] != 1) {
            return false;
        }
        for (int i = 1; i < 7; i++) {
            if (jArr[i] != 0) {
                return false;
            }
        }
        return true;
    }

    public static void a(int[] iArr, int[] iArr2, int[] iArr3) {
        int c;
        v5.c(iArr, iArr2, iArr3);
        v5.b(iArr, 7, iArr2, 7, iArr3, 14);
        int a = v5.a(iArr3, 7, iArr3, 14);
        int a2 = a + v5.a(iArr3, 21, iArr3, 14, v5.a(iArr3, 0, iArr3, 7, 0) + a);
        int[] a3 = v5.a();
        int[] a4 = v5.a();
        boolean z = v5.a(iArr, 7, iArr, 0, a3, 0) != v5.a(iArr2, 7, iArr2, 0, a4, 0);
        int[] b = v5.b();
        v5.c(a3, a4, b);
        if (!z) {
            c = c6.c(14, b, 0, iArr3, 7);
        } else {
            c = c6.a(14, b, 0, iArr3, 7);
        }
        c6.a(28, a2 + c, iArr3, 21);
    }

    public static void a(int[] iArr, int[] iArr2) {
        v5.d(iArr, iArr2);
        v5.d(iArr, 7, iArr2, 14);
        int a = v5.a(iArr2, 7, iArr2, 14);
        int a2 = a + v5.a(iArr2, 21, iArr2, 14, v5.a(iArr2, 0, iArr2, 7, 0) + a);
        int[] a3 = v5.a();
        v5.a(iArr, 7, iArr, 0, a3, 0);
        int[] b = v5.b();
        v5.d(a3, b);
        c6.a(28, a2 + c6.c(14, b, 0, iArr2, 7), iArr2, 21);
    }
}
