package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.obfuscated.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\GenericResponse.smali */
public class GenericResponse extends DigipassResponse {
    private int c;
    private byte[] d;
    private int e;

    public GenericResponse(int i) {
        super(i);
    }

    public int getAttemptLeft() {
        return this.e;
    }

    public byte[] getDynamicVector() {
        return q.c(this.d);
    }

    public int getStatus() {
        return this.c;
    }

    public void setDynamicVector(byte[] bArr) {
        this.d = bArr;
    }

    public GenericResponse(int i, Throwable th) {
        super(i, th);
    }

    public GenericResponse(int i, int i2, byte[] bArr) {
        this(i, i2, bArr, 0);
    }

    public GenericResponse(int i, int i2, byte[] bArr, int i3) {
        super(i);
        this.c = i2;
        this.d = q.c(bArr);
        this.e = i3;
    }
}
