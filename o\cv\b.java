package o.cv;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.eg.d;
import o.fc.c;
import o.fd.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cv\b.smali */
public final class b implements o.ct.b<a> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int c;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        c = 1;
        a();
        Color.red(0);
        MotionEvent.axisFromString("");
        TypedValue.complexToFloat(0);
        int i = b + 59;
        c = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        a = 874635375;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(byte r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r9 = r9 * 4
            int r9 = 1 - r9
            int r7 = r7 * 2
            int r7 = 109 - r7
            byte[] r0 = o.cv.b.$$a
            int r8 = r8 + 4
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L31
        L17:
            r3 = r2
        L18:
            int r8 = r8 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L29
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L29:
            r3 = r0[r8]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L31:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cv.b.g(byte, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{62, -87, 120, -83};
        $$b = Opcodes.I2L;
    }

    @Override // o.ct.b
    public final /* synthetic */ a b(o.eg.b bVar) throws d {
        int i = c + 29;
        b = i % 128;
        int i2 = i % 2;
        a d = d(bVar);
        int i3 = b + 19;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                int i4 = 7 / 0;
                return d;
            default:
                return d;
        }
    }

    private static a d(o.eg.b bVar) throws d {
        Object[] objArr = new Object[1];
        f((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "\ufff8\b", TextUtils.indexOf("", "", 0) + 2, (ViewConfiguration.getScrollDefaultDelay() >> 16) + Opcodes.LSHR, false, objArr);
        int intValue = bVar.i(((String) objArr[0]).intern()).intValue();
        Object[] objArr2 = new Object[1];
        f(1 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), "\u0003\t\n\u0003￦\u000e\ufff7", 7 - View.resolveSize(0, 0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + Opcodes.LSHL, true, objArr2);
        int intValue2 = bVar.i(((String) objArr2[0]).intern()).intValue();
        Object[] objArr3 = new Object[1];
        f(KeyEvent.getDeadChar(0, 0) + 1, "\r\ufffe\u0003\ufff9\ufffa", ((Process.getThreadPriority(0) + 20) >> 6) + 5, 123 - (ViewConfiguration.getPressedStateDuration() >> 16), false, objArr3);
        a aVar = new a(true, c.b, bVar.k(((String) objArr3[0]).intern()).shortValue());
        aVar.b(intValue);
        aVar.a(0);
        aVar.e(intValue2);
        int i = c + 7;
        b = i % 128;
        switch (i % 2 != 0) {
            case true:
                throw null;
            default:
                return aVar;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r19, java.lang.String r20, int r21, int r22, boolean r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 526
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cv.b.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
