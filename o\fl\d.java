package o.fl;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fl\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    static boolean a;
    private static String b;
    private static final Object c;
    private static final List<String> d;
    private static Thread e;
    private static int f;
    private static long g;
    private static char[] h;
    private static int i;

    static void b() {
        h = new char[]{11439, 34980, 25817, 49397, 48347, 6182, 62486, 20604, 3166, 59822, 17873, 8701, 40403, 31022, 54558, 45428, 27990, 51894, 42639, 743, 65216, 23086, 13834, 37474, 20051, 11183, 34717, 25589, 57330, 47929, 5891, 62313, 44891, 2899, 57523, 23723, 14565, 38080, 28726, 11286, 34932, 25676, 49594, 48530, 6654, 62943, 20776, 11429, 35001, 25744};
        g = 4743383970012760278L;
    }

    static void init$0() {
        $$a = new byte[]{20, -38, -101, -62};
        $$b = Opcodes.FDIV;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Type inference failed for: r8v1, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(byte r6, short r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = 105 - r8
            int r7 = r7 * 3
            int r7 = 3 - r7
            byte[] r0 = o.fl.d.$$a
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L15
            r4 = r8
            r3 = r2
            r8 = r7
            goto L2c
        L15:
            r3 = r2
        L16:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r6) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r5
        L2c:
            int r7 = r7 + r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fl.d.k(byte, short, byte, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        i = 1;
        b();
        d = Arrays.asList("A", "B");
        a = false;
        b = null;
        c = new Object();
        int i2 = f + 37;
        i = i2 % 128;
        switch (i2 % 2 == 0 ? 'I' : Typography.amp) {
            case 'I':
                int i3 = 22 / 0;
                return;
            default:
                return;
        }
    }

    public static void a(Context context) {
        boolean z;
        long j;
        if (b != null && a) {
            g.c();
            g.d("LogManager", "redirectLoggingToFile - Already logging");
            return;
        }
        synchronized (c) {
            File file = new File(context.getFilesDir(), "logs");
            if (!file.exists() && !file.mkdir()) {
                g.c();
                g.e("LogManager", new StringBuilder("redirectLoggingToFile - Unable to create folder ").append(file.getAbsolutePath()).toString());
                return;
            }
            int i2 = 1;
            if (b == null) {
                long j2 = 0;
                Object[] objArr = new Object[1];
                j((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), ViewConfiguration.getKeyRepeatDelay() >> 16, 48 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
                String string = context.getSharedPreferences(((String) objArr[0]).intern(), 0).getString("LoggingFileName", null);
                b = string;
                if (string == null) {
                    g.c();
                    g.d("LogManager", "redirectLoggingToFile - No last file written reference stored, checking last file access");
                    File[] listFiles = file.listFiles();
                    if (listFiles != null) {
                        int length = listFiles.length;
                        int i3 = 0;
                        long j3 = 0;
                        while (i3 < length) {
                            File file2 = listFiles[i3];
                            String replace = file2.getName().replace(".txt", "");
                            if (replace.length() <= i2) {
                                j = j2;
                            } else {
                                Object[] objArr2 = new Object[i2];
                                j((char) View.resolveSizeAndState(0, 0, 0), 47 - (ViewConfiguration.getScrollBarSize() >> 8), Color.blue(0) + 3, objArr2);
                                String replace2 = replace.replace(((String) objArr2[0]).intern(), "");
                                if (!d.contains(replace2)) {
                                    j = 0;
                                } else {
                                    long lastModified = file2.lastModified();
                                    g.c();
                                    g.d("LogManager", new StringBuilder("redirectLoggingToFile - File retrieved : ").append(replace).append(" - id : ").append(replace2).append(" - last access : ").append(lastModified).toString());
                                    j = 0;
                                    if (j3 == 0 || lastModified > j3) {
                                        g.c();
                                        g.d("LogManager", "redirectLoggingToFile - Next file to write updated");
                                        b = replace2;
                                        j3 = lastModified;
                                    }
                                }
                            }
                            i3++;
                            j2 = j;
                            i2 = 1;
                        }
                    }
                    if (b == null) {
                        b = d.get(0);
                    }
                }
            }
            String c2 = c();
            File file3 = new File(file, c2);
            if (!file3.exists()) {
                try {
                    if (!file3.createNewFile()) {
                        g.c();
                        g.e("LogManager", new StringBuilder("redirectLoggingToFile - Unable to create file ").append(file3.getAbsolutePath()).toString());
                        return;
                    } else {
                        g.c();
                        g.d("LogManager", "redirectLoggingToFile - File created");
                    }
                } catch (IOException e2) {
                    g.c();
                    g.a("LogManager", new StringBuilder("redirectLoggingToFile - Unable to create file ").append(file3.getAbsolutePath()).toString(), e2);
                    return;
                }
            }
            if (file3.length() < 20000000) {
                g.c();
                g.d("LogManager", new StringBuilder("redirectLoggingToFile - File : ").append(c2).append(" is not too large for logging, using this file").toString());
                z = false;
            } else {
                g.c();
                g.d("LogManager", new StringBuilder("redirectLoggingToFile - File : ").append(c2).append(" is too large for logging, changing logging file").toString());
                List<String> list = d;
                int indexOf = list.indexOf(b);
                if (indexOf == list.size() - 1) {
                    g.c();
                    g.d("LogManager", "redirectLoggingToFile - Switching to file list start");
                    b = list.get(0);
                } else {
                    g.c();
                    g.d("LogManager", "redirectLoggingToFile - Switching to the next file on the list");
                    b = list.get(indexOf + 1);
                }
                String c3 = c();
                g.c();
                g.d("LogManager", "redirectLoggingToFile - New logging file : ".concat(String.valueOf(c3)));
                file3 = new File(file, c3);
                if (!file3.exists()) {
                    try {
                        if (!file3.createNewFile()) {
                            g.c();
                            g.e("LogManager", new StringBuilder("redirectLoggingToFile - Unable to create file ").append(file3.getAbsolutePath()).toString());
                            return;
                        }
                        g.c();
                        g.d("LogManager", "redirectLoggingToFile - file created");
                        Object[] objArr3 = new Object[1];
                        j((char) TextUtils.getOffsetAfter("", 0), TextUtils.indexOf("", "", 0, 0), 46 - TextUtils.lastIndexOf("", '0', 0), objArr3);
                        context.getSharedPreferences(((String) objArr3[0]).intern(), 0).edit().putString("LoggingFileName", b).apply();
                        z = true;
                    } catch (IOException e3) {
                        g.c();
                        g.a("LogManager", new StringBuilder("redirectLoggingToFile - Unable to create file ").append(file3.getAbsolutePath()).toString(), e3);
                        return;
                    }
                } else {
                    g.c();
                    g.d("LogManager", "redirectLoggingToFile - File flushed");
                    try {
                        PrintWriter printWriter = new PrintWriter(file3);
                        printWriter.print("");
                        printWriter.close();
                        Object[] objArr32 = new Object[1];
                        j((char) TextUtils.getOffsetAfter("", 0), TextUtils.indexOf("", "", 0, 0), 46 - TextUtils.lastIndexOf("", '0', 0), objArr32);
                        context.getSharedPreferences(((String) objArr32[0]).intern(), 0).edit().putString("LoggingFileName", b).apply();
                        z = true;
                    } catch (FileNotFoundException e4) {
                        g.c();
                        g.a("LogManager", new StringBuilder("redirectLoggingToFile - Unable to flush file ").append(file3.getAbsolutePath()).toString(), e4);
                        return;
                    }
                }
            }
            if (a) {
                g.c();
                g.d("LogManager", "logging process is active");
                if (z) {
                    g.c();
                    g.d("LogManager", "stopping existing logging process and starting new logging process to the new defined file");
                    Thread thread = e;
                    if (thread != null) {
                        thread.interrupt();
                    }
                    e(file3);
                } else {
                    g.c();
                    g.d("LogManager", "keep using the same logging process");
                }
            } else {
                g.c();
                g.d("LogManager", "logging process is not active : setting up logging");
                e(file3);
            }
        }
    }

    private static void e(final File file) {
        try {
            g.c();
            g.d("LogManager", "redirectLogToFile - file header printing");
            Runtime.getRuntime().exec(new String[]{"/system/bin/sh", "-c", new StringBuilder("echo Starting Logging... >> ").append(file.getAbsolutePath()).toString()}).waitFor();
            g.c();
            g.d("LogManager", "redirectLogToFile - clearing logcat");
            Runtime.getRuntime().exec("logcat -c").waitFor();
            e = new Thread() { // from class: o.fl.d.1
                /* JADX WARN: Code restructure failed: missing block: B:17:0x0060, code lost:
                
                    if (r3 == null) goto L6;
                 */
                @Override // java.lang.Thread, java.lang.Runnable
                /*
                    Code decompiled incorrectly, please refer to instructions dump.
                    To view partially-correct add '--show-bad-code' argument
                */
                public final void run() {
                    /*
                        r5 = this;
                        java.lang.String r0 = "LogManager"
                        r1 = 1
                        r2 = 0
                        r3 = 0
                        o.fl.d.a = r1     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.StringBuilder r1 = new java.lang.StringBuilder     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r4 = "logcat -f "
                        r1.<init>(r4)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.io.File r4 = r1     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r4 = r4.getAbsolutePath()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.StringBuilder r1 = r1.append(r4)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r4 = " &"
                        java.lang.StringBuilder r1 = r1.append(r4)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r1 = r1.toString()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.Runtime r4 = java.lang.Runtime.getRuntime()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.Process r3 = r4.exec(r1)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        o.ee.g.c()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r1 = "redirectLogToFile - Logging process started"
                        o.ee.g.d(r0, r1)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        r3.waitFor()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        o.fl.d.a = r2     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        o.ee.g.c()     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        java.lang.String r1 = "redirectLogToFile - Logging process ended"
                        o.ee.g.d(r0, r1)     // Catch: java.lang.InterruptedException -> L47 java.io.IOException -> L49 java.lang.Throwable -> L4b
                        if (r3 == 0) goto L44
                    L41:
                        r3.destroy()
                    L44:
                        o.fl.d.a = r2
                        return
                    L47:
                        r1 = move-exception
                        goto L4d
                    L49:
                        r1 = move-exception
                        goto L58
                    L4b:
                        r0 = move-exception
                        goto L63
                    L4d:
                        o.ee.g.c()     // Catch: java.lang.Throwable -> L4b
                        java.lang.String r1 = "redirectLogToFile - Interrupted Exception when monitoring logs"
                        o.ee.g.d(r0, r1)     // Catch: java.lang.Throwable -> L4b
                        if (r3 == 0) goto L44
                        goto L41
                    L58:
                        o.ee.g.c()     // Catch: java.lang.Throwable -> L4b
                        java.lang.String r1 = "redirectLogToFile - IO Exception when monitoring logs"
                        o.ee.g.d(r0, r1)     // Catch: java.lang.Throwable -> L4b
                        if (r3 == 0) goto L44
                        goto L41
                    L63:
                        if (r3 == 0) goto L68
                        r3.destroy()
                    L68:
                        o.fl.d.a = r2
                        throw r0
                    */
                    throw new UnsupportedOperationException("Method not decompiled: o.fl.d.AnonymousClass1.run():void");
                }
            };
            g.c();
            g.d("LogManager", "redirectLogToFile - launching logging thread");
            e.start();
            int i2 = i + 93;
            f = i2 % 128;
            int i3 = i2 % 2;
        } catch (IOException e2) {
            g.c();
            g.d("LogManager", "redirectLogToFile - IOException when executing logging instructions");
        } catch (InterruptedException e3) {
            g.c();
            g.d("LogManager", "redirectLogToFile - InterruptedException when executing logging instructions");
        }
    }

    private static String c() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        j((char) (ViewConfiguration.getFadingEdgeLength() >> 16), 47 - View.resolveSizeAndState(0, 0, 0), 3 - (Process.myTid() >> 22), objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(b).append(".txt").toString();
        int i2 = i + 89;
        f = i2 % 128;
        int i3 = i2 % 2;
        return obj;
    }

    /* JADX WARN: Removed duplicated region for block: B:2:0x000d A[LOOP_START] */
    /* JADX WARN: Removed duplicated region for block: B:54:0x01b1  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void j(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 716
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.fl.d.j(char, int, int, java.lang.Object[]):void");
    }
}
