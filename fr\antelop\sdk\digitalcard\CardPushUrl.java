package fr.antelop.sdk.digitalcard;

import o.an.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\CardPushUrl.smali */
public final class CardPushUrl {
    private final e.d innerEcommerceTokenRequestorResponse;

    public CardPushUrl(e.d dVar) {
        this.innerEcommerceTokenRequestorResponse = dVar;
    }

    public final String getInAppUrl() {
        return this.innerEcommerceTokenRequestorResponse.d();
    }

    public final String getWebUrl() {
        return this.innerEcommerceTokenRequestorResponse.c();
    }
}
