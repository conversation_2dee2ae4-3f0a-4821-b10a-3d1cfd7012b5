package fr.antelop.sdk.ui.prompt;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import fr.antelop.sdk.R;
import o.n.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\ui\prompt\AntelopKeypadView.smali */
public final class AntelopKeypadView extends a {
    private final Theming theming;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\ui\prompt\AntelopKeypadView$KeypadCallback.smali */
    public interface KeypadCallback {
        void onExtraButtonPressed();

        void onKeyPressed();

        void onPasscodeEntryDone(byte[] bArr);
    }

    public AntelopKeypadView(Context context, String str, boolean z, boolean z2, int i, int i2, int i3, int i4, int i5, int i6) {
        super(context);
        this.theming = new Theming(str, z, z2, i, i2, context.getResources().getColor(i3), context.getResources().getColor(i4), i5, i6, Boolean.valueOf(context.getResources().getBoolean(R.bool.antelopKeypadViewDefaultShowAlpha)));
    }

    public AntelopKeypadView(Context context) {
        super(context);
        this.theming = init(context, null);
    }

    public AntelopKeypadView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        this.theming = init(context, attributeSet);
    }

    public AntelopKeypadView(Context context, AttributeSet attributeSet, int i) {
        super(context, attributeSet, i);
        this.theming = init(context, attributeSet);
    }

    private Theming init(Context context, AttributeSet attributeSet) {
        String str;
        boolean z;
        boolean z2;
        int i;
        int i2;
        int i3;
        int i4;
        int i5;
        int i6;
        String string = context.getResources().getString(R.string.antelopKeypadViewDefaultOverlayWarningMessage);
        boolean z3 = context.getResources().getBoolean(R.bool.antelopKeypadViewDefaultEnableOverlayProtection);
        boolean z4 = context.getResources().getBoolean(R.bool.antelopKeypadViewDefaultRandomizeKeyboard);
        int integer = context.getResources().getInteger(R.integer.antelopKeypadViewDefaultPinLength);
        int i7 = R.drawable.antelopKeypadViewDefaultBulletIcon;
        int color = context.getResources().getColor(R.color.antelopKeypadViewDefaultColorPrimary);
        int color2 = context.getResources().getColor(R.color.antelopKeypadViewDefaultColorSecondary);
        int i8 = R.drawable.antelopKeypadViewDefaultDeleteButtonIcon;
        boolean z5 = context.getResources().getBoolean(R.bool.antelopKeypadViewDefaultShowAlpha);
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = context.getTheme().obtainStyledAttributes(attributeSet, R.styleable.AntelopKeypadView, 0, 0);
            String string2 = obtainStyledAttributes.getString(R.styleable.AntelopKeypadView_antelopKeypadViewOverlayWarningMessage);
            if (string2 != null) {
                string = string2;
            }
            boolean z6 = obtainStyledAttributes.getBoolean(R.styleable.AntelopKeypadView_antelopKeypadViewEnableOverlayProtection, z3);
            boolean z7 = obtainStyledAttributes.getBoolean(R.styleable.AntelopKeypadView_antelopKeypadViewRandomizeKeyboard, z4);
            int i9 = obtainStyledAttributes.getInt(R.styleable.AntelopKeypadView_antelopKeypadViewPinLength, integer);
            int resourceId = obtainStyledAttributes.getResourceId(R.styleable.AntelopKeypadView_antelopKeypadViewBulletDrawable, i7);
            int color3 = obtainStyledAttributes.getColor(R.styleable.AntelopKeypadView_antelopKeypadViewColorPrimary, color);
            int color4 = obtainStyledAttributes.getColor(R.styleable.AntelopKeypadView_antelopKeypadViewColorSecondary, color2);
            int resourceId2 = obtainStyledAttributes.getResourceId(R.styleable.AntelopKeypadView_antelopKeypadViewDeleteDrawable, i8);
            int resourceId3 = obtainStyledAttributes.getResourceId(R.styleable.AntelopKeypadView_antelopKeypadViewExtraDrawable, 0);
            obtainStyledAttributes.recycle();
            str = string;
            z = z6;
            z2 = z7;
            i = i9;
            i2 = resourceId;
            i3 = color3;
            i4 = color4;
            i5 = resourceId2;
            i6 = resourceId3;
        } else {
            str = string;
            z = z3;
            z2 = z4;
            i = integer;
            i2 = i7;
            i3 = color;
            i4 = color2;
            i5 = i8;
            i6 = 0;
        }
        return new Theming(str, z, z2, i, i2, i3, i4, i5, i6, Boolean.valueOf(z5));
    }

    public final void initializeView(final KeypadCallback keypadCallback) {
        if (this.theming.enableOverlayProtection) {
            enableOverlayProtection(this.theming.overlayWarningMessage);
        }
        super.initializeView(new a.InterfaceC0045a() { // from class: fr.antelop.sdk.ui.prompt.AntelopKeypadView.1
            @Override // o.n.a.InterfaceC0045a
            public void onPasscodeEntryDone(byte[] bArr) {
                KeypadCallback keypadCallback2 = keypadCallback;
                if (keypadCallback2 != null) {
                    keypadCallback2.onPasscodeEntryDone(bArr);
                }
            }

            @Override // o.n.a.InterfaceC0045a
            public void onKeyPressed() {
                KeypadCallback keypadCallback2 = keypadCallback;
                if (keypadCallback2 != null) {
                    keypadCallback2.onKeyPressed();
                }
            }

            @Override // o.n.a.InterfaceC0045a
            public void onExtraButtonPressed() {
                KeypadCallback keypadCallback2 = keypadCallback;
                if (keypadCallback2 != null) {
                    keypadCallback2.onExtraButtonPressed();
                }
            }
        }, this.theming);
        if (this.theming.randomizeKeyboard) {
            randomize();
        }
    }

    public final void randomize() {
        super.scramble();
    }

    @Override // o.n.a
    public final void resetPasscode() {
        super.resetPasscode();
    }

    @Override // o.dz.a
    public final void enableOverlayProtection(String str) {
        super.enableOverlayProtection(str);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\ui\prompt\AntelopKeypadView$Theming.smali */
    static final class Theming extends a.d {
        final boolean enableOverlayProtection;
        final String overlayWarningMessage;
        final boolean randomizeKeyboard;

        Theming(String str, boolean z, boolean z2, int i, int i2, int i3, int i4, int i5, int i6, Boolean bool) {
            super(i, i2, i3, i4, R.style.antelopKeypadViewKeyboardDigitStyle, R.style.antelopKeypadViewKeyboardAlphaStyle, R.style.antelopKeypadViewKeyboardBackgroundStyle, i5, i6, bool.booleanValue());
            this.overlayWarningMessage = str;
            this.enableOverlayProtection = z;
            this.randomizeKeyboard = z2;
        }
    }
}
