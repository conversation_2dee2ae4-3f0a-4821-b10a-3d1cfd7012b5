package o.as;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.bb.e;
import o.cf.i;
import o.ee.g;
import o.eo.j;
import o.h.d;
import o.y.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\as\c.smali */
public final class c extends o.y.b<b> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static char[] f;
    private static boolean g;
    private static boolean h;
    private static int i;
    private static int j;
    private static int m;
    String a;
    String b;
    d c;
    String d;
    j e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\as\c$b.smali */
    public interface b {
        void a(String str);

        void d(o.bb.d dVar);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        m = 1;
        k();
        KeyEvent.normalizeMetaState(0);
        ViewConfiguration.getTapTimeout();
        int i2 = m + 67;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void init$0() {
        $$d = new byte[]{115, -89, 96, 25};
        $$e = 228;
    }

    static void k() {
        f = new char[]{61705, 61740, 61754, 61749, 61745, 61732, 61756, 61727, 61738, 61751, 61706, 61750, 61744, 61737, 61925, 61936, 61755, 61753, 61699};
        h = true;
        g = true;
        j = 782102981;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void o(byte r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 * 2
            int r7 = r7 + 4
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r0 = o.as.c.$$d
            int r9 = 121 - r9
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L16
            r9 = r8
            r3 = r9
            r4 = r2
            r8 = r7
            goto L2c
        L16:
            r3 = r2
            r6 = r8
            r8 = r7
            r7 = r9
            r9 = r6
        L1b:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r8]
        L2c:
            int r7 = r7 + r3
            int r8 = r8 + 1
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.as.c.o(byte, byte, byte, java.lang.Object[]):void");
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        int i2 = i + 67;
        m = i2 % 128;
        switch (i2 % 2 == 0 ? '2' : '\n') {
            case '\n':
                return n();
            default:
                n();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public c(Context context, b bVar, o.ei.c cVar) {
        super(context, bVar, cVar, e.y);
        this.b = null;
    }

    public final void d(d dVar, String str, j jVar, String str2) {
        g.c();
        Object[] objArr = new Object[1];
        l(null, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 127, null, "\u008e\u008a\u0086\u008d\u008d\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        l(null, 127 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), null, "\u008f\u0093\u008f\u0087\u0086\u0085\u0084\u0083\u0082\u008e\u008f\u008c\u0092\u008f\u008e\u0091\u0086\u0089\u008f\u0090\u008f\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081\u008c\u008e", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(jVar).toString());
        this.d = str;
        this.c = dVar;
        this.e = jVar;
        this.a = str2;
        c();
        int i2 = i + 61;
        m = i2 % 128;
        int i3 = i2 % 2;
    }

    private AsyncTaskC0028c n() {
        AsyncTaskC0028c asyncTaskC0028c = new AsyncTaskC0028c(this);
        int i2 = m + 45;
        i = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                int i3 = 48 / 0;
                return asyncTaskC0028c;
            default:
                return asyncTaskC0028c;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i2 = i + Opcodes.DMUL;
        m = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        l(null, KeyEvent.normalizeMetaState(0) + 127, null, "\u008e\u008a\u0086\u008d\u008d\u008c\u008b\u008a\u0089\u0088\u0087\u0086\u0085\u0084\u0083\u0082\u0081", objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = i + 61;
        m = i4 % 128;
        int i5 = i4 % 2;
        return intern;
    }

    /* renamed from: o.as.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\as\c$c.smali */
    static final class AsyncTaskC0028c extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static int b;
        private static short[] c;
        private static byte[] d;
        private static int e;
        private static int f;
        private static int g;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            f = 0;
            g = 1;
            d = new byte[]{81, 87, -121, 66, -81, -90, -89, 80, 95, 28, 28, 27, -32, -26, 29, 24, -25, 25, -30, 28, 29, 24, 29, -25, -29, -30, 30, 31, -34, 32, 55, -44, 59, -41, -63, 10, -34, 56, 57, -57, -55, 31, -52, 33, 40, 41, -34, -47, -112, -112, -112, -112, -112, -112};
            e = 909053680;
            b = 260586436;
            a = 35205249;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void B(int r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                int r6 = r6 * 4
                int r6 = r6 + 4
                int r7 = r7 * 2
                int r7 = r7 + 1
                int r8 = r8 * 2
                int r8 = 110 - r8
                byte[] r0 = o.as.c.AsyncTaskC0028c.$$d
                byte[] r1 = new byte[r7]
                int r7 = r7 + (-1)
                r2 = 0
                if (r0 != 0) goto L1c
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L36
            L1c:
                r3 = r2
            L1d:
                byte r4 = (byte) r8
                r1[r3] = r4
                if (r3 != r7) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                int r3 = r3 + 1
                r4 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L36:
                int r7 = r7 + r9
                int r6 = r6 + 1
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r8
                r8 = r7
                r7 = r5
                goto L1d
            */
            throw new UnsupportedOperationException("Method not decompiled: o.as.c.AsyncTaskC0028c.B(int, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{20, -126, 34, 119};
            $$e = 10;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = g + 89;
            f = i % 128;
            switch (i % 2 != 0 ? 'J' : '`') {
                case 'J':
                    int i2 = 8 / 0;
                    break;
            }
        }

        AsyncTaskC0028c(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = f + 5;
            g = i % 128;
            switch (i % 2 == 0 ? (char) 17 : (char) 25) {
                case 25:
                    Object[] objArr = new Object[1];
                    w((byte) ((-55) - ((byte) KeyEvent.getModifierMetaStateMask())), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 875970066, (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (-85) - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), View.resolveSize(0, 0) - 967259376, objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w((byte) (64 % ((byte) KeyEvent.getModifierMetaStateMask())), (-875970066) - (AudioTrack.getMaxVolume() > 2.0f ? 1 : (AudioTrack.getMaxVolume() == 2.0f ? 0 : -1)), (short) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 3 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (-967259376) >> View.resolveSize(1, 0), objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w((byte) (TextUtils.getCapsMode("", 0, 0) - 115), (-875970056) + (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (short) Color.blue(0), TextUtils.lastIndexOf("", '0', 0, 0) - 76, (-967259422) - (ViewConfiguration.getScrollBarSize() >> 8), objArr);
            o.cf.d dVar = new o.cf.d(context, 40, ((String) objArr[0]).intern());
            int i = f + 5;
            g = i % 128;
            switch (i % 2 != 0) {
                case false:
                    throw null;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w((byte) (Color.blue(0) + Opcodes.INEG), TextUtils.indexOf((CharSequence) "", '0', 0) - 875970037, (short) (ViewConfiguration.getPressedStateDuration() >> 16), (-94) - View.combineMeasuredStates(0, 0), (-967259371) - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
            bVar.d(((String) objArr[0]).intern(), ((c) e()).e.e());
            Object[] objArr2 = new Object[1];
            w((byte) ((KeyEvent.getMaxKeyCode() >> 16) + 85), (-875970037) - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) View.combineMeasuredStates(0, 0), (ViewConfiguration.getWindowTouchSlop() >> 8) - 90, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 967259378, objArr2);
            bVar.d(((String) objArr2[0]).intern(), ((c) e()).a);
            Object[] objArr3 = new Object[1];
            w((byte) (69 - TextUtils.getOffsetBefore("", 0)), TextUtils.lastIndexOf("", '0', 0, 0) - 875970031, (short) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) - 90, (-967259358) - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr3);
            bVar.d(((String) objArr3[0]).intern(), ((c) e()).e.h());
            int i = f + 53;
            g = i % 128;
            int i2 = i % 2;
            return bVar;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.cf.j n() {
            o.cf.j jVar = new o.cf.j(((c) e()).d, false, ((c) e()).c);
            int i = g + 99;
            f = i % 128;
            int i2 = i % 2;
            return jVar;
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = f + 3;
            int i2 = i % 128;
            g = i2;
            int i3 = i % 2;
            int i4 = i2 + 23;
            f = i4 % 128;
            char c2 = i4 % 2 != 0 ? 'T' : (char) 19;
            Object obj = null;
            switch (c2) {
                case 19:
                    return null;
                default:
                    obj.hashCode();
                    throw null;
            }
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = f + 85;
            g = i2 % 128;
            Object obj = null;
            switch (i2 % 2 != 0) {
                case false:
                    obj.hashCode();
                    throw null;
                default:
                    switch (i) {
                        case 5001:
                            return o.bb.a.ay;
                        case 5002:
                            return o.bb.a.az;
                        default:
                            o.bb.a c2 = super.c(i);
                            int i3 = g + 79;
                            f = i3 % 128;
                            switch (i3 % 2 == 0) {
                                case false:
                                    throw null;
                                default:
                                    return c2;
                            }
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = g + 13;
            f = i % 128;
            int i2 = i % 2;
            c cVar = (c) e();
            Object[] objArr = new Object[1];
            w((byte) (KeyEvent.getDeadChar(0, 0) + 68), (-875970027) - View.MeasureSpec.getSize(0), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), (-86) - ImageFormat.getBitsPerPixel(0), (-967259376) + (KeyEvent.getMaxKeyCode() >> 16), objArr);
            cVar.b = bVar.r(((String) objArr[0]).intern());
            int i3 = f + 109;
            g = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void t() {
            int i = g + 71;
            f = i % 128;
            int i2 = i % 2;
            switch (AnonymousClass3.b[h().d().ordinal()]) {
                case 1:
                    f().c(g(), ((c) e()).a);
                    int i3 = g + 91;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                    break;
                case 2:
                    f().e(g(), ((c) e()).a);
                    break;
                default:
                    super.t();
                    break;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(o.bb.d dVar) {
            int i = g + 35;
            f = i % 128;
            switch (i % 2 != 0 ? Typography.less : '\t') {
                case '\t':
                    ((c) e()).j().a(((c) e()).b);
                    return;
                default:
                    ((c) e()).j().a(((c) e()).b);
                    throw null;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(o.bb.d dVar) {
            int i = f + 77;
            g = i % 128;
            Object obj = null;
            switch (i % 2 == 0 ? (char) 17 : ':') {
                case 17:
                    ((c) e()).j().d(dVar);
                    obj.hashCode();
                    throw null;
                default:
                    ((c) e()).j().d(dVar);
                    int i2 = f + 77;
                    g = i2 % 128;
                    switch (i2 % 2 == 0 ? Typography.amp : '(') {
                        case '(':
                            return;
                        default:
                            obj.hashCode();
                            throw null;
                    }
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:87:0x0285, code lost:
        
            r3 = o.as.c.AsyncTaskC0028c.$10 + com.esotericsoftware.asm.Opcodes.DREM;
            o.as.c.AsyncTaskC0028c.$11 = r3 % 128;
            r3 = r3 % 2;
            r3 = r7;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void w(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
            /*
                Method dump skipped, instructions count: 872
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.as.c.AsyncTaskC0028c.w(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.as.c$3, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\as\c$3.smali */
    static /* synthetic */ class AnonymousClass3 {
        static final /* synthetic */ int[] b;
        private static int d;
        private static int e;

        static {
            d = 0;
            e = 1;
            int[] iArr = new int[o.bb.a.values().length];
            b = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = d;
                int i2 = (i ^ Opcodes.DMUL) + ((i & Opcodes.DMUL) << 1);
                e = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                b[o.bb.a.az.ordinal()] = 2;
                int i3 = e;
                int i4 = (i3 ^ 75) + ((i3 & 75) << 1);
                d = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v19, types: [byte[]] */
    private static void l(java.lang.String r16, int r17, int[] r18, java.lang.String r19, java.lang.Object[] r20) {
        /*
            Method dump skipped, instructions count: 888
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.as.c.l(java.lang.String, int, int[], java.lang.String, java.lang.Object[]):void");
    }
}
