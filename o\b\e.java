package o.b;

import android.graphics.Color;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.io.encoding.Base64;
import org.bouncycastle.math.ec.Tnaf;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\b\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final e b;
    public static final e c;
    public static final e d;
    public static final e e;
    private static char[] f;
    private static final /* synthetic */ e[] g;
    private static int h;
    private static long i;
    private static int j;
    private final int a;

    static void b() {
        f = new char[]{11399, 1543, 31231, 21370, 34367, 63875, 54137, 1735, 31154, 21257, 34540, 63564, 54048, 1664, 56399, 63180, 35128, 41860, 30460, 2382, 11397, 1543, 31212, 21325, 34344, 63880, 54080, 1755, 31157, 63213, 56431, 41856, 35113, 23616, 9184};
        i = 6219098694993577576L;
    }

    static void init$0() {
        $$a = new byte[]{Base64.padSymbol, -59, -56, Tnaf.POW_2_WIDTH};
        $$b = 32;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.b.e.$$a
            int r6 = r6 + 4
            int r8 = r8 * 2
            int r8 = 1 - r8
            int r7 = 105 - r7
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L35
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r6 = r6 + 1
            r3 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.e.l(short, int, byte, java.lang.Object[]):void");
    }

    private static /* synthetic */ e[] e() {
        int i2 = h + 31;
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        e[] eVarArr = {b, c, d, e};
        int i5 = i3 + Opcodes.LUSHR;
        h = i5 % 128;
        int i6 = i5 % 2;
        return eVarArr;
    }

    public static e valueOf(String str) {
        int i2 = h + Opcodes.DDIV;
        j = i2 % 128;
        int i3 = i2 % 2;
        e eVar = (e) Enum.valueOf(e.class, str);
        int i4 = h + 37;
        j = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    public static e[] values() {
        int i2 = h + 41;
        j = i2 % 128;
        int i3 = i2 % 2;
        e[] eVarArr = (e[]) g.clone();
        int i4 = h + 47;
        j = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return eVarArr;
            default:
                int i5 = 69 / 0;
                return eVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        b();
        Object[] objArr = new Object[1];
        k((char) ((-1) - MotionEvent.axisFromString("")), ViewConfiguration.getMaximumDrawingCacheSize() >> 24, 15 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
        b = new e(((String) objArr[0]).intern(), 0, 0);
        Object[] objArr2 = new Object[1];
        k((char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 61639), 14 - TextUtils.indexOf("", ""), 6 - Color.alpha(0), objArr2);
        c = new e(((String) objArr2[0]).intern(), 1, 2);
        Object[] objArr3 = new Object[1];
        k((char) Gravity.getAbsoluteGravity(0, 0), 20 - View.MeasureSpec.makeMeasureSpec(0, 0), ExpandableListView.getPackedPositionChild(0L) + 10, objArr3);
        d = new e(((String) objArr3[0]).intern(), 2, 3);
        Object[] objArr4 = new Object[1];
        k((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 55912), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 29, 7 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr4);
        e = new e(((String) objArr4[0]).intern(), 3, 4);
        g = e();
        int i2 = j + 25;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? 'S' : ',') {
            case Opcodes.AASTORE /* 83 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    private e(String str, int i2, int i3) {
        this.a = i3;
    }

    public final int d() {
        int i2 = j + 15;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '%' : (char) 19) {
            case 19:
                return this.a;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.b.e e(int r7) {
        /*
            int r0 = o.b.e.h
            int r0 = r0 + 7
            int r1 = r0 % 128
            o.b.e.j = r1
            int r0 = r0 % 2
            o.b.e[] r0 = values()
            int r1 = r0.length
            r2 = 0
            r3 = r2
        L11:
            r4 = 1
            if (r3 >= r1) goto L16
            r5 = r2
            goto L17
        L16:
            r5 = r4
        L17:
            switch(r5) {
                case 0: goto L1c;
                default: goto L1a;
            }
        L1a:
            r7 = 0
            return r7
        L1c:
            r5 = r0[r3]
            int r6 = r5.a
            if (r7 != r6) goto L24
            r4 = r2
            goto L25
        L24:
        L25:
            switch(r4) {
                case 1: goto L33;
                default: goto L28;
            }
        L28:
            int r7 = o.b.e.j
            int r7 = r7 + 97
            int r0 = r7 % 128
            o.b.e.h = r0
            int r7 = r7 % 2
            goto L36
        L33:
            int r3 = r3 + 1
            goto L11
        L36:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.e.e(int):o.b.e");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 594
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.b.e.k(char, int, int, java.lang.Object[]):void");
    }
}
