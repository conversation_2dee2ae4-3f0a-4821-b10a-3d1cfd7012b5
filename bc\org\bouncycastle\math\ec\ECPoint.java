package bc.org.bouncycastle.math.ec;

import androidx.core.view.InputDeviceCompat;
import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECFieldElement;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import java.math.BigInteger;
import java.util.Hashtable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint.smali */
public abstract class ECPoint {
    protected static final ECFieldElement[] f = new ECFieldElement[0];
    protected ECCurve a;
    protected ECFieldElement b;
    protected ECFieldElement c;
    protected ECFieldElement[] d;
    protected Hashtable e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint$AbstractF2m.smali */
    public static abstract class AbstractF2m extends ECPoint {
        protected AbstractF2m(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            super(eCCurve, eCFieldElement, eCFieldElement2);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected boolean g() {
            ECFieldElement multiplyPlusProduct;
            ECFieldElement squarePlusProduct;
            ECCurve curve = getCurve();
            ECFieldElement eCFieldElement = this.b;
            ECFieldElement a = curve.getA();
            ECFieldElement b = curve.getB();
            int coordinateSystem = curve.getCoordinateSystem();
            if (coordinateSystem != 6) {
                ECFieldElement eCFieldElement2 = this.c;
                ECFieldElement multiply = eCFieldElement2.add(eCFieldElement).multiply(eCFieldElement2);
                if (coordinateSystem != 0) {
                    if (coordinateSystem != 1) {
                        throw new IllegalStateException("unsupported coordinate system");
                    }
                    ECFieldElement eCFieldElement3 = this.d[0];
                    if (!eCFieldElement3.isOne()) {
                        ECFieldElement multiply2 = eCFieldElement3.multiply(eCFieldElement3.square());
                        multiply = multiply.multiply(eCFieldElement3);
                        a = a.multiply(eCFieldElement3);
                        b = b.multiply(multiply2);
                    }
                }
                return multiply.equals(eCFieldElement.add(a).multiply(eCFieldElement.square()).add(b));
            }
            ECFieldElement eCFieldElement4 = this.d[0];
            boolean isOne = eCFieldElement4.isOne();
            if (eCFieldElement.isZero()) {
                ECFieldElement square = this.c.square();
                if (!isOne) {
                    b = b.multiply(eCFieldElement4.square());
                }
                return square.equals(b);
            }
            ECFieldElement eCFieldElement5 = this.c;
            ECFieldElement square2 = eCFieldElement.square();
            if (isOne) {
                multiplyPlusProduct = eCFieldElement5.square().add(eCFieldElement5).add(a);
                squarePlusProduct = square2.square().add(b);
            } else {
                ECFieldElement square3 = eCFieldElement4.square();
                ECFieldElement square4 = square3.square();
                multiplyPlusProduct = eCFieldElement5.add(eCFieldElement4).multiplyPlusProduct(eCFieldElement5, a, square3);
                squarePlusProduct = square2.squarePlusProduct(b, square4);
            }
            return multiplyPlusProduct.multiply(square2).equals(squarePlusProduct);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected boolean h() {
            BigInteger cofactor = this.a.getCofactor();
            if (ECConstants.TWO.equals(cofactor)) {
                return ((ECFieldElement.AbstractF2m) normalize().getAffineXCoord()).trace() != 0;
            }
            if (!ECConstants.FOUR.equals(cofactor)) {
                return super.h();
            }
            ECPoint normalize = normalize();
            ECFieldElement affineXCoord = normalize.getAffineXCoord();
            ECCurve eCCurve = this.a;
            ECFieldElement a = ((ECCurve.AbstractF2m) eCCurve).a(affineXCoord.add(eCCurve.getA()));
            if (a == null) {
                return false;
            }
            return ((ECFieldElement.AbstractF2m) affineXCoord.multiply(a).add(normalize.getAffineYCoord())).trace() == 0;
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint scaleX(ECFieldElement eCFieldElement) {
            if (isInfinity()) {
                return this;
            }
            int d = d();
            if (d == 5) {
                ECFieldElement rawXCoord = getRawXCoord();
                return getCurve().a(rawXCoord, getRawYCoord().add(rawXCoord).divide(eCFieldElement).add(rawXCoord.multiply(eCFieldElement)), e());
            }
            if (d != 6) {
                return super.scaleX(eCFieldElement);
            }
            ECFieldElement rawXCoord2 = getRawXCoord();
            ECFieldElement rawYCoord = getRawYCoord();
            ECFieldElement eCFieldElement2 = e()[0];
            ECFieldElement multiply = rawXCoord2.multiply(eCFieldElement.square());
            return getCurve().a(multiply, rawYCoord.add(rawXCoord2).add(multiply), new ECFieldElement[]{eCFieldElement2.multiply(eCFieldElement)});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint scaleXNegateY(ECFieldElement eCFieldElement) {
            return scaleX(eCFieldElement);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint scaleY(ECFieldElement eCFieldElement) {
            if (isInfinity()) {
                return this;
            }
            int d = d();
            if (d != 5 && d != 6) {
                return super.scaleY(eCFieldElement);
            }
            ECFieldElement rawXCoord = getRawXCoord();
            return getCurve().a(rawXCoord, getRawYCoord().add(rawXCoord).multiply(eCFieldElement).add(rawXCoord), e());
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint scaleYNegateX(ECFieldElement eCFieldElement) {
            return scaleY(eCFieldElement);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint subtract(ECPoint eCPoint) {
            return eCPoint.isInfinity() ? this : add(eCPoint.negate());
        }

        public AbstractF2m tau() {
            if (isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement eCFieldElement = this.b;
            if (coordinateSystem != 0) {
                if (coordinateSystem != 1) {
                    if (coordinateSystem != 5) {
                        if (coordinateSystem != 6) {
                            throw new IllegalStateException("unsupported coordinate system");
                        }
                    }
                }
                return (AbstractF2m) curve.a(eCFieldElement.square(), this.c.square(), new ECFieldElement[]{this.d[0].square()});
            }
            return (AbstractF2m) curve.a(eCFieldElement.square(), this.c.square());
        }

        public AbstractF2m tauPow(int i) {
            if (isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement eCFieldElement = this.b;
            if (coordinateSystem != 0) {
                if (coordinateSystem != 1) {
                    if (coordinateSystem != 5) {
                        if (coordinateSystem != 6) {
                            throw new IllegalStateException("unsupported coordinate system");
                        }
                    }
                }
                return (AbstractF2m) curve.a(eCFieldElement.squarePow(i), this.c.squarePow(i), new ECFieldElement[]{this.d[0].squarePow(i)});
            }
            return (AbstractF2m) curve.a(eCFieldElement.squarePow(i), this.c.squarePow(i));
        }

        protected AbstractF2m(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint$AbstractFp.smali */
    public static abstract class AbstractFp extends ECPoint {
        protected AbstractFp(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            super(eCCurve, eCFieldElement, eCFieldElement2);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected boolean c() {
            return getAffineYCoord().testBitZero();
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected boolean g() {
            ECFieldElement eCFieldElement = this.b;
            ECFieldElement eCFieldElement2 = this.c;
            ECFieldElement a = this.a.getA();
            ECFieldElement b = this.a.getB();
            ECFieldElement square = eCFieldElement2.square();
            switch (d()) {
                case 0:
                    break;
                case 1:
                    ECFieldElement eCFieldElement3 = this.d[0];
                    if (!eCFieldElement3.isOne()) {
                        ECFieldElement square2 = eCFieldElement3.square();
                        ECFieldElement multiply = eCFieldElement3.multiply(square2);
                        square = square.multiply(eCFieldElement3);
                        a = a.multiply(square2);
                        b = b.multiply(multiply);
                        break;
                    }
                    break;
                case 2:
                case 3:
                case 4:
                    ECFieldElement eCFieldElement4 = this.d[0];
                    if (!eCFieldElement4.isOne()) {
                        ECFieldElement square3 = eCFieldElement4.square();
                        ECFieldElement square4 = square3.square();
                        ECFieldElement multiply2 = square3.multiply(square4);
                        a = a.multiply(square4);
                        b = b.multiply(multiply2);
                        break;
                    }
                    break;
                default:
                    throw new IllegalStateException("unsupported coordinate system");
            }
            return square.equals(eCFieldElement.square().add(a).multiply(eCFieldElement).add(b));
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint subtract(ECPoint eCPoint) {
            return eCPoint.isInfinity() ? this : add(eCPoint.negate());
        }

        protected AbstractFp(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint$F2m.smali */
    public static class F2m extends AbstractF2m {
        F2m(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            super(eCCurve, eCFieldElement, eCFieldElement2);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint add(ECPoint eCPoint) {
            ECFieldElement eCFieldElement;
            ECFieldElement eCFieldElement2;
            ECFieldElement eCFieldElement3;
            ECFieldElement eCFieldElement4;
            ECFieldElement eCFieldElement5;
            ECFieldElement eCFieldElement6;
            if (isInfinity()) {
                return eCPoint;
            }
            if (eCPoint.isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement eCFieldElement7 = this.b;
            ECFieldElement eCFieldElement8 = eCPoint.b;
            if (coordinateSystem == 0) {
                ECFieldElement eCFieldElement9 = this.c;
                ECFieldElement eCFieldElement10 = eCPoint.c;
                ECFieldElement add = eCFieldElement7.add(eCFieldElement8);
                ECFieldElement add2 = eCFieldElement9.add(eCFieldElement10);
                if (add.isZero()) {
                    return add2.isZero() ? twice() : curve.getInfinity();
                }
                ECFieldElement divide = add2.divide(add);
                ECFieldElement add3 = divide.square().add(divide).add(add).add(curve.getA());
                return new F2m(curve, add3, divide.multiply(eCFieldElement7.add(add3)).add(add3).add(eCFieldElement9));
            }
            if (coordinateSystem == 1) {
                ECFieldElement eCFieldElement11 = this.c;
                ECFieldElement eCFieldElement12 = this.d[0];
                ECFieldElement eCFieldElement13 = eCPoint.c;
                ECFieldElement eCFieldElement14 = eCPoint.d[0];
                boolean isOne = eCFieldElement14.isOne();
                ECFieldElement add4 = eCFieldElement12.multiply(eCFieldElement13).add(isOne ? eCFieldElement11 : eCFieldElement11.multiply(eCFieldElement14));
                ECFieldElement add5 = eCFieldElement12.multiply(eCFieldElement8).add(isOne ? eCFieldElement7 : eCFieldElement7.multiply(eCFieldElement14));
                if (add5.isZero()) {
                    return add4.isZero() ? twice() : curve.getInfinity();
                }
                ECFieldElement square = add5.square();
                ECFieldElement multiply = square.multiply(add5);
                if (!isOne) {
                    eCFieldElement12 = eCFieldElement12.multiply(eCFieldElement14);
                }
                ECFieldElement add6 = add4.add(add5);
                ECFieldElement add7 = add6.multiplyPlusProduct(add4, square, curve.getA()).multiply(eCFieldElement12).add(multiply);
                ECFieldElement multiply2 = add5.multiply(add7);
                if (!isOne) {
                    square = square.multiply(eCFieldElement14);
                }
                return new F2m(curve, multiply2, add4.multiplyPlusProduct(eCFieldElement7, add5, eCFieldElement11).multiplyPlusProduct(square, add6, add7), new ECFieldElement[]{multiply.multiply(eCFieldElement12)});
            }
            if (coordinateSystem != 6) {
                throw new IllegalStateException("unsupported coordinate system");
            }
            if (eCFieldElement7.isZero()) {
                return eCFieldElement8.isZero() ? curve.getInfinity() : eCPoint.add(this);
            }
            ECFieldElement eCFieldElement15 = this.c;
            ECFieldElement eCFieldElement16 = this.d[0];
            ECFieldElement eCFieldElement17 = eCPoint.c;
            ECFieldElement eCFieldElement18 = eCPoint.d[0];
            boolean isOne2 = eCFieldElement16.isOne();
            if (isOne2) {
                eCFieldElement = eCFieldElement8;
                eCFieldElement2 = eCFieldElement17;
            } else {
                eCFieldElement = eCFieldElement8.multiply(eCFieldElement16);
                eCFieldElement2 = eCFieldElement17.multiply(eCFieldElement16);
            }
            boolean isOne3 = eCFieldElement18.isOne();
            if (isOne3) {
                eCFieldElement3 = eCFieldElement15;
            } else {
                eCFieldElement7 = eCFieldElement7.multiply(eCFieldElement18);
                eCFieldElement3 = eCFieldElement15.multiply(eCFieldElement18);
            }
            ECFieldElement add8 = eCFieldElement3.add(eCFieldElement2);
            ECFieldElement add9 = eCFieldElement7.add(eCFieldElement);
            if (add9.isZero()) {
                return add8.isZero() ? twice() : curve.getInfinity();
            }
            if (eCFieldElement8.isZero()) {
                ECPoint normalize = normalize();
                ECFieldElement xCoord = normalize.getXCoord();
                ECFieldElement yCoord = normalize.getYCoord();
                ECFieldElement divide2 = yCoord.add(eCFieldElement17).divide(xCoord);
                eCFieldElement4 = divide2.square().add(divide2).add(xCoord).add(curve.getA());
                if (eCFieldElement4.isZero()) {
                    return new F2m(curve, eCFieldElement4, curve.getB().sqrt());
                }
                eCFieldElement6 = divide2.multiply(xCoord.add(eCFieldElement4)).add(eCFieldElement4).add(yCoord).divide(eCFieldElement4).add(eCFieldElement4);
                eCFieldElement5 = curve.fromBigInteger(ECConstants.ONE);
            } else {
                ECFieldElement square2 = add9.square();
                ECFieldElement multiply3 = add8.multiply(eCFieldElement7);
                ECFieldElement multiply4 = add8.multiply(eCFieldElement);
                ECFieldElement multiply5 = multiply3.multiply(multiply4);
                if (multiply5.isZero()) {
                    return new F2m(curve, multiply5, curve.getB().sqrt());
                }
                ECFieldElement multiply6 = add8.multiply(square2);
                ECFieldElement multiply7 = !isOne3 ? multiply6.multiply(eCFieldElement18) : multiply6;
                ECFieldElement squarePlusProduct = multiply4.add(square2).squarePlusProduct(multiply7, eCFieldElement15.add(eCFieldElement16));
                if (!isOne2) {
                    multiply7 = multiply7.multiply(eCFieldElement16);
                }
                eCFieldElement4 = multiply5;
                eCFieldElement5 = multiply7;
                eCFieldElement6 = squarePlusProduct;
            }
            return new F2m(curve, eCFieldElement4, eCFieldElement6, new ECFieldElement[]{eCFieldElement5});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected ECPoint b() {
            return new F2m(null, getAffineXCoord(), getAffineYCoord());
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected boolean c() {
            ECFieldElement rawXCoord = getRawXCoord();
            if (rawXCoord.isZero()) {
                return false;
            }
            ECFieldElement rawYCoord = getRawYCoord();
            int d = d();
            return (d == 5 || d == 6) ? rawYCoord.testBitZero() != rawXCoord.testBitZero() : rawYCoord.divide(rawXCoord).testBitZero();
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECFieldElement getYCoord() {
            int d = d();
            if (d != 5 && d != 6) {
                return this.c;
            }
            ECFieldElement eCFieldElement = this.b;
            ECFieldElement eCFieldElement2 = this.c;
            if (isInfinity() || eCFieldElement.isZero()) {
                return eCFieldElement2;
            }
            ECFieldElement multiply = eCFieldElement2.add(eCFieldElement).multiply(eCFieldElement);
            if (6 != d) {
                return multiply;
            }
            ECFieldElement eCFieldElement3 = this.d[0];
            return !eCFieldElement3.isOne() ? multiply.divide(eCFieldElement3) : multiply;
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint negate() {
            if (isInfinity()) {
                return this;
            }
            ECFieldElement eCFieldElement = this.b;
            if (eCFieldElement.isZero()) {
                return this;
            }
            int d = d();
            if (d == 0) {
                return new F2m(this.a, eCFieldElement, this.c.add(eCFieldElement));
            }
            if (d == 1) {
                return new F2m(this.a, eCFieldElement, this.c.add(eCFieldElement), new ECFieldElement[]{this.d[0]});
            }
            if (d == 5) {
                return new F2m(this.a, eCFieldElement, this.c.addOne());
            }
            if (d != 6) {
                throw new IllegalStateException("unsupported coordinate system");
            }
            ECFieldElement eCFieldElement2 = this.c;
            ECFieldElement eCFieldElement3 = this.d[0];
            return new F2m(this.a, eCFieldElement, eCFieldElement2.add(eCFieldElement3), new ECFieldElement[]{eCFieldElement3});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint twice() {
            ECFieldElement add;
            if (isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            ECFieldElement eCFieldElement = this.b;
            if (eCFieldElement.isZero()) {
                return curve.getInfinity();
            }
            int coordinateSystem = curve.getCoordinateSystem();
            if (coordinateSystem == 0) {
                ECFieldElement add2 = this.c.divide(eCFieldElement).add(eCFieldElement);
                ECFieldElement add3 = add2.square().add(add2).add(curve.getA());
                return new F2m(curve, add3, eCFieldElement.squarePlusProduct(add3, add2.addOne()));
            }
            if (coordinateSystem == 1) {
                ECFieldElement eCFieldElement2 = this.c;
                ECFieldElement eCFieldElement3 = this.d[0];
                boolean isOne = eCFieldElement3.isOne();
                ECFieldElement multiply = isOne ? eCFieldElement : eCFieldElement.multiply(eCFieldElement3);
                if (!isOne) {
                    eCFieldElement2 = eCFieldElement2.multiply(eCFieldElement3);
                }
                ECFieldElement square = eCFieldElement.square();
                ECFieldElement add4 = square.add(eCFieldElement2);
                ECFieldElement square2 = multiply.square();
                ECFieldElement add5 = add4.add(multiply);
                ECFieldElement multiplyPlusProduct = add5.multiplyPlusProduct(add4, square2, curve.getA());
                return new F2m(curve, multiply.multiply(multiplyPlusProduct), square.square().multiplyPlusProduct(multiply, multiplyPlusProduct, add5), new ECFieldElement[]{multiply.multiply(square2)});
            }
            if (coordinateSystem != 6) {
                throw new IllegalStateException("unsupported coordinate system");
            }
            ECFieldElement eCFieldElement4 = this.c;
            ECFieldElement eCFieldElement5 = this.d[0];
            boolean isOne2 = eCFieldElement5.isOne();
            ECFieldElement multiply2 = isOne2 ? eCFieldElement4 : eCFieldElement4.multiply(eCFieldElement5);
            ECFieldElement square3 = isOne2 ? eCFieldElement5 : eCFieldElement5.square();
            ECFieldElement a = curve.getA();
            ECFieldElement multiply3 = isOne2 ? a : a.multiply(square3);
            ECFieldElement add6 = eCFieldElement4.square().add(multiply2).add(multiply3);
            if (add6.isZero()) {
                return new F2m(curve, add6, curve.getB().sqrt());
            }
            ECFieldElement square4 = add6.square();
            ECFieldElement multiply4 = isOne2 ? add6 : add6.multiply(square3);
            ECFieldElement b = curve.getB();
            if (b.bitLength() < (curve.getFieldSize() >> 1)) {
                ECFieldElement square5 = eCFieldElement4.add(eCFieldElement).square();
                add = square5.add(add6).add(square3).multiply(square5).add(b.isOne() ? multiply3.add(square3).square() : multiply3.squarePlusProduct(b, square3.square())).add(square4);
                if (a.isZero()) {
                    add = add.add(multiply4);
                } else if (!a.isOne()) {
                    add = add.add(a.addOne().multiply(multiply4));
                }
            } else {
                if (!isOne2) {
                    eCFieldElement = eCFieldElement.multiply(eCFieldElement5);
                }
                add = eCFieldElement.squarePlusProduct(add6, multiply2).add(square4).add(multiply4);
            }
            return new F2m(curve, square4, add, new ECFieldElement[]{multiply4});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint twicePlus(ECPoint eCPoint) {
            if (isInfinity()) {
                return eCPoint;
            }
            if (eCPoint.isInfinity()) {
                return twice();
            }
            ECCurve curve = getCurve();
            ECFieldElement eCFieldElement = this.b;
            if (eCFieldElement.isZero()) {
                return eCPoint;
            }
            if (curve.getCoordinateSystem() != 6) {
                return twice().add(eCPoint);
            }
            ECFieldElement eCFieldElement2 = eCPoint.b;
            ECFieldElement eCFieldElement3 = eCPoint.d[0];
            if (eCFieldElement2.isZero() || !eCFieldElement3.isOne()) {
                return twice().add(eCPoint);
            }
            ECFieldElement eCFieldElement4 = this.c;
            ECFieldElement eCFieldElement5 = this.d[0];
            ECFieldElement eCFieldElement6 = eCPoint.c;
            ECFieldElement square = eCFieldElement.square();
            ECFieldElement square2 = eCFieldElement4.square();
            ECFieldElement square3 = eCFieldElement5.square();
            ECFieldElement add = curve.getA().multiply(square3).add(square2).add(eCFieldElement4.multiply(eCFieldElement5));
            ECFieldElement addOne = eCFieldElement6.addOne();
            ECFieldElement multiplyPlusProduct = curve.getA().add(addOne).multiply(square3).add(square2).multiplyPlusProduct(add, square, square3);
            ECFieldElement multiply = eCFieldElement2.multiply(square3);
            ECFieldElement square4 = multiply.add(add).square();
            if (square4.isZero()) {
                return multiplyPlusProduct.isZero() ? eCPoint.twice() : curve.getInfinity();
            }
            if (multiplyPlusProduct.isZero()) {
                return new F2m(curve, multiplyPlusProduct, curve.getB().sqrt());
            }
            ECFieldElement multiply2 = multiplyPlusProduct.square().multiply(multiply);
            ECFieldElement multiply3 = multiplyPlusProduct.multiply(square4).multiply(square3);
            return new F2m(curve, multiply2, multiplyPlusProduct.add(square4).square().multiplyPlusProduct(add, addOne, multiply3), new ECFieldElement[]{multiply3});
        }

        F2m(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint$Fp.smali */
    public static class Fp extends AbstractFp {
        Fp(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            super(eCCurve, eCFieldElement, eCFieldElement2);
        }

        protected Fp a(boolean z) {
            ECFieldElement eCFieldElement = this.b;
            ECFieldElement eCFieldElement2 = this.c;
            ECFieldElement eCFieldElement3 = this.d[0];
            ECFieldElement i = i();
            ECFieldElement add = d(eCFieldElement.square()).add(i);
            ECFieldElement e = e(eCFieldElement2);
            ECFieldElement multiply = e.multiply(eCFieldElement2);
            ECFieldElement e2 = e(eCFieldElement.multiply(multiply));
            ECFieldElement subtract = add.square().subtract(e(e2));
            ECFieldElement e3 = e(multiply.square());
            ECFieldElement subtract2 = add.multiply(e2.subtract(subtract)).subtract(e3);
            ECFieldElement e4 = z ? e(e3.multiply(i)) : null;
            if (!eCFieldElement3.isOne()) {
                e = e.multiply(eCFieldElement3);
            }
            return new Fp(getCurve(), subtract, subtract2, new ECFieldElement[]{e, e4});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint add(ECPoint eCPoint) {
            ECFieldElement square;
            ECFieldElement multiplyMinusProduct;
            ECFieldElement multiply;
            ECFieldElement eCFieldElement;
            if (isInfinity()) {
                return eCPoint;
            }
            if (eCPoint.isInfinity()) {
                return this;
            }
            if (this == eCPoint) {
                return twice();
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement eCFieldElement2 = this.b;
            ECFieldElement eCFieldElement3 = this.c;
            ECFieldElement eCFieldElement4 = eCPoint.b;
            ECFieldElement eCFieldElement5 = eCPoint.c;
            if (coordinateSystem == 0) {
                ECFieldElement subtract = eCFieldElement4.subtract(eCFieldElement2);
                ECFieldElement subtract2 = eCFieldElement5.subtract(eCFieldElement3);
                if (subtract.isZero()) {
                    return subtract2.isZero() ? twice() : curve.getInfinity();
                }
                ECFieldElement divide = subtract2.divide(subtract);
                ECFieldElement subtract3 = divide.square().subtract(eCFieldElement2).subtract(eCFieldElement4);
                return new Fp(curve, subtract3, divide.multiply(eCFieldElement2.subtract(subtract3)).subtract(eCFieldElement3));
            }
            if (coordinateSystem == 1) {
                ECFieldElement eCFieldElement6 = this.d[0];
                ECFieldElement eCFieldElement7 = eCPoint.d[0];
                boolean isOne = eCFieldElement6.isOne();
                boolean isOne2 = eCFieldElement7.isOne();
                if (!isOne) {
                    eCFieldElement5 = eCFieldElement5.multiply(eCFieldElement6);
                }
                if (!isOne2) {
                    eCFieldElement3 = eCFieldElement3.multiply(eCFieldElement7);
                }
                ECFieldElement subtract4 = eCFieldElement5.subtract(eCFieldElement3);
                if (!isOne) {
                    eCFieldElement4 = eCFieldElement4.multiply(eCFieldElement6);
                }
                if (!isOne2) {
                    eCFieldElement2 = eCFieldElement2.multiply(eCFieldElement7);
                }
                ECFieldElement subtract5 = eCFieldElement4.subtract(eCFieldElement2);
                if (subtract5.isZero()) {
                    return subtract4.isZero() ? twice() : curve.getInfinity();
                }
                if (isOne) {
                    eCFieldElement6 = eCFieldElement7;
                } else if (!isOne2) {
                    eCFieldElement6 = eCFieldElement6.multiply(eCFieldElement7);
                }
                ECFieldElement square2 = subtract5.square();
                ECFieldElement multiply2 = square2.multiply(subtract5);
                ECFieldElement multiply3 = square2.multiply(eCFieldElement2);
                ECFieldElement subtract6 = subtract4.square().multiply(eCFieldElement6).subtract(multiply2).subtract(e(multiply3));
                return new Fp(curve, subtract5.multiply(subtract6), multiply3.subtract(subtract6).multiplyMinusProduct(subtract4, eCFieldElement3, multiply2), new ECFieldElement[]{multiply2.multiply(eCFieldElement6)});
            }
            if (coordinateSystem != 2 && coordinateSystem != 4) {
                throw new IllegalStateException("unsupported coordinate system");
            }
            ECFieldElement eCFieldElement8 = this.d[0];
            ECFieldElement eCFieldElement9 = eCPoint.d[0];
            boolean isOne3 = eCFieldElement8.isOne();
            if (isOne3 || !eCFieldElement8.equals(eCFieldElement9)) {
                if (!isOne3) {
                    ECFieldElement square3 = eCFieldElement8.square();
                    eCFieldElement4 = square3.multiply(eCFieldElement4);
                    eCFieldElement5 = square3.multiply(eCFieldElement8).multiply(eCFieldElement5);
                }
                boolean isOne4 = eCFieldElement9.isOne();
                if (!isOne4) {
                    ECFieldElement square4 = eCFieldElement9.square();
                    eCFieldElement2 = square4.multiply(eCFieldElement2);
                    eCFieldElement3 = square4.multiply(eCFieldElement9).multiply(eCFieldElement3);
                }
                ECFieldElement subtract7 = eCFieldElement2.subtract(eCFieldElement4);
                ECFieldElement subtract8 = eCFieldElement3.subtract(eCFieldElement5);
                if (subtract7.isZero()) {
                    return subtract8.isZero() ? twice() : curve.getInfinity();
                }
                square = subtract7.square();
                ECFieldElement multiply4 = square.multiply(subtract7);
                ECFieldElement multiply5 = square.multiply(eCFieldElement2);
                ECFieldElement subtract9 = subtract8.square().add(multiply4).subtract(e(multiply5));
                multiplyMinusProduct = multiply5.subtract(subtract9).multiplyMinusProduct(subtract8, multiply4, eCFieldElement3);
                ECFieldElement multiply6 = !isOne3 ? subtract7.multiply(eCFieldElement8) : subtract7;
                multiply = !isOne4 ? multiply6.multiply(eCFieldElement9) : multiply6;
                if (multiply == subtract7) {
                    eCFieldElement = subtract9;
                } else {
                    eCFieldElement = subtract9;
                    square = null;
                }
            } else {
                ECFieldElement subtract10 = eCFieldElement2.subtract(eCFieldElement4);
                ECFieldElement subtract11 = eCFieldElement3.subtract(eCFieldElement5);
                if (subtract10.isZero()) {
                    return subtract11.isZero() ? twice() : curve.getInfinity();
                }
                ECFieldElement square5 = subtract10.square();
                ECFieldElement multiply7 = eCFieldElement2.multiply(square5);
                ECFieldElement multiply8 = eCFieldElement4.multiply(square5);
                ECFieldElement multiply9 = multiply7.subtract(multiply8).multiply(eCFieldElement3);
                eCFieldElement = subtract11.square().subtract(multiply7).subtract(multiply8);
                multiplyMinusProduct = multiply7.subtract(eCFieldElement).multiply(subtract11).subtract(multiply9);
                multiply = subtract10.multiply(eCFieldElement8);
                square = null;
            }
            return new Fp(curve, eCFieldElement, multiplyMinusProduct, coordinateSystem == 4 ? new ECFieldElement[]{multiply, b(multiply, square)} : new ECFieldElement[]{multiply});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        protected ECPoint b() {
            return new Fp(null, getAffineXCoord(), getAffineYCoord());
        }

        protected ECFieldElement c(ECFieldElement eCFieldElement) {
            return e(e(eCFieldElement));
        }

        protected ECFieldElement d(ECFieldElement eCFieldElement) {
            return e(eCFieldElement).add(eCFieldElement);
        }

        protected ECFieldElement e(ECFieldElement eCFieldElement) {
            return eCFieldElement.add(eCFieldElement);
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECFieldElement getZCoord(int i) {
            return (i == 1 && 4 == d()) ? i() : super.getZCoord(i);
        }

        protected ECFieldElement i() {
            ECFieldElement[] eCFieldElementArr = this.d;
            ECFieldElement eCFieldElement = eCFieldElementArr[1];
            if (eCFieldElement != null) {
                return eCFieldElement;
            }
            ECFieldElement b = b(eCFieldElementArr[0], null);
            eCFieldElementArr[1] = b;
            return b;
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint negate() {
            if (isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            return curve.getCoordinateSystem() != 0 ? new Fp(curve, this.b, this.c.negate(), this.d) : new Fp(curve, this.b, this.c.negate());
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint threeTimes() {
            if (isInfinity()) {
                return this;
            }
            ECFieldElement eCFieldElement = this.c;
            if (eCFieldElement.isZero()) {
                return this;
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            if (coordinateSystem != 0) {
                return coordinateSystem != 4 ? twice().add(this) : a(false).add(this);
            }
            ECFieldElement eCFieldElement2 = this.b;
            ECFieldElement e = e(eCFieldElement);
            ECFieldElement square = e.square();
            ECFieldElement add = d(eCFieldElement2.square()).add(getCurve().getA());
            ECFieldElement subtract = d(eCFieldElement2).multiply(square).subtract(add.square());
            if (subtract.isZero()) {
                return getCurve().getInfinity();
            }
            ECFieldElement invert = subtract.multiply(e).invert();
            ECFieldElement multiply = subtract.multiply(invert).multiply(add);
            ECFieldElement subtract2 = square.square().multiply(invert).subtract(multiply);
            ECFieldElement add2 = subtract2.subtract(multiply).multiply(multiply.add(subtract2)).add(eCFieldElement2);
            return new Fp(curve, add2, eCFieldElement2.subtract(add2).multiply(subtract2).subtract(eCFieldElement));
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint timesPow2(int i) {
            if (i < 0) {
                throw new IllegalArgumentException("'e' cannot be negative");
            }
            if (i == 0 || isInfinity()) {
                return this;
            }
            if (i == 1) {
                return twice();
            }
            ECCurve curve = getCurve();
            ECFieldElement eCFieldElement = this.c;
            if (eCFieldElement.isZero()) {
                return curve.getInfinity();
            }
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement a = curve.getA();
            ECFieldElement eCFieldElement2 = this.b;
            ECFieldElement[] eCFieldElementArr = this.d;
            ECFieldElement fromBigInteger = eCFieldElementArr.length < 1 ? curve.fromBigInteger(ECConstants.ONE) : eCFieldElementArr[0];
            if (!fromBigInteger.isOne() && coordinateSystem != 0) {
                if (coordinateSystem == 1) {
                    ECFieldElement square = fromBigInteger.square();
                    eCFieldElement2 = eCFieldElement2.multiply(fromBigInteger);
                    eCFieldElement = eCFieldElement.multiply(square);
                    a = b(fromBigInteger, square);
                } else if (coordinateSystem == 2) {
                    a = b(fromBigInteger, null);
                } else {
                    if (coordinateSystem != 4) {
                        throw new IllegalStateException("unsupported coordinate system");
                    }
                    a = i();
                }
            }
            int i2 = 0;
            while (i2 < i) {
                if (eCFieldElement.isZero()) {
                    return curve.getInfinity();
                }
                ECFieldElement d = d(eCFieldElement2.square());
                ECFieldElement e = e(eCFieldElement);
                ECFieldElement multiply = e.multiply(eCFieldElement);
                ECFieldElement e2 = e(eCFieldElement2.multiply(multiply));
                ECFieldElement e3 = e(multiply.square());
                if (!a.isZero()) {
                    d = d.add(a);
                    a = e(e3.multiply(a));
                }
                ECFieldElement subtract = d.square().subtract(e(e2));
                eCFieldElement = d.multiply(e2.subtract(subtract)).subtract(e3);
                fromBigInteger = fromBigInteger.isOne() ? e : e.multiply(fromBigInteger);
                i2++;
                eCFieldElement2 = subtract;
            }
            if (coordinateSystem == 0) {
                ECFieldElement invert = fromBigInteger.invert();
                ECFieldElement square2 = invert.square();
                return new Fp(curve, eCFieldElement2.multiply(square2), eCFieldElement.multiply(square2.multiply(invert)));
            }
            if (coordinateSystem == 1) {
                return new Fp(curve, eCFieldElement2.multiply(fromBigInteger), eCFieldElement, new ECFieldElement[]{fromBigInteger.multiply(fromBigInteger.square())});
            }
            if (coordinateSystem == 2) {
                return new Fp(curve, eCFieldElement2, eCFieldElement, new ECFieldElement[]{fromBigInteger});
            }
            if (coordinateSystem == 4) {
                return new Fp(curve, eCFieldElement2, eCFieldElement, new ECFieldElement[]{fromBigInteger, a});
            }
            throw new IllegalStateException("unsupported coordinate system");
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint twice() {
            ECFieldElement eCFieldElement;
            ECFieldElement c;
            if (isInfinity()) {
                return this;
            }
            ECCurve curve = getCurve();
            ECFieldElement eCFieldElement2 = this.c;
            if (eCFieldElement2.isZero()) {
                return curve.getInfinity();
            }
            int coordinateSystem = curve.getCoordinateSystem();
            ECFieldElement eCFieldElement3 = this.b;
            if (coordinateSystem == 0) {
                ECFieldElement divide = d(eCFieldElement3.square()).add(getCurve().getA()).divide(e(eCFieldElement2));
                ECFieldElement subtract = divide.square().subtract(e(eCFieldElement3));
                return new Fp(curve, subtract, divide.multiply(eCFieldElement3.subtract(subtract)).subtract(eCFieldElement2));
            }
            if (coordinateSystem == 1) {
                ECFieldElement eCFieldElement4 = this.d[0];
                boolean isOne = eCFieldElement4.isOne();
                ECFieldElement a = curve.getA();
                if (!a.isZero() && !isOne) {
                    a = a.multiply(eCFieldElement4.square());
                }
                ECFieldElement add = a.add(d(eCFieldElement3.square()));
                ECFieldElement multiply = isOne ? eCFieldElement2 : eCFieldElement2.multiply(eCFieldElement4);
                ECFieldElement square = isOne ? eCFieldElement2.square() : multiply.multiply(eCFieldElement2);
                ECFieldElement c2 = c(eCFieldElement3.multiply(square));
                ECFieldElement subtract2 = add.square().subtract(e(c2));
                ECFieldElement e = e(multiply);
                ECFieldElement multiply2 = subtract2.multiply(e);
                ECFieldElement e2 = e(square);
                return new Fp(curve, multiply2, c2.subtract(subtract2).multiply(add).subtract(e(e2.square())), new ECFieldElement[]{e(isOne ? e(e2) : e.square()).multiply(multiply)});
            }
            if (coordinateSystem != 2) {
                if (coordinateSystem == 4) {
                    return a(true);
                }
                throw new IllegalStateException("unsupported coordinate system");
            }
            ECFieldElement eCFieldElement5 = this.d[0];
            boolean isOne2 = eCFieldElement5.isOne();
            ECFieldElement square2 = eCFieldElement2.square();
            ECFieldElement square3 = square2.square();
            ECFieldElement a2 = curve.getA();
            ECFieldElement negate = a2.negate();
            if (negate.toBigInteger().equals(BigInteger.valueOf(3L))) {
                ECFieldElement square4 = isOne2 ? eCFieldElement5 : eCFieldElement5.square();
                eCFieldElement = d(eCFieldElement3.add(square4).multiply(eCFieldElement3.subtract(square4)));
                c = c(square2.multiply(eCFieldElement3));
            } else {
                ECFieldElement d = d(eCFieldElement3.square());
                if (isOne2) {
                    eCFieldElement = d.add(a2);
                } else if (a2.isZero()) {
                    eCFieldElement = d;
                } else {
                    ECFieldElement square5 = eCFieldElement5.square().square();
                    eCFieldElement = negate.bitLength() < a2.bitLength() ? d.subtract(square5.multiply(negate)) : d.add(square5.multiply(a2));
                }
                c = c(eCFieldElement3.multiply(square2));
            }
            ECFieldElement subtract3 = eCFieldElement.square().subtract(e(c));
            ECFieldElement subtract4 = c.subtract(subtract3).multiply(eCFieldElement).subtract(b(square3));
            ECFieldElement e3 = e(eCFieldElement2);
            if (!isOne2) {
                e3 = e3.multiply(eCFieldElement5);
            }
            return new Fp(curve, subtract3, subtract4, new ECFieldElement[]{e3});
        }

        @Override // bc.org.bouncycastle.math.ec.ECPoint
        public ECPoint twicePlus(ECPoint eCPoint) {
            if (this == eCPoint) {
                return threeTimes();
            }
            if (isInfinity()) {
                return eCPoint;
            }
            if (eCPoint.isInfinity()) {
                return twice();
            }
            ECFieldElement eCFieldElement = this.c;
            if (eCFieldElement.isZero()) {
                return eCPoint;
            }
            ECCurve curve = getCurve();
            int coordinateSystem = curve.getCoordinateSystem();
            if (coordinateSystem != 0) {
                return coordinateSystem != 4 ? twice().add(eCPoint) : a(false).add(eCPoint);
            }
            ECFieldElement eCFieldElement2 = this.b;
            ECFieldElement eCFieldElement3 = eCPoint.b;
            ECFieldElement eCFieldElement4 = eCPoint.c;
            ECFieldElement subtract = eCFieldElement3.subtract(eCFieldElement2);
            ECFieldElement subtract2 = eCFieldElement4.subtract(eCFieldElement);
            if (subtract.isZero()) {
                return subtract2.isZero() ? threeTimes() : this;
            }
            ECFieldElement square = subtract.square();
            ECFieldElement subtract3 = square.multiply(e(eCFieldElement2).add(eCFieldElement3)).subtract(subtract2.square());
            if (subtract3.isZero()) {
                return curve.getInfinity();
            }
            ECFieldElement invert = subtract3.multiply(subtract).invert();
            ECFieldElement multiply = subtract3.multiply(invert).multiply(subtract2);
            ECFieldElement subtract4 = e(eCFieldElement).multiply(square).multiply(subtract).multiply(invert).subtract(multiply);
            ECFieldElement add = subtract4.subtract(multiply).multiply(multiply.add(subtract4)).add(eCFieldElement3);
            return new Fp(curve, add, eCFieldElement2.subtract(add).multiply(subtract4).subtract(eCFieldElement));
        }

        Fp(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
            super(eCCurve, eCFieldElement, eCFieldElement2, eCFieldElementArr);
        }

        protected ECFieldElement b(ECFieldElement eCFieldElement) {
            return c(e(eCFieldElement));
        }

        protected ECFieldElement b(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
            ECFieldElement a = getCurve().getA();
            if (a.isZero() || eCFieldElement.isOne()) {
                return a;
            }
            if (eCFieldElement2 == null) {
                eCFieldElement2 = eCFieldElement.square();
            }
            ECFieldElement square = eCFieldElement2.square();
            ECFieldElement negate = a.negate();
            if (negate.bitLength() < a.bitLength()) {
                return square.multiply(negate).negate();
            }
            return square.multiply(a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\ECPoint$a.smali */
    class a implements PreCompCallback {
        final /* synthetic */ boolean a;
        final /* synthetic */ boolean b;

        a(boolean z, boolean z2) {
            this.a = z;
            this.b = z2;
        }

        public PreCompInfo precompute(PreCompInfo preCompInfo) {
            d dVar = preCompInfo instanceof d ? (d) preCompInfo : null;
            if (dVar == null) {
                dVar = new d();
            }
            if (dVar.b()) {
                return dVar;
            }
            if (!dVar.a()) {
                if (!this.a && !ECPoint.this.g()) {
                    dVar.e();
                    return dVar;
                }
                dVar.d();
            }
            if (this.b && !dVar.c()) {
                if (!ECPoint.this.h()) {
                    dVar.e();
                    return dVar;
                }
                dVar.f();
            }
            return dVar;
        }
    }

    protected ECPoint(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        this(eCCurve, eCFieldElement, eCFieldElement2, a(eCCurve));
    }

    protected static ECFieldElement[] a(ECCurve eCCurve) {
        int coordinateSystem = eCCurve == null ? 0 : eCCurve.getCoordinateSystem();
        if (coordinateSystem == 0 || coordinateSystem == 5) {
            return f;
        }
        ECFieldElement fromBigInteger = eCCurve.fromBigInteger(ECConstants.ONE);
        switch (coordinateSystem) {
            case 1:
            case 2:
            case 6:
                return new ECFieldElement[]{fromBigInteger};
            case 3:
                return new ECFieldElement[]{fromBigInteger, fromBigInteger, fromBigInteger};
            case 4:
                return new ECFieldElement[]{fromBigInteger, eCCurve.getA()};
            case 5:
            default:
                throw new IllegalArgumentException("unknown coordinate system");
        }
    }

    public abstract ECPoint add(ECPoint eCPoint);

    protected abstract ECPoint b();

    protected abstract boolean c();

    protected int d() {
        ECCurve eCCurve = this.a;
        if (eCCurve == null) {
            return 0;
        }
        return eCCurve.getCoordinateSystem();
    }

    protected final ECFieldElement[] e() {
        return this.d;
    }

    public boolean equals(ECPoint eCPoint) {
        ECPoint eCPoint2;
        if (eCPoint == null) {
            return false;
        }
        ECCurve curve = getCurve();
        ECCurve curve2 = eCPoint.getCurve();
        boolean z = curve == null;
        boolean z2 = curve2 == null;
        boolean isInfinity = isInfinity();
        boolean isInfinity2 = eCPoint.isInfinity();
        if (isInfinity || isInfinity2) {
            if (isInfinity && isInfinity2) {
                return z || z2 || curve.equals(curve2);
            }
            return false;
        }
        if (z && z2) {
            eCPoint2 = this;
        } else if (z) {
            eCPoint = eCPoint.normalize();
            eCPoint2 = this;
        } else if (z2) {
            eCPoint2 = normalize();
        } else {
            if (!curve.equals(curve2)) {
                return false;
            }
            ECPoint[] eCPointArr = {this, curve.importPoint(eCPoint)};
            curve.normalizeAll(eCPointArr);
            eCPoint2 = eCPointArr[0];
            eCPoint = eCPointArr[1];
        }
        return eCPoint2.getXCoord().equals(eCPoint.getXCoord()) && eCPoint2.getYCoord().equals(eCPoint.getYCoord());
    }

    boolean f() {
        return a(false, false);
    }

    protected abstract boolean g();

    public ECFieldElement getAffineXCoord() {
        a();
        return getXCoord();
    }

    public ECFieldElement getAffineYCoord() {
        a();
        return getYCoord();
    }

    public ECCurve getCurve() {
        return this.a;
    }

    public final ECPoint getDetachedPoint() {
        return normalize().b();
    }

    public byte[] getEncoded(boolean z) {
        if (isInfinity()) {
            return new byte[1];
        }
        ECPoint normalize = normalize();
        byte[] encoded = normalize.getXCoord().getEncoded();
        if (z) {
            byte[] bArr = new byte[encoded.length + 1];
            bArr[0] = (byte) (normalize.c() ? 3 : 2);
            System.arraycopy(encoded, 0, bArr, 1, encoded.length);
            return bArr;
        }
        byte[] encoded2 = normalize.getYCoord().getEncoded();
        byte[] bArr2 = new byte[encoded.length + encoded2.length + 1];
        bArr2[0] = 4;
        System.arraycopy(encoded, 0, bArr2, 1, encoded.length);
        System.arraycopy(encoded2, 0, bArr2, encoded.length + 1, encoded2.length);
        return bArr2;
    }

    public final ECFieldElement getRawXCoord() {
        return this.b;
    }

    public final ECFieldElement getRawYCoord() {
        return this.c;
    }

    public ECFieldElement getXCoord() {
        return this.b;
    }

    public ECFieldElement getYCoord() {
        return this.c;
    }

    public ECFieldElement getZCoord(int i) {
        if (i >= 0) {
            ECFieldElement[] eCFieldElementArr = this.d;
            if (i < eCFieldElementArr.length) {
                return eCFieldElementArr[i];
            }
        }
        return null;
    }

    public ECFieldElement[] getZCoords() {
        ECFieldElement[] eCFieldElementArr = this.d;
        int length = eCFieldElementArr.length;
        if (length == 0) {
            return f;
        }
        ECFieldElement[] eCFieldElementArr2 = new ECFieldElement[length];
        System.arraycopy(eCFieldElementArr, 0, eCFieldElementArr2, 0, length);
        return eCFieldElementArr2;
    }

    protected boolean h() {
        BigInteger order;
        return ECConstants.ONE.equals(this.a.getCofactor()) || (order = this.a.getOrder()) == null || ECAlgorithms.referenceMultiply(this, order).isInfinity();
    }

    public int hashCode() {
        ECCurve curve = getCurve();
        int i = curve == null ? 0 : ~curve.hashCode();
        if (isInfinity()) {
            return i;
        }
        ECPoint normalize = normalize();
        return (i ^ (normalize.getXCoord().hashCode() * 17)) ^ (normalize.getYCoord().hashCode() * InputDeviceCompat.SOURCE_KEYBOARD);
    }

    public boolean isInfinity() {
        if (this.b != null && this.c != null) {
            ECFieldElement[] eCFieldElementArr = this.d;
            if (eCFieldElementArr.length <= 0 || !eCFieldElementArr[0].isZero()) {
                return false;
            }
        }
        return true;
    }

    public boolean isNormalized() {
        int d = d();
        return d == 0 || d == 5 || isInfinity() || this.d[0].isOne();
    }

    public boolean isValid() {
        return a(false, true);
    }

    public ECPoint multiply(BigInteger bigInteger) {
        return getCurve().getMultiplier().multiply(this, bigInteger);
    }

    public abstract ECPoint negate();

    public ECPoint normalize() {
        int d;
        if (isInfinity() || (d = d()) == 0 || d == 5) {
            return this;
        }
        ECFieldElement zCoord = getZCoord(0);
        if (zCoord.isOne()) {
            return this;
        }
        if (this.a == null) {
            throw new IllegalStateException("Detached points must be in affine coordinates");
        }
        ECFieldElement randomFieldElementMult = this.a.randomFieldElementMult(t1.b());
        return a(zCoord.multiply(randomFieldElementMult).invert().multiply(randomFieldElementMult));
    }

    public ECPoint scaleX(ECFieldElement eCFieldElement) {
        return isInfinity() ? this : getCurve().a(getRawXCoord().multiply(eCFieldElement), getRawYCoord(), e());
    }

    public ECPoint scaleXNegateY(ECFieldElement eCFieldElement) {
        return isInfinity() ? this : getCurve().a(getRawXCoord().multiply(eCFieldElement), getRawYCoord().negate(), e());
    }

    public ECPoint scaleY(ECFieldElement eCFieldElement) {
        return isInfinity() ? this : getCurve().a(getRawXCoord(), getRawYCoord().multiply(eCFieldElement), e());
    }

    public ECPoint scaleYNegateX(ECFieldElement eCFieldElement) {
        return isInfinity() ? this : getCurve().a(getRawXCoord().negate(), getRawYCoord().multiply(eCFieldElement), e());
    }

    public abstract ECPoint subtract(ECPoint eCPoint);

    public ECPoint threeTimes() {
        return twicePlus(this);
    }

    public ECPoint timesPow2(int i) {
        if (i < 0) {
            throw new IllegalArgumentException("'e' cannot be negative");
        }
        ECPoint eCPoint = this;
        while (true) {
            i--;
            if (i < 0) {
                return eCPoint;
            }
            eCPoint = eCPoint.twice();
        }
    }

    public String toString() {
        if (isInfinity()) {
            return "INF";
        }
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append('(');
        stringBuffer.append(getRawXCoord());
        stringBuffer.append(',');
        stringBuffer.append(getRawYCoord());
        for (int i = 0; i < this.d.length; i++) {
            stringBuffer.append(',');
            stringBuffer.append(this.d[i]);
        }
        stringBuffer.append(')');
        return stringBuffer.toString();
    }

    public abstract ECPoint twice();

    public ECPoint twicePlus(ECPoint eCPoint) {
        return twice().add(eCPoint);
    }

    protected ECPoint(ECCurve eCCurve, ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2, ECFieldElement[] eCFieldElementArr) {
        this.e = null;
        this.a = eCCurve;
        this.b = eCFieldElement;
        this.c = eCFieldElement2;
        this.d = eCFieldElementArr;
    }

    protected void a() {
        if (!isNormalized()) {
            throw new IllegalStateException("point not in normal form");
        }
    }

    ECPoint a(ECFieldElement eCFieldElement) {
        switch (d()) {
            case 1:
            case 6:
                return a(eCFieldElement, eCFieldElement);
            case 2:
            case 3:
            case 4:
                ECFieldElement square = eCFieldElement.square();
                return a(square, square.multiply(eCFieldElement));
            case 5:
            default:
                throw new IllegalStateException("not a projective coordinate system");
        }
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof ECPoint) {
            return equals((ECPoint) obj);
        }
        return false;
    }

    protected ECPoint a(ECFieldElement eCFieldElement, ECFieldElement eCFieldElement2) {
        return getCurve().a(getRawXCoord().multiply(eCFieldElement), getRawYCoord().multiply(eCFieldElement2));
    }

    boolean a(boolean z, boolean z2) {
        if (isInfinity()) {
            return true;
        }
        return !((d) getCurve().precompute(this, "bc_validity", new a(z, z2))).b();
    }
}
