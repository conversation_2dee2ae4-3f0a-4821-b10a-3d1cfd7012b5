package o.f;

import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\f.smali */
public final class f extends i {
    private static int d = 0;
    private static int a = 1;

    public f(e.d dVar, Date date, d dVar2) {
        super(dVar, date, dVar2);
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = (a + 86) - 1;
        d = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case true:
                o.i.f f = o.i.d.c().f();
                int i2 = (a + Opcodes.IAND) - 1;
                d = i2 % 128;
                switch (i2 % 2 != 0 ? '4' : (char) 6) {
                    case '4':
                        throw null;
                    default:
                        return f;
                }
            default:
                o.i.d.c().f();
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final String c() {
        int i = (d + 10) - 1;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = (i2 & 69) + (i2 | 69);
        d = i4 % 128;
        switch (i4 % 2 != 0 ? '/' : 'K') {
            case '/':
                throw null;
            default:
                return null;
        }
    }

    @Override // o.f.e
    public final String j() {
        int i = d + 19;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = (i2 ^ 89) + ((i2 & 89) << 1);
        d = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.f.e
    public final byte[] d() {
        int i = a + 23;
        d = i % 128;
        int i2 = i % 2;
        byte[] e = l().b().e();
        int i3 = a;
        int i4 = ((i3 | 81) << 1) - (i3 ^ 81);
        d = i4 % 128;
        int i5 = i4 % 2;
        return e;
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = a;
        int i2 = ((i | 87) << 1) - (i ^ 87);
        d = i2 % 128;
        switch (i2 % 2 != 0 ? '`' : 'X') {
            case Opcodes.IADD /* 96 */:
                l().b().e();
                throw null;
            default:
                return l().b().e();
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = (a + 14) - 1;
        d = i % 128;
        int i2 = i % 2;
        byte[] a2 = l().b().a();
        int i3 = (a + 102) - 1;
        d = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 7 : ':') {
            case Opcodes.ASTORE /* 58 */:
                return a2;
            default:
                int i4 = 58 / 0;
                return a2;
        }
    }
}
