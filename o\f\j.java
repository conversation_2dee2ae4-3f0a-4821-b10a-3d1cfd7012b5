package o.f;

import android.os.Process;
import android.os.SystemClock;
import android.view.Gravity;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.util.Date;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.f.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\f\j.smali */
public final class j extends i {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int b;
    private static int[] c;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        f();
        SystemClock.currentThreadTimeMillis();
        int i = d + 83;
        b = i % 128;
        int i2 = i % 2;
    }

    static void f() {
        c = new int[]{1625006881, -1709686281, 71083916, 1187012453, 402675952, 1653748458, 42608811, -1322298699, -166981827, 439157012, 1815947812, 1437739523, 412111006, 235336431, 1334800191, 1223242915, 162709365, -1868393297};
    }

    static void init$0() {
        $$a = new byte[]{13, -73, -57, -113};
        $$b = 235;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.f.j.$$a
            int r8 = 116 - r8
            int r6 = r6 * 2
            int r6 = 1 - r6
            int r7 = r7 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L33
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r8
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r3 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L33:
            int r7 = -r7
            int r7 = r7 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.j.k(short, int, short, java.lang.Object[]):void");
    }

    public j(e.d dVar, Date date, d dVar2) {
        super(dVar, date, dVar2);
    }

    @Override // o.f.e
    public final o.i.f b() {
        int i = d + 11;
        b = i % 128;
        int i2 = i % 2;
        o.i.f fVar = o.i.f.b;
        int i3 = b + 45;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                int i4 = 94 / 0;
                return fVar;
            default:
                return fVar;
        }
    }

    @Override // o.f.e
    public final String j() {
        int i = d + 59;
        int i2 = i % 128;
        b = i2;
        int i3 = i % 2;
        int i4 = i2 + Opcodes.DDIV;
        d = i4 % 128;
        int i5 = i4 % 2;
        return null;
    }

    @Override // o.f.e
    public final String c() {
        StringBuilder append = new StringBuilder().append(b().toString());
        Object[] objArr = new Object[1];
        h(new int[]{1732923717, 84602766}, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr);
        StringBuilder append2 = append.append(((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        h(new int[]{-975464184, -253295100}, 2 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr2);
        StringBuilder append3 = append2.append(((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        h(new int[]{1732923717, 84602766}, 1 - Gravity.getAbsoluteGravity(0, 0), objArr3);
        String obj = append3.append(((String) objArr3[0]).intern()).toString();
        int i = d + 35;
        b = i % 128;
        int i2 = i % 2;
        return obj;
    }

    @Override // o.f.e
    public final byte[] d() {
        int i = b + 37;
        d = i % 128;
        int i2 = i % 2;
        byte[] a = l().b().a();
        int i3 = b + Opcodes.LNEG;
        d = i3 % 128;
        int i4 = i3 % 2;
        return a;
    }

    @Override // o.f.e
    public final byte[] e() {
        int i = b + 47;
        d = i % 128;
        Object obj = null;
        switch (i % 2 != 0 ? '[' : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                byte[] a = l().b().a();
                int i2 = b + Opcodes.DREM;
                d = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        return a;
                    default:
                        obj.hashCode();
                        throw null;
                }
            default:
                l().b().a();
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.f.e
    public final byte[] a() {
        int i = b + 13;
        d = i % 128;
        switch (i % 2 == 0) {
            case true:
                return l().b().a();
            default:
                l().b().a();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(int[] r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 882
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.f.j.h(int[], int, java.lang.Object[]):void");
    }
}
