package o.dg;

import android.content.Context;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dg\c.smali */
public abstract class c {
    private static int a = 0;
    private static int e = 1;

    public abstract a e(Context context, o.eg.b bVar, Long l);

    public final int hashCode() {
        int i = a;
        int i2 = ((i | 25) << 1) - (i ^ 25);
        e = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                int hashCode = super.hashCode();
                int i3 = (e + 66) - 1;
                a = i3 % 128;
                int i4 = i3 % 2;
                return hashCode;
            default:
                super.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = a + 87;
        e = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = e;
        int i4 = ((i3 | 73) << 1) - (i3 ^ 73);
        a = i4 % 128;
        int i5 = i4 % 2;
        return equals;
    }

    public final String toString() {
        int i = e;
        int i2 = (i & Opcodes.LMUL) + (i | Opcodes.LMUL);
        a = i2 % 128;
        int i3 = i2 % 2;
        String obj = super.toString();
        int i4 = a;
        int i5 = ((i4 | 53) << 1) - (i4 ^ 53);
        e = i5 % 128;
        int i6 = i5 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i = (a + 10) - 1;
        e = i % 128;
        int i2 = i % 2;
        super.finalize();
        int i3 = a + 79;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
