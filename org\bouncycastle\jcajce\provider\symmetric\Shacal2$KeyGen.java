package org.bouncycastle.jcajce.provider.symmetric;

import org.bouncycastle.crypto.CipherKeyGenerator;
import org.bouncycastle.jcajce.provider.symmetric.util.BaseKeyGenerator;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\jcajce\provider\symmetric\Shacal2$KeyGen.smali */
public class Shacal2$KeyGen extends BaseKeyGenerator {
    public Shacal2$KeyGen() {
        super("SHACAL-2", 128, new CipherKeyGenerator());
    }
}
