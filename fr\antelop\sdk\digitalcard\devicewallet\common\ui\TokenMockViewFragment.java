package fr.antelop.sdk.digitalcard.devicewallet.common.ui;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.fragment.app.Fragment;
import com.google.android.material.button.MaterialButton;
import fr.antelop.sdk.R;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.CardMocksAdapter;
import o.ep.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\TokenMockViewFragment.smali */
public final class TokenMockViewFragment extends Fragment {
    private final CardMocksAdapter.CardMocksAdapterCallback cardMocksAdapterCallback;
    private MaterialButton deleteButton;
    private TextView lastDigitsTextview;
    private final e token;

    @Override // androidx.fragment.app.Fragment
    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        View inflate = layoutInflater.inflate(R.layout.token_mock_view_fragment, viewGroup, false);
        this.lastDigitsTextview = (TextView) inflate.findViewById(R.id.lastdigits_textview);
        this.deleteButton = (MaterialButton) inflate.findViewById(R.id.delete_button);
        return inflate;
    }

    @Override // androidx.fragment.app.Fragment
    public final void onViewCreated(View view, Bundle bundle) {
        super.onViewCreated(view, bundle);
        this.lastDigitsTextview.setText(getString(R.string.antelopLastDigitsCanvasText, this.token.d()));
        this.deleteButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.TokenMockViewFragment$$ExternalSyntheticLambda0
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                TokenMockViewFragment.this.m227x8491ed09(view2);
            }
        });
    }

    /* renamed from: lambda$onViewCreated$0$fr-antelop-sdk-digitalcard-devicewallet-common-ui-TokenMockViewFragment, reason: not valid java name */
    /* synthetic */ void m227x8491ed09(View view) {
        this.cardMocksAdapterCallback.deleteToken(this.token);
    }

    public TokenMockViewFragment(e eVar, CardMocksAdapter.CardMocksAdapterCallback cardMocksAdapterCallback) {
        this.token = eVar;
        this.cardMocksAdapterCallback = cardMocksAdapterCallback;
    }
}
