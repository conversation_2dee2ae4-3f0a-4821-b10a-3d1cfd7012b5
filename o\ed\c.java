package o.ed;

import android.content.Context;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.R;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcessActivityCallback;
import fr.antelop.sdk.digitalcard.SecurePinInput;
import java.lang.reflect.Method;
import java.util.Arrays;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.dw.j;
import o.ee.g;
import org.bouncycastle.i18n.LocalizedMessage;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c.smali */
public final class c extends j {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static char[] g;
    private static /* synthetic */ boolean j;
    private static int l;
    private static boolean m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static boolean f59o;
    private final e a;
    private final b b;
    private d c;
    private final C0039c d;
    private final CustomerAuthenticatedProcessActivityCallback e;
    private byte[] h;
    private byte[] i;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c$b.smali */
    public interface b {
        void b();

        void c(byte[] bArr, byte[] bArr2);
    }

    static void c() {
        g = new char[]{61593, 61616, 61609, 61630, 61622, 61611, 61626, 61610, 61590, 61613, 61620, 61623, 61617, 61592, 61631, 61585, 61618, 61627, 61577, 61625, 61798, 61595, 61605};
        m = true;
        f59o = true;
        f = 782102854;
    }

    static void init$0() {
        $$a = new byte[]{123, Tnaf.POW_2_WIDTH, 2, 97};
        $$b = Opcodes.IFNULL;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void r(short r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = 121 - r7
            int r8 = r8 * 3
            int r8 = r8 + 4
            int r9 = r9 * 2
            int r9 = r9 + 1
            byte[] r0 = o.ed.c.$$a
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r8
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L32
        L18:
            r3 = r2
        L19:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r9) goto L28
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L28:
            r3 = r0[r8]
            r6 = r9
            r9 = r7
            r7 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L32:
            int r8 = -r8
            int r8 = r8 + r9
            int r7 = r7 + 1
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ed.c.r(short, byte, int, java.lang.Object[]):void");
    }

    @Override // o.dw.j
    public final void onDisplayImpossible(o.bv.c cVar) {
        int i = n + 39;
        l = i % 128;
        int i2 = i % 2;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x0038. Please report as an issue. */
    static {
        init$0();
        $10 = 0;
        $11 = 1;
        n = 0;
        l = 1;
        c();
        TextUtils.indexOf((CharSequence) "", '0', 0);
        switch (1) {
            case 0:
                int i = n + Opcodes.DREM;
                l = i % 128;
                int i2 = i % 2;
                int i3 = 2 % 2;
                break;
            default:
                int i4 = l + 109;
                n = i4 % 128;
                switch (i4 % 2 != 0 ? 'E' : '3') {
                }
        }
        j = true;
        int i5 = l + Opcodes.LNEG;
        n = i5 % 128;
        int i6 = i5 % 2;
    }

    public c(b bVar, C0039c c0039c, e eVar, CustomerAuthenticatedProcessActivityCallback customerAuthenticatedProcessActivityCallback) {
        this.b = bVar;
        this.d = c0039c;
        this.a = eVar;
        if (c0039c != null) {
            this.c = d.d;
        } else if (eVar != null) {
            this.c = d.c;
        } else {
            Object[] objArr = new Object[1];
            q(null, (Process.myTid() >> 22) + 127, null, "\u0088\u0083\u0087\u0086\u0083\u0085\u0084\u0083\u0082\u0081", objArr);
            throw new RuntimeException(((String) objArr[0]).intern());
        }
        this.e = customerAuthenticatedProcessActivityCallback;
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c$d.smali */
    static final class d {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final d a;
        private static final /* synthetic */ d[] b;
        public static final d c;
        public static final d d;
        public static final d e;
        private static int g;
        private static int[] i;
        private static int j;

        static void e() {
            i = new int[]{1737926618, -1549519875, -1075996251, -1139996846, -487696533, 1337229237, 2072318955, -1425324042, -80974682, 2037188545, 794577153, -717551039, -2055058485, 733254948, 472276997, -85543974, 1397237096, -259962308};
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0034). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(byte r6, int r7, byte r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.ed.c.d.$$a
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r7 = r7 * 2
                int r7 = 3 - r7
                int r6 = 116 - r6
                byte[] r1 = new byte[r8]
                r2 = 0
                if (r0 != 0) goto L18
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                r8 = r7
                goto L34
            L18:
                r3 = r2
            L19:
                byte r4 = (byte) r6
                int r7 = r7 + 1
                r1[r3] = r4
                int r3 = r3 + 1
                if (r3 != r8) goto L2a
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L2a:
                r4 = r0[r7]
                r5 = r8
                r8 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L34:
                int r6 = r6 + r7
                r7 = r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ed.c.d.h(byte, int, byte, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{117, 56, 99, 31};
            $$b = 233;
        }

        private d(String str, int i2) {
        }

        private static /* synthetic */ d[] d() {
            int i2 = j + 83;
            g = i2 % 128;
            switch (i2 % 2 == 0) {
                case true:
                    d[] dVarArr = new d[3];
                    dVarArr[0] = d;
                    dVarArr[1] = c;
                    dVarArr[3] = e;
                    dVarArr[3] = a;
                    return dVarArr;
                default:
                    return new d[]{d, c, e, a};
            }
        }

        public static d valueOf(String str) {
            int i2 = j + 37;
            g = i2 % 128;
            char c2 = i2 % 2 == 0 ? '_' : (char) 15;
            d dVar = (d) Enum.valueOf(d.class, str);
            switch (c2) {
                case 15:
                    int i3 = j + 37;
                    g = i3 % 128;
                    int i4 = i3 % 2;
                    return dVar;
                default:
                    throw null;
            }
        }

        public static d[] values() {
            int i2 = g + Opcodes.LSHR;
            j = i2 % 128;
            int i3 = i2 % 2;
            d[] dVarArr = (d[]) b.clone();
            int i4 = j + Opcodes.LUSHR;
            g = i4 % 128;
            switch (i4 % 2 == 0 ? '/' : (char) 11) {
                case 11:
                    return dVarArr;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            g = 1;
            e();
            Object[] objArr = new Object[1];
            f(new int[]{-1520266208, -628470263, 1613432667, -257972198, 1973673911, -515173738, -1958271182, 1034510358, -952394158, -559561762}, 17 - View.combineMeasuredStates(0, 0), objArr);
            d = new d(((String) objArr[0]).intern(), 0);
            Object[] objArr2 = new Object[1];
            f(new int[]{-1520266208, -628470263, -31520755, 872131285, 911963486, -683724150, -952394158, -559561762}, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 12, objArr2);
            c = new d(((String) objArr2[0]).intern(), 1);
            Object[] objArr3 = new Object[1];
            f(new int[]{-1520266208, -628470263, -31520755, 872131285, 911963486, -683724150, 1958669394, 1983321790, 815503631, -1039463170}, (ViewConfiguration.getScrollDefaultDelay() >> 16) + 20, objArr3);
            e = new d(((String) objArr3[0]).intern(), 2);
            Object[] objArr4 = new Object[1];
            f(new int[]{-1520266208, -628470263, -31520755, 872131285, 911963486, -683724150, 1738449478, 1887287248, 762198056, -163658665, -731790629, 1582554067}, 22 - TextUtils.lastIndexOf("", '0', 0, 0), objArr4);
            a = new d(((String) objArr4[0]).intern(), 3);
            b = d();
            int i2 = j + 5;
            g = i2 % 128;
            int i3 = i2 % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void f(int[] r25, int r26, java.lang.Object[] r27) {
            /*
                Method dump skipped, instructions count: 846
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ed.c.d.f(int[], int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.ed.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c$c.smali */
    public static class C0039c {
        final String b;
        final String c;

        public C0039c(SecurePinInput.CurrentPinInputProperties currentPinInputProperties) {
            this.b = currentPinInputProperties.getTitle();
            this.c = currentPinInputProperties.getSubtitle();
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c$e.smali */
    public static class e {
        protected final String a;
        protected final String b;
        protected final boolean c;
        protected final String d;
        protected final String e;
        protected final String i;

        private e(String str, String str2, boolean z, String str3, String str4, String str5) {
            this.d = str;
            this.a = str2;
            this.c = z;
            this.e = str3;
            this.b = str4;
            this.i = str5;
        }

        public e(Context context, SecurePinInput.NewPinInputProperties newPinInputProperties) {
            this(newPinInputProperties.getTitle(), newPinInputProperties.getSubtitle(), newPinInputProperties.getRequestConfirmation(), newPinInputProperties.getConfirmationTitle(), newPinInputProperties.getConfirmationSubtitle(), context.getString(R.string.antelopSecurePinInputPinsNotMatchingErrorDescription));
        }
    }

    public final void b(Context context) {
        int i = n + 47;
        l = i % 128;
        int i2 = i % 2;
        o.ed.d.b(context, this, b());
        int i3 = n + 17;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.dw.j
    public final void onDisplayCancelled() {
        int i = n + Opcodes.LSHL;
        l = i % 128;
        switch (i % 2 == 0 ? '\t' : '2') {
            case '2':
                this.b.b();
                return;
            default:
                this.b.b();
                throw null;
        }
    }

    @Override // o.dw.j
    public final void onDisplaySuccess() {
        int i = l + Opcodes.LUSHR;
        n = i % 128;
        int i2 = i % 2;
        this.b.c(this.i, this.h);
        int i3 = n + 43;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    public final boolean b(byte[] bArr, o.ed.d dVar) {
        int i = n + 95;
        l = i % 128;
        int i2 = i % 2;
        boolean a = a(bArr, dVar);
        int i3 = l + 35;
        n = i3 % 128;
        switch (i3 % 2 != 0 ? Typography.quote : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return a;
            default:
                throw null;
        }
    }

    /* renamed from: o.ed.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ed\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] e;

        static {
            a = 0;
            c = 1;
            int[] iArr = new int[d.values().length];
            e = iArr;
            try {
                iArr[d.d.ordinal()] = 1;
                int i = (a + 80) - 1;
                c = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                e[d.c.ordinal()] = 2;
                int i3 = a;
                int i4 = (i3 ^ 93) + ((i3 & 93) << 1);
                c = i4 % 128;
                if (i4 % 2 == 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                e[d.a.ordinal()] = 3;
                int i5 = c;
                int i6 = (i5 ^ 79) + ((i5 & 79) << 1);
                a = i6 % 128;
                if (i6 % 2 == 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                e[d.e.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    private boolean a(byte[] bArr, o.ed.d dVar) {
        int i = l + 13;
        n = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        Object obj = null;
        q(null, KeyEvent.normalizeMetaState(0) + 127, null, "\u0091\u0083\u0088\u008c\u0090\u008f\u0083\u008a\u008e\u0087\u0085\u008d\u008c\u008b\u0089\u0082\u008a\u0089", objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        q(null, TextUtils.indexOf("", "", 0) + 127, null, "\u0082\u008a\u0089\u0088\u0083\u008b\u0083\u0087\u0082\u0093\u0092\u0092\u0083\u0086\u008c\u008b\u0085", objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        switch (AnonymousClass4.e[this.c.ordinal()]) {
            case 1:
                if (!j && this.d == null) {
                    throw new AssertionError();
                }
                this.i = bArr;
                if (this.a == null) {
                    return true;
                }
                int i3 = n + Opcodes.LNEG;
                l = i3 % 128;
                if (i3 % 2 != 0) {
                    this.c = d.c;
                    dVar.a(b());
                    return false;
                }
                this.c = d.c;
                dVar.a(b());
                throw null;
            case 2:
            case 3:
                if (!j) {
                    int i4 = l + 3;
                    n = i4 % 128;
                    if (i4 % 2 != 0) {
                        obj.hashCode();
                        throw null;
                    }
                    if (this.a == null) {
                        throw new AssertionError();
                    }
                }
                this.h = bArr;
                switch (this.a.c ? ' ' : 'V') {
                    case ' ':
                        this.c = d.e;
                        dVar.a(b());
                        return false;
                }
            case 4:
                switch (!Arrays.equals(bArr, this.h)) {
                    case false:
                        break;
                    default:
                        this.c = d.a;
                        dVar.a(b());
                        return false;
                }
            default:
                return false;
        }
        return true;
    }

    private o.ed.e b() {
        switch (AnonymousClass4.e[this.c.ordinal()]) {
            case 1:
                if (!j) {
                    int i = n + 99;
                    l = i % 128;
                    int i2 = i % 2;
                    if (this.d == null) {
                        throw new AssertionError();
                    }
                }
                return new o.ed.e(this.d.b, this.d.c, null, false, this.e);
            case 2:
                switch (!j ? '\'' : (char) 2) {
                    case '\'':
                        int i3 = n + 29;
                        l = i3 % 128;
                        switch (i3 % 2 == 0 ? (char) 26 : '_') {
                            case Opcodes.SWAP /* 95 */:
                                if (this.a == null) {
                                    throw new AssertionError();
                                }
                                break;
                            default:
                                throw null;
                        }
                }
                return new o.ed.e(this.a.d, this.a.a, null, false, this.e);
            case 3:
                if (!j) {
                    int i4 = n + 51;
                    l = i4 % 128;
                    int i5 = i4 % 2;
                    if (this.a == null) {
                        throw new AssertionError();
                    }
                }
                return new o.ed.e(this.a.d, this.a.a, this.a.i, true, this.e);
            case 4:
                switch (!j ? Typography.less : 'Y') {
                    case '<':
                        int i6 = l + 5;
                        n = i6 % 128;
                        int i7 = i6 % 2;
                        if (this.a == null) {
                            throw new AssertionError();
                        }
                        break;
                }
                return new o.ed.e(this.a.e, this.a.b, null, false, this.e);
            default:
                Object[] objArr = new Object[1];
                q(null, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.IAND, null, "\u0083\u0087\u0097\u0087\u0096\u0095\u0088\u0083\u0087\u008b\u008c\u0085\u0085\u0094\u0092\u0082\u0081", objArr);
                throw new RuntimeException(((String) objArr[0]).intern());
        }
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r1v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r1v1 */
    /* JADX WARN: Type inference failed for: r1v25, types: [byte[]] */
    private static void q(String str, int i, int[] iArr, String str2, Object[] objArr) {
        char[] charArray;
        int length;
        char[] cArr;
        int i2;
        ?? r1 = str2;
        switch (r1 != 0 ? (char) 29 : Typography.less) {
            case 29:
                r1 = r1.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r1;
        switch (str == null) {
            case false:
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr2 = charArray;
        o.a.j jVar = new o.a.j();
        char[] cArr3 = g;
        long j2 = 0;
        if (cArr3 != null) {
            int i3 = $10 + Opcodes.LSHL;
            $11 = i3 % 128;
            if (i3 % 2 == 0) {
                length = cArr3.length;
                cArr = new char[length];
                i2 = 0;
            } else {
                length = cArr3.length;
                cArr = new char[length];
                i2 = 0;
            }
            while (true) {
                switch (i2 < length) {
                    case false:
                        cArr3 = cArr;
                        break;
                    default:
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr3[i2])};
                            Object obj = o.e.a.s.get(1085633688);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(11 - (KeyEvent.getMaxKeyCode() >> 16), (char) (ImageFormat.getBitsPerPixel(0) + 1), 493 - ExpandableListView.getPackedPositionType(j2));
                                byte b2 = (byte) 0;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                r(b2, b3, b3, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(1085633688, obj);
                            }
                            cArr[i2] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i2++;
                            j2 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(f)};
            Object obj2 = o.e.a.s.get(-1667314477);
            if (obj2 == null) {
                Class cls2 = (Class) o.e.a.c(ExpandableListView.getPackedPositionType(0L) + 10, (char) (8856 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), 323 - Process.getGidForName(""));
                byte b4 = (byte) ($$a[2] + 1);
                byte b5 = (byte) (b4 - 3);
                Object[] objArr5 = new Object[1];
                r(b4, b5, b5, objArr5);
                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                o.e.a.s.put(-1667314477, obj2);
            }
            int intValue = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
            if (f59o) {
                jVar.e = bArr.length;
                char[] cArr4 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    cArr4[jVar.c] = (char) (cArr3[bArr[(jVar.e - 1) - jVar.c] + i] - intValue);
                    try {
                        Object[] objArr6 = {jVar, jVar};
                        Object obj3 = o.e.a.s.get(745816316);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 9, (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 1), View.combineMeasuredStates(0, 0) + 207);
                            byte length2 = (byte) $$a.length;
                            byte b6 = (byte) (length2 - 4);
                            Object[] objArr7 = new Object[1];
                            r(length2, b6, b6, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(745816316, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(cArr4);
                return;
            }
            switch (m ? '5' : 'c') {
                case Opcodes.DADD /* 99 */:
                    jVar.e = iArr.length;
                    char[] cArr5 = new char[jVar.e];
                    jVar.c = 0;
                    int i4 = $10 + Opcodes.LNEG;
                    $11 = i4 % 128;
                    switch (i4 % 2 == 0) {
                    }
                    while (true) {
                        switch (jVar.c < jVar.e ? '-' : '4') {
                            case '4':
                                objArr[0] = new String(cArr5);
                                return;
                            default:
                                cArr5[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                jVar.c++;
                        }
                    }
                default:
                    jVar.e = cArr2.length;
                    char[] cArr6 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        int i5 = $10 + 69;
                        $11 = i5 % 128;
                        if (i5 % 2 == 0) {
                            cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e % 1) / jVar.c] + i] >> intValue);
                            try {
                                Object[] objArr8 = {jVar, jVar};
                                Object obj4 = o.e.a.s.get(745816316);
                                if (obj4 == null) {
                                    Class cls4 = (Class) o.e.a.c((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 10, (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), TextUtils.indexOf("", "", 0) + 207);
                                    byte length3 = (byte) $$a.length;
                                    byte b7 = (byte) (length3 - 4);
                                    Object[] objArr9 = new Object[1];
                                    r(length3, b7, b7, objArr9);
                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    o.e.a.s.put(745816316, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr8);
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                        } else {
                            cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i] - intValue);
                            try {
                                Object[] objArr10 = {jVar, jVar};
                                Object obj5 = o.e.a.s.get(745816316);
                                if (obj5 == null) {
                                    Class cls5 = (Class) o.e.a.c(10 - (ViewConfiguration.getTapTimeout() >> 16), (char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 207 - TextUtils.indexOf("", "", 0, 0));
                                    byte length4 = (byte) $$a.length;
                                    byte b8 = (byte) (length4 - 4);
                                    Object[] objArr11 = new Object[1];
                                    r(length4, b8, b8, objArr11);
                                    obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                    o.e.a.s.put(745816316, obj5);
                                }
                                ((Method) obj5).invoke(null, objArr10);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        }
                    }
                    objArr[0] = new String(cArr6);
                    return;
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
