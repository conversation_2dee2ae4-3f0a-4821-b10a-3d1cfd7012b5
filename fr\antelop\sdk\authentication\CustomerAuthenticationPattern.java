package fr.antelop.sdk.authentication;

import o.ee.o;
import o.s.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\authentication\CustomerAuthenticationPattern.smali */
public final class CustomerAuthenticationPattern {
    public static final String LOW = "low";
    public static final String MEDIUM = "medium";
    public static final String NO_AUTHENTICATION = "no_authentication";
    public static final String STRONG = "strong";
    private final c authenticationPattern;

    public CustomerAuthenticationPattern(c cVar) {
        this.authenticationPattern = cVar;
    }

    public final String getName() {
        return this.authenticationPattern.d();
    }

    public final CustomerAuthenticationMethodType[] getStep(int i) {
        return (CustomerAuthenticationMethodType[]) o.b(CustomerAuthenticationMethodType.class, this.authenticationPattern.e(i));
    }

    public final int getStepCount() {
        return this.authenticationPattern.e();
    }

    public final boolean isReady() {
        return this.authenticationPattern.c(null);
    }

    public final String toString() {
        return new StringBuilder("CustomerAuthenticationPattern{name='").append(getName()).append('\'').append(", stepCount=").append(getStepCount()).append('}').toString();
    }
}
