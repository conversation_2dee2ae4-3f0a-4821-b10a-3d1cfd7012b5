package o.cz;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import kotlin.io.encoding.Base64;
import o.ee.g;
import o.ei.i;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cz\c.smali */
public final class c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static long b;
    private static int c;
    private static int d;
    private static char[] e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        d = 1;
        a();
        KeyEvent.keyCodeFromString("");
        View.resolveSizeAndState(0, 0, 0);
        MotionEvent.axisFromString("");
        int i = c + Opcodes.LSUB;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 4 / 0;
                break;
        }
    }

    static void a() {
        e = new char[]{62979, 47511, 26913, 6343, 51325, 30769, 11187, 56125, 35533, 11400, 25383, 45963, 49776, 4800, 41632, 61727, 413, 20594, 57563, 12467, 18185, 38882, 9848, 30435, 34493, 54541, 26102, 46154, 50234, 5307, 43789, 64457, 2625, 23072, 60046, 14610, 18914, 11451, 25399, 45982, 49788, 4836, 41659, 61727, 412, 20596, 57556, 12467, 18185, 38894, 9847, 30407, 34485, 54550, 26092, 46207, 50217, 5281, 43786, 64510, 2646, 23087, 60057, 14679, 18877, 38941, 10260, 30854, 36717, 57293, 28251, 48689, 52895, 7461, 44519, 64933, 3120, 23700, 37730, 9095, 29600, 11433, 25458, 46034, 49720, 4843, 41647, 61702, 401, 20523, 57498, 11421, 25402, 45978, 49720, 4805, 41707, 61720, 404, 20529, 57555, 12468, 18240, 38909, 9828, 30422, 34479, 54556, 26092, 46171, 50280, 5303, 43787, 64495, 2564, 23073, 60111, 14596, 18928, 38941, 10287, 30864, 36652, 57284, 28251, 48684, 52875, 7532, 44480, 64940, 11395, 25377, 45968, 49782, 4741, 41611, 61715, 407, 20596, 57546, 12467, 18185, 38882, 9848, 30355, 34489, 54551, 26081, 46144, 50237, 5307, 43786, 64510, 2646, 23076, 60046, 14679, 18919, 38997, 10287, 30863, 36713, 57225, 28226, 48685, 52887, 7526, 44491, 64952, 3111, 23704, 37748, 9152, 29664, 33324, 53891, 24935, 45524, 49596, 4108, 41211, 63297, 2006, 22463, 58895, 14061, 17742, 38180, 9607, 29713, 34025, 56146, 27430, 48030, 51815, 6881, 43295, 63842, 2501};
        a = 8608703081257460562L;
        b = 5888900119932106879L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 1
            int r6 = 114 - r6
            int r7 = r7 + 4
            byte[] r0 = o.cz.c.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L17:
            r3 = r2
        L18:
            int r7 = r7 + 1
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r3 = r0[r7]
            r5 = r8
            r8 = r6
            r6 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L32:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.c.h(short, int, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{2, Base64.padSymbol, -41, 17};
        $$b = 37;
    }

    public static o.fn.e b(o.eg.e eVar, Collection<f> collection) throws i {
        String[] strArr;
        ArrayList arrayList;
        String str;
        long j;
        ArrayList arrayList2;
        f[] e2;
        int i;
        String str2 = "㺁⊝ڤ檳";
        int i2 = 1;
        Object[] objArr = new Object[1];
        f((char) (ViewConfiguration.getLongPressTimeout() >> 16), 9 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 27 - TextUtils.lastIndexOf("", '0'), objArr);
        int i3 = 0;
        String intern = ((String) objArr[0]).intern();
        ArrayList arrayList3 = new ArrayList(eVar.d());
        long j2 = 0;
        Object[] objArr2 = new Object[1];
        f((char) (TextUtils.indexOf((CharSequence) "", '0', 0) + 55981), ExpandableListView.getPackedPositionType(0L), Color.green(0) + 9, objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        g("㺜亅\ude92溭ﺽເ黦⻲뻲츘", 28687 - (Process.myTid() >> 22), objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g("㺛맀〈ꭈ⎗髟ᔱ豻ҧ", 34630 - ExpandableListView.getPackedPositionChild(0L), objArr4);
        String[] strArr2 = {intern2, intern3, ((String) objArr4[0]).intern()};
        int i4 = 0;
        boolean z = false;
        while (i4 < eVar.d()) {
            g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr5 = new Object[i2];
            f((char) ExpandableListView.getPackedPositionType(j2), 37 - TextUtils.getOffsetAfter("", i3), (ViewConfiguration.getLongPressTimeout() >> 16) + 44, objArr5);
            StringBuilder append = sb.append(((String) objArr5[i3]).intern()).append(i4);
            Object[] objArr6 = new Object[i2];
            g("㺏", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 38669, objArr6);
            g.d(intern, append.append(((String) objArr6[i3]).intern()).toString());
            try {
                o.eg.b b2 = eVar.b(i4);
                Object[] objArr7 = new Object[i2];
                g(str2, 7187 - (TypedValue.complexToFloat(i3) > 0.0f ? 1 : (TypedValue.complexToFloat(i3) == 0.0f ? 0 : -1)), objArr7);
                if (!b2.C(((String) objArr7[i3]).intern())) {
                    Object[] objArr8 = new Object[1];
                    g("㺂딯⧎鱧ဢ蓌签\uef50揉홿䨘㻉딣⤌鶰ၤ萊碳", (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 35752, objArr8);
                    throw new i(((String) objArr8[0]).intern());
                }
                Object[] objArr9 = new Object[i2];
                g(str2, 7186 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr9);
                String r = b2.r(((String) objArr9[i3]).intern());
                g.c();
                StringBuilder sb2 = new StringBuilder();
                String str3 = str2;
                boolean z2 = z;
                Object[] objArr10 = new Object[1];
                f((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), KeyEvent.normalizeMetaState(i3) + 37, Drawable.resolveOpacity(i3, i3) + 44, objArr10);
                StringBuilder append2 = sb2.append(((String) objArr10[0]).intern()).append(i4);
                Object[] objArr11 = new Object[1];
                f((char) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getTapTimeout() >> 16) + 81, TextUtils.indexOf((CharSequence) "", '0', 0) + 11, objArr11);
                g.d(intern, append2.append(((String) objArr11[0]).intern()).append(r).toString());
                int i5 = 3;
                f[][] fVarArr = new f[3][];
                int i6 = 0;
                int i7 = 0;
                String str4 = null;
                while (true) {
                    if (i6 < i5) {
                        String str5 = strArr2[i6];
                        g.c();
                        StringBuilder sb3 = new StringBuilder();
                        strArr = strArr2;
                        arrayList = arrayList3;
                        str = r;
                        Object[] objArr12 = new Object[1];
                        g("㺝候\ue3a8甲蓢ᙅꧩ㢂䨒\uddaa漥ﻗၨꏹ㊑䐛ힰ楂\uf8e9੧鷧Ⲕ븨톨捉\uf2c7С靃⛛렁쮻嵆\uecea繤酉ₖ", 28306 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr12);
                        StringBuilder append3 = sb3.append(((String) objArr12[0]).intern()).append(i4);
                        Object[] objArr13 = new Object[1];
                        g("㺏僼\ue2a4瑖虰ᡤꮸ㷺佗\ue144", 28211 - TextUtils.getTrimmedLength(""), objArr13);
                        StringBuilder append4 = append3.append(((String) objArr13[0]).intern()).append(str5);
                        Object[] objArr14 = new Object[1];
                        g("㺏", 38669 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr14);
                        g.d(intern, append4.append(((String) objArr14[0]).intern()).toString());
                        f[] e3 = e(b2, str5);
                        if (e3.length == 0) {
                            g.c();
                            StringBuilder sb4 = new StringBuilder();
                            Object[] objArr15 = new Object[1];
                            g("㺝候\ue3a8甲蓢ᙅꧩ㢂䨒\uddaa漥ﻗၨꏹ㊑䐛ힰ楂\uf8e9੧鷧Ⲕ븨톨捉\uf2c7С靃⛛렁쮻嵆\uecea繤酉ₖ", 28307 - TextUtils.getTrimmedLength(""), objArr15);
                            StringBuilder append5 = sb4.append(((String) objArr15[0]).intern()).append(i4);
                            Object[] objArr16 = new Object[1];
                            g("㺏僼\ue2a4瑖虰ᡤꮸ㷺佗\ue144", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 28210, objArr16);
                            StringBuilder append6 = append5.append(((String) objArr16[0]).intern()).append(str5);
                            Object[] objArr17 = new Object[1];
                            g("㺏ಒ婵ꦱ\uf7e8씞Ⴏ帑걫\ufbc6쥭\u177f拁끶ﾜ췱\u1b4f暶된", 12893 - (ViewConfiguration.getTouchSlop() >> 8), objArr17);
                            g.d(intern, append6.append(((String) objArr17[0]).intern()).toString());
                            str4 = str5;
                            e2 = null;
                        } else {
                            j = 0;
                            if (str4 != null) {
                                Object[] objArr18 = new Object[1];
                                f((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), 91 - View.combineMeasuredStates(0, 0), ((byte) KeyEvent.getModifierMetaStateMask()) + 40, objArr18);
                                throw new i(String.format(((String) objArr18[0]).intern(), str5, str4));
                            }
                            e2 = e(e3, collection);
                            if (e2.length == 0) {
                                g.c();
                                StringBuilder sb5 = new StringBuilder();
                                Object[] objArr19 = new Object[1];
                                g("㺝候\ue3a8甲蓢ᙅꧩ㢂䨒\uddaa漥ﻗၨꏹ㊑䐛ힰ楂\uf8e9੧鷧Ⲕ븨톨捉\uf2c7С靃⛛렁쮻嵆\uecea繤酉ₖ", 28306 - TextUtils.lastIndexOf("", '0', 0, 0), objArr19);
                                StringBuilder append7 = sb5.append(((String) objArr19[0]).intern()).append(i4);
                                Object[] objArr20 = new Object[1];
                                g("㺏僼\ue2a4瑖虰ᡤꮸ㷺佗\ue144", TextUtils.indexOf((CharSequence) "", '0') + 28212, objArr20);
                                StringBuilder append8 = append7.append(((String) objArr20[0]).intern()).append(str5);
                                Object[] objArr21 = new Object[1];
                                g("㺏躦幔⾧ｫ䲌ᳶ\uec44붇ഫ\uda9bꨟ究쯊鬡梧㠍衢姨⥀\uf6fb䙟៉\ue7f3띜Ӈ퐵ꖌ痺앤鋆扸㎿茇卩\u20c0\uf04e䆰ᄗ", 45161 - TextUtils.getOffsetBefore("", 0), objArr21);
                                g.d(intern, append8.append(((String) objArr21[0]).intern()).toString());
                                z2 = true;
                            } else {
                                str4 = null;
                            }
                        }
                        if (e2 != null) {
                            int i8 = c + 75;
                            d = i8 % 128;
                            switch (i8 % 2 == 0 ? ',' : (char) 26) {
                                case 26:
                                    i = i7 + 1;
                                    fVarArr[i7] = e2;
                                    break;
                                default:
                                    i = i7 + 61;
                                    fVarArr[i7] = e2;
                                    break;
                            }
                            i7 = i;
                        }
                        i6++;
                        strArr2 = strArr;
                        arrayList3 = arrayList;
                        r = str;
                        i5 = 3;
                    } else {
                        strArr = strArr2;
                        arrayList = arrayList3;
                        str = r;
                        j = 0;
                    }
                }
                switch (z2) {
                    case false:
                        arrayList2 = arrayList;
                        arrayList2.add(new o.s.c(str, (f[][]) Arrays.copyOf(fVarArr, i7)));
                        z = z2;
                        break;
                    default:
                        arrayList2 = arrayList;
                        z = false;
                        break;
                }
                i4++;
                int i9 = d + 3;
                c = i9 % 128;
                int i10 = i9 % 2;
                arrayList3 = arrayList2;
                j2 = j;
                str2 = str3;
                strArr2 = strArr;
                i3 = 0;
                i2 = 1;
            } catch (o.eg.d e4) {
                StringBuilder sb6 = new StringBuilder();
                Object[] objArr22 = new Object[1];
                f((char) TextUtils.getCapsMode("", 0, 0), MotionEvent.axisFromString("") + Opcodes.LXOR, 69 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr22);
                throw new i(sb6.append(((String) objArr22[0]).intern()).append(e4.getMessage()).toString());
            }
        }
        return new o.fn.e(arrayList3);
    }

    private static f[] e(o.eg.b bVar, String str) throws o.eg.d {
        f[] fVarArr = new f[0];
        switch (bVar.C(str)) {
            case false:
                break;
            default:
                int i = d + Opcodes.LSHL;
                c = i % 128;
                switch (i % 2 == 0) {
                    case false:
                        fVarArr = (f[]) bVar.c(f.class, str);
                        int i2 = 3 / 0;
                        break;
                    default:
                        fVarArr = (f[]) bVar.c(f.class, str);
                        break;
                }
        }
        int i3 = c + 97;
        d = i3 % 128;
        int i4 = i3 % 2;
        return fVarArr;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static o.i.f[] e(o.i.f[] r7, java.util.Collection<o.i.f> r8) {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            int r1 = r7.length
            r2 = 0
            r3 = r2
        L9:
            if (r3 >= r1) goto Le
            r4 = 45
            goto L10
        Le:
            r4 = 11
        L10:
            switch(r4) {
                case 45: goto L1c;
                default: goto L13;
            }
        L13:
            o.i.f[] r7 = new o.i.f[r2]
            java.lang.Object[] r7 = r0.toArray(r7)
            o.i.f[] r7 = (o.i.f[]) r7
            return r7
        L1c:
            int r4 = o.cz.c.d
            int r4 = r4 + 69
            int r5 = r4 % 128
            o.cz.c.c = r5
            int r4 = r4 % 2
            r4 = r7[r3]
            boolean r5 = r8.contains(r4)
            if (r5 == 0) goto L31
            r5 = 49
            goto L32
        L31:
            r5 = 1
        L32:
            switch(r5) {
                case 49: goto L36;
                default: goto L35;
            }
        L35:
            goto L43
        L36:
            int r5 = o.cz.c.c
            int r5 = r5 + 91
            int r6 = r5 % 128
            o.cz.c.d = r6
            int r5 = r5 % 2
            r0.add(r4)
        L43:
            int r3 = r3 + 1
            goto L9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.c.e(o.i.f[], java.util.Collection):o.i.f[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 606
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.c.f(char, int, int, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Failed to find 'out' block for switch in B:108:0x0021. Please report as an issue. */
    private static void g(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 816
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cz.c.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
