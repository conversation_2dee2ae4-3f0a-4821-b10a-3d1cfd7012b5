package fr.antelop.sdk.digitalcard.devicewallet.common.ui;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.os.Bundle;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.ExpandableListView;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.content.res.AppCompatResources;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager2.widget.ViewPager2;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;
import fr.antelop.sdk.R;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.CardMocksAdapter;
import fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils;
import fr.antelop.sdk.digitalcard.devicewallet.common.viewmodels.ManageDeviceWalletViewModel;
import fr.antelop.sdk.digitalcard.devicewallet.googlepay.viewmodels.ManageGooglePayMockViewModel;
import fr.antelop.sdk.digitalcard.devicewallet.samsungpay.viewmodels.ManageSamsungPayMockViewModel;
import java.lang.reflect.Method;
import java.util.List;
import o.a.o;
import o.e.a;
import o.ep.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\devicewallet\common\ui\ManageDeviceWalletMockFragment.smali */
public final class ManageDeviceWalletMockFragment extends Fragment {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static int c;
    private static int d;
    private static char e;
    private ImageView logoImageView;
    ManageDeviceWalletViewModel manageDeviceWalletMockViewModel;
    private TextView noCardMessage;
    private ViewPager2 pager;
    private TabLayout tabLayout;
    private Toolbar toolbar;
    private MaterialButton wipeDataButton;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        b = 1;
        e = (char) 17957;
        d = 161105445;
        a = 7322938993703286820L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r7 = 106 - r7
            byte[] r0 = fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment.$$a
            int r8 = r8 + 4
            int r6 = r6 * 2
            int r6 = 1 - r6
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r7 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L35
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L26
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L26:
            int r3 = r3 + 1
            int r8 = r8 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L35:
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment.g(short, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{106, 58, 15, 91};
        $$b = 29;
    }

    static /* synthetic */ void lambda$onViewCreated$3(TabLayout.Tab tab, int i) {
        int i2 = b + 75;
        c = i2 % 128;
        switch (i2 % 2 != 0 ? '_' : 'P') {
            case 'P':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:9:0x009a. Please report as an issue. */
    @Override // androidx.fragment.app.Fragment
    public final void onCreate(Bundle bundle) {
        int i = b + 91;
        c = i % 128;
        int i2 = i % 2;
        super.onCreate(bundle);
        String string = requireArguments().getString(DeviceWalletMockActivity.DEVICE_WALLET_BUNDLE_KEY);
        Object[] objArr = new Object[1];
        f(ViewConfiguration.getFadingEdgeLength() >> 16, "\udea6泀붩\ude6d\u245a兄滥蠳윔캕", (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "\ue5e3랕ⅅꑁ", "㸁帐푱㺾", objArr);
        if (((String) objArr[0]).intern().equals(string)) {
            this.manageDeviceWalletMockViewModel = (ManageDeviceWalletViewModel) new ViewModelProvider(this).get(ManageGooglePayMockViewModel.class);
        }
        Object[] objArr2 = new Object[1];
        f((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 873767873, "榉邸Ȳ댱倽䜼뻜窎璓板퀄", (char) (ImageFormat.getBitsPerPixel(0) + 1), "슗ᒣ蜴夤", "㸁帐푱㺾", objArr2);
        if (((String) objArr2[0]).intern().equals(string)) {
            this.manageDeviceWalletMockViewModel = (ManageDeviceWalletViewModel) new ViewModelProvider(this).get(ManageSamsungPayMockViewModel.class);
            int i3 = c + 39;
            b = i3 % 128;
            switch (i3 % 2 != 0) {
            }
        }
    }

    @Override // androidx.fragment.app.Fragment
    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        int i = c + Opcodes.DREM;
        b = i % 128;
        int i2 = i % 2;
        View inflate = layoutInflater.inflate(R.layout.manage_device_wallet_mock_fragment_layout, viewGroup, false);
        this.toolbar = (Toolbar) inflate.findViewById(R.id.toolbar);
        this.noCardMessage = (TextView) inflate.findViewById(R.id.no_card_message);
        this.pager = (ViewPager2) inflate.findViewById(R.id.pager);
        this.logoImageView = (ImageView) inflate.findViewById(R.id.device_wallet_logo);
        this.wipeDataButton = (MaterialButton) inflate.findViewById(R.id.wipe_data_button);
        this.tabLayout = (TabLayout) inflate.findViewById(R.id.tab_layout);
        int i3 = c + 91;
        b = i3 % 128;
        int i4 = i3 % 2;
        return inflate;
    }

    /* renamed from: lambda$onViewCreated$0$fr-antelop-sdk-digitalcard-devicewallet-common-ui-ManageDeviceWalletMockFragment, reason: not valid java name */
    /* synthetic */ void m223xabf824fd(View view) {
        int i = c + 37;
        b = i % 128;
        int i2 = i % 2;
        requireActivity().setResult(-1);
        requireActivity().finish();
        int i3 = c + 45;
        b = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    @Override // androidx.fragment.app.Fragment
    public final void onViewCreated(View view, Bundle bundle) {
        super.onViewCreated(view, bundle);
        requireActivity().getOnBackPressedDispatcher().addCallback(getViewLifecycleOwner(), new OnBackPressedCallback(true) { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment.1
            @Override // androidx.activity.OnBackPressedCallback
            public void handleOnBackPressed() {
                ManageDeviceWalletMockFragment.this.requireActivity().setResult(-1);
                ManageDeviceWalletMockFragment.this.requireActivity().finish();
            }
        });
        AppCompatActivity appCompatActivity = (AppCompatActivity) requireActivity();
        appCompatActivity.setSupportActionBar(this.toolbar);
        ActionBar supportActionBar = appCompatActivity.getSupportActionBar();
        switch (supportActionBar != null) {
            case true:
                int i = c + 39;
                b = i % 128;
                int i2 = i % 2;
                supportActionBar.setDisplayHomeAsUpEnabled(true);
                supportActionBar.setHomeAsUpIndicator(AppCompatResources.getDrawable(requireContext(), androidx.appcompat.R.drawable.abc_ic_ab_back_material));
                int i3 = c + 83;
                b = i3 % 128;
                if (i3 % 2 == 0) {
                    break;
                }
                break;
        }
        this.logoImageView.setImageDrawable(ResourcesCompat.getDrawable(getResources(), requireArguments().getInt(DeviceWalletMockActivity.LOGO_RESOURCE_KEY), null));
        final int i4 = requireArguments().getInt(DeviceWalletMockActivity.WIPE_BUTTON_STRING_RESOURCE_KEY);
        this.wipeDataButton.setText(getString(i4));
        this.toolbar.setNavigationOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment$$ExternalSyntheticLambda3
            @Override // android.view.View.OnClickListener
            public final void onClick(View view2) {
                ManageDeviceWalletMockFragment.this.m223xabf824fd(view2);
            }
        });
        this.manageDeviceWalletMockViewModel.getMockedTokens().observe(getViewLifecycleOwner(), new Observer() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment$$ExternalSyntheticLambda4
            @Override // androidx.lifecycle.Observer
            public final void onChanged(Object obj) {
                ManageDeviceWalletMockFragment.this.m226x66bdcd79(i4, (List) obj);
            }
        });
    }

    /* renamed from: lambda$onViewCreated$1$fr-antelop-sdk-digitalcard-devicewallet-common-ui-ManageDeviceWalletMockFragment, reason: not valid java name */
    /* synthetic */ void m224xdaa98f1c(final e eVar) {
        ContextUtils.buildDialog(requireContext(), R.string.antelopDeleteCardTextTitle, R.string.antelopDeleteCardTextMessage, R.string.antelopAlertDialogWipeDataPositiveButtonText, Integer.valueOf(R.string.antelopAlertDialogWipeDataNegativeButtonText), new ContextUtils.AlertDialogClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment.2
            @Override // fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils.AlertDialogClickListener
            public void onPositiveButtonClicked() {
                ManageDeviceWalletMockFragment.this.manageDeviceWalletMockViewModel.deleteToken(eVar);
            }

            @Override // fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils.AlertDialogClickListener
            public void onNegativeButtonClicked() {
            }
        }).show();
        int i = c + 65;
        b = i % 128;
        int i2 = i % 2;
    }

    /* renamed from: lambda$onViewCreated$2$fr-antelop-sdk-digitalcard-devicewallet-common-ui-ManageDeviceWalletMockFragment, reason: not valid java name */
    /* synthetic */ void m225x95af93b(int i, int i2, View view) {
        ContextUtils.buildDialog(requireContext(), i, i2, R.string.antelopAlertDialogWipeDataPositiveButtonText, Integer.valueOf(R.string.antelopAlertDialogWipeDataNegativeButtonText), new ContextUtils.AlertDialogClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment.3
            @Override // fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils.AlertDialogClickListener
            public void onPositiveButtonClicked() {
                ManageDeviceWalletMockFragment.this.manageDeviceWalletMockViewModel.wipeDeviceWalletData();
            }

            @Override // fr.antelop.sdk.digitalcard.devicewallet.common.ui.ContextUtils.AlertDialogClickListener
            public void onNegativeButtonClicked() {
            }
        }).show();
        int i3 = c + 33;
        b = i3 % 128;
        int i4 = i3 % 2;
    }

    /* renamed from: lambda$onViewCreated$4$fr-antelop-sdk-digitalcard-devicewallet-common-ui-ManageDeviceWalletMockFragment, reason: not valid java name */
    /* synthetic */ void m226x66bdcd79(final int i, List list) {
        switch (list.isEmpty()) {
            case false:
                this.pager.setVisibility(0);
                this.wipeDataButton.setVisibility(0);
                this.tabLayout.setVisibility(0);
                this.noCardMessage.setVisibility(8);
                this.pager.setAdapter(new CardMocksAdapter(this, list, new CardMocksAdapter.CardMocksAdapterCallback() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment$$ExternalSyntheticLambda0
                    @Override // fr.antelop.sdk.digitalcard.devicewallet.common.ui.CardMocksAdapter.CardMocksAdapterCallback
                    public final void deleteToken(e eVar) {
                        ManageDeviceWalletMockFragment.this.m224xdaa98f1c(eVar);
                    }
                }));
                final int i2 = requireArguments().getInt(DeviceWalletMockActivity.WIPE_DATA_DESCRIPTION_RESOURCE_KEY);
                this.wipeDataButton.setOnClickListener(new View.OnClickListener() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment$$ExternalSyntheticLambda1
                    @Override // android.view.View.OnClickListener
                    public final void onClick(View view) {
                        ManageDeviceWalletMockFragment.this.m225x95af93b(i, i2, view);
                    }
                });
                new TabLayoutMediator(this.tabLayout, this.pager, new TabLayoutMediator.TabConfigurationStrategy() { // from class: fr.antelop.sdk.digitalcard.devicewallet.common.ui.ManageDeviceWalletMockFragment$$ExternalSyntheticLambda2
                    @Override // com.google.android.material.tabs.TabLayoutMediator.TabConfigurationStrategy
                    public final void onConfigureTab(TabLayout.Tab tab, int i3) {
                        ManageDeviceWalletMockFragment.lambda$onViewCreated$3(tab, i3);
                    }
                }).attach();
                break;
            default:
                int i3 = c + 3;
                b = i3 % 128;
                if (i3 % 2 == 0) {
                }
                this.noCardMessage.setVisibility(0);
                this.pager.setVisibility(8);
                this.wipeDataButton.setVisibility(8);
                this.tabLayout.setVisibility(8);
                int i4 = b + 71;
                c = i4 % 128;
                int i5 = i4 % 2;
                break;
        }
    }

    private static void f(int i, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        char c3;
        if (str3 != null) {
            int i2 = $10 + 95;
            $11 = i2 % 128;
            int i3 = i2 % 2;
            cArr = str3.toCharArray();
        } else {
            cArr = str3;
        }
        char[] cArr3 = cArr;
        int i4 = 0;
        Object obj = null;
        switch (str2 != null) {
            case false:
                cArr2 = str2;
                break;
            default:
                int i5 = $11 + Opcodes.LNEG;
                $10 = i5 % 128;
                switch (i5 % 2 != 0) {
                    case true:
                        str2.toCharArray();
                        obj.hashCode();
                        throw null;
                    default:
                        cArr2 = str2.toCharArray();
                        int i6 = $11 + 25;
                        $10 = i6 % 128;
                        int i7 = i6 % 2;
                        break;
                }
        }
        char[] cArr4 = cArr2;
        char[] charArray = str != null ? str.toCharArray() : str;
        o oVar = new o();
        int length = cArr4.length;
        char[] cArr5 = new char[length];
        int length2 = cArr3.length;
        char[] cArr6 = new char[length2];
        System.arraycopy(cArr4, 0, cArr5, 0, length);
        System.arraycopy(cArr3, 0, cArr6, 0, length2);
        cArr5[0] = (char) (cArr5[0] ^ c2);
        cArr6[2] = (char) (cArr6[2] + ((char) i));
        int length3 = charArray.length;
        char[] cArr7 = new char[length3];
        oVar.e = 0;
        while (oVar.e < length3) {
            try {
                Object[] objArr2 = {oVar};
                Object obj2 = a.s.get(-429442487);
                if (obj2 == null) {
                    Class cls = (Class) a.c(10 - (CdmaCellLocation.convertQuartSecToDecDegrees(i4) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i4) == 0.0d ? 0 : -1)), (char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 20954), ExpandableListView.getPackedPositionGroup(0L) + 344);
                    byte b2 = (byte) i4;
                    Object[] objArr3 = new Object[1];
                    g(b2, (byte) (b2 | 7), (byte) (-1), objArr3);
                    String str4 = (String) objArr3[i4];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i4] = Object.class;
                    obj2 = cls.getMethod(str4, clsArr);
                    a.s.put(-429442487, obj2);
                }
                int intValue = ((Integer) ((Method) obj2).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj3 = a.s.get(-515165572);
                    if (obj3 == null) {
                        Class cls2 = (Class) a.c(TextUtils.indexOf("", "", i4, i4) + 10, (char) (TextUtils.lastIndexOf("", '0') + 1), 207 - TextUtils.indexOf("", ""));
                        byte b3 = (byte) i4;
                        Object[] objArr5 = new Object[1];
                        g(b3, (byte) (b3 + 5), (byte) (-1), objArr5);
                        String str5 = (String) objArr5[i4];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i4] = Object.class;
                        obj3 = cls2.getMethod(str5, clsArr2);
                        a.s.put(-515165572, obj3);
                    }
                    int intValue2 = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
                    int i8 = cArr5[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr6[intValue]);
                        objArr6[1] = Integer.valueOf(i8);
                        objArr6[i4] = oVar;
                        Object obj4 = a.s.get(-1614232674);
                        if (obj4 == null) {
                            Class cls3 = (Class) a.c(12 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (char) View.resolveSize(i4, i4), 281 - (ViewConfiguration.getTapTimeout() >> 16));
                            byte b4 = (byte) i4;
                            byte b5 = (byte) (b4 + 3);
                            Object[] objArr7 = new Object[1];
                            g(b4, b5, (byte) (b5 - 4), objArr7);
                            obj4 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            a.s.put(-1614232674, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                            Object obj5 = a.s.get(406147795);
                            if (obj5 != null) {
                                c3 = 2;
                            } else {
                                Class cls4 = (Class) a.c(Color.alpha(0) + 19, (char) (MotionEvent.axisFromString("") + 14688), 112 - (ViewConfiguration.getWindowTouchSlop() >> 8));
                                byte b6 = (byte) 0;
                                byte b7 = b6;
                                Object[] objArr9 = new Object[1];
                                g(b6, b7, (byte) (b7 - 1), objArr9);
                                c3 = 2;
                                obj5 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                a.s.put(406147795, obj5);
                            }
                            cArr6[intValue2] = ((Character) ((Method) obj5).invoke(null, objArr8)).charValue();
                            cArr5[intValue2] = oVar.d;
                            cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ charArray[oVar.e]) ^ (a ^ 6565854932352255525L)) ^ ((int) (d ^ 6565854932352255525L))) ^ ((char) (e ^ 6565854932352255525L)));
                            oVar.e++;
                            i4 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr7);
    }
}
