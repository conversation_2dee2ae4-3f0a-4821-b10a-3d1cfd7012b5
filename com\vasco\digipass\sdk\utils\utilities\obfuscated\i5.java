package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.util.Enumeration;
import java.util.Iterator;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\i5.smali */
class i5 extends e0 {
    private byte[] C;

    i5(byte[] bArr) throws IOException {
        if (bArr == null) {
            throw new NullPointerException("'encoded' cannot be null");
        }
        this.C = bArr;
    }

    private synchronized void p() {
        if (this.C != null) {
            q qVar = new q(this.C, true);
            try {
                i d = qVar.d();
                qVar.close();
                this.b = d.c();
                this.C = null;
            } catch (IOException e) {
                throw new a0("malformed ASN.1: " + e, e);
            }
        }
    }

    private synchronized byte[] q() {
        return this.C;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    public h a(int i) {
        p();
        return super.a(i);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        p();
        return super.f();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        p();
        return super.g();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        p();
        return super.hashCode();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0, java.lang.Iterable
    public Iterator<h> iterator() {
        p();
        return super.iterator();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    public Enumeration j() {
        byte[] q = q();
        return q != null ? new h5(q) : super.j();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    d k() {
        return ((e0) g()).k();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    l l() {
        return ((e0) g()).l();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    x m() {
        return ((e0) g()).m();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    f0 n() {
        return ((e0) g()).n();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.e0
    public int size() {
        p();
        return super.size();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) throws IOException {
        byte[] q = q();
        if (q != null) {
            return z.a(z, q.length);
        }
        return super.g().a(z);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        byte[] q = q();
        if (q != null) {
            zVar.a(z, 48, q);
        } else {
            super.g().a(zVar, z);
        }
    }
}
