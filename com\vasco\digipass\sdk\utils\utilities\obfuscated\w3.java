package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\w3.smali */
public class w3 implements p1 {
    private final String a;
    private final int b;
    private final Object c;
    private final q1 d;

    public w3(String str, int i) {
        this(str, i, null, q1.ANY);
    }

    public w3(String str, int i, Object obj, q1 q1Var) {
        this.a = str;
        this.b = i;
        this.c = obj;
        if (obj instanceof q1) {
            throw new IllegalArgumentException("params should not be CryptoServicePurpose");
        }
        this.d = q1Var;
    }
}
