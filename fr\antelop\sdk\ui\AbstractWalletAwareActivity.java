package fr.antelop.sdk.ui;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.WalletManager;
import fr.antelop.sdk.WalletManagerCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.exception.WalletValidationException;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\ui\AbstractWalletAwareActivity.smali */
public abstract class AbstractWalletAwareActivity extends AppCompatActivity implements WalletManagerCallback {
    private static final String TAG = "AbstractWalletAwareActivity";
    private WalletManager walletManager;

    @Override // androidx.fragment.app.FragmentActivity, androidx.activity.ComponentActivity, androidx.core.app.ComponentActivity, android.app.Activity
    protected void onCreate(Bundle bundle) {
        try {
            super.onCreate(bundle);
            this.walletManager = new WalletManager(this, this, null);
        } catch (WalletValidationException e) {
            g.c();
            g.a(TAG, "onCreate", e);
        }
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onStart() {
        g.c();
        g.d(TAG, "onStart");
        super.onStart();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() {
        super.onResume();
        g.c();
        g.d(TAG, "onResume");
        this.walletManager.connect();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
        g.c();
        g.d(TAG, "onPause");
        this.walletManager.disconnect();
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onStop() {
        super.onStop();
        g.c();
        g.d(TAG, "onStop");
    }

    @Override // androidx.appcompat.app.AppCompatActivity, androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onDestroy() {
        WalletManager walletManager = this.walletManager;
        if (walletManager != null) {
            walletManager.clean();
        }
        this.walletManager = null;
        super.onDestroy();
    }

    protected final void connect(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        this.walletManager.connect(customerAuthenticationCredentials, customerAuthenticationCredentials2);
    }

    protected final void disconnect() {
        this.walletManager.disconnect();
    }

    protected final void logout() throws WalletValidationException {
        this.walletManager.logout();
    }

    public final void changeCredentials(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        this.walletManager.changeCredentials(customerAuthenticationCredentials, customerAuthenticationCredentials2);
    }

    public final void synchronizeAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.walletManager.synchronizeAuthenticationMethod(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    public final void checkCredentials(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.walletManager.checkCredentials(customerAuthenticationCredentials);
    }

    protected final void cancelOngoingTransaction() throws WalletValidationException {
        this.walletManager.cancelOngoingTransaction();
    }

    public final boolean setCustomerCredentialsForTransaction(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        return this.walletManager.setCustomerCredentialsForTransaction(customerAuthenticationCredentials);
    }

    protected final void lock(WalletLockReason walletLockReason) throws WalletValidationException {
        this.walletManager.lock(walletLockReason);
    }

    protected final void delete() throws WalletValidationException {
        this.walletManager.delete();
    }

    protected final WalletManager getWalletManager() {
        return this.walletManager;
    }

    public void activateAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.walletManager.activateAuthenticationMethod(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    public void deactivateAuthenticationMethod(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.walletManager.deactivateAuthenticationMethod(customerAuthenticationMethodType, customerAuthenticationCredentials);
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }
}
