package com.google.android.gms.tapandpay;

import android.app.Activity;
import android.app.PendingIntent;
import com.google.android.gms.common.api.Api;
import com.google.android.gms.common.api.HasApiKey;
import com.google.android.gms.tapandpay.TapAndPay;
import com.google.android.gms.tapandpay.issuer.CreatePushProvisionSessionRequest;
import com.google.android.gms.tapandpay.issuer.IsTokenizedRequest;
import com.google.android.gms.tapandpay.issuer.PushProvisionSessionContext;
import com.google.android.gms.tapandpay.issuer.PushTokenizeRequest;
import com.google.android.gms.tapandpay.issuer.ServerPushProvisionRequest;
import com.google.android.gms.tapandpay.issuer.TokenInfo;
import com.google.android.gms.tapandpay.issuer.TokenStatus;
import com.google.android.gms.tapandpay.issuer.ViewTokenRequest;
import com.google.android.gms.tasks.Task;
import java.util.List;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\TapAndPayClient.smali */
public interface TapAndPayClient extends HasApiKey<Api.ApiOptions.NotRequiredOptions> {
    public static final String DATA_CHANGED_LISTENER_KEY = "tapAndPayDataChangedListener";

    Task<PushProvisionSessionContext> createPushProvisionSession(CreatePushProvisionSessionRequest createPushProvisionSessionRequest);

    void createWallet(Activity activity, int i);

    Task<String> getActiveWalletId();

    Task<String> getEnvironment();

    Task<String> getLinkingToken(String str);

    Task<String> getStableHardwareId();

    Task<TokenStatus> getTokenStatus(int i, String str);

    Task<Boolean> isTokenized(IsTokenizedRequest isTokenizedRequest);

    Task<List<TokenInfo>> listTokens();

    void pushTokenize(Activity activity, PushTokenizeRequest pushTokenizeRequest, int i);

    Task<Void> registerDataChangedListener(TapAndPay.DataChangedListener dataChangedListener);

    Task<Void> removeDataChangedListener(TapAndPay.DataChangedListener dataChangedListener);

    void requestDeleteToken(Activity activity, String str, int i, int i2);

    void requestSelectToken(Activity activity, String str, int i, int i2);

    void serverPushProvision(Activity activity, ServerPushProvisionRequest serverPushProvisionRequest, int i);

    void tokenize(Activity activity, String str, int i, String str2, int i2, int i3);

    Task<PendingIntent> viewToken(ViewTokenRequest viewTokenRequest);
}
