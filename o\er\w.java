package o.er;

import com.esotericsoftware.asm.Opcodes;
import java.util.List;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\w.smali */
public final class w extends a {
    private final o.es.c a;
    private final String d;
    private static int e = 0;
    private static int b = 1;

    public w(boolean z, String str, List<o.es.b> list) {
        super(z);
        this.d = str;
        o.es.c cVar = new o.es.c();
        this.a = cVar;
        if (list != null) {
            cVar.c(list);
        }
    }

    public final String e() {
        int i = e;
        int i2 = (i ^ Opcodes.LSHR) + ((i & Opcodes.LSHR) << 1);
        b = i2 % 128;
        int i3 = i2 % 2;
        String str = this.d;
        int i4 = i + 43;
        b = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final Boolean b(o.es.b bVar) {
        int i = e;
        int i2 = (i ^ 67) + ((i & 67) << 1);
        int i3 = i2 % 128;
        b = i3;
        int i4 = i2 % 2;
        o.es.c cVar = this.a;
        switch (cVar == null) {
            case true:
                return Boolean.FALSE;
            default:
                int i5 = (i3 & 53) + (i3 | 53);
                e = i5 % 128;
                if (i5 % 2 != 0) {
                }
                Boolean c = cVar.c(bVar);
                int i6 = b;
                int i7 = (i6 & 65) + (i6 | 65);
                e = i7 % 128;
                int i8 = i7 % 2;
                return c;
        }
    }
}
