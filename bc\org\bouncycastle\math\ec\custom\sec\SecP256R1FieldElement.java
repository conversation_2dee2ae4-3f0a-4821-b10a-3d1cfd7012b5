package bc.org.bouncycastle.math.ec.custom.sec;

import bc.org.bouncycastle.math.ec.ECFieldElement;
import bc.org.bouncycastle.util.Arrays;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.z4;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP256R1FieldElement.smali */
public class SecP256R1FieldElement extends ECFieldElement.AbstractFp {
    public static final BigInteger Q = new BigInteger(1, z4.a("FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF"));
    protected int[] a;

    public SecP256R1FieldElement(BigInteger bigInteger) {
        if (bigInteger == null || bigInteger.signum() < 0 || bigInteger.compareTo(Q) >= 0) {
            throw new IllegalArgumentException("x value invalid for SecP256R1FieldElement");
        }
        this.a = SecP256R1Field.fromBigInteger(bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement add(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256R1Field.add(this.a, ((SecP256R1FieldElement) eCFieldElement).a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement addOne() {
        int[] a = w5.a();
        SecP256R1Field.addOne(this.a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement divide(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256R1Field.inv(((SecP256R1FieldElement) eCFieldElement).a, a);
        SecP256R1Field.multiply(a, this.a, a);
        return new SecP256R1FieldElement(a);
    }

    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof SecP256R1FieldElement) {
            return w5.b(this.a, ((SecP256R1FieldElement) obj).a);
        }
        return false;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public String getFieldName() {
        return "SecP256R1Field";
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public int getFieldSize() {
        return Q.bitLength();
    }

    public int hashCode() {
        return Q.hashCode() ^ Arrays.hashCode(this.a, 0, 8);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement invert() {
        int[] a = w5.a();
        SecP256R1Field.inv(this.a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isOne() {
        return w5.a(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean isZero() {
        return w5.b(this.a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement multiply(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256R1Field.multiply(this.a, ((SecP256R1FieldElement) eCFieldElement).a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement negate() {
        int[] a = w5.a();
        SecP256R1Field.negate(this.a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement sqrt() {
        int[] iArr = this.a;
        if (w5.b(iArr) || w5.a(iArr)) {
            return this;
        }
        int[] c = w5.c();
        int[] a = w5.a();
        int[] a2 = w5.a();
        SecP256R1Field.square(iArr, a, c);
        SecP256R1Field.multiply(a, iArr, a, c);
        SecP256R1Field.squareN(a, 2, a2, c);
        SecP256R1Field.multiply(a2, a, a2, c);
        SecP256R1Field.squareN(a2, 4, a, c);
        SecP256R1Field.multiply(a, a2, a, c);
        SecP256R1Field.squareN(a, 8, a2, c);
        SecP256R1Field.multiply(a2, a, a2, c);
        SecP256R1Field.squareN(a2, 16, a, c);
        SecP256R1Field.multiply(a, a2, a, c);
        SecP256R1Field.squareN(a, 32, a, c);
        SecP256R1Field.multiply(a, iArr, a, c);
        SecP256R1Field.squareN(a, 96, a, c);
        SecP256R1Field.multiply(a, iArr, a, c);
        SecP256R1Field.squareN(a, 94, a, c);
        SecP256R1Field.square(a, a2, c);
        if (w5.b(iArr, a2)) {
            return new SecP256R1FieldElement(a);
        }
        return null;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement square() {
        int[] a = w5.a();
        SecP256R1Field.square(this.a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public ECFieldElement subtract(ECFieldElement eCFieldElement) {
        int[] a = w5.a();
        SecP256R1Field.subtract(this.a, ((SecP256R1FieldElement) eCFieldElement).a, a);
        return new SecP256R1FieldElement(a);
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public boolean testBitZero() {
        return w5.a(this.a, 0) == 1;
    }

    @Override // bc.org.bouncycastle.math.ec.ECFieldElement
    public BigInteger toBigInteger() {
        return w5.c(this.a);
    }

    public SecP256R1FieldElement() {
        this.a = w5.a();
    }

    protected SecP256R1FieldElement(int[] iArr) {
        this.a = iArr;
    }
}
