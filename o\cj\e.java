package o.cj;

import android.graphics.ImageFormat;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.ArrayList;
import o.a.j;
import o.e.a;
import o.eg.d;
import o.et.b;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\cj\e.smali */
public final class e implements o.cc.e<b> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static boolean c;
    private static char[] d;
    private static boolean e;
    private static int h;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        a();
        SystemClock.uptimeMillis();
        int i = j + 93;
        h = i % 128;
        int i2 = i % 2;
    }

    static void a() {
        d = new char[]{61592, 61621, 61620, 61628, 61624, 61616, 61610, 61638, 61601, 61639, 61634, 61627, 61629, 61607, 61584, 61633, 61622, 61637, 61635, 61618, 61636, 61640, 61643, 61623, 61589, 61595, 61569, 61590, 61568, 61573, 61572, 61575};
        c = true;
        e = true;
        a = 782102865;
        b = new char[]{50937, 50855, 50853, 50831, 50912, 50912, 50904, 50887, 50887, 50931, 50854, 50862, 50863, 50839, 50860, 50877, 50855, 50841, 50788, 50932, 50876, 50877, 50876, 50849, 50863, 50860, 50857, 50856, 50916, 50860, 50855, 50857, 50859, 50858, 50858, 50859, 50846, 50847, 50855, 50855, 50933, 50853, 50861, 50853, 50854, 50848, 50873, 50878, 50851, 50856, 50855, 50855, 50863, 50860, 50859, 50851, 50849, 50851, 50855, 50863, 50859, 50855, 50851, 50935, 50854, 50860, 50856, 50853, 50852, 50855, 50848, 50851, 50857, 50939, 50858, 50863, 50852, 50844, 50794, 50794, 50798, 50798, 50771, 50775, 50796, 50796, 50774, 50775, 50770, 50791, 50792, 50769, 50769, 50793, 50792, 51173, 51177, 51170, 51159, 51172, 51175, 51177, 51171, 51171, 51181, 51171, 51159, 51172, 51175, 51177, 51170, 51155, 51153, 51171, 51172, 51153, 51168, 51170, 51168, 51181, 50935, 50851, 50854, 50854, 50855, 50856, 50859, 50876, 50849, 50860, 50861, 50858, 50847, 50794, 50794, 50795, 50796, 50799, 50791, 50789, 50791, 50794, 50771, 50795, 50785, 50798, 50792, 50786, 50792, 50799, 50784, 50789, 50768, 50769, 50798, 50785, 50932, 50849, 50853, 50855, 50858, 50855, 50877, 50876, 50927, 50824, 50934, 50941, 50923, 50827, 50830, 50839, 50807, 50805, 50755, 50865, 50793, 50801, 50807, 50811, 50815, 50787, 50811, 50807, 50805, 50807, 50782, 50868, 50868, 50867, 50791, 50804, 50813, 50811, 50803, 50807, 50805, 50807, 50782, 50755, 50808, 50809, 50808, 50803, 50800, 50800, 50805, 50810, 50810, 50813, 50755, 50778, 50806, 50809, 50811, 50809, 50755, 50776, 50810, 50786, 50787, 50815, 50810, 50811, 50754, 50765, 50799, 50813, 50808, 50810, 50812, 50753, 50762, 50853, 50691, 50841, 50796, 50799, 50790, 50792, 50777, 50777, 50768, 50799, 50788, 50793, 50793, 50767, 50834, 50834, 50864, 50793, 50790, 50793, 50770, 50798, 50770, 50870, 50767, 50793, 50768, 50799, 50788, 50793, 50754, 50754, 50870, 50866, 50796, 50799, 50798, 50795, 50794};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 + 4
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r0 = o.cj.e.$$a
            int r6 = 122 - r6
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L34
        L18:
            r3 = r2
        L19:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r7 = r7 + 1
            if (r3 != r8) goto L28
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L28:
            int r3 = r3 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L34:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cj.e.i(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{31, 2, -38, 71};
        $$b = Opcodes.I2D;
    }

    @Override // o.cc.e
    public final /* synthetic */ b d(String str, String str2, int i, String str3) {
        int i2 = j + Opcodes.LSHR;
        h = i2 % 128;
        int i3 = i2 % 2;
        b e2 = e(str, str2, i, str3);
        int i4 = h + Opcodes.DNEG;
        j = i4 % 128;
        switch (i4 % 2 == 0 ? '\'' : (char) 6) {
            case 6:
                return e2;
            default:
                int i5 = 21 / 0;
                return e2;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @Override // o.cc.e
    public final java.util.List<o.et.b> a(java.lang.String r19, java.lang.String r20, int r21, java.lang.String r22, o.eg.b r23) throws o.ei.i {
        /*
            Method dump skipped, instructions count: 1166
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cj.e.a(java.lang.String, java.lang.String, int, java.lang.String, o.eg.b):java.util.List");
    }

    private static b b(o.eg.b bVar, String str, String str2, int i, String str3) throws d {
        b bVar2 = new b(str, str2, i, str3);
        Object[] objArr = new Object[1];
        f(null, 127 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), null, "\u0082\u0085\u0086", objArr);
        bVar2.a(bVar.B(((String) objArr[0]).intern()));
        Object[] objArr2 = new Object[1];
        f(null, 127 - KeyEvent.normalizeMetaState(0), null, "\u0085\u0091\u008c", objArr2);
        byte[] B = bVar.B(((String) objArr2[0]).intern());
        bVar2.e(B);
        ArrayList<o.ej.d> c2 = o.ej.d.c(B, 0);
        Object[] objArr3 = new Object[1];
        f(null, View.resolveSize(0, 0) + 127, null, "\u009e\u009e\u009a\u0099", objArr3);
        o.ej.d a2 = o.ej.d.a(((String) objArr3[0]).intern(), c2);
        switch (a2 != null) {
            case false:
                break;
            default:
                int i2 = j + Opcodes.DREM;
                h = i2 % 128;
                boolean z = i2 % 2 != 0;
                a2.b();
                switch (z) {
                    case true:
                        throw null;
                }
        }
        Object[] objArr4 = new Object[1];
        f(null, 127 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), null, "\u0099 \u009a\u009f", objArr4);
        bVar2.d(o.ej.d.b(B, ((String) objArr4[0]).intern()));
        int i3 = j + 5;
        h = i3 % 128;
        if (i3 % 2 == 0) {
            return bVar2;
        }
        throw null;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<o.cc.a> d(o.eg.e r10) throws o.eg.d, o.ei.i {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            o.di.a r1 = new o.di.a
            r1.<init>()
            r1 = 0
            r2 = r1
        Ld:
            int r3 = r10.d()
            r4 = 1
            if (r2 >= r3) goto L16
            r3 = r4
            goto L17
        L16:
            r3 = r1
        L17:
            r5 = 0
            r6 = 2
            switch(r3) {
                case 0: goto L28;
                default: goto L1c;
            }
        L1c:
            int r3 = o.cj.e.j
            int r3 = r3 + 17
            int r7 = r3 % 128
            o.cj.e.h = r7
            int r3 = r3 % r6
            if (r3 == 0) goto L3e
            goto L3e
        L28:
            int r10 = o.cj.e.h
            int r10 = r10 + 31
            int r1 = r10 % 128
            o.cj.e.j = r1
            int r10 = r10 % r6
            if (r10 != 0) goto L34
            goto L36
        L34:
            r6 = 8
        L36:
            switch(r6) {
                case 8: goto L3a;
                default: goto L39;
            }
        L39:
            goto L3b
        L3a:
            return r0
        L3b:
            throw r5     // Catch: java.lang.Throwable -> L3c
        L3c:
            r10 = move-exception
            throw r10
        L3e:
            o.eg.b r3 = r10.b(r2)
            r7 = 233(0xe9, float:3.27E-43)
            r8 = 104(0x68, float:1.46E-43)
            int[] r7 = new int[]{r7, r6, r8, r1}
            java.lang.Object[] r8 = new java.lang.Object[r4]
            java.lang.String r9 = "\u0001\u0001"
            g(r9, r7, r1, r8)
            r7 = r8[r1]
            java.lang.String r7 = (java.lang.String) r7
            java.lang.String r7 = r7.intern()
            java.lang.String r7 = r3.r(r7)
            int r8 = r7.length()
            r9 = 4
            if (r8 != r9) goto La3
            java.lang.String r8 = r7.substring(r1, r6)
            byte r8 = o.dk.b.c(r8)
            java.lang.String r6 = r7.substring(r6, r9)
            byte r6 = o.dk.b.c(r6)
            java.lang.String r7 = ""
            int r7 = android.text.TextUtils.getTrimmedLength(r7)
            int r7 = 127 - r7
            java.lang.Object[] r4 = new java.lang.Object[r4]
            java.lang.String r9 = "\u0083\u0095\u008d\u0086\u0097"
            f(r5, r7, r5, r9, r4)
            r4 = r4[r1]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            byte[] r3 = r3.B(r4)
            byte[] r4 = o.di.a.e()
            byte[] r3 = o.ej.d.e(r3, r4)
            o.cc.a r4 = new o.cc.a
            r4.<init>(r8, r6, r3)
            r0.add(r4)
            int r2 = r2 + 1
            goto Ld
        La3:
            o.ei.i r10 = new o.ei.i
            r0 = 38
            r2 = 57
            r3 = 235(0xeb, float:3.3E-43)
            int[] r0 = new int[]{r3, r0, r2, r1}
            java.lang.Object[] r2 = new java.lang.Object[r4]
            java.lang.String r3 = "\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000"
            g(r3, r0, r1, r2)
            r0 = r2[r1]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            r10.<init>(r0)
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cj.e.d(o.eg.e):java.util.List");
    }

    private static b e(String str, String str2, int i, String str3) {
        b bVar = new b(str, str2, i, str3);
        int i2 = h + Opcodes.DREM;
        j = i2 % 128;
        int i3 = i2 % 2;
        return bVar;
    }

    private static void f(String str, int i, int[] iArr, String str2, Object[] objArr) {
        char[] cArr;
        String str3 = str2;
        int i2 = $10;
        int i3 = i2 + 109;
        $11 = i3 % 128;
        int i4 = 2;
        int i5 = i3 % 2;
        Object obj = null;
        byte[] bArr = str3;
        if (str3 != null) {
            int i6 = i2 + 67;
            $11 = i6 % 128;
            if (i6 % 2 == 0) {
                str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                obj.hashCode();
                throw null;
            }
            bArr = str3.getBytes(LocalizedMessage.DEFAULT_ENCODING);
        }
        byte[] bArr2 = bArr;
        int i7 = 0;
        switch (str != null) {
            case false:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        j jVar = new j();
        char[] cArr3 = d;
        char c2 = '0';
        if (cArr3 != null) {
            int length = cArr3.length;
            char[] cArr4 = new char[length];
            int i8 = 0;
            while (true) {
                switch (i8 < length ? '4' : (char) 6) {
                    case 6:
                        int i9 = $11 + 47;
                        $10 = i9 % 128;
                        int i10 = i9 % i4;
                        cArr3 = cArr4;
                        break;
                    default:
                        int i11 = $11 + 17;
                        $10 = i11 % 128;
                        if (i11 % i4 != 0) {
                        }
                        try {
                            Object[] objArr2 = new Object[1];
                            objArr2[i7] = Integer.valueOf(cArr3[i8]);
                            Object obj2 = a.s.get(1085633688);
                            if (obj2 == null) {
                                Class cls = (Class) a.c(10 - TextUtils.indexOf("", c2, i7, i7), (char) (TextUtils.lastIndexOf("", c2, i7) + 1), KeyEvent.normalizeMetaState(i7) + 493);
                                byte b2 = (byte) ($$b & 1);
                                byte b3 = (byte) (-b2);
                                Object[] objArr3 = new Object[1];
                                i(b2, b3, (byte) (b3 + 1), objArr3);
                                obj2 = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                a.s.put(1085633688, obj2);
                            }
                            cArr4[i8] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                            i8++;
                            i4 = 2;
                            i7 = 0;
                            c2 = '0';
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        try {
            Object[] objArr4 = {Integer.valueOf(a)};
            Object obj3 = a.s.get(-1667314477);
            long j2 = 0;
            if (obj3 == null) {
                Class cls2 = (Class) a.c((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 9, (char) (8855 - Process.getGidForName("")), 324 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)));
                byte length2 = (byte) $$a.length;
                byte b4 = (byte) (length2 - 5);
                Object[] objArr5 = new Object[1];
                i(length2, b4, (byte) (b4 + 1), objArr5);
                obj3 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                a.s.put(-1667314477, obj3);
            }
            int intValue = ((Integer) ((Method) obj3).invoke(null, objArr4)).intValue();
            if (e) {
                jVar.e = bArr2.length;
                char[] cArr5 = new char[jVar.e];
                jVar.c = 0;
                while (jVar.c < jVar.e) {
                    int i12 = $11 + 11;
                    $10 = i12 % 128;
                    switch (i12 % 2 == 0) {
                        case true:
                            cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e - 1) - jVar.c] + i] - intValue);
                            try {
                                Object[] objArr6 = {jVar, jVar};
                                Object obj4 = a.s.get(745816316);
                                if (obj4 == null) {
                                    Class cls3 = (Class) a.c(11 - (SystemClock.elapsedRealtime() > j2 ? 1 : (SystemClock.elapsedRealtime() == j2 ? 0 : -1)), (char) View.MeasureSpec.getMode(0), 206 - ExpandableListView.getPackedPositionChild(j2));
                                    byte b5 = (byte) (-1);
                                    Object[] objArr7 = new Object[1];
                                    i((byte) ($$b & 29), b5, (byte) (b5 + 1), objArr7);
                                    obj4 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    a.s.put(745816316, obj4);
                                }
                                ((Method) obj4).invoke(null, objArr6);
                                break;
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        default:
                            cArr5[jVar.c] = (char) (cArr3[bArr2[(jVar.e >>> 0) / jVar.c] + i] << intValue);
                            try {
                                Object[] objArr8 = {jVar, jVar};
                                Object obj5 = a.s.get(745816316);
                                if (obj5 == null) {
                                    Class cls4 = (Class) a.c(9 - ImageFormat.getBitsPerPixel(0), (char) View.resolveSizeAndState(0, 0, 0), TextUtils.indexOf("", "", 0) + 207);
                                    byte b6 = (byte) (-1);
                                    Object[] objArr9 = new Object[1];
                                    i((byte) ($$b & 29), b6, (byte) (b6 + 1), objArr9);
                                    obj5 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                    a.s.put(745816316, obj5);
                                }
                                ((Method) obj5).invoke(null, objArr8);
                                break;
                            } catch (Throwable th3) {
                                Throwable cause3 = th3.getCause();
                                if (cause3 == null) {
                                    throw th3;
                                }
                                throw cause3;
                            }
                    }
                    j2 = 0;
                }
                objArr[0] = new String(cArr5);
                return;
            }
            switch (c ? '6' : (char) 19) {
                case Opcodes.ISTORE /* 54 */:
                    jVar.e = cArr2.length;
                    char[] cArr6 = new char[jVar.e];
                    jVar.c = 0;
                    while (jVar.c < jVar.e) {
                        cArr6[jVar.c] = (char) (cArr3[cArr2[(jVar.e - 1) - jVar.c] - i] - intValue);
                        try {
                            Object[] objArr10 = {jVar, jVar};
                            Object obj6 = a.s.get(745816316);
                            if (obj6 == null) {
                                Class cls5 = (Class) a.c(TextUtils.indexOf("", "", 0, 0) + 10, (char) (Process.getGidForName("") + 1), 255 - AndroidCharacter.getMirror('0'));
                                byte b7 = (byte) (-1);
                                Object[] objArr11 = new Object[1];
                                i((byte) ($$b & 29), b7, (byte) (b7 + 1), objArr11);
                                obj6 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                a.s.put(745816316, obj6);
                            }
                            ((Method) obj6).invoke(null, objArr10);
                            int i13 = $10 + Opcodes.LSUB;
                            $11 = i13 % 128;
                            int i14 = i13 % 2;
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    }
                    objArr[0] = new String(cArr6);
                    return;
                default:
                    int i15 = 0;
                    jVar.e = iArr.length;
                    char[] cArr7 = new char[jVar.e];
                    while (true) {
                        jVar.c = i15;
                        if (jVar.c >= jVar.e) {
                            objArr[0] = new String(cArr7);
                            return;
                        }
                        int i16 = $10 + 93;
                        $11 = i16 % 128;
                        switch (i16 % 2 == 0) {
                            case false:
                                cArr7[jVar.c] = (char) (cArr3[iArr[(jVar.e - 1) - jVar.c] - i] - intValue);
                                i15 = jVar.c + 1;
                                break;
                            default:
                                cArr7[jVar.c] = (char) (cArr3[iArr[(jVar.e + 0) >> jVar.c] / i] << intValue);
                                i15 = jVar.c - 1;
                                break;
                        }
                    }
            }
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:96:0x0129, code lost:
    
        if (r0[r4.d] == 1) goto L74;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(java.lang.String r23, int[] r24, boolean r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 862
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.cj.e.g(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }
}
