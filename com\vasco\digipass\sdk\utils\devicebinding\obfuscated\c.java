package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingBiometricAuthenticationCallback;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.devicebinding.R;
import java.util.concurrent.Executor;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\bÀ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0013\u0010\u0014J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u0002J\u0016\u0010\u0005\u001a\u00020\t2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u0004J\u0016\u0010\u0005\u001a\u00020\f2\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\nJ.\u0010\u0005\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\r2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u000b\u001a\u00020\n¨\u0006\u0015"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/c;", "", "Landroidx/fragment/app/FragmentActivity;", "activity", "", "a", "Landroid/content/Context;", "context", "hasBiometricCapability", "", "", "fallbackOnDeviceCredential", "Landroidx/biometric/BiometricPrompt$PromptInfo;", "", "salt", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBindingBiometricAuthenticationCallback;", "deviceBindingBiometricAuthenticationCallback", "Landroidx/biometric/BiometricPrompt$CryptoObject;", "cryptoObject", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\c.smali */
public final class c {
    public static final c a = new c();

    private c() {
    }

    public final int a(FragmentActivity activity) {
        Intrinsics.checkNotNullParameter(activity, "activity");
        return BiometricManager.from(activity).canAuthenticate(32783);
    }

    public final void a(Context context, int hasBiometricCapability) {
        Intrinsics.checkNotNullParameter(context, "context");
        if (hasBiometricCapability == 11) {
            String string = context.getString(R.string.biometric_none_enrolled_error_msg);
            Intrinsics.checkNotNullExpressionValue(string, "context.getString(R.stri…_none_enrolled_error_msg)");
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.BIOMETRIC_AUTH_NOT_ENROLLED_ERROR, new Throwable(string));
        }
        if (hasBiometricCapability != 12) {
            String string2 = context.getString(R.string.biometric_unspecified_error_msg);
            Intrinsics.checkNotNullExpressionValue(string2, "context.getString(R.stri…ic_unspecified_error_msg)");
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.BIOMETRIC_UNSPECIFIED_ERROR, new Throwable(string2));
        }
        String string3 = context.getString(R.string.biometric_hardware_error_msg);
        Intrinsics.checkNotNullExpressionValue(string3, "context.getString(R.stri…etric_hardware_error_msg)");
        throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.BIOMETRIC_NO_HARDWARE_ERROR, new Throwable(string3));
    }

    public final BiometricPrompt.PromptInfo a(Context context, boolean fallbackOnDeviceCredential) {
        Intrinsics.checkNotNullParameter(context, "context");
        BiometricPrompt.PromptInfo.Builder subtitle = new BiometricPrompt.PromptInfo.Builder().setTitle(context.getString(R.string.biometric_prompt_title_text)).setSubtitle(context.getString(R.string.biometric_prompt_subtitle_text));
        Intrinsics.checkNotNullExpressionValue(subtitle, "Builder()\n            .s…ic_prompt_subtitle_text))");
        if (fallbackOnDeviceCredential) {
            subtitle.setAllowedAuthenticators(32783);
        } else {
            subtitle.setAllowedAuthenticators(15).setNegativeButtonText(context.getString(R.string.biometric_prompt_cancel));
        }
        BiometricPrompt.PromptInfo build = subtitle.build();
        Intrinsics.checkNotNullExpressionValue(build, "prompt.build()");
        return build;
    }

    public final void a(String salt, FragmentActivity activity, DeviceBindingBiometricAuthenticationCallback deviceBindingBiometricAuthenticationCallback, BiometricPrompt.CryptoObject cryptoObject, boolean fallbackOnDeviceCredential) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        Intrinsics.checkNotNullParameter(activity, "activity");
        Intrinsics.checkNotNullParameter(deviceBindingBiometricAuthenticationCallback, "deviceBindingBiometricAuthenticationCallback");
        Intrinsics.checkNotNullParameter(cryptoObject, "cryptoObject");
        BiometricPrompt.PromptInfo a2 = a(activity, fallbackOnDeviceCredential);
        Executor mainExecutor = ContextCompat.getMainExecutor(activity);
        Intrinsics.checkNotNullExpressionValue(mainExecutor, "getMainExecutor(activity)");
        new BiometricPrompt(activity, mainExecutor, new a(salt, deviceBindingBiometricAuthenticationCallback)).authenticate(a2, cryptoObject);
    }
}
