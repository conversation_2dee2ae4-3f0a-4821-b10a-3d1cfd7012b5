package com.google.android.play.core.appupdate;

import android.os.Bundle;
import android.os.RemoteException;
import com.google.android.gms.tasks.TaskCompletionSource;

/* compiled from: com.google.android.play:app-update@@2.1.0 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\play\core\appupdate\zzo.smali */
class zzo extends com.google.android.play.core.appupdate.internal.zzg {
    final com.google.android.play.core.appupdate.internal.zzm zza;
    final TaskCompletionSource zzb;
    final /* synthetic */ zzr zzc;

    zzo(zzr zzrVar, com.google.android.play.core.appupdate.internal.zzm zzmVar, TaskCompletionSource taskCompletionSource) {
        this.zzc = zzrVar;
        this.zza = zzmVar;
        this.zzb = taskCompletionSource;
    }

    @Override // com.google.android.play.core.appupdate.internal.zzh
    public void zzb(Bundle bundle) throws RemoteException {
        this.zzc.zza.zzu(this.zzb);
        this.zza.zzd("onCompleteUpdate", new Object[0]);
    }

    @Override // com.google.android.play.core.appupdate.internal.zzh
    public void zzc(Bundle bundle) throws RemoteException {
        this.zzc.zza.zzu(this.zzb);
        this.zza.zzd("onRequestInfo", new Object[0]);
    }
}
