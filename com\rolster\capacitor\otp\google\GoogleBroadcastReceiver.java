package com.rolster.capacitor.otp.google;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import com.google.android.gms.auth.api.phone.SmsRetriever;
import com.google.android.gms.common.api.Status;
import com.rolster.capacitor.otp.OtpReceiveListener;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes12\com\rolster\capacitor\otp\google\GoogleBroadcastReceiver.smali */
public class GoogleBroadcastReceiver extends BroadcastReceiver {
    private final OtpReceiveListener listener;

    public GoogleBroadcastReceiver(OtpReceiveListener listener) {
        this.listener = listener;
    }

    @Override // android.content.BroadcastReceiver
    public void onReceive(Context context, Intent intent) {
        if (SmsRetriever.SMS_RETRIEVED_ACTION.equals(intent.getAction())) {
            Bundle extras = intent.getExtras();
            Status smsRetrieverStatus = (Status) extras.get("com.google.android.gms.auth.api.phone.EXTRA_STATUS");
            switch (smsRetrieverStatus.getStatusCode()) {
                case 0:
                    Intent consentIntent = (Intent) extras.getParcelable(SmsRetriever.EXTRA_CONSENT_INTENT);
                    this.listener.onSmsReceivedSuccess(consentIntent, "handlerGoogleSMS");
                    break;
                case 15:
                    this.listener.onSmsReceivedTimeOut();
                    break;
                default:
                    this.listener.onSmsReceivedError("Unknown error");
                    break;
            }
        }
    }
}
