package fr.antelop.sdk.firebase;

import android.content.Context;
import com.google.firebase.messaging.RemoteMessage;
import o.bo.a;
import o.bo.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\firebase\AntelopFirebaseMessagingUtil.smali */
public final class AntelopFirebaseMessagingUtil {
    public static void onTokenRefresh(Context context) {
        a.e(context);
    }

    public static boolean onMessageReceived(Context context, RemoteMessage remoteMessage) {
        return a.e(context, e.a(remoteMessage));
    }
}
