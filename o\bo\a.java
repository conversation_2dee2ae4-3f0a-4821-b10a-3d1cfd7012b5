package o.bo;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.lang.reflect.Method;
import o.a.k;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bo\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static Handler a;
    private static char b;
    private static char c;
    private static char d;
    private static char e;
    private static int f;
    private static long g;
    private static int i;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        b();
        KeyEvent.normalizeMetaState(0);
        int i2 = i + 17;
        f = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void b() {
        c = (char) 40437;
        e = (char) 28975;
        b = (char) 35216;
        d = (char) 13768;
        g = 5630254830113076985L;
    }

    static void init$0() {
        $$a = new byte[]{29, -34, -102, -75};
        $$b = 37;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r6 = r6 * 2
            int r6 = r6 + 112
            int r8 = r8 * 4
            int r8 = r8 + 4
            byte[] r0 = o.bo.a.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L35
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r7) goto L2c
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2c:
            r3 = r0[r8]
            r5 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L35:
            int r6 = r6 + r7
            int r7 = r9 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.a.k(int, int, int, java.lang.Object[]):void");
    }

    private static Handler e() {
        int i2 = i + 45;
        f = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                if (a == null) {
                    Object[] objArr = new Object[1];
                    h("鏠突椓웻澦突Რ溋㓕מ\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 28, objArr);
                    HandlerThread handlerThread = new HandlerThread(((String) objArr[0]).intern());
                    handlerThread.start();
                    a = new o.ee.b(handlerThread.getLooper());
                    int i3 = i + 75;
                    f = i3 % 128;
                    int i4 = i3 % 2;
                }
                return a;
        }
    }

    public static void e(final Context context) {
        String intern;
        Object obj;
        int i2 = f + 49;
        i = i2 % 128;
        int i3 = i2 % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", 32 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr);
        String intern2 = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        h("嘣᧯႞욦뛠\ue33a驪ᢢ\uf5f7㟰ᆹ㩀\uf7eb掙", View.MeasureSpec.getSize(0) + 14, objArr2);
        o.ee.g.d(intern2, ((String) objArr2[0]).intern());
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 31, objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        j("\uf006繖\uec9f嫵쥆㞙ꗡᐌ芄\uf0d6缱\ued77寖쨜㠧\ua6fbᕙ荤\uf1ba怟\uee53岯쫷㥇ꞟᗧ葳\uf296惚\uef2f嵲쯈㩩ꡭᛛ蔞\uf36c憨퀊帞첱㫢ꥌទ藠\uf427抔탟強춏", (ViewConfiguration.getScrollDefaultDelay() >> 16) + 36433, objArr4);
        o.ee.g.d(intern3, ((String) objArr4[0]).intern());
        switch (!o.ei.c.c().b() ? '!' : (char) 22) {
            case 22:
                e().post(new Runnable() { // from class: o.bo.a$$ExternalSyntheticLambda0
                    @Override // java.lang.Runnable
                    public final void run() {
                        a.b(context);
                    }
                });
                int i4 = f + Opcodes.LSUB;
                i = i4 % 128;
                if (i4 % 2 == 0) {
                    return;
                }
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                int i5 = i + Opcodes.DNEG;
                f = i5 % 128;
                switch (i5 % 2 != 0) {
                    case true:
                        o.ee.g.c();
                        Object[] objArr5 = new Object[1];
                        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", 32 - (Process.myTid() >> 22), objArr5);
                        intern = ((String) objArr5[0]).intern();
                        Object[] objArr6 = new Object[1];
                        j("\uf006蟮`鞽⾦ꞁ㽱띤佄윾弁휏滶\ue6d4绷\uf6e3軙ه鹪ᙎ긱☑븛㖦췟䗇\uddb7嗚\ued6c敩ﵓ畾ഠ蔎ᳲ铆Ⳅ꒥㲓둿䱻쑝尷", 30697 - View.resolveSize(0, 0), objArr6);
                        obj = objArr6[0];
                        break;
                    default:
                        o.ee.g.c();
                        Object[] objArr7 = new Object[1];
                        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", 32 >> (Process.myTid() * 47), objArr7);
                        intern = ((String) objArr7[0]).intern();
                        Object[] objArr8 = new Object[1];
                        j("\uf006蟮`鞽⾦ꞁ㽱띤佄윾弁휏滶\ue6d4绷\uf6e3軙ه鹪ᙎ긱☑븛㖦췟䗇\uddb7嗚\ued6c敩ﵓ畾ഠ蔎ᳲ铆Ⳅ꒥㲓둿䱻쑝尷", 22386 << View.resolveSize(0, 1), objArr8);
                        obj = objArr8[0];
                        break;
                }
                o.ee.g.d(intern, ((String) obj).intern());
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void b(Context context) {
        try {
            new d();
            d.a(context);
            int i2 = i + 91;
            f = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    return;
                default:
                    throw null;
            }
        } catch (g e2) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 32, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            j("\uf006\ue9c0쎳뵓霞烯檭䑊㸴៰\uf1dd\ueb81앎뼚颫狭氹䘬㿲\u19ca\uf389\ued5f윐ꃼ骕瑩渴䟱⇃᮷\uf54b\uef14죨ꊺ鱢", 6599 - Color.alpha(0), objArr2);
            o.ee.g.a(intern, ((String) objArr2[0]).intern(), e2);
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void e(Context context, String str, String str2, String str3) {
        int i2 = f + Opcodes.LSHR;
        i = i2 % 128;
        boolean z = i2 % 2 != 0;
        b(context, str, str2, str3);
        switch (z) {
            case false:
                return;
            default:
                int i3 = 39 / 0;
                return;
        }
    }

    public static boolean e(final Context context, i iVar) {
        Object[] objArr = new Object[1];
        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", 33 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        j("\uf006ⵔ䪂柵蕖ꊅ\udffa\ufd4b᪔㟐唲犛꿨촷\uea95ߑ┽", 56659 - (Process.myPid() >> 22), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (iVar == null) {
            int i2 = i + 57;
            f = i2 % 128;
            int i3 = i2 % 2;
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            j("\uf006縆\uec26娏젞㘟ꐎሉ耄า簆\uea01堀옍㐑ꈃဝ鹘ౖ穚\ue81e嘝쐑㈐ꀞ⸄鱓ਂ研\ue606各숓ど븦Ⱔ騾ࠤ瘪\ue426刭쀠临밪⨭頫٤瑪\ue266倷\ude37䰯멺⠾阳б爭\ue024渽\udc3a䨼렲♴鐾Ȣ", (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 36352, objArr3);
            o.ee.g.d(intern, ((String) objArr3[0]).intern());
            int i4 = f + Opcodes.DDIV;
            i = i4 % 128;
            if (i4 % 2 == 0) {
                return false;
            }
            int i5 = 11 / 0;
            return false;
        }
        b c2 = new j().c(context);
        if (c2 == null) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            h("嘣᧯ᯭ排瞎둳ጽᙳ易☺ക갓곘톲\u187d겖쒐䜣뙏鷦웝\ue11f鏛관젫⎵㙰\ueab5妖팗엎ἆ妖팗晑\ue254砦\ud96f嘸뗴끽\uf66a棘愦\uf6e1ᄵ\uf741췽啕ᄦ\uf741췽텆徔妖팗マ鎋焼ᘷ햰⓵鏠突솲ꕛꄖ䭁", (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 68, objArr4);
            o.ee.g.d(intern, ((String) objArr4[0]).intern());
            switch (iVar.a() != null) {
                default:
                    switch (iVar.d() != null ? (char) 15 : (char) 1) {
                        case 1:
                            break;
                        default:
                            o.ee.g.c();
                            Object[] objArr5 = new Object[1];
                            h("嘣᧯ᯭ排瞎둳ጽᙳ易☺ക갓곘톲\u187d겖쒐䜣뙏鷦Ꞙ\uf215㋯㶭晑\ue254⋻정㋯㶭嘣᧯幼蕧暾刀ꢚ록妖팗鬚膝鏠突椓웻澦突랉௷\udfa2\uf75eക갓놆颹\udcd9⃪\uebd4穞珰㛠\u0ece呤埀셾榢见嘣᧯햁Ņ妖팗躿ᱏㅐ䵲幼蕧햰⓵騺厤\ueb36覆棘愦鬚膝\uf5ab亼瑟鰦፝\ue2e0웋Ṃ", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 97, objArr5);
                            o.ee.g.d(intern, ((String) objArr5[0]).intern());
                            break;
                    }
                case false:
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    j("\uf006禆\ue326沏혞徟줎㊉밄▲꼆ᢁ舀\u0b8d甑ﺃ栝퇘孖쓚专랓ℋꪗᐗ鶙ܐ炓度掝\ued18嚘쁩䦬댤㲯\ua63e\u2fec餡ʡ谵\uf5e0缪\ue8ac刦\udba8䔲캢㠼ꇸ⬺钴ḩ螹\uf133窱\ue421淰휠䂢쨰㎷봾⚰遀\u19cb茋ೌ癄\uffc9楃틊屒얌伃료≊ꯐᔇ黅ࡖ燖רּ擓\uee59埙셍䫗둟㷗꜓მ驁Δ赃\uf6d7恻\ue9ef卮\udcfe䙾쾬㥻ꋦⱤ閠ὰ裦\uf26e", (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 35201, objArr6);
                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                    return false;
            }
        } else if (!c2.d(iVar)) {
            o.ee.g.c();
            Object[] objArr7 = new Object[1];
            j("\uf006쳖覆䙿͞쀏鳮妹ᚄ퍢逦泱⧀\ue69dꍱ怳㴝令뛶珊びണ짫蛇䎗i\udd30騃囁Ꭽ큸굈橩⛬\ue3caꂈ絪㨹\uf71b돍烡䵱੍윝菱䂼ᶂ\uda44靹君პ\uedb8ꩫ杅␜\ue0eb", TextUtils.indexOf("", "", 0) + 15569, objArr7);
            o.ee.g.e(intern, ((String) objArr7[0]).intern());
            return false;
        }
        final String a2 = iVar.a();
        final String d2 = iVar.d();
        final String c3 = iVar.c();
        if (a2 == null || d2 == null) {
            o.ee.g.c();
            Object[] objArr8 = new Object[1];
            h("嘣᧯ᯭ排瞎둳ጽᙳ易☺ക갓곘톲\u187d겖쒐䜣뙏鷦斔街\ufb10편谅쑳믨縨벊豔晑\ue254\uef09췒\ud980Ѽ蟨明㰻\ueaea햁Ņㅐ䵲", TextUtils.getOffsetBefore("", 0) + 44, objArr8);
            o.ee.g.e(intern, ((String) objArr8[0]).intern());
            return true;
        }
        o.ee.g.c();
        Object[] objArr9 = new Object[1];
        j("\uf006푠룪鴹憆䘙⩢ໟ팴랤鰊恧䓘⤻ල툅뙽骞罺䏬⠉౻탆땆馢縎䉶⛚\u0b4a\uefe2됋顱糰䅂▨ਝ\uee71늪靓箪䀞⑵࣪\ued57놮阛窅廧", 9318 - TextUtils.indexOf((CharSequence) "", '0'), objArr9);
        o.ee.g.d(intern, ((String) objArr9[0]).intern());
        e().post(new Runnable() { // from class: o.bo.a$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                a.e(context, c3, a2, d2);
            }
        });
        return true;
    }

    private static void b(Context context, String str, String str2, String str3) {
        Object[] objArr = new Object[1];
        h("䐒谞砦\ud96fⅰ﹪ꠄ䋼\uef09췒\ue3abΈ\uf0c2込\uf7eb掙ᯭ排瞎둳ጽᙳ嶳ᛍ闃\udcb7倷騍㙰\ueab5鸀籇", 31 - TextUtils.lastIndexOf("", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        j("\uf019꿸俀\uefa3辀⽵콈漌༐껫仛\ueec7躬⺊츫渍\u0e79깗䶿\uedc3趰ⶅ춘浥ൎ괬䴮\uecfc貝ⳤ쳓", TextUtils.lastIndexOf("", '0') + 24548, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr3 = new Object[1];
        j("\uf019곶䧜\ue6cd莸₻\udd94穂ᝠ둅兇ษꬔ䠄\ue4b7膣㺙\udbf9磣ᖎ늌潡\u0c5bꥍ䘰\ue328聛㲬\ud9a5", (Process.myPid() >> 22) + 23789, objArr3);
        o.ee.g.d(intern, sb2.append(((String) objArr3[0]).intern()).append(str3).toString());
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr4 = new Object[1];
        h("⠁晙ୗꍵ䉸\ue8b7鄂гꄖ䭁澦突꾚箤超聢ꪛ緄鏛관ꄖ䭁澦突꾚箤\ue6c8\uf8a3マ鎋焼ᘷ㋯㶭嘣᧯러\uf685\ud826顆", 39 - TextUtils.indexOf("", "", 0), objArr4);
        o.ee.g.d(intern, sb3.append(((String) objArr4[0]).intern()).append(str2).toString());
        if (str3 == null || str3.isEmpty()) {
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            j("\uf019᪶╜」媸敻瀔骂ꕠ뀅\udac7\ue569\uf014\u1ac4┷っ媙改灣髐ꖂ끸\udac7\ue583\uf028\u1ae0▔〷嫡旜瀏髻ꖫ끋\udae1\ue5ba\uf054ᬆ■", (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 60078, objArr5);
            o.ee.g.e(intern, ((String) objArr5[0]).intern());
            return;
        }
        switch (str != null ? '!' : (char) 2) {
            case '!':
                int i2 = f + 21;
                i = i2 % 128;
                int i3 = i2 % 2;
                if (!str.isEmpty()) {
                    int i4 = i + 13;
                    f = i4 % 128;
                    boolean z = i4 % 2 == 0;
                    o.dc.a.e(str3, str);
                    switch (z) {
                        case false:
                            return;
                        default:
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                    }
                }
                break;
        }
        Object[] objArr6 = new Object[1];
        h("훁俕쩟洃磸Ꮮ", Color.argb(0, 0, 0, 0) + 6, objArr6);
        switch (((String) objArr6[0]).intern().equals(str2) ? 'A' : '4') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                o.ee.g.c();
                Object[] objArr7 = new Object[1];
                h("⠁晙ୗꍵ䉸\ue8b7鄂гꄖ䭁澦突꾚箤超聢ꪛ緄쓇྅唖嘢拚곘뙏鷦훁俕쩟洃磸Ꮮ\ue3e7㞱\uf43c엄", Color.alpha(0) + 35, objArr7);
                o.ee.g.d(intern, ((String) objArr7[0]).intern());
                o.dc.a.b(context, str3);
                return;
            default:
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                h("⠁晙ୗꍵ䉸\ue8b7鄂гꄖ䭁澦突꾚箤超聢ꪛ緄馭잧顾ἲఽ\uf75d䑦蓪鏛관ꄖ䭁澦突꾚箤䭜ٜ暾刀啕ᄦ칩㴛镵둶㓤➽", View.resolveSize(0, 0) + 45, objArr8);
                o.ee.g.e(intern, ((String) objArr8[0]).intern());
                int i5 = f + 71;
                i = i5 % 128;
                if (i5 % 2 != 0) {
                    int i6 = 36 / 0;
                    return;
                }
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void h(java.lang.String r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 586
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bo.a.h(java.lang.String, int, java.lang.Object[]):void");
    }

    private static void j(String str, int i2, Object[] objArr) {
        char[] charArray;
        int i3 = $10 + 1;
        int i4 = i3 % 128;
        $11 = i4;
        int i5 = i3 % 2;
        switch (str != null ? '+' : '?') {
            case '+':
                int i6 = i4 + 45;
                $10 = i6 % 128;
                int i7 = i6 % 2;
                charArray = str.toCharArray();
                break;
            default:
                charArray = str;
                break;
        }
        char[] cArr = charArray;
        k kVar = new k();
        kVar.a = i2;
        int length = cArr.length;
        long[] jArr = new long[length];
        kVar.b = 0;
        while (kVar.b < cArr.length) {
            int i8 = kVar.b;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr[kVar.b]), kVar, kVar};
                Object obj = o.e.a.s.get(806930129);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(TextUtils.getCapsMode("", 0, 0) + 10, (char) (60578 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 354 - TextUtils.getTrimmedLength(""));
                    byte b2 = (byte) ($$b & 3);
                    byte b3 = (byte) (b2 - 1);
                    Object[] objArr3 = new Object[1];
                    k(b2, b3, b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Object.class, Object.class);
                    o.e.a.s.put(806930129, obj);
                }
                jArr[i8] = ((Long) ((Method) obj).invoke(null, objArr2)).longValue() ^ (g ^ (-5249873463433509232L));
                try {
                    Object[] objArr4 = {kVar, kVar};
                    Object obj2 = o.e.a.s.get(-10300751);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c((-16777204) - Color.rgb(0, 0, 0), (char) (55185 - Color.blue(0)), 537 - TextUtils.lastIndexOf("", '0'));
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        Object[] objArr5 = new Object[1];
                        k(b4, b5, b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-10300751, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        char[] cArr2 = new char[length];
        kVar.b = 0;
        while (kVar.b < cArr.length) {
            cArr2[kVar.b] = (char) jArr[kVar.b];
            try {
                Object[] objArr6 = {kVar, kVar};
                Object obj3 = o.e.a.s.get(-10300751);
                if (obj3 == null) {
                    Class cls3 = (Class) o.e.a.c(12 - View.resolveSize(0, 0), (char) (55185 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 538);
                    byte b6 = (byte) 0;
                    byte b7 = b6;
                    Object[] objArr7 = new Object[1];
                    k(b6, b7, b7, objArr7);
                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                    o.e.a.s.put(-10300751, obj3);
                }
                ((Method) obj3).invoke(null, objArr6);
            } catch (Throwable th3) {
                Throwable cause3 = th3.getCause();
                if (cause3 == null) {
                    throw th3;
                }
                throw cause3;
            }
        }
        String str2 = new String(cArr2);
        int i9 = $10 + 15;
        $11 = i9 % 128;
        switch (i9 % 2 == 0 ? (char) 17 : 'Z') {
            case 'Z':
                objArr[0] = str2;
                return;
            default:
                int i10 = 37 / 0;
                objArr[0] = str2;
                return;
        }
    }
}
