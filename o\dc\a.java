package o.dc;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.core.view.ViewCompat;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import o.a.m;
import o.ee.g;
import o.ee.j;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final ArrayList<o.az.c> a;
    private static long b;
    private static final Object c;
    private static final List<Long> d;
    private static char[] e;
    private static int g;
    private static int h;

    static void c() {
        char[] cArr = new char[1070];
        ByteBuffer.wrap("U\u008f\u0080ÒÿMÕÜ\u0000W~ÄUJ\u0083ÄþUÔô\u0003vyûTY\u0082øùk,¦ùÛ\u0086}¬Ôy_\u0007À,BúÔ\u0087J\u00adùzt\u0000Ó-vûñ\u0080h®ë{`\u0001\u0096.\u0010ô\u0089\u0081\u0010¯\u008at\u000f\u0002¿/\fõ¶\u00824¨´u/\u0003 (%öå\u0083`,¦ùÛ\u0086}¬Ôy_\u0007À,BúÔ\u0087J\u00adùzt\u0000Ó-vûñ\u0080h®ë{`\u0001\u0096.\u0010ô\u0089\u0081\u0010¯\u008at\u000f\u0002¿/\fõ¶\u00824¨´u/\u0003 (%öå\u0083`¨\u0015v\u009c\u0003\u001d)ÎöD\u009cÍ©AwÌ\u001ca*±÷t\u009dêª%pï\u001db+ýðU\u009e\u0083«\bq\u0097\u001e\u000b$\u0088ñ\u0003\u009f\u008e¤yrñ\u001f<%\u00adò1\u0098¤¥ s¹\u0018Á%XóÓ\u0098^¦\u0085s@\u0019Ã&\tÌô\u0099r§éLp\u001aó'`Íù\u009a` \u009aM\u001f}4¨I×ËýV(ÙVL}Ì«dÖÑüu+äQk|áªxÑ°ÿ=*°PA\u007fÈ¥\u0000Ð\u0085þ\u001f%\u008aS\r~\u0080¤8Ó\u00adù $àR,y¹§-Ò¼ùÃ'IRÀx\u0000§ÚÍJøÛ&]M¬{K¦¤Ìiûï!8Lúze¡\u0080Ï\u0004úÈ @O\u009du\u001a \u009bÎ\u0002õ¸#!Nªt',¦ùÛ\u0086}¬Ôy_\u0007À,BúÔ\u0087J\u00adùzt\u0000Ó-vûñ\u0080h®ë{`\u0001\u0096.\u0010ô\u0089\u0081\u0010¯\u008at\u000f\u0002¿/\fõ¶\u00824¨´u/\u0003 (%öå\u0083`¨\u0015v\u009c\u0003\u001d)ìöK\u009cÀ©OwÅ\u001cp*±÷i\u009döª%pã\u001dx+àð\u0019\u009e\u0095«]q³\u001e\u0016$\u008eñ\u0003\u009f¦¤7r»\u001f8%ºò1Y\u001f\u008cbóàÙ}\fòrgYç\u008fOòúØ^\u000fÏu@XÊ\u008eSõ\u009bÛ\u0016\u000e\u009btj[ã\u0081+ô®Ú4\u0001ºw/Z³\u0080\u0002÷\u0091Ý\n\u0000\u008fvW]£\u0083\u001eö\u0089Ýæ\u0003nvê\\\u007f\u0083òéaÜì\u0002;i\u009d_\u0003,¦ùÛ\u0086\u007f¬ÒyM\u0007Ì,GúÄ\u0087J\u00adôze\u0000ô-vûë\u0080S®è{j\u0001\u0090.\u0018ô\u008b\u0081\u001c¯\u0081tA\u0002×/Iõ»\u0082>¨©u0\u0003£((ö®\u0083(¨AvØ\u0003R)×ö\u0005\u009cÇ©AwÈ\u001cr*ö÷x\u009dýª%pà\u001dy+©ð\u0006\u009e\u0085«\u0014q\u009a\u001e\u000e$\u0098,¦ùÛ\u0086\u007f¬ÒyM\u0007Ì,GúÄ\u0087J\u00adôze\u0000ô-vûë\u0080S®è{j\u0001\u0090.\u0018ô\u008b\u0081\u001c¯\u0081tA\u0002×/Iõ»\u0082>¨©u0\u0003£((ö®\u0083(¨AvØ\u0003R)×ö\u0005\u009cÈ©Iw\u0089\u001ca*þ÷=\u009dúªipä\u001dl+çð\u0000\u009e\u0081«GqÙ,¦ùÛ\u0086\u007f¬ÒyM\u0007Ì,GúÄ\u0087J\u00adôze\u0000ô-vûë\u0080S®è{j\u0001\u0090.\u0018ô\u008b\u0081\u001c¯\u0081tA\u0002À/Iõ\u0090\u0082)¨¾u<\u0003µ(5ö¤\u0083&¨[v\u0091q{¤\u0006Û\u00adñ\u0003$\u0090Z\u0011q\u008a§\u0011Ú\u0080ð!'£].p\u008a¦7Ý¨ó9&²\\AsÏ©AÜÐòQ)Ó_^ræ¨mßïõe(í^nuù«tÞ´õÅ+L^\u0085t\n«\u009bÁ\u000eô\u0089*\u0004A¼w)ª¤Àd÷¨-=@©v8\u00adÇÃMöÄ,\u0004C\u0098y\u0006¬\u0090æÞ3¯L&fù³kÍáæe0óMqgÌ°\u0006ÊÃçJ1\u0092J\fd\u009a,\u00adùÜ\u0086B¬ÍyX\u0007Ñ,BúÅ\u0087z\u00adázx\u0000þ-rûü\u0080O®â{}\u0001\u009c.\u0017ô\u0094\u0081\u001a¯\u0084t\u0015\u0002\u0084/\u0006õ»\u0082\"ö¥#Ô\\JvÅ£PÝÙöJ Í]rwé pÚö÷z!ôZGtê¡uÛ\u0094ô\u001f.\u009c[\u0012u\u008c®\u001dØ\u008cõ\u000e/³X*rõ¯|Ùíò',ªY5rT¬ßÙ\\óÒ,LFÝsL\u00adÎÆsð¹-5G«p-,éùÔ\u0086]¬Ïy\\\u0007Ä,EúÔ\u0087\t\u00adñzx\u0000î-iûä\u0080u®î{a\u0001\u0090.\u0015,\u00adùÜ\u0086B¬ÍyX\u0007Ñ,BúÅ\u0087z\u00adázx\u0000þ-rûü\u0080O®â{}\u0001\u009c.\u0017ô\u0094\u0081\u001a¯\u0084t\u0015\u0002\u0084/\u0006õ»\u0082\"¨ýut\u0003å(%ö¤\u0083:¨EvÐ\u0003I)ÚöM\u009cÈ©CwÎ\u001c5*ÿ÷r\u009díªlpç\u001dd+êð\u0014\u009e\u0085«\u0014q\u0096\u001e\u000b$ÁñM\u009fÓ¤u\u0087IR(-·\u00075Ò¿¬$,\u009cùÛ\u0086P¬ßyU\u0007À,\u0001úÙ\u0087F\u00adµzc\u0000ø-jûê\u0080m®û{l\u0001Õ.\u001fô\u0092\u0081\r¯\u008ct\u0007\u0002\u0084/\nõ´\u0082%¨´u6\u0003«(aö¢\u0083;¨\\vÖ\u0003T)×ö\u0005\u009c\u009b©\r_q\u008a\u0000,ªùÇ\u0086T¬ÜyM\u0007Ì,NúÃ\u0087m\u00adôze\u0000ø,¬ùÍ\u0086A¬ÔyK\u0007Ü,eúÌ\u0087]\u00adðt'¡SÞÁôM!Û_AtÇ:nï\u0014\u0090\u0087º\u0004o\u0084\u0011\u0005:»ì\u0016\u0091\u0089»(l£\u0016 ;®í0\u0096¡¸0m²\u0017O8ãâF\u0097ß¹bbÁ\u0014V9Ïã`\u0094â¾lc\u00ad\u0015<>µàj\u0095é¾\u008e`\u0017\u0015\u0088?\nà\u0094\u008aU¿\u008ba\u0018\n°<0á \u008b?¼´f1\u000bù=;æÎ\u0088W½\u0089gC\bÞ2AçÐ\u0089[²èdf\tè3yäø\u008ez³÷e=\u000e[3Å,ºùÀ\u0086S¬ÐyP\u0007Ñ,oúÂ\u0087]\u00adüzw\u0000ô-zûä\u0080u®ä{f\u0001\u009b.7ô\u0092\u0081\u000b¯¶t\u0015\u0002\u0082/\u001bõ´\u00826¨¸uy\u0003è(aö\u009d\u0083<¨YvÝ\u0003s)ÖöQ\u009cÈ©KwÀ\u001cv*ð÷i\u009dðªjpï\u001d@+ìð\u0006\u009e\u0082«\u001cq\u009e\u001e\u0000$Áñ\u001a\u009f\u0080¤!r¹\u001f}%°ò!\u0098á¥wséG\u0015\u0092(í¡Ç3\u0012 l8G¹\u0091(ìõÆ\u0019\u0011\u0088k\u0013F\u0096\u0090\u0010ë\u008eÅ\u0005\u0010\u0090jmE\u00ad\u009f`êëÄ}\u001f½iuDü\u009eZéÝÃ@\u001eÑhZCÕ\u009dTèÑàá5°J*`±µ1Ë¨à16äK$a\u0095¶\u000bÌ\u0084á\u00117\u0098L\u000bb\u008c·\u0005Íøâ88úM\u007fcø¸aÎâãi9ßNYdÀ¹YÏÃäF,ºùÀ\u0086S¬ÐyP\u0007Ñ,oúÂ\u0087]\u00adüzw\u0000ô-zûä\u0080u®ä{f\u0001\u009b.7ô\u0092\u0081\u000b¯¶t\u0015\u0002\u0082/\u001bõ´\u00826¨¸uy\u0003è(aö¨\u0083;¨GvÞ\u0003O)\u0099öR\u009cÉ©DwÅ\u001cp*±÷m\u009düªwpò\u001dd+úð\u0001\u009e\u0098«\u0013q\u009e\u001eE$\u008fñ\u0002\u009f\u009d¤<r·\u001f4%ºò$\u0098µ¥$s¦\u0018Û\u00813TI+Ú\u0001YÔÙªX\u0081æWK*Ô\u0000u×þ\u00ad}\u0080óVm-ü\u0003mÖï¬\u0012\u0083¾Y\u001b,\u0082\u0002?Ù\u009c¯\u000b\u0082\u0092X=/¿\u00051Øð®a\u0085è[7.´\u0005ÓÛJ®Õ\u0084W[É1\b\u0004ÊÚO±è\u00878Zæ0u\u0007ýÝ}°í\u0086r]\u00993\u001c\u0006ÔÜ\u0016³\u0083\u0089\u001a\\Ä2\u000e\t³ß,²½\u00886_¥5+\b¥Þ4µU\u0088×^Z5\u0090".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1070);
        e = cArr;
        b = 1401863541948676533L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002c  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0024  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002c -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r7, int r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 4
            int r8 = 4 - r8
            int r9 = 105 - r9
            byte[] r0 = o.dc.a.$$a
            int r7 = r7 * 4
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r9 = r8
            r3 = r1
            r5 = r2
            r8 = r7
            r1 = r0
            r0 = r10
            r10 = r9
            goto L35
        L19:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1d:
            byte r4 = (byte) r8
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L2c
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2c:
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r9 = r9 + 1
            int r7 = -r7
            int r7 = r7 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.a.i(byte, int, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{2, Base64.padSymbol, -41, 17};
        $$b = 227;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        g = 1;
        c();
        ViewConfiguration.getScrollDefaultDelay();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        Process.myTid();
        d = new ArrayList();
        c = new Object();
        a = new ArrayList<>();
        int i = h + 5;
        g = i % 128;
        int i2 = i % 2;
    }

    public static void e(o.az.c cVar) {
        int i = g + 17;
        h = i % 128;
        int i2 = i % 2;
        a.add(cVar);
        int i3 = h + 47;
        g = i3 % 128;
        int i4 = i3 % 2;
    }

    public static void a(o.az.c cVar) {
        int i = h + 81;
        g = i % 128;
        int i2 = i % 2;
        a.remove(cVar);
        int i3 = g + 55;
        h = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static void b(final Context context, final String str) {
        int i = g + 85;
        h = i % 128;
        int i2 = i % 2;
        g.c();
        Object[] objArr = new Object[1];
        f((char) (30984 - Color.red(0)), ViewConfiguration.getDoubleTapTimeout() >> 16, TextUtils.getCapsMode("", 0, 0) + 15, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (ViewConfiguration.getWindowTouchSlop() >> 8) + 15, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 33, objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        if (o.ei.c.c().q()) {
            d(context, str);
            return;
        }
        g.c();
        Object[] objArr3 = new Object[1];
        f((char) (ExpandableListView.getPackedPositionType(0L) + 30984), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), View.resolveSize(0, 0) + 15, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        f((char) View.MeasureSpec.makeMeasureSpec(0, 0), (ViewConfiguration.getTapTimeout() >> 16) + 48, Gravity.getAbsoluteGravity(0, 0) + 83, objArr4);
        g.d(intern2, ((String) objArr4[0]).intern());
        final b bVar = new b();
        o.b.a aVar = new o.b.a() { // from class: o.dc.a.1
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static char[] b;
            private static char e;
            private static int h;
            private static int i;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                h = 0;
                i = 1;
                b = new char[]{30570, 30535, 29847, 30573, 30572, 30588, 30571, 30568, 30560, 30584, 29841, 30587, 30567, 29846, 30574, 30582, 30511, 30561, 30586, 30585, 30499, 30502, 30569, 30503, 30557, 30591, 30498, 30531, 30529, 30562, 29845, 30563, 29840, 29844, 30589, 30566};
                e = (char) 17043;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(int r6, byte r7, int r8, java.lang.Object[] r9) {
                /*
                    int r6 = 73 - r6
                    int r8 = r8 * 3
                    int r8 = r8 + 4
                    int r7 = r7 * 4
                    int r7 = r7 + 1
                    byte[] r0 = o.dc.a.AnonymousClass1.$$a
                    byte[] r1 = new byte[r7]
                    int r7 = r7 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r6 = r7
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L35
                L1a:
                    r3 = r2
                L1b:
                    byte r4 = (byte) r6
                    r1[r3] = r4
                    if (r3 != r7) goto L28
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L28:
                    r4 = r0[r8]
                    int r3 = r3 + 1
                    r5 = r8
                    r8 = r6
                    r6 = r7
                    r7 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r5
                L35:
                    int r7 = -r7
                    int r7 = r7 + r8
                    int r8 = r9 + 1
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r7
                    r7 = r6
                    r6 = r5
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.dc.a.AnonymousClass1.g(int, byte, int, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{57, -45, 96, 7};
                $$b = Opcodes.RETURN;
            }

            @Override // o.b.a
            public final void a() {
                int i3 = i + 97;
                h = i3 % 128;
                switch (i3 % 2 == 0) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.b.a
            public final void e() {
                int i3 = i + 45;
                h = i3 % 128;
                switch (i3 % 2 != 0 ? '^' : (char) 29) {
                    case Opcodes.DUP2_X2 /* 94 */:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.b.a
            public final void c(o.ei.c cVar, o.bb.d dVar, o.bv.g gVar) {
                boolean z;
                int i3 = i + Opcodes.LNEG;
                h = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr5 = new Object[1];
                f(Color.green(0) + 15, "\u001a\n\u0011\u0005\u0017\"\u0002\u0010\u0011\u0005\u000b\u000e\u0000\u0013㗹", (byte) (1 - ExpandableListView.getPackedPositionGroup(0L)), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f(View.MeasureSpec.getSize(0) + 95, "\u000b\u000e\u001d!\u0012\u0004\u0003\u0010\u0001\"\u0004\u0018\t\u0006\"\u0017\"\u0005\u0011\b \u000b\f\u001d\u0001\u0005\u0005\u001e\u0012\u0001\u000b\u0012\u0016\u000f\u001c\u000e\u0002\n㙜㙜\u0001\u0005\u0011\u0005\u000b\u000e\u0011\u0004\u0016\u0000\u0005\u0001㙑㙑\u000e\u001c\r\u001c\u0016\u001e\u0000\u0017\u0005\u0017\n\r\u000b!\u0006\u0011\u0011\f\t\u0006\"\u0017\"\u0005\u0011\b \u000b\f\u0011\f\u0006\u0005\"\u0018\r\u0004\u001e\u0005\u0017㙥", (byte) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 104), objArr6);
                g.d(intern3, ((String) objArr6[0]).intern());
                o.b.c e2 = b.this.e();
                if (e2 == null) {
                    z = false;
                } else {
                    z = true;
                }
                switch (z) {
                    case false:
                        break;
                    default:
                        int i5 = i + 89;
                        h = i5 % 128;
                        switch (i5 % 2 == 0) {
                            case false:
                                e2.a(context);
                                throw null;
                            default:
                                e2.a(context);
                                break;
                        }
                }
                a.d(context, str);
                int i6 = i + 61;
                h = i6 % 128;
                int i7 = i6 % 2;
            }

            @Override // o.b.a
            public final void d(o.bb.d dVar) {
                int i3 = i + 71;
                h = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr5 = new Object[1];
                f(15 - View.combineMeasuredStates(0, 0), "\u001a\n\u0011\u0005\u0017\"\u0002\u0010\u0011\u0005\u000b\u000e\u0000\u0013㗹", (byte) (1 - View.MeasureSpec.getMode(0)), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f(96 - (ViewConfiguration.getScrollDefaultDelay() >> 16), "\u000b\u000e\u001d!\u0012\u0004\u0003\u0010\u0001\"\u0004\u0018\t\u0006\"\u0017\"\u0005\u0011\b \u000b\f\u001d\u0001\u0005\u0005\u001e\u0012\u0001\u000b\u0012\u0016\u000f\u001c\u000e\u0002\n㙍㙍\u0001\u0005\u0011\u0005\u000b\u000e\u0016\u001c\u0011 \u001e\u0001\b\u0012\u0011\u0004\u0011\u0005㙏㙏\u0011\u000f㙇㙇\u0005\u0018\u001d\u0007\u0005\u0017\n\r\u0006\t\f\n\u0001\u0005\u001f\u001d\u0012\u0006\u0004\u0016\u000e\u000b\u0011\u0005\u0017\"\u0002\u0010\u0011\u0005\u000b\u000e", (byte) (89 - (ViewConfiguration.getFadingEdgeLength() >> 16)), objArr6);
                g.d(intern3, ((String) objArr6[0]).intern());
                a.d(context, str);
                int i5 = i + 63;
                h = i5 % 128;
                int i6 = i5 % 2;
            }

            @Override // o.b.a
            public final void d(o.cb.a aVar2, o.g.b bVar2, o.bb.d dVar) {
                int i3 = i + 79;
                h = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr5 = new Object[1];
                f(Gravity.getAbsoluteGravity(0, 0) + 15, "\u001a\n\u0011\u0005\u0017\"\u0002\u0010\u0011\u0005\u000b\u000e\u0000\u0013㗹", (byte) (1 - View.MeasureSpec.getMode(0)), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                Object[] objArr6 = new Object[1];
                f(AndroidCharacter.getMirror('0') + '0', "\u000b\u000e\u001d!\u0012\u0004\u0003\u0010\u0001\"\u0004\u0018\t\u0006\"\u0017\"\u0005\u0011\b \u000b\f\u001d\u0001\u0005\u0005\u001e\u0012\u0001\u000b\u0012\u0016\u000f\u001c\u000e\u0002\n㙍㙍\u0001\u0005\u0011\u0005\u000b\u000e\u0016\u001c\u0011 \u001e\u0001\b\u0012\u0011\u0004\u0011\u0005㙏㙏\u0011\u000f㙇㙇\u0005\u0018\u001d\u0007\u0005\u0017\n\r\u0006\t\f\n\u0001\u0005\u001f\u001d\u0012\u0006\u0004\u0016\u000e\u000b\u0011\u0005\u0017\"\u0002\u0010\u0011\u0005\u000b\u000e", (byte) (89 - Color.argb(0, 0, 0, 0)), objArr6);
                g.d(intern3, ((String) objArr6[0]).intern());
                a.d(context, str);
                int i5 = h + 61;
                i = i5 % 128;
                int i6 = i5 % 2;
            }

            private static void f(int i3, String str2, byte b2, Object[] objArr5) {
                char[] cArr;
                int i4;
                char c2;
                char c3;
                if (str2 != null) {
                    int i5 = $10 + 5;
                    $11 = i5 % 128;
                    int i6 = i5 % 2;
                    cArr = str2.toCharArray();
                } else {
                    cArr = str2;
                }
                char[] cArr2 = cArr;
                m mVar = new m();
                char[] cArr3 = b;
                float f = 0.0f;
                long j = 0;
                if (cArr3 != null) {
                    int length = cArr3.length;
                    char[] cArr4 = new char[length];
                    int i7 = 0;
                    while (i7 < length) {
                        try {
                            Object[] objArr6 = {Integer.valueOf(cArr3[i7])};
                            Object obj = o.e.a.s.get(-1401577988);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((Process.getElapsedCpuTime() > j ? 1 : (Process.getElapsedCpuTime() == j ? 0 : -1)) + 16, (char) (ViewConfiguration.getPressedStateDuration() >> 16), 76 - (AudioTrack.getMinVolume() > f ? 1 : (AudioTrack.getMinVolume() == f ? 0 : -1)));
                                byte b3 = (byte) 0;
                                byte b4 = b3;
                                Object[] objArr7 = new Object[1];
                                g(b3, b4, b4, objArr7);
                                obj = cls.getMethod((String) objArr7[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr4[i7] = ((Character) ((Method) obj).invoke(null, objArr6)).charValue();
                            i7++;
                            f = 0.0f;
                            j = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr3 = cArr4;
                }
                try {
                    Object[] objArr8 = {Integer.valueOf(e)};
                    Object obj2 = o.e.a.s.get(-1401577988);
                    char c4 = 17;
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(17 - View.getDefaultSize(0, 0), (char) Color.argb(0, 0, 0, 0), 76 - (ViewConfiguration.getDoubleTapTimeout() >> 16));
                        byte b5 = (byte) 0;
                        byte b6 = b5;
                        Object[] objArr9 = new Object[1];
                        g(b5, b6, b6, objArr9);
                        obj2 = cls2.getMethod((String) objArr9[0], Integer.TYPE);
                        o.e.a.s.put(-1401577988, obj2);
                    }
                    char charValue = ((Character) ((Method) obj2).invoke(null, objArr8)).charValue();
                    char[] cArr5 = new char[i3];
                    switch (i3 % 2 != 0 ? '*' : '!') {
                        case '*':
                            int i8 = $10 + 99;
                            $11 = i8 % 128;
                            switch (i8 % 2 == 0 ? 'b' : (char) 0) {
                                case Opcodes.FADD /* 98 */:
                                    i4 = i3 + 82;
                                    cArr5[i4] = (char) (cArr2[i4] >>> b2);
                                    break;
                                default:
                                    i4 = i3 - 1;
                                    cArr5[i4] = (char) (cArr2[i4] - b2);
                                    break;
                            }
                        default:
                            i4 = i3;
                            break;
                    }
                    char c5 = 7;
                    if (i4 > 1) {
                        mVar.b = 0;
                        int i9 = $10 + Opcodes.LSHL;
                        $11 = i9 % 128;
                        int i10 = i9 % 2;
                        while (true) {
                            switch (mVar.b < i4 ? c4 : '*') {
                                case '*':
                                    c2 = c5;
                                    break;
                                default:
                                    mVar.e = cArr2[mVar.b];
                                    mVar.a = cArr2[mVar.b + 1];
                                    switch (mVar.e == mVar.a) {
                                        case false:
                                            try {
                                                Object[] objArr10 = new Object[13];
                                                objArr10[12] = mVar;
                                                objArr10[11] = Integer.valueOf(charValue);
                                                objArr10[10] = mVar;
                                                objArr10[9] = mVar;
                                                objArr10[8] = Integer.valueOf(charValue);
                                                objArr10[c5] = mVar;
                                                objArr10[6] = mVar;
                                                objArr10[5] = Integer.valueOf(charValue);
                                                objArr10[4] = mVar;
                                                objArr10[3] = mVar;
                                                objArr10[2] = Integer.valueOf(charValue);
                                                objArr10[1] = mVar;
                                                objArr10[0] = mVar;
                                                Object obj3 = o.e.a.s.get(696901393);
                                                if (obj3 == null) {
                                                    Class cls3 = (Class) o.e.a.c((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 9, (char) (8857 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), 324 - (ViewConfiguration.getKeyRepeatTimeout() >> 16));
                                                    byte length2 = (byte) $$a.length;
                                                    byte b7 = (byte) (length2 - 4);
                                                    Object[] objArr11 = new Object[1];
                                                    g(length2, b7, b7, objArr11);
                                                    obj3 = cls3.getMethod((String) objArr11[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(696901393, obj3);
                                                }
                                                if (((Integer) ((Method) obj3).invoke(null, objArr10)).intValue() != mVar.h) {
                                                    c3 = 7;
                                                    switch (mVar.c == mVar.d ? 'U' : 'D') {
                                                        case Opcodes.CASTORE /* 85 */:
                                                            mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                            mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                            int i11 = (mVar.c * charValue) + mVar.i;
                                                            int i12 = (mVar.d * charValue) + mVar.h;
                                                            cArr5[mVar.b] = cArr3[i11];
                                                            cArr5[mVar.b + 1] = cArr3[i12];
                                                            break;
                                                        default:
                                                            int i13 = (mVar.c * charValue) + mVar.h;
                                                            int i14 = (mVar.d * charValue) + mVar.i;
                                                            cArr5[mVar.b] = cArr3[i13];
                                                            cArr5[mVar.b + 1] = cArr3[i14];
                                                            int i15 = $11 + Opcodes.LSHL;
                                                            $10 = i15 % 128;
                                                            int i16 = i15 % 2;
                                                            break;
                                                    }
                                                } else {
                                                    int i17 = $11 + 37;
                                                    $10 = i17 % 128;
                                                    int i18 = i17 % 2;
                                                    try {
                                                        Object[] objArr12 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                        Object obj4 = o.e.a.s.get(1075449051);
                                                        if (obj4 != null) {
                                                            c3 = 7;
                                                        } else {
                                                            Class cls4 = (Class) o.e.a.c(Color.red(0) + 11, (char) View.combineMeasuredStates(0, 0), 65 - View.getDefaultSize(0, 0));
                                                            byte b8 = (byte) 3;
                                                            byte b9 = (byte) (b8 - 3);
                                                            Object[] objArr13 = new Object[1];
                                                            g(b8, b9, b9, objArr13);
                                                            String str3 = (String) objArr13[0];
                                                            c3 = 7;
                                                            obj4 = cls4.getMethod(str3, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                            o.e.a.s.put(1075449051, obj4);
                                                        }
                                                        int intValue = ((Integer) ((Method) obj4).invoke(null, objArr12)).intValue();
                                                        int i19 = (mVar.d * charValue) + mVar.h;
                                                        cArr5[mVar.b] = cArr3[intValue];
                                                        cArr5[mVar.b + 1] = cArr3[i19];
                                                        break;
                                                    } catch (Throwable th2) {
                                                        Throwable cause2 = th2.getCause();
                                                        if (cause2 == null) {
                                                            throw th2;
                                                        }
                                                        throw cause2;
                                                    }
                                                }
                                            } catch (Throwable th3) {
                                                Throwable cause3 = th3.getCause();
                                                if (cause3 == null) {
                                                    throw th3;
                                                }
                                                throw cause3;
                                            }
                                        default:
                                            c3 = c5;
                                            cArr5[mVar.b] = (char) (mVar.e - b2);
                                            cArr5[mVar.b + 1] = (char) (mVar.a - b2);
                                            break;
                                    }
                                    mVar.b += 2;
                                    c5 = c3;
                                    c4 = 17;
                            }
                        }
                    } else {
                        c2 = 7;
                    }
                    int i20 = 0;
                    while (true) {
                        switch (i20 < i3 ? c2 : (char) 15) {
                            case 7:
                                int i21 = $10 + Opcodes.DNEG;
                                $11 = i21 % 128;
                                int i22 = i21 % 2;
                                cArr5[i20] = (char) (cArr5[i20] ^ 13722);
                                i20++;
                            default:
                                objArr5[0] = new String(cArr5);
                                return;
                        }
                    }
                } catch (Throwable th4) {
                    Throwable cause4 = th4.getCause();
                    if (cause4 == null) {
                        throw th4;
                    }
                    throw cause4;
                }
            }
        };
        o.b.c cVar = new o.b.c(context);
        bVar.d(cVar);
        cVar.c(context, aVar, new e(), null, null);
        int i3 = g + 27;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? 'P' : (char) 5) {
            case 5:
                return;
            default:
                throw null;
        }
    }

    static void d(Context context, String str) {
        String b2 = new o.dd.e(context).b(10, 3, str, null, null, null, null);
        switch (b2 != null) {
            case false:
                break;
            default:
                int i = h + 67;
                g = i % 128;
                int i2 = i % 2;
                switch (b2.length() == 0) {
                    case true:
                        break;
                    default:
                        try {
                            o.eg.b bVar = new o.eg.b(b2);
                            int i3 = h + 61;
                            g = i3 % 128;
                            int i4 = i3 % 2;
                            g.c();
                            Object[] objArr = new Object[1];
                            f((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 30984), ViewConfiguration.getWindowTouchSlop() >> 8, 14 - ((byte) KeyEvent.getModifierMetaStateMask()), objArr);
                            String intern = ((String) objArr[0]).intern();
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr2 = new Object[1];
                            f((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 30131), 254 - (ViewConfiguration.getTouchSlop() >> 8), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 43, objArr2);
                            g.d(intern, sb.append(((String) objArr2[0]).intern()).append(b2).toString());
                            c(context, bVar);
                            break;
                        } catch (o.eg.d e2) {
                            g.c();
                            Object[] objArr3 = new Object[1];
                            f((char) ((ViewConfiguration.getFadingEdgeLength() >> 16) + 30984), KeyEvent.keyCodeFromString(""), View.MeasureSpec.makeMeasureSpec(0, 0) + 15, objArr3);
                            String intern2 = ((String) objArr3[0]).intern();
                            Object[] objArr4 = new Object[1];
                            f((char) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), MotionEvent.axisFromString("") + Opcodes.INSTANCEOF, Color.argb(0, 0, 0, 0) + 62, objArr4);
                            g.a(intern2, ((String) objArr4[0]).intern(), e2);
                            return;
                        }
                }
        }
        g.c();
        Object[] objArr5 = new Object[1];
        f((char) (View.MeasureSpec.makeMeasureSpec(0, 0) + 30984), View.combineMeasuredStates(0, 0), TextUtils.indexOf("", "", 0, 0) + 15, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        f((char) (20889 - (ViewConfiguration.getPressedStateDuration() >> 16)), Gravity.getAbsoluteGravity(0, 0) + Opcodes.LXOR, 62 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr6);
        g.d(intern3, ((String) objArr6[0]).intern());
    }

    public static void c(Context context, o.eg.b bVar) {
        d dVar;
        synchronized (c) {
            try {
                h d2 = d(bVar);
                Long l = null;
                if (d2 == null) {
                    dVar = null;
                } else {
                    dVar = d(context, bVar, d2);
                    if (dVar != null) {
                        l = Long.valueOf(dVar.d());
                    }
                }
                new o.dg.d();
                o.dg.a e2 = o.dg.d.d(d2).e(context, bVar, l);
                if (dVar != null) {
                    if (e2.d()) {
                        g.c();
                        Object[] objArr = new Object[1];
                        f((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 30984), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), TextUtils.getOffsetAfter("", 0) + 15, objArr);
                        String intern = ((String) objArr[0]).intern();
                        Object[] objArr2 = new Object[1];
                        f((char) (ImageFormat.getBitsPerPixel(0) + 1), Gravity.getAbsoluteGravity(0, 0) + 297, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 55, objArr2);
                        g.d(intern, ((String) objArr2[0]).intern());
                        c.c(context, Long.valueOf(dVar.d()));
                        d.add(Long.valueOf(dVar.d()));
                    }
                    if (e2.b() != null) {
                        g.c();
                        Object[] objArr3 = new Object[1];
                        f((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 30984), TextUtils.indexOf("", "", 0), View.MeasureSpec.getSize(0) + 15, objArr3);
                        String intern2 = ((String) objArr3[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr4 = new Object[1];
                        f((char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 352 - TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 52, objArr4);
                        g.d(intern2, sb.append(((String) objArr4[0]).intern()).append(e2.b()).toString());
                        c.d(context, e2.b());
                        d.remove(e2.b());
                    }
                }
                e2.a().b(context);
                if (e2.e() != null) {
                    o.db.b.a().e(context, e2.e(), true);
                }
            } catch (o.dc.b e3) {
                g.c();
                Object[] objArr5 = new Object[1];
                f((char) (30984 - TextUtils.getOffsetAfter("", 0)), TextUtils.indexOf("", "", 0), 15 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr5);
                String intern3 = ((String) objArr5[0]).intern();
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr6 = new Object[1];
                f((char) View.getDefaultSize(0, 0), (ViewConfiguration.getTouchSlop() >> 8) + 405, 35 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr6);
                g.e(intern3, sb2.append(((String) objArr6[0]).intern()).append(e3).toString());
            }
        }
    }

    public static void e(String str, String str2) {
        synchronized (c) {
            g.c();
            Object[] objArr = new Object[1];
            f((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 30983), ViewConfiguration.getDoubleTapTimeout() >> 16, '?' - AndroidCharacter.getMirror('0'), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            f((char) (Color.rgb(0, 0, 0) + 16801245), 440 - Color.blue(0), 55 - TextUtils.lastIndexOf("", '0', 0), objArr2);
            StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
            Object[] objArr3 = new Object[1];
            f((char) (51768 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), TextUtils.getOffsetBefore("", 0) + 496, (ViewConfiguration.getEdgeSlop() >> 16) + 16, objArr3);
            g.d(intern, append.append(((String) objArr3[0]).intern()).append(str2).toString());
            Iterator<o.az.c> it = a.iterator();
            while (it.hasNext()) {
                it.next().d(str, str2);
            }
        }
    }

    public static void d(Context context) {
        synchronized (c) {
            g.c();
            Object[] objArr = new Object[1];
            f((char) (View.MeasureSpec.getMode(0) + 30984), ViewConfiguration.getLongPressTimeout() >> 16, 15 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), ExpandableListView.getPackedPositionGroup(0L) + 512, Color.argb(0, 0, 0, 0) + 27, objArr2);
            g.d(intern, ((String) objArr2[0]).intern());
            for (d dVar : c.a(context)) {
                List<Long> list = d;
                if (list.contains(Long.valueOf(dVar.d()))) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    f((char) (30984 - (ViewConfiguration.getLongPressTimeout() >> 16)), ViewConfiguration.getScrollBarSize() >> 8, 15 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    f((char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 55817), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 540, Color.argb(0, 0, 0, 0) + 46, objArr4);
                    StringBuilder append = sb.append(((String) objArr4[0]).intern()).append(dVar.d());
                    Object[] objArr5 = new Object[1];
                    f((char) (ViewCompat.MEASURED_STATE_MASK - Color.rgb(0, 0, 0)), 585 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), View.MeasureSpec.getSize(0) + 19, objArr5);
                    g.d(intern2, append.append(((String) objArr5[0]).intern()).toString());
                } else {
                    g.c();
                    Object[] objArr6 = new Object[1];
                    f((char) (Color.blue(0) + 30984), ViewConfiguration.getLongPressTimeout() >> 16, 15 - View.resolveSize(0, 0), objArr6);
                    String intern3 = ((String) objArr6[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr7 = new Object[1];
                    f((char) (1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), 604 - (ViewConfiguration.getPressedStateDuration() >> 16), ExpandableListView.getPackedPositionGroup(0L) + 58, objArr7);
                    g.d(intern3, sb2.append(((String) objArr7[0]).intern()).append(dVar.d()).toString());
                    new o.dg.d();
                    o.dg.a e2 = o.dg.d.d(dVar.a()).e(context, dVar.g(), Long.valueOf(dVar.d()));
                    if (!e2.c()) {
                        c.d(context, Long.valueOf(dVar.d()));
                    } else {
                        list.add(Long.valueOf(dVar.d()));
                        e2.a().b(context);
                    }
                    if (e2.e() != null) {
                        o.db.b.a().e(context, e2.e(), true);
                    }
                }
            }
        }
    }

    public static void b(Context context) {
        synchronized (c) {
            d.clear();
            c.d(context);
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:14:0x00b2  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static o.dc.h d(o.eg.b r10) throws o.dc.b {
        /*
            Method dump skipped, instructions count: 270
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.a.d(o.eg.b):o.dc.h");
    }

    private static d d(Context context, o.eg.b bVar, h hVar) throws o.dc.b {
        d dVar;
        try {
            Object[] objArr = new Object[1];
            f((char) (View.combineMeasuredStates(0, 0) + 29649), 709 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), KeyEvent.keyCodeFromString("") + 2, objArr);
            switch (o.d(bVar, ((String) objArr[0]).intern()) ? 'W' : '_') {
                case Opcodes.POP /* 87 */:
                    int i = g + 75;
                    h = i % 128;
                    int i2 = i % 2;
                    Object[] objArr2 = new Object[1];
                    f((char) TextUtils.indexOf("", ""), (ViewConfiguration.getFadingEdgeLength() >> 16) + 710, '<' - AndroidCharacter.getMirror('0'), objArr2);
                    if (o.d(bVar, ((String) objArr2[0]).intern())) {
                        Object[] objArr3 = new Object[1];
                        f((char) (29648 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), KeyEvent.keyCodeFromString("") + 708, Color.rgb(0, 0, 0) + 16777218, objArr3);
                        long e2 = o.e(android.util.Base64.decode(bVar.r(((String) objArr3[0]).intern()).getBytes(j.c()), 8));
                        Object[] objArr4 = new Object[1];
                        f((char) (AndroidCharacter.getMirror('0') - '0'), (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 709, 12 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr4);
                        Date e3 = bVar.e(((String) objArr4[0]).intern(), false);
                        Object[] objArr5 = new Object[1];
                        f((char) ((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1))), 722 - Color.green(0), View.combineMeasuredStates(0, 0) + 10, objArr5);
                        Date b2 = bVar.b(((String) objArr5[0]).intern(), false);
                        Object[] objArr6 = new Object[1];
                        f((char) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 22659), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 731, 7 - Gravity.getAbsoluteGravity(0, 0), objArr6);
                        d dVar2 = new d(e2, hVar, e3, b2, bVar.u(((String) objArr6[0]).intern()));
                        g.c();
                        Object[] objArr7 = new Object[1];
                        f((char) (30984 - View.MeasureSpec.makeMeasureSpec(0, 0)), ViewConfiguration.getTouchSlop() >> 8, KeyEvent.normalizeMetaState(0) + 15, objArr7);
                        String intern = ((String) objArr7[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr8 = new Object[1];
                        f((char) (Color.green(0) + 5844), 739 - TextUtils.getOffsetBefore("", 0), 67 - TextUtils.getTrimmedLength(""), objArr8);
                        g.d(intern, sb.append(((String) objArr8[0]).intern()).append(dVar2).toString());
                        if (c.b(context, dVar2)) {
                            g.c();
                            Object[] objArr9 = new Object[1];
                            f((char) (30985 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1))), Process.myTid() >> 22, View.resolveSize(0, 0) + 15, objArr9);
                            String intern2 = ((String) objArr9[0]).intern();
                            StringBuilder sb2 = new StringBuilder();
                            Object[] objArr10 = new Object[1];
                            f((char) ExpandableListView.getPackedPositionGroup(0L), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 805, 66 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr10);
                            StringBuilder append = sb2.append(((String) objArr10[0]).intern()).append(dVar2.d());
                            Object[] objArr11 = new Object[1];
                            f((char) (View.MeasureSpec.getSize(0) + 27644), 871 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 33, objArr11);
                            g.e(intern2, append.append(((String) objArr11[0]).intern()).toString());
                            Object[] objArr12 = new Object[1];
                            f((char) (TextUtils.lastIndexOf("", '0', 0, 0) + 52330), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 904, TextUtils.indexOf((CharSequence) "", '0', 0) + 32, objArr12);
                            throw new o.dc.b(((String) objArr12[0]).intern());
                        }
                        switch (!c.a(context, dVar2) ? '+' : '#') {
                            case '+':
                                int i3 = g + 55;
                                h = i3 % 128;
                                int i4 = i3 % 2;
                                g.c();
                                Object[] objArr13 = new Object[1];
                                f((char) (30984 - ((Process.getThreadPriority(0) + 20) >> 6)), Process.myTid() >> 22, ImageFormat.getBitsPerPixel(0) + 16, objArr13);
                                String intern3 = ((String) objArr13[0]).intern();
                                Object[] objArr14 = new Object[1];
                                f((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), View.resolveSizeAndState(0, 0, 0) + 935, (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 66, objArr14);
                                g.e(intern3, ((String) objArr14[0]).intern());
                                dVar = null;
                                break;
                            default:
                                dVar = dVar2;
                                break;
                        }
                        return dVar;
                    }
                    break;
            }
            g.c();
            Object[] objArr15 = new Object[1];
            f((char) (30984 - ExpandableListView.getPackedPositionType(0L)), TextUtils.getTrimmedLength(""), 14 - Process.getGidForName(""), objArr15);
            String intern4 = ((String) objArr15[0]).intern();
            Object[] objArr16 = new Object[1];
            f((char) (44426 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), View.resolveSize(0, 0) + 1001, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 69, objArr16);
            g.d(intern4, ((String) objArr16[0]).intern());
            return null;
        } catch (o.eg.d e4) {
            throw new o.dc.b(e4.getMessage());
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dc\a$b.smali */
    static final class b {
        private o.b.c b;
        private static int e = 0;
        private static int d = 1;

        b() {
        }

        public final o.b.c e() {
            int i = e;
            int i2 = ((i | Opcodes.LSUB) << 1) - (i ^ Opcodes.LSUB);
            d = i2 % 128;
            switch (i2 % 2 == 0 ? 'U' : '9') {
                case Opcodes.CASTORE /* 85 */:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    o.b.c cVar = this.b;
                    int i3 = (i ^ 109) + ((i & 109) << 1);
                    d = i3 % 128;
                    int i4 = i3 % 2;
                    return cVar;
            }
        }

        public final void d(o.b.c cVar) {
            int i = d + 109;
            int i2 = i % 128;
            e = i2;
            int i3 = i % 2;
            this.b = cVar;
            int i4 = (i2 + 46) - 1;
            d = i4 % 128;
            switch (i4 % 2 == 0 ? '*' : 'W') {
                case '*':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return;
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 592
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dc.a.f(char, int, int, java.lang.Object[]):void");
    }
}
