package o.er;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import fr.antelop.sdk.digitalcard.CardPushUrl;
import fr.antelop.sdk.digitalcard.SecureCardPushToTokenRequestor;
import fr.antelop.sdk.digitalcard.TokenRequestor;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.lang.reflect.Method;
import java.util.List;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\t.smali */
public final class t {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int f;
    private static int h;
    private final o.du.i b;
    private final d[] c;
    private final String d;
    private final String e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        h = 1;
        a = 1514853755327847365L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(short r7, byte r8, int r9, java.lang.Object[] r10) {
        /*
            int r7 = r7 + 4
            byte[] r0 = o.er.t.$$a
            int r9 = r9 * 2
            int r9 = r9 + 112
            int r8 = r8 * 2
            int r8 = r8 + 1
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L18
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L33
        L18:
            r3 = r2
        L19:
            int r7 = r7 + 1
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r8) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            r3 = r0[r7]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L33:
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r8
            r8 = r6
            goto L19
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.t.i(short, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{73, -116, 106, -1};
        $$b = 220;
    }

    public t(String str, d[] dVarArr, String str2, o.du.i iVar) {
        this.d = str;
        this.c = dVarArr;
        this.e = str2;
        this.b = iVar;
    }

    public final String e() {
        int i = f + 27;
        h = i % 128;
        switch (i % 2 == 0 ? (char) 18 : '1') {
            case 18:
                throw null;
            default:
                return this.d;
        }
    }

    public final d[] c() {
        int i = f;
        int i2 = i + 43;
        h = i2 % 128;
        int i3 = i2 % 2;
        d[] dVarArr = this.c;
        int i4 = i + 47;
        h = i4 % 128;
        int i5 = i4 % 2;
        return dVarArr;
    }

    public final String b() {
        int i = h + 109;
        int i2 = i % 128;
        f = i2;
        int i3 = i % 2;
        String str = this.e;
        int i4 = i2 + 29;
        h = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final Drawable d(Context context) {
        int i = h;
        int i2 = i + 41;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? '\b' : '0') {
            case '\b':
                throw null;
            default:
                o.du.i iVar = this.b;
                switch (iVar != null ? '%' : 'O') {
                    case Opcodes.IASTORE /* 79 */:
                        int i3 = i + 27;
                        f = i3 % 128;
                        switch (i3 % 2 != 0 ? (char) 15 : Typography.greater) {
                            case '>':
                                return null;
                            default:
                                obj.hashCode();
                                throw null;
                        }
                    default:
                        return iVar.b(context);
                }
        }
    }

    public final o.du.i d() {
        int i = h;
        int i2 = i + 87;
        f = i2 % 128;
        int i3 = i2 % 2;
        o.du.i iVar = this.b;
        int i4 = i + 63;
        f = i4 % 128;
        switch (i4 % 2 != 0 ? '3' : (char) 22) {
            case '3':
                throw null;
            default:
                return iVar;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:18:0x003c  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x002c  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final java.lang.String a() {
        /*
            r3 = this;
            int r0 = o.er.t.h
            int r1 = r0 + 85
            int r2 = r1 % 128
            o.er.t.f = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L1f
            o.du.i r1 = r3.b
            r2 = 62
            int r2 = r2 / 0
            if (r1 == 0) goto L17
            r1 = 22
            goto L19
        L17:
            r1 = 29
        L19:
            switch(r1) {
                case 22: goto L2b;
                default: goto L1c;
            }
        L1c:
            goto L2c
        L1d:
            r0 = move-exception
            throw r0
        L1f:
            o.du.i r1 = r3.b
            if (r1 == 0) goto L26
            r1 = 19
            goto L28
        L26:
            r1 = 8
        L28:
            switch(r1) {
                case 8: goto L2c;
                default: goto L2b;
            }
        L2b:
            goto L3c
        L2c:
            int r0 = r0 + 55
            int r1 = r0 % 128
            o.er.t.f = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 != 0) goto L39
            return r1
        L39:
            throw r1     // Catch: java.lang.Throwable -> L3a
        L3a:
            r0 = move-exception
            throw r0
        L3c:
            o.du.i r0 = r3.b
            o.dx.d r0 = r0.e()
            java.lang.String r0 = r0.b()
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.er.t.a():java.lang.String");
    }

    public final SecureCardPushToTokenRequestor b(o.eo.e eVar) throws WalletValidationException {
        int i = f + 35;
        h = i % 128;
        int i2 = i % 2;
        switch (eVar.w().hasNext() ? '@' : ' ') {
            case ' ':
                WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                Object[] objArr = new Object[1];
                g("崁ോ\ufddc걣\u1cff", 20593 - View.combineMeasuredStates(0, 0), objArr);
                throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
            default:
                int i3 = h + 33;
                f = i3 % 128;
                if (i3 % 2 != 0) {
                }
                e eVar2 = (e) eVar.w().next().iterator().next();
                int i4 = f + 49;
                h = i4 % 128;
                int i5 = i4 % 2;
                i j = eVar2.j();
                return new SecureCardPushToTokenRequestor(new o.v.j(j.d(), eVar, j.b(), this.d));
        }
    }

    public final void e(Context context, String str, o.eo.e eVar, final OperationCallback<CardPushUrl> operationCallback) throws WalletValidationException {
        final SecureCardPushToTokenRequestor b = b(eVar);
        b.setReturnUrl(str);
        b.launch(context, new CustomCustomerAuthenticatedProcessCallback() { // from class: o.er.t.3
            private static int c = 0;
            private static int b = 1;

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i = b;
                int i2 = ((i | 93) << 1) - (i ^ 93);
                c = i2 % 128;
                switch (i2 % 2 != 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsInvalid(LocalAuthenticationErrorReason localAuthenticationErrorReason, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i = b;
                int i2 = (i & 17) + (i | 17);
                c = i2 % 128;
                switch (i2 % 2 == 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i = b;
                int i2 = (i ^ 3) + ((i & 3) << 1);
                c = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        break;
                    default:
                        int i3 = 73 / 0;
                        break;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsRequired(List<CustomerAuthenticationMethod> list, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i = b + 47;
                c = i % 128;
                switch (i % 2 != 0 ? ':' : '*') {
                    case Opcodes.ASTORE /* 58 */:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i = c;
                int i2 = (i & 81) + (i | 81);
                b = i2 % 128;
                switch (i2 % 2 == 0) {
                    case true:
                        operationCallback.onSuccess(b.getCardPushUrl());
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        operationCallback.onSuccess(b.getCardPushUrl());
                        return;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i = c;
                int i2 = (i ^ 109) + ((i & 109) << 1);
                b = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onError(antelopError);
                int i4 = b;
                int i5 = ((i4 | 69) << 1) - (i4 ^ 69);
                c = i5 % 128;
                int i6 = i5 % 2;
            }
        });
        int i = f + Opcodes.LSHL;
        h = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return;
        }
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\t$d.smali */
    public static final class d implements o.ee.d<TokenRequestor.Type>, o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static char[] a;
        public static final d b;
        public static final d c;
        private static final /* synthetic */ d[] e;
        private static int g;
        private static char i;
        private static int j;
        private final String d;

        static void d() {
            a = new char[]{30563, 30557, 30562, 30555, 30553, 30540, 30561, 30591, 30570, 30571, 30573, 30587, 30586, 30511, 30583, 30531, 30538, 30574, 30572, 30530, 30552, 30528, 30542, 30554, 30517};
            i = (char) 17040;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0032). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void h(short r6, byte r7, int r8, java.lang.Object[] r9) {
            /*
                byte[] r0 = o.er.t.d.$$a
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r6 = r6 + 4
                int r7 = r7 + 69
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L17
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L32
            L17:
                r3 = r2
            L18:
                byte r4 = (byte) r7
                r1[r3] = r4
                int r6 = r6 + 1
                int r4 = r3 + 1
                if (r3 != r8) goto L29
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L29:
                r3 = r0[r6]
                r5 = r8
                r8 = r7
                r7 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L32:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L18
            */
            throw new UnsupportedOperationException("Method not decompiled: o.er.t.d.h(short, byte, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{27, 43, 25, -109};
            $$b = Opcodes.INVOKEVIRTUAL;
        }

        private static /* synthetic */ d[] c() {
            d[] dVarArr;
            int i2 = j + 69;
            int i3 = i2 % 128;
            g = i3;
            switch (i2 % 2 == 0) {
                case false:
                    dVarArr = new d[]{b, c};
                    break;
                default:
                    dVarArr = new d[]{c, b};
                    break;
            }
            int i4 = i3 + 21;
            j = i4 % 128;
            switch (i4 % 2 != 0 ? 'N' : (char) 20) {
                case 20:
                    return dVarArr;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public static d valueOf(String str) {
            int i2 = g + 33;
            j = i2 % 128;
            char c2 = i2 % 2 != 0 ? '\n' : ',';
            d dVar = (d) Enum.valueOf(d.class, str);
            switch (c2) {
                case '\n':
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVar;
            }
        }

        public static d[] values() {
            int i2 = g + Opcodes.DSUB;
            j = i2 % 128;
            int i3 = i2 % 2;
            d[] dVarArr = (d[]) e.clone();
            int i4 = g + 63;
            j = i4 % 128;
            int i5 = i4 % 2;
            return dVarArr;
        }

        @Override // o.ee.d
        public final /* synthetic */ TokenRequestor.Type a() {
            int i2 = j + 93;
            g = i2 % 128;
            int i3 = i2 % 2;
            TokenRequestor.Type b2 = b();
            int i4 = j + 45;
            g = i4 % 128;
            int i5 = i4 % 2;
            return b2;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            j = 0;
            g = 1;
            d();
            Object[] objArr = new Object[1];
            f((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 5, "\u0015\u0017㘩㘩\u0012\u0001", (byte) (TextUtils.lastIndexOf("", '0') + 84), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 5, "\u0015\u0017㘩㘩\u0012\u0001", (byte) (83 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), objArr2);
            b = new d(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            f(9 - TextUtils.indexOf("", "", 0, 0), "\u000f\u0006\u0018\u0010\u000f\u0011\u0000\u0006㘈", (byte) (41 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            f(Color.argb(0, 0, 0, 0) + 9, "\u000f\u0006\u0018\u0010\u000f\u0011\u0000\u0006㘈", (byte) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 41), objArr4);
            c = new d(intern2, 1, ((String) objArr4[0]).intern());
            e = c();
            int i2 = g + 93;
            j = i2 % 128;
            switch (i2 % 2 != 0 ? ':' : 'Z') {
                case Opcodes.ASTORE /* 58 */:
                    throw null;
                default:
                    return;
            }
        }

        private d(String str, int i2, String str2) {
            this.d = str2;
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = g + 89;
            j = i2 % 128;
            switch (i2 % 2 != 0) {
                case false:
                    return this.d;
                default:
                    int i3 = 3 / 0;
                    return this.d;
            }
        }

        private TokenRequestor.Type b() {
            int i2 = j + Opcodes.LNEG;
            g = i2 % 128;
            Object obj = null;
            switch (i2 % 2 == 0 ? 'P' : '\r') {
                case '\r':
                    switch (AnonymousClass5.d[ordinal()]) {
                        case 1:
                            return TokenRequestor.Type.Wallet;
                        case 2:
                            TokenRequestor.Type type = TokenRequestor.Type.Ecommerce;
                            int i3 = g + 11;
                            j = i3 % 128;
                            switch (i3 % 2 != 0) {
                                case true:
                                    throw null;
                                default:
                                    return type;
                            }
                        default:
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr = new Object[1];
                            f((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 17, "\u0015\b\t\r\b\t\u0010\r\t\u0005\u000e\u0003\u000f\u0002\r\u0007\u0017\u000e", (byte) (ViewConfiguration.getTouchSlop() >> 8), objArr);
                            throw new UnsupportedOperationException(sb.append(((String) objArr[0]).intern()).append(name()).toString());
                    }
                default:
                    int i4 = AnonymousClass5.d[ordinal()];
                    obj.hashCode();
                    throw null;
            }
        }

        private static void f(int i2, String str, byte b2, Object[] objArr) {
            int i3;
            int length;
            char[] cArr;
            int i4;
            char[] charArray = str != null ? str.toCharArray() : str;
            o.a.m mVar = new o.a.m();
            char[] cArr2 = a;
            long j2 = 0;
            int i5 = -1;
            switch (cArr2 != null ? (char) 18 : (char) 21) {
                case 18:
                    int i6 = $10 + Opcodes.LSHL;
                    $11 = i6 % 128;
                    switch (i6 % 2 != 0) {
                        case true:
                            length = cArr2.length;
                            cArr = new char[length];
                            i4 = 0;
                            break;
                        default:
                            length = cArr2.length;
                            cArr = new char[length];
                            i4 = 1;
                            break;
                    }
                    while (i4 < length) {
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr2[i4])};
                            Object obj = o.e.a.s.get(-1401577988);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(16 - (ExpandableListView.getPackedPositionForChild(0, 0) > j2 ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == j2 ? 0 : -1)), (char) (ViewConfiguration.getLongPressTimeout() >> 16), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 75);
                                byte length2 = (byte) $$a.length;
                                Object[] objArr3 = new Object[1];
                                h((byte) i5, length2, (byte) (length2 - 4), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1401577988, obj);
                            }
                            cArr[i4] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            i4++;
                            j2 = 0;
                            i5 = -1;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    }
                    cArr2 = cArr;
                    break;
            }
            try {
                Object[] objArr4 = {Integer.valueOf(i)};
                Object obj2 = o.e.a.s.get(-1401577988);
                if (obj2 == null) {
                    Class cls2 = (Class) o.e.a.c(17 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) (ExpandableListView.getPackedPositionChild(0L) + 1), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 75);
                    byte length3 = (byte) $$a.length;
                    Object[] objArr5 = new Object[1];
                    h((byte) (-1), length3, (byte) (length3 - 4), objArr5);
                    obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                    o.e.a.s.put(-1401577988, obj2);
                }
                char charValue = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                char[] cArr3 = new char[i2];
                if (i2 % 2 != 0) {
                    i3 = i2 - 1;
                    cArr3[i3] = (char) (charArray[i3] - b2);
                } else {
                    i3 = i2;
                }
                switch (i3 > 1 ? '(' : '#') {
                    case '(':
                        mVar.b = 0;
                        while (mVar.b < i3) {
                            mVar.e = charArray[mVar.b];
                            mVar.a = charArray[mVar.b + 1];
                            if (mVar.e == mVar.a) {
                                cArr3[mVar.b] = (char) (mVar.e - b2);
                                cArr3[mVar.b + 1] = (char) (mVar.a - b2);
                            } else {
                                try {
                                    Object[] objArr6 = {mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), mVar};
                                    Object obj3 = o.e.a.s.get(696901393);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(View.MeasureSpec.getSize(0) + 10, (char) (8856 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), TextUtils.indexOf("", "", 0) + 324);
                                        byte b3 = (byte) (-1);
                                        byte b4 = (byte) (b3 + 1);
                                        Object[] objArr7 = new Object[1];
                                        h(b3, b4, b4, objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Object.class);
                                        o.e.a.s.put(696901393, obj3);
                                    }
                                    switch (((Integer) ((Method) obj3).invoke(null, objArr6)).intValue() != mVar.h) {
                                        case true:
                                            switch (mVar.c == mVar.d ? '\t' : '#') {
                                                case '#':
                                                    int i7 = (mVar.c * charValue) + mVar.h;
                                                    int i8 = (mVar.d * charValue) + mVar.i;
                                                    cArr3[mVar.b] = cArr2[i7];
                                                    cArr3[mVar.b + 1] = cArr2[i8];
                                                    break;
                                                default:
                                                    int i9 = $10 + Opcodes.LSUB;
                                                    $11 = i9 % 128;
                                                    if (i9 % 2 == 0) {
                                                    }
                                                    mVar.i = ((mVar.i + charValue) - 1) % charValue;
                                                    mVar.h = ((mVar.h + charValue) - 1) % charValue;
                                                    int i10 = (mVar.c * charValue) + mVar.i;
                                                    int i11 = (mVar.d * charValue) + mVar.h;
                                                    cArr3[mVar.b] = cArr2[i10];
                                                    cArr3[mVar.b + 1] = cArr2[i11];
                                                    break;
                                            }
                                        default:
                                            try {
                                                Object[] objArr8 = {mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, mVar, Integer.valueOf(charValue), Integer.valueOf(charValue), mVar, Integer.valueOf(charValue), mVar};
                                                Object obj4 = o.e.a.s.get(1075449051);
                                                if (obj4 == null) {
                                                    Class cls4 = (Class) o.e.a.c(View.combineMeasuredStates(0, 0) + 11, (char) TextUtils.indexOf("", ""), MotionEvent.axisFromString("") + 66);
                                                    byte b5 = (byte) (-1);
                                                    byte b6 = (byte) (-b5);
                                                    Object[] objArr9 = new Object[1];
                                                    h(b5, b6, (byte) (b6 - 1), objArr9);
                                                    obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Object.class, Integer.TYPE, Integer.TYPE, Object.class, Integer.TYPE, Object.class);
                                                    o.e.a.s.put(1075449051, obj4);
                                                }
                                                int intValue = ((Integer) ((Method) obj4).invoke(null, objArr8)).intValue();
                                                int i12 = (mVar.d * charValue) + mVar.h;
                                                cArr3[mVar.b] = cArr2[intValue];
                                                cArr3[mVar.b + 1] = cArr2[i12];
                                                break;
                                            } catch (Throwable th2) {
                                                Throwable cause2 = th2.getCause();
                                                if (cause2 == null) {
                                                    throw th2;
                                                }
                                                throw cause2;
                                            }
                                    }
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                            }
                            mVar.b += 2;
                        }
                        break;
                }
                int i13 = 0;
                while (i13 < i2) {
                    cArr3[i13] = (char) (cArr3[i13] ^ 13722);
                    i13++;
                    int i14 = $11 + 87;
                    $10 = i14 % 128;
                    int i15 = i14 % 2;
                }
                objArr[0] = new String(cArr3);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
    }

    /* renamed from: o.er.t$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\er\t$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        private static int b;
        static final /* synthetic */ int[] d;
        private static int e;

        static {
            e = 0;
            b = 1;
            int[] iArr = new int[d.values().length];
            d = iArr;
            try {
                iArr[d.b.ordinal()] = 1;
                int i = b + 59;
                e = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[d.c.ordinal()] = 2;
                int i3 = e + 75;
                b = i3 % 128;
                switch (i3 % 2 == 0 ? ')' : 'E') {
                    case ')':
                        int i4 = 54 / 0;
                        return;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e3) {
            }
        }
    }

    private static void g(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2 = $11 + Opcodes.LREM;
        $10 = i2 % 128;
        switch (i2 % 2 != 0 ? '=' : 'T') {
            case Opcodes.BASTORE /* 84 */:
                switch (str != null) {
                    case true:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str;
                        break;
                }
                char[] cArr2 = cArr;
                o.a.k kVar = new o.a.k();
                kVar.a = i;
                int length = cArr2.length;
                long[] jArr = new long[length];
                kVar.b = 0;
                while (true) {
                    long j = 0;
                    if (kVar.b >= cArr2.length) {
                        char[] cArr3 = new char[length];
                        kVar.b = 0;
                        while (kVar.b < cArr2.length) {
                            cArr3[kVar.b] = (char) jArr[kVar.b];
                            try {
                                Object[] objArr2 = {kVar, kVar};
                                Object obj = o.e.a.s.get(-10300751);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(12 - KeyEvent.keyCodeFromString(""), (char) (55184 - ExpandableListView.getPackedPositionChild(j)), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 538);
                                    byte b = $$a[3];
                                    byte b2 = (byte) (b + 1);
                                    Object[] objArr3 = new Object[1];
                                    i(b, b2, b2, objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Object.class, Object.class);
                                    o.e.a.s.put(-10300751, obj);
                                }
                                ((Method) obj).invoke(null, objArr2);
                                j = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        objArr[0] = new String(cArr3);
                        return;
                    }
                    int i3 = $10 + 95;
                    $11 = i3 % 128;
                    if (i3 % 2 == 0) {
                        int i4 = kVar.b;
                        try {
                            Object[] objArr4 = {Integer.valueOf(cArr2[kVar.b]), kVar, kVar};
                            Object obj2 = o.e.a.s.get(806930129);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(9 - ExpandableListView.getPackedPositionChild(0L), (char) (MotionEvent.axisFromString("") + 60578), 355 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)));
                                byte b3 = $$a[3];
                                byte b4 = b3;
                                Object[] objArr5 = new Object[1];
                                i(b4, (byte) (b4 + 1), (byte) (-b3), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Object.class, Object.class);
                                o.e.a.s.put(806930129, obj2);
                            }
                            jArr[i4] = ((Long) ((Method) obj2).invoke(null, objArr4)).longValue() * (a - (-5249873463433509232L));
                            try {
                                Object[] objArr6 = {kVar, kVar};
                                Object obj3 = o.e.a.s.get(-10300751);
                                if (obj3 == null) {
                                    Class cls3 = (Class) o.e.a.c(12 - (ViewConfiguration.getPressedStateDuration() >> 16), (char) (TextUtils.getTrimmedLength("") + 55185), 538 - (ViewConfiguration.getKeyRepeatDelay() >> 16));
                                    byte b5 = $$a[3];
                                    byte b6 = (byte) (b5 + 1);
                                    Object[] objArr7 = new Object[1];
                                    i(b5, b6, b6, objArr7);
                                    obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                    o.e.a.s.put(-10300751, obj3);
                                }
                                ((Method) obj3).invoke(null, objArr6);
                            } catch (Throwable th2) {
                                Throwable cause2 = th2.getCause();
                                if (cause2 == null) {
                                    throw th2;
                                }
                                throw cause2;
                            }
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    } else {
                        int i5 = kVar.b;
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr2[kVar.b]), kVar, kVar};
                            Object obj4 = o.e.a.s.get(806930129);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(10 - View.MeasureSpec.getSize(0), (char) (TextUtils.lastIndexOf("", '0', 0) + 60578), TextUtils.getCapsMode("", 0, 0) + 354);
                                byte b7 = $$a[3];
                                byte b8 = b7;
                                Object[] objArr9 = new Object[1];
                                i(b8, (byte) (b8 + 1), (byte) (-b7), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Object.class, Object.class);
                                o.e.a.s.put(806930129, obj4);
                            }
                            jArr[i5] = ((Long) ((Method) obj4).invoke(null, objArr8)).longValue() ^ (a ^ (-5249873463433509232L));
                            try {
                                Object[] objArr10 = {kVar, kVar};
                                Object obj5 = o.e.a.s.get(-10300751);
                                if (obj5 == null) {
                                    Class cls5 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 13, (char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 55185), (ViewConfiguration.getTapTimeout() >> 16) + 538);
                                    byte b9 = $$a[3];
                                    byte b10 = (byte) (b9 + 1);
                                    Object[] objArr11 = new Object[1];
                                    i(b9, b10, b10, objArr11);
                                    obj5 = cls5.getMethod((String) objArr11[0], Object.class, Object.class);
                                    o.e.a.s.put(-10300751, obj5);
                                }
                                ((Method) obj5).invoke(null, objArr10);
                            } catch (Throwable th4) {
                                Throwable cause4 = th4.getCause();
                                if (cause4 == null) {
                                    throw th4;
                                }
                                throw cause4;
                            }
                        } catch (Throwable th5) {
                            Throwable cause5 = th5.getCause();
                            if (cause5 == null) {
                                throw th5;
                            }
                            throw cause5;
                        }
                    }
                }
            default:
                Object obj6 = null;
                obj6.hashCode();
                throw null;
        }
    }
}
