package fr.antelop.sdk.digitalcard.transactioncontrol;

import android.content.Context;
import fr.antelop.sdk.digitalcard.DigitalCardServiceStatus;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import o.er.p;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControlService.smali */
public final class TransactionControlService {
    private final p innerTransactionControlService;

    public TransactionControlService(p pVar) {
        this.innerTransactionControlService = pVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerTransactionControlService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final void getTransactionControls(Context context, OperationCallback<TransactionControls> operationCallback) throws WalletValidationException {
        this.innerTransactionControlService.a(context, operationCallback);
    }
}
