package bc.org.bouncycastle.crypto;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.g6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.l7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.o5;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\a.smali */
public class a extends BufferedBlockCipher {
    protected byte[] h;
    protected int i;
    protected boolean j;
    protected BlockCipher k;
    protected o5 l;
    protected boolean m;
    protected boolean n;

    protected a() {
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int doFinal(byte[] bArr, int i) throws DataLengthException, IllegalStateException, InvalidCipherTextException {
        try {
            int i2 = this.i;
            if (i + i2 > bArr.length) {
                throw new g6("output buffer too short for doFinal()");
            }
            int i3 = 0;
            if (i2 != 0) {
                if (!this.m) {
                    throw new DataLengthException("data not block size aligned");
                }
                BlockCipher blockCipher = this.k;
                byte[] bArr2 = this.h;
                blockCipher.processBlock(bArr2, 0, bArr2, 0);
                int i4 = this.i;
                this.i = 0;
                System.arraycopy(this.h, 0, bArr, i, i4);
                i3 = i4;
            }
            return i3;
        } finally {
            reset();
        }
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int getBlockSize() {
        return this.k.getBlockSize();
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int getOutputSize(int i) {
        return (this.n && this.j) ? i + this.i + this.k.getBlockSize() + 2 : i + this.i;
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public BlockCipher getUnderlyingCipher() {
        return this.k;
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int getUpdateOutputSize(int i) {
        int i2 = i + this.i;
        return i2 - (this.n ? this.j ? (i2 % this.h.length) - (this.k.getBlockSize() + 2) : i2 % this.h.length : i2 % this.h.length);
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public void init(boolean z, CipherParameters cipherParameters) throws IllegalArgumentException {
        this.j = z;
        reset();
        this.k.init(z, cipherParameters);
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int processByte(byte b, byte[] bArr, int i) throws DataLengthException, IllegalStateException {
        byte[] bArr2 = this.h;
        int i2 = this.i;
        int i3 = i2 + 1;
        this.i = i3;
        bArr2[i2] = b;
        if (i3 != bArr2.length) {
            return 0;
        }
        int processBlock = this.k.processBlock(bArr2, 0, bArr, i);
        this.i = 0;
        return processBlock;
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public int processBytes(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException, IllegalStateException {
        int i4;
        int i5;
        int i6;
        if (i2 < 0) {
            throw new IllegalArgumentException("Can't have a negative input length!");
        }
        int blockSize = getBlockSize();
        int updateOutputSize = getUpdateOutputSize(i2);
        if (updateOutputSize > 0 && updateOutputSize + i3 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        byte[] bArr3 = this.h;
        int length = bArr3.length;
        int i7 = this.i;
        int i8 = length - i7;
        if (i2 > i8) {
            System.arraycopy(bArr, i, bArr3, i7, i8);
            i6 = this.k.processBlock(this.h, 0, bArr2, i3) + 0;
            this.i = 0;
            i5 = i2 - i8;
            i4 = i + i8;
            o5 o5Var = this.l;
            if (o5Var != null) {
                int multiBlockSize = i5 / o5Var.getMultiBlockSize();
                if (multiBlockSize > 0) {
                    i6 += this.l.processBlocks(bArr, i4, multiBlockSize, bArr2, i3 + i6);
                    int multiBlockSize2 = multiBlockSize * this.l.getMultiBlockSize();
                    i5 -= multiBlockSize2;
                    i4 += multiBlockSize2;
                }
            } else {
                while (i5 > this.h.length) {
                    i6 += this.k.processBlock(bArr, i4, bArr2, i3 + i6);
                    i5 -= blockSize;
                    i4 += blockSize;
                }
            }
        } else {
            i4 = i;
            i5 = i2;
            i6 = 0;
        }
        System.arraycopy(bArr, i4, this.h, this.i, i5);
        int i9 = this.i + i5;
        this.i = i9;
        byte[] bArr4 = this.h;
        if (i9 != bArr4.length) {
            return i6;
        }
        int processBlock = i6 + this.k.processBlock(bArr4, 0, bArr2, i3 + i6);
        this.i = 0;
        return processBlock;
    }

    @Override // bc.org.bouncycastle.crypto.BufferedBlockCipher
    public void reset() {
        int i = 0;
        while (true) {
            byte[] bArr = this.h;
            if (i >= bArr.length) {
                this.i = 0;
                this.k.reset();
                return;
            } else {
                bArr[i] = 0;
                i++;
            }
        }
    }

    public a(BlockCipher blockCipher) {
        this.k = blockCipher;
        if (blockCipher instanceof o5) {
            o5 o5Var = (o5) blockCipher;
            this.l = o5Var;
            this.h = new byte[o5Var.getMultiBlockSize()];
        } else {
            this.l = null;
            this.h = new byte[blockCipher.getBlockSize()];
        }
        boolean z = false;
        this.i = 0;
        String algorithmName = blockCipher.getAlgorithmName();
        int indexOf = algorithmName.indexOf(47) + 1;
        boolean z2 = indexOf > 0 && algorithmName.startsWith("PGP", indexOf);
        this.n = z2;
        if (z2 || (blockCipher instanceof l7)) {
            this.m = true;
            return;
        }
        if (indexOf > 0 && algorithmName.startsWith("OpenPGP", indexOf)) {
            z = true;
        }
        this.m = z;
    }
}
