package o.ee;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ColorSpace;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.Base64;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.text.Normalizer;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\o.smali */
public final class o {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char a;
    private static char b;
    private static int c;
    private static char d;
    private static char e;
    private static int h;
    private static int j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        j = 0;
        h = 1;
        b();
        View.resolveSizeAndState(0, 0, 0);
        int i = j + 75;
        h = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void b() {
        c = 874635483;
        d = (char) 25960;
        b = (char) 12654;
        a = (char) 55370;
        e = (char) 31713;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002d  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0025  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002d -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(int r6, byte r7, int r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 4
            int r6 = r6 * 2
            int r6 = r6 + 107
            int r8 = r8 * 3
            int r8 = 1 - r8
            byte[] r0 = o.ee.o.$$a
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L38
        L1a:
            r3 = r2
        L1b:
            r5 = r7
            r7 = r6
            r6 = r5
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L2d
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2d:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r7 = r7 + 1
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.i(int, byte, int, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{88, 54, 68, 27};
        $$b = Opcodes.IFGE;
    }

    public static String a(Context context, String str) throws PackageManager.NameNotFoundException, IllegalArgumentException {
        int i = j + 25;
        h = i % 128;
        int i2 = i % 2;
        if (context == null) {
            throw new IllegalArgumentException();
        }
        e.a();
        String c2 = c.c(context, str);
        if (c2 == null) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            f(6 - (ViewConfiguration.getEdgeSlop() >> 16), "\f\u001a\u001bￇ￡ￇ\ufff4\u0010\u001a\u001a\u0010\u0015\u000eￇ\ufff4\f\u001b\b￫\b\u001b\bￇ\u0010\u0015ￇ\u001b\u000f\fￇ\u0014\b\u0015\u0010\r", (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 35, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 252, false, objArr);
            throw new PackageManager.NameNotFoundException(sb.append(((String) objArr[0]).intern()).append(str).toString());
        }
        g.c();
        Object[] objArr2 = new Object[1];
        g("㜺ᡩ㠏涸芓\uf29e", KeyEvent.normalizeMetaState(0) + 5, objArr2);
        String intern = ((String) objArr2[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr3 = new Object[1];
        g("邹ჸ맴퀨쭞䰑影䉿Δ\ue032ᨼ騒设⊟櫊펒ﷁ퇬鰶\uf482忕솲龠ꎚ濫㍔⋦絋㎕䜢ቱ✸鎵癃噩괝튦蒸\ude58疑튦蒸隈ᓃꭒ퀋ኹ⹂", MotionEvent.axisFromString("") + 49, objArr3);
        g.d(intern, sb2.append(((String) objArr3[0]).intern()).append(c2).toString());
        int i3 = j + 29;
        h = i3 % 128;
        int i4 = i3 % 2;
        return c2;
    }

    public static boolean d(Context context) {
        Object[] objArr = new Object[1];
        g("㦾⩕㲖鱶ἲ喍촓榬ꢷ꿯莣ꭸ", 13 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
        NetworkInfo activeNetworkInfo = ((ConnectivityManager) context.getSystemService(((String) objArr[0]).intern())).getActiveNetworkInfo();
        switch (activeNetworkInfo == null) {
            default:
                int i = h + 19;
                j = i % 128;
                int i2 = i % 2;
                switch (activeNetworkInfo.isConnected()) {
                    case true:
                        int i3 = j + 77;
                        int i4 = i3 % 128;
                        h = i4;
                        int i5 = i3 % 2;
                        int i6 = i4 + 29;
                        j = i6 % 128;
                        int i7 = i6 % 2;
                        return true;
                }
            case true:
                return false;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0050  */
    /* JADX WARN: Removed duplicated region for block: B:16:0x0051 A[Catch: Exception -> 0x003f, PHI: r3
  0x0051: PHI (r3v15 java.lang.Class<?>) = (r3v14 java.lang.Class<?>), (r3v23 java.lang.Class<?>) binds: [B:26:0x0038, B:11:0x004d] A[DONT_GENERATE, DONT_INLINE], TRY_LEAVE, TryCatch #0 {Exception -> 0x003f, blocks: (B:7:0x0021, B:8:0x0042, B:11:0x004d, B:13:0x0060, B:14:0x00d1, B:16:0x0051, B:20:0x0026, B:23:0x0030), top: B:5:0x001e }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static <T> T e(java.lang.Class<T> r17, java.lang.String r18, java.lang.String r19) {
        /*
            Method dump skipped, instructions count: 372
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.e(java.lang.Class, java.lang.String, java.lang.String):java.lang.Object");
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\o$a.smali */
    public static final class a {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static int b;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            e = 0;
            a = 1;
            b = 874635278;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x002e). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void g(short r7, int r8, int r9, java.lang.Object[] r10) {
            /*
                byte[] r0 = o.ee.o.a.$$a
                int r8 = r8 * 4
                int r8 = r8 + 4
                int r9 = r9 * 2
                int r9 = 109 - r9
                int r7 = r7 * 4
                int r7 = 1 - r7
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L18
                r3 = r9
                r5 = r2
                r9 = r8
                r8 = r7
                goto L2e
            L18:
                r3 = r2
            L19:
                byte r4 = (byte) r9
                int r5 = r3 + 1
                r1[r3] = r4
                if (r5 != r7) goto L28
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L28:
                r3 = r0[r8]
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L2e:
                int r3 = -r3
                int r9 = r9 + 1
                int r7 = r7 + r3
                r3 = r5
                r6 = r9
                r9 = r7
                r7 = r8
                r8 = r6
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ee.o.a.g(short, int, int, java.lang.Object[]):void");
        }

        static void init$0() {
            $$a = new byte[]{76, -124, -102, 114};
            $$b = Opcodes.IREM;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        @java.lang.SafeVarargs
        public static <T> boolean c(T r6, T... r7) {
            /*
                int r0 = r7.length
                r1 = 0
                r2 = r1
            L4:
                r3 = 1
                if (r2 >= r0) goto L9
                r4 = r1
                goto La
            L9:
                r4 = r3
            La:
                switch(r4) {
                    case 0: goto Le;
                    default: goto Ld;
                }
            Ld:
                goto L38
            Le:
                int r4 = o.ee.o.a.e
                int r4 = r4 + 87
                int r5 = r4 % 128
                o.ee.o.a.a = r5
                int r4 = r4 % 2
                if (r4 != 0) goto L1d
                r4 = 59
                goto L1f
            L1d:
                r4 = 40
            L1f:
                switch(r4) {
                    case 59: goto L2b;
                    default: goto L22;
                }
            L22:
                r4 = r7[r2]
                boolean r4 = r4.equals(r6)
                if (r4 == 0) goto L35
                goto L34
            L2b:
                r7 = r7[r2]
                r7.equals(r6)
                r6 = 0
                throw r6     // Catch: java.lang.Throwable -> L32
            L32:
                r6 = move-exception
                throw r6
            L34:
                return r3
            L35:
                int r2 = r2 + 1
                goto L4
            L38:
                int r6 = o.ee.o.a.a
                int r6 = r6 + 55
                int r7 = r6 % 128
                o.ee.o.a.e = r7
                int r6 = r6 % 2
                return r1
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ee.o.a.c(java.lang.Object, java.lang.Object[]):boolean");
        }

        public static boolean b(int i, int... iArr) {
            boolean z;
            int length = iArr.length;
            int i2 = 0;
            while (i2 < 5) {
                switch (iArr[i2] == i ? 'J' : 'Y') {
                    case Opcodes.DUP /* 89 */:
                        i2++;
                        int i3 = e + 33;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                    default:
                        int i5 = a + Opcodes.LSHL;
                        int i6 = i5 % 128;
                        e = i6;
                        switch (i5 % 2 != 0) {
                            case false:
                                z = true;
                                break;
                            default:
                                z = false;
                                break;
                        }
                        int i7 = i6 + 69;
                        a = i7 % 128;
                        switch (i7 % 2 != 0) {
                            case true:
                                return z;
                            default:
                                throw null;
                        }
                }
            }
            return false;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        public static boolean d(java.lang.String r5, java.lang.String... r6) {
            /*
                int r0 = o.ee.o.a.e
                int r0 = r0 + 115
                int r1 = r0 % 128
                o.ee.o.a.a = r1
                int r0 = r0 % 2
                int r0 = r6.length
                r1 = 0
                r2 = r1
            Le:
                if (r2 >= r0) goto L13
                r3 = 24
                goto L15
            L13:
                r3 = 61
            L15:
                switch(r3) {
                    case 61: goto L25;
                    default: goto L18;
                }
            L18:
                int r3 = o.ee.o.a.e
                int r3 = r3 + 77
                int r4 = r3 % 128
                o.ee.o.a.a = r4
                int r3 = r3 % 2
                if (r3 != 0) goto L3b
                goto L26
            L25:
                return r1
            L26:
                r3 = r6[r2]
                boolean r3 = r3.equalsIgnoreCase(r5)
                r4 = 7
                int r4 = r4 / r1
                if (r3 == 0) goto L33
                r3 = 15
                goto L35
            L33:
                r3 = 32
            L35:
                switch(r3) {
                    case 15: goto L43;
                    default: goto L38;
                }
            L38:
                goto L45
            L39:
                r5 = move-exception
                throw r5
            L3b:
                r3 = r6[r2]
                boolean r3 = r3.equalsIgnoreCase(r5)
                if (r3 == 0) goto L45
            L43:
                r5 = 1
                return r5
            L45:
                int r2 = r2 + 1
                goto Le
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ee.o.a.d(java.lang.String, java.lang.String[]):boolean");
        }

        public static boolean a(String str, int i, int i2) {
            int i3 = e + Opcodes.LSHR;
            a = i3 % 128;
            char c = i3 % 2 == 0 ? (char) 17 : 'I';
            boolean a2 = a(str.length(), i, i2);
            switch (c) {
                case 17:
                    int i4 = 10 / 0;
                default:
                    return a2;
            }
        }

        public static boolean b(byte[] bArr, int i, int i2) {
            int i3 = e + 31;
            a = i3 % 128;
            int i4 = i3 % 2;
            boolean a2 = a(bArr.length, 0, i2);
            int i5 = a + 71;
            e = i5 % 128;
            switch (i5 % 2 != 0) {
                case true:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return a2;
            }
        }

        /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0025. Please report as an issue. */
        public static boolean a(int i, int i2, int i3) {
            switch (i >= i2 ? (char) 29 : (char) 17) {
                default:
                    int i4 = a;
                    int i5 = i4 + 47;
                    e = i5 % 128;
                    int i6 = i5 % 2;
                    if (i <= i3) {
                        int i7 = i4 + 9;
                        int i8 = i7 % 128;
                        e = i8;
                        switch (i7 % 2 == 0) {
                        }
                        int i9 = i8 + 57;
                        a = i9 % 128;
                        if (i9 % 2 != 0) {
                            return true;
                        }
                        int i10 = 63 / 0;
                        return true;
                    }
                case 17:
                    return false;
            }
        }

        public static boolean d(String str) {
            StringBuilder sb = new StringBuilder();
            Object[] objArr = new Object[1];
            f(4 - (ViewConfiguration.getEdgeSlop() >> 16), "￭￡￤\u000f/\u0011", 5 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 189 - Color.green(0), true, objArr);
            StringBuilder append = sb.append(((String) objArr[0]).intern()).append(4);
            Object[] objArr2 = new Object[1];
            f(KeyEvent.getDeadChar(0, 0) + 1, "\u0000", 1 - View.resolveSize(0, 0), 238 - (ViewConfiguration.getScrollBarSize() >> 8), true, objArr2);
            if (!str.matches(append.append(((String) objArr2[0]).intern()).toString())) {
                int i = a + 83;
                e = i % 128;
                int i2 = i % 2;
                return false;
            }
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr3 = new Object[1];
            f(KeyEvent.keyCodeFromString("") + 2, "\u0001\uffff", (ViewConfiguration.getKeyRepeatDelay() >> 16) + 2, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + Opcodes.IF_ICMPGE, false, objArr3);
            int intValue = Integer.valueOf(sb2.append(((String) objArr3[0]).intern()).append(str.substring(0, 2)).toString()).intValue();
            int intValue2 = Integer.valueOf(str.substring(2, 4)).intValue();
            switch (intValue2 > 0) {
                case true:
                    int i3 = e + 57;
                    a = i3 % 128;
                    int i4 = i3 % 2;
                    if (12 >= intValue2) {
                        Calendar calendar = Calendar.getInstance();
                        int i5 = calendar.get(1);
                        int i6 = calendar.get(2) + 1;
                        switch (intValue < i5 ? Typography.amp : '\'') {
                            case '\'':
                                switch (intValue != i5 ? (char) 19 : (char) 5) {
                                    default:
                                        if (intValue2 < i6) {
                                        }
                                        break;
                                    case 19:
                                        return true;
                                }
                            default:
                                int i7 = a + 93;
                                e = i7 % 128;
                                int i8 = i7 % 2;
                                break;
                        }
                        return false;
                    }
                    break;
                default:
                    return false;
            }
        }

        private static void f(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
            char[] cArr;
            int i4 = $10 + 97;
            $11 = i4 % 128;
            switch (i4 % 2 != 0) {
                case true:
                    switch (str != null ? 'Y' : (char) 28) {
                        case Opcodes.DUP /* 89 */:
                            cArr = str.toCharArray();
                            break;
                        default:
                            cArr = str;
                            break;
                    }
                    char[] cArr2 = cArr;
                    o.a.h hVar = new o.a.h();
                    char[] cArr3 = new char[i2];
                    hVar.a = 0;
                    while (hVar.a < i2) {
                        hVar.b = cArr2[hVar.a];
                        cArr3[hVar.a] = (char) (i3 + hVar.b);
                        int i5 = hVar.a;
                        try {
                            Object[] objArr2 = {Integer.valueOf(cArr3[i5]), Integer.valueOf(b)};
                            Object obj = o.e.a.s.get(2038615114);
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 12, (char) (Process.myPid() >> 22), 459 - View.resolveSize(0, 0));
                                byte b2 = (byte) 0;
                                byte b3 = b2;
                                Object[] objArr3 = new Object[1];
                                g(b2, b3, (byte) (b3 + 1), objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(2038615114, obj);
                            }
                            cArr3[i5] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                            try {
                                Object[] objArr4 = {hVar, hVar};
                                Object obj2 = o.e.a.s.get(-1412673904);
                                if (obj2 == null) {
                                    Class cls2 = (Class) o.e.a.c(10 - TextUtils.indexOf((CharSequence) "", '0'), (char) (ViewConfiguration.getWindowTouchSlop() >> 8), AndroidCharacter.getMirror('0') + 265);
                                    byte b4 = (byte) 0;
                                    byte b5 = b4;
                                    Object[] objArr5 = new Object[1];
                                    g(b4, b5, b5, objArr5);
                                    obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj2);
                                }
                                ((Method) obj2).invoke(null, objArr4);
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        } catch (Throwable th2) {
                            Throwable cause2 = th2.getCause();
                            if (cause2 == null) {
                                throw th2;
                            }
                            throw cause2;
                        }
                    }
                    if (i > 0) {
                        hVar.c = i;
                        char[] cArr4 = new char[i2];
                        System.arraycopy(cArr3, 0, cArr4, 0, i2);
                        System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                        System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
                    }
                    if (z) {
                        char[] cArr5 = new char[i2];
                        hVar.a = 0;
                        while (true) {
                            switch (hVar.a < i2 ? '+' : ';') {
                                case ';':
                                    int i6 = $10 + 33;
                                    $11 = i6 % 128;
                                    int i7 = i6 % 2;
                                    cArr3 = cArr5;
                                    break;
                                default:
                                    cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                                    try {
                                        Object[] objArr6 = {hVar, hVar};
                                        Object obj3 = o.e.a.s.get(-1412673904);
                                        if (obj3 == null) {
                                            Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getLongPressTimeout() >> 16), (char) TextUtils.getCapsMode("", 0, 0), 312 - TextUtils.indexOf((CharSequence) "", '0', 0));
                                            byte b6 = (byte) 0;
                                            byte b7 = b6;
                                            Object[] objArr7 = new Object[1];
                                            g(b6, b7, b7, objArr7);
                                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                            o.e.a.s.put(-1412673904, obj3);
                                        }
                                        ((Method) obj3).invoke(null, objArr6);
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                            }
                        }
                    }
                    objArr[0] = new String(cArr3);
                    return;
                default:
                    throw null;
            }
        }
    }

    public static String e(CharSequence charSequence) {
        int i = j + 95;
        int i2 = i % 128;
        h = i2;
        int i3 = i % 2;
        switch (charSequence == null ? 'U' : '*') {
            case '*':
                return Normalizer.normalize(charSequence, Normalizer.Form.NFKC);
            default:
                int i4 = i2 + 85;
                j = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    public static Date a(long j2) {
        if (j2 != -1) {
            Date date = new Date(j2);
            int i = j + 97;
            h = i % 128;
            int i2 = i % 2;
            return date;
        }
        int i3 = j;
        int i4 = i3 + 69;
        h = i4 % 128;
        Object obj = null;
        switch (i4 % 2 == 0 ? 'C' : '2') {
            case 'C':
                throw null;
            default:
                int i5 = i3 + 91;
                h = i5 % 128;
                switch (i5 % 2 == 0 ? (char) 17 : (char) 6) {
                    case 6:
                        return null;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static <T> java.lang.String d(java.lang.String r6, T[] r7) {
        /*
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            int r1 = r7.length
            r2 = 0
            r3 = r2
        La:
            if (r3 >= r1) goto Lf
            r4 = 75
            goto L11
        Lf:
            r4 = 81
        L11:
            switch(r4) {
                case 81: goto L21;
                default: goto L14;
            }
        L14:
            int r4 = o.ee.o.j
            int r4 = r4 + 19
            int r5 = r4 % 128
            o.ee.o.h = r5
            int r4 = r4 % 2
            if (r4 != 0) goto L3a
            goto L26
        L21:
            java.lang.String r6 = r0.toString()
            return r6
        L26:
            r4 = r7[r3]
            java.lang.String r4 = r4.toString()
            r0.append(r4)
            int r4 = r3 << 1
            if (r4 >= r1) goto L35
            r4 = 1
            goto L36
        L35:
            r4 = r2
        L36:
            switch(r4) {
                case 0: goto L5c;
                default: goto L39;
            }
        L39:
            goto L4f
        L3a:
            r4 = r7[r3]
            java.lang.String r4 = r4.toString()
            r0.append(r4)
            int r4 = r3 + 1
            if (r4 >= r1) goto L4a
            r4 = 86
            goto L4c
        L4a:
            r4 = 15
        L4c:
            switch(r4) {
                case 15: goto L5c;
                default: goto L4f;
            }
        L4f:
            r0.append(r6)
            int r4 = o.ee.o.j
            int r4 = r4 + 117
            int r5 = r4 % 128
            o.ee.o.h = r5
            int r4 = r4 % 2
        L5c:
            int r3 = r3 + 1
            goto La
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.d(java.lang.String, java.lang.Object[]):java.lang.String");
    }

    /* JADX WARN: Removed duplicated region for block: B:15:0x0054  */
    /* JADX WARN: Removed duplicated region for block: B:17:0x005e  */
    /* JADX WARN: Removed duplicated region for block: B:20:0x005a A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static <T> java.lang.String d(java.lang.String r4, java.util.Iterator<T> r5) {
        /*
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
        L6:
            boolean r1 = r5.hasNext()
            if (r1 == 0) goto L6b
            int r1 = o.ee.o.j
            int r1 = r1 + 85
            int r2 = r1 % 128
            o.ee.o.h = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L1b
            r1 = 56
            goto L1d
        L1b:
            r1 = 25
        L1d:
            r2 = 0
            switch(r1) {
                case 25: goto L31;
                default: goto L21;
            }
        L21:
            java.lang.Object r1 = r5.next()
            java.lang.String r1 = r1.toString()
            r0.append(r1)
            boolean r1 = r5.hasNext()
            goto L43
        L31:
            java.lang.Object r1 = r5.next()
            java.lang.String r1 = r1.toString()
            r0.append(r1)
            boolean r1 = r5.hasNext()
            if (r1 == 0) goto L6
        L42:
            goto L48
        L43:
            r3 = 1
            int r3 = r3 / r2
            if (r1 == 0) goto L6
            goto L42
        L48:
            int r1 = o.ee.o.j
            int r1 = r1 + 125
            int r3 = r1 % 128
            o.ee.o.h = r3
            int r1 = r1 % 2
            if (r1 != 0) goto L56
            r2 = 1
            goto L57
        L56:
        L57:
            switch(r2) {
                case 0: goto L5e;
                default: goto L5a;
            }
        L5a:
            r0.append(r4)
            goto L62
        L5e:
            r0.append(r4)
            goto L6
        L62:
            r4 = 0
            r4.hashCode()     // Catch: java.lang.Throwable -> L67
            throw r4     // Catch: java.lang.Throwable -> L67
        L67:
            r4 = move-exception
            throw r4
        L69:
            r4 = move-exception
            throw r4
        L6b:
            java.lang.String r4 = r0.toString()
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.d(java.lang.String, java.util.Iterator):java.lang.String");
    }

    public static boolean d(o.eg.b bVar, String str) {
        int i = j + 35;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                bVar.b(str);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                switch (bVar.b(str)) {
                    default:
                        if (!bVar.a(str)) {
                            int i2 = j + 91;
                            h = i2 % 128;
                            int i3 = i2 % 2;
                            return true;
                        }
                    case false:
                        return false;
                }
        }
    }

    public static byte[] e(long j2) {
        ByteBuffer allocate;
        int i = j + Opcodes.LNEG;
        h = i % 128;
        switch (i % 2 == 0) {
            case true:
                allocate = ByteBuffer.allocate(Opcodes.LMUL);
                allocate.putLong(1, j2);
                break;
            default:
                allocate = ByteBuffer.allocate(8);
                allocate.putLong(0, j2);
                break;
        }
        byte[] array = allocate.array();
        int i2 = j + 9;
        h = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                return array;
            default:
                throw null;
        }
    }

    public static long e(byte[] bArr) {
        int i = j + 109;
        h = i % 128;
        int i2 = i % 2;
        ByteBuffer allocate = ByteBuffer.allocate(8);
        allocate.put(bArr, 0, bArr.length);
        allocate.flip();
        long j2 = allocate.getLong();
        int i3 = j + 75;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? 'J' : 'K') {
            case 'J':
                return j2;
            default:
                int i4 = 19 / 0;
                return j2;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Multi-variable type inference failed */
    @java.lang.SafeVarargs
    public static <T extends o.ee.d<O>, O> O[] a(java.lang.Class<O> r4, T... r5) {
        /*
            int r0 = r5.length
            java.lang.Object r4 = java.lang.reflect.Array.newInstance(r4, r0)
            java.lang.Object[] r4 = (java.lang.Object[]) r4
            r1 = 0
        L9:
            if (r1 >= r0) goto Le
            r2 = 72
            goto Lf
        Le:
            r2 = 1
        Lf:
            switch(r2) {
                case 1: goto L1f;
                default: goto L12;
            }
        L12:
            int r2 = o.ee.o.j
            int r2 = r2 + 115
            int r3 = r2 % 128
            o.ee.o.h = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L20
            goto L20
        L1f:
            return r4
        L20:
            r2 = r5[r1]
            java.lang.Object r2 = r2.a()
            r4[r1] = r2
            int r1 = r1 + 1
            int r2 = o.ee.o.h
            int r2 = r2 + 39
            int r3 = r2 % 128
            o.ee.o.j = r3
            int r2 = r2 % 2
            goto L9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.a(java.lang.Class, o.ee.d[]):java.lang.Object[]");
    }

    @SafeVarargs
    public static <T extends d<O>, O> O[] b(Class<O> cls, T... tArr) {
        int i = j;
        int i2 = i + 19;
        h = i2 % 128;
        int i3 = i2 % 2;
        switch (tArr == null ? ']' : (char) 31) {
            case Opcodes.DUP2_X1 /* 93 */:
                int i4 = i + 27;
                h = i4 % 128;
                int i5 = i4 % 2;
                return null;
            default:
                O[] oArr = (O[]) a(cls, tArr);
                int i6 = h + Opcodes.DDIV;
                j = i6 % 128;
                switch (i6 % 2 != 0) {
                    case false:
                        return oArr;
                    default:
                        int i7 = 46 / 0;
                        return oArr;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static <T extends o.ee.d<O>, O> java.util.List<O> d(java.util.Collection<T> r3) {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            int r1 = r3.size()
            r0.<init>(r1)
            java.util.Iterator r3 = r3.iterator()
        Le:
            boolean r1 = r3.hasNext()
            if (r1 == 0) goto L17
            r1 = 48
            goto L19
        L17:
            r1 = 31
        L19:
            switch(r1) {
                case 48: goto L1d;
                default: goto L1c;
            }
        L1c:
            goto L51
        L1d:
            int r1 = o.ee.o.j
            int r1 = r1 + 7
            int r2 = r1 % 128
            o.ee.o.h = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L2b
            r1 = 0
            goto L2c
        L2b:
            r1 = 1
        L2c:
            switch(r1) {
                case 0: goto L3d;
                default: goto L2f;
            }
        L2f:
            java.lang.Object r1 = r3.next()
            o.ee.d r1 = (o.ee.d) r1
            java.lang.Object r1 = r1.a()
            r0.add(r1)
            goto Le
        L3d:
            java.lang.Object r3 = r3.next()
            o.ee.d r3 = (o.ee.d) r3
            java.lang.Object r3 = r3.a()
            r0.add(r3)
            r3 = 0
            r3.hashCode()     // Catch: java.lang.Throwable -> L4f
            throw r3     // Catch: java.lang.Throwable -> L4f
        L4f:
            r3 = move-exception
            throw r3
        L51:
            int r3 = o.ee.o.h
            int r3 = r3 + 47
            int r1 = r3 % 128
            o.ee.o.j = r1
            int r3 = r3 % 2
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.d(java.util.Collection):java.util.List");
    }

    /* JADX WARN: Removed duplicated region for block: B:12:0x0036  */
    /* JADX WARN: Removed duplicated region for block: B:9:0x0027  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public static <T extends o.ee.d<O>, O> java.util.List<O> b(java.util.Collection<T> r3) {
        /*
            int r0 = o.ee.o.j
            int r1 = r0 + 73
            int r2 = r1 % 128
            o.ee.o.h = r2
            int r1 = r1 % 2
            if (r1 != 0) goto L1d
            r1 = 4
            int r1 = r1 / 0
            if (r3 != 0) goto L15
            r1 = 67
            goto L17
        L15:
            r1 = 24
        L17:
            switch(r1) {
                case 67: goto L26;
                default: goto L1a;
            }
        L1a:
            goto L27
        L1b:
            r3 = move-exception
            throw r3
        L1d:
            if (r3 != 0) goto L22
            r1 = 21
            goto L23
        L22:
            r1 = 1
        L23:
            switch(r1) {
                case 1: goto L27;
                default: goto L26;
            }
        L26:
            goto L36
        L27:
            java.util.List r3 = d(r3)
            int r0 = o.ee.o.h
            int r0 = r0 + 79
            int r1 = r0 % 128
            o.ee.o.j = r1
            int r0 = r0 % 2
            return r3
        L36:
            int r0 = r0 + 121
            int r3 = r0 % 128
            o.ee.o.h = r3
            int r0 = r0 % 2
            r3 = 0
            if (r0 == 0) goto L43
        L42:
            return r3
        L43:
            throw r3     // Catch: java.lang.Throwable -> L44
        L44:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.b(java.util.Collection):java.util.List");
    }

    public static <T1 extends d<O1>, O1> Map<String, O1> d(Map<String, T1> map) {
        HashMap hashMap = new HashMap();
        Iterator<Map.Entry<String, T1>> it = map.entrySet().iterator();
        while (true) {
            switch (it.hasNext()) {
                case true:
                    int i = h + 5;
                    j = i % 128;
                    int i2 = i % 2;
                    Map.Entry<String, T1> next = it.next();
                    hashMap.put(next.getKey(), next.getValue().a());
                default:
                    int i3 = h + 99;
                    j = i3 % 128;
                    switch (i3 % 2 != 0 ? '0' : (char) 18) {
                        case 18:
                            return hashMap;
                        default:
                            throw null;
                    }
            }
        }
    }

    /* JADX WARN: Incorrect return type in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>(Ljava/lang/Class<TE;>;Ljava/lang/String;)TE; */
    /* JADX WARN: Multi-variable type inference failed */
    public static Enum c(Class cls, String str) {
        int i = j + 9;
        h = i % 128;
        int i2 = i % 2;
        Enum[] enumArr = (Enum[]) cls.getEnumConstants();
        switch (enumArr == 0 ? ',' : '/') {
            case '/':
                for (ColorSpace.Named named : enumArr) {
                    switch (((o.ei.b) named).e().equals(str) ? '\\' : '\f') {
                        case '\f':
                        default:
                            int i3 = j + 77;
                            h = i3 % 128;
                            int i4 = i3 % 2;
                            return named;
                    }
                }
                int i5 = j + Opcodes.LREM;
                h = i5 % 128;
                int i6 = i5 % 2;
                return null;
            default:
                return null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    /* JADX WARN: Incorrect types in method signature: <E:Ljava/lang/Enum<TE;>;:Lo/ei/b;>([TE;)Lo/eg/e; */
    /* JADX WARN: Multi-variable type inference failed */
    public static o.eg.e a(java.lang.Enum[] r6) {
        /*
            o.eg.e r0 = new o.eg.e
            r0.<init>()
            int r1 = r6.length
            int r2 = o.ee.o.j
            int r2 = r2 + 17
            int r3 = r2 % 128
            o.ee.o.h = r3
            int r2 = r2 % 2
            r2 = 0
            r3 = r2
        L13:
            if (r3 >= r1) goto L17
            r4 = r2
            goto L18
        L17:
            r4 = 1
        L18:
            switch(r4) {
                case 0: goto L1c;
                default: goto L1b;
            }
        L1b:
            return r0
        L1c:
            int r4 = o.ee.o.h
            int r4 = r4 + 69
            int r5 = r4 % 128
            o.ee.o.j = r5
            int r4 = r4 % 2
            if (r4 == 0) goto L2b
            r4 = 94
            goto L2d
        L2b:
            r4 = 93
        L2d:
            switch(r4) {
                case 94: goto L3e;
                default: goto L30;
            }
        L30:
            r4 = r6[r3]
            o.ei.b r4 = (o.ei.b) r4
            java.lang.String r4 = r4.e()
            r0.b(r4)
            int r3 = r3 + 1
            goto L13
        L3e:
            r4 = r6[r3]
            o.ei.b r4 = (o.ei.b) r4
            java.lang.String r4 = r4.e()
            r0.b(r4)
            int r3 = r3 + 66
            goto L13
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.a(java.lang.Enum[]):o.eg.e");
    }

    public static o.eg.e e(List<?> list) {
        o.eg.e eVar = new o.eg.e();
        Iterator<?> it = list.iterator();
        while (true) {
            switch (!it.hasNext()) {
                case false:
                    int i = h + 57;
                    j = i % 128;
                    int i2 = i % 2;
                    eVar.b(it.next());
                    int i3 = j + Opcodes.LMUL;
                    h = i3 % 128;
                    int i4 = i3 % 2;
                default:
                    return eVar;
            }
        }
    }

    public static String c(String str) {
        int i = h + 95;
        j = i % 128;
        int i2 = i % 2;
        String a2 = a(str.getBytes(j.c()));
        int i3 = h + 17;
        j = i3 % 128;
        int i4 = i3 % 2;
        return a2;
    }

    private static String a(byte[] bArr) {
        String str = new String(Base64.encode(o.ec.e.d(bArr), 10), j.c());
        int i = j + 83;
        h = i % 128;
        int i2 = i % 2;
        return str;
    }

    public static <T> T d(T t, T t2) {
        int i = h;
        int i2 = i + 47;
        j = i2 % 128;
        int i3 = i2 % 2;
        switch (t != null ? (char) 23 : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return t2;
            default:
                int i4 = i + 109;
                j = i4 % 128;
                int i5 = i4 % 2;
                return t;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    @java.lang.SafeVarargs
    public static <K, V> java.util.List<V> e(java.util.Map<K, V> r7, K... r8) {
        /*
            int r0 = r8.length
            java.util.ArrayList r1 = new java.util.ArrayList
            r1.<init>(r0)
            int r0 = r8.length
            r2 = 0
            r3 = r2
        La:
            r4 = 1
            if (r3 >= r0) goto Lf
            r5 = r2
            goto L10
        Lf:
            r5 = r4
        L10:
            switch(r5) {
                case 0: goto L14;
                default: goto L13;
            }
        L13:
            goto L4a
        L14:
            int r5 = o.ee.o.j
            int r5 = r5 + 17
            int r6 = r5 % 128
            o.ee.o.h = r6
            int r5 = r5 % 2
            r5 = r8[r3]
            java.lang.Object r6 = r7.get(r5)
            if (r6 == 0) goto L29
            r6 = 41
            goto L2b
        L29:
            r6 = 54
        L2b:
            switch(r6) {
                case 41: goto L2f;
                default: goto L2e;
            }
        L2e:
            goto L36
        L2f:
            java.lang.Object r5 = r7.get(r5)
            r1.add(r5)
        L36:
            int r3 = r3 + 1
            int r5 = o.ee.o.j
            int r5 = r5 + 45
            int r6 = r5 % 128
            o.ee.o.h = r6
            int r5 = r5 % 2
            if (r5 != 0) goto L45
            goto L46
        L45:
            r4 = r2
        L46:
            switch(r4) {
                case 0: goto L49;
                default: goto L49;
            }
        L49:
            goto La
        L4a:
            int r7 = o.ee.o.j
            int r7 = r7 + 113
            int r8 = r7 % 128
            o.ee.o.h = r8
            int r7 = r7 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.e(java.util.Map, java.lang.Object[]):java.util.List");
    }

    public static int e(Context context, float f) {
        float f2;
        int i = h + Opcodes.LSUB;
        j = i % 128;
        switch (i % 2 != 0) {
            case true:
                f2 = f % context.getResources().getDisplayMetrics().density;
                break;
            default:
                f2 = f * context.getResources().getDisplayMetrics().density;
                break;
        }
        return (int) f2;
    }

    public static String d(String str) {
        int length = str.length() - 1;
        char upperCase = Character.toUpperCase('f');
        int i = h + 87;
        j = i % 128;
        int i2 = i % 2;
        int i3 = length;
        int i4 = i3;
        while (true) {
            switch (i3 > 0) {
                case false:
                    break;
                default:
                    switch (Character.toUpperCase(str.charAt(i3)) == upperCase ? false : 43) {
                        case true:
                            break;
                        default:
                            int i5 = j + 33;
                            h = i5 % 128;
                            if (i5 % 2 == 0) {
                                i4 += 54;
                                i3 += 0;
                            } else {
                                i4--;
                                i3--;
                            }
                    }
            }
        }
        Object obj = null;
        switch (i4 == 0 ? '(' : (char) 4) {
            case '(':
                int i6 = j + Opcodes.DREM;
                int i7 = i6 % 128;
                h = i7;
                if (i6 % 2 == 0) {
                    obj.hashCode();
                    throw null;
                }
                int i8 = i7 + 31;
                j = i8 % 128;
                int i9 = i8 % 2;
                return "";
            default:
                switch (i4 != length ? 'L' : '+') {
                    case kotlin.io.encoding.Base64.mimeLineLength /* 76 */:
                        return str.substring(0, i4 + 1);
                    default:
                        int i10 = h + 55;
                        j = i10 % 128;
                        if (i10 % 2 == 0) {
                            return str;
                        }
                        obj.hashCode();
                        throw null;
                }
        }
    }

    public static String e(String str) {
        int length = str.length();
        switch (length == 4) {
            case false:
                switch (length <= 4) {
                    case false:
                        int i = j + 51;
                        h = i % 128;
                        int i2 = i % 2;
                        return str.substring(length - 4);
                    default:
                        g.c();
                        Object[] objArr = new Object[1];
                        g("㜺ᡩ㠏涸芓\uf29e", TextUtils.getCapsMode("", 0, 0) + 5, objArr);
                        String intern = ((String) objArr[0]).intern();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr2 = new Object[1];
                        f(25 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "\u001a\u001b￪\u000f\b\u0019ￏ\uffd0ￇￔￇ\ufff0\u0015\u0017\u001c\u001bￇ\ufffa\u001b\u0019\u0010\u0015\u000e\u0007\u000e\f\u001b\ufff3\b", 29 - TextUtils.indexOf("", "", 0), 252 - ExpandableListView.getPackedPositionChild(0L), false, objArr2);
                        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
                        Object[] objArr3 = new Object[1];
                        f((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 5, "ￇ\u001b\u000f\b\u0015ￇ\u0007ￇ\u0010\u001aￇ\u0013\u0016\u001e\f\u0019", 16 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 253 - View.getDefaultSize(0, 0), false, objArr3);
                        StringBuilder append2 = append.append(((String) objArr3[0]).intern()).append(4);
                        Object[] objArr4 = new Object[1];
                        f(3 - KeyEvent.keyCodeFromString(""), "\u000f\nￇ\u0019\b", 4 - MotionEvent.axisFromString(""), TextUtils.indexOf("", "", 0, 0) + 253, true, objArr4);
                        g.d(intern, append2.append(((String) objArr4[0]).intern()).toString());
                        return "";
                }
            default:
                int i3 = j + 19;
                h = i3 % 128;
                int i4 = i3 % 2;
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static int b(int[] r8) {
        /*
            r0 = 10
            int[] r1 = new int[r0]
            r1 = {x0086: FILL_ARRAY_DATA , data: [0, 2, 4, 6, 8, 1, 3, 5, 7, 9} // fill-array
            r2 = 0
            r3 = r2
            r4 = r3
        Lc:
            int r5 = r8.length
            if (r2 >= r5) goto L12
            r5 = 58
            goto L13
        L12:
            r5 = r0
        L13:
            switch(r5) {
                case 58: goto L1b;
                default: goto L16;
            }
        L16:
            int r3 = r3 + r4
            int r3 = r3 % r0
            if (r3 <= 0) goto L6d
            goto L54
        L1b:
            int r5 = o.ee.o.h
            int r5 = r5 + 71
            int r6 = r5 % 128
            o.ee.o.j = r6
            int r5 = r5 % 2
            r7 = 42
            if (r5 == 0) goto L34
            int r5 = r2 >>> 3
            if (r5 != 0) goto L2e
            goto L30
        L2e:
            r7 = 93
        L30:
            switch(r7) {
                case 42: goto L3f;
                default: goto L33;
            }
        L33:
            goto L40
        L34:
            int r5 = r2 % 2
            if (r5 != 0) goto L3b
            r7 = 57
            goto L3c
        L3b:
        L3c:
            switch(r7) {
                case 42: goto L40;
                default: goto L3f;
            }
        L3f:
            goto L4e
        L40:
            r5 = r8[r2]
            r5 = r1[r5]
            int r4 = r4 + r5
            int r6 = r6 + 83
            int r5 = r6 % 128
            o.ee.o.h = r5
            int r6 = r6 % 2
            goto L51
        L4e:
            r5 = r8[r2]
            int r3 = r3 + r5
        L51:
            int r2 = r2 + 1
            goto Lc
        L54:
            int r8 = o.ee.o.j
            int r8 = r8 + 83
            int r0 = r8 % 128
            o.ee.o.h = r0
            int r8 = r8 % 2
            if (r8 != 0) goto L63
            r8 = 69
            goto L65
        L63:
            r8 = 37
        L65:
            switch(r8) {
                case 69: goto L6b;
                default: goto L68;
            }
        L68:
            int r3 = 10 - r3
            goto L6d
        L6b:
            int r3 = r3 * 32
        L6d:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.b(int[]):int");
    }

    public static boolean a(CharSequence charSequence) {
        int i = h;
        int i2 = i + 65;
        j = i2 % 128;
        int i3 = i2 % 2;
        if (charSequence != null) {
            int i4 = i + 47;
            j = i4 % 128;
            int i5 = i4 % 2;
            switch (charSequence.length() == 0 ? (char) 30 : (char) 24) {
                case 24:
                    int i6 = j + 83;
                    h = i6 % 128;
                    switch (i6 % 2 != 0) {
                        case true:
                            return false;
                        default:
                            int i7 = 48 / 0;
                            return false;
                    }
            }
        }
        return true;
    }

    private static void f(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] cArr;
        switch (str != null ? '7' : (char) 25) {
            case 25:
                cArr = str;
                break;
            default:
                cArr = str.toCharArray();
                break;
        }
        char[] cArr2 = cArr;
        o.a.h hVar = new o.a.h();
        char[] cArr3 = new char[i2];
        hVar.a = 0;
        while (hVar.a < i2) {
            int i4 = $10 + 35;
            $11 = i4 % 128;
            int i5 = i4 % 2;
            hVar.b = cArr2[hVar.a];
            cArr3[hVar.a] = (char) (i3 + hVar.b);
            int i6 = hVar.a;
            try {
                Object[] objArr2 = {Integer.valueOf(cArr3[i6]), Integer.valueOf(c)};
                Object obj = o.e.a.s.get(2038615114);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(13 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), (char) TextUtils.getTrimmedLength(""), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 458);
                    byte b2 = (byte) 0;
                    byte b3 = b2;
                    Object[] objArr3 = new Object[1];
                    i(b2, b3, b3, objArr3);
                    obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                    o.e.a.s.put(2038615114, obj);
                }
                cArr3[i6] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                try {
                    Object[] objArr4 = {hVar, hVar};
                    Object obj2 = o.e.a.s.get(-1412673904);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(Color.alpha(0) + 11, (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 313 - View.MeasureSpec.getMode(0));
                        byte b4 = (byte) 1;
                        byte b5 = (byte) (b4 - 1);
                        Object[] objArr5 = new Object[1];
                        i(b4, b5, b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                        o.e.a.s.put(-1412673904, obj2);
                    }
                    ((Method) obj2).invoke(null, objArr4);
                    int i7 = $10 + 91;
                    $11 = i7 % 128;
                    int i8 = i7 % 2;
                } catch (Throwable th) {
                    Throwable cause = th.getCause();
                    if (cause == null) {
                        throw th;
                    }
                    throw cause;
                }
            } catch (Throwable th2) {
                Throwable cause2 = th2.getCause();
                if (cause2 == null) {
                    throw th2;
                }
                throw cause2;
            }
        }
        switch (i <= 0) {
            case false:
                hVar.c = i;
                char[] cArr4 = new char[i2];
                System.arraycopy(cArr3, 0, cArr4, 0, i2);
                System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
                break;
        }
        if (z) {
            char[] cArr5 = new char[i2];
            hVar.a = 0;
            while (hVar.a < i2) {
                int i9 = $10 + Opcodes.LREM;
                $11 = i9 % 128;
                if (i9 % 2 == 0) {
                    cArr5[hVar.a] = cArr3[hVar.a * i2 * 1];
                    try {
                        Object[] objArr6 = {hVar, hVar};
                        Object obj3 = o.e.a.s.get(-1412673904);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 11, (char) (ViewConfiguration.getEdgeSlop() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) + 313);
                            byte b6 = (byte) 1;
                            byte b7 = (byte) (b6 - 1);
                            Object[] objArr7 = new Object[1];
                            i(b6, b7, b7, objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th3) {
                        Throwable cause3 = th3.getCause();
                        if (cause3 == null) {
                            throw th3;
                        }
                        throw cause3;
                    }
                } else {
                    cArr5[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                    try {
                        Object[] objArr8 = {hVar, hVar};
                        Object obj4 = o.e.a.s.get(-1412673904);
                        if (obj4 == null) {
                            Class cls4 = (Class) o.e.a.c(TextUtils.indexOf((CharSequence) "", '0') + 12, (char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) + 313);
                            byte b8 = (byte) 1;
                            byte b9 = (byte) (b8 - 1);
                            Object[] objArr9 = new Object[1];
                            i(b8, b9, b9, objArr9);
                            obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj4);
                        }
                        ((Method) obj4).invoke(null, objArr8);
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                }
            }
            cArr3 = cArr5;
        }
        objArr[0] = new String(cArr3);
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 588
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ee.o.g(java.lang.String, int, java.lang.Object[]):void");
    }
}
