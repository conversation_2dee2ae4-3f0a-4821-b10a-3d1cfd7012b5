package o.ds;

import android.text.TextUtils;
import android.view.ViewConfiguration;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ds\a.smali */
final class a implements b {
    private static char[] b;
    private static long c;
    private static int d = 0;
    private static int e;

    static {
        e = 1;
        c();
        ViewConfiguration.getFadingEdgeLength();
        ViewConfiguration.getKeyRepeatDelay();
        TextUtils.indexOf("", "", 0);
        int i = d + 95;
        e = i % 128;
        switch (i % 2 != 0) {
            case true:
                break;
            default:
                int i2 = 64 / 0;
                break;
        }
    }

    static void c() {
        b = new char[]{11399, 46189, 7452, 59079, 20363, 55130, 47202, 304, 60152, 29573, 56137};
        c = 771742684883170306L;
    }

    a() {
    }

    @Override // o.ds.b
    public final f d() {
        int i = d + 77;
        e = i % 128;
        switch (i % 2 == 0) {
            case true:
                f fVar = f.c;
                throw null;
            default:
                return f.c;
        }
    }
}
