package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\d.smali */
public interface d {
    void a(SecureStorageSDKException secureStorageSDKException);

    void a(byte[] bArr);
}
