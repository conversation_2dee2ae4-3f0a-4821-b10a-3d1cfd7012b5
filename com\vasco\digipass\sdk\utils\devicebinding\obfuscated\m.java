package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import android.util.Base64;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.Key;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import javax.crypto.SecretKey;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\bÀ\u0002\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\r\u0010\u000eJ*\u0010\n\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0003\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u0006J\u0016\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u00042\u0006\u0010\f\u001a\u00020\u0004¨\u0006\u000f"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/m;", "", "Landroid/content/Context;", "context", "", "keyAlias", "", "invalidateKeyOnNewEnrollment", "userBiometricAuthenticationRequired", "Ljavax/crypto/SecretKey;", "a", "salt", "prefix", "<init>", "()V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\m.smali */
public final class m {
    public static final m a = new m();

    private m() {
    }

    public static /* synthetic */ SecretKey a(m mVar, Context context, String str, boolean z, boolean z2, int i, Object obj) throws DeviceBindingSDKException {
        if ((i & 8) != 0) {
            z2 = false;
        }
        return mVar.a(context, str, z, z2);
    }

    public final SecretKey a(Context context, String keyAlias, boolean invalidateKeyOnNewEnrollment, boolean userBiometricAuthenticationRequired) throws DeviceBindingSDKException {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        try {
            KeyStore keyStore = KeyStore.getInstance("AndroidKeyStore");
            keyStore.load(null);
            Key key = keyStore.getKey(keyAlias, null);
            SecretKey secretKey = key instanceof SecretKey ? (SecretKey) key : null;
            return secretKey == null ? k.a.a(keyAlias, context, invalidateKeyOnNewEnrollment, userBiometricAuthenticationRequired) : secretKey;
        } catch (DeviceBindingSDKException e) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e);
        } catch (IOException e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e2);
        } catch (KeyStoreException e3) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e3);
        } catch (NoSuchAlgorithmException e4) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e4);
        } catch (UnrecoverableKeyException e5) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e5);
        } catch (CertificateException e6) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.HMAC_KEYGEN_ERROR, e6);
        }
    }

    public final String a(String salt, String prefix) throws DeviceBindingSDKException {
        Intrinsics.checkNotNullParameter(salt, "salt");
        Intrinsics.checkNotNullParameter(prefix, "prefix");
        Charset forName = Charset.forName("UTF-8");
        Intrinsics.checkNotNullExpressionValue(forName, "forName(charsetName)");
        byte[] bytes = salt.getBytes(forName);
        Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
        UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash((byte) 3, bytes);
        if (hash.getReturnCode() == 0) {
            StringBuilder append = new StringBuilder().append(prefix);
            byte[] encode = Base64.encode(hash.getOutputData(), 0);
            Intrinsics.checkNotNullExpressionValue(encode, "encode(hash.outputData, Base64.DEFAULT)");
            return append.append(new String(encode, Charsets.UTF_8)).toString();
        }
        throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, null, 2, null);
    }
}
