package fr.antelop.sdk.card.emvapplication;

import fr.antelop.sdk.exception.WalletValidationException;
import java.util.Map;
import o.eo.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\emvapplication\EmvApplicationGroup.smali */
public final class EmvApplicationGroup {
    private final d innerGroup;

    public EmvApplicationGroup(d dVar) {
        this.innerGroup = dVar;
    }

    public final boolean isCredit() {
        return this.innerGroup.a();
    }

    public final String getId() {
        return this.innerGroup.h();
    }

    public final String getLabel() {
        return this.innerGroup.j();
    }

    public final Map<String, EmvApplication> emvApplications() {
        return this.innerGroup.c();
    }

    public final EmvApplication getEmvApplication(String str) {
        return this.innerGroup.b(str);
    }

    public final String getDefaultEmvApplicationId() {
        return this.innerGroup.m();
    }

    public final String getCardId() {
        return this.innerGroup.l();
    }

    public final void setDefaultEmvApplication(String str) throws WalletValidationException {
        this.innerGroup.i(str);
    }

    public final void resetDefaultEmvApplication() throws WalletValidationException {
        this.innerGroup.n();
    }

    public final String getNextTransactionEmvApplicationId() {
        return this.innerGroup.k();
    }

    public final void setNextTransactionEmvApplication(String str) throws WalletValidationException {
        this.innerGroup.h(str);
    }

    public final void resetNextTransactionEmvApplication() throws WalletValidationException {
        this.innerGroup.o();
    }

    public final String toString() {
        return new StringBuilder("EmvApplicationGroup{, id=").append(getId()).append(", label=").append(getLabel()).append(", cardId=").append(getCardId() == null ? "" : getCardId()).append(", nextTransactionEmvApplicationId=").append(getNextTransactionEmvApplicationId() != null ? getNextTransactionEmvApplicationId() : "").append('}').toString();
    }
}
