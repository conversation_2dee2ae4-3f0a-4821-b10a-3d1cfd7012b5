package bc.org.bouncycastle.crypto.params;

import bc.org.bouncycastle.math.ec.ECPoint;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\params\ECPublicKeyParameters.smali */
public class ECPublicKeyParameters extends a {
    private final ECPoint c;

    public ECPublicKeyParameters(ECPoint eCPoint, ECDomainParameters eCDomainParameters) {
        super(false, eCDomainParameters);
        this.c = eCDomainParameters.validatePublicPoint(eCPoint);
    }

    public ECPoint getQ() {
        return this.c;
    }
}
