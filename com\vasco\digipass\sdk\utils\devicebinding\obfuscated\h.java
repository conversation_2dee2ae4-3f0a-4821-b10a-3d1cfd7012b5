package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKErrorCodes;
import com.vasco.digipass.sdk.utils.devicebinding.DeviceBindingSDKException;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.Charsets;

@Metadata(bv = {}, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0006\u001a\u00020\u0005¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\t"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/h;", "Lcom/vasco/digipass/sdk/utils/devicebinding/DeviceBinding;", "", "salt", "fingerprint", "Landroid/content/Context;", "context", "<init>", "(Landroid/content/Context;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\h.smali */
public final class h implements DeviceBinding {
    private final Context a;
    private final String b;

    public h(Context context) {
        Intrinsics.checkNotNullParameter(context, "context");
        this.a = context;
        this.b = "OneSpan_DeviceBinding_SE_";
    }

    @Override // com.vasco.digipass.sdk.utils.devicebinding.DeviceBinding
    public String fingerprint(String salt) {
        Intrinsics.checkNotNullParameter(salt, "salt");
        if (salt.length() == 0) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_NULL, null, 2, null);
        }
        if (salt.length() < 64) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.SALT_TOO_SHORT, null, 2, null);
        }
        try {
            m mVar = m.a;
            SecretKey a = m.a(mVar, this.a, mVar.a(salt, this.b), false, false, 8, null);
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(a);
            byte[] bytes = salt.getBytes(Charsets.UTF_8);
            Intrinsics.checkNotNullExpressionValue(bytes, "this as java.lang.String).getBytes(charset)");
            return p.a(mac.doFinal(bytes));
        } catch (DeviceBindingSDKException e) {
            throw e;
        } catch (SecurityException e2) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.PERMISSION_DENIED, null, 2, null);
        } catch (Exception e3) {
            throw new DeviceBindingSDKException(DeviceBindingSDKErrorCodes.INTERNAL_ERROR, e3);
        }
    }
}
