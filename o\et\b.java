package o.et;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplicationType;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\et\b.smali */
public final class b extends k {
    private static int e = 0;
    private static int a = 1;

    public b(String str, String str2, int i, String str3) {
        super(str, o.dp.b.g, str2, i, str3);
    }

    @Override // o.et.c
    protected final c c(String str, String str2, int i, String str3) {
        b bVar = new b(str, str2, i, str3);
        int i2 = a + 65;
        e = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return bVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.et.k, o.et.c
    public final EmvApplicationType e() {
        int i = e;
        int i2 = ((i | Opcodes.LREM) << 1) - (i ^ Opcodes.LREM);
        a = i2 % 128;
        int i3 = i2 % 2;
        EmvApplicationType emvApplicationType = EmvApplicationType.HceIdemiaWise;
        int i4 = (e + 100) - 1;
        a = i4 % 128;
        int i5 = i4 % 2;
        return emvApplicationType;
    }
}
