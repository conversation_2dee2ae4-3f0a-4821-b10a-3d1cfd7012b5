package o.df;

import android.graphics.drawable.Drawable;
import android.os.Process;
import android.view.ViewConfiguration;
import androidx.work.BackoffPolicy;
import com.esotericsoftware.asm.Opcodes;
import o.de.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\df\d.smali */
public final class d implements e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char b;
    private static int c;
    private static int d;
    private static long e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        c = 1;
        e();
        ViewConfiguration.getKeyRepeatTimeout();
        ViewConfiguration.getMaximumFlingVelocity();
        int i = c + Opcodes.LREM;
        d = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void e() {
        b = (char) 17149;
        a = 161105445;
        e = 6565854932352255525L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.df.d.$$a
            int r8 = 106 - r8
            int r7 = r7 * 2
            int r7 = 1 - r7
            int r6 = r6 * 4
            int r6 = 3 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1b
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L38
        L1b:
            r3 = r2
        L1c:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            int r6 = r6 + 1
            int r3 = r3 + 1
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L38:
            int r6 = -r6
            int r6 = r6 + r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1c
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.d.g(int, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{3, 85, -79, -50};
        $$b = Opcodes.INVOKEINTERFACE;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:12:0x003a. Please report as an issue. */
    @Override // o.df.e
    public final boolean a(o.bb.d dVar, o.de.b bVar) {
        switch (dVar.b()) {
            default:
                int i = d + 13;
                c = i % 128;
                int i2 = i % 2;
                switch (dVar.d() != o.bb.a.t ? 'C' : (char) 3) {
                    case 'C':
                        int i3 = d;
                        int i4 = i3 + 89;
                        c = i4 % 128;
                        switch (i4 % 2 == 0 ? 'B' : 'W') {
                        }
                        int i5 = i3 + 37;
                        c = i5 % 128;
                        if (i5 % 2 != 0) {
                            return true;
                        }
                        throw null;
                }
            case true:
                return false;
        }
    }

    @Override // o.df.e
    public final BackoffPolicy b() {
        int i = d + 69;
        c = i % 128;
        int i2 = i % 2;
        BackoffPolicy backoffPolicy = BackoffPolicy.EXPONENTIAL;
        int i3 = c + 63;
        d = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return backoffPolicy;
            default:
                int i4 = 62 / 0;
                return backoffPolicy;
        }
    }

    @Override // o.df.e
    public final int d() {
        int i = d + 39;
        c = i % 128;
        switch (i % 2 == 0 ? 'V' : (char) 29) {
            case 29:
                return Opcodes.ISHL;
            default:
                return 104;
        }
    }

    @Override // o.df.e
    public final int d(int i) {
        int i2 = c + 1;
        d = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                return ((int) Math.pow(2.0d, i - 1)) * Opcodes.ISHL;
            default:
                return 85 % ((int) Math.pow(2.0d, i % 1));
        }
    }

    @Override // o.df.e
    public final String a() {
        Object obj;
        int i = d + 13;
        c = i % 128;
        switch (i % 2 == 0 ? 'P' : '\b') {
            case 'P':
                Object[] objArr = new Object[1];
                f(Drawable.resolveOpacity(1, 1) + 1482156460, "垒\uf208れᨸ횳Ⴚᙬ硌⨫엵旰軀廰셗♖댖磅\uef61\ud89c", (char) (15152 - Process.getGidForName("")), "겳埩ၘ\ued16", "\u0000\u0000\u0000\u0000", objArr);
                obj = objArr[0];
                break;
            default:
                Object[] objArr2 = new Object[1];
                f(1482156460 - Drawable.resolveOpacity(0, 0), "垒\uf208れᨸ횳Ⴚᙬ硌⨫엵旰軀廰셗♖댖磅\uef61\ud89c", (char) (Process.getGidForName("") + 5649), "겳埩ၘ\ued16", "\u0000\u0000\u0000\u0000", objArr2);
                obj = objArr2[0];
                break;
        }
        String intern = ((String) obj).intern();
        int i2 = d + 25;
        c = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return intern;
        }
    }

    @Override // o.df.e
    public final f c(o.bb.d dVar) {
        switch (!dVar.b() ? 'U' : '%') {
            case Opcodes.CASTORE /* 85 */:
                int i = c + Opcodes.DREM;
                d = i % 128;
                int i2 = i % 2;
                switch (dVar.d() != o.bb.a.j) {
                    case false:
                        f fVar = f.l;
                        int i3 = d + 53;
                        c = i3 % 128;
                        if (i3 % 2 != 0) {
                            return fVar;
                        }
                        int i4 = 17 / 0;
                        return fVar;
                }
        }
        f fVar2 = f.k;
        int i5 = d + 89;
        c = i5 % 128;
        int i6 = i5 % 2;
        return fVar2;
    }

    @Override // o.df.e
    public final boolean b(f fVar) {
        int i = d + 41;
        c = i % 128;
        int i2 = i % 2;
        boolean z = true;
        switch (fVar != f.k) {
            case true:
                return false;
            default:
                int i3 = c;
                int i4 = i3 + 9;
                d = i4 % 128;
                switch (i4 % 2 != 0 ? '+' : 'W') {
                    case '+':
                        z = false;
                        break;
                }
                int i5 = i3 + 23;
                d = i5 % 128;
                int i6 = i5 % 2;
                return z;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(int r19, java.lang.String r20, char r21, java.lang.String r22, java.lang.String r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 676
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.df.d.f(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }
}
