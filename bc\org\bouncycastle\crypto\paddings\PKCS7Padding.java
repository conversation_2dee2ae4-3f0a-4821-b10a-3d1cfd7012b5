package bc.org.bouncycastle.crypto.paddings;

import bc.org.bouncycastle.crypto.InvalidCipherTextException;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h1;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\paddings\PKCS7Padding.smali */
public class PKCS7Padding implements h1 {
    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h1
    public int addPadding(byte[] bArr, int i) {
        byte length = (byte) (bArr.length - i);
        while (i < bArr.length) {
            bArr[i] = length;
            i++;
        }
        return length;
    }

    public String getPaddingName() {
        return "PKCS7";
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h1
    public void init(SecureRandom secureRandom) throws IllegalArgumentException {
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.h1
    public int padCount(byte[] bArr) throws InvalidCipherTextException {
        byte b = bArr[bArr.length - 1];
        int i = b & 255;
        int length = bArr.length - i;
        int i2 = ((i - 1) | length) >> 31;
        for (int i3 = 0; i3 < bArr.length; i3++) {
            i2 |= (bArr[i3] ^ b) & (~((i3 - length) >> 31));
        }
        if (i2 == 0) {
            return i;
        }
        throw new InvalidCipherTextException("pad block corrupted");
    }
}
