package o.bh;

import android.os.Process;
import android.text.TextUtils;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.n;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bh\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final d b;
    private static int c;
    private static long d;
    private static final /* synthetic */ d[] e;
    private static int h;
    private final String a;

    static void b() {
        d = -2507178742537920840L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0032). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r6, short r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 3
            int r7 = r7 + 68
            byte[] r0 = o.bh.d.$$a
            int r6 = r6 * 4
            int r6 = 4 - r6
            int r8 = r8 * 4
            int r8 = 1 - r8
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            goto L32
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            r5 = r9
            r9 = r8
            r8 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L32:
            int r7 = r7 + r8
            int r6 = r6 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bh.d.g(int, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{106, 35, -45, 57};
        $$b = 225;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    private static /* synthetic */ d[] c() {
        int i = c + Opcodes.DMUL;
        int i2 = i % 128;
        h = i2;
        switch (i % 2 == 0 ? (char) 4 : ')') {
        }
        d[] dVarArr = {b};
        int i3 = i2 + 29;
        c = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return dVarArr;
        }
    }

    public static d valueOf(String str) {
        int i = h + 51;
        c = i % 128;
        int i2 = i % 2;
        d dVar = (d) Enum.valueOf(d.class, str);
        int i3 = c + 23;
        h = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return dVar;
            default:
                int i4 = 18 / 0;
                return dVar;
        }
    }

    public static d[] values() {
        int i = c + 19;
        h = i % 128;
        int i2 = i % 2;
        d[] dVarArr = (d[]) e.clone();
        int i3 = h + 93;
        c = i3 % 128;
        int i4 = i3 % 2;
        return dVarArr;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        c = 0;
        h = 1;
        b();
        Object[] objArr = new Object[1];
        f("㝢㾺疑㜭ꆩ⒮䤷\ufffe二", 1 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("ꑣᰰ搗ꐬ舃ꊚ墑秪\uddad", (Process.myPid() >> 22) + 1, objArr2);
        b = new d(intern, ((String) objArr2[0]).intern());
        e = c();
        int i = h + 71;
        c = i % 128;
        int i2 = i % 2;
    }

    private d(String str, String str2) {
        this.a = str2;
    }

    public final String a() {
        int i = c;
        int i2 = i + Opcodes.LSHR;
        h = i2 % 128;
        int i3 = i2 % 2;
        String str = this.a;
        int i4 = i + 33;
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return str;
        }
    }

    private static void f(String str, int i, Object[] objArr) {
        char[] cArr;
        int i2 = $11 + 59;
        $10 = i2 % 128;
        Object obj = null;
        switch (i2 % 2 != 0 ? 'R' : '_') {
            case Opcodes.SWAP /* 95 */:
                switch (str == null) {
                    case false:
                        cArr = str.toCharArray();
                        break;
                    default:
                        cArr = str;
                        break;
                }
                n nVar = new n();
                char[] b2 = n.b(d ^ 8632603938177761503L, cArr, i);
                int i3 = 4;
                nVar.c = 4;
                while (nVar.c < b2.length) {
                    int i4 = $10 + 91;
                    $11 = i4 % 128;
                    int i5 = i4 % 2;
                    nVar.e = nVar.c - i3;
                    int i6 = nVar.c;
                    try {
                        Object[] objArr2 = {Long.valueOf(b2[nVar.c] ^ b2[nVar.c % i3]), Long.valueOf(nVar.e), Long.valueOf(d)};
                        Object obj2 = o.e.a.s.get(-1945790373);
                        if (obj2 == null) {
                            Class cls = (Class) o.e.a.c((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 11, (char) TextUtils.getTrimmedLength(""), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 42);
                            byte b3 = (byte) 0;
                            byte b4 = b3;
                            Object[] objArr3 = new Object[1];
                            g(b3, b4, b4, objArr3);
                            obj2 = cls.getMethod((String) objArr3[0], Long.TYPE, Long.TYPE, Long.TYPE);
                            o.e.a.s.put(-1945790373, obj2);
                        }
                        b2[i6] = ((Character) ((Method) obj2).invoke(null, objArr2)).charValue();
                        try {
                            Object[] objArr4 = {nVar, nVar};
                            Object obj3 = o.e.a.s.get(-341518981);
                            if (obj3 == null) {
                                Class cls2 = (Class) o.e.a.c(TextUtils.indexOf("", "", 0) + 10, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 249 - (ViewConfiguration.getJumpTapTimeout() >> 16));
                                byte b5 = (byte) 0;
                                byte b6 = (byte) (b5 + 1);
                                Object[] objArr5 = new Object[1];
                                g(b5, b6, (byte) (b6 - 1), objArr5);
                                obj3 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                o.e.a.s.put(-341518981, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr4);
                            i3 = 4;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                objArr[0] = new String(b2, 4, b2.length - 4);
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }
}
