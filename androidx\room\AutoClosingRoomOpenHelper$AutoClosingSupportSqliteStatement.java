package androidx.room;

import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.functions.Function1;
import kotlin.jvm.internal.Intrinsics;

/* JADX INFO: Access modifiers changed from: private */
/* compiled from: AutoClosingRoomOpenHelper.kt */
@Metadata(d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\t\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0002\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005¢\u0006\u0002\u0010\u0006J\u0018\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016J\u0018\u0010\u0011\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0012H\u0016J\u0018\u0010\u0013\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0014H\u0016J\u0010\u0010\u0015\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0018\u0010\u0016\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0003H\u0016J\b\u0010\u0017\u001a\u00020\fH\u0016J\b\u0010\u0018\u001a\u00020\fH\u0016J\u0010\u0010\u0019\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\u0001H\u0002J\b\u0010\u001b\u001a\u00020\fH\u0016J\b\u0010\u001c\u001a\u00020\u0014H\u0016J'\u0010\u001d\u001a\u0002H\u001e\"\u0004\b\u0000\u0010\u001e2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u0002H\u001e0 H\u0002¢\u0006\u0002\u0010!J\b\u0010\"\u001a\u00020\u000eH\u0016J\u001a\u0010#\u001a\u00020\f2\u0006\u0010$\u001a\u00020\u000e2\b\u0010\u000f\u001a\u0004\u0018\u00010\tH\u0002J\b\u0010%\u001a\u00020\u0014H\u0016J\n\u0010&\u001a\u0004\u0018\u00010\u0003H\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004¢\u0006\u0002\n\u0000R\"\u0010\u0007\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010\t0\bj\n\u0012\u0006\u0012\u0004\u0018\u00010\t`\nX\u0082\u0004¢\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004¢\u0006\u0002\n\u0000¨\u0006'"}, d2 = {"Landroidx/room/AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement;", "Landroidx/sqlite/db/SupportSQLiteStatement;", "sql", "", "autoCloser", "Landroidx/room/AutoCloser;", "(Ljava/lang/String;Landroidx/room/AutoCloser;)V", "binds", "Ljava/util/ArrayList;", "", "Lkotlin/collections/ArrayList;", "bindBlob", "", "index", "", "value", "", "bindDouble", "", "bindLong", "", "bindNull", "bindString", "clearBindings", "close", "doBinds", "supportSQLiteStatement", "execute", "executeInsert", "executeSqliteStatementWithRefCount", "T", "block", "Lkotlin/Function1;", "(Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;", "executeUpdateDelete", "saveBinds", "bindIndex", "simpleQueryForLong", "simpleQueryForString", "room-runtime_release"}, k = 1, mv = {1, 7, 1}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\room\AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement.smali */
public final class AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement implements SupportSQLiteStatement {
    private final AutoCloser autoCloser;
    private final ArrayList<Object> binds;
    private final String sql;

    public AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement(String sql, AutoCloser autoCloser) {
        Intrinsics.checkNotNullParameter(sql, "sql");
        Intrinsics.checkNotNullParameter(autoCloser, "autoCloser");
        this.sql = sql;
        this.autoCloser = autoCloser;
        this.binds = new ArrayList<>();
    }

    private final <T> T executeSqliteStatementWithRefCount(final Function1<? super SupportSQLiteStatement, ? extends T> block) {
        return (T) this.autoCloser.executeRefCountingFunction(new Function1<SupportSQLiteDatabase, T>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeSqliteStatementWithRefCount$1
            /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
            /* JADX WARN: Multi-variable type inference failed */
            {
                super(1);
            }

            @Override // kotlin.jvm.functions.Function1
            public final T invoke(SupportSQLiteDatabase db) {
                String str;
                Intrinsics.checkNotNullParameter(db, "db");
                str = AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement.this.sql;
                SupportSQLiteStatement statement = db.compileStatement(str);
                AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement.this.doBinds(statement);
                return block.invoke(statement);
            }
        });
    }

    /* JADX INFO: Access modifiers changed from: private */
    public final void doBinds(SupportSQLiteStatement supportSQLiteStatement) {
        Iterable $this$forEachIndexed$iv = this.binds;
        int i = 0;
        Iterator it = $this$forEachIndexed$iv.iterator();
        while (it.hasNext()) {
            it.next();
            int index$iv = i + 1;
            if (i < 0) {
                CollectionsKt.throwIndexOverflow();
            }
            int bindIndex = i + 1;
            Object bind = this.binds.get(i);
            if (bind == null) {
                supportSQLiteStatement.bindNull(bindIndex);
            } else if (bind instanceof Long) {
                supportSQLiteStatement.bindLong(bindIndex, ((Number) bind).longValue());
            } else if (bind instanceof Double) {
                supportSQLiteStatement.bindDouble(bindIndex, ((Number) bind).doubleValue());
            } else if (bind instanceof String) {
                supportSQLiteStatement.bindString(bindIndex, (String) bind);
            } else if (bind instanceof byte[]) {
                supportSQLiteStatement.bindBlob(bindIndex, (byte[]) bind);
            }
            i = index$iv;
        }
    }

    private final void saveBinds(int bindIndex, Object value) {
        int i;
        int index = bindIndex - 1;
        if (index >= this.binds.size() && (i = this.binds.size()) <= index) {
            while (true) {
                this.binds.add(null);
                if (i == index) {
                    break;
                } else {
                    i++;
                }
            }
        }
        this.binds.set(index, value);
    }

    @Override // java.io.Closeable, java.lang.AutoCloseable
    public void close() throws IOException {
    }

    @Override // androidx.sqlite.db.SupportSQLiteStatement
    public void execute() {
        executeSqliteStatementWithRefCount(new Function1<SupportSQLiteStatement, Object>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$execute$1
            @Override // kotlin.jvm.functions.Function1
            public final Object invoke(SupportSQLiteStatement statement) {
                Intrinsics.checkNotNullParameter(statement, "statement");
                statement.execute();
                return null;
            }
        });
    }

    @Override // androidx.sqlite.db.SupportSQLiteStatement
    public int executeUpdateDelete() {
        return ((Number) executeSqliteStatementWithRefCount(new Function1<SupportSQLiteStatement, Integer>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeUpdateDelete$1
            @Override // kotlin.jvm.functions.Function1
            public final Integer invoke(SupportSQLiteStatement obj) {
                Intrinsics.checkNotNullParameter(obj, "obj");
                return Integer.valueOf(obj.executeUpdateDelete());
            }
        })).intValue();
    }

    @Override // androidx.sqlite.db.SupportSQLiteStatement
    public long executeInsert() {
        return ((Number) executeSqliteStatementWithRefCount(new Function1<SupportSQLiteStatement, Long>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$executeInsert$1
            @Override // kotlin.jvm.functions.Function1
            public final Long invoke(SupportSQLiteStatement obj) {
                Intrinsics.checkNotNullParameter(obj, "obj");
                return Long.valueOf(obj.executeInsert());
            }
        })).longValue();
    }

    @Override // androidx.sqlite.db.SupportSQLiteStatement
    public long simpleQueryForLong() {
        return ((Number) executeSqliteStatementWithRefCount(new Function1<SupportSQLiteStatement, Long>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForLong$1
            @Override // kotlin.jvm.functions.Function1
            public final Long invoke(SupportSQLiteStatement obj) {
                Intrinsics.checkNotNullParameter(obj, "obj");
                return Long.valueOf(obj.simpleQueryForLong());
            }
        })).longValue();
    }

    @Override // androidx.sqlite.db.SupportSQLiteStatement
    public String simpleQueryForString() {
        return (String) executeSqliteStatementWithRefCount(new Function1<SupportSQLiteStatement, String>() { // from class: androidx.room.AutoClosingRoomOpenHelper$AutoClosingSupportSqliteStatement$simpleQueryForString$1
            @Override // kotlin.jvm.functions.Function1
            public final String invoke(SupportSQLiteStatement obj) {
                Intrinsics.checkNotNullParameter(obj, "obj");
                return obj.simpleQueryForString();
            }
        });
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void bindNull(int index) {
        saveBinds(index, null);
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void bindLong(int index, long value) {
        saveBinds(index, Long.valueOf(value));
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void bindDouble(int index, double value) {
        saveBinds(index, Double.valueOf(value));
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void bindString(int index, String value) {
        Intrinsics.checkNotNullParameter(value, "value");
        saveBinds(index, value);
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void bindBlob(int index, byte[] value) {
        Intrinsics.checkNotNullParameter(value, "value");
        saveBinds(index, value);
    }

    @Override // androidx.sqlite.db.SupportSQLiteProgram
    public void clearBindings() {
        this.binds.clear();
    }
}
