package o.eo;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.card.emvapplication.EmvApplication;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\d.smali */
public final class d implements Iterable<o.el.d> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char f;
    private static int h;
    private static char[] i;

    /* renamed from: o, reason: collision with root package name */
    private static int f70o;
    private final LinkedHashSet<o.el.d> a = new LinkedHashSet<>();
    private String b;
    private String c;
    private boolean d;
    private final String e;
    private byte[] g;
    private byte[] j;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        f70o = 1;
        r();
        TextUtils.getCapsMode("", 0, 0);
        Color.alpha(0);
        int i2 = h + 83;
        f70o = i2 % 128;
        switch (i2 % 2 == 0 ? Typography.amp : '\\') {
            case Opcodes.DUP2 /* 92 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void init$0() {
        $$a = new byte[]{83, 27, -79, -63};
        $$b = 21;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(int r6, int r7, byte r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r7 = 73 - r7
            int r8 = r8 * 4
            int r8 = r8 + 1
            byte[] r0 = o.eo.d.$$a
            byte[] r1 = new byte[r8]
            int r8 = r8 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L36
        L19:
            r3 = r2
        L1a:
            int r6 = r6 + 1
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r8) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L36:
            int r6 = -r6
            int r6 = r6 + r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.q(int, int, byte, java.lang.Object[]):void");
    }

    static void r() {
        i = new char[]{30552, 30536, 30587, 30568, 30583, 30542, 30588, 30574, 30572, 30529, 30569, 30571, 30589, 30539, 30563, 30591, 30534, 30559, 30533, 30585, 30499, 30538, 30498, 30566, 30511, 30561, 30506, 30554, 30586, 30555, 30590, 30562, 30517, 30560, 30570, 30582};
        f = (char) 17043;
    }

    public d(String str) {
        this.e = str;
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:18:0x004c. Please report as an issue. */
    public final d a(o.el.b bVar) {
        d dVar = new d(this.e);
        dVar.b = this.b;
        dVar.c = this.c;
        dVar.d = this.d;
        byte[] bArr = this.g;
        if (bArr != null) {
            int i2 = h + 29;
            f70o = i2 % 128;
            switch (i2 % 2 == 0 ? '8' : (char) 11) {
                case 11:
                    dVar.g = Arrays.copyOf(bArr, bArr.length);
                    int i3 = f70o + 27;
                    h = i3 % 128;
                    switch (i3 % 2 != 0 ? 'C' : 'S') {
                    }
                default:
                    dVar.g = Arrays.copyOf(bArr, bArr.length);
                    throw null;
            }
        }
        byte[] bArr2 = this.j;
        switch (bArr2 != null ? ']' : (char) 7) {
            case Opcodes.DUP2_X1 /* 93 */:
                dVar.j = Arrays.copyOf(bArr2, bArr2.length);
                break;
        }
        Iterator<o.el.d> it = this.a.iterator();
        while (it.hasNext()) {
            dVar.a.add(it.next().c(bVar));
            int i4 = f70o + 73;
            h = i4 % 128;
            int i5 = i4 % 2;
        }
        return dVar;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ int d(o.et.c cVar, o.et.c cVar2) {
        int i2 = f70o + 45;
        h = i2 % 128;
        int i3 = i2 % 2;
        int m = cVar.m() - cVar2.m();
        switch (m == 0 ? '5' : (char) 14) {
            case 14:
                return m;
            default:
                int i4 = h + 55;
                f70o = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        int i5 = 0 / 0;
                        return 1;
                    default:
                        return 1;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.List<o.et.c> d() {
        /*
            r4 = this;
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.LinkedHashSet<o.el.d> r1 = r4.a
            java.util.Iterator r1 = r1.iterator()
        Lc:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L15
            r2 = 15
            goto L17
        L15:
            r2 = 43
        L17:
            switch(r2) {
                case 43: goto L27;
                default: goto L1a;
            }
        L1a:
            int r2 = o.eo.d.h
            int r2 = r2 + 11
            int r3 = r2 % 128
            o.eo.d.f70o = r3
            int r2 = r2 % 2
            if (r2 != 0) goto L33
            goto L30
        L27:
            o.eo.d$$ExternalSyntheticLambda0 r1 = new o.eo.d$$ExternalSyntheticLambda0
            r1.<init>()
            java.util.Collections.sort(r0, r1)
            return r0
        L30:
            r2 = 32
            goto L35
        L33:
            r2 = 63
        L35:
            switch(r2) {
                case 32: goto L43;
                default: goto L38;
            }
        L38:
            java.lang.Object r2 = r1.next()
            o.el.d r2 = (o.el.d) r2
            boolean r3 = r2 instanceof o.et.c
            if (r3 == 0) goto L57
            goto L52
        L43:
            java.lang.Object r0 = r1.next()
            o.el.d r0 = (o.el.d) r0
            boolean r0 = r0 instanceof o.et.c
            r0 = 0
            r0.hashCode()     // Catch: java.lang.Throwable -> L50
            throw r0     // Catch: java.lang.Throwable -> L50
        L50:
            r0 = move-exception
            throw r0
        L52:
            o.et.c r2 = (o.et.c) r2
            r0.add(r2)
        L57:
            int r2 = o.eo.d.f70o
            int r2 = r2 + 85
            int r3 = r2 % 128
            o.eo.d.h = r3
            int r2 = r2 % 2
            goto Lc
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.d():java.util.List");
    }

    public final List<o.el.d> b() {
        ArrayList arrayList = new ArrayList(this.a);
        int i2 = f70o + 9;
        h = i2 % 128;
        int i3 = i2 % 2;
        return arrayList;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.util.Map<java.lang.String, fr.antelop.sdk.card.emvapplication.EmvApplication> c() {
        /*
            r4 = this;
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            java.util.LinkedHashSet<o.el.d> r1 = r4.a
            java.util.Iterator r1 = r1.iterator()
            int r2 = o.eo.d.f70o
            int r2 = r2 + 13
            int r3 = r2 % 128
            o.eo.d.h = r3
            int r2 = r2 % 2
            if (r2 == 0) goto L1b
            r2 = 48
            goto L1d
        L1b:
            r2 = 29
        L1d:
            switch(r2) {
                case 29: goto L20;
                default: goto L20;
            }
        L20:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L29
            r2 = 97
            goto L2b
        L29:
            r2 = 41
        L2b:
            switch(r2) {
                case 41: goto L42;
                default: goto L2e;
            }
        L2e:
            java.lang.Object r2 = r1.next()
            o.el.d r2 = (o.el.d) r2
            java.lang.String r3 = r2.n()
            java.lang.Object r2 = r2.a()
            fr.antelop.sdk.card.emvapplication.EmvApplication r2 = (fr.antelop.sdk.card.emvapplication.EmvApplication) r2
            r0.put(r3, r2)
            goto L43
        L42:
            return r0
        L43:
            int r2 = o.eo.d.f70o
            int r2 = r2 + 37
            int r3 = r2 % 128
            o.eo.d.h = r3
            int r2 = r2 % 2
            goto L20
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.c():java.util.Map");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.el.d e(java.lang.String r4) {
        /*
            r3 = this;
            int r0 = o.eo.d.f70o
            int r0 = r0 + 73
            int r1 = r0 % 128
            o.eo.d.h = r1
            int r0 = r0 % 2
            java.util.LinkedHashSet<o.el.d> r0 = r3.a
            java.util.Iterator r0 = r0.iterator()
            int r1 = o.eo.d.f70o
            int r1 = r1 + 27
            int r2 = r1 % 128
            o.eo.d.h = r2
            int r1 = r1 % 2
        L1a:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L22
            r1 = 1
            goto L23
        L22:
            r1 = 0
        L23:
            switch(r1) {
                case 1: goto L28;
                default: goto L26;
            }
        L26:
            r4 = 0
            return r4
        L28:
            java.lang.Object r1 = r0.next()
            o.el.d r1 = (o.el.d) r1
            java.lang.String r2 = r1.n()
            boolean r2 = r2.equals(r4)
            if (r2 == 0) goto L3b
            r2 = 56
            goto L3d
        L3b:
            r2 = 57
        L3d:
            switch(r2) {
                case 56: goto L41;
                default: goto L40;
            }
        L40:
            goto L1a
        L41:
            int r4 = o.eo.d.f70o
            int r4 = r4 + 55
            int r0 = r4 % 128
            o.eo.d.h = r0
            int r4 = r4 % 2
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.e(java.lang.String):o.el.d");
    }

    public final EmvApplication b(String str) {
        int i2 = f70o + 59;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.el.d e = e(str);
        switch (e != null ? 'Y' : (char) 15) {
            case Opcodes.DUP /* 89 */:
                return e.a();
            default:
                int i4 = h + 73;
                f70o = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        throw null;
                    default:
                        return null;
                }
        }
    }

    public final boolean e() {
        int i2 = h + Opcodes.DMUL;
        f70o = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                boolean d = d(o.el.b.d);
                int i3 = f70o + 45;
                h = i3 % 128;
                int i4 = i3 % 2;
                return d;
            default:
                d(o.el.b.d);
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:103)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.processFallThroughCases(SwitchRegionMaker.java:105)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private boolean d(o.el.b r5) {
        /*
            r4 = this;
            int r0 = o.eo.d.f70o
            int r0 = r0 + 91
            int r1 = r0 % 128
            o.eo.d.h = r1
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            if (r0 == 0) goto L10
            r0 = r1
            goto L11
        L10:
            r0 = r2
        L11:
            switch(r0) {
                case 1: goto L1a;
                default: goto L14;
            }
        L14:
            java.util.LinkedHashSet<o.el.d> r5 = r4.a
            r5.iterator()
            goto L49
        L1a:
            java.util.LinkedHashSet<o.el.d> r0 = r4.a
            java.util.Iterator r0 = r0.iterator()
        L21:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L2a
            r3 = 71
            goto L2c
        L2a:
            r3 = 78
        L2c:
            switch(r3) {
                case 78: goto L3c;
                default: goto L2f;
            }
        L2f:
            java.lang.Object r3 = r0.next()
            o.el.d r3 = (o.el.d) r3
            o.el.b r3 = r3.s()
            if (r3 != r5) goto L48
            goto L3d
        L3c:
            return r1
        L3d:
            int r5 = o.eo.d.h
            int r5 = r5 + 55
            int r0 = r5 % 128
            o.eo.d.f70o = r0
            int r5 = r5 % 2
            return r2
        L48:
            goto L21
        L49:
            r5 = 0
            throw r5     // Catch: java.lang.Throwable -> L4b
        L4b:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.d(o.el.b):boolean");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.el.d a(java.lang.String r5) {
        /*
            r4 = this;
            int r0 = o.eo.d.f70o
            int r0 = r0 + 43
            int r1 = r0 % 128
            o.eo.d.h = r1
            int r0 = r0 % 2
            if (r0 == 0) goto Lf
            r0 = 91
            goto L11
        Lf:
            r0 = 49
        L11:
            r1 = 0
            switch(r0) {
                case 49: goto L1c;
                default: goto L15;
            }
        L15:
            java.util.LinkedHashSet<o.el.d> r0 = r4.a
            java.util.Iterator r0 = r0.iterator()
            goto L23
        L1c:
            java.util.LinkedHashSet<o.el.d> r0 = r4.a
            java.util.Iterator r0 = r0.iterator()
            goto L26
        L23:
            r2 = 93
            int r2 = r2 / r1
        L26:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L30
            r2 = 32
            goto L32
        L30:
            r2 = 20
        L32:
            switch(r2) {
                case 20: goto L42;
                default: goto L35;
            }
        L35:
            int r2 = o.eo.d.f70o
            int r2 = r2 + 37
            int r3 = r2 % 128
            o.eo.d.h = r3
            int r2 = r2 % 2
            if (r2 == 0) goto L44
            goto L44
        L42:
            r5 = 0
            return r5
        L44:
            java.lang.Object r2 = r0.next()
            o.el.d r2 = (o.el.d) r2
            java.lang.String r3 = r2.n()
            boolean r3 = r3.equals(r5)
            if (r3 == 0) goto L56
            r3 = 1
            goto L57
        L56:
            r3 = r1
        L57:
            switch(r3) {
                case 0: goto L65;
                default: goto L5a;
            }
        L5a:
            int r5 = o.eo.d.f70o
            int r5 = r5 + 85
            int r0 = r5 % 128
            o.eo.d.h = r0
            int r5 = r5 % 2
            goto L66
        L65:
            goto L26
        L66:
            return r2
        L67:
            r5 = move-exception
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.a(java.lang.String):o.el.d");
    }

    public final boolean a() {
        int i2 = f70o + Opcodes.LSUB;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? Typography.less : (char) 0) {
            case '<':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.d;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:100)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean d(o.el.d r6) {
        /*
            r5 = this;
            r0 = 1
            r1 = 0
            if (r6 != 0) goto L20
        L6:
            int r6 = o.eo.d.f70o
            int r6 = r6 + 17
            int r2 = r6 % 128
            o.eo.d.h = r2
            int r6 = r6 % 2
            if (r6 == 0) goto L14
            r0 = r1
            goto L15
        L14:
        L15:
            switch(r0) {
                case 0: goto L19;
                default: goto L18;
            }
        L18:
            return r1
        L19:
            r6 = 0
            r6.hashCode()     // Catch: java.lang.Throwable -> L1e
            throw r6     // Catch: java.lang.Throwable -> L1e
        L1e:
            r6 = move-exception
            throw r6
        L20:
            java.util.LinkedHashSet<o.el.d> r2 = r5.a
            java.util.Iterator r2 = r2.iterator()
        L26:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L2f
            r3 = 10
            goto L31
        L2f:
            r3 = 96
        L31:
            switch(r3) {
                case 96: goto L41;
                default: goto L34;
            }
        L34:
            int r3 = o.eo.d.f70o
            int r3 = r3 + 53
            int r4 = r3 % 128
            o.eo.d.h = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L60
            goto L47
        L41:
            java.util.LinkedHashSet<o.el.d> r1 = r5.a
            r1.add(r6)
            return r0
        L47:
            java.lang.Object r3 = r2.next()
            o.el.d r3 = (o.el.d) r3
            java.lang.String r3 = r3.n()
            java.lang.String r4 = r6.n()
            boolean r3 = r3.equals(r4)
            r4 = 1
            int r4 = r4 / r1
            if (r3 == 0) goto L7d
            goto L7c
        L5e:
            r6 = move-exception
            throw r6
        L60:
            java.lang.Object r3 = r2.next()
            o.el.d r3 = (o.el.d) r3
            java.lang.String r3 = r3.n()
            java.lang.String r4 = r6.n()
            boolean r3 = r3.equals(r4)
            if (r3 == 0) goto L77
            r3 = 36
            goto L79
        L77:
            r3 = 87
        L79:
            switch(r3) {
                case 87: goto L7d;
                default: goto L7c;
            }
        L7c:
            goto L89
        L7d:
            int r3 = o.eo.d.f70o
            int r3 = r3 + 93
            int r4 = r3 % 128
            o.eo.d.h = r4
            int r3 = r3 % 2
            goto L26
        L89:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.d(o.el.d):boolean");
    }

    final o.el.b f() {
        int i2 = f70o + 53;
        h = i2 % 128;
        int i3 = i2 % 2;
        Iterator<o.el.d> it = this.a.iterator();
        o.el.b bVar = null;
        while (true) {
            switch (it.hasNext()) {
                case true:
                    int i4 = h + 75;
                    f70o = i4 % 128;
                    int i5 = i4 % 2;
                    bVar = o.el.b.d(bVar, it.next().s());
                default:
                    return bVar;
            }
        }
    }

    final int g() {
        Iterator<o.el.d> it;
        int i2 = f70o + 83;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                it = this.a.iterator();
                break;
            default:
                it = this.a.iterator();
                int i3 = 76 / 0;
                break;
        }
        short s = -1;
        while (it.hasNext()) {
            o.el.d next = it.next();
            switch (next instanceof o.et.c ? '6' : '^') {
                case Opcodes.ISTORE /* 54 */:
                    int i4 = f70o + Opcodes.LNEG;
                    h = i4 % 128;
                    int i5 = i4 % 2;
                    short D = ((o.et.c) next).D();
                    switch (s != -1) {
                        case false:
                            s = D;
                            break;
                        default:
                            int i6 = f70o + 3;
                            h = i6 % 128;
                            if (i6 % 2 == 0) {
                                if (D >= s) {
                                    break;
                                } else {
                                    s = D;
                                }
                            } else {
                                int i7 = 16 / 0;
                                if (D >= s) {
                                    break;
                                } else {
                                    s = D;
                                    break;
                                }
                            }
                    }
            }
        }
        return Math.max(0, (int) s);
    }

    public final int i() {
        int i2 = h + 27;
        f70o = i2 % 128;
        switch (i2 % 2 == 0) {
            case true:
                this.a.size();
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                int size = this.a.size();
                int i3 = h + 31;
                f70o = i3 % 128;
                int i4 = i3 % 2;
                return size;
        }
    }

    public final String h() {
        String str;
        int i2 = h + 75;
        int i3 = i2 % 128;
        f70o = i3;
        switch (i2 % 2 == 0 ? (char) 26 : ',') {
            case ',':
                str = this.b;
                break;
            default:
                str = this.b;
                int i4 = 72 / 0;
                break;
        }
        int i5 = i3 + 33;
        h = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    public final String j() {
        int i2 = h;
        int i3 = i2 + 25;
        f70o = i3 % 128;
        int i4 = i3 % 2;
        String str = this.c;
        int i5 = i2 + 49;
        f70o = i5 % 128;
        int i6 = i5 % 2;
        return str;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final java.lang.String l() {
        /*
            r6 = this;
            int r0 = o.eo.d.f70o
            int r0 = r0 + 107
            int r1 = r0 % 128
            o.eo.d.h = r1
            int r0 = r0 % 2
            o.ei.c r0 = o.ei.c.c()
            java.util.Map r0 = r0.i()
            java.util.Collection r0 = r0.values()
            java.util.Iterator r0 = r0.iterator()
        L1a:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L22
            r1 = 7
            goto L24
        L22:
            r1 = 59
        L24:
            switch(r1) {
                case 59: goto L36;
                default: goto L27;
            }
        L27:
            int r1 = o.eo.d.f70o
            int r1 = r1 + 63
            int r2 = r1 % 128
            o.eo.d.h = r2
            int r1 = r1 % 2
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L62
            goto L48
        L36:
            int r0 = o.eo.d.f70o
            int r0 = r0 + 91
            int r1 = r0 % 128
            o.eo.d.h = r1
            int r0 = r0 % 2
            r1 = 0
            if (r0 != 0) goto L45
            return r1
        L45:
            throw r1     // Catch: java.lang.Throwable -> L46
        L46:
            r0 = move-exception
            throw r0
        L48:
            java.lang.Object r1 = r0.next()
            o.eo.e r1 = (o.eo.e) r1
            java.lang.String r4 = r6.h()
            boolean r4 = r1.i(r4)
            r5 = 5
            int r5 = r5 / r3
            if (r4 == 0) goto L5b
            goto L5c
        L5b:
            r2 = r3
        L5c:
            switch(r2) {
                case 1: goto L77;
                default: goto L5f;
            }
        L5f:
            goto L76
        L60:
            r0 = move-exception
            throw r0
        L62:
            java.lang.Object r1 = r0.next()
            o.eo.e r1 = (o.eo.e) r1
            java.lang.String r4 = r6.h()
            boolean r4 = r1.i(r4)
            if (r4 == 0) goto L73
            r2 = r3
        L73:
            switch(r2) {
                case 0: goto L77;
                default: goto L76;
            }
        L76:
            goto L92
        L77:
            int r0 = o.eo.d.h
            int r0 = r0 + 33
            int r2 = r0 % 128
            o.eo.d.f70o = r2
            int r0 = r0 % 2
            if (r0 != 0) goto L8d
            java.lang.String r0 = r1.e()
            r1 = 85
            int r1 = r1 / r3
            goto L91
        L8b:
            r0 = move-exception
            throw r0
        L8d:
            java.lang.String r0 = r1.e()
        L91:
            return r0
        L92:
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.l():java.lang.String");
    }

    @Override // java.lang.Iterable
    public final Iterator<o.el.d> iterator() {
        int i2 = h + Opcodes.LMUL;
        f70o = i2 % 128;
        int i3 = i2 % 2;
        Iterator<o.el.d> it = this.a.iterator();
        int i4 = f70o + 35;
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case false:
                int i5 = 36 / 0;
                return it;
            default:
                return it;
        }
    }

    public final void d(String str) {
        int i2 = h;
        int i3 = i2 + 99;
        f70o = i3 % 128;
        boolean z = i3 % 2 != 0;
        this.b = str;
        switch (z) {
            case true:
                int i4 = i2 + 41;
                f70o = i4 % 128;
                int i5 = i4 % 2;
                return;
            default:
                throw null;
        }
    }

    public final void c(String str) {
        int i2 = h + Opcodes.DREM;
        int i3 = i2 % 128;
        f70o = i3;
        char c = i2 % 2 == 0 ? '`' : ':';
        this.c = str;
        switch (c) {
            case Opcodes.IADD /* 96 */:
                int i4 = 87 / 0;
                break;
        }
        int i5 = i3 + 19;
        h = i5 % 128;
        switch (i5 % 2 != 0 ? 'P' : (char) 16) {
            case 16:
                return;
            default:
                int i6 = 79 / 0;
                return;
        }
    }

    public final void b(boolean z) {
        int i2 = f70o;
        int i3 = i2 + Opcodes.LUSHR;
        h = i3 % 128;
        int i4 = i3 % 2;
        this.d = z;
        int i5 = i2 + 15;
        h = i5 % 128;
        int i6 = i5 % 2;
    }

    public final void b(byte[] bArr) {
        int i2 = f70o;
        int i3 = i2 + 69;
        h = i3 % 128;
        int i4 = i3 % 2;
        this.g = bArr;
        int i5 = i2 + 19;
        h = i5 % 128;
        int i6 = i5 % 2;
    }

    public final String k() {
        o.ei.c c = o.ei.c.c();
        switch (!c.q()) {
            case true:
                int i2 = h + Opcodes.LREM;
                f70o = i2 % 128;
                int i3 = i2 % 2;
                return null;
            default:
                String a = c.d().e().a(this.e, this.b);
                Set<String> keySet = c().keySet();
                switch (a != null ? '[' : '\r') {
                    case '\r':
                        break;
                    default:
                        if (keySet.contains(a)) {
                            int i4 = f70o + 19;
                            h = i4 % 128;
                            int i5 = i4 % 2;
                            return a;
                        }
                        break;
                }
                return m();
        }
    }

    public final void h(String str) throws WalletValidationException {
        int i2 = f70o + 77;
        h = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        p((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 23, "\r\u001c\u001c\u001f\u000f\u0012\u0001\u0019\u0003\u0011\u0010\u000f\u0014\u000b\b\u0001\u0015#\u001f\u0007\u000f\u001e\u001b\u0010", (byte) (121 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1))), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p((ViewConfiguration.getJumpTapTimeout() >> 16) + 70, "\n\u001e\u0003\b\u0004\n\u0005\u001a\r\u0006\u0018\u0007\b\t\u0005\u0014\u001f\u001b\u0013!\u0017\u0001㘘㘘\u0011\u0014\t\b\u0005\u0014\u001f\u001b\u001c\u0012\u0019\u0000\u000f\u001e\u001b\u0010\u001d\u0012\u0006\u001d\u001e\u001a\u0018\b\u001c\u0012\u001d\u0000㘘㘘\u0011\u0014\t\b\u0005\u0014\u001f\u001b\u001d\u0012\u0006\u001d\u001e\u001a\u0018\b", (byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 45), objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), this.b, str));
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            p((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 14, "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (70 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        o.el.d a = a(str);
        if (a == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unknown;
            Object[] objArr4 = new Object[1];
            p(KeyEvent.keyCodeFromString("") + 14, "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (71 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        if (a.s() != o.el.b.d) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr5 = new Object[1];
            p(13 - TextUtils.lastIndexOf("", '0', 0), "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (70 - KeyEvent.keyCodeFromString("")), objArr5);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr5[0]).intern());
        }
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            p((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 6, "\u0001\u0006㙬㙬 \u0004", (byte) (118 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            p(43 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), "\u0001\u0006㘻㘻 \u0004\u001d\u0012\f\u001e\u001b\u001f\u0000\u001a\u0010\u0018㘹㘹\u0013\u001d\u0002\u0015\u001a\u0006\u001f\u001b\u001c\u001f\u000e\b\u0015#\u001a\u0019\u0010\u001e\"\u0018\u0012\u0011#\n", (byte) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 68), objArr7);
            throw new WalletValidationException(walletValidationErrorCode4, intern2, ((String) objArr7[0]).intern());
        }
        c.d().e().c(this.e, this.b, str);
        int i4 = h + 29;
        f70o = i4 % 128;
        switch (i4 % 2 == 0 ? '#' : Typography.amp) {
            case '#':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void o() throws WalletValidationException {
        int i2 = h + 25;
        f70o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        p(24 - (ViewConfiguration.getJumpTapTimeout() >> 16), "\r\u001c\u001c\u001f\u000f\u0012\u0001\u0019\u0003\u0011\u0010\u000f\u0014\u000b\b\u0001\u0015#\u001f\u0007\u000f\u001e\u001b\u0010", (byte) (120 - KeyEvent.getDeadChar(0, 0)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p(25 - Process.getGidForName(""), "\u0010\u001e\n\u001e\u0005\u000e\u000b\u001f #\u001a\u0001\u0013!\u0017\u0001㙤㙤\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (121 - ExpandableListView.getPackedPositionChild(0L)), objArr2);
        g.d(intern, ((String) objArr2[0]).intern());
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            p((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 5, "\u0001\u0006㙬㙬 \u0004", (byte) (118 - Color.red(0)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            p(41 - ImageFormat.getBitsPerPixel(0), "\u0001\u0006㘻㘻 \u0004\u001d\u0012\f\u001e\u001b\u001f\u0000\u001a\u0010\u0018㘹㘹\u0013\u001d\u0002\u0015\u001a\u0006\u001f\u001b\u001c\u001f\u000e\b\u0015#\u001a\u0019\u0010\u001e\"\u0018\u0012\u0011#\n", (byte) (69 - (ViewConfiguration.getJumpTapTimeout() >> 16)), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        c.d().e().f(this.e, this.b);
        int i4 = h + 45;
        f70o = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void i(String str) throws WalletValidationException {
        int i2 = h + 93;
        f70o = i2 % 128;
        int i3 = i2 % 2;
        g.c();
        Object[] objArr = new Object[1];
        p(24 - Color.alpha(0), "\r\u001c\u001c\u001f\u000f\u0012\u0001\u0019\u0003\u0011\u0010\u000f\u0014\u000b\b\u0001\u0015#\u001f\u0007\u000f\u001e\u001b\u0010", (byte) (TextUtils.indexOf((CharSequence) "", '0') + Opcodes.LSHL), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        p(62 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), "\n\u001e\u0001\u000e\u0004\u0010\n\u0019\u0014\b\u0013!\u0017\u0001㘩㘩\u0011\u0014\t\b\u0005\u0014\u001f\u001b\u001c\u0012\u0019\u0000\u000f\u001e\u001b\u0010\u001d\u0012\u0006\u001d\u001e\u001a\u0018\b\u001c\u0012\u001d\u0000㘩㘩\u0011\u0014\t\b\u0005\u0014\u001f\u001b\u001d\u0012\u0006\u001d\u001e\u001a\u0018\b", (byte) (TextUtils.lastIndexOf("", '0', 0) + 64), objArr2);
        g.d(intern, String.format(((String) objArr2[0]).intern(), this.b, str));
        if (str == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            p(AndroidCharacter.getMirror('0') - '\"', "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (69 - TextUtils.lastIndexOf("", '0')), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        o.el.d a = a(str);
        if (a == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unknown;
            Object[] objArr4 = new Object[1];
            p(Gravity.getAbsoluteGravity(0, 0) + 14, "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 69), objArr4);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr4[0]).intern());
        }
        if (a.s() != o.el.b.d) {
            WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.WrongState;
            Object[] objArr5 = new Object[1];
            p((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 13, "\u0013!\u0017\u0001㘰㘰\u0011\u0014\t\b\u0005\u0014\u001f\u001b", (byte) (70 - TextUtils.indexOf("", "", 0)), objArr5);
            throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr5[0]).intern());
        }
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
            Object[] objArr6 = new Object[1];
            p((Process.myTid() >> 22) + 6, "\u0001\u0006㙬㙬 \u0004", (byte) ((ViewConfiguration.getTapTimeout() >> 16) + Opcodes.FNEG), objArr6);
            String intern2 = ((String) objArr6[0]).intern();
            Object[] objArr7 = new Object[1];
            p((ViewConfiguration.getScrollBarSize() >> 8) + 42, "\u0001\u0006㘻㘻 \u0004\u001d\u0012\f\u001e\u001b\u001f\u0000\u001a\u0010\u0018㘹㘹\u0013\u001d\u0002\u0015\u001a\u0006\u001f\u001b\u001c\u001f\u000e\b\u0015#\u001a\u0019\u0010\u001e\"\u0018\u0012\u0011#\n", (byte) (TextUtils.lastIndexOf("", '0', 0) + 70), objArr7);
            throw new WalletValidationException(walletValidationErrorCode4, intern2, ((String) objArr7[0]).intern());
        }
        c.d().e().a(this.e, this.b, str);
        int i4 = f70o + 99;
        h = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String m() {
        int i2 = f70o + 15;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.ei.c c = o.ei.c.c();
        switch (!c.q()) {
            case true:
                return null;
            default:
                String c2 = c.d().e().c(this.e, this.b);
                Set<String> keySet = c().keySet();
                if (c2 != null) {
                    int i4 = f70o + 25;
                    h = i4 % 128;
                    switch (i4 % 2 != 0 ? '0' : '+') {
                        case '+':
                            if (keySet.contains(c2)) {
                                int i5 = f70o + Opcodes.LSUB;
                                h = i5 % 128;
                                switch (i5 % 2 == 0) {
                                    case false:
                                        int i6 = 6 / 0;
                                        return c2;
                                    default:
                                        return c2;
                                }
                            }
                            break;
                        default:
                            keySet.contains(c2);
                            throw null;
                    }
                }
                return null;
        }
    }

    public final void n() throws WalletValidationException {
        g.c();
        Object[] objArr = new Object[1];
        p(Color.argb(0, 0, 0, 0) + 24, "\r\u001c\u001c\u001f\u000f\u0012\u0001\u0019\u0003\u0011\u0010\u000f\u0014\u000b\b\u0001\u0015#\u001f\u0007\u000f\u001e\u001b\u0010", (byte) (120 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        p(40 - TextUtils.getOffsetAfter("", 0), "\u0010\u001e\n\u001e\u0001\u000e\u0004\u0010\n\u0019\u0014\b\u0013!\u0017\u0001㙃㙃\u0011\u0014\t\b\u0005\u0014\u001f\u001b\u001c\u0012\u0019\u0000\u000f\u001e\u001b\u0010\u001d\u0012\u0006\u001d\u001e\u001a", (byte) (89 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.b).toString());
        o.ei.c c = o.ei.c.c();
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            p((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 6, "\u0001\u0006㙬㙬 \u0004", (byte) (View.MeasureSpec.getSize(0) + Opcodes.FNEG), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            p(ImageFormat.getBitsPerPixel(0) + 43, "\u0001\u0006㘻㘻 \u0004\u001d\u0012\f\u001e\u001b\u001f\u0000\u001a\u0010\u0018㘹㘹\u0013\u001d\u0002\u0015\u001a\u0006\u001f\u001b\u001c\u001f\u000e\b\u0015#\u001a\u0019\u0010\u001e\"\u0018\u0012\u0011#\n", (byte) (((byte) KeyEvent.getModifierMetaStateMask()) + 70), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        c.d().e().d(this.e, this.b);
        int i2 = h + 71;
        f70o = i2 % 128;
        switch (i2 % 2 == 0 ? ',' : '9') {
            case '9':
                return;
            default:
                int i3 = 9 / 0;
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final boolean g(java.lang.String r5) {
        /*
            r4 = this;
            java.util.LinkedHashSet<o.el.d> r0 = r4.a
            java.util.Iterator r0 = r0.iterator()
        L7:
            boolean r1 = r0.hasNext()
            r2 = 0
            r3 = 1
            if (r1 == 0) goto L12
            r1 = r2
            goto L13
        L12:
            r1 = r3
        L13:
            switch(r1) {
                case 1: goto L23;
                default: goto L16;
            }
        L16:
            int r1 = o.eo.d.f70o
            int r1 = r1 + 79
            int r2 = r1 % 128
            o.eo.d.h = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L24
            goto L24
        L23:
            return r2
        L24:
            java.lang.Object r1 = r0.next()
            o.el.d r1 = (o.el.d) r1
            java.lang.String r1 = r1.n()
            boolean r1 = r1.equals(r5)
            if (r1 == 0) goto L37
            r1 = 74
            goto L39
        L37:
            r1 = 26
        L39:
            switch(r1) {
                case 26: goto L3d;
                default: goto L3c;
            }
        L3c:
            return r3
        L3d:
            int r1 = o.eo.d.f70o
            int r1 = r1 + 99
            int r2 = r1 % 128
            o.eo.d.h = r2
            int r1 = r1 % 2
            goto L7
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.g(java.lang.String):boolean");
    }

    /* JADX WARN: Code restructure failed: missing block: B:112:0x01f0, code lost:
    
        if (r3.e == r3.a) goto L71;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(int r23, java.lang.String r24, byte r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 1134
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.d.p(int, java.lang.String, byte, java.lang.Object[]):void");
    }
}
