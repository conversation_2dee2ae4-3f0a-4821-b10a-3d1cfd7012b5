package com.esotericsoftware.asm;

import androidx.core.internal.view.SupportMenu;
import androidx.core.view.InputDeviceCompat;
import androidx.core.view.ViewCompat;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.io.IOException;
import java.io.InputStream;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\asm\ClassReader.smali */
public class ClassReader {
    public static final int EXPAND_FRAMES = 8;
    public static final int SKIP_CODE = 1;
    public static final int SKIP_DEBUG = 2;
    public static final int SKIP_FRAMES = 4;
    private final int[] a;
    public final byte[] b;
    private final String[] c;
    private final int d;
    public final int header;

    public ClassReader(InputStream inputStream) throws IOException {
        this(a(inputStream, false));
    }

    public ClassReader(String str) throws IOException {
        this(a(ClassLoader.getSystemResourceAsStream(new StringBuffer().append(str.replace('.', '/')).append(".class").toString()), true));
    }

    public ClassReader(byte[] bArr) {
        this(bArr, 0, bArr.length);
    }

    public ClassReader(byte[] bArr, int i, int i2) {
        this.b = bArr;
        if (readShort(i + 6) > 52) {
            throw new IllegalArgumentException();
        }
        int[] iArr = new int[readUnsignedShort(i + 8)];
        this.a = iArr;
        int length = iArr.length;
        this.c = new String[length];
        int i3 = i + 10;
        int i4 = 0;
        int i5 = 1;
        while (i5 < length) {
            int i6 = i3 + 1;
            this.a[i5] = i6;
            int i7 = 3;
            switch (bArr[i3]) {
                case 1:
                    i7 = 3 + readUnsignedShort(i6);
                    if (i7 <= i4) {
                        break;
                    } else {
                        i4 = i7;
                        break;
                    }
                case 3:
                case 4:
                case 9:
                case 10:
                case 11:
                case 12:
                case 18:
                    i7 = 5;
                    break;
                case 5:
                case 6:
                    i5++;
                    i7 = 9;
                    break;
                case 15:
                    i7 = 4;
                    break;
            }
            i3 += i7;
            i5++;
        }
        this.d = i4;
        this.header = i3;
    }

    private int a() {
        int i = this.header;
        int readUnsignedShort = i + 8 + (readUnsignedShort(i + 6) * 2);
        for (int readUnsignedShort2 = readUnsignedShort(readUnsignedShort); readUnsignedShort2 > 0; readUnsignedShort2--) {
            for (int readUnsignedShort3 = readUnsignedShort(readUnsignedShort + 8); readUnsignedShort3 > 0; readUnsignedShort3--) {
                readUnsignedShort += readInt(readUnsignedShort + 12) + 6;
            }
            readUnsignedShort += 8;
        }
        int i2 = readUnsignedShort + 2;
        for (int readUnsignedShort4 = readUnsignedShort(i2); readUnsignedShort4 > 0; readUnsignedShort4--) {
            for (int readUnsignedShort5 = readUnsignedShort(i2 + 8); readUnsignedShort5 > 0; readUnsignedShort5--) {
                i2 += readInt(i2 + 12) + 6;
            }
            i2 += 8;
        }
        return i2 + 2;
    }

    private int a(int i, boolean z, boolean z2, Context context) {
        int i2;
        int i3;
        char[] cArr = context.c;
        Label[] labelArr = context.h;
        if (z) {
            int i4 = i + 1;
            i3 = this.b[i] & 255;
            i2 = i4;
        } else {
            context.f12o = -1;
            i2 = i;
            i3 = 255;
        }
        int i5 = 0;
        context.r = 0;
        if (i3 < 64) {
            context.p = 3;
            context.t = 0;
        } else if (i3 < 128) {
            i3 -= 64;
            i2 = a(context.u, 0, i2, cArr, labelArr);
            context.p = 4;
            context.t = 1;
        } else {
            int readUnsignedShort = readUnsignedShort(i2);
            i2 += 2;
            if (i3 == 247) {
                i2 = a(context.u, 0, i2, cArr, labelArr);
                context.p = 4;
                context.t = 1;
            } else {
                if (i3 >= 248 && i3 < 251) {
                    context.p = 2;
                    context.r = 251 - i3;
                    context.q -= context.r;
                } else if (i3 != 251) {
                    if (i3 >= 255) {
                        context.p = 0;
                        int readUnsignedShort2 = readUnsignedShort(i2);
                        int i6 = i2 + 2;
                        context.r = readUnsignedShort2;
                        context.q = readUnsignedShort2;
                        int i7 = 0;
                        while (readUnsignedShort2 > 0) {
                            i6 = a(context.s, i7, i6, cArr, labelArr);
                            readUnsignedShort2--;
                            i7++;
                        }
                        int readUnsignedShort3 = readUnsignedShort(i6);
                        i2 = i6 + 2;
                        context.t = readUnsignedShort3;
                        while (true) {
                            int i8 = i5;
                            if (readUnsignedShort3 <= 0) {
                                break;
                            }
                            i5 = i8 + 1;
                            i2 = a(context.u, i8, i2, cArr, labelArr);
                            readUnsignedShort3--;
                        }
                    } else {
                        int i9 = i3 - 251;
                        int i10 = z2 ? context.q : 0;
                        int i11 = i9;
                        while (i11 > 0) {
                            i2 = a(context.s, i10, i2, cArr, labelArr);
                            i11--;
                            i10++;
                        }
                        context.p = 1;
                        context.r = i9;
                        context.q += context.r;
                    }
                } else {
                    context.p = 3;
                }
                context.t = 0;
            }
            i3 = readUnsignedShort;
        }
        context.f12o += i3 + 1;
        readLabel(context.f12o, labelArr);
        return i2;
    }

    private int a(int i, char[] cArr, String str, AnnotationVisitor annotationVisitor) {
        Object b;
        Object readConst;
        int i2 = 0;
        if (annotationVisitor == null) {
            switch (this.b[i] & 255) {
                case 64:
                    return a(i + 3, cArr, true, (AnnotationVisitor) null);
                case Opcodes.DUP_X2 /* 91 */:
                    return a(i + 1, cArr, false, (AnnotationVisitor) null);
                case Opcodes.LSUB /* 101 */:
                    return i + 5;
                default:
                    return i + 3;
            }
        }
        int i3 = i + 1;
        switch (this.b[i] & 255) {
            case 64:
                return a(i3 + 2, cArr, true, annotationVisitor.visitAnnotation(str, readUTF8(i3, cArr)));
            case 66:
                b = new Byte((byte) readInt(this.a[readUnsignedShort(i3)]));
                annotationVisitor.visit(str, b);
                return i3 + 2;
            case 67:
                b = new Character((char) readInt(this.a[readUnsignedShort(i3)]));
                annotationVisitor.visit(str, b);
                return i3 + 2;
            case 68:
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
            case 73:
            case 74:
                readConst = readConst(readUnsignedShort(i3), cArr);
                annotationVisitor.visit(str, readConst);
                return i3 + 2;
            case Opcodes.AASTORE /* 83 */:
                b = new Short((short) readInt(this.a[readUnsignedShort(i3)]));
                annotationVisitor.visit(str, b);
                return i3 + 2;
            case 90:
                readConst = readInt(this.a[readUnsignedShort(i3)]) == 0 ? Boolean.FALSE : Boolean.TRUE;
                annotationVisitor.visit(str, readConst);
                return i3 + 2;
            case Opcodes.DUP_X2 /* 91 */:
                int readUnsignedShort = readUnsignedShort(i3);
                int i4 = i3 + 2;
                if (readUnsignedShort == 0) {
                    return a(i4 - 2, cArr, false, annotationVisitor.visitArray(str));
                }
                int i5 = i4 + 1;
                switch (this.b[i4] & 255) {
                    case 66:
                        byte[] bArr = new byte[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            bArr[i2] = (byte) readInt(this.a[readUnsignedShort(i5)]);
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, bArr);
                        break;
                    case 67:
                        char[] cArr2 = new char[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            cArr2[i2] = (char) readInt(this.a[readUnsignedShort(i5)]);
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, cArr2);
                        break;
                    case 68:
                        double[] dArr = new double[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            dArr[i2] = Double.longBitsToDouble(readLong(this.a[readUnsignedShort(i5)]));
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, dArr);
                        break;
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        float[] fArr = new float[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            fArr[i2] = Float.intBitsToFloat(readInt(this.a[readUnsignedShort(i5)]));
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, fArr);
                        break;
                    case 73:
                        int[] iArr = new int[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            iArr[i2] = readInt(this.a[readUnsignedShort(i5)]);
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, iArr);
                        break;
                    case 74:
                        long[] jArr = new long[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            jArr[i2] = readLong(this.a[readUnsignedShort(i5)]);
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, jArr);
                        break;
                    case Opcodes.AASTORE /* 83 */:
                        short[] sArr = new short[readUnsignedShort];
                        while (i2 < readUnsignedShort) {
                            sArr[i2] = (short) readInt(this.a[readUnsignedShort(i5)]);
                            i5 += 3;
                            i2++;
                        }
                        annotationVisitor.visit(str, sArr);
                        break;
                    case 90:
                        boolean[] zArr = new boolean[readUnsignedShort];
                        for (int i6 = 0; i6 < readUnsignedShort; i6++) {
                            zArr[i6] = readInt(this.a[readUnsignedShort(i5)]) != 0;
                            i5 += 3;
                        }
                        annotationVisitor.visit(str, zArr);
                        break;
                    default:
                        return a(i5 - 3, cArr, false, annotationVisitor.visitArray(str));
                }
                return i5 - 1;
            case Opcodes.DADD /* 99 */:
                readConst = Type.getType(readUTF8(i3, cArr));
                annotationVisitor.visit(str, readConst);
                return i3 + 2;
            case Opcodes.LSUB /* 101 */:
                annotationVisitor.visitEnum(str, readUTF8(i3, cArr), readUTF8(i3 + 2, cArr));
                return i3 + 4;
            case Opcodes.DREM /* 115 */:
                readConst = readUTF8(i3, cArr);
                annotationVisitor.visit(str, readConst);
                return i3 + 2;
            default:
                return i3;
        }
    }

    private int a(int i, char[] cArr, boolean z, AnnotationVisitor annotationVisitor) {
        int readUnsignedShort = readUnsignedShort(i);
        int i2 = i + 2;
        if (z) {
            while (readUnsignedShort > 0) {
                i2 = a(i2 + 2, cArr, readUTF8(i2, cArr), annotationVisitor);
                readUnsignedShort--;
            }
        } else {
            while (readUnsignedShort > 0) {
                i2 = a(i2, cArr, (String) null, annotationVisitor);
                readUnsignedShort--;
            }
        }
        if (annotationVisitor != null) {
            annotationVisitor.visitEnd();
        }
        return i2;
    }

    private int a(ClassVisitor classVisitor, Context context, int i) {
        int i2;
        Context context2 = context;
        char[] cArr = context2.c;
        int readUnsignedShort = readUnsignedShort(i);
        String readUTF8 = readUTF8(i + 2, cArr);
        String readUTF82 = readUTF8(i + 4, cArr);
        int i3 = i + 6;
        int i4 = i3;
        int i5 = readUnsignedShort;
        int readUnsignedShort2 = readUnsignedShort(i3);
        int i6 = 0;
        int i7 = 0;
        int i8 = 0;
        int i9 = 0;
        Attribute attribute = null;
        String str = null;
        Object obj = null;
        while (readUnsignedShort2 > 0) {
            String readUTF83 = readUTF8(i4 + 2, cArr);
            if ("ConstantValue".equals(readUTF83)) {
                int readUnsignedShort3 = readUnsignedShort(i4 + 8);
                obj = readUnsignedShort3 == 0 ? null : readConst(readUnsignedShort3, cArr);
            } else if ("Signature".equals(readUTF83)) {
                str = readUTF8(i4 + 8, cArr);
            } else {
                if ("Deprecated".equals(readUTF83)) {
                    i2 = 131072;
                } else if ("Synthetic".equals(readUTF83)) {
                    i2 = 266240;
                } else if ("RuntimeVisibleAnnotations".equals(readUTF83)) {
                    i9 = i4 + 8;
                } else if ("RuntimeVisibleTypeAnnotations".equals(readUTF83)) {
                    i7 = i4 + 8;
                } else if ("RuntimeInvisibleAnnotations".equals(readUTF83)) {
                    i8 = i4 + 8;
                } else if ("RuntimeInvisibleTypeAnnotations".equals(readUTF83)) {
                    i6 = i4 + 8;
                } else {
                    Attribute attribute2 = attribute;
                    int i10 = i6;
                    int i11 = i7;
                    int i12 = i8;
                    int i13 = i9;
                    attribute = a(context2.a, readUTF83, i4 + 8, readInt(i4 + 4), cArr, -1, null);
                    if (attribute != null) {
                        attribute.a = attribute2;
                        i8 = i12;
                    } else {
                        i8 = i12;
                        attribute = attribute2;
                    }
                    i9 = i13;
                    i6 = i10;
                    i7 = i11;
                }
                i5 |= i2;
            }
            i4 += readInt(i4 + 4) + 6;
            readUnsignedShort2--;
            context2 = context;
        }
        Attribute attribute3 = attribute;
        int i14 = i6;
        int i15 = i7;
        int i16 = i8;
        int i17 = i9;
        int i18 = i4 + 2;
        FieldVisitor visitField = classVisitor.visitField(i5, readUTF8, readUTF82, str, obj);
        if (visitField == null) {
            return i18;
        }
        if (i17 != 0) {
            int i19 = i17 + 2;
            for (int readUnsignedShort4 = readUnsignedShort(i17); readUnsignedShort4 > 0; readUnsignedShort4--) {
                i19 = a(i19 + 2, cArr, true, visitField.visitAnnotation(readUTF8(i19, cArr), true));
            }
        }
        if (i16 != 0) {
            int i20 = i16 + 2;
            for (int readUnsignedShort5 = readUnsignedShort(i16); readUnsignedShort5 > 0; readUnsignedShort5--) {
                i20 = a(i20 + 2, cArr, true, visitField.visitAnnotation(readUTF8(i20, cArr), false));
            }
        }
        if (i15 != 0) {
            int i21 = i15 + 2;
            for (int readUnsignedShort6 = readUnsignedShort(i15); readUnsignedShort6 > 0; readUnsignedShort6--) {
                int a = a(context, i21);
                i21 = a(a + 2, cArr, true, visitField.visitTypeAnnotation(context.i, context.j, readUTF8(a, cArr), true));
            }
        }
        if (i14 != 0) {
            int i22 = i14 + 2;
            for (int readUnsignedShort7 = readUnsignedShort(i14); readUnsignedShort7 > 0; readUnsignedShort7--) {
                int a2 = a(context, i22);
                i22 = a(a2 + 2, cArr, true, visitField.visitTypeAnnotation(context.i, context.j, readUTF8(a2, cArr), false));
            }
        }
        while (attribute3 != null) {
            Attribute attribute4 = attribute3.a;
            attribute3.a = null;
            visitField.visitAttribute(attribute3);
            attribute3 = attribute4;
        }
        visitField.visitEnd();
        return i18;
    }

    private int a(Context context, int i) {
        int i2;
        int i3;
        int readInt = readInt(i);
        int i4 = readInt >>> 24;
        int i5 = ViewCompat.MEASURED_STATE_MASK;
        switch (i4) {
            case 0:
            case 1:
            case 22:
                i2 = readInt & SupportMenu.CATEGORY_MASK;
                i3 = i + 2;
                break;
            case 19:
            case 20:
            case 21:
                i2 = readInt & ViewCompat.MEASURED_STATE_MASK;
                i3 = i + 1;
                break;
            case 64:
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                i2 = readInt & ViewCompat.MEASURED_STATE_MASK;
                int readUnsignedShort = readUnsignedShort(i + 1);
                context.l = new Label[readUnsignedShort];
                context.m = new Label[readUnsignedShort];
                context.n = new int[readUnsignedShort];
                i3 = i + 3;
                for (int i6 = 0; i6 < readUnsignedShort; i6++) {
                    int readUnsignedShort2 = readUnsignedShort(i3);
                    int readUnsignedShort3 = readUnsignedShort(i3 + 2);
                    context.l[i6] = readLabel(readUnsignedShort2, context.h);
                    context.m[i6] = readLabel(readUnsignedShort2 + readUnsignedShort3, context.h);
                    context.n[i6] = readUnsignedShort(i3 + 4);
                    i3 += 6;
                }
                break;
            case 71:
            case 72:
            case 73:
            case 74:
            case 75:
                i2 = readInt & (-16776961);
                i3 = i + 4;
                break;
            default:
                if (i4 < 67) {
                    i5 = InputDeviceCompat.SOURCE_ANY;
                }
                i2 = readInt & i5;
                i3 = i + 3;
                break;
        }
        int readByte = readByte(i3);
        context.i = i2;
        context.j = readByte == 0 ? null : new TypePath(this.b, i3);
        return i3 + 1 + (readByte * 2);
    }

    private int a(Object[] objArr, int i, int i2, char[] cArr, Label[] labelArr) {
        int i3 = i2 + 1;
        switch (this.b[i2] & 255) {
            case 0:
                objArr[i] = Opcodes.TOP;
                return i3;
            case 1:
                objArr[i] = Opcodes.INTEGER;
                return i3;
            case 2:
                objArr[i] = Opcodes.FLOAT;
                return i3;
            case 3:
                objArr[i] = Opcodes.DOUBLE;
                return i3;
            case 4:
                objArr[i] = Opcodes.LONG;
                return i3;
            case 5:
                objArr[i] = Opcodes.NULL;
                return i3;
            case 6:
                objArr[i] = Opcodes.UNINITIALIZED_THIS;
                return i3;
            case 7:
                objArr[i] = readClass(i3, cArr);
                break;
            default:
                objArr[i] = readLabel(readUnsignedShort(i3), labelArr);
                break;
        }
        return i3 + 2;
    }

    private Attribute a(Attribute[] attributeArr, String str, int i, int i2, char[] cArr, int i3, Label[] labelArr) {
        for (int i4 = 0; i4 < attributeArr.length; i4++) {
            if (attributeArr[i4].type.equals(str)) {
                return attributeArr[i4].read(this, i, i2, cArr, i3, labelArr);
            }
        }
        return new Attribute(str).read(this, i, i2, null, -1, null);
    }

    private String a(int i, int i2, char[] cArr) {
        int i3;
        int i4 = i2 + i;
        byte[] bArr = this.b;
        int i5 = 0;
        char c = 0;
        char c2 = 0;
        while (i < i4) {
            int i6 = i + 1;
            byte b = bArr[i];
            switch (c) {
                case 0:
                    int i7 = b & 255;
                    if (i7 >= 128) {
                        if (i7 < 224 && i7 > 191) {
                            i3 = i7 & 31;
                            break;
                        } else {
                            c2 = (char) (i7 & 15);
                            c = 2;
                            break;
                        }
                    } else {
                        cArr[i5] = (char) i7;
                        i5++;
                        break;
                    }
                    break;
                case 1:
                    cArr[i5] = (char) ((b & 63) | (c2 << 6));
                    i5++;
                    c = 0;
                    continue;
                case 2:
                    i3 = (b & 63) | (c2 << 6);
                    break;
            }
            c2 = (char) i3;
            c = 1;
            i = i6;
        }
        return new String(cArr, 0, i5);
    }

    private void a(ClassWriter classWriter, Item[] itemArr, char[] cArr) {
        int a = a();
        for (int readUnsignedShort = readUnsignedShort(a); readUnsignedShort > 0; readUnsignedShort--) {
            if ("BootstrapMethods".equals(readUTF8(a + 2, cArr))) {
                int readUnsignedShort2 = readUnsignedShort(a + 8);
                int i = a + 10;
                int i2 = i;
                for (int i3 = 0; i3 < readUnsignedShort2; i3++) {
                    int i4 = (i2 - a) - 10;
                    int hashCode = readConst(readUnsignedShort(i2), cArr).hashCode();
                    for (int readUnsignedShort3 = readUnsignedShort(i2 + 2); readUnsignedShort3 > 0; readUnsignedShort3--) {
                        hashCode ^= readConst(readUnsignedShort(i2 + 4), cArr).hashCode();
                        i2 += 2;
                    }
                    i2 += 4;
                    Item item = new Item(i3);
                    item.a(i4, hashCode & Integer.MAX_VALUE);
                    int length = item.j % itemArr.length;
                    item.k = itemArr[length];
                    itemArr[length] = item;
                }
                int readInt = readInt(a + 4);
                ByteVector byteVector = new ByteVector(readInt + 62);
                byteVector.putByteArray(this.b, i, readInt - 2);
                classWriter.z = readUnsignedShort2;
                classWriter.A = byteVector;
                return;
            }
            a += readInt(a + 4) + 6;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:12:0x0034, code lost:
    
        r10.q = r4;
     */
    /* JADX WARN: Code restructure failed: missing block: B:13:0x0036, code lost:
    
        return;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void a(com.esotericsoftware.asm.Context r10) {
        /*
            r9 = this;
            java.lang.String r0 = r10.g
            java.lang.Object[] r1 = r10.s
            int r2 = r10.e
            r2 = r2 & 8
            r3 = 1
            r4 = 0
            if (r2 != 0) goto L28
            java.lang.String r2 = "<init>"
            java.lang.String r5 = r10.f
            boolean r2 = r2.equals(r5)
            if (r2 == 0) goto L1b
            java.lang.Integer r2 = com.esotericsoftware.asm.Opcodes.UNINITIALIZED_THIS
            r1[r4] = r2
            goto L27
        L1b:
            int r2 = r9.header
            int r2 = r2 + 2
            char[] r5 = r10.c
            java.lang.String r2 = r9.readClass(r2, r5)
            r1[r4] = r2
        L27:
            r4 = r3
        L28:
            r2 = r3
        L29:
            int r5 = r2 + 1
            char r6 = r0.charAt(r2)
            r7 = 59
            switch(r6) {
                case 66: goto L8b;
                case 67: goto L8b;
                case 68: goto L84;
                case 70: goto L7d;
                case 73: goto L8b;
                case 74: goto L76;
                case 76: goto L5f;
                case 83: goto L8b;
                case 90: goto L8b;
                case 91: goto L37;
                default: goto L34;
            }
        L34:
            r10.q = r4
            return
        L37:
            char r6 = r0.charAt(r5)
            r8 = 91
            if (r6 != r8) goto L42
            int r5 = r5 + 1
            goto L37
        L42:
            char r6 = r0.charAt(r5)
            r8 = 76
            if (r6 != r8) goto L53
        L4a:
            int r5 = r5 + 1
            char r6 = r0.charAt(r5)
            if (r6 == r7) goto L53
            goto L4a
        L53:
            int r6 = r4 + 1
            int r5 = r5 + r3
            java.lang.String r2 = r0.substring(r2, r5)
            r1[r4] = r2
            r2 = r5
            r4 = r6
            goto L29
        L5f:
            r2 = r5
        L60:
            char r6 = r0.charAt(r2)
            if (r6 == r7) goto L69
            int r2 = r2 + 1
            goto L60
        L69:
            int r6 = r4 + 1
            int r7 = r2 + 1
            java.lang.String r2 = r0.substring(r5, r2)
            r1[r4] = r2
            r4 = r6
            r2 = r7
            goto L29
        L76:
            int r2 = r4 + 1
            java.lang.Integer r6 = com.esotericsoftware.asm.Opcodes.LONG
            r1[r4] = r6
            goto L91
        L7d:
            int r2 = r4 + 1
            java.lang.Integer r6 = com.esotericsoftware.asm.Opcodes.FLOAT
            r1[r4] = r6
            goto L91
        L84:
            int r2 = r4 + 1
            java.lang.Integer r6 = com.esotericsoftware.asm.Opcodes.DOUBLE
            r1[r4] = r6
            goto L91
        L8b:
            int r2 = r4 + 1
            java.lang.Integer r6 = com.esotericsoftware.asm.Opcodes.INTEGER
            r1[r4] = r6
        L91:
            r4 = r2
            r2 = r5
            goto L29
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.ClassReader.a(com.esotericsoftware.asm.Context):void");
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:214:0x04ab  */
    /* JADX WARN: Removed duplicated region for block: B:222:0x0722  */
    /* JADX WARN: Removed duplicated region for block: B:242:0x0765  */
    /* JADX WARN: Removed duplicated region for block: B:253:0x0785  */
    /* JADX WARN: Removed duplicated region for block: B:260:0x04c6  */
    /* JADX WARN: Removed duplicated region for block: B:265:0x04fe  */
    /* JADX WARN: Removed duplicated region for block: B:271:0x053f  */
    /* JADX WARN: Removed duplicated region for block: B:276:0x057c  */
    /* JADX WARN: Removed duplicated region for block: B:278:0x058e  */
    /* JADX WARN: Removed duplicated region for block: B:279:0x05a0  */
    /* JADX WARN: Removed duplicated region for block: B:280:0x05b4  */
    /* JADX WARN: Removed duplicated region for block: B:282:0x05cb  */
    /* JADX WARN: Removed duplicated region for block: B:283:0x05eb  */
    /* JADX WARN: Removed duplicated region for block: B:288:0x0641  */
    /* JADX WARN: Removed duplicated region for block: B:300:0x0693  */
    /* JADX WARN: Removed duplicated region for block: B:301:0x06a6  */
    /* JADX WARN: Removed duplicated region for block: B:306:0x06ca  */
    /* JADX WARN: Removed duplicated region for block: B:308:0x06dd  */
    /* JADX WARN: Removed duplicated region for block: B:309:0x06f2  */
    /* JADX WARN: Removed duplicated region for block: B:310:0x0705  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private void a(com.esotericsoftware.asm.MethodVisitor r41, com.esotericsoftware.asm.Context r42, int r43) {
        /*
            Method dump skipped, instructions count: 2472
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.ClassReader.a(com.esotericsoftware.asm.MethodVisitor, com.esotericsoftware.asm.Context, int):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:26:0x0014, code lost:
    
        if (r2 >= r0.length) goto L10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0016, code lost:
    
        r3 = new byte[r2];
        java.lang.System.arraycopy(r0, 0, r3, 0, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:28:0x001b, code lost:
    
        r0 = r3;
     */
    /* JADX WARN: Code restructure failed: missing block: B:32:0x0021, code lost:
    
        return r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static byte[] a(java.io.InputStream r5, boolean r6) throws java.io.IOException {
        /*
            if (r5 == 0) goto L49
            int r0 = r5.available()     // Catch: java.lang.Throwable -> L42
            byte[] r0 = new byte[r0]     // Catch: java.lang.Throwable -> L42
            r1 = 0
            r2 = r1
        La:
            int r3 = r0.length     // Catch: java.lang.Throwable -> L42
            int r3 = r3 - r2
            int r3 = r5.read(r0, r2, r3)     // Catch: java.lang.Throwable -> L42
            r4 = -1
            if (r3 != r4) goto L22
            int r3 = r0.length     // Catch: java.lang.Throwable -> L42
            if (r2 >= r3) goto L1c
            byte[] r3 = new byte[r2]     // Catch: java.lang.Throwable -> L42
            java.lang.System.arraycopy(r0, r1, r3, r1, r2)     // Catch: java.lang.Throwable -> L42
            r0 = r3
        L1c:
            if (r6 == 0) goto L21
            r5.close()
        L21:
            return r0
        L22:
            int r2 = r2 + r3
            int r3 = r0.length     // Catch: java.lang.Throwable -> L42
            if (r2 != r3) goto La
            int r3 = r5.read()     // Catch: java.lang.Throwable -> L42
            if (r3 >= 0) goto L32
            if (r6 == 0) goto L31
            r5.close()
        L31:
            return r0
        L32:
            int r4 = r0.length     // Catch: java.lang.Throwable -> L42
            int r4 = r4 + 1000
            byte[] r4 = new byte[r4]     // Catch: java.lang.Throwable -> L42
            java.lang.System.arraycopy(r0, r1, r4, r1, r2)     // Catch: java.lang.Throwable -> L42
            int r0 = r2 + 1
            byte r3 = (byte) r3     // Catch: java.lang.Throwable -> L42
            r4[r2] = r3     // Catch: java.lang.Throwable -> L42
            r2 = r0
            r0 = r4
            goto La
        L42:
            r0 = move-exception
            if (r6 == 0) goto L48
            r5.close()
        L48:
            throw r0
        L49:
            java.io.IOException r5 = new java.io.IOException
            java.lang.String r6 = "Class not found"
            r5.<init>(r6)
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.ClassReader.a(java.io.InputStream, boolean):byte[]");
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    private int[] a(MethodVisitor methodVisitor, Context context, int i, boolean z) {
        int i2;
        char[] cArr = context.c;
        int readUnsignedShort = readUnsignedShort(i);
        int[] iArr = new int[readUnsignedShort];
        int i3 = i + 2;
        for (int i4 = 0; i4 < readUnsignedShort; i4++) {
            iArr[i4] = i3;
            int readInt = readInt(i3);
            int i5 = readInt >>> 24;
            switch (i5) {
                case 0:
                case 1:
                case 22:
                    i2 = i3 + 2;
                    break;
                case 19:
                case 20:
                case 21:
                    i2 = i3 + 1;
                    break;
                case 64:
                case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                    for (int readUnsignedShort2 = readUnsignedShort(i3 + 1); readUnsignedShort2 > 0; readUnsignedShort2--) {
                        int readUnsignedShort3 = readUnsignedShort(i3 + 3);
                        int readUnsignedShort4 = readUnsignedShort(i3 + 5);
                        readLabel(readUnsignedShort3, context.h);
                        readLabel(readUnsignedShort3 + readUnsignedShort4, context.h);
                        i3 += 6;
                    }
                    i2 = i3 + 3;
                    break;
                case 71:
                case 72:
                case 73:
                case 74:
                case 75:
                    i2 = i3 + 4;
                    break;
                default:
                    i2 = i3 + 3;
                    break;
            }
            int readByte = readByte(i2);
            if (i5 == 66) {
                TypePath typePath = readByte != 0 ? new TypePath(this.b, i2) : null;
                int i6 = i2 + (readByte * 2) + 1;
                i3 = a(i6 + 2, cArr, true, methodVisitor.visitTryCatchAnnotation(readInt, typePath, readUTF8(i6, cArr), z));
            } else {
                i3 = a(i2 + 3 + (readByte * 2), cArr, true, (AnnotationVisitor) null);
            }
        }
        return iArr;
    }

    /* JADX WARN: Code restructure failed: missing block: B:75:0x01b1, code lost:
    
        if (r1.j == 0) goto L80;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private int b(com.esotericsoftware.asm.ClassVisitor r32, com.esotericsoftware.asm.Context r33, int r34) {
        /*
            Method dump skipped, instructions count: 700
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.ClassReader.b(com.esotericsoftware.asm.ClassVisitor, com.esotericsoftware.asm.Context, int):int");
    }

    private void b(MethodVisitor methodVisitor, Context context, int i, boolean z) {
        int i2 = i + 1;
        int i3 = this.b[i] & 255;
        int length = Type.getArgumentTypes(context.g).length - i3;
        int i4 = 0;
        while (i4 < length) {
            AnnotationVisitor visitParameterAnnotation = methodVisitor.visitParameterAnnotation(i4, "Ljava/lang/Synthetic;", false);
            if (visitParameterAnnotation != null) {
                visitParameterAnnotation.visitEnd();
            }
            i4++;
        }
        char[] cArr = context.c;
        while (i4 < i3 + length) {
            i2 += 2;
            for (int readUnsignedShort = readUnsignedShort(i2); readUnsignedShort > 0; readUnsignedShort--) {
                i2 = a(i2 + 2, cArr, true, methodVisitor.visitParameterAnnotation(i4, readUTF8(i2, cArr), z));
            }
            i4++;
        }
    }

    void a(ClassWriter classWriter) {
        char[] cArr = new char[this.d];
        int length = this.a.length;
        Item[] itemArr = new Item[length];
        int i = 1;
        while (i < length) {
            int i2 = this.a[i];
            byte b = this.b[i2 - 1];
            Item item = new Item(i);
            switch (b) {
                case 1:
                    String[] strArr = this.c;
                    String str = strArr[i];
                    if (str == null) {
                        int i3 = this.a[i];
                        str = a(i3 + 2, readUnsignedShort(i3), cArr);
                        strArr[i] = str;
                    }
                    item.a(b, str, null, null);
                    continue;
                case 2:
                case 7:
                case 8:
                case 13:
                case 14:
                case 16:
                case 17:
                default:
                    item.a(b, readUTF8(i2, cArr), null, null);
                    continue;
                case 3:
                    item.a(readInt(i2));
                    continue;
                case 4:
                    item.a(Float.intBitsToFloat(readInt(i2)));
                    continue;
                case 5:
                    item.a(readLong(i2));
                    break;
                case 6:
                    item.a(Double.longBitsToDouble(readLong(i2)));
                    break;
                case 9:
                case 10:
                case 11:
                    int i4 = this.a[readUnsignedShort(i2 + 2)];
                    item.a(b, readClass(i2, cArr), readUTF8(i4, cArr), readUTF8(i4 + 2, cArr));
                    continue;
                case 12:
                    item.a(b, readUTF8(i2, cArr), readUTF8(i2 + 2, cArr), null);
                    continue;
                case 15:
                    int i5 = this.a[readUnsignedShort(i2 + 1)];
                    int i6 = this.a[readUnsignedShort(i5 + 2)];
                    item.a(readByte(i2) + 20, readClass(i5, cArr), readUTF8(i6, cArr), readUTF8(i6 + 2, cArr));
                    continue;
                case 18:
                    if (classWriter.A == null) {
                        a(classWriter, itemArr, cArr);
                    }
                    int i7 = this.a[readUnsignedShort(i2 + 2)];
                    item.a(readUTF8(i7, cArr), readUTF8(i7 + 2, cArr), readUnsignedShort(i2));
                    continue;
            }
            i++;
            int i8 = item.j % length;
            item.k = itemArr[i8];
            itemArr[i8] = item;
            i++;
        }
        int i9 = this.a[1] - 1;
        classWriter.d.putByteArray(this.b, i9, this.header - i9);
        classWriter.e = itemArr;
        classWriter.f = (int) (length * 0.75d);
        classWriter.c = length;
    }

    public void accept(ClassVisitor classVisitor, int i) {
        accept(classVisitor, new Attribute[0], i);
    }

    public void accept(ClassVisitor classVisitor, Attribute[] attributeArr, int i) {
        int i2;
        String str;
        String str2;
        String[] strArr;
        String str3;
        Attribute attribute;
        int i3;
        int i4 = this.header;
        char[] cArr = new char[this.d];
        Context context = new Context();
        context.a = attributeArr;
        context.b = i;
        context.c = cArr;
        int readUnsignedShort = readUnsignedShort(i4);
        String readClass = readClass(i4 + 2, cArr);
        String readClass2 = readClass(i4 + 4, cArr);
        int readUnsignedShort2 = readUnsignedShort(i4 + 6);
        String[] strArr2 = new String[readUnsignedShort2];
        int i5 = i4 + 8;
        for (int i6 = 0; i6 < readUnsignedShort2; i6++) {
            strArr2[i6] = readClass(i5, cArr);
            i5 += 2;
        }
        int a = a();
        int i7 = a;
        int i8 = readUnsignedShort;
        int readUnsignedShort3 = readUnsignedShort(a);
        int i9 = 0;
        String str4 = null;
        String str5 = null;
        String str6 = null;
        String str7 = null;
        String str8 = null;
        String str9 = null;
        int i10 = 0;
        int i11 = 0;
        int i12 = 0;
        int i13 = 0;
        Attribute attribute2 = null;
        while (readUnsignedShort3 > 0) {
            String readUTF8 = readUTF8(i7 + 2, cArr);
            if ("SourceFile".equals(readUTF8)) {
                str6 = readUTF8(i7 + 8, cArr);
            } else if ("InnerClasses".equals(readUTF8)) {
                i13 = i7 + 8;
            } else if ("EnclosingMethod".equals(readUTF8)) {
                String readClass3 = readClass(i7 + 8, cArr);
                int readUnsignedShort4 = readUnsignedShort(i7 + 10);
                if (readUnsignedShort4 != 0) {
                    str9 = readUTF8(this.a[readUnsignedShort4], cArr);
                    str4 = readUTF8(this.a[readUnsignedShort4] + 2, cArr);
                }
                str8 = readClass3;
            } else if ("Signature".equals(readUTF8)) {
                str7 = readUTF8(i7 + 8, cArr);
            } else if ("RuntimeVisibleAnnotations".equals(readUTF8)) {
                i9 = i7 + 8;
            } else if ("RuntimeVisibleTypeAnnotations".equals(readUTF8)) {
                i11 = i7 + 8;
            } else {
                if ("Deprecated".equals(readUTF8)) {
                    i3 = 131072;
                } else if ("Synthetic".equals(readUTF8)) {
                    i3 = 266240;
                } else if ("SourceDebugExtension".equals(readUTF8)) {
                    int readInt = readInt(i7 + 4);
                    str5 = a(i7 + 8, readInt, new char[readInt]);
                } else if ("RuntimeInvisibleAnnotations".equals(readUTF8)) {
                    i10 = i7 + 8;
                } else if ("RuntimeInvisibleTypeAnnotations".equals(readUTF8)) {
                    i12 = i7 + 8;
                } else {
                    if ("BootstrapMethods".equals(readUTF8)) {
                        int readUnsignedShort5 = readUnsignedShort(i7 + 8);
                        int[] iArr = new int[readUnsignedShort5];
                        int i14 = i7 + 10;
                        int i15 = 0;
                        while (i15 < readUnsignedShort5) {
                            iArr[i15] = i14;
                            i14 += (readUnsignedShort(i14 + 2) + 2) << 1;
                            i15++;
                            i9 = i9;
                        }
                        context.d = iArr;
                        str = str4;
                        str2 = str5;
                        str3 = str6;
                        strArr = strArr2;
                        attribute = attribute2;
                        i2 = i9;
                    } else {
                        i2 = i9;
                        str = str4;
                        str2 = str5;
                        strArr = strArr2;
                        str3 = str6;
                        Attribute a2 = a(attributeArr, readUTF8, i7 + 8, readInt(i7 + 4), cArr, -1, null);
                        attribute = attribute2;
                        if (a2 != null) {
                            a2.a = attribute;
                            attribute2 = a2;
                            str6 = str3;
                            i9 = i2;
                            str4 = str;
                            str5 = str2;
                            i7 += readInt(i7 + 4) + 6;
                            readUnsignedShort3--;
                            strArr2 = strArr;
                        }
                    }
                    attribute2 = attribute;
                    str6 = str3;
                    i9 = i2;
                    str4 = str;
                    str5 = str2;
                    i7 += readInt(i7 + 4) + 6;
                    readUnsignedShort3--;
                    strArr2 = strArr;
                }
                i8 |= i3;
            }
            strArr = strArr2;
            i7 += readInt(i7 + 4) + 6;
            readUnsignedShort3--;
            strArr2 = strArr;
        }
        int i16 = i9;
        String str10 = str4;
        String str11 = str5;
        String str12 = str6;
        Attribute attribute3 = attribute2;
        classVisitor.visit(readInt(this.a[1] - 7), i8, readClass, str7, readClass2, strArr2);
        if ((i & 2) == 0 && (str12 != null || str11 != null)) {
            classVisitor.visitSource(str12, str11);
        }
        String str13 = str8;
        if (str13 != null) {
            classVisitor.visitOuterClass(str13, str9, str10);
        }
        if (i16 != 0) {
            int i17 = i16 + 2;
            for (int readUnsignedShort6 = readUnsignedShort(i16); readUnsignedShort6 > 0; readUnsignedShort6--) {
                i17 = a(i17 + 2, cArr, true, classVisitor.visitAnnotation(readUTF8(i17, cArr), true));
            }
        }
        int i18 = i10;
        if (i18 != 0) {
            int i19 = i18 + 2;
            for (int readUnsignedShort7 = readUnsignedShort(i18); readUnsignedShort7 > 0; readUnsignedShort7--) {
                i19 = a(i19 + 2, cArr, true, classVisitor.visitAnnotation(readUTF8(i19, cArr), false));
            }
        }
        int i20 = i11;
        if (i20 != 0) {
            int i21 = i20 + 2;
            for (int readUnsignedShort8 = readUnsignedShort(i20); readUnsignedShort8 > 0; readUnsignedShort8--) {
                int a3 = a(context, i21);
                i21 = a(a3 + 2, cArr, true, classVisitor.visitTypeAnnotation(context.i, context.j, readUTF8(a3, cArr), true));
            }
        }
        int i22 = i12;
        if (i22 != 0) {
            int i23 = i22 + 2;
            for (int readUnsignedShort9 = readUnsignedShort(i22); readUnsignedShort9 > 0; readUnsignedShort9--) {
                int a4 = a(context, i23);
                i23 = a(a4 + 2, cArr, true, classVisitor.visitTypeAnnotation(context.i, context.j, readUTF8(a4, cArr), false));
            }
        }
        while (attribute3 != null) {
            Attribute attribute4 = attribute3.a;
            attribute3.a = null;
            classVisitor.visitAttribute(attribute3);
            attribute3 = attribute4;
        }
        int i24 = i13;
        if (i24 != 0) {
            int i25 = i24 + 2;
            for (int readUnsignedShort10 = readUnsignedShort(i24); readUnsignedShort10 > 0; readUnsignedShort10--) {
                classVisitor.visitInnerClass(readClass(i25, cArr), readClass(i25 + 2, cArr), readUTF8(i25 + 4, cArr), readUnsignedShort(i25 + 6));
                i25 += 8;
            }
        }
        int i26 = this.header + 10 + (readUnsignedShort2 * 2);
        for (int readUnsignedShort11 = readUnsignedShort(i26 - 2); readUnsignedShort11 > 0; readUnsignedShort11--) {
            i26 = a(classVisitor, context, i26);
        }
        int i27 = i26 + 2;
        for (int readUnsignedShort12 = readUnsignedShort(i27 - 2); readUnsignedShort12 > 0; readUnsignedShort12--) {
            i27 = b(classVisitor, context, i27);
        }
        classVisitor.visitEnd();
    }

    public int getAccess() {
        return readUnsignedShort(this.header);
    }

    public String getClassName() {
        return readClass(this.header + 2, new char[this.d]);
    }

    public String[] getInterfaces() {
        int i = this.header + 6;
        int readUnsignedShort = readUnsignedShort(i);
        String[] strArr = new String[readUnsignedShort];
        if (readUnsignedShort > 0) {
            char[] cArr = new char[this.d];
            for (int i2 = 0; i2 < readUnsignedShort; i2++) {
                i += 2;
                strArr[i2] = readClass(i, cArr);
            }
        }
        return strArr;
    }

    public int getItem(int i) {
        return this.a[i];
    }

    public int getItemCount() {
        return this.a.length;
    }

    public int getMaxStringLength() {
        return this.d;
    }

    public String getSuperName() {
        return readClass(this.header + 4, new char[this.d]);
    }

    public int readByte(int i) {
        return this.b[i] & 255;
    }

    public String readClass(int i, char[] cArr) {
        return readUTF8(this.a[readUnsignedShort(i)], cArr);
    }

    public Object readConst(int i, char[] cArr) {
        int i2 = this.a[i];
        switch (this.b[i2 - 1]) {
            case 3:
                return new Integer(readInt(i2));
            case 4:
                return new Float(Float.intBitsToFloat(readInt(i2)));
            case 5:
                return new Long(readLong(i2));
            case 6:
                return new Double(Double.longBitsToDouble(readLong(i2)));
            case 7:
                return Type.getObjectType(readUTF8(i2, cArr));
            case 8:
                return readUTF8(i2, cArr);
            case 16:
                return Type.getMethodType(readUTF8(i2, cArr));
            default:
                int readByte = readByte(i2);
                int[] iArr = this.a;
                int i3 = iArr[readUnsignedShort(i2 + 1)];
                boolean z = this.b[i3 + (-1)] == 11;
                String readClass = readClass(i3, cArr);
                int i4 = iArr[readUnsignedShort(i3 + 2)];
                return new Handle(readByte, readClass, readUTF8(i4, cArr), readUTF8(i4 + 2, cArr), z);
        }
    }

    public int readInt(int i) {
        byte[] bArr = this.b;
        return (bArr[i + 3] & 255) | ((bArr[i] & 255) << 24) | ((bArr[i + 1] & 255) << 16) | ((bArr[i + 2] & 255) << 8);
    }

    protected Label readLabel(int i, Label[] labelArr) {
        if (labelArr[i] == null) {
            labelArr[i] = new Label();
        }
        return labelArr[i];
    }

    public long readLong(int i) {
        return (readInt(i) << 32) | (readInt(i + 4) & 4294967295L);
    }

    public short readShort(int i) {
        byte[] bArr = this.b;
        return (short) ((bArr[i + 1] & 255) | ((bArr[i] & 255) << 8));
    }

    public String readUTF8(int i, char[] cArr) {
        int readUnsignedShort = readUnsignedShort(i);
        if (i == 0 || readUnsignedShort == 0) {
            return null;
        }
        String[] strArr = this.c;
        String str = strArr[readUnsignedShort];
        if (str != null) {
            return str;
        }
        int i2 = this.a[readUnsignedShort];
        String a = a(i2 + 2, readUnsignedShort(i2), cArr);
        strArr[readUnsignedShort] = a;
        return a;
    }

    public int readUnsignedShort(int i) {
        byte[] bArr = this.b;
        return (bArr[i + 1] & 255) | ((bArr[i] & 255) << 8);
    }
}
