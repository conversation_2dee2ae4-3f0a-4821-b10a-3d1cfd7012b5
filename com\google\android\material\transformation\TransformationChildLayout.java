package com.google.android.material.transformation;

import android.content.Context;
import android.util.AttributeSet;
import com.google.android.material.circularreveal.CircularRevealFrameLayout;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\material\transformation\TransformationChildLayout.smali */
public class TransformationChildLayout extends CircularRevealFrameLayout {
    public TransformationChildLayout(Context context) {
        this(context, null);
    }

    public TransformationChildLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
}
