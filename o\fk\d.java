package o.fk;

import com.esotericsoftware.asm.Opcodes;
import java.util.Collections;
import java.util.List;
import kotlin.text.Typography;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\fk\d.smali */
public final class d {
    private static int h = 0;
    private static int j = 1;
    private boolean a;
    private boolean b;
    private boolean c;
    private boolean d;
    private List<o.dr.a> e = Collections.emptyList();
    private e f;
    private b g;

    public final boolean d() {
        int i = j;
        int i2 = ((i | 31) << 1) - (i ^ 31);
        int i3 = i2 % 128;
        h = i3;
        int i4 = i2 % 2;
        boolean z = this.d;
        int i5 = i3 + Opcodes.DNEG;
        j = i5 % 128;
        int i6 = i5 % 2;
        return z;
    }

    public final void a(boolean z) {
        int i = h;
        int i2 = (i & 67) + (i | 67);
        int i3 = i2 % 128;
        j = i3;
        boolean z2 = i2 % 2 != 0;
        this.d = z;
        switch (z2) {
            case false:
                int i4 = 0 / 0;
                break;
        }
        int i5 = (i3 + Opcodes.FMUL) - 1;
        h = i5 % 128;
        switch (i5 % 2 != 0 ? '^' : '@') {
            case '@':
                return;
            default:
                int i6 = 67 / 0;
                return;
        }
    }

    public final boolean c() {
        int i = h;
        int i2 = (i ^ 75) + ((i & 75) << 1);
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        boolean z = this.c;
        int i5 = i3 + 1;
        h = i5 % 128;
        switch (i5 % 2 != 0 ? 'H' : '3') {
            case 'H':
                throw null;
            default:
                return z;
        }
    }

    public final void c(boolean z) {
        int i = j;
        int i2 = (i & 37) + (i | 37);
        h = i2 % 128;
        int i3 = i2 % 2;
        this.c = z;
        int i4 = ((i | 61) << 1) - (i ^ 61);
        h = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return;
            default:
                int i5 = 6 / 0;
                return;
        }
    }

    public final boolean b() {
        int i = j;
        int i2 = ((i | 97) << 1) - (i ^ 97);
        h = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.a;
        int i4 = ((i | 21) << 1) - (i ^ 21);
        h = i4 % 128;
        switch (i4 % 2 != 0 ? 'S' : '=') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                return z;
            default:
                throw null;
        }
    }

    public final void b(boolean z) {
        int i = h;
        int i2 = (i & 9) + (i | 9);
        int i3 = i2 % 128;
        j = i3;
        int i4 = i2 % 2;
        this.a = z;
        int i5 = (i3 + 2) - 1;
        h = i5 % 128;
        switch (i5 % 2 != 0 ? '/' : '1') {
            case '1':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final List<o.dr.a> e() {
        int i = j;
        int i2 = i + 89;
        h = i2 % 128;
        int i3 = i2 % 2;
        List<o.dr.a> list = this.e;
        int i4 = (i + 40) - 1;
        h = i4 % 128;
        int i5 = i4 % 2;
        return list;
    }

    public final void b(List<o.dr.a> list) {
        int i = (h + 50) - 1;
        int i2 = i % 128;
        j = i2;
        boolean z = i % 2 != 0;
        Object obj = null;
        this.e = list;
        switch (z) {
            case true:
                int i3 = (i2 ^ Opcodes.LNEG) + ((i2 & Opcodes.LNEG) << 1);
                h = i3 % 128;
                switch (i3 % 2 != 0 ? 'b' : '2') {
                    case Opcodes.FADD /* 98 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    public final boolean a() {
        int i = h + 17;
        j = i % 128;
        switch (i % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.b;
        }
    }

    public final void e(boolean z) {
        int i = h;
        int i2 = ((i | 109) << 1) - (i ^ 109);
        j = i2 % 128;
        char c = i2 % 2 == 0 ? '?' : (char) 6;
        this.b = z;
        switch (c) {
            case '?':
                int i3 = 33 / 0;
                return;
            default:
                return;
        }
    }

    public final e h() {
        int i = h + Opcodes.LSHR;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        e eVar = this.f;
        int i4 = ((i2 | 89) << 1) - (i2 ^ 89);
        h = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    public final void d(e eVar) {
        int i = h + 1;
        int i2 = i % 128;
        j = i2;
        int i3 = i % 2;
        this.f = eVar;
        int i4 = ((i2 | 37) << 1) - (i2 ^ 37);
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final b i() {
        int i = h + 25;
        j = i % 128;
        switch (i % 2 == 0 ? (char) 28 : Typography.amp) {
            case 28:
                throw null;
            default:
                return this.g;
        }
    }

    public final void d(b bVar) {
        int i = h;
        int i2 = (i + 6) - 1;
        j = i2 % 128;
        char c = i2 % 2 == 0 ? (char) 1 : 'E';
        Object obj = null;
        this.g = bVar;
        switch (c) {
            case 1:
                obj.hashCode();
                throw null;
            default:
                int i3 = (i + Opcodes.FMUL) - 1;
                j = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 19 : (char) 7) {
                    case 7:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }
}
