package kotlin;

/* compiled from: TypeAliases.kt */
@Metadata(d1 = {"\u0000f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000*\u001a\b\u0007\u0010\u0000\"\u00020\u00012\u00020\u0001B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004*\u001a\b\u0007\u0010\u0005\"\u00020\u00062\u00020\u0006B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\b\"\u00020\t2\u00020\tB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*,\b\u0007\u0010\n\u001a\u0004\b\u0000\u0010\u000b\"\b\u0012\u0004\u0012\u0002H\u000b0\f2\b\u0012\u0004\u0012\u0002H\u000b0\fB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\r\"\u00020\u000e2\u00020\u000eB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0004*\u001a\b\u0007\u0010\u000f\"\u00020\u00102\u00020\u0010B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u0011\"\u00020\u00122\u00020\u0012B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u0013\"\u00020\u00142\u00020\u0014B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u0015\"\u00020\u00162\u00020\u0016B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u0017\"\u00020\u00182\u00020\u0018B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u0019\"\u00020\u001a2\u00020\u001aB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u001b\"\u00020\u001c2\u00020\u001cB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u001d\"\u00020\u001e2\u00020\u001eB\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010\u001f\"\u00020 2\u00020 B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007*\u001a\b\u0007\u0010!\"\u00020\"2\u00020\"B\f\b\u0002\u0012\b\b\u0003\u0012\u0004\b\b(\u0007¨\u0006#"}, d2 = {"ArithmeticException", "Ljava/lang/ArithmeticException;", "Lkotlin/SinceKotlin;", "version", "1.3", "AssertionError", "Ljava/lang/AssertionError;", "1.1", "ClassCastException", "Ljava/lang/ClassCastException;", "Comparator", "T", "Ljava/util/Comparator;", "ConcurrentModificationException", "Ljava/util/ConcurrentModificationException;", "Error", "Ljava/lang/Error;", "Exception", "Ljava/lang/Exception;", "IllegalArgumentException", "Ljava/lang/IllegalArgumentException;", "IllegalStateException", "Ljava/lang/IllegalStateException;", "IndexOutOfBoundsException", "Ljava/lang/IndexOutOfBoundsException;", "NoSuchElementException", "Ljava/util/NoSuchElementException;", "NullPointerException", "Ljava/lang/NullPointerException;", "NumberFormatException", "Ljava/lang/NumberFormatException;", "RuntimeException", "Ljava/lang/RuntimeException;", "UnsupportedOperationException", "Ljava/lang/UnsupportedOperationException;", "kotlin-stdlib"}, k = 2, mv = {1, 9, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlin\TypeAliasesKt.smali */
public final class TypeAliasesKt {
    public static /* synthetic */ void ArithmeticException$annotations() {
    }

    public static /* synthetic */ void AssertionError$annotations() {
    }

    public static /* synthetic */ void ClassCastException$annotations() {
    }

    public static /* synthetic */ void Comparator$annotations() {
    }

    public static /* synthetic */ void ConcurrentModificationException$annotations() {
    }

    public static /* synthetic */ void Error$annotations() {
    }

    public static /* synthetic */ void Exception$annotations() {
    }

    public static /* synthetic */ void IllegalArgumentException$annotations() {
    }

    public static /* synthetic */ void IllegalStateException$annotations() {
    }

    public static /* synthetic */ void IndexOutOfBoundsException$annotations() {
    }

    public static /* synthetic */ void NoSuchElementException$annotations() {
    }

    public static /* synthetic */ void NullPointerException$annotations() {
    }

    public static /* synthetic */ void NumberFormatException$annotations() {
    }

    public static /* synthetic */ void RuntimeException$annotations() {
    }

    public static /* synthetic */ void UnsupportedOperationException$annotations() {
    }
}
