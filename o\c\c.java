package o.c;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.IBinder;
import android.os.Process;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\c\c.smali */
public abstract class c extends BroadcastReceiver {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final String TAG;
    private static int a;
    private static int c;
    private static int e;
    private static long lastUserPresentEvent;

    static void d() {
        e = 874635371;
    }

    private static void f(short s, int i, short s2, Object[] objArr) {
        byte[] bArr = $$a;
        int i2 = (s2 * 2) + Opcodes.DMUL;
        int i3 = i + 4;
        int i4 = (s * 4) + 1;
        byte[] bArr2 = new byte[i4];
        int i5 = -1;
        int i6 = i4 - 1;
        if (bArr == null) {
            i2 = i6 + i2;
            i6 = i6;
            i3 = i3;
        }
        while (true) {
            int i7 = i3 + 1;
            i5++;
            bArr2[i5] = (byte) i2;
            if (i5 == i6) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            i2 += bArr[i7];
            i6 = i6;
            i3 = i7;
        }
    }

    static void init$0() {
        $$a = new byte[]{78, -3, -72, 11};
        $$b = 248;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        c = 1;
        d();
        Object[] objArr = new Object[1];
        b(TextUtils.lastIndexOf("", '0') + 26, "\t\u0007\n\ufffb\u000e￭�\f\uffff\uffff\b\uffef\b\u0006\t�\u0005￬\uffff�\uffff\u0003\u0010\uffff\f￣\b\b\uffff\f\uffdd", TextUtils.indexOf((CharSequence) "", '0', 0) + 32, 122 - (Process.myTid() >> 22), false, objArr);
        TAG = ((String) objArr[0]).intern();
        lastUserPresentEvent = 0L;
        int i = c + 53;
        a = i % 128;
        switch (i % 2 != 0 ? '\\' : '9') {
            case Opcodes.DUP2 /* 92 */:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // android.content.BroadcastReceiver
    public final void onReceive(Context context, Intent intent) {
        switch (intent.getAction() == null) {
            case false:
                String action = intent.getAction();
                Object[] objArr = new Object[1];
                b((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 18, "\u0018\u0007\u0005ￒ\u0018\u0012\t\u0018\u0012\rￒ\b\r\u0013\u0016\b\u0012\u0005\ufff8\ufff2￩\ufff7￩\ufff6\ufff4\u0003\ufff6￩\ufff7\ufff9ￒ\u0012\u0013\r", 34 - Color.red(0), TextUtils.lastIndexOf("", '0') + Opcodes.LREM, true, objArr);
                switch (action.equals(((String) objArr[0]).intern()) ? (char) 4 : 'P') {
                    case 'P':
                        break;
                    default:
                        int i = a + Opcodes.LREM;
                        c = i % 128;
                        int i2 = i % 2;
                        g.c();
                        Object[] objArr2 = new Object[1];
                        b((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 25, "\t\u0007\n\ufffb\u000e￭�\f\uffff\uffff\b\uffef\b\u0006\t�\u0005￬\uffff�\uffff\u0003\u0010\uffff\f￣\b\b\uffff\f\uffdd", 31 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 122 - Color.argb(0, 0, 0, 0), false, objArr2);
                        String intern = ((String) objArr2[0]).intern();
                        Object[] objArr3 = new Object[1];
                        b(Process.getGidForName("") + 8, "\ufff1\uffd0\u0018\u0013$\u0011\ufff3\u0004\ufffe\ufff5\u0003\ufff5\u0002\u0000\u000f\u0002\ufff5\u0003\u0005\u000f\ufffe\uffff\ufff9\u0004\ufff3", 24 - TextUtils.indexOf((CharSequence) "", '0'), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 100, true, objArr3);
                        g.d(intern, ((String) objArr3[0]).intern());
                        lastUserPresentEvent = System.currentTimeMillis();
                        int i3 = c + Opcodes.LUSHR;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                        break;
                }
        }
    }

    public static long getLastUserPresentEvent() {
        int i = a + 83;
        int i2 = i % 128;
        c = i2;
        int i3 = i % 2;
        long j = lastUserPresentEvent;
        int i4 = i2 + 71;
        a = i4 % 128;
        switch (i4 % 2 != 0 ? '\f' : (char) 24) {
            case 24:
                return j;
            default:
                throw null;
        }
    }

    @Override // android.content.BroadcastReceiver
    public final IBinder peekService(Context context, Intent intent) {
        int i = a + 69;
        c = i % 128;
        char c2 = i % 2 == 0 ? (char) 16 : '7';
        IBinder peekService = super.peekService(context, intent);
        switch (c2) {
            case 16:
                int i2 = 13 / 0;
                break;
        }
        int i3 = c + 19;
        a = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return peekService;
        }
    }

    public int hashCode() {
        int i = c + Opcodes.LMUL;
        a = i % 128;
        switch (i % 2 != 0) {
            case false:
                int hashCode = super.hashCode();
                int i2 = c + Opcodes.LSHL;
                a = i2 % 128;
                int i3 = i2 % 2;
                return hashCode;
            default:
                super.hashCode();
                throw null;
        }
    }

    public final boolean equals(Object obj) {
        int i = a + Opcodes.LSHR;
        c = i % 128;
        int i2 = i % 2;
        boolean equals = super.equals(obj);
        int i3 = a + Opcodes.DMUL;
        c = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 19 : '7') {
            case '7':
                return equals;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    protected final Object clone() throws CloneNotSupportedException {
        throw new CloneNotSupportedException();
    }

    public final String toString() {
        int i = c + 35;
        a = i % 128;
        int i2 = i % 2;
        String obj = super.toString();
        int i3 = a + Opcodes.DSUB;
        c = i3 % 128;
        int i4 = i3 % 2;
        return obj;
    }

    protected final void finalize() throws Throwable {
        int i = a + Opcodes.LUSHR;
        c = i % 128;
        char c2 = i % 2 == 0 ? 'J' : 'R';
        super.finalize();
        switch (c2) {
            case 'J':
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void b(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 514
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.c.c.b(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
