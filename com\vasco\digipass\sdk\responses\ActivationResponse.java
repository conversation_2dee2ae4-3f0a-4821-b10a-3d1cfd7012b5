package com.vasco.digipass.sdk.responses;

import com.vasco.digipass.sdk.obfuscated.q;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\responses\ActivationResponse.smali */
public class ActivationResponse extends GenericResponse {
    private byte[] f;

    public ActivationResponse(int i) {
        super(i);
    }

    public byte[] getStaticVector() {
        return q.c(this.f);
    }

    public void setStaticVector(byte[] bArr) {
        this.f = q.c(bArr);
    }

    public ActivationResponse(int i, Throwable th) {
        super(i, th);
    }

    public ActivationResponse(int i, int i2, byte[] bArr, int i3) {
        super(i, i2, bArr, i3);
    }
}
