package androidx.core.view;

import android.os.CancellationSignal;
import android.view.animation.Interpolator;

/* JADX INFO: Access modifiers changed from: private */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\core\view\WindowInsetsControllerCompat$Impl.smali */
public class WindowInsetsControllerCompat$Impl {
    WindowInsetsControllerCompat$Impl() {
    }

    void show(int types) {
    }

    void hide(int types) {
    }

    void controlWindowInsetsAnimation(int types, long durationMillis, Interpolator interpolator, CancellationSignal cancellationSignal, WindowInsetsAnimationControlListenerCompat listener) {
    }

    void setSystemBarsBehavior(int behavior) {
    }

    int getSystemBarsBehavior() {
        return 0;
    }

    public boolean isAppearanceLightStatusBars() {
        return false;
    }

    public void setAppearanceLightStatusBars(boolean isLight) {
    }

    public boolean isAppearanceLightNavigationBars() {
        return false;
    }

    public void setAppearanceLightNavigationBars(boolean isLight) {
    }

    void addOnControllableInsetsChangedListener(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
    }

    void removeOnControllableInsetsChangedListener(WindowInsetsControllerCompat$OnControllableInsetsChangedListener listener) {
    }
}
