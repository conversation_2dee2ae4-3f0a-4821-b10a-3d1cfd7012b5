package com.vasco.digipass.sdk.utils.utilities.srp;

import bc.org.bouncycastle.crypto.Digest;
import bc.org.bouncycastle.crypto.digests.MD5Digest;
import bc.org.bouncycastle.crypto.digests.SHA1Digest;
import bc.org.bouncycastle.crypto.digests.SHA256Digest;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDK;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKCryptoResponse;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKException;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKReturnCodes;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.h7;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.u7;
import java.io.ByteArrayOutputStream;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\srp\SRP6VascoUtils.smali */
public final class SRP6VascoUtils extends g7 {
    private SRP6VascoUtils() {
    }

    static byte a(Digest digest) throws UtilitiesSDKException {
        if (digest instanceof MD5Digest) {
            return (byte) 1;
        }
        if (digest instanceof SHA1Digest) {
            return (byte) 2;
        }
        if (digest instanceof SHA256Digest) {
            return (byte) 3;
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID);
    }

    static Digest b(byte b) throws UtilitiesSDKException {
        a(b);
        return b != 1 ? b != 2 ? new SHA256Digest() : new SHA1Digest() : new MD5Digest();
    }

    static void c(BigInteger bigInteger) throws UtilitiesSDKException {
        if (bigInteger == null || BigInteger.ZERO.equals(bigInteger)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.MODULO_NULL_OR_EMPTY);
        }
    }

    public static UtilitiesSDKCryptoResponse calculateClientEvidenceMessage(byte b, BigInteger bigInteger, BigInteger bigInteger2, BigInteger bigInteger3, BigInteger bigInteger4, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        try {
            a(b);
            c(bigInteger);
            b(bigInteger2);
            a(bigInteger3, bigInteger);
            b(bigInteger4, bigInteger);
            c(bArr);
            b(bArr2);
            e(bArr3);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byteArrayOutputStream.write(a(a(b, bigInteger), a(b, bigInteger2)));
            byteArrayOutputStream.write(a(b, bArr2));
            byteArrayOutputStream.write(bArr3);
            byteArrayOutputStream.write(f1.a(bigInteger3));
            byteArrayOutputStream.write(f1.a(bigInteger4));
            byteArrayOutputStream.write(bArr);
            return new UtilitiesSDKCryptoResponse(0, a(b, byteArrayOutputStream.toByteArray()));
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static UtilitiesSDKCryptoResponse calculateServerEvidenceMessage(byte b, byte[] bArr, BigInteger bigInteger, byte[] bArr2) {
        try {
            a(b);
            a(bArr);
            a(bigInteger);
            c(bArr2);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byteArrayOutputStream.write(f1.a(bigInteger));
            byteArrayOutputStream.write(bArr);
            byteArrayOutputStream.write(bArr2);
            return new UtilitiesSDKCryptoResponse(0, a(b, byteArrayOutputStream.toByteArray()));
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    public static BigInteger calculateV(byte b, BigInteger bigInteger, BigInteger bigInteger2, byte[] bArr, byte[] bArr2, byte[] bArr3) throws UtilitiesSDKException {
        try {
            a(b);
            c(bigInteger);
            b(bigInteger2);
            e(bArr);
            b(bArr2);
            d(bArr3);
            h7 h7Var = new h7();
            h7Var.a(bigInteger, bigInteger2, b(b));
            return h7Var.a(bArr, bArr2, bArr3);
        } catch (UtilitiesSDKException e) {
            throw e;
        } catch (Exception e2) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    static void d(byte[] bArr) throws UtilitiesSDKException {
        if (u7.c(bArr)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.PASSWORD_NULL_OR_EMPTY);
        }
        if (bArr.length > 64 || bArr.length < 4) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.PASSWORD_INCORRECT_LENGTH);
        }
    }

    public static UtilitiesSDKCryptoResponse deriveKey(byte[] bArr, byte b) {
        try {
            c(bArr);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byteArrayOutputStream.write(new byte[]{1, 0});
            byteArrayOutputStream.write(b);
            byteArrayOutputStream.write(0);
            byteArrayOutputStream.write(new byte[]{0, 1});
            return UtilitiesSDK.hmac((byte) 3, byteArrayOutputStream.toByteArray(), bArr);
        } catch (UtilitiesSDKException e) {
            return new UtilitiesSDKCryptoResponse(e.getReturnErrorCode());
        } catch (Exception e2) {
            return new UtilitiesSDKCryptoResponse(UtilitiesSDKReturnCodes.UNKNOWN_ERROR);
        }
    }

    static void e(byte[] bArr) throws UtilitiesSDKException {
        if (u7.c(bArr)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SALT_NULL);
        }
        if (bArr.length != 16) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SALT_INCORRECT_LENGTH);
        }
    }

    static void c(byte[] bArr) throws UtilitiesSDKException {
        if (!u7.c(bArr)) {
            if (bArr.length != 32) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.KEY_INCORRECT_LENGTH);
            }
            return;
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.KEY_NULL);
    }

    static void a(byte b) throws UtilitiesSDKException {
        if (b != 1 && b != 2 && b != 3) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CRYPTO_MECANISM_INVALID);
        }
    }

    static void a(BigInteger bigInteger) throws UtilitiesSDKException {
        if (bigInteger != null) {
            if (!BigInteger.ZERO.equals(bigInteger)) {
                if (f1.a(bigInteger).length > 256) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_EPHEMERAL_KEY_INCORRECT_LENGTH);
                }
                return;
            }
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_EPHEMERAL_KEY_INVALID);
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_EPHEMERAL_KEY_NULL_OR_EMPTY);
    }

    static void b(BigInteger bigInteger) throws UtilitiesSDKException {
        if (bigInteger == null || BigInteger.ZERO.equals(bigInteger)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.GENERATOR_NULL_OR_EMPTY);
        }
    }

    static void a(BigInteger bigInteger, BigInteger bigInteger2) throws UtilitiesSDKException {
        a(bigInteger);
        if (bigInteger.mod(bigInteger2).equals(BigInteger.ZERO)) {
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_EPHEMERAL_KEY_INVALID);
        }
    }

    static void b(byte[] bArr) throws UtilitiesSDKException {
        if (!u7.c(bArr)) {
            if (bArr.length > 64 || bArr.length < 4) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.IDENTITY_INCORRECT_LENGTH);
            }
            return;
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.IDENTITY_NULL_OR_EMPTY);
    }

    static void a(byte[] bArr) throws UtilitiesSDKException {
        if (bArr != null) {
            if (bArr.length != 32) {
                throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_MESSAGE_INCORRECT_LENGTH);
            }
            return;
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.CLIENT_MESSAGE_NULL_OR_EMPTY);
    }

    private static byte[] a(byte b, byte[] bArr) throws UtilitiesSDKException {
        UtilitiesSDKCryptoResponse hash = UtilitiesSDK.hash(b, bArr);
        int returnCode = hash.getReturnCode();
        if (returnCode == 0) {
            return hash.getOutputData();
        }
        throw new UtilitiesSDKException(returnCode);
    }

    static void b(BigInteger bigInteger, BigInteger bigInteger2) throws UtilitiesSDKException {
        if (bigInteger != null) {
            if (f1.a(bigInteger).length <= 256) {
                BigInteger bigInteger3 = BigInteger.ZERO;
                if (bigInteger3.equals(bigInteger) || bigInteger.mod(bigInteger2).equals(bigInteger3)) {
                    throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_EPHEMERAL_KEY_INVALID);
                }
                return;
            }
            throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_EPHEMERAL_KEY_INCORRECT_LENGTH);
        }
        throw new UtilitiesSDKException(UtilitiesSDKReturnCodes.SERVER_EPHEMERAL_KEY_NULL_OR_EMPTY);
    }

    private static byte[] a(Digest digest, byte[] bArr) throws UtilitiesSDKException {
        return a(a(digest), bArr);
    }

    private static byte[] a(byte b, BigInteger bigInteger) throws UtilitiesSDKException {
        return a(b, f1.a(bigInteger));
    }

    static byte[] a(Digest digest, BigInteger bigInteger) throws UtilitiesSDKException {
        return a(digest, f1.a(bigInteger));
    }

    private static byte[] a(byte[] bArr, byte[] bArr2) {
        int min = Math.min(bArr.length, bArr2.length);
        byte[] bArr3 = new byte[min];
        for (int i = 0; i < min; i++) {
            bArr3[i] = (byte) (bArr[i] ^ bArr2[i]);
        }
        return bArr3;
    }
}
