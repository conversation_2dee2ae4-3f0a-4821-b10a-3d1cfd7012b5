package o.ev;

import android.content.Context;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Date;
import kotlinx.coroutines.internal.LockFreeTaskQueueCore;
import o.a.h;
import o.ct.b;
import o.ee.g;
import o.eg.d;
import o.ey.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ev\c.smali */
public final class c extends e<o.fd.a> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static int[] d;
    private static int e;
    private int b;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        a = 0;
        e = 1;
        t();
        ViewConfiguration.getGlobalActionKeyTimeout();
        int i = a + 89;
        e = i % 128;
        switch (i % 2 == 0 ? '=' : '\r') {
            case LockFreeTaskQueueCore.CLOSED_SHIFT /* 61 */:
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$d = new byte[]{29, -34, -102, -75};
        $$e = 43;
    }

    static void t() {
        d = new int[]{1786481052, -1311252963, -502224585, 1620165846, 857696632, 986156235, -1546124836, -449066863, -995578092, -1187190244, -964672682, 1700982570, -834569743, -1112029887, 509183774, -214593186, -223019046, 891705886};
        c = 874635503;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void y(byte r6, short r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 + 4
            int r7 = r7 * 3
            int r7 = 1 - r7
            byte[] r0 = o.ev.c.$$d
            int r8 = 116 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L18
            r8 = r6
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            goto L38
        L18:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1d:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r8) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            int r3 = r3 + 1
            int r7 = r7 + 1
            r4 = r0[r7]
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L38:
            int r7 = -r7
            int r6 = r6 + r7
            r7 = r8
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ev.c.y(byte, short, int, java.lang.Object[]):void");
    }

    @Override // o.ey.e
    public final void b() {
        int i = e + 13;
        a = i % 128;
        int i2 = i % 2;
    }

    @Override // o.ey.e
    public final /* synthetic */ boolean a(Context context, o.fd.a aVar) {
        boolean e2;
        int i = a + 77;
        e = i % 128;
        o.fd.a aVar2 = aVar;
        switch (i % 2 == 0 ? '\f' : (char) 21) {
            case '\f':
                e2 = e(aVar2);
                int i2 = 42 / 0;
                break;
            default:
                e2 = e(aVar2);
                break;
        }
        int i3 = e + 93;
        a = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    @Override // o.ey.e
    public final /* synthetic */ b<o.fd.a> c() {
        int i = e + Opcodes.LSHL;
        a = i % 128;
        int i2 = i % 2;
        o.cv.b u = u();
        int i3 = a + 21;
        e = i3 % 128;
        int i4 = i3 % 2;
        return u;
    }

    @Override // o.ey.e
    public final /* synthetic */ o.ey.a e() {
        int i = e + 33;
        a = i % 128;
        int i2 = i % 2;
        a p = p();
        int i3 = a + Opcodes.DSUB;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? '-' : 'S') {
            case Opcodes.AASTORE /* 83 */:
                return p;
            default:
                throw null;
        }
    }

    public c(String str, String str2, boolean z) {
        super(str, str2, z);
        this.b = 1;
    }

    final int r() {
        int i = e + 89;
        int i2 = i % 128;
        a = i2;
        int i3 = i % 2;
        int i4 = this.b;
        int i5 = i2 + 31;
        e = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                int i6 = 10 / 0;
                return i4;
            default:
                return i4;
        }
    }

    final void b(int i) {
        int i2 = a;
        int i3 = i2 + 11;
        e = i3 % 128;
        int i4 = i3 % 2;
        this.b = i;
        int i5 = i2 + Opcodes.LMUL;
        e = i5 % 128;
        int i6 = i5 % 2;
    }

    private static a p() {
        a aVar = new a();
        int i = e + 35;
        a = i % 128;
        switch (i % 2 != 0 ? 'M' : '@') {
            case 'M':
                throw null;
            default:
                return aVar;
        }
    }

    private static o.cv.b u() {
        o.cv.b bVar = new o.cv.b();
        int i = e + 31;
        a = i % 128;
        switch (i % 2 != 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return bVar;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:21:0x016c, code lost:
    
        r0 = true;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private boolean e(o.fd.a r13) {
        /*
            Method dump skipped, instructions count: 724
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ev.c.e(o.fd.a):boolean");
    }

    @Override // o.ey.e
    public final o.eg.b b(o.ek.b bVar) throws d {
        o.eg.b bVar2 = new o.eg.b();
        Object[] objArr = new Object[1];
        x(9 - KeyEvent.getDeadChar(0, 0), "\u0005\u0002\u000b\u000e￬\u0000\u000e�\uffff\u0000￥\u0001\b", 13 - TextUtils.indexOf("", "", 0, 0), Gravity.getAbsoluteGravity(0, 0) + 244, true, objArr);
        bVar2.d(((String) objArr[0]).intern(), e(bVar));
        Object[] objArr2 = new Object[1];
        x((ViewConfiguration.getJumpTapTimeout() >> 16) + 1, "\u0003\ufffe", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 1, View.getDefaultSize(0, 0) + 246, true, objArr2);
        bVar2.d(((String) objArr2[0]).intern(), d());
        switch (f() != null ? 'C' : '0') {
            case '0':
                break;
            default:
                if (j() != null) {
                    o.fd.a aVar = f().get(0);
                    o.eg.e eVar = null;
                    if (aVar != null) {
                        int i = e + 87;
                        a = i % 128;
                        switch (i % 2 == 0) {
                            case true:
                                eVar = aVar.c(this.b);
                                int i2 = a + 67;
                                e = i2 % 128;
                                switch (i2 % 2 == 0 ? '+' : '!') {
                                }
                            default:
                                aVar.c(this.b);
                                eVar.hashCode();
                                throw null;
                        }
                    }
                    Object[] objArr3 = new Object[1];
                    v(new int[]{897269825, 1717258650, -763649049, -482621870, 1137827759, 1182419578}, View.MeasureSpec.getSize(0) + 11, objArr3);
                    bVar2.d(((String) objArr3[0]).intern(), eVar);
                    Object[] objArr4 = new Object[1];
                    v(new int[]{897269825, 1717258650, -763649049, -482621870, 734613211, -1970110844, 732130912, -1252592680}, 17 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr4);
                    bVar2.d(((String) objArr4[0]).intern(), j().e());
                    Date d2 = j().d();
                    if (d2 != null) {
                        Object[] objArr5 = new Object[1];
                        x(5 - (ViewConfiguration.getJumpTapTimeout() >> 16), "\t\u0000\u0007\u000f￼￼\u000b\ufff8ￛ\u0010", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 10, TextUtils.getOffsetAfter("", 0) + 249, true, objArr5);
                        bVar2.d(((String) objArr5[0]).intern(), d2.getTime() / 1000);
                        int i3 = e + 93;
                        a = i3 % 128;
                        int i4 = i3 % 2;
                        break;
                    }
                }
                break;
        }
        g.c();
        Object[] objArr6 = new Object[1];
        v(new int[]{1743088219, -755596394, 928638749, 2134065163, 108666995, -1120860915, 706221127, -1584346513, -1177754681, -631212566, 952826015, -806867813}, TextUtils.indexOf("", "", 0) + 22, objArr6);
        String intern = ((String) objArr6[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr7 = new Object[1];
        x(View.MeasureSpec.makeMeasureSpec(0, 0) + 9, "\u0018\u0005\u0018\ufff7\u0018\u0012\r\u0016\u0014ￄ\uffdeￄ\u0018\u0019\u0014\u0018\u0019\u0013ￄ\uffd1ￄ\u0017\u0019", 23 - TextUtils.getOffsetAfter("", 0), TextUtils.indexOf("", "", 0, 0) + 236, true, objArr7);
        g.d(intern, sb.append(((String) objArr7[0]).intern()).append(bVar2.b()).toString());
        return bVar2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:7:0x001f, code lost:
    
        if (f() != null) goto L20;
     */
    @Override // o.ey.e
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final o.fc.e k() {
        /*
            Method dump skipped, instructions count: 670
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ev.c.k():o.fc.e");
    }

    private static void v(int[] iArr, int i, Object[] objArr) {
        Object method;
        int[] iArr2;
        int i2;
        o.a.g gVar = new o.a.g();
        char[] cArr = new char[4];
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = d;
        long j = 0;
        int i3 = -1667374059;
        int i4 = 1;
        int i5 = 0;
        if (iArr3 != null) {
            int length = iArr3.length;
            int[] iArr4 = new int[length];
            int i6 = 0;
            while (true) {
                switch (i6 < length ? 'H' : '@') {
                    case '@':
                        iArr3 = iArr4;
                        break;
                    default:
                        try {
                            Object[] objArr2 = new Object[i4];
                            objArr2[i5] = Integer.valueOf(iArr3[i6]);
                            Object obj = o.e.a.s.get(Integer.valueOf(i3));
                            if (obj == null) {
                                Class cls = (Class) o.e.a.c(ExpandableListView.getPackedPositionChild(j) + 11, (char) (8855 - TextUtils.lastIndexOf("", '0', i5)), 325 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)));
                                byte b = (byte) (-1);
                                byte b2 = (byte) (b + 1);
                                Object[] objArr3 = new Object[1];
                                y(b, b2, b2, objArr3);
                                obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                                o.e.a.s.put(-1667374059, obj);
                            }
                            iArr4[i6] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                            i6++;
                            j = 0;
                            i3 = -1667374059;
                            i4 = 1;
                            i5 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                }
            }
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = d;
        int i7 = 3;
        switch (iArr6 == null) {
            case true:
                break;
            default:
                int i8 = $11 + Opcodes.DDIV;
                $10 = i8 % 128;
                int i9 = i8 % 2;
                int length3 = iArr6.length;
                int[] iArr7 = new int[length3];
                int i10 = 0;
                while (i10 < length3) {
                    int i11 = $11 + i7;
                    $10 = i11 % 128;
                    int i12 = i11 % 2;
                    try {
                        Object[] objArr4 = {Integer.valueOf(iArr6[i10])};
                        Object obj2 = o.e.a.s.get(-1667374059);
                        if (obj2 != null) {
                            iArr2 = iArr6;
                            i2 = length3;
                        } else {
                            Class cls2 = (Class) o.e.a.c((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 9, (char) ((ViewConfiguration.getScrollBarSize() >> 8) + 8856), 324 - Drawable.resolveOpacity(0, 0));
                            byte b3 = (byte) (-1);
                            byte b4 = (byte) (b3 + 1);
                            iArr2 = iArr6;
                            i2 = length3;
                            Object[] objArr5 = new Object[1];
                            y(b3, b4, b4, objArr5);
                            obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                            o.e.a.s.put(-1667374059, obj2);
                        }
                        iArr7[i10] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                        i10++;
                        iArr6 = iArr2;
                        length3 = i2;
                        i7 = 3;
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                iArr6 = iArr7;
                break;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        int i13 = $11 + 45;
        $10 = i13 % 128;
        int i14 = i13 % 2;
        while (gVar.a < iArr.length) {
            int i15 = $11 + 9;
            $10 = i15 % 128;
            int i16 = i15 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            o.a.g.d(iArr5);
            for (int i17 = 0; i17 < 16; i17++) {
                int i18 = $11 + 49;
                $10 = i18 % 128;
                int i19 = i18 % 2;
                gVar.e ^= iArr5[i17];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(o.a.g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c(11 - (ViewConfiguration.getWindowTouchSlop() >> 8), (char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (ViewConfiguration.getScrollBarSize() >> 8) + 572)).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i20 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i20;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i21 = gVar.e;
            int i22 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            o.a.g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 != null) {
                    method = obj4;
                } else {
                    Class cls3 = (Class) o.e.a.c(11 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (char) (KeyEvent.normalizeMetaState(0) + 55183), TextUtils.indexOf("", "", 0, 0) + 515);
                    byte b5 = (byte) (-1);
                    byte b6 = (byte) (b5 + 1);
                    Object[] objArr8 = new Object[1];
                    y(b5, b6, (byte) (b6 + 1), objArr8);
                    method = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, method);
                }
                ((Method) method).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i);
    }

    private static void x(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
        char[] charArray;
        int i4 = $10 + Opcodes.DNEG;
        $11 = i4 % 128;
        switch (i4 % 2 == 0 ? '`' : (char) 24) {
            case Opcodes.IADD /* 96 */:
                throw null;
            default:
                switch (str != null) {
                    case false:
                        charArray = str;
                        break;
                    default:
                        charArray = str.toCharArray();
                        break;
                }
                char[] cArr = charArray;
                h hVar = new h();
                char[] cArr2 = new char[i2];
                hVar.a = 0;
                while (hVar.a < i2) {
                    int i5 = $10 + 41;
                    $11 = i5 % 128;
                    int i6 = i5 % 2;
                    hVar.b = cArr[hVar.a];
                    cArr2[hVar.a] = (char) (i3 + hVar.b);
                    int i7 = hVar.a;
                    try {
                        Object[] objArr2 = {Integer.valueOf(cArr2[i7]), Integer.valueOf(c)};
                        Object obj = o.e.a.s.get(2038615114);
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(12 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (char) View.resolveSize(0, 0), 459 - (Process.myTid() >> 22));
                            byte b = (byte) (-1);
                            byte b2 = (byte) (b + 1);
                            Object[] objArr3 = new Object[1];
                            y(b, b2, (byte) (b2 | 9), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(2038615114, obj);
                        }
                        cArr2[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        try {
                            Object[] objArr4 = {hVar, hVar};
                            Object obj2 = o.e.a.s.get(-1412673904);
                            if (obj2 == null) {
                                Class cls2 = (Class) o.e.a.c(KeyEvent.normalizeMetaState(0) + 11, (char) (ViewConfiguration.getTapTimeout() >> 16), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 312);
                                byte b3 = (byte) (-1);
                                byte b4 = (byte) (b3 + 1);
                                Object[] objArr5 = new Object[1];
                                y(b3, b4, (byte) (b4 | 7), objArr5);
                                obj2 = cls2.getMethod((String) objArr5[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj2);
                            }
                            ((Method) obj2).invoke(null, objArr4);
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                }
                switch (i > 0) {
                    case true:
                        hVar.c = i;
                        char[] cArr3 = new char[i2];
                        System.arraycopy(cArr2, 0, cArr3, 0, i2);
                        System.arraycopy(cArr3, 0, cArr2, i2 - hVar.c, hVar.c);
                        System.arraycopy(cArr3, hVar.c, cArr2, 0, i2 - hVar.c);
                        break;
                }
                if (z) {
                    int i8 = $11 + Opcodes.LUSHR;
                    $10 = i8 % 128;
                    int i9 = i8 % 2;
                    char[] cArr4 = new char[i2];
                    hVar.a = 0;
                    while (hVar.a < i2) {
                        int i10 = $10 + 53;
                        $11 = i10 % 128;
                        int i11 = i10 % 2;
                        cArr4[hVar.a] = cArr2[(i2 - hVar.a) - 1];
                        try {
                            Object[] objArr6 = {hVar, hVar};
                            Object obj3 = o.e.a.s.get(-1412673904);
                            if (obj3 == null) {
                                Class cls3 = (Class) o.e.a.c(12 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), (char) (ViewConfiguration.getFadingEdgeLength() >> 16), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 312);
                                byte b5 = (byte) (-1);
                                byte b6 = (byte) (b5 + 1);
                                Object[] objArr7 = new Object[1];
                                y(b5, b6, (byte) (b6 | 7), objArr7);
                                obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                                o.e.a.s.put(-1412673904, obj3);
                            }
                            ((Method) obj3).invoke(null, objArr6);
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
                    cArr2 = cArr4;
                }
                objArr[0] = new String(cArr2);
                return;
        }
    }
}
