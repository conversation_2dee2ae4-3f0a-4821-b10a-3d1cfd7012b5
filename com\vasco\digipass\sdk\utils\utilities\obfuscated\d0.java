package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import kotlin.jvm.internal.ByteCompanionObject;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d0.smali */
public class d0 extends b0 {
    static final o0 C = new a(d0.class, 13);
    private final String b;
    private byte[] x;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\d0$a.smali */
    class a extends o0 {
        a(Class cls, int i) {
            super(cls, i);
        }

        @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.o0
        b0 a(f2 f2Var) {
            return d0.a(f2Var.h(), false);
        }
    }

    private d0(byte[] bArr, boolean z) {
        byte[] bArr2 = bArr;
        StringBuffer stringBuffer = new StringBuffer();
        boolean z2 = true;
        BigInteger bigInteger = null;
        long j = 0;
        for (int i = 0; i != bArr2.length; i++) {
            int i2 = bArr2[i] & 255;
            if (j <= 72057594037927808L) {
                long j2 = j + (i2 & 127);
                if ((i2 & 128) == 0) {
                    if (z2) {
                        z2 = false;
                    } else {
                        stringBuffer.append('.');
                    }
                    stringBuffer.append(j2);
                    j = 0;
                } else {
                    j = j2 << 7;
                }
            } else {
                BigInteger or = (bigInteger == null ? BigInteger.valueOf(j) : bigInteger).or(BigInteger.valueOf(i2 & 127));
                if ((i2 & 128) == 0) {
                    if (z2) {
                        z2 = false;
                    } else {
                        stringBuffer.append('.');
                    }
                    stringBuffer.append(or);
                    bigInteger = null;
                    j = 0;
                } else {
                    bigInteger = or.shiftLeft(7);
                }
            }
        }
        this.b = stringBuffer.toString();
        this.x = z ? Arrays.clone(bArr) : bArr2;
    }

    private synchronized byte[] h() {
        if (this.x == null) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            a(byteArrayOutputStream);
            this.x = byteArrayOutputStream.toByteArray();
        }
        return this.x;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean a(b0 b0Var) {
        if (this == b0Var) {
            return true;
        }
        if (b0Var instanceof d0) {
            return this.b.equals(((d0) b0Var).b);
        }
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    boolean e() {
        return false;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return this.b.hashCode();
    }

    public String i() {
        return this.b;
    }

    public String toString() {
        return i();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    int a(boolean z) {
        return z.a(z, h().length);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    void a(z zVar, boolean z) throws IOException {
        zVar.a(z, 13, h());
    }

    private void a(ByteArrayOutputStream byteArrayOutputStream) {
        d6 d6Var = new d6(this.b);
        while (d6Var.a()) {
            String b = d6Var.b();
            if (b.length() <= 18) {
                a(byteArrayOutputStream, Long.parseLong(b));
            } else {
                a(byteArrayOutputStream, new BigInteger(b));
            }
        }
    }

    static d0 a(byte[] bArr, boolean z) {
        return new d0(bArr, z);
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x002e, code lost:
    
        return false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    static boolean a(java.lang.String r7, int r8) {
        /*
            int r0 = r7.length()
            r1 = 0
            r2 = r1
        L6:
            int r0 = r0 + (-1)
            r3 = 48
            r4 = 1
            if (r0 < r8) goto L2f
            char r5 = r7.charAt(r0)
            r6 = 46
            if (r5 != r6) goto L25
            if (r2 == 0) goto L24
            if (r2 <= r4) goto L22
            int r2 = r0 + 1
            char r2 = r7.charAt(r2)
            if (r2 != r3) goto L22
            goto L24
        L22:
            r2 = r1
            goto L6
        L24:
            return r1
        L25:
            if (r3 > r5) goto L2e
            r3 = 57
            if (r5 > r3) goto L2e
            int r2 = r2 + 1
            goto L6
        L2e:
            return r1
        L2f:
            if (r2 == 0) goto L3c
            if (r2 <= r4) goto L3b
            int r0 = r0 + r4
            char r7 = r7.charAt(r0)
            if (r7 != r3) goto L3b
            goto L3c
        L3b:
            return r4
        L3c:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: com.vasco.digipass.sdk.utils.utilities.obfuscated.d0.a(java.lang.String, int):boolean");
    }

    static void a(ByteArrayOutputStream byteArrayOutputStream, long j) {
        byte[] bArr = new byte[9];
        int i = 8;
        bArr[8] = (byte) (((int) j) & 127);
        while (j >= 128) {
            j >>= 7;
            i--;
            bArr[i] = (byte) (((int) j) | 128);
        }
        byteArrayOutputStream.write(bArr, i, 9 - i);
    }

    static void a(ByteArrayOutputStream byteArrayOutputStream, BigInteger bigInteger) {
        int bitLength = (bigInteger.bitLength() + 6) / 7;
        if (bitLength == 0) {
            byteArrayOutputStream.write(0);
            return;
        }
        byte[] bArr = new byte[bitLength];
        int i = bitLength - 1;
        for (int i2 = i; i2 >= 0; i2--) {
            bArr[i2] = (byte) (bigInteger.intValue() | 128);
            bigInteger = bigInteger.shiftRight(7);
        }
        bArr[i] = (byte) (bArr[i] & ByteCompanionObject.MAX_VALUE);
        byteArrayOutputStream.write(bArr, 0, bitLength);
    }
}
