package bc.org.bouncycastle.math.ec.endo;

import bc.org.bouncycastle.math.ec.ECCurve;
import bc.org.bouncycastle.math.ec.ECPointMap;
import bc.org.bouncycastle.math.ec.ScaleXPointMap;
import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\GLVTypeBEndomorphism.smali */
public class GLVTypeBEndomorphism implements GLVEndomorphism {
    protected final GLVTypeBParameters a;
    protected final ECPointMap b;

    public GLVTypeBEndomorphism(ECCurve eCCurve, GLVTypeBParameters gLVTypeBParameters) {
        this.a = gLVTypeBParameters;
        this.b = new ScaleXPointMap(eCCurve.fromBigInteger(gLVTypeBParameters.getBeta()));
    }

    @Override // bc.org.bouncycastle.math.ec.endo.GLVEndomorphism
    public BigInteger[] decomposeScalar(BigInteger bigInteger) {
        return EndoUtil.decomposeScalar(this.a.getSplitParams(), bigInteger);
    }

    @Override // bc.org.bouncycastle.math.ec.endo.ECEndomorphism
    public ECPointMap getPointMap() {
        return this.b;
    }

    @Override // bc.org.bouncycastle.math.ec.endo.ECEndomorphism
    public boolean hasEfficientPointMap() {
        return true;
    }
}
