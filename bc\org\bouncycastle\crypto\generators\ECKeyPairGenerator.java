package bc.org.bouncycastle.crypto.generators;

import bc.org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import bc.org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import bc.org.bouncycastle.crypto.params.ECDomainParameters;
import bc.org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import bc.org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import bc.org.bouncycastle.crypto.params.ECPublicKeyParameters;
import bc.org.bouncycastle.math.ec.ECConstants;
import bc.org.bouncycastle.math.ec.ECMultiplier;
import bc.org.bouncycastle.math.ec.FixedPointCombMultiplier;
import bc.org.bouncycastle.math.ec.WNafUtil;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.f1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.g5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.k1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.q1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.t1;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w3;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\crypto\generators\ECKeyPairGenerator.smali */
public class ECKeyPairGenerator implements ECConstants {
    private final String a;
    ECDomainParameters b;
    SecureRandom c;

    public ECKeyPairGenerator() {
        this("ECKeyGen");
    }

    protected boolean a(BigInteger bigInteger, BigInteger bigInteger2) {
        return bigInteger.compareTo(ECConstants.ONE) < 0 || bigInteger.compareTo(bigInteger2) >= 0;
    }

    public AsymmetricCipherKeyPair generateKeyPair() {
        BigInteger n = this.b.getN();
        int bitLength = n.bitLength();
        int i = bitLength >>> 2;
        while (true) {
            BigInteger b = f1.b(bitLength, this.c);
            if (!a(b, n) && WNafUtil.getNafWeight(b) >= i) {
                return new AsymmetricCipherKeyPair((AsymmetricKeyParameter) new ECPublicKeyParameters(a().multiply(this.b.getG(), b), this.b), (AsymmetricKeyParameter) new ECPrivateKeyParameters(b, this.b));
            }
        }
    }

    public void init(g5 g5Var) {
        ECKeyGenerationParameters eCKeyGenerationParameters = (ECKeyGenerationParameters) g5Var;
        this.c = eCKeyGenerationParameters.getRandom();
        ECDomainParameters domainParameters = eCKeyGenerationParameters.getDomainParameters();
        this.b = domainParameters;
        t1.a(new w3(this.a, k1.a(domainParameters.getCurve()), eCKeyGenerationParameters.getDomainParameters(), q1.KEYGEN));
    }

    protected ECKeyPairGenerator(String str) {
        this.a = str;
    }

    protected ECMultiplier a() {
        return new FixedPointCombMultiplier();
    }
}
