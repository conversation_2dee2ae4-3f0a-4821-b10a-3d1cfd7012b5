package o.eo;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.card.CardStatus;
import fr.antelop.sdk.digitalcard.VirtualCardNumber;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.Date;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.ao.e;
import o.ee.g;
import o.er.i;
import o.v.m;
import o.v.q;
import org.bouncycastle.asn1.BERTags;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\j.smali */
public final class j {
    private static char k;
    private static char m;
    private static char n;

    /* renamed from: o, reason: collision with root package name */
    private static char f74o;
    private static int q;
    private final String a;
    private final String b;
    private final String c;
    private final String d;
    private b e;
    private final String f;
    private final String g;
    private final Integer h;
    private final Date i;
    private final Date j;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int l = 0;

    static {
        q = 1;
        l();
        Process.getThreadPriority(0);
        int i = l + 61;
        q = i % 128;
        switch (i % 2 != 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void l() {
        k = (char) 11409;
        n = (char) 34748;
        f74o = (char) 34623;
        m = (char) 38842;
    }

    public j(String str, b bVar, String str2, String str3, String str4, Date date, String str5, String str6, Date date2, Integer num) {
        this.b = str;
        this.e = bVar;
        this.d = str2;
        this.a = str3;
        this.c = str4;
        this.j = date;
        this.g = str5;
        this.f = str6;
        this.i = date2;
        this.h = num;
    }

    public final String e() {
        int i = l + 51;
        q = i % 128;
        switch (i % 2 == 0 ? (char) 17 : '\\') {
            case 17:
                int i2 = 76 / 0;
                return this.b;
            default:
                return this.b;
        }
    }

    public final b a() {
        int i = q;
        int i2 = i + 65;
        l = i2 % 128;
        int i3 = i2 % 2;
        b bVar = this.e;
        int i4 = i + 39;
        l = i4 % 128;
        switch (i4 % 2 != 0 ? (char) 5 : '5') {
            case 5:
                int i5 = 10 / 0;
                return bVar;
            default:
                return bVar;
        }
    }

    public final void b(b bVar) {
        int i = q;
        int i2 = i + Opcodes.DREM;
        l = i2 % 128;
        int i3 = i2 % 2;
        this.e = bVar;
        int i4 = i + 61;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    public final String b() {
        String str;
        int i = q;
        int i2 = i + Opcodes.DSUB;
        l = i2 % 128;
        switch (i2 % 2 != 0) {
            case true:
                str = this.d;
                int i3 = 7 / 0;
                break;
            default:
                str = this.d;
                break;
        }
        int i4 = i + 73;
        l = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String d() {
        int i = l + 63;
        q = i % 128;
        switch (i % 2 == 0 ? 'b' : 'N') {
            case 'N':
                return this.a;
            default:
                int i2 = 34 / 0;
                return this.a;
        }
    }

    public final String c() {
        int i = l;
        int i2 = i + Opcodes.LSUB;
        q = i2 % 128;
        int i3 = i2 % 2;
        String str = this.c;
        int i4 = i + 91;
        q = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final Date g() {
        int i = l + 33;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        Date date = this.j;
        int i4 = i2 + 97;
        l = i4 % 128;
        switch (i4 % 2 != 0 ? '7' : '+') {
            case '+':
                return date;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String h() {
        int i = l + Opcodes.LSHL;
        q = i % 128;
        switch (i % 2 == 0 ? 'C' : '.') {
            case '.':
                return this.g;
            default:
                int i2 = 7 / 0;
                return this.g;
        }
    }

    public final String f() {
        String str;
        int i = q;
        int i2 = i + 3;
        l = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 31 : 'T') {
            case Opcodes.BASTORE /* 84 */:
                str = this.f;
                break;
            default:
                str = this.f;
                int i3 = 32 / 0;
                break;
        }
        int i4 = i + 83;
        l = i4 % 128;
        switch (i4 % 2 != 0 ? '/' : (char) 25) {
            case 25:
                return str;
            default:
                throw null;
        }
    }

    public final Date j() {
        int i = l + Opcodes.LUSHR;
        int i2 = i % 128;
        q = i2;
        int i3 = i % 2;
        Date date = this.i;
        int i4 = i2 + 51;
        l = i4 % 128;
        switch (i4 % 2 == 0) {
            case true:
                return date;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final Integer i() {
        int i = l + 77;
        q = i % 128;
        switch (i % 2 == 0 ? (char) 24 : (char) 7) {
            case 7:
                return this.h;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final void e(Context context, o.er.f fVar, e eVar, OperationCallback<Void> operationCallback) throws WalletValidationException {
        int i = q + Opcodes.LSUB;
        l = i % 128;
        Object obj = null;
        switch (i % 2 == 0) {
            case false:
                a();
                b bVar = b.a;
                throw null;
            default:
                if (a() != b.a) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    p("㭤⟟錖圳≦良됔ꢩ誂᪠턉\uf473죔\ude50俊狷\uec1b삘", (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 17, objArr);
                    throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
                }
                b(context, e.c.b, fVar, eVar, operationCallback);
                int i2 = q + 41;
                l = i2 % 128;
                switch (i2 % 2 != 0 ? '\f' : '+') {
                    case '\f':
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
        }
    }

    public final void a(Context context, o.er.f fVar, e eVar, OperationCallback<Void> operationCallback) throws WalletValidationException {
        int i = l + 1;
        q = i % 128;
        char c = i % 2 == 0 ? '4' : (char) 5;
        b(context, e.c.e, fVar, eVar, operationCallback);
        switch (c) {
            case 5:
                int i2 = l + 41;
                q = i2 % 128;
                switch (i2 % 2 != 0) {
                    case false:
                        throw null;
                    default:
                        return;
                }
            default:
                throw null;
        }
    }

    private void b(Context context, e.c cVar, o.er.f fVar, e eVar, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        int i = q + 89;
        l = i % 128;
        int i2 = i % 2;
        if (eVar.z() != CardStatus.Active) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            p("⥠뻍\ue2d4蘽", Color.green(0) + 4, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (!fVar.b()) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr2 = new Object[1];
            p("㭤⟟錖圳≦良됔ꢩ誂᪠턉\uf473죔\ude50俊狷\uec1b삘", View.MeasureSpec.getMode(0) + 17, objArr2);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr2[0]).intern());
        }
        o.ao.e eVar2 = new o.ao.e(context, new e.a() { // from class: o.eo.j.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int b;
            private static int c;
            private static int d;
            private static int g;
            private static int h;
            private static short[] i;
            private static byte[] j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                h = 0;
                g = 1;
                j = new byte[]{-99, -109, 101, 104, -73, 122, 98, -127, -114, 71, -101, 124, -111, -110, -103, -125, 116, -99, 103, -112, -75, -112, -98, -110, -112, 126, UtilitiesSDKConstants.SRP_LABEL_MAC, 117, -101, -99, 97, 110, -106, 99, -99, -124, 79, 111, -33, 99, -99, Base64.padSymbol, -112, -98, -110, 100, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 114, 105, -109, 99, -99, 43, 99, 109, -103, -109, -104, -117, 72, -101, -99, 97, 110, -106, 99, -99, -124, 79, 111, -33, 99, -99, Base64.padSymbol, -112, -98, -110, 100, 109, UtilitiesSDKConstants.SRP_LABEL_MAC, 114, 105, -109, -112, -112, -112};
                c = 909053575;
                b = 1550856157;
                d = 1204928579;
            }

            static void init$0() {
                $$a = new byte[]{119, -13, -39, 23};
                $$b = 58;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0038). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void k(int r7, short r8, short r9, java.lang.Object[] r10) {
                /*
                    byte[] r0 = o.eo.j.AnonymousClass3.$$a
                    int r9 = r9 + 4
                    int r8 = r8 * 2
                    int r8 = 1 - r8
                    int r7 = r7 * 2
                    int r7 = 110 - r7
                    byte[] r1 = new byte[r8]
                    int r8 = r8 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r7 = r8
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r10
                    r10 = r9
                    goto L38
                L1a:
                    r3 = r2
                L1b:
                    byte r4 = (byte) r7
                    r1[r3] = r4
                    int r9 = r9 + 1
                    if (r3 != r8) goto L2a
                    java.lang.String r7 = new java.lang.String
                    r7.<init>(r1, r2)
                    r10[r2] = r7
                    return
                L2a:
                    int r3 = r3 + 1
                    r4 = r0[r9]
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    r6 = r10
                    r10 = r9
                    r9 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r6
                L38:
                    int r9 = -r9
                    int r8 = r8 + r9
                    r9 = r10
                    r10 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eo.j.AnonymousClass3.k(int, short, short, java.lang.Object[]):void");
            }

            @Override // o.ao.e.a
            public final void c() {
                int i3 = g + 79;
                h = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr3 = new Object[1];
                f((byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1), (-1912524499) - (ViewConfiguration.getScrollBarFadeDuration() >> 16), (short) Color.red(0), (-1) - TextUtils.indexOf("", ""), (-1784619268) + View.combineMeasuredStates(0, 0), objArr3);
                String intern = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                f((byte) (ViewConfiguration.getKeyRepeatDelay() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) - 1912524478, (short) Gravity.getAbsoluteGravity(0, 0), Drawable.resolveOpacity(0, 0) + 8, (-1784619227) - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr4);
                g.d(intern, ((String) objArr4[0]).intern());
                Object obj = null;
                operationCallback.onSuccess(null);
                int i5 = h + 43;
                g = i5 % 128;
                switch (i5 % 2 == 0 ? (char) 17 : 'X') {
                    case Opcodes.POP2 /* 88 */:
                        return;
                    default:
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // o.ao.e.a
            public final void c(o.bb.d dVar) {
                o.bv.c c2 = o.bv.c.c(dVar);
                g.c();
                Object[] objArr3 = new Object[1];
                f((byte) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 1), (-1912524499) - KeyEvent.normalizeMetaState(0), (short) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) - 1), (ViewConfiguration.getDoubleTapTimeout() >> 16) - 1, (-1784619268) + (Process.myTid() >> 22), objArr3);
                String intern = ((String) objArr3[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr4 = new Object[1];
                f((byte) Color.alpha(0), (-1912524448) - ((Process.getThreadPriority(0) + 20) >> 6), (short) (TextUtils.lastIndexOf("", '0', 0, 0) + 1), 12 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), KeyEvent.getDeadChar(0, 0) - 1784619227, objArr4);
                g.d(intern, sb.append(((String) objArr4[0]).intern()).append(c2).toString());
                operationCallback.onError(o.bv.c.c(dVar).d());
                int i3 = h + 59;
                g = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:92:0x0242, code lost:
            
                r4 = r7;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void f(byte r19, int r20, short r21, int r22, int r23, java.lang.Object[] r24) {
                /*
                    Method dump skipped, instructions count: 896
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eo.j.AnonymousClass3.f(byte, int, short, int, int, java.lang.Object[]):void");
            }
        }, o.ei.c.c());
        o.h.d dVar = new o.h.d();
        Object[] objArr3 = new Object[1];
        p("禅㪔쏢曭ꯓ\u0897\uf6e9㞡ꭆ쀏\ue965蛁빥鶧\uf8c5茨ഉ埜", 17 - View.combineMeasuredStates(0, 0), objArr3);
        eVar2.b(dVar, ((String) objArr3[0]).intern(), cVar, eVar, this);
        int i3 = q + 21;
        l = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                return;
            default:
                throw null;
        }
    }

    public final void d(Context context, o.er.f fVar, e eVar, final OperationCallback<Void> operationCallback) throws WalletValidationException {
        e(fVar, eVar).e(context, new o.p.g() { // from class: o.eo.j.5
            private static int d = 0;
            private static int a = 1;

            @Override // o.p.g
            public final void abortPrompt() {
                int i = a + Opcodes.LNEG;
                d = i % 128;
                int i2 = i % 2;
            }

            @Override // o.p.g
            public final void onAuthenticationDeclined() {
                int i = a + 55;
                d = i % 128;
                int i2 = i % 2;
            }

            @Override // o.p.g
            public final void onCustomerCredentialsInvalid(o.g.b bVar) {
                int i = d;
                int i2 = (i & 37) + (i | 37);
                a = i2 % 128;
                switch (i2 % 2 == 0 ? 'F' : (char) 17) {
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.p.g
            public final void onProcessStart() {
                int i = (d + 84) - 1;
                a = i % 128;
                switch (i % 2 == 0) {
                    case true:
                        int i2 = 76 / 0;
                        break;
                }
            }

            @Override // o.p.g
            public final void onCustomerCredentialsRequired(List<o.i.g> list) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i = d;
                int i2 = (i ^ 53) + ((i & 53) << 1);
                a = i2 % 128;
                int i3 = i2 % 2;
            }

            @Override // o.p.g
            public final void onProcessSuccess() {
                int i = a + 67;
                d = i % 128;
                switch (i % 2 != 0 ? (char) 16 : 'X') {
                    case 16:
                        operationCallback.onSuccess(null);
                        int i2 = 46 / 0;
                        return;
                    default:
                        operationCallback.onSuccess(null);
                        return;
                }
            }

            @Override // o.p.g
            public final void onError(o.bv.c cVar) {
                int i = a + 73;
                d = i % 128;
                int i2 = i % 2;
                operationCallback.onError(cVar.d());
                int i3 = (a + 90) - 1;
                d = i3 % 128;
                switch (i3 % 2 != 0) {
                    case true:
                        throw null;
                    default:
                        return;
                }
            }
        });
        int i = l + Opcodes.DREM;
        q = i % 128;
        switch (i % 2 == 0 ? '3' : 'B') {
            case '3':
                int i2 = 12 / 0;
                return;
            default:
                return;
        }
    }

    public final q e(o.er.f fVar, e eVar) {
        q qVar = new q(fVar.c(), eVar, fVar.b(), this);
        int i = q + 13;
        l = i % 128;
        switch (i % 2 != 0 ? 'Z' : 'O') {
            case 'Z':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return qVar;
        }
    }

    public final m b(i iVar, e eVar, o.dw.b bVar) {
        m mVar = new m(iVar.d(), eVar, iVar.b(), bVar, this);
        int i = q + 17;
        l = i % 128;
        int i2 = i % 2;
        return mVar;
    }

    /* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
    /* JADX WARN: Unknown enum class pattern. Please report as an issue! */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\j$b.smali */
    public static final class b implements o.ee.a<VirtualCardNumber.Status>, o.ei.b {
        public static final byte[] $$a = null;
        public static final int $$b = 0;
        private static int $10;
        private static int $11;
        public static final b a;
        public static final b b;
        private static final /* synthetic */ b[] c;
        public static final b d;
        private static int f;
        private static short[] g;
        private static byte[] h;
        private static int i;
        private static int j;
        private static int k;

        /* renamed from: o, reason: collision with root package name */
        private static int f75o;
        private final String e;

        static void b() {
            h = new byte[]{-101, 66, 95, -87, 66, 69, -66, -24, 6, -82, -17, 49, -88, -84, 19, 1, 69, 90, -85, -86, 88, -2, 31, -42, -48, -101, 1, 66, 92, -46, 68, -86, -116, 108, -109, -90, -127, 74, 72, 72, 67, -35, -100, -126, 98, -101, -122, -103, 96, -124, 122, 96, -115, -21, -77, 23, -56, 96, 49, -109, 11, 103, -108, 99, -81, 93, -90, 69, -87, -47, -82, 122, 99, -12, 62, -51, 38, -54, -14, -11, 33, 97, 125, ByteCompanionObject.MAX_VALUE, 13, 119, 53, 47, 97, 75, 69, 91, 77, 83, 85};
            i = 909053574;
            j = -1818623242;
            f = 2119482453;
        }

        static void init$0() {
            $$a = new byte[]{1, 28, -75, 69};
            $$b = BERTags.FLAGS;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0030  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0028  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0030 -> B:4:0x0039). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void m(int r7, short r8, byte r9, java.lang.Object[] r10) {
            /*
                int r8 = r8 * 3
                int r8 = 3 - r8
                int r7 = r7 * 4
                int r7 = 1 - r7
                byte[] r0 = o.eo.j.b.$$a
                int r9 = r9 * 2
                int r9 = r9 + 108
                byte[] r1 = new byte[r7]
                r2 = 0
                if (r0 != 0) goto L1a
                r9 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r10
                r10 = r8
                goto L39
            L1a:
                r3 = r2
                r6 = r8
                r8 = r7
                r7 = r9
                r9 = r6
            L1f:
                int r4 = r3 + 1
                int r9 = r9 + 1
                byte r5 = (byte) r7
                r1[r3] = r5
                if (r4 != r8) goto L30
                java.lang.String r7 = new java.lang.String
                r7.<init>(r1, r2)
                r10[r2] = r7
                return
            L30:
                r3 = r0[r9]
                r6 = r9
                r9 = r8
                r8 = r3
                r3 = r1
                r1 = r0
                r0 = r10
                r10 = r6
            L39:
                int r8 = -r8
                int r7 = r7 + r8
                r8 = r9
                r9 = r10
                r10 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                goto L1f
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.j.b.m(int, short, byte, java.lang.Object[]):void");
        }

        private static /* synthetic */ b[] c() {
            int i2 = f75o + 5;
            k = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    b[] bVarArr = new b[4];
                    bVarArr[1] = a;
                    bVarArr[0] = b;
                    bVarArr[3] = d;
                    return bVarArr;
                default:
                    return new b[]{a, b, d};
            }
        }

        public static b valueOf(String str) {
            int i2 = f75o + 17;
            k = i2 % 128;
            char c2 = i2 % 2 != 0 ? (char) 29 : '`';
            b bVar = (b) Enum.valueOf(b.class, str);
            switch (c2) {
                case Opcodes.IADD /* 96 */:
                    return bVar;
                default:
                    throw null;
            }
        }

        public static b[] values() {
            int i2 = f75o + Opcodes.LNEG;
            k = i2 % 128;
            int i3 = i2 % 2;
            b[] bVarArr = (b[]) c.clone();
            int i4 = k + 81;
            f75o = i4 % 128;
            switch (i4 % 2 == 0) {
                case false:
                    return bVarArr;
                default:
                    throw null;
            }
        }

        @Override // o.ee.d
        public final /* synthetic */ Object a() {
            int i2 = f75o + 95;
            k = i2 % 128;
            int i3 = i2 % 2;
            VirtualCardNumber.Status d2 = d();
            int i4 = f75o + Opcodes.DSUB;
            k = i4 % 128;
            int i5 = i4 % 2;
            return d2;
        }

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            k = 0;
            f75o = 1;
            b();
            Object[] objArr = new Object[1];
            l((byte) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 93), TextUtils.getOffsetAfter("", 0) - 1216066194, (short) (46 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 22, 1514861532 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            l((byte) (Color.blue(0) + 58), (-1216066189) + (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (short) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 53), ExpandableListView.getPackedPositionType(0L) - 23, 1514861530 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr2);
            a = new b(intern, 0, ((String) objArr2[0]).intern());
            Object[] objArr3 = new Object[1];
            l((byte) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 71), (-1216066182) + (ViewConfiguration.getMaximumFlingVelocity() >> 16), (short) ((KeyEvent.getMaxKeyCode() >> 16) + Opcodes.ISHR), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) - 24, KeyEvent.keyCodeFromString("") + 1514861549, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            l((byte) (Color.red(0) + 37), ExpandableListView.getPackedPositionChild(0L) - 1216066172, (short) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + Opcodes.LNEG), (-23) - (KeyEvent.getMaxKeyCode() >> 16), Color.argb(0, 0, 0, 0) + 1514861549, objArr4);
            b = new b(intern2, 1, ((String) objArr4[0]).intern());
            Object[] objArr5 = new Object[1];
            l((byte) ((-40) - TextUtils.indexOf("", "", 0, 0)), KeyEvent.getDeadChar(0, 0) - 1216066164, (short) (MotionEvent.axisFromString("") + 59), (ViewConfiguration.getJumpTapTimeout() >> 16) - 23, 1514861534 - TextUtils.indexOf("", "", 0, 0), objArr5);
            String intern3 = ((String) objArr5[0]).intern();
            Object[] objArr6 = new Object[1];
            l((byte) ((ViewConfiguration.getJumpTapTimeout() >> 16) + Opcodes.LNEG), (ViewConfiguration.getScrollDefaultDelay() >> 16) - 1216066157, (short) ((-82) - Process.getGidForName("")), (-23) - Color.red(0), 1514861534 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr6);
            d = new b(intern3, 2, ((String) objArr6[0]).intern());
            c = c();
            int i2 = k + 25;
            f75o = i2 % 128;
            int i3 = i2 % 2;
        }

        private b(String str, int i2, String str2) {
            this.e = str2;
        }

        public final VirtualCardNumber.Status d() {
            int i2 = f75o + 91;
            k = i2 % 128;
            switch (i2 % 2 != 0 ? 'F' : 'H') {
                case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                    int i3 = AnonymousClass4.a[ordinal()];
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    switch (AnonymousClass4.a[ordinal()]) {
                        case 1:
                            return VirtualCardNumber.Status.Active;
                        case 2:
                            VirtualCardNumber.Status status = VirtualCardNumber.Status.Suspended;
                            int i4 = k + 49;
                            f75o = i4 % 128;
                            int i5 = i4 % 2;
                            return status;
                        case 3:
                            Object[] objArr = new Object[1];
                            l((byte) (53 - KeyEvent.normalizeMetaState(0)), TextUtils.indexOf("", "") - 1216066245, (short) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 9), View.getDefaultSize(0, 0) - 23, 1514861534 - KeyEvent.getDeadChar(0, 0), objArr);
                            throw new RuntimeException(((String) objArr[0]).intern());
                        default:
                            StringBuilder sb = new StringBuilder();
                            Object[] objArr2 = new Object[1];
                            l((byte) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + Opcodes.LMUL), (-1216066212) - TextUtils.indexOf("", "", 0, 0), (short) (TextUtils.lastIndexOf("", '0') - 118), (-23) - (ViewConfiguration.getLongPressTimeout() >> 16), 1514861552 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr2);
                            throw new UnsupportedOperationException(sb.append(((String) objArr2[0]).intern()).append(name()).toString());
                    }
            }
        }

        @Override // o.ei.b
        public final String e() {
            int i2 = k;
            int i3 = i2 + 33;
            f75o = i3 % 128;
            int i4 = i3 % 2;
            String str = this.e;
            int i5 = i2 + 43;
            f75o = i5 % 128;
            switch (i5 % 2 == 0) {
                case true:
                    throw null;
                default:
                    return str;
            }
        }

        /* JADX WARN: Code restructure failed: missing block: B:97:0x02fc, code lost:
        
            if (r3 != false) goto L105;
         */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void l(byte r17, int r18, short r19, int r20, int r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 916
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.eo.j.b.l(byte, int, short, int, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.eo.j$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eo\j$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        static final /* synthetic */ int[] a;
        private static int d;
        private static int e;

        static {
            d = 0;
            e = 1;
            int[] iArr = new int[b.values().length];
            a = iArr;
            try {
                iArr[b.a.ordinal()] = 1;
                int i = e;
                int i2 = ((i | 31) << 1) - (i ^ 31);
                d = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[b.b.ordinal()] = 2;
                int i3 = (d + 86) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[b.d.ordinal()] = 3;
            } catch (NoSuchFieldError e4) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 594
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eo.j.p(java.lang.String, int, java.lang.Object[]):void");
    }
}
