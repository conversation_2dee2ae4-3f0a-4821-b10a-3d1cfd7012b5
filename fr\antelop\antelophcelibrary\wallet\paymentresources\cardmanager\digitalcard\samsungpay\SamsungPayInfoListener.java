package fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay;

import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Bundle;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.samsung.android.sdk.samsungpay.v2.StatusListener;
import fr.antelop.sdk.AntelopErrorCode;
import java.lang.reflect.Method;
import o.a.l;
import o.ee.g;
import o.ep.a;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\antelophcelibrary\wallet\paymentresources\cardmanager\digitalcard\samsungpay\SamsungPayInfoListener.smali */
public class SamsungPayInfoListener implements StatusListener {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] a;
    private static int g;
    private static int h;
    private final String b;
    private final a.InterfaceC0042a<String> c;
    private final c d;
    private final Boolean e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        g = 1;
        d();
        int i = g + 83;
        h = i % 128;
        switch (i % 2 != 0) {
            case false:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static void d() {
        a = new char[]{50932, 50854, 50852, 50852, 50849, 50878, 50851, 50839, 50834, 50873, 50873, 50855, 50855, 50862, 50859, 50848, 50837, 50838, 50855, 50876, 50873, 50877, 50858, 50839, 50776, 50777, 50828, 50819, 50828, 50777, 50777, 50763, 50761, 50761, 50779, 50777, 50828, 50781, 50762, 50754, 50855, 50828, 50776, 50763, 50752, 50752, 50767, 50869, 50828, 50776, 50763, 50757, 50828, 50838, 50828, 50777, 50779, 50776, 50767, 50764, 51150, 50738, 51150, 50750, 50724, 51148, 51148, 50714, 50697, 50697, 50810, 50803, 50803, 50735, 51150, 50909, 50912, 50912, 50831, 50843, 50835, 50849, 50853, 50849, 50859, 50849, 50822, 50923, 50923, 50831, 50843, 50835, 50849, 50853, 50849, 50859, 50833, 50853, 50848, 50837, 50839, 50857, 50859, 50850, 50848, 50841, 50833, 50849, 50849, 50820, 50923, 50923, 50820, 50878, 50849, 50857, 50862, 50849, 50857, 50861, 50851, 50909, 50912, 50912, 50831, 50843, 50842, 50857, 50859, 50850, 50848, 50857, 50831, 50923, 50923, 50831, 50843, 50842, 50857, 50859, 50850, 50848, 50841, 50835, 50848, 50837, 50839, 50857, 50859, 50850, 50848, 50841, 50833, 50849, 50849, 50820, 50923, 50923, 50820, 50878, 50849, 50857, 50862, 50849, 50857, 50861, 50851, 50868, 50751, 50741, 50715, 50698, 50725, 50751, 50741, 50745, 50741, 50715, 50701, 50730, 50747, 50747, 50718, 50690, 50722, 50748, 50747, 50745, 50749, 50691, 50815, 50815, 50691, 50746, 50739, 50737, 50737, 50731, 50728, 50744, 50749, 50691, 50804, 50804, 50838, 50803, 50803, 50708, 51151, 51140, 51146, 51146, 50751, 50724, 51148, 51148, 50714, 50697, 50697};
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0025  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001d  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0025 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void i(byte r6, byte r7, byte r8, java.lang.Object[] r9) {
        /*
            int r8 = r8 * 4
            int r8 = 4 - r8
            int r7 = 122 - r7
            byte[] r0 = fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayInfoListener.$$a
            int r6 = r6 * 2
            int r6 = r6 + 1
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r4 = r7
            r3 = r2
            r7 = r6
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            if (r3 != r6) goto L25
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L25:
            int r3 = r3 + 1
            r4 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r8 = r8 + 1
            int r6 = r6 + r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: fr.antelop.antelophcelibrary.wallet.paymentresources.cardmanager.digitalcard.samsungpay.SamsungPayInfoListener.i(byte, byte, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{9, -87, 124, -45};
        $$b = 220;
    }

    protected SamsungPayInfoListener(c cVar, a.InterfaceC0042a<String> interfaceC0042a, Boolean bool, String str) {
        this.d = cVar;
        this.c = interfaceC0042a;
        this.e = bool;
        this.b = str;
    }

    public void onSuccess(int i, Bundle bundle) {
        g.c();
        Object[] objArr = new Object[1];
        f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 24, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f(null, new int[]{24, 35, 33, 28}, true, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        f("\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000", new int[]{59, 16, Opcodes.DCMPL, 11}, false, objArr3);
        g.d(intern, append.append(((String) objArr3[0]).intern()).append(bundle.toString()).toString());
        if (this.e.booleanValue()) {
            String string = bundle.getString(this.b);
            g.c();
            Object[] objArr4 = new Object[1];
            f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 24, 0, 0}, true, objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr5 = new Object[1];
            f("\u0000\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001", new int[]{75, 46, 0, 0}, true, objArr5);
            g.d(intern2, sb2.append(((String) objArr5[0]).intern()).append(string).toString());
            this.d.d(string);
            this.c.e((a.InterfaceC0042a<String>) string);
            int i2 = g + 81;
            h = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    throw null;
                default:
                    return;
            }
        }
        String string2 = bundle.getString(this.b);
        g.c();
        Object[] objArr6 = new Object[1];
        f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 24, 0, 0}, true, objArr6);
        String intern3 = ((String) objArr6[0]).intern();
        StringBuilder sb3 = new StringBuilder();
        Object[] objArr7 = new Object[1];
        f("\u0000\u0000\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000\u0001\u0001", new int[]{Opcodes.LSHL, 46, 0, 0}, true, objArr7);
        g.d(intern3, sb3.append(((String) objArr7[0]).intern()).append(string2).toString());
        this.d.e(string2);
        this.c.e((a.InterfaceC0042a<String>) string2);
        int i3 = g + 51;
        h = i3 % 128;
        switch (i3 % 2 != 0 ? '_' : 'X') {
            case Opcodes.POP2 /* 88 */:
                return;
            default:
                int i4 = 46 / 0;
                return;
        }
    }

    public void onFail(int i, Bundle bundle) {
        g.c();
        Object[] objArr = new Object[1];
        f("\u0000\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000", new int[]{0, 24, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f("\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0001\u0001\u0001\u0000\u0000", new int[]{Opcodes.GOTO, 37, Opcodes.F2L, 0}, false, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(i);
        Object[] objArr3 = new Object[1];
        f("\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000", new int[]{204, 15, Opcodes.DCMPL, 0}, false, objArr3);
        g.e(intern, append.append(((String) objArr3[0]).intern()).append(bundle.toString()).toString());
        this.c.e(new o.bv.c(AntelopErrorCode.SamsungPayWalletNotAvailable));
        int i2 = h + 95;
        g = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Type inference failed for: r0v0, types: [java.lang.String] */
    /* JADX WARN: Type inference failed for: r0v1 */
    /* JADX WARN: Type inference failed for: r0v29, types: [byte[]] */
    private static void f(String str, int[] iArr, boolean z, Object[] objArr) {
        char[] cArr;
        ?? r0 = str;
        int i = 1;
        int i2 = 0;
        switch (r0 == 0) {
            case true:
                break;
            default:
                r0 = r0.getBytes(LocalizedMessage.DEFAULT_ENCODING);
                break;
        }
        byte[] bArr = (byte[]) r0;
        l lVar = new l();
        int i3 = iArr[0];
        int i4 = iArr[1];
        int i5 = iArr[2];
        int i6 = iArr[3];
        char[] cArr2 = a;
        long j = 0;
        switch (cArr2 != null ? '\'' : '(') {
            case '(':
                break;
            default:
                int length = cArr2.length;
                char[] cArr3 = new char[length];
                int i7 = 0;
                while (i7 < length) {
                    try {
                        Object[] objArr2 = new Object[i];
                        objArr2[i2] = Integer.valueOf(cArr2[i7]);
                        Object obj = o.e.a.s.get(1951085128);
                        if (obj != null) {
                            cArr = cArr2;
                        } else {
                            Class cls = (Class) o.e.a.c(AndroidCharacter.getMirror('0') - '%', (char) ((-1) - Process.getGidForName("")), 43 - ExpandableListView.getPackedPositionGroup(j));
                            byte b = (byte) i2;
                            byte b2 = (byte) (b + 2);
                            cArr = cArr2;
                            Object[] objArr3 = new Object[1];
                            i(b, b2, (byte) (b2 - 2), objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(1951085128, obj);
                        }
                        cArr3[i7] = ((Character) ((Method) obj).invoke(null, objArr2)).charValue();
                        i7++;
                        cArr2 = cArr;
                        i = 1;
                        i2 = 0;
                        j = 0;
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                cArr2 = cArr3;
                break;
        }
        char[] cArr4 = new char[i4];
        System.arraycopy(cArr2, i3, cArr4, 0, i4);
        if (bArr != null) {
            int i8 = $11 + 81;
            $10 = i8 % 128;
            int i9 = i8 % 2;
            char[] cArr5 = new char[i4];
            lVar.d = 0;
            char c = 0;
            while (true) {
                switch (lVar.d >= i4) {
                    case false:
                        switch (bArr[lVar.d] == 1 ? 'L' : '_') {
                            case Opcodes.SWAP /* 95 */:
                                int i10 = lVar.d;
                                try {
                                    Object[] objArr4 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj2 = o.e.a.s.get(804049217);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(Color.argb(0, 0, 0, 0) + 10, (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 206);
                                        byte b3 = (byte) 0;
                                        byte b4 = b3;
                                        Object[] objArr5 = new Object[1];
                                        i(b3, b4, b4, objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(804049217, obj2);
                                    }
                                    cArr5[i10] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                                    break;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            default:
                                int i11 = lVar.d;
                                try {
                                    Object[] objArr6 = {Integer.valueOf(cArr4[lVar.d]), Integer.valueOf(c)};
                                    Object obj3 = o.e.a.s.get(2016040108);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(11 - (ViewConfiguration.getJumpTapTimeout() >> 16), (char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) - 1), 448 - View.resolveSize(0, 0));
                                        byte b5 = (byte) 0;
                                        byte b6 = (byte) (b5 + 3);
                                        Object[] objArr7 = new Object[1];
                                        i(b5, b6, (byte) (b6 - 3), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE, Integer.TYPE);
                                        o.e.a.s.put(2016040108, obj3);
                                    }
                                    cArr5[i11] = ((Character) ((Method) obj3).invoke(null, objArr6)).charValue();
                                    break;
                                } catch (Throwable th3) {
                                    Throwable cause3 = th3.getCause();
                                    if (cause3 == null) {
                                        throw th3;
                                    }
                                    throw cause3;
                                }
                        }
                        c = cArr5[lVar.d];
                        try {
                            Object[] objArr8 = {lVar, lVar};
                            Object obj4 = o.e.a.s.get(-2112603350);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c(11 - (ViewConfiguration.getTouchSlop() >> 8), (char) View.MeasureSpec.makeMeasureSpec(0, 0), 258 - ExpandableListView.getPackedPositionChild(0L));
                                byte b7 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                i(b7, (byte) (b7 | 56), b7, objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Object.class, Object.class);
                                o.e.a.s.put(-2112603350, obj4);
                            }
                            ((Method) obj4).invoke(null, objArr8);
                        } catch (Throwable th4) {
                            Throwable cause4 = th4.getCause();
                            if (cause4 == null) {
                                throw th4;
                            }
                            throw cause4;
                        }
                    default:
                        cArr4 = cArr5;
                        break;
                }
            }
        }
        if (i6 > 0) {
            int i12 = $11 + 77;
            $10 = i12 % 128;
            if (i12 % 2 != 0) {
                char[] cArr6 = new char[i4];
                System.arraycopy(cArr4, 1, cArr6, 0, i4);
                int i13 = i4 + i6;
                System.arraycopy(cArr6, 0, cArr4, i13, i6);
                System.arraycopy(cArr6, i6, cArr4, 0, i13);
            } else {
                char[] cArr7 = new char[i4];
                System.arraycopy(cArr4, 0, cArr7, 0, i4);
                int i14 = i4 - i6;
                System.arraycopy(cArr7, 0, cArr4, i14, i6);
                System.arraycopy(cArr7, i6, cArr4, 0, i14);
            }
        }
        switch (z ? (char) 1 : '-') {
            case 1:
                char[] cArr8 = new char[i4];
                int i15 = 0;
                while (true) {
                    lVar.d = i15;
                    if (lVar.d >= i4) {
                        cArr4 = cArr8;
                        break;
                    } else {
                        cArr8[lVar.d] = cArr4[(i4 - lVar.d) - 1];
                        i15 = lVar.d + 1;
                    }
                }
        }
        switch (i5 <= 0) {
            case false:
                int i16 = $10 + 85;
                $11 = i16 % 128;
                int i17 = i16 % 2;
                int i18 = 0;
                while (true) {
                    lVar.d = i18;
                    if (lVar.d >= i4) {
                        break;
                    } else {
                        cArr4[lVar.d] = (char) (cArr4[lVar.d] - iArr[2]);
                        i18 = lVar.d + 1;
                    }
                }
        }
        objArr[0] = new String(cArr4);
    }
}
