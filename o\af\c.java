package o.af;

import android.content.Context;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import o.a.h;
import o.bb.d;
import o.cf.i;
import o.cf.j;
import o.ee.g;
import o.eo.f;
import o.y.a;
import o.y.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\af\c.smali */
public final class c extends b<e> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int d;
    String c;
    List<f> e;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\af\c$e.smali */
    public interface e {
        void a(d dVar);

        void c(List<f> list);
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        o();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        TypedValue.complexToFraction(0, 0.0f, 0.0f);
        SystemClock.currentThreadTimeMillis();
        CdmaCellLocation.convertQuartSecToDecDegrees(0);
        Color.rgb(0, 0, 0);
        Color.rgb(0, 0, 0);
        int i = b + 27;
        d = i % 128;
        switch (i % 2 != 0 ? '#' : 'K') {
            case '#':
                throw null;
            default:
                return;
        }
    }

    static void init$0() {
        $$d = new byte[]{33, 17, -65, 85};
        $$e = Opcodes.ISHR;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(short r6, int r7, short r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.af.c.$$d
            int r8 = r8 * 2
            int r8 = r8 + 107
            int r7 = r7 * 2
            int r7 = r7 + 1
            int r6 = r6 * 2
            int r6 = 4 - r6
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1c
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            goto L36
        L1c:
            r3 = r2
        L1d:
            byte r4 = (byte) r8
            r1[r3] = r4
            if (r3 != r7) goto L2a
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2a:
            r4 = r0[r6]
            int r3 = r3 + 1
            r5 = r8
            r8 = r7
            r7 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r5
        L36:
            int r7 = -r7
            int r7 = r7 + r9
            int r6 = r6 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r7
            r7 = r5
            goto L1d
        */
        throw new UnsupportedOperationException("Method not decompiled: o.af.c.l(short, int, short, java.lang.Object[]):void");
    }

    static void o() {
        a = 874635467;
    }

    @Override // o.y.b
    public final /* synthetic */ a b() {
        int i = b + 27;
        d = i % 128;
        switch (i % 2 != 0 ? 'O' : '8') {
            case '8':
                return n();
            default:
                n();
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public c(Context context, e eVar, o.ei.c cVar) {
        super(context, eVar, cVar, o.bb.e.q);
    }

    public final void c(String str) {
        g.c();
        Object[] objArr = new Object[1];
        k(ExpandableListView.getPackedPositionGroup(0L) + 3, "\u0011\u0002￤\u0001\u000b\ufffe\n\n\f￠\u0010\u000b\u0002\b\f\ufff1\u0001\u000f\ufffe￠", 20 - View.MeasureSpec.getMode(0), 279 - (ViewConfiguration.getTouchSlop() >> 8), true, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        k(TextUtils.lastIndexOf("", '0', 0, 0) + 5, "\u001b\uffc8￢\uffc8\f\u0017\uffef\r\u001c￫\t\u001a\f￼\u0017\u0013\r\u0016\u001b\uffc8ￕ\uffc8\u000b\t\u001a\f\uffc8\u001c\u0017\uffc8\u000f\r\u001c\uffc8\u001c\u0017\u0013\r\u0016", TextUtils.indexOf("", "", 0) + 39, 268 - TextUtils.indexOf("", "", 0), false, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(str).toString());
        this.c = str;
        c();
        int i = b + 95;
        d = i % 128;
        int i2 = i % 2;
    }

    private AsyncTaskC0017c n() {
        AsyncTaskC0017c asyncTaskC0017c = new AsyncTaskC0017c(this);
        int i = b + 85;
        d = i % 128;
        switch (i % 2 != 0 ? 'B' : (char) 16) {
            case 'B':
                int i2 = 10 / 0;
                return asyncTaskC0017c;
            default:
                return asyncTaskC0017c;
        }
    }

    @Override // o.y.b
    public final String a() {
        int i = d + 19;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        k(3 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), "\u0011\u0002￤\u0001\u000b\ufffe\n\n\f￠\u0010\u000b\u0002\b\f\ufff1\u0001\u000f\ufffe￠", ExpandableListView.getPackedPositionType(0L) + 20, 279 - (ViewConfiguration.getTouchSlop() >> 8), true, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = d + Opcodes.LMUL;
        b = i3 % 128;
        switch (i3 % 2 == 0 ? '3' : '6') {
            case Opcodes.ISTORE /* 54 */:
                return intern;
            default:
                throw null;
        }
    }

    /* renamed from: o.af.c$c, reason: collision with other inner class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\af\c$c.smali */
    static final class AsyncTaskC0017c extends o.y.c<c> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static long c;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            a = 0;
            d = 1;
            e = 874635347;
            c = -6400740656294970334L;
        }

        /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
        /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0035). Please report as a decompilation issue!!! */
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        private static void C(int r6, int r7, short r8, java.lang.Object[] r9) {
            /*
                int r7 = r7 + 4
                int r8 = r8 * 3
                int r8 = 1 - r8
                int r6 = 114 - r6
                byte[] r0 = o.af.c.AsyncTaskC0017c.$$d
                byte[] r1 = new byte[r8]
                int r8 = r8 + (-1)
                r2 = 0
                if (r0 != 0) goto L18
                r6 = r7
                r3 = r1
                r4 = r2
                r1 = r0
                r0 = r9
                r9 = r8
                goto L35
            L18:
                r3 = r2
            L19:
                int r7 = r7 + 1
                byte r4 = (byte) r6
                r1[r3] = r4
                if (r3 != r8) goto L28
                java.lang.String r6 = new java.lang.String
                r6.<init>(r1, r2)
                r9[r2] = r6
                return
            L28:
                int r3 = r3 + 1
                r4 = r0[r7]
                r5 = r8
                r8 = r6
                r6 = r7
                r7 = r4
                r4 = r3
                r3 = r1
                r1 = r0
                r0 = r9
                r9 = r5
            L35:
                int r7 = r7 + r8
                r8 = r9
                r9 = r0
                r0 = r1
                r1 = r3
                r3 = r4
                r5 = r7
                r7 = r6
                r6 = r5
                goto L19
            */
            throw new UnsupportedOperationException("Method not decompiled: o.af.c.AsyncTaskC0017c.C(int, int, short, java.lang.Object[]):void");
        }

        static void init$0() {
            $$d = new byte[]{71, -50, -52, -118};
            $$e = 41;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 53;
            a = i % 128;
            int i2 = i % 2;
        }

        @Override // o.y.c
        public final /* synthetic */ i c(Context context) {
            int i = a + Opcodes.LMUL;
            d = i % 128;
            int i2 = i % 2;
            o.cf.d d2 = d(context);
            int i3 = a + 17;
            d = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 1 : '`') {
                case 1:
                    int i4 = 58 / 0;
                    return d2;
                default:
                    return d2;
            }
        }

        AsyncTaskC0017c(c cVar) {
            super(cVar, true);
        }

        @Override // o.y.c
        public final String l() {
            Object obj;
            int i = a + Opcodes.LNEG;
            d = i % 128;
            switch (i % 2 == 0) {
                case true:
                    Object[] objArr = new Object[1];
                    w(69 << TextUtils.indexOf((CharSequence) "", ' ', 1), "\t￮\ufffe\f\ufffb\uffdd\u000e\uffff\u0001\r\b\uffff\u0005", (SystemClock.elapsedRealtimeNanos() > 1L ? 1 : (SystemClock.elapsedRealtimeNanos() == 1L ? 0 : -1)) * 9, 421 >> View.MeasureSpec.getMode(0), false, objArr);
                    obj = objArr[0];
                    break;
                default:
                    Object[] objArr2 = new Object[1];
                    w(8 - TextUtils.indexOf((CharSequence) "", '0', 0), "\t￮\ufffe\f\ufffb\uffdd\u000e\uffff\u0001\r\b\uffff\u0005", 14 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), View.MeasureSpec.getMode(0) + Opcodes.I2C, true, objArr2);
                    obj = objArr2[0];
                    break;
            }
            return ((String) obj).intern();
        }

        private static o.cf.d d(Context context) {
            Object[] objArr = new Object[1];
            w(MotionEvent.axisFromString("") + 13, "\u0004\u0005\u0002��\u0005��\u0001\ufffe￼�\u0002\u0005\u0001￼\u0003\ufffe\u0003", Process.getGidForName("") + 20, TextUtils.indexOf("", "") + 96, false, objArr);
            o.cf.d dVar = new o.cf.d(context, 33, ((String) objArr[0]).intern());
            int i = d + 75;
            a = i % 128;
            switch (i % 2 == 0) {
                case false:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    return dVar;
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            B("𣏕꒺䘒\ue1ed荟⋛", Color.red(0) + 24169, objArr);
            bVar.d(((String) objArr[0]).intern(), ((c) e()).c);
            int i = a + Opcodes.LUSHR;
            d = i % 128;
            switch (i % 2 == 0) {
                case false:
                    return bVar;
                default:
                    int i2 = 77 / 0;
                    return bVar;
            }
        }

        @Override // o.y.c
        public final j n() {
            int i = d + 69;
            int i2 = i % 128;
            a = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.DMUL;
            d = i4 % 128;
            switch (i4 % 2 != 0) {
                case true:
                    return null;
                default:
                    int i5 = 35 / 0;
                    return null;
            }
        }

        @Override // o.y.c
        public final byte[][] k() {
            int i = a + 95;
            int i2 = i % 128;
            d = i2;
            int i3 = i % 2;
            int i4 = i2 + Opcodes.DDIV;
            a = i4 % 128;
            int i5 = i4 % 2;
            return null;
        }

        @Override // o.y.c
        public final o.bb.a c(int i) {
            int i2 = d + 85;
            a = i2 % 128;
            switch (i2 % 2 != 0) {
                case true:
                    throw null;
                default:
                    switch (i) {
                        case 5001:
                            o.bb.a aVar = o.bb.a.ay;
                            int i3 = d + Opcodes.LSHR;
                            a = i3 % 128;
                            int i4 = i3 % 2;
                            return aVar;
                        case 5002:
                            o.bb.a aVar2 = o.bb.a.az;
                            int i5 = a + 81;
                            d = i5 % 128;
                            switch (i5 % 2 != 0) {
                                case false:
                                    int i6 = 70 / 0;
                                    return aVar2;
                                default:
                                    return aVar2;
                            }
                        default:
                            return super.c(i);
                    }
            }
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            o.du.b bVar2;
            Object[] objArr = new Object[1];
            B("陼彊뇷\u0a12沀섲", Color.red(0) + 42391, objArr);
            o.eg.e p = bVar.p(((String) objArr[0]).intern());
            ArrayList arrayList = new ArrayList();
            switch (p == null) {
                case true:
                    break;
                default:
                    switch (p.d() > 0 ? '0' : (char) 7) {
                        case '0':
                            int i = d + Opcodes.LREM;
                            a = i % 128;
                            int i2 = i % 2;
                            int i3 = 0;
                            while (true) {
                                switch (i3 < p.d()) {
                                    case false:
                                        break;
                                    default:
                                        o.eg.b b = p.b(i3);
                                        Object[] objArr2 = new Object[1];
                                        w((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 1, "\u0001\u0002\ufffe", 3 - (ViewConfiguration.getTouchSlop() >> 8), 158 - KeyEvent.getDeadChar(0, 0), true, objArr2);
                                        f.b bVar3 = (f.b) b.d(f.b.class, ((String) objArr2[0]).intern());
                                        Object[] objArr3 = new Object[1];
                                        w((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), "\ufffe\u000e\r\n￮\t\u0005\uffff\b￣", (ViewConfiguration.getJumpTapTimeout() >> 16) + 10, View.MeasureSpec.getMode(0) + Opcodes.I2C, false, objArr3);
                                        String r = b.r(((String) objArr3[0]).intern());
                                        Object[] objArr4 = new Object[1];
                                        B("陼餮㴜턫畡ॲ굍䅕\ue59a禧ᶋ놁嗯\ue9e2跉⇜쐬堄ﰒ遨㑗졍", 25583 - (KeyEvent.getMaxKeyCode() >> 16), objArr4);
                                        String q = b.q(((String) objArr4[0]).intern());
                                        Object[] objArr5 = new Object[1];
                                        w(1 - ((Process.getThreadPriority(0) + 20) >> 6), "\u0001\u0011\u0010\r￠\ufffe\u000f\u0001￦", 9 - (KeyEvent.getMaxKeyCode() >> 16), 143 - Color.alpha(0), false, objArr5);
                                        String q2 = b.q(((String) objArr5[0]).intern());
                                        Object[] objArr6 = new Object[1];
                                        B("陼᭖㧬帣粁鴪덝퇽\uf658ᒘ㔥䮺槃蹪겄촄\ue3b0Ǽ♈", 57751 - View.MeasureSpec.getMode(0), objArr6);
                                        String r2 = b.r(((String) objArr6[0]).intern());
                                        Object[] objArr7 = new Object[1];
                                        B("陼㍔構Ꙍ\udcf8ᕍ䏡硼뚏\uef06▛別袱섵ﾂ㗔扏风", TextUtils.lastIndexOf("", '0') + 51594, objArr7);
                                        String q3 = b.q(((String) objArr7[0]).intern());
                                        Object[] objArr8 = new Object[1];
                                        w(Process.getGidForName("") + 14, "\f￼\n\u000b\u0006\t￣\u0006\ufffe\u0006￬\t\u0003\u000b\u0006\u0002￼\u0005￩￼\b", TextUtils.lastIndexOf("", '0', 0, 0) + 22, 149 - TextUtils.getOffsetBefore("", 0), false, objArr8);
                                        String q4 = b.q(((String) objArr8[0]).intern());
                                        if (q4 != null) {
                                            o.du.b bVar4 = new o.du.b(q4);
                                            int i4 = d + Opcodes.DREM;
                                            a = i4 % 128;
                                            int i5 = i4 % 2;
                                            bVar2 = bVar4;
                                        } else {
                                            bVar2 = null;
                                        }
                                        Object[] objArr9 = new Object[1];
                                        B("陼╨䖳旈萈ꑩ쓩\ue730ݯ➊䟓昁蚡꛱섀\ue150ƒ⇒", 57268 - Process.getGidForName(""), objArr9);
                                        f.a aVar = (f.a) b.e(f.a.class, ((String) objArr9[0]).intern());
                                        Object[] objArr10 = new Object[1];
                                        w(View.getDefaultSize(0, 0) + 3, "\u0000\t\u0004\ufff5", 4 - View.MeasureSpec.getSize(0), 156 - ExpandableListView.getPackedPositionGroup(0L), true, objArr10);
                                        f.e eVar = (f.e) b.e(f.e.class, ((String) objArr10[0]).intern());
                                        Object[] objArr11 = new Object[1];
                                        w(KeyEvent.keyCodeFromString("") + 5, "\u0005\u0004\ufff1\u0004\u0003\u0003", (ViewConfiguration.getTouchSlop() >> 8) + 6, 156 - (ViewConfiguration.getDoubleTapTimeout() >> 16), true, objArr11);
                                        f.d dVar = (f.d) b.d(f.d.class, ((String) objArr11[0]).intern(), f.d.c);
                                        Object[] objArr12 = new Object[1];
                                        w(TextUtils.lastIndexOf("", '0', 0, 0) + 15, "\ufff8\ufffa\u000b\u0000\r\ufff8\u000b\u0000\u0006\u0005￫\u0000\u0004￼", 14 - (KeyEvent.getMaxKeyCode() >> 16), 149 - (ViewConfiguration.getDoubleTapTimeout() >> 16), false, objArr12);
                                        Long n = b.n(((String) objArr12[0]).intern());
                                        Object[] objArr13 = new Object[1];
                                        w(3 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), "\t\u0001\u0000\u0001\u0012\u0005\uffff\u0001￪�", (Process.myPid() >> 22) + 10, 144 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), false, objArr13);
                                        String q5 = b.q(((String) objArr13[0]).intern());
                                        Object[] objArr14 = new Object[1];
                                        w(TextUtils.lastIndexOf("", '0', 0) + 5, "\uffff\u0005\t\u000e�\ufffb\uffe7\b", (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 7, 146 - (ViewConfiguration.getFadingEdgeLength() >> 16), true, objArr14);
                                        arrayList.add(new f(bVar3, r, q, q2, r2, q3, bVar2, aVar, eVar, dVar, n, q5, b.r(((String) objArr14[0]).intern())));
                                        i3++;
                                        int i6 = a + 23;
                                        d = i6 % 128;
                                        int i7 = i6 % 2;
                                }
                            }
                    }
                    break;
            }
            ((c) e()).e = arrayList;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.c
        public final void q() {
            /*
                r7 = this;
                o.y.b r0 = r7.e()
                o.af.c r0 = (o.af.c) r0
                java.util.List<o.eo.f> r0 = r0.e
                java.util.Iterator r0 = r0.iterator()
                int r1 = o.af.c.AsyncTaskC0017c.a
                int r1 = r1 + 121
                int r2 = r1 % 128
                o.af.c.AsyncTaskC0017c.d = r2
                int r1 = r1 % 2
            L17:
                boolean r1 = r0.hasNext()
                r2 = 0
                r3 = 1
                if (r1 == 0) goto L22
                r1 = r2
                goto L23
            L22:
                r1 = r3
            L23:
                switch(r1) {
                    case 1: goto L33;
                    default: goto L26;
                }
            L26:
                int r1 = o.af.c.AsyncTaskC0017c.d
                int r1 = r1 + 59
                int r4 = r1 % 128
                o.af.c.AsyncTaskC0017c.a = r4
                int r1 = r1 % 2
                if (r1 == 0) goto L34
                goto L34
            L33:
                return
            L34:
                java.lang.Object r1 = r0.next()
                o.eo.f r1 = (o.eo.f) r1
                o.du.b r4 = r1.i()
                if (r4 == 0) goto L42
                r4 = r2
                goto L43
            L42:
                r4 = r3
            L43:
                switch(r4) {
                    case 1: goto L17;
                    default: goto L46;
                }
            L46:
                int r4 = o.af.c.AsyncTaskC0017c.a
                int r4 = r4 + 115
                int r5 = r4 % 128
                o.af.c.AsyncTaskC0017c.d = r5
                int r4 = r4 % 2
                o.du.a r4 = o.du.a.a()     // Catch: o.du.e -> L60
                android.content.Context r5 = r7.g()     // Catch: o.du.e -> L60
                o.du.b r1 = r1.i()     // Catch: o.du.e -> L60
                r4.e(r5, r1)     // Catch: o.du.e -> L60
                goto L17
            L60:
                r1 = move-exception
                o.ee.g.c()
                java.lang.String r4 = r7.c()
                int r5 = android.view.ViewConfiguration.getKeyRepeatDelay()
                int r5 = r5 >> 16
                int r5 = r5 + 10937
                java.lang.Object[] r3 = new java.lang.Object[r3]
                java.lang.String r6 = "\ufadd큥꾓竬倵⽌嬨퇎꼉穣凧⼲頻톇곞稊允Ⲟ\ufbc3텺곦箲兴ⱸﮄ훂갚筝囫Ⱨﭨ횺궲笈噁\u2d9a\uf8de홪귤磉嘵\u2d78\uf88dퟏ굞硥埩ⴴ\uf877ힾꋣ砝坉⊍璉흁ꊥ秤圷"
                B(r6, r5, r3)
                r2 = r3[r2]
                java.lang.String r2 = (java.lang.String) r2
                java.lang.String r2 = r2.intern()
                o.ee.g.a(r4, r2, r1)
                goto L17
            */
            throw new UnsupportedOperationException("Method not decompiled: o.af.c.AsyncTaskC0017c.q():void");
        }

        /* JADX WARN: Multi-variable type inference failed */
        /* JADX WARN: Removed duplicated region for block: B:11:0x0041  */
        /* JADX WARN: Removed duplicated region for block: B:14:0x0055  */
        /* JADX WARN: Removed duplicated region for block: B:16:0x0069  */
        @Override // o.y.c
        /*
            Code decompiled incorrectly, please refer to instructions dump.
            To view partially-correct add '--show-bad-code' argument
        */
        public final void t() {
            /*
                r3 = this;
                int r0 = o.af.c.AsyncTaskC0017c.a
                int r0 = r0 + 101
                int r1 = r0 % 128
                o.af.c.AsyncTaskC0017c.d = r1
                int r0 = r0 % 2
                if (r0 != 0) goto Lf
                r0 = 48
                goto L11
            Lf:
                r0 = 39
            L11:
                r1 = 0
                switch(r0) {
                    case 39: goto L26;
                    default: goto L15;
                }
            L15:
                int[] r0 = o.af.c.AnonymousClass4.a
                o.bb.d r2 = r3.h()
                o.bb.a r2 = r2.d()
                int r2 = r2.ordinal()
                r0 = r0[r2]
                goto L3a
            L26:
                int[] r0 = o.af.c.AnonymousClass4.a
                o.bb.d r2 = r3.h()
                o.bb.a r2 = r2.d()
                int r2 = r2.ordinal()
                r0 = r0[r2]
                switch(r0) {
                    case 1: goto L55;
                    case 2: goto L41;
                    default: goto L39;
                }
            L39:
                goto L69
            L3a:
                r2 = 24
                int r2 = r2 / r1
                switch(r0) {
                    case 1: goto L55;
                    case 2: goto L41;
                    default: goto L40;
                }
            L40:
                goto L39
            L41:
                o.ei.c r0 = r3.f()
                android.content.Context r1 = r3.g()
                o.y.b r2 = r3.e()
                o.af.c r2 = (o.af.c) r2
                java.lang.String r2 = r2.c
                r0.e(r1, r2)
                return
            L55:
                o.ei.c r0 = r3.f()
                android.content.Context r1 = r3.g()
                o.y.b r2 = r3.e()
                o.af.c r2 = (o.af.c) r2
                java.lang.String r2 = r2.c
                r0.c(r1, r2)
                return
            L69:
                super.t()
                int r0 = o.af.c.AsyncTaskC0017c.d
                int r0 = r0 + 71
                int r2 = r0 % 128
                o.af.c.AsyncTaskC0017c.a = r2
                int r0 = r0 % 2
                if (r0 == 0) goto L79
                goto L7a
            L79:
                r1 = 1
            L7a:
                switch(r1) {
                    case 0: goto L7e;
                    default: goto L7d;
                }
            L7d:
                return
            L7e:
                r0 = 0
                r0.hashCode()     // Catch: java.lang.Throwable -> L83
                throw r0     // Catch: java.lang.Throwable -> L83
            L83:
                r0 = move-exception
                throw r0
            L85:
                r0 = move-exception
                throw r0
            */
            throw new UnsupportedOperationException("Method not decompiled: o.af.c.AsyncTaskC0017c.t():void");
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void a(d dVar) {
            int i = a + 49;
            d = i % 128;
            int i2 = i % 2;
            ((c) e()).j().c(((c) e()).e);
            int i3 = d + 65;
            a = i3 % 128;
            int i4 = i3 % 2;
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // o.y.a
        public final void e(d dVar) {
            int i = d + Opcodes.LSHR;
            a = i % 128;
            switch (i % 2 != 0 ? 'X' : '+') {
                case Opcodes.POP2 /* 88 */:
                    ((c) e()).j().a(dVar);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                default:
                    ((c) e()).j().a(dVar);
                    int i2 = d + 9;
                    a = i2 % 128;
                    switch (i2 % 2 != 0) {
                        case false:
                            return;
                        default:
                            int i3 = 55 / 0;
                            return;
                    }
            }
        }

        private static void w(int i, String str, int i2, int i3, boolean z, Object[] objArr) {
            char[] charArray;
            char[] cArr;
            int i4 = $11 + Opcodes.LUSHR;
            $10 = i4 % 128;
            if (i4 % 2 != 0) {
                throw null;
            }
            switch (str != null ? '9' : 'S') {
                case Opcodes.AASTORE /* 83 */:
                    charArray = str;
                    break;
                default:
                    charArray = str.toCharArray();
                    break;
            }
            char[] cArr2 = charArray;
            h hVar = new h();
            char[] cArr3 = new char[i2];
            hVar.a = 0;
            int i5 = $10 + 87;
            $11 = i5 % 128;
            int i6 = i5 % 2;
            while (true) {
                long j = 0;
                if (hVar.a >= i2) {
                    if (i > 0) {
                        hVar.c = i;
                        char[] cArr4 = new char[i2];
                        System.arraycopy(cArr3, 0, cArr4, 0, i2);
                        System.arraycopy(cArr4, 0, cArr3, i2 - hVar.c, hVar.c);
                        System.arraycopy(cArr4, hVar.c, cArr3, 0, i2 - hVar.c);
                    }
                    if (z) {
                        int i7 = $10 + 79;
                        $11 = i7 % 128;
                        switch (i7 % 2 == 0 ? 'E' : (char) 21) {
                            case 'E':
                                cArr = new char[i2];
                                hVar.a = 1;
                                break;
                            default:
                                cArr = new char[i2];
                                hVar.a = 0;
                                break;
                        }
                        while (hVar.a < i2) {
                            cArr[hVar.a] = cArr3[(i2 - hVar.a) - 1];
                            try {
                                Object[] objArr2 = {hVar, hVar};
                                Object obj = o.e.a.s.get(-1412673904);
                                if (obj == null) {
                                    Class cls = (Class) o.e.a.c(11 - Color.red(0), (char) ((ViewConfiguration.getZoomControlsTimeout() > j ? 1 : (ViewConfiguration.getZoomControlsTimeout() == j ? 0 : -1)) - 1), TextUtils.lastIndexOf("", '0', 0) + 314);
                                    byte b = (byte) (-1);
                                    Object[] objArr3 = new Object[1];
                                    C((byte) 5, b, (byte) (b + 1), objArr3);
                                    obj = cls.getMethod((String) objArr3[0], Object.class, Object.class);
                                    o.e.a.s.put(-1412673904, obj);
                                }
                                ((Method) obj).invoke(null, objArr2);
                                int i8 = $11 + 95;
                                $10 = i8 % 128;
                                int i9 = i8 % 2;
                                j = 0;
                            } catch (Throwable th) {
                                Throwable cause = th.getCause();
                                if (cause == null) {
                                    throw th;
                                }
                                throw cause;
                            }
                        }
                        cArr3 = cArr;
                    }
                    objArr[0] = new String(cArr3);
                    return;
                }
                hVar.b = cArr2[hVar.a];
                cArr3[hVar.a] = (char) (i3 + hVar.b);
                int i10 = hVar.a;
                try {
                    Object[] objArr4 = {Integer.valueOf(cArr3[i10]), Integer.valueOf(e)};
                    Object obj2 = o.e.a.s.get(2038615114);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(12 - (Process.myTid() >> 22), (char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 459 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)));
                        byte b2 = (byte) (-1);
                        Object[] objArr5 = new Object[1];
                        C((byte) 7, b2, (byte) (b2 + 1), objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE, Integer.TYPE);
                        o.e.a.s.put(2038615114, obj2);
                    }
                    cArr3[i10] = ((Character) ((Method) obj2).invoke(null, objArr4)).charValue();
                    try {
                        Object[] objArr6 = {hVar, hVar};
                        Object obj3 = o.e.a.s.get(-1412673904);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((ViewConfiguration.getWindowTouchSlop() >> 8) + 11, (char) (Process.myPid() >> 22), TextUtils.getOffsetAfter("", 0) + 313);
                            byte b3 = (byte) (-1);
                            Object[] objArr7 = new Object[1];
                            C((byte) 5, b3, (byte) (b3 + 1), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Object.class);
                            o.e.a.s.put(-1412673904, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0022. Please report as an issue. */
        private static void B(java.lang.String r16, int r17, java.lang.Object[] r18) {
            /*
                Method dump skipped, instructions count: 500
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.af.c.AsyncTaskC0017c.B(java.lang.String, int, java.lang.Object[]):void");
        }
    }

    /* renamed from: o.af.c$4, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\af\c$4.smali */
    static /* synthetic */ class AnonymousClass4 {
        static final /* synthetic */ int[] a;
        private static int b;
        private static int c = 1;

        static {
            b = 0;
            int[] iArr = new int[o.bb.a.values().length];
            a = iArr;
            try {
                iArr[o.bb.a.ay.ordinal()] = 1;
                int i = c;
                int i2 = (i & 21) + (i | 21);
                b = i2 % 128;
                if (i2 % 2 == 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                a[o.bb.a.az.ordinal()] = 2;
                int i3 = c;
                int i4 = (i3 & Opcodes.DNEG) + (i3 | Opcodes.DNEG);
                b = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e2) {
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(int r16, java.lang.String r17, int r18, int r19, boolean r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 486
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.af.c.k(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
