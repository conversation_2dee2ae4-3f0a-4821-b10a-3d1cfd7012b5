package android.support.v4.app;

import android.app.Notification;
import android.os.Binder;
import android.os.IBinder;
import android.os.Parcel;
import android.os.RemoteException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\android\support\v4\app\INotificationSideChannel$Stub.smali */
public abstract class INotificationSideChannel$Stub extends Binder implements INotificationSideChannel {
    static final int TRANSACTION_cancel = 2;
    static final int TRANSACTION_cancelAll = 3;
    static final int TRANSACTION_notify = 1;

    /* JADX WARN: Multi-variable type inference failed */
    public INotificationSideChannel$Stub() {
        attachInterface(this, DESCRIPTOR);
    }

    public static INotificationSideChannel asInterface(IBinder obj) {
        if (obj == null) {
            return null;
        }
        INotificationSideChannel queryLocalInterface = obj.queryLocalInterface(DESCRIPTOR);
        if (queryLocalInterface != null && (queryLocalInterface instanceof INotificationSideChannel)) {
            return queryLocalInterface;
        }
        return new Proxy(obj);
    }

    public IBinder asBinder() {
        return this;
    }

    @Override // android.os.Binder
    public boolean onTransact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
        Object readTypedObject;
        String descriptor = DESCRIPTOR;
        if (code >= 1 && code <= 16777215) {
            data.enforceInterface(descriptor);
        }
        if (code == 1598968902) {
            reply.writeString(descriptor);
            return true;
        }
        switch (code) {
            case 1:
                String _arg0 = data.readString();
                int _arg1 = data.readInt();
                String _arg2 = data.readString();
                readTypedObject = INotificationSideChannel$_Parcel.readTypedObject(data, Notification.CREATOR);
                Notification _arg3 = (Notification) readTypedObject;
                notify(_arg0, _arg1, _arg2, _arg3);
                return true;
            case 2:
                String _arg02 = data.readString();
                int _arg12 = data.readInt();
                String _arg22 = data.readString();
                cancel(_arg02, _arg12, _arg22);
                return true;
            case 3:
                String _arg03 = data.readString();
                cancelAll(_arg03);
                return true;
            default:
                return super.onTransact(code, data, reply, flags);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\android\support\v4\app\INotificationSideChannel$Stub$Proxy.smali */
    private static class Proxy implements INotificationSideChannel {
        private IBinder mRemote;

        Proxy(IBinder remote) {
            this.mRemote = remote;
        }

        public IBinder asBinder() {
            return this.mRemote;
        }

        public String getInterfaceDescriptor() {
            return DESCRIPTOR;
        }

        public void notify(String packageName, int id, String tag, Notification notification) throws RemoteException {
            Parcel _data = Parcel.obtain();
            try {
                _data.writeInterfaceToken(DESCRIPTOR);
                _data.writeString(packageName);
                _data.writeInt(id);
                _data.writeString(tag);
                INotificationSideChannel$_Parcel.writeTypedObject(_data, notification, 0);
                this.mRemote.transact(1, _data, null, 1);
            } finally {
                _data.recycle();
            }
        }

        public void cancel(String packageName, int id, String tag) throws RemoteException {
            Parcel _data = Parcel.obtain();
            try {
                _data.writeInterfaceToken(DESCRIPTOR);
                _data.writeString(packageName);
                _data.writeInt(id);
                _data.writeString(tag);
                this.mRemote.transact(2, _data, null, 1);
            } finally {
                _data.recycle();
            }
        }

        public void cancelAll(String packageName) throws RemoteException {
            Parcel _data = Parcel.obtain();
            try {
                _data.writeInterfaceToken(DESCRIPTOR);
                _data.writeString(packageName);
                this.mRemote.transact(3, _data, null, 1);
            } finally {
                _data.recycle();
            }
        }
    }
}
