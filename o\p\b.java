package o.p;

import java.util.List;
import o.i.f;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\b.smali */
public final class b {
    private static int i = 0;

    /* renamed from: o, reason: collision with root package name */
    private static int f95o = 1;
    private final String a;
    private final String b;
    private final String c;
    private final String d;
    private final List<f> e;
    private final String f;
    private final String g;
    private final Long h;
    private final Long j;

    public b(String str, List<f> list, String str2, String str3, String str4, String str5, String str6, Long l, Long l2) {
        this.d = str;
        this.e = list;
        this.a = str2;
        this.c = str3;
        this.b = str4;
        this.f = str5;
        this.g = str6;
        this.h = l;
        this.j = l2;
    }

    public final List<f> a() {
        int i2 = i;
        int i3 = (i2 ^ 83) + ((i2 & 83) << 1);
        int i4 = i3 % 128;
        f95o = i4;
        int i5 = i3 % 2;
        List<f> list = this.e;
        int i6 = (i4 ^ 93) + ((i4 & 93) << 1);
        i = i6 % 128;
        int i7 = i6 % 2;
        return list;
    }
}
