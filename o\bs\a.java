package o.bs;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.huawei.hms.api.HuaweiServicesNotAvailableException;
import com.huawei.hms.api.HuaweiServicesRepairableException;
import com.huawei.hms.security.SecComponentInstallWizard;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bs\a.smali */
public final class a implements c {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int b;
    private static int d;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        b = 1;
        e();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        ExpandableListView.getPackedPositionType(0L);
        ViewConfiguration.getDoubleTapTimeout();
        int i = d + Opcodes.LREM;
        b = i % 128;
        int i2 = i % 2;
    }

    static void e() {
        a = 874635467;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0038). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(short r7, short r8, short r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = r8 + 4
            int r9 = r9 * 2
            int r9 = 109 - r9
            byte[] r0 = o.bs.a.$$a
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L1b
            r9 = r8
            r3 = r1
            r4 = r2
            r8 = r7
            r1 = r0
            r0 = r10
            r10 = r9
            goto L38
        L1b:
            r3 = r2
            r6 = r9
            r9 = r8
            r8 = r6
        L1f:
            int r4 = r3 + 1
            byte r5 = (byte) r8
            r1[r3] = r5
            if (r4 != r7) goto L2e
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2e:
            r3 = r0[r9]
            r6 = r8
            r8 = r7
            r7 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r6
        L38:
            int r7 = r7 + r9
            int r9 = r10 + 1
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r6 = r8
            r8 = r7
            r7 = r6
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.a.g(short, short, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{70, -116, 4, 37};
        $$b = Opcodes.LOOKUPSWITCH;
    }

    @Override // o.bs.c
    public final i d(Context context) {
        i iVar;
        int i = b + 79;
        d = i % 128;
        int i2 = i % 2;
        o.ee.e.a();
        switch (o.ee.c.n(context)) {
            case 0:
                iVar = i.a;
                break;
            case 1:
                iVar = i.h;
                int i3 = d + 71;
                b = i3 % 128;
                if (i3 % 2 != 0) {
                    break;
                } else {
                    break;
                }
            case 2:
                iVar = i.b;
                break;
            case 3:
                iVar = i.d;
                break;
            case 9:
                iVar = i.e;
                break;
            case 21:
                iVar = i.i;
                break;
            default:
                iVar = i.h;
                break;
        }
        g.c();
        Object[] objArr = new Object[1];
        f(9 - View.combineMeasuredStates(0, 0), "￩\r￭\uffff\f\u0010\u0003�\uffff￢\u000f\ufffb\u0011\uffff\u0003\uffe7\t￼\u0003\u0006\uffff￭\uffff\f\u0010\u0003�\uffff\r", 29 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 282 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), false, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        f(Color.red(0) + 4, "\ufff9\u001a\u000b\rￆ￠ￆ\u0019\u001b\u001a\u0007\u001a", (ViewConfiguration.getLongPressTimeout() >> 16) + 12, 318 - AndroidCharacter.getMirror('0'), true, objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(iVar).toString());
        int i4 = b + 77;
        d = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    @Override // o.bs.c
    public final void c(Context context) {
        int i = b + Opcodes.DMUL;
        d = i % 128;
        int i2 = i % 2;
        try {
            SecComponentInstallWizard.install(context);
            int i3 = b + 3;
            d = i3 % 128;
            int i4 = i3 % 2;
        } catch (HuaweiServicesRepairableException | HuaweiServicesNotAvailableException e) {
            g.c();
            Object[] objArr = new Object[1];
            f(8 - Process.getGidForName(""), "￩\r￭\uffff\f\u0010\u0003�\uffff￢\u000f\ufffb\u0011\uffff\u0003\uffe7\t￼\u0003\u0006\uffff￭\uffff\f\u0010\u0003�\uffff\r", TextUtils.lastIndexOf("", '0', 0) + 30, 282 - TextUtils.indexOf("", "", 0), false, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            f(58 - (ViewConfiguration.getTouchSlop() >> 8), "\u000f\u0011\u0004\u000f\u0000\u0011\u0004\uffbfￌ\uffbf\u0004\u0017\u0002\u0004\u000f\u0013\b\u000e\r\uffbf\u0016\u0007\u0004\r\uffbf\u0014\u000f\u0003\u0000\u0013\b\r\u0006\uffbf\u0003\u0004\u0015\b\u0002\u0004\uffbf\u0012\u0004\u0002\u0014\u0011\b\u0013\u0018\uffbf\u000f\u0011\u000e\u0015\b\u0003\u0004\u0011", (Process.myTid() >> 22) + 58, 277 - (ViewConfiguration.getFadingEdgeLength() >> 16), false, objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e);
        }
    }

    @Override // o.bs.c
    public final String b() {
        int i = b + 39;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f(9 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), "￩\r￭\uffff\f\u0010\u0003�\uffff￢\u000f\ufffb\u0011\uffff\u0003\uffe7\t￼\u0003\u0006\uffff￭\uffff\f\u0010\u0003�\uffff\r", Color.rgb(0, 0, 0) + 16777245, 282 - View.MeasureSpec.makeMeasureSpec(0, 0), false, objArr);
        String intern = ((String) objArr[0]).intern();
        int i3 = d + Opcodes.DNEG;
        b = i3 % 128;
        int i4 = i3 % 2;
        return intern;
    }

    /* JADX WARN: Code restructure failed: missing block: B:46:0x016b, code lost:
    
        r0 = new char[r22];
        r3.a = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:48:0x0171, code lost:
    
        if (r3.a >= r22) goto L50;
     */
    /* JADX WARN: Code restructure failed: missing block: B:49:0x0173, code lost:
    
        r2 = true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:50:0x0176, code lost:
    
        switch(r2) {
            case 0: goto L74;
            default: goto L52;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:51:0x0179, code lost:
    
        r0[r3.a] = r4[(r22 - r3.a) - 1];
     */
    /* JADX WARN: Code restructure failed: missing block: B:53:0x018f, code lost:
    
        r2 = new java.lang.Object[]{r3, r3};
        r6 = o.e.a.s.get(-1412673904);
     */
    /* JADX WARN: Code restructure failed: missing block: B:54:0x01a0, code lost:
    
        if (r6 == null) goto L59;
     */
    /* JADX WARN: Code restructure failed: missing block: B:57:0x01e7, code lost:
    
        ((java.lang.reflect.Method) r6).invoke(null, r2);
     */
    /* JADX WARN: Code restructure failed: missing block: B:58:0x01a6, code lost:
    
        r6 = (java.lang.Class) o.e.a.c(11 - (android.view.ViewConfiguration.getKeyRepeatTimeout() >> 16), (char) (android.view.ViewConfiguration.getDoubleTapTimeout() >> 16), 312 - android.text.TextUtils.lastIndexOf("", '0'));
        r9 = (byte) 0;
        r10 = r9;
        r14 = new java.lang.Object[1];
        g(r9, r10, r10, r14);
        r6 = r6.getMethod((java.lang.String) r14[0], java.lang.Object.class, java.lang.Object.class);
        o.e.a.s.put(-1412673904, r6);
     */
    /* JADX WARN: Code restructure failed: missing block: B:61:0x01ee, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:62:0x01ef, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:63:0x01f3, code lost:
    
        if (r1 != null) goto L65;
     */
    /* JADX WARN: Code restructure failed: missing block: B:64:0x01f5, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:65:0x01f6, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:67:0x0185, code lost:
    
        r4 = r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:68:0x0175, code lost:
    
        r2 = false;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(int r20, java.lang.String r21, int r22, int r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 528
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bs.a.f(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
    }
}
