package o.ax;

import android.content.Context;
import android.graphics.Color;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;
import o.av.b;
import o.cf.i;
import o.dd.e;
import o.de.f;
import o.ei.c;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ax\a.smali */
public final class a extends b {
    private static char a;
    private static char c;
    private static char d;
    private static char e;
    private static int i;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 1;

    static {
        i = 0;
        n();
        ViewConfiguration.getFadingEdgeLength();
        Process.getGidForName("");
        int i2 = h + 23;
        i = i2 % 128;
        int i3 = i2 % 2;
    }

    static void n() {
        c = (char) 44779;
        d = (char) 52858;
        e = (char) 47434;
        a = (char) 31046;
    }

    public a(Context context, b.InterfaceC0029b interfaceC0029b, c cVar, f fVar) {
        super(context, interfaceC0029b, cVar, fVar);
    }

    @Override // o.y.b
    public final String a() {
        int i2 = h + 43;
        i = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        r("㬶涾醈뾥㧶굎蝓䦆챜奬탩룕", 11 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = h + Opcodes.DDIV;
        i = i4 % 128;
        int i5 = i4 % 2;
        return intern;
    }

    @Override // o.y.b
    public final o.y.a<?> b() {
        d dVar = new d(this);
        int i2 = h + 65;
        i = i2 % 128;
        switch (i2 % 2 != 0 ? (char) 28 : 'W') {
            case Opcodes.POP /* 87 */:
                return dVar;
            default:
                throw null;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ax\a$d.smali */
    public static final class d extends b.a<a> {
        public static final byte[] $$d = null;
        public static final int $$e = 0;
        private static int $10;
        private static int $11;
        private static int a;
        private static int d;
        private static int e;

        static {
            init$0();
            $10 = 0;
            $11 = 1;
            d = 0;
            a = 1;
            e = 874635452;
        }

        private static void B(byte b, short s, byte b2, Object[] objArr) {
            int i = (b * 2) + Opcodes.DMUL;
            byte[] bArr = $$d;
            int i2 = 1 - (s * 4);
            int i3 = (b2 * 2) + 4;
            byte[] bArr2 = new byte[i2];
            int i4 = -1;
            int i5 = i2 - 1;
            if (bArr == null) {
                i3++;
                int i6 = (-i5) + i3;
                objArr = objArr;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = -1;
                i5 = i5;
                i = i6;
            }
            while (true) {
                int i7 = i4 + 1;
                bArr2[i7] = (byte) i;
                if (i7 == i5) {
                    objArr[0] = new String(bArr2, 0);
                    return;
                }
                byte b3 = bArr[i3];
                Object[] objArr2 = objArr;
                int i8 = i;
                int i9 = i5;
                i3++;
                int i10 = (-b3) + i8;
                objArr = objArr2;
                bArr = bArr;
                bArr2 = bArr2;
                i4 = i7;
                i5 = i9;
                i = i10;
            }
        }

        static void init$0() {
            $$d = new byte[]{8, 72, -108, -33};
            $$e = 32;
        }

        @Override // o.y.c
        public final void a(o.eg.b bVar) throws o.eg.d {
            int i = d + 15;
            a = i % 128;
            switch (i % 2 == 0) {
                case false:
                    break;
                default:
                    int i2 = 93 / 0;
                    break;
            }
        }

        @Override // o.y.c
        public final void c(o.eg.b bVar) throws o.eg.d {
            int i = d + 83;
            a = i % 128;
            switch (i % 2 != 0) {
                case false:
                    int i2 = 10 / 0;
                    break;
            }
        }

        /* JADX WARN: 'super' call moved to the top of the method (can break code semantics) */
        d(a aVar) {
            super(aVar);
            KeyEvent.getMaxKeyCode();
            ExpandableListView.getPackedPositionChild(0L);
            ExpandableListView.getPackedPositionType(0L);
        }

        @Override // o.y.c
        public final String l() {
            int i = a + Opcodes.DMUL;
            d = i % 128;
            int i2 = i % 2;
            Object[] objArr = new Object[1];
            w(2 - Color.blue(0), "\ufffe\u0005￼\u0003", (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 4, ((Process.getThreadPriority(0) + 20) >> 6) + 302, true, objArr);
            String intern = ((String) objArr[0]).intern();
            int i3 = a + 59;
            d = i3 % 128;
            int i4 = i3 % 2;
            return intern;
        }

        @Override // o.y.c
        public final i c(Context context) {
            Object[] objArr = new Object[1];
            w(2 - TextUtils.indexOf((CharSequence) "", '0', 0), "\u0001\u0000\ufffe\u0001\u0000\u0004\ufffe\u0003￼\u0000\u0004�\ufffe�\u0000\u0004\u0003\u0000\u0004", (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 18, (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 247, true, objArr);
            o.cf.d dVar = new o.cf.d(context, 3, ((String) objArr[0]).intern());
            int i = d + 85;
            a = i % 128;
            switch (i % 2 != 0) {
                case true:
                    return dVar;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final o.eg.b m() throws o.eg.d {
            o.eg.b bVar = new o.eg.b();
            Object[] objArr = new Object[1];
            w((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 16, "\f\r￫\ufffe￼\ufffe\u0002\u000f\ufffe�￮\t�\ufffa\r\ufffe\u0005\ufffa", 17 - ((byte) KeyEvent.getModifierMetaStateMask()), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 298, false, objArr);
            bVar.d(((String) objArr[0]).intern(), f().e().g());
            int i = a + Opcodes.LSHL;
            d = i % 128;
            switch (i % 2 == 0) {
                case true:
                    return bVar;
                default:
                    throw null;
            }
        }

        @Override // o.y.c
        public final void b(o.eg.b bVar) throws o.eg.d {
            int i = d + 75;
            a = i % 128;
            int i2 = i % 2;
            o.eg.b e2 = f().a().e();
            Object[] objArr = new Object[1];
            w((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 10, "\u0005\ufff8\uffff\u0002\ufffb￩\n\ufff7\n\u000b\t\u0003", (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 12, 301 - KeyEvent.getDeadChar(0, 0), false, objArr);
            bVar.d(((String) objArr[0]).intern(), e2);
            int i3 = a + 29;
            d = i3 % 128;
            int i4 = i3 % 2;
        }

        @Override // o.y.c
        public final void s() {
            o.dc.c.b(g());
            f().a().f().e(new e(g()));
            int i = a + 31;
            d = i % 128;
            switch (i % 2 == 0) {
                case true:
                    return;
                default:
                    int i2 = 48 / 0;
                    return;
            }
        }

        @Override // o.y.c
        public final void q() {
            f().a().f().e(new e(g()));
            int i = a + 55;
            d = i % 128;
            int i2 = i % 2;
        }

        /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
            jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
            	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
            	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
            	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
            	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
            */
        private static void w(int r17, java.lang.String r18, int r19, int r20, boolean r21, java.lang.Object[] r22) {
            /*
                Method dump skipped, instructions count: 500
                To view this dump add '--comments-level debug' option
            */
            throw new UnsupportedOperationException("Method not decompiled: o.ax.a.d.w(int, java.lang.String, int, int, boolean, java.lang.Object[]):void");
        }
    }

    @Override // o.av.b
    public final boolean k() {
        int i2 = i;
        int i3 = i2 + Opcodes.DNEG;
        h = i3 % 128;
        int i4 = i3 % 2;
        int i5 = i2 + 73;
        h = i5 % 128;
        switch (i5 % 2 == 0 ? Typography.dollar : 'a') {
            case Opcodes.LADD /* 97 */:
                return false;
            default:
                throw null;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:124)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void r(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 582
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ax.a.r(java.lang.String, int, java.lang.Object[]):void");
    }
}
