package bc.org.bouncycastle.math.ec.endo;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\endo\GLVTypeAParameters.smali */
public class GLVTypeAParameters {
    protected final BigInteger a;
    protected final BigInteger b;
    protected final ScalarSplitParameters c;

    public GLVTypeAParameters(BigInteger bigInteger, BigInteger bigInteger2, ScalarSplitParameters scalarSplitParameters) {
        this.a = bigInteger;
        this.b = bigInteger2;
        this.c = scalarSplitParameters;
    }

    public BigInteger getI() {
        return this.a;
    }

    public BigInteger getLambda() {
        return this.b;
    }

    public ScalarSplitParameters getSplitParams() {
        return this.c;
    }
}
