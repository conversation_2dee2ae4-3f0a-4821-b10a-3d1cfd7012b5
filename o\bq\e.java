package o.bq;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import o.a.g;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bq\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final e a;
    public static final e b;
    private static e c;
    public static final e e;
    private static int f;
    private static int[] g;
    private static final /* synthetic */ e[] i;
    private static int j;
    private final int d;

    static void b() {
        g = new int[]{-1951086498, -1104842848, 2053629210, 252650406, 1785622162, 87077996, 1701136984, 211133856, -1565989447, -1377823684, 471789865, -452508063, -619600910, 1474747588, -342508599, 1583242058, 518443155, 87132229};
    }

    static void init$0() {
        $$a = new byte[]{71, -50, -52, -118};
        $$b = Opcodes.INVOKESPECIAL;
    }

    /* JADX WARN: Multi-variable type inference failed */
    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Type inference failed for: r6v2, types: [int] */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0028). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void k(short r5, short r6, int r7, java.lang.Object[] r8) {
        /*
            int r5 = 116 - r5
            int r6 = r6 * 3
            int r6 = r6 + 4
            int r7 = r7 * 2
            int r7 = 1 - r7
            byte[] r0 = o.bq.e.$$a
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L16
            r4 = r6
            r3 = r2
            goto L28
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r5
            r1[r3] = r4
            if (r3 != r7) goto L24
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L24:
            int r3 = r3 + 1
            r4 = r0[r6]
        L28:
            int r6 = r6 + 1
            int r4 = -r4
            int r5 = r5 + r4
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.e.k(short, short, int, java.lang.Object[]):void");
    }

    private static /* synthetic */ e[] d() {
        int i2 = j + 3;
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        e[] eVarArr = {e, a, b, c};
        int i5 = i3 + 37;
        j = i5 % 128;
        switch (i5 % 2 == 0 ? 'N' : ']') {
            case 'N':
                int i6 = 46 / 0;
                return eVarArr;
            default:
                return eVarArr;
        }
    }

    public static e valueOf(String str) {
        int i2 = f + Opcodes.LREM;
        j = i2 % 128;
        char c2 = i2 % 2 == 0 ? (char) 3 : 'Z';
        e eVar = (e) Enum.valueOf(e.class, str);
        switch (c2) {
            case 'Z':
                return eVar;
            default:
                throw null;
        }
    }

    public static e[] values() {
        int i2 = j + 97;
        f = i2 % 128;
        Object obj = null;
        switch (i2 % 2 == 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                e[] eVarArr = (e[]) i.clone();
                int i3 = f + Opcodes.LNEG;
                j = i3 % 128;
                switch (i3 % 2 == 0 ? 'M' : 'J') {
                    case 'J':
                        return eVarArr;
                    default:
                        obj.hashCode();
                        throw null;
                }
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        f = 0;
        j = 1;
        b();
        Object[] objArr = new Object[1];
        h(new int[]{1867547431, -1996129363, 45867311, 1468176812}, 7 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
        e = new e(((String) objArr[0]).intern(), 0, 1);
        Object[] objArr2 = new Object[1];
        h(new int[]{-141292607, 1101345625, 630488532, -728037545}, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 5, objArr2);
        a = new e(((String) objArr2[0]).intern(), 1, 3);
        Object[] objArr3 = new Object[1];
        h(new int[]{1867547431, -1996129363, 802802397, -926966567, -1979087087, -635798892}, 11 - Process.getGidForName(""), objArr3);
        b = new e(((String) objArr3[0]).intern(), 2, 4);
        Object[] objArr4 = new Object[1];
        h(new int[]{408665426, 1669152302, 1531124975, 216661886}, 7 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr4);
        c = new e(((String) objArr4[0]).intern(), 3, 9);
        i = d();
        int i2 = j + 109;
        f = i2 % 128;
        int i3 = i2 % 2;
    }

    private e(String str, int i2, int i3) {
        this.d = i3;
    }

    public final int c() {
        int i2 = f + 67;
        j = i2 % 128;
        switch (i2 % 2 == 0 ? '-' : '0') {
            case '-':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return this.d;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.bq.e a(int r6) {
        /*
            int r0 = o.bq.e.f
            int r0 = r0 + 73
            int r1 = r0 % 128
            o.bq.e.j = r1
            int r0 = r0 % 2
            r1 = 62
            if (r0 != 0) goto L11
            r0 = 22
            goto L12
        L11:
            r0 = r1
        L12:
            switch(r0) {
                case 22: goto L1c;
                default: goto L15;
            }
        L15:
            o.bq.e[] r0 = values()
            int r2 = r0.length
            r3 = 0
            goto L22
        L1c:
            o.bq.e[] r0 = values()
            int r2 = r0.length
            r3 = 1
        L22:
        L23:
            if (r3 >= r2) goto L28
            r4 = 50
            goto L2a
        L28:
            r4 = 77
        L2a:
            switch(r4) {
                case 50: goto L2f;
                default: goto L2d;
            }
        L2d:
            r6 = 0
            return r6
        L2f:
            r4 = r0[r3]
            int r5 = r4.c()
            if (r6 != r5) goto L39
            r5 = r1
            goto L3a
        L39:
            r5 = 7
        L3a:
            switch(r5) {
                case 7: goto L48;
                default: goto L3d;
            }
        L3d:
            int r6 = o.bq.e.j
            int r6 = r6 + 111
            int r0 = r6 % 128
            o.bq.e.f = r0
            int r6 = r6 % 2
            goto L4b
        L48:
            int r3 = r3 + 1
            goto L23
        L4b:
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bq.e.a(int):o.bq.e");
    }

    private static void h(int[] iArr, int i2, Object[] objArr) {
        int[] iArr2;
        int i3;
        g gVar = new g();
        char[] cArr = new char[4];
        int i4 = 2;
        char[] cArr2 = new char[iArr.length * 2];
        int[] iArr3 = g;
        int i5 = -1667374059;
        int i6 = 1;
        int i7 = 0;
        switch (iArr3 != null ? ':' : '\'') {
            case Opcodes.ASTORE /* 58 */:
                int length = iArr3.length;
                int[] iArr4 = new int[length];
                int i8 = 0;
                while (i8 < length) {
                    int i9 = $10 + 61;
                    $11 = i9 % 128;
                    int i10 = i9 % i4;
                    try {
                        Object[] objArr2 = new Object[1];
                        objArr2[i7] = Integer.valueOf(iArr3[i8]);
                        Object obj = o.e.a.s.get(Integer.valueOf(i5));
                        if (obj == null) {
                            Class cls = (Class) o.e.a.c(ImageFormat.getBitsPerPixel(i7) + 11, (char) (8856 - TextUtils.indexOf("", "", i7)), 323 - ExpandableListView.getPackedPositionChild(0L));
                            byte b2 = (byte) i7;
                            byte b3 = b2;
                            Object[] objArr3 = new Object[1];
                            k(b2, b3, b3, objArr3);
                            obj = cls.getMethod((String) objArr3[0], Integer.TYPE);
                            o.e.a.s.put(-1667374059, obj);
                        }
                        iArr4[i8] = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                        i8++;
                        int i11 = $10 + 1;
                        $11 = i11 % 128;
                        switch (i11 % 2 == 0) {
                            case false:
                            default:
                                i4 = 2;
                                i5 = -1667374059;
                                i7 = 0;
                        }
                    } catch (Throwable th) {
                        Throwable cause = th.getCause();
                        if (cause == null) {
                            throw th;
                        }
                        throw cause;
                    }
                }
                iArr3 = iArr4;
                break;
        }
        int length2 = iArr3.length;
        int[] iArr5 = new int[length2];
        int[] iArr6 = g;
        float f2 = 0.0f;
        int i12 = 16;
        if (iArr6 != null) {
            int length3 = iArr6.length;
            int[] iArr7 = new int[length3];
            int i13 = 0;
            while (i13 < length3) {
                try {
                    Object[] objArr4 = new Object[i6];
                    objArr4[0] = Integer.valueOf(iArr6[i13]);
                    Object obj2 = o.e.a.s.get(-1667374059);
                    if (obj2 != null) {
                        iArr2 = iArr6;
                        i3 = length3;
                    } else {
                        Class cls2 = (Class) o.e.a.c(10 - View.combineMeasuredStates(0, 0), (char) ((ViewConfiguration.getJumpTapTimeout() >> i12) + 8856), 325 - (ViewConfiguration.getScrollFriction() > f2 ? 1 : (ViewConfiguration.getScrollFriction() == f2 ? 0 : -1)));
                        byte b4 = (byte) 0;
                        byte b5 = b4;
                        iArr2 = iArr6;
                        i3 = length3;
                        Object[] objArr5 = new Object[1];
                        k(b4, b5, b5, objArr5);
                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                        o.e.a.s.put(-1667374059, obj2);
                    }
                    iArr7[i13] = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    i13++;
                    iArr6 = iArr2;
                    length3 = i3;
                    f2 = 0.0f;
                    i12 = 16;
                    i6 = 1;
                } catch (Throwable th2) {
                    Throwable cause2 = th2.getCause();
                    if (cause2 == null) {
                        throw th2;
                    }
                    throw cause2;
                }
            }
            iArr6 = iArr7;
        }
        System.arraycopy(iArr6, 0, iArr5, 0, length2);
        gVar.a = 0;
        while (gVar.a < iArr.length) {
            int i14 = $11 + 63;
            $10 = i14 % 128;
            int i15 = i14 % 2;
            cArr[0] = (char) (iArr[gVar.a] >> 16);
            cArr[1] = (char) iArr[gVar.a];
            cArr[2] = (char) (iArr[gVar.a + 1] >> 16);
            cArr[3] = (char) iArr[gVar.a + 1];
            gVar.e = (cArr[0] << 16) + cArr[1];
            gVar.c = (cArr[2] << 16) + cArr[3];
            g.d(iArr5);
            for (int i16 = 0; i16 < 16; i16++) {
                gVar.e ^= iArr5[i16];
                try {
                    Object[] objArr6 = {gVar, Integer.valueOf(g.b(gVar.e)), gVar, gVar};
                    Object obj3 = o.e.a.s.get(-2036901605);
                    if (obj3 == null) {
                        obj3 = ((Class) o.e.a.c(12 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), 572 - Color.red(0))).getMethod("q", Object.class, Integer.TYPE, Object.class, Object.class);
                        o.e.a.s.put(-2036901605, obj3);
                    }
                    int intValue = ((Integer) ((Method) obj3).invoke(null, objArr6)).intValue();
                    gVar.e = gVar.c;
                    gVar.c = intValue;
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            }
            int i17 = gVar.e;
            gVar.e = gVar.c;
            gVar.c = i17;
            gVar.c ^= iArr5[16];
            gVar.e ^= iArr5[17];
            int i18 = gVar.e;
            int i19 = gVar.c;
            cArr[0] = (char) (gVar.e >>> 16);
            cArr[1] = (char) gVar.e;
            cArr[2] = (char) (gVar.c >>> 16);
            cArr[3] = (char) gVar.c;
            g.d(iArr5);
            cArr2[gVar.a * 2] = cArr[0];
            cArr2[(gVar.a * 2) + 1] = cArr[1];
            cArr2[(gVar.a * 2) + 2] = cArr[2];
            cArr2[(gVar.a * 2) + 3] = cArr[3];
            try {
                Object[] objArr7 = {gVar, gVar};
                Object obj4 = o.e.a.s.get(-331007466);
                if (obj4 == null) {
                    Class cls3 = (Class) o.e.a.c(Color.red(0) + 12, (char) (55183 - Gravity.getAbsoluteGravity(0, 0)), 514 - TextUtils.indexOf((CharSequence) "", '0', 0));
                    byte b6 = (byte) ($$b & 1);
                    byte b7 = (byte) (b6 - 1);
                    Object[] objArr8 = new Object[1];
                    k(b6, b7, b7, objArr8);
                    obj4 = cls3.getMethod((String) objArr8[0], Object.class, Object.class);
                    o.e.a.s.put(-331007466, obj4);
                }
                ((Method) obj4).invoke(null, objArr7);
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr2, 0, i2);
    }
}
