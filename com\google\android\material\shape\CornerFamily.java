package com.google.android.material.shape;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.SOURCE)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\material\shape\CornerFamily.smali */
public @interface CornerFamily {
    public static final int CUT = 1;
    public static final int ROUNDED = 0;
}
