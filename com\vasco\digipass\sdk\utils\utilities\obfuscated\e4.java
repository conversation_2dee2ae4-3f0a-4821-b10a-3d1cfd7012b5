package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\e4.smali */
public class e4 extends u {
    private e0 b;

    private e4(e0 e0Var) {
        this.b = e0Var;
    }

    public static e4 a(Object obj) {
        if (obj instanceof e4) {
            return (e4) obj;
        }
        if (obj != null) {
            return new e4(e0.a(obj));
        }
        return null;
    }

    public BigInteger e() {
        return new BigInteger(1, ((x) this.b.a(1)).h());
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        return this.b;
    }
}
