package com.capacitorjs.plugins.network;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import androidx.appcompat.app.AppCompatActivity;
import com.capacitorjs.plugins.network.NetworkStatus;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\Network.smali */
public class Network {
    private ConnectivityCallback connectivityCallback = new ConnectivityCallback();
    private ConnectivityManager connectivityManager;
    private Context context;
    private BroadcastReceiver receiver;
    private NetworkStatusChangeListener statusChangeListener;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\Network$NetworkStatusChangeListener.smali */
    interface NetworkStatusChangeListener {
        void onNetworkStatusChanged(boolean z);
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\Network$ConnectivityCallback.smali */
    class ConnectivityCallback extends ConnectivityManager.NetworkCallback {
        ConnectivityCallback() {
        }

        @Override // android.net.ConnectivityManager.NetworkCallback
        public void onLost(android.net.Network network) {
            super.onLost(network);
            Network.this.statusChangeListener.onNetworkStatusChanged(true);
        }

        @Override // android.net.ConnectivityManager.NetworkCallback
        public void onCapabilitiesChanged(android.net.Network network, NetworkCapabilities networkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities);
            Network.this.statusChangeListener.onNetworkStatusChanged(false);
        }
    }

    public Network(Context context) {
        this.context = context;
        this.connectivityManager = (ConnectivityManager) context.getSystemService("connectivity");
    }

    /* renamed from: com.capacitorjs.plugins.network.Network$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes9\com\capacitorjs\plugins\network\Network$1.smali */
    class AnonymousClass1 extends BroadcastReceiver {
        AnonymousClass1() {
        }

        @Override // android.content.BroadcastReceiver
        public void onReceive(Context context, Intent intent) {
            Network.this.statusChangeListener.onNetworkStatusChanged(false);
        }
    }

    public void setStatusChangeListener(NetworkStatusChangeListener listener) {
        this.statusChangeListener = listener;
    }

    public NetworkStatusChangeListener getStatusChangeListener() {
        return this.statusChangeListener;
    }

    public NetworkStatus getNetworkStatus() {
        NetworkStatus networkStatus = new NetworkStatus();
        ConnectivityManager connectivityManager = this.connectivityManager;
        if (connectivityManager != null) {
            android.net.Network activeNetwork = connectivityManager.getActiveNetwork();
            ConnectivityManager connectivityManager2 = this.connectivityManager;
            NetworkCapabilities capabilities = connectivityManager2.getNetworkCapabilities(connectivityManager2.getActiveNetwork());
            if (activeNetwork != null && capabilities != null) {
                networkStatus.connected = capabilities.hasCapability(16) && capabilities.hasCapability(12);
                if (capabilities.hasTransport(1)) {
                    networkStatus.connectionType = NetworkStatus.ConnectionType.WIFI;
                } else if (capabilities.hasTransport(0)) {
                    networkStatus.connectionType = NetworkStatus.ConnectionType.CELLULAR;
                } else {
                    networkStatus.connectionType = NetworkStatus.ConnectionType.UNKNOWN;
                }
            }
        }
        return networkStatus;
    }

    private NetworkStatus getAndParseNetworkInfo() {
        NetworkStatus networkStatus = new NetworkStatus();
        NetworkInfo networkInfo = this.connectivityManager.getActiveNetworkInfo();
        if (networkInfo != null) {
            networkStatus.connected = networkInfo.isConnected();
            String typeName = networkInfo.getTypeName();
            if (typeName.equals("WIFI")) {
                networkStatus.connectionType = NetworkStatus.ConnectionType.WIFI;
            } else if (typeName.equals("MOBILE")) {
                networkStatus.connectionType = NetworkStatus.ConnectionType.CELLULAR;
            }
        }
        return networkStatus;
    }

    public void startMonitoring() {
        this.connectivityManager.registerDefaultNetworkCallback(this.connectivityCallback);
    }

    public void startMonitoring(AppCompatActivity activity) {
        IntentFilter filter = new IntentFilter("android.net.conn.CONNECTIVITY_CHANGE");
        activity.registerReceiver(this.receiver, filter);
    }

    public void stopMonitoring() {
        this.connectivityManager.unregisterNetworkCallback(this.connectivityCallback);
    }

    public void stopMonitoring(AppCompatActivity activity) {
        activity.unregisterReceiver(this.receiver);
    }
}
