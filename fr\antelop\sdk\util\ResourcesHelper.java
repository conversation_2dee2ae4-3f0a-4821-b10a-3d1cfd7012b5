package fr.antelop.sdk.util;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import androidx.core.app.ActivityCompat;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\util\ResourcesHelper.smali */
public final class ResourcesHelper {
    public static String trimExtensionFile(String str) {
        if (str == null) {
            return null;
        }
        if (str.contains(".")) {
            return str.substring(0, str.lastIndexOf(46));
        }
        return str;
    }

    public static Drawable resolveResourceAsDrawable(Context context, String str) {
        if (str == null) {
            return null;
        }
        try {
            return ActivityCompat.getDrawable(context, context.getResources().getIdentifier(trimExtensionFile(str), "drawable", context.getPackageName()));
        } catch (Resources.NotFoundException e) {
            return null;
        }
    }
}
