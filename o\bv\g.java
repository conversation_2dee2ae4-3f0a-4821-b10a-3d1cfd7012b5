package o.bv;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.WalletLockReason;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import kotlin.text.Typography;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\g.smali */
public final class g {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] s;
    private static long t;
    private static int v;
    private static int x;
    private boolean a;
    private boolean b;
    private boolean c;
    private boolean d;
    private boolean e;
    private Date f;
    private boolean g;
    private boolean h;
    private boolean i;
    private WalletLockReason j;
    private boolean k;
    private boolean n;
    private String q;
    private String r;
    private final List<o.el.d> l = new ArrayList();

    /* renamed from: o, reason: collision with root package name */
    private final List<o.dr.a> f44o = new ArrayList();
    private final List<o.de.d> m = new ArrayList();
    private final List<o.fk.a> p = new LinkedList();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        x = 0;
        v = 1;
        i();
        ViewConfiguration.getScrollFriction();
        KeyEvent.keyCodeFromString("");
        TextUtils.getTrimmedLength("");
        int i = x + 91;
        v = i % 128;
        switch (i % 2 != 0) {
            case false:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    static void i() {
        char[] cArr = new char[1072];
        ByteBuffer.wrap("ÄÓ3w+Ï\"?\u001a\u0091\u0012ï\tW\u0001Îx*pªhägP_¶V7N´Fì½Kµ¾¬\t¤i\u009cå\u008bY,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012K\u001a/\u0002½\t\u00161ü8Q #(\u0097×HßÌÆ^Î:ö\u008cý`åÊ,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012K\u001a/\u0002½\t\u00161ü8Q #(\u0097×@ßÆÆQÎ4ö\u009dýaå\u008eíæ\u0094t\u009c\u0083\u008b\u007f³Æ»³¢\u0002ª\u0098Q3Y\u0086Aù\u007f\u0086\u0088\u001b\u0090\u00ad\u0099D¡ù©\u0090²\"º\u008aÃ\\ËäÓ\u0092Ü/äÅí\rõ¥ýÇ\u0006,\u000eÖ\u0017`\u001f\u001a'\u00880<8\u0080ApI\u0014Q\u0086Z-bÇkjs\u0018{¬\u0084b\u008cü\u0095e\u009d\u000b¥ ®U¶ð¾\u0094\u0005ùòdêÒã;Û\u0086ÓïÈ]Àõ¹#±\u009b©í¦P\u009eº\u0097r\u008fÚ\u0087¸|St©m\u001fee]÷JCBÿ;\u000f3k+ù R\u0018¸\u0011\u0015\tg\u0001Óþ\u0004ö\u0082ï\u0011çtßÉÔ5\u0099HnÕvc\u007f\u008aG7O^Tì\\D%\u0092-*5\\:á\u0002\u000b\u000bÃ\u0013k\u001b\tàâè\u0018ñ®ùÔÁFÖòÞN§¾¯Ú·H¼ã\u0084\t\u008d¤\u0095Ö\u009dbb©j.s¨{ÎCxH\u0093P/XM!ô)t>\u008b\u00063\u000eA\u0017ý\u001fgÔ\u008c#\u0011;§2N\nó\u0002\u009a\u0019(\u0011\u0080hV`îx\u0098w%OÏF\u0007^¯VÍ\u00ad&¥Ü¼j´\u0010\u008c\u0082\u009b6\u0093\u008aêzâ\u001eú\u0098ñ3ÉÒÀxØ\u0018Ð¿/X'ê>@6\u001c\u000e¬\u0005P\u001dú\u0015\u0094l\u0011d©sJKúC\u0082Z\u000eR¢©Q¡è¹\u009c,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012K\u001a/\u0002®\t\u00121æ8T %(\u0086×IßÅÆ[Î8ö\u0091ýgåÇí§\u0094=\u009c\u0085\u008bc³ë»¯¢\u001eª\u0082Û»,&4\u0090=y\u0005Ä\r\u00ad\u0016\u001f\u001e·gaoÙw¯x\u0012@øI0Q\u0098Yú¢\u0011ªë³]»'\u0083µ\u0094\u0001\u009c½åMí)õ\u00adþ\u0001ÆæÏh×5ß\u008b y(Ê1@9\n\u0001\u009d\nk\u0012Í\u001a©c'k\u009b|yDÅLæUF]Ð¦q®Û¶«¿\u0001\u0087©\u0088\u0014\u0090\u0093è\u00ad\u001f0\u0007\u0086\u000eo6Ò>»%\t-¡Tw\\ÏD¹K\u0004sîz&b\u008ejì\u0091\u0007\u0099ý\u0080K\u00881°£§\u0017¯«Ö[Þ?Æ\u00adÍ\u0006õìüAä3ì\u0087\u0013O\u001bÜ\u0002V\n;2\u00819{!Ù)\u008eP4X\u0085OkwÃ\u007fµf\u0019,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012K\u001a/\u0002©\t\u00161â8Y 5(¶×|ßÍÆSÎ+ö\u009dýaØX/Å7s>\u009a\u0006'\u000eN\u0015ü\u001dTd\u0082l:tL{ñC\u001bJÓR{Z\u0019¡ò©\b°¾¸Ä\u0080V\u0097â\u009f^æ®îÊöLýýÅ\u0000Ì¶Ô×Üc#\u009b+?2\u0082:Ê\u0002y\t\u0081\u0011?\u0019K`Õ#IÔÔÌbÅ\u008bý6õ_îíæE\u009f\u0093\u0097+\u008f]\u0080à¸\n±Â©j¡\bZãR\u0019K¯CÕ{GlódO\u001d¿\u0015Û\r[\u0006î>\u00127\u0088/Â'gØ\u0094Ð4É¥ÁÊùxò\u0098ê5âQ\u009bã\u0093w\u0084\u008b¼7´Q\u00ad÷¥v^\u008eV)NAGå\u007f.p¬h%`K\u0019û\u0011\u0015\n±,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012K\u001a/\u0002¯\t\u001a1æ8| 6(\u0093×`ßÀÆQÎ>ö\u008cýlåÁí¥\u0094\u0015\u009c\u0092\u008bn³Î»¶¢\fª\u0082QzYÓA·H0pê\u007fYgÀo·\u0016\t\u001eá\u0005E\rj5±<\u001f$ïÓ\u0006Û&Ã\u0081Ê\u007fò²ù^á(é\u0095\u0090.\u0098\u0091\u0087\u0014,½Û Ã\u0096Ê\u007fòÂú«á\u0019é±\u0090g\u0098ß\u0080©\u008f\u0014·þ¾6¦\u009e®üU\u0017]íD[L!t³c\u0007k»\u0012G\u001a \u0002\u0098\t\u00131Å8M \"(\u0082×xßÌÆ{Î1ö\u009eýjåÝíë\u0093@dÝ|ku\u0082M?EV^äVL/\u009a'\"?T0é\b\u0003\u0001Ë\u0019c\u0011\u0001êêâ\u0010û¦óÜËNÜúÔF\u00ad¶¥Ò½C¶ø\u008e\f\u0087®\u009fÈ\u0097\u007fh\u0092` y¦qÍIkB\u008bZ\u0006RF+Í#m4\u0093\f?\u0004Y,§Û=Ã\u008bÊqòÃú·á8é\u0091\u0090e\u0098Î\u0080®\u008f\u000e·ê¾e¦æ®¬U\u001d]ãD[L-t±cVk²,§Û=Ã\u008bÊqòÃú·á(é\u0095\u0090c\u0098Þ\u0080´\u008f5·ý¾r¦Ò®¨U\u001c]æD\u0007La,§Û=Ã\u008bÊqòÃú·á(é\u009b\u0090d\u0098Ô\u0080³\u008f\u0005·ÿ¾e¦æ®¬U\u001d]ãD[L-t±cVk²,§Û=Ã\u008bÊqòÃú·á;é\u0095\u0090h\u0098×\u0080¢\u008f\u000e·ù¾]¦Ö®¥U\n]×D_L,t´c\nkþ\u0012\f\u001ah,§Û=Ã\u008bÊqòÃú·á<é\u0095\u0090}\u0098Ö\u0080¢\u008f\u0014·É¾s¦ß®¹U\r]çDKL`tüe\u0099\u0092\u0014\u008a¨\u0083w»é³\u008e¨# \u0085ÙWÑøÉ\u0097Æ,þÀ÷aïÿç±\u001c5\u0014Õ\re\u0005\r=\u008f*u\"\u0091['SOKéÍ\\:Æ\"p+\u008a\u00138\u001bL\u0000Ç\bnq\u0086y-aYnïV#_\u0083G$OH´á¼\u0012¥±\u00ad×\u0095\u0006\u0082¬,§Û=Ã\u008bÊqòÃú·á<é\u0095\u0090}\u0098Ö\u0080¢\u008f\u0014·Á¾y¦Ô®³U\f]öD\u0007La,§Û=Ã\u008bÊqòÃú·á;é\u0086\u0090~\u0098Þ\u0080²\u008f\u0003·ù¾C¦Ã®¸U\u0018]öDJL,týcW,ºÛ7Ã\u008bÊKòÐú á\u0018é\u0091\u0090e\u0098þ\u0080¦\u008f\u0014·è¾B¦Ü®\u0092U\u0016]öDFL.t¬cVk²\u0012\u0004\u001al\u0002ÊG\u0001°\u009b¨-¡×\u0099e\u0091\u0011\u008a\u008e\u0082 ûÒóxë\u0004ä¨Ü_ÕÙÍtÅ\u0016>¬6v/ì'\u009d\u001f\u0016\b¬\u0000\u0015y«\u008c\u0007{\u009dc+jÑRcZ\u0017A\u008eI80Ø8} \u000e/¢\u0017D\u001eÚ\u0006z\u000e\bõ ýnäàì\u009bÔ\u0001ÃöË\u0012,¼Û\"Ã\u009bÊyòÑú«á<é\u009d\u0090e\u0098Ò\u0080\u0090\u008f\u0001·á¾z¦Ö®¨U+]çD_L't§c\nk³\u0012\r8!Ï¿×\u0012ÞÁæCî4õ\u0096ý7\u0084÷\u008cQ\u0094\u001c\u009b\u008c£uªê²_º&A\u0084I#P\u008fXá`qw×\b7b\u0002\u0095\u008f\u008d3\u0084î¼r´\u0002¯º§*ÞÀÖaÎ\u001eÁ¬ù\\ðÁèeà \u001b \u0013N\nö\u0002Ø:D-æ%\u000e\\¼".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1072);
        s = cArr;
        t = -4028681843099182254L;
    }

    static void init$0() {
        $$a = new byte[]{98, -93, -101, -106};
        $$b = 99;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0024  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001c  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0024 -> B:4:0x0026). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void w(int r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 3
            int r6 = 1 - r6
            byte[] r0 = o.bv.g.$$a
            int r7 = 105 - r7
            int r8 = r8 * 2
            int r8 = 4 - r8
            byte[] r1 = new byte[r6]
            r2 = 0
            if (r0 != 0) goto L14
            r3 = r8
            r4 = r2
            goto L26
        L14:
            r3 = r2
        L15:
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r6) goto L24
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L24:
            r3 = r0[r8]
        L26:
            int r8 = r8 + 1
            int r7 = r7 + r3
            r3 = r4
            goto L15
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.g.w(int, int, int, java.lang.Object[]):void");
    }

    /* renamed from: o.bv.g$5, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\g$5.smali */
    static /* synthetic */ class AnonymousClass5 {
        static final /* synthetic */ int[] a;
        private static int d;
        private static int e;

        static {
            d = 0;
            e = 1;
            int[] iArr = new int[o.fk.c.values().length];
            a = iArr;
            try {
                iArr[o.fk.c.e.ordinal()] = 1;
                int i = e + Opcodes.LSUB;
                d = i % 128;
                int i2 = i % 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                a[o.fk.c.d.ordinal()] = 2;
            } catch (NoSuchFieldError e3) {
            }
            try {
                a[o.fk.c.b.ordinal()] = 3;
                int i3 = e;
                int i4 = (i3 & Opcodes.DSUB) + (i3 | Opcodes.DSUB);
                d = i4 % 128;
                int i5 = i4 % 2;
            } catch (NoSuchFieldError e4) {
            }
            try {
                a[o.fk.c.c.ordinal()] = 4;
            } catch (NoSuchFieldError e5) {
            }
            try {
                a[o.fk.c.a.ordinal()] = 5;
                int i6 = e + 79;
                d = i6 % 128;
                if (i6 % 2 != 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                a[o.fk.c.f.ordinal()] = 6;
            } catch (NoSuchFieldError e7) {
            }
            try {
                a[o.fk.c.g.ordinal()] = 7;
            } catch (NoSuchFieldError e8) {
            }
            try {
                a[o.fk.c.j.ordinal()] = 8;
                int i7 = d + 51;
                e = i7 % 128;
                int i8 = i7 % 2;
            } catch (NoSuchFieldError e9) {
            }
            try {
                a[o.fk.c.i.ordinal()] = 9;
            } catch (NoSuchFieldError e10) {
            }
            try {
                a[o.fk.c.h.ordinal()] = 10;
                int i9 = (e + 72) - 1;
                d = i9 % 128;
                int i10 = i9 % 2;
            } catch (NoSuchFieldError e11) {
            }
        }
    }

    public final void b(Context context) {
        Object obj;
        Object[] objArr = new Object[1];
        u((char) (59525 - AndroidCharacter.getMirror('0')), View.resolveSize(0, 0), 23 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        u((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), 22 - (ViewConfiguration.getScrollBarSize() >> 8), (Process.myPid() >> 22) + 13, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        new o.cd.e();
        if (this.a) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            u((char) TextUtils.getCapsMode("", 0, 0), View.resolveSize(0, 0) + 35, Gravity.getAbsoluteGravity(0, 0) + 38, objArr3);
            o.ee.g.d(intern, ((String) objArr3[0]).intern());
            o.cd.e.a(context).h(context);
        }
        float f = 0.0f;
        if (this.j != null) {
            o.ee.g.c();
            StringBuilder sb = new StringBuilder();
            Object[] objArr4 = new Object[1];
            u((char) (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 73 - (ViewConfiguration.getLongPressTimeout() >> 16), TextUtils.indexOf((CharSequence) "", '0', 0) + 50, objArr4);
            o.ee.g.d(intern, sb.append(((String) objArr4[0]).intern()).append(this.j).toString());
            o.cd.e.a(context).a(context, this.j);
        }
        if (this.i) {
            int i = x + 59;
            v = i % 128;
            int i2 = i % 2;
            o.ee.g.c();
            Object[] objArr5 = new Object[1];
            u((char) ((TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 21307), (ViewConfiguration.getEdgeSlop() >> 16) + Opcodes.ISHR, 39 - View.combineMeasuredStates(0, 0), objArr5);
            o.ee.g.d(intern, ((String) objArr5[0]).intern());
            o.cd.e.a(context).f(context);
        }
        if (this.h) {
            int i3 = v + 33;
            x = i3 % 128;
            int i4 = i3 % 2;
            o.ee.g.c();
            Object[] objArr6 = new Object[1];
            u((char) (10564 - TextUtils.getOffsetAfter("", 0)), TextUtils.lastIndexOf("", '0', 0, 0) + Opcodes.IF_ICMPGE, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 36, objArr6);
            o.ee.g.d(intern, ((String) objArr6[0]).intern());
            o.cd.e.a(context).j(context);
            int i5 = v + 9;
            x = i5 % 128;
            int i6 = i5 % 2;
        }
        switch (this.g) {
            case false:
                break;
            default:
                int i7 = v + Opcodes.DREM;
                x = i7 % 128;
                int i8 = i7 % 2;
                o.ee.g.c();
                Object[] objArr7 = new Object[1];
                u((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 46581), TextUtils.getOffsetBefore("", 0) + Opcodes.IFNULL, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 46, objArr7);
                o.ee.g.d(intern, ((String) objArr7[0]).intern());
                o.cd.e.a(context).c(context);
                break;
        }
        switch (this.k ? '[' : '-') {
            case '-':
                break;
            default:
                o.ee.g.c();
                Object[] objArr8 = new Object[1];
                u((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 63537), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 243, (ViewConfiguration.getFadingEdgeLength() >> 16) + 49, objArr8);
                o.ee.g.d(intern, ((String) objArr8[0]).intern());
                o.cd.e.a(context).i(context);
                break;
        }
        if (this.n) {
            o.ee.g.c();
            Object[] objArr9 = new Object[1];
            u((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 292, ExpandableListView.getPackedPositionType(0L) + 46, objArr9);
            o.ee.g.d(intern, ((String) objArr9[0]).intern());
            o.cd.e.a(context).m(context);
        }
        if (this.f != null) {
            o.ee.g.c();
            StringBuilder sb2 = new StringBuilder();
            Object[] objArr10 = new Object[1];
            u((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 63237), TextUtils.getOffsetBefore("", 0) + 339, ExpandableListView.getPackedPositionType(0L) + 53, objArr10);
            o.ee.g.d(intern, sb2.append(((String) objArr10[0]).intern()).append(this.f).toString());
            o.cd.e.a(context).d(context, this.f);
        }
        switch (!this.e) {
            case true:
                break;
            default:
                o.ee.g.c();
                Object[] objArr11 = new Object[1];
                u((char) ((ViewConfiguration.getScrollBarSize() >> 8) + 50192), 440 - AndroidCharacter.getMirror('0'), 45 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr11);
                o.ee.g.d(intern, ((String) objArr11[0]).intern());
                o.cd.e.a(context).d(context);
                break;
        }
        switch (this.b ? 'G' : 'I') {
            case 'G':
                int i9 = v + 47;
                x = i9 % 128;
                switch (i9 % 2 != 0) {
                    case false:
                        o.ee.g.c();
                        Object[] objArr12 = new Object[1];
                        u((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), 437 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 37 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr12);
                        obj = objArr12[0];
                        break;
                    default:
                        o.ee.g.c();
                        Object[] objArr13 = new Object[1];
                        u((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) * 1), 26238 % (AudioTrack.getMinVolume() > 2.0f ? 1 : (AudioTrack.getMinVolume() == 2.0f ? 0 : -1)), 32 / (AudioTrack.getMinVolume() > 2.0f ? 1 : (AudioTrack.getMinVolume() == 2.0f ? 0 : -1)), objArr13);
                        obj = objArr13[0];
                        break;
                }
                o.ee.g.d(intern, ((String) obj).intern());
                o.cd.e.a(context).b(context);
                break;
        }
        if (this.d) {
            o.ee.g.c();
            Object[] objArr14 = new Object[1];
            u((char) (62693 - TextUtils.getTrimmedLength("")), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 474, (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 39, objArr14);
            o.ee.g.d(intern, ((String) objArr14[0]).intern());
            o.cd.e.a(context).e(context);
        }
        if (this.c) {
            o.ee.g.c();
            Object[] objArr15 = new Object[1];
            u((char) (Color.green(0) + 4084), 514 - TextUtils.indexOf("", ""), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 57, objArr15);
            o.ee.g.d(intern, ((String) objArr15[0]).intern());
            o.cd.e.a(context).g(context);
        }
        for (o.el.d dVar : this.l) {
            o.ee.g.c();
            StringBuilder sb3 = new StringBuilder();
            char c = (char) (TypedValue.complexToFraction(0, f, f) > f ? 1 : (TypedValue.complexToFraction(0, f, f) == f ? 0 : -1));
            int i10 = 572 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1));
            int i11 = (AudioTrack.getMinVolume() > f ? 1 : (AudioTrack.getMinVolume() == f ? 0 : -1)) + 72;
            Object[] objArr16 = new Object[1];
            u(c, i10, i11, objArr16);
            o.ee.g.d(intern, sb3.append(((String) objArr16[0]).intern()).append(dVar.n()).toString());
            o.cd.e.a(context).e(context, dVar.n(), o.d(dVar.p() == null ? new ArrayList<>() : dVar.p().values()));
            f = 0.0f;
        }
        if (!this.p.isEmpty()) {
            o.ee.g.c();
            Object[] objArr17 = new Object[1];
            u((char) (1 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1))), KeyEvent.keyCodeFromString("") + 643, 39 - ((Process.getThreadPriority(0) + 20) >> 6), objArr17);
            o.ee.g.d(intern, ((String) objArr17[0]).intern());
            int i12 = v + Opcodes.LSHR;
            x = i12 % 128;
            int i13 = i12 % 2;
            for (o.fk.a aVar : c(this.p)) {
                switch (AnonymousClass5.a[aVar.c().ordinal()]) {
                    case 1:
                        o.u.b.a(context).b(aVar.e(), aVar.d());
                        break;
                    case 2:
                        o.u.b.a(context).a(aVar.e(), aVar.d());
                        break;
                    case 3:
                        o.u.b.a(context).d(aVar.e(), aVar.d());
                        break;
                    case 4:
                        o.u.b.a(context).e(aVar.e(), aVar.d());
                        break;
                    case 5:
                        o.u.b.a(context).c(aVar.e(), aVar.d());
                        break;
                    case 6:
                        o.u.b.a(context).g(aVar.e(), aVar.d());
                        break;
                    case 7:
                        o.u.b.a(context).f(aVar.e(), aVar.d());
                        break;
                    case 8:
                        o.u.b.a(context).j(aVar.e(), aVar.d());
                        break;
                    case 9:
                        o.u.b.a(context).i(aVar.e(), aVar.d());
                        break;
                    case 10:
                        o.u.b.a(context).h(aVar.e(), aVar.d());
                        break;
                }
            }
        }
        if (!this.f44o.isEmpty()) {
            o.ee.g.c();
            Object[] objArr18 = new Object[1];
            u((char) (Color.red(0) + 49149), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 683, TextUtils.getOffsetBefore("", 0) + 44, objArr18);
            o.ee.g.d(intern, ((String) objArr18[0]).intern());
            o.dj.c.b(context).e(context, this.f44o);
        }
        o();
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<o.fk.a> c(java.util.List<o.fk.a> r8) {
        /*
            java.util.LinkedHashSet r0 = new java.util.LinkedHashSet
            r0.<init>(r8)
            java.util.LinkedList r8 = new java.util.LinkedList
            r8.<init>(r0)
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.Iterator r1 = r8.iterator()
        L15:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L1e
            r2 = 53
            goto L20
        L1e:
            r2 = 93
        L20:
            r3 = 37
            r4 = 1
            r5 = 0
            switch(r2) {
                case 53: goto L2c;
                default: goto L27;
            }
        L27:
            java.util.Iterator r1 = r8.iterator()
            goto L8f
        L2c:
            int r2 = o.bv.g.x
            int r2 = r2 + 99
            int r6 = r2 % 128
            o.bv.g.v = r6
            int r2 = r2 % 2
            java.lang.Object r2 = r1.next()
            o.fk.a r2 = (o.fk.a) r2
            o.fk.c r6 = r2.c()
            o.fk.c r7 = o.fk.c.b
            if (r6 == r7) goto L7d
            int r6 = o.bv.g.x
            int r6 = r6 + 105
            int r7 = r6 % 128
            o.bv.g.v = r7
            int r6 = r6 % 2
            if (r6 != 0) goto L51
            goto L53
        L51:
            r3 = 16
        L53:
            switch(r3) {
                case 16: goto L5c;
                default: goto L56;
            }
        L56:
            r2.c()
            o.fk.c r8 = o.fk.c.a
            goto L79
        L5c:
            o.fk.c r3 = r2.c()
            o.fk.c r6 = o.fk.c.a
            if (r3 == r6) goto L65
            r4 = r5
        L65:
            switch(r4) {
                case 1: goto L7d;
                default: goto L68;
            }
        L68:
            o.fk.c r3 = r2.c()
            o.fk.c r4 = o.fk.c.c
            if (r3 == r4) goto L7d
            o.fk.c r3 = r2.c()
            o.fk.c r4 = o.fk.c.i
            if (r3 != r4) goto L8e
            goto L7d
        L79:
            r8 = 0
            throw r8     // Catch: java.lang.Throwable -> L7b
        L7b:
            r8 = move-exception
            throw r8
        L7d:
            java.lang.String r2 = r2.e()
            r0.add(r2)
            int r2 = o.bv.g.x
            int r2 = r2 + 11
            int r3 = r2 % 128
            o.bv.g.v = r3
            int r2 = r2 % 2
        L8e:
            goto L15
        L8f:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L97
            r2 = r4
            goto L98
        L97:
            r2 = r5
        L98:
            switch(r2) {
                case 0: goto La7;
                default: goto L9b;
            }
        L9b:
            int r2 = o.bv.g.v
            int r2 = r2 + r3
            int r6 = r2 % 128
            o.bv.g.x = r6
            int r2 = r2 % 2
            if (r2 == 0) goto La8
            goto La8
        La7:
            return r8
        La8:
            java.lang.Object r2 = r1.next()
            o.fk.a r2 = (o.fk.a) r2
            o.fk.c r6 = r2.c()
            o.fk.c r7 = o.fk.c.g
            if (r6 != r7) goto Lc4
            java.lang.String r2 = r2.e()
            boolean r2 = r0.contains(r2)
            if (r2 == 0) goto Lc4
            r1.remove()
        Lc4:
            goto L8f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.g.c(java.util.List):java.util.List");
    }

    private void o() {
        int i = x + Opcodes.DREM;
        v = i % 128;
        int i2 = i % 2;
        this.e = false;
        this.b = false;
        this.d = false;
        this.c = false;
        this.a = false;
        this.j = null;
        this.i = false;
        this.h = false;
        this.g = false;
        this.f = null;
        this.k = false;
        this.n = false;
        this.l.clear();
        this.f44o.clear();
        this.p.clear();
        int i3 = v + Opcodes.DMUL;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    private void n() {
        int i = v + Opcodes.LMUL;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 59477), TextUtils.indexOf("", "", 0), Process.getGidForName("") + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) (KeyEvent.getMaxKeyCode() >> 16), 725 - TextUtils.indexOf((CharSequence) "", '0'), 23 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.e = true;
        int i3 = x + 49;
        v = i3 % 128;
        switch (i3 % 2 != 0 ? 'R' : (char) 16) {
            case Opcodes.DASTORE /* 82 */:
                return;
            default:
                int i4 = 83 / 0;
                return;
        }
    }

    private void m() {
        int i = v + Opcodes.LMUL;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((ViewConfiguration.getLongPressTimeout() >> 16) + 59477), ViewConfiguration.getMaximumFlingVelocity() >> 16, 22 - (ViewConfiguration.getPressedStateDuration() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) (Process.myPid() >> 22), 749 - KeyEvent.keyCodeFromString(""), 20 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.b = true;
        int i3 = x + 33;
        v = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void c() {
        int i = v + 97;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((-16717739) - Color.rgb(0, 0, 0)), (-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), KeyEvent.normalizeMetaState(0) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) (ImageFormat.getBitsPerPixel(0) + 1), (ViewConfiguration.getScrollBarSize() >> 8) + 769, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 22, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.d = true;
        int i3 = x + Opcodes.DSUB;
        v = i3 % 128;
        int i4 = i3 % 2;
    }

    private void k() {
        int i = x + 67;
        v = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - Gravity.getAbsoluteGravity(0, 0)), ViewConfiguration.getScrollBarFadeDuration() >> 16, (ViewConfiguration.getLongPressTimeout() >> 16) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) Color.blue(0), 792 - View.combineMeasuredStates(0, 0), TextUtils.indexOf("", "") + 25, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.c = true;
        int i3 = v + 27;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void e() {
        int i = v + 53;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 59477), Color.red(0), TextUtils.lastIndexOf("", '0') + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) Color.blue(0), (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 816, 21 - (ViewConfiguration.getEdgeSlop() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.a = true;
        int i3 = v + 73;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void b(WalletLockReason walletLockReason) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - (ViewConfiguration.getFadingEdgeLength() >> 16)), View.MeasureSpec.getMode(0), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u((char) (18723 - (ViewConfiguration.getWindowTouchSlop() >> 8)), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 838, 26 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(walletLockReason.name()).toString());
        this.j = walletLockReason;
        int i = v + Opcodes.DDIV;
        x = i % 128;
        int i2 = i % 2;
    }

    public final void b() {
        int i = x + 33;
        v = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - TextUtils.getOffsetBefore("", 0)), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) - 1, 22 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) (ExpandableListView.getPackedPositionType(0L) + 57851), (Process.myPid() >> 22) + 864, 23 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.i = true;
        int i3 = x + 1;
        v = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    public final void d() {
        int i = x + 33;
        v = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (Color.rgb(0, 0, 0) + 16836693), ViewConfiguration.getKeyRepeatDelay() >> 16, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) View.getDefaultSize(0, 0), 885 - Process.getGidForName(""), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 20, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.h = true;
        int i3 = v + Opcodes.LMUL;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    private void l() {
        int i = x + 41;
        v = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 59477), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), 23 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 906, Drawable.resolveOpacity(0, 0) + 22, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.g = true;
        int i3 = x + 69;
        v = i3 % 128;
        switch (i3 % 2 == 0 ? '\'' : (char) 31) {
            case 31:
                return;
            default:
                throw null;
        }
    }

    public final void c(Date date) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 59477), Process.myPid() >> 22, (ViewConfiguration.getLongPressTimeout() >> 16) + 22, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 927, Color.red(0) + 26, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(date).toString());
        this.f = date;
        int i = x + 71;
        v = i % 128;
        int i2 = i % 2;
    }

    public final void a() {
        int i = v + 53;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (KeyEvent.keyCodeFromString("") + 59477), ViewConfiguration.getPressedStateDuration() >> 16, 23 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) (27558 - (Process.myTid() >> 22)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 954, (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 24, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.k = true;
        int i3 = x + 45;
        v = i3 % 128;
        switch (i3 % 2 == 0) {
            case true:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void j() {
        int i = x + 31;
        v = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (Color.alpha(0) + 59477), Color.blue(0), Color.rgb(0, 0, 0) + 16777238, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) ((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 41119), 978 - View.resolveSize(0, 0), 23 - View.resolveSizeAndState(0, 0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.n = true;
        int i3 = v + 99;
        x = i3 % 128;
        switch (i3 % 2 != 0 ? ';' : 'O') {
            case ';':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    public final void a(o.fk.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - View.combineMeasuredStates(0, 0)), TextUtils.lastIndexOf("", '0', 0) + 1, 21 - TextUtils.indexOf((CharSequence) "", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        u((char) ((-1) - TextUtils.lastIndexOf("", '0', 0)), 1002 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 23, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (dVar.h().b()) {
            case true:
                int i = x + 19;
                v = i % 128;
                if (i % 2 == 0) {
                    n();
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                n();
                break;
        }
        if (dVar.h().d()) {
            c();
            int i2 = x + Opcodes.DREM;
            v = i2 % 128;
            int i3 = i2 % 2;
        }
        switch (dVar.h().g() ? false : true) {
            case true:
                break;
            default:
                l();
                break;
        }
        switch (dVar.a() ? 'A' : 'R') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                k();
                break;
        }
        e(dVar.i());
        this.f44o.clear();
        this.f44o.addAll(dVar.e());
        int i4 = x + 89;
        v = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void e(o.fk.b bVar) {
        if (bVar.a()) {
            int i = x + 35;
            v = i % 128;
            int i2 = i % 2;
            m();
        }
        switch (!bVar.i().isEmpty()) {
            case false:
                break;
            default:
                int i3 = v + Opcodes.DNEG;
                x = i3 % 128;
                if (i3 % 2 == 0) {
                    this.l.addAll(bVar.i());
                    break;
                } else {
                    this.l.addAll(bVar.i());
                    int i4 = 82 / 0;
                    break;
                }
        }
        switch (bVar.d().isEmpty() ? false : true) {
            case false:
                return;
            default:
                int i5 = v + 61;
                x = i5 % 128;
                int i6 = i5 % 2;
                this.p.addAll(bVar.d());
                int i7 = x + 67;
                v = i7 % 128;
                int i8 = i7 % 2;
                return;
        }
    }

    public final void e(o.de.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (-1) - MotionEvent.axisFromString(""), TextUtils.indexOf((CharSequence) "", '0', 0) + 23, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u((char) (ExpandableListView.getPackedPositionType(0L) + 5257), 1026 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 22 - Color.alpha(0), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(dVar.c().name());
        Object[] objArr3 = new Object[1];
        u((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 9438), (ViewConfiguration.getWindowTouchSlop() >> 8) + 1047, -ExpandableListView.getPackedPositionChild(0L), objArr3);
        o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).append(dVar.e().name()).toString());
        this.m.add(dVar);
        int i = x + Opcodes.DSUB;
        v = i % 128;
        switch (i % 2 == 0 ? '_' : 'B') {
            case Opcodes.SWAP /* 95 */:
                throw null;
            default:
                return;
        }
    }

    public final List<o.de.d> h() {
        int i = x + 45;
        int i2 = i % 128;
        v = i2;
        int i3 = i % 2;
        List<o.de.d> list = this.m;
        int i4 = i2 + 33;
        x = i4 % 128;
        int i5 = i4 % 2;
        return list;
    }

    public final void a(String str, String str2) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        u((char) (59477 - (ViewConfiguration.getJumpTapTimeout() >> 16)), View.MeasureSpec.getMode(0), 21 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        u((char) (20152 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1))), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 1048, 24 - Color.red(0), objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(str);
        Object[] objArr3 = new Object[1];
        u((char) (ImageFormat.getBitsPerPixel(0) + 9439), Color.blue(0) + 1047, 1 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr3);
        o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).append(str2).toString());
        this.r = str;
        this.q = str2;
        int i = x + 43;
        v = i % 128;
        switch (i % 2 == 0 ? (char) 4 : '=') {
            case 4:
                throw null;
            default:
                return;
        }
    }

    public final String g() {
        String str;
        int i = v + 69;
        int i2 = i % 128;
        x = i2;
        switch (i % 2 != 0) {
            case true:
                str = this.r;
                int i3 = 34 / 0;
                break;
            default:
                str = this.r;
                break;
        }
        int i4 = i2 + 45;
        v = i4 % 128;
        int i5 = i4 % 2;
        return str;
    }

    public final String f() {
        int i = v + 53;
        int i2 = i % 128;
        x = i2;
        int i3 = i % 2;
        String str = this.q;
        int i4 = i2 + 25;
        v = i4 % 128;
        switch (i4 % 2 == 0 ? ')' : Typography.less) {
            case '<':
                return str;
            default:
                int i5 = 40 / 0;
                return str;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void u(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 1106
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.g.u(char, int, int, java.lang.Object[]):void");
    }
}
