package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.util.Arrays;
import java.io.IOException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\j0.smali */
public abstract class j0 extends b0 implements b5 {
    final int C;
    final h L;
    final int b;
    final int x;

    protected j0(boolean z, int i, h hVar) {
        this(z, 128, i, hVar);
    }

    static b0 b(int i, int i2, i iVar) {
        return iVar.b() == 1 ? new d1(3, i, i2, iVar.a(0)) : new d1(4, i, i2, w0.a(iVar));
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b5
    public final b0 a() {
        return this;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    final boolean a(b0 b0Var) {
        if (!(b0Var instanceof j0)) {
            return false;
        }
        j0 j0Var = (j0) b0Var;
        if (this.C != j0Var.C || this.x != j0Var.x) {
            return false;
        }
        if (this.b != j0Var.b && k() != j0Var.k()) {
            return false;
        }
        b0 aSN1Primitive = this.L.toASN1Primitive();
        b0 aSN1Primitive2 = j0Var.L.toASN1Primitive();
        if (aSN1Primitive == aSN1Primitive2) {
            return true;
        }
        if (k()) {
            return aSN1Primitive.a(aSN1Primitive2);
        }
        try {
            return Arrays.areEqual(getEncoded(), j0Var.getEncoded());
        } catch (IOException e) {
            return false;
        }
    }

    abstract e0 c(b0 b0Var);

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 f() {
        return new m2(this.b, this.x, this.C, this.L);
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0
    b0 g() {
        return new h3(this.b, this.x, this.C, this.L);
    }

    public u h() {
        if (!k()) {
            throw new IllegalStateException("object implicit - explicit expected.");
        }
        h hVar = this.L;
        return hVar instanceof u ? (u) hVar : hVar.toASN1Primitive();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.b0, com.vasco.digipass.sdk.utils.utilities.obfuscated.u
    public int hashCode() {
        return (((this.x * 7919) ^ this.C) ^ (k() ? 15 : 240)) ^ this.L.toASN1Primitive().hashCode();
    }

    public int i() {
        return this.x;
    }

    public int j() {
        return this.C;
    }

    public boolean k() {
        int i = this.b;
        return i == 1 || i == 3;
    }

    public String toString() {
        return p0.a(this.x, this.C) + this.L;
    }

    protected j0(boolean z, int i, int i2, h hVar) {
        this(z ? 1 : 2, i, i2, hVar);
    }

    j0(int i, int i2, int i3, h hVar) {
        if (hVar == null) {
            throw new NullPointerException("'obj' cannot be null");
        }
        if (i2 != 0 && (i2 & 192) == i2) {
            this.b = hVar instanceof g ? 1 : i;
            this.x = i2;
            this.C = i3;
            this.L = hVar;
            return;
        }
        throw new IllegalArgumentException("invalid tag class: " + i2);
    }

    b0 a(boolean z, o0 o0Var) {
        if (z) {
            if (k()) {
                return o0Var.a(this.L.toASN1Primitive());
            }
            throw new IllegalStateException("object explicit - implicit expected.");
        }
        if (1 != this.b) {
            b0 aSN1Primitive = this.L.toASN1Primitive();
            int i = this.b;
            if (i == 3) {
                return o0Var.a(c(aSN1Primitive));
            }
            if (i != 4) {
                return o0Var.a(aSN1Primitive);
            }
            if (aSN1Primitive instanceof e0) {
                return o0Var.a((e0) aSN1Primitive);
            }
            return o0Var.a((f2) aSN1Primitive);
        }
        throw new IllegalStateException("object explicit - implicit expected.");
    }

    static b0 a(int i, int i2, i iVar) {
        if (iVar.b() == 1) {
            return new h3(3, i, i2, iVar.a(0));
        }
        return new h3(4, i, i2, b3.a(iVar));
    }

    static b0 a(int i, int i2, byte[] bArr) {
        return new h3(4, i, i2, new f2(bArr));
    }
}
