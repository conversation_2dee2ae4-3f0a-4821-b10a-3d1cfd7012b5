package o.es;

import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\es\a.smali */
public final class a<T> {
    private static int c = 0;
    private static int f = 1;
    private final o.eo.e a;
    private final Boolean b;
    private final T d;
    private final b e;

    public a(T t, b bVar, Boolean bool, o.eo.e eVar) {
        this.d = t;
        this.e = bVar;
        this.b = bool;
        this.a = eVar;
    }

    public final b e() {
        int i = f + 13;
        c = i % 128;
        switch (i % 2 != 0 ? (char) 19 : 'Q') {
            case Opcodes.FASTORE /* 81 */:
                return this.e;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final T d() {
        int i = c;
        int i2 = i + 33;
        f = i2 % 128;
        int i3 = i2 % 2;
        T t = this.d;
        int i4 = (i & 21) + (i | 21);
        f = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                throw null;
            default:
                return t;
        }
    }

    public final Boolean b() {
        int i = c;
        int i2 = (i & Opcodes.DDIV) + (i | Opcodes.DDIV);
        int i3 = i2 % 128;
        f = i3;
        int i4 = i2 % 2;
        Boolean bool = this.b;
        int i5 = i3 + 29;
        c = i5 % 128;
        switch (i5 % 2 != 0) {
            case false:
                return bool;
            default:
                throw null;
        }
    }

    public final o.eo.e c() {
        int i = (f + 104) - 1;
        c = i % 128;
        switch (i % 2 == 0) {
            case true:
                return this.a;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }
}
