package androidx.constraintlayout.solver.widgets;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\androidx\constraintlayout\solver\widgets\ResolutionDimension.smali */
public class ResolutionDimension extends ResolutionNode {
    float value = 0.0f;

    @Override // androidx.constraintlayout.solver.widgets.ResolutionNode
    public void reset() {
        super.reset();
        this.value = 0.0f;
    }

    public void resolve(int value) {
        if (this.state == 0 || this.value != value) {
            this.value = value;
            if (this.state == 1) {
                invalidate();
            }
            didResolve();
        }
    }

    public void remove() {
        this.state = 2;
    }
}
