package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import bc.org.bouncycastle.crypto.BlockCipher;
import bc.org.bouncycastle.crypto.DataLengthException;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\k7.smali */
public abstract class k7 extends v3 implements l7 {
    private final BlockCipher a;

    protected k7(BlockCipher blockCipher) {
        this.a = blockCipher;
    }

    protected abstract byte a(byte b);

    public int a(byte[] bArr, int i, int i2, byte[] bArr2, int i3) throws DataLengthException {
        int i4 = i + i2;
        if (i4 > bArr.length) {
            throw new DataLengthException("input buffer too small");
        }
        if (i3 + i2 > bArr2.length) {
            throw new g6("output buffer too short");
        }
        while (i < i4) {
            bArr2[i3] = a(bArr[i]);
            i3++;
            i++;
        }
        return i2;
    }
}
