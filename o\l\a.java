package o.l;

import android.content.Context;
import android.os.CancellationSignal;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import fr.antelop.sdk.R;
import java.util.Date;
import o.ee.o;
import o.f.e;
import o.i.e;
import o.l.c;
import o.o.b;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\l\a.smali */
public final class a extends o.o.c {
    private static final int a;
    private static final int b;
    private static final int c;
    private static final int d;
    private static final int e;
    private static char[] l;
    private static int m;
    private static int n = 0;
    private final String f;
    private b g;
    private FragmentManager h;
    private final String i;
    private c j;
    private final e k;

    /* renamed from: o, reason: collision with root package name */
    private final String f91o;

    static void h() {
        l = new char[]{50935, 50873, 50878, 50876, 50851, 50852, 50854, 50839, 50836, 50851, 50877, 50849, 50852, 50876, 50863, 50860, 50877, 50851, 50851, 50879, 50835, 50838, 50851, 50852, 50854, 50839};
    }

    static {
        m = 1;
        h();
        b = R.string.antelopConsentPromptName;
        c = R.string.antelopConsentPromptDefaultTitle;
        d = R.string.antelopConsentPromptDefaultSubtitle;
        e = R.string.antelopConsentPromptSubmitButtonLabel;
        a = R.drawable.antelopConsentPromptIcon;
        int i = n + 71;
        m = i % 128;
        int i2 = i % 2;
    }

    private static String e(Context context) {
        int i = n + 21;
        m = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(b);
        int i3 = n + 81;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return string;
            default:
                throw null;
        }
    }

    private static String a(Context context) {
        int i = m + 35;
        n = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(c);
        int i3 = m + 89;
        n = i3 % 128;
        switch (i3 % 2 != 0 ? 'J' : '*') {
            case '*':
                return string;
            default:
                throw null;
        }
    }

    private static String b(Context context) {
        int i = n + 65;
        m = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(d);
        int i3 = m + 65;
        n = i3 % 128;
        int i4 = i3 % 2;
        return string;
    }

    private static String c(Context context) {
        int i = m + 71;
        n = i % 128;
        int i2 = i % 2;
        String string = context.getResources().getString(e);
        int i3 = n + 51;
        m = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return string;
            default:
                int i4 = 54 / 0;
                return string;
        }
    }

    @Override // o.o.c
    public final boolean c() {
        int i = m;
        int i2 = i + 33;
        n = i2 % 128;
        int i3 = i2 % 2;
        int i4 = i + 59;
        n = i4 % 128;
        int i5 = i4 % 2;
        return true;
    }

    public a(Context context, String str, String str2, e eVar) {
        super(e(context), a);
        if (str == null) {
            this.f = o.e((CharSequence) a(context));
        } else {
            this.f = o.e((CharSequence) str);
        }
        if (str2 == null) {
            this.i = o.e((CharSequence) b(context));
        } else {
            this.i = o.e((CharSequence) str2);
        }
        this.f91o = c(context);
        this.k = eVar;
    }

    public final String i() {
        int i = n + 65;
        int i2 = i % 128;
        m = i2;
        switch (i % 2 == 0 ? '@' : 'K') {
            case 'K':
                String str = this.f;
                int i3 = i2 + 23;
                n = i3 % 128;
                int i4 = i3 % 2;
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String j() {
        int i = m;
        int i2 = i + 41;
        n = i2 % 128;
        int i3 = i2 % 2;
        String str = this.i;
        int i4 = i + 79;
        n = i4 % 128;
        switch (i4 % 2 != 0 ? '-' : (char) 19) {
            case 19:
                return str;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final String f() {
        int i = m + 99;
        n = i % 128;
        switch (i % 2 != 0 ? '9' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return this.f91o;
            default:
                int i2 = 45 / 0;
                return this.f91o;
        }
    }

    @Override // o.o.c
    public final void d(FragmentActivity fragmentActivity, int i, CancellationSignal cancellationSignal, final b bVar) {
        this.j = new c(this, new c.d() { // from class: o.l.a.1
            private static int b = 0;
            private static int c = 1;

            @Override // o.l.c.d
            public final void c() {
                bVar.b(new o.f.b(e.d.c, new Date()), a.this);
                int i2 = b;
                int i3 = (i2 & 11) + (i2 | 11);
                c = i3 % 128;
                switch (i3 % 2 == 0 ? ' ' : '\f') {
                    case ' ':
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return;
                }
            }

            @Override // o.l.c.d
            public final void b() {
                int i2 = c + 23;
                b = i2 % 128;
                int i3 = i2 % 2;
                bVar.c(o.o.e.a, a.this);
                int i4 = b + 37;
                c = i4 % 128;
                int i5 = i4 % 2;
            }
        });
        FragmentManager supportFragmentManager = fragmentActivity.getSupportFragmentManager();
        this.h = supportFragmentManager;
        this.g = bVar;
        supportFragmentManager.beginTransaction().replace(i, this.j).addToBackStack(null).commit();
        int i2 = n + 43;
        m = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                int i3 = 75 / 0;
                return;
            default:
                return;
        }
    }

    @Override // o.o.c
    public final boolean a(o.o.c cVar) {
        switch (cVar != null) {
            case false:
                int i = n + Opcodes.LNEG;
                m = i % 128;
                int i2 = i % 2;
                return true;
            default:
                int i3 = n + 33;
                m = i3 % 128;
                switch (i3 % 2 == 0) {
                    case false:
                        return false;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
        }
    }
}
