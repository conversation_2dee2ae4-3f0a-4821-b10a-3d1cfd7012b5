package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.math.BigInteger;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\v2.smali */
public class v2 extends u {
    private r b;

    private v2(r rVar) {
        if (rVar == null) {
            throw new IllegalArgumentException("'y' cannot be null");
        }
        this.b = rVar;
    }

    public static v2 a(Object obj) {
        if (obj == null || (obj instanceof v2)) {
            return (v2) obj;
        }
        if (obj instanceof r) {
            return new v2((r) obj);
        }
        throw new IllegalArgumentException("Invalid DHPublicKey: " + obj.getClass().getName());
    }

    public BigInteger e() {
        return this.b.h();
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        return this.b;
    }
}
