package o.eq;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import com.vasco.digipass.sdk.utils.utilities.UtilitiesSDKConstants;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import fr.antelop.sdk.digitalcard.SecureCardPushToGooglePay;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.Address;
import fr.antelop.sdk.util.AndroidActivityResultCallback;
import fr.antelop.sdk.util.OperationCallback;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.i;
import o.eo.e;
import o.eo.f;
import o.ep.a;
import o.v.h;
import org.bouncycastle.math.ec.Tnaf;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\eq\d.smali */
public final class d extends o.er.d<o.ep.c> {
    public static final byte[] $$d = null;
    public static final int $$e = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static char[] b;
    private static char e;
    private static short[] f;
    private static int g;
    private static int h;
    private static byte[] i;
    private static int j;
    private static int m;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        m = 1;
        g();
        ViewConfiguration.getTapTimeout();
        ViewConfiguration.getZoomControlsTimeout();
        ViewConfiguration.getPressedStateDuration();
        TextUtils.getTrimmedLength("");
        ImageFormat.getBitsPerPixel(0);
        int i2 = m + 81;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                return;
        }
    }

    static void g() {
        b = new char[]{30542, 30591, 30587, 30577, 30538, 30573, 30559, 30566, 30586, 30556, 30540, 30570, 30561, 30589, 30584, 30554, 30588, 30580, 30568, 30569, 30572, 30571, 30536, 30560, 30537, 30567, 30563, 30562, 30574, 30498, 30539, 30585, 30583, 30581, 30511, 30582};
        e = (char) 17043;
        i = new byte[]{27, 19, -20, 29, 6, 11, -13, 49, 10, -28, 18, 30, 17, 25, 33, -50, 6, Tnaf.POW_2_WIDTH, 25, 62, 117, 124, 75, 105, 116, 112, 75, 124, ByteCompanionObject.MAX_VALUE, 115, 74, 117, 106, 120, 70, 119, -23, 8, 53, -58, 29, 0, -17, -21, 15, 8, -13, 31, -18, 11, 18, 24, 56, -56, 1, -112, -112, -112};
        g = 909053616;
        j = -124779212;
        a = 300579560;
    }

    static void init$0() {
        $$d = new byte[]{117, -111, 19, -37};
        $$e = Opcodes.INVOKEINTERFACE;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x0031). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            byte[] r0 = o.eq.d.$$d
            int r9 = r9 + 69
            int r7 = r7 * 3
            int r7 = 1 - r7
            int r8 = r8 + 4
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L16
            r3 = r1
            r5 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            r9 = r8
            goto L31
        L16:
            r3 = r2
        L17:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L26
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L26:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L31:
            int r8 = -r8
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r8
            r8 = r6
            goto L17
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.d.p(int, short, int, java.lang.Object[]):void");
    }

    static /* synthetic */ e a(d dVar) {
        int i2 = m + 37;
        h = i2 % 128;
        int i3 = i2 % 2;
        e eVar = dVar.c;
        int i4 = h + 79;
        m = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    static /* synthetic */ e b(d dVar) {
        int i2 = m + 55;
        h = i2 % 128;
        int i3 = i2 % 2;
        e eVar = dVar.c;
        int i4 = m + 63;
        h = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    static /* synthetic */ e c(d dVar) {
        int i2 = m + 93;
        h = i2 % 128;
        char c = i2 % 2 != 0 ? Typography.amp : '(';
        Object obj = null;
        e eVar = dVar.c;
        switch (c) {
            case '(':
                int i3 = h + Opcodes.LMUL;
                m = i3 % 128;
                switch (i3 % 2 == 0 ? 'V' : '\t') {
                    case Opcodes.SASTORE /* 86 */:
                        throw null;
                    default:
                        return eVar;
                }
            default:
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ e d(d dVar) {
        int i2 = m + 33;
        h = i2 % 128;
        boolean z = i2 % 2 == 0;
        e eVar = dVar.c;
        switch (z) {
            case true:
                return eVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    static /* synthetic */ e e(d dVar) {
        int i2 = h + 25;
        m = i2 % 128;
        boolean z = i2 % 2 == 0;
        e eVar = dVar.c;
        switch (z) {
            case false:
                return eVar;
            default:
                throw null;
        }
    }

    static /* synthetic */ e f(d dVar) {
        int i2 = h + Opcodes.DMUL;
        m = i2 % 128;
        int i3 = i2 % 2;
        e eVar = dVar.c;
        int i4 = h + 17;
        m = i4 % 128;
        int i5 = i4 % 2;
        return eVar;
    }

    static /* synthetic */ e g(d dVar) {
        int i2 = m + 59;
        h = i2 % 128;
        boolean z = i2 % 2 == 0;
        e eVar = dVar.c;
        switch (z) {
            case false:
                throw null;
            default:
                return eVar;
        }
    }

    static /* synthetic */ e h(d dVar) {
        int i2 = m + 37;
        h = i2 % 128;
        boolean z = i2 % 2 != 0;
        e eVar = dVar.c;
        switch (z) {
            default:
                int i3 = 61 / 0;
            case false:
                return eVar;
        }
    }

    static /* synthetic */ e i(d dVar) {
        int i2 = m + 73;
        h = i2 % 128;
        int i3 = i2 % 2;
        e eVar = dVar.c;
        int i4 = m + 79;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                int i5 = 13 / 0;
                return eVar;
            default:
                return eVar;
        }
    }

    @Override // o.er.d
    public final /* synthetic */ o.ep.c e(Context context) {
        int i2 = m + 85;
        h = i2 % 128;
        switch (i2 % 2 == 0 ? 'P' : (char) 2) {
            case 'P':
                return c(context);
            default:
                c(context);
                throw null;
        }
    }

    public d(e eVar, o.el.e eVar2) {
        super(eVar, eVar2);
    }

    public static o.ep.c c(Context context) {
        int i2 = h + Opcodes.LSUB;
        m = i2 % 128;
        int i3 = i2 % 2;
        o.ee.c.a();
        o.ep.c k = o.ee.c.k(context);
        int i4 = h + 3;
        m = i4 % 128;
        int i5 = i4 % 2;
        return k;
    }

    @Override // o.er.h
    public final o.er.a[] i() {
        int i2 = m + 87;
        h = i2 % 128;
        int i3 = i2 % 2;
        o.er.a[] aVarArr = {this.d.d()};
        int i4 = h + 53;
        m = i4 % 128;
        switch (i4 % 2 == 0 ? '\\' : (char) 30) {
            case 30:
                return aVarArr;
            default:
                int i5 = 75 / 0;
                return aVarArr;
        }
    }

    @Override // o.er.d
    public final String c() {
        int i2 = h + 49;
        m = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        n(Color.argb(0, 0, 0, 0) + 16, "\u0017\u0012\u0012\u0013\u001d\b\n\u0018!\u000b\u0007\u0011\u0001\r\u0017\b", (byte) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 55), objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = m + 47;
        h = i4 % 128;
        switch (i4 % 2 != 0 ? '@' : (char) 14) {
            case 14:
                return intern;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    @Override // o.er.d
    public final AntelopErrorCode a() {
        int i2 = m + 65;
        h = i2 % 128;
        int i3 = i2 % 2;
        AntelopErrorCode antelopErrorCode = AntelopErrorCode.GooglePayWalletNotAvailable;
        int i4 = h + Opcodes.LSHR;
        m = i4 % 128;
        switch (i4 % 2 == 0 ? '0' : 'J') {
            case 'J':
                return antelopErrorCode;
            default:
                throw null;
        }
    }

    @Override // o.er.d
    public final f.a d() {
        int i2 = m + 89;
        h = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                f.a aVar = f.a.e;
                throw null;
            default:
                f.a aVar2 = f.a.e;
                int i3 = h + 63;
                m = i3 % 128;
                int i4 = i3 % 2;
                return aVar2;
        }
    }

    @Override // o.er.d
    public final String e() {
        int i2 = m + Opcodes.LSHR;
        h = i2 % 128;
        int i3 = i2 % 2;
        Object[] objArr = new Object[1];
        o((byte) View.getDefaultSize(0, 0), (-667251832) - Color.argb(0, 0, 0, 0), (short) ((TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + Opcodes.DNEG), (ViewConfiguration.getMinimumFlingVelocity() >> 16) - 11, 826340518 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        int i4 = m + 93;
        h = i4 % 128;
        switch (i4 % 2 != 0) {
            case false:
                return intern;
            default:
                throw null;
        }
    }

    private String j() {
        int i2 = m + Opcodes.LREM;
        h = i2 % 128;
        if (i2 % 2 != 0) {
            this.d.d().d();
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        String d = this.d.d().d();
        switch (d == null) {
            case false:
                return d;
            default:
                int i3 = m + Opcodes.DDIV;
                h = i3 % 128;
                int i4 = i3 % 2;
                g.c();
                Object[] objArr = new Object[1];
                o((byte) TextUtils.getTrimmedLength(""), ExpandableListView.getPackedPositionChild(0L) - 667251831, (short) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + Opcodes.FNEG), (-11) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), TextUtils.indexOf((CharSequence) "", '0') + 826340518, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                n(TextUtils.indexOf((CharSequence) "", '0', 0) + Opcodes.LUSHR, "\u0017\u0006\u0000\b\n\u000e\u001c\u0007\u0019\u0010\u0016\t\n\u000e\u0005\u0014\u001d\t\f\u0001\u000e\b\u001d\u0007\u000e\u0000\b\u0013\u001a\u0004\u000b\u0013\u0012\f\u001a\u0004\u0005\b\u000e\r#\u001c!\u0010\u0010\u0018\u0002\u001d\n#\u0005\u0014\u001e\u0016\b\u0005\u0004\u001c㘶㘶\u0014\u0018\u0006\u0007\u001d\"\u001f\u0004\u001a\u0004\u0005\b\u000e\r\u001e\u0010\u001d\u001c\n#\"\u0016\u0011\u0016\u0013\b\u001a\u0004\t\u0017#\u001c!\u0010\u0011\n \u0004\u001d\u0007!\u0016\u0007\u0017\u001a\n \b\u001e\u0010\u0016#\u001a\n\u0001\u001a\u001f\u0004\u001a\u0004\u0005\b\u000e\r", (byte) (65 - TextUtils.getOffsetBefore("", 0)), objArr2);
                g.e(intern, ((String) objArr2[0]).intern());
                Object[] objArr3 = new Object[1];
                o((byte) (ViewConfiguration.getScrollDefaultDelay() >> 16), (-667251813) - TextUtils.indexOf((CharSequence) "", '0', 0), (short) (27 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (-15) - (ViewConfiguration.getScrollBarSize() >> 8), 826340553 - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), objArr3);
                return ((String) objArr3[0]).intern();
        }
    }

    public final SecureCardPushToGooglePay h() {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        n(KeyEvent.getDeadChar(0, 0) + 17, "\u0017\u0006\u0003\b\b\u0017\u0007\u000e\u0006\u000b\u0019\u0010\u0012\t\n\u000e㙊", (byte) (87 - ImageFormat.getBitsPerPixel(0)), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        SecureCardPushToGooglePay secureCardPushToGooglePay = new SecureCardPushToGooglePay(new h(j(), this.c, b()));
        int i2 = m + 81;
        h = i2 % 128;
        switch (i2 % 2 != 0 ? '\t' : 'A') {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                return secureCardPushToGooglePay;
            default:
                throw null;
        }
    }

    public final AndroidActivityResultCallback b(Activity activity, OperationCallback<Void> operationCallback) throws WalletValidationException {
        int i2 = h + 21;
        m = i2 % 128;
        int i3 = i2 % 2;
        AndroidActivityResultCallback a2 = a(activity, operationCallback, null);
        int i4 = m + 87;
        h = i4 % 128;
        int i5 = i4 % 2;
        return a2;
    }

    public final AndroidActivityResultCallback a(Activity activity, final OperationCallback<Void> operationCallback, Address address) throws WalletValidationException {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        n(KeyEvent.normalizeMetaState(0) + 8, "\u0002\u0007\r\u001c\u0010\"\u000f\u0013", (byte) (KeyEvent.keyCodeFromString("") + 71), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        SecureCardPushToGooglePay h2 = h();
        switch (address == null) {
            case false:
                int i2 = h + 7;
                m = i2 % 128;
                int i3 = i2 % 2;
                h2.setUserAddress(address);
                break;
        }
        AndroidActivityResultCallback launch = h2.launch(activity, new CustomCustomerAuthenticatedProcessCallback() { // from class: o.eq.d.5
            private static int a = 0;
            private static int c = 1;

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onAuthenticationDeclined(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i4 = a;
                int i5 = (i4 & 3) + (i4 | 3);
                c = i5 % 128;
                switch (i5 % 2 != 0) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsInvalid(LocalAuthenticationErrorReason localAuthenticationErrorReason, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i4 = c + 67;
                a = i4 % 128;
                switch (i4 % 2 != 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessStart(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i4 = c + Opcodes.DSUB;
                a = i4 % 128;
                int i5 = i4 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onCustomerCredentialsRequired(List<CustomerAuthenticationMethod> list, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                operationCallback.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible).d());
                int i4 = c;
                int i5 = ((i4 | 95) << 1) - (i4 ^ 95);
                a = i5 % 128;
                switch (i5 % 2 != 0 ? 'X' : '/') {
                    case '/':
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onProcessSuccess(CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i4 = c + 27;
                a = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        operationCallback.onSuccess(null);
                        break;
                    default:
                        operationCallback.onSuccess(null);
                        int i5 = 22 / 0;
                        break;
                }
                int i6 = c;
                int i7 = (i6 ^ Opcodes.DMUL) + ((i6 & Opcodes.DMUL) << 1);
                a = i7 % 128;
                int i8 = i7 % 2;
            }

            @Override // fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback
            public final void onError(AntelopError antelopError, CustomerAuthenticatedProcess customerAuthenticatedProcess) {
                int i4 = a;
                int i5 = (i4 ^ 21) + ((i4 & 21) << 1);
                c = i5 % 128;
                int i6 = i5 % 2;
                operationCallback.onError(antelopError);
                int i7 = (c + 58) - 1;
                a = i7 % 128;
                switch (i7 % 2 == 0) {
                    case true:
                        return;
                    default:
                        int i8 = 18 / 0;
                        return;
                }
            }
        });
        int i4 = h + 85;
        m = i4 % 128;
        int i5 = i4 % 2;
        return launch;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void e(OperationCallback operationCallback, int i2, Intent intent) {
        int i3 = h + Opcodes.DDIV;
        int i4 = i3 % 128;
        m = i4;
        switch (i3 % 2 == 0 ? '#' : (char) 29) {
            case '#':
                throw null;
            default:
                if (i2 == 0) {
                    operationCallback.onError(new AntelopError(new o.bv.c(AntelopErrorCode.UserCancelled)));
                    return;
                }
                switch (i2 != -1) {
                    case false:
                        int i5 = i4 + 19;
                        h = i5 % 128;
                        int i6 = i5 % 2;
                        operationCallback.onSuccess(null);
                        return;
                    default:
                        operationCallback.onError(new AntelopError(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable)));
                        int i7 = m + 49;
                        h = i7 % 128;
                        if (i7 % 2 == 0) {
                            return;
                        } else {
                            throw null;
                        }
                }
        }
    }

    public final AndroidActivityResultCallback e(final Activity activity, final OperationCallback<Void> operationCallback) {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        n((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 15, "\u0011\n\u0003\u0001\f\"\u0007\u0017\u001a\n \b\u0010\"\u000f\u0013", (byte) (11 - View.MeasureSpec.makeMeasureSpec(0, 0)), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        i iVar = new i();
        iVar.e(53, new i.a() { // from class: o.eq.d$$ExternalSyntheticLambda1
            @Override // o.ee.i.a
            public final void onActivityResult(int i2, Intent intent) {
                d.e(OperationCallback.this, i2, intent);
            }
        });
        c(activity).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.eq.d.1
            private static int b = 0;
            private static int a = 1;

            @Override // o.ep.a.InterfaceC0042a
            public final /* bridge */ /* synthetic */ void e(List<o.ep.e> list) {
                int i2 = a;
                int i3 = (i2 ^ 93) + ((i2 & 93) << 1);
                b = i3 % 128;
                char c = i3 % 2 != 0 ? (char) 0 : '\n';
                e2(list);
                switch (c) {
                    case 0:
                        throw null;
                    default:
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            /* renamed from: e, reason: avoid collision after fix types in other method */
            private void e2(java.util.List<o.ep.e> r8) {
                /*
                    Method dump skipped, instructions count: 268
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass1.e2(java.util.List):void");
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i2 = b + 41;
                a = i2 % 128;
                int i3 = i2 % 2;
                operationCallback.onError(cVar.d());
                int i4 = a + 77;
                b = i4 % 128;
                int i5 = i4 % 2;
            }
        });
        AndroidActivityResultCallback e3 = iVar.e();
        int i2 = h + 97;
        m = i2 % 128;
        int i3 = i2 % 2;
        return e3;
    }

    public final void a(final Activity activity, final OperationCallback<Boolean> operationCallback) throws WalletValidationException {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        o((byte) (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), (-667251796) + KeyEvent.keyCodeFromString(""), (short) (122 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), (-13) - Process.getGidForName(""), 826340549 + (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        c(activity, new OperationCallback<Boolean>() { // from class: o.eq.d.2
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static int c;
            private static short[] f;
            private static byte[] g;
            private static int h;
            private static int i;
            private static int j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                i = 0;
                h = 1;
                g = new byte[]{-71, -110, -15, 92, -102, -122, -103, -127, -119, -56, 83, -122, -54, 45, -50, 90, -100, -117, -54, 93, -109, UtilitiesSDKConstants.SRP_LABEL_MAC, -97, -60, -108, -114, 93, -109, UtilitiesSDKConstants.SRP_LABEL_MAC, -65, 112, -121, -118, -103, -107, -71, UtilitiesSDKConstants.SRP_LABEL_MAC, 125, -119, -104, -75, -100, -126, -94, 114, -117, -112};
                a = 909053596;
                j = -1939700633;
                c = -236385951;
            }

            static void init$0() {
                $$a = new byte[]{17, -48, -95, 64};
                $$b = Opcodes.LRETURN;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x003a). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void l(int r6, int r7, byte r8, java.lang.Object[] r9) {
                /*
                    byte[] r0 = o.eq.d.AnonymousClass2.$$a
                    int r7 = r7 * 3
                    int r7 = r7 + 4
                    int r6 = r6 * 2
                    int r6 = r6 + 1
                    int r8 = r8 * 2
                    int r8 = 110 - r8
                    byte[] r1 = new byte[r6]
                    int r6 = r6 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1d
                    r8 = r7
                    r3 = r1
                    r4 = r2
                    r7 = r6
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    goto L3a
                L1d:
                    r3 = r2
                    r5 = r8
                    r8 = r7
                    r7 = r5
                L21:
                    byte r4 = (byte) r7
                    r1[r3] = r4
                    if (r3 != r6) goto L2e
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2e:
                    int r3 = r3 + 1
                    r4 = r0[r8]
                    r5 = r7
                    r7 = r6
                    r6 = r4
                    r4 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r5
                L3a:
                    int r6 = -r6
                    int r8 = r8 + 1
                    int r6 = r6 + r9
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r7
                    r7 = r6
                    r6 = r5
                    goto L21
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass2.l(int, int, byte, java.lang.Object[]):void");
            }

            @Override // fr.antelop.sdk.util.OperationCallback
            public final /* synthetic */ void onSuccess(Boolean bool) {
                int i2 = i + Opcodes.LMUL;
                h = i2 % 128;
                char c2 = i2 % 2 == 0 ? Typography.amp : '/';
                c(bool);
                switch (c2) {
                    case '/':
                        break;
                    default:
                        int i3 = 15 / 0;
                        break;
                }
                int i4 = h + 23;
                i = i4 % 128;
                int i5 = i4 % 2;
            }

            private void c(Boolean bool) {
                int i2 = h + Opcodes.LUSHR;
                i = i2 % 128;
                Object obj = null;
                switch (i2 % 2 == 0) {
                    case false:
                        bool.booleanValue();
                        obj.hashCode();
                        throw null;
                    default:
                        if (bool.booleanValue()) {
                            d.c(activity).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.eq.d.2.1
                                public static final byte[] $$a = null;
                                public static final int $$b = 0;
                                private static int $10;
                                private static int $11;
                                private static char[] a;
                                private static int c;
                                private static int d;

                                static {
                                    init$0();
                                    $10 = 0;
                                    $11 = 1;
                                    d = 0;
                                    c = 1;
                                    a = new char[]{50824, 50754, 50865, 50872, 50763, 50761, 50753, 50780, 50780, 50766, 50868, 50755, 50777, 50759, 50759, 50783, 50865, 50878, 50759, 50753, 50862, 50826, 50826, 50849, 50752, 50762, 50752, 50756, 50752, 50854, 50854, 50783, 50755, 50756, 50759, 50853, 50857, 50763, 50752, 50753, 50763, 50756, 50753, 50758, 50857, 50855, 50776, 50752, 50761, 50760, 50752, 50777, 50824, 50761, 50771, 50771, 50789, 50690, 50694, 50699, 50698, 50792, 50796, 50693, 50691, 50794, 50797, 50697, 50699, 50699, 50689, 50693, 50697, 50793, 50770, 50701, 50698, 50692, 50797, 50799, 50689, 50804, 50815, 50702, 50700, 50692, 50691, 50691, 50701, 50811, 50694, 50716, 50698, 50698, 50690, 50804, 50813, 50698, 50692, 50797, 50909, 50831, 50852, 50876, 50879, 50855, 50830, 50820, 50851, 50825, 50823, 50849, 50853, 50849, 50859, 50849, 50822, 50826, 50854, 50935, 50855, 50858, 50876, 50823, 50821, 50853, 50848, 50817, 50822, 50849, 50859, 50849, 50853, 50849, 50823, 50823, 50876, 50848, 50853, 50852, 50826, 50830, 50856, 50849, 50854, 50856, 50853, 50854, 50855, 50830, 50830, 50862, 50856, 50855, 50853, 50857, 50831, 50912, 50912, 50825, 50851, 50838, 50841, 50856, 50862, 50854, 50877, 50877, 50863, 50837, 50848, 50878, 50852, 50852, 50876, 50838, 50847, 50852, 50854, 50831, 50923, 50923, 50831, 50859, 50851};
                                }

                                /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
                                /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
                                /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0033). Please report as a decompilation issue!!! */
                                /*
                                    Code decompiled incorrectly, please refer to instructions dump.
                                    To view partially-correct add '--show-bad-code' argument
                                */
                                private static void g(short r6, int r7, int r8, java.lang.Object[] r9) {
                                    /*
                                        int r7 = r7 * 2
                                        int r7 = r7 + 1
                                        byte[] r0 = o.eq.d.AnonymousClass2.AnonymousClass1.$$a
                                        int r6 = 122 - r6
                                        int r8 = r8 * 2
                                        int r8 = 3 - r8
                                        byte[] r1 = new byte[r7]
                                        int r7 = r7 + (-1)
                                        r2 = 0
                                        if (r0 != 0) goto L19
                                        r3 = r1
                                        r4 = r2
                                        r1 = r0
                                        r0 = r9
                                        r9 = r8
                                        goto L33
                                    L19:
                                        r3 = r2
                                    L1a:
                                        byte r4 = (byte) r6
                                        r1[r3] = r4
                                        int r4 = r3 + 1
                                        if (r3 != r7) goto L29
                                        java.lang.String r6 = new java.lang.String
                                        r6.<init>(r1, r2)
                                        r9[r2] = r6
                                        return
                                    L29:
                                        int r8 = r8 + 1
                                        r3 = r0[r8]
                                        r5 = r9
                                        r9 = r8
                                        r8 = r3
                                        r3 = r1
                                        r1 = r0
                                        r0 = r5
                                    L33:
                                        int r8 = -r8
                                        int r6 = r6 + r8
                                        r8 = r9
                                        r9 = r0
                                        r0 = r1
                                        r1 = r3
                                        r3 = r4
                                        goto L1a
                                    */
                                    throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass2.AnonymousClass1.g(short, int, int, java.lang.Object[]):void");
                                }

                                static void init$0() {
                                    $$a = new byte[]{117, -111, 19, -37};
                                    $$b = 73;
                                }

                                @Override // o.ep.a.InterfaceC0042a
                                public final /* synthetic */ void e(List<o.ep.e> list) {
                                    int i3 = d + 7;
                                    c = i3 % 128;
                                    int i4 = i3 % 2;
                                    a(list);
                                    int i5 = c + 9;
                                    d = i5 % 128;
                                    switch (i5 % 2 != 0 ? 'V' : '\r') {
                                        case Opcodes.SASTORE /* 86 */:
                                            throw null;
                                        default:
                                            return;
                                    }
                                }

                                private void a(List<o.ep.e> list) {
                                    g.c();
                                    String e3 = d.this.e();
                                    Object[] objArr2 = new Object[1];
                                    f("\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0000\u0000\u0000", new int[]{0, 52, 33, 0}, false, objArr2);
                                    g.d(e3, ((String) objArr2[0]).intern());
                                    Boolean bool2 = Boolean.FALSE;
                                    Iterator<o.ep.e> it = list.iterator();
                                    while (true) {
                                        switch (it.hasNext() ? '/' : 'L') {
                                            case Base64.mimeLineLength /* 76 */:
                                                break;
                                            default:
                                                int i3 = c + Opcodes.LNEG;
                                                d = i3 % 128;
                                                int i4 = i3 % 2;
                                                o.ep.e next = it.next();
                                                switch (d.e(d.this).s() != null ? '@' : '?') {
                                                    case '@':
                                                        int i5 = c + 39;
                                                        d = i5 % 128;
                                                        int i6 = i5 % 2;
                                                        if (!next.d().equals(d.d(d.this).s().a())) {
                                                            break;
                                                        } else {
                                                            g.c();
                                                            String e4 = d.this.e();
                                                            StringBuilder sb = new StringBuilder();
                                                            Object[] objArr3 = new Object[1];
                                                            f("\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000", new int[]{52, 48, 94, 27}, false, objArr3);
                                                            StringBuilder append = sb.append(((String) objArr3[0]).intern()).append(d.b(d.this).e());
                                                            Object[] objArr4 = new Object[1];
                                                            f("\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0000\u0001", new int[]{100, 19, 0, 10}, true, objArr4);
                                                            g.d(e4, append.append(((String) objArr4[0]).intern()).toString());
                                                            bool2 = next.f();
                                                            break;
                                                        }
                                                }
                                                int i7 = c + 83;
                                                d = i7 % 128;
                                                int i8 = i7 % 2;
                                                break;
                                        }
                                    }
                                    operationCallback.onSuccess(bool2);
                                }

                                @Override // o.ep.a.InterfaceC0042a
                                public final void e(o.bv.c cVar) {
                                    g.c();
                                    String e3 = d.this.e();
                                    StringBuilder sb = new StringBuilder();
                                    Object[] objArr2 = new Object[1];
                                    f("\u0000\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0001\u0000\u0000\u0001\u0000\u0000\u0000\u0001\u0000\u0001\u0000\u0001\u0000\u0000\u0001\u0001\u0000\u0001\u0000", new int[]{Opcodes.DNEG, 66, 0, 40}, false, objArr2);
                                    g.e(e3, sb.append(((String) objArr2[0]).intern()).append(cVar.c()).toString());
                                    operationCallback.onError(cVar.d());
                                    int i3 = c + 97;
                                    d = i3 % 128;
                                    switch (i3 % 2 != 0) {
                                        case false:
                                            return;
                                        default:
                                            Object obj2 = null;
                                            obj2.hashCode();
                                            throw null;
                                    }
                                }

                                /* JADX WARN: Code restructure failed: missing block: B:113:0x0335, code lost:
                                
                                    r1 = r0;
                                 */
                                /*
                                    Code decompiled incorrectly, please refer to instructions dump.
                                    To view partially-correct add '--show-bad-code' argument
                                */
                                private static void f(java.lang.String r21, int[] r22, boolean r23, java.lang.Object[] r24) {
                                    /*
                                        Method dump skipped, instructions count: 922
                                        To view this dump add '--comments-level debug' option
                                    */
                                    throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass2.AnonymousClass1.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
                                }
                            });
                            return;
                        }
                        g.c();
                        String e3 = d.this.e();
                        Object[] objArr2 = new Object[1];
                        k((byte) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), KeyEvent.keyCodeFromString("") + 943323151, (short) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 18), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 35, Drawable.resolveOpacity(0, 0) + 1169323378, objArr2);
                        g.d(e3, ((String) objArr2[0]).intern());
                        operationCallback.onSuccess(Boolean.FALSE);
                        int i3 = h + 35;
                        i = i3 % 128;
                        switch (i3 % 2 != 0) {
                            case false:
                                return;
                            default:
                                throw null;
                        }
                }
            }

            @Override // fr.antelop.sdk.util.OperationCallback
            public final void onError(AntelopError antelopError) {
                int i2 = i + 67;
                h = i2 % 128;
                switch (i2 % 2 == 0 ? '4' : 'G') {
                    case '4':
                        operationCallback.onError(antelopError);
                        throw null;
                    default:
                        operationCallback.onError(antelopError);
                        return;
                }
            }

            /* JADX WARN: Code restructure failed: missing block: B:60:0x01f7, code lost:
            
                if (r4 != false) goto L68;
             */
            /* JADX WARN: Code restructure failed: missing block: B:78:0x02c3, code lost:
            
                r3 = r7;
             */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void k(byte r18, int r19, short r20, int r21, int r22, java.lang.Object[] r23) {
                /*
                    Method dump skipped, instructions count: 860
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass2.k(byte, int, short, int, int, java.lang.Object[]):void");
            }
        });
        int i2 = m + 45;
        h = i2 % 128;
        int i3 = i2 % 2;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ void d(OperationCallback operationCallback, int i2, Intent intent) {
        int i3 = m + 55;
        h = i3 % 128;
        int i4 = i3 % 2;
        if (i2 == 0) {
            operationCallback.onError(new AntelopError(new o.bv.c(AntelopErrorCode.UserCancelled)));
            int i5 = m + Opcodes.LSHL;
            h = i5 % 128;
            int i6 = i5 % 2;
        }
        switch (i2 != -1) {
            case false:
                operationCallback.onSuccess(null);
                break;
            default:
                operationCallback.onError(new AntelopError(new o.bv.c(AntelopErrorCode.GooglePayWalletNotAvailable)));
                break;
        }
    }

    public final AndroidActivityResultCallback c(final Activity activity, final OperationCallback<Void> operationCallback) {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        n(10 - ExpandableListView.getPackedPositionGroup(0L), "\u0017\t\u001d\b\u0005\b\u0010\"\u000f\u0013", (byte) (71 - Drawable.resolveOpacity(0, 0)), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        i iVar = new i();
        iVar.e(54, new i.a() { // from class: o.eq.d$$ExternalSyntheticLambda0
            @Override // o.ee.i.a
            public final void onActivityResult(int i2, Intent intent) {
                d.d(OperationCallback.this, i2, intent);
            }
        });
        c(activity).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.eq.d.3
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static char[] b;
            private static long c;
            private static int g;
            private static int j;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                g = 0;
                j = 1;
                b = new char[]{45881, 19635, 19495, 19865, 19717, 20111, 20060, 20465, 20343, 18650, 18451, 18825, 18937, 18749, 19113, 18987, 19352, 19205, 17656, 17513, 17906, 17737, 11437, 54055, 54195, 53773, 53905, 53531, 53704, 53349, 53475, 55118, 55175, 54813, 54893, 54953, 54589, 54697, 54283, 54400, 56160, 56298};
                c = -4497848855591267518L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0033). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void h(int r6, byte r7, byte r8, java.lang.Object[] r9) {
                /*
                    int r7 = r7 * 3
                    int r7 = r7 + 1
                    int r6 = r6 * 3
                    int r6 = r6 + 4
                    int r8 = r8 + 102
                    byte[] r0 = o.eq.d.AnonymousClass3.$$a
                    byte[] r1 = new byte[r7]
                    int r7 = r7 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r3 = r1
                    r4 = r2
                    r1 = r0
                    r0 = r9
                    r9 = r8
                    r8 = r7
                    goto L33
                L1a:
                    r3 = r2
                L1b:
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    int r4 = r3 + 1
                    if (r3 != r7) goto L2a
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L2a:
                    r3 = r0[r6]
                    r5 = r8
                    r8 = r7
                    r7 = r3
                    r3 = r1
                    r1 = r0
                    r0 = r9
                    r9 = r5
                L33:
                    int r7 = -r7
                    int r7 = r7 + r9
                    int r6 = r6 + 1
                    r9 = r0
                    r0 = r1
                    r1 = r3
                    r3 = r4
                    r5 = r8
                    r8 = r7
                    r7 = r5
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass3.h(int, byte, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{122, -6, -127, 6};
                $$b = 33;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(List<o.ep.e> list) {
                int i2 = g + 35;
                j = i2 % 128;
                int i3 = i2 % 2;
                c(list);
                int i4 = g + 85;
                j = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            }

            private void c(List<o.ep.e> list) {
                int i2 = j + Opcodes.LREM;
                g = i2 % 128;
                int i3 = i2 % 2;
                g.c();
                String e3 = d.this.e();
                Object[] objArr2 = new Object[1];
                f((char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 40852), (-1) - ExpandableListView.getPackedPositionChild(0L), TextUtils.lastIndexOf("", '0', 0) + 23, objArr2);
                g.d(e3, ((String) objArr2[0]).intern());
                o.ep.e eVar = null;
                for (o.ep.e eVar2 : list) {
                    switch (d.h(d.this).s() != null) {
                        case true:
                            switch (eVar2.d().equals(d.i(d.this).s().a()) ? '#' : (char) 24) {
                                case '#':
                                    eVar = eVar2;
                                    break;
                            }
                    }
                }
                if (eVar != null) {
                    d.c(activity).d(activity, eVar);
                    return;
                }
                operationCallback.onError(new AntelopError(new o.bv.c(AntelopErrorCode.CardDeleted)));
                int i4 = j + Opcodes.LNEG;
                g = i4 % 128;
                int i5 = i4 % 2;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                int i2 = g + Opcodes.DDIV;
                j = i2 % 128;
                int i3 = i2 % 2;
                g.c();
                String e3 = d.this.e();
                Object[] objArr2 = new Object[1];
                f((char) (ViewConfiguration.getScrollDefaultDelay() >> 16), Color.red(0) + 22, 20 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr2);
                g.d(e3, ((String) objArr2[0]).intern());
                operationCallback.onError(cVar.d());
                int i4 = g + 5;
                j = i4 % 128;
                switch (i4 % 2 == 0 ? 'Z' : '2') {
                    case 'Z':
                        int i5 = 94 / 0;
                        return;
                    default:
                        return;
                }
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(char r18, int r19, int r20, java.lang.Object[] r21) {
                /*
                    Method dump skipped, instructions count: 706
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass3.f(char, int, int, java.lang.Object[]):void");
            }
        });
        AndroidActivityResultCallback e3 = iVar.e();
        int i2 = m + 11;
        h = i2 % 128;
        int i3 = i2 % 2;
        return e3;
    }

    public final void a(final Activity activity) {
        g.c();
        String e2 = e();
        Object[] objArr = new Object[1];
        n((ViewConfiguration.getDoubleTapTimeout() >> 16) + 8, "\r\u001c\u0014\u0011\u0010\"\u000f\u0013", (byte) (TextUtils.getOffsetBefore("", 0) + 90), objArr);
        g.d(e2, ((String) objArr[0]).intern());
        c(activity).d(new a.InterfaceC0042a<List<o.ep.e>>() { // from class: o.eq.d.4
            public static final byte[] $$a = null;
            public static final int $$b = 0;
            private static int $10;
            private static int $11;
            private static int a;
            private static long b;
            private static int c;

            static {
                init$0();
                $10 = 0;
                $11 = 1;
                a = 0;
                c = 1;
                b = 6546391511125340298L;
            }

            /* JADX WARN: Removed duplicated region for block: B:10:0x0028  */
            /* JADX WARN: Removed duplicated region for block: B:7:0x0020  */
            /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0028 -> B:4:0x0030). Please report as a decompilation issue!!! */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private static void g(short r6, int r7, byte r8, java.lang.Object[] r9) {
                /*
                    int r6 = r6 * 4
                    int r6 = 1 - r6
                    int r7 = r7 * 3
                    int r7 = r7 + 4
                    byte[] r0 = o.eq.d.AnonymousClass4.$$a
                    int r8 = r8 * 3
                    int r8 = r8 + 68
                    byte[] r1 = new byte[r6]
                    int r6 = r6 + (-1)
                    r2 = 0
                    if (r0 != 0) goto L1a
                    r8 = r7
                    r4 = r8
                    r3 = r2
                    r7 = r6
                    goto L30
                L1a:
                    r3 = r2
                L1b:
                    byte r4 = (byte) r8
                    r1[r3] = r4
                    if (r3 != r6) goto L28
                    java.lang.String r6 = new java.lang.String
                    r6.<init>(r1, r2)
                    r9[r2] = r6
                    return
                L28:
                    r4 = r0[r7]
                    int r3 = r3 + 1
                    r5 = r7
                    r7 = r6
                    r6 = r8
                    r8 = r5
                L30:
                    int r6 = r6 + r4
                    int r8 = r8 + 1
                    r5 = r8
                    r8 = r6
                    r6 = r7
                    r7 = r5
                    goto L1b
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass4.g(short, int, byte, java.lang.Object[]):void");
            }

            static void init$0() {
                $$a = new byte[]{17, -62, 117, -122};
                $$b = 254;
            }

            @Override // o.ep.a.InterfaceC0042a
            public final /* synthetic */ void e(List<o.ep.e> list) {
                int i2 = c + Opcodes.DMUL;
                a = i2 % 128;
                boolean z = i2 % 2 != 0;
                c(list);
                switch (z) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            }

            /* JADX WARN: Removed duplicated region for block: B:4:0x0034  */
            /*
                Code decompiled incorrectly, please refer to instructions dump.
                To view partially-correct add '--show-bad-code' argument
            */
            private void c(java.util.List<o.ep.e> r9) {
                /*
                    r8 = this;
                    o.ee.g.c()
                    o.eq.d r0 = o.eq.d.this
                    java.lang.String r0 = r0.e()
                    r1 = 0
                    r2 = 0
                    float r3 = android.util.TypedValue.complexToFraction(r1, r2, r2)
                    int r2 = (r3 > r2 ? 1 : (r3 == r2 ? 0 : -1))
                    r3 = 1
                    int r2 = 1 - r2
                    java.lang.Object[] r4 = new java.lang.Object[r3]
                    java.lang.String r5 = "㼂㽱嬭⬐잇❂\ue6dd뙕︕櫥昋\uf4ea붊ꧽꓵ㔕粐\uef2f\ue354牅㨱⻭∎냡"
                    f(r5, r2, r4)
                    r2 = r4[r1]
                    java.lang.String r2 = (java.lang.String) r2
                    java.lang.String r2 = r2.intern()
                    o.ee.g.d(r0, r2)
                    java.util.Iterator r9 = r9.iterator()
                    r0 = 0
                    r2 = r0
                L2e:
                    boolean r4 = r9.hasNext()
                    if (r4 == 0) goto L97
                    int r4 = o.eq.d.AnonymousClass4.c
                    int r4 = r4 + 39
                    int r5 = r4 % 128
                    o.eq.d.AnonymousClass4.a = r5
                    int r4 = r4 % 2
                    java.lang.Object r4 = r9.next()
                    o.ep.e r4 = (o.ep.e) r4
                    o.eq.d r5 = o.eq.d.this
                    o.eo.e r5 = o.eq.d.f(r5)
                    o.eo.c r5 = r5.s()
                    r6 = 50
                    if (r5 == 0) goto L54
                    r5 = r6
                    goto L56
                L54:
                    r5 = 11
                L56:
                    switch(r5) {
                        case 11: goto L82;
                        default: goto L59;
                    }
                L59:
                    java.lang.String r5 = r4.d()
                    o.eq.d r7 = o.eq.d.this
                    o.eo.e r7 = o.eq.d.g(r7)
                    o.eo.c r7 = r7.s()
                    java.lang.String r7 = r7.a()
                    boolean r5 = r5.equals(r7)
                    if (r5 == 0) goto L82
                    int r2 = o.eq.d.AnonymousClass4.c
                    int r2 = r2 + 73
                    int r5 = r2 % 128
                    o.eq.d.AnonymousClass4.a = r5
                    int r2 = r2 % 2
                    if (r2 != 0) goto L7f
                    r2 = r4
                    goto L82
                L7f:
                    throw r0     // Catch: java.lang.Throwable -> L80
                L80:
                    r9 = move-exception
                    throw r9
                L82:
                    int r4 = o.eq.d.AnonymousClass4.c
                    int r4 = r4 + 117
                    int r5 = r4 % 128
                    o.eq.d.AnonymousClass4.a = r5
                    int r4 = r4 % 2
                    if (r4 == 0) goto L92
                    r6 = 82
                    goto L93
                L92:
                L93:
                    switch(r6) {
                        case 50: goto L96;
                        default: goto L96;
                    }
                L96:
                    goto L2e
                L97:
                    if (r2 == 0) goto L9a
                    goto L9b
                L9a:
                    r1 = r3
                L9b:
                    switch(r1) {
                        case 0: goto L9f;
                        default: goto L9e;
                    }
                L9e:
                    goto Laa
                L9f:
                    android.app.Activity r9 = r2
                    o.ep.c r9 = o.eq.d.c(r9)
                    android.app.Activity r0 = r2
                    r9.c(r0, r2)
                Laa:
                    return
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass4.c(java.util.List):void");
            }

            @Override // o.ep.a.InterfaceC0042a
            public final void e(o.bv.c cVar) {
                g.c();
                Object[] objArr2 = new Object[1];
                f("隇雎돱쏊꺔乐䕸ᗢ垡舟༅坄ᑈ䅠춣随픚ߙ詡퇦鎥옢䬇ፔ偆", (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr2);
                String intern = ((String) objArr2[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                f("䝢䜑㺀亽Ҡ\ue465뤃\ue98b虵\u0f48ꔬꬴ엢챔柒櫃ӻ誥⁃ⶖ䉀䭊\ue136\uef20膣ࠝꎺ껏샱욨汬懔๋蝓ⴤ⌧䶰䑕\uefd0\ue2bc", KeyEvent.normalizeMetaState(0) + 1, objArr3);
                g.e(intern, sb.append(((String) objArr3[0]).intern()).append(cVar).toString());
                int i2 = a + Opcodes.LREM;
                c = i2 % 128;
                int i3 = i2 % 2;
            }

            /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
                jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
                	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
                	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
                	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
                	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
                */
            private static void f(java.lang.String r13, int r14, java.lang.Object[] r15) {
                /*
                    Method dump skipped, instructions count: 350
                    To view this dump add '--comments-level debug' option
                */
                throw new UnsupportedOperationException("Method not decompiled: o.eq.d.AnonymousClass4.f(java.lang.String, int, java.lang.Object[]):void");
            }
        });
        int i2 = h + 55;
        m = i2 % 128;
        switch (i2 % 2 == 0 ? 'F' : '\n') {
            case '\n':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:11:0x0044, code lost:
    
        r11 = 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:12:0x0045, code lost:
    
        if (r11 >= r9) goto L122;
     */
    /* JADX WARN: Code restructure failed: missing block: B:15:0x0049, code lost:
    
        r13 = new java.lang.Object[]{java.lang.Integer.valueOf(r5[r11])};
        r12 = o.e.a.s.get(-1401577988);
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x005b, code lost:
    
        if (r12 == null) goto L20;
     */
    /* JADX WARN: Code restructure failed: missing block: B:19:0x00ae, code lost:
    
        r10[r11] = ((java.lang.Character) ((java.lang.reflect.Method) r12).invoke(null, r13)).charValue();
        r11 = r11 + 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x005e, code lost:
    
        r12 = (java.lang.Class) o.e.a.c((android.view.ViewConfiguration.getScrollDefaultDelay() >> 16) + 17, (char) (1 - (android.view.ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (android.view.ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), 76 - android.view.View.getDefaultSize(0, 0));
        r14 = (byte) 0;
        r4 = new java.lang.Object[1];
        p(r14, (byte) (r14 - 1), (byte) o.eq.d.$$d.length, r4);
        r12 = r12.getMethod((java.lang.String) r4[0], java.lang.Integer.TYPE);
        o.e.a.s.put(-1401577988, r12);
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x00b4, code lost:
    
        r0 = move-exception;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x00b5, code lost:
    
        r1 = r0.getCause();
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x00b9, code lost:
    
        if (r1 != null) goto L26;
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x00bb, code lost:
    
        throw r1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x00bc, code lost:
    
        throw r0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:29:0x00bd, code lost:
    
        r5 = r10;
     */
    /* JADX WARN: Code restructure failed: missing block: B:97:0x018e, code lost:
    
        if (r2.e == r2.a) goto L54;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void n(int r23, java.lang.String r24, byte r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 1090
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.eq.d.n(int, java.lang.String, byte, java.lang.Object[]):void");
    }

    private static void o(byte b2, int i2, short s, int i3, int i4, Object[] objArr) {
        boolean z;
        o.a.f fVar = new o.a.f();
        StringBuilder sb = new StringBuilder();
        int i5 = 2;
        try {
            Object[] objArr2 = {Integer.valueOf(i3), Integer.valueOf(g)};
            Object obj = o.e.a.s.get(-2120899312);
            if (obj == null) {
                Class cls = (Class) o.e.a.c((ViewConfiguration.getDoubleTapTimeout() >> 16) + 11, (char) (ViewConfiguration.getTapTimeout() >> 16), 65 - TextUtils.getCapsMode("", 0, 0));
                byte b3 = (byte) 0;
                byte b4 = (byte) (b3 - 1);
                Object[] objArr3 = new Object[1];
                p(b3, b4, (byte) (b4 & 39), objArr3);
                obj = cls.getMethod((String) objArr3[0], Integer.TYPE, Integer.TYPE);
                o.e.a.s.put(-2120899312, obj);
            }
            int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
            boolean z2 = intValue == -1;
            long j2 = 0;
            switch (z2 ? '^' : 'Q') {
                case Opcodes.FASTORE /* 81 */:
                    break;
                default:
                    byte[] bArr = i;
                    if (bArr != null) {
                        int length = bArr.length;
                        byte[] bArr2 = new byte[length];
                        int i6 = 0;
                        while (i6 < length) {
                            int i7 = $11 + 5;
                            $10 = i7 % 128;
                            if (i7 % i5 != 0) {
                                try {
                                    Object[] objArr4 = {Integer.valueOf(bArr[i6])};
                                    Object obj2 = o.e.a.s.get(494867332);
                                    if (obj2 == null) {
                                        Class cls2 = (Class) o.e.a.c(19 - Color.alpha(0), (char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 16424), (SystemClock.elapsedRealtime() > j2 ? 1 : (SystemClock.elapsedRealtime() == j2 ? 0 : -1)) + Opcodes.FCMPL);
                                        byte b5 = (byte) 0;
                                        byte b6 = (byte) (b5 - 1);
                                        Object[] objArr5 = new Object[1];
                                        p(b5, b6, (byte) (b6 & 41), objArr5);
                                        obj2 = cls2.getMethod((String) objArr5[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj2);
                                    }
                                    bArr2[i6] = ((Byte) ((Method) obj2).invoke(null, objArr4)).byteValue();
                                    i6 >>= 0;
                                    i5 = 2;
                                    j2 = 0;
                                } catch (Throwable th) {
                                    Throwable cause = th.getCause();
                                    if (cause == null) {
                                        throw th;
                                    }
                                    throw cause;
                                }
                            } else {
                                try {
                                    Object[] objArr6 = {Integer.valueOf(bArr[i6])};
                                    Object obj3 = o.e.a.s.get(494867332);
                                    if (obj3 == null) {
                                        Class cls3 = (Class) o.e.a.c(19 - View.resolveSize(0, 0), (char) (16425 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), 150 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)));
                                        byte b7 = (byte) 0;
                                        byte b8 = (byte) (b7 - 1);
                                        Object[] objArr7 = new Object[1];
                                        p(b7, b8, (byte) (b8 & 41), objArr7);
                                        obj3 = cls3.getMethod((String) objArr7[0], Integer.TYPE);
                                        o.e.a.s.put(494867332, obj3);
                                    }
                                    bArr2[i6] = ((Byte) ((Method) obj3).invoke(null, objArr6)).byteValue();
                                    i6++;
                                    i5 = 2;
                                    j2 = 0;
                                } catch (Throwable th2) {
                                    Throwable cause2 = th2.getCause();
                                    if (cause2 == null) {
                                        throw th2;
                                    }
                                    throw cause2;
                                }
                            }
                        }
                        bArr = bArr2;
                    }
                    if (bArr == null) {
                        intValue = (short) (((short) (f[i2 + ((int) (a ^ (-5810760824076169584L)))] ^ (-5810760824076169584L))) + ((int) (g ^ (-5810760824076169584L))));
                        break;
                    } else {
                        byte[] bArr3 = i;
                        try {
                            Object[] objArr8 = {Integer.valueOf(i2), Integer.valueOf(a)};
                            Object obj4 = o.e.a.s.get(-2120899312);
                            if (obj4 == null) {
                                Class cls4 = (Class) o.e.a.c((ViewConfiguration.getEdgeSlop() >> 16) + 11, (char) Color.argb(0, 0, 0, 0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 64);
                                byte b9 = (byte) 0;
                                byte b10 = (byte) (b9 - 1);
                                Object[] objArr9 = new Object[1];
                                p(b9, b10, (byte) (b10 & 39), objArr9);
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(-2120899312, obj4);
                            }
                            intValue = (byte) (((byte) (bArr3[((Integer) ((Method) obj4).invoke(null, objArr8)).intValue()] ^ (-5810760824076169584L))) + ((int) (g ^ (-5810760824076169584L))));
                            break;
                        } catch (Throwable th3) {
                            Throwable cause3 = th3.getCause();
                            if (cause3 == null) {
                                throw th3;
                            }
                            throw cause3;
                        }
                    }
            }
            switch (intValue > 0 ? (char) 24 : '\t') {
                case '\t':
                    break;
                default:
                    fVar.d = ((i2 + intValue) - 2) + ((int) (a ^ (-5810760824076169584L))) + (z2 ? 1 : 0);
                    try {
                        Object[] objArr10 = {fVar, Integer.valueOf(i4), Integer.valueOf(j), sb};
                        Object obj5 = o.e.a.s.get(160906762);
                        if (obj5 == null) {
                            obj5 = ((Class) o.e.a.c(TextUtils.lastIndexOf("", '0') + 12, (char) (AndroidCharacter.getMirror('0') - '0'), 604 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)))).getMethod("o", Object.class, Integer.TYPE, Integer.TYPE, Object.class);
                            o.e.a.s.put(160906762, obj5);
                        }
                        ((StringBuilder) ((Method) obj5).invoke(null, objArr10)).append(fVar.e);
                        fVar.b = fVar.e;
                        byte[] bArr4 = i;
                        if (bArr4 != null) {
                            int length2 = bArr4.length;
                            byte[] bArr5 = new byte[length2];
                            for (int i8 = 0; i8 < length2; i8++) {
                                bArr5[i8] = (byte) (bArr4[i8] ^ (-5810760824076169584L));
                            }
                            bArr4 = bArr5;
                        }
                        if (bArr4 != null) {
                            int i9 = $11 + 7;
                            $10 = i9 % 128;
                            int i10 = i9 % 2;
                            z = true;
                        } else {
                            int i11 = $10 + 57;
                            $11 = i11 % 128;
                            int i12 = i11 % 2;
                            z = false;
                        }
                        fVar.c = 1;
                        int i13 = $11 + 69;
                        $10 = i13 % 128;
                        switch (i13 % 2 != 0) {
                        }
                        while (true) {
                            switch (fVar.c < intValue) {
                                case true:
                                    if (z) {
                                        byte[] bArr6 = i;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((byte) (((byte) (bArr6[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                    } else {
                                        short[] sArr = f;
                                        fVar.d = fVar.d - 1;
                                        fVar.e = (char) (fVar.b + (((short) (((short) (sArr[r7] ^ (-5810760824076169584L))) + s)) ^ b2));
                                    }
                                    sb.append(fVar.e);
                                    fVar.b = fVar.e;
                                    fVar.c++;
                            }
                        }
                    } catch (Throwable th4) {
                        Throwable cause4 = th4.getCause();
                        if (cause4 == null) {
                            throw th4;
                        }
                        throw cause4;
                    }
                    break;
            }
            objArr[0] = sb.toString();
        } catch (Throwable th5) {
            Throwable cause5 = th5.getCause();
            if (cause5 == null) {
                throw th5;
            }
            throw cause5;
        }
    }
}
