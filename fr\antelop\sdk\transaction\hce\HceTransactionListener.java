package fr.antelop.sdk.transaction.hce;

import android.content.Context;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethod;
import fr.antelop.sdk.transaction.TransactionDecision;
import java.util.Date;
import java.util.List;
import o.dj.b;

@Deprecated
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\transaction\hce\HceTransactionListener.smali */
public abstract class HceTransactionListener extends b {
    @Override // o.dj.b
    public abstract void onCredentialsRequired(Context context, List<CustomerAuthenticationMethod> list, HceTransaction hceTransaction);

    @Override // o.dj.b
    public abstract void onTransactionDone(Context context, HceTransaction hceTransaction);

    @Override // o.dj.b
    public abstract void onTransactionError(Context context, AntelopError antelopError);

    @Override // o.dj.b
    public abstract void onTransactionStart(Context context);

    @Override // o.dj.b
    public void onTransactionProgress(Context context, HceTransactionStep hceTransactionStep) {
    }

    @Override // o.dj.b
    public TransactionDecision onTransactionFinalization(Context context, CustomerAuthenticationMethod customerAuthenticationMethod, Date date, HceTransaction hceTransaction) {
        return null;
    }
}
