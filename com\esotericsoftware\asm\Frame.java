package com.esotericsoftware.asm;

import kotlin.io.encoding.Base64;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\asm\Frame.smali */
final class Frame {
    static final int[] a;
    Label b;
    int[] c;
    int[] d;
    private int[] e;
    private int[] f;
    private int g;
    private int h;
    private int[] i;

    static {
        _clinit_();
        int[] iArr = new int[202];
        for (int i = 0; i < 202; i++) {
            iArr[i] = "EFFFFFFFFGGFFFGGFFFEEFGFGFEEEEEEEEEEEEEEEEEEEEDEDEDDDDDCDCDEEEEEEEEEEEEEEEEEEEEBABABBBBDCFFFGGGEDCDCDCDCDCDCDCDCDCDCEEEEDDDDDDDCDCDCEFEFDDEEFFDEDEEEBDDBBDDDDDDCCCCCCCCEFEDDDCDCDEEEEEEEEEEFEEEEEEDDEEDDEE".charAt(i) - 'E';
        }
        a = iArr;
    }

    Frame() {
    }

    static /* synthetic */ void _clinit_() {
    }

    private int a() {
        int i = this.g;
        if (i > 0) {
            int[] iArr = this.f;
            int i2 = i - 1;
            this.g = i2;
            return iArr[i2];
        }
        Label label = this.b;
        int i3 = label.f - 1;
        label.f = i3;
        return (-i3) | 50331648;
    }

    private int a(int i) {
        int[] iArr = this.e;
        if (iArr == null || i >= iArr.length) {
            return i | 33554432;
        }
        int i2 = iArr[i];
        if (i2 != 0) {
            return i2;
        }
        int i3 = i | 33554432;
        iArr[i] = i3;
        return i3;
    }

    /* JADX WARN: Removed duplicated region for block: B:13:0x004d A[LOOP:0: B:6:0x0022->B:13:0x004d, LOOP_END] */
    /* JADX WARN: Removed duplicated region for block: B:14:0x004c A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private int a(com.esotericsoftware.asm.ClassWriter r7, int r8) {
        /*
            r6 = this;
            r0 = 16777222(0x1000006, float:2.3509904E-38)
            r1 = 24117248(0x1700000, float:4.4081038E-38)
            if (r8 != r0) goto Lf
            java.lang.String r0 = r7.I
        L9:
            int r7 = r7.c(r0)
            r7 = r7 | r1
            goto L21
        Lf:
            r0 = -1048576(0xfffffffffff00000, float:NaN)
            r0 = r0 & r8
            r2 = 25165824(0x1800000, float:4.7019774E-38)
            if (r0 != r2) goto L50
            com.esotericsoftware.asm.Item[] r0 = r7.H
            r2 = 1048575(0xfffff, float:1.469367E-39)
            r2 = r2 & r8
            r0 = r0[r2]
            java.lang.String r0 = r0.g
            goto L9
        L21:
            r0 = 0
        L22:
            int r1 = r6.h
            if (r0 >= r1) goto L50
            int[] r1 = r6.i
            r1 = r1[r0]
            r2 = -268435456(0xfffffffff0000000, float:-1.58456325E29)
            r2 = r2 & r1
            r3 = 251658240(0xf000000, float:6.3108872E-30)
            r3 = r3 & r1
            r4 = 33554432(0x2000000, float:9.403955E-38)
            r5 = 8388607(0x7fffff, float:1.1754942E-38)
            if (r3 != r4) goto L3e
            int[] r3 = r6.c
            r1 = r1 & r5
            r1 = r3[r1]
        L3c:
            int r1 = r1 + r2
            goto L4a
        L3e:
            r4 = 50331648(0x3000000, float:3.761582E-37)
            if (r3 != r4) goto L4a
            int[] r3 = r6.d
            int r4 = r3.length
            r1 = r1 & r5
            int r4 = r4 - r1
            r1 = r3[r4]
            goto L3c
        L4a:
            if (r8 != r1) goto L4d
            return r7
        L4d:
            int r0 = r0 + 1
            goto L22
        L50:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.Frame.a(com.esotericsoftware.asm.ClassWriter, int):int");
    }

    private void a(int i, int i2) {
        if (this.e == null) {
            this.e = new int[10];
        }
        int length = this.e.length;
        if (i >= length) {
            int[] iArr = new int[Math.max(i + 1, length * 2)];
            System.arraycopy(this.e, 0, iArr, 0, length);
            this.e = iArr;
        }
        this.e[i] = i2;
    }

    private void a(ClassWriter classWriter, String str) {
        int b = b(classWriter, str);
        if (b != 0) {
            b(b);
            if (b == 16777220 || b == 16777219) {
                b(16777216);
            }
        }
    }

    private void a(String str) {
        char charAt = str.charAt(0);
        if (charAt == '(') {
            c((Type.getArgumentsAndReturnSizes(str) >> 2) - 1);
        } else if (charAt == 'J' || charAt == 'D') {
            c(2);
        } else {
            c(1);
        }
    }

    private static boolean a(ClassWriter classWriter, int i, int[] iArr, int i2) {
        int min;
        int i3 = iArr[i2];
        if (i3 == i) {
            return false;
        }
        if ((268435455 & i) == 16777221) {
            if (i3 == 16777221) {
                return false;
            }
            i = 16777221;
        }
        if (i3 == 0) {
            iArr[i2] = i;
            return true;
        }
        int i4 = i3 & 267386880;
        int i5 = 16777216;
        if (i4 == 24117248 || (i3 & (-268435456)) != 0) {
            if (i == 16777221) {
                return false;
            }
            if ((i & (-1048576)) != ((-1048576) & i3)) {
                int i6 = i & 267386880;
                if (i6 == 24117248 || (i & (-268435456)) != 0) {
                    int i7 = i & (-268435456);
                    int i8 = ((i7 == 0 || i6 == 24117248) ? 0 : -268435456) + i7;
                    int i9 = i3 & (-268435456);
                    min = Math.min(i8, ((i9 == 0 || i4 == 24117248) ? 0 : -268435456) + i9);
                    i5 = min | 24117248 | classWriter.c("java/lang/Object");
                }
            } else if (i4 == 24117248) {
                i5 = (i & (-268435456)) | 24117248 | classWriter.a(i & 1048575, 1048575 & i3);
            } else {
                min = (i3 & (-268435456)) - 268435456;
                i5 = min | 24117248 | classWriter.c("java/lang/Object");
            }
        } else if (i3 == 16777221) {
            if ((i & 267386880) != 24117248 && (i & (-268435456)) == 0) {
                i = 16777216;
            }
            i5 = i;
        }
        if (i3 == i5) {
            return false;
        }
        iArr[i2] = i5;
        return true;
    }

    private static int b(ClassWriter classWriter, String str) {
        int indexOf = str.charAt(0) == '(' ? str.indexOf(41) + 1 : 0;
        int i = 16777220;
        switch (str.charAt(indexOf)) {
            case 'B':
            case 'C':
            case 'I':
            case Opcodes.AASTORE /* 83 */:
            case 'Z':
                return 16777217;
            case 'D':
                return 16777219;
            case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                return 16777218;
            case 'J':
                return 16777220;
            case Base64.mimeLineLength /* 76 */:
                return classWriter.c(str.substring(indexOf + 1, str.length() - 1)) | 24117248;
            case Opcodes.SASTORE /* 86 */:
                return 0;
            default:
                int i2 = indexOf + 1;
                while (str.charAt(i2) == '[') {
                    i2++;
                }
                switch (str.charAt(i2)) {
                    case 'B':
                        i = 16777226;
                        break;
                    case 'C':
                        i = 16777227;
                        break;
                    case 'D':
                        i = 16777219;
                        break;
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        i = 16777218;
                        break;
                    case 'I':
                        i = 16777217;
                        break;
                    case 'J':
                        break;
                    case Opcodes.AASTORE /* 83 */:
                        i = 16777228;
                        break;
                    case 'Z':
                        i = 16777225;
                        break;
                    default:
                        i = classWriter.c(str.substring(i2 + 1, str.length() - 1)) | 24117248;
                        break;
                }
                return ((i2 - indexOf) << 28) | i;
        }
    }

    private void b(int i) {
        if (this.f == null) {
            this.f = new int[10];
        }
        int length = this.f.length;
        int i2 = this.g;
        if (i2 >= length) {
            int[] iArr = new int[Math.max(i2 + 1, length * 2)];
            System.arraycopy(this.f, 0, iArr, 0, length);
            this.f = iArr;
        }
        int[] iArr2 = this.f;
        int i3 = this.g;
        this.g = i3 + 1;
        iArr2[i3] = i;
        int i4 = this.b.f + this.g;
        if (i4 > this.b.g) {
            this.b.g = i4;
        }
    }

    private void c(int i) {
        int i2 = this.g;
        if (i2 >= i) {
            this.g = i2 - i;
            return;
        }
        this.b.f -= i - this.g;
        this.g = 0;
    }

    private void d(int i) {
        if (this.i == null) {
            this.i = new int[2];
        }
        int length = this.i.length;
        int i2 = this.h;
        if (i2 >= length) {
            int[] iArr = new int[Math.max(i2 + 1, length * 2)];
            System.arraycopy(this.i, 0, iArr, 0, length);
            this.i = iArr;
        }
        int[] iArr2 = this.i;
        int i3 = this.h;
        this.h = i3 + 1;
        iArr2[i3] = i;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Code restructure failed: missing block: B:8:0x0036, code lost:
    
        if (r1.charAt(0) == '[') goto L5;
     */
    /* JADX WARN: Removed duplicated region for block: B:108:0x0212  */
    /* JADX WARN: Removed duplicated region for block: B:111:0x0216  */
    /* JADX WARN: Removed duplicated region for block: B:113:0x021a  */
    /* JADX WARN: Removed duplicated region for block: B:114:0x0221  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    void a(int r17, int r18, com.esotericsoftware.asm.ClassWriter r19, com.esotericsoftware.asm.Item r20) {
        /*
            Method dump skipped, instructions count: 1008
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.Frame.a(int, int, com.esotericsoftware.asm.ClassWriter, com.esotericsoftware.asm.Item):void");
    }

    void a(ClassWriter classWriter, int i, Type[] typeArr, int i2) {
        int i3;
        int[] iArr = new int[i2];
        this.c = iArr;
        this.d = new int[0];
        if ((i & 8) == 0) {
            i3 = 1;
            if ((i & 524288) == 0) {
                iArr[0] = classWriter.c(classWriter.I) | 24117248;
            } else {
                iArr[0] = 16777222;
            }
        } else {
            i3 = 0;
        }
        for (Type type : typeArr) {
            int b = b(classWriter, type.getDescriptor());
            int[] iArr2 = this.c;
            int i4 = i3 + 1;
            iArr2[i3] = b;
            if (b == 16777220 || b == 16777219) {
                iArr2[i4] = 16777216;
                i3 = i4 + 1;
            } else {
                i3 = i4;
            }
        }
        while (i3 < i2) {
            this.c[i3] = 16777216;
            i3++;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:66:0x0105  */
    /* JADX WARN: Removed duplicated region for block: B:69:0x0109 A[SYNTHETIC] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    boolean a(com.esotericsoftware.asm.ClassWriter r19, com.esotericsoftware.asm.Frame r20, int r21) {
        /*
            Method dump skipped, instructions count: 278
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: com.esotericsoftware.asm.Frame.a(com.esotericsoftware.asm.ClassWriter, com.esotericsoftware.asm.Frame, int):boolean");
    }
}
