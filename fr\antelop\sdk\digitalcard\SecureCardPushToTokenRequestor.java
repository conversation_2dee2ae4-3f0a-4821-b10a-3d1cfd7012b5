package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.authentication.CustomCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticatedProcess;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.DefaultCustomerAuthenticatedProcessCallback;
import fr.antelop.sdk.exception.WalletValidationException;
import java.util.List;
import o.an.e;
import o.ee.o;
import o.p.e;
import o.p.i;
import o.v.j;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\SecureCardPushToTokenRequestor.smali */
public final class SecureCardPushToTokenRequestor implements CustomerAuthenticatedProcess {
    private final j innerSecureDigitalCardPushToTokenRequestorProcess;

    public SecureCardPushToTokenRequestor(j jVar) {
        this.innerSecureDigitalCardPushToTokenRequestorProcess = jVar;
    }

    public final void setCustomerCredentials(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        this.innerSecureDigitalCardPushToTokenRequestorProcess.d(context, customerAuthenticationCredentials);
    }

    public final List<CustomerAuthenticationMethodType> getAuthenticatedMethods() {
        return o.d(this.innerSecureDigitalCardPushToTokenRequestorProcess.c());
    }

    public final String getCustomerAuthenticationPatternName() {
        return this.innerSecureDigitalCardPushToTokenRequestorProcess.o();
    }

    public final boolean isOnline() {
        return !this.innerSecureDigitalCardPushToTokenRequestorProcess.k();
    }

    public final void setReturnUrl(String str) throws WalletValidationException {
        this.innerSecureDigitalCardPushToTokenRequestorProcess.e(str);
    }

    public final void launch(Context context, CustomCustomerAuthenticatedProcessCallback customCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardPushToTokenRequestorProcess.e(context, new e(context, customCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToTokenRequestorProcess));
    }

    public final void launch(Context context, DefaultCustomerAuthenticatedProcessCallback defaultCustomerAuthenticatedProcessCallback) throws WalletValidationException {
        this.innerSecureDigitalCardPushToTokenRequestorProcess.e(context, new i(context, defaultCustomerAuthenticatedProcessCallback, this, this.innerSecureDigitalCardPushToTokenRequestorProcess));
    }

    public final String getMessage() {
        return null;
    }

    public final CardPushUrl getCardPushUrl() {
        e.d a = this.innerSecureDigitalCardPushToTokenRequestorProcess.a();
        if (a == null) {
            return null;
        }
        return new CardPushUrl(a);
    }
}
