package fr.antelop.sdk.digitalcard.transactioncontrol;

import fr.antelop.sdk.TimePeriod;
import java.math.BigDecimal;
import java.util.Date;
import java.util.TimeZone;
import o.es.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls.smali */
public final class TransactionControls {
    private final Date creationDate;
    private final GlobalControls globalControls;
    private final String id;
    private final e innerTransactionControlUpdateRegister;
    private final MerchantTypeControls merchantTypeControls;
    private final TransactionGeolocationControls transactionGeolocationControls;
    private final TransactionTypeControls transactionTypeControls;
    private final VelocityControls velocityControls;

    public TransactionControls(String str, Date date, e eVar, GlobalControls globalControls, TransactionTypeControls transactionTypeControls, TransactionGeolocationControls transactionGeolocationControls, MerchantTypeControls merchantTypeControls, VelocityControls velocityControls) {
        this.id = str;
        this.creationDate = date;
        this.innerTransactionControlUpdateRegister = eVar;
        this.globalControls = globalControls;
        this.transactionTypeControls = transactionTypeControls;
        this.transactionGeolocationControls = transactionGeolocationControls;
        this.merchantTypeControls = merchantTypeControls;
        this.velocityControls = velocityControls;
    }

    public final String getId() {
        return this.id;
    }

    public final Date getCreationDate() {
        return this.creationDate;
    }

    public final TransactionControlUpdateRegister getTransactionControlUpdateRegister() {
        return new TransactionControlUpdateRegister(this.innerTransactionControlUpdateRegister);
    }

    public final GlobalControls getGlobalControls() {
        return this.globalControls;
    }

    public final TransactionTypeControls getTransactionTypeControls() {
        return this.transactionTypeControls;
    }

    public final TransactionGeolocationControls getTransactionGeolocationControls() {
        return this.transactionGeolocationControls;
    }

    public final MerchantTypeControls getMerchantTypeControls() {
        return this.merchantTypeControls;
    }

    public final VelocityControls getVelocityControls() {
        return this.velocityControls;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$GlobalControls.smali */
    public static final class GlobalControls {
        private final TransactionControl<BigDecimal> alertThreshold;
        private final TransactionControl<Boolean> blockAll;
        private final TransactionControl<BigDecimal> declineThreshold;
        private final TransactionControl<Boolean> tokenizedOnly;

        public GlobalControls(TransactionControl<Boolean> transactionControl, TransactionControl<BigDecimal> transactionControl2, TransactionControl<BigDecimal> transactionControl3, TransactionControl<Boolean> transactionControl4) {
            this.blockAll = transactionControl;
            this.declineThreshold = transactionControl2;
            this.alertThreshold = transactionControl3;
            this.tokenizedOnly = transactionControl4;
        }

        public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
            return this.blockAll;
        }

        public final TransactionControl<BigDecimal> getTransactionDeclineThresholdControl() {
            return this.declineThreshold;
        }

        public final TransactionControl<BigDecimal> getTransactionAlertThresholdControl() {
            return this.alertThreshold;
        }

        public final TransactionControl<Boolean> getAllowOnlyTokenizedTransactionsControl() {
            return this.tokenizedOnly;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls.smali */
    public static final class TransactionTypeControls {
        private final AtmTransactionControls atmTransactionControls;
        private final BrickAndMortarTransactionControls brickAndMortarTransactionControls;
        private final ContactlessTransactionControls contactlessTransactionControls;
        private final CrossBorderTransactionControls crossBorderTransactionControls;
        private final EcommerceTransactionControls ecommerceTransactionControls;
        private final RecurringTransactionControls recurringTransactionControls;

        public TransactionTypeControls(AtmTransactionControls atmTransactionControls, RecurringTransactionControls recurringTransactionControls, BrickAndMortarTransactionControls brickAndMortarTransactionControls, CrossBorderTransactionControls crossBorderTransactionControls, EcommerceTransactionControls ecommerceTransactionControls, ContactlessTransactionControls contactlessTransactionControls) {
            this.atmTransactionControls = atmTransactionControls;
            this.recurringTransactionControls = recurringTransactionControls;
            this.brickAndMortarTransactionControls = brickAndMortarTransactionControls;
            this.crossBorderTransactionControls = crossBorderTransactionControls;
            this.ecommerceTransactionControls = ecommerceTransactionControls;
            this.contactlessTransactionControls = contactlessTransactionControls;
        }

        public final AtmTransactionControls getAtmTransactionControls() {
            return this.atmTransactionControls;
        }

        public final RecurringTransactionControls getRecurringTransactionControls() {
            return this.recurringTransactionControls;
        }

        public final BrickAndMortarTransactionControls getBrickAndMortarTransactionControls() {
            return this.brickAndMortarTransactionControls;
        }

        public final CrossBorderTransactionControls getCrossBorderTransactionControls() {
            return this.crossBorderTransactionControls;
        }

        public final EcommerceTransactionControls getEcommerceTransactionControls() {
            return this.ecommerceTransactionControls;
        }

        public final ContactlessTransactionControls getContactlessTransactionControls() {
            return this.contactlessTransactionControls;
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$AtmTransactionControls.smali */
        public static final class AtmTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public AtmTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$RecurringTransactionControls.smali */
        public static final class RecurringTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public RecurringTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$BrickAndMortarTransactionControls.smali */
        public static final class BrickAndMortarTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public BrickAndMortarTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$CrossBorderTransactionControls.smali */
        public static final class CrossBorderTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public CrossBorderTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$EcommerceTransactionControls.smali */
        public static final class EcommerceTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public EcommerceTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }

        /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionTypeControls$ContactlessTransactionControls.smali */
        public static final class ContactlessTransactionControls {
            private final TransactionControl<Boolean> alertAll;
            private final TransactionControl<Boolean> blockAll;

            public ContactlessTransactionControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2) {
                this.blockAll = transactionControl;
                this.alertAll = transactionControl2;
            }

            public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
                return this.blockAll;
            }

            public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
                return this.alertAll;
            }
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$TransactionGeolocationControls.smali */
    public static final class TransactionGeolocationControls {
        private final TransactionControl<Boolean> alertAll;
        private final TransactionControl<Boolean> blockAll;
        private final TransactionControl<Integer[]> countriesWhitelist;
        private final TransactionControl<String[]> statesWhitelist;

        public TransactionGeolocationControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2, TransactionControl<Integer[]> transactionControl3, TransactionControl<String[]> transactionControl4) {
            this.blockAll = transactionControl;
            this.alertAll = transactionControl2;
            this.countriesWhitelist = transactionControl3;
            this.statesWhitelist = transactionControl4;
        }

        public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
            return this.blockAll;
        }

        public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
            return this.alertAll;
        }

        public final TransactionControl<Integer[]> getWhitelistedCountriesControl() {
            return this.countriesWhitelist;
        }

        public final TransactionControl<String[]> getWhitelistedStatesControl() {
            return this.statesWhitelist;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$MerchantTypeControls.smali */
    public static final class MerchantTypeControls {
        private final TransactionControl<Boolean> alertAll;
        private final TransactionControl<Boolean> blockAll;
        private final TransactionControl<String[]> types;

        public MerchantTypeControls(TransactionControl<Boolean> transactionControl, TransactionControl<Boolean> transactionControl2, TransactionControl<String[]> transactionControl3) {
            this.blockAll = transactionControl;
            this.alertAll = transactionControl2;
            this.types = transactionControl3;
        }

        public final TransactionControl<Boolean> getBlockAllTransactionsControl() {
            return this.blockAll;
        }

        public final TransactionControl<Boolean> getAlertAllTransactionsControl() {
            return this.alertAll;
        }

        public final TransactionControl<String[]> getBlacklistedMerchantTypes() {
            return this.types;
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\transactioncontrol\TransactionControls$VelocityControls.smali */
    public static final class VelocityControls {
        private final TransactionControl<BigDecimal> alertThreshold;
        private final TransactionControl<BigDecimal> blockThreshold;
        private final TransactionControl<TimePeriod> period;
        private final TransactionControl<TimeZone> timeZone;

        public VelocityControls(TransactionControl<TimePeriod> transactionControl, TransactionControl<BigDecimal> transactionControl2, TransactionControl<BigDecimal> transactionControl3, TransactionControl<TimeZone> transactionControl4) {
            this.period = transactionControl;
            this.blockThreshold = transactionControl2;
            this.alertThreshold = transactionControl3;
            this.timeZone = transactionControl4;
        }

        public final TransactionControl<TimePeriod> getPeriod() {
            return this.period;
        }

        public final TransactionControl<BigDecimal> getBlockThreshold() {
            return this.blockThreshold;
        }

        public final TransactionControl<BigDecimal> getAlertThreshold() {
            return this.alertThreshold;
        }

        public final TransactionControl<TimeZone> getTimeZone() {
            return this.timeZone;
        }
    }
}
