package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.security.AccessControlException;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.security.Security;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r6.smali */
public class r6 {
    private static final ThreadLocal a = new ThreadLocal();

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r6$a.smali */
    class a implements PrivilegedAction {
        final /* synthetic */ String a;

        a(String str) {
            this.a = str;
        }

        @Override // java.security.PrivilegedAction
        public Object run() {
            return Security.getProperty(this.a);
        }
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\r6$b.smali */
    class b implements PrivilegedAction {
        final /* synthetic */ String a;

        b(String str) {
            this.a = str;
        }

        @Override // java.security.PrivilegedAction
        public Object run() {
            return System.getProperty(this.a);
        }
    }

    public static boolean a(String str, boolean z) {
        try {
            String a2 = a(str);
            return z ? d(a2) : c(a2);
        } catch (AccessControlException e) {
            return false;
        }
    }

    public static boolean b(String str) {
        try {
            return d(a(str));
        } catch (AccessControlException e) {
            return false;
        }
    }

    private static boolean c(String str) {
        if (str == null || str.length() != 5) {
            return false;
        }
        if (str.charAt(0) != 'f' && str.charAt(0) != 'F') {
            return false;
        }
        if (str.charAt(1) != 'a' && str.charAt(1) != 'A') {
            return false;
        }
        if (str.charAt(2) != 'l' && str.charAt(2) != 'L') {
            return false;
        }
        if (str.charAt(3) == 's' || str.charAt(3) == 'S') {
            return str.charAt(4) == 'e' || str.charAt(4) == 'E';
        }
        return false;
    }

    private static boolean d(String str) {
        if (str == null || str.length() != 4) {
            return false;
        }
        if (str.charAt(0) != 't' && str.charAt(0) != 'T') {
            return false;
        }
        if (str.charAt(1) != 'r' && str.charAt(1) != 'R') {
            return false;
        }
        if (str.charAt(2) == 'u' || str.charAt(2) == 'U') {
            return str.charAt(3) == 'e' || str.charAt(3) == 'E';
        }
        return false;
    }

    public static int a(String str, int i) {
        String a2 = a(str);
        return a2 != null ? Integer.parseInt(a2) : i;
    }

    public static String a(String str) {
        String str2;
        String str3 = (String) AccessController.doPrivileged(new a(str));
        if (str3 != null) {
            return str3;
        }
        Map map = (Map) a.get();
        return (map == null || (str2 = (String) map.get(str)) == null) ? (String) AccessController.doPrivileged(new b(str)) : str2;
    }
}
