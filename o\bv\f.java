package o.bv;

import android.app.Activity;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.nfc.NfcAdapter;
import android.nfc.cardemulation.CardEmulation;
import android.os.Build;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopError;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.AsyncRequestType;
import fr.antelop.sdk.Wallet;
import fr.antelop.sdk.WalletLockReason;
import fr.antelop.sdk.WalletManagerCallback;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.authentication.CustomerAuthenticationMethodType;
import fr.antelop.sdk.authentication.CustomerCredentialsRequiredReason;
import fr.antelop.sdk.authentication.LocalAuthenticationErrorReason;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.transaction.hce.HceTransaction;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicBoolean;
import kotlin.io.encoding.Base64;
import kotlin.text.Typography;
import o.bf.b;
import o.bg.b;
import o.bh.a;
import o.bh.b;
import o.ee.j;
import o.ee.l;
import o.ee.o;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\f.smali */
public final class f implements o.b.a, b.e, b.c, a.c, o.bw.b, o.by.b, o.bz.e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static final AtomicBoolean b;
    private static long g;
    private static char[] j;
    private static int l;

    /* renamed from: o, reason: collision with root package name */
    private static int f43o;
    private o.bz.a a;
    private o.b.c c;
    private Context d;
    private WalletManagerCallback e;
    private o.by.a f;
    private boolean h;
    private Object i;

    static void init$0() {
        $$a = new byte[]{85, 91, 121, -13};
        $$b = 47;
    }

    static void r() {
        char[] cArr = new char[2696];
        ByteBuffer.wrap("¡i;ê\u0094\u0006n©ËÂ¤C>\u0099\u009b0tlÎ\u0081«<\u0004a\u009eñ{\u001aÕ¹®Û\u000b\u0005å¶,ª¶\u0002\u0019ïãVF-)\u008f³d\u0016Öù\u009dCb&Óðõj}Å\u0090?.\u009aCõúo\u001a,ª¶\u0002\u0019ïãKF<)\u009e³e\u0016\u009dùÀC-&\u008c\u0089å\u00131öþXT#\u0003\u0086èhAó(V\u00818x\u0083Éf¸È\u001aSÇ6\u0088\u0098\u0019cæÅ\\¨-3\u0085\u0095|x&Ã\u0083©\u00933;\u009cÖfrÃ\u0005¬§6\\\u0093¬|¤Æ[£¸\f½\u0096.sÐÝm¦\u0000\u0003ßídvXÓ\u0094½c\u0006ÁãÈM!Öñ³\u009a\u001d9æÛ@e-\u0016¶è\u0010\u0002ý^Fú\r<\u0097\u00878wÂÔg\u009f\b\u0017\u0092ú7^Ø\tbë\u0007P¨h2Õ,\u008c¶\u0003\u0019õã@F+)Ý³u\u0016Üù\u009aCn&Î\u0089«\u0013\u0017öøXR#!\u0086¡h\u0004óaVß89\u0083ôf¿È\u0006SÝ6¬\u0098\u000fcæÅ\\¨}3Ë\u00955zhà¯OaµÈ\u0010´\u007f\u001eåñ@W¯\u0003\u0015¬,\u00ad¶\u0004\u0019òãFF6)\u0093³\u007f\u0016Ðù\u008aCy&\u0081\u0089£\u0013\u000böòX\\#u\u0086ÈhCó5V\u00808u\u0083Òf¡ÈUSþ6¬\u0098\rcéÅ\\¨)3Ñ\u0095xx(Ã\u0083¥`\bÂ\u0093¼u\u000fØ±¢\u001b\u0005Gè£,\u008c¶\u0015\u0019èãQFy)\u0099³x\u0016Æù\u008aCb&Ï\u0089«\u0013\u001cöþXE#}\u0086 ,ª¶\u0001\u0019äãDF7,\u009e¶\f\u0019íãIF<)\u0089³\\\u0016Ôù\u0087Cl&Æ\u0089 \u0013\u000b +:¹\u0095XoüÊ\u0089¥<?\u0084\u009aMu=ÏÖªu\u0005\u0017\u009f©zZÔ¤¯\u0089\nOä¸\u007f\u009aÚ?´Ø\u000f(ê\u0007D¯ßrº\u0016\u0014±ïSIø$\u008d¿ ,\u008a¶\u0018\u0019òãQF6)\u0090³t\u0016Çù¨Cx&Õ\u0089\u00ad\u0013\u001cöóXE#<\u0086êhLó5V\u008c8v\u0083Óf\u0092È\u0007SÌ6©\u0098\u0004cëÅM¨43\u0090\u0095yx:,\u008a¶\u0018\u0019òãQF6)\u0090³t\u0016Çù¨Cx&Õ\u0089\u00ad\u0013\u001cöóXE#<\u0086êhLó5V\u008c8v\u0083Óf\u009cÈ\u0010SÝ6¥\u0098\u000ecá,ó¶|\u0019\u008aã?FT)¢³\u0002\u0016¥ùñC\u001d&«\u0089Î\u0013.öË,º¶\b\u0019õãfF,)\u008e³e\u0016Úù\u0084Ch&Ó\u0089\u0086\u0013\u000böøXU#0\u0086çhYó(V\u00848u\u0083Îf\u0097È\u001aSÛ6\u0099\u0098\u0013cäÅW¨.3\u0090\u0095vx=Ã\u0084¥n\bË\u0093ñuTØ±¢\u0018\u0005IèþrDÕ1¸\u008d\u0002tåßH²Òzµ\u008d\u0018¢â\u0017Eü/Y²4\u0015\u009bÿ]B$%\u0080\u008fi\u0012Ê\u0087ô\u001dR²±H\u0018íb\u0082Ï\u0018\u0000½\u0085RÐè<\u008d\u0096\"õ¸@]\u0097ó\u001d\u0088j-¹Ã\u0000X~ýØ\u00933(\u008aÍàcEøß\u009dº\u001aï\u0080}/\u009cÕ8pM\u001fø\u0085, «Ïûu\u0017\u0010\u0082¿Ñ%iÀ\u009fn/\u0015JnlôÉ[,¡\u0081\u0004ìkYñøT]»\b\u0001ád@ËeQÖ´(\u001a\u0095aøÄ'*\u009c±×\u0014Ez´Á\u0010$u\u008aÀ\u0011%tmÚÎ!%\u0087\u009fêùqB×ô:á\u0081_çàJ\nÑm7Ð\u009a<àÔG\u0085ªr0À\u0097ÈúW@¿§\u0011\nx\u0090è÷#Zn È\u0007!~\bä\u00adKH±å\u0014\u0088{=á\u009cD9«l\u0011\u0085t$Û\u0001A²¤L\nñq\u009cÔC:ø¡³\u0004!jÐÑt4\u0011\u009a¤\u0001Ad\tÊª1A\u0097ûú\u009da&Ç\u0090*\u0082\u0091'÷ÐZ Á\u0012'\u00ad\u008aXðüWìº\u0005 º\u0087Àê\u001fPÙ·x\u001a\u001c\u0080\u008cçjJ\u0005°£\u0017W}ýà\u009aG4B\u0015Ø\u0092wv\u008dÚ(¼G\u0019Ý¡xc\u00978-ÔH}ç\u0000}»\u0098H,é¶C\u0019¯ã\u000bFy)\u008a³x\u0016Áù\u0081C-&Ä\u0089·\u0013\u000böòXC#h\u0086ÒM}×Õ,\u0094,\u0085¶\u0002\u0019æãJF,)\u0089³1\u0016æù¼CN&â\u0089\u0080\u0013*öÎ\u000fk\u0095ã:\u000eÀªeÝ\n\u007f\u0090\u00845=Úg`\u0082\u0005`ªP0÷Õ\\{\u0091\u0000Ú¥\u001cK©ÐÌuk\u001b\u0088 |Exë×p\r\u0015\f»í@\u0005æ¶\u008bÝ\u0010w¶\u0091[Úà,\u0086³+\u0011°{Vßû5\u0081\u0087&Ûµp/û\u0080\rz¸ßÍ°j*\u0099\u008fm`YÚ¶¿\u001c\u0010\u001d\u008aìo\u0004Á§ºÌ\u001f\u0016ñ°jËÏ=¡\u0082\u001a*ÿGQãÊ4¯V\u0001íú\u0014\\®1Ëª)\f«áðZ\\<µ\u0091\b\nsìÀAI;º\u009cøq\u0001ë±L\u009d!s\u009b\u0080|(Ñ^K\u009e,;\u0081\u0004{Æ[?Á\u0095ny\u0094Ý1¯^JÄ©a\u0007\u008e\u001f4¾Q\u0005þadÀ\u00819/ÚTØ,\u0088¶\u0003\u0019õã@F5)\u0092³a\u0016\u0095ù¡CN&ä\u0089å\u0013\u0014öüX_#4\u0086îhHó3VÅ8z\u0083Òf¿È\u001bSÌ6®\u0098\u0015cìÅV¨33Ñ\u0095Sx\bÃ¤¥M\bð\u0093\u008bu8Ø½¢\u0015\u0005\nèÿrDÕ!¸\u009c\u0002såÅH¼ÒhµÁ\u0018²âEEË/X² \u0015\u0080ÿ@B?%\u0084\u008fa,ª¶\u001f\u0019äãAF<)\u0093³e\u0016Üù\u0088Ca&Ò\u0089å\u0013\u0010öóXG#4\u0086åhDó%V\u008c8m\u0083ÄfñÈ\u0010SÛ6¿\u0098\u000ec÷Å\u0019¨)3\u009e\u00955x+Ã\u0088¥!\bÌ\u0093·u\u0019Øø¢V\u0005\bèùrDÕ! UºÍ\u0015,ïÍJá%G¿¶\u001a\u000bõHO¡*\f\u0085i\u001f\u0091ú T\u008a/ø\u008a3dÅÿèZX4¥\u008f\u001dj|ÄÓ_\u0015:l\u0094Êo,É\u0085¤ü?V\u0099³t¡ÏF©»\u0004\b\u009fuyÐÔ7®\u0089\tÈä$~\u0085Ùþ´\u0011\u000e±é\u0016D=Þ¯¹\n\u0014}î\u008dI<#\u0094¾í\u0019^ó\u0089N¥)^\u0083¤\u001e\u0005ù}S\u0099.)\u0089icÀþiX\u00823ÿ\u008ePhªÃ]^B8°\u0093\u001bn\u007fÈÔ£;=\u008d\u0098ñs8ÍÅ¨ú\u0003H\u009d¥ªY0á\u009f\u0000eáÀÍ¯k5\u009a\u0090'\u007fdÅ\u008d  \u000fE\u0095½p\fÞ¦¥Ô\u0000\u001fîéuÄÐt¾\u0089\u00051àPNÿÕ9°@\u001eæå\u0000C©.Ðµz\u0013\u009fþ\u008dEj#\u0097\u008e$\u0015Yóü^\u001b$¥\u0083än\bô©SÒ>=\u0084\u0091c4ÎGT\u00883i\u009eDdïÃ]©°4Û\u0093gy¬ÄÅ£l\t\u0085\u0094}s_Ùú¤\u0003\u0003@éèt\u0011\u0003w\u0099Ò6\u0011Ì\u009aiü\u0006I\u009c¬9\u000bÖHl\u008b\t\u0011¦x<ÄÙ)w\u0094\fÉ©9G\u0092ÜñyS\u0017\u00ad¬\u001eIOçÊ|\u0014\u0019u·ÞL1ê©\u0087ï\u001cTº\u00adWîì]\u008a¤'\u001d¼gZÂ÷\u000f\u008d\u008a*ÿÇ3]\u0099úú\u0097Oâ6x\u0093×P-Û\u0088½ç\b}íØJ7\t\u008dÊèPG9Ý\u00858h\u0096Õí\u0088Hx¦Ó=°\u0098\u0012öìM_¨\u000e\u0006\u0083\u009d_ø1V\u0098\u00ad{\u000bÌf\u008cý\u0002[ñ¶°\r\u000bkðÆA] »\u0082\u0016olêË\u0097&Z¼Þ\u001b¼v\u0007Ìê,\u0085¶\u0002\u0019âãNFy)®³D\u0016öùªCH&ò\u0089\u0096\u0010»\u008a\t%èßLz9\u0015\u008c\u008f4*ÜÅ\u0083\u007fk\u001aÏµ©/\u0012Êÿd\u0014\u001f8ºíT[Ïdj\u0086\u0004}¿ÑZ¸ô\u0015oÈ,¦¶\u0003\u0019Åã@F5)\u0098³e\u0016Ðù¾Cl&Í\u0089©\u0013\u001cöéXr#:\u0086äh]ó-V\u00808m\u0083ØfµÈ]S\u0080_íÅHj\u0089\u0090\u00065wZÕÀ1e½\u008aÐ0#U\u008eúë`\\\u0085¢+\u0013P\u007fõ®\u001b\u0015\u0080Y%ÛK1ð\u0095\u0015ÿ»M \u0091,¦¶\u0003\u0019ÂãMF<)\u009e³z\u0016öù\u009bCh&Å\u0089 \u0013\u0017öéXX#4\u0086åh^ó\u0007V\u00848p\u0083Ñf´È\u0011S\u00896÷\u0098AcòÅK¨23\u009f\u0095rxiÃ\u008e¥s\bÀ\u0093½u\u0018Øÿ¢A\u0005\u0000èìrMÕ6¸Ù\u0002uåÐH£Òlµ\u008d\u0018£â\u0000Eü/S²q\u0015\u0085ÿ[B\"%\u0097\u008fl\u0012Ýõ¸_\u0015î@táÛ\u0010!µ\u0084Àë#q\u008cÔ9;r\u0081\u0097ä:KUÑó4\n\u009a®áÇD\u0004ªó1×\u0094zú\u0091A&¤\u000f\né\u00912ôVZñ¡[\u0007·jÑñ`W\u009dºÞ\u0001wg\u009aÊ?x\u008aâ/Mî·a\u0012\u0010}²çVBÚ\u00ad·\u0017DréÝ\u008cG;¢Å\ftw\u0018ÒÉ<r§(\u0002»lG×þ2\u008f\u0013Ü\u0089y&¸Ü7yB\u0016é\u008c\f)ªÆÐ|\u0005\u0019¾¶Û,fÉ\u0089g?\u001cF¹\u0092W;ÌHiÌ\u0007\u0016¼¤YÈ÷jl \tÄ-Í·h\u0018©â&GS(ø²\u001d\u0017»øÁB\u0014'¯\u0088Ê\u0012w÷\u0098Y.\"W\u0087\u0083i*òYWË9\u0000\u0082¤gÕÉl,¦¶\u0003\u0019Òã\\F7)\u009e³y\u0016Çù\u0086Cc&È\u0089¿\u0013\u001cöÞXG#8\u0086Êh_ó$V\u00818|\u0083Óf¥È\u001cSÈ6¡\u0098\u0012cÖÅL¨>3\u0092\u0095px:Ã\u009e,¦¶\u0003\u0019Òã\\F7)\u009e³y\u0016Çù\u0086Cc&È\u0089¿\u0013\u001cöÞXG#8\u0086Êh_ó$V\u00818|\u0083Óf¥È\u001cSÈ6¡\u0098\u0012cÀÅK¨/3\u009e\u0095g,»¶\b\u0019æãLF*)\u0089³t\u0016Çù¯Cb&Ó\u0089\u0095\u0013\u000böøXW#0\u0086ûh_ó$V\u00818J\u0083Øf£È\u0003SÀ6®\u0098\u0004c¥Å\u0014¨}3¥\u0095}x,ÃÍ¥I\bæ\u0093\u009cu]ØÁ¢G\u0005\u0006èérTÕ&¸\u008d\u0002=åØH¦Ò)µÃ\u0018®â\u0011E¹/N²$\u0015\u0085ÿYB\"%\u0093\u008fq\u0012Üõ¹_Q\"÷\u0085°oMòõTM?<\u0082ÝdBÏñR¢,¯¶\u001f\u0019¯ãDF7)\u0089³t\u0016Ùù\u0086C}&\u008f\u0089µ\u0013\u000böøXW#0\u0086ûh_ó$V\u00818J\u0083Øf£È\u0003SÀ6®\u0098\u0004cÊÅW¨\u001b3\u009e\u0095gx,Ã\u008a¥s\bÊ\u0093¬u\u0013Øõ«ð1C\u009e\u00add\u0007Áa®Â4?\u0091\u008c~äÄ)¡\u0098\u000eÞ\u0094@q³ß\u001c¤{\u0001°ï\u0014toÑÊ¿\u0001\u0004\u0093áèOHÔ\u008b±å\u001fOäîB_/6´×\u0012?ÿlDÏ\",\u008f\u008b\u0014áòB_ú%\u001a\u0082Co²õ\u000bR.?Ü\u00859b\u008eÏ¾U12\u0083\u009fþe\u0002Âò¨\u00155u\u0092Ðx\u0011Åo¢Î\b+\u0095\u0080rÿØT¥¹\u0002¢èOu¾ÓN¸{\u0005ÅãzH\u009bÕÌ³'\u0018\u0088åâCW(²°\u0083*0\u0085Þ\u007ftÚ\u0012µ±/L\u008aÿe\u0097ßZºë\u0015\u00ad\u008f3jÀÄo¿\b\u001aÃôgo\u001cÊ¹¤r\u001fàú\u009bT;Ïøª\u0096\u0004<ÿ\u009dY,4E¯§\tBäQ_§9\\\u0094ú\u000f\u0088é6DÝ>\u007f\u00990tÁîpI\u0012$¯\u009e\u0005yùÔ\u0082NB)æ\u0084\u0090~?ÙÍ³`.E\u0089íc\u007fÞ\u0013¹º\u0013|\u008eåi\u0084Ã9¾Ù\u0019\u0094ó'n\u0099Èt£\u0012\u001eåøGSøÎ½¨Y,»¶\b\u0019æãLF*)\u0089³t\u0016Çù¯Cb&Ó\u0089\u0095\u0013\u000böøXW#0\u0086ûh_ó$V\u00818J\u0083Øf£È\u0003SÀ6®\u0098\u0004c¥Å\u0014¨}3¤\u0095{x(Ã\u008f¥m\bÀ\u0093ùu\tØþ¢\u0015\u0005\u000eèèrUÕe¸º\u0002|åÃH±ÒLµÀ\u0018´â\tEø/I²8\u0015\u009aÿGBm%\u0088\u008fk\u0012Êõ©_\u0010\"û\u0085ªo\b\u0084\u008b\u001e>±ÊKzî\u000e\u0081¡\u001bW,»¶\b\u0019æãLF*)\u0089³t\u0016Çù¯Cb&Ó\u0089\u0095\u0013\u000böøXW#0\u0086ûh_ó$V\u00818J\u0083Øf£È\u0003SÀ6®\u0098\u0004c¥Å\u0014¨}3\u0090\u0095vx=Ã\u0084¥w\bÌ\u0093\u00adu\u0004Ø±¢G\u0005\fèêrHÕ6¸\u008d\u0002xåÃH°Òm'Ó½`\u0012\u008eè$MB\"á¸\u001c\u001d¯òÇH\n-»\u0082ý\u0018cý\u0090S?(X\u008d\u0093c7øL]é3\"\u0088°mËÃkX¨=Æ\u0093lhÍÎ|£\u00158ø\u009e\u001esUÈì®\u001f\u0003¤\u0098Å~lÓÙ©>\u000e`ã\u008by'ÞB³å\tUî¾CØÙ\u0015¾å\u0013ÛéhN\u0096$<¹J\u001eéô$IW.ì\u0084\të\u009eq-ÞÃ$i\u0081\u000fî¬tQÑâ>\u008a\u0084GáöN°Ô.1Ý\u009frä\u0015AÞ¯z4\u0001\u0091¤ÿoDý¡\u0086\u000f&\u0094åñ\u008b_!¤\u0080\u00021oXôµRS¿\u0018\u0004¡bRÏéT\u0088²!\u001f\u0094eyÂ?/\u0088µj\u0012\u000f\u007f¨Å\u0018\"õ\u008f\u009c\u0015@rçß\u0093%%\u0082Øè8u\u0000Ò¿8,\u0085\u001aâ¡HGÕõ2\u008b\u0098 åÕB\u009e@ËÚxu\u0096\u008f<*ZEùß\u0004z·\u0095ß/\u0012J£åå\u007f{\u009a\u00884'O@ê\u008b\u0004/\u009fT:ñT:ï¨\nÓ¤s?°ZÞôt\u000fÕ©dÄ\r_ïù\n\u0014\u0019¯ïÉ\u0014d²ÿÀ\u0019~´\u0095Î7ix\u0084\u0089\u001e8¹ZÔçnM\u0089±$Ê¾\nÙ®tØ\u008ew)\u0085C(,»¶\b\u0019æãLF*)\u0089³t\u0016Çù¯Cb&Ó\u0089\u0095\u0013\u000böøXW#0\u0086ûh_ó$V\u00818J\u0083Øf£È\u0003SÀ6®\u0098\u0004c¥Å\u0014¨}3Ñ\u0095px1Ã\u008e¥d\bÕ\u0093\u00adu\u0014Øþ¢[,¼¶\u0003\u0019óã@F>)\u0094³b\u0016Áù\u008cC\u007f&ç\u0089ª\u0013\u000böÍXC#0\u0086ïhHó3V\u00978|\u0083Ùf\u0082È\u0010SÛ6»\u0098\bcæÅ\\¨}3Ü\u00955x\u001dÃ\u0085¥d\b\u0085\u0093\u0091u>ØÔ¢\u0015\u00059èÿrNÕ!¸\u008c\u0002~åÅHõÒ`µÞ\u0018áâ\u000bEö/I²q\u0015\u0086ÿ\\B=%\u0091\u008fj\u0012Ëõ©_\u0014\"ñ\u0085éo\u000fòøT\u0005?-\u0082\u0095dtÏ\u0095Rº4I\u009fê¯\u000f5°\u009a@`óÅ\u008dª'0Ñ\u0095rz?ÀÌ¥T\n\u0019\u0090¸u~Ûð \u0083\u0005\\ëûp\u0080Õ$»Ï\u0000jå1K£Ðhµ\b\u001b»àUFï+Î°o\u0016\u0086û\u0097@?&Ü\u008b\u007f\u0010\fö«[Q!ò\u0086úkZñóV\u0082;+\u0081\u008eflË\tQÎ6>\u009b\u0001a³Æ^¬¢1Â\u0096%|õÁ\u0090¦!\fß\u0091nv\u000bÜ°¡O\u0006\u0014ì¹q\u0012×ÿ¼\u009e\u0001nçËLuÑz·Û\u001c|á\u0017G¨,B²ç\u0017\u0082\u0091G\u000bø¤\b^»ûÅ\u0094o\u000e\u0099«:Dwþ\u0084\u009b\u001c4Q®ðK6å¸\u009eË;\u0014Õ³NÈël\u0085\u0087>\"Ûyuëî \u008b@%óÞ\u001dx§\u0015\u0086\u008e'(ÎÅÜ~y\u0018Úµ+.LÈôe\u000f\u001f©¸ûU\u0005Ï®hÌ\u0005c¿\u0092X#õAo\u009c\bv¥J_ñø\u0011\u0092µ\u000fÃ¨lB¾ÿÓ\u009862Þ¯,H@âé\u009f/8VÒ÷O\néª\u0082Ç?tÙÊr'ïa\u0089Ö\"4ßKyî\u0012\n,¼¶\u0003\u0019óã@F>)\u0094³b\u0016Áù\u008cC\u007f&ç\u0089ª\u0013\u000böÍXC#0\u0086ïhHó3V\u00978|\u0083Ùf\u0082È\u0010SÛ6»\u0098\bcæÅ\\¨}3Ü\u00955x\u001cÃ\u0083¥`\bÇ\u0093µu\u0018Ø±¢A\u0005\u0006è\u00adrFÕ ¸\u008d\u0002=åòH´Ò{µÉ\u0018\u0084â\bEì/Q²0\u0015\u0081ÿ@B\"%\u008f\u008f%\u0012Ðõ³_\u0002\"á\u0085¨o\u0003òâT@5i¯Ö\u0000&ú\u0095_ë0Aª·\u000f\u0014àYZª?2\u0090\u007f\nÞï\u0018A\u0096:å\u009f:q\u009dêæOB!©\u009a\f\u007fWÑÅJ\u000e/n\u0081Ýz3Ü\u0089±¨*\t\u008càaýÚ[¼ \u0011\u0019\u008azlÁÁ0»\u0099\u001c\u009cñ-k\u009aÌâ¡I\u001b¯ü\rQsË¨¬\u001d\u0001fûÕ\\(,¼¶\u0003\u0019óã@F>)\u0094³b\u0016Áù\u008cC\u007f&ç\u0089ª\u0013\u000böÍXC#0\u0086ïhHó3V\u00978|\u0083Ùf\u0082È\u0010SÛ6»\u0098\bcæÅ\\¨}3Ü\u00955x(Ã\u008e¥u\bÌ\u0093¯u\u0014Øå¢L\u0005Ièîr@Õ+¸\u0097\u0002råÅHõÒnµÈ\u0018µâEEì/S²#\u0015\u0090ÿNB$%\u0092\u008fq\u0012Üõ¯_\u0014\"ñÊ\u001cP£ÿS\u0005à \u009eÏ4UÂða\u001f,¥ßÀGo\nõ«\u0010m¾ãÅ\u0090`O\u008eè\u0015\u0093° ÞÝeN\u0080\u0014.§µ\u007fÐ\u0004~¢\u0085@#¹NÐÕqsÛ\u009e\u0086%mCÔîku\u000b\u0093¸>VDüãº\u000eY\u0094ó3\u0084^-äÔ\u0003~®\u001b4\u0089S}þ\u000e\u0004¶£JÉôT\u0093ó9\u0019ì,¼¶\u0003\u0019óã@F>)\u0094³b\u0016Áù\u008cC\u007f&ç\u0089ª\u0013\u000böÍXC#0\u0086ïhHó3V\u00808}\u0083îf´È\u0007Sß6¤\u0098\u0002càÅ\u0019¨p3Ñ\u00955x,Ã\u0095¥b\bÀ\u0093©u\tØø¢Z\u0005\u0007è\u00adr\u001bÕe¸Ü\u0002nå\u0091èýrXÝ\u008f'\u000e\u0082fíÇw>Ò\u008b=á\u00873â\u008eMê×K2¨\u009c\rç}B\u0081¬\u00037y\u0092Ýü'G\u0095¢ù\u0082§\u0018\u0002·ÕMTè<\u0087\u009d\u001dd¸ÑW»íi\u0088Ô'°½\u0011XòöW\u008d'(ÍÆ^]2ø\u008b\u0096j-\u0090Èðf\u0011ýÚ\u0098¾6\u000fÍök\u0018\u0006f\u009dÐó\u0092i7Æù<~\u0099\u000eö¨lIÉÀ&¨\u009cMùýV\u0094Ì#)Ý\u0087lü\u0002YÜ·m,\u001c\u0089¾çC\\Ú¹\u0090\u0017\"\u008cþé\u009cG&¼Â\u001a-wSìå,¦¶\u0003\u0019ÍãJF:)\u009c³}\u0016ôù\u009cCy&É\u0089 \u0013\u0017öéXX#6\u0086èhYó(V\u008a8w\u0083ûf°È\u001cSÅ6¸\u0098\u0013càÅ\u0019¨g3Ñ\u00950x:ÃÍ¥,\b\u0085\u0093«u\u0018Øð¢F\u0005\u0006èãr\u0001Õ`¸\u008a\u0002=å\u009cHõÒdµÈ\u0018²â\u0016Eø/Z²4\u0015Õÿ\fB>r\tè\u0091Gp½\u0091\u0018\u0085w*íÀH\u0001§-\u001dëxZ×5M\u0098¨j\u0006Ñ}áØt6Ê\u00adõ\b\u001ffâÝ]8e\u0096\u0092\rHh)Æ\u0085=~\u009bßö½m\u0000Ëå&ý\u009d\u001bûìV\u0011Í9+\u0081\u0086`ü\u0081[®¶],þ".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 2696);
        j = cArr;
        g = 4812641711493068397L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0029  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0021  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0029 -> B:4:0x0034). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void u(short r6, int r7, int r8, java.lang.Object[] r9) {
        /*
            byte[] r0 = o.bv.f.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            int r8 = r8 + 102
            int r6 = r6 * 3
            int r6 = r6 + 4
            byte[] r1 = new byte[r7]
            r2 = 0
            if (r0 != 0) goto L19
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            goto L34
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r8
            r1[r3] = r4
            int r3 = r3 + 1
            if (r3 != r7) goto L29
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L29:
            r4 = r0[r6]
            r5 = r7
            r7 = r6
            r6 = r4
            r4 = r3
            r3 = r1
            r1 = r0
            r0 = r9
            r9 = r8
            r8 = r5
        L34:
            int r6 = r6 + r9
            int r7 = r7 + 1
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            r5 = r8
            r8 = r6
            r6 = r7
            r7 = r5
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.f.u(short, int, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        f43o = 1;
        r();
        Gravity.getAbsoluteGravity(0, 0);
        AudioTrack.getMinVolume();
        TextUtils.getTrimmedLength("");
        TextUtils.getTrimmedLength("");
        ExpandableListView.getPackedPositionForChild(0, 0);
        KeyEvent.normalizeMetaState(0);
        View.combineMeasuredStates(0, 0);
        View.MeasureSpec.makeMeasureSpec(0, 0);
        TextUtils.indexOf((CharSequence) "", '0', 0);
        KeyEvent.normalizeMetaState(0);
        KeyEvent.keyCodeFromString("");
        KeyEvent.keyCodeFromString("");
        TextUtils.getTrimmedLength("");
        AndroidCharacter.getMirror('0');
        View.resolveSizeAndState(0, 0, 0);
        Color.alpha(0);
        ViewConfiguration.getScrollBarFadeDuration();
        TextUtils.getCapsMode("", 0, 0);
        b = new AtomicBoolean(false);
        int i = l + 79;
        f43o = i % 128;
        switch (i % 2 == 0) {
            case false:
                return;
            default:
                int i2 = 3 / 0;
                return;
        }
    }

    public f(Context context, WalletManagerCallback walletManagerCallback, Object obj) throws WalletValidationException {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), ViewConfiguration.getEdgeSlop() >> 16, 18 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (ViewConfiguration.getWindowTouchSlop() >> 8), 18 - ExpandableListView.getPackedPositionGroup(0L), 11 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            t((char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 56447), 29 - (ViewConfiguration.getJumpTapTimeout() >> 16), 7 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr3);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr3[0]).intern());
        }
        this.d = context;
        this.e = walletManagerCallback;
        this.i = obj;
        o.fl.d.a(context);
        o.ee.g.e();
        l.d(context);
    }

    public final void g() {
        int i = f43o + 21;
        l = i % 128;
        int i2 = i % 2;
        try {
            a((CustomerAuthenticationCredentials) null, (CustomerAuthenticationCredentials) null);
            int i3 = f43o + 35;
            l = i3 % 128;
            int i4 = i3 % 2;
        } catch (WalletValidationException e) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            t((char) (36329 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1))), ViewConfiguration.getScrollDefaultDelay() >> 16, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 18, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), Color.argb(0, 0, 0, 0) + 36, 34 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
        }
    }

    public final void a(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        o.h.d dVar;
        o.c.a aVar;
        int i = l + Opcodes.LSHL;
        f43o = i % 128;
        Object obj = null;
        switch (i % 2 == 0 ? '`' : 'W') {
            case Opcodes.IADD /* 96 */:
                obj.hashCode();
                throw null;
            default:
                if (this.c == null) {
                    o.ee.g.c();
                    Object[] objArr = new Object[1];
                    t((char) (36328 - Process.getGidForName("")), MotionEvent.axisFromString("") + 1, 18 - ExpandableListView.getPackedPositionGroup(0L), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    t((char) (TextUtils.getCapsMode("", 0, 0) + 34105), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 71, (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 34, objArr2);
                    o.ee.g.d(intern, ((String) objArr2[0]).intern());
                    this.c = new o.b.c(this.d);
                }
                new o.bx.b();
                o.b.c.e(o.ei.c.c(), this.d);
                if (customerAuthenticationCredentials == null) {
                    dVar = null;
                } else {
                    o.f.e b2 = o.bx.b.b(customerAuthenticationCredentials);
                    o.ad.a aVar2 = new o.ad.a();
                    o.h.d dVar2 = new o.h.d();
                    aVar2.e(this.d, dVar2, b2, null);
                    dVar = dVar2;
                }
                if (customerAuthenticationCredentials2 == null) {
                    aVar = null;
                } else {
                    aVar = new o.c.a(o.bx.b.b(customerAuthenticationCredentials2));
                }
                this.c.c(this.d, this, new b(), dVar, aVar);
                int i2 = f43o + 79;
                l = i2 % 128;
                int i3 = i2 % 2;
                return;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void c(o.ei.c cVar) {
        int i = l + 39;
        f43o = i % 128;
        int i2 = i % 2;
        cVar.e().e().e(this.d);
        int i3 = f43o + Opcodes.LREM;
        l = i3 % 128;
        switch (i3 % 2 != 0) {
            case false:
                return;
            default:
                int i4 = 39 / 0;
                return;
        }
    }

    private void e(final o.ei.c cVar) {
        int i = l + 75;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - Drawable.resolveOpacity(0, 0)), 1 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), 18 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) ((Process.myTid() >> 22) + 8581), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 104, TextUtils.getOffsetAfter("", 0) + 13, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (this.a == null) {
            this.a = new o.bz.a(this.d, this, cVar);
        }
        if (this.f == null) {
            this.f = new o.by.a(this.d, this, cVar);
        }
        switch (!b.get()) {
            case false:
                break;
            default:
                int i3 = f43o + 13;
                l = i3 % 128;
                boolean z = i3 % 2 != 0;
                p();
                switch (z) {
                    case false:
                        break;
                    default:
                        int i4 = 74 / 0;
                        break;
                }
        }
        WalletManagerCallback walletManagerCallback = this.e;
        if (walletManagerCallback != null) {
            walletManagerCallback.onConnectionSuccess(new Wallet(cVar), this.i);
        }
        new Thread(new Runnable() { // from class: o.bv.f$$ExternalSyntheticLambda1
            @Override // java.lang.Runnable
            public final void run() {
                f.this.c(cVar);
            }
        }).start();
    }

    public final void h() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36328 - TextUtils.indexOf((CharSequence) "", '0', 0)), TextUtils.indexOf("", ""), 18 - Drawable.resolveOpacity(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        t((char) Drawable.resolveOpacity(0, 0), 117 - Color.green(0), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 32, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(this);
        Object[] objArr3 = new Object[1];
        t((char) (22145 - Color.green(0)), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + Opcodes.LCMP, 10 - View.resolveSize(0, 0), objArr3);
        o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).append(this.e).toString());
        if (this.h) {
            this.h = false;
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            t((char) (36329 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1))), KeyEvent.getDeadChar(0, 0), 18 - (ViewConfiguration.getScrollBarSize() >> 8), objArr4);
            String intern2 = ((String) objArr4[0]).intern();
            Object[] objArr5 = new Object[1];
            t((char) View.getDefaultSize(0, 0), 159 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 42, objArr5);
            o.ee.g.d(intern2, ((String) objArr5[0]).intern());
            if (!b.get()) {
                int i = f43o + 93;
                l = i % 128;
                int i2 = i % 2;
                q();
            }
            switch (o.ei.c.c().q() ? 'P' : (char) 14) {
                case 'P':
                    int i3 = l + 109;
                    f43o = i3 % 128;
                    int i4 = i3 % 2;
                    o.fm.c d = o.ei.c.c().d();
                    switch (d.a() ? 'R' : 'B') {
                        case 'B':
                            break;
                        default:
                            d.b(this.d);
                            break;
                    }
            }
        }
        o.b.c cVar = this.c;
        switch (cVar != null ? '%' : (char) 6) {
            case '%':
                cVar.a(this.d);
                break;
        }
        this.c = null;
        o.bz.a aVar = this.a;
        switch (aVar == null) {
            case false:
                int i5 = l + 75;
                f43o = i5 % 128;
                int i6 = i5 % 2;
                aVar.b();
                break;
        }
        this.a = null;
        o.by.a aVar2 = this.f;
        if (aVar2 != null) {
            aVar2.b();
        }
        this.f = null;
        o.ee.g.c();
        Object[] objArr6 = new Object[1];
        t((char) (36329 - (ViewConfiguration.getKeyRepeatDelay() >> 16)), KeyEvent.normalizeMetaState(0), 18 - KeyEvent.keyCodeFromString(""), objArr6);
        String intern3 = ((String) objArr6[0]).intern();
        Object[] objArr7 = new Object[1];
        t((char) ((Process.getThreadPriority(0) + 20) >> 6), (ViewConfiguration.getLongPressTimeout() >> 16) + 201, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 16, objArr7);
        o.ee.g.d(intern3, ((String) objArr7[0]).intern());
    }

    public final void i() {
        int i = l + 21;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (ExpandableListView.getPackedPositionType(0L) + 36329), (-1) - ((byte) KeyEvent.getModifierMetaStateMask()), 18 - TextUtils.indexOf("", "", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) View.resolveSize(0, 0), 218 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)) + 5, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        h();
        this.d = null;
        this.e = null;
        this.i = null;
        int i3 = l + Opcodes.DDIV;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void e(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        int i = f43o + 29;
        l = i % 128;
        int i2 = i % 2;
        if (this.a == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            t((char) ExpandableListView.getPackedPositionGroup(0L), 223 - KeyEvent.normalizeMetaState(0), 12 - ImageFormat.getBitsPerPixel(0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t((char) (36021 - KeyEvent.getDeadChar(0, 0)), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 236, KeyEvent.getDeadChar(0, 0) + 31, objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
        if (customerAuthenticationCredentials == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            t((char) KeyEvent.getDeadChar(0, 0), 266 - TextUtils.lastIndexOf("", '0', 0), Process.getGidForName("") + 34, objArr3);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
        }
        o.h.d dVar = new o.h.d();
        o.ad.a aVar = new o.ad.a();
        new o.bx.b();
        aVar.e(this.d, dVar, o.bx.b.b(customerAuthenticationCredentials), null);
        this.a.d(dVar);
        int i3 = l + 83;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void c(CustomerAuthenticationCredentials customerAuthenticationCredentials, CustomerAuthenticationCredentials customerAuthenticationCredentials2) throws WalletValidationException {
        int i = l + 85;
        f43o = i % 128;
        switch (i % 2 != 0) {
            case true:
                if (this.a == null) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    t((char) Gravity.getAbsoluteGravity(0, 0), 223 - KeyEvent.keyCodeFromString(""), 13 - (Process.myPid() >> 22), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    t((char) (TextUtils.getOffsetAfter("", 0) + 36021), (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 237, 31 - TextUtils.getTrimmedLength(""), objArr2);
                    throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
                }
                if (customerAuthenticationCredentials == null) {
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr3 = new Object[1];
                    t((char) Color.green(0), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 266, 34 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr3);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
                }
                if (customerAuthenticationCredentials2 == null) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr4 = new Object[1];
                    t((char) ((-1) - TextUtils.indexOf((CharSequence) "", '0', 0)), 266 - ExpandableListView.getPackedPositionChild(0L), (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) + 32, objArr4);
                    throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr4[0]).intern());
                }
                new o.bx.b();
                o.f.e b2 = o.bx.b.b(customerAuthenticationCredentials2);
                o.f.e b3 = o.bx.b.b(customerAuthenticationCredentials);
                o.ad.a aVar = new o.ad.a();
                o.h.d dVar = new o.h.d();
                aVar.e(this.d, dVar, b3, null);
                this.a.e(dVar, new o.c.a(b2));
                int i2 = f43o + 59;
                l = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                throw null;
        }
    }

    public final void b(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        int i = f43o + Opcodes.DNEG;
        l = i % 128;
        o.h.d dVar = null;
        switch (i % 2 != 0 ? '\\' : '5') {
            case Opcodes.SALOAD /* 53 */:
                o.ei.c.c().C();
                if (this.f == null) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    t((char) TextUtils.indexOf("", "", 0), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 223, (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 12, objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    t((char) (ExpandableListView.getPackedPositionGroup(0L) + 36021), 237 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 30 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), objArr2);
                    throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
                }
                if (customerAuthenticationMethodType == null) {
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr3 = new Object[1];
                    t((char) ExpandableListView.getPackedPositionGroup(0L), (ViewConfiguration.getTouchSlop() >> 8) + 300, 28 - Color.green(0), objArr3);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
                }
                new o.bx.b();
                if (customerAuthenticationCredentials != null) {
                    o.ad.a aVar = new o.ad.a();
                    o.h.d dVar2 = new o.h.d();
                    aVar.e(this.d, dVar2, o.bx.b.b(customerAuthenticationCredentials), null);
                    dVar = dVar2;
                }
                this.f.b(o.i.f.b(customerAuthenticationMethodType), true, dVar);
                int i2 = f43o + 59;
                l = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                o.ei.c.c().C();
                throw null;
        }
    }

    public final void a(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        int i = l + 99;
        f43o = i % 128;
        int i2 = i % 2;
        o.ei.c.c().C();
        if (this.f == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            t((char) ((ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1), 223 - (ViewConfiguration.getDoubleTapTimeout() >> 16), View.MeasureSpec.makeMeasureSpec(0, 0) + 13, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 36021), (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 236, 31 - View.MeasureSpec.getMode(0), objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
        if (customerAuthenticationMethodType == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
            Object[] objArr3 = new Object[1];
            t((char) (ViewConfiguration.getPressedStateDuration() >> 16), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 300, (ViewConfiguration.getLongPressTimeout() >> 16) + 28, objArr3);
            throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
        }
        new o.bx.b();
        o.h.d dVar = null;
        if (customerAuthenticationCredentials != null) {
            o.f.e b2 = o.bx.b.b(customerAuthenticationCredentials);
            o.ad.a aVar = new o.ad.a();
            o.h.d dVar2 = new o.h.d();
            aVar.e(this.d, dVar2, b2, null);
            dVar = dVar2;
        }
        this.f.b(o.i.f.b(customerAuthenticationMethodType), false, dVar);
        int i3 = f43o + 61;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void f() throws WalletValidationException {
        int i = f43o + 71;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (ViewConfiguration.getJumpTapTimeout() >> 16)), ViewConfiguration.getTouchSlop() >> 8, (ViewConfiguration.getJumpTapTimeout() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (127 - Color.argb(0, 0, 0, 0)), TextUtils.indexOf("", "", 0) + 328, 14 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!this.h) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            t((char) TextUtils.indexOf("", "", 0, 0), 223 - (ViewConfiguration.getFadingEdgeLength() >> 16), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 12, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            t((char) (MotionEvent.axisFromString("") + 36022), 236 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 31 - (KeyEvent.getMaxKeyCode() >> 16), objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        this.c.e(this.d, this);
        int i3 = f43o + 47;
        l = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void e(CustomerAuthenticationMethodType customerAuthenticationMethodType, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        int i = f43o + 31;
        l = i % 128;
        Object obj = null;
        switch (i % 2 != 0) {
            case false:
                if (this.a == null) {
                    WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
                    Object[] objArr = new Object[1];
                    t((char) (ViewConfiguration.getTouchSlop() >> 8), 222 - ImageFormat.getBitsPerPixel(0), 13 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
                    String intern = ((String) objArr[0]).intern();
                    Object[] objArr2 = new Object[1];
                    t((char) (36021 - (ViewConfiguration.getEdgeSlop() >> 16)), 236 - (ViewConfiguration.getEdgeSlop() >> 16), ExpandableListView.getPackedPositionGroup(0L) + 31, objArr2);
                    throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
                }
                if (customerAuthenticationMethodType == null) {
                    WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr3 = new Object[1];
                    t((char) Color.blue(0), (ViewConfiguration.getTapTimeout() >> 16) + 300, 28 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr3);
                    throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr3[0]).intern());
                }
                if (customerAuthenticationCredentials == null) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
                    Object[] objArr4 = new Object[1];
                    t((char) TextUtils.indexOf("", "", 0, 0), View.resolveSizeAndState(0, 0, 0) + 300, (ViewConfiguration.getScrollBarSize() >> 8) + 28, objArr4);
                    throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr4[0]).intern());
                }
                o.i.f.b(customerAuthenticationMethodType);
                new o.bx.b();
                o.f.e b2 = o.bx.b.b(customerAuthenticationCredentials);
                o.ad.a aVar = new o.ad.a();
                o.h.d dVar = new o.h.d();
                aVar.e(this.d, dVar, b2, null);
                this.a.b(dVar);
                int i2 = l + 35;
                f43o = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                obj.hashCode();
                throw null;
        }
    }

    public static LiveData<List<HceTransaction>> c(Application application, int i, int i2) {
        MutableLiveData<List<HceTransaction>> c = new o.br.e(application, o.ei.c.c(), i, i2).c();
        int i3 = l + 65;
        f43o = i3 % 128;
        int i4 = i3 % 2;
        return c;
    }

    public final boolean c(CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        boolean z = true;
        if (!this.h) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            t((char) View.MeasureSpec.getMode(0), ExpandableListView.getPackedPositionGroup(0L) + 223, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 12, objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 36020), 237 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 31 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        t((char) (Drawable.resolveOpacity(0, 0) + 36329), (-1) - ExpandableListView.getPackedPositionChild(0L), KeyEvent.normalizeMetaState(0) + 18, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        t((char) (ImageFormat.getBitsPerPixel(0) + 1), (ViewConfiguration.getPressedStateDuration() >> 16) + 342, ((byte) KeyEvent.getModifierMetaStateMask()) + 62, objArr4);
        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
        new o.bx.b();
        o.f.e b2 = o.bx.b.b(customerAuthenticationCredentials);
        switch (AnonymousClass1.b[new o.dl.c().e(this.d, o.dl.c.b(), b2, this).ordinal()]) {
            case 1:
                int i = l + 71;
                f43o = i % 128;
                int i2 = i % 2;
                break;
            case 2:
            case 3:
                WalletManagerCallback walletManagerCallback = this.e;
                switch (walletManagerCallback != null) {
                    case false:
                        break;
                    default:
                        int i3 = f43o + 3;
                        l = i3 % 128;
                        int i4 = i3 % 2;
                        walletManagerCallback.onLocalAuthenticationError(b2.b().b(), LocalAuthenticationErrorReason.Forbidden, null, this.i);
                        int i5 = f43o + 77;
                        l = i5 % 128;
                        if (i5 % 2 == 0) {
                            break;
                        }
                        break;
                }
                z = false;
                break;
            case 4:
                WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Unexpected;
                Object[] objArr5 = new Object[1];
                t((char) (1 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), TextUtils.indexOf("", "", 0, 0) + 300, Color.rgb(0, 0, 0) + 16777244, objArr5);
                throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr5[0]).intern());
            case 5:
                WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Unknown;
                Object[] objArr6 = new Object[1];
                t((char) View.MeasureSpec.makeMeasureSpec(0, 0), 300 - Color.blue(0), View.getDefaultSize(0, 0) + 28, objArr6);
                throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr6[0]).intern());
            default:
                WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
                Object[] objArr7 = new Object[1];
                t((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), 299 - TextUtils.indexOf((CharSequence) "", '0', 0), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 27, objArr7);
                throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr7[0]).intern());
        }
        o.dj.e.a().d();
        int i6 = l + 47;
        f43o = i6 % 128;
        if (i6 % 2 != 0) {
            return z;
        }
        throw null;
    }

    public final void j() throws WalletValidationException {
        int i = f43o + Opcodes.DSUB;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - Gravity.getAbsoluteGravity(0, 0)), TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getLongPressTimeout() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (43918 - AndroidCharacter.getMirror('0')), 403 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), 26 - Drawable.resolveOpacity(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (this.c == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr3 = new Object[1];
            t((char) ExpandableListView.getPackedPositionGroup(0L), View.MeasureSpec.getSize(0) + 223, 14 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            t((char) (AndroidCharacter.getMirror('0') + 35973), KeyEvent.normalizeMetaState(0) + 236, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 30, objArr4);
            throw new WalletValidationException(walletValidationErrorCode, intern2, ((String) objArr4[0]).intern());
        }
        o.b.c.d();
        o.dj.e.a().c();
        o.dj.a.a();
        int i3 = l + 57;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    public final void a(WalletLockReason walletLockReason) throws WalletValidationException {
        int i = f43o + 83;
        l = i % 128;
        int i2 = i % 2;
        if (walletLockReason == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            t((char) ((ViewConfiguration.getKeyRepeatDelay() >> 16) + 13937), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 429, (ViewConfiguration.getTouchSlop() >> 8) + 16, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        if (this.c == null) {
            WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.WrongState;
            Object[] objArr2 = new Object[1];
            t((char) (Process.myPid() >> 22), 223 - (ViewConfiguration.getKeyRepeatDelay() >> 16), 14 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr2);
            String intern = ((String) objArr2[0]).intern();
            Object[] objArr3 = new Object[1];
            t((char) ((ViewConfiguration.getEdgeSlop() >> 16) + 36021), 236 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), 31 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), objArr3);
            throw new WalletValidationException(walletValidationErrorCode2, intern, ((String) objArr3[0]).intern());
        }
        switch (walletLockReason != WalletLockReason.FraudulentUseSuspected ? '!' : '9') {
            case '!':
                int i3 = l + 85;
                f43o = i3 % 128;
                int i4 = i3 % 2;
                if (walletLockReason != WalletLockReason.StopService) {
                    WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.InvalidFormat;
                    Object[] objArr4 = new Object[1];
                    t((char) (KeyEvent.getDeadChar(0, 0) + 13937), 429 - View.combineMeasuredStates(0, 0), 16 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr4);
                    throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr4[0]).intern());
                }
                break;
        }
        this.c.d(this.d, this, walletLockReason);
    }

    /* JADX INFO: Access modifiers changed from: private */
    /* JADX WARN: Code restructure failed: missing block: B:20:0x001b, code lost:
    
        r0 = r0 + 45;
        o.bv.f.l = r0 % 128;
     */
    /* JADX WARN: Code restructure failed: missing block: B:21:0x0023, code lost:
    
        if ((r0 % 2) == 0) goto L15;
     */
    /* JADX WARN: Code restructure failed: missing block: B:22:0x0025, code lost:
    
        r0 = 30;
     */
    /* JADX WARN: Code restructure failed: missing block: B:23:0x002a, code lost:
    
        switch(r0) {
            case 30: goto L18;
            default: goto L17;
        };
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x002d, code lost:
    
        r3.e.onAsyncRequestSuccess(fr.antelop.sdk.AsyncRequestType.Delete, r3.i);
     */
    /* JADX WARN: Code restructure failed: missing block: B:25:0x0037, code lost:
    
        r3.e.onAsyncRequestSuccess(fr.antelop.sdk.AsyncRequestType.Delete, r3.i);
     */
    /* JADX WARN: Code restructure failed: missing block: B:27:0x0042, code lost:
    
        r0 = 21 / 0;
     */
    /* JADX WARN: Code restructure failed: missing block: B:31:0x0028, code lost:
    
        r0 = '0';
     */
    /* JADX WARN: Code restructure failed: missing block: B:36:0x0019, code lost:
    
        if (r3.e != null) goto L12;
     */
    /* JADX WARN: Code restructure failed: missing block: B:6:0x0012, code lost:
    
        if (r3.e != null) goto L12;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public /* synthetic */ void s() {
        /*
            r3 = this;
            int r0 = o.bv.f.f43o
            int r1 = r0 + 77
            int r2 = r1 % 128
            o.bv.f.l = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L17
            fr.antelop.sdk.WalletManagerCallback r1 = r3.e
            r2 = 64
            int r2 = r2 / 0
            if (r1 == 0) goto L47
            goto L1b
        L15:
            r0 = move-exception
            throw r0
        L17:
            fr.antelop.sdk.WalletManagerCallback r1 = r3.e
            if (r1 == 0) goto L47
        L1b:
            int r0 = r0 + 45
            int r1 = r0 % 128
            o.bv.f.l = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L28
            r0 = 30
            goto L2a
        L28:
            r0 = 48
        L2a:
            switch(r0) {
                case 30: goto L37;
                default: goto L2d;
            }
        L2d:
            fr.antelop.sdk.WalletManagerCallback r0 = r3.e
            fr.antelop.sdk.AsyncRequestType r1 = fr.antelop.sdk.AsyncRequestType.Delete
            java.lang.Object r2 = r3.i
            r0.onAsyncRequestSuccess(r1, r2)
            goto L47
        L37:
            fr.antelop.sdk.WalletManagerCallback r0 = r3.e
            fr.antelop.sdk.AsyncRequestType r1 = fr.antelop.sdk.AsyncRequestType.Delete
            java.lang.Object r2 = r3.i
            r0.onAsyncRequestSuccess(r1, r2)
            r0 = 21
            int r0 = r0 / 0
            goto L47
        L45:
            r0 = move-exception
            throw r0
        L47:
            int r0 = o.bv.f.l
            int r0 = r0 + 71
            int r1 = r0 % 128
            o.bv.f.f43o = r1
            int r0 = r0 % 2
            if (r0 != 0) goto L56
            r0 = 19
            goto L58
        L56:
            r0 = 93
        L58:
            switch(r0) {
                case 19: goto L5c;
                default: goto L5b;
            }
        L5b:
            return
        L5c:
            r0 = 0
            throw r0     // Catch: java.lang.Throwable -> L5e
        L5e:
            r0 = move-exception
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.f.s():void");
    }

    public final void n() {
        int i = f43o + 25;
        l = i % 128;
        int i2 = i % 2;
        if (this.c == null) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            t((char) (Drawable.resolveOpacity(0, 0) + 36329), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1, 17 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            t((char) (17089 - TextUtils.indexOf("", "")), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 445, ExpandableListView.getPackedPositionType(0L) + 53, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            new o.bh.b(this.d, new b.d() { // from class: o.bv.f$$ExternalSyntheticLambda0
                @Override // o.bh.b.d
                public final void onDeleteWalletCompleted() {
                    f.this.s();
                }
            }).b(o.bh.d.b);
            int i3 = f43o + 73;
            l = i3 % 128;
            int i4 = i3 % 2;
            return;
        }
        o.ee.g.c();
        Object[] objArr3 = new Object[1];
        t((char) ((SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 36328), TextUtils.getOffsetAfter("", 0), View.resolveSizeAndState(0, 0, 0) + 18, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        t((char) (((Process.getThreadPriority(0) + 20) >> 6) + 21157), Drawable.resolveOpacity(0, 0) + 498, (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 56, objArr4);
        o.ee.g.d(intern2, ((String) objArr4[0]).intern());
        this.c.a(this.d, this);
    }

    @Override // o.bf.b.e
    public final void c(o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36328 - TextUtils.lastIndexOf("", '0')), View.combineMeasuredStates(0, 0), 18 - TextUtils.getTrimmedLength(""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 28304), TextUtils.indexOf("", "", 0, 0) + 554, TextUtils.lastIndexOf("", '0', 0, 0) + 15, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        t((char) ((ViewConfiguration.getScrollBarFadeDuration() >> 16) + 36329), ViewConfiguration.getMaximumFlingVelocity() >> 16, 17 - ImageFormat.getBitsPerPixel(0), objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr4 = new Object[1];
        t((char) View.getDefaultSize(0, 0), 568 - Color.alpha(0), 17 - (ViewConfiguration.getScrollBarFadeDuration() >> 16), objArr4);
        StringBuilder append = sb.append(((String) objArr4[0]).intern()).append(dVar.d());
        Object[] objArr5 = new Object[1];
        t((char) (24984 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), 586 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 1 - TextUtils.lastIndexOf("", '0', 0), objArr5);
        StringBuilder append2 = append.append(((String) objArr5[0]).intern()).append(dVar.e());
        Object[] objArr6 = new Object[1];
        t((char) (ViewConfiguration.getJumpTapTimeout() >> 16), 587 - (ViewConfiguration.getScrollDefaultDelay() >> 16), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 1, objArr6);
        o.ee.g.d(intern2, append2.append(((String) objArr6[0]).intern()).toString());
        switch (o.db.b.b(dVar) ? false : true) {
            case true:
                break;
            default:
                int i = f43o + Opcodes.DSUB;
                l = i % 128;
                int i2 = i % 2;
                h();
                break;
        }
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback == null ? 'T' : (char) 17) {
            case 17:
                int i3 = f43o + 25;
                l = i3 % 128;
                if (i3 % 2 == 0) {
                    walletManagerCallback.onAsyncRequestError(AsyncRequestType.Logout, c.c(dVar).d(), this.i);
                    return;
                }
                walletManagerCallback.onAsyncRequestError(AsyncRequestType.Logout, c.c(dVar).d(), this.i);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.bf.b.e
    public final void c() {
        int i = l + 25;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (Color.red(0) + 36329), (-1) - ImageFormat.getBitsPerPixel(0), (ViewConfiguration.getScrollBarSize() >> 8) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) View.MeasureSpec.getSize(0), 587 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 14 - View.resolveSizeAndState(0, 0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        h();
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null) {
            case false:
                break;
            default:
                walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.Logout, this.i);
                break;
        }
        int i3 = l + 73;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.b.a
    public final void c(o.ei.c cVar, o.bb.d dVar, g gVar) {
        int i = l + 33;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - Color.green(0)), Color.blue(0), 18 - View.resolveSize(0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 9184), 602 - View.resolveSizeAndState(0, 0, 0), 41 - KeyEvent.getDeadChar(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        this.h = true;
        e(cVar);
        int i3 = f43o + Opcodes.DMUL;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? (char) 14 : (char) 26) {
            case 26:
                return;
            default:
                int i4 = 75 / 0;
                return;
        }
    }

    @Override // o.b.a
    public final void d(o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) ((ViewConfiguration.getJumpTapTimeout() >> 16) + 36329), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) - 1, 18 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        t((char) (39416 - Color.blue(0)), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 643, (ViewConfiguration.getKeyRepeatTimeout() >> 16) + 52, objArr2);
        StringBuilder append = sb.append(((String) objArr2[0]).intern()).append(dVar.d());
        Object[] objArr3 = new Object[1];
        t((char) View.MeasureSpec.getMode(0), TextUtils.lastIndexOf("", '0', 0) + 588, TextUtils.indexOf("", "", 0, 0) + 1, objArr3);
        o.ee.g.d(intern, append.append(((String) objArr3[0]).intern()).toString());
        o.ee.g.c();
        Object[] objArr4 = new Object[1];
        t((char) ((-16740887) - Color.rgb(0, 0, 0)), View.combineMeasuredStates(0, 0), Color.blue(0) + 18, objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        StringBuilder sb2 = new StringBuilder();
        Object[] objArr5 = new Object[1];
        t((char) (30677 - TextUtils.lastIndexOf("", '0')), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 695, 16 - (ViewConfiguration.getScrollDefaultDelay() >> 16), objArr5);
        StringBuilder append2 = sb2.append(((String) objArr5[0]).intern()).append(dVar.d());
        Object[] objArr6 = new Object[1];
        t((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 24984), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 584, KeyEvent.keyCodeFromString("") + 2, objArr6);
        StringBuilder append3 = append2.append(((String) objArr6[0]).intern()).append(dVar.e());
        Object[] objArr7 = new Object[1];
        t((char) (Process.myTid() >> 22), TextUtils.getCapsMode("", 0, 0) + 587, View.combineMeasuredStates(0, 0) + 1, objArr7);
        o.ee.g.d(intern2, append3.append(((String) objArr7[0]).intern()).toString());
        h();
        if (dVar.d() == o.bb.a.Z) {
            WalletManagerCallback walletManagerCallback = this.e;
            switch (walletManagerCallback == null) {
                case false:
                    int i = f43o + 59;
                    l = i % 128;
                    int i2 = i % 2;
                    walletManagerCallback.onProvisioningRequired(this.i);
                    return;
            }
        }
        WalletManagerCallback walletManagerCallback2 = this.e;
        if (walletManagerCallback2 != null) {
            int i3 = f43o + 43;
            l = i3 % 128;
            boolean z = i3 % 2 == 0;
            walletManagerCallback2.onConnectionError(c.c(dVar).d(), this.i);
            switch (z) {
                case true:
                    break;
                default:
                    throw null;
            }
        }
        int i4 = l + 73;
        f43o = i4 % 128;
        int i5 = i4 % 2;
    }

    /* renamed from: o.bv.f$1, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\f$1.smali */
    static /* synthetic */ class AnonymousClass1 {
        private static int a;
        static final /* synthetic */ int[] b;
        private static int c;
        static final /* synthetic */ int[] d;

        /* JADX WARN: Failed to find 'out' block for switch in B:16:0x0053. Please report as an issue. */
        /* JADX WARN: Failed to find 'out' block for switch in B:7:0x0027. Please report as an issue. */
        static {
            a = 0;
            c = 1;
            int[] iArr = new int[o.g.b.values().length];
            d = iArr;
            try {
                iArr[o.g.b.e.ordinal()] = 1;
                int i = (a + 36) - 1;
                c = i % 128;
                switch (i % 2 != 0) {
                }
            } catch (NoSuchFieldError e) {
            }
            try {
                d[o.g.b.c.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.g.b.b.ordinal()] = 3;
                int i2 = a + 37;
                c = i2 % 128;
                switch (i2 % 2 == 0 ? '9' : 'X') {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[o.g.b.a.ordinal()] = 4;
            } catch (NoSuchFieldError e4) {
            }
            int[] iArr2 = new int[o.f.c.values().length];
            b = iArr2;
            try {
                iArr2[o.f.c.d.ordinal()] = 1;
                int i3 = c + 65;
                a = i3 % 128;
                if (i3 % 2 != 0) {
                }
            } catch (NoSuchFieldError e5) {
            }
            try {
                b[o.f.c.e.ordinal()] = 2;
                int i4 = c;
                int i5 = (i4 ^ 51) + ((i4 & 51) << 1);
                a = i5 % 128;
                if (i5 % 2 != 0) {
                }
            } catch (NoSuchFieldError e6) {
            }
            try {
                b[o.f.c.c.ordinal()] = 3;
            } catch (NoSuchFieldError e7) {
            }
            try {
                b[o.f.c.b.ordinal()] = 4;
            } catch (NoSuchFieldError e8) {
            }
            try {
                b[o.f.c.a.ordinal()] = 5;
                int i6 = a;
                int i7 = (i6 & 53) + (i6 | 53);
                c = i7 % 128;
                switch (i7 % 2 == 0) {
                    case false:
                        return;
                    default:
                        throw null;
                }
            } catch (NoSuchFieldError e9) {
            }
        }
    }

    @Override // o.b.a
    public final void d(o.cb.a aVar, o.g.b bVar, o.bb.d dVar) {
        c cVar;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (KeyEvent.getDeadChar(0, 0) + 36329), ViewConfiguration.getMaximumDrawingCacheSize() >> 24, View.combineMeasuredStates(0, 0) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), ExpandableListView.getPackedPositionType(0L) + 711, View.resolveSize(0, 0) + 60, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        h();
        AntelopError antelopError = null;
        switch (bVar != null ? '0' : (char) 24) {
            case '0':
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                t((char) (36329 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), ViewConfiguration.getFadingEdgeLength() >> 16, 18 - (ViewConfiguration.getScrollBarSize() >> 8), objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                t((char) (Process.myTid() >> 22), (ViewConfiguration.getEdgeSlop() >> 16) + 771, TextUtils.getCapsMode("", 0, 0) + 44, objArr4);
                o.ee.g.d(intern2, ((String) objArr4[0]).intern());
                switch (AnonymousClass1.d[bVar.ordinal()]) {
                    case 1:
                        AntelopErrorCode antelopErrorCode = AntelopErrorCode.CustomerCredentialsInvalid;
                        Object[] objArr5 = new Object[1];
                        t((char) (3271 - TextUtils.lastIndexOf("", '0')), 815 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 86 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr5);
                        cVar = new c(antelopErrorCode, -1, ((String) objArr5[0]).intern());
                        break;
                    case 2:
                        AntelopErrorCode antelopErrorCode2 = AntelopErrorCode.InvalidCustomerCredentialsFormat;
                        Object[] objArr6 = new Object[1];
                        t((char) (TextUtils.getCapsMode("", 0, 0) + 34532), (Process.myTid() >> 22) + 900, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 67, objArr6);
                        cVar = new c(antelopErrorCode2, -1, ((String) objArr6[0]).intern());
                        break;
                }
            default:
                cVar = null;
                break;
        }
        CustomerCredentialsRequiredReason d = aVar.d();
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? ')' : (char) 15) {
            case 15:
                return;
            default:
                switch (cVar == null) {
                    case false:
                        antelopError = cVar.d();
                        int i = f43o + Opcodes.DDIV;
                        l = i % 128;
                        if (i % 2 == 0) {
                            break;
                        } else {
                            break;
                        }
                    default:
                        int i2 = l + 9;
                        f43o = i2 % 128;
                        if (i2 % 2 == 0) {
                            int i3 = 39 / 0;
                            break;
                        }
                        break;
                }
                walletManagerCallback.onCredentialsRequired(d, antelopError, this.i);
                return;
        }
    }

    @Override // o.b.a
    public final void e() {
        int i = f43o + 29;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1))), (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), 18 - TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 12240), 966 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 45 - (ViewConfiguration.getDoubleTapTimeout() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        int i3 = l + 91;
        f43o = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                return;
            default:
                throw null;
        }
    }

    @Override // o.b.a
    public final void a() {
        int i = l + 81;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (Color.alpha(0) + 36329), ExpandableListView.getPackedPositionType(0L), View.resolveSize(0, 0) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (52881 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1))), 1013 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)), (ViewConfiguration.getScrollBarSize() >> 8) + 46, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        int i3 = l + 19;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.bg.b.c
    public final void b() {
        boolean z;
        int i = f43o + 67;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1))), (-1) - Process.getGidForName(""), 18 - View.MeasureSpec.getMode(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), TextUtils.indexOf("", "", 0) + 1058, 11 - ExpandableListView.getPackedPositionChild(0L), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        h();
        WalletManagerCallback walletManagerCallback = this.e;
        if (walletManagerCallback == null) {
            z = false;
        } else {
            z = true;
        }
        switch (z) {
            case false:
                break;
            default:
                walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.Lock, this.i);
                int i3 = f43o + 71;
                l = i3 % 128;
                if (i3 % 2 != 0) {
                    break;
                }
                break;
        }
    }

    @Override // o.bg.b.c
    public final void b(o.bb.d dVar) {
        int i = l + Opcodes.DREM;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - ((Process.getThreadPriority(0) + 20) >> 6)), (Process.getThreadPriority(0) + 20) >> 6, 18 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (15365 - Drawable.resolveOpacity(0, 0)), View.resolveSizeAndState(0, 0, 0) + 1070, (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 24, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (o.db.b.b(dVar)) {
            h();
        }
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? 'D' : '`') {
            case Opcodes.IADD /* 96 */:
                return;
            default:
                int i3 = l + 35;
                f43o = i3 % 128;
                switch (i3 % 2 == 0 ? (char) 17 : '^') {
                    case 17:
                        walletManagerCallback.onAsyncRequestError(AsyncRequestType.Lock, c.c(dVar).d(), this.i);
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        walletManagerCallback.onAsyncRequestError(AsyncRequestType.Lock, c.c(dVar).d(), this.i);
                        return;
                }
        }
    }

    @Override // o.bh.a.c
    public final void d() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - ((Process.getThreadPriority(0) + 20) >> 6)), ViewConfiguration.getPressedStateDuration() >> 16, 18 - (KeyEvent.getMaxKeyCode() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (ExpandableListView.getPackedPositionChild(0L) + 1), 1095 - (ViewConfiguration.getPressedStateDuration() >> 16), 24 - ExpandableListView.getPackedPositionChild(0L), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        h();
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback == null) {
            case false:
                int i = l + 91;
                f43o = i % 128;
                int i2 = i % 2;
                walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.Delete, this.i);
                break;
        }
        int i3 = f43o + 93;
        l = i3 % 128;
        switch (i3 % 2 != 0 ? 'Z' : Typography.amp) {
            case 'Z':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    @Override // o.bz.e
    public final void o() {
        int i = l + 75;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) ((ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 36329), ViewConfiguration.getMaximumDrawingCacheSize() >> 24, TextUtils.lastIndexOf("", '0') + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (29515 - Color.red(0)), (ViewConfiguration.getMinimumFlingVelocity() >> 16) + 1120, 25 - (ViewConfiguration.getEdgeSlop() >> 16), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? 'F' : '-') {
            case '-':
                return;
            default:
                int i3 = f43o + 57;
                l = i3 % 128;
                switch (i3 % 2 != 0) {
                    case false:
                        walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.CheckCredentials, this.i);
                        return;
                    default:
                        walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.CheckCredentials, this.i);
                        int i4 = 72 / 0;
                        return;
                }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:6:0x00af. Please report as an issue. */
    @Override // o.bz.e
    public final void k() {
        int i = f43o + Opcodes.LMUL;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (KeyEvent.getMaxKeyCode() >> 16)), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1, 18 - (ViewConfiguration.getEdgeSlop() >> 16), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) KeyEvent.getDeadChar(0, 0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 1145, 64 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        WalletManagerCallback walletManagerCallback = this.e;
        if (walletManagerCallback != null) {
            AsyncRequestType asyncRequestType = AsyncRequestType.CheckCredentials;
            AntelopErrorCode antelopErrorCode = AntelopErrorCode.CustomerCredentialsInvalid;
            Object[] objArr3 = new Object[1];
            t((char) (49918 - View.MeasureSpec.getSize(0)), 1207 - TextUtils.indexOf((CharSequence) "", '0'), 36 - (ViewConfiguration.getFadingEdgeLength() >> 16), objArr3);
            walletManagerCallback.onAsyncRequestError(asyncRequestType, new c(antelopErrorCode, ((String) objArr3[0]).intern()).d(), this.i);
            int i3 = l + 85;
            f43o = i3 % 128;
            switch (i3 % 2 == 0 ? (char) 11 : '!') {
            }
        }
    }

    @Override // o.bz.e
    public final void e(o.bb.d dVar) {
        int i = l + 109;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - TextUtils.getOffsetBefore("", 0)), Process.myPid() >> 22, (ViewConfiguration.getLongPressTimeout() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (21548 - View.MeasureSpec.getMode(0)), 1244 - (Process.myTid() >> 22), 24 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (o.db.b.b(dVar)) {
            int i3 = f43o + 77;
            l = i3 % 128;
            char c = i3 % 2 != 0 ? (char) 28 : '+';
            h();
            switch (c) {
                case '+':
                    break;
                default:
                    throw null;
            }
        }
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? '2' : (char) 16) {
            case 16:
                return;
            default:
                walletManagerCallback.onAsyncRequestError(AsyncRequestType.CheckCredentials, c.c(dVar).d(), this.i);
                return;
        }
    }

    @Override // o.bz.e
    public final void l() {
        int i = f43o + 51;
        l = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 36328), (-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), 18 - Color.alpha(0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (16250 - (ViewConfiguration.getDoubleTapTimeout() >> 16)), (ViewConfiguration.getFadingEdgeLength() >> 16) + 1267, 26 - (Process.myPid() >> 22), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback == null) {
            case false:
                int i3 = f43o + 83;
                l = i3 % 128;
                int i4 = i3 % 2;
                walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.ChangeCredentials, this.i);
                break;
        }
        int i5 = f43o + 69;
        l = i5 % 128;
        switch (i5 % 2 == 0) {
            case true:
                return;
            default:
                int i6 = 67 / 0;
                return;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:21:0x00a7. Please report as an issue. */
    @Override // o.bz.e
    public final void a(o.bb.d dVar) {
        int i = l + 35;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (ViewConfiguration.getFadingEdgeLength() >> 16)), ViewConfiguration.getScrollBarSize() >> 8, (ViewConfiguration.getKeyRepeatDelay() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (ExpandableListView.getPackedPositionChild(0L) + 364), ExpandableListView.getPackedPositionGroup(0L) + 1293, 25 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (o.db.b.b(dVar) ? '/' : '4') {
            case '/':
                int i3 = l + 75;
                f43o = i3 % 128;
                if (i3 % 2 != 0) {
                    h();
                    break;
                } else {
                    h();
                    int i4 = 56 / 0;
                    break;
                }
        }
        WalletManagerCallback walletManagerCallback = this.e;
        if (walletManagerCallback != null) {
            walletManagerCallback.onAsyncRequestError(AsyncRequestType.ChangeCredentials, c.c(dVar).d(), this.i);
            int i5 = l + Opcodes.LNEG;
            f43o = i5 % 128;
            switch (i5 % 2 == 0 ? '5' : 'K') {
            }
        }
    }

    @Override // o.bz.e
    public final void m() {
        int i = l + Opcodes.LMUL;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - View.combineMeasuredStates(0, 0)), Color.argb(0, 0, 0, 0), (ViewConfiguration.getScrollBarSize() >> 8) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) (ViewConfiguration.getJumpTapTimeout() >> 16), TextUtils.indexOf("", "") + 1317, ImageFormat.getBitsPerPixel(0) + 35, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? 'L' : 'F') {
            case Base64.mimeLineLength /* 76 */:
                walletManagerCallback.onAsyncRequestSuccess(AsyncRequestType.SynchronizeAuthenticationMethod, this.i);
                break;
        }
        int i3 = l + 61;
        f43o = i3 % 128;
        int i4 = i3 % 2;
    }

    @Override // o.bz.e
    public final void h(o.bb.d dVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), (ViewConfiguration.getKeyRepeatDelay() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        t((char) ExpandableListView.getPackedPositionType(0L), View.resolveSize(0, 0) + 1351, Color.blue(0) + 32, objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (o.db.b.b(dVar) ? '/' : (char) 0) {
            case 0:
                break;
            default:
                h();
                break;
        }
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? (char) 27 : '#') {
            case 27:
                int i = l + 81;
                f43o = i % 128;
                if (i % 2 == 0) {
                    walletManagerCallback.onAsyncRequestError(AsyncRequestType.SynchronizeAuthenticationMethod, c.c(dVar).d(), this.i);
                    throw null;
                }
                walletManagerCallback.onAsyncRequestError(AsyncRequestType.SynchronizeAuthenticationMethod, c.c(dVar).d(), this.i);
                int i2 = f43o + 19;
                l = i2 % 128;
                int i3 = i2 % 2;
                return;
            default:
                return;
        }
    }

    private void p() {
        boolean z;
        Object[] objArr = new Object[1];
        t((char) ((Process.myPid() >> 22) + 36329), Color.blue(0), (ViewConfiguration.getJumpTapTimeout() >> 16) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        if (o.ei.c.c().q() && !o.ei.c.c().e().e().b(o.ei.a.d)) {
            int i = l + 75;
            f43o = i % 128;
            int i2 = i % 2;
            o.ee.g.c();
            Object[] objArr2 = new Object[1];
            t((char) TextUtils.indexOf("", "", 0, 0), TextUtils.getTrimmedLength("") + 1383, TextUtils.indexOf("", "") + 73, objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            int i3 = f43o + 27;
            l = i3 % 128;
            int i4 = i3 % 2;
            return;
        }
        try {
            Context context = this.d;
            Object[] objArr3 = new Object[1];
            t((char) KeyEvent.getDeadChar(0, 0), (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 1455, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 38, objArr3);
            z = Boolean.parseBoolean(o.a(context, ((String) objArr3[0]).intern()));
        } catch (PackageManager.NameNotFoundException e) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            t((char) (34635 - (ViewConfiguration.getWindowTouchSlop() >> 8)), 1495 - Color.blue(0), Gravity.getAbsoluteGravity(0, 0) + 78, objArr4);
            o.ee.g.d(intern, ((String) objArr4[0]).intern());
            z = true;
        }
        try {
            Class<?> c = o.ei.a.d.c();
            switch (z) {
                case false:
                    break;
                default:
                    if (c != null) {
                        int i5 = l + Opcodes.DNEG;
                        f43o = i5 % 128;
                        if (i5 % 2 == 0) {
                            if (Build.VERSION.SDK_INT >= 43) {
                            }
                        }
                        Context context2 = this.d;
                        if (context2 instanceof Activity) {
                            Activity activity = (Activity) context2;
                            o.ee.e.a();
                            NfcAdapter c2 = o.ee.c.c(this.d);
                            switch (c2 == null) {
                                case false:
                                    CardEmulation cardEmulation = CardEmulation.getInstance(c2);
                                    if (cardEmulation != null) {
                                        ComponentName componentName = new ComponentName(this.d, c);
                                        Object[] objArr5 = new Object[1];
                                        t((char) (((Process.getThreadPriority(0) + 20) >> 6) + 43058), (ViewConfiguration.getScrollBarSize() >> 8) + 1713, View.resolveSizeAndState(0, 0, 0) + 7, objArr5);
                                        switch (!cardEmulation.categoryAllowsForegroundPreference(((String) objArr5[0]).intern())) {
                                            case true:
                                                o.ee.g.c();
                                                Object[] objArr6 = new Object[1];
                                                t((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 50980), 1829 - ExpandableListView.getPackedPositionType(0L), 65 - Drawable.resolveOpacity(0, 0), objArr6);
                                                o.ee.g.d(intern, ((String) objArr6[0]).intern());
                                                break;
                                            default:
                                                if (!cardEmulation.setPreferredService(activity, componentName)) {
                                                    o.ee.g.c();
                                                    Object[] objArr7 = new Object[1];
                                                    t((char) (2920 - ExpandableListView.getPackedPositionGroup(0L)), 1769 - View.resolveSizeAndState(0, 0, 0), 59 - TextUtils.indexOf((CharSequence) "", '0'), objArr7);
                                                    o.ee.g.d(intern, ((String) objArr7[0]).intern());
                                                    break;
                                                } else {
                                                    o.ee.g.c();
                                                    Object[] objArr8 = new Object[1];
                                                    t((char) (ViewConfiguration.getScrollBarFadeDuration() >> 16), TextUtils.getOffsetAfter("", 0) + 1720, 49 - (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr8);
                                                    o.ee.g.d(intern, ((String) objArr8[0]).intern());
                                                    break;
                                                }
                                        }
                                    } else {
                                        o.ee.g.c();
                                        Object[] objArr9 = new Object[1];
                                        t((char) KeyEvent.normalizeMetaState(0), TextUtils.getOffsetBefore("", 0) + 1647, (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 66, objArr9);
                                        o.ee.g.e(intern, ((String) objArr9[0]).intern());
                                        break;
                                    }
                                default:
                                    o.ee.g.c();
                                    Object[] objArr10 = new Object[1];
                                    t((char) (Gravity.getAbsoluteGravity(0, 0) + 39992), 1574 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), 74 - View.MeasureSpec.getMode(0), objArr10);
                                    o.ee.g.d(intern, ((String) objArr10[0]).intern());
                                    break;
                            }
                            return;
                        }
                    }
                    break;
            }
            o.ee.g.c();
            Object[] objArr11 = new Object[1];
            t((char) (TextUtils.indexOf((CharSequence) "", '0') + 27761), 1894 - (ViewConfiguration.getTapTimeout() >> 16), 54 - ExpandableListView.getPackedPositionGroup(0L), objArr11);
            o.ee.g.d(intern, ((String) objArr11[0]).intern());
        } catch (IllegalArgumentException | IllegalStateException e2) {
            o.ee.g.c();
            Object[] objArr12 = new Object[1];
            t((char) (1 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1))), 1947 - TextUtils.lastIndexOf("", '0', 0, 0), 40 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr12);
            o.ee.g.a(intern, ((String) objArr12[0]).intern(), e2);
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:16:0x00c6. Please report as an issue. */
    private void q() {
        boolean z;
        int i = l + 17;
        f43o = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        t((char) (36329 - (ViewConfiguration.getMaximumFlingVelocity() >> 16)), Process.myTid() >> 22, (ViewConfiguration.getWindowTouchSlop() >> 8) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        switch (o.ei.c.c().q() ? 'T' : (char) 20) {
            case Opcodes.BASTORE /* 84 */:
                if (!o.ei.c.c().e().e().b(o.ei.a.d)) {
                    o.ee.g.c();
                    Object[] objArr2 = new Object[1];
                    t((char) (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 1988 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), 75 - Color.argb(0, 0, 0, 0), objArr2);
                    o.ee.g.d(intern, ((String) objArr2[0]).intern());
                    return;
                }
                break;
        }
        try {
            Context context = this.d;
            Object[] objArr3 = new Object[1];
            t((char) (ExpandableListView.getPackedPositionChild(0L) + 1), 1457 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 39, objArr3);
            z = Boolean.parseBoolean(o.a(context, ((String) objArr3[0]).intern()));
            int i3 = l + Opcodes.DREM;
            f43o = i3 % 128;
            switch (i3 % 2 != 0 ? (char) 6 : (char) 20) {
            }
        } catch (PackageManager.NameNotFoundException e) {
            o.ee.g.c();
            Object[] objArr4 = new Object[1];
            t((char) (((byte) KeyEvent.getModifierMetaStateMask()) + 33716), 2063 - Color.argb(0, 0, 0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 81, objArr4);
            o.ee.g.d(intern, ((String) objArr4[0]).intern());
            z = true;
        }
        try {
            if (z) {
                int i4 = f43o + Opcodes.LSHR;
                l = i4 % 128;
                int i5 = i4 % 2;
                switch (24) {
                    case 60:
                        break;
                    default:
                        int i6 = l + 69;
                        f43o = i6 % 128;
                        if (i6 % 2 == 0) {
                            boolean z2 = this.d instanceof Activity;
                            Object obj = null;
                            obj.hashCode();
                            throw null;
                        }
                        Context context2 = this.d;
                        if (context2 instanceof Activity) {
                            Activity activity = (Activity) context2;
                            o.ee.e.a();
                            NfcAdapter c = o.ee.c.c(this.d);
                            if (c == null) {
                                o.ee.g.c();
                                Object[] objArr5 = new Object[1];
                                t((char) (48635 - Color.alpha(0)), 2143 - ExpandableListView.getPackedPositionGroup(0L), 78 - (ViewConfiguration.getTapTimeout() >> 16), objArr5);
                                o.ee.g.d(intern, ((String) objArr5[0]).intern());
                                return;
                            }
                            CardEmulation cardEmulation = CardEmulation.getInstance(c);
                            if (cardEmulation == null) {
                                o.ee.g.c();
                                Object[] objArr6 = new Object[1];
                                t((char) View.MeasureSpec.getMode(0), 2220 - TextUtils.indexOf((CharSequence) "", '0'), 68 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr6);
                                o.ee.g.e(intern, ((String) objArr6[0]).intern());
                                return;
                            }
                            switch (!cardEmulation.unsetPreferredService(activity)) {
                                case true:
                                    o.ee.g.c();
                                    Object[] objArr7 = new Object[1];
                                    t((char) ExpandableListView.getPackedPositionGroup(0L), TextUtils.getOffsetBefore("", 0) + 2342, (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 63, objArr7);
                                    o.ee.g.d(intern, ((String) objArr7[0]).intern());
                                    return;
                                default:
                                    int i7 = l + Opcodes.LNEG;
                                    f43o = i7 % 128;
                                    if (i7 % 2 == 0) {
                                    }
                                    o.ee.g.c();
                                    Object[] objArr8 = new Object[1];
                                    t((char) (6613 - (ViewConfiguration.getWindowTouchSlop() >> 8)), 2289 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), 54 - (Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)), objArr8);
                                    o.ee.g.d(intern, ((String) objArr8[0]).intern());
                                    return;
                            }
                        }
                        break;
                }
            }
            o.ee.g.c();
            Object[] objArr9 = new Object[1];
            t((char) (59040 - Drawable.resolveOpacity(0, 0)), (Process.myPid() >> 22) + 2406, 57 - View.MeasureSpec.getMode(0), objArr9);
            o.ee.g.d(intern, ((String) objArr9[0]).intern());
        } catch (IllegalArgumentException | IllegalStateException e2) {
            o.ee.g.c();
            Object[] objArr10 = new Object[1];
            t((char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), 2463 - (ViewConfiguration.getTouchSlop() >> 8), TextUtils.lastIndexOf("", '0', 0) + 48, objArr10);
            o.ee.g.d(intern, String.format(((String) objArr10[0]).intern(), e2.getMessage()));
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:11:0x007d  */
    /* JADX WARN: Removed duplicated region for block: B:15:0x0080  */
    @Override // o.by.b
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final void c(boolean r9) {
        /*
            r8 = this;
            o.ee.g.c()
            java.lang.String r0 = ""
            r1 = 48
            r2 = 0
            int r0 = android.text.TextUtils.lastIndexOf(r0, r1, r2, r2)
            r1 = 36328(0x8de8, float:5.0906E-41)
            int r1 = r1 - r0
            char r0 = (char) r1
            int r1 = android.view.ViewConfiguration.getDoubleTapTimeout()
            int r1 = r1 >> 16
            long r3 = android.os.Process.getElapsedCpuTime()
            r5 = 0
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            int r3 = r3 + 17
            r4 = 1
            java.lang.Object[] r7 = new java.lang.Object[r4]
            t(r0, r1, r3, r7)
            r0 = r7[r2]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            int r1 = android.view.ViewConfiguration.getScrollBarFadeDuration()
            int r1 = r1 >> 16
            r3 = 50267(0xc45b, float:7.0439E-41)
            int r3 = r3 - r1
            char r1 = (char) r3
            int r3 = android.graphics.Color.red(r2)
            int r3 = r3 + 2510
            int r5 = android.widget.ExpandableListView.getPackedPositionGroup(r5)
            int r5 = r5 + 23
            java.lang.Object[] r6 = new java.lang.Object[r4]
            t(r1, r3, r5, r6)
            r1 = r6[r2]
            java.lang.String r1 = (java.lang.String) r1
            java.lang.String r1 = r1.intern()
            o.ee.g.d(r0, r1)
            fr.antelop.sdk.WalletManagerCallback r0 = r8.e
            if (r0 == 0) goto L92
            int r1 = o.bv.f.l
            int r1 = r1 + 13
            int r3 = r1 % 128
            o.bv.f.f43o = r3
            int r1 = r1 % 2
            if (r1 != 0) goto L76
            r1 = 51
            int r1 = r1 / r2
            if (r9 == 0) goto L6f
            r9 = 36
            goto L70
        L6f:
            r9 = 6
        L70:
            switch(r9) {
                case 6: goto L7c;
                default: goto L73;
            }
        L73:
            goto L7d
        L74:
            r9 = move-exception
            throw r9
        L76:
            if (r9 == 0) goto L79
            r2 = r4
        L79:
            switch(r2) {
                case 1: goto L7d;
                default: goto L7c;
            }
        L7c:
            goto L80
        L7d:
            fr.antelop.sdk.AsyncRequestType r9 = fr.antelop.sdk.AsyncRequestType.ActivateAuthenticationMethod
            goto L82
        L80:
            fr.antelop.sdk.AsyncRequestType r9 = fr.antelop.sdk.AsyncRequestType.DeactivateAuthenticationMethod
        L82:
            java.lang.Object r1 = r8.i
            r0.onAsyncRequestSuccess(r9, r1)
            int r9 = o.bv.f.f43o
            int r9 = r9 + 107
            int r0 = r9 % 128
            o.bv.f.l = r0
            int r9 = r9 % 2
        L92:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.f.c(boolean):void");
    }

    @Override // o.by.b
    public final void d(o.bb.d dVar, boolean z) {
        AsyncRequestType asyncRequestType;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - View.MeasureSpec.getSize(0)), View.getDefaultSize(0, 0), 18 - ExpandableListView.getPackedPositionGroup(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        t((char) (KeyEvent.keyCodeFromString("") + 44545), View.MeasureSpec.getMode(0) + 2533, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 31, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(dVar.d()).toString());
        switch (o.db.b.b(dVar)) {
            case true:
                int i = l + 93;
                f43o = i % 128;
                int i2 = i % 2;
                h();
                break;
        }
        WalletManagerCallback walletManagerCallback = this.e;
        if (walletManagerCallback != null) {
            int i3 = l;
            int i4 = i3 + 21;
            f43o = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    throw null;
                default:
                    switch (z) {
                        case false:
                            asyncRequestType = AsyncRequestType.DeactivateAuthenticationMethod;
                            break;
                        default:
                            int i5 = i3 + 19;
                            f43o = i5 % 128;
                            int i6 = i5 % 2;
                            asyncRequestType = AsyncRequestType.ActivateAuthenticationMethod;
                            break;
                    }
                    walletManagerCallback.onAsyncRequestError(asyncRequestType, c.c(dVar).d(), this.i);
                    return;
            }
        }
    }

    @Override // o.bw.b
    public final void d(o.i.f fVar) {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (View.MeasureSpec.makeMeasureSpec(0, 0) + 36329), (-1) - ((byte) KeyEvent.getModifierMetaStateMask()), (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 18, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        t((char) ((ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 57140), 2564 - (KeyEvent.getMaxKeyCode() >> 16), 31 - TextUtils.getOffsetBefore("", 0), objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(fVar).toString());
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback == null ? (char) 14 : (char) 1) {
            case 14:
                break;
            default:
                int i = l + Opcodes.LSHL;
                f43o = i % 128;
                int i2 = i % 2;
                walletManagerCallback.onLocalAuthenticationSuccess(fVar.b(), this.i);
                int i3 = l + 75;
                f43o = i3 % 128;
                if (i3 % 2 != 0) {
                    break;
                } else {
                    break;
                }
        }
    }

    @Override // o.bw.b
    public final void c(o.i.f fVar, o.g.b bVar) {
        int i = l + 81;
        f43o = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        t((char) (36329 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), ViewConfiguration.getScrollBarSize() >> 8, Process.getGidForName("") + 19, objArr);
        String intern = ((String) objArr[0]).intern();
        Locale a = j.a();
        Object[] objArr2 = new Object[1];
        t((char) ((-1) - Process.getGidForName("")), 2595 - (ViewConfiguration.getWindowTouchSlop() >> 8), TextUtils.getTrimmedLength("") + 58, objArr2);
        Object obj = null;
        o.ee.g.d(intern, String.format(a, ((String) objArr2[0]).intern(), fVar, bVar, null));
        WalletManagerCallback walletManagerCallback = this.e;
        switch (walletManagerCallback != null ? 'B' : (char) 19) {
            case 'B':
                walletManagerCallback.onLocalAuthenticationError(fVar.b(), bVar.b(), null, this.i);
                break;
        }
        int i3 = f43o + 53;
        l = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                obj.hashCode();
                throw null;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void t(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 954
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.f.t(char, int, int, java.lang.Object[]):void");
    }
}
