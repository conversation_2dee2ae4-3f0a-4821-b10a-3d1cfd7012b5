package androidx.viewpager2;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes15\androidx\viewpager2\R$attr.smali */
public final class R$attr {
    public static int alpha = **********;
    public static int fastScrollEnabled = **********;
    public static int fastScrollHorizontalThumbDrawable = **********;
    public static int fastScrollHorizontalTrackDrawable = **********;
    public static int fastScrollVerticalThumbDrawable = **********;
    public static int fastScrollVerticalTrackDrawable = **********;
    public static int font = **********;
    public static int fontProviderAuthority = **********;
    public static int fontProviderCerts = **********;
    public static int fontProviderFetchStrategy = **********;
    public static int fontProviderFetchTimeout = **********;
    public static int fontProviderPackage = **********;
    public static int fontProviderQuery = **********;
    public static int fontStyle = **********;
    public static int fontVariationSettings = **********;
    public static int fontWeight = **********;
    public static int layoutManager = **********;
    public static int recyclerViewStyle = **********;
    public static int reverseLayout = **********;
    public static int spanCount = **********;
    public static int stackFromEnd = **********;
    public static int ttcIndex = **********;

    private R$attr() {
    }
}
