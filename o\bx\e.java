package o.bx;

import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bx\e.smali */
final class e extends CustomerAuthenticationCredentials {
    private static int d = 0;
    private static int e = 1;
    private final o.f.e b;

    e(o.f.e eVar) {
        this.b = eVar;
    }

    final o.f.e d() {
        int i = d;
        int i2 = ((i | 13) << 1) - (i ^ 13);
        int i3 = i2 % 128;
        e = i3;
        boolean z = i2 % 2 != 0;
        o.f.e eVar = this.b;
        switch (z) {
            case false:
                int i4 = 71 / 0;
                break;
        }
        int i5 = i3 + Opcodes.LREM;
        d = i5 % 128;
        switch (i5 % 2 != 0 ? 'M' : 'b') {
            case Opcodes.FADD /* 98 */:
                return eVar;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }
}
