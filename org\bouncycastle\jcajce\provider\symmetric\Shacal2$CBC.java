package org.bouncycastle.jcajce.provider.symmetric;

import org.bouncycastle.crypto.engines.Shacal2Engine;
import org.bouncycastle.crypto.modes.CBCBlockCipher;
import org.bouncycastle.jcajce.provider.symmetric.util.BaseBlockCipher;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\jcajce\provider\symmetric\Shacal2$CBC.smali */
public class Shacal2$CBC extends BaseBlockCipher {
    public Shacal2$CBC() {
        super(new CBCBlockCipher(new Shacal2Engine()), 256);
    }
}
