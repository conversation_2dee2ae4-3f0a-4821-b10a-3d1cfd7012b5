package com.vasco.digipass.sdk.utils.securestorage.obfuscated;

import com.vasco.digipass.sdk.utils.securestorage.SecureStorageSDKException;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.BiometricWriteProtectionSettings;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.SecureStorageBiometricWriteCallback;
import kotlin.jvm.internal.Intrinsics;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\obfuscated\p.smali */
public final class p implements d {
    public final /* synthetic */ com.vasco.digipass.sdk.utils.securestorage.a a;
    public final /* synthetic */ BiometricWriteProtectionSettings b;
    public final /* synthetic */ byte[] c;
    public final /* synthetic */ SecureStorageBiometricWriteCallback d;

    public p(com.vasco.digipass.sdk.utils.securestorage.a aVar, BiometricWriteProtectionSettings biometricWriteProtectionSettings, byte[] bArr, SecureStorageBiometricWriteCallback secureStorageBiometricWriteCallback) {
        this.a = aVar;
        this.b = biometricWriteProtectionSettings;
        this.c = bArr;
        this.d = secureStorageBiometricWriteCallback;
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.d
    public final void a(SecureStorageSDKException secureStorageSDKException) {
        Intrinsics.checkNotNullParameter(secureStorageSDKException, "secureStorageSDKException");
        y.b(this.c);
        this.d.onWriteFailed(secureStorageSDKException);
    }

    @Override // com.vasco.digipass.sdk.utils.securestorage.obfuscated.d
    public final void a(byte[] signedStorageEncryptionKey) {
        Intrinsics.checkNotNullParameter(signedStorageEncryptionKey, "signedStorageEncryptionKey");
        this.a.cipherAndWriteData$lib_release(signedStorageEncryptionKey, this.b.getFragmentActivity(), true, this.b.getFallbackToDeviceCredential());
        y.b(this.c);
        y.b(signedStorageEncryptionKey);
        this.d.onWriteSuccess();
    }
}
