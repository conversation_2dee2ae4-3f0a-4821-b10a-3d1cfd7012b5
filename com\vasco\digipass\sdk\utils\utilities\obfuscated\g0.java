package com.vasco.digipass.sdk.utils.utilities.obfuscated;

import java.io.IOException;
import java.io.InputStream;
import org.bouncycastle.asn1.BERTags;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\g0.smali */
public class g0 {
    private final InputStream a;
    private final int b;
    private final byte[][] c;

    g0(InputStream inputStream, int i, byte[][] bArr) {
        this.a = inputStream;
        this.b = i;
        this.c = bArr;
    }

    public h a() throws IOException {
        int read = this.a.read();
        if (read < 0) {
            return null;
        }
        return a(read);
    }

    h b(int i) throws IOException {
        if (i == 3) {
            return new v0(this);
        }
        if (i == 4) {
            return new y0(this);
        }
        if (i == 8) {
            return new y1(this);
        }
        if (i == 16) {
            return new e3(this);
        }
        if (i == 17) {
            return new g3(this);
        }
        throw new k("unknown DL object encountered: 0x" + Integer.toHexString(i));
    }

    h c(int i) throws IOException {
        if (i == 3) {
            return new v0(this);
        }
        if (i == 4) {
            return new y0(this);
        }
        if (i == 8) {
            return new y1(this);
        }
        if (i == 16) {
            return new a1(this);
        }
        if (i == 17) {
            return new c1(this);
        }
        throw new k("unknown BER object encountered: 0x" + Integer.toHexString(i));
    }

    h a(int i) throws IOException {
        a(false);
        int a = q.a(this.a, i);
        int a2 = q.a(this.a, this.b, a == 3 || a == 4 || a == 16 || a == 17 || a == 8);
        if (a2 < 0) {
            if ((i & 32) != 0) {
                g0 g0Var = new g0(new c5(this.a, this.b), this.b, this.c);
                int i2 = i & 192;
                if (i2 != 0) {
                    return new e1(i2, a, g0Var);
                }
                return g0Var.c(a);
            }
            throw new IOException("indefinite-length primitive encoding encountered");
        }
        x3 x3Var = new x3(this.a, a2, this.b);
        if ((i & BERTags.FLAGS) == 0) {
            return a(a, x3Var);
        }
        g0 g0Var2 = new g0(x3Var, x3Var.a(), this.c);
        int i3 = i & 192;
        if (i3 != 0) {
            return new i3(i3, a, (i & 32) != 0, g0Var2);
        }
        return g0Var2.b(a);
    }

    i b() throws IOException {
        int read = this.a.read();
        if (read < 0) {
            return new i(0);
        }
        i iVar = new i();
        do {
            h a = a(read);
            if (a instanceof b5) {
                iVar.a(((b5) a).a());
            } else {
                iVar.a(a.toASN1Primitive());
            }
            read = this.a.read();
        } while (read >= 0);
        return iVar;
    }

    b0 a(int i, int i2, boolean z) throws IOException {
        if (!z) {
            return j0.a(i, i2, ((x3) this.a).c());
        }
        return j0.a(i, i2, b());
    }

    b0 a(int i, int i2) throws IOException {
        return j0.b(i, i2, b());
    }

    h a(int i, x3 x3Var) throws IOException {
        if (i == 3) {
            return new z2(x3Var);
        }
        if (i == 4) {
            return new g2(x3Var);
        }
        if (i == 8) {
            throw new k("externals must use constructed encoding (see X.690 8.18)");
        }
        if (i == 16) {
            throw new k("sets must use constructed encoding (see X.690 8.11.1/8.12.1)");
        }
        if (i != 17) {
            try {
                return q.a(i, x3Var, this.c);
            } catch (IllegalArgumentException e) {
                throw new k("corrupted stream detected", e);
            }
        }
        throw new k("sequences must use constructed encoding (see X.690 8.9.1/8.10.1)");
    }

    private void a(boolean z) {
        InputStream inputStream = this.a;
        if (inputStream instanceof c5) {
            ((c5) inputStream).b(z);
        }
    }
}
