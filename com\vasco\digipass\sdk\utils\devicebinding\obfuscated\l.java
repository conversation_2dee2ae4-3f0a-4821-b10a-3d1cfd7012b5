package com.vasco.digipass.sdk.utils.devicebinding.obfuscated;

import android.content.Context;
import android.os.Build;
import android.security.keystore.KeyGenParameterSpec;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;

@Metadata(bv = {}, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006¢\u0006\u0004\b\b\u0010\tJ\b\u0010\u0003\u001a\u00020\u0002H\u0016¨\u0006\n"}, d2 = {"Lcom/vasco/digipass/sdk/utils/devicebinding/obfuscated/l;", "", "Landroid/security/keystore/KeyGenParameterSpec$Builder;", "a", "Landroid/content/Context;", "context", "", "keyAlias", "<init>", "(Landroid/content/Context;Ljava/lang/String;)V", "lib_release"}, k = 1, mv = {1, 7, 1})
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\devicebinding\obfuscated\l.smali */
public final class l {
    private final Context a;
    private final String b;

    public l(Context context, String keyAlias) {
        Intrinsics.checkNotNullParameter(context, "context");
        Intrinsics.checkNotNullParameter(keyAlias, "keyAlias");
        this.a = context;
        this.b = keyAlias;
    }

    public KeyGenParameterSpec.Builder a() {
        KeyGenParameterSpec.Builder builder = new KeyGenParameterSpec.Builder(this.b, 4);
        if (Build.VERSION.SDK_INT >= 28 && this.a.getPackageManager().hasSystemFeature("android.hardware.strongbox_keystore")) {
            builder.setIsStrongBoxBacked(true);
        }
        return builder;
    }
}
