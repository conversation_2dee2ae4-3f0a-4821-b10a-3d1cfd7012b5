package o.ee;

import com.esotericsoftware.asm.Opcodes;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Locale;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ee\j.smali */
public final class j {
    private static int c;
    private static int b = 0;
    private static Charset a = StandardCharsets.UTF_8;
    private static Locale d = Locale.getDefault();

    static {
        c = 1;
        int i = (b + Opcodes.IAND) - 1;
        c = i % 128;
        int i2 = i % 2;
    }

    public static Charset c() {
        int i = b;
        int i2 = (i ^ 65) + ((i & 65) << 1);
        c = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                return a;
            default:
                int i3 = 35 / 0;
                return a;
        }
    }

    public static Locale a() {
        int i = (c + 80) - 1;
        b = i % 128;
        switch (i % 2 == 0) {
            case true:
                return d;
            default:
                throw null;
        }
    }
}
