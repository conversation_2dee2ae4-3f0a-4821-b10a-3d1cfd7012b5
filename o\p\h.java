package o.p;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import fr.antelop.sdk.AntelopErrorCode;
import fr.antelop.sdk.authentication.CustomerAuthenticationCredentials;
import fr.antelop.sdk.exception.WalletValidationErrorCode;
import fr.antelop.sdk.exception.WalletValidationException;
import java.lang.reflect.Method;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.TreeMap;
import kotlin.text.Typography;
import o.a.o;
import o.h.a;
import o.i.f;
import o.p.c;
import o.p.j;
import org.bouncycastle.i18n.LocalizedMessage;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\h.smali */
public abstract class h<CredentialsLoader extends o.h.a> {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static char[] q;
    private static int r;
    private static long t;
    private static int x;
    public final boolean d;
    o.ei.c e;
    g f;
    private CredentialsLoader h;
    private final o.i.i i;
    private o.s.c k;
    private final String l;
    private Integer m;
    private o.b.c p;
    public boolean b = false;
    final List<f> c = new ArrayList();
    boolean j = false;
    private final o.t.c n = new o.t.c();

    /* renamed from: o, reason: collision with root package name */
    private final TreeMap<Integer, f> f96o = new TreeMap<>();
    public final Object g = new Object();
    private boolean s = true;
    public final o.h.d a = new o.h.d();

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        r = 0;
        x = 1;
        q();
        KeyEvent.getModifierMetaStateMask();
        MotionEvent.axisFromString("");
        View.combineMeasuredStates(0, 0);
        int i = r + 35;
        x = i % 128;
        switch (i % 2 == 0 ? 'G' : ']') {
            case Opcodes.DUP2_X1 /* 93 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002c). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void D(short r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r6 = r6 * 4
            int r6 = r6 + 1
            int r7 = r7 + 102
            byte[] r0 = o.p.h.$$a
            int r8 = r8 * 4
            int r8 = r8 + 4
            byte[] r1 = new byte[r6]
            int r6 = r6 + (-1)
            r2 = 0
            if (r0 != 0) goto L17
            r3 = r7
            r4 = r2
            r7 = r6
            goto L2c
        L17:
            r3 = r2
        L18:
            byte r4 = (byte) r7
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r6) goto L27
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L27:
            r3 = r0[r8]
            r5 = r7
            r7 = r6
            r6 = r5
        L2c:
            int r6 = r6 + r3
            int r8 = r8 + 1
            r3 = r4
            r5 = r7
            r7 = r6
            r6 = r5
            goto L18
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.h.D(short, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{114, 12, -103, 122};
        $$b = 47;
    }

    static void q() {
        char[] cArr = new char[1730];
        ByteBuffer.wrap(",\u008aº\u009d\u0000Ñî\ft@Ã\u0096©ß,\u0080º\u009c\u0000Ñî\u001dtWÃ\u00ad©Þ7'\u009dedµòêX%&\u007f\u008cw\u001b\u0086áÈO\u0011ÕG¼\u0081\nÜ\u0090<~}ÅºSð9$\u0087nmgô\u0082BÒ(\u0005¶F\u001d\u009fëÚ,ºº\u0097\u0000Ëî;tPÃ\u009d©ß7;\u009d|d¿òõX\u0003&\u007f\u008cS\u001b\u0097áÙO\u0017ÕV¼\u0086\nÉ\u00909~mÅóS\u00ad'\t±$\u000bxå\u0088\u007fãÈ.¢l<\u0088\u0096Ïo\fùFS°-Ì\u0087à\u0010$êjD¤Þå·5\u0001z\u009b\u008auÞÎ@X\u001e2Ò\u008c\u0094f¤ÿ3I|#º½ó\u0016:àiz\u0092Ô\u008c\u00ad\u0005\u0007Y\u0091\u0089k\u0098Åë^#(|\u0082º\u001cðõ6O@Ù\u0084³\u008f\f\u0005æCpÜÊÕ¤é=9\u0097(a¡ûóT5.M¸\u0087×«A\u0086ûÚ\u0015*\u008fA8\u008cRÎÌ*fm\u009f®\tä£\u0012ÝnwBà\u0086\u001aÈ´\u0006.GG\u0097ñØk(\u0085|>â¨¼Âp|6\u0096\u0006\u000f\u0096¹ÍÓ\u001bM^æ\u0098\u0010Ì\u008ac$`]¦÷àa\u007f\u009bh5P®\u008eØÅr\u001fì_\u0005\u009b,ºº\u0097\u0000Ëî;tPÃ\u009d©ß7;\u009d|d¿òõX\u0003&\u007f\u008cS\u001b\u0097áÙO\u0017ÕV¼\u0086\nÉ\u00909~mÅóS\u00ad9a\u0087'm\u0017ô³BÕ(\u0003¶@\u001d\u0087ë\u0089q;ßy¦ø\fæ\u009a<`nÎPU\u0094#Ô\u0089\u0013\u0017Iþ\u008cDúÒ ¸<\u0007°íñ{oÁi¯F6\u008d\u009cÔj\u0007ðH_\u008b%ã³5\u0019yàæN÷Ô#¢)\b]\u0097\u0091}ÝËEQC8\u008e\u0086àl9úuA£/ µ\"\u0003\u0010é\u0013p\u0088ÞÑ¤\u00072\u000f\u0099\u009bgáÍ;[kÜ¥J\u0088ðÔ\u001e$\u0084O3\u0082YÀÇ$mc\u0094 \u0002ê¨\u001cÖ`|Lë\u0088\u0011Æ¿\b%IL\u0099úÖ`&\u008er5ì£²É~w8\u009d\b\u0004º²ÌØ\u001cFDí\u0083\u001bÓ\u0081./tV¢üþjq\u0090y>N¥\u009aÓÍy\u0017ç[\u000eÒ´³\"l,ºº\u0097\u0000Ëî;tPÃ\u009d©ß7;\u009d|d¿òõX\u0003&\u007f\u008cS\u001b\u0097áÙO\u0017ÕV¼\u0086\nÉ\u00909~mÅóS\u00ad9a\u0087'm\u0017ô¼BÒ(\u0007¶G\u001d¯ëÛq7ß{¦½\fë\u009a:`bÎUU\u009d#É,®º\u0097\u0000Ëî9tPÃ\u009a©Ã71\u009d\u007fd®òîX#&l\u008cB\u001b\u0096áØO4ÕG¼\u009b\nÀ\u0090:~zÅ¨S¬9h\u0087*m\u001aôÐBî(\u0012¶F\u001d\u009cëàq<ß{¦½\fý\u009at`+,\u009eº\u0093\u0000Óî\u0014t@Ã\u009a%\u0017³\u001a\tZç\u009d}ÉÊ\u0013 \u0002>´\u0094ëmsû`Q¦/ð\u0085\u009f\u0012\bè@F\u009eÜÅµ\u000f\u0003O\u0099»w»ÌrZn0§\u008eídÐý\u001cKW!\u009b¿Ã\u0014\nâNxûÖä¯4\u0005}\u0093²iëÇÏ\\\u001d*W,¥º\u0093\u0000Êî\u0016tFÃ\u0086©\u00837},\u008aº\u0087\u0000Ìî\ftJÃ\u0083©Î7&\u009dPd¯òóX(&h\u008cX\u001b\u0087áÕO\u001aÕC¼\u009b\nÍ\u00901~NÅ©Së9\"\u0087omDô\u0083Bþ(\u0007¶O\u001d\u0080ëËq3ß|¦³,\u008aº\u0087\u0000Ìî\ftJÃ\u0083©Î7&\u009dPd¯òóX(&h\u008cX\u001b\u0087áÕO\u001aÕC¼\u009b\nÍ\u00901~NÅ©Së9\"\u0087omDô\u0083\u0088\u008e\u001e¸¤áJ=Ðmg\u00ad\r¨\u0093V9\u001aÀÜV\u008cü>\u0082H(|¿ºEûë7q)\u0018°®ì4^ÚSa\u0099÷Á\u009d\u000e#\u0001ÉlPºæâ\u008c9\u0012m¹µOìÕY{C\u0002\u009a¨Ú>\rÄ\u0000jqñ»\u0087ü-)³1Zæ@\u0005Ö<ll\u0082§\u0018ü¯7Ås[¸ñß\b\t\u009eX4\u0099JÂàùwg\u008d),¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕR¼\u008e\nÚ\u0090&~wÅµSã9a\u0087kmBô\u0084BÕ(\u0003¶M\u001d\u0098ëÀq1ß~¦¬\fì\u009a!`eÎ\u0014U\u0085#Õ\u0089\f\u0017Eþ\u0083D¶Ò ¸h\u0007¸íð{;Á{ó®e\u0097ßÕ1\u0012«N\u001c\u009ev\u008fè3Bz»°-ð\u00870ù{SGÄ\u0094>Ì\u0090]\nGc\u009eÕØO9¡\u007f\u001a±\u008côæ,Xm²R+\u0080\u009dÐ÷\riIÂÈ4À®3\u0000oy´ÓîE.¿|\u0011\u0010\u008a\u0093üÌV\fÈI!É\u009bó\r\"glØµ2ã¤%\u001expXé\u0099CÞµ\u0014/L\u0080\u0081úýltÆm?\u00ad\u0091ì\u000b-}c,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕL¼\u0080\nß\u0090u~lÅ®Sê9/\u0087cmYô\u0097B\u009d(\u0016¶Q\u001d\u0083ëÊq7ßl¦«\f¥\u009a9`bÎ@U\u0099#\u009a\u0089\u0006\u0017Uþ\u0099DþÒ6¸r\u0007\u00adíë{,Ái¯A6\u0097\u009cÔj\nð\u0001_\u009e%ø³;\u0019xà¨,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕC¼\u009a\nÜ\u0090=~{ÅµSð9(\u0087imVô\u0084BÔ(\t¶M\u001dÌëÊq=ßr¦¨\fé\u009a+`\u007fÎQU\u0095\u0010·\u0086\u008e<ÞÒ\u0015HNÿ\u0085\u0095Á\u000b\n¡mX»Îêd+\u001ap°K'ÝÝ\u009fsWéM\u0080\u008f6\u0086¬:Beù¡oâ\u0005*»jQMÈ\u0097~Ð\u0014\t\u008aY!\u008b×ÈM2ã1\u009a¤0î¦5\\vò_iß\u001fÑµ\u0011+^Â\u008axêî8\u0084v;÷ÑèG4ýt\u0093R\n\u009e ÒVJÌ_c\u0096\u0019ö\u008f=%vÜ»rþèb\u009e*4\u001c«\u0083AÙ÷\u0007mL\u0004\u008cºôP8Æ4}«\u0013ï\u0089 ?\u0013Õ\u001dL\u0093âÂ\u0098\u0018\u000eI¥\u0083[õñ$g|\u001e©´î*0À\u0010vQí\u009d\u0083\u00889\u000b¯PF\u0088üñ\u0092q\bb¿£UåËea\u001b\u0017L\u008e\u0087$ÆÚ\rpJç¹\u009dé37©s@ìöòl2\u0002\u001e¸@,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕC¼\u009a\nÜ\u0090=~{ÅµSð9(\u0087imVô\u0084BÔ(\t¶M\u001dÌëÄq'ßl¦¬\f¥\u009a,`nÎ\u0014U\u0092#Ò\u0089\u0002\u0017Cþ\u0086DóÒ7¸<\u0007¶íì{#Áa¯[6\u009b\u009c\u009bj\u0013ðH_\u009e%ÿ³p\u0019~à®NæÔ/¢b\bq\u0097\u008d}ÝË\u0001QK8\u0085\u0086àl8ú{A«/ó,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕL¼\u0080\nß\u0090u~lÅ®Sê9/\u0087cmYô\u0097B\u009d(\u0016¶Q\u001d\u0083ëÊq7ßl¦«P\u0000Æ9|i\u0092¢\bù¿2ÕvK½áÚ\u0018\f\u008e]$\u009cZÇðügb\u009d,3à©¶Àvv\\ì\u0089\u0002Ó¹\n/RE\u009cûç\u0011÷\u00889>aT÷,éº\u008e\u0000\u009f,à\u008a¸\u001c\u0081¦ÑH\u001aÒAe\u008a\u000fÎ\u0091\u0005;bÂ´Tåþ$\u0080\u007f*D½ÒG\u0090éXsN\u001a\u008b¬Ý6<Øpc¾õ¥\u009fe!xË\u0016R\u0098äÏ\u008eG\u0010A»\u0082MÆ× yw\u0000½ªá<=ÆohQóÐ\u0085Ý/\t±SXÌâät&\u001ex¡¨,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕl¼\u0080\nÆ\u00900~>Å²S÷9a\u0087zmVô\u0082BÉ(F¶L\u001d\u008aë\u0089q&ßw¦½\f¥\u009a=`\u007fÎQU\u0081#\u009a\u0089]\u0017\u0000þ\u009eDâÒ6¸l\u0007ùíá{.Áf¯\u00156\u009c\u009cÞjDðR_\u0081%þ³ \u0019mà£Nç,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕq¼\u008c\nÚ\u00900~{ÅµS¤9\u0014\u0087dm[ô\u009fBÞ(\r¶\u0003\u001d\u0085ëÚqrßo¦¹\f÷\u009a:`+Î[U\u0097#\u009a\u0089\u0013\u0017Hþ\u0088D¶Ò ¸h\u0007¼íò{oÁi¯[6\u009a\u009c\u009bj\rðU_Ê%þ³#\u0019=à§NïÔ>¢l\bS\u0097\u009b}ÁËEQO8\u009e\u0086àl9ú\u007fA©/ôµ$\u0003\u0015éRp\u0088ÞÜ¤\u00062\u000f\u0099ÒgµÍ-[o\"¡\u0088ñ\u0016jü\u0014JQÑ\u0093¿\u0086\u0005\u0001\u0093IzÉÀá®44q\u0083µiþ÷.]\u0010ô>b\u0007ØW6\u009c¬Ç\u001b\fqHï\u0083Eä¼2*c\u0080¢þùTÂÃT9\u0016\u0097Þ\rÈd\rÒ[Hº¦ö\u001d8\u008b#áã_þµ\u0090,\u001e\u009aIðÁnÅÅ\u00073\\©°\u0007ù~;Ô{Bé¸í\u0016Æ\u008d\u0002ûUQ\u0085ÏÉ&\u001e\u009cx\n·`úß*5`£¬\u0019¯w\u0088îYDO²\u0097(Ã\u0087\u001dý0k´Áû8/\u0096$\f©zëÐ\u0095O\u000b¥T\u0013\u008b\u0089Ùà\u001c^v´²,¹º\u0080\u0000Ðî\u001bt@Ã\u008b©Ï7\u0004\u009dcdµòäX%&~\u008cE\u001bÓá\u0091OYÕP¼\u008a\nÝ\u0090&~wÅµSã9a\u0087zmEô\u0095BË(\u000f¶L\u001d\u0099ëÚqrß~¦\u00ad\fñ\u009a&`nÎZU\u0085#Ó\u0089\u0004\u0017Aþ\u0099DÿÒ<¸r\u0007ùíí{)Á(¯X6\u009b\u009cÏj\fðN_\u008e%·³u\u0019nàæN¹Ôl¢z\bF\u0097\u009a}ÈËEQM8\u008a\u0086úlqúxA¢/ µ>\u0003\u001déZp\u008cÞÉ¤\u00072K\u0090v\u0006O¼\u001fRÔÈ\u008f\u007fD\u0015\u0000\u008bË!¬ØzN+äê\u009a±0\u008a§\u0014]Zó\u0096iÀ\u0000\u0000¶2,ôÂ°yvï'\u0085ë;åÑ\u008cHPþR\u0094Ï\n\u0085¡MW\u0002Í½c£\u001ab°#&õÜ¥r\u0099éR\u009f\u00105\u0088«\u008cBPø<nø\u0004¶»xQ9Çé}¦\u0013\u0096\u008aB\u0002¾\u0094\u0087.×À\u001cZGí\u008c\u0087È\u0019\u0003³dJ²Üãv\"\by¢B5ÜÏ\u0092a^û\b\u0092È$ü¾9Ppë¬}ó\u0017/©cCWÚ×lÉ\u0006\u0015\u0098A3\u009b,¨º\u0090\u0000Ðî\ntQÃÆ©\u0082Ý?K#ñn\u001f¢\u0085è2\u0012XaÆ\u0098lÚ\u0095\n\u0003U©\u009a×À}Èê9\u0010w¾®$øM>ûca\u0083\u008fÂ4\u0005¢OÈ\u009bvÑ\u009cØ\u0005=³mÙºGùì \u001ae\u0080\u0096.ÕW\u0014ý[k\u0096\u0091Ñ?¶,åºÒ\u0000Ïî\u0019tQÃ\u009a©Î7&\u009d\u007fd\u0094òæX-&h\u008c\u000b\u001bÔCÉÕþoò\u0081!\u001b}¬ªÆâX\u0016òI\u000b\u009f\u009dÈ7\rIUã\u007ft»\u008eÝ 0ºzÓ«eëÿ\u001d\u0011AªÊåÛsìÉñ''½o\n¤`ðþ\u0018TA\u00adÙ2;¤\f\u001e\u0012ðÒj\u009eÝ@·<)ä\u0083«zaì!F£\u0090a\u0006W¼\u000eRÒÈ\u0082\u007fB\u0015O\u008b½!ëØ>N1äñ\u009a§0¥§^]\fóÕi±\u0000J¶\u0000,ýÂ¿ykïh\u0085¬,¥º\u0093\u0000Êî\u0016tFÃ\u0086©\u008b7y\u009d1d\u0093òéX.&h\u008cD\u001b°áÉO\nÕV¼\u0080\nÅ\u00900~lÅ\u009aSñ95\u0087bmRô\u009eBÉ(\u000f¶@\u001d\u008dëÝq7ß{¦\u0088\f÷\u009a!`hÎQU\u0082#É\u0089\"\u0017Xþ\u008eDóÒ#¸h\u0007°íí{!Á(¯\u000f6Þ\u009c\u009ej\u0017ð\u0001_Ç%·³\"\u0019xà¥NæÔ%¢\u007f\bW\u0097\u009b}\u0098Ë\u0004QH8\u009f\u0086ñl#ú:A°/áµ!\u0003\u001aéVp\u0088Þ\u0099¤\u00012@\u0099\u0086gûÍ;[x\"°\u0088è\u0016%ü\u0019".getBytes(LocalizedMessage.DEFAULT_ENCODING)).asCharBuffer().get(cArr, 0, 1730);
        q = cArr;
        t = -5443871357264872718L;
    }

    protected abstract int a(o.ei.c cVar);

    protected abstract void a(Context context, o.ei.c cVar, o.h.d dVar);

    protected abstract o.at.d b();

    protected abstract String d();

    protected abstract void d(o.ei.c cVar) throws WalletValidationException;

    protected abstract String e();

    protected abstract CredentialsLoader r();

    public h(o.i.i iVar, String str, boolean z) {
        this.i = iVar;
        this.l = str;
        this.d = z;
    }

    protected static o.b.c d(Context context, g gVar, c.d dVar) {
        c cVar = new c(dVar, context, gVar);
        o.b.c cVar2 = new o.b.c(context);
        cVar2.c(context, cVar, new d(), new o.h.d(), null);
        int i = r + Opcodes.LSHR;
        x = i % 128;
        switch (i % 2 == 0) {
            case true:
                int i2 = 96 / 0;
                return cVar2;
            default:
                return cVar2;
        }
    }

    public final void d(Context context, CustomerAuthenticationCredentials customerAuthenticationCredentials) throws WalletValidationException {
        new o.bx.b();
        o.f.e b = o.bx.b.b(customerAuthenticationCredentials);
        if (context == null) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.Mandatory;
            Object[] objArr = new Object[1];
            B((char) ((Process.getThreadPriority(0) + 20) >> 6), KeyEvent.normalizeMetaState(0), View.MeasureSpec.makeMeasureSpec(0, 0) + 7, objArr);
            throw new WalletValidationException(walletValidationErrorCode, ((String) objArr[0]).intern());
        }
        try {
            b(context, b);
            int i = x + 1;
            r = i % 128;
            int i2 = i % 2;
        } catch (j e) {
            throw e.c(d());
        }
    }

    /* renamed from: o.p.h$2, reason: invalid class name */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\p\h$2.smali */
    static /* synthetic */ class AnonymousClass2 {
        private static int a;
        private static int c;
        static final /* synthetic */ int[] d;

        static {
            a = 0;
            c = 1;
            int[] iArr = new int[o.f.c.values().length];
            d = iArr;
            try {
                iArr[o.f.c.d.ordinal()] = 1;
            } catch (NoSuchFieldError e) {
            }
            try {
                d[o.f.c.c.ordinal()] = 2;
            } catch (NoSuchFieldError e2) {
            }
            try {
                d[o.f.c.e.ordinal()] = 3;
                int i = c;
                int i2 = ((i | 55) << 1) - (i ^ 55);
                a = i2 % 128;
                if (i2 % 2 != 0) {
                }
            } catch (NoSuchFieldError e3) {
            }
            try {
                d[o.f.c.a.ordinal()] = 4;
                int i3 = a;
                int i4 = (i3 ^ Opcodes.LSHL) + ((i3 & Opcodes.LSHL) << 1);
                c = i4 % 128;
                if (i4 % 2 != 0) {
                }
            } catch (NoSuchFieldError e4) {
            }
            try {
                d[o.f.c.b.ordinal()] = 5;
                int i5 = c + 77;
                a = i5 % 128;
                switch (i5 % 2 != 0 ? (char) 25 : '\r') {
                    case 25:
                        int i6 = 44 / 0;
                        return;
                    default:
                        return;
                }
            } catch (NoSuchFieldError e5) {
            }
        }
    }

    public synchronized void b(final Context context, o.f.e eVar) throws j {
        int i = x + 61;
        r = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        B((char) KeyEvent.normalizeMetaState(0), 7 - (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 33, objArr);
        String intern = ((String) objArr[0]).intern();
        o.ee.g.c();
        Object[] objArr2 = new Object[1];
        B((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), 39 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 24 - TextUtils.getOffsetAfter("", 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        switch (i()) {
            case false:
                break;
            default:
                int i3 = r + 23;
                x = i3 % 128;
                int i4 = i3 % 2;
                if (h()) {
                    if (!this.e.q()) {
                        o.ee.g.c();
                        Object[] objArr3 = new Object[1];
                        B((char) (64272 - TextUtils.indexOf((CharSequence) "", '0', 0, 0)), TextUtils.indexOf("", "", 0) + Opcodes.IUSHR, (ViewConfiguration.getEdgeSlop() >> 16) + 45, objArr3);
                        o.ee.g.e(intern, ((String) objArr3[0]).intern());
                        throw new j(j.c.c);
                    }
                    o.ee.g.c();
                    Object[] objArr4 = new Object[1];
                    B((char) (ViewConfiguration.getJumpTapTimeout() >> 16), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + Opcodes.JSR, 87 - (ViewConfiguration.getKeyRepeatTimeout() >> 16), objArr4);
                    o.ee.g.d(intern, ((String) objArr4[0]).intern());
                    if (!this.k.b(eVar.b(), this.m.intValue() - 1)) {
                        o.ee.g.c();
                        StringBuilder sb = new StringBuilder();
                        Object[] objArr5 = new Object[1];
                        B((char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 61470), View.combineMeasuredStates(0, 0) + 256, 47 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), objArr5);
                        o.ee.g.e(intern, sb.append(((String) objArr5[0]).intern()).append(eVar.b()).toString());
                        throw new j(j.c.b);
                    }
                    o.ee.g.c();
                    Object[] objArr6 = new Object[1];
                    B((char) View.combineMeasuredStates(0, 0), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 302, 42 - (Process.myTid() >> 22), objArr6);
                    o.ee.g.d(intern, ((String) objArr6[0]).intern());
                    switch (AnonymousClass2.d[p().e(context, this.a, eVar, new o.bw.b() { // from class: o.p.h.4
                        public static final byte[] $$a = null;
                        public static final int $$b = 0;
                        private static int $10;
                        private static int $11;
                        private static char a;
                        private static int b;
                        private static long c;
                        private static int g;
                        private static int h;

                        static {
                            init$0();
                            $10 = 0;
                            $11 = 1;
                            h = 0;
                            g = 1;
                            a = (char) 13844;
                            b = 161105445;
                            c = 6565854932352255525L;
                        }

                        /* JADX WARN: Removed duplicated region for block: B:10:0x0026  */
                        /* JADX WARN: Removed duplicated region for block: B:7:0x001e  */
                        /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0026 -> B:4:0x002e). Please report as a decompilation issue!!! */
                        /*
                            Code decompiled incorrectly, please refer to instructions dump.
                            To view partially-correct add '--show-bad-code' argument
                        */
                        private static void i(short r6, int r7, int r8, java.lang.Object[] r9) {
                            /*
                                int r6 = r6 * 3
                                int r6 = 1 - r6
                                int r8 = r8 + 99
                                byte[] r0 = o.p.h.AnonymousClass4.$$a
                                int r7 = r7 + 4
                                byte[] r1 = new byte[r6]
                                int r6 = r6 + (-1)
                                r2 = 0
                                if (r0 != 0) goto L16
                                r4 = r8
                                r3 = r2
                                r8 = r7
                                r7 = r6
                                goto L2e
                            L16:
                                r3 = r2
                            L17:
                                int r7 = r7 + 1
                                byte r4 = (byte) r8
                                r1[r3] = r4
                                if (r3 != r6) goto L26
                                java.lang.String r6 = new java.lang.String
                                r6.<init>(r1, r2)
                                r9[r2] = r6
                                return
                            L26:
                                int r3 = r3 + 1
                                r4 = r0[r7]
                                r5 = r7
                                r7 = r6
                                r6 = r8
                                r8 = r5
                            L2e:
                                int r6 = r6 + r4
                                r5 = r8
                                r8 = r6
                                r6 = r7
                                r7 = r5
                                goto L17
                            */
                            throw new UnsupportedOperationException("Method not decompiled: o.p.h.AnonymousClass4.i(short, int, int, java.lang.Object[]):void");
                        }

                        static void init$0() {
                            $$a = new byte[]{12, -43, 42, 57};
                            $$b = 19;
                        }

                        @Override // o.bw.b
                        public final void d(f fVar) {
                            int i5 = h + 11;
                            g = i5 % 128;
                            int i6 = i5 % 2;
                            o.ee.g.c();
                            Object[] objArr7 = new Object[1];
                            f((ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)) - 403413473, "硭\ua957\ue2f1ᦦ㑥휵龾殗Ⳍ쭞ƒ捫锃岷哮钁酁⯙ꨗ﮸丸䬘\uec7eⴘ뽜鱵\ue06a㖇\uf660絲蠺⊑\ue963", (char) TextUtils.indexOf("", ""), "⃮\uf466盧㗸", "\u0000\u0000\u0000\u0000", objArr7);
                            String intern2 = ((String) objArr7[0]).intern();
                            Object[] objArr8 = new Object[1];
                            f(481674522 - (ViewConfiguration.getFadingEdgeLength() >> 16), "쇉ꀀꬋ\uf64e뵛股̋㎉庭櫩ꩠ햜؎᧔蟦粹鳝ꖇ䒍뀈섚캛䦖⌛裺읓ꈳ\u0b45䟘ꫴ緪少ஞԵ\u2e6d昮ሠﭸ镍\ud83e푶ᓅ䛁䈴\u2fe4㓛΅淔⦤㢋鮟궮૿擂䞶", (char) ((ViewConfiguration.getMaximumFlingVelocity() >> 16) + 30763), "᪃뗅⬜\uec78", "\u0000\u0000\u0000\u0000", objArr8);
                            o.ee.g.d(intern2, ((String) objArr8[0]).intern());
                            h.this.c.add(fVar);
                            h.this.e(context);
                            int i7 = h + 45;
                            g = i7 % 128;
                            switch (i7 % 2 == 0 ? 'G' : Typography.quote) {
                                case '\"':
                                    return;
                                default:
                                    throw null;
                            }
                        }

                        @Override // o.bw.b
                        public final void c(f fVar, o.g.b bVar) {
                            int i5 = g + 7;
                            h = i5 % 128;
                            int i6 = i5 % 2;
                            o.ee.g.c();
                            Object[] objArr7 = new Object[1];
                            f((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 403413473, "硭\ua957\ue2f1ᦦ㑥휵龾殗Ⳍ쭞ƒ捫锃岷哮钁酁⯙ꨗ﮸丸䬘\uec7eⴘ뽜鱵\ue06a㖇\uf660絲蠺⊑\ue963", (char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), "⃮\uf466盧㗸", "\u0000\u0000\u0000\u0000", objArr7);
                            String intern2 = ((String) objArr7[0]).intern();
                            Object[] objArr8 = new Object[1];
                            f(1093279454 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), "ࣦ縔例녋㟾閚娷ㄋ齚黯₀괫܅⧄娚桦浇ﵸཞҥ䳰ຆ夔珳이\ude22赆渍₊꽶覴\u2d69Ḉꔷ頯囨騧룓耕冽㰽\uf01bꚶ\udeff뎙\ue8b5襑ﱇ㔲쥡䗂\ue72f蚗딀掘", (char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 21266), "\uddec⨞ቁ晓", "\u0000\u0000\u0000\u0000", objArr8);
                            o.ee.g.d(intern2, ((String) objArr8[0]).intern());
                            switch (h.this.l() != null ? (char) 16 : '[') {
                                case 16:
                                    int i7 = h + 71;
                                    g = i7 % 128;
                                    switch (i7 % 2 == 0) {
                                        case true:
                                            h.this.l().onCustomerCredentialsInvalid(bVar);
                                            int i8 = 14 / 0;
                                            return;
                                        default:
                                            h.this.l().onCustomerCredentialsInvalid(bVar);
                                            return;
                                    }
                                default:
                                    return;
                            }
                        }

                        private static void f(int i5, String str, char c2, String str2, String str3, Object[] objArr7) {
                            char[] cArr;
                            char[] charArray;
                            char[] cArr2;
                            int i6;
                            Object method;
                            int i7 = 2;
                            switch (str3 != null ? (char) 2 : 'N') {
                                case 'N':
                                    cArr = str3;
                                    break;
                                default:
                                    int i8 = $10 + 81;
                                    $11 = i8 % 128;
                                    if (i8 % 2 == 0) {
                                    }
                                    cArr = str3.toCharArray();
                                    break;
                            }
                            char[] cArr3 = cArr;
                            switch (str2 != null ? '8' : '4') {
                                case '8':
                                    charArray = str2.toCharArray();
                                    break;
                                default:
                                    charArray = str2;
                                    break;
                            }
                            char[] cArr4 = charArray;
                            if (str != null) {
                                int i9 = $11 + 47;
                                $10 = i9 % 128;
                                int i10 = i9 % 2;
                                cArr2 = str.toCharArray();
                            } else {
                                cArr2 = str;
                            }
                            o oVar = new o();
                            int length = cArr4.length;
                            char[] cArr5 = new char[length];
                            int length2 = cArr3.length;
                            char[] cArr6 = new char[length2];
                            int i11 = 0;
                            System.arraycopy(cArr4, 0, cArr5, 0, length);
                            System.arraycopy(cArr3, 0, cArr6, 0, length2);
                            cArr5[0] = (char) (cArr5[0] ^ c2);
                            cArr6[2] = (char) (cArr6[2] + ((char) i5));
                            int length3 = cArr2.length;
                            char[] cArr7 = new char[length3];
                            oVar.e = 0;
                            while (oVar.e < length3) {
                                int i12 = $10 + 69;
                                $11 = i12 % 128;
                                int i13 = i12 % i7;
                                try {
                                    Object[] objArr8 = {oVar};
                                    Object obj = o.e.a.s.get(-429442487);
                                    if (obj == null) {
                                        Class cls = (Class) o.e.a.c(10 - (ViewConfiguration.getDoubleTapTimeout() >> 16), (char) (20953 - ImageFormat.getBitsPerPixel(i11)), 344 - (CdmaCellLocation.convertQuartSecToDecDegrees(i11) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(i11) == 0.0d ? 0 : -1)));
                                        byte b2 = (byte) i11;
                                        byte b3 = (byte) (b2 - 1);
                                        Object[] objArr9 = new Object[1];
                                        i(b2, b3, (byte) (b3 + 1), objArr9);
                                        String str4 = (String) objArr9[i11];
                                        Class<?>[] clsArr = new Class[1];
                                        clsArr[i11] = Object.class;
                                        obj = cls.getMethod(str4, clsArr);
                                        o.e.a.s.put(-429442487, obj);
                                    }
                                    int intValue = ((Integer) ((Method) obj).invoke(null, objArr8)).intValue();
                                    try {
                                        Object[] objArr10 = {oVar};
                                        Object obj2 = o.e.a.s.get(-515165572);
                                        if (obj2 == null) {
                                            Class cls2 = (Class) o.e.a.c((ViewConfiguration.getScrollDefaultDelay() >> 16) + 10, (char) ((-1) - ExpandableListView.getPackedPositionChild(0L)), 207 - Color.argb(i11, i11, i11, i11));
                                            byte b4 = (byte) i11;
                                            byte b5 = (byte) (b4 - 1);
                                            Object[] objArr11 = new Object[1];
                                            i(b4, b5, (byte) (b5 + 3), objArr11);
                                            String str5 = (String) objArr11[i11];
                                            Class<?>[] clsArr2 = new Class[1];
                                            clsArr2[i11] = Object.class;
                                            obj2 = cls2.getMethod(str5, clsArr2);
                                            o.e.a.s.put(-515165572, obj2);
                                        }
                                        int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr10)).intValue();
                                        int i14 = cArr5[oVar.e % 4] * 32718;
                                        try {
                                            Object[] objArr12 = new Object[3];
                                            objArr12[2] = Integer.valueOf(cArr6[intValue]);
                                            objArr12[1] = Integer.valueOf(i14);
                                            objArr12[i11] = oVar;
                                            Object obj3 = o.e.a.s.get(-1614232674);
                                            if (obj3 == null) {
                                                Class cls3 = (Class) o.e.a.c((KeyEvent.getMaxKeyCode() >> 16) + 11, (char) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 281);
                                                byte b6 = (byte) i11;
                                                Object[] objArr13 = new Object[1];
                                                i(b6, (byte) (b6 - 1), (byte) $$a.length, objArr13);
                                                obj3 = cls3.getMethod((String) objArr13[0], Object.class, Integer.TYPE, Integer.TYPE);
                                                o.e.a.s.put(-1614232674, obj3);
                                            }
                                            ((Method) obj3).invoke(null, objArr12);
                                            try {
                                                Object[] objArr14 = {Integer.valueOf(cArr5[intValue2] * 32718), Integer.valueOf(cArr6[intValue])};
                                                Object obj4 = o.e.a.s.get(406147795);
                                                if (obj4 != null) {
                                                    method = obj4;
                                                    i6 = 2;
                                                } else {
                                                    Class cls4 = (Class) o.e.a.c(19 - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (char) (14687 - TextUtils.indexOf("", "", 0, 0)), 111 - TextUtils.lastIndexOf("", '0', 0, 0));
                                                    byte b7 = (byte) 0;
                                                    byte b8 = (byte) (b7 - 1);
                                                    Object[] objArr15 = new Object[1];
                                                    i(b7, b8, (byte) (b8 & 7), objArr15);
                                                    i6 = 2;
                                                    method = cls4.getMethod((String) objArr15[0], Integer.TYPE, Integer.TYPE);
                                                    o.e.a.s.put(406147795, method);
                                                }
                                                cArr6[intValue2] = ((Character) ((Method) method).invoke(null, objArr14)).charValue();
                                                cArr5[intValue2] = oVar.d;
                                                cArr7[oVar.e] = (char) ((((cArr5[intValue2] ^ r4[oVar.e]) ^ (c ^ 6565854932352255525L)) ^ ((int) (b ^ 6565854932352255525L))) ^ ((char) (a ^ 6565854932352255525L)));
                                                oVar.e++;
                                                i7 = i6;
                                                i11 = 0;
                                            } catch (Throwable th) {
                                                Throwable cause = th.getCause();
                                                if (cause == null) {
                                                    throw th;
                                                }
                                                throw cause;
                                            }
                                        } catch (Throwable th2) {
                                            Throwable cause2 = th2.getCause();
                                            if (cause2 == null) {
                                                throw th2;
                                            }
                                            throw cause2;
                                        }
                                    } catch (Throwable th3) {
                                        Throwable cause3 = th3.getCause();
                                        if (cause3 == null) {
                                            throw th3;
                                        }
                                        throw cause3;
                                    }
                                } catch (Throwable th4) {
                                    Throwable cause4 = th4.getCause();
                                    if (cause4 == null) {
                                        throw th4;
                                    }
                                    throw cause4;
                                }
                            }
                            objArr7[0] = new String(cArr7);
                        }
                    }).ordinal()]) {
                        case 1:
                            break;
                        case 2:
                        case 3:
                            switch (l() != null ? 'K' : '0') {
                                case 'K':
                                    int i5 = x + Opcodes.DSUB;
                                    r = i5 % 128;
                                    int i6 = i5 % 2;
                                    l().onCustomerCredentialsInvalid(o.g.b.b);
                                    return;
                            }
                        default:
                            throw new j(j.c.b);
                    }
                    return;
                }
                break;
        }
        o.ee.g.c();
        Object[] objArr7 = new Object[1];
        B((char) (2995 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1))), (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 63, Color.red(0) + 60, objArr7);
        o.ee.g.e(intern, ((String) objArr7[0]).intern());
        throw new j(j.c.e);
    }

    public final List<f> c() {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        B((char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), View.combineMeasuredStates(0, 0) + 7, 33 - ExpandableListView.getPackedPositionGroup(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        B((char) KeyEvent.normalizeMetaState(0), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 345, TextUtils.lastIndexOf("", '0', 0) + 40, objArr2);
        o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.m).toString());
        List<f> list = this.c;
        int i = x + 37;
        r = i % 128;
        int i2 = i % 2;
        return list;
    }

    protected final void d(Context context, final g gVar) throws WalletValidationException {
        o.ei.c c = o.ei.c.c();
        if (!c.b()) {
            c.b(context);
        }
        if (!c.q()) {
            WalletValidationErrorCode walletValidationErrorCode = WalletValidationErrorCode.WrongState;
            Object[] objArr = new Object[1];
            B((char) (TextUtils.indexOf((CharSequence) "", '0') + 1), TextUtils.lastIndexOf("", '0', 0) + 385, 6 - (ViewConfiguration.getTapTimeout() >> 16), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            B((char) (TextUtils.indexOf((CharSequence) "", '0') + 2442), 391 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), 42 - (ViewConfiguration.getTouchSlop() >> 8), objArr2);
            throw new WalletValidationException(walletValidationErrorCode, intern, ((String) objArr2[0]).intern());
        }
        d(c);
        synchronized (this.g) {
            o.ee.g.c();
            Object[] objArr3 = new Object[1];
            B((char) (TextUtils.lastIndexOf("", '0') + 1), TextUtils.getCapsMode("", 0, 0) + 7, (ViewConfiguration.getScrollBarSize() >> 8) + 33, objArr3);
            String intern2 = ((String) objArr3[0]).intern();
            Object[] objArr4 = new Object[1];
            B((char) TextUtils.indexOf("", "", 0), Drawable.resolveOpacity(0, 0) + 432, 8 - (Process.myTid() >> 22), objArr4);
            o.ee.g.d(intern2, ((String) objArr4[0]).intern());
            if (context == null) {
                WalletValidationErrorCode walletValidationErrorCode2 = WalletValidationErrorCode.Mandatory;
                Object[] objArr5 = new Object[1];
                B((char) (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), KeyEvent.getDeadChar(0, 0), View.getDefaultSize(0, 0) + 7, objArr5);
                throw new WalletValidationException(walletValidationErrorCode2, ((String) objArr5[0]).intern());
            }
            if (gVar == null) {
                WalletValidationErrorCode walletValidationErrorCode3 = WalletValidationErrorCode.Mandatory;
                Object[] objArr6 = new Object[1];
                B((char) (ViewConfiguration.getEdgeSlop() >> 16), TextUtils.indexOf("", "", 0, 0) + 440, 36 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr6);
                throw new WalletValidationException(walletValidationErrorCode3, ((String) objArr6[0]).intern());
            }
            if (i()) {
                throw new WalletValidationException(WalletValidationErrorCode.WrongState, d());
            }
            if (!h()) {
                throw new WalletValidationException(WalletValidationErrorCode.WrongState, d());
            }
            if (this.p != null || f()) {
                WalletValidationErrorCode walletValidationErrorCode4 = WalletValidationErrorCode.WrongState;
                Object[] objArr7 = new Object[1];
                B((char) ExpandableListView.getPackedPositionType(0L), (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 475, 28 - ((Process.getThreadPriority(0) + 20) >> 6), objArr7);
                throw new WalletValidationException(walletValidationErrorCode4, ((String) objArr7[0]).intern());
            }
            this.p = d(context, gVar, new c.d() { // from class: o.p.h$$ExternalSyntheticLambda0
                @Override // o.p.c.d
                public final void runWithWallet(Context context2, o.ei.c cVar) {
                    h.this.b(gVar, context2, cVar);
                }
            });
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public /* synthetic */ void b(g gVar, Context context, o.ei.c cVar) {
        synchronized (this.g) {
            o.ee.g.c();
            Object[] objArr = new Object[1];
            B((char) View.MeasureSpec.makeMeasureSpec(0, 0), 7 - TextUtils.indexOf("", ""), 34 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            B((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 48323), 1614 - Color.alpha(0), 25 - ((Process.getThreadPriority(0) + 20) >> 6), objArr2);
            o.ee.g.d(intern, ((String) objArr2[0]).intern());
            if (f()) {
                return;
            }
            try {
                d(context, gVar, cVar);
            } catch (j e) {
                o.ee.g.c();
                Object[] objArr3 = new Object[1];
                B((char) (TextUtils.lastIndexOf("", '0') + 1), View.getDefaultSize(0, 0) + 7, (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 33, objArr3);
                String intern2 = ((String) objArr3[0]).intern();
                Object[] objArr4 = new Object[1];
                B((char) Drawable.resolveOpacity(0, 0), 1639 - KeyEvent.getDeadChar(0, 0), KeyEvent.normalizeMetaState(0) + 91, objArr4);
                o.ee.g.e(intern2, String.format(((String) objArr4[0]).intern(), e.e()));
                gVar.onError(new o.bv.c(AntelopErrorCode.InternalError));
            }
        }
    }

    protected boolean f() {
        int i = x + 3;
        int i2 = i % 128;
        r = i2;
        int i3 = i % 2;
        int i4 = i2 + Opcodes.LUSHR;
        x = i4 % 128;
        switch (i4 % 2 != 0) {
            case true:
                return false;
            default:
                throw null;
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:4:0x0010. Please report as an issue. */
    protected boolean g() {
        int i = x;
        int i2 = i + Opcodes.LSHR;
        r = i2 % 128;
        switch (i2 % 2 == 0) {
        }
        int i3 = i + 75;
        r = i3 % 128;
        int i4 = i3 % 2;
        return false;
    }

    private synchronized void d(Context context, g gVar, o.ei.c cVar) throws j {
        o.ee.g.c();
        Object[] objArr = new Object[1];
        B((char) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), AndroidCharacter.getMirror('0') - ')', (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 32, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        B((char) (Process.myPid() >> 22), 432 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), 8 - (ViewConfiguration.getWindowTouchSlop() >> 8), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        if (!cVar.q()) {
            throw new j(j.c.c);
        }
        switch (!i() ? (char) 21 : (char) 30) {
            case 21:
                int i = x + 23;
                r = i % 128;
                if (i % 2 != 0) {
                    g();
                    throw null;
                }
                if (!g() && h()) {
                    this.f = gVar;
                    this.e = cVar;
                    this.k = cVar.e().b().e().get(this.l);
                    this.m = 0;
                    this.c.clear();
                    this.a.a();
                    o.s.c cVar2 = this.k;
                    if (cVar2 != null) {
                        if (!cVar2.c(a())) {
                            gVar.onError(new o.bv.c(AntelopErrorCode.CustomerAuthenticationImpossible));
                            return;
                        }
                        e(context);
                        int i2 = x + 53;
                        r = i2 % 128;
                        int i3 = i2 % 2;
                        return;
                    }
                    o.ee.g.c();
                    Object[] objArr3 = new Object[1];
                    B((char) TextUtils.getCapsMode("", 0, 0), 7 - (KeyEvent.getMaxKeyCode() >> 16), (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)) + 33, objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    B((char) (TextUtils.indexOf("", "", 0, 0) + 42027), Color.red(0) + 504, TextUtils.getOffsetAfter("", 0) + 45, objArr4);
                    o.ee.g.e(intern2, sb.append(((String) objArr4[0]).intern()).append(this.l).toString());
                    throw new j(j.c.d);
                }
                throw new j(j.c.e);
            default:
                throw new j(j.c.e);
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    final void e(final android.content.Context r18) {
        /*
            Method dump skipped, instructions count: 1460
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.h.e(android.content.Context):void");
    }

    public synchronized void a(o.bv.c cVar) {
        int i = r + 91;
        x = i % 128;
        int i2 = i % 2;
        o.ee.g.c();
        Object[] objArr = new Object[1];
        B((char) TextUtils.indexOf("", "", 0, 0), TextUtils.indexOf((CharSequence) "", '0', 0) + 8, 33 - TextUtils.getCapsMode("", 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        B((char) (AndroidCharacter.getMirror('0') - '0'), TextUtils.indexOf((CharSequence) "", '0', 0) + 1508, 7 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        o.ee.g.d(intern, ((String) objArr2[0]).intern());
        g gVar = this.f;
        switch (gVar != null ? 'b' : (char) 21) {
            case 21:
                break;
            default:
                gVar.abortPrompt();
                this.f.onError(cVar);
                break;
        }
        int i3 = r + 61;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    public void a(Context context, o.bv.c cVar) {
        boolean z;
        int i = x + Opcodes.DMUL;
        int i2 = i % 128;
        r = i2;
        switch (i % 2 != 0 ? (char) 11 : 'T') {
            case Opcodes.BASTORE /* 84 */:
                z = false;
                break;
            default:
                z = true;
                break;
        }
        this.s = z;
        int i3 = i2 + 21;
        x = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final o.ei.c j() {
        int i = r;
        int i2 = i + 79;
        x = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                o.ei.c cVar = this.e;
                int i3 = i + Opcodes.LSHL;
                x = i3 % 128;
                switch (i3 % 2 == 0 ? '2' : ';') {
                    case '2':
                        throw null;
                    default:
                        return cVar;
                }
            default:
                throw null;
        }
    }

    /* JADX WARN: Code restructure failed: missing block: B:14:0x002a, code lost:
    
        if (r5.k != null) goto L23;
     */
    /* JADX WARN: Code restructure failed: missing block: B:16:0x0035, code lost:
    
        if (r5.m == null) goto L27;
     */
    /* JADX WARN: Code restructure failed: missing block: B:17:0x0037, code lost:
    
        r0 = r0 + 83;
        r2 = r0 % 128;
        o.p.h.r = r2;
        r0 = r0 % 2;
        r2 = r2 + 65;
        o.p.h.x = r2 % 128;
        r2 = r2 % 2;
     */
    /* JADX WARN: Code restructure failed: missing block: B:18:0x0047, code lost:
    
        return true;
     */
    /* JADX WARN: Code restructure failed: missing block: B:24:0x0031, code lost:
    
        if (r5.k != null) goto L23;
     */
    /* JADX WARN: Removed duplicated region for block: B:26:0x0048 A[FALL_THROUGH, RETURN] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    protected final boolean i() {
        /*
            r5 = this;
            o.ei.c r0 = r5.e
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L8
            r0 = r2
            goto L9
        L8:
            r0 = r1
        L9:
            switch(r0) {
                case 0: goto Ld;
                default: goto Lc;
            }
        Lc:
            goto L48
        Ld:
            o.p.g r0 = r5.f
            if (r0 == 0) goto L14
            r0 = 13
            goto L16
        L14:
            r0 = 32
        L16:
            switch(r0) {
                case 32: goto Lc;
                default: goto L19;
            }
        L19:
            int r0 = o.p.h.x
            int r3 = r0 + 123
            int r4 = r3 % 128
            o.p.h.r = r4
            int r3 = r3 % 2
            if (r3 == 0) goto L2f
            o.s.c r3 = r5.k
            r4 = 29
            int r4 = r4 / r2
            if (r3 == 0) goto Lc
            goto L33
        L2d:
            r0 = move-exception
            throw r0
        L2f:
            o.s.c r3 = r5.k
            if (r3 == 0) goto Lc
        L33:
            java.lang.Integer r3 = r5.m
            if (r3 == 0) goto Lc
            int r0 = r0 + 83
            int r2 = r0 % 128
            o.p.h.r = r2
            int r0 = r0 % 2
            int r2 = r2 + 65
            int r0 = r2 % 128
            o.p.h.x = r0
            int r2 = r2 % 2
            return r1
        L48:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.h.i():boolean");
    }

    protected final boolean h() {
        int i = x + 83;
        r = i % 128;
        switch (i % 2 != 0) {
            case true:
                int i2 = 25 / 0;
                return this.s;
            default:
                return this.s;
        }
    }

    public void n() {
        int i = x + 31;
        r = i % 128;
        int i2 = i % 2;
        this.m = null;
        this.p = null;
        this.c.clear();
        this.a.a();
        this.s = true;
        this.j = false;
        int i3 = x + 57;
        r = i3 % 128;
        int i4 = i3 % 2;
    }

    protected final boolean m() {
        int i = x;
        int i2 = i + 67;
        r = i2 % 128;
        int i3 = i2 % 2;
        boolean z = this.j;
        int i4 = i + Opcodes.DMUL;
        r = i4 % 128;
        int i5 = i4 % 2;
        return z;
    }

    private o.i.i a() {
        o.i.i iVar;
        int i = x;
        int i2 = i + Opcodes.LUSHR;
        r = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                iVar = this.i;
                break;
            default:
                iVar = this.i;
                int i3 = 2 / 0;
                break;
        }
        int i4 = i + 11;
        r = i4 % 128;
        int i5 = i4 % 2;
        return iVar;
    }

    public final String o() {
        int i = x + 39;
        r = i % 128;
        switch (i % 2 != 0 ? (char) 30 : (char) 19) {
            case 19:
                return this.l;
            default:
                throw null;
        }
    }

    public final boolean k() {
        int i = x + 77;
        r = i % 128;
        switch (i % 2 == 0) {
            case false:
                int i2 = 86 / 0;
                return this.d;
            default:
                return this.d;
        }
    }

    public final g l() {
        int i = r + Opcodes.LSHR;
        x = i % 128;
        switch (i % 2 == 0) {
            case true:
                throw null;
            default:
                return this.f;
        }
    }

    private CredentialsLoader p() {
        int i = x + Opcodes.DDIV;
        r = i % 128;
        int i2 = i % 2;
        switch (this.h != null) {
            case false:
                this.h = r();
                break;
        }
        CredentialsLoader credentialsloader = this.h;
        int i3 = x + 11;
        r = i3 % 128;
        int i4 = i3 % 2;
        return credentialsloader;
    }

    public final String toString() {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        B((char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 61887), 1514 - View.getDefaultSize(0, 0), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 40, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(this.i);
        Object[] objArr2 = new Object[1];
        B((char) (ExpandableListView.getPackedPositionChild(0L) + 1), View.resolveSizeAndState(0, 0, 0) + 1554, (ViewConfiguration.getDoubleTapTimeout() >> 16) + 15, objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(this.l).append('\'');
        Object[] objArr3 = new Object[1];
        B((char) (View.MeasureSpec.getSize(0) + 28460), 1569 - (ViewConfiguration.getTouchSlop() >> 8), 23 - (ViewConfiguration.getScrollBarSize() >> 8), objArr3);
        StringBuilder append3 = append2.append(((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        B((char) Color.argb(0, 0, 0, 0), TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 1046, 3 - (ViewConfiguration.getKeyRepeatDelay() >> 16), objArr4);
        StringBuilder append4 = append3.append(o.ee.o.d(((String) objArr4[0]).intern(), (Iterator) this.c.iterator()));
        Object[] objArr5 = new Object[1];
        B((char) (51518 - Color.red(0)), 1592 - (ViewConfiguration.getDoubleTapTimeout() >> 16), 10 - (ViewConfiguration.getTapTimeout() >> 16), objArr5);
        StringBuilder append5 = append4.append(((String) objArr5[0]).intern()).append(this.k);
        Object[] objArr6 = new Object[1];
        B((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 7902), ExpandableListView.getPackedPositionGroup(0L) + 1602, (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 11, objArr6);
        String obj = append5.append(((String) objArr6[0]).intern()).append(this.m).append('}').toString();
        int i = r + 59;
        x = i % 128;
        int i2 = i % 2;
        return obj;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void B(char r19, int r20, int r21, java.lang.Object[] r22) {
        /*
            Method dump skipped, instructions count: 722
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.p.h.B(char, int, int, java.lang.Object[]):void");
    }
}
