package kotlinx.coroutines;

import kotlin.Metadata;

/* compiled from: Dispatchers.kt */
@Metadata(d1 = {"\u0000\b\n\u0000\n\u0002\u0010\u000e\n\u0000\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0086T¢\u0006\u0002\n\u0000¨\u0006\u0002"}, d2 = {"IO_PARALLELISM_PROPERTY_NAME", "", "kotlinx-coroutines-core"}, k = 2, mv = {1, 6, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\kotlinx\coroutines\DispatchersKt.smali */
public final class DispatchersKt {
    public static final String IO_PARALLELISM_PROPERTY_NAME = "kotlinx.coroutines.io.parallelism";
}
