package o.bt;

import android.content.Context;
import android.media.AudioTrack;
import android.os.Process;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import androidx.core.content.ContextCompat;
import com.esotericsoftware.asm.Opcodes;
import java.util.Arrays;
import java.util.List;
import kotlin.text.Typography;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bt\b.smali */
public final class b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static List<String> a;
    private static final String[] b;
    private static long c;
    private static int d;
    private static int e;

    static void d() {
        c = -8218699944955088243L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002b). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void g(int r5, int r6, byte r7, java.lang.Object[] r8) {
        /*
            int r6 = r6 * 2
            int r6 = r6 + 112
            int r5 = r5 * 4
            int r5 = r5 + 4
            byte[] r0 = o.bt.b.$$a
            int r7 = r7 * 3
            int r7 = r7 + 1
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L19
            r4 = r6
            r3 = r2
            r6 = r5
            goto L2b
        L19:
            r3 = r2
        L1a:
            byte r4 = (byte) r6
            r1[r3] = r4
            if (r3 != r7) goto L27
            java.lang.String r5 = new java.lang.String
            r5.<init>(r1, r2)
            r8[r2] = r5
            return
        L27:
            int r3 = r3 + 1
            r4 = r0[r5]
        L2b:
            int r5 = r5 + 1
            int r6 = r6 + r4
            goto L1a
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bt.b.g(int, int, byte, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{117, -111, 19, -37};
        $$b = 11;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        d = 0;
        e = 1;
        d();
        KeyEvent.normalizeMetaState(0);
        Object[] objArr = new Object[1];
        f("㑼拰饿㟦湾蓻㍫榦聵㻣啱菑㩐僉轄◙层諀℅忪\uf667ⳡ", 22148 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("㑼坶\uf273ᵠ롦\udb6d晧脐ⱅ何\uea5d畇遈㌯帨錄ТꜦ쉩洋蠷⬠똶턼簫鼥㫋", 25348 - Process.getGidForName(""), objArr2);
        String intern2 = ((String) objArr2[0]).intern();
        Object[] objArr3 = new Object[1];
        f("㑼헆\uf713酰늦峽繇῀㧅\udb25\ue57d蚷ꀈ䉟授෯⼢쥶\uea89\uf433険랇凖猍Ჶ㻯\ud831祐鮅ꗋ䝤悤˶Ⱇ칄\ueff6褨ꭠ뒆", (ViewConfiguration.getDoubleTapTimeout() >> 16) + 57781, objArr3);
        String intern3 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        f("㑼㬘⪯ᨮ\u09de祣棻忞伵뺻깁鷩走ﰁ\ue3b4팱싂㉨↵Ⴞ\u0004瞙杪囉䙃딫꒜鐖鯦譍遲\ue9b7\ud93e좙㡦\u2fecὝฯ綫洕峡", (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) + 3946, objArr4);
        b = new String[]{intern, intern2, intern3, ((String) objArr4[0]).intern()};
        int i = e + 43;
        d = i % 128;
        switch (i % 2 != 0 ? '%' : '[') {
            case Opcodes.DUP_X2 /* 91 */:
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public static List<String> c(Context context) {
        int i = e;
        int i2 = i + 21;
        d = i2 % 128;
        if (i2 % 2 != 0) {
            Object obj = null;
            obj.hashCode();
            throw null;
        }
        switch (a == null ? '!' : Typography.amp) {
            case '&':
                break;
            default:
                int i3 = i + 1;
                d = i3 % 128;
                int i4 = i3 % 2;
                a = d(context, Arrays.asList(b));
                int i5 = e + 93;
                d = i5 % 128;
                int i6 = i5 % 2;
                break;
        }
        return a;
    }

    public static void a() {
        int i = e;
        int i2 = i + 55;
        d = i2 % 128;
        int i3 = i2 % 2;
        a = null;
        int i4 = i + 99;
        d = i4 % 128;
        switch (i4 % 2 != 0 ? '.' : (char) 14) {
            case '.':
                int i5 = 1 / 0;
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static java.util.List<java.lang.String> d(android.content.Context r9, java.util.List<java.lang.String> r10) {
        /*
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            java.util.Iterator r10 = r10.iterator()
        La:
            boolean r1 = r10.hasNext()
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L14
            r1 = r3
            goto L15
        L14:
            r1 = r2
        L15:
            switch(r1) {
                case 0: goto L19;
                default: goto L18;
            }
        L18:
            return r0
        L19:
            int r1 = o.bt.b.d
            int r1 = r1 + 119
            int r4 = r1 % 128
            o.bt.b.e = r4
            int r1 = r1 % 2
            java.lang.Object r1 = r10.next()
            java.lang.String r1 = (java.lang.String) r1
            boolean r4 = e(r9, r1)
            java.lang.String r5 = "㑍࠻䳩肹앸ᤡ巼醡홪⨨溣ꊱ\ue758㬔翖뎗\uf048㐜"
            if (r4 != 0) goto L7f
            o.ee.g.c()
            int r4 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r4 = (byte) r4
            int r4 = r4 + 15428
            java.lang.Object[] r6 = new java.lang.Object[r2]
            f(r5, r4, r6)
            r4 = r6[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            int r6 = android.graphics.Color.green(r3)
            int r6 = 19961 - r6
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r7 = "㑯禁꾞\udd83Μ놳\ue7bfᗲ宥覹㿕淃鏘쇋矰ꗣ\uebe2᧺便"
            f(r7, r6, r2)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r2 = r5.append(r2)
            java.lang.StringBuilder r2 = r2.append(r1)
            java.lang.String r2 = r2.toString()
            o.ee.g.d(r4, r2)
            r0.add(r1)
            int r1 = o.bt.b.d
            int r1 = r1 + 73
            int r2 = r1 % 128
            o.bt.b.e = r2
            int r1 = r1 % 2
            goto La
        L7f:
            o.ee.g.c()
            int r4 = android.view.ViewConfiguration.getKeyRepeatTimeout()
            int r4 = r4 >> 16
            int r4 = 15427 - r4
            java.lang.Object[] r6 = new java.lang.Object[r2]
            f(r5, r4, r6)
            r4 = r6[r3]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            r6 = 39551(0x9a7f, float:5.5423E-41)
            int r7 = android.view.View.MeasureSpec.getMode(r3)
            int r7 = r7 + r6
            java.lang.Object[] r6 = new java.lang.Object[r2]
            java.lang.String r8 = "㑭긇\u0091\ufb0d嶈〕ꪔ\u0d0d\ue78a娄㳋"
            f(r8, r7, r6)
            r6 = r6[r3]
            java.lang.String r6 = (java.lang.String) r6
            java.lang.String r6 = r6.intern()
            java.lang.StringBuilder r5 = r5.append(r6)
            java.lang.StringBuilder r1 = r5.append(r1)
            int r5 = android.view.KeyEvent.getModifierMetaStateMask()
            byte r5 = (byte) r5
            int r5 = r5 + 18542
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r6 = "㐽簑꒫\ued28ᗌ幝蛷캟睕뾯\ue02d⣓兯駠솎ਚ"
            f(r6, r5, r2)
            r2 = r2[r3]
            java.lang.String r2 = (java.lang.String) r2
            java.lang.String r2 = r2.intern()
            java.lang.StringBuilder r1 = r1.append(r2)
            java.lang.String r1 = r1.toString()
            o.ee.g.d(r4, r1)
            goto La
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bt.b.d(android.content.Context, java.util.List):java.util.List");
    }

    public static boolean e(Context context, String str) {
        int i = d + 43;
        e = i % 128;
        switch (i % 2 == 0 ? '\f' : (char) 5) {
            case '\f':
                ContextCompat.checkSelfPermission(context, str);
                throw null;
            default:
                if (ContextCompat.checkSelfPermission(context, str) != -1) {
                    return true;
                }
                int i2 = d + 85;
                e = i2 % 128;
                int i3 = i2 % 2;
                return false;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void f(java.lang.String r16, int r17, java.lang.Object[] r18) {
        /*
            Method dump skipped, instructions count: 722
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bt.b.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
