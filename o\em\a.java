package o.em;

import android.graphics.Color;
import android.graphics.PointF;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import kotlin.io.encoding.Base64;
import kotlin.jvm.internal.ByteCompanionObject;
import o.a.o;
import o.ei.i;
import o.eo.f;
import o.er.t;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static int c;
    private static int d;
    private static final Map<Class<? extends d<?>>, String> e;
    private static int f;
    private static short[] g;
    private static byte[] h;
    private static long i;
    private static char j;
    private static int l;
    private static int n;
    private final Map<String, c> b = new HashMap();

    static void a() {
        h = new byte[]{-17, 17, 31, -30, -16, 19, 28, -43, 9, -17, 9, 0, -4, 7, 120, -122, -111, 114, 125, -76, 104, -114, 104, 97, -99, 102, 46, 34, -47, -41, -48, -53, 49, 49, 50, -54, 51, -32, 19, 62, -35, 50, -105, 100, -100, 105, -105, -114, 118, 119, -119, -121, 122, 104, -117, -124, 77, -111, 119, -111, -104, 100, -65, -116, ByteCompanionObject.MAX_VALUE, -121, 114, -116, 117, -52, 51, -120, 123, 125, 122, -43, 56, 120, -124, 116, ByteCompanionObject.MIN_VALUE, -117, 119, ByteCompanionObject.MIN_VALUE, -116, 116, -47, 51, 126, 125, -120, -53, Base64.padSymbol, 122, 124, -118, 106, -119, -124, -112, -112, -112, -112, -112, -112};
        d = 909053590;
        a = -513663029;
        c = -156512603;
        j = (char) 17957;
        f = 746079924;
        i = 6565854932352255525L;
    }

    static void init$0() {
        $$a = new byte[]{75, 105, 70, 99};
        $$b = 206;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x0027  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x001f  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x0027 -> B:4:0x002f). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void p(short r7, byte r8, byte r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 * 3
            int r8 = 1 - r8
            int r7 = r7 + 99
            byte[] r0 = o.em.a.$$a
            int r9 = r9 + 4
            byte[] r1 = new byte[r8]
            r2 = 0
            if (r0 != 0) goto L15
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r10
            r10 = r9
            goto L2f
        L15:
            r3 = r2
        L16:
            int r9 = r9 + 1
            int r4 = r3 + 1
            byte r5 = (byte) r7
            r1[r3] = r5
            if (r4 != r8) goto L27
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L27:
            r3 = r0[r9]
            r6 = r10
            r10 = r9
            r9 = r3
            r3 = r1
            r1 = r0
            r0 = r6
        L2f:
            int r7 = r7 + r9
            r9 = r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L16
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.a.p(short, byte, byte, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        n = 1;
        a();
        View.MeasureSpec.getSize(0);
        ExpandableListView.getPackedPositionForGroup(0);
        AudioTrack.getMinVolume();
        ViewConfiguration.getWindowTouchSlop();
        Color.rgb(0, 0, 0);
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getZoomControlsTimeout();
        ViewConfiguration.getScrollBarSize();
        TextUtils.indexOf("", "", 0, 0);
        TextUtils.indexOf("", "", 0);
        TextUtils.indexOf((CharSequence) "", '0', 0);
        ViewConfiguration.getScrollBarFadeDuration();
        ViewConfiguration.getMaximumDrawingCacheSize();
        KeyEvent.getDeadChar(0, 0);
        SystemClock.uptimeMillis();
        KeyEvent.normalizeMetaState(0);
        Process.myTid();
        Color.argb(0, 0, 0, 0);
        KeyEvent.keyCodeFromString("");
        ViewConfiguration.getEdgeSlop();
        TextUtils.indexOf((CharSequence) "", '0', 0, 0);
        TextUtils.getOffsetBefore("", 0);
        ViewConfiguration.getLongPressTimeout();
        ViewConfiguration.getMinimumFlingVelocity();
        Process.getGidForName("");
        Color.green(0);
        AudioTrack.getMinVolume();
        KeyEvent.normalizeMetaState(0);
        KeyEvent.getMaxKeyCode();
        HashMap hashMap = new HashMap();
        Object[] objArr = new Object[1];
        m((byte) ((Process.myPid() >> 22) - 69), 1065041892 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), (short) Gravity.getAbsoluteGravity(0, 0), (-1) - ((byte) KeyEvent.getModifierMetaStateMask()), 682815256 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr);
        hashMap.put(o.em.c.class, ((String) objArr[0]).intern());
        Object[] objArr2 = new Object[1];
        o((-1457460225) - TextUtils.indexOf("", "", 0), "赠栭▄㏨簥ء劢\uee83䨮큦柛닞┧념\uf1b8", (char) (51843 - ExpandableListView.getPackedPositionGroup(0L)), "Ｃ⃫莩賊", "\u0000\u0000\u0000\u0000", objArr2);
        hashMap.put(b.class, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        o(862515771 - TextUtils.lastIndexOf("", '0', 0, 0), "젫푩᭥怩쐥ᄡ뉧芔㺖\ud9b3\ud8b7䢺㬌仆\uec1b홻̽", (char) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 63933), "㳢棲븳㻹", "\u0000\u0000\u0000\u0000", objArr3);
        hashMap.put(g.class, ((String) objArr3[0]).intern());
        Object[] objArr4 = new Object[1];
        m((byte) ((SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)) + 91), 1065041898 - (ViewConfiguration.getScrollBarSize() >> 8), (short) KeyEvent.keyCodeFromString(""), TextUtils.getOffsetBefore("", 0) + 6, TextUtils.indexOf((CharSequence) "", '0', 0) + 682815241, objArr4);
        hashMap.put(j.class, ((String) objArr4[0]).intern());
        e = Collections.unmodifiableMap(hashMap);
        int i2 = l + 35;
        n = i2 % 128;
        int i3 = i2 % 2;
    }

    public final void d(o.eg.b bVar) throws i {
        this.b.clear();
        int i2 = 682815241;
        Object[] objArr = new Object[1];
        m((byte) ((-110) - (ViewConfiguration.getMinimumFlingVelocity() >> 16)), 1065041867 - TextUtils.getTrimmedLength(""), (short) View.MeasureSpec.getSize(0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 9, 682815241 - TextUtils.getCapsMode("", 0, 0), objArr);
        if (bVar.C(((String) objArr[0]).intern())) {
            float f2 = 0.0f;
            long j2 = 0;
            try {
                Object[] objArr2 = new Object[1];
                m((byte) (KeyEvent.normalizeMetaState(0) - 110), KeyEvent.normalizeMetaState(0) + 1065041867, (short) Color.blue(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 9, 682815241 - KeyEvent.normalizeMetaState(0), objArr2);
                o.eg.e s = bVar.s(((String) objArr2[0]).intern());
                int i3 = 0;
                while (i3 < s.d()) {
                    o.eg.b b = s.b(i3);
                    Object[] objArr3 = new Object[1];
                    m((byte) ((-14) - MotionEvent.axisFromString("")), 1065041882 - (SystemClock.elapsedRealtime() > j2 ? 1 : (SystemClock.elapsedRealtime() == j2 ? 0 : -1)), (short) ExpandableListView.getPackedPositionType(j2), (TypedValue.complexToFloat(0) > f2 ? 1 : (TypedValue.complexToFloat(0) == f2 ? 0 : -1)) + 7, i2 - View.MeasureSpec.getSize(0), objArr3);
                    String r = b.r(((String) objArr3[0]).intern());
                    o.em.c cVar = new o.em.c();
                    b bVar2 = new b();
                    g gVar = new g();
                    j jVar = new j();
                    Object[] objArr4 = new Object[1];
                    m((byte) ((ViewConfiguration.getWindowTouchSlop() >> 8) - 69), Color.green(0) + 1065041893, (short) ((ViewConfiguration.getGlobalActionKeyTimeout() > j2 ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == j2 ? 0 : -1)) - 1), AndroidCharacter.getMirror('0') - '0', TextUtils.indexOf((CharSequence) "", '0', 0) + 682815258, objArr4);
                    switch (b.C(((String) objArr4[0]).intern())) {
                        case false:
                            break;
                        default:
                            Object[] objArr5 = new Object[1];
                            m((byte) (TextUtils.getTrimmedLength("") - 69), Color.red(0) + 1065041893, (short) TextUtils.indexOf("", "", 0), KeyEvent.normalizeMetaState(0), View.combineMeasuredStates(0, 0) + 682815257, objArr5);
                            cVar.b(b.v(((String) objArr5[0]).intern()));
                            break;
                    }
                    Object[] objArr6 = new Object[1];
                    o((Process.myPid() >> 22) - 1457460225, "赠栭▄㏨簥ء劢\uee83䨮큦柛닞┧념\uf1b8", (char) (View.resolveSizeAndState(0, 0, 0) + 51843), "Ｃ⃫莩賊", "\u0000\u0000\u0000\u0000", objArr6);
                    switch (b.C(((String) objArr6[0]).intern()) ? '\n' : (char) 27) {
                        case 27:
                            break;
                        default:
                            Object[] objArr7 = new Object[1];
                            o((-1457460225) - KeyEvent.normalizeMetaState(0), "赠栭▄㏨簥ء劢\uee83䨮큦柛닞┧념\uf1b8", (char) ((ViewConfiguration.getScrollBarSize() >> 8) + 51843), "Ｃ⃫莩賊", "\u0000\u0000\u0000\u0000", objArr7);
                            bVar2.b(b.v(((String) objArr7[0]).intern()));
                            break;
                    }
                    Object[] objArr8 = new Object[1];
                    o((ViewConfiguration.getPressedStateDuration() >> 16) + 862515772, "젫푩᭥怩쐥ᄡ뉧芔㺖\ud9b3\ud8b7䢺㬌仆\uec1b홻̽", (char) ((Process.getElapsedCpuTime() > 0L ? 1 : (Process.getElapsedCpuTime() == 0L ? 0 : -1)) + 63933), "㳢棲븳㻹", "\u0000\u0000\u0000\u0000", objArr8);
                    switch (b.C(((String) objArr8[0]).intern()) ? '0' : (char) 27) {
                        case 27:
                            break;
                        default:
                            Object[] objArr9 = new Object[1];
                            o(862515772 - View.resolveSize(0, 0), "젫푩᭥怩쐥ᄡ뉧芔㺖\ud9b3\ud8b7䢺㬌仆\uec1b홻̽", (char) ((SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 63933), "㳢棲븳㻹", "\u0000\u0000\u0000\u0000", objArr9);
                            gVar.b(b.v(((String) objArr9[0]).intern()));
                            int i4 = l + Opcodes.LSHR;
                            n = i4 % 128;
                            int i5 = i4 % 2;
                            break;
                    }
                    Object[] objArr10 = new Object[1];
                    m((byte) (TextUtils.indexOf((CharSequence) "", '0', 0, 0) + 93), Color.argb(0, 0, 0, 0) + 1065041898, (short) Color.argb(0, 0, 0, 0), View.resolveSize(0, 0) + 6, 682815240 - (ViewConfiguration.getEdgeSlop() >> 16), objArr10);
                    if (b.C(((String) objArr10[0]).intern())) {
                        int i6 = n + 63;
                        l = i6 % 128;
                        int i7 = i6 % 2;
                        Object[] objArr11 = new Object[1];
                        m((byte) (92 - (ViewConfiguration.getLongPressTimeout() >> 16)), 1065041898 - KeyEvent.keyCodeFromString(""), (short) (ViewConfiguration.getFadingEdgeLength() >> 16), 7 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)), (ViewConfiguration.getScrollBarSize() >> 8) + 682815240, objArr11);
                        jVar.b(b.v(((String) objArr11[0]).intern()));
                    }
                    this.b.put(r, new c(cVar, bVar2, gVar, jVar));
                    i3++;
                    f2 = 0.0f;
                    j2 = 0;
                    i2 = 682815241;
                }
            } catch (o.eg.d e2) {
                o.ee.g.c();
                Object[] objArr12 = new Object[1];
                m((byte) (10 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24)), 1065041909 + (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (short) (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getMaximumDrawingCacheSize() >> 24) + 16, 682815210 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)), objArr12);
                String intern = ((String) objArr12[0]).intern();
                Object[] objArr13 = new Object[1];
                o((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 277154820, "∔㙎㌕籝\uf0c9蟡ﵝ⢼ꂴ涽竱ﲝ狖걃\uf51e黹ấꠂ䝥鲈ペ늫鞹퓡偩풖嫡", (char) (59910 - Color.argb(0, 0, 0, 0)), "ﱕ竳ۯ離", "\u0000\u0000\u0000\u0000", objArr13);
                o.ee.g.a(intern, ((String) objArr13[0]).intern(), e2);
                Object[] objArr14 = new Object[1];
                m((byte) ((ViewConfiguration.getKeyRepeatTimeout() >> 16) + 17), KeyEvent.keyCodeFromString("") + 1065041930, (short) Color.red(0), 31 - (ViewConfiguration.getTapTimeout() >> 16), 682815246 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), objArr14);
                throw new i(((String) objArr14[0]).intern());
            }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.IfRegionMaker.process(IfRegionMaker.java:94)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:109)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public final o.eg.b c() throws o.eg.d {
        /*
            Method dump skipped, instructions count: 374
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.a.c():o.eg.b");
    }

    public final List<f> b(String str) {
        int i2 = n + Opcodes.LREM;
        l = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        switch (cVar != null) {
            case false:
                int i4 = l + 37;
                n = i4 % 128;
                int i5 = i4 % 2;
                List<f> emptyList = Collections.emptyList();
                int i6 = l + Opcodes.LSUB;
                n = i6 % 128;
                switch (i6 % 2 == 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return emptyList;
                }
            default:
                return cVar.b().d();
        }
    }

    public final Long c(String str) {
        int i2 = l + Opcodes.DREM;
        n = i2 % 128;
        Object obj = null;
        if (i2 % 2 != 0) {
            c cVar = this.b.get(str);
            switch (cVar == null ? (char) 28 : (char) 20) {
                case 20:
                    return Long.valueOf(cVar.b().a());
                default:
                    int i3 = n + Opcodes.LSHR;
                    l = i3 % 128;
                    switch (i3 % 2 != 0 ? '`' : 'O') {
                        case Opcodes.IADD /* 96 */:
                            throw null;
                        default:
                            return null;
                    }
            }
        }
        this.b.get(str);
        obj.hashCode();
        throw null;
    }

    public final List<t> d(String str) {
        int i2 = l + 65;
        n = i2 % 128;
        switch (i2 % 2 == 0 ? '\b' : '4') {
            case '4':
                c cVar = this.b.get(str);
                switch (cVar == null ? 'U' : '\f') {
                    case '\f':
                        return cVar.c().d();
                    default:
                        int i3 = l + 51;
                        n = i3 % 128;
                        int i4 = i3 % 2;
                        List<t> emptyList = Collections.emptyList();
                        int i5 = l + Opcodes.LUSHR;
                        n = i5 % 128;
                        int i6 = i5 % 2;
                        return emptyList;
                }
            default:
                this.b.get(str);
                throw null;
        }
    }

    public final Long e(String str) {
        c cVar = this.b.get(str);
        switch (cVar == null ? 'a' : ')') {
            case ')':
                Long valueOf = Long.valueOf(cVar.c().a());
                int i2 = l + 73;
                n = i2 % 128;
                int i3 = i2 % 2;
                return valueOf;
            default:
                int i4 = l + 91;
                n = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    public final List<o.eo.j> a(String str) {
        int i2 = n + 93;
        l = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        switch (cVar == null) {
            case true:
                List<o.eo.j> emptyList = Collections.emptyList();
                int i4 = l + 73;
                n = i4 % 128;
                switch (i4 % 2 == 0) {
                    case true:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                    default:
                        return emptyList;
                }
            default:
                return cVar.e().d();
        }
    }

    public final List<o.es.a<?>> i(String str) {
        c cVar = this.b.get(str);
        switch (cVar == null) {
            case true:
                int i2 = n + 25;
                l = i2 % 128;
                int i3 = i2 % 2;
                List<o.es.a<?>> emptyList = Collections.emptyList();
                int i4 = l + 49;
                n = i4 % 128;
                switch (i4 % 2 != 0) {
                    case true:
                        return emptyList;
                    default:
                        Object obj = null;
                        obj.hashCode();
                        throw null;
                }
            default:
                return cVar.a().d();
        }
    }

    public final Date h(String str) {
        int i2 = n + 15;
        l = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        if (cVar != null) {
            return cVar.a().j();
        }
        Date date = new Date();
        int i4 = l + 45;
        n = i4 % 128;
        switch (i4 % 2 == 0 ? (char) 19 : 'X') {
            case 19:
                int i5 = 65 / 0;
                return date;
            default:
                return date;
        }
    }

    public final Long j(String str) {
        c cVar = this.b.get(str);
        switch (cVar == null) {
            case true:
                int i2 = n;
                int i3 = i2 + 87;
                l = i3 % 128;
                switch (i3 % 2 != 0 ? '=' : 'X') {
                    case Opcodes.POP2 /* 88 */:
                        int i4 = i2 + Opcodes.LNEG;
                        l = i4 % 128;
                        if (i4 % 2 == 0) {
                            return null;
                        }
                        throw null;
                    default:
                        throw null;
                }
            default:
                return Long.valueOf(cVar.a().a());
        }
    }

    public final Long g(String str) {
        int i2 = n + Opcodes.DREM;
        l = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        switch (cVar == null ? 'P' : ':') {
            case Opcodes.ASTORE /* 58 */:
                return Long.valueOf(cVar.e().a());
            default:
                int i4 = l + 85;
                n = i4 % 128;
                int i5 = i4 % 2;
                return null;
        }
    }

    public final void b(String str, String str2, f.a aVar) {
        f fVar = new f(null, str2, null, null, null, null, null, aVar, null, null, null, null, null);
        c cVar = this.b.get(str);
        switch (cVar != null ? 'b' : 'V') {
            case Opcodes.SASTORE /* 86 */:
                break;
            default:
                int i2 = l + 91;
                n = i2 % 128;
                if (i2 % 2 == 0) {
                    cVar.b().d().add(fVar);
                    Object obj = null;
                    obj.hashCode();
                    throw null;
                }
                cVar.b().d().add(fVar);
                int i3 = n + 99;
                l = i3 % 128;
                if (i3 % 2 == 0) {
                    break;
                } else {
                    break;
                }
        }
        int i4 = n + 9;
        l = i4 % 128;
        int i5 = i4 % 2;
    }

    public final void d() {
        int i2 = l + 83;
        n = i2 % 128;
        int i3 = i2 % 2;
        Iterator<c> it = this.b.values().iterator();
        int i4 = n + 73;
        l = i4 % 128;
        int i5 = i4 % 2;
        while (true) {
            switch (it.hasNext() ? (char) 27 : (char) 22) {
                case 22:
                    this.b.clear();
                    int i6 = n + 81;
                    l = i6 % 128;
                    int i7 = i6 % 2;
                    return;
                default:
                    it.next().h();
            }
        }
    }

    public final c f(String str) {
        int i2 = l + Opcodes.LSHL;
        n = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        int i4 = n + 33;
        l = i4 % 128;
        int i5 = i4 % 2;
        return cVar;
    }

    public final c k(String str) {
        int i2 = n + Opcodes.LSHR;
        l = i2 % 128;
        int i3 = i2 % 2;
        c cVar = this.b.get(str);
        if (cVar != null) {
            return cVar;
        }
        c cVar2 = new c();
        this.b.put(str, cVar2);
        int i4 = l + 23;
        n = i4 % 128;
        int i5 = i4 % 2;
        return cVar2;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\em\a$c.smali */
    public static final class c {
        private static int a = 0;
        private static int j = 1;
        private final b b;
        private final j c;
        private final o.em.c d;
        private final g e;

        public c(o.em.c cVar, b bVar, g gVar, j jVar) {
            this.d = cVar;
            this.b = bVar;
            this.e = gVar;
            this.c = jVar;
        }

        public c() {
            this(new o.em.c(), new b(), new g(), new j());
        }

        public final o.em.c b() {
            int i = j;
            int i2 = ((i | 13) << 1) - (i ^ 13);
            int i3 = i2 % 128;
            a = i3;
            int i4 = i2 % 2;
            o.em.c cVar = this.d;
            int i5 = (i3 ^ 41) + ((i3 & 41) << 1);
            j = i5 % 128;
            int i6 = i5 % 2;
            return cVar;
        }

        public final b c() {
            int i = a;
            int i2 = ((i | Opcodes.LUSHR) << 1) - (i ^ Opcodes.LUSHR);
            j = i2 % 128;
            int i3 = i2 % 2;
            b bVar = this.b;
            int i4 = i + 37;
            j = i4 % 128;
            int i5 = i4 % 2;
            return bVar;
        }

        public final g e() {
            int i = a;
            int i2 = (i ^ 71) + ((i & 71) << 1);
            j = i2 % 128;
            boolean z = i2 % 2 != 0;
            g gVar = this.e;
            switch (z) {
                case false:
                    int i3 = 64 / 0;
                default:
                    return gVar;
            }
        }

        public final j a() {
            j jVar;
            int i = j;
            int i2 = (i & 81) + (i | 81);
            a = i2 % 128;
            switch (i2 % 2 == 0) {
                case false:
                    jVar = this.c;
                    int i3 = 0 / 0;
                    break;
                default:
                    jVar = this.c;
                    break;
            }
            int i4 = ((i | 39) << 1) - (i ^ 39);
            a = i4 % 128;
            switch (i4 % 2 == 0) {
                case true:
                    return jVar;
                default:
                    Object obj = null;
                    obj.hashCode();
                    throw null;
            }
        }

        public final d<?>[] d() {
            int i = a;
            int i2 = (i ^ 85) + ((i & 85) << 1);
            int i3 = i2 % 128;
            j = i3;
            int i4 = i2 % 2;
            d<?>[] dVarArr = {this.d, this.b, this.e, this.c};
            int i5 = (i3 + 24) - 1;
            a = i5 % 128;
            switch (i5 % 2 == 0) {
                case false:
                    throw null;
                default:
                    return dVarArr;
            }
        }

        public final void h() {
            int i = j + Opcodes.DNEG;
            a = i % 128;
            switch (i % 2 == 0) {
                case false:
                    this.d.b();
                    this.b.b();
                    this.e.b();
                    this.c.b();
                    int i2 = 46 / 0;
                    return;
                default:
                    this.d.b();
                    this.b.b();
                    this.e.b();
                    this.c.b();
                    return;
            }
        }
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:76:0x0291. Please report as an issue. */
    /* JADX WARN: Removed duplicated region for block: B:119:0x02c9 A[PHI: r3
  0x02c9: PHI (r3v9 int) = (r3v8 int), (r3v39 int) binds: [B:124:0x02bb, B:80:0x02c3] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:81:0x02c7 A[PHI: r3
  0x02c7: PHI (r3v36 int) = (r3v8 int), (r3v39 int) binds: [B:124:0x02bb, B:80:0x02c3] A[DONT_GENERATE, DONT_INLINE]] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void m(byte r22, int r23, short r24, int r25, int r26, java.lang.Object[] r27) {
        /*
            Method dump skipped, instructions count: 1056
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.em.a.m(byte, int, short, int, int, java.lang.Object[]):void");
    }

    private static void o(int i2, String str, char c2, String str2, String str3, Object[] objArr) {
        char[] cArr;
        char[] cArr2;
        int i3;
        char[] charArray = str3 != null ? str3.toCharArray() : str3;
        int i4 = 0;
        switch (str2 != null) {
            case false:
                cArr = str2;
                break;
            default:
                cArr = str2.toCharArray();
                break;
        }
        char[] cArr3 = cArr;
        switch (str != null ? (char) 1 : '_') {
            case Opcodes.SWAP /* 95 */:
                cArr2 = str;
                break;
            default:
                cArr2 = str.toCharArray();
                break;
        }
        o oVar = new o();
        int length = cArr3.length;
        char[] cArr4 = new char[length];
        int length2 = charArray.length;
        char[] cArr5 = new char[length2];
        System.arraycopy(cArr3, 0, cArr4, 0, length);
        System.arraycopy(charArray, 0, cArr5, 0, length2);
        cArr4[0] = (char) (cArr4[0] ^ c2);
        int i5 = 2;
        cArr5[2] = (char) (cArr5[2] + ((char) i2));
        int length3 = cArr2.length;
        char[] cArr6 = new char[length3];
        oVar.e = 0;
        int i6 = $10 + 59;
        $11 = i6 % 128;
        int i7 = i6 % 2;
        while (oVar.e < length3) {
            int i8 = $10 + 77;
            $11 = i8 % 128;
            int i9 = i8 % i5;
            try {
                Object[] objArr2 = {oVar};
                Object obj = o.e.a.s.get(-429442487);
                if (obj == null) {
                    Class cls = (Class) o.e.a.c(11 - (ViewConfiguration.getGlobalActionKeyTimeout() > 0L ? 1 : (ViewConfiguration.getGlobalActionKeyTimeout() == 0L ? 0 : -1)), (char) ((ViewConfiguration.getScrollDefaultDelay() >> 16) + 20954), 344 - Color.blue(i4));
                    byte b = (byte) i4;
                    byte b2 = b;
                    Object[] objArr3 = new Object[1];
                    p(b, b2, (byte) (b2 - 1), objArr3);
                    String str4 = (String) objArr3[i4];
                    Class<?>[] clsArr = new Class[1];
                    clsArr[i4] = Object.class;
                    obj = cls.getMethod(str4, clsArr);
                    o.e.a.s.put(-429442487, obj);
                }
                int intValue = ((Integer) ((Method) obj).invoke(null, objArr2)).intValue();
                try {
                    Object[] objArr4 = {oVar};
                    Object obj2 = o.e.a.s.get(-515165572);
                    if (obj2 == null) {
                        Class cls2 = (Class) o.e.a.c(KeyEvent.getDeadChar(i4, i4) + 10, (char) (ViewConfiguration.getMinimumFlingVelocity() >> 16), ((byte) KeyEvent.getModifierMetaStateMask()) + 208);
                        byte b3 = (byte) ($$b & 3);
                        byte b4 = (byte) (b3 - 2);
                        Object[] objArr5 = new Object[1];
                        p(b3, b4, (byte) (b4 - 1), objArr5);
                        String str5 = (String) objArr5[i4];
                        Class<?>[] clsArr2 = new Class[1];
                        clsArr2[i4] = Object.class;
                        obj2 = cls2.getMethod(str5, clsArr2);
                        o.e.a.s.put(-515165572, obj2);
                    }
                    int intValue2 = ((Integer) ((Method) obj2).invoke(null, objArr4)).intValue();
                    int i10 = cArr4[oVar.e % 4] * 32718;
                    try {
                        Object[] objArr6 = new Object[3];
                        objArr6[2] = Integer.valueOf(cArr5[intValue]);
                        objArr6[1] = Integer.valueOf(i10);
                        objArr6[i4] = oVar;
                        Object obj3 = o.e.a.s.get(-1614232674);
                        if (obj3 == null) {
                            Class cls3 = (Class) o.e.a.c((PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 11, (char) TextUtils.getOffsetBefore("", i4), TextUtils.lastIndexOf("", '0') + 282);
                            byte length4 = (byte) $$a.length;
                            byte b5 = (byte) (length4 - 4);
                            Object[] objArr7 = new Object[1];
                            p(length4, b5, (byte) (b5 - 1), objArr7);
                            obj3 = cls3.getMethod((String) objArr7[0], Object.class, Integer.TYPE, Integer.TYPE);
                            o.e.a.s.put(-1614232674, obj3);
                        }
                        ((Method) obj3).invoke(null, objArr6);
                        try {
                            Object[] objArr8 = {Integer.valueOf(cArr4[intValue2] * 32718), Integer.valueOf(cArr5[intValue])};
                            Object obj4 = o.e.a.s.get(406147795);
                            if (obj4 != null) {
                                i3 = 2;
                            } else {
                                Class cls4 = (Class) o.e.a.c(KeyEvent.getDeadChar(0, 0) + 19, (char) ((AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) + 14687), 113 - (SystemClock.uptimeMillis() > 0L ? 1 : (SystemClock.uptimeMillis() == 0L ? 0 : -1)));
                                byte b6 = (byte) 0;
                                Object[] objArr9 = new Object[1];
                                p((byte) 7, b6, (byte) (b6 - 1), objArr9);
                                i3 = 2;
                                obj4 = cls4.getMethod((String) objArr9[0], Integer.TYPE, Integer.TYPE);
                                o.e.a.s.put(406147795, obj4);
                            }
                            cArr5[intValue2] = ((Character) ((Method) obj4).invoke(null, objArr8)).charValue();
                            cArr4[intValue2] = oVar.d;
                            cArr6[oVar.e] = (char) ((((cArr4[intValue2] ^ r5[oVar.e]) ^ (i ^ 6565854932352255525L)) ^ ((int) (f ^ 6565854932352255525L))) ^ ((char) (j ^ 6565854932352255525L)));
                            oVar.e++;
                            i5 = i3;
                            i4 = 0;
                        } catch (Throwable th) {
                            Throwable cause = th.getCause();
                            if (cause == null) {
                                throw th;
                            }
                            throw cause;
                        }
                    } catch (Throwable th2) {
                        Throwable cause2 = th2.getCause();
                        if (cause2 == null) {
                            throw th2;
                        }
                        throw cause2;
                    }
                } catch (Throwable th3) {
                    Throwable cause3 = th3.getCause();
                    if (cause3 == null) {
                        throw th3;
                    }
                    throw cause3;
                }
            } catch (Throwable th4) {
                Throwable cause4 = th4.getCause();
                if (cause4 == null) {
                    throw th4;
                }
                throw cause4;
            }
        }
        objArr[0] = new String(cArr6);
    }
}
