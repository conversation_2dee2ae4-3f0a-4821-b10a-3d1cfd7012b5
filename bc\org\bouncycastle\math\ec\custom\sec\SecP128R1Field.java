package bc.org.bouncycastle.math.ec.custom.sec;

import com.vasco.digipass.sdk.utils.utilities.obfuscated.c6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.j6;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.n5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.s5;
import com.vasco.digipass.sdk.utils.utilities.obfuscated.w5;
import java.math.BigInteger;
import java.security.SecureRandom;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\bc\org\bouncycastle\math\ec\custom\sec\SecP128R1Field.smali */
public class SecP128R1Field {
    static final int[] a = {-1, -1, -1, -3};
    private static final int[] b = {1, 0, 0, 4, -2, -1, 3, -4};
    private static final int[] c = {-1, -1, -1, -5, 1, 0, -4, 3};

    private static void a(int[] iArr) {
        long j = (iArr[0] & 4294967295L) + 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            long j4 = (j3 >> 32) + (iArr[2] & 4294967295L);
            iArr[2] = (int) j4;
            j2 = j4 >> 32;
        }
        iArr[3] = (int) (j2 + (4294967295L & iArr[3]) + 2);
    }

    public static void add(int[] iArr, int[] iArr2, int[] iArr3) {
        if (s5.a(iArr, iArr2, iArr3) != 0 || ((iArr3[3] >>> 1) >= 2147483646 && s5.b(iArr3, a))) {
            a(iArr3);
        }
    }

    public static void addExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (w5.a(iArr, iArr2, iArr3) != 0 || ((iArr3[7] >>> 1) >= 2147483646 && w5.c(iArr3, b))) {
            int[] iArr4 = c;
            c6.a(iArr4.length, iArr4, iArr3);
        }
    }

    public static void addOne(int[] iArr, int[] iArr2) {
        if (c6.e(4, iArr, iArr2) != 0 || ((iArr2[3] >>> 1) >= 2147483646 && s5.b(iArr2, a))) {
            a(iArr2);
        }
    }

    private static void b(int[] iArr) {
        long j = (iArr[0] & 4294967295L) - 1;
        iArr[0] = (int) j;
        long j2 = j >> 32;
        if (j2 != 0) {
            long j3 = j2 + (iArr[1] & 4294967295L);
            iArr[1] = (int) j3;
            long j4 = (j3 >> 32) + (iArr[2] & 4294967295L);
            iArr[2] = (int) j4;
            j2 = j4 >> 32;
        }
        iArr[3] = (int) (j2 + ((4294967295L & iArr[3]) - 2));
    }

    public static int[] fromBigInteger(BigInteger bigInteger) {
        int[] a2 = s5.a(bigInteger);
        if ((a2[3] >>> 1) >= 2147483646) {
            int[] iArr = a;
            if (s5.b(a2, iArr)) {
                s5.d(iArr, a2);
            }
        }
        return a2;
    }

    public static void half(int[] iArr, int[] iArr2) {
        if ((iArr[0] & 1) == 0) {
            c6.a(4, iArr, 0, iArr2);
        } else {
            c6.d(4, iArr2, s5.a(iArr, a, iArr2));
        }
    }

    public static void inv(int[] iArr, int[] iArr2) {
        n5.a(a, iArr, iArr2);
    }

    public static int isZero(int[] iArr) {
        int i = 0;
        for (int i2 = 0; i2 < 4; i2++) {
            i |= iArr[i2];
        }
        return (((i >>> 1) | (i & 1)) - 1) >> 31;
    }

    public static void multiply(int[] iArr, int[] iArr2, int[] iArr3) {
        int[] c2 = s5.c();
        s5.c(iArr, iArr2, c2);
        reduce(c2, iArr3);
    }

    public static void multiplyAddToExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (s5.d(iArr, iArr2, iArr3) != 0 || ((iArr3[7] >>> 1) >= 2147483646 && w5.c(iArr3, b))) {
            int[] iArr4 = c;
            c6.a(iArr4.length, iArr4, iArr3);
        }
    }

    public static void negate(int[] iArr, int[] iArr2) {
        if (isZero(iArr) == 0) {
            s5.e(a, iArr, iArr2);
        } else {
            int[] iArr3 = a;
            s5.e(iArr3, iArr3, iArr2);
        }
    }

    public static void random(SecureRandom secureRandom, int[] iArr) {
        byte[] bArr = new byte[16];
        do {
            secureRandom.nextBytes(bArr);
            j6.a(bArr, 0, iArr, 0, 4);
        } while (c6.f(4, iArr, a) == 0);
    }

    public static void randomMult(SecureRandom secureRandom, int[] iArr) {
        do {
            random(secureRandom, iArr);
        } while (isZero(iArr) != 0);
    }

    public static void reduce(int[] iArr, int[] iArr2) {
        long j = iArr[7] & 4294967295L;
        long j2 = (iArr[3] & 4294967295L) + j;
        long j3 = (iArr[6] & 4294967295L) + (j << 1);
        long j4 = (iArr[2] & 4294967295L) + j3;
        long j5 = (iArr[5] & 4294967295L) + (j3 << 1);
        long j6 = (iArr[1] & 4294967295L) + j5;
        long j7 = (iArr[4] & 4294967295L) + (j5 << 1);
        long j8 = (iArr[0] & 4294967295L) + j7;
        iArr2[0] = (int) j8;
        long j9 = j6 + (j8 >>> 32);
        iArr2[1] = (int) j9;
        long j10 = j4 + (j9 >>> 32);
        iArr2[2] = (int) j10;
        long j11 = j2 + (j7 << 1) + (j10 >>> 32);
        iArr2[3] = (int) j11;
        reduce32((int) (j11 >>> 32), iArr2);
    }

    public static void reduce32(int i, int[] iArr) {
        while (i != 0) {
            long j = i & 4294967295L;
            long j2 = (iArr[0] & 4294967295L) + j;
            iArr[0] = (int) j2;
            long j3 = j2 >> 32;
            if (j3 != 0) {
                long j4 = j3 + (iArr[1] & 4294967295L);
                iArr[1] = (int) j4;
                long j5 = (j4 >> 32) + (iArr[2] & 4294967295L);
                iArr[2] = (int) j5;
                j3 = j5 >> 32;
            }
            long j6 = j3 + (4294967295L & iArr[3]) + (j << 1);
            iArr[3] = (int) j6;
            i = (int) (j6 >> 32);
        }
        if ((iArr[3] >>> 1) < 2147483646 || !s5.b(iArr, a)) {
            return;
        }
        a(iArr);
    }

    public static void square(int[] iArr, int[] iArr2) {
        int[] c2 = s5.c();
        s5.c(iArr, c2);
        reduce(c2, iArr2);
    }

    public static void squareN(int[] iArr, int i, int[] iArr2) {
        int[] c2 = s5.c();
        s5.c(iArr, c2);
        reduce(c2, iArr2);
        while (true) {
            i--;
            if (i <= 0) {
                return;
            }
            s5.c(iArr2, c2);
            reduce(c2, iArr2);
        }
    }

    public static void subtract(int[] iArr, int[] iArr2, int[] iArr3) {
        if (s5.e(iArr, iArr2, iArr3) != 0) {
            b(iArr3);
        }
    }

    public static void subtractExt(int[] iArr, int[] iArr2, int[] iArr3) {
        if (c6.d(10, iArr, iArr2, iArr3) != 0) {
            int[] iArr4 = c;
            c6.g(iArr4.length, iArr4, iArr3);
        }
    }

    public static void twice(int[] iArr, int[] iArr2) {
        if (c6.b(4, iArr, 0, iArr2) != 0 || ((iArr2[3] >>> 1) >= 2147483646 && s5.b(iArr2, a))) {
            a(iArr2);
        }
    }
}
