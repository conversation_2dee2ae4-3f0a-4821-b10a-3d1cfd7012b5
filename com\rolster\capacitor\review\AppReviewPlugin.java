package com.rolster.capacitor.review;

import android.content.Intent;
import android.net.Uri;
import androidx.core.app.NotificationCompat;
import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "AppReview")
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes2\com\rolster\capacitor\review\AppReviewPlugin.smali */
public class AppReviewPlugin extends Plugin implements AppReviewResolve {
    @PluginMethod
    public void request(PluginCall call) {
        AppReview.request(getActivity(), this);
        call.resolve();
    }

    @PluginMethod
    public void openStore(PluginCall call) {
        String packageName = getActivity().getPackageName();
        Intent intent = new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + packageName));
        intent.addFlags(1208483840);
        getActivity().startActivity(intent);
        call.resolve();
    }

    @Override // com.rolster.capacitor.review.AppReviewResolve
    public void onComplete(String status) {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, status);
        notifyListeners("appReviewEvent", result);
    }

    @Override // com.rolster.capacitor.review.AppReviewResolve
    public void onFailure(String status, String message) {
        JSObject result = new JSObject();
        result.put(NotificationCompat.CATEGORY_STATUS, status);
        result.put("msgError", message);
        notifyListeners("appReviewEvent", result);
    }
}
