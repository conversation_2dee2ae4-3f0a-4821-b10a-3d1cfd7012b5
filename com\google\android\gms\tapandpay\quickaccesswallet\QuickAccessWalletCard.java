package com.google.android.gms.tapandpay.quickaccesswallet;

import android.graphics.Bitmap;
import android.os.Parcel;
import android.os.Parcelable;
import com.google.android.gms.common.internal.Objects;
import com.google.android.gms.common.internal.safeparcel.AbstractSafeParcelable;
import com.google.android.gms.common.internal.safeparcel.SafeParcelWriter;
import java.util.Arrays;

/* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\QuickAccessWalletCard.smali */
public final class QuickAccessWalletCard extends AbstractSafeParcelable {
    public static final Parcelable.Creator<QuickAccessWalletCard> CREATOR = new zzf();
    private String zza;
    private Bitmap zzb;
    private String zzc;
    private WalletCardIntent[] zzd;
    private CardIconMessage[] zze;
    private long zzf;
    private long zzg;
    private String zzh;

    /* compiled from: com.google.android.gms:play-services-tapandpay@@18.3.3 */
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\android\gms\tapandpay\quickaccesswallet\QuickAccessWalletCard$Builder.smali */
    public static final class Builder {
        private final QuickAccessWalletCard zza;

        public Builder() {
            this.zza = new QuickAccessWalletCard(null);
        }

        public QuickAccessWalletCard build() {
            return this.zza;
        }

        public Builder setAvailableBalance(String availableBalance) {
            this.zza.zzh = availableBalance;
            return this;
        }

        public Builder setAvailableTimestamp(long availableTimestamp) {
            this.zza.zzf = availableTimestamp;
            return this;
        }

        public Builder setCardId(String cardId) {
            this.zza.zza = cardId;
            return this;
        }

        public Builder setCardImage(Bitmap cardImage) {
            this.zza.zzb = cardImage;
            return this;
        }

        public Builder setContentDescription(String contentDescription) {
            this.zza.zzc = contentDescription;
            return this;
        }

        public Builder setExpirationTimestamp(long expirationTimestamp) {
            this.zza.zzg = expirationTimestamp;
            return this;
        }

        public Builder setIconMessages(CardIconMessage[] iconMessages) {
            this.zza.zze = iconMessages;
            return this;
        }

        public Builder setIntents(WalletCardIntent[] intents) {
            this.zza.zzd = intents;
            return this;
        }

        public Builder(QuickAccessWalletCard origin) {
            QuickAccessWalletCard quickAccessWalletCard = new QuickAccessWalletCard(null);
            this.zza = quickAccessWalletCard;
            quickAccessWalletCard.zza = origin.zza;
            quickAccessWalletCard.zzb = origin.zzb;
            quickAccessWalletCard.zzc = origin.zzc;
            quickAccessWalletCard.zzd = origin.zzd;
            quickAccessWalletCard.zze = origin.zze;
            quickAccessWalletCard.zzf = origin.zzf;
            quickAccessWalletCard.zzg = origin.zzg;
            quickAccessWalletCard.zzh = origin.zzh;
        }
    }

    private QuickAccessWalletCard() {
    }

    public boolean equals(Object other) {
        if (this == other) {
            return true;
        }
        if (other instanceof QuickAccessWalletCard) {
            QuickAccessWalletCard quickAccessWalletCard = (QuickAccessWalletCard) other;
            if (Objects.equal(this.zza, quickAccessWalletCard.zza) && Objects.equal(this.zzb, quickAccessWalletCard.zzb) && Objects.equal(this.zzc, quickAccessWalletCard.zzc) && Arrays.equals(this.zzd, quickAccessWalletCard.zzd) && Arrays.equals(this.zze, quickAccessWalletCard.zze) && Objects.equal(Long.valueOf(this.zzf), Long.valueOf(quickAccessWalletCard.zzf)) && Objects.equal(Long.valueOf(this.zzg), Long.valueOf(quickAccessWalletCard.zzg)) && Objects.equal(this.zzh, quickAccessWalletCard.zzh)) {
                return true;
            }
        }
        return false;
    }

    public String getAvailableBalance() {
        return this.zzh;
    }

    public long getAvailableTimestamp() {
        return this.zzf;
    }

    public String getCardId() {
        return this.zza;
    }

    public Bitmap getCardImage() {
        return this.zzb;
    }

    public String getContentDescription() {
        return this.zzc;
    }

    public long getExpirationTimestamp() {
        return this.zzg;
    }

    public CardIconMessage[] getIconMessages() {
        return this.zze;
    }

    public WalletCardIntent[] getIntents() {
        return this.zzd;
    }

    public int hashCode() {
        return Objects.hashCode(this.zza, this.zzb, this.zzc, Integer.valueOf(Arrays.hashCode(this.zzd)), Integer.valueOf(Arrays.hashCode(this.zze)), Long.valueOf(this.zzf), Long.valueOf(this.zzg), this.zzh);
    }

    @Override // android.os.Parcelable
    public void writeToParcel(Parcel dest, int flags) {
        int beginObjectHeader = SafeParcelWriter.beginObjectHeader(dest);
        SafeParcelWriter.writeString(dest, 1, getCardId(), false);
        SafeParcelWriter.writeParcelable(dest, 2, getCardImage(), flags, false);
        SafeParcelWriter.writeString(dest, 3, getContentDescription(), false);
        SafeParcelWriter.writeTypedArray(dest, 4, getIntents(), flags, false);
        SafeParcelWriter.writeTypedArray(dest, 5, getIconMessages(), flags, false);
        SafeParcelWriter.writeLong(dest, 6, getAvailableTimestamp());
        SafeParcelWriter.writeLong(dest, 7, getExpirationTimestamp());
        SafeParcelWriter.writeString(dest, 8, getAvailableBalance(), false);
        SafeParcelWriter.finishObjectHeader(dest, beginObjectHeader);
    }

    /* synthetic */ QuickAccessWalletCard(zze zzeVar) {
    }

    QuickAccessWalletCard(String str, Bitmap bitmap, String str2, WalletCardIntent[] walletCardIntentArr, CardIconMessage[] cardIconMessageArr, long j, long j2, String str3) {
        this.zza = str;
        this.zzb = bitmap;
        this.zzc = str2;
        this.zzd = walletCardIntentArr;
        this.zze = cardIconMessageArr;
        this.zzf = j;
        this.zzg = j2;
        this.zzh = str3;
    }
}
