package o.o;

import android.os.CancellationSignal;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.util.HashMap;
import java.util.Map;
import kotlin.text.Typography;
import org.bouncycastle.crypto.agreement.jpake.JPAKEParticipant;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\a.smali */
final class a {
    private static int d = 0;
    private static int e = 1;
    private final Map<Long, b> c = new HashMap();

    a() {
    }

    final synchronized void a(c cVar, o.o.b bVar, CancellationSignal cancellationSignal) {
        this.c.put(Long.valueOf(cVar.e()), new b(cVar, bVar, cancellationSignal));
        int i = e;
        int i2 = ((i | 69) << 1) - (i ^ 69);
        d = i2 % 128;
        int i3 = i2 % 2;
    }

    final synchronized c e(long j) {
        int i = (e + Opcodes.FNEG) - 1;
        d = i % 128;
        int i2 = i % 2;
        b bVar = this.c.get(Long.valueOf(j));
        switch (bVar != null ? Typography.dollar : (char) 21) {
            case '$':
                int i3 = (d + 100) - 1;
                e = i3 % 128;
                int i4 = i3 % 2;
                c cVar = bVar.d;
                int i5 = (d + 94) - 1;
                e = i5 % 128;
                int i6 = i5 % 2;
                return cVar;
            default:
                int i7 = d;
                int i8 = ((i7 | Opcodes.LNEG) << 1) - (i7 ^ Opcodes.LNEG);
                e = i8 % 128;
                char c = i8 % 2 == 0 ? 'c' : '=';
                Object obj = null;
                switch (c) {
                    case Opcodes.DADD /* 99 */:
                        obj.hashCode();
                        throw null;
                    default:
                        return null;
                }
        }
    }

    final synchronized o.o.b b(long j) {
        o.o.b bVar;
        int i = e + Opcodes.LSHL;
        d = i % 128;
        int i2 = i % 2;
        b bVar2 = this.c.get(Long.valueOf(j));
        Object obj = null;
        switch (bVar2 != null) {
            case true:
                int i3 = e;
                int i4 = ((i3 | Opcodes.DNEG) << 1) - (i3 ^ Opcodes.DNEG);
                d = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 25 : (char) 15) {
                    case 15:
                        bVar = bVar2.e;
                        break;
                    default:
                        bVar = bVar2.e;
                        try {
                            int i5 = 8 / 0;
                            break;
                        } catch (Throwable th) {
                            throw th;
                        }
                }
                int i6 = d + Opcodes.LSHR;
                e = i6 % 128;
                switch (i6 % 2 == 0 ? 'F' : 'Z') {
                    case JPAKEParticipant.STATE_ROUND_3_VALIDATED /* 70 */:
                        try {
                            obj.hashCode();
                            throw null;
                        } catch (Throwable th2) {
                            throw th2;
                        }
                    default:
                        return bVar;
                }
            default:
                int i7 = d + 109;
                e = i7 % 128;
                int i8 = i7 % 2;
                return null;
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:16:0x0030 A[PHI: r5
  0x0030: PHI (r5v8 o.o.a$b) = (r5v4 o.o.a$b), (r5v15 o.o.a$b) binds: [B:54:0x002d, B:15:0x0045] A[DONT_GENERATE, DONT_INLINE]] */
    /* JADX WARN: Removed duplicated region for block: B:44:0x004a A[Catch: all -> 0x0098, TRY_ENTER, TRY_LEAVE, TryCatch #3 {, blocks: (B:3:0x0002, B:7:0x0014, B:8:0x0017, B:9:0x0031, B:17:0x0056, B:20:0x0068, B:21:0x006b, B:22:0x0076, B:42:0x0075, B:36:0x006e, B:44:0x004a, B:50:0x0097, B:51:0x001a, B:54:0x002d, B:12:0x003d), top: B:2:0x0002, inners: #2 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    final synchronized android.os.CancellationSignal c(long r5) {
        /*
            r4 = this;
            monitor-enter(r4)
            int r0 = o.o.a.e     // Catch: java.lang.Throwable -> L98
            int r0 = r0 + 51
            int r1 = r0 % 128
            o.o.a.d = r1     // Catch: java.lang.Throwable -> L98
            int r0 = r0 % 2
            r1 = 0
            r2 = 1
            if (r0 == 0) goto L12
            r0 = r1
            goto L13
        L12:
            r0 = r2
        L13:
            r3 = 0
            switch(r0) {
                case 1: goto L1a;
                default: goto L17;
            }     // Catch: java.lang.Throwable -> L98
        L17:
            java.util.Map<java.lang.Long, o.o.a$b> r0 = r4.c     // Catch: java.lang.Throwable -> L98
            goto L31
        L1a:
            java.util.Map<java.lang.Long, o.o.a$b> r0 = r4.c     // Catch: java.lang.Throwable -> L98
            java.lang.Long r5 = java.lang.Long.valueOf(r5)     // Catch: java.lang.Throwable -> L98
            java.lang.Object r5 = r0.get(r5)     // Catch: java.lang.Throwable -> L98
            o.o.a$b r5 = (o.o.a.b) r5     // Catch: java.lang.Throwable -> L98
            if (r5 == 0) goto L2b
            r6 = 16
            goto L2d
        L2b:
            r6 = 80
        L2d:
            switch(r6) {
                case 80: goto L49;
                default: goto L30;
            }     // Catch: java.lang.Throwable -> L98
        L30:
            goto L56
        L31:
            java.lang.Long r5 = java.lang.Long.valueOf(r5)     // Catch: java.lang.Throwable -> L98
            java.lang.Object r5 = r0.get(r5)     // Catch: java.lang.Throwable -> L98
            o.o.a$b r5 = (o.o.a.b) r5     // Catch: java.lang.Throwable -> L98
            r6 = 19
            int r6 = r6 / r1
            if (r5 == 0) goto L43
            r6 = 48
            goto L45
        L43:
            r6 = 62
        L45:
            switch(r6) {
                case 62: goto L49;
                default: goto L48;
            }
        L48:
            goto L30
        L49:
            int r5 = o.o.a.e     // Catch: java.lang.Throwable -> L98
            int r5 = r5 + 49
            int r6 = r5 % 128
            o.o.a.d = r6     // Catch: java.lang.Throwable -> L98
            int r5 = r5 % 2
            monitor-exit(r4)
            return r3
        L56:
            int r6 = o.o.a.e     // Catch: java.lang.Throwable -> L98
            r0 = r6 ^ 95
            r6 = r6 & 95
            int r6 = r6 << r2
            int r0 = r0 + r6
            int r6 = r0 % 128
            o.o.a.d = r6     // Catch: java.lang.Throwable -> L98
            int r0 = r0 % 2
            if (r0 == 0) goto L67
            goto L68
        L67:
            r1 = r2
        L68:
            switch(r1) {
                case 0: goto L6e;
                default: goto L6b;
            }     // Catch: java.lang.Throwable -> L98
        L6b:
            android.os.CancellationSignal r5 = r5.b     // Catch: java.lang.Throwable -> L98
            goto L76
        L6e:
            android.os.CancellationSignal r5 = r5.b     // Catch: java.lang.Throwable -> L98
            r3.hashCode()     // Catch: java.lang.Throwable -> L74
            throw r3     // Catch: java.lang.Throwable -> L74
        L74:
            r5 = move-exception
        L75:
            throw r5     // Catch: java.lang.Throwable -> L98
        L76:
            int r6 = o.o.a.d     // Catch: java.lang.Throwable -> L98
            r0 = r6 | 117(0x75, float:1.64E-43)
            int r0 = r0 << r2
            r6 = r6 ^ 117(0x75, float:1.64E-43)
            int r0 = r0 - r6
            int r6 = r0 % 128
            o.o.a.e = r6     // Catch: java.lang.Throwable -> L98
            int r0 = r0 % 2
            if (r0 != 0) goto L89
            r6 = 41
            goto L8b
        L89:
            r6 = 92
        L8b:
            switch(r6) {
                case 41: goto L90;
                default: goto L8e;
            }
        L8e:
            monitor-exit(r4)
            return r5
        L90:
            r3.hashCode()     // Catch: java.lang.Throwable -> L94
            throw r3     // Catch: java.lang.Throwable -> L94
        L94:
            r5 = move-exception
            goto L75
        L96:
            r5 = move-exception
            throw r5     // Catch: java.lang.Throwable -> L98
        L98:
            r5 = move-exception
            monitor-exit(r4)
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: o.o.a.c(long):android.os.CancellationSignal");
    }

    final synchronized b a(long j) {
        b remove;
        int i = d + 41;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                remove = this.c.remove(Long.valueOf(j));
                int i2 = e;
                int i3 = (i2 & 15) + (i2 | 15);
                d = i3 % 128;
                switch (i3 % 2 != 0 ? '!' : 'A') {
                    case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                        break;
                    default:
                        try {
                            throw null;
                        } catch (Throwable th) {
                            throw th;
                        }
                }
            default:
                this.c.remove(Long.valueOf(j));
                try {
                    throw null;
                } catch (Throwable th2) {
                    throw th2;
                }
        }
        return remove;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\o\a$b.smali */
    static final class b {
        private static int a = 0;
        private static int c = 1;
        final CancellationSignal b;
        final c d;
        final o.o.b e;

        b(c cVar, o.o.b bVar, CancellationSignal cancellationSignal) {
            this.d = cVar;
            this.e = bVar;
            this.b = cancellationSignal;
        }

        public final c b() {
            int i = a;
            int i2 = (i ^ 59) + ((i & 59) << 1);
            c = i2 % 128;
            int i3 = i2 % 2;
            c cVar = this.d;
            int i4 = (i + 38) - 1;
            c = i4 % 128;
            int i5 = i4 % 2;
            return cVar;
        }

        public final o.o.b c() {
            int i = a;
            int i2 = ((i | 13) << 1) - (i ^ 13);
            int i3 = i2 % 128;
            c = i3;
            int i4 = i2 % 2;
            o.o.b bVar = this.e;
            int i5 = i3 + Opcodes.LUSHR;
            a = i5 % 128;
            switch (i5 % 2 != 0 ? '\n' : (char) 18) {
                case '\n':
                    throw null;
                default:
                    return bVar;
            }
        }
    }
}
