package fr.antelop.sdk.digitalcard;

import fr.antelop.sdk.exception.WalletValidationException;
import o.er.n;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\PinDisplayService.smali */
public final class PinDisplayService {
    private final n innerPinDisplayService;

    public PinDisplayService(n nVar) {
        this.innerPinDisplayService = nVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerPinDisplayService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final SecurePinDisplay getSecurePinDisplay() throws WalletValidationException {
        return new SecurePinDisplay(this.innerPinDisplayService.a());
    }
}
