package fr.antelop.sdk.digitalcard;

import android.content.Context;
import fr.antelop.sdk.exception.WalletValidationException;
import fr.antelop.sdk.util.OperationCallback;
import java.util.List;
import o.er.r;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\digitalcard\VirtualCardNumberService.smali */
public final class VirtualCardNumberService {
    private final r innerPinDisplayService;

    public VirtualCardNumberService(r rVar) {
        this.innerPinDisplayService = rVar;
    }

    public final DigitalCardServiceStatus getStatus() {
        if (this.innerPinDisplayService.b()) {
            return DigitalCardServiceStatus.Active;
        }
        return DigitalCardServiceStatus.Disabled;
    }

    public final void getVirtualCardNumbers(Context context, OperationCallback<List<VirtualCardNumber>> operationCallback) throws WalletValidationException {
        this.innerPinDisplayService.c(context, operationCallback);
    }

    public final VirtualCardNumberGenerator getVirtualCardNumberGenerator() {
        return new VirtualCardNumberGenerator(this.innerPinDisplayService.e());
    }
}
