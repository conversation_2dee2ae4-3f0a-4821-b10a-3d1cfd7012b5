package org.bouncycastle;

import org.bouncycastle.util.Strings;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\org\bouncycastle\LICENSE.smali */
public class LICENSE {
    public static final String licenseText = "Copyright (c) 2000-2021 The Legion of the Bouncy Castle Inc. (https://www.bouncycastle.org) " + Strings.lineSeparator() + Strings.lineSeparator() + "Permission is hereby granted, free of charge, to any person obtaining a copy of this software " + Strings.lineSeparator() + "and associated documentation files (the \"Software\"), to deal in the Software without restriction, " + Strings.lineSeparator() + "including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, " + Strings.lineSeparator() + "and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so," + Strings.lineSeparator() + "subject to the following conditions:" + Strings.lineSeparator() + Strings.lineSeparator() + "The above copyright notice and this permission notice shall be included in all copies or substantial" + Strings.lineSeparator() + "portions of the Software." + Strings.lineSeparator() + Strings.lineSeparator() + "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED," + Strings.lineSeparator() + "INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR" + Strings.lineSeparator() + "PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE" + Strings.lineSeparator() + "LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR" + Strings.lineSeparator() + "OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER" + Strings.lineSeparator() + "DEALINGS IN THE SOFTWARE.";

    public static void main(String[] strArr) {
        System.out.println(licenseText);
    }
}
