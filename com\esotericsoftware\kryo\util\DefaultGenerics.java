package com.esotericsoftware.kryo.util;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.util.Generics;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\util\DefaultGenerics.smali */
public final class DefaultGenerics implements Generics {
    private int argumentsSize;
    private int genericTypesSize;
    private final Kryo kryo;
    private Generics.GenericType[] genericTypes = new Generics.GenericType[16];
    private int[] depths = new int[16];
    private Type[] arguments = new Type[16];

    public DefaultGenerics(Kryo kryo) {
        this.kryo = kryo;
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public void pushGenericType(Generics.GenericType fieldType) {
        int size = this.genericTypesSize;
        int i = size + 1;
        Generics.GenericType[] genericTypeArr = this.genericTypes;
        if (i == genericTypeArr.length) {
            Generics.GenericType[] genericTypesNew = new Generics.GenericType[genericTypeArr.length << 1];
            System.arraycopy(genericTypeArr, 0, genericTypesNew, 0, size);
            this.genericTypes = genericTypesNew;
            int[] iArr = this.depths;
            int[] depthsNew = new int[iArr.length << 1];
            System.arraycopy(iArr, 0, depthsNew, 0, size);
            this.depths = depthsNew;
        }
        this.genericTypesSize = size + 1;
        this.genericTypes[size] = fieldType;
        this.depths[size] = this.kryo.getDepth();
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public void popGenericType() {
        int size = this.genericTypesSize;
        if (size == 0) {
            return;
        }
        int size2 = size - 1;
        if (this.depths[size2] < this.kryo.getDepth()) {
            return;
        }
        this.genericTypes[size2] = null;
        this.genericTypesSize = size2;
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public Generics.GenericType[] nextGenericTypes() {
        int index = this.genericTypesSize;
        if (index > 0) {
            int index2 = index - 1;
            Generics.GenericType genericType = this.genericTypes[index2];
            if (genericType.arguments != null && this.depths[index2] == this.kryo.getDepth() - 1) {
                pushGenericType(genericType.arguments[genericType.arguments.length - 1]);
                return genericType.arguments;
            }
        }
        return null;
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public Class nextGenericClass() {
        Generics.GenericType[] arguments = nextGenericTypes();
        if (arguments == null) {
            return null;
        }
        return arguments[0].resolve(this);
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public int pushTypeVariables(Generics.GenericsHierarchy hierarchy, Generics.GenericType[] args) {
        if (hierarchy.total == 0 || hierarchy.rootTotal > args.length) {
            return 0;
        }
        int startSize = this.argumentsSize;
        int sizeNeeded = hierarchy.total + startSize;
        Type[] typeArr = this.arguments;
        if (sizeNeeded > typeArr.length) {
            Type[] newArray = new Type[Math.max(sizeNeeded, typeArr.length << 1)];
            System.arraycopy(this.arguments, 0, newArray, 0, startSize);
            this.arguments = newArray;
        }
        int[] counts = hierarchy.counts;
        TypeVariable[] params = hierarchy.parameters;
        int p = 0;
        int n = args.length;
        for (int i = 0; i < n; i++) {
            Generics.GenericType arg = args[i];
            Class resolved = arg.resolve(this);
            if (resolved != null) {
                int count = counts[i];
                if (arg == null) {
                    p += count;
                } else {
                    int nn = p + count;
                    while (p < nn) {
                        Type[] typeArr2 = this.arguments;
                        int i2 = this.argumentsSize;
                        typeArr2[i2] = params[p];
                        typeArr2[i2 + 1] = resolved;
                        this.argumentsSize = i2 + 2;
                        p++;
                    }
                }
            }
        }
        int i3 = this.argumentsSize;
        return i3 - startSize;
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public void popTypeVariables(int count) {
        int n = this.argumentsSize;
        int i = n - count;
        this.argumentsSize = i;
        while (i < n) {
            this.arguments[i] = null;
            i++;
        }
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public Class resolveTypeVariable(TypeVariable typeVariable) {
        for (int i = this.argumentsSize - 2; i >= 0; i -= 2) {
            Type arg = this.arguments[i];
            if (arg == typeVariable || arg.equals(typeVariable)) {
                return (Class) this.arguments[i + 1];
            }
        }
        return null;
    }

    @Override // com.esotericsoftware.kryo.util.Generics
    public int getGenericTypesSize() {
        return this.genericTypesSize;
    }

    public String toString() {
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < this.argumentsSize; i += 2) {
            if (i != 0) {
                buffer.append(", ");
            }
            buffer.append(((TypeVariable) this.arguments[i]).getName());
            buffer.append("=");
            buffer.append(((Class) this.arguments[i + 1]).getSimpleName());
        }
        return buffer.toString();
    }
}
