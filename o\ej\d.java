package o.ej;

import android.graphics.Color;
import android.graphics.ImageFormat;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.AndroidCharacter;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import com.google.android.gms.fido.u2f.api.common.RegisterRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import kotlin.text.Typography;
import o.ee.g;
import o.ee.j;
import org.bouncycastle.crypto.signers.PSSSigner;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\ej\d.smali */
public final class d {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int f;
    private static long g;
    private static int h;
    private static char[] i;
    private static int k;
    private static int l;
    private static short[] m;
    private static int n;

    /* renamed from: o, reason: collision with root package name */
    private static byte[] f65o;
    private int a = -1;
    private int c = -1;
    private boolean e = false;
    private byte[] d = null;
    private byte[] b = null;
    private int j = 0;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        l = 0;
        k = 1;
        c();
        Process.myPid();
        ImageFormat.getBitsPerPixel(0);
        View.MeasureSpec.getMode(0);
        Color.green(0);
        ImageFormat.getBitsPerPixel(0);
        int i2 = k + 83;
        l = i2 % 128;
        switch (i2 % 2 != 0) {
            case false:
                break;
            default:
                int i3 = 23 / 0;
                break;
        }
    }

    static void c() {
        i = new char[]{11403, 57434, 46391, 18975, 8157, 11441, 11397, 57466, 46347, 18988, 8165, 11407, 57837, 46454, 18955, 7997, 11514, 57737, 46817, 18989, 8029, 11500, 57391, 4141};
        g = 7864220965887729695L;
        f65o = new byte[]{7, 118, -118, 40, 100, -99, 105, -103, -119, 2, -112, 103, -105, 2, -74, -110, -101, 8, -109, 101, -113, 79, -102, -120, 114, -99, -77, 58, 111, -106, 101, -108, -101, -110, 123, -93, 102, -106, -71, 99, -99, 103, -111, 83, -111, 101, -100, 101, -71, 95, -102, -120, 114, -99, -109, 62, 111, -106, 101, -108, -101, -110, -101, -125, 102, -106, -71, 99, -99, 103, -111, 83, -111, 101, PSSSigner.TRAILER_IMPLICIT, 95, -104, 103, -110, -98, -125, 126, 102, -103, 101, -112, -97};
        h = 909053666;
        n = 1510889666;
        f = 382454985;
    }

    static void init$0() {
        $$a = new byte[]{73, 49, 124, 25};
        $$b = Opcodes.FMUL;
    }

    private static void r(byte b, byte b2, int i2, Object[] objArr) {
        int i3 = 4 - (i2 * 4);
        byte[] bArr = $$a;
        int i4 = 110 - b2;
        int i5 = (b * 4) + 1;
        byte[] bArr2 = new byte[i5];
        int i6 = -1;
        int i7 = i5 - 1;
        if (bArr == null) {
            i3++;
            i4 = i7 + (-i4);
            i7 = i7;
        }
        while (true) {
            i6++;
            bArr2[i6] = (byte) i4;
            if (i6 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i8 = i4;
            int i9 = i7;
            i3++;
            i4 = i8 + (-bArr[i3]);
            i7 = i9;
        }
    }

    d() {
    }

    private static ArrayList<d> a(byte[] bArr) {
        int i2;
        int i3;
        switch (bArr != null) {
            case true:
                if (bArr.length == 0) {
                    return null;
                }
                ArrayList<d> arrayList = new ArrayList<>();
                int i4 = 0;
                while (i4 < bArr.length) {
                    d dVar = new d();
                    int i5 = i4 + 1;
                    int i6 = bArr[i4] & 255;
                    dVar.a = i6;
                    dVar.e = (i6 & 32) == 32;
                    while (true) {
                        int i7 = dVar.a;
                        if ((i7 & 31) == 31 && i5 < bArr.length) {
                            dVar.a = (bArr[i5] & 255) | (i7 << 8);
                            i5++;
                        }
                    }
                    if (i5 < bArr.length) {
                        int i8 = k + 39;
                        l = i8 % 128;
                        int i9 = i8 % 2;
                        dVar.c = bArr[i5] & 255;
                        i5++;
                    }
                    int i10 = dVar.c;
                    if (i10 != -1) {
                        int i11 = k + 93;
                        int i12 = i11 % 128;
                        l = i12;
                        int i13 = i11 % 2;
                        switch ((i10 & 128) != 128) {
                            case true:
                                break;
                            default:
                                int i14 = i12 + 19;
                                int i15 = i14 % 128;
                                k = i15;
                                int i16 = i14 % 2;
                                switch ((i10 & Opcodes.LXOR) != 129 ? '\\' : ' ') {
                                    case ' ':
                                        if (i5 < bArr.length) {
                                            int i17 = i15 + 41;
                                            l = i17 % 128;
                                            if (i17 % 2 != 0) {
                                                i2 = i5 + Opcodes.LSHL;
                                                i3 = bArr[i5] & 29198;
                                            } else {
                                                i2 = i5 + 1;
                                                i3 = bArr[i5] & 255;
                                            }
                                            dVar.c = i3;
                                            i5 = i2;
                                            break;
                                        }
                                    default:
                                        if ((i10 & Opcodes.LXOR) == 130) {
                                            int i18 = i12 + 41;
                                            k = i18 % 128;
                                            int i19 = i18 % 2;
                                            switch (i5 >= bArr.length - 1) {
                                                case false:
                                                    int i20 = i5 + 1;
                                                    dVar.c = ((bArr[i5] & 255) << 8) | (bArr[i20] & 255);
                                                    i5 = i20 + 1;
                                                    break;
                                            }
                                        }
                                        if ((i10 & Opcodes.LXOR) == 131) {
                                            int i21 = k + 3;
                                            int i22 = i21 % 128;
                                            l = i22;
                                            if (i21 % 2 == 0 ? i5 < bArr.length - 2 : i5 < (bArr.length >> 4)) {
                                                int i23 = i22 + 41;
                                                k = i23 % 128;
                                                int i24 = i23 % 2;
                                                int i25 = i5 + 1;
                                                int i26 = i25 + 1;
                                                dVar.c = ((bArr[i5] & 255) << 16) | ((bArr[i25] & 255) << 8) | (bArr[i26] & 255);
                                                i5 = i26 + 1;
                                                break;
                                            }
                                        }
                                        dVar.c = -1;
                                        g.c();
                                        Object[] objArr = new Object[1];
                                        p((char) ((AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) - 1), (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1)) - 1, View.combineMeasuredStates(0, 0) + 6, objArr);
                                        String intern = ((String) objArr[0]).intern();
                                        StringBuilder sb = new StringBuilder();
                                        Object[] objArr2 = new Object[1];
                                        p((char) View.combineMeasuredStates(0, 0), (ViewConfiguration.getTouchSlop() >> 8) + 6, (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)) + 14, objArr2);
                                        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(Integer.toHexString(dVar.c)).toString());
                                        break;
                                }
                                g.c();
                                Object[] objArr3 = new Object[1];
                                p((char) TextUtils.indexOf("", ""), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (ViewConfiguration.getDoubleTapTimeout() >> 16) + 6, objArr3);
                                String intern2 = ((String) objArr3[0]).intern();
                                StringBuilder sb2 = new StringBuilder();
                                Object[] objArr4 = new Object[1];
                                q((byte) TextUtils.getCapsMode("", 0, 0), (ViewConfiguration.getKeyRepeatTimeout() >> 16) - 551862873, (short) (KeyEvent.getMaxKeyCode() >> 16), (ViewConfiguration.getScrollBarSize() >> 8) - 115, (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) - 1814124037, objArr4);
                                g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(dVar.c).toString());
                                break;
                        }
                    }
                    int i27 = dVar.c;
                    switch (i27 != -1) {
                        default:
                            if (i27 != 0) {
                                switch (i27 > bArr.length - i5) {
                                    case true:
                                        break;
                                    default:
                                        int i28 = l + Opcodes.DSUB;
                                        k = i28 % 128;
                                        int i29 = i28 % 2;
                                        byte[] bArr2 = new byte[i27];
                                        dVar.b = bArr2;
                                        dVar.d = new byte[(i27 + i5) - i4];
                                        System.arraycopy(bArr, i5, bArr2, 0, i27);
                                        System.arraycopy(bArr, i4, dVar.d, 0, (dVar.c + i5) - i4);
                                        i5 += dVar.c;
                                        break;
                                }
                            }
                        case false:
                            if (i27 <= bArr.length - i5) {
                                dVar.b = null;
                                byte[] bArr3 = new byte[bArr.length - i4];
                                dVar.d = bArr3;
                                System.arraycopy(bArr, i4, bArr3, 0, bArr.length - i4);
                                break;
                            } else {
                                dVar.b = null;
                                byte[] bArr4 = new byte[bArr.length - i4];
                                dVar.d = bArr4;
                                System.arraycopy(bArr, i4, bArr4, 0, bArr.length - i4);
                                i5 += dVar.c;
                                break;
                            }
                    }
                    i4 = i5;
                    arrayList.add(dVar);
                }
                return arrayList;
            default:
                return null;
        }
    }

    public static ArrayList<d> b(byte[] bArr) {
        int i2;
        int i3 = l + 29;
        k = i3 % 128;
        switch (i3 % 2 == 0 ? '3' : (char) 20) {
            case '3':
                i2 = 1;
                break;
            default:
                i2 = 0;
                break;
        }
        return a(bArr, i2, Integer.MAX_VALUE);
    }

    public static ArrayList<d> c(byte[] bArr, int i2) {
        int i3 = l + Opcodes.DDIV;
        k = i3 % 128;
        int i4 = i3 % 2;
        ArrayList<d> a = a(bArr, 0, i2);
        int i5 = l + 45;
        k = i5 % 128;
        int i6 = i5 % 2;
        return a;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static o.ej.d a(java.lang.String r3, java.util.List<o.ej.d> r4) {
        /*
            java.util.Iterator r4 = r4.iterator()
            int r0 = o.ej.d.k
            int r0 = r0 + 99
            int r1 = r0 % 128
            o.ej.d.l = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L13
            r0 = 0
            goto L14
        L13:
            r0 = 1
        L14:
            switch(r0) {
                case 0: goto L17;
                default: goto L18;
            }
        L17:
        L18:
            boolean r0 = r4.hasNext()
            if (r0 == 0) goto L21
            r0 = 71
            goto L23
        L21:
            r0 = 82
        L23:
            r1 = 0
            switch(r0) {
                case 71: goto L28;
                default: goto L27;
            }
        L27:
            return r1
        L28:
            int r0 = o.ej.d.l
            int r0 = r0 + 51
            int r2 = r0 % 128
            o.ej.d.k = r2
            int r0 = r0 % 2
            if (r0 == 0) goto L4f
            java.lang.Object r0 = r4.next()
            o.ej.d r0 = (o.ej.d) r0
            java.lang.String r1 = r0.a()
            boolean r1 = r1.equals(r3)
            if (r1 == 0) goto L46
            r1 = 7
            goto L48
        L46:
            r1 = 57
        L48:
            switch(r1) {
                case 7: goto L4c;
                default: goto L4b;
            }
        L4b:
            goto L4e
        L4c:
            return r0
        L4e:
            goto L18
        L4f:
            java.lang.Object r4 = r4.next()
            o.ej.d r4 = (o.ej.d) r4
            java.lang.String r4 = r4.a()
            r4.equals(r3)
            r1.hashCode()     // Catch: java.lang.Throwable -> L60
            throw r1     // Catch: java.lang.Throwable -> L60
        L60:
            r3 = move-exception
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.d.a(java.lang.String, java.util.List):o.ej.d");
    }

    private static ArrayList<d> a(byte[] bArr, int i2, int i3) {
        ArrayList<d> arrayList = new ArrayList<>();
        ArrayList<d> a = a(bArr);
        switch (a == null) {
            case true:
                return arrayList;
        }
        for (int i4 = 0; i4 < a.size(); i4++) {
            a.get(i4).j = i2;
            switch (a.get(i4).b != null ? 'U' : 'O') {
                case Opcodes.IASTORE /* 79 */:
                    arrayList.add(a.get(i4));
                    break;
                default:
                    int i5 = l + 15;
                    k = i5 % 128;
                    if (i5 % 2 == 0) {
                    }
                    if (a.get(i4).e) {
                        arrayList.add(a.get(i4));
                        i3--;
                        if (i3 > 0) {
                            arrayList.addAll(a(a.get(i4).e(), i2 + 1, i3));
                            break;
                        } else {
                            break;
                        }
                    } else {
                        arrayList.add(a.get(i4));
                        break;
                    }
            }
        }
        int i6 = l + 71;
        k = i6 % 128;
        int i7 = i6 % 2;
        return arrayList;
    }

    public final String a() {
        int i2 = k + Opcodes.LNEG;
        l = i2 % 128;
        int i3 = i2 % 2;
        String upperCase = Integer.toHexString(this.a).toUpperCase(j.a());
        int i4 = l + Opcodes.DNEG;
        k = i4 % 128;
        switch (i4 % 2 == 0 ? ')' : (char) 18) {
            case ')':
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return upperCase;
        }
    }

    public final String d() {
        int i2 = k;
        int i3 = i2 + 99;
        l = i3 % 128;
        int i4 = i3 % 2;
        byte[] bArr = this.b;
        switch (bArr != null) {
            case false:
                Object[] objArr = new Object[1];
                q((byte) TextUtils.getTrimmedLength(""), (-551862864) + (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), (short) ExpandableListView.getPackedPositionGroup(0L), (-115) - KeyEvent.keyCodeFromString(""), (-1814124004) - TextUtils.indexOf("", ""), objArr);
                return ((String) objArr[0]).intern();
            default:
                int i5 = i2 + 27;
                l = i5 % 128;
                switch (i5 % 2 != 0) {
                    case false:
                        return c(bArr);
                    default:
                        c(bArr);
                        throw null;
                }
        }
    }

    public final byte[] e() {
        int i2 = k + 41;
        int i3 = i2 % 128;
        l = i3;
        int i4 = i2 % 2;
        byte[] bArr = this.b;
        int i5 = i3 + 17;
        k = i5 % 128;
        int i6 = i5 % 2;
        return bArr;
    }

    public final byte[] b() {
        int i2 = l;
        int i3 = i2 + 45;
        k = i3 % 128;
        int i4 = i3 % 2;
        byte[] bArr = this.d;
        int i5 = i2 + 81;
        k = i5 % 128;
        switch (i5 % 2 == 0) {
            case false:
                return bArr;
            default:
                throw null;
        }
    }

    private static String c(byte[] bArr) {
        BigInteger bigInteger = new BigInteger(1, bArr);
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        p((char) (ViewConfiguration.getKeyRepeatTimeout() >> 16), ExpandableListView.getPackedPositionGroup(0L) + 21, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 1, objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(bArr.length << 1);
        Object[] objArr2 = new Object[1];
        p((char) (Color.red(0) + 15548), 22 - TextUtils.indexOf((CharSequence) "", '0'), (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
        String format = String.format(append.append(((String) objArr2[0]).intern()).toString(), bigInteger);
        int i2 = k + 23;
        l = i2 % 128;
        int i3 = i2 % 2;
        return format;
    }

    public static LinkedHashMap<String, Integer> d(byte[] bArr) {
        int i2;
        int i3;
        if (bArr.length == 0) {
            return new LinkedHashMap<>();
        }
        LinkedHashMap<String, Integer> linkedHashMap = new LinkedHashMap<>();
        int i4 = 0;
        while (i4 < bArr.length) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byteArrayOutputStream.write(bArr[i4]);
            int i5 = i4 + 1;
            int i6 = bArr[i4] & 255;
            if ((i6 & 31) == 31) {
                switch (i5 < bArr.length) {
                    case false:
                        break;
                    default:
                        while (true) {
                            byteArrayOutputStream.write(bArr[i5]);
                            i3 = i5 + 1;
                            i6 = (i6 << 8) | (bArr[i5] & 255);
                            switch ((i6 & 128) != 128) {
                                case true:
                                    break;
                                default:
                                    int i7 = k + 59;
                                    l = i7 % 128;
                                    switch (i7 % 2 != 0 ? 'a' : 'P') {
                                        case Opcodes.LADD /* 97 */:
                                            int length = bArr.length;
                                            throw null;
                                        default:
                                            if (i3 >= bArr.length) {
                                                break;
                                            } else {
                                                i5 = i3;
                                            }
                                    }
                            }
                        }
                        i5 = i3;
                        break;
                }
            }
            if (i5 < bArr.length) {
                int i8 = k + Opcodes.DMUL;
                l = i8 % 128;
                switch (i8 % 2 != 0) {
                    case true:
                        i4 = i5 + 71;
                        i2 = bArr[i5] & 1530;
                        break;
                    default:
                        i4 = i5 + 1;
                        i2 = bArr[i5] & 255;
                        break;
                }
            } else {
                i4 = i5;
                i2 = 0;
            }
            linkedHashMap.put(o.dk.b.e(byteArrayOutputStream.toByteArray()), Integer.valueOf(i2));
        }
        return linkedHashMap;
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static byte[] a(java.util.LinkedHashMap<java.lang.String, java.lang.Integer> r11) {
        /*
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            r0.<init>()
            java.util.Set r11 = r11.entrySet()
            java.util.Iterator r11 = r11.iterator()
        Le:
            boolean r1 = r11.hasNext()
            if (r1 == 0) goto L17
            r1 = 28
            goto L19
        L17:
            r1 = 55
        L19:
            switch(r1) {
                case 55: goto L29;
                default: goto L1c;
            }
        L1c:
            int r1 = o.ej.d.k
            int r1 = r1 + 75
            int r2 = r1 % 128
            o.ej.d.l = r2
            int r1 = r1 % 2
            if (r1 == 0) goto L32
            goto L32
        L29:
            java.lang.String r11 = r0.toString()
            byte[] r11 = o.dk.b.b(r11)
            return r11
        L32:
            java.lang.Object r1 = r11.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r2 = r1.getKey()
            java.lang.String r2 = (java.lang.String) r2
            r0.append(r2)
            r2 = 0
            int r3 = android.view.KeyEvent.getDeadChar(r2, r2)
            byte r4 = (byte) r3
            int r3 = android.view.ViewConfiguration.getLongPressTimeout()
            int r3 = r3 >> 16
            r5 = -551862860(0xffffffffdf1b3db4, float:-1.1186295E19)
            int r5 = r5 + r3
            java.lang.String r3 = ""
            int r3 = android.text.TextUtils.getOffsetBefore(r3, r2)
            short r6 = (short) r3
            int r3 = android.view.ViewConfiguration.getMaximumFlingVelocity()
            int r3 = r3 >> 16
            int r7 = r3 + (-115)
            int r3 = android.view.ViewConfiguration.getJumpTapTimeout()
            int r3 = r3 >> 16
            r8 = -1814124077(0xffffffff93dea9d3, float:-5.620814E-27)
            int r8 = r8 - r3
            r3 = 1
            java.lang.Object[] r10 = new java.lang.Object[r3]
            r9 = r10
            q(r4, r5, r6, r7, r8, r9)
            r4 = r10[r2]
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = r4.intern()
            java.lang.Object r1 = r1.getValue()
            java.lang.Integer r1 = (java.lang.Integer) r1
            byte r1 = r1.byteValue()
            java.lang.Byte r1 = java.lang.Byte.valueOf(r1)
            java.lang.Object[] r1 = new java.lang.Object[]{r1}
            java.lang.String r1 = java.lang.String.format(r4, r1)
            r0.append(r1)
            int r1 = o.ej.d.l
            int r1 = r1 + 59
            int r4 = r1 % 128
            o.ej.d.k = r4
            int r1 = r1 % 2
            if (r1 != 0) goto L9f
            r2 = r3
        L9f:
            switch(r2) {
                case 0: goto La2;
                default: goto La2;
            }
        La2:
            goto Le
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.d.a(java.util.LinkedHashMap):byte[]");
    }

    public static byte[] e(String str, byte[]... bArr) {
        int i2 = k + 91;
        l = i2 % 128;
        char c = i2 % 2 != 0 ? (char) 14 : '1';
        byte[] b = o.dk.b.b(str);
        switch (c) {
            case 14:
                byte[] d = d(b, bArr);
                int i3 = 86 / 0;
                return d;
            default:
                return d(b, bArr);
        }
    }

    public static byte[] d(byte[] bArr, byte[]... bArr2) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ByteArrayOutputStream byteArrayOutputStream2 = new ByteArrayOutputStream();
        if (bArr2 != null) {
            try {
                int length = bArr2.length;
                int i2 = 0;
                while (true) {
                    switch (i2 < length ? (char) 2 : 'Y') {
                        case Opcodes.DUP /* 89 */:
                            break;
                        default:
                            byte[] bArr3 = bArr2[i2];
                            if (bArr3 != null) {
                                byteArrayOutputStream2.write(bArr3);
                            }
                            i2++;
                    }
                }
            } catch (IOException e) {
                g.c();
                Object[] objArr = new Object[1];
                q((byte) (ImageFormat.getBitsPerPixel(0) + 1), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 551862856, (short) View.MeasureSpec.getMode(0), View.getDefaultSize(0, 0) - 115, ExpandableListView.getPackedPositionChild(0L) - 1814124047, objArr);
                String intern = ((String) objArr[0]).intern();
                Object[] objArr2 = new Object[1];
                q((byte) TextUtils.indexOf("", "", 0), MotionEvent.axisFromString("") - 551862845, (short) (ViewConfiguration.getTouchSlop() >> 8), TextUtils.lastIndexOf("", '0') - 114, 43584 - AndroidCharacter.getMirror('0'), objArr2);
                g.a(intern, ((String) objArr2[0]).intern(), e);
                return new byte[0];
            }
        }
        switch (byteArrayOutputStream2.size() > 0 ? (char) 20 : Typography.greater) {
            case 20:
                int i3 = k + 27;
                l = i3 % 128;
                int i4 = i3 % 2;
                byteArrayOutputStream.write(bArr);
                switch (byteArrayOutputStream2.size() > 127 ? '\\' : 'F') {
                    case Opcodes.DUP2 /* 92 */:
                        int i5 = k + 19;
                        l = i5 % 128;
                        int i6 = i5 % 2;
                        byteArrayOutputStream.write(-127);
                        break;
                }
                byteArrayOutputStream.write((byte) byteArrayOutputStream2.size());
                byteArrayOutputStream.write(byteArrayOutputStream2.toByteArray());
            default:
                return byteArrayOutputStream.toByteArray();
        }
        g.c();
        Object[] objArr3 = new Object[1];
        q((byte) (ImageFormat.getBitsPerPixel(0) + 1), (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)) - 551862856, (short) View.MeasureSpec.getMode(0), View.getDefaultSize(0, 0) - 115, ExpandableListView.getPackedPositionChild(0L) - 1814124047, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr22 = new Object[1];
        q((byte) TextUtils.indexOf("", "", 0), MotionEvent.axisFromString("") - 551862845, (short) (ViewConfiguration.getTouchSlop() >> 8), TextUtils.lastIndexOf("", '0') - 114, 43584 - AndroidCharacter.getMirror('0'), objArr22);
        g.a(intern2, ((String) objArr22[0]).intern(), e);
        return new byte[0];
    }

    private static byte[] c(byte[] bArr, byte b) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            byteArrayOutputStream.write(bArr);
            byteArrayOutputStream.write(1);
            byteArrayOutputStream.write(b);
            int i2 = k + 65;
            l = i2 % 128;
            int i3 = i2 % 2;
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            int i4 = l + 45;
            k = i4 % 128;
            int i5 = i4 % 2;
            return byteArray;
        } catch (IOException e) {
            g.c();
            Object[] objArr = new Object[1];
            q((byte) View.MeasureSpec.getSize(0), (-551862856) - TextUtils.indexOf("", "", 0), (short) ((-1) - TextUtils.lastIndexOf("", '0')), (-116) - TextUtils.lastIndexOf("", '0'), (-1814124048) + (ViewConfiguration.getTouchSlop() >> 8), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            q((byte) TextUtils.indexOf("", ""), Color.green(0) - 551862846, (short) View.combineMeasuredStates(0, 0), TextUtils.indexOf("", "") - 115, Color.red(0) - 1814124016, objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e);
            return new byte[0];
        }
    }

    public static byte[] d(String str, byte b) {
        int i2 = k + 95;
        l = i2 % 128;
        char c = i2 % 2 != 0 ? 'A' : (char) 2;
        byte[] b2 = o.dk.b.b(str);
        switch (c) {
            case RegisterRequest.U2F_V1_CHALLENGE_BYTE_LENGTH /* 65 */:
                c(b2, b);
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                byte[] c2 = c(b2, b);
                int i3 = k + 45;
                l = i3 % 128;
                int i4 = i3 % 2;
                return c2;
        }
    }

    public static byte[] e(byte[] bArr, byte[] bArr2) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            byteArrayOutputStream.write(bArr);
            byteArrayOutputStream.write(bArr2);
            byte[] byteArray = byteArrayOutputStream.toByteArray();
            int i2 = k + Opcodes.DDIV;
            l = i2 % 128;
            switch (i2 % 2 != 0 ? ')' : '#') {
                case ')':
                    throw null;
                default:
                    return byteArray;
            }
        } catch (IOException e) {
            g.c();
            Object[] objArr = new Object[1];
            q((byte) (ViewConfiguration.getPressedStateDuration() >> 16), (-551862856) + (ViewConfiguration.getDoubleTapTimeout() >> 16), (short) View.resolveSizeAndState(0, 0, 0), (-115) - (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)), (-1814124048) - ExpandableListView.getPackedPositionGroup(0L), objArr);
            String intern = ((String) objArr[0]).intern();
            Object[] objArr2 = new Object[1];
            q((byte) KeyEvent.normalizeMetaState(0), (-551862818) - ExpandableListView.getPackedPositionGroup(0L), (short) (ViewConfiguration.getJumpTapTimeout() >> 16), (ViewConfiguration.getEdgeSlop() >> 16) - 115, (-1814124017) - View.combineMeasuredStates(0, 0), objArr2);
            g.a(intern, ((String) objArr2[0]).intern(), e);
            return new byte[0];
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    public static byte[] b(byte[] r2, java.lang.String... r3) {
        /*
            java.util.ArrayList r2 = b(r2)
            java.util.Iterator r2 = r2.iterator()
        L9:
            boolean r0 = r2.hasNext()
            if (r0 == 0) goto L11
            r0 = 1
            goto L12
        L11:
            r0 = 0
        L12:
            switch(r0) {
                case 0: goto L22;
                default: goto L15;
            }
        L15:
            int r0 = o.ej.d.k
            int r0 = r0 + 53
            int r1 = r0 % 128
            o.ej.d.l = r1
            int r0 = r0 % 2
            if (r0 == 0) goto L2e
            goto L2e
        L22:
            int r2 = o.ej.d.l
            int r2 = r2 + 99
            int r3 = r2 % 128
            o.ej.d.k = r3
            int r2 = r2 % 2
            r2 = 0
            return r2
        L2e:
            java.lang.Object r0 = r2.next()
            o.ej.d r0 = (o.ej.d) r0
            java.lang.String r1 = r0.a()
            boolean r1 = o.ee.o.a.d(r1, r3)
            if (r1 == 0) goto L41
            r1 = 81
            goto L43
        L41:
            r1 = 93
        L43:
            switch(r1) {
                case 81: goto L47;
                default: goto L46;
            }
        L46:
            goto L4c
        L47:
            byte[] r2 = r0.e()
            return r2
        L4c:
            goto L9
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.d.b(byte[], java.lang.String[]):byte[]");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void p(char r21, int r22, int r23, java.lang.Object[] r24) {
        /*
            Method dump skipped, instructions count: 592
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.d.p(char, int, int, java.lang.Object[]):void");
    }

    /* JADX WARN: Code restructure failed: missing block: B:105:0x033d, code lost:
    
        r0 = r5;
     */
    /* JADX WARN: Code restructure failed: missing block: B:135:0x028c, code lost:
    
        r0 = 1;
     */
    /* JADX WARN: Code restructure failed: missing block: B:137:0x028a, code lost:
    
        if (r5 != false) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:86:0x0279, code lost:
    
        if (r5 != false) goto L81;
     */
    /* JADX WARN: Code restructure failed: missing block: B:87:0x028e, code lost:
    
        r8 = r8 + 91;
        o.ej.d.$10 = r8 % 128;
        r8 = r8 % 2;
        r0 = 0;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:84:0x0267. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void q(byte r16, int r17, short r18, int r19, int r20, java.lang.Object[] r21) {
        /*
            Method dump skipped, instructions count: 1036
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.ej.d.q(byte, int, short, int, int, java.lang.Object[]):void");
    }
}
