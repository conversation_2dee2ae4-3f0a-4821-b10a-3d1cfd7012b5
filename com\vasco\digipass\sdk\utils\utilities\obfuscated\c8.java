package com.vasco.digipass.sdk.utils.utilities.obfuscated;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\utilities\obfuscated\c8.smali */
public class c8 extends u implements g {
    private b0 b;

    private c8(b0 b0Var) {
        this.b = b0Var;
    }

    public static c8 a(Object obj) {
        if (obj == null || (obj instanceof c8)) {
            return (c8) obj;
        }
        if (obj instanceof b0) {
            return new c8((b0) obj);
        }
        if (!(obj instanceof byte[])) {
            throw new IllegalArgumentException("unknown object in getInstance()");
        }
        try {
            return new c8(b0.a((byte[]) obj));
        } catch (Exception e) {
            throw new IllegalArgumentException("unable to parse encoded data: " + e.getMessage());
        }
    }

    public b0 e() {
        return this.b;
    }

    public boolean f() {
        return this.b instanceof s;
    }

    public boolean g() {
        return this.b instanceof w;
    }

    @Override // com.vasco.digipass.sdk.utils.utilities.obfuscated.u, com.vasco.digipass.sdk.utils.utilities.obfuscated.h
    public b0 toASN1Primitive() {
        return this.b;
    }
}
