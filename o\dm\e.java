package o.dm;

import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.telephony.cdma.CdmaCellLocation;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import o.dk.c;
import o.ej.d;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dm\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static int b;
    private static char[] c;
    private static char[] d;
    private static int e;

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        b = 0;
        e = 1;
        d = new char[]{50901, 50942, 50901, 50943, 50902, 50938, 50902, 50931, 50933, 50850, 50924, 50932, 50934, 50825, 50912, 50841, 50843, 50837, 50926, 50929, 50936, 50933, 50926, 50928, 50929, 50934, 50928, 50764, 50765, 50866, 50926, 50930, 50872, 50750, 50747, 50721, 50834, 50805, 50813, 50787, 50903, 50942, 50929, 50928, 50925, 50934};
        c = new char[]{24829, 52406, 31891, 53461, 9448, 30784, 11504, 32950, 29833, 10275, 11400, 32965, 21289, 65303, 11504, 32950, 29835, 10325, 11516, 32950, 29833, 10322, 38934, 13404, 49251, 40112, 11504, 32950, 29834, 10331, 11504, 32950, 29833, 10326, 6093, 48125, 11504, 32962, 7708, 45658, 18019, 6841, 48423, 4449, 58712, 47501};
        a = 6160494880828260592L;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002e  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0026  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002e -> B:4:0x0036). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void h(byte r6, byte r7, short r8, java.lang.Object[] r9) {
        /*
            int r7 = r7 * 4
            int r7 = r7 + 1
            int r6 = r6 * 2
            int r6 = r6 + 4
            byte[] r0 = o.dm.e.$$a
            int r8 = 122 - r8
            byte[] r1 = new byte[r7]
            int r7 = r7 + (-1)
            r2 = 0
            if (r0 != 0) goto L1a
            r3 = r1
            r4 = r2
            r1 = r0
            r0 = r9
            r9 = r7
            r7 = r6
            goto L36
        L1a:
            r3 = r2
            r5 = r7
            r7 = r6
            r6 = r8
            r8 = r5
        L1f:
            byte r4 = (byte) r6
            r1[r3] = r4
            int r4 = r3 + 1
            if (r3 != r8) goto L2e
            java.lang.String r6 = new java.lang.String
            r6.<init>(r1, r2)
            r9[r2] = r6
            return
        L2e:
            r3 = r0[r7]
            r5 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r5
        L36:
            int r8 = -r8
            int r6 = r6 + r8
            int r7 = r7 + 1
            r8 = r9
            r9 = r0
            r0 = r1
            r1 = r3
            r3 = r4
            goto L1f
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dm.e.h(byte, byte, short, java.lang.Object[]):void");
    }

    static void init$0() {
        $$a = new byte[]{116, -79, 3, -53};
        $$b = 68;
    }

    public static byte[] e(byte[] bArr, byte[] bArr2, byte b2, byte[] bArr3, byte[] bArr4) {
        int i = e + Opcodes.DREM;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("\u0001\u0001", new int[]{0, 2, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) (19456 - (ViewConfiguration.getScrollDefaultDelay() >> 16)), Drawable.resolveOpacity(0, 0), 3 - (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)), objArr2);
        Object[] objArr3 = new Object[1];
        f("\u0000\u0001", new int[]{2, 2, 0, 0}, true, objArr3);
        Object[] objArr4 = new Object[1];
        f("\u0001\u0001", new int[]{4, 2, 0, 2}, true, objArr4);
        Object[] objArr5 = new Object[1];
        g((char) (20580 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), (Process.myPid() >> 22) + 2, 4 - ExpandableListView.getPackedPositionGroup(0L), objArr5);
        Object[] objArr6 = new Object[1];
        g((char) (TypedValue.complexToFraction(0, 0.0f, 0.0f) > 0.0f ? 1 : (TypedValue.complexToFraction(0, 0.0f, 0.0f) == 0.0f ? 0 : -1)), KeyEvent.normalizeMetaState(0) + 6, 3 - Process.getGidForName(""), objArr6);
        byte[] e2 = d.e(intern, d.e(((String) objArr2[0]).intern(), bArr), d.e(((String) objArr3[0]).intern(), bArr2), d.d(((String) objArr4[0]).intern(), b2), d.e(((String) objArr5[0]).intern(), bArr3), d.e(((String) objArr6[0]).intern(), bArr4));
        Object[] objArr7 = new Object[1];
        f("\u0000\u0000", new int[]{6, 2, 0, 1}, true, objArr7);
        String intern2 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        f("\u0001\u0000", new int[]{8, 2, 57, 0}, false, objArr8);
        String intern3 = ((String) objArr8[0]).intern();
        byte[][] bArr5 = {c.e()};
        Object[] objArr9 = new Object[1];
        g((char) TextUtils.indexOf("", ""), 10 - KeyEvent.keyCodeFromString(""), TextUtils.lastIndexOf("", '0') + 3, objArr9);
        String intern4 = ((String) objArr9[0]).intern();
        Object[] objArr10 = new Object[1];
        f("\u0001\u0001\u0000\u0000", new int[]{10, 4, 0, 0}, true, objArr10);
        byte[] e3 = d.e(intern2, d.e(intern3, bArr5), d.e(intern4, d.e(((String) objArr10[0]).intern(), e2)));
        int i3 = b + 25;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? (char) 19 : '+') {
            case 19:
                Object obj = null;
                obj.hashCode();
                throw null;
            default:
                return e3;
        }
    }

    public static byte[] b(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[] bArr5) {
        int i = b + 51;
        e = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("\u0001\u0001\u0000\u0000", new int[]{10, 4, 0, 0}, true, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) (20580 - (ViewConfiguration.getZoomControlsTimeout() > 0L ? 1 : (ViewConfiguration.getZoomControlsTimeout() == 0L ? 0 : -1))), (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)) + 1, 4 - KeyEvent.keyCodeFromString(""), objArr2);
        Object[] objArr3 = new Object[1];
        f("\u0000\u0000\u0001\u0001", new int[]{14, 4, 25, 0}, true, objArr3);
        byte[] e2 = d.e(intern, d.e(((String) objArr2[0]).intern(), bArr4), d.e(((String) objArr3[0]).intern(), bArr5));
        Object[] objArr4 = new Object[1];
        g((char) ((Process.getThreadPriority(0) + 20) >> 6), (ExpandableListView.getPackedPositionForGroup(0) > 0L ? 1 : (ExpandableListView.getPackedPositionForGroup(0) == 0L ? 0 : -1)) + 10, View.MeasureSpec.makeMeasureSpec(0, 0) + 2, objArr4);
        String intern2 = ((String) objArr4[0]).intern();
        Object[] objArr5 = new Object[1];
        f("\u0000\u0001", new int[]{2, 2, 0, 0}, true, objArr5);
        Object[] objArr6 = new Object[1];
        f("\u0000\u0001\u0001\u0001", new int[]{18, 4, 0, 3}, false, objArr6);
        Object[] objArr7 = new Object[1];
        f("\u0000\u0001\u0001\u0000", new int[]{22, 4, 0, 2}, true, objArr7);
        byte[] e3 = d.e(intern2, d.e(((String) objArr5[0]).intern(), bArr2), d.e(((String) objArr6[0]).intern(), bArr3), d.e(((String) objArr7[0]).intern(), null), e2);
        Object[] objArr8 = new Object[1];
        f("\u0000\u0000", new int[]{6, 2, 0, 1}, true, objArr8);
        String intern3 = ((String) objArr8[0]).intern();
        Object[] objArr9 = new Object[1];
        f("\u0001\u0000", new int[]{8, 2, 57, 0}, false, objArr9);
        byte[] e4 = d.e(intern3, d.e(((String) objArr9[0]).intern(), bArr), e3);
        int i3 = b + Opcodes.LNEG;
        e = i3 % 128;
        switch (i3 % 2 == 0 ? '5' : 'b') {
            case Opcodes.FADD /* 98 */:
                return e4;
            default:
                throw null;
        }
    }

    public static byte[] e(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[] bArr5, byte[] bArr6) {
        int i = b + 81;
        e = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) ((CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 32727), 12 - (ViewConfiguration.getFadingEdgeLength() >> 16), 1 - TextUtils.lastIndexOf("", '0'), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g((char) View.resolveSizeAndState(0, 0, 0), TextUtils.getTrimmedLength("") + 14, 4 - (ViewConfiguration.getTapTimeout() >> 16), objArr2);
        Object[] objArr3 = new Object[1];
        g((char) TextUtils.indexOf("", "", 0, 0), (ViewConfiguration.getScrollDefaultDelay() >> 16) + 18, 3 - TextUtils.indexOf((CharSequence) "", '0', 0), objArr3);
        Object[] objArr4 = new Object[1];
        f("\u0001\u0001\u0001\u0000", new int[]{26, 4, 66, 0}, false, objArr4);
        Object[] objArr5 = new Object[1];
        g((char) ((ViewConfiguration.getMinimumFlingVelocity() >> 16) + 46314), TextUtils.indexOf((CharSequence) "", '0', 0) + 23, 4 - (ViewConfiguration.getLongPressTimeout() >> 16), objArr5);
        Object[] objArr6 = new Object[1];
        g((char) Color.argb(0, 0, 0, 0), 26 - KeyEvent.getDeadChar(0, 0), (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)) + 4, objArr6);
        Object[] objArr7 = new Object[1];
        g((char) (1 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1))), 30 - (ViewConfiguration.getWindowTouchSlop() >> 8), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 4, objArr7);
        byte[] e2 = d.e(intern, d.e(((String) objArr2[0]).intern(), bArr), d.e(((String) objArr3[0]).intern(), bArr2), d.e(((String) objArr4[0]).intern(), bArr3), d.e(((String) objArr5[0]).intern(), bArr4), d.e(((String) objArr6[0]).intern(), bArr5), d.e(((String) objArr7[0]).intern(), bArr6));
        int i3 = e + Opcodes.DMUL;
        b = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                return e2;
        }
    }

    public static byte[] a(byte b2, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int i = b + 93;
        e = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) ((-16744489) - Color.rgb(0, 0, 0)), 12 - (ViewConfiguration.getScrollBarSize() >> 8), Process.getGidForName("") + 3, objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("\u0000\u0000", new int[]{30, 2, 0, 2}, true, objArr2);
        Object[] objArr3 = new Object[1];
        g((char) (KeyEvent.keyCodeFromString("") + 15165), 34 - View.MeasureSpec.getMode(0), TextUtils.indexOf((CharSequence) "", '0', 0) + 3, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        byte[][] bArr4 = {bArr};
        Object[] objArr4 = new Object[1];
        f("\u0000\u0001\u0001\u0001", new int[]{32, 4, Opcodes.INVOKESPECIAL, 3}, true, objArr4);
        Object[] objArr5 = new Object[1];
        g((char) (ViewConfiguration.getEdgeSlop() >> 16), 36 - KeyEvent.keyCodeFromString(""), 2 - View.resolveSizeAndState(0, 0, 0), objArr5);
        byte[] e2 = d.e(intern, d.d(((String) objArr2[0]).intern(), b2), d.e(intern2, bArr4), d.e(((String) objArr4[0]).intern(), bArr2), d.e(((String) objArr5[0]).intern(), bArr3));
        int i3 = b + 71;
        e = i3 % 128;
        int i4 = i3 % 2;
        return e2;
    }

    public static byte[] d(byte[] bArr, byte[] bArr2, byte[] bArr3) {
        int i = e + 55;
        b = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        g((char) ((ViewConfiguration.getPressedStateDuration() >> 16) + 32727), 12 - View.MeasureSpec.getSize(0), 2 - ExpandableListView.getPackedPositionGroup(0L), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        f("\u0001\u0001\u0001\u0000", new int[]{36, 4, Opcodes.LSHL, 2}, true, objArr2);
        Object[] objArr3 = new Object[1];
        g((char) (13037 - (ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1))), (ViewConfiguration.getScrollBarSize() >> 8) + 38, 3 - MotionEvent.axisFromString(""), objArr3);
        Object[] objArr4 = new Object[1];
        g((char) ((ViewConfiguration.getScrollFriction() > 0.0f ? 1 : (ViewConfiguration.getScrollFriction() == 0.0f ? 0 : -1)) + 37334), (ViewConfiguration.getScrollBarFadeDuration() >> 16) + 42, (AudioTrack.getMaxVolume() > 0.0f ? 1 : (AudioTrack.getMaxVolume() == 0.0f ? 0 : -1)) + 3, objArr4);
        byte[] e2 = d.e(intern, d.e(((String) objArr2[0]).intern(), bArr), d.e(((String) objArr3[0]).intern(), bArr2), d.e(((String) objArr4[0]).intern(), bArr3));
        int i3 = b + 55;
        e = i3 % 128;
        switch (i3 % 2 != 0) {
            case true:
                return e2;
            default:
                int i4 = 35 / 0;
                return e2;
        }
    }

    public static b c(byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4, byte[] bArr5, byte[] bArr6) {
        b bVar = new b();
        Object[] objArr = new Object[1];
        f("\u0000\u0000\u0000\u0001", new int[]{40, 4, 0, 0}, true, objArr);
        d.e(((String) objArr[0]).intern(), bArr);
        Object[] objArr2 = new Object[1];
        f("\u0001\u0000", new int[]{44, 2, 0, 1}, false, objArr2);
        d.e(((String) objArr2[0]).intern(), bArr2);
        Object[] objArr3 = new Object[1];
        g((char) ((SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) - 1), 14 - (ViewConfiguration.getMaximumDrawingCacheSize() >> 24), (ViewConfiguration.getScrollBarSize() >> 8) + 4, objArr3);
        d.e(((String) objArr3[0]).intern(), bArr3);
        Object[] objArr4 = new Object[1];
        g((char) ((ViewConfiguration.getTapTimeout() >> 16) + 46314), 22 - (ViewConfiguration.getMinimumFlingVelocity() >> 16), (Process.myPid() >> 22) + 4, objArr4);
        d.e(((String) objArr4[0]).intern(), bArr4);
        Object[] objArr5 = new Object[1];
        g((char) ((Process.getThreadPriority(0) + 20) >> 6), View.MeasureSpec.getMode(0) + 26, View.combineMeasuredStates(0, 0) + 4, objArr5);
        d.e(((String) objArr5[0]).intern(), bArr5);
        Object[] objArr6 = new Object[1];
        g((char) ((-1) - ((byte) KeyEvent.getModifierMetaStateMask())), (Process.myPid() >> 22) + 30, 4 - (CdmaCellLocation.convertQuartSecToDecDegrees(0) > 0.0d ? 1 : (CdmaCellLocation.convertQuartSecToDecDegrees(0) == 0.0d ? 0 : -1)), objArr6);
        d.e(((String) objArr6[0]).intern(), bArr6);
        int i = e + 17;
        b = i % 128;
        int i2 = i % 2;
        return bVar;
    }

    /* JADX WARN: Code restructure failed: missing block: B:95:0x02a9, code lost:
    
        r2 = r0;
     */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r22, int[] r23, boolean r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 796
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dm.e.f(java.lang.String, int[], boolean, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(char r20, int r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 714
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dm.e.g(char, int, int, java.lang.Object[]):void");
    }
}
