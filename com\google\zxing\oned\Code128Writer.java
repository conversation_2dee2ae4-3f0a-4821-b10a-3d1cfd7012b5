package com.google.zxing.oned;

import com.esotericsoftware.asm.Opcodes;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\zxing\oned\Code128Writer.smali */
public final class Code128Writer extends OneDimensionalCodeWriter {
    private static final int CODE_CODE_B = 100;
    private static final int CODE_CODE_C = 99;
    private static final int CODE_FNC_1 = 102;
    private static final int CODE_FNC_2 = 97;
    private static final int CODE_FNC_3 = 96;
    private static final int CODE_FNC_4_B = 100;
    private static final int CODE_START_B = 104;
    private static final int CODE_START_C = 105;
    private static final int CODE_STOP = 106;
    private static final char ESCAPE_FNC_1 = 241;
    private static final char ESCAPE_FNC_2 = 242;
    private static final char ESCAPE_FNC_3 = 243;
    private static final char ESCAPE_FNC_4 = 244;

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\google\zxing\oned\Code128Writer$CType.smali */
    private enum CType {
        UNCODABLE,
        ONE_DIGIT,
        TWO_DIGITS,
        FNC_1
    }

    @Override // com.google.zxing.oned.OneDimensionalCodeWriter, com.google.zxing.Writer
    public BitMatrix encode(String contents, BarcodeFormat format, int width, int height, Map<EncodeHintType, ?> hints) throws WriterException {
        if (format != BarcodeFormat.CODE_128) {
            throw new IllegalArgumentException("Can only encode CODE_128, but got " + format);
        }
        return super.encode(contents, format, width, height, hints);
    }

    @Override // com.google.zxing.oned.OneDimensionalCodeWriter
    public boolean[] encode(String str) {
        int length = str.length();
        if (length <= 0 || length > 80) {
            throw new IllegalArgumentException("Contents length should be between 1 and 80 characters, but got " + length);
        }
        int i = 0;
        for (int i2 = 0; i2 < length; i2++) {
            char charAt = str.charAt(i2);
            if (charAt < ' ' || charAt > '~') {
                switch (charAt) {
                    case 241:
                    case 242:
                    case 243:
                    case 244:
                        break;
                    default:
                        throw new IllegalArgumentException("Bad character in input: " + charAt);
                }
            }
        }
        ArrayList<int[]> arrayList = new ArrayList();
        int i3 = 0;
        int i4 = 0;
        int i5 = 0;
        int i6 = 1;
        while (i3 < length) {
            int chooseCode = chooseCode(str, i3, i5);
            int i7 = 100;
            if (chooseCode == i5) {
                switch (str.charAt(i3)) {
                    case 241:
                        i7 = 102;
                        break;
                    case 242:
                        i7 = 97;
                        break;
                    case 243:
                        i7 = 96;
                        break;
                    case 244:
                        break;
                    default:
                        if (i5 != 100) {
                            i7 = Integer.parseInt(str.substring(i3, i3 + 2));
                            i3++;
                            break;
                        } else {
                            i7 = str.charAt(i3) - ' ';
                            break;
                        }
                }
                i3++;
            } else {
                if (i5 == 0) {
                    if (chooseCode == 100) {
                        i7 = 104;
                    } else {
                        i7 = 105;
                    }
                } else {
                    i7 = chooseCode;
                }
                i5 = chooseCode;
            }
            arrayList.add(Code128Reader.CODE_PATTERNS[i7]);
            i4 += i7 * i6;
            if (i3 != 0) {
                i6++;
            }
        }
        arrayList.add(Code128Reader.CODE_PATTERNS[i4 % Opcodes.DSUB]);
        arrayList.add(Code128Reader.CODE_PATTERNS[106]);
        int i8 = 0;
        for (int[] iArr : arrayList) {
            for (int i9 : iArr) {
                i8 += i9;
            }
        }
        boolean[] zArr = new boolean[i8];
        Iterator it = arrayList.iterator();
        while (it.hasNext()) {
            i += appendPattern(zArr, i, (int[]) it.next(), true);
        }
        return zArr;
    }

    private static CType findCType(CharSequence value, int start) {
        int last = value.length();
        if (start >= last) {
            return CType.UNCODABLE;
        }
        char c = value.charAt(start);
        if (c != 241) {
            if (c < '0' || c > '9') {
                return CType.UNCODABLE;
            }
            if (start + 1 >= last) {
                return CType.ONE_DIGIT;
            }
            char c2 = value.charAt(start + 1);
            if (c2 < '0' || c2 > '9') {
                return CType.ONE_DIGIT;
            }
            return CType.TWO_DIGITS;
        }
        return CType.FNC_1;
    }

    private static int chooseCode(CharSequence value, int start, int oldCode) {
        CType lookahead;
        CType findCType = findCType(value, start);
        CType lookahead2 = findCType;
        if (findCType == CType.UNCODABLE || lookahead2 == CType.ONE_DIGIT) {
            return 100;
        }
        if (oldCode == 99) {
            return oldCode;
        }
        if (oldCode == 100) {
            if (lookahead2 == CType.FNC_1) {
                return oldCode;
            }
            CType lookahead3 = findCType(value, start + 2);
            if (lookahead3 != CType.UNCODABLE && lookahead3 != CType.ONE_DIGIT) {
                if (lookahead3 == CType.FNC_1) {
                    return findCType(value, start + 3) == CType.TWO_DIGITS ? 99 : 100;
                }
                int index = start + 4;
                while (true) {
                    lookahead = findCType(value, index);
                    if (lookahead != CType.TWO_DIGITS) {
                        break;
                    }
                    index += 2;
                }
                return lookahead == CType.ONE_DIGIT ? 100 : 99;
            }
            return oldCode;
        }
        if (lookahead2 == CType.FNC_1) {
            lookahead2 = findCType(value, start + 1);
        }
        return lookahead2 == CType.TWO_DIGITS ? 99 : 100;
    }
}
