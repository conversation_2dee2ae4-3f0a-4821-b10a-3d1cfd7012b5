package o.dl;

import android.graphics.Color;
import com.esotericsoftware.asm.Opcodes;
import o.h.a;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\dl\e.smali */
public final class e extends a {
    private static char a;
    private static char c;
    private static char d;
    private static char e;
    private static int g;
    private static int $10 = 0;
    private static int $11 = 1;
    private static int h = 0;

    static {
        g = 1;
        e();
        Color.red(0);
        int i = h + Opcodes.LNEG;
        g = i % 128;
        switch (i % 2 != 0) {
            case false:
                int i2 = 95 / 0;
                break;
        }
    }

    static void e() {
        c = (char) 61127;
        a = (char) 22102;
        d = (char) 21736;
        e = (char) 20375;
    }

    /* JADX WARN: Illegal instructions before constructor call */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public e(boolean r5) {
        /*
            r4 = this;
            r0 = 0
            int r1 = android.view.KeyEvent.getDeadChar(r0, r0)
            int r1 = 20 - r1
            r2 = 1
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r3 = "鮈ꢁæ䘉ꂕ査븬웧⭳럑㍒珧⭫\uf363쵑ᱷᥕﭩꑡ풞"
            k(r3, r1, r2)
            r0 = r2[r0]
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r0 = r0.intern()
            o.i.i r1 = o.i.i.d
            r4.<init>(r0, r1, r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.e.<init>(boolean):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(java.lang.String r24, int r25, java.lang.Object[] r26) {
        /*
            Method dump skipped, instructions count: 548
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.dl.e.k(java.lang.String, int, java.lang.Object[]):void");
    }
}
