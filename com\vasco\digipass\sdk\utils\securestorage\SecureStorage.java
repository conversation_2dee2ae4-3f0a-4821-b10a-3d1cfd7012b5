package com.vasco.digipass.sdk.utils.securestorage;

import android.content.Context;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.BiometricWriteProtectionSettings;
import com.vasco.digipass.sdk.utils.securestorage.biometrics.SecureStorageBiometricWriteCallback;
import com.vasco.digipass.sdk.utils.securestorage.initialization.SecureStorageInitCallback;
import com.vasco.digipass.sdk.utils.securestorage.model.IVEncryptedData;
import com.vasco.digipass.sdk.utils.securestorage.obfuscated.m;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.Intrinsics;

@Metadata(d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u0000 '2\u00020\u0001:\u0001'J\b\u0010\r\u001a\u00020\u000eH&J\u0011\u0010\u000f\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u0004H¦\u0002J\u0010\u0010\u0011\u001a\u00020\u000e2\u0006\u0010\u0012\u001a\u00020\u0013H&J\u0014\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0003H&J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0010\u001a\u00020\u0004H&J\b\u0010\u0017\u001a\u00020\u0004H&J\u0010\u0010\u0018\u001a\u00020\u00042\u0006\u0010\u0010\u001a\u00020\u0004H&J\u0018\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0016H&J\u0018\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u0004H&J\u0010\u0010\u001c\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0004H&J\b\u0010\u001d\u001a\u00020\u001eH&J\"\u0010\u001f\u001a\u00020\u000e2\b\u0010 \u001a\u0004\u0018\u00010\u00042\u0006\u0010!\u001a\u00020\u001e2\u0006\u0010\u0012\u001a\u00020\u0013H&J*\u0010\"\u001a\u00020\u000e2\b\u0010 \u001a\u0004\u0018\u00010\u00042\u0006\u0010!\u001a\u00020\u001e2\u0006\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020&H'R\u001e\u0010\u0002\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003X¦\u0004¢\u0006\u0006\u001a\u0004\b\u0006\u0010\u0007R\u0012\u0010\b\u001a\u00020\tX¦\u0004¢\u0006\u0006\u001a\u0004\b\b\u0010\nR\u001e\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00050\u0003X¦\u0004¢\u0006\u0006\u001a\u0004\b\f\u0010\u0007¨\u0006("}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorage;", "", "bytesMap", "", "", "Lcom/vasco/digipass/sdk/utils/securestorage/model/IVEncryptedData;", "getBytesMap", "()Ljava/util/Map;", "isSecureHardwareProtected", "", "()Z", "stringsMap", "getStringsMap", "clear", "", "contains", "key", "delete", "context", "Landroid/content/Context;", "getAll", "getBytes", "", "getKeyName", "getString", "putBytes", "value", "putString", "remove", "size", "", "write", "fingerprint", "iterationNumber", "writeBiometryProtected", "biometricWriteProtectionSettings", "Lcom/vasco/digipass/sdk/utils/securestorage/biometrics/BiometricWriteProtectionSettings;", "biometricWriteCallback", "Lcom/vasco/digipass/sdk/utils/securestorage/biometrics/SecureStorageBiometricWriteCallback;", "Companion", "lib_release"}, k = 1, mv = {1, 8, 0}, xi = 48)
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\SecureStorage.smali */
public interface SecureStorage {

    /* renamed from: Companion, reason: from kotlin metadata */
    public static final Companion INSTANCE = Companion.a;
    public static final int FILENAME_MAX_LENGTH = 100;
    public static final int STORAGE_KEY_MAX_LENGTH = 100;
    public static final String VERSION = "5.3.0";

    @Metadata(d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001J9\u0010\f\u001a\u00020\u000b2\u0006\u0010\u0003\u001a\u00020\u00022\b\u0010\u0004\u001a\u0004\u0018\u00010\u00022\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\tH\u0007¢\u0006\u0004\b\f\u0010\rJ!\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u0003\u001a\u00020\u00022\b\u0010\b\u001a\u0004\u0018\u00010\u0007H\u0007¢\u0006\u0004\b\u000e\u0010\u000fR\u0014\u0010\u0010\u001a\u00020\u00058\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0010\u0010\u0011R\u0014\u0010\u0012\u001a\u00020\u00058\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0012\u0010\u0011R\u0014\u0010\u0013\u001a\u00020\u00028\u0006X\u0086T¢\u0006\u0006\n\u0004\b\u0013\u0010\u0014¨\u0006\u0015"}, d2 = {"Lcom/vasco/digipass/sdk/utils/securestorage/SecureStorage$Companion;", "", "", "fileName", "fingerprint", "", "iterationNumber", "Landroid/content/Context;", "context", "Lcom/vasco/digipass/sdk/utils/securestorage/initialization/SecureStorageInitCallback;", "secureStorageInitCallback", "", "createSecureStorage", "(Ljava/lang/String;Ljava/lang/String;ILandroid/content/Context;Lcom/vasco/digipass/sdk/utils/securestorage/initialization/SecureStorageInitCallback;)V", "delete", "(Ljava/lang/String;Landroid/content/Context;)V", "FILENAME_MAX_LENGTH", "I", "STORAGE_KEY_MAX_LENGTH", "VERSION", "Ljava/lang/String;", "lib_release"}, k = 1, mv = {1, 8, 0})
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\com\vasco\digipass\sdk\utils\securestorage\SecureStorage$Companion.smali */
    public static final class Companion {
        public static final int FILENAME_MAX_LENGTH = 100;
        public static final int STORAGE_KEY_MAX_LENGTH = 100;
        public static final String VERSION = "5.3.0";
        public static final /* synthetic */ Companion a = new Companion();

        @JvmStatic
        public final void createSecureStorage(String fileName, String fingerprint, int iterationNumber, Context context, SecureStorageInitCallback secureStorageInitCallback) throws SecureStorageSDKException {
            Intrinsics.checkNotNullParameter(fileName, "fileName");
            Intrinsics.checkNotNullParameter(context, "context");
            Intrinsics.checkNotNullParameter(secureStorageInitCallback, "secureStorageInitCallback");
            new a().init$lib_release(fileName, fingerprint, iterationNumber, context, secureStorageInitCallback);
        }

        @JvmStatic
        public final void delete(String fileName, Context context) throws SecureStorageSDKException {
            Intrinsics.checkNotNullParameter(fileName, "fileName");
            if (context == null) {
                throw new SecureStorageSDKException(SecureStorageSDKErrorCodes.CONTEXT_NULL, null, 2, null);
            }
            a.Companion.getClass();
            m.a(fileName, context);
        }
    }

    @JvmStatic
    static void createSecureStorage(String str, String str2, int i, Context context, SecureStorageInitCallback secureStorageInitCallback) throws SecureStorageSDKException {
        INSTANCE.createSecureStorage(str, str2, i, context, secureStorageInitCallback);
    }

    @JvmStatic
    static void delete(String str, Context context) throws SecureStorageSDKException {
        INSTANCE.delete(str, context);
    }

    void clear() throws SecureStorageSDKException;

    boolean contains(String key) throws SecureStorageSDKException;

    void delete(Context context) throws SecureStorageSDKException;

    Map<String, Object> getAll() throws SecureStorageSDKException;

    byte[] getBytes(String key) throws SecureStorageSDKException;

    Map<String, IVEncryptedData> getBytesMap();

    String getKeyName() throws SecureStorageSDKException;

    String getString(String key) throws SecureStorageSDKException;

    Map<String, IVEncryptedData> getStringsMap();

    boolean isSecureHardwareProtected();

    void putBytes(String key, byte[] value) throws SecureStorageSDKException;

    void putString(String key, String value) throws SecureStorageSDKException;

    void remove(String key) throws SecureStorageSDKException;

    int size();

    void write(String fingerprint, int iterationNumber, Context context) throws SecureStorageSDKException;

    void writeBiometryProtected(String fingerprint, int iterationNumber, BiometricWriteProtectionSettings biometricWriteProtectionSettings, SecureStorageBiometricWriteCallback biometricWriteCallback) throws SecureStorageSDKException;
}
