package fr.antelop.sdk.card;

import android.content.Context;
import android.graphics.drawable.Drawable;
import o.eo.e;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\fr\antelop\sdk\card\CardDisplay.smali */
public final class CardDisplay implements ICardDisplay {
    private final e innerCard;

    public CardDisplay(e eVar) {
        this.innerCard = eVar;
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getLabel() {
        return this.innerCard.j();
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getForegroundColor() {
        return this.innerCard.g();
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final Drawable getGraphicResource(Context context) {
        return this.innerCard.b(context);
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getLayoutDescription() {
        return this.innerCard.f();
    }

    @Override // fr.antelop.sdk.card.ICardDisplay
    public final String getDescription() {
        return this.innerCard.i();
    }

    public final String toString() {
        return new StringBuilder("CardDisplay{label=").append(getLabel() == null ? "" : getLabel()).append(", foregroundColor=").append(getForegroundColor() == null ? "" : getForegroundColor()).append(", layoutDescription=").append(getLayoutDescription() == null ? "" : getLayoutDescription()).append(", description=").append(getDescription() != null ? getDescription() : "").append('}').toString();
    }
}
