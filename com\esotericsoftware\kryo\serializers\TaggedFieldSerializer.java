package com.esotericsoftware.kryo.serializers;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.KryoException;
import com.esotericsoftware.kryo.Registration;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.InputChunked;
import com.esotericsoftware.kryo.io.Output;
import com.esotericsoftware.kryo.io.OutputChunked;
import com.esotericsoftware.kryo.serializers.FieldSerializer;
import com.esotericsoftware.kryo.util.IntMap;
import com.esotericsoftware.kryo.util.Util;
import com.esotericsoftware.minlog.Log;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.lang.reflect.Field;
import java.util.ArrayList;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TaggedFieldSerializer.smali */
public class TaggedFieldSerializer<T> extends FieldSerializer<T> {
    private final TaggedFieldSerializerConfig config;
    private IntMap<FieldSerializer.CachedField> readTags;
    private FieldSerializer.CachedField[] writeTags;

    @Target({ElementType.FIELD})
    @Retention(RetentionPolicy.RUNTIME)
    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TaggedFieldSerializer$Tag.smali */
    public @interface Tag {
        int value();
    }

    public TaggedFieldSerializer(Kryo kryo, Class type) {
        this(kryo, type, new TaggedFieldSerializerConfig());
    }

    public TaggedFieldSerializer(Kryo kryo, Class type, TaggedFieldSerializerConfig config) {
        super(kryo, type, config);
        this.config = config;
        setAcceptsNull(true);
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    protected void initializeCachedFields() {
        FieldSerializer.CachedField[] fields = this.cachedFields.fields;
        int n = fields.length;
        for (int i = 0; i < n; i++) {
            if (fields[i].field.getAnnotation(Tag.class) == null) {
                if (Log.TRACE) {
                    Log.trace("kryo", "Ignoring field without tag: " + fields[i]);
                }
                super.removeField(fields[i]);
            }
        }
        FieldSerializer.CachedField[] fields2 = this.cachedFields.fields;
        ArrayList writeTags = new ArrayList(fields2.length);
        this.readTags = new IntMap<>((int) (fields2.length / 0.8f));
        for (FieldSerializer.CachedField cachedField : fields2) {
            Field field = cachedField.field;
            int tag = ((Tag) field.getAnnotation(Tag.class)).value();
            if (this.readTags.containsKey(tag)) {
                throw new KryoException(String.format("Duplicate tag %d on fields: %s and %s", Integer.valueOf(tag), field, writeTags.get(tag)));
            }
            this.readTags.put(tag, cachedField);
            if (field.getAnnotation(Deprecated.class) == null) {
                writeTags.add(cachedField);
            }
            cachedField.tag = tag;
        }
        this.writeTags = (FieldSerializer.CachedField[]) writeTags.toArray(new FieldSerializer.CachedField[writeTags.size()]);
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    public void removeField(String fieldName) {
        super.removeField(fieldName);
        initializeCachedFields();
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer
    public void removeField(FieldSerializer.CachedField field) {
        super.removeField(field);
        initializeCachedFields();
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public void write(Kryo kryo, Output output, T object) {
        Output fieldOutput;
        OutputChunked outputChunked;
        if (object == null) {
            output.writeByte((byte) 0);
            return;
        }
        int pop = pushTypeVariables();
        FieldSerializer.CachedField[] writeTags = this.writeTags;
        boolean z = true;
        output.writeVarInt(writeTags.length + 1, true);
        writeHeader(kryo, output, object);
        boolean chunked = this.config.chunked;
        boolean readUnknownTagData = this.config.readUnknownTagData;
        if (chunked) {
            outputChunked = new OutputChunked(output, this.config.chunkSize);
            fieldOutput = outputChunked;
        } else {
            fieldOutput = output;
            outputChunked = null;
        }
        int n = writeTags.length;
        int i = 0;
        while (i < n) {
            FieldSerializer.CachedField cachedField = writeTags[i];
            if (Log.TRACE) {
                log("Write", cachedField, output.position());
            }
            output.writeVarInt(cachedField.tag, z);
            if (readUnknownTagData) {
                Class valueClass = null;
                if (object != null) {
                    try {
                        Object value = cachedField.field.get(object);
                        if (value != null) {
                            valueClass = value.getClass();
                        }
                    } catch (IllegalAccessException e) {
                    }
                }
                kryo.writeClass(fieldOutput, valueClass);
                if (valueClass == null) {
                    if (chunked) {
                        outputChunked.endChunk();
                    }
                    i++;
                    z = true;
                } else {
                    cachedField.setCanBeNull(false);
                    cachedField.setValueClass(valueClass);
                    cachedField.setReuseSerializer(false);
                }
            }
            cachedField.write(fieldOutput, object);
            if (chunked) {
                outputChunked.endChunk();
            }
            i++;
            z = true;
        }
        popTypeVariables(pop);
    }

    protected void writeHeader(Kryo kryo, Output output, T object) {
    }

    @Override // com.esotericsoftware.kryo.serializers.FieldSerializer, com.esotericsoftware.kryo.Serializer
    public T read(Kryo kryo, Input input, Class<? extends T> type) {
        Input fieldInput;
        InputChunked inputChunked;
        boolean readUnknownTagData;
        int i;
        T object;
        T object2;
        T object3;
        Input input2 = input;
        boolean z = true;
        int fieldCount = input2.readVarInt(true);
        if (fieldCount == 0) {
            return null;
        }
        int fieldCount2 = fieldCount - 1;
        int pop = pushTypeVariables();
        T object4 = create(kryo, input, type);
        kryo.reference(object4);
        boolean chunked = this.config.chunked;
        boolean readUnknownTagData2 = this.config.readUnknownTagData;
        if (chunked) {
            inputChunked = new InputChunked(input2, this.config.chunkSize);
            fieldInput = inputChunked;
        } else {
            fieldInput = input;
            inputChunked = null;
        }
        IntMap<FieldSerializer.CachedField> readTags = this.readTags;
        int i2 = 0;
        while (i2 < fieldCount2) {
            int fieldCount3 = fieldCount2;
            int tag = input2.readVarInt(z);
            FieldSerializer.CachedField cachedField = readTags.get(tag);
            IntMap<FieldSerializer.CachedField> readTags2 = readTags;
            int pop2 = pop;
            if (readUnknownTagData2) {
                try {
                    Registration registration = kryo.readClass(fieldInput);
                    if (registration == null) {
                        if (chunked) {
                            inputChunked.nextChunk();
                        }
                        object = object4;
                        readUnknownTagData = readUnknownTagData2;
                        i = i2;
                    } else {
                        readUnknownTagData = readUnknownTagData2;
                        Class valueClass = registration.getType();
                        if (cachedField == null) {
                            i = i2;
                            if (Log.TRACE) {
                                object3 = object4;
                                Log.trace("kryo", "Read unknown tag " + tag + " data, type: " + Util.className(valueClass));
                            } else {
                                object3 = object4;
                            }
                            try {
                                kryo.readObject(fieldInput, valueClass);
                            } catch (KryoException ex) {
                                String message = "Unable to read unknown tag " + tag + " data, type: " + Util.className(valueClass) + " (" + getType().getName() + "#" + cachedField + ")";
                                if (!chunked) {
                                    throw new KryoException(message, ex);
                                }
                                if (Log.DEBUG) {
                                    Log.debug("kryo", message, ex);
                                }
                            }
                            if (chunked) {
                                inputChunked.nextChunk();
                            }
                            object = object3;
                        } else {
                            object2 = object4;
                            i = i2;
                            cachedField.setCanBeNull(false);
                            cachedField.setValueClass(valueClass);
                            cachedField.setReuseSerializer(false);
                        }
                    }
                } catch (KryoException ex2) {
                    T object5 = object4;
                    readUnknownTagData = readUnknownTagData2;
                    i = i2;
                    String message2 = "Unable to read unknown tag " + tag + " data (unknown type). (" + getType().getName() + "#" + cachedField + ")";
                    if (!chunked) {
                        throw new KryoException(message2, ex2);
                    }
                    if (Log.DEBUG) {
                        Log.debug("kryo", message2, ex2);
                    }
                    inputChunked.nextChunk();
                    object = object5;
                }
                i2 = i + 1;
                object4 = object;
                fieldCount2 = fieldCount3;
                readTags = readTags2;
                pop = pop2;
                readUnknownTagData2 = readUnknownTagData;
                z = true;
                input2 = input;
            } else {
                object2 = object4;
                readUnknownTagData = readUnknownTagData2;
                i = i2;
                if (cachedField == null) {
                    if (!chunked) {
                        throw new KryoException("Unknown field tag: " + tag + " (" + getType().getName() + ")");
                    }
                    if (Log.TRACE) {
                        Log.trace("kryo", "Skip unknown field tag: " + tag);
                    }
                    inputChunked.nextChunk();
                    object = object2;
                    i2 = i + 1;
                    object4 = object;
                    fieldCount2 = fieldCount3;
                    readTags = readTags2;
                    pop = pop2;
                    readUnknownTagData2 = readUnknownTagData;
                    z = true;
                    input2 = input;
                }
            }
            if (Log.TRACE) {
                log("Read", cachedField, input.position());
            }
            object = object2;
            cachedField.read(fieldInput, object);
            if (chunked) {
                inputChunked.nextChunk();
            }
            i2 = i + 1;
            object4 = object;
            fieldCount2 = fieldCount3;
            readTags = readTags2;
            pop = pop2;
            readUnknownTagData2 = readUnknownTagData;
            z = true;
            input2 = input;
        }
        T object6 = object4;
        popTypeVariables(pop);
        return object6;
    }

    public TaggedFieldSerializerConfig getTaggedFieldSerializerConfig() {
        return this.config;
    }

    /* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali\com\esotericsoftware\kryo\serializers\TaggedFieldSerializer$TaggedFieldSerializerConfig.smali */
    public static class TaggedFieldSerializerConfig extends FieldSerializer.FieldSerializerConfig {
        int chunkSize = 1024;
        boolean chunked;
        boolean readUnknownTagData;

        @Override // com.esotericsoftware.kryo.serializers.FieldSerializer.FieldSerializerConfig
        /* renamed from: clone */
        public TaggedFieldSerializerConfig mo108clone() {
            return (TaggedFieldSerializerConfig) super.mo108clone();
        }

        public void setReadUnknownTagData(boolean readUnknownTagData) {
            this.readUnknownTagData = readUnknownTagData;
        }

        public boolean getReadUnknownTagData() {
            return this.readUnknownTagData;
        }

        public void setChunkedEncoding(boolean chunked) {
            this.chunked = chunked;
            if (Log.TRACE) {
                Log.trace("kryo", "TaggedFieldSerializerConfig setChunked: " + chunked);
            }
        }

        public boolean getChunkedEncoding() {
            return this.chunked;
        }

        public void setChunkSize(int chunkSize) {
            this.chunkSize = chunkSize;
            if (Log.TRACE) {
                Log.trace("kryo", "TaggedFieldSerializerConfig setChunkSize: " + chunkSize);
            }
        }

        public int getChunkSize() {
            return this.chunkSize;
        }
    }
}
