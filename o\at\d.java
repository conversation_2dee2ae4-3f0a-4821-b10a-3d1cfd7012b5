package o.at;

import android.graphics.Color;
import android.graphics.PointF;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;
import kotlin.text.Typography;

/* JADX WARN: Failed to restore enum class, 'enum' modifier and super class removed */
/* JADX WARN: Unknown enum class pattern. Please report as an issue! */
/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\at\d.smali */
public final class d implements o.ei.b {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    public static final d a;
    public static final d b;
    public static final d d;
    public static final d e;
    private static int f;
    private static int[] h;
    private static int i;
    private static final /* synthetic */ d[] j;
    private final String c;

    static void d() {
        h = new int[]{-782913308, -630072240, 1010922606, -1477058883, -1706709534, -982322412, 1316854472, -364150285, -1193453622, 1578408936, -710338912, -17933918, 2021282639, 1018618910, -980023881, 1425867681, 1073838530, -1110072478};
    }

    static void init$0() {
        $$a = new byte[]{114, -113, -41, 111};
        $$b = Opcodes.ARETURN;
    }

    private static void k(short s, int i2, short s2, Object[] objArr) {
        int i3 = i2 + Opcodes.DREM;
        byte[] bArr = $$a;
        int i4 = 1 - (s * 4);
        int i5 = 3 - (s2 * 3);
        byte[] bArr2 = new byte[i4];
        int i6 = -1;
        int i7 = i4 - 1;
        if (bArr == null) {
            int i8 = i7 + i5;
            i5 = i5;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = -1;
            i3 = i8;
            i7 = i7;
        }
        while (true) {
            int i9 = i5 + 1;
            int i10 = i6 + 1;
            bArr2[i10] = (byte) i3;
            if (i10 == i7) {
                objArr[0] = new String(bArr2, 0);
                return;
            }
            int i11 = i3;
            int i12 = i7;
            i5 = i9;
            objArr = objArr;
            bArr = bArr;
            bArr2 = bArr2;
            i6 = i10;
            i3 = i11 + bArr[i9];
            i7 = i12;
        }
    }

    private static /* synthetic */ d[] b() {
        int i2 = f + Opcodes.LMUL;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                d[] dVarArr = {b, e};
                dVarArr[2] = a;
                dVarArr[3] = d;
                return dVarArr;
            default:
                return new d[]{e, b, a, d};
        }
    }

    public static d valueOf(String str) {
        int i2 = i + 99;
        f = i2 % 128;
        int i3 = i2 % 2;
        d dVar = (d) Enum.valueOf(d.class, str);
        int i4 = i + 63;
        f = i4 % 128;
        switch (i4 % 2 == 0 ? Typography.quote : 'D') {
            case 'D':
                return dVar;
            default:
                int i5 = 9 / 0;
                return dVar;
        }
    }

    public static d[] values() {
        int i2 = f + 29;
        i = i2 % 128;
        switch (i2 % 2 == 0) {
            case false:
                throw null;
            default:
                d[] dVarArr = (d[]) j.clone();
                int i3 = f + Opcodes.LUSHR;
                i = i3 % 128;
                int i4 = i3 % 2;
                return dVarArr;
        }
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        i = 0;
        f = 1;
        d();
        Object[] objArr = new Object[1];
        g(new int[]{-297675722, 1603185338, -1189759826, 2099182641, 1212592858, 1243623012}, 9 - TextUtils.getTrimmedLength(""), objArr);
        String intern = ((String) objArr[0]).intern();
        Object[] objArr2 = new Object[1];
        g(new int[]{1539706403, -1529760474, 657396482, -1661856456, -1111417583, -1640790508}, 9 - (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)), objArr2);
        e = new d(intern, 0, ((String) objArr2[0]).intern());
        Object[] objArr3 = new Object[1];
        g(new int[]{1752227026, 186690696, 1514854972, 654058058, 1062984154, -1714915533}, KeyEvent.normalizeMetaState(0) + 12, objArr3);
        String intern2 = ((String) objArr3[0]).intern();
        Object[] objArr4 = new Object[1];
        g(new int[]{-148129199, 134832772, -541601715, 1213129071, -1664800114, -1396477408}, 11 - (ViewConfiguration.getJumpTapTimeout() >> 16), objArr4);
        b = new d(intern2, 1, ((String) objArr4[0]).intern());
        Object[] objArr5 = new Object[1];
        g(new int[]{1752227026, 186690696, 805135182, 375888146, -1584364057, 1994165415, -231229805, 1130088232}, Color.green(0) + 14, objArr5);
        String intern3 = ((String) objArr5[0]).intern();
        Object[] objArr6 = new Object[1];
        g(new int[]{-148129199, 134832772, -1454825698, -1688252648, 257809644, 2091152000, -2034612870, 1623875236}, 13 - (TypedValue.complexToFloat(0) > 0.0f ? 1 : (TypedValue.complexToFloat(0) == 0.0f ? 0 : -1)), objArr6);
        a = new d(intern3, 2, ((String) objArr6[0]).intern());
        Object[] objArr7 = new Object[1];
        g(new int[]{1976962340, 472019080, -556126171, 323670378}, 5 - KeyEvent.keyCodeFromString(""), objArr7);
        String intern4 = ((String) objArr7[0]).intern();
        Object[] objArr8 = new Object[1];
        g(new int[]{709954850, 2112081550, 1435714568, 1815678865}, 5 - TextUtils.getCapsMode("", 0, 0), objArr8);
        d = new d(intern4, 3, ((String) objArr8[0]).intern());
        j = b();
        int i2 = i + 97;
        f = i2 % 128;
        switch (i2 % 2 != 0 ? 'S' : '\t') {
            case '\t':
                throw null;
            default:
                return;
        }
    }

    private d(String str, int i2, String str2) {
        this.c = str2;
    }

    @Override // o.ei.b
    public final String e() {
        int i2 = f;
        int i3 = i2 + 83;
        i = i3 % 128;
        switch (i3 % 2 == 0) {
            case false:
                throw null;
            default:
                String str = this.c;
                int i4 = i2 + 69;
                i = i4 % 128;
                switch (i4 % 2 != 0 ? (char) 25 : 'E') {
                    case 'E':
                        return str;
                    default:
                        throw null;
                }
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void g(int[] r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 840
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.at.d.g(int[], int, java.lang.Object[]):void");
    }
}
