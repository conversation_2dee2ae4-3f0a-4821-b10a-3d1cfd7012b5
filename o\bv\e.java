package o.bv;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.os.SystemClock;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.esotericsoftware.asm.Opcodes;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\bv\e.smali */
public final class e {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static long a;
    private static final Object c;
    private static int d;
    private static int e;
    private Boolean b = null;

    static void b() {
        a = -505177371449295443L;
    }

    private static void g(short s, short s2, short s3, Object[] objArr) {
        byte[] bArr = $$a;
        int i = 1 - (s * 3);
        int i2 = (s3 * 2) + Opcodes.IREM;
        int i3 = (s2 * 2) + 4;
        byte[] bArr2 = new byte[i];
        int i4 = -1;
        int i5 = i - 1;
        if (bArr == null) {
            i3++;
            i2 = i3 + (-i2);
        }
        while (true) {
            i4++;
            bArr2[i4] = (byte) i2;
            if (i4 == i5) {
                objArr[0] = new String(bArr2, 0);
                return;
            } else {
                i3++;
                i2 += -bArr[i3];
            }
        }
    }

    static void init$0() {
        $$a = new byte[]{102, 46, -74, -23};
        $$b = Opcodes.F2L;
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        e = 0;
        d = 1;
        b();
        ViewConfiguration.getPressedStateDuration();
        c = new Object();
        int i = d + 7;
        e = i % 128;
        switch (i % 2 != 0 ? (char) 15 : '*') {
            case '*':
                return;
            default:
                Object obj = null;
                obj.hashCode();
                throw null;
        }
    }

    public final boolean c(Context context) {
        boolean booleanValue;
        synchronized (c) {
            if (this.b == null) {
                o.ee.e.a();
                long o2 = o.ee.c.o(context);
                if (b(context) != o2) {
                    e(context, o2);
                    this.b = Boolean.TRUE;
                } else {
                    this.b = Boolean.FALSE;
                }
            }
            o.ee.g.c();
            Object[] objArr = new Object[1];
            f("㭼ࡨ崇ꈄ\uf7cc쓶ঐ嵗ꉺ\uf71e쐁য়廬ꎾ\uf75e쑤ठ帩ꏉ\uf0e3얾॑幡", 13093 - KeyEvent.normalizeMetaState(0), objArr);
            String intern = ((String) objArr[0]).intern();
            StringBuilder sb = new StringBuilder();
            Object[] objArr2 = new Object[1];
            f("㭔ꋡࠢ\uf640己㬃ꅗ\u0893\uf637屻㮏ꇝཌྷ\uf6bb峛㨙ꆮ࿖\uf516岤㫗ꀔ๘\uf5e9匙㥋ꂎฦ\uf47a历㦟꜡\u0efd\uf4d3剳㦠ꞁഃ\uf4b7勳㠗ꙛ෯\ueb3d儉㢚ꘪ౨\ueb88儡㽧ꚅೕ\uea26凨㾄", 39344 - (SystemClock.elapsedRealtime() > 0L ? 1 : (SystemClock.elapsedRealtime() == 0L ? 0 : -1)), objArr2);
            o.ee.g.d(intern, sb.append(((String) objArr2[0]).intern()).append(this.b).toString());
            booleanValue = this.b.booleanValue();
        }
        return booleanValue;
    }

    private static long b(Context context) {
        SharedPreferences sharedPreferences;
        Object obj;
        int i = d + 63;
        e = i % 128;
        switch (i % 2 == 0) {
            case false:
                Object[] objArr = new Object[1];
                f("㭛씄입솽쉿찾캚콜줊쯮헽홥탗튆퍂\udd34\udfe2\ud9b6\uda13\ue4cf\ue684\ue776\ue126\ue3e2\uec47\uee0f\ue8d1\ueaad\ueb56\uf531\uf79f\uf049\uf22fﳳﺯＣ療ﮘ葺蘶胠腌茖跊辺衷訴", (PointF.length(2.0f, 0.0f) > 1.0f ? 1 : (PointF.length(2.0f, 0.0f) == 1.0f ? 0 : -1)) * 65099, objArr);
                sharedPreferences = context.getSharedPreferences(((String) objArr[0]).intern(), 1);
                Object[] objArr2 = new Object[1];
                f("㭼┠ޗ怬䋬ꍮ跀\uefaf젺⪆\u0b31痧噌냖銮\uf32c\udda0㹡\u18f9祋寞䖩ꘑ胘\ue169쏹ⱜึ梎䤅ꮋ鑾\uf6c2흆ㄢᎨ簚底뽼駈頻\ue426욪✟ǣ扱䳙", (-16769427) >>> Color.rgb(0, 1, 1), objArr2);
                obj = objArr2[0];
                break;
            default:
                Object[] objArr3 = new Object[1];
                f("㭛씄입솽쉿찾캚콜줊쯮헽홥탗튆퍂\udd34\udfe2\ud9b6\uda13\ue4cf\ue684\ue776\ue126\ue3e2\uec47\uee0f\ue8d1\ueaad\ueb56\uf531\uf79f\uf049\uf22fﳳﺯＣ療ﮘ葺蘶胠腌茖跊辺衷訴", (PointF.length(0.0f, 0.0f) > 0.0f ? 1 : (PointF.length(0.0f, 0.0f) == 0.0f ? 0 : -1)) + 65099, objArr3);
                sharedPreferences = context.getSharedPreferences(((String) objArr3[0]).intern(), 0);
                Object[] objArr4 = new Object[1];
                f("㭼┠ޗ怬䋬ꍮ跀\uefaf젺⪆\u0b31痧噌냖銮\uf32c\udda0㹡\u18f9祋寞䖩ꘑ胘\ue169쏹ⱜึ梎䤅ꮋ鑾\uf6c2흆ㄢᎨ簚底뽼駈頻\ue426욪✟ǣ扱䳙", (-16769427) - Color.rgb(0, 0, 0), objArr4);
                obj = objArr4[0];
                break;
        }
        long j = sharedPreferences.getLong(((String) obj).intern(), 0L);
        int i2 = e + 15;
        d = i2 % 128;
        switch (i2 % 2 != 0 ? '%' : (char) 0) {
            case '%':
                return j;
            default:
                Object obj2 = null;
                obj2.hashCode();
                throw null;
        }
    }

    private static void e(Context context, long j) {
        int i = e + 41;
        d = i % 128;
        int i2 = i % 2;
        Object[] objArr = new Object[1];
        f("㭛씄입솽쉿찾캚콜줊쯮헽홥탗튆퍂\udd34\udfe2\ud9b6\uda13\ue4cf\ue684\ue776\ue126\ue3e2\uec47\uee0f\ue8d1\ueaad\ueb56\uf531\uf79f\uf049\uf22fﳳﺯＣ療ﮘ葺蘶胠腌茖跊辺衷訴", ImageFormat.getBitsPerPixel(0) + 65100, objArr);
        SharedPreferences.Editor edit = context.getSharedPreferences(((String) objArr[0]).intern(), 0).edit();
        Object[] objArr2 = new Object[1];
        f("㭼┠ޗ怬䋬ꍮ跀\uefaf젺⪆\u0b31痧噌냖銮\uf32c\udda0㹡\u18f9祋寞䖩ꘑ胘\ue169쏹ⱜึ梎䤅ꮋ鑾\uf6c2흆ㄢᎨ簚底뽼駈頻\ue426욪✟ǣ扱䳙", 7789 - View.MeasureSpec.makeMeasureSpec(0, 0), objArr2);
        edit.putLong(((String) objArr2[0]).intern(), j).commit();
        int i3 = d + 19;
        e = i3 % 128;
        int i4 = i3 % 2;
    }

    /* JADX WARN: Code restructure failed: missing block: B:6:0x0010, code lost:
    
        if (r21 != null) goto L16;
     */
    /* JADX WARN: Failed to find 'out' block for switch in B:10:0x0034. Please report as an issue. */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void f(java.lang.String r21, int r22, java.lang.Object[] r23) {
        /*
            Method dump skipped, instructions count: 506
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.bv.e.f(java.lang.String, int, java.lang.Object[]):void");
    }
}
