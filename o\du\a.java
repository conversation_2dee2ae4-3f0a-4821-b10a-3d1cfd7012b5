package o.du;

import android.content.Context;
import android.graphics.Color;
import android.graphics.ImageFormat;
import android.graphics.drawable.Drawable;
import android.media.AudioTrack;
import android.os.Process;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.LruCache;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.ExpandableListView;
import com.esotericsoftware.asm.Opcodes;
import java.io.File;
import java.io.FileFilter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import kotlin.text.Typography;
import o.ee.g;

/* loaded from: C:\Users\<USER>\Desktop\apk\..\..\Downloads\bocc-android-tokenization-17052025 (1)\smali_classes17\o\du\a.smali */
public final class a {
    public static final byte[] $$a = null;
    public static final int $$b = 0;
    private static int $10;
    private static int $11;
    private static int a;
    private static final a b;
    private static long c;
    private static char d;
    private static char[] f;
    private static long g;
    private static int h;
    private static int j;
    private final LruCache<String, Object> e = new LruCache<>(4194304);

    static void d() {
        d = (char) 30628;
        a = 161105445;
        c = 6565854932352255525L;
        f = new char[]{11495, 11402, 9768, 14762, 13089, 1708, 6189, 5097, 25903, 30880, 29221, 17836, 24425, 21165, 42022, 49068, 45370, 34025, 40487, 37286, 60221, 65257, 61484, 52145, 56608, 53434, 10813, 31037, 29578, 27650, 26240, 21275, 19850, 17981, 12426, 11548, 10112, 4122, 2717, 1804, 61834, 59947, 58510, 53531, 52110, 50239, 48797, 43776, 42393, 40454, 34955, 34058, 32669, 32257, 29824, 27398, 24970, 21505, 19072, 16694, 14218, 10768, 8343, 5894, 3456, Typography.dollar, 63115, 60673, 58278, 54788, 52358, 49933, 47488, 44081, 41610, 39209, 36759, 33296, 30917, 28488, 26053, 11420, 9767, 14760, 13099, 1701, 6188, 5097, 25917, 30886, 29289, 17834, 24379, 21164, 42024, 49085, 45356, 34025, 40487, 37292, 60222, 65257, 61487, 52128, 56613, 53420, 11451, 9772, 14778, 13094, 1701, 6207, 5036, 25864, 30887, 29229, 17817, 24364, 21179, 42042, 49056, 45370, 33981, 40553, 37348, 60265, 65180, 61479, 52136, 56619, 53413, 10796, 15849, 14141, 2726, 7273, 6078, 26939, 31904, 30269, 18860, 17257, 22189, 43048, 41917, 46376, 12468, 14902, 9639, 12055, 6834, 1063, 4018, 31091, 25854, 28275, 22933, 17212, 20134, 47165, 41911, 44403, 39098, 33341, 36339, 63280, 58034, 60464, 55227, 49462, 23416, 20983, 20094, 17658, 29045, 28634, 25719, 4855, 3912, 1524, 12910, 10473, 9592, 54270, 51293, 50932, 62313, 59855, 58978, 40171, 35198, 34782, 48227, 43768, 42878, 24043, 19055, 16571, 32054, 27579, 24654, 7925, 2938, 505, 15991, 13566, 8507, 57327, 54388, 49851, 65407, 62974, 57975, 39166, 38255, 33790, 47163, 46845, 41842, 23031, 22142};
        g = -3149025465492167095L;
    }

    static void init$0() {
        $$a = new byte[]{24, -81, 39, 82};
        $$b = 43;
    }

    /* JADX WARN: Removed duplicated region for block: B:10:0x002a  */
    /* JADX WARN: Removed duplicated region for block: B:7:0x0022  */
    /* JADX WARN: Unsupported multi-entry loop pattern (BACK_EDGE: B:10:0x002a -> B:4:0x0035). Please report as a decompilation issue!!! */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    private static void l(int r7, short r8, int r9, java.lang.Object[] r10) {
        /*
            int r8 = r8 + 4
            int r9 = r9 * 2
            int r9 = 1 - r9
            byte[] r0 = o.du.a.$$a
            int r7 = 106 - r7
            byte[] r1 = new byte[r9]
            r2 = 0
            if (r0 != 0) goto L17
            r7 = r9
            r3 = r1
            r5 = r2
            r9 = r8
            r1 = r0
            r0 = r10
            r10 = r7
            goto L35
        L17:
            r3 = r2
            r6 = r9
            r9 = r7
            r7 = r6
        L1b:
            byte r4 = (byte) r9
            int r5 = r3 + 1
            r1[r3] = r4
            if (r5 != r7) goto L2a
            java.lang.String r7 = new java.lang.String
            r7.<init>(r1, r2)
            r10[r2] = r7
            return
        L2a:
            int r8 = r8 + 1
            r3 = r0[r8]
            r6 = r9
            r9 = r8
            r8 = r3
            r3 = r1
            r1 = r0
            r0 = r10
            r10 = r6
        L35:
            int r8 = -r8
            int r8 = r8 + r10
            r10 = r0
            r0 = r1
            r1 = r3
            r3 = r5
            r6 = r9
            r9 = r8
            r8 = r6
            goto L1b
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.a.l(int, short, int, java.lang.Object[]):void");
    }

    static {
        init$0();
        $10 = 0;
        $11 = 1;
        h = 0;
        j = 1;
        d();
        ViewConfiguration.getGlobalActionKeyTimeout();
        View.MeasureSpec.getSize(0);
        Process.myTid();
        TextUtils.indexOf("", "");
        ExpandableListView.getPackedPositionChild(0L);
        b = new a();
        int i = h + 61;
        j = i % 128;
        int i2 = i % 2;
    }

    public static a a() {
        int i = j;
        int i2 = i + 47;
        h = i2 % 128;
        int i3 = i2 % 2;
        a aVar = b;
        int i4 = i + 23;
        h = i4 % 128;
        int i5 = i4 % 2;
        return aVar;
    }

    private a() {
    }

    /* JADX WARN: Type inference failed for: r15v1, types: [o.dx.a] */
    /* JADX WARN: Type inference failed for: r3v5, types: [o.dy.e] */
    private static String a(d<?, ?, ?, ?> dVar) {
        String str;
        String c2 = dVar.c();
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i(1005609258 - (ViewConfiguration.getMaximumFlingVelocity() >> 16), "\ud9bf搨뎔", (char) (20067 - (ViewConfiguration.getJumpTapTimeout() >> 16)), "⫗\uf061挻ꅎ", "\u0000\u0000\u0000\u0000", objArr);
        StringBuilder append = sb.append(((String) objArr[0]).intern()).append(dVar.b().e());
        Object[] objArr2 = new Object[1];
        i(1400146851 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "喉", (char) ((ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)) + 45198), "ꉨ璋赓▰", "\u0000\u0000\u0000\u0000", objArr2);
        StringBuilder append2 = append.append(((String) objArr2[0]).intern()).append(dVar.d().d());
        switch (c2 == null ? '5' : 'A') {
            case Opcodes.SALOAD /* 53 */:
                int i = h + 21;
                j = i % 128;
                int i2 = i % 2;
                str = "";
                break;
            default:
                StringBuilder sb2 = new StringBuilder();
                Object[] objArr3 = new Object[1];
                k((char) (ViewConfiguration.getScrollBarSize() >> 8), ViewConfiguration.getMinimumFlingVelocity() >> 16, (ViewConfiguration.getScrollBarSize() >> 8) + 1, objArr3);
                str = sb2.append(((String) objArr3[0]).intern()).append(c2).toString();
                break;
        }
        String obj = append2.append(str).toString();
        int i3 = h + 53;
        j = i3 % 128;
        switch (i3 % 2 != 0 ? '-' : '\b') {
            case '\b':
                Object obj2 = null;
                obj2.hashCode();
                throw null;
            default:
                return obj;
        }
    }

    private static String e(o.dy.e eVar) {
        StringBuilder sb = new StringBuilder();
        Object[] objArr = new Object[1];
        i((ViewConfiguration.getWindowTouchSlop() >> 8) + 1005609258, "\ud9bf搨뎔", (char) (Color.rgb(0, 0, 0) + 16797283), "⫗\uf061挻ꅎ", "\u0000\u0000\u0000\u0000", objArr);
        String obj = sb.append(((String) objArr[0]).intern()).append(eVar.e()).toString();
        int i = j + 67;
        h = i % 128;
        int i2 = i % 2;
        return obj;
    }

    private static File a(Context context, d<?, ?, ?, ?> dVar) {
        File file = new File(context.getCacheDir(), a(dVar));
        int i = j + 27;
        h = i % 128;
        switch (i % 2 == 0) {
            case false:
                throw null;
            default:
                return file;
        }
    }

    private <ObjRes> void d(Context context, d<?, ?, ? extends o.dq.d<ObjRes>, ObjRes> dVar) throws e {
        String a2 = a(dVar);
        synchronized (this.e) {
            if (this.e.get(a2) != null) {
                return;
            }
            File a3 = a(context, dVar);
            if (!a3.exists() || !a3.isFile()) {
                Object[] objArr = new Object[1];
                k((char) View.resolveSizeAndState(0, 0, 0), 1 - Color.green(0), (ViewConfiguration.getPressedStateDuration() >> 16) + 26, objArr);
                throw new e(((String) objArr[0]).intern());
            }
            try {
                ObjRes a4 = dVar.a().a(a3);
                synchronized (this.e) {
                    this.e.put(a2, a4);
                }
            } catch (e e) {
                g.c();
                Object[] objArr2 = new Object[1];
                k((char) (21926 - TextUtils.getOffsetAfter("", 0)), 27 - TextUtils.indexOf("", ""), 26 - Color.blue(0), objArr2);
                String intern = ((String) objArr2[0]).intern();
                StringBuilder sb = new StringBuilder();
                Object[] objArr3 = new Object[1];
                k((char) (KeyEvent.getDeadChar(0, 0) + 21164), Gravity.getAbsoluteGravity(0, 0) + 53, Color.red(0) + 28, objArr3);
                g.a(intern, sb.append(((String) objArr3[0]).intern()).append(dVar.toString()).toString(), e);
            }
        }
    }

    /* JADX WARN: Removed duplicated region for block: B:55:0x023d  */
    /* JADX WARN: Removed duplicated region for block: B:57:0x0245  */
    /* JADX WARN: Removed duplicated region for block: B:71:? A[SYNTHETIC] */
    /* JADX WARN: Removed duplicated region for block: B:72:0x0240  */
    /* JADX WARN: Type inference failed for: r2v10, types: [o.dx.a] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct add '--show-bad-code' argument
    */
    public final boolean e(android.content.Context r20, o.du.d<?, ?, ?, ?> r21) throws o.du.e {
        /*
            Method dump skipped, instructions count: 624
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.a.e(android.content.Context, o.du.d):boolean");
    }

    final <ObjRes> ObjRes c(Context context, d<?, ?, ? extends o.dq.d<ObjRes>, ObjRes> dVar) throws e {
        g.c();
        Object[] objArr = new Object[1];
        k((char) (21926 - (Process.myPid() >> 22)), TextUtils.getTrimmedLength("") + 27, (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)) + 25, objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        i((-1) - (ExpandableListView.getPackedPositionForChild(0, 0) > 0L ? 1 : (ExpandableListView.getPackedPositionForChild(0, 0) == 0L ? 0 : -1)), "\u16fd㿈\u2ef5箇떴⓵骡䢃␢镼", (char) ((-1) - TextUtils.lastIndexOf("", '0')), "㿛둋菥헱", "\u0000\u0000\u0000\u0000", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(dVar.toString()).toString());
        d(context, dVar);
        synchronized (this.e) {
            ObjRes objres = (ObjRes) this.e.get(a(dVar));
            if (objres != null) {
                if (!dVar.a().a().isAssignableFrom(objres.getClass())) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k((char) (KeyEvent.keyCodeFromString("") + 21926), 27 - TextUtils.getCapsMode("", 0, 0), 26 - KeyEvent.keyCodeFromString(""), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    Object[] objArr4 = new Object[1];
                    i(1 - (SystemClock.elapsedRealtimeNanos() > 0L ? 1 : (SystemClock.elapsedRealtimeNanos() == 0L ? 0 : -1)), "\uf3dc쯍껊丑繌⟟\uf4ad혎윁낝轎ḽ䞚錙交䜳ꟼ釢會鶅獗佒쥥\uf414\ue713₪땪\uda61㶯癓䍰快ⷺ捲樢罚\uf378绅셨㻉段汌ᴉ亊宪井뭆뢒꾍\ue8b8ῶျ\uf175坪\udc8d羒\u0bba뗦맮Ʈⲇ灏\uda08Ⓜ匲屔", (char) (18525 - (ViewConfiguration.getJumpTapTimeout() >> 16)), "㔃ᱮ嶹\uf348", "\u0000\u0000\u0000\u0000", objArr4);
                    g.e(intern2, String.format(((String) objArr4[0]).intern(), dVar.getClass().getCanonicalName(), dVar.a().a().getCanonicalName(), objres.getClass().getCanonicalName()));
                    Object[] objArr5 = new Object[1];
                    i(1633856486 - TextUtils.getOffsetBefore("", 0), "ᱥ\ue838౺\ueaf3作ἇ㽩걍妪\ud812ꯤ潟⃛娰ᜤ\uda3b\ue971뒆꿣莈ݳ\uea0f\uf3d4炕\ue2daゔ\uf7de㷞\ue676❺⟎艃隕", (char) (ImageFormat.getBitsPerPixel(0) + 1), "\ue61a披㝡\udc32", "\u0000\u0000\u0000\u0000", objArr5);
                    throw new e(((String) objArr5[0]).intern());
                }
                g.c();
                Object[] objArr6 = new Object[1];
                k((char) (ImageFormat.getBitsPerPixel(0) + 21927), 28 - (SystemClock.currentThreadTimeMillis() > (-1L) ? 1 : (SystemClock.currentThreadTimeMillis() == (-1L) ? 0 : -1)), 26 - ExpandableListView.getPackedPositionGroup(0L), objArr6);
                String intern3 = ((String) objArr6[0]).intern();
                Object[] objArr7 = new Object[1];
                k((char) (7194 - KeyEvent.keyCodeFromString("")), 145 - TextUtils.indexOf((CharSequence) "", '0'), Drawable.resolveOpacity(0, 0) + 24, objArr7);
                g.d(intern3, ((String) objArr7[0]).intern());
                return objres;
            }
            return null;
        }
    }

    /* JADX INFO: Access modifiers changed from: private */
    public static /* synthetic */ boolean c(o.dy.e eVar, List list, File file) {
        int i = h + 79;
        j = i % 128;
        if (i % 2 == 0) {
            file.getName().startsWith(e(eVar));
            throw null;
        }
        if (!file.getName().startsWith(e(eVar))) {
            return false;
        }
        switch (!list.contains(file.getName())) {
            case false:
                return false;
            default:
                int i2 = h + 51;
                j = i2 % 128;
                return i2 % 2 != 0;
        }
    }

    public final <Type extends o.dy.e> void c(Context context, final Type type, Collection<d<Type, ?, ?, ?>> collection) {
        g.c();
        Object[] objArr = new Object[1];
        k((char) (TextUtils.getCapsMode("", 0, 0) + 21926), (ViewConfiguration.getMaximumFlingVelocity() >> 16) + 27, 25 - TextUtils.lastIndexOf("", '0', 0, 0), objArr);
        String intern = ((String) objArr[0]).intern();
        StringBuilder sb = new StringBuilder();
        Object[] objArr2 = new Object[1];
        i(ViewConfiguration.getTapTimeout() >> 16, "ኢᄤ咼懑濃䣁㼸쳮玒\ue13e\ue1d7؉Ⱂ参꒚강ၵ鲌ᔡﻐ篬䅻\uf878\uf028⮀龬蹡箟᠙か", (char) ((ViewConfiguration.getWindowTouchSlop() >> 8) + 16490), "ꔇ䉉橉\ue440", "\u0000\u0000\u0000\u0000", objArr2);
        g.d(intern, sb.append(((String) objArr2[0]).intern()).append(type.getClass().getSimpleName()).toString());
        final ArrayList arrayList = new ArrayList(collection.size());
        Iterator<d<Type, ?, ?, ?>> it = collection.iterator();
        while (it.hasNext()) {
            int i = h + 81;
            j = i % 128;
            if (i % 2 == 0) {
                arrayList.add(a(it.next()));
                int i2 = 59 / 0;
            } else {
                arrayList.add(a(it.next()));
            }
        }
        File[] listFiles = context.getCacheDir().listFiles(new FileFilter() { // from class: o.du.a$$ExternalSyntheticLambda0
            @Override // java.io.FileFilter
            public final boolean accept(File file) {
                boolean c2;
                c2 = a.c(o.dy.e.this, arrayList, file);
                return c2;
            }
        });
        switch (listFiles != null ? 'C' : '.') {
            case 'C':
                int i3 = j + 7;
                h = i3 % 128;
                int i4 = i3 % 2;
                for (File file : listFiles) {
                    g.c();
                    Object[] objArr3 = new Object[1];
                    k((char) (Color.rgb(0, 0, 0) + 16799142), 27 - (ViewConfiguration.getScrollDefaultDelay() >> 16), 25 - Process.getGidForName(""), objArr3);
                    String intern2 = ((String) objArr3[0]).intern();
                    StringBuilder sb2 = new StringBuilder();
                    Object[] objArr4 = new Object[1];
                    i(Drawable.resolveOpacity(0, 0), "\uf5a8幂ˈ䌩\ud813䴳됣\ueac9ꇫ\ua48f\uef34\uee58㫫ђ묐ﴛ㴜鴁좰瓦쫗\ue31c䮱堅汵\ue994廱铑\uec81蛻弅싩\uef60㗓\ueda3鼿ᤕ\ue5bb荏뻅\uf838믝誧愎㷂黎귈㏋祆慬學蒤屽똈⫮㒥⎣䷧穹뒲됹婐", (char) (ViewConfiguration.getScrollBarSize() >> 8), "䑰컌Ӕ\ud8e7", "\u0000\u0000\u0000\u0000", objArr4);
                    g.d(intern2, sb2.append(((String) objArr4[0]).intern()).append(file.getName()).toString());
                    this.e.remove(file.getName());
                    switch (!file.delete() ? 'D' : '\\') {
                        case 'D':
                            g.c();
                            Object[] objArr5 = new Object[1];
                            k((char) (21926 - View.MeasureSpec.getMode(0)), TextUtils.lastIndexOf("", '0') + 28, 26 - (AudioTrack.getMinVolume() > 0.0f ? 1 : (AudioTrack.getMinVolume() == 0.0f ? 0 : -1)), objArr5);
                            String intern3 = ((String) objArr5[0]).intern();
                            Object[] objArr6 = new Object[1];
                            k((char) (30674 - KeyEvent.getDeadChar(0, 0)), 169 - TextUtils.indexOf((CharSequence) "", '0', 0, 0), 51 - Color.blue(0), objArr6);
                            g.e(intern3, ((String) objArr6[0]).intern());
                            int i5 = h + 9;
                            j = i5 % 128;
                            int i6 = i5 % 2;
                            break;
                    }
                }
                return;
            default:
                return;
        }
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void i(int r20, java.lang.String r21, char r22, java.lang.String r23, java.lang.String r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 736
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.a.i(int, java.lang.String, char, java.lang.String, java.lang.String, java.lang.Object[]):void");
    }

    /*  JADX ERROR: JadxRuntimeException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxRuntimeException: Failed to find switch 'out' block (already processed)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.calcSwitchOut(SwitchRegionMaker.java:202)
        	at jadx.core.dex.visitors.regions.maker.SwitchRegionMaker.process(SwitchRegionMaker.java:61)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:115)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.makeEndlessLoop(LoopRegionMaker.java:281)
        	at jadx.core.dex.visitors.regions.maker.LoopRegionMaker.process(LoopRegionMaker.java:64)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.traverse(RegionMaker.java:92)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeRegion(RegionMaker.java:69)
        	at jadx.core.dex.visitors.regions.maker.RegionMaker.makeMthRegion(RegionMaker.java:49)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:25)
        */
    private static void k(char r22, int r23, int r24, java.lang.Object[] r25) {
        /*
            Method dump skipped, instructions count: 1032
            To view this dump add '--comments-level debug' option
        */
        throw new UnsupportedOperationException("Method not decompiled: o.du.a.k(char, int, int, java.lang.Object[]):void");
    }
}
